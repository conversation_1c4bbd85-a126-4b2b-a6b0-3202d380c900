## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

This modernization plan outlines the automated steps to transition your `FinishProcessingReport` module from ASP.NET to a robust, modern Django application. By leveraging AI-assisted automation, we ensure a faster, more accurate, and cost-effective migration, minimizing manual errors and accelerating your digital transformation.

### Business Value Proposition:

Migrating to Django with this approach delivers several key benefits for your organization:
*   **Reduced Operational Costs:** Automated conversion significantly cuts down development time and resource allocation.
*   **Enhanced Maintainability:** A clean, modular Django architecture is easier to understand, debug, and extend, reducing future maintenance overhead.
*   **Improved Performance:** Modern Django patterns, coupled with efficient frontend technologies like HTMX and Alpine.js, provide a faster, more responsive user experience without heavy client-side JavaScript frameworks.
*   **Scalability:** Django's robust nature allows your application to grow seamlessly with your business needs.
*   **Future-Proofing:** Moving away from legacy ASP.NET ensures your application remains compatible with modern web standards and security practices.
*   **Simplified Development:** The "Fat Model, Thin View" philosophy and clear separation of concerns make future feature development more streamlined and predictable.

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

The ASP.NET `GridView` explicitly binds to several fields. Since no `SqlDataSource` or direct SQL query was provided, we infer the table name from the report's purpose and the fields from the `GridView` columns.

*   **Inferred Table Name:** `tbl_finish_processing_report`
*   **Inferred Columns:**
    *   `Id` (Primary Key, likely an integer)
    *   `FinYear` (Text/Varchar)
    *   `PONo` (Text/Varchar)
    *   `SysDate1` (Date/DateTime)
    *   `ItemCode` (Text/Varchar)
    *   `ManfDesc` (Text/Varchar)
    *   `Qty` (Numeric/Decimal)
    *   `MRSNo` (Text/Varchar)
    *   `SysDate2` (Date/DateTime)
    *   `MINNo` (Text/Varchar)
    *   `SysDate3` (Date/DateTime)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

Based on the provided ASP.NET code:

*   **Create:** No explicit create functionality (e.g., input forms, add buttons) is visible. The `GridView` is purely for display.
*   **Read:** This module's primary function is **Read**. The `GridView` displays a list of "Finish Processing Report" entries with pagination.
*   **Update:** No explicit update functionality (e.g., edit buttons, editable GridView rows) is visible.
*   **Delete:** No explicit delete functionality (e.g., delete buttons) is visible.

Given this, the Django modernization will initially focus on the **Read (List View)** functionality, providing placeholders for CRUD as per the general template, but prioritizing the list display. If CRUD operations are needed in the future, they can be easily added following the established Django patterns.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

*   **GridView (`GridView1`):** This is the central component, responsible for displaying tabular data. It includes basic pagination (though DataTables will handle this client-side in Django) and columns for various data points.
*   **Labels (`asp:Label`):** Used within `TemplateField` to display individual data values from the bound object.
*   **Styling:** Custom CSS (`StyleSheet.css`, `yui-datatable.css`, inline styles) and `Theme ="Default"`. In Django, this will be handled by Tailwind CSS, with DataTables providing its own styling.

## Step 4: Generate Django Code

### 4.1 Models

Task: Create a Django model based on the inferred database schema. We'll name the model `FinishProcessingReport` and assume a table named `tbl_finish_processing_report`.

```python
# mis_reports/models.py
from django.db import models

class FinishProcessingReport(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50, blank=True, null=True)
    po_no = models.CharField(db_column='PONo', max_length=100, blank=True, null=True)
    po_date = models.DateField(db_column='SysDate1', blank=True, null=True)
    item_code = models.CharField(db_column='ItemCode', max_length=100, blank=True, null=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True)
    qty = models.DecimalField(db_column='Qty', max_digits=10, decimal_places=2, blank=True, null=True)
    mrs_no = models.CharField(db_column='MRSNo', max_length=100, blank=True, null=True)
    mrs_date = models.DateField(db_column='SysDate2', blank=True, null=True)
    min_no = models.CharField(db_column='MINNo', max_length=100, blank=True, null=True)
    min_date = models.DateField(db_column='SysDate3', blank=True, null=True)

    class Meta:
        managed = False  # Set to False if the table already exists in the database
        db_table = 'tbl_finish_processing_report'
        verbose_name = 'Finish Processing Report'
        verbose_name_plural = 'Finish Processing Reports'

    def __str__(self):
        # A sensible default, adjust if a more specific field is primary
        return f"{self.po_no} - {self.item_code}"
        
    # Example business logic methods (if any were identified)
    def get_display_qty(self):
        # Example of a fat model method
        return f"{self.qty or 0:.2f} units"

```

### 4.2 Forms

Task: Define a Django form for user input. Given the original ASP.NET page was purely for display, we'll create a simple form for potential future use (e.g., if edit/add functionality is introduced), ensuring it aligns with the Fat Model, Thin View principle by not implementing any business logic here.

```python
# mis_reports/forms.py
from django import forms
from .models import FinishProcessingReport

class FinishProcessingReportForm(forms.ModelForm):
    class Meta:
        model = FinishProcessingReport
        fields = [
            'fin_year', 'po_no', 'po_date', 'item_code', 
            'manf_desc', 'qty', 'mrs_no', 'mrs_date', 
            'min_no', 'min_date'
        ]
        widgets = {
            'fin_year': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'po_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'po_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'item_code': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'manf_desc': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'qty': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'mrs_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'mrs_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'min_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'min_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
        }
        
    def clean_qty(self):
        qty = self.cleaned_data.get('qty')
        if qty is not None and qty < 0:
            raise forms.ValidationError("Quantity cannot be negative.")
        return qty

```

### 4.3 Views

Task: Implement CRUD operations using CBVs. We will focus on the `ListView` as it's the primary functionality identified. We'll also provide the partial view for the DataTables table.

```python
# mis_reports/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import FinishProcessingReport
from .forms import FinishProcessingReportForm

class FinishProcessingReportListView(ListView):
    model = FinishProcessingReport
    template_name = 'mis_reports/finishprocessingreport/list.html'
    context_object_name = 'finish_processing_reports' # Pluralized, lowercased

class FinishProcessingReportTablePartialView(ListView):
    model = FinishProcessingReport
    template_name = 'mis_reports/finishprocessingreport/_finishprocessingreport_table.html'
    context_object_name = 'finish_processing_reports'

class FinishProcessingReportCreateView(CreateView):
    model = FinishProcessingReport
    form_class = FinishProcessingReportForm
    template_name = 'mis_reports/finishprocessingreport/form.html'
    success_url = reverse_lazy('finishprocessingreport_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Finish Processing Report entry added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshFinishProcessingReportList'
                }
            )
        return response

class FinishProcessingReportUpdateView(UpdateView):
    model = FinishProcessingReport
    form_class = FinishProcessingReportForm
    template_name = 'mis_reports/finishprocessingreport/form.html'
    success_url = reverse_lazy('finishprocessingreport_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Finish Processing Report entry updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshFinishProcessingReportList'
                }
            )
        return response

class FinishProcessingReportDeleteView(DeleteView):
    model = FinishProcessingReport
    template_name = 'mis_reports/finishprocessingreport/confirm_delete.html'
    success_url = reverse_lazy('finishprocessingreport_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Finish Processing Report entry deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshFinishProcessingReportList'
                }
            )
        return response

```

### 4.4 Templates

Task: Create templates for each view, adhering to HTMX/Alpine.js for dynamic interactions and DataTables for list presentation.

#### `mis_reports/finishprocessingreport/list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Finish Processing Reports</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            hx-get="{% url 'finishprocessingreport_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Report Entry
        </button>
    </div>
    
    <div id="finishprocessingreportTable-container"
         hx-trigger="load, refreshFinishProcessingReportList from:body"
         hx-get="{% url 'finishprocessingreport_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading reports...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center p-4 z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI state
    });
</script>
{% endblock %}

```

#### `mis_reports/finishprocessingreport/_finishprocessingreport_table.html` (Partial Template)

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-md p-4">
    <table id="finishprocessingreportTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin. Year</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO Date</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th scope="col" class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MRS No</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MRS Date</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MIN No</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MIN Date</th>
                <th scope="col" class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for report in finish_processing_reports %}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ report.fin_year }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ report.po_no }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ report.po_date|date:"Y-m-d" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ report.item_code }}</td>
                <td class="py-2 px-4 text-sm text-gray-900">{{ report.manf_desc }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ report.qty|floatformat:"2" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ report.mrs_no }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ report.mrs_date|date:"Y-m-d" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ report.min_no }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ report.min_date|date:"Y-m-d" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm font-medium text-center">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white py-1 px-3 rounded-md mr-2 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
                        hx-get="{% url 'finishprocessingreport_edit' report.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white py-1 px-3 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                        hx-get="{% url 'finishprocessingreport_delete' report.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="12" class="py-4 px-4 text-center text-sm text-red-500 font-bold">
                    No data found to display
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Ensure DataTables JS is loaded in base.html
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#finishprocessingreportTable')) {
            $('#finishprocessingreportTable').DataTable().destroy();
        }
        $('#finishprocessingreportTable').DataTable({
            "pageLength": 20, // Matches ASP.NET GridView PageSize
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "responsive": true,
            "autoWidth": false
        });
    });
</script>
```

#### `mis_reports/finishprocessingreport/form.html` (Partial Template for Add/Edit)

```html
<div class="p-6 bg-white rounded-lg shadow-lg">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">{{ form.instance.pk|yesno:'Edit,Add' }} Finish Processing Report Entry</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-6">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}
                        <span class="text-red-500">*</span>
                    {% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                    <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600 list-disc pl-5">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        {% if form.non_field_errors %}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4">
            <strong class="font-bold">Error!</strong>
            <span class="block sm:inline">{{ form.non_field_errors }}</span>
        </div>
        {% endif %}

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-5 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-5 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Save
            </button>
        </div>
    </form>
</div>
```

#### `mis_reports/finishprocessingreport/confirm_delete.html` (Partial Template for Delete Confirmation)

```html
<div class="p-6 bg-white rounded-lg shadow-lg">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Finish Processing Report entry for <strong>"{{ object.po_no }} - {{ object.item_code }}"</strong>?</p>
    
    <form hx-post="{% url 'finishprocessingreport_delete' object.pk %}" hx-swap="none" class="flex justify-end space-x-4">
        {% csrf_token %}
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-5 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button 
            type="submit" 
            class="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-5 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
            Delete
        </button>
    </form>
</div>
```

### 4.5 URLs

Task: Define URL patterns for the views within the `mis_reports` app.

```python
# mis_reports/urls.py
from django.urls import path
from .views import (
    FinishProcessingReportListView, 
    FinishProcessingReportTablePartialView,
    FinishProcessingReportCreateView, 
    FinishProcessingReportUpdateView, 
    FinishProcessingReportDeleteView
)

urlpatterns = [
    path('finish-processing-report/', FinishProcessingReportListView.as_view(), name='finishprocessingreport_list'),
    path('finish-processing-report/table/', FinishProcessingReportTablePartialView.as_view(), name='finishprocessingreport_table'),
    path('finish-processing-report/add/', FinishProcessingReportCreateView.as_view(), name='finishprocessingreport_add'),
    path('finish-processing-report/edit/<int:pk>/', FinishProcessingReportUpdateView.as_view(), name='finishprocessingreport_edit'),
    path('finish-processing-report/delete/<int:pk>/', FinishProcessingReportDeleteView.as_view(), name='finishprocessingreport_delete'),
]

```

### 4.6 Tests

Task: Write comprehensive tests for the model and views to ensure functionality and maintainability.

```python
# mis_reports/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import FinishProcessingReport
from .forms import FinishProcessingReportForm

class FinishProcessingReportModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a single test object for all tests
        cls.report = FinishProcessingReport.objects.create(
            id=1, # Explicit ID if not auto-incrementing
            fin_year='2023-24',
            po_no='PO-001',
            po_date=timezone.now().date(),
            item_code='ITEM001',
            manf_desc='Test Manufacturing Description',
            qty=150.75,
            mrs_no='MRS-001',
            mrs_date=timezone.now().date(),
            min_no='MIN-001',
            min_date=timezone.now().date(),
        )
  
    def test_finish_processing_report_creation(self):
        self.assertEqual(self.report.fin_year, '2023-24')
        self.assertEqual(self.report.po_no, 'PO-001')
        self.assertEqual(self.report.item_code, 'ITEM001')
        self.assertAlmostEqual(float(self.report.qty), 150.75)
        self.assertEqual(str(self.report), 'PO-001 - ITEM001')
        
    def test_model_fields_verbose_names(self):
        field_labels = {
            'fin_year': 'Fin Year',
            'po_no': 'Po No',
            'po_date': 'Po Date',
            'item_code': 'Item Code',
            'manf_desc': 'Manf Desc',
            'qty': 'Qty',
            'mrs_no': 'Mrs No',
            'mrs_date': 'Mrs Date',
            'min_no': 'Min No',
            'min_date': 'Min Date',
        }
        for field, expected_label in field_labels.items():
            self.assertEqual(self.report._meta.get_field(field).verbose_name, expected_label)

    def test_get_display_qty_method(self):
        self.assertEqual(self.report.get_display_qty(), "150.75 units")
        self.report.qty = None
        self.assertEqual(self.report.get_display_qty(), "0.00 units")

class FinishProcessingReportFormTest(TestCase):
    def test_form_valid_data(self):
        form = FinishProcessingReportForm(data={
            'fin_year': '2024-25',
            'po_no': 'PO-002',
            'po_date': '2024-01-15',
            'item_code': 'ITEM002',
            'manf_desc': 'Another Description',
            'qty': 200.50,
            'mrs_no': 'MRS-002',
            'mrs_date': '2024-01-16',
            'min_no': 'MIN-002',
            'min_date': '2024-01-17',
        })
        self.assertTrue(form.is_valid(), form.errors)

    def test_form_invalid_qty(self):
        form = FinishProcessingReportForm(data={
            'fin_year': '2024-25', 'po_no': 'PO-002', 'po_date': '2024-01-15', 'item_code': 'ITEM002',
            'manf_desc': 'Another Description', 'qty': -10.00, 'mrs_no': 'MRS-002', 'mrs_date': '2024-01-16',
            'min_no': 'MIN-002', 'min_date': '2024-01-17',
        })
        self.assertFalse(form.is_valid())
        self.assertIn('qty', form.errors)
        self.assertIn('Quantity cannot be negative.', form.errors['qty'])

class FinishProcessingReportViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create multiple test objects for list view
        for i in range(1, 4):
            FinishProcessingReport.objects.create(
                id=i,
                fin_year=f'2023-24-{i}',
                po_no=f'PO-00{i}',
                po_date=timezone.now().date(),
                item_code=f'ITEM00{i}',
                manf_desc=f'Description {i}',
                qty=float(i * 10),
                mrs_no=f'MRS-00{i}',
                mrs_date=timezone.now().date(),
                min_no=f'MIN-00{i}',
                min_date=timezone.now().date(),
            )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view(self):
        response = self.client.get(reverse('finishprocessingreport_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis_reports/finishprocessingreport/list.html')
        self.assertTrue('finish_processing_reports' in response.context)
        self.assertEqual(len(response.context['finish_processing_reports']), 3)

    def test_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('finishprocessingreport_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis_reports/finishprocessingreport/_finishprocessingreport_table.html')
        self.assertTrue('finish_processing_reports' in response.context)
        self.assertEqual(len(response.context['finish_processing_reports']), 3)
        self.assertContains(response, 'id="finishprocessingreportTable"') # Check for DataTables ID

    def test_create_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('finishprocessingreport_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis_reports/finishprocessingreport/form.html')
        self.assertTrue('form' in response.context)
        
    def test_create_view_post_htmx(self):
        new_id = FinishProcessingReport.objects.count() + 1
        data = {
            'id': new_id, # Manual ID for testing due to managed=False
            'fin_year': '2025-26',
            'po_no': 'PO-NEW',
            'po_date': '2025-01-01',
            'item_code': 'ITEM-NEW',
            'manf_desc': 'New Entry',
            'qty': 500.00,
            'mrs_no': 'MRS-NEW',
            'mrs_date': '2025-01-02',
            'min_no': 'MIN-NEW',
            'min_date': '2025-01-03',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('finishprocessingreport_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX 204 No Content
        self.assertTrue(FinishProcessingReport.objects.filter(po_no='PO-NEW').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshFinishProcessingReportList')

    def test_update_view_get(self):
        report_obj = FinishProcessingReport.objects.get(id=1)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('finishprocessingreport_edit', args=[report_obj.id]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis_reports/finishprocessingreport/form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, report_obj)

    def test_update_view_post_htmx(self):
        report_obj = FinishProcessingReport.objects.get(id=1)
        data = {
            'id': report_obj.id, # Must include ID
            'fin_year': '2023-24-Updated',
            'po_no': 'PO-001-Updated',
            'po_date': report_obj.po_date.isoformat(), # Ensure date format
            'item_code': report_obj.item_code,
            'manf_desc': report_obj.manf_desc,
            'qty': report_obj.qty,
            'mrs_no': report_obj.mrs_no,
            'mrs_date': report_obj.mrs_date.isoformat(),
            'min_no': report_obj.min_no,
            'min_date': report_obj.min_date.isoformat(),
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('finishprocessingreport_edit', args=[report_obj.id]), data, **headers)
        self.assertEqual(response.status_code, 204)
        report_obj.refresh_from_db()
        self.assertEqual(report_obj.po_no, 'PO-001-Updated')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshFinishProcessingReportList')

    def test_delete_view_get(self):
        report_obj = FinishProcessingReport.objects.get(id=1)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('finishprocessingreport_delete', args=[report_obj.id]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis_reports/finishprocessingreport/confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'], report_obj)

    def test_delete_view_post_htmx(self):
        report_to_delete = FinishProcessingReport.objects.create(
            id=99,
            fin_year='2020-21', po_no='TEMP-DEL', po_date=timezone.now().date(),
            item_code='TDEL', manf_desc='Temporary Delete', qty=1,
            mrs_no='TDEL', mrs_date=timezone.now().date(),
            min_no='TDEL', min_date=timezone.now().date()
        )
        report_count_before = FinishProcessingReport.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('finishprocessingreport_delete', args=[report_to_delete.id]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertFalse(FinishProcessingReport.objects.filter(id=report_to_delete.id).exists())
        self.assertEqual(FinishProcessingReport.objects.count(), report_count_before - 1)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshFinishProcessingReportList')

```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

The provided templates and views already integrate HTMX for dynamic content loading, form submissions, and modal interactions. Alpine.js is included in the `list.html` block but no specific `x-data` directives are currently used for complex UI state, as HTMX handles most of the reactivity directly. DataTables is explicitly initialized in the `_finishprocessingreport_table.html` partial for client-side functionality.

*   **HTMX:**
    *   List view (`list.html`) uses `hx-trigger="load, refreshFinishProcessingReportList from:body"` and `hx-get` to dynamically load the table content.
    *   CRUD buttons (`Add New`, `Edit`, `Delete`) use `hx-get` to fetch forms/confirmation dialogues into the modal target (`#modalContent`).
    *   Form submissions (`_form.html`, `_confirm_delete.html`) use `hx-post` with `hx-swap="none"` and rely on the `HX-Trigger` header from the Django view to signal a refresh of the list (`refreshFinishProcessingReportList`).
    *   Modal activation is handled by `_="on click add .is-active to #modal"` and deactivation by `_="on click if event.target.id == 'modal' remove .is-active from me"`.
*   **Alpine.js:**
    *   The `{% block extra_js %}` in `base.html` (implicitly) and `list.html` includes Alpine.js. While not explicitly used with `x-data` in this specific example, it's available for more complex client-side UI states or interactions if needed later.
*   **DataTables:**
    *   The `_finishprocessingreport_table.html` partial initializes DataTables on the `finishprocessingreportTable` ID. This provides client-side searching, sorting, and pagination for the report data, replicating and enhancing the original ASP.NET GridView functionality.

## Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating your `FinishProcessingReport` module to Django. By leveraging automation-friendly principles, it ensures a smooth transition, resulting in a modern, maintainable, and high-performing application. Remember to replace any placeholders (like database connection details in Django settings) with your specific environment configurations.