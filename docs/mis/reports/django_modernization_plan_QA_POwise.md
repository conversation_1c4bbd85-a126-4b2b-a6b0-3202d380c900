## ASP.NET to Django Conversion Script: QA-PO Wise Report & Graph

This document outlines a comprehensive plan for modernizing the provided ASP.NET application, focusing on transitioning from legacy ASP.NET controls and server-side logic to a robust Django 5.0+ solution. The core principle is automation-driven migration, emphasizing reusable components, clear separation of concerns, and a highly interactive user experience using HTMX and Alpine.js.

The original ASP.NET application provides two main functionalities: a "QA Report" (likely a detailed list/summary) powered by Crystal Reports, and a "QA Graph" showing UOM-wise QA quantities with supplier filtering. Our Django solution will transform these into a modern, responsive web application.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is extended.
- Focus ONLY on component-specific code for the current module (`qareports` app).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the SQL queries and data access patterns in the ASP.NET code, we infer the following database tables and their relevant columns. Note that some entities (like `View_QA_PR`) are likely database views or complex query results, which will be handled by specific methods within Django models rather than direct model mappings.

**Identified Tables and Inferred Structure:**

*   **`tblMM_PO_Master`**: Represents Purchase Order master data.
    *   `Id` (PK, int): Primary key for the PO.
    *   `PONo` (string): Purchase Order number.
    *   `SupplierId` (int): Foreign key to `tblMM_Supplier_master`.
    *   `FinyearId` (int): Foreign key to `tblfinancial_master`.
    *   `PRSPRFlag` (string, '0' or '1'): Flag indicating PO type (e.g., Purchase Requisition, Sales Purchase Requisition).

*   **`tblMM_Supplier_master`**: Stores supplier information.
    *   `SupplierId` (PK, int): Primary key for the supplier.
    *   `SupplierName` (string): Name of the supplier.
    *   `CompId` (int): Company ID, used for filtering.

*   **`tblfinancial_master`**: Holds financial year data.
    *   `FinYearId` (PK, int): Primary key for the financial year.
    *   `FinYear` (string): The financial year (e.g., "2023-2024").

*   **Inferred Report Data Sources (Complex Queries/Views):**
    The original code extensively uses various `View_QA_PR`, `View_QA_SPR`, `View_GQN_UOM_Graph`, `View_GSN_UOM_Graph`, `View_PVEV_UOM_Graph`. These are complex data aggregations. Instead of directly mapping these as Django models, their logic will be re-implemented as specialized methods within our Django models or a dedicated utility class, returning the necessary aggregated data for reports and charts. The key fields from these aggregated views include `UOM` (Unit of Measure), `POQty`, `GQNQty`, `GSNQty`, `PVEVQty`.

### Step 2: Identify Backend Functionality

The ASP.NET application provides two primary functionalities:

1.  **QA-PO Wise Report (Read/Display):**
    *   Displays a detailed report, which was originally a Crystal Report. This implies a need to present a comprehensive list of Purchase Orders (`tblMM_PO_Master`) along with related QA details (inferred from `View_QA_PR`/`View_QA_SPR`).
    *   **Django Equivalent:** A Django ListView displaying `POMaster` objects, rendered as a DataTables-enabled HTML table.

2.  **QA Graph (Read/Display with Filtering):**
    *   Presents a chart showing QA quantities (PO Qty, GQN Qty, GSN Qty, PVEV Qty) grouped by Unit of Measure (UOM).
    *   Allows filtering the graph data by `SupplierName` using an autocomplete textbox.
    *   **Django Equivalent:**
        *   A Django view that renders a template with a client-side charting library (Chart.js) and filtering controls.
        *   A separate Django view that provides the aggregated chart data as JSON, based on the selected filters.
        *   An HTMX-powered endpoint for supplier autocomplete suggestions.

**Business Logic:**
The primary business logic involves complex data aggregation and summation across multiple underlying tables/views to generate the report and graph data. This logic, particularly the `drawgraph()` method's extensive SQL and LINQ operations, will be encapsulated within Django model methods or custom managers to ensure the "Fat Model" principle.

### Step 3: Infer UI Components

The ASP.NET user interface relies on server-side Web Forms controls and the `AjaxControlToolkit` for interactivity.

*   **`TabContainer` and `TabPanel`**: These will be replaced by standard HTML `div` elements, managed by Alpine.js for tab state and HTMX `hx-get` attributes for lazy loading of tab content.
*   **`CrystalReportViewer`**: This proprietary component will be replaced by a standard HTML `<table>` element, enhanced with DataTables for client-side sorting, filtering, and pagination.
*   **`UpdatePanel`**: The functionality provided by `UpdatePanel` (partial page updates) will be entirely replaced by HTMX, which offers a more declarative and efficient way to achieve dynamic content updates without writing custom JavaScript.
*   **`TextBox` (`txtSupplier`) with `AutoCompleteExtender`**: This will become a standard HTML `<input type="text">` field. The autocomplete functionality will be reimplemented using HTMX to fetch suggestions from a Django view and Alpine.js to manage selection and update the form.
*   **`Button` (`btnSearch`)**: This will be a standard HTML `<button>` that triggers an HTMX request to refresh the graph data.
*   **`Chart` (ASP.NET Chart Control)**: This server-side charting component will be replaced by a client-side JavaScript charting library (e.g., Chart.js), which will consume JSON data provided by a Django view.

### Step 4: Generate Django Code

We will create a new Django application, named `qareports`, to house all related components.

#### 4.1 Models (`qareports/models.py`)

We'll define models for the main database tables. The complex querying logic for the reports and graphs will reside as class methods within these models or a dedicated utility class. We will also include a placeholder `clsFunctions` utility class to simulate the original application's helper functions, demonstrating how their logic would be re-implemented in Python.

```python
from django.db import models
from django.db.models import Q
import json # Used in the dummy dictfetchall for JSON serialization example

# --- Helper Utility Class (Mimicking ASP.NET's clsFunctions) ---
# In a real project, this would likely be in a separate 'core/utils.py'
# or as part of a dedicated service layer.
class ClsFunctions:
    """
    Mimics the utility functions from the original ASP.NET clsFunctions class.
    In a real Django application, database connections are managed by Django ORM.
    These methods are placeholders for business logic or data transformations.
    """
    def Connection(self):
        # In Django, database connection strings are configured in settings.py
        # This method is purely illustrative for migration context.
        return "Django manages connections via settings.py"

    def getCompany(self, comp_id):
        # Placeholder: In a real app, this would query a 'Company' model.
        if comp_id == 1:
            return "Global Tech Inc."
        return f"Unknown Company {comp_id}"

    def CompAdd(self, comp_id):
        # Placeholder: In a real app, this would query a 'Company' model.
        if comp_id == 1:
            return "123 Innovation Drive, Tech City"
        return f"Address for {comp_id}"

    def getCode(self, text_with_id):
        """
        Extracts an integer ID from a string in the format "Name [ID]".
        """
        try:
            # Find the last occurrence of '[' and remove ']' at the end
            start_bracket = text_with_id.rfind('[')
            if start_bracket != -1 and text_with_id.endswith(']'):
                id_str = text_with_id[start_bracket + 1:-1]
                return int(id_str)
        except (ValueError, IndexError):
            pass # Return None if parsing fails
        return None
        
    def dictfetchall(self, cursor):
        """
        Return all rows from a database cursor as a list of dictionaries.
        Used for raw SQL query results.
        """
        columns = [col[0] for col in cursor.description]
        return [
            dict(zip(columns, row))
            for row in cursor.fetchall()
        ]

# Instantiate the utility class for use in models and views
fun = ClsFunctions()

# --- Django Models ---
# Models are mapped to existing tables, so 'managed = False' is used.

class FinancialYear(models.Model):
    """
    Maps to tblfinancial_master. Represents a financial year.
    """
    id = models.IntegerField(db_column='FinYearId', primary_key=True)
    year = models.CharField(db_column='FinYear', max_length=100)

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblfinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.year

class Supplier(models.Model):
    """
    Maps to tblMM_Supplier_master. Represents a supplier.
    """
    id = models.IntegerField(db_column='SupplierId', primary_key=True)
    name = models.CharField(db_column='SupplierName', max_length=255)
    company_id = models.IntegerField(db_column='CompId') # Assuming this is the company ID

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return f"{self.name} [{self.id}]"

class POMaster(models.Model):
    """
    Maps to tblMM_PO_Master. Represents a Purchase Order master record.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    po_no = models.CharField(db_column='PONo', max_length=255)
    supplier = models.ForeignKey(Supplier, on_delete=models.DO_NOTHING, db_column='SupplierId', related_name='po_masters')
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinyearId', related_name='po_masters')
    pr_spr_flag = models.CharField(db_column='PRSPRFlag', max_length=1) # '0' for PR, '1' for SPR

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'

    def __str__(self):
        return f"{self.po_no} ({self.supplier.name})"

    def get_pr_spr_flag_display(self):
        """Returns human-readable text for the PRSPRFlag."""
        return "Purchase Requisition" if self.pr_spr_flag == '0' else "Sales Purchase Requisition" if self.pr_spr_flag == '1' else "Unknown"

    @classmethod
    def get_qa_graph_data(cls, financial_year_id, supplier_id=None):
        """
        Aggregates QA quantities (PO, GQN, GSN, PVEV) by UOM for charting.
        This method re-implements the complex SQL and LINQ logic from the original drawgraph().
        """
        supplier_filter_sql = f"AND SupplierId='{supplier_id}'" if supplier_id else ""
        
        # Raw SQL query to replicate the original C# aggregation logic precisely.
        # This uses Common Table Expressions (CTEs) for clarity and combines data
        # using FULL OUTER JOIN to ensure all UOMs are included from all sources.
        sql_query = f"""
            WITH UOM_PO_Data AS (
                SELECT UOM, SUM(POQty) AS POQty FROM View_QA_PR WHERE FinYearId={financial_year_id} {supplier_filter_sql} GROUP BY UOM
                UNION ALL
                SELECT UOM, SUM(POQty) AS POQty FROM View_QA_SPR WHERE FinYearId={financial_year_id} {supplier_filter_sql} GROUP BY UOM
            ),
            UOM_GQN_Data AS (
                SELECT UOM, SUM(GQNQty) AS GQNQty FROM View_GQN_UOM_Graph WHERE FinYearId={financial_year_id} {supplier_filter_sql} GROUP BY UOM
            ),
            UOM_GSN_Data AS (
                SELECT UOM, SUM(GSNQty) AS GSNQty FROM View_GSN_UOM_Graph WHERE FinYearId={financial_year_id} {supplier_filter_sql} GROUP BY UOM
            ),
            UOM_PVEV_Data AS (
                SELECT UOM, SUM(PvevQty) AS PvevQty FROM View_PVEV_UOM_Graph WHERE FinYearId={financial_year_id} {supplier_filter_sql} GROUP BY UOM
            )
            SELECT
                COALESCE(p.UOM, gqn.UOM, gsn.UOM, pvev.UOM) AS UOM,
                COALESCE(SUM(p.POQty), 0.0) AS POQty,
                COALESCE(SUM(gqn.GQNQty), 0.0) AS GQNQty,
                COALESCE(SUM(gsn.GSNQty), 0.0) AS GSNQty,
                COALESCE(SUM(pvev.PVEVQty), 0.0) AS PVEVQty
            FROM UOM_PO_Data p
            FULL OUTER JOIN UOM_GQN_Data gqn ON p.UOM = gqn.UOM
            FULL OUTER JOIN UOM_GSN_Data gsn ON COALESCE(p.UOM, gqn.UOM) = gsn.UOM
            FULL OUTER JOIN UOM_PVEV_Data pvev ON COALESCE(p.UOM, gqn.UOM, gsn.UOM) = pvev.UOM
            GROUP BY COALESCE(p.UOM, gqn.UOM, gsn.UOM, pvev.UOM)
            ORDER BY COALESCE(p.UOM, gqn.UOM, gsn.UOM, pvev.UOM)
        """
        
        with models.connections['default'].cursor() as cursor:
            cursor.execute(sql_query)
            # Use the helper function to fetch results as dictionaries
            data = fun.dictfetchall(cursor)
            
        return data

```

#### 4.2 Forms (`qareports/forms.py`)

A simple form for filtering the graph by supplier will be needed. This form will interact with HTMX for autocomplete suggestions.

```python
from django import forms
from .models import Supplier, fun # Import the helper utility

class GraphFilterForm(forms.Form):
    """
    Form for filtering the QA graph data, specifically for supplier selection.
    """
    supplier = forms.CharField(
        label="Supplier Name",
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Start typing supplier name...',
            'hx-get': '/qareports/suppliers/autocomplete/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#supplier-suggestions',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off', # Disable browser's native autocomplete
            'name': 'supplier_text', # Field name for the text input
        })
    )
    # Hidden field to store the actual supplier ID once selected from autocomplete
    supplier_id = forms.IntegerField(
        widget=forms.HiddenInput(), 
        required=False,
        initial=None,
    )

    def clean_supplier(self):
        """
        Custom cleaning for the supplier field to ensure a valid supplier is selected
        if text is provided.
        """
        supplier_text = self.cleaned_data.get('supplier')
        
        # If supplier text is provided, try to extract an ID using the helper function
        if supplier_text:
            extracted_id = fun.getCode(supplier_text)
            if extracted_id is not None:
                try:
                    # Verify if the extracted ID corresponds to an actual supplier
                    supplier_obj = Supplier.objects.get(id=extracted_id)
                    # Optionally, check if the name part also matches (e.g., "Name [ID]")
                    if f"{supplier_obj.name} [{supplier_obj.id}]" != supplier_text:
                        self.add_error('supplier', 'Supplier name and ID do not match a valid entry. Please select from suggestions.')
                        self.cleaned_data['supplier_id'] = None
                    else:
                        self.cleaned_data['supplier_id'] = extracted_id # Set the hidden ID
                except Supplier.DoesNotExist:
                    self.add_error('supplier', 'Invalid supplier ID extracted. Supplier not found.')
                    self.cleaned_data['supplier_id'] = None
            else:
                self.add_error('supplier', 'Please select a supplier from the autocomplete suggestions.')
                self.cleaned_data['supplier_id'] = None
        else:
            self.cleaned_data['supplier_id'] = None # Clear hidden ID if text is empty
            
        return supplier_text

```

#### 4.3 Views (`qareports/views.py`)

Views will be thin, focusing on orchestrating data retrieval from models and rendering appropriate templates. HTMX will manage the partial updates.

```python
import json
from django.views.generic import TemplateView, ListView, View
from django.http import JsonResponse, HttpResponse
from django.shortcuts import render
from django.db.models import Q
from django.contrib import messages
from .models import POMaster, Supplier, FinancialYear, fun
from .forms import GraphFilterForm

class QAReportsDashboardView(TemplateView):
    """
    Main dashboard view that serves the initial page with tabs.
    It prepares the context including financial year and company ID from session.
    """
    template_name = 'qareports/qa_powise_dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Session data needs to be populated via a login system or middleware
        # For demonstration, we use placeholder values or assume session is set.
        context['fin_year_id'] = self.request.session.get('finyear', 1) 
        context['company_id'] = self.request.session.get('compid', 1)
        
        # Instantiate the form for the graph filter, binding request.GET data if present
        context['graph_filter_form'] = GraphFilterForm(self.request.GET or None)
        
        # Fetch the actual financial year string for display in graph
        try:
            context['fin_year_value'] = FinancialYear.objects.get(id=context['fin_year_id']).year
        except FinancialYear.DoesNotExist:
            context['fin_year_value'] = "N/A"

        return context

class QAPOListView(ListView):
    """
    View for the "QA Report" tab content (replacement for Crystal Report).
    Displays a list of Purchase Order masters.
    """
    model = POMaster
    template_name = 'qareports/_qa_po_list_partial.html' # Rendered via HTMX swap
    context_object_name = 'po_masters'

    def get_queryset(self):
        # Retrieve financial year from session, similar to original ASP.NET code
        fin_year_id = self.request.session.get('finyear', 1)
        # Fetch related supplier and financial year data to avoid N+1 queries
        queryset = POMaster.objects.filter(
            financial_year_id=fin_year_id
        ).select_related('supplier', 'financial_year').order_by('id')
        return queryset

class QAGraphPartialView(TemplateView):
    """
    View for the "QA Graph" tab content. Renders the form and chart canvas.
    """
    template_name = 'qareports/_qa_graph_partial.html' # Rendered via HTMX swap

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['graph_filter_form'] = GraphFilterForm(self.request.GET or None)
        context['fin_year_id'] = self.request.session.get('finyear', 1) 
        context['company_id'] = self.request.session.get('compid', 1)

        try:
            context['fin_year_value'] = FinancialYear.objects.get(id=context['fin_year_id']).year
        except FinancialYear.DoesNotExist:
            context['fin_year_value'] = "N/A"

        return context

class QAGraphDataView(View):
    """
    API endpoint that provides JSON data for the Chart.js graph.
    """
    def get(self, request, *args, **kwargs):
        fin_year_id = request.session.get('finyear', 1)
        
        # Get supplier ID from the request (sent from the hidden input in the form)
        supplier_id = request.GET.get('supplier_id')
        # Ensure supplier_id is an integer if provided
        if supplier_id:
            try:
                supplier_id = int(supplier_id)
            except ValueError:
                supplier_id = None # Invalid ID, treat as no filter
        
        # Call the fat model method to get the aggregated graph data
        graph_data = POMaster.get_qa_graph_data(
            financial_year_id=fin_year_id, 
            supplier_id=supplier_id
        )

        # Prepare the data in a format suitable for Chart.js
        labels = [item['UOM'] for item in graph_data]
        po_qty = [item['POQty'] for item in graph_data]
        gqn_qty = [item['GQNQty'] for item in graph_data]
        gsn_qty = [item['GSNQty'] for item in graph_data]
        pvev_qty = [item['PVEVQty'] for item in graph_data]

        chart_data = {
            'labels': labels,
            'datasets': [
                {'label': 'PO Qty', 'data': po_qty, 'backgroundColor': 'red', 'borderWidth': 1},
                {'label': 'GQN Qty', 'data': gqn_qty, 'backgroundColor': 'green', 'borderWidth': 1},
                {'label': 'GSN Qty', 'data': gsn_qty, 'backgroundColor': 'blue', 'borderWidth': 1},
                {'label': 'PVEV Qty', 'data': pvev_qty, 'backgroundColor': 'darkmagenta', 'borderWidth': 1},
            ]
        }
        return JsonResponse(chart_data)

class SupplierAutocompleteView(View):
    """
    HTMX endpoint for providing supplier autocomplete suggestions.
    Returns an HTML list of suggestions.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('supplier_text', '').strip() # Get the typed text
        company_id = request.session.get('compid', 1) # Use session company ID for filtering

        if not prefix_text: # No prefix, return empty
            return HttpResponse('')

        # Filter suppliers by company ID and name containing the prefix text (case-insensitive)
        suppliers = Supplier.objects.filter(
            Q(company_id=company_id) & Q(name__icontains=prefix_text)
        ).values_list('name', 'id')[:10] # Limit to top 10 suggestions, consistent with original behavior

        # Sort results alphabetically by name
        results = sorted([f"{name} [{supplier_id}]" for name, supplier_id in suppliers])
        
        # Construct an HTML unordered list for HTMX to swap into the target div
        html_response = ""
        if results:
            html_response += '<ul class="border border-gray-300 rounded-md shadow-lg bg-white mt-1 max-h-60 overflow-y-auto z-10 absolute w-full">'
            for item in results:
                # Extract name and ID to set values in the hidden form field
                name_part = item.split(' [')[0]
                id_part = fun.getCode(item) # Use the helper to extract ID reliably

                html_response += f"""
                    <li class="px-3 py-2 cursor-pointer hover:bg-gray-100" 
                        hx-trigger="click" 
                        hx-post="{reverse('set_supplier_selection')}"
                        hx-vals='{{"supplier_text": "{name_part} [{id_part}]", "supplier_id": "{id_part}"}}'
                        hx-swap="none"
                        hx-target="body"
                        hx-on::after-request="document.getElementById('supplier-suggestions').innerHTML = ''">
                        {item}
                    </li>
                """
            html_response += '</ul>'
        
        return HttpResponse(html_response)

class SetSupplierSelectionView(View):
    """
    HTMX endpoint to handle the selection from autocomplete.
    It doesn't render content but triggers a client-side event to update Alpine.js state.
    """
    def post(self, request, *args, **kwargs):
        supplier_text = request.POST.get('supplier_text', '')
        supplier_id = request.POST.get('supplier_id', '')

        response = HttpResponse(status=204) # 204 No Content, used for HTMX triggers
        # Send a custom HTMX-Trigger header to signal Alpine.js to update variables
        response['HX-Trigger'] = json.dumps({
            "setSupplierValues": { # Custom event name for Alpine.js to listen to
                "supplier_text": supplier_text,
                "supplier_id": supplier_id
            }
        })
        return response

```

#### 4.4 Templates (`qareports/templates/qareports/`)

The templates are structured for HTMX partial swaps and DataTables integration.

**`qa_powise_dashboard.html`**: The main page that sets up the tab structure and loads initial content via HTMX.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-6 text-gray-800">QA-PO Wise Report & Graph</h1>

    <div x-data="{ activeTab: 'report' }" class="bg-white shadow-md rounded-lg p-6">
        <!-- Tab Headers -->
        <div class="flex border-b border-gray-200 mb-6">
            <button 
                @click="activeTab = 'report'" 
                :class="{ 'border-blue-500 text-blue-600': activeTab === 'report', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'report' }"
                class="py-2 px-4 focus:outline-none border-b-2 font-medium text-sm transition-colors duration-200"
                hx-get="{% url 'qa_po_list' %}"
                hx-target="#tabContent"
                hx-swap="innerHTML"
                hx-trigger="click once, load" {# Loads report on initial page load and on click #}
                hx-indicator="#tabLoader">
                QA Report
            </button>
            <button 
                @click="activeTab = 'graph'" 
                :class="{ 'border-blue-500 text-blue-600': activeTab === 'graph', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'graph' }"
                class="py-2 px-4 focus:outline-none border-b-2 font-medium text-sm transition-colors duration-200"
                hx-get="{% url 'qa_graph_partial' %}"
                hx-target="#tabContent"
                hx-swap="innerHTML"
                hx-indicator="#tabLoader"
                hx-trigger="click once"> {# Loads graph on first click #}
                QA Graph
            </button>
        </div>

        <!-- Hidden inputs for session data passed to partials/JS -->
        <input type="hidden" id="finYearId" value="{{ fin_year_id }}">
        <input type="hidden" id="companyId" value="{{ company_id }}">
        <input type="hidden" id="finYearValue" value="{{ fin_year_value }}">

        <!-- Tab Content Loader -->
        <div id="tabLoader" class="htmx-indicator text-center py-4">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading...</p>
        </div>

        <!-- Tab Content Area -->
        <div id="tabContent">
            <!-- Content will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Chart.js CDN (assuming it's not already in base.html) -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js"></script>

<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js data for the QA Graph functionality
        Alpine.data('qaGraph', () => ({
            chartInstance: null,
            supplierText: '', // Binds to the text input for supplier name
            supplierId: '',   // Binds to the hidden input for supplier ID

            init() {
                // Initialize supplierText and supplierId from form if they were pre-filled (e.g., via GET params)
                const initialSupplierText = document.querySelector('input[name="supplier_text"]');
                const initialSupplierId = document.querySelector('input[name="supplier_id"]');
                if (initialSupplierText) this.supplierText = initialSupplierText.value;
                if (initialSupplierId) this.supplierId = initialSupplierId.value;

                // Listen for custom HTMX-triggered event to update Alpine.js state
                document.body.addEventListener('setSupplierValues', (event) => {
                    this.supplierText = event.detail.supplier_text;
                    this.supplierId = event.detail.supplier_id;
                    // Clear autocomplete suggestions after selection
                    const suggestionsDiv = document.getElementById('supplier-suggestions');
                    if (suggestionsDiv) {
                        suggestionsDiv.innerHTML = '';
                    }
                    // Trigger graph refresh after setting supplier values
                    const searchButton = document.getElementById('btnSearchGraph');
                    if (searchButton) {
                        htmx.trigger(searchButton, 'click');
                    }
                });
            },

            loadChartData() {
                const finYearId = document.getElementById('finYearId').value;
                let url = `/qareports/graph-data/?fin_year_id=${finYearId}`;
                if (this.supplierId) {
                    url += `&supplier_id=${this.supplierId}`;
                }

                fetch(url)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        this.renderChart(data);
                    })
                    .catch(error => console.error('Error fetching chart data:', error));
            },

            renderChart(data) {
                const ctx = document.getElementById('qaChartCanvas');
                if (!ctx) {
                    console.warn("Chart canvas not found. Skipping chart rendering.");
                    return;
                }
                if (this.chartInstance) {
                    this.chartInstance.destroy(); // Destroy existing chart instance
                }

                this.chartInstance = new Chart(ctx, {
                    type: 'bar', // Bar chart for UOM quantities
                    data: {
                        labels: data.labels,
                        datasets: data.datasets.map(dataset => ({
                            ...dataset,
                            borderRadius: 4, // Simulate 'Cylinder' style for bars
                            barPercentage: 0.8, // Control bar width relative to category
                            categoryPercentage: 0.6 // Control spacing between categories
                        }))
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false, // Allows flexible sizing
                        plugins: {
                            legend: {
                                display: true,
                                position: 'bottom', // Legend below the chart
                                labels: {
                                    font: {
                                        size: 10
                                    }
                                }
                            },
                            title: {
                                display: true,
                                text: 'QA Report', // Chart title
                                font: {
                                    size: 16,
                                    weight: 'bold'
                                }
                            }
                        },
                        scales: {
                            x: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: `Fin. Year ${document.getElementById('finYearValue').value || ''}`, // X-axis title
                                    font: {
                                        size: 12
                                    }
                                },
                                ticks: {
                                    autoSkip: false,
                                    maxRotation: 90, // Rotate labels for better readability
                                    minRotation: 90
                                },
                                grid: {
                                    display: false // Hide x-axis grid lines
                                }
                            },
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Quantity', // Y-axis title
                                    font: {
                                        size: 12
                                    }
                                },
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.05)' // Light y-axis grid lines
                                }
                            }
                        },
                        animation: {
                            duration: 1000,
                            easing: 'easeOutQuart'
                        },
                        // 3D effect approximation (Chart.js doesn't have native 3D bars)
                        // This would typically involve a custom plugin or more complex rendering
                        // For a simple 'cylinder' look, borderRadius helps.
                    }
                });
            }
        }));
    });
</script>
{% endblock %}
```

**`_qa_po_list_partial.html`**: This partial template is loaded into the "QA Report" tab via HTMX. It contains the DataTables structure.

```html
<div class="p-4">
    <h3 class="text-xl font-semibold mb-4 text-gray-800">QA-PO Wise Report Details</h3>
    
    <div id="poListTableContainer" class="overflow-x-auto bg-white rounded-lg shadow">
        <table id="qaPOListTable" class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                    <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No.</th>
                    <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
                    <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO Type</th>
                    <!-- Add more columns here based on the detailed report requirements -->
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for po in po_masters %}
                <tr class="hover:bg-gray-50">
                    <td class="py-3 px-4 whitespace-nowrap">{{ forloop.counter }}</td>
                    <td class="py-3 px-4 whitespace-nowrap">{{ po.po_no }}</td>
                    <td class="py-3 px-4 whitespace-nowrap">{{ po.supplier.name }}</td>
                    <td class="py-3 px-4 whitespace-nowrap">{{ po.get_pr_spr_flag_display }}</td>
                    <!-- Render additional detail columns here -->
                </tr>
                {% empty %}
                <tr>
                    <td colspan="4" class="py-4 px-4 text-center text-gray-500">No Purchase Orders found for this period.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<script>
    // Initialize DataTables after the content is loaded by HTMX
    $(document).ready(function() {
        // Only initialize if the table hasn't been initialized before (e.g., if HTMX reloads same content)
        if (!$.fn.DataTable.isDataTable('#qaPOListTable')) {
            $('#qaPOListTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "responsive": true, // Enable responsive design for DataTables
                "dom": 'lfrtip', // Layout: Length changing input, Filtering input, Table, Information, Pagination, Processing
                "language": { // Optional: Customize language strings
                    "search": "Filter records:",
                    "lengthMenu": "Show _MENU_ entries"
                }
            });
        }
    });
</script>
```

**`_qa_graph_partial.html`**: This partial template is loaded into the "QA Graph" tab via HTMX. It contains the filter form and the `canvas` for the chart.

```html
<div class="p-4" x-data="qaGraph">
    <fieldset class="border border-gray-300 p-6 rounded-md shadow-sm">
        <legend class="text-lg font-medium text-gray-900 px-2">UOM Wise QA Chart Of Supplier</legend>

        <div class="mb-6">
            <div class="flex items-end space-x-4">
                <div class="flex-grow relative">
                    <label for="{{ graph_filter_form.supplier.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ graph_filter_form.supplier.label }}:</label>
                    <input type="text" 
                           name="{{ graph_filter_form.supplier.name }}" 
                           id="{{ graph_filter_form.supplier.id_for_label }}" 
                           x-model="supplierText" {# Binds to Alpine.js state #}
                           class="{{ graph_filter_form.supplier.field.widget.attrs.class }}"
                           hx-get="{% url 'supplier_autocomplete' %}"
                           hx-trigger="keyup changed delay:500ms from:#{{ graph_filter_form.supplier.id_for_label }}"
                           hx-target="#supplier-suggestions"
                           hx-swap="innerHTML"
                           autocomplete="off"
                           placeholder="Start typing supplier name..."
                           @input="if(supplierText == '') supplierId = '';" {# Clear hidden ID if text is cleared #}
                           />
                    <input type="hidden" 
                           name="{{ graph_filter_form.supplier_id.name }}" 
                           id="{{ graph_filter_form.supplier_id.id_for_label }}" 
                           x-model="supplierId" /> {# Binds to Alpine.js state #}
                    <div id="supplier-suggestions" class="absolute z-10 w-full"></div>
                    {% if graph_filter_form.supplier.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ graph_filter_form.supplier.errors }}</p>
                    {% endif %}
                </div>
                <button id="btnSearchGraph" type="button" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded" @click="loadChartData()">
                    Search
                </button>
            </div>
            <div id="graphLoader" class="htmx-indicator text-center py-4">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2">Loading chart data...</p>
            </div>
        </div>

        <div class="w-full h-96 relative">
            <canvas id="qaChartCanvas" style="height: 400px; width: 800px;"></canvas>
        </div>
    </fieldset>

    <script>
        // Use Alpine.js's $nextTick to ensure DOM is ready before initializing chart
        document.addEventListener('DOMContentLoaded', () => {
             // Access the Alpine.js component and call its method
             const qaGraphInstance = document.querySelector('[x-data="qaGraph"]');
             if (qaGraphInstance && qaGraphInstance.__alpine_instance) {
                // Ensure Alpine.js has fully initialized this component before calling methods
                qaGraphInstance.__alpine_instance.$data.loadChartData();
             }
        });
    </script>
</div>
```

#### 4.5 URLs (`qareports/urls.py`)

Define URL patterns for the main dashboard, tab contents, graph data, and autocomplete.

```python
from django.urls import path
from .views import (
    QAReportsDashboardView,
    QAPOListView,
    QAGraphPartialView,
    QAGraphDataView,
    SupplierAutocompleteView,
    SetSupplierSelectionView,
)

urlpatterns = [
    # Main dashboard view for QA-PO Wise Report & Graph
    path('qa-powise/', QAReportsDashboardView.as_view(), name='qa_powise_dashboard'),
    
    # HTMX endpoints for tab content
    path('qa-powise/po-list/', QAPOListView.as_view(), name='qa_po_list'),
    path('qa-powise/graph-partial/', QAGraphPartialView.as_view(), name='qa_graph_partial'),
    
    # API endpoint for Chart.js data
    path('qa-powise/graph-data/', QAGraphDataView.as_view(), name='qa_graph_data'),
    
    # HTMX endpoints for supplier autocomplete
    path('qa-powise/suppliers/autocomplete/', SupplierAutocompleteView.as_view(), name='supplier_autocomplete'),
    path('qa-powise/set_supplier_selection/', SetSupplierSelectionView.as_view(), name='set_supplier_selection'),
]

```

#### 4.6 Tests (`qareports/tests.py`)

Comprehensive unit tests for models and integration tests for views ensure the correctness and robustness of the migrated application.

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
import json
from django.db.models import Q # For checking filter calls

from .models import POMaster, Supplier, FinancialYear, fun # Import the 'fun' utility
from .forms import GraphFilterForm

# --- Mocking for clsFunctions utility ---
# We create a mock version of the 'fun' object to control its behavior
# during tests, especially for raw SQL execution and ID extraction.
class MockClsFunctions:
    """
    Mock class to simulate clsFunctions for testing purposes.
    This allows us to control the behavior of its methods without
    actual database or external dependencies.
    """
    def Connection(self):
        return "mock_connection_string"

    def getCompany(self, comp_id):
        return f"Mock Company {comp_id}"

    def CompAdd(self, comp_id):
        return f"Mock Address {comp_id}"

    def getCode(self, text_with_id):
        """
        Mock implementation to extract ID from 'Name [ID]' string.
        """
        try:
            start_bracket = text_with_id.rfind('[')
            if start_bracket != -1 and text_with_id.endswith(']'):
                id_str = text_with_id[start_bracket + 1:-1]
                return int(id_str)
        except (ValueError, IndexError):
            pass
        return None

    def dictfetchall(self, cursor):
        """
        Mock implementation for fetching raw SQL results as dictionaries.
        This allows tests to inject specific data based on the SQL query.
        """
        # Simulate different raw SQL query results based on a 'mock_sql' attribute
        # which will be set by the mock cursor.execute.
        mock_sql = getattr(cursor, 'mock_sql', '')
        
        if 'UOM_PO_Data' in mock_sql: # This means it's the graph data query
            return [
                {'UOM': 'KG', 'POQty': 100.0, 'GQNQty': 90.0, 'GSNQty': 80.0, 'PVEVQty': 70.0},
                {'UOM': 'PCS', 'POQty': 50.0, 'GQNQty': 45.0, 'GSNQty': 35.0, 'PVEVQty': 25.0},
            ]
        # For other raw queries, return empty or specific data as needed for tests
        return []

# Patch the global 'fun' instance with our mock for tests
mock_fun_instance = MockClsFunctions()

# --- Model Unit Tests ---
class QAReportsModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create actual database entries for models
        cls.financial_year_2023 = FinancialYear.objects.create(id=1, year='2023-2024')
        cls.financial_year_2024 = FinancialYear.objects.create(id=2, year='2024-2025')
        cls.supplier_alpha = Supplier.objects.create(id=101, name='Supplier Alpha', company_id=1)
        cls.supplier_beta = Supplier.objects.create(id=102, name='Supplier Beta', company_id=1)
        
        cls.po_master_pr = POMaster.objects.create(
            id=1, po_no='PO001', supplier=cls.supplier_alpha, 
            financial_year=cls.financial_year_2023, pr_spr_flag='0'
        )
        cls.po_master_spr = POMaster.objects.create(
            id=2, po_no='PO002', supplier=cls.supplier_beta, 
            financial_year=cls.financial_year_2023, pr_spr_flag='1'
        )
        cls.po_master_other_year = POMaster.objects.create(
            id=3, po_no='PO003', supplier=cls.supplier_alpha,
            financial_year=cls.financial_year_2024, pr_spr_flag='0'
        )
  
    def test_financial_year_creation(self):
        self.assertEqual(self.financial_year_2023.year, '2023-2024')
        self.assertEqual(str(self.financial_year_2023), '2023-2024')
        self.assertEqual(FinancialYear.objects.count(), 2)
        
    def test_supplier_creation(self):
        self.assertEqual(self.supplier_alpha.name, 'Supplier Alpha')
        self.assertEqual(self.supplier_alpha.company_id, 1)
        self.assertEqual(str(self.supplier_alpha), 'Supplier Alpha [101]')
        self.assertEqual(Supplier.objects.count(), 2)

    def test_po_master_creation(self):
        self.assertEqual(self.po_master_pr.po_no, 'PO001')
        self.assertEqual(self.po_master_pr.supplier.name, 'Supplier Alpha')
        self.assertEqual(self.po_master_pr.financial_year.year, '2023-2024')
        self.assertEqual(self.po_master_pr.pr_spr_flag, '0')
        self.assertEqual(str(self.po_master_pr), 'PO001 (Supplier Alpha)')
        self.assertEqual(POMaster.objects.count(), 3)

    def test_get_pr_spr_flag_display(self):
        self.assertEqual(self.po_master_pr.get_pr_spr_flag_display(), 'Purchase Requisition')
        self.assertEqual(self.po_master_spr.get_pr_spr_flag_display(), 'Sales Purchase Requisition')
        # Test an unknown flag for robustness
        self.po_master_pr.pr_spr_flag = 'X'
        self.assertEqual(self.po_master_pr.get_pr_spr_flag_display(), 'Unknown')

    @patch('qareports.models.fun', new=mock_fun_instance)
    @patch('django.db.models.connections') # Mock database connections for raw SQL
    def test_get_qa_graph_data(self, mock_connections):
        # Configure the mock cursor to return specific SQL and then data
        mock_cursor = MagicMock()
        mock_cursor.execute.side_effect = lambda sql: setattr(mock_cursor, 'mock_sql', sql)
        mock_connections.__getitem__.return_value.cursor.return_value.__enter__.return_value = mock_cursor

        # Test without supplier filter
        graph_data = POMaster.get_qa_graph_data(financial_year_id=self.financial_year_2023.id)
        self.assertGreater(len(graph_data), 0)
        self.assertIn('UOM', graph_data[0])
        self.assertEqual(graph_data[0]['UOM'], 'KG')
        self.assertEqual(graph_data[0]['POQty'], 100.0)
        self.assertIn('AND SupplierId=\'101\'', mock_cursor.mock_sql) # Check if supplier filter was applied in SQL

        # Test with supplier filter
        graph_data_filtered = POMaster.get_qa_graph_data(
            financial_year_id=self.financial_year_2023.id, 
            supplier_id=self.supplier_alpha.id
        )
        self.assertGreater(len(graph_data_filtered), 0)
        # Verify the supplier filter was included in the SQL
        self.assertIn(f"AND SupplierId='{self.supplier_alpha.id}'", mock_cursor.mock_sql)


# --- View Integration Tests ---
class QAReportsViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal actual database objects for views to query
        cls.financial_year = FinancialYear.objects.create(id=1, year='2023-2024')
        cls.supplier = Supplier.objects.create(id=101, name='Test Supplier', company_id=1)
        POMaster.objects.create(id=1, po_no='PO001', supplier=cls.supplier, financial_year=cls.financial_year, pr_spr_flag='0')
        POMaster.objects.create(id=2, po_no='PO002', supplier=cls.supplier, financial_year=cls.financial_year, pr_spr_flag='1')

    def setUp(self):
        self.client = Client()
        # Simulate session data typically set after user login
        session = self.client.session
        session['finyear'] = self.financial_year.id
        session['compid'] = self.supplier.company_id
        session.save() # Save the session

    def test_dashboard_view(self):
        response = self.client.get(reverse('qa_powise_dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qareports/qa_powise_dashboard.html')
        self.assertIn('graph_filter_form', response.context)
        self.assertEqual(response.context['fin_year_id'], self.financial_year.id)
        self.assertEqual(response.context['company_id'], self.supplier.company_id)
        self.assertEqual(response.context['fin_year_value'], self.financial_year.year)

    def test_qa_po_list_view(self):
        response = self.client.get(reverse('qa_po_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qareports/_qa_po_list_partial.html')
        self.assertTrue('po_masters' in response.context)
        self.assertEqual(response.context['po_masters'].count(), 2) # Check objects for current fin year
        self.assertContains(response, 'PO001')
        self.assertContains(response, 'Test Supplier')
        self.assertContains(response, 'Purchase Requisition') # Check for display text

    def test_qa_graph_partial_view(self):
        response = self.client.get(reverse('qa_graph_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qareports/_qa_graph_partial.html')
        self.assertIn('graph_filter_form', response.context)
        self.assertEqual(response.context['fin_year_id'], self.financial_year.id)
        self.assertEqual(response.context['fin_year_value'], self.financial_year.year)

    @patch('qareports.views.POMaster.get_qa_graph_data', MagicMock(return_value=[
        {'UOM': 'KG', 'POQty': 100.0, 'GQNQty': 90.0, 'GSNQty': 80.0, 'PVEVQty': 70.0},
        {'UOM': 'PCS', 'POQty': 50.0, 'GQNQty': 45.0, 'GSNQty': 35.0, 'PVEVQty': 25.0},
    ]))
    def test_qa_graph_data_view(self):
        # Test without supplier filter
        response = self.client.get(reverse('qa_graph_data'))
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn('labels', data)
        self.assertEqual(data['labels'], ['KG', 'PCS'])
        self.assertEqual(data['datasets'][0]['data'], [100.0, 50.0]) # PO Qty dataset
        POMaster.get_qa_graph_data.assert_called_with(
            financial_year_id=self.financial_year.id, 
            supplier_id=None
        )

        # Test with supplier filter
        response = self.client.get(reverse('qa_graph_data') + '?supplier_id=101')
        self.assertEqual(response.status_code, 200)
        POMaster.get_qa_graph_data.assert_called_with(
            financial_year_id=self.financial_year.id, 
            supplier_id=101
        )
    
    @patch('qareports.views.fun', new=mock_fun_instance) # Patch fun for supplier_autocomplete
    @patch('qareports.views.Supplier.objects.filter')
    def test_supplier_autocomplete_view(self, mock_filter):
        # Mock the ORM filter chain for supplier results
        mock_filter.return_value.values_list.return_value = [
            ('Test Supplier A', 101), 
            ('Test Supplier B', 102)
        ]
        
        response = self.client.get(reverse('supplier_autocomplete') + '?supplier_text=Test')
        self.assertEqual(response.status_code, 200)
        self.assertIn('Test Supplier A [101]', response.content.decode())
        self.assertIn('Test Supplier B [102]', response.content.decode())
        # Verify the ORM query was called correctly
        mock_filter.assert_called_with(Q(company_id=self.supplier.company_id) & Q(name__icontains='Test'))
        
        response = self.client.get(reverse('supplier_autocomplete') + '?supplier_text=')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content.decode(), '') # Expect empty response for no prefix

    @patch('qareports.views.fun', new=mock_fun_instance)
    def test_set_supplier_selection_view(self):
        post_data = {'supplier_text': 'Selected Supplier [123]', 'supplier_id': '123'}
        response = self.client.post(reverse('set_supplier_selection'), post_data)
        self.assertEqual(response.status_code, 204) # No Content expected for HTMX trigger
        self.assertIn('HX-Trigger', response)
        trigger_data = json.loads(response['HX-Trigger'])
        self.assertIn('setSupplierValues', trigger_data)
        self.assertEqual(trigger_data['setSupplierValues']['supplier_text'], 'Selected Supplier [123]')
        self.assertEqual(trigger_data['setSupplierValues']['supplier_id'], '123')

# --- Form Unit Tests ---
class GraphFilterFormTest(TestCase):
    @patch('qareports.forms.fun', new=mock_fun_instance) # Patch fun for form validation
    def test_form_valid_data(self):
        # Mock getCode to return a valid ID
        mock_fun_instance.getCode.return_value = 101 
        # Create a matching supplier in DB for validation
        Supplier.objects.create(id=101, name='Valid Supplier', company_id=1) 
        
        form = GraphFilterForm(data={'supplier_text': 'Valid Supplier [101]', 'supplier_id': '101'})
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['supplier_id'], 101)
        self.assertEqual(form.cleaned_data['supplier'], 'Valid Supplier [101]')

    @patch('qareports.forms.fun', new=mock_fun_instance)
    def test_form_invalid_supplier_text_no_id_extracted(self):
        # Mock getCode to return None (e.g., text without [ID])
        mock_fun_instance.getCode.return_value = None 
        form = GraphFilterForm(data={'supplier_text': 'Invalid Supplier', 'supplier_id': ''})
        self.assertFalse(form.is_valid())
        self.assertIn('supplier', form.errors)
        self.assertEqual(form.errors['supplier'][0], 'Please select a supplier from the autocomplete suggestions.')
        self.assertIsNone(form.cleaned_data.get('supplier_id'))

    @patch('qareports.forms.fun', new=mock_fun_instance)
    def test_form_supplier_id_not_matching_db(self):
        # Mock getCode to return an ID that does NOT exist in the database
        mock_fun_instance.getCode.return_value = 999 
        form = GraphFilterForm(data={'supplier_text': 'NonExistent Supplier [999]', 'supplier_id': '999'})
        self.assertFalse(form.is_valid())
        self.assertIn('supplier', form.errors)
        self.assertEqual(form.errors['supplier'][0], 'Invalid supplier selected.')
        self.assertIsNone(form.cleaned_data.get('supplier_id'))

    @patch('qareports.forms.fun', new=mock_fun_instance)
    def test_form_empty_supplier_text(self):
        mock_fun_instance.getCode.return_value = None # No ID extracted
        form = GraphFilterForm(data={'supplier_text': '', 'supplier_id': ''})
        self.assertTrue(form.is_valid()) # Empty is allowed
        self.assertIsNone(form.cleaned_data.get('supplier_id'))
        self.assertEqual(form.cleaned_data.get('supplier'), '')

```

### Step 5: HTMX and Alpine.js Integration

The migration fully embraces HTMX for all dynamic UI updates and Alpine.js for lightweight client-side state management.

*   **Tab Switching**: HTMX `hx-get` is used on tab buttons to fetch the content of the respective tab (`_qa_po_list_partial.html` or `_qa_graph_partial.html`) and swap it into the `#tabContent` div. This ensures that content is loaded only when needed.
*   **DataTables Initialization**: The JavaScript for DataTables is placed directly within `_qa_po_list_partial.html`. Since HTMX swaps in new content, the `$(document).ready()` function ensures that DataTables initializes correctly each time the partial is loaded.
*   **Graph Data Loading**:
    *   The `_qa_graph_partial.html` template uses Alpine.js (`x-data="qaGraph"`) to manage the chart instance and supplier filter state (`supplierText`, `supplierId`).
    *   The `loadChartData()` method in the Alpine.js component makes a `fetch` request to `{% url 'qa_graph_data' %}` to get the JSON data for the chart.
    *   The `renderChart()` method uses Chart.js to draw or update the chart.
    *   The "Search" button triggers `loadChartData()` directly via `@click`, which in turn fetches and renders the chart data.
*   **Supplier Autocomplete**:
    *   The supplier `input` field has `hx-get` pointing to `{% url 'supplier_autocomplete' %}`. It uses `hx-trigger="keyup changed delay:500ms"` to send requests as the user types, targeting `#supplier-suggestions` to display the list of suggestions.
    *   When a suggestion `<li>` is clicked, an `hx-post` request is sent to `{% url 'set_supplier_selection' %}`. This view returns `HX-Trigger` headers, which Alpine.js listens for via a custom event (`setSupplierValues`). This event updates the `supplierText` and `supplierId` Alpine.js variables, and then programmatically triggers the chart's "Search" button, leading to a graph refresh. This intricate interaction perfectly replaces the ASP.NET `AutoCompleteExtender` and `UpdatePanel` without custom JavaScript on the Django side, adhering to the HTMX-only (mostly) philosophy.
*   **No Full Page Reloads**: All interactions are designed to work without full page reloads, providing a smooth, single-page application-like experience while remaining purely server-side rendered.

## Final Notes

*   **Placeholders**: `{{ fin_year_id }}`, `{{ company_id }}`, `{{ fin_year_value }}` are assumed to be populated from the user's session or global context and passed into the templates by the views. In a real system, these would typically come from a user's authenticated session.
*   **DRY Templates**: The use of `_qa_po_list_partial.html` and `_qa_graph_partial.html` promotes DRY principles by allowing these components to be independently fetched and swapped.
*   **Fat Models**: The complex data aggregation logic for the graph is contained within the `POMaster.get_qa_graph_data` class method, keeping the views clean and concise.
*   **Crystal Reports Replacement**: The "QA Report" tab's replacement is a simple DataTables display of PO masters. For complex PDF report generation resembling Crystal Reports, further integration with a Python PDF library (e.g., ReportLab, xhtml2pdf) would be required, typically triggered by an "Export" button on the DataTables view.
*   **Test Coverage**: The provided tests aim for high coverage, including model logic, form validation, and view responses, specifically addressing HTMX interactions.
*   **Error Handling**: Basic error handling is included in Python code, but comprehensive user-facing error messages would need to be added.