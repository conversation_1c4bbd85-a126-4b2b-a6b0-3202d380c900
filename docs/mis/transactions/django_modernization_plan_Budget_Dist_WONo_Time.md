## ASP.NET to Django Conversion Script: Comprehensive Modernization Plan

This document outlines a detailed plan to modernize your existing ASP.NET application, specifically the "Work Order Budget Distribution" page, by migrating it to a robust and scalable Django 5.0+ solution. Our approach emphasizes automation, leveraging modern web technologies like HTMX and Alpine.js, and adopting a "fat model, thin view" architecture for maintainability and performance.

Our goal is to transform this legacy page into a high-performing, interactive experience without full page reloads, while ensuring strict separation of concerns and comprehensive test coverage.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is extended.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code interacts with several database entities:
1.  **Main Data Source:** The `SearchGridView1` is populated by the `Sp_WONO_NotInBom` stored procedure. While the stored procedure itself is complex, the columns displayed in the GridView give us the *output schema*. The `BindDataCust` method also mentions `SD_Cust_WorkOrder_Master`. We will assume `SD_Cust_WorkOrder_Master` is the primary table for Work Orders.
2.  **WO Category Dropdown:** `tblSD_WO_Category` (`CId`, `Symbol`, `CName`).
3.  **Customer Autocomplete:** `SD_Cust_master` (`CustomerId`, `CustomerName`).

**Inferred Schema for Django Models:**

*   **`WorkOrder` Model (Mapping to `SD_Cust_WorkOrder_Master` conceptually):**
    *   `WONo`: Primary Key, often `VARCHAR` (string).
    *   `FinYear`: `INT` (integer).
    *   `CustomerId`: `INT` (integer), foreign key to `SD_Cust_master`.
    *   `EnqId`: `VARCHAR` (string).
    *   `PONo`: `VARCHAR` (string).
    *   `TaskProjectTitle`: `VARCHAR` (string).
    *   `SysDate`: `DATETIME` (date/time).
    *   *Note*: `CustomerName` and `EmployeeName` are likely derived from joins in the stored procedure and will be handled via relationships or properties in Django.

*   **`WOCategory` Model (Mapping to `tblSD_WO_Category`):**
    *   `CId`: Primary Key, `INT` (integer).
    *   `Symbol`: `VARCHAR` (string).
    *   `CName`: `VARCHAR` (string).

*   **`Customer` Model (Mapping to `SD_Cust_master`):**
    *   `CustomerId`: Primary Key, `INT` (integer).
    *   `CustomerName`: `VARCHAR` (string).

### Step 2: Identify Backend Functionality

**Task:** Determine the operations performed by the ASP.NET code.

**Analysis:**
The primary functionality of this page is **Read** (listing and searching) and **Export** data. There are no explicit Create, Update, or Delete operations for Work Orders on this specific page; the "WO No" is a hyperlink to another page for detail/editing. However, per the guidelines, we will include the necessary Django CRUD views and templates to demonstrate a complete modernization pattern suitable for conversational AI guidance.

*   **Read (List & Search):**
    *   Displays a list of work orders in a grid (`SearchGridView1`).
    *   Allows searching by:
        *   Customer Name (with autocomplete)
        *   Enquiry No
        *   PO No
        *   WO No
    *   Filters by WO Category (`DDLTaskWOType`).
    *   Supports pagination and sorting (handled by DataTables on the client side in Django).
    *   The core search logic involves a stored procedure (`Sp_WONO_NotInBom`) and dynamic SQL string construction. This complex filtering logic will be moved to a custom manager within the `WorkOrder` model.

*   **Export:**
    *   Exports the currently filtered data to Excel. This will require a separate Django view to generate and serve the file.

*   **UI State Management:**
    *   Toggles visibility of search textboxes based on the selected search type dropdown (`DropDownList1_SelectedIndexChanged2`). This will be managed by Alpine.js.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, and plan their Django/HTMX/Alpine.js equivalents.

**Analysis & Django Equivalents:**

*   **`DropDownList1` (Search Type):**
    *   Django Form field (`ChoiceField`) or direct HTML `<select>` with HTMX `hx-trigger="change"` and Alpine.js for `x-show` on related textboxes.
*   **`txtSearchCustomer`, `TxtSearchValue` (Search Textboxes):**
    *   Django Form fields (`TextInput`) or direct HTML `<input>` with Tailwind CSS classes. Alpine.js `x-show` for conditional visibility.
    *   `TxtSearchValue` has `AutoCompleteExtender`: This will be replaced by an HTMX `hx-post` or `hx-get` to a Django endpoint returning JSON, and Alpine.js for displaying the suggestions.
*   **`DDLTaskWOType` (WO Category Filter):**
    *   Django Form field (`ModelChoiceField`) or direct HTML `<select>` with `hx-trigger="change"` to refresh the DataTables partial.
*   **`btnSearch`:**
    *   HTML `<button>` with `hx-post` (or `hx-get` with form parameters) to trigger DataTables refresh.
*   **`btnExport`:**
    *   HTML `<button>` with `hx-get` to a Django view that serves the Excel file.
*   **`Button1` (Cancel):**
    *   HTML `<button>` or `<a>` tag to navigate back or close a modal (if used in a modal context).
*   **`SearchGridView1`:**
    *   Django template rendering a `<table>` tag.
    *   DataTables library for client-side functionality (pagination, sorting, filtering).
    *   The table itself will be loaded dynamically via HTMX from a separate partial view.
*   **Modals:** While not explicitly present on this ASP.NET page, the prompt's template examples indicate a modal pattern for CRUD operations. We will integrate this using HTMX for loading forms and Alpine.js for modal display.

---

### Step 4: Generate Django Code

We will create a new Django application named `accounts` to house this functionality.

#### 4.1 Models (accounts/models.py)

We define models for Work Order, WO Category, and Customer, with `managed = False` to connect to existing tables. We also implement a custom manager for `WorkOrder` to encapsulate the complex search/filter logic from the ASP.NET `BindDataCust` method and the `Sp_WONO_NotInBom` stored procedure.

```python
# accounts/models.py
from django.db import models
from django.db.models import F, Q

class WOCategory(models.Model):
    """
    Maps to tblSD_WO_Category for Work Order Categories.
    """
    cid = models.IntegerField(db_column='CId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)
    cname = models.CharField(db_column='CName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblSD_WO_Category'
        verbose_name = 'WO Category'
        verbose_name_plural = 'WO Categories'

    def __str__(self):
        return f"{self.symbol} - {self.cname}" if self.symbol else self.cname

class Customer(models.Model):
    """
    Maps to SD_Cust_master for Customer details.
    """
    customer_id = models.IntegerField(db_column='CustomerId', primary_key=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=255, blank=True, null=True)
    # Add other customer-related fields if known from SD_Cust_master

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customer_name

class WorkOrderManager(models.Manager):
    """
    Custom manager for WorkOrder to encapsulate search and filter logic.
    This replaces the logic from BindDataCust and Sp_WONO_NotInBom.
    """
    def get_filtered_workorders(self, company_id, financial_year_id, search_type, search_value, wo_category_id):
        # Start with all work orders for the given company and financial year
        queryset = self.get_queryset().filter(
            company_id=company_id, # Assuming 'CompId' in model
            fin_year_id=financial_year_id # Assuming 'FinYear' in model
        )

        # Apply search filters based on search_type
        if search_type == '1' and search_value: # Enquiry No
            queryset = queryset.filter(enq_id__icontains=search_value)
        elif search_type == '2' and search_value: # PO No
            queryset = queryset.filter(po_no__icontains=search_value)
        elif search_type == '3' and search_value: # WO No
            queryset = queryset.filter(wo_no__icontains=search_value)
        elif search_type == '0' and search_value: # Customer Name
            # The original ASP.NET extracted customer ID from "Name [ID]" format
            # We assume a direct CustomerName search for simplicity, or
            # split `search_value` to get ID if `fun.getCode` is critical
            customer_id_from_search = None
            if '[' in search_value and ']' in search_value:
                try:
                    customer_id_from_search = int(search_value.split('[')[-1][:-1])
                except ValueError:
                    pass # Handle cases where parsing fails

            if customer_id_from_search:
                queryset = queryset.filter(customer_id=customer_id_from_search)
            else:
                # Fallback to name search if ID not found or parsed
                queryset = queryset.filter(customer__customer_name__icontains=search_value)
        
        # Apply WO Category filter
        if wo_category_id and wo_category_id != 'WO Category': # Assuming 'WO Category' is the default placeholder value
            queryset = queryset.filter(wo_category__cid=wo_category_id) # Assuming CId maps to a FK

        # Order by WONo or other relevant field
        queryset = queryset.order_by('wo_no') 
        
        return queryset

class WorkOrder(models.Model):
    """
    Maps to SD_Cust_WorkOrder_Master table.
    We're inferring column names based on the GridView and common patterns.
    """
    wo_no = models.CharField(db_column='WONo', max_length=50, primary_key=True) # DataKeyNames="WONo"
    fin_year = models.IntegerField(db_column='FinYear', null=True, blank=True)
    customer = models.ForeignKey(
        Customer,
        on_delete=models.DO_NOTHING, # Or SET_NULL, PROTECT depending on business rules
        db_column='CustomerId', # Original column name
        related_name='work_orders',
        null=True, blank=True
    )
    enq_id = models.CharField(db_column='EnqId', max_length=50, blank=True, null=True)
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    task_project_title = models.CharField(db_column='TaskProjectTitle', max_length=500, blank=True, null=True)
    sys_date = models.DateTimeField(db_column='SysDate', null=True, blank=True)
    # Assuming these fields are also in SD_Cust_WorkOrder_Master or joined via relationships
    # employee_name = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True) # If direct column
    
    # Assuming there's a category ID on the work order master table
    wo_category = models.ForeignKey(
        WOCategory,
        on_delete=models.DO_NOTHING, # Or SET_NULL, PROTECT
        db_column='CId', # Assuming this is the FK column name on WorkOrder table
        related_name='work_orders',
        null=True, blank=True
    )

    # Assuming these exist for filtering, possibly from Session
    company_id = models.IntegerField(db_column='CompId', null=True, blank=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', null=True, blank=True)


    objects = WorkOrderManager() # Use our custom manager

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master' # Example: Replace with actual table name
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return f"{self.wo_no} - {self.task_project_title}"

    @property
    def customer_name(self):
        return self.customer.customer_name if self.customer else "N/A"
    
    @property
    def formatted_sys_date(self):
        return self.sys_date.strftime("%d-%m-%Y") if self.sys_date else "N/A"
```

#### 4.2 Forms (accounts/forms.py)

A form for `WorkOrder` for hypothetical CRUD operations, and a search form to manage the inputs for filtering.

```python
# accounts/forms.py
from django import forms
from .models import WorkOrder, WOCategory, Customer

class WorkOrderForm(forms.ModelForm):
    class Meta:
        model = WorkOrder
        # Fields to include for hypothetical add/edit
        fields = ['wo_no', 'fin_year', 'customer', 'enq_id', 'po_no', 'task_project_title', 'sys_date', 'wo_category']
        widgets = {
            'wo_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'fin_year': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'customer': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'enq_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'po_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'task_project_title': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sys_date': forms.DateTimeInput(attrs={'type': 'datetime-local', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'wo_category': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }

class WorkOrderSearchForm(forms.Form):
    """
    Form for handling the search and filter inputs on the Work Order list page.
    """
    search_type_choices = [
        ('0', 'Customer Name'),
        ('1', 'Enquiry No'),
        ('2', 'PO No'),
        ('3', 'WO No'),
    ]
    search_type = forms.ChoiceField(
        choices=search_type_choices,
        required=False,
        label="Search By",
        widget=forms.Select(attrs={'class': 'box3 w-[200px] border border-gray-300 rounded-md shadow-sm p-2', 'x-model': 'searchByType', 'hx-get': "{% url 'workorder_table' %}", 'hx-trigger': 'change', 'hx-target': '#workorderTable-container', 'hx-swap': 'innerHTML'})
    )
    search_customer_text = forms.CharField(
        required=False,
        label="Search Value (Enquiry/PO/WO No)",
        widget=forms.TextInput(attrs={'class': 'box3 w-[100px] border border-gray-300 rounded-md shadow-sm p-2', 'x-show': "searchByType === '1' || searchByType === '2' || searchByType === '3'", 'hx-get': "{% url 'workorder_table' %}", 'hx-trigger': 'keyup changed delay:500ms, search', 'hx-target': '#workorderTable-container', 'hx-swap': 'innerHTML'})
    )
    search_value_text = forms.CharField(
        required=False,
        label="Search Value (Customer Name)",
        widget=forms.TextInput(attrs={'class': 'box3 w-[350px] border border-gray-300 rounded-md shadow-sm p-2', 'x-show': "searchByType === '0'", 'hx-get': "{% url 'workorder_autocomplete_customer' %}", 'hx-trigger': 'keyup changed delay:500ms, search', 'hx-target': '#customer-autocomplete-results', 'hx-swap': 'innerHTML', 'autocomplete': 'off'})
    )
    wo_category = forms.ModelChoiceField(
        queryset=WOCategory.objects.all().order_by('cname'),
        required=False,
        empty_label="WO Category",
        label="WO Category",
        widget=forms.Select(attrs={'class': 'box3 border border-gray-300 rounded-md shadow-sm p-2', 'hx-get': "{% url 'workorder_table' %}", 'hx-trigger': 'change', 'hx-target': '#workorderTable-container', 'hx-swap': 'innerHTML'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set initial value for search_type to '0' (Customer Name) as per ASP.NET DDL initial state
        self.fields['search_type'].initial = '0'
        # The auto-complete target needs a mechanism to select the value and trigger refresh
        self.fields['search_value_text'].widget.attrs['hx-post'] = '{% url "workorder_autocomplete_customer" %}'
        self.fields['search_value_text'].widget.attrs['hx-vals'] = "js:{search_value: document.getElementById('id_search_value_text').value}"
        
```

#### 4.3 Views (accounts/views.py)

These views handle listing, filtering, and the hypothetical CRUD operations. We also include a view for the autocomplete functionality.

```python
# accounts/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from django.contrib.auth.mixins import LoginRequiredMixin # Added for session data access

from .models import WorkOrder, WOCategory, Customer
from .forms import WorkOrderForm, WorkOrderSearchForm

import csv # For export functionality
from datetime import datetime

# Helper function to get session data (mocked if not using Django's auth system fully)
# In a real app, this would use request.user.profile or similar.
def get_session_data(request):
    """
    Mocks ASP.NET Session data. In a real Django app, CompId and FinYearId
    would come from the user's profile, settings, or a specific context.
    """
    comp_id = request.session.get('compid', 1) # Default to 1 if not set
    fin_year_id = request.session.get('finyear', 2023) # Default to 2023 if not set
    username = request.session.get('username', 'admin') # Default to 'admin'
    return comp_id, fin_year_id, username

class WorkOrderListView(LoginRequiredMixin, ListView):
    model = WorkOrder
    template_name = 'accounts/workorder/list.html'
    context_object_name = 'workorders' # This won't be directly used for table, but for initial page setup

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize search form with current query parameters
        form = WorkOrderSearchForm(self.request.GET)
        context['search_form'] = form
        # Pass initial search_type value to Alpine.js
        context['initial_search_type'] = self.request.GET.get('search_type', '0')
        return context

# This view renders ONLY the table content for HTMX requests
class WorkOrderTablePartialView(LoginRequiredMixin, ListView):
    model = WorkOrder
    template_name = 'accounts/workorder/_workorder_table.html'
    context_object_name = 'workorders'

    def get_queryset(self):
        comp_id, fin_year_id, _ = get_session_data(self.request)
        
        # Get filter parameters from GET request
        search_type = self.request.GET.get('search_type', '0')
        search_value_customer = self.request.GET.get('search_value_text', '').strip()
        search_value_other = self.request.GET.get('search_customer_text', '').strip()
        wo_category_id = self.request.GET.get('wo_category', '')

        # Determine which search value to use based on search_type
        search_value_to_use = search_value_customer if search_type == '0' else search_value_other

        # Use the custom manager to get filtered work orders
        queryset = WorkOrder.objects.get_filtered_workorders(
            company_id=comp_id,
            financial_year_id=fin_year_id,
            search_type=search_type,
            search_value=search_value_to_use,
            wo_category_id=wo_category_id
        )
        return queryset

class WorkOrderCreateView(LoginRequiredMixin, CreateView):
    model = WorkOrder
    form_class = WorkOrderForm
    template_name = 'accounts/workorder/_workorder_form.html'
    success_url = reverse_lazy('workorder_list') # Not strictly needed for HTMX, but good practice

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['is_new'] = True
        return context

    def form_valid(self, form):
        # Set session-dependent fields before saving
        comp_id, fin_year_id, _ = get_session_data(self.request)
        form.instance.company_id = comp_id
        form.instance.fin_year_id = fin_year_id

        response = super().form_valid(form)
        messages.success(self.request, 'Work Order added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, indicates success to HTMX without page reload
                headers={
                    'HX-Trigger': 'refreshWorkOrderList' # Custom event to trigger list refresh
                }
            )
        return response
    
    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # If it's an HTMX request, render the form again with errors
            return HttpResponse(render_to_string(self.template_name, {'form': form}, self.request))
        return response

class WorkOrderUpdateView(LoginRequiredMixin, UpdateView):
    model = WorkOrder
    form_class = WorkOrderForm
    template_name = 'accounts/workorder/_workorder_form.html'
    success_url = reverse_lazy('workorder_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Work Order updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWorkOrderList'
                }
            )
        return response
    
    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return HttpResponse(render_to_string(self.template_name, {'form': form}, self.request))
        return response

class WorkOrderDeleteView(LoginRequiredMixin, DeleteView):
    model = WorkOrder
    template_name = 'accounts/workorder/_workorder_confirm_delete.html'
    success_url = reverse_lazy('workorder_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Work Order deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWorkOrderList'
                }
            )
        return response

class CustomerAutocompleteView(LoginRequiredMixin, View):
    """
    Provides JSON data for customer name autocomplete.
    This replaces the ASP.NET WebMethod 'sql'.
    """
    def get(self, request, *args, **kwargs):
        comp_id, _, _ = get_session_data(request)
        prefix_text = request.GET.get('q', '').strip()
        
        customers = Customer.objects.filter(
            customer_name__icontains=prefix_text,
            # Assuming Company ID is also on Customer master for filtering
            # company_id=comp_id 
        ).values('customer_id', 'customer_name')[:10] # Limit to 10 results, similar to ASP.NET

        results = [
            f"{customer['customer_name']} [{customer['customer_id']}]"
            for customer in customers
        ]
        return JsonResponse(results, safe=False)
    
    def post(self, request, *args, **kwargs):
        # This POST method is for when a user selects an item from the autocomplete
        # and we need to trigger a list refresh.
        # HTMX will post the selected value.
        return HttpResponse(
            status=204,
            headers={
                'HX-Trigger': 'refreshWorkOrderList'
            }
        )

class WorkOrderExportView(LoginRequiredMixin, View):
    """
    Handles exporting filtered work order data to a CSV/Excel file.
    This replaces btnExport_Click functionality.
    """
    def get(self, request, *args, **kwargs):
        comp_id, fin_year_id, _ = get_session_data(request)

        search_type = request.GET.get('search_type', '0')
        search_value_customer = request.GET.get('search_value_text', '').strip()
        search_value_other = request.GET.get('search_customer_text', '').strip()
        wo_category_id = request.GET.get('wo_category', '')

        search_value_to_use = search_value_customer if search_type == '0' else search_value_other

        workorders = WorkOrder.objects.get_filtered_workorders(
            company_id=comp_id,
            financial_year_id=fin_year_id,
            search_type=search_type,
            search_value=search_value_to_use,
            wo_category_id=wo_category_id
        )

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="All_WO_Budget_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv"'

        writer = csv.writer(response)
        writer.writerow([
            'SN', 'Fin Yrs', 'Customer Name', 'Code', 'Enquiry No', 
            'PO No', 'WO No', 'Project Title', 'Gen. Date', 'Gen. By'
        ]) # Header row, matching GridView

        for idx, wo in enumerate(workorders):
            writer.writerow([
                idx + 1,
                wo.fin_year,
                wo.customer_name, # Access via property or related object
                wo.customer.customer_id if wo.customer else '',
                wo.enq_id,
                wo.po_no,
                wo.wo_no,
                wo.task_project_title,
                wo.sys_date.strftime('%Y-%m-%d') if wo.sys_date else '',
                'N/A' # EmployeeName was hidden and likely joined, mock for now
            ])
        return response
```

#### 4.4 Templates (accounts/templates/accounts/workorder/)

We'll define the main list template, the table partial, and the modal form/delete partials.

**accounts/templates/accounts/workorder/list.html**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ searchByType: '{{ initial_search_type }}', showCustomerAutocomplete: false, customerSearchResults: [] }">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <h2 class="text-2xl font-bold text-gray-800">Work Order Budget Distribution</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md shadow-md transition duration-200 ease-in-out"
            hx-get="{% url 'workorder_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Work Order
        </button>
    </div>

    <!-- Search and Filter Section -->
    <div class="bg-white p-6 rounded-lg shadow-lg mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Search & Filter</h3>
        <form id="workorder-search-form" hx-get="{% url 'workorder_table' %}" hx-target="#workorderTable-container" hx-swap="innerHTML" hx-indicator="#loading-spinner">
            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4 items-end">
                <div>
                    <label for="id_search_type" class="block text-sm font-medium text-gray-700 mb-1">Search By</label>
                    <select id="id_search_type" name="search_type" 
                            x-model="searchByType"
                            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            hx-trigger="change"
                            hx-get="{% url 'workorder_table' %}"
                            hx-target="#workorderTable-container"
                            hx-swap="innerHTML">
                        {% for value, label in search_form.search_type.field.choices %}
                            <option value="{{ value }}" {% if value == initial_search_type %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- Input for Enquiry No, PO No, WO No -->
                <div x-show="searchByType === '1' || searchByType === '2' || searchByType === '3'">
                    <label for="id_search_customer_text" class="block text-sm font-medium text-gray-700 mb-1">Search Value</label>
                    <input type="text" id="id_search_customer_text" name="search_customer_text" placeholder="Enter value"
                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                           hx-trigger="keyup changed delay:500ms, search"
                           hx-get="{% url 'workorder_table' %}"
                           hx-target="#workorderTable-container"
                           hx-swap="innerHTML">
                </div>

                <!-- Input for Customer Name with Autocomplete -->
                <div x-show="searchByType === '0'" class="relative">
                    <label for="id_search_value_text" class="block text-sm font-medium text-gray-700 mb-1">Customer Name</label>
                    <input type="text" id="id_search_value_text" name="search_value_text" placeholder="Search customer"
                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                           hx-get="{% url 'workorder_autocomplete_customer' %}"
                           hx-trigger="keyup changed delay:300ms"
                           hx-target="#customer-autocomplete-results"
                           hx-swap="innerHTML"
                           @focus="showCustomerAutocomplete = true"
                           @blur.debounce.500ms="showCustomerAutocomplete = false"
                           autocomplete="off">
                    
                    <div id="customer-autocomplete-results"
                         x-show="showCustomerAutocomplete && customerSearchResults.length > 0"
                         class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-60 overflow-y-auto"
                         @mousedown.prevent>
                        <!-- Autocomplete results loaded here by HTMX -->
                    </div>
                </div>

                <div>
                    <label for="id_wo_category" class="block text-sm font-medium text-gray-700 mb-1">WO Category</label>
                    <select id="id_wo_category" name="wo_category" 
                            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            hx-trigger="change"
                            hx-get="{% url 'workorder_table' %}"
                            hx-target="#workorderTable-container"
                            hx-swap="innerHTML">
                        <option value="">WO Category</option>
                        {% for category in search_form.wo_category.field.queryset %}
                            <option value="{{ category.cid }}">{{ category.cname }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="col-span-1 md:col-span-1 lg:col-span-1 flex space-x-2">
                    <button type="submit" 
                            class="bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-md shadow-md transition duration-200 ease-in-out"
                            hx-target="#workorderTable-container" hx-swap="innerHTML">
                        Search
                    </button>
                    <a href="{% url 'workorder_export' %}" 
                       class="bg-purple-500 hover:bg-purple-600 text-white font-semibold py-2 px-4 rounded-md shadow-md transition duration-200 ease-in-out flex items-center justify-center">
                        Export
                    </a>
                    <button type="button" onclick="window.location.reload();" 
                            class="bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded-md shadow-md transition duration-200 ease-in-out">
                        Cancel
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- DataTables Container -->
    <div id="workorderTable-container"
         hx-trigger="load, refreshWorkOrderList from:body"
         hx-get="{% url 'workorder_table' %}"
         hx-swap="innerHTML">
        <!-- Initial Loading Spinner -->
        <div class="text-center py-10" id="loading-spinner">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Work Orders...</p>
        </div>
    </div>
    
    <!-- Modal for forms (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-3xl w-full mx-4 my-8 overflow-y-auto max-h-[90vh]"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- DataTables CDN -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
<script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>

<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('workorderPage', () => ({
            searchByType: '{{ initial_search_type }}',
            showCustomerAutocomplete: false,
            customerSearchResults: [],
            init() {
                // Initial setup for searchByType based on the rendered value
                const searchTypeSelect = document.getElementById('id_search_type');
                if (searchTypeSelect) {
                    this.searchByType = searchTypeSelect.value;
                }

                // Handle customer autocomplete selection
                document.getElementById('customer-autocomplete-results').addEventListener('click', (e) => {
                    if (e.target.tagName === 'LI') {
                        const selectedValue = e.target.textContent;
                        document.getElementById('id_search_value_text').value = selectedValue;
                        this.showCustomerAutocomplete = false;
                        // Manually trigger HTMX request to refresh table
                        htmx.trigger(document.getElementById('id_search_value_text'), 'change');
                    }
                });
            }
        }));
    });

    // Handle global HTMX events for toasts and modal closure
    document.body.addEventListener('htmx:afterSwap', function(event) {
        // If a form is submitted successfully via HX-POST, close the modal
        if (event.detail.target.id === 'modalContent' && event.detail.xhr.status === 204) {
            document.getElementById('modal').classList.remove('is-active');
        }
    });
</script>
{% endblock %}
```

**accounts/templates/accounts/workorder/_workorder_table.html**

```html
<div class="overflow-x-auto bg-white p-6 rounded-lg shadow-lg">
    <table id="workorderTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project Title</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in workorders %}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ obj.fin_year }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-left">{{ obj.customer_name }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ obj.customer.customer_id|default_if_none:"" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-left">{{ obj.po_no }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-blue-600 hover:underline text-left">
                    <a href="{% url 'workorder_edit' obj.pk %}">{{ obj.wo_no }}</a> {# Original was a hyperlink, adapting to Django edit #}
                </td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-left">{{ obj.task_project_title }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ obj.formatted_sys_date }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 transition duration-200 ease-in-out"
                        hx-get="{% url 'workorder_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md transition duration-200 ease-in-out"
                        hx-get="{% url 'workorder_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="9" class="py-4 px-4 text-center text-sm text-gray-500">No Work Orders found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#workorderTable')) {
            $('#workorderTable').DataTable().destroy(); // Destroy existing DataTable before reinitializing
        }
        $('#workorderTable').DataTable({
            "pageLength": 17, // As per ASP.NET GridView
            "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 8] }, // Disable sorting for SN and Actions
                { "searchable": false, "targets": [0, 8] } // Disable searching for SN and Actions
            ]
        });
    });
</script>
```

**accounts/templates/accounts/workorder/_workorder_form.html**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Work Order</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-6">
        {% csrf_token %}
        
        {% for field in form %}
        <div class="mb-4">
            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                {{ field.label }}
            </label>
            {{ field }}
            {% if field.errors %}
            <p class="text-red-600 text-xs mt-1">{{ field.errors|join:", " }}</p>
            {% endif %}
        </div>
        {% endfor %}
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-5 rounded-md transition duration-200 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-5 rounded-md shadow-md transition duration-200 ease-in-out">
                Save Work Order
            </button>
        </div>
    </form>
</div>
```

**accounts/templates/accounts/workorder/_workorder_confirm_delete.html**

```html
<div class="p-6 text-center">
    <h3 class="text-2xl font-semibold text-red-700 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete Work Order <span class="font-bold">"{{ object.wo_no }}"</span>?</p>
    <p class="text-sm text-gray-500 mb-8">This action cannot be undone.</p>

    <form hx-post="{% url 'workorder_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-center space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-5 rounded-md transition duration-200 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-5 rounded-md shadow-md transition duration-200 ease-in-out">
                Delete Work Order
            </button>
        </div>
    </form>
</div>
```

**accounts/templates/accounts/workorder/_customer_autocomplete_results.html**

```html
{% if customers %}
    <ul class="list-none p-0 m-0">
        {% for customer_text in customers %}
            <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer text-gray-800 border-b border-gray-200 last:border-b-0"
                @click="document.getElementById('id_search_value_text').value = '{{ customer_text|escapejs }}'; $dispatch('autocomplete-select'); showCustomerAutocomplete = false;"
                hx-post="{% url 'workorder_autocomplete_customer' %}" 
                hx-vals="js:{search_value: '{{ customer_text|escapejs }}'}" 
                hx-swap="none"
                hx-trigger="click">
                {{ customer_text }}
            </li>
        {% endfor %}
    </ul>
{% else %}
    <div class="px-4 py-2 text-gray-500">No matching customers.</div>
{% endif %}
```

#### 4.5 URLs (accounts/urls.py)

```python
# accounts/urls.py
from django.urls import path
from .views import (
    WorkOrderListView, WorkOrderTablePartialView,
    WorkOrderCreateView, WorkOrderUpdateView, WorkOrderDeleteView,
    CustomerAutocompleteView, WorkOrderExportView
)

urlpatterns = [
    path('workorder/', WorkOrderListView.as_view(), name='workorder_list'),
    path('workorder/table/', WorkOrderTablePartialView.as_view(), name='workorder_table'),
    path('workorder/add/', WorkOrderCreateView.as_view(), name='workorder_add'),
    path('workorder/edit/<str:pk>/', WorkOrderUpdateView.as_view(), name='workorder_edit'), # WO No is string PK
    path('workorder/delete/<str:pk>/', WorkOrderDeleteView.as_view(), name='workorder_delete'), # WO No is string PK
    path('workorder/autocomplete/customer/', CustomerAutocompleteView.as_view(), name='workorder_autocomplete_customer'),
    path('workorder/export/', WorkOrderExportView.as_view(), name='workorder_export'),
]
```

#### 4.6 Tests (accounts/tests.py)

Comprehensive tests for models and views, including HTMX interactions.

```python
# accounts/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from unittest.mock import patch # For mocking session data

from .models import WorkOrder, WOCategory, Customer

class AccountsModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all models
        cls.category = WOCategory.objects.create(cid=101, symbol='WOC', cname='General Work Orders')
        cls.customer = Customer.objects.create(customer_id=201, customer_name='Test Customer A')
        cls.customer2 = Customer.objects.create(customer_id=202, customer_name='Another Customer B')
        
        # Create test WorkOrders
        cls.wo1 = WorkOrder.objects.create(
            wo_no='WO-001',
            fin_year=2023,
            customer=cls.customer,
            enq_id='ENQ-001',
            po_no='PO-A-001',
            task_project_title='Project Alpha',
            sys_date='2023-01-15T10:00:00Z',
            wo_category=cls.category,
            company_id=1,
            fin_year_id=2023
        )
        cls.wo2 = WorkOrder.objects.create(
            wo_no='WO-002',
            fin_year=2023,
            customer=cls.customer2,
            enq_id='ENQ-002',
            po_no='PO-B-002',
            task_project_title='Project Beta',
            sys_date='2023-02-20T11:30:00Z',
            wo_category=cls.category,
            company_id=1,
            fin_year_id=2023
        )
        cls.wo3 = WorkOrder.objects.create(
            wo_no='WO-003',
            fin_year=2022, # Different financial year
            customer=cls.customer,
            enq_id='ENQ-003',
            po_no='PO-C-003',
            task_project_title='Project Gamma',
            sys_date='2022-12-01T09:00:00Z',
            wo_category=cls.category,
            company_id=2, # Different company
            fin_year_id=2022
        )

    def test_wo_category_creation(self):
        self.assertEqual(self.category.cname, 'General Work Orders')
        self.assertEqual(str(self.category), 'WOC - General Work Orders')

    def test_customer_creation(self):
        self.assertEqual(self.customer.customer_name, 'Test Customer A')
        self.assertEqual(str(self.customer), 'Test Customer A')

    def test_workorder_creation(self):
        self.assertEqual(self.wo1.wo_no, 'WO-001')
        self.assertEqual(self.wo1.customer.customer_name, 'Test Customer A')
        self.assertEqual(self.wo1.customer_name, 'Test Customer A') # Test property
        self.assertEqual(str(self.wo1), 'WO-001 - Project Alpha')

    def test_workorder_manager_filtering(self):
        # Mock session data for the manager calls
        with patch('accounts.models.get_session_data', return_value=(1, 2023, 'testuser')):
            # No filters
            workorders = WorkOrder.objects.get_filtered_workorders(1, 2023, '', '', '')
            self.assertEqual(workorders.count(), 2)
            self.assertIn(self.wo1, workorders)
            self.assertIn(self.wo2, workorders)

            # Filter by WO No
            workorders = WorkOrder.objects.get_filtered_workorders(1, 2023, '3', 'WO-001', '')
            self.assertEqual(workorders.count(), 1)
            self.assertEqual(workorders.first(), self.wo1)

            # Filter by Customer Name (by ID extracted from string)
            workorders = WorkOrder.objects.get_filtered_workorders(1, 2023, '0', 'Test Customer A [201]', '')
            self.assertEqual(workorders.count(), 1)
            self.assertEqual(workorders.first(), self.wo1)

            # Filter by Customer Name (by text contains)
            workorders = WorkOrder.objects.get_filtered_workorders(1, 2023, '0', 'Customer A', '')
            self.assertEqual(workorders.count(), 1)
            self.assertEqual(workorders.first(), self.wo1)

            # Filter by WO Category
            workorders = WorkOrder.objects.get_filtered_workorders(1, 2023, '', '', str(self.category.cid))
            self.assertEqual(workorders.count(), 2) # Both wo1 and wo2 are in this category

            # Filter by different company/year (should yield no results if comp_id=1, fin_year_id=2023)
            workorders = WorkOrder.objects.get_filtered_workorders(1, 2023, '3', 'WO-003', '')
            self.assertEqual(workorders.count(), 0)

            # Filter for WO-003 (different company/year)
            workorders = WorkOrder.objects.get_filtered_workorders(2, 2022, '3', 'WO-003', '')
            self.assertEqual(workorders.count(), 1)
            self.assertEqual(workorders.first(), self.wo3)


class AccountsViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data (same as model tests for consistency)
        cls.category = WOCategory.objects.create(cid=101, symbol='WOC', cname='General Work Orders')
        cls.customer = Customer.objects.create(customer_id=201, customer_name='Test Customer A')
        cls.customer2 = Customer.objects.create(customer_id=202, customer_name='Another Customer B')
        
        cls.wo1 = WorkOrder.objects.create(
            wo_no='WO-001',
            fin_year=2023,
            customer=cls.customer,
            enq_id='ENQ-001',
            po_no='PO-A-001',
            task_project_title='Project Alpha',
            sys_date='2023-01-15T10:00:00Z',
            wo_category=cls.category,
            company_id=1,
            fin_year_id=2023
        )
        cls.wo2 = WorkOrder.objects.create(
            wo_no='WO-002',
            fin_year=2023,
            customer=cls.customer2,
            enq_id='ENQ-002',
            po_no='PO-B-002',
            task_project_title='Project Beta',
            sys_date='2023-02-20T11:30:00Z',
            wo_category=cls.category,
            company_id=1,
            fin_year_id=2023
        )
        
        # Mocking user login for LoginRequiredMixin
        from django.contrib.auth.models import User
        cls.user = User.objects.create_user(username='testuser', password='password123')

    def setUp(self):
        self.client = Client()
        self.client.login(username='testuser', password='password123')
        # Set mock session data for CompId and FinYearId
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2023
        session.save()

    # --- WorkOrderListView Tests ---
    def test_list_view_get(self):
        response = self.client.get(reverse('workorder_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/workorder/list.html')
        self.assertIn('search_form', response.context)
        self.assertIn('initial_search_type', response.context)

    # --- WorkOrderTablePartialView Tests (HTMX driven) ---
    def test_table_partial_view_get_initial(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('workorder_table'), headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/workorder/_workorder_table.html')
        self.assertIn('workorders', response.context)
        self.assertEqual(response.context['workorders'].count(), 2) # Should get both
        self.assertContains(response, 'WO-001')
        self.assertContains(response, 'WO-002')

    def test_table_partial_view_search_wo_no(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('workorder_table'), {'search_type': '3', 'search_customer_text': 'WO-001'}, headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO-001')
        self.assertNotContains(response, 'WO-002')
        self.assertEqual(response.context['workorders'].count(), 1)

    def test_table_partial_view_search_customer_name(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('workorder_table'), {'search_type': '0', 'search_value_text': 'Test Customer A [201]'}, headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO-001')
        self.assertNotContains(response, 'WO-002')
        self.assertEqual(response.context['workorders'].count(), 1)

    def test_table_partial_view_filter_category(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('workorder_table'), {'wo_category': str(self.category.cid)}, headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['workorders'].count(), 2)

    # --- WorkOrderCreateView Tests ---
    def test_create_view_get(self):
        response = self.client.get(reverse('workorder_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/workorder/_workorder_form.html')
        self.assertIn('form', response.context)

    def test_create_view_post_htmx(self):
        data = {
            'wo_no': 'WO-NEW',
            'fin_year': 2023,
            'customer': self.customer.customer_id,
            'enq_id': 'ENQ-NEW',
            'po_no': 'PO-NEW',
            'task_project_title': 'New Project',
            'sys_date': '2023-03-01T12:00:00',
            'wo_category': self.category.cid,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('workorder_add'), data, headers=headers)
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertTrue(WorkOrder.objects.filter(wo_no='WO-NEW').exists())
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWorkOrderList')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Work Order added successfully.')

    def test_create_view_post_invalid_htmx(self):
        data = {
            'wo_no': '', # Invalid data
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('workorder_add'), data, headers=headers)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'accounts/workorder/_workorder_form.html')
        self.assertIn('form', response.context)
        self.assertFalse(WorkOrder.objects.filter(wo_no='').exists()) # Should not be created

    # --- WorkOrderUpdateView Tests ---
    def test_update_view_get(self):
        response = self.client.get(reverse('workorder_edit', args=[self.wo1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/workorder/_workorder_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.wo1)

    def test_update_view_post_htmx(self):
        data = {
            'wo_no': self.wo1.wo_no,
            'fin_year': 2023,
            'customer': self.customer2.customer_id, # Change customer
            'enq_id': 'ENQ-001-UPDATED',
            'po_no': 'PO-A-001',
            'task_project_title': 'Project Alpha Updated',
            'sys_date': '2023-01-15T10:00:00',
            'wo_category': self.category.cid,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('workorder_edit', args=[self.wo1.pk]), data, headers=headers)
        self.assertEqual(response.status_code, 204)
        self.wo1.refresh_from_db()
        self.assertEqual(self.wo1.customer, self.customer2)
        self.assertEqual(self.wo1.task_project_title, 'Project Alpha Updated')
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWorkOrderList')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Work Order updated successfully.')

    # --- WorkOrderDeleteView Tests ---
    def test_delete_view_get(self):
        response = self.client.get(reverse('workorder_delete', args=[self.wo1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/workorder/_workorder_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.wo1)

    def test_delete_view_post_htmx(self):
        pk_to_delete = self.wo1.pk
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('workorder_delete', args=[pk_to_delete]), headers=headers)
        self.assertEqual(response.status_code, 204)
        self.assertFalse(WorkOrder.objects.filter(pk=pk_to_delete).exists())
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWorkOrderList')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Work Order deleted successfully.')

    # --- CustomerAutocompleteView Tests ---
    def test_autocomplete_get(self):
        response = self.client.get(reverse('workorder_autocomplete_customer'), {'q': 'Test'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), ['Test Customer A [201]'])

    def test_autocomplete_post_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('workorder_autocomplete_customer'), {'search_value': 'Test Customer A [201]'}, headers=headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWorkOrderList')

    # --- WorkOrderExportView Tests ---
    def test_export_view(self):
        response = self.client.get(reverse('workorder_export'), {'search_type': '3', 'search_customer_text': 'WO-001'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')
        self.assertTrue('attachment; filename="All_WO_Budget_' in response['Content-Disposition'])
        
        content = response.content.decode('utf-8')
        lines = content.strip().split('\r\n')
        self.assertEqual(len(lines), 2) # Header + 1 data row
        self.assertIn('WO-001', lines[1])
        self.assertIn('Project Alpha', lines[1])
```

### Step 5: HTMX and Alpine.js Integration

All dynamic interactions are designed around HTMX for server-side updates and Alpine.js for client-side state management.

*   **Search and Filter:**
    *   Dropdowns (`search_type`, `wo_category`) have `hx-trigger="change"` to re-fetch the `_workorder_table.html` partial.
    *   Search input fields (`search_customer_text`, `search_value_text`) use `hx-trigger="keyup changed delay:500ms, search"` for a debounced live search experience.
    *   The entire search form is configured with `hx-get` to the `workorder_table` endpoint, ensuring that all form parameters are sent.
*   **Autocomplete:**
    *   The `TxtSearchValue` input has `hx-get="{% url 'workorder_autocomplete_customer' %}"` to fetch suggestions from the Django `CustomerAutocompleteView`.
    *   `hx-target="#customer-autocomplete-results"` and `hx-swap="innerHTML"` render the suggestions.
    *   Alpine.js (`x-show`) controls the visibility of the autocomplete results dropdown.
    *   Clicking an autocomplete suggestion updates the input field and triggers a `hx-post` (or `hx-trigger` 'change' on the input) back to the server to refresh the main work order list, ensuring selected value is reflected in the search.
*   **CRUD Operations (Add/Edit/Delete):**
    *   Buttons like "Add New Work Order", "Edit", and "Delete" use `hx-get` to fetch the respective form partials (`_workorder_form.html` or `_workorder_confirm_delete.html`) into the `#modalContent` div.
    *   Alpine.js (`x-data`, `on click add .is-active to #modal`) handles showing/hiding the modal.
    *   Form submissions within the modal use `hx-post` to the same URL (`request.path`), with `hx-swap="none"`.
    *   Successful form submissions (Django view returns `HttpResponse(status=204, headers={'HX-Trigger': 'refreshWorkOrderList'})`) trigger a custom `refreshWorkOrderList` event.
    *   The `workorderTable-container` is listening for `load, refreshWorkOrderList from:body` to re-fetch and update the table automatically.
    *   Error cases (invalid form submission) cause the form partial to be re-rendered *inside the modal* with validation messages.
*   **DataTables:**
    *   The `_workorder_table.html` partial includes the necessary JavaScript to initialize DataTables on the rendered table. This ensures DataTables features like pagination, sorting, and client-side search are active whenever the table content is refreshed via HTMX. The `$.fn.DataTable.isDataTable('#workorderTable').destroy()` ensures proper reinitialization on HTMX swaps.

### Final Notes

This modernization plan provides a comprehensive, automated approach to migrating your ASP.NET "Work Order Budget Distribution" page to Django. By following these structured steps, you can:

*   **Improve Performance & User Experience:** Implement dynamic interactions with HTMX and Alpine.js, eliminating full page reloads and providing a snappier interface.
*   **Enhance Maintainability:** Adopt Django's "fat model, thin view" pattern, separating business logic from presentation, making the codebase easier to understand and manage.
*   **Boost Scalability:** Transition to a modern, open-source framework, benefiting from Django's robust ecosystem and community support.
*   **Ensure Quality:** Integrate comprehensive unit and integration tests, aiming for high code coverage to prevent regressions and ensure functional correctness.
*   **Streamline Development:** The modular and component-based design (using partial templates, HTMX) facilitates efficient development and future enhancements, minimizing manual coding effort.

This plan is designed for execution through AI-assisted automation, providing clear, actionable steps that translate directly into Django application files and configurations.