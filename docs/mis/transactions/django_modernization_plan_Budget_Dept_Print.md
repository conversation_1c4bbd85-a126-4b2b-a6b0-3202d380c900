## ASP.NET to Django Conversion Script: Budget Department Report

This modernization plan outlines the transition of your ASP.NET Crystal Report viewer for "Budget by Department" to a modern Django application. The focus is on providing a dynamic, interactive web page that displays the same report data, leveraging Django's robust backend capabilities with a reactive frontend using HTMX and Alpine.js.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code to understand the data sources for the report.

**Instructions:**
The ASP.NET code's `SELECT` query provides direct insight into the required tables and columns. The report pulls data from `AccHead`, `tblACC_Budget_Dept`, and `tblHR_Departments`.

**Identified Schema:**

*   **`AccHead` Table:**
    *   `Id` (Primary Key, integer)
    *   `Description` (Text, e.g., 'Salaries')
    *   `Symbol` (Text, e.g., 'SAL')
    *   `Abbrivation` (Text, e.g., 'SLR')
    *   `Category` (Text, e.g., 'Labour', used for filtering)
*   **`tblHR_Departments` Table:**
    *   `Id` (Primary Key, integer)
    *   `Description` (Text, e.g., 'HR Department', aliased as `DeptName`)
*   **`tblACC_Budget_Dept` Table:**
    *   `AccId` (Foreign Key to `AccHead.Id`, integer)
    *   `DeptId` (Foreign Key to `tblHR_Departments.Id`, integer, used for filtering)
    *   `Amount` (Decimal number representing the budget amount)

### Step 2: Identify Backend Functionality

**Task:** Determine the purpose of the ASP.NET page and map it to core application functionalities.

**Instructions:**
The ASP.NET page `Budget_Dept_Print.aspx` uses a `CrystalReportViewer` and a `SqlDataAdapter` to fetch data based on a `DeptId` from the query string and display it. This is a **Read (Reporting)** operation. There are no indications of creating, updating, or deleting data.

**Identified Functionality:**
*   **Read:** Displays a filtered list of budget entries (`tblACC_Budget_Dept`) joined with account head details (`AccHead`) and department names (`tblHR_Departments`). The data is filtered by a `DeptId` provided in the URL and by `AccHead.Category = 'Labour'`.

### Step 3: Infer UI Components

**Task:** Analyze how the ASP.NET page presents information and translate it into modern web UI components.

**Instructions:**
The `CrystalReportViewer` is a display component for structured data, typically tabular. The presence of `loadingNotifier.js` suggests a loading indicator during data retrieval.

**Inferred UI Components for Django:**
*   **Main Page:** A simple HTML page that will serve as the container for the report.
*   **Data Display:** A dynamic table (DataTables) to present the budget information for the selected department.
*   **Loading Indicator:** HTMX's built-in loading states or a simple spinner will replace the custom JavaScript for showing data loading.
*   **Department Selection:** The `DeptId` will be passed as a URL parameter, similar to the ASP.NET query string, making the report specific to a department.

### Step 4: Generate Django Code

The Django application will be named `accounts` to logically group financial and budgeting functionalities.

#### 4.1 Models (`accounts/models.py`)

**Task:** Create Django models that map to the identified database tables (`AccHead`, `tblHR_Departments`, `tblACC_Budget_Dept`) and include business logic for retrieving report data.

**Instructions:**
*   Define three models: `AccHead`, `Department`, and `BudgetDeptTransaction`.
*   Set `managed = False` and `db_table` in their `Meta` classes to map them to your existing database tables.
*   Define fields with appropriate Django types, ensuring correct `db_column` mapping.
*   Implement a class method `get_budget_report_data` on `BudgetDeptTransaction` to encapsulate the report's data retrieval logic (Fat Model approach).

```python
# accounts/models.py
from django.db import models

class AccHead(models.Model):
    """
    Maps to the existing 'AccHead' database table.
    Contains details about different account categories.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    abbrivation = models.CharField(db_column='Abbrivation', max_length=50)
    category = models.CharField(db_column='Category', max_length=50)

    class Meta:
        managed = False  # Django will not manage this table's schema
        db_table = 'AccHead'
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'

    def __str__(self):
        return f"{self.description} ({self.symbol})"

class Department(models.Model):
    """
    Maps to the existing 'tblHR_Departments' database table.
    Contains details about various organizational departments.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255)

    class Meta:
        managed = False  # Django will not manage this table's schema
        db_table = 'tblHR_Departments'
        verbose_name = 'Department'
        verbose_name_plural = 'Departments'

    def __str__(self):
        return self.description

class BudgetDeptTransaction(models.Model):
    """
    Maps to the existing 'tblACC_Budget_Dept' database table.
    Represents budget allocations for specific accounts within departments.
    """
    # Assuming the table has an implicit primary key that Django will manage
    # Or an existing 'Id' column which should be defined as primary_key=True
    # If the original table does not have an explicit PK, Django will create one.
    
    # Foreign keys to AccHead and Department models.
    # models.DO_NOTHING means no action on related object deletion, assuming
    # referential integrity is handled at the database level.
    acc_head = models.ForeignKey(AccHead, models.DO_NOTHING, db_column='AccId')
    department = models.ForeignKey(Department, models.DO_NOTHING, db_column='DeptId')
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=2) 
    # Adjust max_digits and decimal_places based on actual database schema

    class Meta:
        managed = False  # Django will not manage this table's schema
        db_table = 'tblACC_Budget_Dept'
        verbose_name = 'Budget Department Transaction'
        verbose_name_plural = 'Budget Department Transactions'
        # If (AccId, DeptId) is unique, consider adding: unique_together = (('acc_head', 'department'),)

    def __str__(self):
        return f"Budget for {self.department.description} on {self.acc_head.description}"

    @classmethod
    def get_budget_report_data(cls, department_id):
        """
        Retrieves the specific budget report data for a given department.
        This method encapsulates the business logic of the original SQL query:
        - Filters by department ID.
        - Filters by 'Labour' category for Account Head.
        - Selects related AccHead and Department data for display.
        """
        return cls.objects.filter(
            department__id=department_id,
            acc_head__category='Labour'
        ).select_related('acc_head', 'department').order_by('acc_head__description')

```

#### 4.2 Forms (`accounts/forms.py`)

**Task:** Define a Django form for user input.

**Instructions:**
The original ASP.NET page was a report viewer and did not involve user input forms for data creation or modification. Therefore, no Django forms are required for this specific conversion. If there were a need to select a department from a dropdown, a simple `forms.Form` could be created for that purpose, but it's not part of the core report display.

```python
# accounts/forms.py
# No forms are required for this report viewing functionality.
# This file can be empty or contain a comment.
```

#### 4.3 Views (`accounts/views.py`)

**Task:** Implement the report display using Django Class-Based Views (CBVs), keeping them thin and delegating logic to the models.

**Instructions:**
*   Create `BudgetReportView` (a `TemplateView`) to serve the main report page, handling the `dept_id` parameter.
*   Create `BudgetReportTablePartialView` (a `ListView`) to serve the dynamic table content via HTMX. This view will use the `get_budget_report_data` method from the model.
*   Ensure views remain concise (5-15 lines per method).

```python
# accounts/views.py
from django.views.generic import TemplateView, ListView
from django.shortcuts import get_object_or_404
from django.http import HttpResponse

from .models import BudgetDeptTransaction, Department, AccHead

class BudgetReportView(TemplateView):
    """
    Main view for the Budget Department Report page.
    This view serves the initial HTML container for the report.
    """
    template_name = 'accounts/budget_report/list.html'

    def get_context_data(self, **kwargs):
        """
        Adds the department object to the context for display on the page.
        """
        context = super().get_context_data(**kwargs)
        department_id = self.kwargs.get('dept_id')
        if department_id:
            context['department'] = get_object_or_404(Department, pk=department_id)
            context['department_id'] = department_id # Pass ID to template for HTMX URL
        return context

class BudgetReportTablePartialView(ListView):
    """
    HTMX-driven partial view to load the DataTables content for the budget report.
    This view fetches the specific data based on the department ID.
    """
    model = BudgetDeptTransaction
    template_name = 'accounts/budget_report/_budget_report_table.html'
    context_object_name = 'budget_entries' # Name for the queryset in the template

    def get_queryset(self):
        """
        Fetches the filtered budget data using the fat model method.
        """
        department_id = self.kwargs.get('dept_id')
        if not department_id:
            # If no department ID, return an empty queryset
            return BudgetDeptTransaction.objects.none()
        
        # Use the class method from the model to get the specific report data
        queryset = BudgetDeptTransaction.get_budget_report_data(department_id)
        return queryset

```

#### 4.4 Templates (`accounts/templates/accounts/budget_report/`)

**Task:** Create HTML templates for the main report page and the dynamic table content.

**Instructions:**
*   `list.html`: The main page template. It extends `core/base.html` and contains a container for the HTMX-loaded table.
*   `_budget_report_table.html`: A partial template containing the DataTables structure and the data rows. This is loaded via HTMX into `list.html`.
*   Ensure all necessary CDN links for DataTables, HTMX, and Alpine.js are handled in `core/base.html` (not included here).
*   Use Tailwind CSS classes for styling.

**`list.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        {% if department %}
        <h2 class="text-2xl font-bold text-gray-800">Budget Report for Department: <span class="text-blue-600">{{ department.description }}</span></h2>
        {% else %}
        <h2 class="text-2xl font-bold text-gray-800">Budget Report</h2>
        <p class="text-red-500">No department selected. Please provide a department ID in the URL.</p>
        {% endif %}
    </div>
    
    <div id="budgetReportTable-container"
         {% if department_id %}
         hx-trigger="load"
         hx-get="{% url 'budget_report_table_partial' dept_id=department_id %}"
         hx-swap="innerHTML"
         {% endif %}>
        <!-- This content will be replaced by the HTMX call -->
        <div class="text-center py-10" hx-indicator>
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-gray-600 text-lg">Loading Budget Data...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js can be used here for any client-side UI state management
        // For this simple report, it might not be strictly necessary,
        // but included for completeness as per guidelines.
        Alpine.data('reportPage', () => ({
            // Example: If you had a client-side filter
            filterText: '',
            init() {
                // console.log('Report page initialized.');
            }
        }));
    });
</script>
{% endblock %}
```

**`_budget_report_table.html`**
```html
<div class="overflow-x-auto bg-white shadow-lg rounded-lg border border-gray-200">
    <table id="budgetReportTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Account Description</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Symbol</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Abbreviation</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-600 uppercase tracking-wider">Amount</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Department Name</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for entry in budget_entries %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ entry.acc_head.description }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ entry.acc_head.symbol }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ entry.acc_head.abbrivation }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700 text-right font-semibold">{{ entry.amount }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ entry.department.description }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 px-4 text-center text-base text-gray-600">
                    <p class="mb-2">No budget entries found for this department with 'Labour' category accounts.</p>
                    <p class="text-sm text-gray-500">Please check the department ID or data filters.</p>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
// Initialize DataTables after the table element is loaded by HTMX
$(document).ready(function() {
    $('#budgetReportTable').DataTable({
        "paging": true,
        "searching": true,
        "ordering": true,
        "info": true,
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0] } // Disable ordering on SN column
        ],
        "order": [[1, 'asc']] // Default order by 'Account Description'
    });
});
</script>
```

#### 4.5 URLs (`accounts/urls.py`)

**Task:** Define URL patterns to map incoming requests to the appropriate Django views.

**Instructions:**
*   Create a main URL pattern for the report page that includes the `dept_id` as an integer parameter.
*   Define a separate URL for the HTMX-loaded table partial, also accepting `dept_id`.
*   Name the URLs for easy referencing in templates and views.

```python
# accounts/urls.py
from django.urls import path
from .views import BudgetReportView, BudgetReportTablePartialView

urlpatterns = [
    # Main URL for the budget report page (e.g., /accounts/budget-report/101/)
    path('budget-report/<int:dept_id>/', BudgetReportView.as_view(), name='budget_report_list'),
    
    # HTMX endpoint to dynamically load the report table content
    # (e.g., /accounts/budget-report/101/table/)
    path('budget-report/<int:dept_id>/table/', BudgetReportTablePartialView.as_view(), name='budget_report_table_partial'),
]

```

#### 4.6 Tests (`accounts/tests.py`)

**Task:** Write comprehensive tests for the models and views to ensure functionality and data integrity.

**Instructions:**
*   Include unit tests for each model (`AccHead`, `Department`, `BudgetDeptTransaction`) to verify creation, string representation, and specific methods (like `get_budget_report_data`).
*   Add integration tests for the views (`BudgetReportView`, `BudgetReportTablePartialView`) to check HTTP responses, template usage, and context data.
*   Ensure test coverage for edge cases, such as non-existent department IDs or no matching data.

```python
# accounts/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from decimal import Decimal

# Import all models from the current app
from .models import AccHead, Department, BudgetDeptTransaction

class AccHeadModelTest(TestCase):
    """
    Unit tests for the AccHead model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        AccHead.objects.create(
            id=1, description='Labour Salaries', symbol='LS', abbrivation='LSR', category='Labour'
        )
        AccHead.objects.create(
            id=2, description='Overhead Rent', symbol='OH', abbrivation='OVR', category='Overhead'
        )

    def test_acc_head_creation(self):
        """Verify AccHead object creation and attribute values."""
        acc_head = AccHead.objects.get(id=1)
        self.assertEqual(acc_head.description, 'Labour Salaries')
        self.assertEqual(acc_head.category, 'Labour')
        self.assertEqual(str(acc_head), 'Labour Salaries (LS)')

    def test_acc_head_verbose_name(self):
        """Verify verbose name for AccHead model."""
        self.assertEqual(AccHead._meta.verbose_name, 'Account Head')
        self.assertEqual(AccHead._meta.verbose_name_plural, 'Account Heads')

class DepartmentModelTest(TestCase):
    """
    Unit tests for the Department model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        Department.objects.create(id=101, description='Sales Department')
        Department.objects.create(id=102, description='HR Department')

    def test_department_creation(self):
        """Verify Department object creation and attribute values."""
        dept = Department.objects.get(id=101)
        self.assertEqual(dept.description, 'Sales Department')
        self.assertEqual(str(dept), 'Sales Department')

    def test_department_verbose_name(self):
        """Verify verbose name for Department model."""
        self.assertEqual(Department._meta.verbose_name, 'Department')
        self.assertEqual(Department._meta.verbose_name_plural, 'Departments')

class BudgetDeptTransactionModelTest(TestCase):
    """
    Unit tests for the BudgetDeptTransaction model and its class methods.
    """
    @classmethod
    def setUpTestData(cls):
        # Create dependent data first
        cls.acc_head_labour_1 = AccHead.objects.create(
            id=10, description='Direct Labour Costs', symbol='DLC', abbrivation='DLC', category='Labour'
        )
        cls.acc_head_labour_2 = AccHead.objects.create(
            id=11, description='Indirect Labour Costs', symbol='ILC', abbrivation='ILC', category='Labour'
        )
        cls.acc_head_overhead = AccHead.objects.create(
            id=12, description='Office Supplies', symbol='OS', abbrivation='OS', category='Overhead'
        )
        cls.dept_marketing = Department.objects.create(id=201, description='Marketing')
        cls.dept_finance = Department.objects.create(id=202, description='Finance')

        # Create budget transactions
        BudgetDeptTransaction.objects.create(
            acc_head=cls.acc_head_labour_1, department=cls.dept_marketing, amount=Decimal('10000.00')
        )
        BudgetDeptTransaction.objects.create(
            acc_head=cls.acc_head_labour_2, department=cls.dept_marketing, amount=Decimal('5000.00')
        )
        BudgetDeptTransaction.objects.create(
            acc_head=cls.acc_head_overhead, department=cls.dept_marketing, amount=Decimal('2000.00')
        ) # This should be excluded by the 'Labour' filter
        BudgetDeptTransaction.objects.create(
            acc_head=cls.acc_head_labour_1, department=cls.dept_finance, amount=Decimal('15000.00')
        )

    def test_budget_transaction_creation(self):
        """Verify BudgetDeptTransaction object creation."""
        transaction = BudgetDeptTransaction.objects.get(amount=Decimal('10000.00'))
        self.assertEqual(transaction.acc_head, self.acc_head_labour_1)
        self.assertEqual(transaction.department, self.dept_marketing)
        self.assertEqual(str(transaction), 'Budget for Marketing on Direct Labour Costs')

    def test_get_budget_report_data_method(self):
        """
        Test the get_budget_report_data class method for correct filtering
        and related object selection.
        """
        # Test for Marketing Department (ID 201)
        marketing_report = BudgetDeptTransaction.get_budget_report_data(self.dept_marketing.id)
        self.assertEqual(marketing_report.count(), 2) # Should only include 'Labour' category entries
        
        # Check if the correct accounts are included
        self.assertIn(self.acc_head_labour_1, [b.acc_head for b in marketing_report])
        self.assertIn(self.acc_head_labour_2, [b.acc_head for b in marketing_report])
        self.assertNotIn(self.acc_head_overhead, [b.acc_head for b in marketing_report])

        # Test for Finance Department (ID 202)
        finance_report = BudgetDeptTransaction.get_budget_report_data(self.dept_finance.id)
        self.assertEqual(finance_report.count(), 1)
        self.assertIn(self.acc_head_labour_1, [b.acc_head for b in finance_report])

        # Test for a department with no labour budget entries
        non_existent_dept_id = 999
        no_entries_report = BudgetDeptTransaction.get_budget_report_data(non_existent_dept_id)
        self.assertEqual(no_entries_report.count(), 0)

class BudgetReportViewsTest(TestCase):
    """
    Integration tests for the Budget Report views.
    """
    @classmethod
    def setUpTestData(cls):
        # Create minimal data for views to operate
        cls.acc_head_labour = AccHead.objects.create(
            id=1, description='Consultant Fees', symbol='CF', abbrivation='CFS', category='Labour'
        )
        cls.acc_head_non_labour = AccHead.objects.create(
            id=2, description='Software Licenses', symbol='SL', abbrivation='SFL', category='Overhead'
        )
        cls.department_alpha = Department.objects.create(id=10, description='Alpha Dept')
        cls.department_beta = Department.objects.create(id=20, description='Beta Dept')

        BudgetDeptTransaction.objects.create(
            acc_head=cls.acc_head_labour, department=cls.department_alpha, amount=Decimal('5000.00')
        )
        BudgetDeptTransaction.objects.create(
            acc_head=cls.acc_head_non_labour, department=cls.department_alpha, amount=Decimal('1000.00')
        )
        BudgetDeptTransaction.objects.create(
            acc_head=cls.acc_head_labour, department=cls.department_beta, amount=Decimal('7500.00')
        )

    def setUp(self):
        self.client = Client()

    def test_budget_report_list_view_success(self):
        """Test the main report page loads correctly for an existing department."""
        response = self.client.get(reverse('budget_report_list', args=[self.department_alpha.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/budget_report/list.html')
        self.assertContains(response, f"Budget Report for Department: {self.department_alpha.description}")
        self.assertIn('department', response.context)
        self.assertEqual(response.context['department'].id, self.department_alpha.id)

    def test_budget_report_list_view_non_existent_department(self):
        """Test the main report page handles non-existent department IDs (should 404)."""
        response = self.client.get(reverse('budget_report_list', args=[9999]))
        self.assertEqual(response.status_code, 404)

    def test_budget_report_table_partial_view_success(self):
        """
        Test the HTMX partial view loads the table correctly with filtered data.
        """
        response = self.client.get(reverse('budget_report_table_partial', args=[self.department_alpha.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/budget_report/_budget_report_table.html')
        
        # Verify correct number of entries (only 'Labour' category)
        self.assertIn('budget_entries', response.context)
        budget_entries = response.context['budget_entries']
        self.assertEqual(len(budget_entries), 1) 
        self.assertEqual(budget_entries[0].acc_head, self.acc_head_labour)
        self.assertEqual(budget_entries[0].amount, Decimal('5000.00'))
        
        # Verify table content
        self.assertContains(response, self.acc_head_labour.description)
        self.assertContains(response, str(self.acc_head_labour.symbol))
        self.assertContains(response, str(self.acc_head_labour.abbrivation))
        self.assertContains(response, str(self.department_alpha.description))
        self.assertContains(response, str(Decimal('5000.00')))
        
        # Ensure non-labour entry is not present
        self.assertNotContains(response, self.acc_head_non_labour.description)

    def test_budget_report_table_partial_view_no_matching_entries(self):
        """Test the HTMX partial view when no budget entries match the criteria."""
        # Create a department with no 'Labour' budget entries
        department_gamma = Department.objects.create(id=30, description='Gamma Dept')
        BudgetDeptTransaction.objects.create(
            acc_head=self.acc_head_non_labour, department=department_gamma, amount=Decimal('3000.00')
        )
        
        response = self.client.get(reverse('budget_report_table_partial', args=[department_gamma.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/budget_report/_budget_report_table.html')
        
        self.assertIn('budget_entries', response.context)
        self.assertEqual(len(response.context['budget_entries']), 0)
        self.assertContains(response, "No budget entries found for this department or category.")

    def test_budget_report_table_partial_view_non_existent_department(self):
        """Test the HTMX partial view with a non-existent department ID."""
        response = self.client.get(reverse('budget_report_table_partial', args=[9999]))
        self.assertEqual(response.status_code, 200) # Partial views often return 200 with empty content
        self.assertTemplateUsed(response, 'accounts/budget_report/_budget_report_table.html')
        self.assertContains(response, "No budget entries found for this department or category.")
        self.assertEqual(len(response.context['budget_entries']), 0)

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
*   **HTMX:** Used in `list.html` with `hx-get` and `hx-trigger="load"` to fetch the `_budget_report_table.html` partial. This ensures the main page loads quickly, and the data table is populated dynamically without a full page refresh. `hx-indicator` is added for a loading spinner.
*   **Alpine.js:** Included in `core/base.html` (assumed). While not extensively used for this specific report (as it's primarily a display), it's available for future client-side interactivity, like dynamic filters or toggleable UI elements.
*   **DataTables:** The `_budget_report_table.html` partial includes the JavaScript to initialize DataTables on the rendered table. This provides out-of-the-box searching, sorting, and pagination for the report data, significantly enhancing user experience compared to a static report.
*   **No Custom JavaScript:** All dynamic interactions are handled by HTMX and DataTables, adhering to the "no additional JavaScript" guideline beyond framework requirements.
*   **No full page reloads:** Achieved through HTMX.

### Final Notes

*   **Placeholders:** `[APP_NAME]` is `accounts`. `[MODEL_NAME]`, `[MODEL_NAME_LOWER]`, `[MODEL_NAME_PLURAL]` were replaced with `BudgetReport`, `budget_report`, `budget_reports`, or `budget_entries` where appropriate for the report context.
*   **DRY Templates:** The use of `_budget_report_table.html` as a partial ensures the table rendering logic is encapsulated and reusable if needed elsewhere.
*   **Fat Model, Thin View:** The `get_budget_report_data` class method in the `BudgetDeptTransaction` model centralizes the complex data retrieval and filtering logic, keeping the views clean and focused on HTTP concerns.
*   **Comprehensive Tests:** The included tests cover model functionality and view interactions, promoting code quality and maintainability.
*   **Business Value:** This modernized approach provides a significantly more interactive and responsive user experience compared to a static Crystal Report. Users can instantly search, sort, and paginate through the budget data without waiting for full page reloads or relying on specialized report viewer software. This improves data accessibility and operational efficiency. The use of standard web technologies (Django, HTMX, DataTables, Tailwind CSS) makes the application easier to maintain, scale, and integrate with other systems.