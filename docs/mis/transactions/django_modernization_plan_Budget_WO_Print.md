## ASP.NET to Django Conversion Script: Modernizing Budget Work Order Printing

This document outlines a comprehensive plan to migrate the `Budget_WO_Print.aspx` module from ASP.NET to a modern Django 5.0+ application. Since the provided ASP.NET code is primarily an empty content page with no specific business logic, database interactions, or UI components, this plan will infer typical requirements for a "Budget Work Order Print" module. The focus will be on creating a robust, maintainable, and highly interactive Django application using AI-assisted automation principles.

Our approach prioritizes a "Fat Model, Thin View" architecture, employing HTMX and Alpine.js for dynamic frontend interactions, and DataTables for efficient data presentation. This will result in a highly responsive, single-page application (SPA)-like experience without the complexity of traditional JavaScript frameworks, delivering significant business value through improved user experience and reduced development costs.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
The provided ASP.NET `.aspx` and C# code-behind files do not explicitly contain database schema information, SQL commands, or data-bound controls. Based on the file name `Budget_WO_Print.aspx`, we infer that this module likely interacts with a table related to "Budget Work Orders" to facilitate their display or printing.

**Inferred Database Schema:**
- **Table Name:** `tblBudgetWorkOrder` (inferred from `Budget_WO_Print`)
- **Columns (hypothetical, common for such a module):**
    - `ID` (Primary Key, integer)
    - `WorkOrderNumber` (string, e.g., `WO-2023-001`)
    - `BudgetDate` (date)
    - `Amount` (decimal)
    - `Status` (string, e.g., 'Draft', 'Approved', 'Printed', 'Cancelled')
    - `Description` (text)
    - `CreatedDate` (datetime, for auditing)
    - `LastModifiedDate` (datetime, for auditing)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
Given the empty `Page_Load` and lack of ASP.NET controls, no explicit CRUD operations can be identified from the provided source. However, a "print" module typically involves:
- **Read:** Displaying a list of budget work orders and the details of a specific work order for printing.
- **Update (inferred):** Potentially updating the status of a work order (e.g., from 'Approved' to 'Printed') once it has been processed.

To provide a complete and modern Django solution that can handle all common scenarios, we will implement full CRUD (Create, Read, Update, Delete) functionality for the `BudgetWorkOrder` entity, as this is a standard practice for managing such data.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
No UI components are present in the provided `.aspx` file. Based on common ASP.NET patterns for displaying lists and forms, we would typically expect:
- **For Listing:** A `GridView` control to display a list of Budget Work Orders with columns for Work Order Number, Budget Date, Amount, Status, and action buttons (Edit, Delete, Print).
- **For Forms:** `TextBox` controls for data entry (Work Order Number, Amount, Description), a `Calendar` or `DatePicker` for Budget Date, and a `DropDownList` for Status selection. `Button` controls for 'Save', 'Cancel', 'Delete'.

The Django modernization will replace these with HTML tables (enhanced by DataTables), input fields, dropdowns, and buttons, all integrated with HTMX and Alpine.js for a seamless user experience.

### Step 4: Generate Django Code

We will create a new Django application named `budget_wo`.

#### 4.1 Models

**Task:** Create a Django model based on the database schema.

**Instructions:**
The model `BudgetWorkOrder` will map to the inferred `tblBudgetWorkOrder` table. It will include the hypothetical fields identified in Step 1. We will also add a simple business logic method `is_open()` to demonstrate the "fat model" approach.

```python
# budget_wo/models.py
from django.db import models
from django.utils import timezone

class BudgetWorkOrder(models.Model):
    id = models.AutoField(db_column='ID', primary_key=True)
    work_order_number = models.CharField(db_column='WorkOrderNumber', max_length=100, unique=True, verbose_name="Work Order No.")
    budget_date = models.DateField(db_column='BudgetDate', verbose_name="Budget Date")
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=2, verbose_name="Amount")
    status = models.CharField(db_column='Status', max_length=50, default='Draft', verbose_name="Status")
    description = models.TextField(db_column='Description', null=True, blank=True, verbose_name="Description")
    created_date = models.DateTimeField(db_column='CreatedDate', default=timezone.now, verbose_name="Created Date")
    last_modified_date = models.DateTimeField(db_column='LastModifiedDate', auto_now=True, verbose_name="Last Modified Date")

    class Meta:
        managed = False  # Set to True if Django should manage this table, False if it's an existing table
        db_table = 'tblBudgetWorkOrder'
        verbose_name = 'Budget Work Order'
        verbose_name_plural = 'Budget Work Orders'
        ordering = ['-budget_date', 'work_order_number']

    def __str__(self):
        return f"{self.work_order_number} ({self.status})"

    # Business logic methods go here (Fat Model)
    def is_open(self):
        """
        Checks if the budget work order is in an 'open' status.
        """
        return self.status in ['Draft', 'Approved']

    def mark_as_printed(self):
        """
        Marks the budget work order status as 'Printed'.
        """
        if self.status in ['Approved']:
            self.status = 'Printed'
            self.save()
            return True
        return False
        
    def get_status_display(self):
        """
        Returns a user-friendly display for the status.
        """
        status_map = {
            'Draft': 'Draft (Pending Review)',
            'Approved': 'Approved (Ready to Print)',
            'Printed': 'Printed (Completed)',
            'Cancelled': 'Cancelled'
        }
        return status_map.get(self.status, self.status)
```

#### 4.2 Forms

**Task:** Define a Django form for user input.

**Instructions:**
A `ModelForm` will be created for the `BudgetWorkOrder` model. Fields will be included with appropriate widgets and Tailwind CSS classes for consistent styling. Basic validation will be demonstrated.

```python
# budget_wo/forms.py
from django import forms
from .models import BudgetWorkOrder

class BudgetWorkOrderForm(forms.ModelForm):
    class Meta:
        model = BudgetWorkOrder
        fields = ['work_order_number', 'budget_date', 'amount', 'status', 'description']
        widgets = {
            'work_order_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'budget_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01'}),
            'status': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}, choices=[
                ('Draft', 'Draft'),
                ('Approved', 'Approved'),
                ('Printed', 'Printed'),
                ('Cancelled', 'Cancelled'),
            ]),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
        }
        labels = {
            'work_order_number': 'Work Order No.',
            'budget_date': 'Budget Date',
            'amount': 'Amount',
            'status': 'Status',
            'description': 'Description',
        }
        
    # Add custom validation methods here
    def clean_amount(self):
        amount = self.cleaned_data['amount']
        if amount <= 0:
            raise forms.ValidationError("Amount must be positive.")
        return amount

    def clean_budget_date(self):
        budget_date = self.cleaned_data['budget_date']
        if budget_date > timezone.now().date():
            raise forms.ValidationError("Budget Date cannot be in the future.")
        return budget_date
```

#### 4.3 Views

**Task:** Implement CRUD operations using CBVs.

**Instructions:**
We will define `ListView`, `CreateView`, `UpdateView`, and `DeleteView`. A specific `TablePartialView` will be added to handle HTMX requests for the DataTables content, ensuring efficient partial updates. All views will adhere to the "thin view" principle, with business logic primarily delegated to the model.

```python
# budget_wo/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import render, get_object_or_404
from .models import BudgetWorkOrder
from .forms import BudgetWorkOrderForm

class BudgetWorkOrderListView(ListView):
    model = BudgetWorkOrder
    template_name = 'budget_wo/budgetworkorder/list.html'
    context_object_name = 'budgetworkorders' # Renamed for clarity in templates

class BudgetWorkOrderTablePartialView(ListView):
    model = BudgetWorkOrder
    template_name = 'budget_wo/budgetworkorder/_budgetworkorder_table.html'
    context_object_name = 'budgetworkorders' # Renamed for clarity in templates

    def get_queryset(self):
        return BudgetWorkOrder.objects.all()

class BudgetWorkOrderCreateView(CreateView):
    model = BudgetWorkOrder
    form_class = BudgetWorkOrderForm
    template_name = 'budget_wo/budgetworkorder/_budgetworkorder_form.html' # Use partial template
    success_url = reverse_lazy('budgetworkorder_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Budget Work Order added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, tells HTMX to do nothing but process headers
                headers={
                    'HX-Trigger': 'refreshBudgetWorkOrderList', # Custom event for HTMX
                    'HX-Redirect': reverse_lazy('budgetworkorder_list') # Optional redirect if needed after modal close
                }
            )
        return response

class BudgetWorkOrderUpdateView(UpdateView):
    model = BudgetWorkOrder
    form_class = BudgetWorkOrderForm
    template_name = 'budget_wo/budgetworkorder/_budgetworkorder_form.html' # Use partial template
    success_url = reverse_lazy('budgetworkorder_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Budget Work Order updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBudgetWorkOrderList'
                }
            )
        return response

class BudgetWorkOrderDeleteView(DeleteView):
    model = BudgetWorkOrder
    template_name = 'budget_wo/budgetworkorder/_budgetworkorder_confirm_delete.html' # Use partial template
    success_url = reverse_lazy('budgetworkorder_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Budget Work Order deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBudgetWorkOrderList'
                }
            )
        return response

class BudgetWorkOrderMarkPrintedView(View):
    def post(self, request, pk):
        budget_wo = get_object_or_404(BudgetWorkOrder, pk=pk)
        if budget_wo.mark_as_printed():
            messages.success(request, f"Work Order {budget_wo.work_order_number} marked as Printed.")
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBudgetWorkOrderList'
                }
            )
        messages.error(request, f"Could not mark Work Order {budget_wo.work_order_number} as Printed.")
        return HttpResponse(status=400) # Bad request if operation failed
```

#### 4.4 Templates

**Task:** Create templates for each view.

**Instructions:**
Templates will extend `core/base.html` for common layout and CDN links. HTMX will be heavily used for dynamic content loading, especially for forms in modals and refreshing the DataTables. Alpine.js will manage modal visibility.

**`budget_wo/budgetworkorder/list.html`**
This is the main page for listing and triggering modal actions.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Budget Work Orders</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'budgetworkorder_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Budget Work Order
        </button>
    </div>
    
    <div id="budgetworkorderTable-container"
         hx-trigger="load, refreshBudgetWorkOrderList from:body"
         hx-get="{% url 'budgetworkorder_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg p-6">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Budget Work Orders...</p>
        </div>
    </div>
    
    <!-- Modal for form/delete confirmation -->
    <div id="modal" 
         class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center transition-opacity duration-300 z-50 opacity-0 pointer-events-none"
         _="on closeModal add .opacity-0 to me then add .pointer-events-none to me
            on openModal remove .opacity-0 from me then remove .pointer-events-none from me
            on click if event.target.id == 'modal' trigger closeModal">
        <div id="modalContent" 
             class="bg-white rounded-lg shadow-2xl max-w-4xl w-full mx-4 sm:mx-6 md:mx-auto transform transition-all duration-300 scale-95 opacity-0"
             _="on load transition ease-out duration-300 transform scale-100 opacity-100">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('modal', () => ({
            isOpen: false,
            open() { this.isOpen = true },
            close() { this.isOpen = false }
        }));
    });

    // Listen for custom HX-Trigger to close modal
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'modalContent' && !event.detail.xhr.responseURL.includes('add') && !event.detail.xhr.responseURL.includes('edit') && !event.detail.xhr.responseURL.includes('delete')) {
            document.getElementById('modal').dispatchEvent(new Event('closeModal'));
        }
    });

    // This handles messages from Django's messages framework
    document.body.addEventListener('htmx:afterOnLoad', function(event) {
        const messages = event.detail.xhr.getResponseHeader('HX-Trigger-After-Swap');
        if (messages) {
            try {
                const parsedMessages = JSON.parse(messages);
                if (parsedMessages.showMessages) {
                    // Logic to display Django messages (e.g., using Alpine.js or a custom notification system)
                    // For simplicity, we'll just log them here.
                    console.log('Django messages:', parsedMessages.showMessages);
                    // You might have a dedicated Alpine.js component to handle these
                    // e.g., Alpine.$data(document.getElementById('messages-container')).addMessage(type, text)
                }
            } catch (e) {
                console.error("Error parsing HX-Trigger-After-Swap messages:", e);
            }
        }
    });
</script>
{% endblock %}
```

**`budget_wo/budgetworkorder/_budgetworkorder_table.html`**
This partial template contains the DataTables structure and will be dynamically loaded by HTMX.

```html
<table id="budgetworkorderTable" class="min-w-full bg-white border border-gray-200 divide-y divide-gray-200 rounded-lg overflow-hidden">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">{{ budgetworkorders.first.work_order_number.field.verbose_name }}</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">{{ budgetworkorders.first.budget_date.field.verbose_name }}</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">{{ budgetworkorders.first.amount.field.verbose_name }}</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">{{ budgetworkorders.first.status.field.verbose_name }}</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="divide-y divide-gray-200">
        {% for obj in budgetworkorders %}
        <tr class="hover:bg-gray-50 transition duration-150 ease-in-out">
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700">{{ forloop.counter }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ obj.work_order_number }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700">{{ obj.budget_date|date:"Y-m-d" }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700 text-right">${{ obj.amount|floatformat:2 }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                    {% if obj.status == 'Approved' %}bg-green-100 text-green-800
                    {% elif obj.status == 'Printed' %}bg-blue-100 text-blue-800
                    {% elif obj.status == 'Draft' %}bg-yellow-100 text-yellow-800
                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                    {{ obj.get_status_display }}
                </span>
            </td>
            <td class="py-2 px-4 whitespace-nowrap text-sm font-medium">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 shadow-sm transition duration-150 ease-in-out"
                    hx-get="{% url 'budgetworkorder_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click trigger openModal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md mr-2 shadow-sm transition duration-150 ease-in-out"
                    hx-get="{% url 'budgetworkorder_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click trigger openModal">
                    Delete
                </button>
                {% if obj.is_open %}
                <button 
                    class="bg-purple-500 hover:bg-purple-600 text-white font-bold py-1 px-3 rounded-md shadow-sm transition duration-150 ease-in-out"
                    hx-post="{% url 'budgetworkorder_mark_printed' obj.pk %}"
                    hx-confirm="Are you sure you want to mark this work order as printed?"
                    hx-indicator="#mark-printed-spinner-{{ obj.pk }}"
                    hx-swap="none"
                    _="on htmx:afterRequest trigger refreshBudgetWorkOrderList from:body">
                    Mark Printed
                </button>
                <span id="mark-printed-spinner-{{ obj.pk }}" class="htmx-indicator ml-2 text-blue-500">
                    <i class="fas fa-spinner fa-spin"></i>
                </span>
                {% endif %}
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-4 text-center text-gray-500">No Budget Work Orders found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// DataTables initialization
$(document).ready(function() {
    // Destroy existing DataTable instance if it exists to prevent re-initialization errors
    if ($.fn.DataTable.isDataTable('#budgetworkorderTable')) {
        $('#budgetworkorderTable').DataTable().destroy();
    }
    
    $('#budgetworkorderTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [5] } // Disable sorting on Actions column
        ]
    });
});
</script>
```

**`budget_wo/budgetworkorder/_budgetworkorder_form.html`**
This partial template is used for both create and update forms within the modal.

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-4">{{ form.instance.pk|yesno:'Edit,Add' }} Budget Work Order</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {% for field in form %}
            <div class="{% if field.name == 'description' %}md:col-span-2{% endif %}">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %} <span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-5 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click trigger closeModal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-5 rounded-md shadow-md transition duration-300 ease-in-out">
                <span id="form-spinner" class="htmx-indicator mr-2">
                    <i class="fas fa-spinner fa-spin"></i>
                </span>
                Save Budget Work Order
            </button>
        </div>
    </form>
</div>
```

**`budget_wo/budgetworkorder/_budgetworkorder_confirm_delete.html`**
This partial template provides the delete confirmation interface.

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Budget Work Order: <span class="font-bold">{{ budgetworkorder.work_order_number }}</span>?</p>
    
    <div class="flex justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-5 rounded-md shadow-sm transition duration-150 ease-in-out"
            _="on click trigger closeModal">
            Cancel
        </button>
        <button 
            type="button" 
            hx-delete="{% url 'budgetworkorder_delete' budgetworkorder.pk %}"
            hx-swap="none"
            hx-indicator="#delete-spinner"
            class="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-5 rounded-md shadow-md transition duration-300 ease-in-out">
            <span id="delete-spinner" class="htmx-indicator mr-2">
                <i class="fas fa-spinner fa-spin"></i>
            </span>
            Delete
        </button>
    </div>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
URL patterns will be defined in `budget_wo/urls.py` and included in the project's main `urls.py`.

```python
# budget_wo/urls.py
from django.urls import path
from .views import (
    BudgetWorkOrderListView, BudgetWorkOrderCreateView, BudgetWorkOrderUpdateView, 
    BudgetWorkOrderDeleteView, BudgetWorkOrderTablePartialView, BudgetWorkOrderMarkPrintedView
)

urlpatterns = [
    path('budget-work-orders/', BudgetWorkOrderListView.as_view(), name='budgetworkorder_list'),
    path('budget-work-orders/table/', BudgetWorkOrderTablePartialView.as_view(), name='budgetworkorder_table'),
    path('budget-work-orders/add/', BudgetWorkOrderCreateView.as_view(), name='budgetworkorder_add'),
    path('budget-work-orders/edit/<int:pk>/', BudgetWorkOrderUpdateView.as_view(), name='budgetworkorder_edit'),
    path('budget-work-orders/delete/<int:pk>/', BudgetWorkOrderDeleteView.as_view(), name='budgetworkorder_delete'),
    path('budget-work-orders/mark-printed/<int:pk>/', BudgetWorkOrderMarkPrintedView.as_view(), name='budgetworkorder_mark_printed'),
]
```

#### 4.6 Tests

**Task:** Write tests for the model and views.

**Instructions:**
Comprehensive unit tests for model methods and integration tests for all views (list, create, update, delete, partials) will ensure correctness and maintainability.

```python
# budget_wo/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from decimal import Decimal
from .models import BudgetWorkOrder
from .forms import BudgetWorkOrderForm

class BudgetWorkOrderModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.wo1 = BudgetWorkOrder.objects.create(
            work_order_number='WO-2023-001',
            budget_date='2023-01-15',
            amount=1500.75,
            status='Draft',
            description='Initial budget for project A'
        )
        cls.wo2 = BudgetWorkOrder.objects.create(
            work_order_number='WO-2023-002',
            budget_date='2023-02-20',
            amount=250.00,
            status='Approved',
            description='Approved budget for task B'
        )
        cls.wo3 = BudgetWorkOrder.objects.create(
            work_order_number='WO-2023-003',
            budget_date='2023-03-01',
            amount=750.50,
            status='Printed',
            description='Printed budget for client C'
        )

    def test_budget_work_order_creation(self):
        self.assertEqual(self.wo1.work_order_number, 'WO-2023-001')
        self.assertEqual(self.wo1.budget_date, timezone.localdate(timezone.datetime(2023, 1, 15)))
        self.assertEqual(self.wo1.amount, Decimal('1500.75'))
        self.assertEqual(self.wo1.status, 'Draft')
        self.assertEqual(self.wo1.description, 'Initial budget for project A')
        self.assertIsNotNone(self.wo1.created_date)
        self.assertIsNotNone(self.wo1.last_modified_date)

    def test_work_order_number_label(self):
        field_label = self.wo1._meta.get_field('work_order_number').verbose_name
        self.assertEqual(field_label, 'Work Order No.')

    def test_amount_max_digits_decimal_places(self):
        amount_field = self.wo1._meta.get_field('amount')
        self.assertEqual(amount_field.max_digits, 18)
        self.assertEqual(amount_field.decimal_places, 2)

    def test_str_method(self):
        self.assertEqual(str(self.wo1), 'WO-2023-001 (Draft)')

    def test_is_open_method(self):
        self.assertTrue(self.wo1.is_open()) # Draft
        self.assertTrue(self.wo2.is_open()) # Approved
        self.assertFalse(self.wo3.is_open()) # Printed

    def test_mark_as_printed_method(self):
        # Test marking an approved WO as printed
        self.assertTrue(self.wo2.mark_as_printed())
        self.assertEqual(self.wo2.status, 'Printed')
        
        # Test trying to mark a draft WO as printed (should fail as per logic)
        self.assertFalse(self.wo1.mark_as_printed())
        self.assertEqual(self.wo1.status, 'Draft') # Status should remain unchanged

    def test_get_status_display_method(self):
        self.assertEqual(self.wo1.get_status_display(), 'Draft (Pending Review)')
        self.assertEqual(self.wo2.get_status_display(), 'Approved (Ready to Print)')
        self.assertEqual(self.wo3.get_status_display(), 'Printed (Completed)')
        
class BudgetWorkOrderViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.wo1 = BudgetWorkOrder.objects.create(
            work_order_number='WO-VIEW-001',
            budget_date='2023-04-01',
            amount=500.00,
            status='Approved',
            description='Budget for testing views'
        )

    def setUp(self):
        self.client = Client()
        self.list_url = reverse('budgetworkorder_list')
        self.table_url = reverse('budgetworkorder_table')
        self.add_url = reverse('budgetworkorder_add')
        self.edit_url = reverse('budgetworkorder_edit', args=[self.wo1.pk])
        self.delete_url = reverse('budgetworkorder_delete', args=[self.wo1.pk])
        self.mark_printed_url = reverse('budgetworkorder_mark_printed', args=[self.wo1.pk])

    def test_list_view_get(self):
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'budget_wo/budgetworkorder/list.html')
        self.assertTrue('budgetworkorders' in response.context)
        self.assertContains(response, self.wo1.work_order_number)

    def test_table_partial_view_get(self):
        response = self.client.get(self.table_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'budget_wo/budgetworkorder/_budgetworkorder_table.html')
        self.assertTrue('budgetworkorders' in response.context)
        self.assertContains(response, self.wo1.work_order_number)
        # Ensure DataTables script is included
        self.assertContains(response, '$(document).ready(function() {')

    def test_create_view_get(self):
        response = self.client.get(self.add_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'budget_wo/budgetworkorder/_budgetworkorder_form.html')
        self.assertTrue('form' in response.context)
        self.assertIsInstance(response.context['form'], BudgetWorkOrderForm)

    def test_create_view_post_success(self):
        data = {
            'work_order_number': 'WO-NEW-001',
            'budget_date': '2024-01-01',
            'amount': '100.00',
            'status': 'Draft',
            'description': 'New work order via form'
        }
        response = self.client.post(self.add_url, data)
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertTrue(BudgetWorkOrder.objects.filter(work_order_number='WO-NEW-001').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshBudgetWorkOrderList', response.headers['HX-Trigger'])

    def test_create_view_post_invalid(self):
        data = {
            'work_order_number': 'WO-INVALID',
            'budget_date': '2025-01-01', # Future date, should be invalid
            'amount': '-10.00', # Negative amount, should be invalid
            'status': 'Draft',
        }
        response = self.client.post(self.add_url, data)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertContains(response, 'Amount must be positive.')
        self.assertContains(response, 'Budget Date cannot be in the future.')
        self.assertTemplateUsed(response, 'budget_wo/budgetworkorder/_budgetworkorder_form.html')
        self.assertFalse(BudgetWorkOrder.objects.filter(work_order_number='WO-INVALID').exists())

    def test_update_view_get(self):
        response = self.client.get(self.edit_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'budget_wo/budgetworkorder/_budgetworkorder_form.html')
        self.assertTrue('form' in response.context)
        self.assertIsInstance(response.context['form'], BudgetWorkOrderForm)
        self.assertEqual(response.context['form'].instance, self.wo1)

    def test_update_view_post_success(self):
        data = {
            'work_order_number': 'WO-VIEW-001', # Same WO number
            'budget_date': '2023-04-01',
            'amount': '550.00', # Changed amount
            'status': 'Approved',
            'description': 'Updated description'
        }
        response = self.client.post(self.edit_url, data)
        self.assertEqual(response.status_code, 204) # HTMX success
        self.wo1.refresh_from_db()
        self.assertEqual(self.wo1.amount, Decimal('550.00'))
        self.assertEqual(self.wo1.description, 'Updated description')
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshBudgetWorkOrderList', response.headers['HX-Trigger'])

    def test_delete_view_get(self):
        response = self.client.get(self.delete_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'budget_wo/budgetworkorder/_budgetworkorder_confirm_delete.html')
        self.assertTrue('budgetworkorder' in response.context)
        self.assertEqual(response.context['budgetworkorder'], self.wo1)

    def test_delete_view_post_success(self):
        # Create a new object to delete, so setUpTestData object is not affected for other tests
        wo_to_delete = BudgetWorkOrder.objects.create(
            work_order_number='WO-TO-DELETE',
            budget_date='2023-05-01',
            amount=99.99,
            status='Draft'
        )
        delete_url = reverse('budgetworkorder_delete', args=[wo_to_delete.pk])
        response = self.client.delete(delete_url) # DELETE verb is used for DeleteView
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertFalse(BudgetWorkOrder.objects.filter(pk=wo_to_delete.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshBudgetWorkOrderList', response.headers['HX-Trigger'])

    def test_mark_printed_view_post_success(self):
        wo_to_print = BudgetWorkOrder.objects.create(
            work_order_number='WO-PRINT-001',
            budget_date='2023-06-01',
            amount=1000.00,
            status='Approved' # Can be marked printed
        )
        mark_printed_url = reverse('budgetworkorder_mark_printed', args=[wo_to_print.pk])
        response = self.client.post(mark_printed_url)
        self.assertEqual(response.status_code, 204)
        wo_to_print.refresh_from_db()
        self.assertEqual(wo_to_print.status, 'Printed')
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshBudgetWorkOrderList', response.headers['HX-Trigger'])

    def test_mark_printed_view_post_failure(self):
        wo_cannot_print = BudgetWorkOrder.objects.create(
            work_order_number='WO-PRINT-002',
            budget_date='2023-06-02',
            amount=2000.00,
            status='Draft' # Cannot be marked printed as per logic
        )
        mark_printed_url = reverse('budgetworkorder_mark_printed', args=[wo_cannot_print.pk])
        response = self.client.post(mark_printed_url)
        self.assertEqual(response.status_code, 400) # Bad request as operation failed
        wo_cannot_print.refresh_from_db()
        self.assertEqual(wo_cannot_print.status, 'Draft') # Status remains unchanged
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
- **HTMX:**
    - `list.html` uses `hx-get` to load `_budgetworkorder_table.html` initially and on `refreshBudgetWorkOrderList` event.
    - Buttons in `_budgetworkorder_table.html` use `hx-get` to load forms (`_budgetworkorder_form.html`, `_budgetworkorder_confirm_delete.html`) into the modal.
    - Form submissions in `_budgetworkorder_form.html` use `hx-post` (or `hx-delete` for delete) with `hx-swap="none"` and `HX-Trigger` headers to signal success and refresh the list without reloading the entire page.
    - `hx-indicator` is used to show loading spinners during HTMX requests.
- **Alpine.js:**
    - Manages the visibility of the main modal (`#modal`) using `x-data` and custom events (`openModal`, `closeModal`).
    - The modal also includes logic (`on click if event.target.id == 'modal'`) to close itself when clicking outside its content.
- **DataTables:**
    - Initialized within the `_budgetworkorder_table.html` partial, ensuring that when the partial is loaded via HTMX, the DataTables library correctly applies its functionality to the new `<table>` element.
    - The `hx-trigger="load, refreshBudgetWorkOrderList from:body"` on the table container ensures DataTables is re-initialized whenever the table content is refreshed.

This combination provides a highly dynamic and interactive user experience, similar to a Single Page Application, but with minimal custom JavaScript, leveraging the power of HTMX for server-side rendering and Alpine.js for lightweight UI state management.

## Final Notes

- This plan replaces placeholders with concrete values inferred from the ASP.NET file name, demonstrating a full CRUD implementation.
- All templates adhere to DRY principles by using partial templates (`_*.html`) for reusable components loaded via HTMX.
- Business logic (e.g., `is_open()`, `mark_as_printed()`) resides in the `BudgetWorkOrder` model, keeping views concise and focused on request handling.
- Comprehensive unit and integration tests are provided to ensure the correctness and robustness of the migrated functionality.
- The use of HTMX, Alpine.js, and DataTables ensures a modern, responsive, and efficient user interface without complex JavaScript frameworks.
- Tailwind CSS classes are integrated directly into the HTML for streamlined styling.