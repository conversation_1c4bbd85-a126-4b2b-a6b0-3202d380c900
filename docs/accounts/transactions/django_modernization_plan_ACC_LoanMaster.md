The following comprehensive Django modernization plan addresses the migration of your ASP.NET `ACC_LoanMaster.aspx` and its C# code-behind to a modern Django application. This plan focuses on leveraging Django 5.0+ features, a "fat model, thin view" architecture, HTMX for dynamic interactions, Alpine.js for UI state, and DataTables for efficient data presentation, all within the constraints of your existing database schema.

---

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

This conversion will be implemented within a new Django application, which we will name `accounts`.

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

From the `SqlDataSource` and direct SQL queries within the C# code, we identify two primary tables and their inferred columns:

1.  **`tblAcc_LoanMaster`**:
    *   `Id`: Primary Key (Integer)
    *   `Particulars`: Text (String)
    *   `SysDate`: System Date (Date) - *Used in inserts, implying a column.*
    *   `SysTime`: System Time (Time) - *Used in inserts, implying a column.*
    *   `CompId`: Company ID (Integer) - *Used in inserts and selects for filtering.*
    *   `FinYearId`: Financial Year ID (Integer) - *Used in inserts and selects for filtering.*
    *   `SessionId`: User Session ID / Username (String) - *Used in inserts.*

2.  **`tblAcc_LoanDetails`**:
    *   `Id`: Primary Key (Integer)
    *   `MId`: Foreign Key to `tblAcc_LoanMaster.Id` (Integer)
    *   `Particulars`: Text (String)
    *   `CreditAmt`: Credit Amount (Decimal, with up to 3 decimal places, inferred from `RegularExpressionValidator` `^\d{1,15}(\.\d{0,3})?$`)
    *   `SysDate`: System Date (Date) - *Used in inserts, implying a column.*
    *   `SysTime`: System Time (Time) - *Used in inserts, implying a column.*
    *   `CompId`: Company ID (Integer) - *Used in inserts.*
    *   `FinYearId`: Financial Year ID (Integer) - *Used in inserts.*
    *   `SessionId`: User Session ID / Username (String) - *Used in inserts.*

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**For `tblAcc_LoanMaster` (Loan Master):**

*   **Read (List View):** The `GridView2` displays a paginated list of Loan Master entries, filtered by `FinYearId` and `CompId`.
*   **Create:** New Loan Master entries can be added via textboxes and buttons in the grid's footer or empty data template.
*   **Delete:** Loan Master entries can be deleted. A critical business rule prevents deletion if the master record has associated `LoanDetail` entries.
*   **Action (View Details):** Clicking on a master's `Particulars` (a `LinkButton`) loads its associated `LoanDetail` records into a separate panel.

**For `tblAcc_LoanDetails` (Loan Details):**

*   **Read (List View):** The `GridView3` displays a paginated list of `LoanDetail` entries specific to the currently selected `LoanMaster`.
*   **Create:** New `LoanDetail` entries can be added via textboxes and buttons in the grid's footer or empty data template. Input fields include `Particulars` and `CreditAmt`.
*   **Delete:** `LoanDetail` entries can be deleted.

**Validation Logic:**

*   `Particulars` fields for both master and detail records are required.
*   `CreditAmt` for Loan Details is required and must conform to a numeric format (decimal with up to 3 decimal places).
*   Client-side confirmation dialogs are used for Add and Delete actions.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

The ASP.NET page is logically divided into two primary content areas, likely displayed side-by-side:

1.  **Left Panel (Loan Master List):**
    *   A `Panel` containing an `UpdatePanel` for AJAX updates.
    *   `GridView2`: Displays `LoanMaster` data with columns for "SN" (serial number), "Particulars" (acting as a clickable link to load details), and "Delete" actions. It includes a footer row for adding new entries and an `EmptyDataTemplate` for initial entry.
    *   `SqlDataSource1`: Data source for `GridView2`.

2.  **Right Panel (Loan Details List):**
    *   A `Panel` containing another `UpdatePanel`.
    *   `Panel3`: A "No data found !" message panel, conditionally visible based on `GridView3` content.
    *   `GridView3`: Displays `LoanDetail` data associated with the selected `LoanMaster`. Columns include "SN", "Particulars", "Credit", and "Delete" actions. It also has a footer row and `EmptyDataTemplate` for adding new entries.
    *   `SqlDataSource2`: Data source for `GridView3` (though data binding is primarily programmatic).

**Client-Side Interactions:**
*   `AjaxControlToolkit` `UpdatePanel`s are extensively used for partial page updates, which will be replaced entirely by HTMX.
*   JavaScript functions like `confirmationDelete()` and `confirmationAdd()` for user confirmation. In Django, this will be handled by HTMX's `hx-confirm` or Alpine.js modals for a smoother user experience.

### Step 4: Generate Django Code

We will create a Django application named `accounts` to house this functionality.

#### 4.1 Models

We will create `LoanMaster` and `LoanDetail` models. We assume the existing database table `Id` columns are auto-incrementing; thus, we define `id` as `primary_key=True` but exclude it from forms for new entries, letting the database handle it. `managed = False` is crucial for existing tables.

```python
# accounts/models.py
from django.db import models
from django.utils import timezone # For sys_date and sys_time

class LoanMaster(models.Model):
    """
    Represents the main loan records from tblAcc_LoanMaster.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    particulars = models.CharField(db_column='Particulars', max_length=255)
    
    # Audit fields, populated from ASP.NET code-behind's `fun.insert` statements.
    # In a real Django app, these might be populated automatically via signals/middleware.
    sys_date = models.DateField(db_column='SysDate', default=timezone.now)
    sys_time = models.TimeField(db_column='SysTime', default=timezone.now)
    comp_id = models.IntegerField(db_column='CompId', null=True, blank=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', null=True, blank=True)
    session_id = models.CharField(db_column='SessionId', max_length=255, null=True, blank=True)

    class Meta:
        managed = False  # Critical: Tells Django not to manage this table's schema
        db_table = 'tblAcc_LoanMaster'
        verbose_name = 'Loan Master'
        verbose_name_plural = 'Loan Masters'
        ordering = ['-id'] # Matches the 'Order by Id DESC' in ASP.NET

    def __str__(self):
        return self.particulars
    
    # Business logic from ASP.NET: Check if a master record has any associated details
    def has_details(self):
        """Returns True if this LoanMaster has any associated LoanDetail records."""
        return self.loandetail_set.exists()

class LoanDetail(models.Model):
    """
    Represents the detailed entries for each loan master from tblAcc_LoanDetails.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    # MId is a Foreign Key to LoanMaster.Id
    master = models.ForeignKey(
        LoanMaster, 
        on_delete=models.CASCADE, # Assuming CASCADE, verify DB integrity rules
        db_column='MId', 
        related_name='loandetail_set' # Custom related name for reverse relation
    )
    particulars = models.CharField(db_column='Particulars', max_length=255)
    credit_amt = models.DecimalField(db_column='CreditAmt', max_digits=18, decimal_places=3)
    
    # Audit fields, inferred from ASP.NET code-behind's `fun.insert` statements.
    sys_date = models.DateField(db_column='SysDate', default=timezone.now)
    sys_time = models.TimeField(db_column='SysTime', default=timezone.now)
    comp_id = models.IntegerField(db_column='CompId', null=True, blank=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', null=True, blank=True)
    session_id = models.CharField(db_column='SessionId', max_length=255, null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblAcc_LoanDetails'
        verbose_name = 'Loan Detail'
        verbose_name_plural = 'Loan Details'

    def __str__(self):
        return f"{self.particulars} ({self.credit_amt})"

    # No additional business logic identified for LoanDetail beyond standard CRUD
```

#### 4.2 Forms

We'll create `ModelForm` classes for `LoanMaster` and `LoanDetail` to handle input and validation.

```python
# accounts/forms.py
from django import forms
from .models import LoanMaster, LoanDetail
from decimal import Decimal

class LoanMasterForm(forms.ModelForm):
    """
    Form for creating new LoanMaster records.
    """
    class Meta:
        model = LoanMaster
        # Exclude 'id' as it's assumed to be auto-incrementing in the existing DB.
        fields = ['particulars'] 
        widgets = {
            'particulars': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter particulars'
            }),
        }

    # Custom validation for 'Particulars' (ASP.NET RequiredFieldValidator)
    def clean_particulars(self):
        particulars = self.cleaned_data.get('particulars')
        if not particulars:
            raise forms.ValidationError("Particulars is required.")
        return particulars

class LoanDetailForm(forms.ModelForm):
    """
    Form for creating new LoanDetail records.
    """
    class Meta:
        model = LoanDetail
        fields = ['particulars', 'credit_amt']
        widgets = {
            'particulars': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter particulars'
            }),
            'credit_amt': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter credit amount',
                'step': '0.001' # Allows up to 3 decimal places in browser input
            }),
        }
    
    # Custom validation for 'Particulars' (ASP.NET RequiredFieldValidator)
    def clean_particulars(self):
        particulars = self.cleaned_data.get('particulars')
        if not particulars:
            raise forms.ValidationError("Particulars is required.")
        return particulars

    # Custom validation for 'CreditAmt' (ASP.NET RequiredFieldValidator + RegularExpressionValidator)
    def clean_credit_amt(self):
        credit_amt = self.cleaned_data.get('credit_amt')
        if credit_amt is None: # Covers empty or non-numeric input
            raise forms.ValidationError("Credit amount is required.")
        if credit_amt == Decimal('0.000'): # Specific check for zero amount as in ASP.NET
            raise forms.ValidationError("Credit amount cannot be zero.")
        return credit_amt
```

#### 4.3 Views

We will implement Django's Class-Based Views (CBVs) for the primary listing, creation, and deletion operations. Dedicated `View` classes will handle HTMX partial rendering. We'll include a helper function `get_current_context` to simulate session data (`CompId`, `FinYearId`, `SessionId`), which in a real application would come from `request.user` or a user profile.

```python
# accounts/views.py
from django.views.generic import ListView, CreateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect, Http404
from django.shortcuts import get_object_or_404, render
from django.utils import timezone

from .models import LoanMaster, LoanDetail
from .forms import LoanMasterForm, LoanDetailForm

# Helper function to get current context (company, financial year, user).
# In a full application, this would integrate with Django's authentication
# and a user profile model to retrieve actual CompId, FinYearId, and SessionId.
def get_current_context(request):
    # Dummy values for demonstration. Replace with actual logic.
    # e.g., comp_id = request.user.profile.company_id if using profiles
    # e.g., fin_year_id = request.session.get('current_fin_year_id', default_year)
    # e.g., session_id = request.user.username if authenticated
    comp_id = getattr(request.user, 'company_id', 1) 
    fin_year_id = getattr(request.user, 'financial_year_id', 2023)
    session_id = getattr(request.user, 'username', 'system')
    return comp_id, fin_year_id, session_id

class LoanMasterListView(ListView):
    """
    Renders the main Loan Master list page (accounts/loanmaster/list.html).
    This view serves the initial full page, which then loads partials via HTMX.
    """
    model = LoanMaster
    template_name = 'accounts/loanmaster/list.html'
    context_object_name = 'loan_masters'
    paginate_by = 24 # Matches PageSize="24" in ASP.NET GridView2

    def get_queryset(self):
        # Apply filtering based on CompId and FinYearId from current context, matching ASP.NET logic.
        comp_id, fin_year_id, _ = get_current_context(self.request)
        return LoanMaster.objects.filter(
            fin_year_id__lte=fin_year_id, comp_id=comp_id
        ).order_by('-id') # Order by Id DESC as in ASP.NET

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # The actual table data will be loaded via HTMX in a separate partial view.
        return context

class LoanMasterTablePartialView(ListView):
    """
    Renders only the Loan Master table (accounts/loanmaster/_master_table.html) for HTMX requests.
    """
    model = LoanMaster
    template_name = 'accounts/loanmaster/_master_table.html'
    context_object_name = 'loan_masters'
    paginate_by = 24 # Matches PageSize="24" in ASP.NET GridView2

    def get_queryset(self):
        comp_id, fin_year_id, _ = get_current_context(self.request)
        return LoanMaster.objects.filter(
            fin_year_id__lte=fin_year_id, comp_id=comp_id
        ).order_by('-id')

class LoanMasterCreateView(CreateView):
    """
    Handles creating new Loan Master records.
    Renders accounts/loanmaster/_master_form.html for HTMX modal.
    """
    model = LoanMaster
    form_class = LoanMasterForm
    template_name = 'accounts/loanmaster/_master_form.html' # Partial template for modal
    # success_url is primarily for non-HTMX requests; HTMX handles response via headers.
    success_url = reverse_lazy('loanmaster_list') 

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['action'] = 'Add' # Context for template display
        return context

    def form_valid(self, form):
        # Populate audit fields before saving, as done in ASP.NET code-behind.
        comp_id, fin_year_id, session_id = get_current_context(self.request)
        form.instance.comp_id = comp_id
        form.instance.fin_year_id = fin_year_id
        form.instance.session_id = session_id
        form.instance.sys_date = timezone.now().date()
        form.instance.sys_time = timezone.now().time()
        
        response = super().form_valid(form)
        messages.success(self.request, 'Loan Master added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX, return 204 No Content to indicate success and trigger client-side events.
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshLoanMasterList' # Custom event to refresh the master table
                }
            )
        return response
    
    def form_invalid(self, form):
        # If form is invalid during an HTMX request, re-render the form with errors.
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form, 'action': 'Add'})
        return super().form_invalid(form)


class LoanMasterDeleteView(DeleteView):
    """
    Handles deleting Loan Master records.
    Renders accounts/loanmaster/_master_confirm_delete.html for HTMX modal.
    Includes business logic to prevent deletion if details exist.
    """
    model = LoanMaster
    template_name = 'accounts/loanmaster/_master_confirm_delete.html' # Partial for modal
    success_url = reverse_lazy('loanmaster_list') # Fallback URL

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Business logic: Check if deletion is allowed based on associated details.
        context['can_delete'] = not self.object.has_details()
        return context

    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        # Enforce business rule: Cannot delete Loan Master if it has associated details.
        if self.object.has_details():
            messages.error(self.request, 'Cannot delete Loan Master with associated Loan Details.')
            if request.headers.get('HX-Request'):
                # For HTMX, re-render the confirmation modal with the error message.
                context = self.get_context_data(object=self.object)
                return render(request, self.template_name, context)
            return HttpResponseRedirect(self.get_success_url())

        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Loan Master deleted successfully.')
        if request.headers.get('HX-Request'):
            # Trigger events to refresh both master and detail tables after successful delete.
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshLoanMasterList, clearLoanDetailTable' 
                }
            )
        return response
    
    def http_method_not_allowed(self, request, *args, **kwargs):
        # Handle GET requests for delete confirmation via HTMX.
        if request.method == 'GET' and request.headers.get('HX-Request'):
            self.object = self.get_object()
            context = self.get_context_data(object=self.object)
            return render(request, self.template_name, context)
        return super().http_method_not_allowed(request, *args, **kwargs)


class LoanMasterSelectView(View):
    """
    Handles the action of selecting a Loan Master. 
    It renders the entire right-hand pane (`_detail_pane.html`) including the details table
    and its add form, replacing the ASP.NET `HpPerticulars` command.
    """
    def get(self, request, master_pk):
        loan_master = get_object_or_404(LoanMaster, pk=master_pk)
        
        loan_details = LoanDetail.objects.filter(master=loan_master)
        
        context = {
            'loan_master': loan_master,
            'loan_details': loan_details,
            'loan_master_id': master_pk, # Pass master_pk for detail form URLs
            'loan_detail_form': LoanDetailForm(), # Provide an empty form for the detail table footer
            'no_data_found': not loan_details.exists() # Controls visibility of "No data found!" message
        }
        return render(request, 'accounts/loandetail/_detail_pane.html', context)


# Loan Detail Views

class LoanDetailTablePartialView(View):
    """
    Renders only the Loan Detail table (`_detail_table.html`) for HTMX updates.
    This is used when a detail table needs to be refreshed without reloading the entire pane.
    """
    def get(self, request, master_pk, *args, **kwargs):
        loan_master = get_object_or_404(LoanMaster, pk=master_pk)
        
        loan_details = LoanDetail.objects.filter(master=loan_master)
        
        context = {
            'loan_details': loan_details,
            'loan_master_id': master_pk,
            'loan_detail_form': LoanDetailForm(),
            'no_data_found': not loan_details.exists()
        }
        return render(request, 'accounts/loandetail/_detail_table.html', context)

class LoanDetailCreateView(CreateView):
    """
    Handles creating new Loan Detail records for a specific Loan Master.
    The form is rendered directly within the _detail_table.html partial.
    """
    model = LoanDetail
    form_class = LoanDetailForm
    template_name = 'accounts/loandetail/_detail_form.html' # Used for error re-rendering via HTMX

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['action'] = 'Add'
        context['master_pk'] = self.kwargs['master_pk'] # Pass master_pk for form action URL
        return context

    def form_valid(self, form):
        master_pk = self.kwargs['master_pk']
        loan_master = get_object_or_404(LoanMaster, pk=master_pk)
        
        # Populate audit fields and link to master record
        comp_id, fin_year_id, session_id = get_current_context(self.request)
        form.instance.master = loan_master
        form.instance.comp_id = comp_id
        form.instance.fin_year_id = fin_year_id
        form.instance.session_id = session_id
        form.instance.sys_date = timezone.now().date()
        form.instance.sys_time = timezone.now().time()
        
        response = super().form_valid(form)
        messages.success(self.request, 'Loan Detail added successfully.')
        if self.request.headers.get('HX-Request'):
            # Trigger events to refresh both detail and master lists.
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': f'refreshLoanDetailList-{master_pk}, refreshLoanMasterList'
                }
            )
        return response
    
    def form_invalid(self, form):
        # If form is invalid during an HTMX request (e.g., from inline table form),
        # re-render the detail table with errors. This will be tricky if the form
        # is part of the table footer. A more robust solution might involve returning
        # JSON errors or updating specific form fields only.
        # For simplicity here, we'll re-render the modal if it's invoked via modal.
        # If the form is inline in table footer, client-side validation is preferred or
        # a full table re-render. Given the ASP.NET approach, we'll re-render the table.
        master_pk = self.kwargs['master_pk']
        loan_master = get_object_or_404(LoanMaster, pk=master_pk)
        loan_details = LoanDetail.objects.filter(master=loan_master)
        
        context = {
            'loan_details': loan_details,
            'loan_master_id': master_pk,
            'loan_detail_form': form, # Pass the form with errors
            'no_data_found': not loan_details.exists()
        }
        # Re-render the table with the form containing errors.
        return render(self.request, 'accounts/loandetail/_detail_table.html', context)


class LoanDetailDeleteView(DeleteView):
    """
    Handles deleting Loan Detail records.
    Renders accounts/loandetail/_detail_confirm_delete.html for HTMX modal.
    """
    model = LoanDetail
    template_name = 'accounts/loandetail/_detail_confirm_delete.html' # Partial for modal

    # After deletion, return to the master's detail list, not the global master list.
    def get_success_url(self):
        master_pk = self.kwargs['master_pk']
        return reverse_lazy('loandetail_table', kwargs={'master_pk': master_pk})

    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        master_pk = self.object.master.pk # Get master_pk before deletion
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Loan Detail deleted successfully.')
        if request.headers.get('HX-Request'):
            # Trigger events to refresh both detail and master lists.
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': f'refreshLoanDetailList-{master_pk}, refreshLoanMasterList'
                }
            )
        return response
    
    def http_method_not_allowed(self, request, *args, **kwargs):
        # Handle GET requests for delete confirmation via HTMX.
        if request.method == 'GET' and request.headers.get('HX-Request'):
            self.object = self.get_object()
            context = self.get_context_data(object=self.object)
            return render(request, self.template_name, context)
        return super().http_method_not_allowed(request, *args, **kwargs)

```

#### 4.4 Templates

Templates are designed to be HTMX-friendly partials, extending `core/base.html` for the main page and containing specific components for dynamic loading.

**`accounts/loanmaster/list.html`** (Main page, extends base.html, holds containers for HTMX content)
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Loan Master Management</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'loanmaster_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Loan Master
        </button>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Left Panel: Loan Master List -->
        <div class="bg-white shadow-lg rounded-lg p-6">
            <h3 class="text-xl font-semibold mb-4 text-gray-700">Master List</h3>
            <div id="loanMasterTable-container"
                 hx-trigger="load, refreshLoanMasterList from:body"
                 hx-get="{% url 'loanmaster_table' %}"
                 hx-swap="innerHTML">
                <!-- DataTables for Loan Master will be loaded here via HTMX -->
                <div class="text-center py-10">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading Loan Masters...</p>
                </div>
            </div>
        </div>

        <!-- Right Panel: Loan Details List -->
        <div class="bg-white shadow-lg rounded-lg p-6">
            <h3 class="text-xl font-semibold mb-4 text-gray-700">Loan Details</h3>
            <div id="loanDetailPane-container">
                <!-- Loan Details and associated form will be loaded here via HTMX after selecting a master -->
                <div class="text-center py-10 text-gray-500">
                    <p>Select a Loan Master from the left to view its details.</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal for forms (add/edit/delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 hidden flex items-center justify-center z-50 transition-opacity duration-300"
         x-data="{ showModal: false }"
         x-show="showModal"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0 scale-95"
         x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100 scale-100"
         x-transition:leave-end="opacity-0 scale-95"
         _="on htmx:afterOnLoad from:#modalContent add .is-active to #modal then init Alpine on #modalContent"
         _="on htmx:beforeSwap.p-modalContent remove .is-active from #modal"
         hx-trigger="refreshLoanMasterList, refreshLoanDetailList-* from:body"
         _="on refreshLoanMasterList remove .is-active from #modal and remove .is-active from #modal and add .is-active to #loanDetailPane-container"
         >
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-auto"
             _="on click outside of #modalContent remove .is-active from #modal">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}
```

**`accounts/loanmaster/_master_table.html`** (Partial for Loan Master DataTable)
```html
<table id="loanMasterTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr class="bg-gray-100">
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Particulars</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-600 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in loan_masters %}
        <tr class="hover:bg-gray-50">
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">
                <button class="text-blue-600 hover:text-blue-800 font-medium"
                        hx-get="{% url 'loanmaster_select' obj.pk %}"
                        hx-target="#loanDetailPane-container"
                        hx-swap="innerHTML">
                    {{ obj.particulars }}
                </button>
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                {% if not obj.has_details %}
                <button
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-xs shadow-sm transition duration-300 ease-in-out"
                    hx-get="{% url 'loanmaster_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
                {% else %}
                <span class="text-gray-500 text-xs italic">Has details</span>
                {% endif %}
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="3" class="py-4 px-4 text-center text-gray-500">No Loan Masters found.
                <button class="text-blue-600 hover:text-blue-800 font-medium ml-2"
                        hx-get="{% url 'loanmaster_add' %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Click here to add one.
                </button>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Initialize DataTables on the loaded table
    $(document).ready(function() {
        $('#loanMasterTable').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "pageLength": 24, // Matches ASP.NET PageSize
            "lengthMenu": [[10, 24, 50, -1], [10, 24, 50, "All"]]
        });
    });
</script>
```

**`accounts/loanmaster/_master_form.html`** (Partial for Loan Master Add Form)
```html
<div class="p-6">
    <h3 class="text-xl font-medium text-gray-900 mb-6">{{ action }} Loan Master</h3>
    <form hx-post="{% url 'loanmaster_add' %}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-4">
            <div>
                <label for="{{ form.particulars.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.particulars.label }}<span class="text-red-500">*</span>
                </label>
                {{ form.particulars }}
                {% if form.particulars.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.particulars.errors }}</p>
                {% endif %}
            </div>
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

**`accounts/loanmaster/_master_confirm_delete.html`** (Partial for Loan Master Delete Confirmation)
```html
<div class="p-6">
    <h3 class="text-xl font-medium text-gray-900 mb-6">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Loan Master "{{ object.particulars }}"?</p>

    {% if not can_delete %}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
        <strong class="font-bold">Error:</strong>
        <span class="block sm:inline">This Loan Master cannot be deleted because it has associated Loan Details.</span>
    </div>
    {% endif %}

    <div class="mt-6 flex items-center justify-end space-x-4">
        <button
            type="button"
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-300 ease-in-out"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        {% if can_delete %}
        <button
            hx-post="{% url 'loanmaster_delete' object.pk %}"
            hx-swap="none"
            type="button"
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
            Delete
        </button>
        {% endif %}
    </div>
</div>
```

**`accounts/loandetail/_detail_pane.html`** (This partial renders the entire right-hand section, including the details table container)
```html
<h3 class="text-xl font-semibold mb-4 text-gray-700">Loan Details for "{{ loan_master.particulars }}"</h3>

<div id="loanDetailTable-container"
     hx-trigger="load, refreshLoanDetailList-{{ loan_master_id }} from:body"
     hx-get="{% url 'loandetail_table' loan_master_id %}"
     hx-swap="innerHTML">
    <!-- Loan Detail DataTable will be loaded here via HTMX -->
    <div class="text-center py-10">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">Loading Loan Details...</p>
    </div>
</div>
```

**`accounts/loandetail/_detail_table.html`** (Partial for Loan Detail DataTable, including inline form)
```html
{% if no_data_found %}
<div id="noDataPanel" class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4" role="alert">
    <p class="font-bold">No data found !</p>
    <p>There are no details for this loan master yet. Use the form below to add one.</p>
</div>
{% endif %}

<table id="loanDetailTable-{{ loan_master_id }}" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr class="bg-gray-100">
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Particulars</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Credit</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-600 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in loan_details %}
        <tr class="hover:bg-gray-50">
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ obj.particulars }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ obj.credit_amt }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                <button
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-xs shadow-sm transition duration-300 ease-in-out"
                    hx-get="{% url 'loandetail_delete' loan_master_id=loan_master_id pk=obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        {% comment %} Empty data template is handled by the `no_data_found` variable and `noDataPanel` above {% endcomment %}
        {% endfor %}
    </tbody>
    <tfoot>
        <tr class="bg-gray-100">
            <td colspan="4" class="py-2 px-4 border-t border-gray-200">
                <form hx-post="{% url 'loandetail_add' loan_master_id %}" hx-swap="none">
                    {% csrf_token %}
                    <div class="flex items-center space-x-2">
                        <span class="text-sm font-medium text-gray-700 w-16">SN:</span>
                        <input type="text" value="Insert" readonly class="bg-gray-200 text-gray-700 py-1 px-2 rounded-md text-xs w-20 text-center border border-gray-300">
                        
                        <div class="flex-grow">
                            <label for="{{ loan_detail_form.particulars.id_for_label }}" class="sr-only">Particulars</label>
                            {{ loan_detail_form.particulars }}
                            {% if loan_detail_form.particulars.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ loan_detail_form.particulars.errors }}</p>
                            {% endif %}
                        </div>
                        <div class="w-1/4">
                            <label for="{{ loan_detail_form.credit_amt.id_for_label }}" class="sr-only">Credit Amount</label>
                            {{ loan_detail_form.credit_amt }}
                            {% if loan_detail_form.credit_amt.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ loan_detail_form.credit_amt.errors }}</p>
                            {% endif %}
                        </div>
                        <button type="submit"
                                class="bg-green-500 hover:bg-green-600 text-white font-bold py-1 px-3 rounded-md text-xs shadow-sm transition duration-300 ease-in-out">
                            Insert
                        </button>
                    </div>
                </form>
            </td>
        </tr>
    </tfoot>
</table>

<script>
    // Initialize DataTables on the loaded table
    $(document).ready(function() {
        $('#loanDetailTable-{{ loan_master_id }}').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "pageLength": 22, // Matches ASP.NET PageSize
            "lengthMenu": [[10, 22, 50, -1], [10, 22, 50, "All"]]
        });
    });
</script>
```

**`accounts/loandetail/_detail_form.html`** (Partial for Loan Detail Add/Edit Form - primarily for modal if needed, but in-table form is primary add method)
```html
<div class="p-6">
    <h3 class="text-xl font-medium text-gray-900 mb-6">{{ action }} Loan Detail</h3>
    <form hx-post="{% url 'loandetail_add' master_pk %}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-4">
            <div>
                <label for="{{ form.particulars.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.particulars.label }}<span class="text-red-500">*</span>
                </label>
                {{ form.particulars }}
                {% if form.particulars.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.particulars.errors }}</p>
                {% endif %}
            </div>
            <div>
                <label for="{{ form.credit_amt.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.credit_amt.label }}<span class="text-red-500">*</span>
                </label>
                {{ form.credit_amt }}
                {% if form.credit_amt.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.credit_amt.errors }}</p>
                {% endif %}
            </div>
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

**`accounts/loandetail/_detail_confirm_delete.html`** (Partial for Loan Detail Delete Confirmation)
```html
<div class="p-6">
    <h3 class="text-xl font-medium text-gray-900 mb-6">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Loan Detail "{{ object.particulars }}" with Credit Amount "{{ object.credit_amt }}"?</p>

    <div class="mt-6 flex items-center justify-end space-x-4">
        <button
            type="button"
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-300 ease-in-out"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button
            hx-post="{% url 'loandetail_delete' master_pk=object.master.pk pk=object.pk %}"
            hx-swap="none"
            type="button"
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
            Delete
        </button>
    </div>
</div>
```

#### 4.5 URLs

URL patterns for the `accounts` application.

```python
# accounts/urls.py
from django.urls import path
from .views import (
    LoanMasterListView, 
    LoanMasterTablePartialView, 
    LoanMasterCreateView, 
    LoanMasterDeleteView,
    LoanMasterSelectView, # For selecting a master and loading details pane
    LoanDetailTablePartialView,
    LoanDetailCreateView,
    LoanDetailDeleteView,
)

urlpatterns = [
    # Loan Master URLs
    path('loanmaster/', LoanMasterListView.as_view(), name='loanmaster_list'),
    path('loanmaster/table/', LoanMasterTablePartialView.as_view(), name='loanmaster_table'),
    path('loanmaster/add/', LoanMasterCreateView.as_view(), name='loanmaster_add'),
    path('loanmaster/delete/<int:pk>/', LoanMasterDeleteView.as_view(), name='loanmaster_delete'),
    path('loanmaster/<int:master_pk>/select/', LoanMasterSelectView.as_view(), name='loanmaster_select'), # For 'HpPerticulars' action

    # Loan Detail URLs (nested under master_pk as they are master-specific)
    path('loanmaster/<int:master_pk>/details/table/', LoanDetailTablePartialView.as_view(), name='loandetail_table'),
    path('loanmaster/<int:master_pk>/details/add/', LoanDetailCreateView.as_view(), name='loandetail_add'),
    path('loanmaster/<int:master_pk>/details/delete/<int:pk>/', LoanDetailDeleteView.as_view(), name='loandetail_delete'),
]
```

#### 4.6 Tests

Comprehensive unit tests for models and integration tests for views. These tests will simulate the data and user interactions to ensure correctness and adherence to business rules.

```python       
# accounts/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from unittest.mock import patch
from django.utils import timezone
from decimal import Decimal

from .models import LoanMaster, LoanDetail
from .forms import LoanMasterForm, LoanDetailForm

# Mock the get_current_context helper function for consistent test data.
# In a real application, you would set up a test user and authenticate them.
def mock_get_current_context(request):
    return 1, 2023, 'testuser'

class LoanMasterModelTest(TestCase):
    """
    Unit tests for the LoanMaster model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data that will not be modified by tests.
        # Manually provide `id` because `managed=False` and `primary_key=True`
        # means Django won't auto-assign it unless it's an Identity column in the DB.
        cls.loan_master1 = LoanMaster.objects.create(
            id=101, particulars='Business Loan', comp_id=1, fin_year_id=2023, session_id='testuser',
            sys_date=timezone.now().date(), sys_time=timezone.now().time()
        )
        cls.loan_master2 = LoanMaster.objects.create(
            id=102, particulars='Personal Loan', comp_id=1, fin_year_id=2023, session_id='testuser',
            sys_date=timezone.now().date(), sys_time=timezone.now().time()
        )
        # Create a detail for loan_master1 to test has_details()
        LoanDetail.objects.create(
            id=201, master=cls.loan_master1, particulars='Installment 1', credit_amt=Decimal('100.00'),
            comp_id=1, fin_year_id=2023, session_id='testuser',
            sys_date=timezone.now().date(), sys_time=timezone.now().time()
        )

    def test_loan_master_creation(self):
        """Test that a LoanMaster object is created correctly."""
        obj = LoanMaster.objects.get(id=101)
        self.assertEqual(obj.particulars, 'Business Loan')
        self.assertEqual(obj.comp_id, 1)
        self.assertEqual(obj.fin_year_id, 2023)
        self.assertEqual(obj.session_id, 'testuser')
        self.assertEqual(obj.sys_date, timezone.now().date())

    def test_str_representation(self):
        """Test the __str__ method of LoanMaster."""
        obj = LoanMaster.objects.get(id=101)
        self.assertEqual(str(obj), 'Business Loan')
        
    def test_has_details_method(self):
        """Test the has_details method."""
        self.assertTrue(self.loan_master1.has_details())
        self.assertFalse(self.loan_master2.has_details())

class LoanDetailModelTest(TestCase):
    """
    Unit tests for the LoanDetail model.
    """
    @classmethod
    def setUpTestData(cls):
        cls.loan_master = LoanMaster.objects.create(
            id=101, particulars='Business Loan', comp_id=1, fin_year_id=2023, session_id='testuser',
            sys_date=timezone.now().date(), sys_time=timezone.now().time()
        )
        cls.loan_detail1 = LoanDetail.objects.create(
            id=201, master=cls.loan_master, particulars='Installment 1', credit_amt=Decimal('100.00'),
            comp_id=1, fin_year_id=2023, session_id='testuser',
            sys_date=timezone.now().date(), sys_time=timezone.now().time()
        )

    def test_loan_detail_creation(self):
        """Test that a LoanDetail object is created correctly."""
        obj = LoanDetail.objects.get(id=201)
        self.assertEqual(obj.particulars, 'Installment 1')
        self.assertEqual(obj.credit_amt, Decimal('100.000')) # Decimal field stores with defined precision
        self.assertEqual(obj.master, self.loan_master)
        self.assertEqual(obj.comp_id, 1)

    def test_str_representation(self):
        """Test the __str__ method of LoanDetail."""
        obj = LoanDetail.objects.get(id=201)
        self.assertEqual(str(obj), 'Installment 1 (100.000)')

class LoanMasterFormTest(TestCase):
    """
    Unit tests for the LoanMasterForm.
    """
    def test_form_valid_data(self):
        """Test form with valid data."""
        form = LoanMasterForm(data={'particulars': 'Test Loan'})
        self.assertTrue(form.is_valid())

    def test_form_no_particulars(self):
        """Test form when 'particulars' is missing."""
        form = LoanMasterForm(data={'particulars': ''})
        self.assertFalse(form.is_valid())
        self.assertIn('particulars', form.errors)
        self.assertEqual(form.errors['particulars'], ['Particulars is required.'])

class LoanDetailFormTest(TestCase):
    """
    Unit tests for the LoanDetailForm.
    """
    def test_form_valid_data(self):
        """Test form with valid data."""
        form = LoanDetailForm(data={'particulars': 'Test Detail', 'credit_amt': '123.45'})
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['credit_amt'], Decimal('123.450'))

    def test_form_no_particulars(self):
        """Test form when 'particulars' is missing."""
        form = LoanDetailForm(data={'particulars': '', 'credit_amt': '10.00'})
        self.assertFalse(form.is_valid())
        self.assertIn('particulars', form.errors)
        self.assertEqual(form.errors['particulars'], ['Particulars is required.'])

    def test_form_no_credit_amt(self):
        """Test form when 'credit_amt' is missing."""
        form = LoanDetailForm(data={'particulars': 'Test Detail', 'credit_amt': ''})
        self.assertFalse(form.is_valid())
        self.assertIn('credit_amt', form.errors)
        self.assertEqual(form.errors['credit_amt'], ['Credit amount is required.'])

    def test_form_invalid_credit_amt_format(self):
        """Test form when 'credit_amt' is in an invalid format."""
        form = LoanDetailForm(data={'particulars': 'Test Detail', 'credit_amt': 'abc'})
        self.assertFalse(form.is_valid())
        self.assertIn('credit_amt', form.errors)

    def test_form_zero_credit_amt(self):
        """Test form when 'credit_amt' is zero (as per ASP.NET validation)."""
        form = LoanDetailForm(data={'particulars': 'Test Detail', 'credit_amt': '0'})
        self.assertFalse(form.is_valid())
        self.assertIn('credit_amt', form.errors)
        self.assertEqual(form.errors['credit_amt'], ['Credit amount cannot be zero.'])

@patch('accounts.views.get_current_context', side_effect=mock_get_current_context)
class LoanMasterViewsTest(TestCase):
    """
    Integration tests for LoanMaster views, including HTMX interactions.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test LoanMaster instances, one with details, one without.
        cls.loan_master_no_details = LoanMaster.objects.create(
            id=1, particulars='Loan A (No Details)', comp_id=1, fin_year_id=2023, session_id='user1',
            sys_date=timezone.now().date(), sys_time=timezone.now().time()
        )
        cls.loan_master_with_details = LoanMaster.objects.create(
            id=2, particulars='Loan B (With Details)', comp_id=1, fin_year_id=2023, session_id='user1',
            sys_date=timezone.now().date(), sys_time=timezone.now().time()
        )
        LoanDetail.objects.create(
            id=1, master=cls.loan_master_with_details, particulars='Detail 1', credit_amt='500.00',
            comp_id=1, fin_year_id=2023, session_id='user1',
            sys_date=timezone.now().date(), sys_time=timezone.now().time()
        )

    def setUp(self):
        self.client = Client()

    def test_list_view_get(self, mock_context):
        """Test the initial GET request for the main list page."""
        response = self.client.get(reverse('loanmaster_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/loanmaster/list.html')
        self.assertIn('loan_masters', response.context)
        self.assertEqual(response.context['loan_masters'].count(), 2)

    def test_table_partial_view_get(self, mock_context):
        """Test HTMX GET request for the master table partial."""
        response = self.client.get(reverse('loanmaster_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/loanmaster/_master_table.html')
        self.assertIn('loan_masters', response.context)
        self.assertEqual(response.context['loan_masters'].count(), 2)

    def test_create_view_get_htmx(self, mock_context):
        """Test HTMX GET request for the add form modal."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('loanmaster_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/loanmaster/_master_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['action'], 'Add')

    def test_create_view_post_htmx_valid(self, mock_context):
        """Test HTMX POST request for valid new master creation."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {'particulars': 'New Test Loan'}
        response = self.client.post(reverse('loanmaster_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX No Content response
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshLoanMasterList')
        self.assertTrue(LoanMaster.objects.filter(particulars='New Test Loan').exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Loan Master added successfully.')
    
    def test_create_view_post_htmx_invalid(self, mock_context):
        """Test HTMX POST request for invalid new master creation."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {'particulars': ''} # Invalid data
        response = self.client.post(reverse('loanmaster_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # HTMX returns form with errors
        self.assertTemplateUsed(response, 'accounts/loanmaster/_master_form.html')
        self.assertIn('form', response.context)
        self.assertFalse(response.context['form'].is_valid())
        self.assertFalse(LoanMaster.objects.filter(particulars='').exists()) # Ensure no object was created

    def test_delete_view_get_htmx_can_delete(self, mock_context):
        """Test HTMX GET request for delete confirmation when master can be deleted."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('loanmaster_delete', args=[self.loan_master_no_details.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/loanmaster/_master_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertTrue(response.context['can_delete'])

    def test_delete_view_get_htmx_cannot_delete(self, mock_context):
        """Test HTMX GET request for delete confirmation when master cannot be deleted."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('loanmaster_delete', args=[self.loan_master_with_details.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/loanmaster/_master_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertFalse(response.context['can_delete']) # Should indicate it cannot be deleted

    def test_delete_view_post_htmx_can_delete(self, mock_context):
        """Test HTMX POST request for valid master deletion."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        pk_to_delete = self.loan_master_no_details.pk
        response = self.client.post(reverse('loanmaster_delete', args=[pk_to_delete]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshLoanMasterList, clearLoanDetailTable')
        self.assertFalse(LoanMaster.objects.filter(pk=pk_to_delete).exists()) # Verify object is deleted
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Loan Master deleted successfully.')

    def test_delete_view_post_htmx_cannot_delete(self, mock_context):
        """Test HTMX POST request when attempting to delete master with details."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        pk_to_delete = self.loan_master_with_details.pk
        initial_count = LoanMaster.objects.count()
        response = self.client.post(reverse('loanmaster_delete', args=[pk_to_delete]), **headers)
        # Should return 200 with error message in the re-rendered modal, not 204.
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/loanmaster/_master_confirm_delete.html')
        self.assertTrue(LoanMaster.objects.filter(pk=pk_to_delete).exists()) # Object should not be deleted
        self.assertEqual(LoanMaster.objects.count(), initial_count)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Cannot delete Loan Master with associated Loan Details.')


@patch('accounts.views.get_current_context', side_effect=mock_get_current_context)
class LoanDetailViewsTest(TestCase):
    """
    Integration tests for LoanDetail views, including HTMX interactions.
    """
    @classmethod
    def setUpTestData(cls):
        cls.loan_master = LoanMaster.objects.create(
            id=1, particulars='Loan A', comp_id=1, fin_year_id=2023, session_id='user1',
            sys_date=timezone.now().date(), sys_time=timezone.now().time()
        )
        cls.loan_detail_to_delete = LoanDetail.objects.create(
            id=10, master=cls.loan_master, particulars='Detail X', credit_amt='100.00',
            comp_id=1, fin_year_id=2023, session_id='user1',
            sys_date=timezone.now().date(), sys_time=timezone.now().time()
        )

    def setUp(self):
        self.client = Client()

    def test_loan_master_select_view(self, mock_context):
        """Test GET request for selecting a master and loading its detail pane."""
        response = self.client.get(reverse('loanmaster_select', args=[self.loan_master.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/loandetail/_detail_pane.html')
        self.assertIn('loan_master', response.context)
        self.assertIn('loan_details', response.context)
        self.assertIn('loan_master_id', response.context)
        self.assertIn('loan_detail_form', response.context)
        self.assertFalse(response.context['no_data_found']) # Should be false as there's a detail

    def test_loandetail_table_partial_view(self, mock_context):
        """Test HTMX GET request for the detail table partial."""
        response = self.client.get(reverse('loandetail_table', args=[self.loan_master.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/loandetail/_detail_table.html')
        self.assertIn('loan_details', response.context)
        self.assertIn('loan_master_id', response.context)
        self.assertIn('loan_detail_form', response.context)

    def test_loandetail_create_view_post_htmx_valid(self, mock_context):
        """Test HTMX POST request for valid new detail creation."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {'particulars': 'New Detail', 'credit_amt': '250.75'}
        response = self.client.post(reverse('loandetail_add', args=[self.loan_master.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], f'refreshLoanDetailList-{self.loan_master.pk}, refreshLoanMasterList')
        self.assertTrue(LoanDetail.objects.filter(master=self.loan_master, particulars='New Detail').exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Loan Detail added successfully.')

    def test_loandetail_create_view_post_htmx_invalid(self, mock_context):
        """Test HTMX POST request for invalid new detail creation."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {'particulars': '', 'credit_amt': ''} # Invalid data
        response = self.client.post(reverse('loandetail_add', args=[self.loan_master.pk]), data, **headers)
        self.assertEqual(response.status_code, 200) # HTMX returns table with form errors
        self.assertTemplateUsed(response, 'accounts/loandetail/_detail_table.html')
        self.assertIn('loan_detail_form', response.context)
        self.assertFalse(response.context['loan_detail_form'].is_valid())
        self.assertFalse(LoanDetail.objects.filter(master=self.loan_master, particulars='').exists())

    def test_loandetail_delete_view_get_htmx(self, mock_context):
        """Test HTMX GET request for detail delete confirmation modal."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('loandetail_delete', args=[self.loan_master.pk, self.loan_detail_to_delete.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/loandetail/_detail_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.loan_detail_to_delete)

    def test_loandetail_delete_view_post_htmx(self, mock_context):
        """Test HTMX POST request for valid detail deletion."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        pk_to_delete = self.loan_detail_to_delete.pk
        response = self.client.post(reverse('loandetail_delete', args=[self.loan_master.pk, pk_to_delete]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], f'refreshLoanDetailList-{self.loan_master.pk}, refreshLoanMasterList')
        self.assertFalse(LoanDetail.objects.filter(pk=pk_to_delete).exists()) # Verify object is deleted
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Loan Detail deleted successfully.')

```

### Step 5: HTMX and Alpine.js Integration

The provided templates and views demonstrate robust HTMX integration for dynamic interactions without full page reloads.

*   **HTMX for DataTables:**
    *   `hx-get` on `loanMasterTable-container` and `loanDetailTable-container` is used with `hx-trigger="load, refreshLoanMasterList from:body"` (or `refreshLoanDetailList-<id>`) to fetch and inject the `_master_table.html` and `_detail_table.html` partials.
    *   This ensures the tables are loaded dynamically and refresh automatically upon relevant events.
    *   DataTables JavaScript is initialized within the partial templates (`_master_table.html` and `_detail_table.html`) after they are loaded into the DOM. This ensures DataTables correctly applies its features (searching, sorting, pagination) to the dynamically loaded content.

*   **HTMX for Modals (Add/Delete Forms):**
    *   Buttons like "Add New Loan Master" and "Delete" have `hx-get` attributes to fetch the respective form/confirmation partials (`_master_form.html`, `_master_confirm_delete.html`, `_detail_confirm_delete.html`).
    *   These partials are loaded into a shared `#modalContent` target, which is part of a hidden `#modal` div.
    *   The `_` (Alpine.js) syntax `on click add .is-active to #modal` is used to show the modal upon button click.
    *   Form submissions (`hx-post`) from within modals use `hx-swap="none"` and the Django view responds with `status=204` and `HX-Trigger` headers to close the modal and refresh the relevant tables.

*   **HTMX for Inter-Table Interaction:**
    *   Clicking a Loan Master's `Particulars` (`loanmaster_select` URL) uses `hx-get` to load the `_detail_pane.html` into `#loanDetailPane-container`, dynamically displaying the details for the selected master.

*   **Alpine.js for UI State:**
    *   The main `#modal` div uses `x-data="{ showModal: false }"` and `x-show="showModal"` for simple declarative visibility control.
    *   The `_="on click add .is-active to #modal"` and `_="on click remove .is-active from me"` (`#modal`) or `#modalContent` simplify showing/hiding the modal, replacing complex JavaScript logic.
    *   Combined with HTMX's `htmx:afterSwap` and `htmx:beforeSwap` events, Alpine.js ensures smooth transitions and cleanup of the modal.

*   **DRY Templates:**
    *   The design uses `_master_table.html`, `_master_form.html`, `_master_confirm_delete.html`, `_detail_pane.html`, `_detail_table.html`, and `_detail_confirm_delete.html` as partials. This follows DRY principles by reusing these components and loading them dynamically.

*   **No Custom JavaScript (beyond DataTables init):**
    *   All dynamic interactions are driven by HTMX and Alpine.js, minimizing the need for custom JavaScript files (`loadingNotifier.js`, `PopUpMsg.js` are no longer needed).

## Final Notes

*   **Authentication and Context:** The `get_current_context` helper function is a placeholder. In a real ERP system, `CompId`, `FinYearId`, and `SessionId` (username) would be derived from the authenticated user (`request.user`) and potentially session data managed by custom authentication backend or middleware.
*   **Error Handling:** While Django forms and views handle validation and error messages, for HTMX interactions, ensuring graceful error display within partials (as shown in the `form_invalid` methods) is crucial for user experience.
*   **Database ID Generation:** The `id = models.IntegerField(db_column='Id', primary_key=True)` assumes the existing database handles `Id` generation as an auto-incrementing identity column. If `Id` was manually assigned, Django's ORM would need to be adapted, typically by fetching `MAX(Id) + 1` from the database.
*   **Front-end Libraries:** Assumes jQuery, DataTables, HTMX, and Alpine.js are properly included in `core/base.html` via CDN or local static files.
*   **Tailwind CSS:** All generated HTML includes Tailwind CSS classes for styling, adhering to the modernization guidelines.

This detailed plan and code provide a clear, automated path for migrating the specified ASP.NET functionality to a modern Django application, focusing on efficiency, maintainability, and a responsive user experience.