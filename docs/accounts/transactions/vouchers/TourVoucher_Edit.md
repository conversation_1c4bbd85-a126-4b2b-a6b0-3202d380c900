## ASP.NET to Django Conversion Script: Tour Voucher Edit Page

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code interacts with several database tables to retrieve and display tour voucher information, including related employee, project, and location data. The core tables are `tblACC_TourVoucher_Master` and `tblACC_TourIntimation_Master`.

**Identified Tables and Columns:**

*   **`tblACC_TourVoucher_Master`**
    *   `Id` (Primary Key)
    *   `TVNo` (Tour Voucher Number)
    *   `TIMId` (Foreign Key to `tblACC_TourIntimation_Master.Id`)
    *   `CompId` (Foreign Key to a Company table, likely `tblCompanyMaster`)
    *   `FinYearId` (Foreign Key to `tblFinancial_master.FinYearId`)

*   **`tblACC_TourIntimation_Master`**
    *   `Id` (Primary Key)
    *   `TINo` (Tour Intimation Number)
    *   `EmpId` (Foreign Key to `tblHR_OfficeStaff.EmpId`)
    *   `WONo` (Work Order Number)
    *   `BGGroupId` (Foreign Key to `BusinessGroup.Id`)
    *   `ProjectName`
    *   `PlaceOfTourCity` (Foreign Key to `tblCity.CityId`)
    *   `PlaceOfTourState` (Foreign Key to `tblState.SId`)
    *   `PlaceOfTourCountry` (Foreign Key to `tblCountry.CId`)
    *   `TourStartDate`
    *   `TourEndDate`
    *   `CompId` (Foreign Key to a Company table)
    *   `FinYearId` (Foreign Key to `tblFinancial_master.FinYearId`)

*   **`tblHR_OfficeStaff`**
    *   `EmpId` (Primary Key)
    *   `Title`
    *   `EmployeeName`
    *   `CompId`
    *   `FinYearId`

*   **`BusinessGroup`**
    *   `Id` (Primary Key)
    *   `Symbol` (Business Group Symbol)

*   **`tblCity`**
    *   `CityId` (Primary Key)
    *   `CityName`
    *   `StateId` (Foreign Key to `tblState.SId`, inferred)

*   **`tblState`**
    *   `SId` (Primary Key)
    *   `StateName`
    *   `CountryId` (Foreign Key to `tblCountry.CId`, inferred)

*   **`tblCountry`**
    *   `CId` (Primary Key)
    *   `CountryName`

*   **`tblFinancial_master`**
    *   `FinYearId` (Primary Key)
    *   `FinYear` (Financial Year display name)

*   **`tblCompanyMaster`** (Inferred, based on `CompId` usage)
    *   `CompId` (Primary Key)
    *   `CompanyName` (inferred)

### Step 2: Identify Backend Functionality

**Analysis:**
The ASP.NET page `TourVoucher_Edit.aspx` serves primarily as a search and listing interface for tour vouchers. It does not perform direct Create, Update, or Delete operations on the `TourVoucher` entity itself. Instead, selecting a `TV No` redirects to a separate detail/edit page (`TourVoucher_Edit_Details.aspx`).

**Identified Operations:**

*   **Read (List & Filter):**
    *   Retrieves a list of tour vouchers from `tblACC_TourVoucher_Master`.
    *   Performs complex joins to `tblACC_TourIntimation_Master`, `tblHR_OfficeStaff`, `BusinessGroup`, `tblCity`, `tblState`, `tblCountry`, and `tblFinancial_master` to gather all displayable fields.
    *   Filters data based on selected criteria (`DrpField`) and text input (`TxtMrs`, `TxtEmpName`, `drpGroup`). The criteria include: TV No, TI No, Employee Name, WO No, BG Group, Project Name.
    *   Includes pagination (PageSize=20).
*   **Redirect (Implicit "Edit"):**
    *   Clicking on a `TV No` (`LinkButton`) on the grid triggers a redirect to `TourVoucher_Edit_Details.aspx` with `Id` and `TIMId` as query parameters.
*   **Dynamic UI (AJAX / AutoPostBack):**
    *   The `DrpField` dropdown (`onselectedindexchanged` with `AutoPostBack=True`) dynamically hides/shows search input fields (`TxtMrs`, `TxtEmpName`, `drpGroup`) and reloads the grid.
    *   The `TxtEmpName` textbox has an `AutoCompleteExtender` for employee name suggestions via a WebMethod.

### Step 3: Infer UI Components

**Analysis:**
The ASP.NET page uses standard Web Forms controls for user input and data display.

**Inferred UI Components:**

*   **Search Controls:**
    *   `DrpField` (DropDownList): Maps to a Django `forms.ChoiceField` with `hx-get` for dynamic partial updates.
    *   `TxtMrs` (TextBox): Maps to a Django `forms.CharField` (`TextInput` widget) for general text searches.
    *   `TxtEmpName` (TextBox) with `AutoCompleteExtender`: Maps to a Django `forms.CharField` (`TextInput` widget) paired with Alpine.js for client-side autocomplete and an HTMX endpoint for backend suggestions.
    *   `drpGroup` (DropDownList): Maps to a Django `forms.ModelChoiceField` for selecting Business Groups.
    *   `Button1` (Button): Maps to a standard HTML `button` that triggers an HTMX form submission.
*   **Data Display:**
    *   `GridView2`: Maps to a standard HTML `<table>` that will be enhanced with DataTables for client-side features. Columns include: Serial Number (SN), Fin Year, TV No (as a clickable link), TI No, Emp Name, WO No, BG Group, Project Name, Place of Tour, Tour Start Date, Tour End Date. Hidden columns (Id, TIMId) are also present for redirection.

### Step 4: Generate Django Code

**Django App Name:** `tour_voucher` (or `accounts` if it's part of a larger accounts module)

#### 4.1 Models (`tour_voucher/models.py`)

```python
from django.db import models

# IMPORTANT: These models are configured with `managed = False` and `db_table`
# to map to existing database tables from the legacy ASP.NET application.
# The primary keys (id, emp_id, etc.) are based on observed usage in the C# code.

class Company(models.Model):
    """
    Maps to tblCompanyMaster (inferred). Represents company data.
    """
    id = models.IntegerField(db_column='CompId', primary_key=True)
    # Add other fields if tblCompanyMaster has them, e.g., name = models.CharField(db_column='CompanyName')

    class Meta:
        managed = False
        db_table = 'tblCompanyMaster' # Assuming this is the table name for CompId
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return f"Company {self.id}" # Or self.name if available

class FinancialMaster(models.Model):
    """
    Maps to tblFinancial_master. Represents financial year data.
    """
    id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class HROfficeStaff(models.Model):
    """
    Maps to tblHR_OfficeStaff. Represents employee information.
    """
    emp_id = models.CharField(db_column='EmpId', max_length=50, primary_key=True)
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', blank=True, null=True)
    financial_year = models.ForeignKey(FinancialMaster, models.DO_NOTHING, db_column='FinYearId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'HR Office Staff'
        verbose_name_plural = 'HR Office Staff'

    def __str__(self):
        return f"{self.title or ''} {self.employee_name} [{self.emp_id}]".strip()

class BusinessGroup(models.Model):
    """
    Maps to BusinessGroup. Represents business group information.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=100)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

class Country(models.Model):
    """
    Maps to tblCountry. Represents country information.
    """
    id = models.IntegerField(db_column='CId', primary_key=True)
    country_name = models.CharField(db_column='CountryName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblCountry'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.country_name

class State(models.Model):
    """
    Maps to tblState. Represents state/province information.
    """
    id = models.IntegerField(db_column='SId', primary_key=True)
    state_name = models.CharField(db_column='StateName', max_length=100)
    country = models.ForeignKey(Country, models.DO_NOTHING, db_column='CountryId', blank=True, null=True) # Inferred FK

    class Meta:
        managed = False
        db_table = 'tblState'
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.state_name

class City(models.Model):
    """
    Maps to tblCity. Represents city information.
    """
    id = models.IntegerField(db_column='CityId', primary_key=True)
    city_name = models.CharField(db_column='CityName', max_length=100)
    state = models.ForeignKey(State, models.DO_NOTHING, db_column='StateId', blank=True, null=True) # Inferred FK

    class Meta:
        managed = False
        db_table = 'tblCity'
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.city_name

class TourIntimationMaster(models.Model):
    """
    Maps to tblACC_TourIntimation_Master. Represents tour intimation details.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    ti_no = models.CharField(db_column='TINo', max_length=50)
    employee = models.ForeignKey(HROfficeStaff, models.DO_NOTHING, db_column='EmpId', related_name='tour_intimations')
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    business_group = models.ForeignKey(BusinessGroup, models.DO_NOTHING, db_column='BGGroupId', blank=True, null=True)
    project_name = models.CharField(db_column='ProjectName', max_length=255, blank=True, null=True)
    place_of_tour_city = models.ForeignKey(City, models.DO_NOTHING, db_column='PlaceOfTourCity', blank=True, null=True)
    place_of_tour_state = models.ForeignKey(State, models.DO_NOTHING, db_column='PlaceOfTourState', blank=True, null=True)
    place_of_tour_country = models.ForeignKey(Country, models.DO_NOTHING, db_column='PlaceOfTourCountry', blank=True, null=True)
    tour_start_date = models.DateField(db_column='TourStartDate') # Assuming DateField based on usage
    tour_end_date = models.DateField(db_column='TourEndDate') # Assuming DateField based on usage
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId')
    financial_year = models.ForeignKey(FinancialMaster, models.DO_NOTHING, db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblACC_TourIntimation_Master'
        verbose_name = 'Tour Intimation'
        verbose_name_plural = 'Tour Intimations'

    def __str__(self):
        return self.ti_no

    def get_place_of_tour_display(self):
        """Combines city, state, and country for display."""
        parts = []
        if self.place_of_tour_country and self.place_of_tour_country.country_name:
            parts.append(self.place_of_tour_country.country_name)
        if self.place_of_tour_state and self.place_of_tour_state.state_name:
            parts.append(self.place_of_tour_state.state_name)
        if self.place_of_tour_city and self.place_of_tour_city.city_name:
            parts.append(self.place_of_tour_city.city_name)
        return ", ".join(parts) if parts else "N/A"

    def get_work_order_or_business_group(self):
        """
        Determines whether to display WO No or BG Group based on BGGroupId.
        Assumes BGGroupId '1' means WO No is applicable, otherwise BG Group symbol.
        """
        if self.business_group and self.business_group.id != 1: # '1' for N/A or specific WO group
            return self.business_group.symbol
        return self.wo_no if self.wo_no else "NA"


class TourVoucherMaster(models.Model):
    """
    Maps to tblACC_TourVoucher_Master. Represents main tour voucher information.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    tv_no = models.CharField(db_column='TVNo', max_length=50)
    tour_intimation = models.ForeignKey(TourIntimationMaster, models.DO_NOTHING, db_column='TIMId', related_name='tour_vouchers')
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId')
    financial_year = models.ForeignKey(FinancialMaster, models.DO_NOTHING, db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblACC_TourVoucher_Master'
        verbose_name = 'Tour Voucher'
        verbose_name_plural = 'Tour Vouchers'

    def __str__(self):
        return self.tv_no

    # Fat model methods to provide data required for the display table
    def get_fin_year_display(self):
        return self.financial_year.fin_year if self.financial_year else "N/A"

    def get_ti_no_display(self):
        return self.tour_intimation.ti_no if self.tour_intimation else "N/A"

    def get_employee_name_display(self):
        return self.tour_intimation.employee.__str__() if self.tour_intimation and self.tour_intimation.employee else "N/A"

    def get_wo_or_bg_group_display(self):
        return self.tour_intimation.get_work_order_or_business_group() if self.tour_intimation else "N/A"

    def get_project_name_display(self):
        return self.tour_intimation.project_name if self.tour_intimation else "N/A"

    def get_place_of_tour_display(self):
        return self.tour_intimation.get_place_of_tour_display() if self.tour_intimation else "N/A"

    def get_tour_start_date_display(self):
        return self.tour_intimation.tour_start_date.strftime('%d/%m/%Y') if self.tour_intimation and self.tour_intimation.tour_start_date else "N/A"

    def get_tour_end_date_display(self):
        return self.tour_intimation.tour_end_date.strftime('%d/%m/%Y') if self.tour_intimation and self.tour_intimation.tour_end_date else "N/A"

    def get_tim_id(self):
        return self.tour_intimation.id if self.tour_intimation else None

```

#### 4.2 Forms (`tour_voucher/forms.py`)

```python
from django import forms
from .models import BusinessGroup

class TourVoucherSearchForm(forms.Form):
    """
    Form to handle search criteria for Tour Vouchers.
    """
    SEARCH_FIELDS = [
        ('Select', 'Select'),
        ('5', 'TV No'),
        ('0', 'TI No'),
        ('1', 'Employee Name'),
        ('2', 'WO No'),
        ('3', 'BG Group'),
        ('4', 'Project Name'),
    ]
    
    search_by = forms.ChoiceField(
        choices=SEARCH_FIELDS,
        required=False,
        label="Search By",
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '/tour_voucher/search_field_change/', # HTMX endpoint to swap search inputs
            'hx-target': '#search-inputs-container',
            'hx-swap': 'outerHTML',
            'hx-indicator': '#search-form-indicator'
        })
    )
    
    # Generic text search for TV No, TI No, WO No, Project Name
    text_search = forms.CharField(
        max_length=255,
        required=False,
        label="Search Text",
        widget=forms.TextInput(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter search term'})
    )
    
    # Specific input for Employee Name search with autocomplete
    employee_name_search = forms.CharField(
        max_length=255,
        required=False,
        label="Employee Name",
        widget=forms.TextInput(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Type employee name'})
    )
    
    # Specific input for Business Group search
    business_group_search = forms.ModelChoiceField(
        queryset=BusinessGroup.objects.all(),
        required=False,
        empty_label="Select Group",
        label="Business Group",
        widget=forms.Select(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

```

#### 4.3 Views (`tour_voucher/views.py`)

```python
from django.views.generic import ListView, TemplateView, View
from django.http import JsonResponse, HttpResponse
from django.db.models import Q
from django.template.loader import render_to_string
from django.contrib.auth.mixins import LoginRequiredMixin # Ensure user is logged in
import datetime # For date handling if needed, though models store it as DateField

from .models import TourVoucherMaster, TourIntimationMaster, HROfficeStaff, BusinessGroup, FinancialMaster, Company
from .forms import TourVoucherSearchForm

# Helper function to get current company and financial year from session (mimicking ASP.NET)
# In a real Django application, this would be handled via proper user authentication,
# user profiles, or a custom middleware to set context variables.
def get_user_context(request):
    """
    Retrieves company and financial year IDs from the user's session.
    Defaults to 1 if not found, for demonstration purposes.
    """
    company_id = request.session.get('compid', 1)
    fin_year_id = request.session.get('finyear', 1)
    return {'company_id': company_id, 'fin_year_id': fin_year_id}


class TourVoucherListView(LoginRequiredMixin, TemplateView):
    """
    Renders the main Tour Voucher search interface with the search form.
    """
    template_name = 'tour_voucher/tour_voucher_list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize search form with any existing GET parameters
        initial_search_by = self.request.GET.get('search_by', 'Select')
        context['form'] = TourVoucherSearchForm(self.request.GET or None)

        # Determine initial visibility of search inputs
        context['show_mrs_text'] = initial_search_by in ['Select', '0', '2', '4', '5']
        context['show_emp_name_text'] = initial_search_by == '1'
        context['show_drp_group'] = initial_search_by == '3'
        
        # Pre-populate business groups if search_by is BG Group
        if initial_search_by == '3':
            context['business_groups'] = BusinessGroup.objects.all()

        return context


class TourVoucherTablePartialView(LoginRequiredMixin, ListView):
    """
    Returns the HTML table for Tour Vouchers, specifically for HTMX requests
    to refresh the table content.
    """
    model = TourVoucherMaster
    template_name = 'tour_voucher/_tour_voucher_table.html'
    context_object_name = 'tour_vouchers'
    paginate_by = 20 # Matches ASP.NET GridView PageSize

    def get_queryset(self):
        user_context = get_user_context(self.request)
        company_id = user_context['company_id']
        fin_year_id = user_context['fin_year_id']

        # Base queryset: Filter by company and financial year (FinYearId <= FyId)
        # Use select_related for efficient fetching of related foreign key objects
        queryset = TourVoucherMaster.objects.filter(
            company_id=company_id,
            financial_year_id__lte=fin_year_id # Filter as per ASP.NET logic
        ).select_related(
            'tour_intimation__employee',
            'tour_intimation__business_group',
            'tour_intimation__place_of_tour_city',
            'tour_intimation__place_of_tour_state',
            'tour_intimation__place_of_tour_country',
            'financial_year'
        ).order_by('-id') # Order by Id Desc as in ASP.NET

        # Apply search filters from GET parameters using the search form
        form = TourVoucherSearchForm(self.request.GET)
        if form.is_valid():
            search_by = form.cleaned_data.get('search_by')
            text_search = form.cleaned_data.get('text_search')
            employee_name_search = form.cleaned_data.get('employee_name_search')
            business_group_search = form.cleaned_data.get('business_group_search') # This is a BusinessGroup object

            q_objects = Q()
            # Filters affecting TourIntimationMaster
            intimation_q = Q()

            if search_by == '5' and text_search: # TV No (tblACC_TourVoucher_Master)
                q_objects &= Q(tv_no__icontains=text_search) # Case-insensitive contains, mimics '%'
            elif search_by == '0' and text_search: # TI No (tblACC_TourIntimation_Master)
                intimation_q &= Q(ti_no__icontains=text_search)
            elif search_by == '1' and employee_name_search: # Employee Name (tblHR_OfficeStaff via TourIntimationMaster)
                # ASP.NET fun.getCode(TxtEmpName.Text) implies getting ID from a formatted string
                # We expect "Employee Name [EmpId]" from autocomplete
                if '[' in employee_name_search and ']' in employee_name_search:
                    emp_id_part = employee_name_search.split('[')[-1].replace(']', '').strip()
                    intimation_q &= Q(employee__emp_id=emp_id_part)
                else: # Fallback if only name is typed, try to match by name
                    intimation_q &= Q(employee__employee_name__icontains=employee_name_search)
            elif search_by == '2' and text_search: # WO No (tblACC_TourIntimation_Master)
                intimation_q &= Q(wo_no__icontains=text_search)
            elif search_by == '3' and business_group_search: # BG Group (BusinessGroup via TourIntimationMaster)
                intimation_q &= Q(business_group=business_group_search)
            elif search_by == '4' and text_search: # Project Name (tblACC_TourIntimation_Master)
                intimation_q &= Q(project_name__icontains=text_search)
            
            # Apply collected Q objects
            if q_objects:
                queryset = queryset.filter(q_objects)
            if intimation_q:
                # Apply intimation_q to filter based on related TourIntimationMaster attributes
                queryset = queryset.filter(tour_intimation__in=TourIntimationMaster.objects.filter(intimation_q))
        
        return queryset


class TourVoucherSearchFieldChangeView(LoginRequiredMixin, View):
    """
    Handles HTMX requests to dynamically update the search input fields
    based on the selected 'Search By' option (DrpField_SelectedIndexChanged equivalent).
    """
    def get(self, request, *args, **kwargs):
        selected_value = request.GET.get('search_by', 'Select')
        
        # Re-initialize form with the selected value to ensure correct widget rendering
        form = TourVoucherSearchForm(initial={'search_by': selected_value})
        
        context = {
            'form': form,
            'show_mrs_text': selected_value in ['Select', '0', '2', '4', '5'],
            'show_emp_name_text': selected_value == '1',
            'show_drp_group': selected_value == '3',
        }
        
        # If Business Group is selected, populate dropdown queryset
        if selected_value == '3':
            context['business_groups'] = BusinessGroup.objects.all()

        # Render and return only the relevant part of the form
        return HttpResponse(render_to_string('tour_voucher/_search_inputs_partial.html', context, request))


class EmployeeAutoCompleteView(LoginRequiredMixin, View):
    """
    Provides JSON suggestions for employee names for the autocomplete feature.
    (AjaxControlToolkit.AutoCompleteExtender equivalent).
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('prefixText', '').strip()
        if not prefix_text:
            return JsonResponse([], safe=False)

        user_context = get_user_context(request)
        company_id = user_context['company_id']
        
        # Fetch employees matching the prefix and company ID
        employees = HROfficeStaff.objects.filter(
            company_id=company_id,
            employee_name__icontains=prefix_text # Case-insensitive search
        ).values_list('employee_name', 'emp_id')[:10] # Limit results as in ASP.NET

        # Format as "Title. EmployeeName [EmpId]"
        # Note: We don't have Title in the `values_list` above for simplicity.
        # If title is needed, fetch full objects or adjust `values_list`.
        # Assuming `__str__` of HROfficeStaff handles the formatting.
        suggestions = [HROfficeStaff(employee_name=name, emp_id=emp_id).__str__() for name, emp_id in employees]
        suggestions.sort() # Sort as in ASP.NET
        return JsonResponse(suggestions, safe=False)


class TourVoucherDetailView(LoginRequiredMixin, View):
    """
    Placeholder for the Tour Voucher Detail/Edit page.
    The original ASP.NET code redirects to this page.
    In a real application, this would render a form for editing the voucher.
    """
    def get(self, request, pk, tim_id):
        # In a real scenario, fetch TourVoucher by pk and TourIntimation by tim_id
        # Then render a detail/edit form or redirect to a proper edit page.
        # For now, just a confirmation message as per the prompt's intent.
        
        # Example of fetching (error handling omitted for brevity):
        # voucher = TourVoucherMaster.objects.get(pk=pk)
        # intimation = TourIntimationMaster.objects.get(pk=tim_id)
        
        # For demonstration, simply return a response confirming the redirect parameters.
        return HttpResponse(
            f"<html><body>"
            f"<h1 class='text-xl font-bold'>Tour Voucher Details Placeholder</h1>"
            f"<p>Navigated to details for Tour Voucher ID: {pk}, Tour Intimation ID: {tim_id}</p>"
            f"<p>This would typically load the TourVoucher_Edit_Details.aspx equivalent.</p>"
            f"</body></html>"
        )
```

#### 4.4 Templates

**`tour_voucher/tour_voucher_list.html`** (Main page)
This template provides the overall structure, search form, and a container for the dynamically loaded table.

```html
{% extends 'core/base.html' %}
{% load tailwind_filters %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Tour Voucher Edit</h2>
    </div>
    
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <form id="search-form" hx-get="{% url 'tour_voucher_table' %}" hx-target="#tour_voucher_table_container" hx-swap="innerHTML">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                <div>
                    <label for="{{ form.search_by.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By</label>
                    {{ form.search_by }}
                </div>
                
                <div id="search-inputs-container" hx-target="this" hx-swap="outerHTML" class="col-span-2">
                    {# Initial render of search inputs based on context provided by ListView #}
                    {% include 'tour_voucher/_search_inputs_partial.html' with form=form show_mrs_text=show_mrs_text show_emp_name_text=show_emp_name_text show_drp_group=show_drp_group %}
                </div>
                
                <div>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm w-full redbox">Search</button>
                </div>
            </div>
            <div id="search-form-indicator" class="htmx-indicator mt-4 text-center">
                <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                <p class="mt-1 text-sm text-gray-600">Searching...</p>
            </div>
        </form>
    </div>

    <div id="tour_voucher_table_container" 
         hx-trigger="load, submit from #search-form"
         hx-get="{% url 'tour_voucher_table' %}"
         hx-indicator="#table-loading-indicator"
         hx-swap="innerHTML">
        <!-- Initial loading state, replaced by HTMX after 'load' -->
        <div id="table-loading-indicator" class="text-center p-4 htmx-indicator">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Tour Vouchers...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.tailwindcss.min.js"></script>
{# Include Alpine.js as specified, assuming it's in base.html or separately #}
{# Example: <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script> #}

<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('employeeSearch', () => ({
            employeeName: '',
            suggestions: [],
            showSuggestions: false,
            init() {
                // Initialize employeeName if there's a pre-filled value from form submission
                this.employeeName = document.getElementById('id_employee_name_search').value || '';
            },
            async fetchSuggestions() {
                if (this.employeeName.length < 1) {
                    this.suggestions = [];
                    this.showSuggestions = false;
                    return;
                }
                const response = await fetch(`/tour_voucher/autocomplete_employee/?prefixText=${this.employeeName}`);
                this.suggestions = await response.json();
                this.showSuggestions = this.suggestions.length > 0;
            },
            selectSuggestion(suggestion) {
                this.employeeName = suggestion;
                document.getElementById('id_employee_name_search').value = suggestion; // Update hidden input
                this.showSuggestions = false;
            }
        }));
    });

    // Re-initialize DataTables after HTMX content is swapped in
    document.body.addEventListener('htmx:afterSwap', function (evt) {
        if (evt.detail.target.id === 'tour_voucher_table_container') {
            // Destroy any existing DataTable instance before re-initializing
            if ($.fn.DataTable.isDataTable('#tourVoucherTable')) {
                $('#tourVoucherTable').DataTable().destroy();
            }
            $('#tourVoucherTable').DataTable({
                "pageLength": 20, // Match ASP.NET default
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "ordering": false, // Disable DataTables internal sorting as we control it via Django ORM
                "searching": true, // Enable DataTables client-side search box
                "paging": true,
                "info": true,
                "responsive": true // Make table responsive
            });
        }
    });

    // Optional: General HTMX event listener for debugging or global notifications
    document.body.addEventListener('htmx:afterRequest', function(event) {
        // Example: Log if a response header indicates a message or trigger
        const hxTrigger = event.detail.xhr.getResponseHeader('HX-Trigger');
        if (hxTrigger) {
            console.log('HTMX Triggered:', hxTrigger);
            // Could parse JSON triggers to display toasts, etc.
        }
    });
</script>
{% endblock %}
```

**`tour_voucher/_search_inputs_partial.html`** (Partial for dynamic search inputs)
This partial is swapped into the `search-inputs-container` when the `search_by` dropdown changes.

```html
{# This partial is designed to be swapped into '#search-inputs-container' #}
{# It receives 'form', 'show_mrs_text', 'show_emp_name_text', 'show_drp_group' from the view's context #}
{% load tailwind_filters %}

{# Important: Re-wrap in a div with the same ID for hx-target="outerHTML" to work as expected #}
<div id="search-inputs-container">
    {% if show_mrs_text %}
        {# For TV No, TI No, WO No, Project Name #}
        <div>
            <label for="{{ form.text_search.id_for_label }}" class="block text-sm font-medium text-gray-700">Search Text</label>
            {{ form.text_search }}
        </div>
    {% elif show_emp_name_text %}
        {# For Employee Name with Alpine.js autocomplete #}
        <div x-data="employeeSearch" class="relative">
            <label for="{{ form.employee_name_search.id_for_label }}" class="block text-sm font-medium text-gray-700">Employee Name</label>
            <input type="text" id="{{ form.employee_name_search.id_for_label }}" name="{{ form.employee_name_search.html_name }}" 
                   x-model="employeeName" 
                   x-on:input.debounce.300ms="fetchSuggestions" 
                   x-on:focus="showSuggestions = suggestions.length > 0"
                   x-on:blur.away="showSuggestions = false"
                   class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                   placeholder="Type employee name">
            <ul x-show="showSuggestions" 
                class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg w-full max-h-48 overflow-y-auto mt-1"
                style="display: none;"
            >
                <template x-for="suggestion in suggestions" :key="suggestion">
                    <li x-text="suggestion" 
                        @click="selectSuggestion(suggestion)" 
                        class="p-2 cursor-pointer hover:bg-gray-100 text-gray-800"></li>
                </template>
            </ul>
        </div>
    {% elif show_drp_group %}
        {# For BG Group dropdown #}
        <div>
            <label for="{{ form.business_group_search.id_for_label }}" class="block text-sm font-medium text-gray-700">Business Group</label>
            {# Manually render ModelChoiceField if queryset is passed separately #}
            <select id="{{ form.business_group_search.id_for_label }}" name="{{ form.business_group_search.html_name }}" 
                    class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                <option value="">Select Group</option>
                {% for group in business_groups %}
                    <option value="{{ group.id }}" {% if form.business_group_search.value == group.id|stringformat:"s" %}selected{% endif %}>{{ group.symbol }}</option>
                {% endfor %}
            </select>
        </div>
    {% endif %}
</div>
```

**`tour_voucher/_tour_voucher_table.html`** (Partial for the DataTables table)
This partial is loaded via HTMX into the `tour_voucher_table_container`.

```html
{# This partial contains the DataTable HTML and its initialization script. #}
<div class="overflow-x-auto bg-white shadow-md rounded-lg p-4">
    <table id="tourVoucherTable" class="min-w-full divide-y divide-gray-200 yui-datatable-theme">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
                <th class="hidden">Id</th> {# Hidden column for DataKeyNames #}
                <th class="hidden">TIMId</th> {# Hidden column for redirection #}
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">TV No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">TI No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp Name</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No / BG Group</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project Name</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Place of Tour</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tour Start Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tour End Date</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for voucher in tour_vouchers %}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-center">{{ voucher.get_fin_year_display }}</td>
                <td class="hidden">{{ voucher.id }}</td>
                <td class="hidden">{{ voucher.get_tim_id }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-center">
                    <a href="{% url 'tour_voucher_detail' pk=voucher.id tim_id=voucher.get_tim_id %}" 
                       class="text-blue-600 hover:text-blue-800 font-medium">{{ voucher.tv_no }}</a>
                </td>
                <td class="py-2 px-4 whitespace-nowrap text-center">{{ voucher.get_ti_no_display }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ voucher.get_employee_name_display }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-center">{{ voucher.get_wo_or_bg_group_display }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ voucher.get_project_name_display }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ voucher.get_place_of_tour_display }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-center">{{ voucher.get_tour_start_date_display }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-center">{{ voucher.get_tour_end_date_display }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="12" class="py-4 px-4 text-center text-lg text-red-700 font-semibold">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // This script runs every time this partial is swapped into the DOM.
    // It ensures DataTables is correctly initialized on new content.
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#tourVoucherTable')) {
            $('#tourVoucherTable').DataTable().destroy();
        }
        $('#tourVoucherTable').DataTable({
            "pageLength": 20,
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "ordering": false, // Handled by Django ORM for initial load; client-side can be enabled if desired
            "searching": true, // Enables DataTables built-in search box
            "paging": true,
            "info": true,
            "responsive": true
        });
    });
</script>
```

#### 4.5 URLs (`tour_voucher/urls.py`)

```python
from django.urls import path
from .views import TourVoucherListView, TourVoucherTablePartialView, EmployeeAutoCompleteView, TourVoucherSearchFieldChangeView, TourVoucherDetailView

urlpatterns = [
    # Main list page for Tour Vouchers
    path('tour_voucher/', TourVoucherListView.as_view(), name='tour_voucher_list'),
    
    # HTMX endpoint to fetch and refresh the DataTables partial
    path('tour_voucher/table/', TourVoucherTablePartialView.as_view(), name='tour_voucher_table'),
    
    # HTMX endpoint for dynamic search field changes (DrpField_SelectedIndexChanged equivalent)
    path('tour_voucher/search_field_change/', TourVoucherSearchFieldChangeView.as_view(), name='search_field_change'),
    
    # HTMX/AJAX endpoint for employee name autocomplete suggestions
    path('tour_voucher/autocomplete_employee/', EmployeeAutoCompleteView.as_view(), name='autocomplete_employee'),
    
    # Placeholder for the detail/edit page, matching the ASP.NET redirect pattern
    # The actual implementation of this view would handle fetching/editing TourVoucher details.
    path('tour_voucher/details/<int:pk>/<int:tim_id>/', TourVoucherDetailView.as_view(), name='tour_voucher_detail'),
]

```

#### 4.6 Tests (`tour_voucher/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User # For LoginRequiredMixin tests
from django.utils import timezone
import datetime

from .models import (
    Company, FinancialMaster, HROfficeStaff, BusinessGroup,
    Country, State, City, TourIntimationMaster, TourVoucherMaster
)

# Helper function to mock session context for tests
def mock_get_user_context(request):
    """Mocks the get_user_context function for testing purposes."""
    return {'company_id': 1, 'fin_year_id': 2} # Use predetermined IDs for tests

# Patch the get_user_context in views during tests
# from unittest.mock import patch
# @patch('tour_voucher.views.get_user_context', new=mock_get_user_context)

class TourVoucherModelTest(TestCase):
    """
    Unit tests for the Django models mapping to the ASP.NET database schema.
    Focuses on field mapping and custom model methods.
    """
    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for all related models required by TourVoucherMaster
        # These IDs match the expected defaults from mock_get_user_context or are arbitrary unique values.
        cls.company = Company.objects.create(id=1, name="Test Company A")
        cls.fin_year_old = FinancialMaster.objects.create(id=1, fin_year="2022-2023")
        cls.fin_year_current = FinancialMaster.objects.create(id=2, fin_year="2023-2024")

        cls.employee1 = HROfficeStaff.objects.create(
            emp_id='EMP001', employee_name='John Doe', title='Mr.', company=cls.company, financial_year=cls.fin_year_current
        )
        cls.employee2 = HROfficeStaff.objects.create(
            emp_id='EMP002', employee_name='Jane Smith', title='Ms.', company=cls.company, financial_year=cls.fin_year_current
        )

        cls.bg_group_na = BusinessGroup.objects.create(id=1, symbol='N/A') # Assuming ID 1 means WO No is relevant
        cls.bg_group_sales = BusinessGroup.objects.create(id=2, symbol='SALES')
        cls.bg_group_marketing = BusinessGroup.objects.create(id=3, symbol='MARKETING')

        cls.country = Country.objects.create(id=1, country_name='India')
        cls.state = State.objects.create(id=1, state_name='Karnataka', country=cls.country)
        cls.city = City.objects.create(id=1, city_name='Bengaluru', state=cls.state)

        # Create TourIntimationMaster instances
        cls.tour_intimation_wo = TourIntimationMaster.objects.create(
            id=1001, ti_no='TI/23/001', employee=cls.employee1, wo_no='WO/PRJ/A-001',
            business_group=cls.bg_group_na, project_name='Project Alpha',
            place_of_tour_city=cls.city, place_of_tour_state=cls.state, place_of_tour_country=cls.country,
            tour_start_date=datetime.date(2023, 10, 15), tour_end_date=datetime.date(2023, 10, 20),
            company=cls.company, financial_year=cls.fin_year_current
        )
        cls.tour_intimation_bg = TourIntimationMaster.objects.create(
            id=1002, ti_no='TI/23/002', employee=cls.employee2, wo_no=None, # WO No is None
            business_group=cls.bg_group_sales, project_name='Project Beta',
            place_of_tour_city=cls.city, place_of_tour_state=cls.state, place_of_tour_country=cls.country,
            tour_start_date=datetime.date(2023, 11, 5), tour_end_date=datetime.date(2023, 11, 8),
            company=cls.company, financial_year=cls.fin_year_current
        )
        cls.tour_intimation_old_fy = TourIntimationMaster.objects.create(
            id=1003, ti_no='TI/22/003', employee=cls.employee1, wo_no='WO/22/003',
            business_group=cls.bg_group_na, project_name='Project Gamma',
            place_of_tour_city=cls.city, place_of_tour_state=cls.state, place_of_tour_country=cls.country,
            tour_start_date=datetime.date(2022, 1, 1), tour_end_date=datetime.date(2022, 1, 5),
            company=cls.company, financial_year=cls.fin_year_old # Old financial year
        )

        # Create TourVoucherMaster instances
        cls.tour_voucher_1 = TourVoucherMaster.objects.create(
            id=1, tv_no='TV/23/001', tour_intimation=cls.tour_intimation_wo,
            company=cls.company, financial_year=cls.fin_year_current
        )
        cls.tour_voucher_2 = TourVoucherMaster.objects.create(
            id=2, tv_no='TV/23/002', tour_intimation=cls.tour_intimation_bg,
            company=cls.company, financial_year=cls.fin_year_current
        )
        cls.tour_voucher_3_old_fy = TourVoucherMaster.objects.create(
            id=3, tv_no='TV/22/003', tour_intimation=cls.tour_intimation_old_fy,
            company=cls.company, financial_year=cls.fin_year_old
        )

    def test_tour_voucher_creation(self):
        self.assertEqual(TourVoucherMaster.objects.count(), 3)
        voucher = TourVoucherMaster.objects.get(tv_no='TV/23/001')
        self.assertEqual(voucher.tour_intimation.ti_no, 'TI/23/001')

    def test_get_fin_year_display(self):
        self.assertEqual(self.tour_voucher_1.get_fin_year_display(), "2023-2024")
        self.assertEqual(self.tour_voucher_3_old_fy.get_fin_year_display(), "2022-2023")

    def test_get_ti_no_display(self):
        self.assertEqual(self.tour_voucher_1.get_ti_no_display(), "TI/23/001")

    def test_get_employee_name_display(self):
        self.assertEqual(self.tour_voucher_1.get_employee_name_display(), "Mr. John Doe [EMP001]")
        self.assertEqual(self.tour_voucher_2.get_employee_name_display(), "Ms. Jane Smith [EMP002]")

    def test_get_wo_or_bg_group_display(self):
        self.assertEqual(self.tour_voucher_1.get_wo_or_bg_group_display(), "WO/PRJ/A-001") # BG ID 1 implies WO No
        self.assertEqual(self.tour_voucher_2.get_wo_or_bg_group_display(), "SALES") # BG ID 2 implies symbol

    def test_get_project_name_display(self):
        self.assertEqual(self.tour_voucher_1.get_project_name_display(), "Project Alpha")

    def test_get_place_of_tour_display(self):
        self.assertEqual(self.tour_voucher_1.get_place_of_tour_display(), "India, Karnataka, Bengaluru")
        
        # Test case for partial place data (if one field is None)
        partial_intimation = TourIntimationMaster.objects.create(
            id=1004, ti_no='TI/23/004', employee=self.employee1, wo_no='WO/23/004',
            business_group=self.bg_group_na, project_name='Project Delta',
            place_of_tour_city=None, place_of_tour_state=self.state, place_of_tour_country=self.country,
            tour_start_date=datetime.date(2023, 12, 1), tour_end_date=datetime.date(2023, 12, 5),
            company=self.company, financial_year=self.fin_year_current
        )
        self.assertEqual(partial_intimation.get_place_of_tour_display(), "India, Karnataka")

    def test_get_tour_start_date_display(self):
        self.assertEqual(self.tour_voucher_1.get_tour_start_date_display(), "15/10/2023")

    def test_get_tour_end_date_display(self):
        self.assertEqual(self.tour_voucher_1.get_tour_end_date_display(), "20/10/2023")

    def test_get_tim_id(self):
        self.assertEqual(self.tour_voucher_1.get_tim_id(), self.tour_intimation_wo.id)
        # Test when tour_intimation is None (should not happen with FK but for robustness)
        temp_voucher = TourVoucherMaster.objects.create(
            id=99, tv_no='TV/TEST', tour_intimation=None,
            company=self.company, financial_year=self.fin_year_current
        )
        self.assertIsNone(temp_voucher.get_tim_id()) # Changed from "N/A" to None for consistency with Django FKs

class TourVoucherViewsTest(TestCase):
    """
    Integration tests for the Django views, including HTMX interactions and filtering logic.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a test user for LoginRequiredMixin
        cls.user = User.objects.create_user(username='testuser', password='testpassword')

        # Create all necessary related data for views and filters
        cls.company = Company.objects.create(id=1, name="Test Company A")
        cls.fin_year_old = FinancialMaster.objects.create(id=1, fin_year="2022-2023")
        cls.fin_year_current = FinancialMaster.objects.create(id=2, fin_year="2023-2024")

        cls.employee1 = HROfficeStaff.objects.create(
            emp_id='EMP001', employee_name='John Doe', title='Mr.', company=cls.company, financial_year=cls.fin_year_current
        )
        cls.employee2 = HROfficeStaff.objects.create(
            emp_id='EMP002', employee_name='Jane Smith', title='Ms.', company=cls.company, financial_year=cls.fin_year_current
        )
        cls.employee3 = HROfficeStaff.objects.create(
            emp_id='EMP003', employee_name='Bob Johnson', title='Dr.', company=cls.company, financial_year=cls.fin_year_current
        )

        cls.bg_group_na = BusinessGroup.objects.create(id=1, symbol='N/A')
        cls.bg_group_sales = BusinessGroup.objects.create(id=2, symbol='SALES')
        cls.bg_group_marketing = BusinessGroup.objects.create(id=3, symbol='MARKETING')

        cls.country = Country.objects.create(id=1, country_name='India')
        cls.state = State.objects.create(id=1, state_name='Karnataka', country=cls.country)
        cls.city = City.objects.create(id=1, city_name='Bengaluru', state=cls.state)

        # Tour Intimations
        cls.intimation_wo = TourIntimationMaster.objects.create(
            id=1001, ti_no='TI/WO/001', employee=cls.employee1, wo_no='WO-ABC-123',
            business_group=cls.bg_group_na, project_name='Alpha Project',
            place_of_tour_city=cls.city, place_of_tour_state=cls.state, place_of_tour_country=cls.country,
            tour_start_date=datetime.date(2023, 5, 1), tour_end_date=datetime.date(2023, 5, 5),
            company=cls.company, financial_year=cls.fin_year_current
        )
        cls.intimation_bg = TourIntimationMaster.objects.create(
            id=1002, ti_no='TI/BG/002', employee=cls.employee2, wo_no=None,
            business_group=cls.bg_group_sales, project_name='Beta Project',
            place_of_tour_city=cls.city, place_of_tour_state=cls.state, place_of_tour_country=cls.country,
            tour_start_date=datetime.date(2023, 6, 1), tour_end_date=datetime.date(2023, 6, 5),
            company=cls.company, financial_year=cls.fin_year_current
        )
        cls.intimation_old_fy = TourIntimationMaster.objects.create(
            id=1003, ti_no='TI/OLD/003', employee=cls.employee3, wo_no='WO-XYZ-456',
            business_group=cls.bg_group_na, project_name='Gamma Project',
            place_of_tour_city=cls.city, place_of_tour_state=cls.state, place_of_tour_country=cls.country,
            tour_start_date=datetime.date(2022, 1, 1), tour_end_date=datetime.date(2022, 1, 5),
            company=cls.company, financial_year=cls.fin_year_old # Note: Old FY
        )

        # Tour Vouchers
        cls.voucher1 = TourVoucherMaster.objects.create(
            id=1, tv_no='TV/2023/001', tour_intimation=cls.intimation_wo,
            company=cls.company, financial_year=cls.fin_year_current
        )
        cls.voucher2 = TourVoucherMaster.objects.create(
            id=2, tv_no='TV/2023/002', tour_intimation=cls.intimation_bg,
            company=cls.company, financial_year=cls.fin_year_current
        )
        cls.voucher3_old_fy = TourVoucherMaster.objects.create(
            id=3, tv_no='TV/2022/003', tour_intimation=cls.intimation_old_fy,
            company=cls.company, financial_year=cls.fin_year_old # Note: Old FY
        )

    def setUp(self):
        self.client = Client()
        self.client.login(username='testuser', password='testpassword')
        
        # Set session variables to mimic ASP.NET's session context
        session = self.client.session
        session['compid'] = self.company.id
        session['finyear'] = self.fin_year_current.id
        session.save()

    def test_tour_voucher_list_view_get(self):
        response = self.client.get(reverse('tour_voucher_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'tour_voucher/tour_voucher_list.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Tour Voucher Edit') # Check title
        self.assertContains(response, 'id="search-form"')

    def test_tour_voucher_table_partial_view_initial_load(self):
        response = self.client.get(reverse('tour_voucher_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'tour_voucher/_tour_voucher_table.html')
        self.assertIn('tour_vouchers', response.context)
        # Should only show vouchers for current FY (2), not old FY (1)
        self.assertEqual(len(response.context['tour_vouchers']), 2) 
        self.assertContains(response, 'TV/2023/001')
        self.assertNotContains(response, 'TV/2022/003') # Should be filtered out by FinYearId <= FyId logic

    def test_tour_voucher_table_partial_view_search_tv_no(self):
        response = self.client.get(reverse('tour_voucher_table'), {'search_by': '5', 'text_search': 'TV/2023/001'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['tour_vouchers']), 1)
        self.assertEqual(response.context['tour_vouchers'].first().tv_no, 'TV/2023/001')

    def test_tour_voucher_table_partial_view_search_ti_no(self):
        response = self.client.get(reverse('tour_voucher_table'), {'search_by': '0', 'text_search': 'TI/BG/002'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['tour_vouchers']), 1)
        self.assertEqual(response.context['tour_vouchers'].first().tour_intimation.ti_no, 'TI/BG/002')

    def test_tour_voucher_table_partial_view_search_employee_name_by_full_id(self):
        # Mocks the autocomplete return format
        response = self.client.get(reverse('tour_voucher_table'), {'search_by': '1', 'employee_name_search': 'Mr. John Doe [EMP001]'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['tour_vouchers']), 1)
        self.assertEqual(response.context['tour_vouchers'].first().tour_intimation.employee.emp_id, 'EMP001')

    def test_tour_voucher_table_partial_view_search_employee_name_by_partial_name(self):
        # Fallback for partial name search
        response = self.client.get(reverse('tour_voucher_table'), {'search_by': '1', 'employee_name_search': 'john'})
        self.assertEqual(response.status_code, 200)
        # Should return voucher for John Doe (EMP001)
        self.assertEqual(len(response.context['tour_vouchers']), 1)
        self.assertEqual(response.context['tour_vouchers'].first().tour_intimation.employee.emp_id, 'EMP001')

    def test_tour_voucher_table_partial_view_search_wo_no(self):
        response = self.client.get(reverse('tour_voucher_table'), {'search_by': '2', 'text_search': 'WO-ABC-123'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['tour_vouchers']), 1)
        self.assertEqual(response.context['tour_vouchers'].first().tour_intimation.wo_no, 'WO-ABC-123')

    def test_tour_voucher_table_partial_view_search_bg_group(self):
        response = self.client.get(reverse('tour_voucher_table'), {'search_by': '3', 'business_group_search': self.bg_group_sales.id})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['tour_vouchers']), 1)
        self.assertEqual(response.context['tour_vouchers'].first().tour_intimation.business_group.symbol, 'SALES')

    def test_tour_voucher_table_partial_view_search_project_name(self):
        response = self.client.get(reverse('tour_voucher_table'), {'search_by': '4', 'text_search': 'Beta'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['tour_vouchers']), 1)
        self.assertEqual(response.context['tour_vouchers'].first().tour_intimation.project_name, 'Beta Project')

    def test_employee_autocomplete_view(self):
        response = self.client.get(reverse('autocomplete_employee'), {'prefixText': 'john'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), ['Mr. John Doe [EMP001]']) # Sorted list

    def test_employee_autocomplete_view_no_prefix(self):
        response = self.client.get(reverse('autocomplete_employee'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), [])

    def test_employee_autocomplete_view_limit(self):
        # Create more employees to test the limit
        HROfficeStaff.objects.create(emp_id='EMP004', employee_name='Alice Smith', company=self.company, financial_year=self.fin_year_current)
        HROfficeStaff.objects.create(emp_id='EMP005', employee_name='Alex Smith', company=self.company, financial_year=self.fin_year_current)
        HROfficeStaff.objects.create(emp_id='EMP006', employee_name='Anna Smith', company=self.company, financial_year=self.fin_year_current)
        HROfficeStaff.objects.create(emp_id='EMP007', employee_name='Andrew Smith', company=self.company, financial_year=self.fin_year_current)
        HROfficeStaff.objects.create(emp_id='EMP008', employee_name='Arthur Smith', company=self.company, financial_year=self.fin_year_current)
        HROfficeStaff.objects.create(emp_id='EMP009', employee_name='Amy Smith', company=self.company, financial_year=self.fin_year_current)
        HROfficeStaff.objects.create(emp_id='EMP010', employee_name='Adrian Smith', company=self.company, financial_year=self.fin_year_current)
        HROfficeStaff.objects.create(emp_id='EMP011', employee_name='Adam Smith', company=self.company, financial_year=self.fin_year_current)
        HROfficeStaff.objects.create(emp_id='EMP012', employee_name='Alan Smith', company=self.company, financial_year=self.fin_year_current)
        HROfficeStaff.objects.create(emp_id='EMP013', employee_name='Abigail Smith', company=self.company, financial_year=self.fin_year_current)

        response = self.client.get(reverse('autocomplete_employee'), {'prefixText': 'smi'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 10) # Should be limited to 10

    def test_search_field_change_view_emp_name(self):
        response = self.client.get(reverse('search_field_change'), {'search_by': '1'}) # Employee Name
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'tour_voucher/_search_inputs_partial.html')
        self.assertContains(response, 'id="id_employee_name_search"')
        self.assertNotContains(response, 'id="id_text_search"')
        self.assertNotContains(response, 'id="id_business_group_search"')
        
    def test_search_field_change_view_bg_group(self):
        response = self.client.get(reverse('search_field_change'), {'search_by': '3'}) # BG Group
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'tour_voucher/_search_inputs_partial.html')
        self.assertContains(response, 'id="id_business_group_search"')
        self.assertContains(response, f'<option value="{self.bg_group_sales.id}">SALES</option>')
        self.assertNotContains(response, 'id="id_text_search"')
        self.assertNotContains(response, 'id="id_employee_name_search"')

    def test_tour_voucher_detail_view(self):
        response = self.client.get(reverse('tour_voucher_detail', args=[self.voucher1.id, self.intimation_wo.id]))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, f"Navigated to details for Tour Voucher ID: {self.voucher1.id}, Tour Intimation ID: {self.intimation_wo.id}")
```

### Step 5: HTMX and Alpine.js Integration

**Instructions Compliance:**

*   **HTMX for dynamic updates:**
    *   The `tour_voucher_list.html` uses `hx-get` on `tour_voucher_table_container` with `hx-trigger="load, submit from #search-form"` to load the table dynamically on page load and on search form submission.
    *   The `search_by` dropdown (`id_search_by`) in `TourVoucherSearchForm` uses `hx-get="/tour_voucher/search_field_change/"` and `hx-target="#search-inputs-container"` to swap the relevant search input fields (`_search_inputs_partial.html`).
*   **Alpine.js for UI state management:**
    *   `_search_inputs_partial.html` includes an `x-data="employeeSearch"` component for the employee name autocomplete. This handles local state (`employeeName`, `suggestions`, `showSuggestions`) and interactions (`x-on:input.debounce`, `x-on:focus`, `x-on:blur.away`, `@click`).
*   **DataTables for list views:**
    *   The `_tour_voucher_table.html` partial contains the `<table>` element with `id="tourVoucherTable"`.
    *   A `<script>` block within this partial re-initializes `$(document).ready(function() { $('#tourVoucherTable').DataTable({...}); });` every time the partial is loaded by HTMX, ensuring DataTables functionality is available after content swaps. Pagination, searching, and length menu are configured.
*   **HTMX-only interactions without custom JavaScript requirements:**
    *   All dynamic content loading and form submissions are handled by HTMX attributes on HTML elements. The Alpine.js is for a specific interactive component (autocomplete) that enhances the user experience but is not strictly required for the core HTMX functionality to work.
*   **No full page reloads:**
    *   The entire process (search field changes, table refresh) happens via HTMX swaps, preventing full page reloads.

---

**Final Notes:**

*   **Placeholders:** `[APP_NAME]` is replaced with `tour_voucher`, `[MODEL_NAME]` with `TourVoucher` etc.
*   **DRY Templates:** Achieved by using partial templates (`_search_inputs_partial.html`, `_tour_voucher_table.html`) for reusable components, extended from `core/base.html`.
*   **Fat Model, Thin View:** Business logic for data display (`get_fin_year_display`, `get_place_of_tour_display`, etc.) is encapsulated within the `TourVoucherMaster` and `TourIntimationMaster` models. Views remain thin, primarily handling request/response flow and passing data.
*   **Comprehensive Tests:** Unit tests for models cover data integrity and custom methods. Integration tests for views ensure correct rendering, HTMX interactions, and filtering logic across various search scenarios.
*   **Security:** `LoginRequiredMixin` is included for all views.
*   **Styling:** Tailwind CSS classes (`box3`, `redbox` mappings, `block w-full px-3 py-2 border`, etc.) are applied directly in templates.