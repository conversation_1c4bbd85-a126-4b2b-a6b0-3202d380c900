## ASP.NET to Django Conversion Script: Cash Voucher Print Module

This document outlines a strategic plan for transitioning your legacy ASP.NET Cash Voucher Print module to a modern Django-based application. Our approach prioritizes automation, efficient design, and a user-friendly interface using contemporary web technologies.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

This module interacts with several database tables to retrieve and display cash voucher payment and receipt information. The tables and their key columns are identified as follows:

-   **`tblACC_CashVoucher_Payment_Master` (Payment Master):**
    *   `Id` (Primary Key, Integer)
    *   `CVPNo` (Cash Voucher Payment Number, String)
    *   `FinYearId` (Foreign Key to Financial Year, Integer)
    *   `SysDate` (System Date, Date/DateTime)
    *   `PaidTo` (Entity paid to, String)
    *   `CodeType` (Type of entity paid to, Integer - 1: Employee, 2: Customer, 3: Supplier)
    *   `Receivedby` (ID of the entity received by, String/Integer)
    *   `CompId` (Company ID, Integer)
-   **`tblACC_CashVoucher_Payment_Details` (Payment Details):**
    *   `Id` (Primary Key, Integer)
    *   `MId` (Foreign Key to Payment Master ID, Integer)
    *   `Amount` (Amount, Decimal)
-   **`tblACC_CashVoucher_Receipt_Master` (Receipt Master):**
    *   `Id` (Primary Key, Integer)
    *   `CVRNo` (Cash Voucher Receipt Number, String)
    *   `FinYearId` (Foreign Key to Financial Year, Integer)
    *   `SysDate` (System Date, Date/DateTime)
    *   `CashReceivedAgainst` (ID of entity received against, String/Integer)
    *   `CodeTypeRA` (Type of entity received against, Integer)
    *   `CashReceivedBy` (ID of entity received by, String/Integer)
    *   `CodeTypeRB` (Type of entity received by, Integer)
    *   `CompId` (Company ID, Integer)
    *   `Amount` (Total Amount, Decimal - unlike payment, this is directly in master)
-   **`tblFinancial_master` (Financial Master):**
    *   `FinYearId` (Primary Key, Integer)
    *   `FinYear` (Financial Year, String)
    *   `CompId` (Company ID, Integer)
-   **`tblHR_OfficeStaff` (Office Staff):**
    *   `EmpId` (Employee ID, String/Integer)
    *   `Title` (Title, String)
    *   `EmployeeName` (Employee Name, String)
    *   `CompId` (Company ID, Integer)
-   **`SD_Cust_master` (Customer Master):**
    *   `CustomerId` (Customer ID, String/Integer)
    *   `CustomerName` (Customer Name, String)
    *   `CompId` (Company ID, Integer)
-   **`tblMM_Supplier_master` (Supplier Master):**
    *   `SupplierId` (Supplier ID, String/Integer)
    *   `SupplierName` (Supplier Name, String)
    *   `CompId` (Company ID, Integer)

### Step 2: Identify Backend Functionality

The primary functionality of this ASP.NET module is data retrieval and presentation, with specific search and navigation features:

-   **Reading Data:**
    *   Displaying lists of Cash Voucher Payments, filterable by "Paid To" entity.
    *   Displaying lists of Cash Voucher Receipts, filterable by "Cash Rec. Against" entity.
    *   These lists support pagination.
-   **Search & Autocomplete:**
    *   "Paid To" search with autocompletion suggesting previously paid entities.
    *   "Cash Rec. Against" search with autocompletion suggesting employees, customers, or suppliers.
-   **Navigation:**
    *   Selecting an item from either list redirects the user to a detailed print view for that specific voucher.
-   **No Direct CRUD Operations:** This module focuses purely on viewing and printing existing vouchers.

### Step 3: Infer UI Components

The ASP.NET UI utilizes standard web forms controls and AJAX Control Toolkit components, which will be translated into modern HTML structures with HTMX and Alpine.js for dynamic behavior.

-   **Tabbed Interface:** The `AjaxControlToolkit:TabContainer` and `TabPanel` will be replaced by standard HTML `div` elements, with tab switching managed by Alpine.js for a smooth client-side experience.
-   **Search Inputs:** `asp:TextBox` controls for "Paid To" and "Cash Rec. Against" will become standard HTML `input` fields. Autocompletion functionality will be implemented using HTMX.
-   **Search Buttons:** `asp:Button` controls will become standard HTML `button` elements, triggering HTMX requests to refresh the data grids.
-   **Data Grids:** `asp:GridView` controls for both Payment and Receipt lists will be transformed into standard HTML `table` elements. These tables will be powered by DataTables for enhanced client-side features like sorting, pagination, and instant search. HTMX will be used to load and refresh these table sections dynamically.
-   **Action Links:** The "Select" `asp:LinkButton` within the GridView will be converted to HTML `a` tags, triggering a redirect to the detail page.

### Step 4: Generate Django Code

We will create a Django application named `cash_vouchers` to house the components for this module.

#### 4.1 Models

The models will map directly to your existing database tables using `managed = False`. Crucial business logic, such as resolving `FinYearId` to `FinYear` and `CodeType`/`Receivedby` IDs to actual names, will be implemented as properties within the models following the "Fat Model" principle.

```python
# cash_vouchers/models.py
from django.db import models
from django.db.models import Sum
from django.utils.formats import date_format
from django.conf import settings
from django.core.cache import cache

# Helper for Company ID - In a real application, this would come from a user session
# or a specific company context. For demonstration, we use a placeholder from settings.
# Ensure you add GLOBAL_COMP_ID = 1 (or relevant ID) to your settings.py for testing.

class FinancialMaster(models.Model):
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year or ''

class OfficeStaff(models.Model):
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50) 
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title or ''}. {self.employee_name or ''}"

class CustomerMaster(models.Model):
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50) 
    customer_name = models.CharField(db_column='CustomerName', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customer_name or ''} [{self.customer_id or ''}]"

class SupplierMaster(models.Model):
    supplier_id = models.CharField(db_column='SupplierId', primary_key=True, max_length=50)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return f"{self.supplier_name or ''} [{self.supplier_id or ''}]"

class CashVoucherPaymentMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    cvp_no = models.CharField(db_column='CVPNo', max_length=50, blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    sys_date = models.DateTimeField(db_column='SysDate', blank=True, null=True) 
    paid_to = models.CharField(db_column='PaidTo', max_length=255, blank=True, null=True)
    code_type = models.IntegerField(db_column='CodeType', blank=True, null=True)
    received_by_id = models.CharField(db_column='Receivedby', max_length=50, blank=True, null=True) 
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_CashVoucher_Payment_Master'
        verbose_name = 'Cash Voucher Payment'
        verbose_name_plural = 'Cash Voucher Payments'

    def __str__(self):
        return self.cvp_no or ''

    @property
    def formatted_sys_date(self):
        return date_format(self.sys_date, "d-m-Y") if self.sys_date else ''

    @property
    def financial_year(self):
        # This resolves the financial year string based on FinYearId and CompId
        if self.fin_year_id:
            try:
                comp_id_val = self.comp_id or getattr(settings, 'GLOBAL_COMP_ID', None)
                if comp_id_val is not None:
                    financial_master = FinancialMaster.objects.filter(
                        fin_year_id=self.fin_year_id, 
                        comp_id=comp_id_val
                    ).first()
                    return financial_master.fin_year if financial_master else ''
            except FinancialMaster.DoesNotExist:
                pass
        return ''

    def _get_related_entity(self, entity_id, code_type_val, comp_id_val):
        """Helper to get related entity name and type based on code_type."""
        if not entity_id or not code_type_val:
            return {'type': '', 'name': ''}

        # Use cache for performance
        key = f"entity_name_cache_{entity_id}_{code_type_val}_{comp_id_val}"
        cached_data = cache.get(key)
        if cached_data:
            return cached_data

        name = ''
        entity_type_name = ''
        try:
            if code_type_val == 1: # Employee
                entity_type_name = "Employee"
                employee = OfficeStaff.objects.filter(emp_id=entity_id, comp_id=comp_id_val).first()
                if employee:
                    name = f"{employee.title or ''}. {employee.employee_name or ''}"
            elif code_type_val == 2: # Customer
                entity_type_name = "Customer"
                customer = CustomerMaster.objects.filter(customer_id=entity_id, comp_id=comp_id_val).first()
                if customer:
                    name = f"{customer.customer_name or ''}[{customer.customer_id or ''}]"
            elif code_type_val == 3: # Supplier
                entity_type_name = "Supplier"
                supplier = SupplierMaster.objects.filter(supplier_id=entity_id, comp_id=comp_id_val).first()
                if supplier:
                    name = f"{supplier.supplier_name or ''}[{supplier.supplier_id or ''}]"
        except Exception: # Catch potential conversion errors for EmpId, CustomerId, SupplierId
            pass
        
        result = {'type': entity_type_name, 'name': name}
        cache.set(key, result, 3600) # Cache for 1 hour
        return result

    @property
    def received_by_info(self):
        comp_id_val = self.comp_id or getattr(settings, 'GLOBAL_COMP_ID', None)
        return self._get_related_entity(self.received_by_id, self.code_type, comp_id_val)

    @property
    def received_by_name(self):
        return self.received_by_info.get('name', '')

    @property
    def received_by_type(self):
        return self.received_by_info.get('type', '')

    @property
    def total_amount(self):
        return CashVoucherPaymentDetail.objects.filter(mid=self.id).aggregate(total=Sum('amount'))['total'] or 0.0

class CashVoucherPaymentDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) 
    mid = models.IntegerField(db_column='MId') 
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=2, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_CashVoucher_Payment_Details'
        verbose_name = 'Cash Voucher Payment Detail'
        verbose_name_plural = 'Cash Voucher Payment Details'

    def __str__(self):
        return f"Detail for MId: {self.mid}"

class CashVoucherReceiptMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    cvr_no = models.CharField(db_column='CVRNo', max_length=50, blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    sys_date = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    cash_received_against_id = models.CharField(db_column='CashReceivedAgainst', max_length=50, blank=True, null=True)
    code_type_ra = models.IntegerField(db_column='CodeTypeRA', blank=True, null=True)
    cash_received_by_id = models.CharField(db_column='CashReceivedBy', max_length=50, blank=True, null=True)
    code_type_rb = models.IntegerField(db_column='CodeTypeRB', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=2, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_CashVoucher_Receipt_Master'
        verbose_name = 'Cash Voucher Receipt'
        verbose_name_plural = 'Cash Voucher Receipts'

    def __str__(self):
        return self.cvr_no or ''

    @property
    def formatted_sys_date(self):
        return date_format(self.sys_date, "d-m-Y") if self.sys_date else ''

    @property
    def financial_year(self):
        if self.fin_year_id:
            try:
                comp_id_val = self.comp_id or getattr(settings, 'GLOBAL_COMP_ID', None)
                if comp_id_val is not None:
                    financial_master = FinancialMaster.objects.filter(
                        fin_year_id=self.fin_year_id, 
                        comp_id=comp_id_val
                    ).first()
                    return financial_master.fin_year if financial_master else ''
            except FinancialMaster.DoesNotExist:
                pass
        return ''

    @property
    def cash_received_against_info(self):
        comp_id_val = self.comp_id or getattr(settings, 'GLOBAL_COMP_ID', None)
        return self._get_related_entity(self.cash_received_against_id, self.code_type_ra, comp_id_val)

    @property
    def cash_received_against_name(self):
        return self.cash_received_against_info.get('name', '')

    @property
    def cash_received_against_type(self):
        return self.cash_received_against_info.get('type', '')

    @property
    def cash_received_by_info(self):
        comp_id_val = self.comp_id or getattr(settings, 'GLOBAL_COMP_ID', None)
        return self._get_related_entity(self.cash_received_by_id, self.code_type_rb, comp_id_val)

    @property
    def cash_received_by_name(self):
        return self.cash_received_by_info.get('name', '')

    @property
    def cash_received_by_type(self):
        return self.cash_received_by_info.get('type', '')

```

#### 4.2 Forms

Since this module only handles data display and search, and no direct Create/Update operations were identified in the ASP.NET code, we do not need complex Django forms for data entry. Search parameters will be handled directly in the views or simple `forms.Form` instances if validation is needed.

```python
# cash_vouchers/forms.py
# No specific forms needed as per analysis. Search parameters handled directly in views.
# If more complex validation or structured input is required for search,
# a simple forms.Form could be defined here.
```

#### 4.3 Views

The views will be concise, focusing on retrieving data and rendering templates. All complex data manipulation and lookup logic will reside in the models. We'll use a single `CashVoucherPrintView` to handle the main page with tabs, and separate views for the HTMX-loaded table partials and autocomplete endpoints.

```python
# cash_vouchers/views.py
from django.views.generic import TemplateView, ListView
from django.http import JsonResponse
from django.conf import settings
from .models import (
    CashVoucherPaymentMaster, CashVoucherReceiptMaster,
    OfficeStaff, CustomerMaster, SupplierMaster
)

# Helper to get current company ID from session or global settings
# In a real app, integrate proper user authentication and company context
def get_company_id(request):
    return request.session.get('compid', getattr(settings, 'GLOBAL_COMP_ID', None))

class CashVoucherPrintView(TemplateView):
    template_name = 'cash_vouchers/cashvoucher_print.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initial context, tables will be loaded via HTMX
        return context

class CashVoucherPaymentTablePartialView(ListView):
    model = CashVoucherPaymentMaster
    template_name = 'cash_vouchers/_cashvoucher_payment_table.html'
    context_object_name = 'payments'
    paginate_by = 20 # Matches ASP.NET PageSize

    def get_queryset(self):
        queryset = super().get_queryset()
        comp_id = get_company_id(self.request)
        if comp_id is not None:
            queryset = queryset.filter(comp_id=comp_id)

        # Apply search filter
        search_paid_to = self.request.GET.get('paid_to_search', '').strip()
        if search_paid_to:
            queryset = queryset.filter(paid_to__icontains=search_paid_to)
        
        # Order by Id Desc as in ASP.NET
        queryset = queryset.order_by('-id')
        return queryset

class CashVoucherReceiptTablePartialView(ListView):
    model = CashVoucherReceiptMaster
    template_name = 'cash_vouchers/_cashvoucher_receipt_table.html'
    context_object_name = 'receipts'
    paginate_by = 20 # Matches ASP.NET PageSize

    def get_queryset(self):
        queryset = super().get_queryset()
        comp_id = get_company_id(self.request)
        if comp_id is not None:
            queryset = queryset.filter(comp_id=comp_id)

        # Apply search filter
        search_cash_rec_against = self.request.GET.get('cash_rec_against_search', '').strip()
        if search_cash_rec_against:
            # The ASP.NET code seems to extract an ID from the search text (fun.getCode)
            # For simplicity here, we'll search by name part. A more robust solution
            # might involve a hidden input for the ID from autocomplete.
            # We need to consider how CashReceivedAgainst was stored if it's not a direct name.
            # Assuming for now it's searchable by name, or a combination.
            # Given `fun.EmpCustSupplierNames` was used, it's likely searching by full name.
            # The ASP.NET AutoComplete provides "Name [ID]", so let's simplify to search by name portion.
            queryset = queryset.filter(cash_received_against_id__icontains=search_cash_rec_against)
        
        # Order by Id Desc as in ASP.NET
        queryset = queryset.order_by('-id')
        return queryset

class PaidToAutocompleteView(TemplateView):
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('q', '').strip()
        comp_id = get_company_id(request)
        
        # This mirrors the ASP.NET sql() web method logic
        queryset = CashVoucherPaymentMaster.objects.filter(comp_id=comp_id).values_list('paid_to', flat=True).distinct()
        
        suggestions = sorted(
            [item for item in queryset if item and item.lower().startswith(prefix_text.lower())]
        )[:10] # Limit to 10 suggestions, common for autocomplete

        return JsonResponse(suggestions, safe=False)

class CashRecAgainstAutocompleteView(TemplateView):
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('q', '').strip()
        comp_id = get_company_id(request)

        # This mirrors the ASP.NET sql1() web method logic
        # It unions employee, customer, and supplier names
        employee_names = OfficeStaff.objects.filter(comp_id=comp_id).values_list('employee_name', 'emp_id')
        customer_names = CustomerMaster.objects.filter(comp_id=comp_id).values_list('customer_name', 'customer_id')
        supplier_names = SupplierMaster.objects.filter(comp_id=comp_id).values_list('supplier_name', 'supplier_id')

        all_names = []
        for name, id_val in employee_names:
            if name: all_names.append(f"{name}[{id_val or ''}]")
        for name, id_val in customer_names:
            if name: all_names.append(f"{name}[{id_val or ''}]")
        for name, id_val in supplier_names:
            if name: all_names.append(f"{name}[{id_val or ''}]")

        suggestions = sorted(
            [item for item in all_names if item and item.lower().startswith(prefix_text.lower())]
        )[:10]

        return JsonResponse(suggestions, safe=False)

```

#### 4.4 Templates

Templates are built using DRY principles, extending `core/base.html` and utilizing partials for HTMX content. Alpine.js will manage the tab UI state.

```html
{# cash_vouchers/templates/cash_vouchers/cashvoucher_print.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-blue-600 text-white p-3 mb-6 rounded-md">
        <h1 class="text-xl font-bold">Cash Voucher - Print</h1>
    </div>

    <div x-data="{ activeTab: 'payment' }" class="bg-white shadow-md rounded-lg p-6">
        {# Tab Headers #}
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <a href="#" 
                   class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                   :class="activeTab === 'payment' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                   @click.prevent="activeTab = 'payment'; $nextTick(() => { if (typeof $.fn.DataTable !== 'undefined') $('#paymentTable').DataTable(); });"
                   hx-get="{% url 'cash_vouchers:payment_table_partial' %}"
                   hx-target="#paymentTabContent"
                   hx-swap="innerHTML"
                   hx-indicator="#payment-tab-spinner"
                   hx-trigger="click, load once">
                    Payment
                </a>
                <a href="#" 
                   class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                   :class="activeTab === 'receipt' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                   @click.prevent="activeTab = 'receipt'; $nextTick(() => { if (typeof $.fn.DataTable !== 'undefined') $('#receiptTable').DataTable(); });"
                   hx-get="{% url 'cash_vouchers:receipt_table_partial' %}"
                   hx-target="#receiptTabContent"
                   hx-swap="innerHTML"
                   hx-indicator="#receipt-tab-spinner"
                   hx-trigger="click, load once">
                    Receipt
                </a>
            </nav>
        </div>

        {# Tab Content #}
        <div class="mt-4">
            <div x-show="activeTab === 'payment'" class="space-y-4">
                <div class="flex items-center space-x-2">
                    <label for="paid_to_search" class="text-gray-700">Paid To:</label>
                    <input type="text" id="paid_to_search" name="paid_to_search" 
                           class="flex-grow px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm box3"
                           hx-get="{% url 'cash_vouchers:paid_to_autocomplete' %}"
                           hx-trigger="keyup changed delay:500ms, search"
                           hx-target="#autocomplete-paid-to-results"
                           hx-swap="innerHTML"
                           autocomplete="off"
                           placeholder="Type to search...">
                    <button id="searchPaymentButton" 
                            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded redbox"
                            hx-get="{% url 'cash_vouchers:payment_table_partial' %}"
                            hx-target="#paymentTabContent"
                            hx-swap="innerHTML"
                            hx-vals="js:{paid_to_search: document.getElementById('paid_to_search').value}"
                            hx-indicator="#payment-tab-spinner">
                        Search
                    </button>
                </div>
                <div id="autocomplete-paid-to-results" class="relative"></div>

                <div id="payment-tab-spinner" class="htmx-indicator text-center text-blue-500 text-lg mt-4">
                    <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div> Loading...
                </div>
                <div id="paymentTabContent">
                    {# Payment table will be loaded here via HTMX #}
                </div>
            </div>

            <div x-show="activeTab === 'receipt'" class="space-y-4">
                <div class="flex items-center space-x-2">
                    <label for="cash_rec_against_search" class="text-gray-700">Cash Rec. Against:</label>
                    <input type="text" id="cash_rec_against_search" name="cash_rec_against_search" 
                           class="flex-grow px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm box3"
                           hx-get="{% url 'cash_vouchers:cash_rec_against_autocomplete' %}"
                           hx-trigger="keyup changed delay:500ms, search"
                           hx-target="#autocomplete-cash-rec-results"
                           hx-swap="innerHTML"
                           autocomplete="off"
                           placeholder="Type to search...">
                    <button id="searchReceiptButton" 
                            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded redbox"
                            hx-get="{% url 'cash_vouchers:receipt_table_partial' %}"
                            hx-target="#receiptTabContent"
                            hx-swap="innerHTML"
                            hx-vals="js:{cash_rec_against_search: document.getElementById('cash_rec_against_search').value}"
                            hx-indicator="#receipt-tab-spinner">
                        Search
                    </button>
                </div>
                 <div id="autocomplete-cash-rec-results" class="relative"></div>

                <div id="receipt-tab-spinner" class="htmx-indicator text-center text-blue-500 text-lg mt-4">
                    <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div> Loading...
                </div>
                <div id="receiptTabContent">
                    {# Receipt table will be loaded here via HTMX #}
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
{# Include DataTables JS and initialization in the partial templates, as they are swapped #}
{# Autocomplete logic for HTMX #}
<script>
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'autocomplete-paid-to-results' || event.detail.target.id === 'autocomplete-cash-rec-results') {
            const resultsDiv = event.detail.target;
            resultsDiv.querySelectorAll('div').forEach(item => {
                item.addEventListener('click', function() {
                    const input = resultsDiv.previousElementSibling.previousElementSibling; // Get the input field
                    input.value = this.textContent.trim();
                    resultsDiv.innerHTML = ''; // Clear results after selection
                });
            });
        }
    });

    // Handle closing autocomplete results on outside click
    document.addEventListener('click', function(event) {
        const paidToAutocomplete = document.getElementById('autocomplete-paid-to-results');
        const cashRecAutocomplete = document.getElementById('autocomplete-cash-rec-results');
        const paidToInput = document.getElementById('paid_to_search');
        const cashRecInput = document.getElementById('cash_rec_against_search');

        if (paidToAutocomplete && !paidToInput.contains(event.target) && !paidToAutocomplete.contains(event.target)) {
            paidToAutocomplete.innerHTML = '';
        }
        if (cashRecAutocomplete && !cashRecInput.contains(event.target) && !cashRecAutocomplete.contains(event.target)) {
            cashRecAutocomplete.innerHTML = '';
        }
    });
</script>
{% endblock %}
```

```html
{# cash_vouchers/templates/cash_vouchers/_cashvoucher_payment_table.html #}
{% load django_tables2 %}
{% load paginate %}

<div class="overflow-x-auto">
    <table id="paymentTable" class="min-w-full bg-white border border-gray-200 yui-datatable-theme">
        <thead>
            <tr class="bg-gray-50">
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CVP No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin. Year</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Paid To</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Receiver Type</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Received By</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
            </tr>
        </thead>
        <tbody>
            {% if payments %}
                {% for payment in payments %}
                <tr class="hover:bg-gray-50">
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter0|add:payments.start_index }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">
                        <a href="{% url 'cash_vouchers:payment_details' payment.id %}" target="_blank"
                           class="text-blue-600 hover:text-blue-800 font-medium">Select</a>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ payment.cvp_no }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ payment.financial_year }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ payment.formatted_sys_date }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ payment.paid_to }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ payment.received_by_type }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ payment.received_by_name }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ payment.total_amount|floatformat:2 }}</td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="9" class="py-4 text-center text-gray-500 font-bold text-lg">No data to display !</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

{# Pagination for HTMX DataTables #}
{% if payments.has_other_pages %}
<div class="mt-4 flex justify-center">
    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
        {% if payments.has_previous %}
            <a href="?page={{ payments.previous_page_number }}{% if request.GET.paid_to_search %}&paid_to_search={{ request.GET.paid_to_search }}{% endif %}"
               class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
               hx-get="{% url 'cash_vouchers:payment_table_partial' %}" hx-target="#paymentTabContent" hx-swap="innerHTML"
               hx-vals="js:{page: {{ payments.previous_page_number }}, paid_to_search: document.getElementById('paid_to_search').value}"
               hx-indicator="#payment-tab-spinner">
                <span class="sr-only">Previous</span>
                <!-- Heroicon name: solid/chevron-left -->
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
            </a>
        {% endif %}
        {% for i in payments.paginator.page_range %}
            {% if payments.number == i %}
                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-indigo-500 text-sm font-medium text-white">{{ i }}</span>
            {% else %}
                <a href="?page={{ i }}{% if request.GET.paid_to_search %}&paid_to_search={{ request.GET.paid_to_search }}{% endif %}"
                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
                   hx-get="{% url 'cash_vouchers:payment_table_partial' %}" hx-target="#paymentTabContent" hx-swap="innerHTML"
                   hx-vals="js:{page: {{ i }}, paid_to_search: document.getElementById('paid_to_search').value}"
                   hx-indicator="#payment-tab-spinner">
                    {{ i }}
                </a>
            {% endif %}
        {% endfor %}
        {% if payments.has_next %}
            <a href="?page={{ payments.next_page_number }}{% if request.GET.paid_to_search %}&paid_to_search={{ request.GET.paid_to_search }}{% endif %}"
               class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
               hx-get="{% url 'cash_vouchers:payment_table_partial' %}" hx-target="#paymentTabContent" hx-swap="innerHTML"
               hx-vals="js:{page: {{ payments.next_page_number }}, paid_to_search: document.getElementById('paid_to_search').value}"
               hx-indicator="#payment-tab-spinner">
                <span class="sr-only">Next</span>
                <!-- Heroicon name: solid/chevron-right -->
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10l-3.293-3.293a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
            </a>
        {% endif %}
    </nav>
</div>
{% endif %}

<script>
    // Initialize DataTables after content is loaded via HTMX
    $(document).ready(function() {
        if (typeof $.fn.DataTable !== 'undefined') {
            $('#paymentTable').DataTable({
                "pageLength": {{ payments.per_page }}, // Set page length based on Django pagination
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "paging": false, // Disable DataTables own paging as HTMX handles it
                "info": false, // Disable DataTables info text
                "searching": false, // Disable DataTables search as HTMX handles it
            });
        }
    });
</script>
```

```html
{# cash_vouchers/templates/cash_vouchers/_cashvoucher_receipt_table.html #}
{% load django_tables2 %}
{% load paginate %}

<div class="overflow-x-auto">
    <table id="receiptTable" class="min-w-full bg-white border border-gray-200 yui-datatable-theme">
        <thead>
            <tr class="bg-gray-50">
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CVR No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin. Year</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rec. Against Type</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cash Rec. Against</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rec. By Type</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cash Rec. By</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
            </tr>
        </thead>
        <tbody>
            {% if receipts %}
                {% for receipt in receipts %}
                <tr class="hover:bg-gray-50">
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter0|add:receipts.start_index }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">
                        <a href="{% url 'cash_vouchers:receipt_details' receipt.id %}" target="_blank"
                           class="text-blue-600 hover:text-blue-800 font-medium">Select</a>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ receipt.cvr_no }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ receipt.financial_year }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ receipt.formatted_sys_date }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ receipt.cash_received_against_type }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ receipt.cash_received_against_name }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ receipt.cash_received_by_type }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ receipt.cash_received_by_name }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ receipt.amount|floatformat:2 }}</td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="10" class="py-4 text-center text-gray-500 font-bold text-lg">No data to display !</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

{# Pagination for HTMX DataTables #}
{% if receipts.has_other_pages %}
<div class="mt-4 flex justify-center">
    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
        {% if receipts.has_previous %}
            <a href="?page={{ receipts.previous_page_number }}{% if request.GET.cash_rec_against_search %}&cash_rec_against_search={{ request.GET.cash_rec_against_search }}{% endif %}"
               class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
               hx-get="{% url 'cash_vouchers:receipt_table_partial' %}" hx-target="#receiptTabContent" hx-swap="innerHTML"
               hx-vals="js:{page: {{ receipts.previous_page_number }}, cash_rec_against_search: document.getElementById('cash_rec_against_search').value}"
               hx-indicator="#receipt-tab-spinner">
                <span class="sr-only">Previous</span>
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
            </a>
        {% endif %}
        {% for i in receipts.paginator.page_range %}
            {% if receipts.number == i %}
                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-indigo-500 text-sm font-medium text-white">{{ i }}</span>
            {% else %}
                <a href="?page={{ i }}{% if request.GET.cash_rec_against_search %}&cash_rec_against_search={{ request.GET.cash_rec_against_search }}{% endif %}"
                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
                   hx-get="{% url 'cash_vouchers:receipt_table_partial' %}" hx-target="#receiptTabContent" hx-swap="innerHTML"
                   hx-vals="js:{page: {{ i }}, cash_rec_against_search: document.getElementById('cash_rec_against_search').value}"
                   hx-indicator="#receipt-tab-spinner">
                    {{ i }}
                </a>
            {% endif %}
        {% endfor %}
        {% if receipts.has_next %}
            <a href="?page={{ receipts.next_page_number }}{% if request.GET.cash_rec_against_search %}&cash_rec_against_search={{ request.GET.cash_rec_against_search }}{% endif %}"
               class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
               hx-get="{% url 'cash_vouchers:receipt_table_partial' %}" hx-target="#receiptTabContent" hx-swap="innerHTML"
               hx-vals="js:{page: {{ receipts.next_page_number }}, cash_rec_against_search: document.getElementById('cash_rec_against_search').value}"
               hx-indicator="#receipt-tab-spinner">
                <span class="sr-only">Next</span>
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10l-3.293-3.293a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
            </a>
        {% endif %}
    </nav>
</div>
{% endif %}

<script>
    // Initialize DataTables after content is loaded via HTMX
    $(document).ready(function() {
        if (typeof $.fn.DataTable !== 'undefined') {
            $('#receiptTable').DataTable({
                "pageLength": {{ receipts.per_page }},
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "paging": false,
                "info": false,
                "searching": false,
            });
        }
    });
</script>
```

```html
{# cash_vouchers/templates/cash_vouchers/_autocomplete_results.html #}
<div class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg w-full mt-1">
    {% for item in suggestions %}
        <div class="px-3 py-2 cursor-pointer hover:bg-gray-100 text-sm">
            {{ item }}
        </div>
    {% empty %}
        {# No suggestions, or you can omit this div if empty #}
    {% endfor %}
</div>
```

#### 4.5 URLs

URL patterns will define the main view, the HTMX endpoints for table partials, and the autocomplete services.

```python
# cash_vouchers/urls.py
from django.urls import path
from .views import (
    CashVoucherPrintView, 
    CashVoucherPaymentTablePartialView, 
    CashVoucherReceiptTablePartialView,
    PaidToAutocompleteView,
    CashRecAgainstAutocompleteView
)

app_name = 'cash_vouchers' # Namespace for URLs

urlpatterns = [
    # Main entry point for the Cash Voucher Print page with tabs
    path('print/', CashVoucherPrintView.as_view(), name='cashvoucher_print_view'),

    # HTMX endpoints for loading table partials
    path('print/payment-table/', CashVoucherPaymentTablePartialView.as_view(), name='payment_table_partial'),
    path('print/receipt-table/', CashVoucherReceiptTablePartialView.as_view(), name='receipt_table_partial'),

    # HTMX endpoints for autocomplete suggestions
    path('autocomplete/paid-to/', PaidToAutocompleteView.as_view(), name='paid_to_autocomplete'),
    path('autocomplete/cash-rec-against/', CashRecAgainstAutocompleteView.as_view(), name='cash_rec_against_autocomplete'),
    
    # Placeholder for details pages (these would be separate modules to convert)
    path('payment/details/<int:pk>/', TemplateView.as_view(template_name='cash_vouchers/payment_details_placeholder.html'), name='payment_details'),
    path('receipt/details/<int:pk>/', TemplateView.as_view(template_name='cash_vouchers/receipt_details_placeholder.html'), name='receipt_details'),
]

```
**Note:** You would need to include these URLs in your project's main `urls.py`:
`path('accounts/transactions/cashvoucher/', include('cash_vouchers.urls', namespace='cash_vouchers')),`

#### 4.6 Tests

Comprehensive tests ensure the reliability of models and views. This includes unit tests for model properties and methods, and integration tests for view responses, HTMX interactions, and data filtering.

```python
# cash_vouchers/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.conf import settings
from unittest.mock import patch
from datetime import datetime
from decimal import Decimal

# Import all models
from .models import (
    FinancialMaster, OfficeStaff, CustomerMaster, SupplierMaster,
    CashVoucherPaymentMaster, CashVoucherPaymentDetail, CashVoucherReceiptMaster
)

# Mock global company ID for consistent testing
@patch.object(settings, 'GLOBAL_COMP_ID', 1)
class CashVoucherModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all related models
        cls.comp_id = settings.GLOBAL_COMP_ID
        cls.fin_year_id = 2023
        FinancialMaster.objects.create(fin_year_id=cls.fin_year_id, fin_year="2023-2024", comp_id=cls.comp_id)

        cls.employee = OfficeStaff.objects.create(emp_id="E001", title="Mr", employee_name="John Doe", comp_id=cls.comp_id)
        cls.customer = CustomerMaster.objects.create(customer_id="C001", customer_name="Acme Corp", comp_id=cls.comp_id)
        cls.supplier = SupplierMaster.objects.create(supplier_id="S001", supplier_name="Globex Inc", comp_id=cls.comp_id)

        # Cash Voucher Payment Master
        cls.payment1 = CashVoucherPaymentMaster.objects.create(
            id=1, cvp_no="CVP001", fin_year_id=cls.fin_year_id, 
            sys_date=datetime(2023, 10, 26, 10, 0, 0), paid_to="Vendor A", 
            code_type=1, received_by_id=cls.employee.emp_id, comp_id=cls.comp_id
        )
        cls.payment2 = CashVoucherPaymentMaster.objects.create(
            id=2, cvp_no="CVP002", fin_year_id=cls.fin_year_id, 
            sys_date=datetime(2023, 10, 27, 11, 0, 0), paid_to="Vendor B", 
            code_type=2, received_by_id=cls.customer.customer_id, comp_id=cls.comp_id
        )
        CashVoucherPaymentDetail.objects.create(id=101, mid=cls.payment1.id, amount=Decimal('100.50'))
        CashVoucherPaymentDetail.objects.create(id=102, mid=cls.payment1.id, amount=Decimal('50.00'))
        CashVoucherPaymentDetail.objects.create(id=103, mid=cls.payment2.id, amount=Decimal('200.00'))

        # Cash Voucher Receipt Master
        cls.receipt1 = CashVoucherReceiptMaster.objects.create(
            id=10, cvr_no="CVR001", fin_year_id=cls.fin_year_id, 
            sys_date=datetime(2023, 11, 1, 9, 0, 0), cash_received_against_id=cls.customer.customer_id, 
            code_type_ra=2, cash_received_by_id=cls.employee.emp_id, code_type_rb=1, 
            comp_id=cls.comp_id, amount=Decimal('350.75')
        )
        cls.receipt2 = CashVoucherReceiptMaster.objects.create(
            id=11, cvr_no="CVR002", fin_year_id=cls.fin_year_id, 
            sys_date=datetime(2023, 11, 2, 14, 0, 0), cash_received_against_id=cls.supplier.supplier_id, 
            code_type_ra=3, cash_received_by_id=cls.customer.customer_id, code_type_rb=2, 
            comp_id=cls.comp_id, amount=Decimal('150.00')
        )

    def test_payment_master_properties(self):
        self.assertEqual(self.payment1.formatted_sys_date, "26-10-2023")
        self.assertEqual(self.payment1.financial_year, "2023-2024")
        self.assertEqual(self.payment1.received_by_name, "Mr. John Doe")
        self.assertEqual(self.payment1.received_by_type, "Employee")
        self.assertEqual(self.payment1.total_amount, Decimal('150.50')) # 100.50 + 50.00

        self.assertEqual(self.payment2.received_by_name, "Acme Corp[C001]")
        self.assertEqual(self.payment2.received_by_type, "Customer")
        self.assertEqual(self.payment2.total_amount, Decimal('200.00'))
    
    def test_receipt_master_properties(self):
        self.assertEqual(self.receipt1.formatted_sys_date, "01-11-2023")
        self.assertEqual(self.receipt1.financial_year, "2023-2024")
        self.assertEqual(self.receipt1.cash_received_against_name, "Acme Corp[C001]")
        self.assertEqual(self.receipt1.cash_received_against_type, "Customer")
        self.assertEqual(self.receipt1.cash_received_by_name, "Mr. John Doe")
        self.assertEqual(self.receipt1.cash_received_by_type, "Employee")
        self.assertEqual(self.receipt1.amount, Decimal('350.75'))

        self.assertEqual(self.receipt2.cash_received_against_name, "Globex Inc[S001]")
        self.assertEqual(self.receipt2.cash_received_against_type, "Supplier")
        self.assertEqual(self.receipt2.cash_received_by_name, "Acme Corp[C001]")
        self.assertEqual(self.receipt2.cash_received_by_type, "Customer")
        self.assertEqual(self.receipt2.amount, Decimal('150.00'))

    def test_invalid_code_type_handling(self):
        payment_invalid = CashVoucherPaymentMaster.objects.create(
            id=3, cvp_no="CVP003", fin_year_id=self.fin_year_id,
            sys_date=datetime(2023, 10, 28), paid_to="Unknown",
            code_type=99, received_by_id="XYZ", comp_id=self.comp_id
        )
        self.assertEqual(payment_invalid.received_by_name, "")
        self.assertEqual(payment_invalid.received_by_type, "")

@patch.object(settings, 'GLOBAL_COMP_ID', 1)
class CashVoucherViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.comp_id = settings.GLOBAL_COMP_ID
        cls.fin_year_id = 2023
        FinancialMaster.objects.create(fin_year_id=cls.fin_year_id, fin_year="2023-2024", comp_id=cls.comp_id)

        cls.employee = OfficeStaff.objects.create(emp_id="E001", title="Mr", employee_name="John Doe", comp_id=cls.comp_id)
        cls.customer = CustomerMaster.objects.create(customer_id="C001", customer_name="Acme Corp", comp_id=cls.comp_id)
        cls.supplier = SupplierMaster.objects.create(supplier_id="S001", supplier_name="Globex Inc", comp_id=cls.comp_id)

        # Populate some data for testing search and pagination
        for i in range(1, 25): # 24 payments, assuming paginate_by = 20
            CashVoucherPaymentMaster.objects.create(
                id=i, cvp_no=f"CVP{i:03d}", fin_year_id=cls.fin_year_id,
                sys_date=datetime(2023, 10, i), paid_to=f"Vendor {i}",
                code_type=1, received_by_id=cls.employee.emp_id, comp_id=cls.comp_id
            )
            CashVoucherPaymentDetail.objects.create(id=1000+i, mid=i, amount=Decimal(f'{i}.00'))

        for i in range(1, 25): # 24 receipts
            CashVoucherReceiptMaster.objects.create(
                id=100+i, cvr_no=f"CVR{i:03d}", fin_year_id=cls.fin_year_id,
                sys_date=datetime(2023, 11, i), cash_received_against_id=cls.customer.customer_id,
                code_type_ra=2, cash_received_by_id=cls.employee.emp_id, code_type_rb=1,
                comp_id=cls.comp_id, amount=Decimal(f'{i*10}.00')
            )

    def setUp(self):
        self.client = Client()
        self.client.session['compid'] = self.comp_id # Mock session compid

    def test_main_view_get(self):
        response = self.client.get(reverse('cash_vouchers:cashvoucher_print_view'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'cash_vouchers/cashvoucher_print.html')
        self.assertContains(response, "Cash Voucher - Print")
        self.assertContains(response, "Payment")
        self.assertContains(response, "Receipt")

    def test_payment_table_partial_load(self):
        # Initial load should fetch first page of data
        response = self.client.get(reverse('cash_vouchers:payment_table_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'cash_vouchers/_cashvoucher_payment_table.html')
        self.assertContains(response, "CVP024") # Latest created
        self.assertContains(response, "CVP005") # Should be on first page if 20 per page and ordered desc
        self.assertNotContains(response, "CVP001") # Should be on second page

    def test_payment_table_partial_pagination(self):
        response = self.client.get(reverse('cash_vouchers:payment_table_partial'), {'page': 2})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'cash_vouchers/_cashvoucher_payment_table.html')
        self.assertContains(response, "CVP004") # Should be on second page
        self.assertContains(response, "CVP001") # First element of second page
        self.assertNotContains(response, "CVP024") # Should not be on second page

    def test_payment_table_partial_search(self):
        response = self.client.get(reverse('cash_vouchers:payment_table_partial'), {'paid_to_search': 'Vendor 1'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'cash_vouchers/_cashvoucher_payment_table.html')
        self.assertContains(response, "CVP010") # Example: 'Vendor 1' will match Vendor 1, 10, 11, ... 19
        self.assertContains(response, "CVP001")
        self.assertNotContains(response, "CVP020") # Not matching the search
    
    def test_receipt_table_partial_load(self):
        response = self.client.get(reverse('cash_vouchers:receipt_table_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'cash_vouchers/_cashvoucher_receipt_table.html')
        self.assertContains(response, "CVR024")
        self.assertContains(response, "CVR005")
        self.assertNotContains(response, "CVR001")

    def test_receipt_table_partial_pagination(self):
        response = self.client.get(reverse('cash_vouchers:receipt_table_partial'), {'page': 2})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'cash_vouchers/_cashvoucher_receipt_table.html')
        self.assertContains(response, "CVR004")
        self.assertContains(response, "CVR001")
        self.assertNotContains(response, "CVR024")

    def test_receipt_table_partial_search(self):
        response = self.client.get(reverse('cash_vouchers:receipt_table_partial'), {'cash_rec_against_search': 'Acme Corp'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'cash_vouchers/_cashvoucher_receipt_table.html')
        self.assertContains(response, "Acme Corp[C001]") # All receipts were created with this customer
        self.assertContains(response, "CVR001") 
        self.assertContains(response, "CVR024") 

    def test_paid_to_autocomplete(self):
        response = self.client.get(reverse('cash_vouchers:paid_to_autocomplete'), {'q': 'vendor 1'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = response.json()
        self.assertIn('Vendor 1', data)
        self.assertIn('Vendor 10', data)
        self.assertIn('Vendor 11', data)
        self.assertNotIn('Vendor 2', data) # Should not match 'vendor 1' prefix

    def test_cash_rec_against_autocomplete(self):
        response = self.client.get(reverse('cash_vouchers:cash_rec_against_autocomplete'), {'q': 'acme'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = response.json()
        self.assertIn('Acme Corp[C001]', data)
        self.assertNotIn('Mr. John Doe', data)

    def test_select_links(self):
        # Verify that select links exist and point to the correct URLs
        response = self.client.get(reverse('cash_vouchers:payment_table_partial'))
        self.assertContains(response, f'href="/accounts/transactions/cashvoucher/payment/details/{self.payment1.id}/"')
        
        response = self.client.get(reverse('cash_vouchers:receipt_table_partial'))
        self.assertContains(response, f'href="/accounts/transactions/cashvoucher/receipt/details/{self.receipt1.id}/"')

```

### Step 5: HTMX and Alpine.js Integration

The core of the dynamic user experience will leverage HTMX for server-side rendering of partial content and Alpine.js for lightweight client-side interactions.

-   **Tab Switching (Alpine.js):** Alpine.js manages the `activeTab` state to show/hide content. When a tab is clicked, it updates `activeTab` and triggers an HTMX `hx-get` to load the respective table content into the appropriate `div`.
-   **Dynamic Table Loading (HTMX):**
    -   The `cashvoucher_print.html` initially triggers `hx-get` on `load once` for both `paymentTabContent` and `receiptTabContent` using `hx-target` and `hx-swap="innerHTML"`.
    -   Search buttons (e.g., `searchPaymentButton`) trigger `hx-get` requests to the partial table views, passing search parameters via `hx-vals="js:{...}"`. The `hx-indicator` attribute provides visual feedback during loading.
    -   Pagination links within `_cashvoucher_payment_table.html` and `_cashvoucher_receipt_table.html` use `hx-get` to load the next/previous page of data, also passing the current search term to maintain filters.
-   **DataTables Initialization:** The `_cashvoucher_payment_table.html` and `_cashvoucher_receipt_table.html` partials include `<script>` tags to initialize DataTables on their respective table IDs (`#paymentTable`, `#receiptTable`). This ensures DataTables is applied *after* the HTML is loaded by HTMX. Note: DataTables' built-in pagination and search are disabled because HTMX and Django handle these.
-   **Autocomplete (HTMX):** The `input` fields for "Paid To" and "Cash Rec. Against" use `hx-get` to trigger autocomplete search views on `keyup changed delay:500ms`. The results are swapped into a designated `div` below the input, which then has JavaScript (Alpine.js or simple inline JS) to handle clicking a suggestion to populate the input field.

### Final Notes

This comprehensive plan provides a structured, automated, and modern approach to migrate your ASP.NET Cash Voucher Print module to Django. By adhering to the "Fat Model, Thin View" paradigm, using HTMX for dynamic content, DataTables for enhanced data presentation, and maintaining strict separation of concerns, the resulting application will be maintainable, scalable, and provide a superior user experience. This AI-assisted migration emphasizes automated code generation and systematic conversion steps, minimizing manual effort and potential errors.