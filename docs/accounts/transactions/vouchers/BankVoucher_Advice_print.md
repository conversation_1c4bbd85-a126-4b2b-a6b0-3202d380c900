## ASP.NET to Django Conversion Script: Bank Voucher Advice Print

This modernization plan outlines the transition of your legacy ASP.NET Bank Voucher Advice Print functionality to a modern Django application. The focus is on leveraging AI-assisted automation by providing clear, structured components that can be generated and integrated systematically. We will emphasize a "fat model, thin view" architecture, utilize HTMX and Alpine.js for dynamic frontends without extensive custom JavaScript, and integrate DataTables for efficient data presentation.

The original ASP.NET page serves primarily as a **report viewer** (displaying a Bank Voucher Payment Advice). It is *not* a data entry or CRUD (Create, Read, Update, Delete) page. Therefore, the Django solution will prioritize recreating the report generation and display functionality. While the provided template includes sections for generic CRUD operations, for this specific module, we will focus on the **read/report** aspect. We will, however, provide generic CRUD templates for `BankVoucherPaymentMaster` to illustrate how such related master data would be handled, as per the requested structure.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is extended.
- Focus ONLY on component-specific code for the current module (`accounts` app, `bankvoucher` sub-module).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
The C# code interacts with several tables to compile the report data. We will define models for the primary tables (`tblACC_BankVoucher_Payment_Master` and `tblACC_BankVoucher_Payment_Details`) and infer basic fields. Other referenced tables (`tblHR_OfficeStaff`, `SD_Cust_master`, `tblMM_Supplier_master`, `tblACC_BillBooking_Master`, `tblACC_PaidType`) are lookup tables; their specific schemas would be fully defined in their respective modules, but their relationships are noted here.

**Identified Tables and Inferred Fields:**

*   **`tblACC_BankVoucher_Payment_Master` (Main Transaction Header)**
    *   `Id` (Primary Key, integer)
    *   `CompId` (Company ID, integer)
    *   `FinYearId` (Financial Year ID, integer)
    *   `BVPNo` (Bank Voucher Payment No., string)
    *   `PayAmt` (Payment Amount, decimal/double)
    *   `AddAmt` (Additional Amount, decimal/double)
    *   `Type` (Payment Type, integer, e.g., 1=ProformaInv, 2,3,4=BillBooking)
    *   `ECSType` (ECS Type, integer, e.g., 1=Employee, 2=Customer, 3=Supplier)
    *   `PayTo` (ID of recipient based on ECSType, string/integer)
    *   `NameOnCheque` (Name on Cheque, string)
    *   `PaidType` (Paid Type ID, integer)
    *   `ChequeDate` (Cheque Date, datetime)
    *   `ChequeNo` (Cheque No., string)
    *   `SysDate` (System Date, datetime)

*   **`tblACC_BankVoucher_Payment_Details` (Transaction Line Items)**
    *   `MId` (Foreign Key to `tblACC_BankVoucher_Payment_Master.Id`, integer)
    *   `ProformaInvNo` (Proforma Invoice No., string)
    *   `InvDate` (Invoice Date, datetime)
    *   `Amount` (Detail Amount, decimal/double)
    *   `Particular` (Particulars, string)

*   **Lookup Tables (Implied Structure):**
    *   `tblHR_OfficeStaff`: `EmpId`, `EmployeeName`, `Address`
    *   `SD_Cust_master`: `CustomerId`, `CustomerName`, `Address`
    *   `tblMM_Supplier_master`: `SupplierId`, `SupplierName`, `Address`
    *   `tblACC_BillBooking_Master`: `Id`, `BillNo`
    *   `tblACC_PaidType`: `Id`, `Particulars`

### Step 2: Identify Backend Functionality

**Task:** Determine the operations performed by the ASP.NET code.

**Instructions:**
The ASP.NET code primarily performs a **READ** operation, specifically:
-   **Data Retrieval & Processing:** It queries `tblACC_BankVoucher_Payment_Master` and `tblACC_BankVoucher_Payment_Details` based on an `Id` from the query string, and `CompId` and `FinYearId` from the session. It then performs complex conditional lookups and data aggregation from various other tables (`tblHR_OfficeStaff`, `SD_Cust_master`, `tblMM_Supplier_master`, `tblACC_BillBooking_Master`, `tblACC_PaidType`) to construct a detailed report `DataTable`.
-   **Report Generation:** It uses Crystal Reports to bind this processed `DataTable` to a report template (`.rpt` file) and displays it.
-   **Navigation:** The "Cancel" button redirects the user to different pages based on query string parameters.

No explicit Create, Update, or Delete operations are performed on this page. The complexity lies in the data aggregation for the report.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
-   **`CR:CrystalReportViewer`**: This is the core display component. In Django, this will be replaced by a dynamically generated HTML table. Given the columnar nature of the C# `DataTable` being built, a `<table>` enhanced with DataTables for client-side features is the ideal modern equivalent.
-   **`asp:Panel`**: A container. In Django, this will be a `<div>` or similar HTML element for structure.
-   **`asp:Button ID="btnCancel"`**: This is a navigation trigger. In Django, it will be a simple HTML `<button>` or `<a>` tag that redirects the user. HTMX `hx-redirect` can be used for a more seamless navigation experience.

The CSS and JavaScript files referenced (`StyleSheet.css`, `yui-datatable.css`, `styles.css`, `loadingNotifier.js`, `PopUpMsg.js`) will be replaced by Tailwind CSS for styling and the minimal necessary JavaScript (HTMX, Alpine.js, DataTables library).

### Step 4: Generate Django Code

We will create a new Django application, perhaps named `accounts`, and within it, a sub-module for `bankvoucher`.

#### 4.1 Models

**Task:** Create Django models based on the identified database schema and implement the complex data processing logic within a custom manager for the "fat model" approach.

**Explanation:**
We define `BankVoucherPaymentMaster` and `BankVoucherPaymentDetail` as Django models, mapping them to their existing database tables. The intricate logic from the C# `Page_Load` method, which gathers and transforms data for the report, is encapsulated within a custom manager (`BankVoucherReportManager`) for `BankVoucherPaymentMaster`. This keeps views lean and consolidates business logic in the model layer. We also define `ReportOutputData` as a `dataclass` to structure the final report data, mirroring the `DataTable` created in C#. This ensures a clean interface between the data processing and presentation layers.

**File:** `accounts/bankvoucher/models.py`

```python
from django.db import models
from django.db.models import F, Sum
from datetime import datetime
from dataclasses import dataclass
from decimal import Decimal

# Assuming these related models exist elsewhere in the system
# For the purpose of this migration, we'll define minimal placeholders or assume their existence.
# In a real migration, these would be proper models from their respective modules.

class Employee(models.Model):
    EmpId = models.CharField(db_column='EmpId', max_length=50, primary_key=True)
    EmployeeName = models.CharField(db_column='EmployeeName', max_length=255)
    Address = models.CharField(db_column='Address', max_length=500, blank=True, null=True)
    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

class Customer(models.Model):
    CustomerId = models.CharField(db_column='CustomerId', max_length=50, primary_key=True)
    CustomerName = models.CharField(db_column='CustomerName', max_length=255)
    Address = models.CharField(db_column='Address', max_length=500, blank=True, null=True)
    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

class Supplier(models.Model):
    SupplierId = models.CharField(db_column='SupplierId', max_length=50, primary_key=True)
    SupplierName = models.CharField(db_column='SupplierName', max_length=255)
    Address = models.CharField(db_column='Address', max_length=500, blank=True, null=True)
    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

class BillBookingMaster(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    BillNo = models.CharField(db_column='BillNo', max_length=255)
    class Meta:
        managed = False
        db_table = 'tblACC_BillBooking_Master'
        verbose_name = 'Bill Booking Master'
        verbose_name_plural = 'Bill Booking Masters'

class PaidType(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    Particulars = models.CharField(db_column='Particulars', max_length=255)
    class Meta:
        managed = False
        db_table = 'tblACC_PaidType'
        verbose_name = 'Paid Type'
        verbose_name_plural = 'Paid Types'

# Dataclass to structure the report output, mimicking the C# DataTable
@dataclass
class BankVoucherReportData:
    paid_to: str
    company_id: int
    cheque_date: str # Formatted as DMY
    amount: Decimal
    address: str
    bvp_no: str
    cheque_no: str
    system_date: str # Formatted as DMY
    bill_no: str
    type_ecs_label: str
    ecs_code: str
    invoice_no: str
    particular: str
    invoice_date: str # Formatted as DMY
    pay_amount: Decimal
    additional_amount: Decimal

class BankVoucherReportManager(models.Manager):
    """
    Custom manager to encapsulate the complex report data retrieval and processing logic.
    This corresponds to the logic in the ASP.NET Page_Load method.
    """
    def _format_date_dmy(self, date_obj):
        """Helper to format date as DD/MM/YYYY, equivalent to fun.FromDateDMY"""
        if isinstance(date_obj, datetime):
            return date_obj.strftime('%d/%m/%Y')
        return "-" # Or an empty string, depending on desired output

    def _get_ecs_name_and_address(self, ecs_type: int, pay_to_id: str, comp_id: int):
        """Equivalent to fun.ECSNames and fun.ECSAddress"""
        name = ""
        address = ""
        try:
            if ecs_type == 1: # Employee
                obj = Employee.objects.using(self.db).get(EmpId=pay_to_id, CompId=comp_id)
                name = obj.EmployeeName
                address = obj.Address or "-"
            elif ecs_type == 2: # Customer
                obj = Customer.objects.using(self.db).get(CustomerId=pay_to_id, CompId=comp_id)
                name = obj.CustomerName
                address = obj.Address or "-"
            elif ecs_type == 3: # Supplier
                obj = Supplier.objects.using(self.db).get(SupplierId=pay_to_id, CompId=comp_id)
                name = obj.SupplierName
                address = obj.Address or "-"
        except (Employee.DoesNotExist, Customer.DoesNotExist, Supplier.DoesNotExist):
            name = "-"
            address = "-"
        return name, address

    def get_report_data(self, voucher_id: int, company_id: int, financial_year_id: int, db_alias='default') -> list[BankVoucherReportData]:
        """
        Processes and retrieves all necessary data for the Bank Voucher Payment Advice report.
        This method fully replaces the C# Page_Load data processing logic.
        """
        self.db = db_alias # Set the database alias for queries

        try:
            master_data = self.filter(Id=voucher_id, CompId=company_id, FinYearId__lte=financial_year_id).first()
            if not master_data:
                return []

            # Initialize common fields from master data
            bvp_no = master_data.BVPNo
            pay_amt = Decimal(master_data.PayAmt or 0)
            add_amt = Decimal(master_data.AddAmt or 0)
            comp_id = master_data.CompId
            cheque_date = self._format_date_dmy(master_data.ChequeDate)
            cheque_no = master_data.ChequeNo
            system_date = self._format_date_dmy(master_data.SysDate)

            # Determine PaidTo name and Address based on ECSType
            paid_to_name = ""
            paid_to_address = ""
            ecs_type_label = ""
            ecs_code = ""

            if master_data.NameOnCheque and master_data.NameOnCheque != "":
                paid_to_name = master_data.NameOnCheque
            elif master_data.PaidType is not None:
                try:
                    paid_type_obj = PaidType.objects.using(self.db).get(Id=master_data.PaidType)
                    paid_to_name = paid_type_obj.Particulars
                except PaidType.DoesNotExist:
                    pass # Handled by later ECSNames if NameOnCheque is also null
            
            # If name not yet set by NameOnCheque or PaidType, use ECS based names
            if not paid_to_name:
                 paid_to_name, paid_to_address = self._get_ecs_name_and_address(
                    master_data.ECSType, master_data.PayTo, master_data.CompId
                )

            # Determine ECS type label and code
            if master_data.ECSType == 1: # Employee
                ecs_type_label = "Employee Code : "
                ecs_code = Employee.objects.using(self.db).filter(EmpId=master_data.PayTo, CompId=comp_id).values_list('EmpId', flat=True).first() or "-"
            elif master_data.ECSType == 2: # Customer
                ecs_type_label = "Customer Code :"
                ecs_code = Customer.objects.using(self.db).filter(CustomerId=master_data.PayTo, CompId=comp_id).values_list('CustomerId', flat=True).first() or "-"
            elif master_data.ECSType == 3: # Supplier
                ecs_type_label = "Supplier Code :"
                ecs_code = Supplier.objects.using(self.db).filter(SupplierId=master_data.PayTo, CompId=comp_id).values_list('SupplierId', flat=True).first() or "-"

            # Get address if not already set (e.g. from NameOnCheque)
            if not paid_to_address:
                _, paid_to_address = self._get_ecs_name_and_address(
                    master_data.ECSType, master_data.PayTo, master_data.CompId
                )

            details_data = master_data.bankvoucherpaymentdetail_set.using(self.db).all()
            report_rows = []

            if details_data.exists():
                for detail in details_data:
                    bill_no = "-"
                    invoice_no = "-"
                    invoice_date = "-"
                    particular = detail.Particular if detail.Particular else "-"

                    if master_data.Type == 1: # Proforma Invoice
                        invoice_no = detail.ProformaInvNo if detail.ProformaInvNo else "-"
                        invoice_date = self._format_date_dmy(detail.InvDate)
                    elif master_data.Type == 4: # Bill Booking
                        if detail.PVEVNO: # Assuming PVEVNO is the ID for BillBookingMaster
                            try:
                                bill_obj = BillBookingMaster.objects.using(self.db).get(Id=detail.PVEVNO)
                                bill_no = bill_obj.BillNo
                            except BillBookingMaster.DoesNotExist:
                                pass

                    report_rows.append(
                        BankVoucherReportData(
                            paid_to=paid_to_name,
                            company_id=comp_id,
                            cheque_date=cheque_date,
                            amount=Decimal(detail.Amount or 0),
                            address=paid_to_address,
                            bvp_no=bvp_no,
                            cheque_no=cheque_no,
                            system_date=system_date,
                            bill_no=bill_no,
                            type_ecs_label=ecs_type_label,
                            ecs_code=ecs_code,
                            invoice_no=invoice_no,
                            particular=particular,
                            invoice_date=invoice_date,
                            pay_amount=pay_amt,
                            additional_amount=add_amt
                        )
                    )
            else:
                # Handle case where no details exist, but master data does (similar to original code)
                report_rows.append(
                    BankVoucherReportData(
                        paid_to=paid_to_name,
                        company_id=comp_id,
                        cheque_date=cheque_date,
                        amount=pay_amt, # Use master's pay_amt if no details
                        address=paid_to_address,
                        bvp_no=bvp_no,
                        cheque_no=cheque_no,
                        system_date=system_date,
                        bill_no="-",
                        type_ecs_label=ecs_type_label,
                        ecs_code=ecs_code,
                        invoice_no="-",
                        particular="-",
                        invoice_date="-",
                        pay_amount=pay_amt,
                        additional_amount=add_amt
                    )
                )

            return report_rows

        except Exception as e:
            # Log the exception for debugging
            print(f"Error generating report data: {e}")
            return [] # Return empty list on error

class BankVoucherPaymentMaster(models.Model):
    # Map fields directly to database columns
    Id = models.IntegerField(db_column='Id', primary_key=True)
    CompId = models.IntegerField(db_column='CompId')
    FinYearId = models.IntegerField(db_column='FinYearId')
    BVPNo = models.CharField(db_column='BVPNo', max_length=50, blank=True, null=True)
    PayAmt = models.DecimalField(db_column='PayAmt', max_digits=18, decimal_places=3, blank=True, null=True)
    AddAmt = models.DecimalField(db_column='AddAmt', max_digits=18, decimal_places=3, blank=True, null=True)
    Type = models.IntegerField(db_column='Type', blank=True, null=True) # Type of payment (e.g., related to ProformaInv, BillBooking)
    ECSType = models.IntegerField(db_column='ECSType', blank=True, null=True) # Type of recipient (e.g., Employee, Customer, Supplier)
    PayTo = models.CharField(db_column='PayTo', max_length=50, blank=True, null=True) # ID of the recipient
    NameOnCheque = models.CharField(db_column='NameOnCheque', max_length=255, blank=True, null=True)
    PaidType = models.IntegerField(db_column='PaidType', blank=True, null=True)
    ChequeDate = models.DateTimeField(db_column='ChequeDate', blank=True, null=True)
    ChequeNo = models.CharField(db_column='ChequeNo', max_length=50, blank=True, null=True)
    SysDate = models.DateTimeField(db_column='SysDate', blank=True, null=True)

    objects = BankVoucherReportManager() # Attach the custom manager

    class Meta:
        managed = False
        db_table = 'tblACC_BankVoucher_Payment_Master'
        verbose_name = 'Bank Voucher Payment Master'
        verbose_name_plural = 'Bank Voucher Payment Masters'

    def __str__(self):
        return f"{self.BVPNo or 'N/A'} - {self.Id}"

class BankVoucherPaymentDetail(models.Model):
    # MId is the foreign key to BankVoucherPaymentMaster
    MId = models.ForeignKey(BankVoucherPaymentMaster, models.DO_NOTHING, db_column='MId', related_name='bankvoucherpaymentdetail_set')
    # Assuming primary key `Id` for details, if not then could be composite or no PK
    Id = models.IntegerField(db_column='Id', primary_key=True) # Assuming Id as PK for detail rows
    ProformaInvNo = models.CharField(db_column='ProformaInvNo', max_length=50, blank=True, null=True)
    InvDate = models.DateTimeField(db_column='InvDate', blank=True, null=True)
    Amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=3, blank=True, null=True)
    Particular = models.CharField(db_column='Particular', max_length=500, blank=True, null=True)
    PVEVNO = models.CharField(db_column='PVEVNO', max_length=50, blank=True, null=True) # Reference to BillBookingMaster.Id

    class Meta:
        managed = False
        db_table = 'tblACC_BankVoucher_Payment_Details'
        verbose_name = 'Bank Voucher Payment Detail'
        verbose_name_plural = 'Bank Voucher Payment Details'

    def __str__(self):
        return f"Detail for MId: {self.MId.Id} - Amount: {self.Amount}"

```

#### 4.2 Forms

**Task:** Define Django forms for user input.

**Explanation:**
The original ASP.NET page (`BankVoucher_Advice_print.aspx`) is purely a display/report page and does *not* contain any input forms for creating or updating records. Therefore, a specific form for this functionality is not required.
However, to adhere to the requested template structure, we provide a generic `ModelForm` for `BankVoucherPaymentMaster`. This would be used if there was a separate page for managing (creating/editing) the master bank voucher records, demonstrating best practices for form creation.

**File:** `accounts/bankvoucher/forms.py`

```python
from django import forms
from .models import BankVoucherPaymentMaster

class BankVoucherPaymentMasterForm(forms.ModelForm):
    """
    This form is provided as an example for managing BankVoucherPaymentMaster records,
    though the original ASP.NET page is a report viewer and doesn't use input forms.
    """
    class Meta:
        model = BankVoucherPaymentMaster
        fields = ['BVPNo', 'PayAmt', 'AddAmt', 'Type', 'ECSType', 'PayTo', 
                  'NameOnCheque', 'PaidType', 'ChequeDate', 'ChequeNo', 'SysDate']
        widgets = {
            'BVPNo': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'PayAmt': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'AddAmt': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'Type': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'ECSType': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'PayTo': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'NameOnCheque': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'PaidType': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'ChequeDate': forms.DateTimeInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'datetime-local'}),
            'ChequeNo': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'SysDate': forms.DateTimeInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'datetime-local'}),
        }

```

#### 4.3 Views

**Task:** Implement the report display and generic CRUD operations using Django Class-Based Views.

**Explanation:**
The `BankVoucherReportView` is the core of this migration, handling the display of the report. It fetches the required `Id` from the URL, `CompId` and `FinYearId` from the session (or defaults), and then uses the custom `BankVoucherReportManager` to retrieve and format the report data. It adheres to the "thin view" principle by delegating complex data processing to the model manager.

We also provide generic `ListView`, `CreateView`, `UpdateView`, and `DeleteView` for the `BankVoucherPaymentMaster` model. These are not directly derived from the original ASP.NET page's functionality but are included as per the requested template structure for demonstrating standard Django CRUD operations for related master data. They use HTMX headers for seamless partial updates and modal interactions.

**File:** `accounts/bankvoucher/views.py`

```python
from django.views.generic import TemplateView, ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy, reverse
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import get_object_or_404
from .models import BankVoucherPaymentMaster
from .forms import BankVoucherPaymentMasterForm

class BankVoucherReportView(TemplateView):
    """
    View to display the Bank Voucher Payment Advice Report.
    This directly replaces the functionality of the ASP.NET BankVoucher_Advice_print.aspx.
    It takes the voucher ID from the URL and fetches report data via the model manager.
    """
    template_name = 'accounts/bankvoucher/report.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        voucher_id = self.kwargs.get('pk') # Get Id from URL path
        
        # Retrieve session parameters; use sensible defaults or raise error if missing
        comp_id = self.request.session.get('compid', 1) # Default to 1 if not in session
        fin_year_id = self.request.session.get('finyear', 1) # Default to 1 if not in session
        
        # Use the custom manager to get the processed report data
        report_data = BankVoucherPaymentMaster.objects.get_report_data(
            voucher_id=voucher_id,
            company_id=comp_id,
            financial_year_id=fin_year_id
        )
        context['report_rows'] = report_data
        context['voucher_id'] = voucher_id

        # Company address for the report header (equivalent to fun.CompAdd)
        # In a real system, this would come from a CompanySettings model
        context['company_address'] = "Your Company Address Line 1, City, State, Zip, Country" 

        return context

# HTMX partial view for the report table
class BankVoucherReportTablePartialView(TemplateView):
    template_name = 'accounts/bankvoucher/_report_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        voucher_id = self.kwargs.get('pk')
        comp_id = self.request.session.get('compid', 1)
        fin_year_id = self.request.session.get('finyear', 1)
        
        report_data = BankVoucherPaymentMaster.objects.get_report_data(
            voucher_id=voucher_id,
            company_id=comp_id,
            financial_year_id=fin_year_id
        )
        context['report_rows'] = report_data
        return context

# --- Generic CRUD Views for BankVoucherPaymentMaster (as per template requirement) ---

class BankVoucherPaymentMasterListView(ListView):
    model = BankVoucherPaymentMaster
    template_name = 'accounts/bankvoucher/list.html'
    context_object_name = 'bankvoucherpaymentmasters'

    def get_queryset(self):
        # Example: Filter by company ID from session if relevant for listing
        comp_id = self.request.session.get('compid', 1)
        return BankVoucherPaymentMaster.objects.filter(CompId=comp_id)

class BankVoucherPaymentMasterCreateView(CreateView):
    model = BankVoucherPaymentMaster
    form_class = BankVoucherPaymentMasterForm
    template_name = 'accounts/bankvoucher/form.html'
    success_url = reverse_lazy('bankvoucher_list') # Redirects to list view

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # Pre-populate CompId and FinYearId from session if needed
        kwargs['initial'] = {
            'CompId': self.request.session.get('compid', 1),
            'FinYearId': self.request.session.get('finyear', 1),
            'SysDate': datetime.now() # Set system date automatically
        }
        return kwargs

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Bank Voucher Payment Master added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, tells HTMX to do nothing more than trigger
                headers={
                    'HX-Trigger': 'refreshBankVoucherPaymentMasterList' # Custom HTMX event
                }
            )
        return response

class BankVoucherPaymentMasterUpdateView(UpdateView):
    model = BankVoucherPaymentMaster
    form_class = BankVoucherPaymentMasterForm
    template_name = 'accounts/bankvoucher/form.html'
    success_url = reverse_lazy('bankvoucher_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Bank Voucher Payment Master updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBankVoucherPaymentMasterList'
                }
            )
        return response

class BankVoucherPaymentMasterDeleteView(DeleteView):
    model = BankVoucherPaymentMaster
    template_name = 'accounts/bankvoucher/confirm_delete.html'
    success_url = reverse_lazy('bankvoucher_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Bank Voucher Payment Master deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBankVoucherPaymentMasterList'
                }
            )
        return response

```

#### 4.4 Templates

**Task:** Create templates for the report view and generic CRUD operations, adhering to HTMX and DataTables usage.

**Explanation:**
-   `accounts/bankvoucher/report.html`: This is the main report page. It extends `core/base.html` and uses HTMX to load the actual report table into a designated container. This setup allows the report table to be dynamically refreshed if needed (e.g., if report parameters were to change, though not in the original ASP.NET code).
-   `accounts/bankvoucher/_report_table.html`: This is a partial template containing the HTML table for the report. It will be loaded via HTMX into `report.html`. It includes a `script` tag for DataTables initialization, ensuring the table is interactive.
-   `accounts/bankvoucher/list.html`, `accounts/bankvoucher/form.html`, `accounts/bankvoucher/confirm_delete.html`: These are generic templates for CRUD operations on `BankVoucherPaymentMaster`. They are provided to fulfill the request's structural requirements, even though the original ASP.NET page is a report viewer. They demonstrate the use of HTMX for modal forms and dynamic list updates.

**File:** `accounts/bankvoucher/templates/accounts/bankvoucher/report.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Bank Voucher Payment Advice Report - Voucher #{{ voucher_id }}</h2>
        <a 
            href="{% url 'home' %}" {# Replace 'home' with actual previous page URL or a dashboard #}
            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
            hx-boost="true" {# Use hx-boost for client-side navigation if returning to a non-HTMX page #}
            hx-target="body"> {# Optionally target body to replace full page, or just let browser handle #}
            Cancel
        </a>
    </div>
    
    <div class="p-6 bg-white rounded-lg shadow-lg mb-8">
        <div class="text-center mb-6">
            <h1 class="text-3xl font-extrabold text-gray-900 mb-2">Bank Voucher Payment Advice</h1>
            <p class="text-gray-600">{{ company_address }}</p>
            <p class="text-gray-600">Voucher No: {{ report_rows.0.bvp_no|default:"N/A" }}</p>
        </div>

        <div id="bankVoucherReportTable-container"
             hx-trigger="load, refreshBankVoucherReport from:body" {# Trigger on page load and custom event #}
             hx-get="{% url 'bankvoucher_report_table_partial' voucher_id %}"
             hx-swap="innerHTML">
            <!-- Report Table will be loaded here via HTMX -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-500">Loading Report Data...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# Alpine.js is not strictly needed for this page as it's mainly display, but for consistency: #}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('reportPage', () => ({
            // Add any Alpine.js state or methods specific to the report page here
        }));
    });
</script>
{% endblock %}
```

**File:** `accounts/bankvoucher/templates/accounts/bankvoucher/_report_table.html`

```html
{# This is a partial template loaded via HTMX for the report table #}
<div class="overflow-x-auto">
    <table id="bankVoucherReportTable" class="min-w-full bg-white shadow-md rounded-lg overflow-hidden">
        <thead>
            <tr class="bg-gray-100 text-gray-600 uppercase text-sm leading-normal">
                <th class="py-3 px-6 text-left">SN</th>
                <th class="py-3 px-6 text-left">Paid To</th>
                <th class="py-3 px-6 text-left">Cheque Date</th>
                <th class="py-3 px-6 text-left">Amount</th>
                <th class="py-3 px-6 text-left">Address</th>
                <th class="py-3 px-6 text-left">Cheque No</th>
                <th class="py-3 px-6 text-left">System Date</th>
                <th class="py-3 px-6 text-left">Bill No</th>
                <th class="py-3 px-6 text-left">Inv No</th>
                <th class="py-3 px-6 text-left">Inv Date</th>
                <th class="py-3 px-6 text-left">Particular</th>
                <th class="py-3 px-6 text-left">ECS Type</th>
                <th class="py-3 px-6 text-left">ECS Code</th>
            </tr>
        </thead>
        <tbody class="text-gray-700 text-sm">
            {% for row in report_rows %}
            <tr class="border-b border-gray-200 hover:bg-gray-50">
                <td class="py-3 px-6 text-left whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-3 px-6 text-left">{{ row.paid_to }}</td>
                <td class="py-3 px-6 text-left">{{ row.cheque_date }}</td>
                <td class="py-3 px-6 text-left">{{ row.amount|floatformat:2 }}</td>
                <td class="py-3 px-6 text-left">{{ row.address }}</td>
                <td class="py-3 px-6 text-left">{{ row.cheque_no }}</td>
                <td class="py-3 px-6 text-left">{{ row.system_date }}</td>
                <td class="py-3 px-6 text-left">{{ row.bill_no }}</td>
                <td class="py-3 px-6 text-left">{{ row.invoice_no }}</td>
                <td class="py-3 px-6 text-left">{{ row.invoice_date }}</td>
                <td class="py-3 px-6 text-left">{{ row.particular }}</td>
                <td class="py-3 px-6 text-left">{{ row.type_ecs_label }}</td>
                <td class="py-3 px-6 text-left">{{ row.ecs_code }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="13" class="py-4 px-6 text-center text-gray-500">No report data found for this voucher.</td>
            </tr>
            {% endfor %}
        </tbody>
        {% if report_rows %}
        <tfoot class="bg-gray-100 text-gray-600 uppercase text-sm font-bold">
            <tr>
                <td colspan="3" class="py-3 px-6 text-right">Total Payment Amount:</td>
                <td class="py-3 px-6 text-left">{{ report_rows.0.pay_amount|floatformat:2 }}</td>
                <td colspan="1" class="py-3 px-6 text-right">Additional Amount:</td>
                <td class="py-3 px-6 text-left">{{ report_rows.0.additional_amount|floatformat:2 }}</td>
                <td colspan="7" class="py-3 px-6 text-right"></td>
            </tr>
        </tfoot>
        {% endif %}
    </table>
</div>

<script>
    // Initialize DataTables after content is loaded via HTMX
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#bankVoucherReportTable')) {
            $('#bankVoucherReportTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "searching": true,
                "paging": true,
                "info": true,
                "ordering": true,
                "dom": 'lfrtip' // Layout: length, filtering, table, info, pagination
            });
        }
    });
</script>
```

**File:** `accounts/bankvoucher/templates/accounts/bankvoucher/list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Bank Voucher Payment Masters</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'bankvoucher_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Bank Voucher
        </button>
    </div>
    
    <div id="bankvoucherpaymentmasterTable-container"
         hx-trigger="load, refreshBankVoucherPaymentMasterList from:body"
         hx-get="{% url 'bankvoucher_list_partial' %}" {# This URL would return _bankvoucher_table.html #}
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for this page
    });
</script>
{% endblock %}
```

**File:** `accounts/bankvoucher/templates/accounts/bankvoucher/_bankvoucher_table.html` (This partial view would be rendered by a `BankVoucherPaymentMasterListPartialView` - similar to `_report_table.html` but for the master list)

```html
{# This is a partial template for the BankVoucherPaymentMaster list table #}
<table id="bankvoucherpaymentmasterTable" class="min-w-full bg-white">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Voucher No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pay Amount</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cheque No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cheque Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in bankvoucherpaymentmasters %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.BVPNo }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.PayAmt|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.ChequeNo }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.ChequeDate|date:"d/m/Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <a 
                    href="{% url 'bankvoucher_report' obj.pk %}"
                    class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-boost="true" hx-target="body">
                    View Report
                </a>
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'bankvoucher_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'bankvoucher_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-2 px-4 border-b border-gray-200 text-center">No bank voucher payment masters found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#bankvoucherpaymentmasterTable')) {
            $('#bankvoucherpaymentmasterTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "searching": true,
                "paging": true,
                "info": true,
                "ordering": true,
                "dom": 'lfrtip'
            });
        }
    });
</script>
```

**File:** `accounts/bankvoucher/templates/accounts/bankvoucher/form.html`

```html
<div class="p-6" x-data="{ open: true }">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Bank Voucher Payment Master</h3>
    <form hx-post="{{ request.path }}" hx-swap="none"> {# hx-swap="none" allows HTMX to handle status codes #}
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**File:** `accounts/bankvoucher/templates/accounts/bankvoucher/confirm_delete.html`

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete Bank Voucher Payment Master "{{ object.BVPNo }}"?</p>
    
    <form hx-post="{{ request.path }}" hx-swap="none"> {# hx-swap="none" allows HTMX to handle status codes #}
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Explanation:**
This `urls.py` defines the paths for the report view (`bankvoucher_report`) and its HTMX partial (`bankvoucher_report_table_partial`). It also includes the standard CRUD paths (`list`, `add`, `edit`, `delete`) for `BankVoucherPaymentMaster` to adhere to the requested structure, and a partial view for the master list.

**File:** `accounts/bankvoucher/urls.py`

```python
from django.urls import path
from .views import (
    BankVoucherReportView, 
    BankVoucherReportTablePartialView,
    BankVoucherPaymentMasterListView,
    BankVoucherPaymentMasterCreateView,
    BankVoucherPaymentMasterUpdateView,
    BankVoucherPaymentMasterDeleteView
)

urlpatterns = [
    # Report View
    path('bankvoucher/report/<int:pk>/', BankVoucherReportView.as_view(), name='bankvoucher_report'),
    path('bankvoucher/report/table/<int:pk>/', BankVoucherReportTablePartialView.as_view(), name='bankvoucher_report_table_partial'),

    # Generic CRUD for BankVoucherPaymentMaster (as per template structure)
    path('bankvoucher/', BankVoucherPaymentMasterListView.as_view(), name='bankvoucher_list'),
    path('bankvoucher/table/', BankVoucherPaymentMasterListView.as_view(template_name='accounts/bankvoucher/_bankvoucher_table.html'), name='bankvoucher_list_partial'), # For HTMX
    path('bankvoucher/add/', BankVoucherPaymentMasterCreateView.as_view(), name='bankvoucher_add'),
    path('bankvoucher/edit/<int:pk>/', BankVoucherPaymentMasterUpdateView.as_view(), name='bankvoucher_edit'),
    path('bankvoucher/delete/<int:pk>/', BankVoucherPaymentMasterDeleteView.as_view(), name='bankvoucher_delete'),
]

```

#### 4.6 Tests

**Task:** Write comprehensive tests for the model and views.

**Explanation:**
Unit tests for models verify their data handling and business logic. Integration tests for views ensure that the views render correctly, handle HTTP requests as expected, and interact properly with the models and forms, including HTMX-specific responses. These tests cover both the report generation logic and the generic CRUD functionalities.

**File:** `accounts/bankvoucher/tests.py`

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import datetime
from decimal import Decimal
from .models import (
    BankVoucherPaymentMaster, BankVoucherPaymentDetail, Employee, Customer, Supplier, 
    BillBookingMaster, PaidType, BankVoucherReportData
)
from django.db import connection

class BankVoucherModelTest(TestCase):
    """
    Unit tests for BankVoucherPaymentMaster model and its custom manager (BankVoucherReportManager).
    Uses a mock database for isolation.
    """
    # Use a specific database alias for tests if you have multiple DBs configured.
    # Otherwise, 'default' is fine.
    # We will mock the related tables to simulate database state.

    @classmethod
    def setUpTestData(cls):
        # Ensure our mock database is used by the manager during testing
        cls.db_alias = 'default' 

        # Create mock data for related tables first
        Employee.objects.using(cls.db_alias).create(EmpId='EMP001', EmployeeName='John Doe', Address='123 Employee St', CompId=1)
        Customer.objects.using(cls.db_alias).create(CustomerId='CUST001', CustomerName='Acme Corp', Address='456 Customer Ave', CompId=1)
        Supplier.objects.using(cls.db_alias).create(SupplierId='SUP001', SupplierName='Widgets Inc', Address='789 Supplier Rd', CompId=1)
        BillBookingMaster.objects.using(cls.db_alias).create(Id=101, BillNo='BB-2023-001')
        PaidType.objects.using(cls.db_alias).create(Id=1, Particulars='Cash Payment')
        PaidType.objects.using(cls.db_alias).create(Id=2, Particulars='Bank Transfer')


        # Create a BankVoucherPaymentMaster record
        cls.master1 = BankVoucherPaymentMaster.objects.using(cls.db_alias).create(
            Id=1, CompId=1, FinYearId=2023, BVPNo='BV001', PayAmt=1000.00, AddAmt=50.00,
            Type=1, ECSType=1, PayTo='EMP001', NameOnCheque=None, PaidType=None,
            ChequeDate=datetime(2023, 1, 15), ChequeNo='CHQ001', SysDate=datetime(2023, 1, 16)
        )
        BankVoucherPaymentDetail.objects.using(cls.db_alias).create(
            Id=10, MId=cls.master1, ProformaInvNo='PI001', InvDate=datetime(2023, 1, 10),
            Amount=500.00, Particular='Partial payment for PI001'
        )
        BankVoucherPaymentDetail.objects.using(cls.db_alias).create(
            Id=11, MId=cls.master1, ProformaInvNo='PI002', InvDate=datetime(2023, 1, 12),
            Amount=500.00, Particular='Remaining for PI002'
        )

        # Master with no details
        cls.master2 = BankVoucherPaymentMaster.objects.using(cls.db_alias).create(
            Id=2, CompId=1, FinYearId=2023, BVPNo='BV002', PayAmt=200.00, AddAmt=0.00,
            Type=2, ECSType=3, PayTo='SUP001', NameOnCheque='Supplier Name A', PaidType=None,
            ChequeDate=datetime(2023, 2, 1), ChequeNo='CHQ002', SysDate=datetime(2023, 2, 2)
        )

        # Master with Bill Booking type
        cls.master3 = BankVoucherPaymentMaster.objects.using(cls.db_alias).create(
            Id=3, CompId=1, FinYearId=2023, BVPNo='BV003', PayAmt=1500.00, AddAmt=10.00,
            Type=4, ECSType=2, PayTo='CUST001', NameOnCheque=None, PaidType=1,
            ChequeDate=datetime(2023, 3, 5), ChequeNo='CHQ003', SysDate=datetime(2023, 3, 6)
        )
        BankVoucherPaymentDetail.objects.using(cls.db_alias).create(
            Id=12, MId=cls.master3, PVEVNO='101', Amount=1500.00, Particular='Payment for Bill'
        )


    def test_bank_voucher_master_creation(self):
        self.assertEqual(self.master1.BVPNo, 'BV001')
        self.assertEqual(self.master1.PayAmt, Decimal('1000.000'))

    def test_bank_voucher_detail_creation(self):
        detail = BankVoucherPaymentDetail.objects.using(self.db_alias).get(Id=10)
        self.assertEqual(detail.MId.BVPNo, 'BV001')
        self.assertEqual(detail.Amount, Decimal('500.000'))

    def test_get_report_data_with_details(self):
        report_data = BankVoucherPaymentMaster.objects.get_report_data(
            voucher_id=self.master1.Id,
            company_id=self.master1.CompId,
            financial_year_id=self.master1.FinYearId,
            db_alias=self.db_alias
        )
        self.assertTrue(len(report_data) > 0)
        row = report_data[0]
        self.assertIsInstance(row, BankVoucherReportData)
        self.assertEqual(row.paid_to, 'John Doe')
        self.assertEqual(row.cheque_date, '15/01/2023')
        self.assertEqual(row.bvp_no, 'BV001')
        self.assertEqual(row.amount, Decimal('500.000')) # Detail amount
        self.assertEqual(row.pay_amount, Decimal('1000.000')) # Master pay amount
        self.assertEqual(row.type_ecs_label, 'Employee Code : ')
        self.assertEqual(row.ecs_code, 'EMP001')
        self.assertEqual(row.invoice_no, 'PI001')
        self.assertEqual(row.invoice_date, '10/01/2023')

    def test_get_report_data_no_details(self):
        report_data = BankVoucherPaymentMaster.objects.get_report_data(
            voucher_id=self.master2.Id,
            company_id=self.master2.CompId,
            financial_year_id=self.master2.FinYearId,
            db_alias=self.db_alias
        )
        self.assertEqual(len(report_data), 1)
        row = report_data[0]
        self.assertEqual(row.paid_to, 'Supplier Name A') # NameOnCheque takes precedence
        self.assertEqual(row.amount, Decimal('200.000')) # Master pay_amt
        self.assertEqual(row.type_ecs_label, 'Supplier Code :')
        self.assertEqual(row.ecs_code, 'SUP001')
        self.assertEqual(row.bill_no, '-')
        self.assertEqual(row.invoice_no, '-')


    def test_get_report_data_bill_booking_type(self):
        report_data = BankVoucherPaymentMaster.objects.get_report_data(
            voucher_id=self.master3.Id,
            company_id=self.master3.CompId,
            financial_year_id=self.master3.FinYearId,
            db_alias=self.db_alias
        )
        self.assertEqual(len(report_data), 1)
        row = report_data[0]
        self.assertEqual(row.paid_to, 'Cash Payment') # PaidType takes precedence
        self.assertEqual(row.bill_no, 'BB-2023-001')
        self.assertEqual(row.invoice_no, '-')
        self.assertEqual(row.type_ecs_label, 'Customer Code :')
        self.assertEqual(row.ecs_code, 'CUST001')

    def test_get_report_data_non_existent_voucher(self):
        report_data = BankVoucherPaymentMaster.objects.get_report_data(
            voucher_id=999,
            company_id=1,
            financial_year_id=2023,
            db_alias=self.db_alias
        )
        self.assertEqual(len(report_data), 0)

class BankVoucherViewsTest(TestCase):
    """
    Integration tests for Django views.
    """
    @classmethod
    def setUpTestData(cls):
        # Set up a test client
        cls.client = Client()

        # Create session data for common use in tests
        session = cls.client.session
        session['compid'] = 1
        session['finyear'] = 2023
        session.save()

        # Create mock data for related tables (as per ModelTest, but for integration)
        Employee.objects.create(EmpId='EMP001', EmployeeName='John Doe', Address='123 Employee St', CompId=1)
        Customer.objects.create(CustomerId='CUST001', CustomerName='Acme Corp', Address='456 Customer Ave', CompId=1)
        Supplier.objects.create(SupplierId='SUP001', SupplierName='Widgets Inc', Address='789 Supplier Rd', CompId=1)
        BillBookingMaster.objects.create(Id=101, BillNo='BB-2023-001')
        PaidType.objects.create(Id=1, Particulars='Cash Payment')


        # Create a BankVoucherPaymentMaster record for testing views
        cls.master_view_test = BankVoucherPaymentMaster.objects.create(
            Id=5, CompId=1, FinYearId=2023, BVPNo='BVVIEW005', PayAmt=2000.00, AddAmt=100.00,
            Type=1, ECSType=1, PayTo='EMP001', NameOnCheque=None, PaidType=None,
            ChequeDate=datetime(2023, 4, 1), ChequeNo='CHQ005', SysDate=datetime(2023, 4, 2)
        )
        BankVoucherPaymentDetail.objects.create(
            Id=50, MId=cls.master_view_test, ProformaInvNo='PI005', InvDate=datetime(2023, 3, 20),
            Amount=2000.00, Particular='Payment for PI005'
        )

    def test_bank_voucher_report_view(self):
        url = reverse('bankvoucher_report', args=[self.master_view_test.Id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/bankvoucher/report.html')
        self.assertContains(response, 'Bank Voucher Payment Advice Report - Voucher #5')
        self.assertContains(response, 'Loading Report Data...') # HTMX placeholder

    def test_bank_voucher_report_table_partial_view(self):
        url = reverse('bankvoucher_report_table_partial', args=[self.master_view_test.Id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/bankvoucher/_report_table.html')
        self.assertContains(response, 'CHQ005')
        self.assertContains(response, 'John Doe')
        self.assertContains(response, '2000.00')

    # --- Generic CRUD View Tests for BankVoucherPaymentMaster ---

    def test_list_view(self):
        url = reverse('bankvoucher_list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/bankvoucher/list.html')
        self.assertContains(response, 'Bank Voucher Payment Masters')
        # Check for HTMX partial load placeholder
        self.assertContains(response, 'id="bankvoucherpaymentmasterTable-container"')

    def test_list_partial_view(self):
        url = reverse('bankvoucher_list_partial')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/bankvoucher/_bankvoucher_table.html')
        self.assertContains(response, 'BVVIEW005') # Check if our created master is in the table

    def test_create_view_get(self):
        url = reverse('bankvoucher_add')
        response = self.client.get(url, HTTP_HX_REQUEST='true') # Simulate HTMX request for modal
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/bankvoucher/form.html')
        self.assertContains(response, 'Add Bank Voucher Payment Master')
        self.assertContains(response, '<form hx-post=')

    def test_create_view_post_success(self):
        url = reverse('bankvoucher_add')
        data = {
            'BVPNo': 'BVNEW001',
            'PayAmt': '3000.00',
            'AddAmt': '0.00',
            'Type': 1,
            'ECSType': 1,
            'PayTo': 'EMP001',
            'NameOnCheque': '',
            'PaidType': 1,
            'ChequeDate': '2023-05-10T10:00:00',
            'ChequeNo': 'NEWCHQ01',
            'SysDate': '2023-05-11T11:00:00',
            'CompId': 1, # These will be set in initial kwargs, but good to have for testing POST
            'FinYearId': 2023
        }
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        
        # Check for HTMX 204 No Content response and HX-Trigger header
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBankVoucherPaymentMasterList')
        
        # Verify object was created
        self.assertTrue(BankVoucherPaymentMaster.objects.filter(BVPNo='BVNEW001').exists())
        self.assertEqual(BankVoucherPaymentMaster.objects.get(BVPNo='BVNEW001').PayTo, 'EMP001')


    def test_update_view_get(self):
        url = reverse('bankvoucher_edit', args=[self.master_view_test.Id])
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/bankvoucher/form.html')
        self.assertContains(response, 'Edit Bank Voucher Payment Master')
        self.assertContains(response, 'BVVIEW005') # Check if existing data is pre-filled

    def test_update_view_post_success(self):
        url = reverse('bankvoucher_edit', args=[self.master_view_test.Id])
        data = {
            'BVPNo': 'BVUPDATED',
            'PayAmt': '2500.00',
            'AddAmt': '0.00',
            'Type': 1,
            'ECSType': 1,
            'PayTo': 'EMP001',
            'NameOnCheque': '',
            'PaidType': 1,
            'ChequeDate': '2023-04-01T10:00:00',
            'ChequeNo': 'CHQ005',
            'SysDate': '2023-04-02T11:00:00',
            'CompId': 1,
            'FinYearId': 2023
        }
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBankVoucherPaymentMasterList')

        self.master_view_test.refresh_from_db()
        self.assertEqual(self.master_view_test.BVPNo, 'BVUPDATED')
        self.assertEqual(self.master_view_test.PayAmt, Decimal('2500.000'))

    def test_delete_view_get(self):
        url = reverse('bankvoucher_delete', args=[self.master_view_test.Id])
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/bankvoucher/confirm_delete.html')
        self.assertContains(response, 'Confirm Delete')
        self.assertContains(response, 'BVVIEW005')

    def test_delete_view_post_success(self):
        # Create a new item to delete so it doesn't affect other tests
        item_to_delete = BankVoucherPaymentMaster.objects.create(
            Id=6, CompId=1, FinYearId=2023, BVPNo='BVDELETE', PayAmt=1.00, AddAmt=0.00,
            Type=1, ECSType=1, PayTo='EMP001', NameOnCheque=None, PaidType=None,
            ChequeDate=datetime(2023, 6, 1), ChequeNo='DELCHQ', SysDate=datetime(2023, 6, 2)
        )
        url = reverse('bankvoucher_delete', args=[item_to_delete.Id])
        response = self.client.post(url, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBankVoucherPaymentMasterList')

        self.assertFalse(BankVoucherPaymentMaster.objects.filter(Id=item_to_delete.Id).exists())

```

---

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
-   **HTMX for dynamic updates:**
    -   The main report view (`report.html`) uses `hx-get` on `bankVoucherReportTable-container` to load the `_report_table.html` partial, which contains the DataTables.
    -   CRUD forms (`form.html`, `confirm_delete.html`) are designed to be loaded into an Alpine.js modal via HTMX `hx-get` on button clicks.
    -   Form submissions (`hx-post`) in `form.html` use `hx-swap="none"` and `hx-trigger` with a custom event (`refreshBankVoucherPaymentMasterList`) to signal the main list to refresh without a full page reload or content swap on the form itself.
    -   Delete operations also use `hx-trigger` to refresh the list.
-   **Alpine.js for UI state management:**
    -   The modal (`#modal`) uses Alpine.js `x-data` and `_="on click add .is-active to #modal"` / `_="on click remove .is-active from me"` for showing/hiding, creating a simple and efficient modal system.
-   **DataTables for list views:**
    -   Both `_report_table.html` and `_bankvoucher_table.html` include DataTables initialization scripts to provide client-side searching, sorting, and pagination. The script is designed to initialize the table only if it hasn't been already, making it robust for HTMX reloads.
-   **No additional JavaScript:** All interactions are handled by HTMX, Alpine.js, and DataTables, eliminating the need for custom, imperative JavaScript.
-   **DRY Templates:** `base.html` is extended, and partial templates are used for reusable components (`_report_table.html`, `_bankvoucher_table.html`, `form.html`, `confirm_delete.html`).

## Final Notes

This comprehensive plan provides a systematic approach to modernize the ASP.NET Bank Voucher Advice Print functionality to Django. By focusing on "fat models, thin views," HTMX, Alpine.js, and DataTables, we ensure a performant, maintainable, and user-friendly solution. The business logic is encapsulated in the model layer, making it easy to test and evolve. The use of automation-friendly structures facilitates a smoother migration process, reducing manual effort and human error.