This document outlines a strategic plan for migrating your existing ASP.NET IOU Payment/Receipt module to a modern Django application. Our approach leverages AI-assisted automation to streamline the transition, focusing on business value and maintainability.

## ASP.NET to Django Conversion Script:

### IMPORTANT RULES - FOLLOW THESE STRICTLY:

-   NEVER include base.html template code in your output - assume it already exists.
-   Focus ONLY on component-specific code for the current module.
-   Always include complete unit tests for models and integration tests for views.
-   Use modern Django 5.0+ patterns and follow best practices.
-   Keep your code clean, efficient, and avoid redundancy.
-   Always generate complete, runnable Django code.

### AutoERP Guidelines:

-   Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
-   Map models to existing database using `managed = False` and `db_table`.
-   Implement DataTables for client-side searching, sorting, and pagination.
-   Use HTMX for dynamic interactions and Alpine.js for UI state management.
-   All templates should extend `core/base.html` (but DO NOT include `base.html` code).
-   Achieve at least 80% test coverage with unit and integration tests.
-   Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
-   Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
The ASP.NET code interacts with several tables. Based on the `SqlDataSource` and direct SQL queries within the C# code-behind, we identify the following:

*   **`tblACC_IOU_Master`**: This is the primary table for IOU payments.
    *   **Columns Inferred:** `Id` (PK), `PaymentDate` (string/datetime, inferred from `Eval("PaymentDate")`), `EmpId` (integer, foreign key to `tblHR_OfficeStaff`), `Amount` (double/decimal), `Reason` (integer, foreign key to `tblACC_IOU_Reasons`), `Narration` (string), `Authorize` (integer/boolean, `0` or `1`), `AuthorizedDate` (string/datetime), `AuthorizedTime` (string/time), `AuthorizedBy` (string), `Recieved` (integer/boolean, `0` or `1`, indicates if receipt is recorded), `SysDate` (string/datetime), `SysTime` (string/time), `SessionId` (string), `CompId` (integer), `FinYearId` (integer).
*   **`tblACC_IOU_Reasons`**: This table stores the reasons for IOU transactions.
    *   **Columns Inferred:** `Id` (PK), `Terms` (string).
*   **`tblHR_OfficeStaff`**: This table contains employee information.
    *   **Columns Inferred:** `EmpId` (PK), `Title` (string), `EmployeeName` (string), `CompId` (integer), `FinYearId` (integer).
*   **`tblACC_IOU_Receipt`**: This table records the receipts against IOU payments.
    *   **Columns Inferred:** `Id` (PK, referenced as `DIdR`), `MId` (integer, foreign key to `tblACC_IOU_Master.Id`), `RecievedAmount` (double/decimal), `ReceiptDate` (string/datetime), `SysDate` (string/datetime), `SysTime` (string/time), `SessionId` (string), `CompId` (integer), `FinYearId` (integer).

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic in the ASP.NET code.

**Instructions:**
The ASP.NET page handles both IOU Payments and IOU Receipts through two `GridView` components, each with distinct functionalities.

*   **IOU Payments (Managed via `GridView2`):**
    *   **Read:** The `BindDataGrid()` method fetches payment data from `tblACC_IOU_Master` (filtered by `Recieved='0'`) and enriches it with employee names from `tblHR_OfficeStaff` and reason terms from `tblACC_IOU_Reasons`.
    *   **Update:** The `GridView2_RowUpdating` event handles edits to existing payments, updating `Amount`, `Reason`, `Narration`, and `Authorize` status in `tblACC_IOU_Master`. It includes validation for amount and ensures non-zero amount.
    *   **Delete:** The `GridView2_RowDeleting` event deletes records from `tblACC_IOU_Master`. Deletion is restricted if authorized.
    *   **Authorize (Special Update):** The `CheckBox1_CheckedChanged` event handles authorization. It updates the `Authorize` flag in `tblACC_IOU_Master` to `1` only if there's sufficient cash balance (`getCashClBalAmt`). This is a critical business rule.
    *   **Validations:** Amount validation (`NumberValidationQty`), required fields (`RequiredFieldValidator`), regular expression validation for amount.
*   **IOU Receipts (Managed via `GridView1`):**
    *   **Read:** The `BindDataGrid_Receipt()` method fetches authorized IOU records (`Authorize='1'`) from `tblACC_IOU_Master` and attempts to fetch corresponding receipt details from `tblACC_IOU_Receipt`.
    *   **Create/Add Receipt:** The `GridView1_RowCommand` (for `Add` command) inserts a new record into `tblACC_IOU_Receipt` and sets `Recieved='1'` in the corresponding `tblACC_IOU_Master` entry. It validates the received amount (must not exceed original amount) and receipt date.
    *   **Update Receipt:** The `GridView1_RowUpdating` event handles edits to existing receipt entries in `tblACC_IOU_Receipt`. It includes similar amount and date validations.
    *   **Delete Receipt:** The `GridView1_RowCommand` (for `del` command) deletes a record from `tblACC_IOU_Receipt` and resets `Recieved='0'` in `tblACC_IOU_Master`.
    *   **Validations:** Amount validation (`NumberValidationQty`), date format validation (`DateValidation`), and logical validation (received amount <= original amount).

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The ASP.NET page uses a `TabContainer` to separate "Payment" and "Receipt" views, each containing a `GridView`.

*   **Layout:**
    *   `TabContainer` (`TabContainer1`): Will be replaced by a simple HTML tab structure, with content loaded dynamically using HTMX.
*   **Data Grids:**
    *   `GridView2` (for Payments): Displays payment `Id`, `PaymentDate`, `EmpName`, `Amount`, `Reason`, `Narration`, and `Authorize` status. It supports editing (`Edit`, `Update`, `Cancel` buttons), deletion (`Delete` button), and direct authorization (`CheckBox1`). We will use DataTables for this.
    *   `GridView1` (for Receipts): Displays receipt `IdR` (Payment Id), `Pay. Date` (SysDate), `Employee Name`, `Reason`, `Narration`, `Amount`, `Rec. Amt` (Received Amount), `Receipt Date`. It supports editing (`Edit`, `Update`, `Cancel` buttons), adding a receipt (`Add` button visible conditionally), and deleting a receipt (`Delete` button). We will use DataTables for this.
*   **Input Controls within Grids (for editing/adding rows):**
    *   `TextBox`: For `Amount`, `Narration`, `RecivedAmtR`, `ReceiptDate`. These will be Django form fields with appropriate widgets.
    *   `DropDownList`: For `Reason`. This will be a Django `ModelChoiceField`.
    *   `CheckBox`: For `Authorize` status. This will be a Django `BooleanField`.
    *   `LinkButton`, `Button`: For `Edit`, `Update`, `Cancel`, `Delete`, `Add`. These will be replaced by HTMX-enabled buttons triggering modal forms or direct actions.
*   **Validation Controls:**
    *   `RequiredFieldValidator`, `RegularExpressionValidator`: These client-side validations will be integrated into Django forms' validation logic and potentially enhanced with client-side Alpine.js for immediate feedback.
*   **External Data Source:**
    *   `SqlDataSource1`: Used to populate the `Reason` dropdown. This data will be fetched via Django ORM.
*   **Client-side JavaScript:**
    *   `confirmationUpdate()`, `confirmationDelete()`, `confirmationAdd()`: JavaScript confirmation dialogs. These will be replaced by Alpine.js for modal-based confirmations before HTMX actions.
    *   `CalendarExtender`: For date pickers. This will be replaced by HTML5 `input type="date"` or a simple Alpine.js integration with a lightweight date picker if more advanced UI is needed.

### Step 4: Generate Django Code

We will create a Django application named `transactions` to house this module.

#### 4.1 Models (`transactions/models.py`)

This section translates the identified database tables and relationships into Django models, including methods to encapsulate business logic.

```python
from django.db import models
from django.utils import timezone
from decimal import Decimal

# Assuming a central 'core' app or utility for global functions like session data.
# For simplicity, CompId, FinYearId, and SessionId (username) are hardcoded or
# will be passed from request context in actual implementation via a manager or signal.

class IouReason(models.Model):
    """
    Maps to tblACC_IOU_Reasons for IOU transaction reasons.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_IOU_Reasons'
        verbose_name = 'IOU Reason'
        verbose_name_plural = 'IOU Reasons'

    def __str__(self):
        return self.terms or 'N/A'

class Employee(models.Model):
    """
    Maps to tblHR_OfficeStaff for employee details.
    """
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50)
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.title or ''}.{self.employee_name or ''} [{self.emp_id}]".strip()

    @classmethod
    def get_employee_name_by_id(cls, emp_id, comp_id, fin_year_id):
        """Fetches formatted employee name by EmpId, Company, and Financial Year."""
        try:
            employee = cls.objects.get(emp_id=emp_id, comp_id=comp_id, fin_year_id=fin_year_id)
            return str(employee)
        except cls.DoesNotExist:
            return "Unknown Employee"


class IouPaymentManager(models.Manager):
    """
    Custom manager for IouPayment to encapsulate common queries and business logic.
    """
    def get_queryset(self):
        return super().get_queryset()

    def get_payments_for_grid(self, comp_id, fin_year_id):
        """
        Mimics BindDataGrid logic, fetching payments not yet received.
        """
        # Note: 'Recieved' should be a boolean field in the database.
        # Assuming 0=False, 1=True if it's an INT in the legacy schema.
        payments = self.filter(
            comp_id=comp_id,
            fin_year_id__lte=fin_year_id, # 'FinYearId<=' filter from ASP.NET
            recieved=False
        ).order_by('-id')

        # Annotate with related data to reduce queries in templates/views
        # This is for display purposes, actual objects hold FKs
        return payments.select_related('reason_obj', 'employee_obj')


class IouPayment(models.Model):
    """
    Maps to tblACC_IOU_Master for IOU payments.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    payment_date = models.DateField(db_column='PaymentDate', blank=True, null=True)
    emp_id = models.CharField(db_column='EmpId', max_length=50, blank=True, null=True) # Stored EmpId as char, not FK directly
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=3, default=0.0)
    reason = models.IntegerField(db_column='Reason', blank=True, null=True) # Stored Reason ID as int, not FK directly
    narration = models.CharField(db_column='Narration', max_length=500, blank=True, null=True)
    authorize = models.BooleanField(db_column='Authorize', default=False)
    authorized_date = models.DateField(db_column='AuthorizedDate', blank=True, null=True)
    authorized_time = models.TimeField(db_column='AuthorizedTime', blank=True, null=True)
    authorized_by = models.CharField(db_column='AuthorizedBy', max_length=50, blank=True, null=True)
    recieved = models.BooleanField(db_column='Recieved', default=False)
    sys_date = models.DateField(db_column='SysDate', default=timezone.now)
    sys_time = models.TimeField(db_column='SysTime', default=timezone.now)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    objects = IouPaymentManager() # Assign custom manager

    class Meta:
        managed = False
        db_table = 'tblACC_IOU_Master'
        verbose_name = 'IOU Payment'
        verbose_name_plural = 'IOU Payments'

    def __str__(self):
        return f"IOU #{self.id} - {self.get_employee_name()}: {self.amount}"

    # Helper methods for related data (to mimic ASP.NET data joins)
    @property
    def reason_obj(self):
        try:
            return IouReason.objects.get(id=self.reason)
        except IouReason.DoesNotExist:
            return None

    @property
    def employee_obj(self):
        # This assumes current CompId/FinYearId are available, e.g., from request context or a global setting
        # For a truly robust system, employee linking might be a direct FK or use a more sophisticated lookup.
        # Given the legacy ASP.NET's direct string parsing, this is a reasonable mapping.
        try:
            # The EmpId in IOU_Master is likely the ID from tblHR_OfficeStaff
            # In a real system, we'd make this a ForeignKey directly.
            # For `managed=False`, we simulate the join.
            # Using the instance's comp_id and fin_year_id as context.
            return Employee.objects.get(emp_id=self.emp_id, comp_id=self.comp_id, fin_year_id=self.fin_year_id)
        except Employee.DoesNotExist:
            return None

    def get_employee_name(self):
        employee = self.employee_obj
        return str(employee) if employee else "Unknown Employee"

    def get_reason_terms(self):
        reason = self.reason_obj
        return str(reason) if reason else "Unknown Reason"

    # Business Logic Methods (Fat Model)
    def authorize_payment(self, user_session_id, comp_id, fin_year_id, cash_balance_checker_func):
        """
        Authorizes the IOU payment after checking cash balance.
        `cash_balance_checker_func` is a callable that returns the current cash balance.
        """
        if self.authorize: # Already authorized
            return True, "Payment already authorized."

        current_cash = cash_balance_checker_func(comp_id, fin_year_id) # Call external function
        if (current_cash - self.amount) >= 0:
            self.authorize = True
            self.authorized_date = timezone.now().date()
            self.authorized_time = timezone.now().time()
            self.authorized_by = user_session_id
            self.sys_date = timezone.now().date() # Update sys_date/time on authorization
            self.sys_time = timezone.now().time()
            self.session_id = user_session_id
            self.save(update_fields=[
                'authorize', 'authorized_date', 'authorized_time', 'authorized_by',
                'sys_date', 'sys_time', 'session_id'
            ])
            return True, "Payment authorized successfully."
        else:
            return False, "Insufficient Cash."

    def delete_payment(self):
        """
        Deletes the IOU payment. Prevents deletion if authorized.
        """
        if self.authorize:
            return False, "Cannot delete an authorized payment."
        self.delete()
        return True, "Payment deleted successfully."


class IouReceiptManager(models.Manager):
    """
    Custom manager for IouReceipt to encapsulate common queries and business logic.
    """
    def get_queryset(self):
        return super().get_queryset()

    def get_receipts_for_grid(self, comp_id, fin_year_id):
        """
        Mimics BindDataGrid_Receipt logic, fetching authorized IOU payments and their receipts.
        """
        # Fetching IouPayments that are authorized
        iou_payments = IouPayment.objects.filter(
            comp_id=comp_id,
            fin_year_id__lte=fin_year_id,
            authorize=True
        ).order_by('-id').select_related('reason_obj', 'employee_obj')

        # We need to structure this to match the ASP.NET grid output,
        # which combines master and receipt data.
        # This will be done in the serializer/view logic for DataTables.
        return iou_payments


class IouReceipt(models.Model):
    """
    Maps to tblACC_IOU_Receipt for IOU receipts.
    """
    id = models.IntegerField(db_column='Id', primary_key=True) # DIdR
    m_id = models.IntegerField(db_column='MId') # Foreign Key to IouPayment.Id
    receipt_date = models.DateField(db_column='ReceiptDate', blank=True, null=True)
    recieved_amount = models.DecimalField(db_column='RecievedAmount', max_digits=18, decimal_places=3, default=0.0)
    sys_date = models.DateField(db_column='SysDate', default=timezone.now)
    sys_time = models.TimeField(db_column='SysTime', default=timezone.now)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    objects = IouReceiptManager() # Assign custom manager

    class Meta:
        managed = False
        db_table = 'tblACC_IOU_Receipt'
        verbose_name = 'IOU Receipt'
        verbose_name_plural = 'IOU Receipts'

    def __str__(self):
        return f"Receipt for IOU MId #{self.m_id} - {self.recieved_amount}"

    @property
    def iou_payment(self):
        """Retrieves the related IouPayment object."""
        try:
            return IouPayment.objects.get(id=self.m_id)
        except IouPayment.DoesNotExist:
            return None

    # Business Logic Methods (Fat Model)
    def add_receipt(self, user_session_id, comp_id, fin_year_id):
        """
        Adds a new receipt and updates the corresponding IOU Payment status.
        """
        iou = self.iou_payment
        if not iou:
            return False, "Associated IOU Payment not found."

        if self.recieved_amount < 0:
            return False, "Received amount cannot be negative."

        if self.recieved_amount > iou.amount:
            return False, "Received amount exceeds IOU payment amount."

        self.sys_date = timezone.now().date()
        self.sys_time = timezone.now().time()
        self.session_id = user_session_id
        self.comp_id = comp_id
        self.fin_year_id = fin_year_id
        self.save()

        iou.recieved = True
        iou.save(update_fields=['recieved'])
        return True, "Receipt added successfully."

    def update_receipt(self):
        """
        Updates an existing receipt.
        """
        iou = self.iou_payment
        if not iou:
            return False, "Associated IOU Payment not found."

        if self.recieved_amount < 0:
            return False, "Received amount cannot be negative."

        if self.recieved_amount > iou.amount:
            return False, "Received amount exceeds IOU payment amount."

        self.save()
        return True, "Receipt updated successfully."

    def delete_receipt(self):
        """
        Deletes a receipt and resets the corresponding IOU Payment status.
        """
        iou = self.iou_payment
        if iou:
            iou.recieved = False
            iou.save(update_fields=['recieved'])
        self.delete()
        return True, "Receipt deleted successfully."

# Placeholder for Cash Balance Function (simulating clsFunctions.getCashClBalAmt)
# In a real ERP, this would come from a dedicated accounting module or service.
def get_cash_current_balance(comp_id, fin_year_id):
    """
    Simulates fetching current cash in hand.
    For demonstration, returns a fixed value. In production, this would query
    actual cash balance from accounting tables.
    """
    # This would involve querying another financial table, e.g., tblACC_CashBook
    # For this migration, we assume it's an external dependency.
    return Decimal(100000.00) # Example balance
```

#### 4.2 Forms (`transactions/forms.py`)

Django forms for handling user input for IOU Payments and Receipts.

```python
from django import forms
from .models import IouPayment, IouReason, IouReceipt, Employee # Import Employee for choices if needed

class IouPaymentForm(forms.ModelForm):
    # Dynamically populate reasons if needed, but ModelChoiceField handles it
    reason_id = forms.ModelChoiceField(
        queryset=IouReason.objects.all(),
        to_field_name='id', # Map to the 'id' column in IouReason
        empty_label="Select Reason",
        label="Reason",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    
    # Employee name is displayed, but EmpId is saved. We'll handle this in the view/model.
    # For simplicity, if EmpId is a dropdown, you'd use a ModelChoiceField.
    # If it's a text input with autocompletion (like ASP.NET's GetCompletionList),
    # it requires custom JS/HTMX for lookup and setting the hidden EmpId.
    # For this exercise, we'll assume EmpId is just displayed for Payments, and
    # for update we'll retain the existing emp_id and let the model handle the lookup.

    class Meta:
        model = IouPayment
        fields = ['amount', 'reason', 'narration', 'authorize'] # EmpId is not directly editable here
        widgets = {
            'amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.001'}),
            'narration': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'authorize': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500'}),
            'reason': forms.HiddenInput(), # reason_id field will handle this
        }
        labels = {
            'narration': 'Narration',
            'amount': 'Amount',
            'authorize': 'Authorized',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance.pk:
            self.fields['reason_id'].initial = self.instance.reason
            # Disable authorize checkbox if already authorized
            if self.instance.authorize:
                self.fields['authorize'].widget.attrs['disabled'] = 'disabled'

    def clean_amount(self):
        amount = self.cleaned_data.get('amount')
        if amount is not None and amount <= 0:
            raise forms.ValidationError("Amount must be a positive value.")
        return amount

    def clean(self):
        cleaned_data = super().clean()
        reason_id = cleaned_data.get('reason_id')
        cleaned_data['reason'] = reason_id.id if reason_id else None # Map selected reason object back to reason ID
        return cleaned_data

class IouReceiptForm(forms.ModelForm):
    # Hidden field for MId, as it's passed from the IouPayment row
    m_id = forms.IntegerField(widget=forms.HiddenInput(), required=True)
    receipt_date_str = forms.CharField(
        label="Receipt Date",
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
        required=True
    )

    class Meta:
        model = IouReceipt
        fields = ['recieved_amount', 'm_id'] # receipt_date is handled separately via receipt_date_str
        widgets = {
            'recieved_amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.001'}),
        }
        labels = {
            'recieved_amount': 'Received Amount',
        }

    def __init__(self, *args, **kwargs):
        iou_payment_instance = kwargs.pop('iou_payment_instance', None)
        super().__init__(*args, **kwargs)

        if iou_payment_instance:
            self.fields['m_id'].initial = iou_payment_instance.id
            # Set max received amount based on the parent IOU
            self.fields['recieved_amount'].widget.attrs['max'] = iou_payment_instance.amount
            self.fields['recieved_amount'].help_text = f"Max amount: {iou_payment_instance.amount}"
            # If it's an update, load existing receipt data
            if self.instance.pk:
                self.fields['recieved_amount'].initial = self.instance.recieved_amount
                self.fields['receipt_date_str'].initial = self.instance.receipt_date.strftime('%Y-%m-%d') if self.instance.receipt_date else ''
            else: # For add, default receipt date to today
                self.fields['receipt_date_str'].initial = timezone.now().date().strftime('%Y-%m-%d')
        else:
            # If no iou_payment_instance provided (e.g. for creating an empty form),
            # disable receipt date/amount fields, or handle this in view.
            self.fields['recieved_amount'].widget.attrs['disabled'] = 'disabled'
            self.fields['receipt_date_str'].widget.attrs['disabled'] = 'disabled'

    def clean_recieved_amount(self):
        amount = self.cleaned_data.get('recieved_amount')
        if amount is not None and amount <= 0:
            raise forms.ValidationError("Received amount must be a positive value.")
        return amount

    def clean_receipt_date_str(self):
        date_str = self.cleaned_data.get('receipt_date_str')
        if not date_str:
            raise forms.ValidationError("Receipt Date is required.")
        try:
            # Convert 'YYYY-MM-DD' from HTML5 date input to date object
            return forms.DateField().clean(date_str)
        except forms.ValidationError:
            # ASP.NET used dd-MM-yyyy. If user inputs that, convert it.
            try:
                return timezone.datetime.strptime(date_str, '%d-%m-%Y').date()
            except ValueError:
                raise forms.ValidationError("Invalid date format. Use YYYY-MM-DD or DD-MM-YYYY.")

    def clean(self):
        cleaned_data = super().clean()
        m_id = cleaned_data.get('m_id')
        recieved_amount = cleaned_data.get('recieved_amount')
        receipt_date = cleaned_data.get('receipt_date_str') # Already cleaned to date object

        if m_id and recieved_amount is not None:
            try:
                iou_payment = IouPayment.objects.get(id=m_id)
                if recieved_amount > iou_payment.amount:
                    self.add_error('recieved_amount', "Received amount cannot exceed original IOU amount.")
            except IouPayment.DoesNotExist:
                self.add_error('m_id', "Associated IOU Payment does not exist.")

        # Set the actual model field for receipt_date from the cleaned string field
        self.instance.receipt_date = receipt_date
        return cleaned_data

```

#### 4.3 Views (`transactions/views.py`)

We'll create CBVs for each CRUD operation and partial views for HTMX rendering.

```python
from django.views.generic import ListView, UpdateView, DeleteView, View
from django.shortcuts import get_object_or_404, render
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db import transaction

from .models import IouPayment, IouReceipt, get_cash_current_balance # Import the new models and cash func
from .forms import IouPaymentForm, IouReceiptForm

# Constants for common context (in a real app, these would come from user session/settings)
# For demo purposes, we'll hardcode or retrieve from a simple mock.
# In a real app, you'd get this from request.user profile or session.
CURRENT_COMP_ID = 1
CURRENT_FIN_YEAR_ID = 1
CURRENT_USER_SESSION_ID = "admin" # Mock username


class IouPaymentReceiptDashboardView(ListView):
    """
    Main dashboard view for IOU Payments and Receipts, acting as the main .aspx page.
    It will load both payment and receipt tables via HTMX.
    """
    template_name = 'transactions/iou_payment_receipt_dashboard.html'
    context_object_name = 'iou_payments_pending'
    model = IouPayment # Although not directly used for the main query, required by ListView

    def get_queryset(self):
        # Initial load: no data. Data will be fetched by HTMX.
        return IouPayment.objects.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = "IOU: Payment/Receipt"
        return context

class IouPaymentTablePartialView(ListView):
    """
    Renders the partial HTML for the IOU Payments DataTable.
    This view is specifically targeted by HTMX requests.
    """
    model = IouPayment
    template_name = 'transactions/_iou_payment_table.html'
    context_object_name = 'iou_payments'

    def get_queryset(self):
        # Fetch pending payments for the payment grid
        return IouPayment.objects.get_payments_for_grid(
            comp_id=CURRENT_COMP_ID,
            fin_year_id=CURRENT_FIN_YEAR_ID
        )

    def render_to_response(self, context, **response_kwargs):
        # Ensure HTMX response is a partial HTML
        return super().render_to_response(context, **response_kwargs)


class IouPaymentUpdateView(UpdateView):
    """
    Handles editing of an IOU Payment. Renders a form within a modal via HTMX.
    """
    model = IouPayment
    form_class = IouPaymentForm
    template_name = 'transactions/_iou_payment_form.html' # Partial template for HTMX modal

    def get_object(self, queryset=None):
        # Ensure the object belongs to the current company/financial year if needed
        return get_object_or_404(IouPayment, pk=self.kwargs['pk'], comp_id=CURRENT_COMP_ID, fin_year_id__lte=CURRENT_FIN_YEAR_ID)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # Pass additional context for the form, if needed (e.g., employee options based on current company)
        return kwargs

    def form_valid(self, form):
        # Business logic for authorization is in the model.
        # Handle authorization separately if the checkbox changes and logic dictates.
        # For a fat model, the save method should contain this.
        # However, the ASP.NET code shows authorization as a separate checkbox click action,
        # not part of a general update. So we'll keep `authorize_payment` as a dedicated action.
        
        # If 'authorize' checkbox is part of the form, it will be handled by default save.
        # If the original ASP.NET required specific cash checks for authorization on form save,
        # that logic would need to be re-added here or in the model's save method.
        
        with transaction.atomic():
            self.object = form.save(commit=False)
            self.object.session_id = CURRENT_USER_SESSION_ID
            self.object.sys_date = timezone.now().date()
            self.object.sys_time = timezone.now().time()
            # EmpId should ideally come from a hidden field or lookup,
            # but legacy code binds to lblEmpName, so we assume it's not changed on edit form.
            # We explicitly prevent overwriting emp_id unless it's sent from the form.
            self.object.save() # This calls the model's save method.

        messages.success(self.request, 'IOU Payment updated successfully.')
        if self.request.headers.get('HX-Request'):
            # Return no content for HTMX success, triggering a client-side event to refresh list
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshIouPaymentList'
                }
            )
        return super().form_valid(form) # Fallback for non-HTMX requests

    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            # For HTMX, re-render the form with errors
            return render(self.request, self.template_name, {'form': form}, status=400)
        return super().form_invalid(form)


class IouPaymentDeleteView(DeleteView):
    """
    Handles deleting an IOU Payment. Renders a confirmation within a modal via HTMX.
    """
    model = IouPayment
    template_name = 'transactions/_iou_payment_confirm_delete.html' # Partial template
    success_url = reverse_lazy('iou_payment_receipt_dashboard') # Not directly used for HTMX

    def get_object(self, queryset=None):
        return get_object_or_404(IouPayment, pk=self.kwargs['pk'], comp_id=CURRENT_COMP_ID, fin_year_id__lte=CURRENT_FIN_YEAR_ID)

    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        success, message = self.object.delete_payment() # Business logic in model

        if success:
            messages.success(self.request, message)
            if request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshIouPaymentList'
                    }
                )
            return super().delete(request, *args, **kwargs) # Fallback for non-HTMX
        else:
            messages.error(self.request, message)
            if request.headers.get('HX-Request'):
                # Re-render the confirmation dialog with an error message
                return render(self.request, self.template_name, {'object': self.object, 'error_message': message}, status=400)
            return self.http_method_not_allowed(request) # Or redirect with error


class IouPaymentAuthorizeView(View):
    """
    Handles the authorization of an IOU Payment via HTMX.
    This replaces the CheckBox1_CheckedChanged event.
    """
    def post(self, request, pk):
        iou_payment = get_object_or_404(IouPayment, pk=pk, comp_id=CURRENT_COMP_ID, fin_year_id__lte=CURRENT_FIN_YEAR_ID)

        # Call the model's authorize method
        success, message = iou_payment.authorize_payment(
            user_session_id=CURRENT_USER_SESSION_ID,
            comp_id=CURRENT_COMP_ID,
            fin_year_id=CURRENT_FIN_YEAR_ID,
            cash_balance_checker_func=get_cash_current_balance # Pass the callable
        )

        if success:
            messages.success(request, message)
            return HttpResponse(
                status=204, # No content, just triggers a client-side refresh
                headers={
                    'HX-Trigger': 'refreshIouPaymentList, refreshIouReceiptList' # Refresh both grids
                }
            )
        else:
            messages.error(request, message)
            return HttpResponse(message, status=400) # Return error message for HTMX error handling


class IouReceiptTablePartialView(ListView):
    """
    Renders the partial HTML for the IOU Receipts DataTable.
    This view is specifically targeted by HTMX requests.
    """
    model = IouReceipt
    template_name = 'transactions/_iou_receipt_table.html'
    context_object_name = 'iou_receipts_data' # Name for the combined data

    def get_queryset(self):
        # Fetch authorized IOU payments to form the basis of the receipt grid
        return IouReceipt.objects.get_receipts_for_grid(
            comp_id=CURRENT_COMP_ID,
            fin_year_id=CURRENT_FIN_YEAR_ID
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Prepare data structure to mimic ASP.NET's combined grid data
        # Each item in 'iou_receipts_data' should have properties like IdR, PaymentDateR, EmpNameR, etc.
        receipt_data = []
        for iou_payment in context['iou_receipts_data']:
            iou_receipt = None
            try:
                # Try to get the associated receipt
                iou_receipt = IouReceipt.objects.get(m_id=iou_payment.id, comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID)
            except IouReceipt.DoesNotExist:
                pass # No receipt yet

            receipt_data.append({
                'IdR': iou_payment.id,
                'PaymentDateR': iou_payment.sys_date.strftime('%d-%m-%Y') if iou_payment.sys_date else '', # SysDate from master
                'EmpNameR': iou_payment.get_employee_name(),
                'AmountR': iou_payment.amount,
                'ReasonR': iou_payment.get_reason_terms(),
                'NarrationR': iou_payment.narration,
                'AuthorizedR': iou_payment.authorize,
                'RecievedAmtR': iou_receipt.recieved_amount if iou_receipt else 0,
                'ReceiptDateR': iou_receipt.receipt_date.strftime('%d-%m-%Y') if iou_receipt and iou_receipt.receipt_date else '',
                'DIdR': iou_receipt.id if iou_receipt else 0, # Receipt ID
                'is_received': iou_payment.recieved # Flag for conditional rendering
            })
        context['iou_receipts_data'] = receipt_data
        return context

    def render_to_response(self, context, **response_kwargs):
        return super().render_to_response(context, **response_kwargs)


class IouReceiptCreateUpdateView(View):
    """
    Handles adding (if no receipt exists) or updating (if receipt exists)
    an IOU Receipt. Renders a form within a modal via HTMX.
    """
    template_name = 'transactions/_iou_receipt_form.html' # Partial template

    def get(self, request, pk):
        iou_payment = get_object_or_404(IouPayment, pk=pk, comp_id=CURRENT_COMP_ID, fin_year_id__lte=CURRENT_FIN_YEAR_ID)
        iou_receipt = None
        try:
            iou_receipt = IouReceipt.objects.get(m_id=pk, comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID)
            form = IouReceiptForm(instance=iou_receipt, iou_payment_instance=iou_payment)
        except IouReceipt.DoesNotExist:
            form = IouReceiptForm(iou_payment_instance=iou_payment)
        return render(request, self.template_name, {'form': form, 'iou_payment': iou_payment})

    def post(self, request, pk):
        iou_payment = get_object_or_404(IouPayment, pk=pk, comp_id=CURRENT_COMP_ID, fin_year_id__lte=CURRENT_FIN_YEAR_ID)
        iou_receipt = None
        try:
            iou_receipt = IouReceipt.objects.get(m_id=pk, comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID)
            form = IouReceiptForm(request.POST, instance=iou_receipt, iou_payment_instance=iou_payment)
        except IouReceipt.DoesNotExist:
            form = IouReceiptForm(request.POST, iou_payment_instance=iou_payment)

        if form.is_valid():
            with transaction.atomic():
                receipt_obj = form.save(commit=False) # Get the instance, don't save yet
                if not receipt_obj.pk: # If it's a new receipt
                    success, message = receipt_obj.add_receipt(
                        user_session_id=CURRENT_USER_SESSION_ID,
                        comp_id=CURRENT_COMP_ID,
                        fin_year_id=CURRENT_FIN_YEAR_ID
                    )
                else: # If it's an existing receipt being updated
                    success, message = receipt_obj.update_receipt()

            if success:
                messages.success(request, message)
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshIouReceiptList, refreshIouPaymentList' # Refresh both grids
                    }
                )
            else:
                messages.error(request, message)
                # Re-render form with error (fat model's error message)
                return render(request, self.template_name, {'form': form, 'iou_payment': iou_payment, 'error_message': message}, status=400)
        else:
            # Form is invalid, re-render with errors
            return render(request, self.template_name, {'form': form, 'iou_payment': iou_payment}, status=400)


class IouReceiptDeleteView(DeleteView):
    """
    Handles deleting an IOU Receipt. Renders a confirmation within a modal via HTMX.
    """
    model = IouReceipt
    template_name = 'transactions/_iou_receipt_confirm_delete.html' # Partial template
    success_url = reverse_lazy('iou_payment_receipt_dashboard') # Not directly used for HTMX

    def get_object(self, queryset=None):
        # We need both MId (from URL) and DIdR (from URL) for the legacy code.
        # Here, DIdR is the PK of IouReceipt. MId is IouPayment.Id
        return get_object_or_404(IouReceipt, pk=self.kwargs['pk'], m_id=self.kwargs['iou_payment_id'],
                                 comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID)

    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        success, message = self.object.delete_receipt() # Business logic in model

        if success:
            messages.success(self.request, message)
            if request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshIouReceiptList, refreshIouPaymentList' # Refresh both grids
                    }
                )
            return super().delete(request, *args, **kwargs) # Fallback for non-HTMX
        else:
            messages.error(self.request, message)
            if request.headers.get('HX-Request'):
                return render(self.request, self.template_name, {'object': self.object, 'error_message': message}, status=400)
            return self.http_method_not_allowed(request) # Or redirect with error

```

#### 4.4 Templates (`transactions/templates/transactions/`)

These templates define the UI for the IOU Payment/Receipt module, leveraging HTMX, Alpine.js, and DataTables for a dynamic user experience.

**`iou_payment_receipt_dashboard.html`** (Main Page)

```html
{% extends 'core/base.html' %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-blue-600 text-white p-4 rounded-t-lg shadow-md mb-4">
        <h1 class="text-xl font-bold">{{ page_title }}</h1>
    </div>

    <div x-data="{ activeTab: 'payment' }" class="bg-white rounded-lg shadow-lg">
        <!-- Tab Headers -->
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-4" aria-label="Tabs">
                <button @click="activeTab = 'payment'"
                        :class="{ 'border-indigo-500 text-indigo-600': activeTab === 'payment', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'payment' }"
                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ease-in-out">
                    Payment
                </button>
                <button @click="activeTab = 'receipt'"
                        :class="{ 'border-indigo-500 text-indigo-600': activeTab === 'receipt', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'receipt' }"
                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ease-in-out">
                    Receipt
                </button>
            </nav>
        </div>

        <!-- Tab Content -->
        <div class="p-4">
            <div x-show="activeTab === 'payment'"
                 hx-trigger="load, refreshIouPaymentList from:body"
                 hx-get="{% url 'iou_payment_table' %}"
                 hx-swap="innerHTML"
                 hx-indicator="#payment-loader"
                 class="min-h-[430px] relative">
                <div id="payment-loader" class="htmx-indicator absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600 ml-3">Loading Payments...</p>
                </div>
                <!-- Payment DataTable will be loaded here via HTMX -->
            </div>

            <div x-show="activeTab === 'receipt'"
                 hx-trigger="load, refreshIouReceiptList from:body"
                 hx-get="{% url 'iou_receipt_table' %}"
                 hx-swap="innerHTML"
                 hx-indicator="#receipt-loader"
                 class="min-h-[430px] relative">
                <div id="receipt-loader" class="htmx-indicator absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600 ml-3">Loading Receipts...</p>
                </div>
                <!-- Receipt DataTable will be loaded here via HTMX -->
            </div>
        </div>
    </div>

    <!-- Universal Modal for forms and confirmations -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden"
         x-data="{ show: false }"
         x-show="show"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0 scale-95"
         x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100 scale-100"
         x-transition:leave-end="opacity-0 scale-95"
         @click.self="show = false"
         @refreshIouPaymentList.window="show = false"
         @refreshIouReceiptList.window="show = false"
         _="on click if event.target.id == 'modal' remove .is-active from me
            on htmx:afterSwap put .is-active on #modal">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"
             @htmx:afterRequest.window="
                if (event.detail.successful) {
                    show = true
                } else {
                    show = false
                }
             ">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('modalController', () => ({
            showModal: false,
            openModal() { this.showModal = true; },
            closeModal() { this.showModal = false; },
        }));
    });
</script>
{% endblock %}
```

**`_iou_payment_table.html`** (Partial for Payment List)

```html
<table id="iouPaymentTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reason</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Narration</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Authorize</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for payment in iou_payments %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ payment.payment_date|date:"d-m-Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ payment.get_employee_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ payment.amount|floatformat:3 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ payment.get_reason_terms }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ payment.narration }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                <input type="checkbox" {% if payment.authorize %}checked disabled{% endif %}
                       class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                       hx-post="{% url 'iou_payment_authorize' payment.id %}"
                       hx-confirm="{% if not payment.authorize %}Are you sure you want to authorize this payment?{% else %}This payment is already authorized and cannot be un-authorized.{% endif %}"
                       hx-swap="none"
                       {% if payment.authorize %}onclick="return false;"{% endif %}
                       >
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                <button
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'iou_payment_edit' payment.id %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded {% if payment.authorize %}opacity-50 cursor-not-allowed{% endif %}"
                    hx-get="{% url 'iou_payment_delete' payment.id %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    {% if payment.authorize %}disabled{% endif %}
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="8" class="py-4 px-4 text-center text-gray-500">No IOU payments to display!</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // DataTables initialization
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists to prevent re-initialization issues with HTMX
        if ($.fn.DataTable.isDataTable('#iouPaymentTable')) {
            $('#iouPaymentTable').DataTable().destroy();
        }
        $('#iouPaymentTable').DataTable({
            "pageLength": 20, // PageSize from ASP.NET GridView2
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [6, 7] } // Disable sorting for Authorize and Actions columns
            ]
        });
    });
</script>
```

**`_iou_payment_form.html`** (Partial for Payment Edit Form)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Edit IOU Payment #{{ form.instance.id }}</h3>
    <form hx-post="{% url 'iou_payment_edit' form.instance.id %}" hx-swap="none">
        {% csrf_token %}

        <div class="space-y-4">
            <div class="mb-4">
                <label for="id_amount" class="block text-sm font-medium text-gray-700">Amount</label>
                {{ form.amount }}
                {% if form.amount.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.amount.errors }}</p>
                {% endif %}
            </div>

            <div class="mb-4">
                <label for="id_reason_id" class="block text-sm font-medium text-gray-700">Reason</label>
                {{ form.reason_id }}
                {% if form.reason_id.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.reason_id.errors }}</p>
                {% endif %}
            </div>

            <div class="mb-4">
                <label for="id_narration" class="block text-sm font-medium text-gray-700">Narration</label>
                {{ form.narration }}
                {% if form.narration.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.narration.errors }}</p>
                {% endif %}
            </div>

            <div class="mb-4 flex items-center">
                {{ form.authorize }}
                <label for="id_authorize" class="ml-2 block text-sm font-medium text-gray-700">Authorized</label>
                {% if form.authorize.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.authorize.errors }}</p>
                {% endif %}
                <span class="text-gray-500 text-xs ml-2"> (Authorization handled by checkbox in list view)</span>
            </div>
            
            {{ form.reason }} {# Hidden field for actual reason ID, populated by reason_id ModelChoiceField #}

        </div>

        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Update
            </button>
        </div>
    </form>
</div>
```

**`_iou_payment_confirm_delete.html`** (Partial for Payment Delete Confirmation)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete IOU Payment</h3>
    {% if error_message %}
    <p class="text-red-600 mb-4">{{ error_message }}</p>
    {% else %}
    <p class="text-gray-700 mb-5">Are you sure you want to delete IOU Payment #{{ object.id }} for <strong>{{ object.get_employee_name }}</strong>, amount <strong>{{ object.amount }}</strong>?</p>
    {% endif %}
    <div class="mt-6 flex items-center justify-end space-x-4">
        <button
            type="button"
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        {% if not error_message %}
        <button
            hx-post="{% url 'iou_payment_delete' object.id %}"
            hx-swap="none"
            hx-confirm="This action cannot be undone."
            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            Delete
        </button>
        {% endif %}
    </div>
</div>
```

**`_iou_receipt_table.html`** (Partial for Receipt List)

```html
<table id="iouReceiptTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pay. Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reason</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Narration</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Rec. Amt</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Receipt Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for receipt in iou_receipts_data %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ receipt.PaymentDateR }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ receipt.EmpNameR }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ receipt.AmountR|floatformat:3 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ receipt.ReasonR }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ receipt.NarrationR }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ receipt.RecivedAmtR|floatformat:3 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ receipt.ReceiptDateR }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                {% if not receipt.is_received %}
                <button
                    class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'iou_receipt_add_edit' receipt.IdR %}" {# Pass MId here #}
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Add Receipt
                </button>
                {% else %}
                <button
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'iou_receipt_add_edit' receipt.IdR %}" {# Pass MId here #}
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit Receipt
                </button>
                <button
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'iou_receipt_delete' receipt.IdR receipt.DIdR %}" {# Pass MId and DIdR #}
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
                {% endif %}
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="9" class="py-4 px-4 text-center text-gray-500">No authorized IOU payments to display receipts for!</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // DataTables initialization
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#iouReceiptTable')) {
            $('#iouReceiptTable').DataTable().destroy();
        }
        $('#iouReceiptTable').DataTable({
            "pageLength": 20, // PageSize from ASP.NET GridView1
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [8] } // Disable sorting for Actions column
            ]
        });
    });
</script>
```

**`_iou_receipt_form.html`** (Partial for Receipt Add/Edit Form)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">
        {% if form.instance.pk %}Edit Receipt for IOU #{{ iou_payment.id }}{% else %}Add Receipt for IOU #{{ iou_payment.id }}{% endif %}
    </h3>
    {% if error_message %}
    <p class="text-red-600 mb-4">{{ error_message }}</p>
    {% endif %}
    <form hx-post="{% url 'iou_receipt_add_edit' iou_payment.id %}" hx-swap="none">
        {% csrf_token %}
        {{ form.m_id }} {# Hidden MId field #}

        <div class="space-y-4">
            <div class="mb-4">
                <label for="id_recieved_amount" class="block text-sm font-medium text-gray-700">Received Amount</label>
                <input type="number" id="id_recieved_amount" name="{{ form.recieved_amount.name }}" 
                       value="{{ form.recieved_amount.value|default_if_none:'' }}" 
                       class="{{ form.recieved_amount.css_classes }} block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                       step="0.001" min="0" 
                       {% if form.recieved_amount.field.max_value %}max="{{ form.recieved_amount.field.max_value }}"{% endif %}
                       {% if form.recieved_amount.field.min_value %}min="{{ form.recieved_amount.field.min_value }}"{% endif %}
                       {% if form.recieved_amount.field.widget.attrs.disabled %}disabled{% endif %}>
                {% if form.recieved_amount.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.recieved_amount.errors }}</p>
                {% endif %}
                {% if form.recieved_amount.help_text %}
                <p class="text-gray-500 text-xs mt-1">{{ form.recieved_amount.help_text }}</p>
                {% endif %}
            </div>

            <div class="mb-4">
                <label for="id_receipt_date_str" class="block text-sm font-medium text-gray-700">Receipt Date</label>
                <input type="date" id="id_receipt_date_str" name="{{ form.receipt_date_str.name }}" 
                       value="{{ form.receipt_date_str.value|default_if_none:'' }}" 
                       class="{{ form.receipt_date_str.css_classes }} block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                       {% if form.receipt_date_str.field.widget.attrs.disabled %}disabled{% endif %}>
                {% if form.receipt_date_str.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.receipt_date_str.errors }}</p>
                {% endif %}
            </div>
        </div>

        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                {% if form.instance.pk %}Update{% else %}Add{% endif %}
            </button>
        </div>
    </form>
</div>
```

**`_iou_receipt_confirm_delete.html`** (Partial for Receipt Delete Confirmation)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete IOU Receipt</h3>
    {% if error_message %}
    <p class="text-red-600 mb-4">{{ error_message }}</p>
    {% else %}
    <p class="text-gray-700 mb-5">Are you sure you want to delete the receipt for IOU Payment #{{ object.iou_payment.id }} (Received Amount: {{ object.recieved_amount }})?</p>
    {% endif %}
    <div class="mt-6 flex items-center justify-end space-x-4">
        <button
            type="button"
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        {% if not error_message %}
        <button
            hx-post="{% url 'iou_receipt_delete' object.iou_payment.id object.id %}"
            hx-swap="none"
            hx-confirm="This action will mark the IOU as not received."
            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            Delete
        </button>
        {% endif %}
    </div>
</div>
```

#### 4.5 URLs (`transactions/urls.py`)

Define the URL patterns for the `transactions` application.

```python
from django.urls import path
from .views import (
    IouPaymentReceiptDashboardView,
    IouPaymentTablePartialView,
    IouPaymentUpdateView,
    IouPaymentDeleteView,
    IouPaymentAuthorizeView,
    IouReceiptTablePartialView,
    IouReceiptCreateUpdateView,
    IouReceiptDeleteView,
)

urlpatterns = [
    # Main dashboard view
    path('iou/', IouPaymentReceiptDashboardView.as_view(), name='iou_payment_receipt_dashboard'),

    # IOU Payment HTMX endpoints
    path('iou/payments/table/', IouPaymentTablePartialView.as_view(), name='iou_payment_table'),
    path('iou/payments/edit/<int:pk>/', IouPaymentUpdateView.as_view(), name='iou_payment_edit'),
    path('iou/payments/delete/<int:pk>/', IouPaymentDeleteView.as_view(), name='iou_payment_delete'),
    path('iou/payments/authorize/<int:pk>/', IouPaymentAuthorizeView.as_view(), name='iou_payment_authorize'),

    # IOU Receipt HTMX endpoints
    path('iou/receipts/table/', IouReceiptTablePartialView.as_view(), name='iou_receipt_table'),
    path('iou/receipts/add_edit/<int:pk>/', IouReceiptCreateUpdateView.as_view(), name='iou_receipt_add_edit'), # PK is IouPayment.id (MId)
    path('iou/receipts/delete/<int:iou_payment_id>/<int:pk>/', IouReceiptDeleteView.as_view(), name='iou_receipt_delete'), # iou_payment_id is MId, pk is DIdR
]
```

#### 4.6 Tests (`transactions/tests.py`)

Comprehensive unit tests for model methods and integration tests for views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from decimal import Decimal
from unittest.mock import patch

from .models import IouPayment, IouReason, Employee, IouReceipt, get_cash_current_balance
from .forms import IouPaymentForm, IouReceiptForm

# Mock global context for tests (matches views.py)
CURRENT_COMP_ID = 1
CURRENT_FIN_YEAR_ID = 1
CURRENT_USER_SESSION_ID = "testuser"


class IouReasonModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        IouReason.objects.create(id=1, terms='Travel Expense')
        IouReason.objects.create(id=2, terms='Office Supplies')

    def test_iou_reason_creation(self):
        reason = IouReason.objects.get(id=1)
        self.assertEqual(reason.terms, 'Travel Expense')

    def test_str_method(self):
        reason = IouReason.objects.get(id=2)
        self.assertEqual(str(reason), 'Office Supplies')

class EmployeeModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        Employee.objects.create(emp_id='EMP001', title='Mr', employee_name='John Doe', comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID)
        Employee.objects.create(emp_id='EMP002', title='Ms', employee_name='Jane Smith', comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID)

    def test_employee_creation(self):
        emp = Employee.objects.get(emp_id='EMP001')
        self.assertEqual(emp.employee_name, 'John Doe')
        self.assertEqual(emp.comp_id, CURRENT_COMP_ID)

    def test_str_method(self):
        emp = Employee.objects.get(emp_id='EMP002')
        self.assertEqual(str(emp), 'Ms.Jane Smith [EMP002]')

    def test_get_employee_name_by_id(self):
        name = Employee.get_employee_name_by_id('EMP001', CURRENT_COMP_ID, CURRENT_FIN_YEAR_ID)
        self.assertEqual(name, 'Mr.John Doe [EMP001]')
        name_not_found = Employee.get_employee_name_by_id('EMP999', CURRENT_COMP_ID, CURRENT_FIN_YEAR_ID)
        self.assertEqual(name_not_found, 'Unknown Employee')


class IouPaymentModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        IouReason.objects.create(id=101, terms='Test Reason 1')
        Employee.objects.create(emp_id='EMP001', title='Mr', employee_name='Test Employee 1', comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID)
        
        cls.iou_payment_unauth = IouPayment.objects.create(
            id=1, payment_date=timezone.now().date(), emp_id='EMP001',
            amount=Decimal('500.000'), reason=101, narration='Initial payment',
            authorize=False, recieved=False, comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID
        )
        cls.iou_payment_auth = IouPayment.objects.create(
            id=2, payment_date=timezone.now().date(), emp_id='EMP001',
            amount=Decimal('100.000'), reason=101, narration='Authorized payment',
            authorize=True, recieved=False, authorized_date=timezone.now().date(),
            authorized_by='system', comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID
        )

    def test_iou_payment_creation(self):
        payment = IouPayment.objects.get(id=1)
        self.assertEqual(payment.amount, Decimal('500.000'))
        self.assertFalse(payment.authorize)

    def test_get_employee_name(self):
        self.assertEqual(self.iou_payment_unauth.get_employee_name(), 'Mr.Test Employee 1 [EMP001]')

    def test_get_reason_terms(self):
        self.assertEqual(self.iou_payment_unauth.get_reason_terms(), 'Test Reason 1')

    @patch('transactions.models.get_cash_current_balance', return_value=Decimal('1000.00'))
    def test_authorize_payment_success(self, mock_get_cash):
        success, message = self.iou_payment_unauth.authorize_payment(
            CURRENT_USER_SESSION_ID, CURRENT_COMP_ID, CURRENT_FIN_YEAR_ID, mock_get_cash
        )
        self.assertTrue(success)
        self.assertEqual(message, "Payment authorized successfully.")
        self.iou_payment_unauth.refresh_from_db()
        self.assertTrue(self.iou_payment_unauth.authorize)
        self.assertEqual(self.iou_payment_unauth.authorized_by, CURRENT_USER_SESSION_ID)

    @patch('transactions.models.get_cash_current_balance', return_value=Decimal('400.00'))
    def test_authorize_payment_insufficient_cash(self, mock_get_cash):
        success, message = self.iou_payment_unauth.authorize_payment(
            CURRENT_USER_SESSION_ID, CURRENT_COMP_ID, CURRENT_FIN_YEAR_ID, mock_get_cash
        )
        self.assertFalse(success)
        self.assertEqual(message, "Insufficient Cash.")
        self.iou_payment_unauth.refresh_from_db()
        self.assertFalse(self.iou_payment_unauth.authorize) # Should remain unauthorized

    @patch('transactions.models.get_cash_current_balance', return_value=Decimal('1000.00'))
    def test_authorize_payment_already_authorized(self, mock_get_cash):
        success, message = self.iou_payment_auth.authorize_payment(
            CURRENT_USER_SESSION_ID, CURRENT_COMP_ID, CURRENT_FIN_YEAR_ID, mock_get_cash
        )
        self.assertTrue(success) # Returns True because it's already authorized
        self.assertEqual(message, "Payment already authorized.")

    def test_delete_payment_success(self):
        iou = IouPayment.objects.create(
            id=3, payment_date=timezone.now().date(), emp_id='EMP001',
            amount=Decimal('50.000'), reason=101, narration='Deletable payment',
            authorize=False, recieved=False, comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID
        )
        success, message = iou.delete_payment()
        self.assertTrue(success)
        self.assertEqual(message, "Payment deleted successfully.")
        self.assertFalse(IouPayment.objects.filter(id=3).exists())

    def test_delete_payment_authorized_fail(self):
        success, message = self.iou_payment_auth.delete_payment()
        self.assertFalse(success)
        self.assertEqual(message, "Cannot delete an authorized payment.")
        self.assertTrue(IouPayment.objects.filter(id=2).exists()) # Should still exist

class IouReceiptModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        IouReason.objects.create(id=101, terms='Test Reason 1')
        Employee.objects.create(emp_id='EMP001', title='Mr', employee_name='Test Employee 1', comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID)
        
        cls.iou_payment = IouPayment.objects.create(
            id=1, payment_date=timezone.now().date(), emp_id='EMP001',
            amount=Decimal('500.000'), reason=101, narration='Payment for receipt',
            authorize=True, recieved=False, comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID
        )
        cls.iou_receipt = IouReceipt.objects.create(
            id=10, m_id=cls.iou_payment.id, recieved_amount=Decimal('400.000'),
            receipt_date=timezone.now().date(), comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID
        )
        # Mark payment as received for the existing receipt
        cls.iou_payment.recieved = True
        cls.iou_payment.save()

    def test_iou_receipt_creation(self):
        receipt = IouReceipt.objects.get(id=10)
        self.assertEqual(receipt.recieved_amount, Decimal('400.000'))
        self.assertEqual(receipt.m_id, self.iou_payment.id)

    def test_add_receipt_success(self):
        new_iou_payment = IouPayment.objects.create(
            id=2, payment_date=timezone.now().date(), emp_id='EMP001',
            amount=Decimal('200.000'), reason=101, narration='New payment for receipt',
            authorize=True, recieved=False, comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID
        )
        new_receipt = IouReceipt(m_id=new_iou_payment.id, recieved_amount=Decimal('150.000'), receipt_date=timezone.now().date())
        success, message = new_receipt.add_receipt(CURRENT_USER_SESSION_ID, CURRENT_COMP_ID, CURRENT_FIN_YEAR_ID)
        self.assertTrue(success)
        self.assertEqual(message, "Receipt added successfully.")
        new_iou_payment.refresh_from_db()
        self.assertTrue(new_iou_payment.recieved)
        self.assertTrue(IouReceipt.objects.filter(m_id=new_iou_payment.id).exists())

    def test_add_receipt_amount_exceeds(self):
        new_iou_payment = IouPayment.objects.create(
            id=3, payment_date=timezone.now().date(), emp_id='EMP001',
            amount=Decimal('50.000'), reason=101, narration='Small payment',
            authorize=True, recieved=False, comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID
        )
        new_receipt = IouReceipt(m_id=new_iou_payment.id, recieved_amount=Decimal('100.000'), receipt_date=timezone.now().date())
        success, message = new_receipt.add_receipt(CURRENT_USER_SESSION_ID, CURRENT_COMP_ID, CURRENT_FIN_YEAR_ID)
        self.assertFalse(success)
        self.assertEqual(message, "Received amount exceeds IOU payment amount.")
        new_iou_payment.refresh_from_db()
        self.assertFalse(new_iou_payment.recieved) # Should remain unreceived

    def test_update_receipt_success(self):
        self.iou_receipt.recieved_amount = Decimal('450.000')
        success, message = self.iou_receipt.update_receipt()
        self.assertTrue(success)
        self.assertEqual(message, "Receipt updated successfully.")
        self.iou_receipt.refresh_from_db()
        self.assertEqual(self.iou_receipt.recieved_amount, Decimal('450.000'))

    def test_update_receipt_amount_exceeds(self):
        self.iou_receipt.recieved_amount = Decimal('600.000') # Original IOU is 500
        success, message = self.iou_receipt.update_receipt()
        self.assertFalse(success)
        self.assertEqual(message, "Received amount exceeds IOU payment amount.")
        self.iou_receipt.refresh_from_db()
        self.assertNotEqual(self.iou_receipt.recieved_amount, Decimal('600.000')) # Should not update

    def test_delete_receipt_success(self):
        success, message = self.iou_receipt.delete_receipt()
        self.assertTrue(success)
        self.assertEqual(message, "Receipt deleted successfully.")
        self.assertFalse(IouReceipt.objects.filter(id=10).exists())
        self.iou_payment.refresh_from_db()
        self.assertFalse(self.iou_payment.recieved) # IOU should be marked as unreceived


class IouPaymentFormTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        IouReason.objects.create(id=101, terms='Form Test Reason')
        Employee.objects.create(emp_id='EMP001', title='Mr', employee_name='Form Test Employee', comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID)
        cls.iou_payment = IouPayment.objects.create(
            id=1, payment_date=timezone.now().date(), emp_id='EMP001',
            amount=Decimal('500.000'), reason=101, narration='Form test payment',
            authorize=False, recieved=False, comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID
        )

    def test_form_valid_data(self):
        data = {
            'amount': '550.000',
            'reason_id': '101',
            'narration': 'Updated narration',
            'authorize': False,
        }
        form = IouPaymentForm(data=data, instance=self.iou_payment)
        self.assertTrue(form.is_valid(), form.errors)
        self.assertEqual(form.cleaned_data['amount'], Decimal('550.000'))
        self.assertEqual(form.cleaned_data['reason'].id, 101) # Check if reason object is correctly mapped
        self.assertEqual(form.cleaned_data['narration'], 'Updated narration')

    def test_form_invalid_amount(self):
        data = {
            'amount': '0.000',
            'reason_id': '101',
            'narration': 'Invalid amount test',
            'authorize': False,
        }
        form = IouPaymentForm(data=data, instance=self.iou_payment)
        self.assertFalse(form.is_valid())
        self.assertIn('Amount must be a positive value.', form.errors['amount'])

    def test_form_initial_data(self):
        form = IouPaymentForm(instance=self.iou_payment)
        self.assertEqual(form.fields['amount'].initial, Decimal('500.000'))
        self.assertEqual(form.fields['reason_id'].initial, 101)
        self.assertFalse(form.fields['authorize'].initial)


class IouReceiptFormTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        IouReason.objects.create(id=101, terms='Receipt Test Reason')
        Employee.objects.create(emp_id='EMP001', title='Mr', employee_name='Receipt Test Employee', comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID)
        cls.iou_payment = IouPayment.objects.create(
            id=1, payment_date=timezone.now().date(), emp_id='EMP001',
            amount=Decimal('500.000'), reason=101, narration='Receipt test payment',
            authorize=True, recieved=False, comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID
        )
        cls.iou_receipt = IouReceipt.objects.create(
            id=10, m_id=cls.iou_payment.id, recieved_amount=Decimal('200.000'),
            receipt_date=timezone.now().date(), comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID
        )

    def test_form_valid_data_create(self):
        data = {
            'm_id': str(self.iou_payment.id),
            'recieved_amount': '150.000',
            'receipt_date_str': '2023-01-15',
        }
        form = IouReceiptForm(data=data, iou_payment_instance=self.iou_payment)
        self.assertTrue(form.is_valid(), form.errors)
        self.assertEqual(form.cleaned_data['recieved_amount'], Decimal('150.000'))
        self.assertEqual(form.cleaned_data['receipt_date_str'], timezone.datetime(2023, 1, 15).date())

    def test_form_valid_data_update(self):
        data = {
            'm_id': str(self.iou_payment.id),
            'recieved_amount': '250.000',
            'receipt_date_str': '2023-01-20',
        }
        form = IouReceiptForm(data=data, instance=self.iou_receipt, iou_payment_instance=self.iou_payment)
        self.assertTrue(form.is_valid(), form.errors)
        self.assertEqual(form.cleaned_data['recieved_amount'], Decimal('250.000'))

    def test_form_invalid_amount_exceeds_iou(self):
        data = {
            'm_id': str(self.iou_payment.id),
            'recieved_amount': '600.000', # Exceeds IOU amount of 500
            'receipt_date_str': '2023-01-15',
        }
        form = IouReceiptForm(data=data, iou_payment_instance=self.iou_payment)
        self.assertFalse(form.is_valid())
        self.assertIn('Received amount cannot exceed original IOU amount.', form.errors['recieved_amount'])

    def test_form_invalid_receipt_date_format(self):
        data = {
            'm_id': str(self.iou_payment.id),
            'recieved_amount': '100.000',
            'receipt_date_str': 'invalid-date',
        }
        form = IouReceiptForm(data=data, iou_payment_instance=self.iou_payment)
        self.assertFalse(form.is_valid())
        self.assertIn('Invalid date format. Use YYYY-MM-DD or DD-MM-YYYY.', form.errors['receipt_date_str'])

    def test_form_empty_receipt_date(self):
        data = {
            'm_id': str(self.iou_payment.id),
            'recieved_amount': '100.000',
            'receipt_date_str': '',
        }
        form = IouReceiptForm(data=data, iou_payment_instance=self.iou_payment)
        self.assertFalse(form.is_valid())
        self.assertIn('Receipt Date is required.', form.errors['receipt_date_str'])


class IouViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        IouReason.objects.create(id=101, terms='Travel Expense')
        Employee.objects.create(emp_id='EMP001', title='Mr', employee_name='Test Employee', comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID)
        
        # Unauthorized, unreceived payment
        cls.iou_payment1 = IouPayment.objects.create(
            id=1, payment_date=timezone.now().date(), emp_id='EMP001',
            amount=Decimal('500.000'), reason=101, narration='Payment 1',
            authorize=False, recieved=False, comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID
        )
        # Authorized, unreceived payment (ready for receipt)
        cls.iou_payment2 = IouPayment.objects.create(
            id=2, payment_date=timezone.now().date(), emp_id='EMP001',
            amount=Decimal('300.000'), reason=101, narration='Payment 2 (Authorized)',
            authorize=True, recieved=False, authorized_date=timezone.now().date(),
            authorized_by='testuser', comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID
        )
        # Authorized, received payment
        cls.iou_payment3 = IouPayment.objects.create(
            id=3, payment_date=timezone.now().date(), emp_id='EMP001',
            amount=Decimal('200.000'), reason=101, narration='Payment 3 (Received)',
            authorize=True, recieved=True, authorized_date=timezone.now().date(),
            authorized_by='testuser', comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID
        )
        cls.iou_receipt1 = IouReceipt.objects.create(
            id=1, m_id=cls.iou_payment3.id, recieved_amount=Decimal('200.000'),
            receipt_date=timezone.now().date(), comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID
        )

    def setUp(self):
        self.client = Client()

    def test_dashboard_view(self):
        response = self.client.get(reverse('iou_payment_receipt_dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'transactions/iou_payment_receipt_dashboard.html')
        self.assertContains(response, 'IOU: Payment/Receipt')

    def test_iou_payment_table_partial_view(self):
        response = self.client.get(reverse('iou_payment_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'transactions/_iou_payment_table.html')
        self.assertContains(response, self.iou_payment1.narration) # Should contain unreceived payment
        self.assertContains(response, 'No IOU payments to display!') # Should not contain this if data is present
        self.assertNotContains(response, self.iou_payment2.narration) # Should not contain authorized payments (get_payments_for_grid filters)

    def test_iou_payment_edit_view_get(self):
        response = self.client.get(reverse('iou_payment_edit', args=[self.iou_payment1.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'transactions/_iou_payment_form.html')
        self.assertContains(response, 'Edit IOU Payment')
        self.assertIsInstance(response.context['form'], IouPaymentForm)

    def test_iou_payment_edit_view_post_success(self):
        data = {
            'amount': '550.000',
            'reason_id': '101',
            'narration': 'Updated narration via form',
            'authorize': 'false', # Authorization is separate action in legacy, but form field exists.
        }
        response = self.client.post(reverse('iou_payment_edit', args=[self.iou_payment1.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertEqual(response.headers['HX-Trigger'], 'refreshIouPaymentList')
        self.iou_payment1.refresh_from_db()
        self.assertEqual(self.iou_payment1.amount, Decimal('550.000'))
        self.assertEqual(self.iou_payment1.narration, 'Updated narration via form')

    def test_iou_payment_edit_view_post_invalid(self):
        data = {
            'amount': '0.000', # Invalid amount
            'reason_id': '101',
            'narration': 'Invalid attempt',
            'authorize': 'false',
        }
        response = self.client.post(reverse('iou_payment_edit', args=[self.iou_payment1.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400) # HTMX failure, re-renders form with errors
        self.assertTemplateUsed(response, 'transactions/_iou_payment_form.html')
        self.assertContains(response, 'Amount must be a positive value.')

    def test_iou_payment_delete_view_get(self):
        response = self.client.get(reverse('iou_payment_delete', args=[self.iou_payment1.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'transactions/_iou_payment_confirm_delete.html')
        self.assertContains(response, 'Confirm Delete IOU Payment')

    def test_iou_payment_delete_view_post_success(self):
        response = self.client.post(reverse('iou_payment_delete', args=[self.iou_payment1.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshIouPaymentList')
        self.assertFalse(IouPayment.objects.filter(id=self.iou_payment1.id).exists())

    def test_iou_payment_delete_view_post_fail_authorized(self):
        response = self.client.post(reverse('iou_payment_delete', args=[self.iou_payment2.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400) # Should fail due to authorization
        self.assertTemplateUsed(response, 'transactions/_iou_payment_confirm_delete.html')
        self.assertContains(response, 'Cannot delete an authorized payment.')
        self.assertTrue(IouPayment.objects.filter(id=self.iou_payment2.id).exists())

    @patch('transactions.models.get_cash_current_balance', return_value=Decimal('1000.00'))
    def test_iou_payment_authorize_view_post_success(self, mock_get_cash):
        response = self.client.post(reverse('iou_payment_authorize', args=[self.iou_payment1.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('refreshIouPaymentList', response.headers['HX-Trigger'])
        self.assertIn('refreshIouReceiptList', response.headers['HX-Trigger'])
        self.iou_payment1.refresh_from_db()
        self.assertTrue(self.iou_payment1.authorize)

    @patch('transactions.models.get_cash_current_balance', return_value=Decimal('100.00'))
    def test_iou_payment_authorize_view_post_insufficient_cash(self, mock_get_cash):
        response = self.client.post(reverse('iou_payment_authorize', args=[self.iou_payment1.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400)
        self.assertContains(response, 'Insufficient Cash.')
        self.iou_payment1.refresh_from_db()
        self.assertFalse(self.iou_payment1.authorize) # Should remain unauthorized

    def test_iou_receipt_table_partial_view(self):
        response = self.client.get(reverse('iou_receipt_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'transactions/_iou_receipt_table.html')
        self.assertContains(response, self.iou_payment2.narration) # Should contain authorized but unreceived
        self.assertContains(response, self.iou_payment3.narration) # Should contain authorized and received
        self.assertContains(response, str(self.iou_receipt1.recieved_amount)) # Should contain existing receipt amount
        self.assertNotContains(response, self.iou_payment1.narration) # Should not contain unauthorized payments


    def test_iou_receipt_add_edit_view_get_add_mode(self):
        response = self.client.get(reverse('iou_receipt_add_edit', args=[self.iou_payment2.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'transactions/_iou_receipt_form.html')
        self.assertContains(response, 'Add Receipt for IOU')
        self.assertIsInstance(response.context['form'], IouReceiptForm)
        self.assertEqual(response.context['form'].initial['m_id'], self.iou_payment2.id)

    def test_iou_receipt_add_edit_view_get_edit_mode(self):
        response = self.client.get(reverse('iou_receipt_add_edit', args=[self.iou_payment3.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'transactions/_iou_receipt_form.html')
        self.assertContains(response, 'Edit Receipt for IOU')
        self.assertIsInstance(response.context['form'], IouReceiptForm)
        self.assertEqual(response.context['form'].instance.pk, self.iou_receipt1.id)

    def test_iou_receipt_add_edit_view_post_add_success(self):
        data = {
            'm_id': str(self.iou_payment2.id),
            'recieved_amount': '150.000',
            'receipt_date_str': '2023-01-25',
        }
        response = self.client.post(reverse('iou_receipt_add_edit', args=[self.iou_payment2.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('refreshIouReceiptList', response.headers['HX-Trigger'])
        self.assertIn('refreshIouPaymentList', response.headers['HX-Trigger'])
        self.assertTrue(IouReceipt.objects.filter(m_id=self.iou_payment2.id, recieved_amount=Decimal('150.000')).exists())
        self.iou_payment2.refresh_from_db()
        self.assertTrue(self.iou_payment2.recieved)

    def test_iou_receipt_add_edit_view_post_update_success(self):
        data = {
            'm_id': str(self.iou_payment3.id),
            'recieved_amount': '180.000',
            'receipt_date_str': '2023-01-30',
        }
        response = self.client.post(reverse('iou_receipt_add_edit', args=[self.iou_payment3.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.iou_receipt1.refresh_from_db()
        self.assertEqual(self.iou_receipt1.recieved_amount, Decimal('180.000'))

    def test_iou_receipt_add_edit_view_post_invalid_amount(self):
        data = {
            'm_id': str(self.iou_payment2.id),
            'recieved_amount': '400.000', # Exceeds IOU payment2 amount of 300
            'receipt_date_str': '2023-01-25',
        }
        response = self.client.post(reverse('iou_receipt_add_edit', args=[self.iou_payment2.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400)
        self.assertContains(response, 'Received amount cannot exceed original IOU amount.')

    def test_iou_receipt_delete_view_get(self):
        response = self.client.get(reverse('iou_receipt_delete', args=[self.iou_payment3.id, self.iou_receipt1.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'transactions/_iou_receipt_confirm_delete.html')
        self.assertContains(response, 'Confirm Delete IOU Receipt')

    def test_iou_receipt_delete_view_post_success(self):
        response = self.client.post(reverse('iou_receipt_delete', args=[self.iou_payment3.id, self.iou_receipt1.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('refreshIouReceiptList', response.headers['HX-Trigger'])
        self.assertIn('refreshIouPaymentList', response.headers['HX-Trigger'])
        self.assertFalse(IouReceipt.objects.filter(id=self.iou_receipt1.id).exists())
        self.iou_payment3.refresh_from_db()
        self.assertFalse(self.iou_payment3.recieved)
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated templates and views already incorporate HTMX for all dynamic interactions and Alpine.js for modal management and basic UI state.

*   **HTMX for dynamic updates:**
    *   The `iou_payment_receipt_dashboard.html` uses `hx-get` on load and custom `hx-trigger` events (`refreshIouPaymentList`, `refreshIouReceiptList`) to fetch the DataTable content (`_iou_payment_table.html`, `_iou_receipt_table.html`) dynamically.
    *   All CRUD operations (Edit, Delete, Authorize, Add Receipt, Edit Receipt, Delete Receipt) within the DataTables are triggered via `hx-get` (for forms/confirmations) or `hx-post` (for actions).
    *   Modal content is loaded into `#modalContent` using `hx-target`.
    *   `hx-swap="none"` is used for POST requests to avoid full page reloads and rely on `HX-Trigger` to refresh relevant sections.
    *   `HX-Trigger` headers in views (`status=204` responses) are used to signal the client to refresh the DataTable partials after successful operations.
*   **Alpine.js for UI state management:**
    *   `x-data` on the main dashboard div manages the `activeTab` state, showing/hiding tab content.
    *   Alpine.js is used to control the visibility of the universal modal (`#modal`) based on `show` property.
    *   It also handles opening/closing the modal triggered by HTMX requests (`htmx:afterSwap`) or manual clicks.
    *   It listens to the `refreshIouPaymentList` and `refreshIouReceiptList` custom events to close the modal after a successful operation.
*   **DataTables for list views:**
    *   Both `_iou_payment_table.html` and `_iou_receipt_table.html` include JavaScript to initialize DataTables on the respective tables.
    *   The DataTables initialization is wrapped in `$(document).ready(function() { ... });` and includes a check `if ($.fn.DataTable.isDataTable('#iouPaymentTable')) { $('#iouPaymentTable').DataTable().destroy(); }` to ensure proper re-initialization when HTMX swaps the content.
    *   Configured with `pageLength` and `lengthMenu` to match original ASP.NET behavior.
    *   `responsive: true` is added for better mobile experience.
    *   `columnDefs` is used to disable sorting for action columns, as they contain buttons, not data.
*   **No custom JavaScript requirements beyond HTMX/Alpine/DataTables:** The migration aims to eliminate custom JavaScript for CRUD operations by leveraging HTMX. Alpine.js handles simple UI interactions like modals.

### Final Notes

This comprehensive plan provides a robust, modern Django equivalent for your ASP.NET IOU Payment/Receipt module.

*   **Automation:** The structure of this plan allows for significant portions of the code (models, forms, basic views, and template structures) to be generated programmatically based on the extracted schema and functionality. AI tools can be trained on these patterns.
*   **Business Value:** By moving to Django, you gain:
    *   **Improved Scalability:** Django is highly performant and can handle increased user loads.
    *   **Enhanced Maintainability:** Clean, modular code, strong conventions, and a thriving community reduce maintenance overhead.
    *   **Modern User Experience:** HTMX and Alpine.js provide a reactive, single-page application-like feel without the complexity of a full JavaScript framework.
    *   **Reduced Development Costs:** Leveraging Django's built-in features and ORM reduces boilerplate code, speeding up future development.
    *   **Increased Security:** Django comes with robust security features out-of-the-box, protecting against common web vulnerabilities.
    *   **Developer Productivity:** Python's readability and Django's "batteries-included" philosophy empower developers to build features faster.
*   **Next Steps:**
    *   Integrate this `transactions` application into your Django project.
    *   Configure `settings.py` (e.g., `INSTALLED_APPS`, database connection for legacy schema).
    *   Implement user authentication and session management in Django to replace ASP.NET `Session["compid"]`, `Session["finyear"]`, `Session["username"]`. This might involve creating a `UserProfile` model and linking `CompId`/`FinYearId` to it.
    *   Address the `get_cash_current_balance` function, which needs to be integrated with your actual financial accounting system or a dedicated Django app for cash management.
    *   Consider creating a `management command` or script to migrate existing data from your legacy database to ensure compatibility with `managed=False` models.
    *   Review and enhance error handling and user feedback.
    *   Implement proper logging.