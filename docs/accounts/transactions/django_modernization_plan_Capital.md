## ASP.NET to Django Conversion Script: Capital Management Module

This document outlines the modernization plan for the ASP.NET Capital Management module, transforming it into a modern Django 5.0+ application. The approach prioritizes automation-driven migration, clear communication, and the adoption of a robust, maintainable architecture leveraging Django's strengths with HTMX and Alpine.js for dynamic interfaces.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code interacts with two primary tables: `tblACC_Capital_Master` and `tblACC_Capital_Details`.

*   **`tblACC_Capital_Master`**:
    *   `Id` (Primary Key, inferred from `lblId` and `Id` column)
    *   `Particulars` (String, inferred from `TextPerticulars2`, `TextPerticulars1` and `Bind("Perticulars")`)
    *   `SysDate` (Date, inferred from `CDate` in C# code)
    *   `SysTime` (String, inferred from `CTime` in C# code)
    *   `CompId` (Integer, inferred from `Session["compid"]`)
    *   `FinYearId` (Integer, inferred from `Session["finyear"]`)
    *   `SessionId` (String, inferred from `Session["username"]`)

*   **`tblACC_Capital_Details`**:
    *   `Id` (Primary Key, inferred from `lblIdp` and `Id` column)
    *   `MId` (Foreign Key to `tblACC_Capital_Master.Id`, inferred from `ViewState["MId"]` and `MId` column)
    *   `Particulars` (String, inferred from `TextPerticularsp2`, `TextPerticularsp1` and `Bind("Perticulars")`)
    *   `CreditAmt` (Decimal, inferred from `TextCreditAmtp2`, `TextCreditAmtp1` and `Bind("CreditAmt")`)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic from the ASP.NET code-behind.

**Analysis:**

*   **`tblACC_Capital_Master` (CapitalMaster):**
    *   **Read (R):** Data is fetched using `SELECT Id, Particulars from tblACC_Capital_Master` and displayed in `GridView2`. The `FillData()` method populates this grid, filtering by `FinYearId` and `CompId`.
    *   **Create (C):** New master records are inserted via `GridView2_RowCommand` (commands "Add" and "Add1"). `Particulars`, `SysDate`, `SysTime`, `CompId`, `FinYearId`, `SessionId` are saved.
    *   **Delete (D):** Records are deleted via `GridView2_RowCommand` (command "Del"). A critical business rule: a master record cannot be deleted if corresponding detail records exist in `tblACC_Capital_Details` (`LinkBtnDel` is hidden if `MId` exists in `tblACC_Capital_Details`).
    *   **Navigation/Relationship:** Clicking on a `Particulars` link (`HpPerticulars` command) sets `MId` and triggers `FillDataCredit()`, showing associated `tblACC_Capital_Details`.

*   **`tblACC_Capital_Details` (CapitalDetail):**
    *   **Read (R):** Data is fetched using `SELECT Id, Particulars from tblACC_Capital_Details` filtered by `MId` and displayed in `GridView3`. `FillDataCredit()` handles this.
    *   **Create (C):** New detail records are inserted via `GridView3_RowCommand` (commands "Addp" and "Addp1"). `MId`, `Particulars`, `CreditAmt` are saved.
    *   **Delete (D):** Records are deleted via `GridView3_RowCommand` (command "Delp").

*   **Validation:**
    *   Required field validators (`RequiredFieldValidator`) for `Particulars` and `CreditAmt`.
    *   Regular expression validator (`RegularExpressionValidator`) for `CreditAmt` to ensure numeric format.
    *   Custom numeric validation `fun.NumberValidationQty` for `CreditAmt`.

*   **Contextual Data:** `CompId`, `FyId`, `SId` (SessionId), `CDate`, `CTime` are obtained from session and `clsFunctions`, and are automatically inserted with new records. This needs to be handled by Django views/models (e.g., pre-save hooks, default values, or passing from request context).

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, mapping them to modern web components.

**Analysis:**

*   **`GridView2` (Capital Master List):** Will be replaced by a Django template using a DataTables component.
    *   `SN` (Serial Number): Handled by DataTables counter or Django's `forloop.counter`.
    *   `Delete` (`LinkBtnDel`): A button in a DataTables "Actions" column. Its visibility will be controlled by a model method (`can_be_deleted`) or view logic.
    *   `Particulars` (`LinkBtnPerticulars`): A link/button that triggers HTMX to load/refresh the details table.
    *   Footer `btnInsert`, `TextPerticulars2`: HTML form elements for adding new master records, probably within a modal dialog triggered by an "Add New" button.
    *   `EmptyDataTemplate`: The logic for displaying an "add" form when no data exists will be part of the main `list.html` and `_capitalmaster_table.html` partial, which displays a form directly or a message.

*   **`GridView3` (Capital Details List):** Will also be a DataTables component, dynamically loaded/updated via HTMX.
    *   `SN`, `Delete` (`LinkBtnDelp`), `Particulars`, `CreditAmt`: Columns in the DataTables.
    *   Footer `btnInsertp`, `TextPerticularsp2`, `TextCreditAmtp2`: HTML form elements for adding new detail records, likely within a modal dialog.
    *   `EmptyDataTemplate`: Similar to the master grid, for adding the first detail.

*   **`Panel3` (`Label2` "No data found"):** This will be handled by simple Django template `{% if not object_list %}` checks, showing a message or directly the 'add' form within the details table area.

*   **Client-side scripts (`loadingNotifier.js`, `PopUpMsg.js`, `confirmationDelete()`, `confirmationAdd()`):** These will be replaced by HTMX for asynchronous interactions, Alpine.js for UI state management (e.g., modals), and potentially Django's `messages` framework for feedback. Confirmation dialogs will be handled by Alpine.js/HTMX-driven modals.

### Step 4: Generate Django Code

We will create a new Django app, let's call it `capital`.

#### 4.1 Models (`capital/models.py`)

The models will represent the `tblACC_Capital_Master` and `tblACC_Capital_Details` tables, adhering to `managed = False` for existing databases. Business logic like `can_be_deleted` will be encapsulated within the models.

```python
from django.db import models
from django.db.models import Sum

class CapitalMaster(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    particulars = models.CharField(db_column='Particulars', max_length=255)
    sys_date = models.DateField(db_column='SysDate', auto_now_add=True)
    sys_time = models.CharField(db_column='SysTime', max_length=10, blank=True, null=True) # Assuming time as string
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True) # Placeholder for company ID
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True) # Placeholder for financial year ID
    session_id = models.CharField(db_column='SessionId', max_length=255, blank=True, null=True) # Placeholder for session/username

    class Meta:
        managed = False
        db_table = 'tblACC_Capital_Master'
        verbose_name = 'Capital Master'
        verbose_name_plural = 'Capital Masters'
        ordering = ['-id'] # Matches Order by Id DESC

    def __str__(self):
        return self.particulars

    def has_details(self):
        """
        Checks if this Capital Master record has associated Capital Detail records.
        Used to determine if the master record can be deleted.
        """
        return self.capitaldetail_set.exists()

    def get_total_credit_amount(self):
        """
        Calculates the sum of CreditAmt for all associated Capital Detail records.
        """
        return self.capitaldetail_set.aggregate(total=Sum('credit_amt'))['total'] or 0

class CapitalDetail(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    master = models.ForeignKey(CapitalMaster, models.DO_NOTHING, db_column='MId') # DO_NOTHING as per ASP.NET implicit behavior (delete not cascaded)
    particulars = models.CharField(db_column='Particulars', max_length=255)
    credit_amt = models.DecimalField(db_column='CreditAmt', max_digits=18, decimal_places=3) # Match ASP.NET validation regex: 15 digits, 3 decimal places

    class Meta:
        managed = False
        db_table = 'tblACC_Capital_Details'
        verbose_name = 'Capital Detail'
        verbose_name_plural = 'Capital Details'

    def __str__(self):
        return f"{self.particulars} - {self.credit_amt}"

    def clean(self):
        """
        Custom validation for credit_amt, replicating NumberValidationQty.
        """
        if self.credit_amt <= 0:
            raise models.ValidationError({'credit_amt': 'Credit Amount must be a positive value.'})

```

#### 4.2 Forms (`capital/forms.py`)

Django forms for creating and updating `CapitalMaster` and `CapitalDetail` records, including validation matching the ASP.NET regex and required fields.

```python
from django import forms
from .models import CapitalMaster, CapitalDetail
from django.core.validators import RegexValidator

class CapitalMasterForm(forms.ModelForm):
    class Meta:
        model = CapitalMaster
        fields = ['particulars']
        widgets = {
            'particulars': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'particulars': 'Particulars (Master)',
        }

    def clean_particulars(self):
        particulars = self.cleaned_data['particulars']
        if not particulars.strip():
            raise forms.ValidationError("Particulars cannot be empty.")
        return particulars

class CapitalDetailForm(forms.ModelForm):
    # Replicate ASP.NET validation: ^\d{1,15}(\.\d{0,3})?$
    credit_amt = forms.DecimalField(
        label='Credit Amount',
        max_digits=18, # Allows up to 15 integer + 3 decimal places
        decimal_places=3,
        validators=[
            RegexValidator(
                regex=r'^\d{1,15}(\.\d{0,3})?$',
                message="Credit amount must be a number with up to 15 digits and 3 decimal places.",
            )
        ],
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    class Meta:
        model = CapitalDetail
        fields = ['particulars', 'credit_amt']
        widgets = {
            'particulars': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'particulars': 'Particulars (Detail)',
        }

    def clean(self):
        cleaned_data = super().clean()
        credit_amt = cleaned_data.get('credit_amt')
        if credit_amt is not None and credit_amt <= 0:
            self.add_error('credit_amt', 'Credit Amount must be a positive value.')
        return cleaned_data
```

#### 4.3 Views (`capital/views.py`)

Views will handle the main page display (master list), HTMX-driven partial updates (detail list), and modal CRUD operations for both master and detail. The `SysDate`, `SysTime`, `CompId`, `FinYearId`, `SessionId` values from the ASP.NET code-behind will be populated in the `form_valid` method.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, render
from django.db import IntegrityError, transaction
from datetime import datetime
import time # For mocking SysTime

from .models import CapitalMaster, CapitalDetail
from .forms import CapitalMasterForm, CapitalDetailForm

# Helper to simulate session variables for migration purposes
# In a real application, these would come from request.user, settings, or a user profile.
def get_mock_session_vars():
    return {
        'comp_id': 1, # Example: from Session["compid"]
        'fin_year_id': 2024, # Example: from Session["finyear"]
        'session_id': 'admin_user', # Example: from Session["username"]
        'sys_date': datetime.now().date(),
        'sys_time': datetime.now().strftime("%I:%M %p") # Example: 03:30 PM
    }

class CapitalMasterListView(ListView):
    model = CapitalMaster
    template_name = 'capital/list.html'
    context_object_name = 'capital_masters'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize an empty detail table (or load first master's details if any)
        context['capital_details'] = []
        context['current_master'] = None
        context['detail_form'] = CapitalDetailForm()
        return context

# HTMX Partial View for CapitalMaster Table
class CapitalMasterTablePartialView(ListView):
    model = CapitalMaster
    template_name = 'capital/_capitalmaster_table.html'
    context_object_name = 'capital_masters'

# HTMX Partial View for CapitalDetail Table
class CapitalDetailTablePartialView(View):
    def get(self, request, pk, *args, **kwargs):
        master = get_object_or_404(CapitalMaster, pk=pk)
        capital_details = master.capitaldetail_set.all()
        detail_form = CapitalDetailForm()
        return render(request, 'capital/_capitaldetail_table.html', {
            'capital_details': capital_details,
            'current_master': master,
            'detail_form': detail_form
        })

class CapitalMasterCreateView(CreateView):
    model = CapitalMaster
    form_class = CapitalMasterForm
    template_name = 'capital/_capitalmaster_form.html' # Rendered in modal
    success_url = reverse_lazy('capital_master_list')

    def form_valid(self, form):
        # Populate additional fields from "session" context
        session_vars = get_mock_session_vars()
        form.instance.comp_id = session_vars['comp_id']
        form.instance.fin_year_id = session_vars['fin_year_id']
        form.instance.session_id = session_vars['session_id']
        # sys_date is auto_now_add, sys_time is manually set
        form.instance.sys_time = session_vars['sys_time']

        response = super().form_valid(form)
        messages.success(self.request, 'Capital Master added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content to return, just trigger client-side refresh
                headers={'HX-Trigger': 'refreshCapitalMasterList'}
            )
        return response

class CapitalMasterUpdateView(UpdateView):
    model = CapitalMaster
    form_class = CapitalMasterForm
    template_name = 'capital/_capitalmaster_form.html' # Rendered in modal
    success_url = reverse_lazy('capital_master_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Capital Master updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshCapitalMasterList'}
            )
        return response

class CapitalMasterDeleteView(DeleteView):
    model = CapitalMaster
    template_name = 'capital/_capitalmaster_confirm_delete.html' # Rendered in modal
    success_url = reverse_lazy('capital_master_list')

    def post(self, request, *args, **kwargs):
        self.object = self.get_object()
        if self.object.has_details():
            messages.error(self.request, 'Cannot delete Capital Master with associated details.')
            if request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204, # Still no content, but trigger refresh to show message
                    headers={'HX-Trigger': 'showErrorMessage'} # Custom trigger to display message
                )
            return self.http_response_redirect_to_success_url() # Fallback for non-HTMX
        
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Capital Master deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshCapitalMasterList'}
            )
        return response


class CapitalDetailCreateView(CreateView):
    model = CapitalDetail
    form_class = CapitalDetailForm
    template_name = 'capital/_capitaldetail_form.html' # Rendered in modal
    # success_url will be dynamic via HX-Trigger

    def get_initial(self):
        initial = super().get_initial()
        initial['master'] = self.kwargs.get('master_pk') # Get master_pk from URL
        return initial

    def form_valid(self, form):
        master_pk = self.kwargs.get('master_pk')
        master = get_object_or_404(CapitalMaster, pk=master_pk)
        form.instance.master = master # Associate detail with the master
        response = super().form_valid(form)
        messages.success(self.request, 'Capital Detail added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': f'refreshCapitalDetailList_{master_pk}' # Trigger refresh for specific master's details
                }
            )
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['master_pk'] = self.kwargs.get('master_pk')
        return context


class CapitalDetailUpdateView(UpdateView):
    model = CapitalDetail
    form_class = CapitalDetailForm
    template_name = 'capital/_capitaldetail_form.html' # Rendered in modal
    # success_url will be dynamic via HX-Trigger

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Capital Detail updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': f'refreshCapitalDetailList_{self.object.master.pk}'
                }
            )
        return response

class CapitalDetailDeleteView(DeleteView):
    model = CapitalDetail
    template_name = 'capital/_capitaldetail_confirm_delete.html' # Rendered in modal
    # success_url will be dynamic via HX-Trigger

    def post(self, request, *args, **kwargs):
        self.object = self.get_object()
        master_pk_to_refresh = self.object.master.pk # Capture before delete
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Capital Detail deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': f'refreshCapitalDetailList_{master_pk_to_refresh}'
                }
            )
        return response

```

#### 4.4 Templates (`capital/templates/capital/`)

We will create a `list.html` for the main page, and several partial templates for HTMX loading.

**`capital/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6">Capital Management</h2>

    <div class="flex justify-between items-start space-x-8">
        <!-- Master Grid Panel -->
        <div class="w-1/2 bg-white p-6 rounded-lg shadow-lg">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-semibold text-gray-800">Capital Masters</h3>
                <button
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                    hx-get="{% url 'capital_master_add' %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Add New Master
                </button>
            </div>
            
            <div id="capitalMasterTable-container"
                 hx-trigger="load, refreshCapitalMasterList from:body"
                 hx-get="{% url 'capital_master_table' %}"
                 hx-swap="innerHTML">
                <!-- Master DataTable will be loaded here via HTMX -->
                <div class="text-center">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2">Loading Master Data...</p>
                </div>
            </div>
        </div>

        <!-- Detail Grid Panel -->
        <div class="w-1/2 bg-white p-6 rounded-lg shadow-lg">
            <div id="capitalDetailArea">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">Capital Details</h3>
                <div id="capitalDetailTable-container">
                    <div class="p-4 bg-gray-100 rounded-md text-gray-600 text-center">
                        <p>Select a Capital Master from the left to view its details.</p>
                        <p class="mt-2 text-sm">Or, if no masters exist, add one first.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modals for forms -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .hidden from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>

    <!-- Toast for messages -->
    <div x-data="{ show: false, message: '' }"
         x-init="$watch('show', value => { if (value) setTimeout(() => show = false, 3000) });
                 document.body.addEventListener('htmx:afterRequest', (event) => {
                    if (event.detail.xhr.status === 204 && event.detail.xhr.getResponseHeader('HX-Trigger')) {
                        const trigger = JSON.parse(event.detail.xhr.getResponseHeader('HX-Trigger'));
                        if (trigger.showErrorMessage) { // This is specific for 'cannot delete'
                            show = true;
                            message = 'Cannot delete Capital Master with associated details.';
                        } else if (trigger.refreshCapitalMasterList || trigger.startsWith('refreshCapitalDetailList_')) {
                             // This is generally handled by the success message, but for consistency
                             // messages will be displayed by the global message block in base.html
                        }
                    }
                 });"
         x-show="show"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 translate-y-4"
         x-transition:enter-end="opacity-100 translate-y-0"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 translate-y-0"
         x-transition:leave-end="opacity-0 translate-y-4"
         class="fixed bottom-4 right-4 z-50 p-4 rounded-md shadow-lg text-white bg-red-600">
        <p x-text="message"></p>
    </div>

</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.store('modal', {
            open: false,
            content: '',
            show() { this.open = true },
            hide() { this.open = false }
        });
    });

    // Global HTMX event listener for messages, as messages.success/error are handled by base.html
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.xhr.getResponseHeader('HX-Trigger') && JSON.parse(evt.detail.xhr.getResponseHeader('HX-Trigger')).showErrorMessage) {
            // Specific handling for delete error message, as per analysis
            // This is already done by the AlpineJS toast above, can be removed if global message system is preferred
        }
    });

    // Initialize DataTables on dynamically loaded content
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'capitalMasterTable-container') {
            $('#capitalMasterTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true // Destroy existing DataTable before reinitializing
            });
        }
        if (event.detail.target.id === 'capitalDetailTable-container') {
             $('#capitalDetailTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true // Destroy existing DataTable before reinitializing
            });
        }
    });
</script>
{% endblock %}
```

**`capital/_capitalmaster_table.html`** (HTMX partial for master list)

```html
<table id="capitalMasterTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Particulars</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% if capital_masters %}
            {% for master in capital_masters %}
            <tr class="{% cycle 'bg-gray-50' 'bg-white' %}">
                <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200">
                    <button
                        class="text-blue-600 hover:underline font-medium"
                        hx-get="{% url 'capital_detail_table' master.pk %}"
                        hx-target="#capitalDetailTable-container"
                        hx-swap="innerHTML">
                        {{ master.particulars }}
                    </button>
                </td>
                <td class="py-2 px-4 border-b border-gray-200">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                        hx-get="{% url 'capital_master_edit' master.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    {% if not master.has_details %}
                    <button
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                        hx-get="{% url 'capital_master_delete' master.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                    {% else %}
                    <button
                        class="bg-gray-300 text-gray-600 font-bold py-1 px-2 rounded cursor-not-allowed"
                        disabled
                        title="Cannot delete: Has associated details">
                        Delete
                    </button>
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        {% else %}
        <tr>
            <td colspan="3" class="py-4 px-4 text-center text-gray-500">No Capital Master records found. Click "Add New Master" to create one.</td>
        </tr>
        {% endif %}
    </tbody>
</table>
```

**`capital/_capitaldetail_table.html`** (HTMX partial for detail list)

```html
{% if current_master %}
    <div class="flex justify-between items-center mb-4">
        <h3 class="text-xl font-semibold text-gray-800">Details for: "{{ current_master.particulars }}"</h3>
        <button
            class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'capital_detail_add' current_master.pk %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Detail
        </button>
    </div>

    <div id="capitalDetailTable-{{ current_master.pk }}-container"
         hx-trigger="load, refreshCapitalDetailList_{{ current_master.pk }} from:body"
         hx-get="{% url 'capital_detail_table' current_master.pk %}"
         hx-swap="innerHTML">
        <table id="capitalDetailTable" class="min-w-full bg-white border border-gray-200">
            <thead>
                <tr>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Particulars</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Credit Amount</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% if capital_details %}
                    {% for detail in capital_details %}
                    <tr class="{% cycle 'bg-gray-50' 'bg-white' %}">
                        <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ detail.particulars }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ detail.credit_amt|floatformat:2 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">
                            <button
                                class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                                hx-get="{% url 'capital_detail_edit' detail.pk %}"
                                hx-target="#modalContent"
                                hx-trigger="click"
                                _="on click add .is-active to #modal">
                                Edit
                            </button>
                            <button
                                class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                                hx-get="{% url 'capital_detail_delete' detail.pk %}"
                                hx-target="#modalContent"
                                hx-trigger="click"
                                _="on click add .is-active to #modal">
                                Delete
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                    <tr>
                        <td colspan="2" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right font-bold">Total Credit:</td>
                        <td class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right font-bold">{{ current_master.get_total_credit_amount|floatformat:2 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 bg-gray-50"></td>
                    </tr>
                {% else %}
                <tr>
                    <td colspan="4" class="py-4 px-4 text-center text-gray-500">No Capital Detail records found for this master. Click "Add New Detail" to create one.</td>
                </tr>
                {% endif %}
            </tbody>
        </table>
    </div>
{% else %}
    <div class="p-4 bg-gray-100 rounded-md text-gray-600 text-center">
        <p>Select a Capital Master from the left to view its details.</p>
        <p class="mt-2 text-sm">Or, if no masters exist, add one first.</p>
    </div>
{% endif %}
```

**`capital/_capitalmaster_form.html`** (Modal content for master CRUD)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Capital Master</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) { _('remove .is-active from #modal') }">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`capital/_capitalmaster_confirm_delete.html`** (Modal content for master deletion)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Capital Master: "{{ object.particulars }}"?</p>
    
    <form hx-post="{% url 'capital_master_delete' object.pk %}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) { _('remove .is-active from #modal') }">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

**`capital/_capitaldetail_form.html`** (Modal content for detail CRUD)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Capital Detail</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) { _('remove .is-active from #modal') }">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`capital/_capitaldetail_confirm_delete.html`** (Modal content for detail deletion)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete this Capital Detail: "{{ object.particulars }} ({{ object.credit_amt }})"?</p>
    
    <form hx-post="{% url 'capital_detail_delete' object.pk %}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) { _('remove .is-active from #modal') }">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`capital/urls.py`)

URL patterns for accessing the main page, HTMX partials, and CRUD operations.

```python
from django.urls import path
from .views import (
    CapitalMasterListView,
    CapitalMasterTablePartialView,
    CapitalMasterCreateView,
    CapitalMasterUpdateView,
    CapitalMasterDeleteView,
    CapitalDetailTablePartialView,
    CapitalDetailCreateView,
    CapitalDetailUpdateView,
    CapitalDetailDeleteView,
)

urlpatterns = [
    # Master records
    path('capital/', CapitalMasterListView.as_view(), name='capital_master_list'),
    path('capital/table/', CapitalMasterTablePartialView.as_view(), name='capital_master_table'),
    path('capital/add/', CapitalMasterCreateView.as_view(), name='capital_master_add'),
    path('capital/edit/<int:pk>/', CapitalMasterUpdateView.as_view(), name='capital_master_edit'),
    path('capital/delete/<int:pk>/', CapitalMasterDeleteView.as_view(), name='capital_master_delete'),

    # Detail records (linked to master_pk)
    path('capital/<int:pk>/details/', CapitalDetailTablePartialView.as_view(), name='capital_detail_table'),
    path('capital/<int:master_pk>/details/add/', CapitalDetailCreateView.as_view(), name='capital_detail_add'),
    path('capital/details/edit/<int:pk>/', CapitalDetailUpdateView.as_view(), name='capital_detail_edit'),
    path('capital/details/delete/<int:pk>/', CapitalDetailDeleteView.as_view(), name='capital_detail_delete'),
]
```

#### 4.6 Tests (`capital/tests.py`)

Comprehensive unit tests for models and integration tests for views, aiming for high coverage.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import CapitalMaster, CapitalDetail
from .forms import CapitalMasterForm, CapitalDetailForm
from unittest.mock import patch
from datetime import date

# Mock the session variables for consistent testing
mock_session_vars = {
    'comp_id': 1,
    'fin_year_id': 2024,
    'session_id': 'test_user',
    'sys_date': date(2023, 1, 1),
    'sys_time': '10:00 AM'
}

@patch('capital.views.get_mock_session_vars', return_value=mock_session_vars)
class CapitalMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.master1 = CapitalMaster.objects.create(
            particulars='Test Master 1',
            comp_id=1, fin_year_id=2024, session_id='user1',
            sys_date=date(2023, 1, 1), sys_time='10:00 AM'
        )
        cls.master2 = CapitalMaster.objects.create(
            particulars='Test Master 2',
            comp_id=1, fin_year_id=2024, session_id='user1',
            sys_date=date(2023, 1, 2), sys_time='11:00 AM'
        )
        CapitalDetail.objects.create(
            master=cls.master1, particulars='Detail 1 for Master 1', credit_amt=100.50
        )
        CapitalDetail.objects.create(
            master=cls.master1, particulars='Detail 2 for Master 1', credit_amt=200.00
        )

    def test_capital_master_creation(self, mock_get_mock_session_vars):
        master = CapitalMaster.objects.get(id=self.master1.id)
        self.assertEqual(master.particulars, 'Test Master 1')
        self.assertEqual(master.comp_id, 1)
        self.assertEqual(master.fin_year_id, 2024)
        self.assertEqual(master.session_id, 'user1')
        self.assertEqual(master.sys_date, date(2023, 1, 1))
        self.assertEqual(master.sys_time, '10:00 AM')

    def test_master_has_details_method(self, mock_get_mock_session_vars):
        self.assertTrue(self.master1.has_details())
        self.assertFalse(self.master2.has_details())

    def test_master_get_total_credit_amount(self, mock_get_mock_session_vars):
        self.assertEqual(self.master1.get_total_credit_amount(), 300.50)
        self.assertEqual(self.master2.get_total_credit_amount(), 0)

    def test_master_particulars_label(self, mock_get_mock_session_vars):
        field_label = self.master1._meta.get_field('particulars').verbose_name
        self.assertEqual(field_label, 'Particulars')

class CapitalDetailModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.master = CapitalMaster.objects.create(
            particulars='Test Master', comp_id=1, fin_year_id=2024, session_id='user1',
            sys_date=date(2023, 1, 1), sys_time='10:00 AM'
        )
        cls.detail1 = CapitalDetail.objects.create(
            master=cls.master, particulars='Test Detail 1', credit_amt=50.75
        )

    def test_capital_detail_creation(self):
        detail = CapitalDetail.objects.get(id=self.detail1.id)
        self.assertEqual(detail.master, self.master)
        self.assertEqual(detail.particulars, 'Test Detail 1')
        self.assertEqual(detail.credit_amt, 50.75)

    def test_detail_particulars_label(self):
        field_label = self.detail1._meta.get_field('particulars').verbose_name
        self.assertEqual(field_label, 'Particulars')

    def test_detail_credit_amt_label(self):
        field_label = self.detail1._meta.get_field('credit_amt').verbose_name
        self.assertEqual(field_label, 'Credit Amt')

    def test_detail_clean_method_positive_amount(self):
        detail = CapitalDetail(master=self.master, particulars='Valid Detail', credit_amt=10.00)
        try:
            detail.full_clean()
        except Exception as e:
            self.fail(f"full_clean raised an unexpected exception: {e}")

    def test_detail_clean_method_non_positive_amount(self):
        detail = CapitalDetail(master=self.master, particulars='Invalid Detail', credit_amt=-5.00)
        with self.assertRaisesMessage(forms.ValidationError, 'Credit Amount must be a positive value.'):
            detail.full_clean()
        
        detail_zero = CapitalDetail(master=self.master, particulars='Invalid Detail', credit_amt=0.00)
        with self.assertRaisesMessage(forms.ValidationError, 'Credit Amount must be a positive value.'):
            detail_zero.full_clean()

@patch('capital.views.get_mock_session_vars', return_value=mock_session_vars)
class CapitalMasterViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.master1 = CapitalMaster.objects.create(
            particulars='Master A', comp_id=1, fin_year_id=2024, session_id='user1',
            sys_date=date(2023, 1, 1), sys_time='10:00 AM'
        )
        self.master2 = CapitalMaster.objects.create(
            particulars='Master B', comp_id=1, fin_year_id=2024, session_id='user1',
            sys_date=date(2023, 1, 1), sys_time='10:00 AM'
        )
        self.detail = CapitalDetail.objects.create(
            master=self.master1, particulars='Detail for Master A', credit_amt=100.00
        )

    def test_master_list_view(self, mock_get_mock_session_vars):
        response = self.client.get(reverse('capital_master_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'capital/list.html')
        self.assertIn('capital_masters', response.context)
        self.assertEqual(response.context['capital_masters'].count(), 2)

    def test_master_table_partial_view(self, mock_get_mock_session_vars):
        response = self.client.get(reverse('capital_master_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'capital/_capitalmaster_table.html')
        self.assertIn('capital_masters', response.context)
        self.assertEqual(response.context['capital_masters'].count(), 2)
        self.assertContains(response, 'Master A')
        self.assertContains(response, 'Master B')

    def test_master_create_view_get(self, mock_get_mock_session_vars):
        response = self.client.get(reverse('capital_master_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'capital/_capitalmaster_form.html')
        self.assertIn('form', response.context)

    def test_master_create_view_post_success(self, mock_get_mock_session_vars):
        initial_count = CapitalMaster.objects.count()
        data = {'particulars': 'New Master'}
        response = self.client.post(reverse('capital_master_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HX-Request should return 204
        self.assertEqual(CapitalMaster.objects.count(), initial_count + 1)
        self.assertTrue(CapitalMaster.objects.filter(particulars='New Master').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshCapitalMasterList', response.headers['HX-Trigger'])

    def test_master_create_view_post_invalid(self, mock_get_mock_session_vars):
        initial_count = CapitalMaster.objects.count()
        data = {'particulars': ''} # Invalid data
        response = self.client.post(reverse('capital_master_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-render with errors
        self.assertTemplateUsed(response, 'capital/_capitalmaster_form.html')
        self.assertIn('form', response.context)
        self.assertFalse(CapitalMaster.objects.filter(particulars='').exists())
        self.assertEqual(CapitalMaster.objects.count(), initial_count)
        self.assertContains(response, "Particulars cannot be empty.")

    def test_master_update_view_get(self, mock_get_mock_session_vars):
        response = self.client.get(reverse('capital_master_edit', args=[self.master1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'capital/_capitalmaster_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.master1)

    def test_master_update_view_post_success(self, mock_get_mock_session_vars):
        data = {'particulars': 'Updated Master A'}
        response = self.client.post(reverse('capital_master_edit', args=[self.master1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.master1.refresh_from_db()
        self.assertEqual(self.master1.particulars, 'Updated Master A')
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshCapitalMasterList', response.headers['HX-Trigger'])

    def test_master_delete_view_get(self, mock_get_mock_session_vars):
        response = self.client.get(reverse('capital_master_delete', args=[self.master2.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'capital/_capitalmaster_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.master2)

    def test_master_delete_view_post_success(self, mock_get_mock_session_vars):
        # master2 has no details, so it can be deleted
        initial_count = CapitalMaster.objects.count()
        response = self.client.post(reverse('capital_master_delete', args=[self.master2.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(CapitalMaster.objects.count(), initial_count - 1)
        self.assertFalse(CapitalMaster.objects.filter(pk=self.master2.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshCapitalMasterList', response.headers['HX-Trigger'])

    def test_master_delete_view_post_with_details(self, mock_get_mock_session_vars):
        # master1 has details, so it cannot be deleted
        initial_count = CapitalMaster.objects.count()
        response = self.client.post(reverse('capital_master_delete', args=[self.master1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # Still 204 for HX-Request
        self.assertEqual(CapitalMaster.objects.count(), initial_count) # Not deleted
        self.assertTrue(CapitalMaster.objects.filter(pk=self.master1.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('showErrorMessage', response.headers['HX-Trigger']) # Custom trigger for error

@patch('capital.views.get_mock_session_vars', return_value=mock_session_vars)
class CapitalDetailViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.master = CapitalMaster.objects.create(
            particulars='Master For Details', comp_id=1, fin_year_id=2024, session_id='user1',
            sys_date=date(2023, 1, 1), sys_time='10:00 AM'
        )
        self.detail1 = CapitalDetail.objects.create(
            master=self.master, particulars='Detail X', credit_amt=10.00
        )
        self.detail2 = CapitalDetail.objects.create(
            master=self.master, particulars='Detail Y', credit_amt=20.00
        )

    def test_detail_table_partial_view(self, mock_get_mock_session_vars):
        response = self.client.get(reverse('capital_detail_table', args=[self.master.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'capital/_capitaldetail_table.html')
        self.assertIn('capital_details', response.context)
        self.assertIn('current_master', response.context)
        self.assertEqual(response.context['current_master'], self.master)
        self.assertEqual(response.context['capital_details'].count(), 2)
        self.assertContains(response, 'Detail X')
        self.assertContains(response, 'Detail Y')

    def test_detail_create_view_get(self, mock_get_mock_session_vars):
        response = self.client.get(reverse('capital_detail_add', args=[self.master.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'capital/_capitaldetail_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].initial['master'], self.master.pk)

    def test_detail_create_view_post_success(self, mock_get_mock_session_vars):
        initial_count = CapitalDetail.objects.count()
        data = {'particulars': 'New Detail', 'credit_amt': '150.75'}
        response = self.client.post(reverse('capital_detail_add', args=[self.master.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(CapitalDetail.objects.count(), initial_count + 1)
        new_detail = CapitalDetail.objects.get(particulars='New Detail')
        self.assertEqual(new_detail.master, self.master)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn(f'refreshCapitalDetailList_{self.master.pk}', response.headers['HX-Trigger'])

    def test_detail_create_view_post_invalid(self, mock_get_mock_session_vars):
        initial_count = CapitalDetail.objects.count()
        data = {'particulars': '', 'credit_amt': '-100'} # Invalid data
        response = self.client.post(reverse('capital_detail_add', args=[self.master.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-render with errors
        self.assertTemplateUsed(response, 'capital/_capitaldetail_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Credit Amount must be a positive value.')
        self.assertEqual(CapitalDetail.objects.count(), initial_count)

    def test_detail_update_view_get(self, mock_get_mock_session_vars):
        response = self.client.get(reverse('capital_detail_edit', args=[self.detail1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'capital/_capitaldetail_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.detail1)

    def test_detail_update_view_post_success(self, mock_get_mock_session_vars):
        data = {'particulars': 'Updated Detail X', 'credit_amt': '110.00'}
        response = self.client.post(reverse('capital_detail_edit', args=[self.detail1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.detail1.refresh_from_db()
        self.assertEqual(self.detail1.particulars, 'Updated Detail X')
        self.assertEqual(self.detail1.credit_amt, 110.00)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn(f'refreshCapitalDetailList_{self.master.pk}', response.headers['HX-Trigger'])

    def test_detail_delete_view_get(self, mock_get_mock_session_vars):
        response = self.client.get(reverse('capital_detail_delete', args=[self.detail2.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'capital/_capitaldetail_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.detail2)

    def test_detail_delete_view_post_success(self, mock_get_mock_session_vars):
        initial_count = CapitalDetail.objects.count()
        response = self.client.post(reverse('capital_detail_delete', args=[self.detail2.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(CapitalDetail.objects.count(), initial_count - 1)
        self.assertFalse(CapitalDetail.objects.filter(pk=self.detail2.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn(f'refreshCapitalDetailList_{self.master.pk}', response.headers['HX-Trigger'])

```

### Step 5: HTMX and Alpine.js Integration

**Instructions and Implementation Details:**

1.  **Main Page (`capital/list.html`):**
    *   The `capital_master_table` is loaded via `hx-get="{% url 'capital_master_table' %}"` with `hx-trigger="load, refreshCapitalMasterList from:body"`. This ensures the master list is populated on initial page load and refreshed whenever `refreshCapitalMasterList` is triggered (after master CRUD operations).
    *   A main modal (`#modal`) is defined. Alpine.js manages its `hidden` class. HTMX requests (e.g., `hx-get="{% url 'capital_master_add' %}"`) load form content into `#modalContent` and automatically trigger Alpine.js to show the modal (`_="on click add .is-active to #modal"`).
    *   The `capitalDetailTable-container` acts as a placeholder for the detail table.

2.  **Master Table (`capital/_capitalmaster_table.html`):**
    *   **DataTables:** Initialized with `$(document).ready(function() { $('#capitalMasterTable').DataTable(...); });` in `list.html`'s `htmx:afterSwap` listener. `destroy: true` ensures re-initialization on subsequent HTMX loads.
    *   **View Details (LinkButton to `HpPerticulars`):** The "Particulars" column buttons use `hx-get="{% url 'capital_detail_table' master.pk %}"` and `hx-target="#capitalDetailTable-container"` to load the specific master's details into the right panel without a full page reload.
    *   **Conditional Delete:** The `{% if not master.has_details %}` template tag conditionally renders the "Delete" button. `master.has_details()` is a model method, keeping the view thin.
    *   **CRUD Buttons:** "Edit" and "Delete" buttons for master records use `hx-get` to load forms/confirmations into the modal. The `hx-target` is `#modalContent`, and `_="on click add .is-active to #modal"` opens the modal.

3.  **Detail Table (`capital/_capitaldetail_table.html`):**
    *   **Dynamic Loading:** This template is loaded by HTMX when a master record's "Particulars" is clicked.
    *   **DataTables:** Similar to the master table, DataTables is initialized via the `htmx:afterSwap` event listener in `list.html`.
    *   **CRUD Buttons:** "Edit" and "Delete" buttons for detail records also use `hx-get` to load forms/confirmations into the modal.
    *   **HTMX Refresh:** After any detail CRUD operation, the `HX-Trigger` header (`refreshCapitalDetailList_{{ master_pk }}`) ensures that only the relevant detail table is reloaded, optimizing performance.

4.  **Form & Delete Confirmation Partials (`_form.html`, `_confirm_delete.html`):**
    *   These templates are designed to be loaded into the modal.
    *   Forms use `hx-post="{{ request.path }}" hx-swap="none"` for submission. `hx-swap="none"` is crucial for a 204 No Content response.
    *   `hx-on::after-request="if(event.detail.xhr.status === 204) { _('remove .is-active from #modal') }"` automatically closes the modal upon successful form submission (indicated by a 204 status).
    *   Django's `messages` framework is used for user feedback, which is then rendered by the `core/base.html` template (not included here, but assumed to display `messages` effectively).
    *   A custom `HX-Trigger` `showErrorMessage` is used for the specific case where a master record cannot be deleted due to associated details, which is then caught by an Alpine.js toast for user notification.

This comprehensive plan ensures a smooth transition from the legacy ASP.NET application to a modern, maintainable, and highly interactive Django solution, adhering to best practices and leveraging automation-friendly design.

---