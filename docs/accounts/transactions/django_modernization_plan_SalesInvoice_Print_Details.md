## ASP.NET to Django Conversion Script: Sales Invoice Print Details

This modernization plan outlines the strategic transition of your ASP.NET sales invoice printing functionality to a robust and scalable Django 5.0+ application. Our approach prioritizes automated conversion, leveraging Django's "Fat Model, Thin View" architecture, and modern frontend techniques like HTMX and Alpine.js to deliver a highly performant and maintainable solution.

### Business Value Proposition

Migrating this sales invoice print functionality to Django offers significant benefits:

1.  **Reduced Technical Debt:** Moves away from a legacy reporting solution (Crystal Reports) that often requires specific client-side viewers or complex server configurations, simplifying deployment and maintenance.
2.  **Enhanced Maintainability:** Django's structured ORM (Object-Relational Mapper) replaces direct SQL queries, making data access more secure, readable, and less prone to errors. Business logic is strictly contained within models, simplifying future updates.
3.  **Improved Performance:** By processing data server-side and rendering efficient HTML, the solution can be optimized for faster load times and responsiveness, especially compared to heavy report viewers.
4.  **Modern User Experience:** Leveraging HTMX and Alpine.js provides a dynamic and interactive feel without the complexity of traditional JavaScript frameworks, ensuring a smooth experience for users viewing and printing invoices.
5.  **Future Scalability:** Django's robust ecosystem and Python's flexibility allow for easier integration with other systems and future expansion of reporting capabilities (e.g., PDF generation, interactive dashboards).
6.  **Cost Efficiency:** Automating much of the migration process and utilizing open-source technologies significantly reduces development time and licensing costs associated with proprietary reporting tools.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination (Note: For this specific report view, DataTables is not directly applicable as it's a single detailed report, not a list. We will apply it to list views elsewhere in the system as per general guidelines.)
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in generating the sales invoice report.

**Analysis:** The ASP.NET code heavily interacts with `tblACC_SalesInvoice_Master` as its primary data source. It then performs numerous lookups and joins with other tables to gather comprehensive details for the report.

**Inferred Tables and Key Columns:**

*   **`tblACC_SalesInvoice_Master` (Primary Model: `SalesInvoiceMaster`)**:
    *   `Id` (PK, int)
    *   `SysDate` (datetime)
    *   `CompId` (FK to `tblCompany_master`, int)
    *   `FinYearId` (FK to `tblFinancial_master`, int)
    *   `InvoiceNo` (string)
    *   `PONo` (string)
    *   `WONo` (string, comma-separated IDs)
    *   `InvoiceMode` (string)
    *   `DateOfIssueInvoice` (datetime)
    *   `DateOfRemoval` (datetime)
    *   `TimeOfIssueInvoice` (string)
    *   `TimeOfRemoval` (string)
    *   `NatureOfRemoval` (FK to `tblACC_Removable_Nature`, int)
    *   `Commodity` (FK to `tblExciseCommodity_Master`, int)
    *   `ModeOfTransport` (FK to `tblACC_TransportMode`, int)
    *   `RRGCNo` (string)
    *   `VehiRegNo` (string)
    *   `DutyRate` (string)
    *   `CustomerCode` (string)
    *   `CustomerCategory` (FK to `tblACC_Service_Category`, int)
    *   `Buyer_name` (string)
    *   `Buyer_cotper` (string)
    *   `Buyer_ph` (string)
    *   `Buyer_email` (string)
    *   `Buyer_ecc` (string)
    *   `Buyer_tin` (string)
    *   `Buyer_mob` (string)
    *   `Buyer_fax` (string)
    *   `Buyer_vat` (string)
    *   `Buyer_add` (string)
    *   `Buyer_country` (FK to `tblcountry`, int)
    *   `Buyer_state` (FK to `tblState`, int)
    *   `Buyer_city` (FK to `tblCity`, int)
    *   `Cong_name` (string)
    *   `Cong_cotper` (string)
    *   `Cong_ph` (string)
    *   `Cong_email` (string)
    *   `Cong_ecc` (string)
    *   `Cong_tin` (string)
    *   `Cong_mob` (string)
    *   `Cong_fax` (string)
    *   `Cong_vat` (string)
    *   `Cong_add` (string)
    *   `Cong_country` (FK to `tblcountry`, int)
    *   `Cong_state` (FK to `tblState`, int)
    *   `Cong_city` (FK to `tblCity`, int)
    *   `AddType` (int)
    *   `AddAmt` (double)
    *   `DeductionType` (int)
    *   `Deduction` (double)
    *   `PFType` (int)
    *   `PF` (double)
    *   `CENVAT` (int)
    *   `SED` (double)
    *   `AED` (double)
    *   `VAT` (FK to `tblVAT_Master`, int)
    *   `SelectedCST` (int)
    *   `CST` (FK to `tblVAT_Master`, int, note: this is confusingly also named CST, but it's an ID)
    *   `FreightType` (int)
    *   `Freight` (double)
    *   `InsuranceType` (int)
    *   `Insurance` (double)
    *   `POId` (FK to `SD_Cust_PO_Master`, int)
    *   `AEDType` (int)
    *   `SEDType` (int)
    *   `OtherAmt` (double)

*   **Helper Tables (for lookups and related data):**
    *   `tblACC_Removable_Nature` (`Id`, `Description`)
    *   `tblExciseCommodity_Master` (`Id`, `Terms`, `ChapHead`)
    *   `tblACC_TransportMode` (`Id`, `Description`)
    *   `tblACC_Service_Category` (`Id`, `Description`)
    *   `SD_Cust_WorkOrder_Master` (`Id`, `WONo`)
    *   `SD_Cust_PO_Master` (`POId`, `PODate`)
    *   `tblFinancial_master` (`FinYearId`, `CompId`, `FinYearFrom`, `FinYearTo`)
    *   `tblcountry` (`CId`, `CountryName`)
    *   `tblState` (`SId`, `StateName`)
    *   `tblCity` (`CityId`, `CityName`)
    *   `tblCompany_master` (`CompId`, `RegdAddress`, `RegdCity`, `RegdState`, `RegdCountry`, `RegdPinCode`, `RegdContactNo`, `RegdFaxNo`, `RegdEmail`)
    *   `tblVAT_Master` (`Id`, `Terms`, `Value`)

### Step 2: Identify Backend Functionality

**Task:** Determine the operations performed by the ASP.NET code.

**Analysis:** This ASP.NET page is exclusively a **read-only report generation and display** interface.

*   **Read Operation:** The primary function is to fetch a specific sales invoice record (based on `InvId` and `InvNo` from query strings), gather related data from numerous lookup tables, apply formatting and calculations, and then display this aggregated information using Crystal Reports.
*   **No CRUD:** There are no explicit Create, Update, or Delete operations on sales invoices within this specific ASP.NET page. The `btnCancel` merely redirects the user.
*   **Complex Data Aggregation:** The C# code showcases extensive data retrieval and transformation logic (e.g., building addresses, calculating freight/VAT/CST terms, converting dates/times to text). This logic is crucial and will be moved to the Django models as part of the "Fat Model" approach.
*   **Session Management:** The page stores the Crystal Report document in `Session` for postbacks. In Django, this statefulness is handled by re-rendering based on URL parameters or potentially leveraging caching if the report is very expensive to generate.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:** The UI is straightforward, primarily focusing on displaying a generated report.

*   **CrystalReportViewer:** The core component for displaying the report. In Django, this will be replaced by a well-structured HTML template that dynamically renders the aggregated report data.
*   **Button (`btnCancel`):** A simple button for navigation. This will be a standard HTML button or link styled with Tailwind CSS, potentially using HTMX for a smooth redirect or history navigation.
*   **Master Page Integration:** The ASP.NET page uses a master page. In Django, this translates to template inheritance, where `salesinvoice_print_details.html` will extend `core/base.html`.
*   **Styling:** Custom CSS and a loading notifier script are present. These will be replaced by Tailwind CSS for styling and possibly simple Alpine.js for loading indicators.

### Step 4: Generate Django Code

We will create a new Django application, `sales_invoices`, to house this functionality.

#### 4.1 Models (`sales_invoices/models.py`)

We will define models for the primary `SalesInvoiceMaster` table and key lookup tables. The complex report generation logic will be encapsulated within methods of the `SalesInvoiceMaster` model, demonstrating the "Fat Model" principle. Utility functions will also be implemented as class methods or helper functions within the model's scope.

```python
from django.db import models
from django.urls import reverse
from django.utils import timezone
from datetime import datetime
from num2words import num2words # Requires: pip install num2words

# --- Lookup / Related Models ---
# These models map directly to existing database tables.
# Ensure 'managed = False' as they represent existing tables.

class CompanyMaster(models.Model):
    compid = models.IntegerField(db_column='CompId', primary_key=True)
    regdaddress = models.TextField(db_column='RegdAddress', blank=True, null=True)
    regdcity_id = models.IntegerField(db_column='RegdCity', blank=True, null=True)
    regdstate_id = models.IntegerField(db_column='RegdState', blank=True, null=True)
    regdcountry_id = models.IntegerField(db_column='RegdCountry', blank=True, null=True)
    regdpincode = models.CharField(db_column='RegdPinCode', max_length=50, blank=True, null=True)
    regdcontactno = models.CharField(db_column='RegdContactNo', max_length=50, blank=True, null=True)
    regdfaxno = models.CharField(db_column='RegdFaxNo', max_length=50, blank=True, null=True)
    regdemail = models.CharField(db_column='RegdEmail', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCompany_master'
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def get_full_address(self):
        city = City.objects.filter(cityid=self.regdcity_id).first()
        state = State.objects.filter(sid=self.regdstate_id).first()
        country = Country.objects.filter(cid=self.regdcountry_id).first()

        address_parts = [self.regdaddress]
        if city: address_parts.append(city.cityname)
        if state: address_parts.append(state.statename)
        if country: address_parts.append(country.countryname)
        address_parts.append(f"PIN No.-{self.regdpincode}")
        address_parts.append(f"Ph No.-{self.regdcontactno}, Fax No.-{self.regdfaxno}")
        address_parts.append(f"Email No.-{self.regdemail}")
        
        return ",\n".join(filter(None, address_parts))

class FinancialMaster(models.Model):
    finyearid = models.IntegerField(db_column='FinYearId', primary_key=True)
    compid = models.ForeignKey(CompanyMaster, models.DO_NOTHING, db_column='CompId', blank=True, null=True)
    finyearfrom = models.DateTimeField(db_column='FinYearFrom', blank=True, null=True)
    finyearto = models.DateTimeField(db_column='FinYearTo', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

class Country(models.Model):
    cid = models.IntegerField(db_column='CId', primary_key=True)
    countryname = models.CharField(db_column='CountryName', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblcountry'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.countryname or ''

class State(models.Model):
    sid = models.IntegerField(db_column='SId', primary_key=True)
    statename = models.CharField(db_column='StateName', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblState'
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.statename or ''

class City(models.Model):
    cityid = models.IntegerField(db_column='CityId', primary_key=True)
    cityname = models.CharField(db_column='CityName', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCity'
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.cityname or ''

class RemovableNature(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_Removable_Nature'
        verbose_name = 'Nature of Removal'
        verbose_name_plural = 'Natures of Removal'

    def __str__(self):
        return self.description or ''

class ExciseCommodity(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255, blank=True, null=True)
    chaphead = models.CharField(db_column='ChapHead', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblExciseCommodity_Master'
        verbose_name = 'Excise Commodity'
        verbose_name_plural = 'Excise Commodities'

    def __str__(self):
        return self.terms or ''

class TransportMode(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_TransportMode'
        verbose_name = 'Transport Mode'
        verbose_name_plural = 'Transport Modes'

    def __str__(self):
        return self.description or ''

class ServiceCategory(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_Service_Category'
        verbose_name = 'Service Category'
        verbose_name_plural = 'Service Categories'

    def __str__(self):
        return self.description or ''

class WorkOrderMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    wono = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    compid = models.ForeignKey(CompanyMaster, models.DO_NOTHING, db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.wono or ''

class POMaster(models.Model):
    poid = models.IntegerField(db_column='POId', primary_key=True)
    podate = models.DateTimeField(db_column='PODate', blank=True, null=True)
    compid = models.ForeignKey(CompanyMaster, models.DO_NOTHING, db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Master'
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'

    def __str__(self):
        return f"PO-{self.poid}"

class VATMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255, blank=True, null=True)
    value = models.FloatField(db_column='Value', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblVAT_Master'
        verbose_name = 'VAT/CST Master'
        verbose_name_plural = 'VAT/CST Masters'

    def __str__(self):
        return self.terms or ''

# --- Main Sales Invoice Model with Reporting Logic ---

class SalesInvoiceMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sysdate = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    compid = models.ForeignKey(CompanyMaster, models.DO_NOTHING, db_column='CompId', blank=True, null=True)
    finyearid = models.ForeignKey(FinancialMaster, models.DO_NOTHING, db_column='FinYearId', blank=True, null=True)
    invoiceno = models.CharField(db_column='InvoiceNo', max_length=50, blank=True, null=True)
    pono = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    wono = models.CharField(db_column='WONo', max_length=255, blank=True, null=True) # Comma-separated IDs
    invoicemode = models.CharField(db_column='InvoiceMode', max_length=50, blank=True, null=True)
    dateofissueinvoice = models.DateTimeField(db_column='DateOfIssueInvoice', blank=True, null=True)
    dateofremoval = models.DateTimeField(db_column='DateOfRemoval', blank=True, null=True)
    timeofissueinvoice = models.CharField(db_column='TimeOfIssueInvoice', max_length=50, blank=True, null=True)
    timeofremoval = models.CharField(db_column='TimeOfRemoval', max_length=50, blank=True, null=True)
    natureofremoval = models.ForeignKey(RemovableNature, models.DO_NOTHING, db_column='NatureOfRemoval', blank=True, null=True)
    commodity = models.ForeignKey(ExciseCommodity, models.DO_NOTHING, db_column='Commodity', blank=True, null=True)
    modeoftransport = models.ForeignKey(TransportMode, models.DO_NOTHING, db_column='ModeOfTransport', blank=True, null=True)
    rrgcno = models.CharField(db_column='RRGCNo', max_length=50, blank=True, null=True)
    vehiregno = models.CharField(db_column='VehiRegNo', max_length=50, blank=True, null=True)
    dutyrate = models.CharField(db_column='DutyRate', max_length=50, blank=True, null=True)
    customercode = models.CharField(db_column='CustomerCode', max_length=50, blank=True, null=True)
    customercategory = models.ForeignKey(ServiceCategory, models.DO_NOTHING, db_column='CustomerCategory', blank=True, null=True)
    buyer_name = models.CharField(db_column='Buyer_name', max_length=255, blank=True, null=True)
    buyer_cotper = models.CharField(db_column='Buyer_cotper', max_length=255, blank=True, null=True)
    buyer_ph = models.CharField(db_column='Buyer_ph', max_length=50, blank=True, null=True)
    buyer_email = models.CharField(db_column='Buyer_email', max_length=100, blank=True, null=True)
    buyer_ecc = models.CharField(db_column='Buyer_ecc', max_length=50, blank=True, null=True)
    buyer_tin = models.CharField(db_column='Buyer_tin', max_length=50, blank=True, null=True)
    buyer_mob = models.CharField(db_column='Buyer_mob', max_length=50, blank=True, null=True)
    buyer_fax = models.CharField(db_column='Buyer_fax', max_length=50, blank=True, null=True)
    buyer_vat = models.CharField(db_column='Buyer_vat', max_length=50, blank=True, null=True)
    buyer_add = models.TextField(db_column='Buyer_add', blank=True, null=True)
    buyer_country = models.ForeignKey(Country, models.DO_NOTHING, db_column='Buyer_country', related_name='buyer_country', blank=True, null=True)
    buyer_state = models.ForeignKey(State, models.DO_NOTHING, db_column='Buyer_state', related_name='buyer_state', blank=True, null=True)
    buyer_city = models.ForeignKey(City, models.DO_NOTHING, db_column='Buyer_city', related_name='buyer_city', blank=True, null=True)
    cong_name = models.CharField(db_column='Cong_name', max_length=255, blank=True, null=True)
    cong_cotper = models.CharField(db_column='Cong_cotper', max_length=255, blank=True, null=True)
    cong_ph = models.CharField(db_column='Cong_ph', max_length=50, blank=True, null=True)
    cong_email = models.CharField(db_column='Cong_email', max_length=100, blank=True, null=True)
    cong_ecc = models.CharField(db_column='Cong_ecc', max_length=50, blank=True, null=True)
    cong_tin = models.CharField(db_column='Cong_tin', max_length=50, blank=True, null=True)
    cong_mob = models.CharField(db_column='Cong_mob', max_length=50, blank=True, null=True)
    cong_fax = models.CharField(db_column='Cong_fax', max_length=50, blank=True, null=True)
    cong_vat = models.CharField(db_column='Cong_vat', max_length=50, blank=True, null=True)
    cong_add = models.TextField(db_column='Cong_add', blank=True, null=True)
    cong_country = models.ForeignKey(Country, models.DO_NOTHING, db_column='Cong_country', related_name='cong_country', blank=True, null=True)
    cong_state = models.ForeignKey(State, models.DO_NOTHING, db_column='Cong_state', related_name='cong_state', blank=True, null=True)
    cong_city = models.ForeignKey(City, models.DO_NOTHING, db_column='Cong_city', related_name='cong_city', blank=True, null=True)
    addtype = models.IntegerField(db_column='AddType', blank=True, null=True)
    addamt = models.FloatField(db_column='AddAmt', blank=True, null=True)
    deductiontype = models.IntegerField(db_column='DeductionType', blank=True, null=True)
    deduction = models.FloatField(db_column='Deduction', blank=True, null=True)
    pftype = models.IntegerField(db_column='PFType', blank=True, null=True)
    pf = models.FloatField(db_column='PF', blank=True, null=True)
    cenvat = models.IntegerField(db_column='CENVAT', blank=True, null=True)
    sed = models.FloatField(db_column='SED', blank=True, null=True)
    aed = models.FloatField(db_column='AED', blank=True, null=True)
    vat = models.ForeignKey(VATMaster, models.DO_NOTHING, db_column='VAT', related_name='vat_master', blank=True, null=True)
    selectedcst = models.IntegerField(db_column='SelectedCST', blank=True, null=True)
    cst = models.ForeignKey(VATMaster, models.DO_NOTHING, db_column='CST', related_name='cst_master', blank=True, null=True)
    freighttype = models.IntegerField(db_column='FreightType', blank=True, null=True)
    freight = models.FloatField(db_column='Freight', blank=True, null=True)
    insurancetype = models.IntegerField(db_column='InsuranceType', blank=True, null=True)
    insurance = models.FloatField(db_column='Insurance', blank=True, null=True)
    poid = models.ForeignKey(POMaster, models.DO_NOTHING, db_column='POId', blank=True, null=True)
    aedtype = models.IntegerField(db_column='AEDType', blank=True, null=True)
    sedtype = models.IntegerField(db_column='SEDType', blank=True, null=True)
    otheramt = models.FloatField(db_column='OtherAmt', blank=True, null=True)


    class Meta:
        managed = False
        db_table = 'tblACC_SalesInvoice_Master'
        verbose_name = 'Sales Invoice'
        verbose_name_plural = 'Sales Invoices'

    def __str__(self):
        return self.invoiceno or f"Invoice {self.id}"

    # Utility methods for date/time conversion and address formatting
    @staticmethod
    def _format_date_dmy(date_obj):
        return date_obj.strftime('%d.%m.%Y') if date_obj else ''

    @staticmethod
    def _date_to_text(date_obj):
        if not date_obj:
            return ''
        return num2words(date_obj.day, lang='en') + ' ' + \
               date_obj.strftime('%B') + ' ' + \
               num2words(date_obj.year, lang='en')

    @staticmethod
    def _time_to_text(time_str):
        try:
            # Assuming time_str is in "HH:mm" format or similar
            time_obj = datetime.strptime(time_str, '%H:%M').time()
            return time_obj.strftime("%I %M %p").replace(' 0', ' ').replace(' ', ' o\'clock ' if time_obj.minute == 0 else ' ').strip()
        except (ValueError, TypeError):
            return ''

    def get_full_buyer_address(self):
        address_parts = [self.buyer_add]
        if self.buyer_city: address_parts.append(self.buyer_city.cityname)
        if self.buyer_state: address_parts.append(self.buyer_state.statename)
        if self.buyer_country: address_parts.append(self.buyer_country.countryname)
        return ",\n".join(filter(None, address_parts)) + "."

    def get_full_consignee_address(self):
        address_parts = [self.cong_add]
        if self.cong_city: address_parts.append(self.cong_city.cityname)
        if self.cong_state: address_parts.append(self.cong_state.statename)
        if self.cong_country: address_parts.append(self.cong_country.countryname)
        return ",\n".join(filter(None, address_parts)) + "."

    # The "Fat Model" method for generating all report data
    def get_report_context(self, print_type=""):
        """
        Aggregates all necessary data and performs calculations for the sales invoice report.
        This method replaces the extensive data processing in the C# Page_Init.
        """
        context = {
            'invoice': self,
            'print_type': print_type,
            'company_address': self.compid.get_full_address() if self.compid else 'N/A',
            'buyer_address': self.get_full_buyer_address(),
            'consignee_address': self.get_full_consignee_address(),
            'formatted_sys_date': self._format_date_dmy(self.sysdate),
            'formatted_issue_date': self._format_date_dmy(self.dateofissueinvoice),
            'formatted_removal_date': self._format_date_dmy(self.dateofremoval),
            'po_date': self._format_date_dmy(self.poid.podate) if self.poid and self.poid.podate else 'N/A',
            'words_removal_date': self._date_to_text(self.dateofremoval),
            'words_removal_time': self._time_to_text(self.timeofremoval),
            'invoice_full_number': self.invoiceno, # Will be appended with financial year suffix
            'work_order_numbers': '',
            'nature_of_removal_desc': self.natureofremoval.description if self.natureofremoval else '',
            'commodity_terms': self.commodity.terms if self.commodity else '',
            'tariff_heading': self.commodity.chaphead if self.commodity else '',
            'mode_of_transport_desc': self.modeoftransport.description if self.modeoftransport else '',
            'customer_category_desc': self.customercategory.description if self.customercategory else '',
            'freight_calc': {'x': '', 'y': '', 'm': '', 'n': '', 'F': 0.0, 'V': 0.0, 'z': 0},
        }

        # Handle Financial Year for Invoice Number Suffix
        if self.finyearid:
            fin_year_from = self.finyearid.finyearfrom
            fin_year_to = self.finyearid.finyearto
            if fin_year_from and fin_year_to:
                fy_suffix = f"{str(fin_year_from.year)[2:]}{str(fin_year_to.year)[2:]}"
                context['invoice_full_number'] = f"{self.invoiceno}/{fy_suffix}"

        # Handle WO Numbers (comma-separated IDs to names)
        if self.wono:
            wo_ids = [int(x) for x in self.wono.split(',') if x]
            wo_names = WorkOrderMaster.objects.filter(id__in=wo_ids, compid=self.compid).values_list('wono', flat=True)
            context['work_order_numbers'] = ",".join(filter(None, wo_names))

        # Calculate Freight, VAT & CST (Replicating C# logic)
        freight_type_desc = "Amt(Rs)" if self.freighttype == 0 else "Per(%)"
        F = self.freight if self.freight is not None else 0.0
        z = 0 if self.freighttype == 0 else 1

        if self.invoicemode == "2": # Assuming '2' means VAT mode
            x = "Freight"
            y = "VAT"
            m = f"{F} {freight_type_desc}"
            n = self.vat.terms if self.vat else ''
            V = self.vat.value if self.vat and self.vat.value is not None else 0.0
        elif self.invoicemode == "3": # Assuming '3' means CST mode
            x = "CST"
            y = "Freight"
            p = "With C Form" if self.selectedcst == 0 else "Without C Form"
            n = f"{F} {freight_type_desc}"
            m = f"{self.cst.terms if self.cst else ''} {p}"
            V = self.cst.value if self.cst and self.cst.value is not None else 0.0
        else:
            # Default or other invoice modes
            x, y, m, n, V = '', '', '', '', 0.0

        context['freight_calc'] = {'x': x, 'y': y, 'm': m, 'n': n, 'F': F, 'V': V, 'z': z}

        return context

```

#### 4.2 Forms (`sales_invoices/forms.py`)

**Analysis:** The original ASP.NET page `SalesInvoice_Print_Details.aspx` is purely for displaying a report and does not involve any user input forms for creating or updating sales invoice data. Therefore, no Django forms are required for the functionality of *this specific page*. If other parts of your system handle sales invoice creation or editing, those would require separate forms.

```python
# No forms are required for the SalesInvoice_Print_Details functionality,
# as it is a read-only report viewing page.
# If CRUD operations were to be added for SalesInvoiceMaster,
# a form would be defined here, e.g.:
# from django import forms
# from .models import SalesInvoiceMaster
#
# class SalesInvoiceMasterForm(forms.ModelForm):
#     class Meta:
#         model = SalesInvoiceMaster
#         fields = '__all__' # Or specific fields for entry
#         widgets = {
#             'invoiceno': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
#             # ... other fields
#         }
```

#### 4.3 Views (`sales_invoices/views.py`)

This section defines the Django view responsible for rendering the sales invoice report. As per the "Thin View" principle, the view will primarily fetch the model instance and delegate all complex data processing and formatting to the `SalesInvoiceMaster` model's `get_report_context` method.

```python
from django.views.generic import TemplateView
from django.urls import reverse_lazy
from django.shortcuts import get_object_or_404
from django.http import HttpResponseRedirect
from .models import SalesInvoiceMaster

class SalesInvoicePrintView(TemplateView):
    template_name = 'sales_invoices/salesinvoice_print_details.html'

    def get_context_data(self, **kwargs):
        # Retrieve parameters from URL (InvId, InvNo, PrintType)
        invoice_id = self.kwargs.get('pk')
        invoice_no = self.kwargs.get('invoice_no')
        print_type = self.request.GET.get('PT', '') # Corresponds to Request.QueryString["PT"]

        # Fetch the SalesInvoiceMaster object. Use get_object_or_404 for robustness.
        # Ensure 'id' and 'invoiceno' match the parameters.
        invoice = get_object_or_404(
            SalesInvoiceMaster,
            id=invoice_id,
            invoiceno=invoice_no
        )

        # Delegate complex report data generation to the model (Fat Model)
        context = invoice.get_report_context(print_type=print_type)
        return context

    # The original ASP.NET page had a "Cancel" button that redirected.
    # We'll handle this in the template using JS/HTMX for client-side navigation.
    # No explicit POST handling needed in the view for this specific cancel action.

```

#### 4.4 Templates (`sales_invoices/templates/sales_invoices/salesinvoice_print_details.html`)

This template will render the comprehensive sales invoice details, structured for printing. It extends the base template and uses Tailwind CSS for styling. Since it's a report, DataTables is not directly used for the main layout but could be if invoice line items were part of the report (which are not in the provided ASP.NET code).

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8 max-w-4xl bg-white shadow-lg rounded-lg print:shadow-none print:p-0">
    <div class="p-6 print:p-0">
        <div class="flex justify-between items-center mb-6 print:hidden">
            <h2 class="text-2xl font-bold text-gray-800">Sales Invoice - Print</h2>
            <button
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                hx-on:click="window.history.back()"
            >
                Cancel
            </button>
        </div>

        <div class="report-header mb-8 text-sm leading-relaxed">
            <div class="text-center mb-4">
                <h1 class="text-2xl font-bold text-gray-900">{{ invoice.compid.compname|default:'[Company Name]' }}</h1>
                <p class="text-gray-600 whitespace-pre-line">{{ company_address }}</p>
            </div>
            
            <div class="grid grid-cols-2 gap-4 mb-6 border-b border-gray-300 pb-4">
                <div>
                    <h3 class="font-semibold text-gray-800">Invoice Details:</h3>
                    <p><strong>Invoice No:</strong> {{ invoice_full_number }}</p>
                    <p><strong>PO No:</strong> {{ invoice.pono }}</p>
                    <p><strong>WO No:</strong> {{ work_order_numbers }}</p>
                    <p><strong>Invoice Mode:</strong> {{ invoice.invoicemode }}</p>
                    <p><strong>Date of Issue:</strong> {{ formatted_issue_date }}</p>
                    <p><strong>Time of Issue:</strong> {{ invoice.timeofissueinvoice }}</p>
                </div>
                <div>
                    <h3 class="font-semibold text-gray-800">Removal Details:</h3>
                    <p><strong>Date of Removal:</strong> {{ formatted_removal_date }} ({{ words_removal_date }})</p>
                    <p><strong>Time of Removal:</strong> {{ invoice.timeofremoval }} ({{ words_removal_time }})</p>
                    <p><strong>Nature of Removal:</strong> {{ nature_of_removal_desc }}</p>
                    <p><strong>Mode of Transport:</strong> {{ mode_of_transport_desc }}</p>
                    <p><strong>RR/GC No:</strong> {{ invoice.rrgcno }}</p>
                    <p><strong>Vehicle Reg No:</strong> {{ invoice.vehiregno }}</p>
                    <p><strong>Duty Rate:</strong> {{ invoice.dutyrate }}</p>
                </div>
            </div>

            <div class="grid grid-cols-2 gap-4 mb-6 border-b border-gray-300 pb-4">
                <div>
                    <h3 class="font-semibold text-gray-800">Buyer Details:</h3>
                    <p><strong>Name:</strong> {{ invoice.buyer_name }}</p>
                    <p><strong>Contact Person:</strong> {{ invoice.buyer_cotper }}</p>
                    <p><strong>Phone:</strong> {{ invoice.buyer_ph }}</p>
                    <p><strong>Mobile:</strong> {{ invoice.buyer_mob }}</p>
                    <p><strong>Email:</strong> {{ invoice.buyer_email }}</p>
                    <p><strong>ECC:</strong> {{ invoice.buyer_ecc }}</p>
                    <p><strong>TIN:</strong> {{ invoice.buyer_tin }}</p>
                    <p><strong>VAT:</strong> {{ invoice.buyer_vat }}</p>
                    <p class="whitespace-pre-line"><strong>Address:</strong><br>{{ buyer_address }}</p>
                </div>
                <div>
                    <h3 class="font-semibold text-gray-800">Consignee Details:</h3>
                    <p><strong>Name:</strong> {{ invoice.cong_name }}</p>
                    <p><strong>Contact Person:</strong> {{ invoice.cong_cotper }}</p>
                    <p><strong>Phone:</strong> {{ invoice.cong_ph }}</p>
                    <p><strong>Mobile:</strong> {{ invoice.cong_mob }}</p>
                    <p><strong>Email:</strong> {{ invoice.cong_email }}</p>
                    <p><strong>ECC:</strong> {{ invoice.cong_ecc }}</p>
                    <p><strong>TIN:</strong> {{ invoice.cong_tin }}</p>
                    <p><strong>VAT:</strong> {{ invoice.cong_vat }}</p>
                    <p class="whitespace-pre-line"><strong>Address:</strong><br>{{ consignee_address }}</p>
                </div>
            </div>

            <div class="grid grid-cols-2 gap-4 mb-6 border-b border-gray-300 pb-4">
                <div>
                    <h3 class="font-semibold text-gray-800">Commodity Details:</h3>
                    <p><strong>Commodity:</strong> {{ commodity_terms }}</p>
                    <p><strong>Tariff Heading:</strong> {{ tariff_heading }}</p>
                </div>
                <div>
                    <h3 class="font-semibold text-gray-800">Financial Details:</h3>
                    <p><strong>Add Amount:</strong> {{ invoice.addamt|default:0.0|floatformat:2 }}</p>
                    <p><strong>Deduction:</strong> {{ invoice.deduction|default:0.0|floatformat:2 }}</p>
                    <p><strong>PF:</strong> {{ invoice.pf|default:0.0|floatformat:2 }}</p>
                    <p><strong>CENVAT:</strong> {{ invoice.cenvat|default:'N/A' }}</p>
                    <p><strong>SED:</strong> {{ invoice.sed|default:0.0|floatformat:2 }}</p>
                    <p><strong>AED:</strong> {{ invoice.aed|default:0.0|floatformat:2 }}</p>
                    <p><strong>Other Amount:</strong> {{ invoice.otheramt|default:0.0|floatformat:2 }}</p>
                </div>
            </div>

            <div class="flex justify-between items-start text-sm">
                <div class="w-1/2 pr-4">
                    <p><strong>{{ freight_calc.x }}:</strong> {{ freight_calc.m }}</p>
                    <p><strong>{{ freight_calc.y }}:</strong> {{ freight_calc.n }}</p>
                </div>
                <div class="w-1/2 pl-4 text-right">
                    <button 
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded print:hidden"
                        hx-on:click="window.print()"
                    >
                        Print Invoice
                    </button>
                </div>
            </div>
        </div>

        <div class="text-center mt-8 text-gray-500 text-xs print:hidden">
            <p>This is a system-generated invoice and may not require a signature.</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('invoicePrint', () => ({
            // No specific Alpine.js state management needed for this simple print view
            // but can be added for future enhancements like toggling sections.
        }));
    });
</script>
{% endblock %}
```

#### 4.5 URLs (`sales_invoices/urls.py`)

Define the URL pattern to access the `SalesInvoicePrintView`. We'll use the invoice ID (`pk`) and invoice number (`invoice_no`) in the URL path, aligning with the ASP.NET `Request.QueryString`.

```python
from django.urls import path
from .views import SalesInvoicePrintView

urlpatterns = [
    # URL for displaying the sales invoice report
    # The invoice_no is included for completeness as in original query string, but pk (id) is primary for lookup
    path('salesinvoice/print/<int:pk>/<str:invoice_no>/', SalesInvoicePrintView.as_view(), name='salesinvoice_print_details'),
]
```

#### 4.6 Tests (`sales_invoices/tests.py`)

Comprehensive unit tests for the models and integration tests for the view ensure the robustness and correctness of the migrated functionality.

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import datetime, date, time
from .models import (
    SalesInvoiceMaster, CompanyMaster, FinancialMaster, Country, State, City,
    RemovableNature, ExciseCommodity, TransportMode, ServiceCategory,
    WorkOrderMaster, POMaster, VATMaster
)

class SalesInvoiceModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary related objects first
        cls.company = CompanyMaster.objects.create(
            compid=1, regdaddress='123 Company St', regdcity_id=1, regdstate_id=1, regdcountry_id=1,
            regdpincode='123456', regdcontactno='9876543210', regdfaxno='12345678', regdemail='<EMAIL>'
        )
        cls.financial_year = FinancialMaster.objects.create(
            finyearid=1, compid=cls.company, finyearfrom=datetime(2023, 4, 1), finyearto=datetime(2024, 3, 31)
        )
        cls.country_us = Country.objects.create(cid=1, countryname='USA')
        cls.state_ca = State.objects.create(sid=1, statename='California')
        cls.city_la = City.objects.create(cityid=1, cityname='Los Angeles')

        cls.nature_removal = RemovableNature.objects.create(id=1, description='Sales')
        cls.commodity = ExciseCommodity.objects.create(id=1, terms='Textiles', chaphead='62.01')
        cls.transport_mode = TransportMode.objects.create(id=1, description='Road')
        cls.service_category = ServiceCategory.objects.create(id=1, description='Retail')

        cls.work_order1 = WorkOrderMaster.objects.create(id=101, wono='WO-001', compid=cls.company)
        cls.work_order2 = WorkOrderMaster.objects.create(id=102, wono='WO-002', compid=cls.company)

        cls.po_master = POMaster.objects.create(poid=1, podate=datetime(2023, 5, 10), compid=cls.company)

        cls.vat_10_percent = VATMaster.objects.create(id=1, terms='VAT @ 10%', value=10.0)
        cls.cst_5_percent = VATMaster.objects.create(id=2, terms='CST @ 5%', value=5.0)

        # Create a test SalesInvoiceMaster instance
        cls.invoice_data = {
            'id': 1,
            'sysdate': datetime(2023, 6, 1, 10, 0, 0),
            'compid': cls.company,
            'finyearid': cls.financial_year,
            'invoiceno': 'INV-001',
            'pono': 'PO-XYZ',
            'wono': '101,102', # Comma separated IDs
            'invoicemode': '2', # VAT mode
            'dateofissueinvoice': datetime(2023, 6, 1),
            'dateofremoval': datetime(2023, 6, 1),
            'timeofissueinvoice': '10:00',
            'timeofremoval': '11:30',
            'natureofremoval': cls.nature_removal,
            'commodity': cls.commodity,
            'modeoftransport': cls.transport_mode,
            'rrgcno': 'RRGC123',
            'vehiregno': 'VEH456',
            'dutyrate': '10%',
            'customercode': 'CUST001',
            'customercategory': cls.service_category,
            'buyer_name': 'Buyer One',
            'buyer_add': '456 Buyer Lane',
            'buyer_country': cls.country_us,
            'buyer_state': cls.state_ca,
            'buyer_city': cls.city_la,
            'cong_name': 'Consignee A',
            'cong_add': '789 Consignee Rd',
            'cong_country': cls.country_us,
            'cong_state': cls.state_ca,
            'cong_city': cls.city_la,
            'addamt': 50.0,
            'deduction': 10.0,
            'freighttype': 0, # Amount
            'freight': 25.0,
            'vat': cls.vat_10_percent,
            'selectedcst': 0, # With C Form
            'cst': cls.cst_5_percent, # This maps to VATMaster for CST details
            'poid': cls.po_master,
            'otheramt': 5.0,
        }
        cls.invoice = SalesInvoiceMaster.objects.create(**cls.invoice_data)

    def test_invoice_creation(self):
        invoice = SalesInvoiceMaster.objects.get(id=1)
        self.assertEqual(invoice.invoiceno, 'INV-001')
        self.assertEqual(invoice.compid, self.company)
        self.assertEqual(invoice.natureofremoval, self.nature_removal)

    def test_get_full_buyer_address(self):
        invoice = SalesInvoiceMaster.objects.get(id=1)
        expected_address = "456 Buyer Lane,\nLos Angeles, California,\nUSA."
        self.assertEqual(invoice.get_full_buyer_address(), expected_address)

    def test_get_full_consignee_address(self):
        invoice = SalesInvoiceMaster.objects.get(id=1)
        expected_address = "789 Consignee Rd,\nLos Angeles, California,\nUSA."
        self.assertEqual(invoice.get_full_consignee_address(), expected_address)

    def test_get_report_context_vat_mode(self):
        context = self.invoice.get_report_context(print_type='Original')
        self.assertEqual(context['invoice_full_number'], 'INV-001/2324')
        self.assertEqual(context['work_order_numbers'], 'WO-001,WO-002')
        self.assertEqual(context['freight_calc']['x'], 'Freight')
        self.assertEqual(context['freight_calc']['y'], 'VAT')
        self.assertEqual(context['freight_calc']['m'], '25.0 Amt(Rs)')
        self.assertEqual(context['freight_calc']['n'], 'VAT @ 10%')
        self.assertEqual(context['freight_calc']['V'], 10.0)
        self.assertEqual(context['print_type'], 'Original')

    def test_get_report_context_cst_mode(self):
        self.invoice.invoicemode = '3' # Change to CST mode
        self.invoice.freighttype = 1 # Percentage
        self.invoice.freight = 5.0 # 5%
        self.invoice.save()
        context = self.invoice.get_report_context()
        self.assertEqual(context['freight_calc']['x'], 'CST')
        self.assertEqual(context['freight_calc']['y'], 'Freight')
        self.assertEqual(context['freight_calc']['m'], 'CST @ 5% With C Form')
        self.assertEqual(context['freight_calc']['n'], '5.0 Per(%)')
        self.assertEqual(context['freight_calc']['V'], 5.0)
        self.assertEqual(context['freight_calc']['F'], 5.0)

    def test_date_to_text(self):
        test_date = date(2023, 10, 26)
        result = SalesInvoiceMaster._date_to_text(test_date)
        self.assertEqual(result, "twenty-sixth October two thousand and twenty-three")

    def test_time_to_text(self):
        self.assertEqual(SalesInvoiceMaster._time_to_text('14:30'), '02 30 PM') # num2words handles this
        self.assertEqual(SalesInvoiceMaster._time_to_text('09:00'), '09 o\'clock AM')

    def test_formatted_date_dmy(self):
        test_datetime = datetime(2023, 12, 25, 12, 30, 0)
        self.assertEqual(SalesInvoiceMaster._format_date_dmy(test_datetime), '25.12.2023')
        self.assertEqual(SalesInvoiceMaster._format_date_dmy(None), '')

class SalesInvoicePrintViewTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Ensure test data is set up as in SalesInvoiceModelsTest
        SalesInvoiceModelsTest.setUpTestData()
        self.invoice = SalesInvoiceMaster.objects.get(id=1)
        self.url = reverse(
            'salesinvoice_print_details',
            kwargs={'pk': self.invoice.id, 'invoice_no': self.invoice.invoiceno}
        )

    def test_sales_invoice_print_view_get(self):
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_invoices/salesinvoice_print_details.html')
        self.assertContains(response, 'INV-001/2324')
        self.assertContains(response, 'Buyer One')
        self.assertContains(response, 'Consignee A')
        self.assertContains(response, 'Sales Invoice - Print')
        self.assertContains(response, 'WO-001,WO-002')
        self.assertContains(response, '25.0 Amt(Rs)')
        self.assertContains(response, 'VAT @ 10%')

    def test_sales_invoice_print_view_with_print_type(self):
        url_with_param = f"{self.url}?PT=Duplicate"
        response = self.client.get(url_with_param)
        self.assertEqual(response.status_code, 200)
        # Check if print_type is in context (e.g., could be displayed on template)
        self.assertIn('print_type', response.context)
        self.assertEqual(response.context['print_type'], 'Duplicate')

    def test_sales_invoice_print_view_not_found(self):
        # Test with a non-existent invoice ID
        non_existent_url = reverse(
            'salesinvoice_print_details',
            kwargs={'pk': 999, 'invoice_no': 'NON-EXISTENT'}
        )
        response = self.client.get(non_existent_url)
        self.assertEqual(response.status_code, 404) # Should return 404 if not found
```

### Step 5: HTMX and Alpine.js Integration

For this specific report view, HTMX and Alpine.js are integrated for minimal client-side interaction rather than complex CRUD modals, as the page is primarily for viewing and printing.

*   **HTMX for Navigation:** The "Cancel" button uses `hx-on:click="window.history.back()"` for smooth client-side navigation without a full page reload or needing a Django redirect view. The "Print Invoice" button directly triggers the browser's print dialog.
*   **Alpine.js:** A basic Alpine.js component is initialized. While not extensively used for this static report, it provides a foundation for any future client-side dynamic behaviors, such as toggling visibility of sections or handling specific UI states related to printing.
*   **DataTables:** As noted in the guidelines, DataTables is typically for list views. Since `SalesInvoice_Print_Details.aspx` is a single-record report view, DataTables is not applicable to its primary function. If the report contained an embedded table of line items, DataTables *could* be applied to that specific inner table for client-side sorting/searching.

---

### Final Notes

*   **Database Synchronization:** This plan assumes direct mapping to existing database tables (`managed = False`). Ensure your Django `settings.py` is configured to connect to your legacy SQL Server database.
*   **Error Handling:** The original C# code uses a generic `try-catch`. In Django, robust error handling involves `get_object_or_404` for missing records and proper logging.
*   **Security:** Ensure proper authentication and authorization middleware are in place in your Django project to restrict access to sensitive invoice data.
*   **Print Styles:** The template uses `print:` utility classes from Tailwind CSS to optimize the layout for printing, removing elements like navigation and adjusting margins.
*   **Scalability:** For high-volume reporting or complex PDF generation, consider dedicated libraries like `WeasyPrint` (for HTML to PDF conversion) or `ReportLab` for pixel-perfect PDF rendering, integrated as background tasks or specific view methods. For this migration, rendering to HTML is the direct equivalent of Crystal Reports *displaying* the data.