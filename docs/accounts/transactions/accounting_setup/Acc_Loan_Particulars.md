This document outlines a strategic plan to modernize your legacy ASP.NET application, specifically the `Acc_Loan_Particulars` module, by transitioning it to a robust and scalable Django 5.0+ framework. Our approach prioritizes AI-assisted automation, ensuring a streamlined and efficient migration process with clear, non-technical instructions for your business stakeholders.

## ASP.NET to Django Conversion Script:

This plan breaks down the modernization into distinct, actionable steps, focusing on generating comprehensive and runnable Django code components.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management (or `_hyperscript` as demonstrated in templates).
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns relevant to the `Acc_Loan_Particulars` functionality from the ASP.NET code.

**Instructions:**
From the C# code, the SQL query `select Sum(CreditAmt) As loan,tblAcc_LoanMaster.Particulars,tblAcc_LoanMaster.Id from tblAcc_LoanDetails inner join tblAcc_LoanMaster on tblAcc_LoanMaster.Id=tblAcc_LoanDetails.MId And CompId=" + CompId + " AND FinYearId<=" + FinYearId + " group by tblAcc_LoanMaster.Particulars,tblAcc_LoanMaster.Id` reveals two primary tables: `tblAcc_LoanMaster` and `tblAcc_LoanDetails`.

*   **`tblAcc_LoanMaster`**: This table seems to hold the main "particulars" information for each loan.
    *   Columns: `Id` (integer, likely primary key), `Particulars` (string).
*   **`tblAcc_LoanDetails`**: This table appears to store transactional details (debit/credit amounts) linked to the `tblAcc_LoanMaster`.
    *   Columns: `Id` (integer, likely primary key), `MId` (integer, foreign key referencing `tblAcc_LoanMaster.Id`), `CreditAmt` (decimal), `DebitAmt` (decimal), `CompId` (integer, Company ID), `FinYearId` (integer, Financial Year ID).

The ASP.NET GridView displays `Particulars`, `TotDrAmt` (Total Debit Amount), `TotCrAmt` (Total Credit Amount), and `Id`. The `TotDrAmt` and `TotCrAmt` are aggregated sums derived from `tblAcc_LoanDetails` linked via `tblAcc_LoanMaster`.

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations performed by the ASP.NET code.

**Instructions:**
The `Acc_Loan_Particulars.aspx` page primarily focuses on **reading** and displaying a summary of loan particulars.

*   **Read (Display List):** The page fetches aggregated loan data (particulars with total debit and credit amounts) and displays it in a grid. This is a read-only report view.
*   **Actions:**
    *   **Drill-down to Details:** Clicking on a "Particulars" link (`LinkButton1`) redirects the user to `Acc_Loan_Part_Details.aspx?MId=[id]`, indicating a separate page for viewing or managing the details of a specific loan particular. This is a navigation action, not a direct CRUD operation on the summary.
    *   **Cancel/Return:** The "Cancel" button (`Button1`) redirects the user to `BalanceSheet.aspx`. This is also a navigation action.

There are no explicit "Create," "Update," or "Delete" operations performed directly on this `Acc_Loan_Particulars` summary page. However, to align with the provided Django template structure and demonstrate full CRUD capabilities, we will generate placeholder CRUD views for `LoanMaster` (the individual loan particulars) as separate functionalities, assuming they would be handled on the `Acc_Loan_Part_Details.aspx` equivalent page in the Django application.

### Step 3: Infer UI Components

**Task:** Analyze the ASP.NET controls and their roles in the user interface.

**Instructions:**
*   **`asp:GridView ID="GridView1"`**: This is the central control, responsible for displaying the list of loan particulars along with their aggregated debit and credit amounts. This will be replaced by a Django template using a `<table>` element, enhanced with the DataTables JavaScript library for client-side functionality.
*   **`asp:LinkButton ID="LinkButton1"`**: Used within the GridView to make the "Particulars" column clickable, leading to a detail page. In Django, this will be a standard `<a>` tag or an HTMX-triggered button, linking to a dedicated loan particular detail view.
*   **`asp:Label ID="lblDrAmt"`, `ID="lblCrAmt"`, `ID="TotDebit"`, `ID="TotCredit"`**: These labels are used to display the numerical debit and credit amounts, including the calculated totals in the footer. In Django templates, these will be rendered directly as text within table cells.
*   **`asp:Button ID="Button1"`**: The "Cancel" button. In Django, this will be a standard HTML `<button>` or `<a>` tag, directing to the Balance Sheet page.
*   **`asp:Panel ID="Panel1"`**: A container for the GridView, providing scrollbars. In Django, this will be handled by standard HTML `<div>` elements and CSS for styling and scrolling.
*   **CSS Links**: The ASP.NET page links to various CSS files (`yui-datatable.css`, `styles.css`, `StyleSheet.css`). In Django, styling will leverage Tailwind CSS, and DataTables will integrate its own modern styling.

### Step 4: Generate Django Code

We will create a Django application named `accounts` to house the modernized `Acc_Loan_Particulars` module.

#### 4.1 Models (`accounts/models.py`)

**Task:** Create Django models that map to the underlying database tables and encapsulate the business logic for summarizing loan data.

**Instructions:**
We define two models: `LoanMaster` for the main loan particulars and `LoanDetail` for the individual debit/credit transactions. We also create a `LoanSummaryManager` to perform the aggregation logic, adhering to the "Fat Model" principle by keeping this complex query logic within the model layer.

```python
# accounts/models.py
from django.db import models
from django.db.models import Sum, F, Q

class LoanMaster(models.Model):
    """
    Represents the tblAcc_LoanMaster table, holding the main loan particulars.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    particulars = models.CharField(db_column='Particulars', max_length=255) # Assuming max_length for Particulars

    class Meta:
        managed = False  # Tells Django not to manage table creation/deletion
        db_table = 'tblAcc_LoanMaster' # Maps to the existing database table
        verbose_name = 'Loan Particular'
        verbose_name_plural = 'Loan Particulars'

    def __str__(self):
        return self.particulars

class LoanDetail(models.Model):
    """
    Represents the tblAcc_LoanDetails table, storing individual loan debit/credit entries.
    """
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming 'Id' as PK for LoanDetails
    master = models.ForeignKey(
        LoanMaster,
        on_delete=models.DO_NOTHING, # DO_NOTHING because managed=False; handle data integrity at DB level
        db_column='MId',
        related_name='details'
    )
    credit_amt = models.DecimalField(db_column='CreditAmt', max_digits=18, decimal_places=2, default=0.0)
    debit_amt = models.DecimalField(db_column='DebitAmt', max_digits=18, decimal_places=2, default=0.0)
    company_id = models.IntegerField(db_column='CompId')
    financial_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblAcc_LoanDetails'
        verbose_name = 'Loan Detail'
        verbose_name_plural = 'Loan Details'

    def __str__(self):
        return f"Detail {self.id} for {self.master.particulars} (Cr: {self.credit_amt}, Dr: {self.debit_amt})"

class LoanSummaryManager(models.Manager):
    """
    Custom manager for LoanMaster to provide aggregated loan summary data.
    This encapsulates the logic that generated the 'TotDrAmt' and 'TotCrAmt' in the ASP.NET DataTable.
    """
    def get_queryset(self):
        return super().get_queryset()

    def get_loan_summary(self, company_id, financial_year_id):
        """
        Retrieves a summary of loan particulars with aggregated debit and credit amounts.
        Filters by company and financial year (inclusive of previous years).
        """
        # Annotate LoanMaster objects with sums from related LoanDetail objects,
        # filtered by the given company_id and financial_year_id (<= current year).
        # Use Q objects to ensure the filter applies correctly before aggregation.
        return self.get_queryset().filter(
            Q(details__company_id=company_id) & Q(details__financial_year_id__lte=financial_year_id)
        ).annotate(
            total_debit=Sum('details__debit_amt', default=0), # Default 0 to handle cases with no details
            total_credit=Sum('details__credit_amt', default=0)
        ).order_by('particulars').distinct() # Distinct ensures one row per LoanMaster after aggregation

# Attach the custom manager to the LoanMaster model
LoanMaster.add_to_class('summary_objects', LoanSummaryManager())
```

#### 4.2 Forms (`accounts/forms.py`)

**Task:** Define a Django ModelForm for editing `LoanMaster` particulars. Although the original page is read-only, this form facilitates basic CRUD operations as per the general modernization guidelines.

**Instructions:**
We create a `LoanMasterForm` for creating and updating `LoanMaster` objects. This form will only include the `particulars` field, as `id` is a primary key and the debit/credit amounts are aggregated and not directly editable via this form.

```python
# accounts/forms.py
from django import forms
from .models import LoanMaster

class LoanMasterForm(forms.ModelForm):
    """
    Form for creating and updating LoanMaster particulars.
    """
    class Meta:
        model = LoanMaster
        fields = ['particulars'] # Only 'particulars' is editable for this form
        widgets = {
            'particulars': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter Loan Particulars'
            }),
        }

    def clean_particulars(self):
        particulars = self.cleaned_data['particulars']
        # Add any specific validation logic here, e.g., unique particulars
        return particulars

```

#### 4.3 Views (`accounts/views.py`)

**Task:** Implement Django Class-Based Views (CBVs) for displaying the loan summary list and for basic CRUD operations on `LoanMaster` particulars, keeping views thin and logic in models.

**Instructions:**
We define views for the main summary list, a partial view for the DataTables content (loaded via HTMX), and standard CRUD views for `LoanMaster` for completeness as per the prompt. Mock session data for `CompId` and `FinYearId` is included for demonstration; in a real application, these would be retrieved from user sessions or request parameters.

```python
# accounts/views.py
from django.views.generic import ListView, TemplateView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from .models import LoanMaster # Only LoanMaster is needed for views, as its manager handles summary logic
from .forms import LoanMasterForm
from django.shortcuts import redirect # For the redirect views

class LoanSummaryListView(ListView):
    """
    Main view for displaying the Loan Particulars summary page.
    This page loads the basic structure; the table content is loaded via HTMX.
    """
    model = LoanMaster # Although it's a summary, the base model is LoanMaster
    template_name = 'accounts/loansummary/list.html'
    context_object_name = 'loan_particulars' # Not directly used for initial load, but consistent

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Mock session data as in original ASP.NET application.
        # In a real application, retrieve these from self.request.session or current user profile.
        context['company_id'] = self.request.session.get('compid', 1)
        context['financial_year_id'] = self.request.session.get('finyear', 2023)
        return context

class LoanSummaryTablePartialView(TemplateView):
    """
    HTMX-loaded partial view that renders the DataTables table content
    for the loan particulars summary.
    """
    template_name = 'accounts/loansummary/_loansummary_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        company_id = self.request.session.get('compid', 1)
        financial_year_id = self.request.session.get('finyear', 2023)

        # Retrieve aggregated loan summaries using the custom manager method
        loan_summaries = LoanMaster.summary_objects.get_loan_summary(
            company_id=company_id,
            financial_year_id=financial_year_id
        )

        # Calculate total debit and credit for the footer, as done in ASP.NET
        total_debit_sum = sum(summary.total_debit for summary in loan_summaries)
        total_credit_sum = sum(summary.total_credit for summary in loan_summaries)

        context['loan_summaries'] = loan_summaries
        context['total_debit_sum'] = total_debit_sum
        context['total_credit_sum'] = total_credit_sum
        return context

# --- CRUD Views for LoanMaster (for individual loan particulars) ---
# These views are included to demonstrate the complete CRUD pattern
# as requested by the template, even though the original ASP.NET page is summary-only.

class LoanMasterCreateView(CreateView):
    """
    View to create a new Loan Master (loan particular).
    """
    model = LoanMaster
    form_class = LoanMasterForm
    template_name = 'accounts/loansummary/form.html' # Reusing a generic form template
    success_url = reverse_lazy('loansummary_list') # Redirect back to the summary list

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, f'Loan particular "{self.object.particulars}" added successfully.')
        # HTMX-specific response: return 204 No Content with a trigger header
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshLoanSummaryList' # Trigger HTMX event to refresh table
                }
            )
        return response

class LoanMasterUpdateView(UpdateView):
    """
    View to update an existing Loan Master (loan particular).
    """
    model = LoanMaster
    form_class = LoanMasterForm
    template_name = 'accounts/loansummary/form.html'
    success_url = reverse_lazy('loansummary_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, f'Loan particular "{self.object.particulars}" updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshLoanSummaryList'
                }
            )
        return response

class LoanMasterDeleteView(DeleteView):
    """
    View to delete a Loan Master (loan particular).
    """
    model = LoanMaster
    template_name = 'accounts/loansummary/confirm_delete.html'
    success_url = reverse_lazy('loansummary_list')

    def delete(self, request, *args, **kwargs):
        # Implement logic for handling associated LoanDetail entries if needed
        # (e.g., delete, archive, or reassign them). For now, assume DB handles CASCADE
        # or it's acceptable to have orphaned details if DO_NOTHING was specified.
        messages.success(self.request, f'Loan particular "{self.get_object().particulars}" deleted successfully.')
        response = super().delete(request, *args, **kwargs)
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshLoanSummaryList'
                }
            )
        return response

# --- Redirect Views mimicking original ASP.NET navigation ---

def balance_sheet_redirect_view(request):
    """
    Redirects to the Balance Sheet page, mimicking the 'Cancel' button's behavior.
    """
    # Replace 'balance_sheet_view' with the actual URL name of your Django Balance Sheet view
    return redirect(reverse_lazy('balance_sheet_view')) # Placeholder for actual Balance Sheet URL

def loan_particular_details_redirect_view(request, pk):
    """
    Redirects to the Loan Particular Details page for a specific loan,
    mimicking the 'Particulars' link's behavior.
    """
    # Replace 'loan_particular_details_view' with the actual URL name of your Django Loan Details view
    # Pass the primary key (pk) as a URL argument to the details view.
    return redirect(reverse_lazy('loan_particular_details_view', kwargs={'pk': pk})) # Placeholder for actual Details URL

```

#### 4.4 Templates (`accounts/templates/accounts/loansummary/`)

**Task:** Create Django templates for the list view, the DataTables partial, and the CRUD form/delete confirmation, integrating HTMX, Alpine.js (via `_hyperscript`), and Tailwind CSS.

**Instructions:**
These templates are designed to be modular and reusable. `list.html` loads the main page, while `_loansummary_table.html`, `form.html`, and `confirm_delete.html` are partials loaded dynamically by HTMX.

**`accounts/templates/accounts/loansummary/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Loan (Liability) Summary</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'loanmaster_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Particular
        </button>
    </div>

    <!-- Message display area for Django messages -->
    {% if messages %}
    <div id="messages" class="mb-4">
        {% for message in messages %}
        <div class="p-3 mb-3 text-sm text-green-700 bg-green-100 rounded-lg" role="alert">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <div id="loansummaryTable-container"
         hx-trigger="load, refreshLoanSummaryList from:body"
         hx-get="{% url 'loansummary_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg overflow-hidden">
        <!-- Initial loading state for HTMX -->
        <div class="flex flex-col items-center justify-center p-8 text-gray-500">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg">Loading loan summary data...</p>
        </div>
    </div>

    <div class="mt-8 text-center">
        <a href="{% url 'balance_sheet_view' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
            Cancel (Go to Balance Sheet)
        </a>
    </div>

    <!-- Modal for forms (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center hidden is-active z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 my-8 relative transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
<script>
    // Alpine.js or any global JS setup can go here if needed.
    // The modal logic is handled by _hyperscript directly in the HTML for simplicity as per example.
</script>
{% endblock %}
```

**`accounts/templates/accounts/loansummary/_loansummary_table.html`**

```html
<div class="p-4">
    <table id="loanSummaryTable" class="min-w-full divide-y divide-gray-200 shadow-sm rounded-lg overflow-hidden">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Particulars</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Debit</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Credit</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for summary in loan_summaries %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium text-blue-600">
                    <a href="{% url 'loan_particular_details_view' pk=summary.id %}" class="hover:underline">
                        {{ summary.particulars }}
                    </a>
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ summary.total_debit|floatformat:2 }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ summary.total_credit|floatformat:2 }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded text-xs mr-2 transition duration-150 ease-in-out"
                        hx-get="{% url 'loanmaster_edit' pk=summary.id %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded text-xs transition duration-150 ease-in-out"
                        hx-get="{% url 'loanmaster_delete' pk=summary.id %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5" class="py-4 px-4 text-center text-sm text-gray-500">No Records To Display</td>
            </tr>
            {% endfor %}
        </tbody>
        {% if loan_summaries %}
        <tfoot class="bg-gray-50">
            <tr>
                <td colspan="2" class="py-3 px-4 text-right text-sm font-bold text-gray-700 uppercase tracking-wider">Total</td>
                <td class="py-3 px-4 text-right text-sm font-bold text-gray-900">{{ total_debit_sum|floatformat:2 }}</td>
                <td class="py-3 px-4 text-right text-sm font-bold text-gray-900">{{ total_credit_sum|floatformat:2 }}</td>
                <td class="py-3 px-4"></td> {# Empty for actions column #}
            </tr>
        </tfoot>
        {% endif %}
    </table>
</div>

<script>
    $(document).ready(function() {
        // Initialize DataTables on the rendered table
        $('#loanSummaryTable').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            // Disable ordering/searching on SN and Actions columns
            "columnDefs": [
                { "orderable": false, "searchable": false, "targets": [0, 4] }
            ]
        });
    });
</script>
```

**`accounts/templates/accounts/loansummary/form.html`**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-4">
        {{ form.instance.pk|yesno:'Edit,Add' }} Loan Particular
    </h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-6">
        {% csrf_token %}

        {% for field in form %}
        <div class="mb-4">
            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ field.label }}
            </label>
            {{ field }}
            {% if field.help_text %}
            <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
            {% endif %}
            {% if field.errors %}
            <p class="text-red-600 text-sm mt-1">{{ field.errors }}</p>
            {% endif %}
        </div>
        {% endfor %}

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                Save Particular
            </button>
        </div>
    </form>
</div>
```

**`accounts/templates/accounts/loansummary/confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-4">
        Confirm Delete
    </h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the loan particular:
        <span class="font-bold text-red-600">"{{ object.particulars }}"</span>?
        This action cannot be undone.
    </p>

    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                Delete Particular
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`accounts/urls.py`)

**Task:** Define URL patterns for all views, including the HTMX partials and navigation redirects.

**Instructions:**
URLs are defined within the `accounts` Django app to maintain modularity.

```python
# accounts/urls.py
from django.urls import path
from .views import (
    LoanSummaryListView,
    LoanSummaryTablePartialView,
    LoanMasterCreateView,
    LoanMasterUpdateView,
    LoanMasterDeleteView,
    balance_sheet_redirect_view,
    loan_particular_details_redirect_view
)

urlpatterns = [
    # Main summary list view
    path('loan-summary/', LoanSummaryListView.as_view(), name='loansummary_list'),
    # HTMX endpoint for the DataTables content
    path('loan-summary/table/', LoanSummaryTablePartialView.as_view(), name='loansummary_table'),

    # CRUD operations for LoanMaster (individual particulars)
    path('loan-particulars/add/', LoanMasterCreateView.as_view(), name='loanmaster_add'),
    path('loan-particulars/edit/<int:pk>/', LoanMasterUpdateView.as_view(), name='loanmaster_edit'),
    path('loan-particulars/delete/<int:pk>/', LoanMasterDeleteView.as_view(), name='loanmaster_delete'),

    # Redirects mimicking original ASP.NET button/link behaviors
    path('balance-sheet-redirect/', balance_sheet_redirect_view, name='balance_sheet_view'),
    path('loan-particular-details/<int:pk>/', loan_particular_details_redirect_view, name='loan_particular_details_view'),
]
```
**Note**: You will need to define `balance_sheet_view` and `loan_particular_details_view` in your project's main `urls.py` or another app's `urls.py` for these redirects to function correctly. For example: `path('balance-sheet/', views.BalanceSheetView.as_view(), name='balance_sheet_view')`

#### 4.6 Tests (`accounts/tests.py`)

**Task:** Write comprehensive unit tests for models and integration tests for views to ensure functionality and maintain code quality.

**Instructions:**
Tests cover model creation, data integrity, custom manager aggregation logic, and all view behaviors, including HTMX interactions.

```python
# accounts/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import LoanMaster, LoanDetail
from decimal import Decimal # Use Decimal for financial calculations

class LoanModelTest(TestCase):
    """
    Tests for LoanMaster and LoanDetail models and the LoanSummaryManager.
    """
    @classmethod
    def setUpTestData(cls):
        # Setup common test data for all model tests
        cls.company_id = 1
        cls.financial_year_id = 2023

        cls.lm1 = LoanMaster.objects.create(id=1, particulars='Vehicle Loan')
        cls.lm2 = LoanMaster.objects.create(id=2, particulars='Home Loan')
        cls.lm3 = LoanMaster.objects.create(id=3, particulars='Business Loan')

        # Loan details for lm1, including previous financial year
        LoanDetail.objects.create(id=10, master=cls.lm1, credit_amt=Decimal('500.00'), debit_amt=Decimal('0.00'), company_id=cls.company_id, financial_year_id=cls.financial_year_id)
        LoanDetail.objects.create(id=11, master=cls.lm1, credit_amt=Decimal('0.00'), debit_amt=Decimal('100.00'), company_id=cls.company_id, financial_year_id=cls.financial_year_id)
        LoanDetail.objects.create(id=12, master=cls.lm1, credit_amt=Decimal('200.00'), debit_amt=Decimal('0.00'), company_id=cls.company_id, financial_year_id=cls.financial_year_id - 1)

        # Loan details for lm2
        LoanDetail.objects.create(id=20, master=cls.lm2, credit_amt=Decimal('1000.00'), debit_amt=Decimal('0.00'), company_id=cls.company_id, financial_year_id=cls.financial_year_id)
        LoanDetail.objects.create(id=21, master=cls.lm2, credit_amt=Decimal('0.00'), debit_amt=Decimal('250.00'), company_id=cls.company_id, financial_year_id=cls.financial_year_id)

        # Loan details for lm3 (different company)
        LoanDetail.objects.create(id=30, master=cls.lm3, credit_amt=Decimal('300.00'), debit_amt=Decimal('0.00'), company_id=cls.company_id + 1, financial_year_id=cls.financial_year_id)
        LoanDetail.objects.create(id=31, master=cls.lm3, credit_amt=Decimal('0.00'), debit_amt=Decimal('50.00'), company_id=cls.company_id + 1, financial_year_id=cls.financial_year_id)


    def test_loan_master_creation(self):
        """Verify LoanMaster object creation and string representation."""
        lm = LoanMaster.objects.get(id=1)
        self.assertEqual(lm.particulars, 'Vehicle Loan')
        self.assertEqual(str(lm), 'Vehicle Loan')

    def test_loan_detail_creation(self):
        """Verify LoanDetail object creation and field values."""
        ld = LoanDetail.objects.get(id=10)
        self.assertEqual(ld.credit_amt, Decimal('500.00'))
        self.assertEqual(ld.master.particulars, 'Vehicle Loan')
        self.assertEqual(str(ld), 'Detail 10 for Vehicle Loan (Cr: 500.00, Dr: 0.00)')

    def test_get_loan_summary_method(self):
        """Test the custom manager's aggregation logic for loan summaries."""
        summary_data = LoanMaster.summary_objects.get_loan_summary(
            company_id=self.company_id,
            financial_year_id=self.financial_year_id
        )

        # Expect two summaries (lm1, lm2) as lm3 is for a different company
        self.assertEqual(summary_data.count(), 2)

        lm1_summary = summary_data.get(id=self.lm1.id)
        # Expected totals for lm1: Credit = 500 (current) + 200 (prev) = 700; Debit = 100 (current) = 100
        self.assertEqual(lm1_summary.total_debit, Decimal('100.00'))
        self.assertEqual(lm1_summary.total_credit, Decimal('700.00'))

        lm2_summary = summary_data.get(id=self.lm2.id)
        # Expected totals for lm2: Credit = 1000; Debit = 250
        self.assertEqual(lm2_summary.total_debit, Decimal('250.00'))
        self.assertEqual(lm2_summary.total_credit, Decimal('1000.00'))

        # Test with a different company_id
        summary_data_diff_comp = LoanMaster.summary_objects.get_loan_summary(
            company_id=self.company_id + 1,
            financial_year_id=self.financial_year_id
        )
        self.assertEqual(summary_data_diff_comp.count(), 1)
        lm3_summary = summary_data_diff_comp.get(id=self.lm3.id)
        self.assertEqual(lm3_summary.total_credit, Decimal('300.00'))
        self.assertEqual(lm3_summary.total_debit, Decimal('50.00'))

    def test_get_loan_summary_no_details(self):
        """Test summary for a LoanMaster with no associated details."""
        lm_no_details = LoanMaster.objects.create(id=4, particulars='No Details Loan')
        summary_data = LoanMaster.summary_objects.get_loan_summary(
            company_id=self.company_id,
            financial_year_id=self.financial_year_id
        )
        # Check if the new loan is in the summary (it should be, but with 0 totals)
        # The query filters on `details__company_id` so if there are no details
        # matching the filter, the LoanMaster might not appear.
        # This is an important distinction from the original logic.
        # Let's verify that a LoanMaster with no matching details for the given filter doesn't appear.
        self.assertFalse(summary_data.filter(id=lm_no_details.id).exists())

class LoanViewsTest(TestCase):
    """
    Integration tests for Django views, including HTMX interactions.
    """
    @classmethod
    def setUpTestData(cls):
        # Setup common test data for all view tests
        cls.company_id = 101
        cls.financial_year_id = 2023
        cls.lm_existing = LoanMaster.objects.create(id=100, particulars='Existing Loan Type')
        LoanDetail.objects.create(id=1000, master=cls.lm_existing, credit_amt=Decimal('1234.56'), debit_amt=Decimal('789.00'), company_id=cls.company_id, financial_year_id=cls.financial_year_id)
        # Create a second loan master for total checks
        cls.lm_second = LoanMaster.objects.create(id=101, particulars='Second Loan Type')
        LoanDetail.objects.create(id=1001, master=cls.lm_second, credit_amt=Decimal('500.00'), debit_amt=Decimal('200.00'), company_id=cls.company_id, financial_year_id=cls.financial_year_id)


    def setUp(self):
        self.client = Client()
        # Simulate session data which is used by views
        session = self.client.session
        session['compid'] = self.company_id
        session['finyear'] = self.financial_year_id
        session.save()

    def test_loan_summary_list_view(self):
        """Test the main list page loads correctly."""
        response = self.client.get(reverse('loansummary_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/loansummary/list.html')
        self.assertContains(response, 'Loan (Liability) Summary') # Check for page title
        self.assertContains(response, 'id="loansummaryTable-container"') # Check for HTMX container

    def test_loan_summary_table_partial_view_htmx(self):
        """Test the HTMX-loaded table content and data aggregation."""
        response = self.client.get(reverse('loansummary_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/loansummary/_loansummary_table.html')

        # Check for presence of aggregated data for existing loans
        self.assertContains(response, 'Existing Loan Type')
        self.assertContains(response, '1234.56') # Total Credit for lm_existing
        self.assertContains(response, '789.00')  # Total Debit for lm_existing

        self.assertContains(response, 'Second Loan Type')
        self.assertContains(response, '500.00')  # Total Credit for lm_second
        self.assertContains(response, '200.00')  # Total Debit for lm_second

        # Check footer totals
        expected_total_debit = Decimal('789.00') + Decimal('200.00')
        expected_total_credit = Decimal('1234.56') + Decimal('500.00')
        self.assertContains(response, f'>{expected_total_debit:.2f}<')
        self.assertContains(response, f'>{expected_total_credit:.2f}<')

    def test_loan_master_create_view_get_htmx(self):
        """Test GET request for creating a new loan particular via HTMX modal."""
        response = self.client.get(reverse('loanmaster_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/loansummary/form.html')
        self.assertContains(response, 'Add Loan Particular')
        self.assertContains(response, 'name="particulars"') # Check for form field

    def test_loan_master_create_view_post_htmx(self):
        """Test POST request for creating a new loan particular via HTMX."""
        data = {'particulars': 'New Loan Particular'}
        response = self.client.post(reverse('loanmaster_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX expects 204 No Content on success
        self.assertTrue(LoanMaster.objects.filter(particulars='New Loan Particular').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshLoanSummaryList')

    def test_loan_master_update_view_get_htmx(self):
        """Test GET request for updating an existing loan particular via HTMX modal."""
        response = self.client.get(reverse('loanmaster_edit', args=[self.lm_existing.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/loansummary/form.html')
        self.assertContains(response, 'Edit Loan Particular')
        self.assertContains(response, self.lm_existing.particulars) # Check that existing data is pre-filled

    def test_loan_master_update_view_post_htmx(self):
        """Test POST request for updating an existing loan particular via HTMX."""
        new_particulars = 'Updated Loan Type Name'
        data = {'particulars': new_particulars}
        response = self.client.post(reverse('loanmaster_edit', args=[self.lm_existing.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.lm_existing.refresh_from_db() # Reload object to get updated data
        self.assertEqual(self.lm_existing.particulars, new_particulars)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshLoanSummaryList')

    def test_loan_master_delete_view_get_htmx(self):
        """Test GET request for delete confirmation via HTMX modal."""
        lm_to_delete = LoanMaster.objects.create(id=999, particulars='Temporary Loan for Delete')
        response = self.client.get(reverse('loanmaster_delete', args=[lm_to_delete.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/loansummary/confirm_delete.html')
        self.assertContains(response, 'Confirm Delete')
        self.assertContains(response, lm_to_delete.particulars)

    def test_loan_master_delete_view_post_htmx(self):
        """Test POST request for deleting a loan particular via HTMX."""
        lm_to_delete = LoanMaster.objects.create(id=998, particulars='Another Temp Loan')
        response = self.client.post(reverse('loanmaster_delete', args=[lm_to_delete.id]), {}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(LoanMaster.objects.filter(id=lm_to_delete.id).exists()) # Verify deletion
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshLoanSummaryList')

    def test_balance_sheet_redirect_view(self):
        """Test the redirect for the 'Cancel' button."""
        response = self.client.get(reverse('balance_sheet_view'))
        self.assertEqual(response.status_code, 302) # Expect a redirect
        # This assumes 'balance_sheet_view' correctly resolves to a URL in your project's urls.
        # If it's not defined, this test will fail.
        self.assertRedirects(response, reverse('balance_sheet_view'))

    def test_loan_particular_details_redirect_view(self):
        """Test the redirect for the 'Particulars' link."""
        response = self.client.get(reverse('loan_particular_details_view', args=[self.lm_existing.id]))
        self.assertEqual(response.status_code, 302)
        # This assumes 'loan_particular_details_view' correctly resolves.
        self.assertRedirects(response, reverse('loan_particular_details_view', kwargs={'pk': self.lm_existing.id}))

```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated code fully embraces HTMX for dynamic content updates and modal interactions.

*   **HTMX for Dynamic Content:**
    *   The `loansummary_list.html` uses `hx-get` to fetch the table content from `loansummary_table` on `load` and `refreshLoanSummaryList` events.
    *   CRUD operations (Add, Edit, Delete) are triggered by `hx-get` to load forms into a modal, and `hx-post` for form submissions.
    *   Successful form submissions from `CreateView`, `UpdateView`, and `DeleteView` return `HTTP 204 No Content` with an `HX-Trigger` header (`refreshLoanSummaryList`) to instruct the frontend to refresh the main table, ensuring the list always reflects the latest data without a full page reload.
*   **Alpine.js / `_hyperscript` for UI State:**
    *   The modal (`#modal`) visibility is managed using `_hyperscript`'s `on click add .is-active` and `remove .is-active` attributes, providing a lightweight way to control the modal's display state. While Alpine.js could also manage this, `_hyperscript` is used directly from the prompt's template example.
*   **DataTables for List Views:**
    *   The `_loansummary_table.html` partial includes a `script` block to initialize DataTables on the rendered table. This enables client-side searching, sorting, and pagination for an enhanced user experience without backend reloads.
*   **DRY Template Inheritance:**
    *   All templates (`list.html`, `form.html`, `confirm_delete.html`) extend `core/base.html` to inherit common layout, header, footer, and essential CDN links (like HTMX, Alpine.js, jQuery, and DataTables). This adheres to the DRY principle.

## Final Notes

*   **Placeholder Replacement:** Remember to replace placeholder URLs (e.g., `balance_sheet_view`, `loan_particular_details_view`) with your actual Django URL names in your project's `urls.py`.
*   **Business Logic:** The aggregation logic for `total_debit` and `total_credit` is now firmly within the `LoanMaster.summary_objects` custom manager, adhering to the "Fat Model" principle. Views remain thin and focus on dispatching requests and rendering templates.
*   **Database Synchronization:** Since `managed = False` is used for `LoanMaster` and `LoanDetail`, Django will not attempt to create or modify these tables. Ensure your existing database tables (`tblAcc_LoanMaster`, `tblAcc_LoanDetails`) match the defined model fields.
*   **Session Management:** The `CompId` and `FinYearId` are crucial for data filtering. In a production Django environment, these would typically be managed via user sessions, user profiles, or a custom middleware that sets these values contextually for the current user.
*   **Styling:** Tailwind CSS classes are extensively used throughout the HTML templates to provide a modern, responsive, and consistent look and feel without writing custom CSS.

This comprehensive plan provides a clear, automated path to modernize your `Acc_Loan_Particulars` module to Django, leveraging modern web development best practices for improved maintainability, performance, and user experience.