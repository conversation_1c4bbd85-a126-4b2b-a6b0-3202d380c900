This comprehensive Django modernization plan addresses the migration of your ASP.NET application, focusing on automation, modern Django patterns, and a clear separation of concerns. We will transition from the existing ASP.NET GridView and C# code-behind to a robust Django 5.0+ solution using HTMX for dynamic interactions, Alpine.js for frontend state, and DataTables for enhanced data presentation.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code utilizes a SQL query to fetch data:
`select Sum(CreditAmt) As loan,tblACC_Capital_Master.Particulars,tblACC_Capital_Master.Id from tblACC_Capital_Details inner join tblACC_Capital_Master on tblACC_Capital_Master.Id=tblACC_Capital_Details.MId And CompId=" + CompId + " AND FinYearId<=" + FinYearId + " group by tblACC_Capital_Master.Particulars,tblACC_Capital_Master.Id`

From this, we identify two primary tables:
- **`tblACC_Capital_Master`**:
    - `Id` (Primary Key, identified by `GridView1`'s `lblId` and `tblACC_Capital_Details.MId`)
    - `Particulars` (String, displayed in `LinkButton1`)
    - `CompId` (Integer, used in `WHERE` clause, assumed to be on `tblACC_Capital_Master` for filtering)
    - `FinYearId` (Integer, used in `WHERE` clause, assumed to be on `tblACC_Capital_Master` for filtering)

- **`tblACC_Capital_Details`**:
    - `MId` (Foreign Key referencing `tblACC_Capital_Master.Id`)
    - `CreditAmt` (Decimal, summed up as `TotCrAmt`)
    - `DebitAmt` (Decimal, inferred from `TotDrAmt` usage, likely exists if `CreditAmt` does for debit/credit entries)

**Assumptions for Data Types:**
- `Id`: Integer
- `Particulars`: Varchar/Text
- `CompId`, `FinYearId`: Integer
- `CreditAmt`, `DebitAmt`: Decimal/Numeric (e.g., `DecimalField(max_digits=18, decimal_places=2)`)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Analysis:**
The provided ASP.NET page `Acc_Capital_Particulars.aspx` primarily serves as a **Read (List)** view.
- **Read:** The page displays a list of aggregated capital particulars, showing `Particulars`, `TotDrAmt`, and `TotCrAmt`. This is achieved by fetching data from `tblACC_Capital_Master` and `tblACC_Capital_Details`, performing a join and group by, then binding to a `GridView`.
- **Navigation/Detail View Trigger:** Clicking on a "Particulars" `LinkButton` triggers a `GridView1_RowCommand` event, which then redirects to `Acc_Capital_Part_Details.aspx?MId=[id]`. This indicates a **Detail View** for each capital particular.
- **Cancel:** The `Button1` (Cancel) redirects to `BalanceSheet.aspx`. This is simple page navigation.

**No direct Create, Update, or Delete operations are performed on `tblACC_Capital_Master` from this specific ASP.NET page.** However, to provide a comprehensive Django solution as per guidelines, we will include placeholder **Create**, **Update**, and **Delete** views and forms for the `CapitalParticular` model, aligning with a typical Django CRUD pattern. The aggregated amounts are derived, not stored directly on `CapitalParticular` objects.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**
- **`asp:GridView ID="GridView1"`**: This will be migrated to a standard HTML `<table>` element enhanced with **DataTables** for client-side search, sorting, and pagination.
- **`asp:Panel ID="Panel1"`**: This is primarily for layout and scrolling. In Django, this will be handled by **Tailwind CSS** for styling and standard HTML `<div>` elements.
- **`asp:LinkButton ID="LinkButton1"`**: This will become a standard HTML `<a>` tag or a `button` with **HTMX** attributes to trigger a modal or redirect for the detail view.
- **`asp:Label` controls (e.g., `lblDrAmt`, `lblCrAmt`, `TotLbl`, `TotDebit`, `TotCredit`, `lblId`)**: These are for displaying data. In Django templates, these will be rendered directly from context variables or model properties. The total labels in the footer will be computed in the view or manager and passed to the template.
- **`asp:Button ID="Button1"` (Cancel)**: This will be a standard HTML `<a>` tag or `<button>` for navigation.

### Step 4: Generate Django Code

**App Name:** `capital` (within an `accounts` or `transactions` Django app, for this exercise, we focus on the `capital` app itself)
**Model Name:** `CapitalParticular` (singular, capitalized)
**Model Name Lower:** `capitalparticular`
**Model Name Plural Lower:** `capitalparticulars`

#### 4.1 Models (`capital/models.py`)

This file defines the Django models that map to your existing database tables. We'll include both `CapitalParticular` (for `tblACC_Capital_Master`) and `CapitalDetail` (for `tblACC_Capital_Details`) to accurately represent the relationship and enable the aggregation logic.

```python
# capital/models.py
from django.db import models
from django.db.models import Sum

class CapitalParticular(models.Model):
    """
    Represents an entry in tblACC_Capital_Master.
    Contains main capital particulars.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    particulars = models.CharField(db_column='Particulars', max_length=255) # Assuming VARCHAR(255)
    # Assuming CompId and FinYearId exist on the master table for filtering
    compid = models.IntegerField(db_column='CompId', null=True, blank=True)
    finyearid = models.IntegerField(db_column='FinYearId', null=True, blank=True)

    class Meta:
        managed = False  # Important: Django won't manage this table's schema
        db_table = 'tblACC_Capital_Master'
        verbose_name = 'Capital Particular'
        verbose_name_plural = 'Capital Particulars'

    def __str__(self):
        return self.particulars

    # Fat Model approach: Add methods for business logic or computed properties
    # These properties would compute sums for a single CapitalParticular instance
    # The ListView will use annotation for efficiency across the queryset.
    @property
    def current_total_debit_amount(self):
        """Calculates total debit amount for this particular instance."""
        return self.capitaldetail_set.aggregate(total=Sum('DebitAmt'))['total'] or 0.0

    @property
    def current_total_credit_amount(self):
        """Calculates total credit amount for this particular instance."""
        return self.capitaldetail_set.aggregate(total=Sum('CreditAmt'))['total'] or 0.0


class CapitalDetail(models.Model):
    """
    Represents an entry in tblACC_Capital_Details.
    Contains detailed debit/credit amounts linked to CapitalParticular.
    """
    # Assuming an auto-incrementing ID for details table, if not, adjust primary_key.
    # If no ID, Django might require a composite primary key or manage it implicitly.
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming 'Id' column in tblACC_Capital_Details
    master = models.ForeignKey(
        CapitalParticular,
        models.DO_NOTHING,  # Do nothing on delete of parent, common for legacy DBs
        db_column='MId',
        related_name='capitaldetail_set' # Explicit related_name for clarity
    )
    credit_amount = models.DecimalField(db_column='CreditAmt', max_digits=18, decimal_places=2)
    debit_amount = models.DecimalField(db_column='DebitAmt', max_digits=18, decimal_places=2) # Inferred existence

    class Meta:
        managed = False  # Important: Django won't manage this table's schema
        db_table = 'tblACC_Capital_Details'
        verbose_name = 'Capital Detail'
        verbose_name_plural = 'Capital Details'

    def __str__(self):
        return f"Detail for {self.master.particulars} (Credit: {self.credit_amount}, Debit: {self.debit_amount})"

```

#### 4.2 Forms (`capital/forms.py`)

This file defines the Django form for interacting with `CapitalParticular` objects.

```python
# capital/forms.py
from django import forms
from .models import CapitalParticular

class CapitalParticularForm(forms.ModelForm):
    """
    Form for creating and updating CapitalParticular objects.
    """
    class Meta:
        model = CapitalParticular
        # Include fields that are directly editable for CapitalParticular.
        # Aggregated amounts are not part of the form's fields.
        fields = ['particulars', 'compid', 'finyearid']
        widgets = {
            'particulars': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter particulars description'
            }),
            'compid': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Company ID'
            }),
            'finyearid': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Financial Year ID'
            }),
        }

    # Add custom validation methods here if needed
    # Example:
    # def clean_particulars(self):
    #     particulars = self.cleaned_data['particulars']
    #     if len(particulars) < 3:
    #         raise forms.ValidationError("Particulars must be at least 3 characters long.")
    #     return particulars
```

#### 4.3 Views (`capital/views.py`)

This file contains the Class-Based Views for handling list, create, update, and delete operations, along with a partial view for HTMX-driven table refreshes. The `ListView` will handle the aggregation logic.

```python
# capital/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, RedirectView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponseRedirect, HttpResponse
from django.db.models import Sum, F # F for referencing model fields
from .models import CapitalParticular, CapitalDetail # Import CapitalDetail
from .forms import CapitalParticularForm

# Helper function to mimic ASP.NET Session variables for filtering
def get_session_params(request):
    """
    Retrieves company and financial year IDs from session.
    Provides default values if not found, as a fallback for testing/initial setup.
    In a real system, these would likely be set upon user login or selection.
    """
    comp_id = request.session.get('compid', 1)  # Default to 1, adjust as per your ERP system
    fin_year_id = request.session.get('finyear', 2024) # Default to 2024, adjust as per your ERP system
    return comp_id, fin_year_id

class CapitalParticularListView(ListView):
    """
    Displays the main list of Capital Particulars with aggregated debit/credit amounts.
    This view replicates the summary table from the ASP.NET page.
    """
    model = CapitalParticular
    template_name = 'capital/capitalparticular/list.html'
    context_object_name = 'capitalparticulars' # This name is used in the main template

    def get_queryset(self):
        """
        Fetches and annotates the CapitalParticulars queryset with total debit/credit amounts.
        Replicates the aggregation logic from the original ASP.NET SQL query.
        Filters by CompId and FinYearId from session.
        """
        comp_id, fin_year_id = get_session_params(self.request)

        # Annotate CapitalParticulars with sums from related CapitalDetail entries
        # F('capitaldetail__debit_amount') refers to the DebitAmt column in CapitalDetail
        # through the ForeignKey relationship.
        queryset = CapitalParticular.objects.filter(
            compid=comp_id,
            finyearid__lte=fin_year_id # Replicating 'FinYearId<=' condition
        ).annotate(
            total_debit_amount=Sum('capitaldetail__debit_amount'),
            total_credit_amount=Sum('capitaldetail__credit_amount')
        ).order_by('particulars') # Order for consistent display

        return queryset

    def get_context_data(self, **kwargs):
        """
        Adds grand totals for debit and credit to the context,
        similar to how the ASP.NET GridView footer displayed sums.
        """
        context = super().get_context_data(**kwargs)
        
        # Calculate grand totals from the annotated queryset
        # Use a list comprehension to ensure we sum only valid numbers
        total_debit = sum(obj.total_debit_amount for obj in context['capitalparticulars'] if obj.total_debit_amount is not None)
        total_credit = sum(obj.total_credit_amount for obj in context['capitalparticulars'] if obj.total_credit_amount is not None)
        
        context['grand_total_debit'] = total_debit
        context['grand_total_credit'] = total_credit
        return context

class CapitalParticularTablePartialView(CapitalParticularListView):
    """
    A specific view for HTMX requests to refresh only the table content.
    Inherits from CapitalParticularListView to reuse queryset logic.
    """
    template_name = 'capital/capitalparticular/_capitalparticular_table.html'

class CapitalParticularCreateView(CreateView):
    """
    Handles the creation of new Capital Particulars.
    """
    model = CapitalParticular
    form_class = CapitalParticularForm
    template_name = 'capital/capitalparticular/form.html'
    success_url = reverse_lazy('capital:capitalparticular_list')

    def form_valid(self, form):
        # Set session-derived fields before saving for new objects
        comp_id, fin_year_id = get_session_params(self.request)
        form.instance.compid = comp_id
        form.instance.finyearid = fin_year_id
        
        response = super().form_valid(form)
        messages.success(self.request, f'Capital Particular "{self.object.particulars}" added successfully.')
        # HTMX response for modal closure and list refresh
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # HTTP 204 No Content for HTMX to do nothing more than trigger
                headers={
                    'HX-Trigger': 'refreshCapitalParticularList' # Custom HTMX event to refresh list
                }
            )
        return response

class CapitalParticularUpdateView(UpdateView):
    """
    Handles updating existing Capital Particulars.
    """
    model = CapitalParticular
    form_class = CapitalParticularForm
    template_name = 'capital/capitalparticular/form.html'
    success_url = reverse_lazy('capital:capitalparticular_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, f'Capital Particular "{self.object.particulars}" updated successfully.')
        # HTMX response for modal closure and list refresh
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCapitalParticularList'
                }
            )
        return response

class CapitalParticularDeleteView(DeleteView):
    """
    Handles deleting Capital Particulars.
    """
    model = CapitalParticular
    template_name = 'capital/capitalparticular/confirm_delete.html'
    success_url = reverse_lazy('capital:capitalparticular_list')

    def delete(self, request, *args, **kwargs):
        obj_particulars = self.get_object().particulars # Get name before deletion
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, f'Capital Particular "{obj_particulars}" deleted successfully.')
        # HTMX response for modal closure and list refresh
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCapitalParticularList'
                }
            )
        return response

class BalanceSheetRedirectView(RedirectView):
    """
    Mimics the ASP.NET btnCancel_Click behavior, redirecting to a balance sheet page.
    """
    url = reverse_lazy('accounts:balance_sheet') # Assume 'accounts' app has a 'balance_sheet' URL

class CapitalParticularDetailRedirectView(RedirectView):
    """
    Mimics the ASP.NET LinkButton1 click behavior, redirecting to a detail page.
    The original ASP.NET redirected to Acc_Capital_Part_Details.aspx.
    This view will redirect to a Django detail view for CapitalParticular.
    """
    # Assuming 'capital:capitalparticular_detail' is the actual detail URL for a particular
    # Replace with your actual detail URL if it's not simple PK based.
    permanent = False # This is a temporary redirect
    query_string = True # Pass query string parameters if any (e.g., ModId, SubModId)

    def get_redirect_url(self, *args, **kwargs):
        particular_id = self.kwargs['pk']
        messages.info(self.request, f"Redirecting to details for Capital Particular ID: {particular_id}")
        # Construct the URL for the actual detail page using reverse_lazy
        # Example: return reverse_lazy('accounts:capitalparticular_detail', args=[particular_id])
        # For now, let's redirect to a dummy placeholder if actual detail view is not yet defined
        return reverse_lazy('capital:capitalparticular_list') + f'?detail_id={particular_id}' # Placeholder for actual redirect
```

#### 4.4 Templates (`capital/templates/capital/capitalparticular/`)

These templates will power the user interface, leveraging HTMX, Alpine.js, and DataTables.

**`list.html`** (Main page displaying the table and modal functionality)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4">
        <h2 class="text-3xl font-extrabold text-gray-900">Capital (Goods) Particulars</h2>
        <div class="flex space-x-3">
            <button
                class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75"
                hx-get="{% url 'capital:capitalparticular_add' %}"
                hx-target="#modalContent"
                hx-trigger="click"
                _="on click add .flex to #modal and remove .hidden from #modal">
                <i class="fas fa-plus mr-2"></i> Add New Particular
            </button>
            <a href="{% url 'accounts:balance_sheet' %}"
               class="bg-gray-400 hover:bg-gray-500 text-gray-800 font-semibold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out focus:outline-none focus:ring-2 focus:ring-gray-300 focus:ring-opacity-75">
                <i class="fas fa-times mr-2"></i> Cancel
            </a>
        </div>
    </div>
    
    {% if messages %}
    <div id="messages" class="mb-4">
        {% for message in messages %}
        <div class="p-3 mb-2 {% if message.tags == 'success' %}bg-green-100 text-green-700{% elif message.tags == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %} rounded-md shadow-sm">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <div id="capitalparticularTable-container"
         hx-trigger="load, refreshCapitalParticularList from:body"
         hx-get="{% url 'capital:capitalparticular_table' %}"
         hx-swap="innerHTML"
         class="bg-white rounded-lg shadow-lg p-6">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center p-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Capital Particulars...</p>
        </div>
    </div>
    
    <!-- Modal for forms (Add/Edit/Delete Confirmation) -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .flex from me and add .hidden to me">
        <div id="modalContent" class="bg-white p-8 rounded-lg shadow-2xl max-w-2xl w-full mx-4 transform transition-all duration-300 scale-95 opacity-0"
             _="on load transition my transform to scale-100 then my opacity to 1">
            <!-- Form content will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Alpine.js (already included in base.html usually) -->
<!-- DataTables JS (already included in base.html usually) -->
<script>
    // Example Alpine.js component if needed for page-specific state
    document.addEventListener('alpine:init', () => {
        Alpine.data('capitalParticularsPage', () => ({
            // Add any page-specific reactive state here
        }));
    });
</script>
{% endblock %}
```

**`_capitalparticular_table.html`** (HTMX partial for the DataTables content)

```html
<table id="capitalparticularTable" class="min-w-full bg-white border border-gray-200 shadow-sm">
    <thead>
        <tr class="bg-gray-100 border-b border-gray-200">
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 uppercase tracking-wider w-1/12">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 uppercase tracking-wider w-4/12">Particulars</th>
            <th class="py-3 px-4 text-right text-xs font-medium text-gray-600 uppercase tracking-wider w-2/12">Debit</th>
            <th class="py-3 px-4 text-right text-xs font-medium text-gray-600 uppercase tracking-wider w-2/12">Credit</th>
            <th class="py-3 px-4 text-center text-xs font-medium text-gray-600 uppercase tracking-wider w-3/12">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in capitalparticulars %}
        <tr class="border-b border-gray-200 hover:bg-gray-50">
            <td class="py-3 px-4 text-sm text-gray-700 text-right">{{ forloop.counter }}</td>
            <td class="py-3 px-4 text-sm text-blue-600 font-medium whitespace-nowrap">
                <button
                    class="hover:underline focus:outline-none"
                    hx-get="{% url 'capital:capitalparticular_detail_redirect' pk=obj.pk %}"
                    hx-target="body"
                    hx-swap="none"
                    hx-on::after-request="location.reload();"
                    title="View details for {{ obj.particulars }}">
                    {{ obj.particulars }}
                </button>
            </td>
            <td class="py-3 px-4 text-sm text-gray-700 text-right">
                {{ obj.total_debit_amount|floatformat:2 }}
            </td>
            <td class="py-3 px-4 text-sm text-gray-700 text-right">
                {{ obj.total_credit_amount|floatformat:2 }}
            </td>
            <td class="py-3 px-4 text-sm text-center whitespace-nowrap">
                <button
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1.5 px-3 rounded-md text-xs transition duration-150 ease-in-out mr-2"
                    hx-get="{% url 'capital:capitalparticular_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .flex to #modal and remove .hidden from #modal">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1.5 px-3 rounded-md text-xs transition duration-150 ease-in-out"
                    hx-get="{% url 'capital:capitalparticular_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .flex to #modal and remove .hidden from #modal">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="5" class="py-4 px-4 text-center text-gray-500 text-base">
                No records to display.
            </td>
        </tr>
        {% endfor %}
    </tbody>
    <tfoot>
        <tr class="bg-gray-100 border-t-2 border-gray-300 font-bold">
            <td colspan="2" class="py-3 px-4 text-right text-gray-800 text-base">Total:</td>
            <td class="py-3 px-4 text-right text-gray-800 text-base">
                {{ grand_total_debit|floatformat:2 }}
            </td>
            <td class="py-3 px-4 text-right text-gray-800 text-base">
                {{ grand_total_credit|floatformat:2 }}
            </td>
            <td class="py-3 px-4"></td> {# Empty cell for actions column in footer #}
        </tr>
    </tfoot>
</table>

<script>
    // Initialize DataTables on the loaded table
    $(document).ready(function() {
        $('#capitalparticularTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "searching": true,
            "ordering": true,
            "info": true,
            "paging": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 4] }, // Disable sorting for SN and Actions
                { "searchable": false, "targets": [0, 2, 3, 4] } // Disable searching for SN, Debit, Credit, Actions
            ]
        });
    });
</script>
```

**`form.html`** (HTMX partial for Add/Edit form)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-4">
        {{ form.instance.pk|yesno:'Edit,Add' }} Capital Particular
    </h3>
    <form hx-post="{{ request.path }}" hx-swap="none"> {# hx-swap="none" combined with HX-Trigger allows full control #}
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
            {% if form.non_field_errors %}
            <div class="text-red-600 text-sm mb-4">
                {% for error in form.non_field_errors %}
                <p>{{ error }}</p>
                {% endfor %}
            </div>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2.5 px-5 rounded-lg shadow-md transition duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-gray-300 focus:ring-opacity-75"
                _="on click remove .flex from #modal and add .hidden to #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2.5 px-5 rounded-lg shadow-md transition duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75">
                Save Capital Particular
            </button>
        </div>
    </form>
</div>
```

**`confirm_delete.html`** (HTMX partial for Delete confirmation)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-red-700 mb-6 border-b pb-4">Confirm Deletion</h3>
    <p class="text-gray-700 text-lg mb-8">
        Are you sure you want to delete the Capital Particular:
        <span class="font-bold text-red-600">"{{ object.particulars }}"</span>?
        This action cannot be undone.
    </p>
    <form hx-post="{% url 'capital:capitalparticular_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2.5 px-5 rounded-lg shadow-md transition duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-gray-300 focus:ring-opacity-75"
                _="on click remove .flex from #modal and add .hidden to #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2.5 px-5 rounded-lg shadow-md transition duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-75">
                Yes, Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`capital/urls.py`)

This file defines the URL patterns for your Django application, mapping URLs to views.

```python
# capital/urls.py
from django.urls import path
from .views import (
    CapitalParticularListView,
    CapitalParticularTablePartialView,
    CapitalParticularCreateView,
    CapitalParticularUpdateView,
    CapitalParticularDeleteView,
    CapitalParticularDetailRedirectView,
    BalanceSheetRedirectView,
)

app_name = 'capital' # Namespace for this app's URLs

urlpatterns = [
    # Main list view (equivalent to Acc_Capital_Particulars.aspx)
    path('capital-particulars/', CapitalParticularListView.as_view(), name='capitalparticular_list'),
    
    # HTMX endpoint for refreshing only the table content
    path('capital-particulars/table/', CapitalParticularTablePartialView.as_view(), name='capitalparticular_table'),
    
    # CRUD operations for Capital Particulars
    path('capital-particulars/add/', CapitalParticularCreateView.as_view(), name='capitalparticular_add'),
    path('capital-particulars/edit/<int:pk>/', CapitalParticularUpdateView.as_view(), name='capitalparticular_edit'),
    path('capital-particulars/delete/<int:pk>/', CapitalParticularDeleteView.as_view(), name='capitalparticular_delete'),
    
    # Redirect for "Particulars" link (mimics ASP.NET's Acc_Capital_Part_Details.aspx redirection)
    path('capital-particulars/<int:pk>/details-redirect/', CapitalParticularDetailRedirectView.as_view(), name='capitalparticular_detail_redirect'),
    
    # Redirect for "Cancel" button (mimics ASP.NET's BalanceSheet.aspx redirection)
    # This assumes an 'accounts' app exists with a 'balance_sheet' URL name.
    # If not, you might need to define a simple view or a static redirect.
    path('balance-sheet-redirect/', BalanceSheetRedirectView.as_view(), name='balance_sheet_redirect'),
]

```
**Note:** You will also need to include `capital.urls` in your project's main `urls.py` (e.g., `path('accounts/', include('capital.urls', namespace='capital'))`). Also, ensure you have an `accounts` app with a `balance_sheet` URL (e.g. `path('balance-sheet/', BalanceSheetView.as_view(), name='balance_sheet')`) for the `BalanceSheetRedirectView` to work.

#### 4.6 Tests (`capital/tests.py`)

Comprehensive tests for models and views ensure reliability and maintainability.

```python
# capital/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from .models import CapitalParticular, CapitalDetail
from django.db.models import Sum

class CapitalParticularModelTest(TestCase):
    """
    Unit tests for the CapitalParticular and CapitalDetail models.
    """
    @classmethod
    def setUpTestData(cls):
        # Create common test data for all tests in this class
        cls.comp_id = 1
        cls.fin_year_id = 2024

        cls.particular1 = CapitalParticular.objects.create(
            id=101, # Explicit ID for managed=False
            particulars='Building Capital',
            compid=cls.comp_id,
            finyearid=cls.fin_year_id
        )
        cls.particular2 = CapitalParticular.objects.create(
            id=102,
            particulars='Machinery Capital',
            compid=cls.comp_id,
            finyearid=cls.fin_year_id - 1 # Older financial year
        )
        cls.particular3 = CapitalParticular.objects.create(
            id=103,
            particulars='Vehicle Capital',
            compid=cls.comp_id + 1, # Different company
            finyearid=cls.fin_year_id
        )

        # Create CapitalDetail entries
        CapitalDetail.objects.create(id=1, master=cls.particular1, credit_amount=1000.00, debit_amount=500.00)
        CapitalDetail.objects.create(id=2, master=cls.particular1, credit_amount=2000.00, debit_amount=1500.00)
        CapitalDetail.objects.create(id=3, master=cls.particular2, credit_amount=500.00, debit_amount=200.00)

    def test_capital_particular_creation(self):
        """Test that CapitalParticular objects are created correctly."""
        particular = CapitalParticular.objects.get(id=self.particular1.id)
        self.assertEqual(particular.particulars, 'Building Capital')
        self.assertEqual(particular.compid, self.comp_id)
        self.assertEqual(particular.finyearid, self.fin_year_id)

    def test_capital_detail_creation(self):
        """Test that CapitalDetail objects are created correctly and link to master."""
        detail = CapitalDetail.objects.get(id=1)
        self.assertEqual(detail.master, self.particular1)
        self.assertEqual(detail.credit_amount, 1000.00)
        self.assertEqual(detail.debit_amount, 500.00)

    def test_particulars_label(self):
        """Test verbose name for 'particulars' field."""
        particular = CapitalParticular.objects.get(id=self.particular1.id)
        field_label = particular._meta.get_field('particulars').verbose_name
        self.assertEqual(field_label, 'particulars') # Django default, can be customized in model

    def test_str_method(self):
        """Test the __str__ method of CapitalParticular."""
        particular = CapitalParticular.objects.get(id=self.particular1.id)
        self.assertEqual(str(particular), 'Building Capital')

    def test_current_total_debit_amount_property(self):
        """Test calculation of total debit amount for a single CapitalParticular."""
        particular = CapitalParticular.objects.get(id=self.particular1.id)
        self.assertEqual(particular.current_total_debit_amount, 2000.00) # 500 + 1500

    def test_current_total_credit_amount_property(self):
        """Test calculation of total credit amount for a single CapitalParticular."""
        particular = CapitalParticular.objects.get(id=self.particular1.id)
        self.assertEqual(particular.current_total_credit_amount, 3000.00) # 1000 + 2000

    def test_no_details_amounts(self):
        """Test properties for a particular with no associated details."""
        new_particular = CapitalParticular.objects.create(id=104, particulars='No Details', compid=self.comp_id, finyearid=self.fin_year_id)
        self.assertEqual(new_particular.current_total_debit_amount, 0.0)
        self.assertEqual(new_particular.current_total_credit_amount, 0.0)

class CapitalParticularViewsTest(TestCase):
    """
    Integration tests for CapitalParticular views, including HTMX interactions.
    """
    @classmethod
    def setUpTestData(cls):
        # Setup common test data for all view tests
        cls.comp_id = 1
        cls.fin_year_id = 2024
        
        cls.particular1 = CapitalParticular.objects.create(
            id=101, particulars='Test Particular 1', compid=cls.comp_id, finyearid=cls.fin_year_id
        )
        cls.particular2 = CapitalParticular.objects.create(
            id=102, particulars='Test Particular 2', compid=cls.comp_id, finyearid=cls.fin_year_id - 1
        )
        CapitalDetail.objects.create(id=1, master=cls.particular1, credit_amount=100.00, debit_amount=50.00)
        CapitalDetail.objects.create(id=2, master=cls.particular1, credit_amount=200.00, debit_amount=150.00)
        CapitalDetail.objects.create(id=3, master=cls.particular2, credit_amount=50.00, debit_amount=20.00)

    def setUp(self):
        self.client = Client()
        # Set session variables for tests, mimicking user login
        session = self.client.session
        session['compid'] = self.comp_id
        session['finyear'] = self.fin_year_id
        session.save()

    def test_list_view_get(self):
        """Test CapitalParticularListView renders correctly."""
        response = self.client.get(reverse('capital:capitalparticular_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'capital/capitalparticular/list.html')
        self.assertIn('capitalparticulars', response.context)
        # Only particular1 should be in the queryset due to filters
        self.assertEqual(response.context['capitalparticulars'].count(), 1)
        self.assertEqual(response.context['capitalparticulars'].first(), self.particular1)
        self.assertContains(response, 'Test Particular 1')
        self.assertContains(response, '200.00') # Debit sum (50 + 150)
        self.assertContains(response, '300.00') # Credit sum (100 + 200)
        self.assertContains(response, 'Total:')
        self.assertContains(response, 'grand_total_debit')
        self.assertContains(response, 'grand_total_credit')


    def test_list_view_get_no_data(self):
        """Test list view when no data matches filters."""
        # Clear existing data or set session params that yield no results
        CapitalParticular.objects.all().delete()
        response = self.client.get(reverse('capital:capitalparticular_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No records to display.')
        self.assertEqual(response.context['capitalparticulars'].count(), 0)
        self.assertEqual(response.context['grand_total_debit'], 0.0)
        self.assertEqual(response.context['grand_total_credit'], 0.0)

    def test_table_partial_view_get(self):
        """Test CapitalParticularTablePartialView renders correctly for HTMX."""
        response = self.client.get(reverse('capital:capitalparticular_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'capital/capitalparticular/_capitalparticular_table.html')
        self.assertIn('capitalparticulars', response.context)
        self.assertContains(response, 'Test Particular 1')
        self.assertContains(response, 'Total:') # Check for grand totals in partial

    def test_create_view_get(self):
        """Test CapitalParticularCreateView GET request."""
        response = self.client.get(reverse('capital:capitalparticular_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'capital/capitalparticular/form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Capital Particular')

    def test_create_view_post_success(self):
        """Test CapitalParticularCreateView POST request for successful creation."""
        initial_count = CapitalParticular.objects.count()
        data = {
            'particulars': 'New Capital Item',
            'compid': self.comp_id,
            'finyearid': self.fin_year_id
        }
        response = self.client.post(reverse('capital:capitalparticular_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX No Content
        self.assertEqual(CapitalParticular.objects.count(), initial_count + 1)
        self.assertTrue(CapitalParticular.objects.filter(particulars='New Capital Item').exists())
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCapitalParticularList')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Capital Particular "New Capital Item" added successfully.')


    def test_create_view_post_invalid(self):
        """Test CapitalParticularCreateView POST request with invalid data."""
        initial_count = CapitalParticular.objects.count()
        data = {
            'particulars': '', # Invalid: empty particulars
            'compid': self.comp_id,
            'finyearid': self.fin_year_id
        }
        response = self.client.post(reverse('capital:capitalparticular_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Renders form with errors
        self.assertTemplateUsed(response, 'capital/capitalparticular/form.html')
        self.assertEqual(CapitalParticular.objects.count(), initial_count)
        self.assertContains(response, 'This field is required.')

    def test_update_view_get(self):
        """Test CapitalParticularUpdateView GET request."""
        response = self.client.get(reverse('capital:capitalparticular_edit', args=[self.particular1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'capital/capitalparticular/form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.particular1)
        self.assertContains(response, 'Edit Capital Particular')

    def test_update_view_post_success(self):
        """Test CapitalParticularUpdateView POST request for successful update."""
        updated_name = 'Updated Capital Name'
        data = {
            'particulars': updated_name,
            'compid': self.particular1.compid,
            'finyearid': self.particular1.finyearid
        }
        response = self.client.post(reverse('capital:capitalparticular_edit', args=[self.particular1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.particular1.refresh_from_db()
        self.assertEqual(self.particular1.particulars, updated_name)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCapitalParticularList')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), f'Capital Particular "{updated_name}" updated successfully.')

    def test_delete_view_get(self):
        """Test CapitalParticularDeleteView GET request."""
        response = self.client.get(reverse('capital:capitalparticular_delete', args=[self.particular1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'capital/capitalparticular/confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.particular1)
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, 'Are you sure you want to delete the Capital Particular: "Test Particular 1"?')

    def test_delete_view_post_success(self):
        """Test CapitalParticularDeleteView POST request for successful deletion."""
        initial_count = CapitalParticular.objects.count()
        particular_id = self.particular1.pk
        response = self.client.post(reverse('capital:capitalparticular_delete', args=[particular_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(CapitalParticular.objects.count(), initial_count - 1)
        self.assertFalse(CapitalParticular.objects.filter(pk=particular_id).exists())
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCapitalParticularList')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Capital Particular "Test Particular 1" deleted successfully.')

    def test_balance_sheet_redirect_view(self):
        """Test BalanceSheetRedirectView redirects correctly."""
        response = self.client.get(reverse('capital:balance_sheet_redirect'))
        self.assertEqual(response.status_code, 302) # Found (temporary redirect)
        # This assumes 'accounts:balance_sheet' is defined in your main urls.py or an accounts app
        self.assertRedirects(response, reverse('accounts:balance_sheet'), fetch_redirect_response=False)

    def test_capital_particular_detail_redirect_view(self):
        """Test CapitalParticularDetailRedirectView redirects correctly."""
        response = self.client.get(reverse('capital:capitalparticular_detail_redirect', args=[self.particular1.pk]))
        self.assertEqual(response.status_code, 302)
        # This checks the placeholder redirect URL. Update this when the actual detail view is ready.
        self.assertRedirects(response, reverse('capital:capitalparticular_list') + f'?detail_id={self.particular1.pk}', fetch_redirect_response=False)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertIn(f"Redirecting to details for Capital Particular ID: {self.particular1.pk}", str(messages[0]))
```

### Step 5: HTMX and Alpine.js Integration

**Instructions and Implementation Details:**
1.  **HTMX for Dynamic Updates:**
    *   The `list.html` uses `hx-get="{% url 'capital:capitalparticular_table' %}" hx-swap="innerHTML" hx-trigger="load, refreshCapitalParticularList from:body"` to load the DataTable initially and refresh it after any CRUD operation.
    *   Add/Edit/Delete buttons use `hx-get` to fetch the form/confirmation partial into the `#modalContent` div.
    *   Form submissions (in `form.html` and `confirm_delete.html`) use `hx-post` to send data. The views respond with `HX-Trigger` headers (`refreshCapitalParticularList`) to signal the main list to re-fetch its data.
    *   `hx-swap="none"` on forms is crucial to allow the view to handle the success response (e.g., triggering a refresh) without replacing the form itself immediately. The modal closure is handled by Alpine.js/hyperscript.

2.  **Alpine.js for UI State Management (Modals):**
    *   The `#modal` div uses Alpine.js (via `_` from hyperscript) to control its visibility.
    *   `on click add .flex to #modal and remove .hidden from #modal` opens the modal.
    *   `on click if event.target.id == 'modal' remove .flex from me and add .hidden to me` closes the modal when clicking outside of `modalContent`.
    *   The form's cancel button also uses `on click remove .flex from #modal and add .hidden to #modal` to close the modal.
    *   `on load transition my transform to scale-100 then my opacity to 1` provides a smooth transition effect when the modal content loads.

3.  **DataTables for List Views:**
    *   The `_capitalparticular_table.html` partial contains the `<table>` element.
    *   A `<script>` block within this partial initializes DataTables on `$(document).ready(function() { $('#capitalparticularTable').DataTable(); });`. This ensures DataTables is re-initialized every time the table content is reloaded via HTMX.
    *   Column definitions are included to disable sorting/searching for SN and Action columns.

4.  **No Additional JavaScript:**
    *   All dynamic interactions are handled by HTMX and Alpine.js/hyperscript. No custom JavaScript beyond the DataTables initialization is required, adhering to the "no additional JavaScript" principle.

5.  **DRY Template Inheritance:**
    *   All templates extend `core/base.html` implicitly (as per instructions).
    *   The DataTables JavaScript and CSS, along with HTMX and Alpine.js, are assumed to be loaded in `core/base.html` via CDN links.

This comprehensive plan provides a clear, actionable roadmap for modernizing the ASP.NET Capital Particulars module to a Django-based solution, leveraging AI-assisted automation principles.