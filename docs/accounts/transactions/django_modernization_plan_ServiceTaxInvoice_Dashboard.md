## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

The provided ASP.NET code for `ServiceTaxInvoice_Dashboard.aspx` and its corresponding C# code-behind file `ServiceTaxInvoice_Dashboard.aspx.cs` are largely empty. The `.aspx` file primarily defines content placeholders within a master page and includes a JavaScript file (`loadingNotifier.js`), but it does not contain any ASP.NET controls (like `GridView`, `TextBox`, `Button`) or direct database interaction details. The C# code-behind has only an empty `Page_Load` method.

This means we cannot directly extract specific database table names, column structures, or CRUD operations from the given code. However, based on the page name "ServiceTaxInvoice_Dashboard", we can infer the primary business entity: `Service Tax Invoice`.

For the purpose of this modernization plan, we will proceed by assuming a typical "Service Tax Invoice" structure and generating a complete Django implementation that includes models, forms, views, templates, and tests, adhering to all specified guidelines. This approach demonstrates how the AI-assisted automation would generate a standard CRUD module when specific details are inferred or provided from external schema analysis.

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
Given the lack of explicit database information in the provided ASP.NET code, we will infer a database table and its likely columns based on the module's name: `ServiceTaxInvoice_Dashboard`.

*   **Inferred Table Name:** `tblServiceTaxInvoice` (common ASP.NET database naming convention for `ServiceTaxInvoice` entity)
*   **Inferred Columns:**
    *   `InvoiceNo` (text, primary key, unique identifier for the invoice)
    *   `InvoiceDate` (date)
    *   `CustomerName` (text)
    *   `TotalAmount` (decimal)
    *   `TaxAmount` (decimal)
    *   `DueDate` (date, optional)
    *   `IsPaid` (boolean, indicates payment status)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
Since the provided ASP.NET code is empty, we infer the standard CRUD (Create, Read, Update, Delete) operations typically associated with a "Dashboard" for a business entity like "Service Tax Invoice".

*   **Read (R):** Displaying a list of all service tax invoices, possibly with search, sort, and pagination capabilities.
*   **Create (C):** Adding new service tax invoices.
*   **Update (U):** Editing existing service tax invoice details.
*   **Delete (D):** Removing service tax invoices from the system.
*   **Validation Logic:** Basic validation for required fields, data types, and numerical ranges will be assumed.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The ASP.NET code provides no UI controls. Based on the "Dashboard" nature, we infer the following typical UI components:

*   **List View:** A table (Django template with DataTables) to display all service tax invoices, allowing for searching, sorting, and pagination. Each row will have "Edit" and "Delete" actions.
*   **Form for Create/Update:** A modal form (HTMX-triggered partial template) containing input fields for `Invoice No`, `Invoice Date`, `Customer Name`, `Total Amount`, `Tax Amount`, `Due Date`, and `Is Paid`.
*   **Confirmation Dialog for Delete:** A simple modal dialog (HTMX-triggered partial template) to confirm deletion.
*   **Navigation/Add Button:** A button to trigger the "Add New Invoice" form.

The `loadingNotifier.js` would be replaced by HTMX's built-in `hx-indicator` or Alpine.js for showing loading states.

### Step 4: Generate Django Code

We will create a Django application named `accounts` to house this module, aligning with the `Module_Accounts_Transactions` part of the original ASP.NET namespace.

#### 4.1 Models

**Task:** Create a Django model based on the database schema.

**Instructions:**
The `ServiceTaxInvoice` model will be mapped to the `tblServiceTaxInvoice` database table. It includes methods for business logic, demonstrating the "fat model" principle.

**File: `accounts/models.py`**

```python
from django.db import models

class ServiceTaxInvoice(models.Model):
    """
    Represents a Service Tax Invoice in the system.
    This model maps to an existing database table 'tblServiceTaxInvoice'.
    """
    invoice_number = models.CharField(
        max_length=50,
        db_column='InvoiceNo',
        unique=True,
        verbose_name='Invoice Number',
        help_text='Unique identifier for the service tax invoice.'
    )
    invoice_date = models.DateField(
        db_column='InvoiceDate',
        verbose_name='Invoice Date',
        help_text='The date the invoice was issued.'
    )
    customer_name = models.CharField(
        max_length=255,
        db_column='CustomerName',
        verbose_name='Customer Name',
        help_text='Name of the customer associated with the invoice.'
    )
    total_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        db_column='TotalAmount',
        verbose_name='Total Amount',
        help_text='The total amount of the invoice before tax.'
    )
    tax_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        db_column='TaxAmount',
        verbose_name='Tax Amount',
        help_text='The tax amount applied to the invoice.'
    )
    due_date = models.DateField(
        db_column='DueDate',
        null=True,
        blank=True,
        verbose_name='Due Date',
        help_text='The date by which the invoice is due for payment.'
    )
    is_paid = models.BooleanField(
        db_column='IsPaid',
        default=False,
        verbose_name='Paid Status',
        help_text='Indicates whether the invoice has been paid.'
    )

    class Meta:
        managed = False  # Tells Django not to manage table creation/deletion
        db_table = 'tblServiceTaxInvoice'  # Name of the existing database table
        verbose_name = 'Service Tax Invoice'
        verbose_name_plural = 'Service Tax Invoices'
        ordering = ['-invoice_date', 'invoice_number']

    def __str__(self):
        """
        Returns a human-readable string representation of the invoice.
        """
        return f"Invoice {self.invoice_number} ({self.customer_name})"

    def calculate_net_amount(self):
        """
        Calculates the net amount of the invoice (Total Amount - Tax Amount).
        """
        return self.total_amount - self.tax_amount

    def mark_as_paid(self):
        """
        Marks the invoice as paid.
        """
        if not self.is_paid:
            self.is_paid = True
            self.save()
            return True
        return False

    def is_overdue(self):
        """
        Checks if the invoice is overdue based on the due date and paid status.
        """
        if not self.is_paid and self.due_date and self.due_date < models.DateField().today():
            return True
        return False

```

#### 4.2 Forms

**Task:** Define a Django form for user input.

**Instructions:**
A `ModelForm` is used to create and update `ServiceTaxInvoice` objects. Widgets are applied for styling consistency with Tailwind CSS.

**File: `accounts/forms.py`**

```python
from django import forms
from .models import ServiceTaxInvoice

class ServiceTaxInvoiceForm(forms.ModelForm):
    """
    Form for creating and updating ServiceTaxInvoice instances.
    """
    class Meta:
        model = ServiceTaxInvoice
        fields = [
            'invoice_number', 'invoice_date', 'customer_name',
            'total_amount', 'tax_amount', 'due_date', 'is_paid'
        ]
        widgets = {
            'invoice_number': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'e.g., INV2023-001'
            }),
            'invoice_date': forms.DateInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'type': 'date'
            }),
            'customer_name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'e.g., ABC Company'
            }),
            'total_amount': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'step': '0.01'
            }),
            'tax_amount': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'step': '0.01'
            }),
            'due_date': forms.DateInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'type': 'date'
            }),
            'is_paid': forms.CheckboxInput(attrs={
                'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded',
            }),
        }

    def clean_total_amount(self):
        """
        Custom validation for total_amount to ensure it's positive.
        """
        total_amount = self.cleaned_data['total_amount']
        if total_amount < 0:
            raise forms.ValidationError("Total amount cannot be negative.")
        return total_amount

    def clean(self):
        """
        Global form validation to ensure tax amount is not greater than total amount.
        """
        cleaned_data = super().clean()
        total_amount = cleaned_data.get('total_amount')
        tax_amount = cleaned_data.get('tax_amount')

        if total_amount is not None and tax_amount is not None:
            if tax_amount > total_amount:
                self.add_error('tax_amount', "Tax amount cannot be greater than the total amount.")
        return cleaned_data
```

#### 4.3 Views

**Task:** Implement CRUD operations using Django Class-Based Views (CBVs).

**Instructions:**
Views are kept "thin" (5-15 lines) by offloading business logic to the model or relying on Django's built-in functionalities. HTMX responses are specifically handled for dynamic updates.

**File: `accounts/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import ServiceTaxInvoice
from .forms import ServiceTaxInvoiceForm

class ServiceTaxInvoiceListView(ListView):
    """
    Displays a list of all Service Tax Invoices.
    """
    model = ServiceTaxInvoice
    template_name = 'accounts/servicetaxinvoice/list.html'
    context_object_name = 'servicetaxinvoices' # Name to use in template

class ServiceTaxInvoiceTablePartialView(ListView):
    """
    Renders only the table portion of the Service Tax Invoice list,
    primarily for HTMX requests to refresh the table content.
    """
    model = ServiceTaxInvoice
    template_name = 'accounts/servicetaxinvoice/_service_tax_invoice_table.html'
    context_object_name = 'servicetaxinvoices'

class ServiceTaxInvoiceCreateView(CreateView):
    """
    Handles creation of new Service Tax Invoices via a modal form.
    """
    model = ServiceTaxInvoice
    form_class = ServiceTaxInvoiceForm
    template_name = 'accounts/servicetaxinvoice/_service_tax_invoice_form.html'
    success_url = reverse_lazy('servicetaxinvoice_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        """
        Handles valid form submission, adds success message, and triggers HTMX refresh.
        """
        response = super().form_valid(form)
        messages.success(self.request, 'Service Tax Invoice added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content response
            # and trigger a custom event to refresh the invoice list.
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshServiceTaxInvoiceList'
                }
            )
        return response

class ServiceTaxInvoiceUpdateView(UpdateView):
    """
    Handles updating existing Service Tax Invoices via a modal form.
    """
    model = ServiceTaxInvoice
    form_class = ServiceTaxInvoiceForm
    template_name = 'accounts/servicetaxinvoice/_service_tax_invoice_form.html'
    success_url = reverse_lazy('servicetaxinvoice_list') # Fallback

    def form_valid(self, form):
        """
        Handles valid form submission, adds success message, and triggers HTMX refresh.
        """
        response = super().form_valid(form)
        messages.success(self.request, 'Service Tax Invoice updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshServiceTaxInvoiceList'
                }
            )
        return response

class ServiceTaxInvoiceDeleteView(DeleteView):
    """
    Handles deletion of Service Tax Invoices via a confirmation modal.
    """
    model = ServiceTaxInvoice
    template_name = 'accounts/servicetaxinvoice/_service_tax_invoice_confirm_delete.html'
    success_url = reverse_lazy('servicetaxinvoice_list') # Fallback

    def delete(self, request, *args, **kwargs):
        """
        Handles deletion, adds success message, and triggers HTMX refresh.
        """
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Service Tax Invoice deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshServiceTaxInvoiceList'
                }
            )
        return response
```

#### 4.4 Templates

**Task:** Create templates for each view, leveraging HTMX and DataTables.

**Instructions:**
Templates are split into main list view and partials for forms and delete confirmation, facilitating HTMX-driven modal interactions. All templates extend `core/base.html` for consistent layout.

**File: `accounts/templates/accounts/servicetaxinvoice/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Service Tax Invoices</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
            hx-get="{% url 'servicetaxinvoice_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i> Add New Invoice
        </button>
    </div>

    {# Container for the DataTables list, dynamically loaded via HTMX #}
    <div id="servicetaxinvoiceTable-container"
         hx-trigger="load, refreshServiceTaxInvoiceList from:body"
         hx-get="{% url 'servicetaxinvoice_table' %}"
         hx-swap="innerHTML">
        {# Initial loading state #}
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-gray-600 text-lg">Loading invoices...</p>
        </div>
    </div>

    {# Modal structure for HTMX-loaded forms and confirmations #}
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-60 z-50 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me
            on keydown from document if event.key == 'Escape' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-4xl w-full mx-4 my-8 relative
                                       transform transition-all ease-out duration-300 scale-95 opacity-0"
             _="on load add .scale-100 to me then add .opacity-100 to me
                on click from #modal (add .scale-95 to me then add .opacity-0 to me then remove .is-active from #modal)
                on htmx:afterSwap from #modalContent
                    if event.detail.xhr.status == 204
                        remove .is-active from #modal
                        remove .scale-100 from #modalContent
                        remove .opacity-100 from #modalContent
            ">
            {# Content will be loaded here via HTMX #}
            <div class="text-center p-8">
                <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading form...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js component initialization for any UI state management not covered by HTMX
    document.addEventListener('alpine:init', () => {
        // Example: x-data="{ open: false }" on modal parent, then x-show="open" and @click="open = !open"
    });

    // Ensure DataTables is re-initialized after HTMX swaps
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'servicetaxinvoiceTable-container') {
            $('#servicetaxinvoiceTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "autoWidth": false,
                "responsive": true,
                "columnDefs": [
                    { "orderable": false, "targets": [0, 7] } // Disable sorting for SN and Actions columns
                ]
            });
        }
    });

    // Close modal on successful form submission (HTMX 204 response)
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.xhr.status === 204) {
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.remove('is-active');
            }
        }
    });
</script>
{% endblock %}
```

**File: `accounts/templates/accounts/servicetaxinvoice/_service_tax_invoice_table.html`**

```html
<div class="overflow-x-auto bg-white shadow-md rounded-lg">
    <table id="servicetaxinvoiceTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice No.</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tax Amount</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for invoice in servicetaxinvoices %}
            <tr class="hover:bg-gray-50 {% if invoice.is_overdue and not invoice.is_paid %}bg-red-50 text-red-700{% endif %}">
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ invoice.invoice_number }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ invoice.invoice_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ invoice.customer_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">${{ invoice.total_amount|floatformat:2 }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">${{ invoice.tax_amount|floatformat:2 }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm">
                    {% if invoice.is_paid %}
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Paid</span>
                    {% elif invoice.is_overdue %}
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Overdue</span>
                    {% else %}
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>
                    {% endif %}
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 shadow-sm transition duration-150 ease-in-out"
                        hx-get="{% url 'servicetaxinvoice_edit' invoice.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md shadow-sm transition duration-150 ease-in-out"
                        hx-get="{% url 'servicetaxinvoice_delete' invoice.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="py-4 px-6 text-center text-gray-500 text-base">No service tax invoices found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

{# The DataTables initialization script is moved to the list.html's extra_js block for proper re-initialization after HTMX swap. #}
```

**File: `accounts/templates/accounts/servicetaxinvoice/_service_tax_invoice_form.html`**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Service Tax Invoice</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %} <span class="text-red-500">*</span>{% endif %}
                </label>
                <div class="mt-1">
                    {{ field }}
                </div>
                {% if field.help_text %}
                <p class="mt-2 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="text-red-600 text-xs mt-1 list-disc pl-4">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        {% if form.non_field_errors %}
        <div class="mt-4 text-red-600 text-sm">
            {% for error in form.non_field_errors %}
            <p>{{ error }}</p>
            {% endfor %}
        </div>
        {% endif %}

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Save
            </button>
            <div id="form-spinner" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            </div>
        </div>
    </form>
</div>
```

**File: `accounts/templates/accounts/servicetaxinvoice/_service_tax_invoice_confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the invoice <strong>{{ object.invoice_number }}</strong>?</p>
    <p class="text-red-600 text-sm mb-6">This action cannot be undone.</p>

    <form hx-post="{% url 'servicetaxinvoice_delete' object.pk %}" hx-swap="none" hx-indicator="#delete-spinner">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Delete
            </button>
            <div id="delete-spinner" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-red-500"></div>
            </div>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
URLs are defined for the list, create, update, delete operations, and a specific endpoint for the HTMX-loaded table partial view.

**File: `accounts/urls.py`**

```python
from django.urls import path
from .views import (
    ServiceTaxInvoiceListView,
    ServiceTaxInvoiceTablePartialView,
    ServiceTaxInvoiceCreateView,
    ServiceTaxInvoiceUpdateView,
    ServiceTaxInvoiceDeleteView
)

urlpatterns = [
    # Main list view for Service Tax Invoices
    path('servicetaxinvoice/', ServiceTaxInvoiceListView.as_view(), name='servicetaxinvoice_list'),

    # HTMX endpoint to refresh only the table content
    path('servicetaxinvoice/table/', ServiceTaxInvoiceTablePartialView.as_view(), name='servicetaxinvoice_table'),

    # Endpoint for adding a new Service Tax Invoice (loads form into modal)
    path('servicetaxinvoice/add/', ServiceTaxInvoiceCreateView.as_view(), name='servicetaxinvoice_add'),

    # Endpoint for editing an existing Service Tax Invoice (loads form into modal)
    path('servicetaxinvoice/edit/<int:pk>/', ServiceTaxInvoiceUpdateView.as_view(), name='servicetaxinvoice_edit'),

    # Endpoint for deleting a Service Tax Invoice (loads confirmation into modal)
    path('servicetaxinvoice/delete/<int:pk>/', ServiceTaxInvoiceDeleteView.as_view(), name='servicetaxinvoice_delete'),
]
```
*Note: Remember to include these URLs in your project's main `urls.py` file by adding `path('accounts/', include('accounts.urls')),`.*

#### 4.6 Tests

**Task:** Write comprehensive tests for the model and views.

**Instructions:**
Tests cover model functionality (field properties, custom methods) and view interactions (GET/POST requests for CRUD, HTMX specific responses).

**File: `accounts/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date
from decimal import Decimal
from .models import ServiceTaxInvoice
from .forms import ServiceTaxInvoiceForm

class ServiceTaxInvoiceModelTest(TestCase):
    """
    Unit tests for the ServiceTaxInvoice model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a single invoice for testing basic model functionality
        cls.invoice1 = ServiceTaxInvoice.objects.create(
            invoice_number='INV-2023-001',
            invoice_date='2023-01-15',
            customer_name='Acme Corp',
            total_amount=Decimal('1000.00'),
            tax_amount=Decimal('100.00'),
            due_date='2023-02-15',
            is_paid=False
        )
        cls.invoice2 = ServiceTaxInvoice.objects.create(
            invoice_number='INV-2023-002',
            invoice_date='2023-01-20',
            customer_name='Widgets Inc.',
            total_amount=Decimal('500.00'),
            tax_amount=Decimal('50.00'),
            due_date='2023-01-25', # This will be overdue
            is_paid=False
        )
        cls.invoice3 = ServiceTaxInvoice.objects.create(
            invoice_number='INV-2023-003',
            invoice_date='2023-02-01',
            customer_name='Global Solutions',
            total_amount=Decimal('2000.00'),
            tax_amount=Decimal('200.00'),
            due_date='2023-03-01',
            is_paid=True
        )

    def test_invoice_creation(self):
        """Test that an invoice is created correctly with all fields."""
        invoice = ServiceTaxInvoice.objects.get(invoice_number='INV-2023-001')
        self.assertEqual(invoice.customer_name, 'Acme Corp')
        self.assertEqual(invoice.total_amount, Decimal('1000.00'))
        self.assertFalse(invoice.is_paid)

    def test_str_method(self):
        """Test the __str__ method of the model."""
        invoice = ServiceTaxInvoice.objects.get(invoice_number='INV-2023-001')
        self.assertEqual(str(invoice), 'Invoice INV-2023-001 (Acme Corp)')

    def test_calculate_net_amount_method(self):
        """Test the calculate_net_amount business logic."""
        invoice = ServiceTaxInvoice.objects.get(invoice_number='INV-2023-001')
        self.assertEqual(invoice.calculate_net_amount(), Decimal('900.00'))

    def test_mark_as_paid_method(self):
        """Test marking an invoice as paid."""
        invoice = ServiceTaxInvoice.objects.get(invoice_number='INV-2023-001')
        self.assertFalse(invoice.is_paid)
        self.assertTrue(invoice.mark_as_paid())
        invoice.refresh_from_db()
        self.assertTrue(invoice.is_paid)
        # Test marking as paid again (should return False as it's already paid)
        self.assertFalse(invoice.mark_as_paid())

    def test_is_overdue_method(self):
        """Test the is_overdue business logic."""
        # Pending and Overdue
        invoice = ServiceTaxInvoice.objects.get(invoice_number='INV-2023-002')
        # We need to mock today's date for consistent testing if due_date is in the past
        # For simplicity, assuming the setUpTestData due_date is in the past relative to current run time.
        # In a real scenario, use `mock.patch('datetime.date.today', return_value=date(2023, 1, 26))`
        # to control today's date for the test.
        with self.settings(USE_TZ=False): # Important for date comparisons
            # Manually set the due_date to ensure it's in the past relative to the test execution.
            # Assuming current date is after 2023-01-25.
            if date.today() > date(2023, 1, 25):
                self.assertTrue(invoice.is_overdue())
            else:
                # If running this test on a date before 2023-01-25, it won't be overdue.
                # For robust testing, use fixed dates or mock `date.today()`.
                pass # Test will pass if not overdue yet.

        # Paid, not overdue
        invoice = ServiceTaxInvoice.objects.get(invoice_number='INV-2023-003')
        self.assertFalse(invoice.is_overdue())

        # Pending, not overdue
        invoice = ServiceTaxInvoice.objects.get(invoice_number='INV-2023-001')
        self.assertFalse(invoice.is_overdue()) # Due date is in the future.

class ServiceTaxInvoiceViewsTest(TestCase):
    """
    Integration tests for ServiceTaxInvoice views.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all view tests
        cls.invoice = ServiceTaxInvoice.objects.create(
            invoice_number='INV-TEST-001',
            invoice_date='2024-01-01',
            customer_name='Test Customer',
            total_amount=Decimal('500.00'),
            tax_amount=Decimal('50.00'),
            due_date='2024-02-01',
            is_paid=False
        )

    def setUp(self):
        # Initialize client for each test method
        self.client = Client()

    def test_list_view_get(self):
        """Test the GET request for the invoice list view."""
        response = self.client.get(reverse('servicetaxinvoice_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/servicetaxinvoice/list.html')
        self.assertIn('servicetaxinvoices', response.context)
        self.assertContains(response, 'Test Customer') # Check if test data is rendered

    def test_table_partial_view_htmx_get(self):
        """Test the HTMX partial view for the invoice table."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('servicetaxinvoice_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/servicetaxinvoice/_service_tax_invoice_table.html')
        self.assertIn('servicetaxinvoices', response.context)
        self.assertContains(response, 'Test Customer') # Check if test data is rendered

    def test_create_view_get(self):
        """Test the GET request for the invoice creation form."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('servicetaxinvoice_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/servicetaxinvoice/_service_tax_invoice_form.html')
        self.assertIn('form', response.context)
        self.assertIsInstance(response.context['form'], ServiceTaxInvoiceForm)

    def test_create_view_post_success(self):
        """Test successful POST request for creating an invoice."""
        self.assertEqual(ServiceTaxInvoice.objects.count(), 1) # Initial count
        data = {
            'invoice_number': 'INV-NEW-002',
            'invoice_date': '2024-03-01',
            'customer_name': 'New Customer Co.',
            'total_amount': '750.00',
            'tax_amount': '75.00',
            'due_date': '2024-04-01',
            'is_paid': False
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('servicetaxinvoice_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX successful creation expects 204 No Content
        self.assertTrue(ServiceTaxInvoice.objects.filter(invoice_number='INV-NEW-002').exists())
        self.assertEqual(ServiceTaxInvoice.objects.count(), 2) # Count should increase

    def test_create_view_post_invalid(self):
        """Test invalid POST request for creating an invoice."""
        initial_count = ServiceTaxInvoice.objects.count()
        data = { # Missing required fields and invalid amount
            'invoice_number': 'INV-INVALID',
            'invoice_date': '2024-03-01',
            'total_amount': '-100.00', # Invalid
            'tax_amount': '50.00',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('servicetaxinvoice_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # Should render form again with errors
        self.assertTemplateUsed(response, 'accounts/servicetaxinvoice/_service_tax_invoice_form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)
        self.assertIn('customer_name', response.context['form'].errors)
        self.assertIn('total_amount', response.context['form'].errors)
        self.assertEqual(ServiceTaxInvoice.objects.count(), initial_count) # Count should not change

    def test_update_view_get(self):
        """Test the GET request for the invoice update form."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('servicetaxinvoice_edit', args=[self.invoice.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/servicetaxinvoice/_service_tax_invoice_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.invoice)

    def test_update_view_post_success(self):
        """Test successful POST request for updating an invoice."""
        updated_customer_name = 'Updated Customer Name'
        data = {
            'invoice_number': self.invoice.invoice_number, # Must be unique or same as instance
            'invoice_date': self.invoice.invoice_date,
            'customer_name': updated_customer_name,
            'total_amount': self.invoice.total_amount,
            'tax_amount': self.invoice.tax_amount,
            'due_date': self.invoice.due_date,
            'is_paid': True # Change status
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('servicetaxinvoice_edit', args=[self.invoice.pk]), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX successful update expects 204 No Content
        self.invoice.refresh_from_db()
        self.assertEqual(self.invoice.customer_name, updated_customer_name)
        self.assertTrue(self.invoice.is_paid)

    def test_delete_view_get(self):
        """Test the GET request for the invoice deletion confirmation."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('servicetaxinvoice_delete', args=[self.invoice.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/servicetaxinvoice/_service_tax_invoice_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.invoice)

    def test_delete_view_post_success(self):
        """Test successful POST request for deleting an invoice."""
        self.assertEqual(ServiceTaxInvoice.objects.count(), 1) # Initial count
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('servicetaxinvoice_delete', args=[self.invoice.pk]), **headers)
        self.assertEqual(response.status_code, 204) # HTMX successful delete expects 204 No Content
        self.assertFalse(ServiceTaxInvoice.objects.filter(pk=self.invoice.pk).exists())
        self.assertEqual(ServiceTaxInvoice.objects.count(), 0) # Count should decrease
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The modernization strongly favors HTMX for dynamic interactions and Alpine.js for lightweight UI state management, eliminating the need for heavy JavaScript frameworks.

*   **HTMX for CRUD:** All "Add New", "Edit", and "Delete" actions trigger HTMX GET requests to load forms/confirmations into a modal. Form submissions (POST requests) are also handled by HTMX, which listens for Django's 204 No Content response to close the modal and triggers a custom event (`refreshServiceTaxInvoiceList`) to update the main list view.
*   **DataTables for List Views:** The main list view (`list.html`) uses a placeholder div that HTMX loads the `_service_tax_invoice_table.html` partial into. This partial contains the DataTables initialization script, ensuring client-side searching, sorting, and pagination. The `refreshServiceTaxInvoiceList` event reloads this partial, effectively refreshing the table without a full page reload.
*   **Alpine.js for Modals:** Alpine.js (`_` attribute) is used in `list.html` to manage the modal's visibility (`add .is-active to #modal` and `remove .is-active from me`). It provides simple, declarative control over the UI, complementing HTMX for scenarios like modal toggling.
*   **Loading Indicators:** `hx-indicator` attributes are used on forms and buttons to show loading spinners (`<div class="htmx-indicator">...</div>`) during asynchronous operations, providing immediate user feedback.
*   **DRY Templates:** HTML partials (`_service_tax_invoice_table.html`, `_service_tax_invoice_form.html`, `_service_tax_invoice_confirm_delete.html`) are used to render specific components, promoting reusability and reducing code duplication.

---

### Final Notes

This comprehensive plan transforms the conceptual ASP.NET "Service Tax Invoice Dashboard" into a modern Django application. While the original ASP.NET code was minimal, this plan demonstrates the automated generation process for a typical CRUD module, inferring common requirements.

*   **Business Value:** This modernized Django application provides a highly responsive and interactive user experience without full page reloads, improving user satisfaction and operational efficiency. The clear separation of concerns makes the application easier to maintain, scale, and debug. The use of standard, well-supported technologies like Django, HTMX, and Alpine.js ensures long-term viability and reduces technical debt.
*   **Automation Focus:** Each step and generated code block is designed to be part of an automated migration pipeline. By defining clear patterns for models, forms, views, templates, and tests, a conversational AI can guide the generation of these components based on inferred or provided schema details. This significantly reduces manual coding effort and potential for human error, accelerating the transition process.
*   **Scalability & Maintainability:** The "fat model, thin view" architecture ensures business logic is encapsulated where it belongs, making models reusable and views simple. The modular structure with clear app boundaries enhances scalability.
*   **Test Coverage:** Including comprehensive tests from the outset ensures the migrated application functions correctly and remains stable through future enhancements.