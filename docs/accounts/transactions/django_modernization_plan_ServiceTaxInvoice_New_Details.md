The ASP.NET application you've provided is a classic example of a complex, multi-page web form. It handles the creation of a "Service Tax Invoice" with multiple sections: general invoice details, buyer information, consignee information, details of goods/items, and taxation. The C# code-behind reveals intricate business logic for calculating remaining quantities, handling customer lookups, cascading dropdowns, and robust validation before saving.

Migrating this to Django will involve breaking down this monolithic ASP.NET page into logical Django components, leveraging Django's ORM, Class-Based Views (CBVs), and modern frontend tooling like HTMX and Alpine.js to replicate the dynamic, tabbed user experience. We will strictly adhere to the "Fat Model, Thin View" principle, moving all complex business logic into the Django models.

## ASP.NET to Django Conversion Script: Service Tax Invoice

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code interacts with several database tables. The core functionality revolves around `tblACC_ServiceTaxInvoice_Master` (for main invoice details) and `tblACC_ServiceTaxInvoice_Details` (for line items). Other tables like `SD_Cust_master`, `SD_Cust_PO_Master`, `SD_Cust_PO_Details`, `Unit_Master`, `Country_Master`, `State_Master`, `City_Master`, `tblACC_Service_Category`, `tblACC_TaxableServices`, and `tblExciseser_Master` are used for lookups, dropdowns, and pre-populating data.

**Inferred Table Names and Key Columns for Migration:**

*   **`tblACC_ServiceTaxInvoice_Master` (Django Model: `ServiceTaxInvoice`)**
    *   **Fields:** `Id` (PK), `SysDate`, `SysTime`, `CompId`, `FinYearId`, `SessionId`, `InvoiceNo`, `POId`, `PONo`, `WONo`, `DateOfIssueInvoice`, `TimeOfIssueInvoice`, `DutyRate`, `CustomerCode`, `CustomerCategory`, `Buyer_name`, `Buyer_add`, `Buyer_country`, `Buyer_state`, `Buyer_city`, `Buyer_cotper` (contact person), `Buyer_ph` (phone), `Buyer_email`, `Buyer_ecc`, `Buyer_tin` (CST No.), `Buyer_mob` (mobile), `Buyer_fax`, `Buyer_vat` (VAT No.), `Cong_name` (consignee name), `Cong_add`, `Cong_Country`, `Cong_state`, `Cong_city`, `Cong_cotper`, `Cong_ph`, `Cong_email`, `Cong_ecc`, `Cong_tin`, `Cong_mob`, `Cong_fax`, `Cong_vat`, `AddType` (addition type, e.g., '0' for Amount, '1' for Percentage), `AddAmt`, `DeductionType`, `Deduction`, `ServiceTax`, `TaxableServices`.
    *   **Relationships:** Foreign Keys to `SD_Cust_master`, `tblACC_Service_Category`, `tblACC_TaxableServices`, `tblExciseser_Master`, `Country_Master`, `State_Master`, `City_Master`.

*   **`tblACC_ServiceTaxInvoice_Details` (Django Model: `ServiceTaxInvoiceItem`)**
    *   **Fields:** `Id` (PK), `MId` (FK to `tblACC_ServiceTaxInvoice_Master`), `InvoiceNo` (redundant), `ItemId` (FK to `SD_Cust_PO_Details`), `Unit` (FK to `Unit_Master`), `Qty` (Total Quantity from PO), `ReqQty` (Requested Quantity for this invoice), `AmtInPer` (Amount in Percentage), `Rate`.

*   **Auxiliary Models (for lookups and related data):**
    *   `Country_Master` (Id, Name)
    *   `State_Master` (Id, Name, CId)
    *   `City_Master` (Id, Name, SId)
    *   `SD_Cust_master` (CustomerId, CustomerName, MaterialDelAddress, etc.)
    *   `tblACC_Service_Category` (Id, Description)
    *   `tblACC_TaxableServices` (Id, Description)
    *   `tblExciseser_Master` (Id, Terms, LiveSerTax)
    *   `Unit_Master` (Id, Symbol)
    *   `SD_Cust_PO_Master` (POId, PONo, CompId)
    *   `SD_Cust_PO_Details` (Id, POId, ItemDesc, TotalQty, Unit, Rate)

### Step 2: Identify Backend Functionality

**Core Business Processes:**
*   **Invoice Creation:** The primary function is to capture all details across multiple tabs and save them as a new Service Tax Invoice with associated line items.
*   **Invoice Number Generation:** Automatic sequential invoice number generation based on company and financial year.
*   **Data Pre-population:** Initial loading of invoice date, PO details (PO No, Date, WO No) from URL query parameters.
*   **Customer Management (Buyer/Consignee):**
    *   Initial population of Buyer details based on a customer ID passed in the query string.
    *   Autocomplete search for existing customers (both Buyer and Consignee).
    *   Fetching and populating detailed customer address and contact information upon selection/search.
    *   Functionality to copy Buyer details to Consignee.
*   **Cascading Dropdowns (Location):** Dynamic updates for State dropdown based on selected Country, and City based on selected State.
*   **Goods/Items Management:**
    *   Displaying line items from a Purchase Order (PO) in a grid format.
    *   Crucial logic to calculate `RemainingQty` for each PO item by deducting quantities already invoiced.
    *   Allowing users to select items, input `ReqQty` (Required Quantity) and `AmtInPer` (Amount in Percentage).
    *   Validation to ensure `ReqQty` does not exceed `RemainingQty`.
*   **Validation:** Extensive server-side validation for mandatory fields, data formats (numeric, email, date), and business rules (quantity checks).
*   **Tabbed Navigation:** User-friendly navigation through different sections of the invoice form.

### Step 3: Infer UI Components

**Django/Frontend Technologies Mapping:**
*   **Django Forms:** A `ModelForm` for `ServiceTaxInvoice` will handle most fields, and a `ModelFormSet` for `ServiceTaxInvoiceItem` will manage the dynamic "Goods" grid rows.
*   **Django Class-Based Views (CBVs):**
    *   `ServiceTaxInvoiceCreateView`: For handling the entire form submission.
    *   `ServiceTaxInvoiceItemListView`: For rendering the "Goods" table (partial view for HTMX).
    *   Auxiliary views/APIs for customer autocomplete, customer detail fetching, and cascading location dropdowns.
*   **Templates:**
    *   `invoices/servicetaxinvoice/invoice_form.html`: The main form template, extending `core/base.html`. This will host the multi-tab interface using Alpine.js.
    *   `invoices/servicetaxinvoice/_goods_table_partial.html`: A partial template to render the "Goods" `DataTables` grid, loaded dynamically via HTMX.
*   **HTMX:**
    *   Powering the dynamic search for customers (autocomplete and detail lookup).
    *   Handling cascading dropdowns for Country/State/City.
    *   Loading the "Goods" table dynamically.
    *   Submitting form parts or the entire form without full page reloads, using `hx-post`, `hx-get`, `hx-swap`, `hx-trigger`.
    *   Refreshing the "Goods" table after invoice item submission/validation.
*   **Alpine.js:**
    *   Managing the state of the tabbed interface (which tab is active).
    *   Showing/hiding content sections based on the active tab.
    *   Handling client-side UI interactions like showing modals for form submission or confirmation.
    *   Managing the `DataTables` initialization and any required client-side data manipulation or validation feedback.
*   **DataTables:**
    *   Used for the "Goods" list (`GridView`) to provide client-side search, sorting, and pagination for an enhanced user experience.
*   **Tailwind CSS:** For consistent and rapid styling across all components.

---

### Step 4: Generate Django Code

**Assumed Django App Name: `invoices`**

#### 4.1 Models (`invoices/models.py`)

These models reflect the database schema, with `managed = False` indicating they map to existing tables. Business logic like invoice number generation and quantity validation is moved into the `ServiceTaxInvoice` model.

```python
from django.db import models, connection
from django.utils import timezone
from datetime import time as dt_time
import re # For extracting customer ID from string

# --- Auxiliary Models (Managed=False, representing existing tables) ---

class CountryMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Country_Master'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.name or ''

class StateMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=255, blank=True, null=True)
    country = models.ForeignKey(CountryMaster, models.DO_NOTHING, db_column='CId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'State_Master'
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.name or ''

class CityMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=255, blank=True, null=True)
    state = models.ForeignKey(StateMaster, models.DO_NOTHING, db_column='SId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'City_Master'
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.name or ''

class CustomerMaster(models.Model):
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50) # Assuming CustomerId is unique
    customer_name = models.CharField(db_column='CustomerName', max_length=255, blank=True, null=True)
    material_del_address = models.TextField(db_column='MaterialDelAddress', blank=True, null=True)
    material_del_country = models.ForeignKey(CountryMaster, models.DO_NOTHING, db_column='MaterialDelCountry', blank=True, null=True, related_name='customer_delivery_country')
    material_del_state = models.ForeignKey(StateMaster, models.DO_NOTHING, db_column='MaterialDelState', blank=True, null=True, related_name='customer_delivery_state')
    material_del_city = models.ForeignKey(CityMaster, models.DO_NOTHING, db_column='MaterialDelCity', blank=True, null=True, related_name='customer_delivery_city')
    material_del_contact_no = models.CharField(db_column='MaterialDelContactNo', max_length=50, blank=True, null=True)
    material_del_fax_no = models.CharField(db_column='MaterialDelFaxNo', max_length=50, blank=True, null=True)
    contact_person = models.CharField(db_column='ContactPerson', max_length=255, blank=True, null=True)
    email = models.CharField(db_column='Email', max_length=255, blank=True, null=True)
    tin_vat_no = models.CharField(db_column='TinVatNo', max_length=50, blank=True, null=True)
    ecc_no = models.CharField(db_column='EccNo', max_length=50, blank=True, null=True)
    contact_no = models.CharField(db_column='ContactNo', max_length=50, blank=True, null=True) # Mobile No
    tin_cst_no = models.CharField(db_column='TinCstNo', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True) # Company ID

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customer_name} [{self.customer_id}]"
    
    @staticmethod
    def get_customer_id_from_name_string(customer_name_str):
        """Extracts customer ID from a string like 'CustomerName [CustomerId]'."""
        match = re.search(r'\[(.*?)\]$', customer_name_str)
        if match:
            return match.group(1)
        return None

class ServiceCategory(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_Service_Category'
        verbose_name = 'Service Category'
        verbose_name_plural = 'Service Categories'

    def __str__(self):
        return self.description or ''

class TaxableService(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_TaxableServices'
        verbose_name = 'Taxable Service'
        verbose_name_plural = 'Taxable Services'

    def __str__(self):
        return self.description or ''

class ExciseServiceMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255, blank=True, null=True)
    live_ser_tax = models.BooleanField(db_column='LiveSerTax', default=False) # Assuming 1/0 maps to boolean

    class Meta:
        managed = False
        db_table = 'tblExciseser_Master'
        verbose_name = 'Excise Service'
        verbose_name_plural = 'Excise Services'

    def __str__(self):
        return self.terms or ''

class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol or ''

class PurchaseOrderMaster(models.Model):
    po_id = models.IntegerField(db_column='POId', primary_key=True) # Assuming this is unique ID for PO
    po_no = models.CharField(db_column='PONo', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    # Add other fields from SD_Cust_PO_Master if needed

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Master'
        verbose_name = 'Purchase Order Master'
        verbose_name_plural = 'Purchase Order Masters'

    def __str__(self):
        return self.po_no or ''

class PurchaseOrderDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # This is the ItemId
    po_master = models.ForeignKey(PurchaseOrderMaster, models.DO_NOTHING, db_column='POId', blank=True, null=True)
    item_desc = models.CharField(db_column='ItemDesc', max_length=255, blank=True, null=True)
    total_qty = models.FloatField(db_column='TotalQty', blank=True, null=True)
    unit = models.ForeignKey(UnitMaster, models.DO_NOTHING, db_column='Unit', blank=True, null=True)
    rate = models.FloatField(db_column='Rate', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Details'
        verbose_name = 'Purchase Order Detail'
        verbose_name_plural = 'Purchase Order Details'

    def __str__(self):
        return self.item_desc or ''


# --- Main Service Tax Invoice Models ---

class ServiceTaxInvoice(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate', default=timezone.now)
    sys_time = models.TimeField(db_column='SysTime', default=dt_time(0, 0, 0)) # Default, will be updated to actual time
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=255, blank=True, null=True)
    invoice_no = models.CharField(db_column='InvoiceNo', max_length=50, unique=True, null=True, blank=True) # Populated by business logic
    po_id = models.IntegerField(db_column='POId', blank=True, null=True) # Storing ID, can be FK if mapped
    po_no = models.CharField(db_column='PONo', max_length=255, blank=True, null=True)
    wo_no = models.CharField(db_column='WONo', max_length=255, blank=True, null=True) # Comma separated string
    date_of_issue_invoice = models.DateField(db_column='DateOfIssueInvoice', blank=True, null=True)
    time_of_issue_invoice = models.TimeField(db_column='TimeOfIssueInvoice', blank=True, null=True)
    duty_rate = models.FloatField(db_column='DutyRate', blank=True, null=True)
    customer_code = models.CharField(db_column='CustomerCode', max_length=50, blank=True, null=True) # Stores ID, can be FK
    customer_category = models.ForeignKey(ServiceCategory, models.DO_NOTHING, db_column='CustomerCategory', blank=True, null=True)
    buyer_name = models.CharField(db_column='Buyer_name', max_length=255, blank=True, null=True)
    buyer_address = models.TextField(db_column='Buyer_add', blank=True, null=True)
    buyer_country = models.ForeignKey(CountryMaster, models.DO_NOTHING, db_column='Buyer_country', blank=True, null=True, related_name='buyer_country')
    buyer_state = models.ForeignKey(StateMaster, models.DO_NOTHING, db_column='Buyer_state', blank=True, null=True, related_name='buyer_state')
    buyer_city = models.ForeignKey(CityMaster, models.DO_NOTHING, db_column='Buyer_city', blank=True, null=True, related_name='buyer_city')
    buyer_contact_person = models.CharField(db_column='Buyer_cotper', max_length=255, blank=True, null=True)
    buyer_phone = models.CharField(db_column='Buyer_ph', max_length=50, blank=True, null=True)
    buyer_email = models.CharField(db_column='Buyer_email', max_length=255, blank=True, null=True)
    buyer_ecc = models.CharField(db_column='Buyer_ecc', max_length=50, blank=True, null=True)
    buyer_tin = models.CharField(db_column='Buyer_tin', max_length=50, blank=True, null=True) # TIN/CST No
    buyer_mobile = models.CharField(db_column='Buyer_mob', max_length=50, blank=True, null=True)
    buyer_fax = models.CharField(db_column='Buyer_fax', max_length=50, blank=True, null=True)
    buyer_vat = models.CharField(db_column='Buyer_vat', max_length=50, blank=True, null=True) # TIN/VAT No
    consignee_name = models.CharField(db_column='Cong_name', max_length=255, blank=True, null=True)
    consignee_address = models.TextField(db_column='Cong_add', blank=True, null=True)
    consignee_country = models.ForeignKey(CountryMaster, models.DO_NOTHING, db_column='Cong_Country', blank=True, null=True, related_name='consignee_country')
    consignee_state = models.ForeignKey(StateMaster, models.DO_NOTHING, db_column='Cong_state', blank=True, null=True, related_name='consignee_state')
    consignee_city = models.ForeignKey(CityMaster, models.DO_NOTHING, db_column='Cong_city', blank=True, null=True, related_name='consignee_city')
    consignee_contact_person = models.CharField(db_column='Cong_cotper', max_length=255, blank=True, null=True)
    consignee_phone = models.CharField(db_column='Cong_ph', max_length=50, blank=True, null=True)
    consignee_email = models.CharField(db_column='Cong_email', max_length=255, blank=True, null=True)
    consignee_ecc = models.CharField(db_column='Cong_ecc', max_length=50, blank=True, null=True)
    consignee_tin = models.CharField(db_column='Cong_tin', max_length=50, blank=True, null=True) # TIN/CST No
    consignee_mobile = models.CharField(db_column='Cong_mob', max_length=50, blank=True, null=True)
    consignee_fax = models.CharField(db_column='Cong_fax', max_length=50, blank=True, null=True)
    consignee_vat = models.CharField(db_column='Cong_vat', max_length=50, blank=True, null=True) # TIN/VAT No
    add_type = models.CharField(db_column='AddType', max_length=10, blank=True, null=True) # '0' for Amt(Rs), '1' for Per(%)
    add_amount = models.FloatField(db_column='AddAmt', blank=True, null=True)
    deduction_type = models.CharField(db_column='DeductionType', max_length=10, blank=True, null=True) # '0' for Amt(Rs), '1' for Per(%)
    deduction_amount = models.FloatField(db_column='Deduction', blank=True, null=True)
    service_tax = models.ForeignKey(ExciseServiceMaster, models.DO_NOTHING, db_column='ServiceTax', blank=True, null=True)
    taxable_services = models.ForeignKey(TaxableService, models.DO_NOTHING, db_column='TaxableServices', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_ServiceTaxInvoice_Master'
        verbose_name = 'Service Tax Invoice'
        verbose_name_plural = 'Service Tax Invoices'

    def __str__(self):
        return self.invoice_no or ''

    @classmethod
    def get_next_invoice_no(cls, comp_id, fin_year_id):
        """
        Business logic to generate the next invoice number (e.g., "0001", "0002").
        This method replaces the C# code's `InvNo` generation logic.
        """
        try:
            # For SQL Server, order by string might not be numerical, convert to int for comparison
            # Using raw SQL to ensure correct numerical sorting if InvoiceNo is VARCHAR
            with connection.cursor() as cursor:
                cursor.execute(f"""
                    SELECT TOP 1 InvoiceNo
                    FROM tblACC_ServiceTaxInvoice_Master
                    WHERE CompId = %s AND FinYearId = %s
                    ORDER BY CAST(InvoiceNo AS INT) DESC
                """, [comp_id, fin_year_id])
                row = cursor.fetchone()

            if row and row[0].isdigit():
                next_num = int(row[0]) + 1
                return f"{next_num:04d}" # Format as '0001'
            else:
                return "0001"
        except Exception as e:
            # Log this error in a real application
            print(f"Error generating invoice number: {e}")
            return "0001"

    @classmethod
    def get_po_details_for_invoice(cls, po_id, comp_id):
        """
        Fetches relevant Purchase Order Details for the Goods tab,
        calculating remaining quantities for each item.
        This replaces the complex `fillgrid()` logic in C#.
        """
        po_details = PurchaseOrderDetail.objects.filter(
            po_master__po_id=po_id,
            po_master__comp_id=comp_id
        ).select_related('unit')

        items_for_grid = []
        for po_item in po_details:
            # Calculate already invoiced quantity for this PO item
            total_invoiced_qty = ServiceTaxInvoiceItem.objects.filter(
                invoice_master__po_id=po_id,
                item_id=po_item.id
            ).aggregate(total=models.Sum('req_qty'))['total'] or 0.0

            remaining_qty = po_item.total_qty - total_invoiced_qty

            if remaining_qty > 0:
                items_for_grid.append({
                    'id': po_item.id,
                    'item_desc': po_item.item_desc,
                    'total_qty': po_item.total_qty,
                    'unit_symbol': po_item.unit.symbol if po_item.unit else '',
                    'rate': po_item.rate,
                    'remaining_qty': remaining_qty,
                })
        return items_for_grid
    
    @classmethod
    def validate_goods_quantities(cls, invoice_items_data, po_id, comp_id, current_invoice_id=None):
        """
        Validates requested quantities against remaining PO quantities.
        This replaces the complex 'z' and 'h' logic in `BtnSubmit_Click`.
        `current_invoice_id` is for edit scenarios to exclude current invoice's items from sum.
        """
        for item_data in invoice_items_data:
            item_id = item_data.get('item_id')
            req_qty = float(item_data.get('req_qty', 0)) # Ensure float for comparison

            if not item_id:
                continue

            po_item = PurchaseOrderDetail.objects.filter(id=item_id, po_master__po_id=po_id, po_master__comp_id=comp_id).first()
            if not po_item:
                return False, f"PO Item with ID {item_id} not found."

            invoiced_qty_query = ServiceTaxInvoiceItem.objects.filter(
                invoice_master__po_id=po_id,
                item_id=item_id
            )
            if current_invoice_id: # Exclude items from the current invoice being edited
                invoiced_qty_query = invoiced_qty_query.exclude(invoice_master__id=current_invoice_id)

            total_invoiced_qty = invoiced_qty_query.aggregate(total=models.Sum('req_qty'))['total'] or 0.0

            available_qty = po_item.total_qty - total_invoiced_qty
            
            # ASP.NET code check: ReqQty > 0 and rmnqty >= ReqQty
            if req_qty <= 0:
                return False, f"Required quantity for '{po_item.item_desc}' must be greater than zero."
            if req_qty > available_qty + 1e-6: # Add small epsilon for float comparison safety
                return False, f"Required quantity ({req_qty}) for '{po_item.item_desc}' exceeds remaining available quantity ({available_qty:.3f})."
        return True, "Quantities are valid."


class ServiceTaxInvoiceItem(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    invoice_master = models.ForeignKey(ServiceTaxInvoice, models.DO_NOTHING, db_column='MId', related_name='items')
    invoice_no = models.CharField(db_column='InvoiceNo', max_length=50, blank=True, null=True) # Redundant, but exists in DB
    item_id = models.IntegerField(db_column='ItemId', blank=True, null=True) # Stores ID, can be FK to PurchaseOrderDetail
    unit = models.ForeignKey(UnitMaster, models.DO_NOTHING, db_column='Unit', blank=True, null=True)
    qty = models.FloatField(db_column='Qty', blank=True, null=True) # Total Qty from PO
    req_qty = models.FloatField(db_column='ReqQty', blank=True, null=True)
    amt_in_per = models.FloatField(db_column='AmtInPer', blank=True, null=True)
    rate = models.FloatField(db_column='Rate', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_ServiceTaxInvoice_Details'
        verbose_name = 'Service Tax Invoice Item'
        verbose_name_plural = 'Service Tax Invoice Items'

    def __str__(self):
        return f"Item {self.item_id} for Invoice {self.invoice_master.invoice_no}"

```

#### 4.2 Forms (`invoices/forms.py`)

This section defines the main `ModelForm` for the invoice and a `ModelFormSet` for the invoice items (goods). Custom dropdown choices and validation are included.

```python
from django import forms
from django.forms import inlineformset_factory
from .models import (
    ServiceTaxInvoice, ServiceTaxInvoiceItem,
    CountryMaster, StateMaster, CityMaster,
    ServiceCategory, TaxableService, ExciseServiceMaster, UnitMaster,
    CustomerMaster
)
import re

class DateInput(forms.DateInput):
    input_type = 'date'
    # Adding a placeholder to guide date format
    input_formats = ['%Y-%m-%d', '%d-%m-%Y', '%d/%m/%Y']

class TimeInput(forms.TimeInput):
    input_type = 'time'
    # Adding a placeholder to guide time format
    input_formats = ['%H:%M', '%H:%M:%S', '%I:%M %p']

class ServiceTaxInvoiceForm(forms.ModelForm):
    # Dynamic fields for country/state/city dropdowns
    buyer_country_choices = forms.ModelChoiceField(
        queryset=CountryMaster.objects.all(),
        required=False,
        label="Buyer Country",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm htmx-trigger', 'hx-get': "{% url 'invoices:get_states' %}", 'hx-target': '#id_buyer_state', 'hx-indicator': '.htmx-indicator', 'hx-swap': 'outerHTML'}),
    )
    buyer_state_choices = forms.ModelChoiceField(
        queryset=StateMaster.objects.none(), # Populated dynamically
        required=False,
        label="Buyer State",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm htmx-trigger', 'hx-get': "{% url 'invoices:get_cities' %}", 'hx-target': '#id_buyer_city', 'hx-indicator': '.htmx-indicator', 'hx-swap': 'outerHTML'}),
    )
    buyer_city_choices = forms.ModelChoiceField(
        queryset=CityMaster.objects.none(), # Populated dynamically
        required=False,
        label="Buyer City",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
    )

    consignee_country_choices = forms.ModelChoiceField(
        queryset=CountryMaster.objects.all(),
        required=False,
        label="Consignee Country",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm htmx-trigger', 'hx-get': "{% url 'invoices:get_states' %}", 'hx-target': '#id_consignee_state', 'hx-indicator': '.htmx-indicator', 'hx-swap': 'outerHTML'}),
    )
    consignee_state_choices = forms.ModelChoiceField(
        queryset=StateMaster.objects.none(), # Populated dynamically
        required=False,
        label="Consignee State",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm htmx-trigger', 'hx-get': "{% url 'invoices:get_cities' %}", 'hx-target': '#id_consignee_city', 'hx-indicator': '.htmx-indicator', 'hx-swap': 'outerHTML'}),
    )
    consignee_city_choices = forms.ModelChoiceField(
        queryset=CityMaster.objects.none(), # Populated dynamically
        required=False,
        label="Consignee City",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
    )

    # Autocomplete fields (will be mapped to actual DB fields in save)
    buyer_name_autocomplete = forms.CharField(
        max_length=255, 
        required=True, 
        label="Buyer Name",
        widget=forms.TextInput(attrs={
            'class': 'box3 hx-autocompleted block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': "{% url 'invoices:customer_autocomplete' %}",
            'hx-trigger': "keyup changed delay:500ms, search",
            'hx-target': "#buyer-autocomplete-results",
            'hx-indicator': ".htmx-indicator",
            'hx-swap': "innerHTML",
            'autocomplete': "off",
            'placeholder': "Type to search buyer..."
        })
    )
    consignee_name_autocomplete = forms.CharField(
        max_length=255, 
        required=True, 
        label="Consignee Name",
        widget=forms.TextInput(attrs={
            'class': 'box3 hx-autocompleted block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': "{% url 'invoices:customer_autocomplete' %}",
            'hx-trigger': "keyup changed delay:500ms, search",
            'hx-target': "#consignee-autocomplete-results",
            'hx-indicator': ".htmx-indicator",
            'hx-swap': "innerHTML",
            'autocomplete': "off",
            'placeholder': "Type to search consignee..."
        })
    )

    class Meta:
        model = ServiceTaxInvoice
        fields = [
            'invoice_no', 'duty_rate', 'date_of_issue_invoice', 'time_of_issue_invoice',
            'customer_category', 'taxable_services',
            'buyer_address', 'buyer_contact_person', 'buyer_phone', 'buyer_email', 'buyer_ecc',
            'buyer_tin', 'buyer_mobile', 'buyer_fax', 'buyer_vat',
            'consignee_address', 'consignee_contact_person', 'consignee_phone', 'consignee_email', 'consignee_ecc',
            'consignee_tin', 'consignee_mobile', 'consignee_fax', 'consignee_vat',
            'add_type', 'add_amount', 'deduction_type', 'deduction_amount', 'service_tax'
        ]
        widgets = {
            'invoice_no': forms.TextInput(attrs={'class': 'box3 bg-gray-100 cursor-not-allowed', 'readonly': 'readonly'}),
            'duty_rate': forms.NumberInput(attrs={'class': 'box3'}),
            'date_of_issue_invoice': DateInput(attrs={'class': 'box3'}),
            'time_of_issue_invoice': TimeInput(attrs={'class': 'box3'}),
            'customer_category': forms.Select(attrs={'class': 'box3'}),
            'taxable_services': forms.Select(attrs={'class': 'box3'}),
            'buyer_address': forms.Textarea(attrs={'class': 'box3 w-full', 'rows': 5}),
            'buyer_contact_person': forms.TextInput(attrs={'class': 'box3'}),
            'buyer_phone': forms.TextInput(attrs={'class': 'box3'}),
            'buyer_email': forms.EmailInput(attrs={'class': 'box3 w-full'}),
            'buyer_ecc': forms.TextInput(attrs={'class': 'box3'}),
            'buyer_tin': forms.TextInput(attrs={'class': 'box3'}),
            'buyer_mobile': forms.TextInput(attrs={'class': 'box3'}),
            'buyer_fax': forms.TextInput(attrs={'class': 'box3'}),
            'buyer_vat': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'consignee_address': forms.Textarea(attrs={'class': 'box3 w-full', 'rows': 5}),
            'consignee_contact_person': forms.TextInput(attrs={'class': 'box3'}),
            'consignee_phone': forms.TextInput(attrs={'class': 'box3'}),
            'consignee_email': forms.EmailInput(attrs={'class': 'box3 w-full'}),
            'consignee_ecc': forms.TextInput(attrs={'class': 'box3'}),
            'consignee_tin': forms.TextInput(attrs={'class': 'box3'}),
            'consignee_mobile': forms.TextInput(attrs={'class': 'box3'}),
            'consignee_fax': forms.TextInput(attrs={'class': 'box3'}),
            'consignee_vat': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'add_type': forms.Select(choices=[('0', 'Amt(Rs)'), ('1', 'Per(%)')], attrs={'class': 'box3'}),
            'add_amount': forms.NumberInput(attrs={'class': 'box3'}),
            'deduction_type': forms.Select(choices=[('0', 'Amt(Rs)'), ('1', 'Per(%)')], attrs={'class': 'box3'}),
            'deduction_amount': forms.NumberInput(attrs={'class': 'box3'}),
            'service_tax': forms.Select(attrs={'class': 'box3'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate initial choices for dropdowns
        self.fields['customer_category'].queryset = ServiceCategory.objects.all()
        self.fields['taxable_services'].queryset = TaxableService.objects.all()
        self.fields['service_tax'].queryset = ExciseServiceMaster.objects.all()

        # Set initial selections for location dropdowns if instance exists
        if self.instance.pk:
            if self.instance.buyer_country:
                self.fields['buyer_country_choices'].initial = self.instance.buyer_country
                self.fields['buyer_state_choices'].queryset = StateMaster.objects.filter(country=self.instance.buyer_country)
                self.fields['buyer_state_choices'].initial = self.instance.buyer_state
                if self.instance.buyer_state:
                    self.fields['buyer_city_choices'].queryset = CityMaster.objects.filter(state=self.instance.buyer_state)
                    self.fields['buyer_city_choices'].initial = self.instance.buyer_city
            
            if self.instance.consignee_country:
                self.fields['consignee_country_choices'].initial = self.instance.consignee_country
                self.fields['consignee_state_choices'].queryset = StateMaster.objects.filter(country=self.instance.consignee_country)
                self.fields['consignee_state_choices'].initial = self.instance.consignee_state
                if self.instance.consignee_state:
                    self.fields['consignee_city_choices'].queryset = CityMaster.objects.filter(state=self.instance.consignee_state)
                    self.fields['consignee_city_choices'].initial = self.instance.consignee_city
            
            self.fields['buyer_name_autocomplete'].initial = self.instance.buyer_name
            self.fields['consignee_name_autocomplete'].initial = self.instance.consignee_name
        else: # For new instances, set today's date and current time
            self.fields['date_of_issue_invoice'].initial = timezone.now().date()
            self.fields['time_of_issue_invoice'].initial = timezone.now().time()


    def clean(self):
        cleaned_data = super().clean()
        
        # Manually assign selected dropdown values to model fields
        cleaned_data['buyer_country'] = cleaned_data.get('buyer_country_choices')
        cleaned_data['buyer_state'] = cleaned_data.get('buyer_state_choices')
        cleaned_data['buyer_city'] = cleaned_data.get('buyer_city_choices')
        cleaned_data['consignee_country'] = cleaned_data.get('consignee_country_choices')
        cleaned_data['consignee_state'] = cleaned_data.get('consignee_state_choices')
        cleaned_data['consignee_city'] = cleaned_data.get('consignee_city_choices')

        # Extract customer ID from autocomplete string if format is "Name [ID]"
        buyer_name_full = cleaned_data.get('buyer_name_autocomplete')
        consignee_name_full = cleaned_data.get('consignee_name_autocomplete')
        
        if buyer_name_full:
            cleaned_data['buyer_name'] = buyer_name_full # Save full string to db
            # Optional: If CustomerCode needs to be set from this
            # cleaned_data['customer_code'] = CustomerMaster.get_customer_id_from_name_string(buyer_name_full)
        if consignee_name_full:
            cleaned_data['consignee_name'] = consignee_name_full # Save full string to db
            
        # Add general form-level validation here (e.g., date formats, email formats)
        # ASP.NET used RequiredFieldValidator, RegularExpressionValidator. Django handles this with field types and required=True
        # For regex validation on specific fields, you can add custom clean_field methods:
        email_regex = r"^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$"
        if cleaned_data.get('buyer_email') and not re.match(email_regex, cleaned_data['buyer_email']):
            self.add_error('buyer_email', 'Invalid email format.')
        if cleaned_data.get('consignee_email') and not re.match(email_regex, cleaned_data['consignee_email']):
            self.add_error('consignee_email', 'Invalid email format.')

        # Numerical validation (e.g., duty_rate, add_amount, deduction_amount)
        # Django's FloatField and NumberInput already handle this.
        # Ensure values are positive if required by business logic.
        for field_name in ['duty_rate', 'add_amount', 'deduction_amount']:
            value = cleaned_data.get(field_name)
            if value is not None and value < 0:
                self.add_error(field_name, "Value cannot be negative.")

        return cleaned_data

class ServiceTaxInvoiceItemForm(forms.ModelForm):
    # Display fields (read-only, pre-filled from PO data)
    item_desc = forms.CharField(max_length=255, required=False, label="Description", 
                                widget=forms.TextInput(attrs={'class': 'box3 bg-gray-100', 'readonly': 'readonly'}))
    unit_symbol = forms.CharField(max_length=50, required=False, label="Unit", 
                                  widget=forms.TextInput(attrs={'class': 'box3 bg-gray-100', 'readonly': 'readonly'}))
    total_po_qty = forms.FloatField(required=False, label="Total Qty", 
                                    widget=forms.NumberInput(attrs={'class': 'box3 bg-gray-100', 'readonly': 'readonly'}))
    remaining_qty = forms.FloatField(required=False, label="Remn Qty", 
                                     widget=forms.NumberInput(attrs={'class': 'box3 bg-gray-100', 'readonly': 'readonly'}))
    po_rate = forms.FloatField(required=False, label="Rate", 
                               widget=forms.NumberInput(attrs={'class': 'box3 bg-gray-100', 'readonly': 'readonly'}))
    
    # Checkbox for selection (not directly in model, but for formset management)
    # The checkbox itself isn't part of ModelForm fields but handled via `formset` in template.
    # For a more Django-centric approach, use `can_delete` on formset or custom logic in the view.
    # We'll assume the checkbox implies "include this item in the invoice" and require req_qty if checked.

    class Meta:
        model = ServiceTaxInvoiceItem
        fields = ['id', 'item_id', 'unit', 'qty', 'req_qty', 'amt_in_per', 'rate']
        widgets = {
            'item_id': forms.HiddenInput(), # Hidden field to store actual PO item ID
            'qty': forms.HiddenInput(), # Hidden field to store total PO quantity
            'rate': forms.HiddenInput(), # Hidden field to store rate from PO
            'unit': forms.Select(attrs={'class': 'box3'}), # User can select unit for ReqQty
            'req_qty': forms.NumberInput(attrs={'class': 'box3'}),
            'amt_in_per': forms.NumberInput(attrs={'class': 'box3'}),
            # 'id' is for existing items in the formset (used for updates/deletes)
        }

    def __init__(self, *args, **kwargs):
        # Additional data passed from view (e.g., initial PO item data)
        po_item_data = kwargs.pop('po_item_data', None)
        super().__init__(*args, **kwargs)

        if po_item_data:
            self.fields['item_desc'].initial = po_item_data.get('item_desc')
            self.fields['unit_symbol'].initial = po_item_data.get('unit_symbol')
            self.fields['total_po_qty'].initial = po_item_data.get('total_qty')
            self.fields['remaining_qty'].initial = po_item_data.get('remaining_qty')
            self.fields['po_rate'].initial = po_item_data.get('rate')
            
            # Ensure the hidden fields also get initial values if creating a new item
            self.fields['item_id'].initial = po_item_data.get('id')
            self.fields['qty'].initial = po_item_data.get('total_qty')
            self.fields['rate'].initial = po_item_data.get('rate')

        self.fields['unit'].queryset = UnitMaster.objects.all() # Populate units dropdown
        # If this is an existing invoice item, set initial unit
        if self.instance.pk and self.instance.unit:
             self.fields['unit'].initial = self.instance.unit
        # Default for Amt in %
        if not self.instance.pk:
            self.fields['amt_in_per'].initial = 100 # Default "100" as in ASP.NET

    def clean(self):
        cleaned_data = super().clean()
        req_qty = cleaned_data.get('req_qty')
        amt_in_per = cleaned_data.get('amt_in_per')

        # Validation from ASP.NET
        if req_qty is not None and req_qty <= 0:
            self.add_error('req_qty', 'Required quantity must be greater than zero.')
        if amt_in_per is not None and amt_in_per <= 0:
            self.add_error('amt_in_per', 'Amount in percentage must be greater than zero.')
        
        # The complex `rmnqty >= ReqQty` validation happens at the ServiceTaxInvoice model level
        # because it needs to aggregate quantities across all invoice items for a given PO item,
        # which is difficult to do reliably for individual forms in a formset during clean().
        # We will perform this in the view's form_valid() method.
        return cleaned_data

ServiceTaxInvoiceItemFormSet = inlineformset_factory(
    ServiceTaxInvoice,
    ServiceTaxInvoiceItem,
    form=ServiceTaxInvoiceItemForm,
    extra=0, # No extra blank forms by default, add via HTMX if needed (complex)
    can_delete=True # Allows deleting existing items if editing
)

```

#### 4.3 Views (`invoices/views.py`)

The views will be thin, primarily handling HTTP requests, form validation, and delegating business logic to the models. We'll include a main `CreateView` and auxiliary views for HTMX interactions (autocomplete, cascading dropdowns, goods table).

```python
from django.views.generic import CreateView, TemplateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.shortcuts import get_object_or_404, render
from django.db import transaction
from django.template.loader import render_to_string
from django.utils import timezone
from datetime import datetime

from .models import (
    ServiceTaxInvoice, ServiceTaxInvoiceItem,
    CustomerMaster, CountryMaster, StateMaster, CityMaster,
    PurchaseOrderMaster, PurchaseOrderDetail, ExciseServiceMaster
)
from .forms import ServiceTaxInvoiceForm, ServiceTaxInvoiceItemFormSet, ServiceTaxInvoiceItemForm

# Assume authentication middleware populates request.user and session['compid'], session['finyear']
# Placeholder for session data extraction (in a real app, use authentication/context processors)
def get_session_data(request):
    return {
        'comp_id': request.session.get('compid', 1), # Default to 1 if not set
        'fin_year_id': request.session.get('finyear', 1), # Default to 1 if not set
        'username': request.user.username if request.user.is_authenticated else 'anonymous'
    }

class ServiceTaxInvoiceCreateView(CreateView):
    model = ServiceTaxInvoice
    form_class = ServiceTaxInvoiceForm
    template_name = 'invoices/servicetaxinvoice/invoice_form.html'
    success_url = reverse_lazy('invoices:invoice_success_list') # Redirect to a list page

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        session_data = get_session_data(self.request)
        
        # Initial data for form fields from URL params
        po_no = self.request.GET.get('pn', '')
        po_id = self.request.GET.get('poid', '') # Assuming POId is int or can be converted
        wo_no_str = self.request.GET.get('wn', '').replace(',', ', ') # Add space after comma for readability
        po_date = self.request.GET.get('date', '')
        customer_code = self.request.GET.get('cid', '') # Buyer customer code

        context['po_no'] = po_no
        context['po_date'] = po_date
        context['wo_no'] = wo_no_str
        context['invoice_date'] = timezone.now().strftime('%d-%m-%Y') # LblInvDate equivalent

        # Initial invoice number (auto-generated)
        if not self.request.POST: # Only for GET requests, not on form POST
            context['form'].fields['invoice_no'].initial = ServiceTaxInvoice.get_next_invoice_no(
                session_data['comp_id'], session_data['fin_year_id']
            )

        # Pre-populate Buyer details from customer_code if present in URL
        if not self.request.POST and customer_code:
            try:
                buyer_customer = CustomerMaster.objects.get(customer_id=customer_code, comp_id=session_data['comp_id'])
                context['form'].fields['buyer_name_autocomplete'].initial = str(buyer_customer)
                context['form'].fields['buyer_address'].initial = buyer_customer.material_del_address
                context['form'].fields['buyer_country_choices'].initial = buyer_customer.material_del_country
                context['form'].fields['buyer_state_choices'].queryset = StateMaster.objects.filter(country=buyer_customer.material_del_country)
                context['form'].fields['buyer_state_choices'].initial = buyer_customer.material_del_state
                if buyer_customer.material_del_state:
                    context['form'].fields['buyer_city_choices'].queryset = CityMaster.objects.filter(state=buyer_customer.material_del_state)
                    context['form'].fields['buyer_city_choices'].initial = buyer_customer.material_del_city
                context['form'].fields['buyer_contact_person'].initial = buyer_customer.contact_person
                context['form'].fields['buyer_phone'].initial = buyer_customer.material_del_contact_no
                context['form'].fields['buyer_email'].initial = buyer_customer.email
                context['form'].fields['buyer_ecc'].initial = buyer_customer.ecc_no
                context['form'].fields['buyer_tin'].initial = buyer_customer.tin_cst_no
                context['form'].fields['buyer_mobile'].initial = buyer_customer.contact_no
                context['form'].fields['buyer_fax'].initial = buyer_customer.material_del_fax_no
                context['form'].fields['buyer_vat'].initial = buyer_customer.tin_vat_no
            except CustomerMaster.DoesNotExist:
                messages.warning(self.request, "Initial buyer customer not found.")
            
        # Initial Service Tax selection (from LiveSerTax=1)
        if not self.request.POST:
            default_service_tax = ExciseServiceMaster.objects.filter(live_ser_tax=True).first()
            if default_service_tax:
                context['form'].fields['service_tax'].initial = default_service_tax.id

        # Goods Formset
        # When creating a new invoice, items come from the PO
        po_item_data = []
        if po_id and session_data['comp_id']:
            po_item_data = ServiceTaxInvoice.get_po_details_for_invoice(int(po_id), session_data['comp_id'])
            
        # Create initial_data for the formset from the PO details
        initial_formset_data = [
            {
                'item_id': item['id'],
                'unit': UnitMaster.objects.filter(symbol=item['unit_symbol']).first(), # Find UnitMaster object by symbol
                'qty': item['total_qty'],
                'req_qty': 0, # Default to 0, user will fill this
                'amt_in_per': 100, # Default as per ASP.NET
                'rate': item['rate'],
                'item_desc': item['item_desc'],
                'unit_symbol': item['unit_symbol'],
                'total_po_qty': item['total_qty'],
                'remaining_qty': item['remaining_qty'],
                'po_rate': item['rate'],
            } for item in po_item_data
        ]
        
        # Pass the po_item_data to each form in the formset
        # This is tricky with inlineformset_factory. A common pattern is to make `extra` 0
        # and create forms manually or handle dynamic rows with HTMX.
        # For simplicity here, we create the formset with `initial` data
        # and let the form's __init__ handle populating display fields.
        context['formset'] = ServiceTaxInvoiceItemFormSet(
            self.request.POST or None,
            self.request.FILES or None,
            prefix='items',
            initial=initial_formset_data
        )

        # Override formset forms' __init__ to pass po_item_data
        if not self.request.POST:
            for i, form in enumerate(context['formset']):
                if i < len(initial_formset_data):
                    form.__init__(prefix=form.prefix, initial=initial_formset_data[i], po_item_data=initial_formset_data[i])

        return context

    def form_valid(self, form):
        session_data = get_session_data(self.request)
        
        # Set system/session-dependent fields before saving main invoice
        form.instance.sys_date = timezone.now().date()
        form.instance.sys_time = timezone.now().time()
        form.instance.comp_id = session_data['comp_id']
        form.instance.fin_year_id = session_data['fin_year_id']
        form.instance.session_id = session_data['username']
        form.instance.po_no = self.request.GET.get('pn', '')
        form.instance.po_id = self.request.GET.get('poid', None)
        form.instance.wo_no = self.request.GET.get('wn', '')
        
        # Generate invoice number if not already set (should be for new invoices)
        if not form.instance.invoice_no:
            form.instance.invoice_no = ServiceTaxInvoice.get_next_invoice_no(
                session_data['comp_id'], session_data['fin_year_id']
            )

        formset = ServiceTaxInvoiceItemFormSet(self.request.POST, self.request.FILES, prefix='items')
        
        # Check if formset is valid before proceeding
        if not formset.is_valid():
            messages.error(self.request, "Please correct the errors in the goods items.")
            # If formset is invalid, re-render the form with errors
            context = self.get_context_data(form=form)
            context['formset'] = formset
            return self.render_to_response(context)

        # Prepare data for model-level quantity validation
        invoice_items_data_for_validation = []
        for item_form in formset:
            if not item_form.cleaned_data.get('DELETE', False): # Only process if not marked for deletion
                item_id = item_form.cleaned_data.get('item_id')
                req_qty = item_form.cleaned_data.get('req_qty')
                # If editing an existing item, pass its PK to exclude it from total sum
                item_pk = item_form.instance.pk
                if item_id is not None and req_qty is not None:
                    invoice_items_data_for_validation.append({
                        'id': item_pk, # Pass item instance ID for exclude in update scenarios
                        'item_id': item_id,
                        'req_qty': req_qty
                    })

        # Perform the critical quantity validation using the model method
        po_id = int(self.request.GET.get('poid', 0))
        is_valid_qty, validation_message = ServiceTaxInvoice.validate_goods_quantities(
            invoice_items_data_for_validation, po_id, session_data['comp_id'],
            current_invoice_id=form.instance.pk if form.instance.pk else None # Pass existing invoice ID for updates
        )

        if not is_valid_qty:
            messages.error(self.request, validation_message)
            # Re-render form with errors
            context = self.get_context_data(form=form)
            context['formset'] = formset
            return self.render_to_response(context)

        with transaction.atomic():
            self.object = form.save()
            
            # Save formset items after main invoice is saved
            for item_form in formset:
                if item_form.cleaned_data.get('DELETE', False):
                    if item_form.instance.pk:
                        item_form.instance.delete()
                else:
                    item_instance = item_form.save(commit=False)
                    item_instance.invoice_master = self.object
                    item_instance.invoice_no = self.object.invoice_no # Redundant but matches original schema
                    
                    # Ensure item_id, qty, rate are set correctly from the PO context
                    # These were hidden fields populated in `__init__` and cleaned in `clean`
                    # Re-retrieve from original PO data if needed, or rely on form's cleaned_data
                    original_po_item = PurchaseOrderDetail.objects.filter(id=item_instance.item_id).first()
                    if original_po_item:
                        item_instance.qty = original_po_item.total_qty
                        item_instance.rate = original_po_item.rate
                    
                    item_instance.save()

        messages.success(self.request, 'Service Tax Invoice created successfully!')
        
        # HTMX response for success
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'invoiceCreated' # Custom event to trigger other UI updates
                }
            )
        return super().form_valid(form)

    def form_invalid(self, form):
        # If the main form has errors, re-render it.
        messages.error(self.request, "Please correct the errors in the form.")
        context = self.get_context_data(form=form)
        context['formset'] = ServiceTaxInvoiceItemFormSet(self.request.POST, self.request.FILES, prefix='items')
        # Re-populate initial data for display fields in formset forms
        po_id = int(self.request.GET.get('poid', 0))
        session_data = get_session_data(self.request)
        if po_id and session_data['comp_id']:
            po_item_data = ServiceTaxInvoice.get_po_details_for_invoice(po_id, session_data['comp_id'])
            for i, formset_form in enumerate(context['formset']):
                if i < len(po_item_data):
                    # Pass the original PO item data to each form for re-initialization
                    formset_form.__init__(prefix=formset_form.prefix, data=self.request.POST, po_item_data=po_item_data[i])
        return self.render_to_response(context)


# --- HTMX/API Views ---

class CustomerAutocomplete(View):
    """
    Provides autocomplete suggestions for customer names.
    Replaces ASP.NET WebMethod `sql`.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        session_data = get_session_data(request)
        customers = []
        if query:
            customers = CustomerMaster.objects.filter(
                customer_name__icontains=query,
                comp_id=session_data['comp_id']
            )[:10] # Limit to 10 results
        
        # Return format "CustomerName [CustomerId]" as in ASP.NET
        results = [f"{c.customer_name} [{c.customer_id}]" for c in customers]
        return HttpResponse('\n'.join(results), content_type='text/plain')

class GetCustomerDetails(View):
    """
    Fetches full customer details by ID (extracted from autocomplete string)
    and returns as JSON or HTMX fragments. Replaces `Button3_Click` and `Button6_Click`.
    """
    def get(self, request, *args, **kwargs):
        customer_name_str = request.GET.get('customer_name_str', '')
        customer_id = CustomerMaster.get_customer_id_from_name_string(customer_name_str)
        session_data = get_session_data(request)

        customer_details = {}
        if customer_id:
            try:
                customer = CustomerMaster.objects.get(customer_id=customer_id, comp_id=session_data['comp_id'])
                customer_details = {
                    'customer_id': customer.customer_id,
                    'customer_name': customer.customer_name,
                    'material_del_address': customer.material_del_address,
                    'material_del_country_id': customer.material_del_country.id if customer.material_del_country else '',
                    'material_del_state_id': customer.material_del_state.id if customer.material_del_state else '',
                    'material_del_city_id': customer.material_del_city.id if customer.material_del_city else '',
                    'material_del_contact_no': customer.material_del_contact_no,
                    'material_del_fax_no': customer.material_del_fax_no,
                    'contact_person': customer.contact_person,
                    'email': customer.email,
                    'tin_vat_no': customer.tin_vat_no,
                    'ecc_no': customer.ecc_no,
                    'contact_no': customer.contact_no, # Mobile No
                    'tin_cst_no': customer.tin_cst_no,
                }
            except CustomerMaster.DoesNotExist:
                pass # Customer not found, return empty dict

        return JsonResponse(customer_details)


class GetStates(View):
    """
    Returns options for State dropdown based on selected Country.
    Replaces `DrpByCountry_SelectedIndexChanged` and `DrpCoCountry_SelectedIndexChanged`.
    """
    def get(self, request, *args, **kwargs):
        country_id = request.GET.get('country_choices')
        if country_id:
            states = StateMaster.objects.filter(country_id=country_id).order_by('name')
        else:
            states = StateMaster.objects.none()
        
        # Render options for the select field
        options_html = '<option value="">---------</option>' # Default blank option
        for state in states:
            options_html += f'<option value="{state.id}">{state.name}</option>'
        return HttpResponse(options_html)

class GetCities(View):
    """
    Returns options for City dropdown based on selected State.
    Replaces `DrpByState_SelectedIndexChanged` and `DrpCoState_SelectedIndexChanged`.
    """
    def get(self, request, *args, **kwargs):
        state_id = request.GET.get('state_choices')
        if state_id:
            cities = CityMaster.objects.filter(state_id=state_id).order_by('name')
        else:
            cities = CityMaster.objects.none()

        # Render options for the select field
        options_html = '<option value="">---------</option>' # Default blank option
        for city in cities:
            options_html += f'<option value="{city.id}">{city.name}</option>'
        return HttpResponse(options_html)

class ServiceTaxInvoiceItemTablePartial(TemplateView):
    """
    Renders the 'Goods' table (GridView equivalent) dynamically via HTMX.
    This will be loaded into the main form's 'Goods' tab.
    """
    template_name = 'invoices/servicetaxinvoice/_goods_table_partial.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        session_data = get_session_data(self.request)
        po_id = self.request.GET.get('poid', '') # Get POId from URL params (or session/form)
        
        # For an edit scenario, formset might already have data
        # For a new invoice, items come from PO
        po_item_data = []
        if po_id and session_data['comp_id']:
            po_item_data = ServiceTaxInvoice.get_po_details_for_invoice(int(po_id), session_data['comp_id'])
            
        # Create initial_data for the formset from the PO details
        initial_formset_data = [
            {
                'item_id': item['id'],
                'unit': UnitMaster.objects.filter(symbol=item['unit_symbol']).first(), # Find UnitMaster object by symbol
                'qty': item['total_qty'],
                'req_qty': 0, # Default to 0, user will fill this
                'amt_in_per': 100, # Default as per ASP.NET
                'rate': item['rate'],
                'item_desc': item['item_desc'],
                'unit_symbol': item['unit_symbol'],
                'total_po_qty': item['total_qty'],
                'remaining_qty': item['remaining_qty'],
                'po_rate': item['rate'],
            } for item in po_item_data
        ]

        # Use the formset to render the goods table.
        # We need to ensure the formset prefix matches what the main form will use.
        context['formset'] = ServiceTaxInvoiceItemFormSet(prefix='items', initial=initial_formset_data)
        
        # Override formset forms' __init__ to pass po_item_data for rendering
        for i, form in enumerate(context['formset']):
            if i < len(initial_formset_data):
                form.__init__(prefix=form.prefix, initial=initial_formset_data[i], po_item_data=initial_formset_data[i])

        return context

# A placeholder for the success redirect page (e.g., a list of invoices)
class InvoiceSuccessListView(TemplateView):
    template_name = 'invoices/invoice_list_success.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # You would load invoice list data here
        context['invoices'] = ServiceTaxInvoice.objects.all().order_by('-sys_date', '-sys_time')[:10] # Example
        return context

```

#### 4.4 Templates

Templates will be split into a main form template and partials for HTMX dynamic content. They'll use Alpine.js for tab management and DataTables for the `_goods_table_partial`.

**`invoices/servicetaxinvoice/invoice_form.html`**
This is the main form template, replicating the multi-tab layout using Alpine.js for tab switching.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ activeTab: 0, showModal: false, modalContent: '' }">
    <div class="bg-white shadow-md rounded-lg p-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Service Tax Invoice - New</h2>

        <!-- Invoice Header Details -->
        <div class="mb-6 grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
            <div>
                <label class="block text-sm font-medium text-gray-700">Ser. Tax Invoice No.:</label>
                {{ form.invoice_no }}
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Date:</label>
                <p class="font-bold text-gray-900">{{ invoice_date }}</p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">PO No.:</label>
                <p class="font-bold text-gray-900">{{ po_no }}</p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">PO Date:</label>
                <p class="font-bold text-gray-900">{{ po_date }}</p>
            </div>
            <div class="col-span-1 md:col-span-2">
                <label class="block text-sm font-medium text-gray-700">WO No.:</label>
                <p class="font-bold text-gray-900">{{ wo_no }}</p>
            </div>
        </div>

        <form method="post" class="space-y-6">
            {% csrf_token %}

            <!-- Tabs Navigation -->
            <div class="flex border-b border-gray-200 mb-6">
                <button type="button" @click="activeTab = 0" :class="{'border-b-2 border-blue-500 text-blue-600': activeTab === 0, 'text-gray-600': activeTab !== 0}" class="py-2 px-4 text-center font-medium focus:outline-none">General</button>
                <button type="button" @click="activeTab = 1" :class="{'border-b-2 border-blue-500 text-blue-600': activeTab === 1, 'text-gray-600': activeTab !== 0}" class="py-2 px-4 text-center font-medium focus:outline-none">Buyer</button>
                <button type="button" @click="activeTab = 2" :class="{'border-b-2 border-blue-500 text-blue-600': activeTab === 2, 'text-gray-600': activeTab !== 1}" class="py-2 px-4 text-center font-medium focus:outline-none">Consignee</button>
                <button type="button" @click="activeTab = 3" :class="{'border-b-2 border-blue-500 text-blue-600': activeTab === 3, 'text-gray-600': activeTab !== 2}" class="py-2 px-4 text-center font-medium focus:outline-none">Goods</button>
                <button type="button" @click="activeTab = 4" :class="{'border-b-2 border-blue-500 text-blue-600': activeTab === 4, 'text-gray-600': activeTab !== 3}" class="py-2 px-4 text-center font-medium focus:outline-none">Taxation</button>
            </div>

            <!-- Tab Content: General -->
            <div x-show="activeTab === 0" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="{{ form.customer_category.id_for_label }}" class="block text-sm font-medium text-gray-700">Category</label>
                        {{ form.customer_category }}
                        {% if form.customer_category.errors %}<p class="text-red-500 text-xs mt-1">{{ form.customer_category.errors }}</p>{% endif %}
                    </div>
                    <div>
                        <label for="{{ form.duty_rate.id_for_label }}" class="block text-sm font-medium text-gray-700">Rate of Duty</label>
                        {{ form.duty_rate }}
                        {% if form.duty_rate.errors %}<p class="text-red-500 text-xs mt-1">{{ form.duty_rate.errors }}</p>{% endif %}
                    </div>
                </div>
                <div>
                    <label for="{{ form.taxable_services.id_for_label }}" class="block text-sm font-medium text-gray-700">Taxable Services</label>
                    {{ form.taxable_services }}
                    {% if form.taxable_services.errors %}<p class="text-red-500 text-xs mt-1">{{ form.taxable_services.errors }}</p>{% endif %}
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="{{ form.date_of_issue_invoice.id_for_label }}" class="block text-sm font-medium text-gray-700">Date Of Issue Of Invoice</label>
                        {{ form.date_of_issue_invoice }}
                        {% if form.date_of_issue_invoice.errors %}<p class="text-red-500 text-xs mt-1">{{ form.date_of_issue_invoice.errors }}</p>{% endif %}
                    </div>
                    <div>
                        <label for="{{ form.time_of_issue_invoice.id_for_label }}" class="block text-sm font-medium text-gray-700">Time of Issue of Invoice</label>
                        {{ form.time_of_issue_invoice }}
                        {% if form.time_of_issue_invoice.errors %}<p class="text-red-500 text-xs mt-1">{{ form.time_of_issue_invoice.errors }}</p>{% endif %}
                    </div>
                </div>
                <div class="mt-6 flex justify-end">
                    <button type="button" @click="activeTab = 1" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Next</button>
                </div>
            </div>

            <!-- Tab Content: Buyer -->
            <div x-show="activeTab === 1" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="{{ form.buyer_name_autocomplete.id_for_label }}" class="block text-sm font-medium text-gray-700">Name</label>
                        {{ form.buyer_name_autocomplete }}
                        <div id="buyer-autocomplete-results" class="relative z-10 bg-white border border-gray-300 rounded-md shadow-lg"></div>
                        {% if form.buyer_name_autocomplete.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_name_autocomplete.errors }}</p>{% endif %}
                    </div>
                    <div class="flex items-end space-x-2">
                        <button type="button" 
                                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                                hx-get="{% url 'invoices:get_customer_details' %}?customer_name_str={{ form.buyer_name_autocomplete.value }}"
                                hx-target="#buyer-details-container"
                                hx-swap="outerHTML"
                                hx-trigger="click"
                                >Search</button>
                    </div>
                </div>
                
                <div id="buyer-details-container" class="space-y-4">
                    <div>
                        <label for="{{ form.buyer_address.id_for_label }}" class="block text-sm font-medium text-gray-700">Address</label>
                        {{ form.buyer_address }}
                        {% if form.buyer_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_address.errors }}</p>{% endif %}
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label for="{{ form.buyer_country_choices.id_for_label }}" class="block text-sm font-medium text-gray-700">Country</label>
                            {{ form.buyer_country_choices }}
                            {% if form.buyer_country_choices.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_country_choices.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.buyer_state_choices.id_for_label }}" class="block text-sm font-medium text-gray-700">State</label>
                            {{ form.buyer_state_choices }}
                            {% if form.buyer_state_choices.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_state_choices.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.buyer_city_choices.id_for_label }}" class="block text-sm font-medium text-gray-700">City</label>
                            {{ form.buyer_city_choices }}
                            {% if form.buyer_city_choices.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_city_choices.errors }}</p{% endif %}
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label for="{{ form.buyer_contact_person.id_for_label }}" class="block text-sm font-medium text-gray-700">Contact Person</label>
                            {{ form.buyer_contact_person }}
                            {% if form.buyer_contact_person.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_contact_person.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.buyer_phone.id_for_label }}" class="block text-sm font-medium text-gray-700">Phone No.</label>
                            {{ form.buyer_phone }}
                            {% if form.buyer_phone.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_phone.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.buyer_mobile.id_for_label }}" class="block text-sm font-medium text-gray-700">Mobile No.</label>
                            {{ form.buyer_mobile }}
                            {% if form.buyer_mobile.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_mobile.errors }}</p>{% endif %}
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label for="{{ form.buyer_email.id_for_label }}" class="block text-sm font-medium text-gray-700">E-mail</label>
                            {{ form.buyer_email }}
                            {% if form.buyer_email.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_email.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.buyer_fax.id_for_label }}" class="block text-sm font-medium text-gray-700">Fax No.</label>
                            {{ form.buyer_fax }}
                            {% if form.buyer_fax.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_fax.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.buyer_vat.id_for_label }}" class="block text-sm font-medium text-gray-700">TIN / VAT No</label>
                            {{ form.buyer_vat }}
                            {% if form.buyer_vat.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_vat.errors }}</p>{% endif %}
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label for="{{ form.buyer_ecc.id_for_label }}" class="block text-sm font-medium text-gray-700">Customer's ECC.No.</label>
                            {{ form.buyer_ecc }}
                            {% if form.buyer_ecc.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_ecc.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.buyer_tin.id_for_label }}" class="block text-sm font-medium text-gray-700">TIN / CST No.</label>
                            {{ form.buyer_tin }}
                            {% if form.buyer_tin.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_tin.errors }}</p>{% endif %}
                        </div>
                    </div>
                </div>
                <div class="mt-6 flex justify-end space-x-4">
                    <button type="button" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded" 
                            onclick="window.location.href='{% url 'invoices:invoice_list_success' %}'">Cancel</button>
                    <button type="button" @click="activeTab = 2" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Next</button>
                </div>
            </div>

            <!-- Tab Content: Consignee -->
            <div x-show="activeTab === 2" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="{{ form.consignee_name_autocomplete.id_for_label }}" class="block text-sm font-medium text-gray-700">Name</label>
                        {{ form.consignee_name_autocomplete }}
                        <div id="consignee-autocomplete-results" class="relative z-10 bg-white border border-gray-300 rounded-md shadow-lg"></div>
                        {% if form.consignee_name_autocomplete.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_name_autocomplete.errors }}</p>{% endif %}
                    </div>
                    <div class="flex items-end space-x-2">
                        <button type="button" 
                                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                                hx-get="{% url 'invoices:get_customer_details' %}?customer_name_str={{ form.consignee_name_autocomplete.value }}"
                                hx-target="#consignee-details-container"
                                hx-swap="outerHTML"
                                hx-trigger="click">Search</button>
                        <button type="button" 
                                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                                onclick="copyBuyerToConsignee()">Copy from Buyer</button>
                    </div>
                </div>

                <div id="consignee-details-container" class="space-y-4">
                    <div>
                        <label for="{{ form.consignee_address.id_for_label }}" class="block text-sm font-medium text-gray-700">Address</label>
                        {{ form.consignee_address }}
                        {% if form.consignee_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_address.errors }}</p>{% endif %}
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label for="{{ form.consignee_country_choices.id_for_label }}" class="block text-sm font-medium text-gray-700">Country</label>
                            {{ form.consignee_country_choices }}
                            {% if form.consignee_country_choices.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_country_choices.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.consignee_state_choices.id_for_label }}" class="block text-sm font-medium text-gray-700">State</label>
                            {{ form.consignee_state_choices }}
                            {% if form.consignee_state_choices.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_state_choices.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.consignee_city_choices.id_for_label }}" class="block text-sm font-medium text-gray-700">City</label>
                            {{ form.consignee_city_choices }}
                            {% if form.consignee_city_choices.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_city_choices.errors }}</p>{% endif %}
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label for="{{ form.consignee_contact_person.id_for_label }}" class="block text-sm font-medium text-gray-700">Contact Person</label>
                            {{ form.consignee_contact_person }}
                            {% if form.consignee_contact_person.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_contact_person.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.consignee_phone.id_for_label }}" class="block text-sm font-medium text-gray-700">Phone No.</label>
                            {{ form.consignee_phone }}
                            {% if form.consignee_phone.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_phone.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.consignee_mobile.id_for_label }}" class="block text-sm font-medium text-gray-700">Mobile No.</label>
                            {{ form.consignee_mobile }}
                            {% if form.consignee_mobile.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_mobile.errors }}</p>{% endif %}
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label for="{{ form.consignee_email.id_for_label }}" class="block text-sm font-medium text-gray-700">E-mail</label>
                            {{ form.consignee_email }}
                            {% if form.consignee_email.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_email.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.consignee_fax.id_for_label }}" class="block text-sm font-medium text-gray-700">Fax No.</label>
                            {{ form.consignee_fax }}
                            {% if form.consignee_fax.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_fax.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.consignee_vat.id_for_label }}" class="block text-sm font-medium text-gray-700">TIN / VAT No</label>
                            {{ form.consignee_vat }}
                            {% if form.consignee_vat.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_vat.errors }}</p>{% endif %}
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label for="{{ form.consignee_ecc.id_for_label }}" class="block text-sm font-medium text-gray-700">Customer's ECC.No.</label>
                            {{ form.consignee_ecc }}
                            {% if form.consignee_ecc.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_ecc.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ form.consignee_tin.id_for_label }}" class="block text-sm font-medium text-gray-700">TIN / CST No.</label>
                            {{ form.consignee_tin }}
                            {% if form.consignee_tin.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_tin.errors }}</p>{% endif %}
                        </div>
                    </div>
                </div>
                <div class="mt-6 flex justify-end space-x-4">
                    <button type="button" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded" 
                            onclick="window.location.href='{% url 'invoices:invoice_list_success' %}'">Cancel</button>
                    <button type="button" @click="activeTab = 3" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Next</button>
                </div>
            </div>

            <!-- Tab Content: Goods -->
            <div x-show="activeTab === 3" class="space-y-4">
                <div id="goods-table-container"
                     hx-get="{% url 'invoices:invoice_item_table_partial' %}?poid={{ po_id }}"
                     hx-trigger="load"
                     hx-swap="innerHTML">
                    <!-- Goods table will be loaded here via HTMX -->
                    <div class="text-center py-8">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p class="mt-2">Loading Goods...</p>
                    </div>
                </div>
                {{ formset.management_form }}
                <div class="mt-6 flex justify-end space-x-4">
                    <button type="button" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded" 
                            onclick="window.location.href='{% url 'invoices:invoice_list_success' %}'">Cancel</button>
                    <button type="button" @click="activeTab = 4" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Next</button>
                </div>
            </div>

            <!-- Tab Content: Taxation -->
            <div x-show="activeTab === 4" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="{{ form.add_amount.id_for_label }}" class="block text-sm font-medium text-gray-700">Add</label>
                        <div class="flex space-x-2">
                            {{ form.add_amount }}
                            {{ form.add_type }}
                        </div>
                        {% if form.add_amount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.add_amount.errors }}</p>{% endif %}
                    </div>
                    <div>
                        <label for="{{ form.deduction_amount.id_for_label }}" class="block text-sm font-medium text-gray-700">Deduction</label>
                        <div class="flex space-x-2">
                            {{ form.deduction_amount }}
                            {{ form.deduction_type }}
                        </div>
                        {% if form.deduction_amount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.deduction_amount.errors }}</p>{% endif %}
                    </div>
                </div>
                <div>
                    <label for="{{ form.service_tax.id_for_label }}" class="block text-sm font-medium text-gray-700">Service Tax</label>
                    {{ form.service_tax }}
                    {% if form.service_tax.errors %}<p class="text-red-500 text-xs mt-1">{{ form.service_tax.errors }}</p>{% endif %}
                </div>
                <div class="mt-6 flex justify-end space-x-4">
                    <button type="button" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded" 
                            onclick="window.location.href='{% url 'invoices:invoice_list_success' %}'">Cancel</button>
                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Submit</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
    // Global function to copy buyer details to consignee
    function copyBuyerToConsignee() {
        const buyerPrefix = 'id_buyer_';
        const consigneePrefix = 'id_consignee_';

        document.getElementById(consigneePrefix + 'name_autocomplete').value = document.getElementById(buyerPrefix + 'name_autocomplete').value;
        document.getElementById(consigneePrefix + 'address').value = document.getElementById(buyerPrefix + 'address').value;
        document.getElementById(consigneePrefix + 'contact_person').value = document.getElementById(buyerPrefix + 'contact_person').value;
        document.getElementById(consigneePrefix + 'phone').value = document.getElementById(buyerPrefix + 'phone').value;
        document.getElementById(consigneePrefix + 'mobile').value = document.getElementById(buyerPrefix + 'mobile').value;
        document.getElementById(consigneePrefix + 'email').value = document.getElementById(buyerPrefix + 'email').value;
        document.getElementById(consigneePrefix + 'fax').value = document.getElementById(buyerPrefix + 'fax').value;
        document.getElementById(consigneePrefix + 'vat').value = document.getElementById(buyerPrefix + 'vat').value;
        document.getElementById(consigneePrefix + 'ecc').value = document.getElementById(buyerPrefix + 'ecc').value;
        document.getElementById(consigneePrefix + 'tin').value = document.getElementById(buyerPrefix + 'tin').value;

        // For dropdowns, update selection and then trigger HTMX to load states/cities
        const buyerCountryId = document.getElementById(buyerPrefix + 'country_choices').value;
        const buyerStateId = document.getElementById(buyerPrefix + 'state_choices').value;
        const buyerCityId = document.getElementById(buyerPrefix + 'city_choices').value;

        document.getElementById(consigneePrefix + 'country_choices').value = buyerCountryId;
        // Manually dispatch change event for HTMX to pick up
        htmx.trigger(document.getElementById(consigneePrefix + 'country_choices'), 'change');
        
        // Wait for states to load, then set state and trigger for cities
        document.body.addEventListener('htmx:afterSwap', function(evt) {
            if (evt.target.id === consigneePrefix + 'state') {
                document.getElementById(consigneePrefix + 'state_choices').value = buyerStateId;
                htmx.trigger(document.getElementById(consigneePrefix + 'state_choices'), 'change');

                document.body.addEventListener('htmx:afterSwap', function(evt2) {
                    if (evt2.target.id === consigneePrefix + 'city') {
                        document.getElementById(consigneePrefix + 'city_choices').value = buyerCityId;
                    }
                }, { once: true });
            }
        }, { once: true });
    }

    // Helper for autocomplete selection
    document.body.addEventListener('click', function(event) {
        if (event.target.classList.contains('autocomplete-item')) {
            const inputId = event.target.closest('.relative').querySelector('input[hx-get]').id;
            document.getElementById(inputId).value = event.target.textContent;
            event.target.closest('.relative').querySelector('.autocomplete-results').innerHTML = ''; // Clear results
        }
    });

    // Handle HTMX autocomplete results
    document.body.addEventListener('htmx:afterOnLoad', function(evt) {
        if (evt.detail.xhr.responseURL.includes('customer-autocomplete')) {
            const resultsDiv = evt.detail.elt;
            resultsDiv.classList.add('autocomplete-results'); // Add class for styling
            const results = evt.detail.xhr.responseText.split('\n').filter(r => r.trim() !== '');
            if (results.length > 0) {
                let html = '<ul class="list-none p-0 m-0">';
                results.forEach(result => {
                    html += `<li class="autocomplete-item px-3 py-2 cursor-pointer hover:bg-gray-100">${result}</li>`;
                });
                html += '</ul>';
                resultsDiv.innerHTML = html;
            } else {
                resultsDiv.innerHTML = '';
            }
        }
    });
</script>
{% endblock %}
```

**`invoices/servicetaxinvoice/_goods_table_partial.html`**
This partial renders the formset for goods, which will be styled and enhanced by `DataTables`.

```html
{% comment %}
    This template is loaded via HTMX into the main invoice_form.html.
    It contains the formset for Service Tax Invoice Items and DataTables initialization.
{% endcomment %}

<div class="overflow-x-auto rounded-lg shadow-sm border border-gray-200">
    <table id="goodsTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Unit</th>
                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Remn Qty</th>
                <th class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Of Qty</th>
                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Req Qty</th>
                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amt in (%)</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {{ formset.management_form }}
            {% for form in formset %}
            <tr>
                <td class="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">{{ form.item_desc }}</td>
                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900 text-center">{{ form.unit_symbol }}</td>
                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900 text-right">{{ form.total_po_qty }}</td>
                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900 text-right">{{ form.remaining_qty }}</td>
                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">{{ form.unit }}</td>
                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">
                    {{ form.req_qty }}
                    {% if form.req_qty.errors %}<p class="text-red-500 text-xs mt-1">{{ form.req_qty.errors }}</p>{% endif %}
                </td>
                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900 text-right">{{ form.po_rate }}</td>
                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">
                    {{ form.amt_in_per }}
                    {% if form.amt_in_per.errors %}<p class="text-red-500 text-xs mt-1">{{ form.amt_in_per.errors }}</p>{% endif %}
                </td>
                {{ form.id }} {# Hidden PK field for existing items #}
                {{ form.item_id }} {# Hidden PO Item ID #}
                {{ form.qty }} {# Hidden Total PO Qty #}
                {{ form.rate }} {# Hidden Rate from PO #}
                {{ form.DELETE }} {# Hidden delete checkbox for formset management #}
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after HTMX loads the content
    $(document).ready(function() {
        $('#goodsTable').DataTable({
            "paging": false,       // Disable pagination as per original GridView (or enable if desired)
            "searching": false,    // Disable search filter (or enable if desired)
            "info": false,         // Disable showing "Showing X of Y entries"
            "ordering": false,     // Disable column sorting
            "lengthChange": false  // Disable "Show X entries" dropdown
        });
    });
</script>
```

**`invoices/invoice_list_success.html`**
A simple placeholder for redirect after successful invoice creation.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-3xl font-bold text-green-700 mb-6">Invoice Operation Successful!</h2>
    <p class="text-lg text-gray-700 mb-8">Your service tax invoice has been processed.</p>
    
    <h3 class="text-xl font-semibold text-gray-800 mb-4">Recent Invoices:</h3>
    <div class="bg-white shadow-md rounded-lg p-6">
        {% if invoices %}
            <table class="min-w-full divide-y divide-gray-200" id="recentInvoicesTable">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice No.</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Buyer</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No.</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for invoice in invoices %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ invoice.invoice_no }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ invoice.date_of_issue_invoice|date:"d-m-Y" }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ invoice.buyer_name }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ invoice.po_no }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <!-- Add view/edit actions here -->
                            <a href="#" class="text-blue-600 hover:text-blue-900">View Details</a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        {% else %}
            <p class="text-gray-600">No recent invoices found.</p>
        {% endif %}
    </div>

    <div class="mt-8">
        <a href="{% url 'invoices:create_invoice' %}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg">Create New Invoice</a>
    </div>
</div>

{% block extra_js %}
<script src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"></script>
<script>
    $(document).ready(function() {
        $('#recentInvoicesTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
        });
    });
</script>
{% endblock %}
{% endblock %}
```

#### 4.5 URLs (`invoices/urls.py`)

Define the URL patterns for the views, including the main form and all HTMX-specific endpoints.

```python
from django.urls import path
from .views import (
    ServiceTaxInvoiceCreateView, ServiceTaxInvoiceItemTablePartial,
    CustomerAutocomplete, GetCustomerDetails, GetStates, GetCities,
    InvoiceSuccessListView
)

app_name = 'invoices'

urlpatterns = [
    # Main Invoice Form (Create)
    path('service-tax-invoice/new/', ServiceTaxInvoiceCreateView.as_view(), name='create_invoice'),

    # HTMX Endpoints
    path('htmx/customer-autocomplete/', CustomerAutocomplete.as_view(), name='customer_autocomplete'),
    path('htmx/get-customer-details/', GetCustomerDetails.as_view(), name='get_customer_details'),
    path('htmx/get-states/', GetStates.as_view(), name='get_states'),
    path('htmx/get-cities/', GetCities.as_view(), name='get_cities'),
    path('htmx/invoice-item-table/', ServiceTaxInvoiceItemTablePartial.as_view(), name='invoice_item_table_partial'),

    # Success/List page after submission (equivalent to original ASP.NET redirect)
    path('service-tax-invoice/list/', InvoiceSuccessListView.as_view(), name='invoice_list_success'),

    # If you were to add update/delete/list views:
    # path('service-tax-invoice/<int:pk>/edit/', ServiceTaxInvoiceUpdateView.as_view(), name='edit_invoice'),
    # path('service-tax-invoice/<int:pk>/delete/', ServiceTaxInvoiceDeleteView.as_view(), name='delete_invoice'),
    # path('service-tax-invoice/', ServiceTaxInvoiceListView.as_view(), name='invoice_list'),
]

```
**Add `invoices` to `INSTALLED_APPS` in `settings.py` and include its URLs in your project's `urls.py`:**
```python
# your_project/urls.py
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('invoices/', include('invoices.urls')),
    # Other project URLs
]

```

#### 4.6 Tests (`invoices/tests.py`)

Comprehensive tests for models (business logic) and views (interactions, data flow, validation).

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db import IntegrityError
from django.utils import timezone
from datetime import time, date

from .models import (
    ServiceTaxInvoice, ServiceTaxInvoiceItem,
    CustomerMaster, CountryMaster, StateMaster, CityMaster,
    ServiceCategory, TaxableService, ExciseServiceMaster, UnitMaster,
    PurchaseOrderMaster, PurchaseOrderDetail
)

class ModelSetupMixin:
    """Mixin to set up common test data for models."""
    @classmethod
    def setUpTestData(cls):
        # Create auxiliary data
        cls.country1 = CountryMaster.objects.create(id=1, name='India')
        cls.state1 = StateMaster.objects.create(id=1, name='Maharashtra', country=cls.country1)
        cls.city1 = CityMaster.objects.create(id=1, name='Mumbai', state=cls.state1)
        cls.service_category1 = ServiceCategory.objects.create(id=1, description='Consulting')
        cls.taxable_service1 = TaxableService.objects.create(id=1, description='IT Services')
        cls.excise_service1 = ExciseServiceMaster.objects.create(id=1, terms='Standard Tax', live_ser_tax=True)
        cls.unit1 = UnitMaster.objects.create(id=1, symbol='Nos')
        cls.unit2 = UnitMaster.objects.create(id=2, symbol='Kgs')

        cls.comp_id = 101
        cls.fin_year_id = 2024

        cls.customer_buyer = CustomerMaster.objects.create(
            customer_id='CUST001', customer_name='Buyer Co.', comp_id=cls.comp_id,
            material_del_address='123 Buyer St', material_del_country=cls.country1,
            material_del_state=cls.state1, material_del_city=cls.city1,
            email='<EMAIL>', contact_person='John Buyer', contact_no='1234567890'
        )
        cls.customer_consignee = CustomerMaster.objects.create(
            customer_id='CUST002', customer_name='Consignee Co.', comp_id=cls.comp_id,
            material_del_address='456 Consignee Rd', material_del_country=cls.country1,
            material_del_state=cls.state1, material_del_city=cls.city1,
            email='<EMAIL>', contact_person='Jane Consignee', contact_no='0987654321'
        )

        cls.po_master1 = PurchaseOrderMaster.objects.create(po_id=1, po_no='PO001', comp_id=cls.comp_id)
        cls.po_detail1 = PurchaseOrderDetail.objects.create(
            id=1, po_master=cls.po_master1, item_desc='Laptop', total_qty=10.0, unit=cls.unit1, rate=50000.0
        )
        cls.po_detail2 = PurchaseOrderDetail.objects.create(
            id=2, po_master=cls.po_master1, item_desc='Mouse', total_qty=100.0, unit=cls.unit1, rate=500.0
        )

        # Ensure unique invoice numbers for tests
        ServiceTaxInvoice.objects.create(
            invoice_no='0001', comp_id=cls.comp_id, fin_year_id=cls.fin_year_id,
            sys_date=date(2023, 1, 1), sys_time=time(9,0,0), customer_code='CUST001',
            buyer_name='Dummy Buyer', buyer_address='Dummy Addr',
            date_of_issue_invoice=date(2023, 1, 1), time_of_issue_invoice=time(9,0,0)
        )
        ServiceTaxInvoice.objects.create(
            invoice_no='0002', comp_id=cls.comp_id, fin_year_id=cls.fin_year_id,
            sys_date=date(2023, 1, 2), sys_time=time(10,0,0), customer_code='CUST001',
            buyer_name='Dummy Buyer', buyer_address='Dummy Addr',
            date_of_issue_invoice=date(2023, 1, 2), time_of_issue_invoice=time(10,0,0)
        )


class ServiceTaxInvoiceModelTest(ModelSetupMixin, TestCase):
    def test_invoice_creation(self):
        invoice = ServiceTaxInvoice.objects.create(
            invoice_no='0003',
            sys_date=timezone.now().date(),
            sys_time=timezone.now().time(),
            comp_id=self.comp_id,
            fin_year_id=self.fin_year_id,
            session_id='testuser',
            po_id=self.po_master1.po_id,
            po_no=self.po_master1.po_no,
            date_of_issue_invoice=timezone.now().date(),
            time_of_issue_invoice=timezone.now().time(),
            duty_rate=18.0,
            customer_code=self.customer_buyer.customer_id,
            customer_category=self.service_category1,
            buyer_name='Test Buyer',
            buyer_address='123 Test St',
            buyer_country=self.country1,
            buyer_state=self.state1,
            buyer_city=self.city1,
            buyer_email='<EMAIL>',
            service_tax=self.excise_service1,
            taxable_services=self.taxable_service1,
            add_type='0', add_amount=100.0,
            deduction_type='1', deduction_amount=5.0
        )
        self.assertEqual(invoice.invoice_no, '0003')
        self.assertEqual(invoice.comp_id, self.comp_id)
        self.assertEqual(invoice.buyer_name, 'Test Buyer')
        self.assertEqual(invoice.buyer_country, self.country1)

    def test_get_next_invoice_no(self):
        next_invoice_no = ServiceTaxInvoice.get_next_invoice_no(self.comp_id, self.fin_year_id)
        self.assertEqual(next_invoice_no, '0003') # Because 0001, 0002 already exist

        # Test with no existing invoices for a different company/finyear
        next_invoice_no_new_context = ServiceTaxInvoice.get_next_invoice_no(999, 999)
        self.assertEqual(next_invoice_no_new_context, '0001')

    def test_get_po_details_for_invoice(self):
        # Create an existing invoice item to test remaining_qty logic
        existing_invoice = ServiceTaxInvoice.objects.create(
            invoice_no='0000', comp_id=self.comp_id, fin_year_id=self.fin_year_id,
            po_id=self.po_master1.po_id, po_no=self.po_master1.po_no,
            sys_date=timezone.now().date(), sys_time=timezone.now().time(),
            date_of_issue_invoice=timezone.now().date(), time_of_issue_invoice=timezone.now().time()
        )
        ServiceTaxInvoiceItem.objects.create(
            invoice_master=existing_invoice, item_id=self.po_detail1.id,
            unit=self.unit1, qty=self.po_detail1.total_qty, req_qty=5.0,
            amt_in_per=100.0, rate=self.po_detail1.rate
        )

        po_items = ServiceTaxInvoice.get_po_details_for_invoice(self.po_master1.po_id, self.comp_id)
        
        # Check Laptop item
        laptop_item = next(item for item in po_items if item['item_desc'] == 'Laptop')
        self.assertAlmostEqual(laptop_item['remaining_qty'], 5.0) # 10 - 5 = 5
        self.assertEqual(laptop_item['unit_symbol'], 'Nos')

        # Check Mouse item (should be full quantity)
        mouse_item = next(item for item in po_items if item['item_desc'] == 'Mouse')
        self.assertAlmostEqual(mouse_item['remaining_qty'], 100.0)

    def test_validate_goods_quantities_valid(self):
        invoice_items_data = [
            {'item_id': self.po_detail1.id, 'req_qty': 5.0},
            {'item_id': self.po_detail2.id, 'req_qty': 50.0},
        ]
        is_valid, msg = ServiceTaxInvoice.validate_goods_quantities(invoice_items_data, self.po_master1.po_id, self.comp_id)
        self.assertTrue(is_valid)
        self.assertEqual(msg, "Quantities are valid.")

    def test_validate_goods_quantities_exceeds(self):
        invoice_items_data = [
            {'item_id': self.po_detail1.id, 'req_qty': 11.0}, # Exceeds total qty 10.0
        ]
        is_valid, msg = ServiceTaxInvoice.validate_goods_quantities(invoice_items_data, self.po_master1.po_id, self.comp_id)
        self.assertFalse(is_valid)
        self.assertIn("exceeds remaining available quantity", msg)

    def test_validate_goods_quantities_already_invoiced(self):
        # Create an existing invoice item
        existing_invoice = ServiceTaxInvoice.objects.create(
            invoice_no='0000', comp_id=self.comp_id, fin_year_id=self.fin_year_id,
            po_id=self.po_master1.po_id, po_no=self.po_master1.po_no,
            sys_date=timezone.now().date(), sys_time=timezone.now().time(),
            date_of_issue_invoice=timezone.now().date(), time_of_issue_invoice=timezone.now().time()
        )
        ServiceTaxInvoiceItem.objects.create(
            invoice_master=existing_invoice, item_id=self.po_detail1.id,
            unit=self.unit1, qty=self.po_detail1.total_qty, req_qty=7.0, # 7 already invoiced
            amt_in_per=100.0, rate=self.po_detail1.rate
        )

        invoice_items_data = [
            {'item_id': self.po_detail1.id, 'req_qty': 4.0}, # Total 7+4=11, exceeds 10
        ]
        is_valid, msg = ServiceTaxInvoice.validate_goods_quantities(invoice_items_data, self.po_master1.po_id, self.comp_id)
        self.assertFalse(is_valid)
        self.assertIn("exceeds remaining available quantity", msg)

    def test_validate_goods_quantities_zero_or_negative(self):
        invoice_items_data = [
            {'item_id': self.po_detail1.id, 'req_qty': 0.0},
        ]
        is_valid, msg = ServiceTaxInvoice.validate_goods_quantities(invoice_items_data, self.po_master1.po_id, self.comp_id)
        self.assertFalse(is_valid)
        self.assertIn("Required quantity", msg)

class ServiceTaxInvoiceViewsTest(ModelSetupMixin, TestCase):
    def setUp(self):
        self.client = Client()
        # Simulate user login and session data
        self.client.session['compid'] = self.comp_id
        self.client.session['finyear'] = self.fin_year_id
        # A simple way to simulate user authentication for `request.user.username`
        # In a real app, use `self.client.login()` or `force_login`
        self.user = self.client.force_login(self.customer_buyer) # Use a dummy user or existing customer

    def test_create_invoice_get(self):
        response = self.client.get(reverse('invoices:create_invoice'), {
            'pn': self.po_master1.po_no,
            'poid': self.po_master1.po_id,
            'wn': 'WO001,WO002,',
            'date': '01-01-2024',
            'cid': self.customer_buyer.customer_id
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'invoices/servicetaxinvoice/invoice_form.html')
        self.assertIn('form', response.context)
        self.assertIn('formset', response.context)
        self.assertEqual(response.context['form'].initial['invoice_no'], '0003') # Auto-generated
        self.assertEqual(response.context['form'].initial['buyer_name_autocomplete'], str(self.customer_buyer))
        self.assertEqual(response.context['form'].initial['buyer_address'], self.customer_buyer.material_del_address)
        self.assertEqual(response.context['form'].initial['service_tax'], self.excise_service1.id) # Default live service tax

    def test_create_invoice_post_valid_data(self):
        initial_po_items = ServiceTaxInvoice.get_po_details_for_invoice(self.po_master1.po_id, self.comp_id)
        
        post_data = {
            'invoice_no': ServiceTaxInvoice.get_next_invoice_no(self.comp_id, self.fin_year_id),
            'duty_rate': 18.0,
            'date_of_issue_invoice': '2024-03-10',
            'time_of_issue_invoice': '10:00:00',
            'customer_category': self.service_category1.id,
            'taxable_services': self.taxable_service1.id,
            'buyer_name_autocomplete': str(self.customer_buyer),
            'buyer_address': '123 Buyer St',
            'buyer_country_choices': self.country1.id,
            'buyer_state_choices': self.state1.id,
            'buyer_city_choices': self.city1.id,
            'buyer_contact_person': 'John Buyer',
            'buyer_phone': '1234567890',
            'buyer_email': '<EMAIL>',
            'buyer_ecc': 'BCC123',
            'buyer_tin': 'BTIN123',
            'buyer_mobile': '0987654321',
            'buyer_fax': 'BFAX123',
            'buyer_vat': 'BVAT123',
            'consignee_name_autocomplete': str(self.customer_consignee),
            'consignee_address': '456 Consignee Rd',
            'consignee_country_choices': self.country1.id,
            'consignee_state_choices': self.state1.id,
            'consignee_city_choices': self.city1.id,
            'consignee_contact_person': 'Jane Consignee',
            'consignee_phone': '0987654321',
            'consignee_email': '<EMAIL>',
            'consignee_ecc': 'CCC123',
            'consignee_tin': 'CTIN123',
            'consignee_mobile': '1234567890',
            'consignee_fax': 'CFAX123',
            'consignee_vat': 'CVAT123',
            'add_type': '0', 'add_amount': 50.0,
            'deduction_type': '1', 'deduction_amount': 2.0,
            'service_tax': self.excise_service1.id,
            
            'pn': self.po_master1.po_no,
            'poid': self.po_master1.po_id,
            'wn': 'WO001,WO002,',
            'date': '01-01-2024',
            'cid': self.customer_buyer.customer_id
        }
        
        # Add formset data for goods items
        total_forms = len(initial_po_items)
        post_data.update({
            'items-TOTAL_FORMS': total_forms,
            'items-INITIAL_FORMS': 0,
            'items-MIN_NUM_FORMS': 0,
            'items-MAX_NUM_FORMS': 1000,
        })
        for i, item_data in enumerate(initial_po_items):
            post_data.update({
                f'items-{i}-id': '', # New item
                f'items-{i}-item_id': item_data['id'],
                f'items-{i}-unit': item_data['unit'].id, # Use unit ID
                f'items-{i}-qty': item_data['total_qty'],
                f'items-{i}-req_qty': 1 if item_data['id'] == self.po_detail1.id else 2, # Request a small qty
                f'items-{i}-amt_in_per': 100,
                f'items-{i}-rate': item_data['rate'],
            })

        response = self.client.post(reverse('invoices:create_invoice') + 
                                    f"?pn={self.po_master1.po_no}&poid={self.po_master1.po_id}&wn=WO001,WO002,&date=01-01-2024&cid={self.customer_buyer.customer_id}", 
                                    post_data, follow=True)
        
        self.assertEqual(response.status_code, 200) # Should redirect to success list
        self.assertTemplateUsed(response, 'invoices/invoice_list_success.html')
        self.assertTrue(ServiceTaxInvoice.objects.filter(invoice_no=post_data['invoice_no']).exists())
        self.assertTrue(ServiceTaxInvoiceItem.objects.filter(invoice_master__invoice_no=post_data['invoice_no']).count() > 0)
        self.assertContains(response, 'Service Tax Invoice created successfully!')

    def test_create_invoice_post_invalid_qty(self):
        initial_po_items = ServiceTaxInvoice.get_po_details_for_invoice(self.po_master1.po_id, self.comp_id)

        post_data = {
            'invoice_no': ServiceTaxInvoice.get_next_invoice_no(self.comp_id, self.fin_year_id),
            'duty_rate': 18.0,
            'date_of_issue_invoice': '2024-03-10',
            'time_of_issue_invoice': '10:00:00',
            'customer_category': self.service_category1.id,
            'taxable_services': self.taxable_service1.id,
            'buyer_name_autocomplete': str(self.customer_buyer),
            'buyer_address': '123 Buyer St',
            'buyer_country_choices': self.country1.id, 'buyer_state_choices': self.state1.id, 'buyer_city_choices': self.city1.id,
            'buyer_contact_person': 'John Buyer', 'buyer_phone': '1234567890', 'buyer_email': '<EMAIL>',
            'buyer_ecc': 'BCC123', 'buyer_tin': 'BTIN123', 'buyer_mobile': '0987654321', 'buyer_fax': 'BFAX123', 'buyer_vat': 'BVAT123',
            'consignee_name_autocomplete': str(self.customer_consignee),
            'consignee_address': '456 Consignee Rd',
            'consignee_country_choices': self.country1.id, 'consignee_state_choices': self.state1.id, 'consignee_city_choices': self.city1.id,
            'consignee_contact_person': 'Jane Consignee', 'consignee_phone': '0987654321', 'consignee_email': '<EMAIL>',
            'consignee_ecc': 'CCC123', 'consignee_tin': 'CTIN123', 'consignee_mobile': '1234567890', 'consignee_fax': 'CFAX123', 'consignee_vat': 'CVAT123',
            'add_type': '0', 'add_amount': 50.0,
            'deduction_type': '1', 'deduction_amount': 2.0,
            'service_tax': self.excise_service1.id,
            
            'pn': self.po_master1.po_no,
            'poid': self.po_master1.po_id,
            'wn': 'WO001,WO002,',
            'date': '01-01-2024',
            'cid': self.customer_buyer.customer_id
        }
        
        # Add formset data with an invalid quantity
        total_forms = len(initial_po_items)
        post_data.update({
            'items-TOTAL_FORMS': total_forms, 'items-INITIAL_FORMS': 0, 'items-MIN_NUM_FORMS': 0, 'items-MAX_NUM_FORMS': 1000,
        })
        for i, item_data in enumerate(initial_po_items):
            post_data.update({
                f'items-{i}-id': '', 
                f'items-{i}-item_id': item_data['id'],
                f'items-{i}-unit': item_data['unit'].id, 
                f'items-{i}-qty': item_data['total_qty'],
                f'items-{i}-req_qty': 11 if item_data['id'] == self.po_detail1.id else 2, # Invalid: 11 > 10
                f'items-{i}-amt_in_per': 100,
                f'items-{i}-rate': item_data['rate'],
            })

        response = self.client.post(reverse('invoices:create_invoice') + 
                                    f"?pn={self.po_master1.po_no}&poid={self.po_master1.po_id}&wn=WO001,WO002,&date=01-01-2024&cid={self.customer_buyer.customer_id}", 
                                    post_data)
        
        self.assertEqual(response.status_code, 200) # Should re-render form with errors
        self.assertTemplateUsed(response, 'invoices/servicetaxinvoice/invoice_form.html')
        self.assertFalse(ServiceTaxInvoice.objects.filter(invoice_no=post_data['invoice_no']).exists())
        self.assertContains(response, 'Required quantity') # Check for error message

    def test_customer_autocomplete_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('invoices:customer_autocomplete'), {'q': 'buyer'}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertIn(str(self.customer_buyer), response.content.decode())
        self.assertEqual(response['Content-Type'], 'text/plain')

    def test_get_customer_details_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        customer_name_str = str(self.customer_buyer)
        response = self.client.get(reverse('invoices:get_customer_details'), {'customer_name_str': customer_name_str}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = response.json()
        self.assertEqual(data['customer_id'], self.customer_buyer.customer_id)
        self.assertEqual(data['material_del_address'], self.customer_buyer.material_del_address)

    def test_get_states_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('invoices:get_states'), {'country_choices': self.country1.id}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertIn(f'<option value="{self.state1.id}">{self.state1.name}</option>', response.content.decode())

    def test_get_cities_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('invoices:get_cities'), {'state_choices': self.state1.id}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertIn(f'<option value="{self.city1.id}">{self.city1.name}</option>', response.content.decode())

    def test_invoice_item_table_partial_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('invoices:invoice_item_table_partial'), {
            'poid': self.po_master1.po_id
        }, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'invoices/servicetaxinvoice/_goods_table_partial.html')
        self.assertContains(response, self.po_detail1.item_desc)
        self.assertContains(response, self.po_detail2.item_desc)
        # Check if formset is rendered
        self.assertContains(response, 'name="items-0-req_qty"')

```