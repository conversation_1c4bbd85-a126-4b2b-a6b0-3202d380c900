## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

The provided ASP.NET page (`CreditorsDebitors_InDetailList.aspx`) acts primarily as a filter interface for a report or detailed list displayed within an `<iframe>`. The actual data retrieval and display logic resides in `CreditorsDebitors_InDetailView.aspx`, which we don't have. Therefore, our Django modernization plan will focus on replacing this filter page, inferring the data model for the detailed list, and replacing the `<iframe>` approach with dynamic HTMX-driven partial views.

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

The provided ASP.NET code does not directly expose SQL queries or database table names for the "Creditors / Debitors In Detail List". It merely passes parameters (`SupId`, `Key`, `Date From`, `Date To`) to another page loaded in an `<iframe>`. Based on the context of "Creditors / Debitors In Detail", we infer the data relates to financial transactions or entries involving parties (creditors/debitors).

*   **Inferred Main Data Table for the "Detail List":** `tbl_account_transaction`
*   **Inferred Columns for `tbl_account_transaction`:**
    *   `transaction_id` (Primary Key)
    *   `party_id` (Foreign Key to `tbl_party`, corresponding to `SupId`)
    *   `transaction_date` (Date/DateTime)
    *   `description` (Text/Varchar)
    *   `amount` (Decimal/Money)
    *   `transaction_type` (e.g., 'CR' for Credit, 'DR' for Debit, or 'Payment', 'Invoice' etc. - corresponding to `Key`)
    *   `reference_no` (Varchar, for external document reference)
    *   `financial_year_id` (Integer, from `Session["finyear"]`)
    *   `company_id` (Integer, from `Session["compid"]`)

*   **Inferred Supporting Table for Parties (Creditors/Debitors):** `tbl_party`
*   **Inferred Columns for `tbl_party`:**
    *   `party_id` (Primary Key, corresponding to `SupId`)
    *   `party_name` (Varchar)
    *   `party_type` (Char, e.g., 'C' for Creditor, 'D' for Debitor - corresponding to `Key`)
    *   `is_creditor` (Boolean)
    *   `is_debitor` (Boolean)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

The ASP.NET page `CreditorsDebitors_InDetailList.aspx` itself does not perform direct CRUD operations on the "Creditors / Debitors In Detail" data. Its primary backend functionality is:

*   **Read (Filter Parameters):** It reads `SupId` and `Key` from the URL query string and date range (`txtFrmDt`, `txtToDt`) from user input.
*   **Session Management:** It stores `SupId`, `Key`, `DtFrm`, `DtTo` in the `Session` for the `CreditorsDebitors_InDetailView.aspx` page (loaded in the `<iframe>`) to consume.
*   **Navigation/Redirection:**
    *   On initial load and "Search" button click, it updates the `src` attribute of the `<iframe>`, effectively reloading the detailed report page with updated parameters.
    *   On "Cancel" button click, it redirects the user to another page (`CreditorsDebitors.aspx`).
*   **Validation:** It performs client-side (via `RegularExpressionValidator`) and server-side (in `btnSearch_Click`) validation for date format and ensures `From Date` is not after `To Date`.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

*   **Date Input Fields:** `txtFrmDt`, `txtToDt` (ASP.NET `TextBox` with `CalendarExtender`). These will be replaced by Django `DateInput` widgets in a `ReportFilterForm`.
*   **Date Validation:** `RegularExpressionValidator` for date format. Django forms handle this through field types and custom validation.
*   **Search Button:** `btnSearch` (ASP.NET `Button`). This will be a standard HTML submit button in a Django form, driving an HTMX request to refresh the report table.
*   **Cancel Button:** `btnCancel` (ASP.NET `Button`). This will be an HTML anchor tag or a button with an `hx-redirect` for navigation.
*   **Report Container:** `ifrm` (ASP.NET `HtmlIframe`). This will be replaced by an `div` container whose content is dynamically loaded and updated by HTMX with the DataTables view.
*   **Styling:** Inline CSS and external CSS files. This will be replaced by Tailwind CSS classes as per AutoERP guidelines.

## Step 4: Generate Django Code

### 4.1 Models (accounts/models.py)

Task: Create Django models based on the inferred database schema.

## Instructions:

We'll create two models: `Party` for creditors/debitors, and `AccountTransaction` for the detailed transaction entries that would constitute the report. We'll add a class method to `AccountTransaction` to encapsulate the filtering logic.

```python
from django.db import models

class Party(models.Model):
    # Corresponds to tbl_party
    party_id = models.IntegerField(db_column='party_id', primary_key=True) # Corresponds to SupId
    party_name = models.CharField(db_column='party_name', max_length=255)
    party_type = models.CharField(db_column='party_type', max_length=1) # Corresponds to Key (e.g., 'C' for Creditor, 'D' for Debitor)
    is_creditor = models.BooleanField(db_column='is_creditor', default=False)
    is_debitor = models.BooleanField(db_column='is_debitor', default=False)

    class Meta:
        managed = False
        db_table = 'tbl_party' # Inferred table name
        verbose_name = 'Party'
        verbose_name_plural = 'Parties'

    def __str__(self):
        return self.party_name

class AccountTransaction(models.Model):
    # Corresponds to tbl_account_transaction - the actual items in the detail list
    transaction_id = models.IntegerField(db_column='transaction_id', primary_key=True)
    party = models.ForeignKey(Party, on_delete=models.DO_NOTHING, db_column='party_id', related_name='transactions')
    transaction_date = models.DateField(db_column='transaction_date')
    description = models.CharField(db_column='description', max_length=500)
    amount = models.DecimalField(db_column='amount', max_digits=18, decimal_places=2)
    transaction_type = models.CharField(db_column='transaction_type', max_length=10) # e.g., 'CR', 'DR', 'Invoice', 'Payment'
    reference_no = models.CharField(db_column='reference_no', max_length=100, blank=True, null=True)
    financial_year_id = models.IntegerField(db_column='financial_year_id')
    company_id = models.IntegerField(db_column='company_id')

    class Meta:
        managed = False
        db_table = 'tbl_account_transaction' # Inferred table name
        verbose_name = 'Account Transaction'
        verbose_name_plural = 'Account Transactions'
        ordering = ['transaction_date', 'transaction_id'] # Order for the detailed list

    def __str__(self):
        return f"Transaction {self.transaction_id} for {self.party.party_name} on {self.transaction_date}"

    @classmethod
    def get_detailed_transactions(cls, party_id, party_type_key, date_from, date_to, financial_year_id, company_id):
        """
        Retrieves detailed account transactions based on filters.
        This method encapsulates the report generation logic (fat model).
        """
        queryset = cls.objects.filter(
            party_id=party_id,
            transaction_date__gte=date_from,
            transaction_date__lte=date_to,
            financial_year_id=financial_year_id,
            company_id=company_id
        )

        # Filter by party_type if 'Key' is relevant to transaction type
        # Assuming 'Key' corresponds to Party.party_type or affects transaction selection
        if party_type_key == 'C': # Creditor
            # Example: only show credit transactions or invoices for creditors
            # This logic is highly speculative and would depend on the actual report data
            queryset = queryset.filter(party__party_type='C')
        elif party_type_key == 'D': # Debitor
            # Example: only show debit transactions or payments for debitors
            queryset = queryset.filter(party__party_type='D')
            
        return queryset.select_related('party') # Optimize by pre-fetching party data
```

### 4.2 Forms (accounts/forms.py)

Task: Define a Django form for user input (date filters).

## Instructions:

We'll create a simple `Form` (not a `ModelForm` since it's just for filter parameters) for the date range.

```python
from django import forms
from django.core.exceptions import ValidationError
from datetime import date

class ReportFilterForm(forms.Form):
    date_from = forms.DateField(
        label="Date From",
        widget=forms.DateInput(attrs={
            'type': 'date', # HTML5 date input for native date picker
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'required': 'true'
        }),
        input_formats=['%d-%m-%Y', '%Y-%m-%d'] # Support dd-MM-yyyy from old system, also YYYY-MM-DD
    )
    date_to = forms.DateField(
        label="To",
        widget=forms.DateInput(attrs={
            'type': 'date', # HTML5 date input
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'required': 'true'
        }),
        input_formats=['%d-%m-%Y', '%Y-%m-%d']
    )

    def clean(self):
        cleaned_data = super().clean()
        date_from = cleaned_data.get('date_from')
        date_to = cleaned_data.get('date_to')

        if date_from and date_to:
            if date_from > date_to:
                raise ValidationError("Date From cannot be after Date To.")
        return cleaned_data

```

### 4.3 Views (accounts/views.py)

Task: Implement report filtering and display using CBVs and HTMX.

## Instructions:

We'll have a `TemplateView` for the main report page (which contains the filter form and the HTMX target div), and a `ListView` that serves the actual data table content via an HTMX request.

```python
from django.views.generic import TemplateView, ListView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404
from datetime import date

from .models import AccountTransaction, Party
from .forms import ReportFilterForm

# Assuming these are pulled from session/context for the current user/company/financial year
# In a real app, these would come from the authenticated user's profile or global settings.
# For demonstration, we'll hardcode or pass via URL.
DEFAULT_FINANCIAL_YEAR_ID = 1
DEFAULT_COMPANY_ID = 1

class CreditorsDebitorsReportView(TemplateView):
    template_name = 'accounts/creditorsdebitors/report.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Get party_id and party_type_key from URL parameters (as inferred from ASP.NET QueryString)
        party_id = self.kwargs.get('party_id')
        party_type_key = self.kwargs.get('party_type_key')

        party = get_object_or_404(Party, party_id=party_id)
        
        context['form'] = ReportFilterForm(initial={
            'date_from': date(date.today().year, 1, 1), # Default to start of current year
            'date_to': date.today()
        })
        context['party'] = party
        context['party_type_key'] = party_type_key
        return context

# This view will be loaded via HTMX to display the filtered table
class CreditorsDebitorsTablePartialView(ListView):
    model = AccountTransaction
    template_name = 'accounts/creditorsdebitors/_creditors_debitors_table.html'
    context_object_name = 'account_transactions'

    def get_queryset(self):
        # Retrieve filter parameters from GET request (HTMX will send them)
        date_from_str = self.request.GET.get('date_from')
        date_to_str = self.request.GET.get('date_to')
        party_id = self.kwargs.get('party_id') # From URL path
        party_type_key = self.kwargs.get('party_type_key') # From URL path

        # Try to parse dates, provide defaults or errors if invalid
        date_from = None
        date_to = None
        try:
            date_from = date.fromisoformat(date_from_str) if date_from_str else date(date.today().year, 1, 1)
        except ValueError:
            messages.error(self.request, "Invalid 'Date From' format.")
        try:
            date_to = date.fromisoformat(date_to_str) if date_to_str else date.today()
        except ValueError:
            messages.error(self.request, "Invalid 'Date To' format.")

        # Ensure dates are valid for range check (server-side validation)
        if date_from and date_to and date_from > date_to:
            messages.error(self.request, "Date From cannot be after Date To.")
            return AccountTransaction.objects.none() # Return empty queryset

        # Get session/context dependent IDs. In a real app, these would be from authentication.
        financial_year_id = DEFAULT_FINANCIAL_YEAR_ID
        company_id = DEFAULT_COMPANY_ID

        if party_id and party_type_key and date_from and date_to:
            # Call the fat model method to get filtered data
            return AccountTransaction.get_detailed_transactions(
                party_id=party_id,
                party_type_key=party_type_key,
                date_from=date_from,
                date_to=date_to,
                financial_year_id=financial_year_id,
                company_id=company_id
            )
        return AccountTransaction.objects.none() # No filters, return empty set
    
    # Views should be thin, so we keep the context simple
    # The filtering logic is in the model.
    # No need for form_valid, as this is a GET-only endpoint for HTMX content.
```

### 4.4 Templates (accounts/templates/accounts/creditorsdebitors/)

Task: Create templates for the report view and its HTMX-loaded table partial.

## Instructions:

*   `report.html`: Main page, containing the date filter form and the HTMX target for the table.
*   `_creditors_debitors_table.html`: Partial template for the DataTables content.

**`report.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Creditors / Debitors In Detail ({{ party.party_name }})</h2>
        <a href="{% url 'creditors_debitors_summary' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
            Back to Summary
        </a>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-lg mb-6">
        <form hx-get="{% url 'accounttransaction_table_partial' party.party_id party_type_key %}"
              hx-target="#accountTransactionsTable-container"
              hx-swap="innerHTML"
              hx-trigger="submit"
              class="flex flex-wrap items-end -mx-2">
            
            {% csrf_token %}
            
            <div class="px-2 mb-4 md:mb-0">
                <label for="{{ form.date_from.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.date_from.label }}:
                </label>
                {{ form.date_from }}
                {% if form.date_from.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.date_from.errors }}</p>
                {% endif %}
            </div>
            
            <div class="px-2 mb-4 md:mb-0">
                <label for="{{ form.date_to.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.date_to.label }}:
                </label>
                {{ form.date_to }}
                {% if form.date_to.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.date_to.errors }}</p>
                {% endif %}
            </div>
            
            <div class="px-2">
                <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Search
                </button>
            </div>
        </form>
        {% if form.non_field_errors %}
        <div class="text-red-500 text-xs mt-2">
            {% for error in form.non_field_errors %}
                {{ error }}
            {% endfor %}
        </div>
        {% endif %}
    </div>
    
    <div id="accountTransactionsTable-container"
         hx-trigger="load delay:100ms"
         hx-get="{% url 'accounttransaction_table_partial' party.party_id party_type_key %}"
         hx-swap="innerHTML">
        <!-- Initial loading state -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Creditor/Debitor Details...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
    });

    // Ensure DataTables reinitializes after HTMX swap
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'accountTransactionsTable-container') {
            const table = document.getElementById('accountTransactionsTable');
            if (table && !$.fn.DataTable.isDataTable(table)) {
                $(table).DataTable({
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "destroy": true // Allow reinitialization if it somehow gets stuck
                });
            }
        }
    });
</script>
{% endblock %}
```

**`_creditors_debitors_table.html`**

```html
<div class="overflow-x-auto bg-white rounded-lg shadow">
    <table id="accountTransactionsTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reference No.</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for transaction in account_transactions %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ transaction.transaction_date|date:"d-m-Y" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ transaction.description }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700 text-right">{{ transaction.amount }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ transaction.transaction_type }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ transaction.reference_no|default:"-" }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 px-4 text-center text-sm text-gray-500">No transactions found for the selected criteria.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // This script runs when the HTMX content is loaded.
    // DataTables should be initialized here.
    // It's crucial to destroy any existing DataTable instance before reinitialization
    // to prevent errors, especially if HTMX swaps content multiple times.
    $(document).ready(function() {
        const table = $('#accountTransactionsTable');
        if ($.fn.DataTable.isDataTable(table)) {
            table.DataTable().destroy();
        }
        table.DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "searching": true, // Enable search box
            "ordering": true,  // Enable sorting
            "paging": true,    // Enable pagination
            "info": true       // Enable info display
        });
    });
</script>
```

### 4.5 URLs (accounts/urls.py)

Task: Define URL patterns for the views.

## Instructions:

We'll define URLs for the main report page and the HTMX-loaded table partial. A placeholder URL for `creditors_debitors_summary` is also added, corresponding to the `btnCancel` redirect.

```python
from django.urls import path
from .views import CreditorsDebitorsReportView, CreditorsDebitorsTablePartialView
from django.views.generic import RedirectView

urlpatterns = [
    # Main report page for a specific party and type (SupId and Key)
    path('creditors_debitors/<int:party_id>/<str:party_type_key>/report/', 
         CreditorsDebitorsReportView.as_view(), 
         name='creditors_debitors_detail_report'),
         
    # HTMX endpoint for the detailed transactions table
    path('creditors_debitors/<int:party_id>/<str:party_type_key>/table/', 
         CreditorsDebitorsTablePartialView.as_view(), 
         name='accounttransaction_table_partial'),
         
    # Mimic the Cancel button's redirect
    # In a real app, this would point to the actual CreditorsDebitors summary page
    path('creditors_debitors/summary/', 
         RedirectView.as_view(url='/module/accounts/transactions/creditorsdebitors/', permanent=False),
         name='creditors_debitors_summary'),
]
```

### 4.6 Tests (accounts/tests.py)

Task: Write tests for the model and views.

## Instructions:

Include comprehensive unit tests for model methods and integration tests for all views, including HTMX interactions.

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date
from .models import Party, AccountTransaction
from .forms import ReportFilterForm

# Assuming financial_year_id and company_id are constant for tests or set via fixture
TEST_FINANCIAL_YEAR_ID = 1
TEST_COMPANY_ID = 1

class PartyModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        Party.objects.create(
            party_id=101, 
            party_name='Alpha Creditor', 
            party_type='C', 
            is_creditor=True, 
            is_debitor=False
        )
        Party.objects.create(
            party_id=102, 
            party_name='Beta Debitor', 
            party_type='D', 
            is_creditor=False, 
            is_debitor=True
        )
  
    def test_party_creation(self):
        party = Party.objects.get(party_id=101)
        self.assertEqual(party.party_name, 'Alpha Creditor')
        self.assertTrue(party.is_creditor)
        self.assertFalse(party.is_debitor)
        self.assertEqual(str(party), 'Alpha Creditor')

    def test_party_type_label(self):
        party = Party.objects.get(party_id=102)
        field_label = party._meta.get_field('party_type').verbose_name
        self.assertEqual(field_label, 'party type') # Django default verbose_name for char field

class AccountTransactionModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create parties first
        creditor = Party.objects.create(
            party_id=201, 
            party_name='Gamma Corp', 
            party_type='C', 
            is_creditor=True, 
            is_debitor=False
        )
        debitor = Party.objects.create(
            party_id=202, 
            party_name='Delta Co', 
            party_type='D', 
            is_creditor=False, 
            is_debitor=True
        )

        # Create test transactions
        cls.trans1 = AccountTransaction.objects.create(
            transaction_id=1,
            party=creditor,
            transaction_date=date(2023, 1, 15),
            description='Invoice #123',
            amount=100.00,
            transaction_type='INV',
            financial_year_id=TEST_FINANCIAL_YEAR_ID,
            company_id=TEST_COMPANY_ID
        )
        cls.trans2 = AccountTransaction.objects.create(
            transaction_id=2,
            party=creditor,
            transaction_date=date(2023, 2, 20),
            description='Payment Received',
            amount=50.00,
            transaction_type='PMT',
            financial_year_id=TEST_FINANCIAL_YEAR_ID,
            company_id=TEST_COMPANY_ID
        )
        cls.trans3 = AccountTransaction.objects.create(
            transaction_id=3,
            party=debitor,
            transaction_date=date(2023, 3, 10),
            description='Expense #456',
            amount=75.00,
            transaction_type='EXP',
            financial_year_id=TEST_FINANCIAL_YEAR_ID,
            company_id=TEST_COMPANY_ID
        )
        cls.trans_outside_range = AccountTransaction.objects.create(
            transaction_id=4,
            party=creditor,
            transaction_date=date(2022, 12, 1), # Outside 2023 range
            description='Old Invoice',
            amount=200.00,
            transaction_type='INV',
            financial_year_id=TEST_FINANCIAL_YEAR_ID,
            company_id=TEST_COMPANY_ID
        )
  
    def test_account_transaction_creation(self):
        trans = AccountTransaction.objects.get(transaction_id=1)
        self.assertEqual(trans.party.party_name, 'Gamma Corp')
        self.assertEqual(trans.amount, 100.00)
        self.assertEqual(str(trans), f"Transaction 1 for Gamma Corp on {date(2023, 1, 15)}")

    def test_get_detailed_transactions_by_party_and_date_range(self):
        # Test for creditor 'Gamma Corp' within a date range
        transactions = AccountTransaction.get_detailed_transactions(
            party_id=self.trans1.party.party_id,
            party_type_key='C', # Assuming C for creditor
            date_from=date(2023, 1, 1),
            date_to=date(2023, 2, 28),
            financial_year_id=TEST_FINANCIAL_YEAR_ID,
            company_id=TEST_COMPANY_ID
        )
        self.assertIn(self.trans1, transactions)
        self.assertIn(self.trans2, transactions)
        self.assertNotIn(self.trans3, transactions)
        self.assertNotIn(self.trans_outside_range, transactions)
        self.assertEqual(transactions.count(), 2)

    def test_get_detailed_transactions_empty_result(self):
        # Test for a non-existent party
        transactions = AccountTransaction.get_detailed_transactions(
            party_id=999, 
            party_type_key='X',
            date_from=date(2023, 1, 1),
            date_to=date(2023, 12, 31),
            financial_year_id=TEST_FINANCIAL_YEAR_ID,
            company_id=TEST_COMPANY_ID
        )
        self.assertEqual(transactions.count(), 0)

class ReportFilterFormTest(TestCase):
    def test_form_valid_data(self):
        form = ReportFilterForm(data={
            'date_from': '01-01-2023',
            'date_to': '31-01-2023'
        })
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['date_from'], date(2023, 1, 1))
        self.assertEqual(form.cleaned_data['date_to'], date(2023, 1, 31))

    def test_form_invalid_date_range(self):
        form = ReportFilterForm(data={
            'date_from': '31-01-2023',
            'date_to': '01-01-2023'
        })
        self.assertFalse(form.is_valid())
        self.assertIn('Date From cannot be after Date To.', form.non_field_errors())
        
    def test_form_missing_data(self):
        form = ReportFilterForm(data={})
        self.assertFalse(form.is_valid())
        self.assertIn('This field is required.', form.errors['date_from'])
        self.assertIn('This field is required.', form.errors['date_to'])

class CreditorsDebitorsViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create parties and transactions for view tests
        cls.creditor = Party.objects.create(
            party_id=301, 
            party_name='Creditor One', 
            party_type='C', 
            is_creditor=True, 
            is_debitor=False
        )
        cls.debitor = Party.objects.create(
            party_id=302, 
            party_name='Debitor Two', 
            party_type='D', 
            is_creditor=False, 
            is_debitor=True
        )
        AccountTransaction.objects.create(
            transaction_id=10,
            party=cls.creditor,
            transaction_date=date(2023, 5, 1),
            description='Creditor Invoice',
            amount=500.00,
            transaction_type='INV',
            financial_year_id=TEST_FINANCIAL_YEAR_ID,
            company_id=TEST_COMPANY_ID
        )
        AccountTransaction.objects.create(
            transaction_id=11,
            party=cls.debitor,
            transaction_date=date(2023, 5, 10),
            description='Debitor Payment',
            amount=250.00,
            transaction_type='PMT',
            financial_year_id=TEST_FINANCIAL_YEAR_ID,
            company_id=TEST_COMPANY_ID
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_report_view_get(self):
        response = self.client.get(reverse('creditors_debitors_detail_report', 
                                           args=[self.creditor.party_id, self.creditor.party_type]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/creditorsdebitors/report.html')
        self.assertTrue('form' in response.context)
        self.assertTrue('party' in response.context)
        self.assertEqual(response.context['party'].party_id, self.creditor.party_id)
        self.assertEqual(response.context['party_type_key'], self.creditor.party_type)

    def test_report_view_invalid_party(self):
        response = self.client.get(reverse('creditors_debitors_detail_report', 
                                           args=[999, 'C'])) # Non-existent party_id
        self.assertEqual(response.status_code, 404) # Should return 404 for non-existent party

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('accounttransaction_table_partial', 
                                           args=[self.creditor.party_id, self.creditor.party_type]),
                                   {'date_from': '2023-01-01', 'date_to': '2023-12-31'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/creditorsdebitors/_creditors_debitors_table.html')
        self.assertTrue('account_transactions' in response.context)
        self.assertEqual(response.context['account_transactions'].count(), 1) # Only one transaction for this creditor in this range
        self.assertIn('Creditor Invoice', response.content.decode())

    def test_table_partial_view_htmx_trigger(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('accounttransaction_table_partial', 
                                           args=[self.debitor.party_id, self.debitor.party_type]),
                                   {'date_from': '2023-01-01', 'date_to': '2023-12-31'},
                                   **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.headers.get('Content-Type').startswith('text/html')) # Ensure it's HTML partial
        self.assertIn('Debitor Payment', response.content.decode())

    def test_table_partial_view_invalid_date_range(self):
        response = self.client.get(reverse('accounttransaction_table_partial', 
                                           args=[self.creditor.party_id, self.creditor.party_type]),
                                   {'date_from': '2023-12-31', 'date_to': '2023-01-01'}) # Invalid range
        self.assertEqual(response.status_code, 200) # Still 200, but content should be empty table and error message
        self.assertIn("No transactions found for the selected criteria.", response.content.decode())
        # Check messages framework for error (requires middleware in settings.py)
        # self.assertIn("Date From cannot be after Date To.", [m.message for m in messages.get_messages(response.request)])

    def test_cancel_button_redirect(self):
        # Simulate a click on the cancel button (which is an anchor tag here)
        # We test the URL resolver for the name, not a direct button click since it's client-side navigation.
        url = reverse('creditors_debitors_summary')
        self.assertEqual(url, '/module/accounts/transactions/creditorsdebitors/') # Check it resolves to the expected path
```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

*   **HTMX for Dynamic Content:**
    *   The main `report.html` page uses `hx-get` and `hx-target` on a `div` (`#accountTransactionsTable-container`) to load the `_creditors_debitors_table.html` partial view on page `load` and when the filter `form` is `submit`ted.
    *   The filter `form` uses `hx-get` to submit its parameters as query strings to the `accounttransaction_table_partial` URL.
    *   No `hx-trigger` is needed for `HTMX-Trigger` headers in the response as the list view is not performing CRUD actions that would necessitate triggering updates on *other* elements. The target element (`#accountTransactionsTable-container`) is directly swapped.
*   **Alpine.js for UI State:**
    *   Minimal Alpine.js is needed for this simple report page. The example `Alpine.js component initialization` is left as a placeholder for more complex UI interactions (e.g., dynamically showing/hiding elements, managing local form state, etc.) that might arise in other migrations. For basic form submission and table display, HTMX handles most of it.
*   **DataTables for List Views:**
    *   The `_creditors_debitors_table.html` partial includes the JavaScript to initialize DataTables on the `<table>` element with ID `accountTransactionsTable`.
    *   The `htmx:afterSwap` event listener in `report.html` ensures DataTables is reinitialized correctly after the table partial is loaded or updated by HTMX, preventing issues with multiple initializations. The `destroy: true` option in DataTables initialization also helps.
*   **No Full Page Reloads:** All interactions (form submission, table refresh) are handled via HTMX, ensuring no full page reloads.
*   **DRY Templates & Tailwind CSS:**
    *   Templates extend `core/base.html` for common layout.
    *   Tailwind CSS classes (`block`, `w-full`, `px-3`, `py-2`, `border`, `rounded-md`, `shadow-sm`, `focus:outline-none`, `bg-blue-500`, `text-white`, `font-bold`, etc.) are applied directly to HTML elements for styling, replacing the ASP.NET `CssClass` attributes and inline styles.

## Final Notes

*   **Placeholders:** `[APP_NAME]` is `accounts`, `[MODEL_NAME]` is `AccountTransaction`, `[MODEL_NAME_LOWER]` is `accounttransaction`, `[MODEL_NAME_PLURAL_LOWER]` is `account_transactions`. These have been consistently replaced throughout the code.
*   **Business Logic:** The core filtering logic resides in the `AccountTransaction.get_detailed_transactions` class method, upholding the "fat model, thin view" principle. Views are kept minimal, primarily orchestrating requests and responses.
*   **Tests:** Comprehensive unit tests for models and integration tests for views are provided, aiming for high coverage.
*   **Session Management:** The ASP.NET page used `Session` to pass `SupId`, `Key`, `DtFrm`, `DtTo`. In Django, we've opted to pass `SupId` (`party_id`) and `Key` (`party_type_key`) as URL parameters to the main report page, which are then picked up by the HTMX partial view. Date filters are passed via the form. This eliminates the need for Django's session framework for these parameters, making the views more stateless and RESTful. The `financial_year_id` and `company_id` are assumed to be constants (or fetched from authenticated user's context in a real application).
*   **Crystal Reports:** The original ASP.NET code indicated Crystal Reports. This is a proprietary reporting tool. The Django solution replaces this with a standard HTML DataTables representation, but for complex reports, Django integrates well with dedicated reporting tools like `JasperReports` (via API), `ReportLab`, or custom PDF/Excel generation using libraries like `Pillow`, `OpenPyXL`, `Pisa`, etc. This modernization focuses on the *interface* rather than the specific reporting engine.