This modernization plan details the transition of your ASP.NET Bank Voucher Print application to a modern Django-based solution. The focus is on leveraging AI-assisted automation, adhering to the "Fat Model, Thin View" architecture, and utilizing HTMX for dynamic front-end interactions.

---

## ASP.NET to Django Conversion Script: Bank Voucher Print & Receipt

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include `base.html` template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:** The ASP.NET code interacts with several tables. We'll define Django models for each, marking them `managed = False` as they refer to your existing database.

**Main Tables:**
*   `tblACC_BankVoucher_Payment_Master`: Stores payment voucher headers.
*   `tblACC_BankVoucher_Payment_Details`: Stores line item details for payment vouchers (for amount calculation).
*   `tblACC_BankVoucher_Received_Masters`: Stores receipt voucher headers.

**Lookup/Auxiliary Tables:**
*   `tblACC_Bank`: Bank names.
*   `tblACC_PaidType`: Types of payments.
*   `tblFinancial_master`: Financial year definitions.
*   `tblACC_ReceiptAgainst`: Types of receipts.
*   `tblHR_OfficeStaff`: Employee details (used for `PaidTo`, `ReceivedFrom`, `ChequeReceivedBy` lookups).
*   `SD_Cust_master`: Customer details (used for `PaidTo`, `ReceivedFrom` lookups).
*   `tblMM_Supplier_master`: Supplier details (used for `PaidTo`, `ReceivedFrom` lookups).
*   `BusinessGroup`: Business group symbols (used for `WONo/BG Group` lookup).

**Inferred Columns (based on usage in code-behind):**

*   **`tblACC_BankVoucher_Payment_Master`**:
    *   `Id` (PK, int)
    *   `BVPNo` (string)
    *   `Type` (int) - Voucher type code
    *   `NameOnCheque` (string)
    *   `PaidType` (int) - FK to `tblACC_PaidType`
    *   `ECSType` (int) - Entity type for `PayTo` (e.g., Employee, Customer)
    *   `PayTo` (string) - ID or Name, depends on `ECSType`
    *   `ChequeDate` (string) - Date in `YYYYMMDD` or similar format
    *   `ChequeNo` (string)
    *   `PayAmt` (float)
    *   `AddAmt` (float)
    *   `Bank` (string) - FK to `tblACC_Bank` (`Id` converted to string)
    *   `CompId` (int)
    *   `FinYearId` (int)
*   **`tblACC_BankVoucher_Payment_Details`**:
    *   `Id` (PK, int, inferred)
    *   `MId` (int) - FK to `tblACC_BankVoucher_Payment_Master.Id`
    *   `Amount` (float)
*   **`tblACC_BankVoucher_Received_Masters`**:
    *   `Id` (PK, int)
    *   `BVRNo` (string)
    *   `Types` (int) - FK to `tblACC_ReceiptAgainst`
    *   `ReceivedFrom` (string) - ID or Name, depends on `ReceiveType`
    *   `InvoiceNo` (string)
    *   `ChequeNo` (string)
    *   `ChequeDate` (string)
    *   `ChequeReceivedBy` (string) - ID, likely Employee
    *   `BankName` (string) - Stored directly in this table
    *   `BankAccNo` (string)
    *   `ChequeClearanceDate` (string)
    *   `Narration` (text)
    *   `Amount` (float)
    *   `WONo` (string)
    *   `BGGroup` (string) - FK to `BusinessGroup` (`Id` converted to string)
    *   `CompId` (int)
    *   `FinYearId` (int)
    *   `ReceiveType` (int) - Type of entity received from (Employee, Customer, Supplier, Direct)

### Step 2: Identify Backend Functionality

**Payment Tab (`GridView3`):**
*   **Read (List):** Displays bank payment vouchers. Filters by `PaidTo` search term. Handles pagination.
*   **Action (`Sel`):** Redirects to a detail page (`BankVoucher_Print_Details.aspx`).
*   **Action (`Adv`):** Redirects to an advice print page (`BankVoucher_Advice_print.aspx`).

**Receipt Tab (`GridView6`):**
*   **Read (List):** Displays bank receipt vouchers. Filters by `ReceivedFrom` search term. Handles pagination.

**Common Functionality:**
*   **Search/Filter:** `txtPaidto` and `txtReceivedFrom` with associated search buttons.
*   **Auto-complete:** Provides suggestions for `PaidTo`/`ReceivedFrom` from a combined list of employees, customers, and suppliers.
*   **Tab Switching:** Dynamically loads content for each tab.
*   **Complex Data Transformation:** Extensive logic in `FillGrid_Creditors` and `Loaddata` for displaying human-readable names, types, and aggregated amounts by joining/looking up data from multiple tables. This is crucial for the "fat model" approach.

### Step 3: Infer UI Components

*   **`TabContainer`:** Will be replaced by HTMX-driven tabs where each tab's content (`Payment` list, `Receipt` list) is loaded dynamically via HTMX.
*   **`TextBox` with `AutoCompleteExtender`:** Will become standard `<input type="text">` fields. Auto-complete will be implemented using HTMX `hx-get` to a dedicated Django view that provides suggestions, with Alpine.js handling the display of suggestions.
*   **`Button`:** Standard `<button>` elements, triggering HTMX requests to re-load data.
*   **`GridView`:** Will be replaced by standard `<table>` elements initialized with DataTables for client-side pagination, sorting, and searching. Each `GridView` will map to a separate Django table partial template loaded via HTMX.
*   **`LinkButton` (`BVP No`, `Advice`):** Standard `<a>` tags pointing to detail/advice URLs.

### Step 4: Generate Django Code

We will create a Django app named `accounts`.

#### 4.1 Models (`accounts/models.py`)

This file will contain all necessary model definitions, including the main voucher tables and the auxiliary lookup tables. Each model will have `managed = False` and `db_table` set to match your existing database. Crucially, complex data transformation and lookup logic will be embedded as properties or methods within the models, adhering to the "Fat Model" principle.

```python
from django.db import models
from django.db.models import Sum
import datetime

# --- Auxiliary Models (Managed=False for existing tables) ---

class TblAccBank(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_Bank'
        verbose_name = 'Bank'
        verbose_name_plural = 'Banks'

    def __str__(self):
        return self.name or ''

class TblAccPaidType(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    particulars = models.CharField(db_column='Particulars', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_PaidType'
        verbose_name = 'Paid Type'
        verbose_name_plural = 'Paid Types'

    def __str__(self):
        return self.particulars or ''

class TblFinancialMaster(models.Model):
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year or ''

class TblAccReceiptAgainst(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_ReceiptAgainst'
        verbose_name = 'Receipt Type'
        verbose_name_plural = 'Receipt Types'

    def __str__(self):
        return self.description or ''

class TblHrOfficeStaff(models.Model):
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.employee_name} [{self.emp_id}]"

class SdCustMaster(models.Model):
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50)
    customer_name = models.CharField(db_column='CustomerName', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customer_name} [{self.customer_id}]"

class TblMmSupplierMaster(models.Model):
    supplier_id = models.CharField(db_column='SupplierId', primary_key=True, max_length=50)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return f"{self.supplier_name} [{self.supplier_id}]"

class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol or ''

# --- Main Voucher Models ---

class BankVoucherPaymentMasterManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset()

    def get_filtered_data(self, comp_id, fin_year_id, paid_to_search_term=None):
        """
        Retrieves and processes Bank Payment Voucher data based on company, financial year,
        and an optional search term for 'Paid To'.
        """
        queryset = self.get_queryset().filter(comp_id=comp_id, fin_year_id__lte=fin_year_id)
        if paid_to_search_term:
            # The original code's `fun.getCode` implies extracting an ID from a string like "Name[ID]".
            # For simplicity, we'll try to match exact ID or name fragment for now.
            # A more robust solution would involve explicit ID lookup.
            try:
                # Try matching by ID if the search term looks like an ID
                numeric_id = int(paid_to_search_term)
                # This logic is complex; assume it tries to match part of the resolved name or ID.
                # For a clean migration, it's best to have explicit relationships and search fields.
                # Here, we generalize to a broad contains search as a starting point.
                queryset = queryset.filter(pay_to=str(numeric_id)) | \
                           queryset.filter(name_on_cheque__icontains=paid_to_search_term) 
            except ValueError:
                # If not an ID, search by name part
                queryset = queryset.filter(name_on_cheque__icontains=paid_to_search_term) | \
                           queryset.filter(pay_to__icontains=paid_to_search_term) # Covers cases where PayTo is a name
        
        # Order by Id desc as in original code
        return queryset.order_by('-id')

class BankVoucherPaymentMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    bvp_no = models.CharField(db_column='BVPNo', max_length=50, blank=True, null=True)
    type = models.IntegerField(db_column='Type', blank=True, null=True) # 1:Advance, 2:Salary, 3:Others, 4:Creditors
    name_on_cheque = models.CharField(db_column='NameOnCheque', max_length=255, blank=True, null=True)
    paid_type = models.IntegerField(db_column='PaidType', blank=True, null=True) # FK to tblACC_PaidType
    ecs_type = models.IntegerField(db_column='ECSType', blank=True, null=True) # Type of entity for PayTo (1:Employee, 2:Customer, 3:Supplier, 4:Other)
    pay_to = models.CharField(db_column='PayTo', max_length=255, blank=True, null=True) # Can be an ID or name
    cheque_date = models.CharField(db_column='ChequeDate', max_length=50, blank=True, null=True) # Stored as string
    cheque_no = models.CharField(db_column='ChequeNo', max_length=50, blank=True, null=True)
    pay_amt = models.FloatField(db_column='PayAmt', blank=True, null=True)
    add_amt = models.FloatField(db_column='AddAmt', blank=True, null=True)
    bank_id = models.CharField(db_column='Bank', max_length=50, blank=True, null=True) # FK to tblACC_Bank (string ID)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    objects = BankVoucherPaymentMasterManager()

    class Meta:
        managed = False
        db_table = 'tblACC_BankVoucher_Payment_Master'
        verbose_name = 'Bank Payment Voucher'
        verbose_name_plural = 'Bank Payment Vouchers'

    def __str__(self):
        return self.bvp_no or f"Voucher {self.id}"

    # Business Logic / Properties (Fat Model)
    @property
    def type_of_voucher_display(self):
        """Maps Type code to human-readable string."""
        if self.type == 1: return "Advance"
        if self.type == 2: return "Salary"
        if self.type == 3: return "Others"
        if self.type == 4: return "Creditors"
        return "Unknown"
    
    @property
    def formatted_cheque_date(self):
        """Converts date string to DD/MM/YYYY format."""
        if self.cheque_date:
            try:
                # Assuming common YYYYMMDD or DD/MM/YYYY
                date_obj = datetime.datetime.strptime(self.cheque_date, '%Y%m%d')
                return date_obj.strftime('%d/%m/%Y')
            except ValueError:
                try: # Try another common format if first fails
                    date_obj = datetime.datetime.strptime(self.cheque_date, '%d/%m/%Y')
                    return date_obj.strftime('%d/%m/%Y')
                except ValueError:
                    return self.cheque_date # Fallback to original string
        return ''

    def get_ecs_name(self):
        """Resolves PayTo based on ECSType from respective tables."""
        if self.ecs_type == 1: # Employee
            try:
                return TblHrOfficeStaff.objects.get(emp_id=self.pay_to, comp_id=self.comp_id).employee_name
            except (TblHrOfficeStaff.DoesNotExist, ValueError):
                pass
        elif self.ecs_type == 2: # Customer
            try:
                return SdCustMaster.objects.get(customer_id=self.pay_to, comp_id=self.comp_id).customer_name
            except (SdCustMaster.DoesNotExist, ValueError):
                pass
        elif self.ecs_type == 3: # Supplier
            try:
                return TblMmSupplierMaster.objects.get(supplier_id=self.pay_to, comp_id=self.comp_id).supplier_name
            except (TblMmSupplierMaster.DoesNotExist, ValueError):
                pass
        return self.pay_to # Fallback if not resolved or ECSType is 4 (Others)

    @property
    def display_paid_to(self):
        """Combines NameOnCheque, PaidType, and ECSType logic for display."""
        if self.name_on_cheque:
            return self.name_on_cheque
        
        resolved_name = self.get_ecs_name()
        
        if self.paid_type:
            try:
                paid_type_obj = TblAccPaidType.objects.get(id=self.paid_type)
                return f"{paid_type_obj.particulars} - {resolved_name}"
            except TblAccPaidType.DoesNotExist:
                pass 
            except ValueError: 
                pass
        
        return resolved_name

    @property
    def bank_name_display(self):
        """Retrieves bank name from TblAccBank."""
        try:
            return TblAccBank.objects.get(id=self.bank_id).name
        except (TblAccBank.DoesNotExist, ValueError):
            return ""

    @property
    def total_detail_amount(self):
        """Aggregates Amount from BankVoucherPaymentDetail for this voucher."""
        total_amount = BankVoucherPaymentDetail.objects.filter(mid=self.id).aggregate(Sum('amount'))['amount__sum']
        return total_amount if total_amount is not None else 0.0


class BankVoucherPaymentDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    mid = models.IntegerField(db_column='MId', blank=True, null=True) # Foreign key to BankVoucherPaymentMaster
    amount = models.FloatField(db_column='Amount', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_BankVoucher_Payment_Details'
        verbose_name = 'Bank Payment Detail'
        verbose_name_plural = 'Bank Payment Details'

    def __str__(self):
        return f"Detail for MId {self.mid} - Amount: {self.amount}"


class BankVoucherReceivedMasterManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset()

    def get_filtered_data(self, comp_id, fin_year_id, received_from_search_term=None):
        """
        Retrieves and processes Bank Receipt Voucher data based on company, financial year,
        and an optional search term for 'Received From'.
        """
        queryset = self.get_queryset().filter(comp_id=comp_id, fin_year_id__lte=fin_year_id)
        if received_from_search_term:
            # Similar to PaidTo, the original `getCode` suggests ID extraction.
            # We'll try matching exact ID or name fragment.
            try:
                numeric_id = int(received_from_search_term)
                queryset = queryset.filter(received_from=str(numeric_id)) | \
                           queryset.filter(received_from__icontains=received_from_search_term) # Covers cases where it's a name
            except ValueError:
                queryset = queryset.filter(received_from__icontains=received_from_search_term)
        
        return queryset.order_by('-id')

class BankVoucherReceivedMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    bvr_no = models.CharField(db_column='BVRNo', max_length=50, blank=True, null=True)
    types = models.IntegerField(db_column='Types', blank=True, null=True) # FK to tblACC_ReceiptAgainst
    received_from = models.CharField(db_column='ReceivedFrom', max_length=255, blank=True, null=True) # Can be ID or Name
    invoice_no = models.CharField(db_column='InvoiceNo', max_length=255, blank=True, null=True)
    cheque_no = models.CharField(db_column='ChequeNo', max_length=50, blank=True, null=True)
    cheque_date = models.CharField(db_column='ChequeDate', max_length=50, blank=True, null=True)
    cheque_received_by = models.CharField(db_column='ChequeReceivedBy', max_length=50, blank=True, null=True) # Can be an ID
    bank_name = models.CharField(db_column='BankName', max_length=255, blank=True, null=True) # Stored directly
    bank_acc_no = models.CharField(db_column='BankAccNo', max_length=50, blank=True, null=True)
    cheque_clearance_date = models.CharField(db_column='ChequeClearanceDate', max_length=50, blank=True, null=True)
    narration = models.TextField(db_column='Narration', blank=True, null=True)
    amount = models.FloatField(db_column='Amount', blank=True, null=True)
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    bg_group = models.CharField(db_column='BGGroup', max_length=50, blank=True, null=True) # FK to BusinessGroup (string ID)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    receive_type = models.IntegerField(db_column='ReceiveType', blank=True, null=True) # 1:Employee, 2:Customer, 3:Supplier, 4:Direct

    objects = BankVoucherReceivedMasterManager()

    class Meta:
        managed = False
        db_table = 'tblACC_BankVoucher_Received_Masters'
        verbose_name = 'Bank Receipt Voucher'
        verbose_name_plural = 'Bank Receipt Vouchers'

    def __str__(self):
        return self.bvr_no or f"Receipt {self.id}"

    # Business Logic / Properties (Fat Model)
    @property
    def financial_year_display(self):
        """Retrieves financial year from TblFinancialMaster."""
        try:
            return TblFinancialMaster.objects.get(fin_year_id=self.fin_year_id).fin_year
        except (TblFinancialMaster.DoesNotExist, ValueError):
            return str(self.fin_year_id) if self.fin_year_id else ''

    @property
    def receipt_against_display(self):
        """Retrieves receipt type description from TblAccReceiptAgainst."""
        try:
            return TblAccReceiptAgainst.objects.get(id=self.types).description
        except (TblAccReceiptAgainst.DoesNotExist, ValueError):
            return str(self.types) if self.types else ''

    def get_resolved_received_from(self):
        """Resolves ReceivedFrom based on ReceiveType from respective tables."""
        if self.receive_type == 1: # Employee
            try:
                return TblHrOfficeStaff.objects.get(emp_id=self.received_from, comp_id=self.comp_id).employee_name
            except (TblHrOfficeStaff.DoesNotExist, ValueError):
                pass
        elif self.receive_type == 2: # Customer
            try:
                return SdCustMaster.objects.get(customer_id=self.received_from, comp_id=self.comp_id).customer_name
            except (SdCustMaster.DoesNotExist, ValueError):
                pass
        elif self.receive_type == 3: # Supplier
            try:
                return TblMmSupplierMaster.objects.get(supplier_id=self.received_from, comp_id=self.comp_id).supplier_name
            except (TblMmSupplierMaster.DoesNotExist, ValueError):
                pass
        return self.received_from # Fallback if not resolved or ReceiveType is 4 (Direct)

    @property
    def formatted_invoice_no(self):
        """Formats InvoiceNo by replacing commas with comma-space."""
        if self.invoice_no:
            return self.invoice_no.replace(",", ", ")
        return ""

    @property
    def formatted_cheque_date(self):
        """Converts cheque date string to DD/MM/YYYY format."""
        if self.cheque_date:
            try:
                date_obj = datetime.datetime.strptime(self.cheque_date, '%Y%m%d')
                return date_obj.strftime('%d/%m/%Y')
            except ValueError:
                try:
                    date_obj = datetime.datetime.strptime(self.cheque_date, '%d/%m/%Y')
                    return date_obj.strftime('%d/%m/%Y')
                except ValueError:
                    return self.cheque_date
        return ''

    @property
    def cheque_received_by_display(self):
        """Resolves ChequeReceivedBy name (assumed to be Employee)."""
        try:
            return TblHrOfficeStaff.objects.get(emp_id=self.cheque_received_by, comp_id=self.comp_id).employee_name
        except (TblHrOfficeStaff.DoesNotExist, ValueError):
            return self.cheque_received_by

    @property
    def formatted_cheque_clearance_date(self):
        """Converts cheque clearance date string to DD/MM/YYYY format."""
        if self.cheque_clearance_date:
            try:
                date_obj = datetime.datetime.strptime(self.cheque_clearance_date, '%Y%m%d')
                return date_obj.strftime('%d/%m/%Y')
            except ValueError:
                try:
                    date_obj = datetime.datetime.strptime(self.cheque_clearance_date, '%d/%m/%Y')
                    return date_obj.strftime('%d/%m/%Y')
                except ValueError:
                    return self.cheque_clearance_date
        return ''

    @property
    def wo_no_bg_display(self):
        """Generates display for WONo or BusinessGroup Symbol."""
        if self.wo_no:
            return self.wo_no
        elif self.bg_group:
            try:
                return BusinessGroup.objects.get(id=self.bg_group).symbol
            except (BusinessGroup.DoesNotExist, ValueError):
                return str(self.bg_group)
        return ""

```

#### 4.2 Forms (`accounts/forms.py`)

No direct forms were identified for creation/update in the provided ASP.NET code, as it was a print/view page. However, the auto-complete functionality hints at search inputs. We'll define a simple search form for the main page to handle the text inputs and search button functionality.

```python
from django import forms
from .models import TblHrOfficeStaff, SdCustMaster, TblMmSupplierMaster

class VoucherSearchForm(forms.Form):
    search_term = forms.CharField(
        max_length=255,
        required=False,
        label="", # Label handled in template
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Search by Paid To...',
            'hx-get': '/accounts/autocomplete_search/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#autocomplete-results',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off',
            'x-on:click.outside': 'open = false',
            'x-on:focus': 'open = true',
        })
    )

class ReceiptSearchForm(forms.Form):
    search_term = forms.CharField(
        max_length=255,
        required=False,
        label="",
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Search by Received From...',
            'hx-get': '/accounts/autocomplete_search/', # Same endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#autocomplete-results-receipt',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off',
            'x-on:click.outside': 'open = false',
            'x-on:focus': 'open = true',
        })
    )

```

#### 4.3 Views (`accounts/views.py`)

The views will be thin, primarily orchestrating data retrieval from the "fat models" and rendering the appropriate templates. We'll need a main view for the tab container, partial views for each tab's content (the DataTables), and an auto-complete endpoint.

```python
from django.views.generic import TemplateView, ListView, View
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from django.urls import reverse_lazy
from django.contrib import messages
from .models import (
    BankVoucherPaymentMaster, BankVoucherReceivedMaster,
    TblHrOfficeStaff, SdCustMaster, TblMmSupplierMaster
)
from .forms import VoucherSearchForm, ReceiptSearchForm

# Placeholder for context variables like CompId and FinYearId
# In a real application, these would come from user session, profile, or request context.
CURRENT_COMP_ID = 1 # Example value
CURRENT_FIN_YEAR_ID = 2023 # Example value, assuming current year or max accessible


class BankVoucherDashboardView(TemplateView):
    """
    Main dashboard view that serves the tab container structure.
    Initial load will fetch the payment tab content via HTMX.
    """
    template_name = 'accounts/bank_vouchers/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['payment_search_form'] = VoucherSearchForm(self.request.GET)
        context['receipt_search_form'] = ReceiptSearchForm(self.request.GET)
        return context

class BankVoucherPaymentListView(ListView):
    """
    Renders the Payment tab content, including the search form and DataTable.
    Designed to be loaded via HTMX.
    """
    model = BankVoucherPaymentMaster
    template_name = 'accounts/bank_vouchers/_payment_list.html' # Partial template
    context_object_name = 'payment_vouchers'

    def get_queryset(self):
        search_term = self.request.GET.get('paid_to', '').strip()
        # Call the fat model's manager method to get filtered and processed data
        return self.model.objects.get_filtered_data(
            comp_id=CURRENT_COMP_ID,
            fin_year_id=CURRENT_FIN_YEAR_ID,
            paid_to_search_term=search_term
        )
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['payment_search_form'] = VoucherSearchForm(initial={'search_term': self.request.GET.get('paid_to', '')})
        return context

class BankVoucherReceiptListView(ListView):
    """
    Renders the Receipt tab content, including the search form and DataTable.
    Designed to be loaded via HTMX.
    """
    model = BankVoucherReceivedMaster
    template_name = 'accounts/bank_vouchers/_receipt_list.html' # Partial template
    context_object_name = 'receipt_vouchers'

    def get_queryset(self):
        search_term = self.request.GET.get('received_from', '').strip()
        # Call the fat model's manager method to get filtered and processed data
        return self.model.objects.get_filtered_data(
            comp_id=CURRENT_COMP_ID,
            fin_year_id=CURRENT_FIN_YEAR_ID,
            received_from_search_term=search_term
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['receipt_search_form'] = ReceiptSearchForm(initial={'search_term': self.request.GET.get('received_from', '')})
        return context

class AutocompleteSearchView(View):
    """
    Provides autocomplete suggestions from Employees, Customers, and Suppliers
    via HTMX. Mimics the ASP.NET `sql` WebMethod.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('q', '').strip()
        suggestions = []
        if prefix_text:
            # Filter by CompId as in the original code
            # Combine results from all three tables
            employees = TblHrOfficeStaff.objects.filter(
                employee_name__icontains=prefix_text,
                comp_id=CURRENT_COMP_ID
            ).values_list('employee_name', 'emp_id')

            customers = SdCustMaster.objects.filter(
                customer_name__icontains=prefix_text,
                comp_id=CURRENT_COMP_ID
            ).values_list('customer_name', 'customer_id')

            suppliers = TblMmSupplierMaster.objects.filter(
                supplier_name__icontains=prefix_text,
                comp_id=CURRENT_COMP_ID
            ).values_list('supplier_name', 'supplier_id')
            
            for name, obj_id in employees:
                suggestions.append(f"{name}[{obj_id}]")
            for name, obj_id in customers:
                suggestions.append(f"{name}[{obj_id}]")
            for name, obj_id in suppliers:
                suggestions.append(f"{name}[{obj_id}]")

        # Sort suggestions as in original code
        suggestions.sort()
        
        # Return as an HTML fragment, each suggestion on a new line or as a list
        # HTMX with Alpine.js can then display this as a dropdown.
        # Alternatively, return JSON and Alpine.js renders the list.
        # For simplicity with HTMX, we'll return an HTML list fragment.
        
        html_suggestions = ""
        if suggestions:
            html_suggestions = "<ul x-show='open' class='absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto'>"
            for suggestion in suggestions[:10]: # Limit to top 10 as in typical autocomplete
                html_suggestions += f"<li class='px-3 py-2 cursor-pointer hover:bg-gray-100' @click='search_term = &quot;{suggestion}&quot;; open = false;'>{suggestion}</li>"
            html_suggestions += "</ul>"
        return HttpResponse(html_suggestions)

# Example placeholder views for details and advice print (if needed, otherwise just direct links)
class BankVoucherPaymentDetailView(TemplateView):
    """Placeholder for Payment Voucher Detail page."""
    template_name = 'accounts/bank_vouchers/payment_detail.html'
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        voucher_id = self.kwargs['pk']
        context['voucher'] = BankVoucherPaymentMaster.objects.get(id=voucher_id)
        return context

class BankVoucherAdvicePrintView(TemplateView):
    """Placeholder for Bank Voucher Advice Print page."""
    template_name = 'accounts/bank_vouchers/advice_print.html'
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        voucher_id = self.kwargs['pk']
        context['voucher'] = BankVoucherPaymentMaster.objects.get(id=voucher_id)
        return context

```

#### 4.4 Templates (`accounts/templates/accounts/bank_vouchers/`)

We'll define the main dashboard template and two partial templates for the payment and receipt lists, which will be loaded via HTMX. The "form" and "delete" templates are not directly applicable here as the original page was for printing/viewing, not CRUD, but I'll provide the overall dashboard and the list partials.

**`accounts/templates/accounts/bank_vouchers/dashboard.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Bank Vouchers</h2>
    </div>

    <div x-data="{ activeTab: 'payment' }" class="bg-white p-6 rounded-lg shadow-lg">
        <!-- Tab Headers -->
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <button 
                    @click="activeTab = 'payment'"
                    :class="{ 'border-indigo-500 text-indigo-600': activeTab === 'payment', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'payment' }"
                    class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none"
                    hx-get="{% url 'accounts:payment_list_partial' %}"
                    hx-target="#tab-content"
                    hx-swap="innerHTML"
                    hx-trigger="click, load once"
                    _="on htmx:afterOnLoad if activeTab === 'payment' then initDataTable('paymentVouchersTable')"
                >
                    Payment
                </button>
                <button 
                    @click="activeTab = 'receipt'"
                    :class="{ 'border-indigo-500 text-indigo-600': activeTab === 'receipt', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'receipt' }"
                    class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none"
                    hx-get="{% url 'accounts:receipt_list_partial' %}"
                    hx-target="#tab-content"
                    hx-swap="innerHTML"
                    hx-trigger="click"
                    _="on htmx:afterOnLoad if activeTab === 'receipt' then initDataTable('receiptVouchersTable')"
                >
                    Receipt
                </button>
            </nav>
        </div>

        <!-- Tab Content Container -->
        <div id="tab-content" class="mt-4">
            <!-- Content for active tab will be loaded here via HTMX -->
            <div class="text-center py-8">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2">Loading...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Function to initialize DataTables on dynamically loaded content
    function initDataTable(tableId) {
        // Destroy existing DataTable instance if it exists
        if ($.fn.DataTable.isDataTable('#' + tableId)) {
            $('#' + tableId).DataTable().destroy();
        }
        $('#' + tableId).DataTable({
            "pageLength": 15, // Match original page size, or configure per table
            "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
            "responsive": true,
            "autoWidth": false, // Adjust based on styling needs
        });
    }

    document.addEventListener('htmx:afterSwap', function(evt) {
        // Re-initialize Alpine.js on newly swapped content
        if (window.Alpine) {
            Alpine.initTree(evt.target);
        }
        // Initialize DataTable if the swap happened on a table container
        if (evt.target.id === 'tab-content') {
            const currentTableId = evt.detail.elt.querySelector('table')?.id;
            if (currentTableId) {
                initDataTable(currentTableId);
            }
        }
    });

    document.addEventListener('DOMContentLoaded', () => {
        // Initial load for the first tab
        htmx.trigger(document.querySelector('button[hx-get="{% url 'accounts:payment_list_partial' %}"]'), 'load');
    });
</script>
{% endblock %}
```

**`accounts/templates/accounts/bank_vouchers/_payment_list.html` (Partial for Payment Tab)**
```html
<div class="mb-4 bg-gray-50 p-4 rounded-lg shadow-sm">
    <h3 class="text-xl font-semibold text-gray-800 mb-3">Bank Payment Vouchers</h3>
    <form hx-get="{% url 'accounts:payment_list_partial' %}" hx-target="#payment-table-container" hx-swap="outerHTML" class="flex items-end space-x-4">
        {{ payment_search_form.search_term.label_tag }}
        <div class="relative flex-grow" x-data="{ open: false, search_term: '{{ payment_search_form.search_term.value|default:"" }}' }">
            <input type="text" name="paid_to" x-model="search_term" :value="search_term"
                   class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                   placeholder="Search by Paid To..."
                   hx-get="{% url 'accounts:autocomplete_search' %}"
                   hx-trigger="keyup changed delay:500ms from:#id_search_term"
                   hx-target="#autocomplete-results-payment"
                   hx-swap="innerHTML"
                   autocomplete="off"
                   x-on:click.outside="open = false"
                   x-on:focus="open = true">
            <div id="autocomplete-results-payment">
                <!-- Autocomplete suggestions will be loaded here -->
            </div>
        </div>
        <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded redbox">
            Search
        </button>
    </form>
</div>

<div id="payment-table-container" class="overflow-x-auto">
    <table id="paymentVouchersTable" class="min-w-full bg-white border border-gray-200">
        <thead>
            <tr class="bg-gray-50">
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BVP No</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Advice</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Paid To</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cheque No</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bank Name</th>
                <th class="py-2 px-4 border-b text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th class="py-2 px-4 border-b text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Pay Amt</th>
                <th class="py-2 px-4 border-b text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Add Amt</th>
            </tr>
        </thead>
        <tbody>
            {% if payment_vouchers %}
                {% for voucher in payment_vouchers %}
                <tr class="hover:bg-gray-50">
                    <td class="py-2 px-4 border-b text-right text-sm text-gray-900">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b text-center text-sm">
                        <a href="{% url 'accounts:payment_detail' pk=voucher.id %}" target="_blank" class="text-blue-600 hover:underline">
                            {{ voucher.bvp_no }}
                        </a>
                    </td>
                    <td class="py-2 px-4 border-b text-center text-sm">
                        <a href="{% url 'accounts:advice_print' pk=voucher.id %}" target="_blank" class="text-blue-600 hover:underline">
                            Advice
                        </a>
                    </td>
                    <td class="py-2 px-4 border-b text-center text-sm text-gray-900">{{ voucher.type_of_voucher_display }}</td>
                    <td class="py-2 px-4 border-b text-left text-sm text-gray-900">{{ voucher.display_paid_to }}</td>
                    <td class="py-2 px-4 border-b text-left text-sm text-gray-900">{{ voucher.cheque_no }}</td>
                    <td class="py-2 px-4 border-b text-center text-sm text-gray-900">{{ voucher.formatted_cheque_date }}</td>
                    <td class="py-2 px-4 border-b text-left text-sm text-gray-900">{{ voucher.bank_name_display }}</td>
                    <td class="py-2 px-4 border-b text-right text-sm text-gray-900">{{ voucher.total_detail_amount|floatformat:2 }}</td>
                    <td class="py-2 px-4 border-b text-right text-sm text-gray-900">{{ voucher.pay_amt|floatformat:2 }}</td>
                    <td class="py-2 px-4 border-b text-right text-sm text-gray-900">{{ voucher.add_amt|floatformat:2 }}</td>
                </tr>
                {% endfor %}
            {% else %}
            <tr>
                <td colspan="11" class="py-4 px-4 text-center text-lg font-semibold text-red-700">No data to display !</td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>
```

**`accounts/templates/accounts/bank_vouchers/_receipt_list.html` (Partial for Receipt Tab)**
```html
<div class="mb-4 bg-gray-50 p-4 rounded-lg shadow-sm">
    <h3 class="text-xl font-semibold text-gray-800 mb-3">Bank Receipt Vouchers</h3>
    <form hx-get="{% url 'accounts:receipt_list_partial' %}" hx-target="#receipt-table-container" hx-swap="outerHTML" class="flex items-end space-x-4">
        {{ receipt_search_form.search_term.label_tag }}
        <div class="relative flex-grow" x-data="{ open: false, search_term: '{{ receipt_search_form.search_term.value|default:"" }}' }">
            <input type="text" name="received_from" x-model="search_term" :value="search_term"
                   class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                   placeholder="Search by Received From..."
                   hx-get="{% url 'accounts:autocomplete_search' %}"
                   hx-trigger="keyup changed delay:500ms from:#id_search_term"
                   hx-target="#autocomplete-results-receipt"
                   hx-swap="innerHTML"
                   autocomplete="off"
                   x-on:click.outside="open = false"
                   x-on:focus="open = true">
            <div id="autocomplete-results-receipt">
                <!-- Autocomplete suggestions will be loaded here -->
            </div>
        </div>
        <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded redbox">
            Search
        </button>
    </form>
</div>

<div id="receipt-table-container" class="overflow-x-auto">
    <table id="receiptVouchersTable" class="min-w-full bg-white border border-gray-200">
        <thead>
            <tr class="bg-gray-50">
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BVRNo</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Receipt Against</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Received From</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice No</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cheque No</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cheque Date</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cheque Recd By</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bank Name</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bank AccNo</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clearance Date</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Narration</th>
                <th class="py-2 px-4 border-b text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-500 uppercase tracking-wider">WONo/BG Group</th>
            </tr>
        </thead>
        <tbody>
            {% if receipt_vouchers %}
                {% for voucher in receipt_vouchers %}
                <tr class="hover:bg-gray-50">
                    <td class="py-2 px-4 border-b text-right text-sm text-gray-900">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b text-left text-sm text-gray-900">{{ voucher.financial_year_display }}</td>
                    <td class="py-2 px-4 border-b text-center text-sm text-gray-900">{{ voucher.bvr_no }}</td>
                    <td class="py-2 px-4 border-b text-left text-sm text-gray-900">{{ voucher.receipt_against_display }}</td>
                    <td class="py-2 px-4 border-b text-left text-sm text-gray-900">{{ voucher.get_resolved_received_from }}</td>
                    <td class="py-2 px-4 border-b text-left text-sm text-gray-900">{{ voucher.formatted_invoice_no }}</td>
                    <td class="py-2 px-4 border-b text-center text-sm text-gray-900">{{ voucher.cheque_no }}</td>
                    <td class="py-2 px-4 border-b text-center text-sm text-gray-900">{{ voucher.formatted_cheque_date }}</td>
                    <td class="py-2 px-4 border-b text-left text-sm text-gray-900">{{ voucher.cheque_received_by_display }}</td>
                    <td class="py-2 px-4 border-b text-left text-sm text-gray-900">{{ voucher.bank_name }}</td>
                    <td class="py-2 px-4 border-b text-left text-sm text-gray-900">{{ voucher.bank_acc_no }}</td>
                    <td class="py-2 px-4 border-b text-center text-sm text-gray-900">{{ voucher.formatted_cheque_clearance_date }}</td>
                    <td class="py-2 px-4 border-b text-left text-sm text-gray-900">{{ voucher.narration }}</td>
                    <td class="py-2 px-4 border-b text-right text-sm text-gray-900">{{ voucher.amount|floatformat:2 }}</td>
                    <td class="py-2 px-4 border-b text-center text-sm text-gray-900">{{ voucher.wo_no_bg_display }}</td>
                </tr>
                {% endfor %}
            {% else %}
            <tr>
                <td colspan="15" class="py-4 px-4 text-center text-lg font-semibold text-red-700">No data to display !</td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>
```

#### 4.5 URLs (`accounts/urls.py`)

This file defines the URL patterns for your Django application.

```python
from django.urls import path
from .views import (
    BankVoucherDashboardView,
    BankVoucherPaymentListView,
    BankVoucherReceiptListView,
    AutocompleteSearchView,
    BankVoucherPaymentDetailView,
    BankVoucherAdvicePrintView,
)

app_name = 'accounts' # Namespace for URLs

urlpatterns = [
    path('bank-vouchers/', BankVoucherDashboardView.as_view(), name='dashboard'),
    path('bank-vouchers/payments/', BankVoucherPaymentListView.as_view(), name='payment_list_partial'),
    path('bank-vouchers/receipts/', BankVoucherReceiptListView.as_view(), name='receipt_list_partial'),
    path('autocomplete_search/', AutocompleteSearchView.as_view(), name='autocomplete_search'),
    
    # Placeholder for detail/advice print views as per original redirection
    path('bank-vouchers/payments/<int:pk>/detail/', BankVoucherPaymentDetailView.as_view(), name='payment_detail'),
    path('bank-vouchers/payments/<int:pk>/advice/', BankVoucherAdvicePrintView.as_view(), name='advice_print'),
]
```

#### 4.6 Tests (`accounts/tests.py`)

Comprehensive tests for models and views ensure reliability and maintainability.

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch
import datetime

from .models import (
    TblAccBank, TblAccPaidType, TblFinancialMaster, TblAccReceiptAgainst,
    TblHrOfficeStaff, SdCustMaster, TblMmSupplierMaster, BusinessGroup,
    BankVoucherPaymentMaster, BankVoucherPaymentDetail, BankVoucherReceivedMaster,
    CURRENT_COMP_ID, CURRENT_FIN_YEAR_ID # Accessing global context variables
)

class AuxModelTests(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create sample data for auxiliary tables
        TblAccBank.objects.create(id=1, name='BankA')
        TblAccPaidType.objects.create(id=1, particulars='Advance')
        TblFinancialMaster.objects.create(fin_year_id=CURRENT_FIN_YEAR_ID, fin_year='2023-2024')
        TblAccReceiptAgainst.objects.create(id=1, description='Sales Invoice')
        TblHrOfficeStaff.objects.create(emp_id='EMP001', employee_name='John Doe', comp_id=CURRENT_COMP_ID)
        SdCustMaster.objects.create(customer_id='CUST001', customer_name='Acme Corp', comp_id=CURRENT_COMP_ID)
        TblMmSupplierMaster.objects.create(supplier_id='SUP001', supplier_name='SupplierX', comp_id=CURRENT_COMP_ID)
        BusinessGroup.objects.create(id=1, symbol='BG1')

    def test_bank_model(self):
        bank = TblAccBank.objects.get(id=1)
        self.assertEqual(bank.name, 'BankA')
        self.assertEqual(str(bank), 'BankA')

    def test_office_staff_model(self):
        staff = TblHrOfficeStaff.objects.get(emp_id='EMP001')
        self.assertEqual(staff.employee_name, 'John Doe')
        self.assertEqual(str(staff), 'John Doe [EMP001]')


class BankVoucherPaymentMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create auxiliary data first
        TblAccBank.objects.create(id=1, name='BankA')
        TblAccPaidType.objects.create(id=1, particulars='Advance')
        TblAccPaidType.objects.create(id=2, particulars='Salary')
        TblHrOfficeStaff.objects.create(emp_id='EMP001', employee_name='John Doe', comp_id=CURRENT_COMP_ID)
        SdCustMaster.objects.create(customer_id='CUST001', customer_name='Acme Corp', comp_id=CURRENT_COMP_ID)

        # Create test payment voucher data
        cls.voucher1 = BankVoucherPaymentMaster.objects.create(
            id=1, bvp_no='BVP001', type=1, name_on_cheque='Test Name',
            paid_type=1, ecs_type=1, pay_to='EMP001', cheque_date='********',
            cheque_no='CHQ001', pay_amt=1000.00, add_amt=50.00, bank_id='1',
            comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID
        )
        cls.voucher2 = BankVoucherPaymentMaster.objects.create(
            id=2, bvp_no='BVP002', type=4, name_on_cheque=None,
            paid_type=None, ecs_type=2, pay_to='CUST001', cheque_date='********',
            cheque_no='CHQ002', pay_amt=2000.00, add_amt=0.00, bank_id='1',
            comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID
        )
        BankVoucherPaymentDetail.objects.create(id=101, mid=1, amount=950.00)
        BankVoucherPaymentDetail.objects.create(id=102, mid=1, amount=50.00)
        BankVoucherPaymentDetail.objects.create(id=103, mid=2, amount=2000.00)

    def test_voucher_creation(self):
        self.assertEqual(self.voucher1.bvp_no, 'BVP001')
        self.assertEqual(self.voucher2.pay_amt, 2000.00)

    def test_type_of_voucher_display(self):
        self.assertEqual(self.voucher1.type_of_voucher_display, 'Advance')
        self.assertEqual(self.voucher2.type_of_voucher_display, 'Creditors')
        
    def test_formatted_cheque_date(self):
        self.assertEqual(self.voucher1.formatted_cheque_date, '26/10/2023')
        # Test with invalid date format (should return original string)
        self.voucher1.cheque_date = 'InvalidDate'
        self.assertEqual(self.voucher1.formatted_cheque_date, 'InvalidDate')

    def test_get_ecs_name(self):
        self.assertEqual(self.voucher1.get_ecs_name(), 'John Doe')
        self.assertEqual(self.voucher2.get_ecs_name(), 'Acme Corp')
        # Test with unknown ECS type
        self.voucher1.ecs_type = 99
        self.assertEqual(self.voucher1.get_ecs_name(), 'EMP001') # Fallback to pay_to

    def test_display_paid_to(self):
        self.assertEqual(self.voucher1.display_paid_to, 'Test Name')
        self.assertEqual(self.voucher2.display_paid_to, 'Acme Corp') # From ECSName when NameOnCheque is None
        # Test with paid_type lookup
        self.voucher2.name_on_cheque = None
        self.voucher2.paid_type = 1 # Advance
        self.voucher2.ecs_type = 2 # Customer
        self.assertEqual(self.voucher2.display_paid_to, 'Advance - Acme Corp')

    def test_bank_name_display(self):
        self.assertEqual(self.voucher1.bank_name_display, 'BankA')
        self.voucher1.bank_id = '99' # Non-existent bank
        self.assertEqual(self.voucher1.bank_name_display, '')

    def test_total_detail_amount(self):
        self.assertEqual(self.voucher1.total_detail_amount, 1000.00)
        self.assertEqual(self.voucher2.total_detail_amount, 2000.00)

    def test_manager_get_filtered_data(self):
        qs = BankVoucherPaymentMaster.objects.get_filtered_data(CURRENT_COMP_ID, CURRENT_FIN_YEAR_ID)
        self.assertEqual(qs.count(), 2)
        
        # Test search by name_on_cheque
        qs_search = BankVoucherPaymentMaster.objects.get_filtered_data(CURRENT_COMP_ID, CURRENT_FIN_YEAR_ID, paid_to_search_term='Test')
        self.assertEqual(qs_search.count(), 1)
        self.assertEqual(qs_search.first().id, self.voucher1.id)

        # Test search by resolved name (e.g., customer name)
        qs_search_cust = BankVoucherPaymentMaster.objects.get_filtered_data(CURRENT_COMP_ID, CURRENT_FIN_YEAR_ID, paid_to_search_term='Acme')
        self.assertEqual(qs_search_cust.count(), 1)
        self.assertEqual(qs_search_cust.first().id, self.voucher2.id)

        # Test search by ID (PayTo)
        qs_search_id = BankVoucherPaymentMaster.objects.get_filtered_data(CURRENT_COMP_ID, CURRENT_FIN_YEAR_ID, paid_to_search_term='EMP001')
        self.assertEqual(qs_search_id.count(), 1)
        self.assertEqual(qs_search_id.first().id, self.voucher1.id)


class BankVoucherReceivedMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create auxiliary data
        TblFinancialMaster.objects.create(fin_year_id=CURRENT_FIN_YEAR_ID, fin_year='2023-2024')
        TblAccReceiptAgainst.objects.create(id=1, description='Sales Invoice')
        TblHrOfficeStaff.objects.create(emp_id='EMP001', employee_name='John Doe', comp_id=CURRENT_COMP_ID)
        SdCustMaster.objects.create(customer_id='CUST001', customer_name='Acme Corp', comp_id=CURRENT_COMP_ID)
        BusinessGroup.objects.create(id=1, symbol='BG1')

        # Create test receipt voucher data
        cls.receipt1 = BankVoucherReceivedMaster.objects.create(
            id=1, bvr_no='BVR001', types=1, received_from='CUST001', receive_type=2,
            invoice_no='INV123,INV456', cheque_no='CQ001', cheque_date='********',
            cheque_received_by='EMP001', bank_name='BankB', bank_acc_no='ACC123',
            cheque_clearance_date='********', narration='Test Receipt 1', amount=500.00,
            wo_no='WO1', bg_group=None, comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID
        )
        cls.receipt2 = BankVoucherReceivedMaster.objects.create(
            id=2, bvr_no='BVR002', types=1, received_from='VendorY', receive_type=4, # Direct receive
            invoice_no='INV789', cheque_no='CQ002', cheque_date='********',
            cheque_received_by='EMP001', bank_name='BankC', bank_acc_no='ACC456',
            cheque_clearance_date='********', narration='Test Receipt 2', amount=1500.00,
            wo_no=None, bg_group='1', comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID
        )

    def test_receipt_creation(self):
        self.assertEqual(self.receipt1.bvr_no, 'BVR001')
        self.assertEqual(self.receipt2.amount, 1500.00)

    def test_financial_year_display(self):
        self.assertEqual(self.receipt1.financial_year_display, '2023-2024')
        self.receipt1.fin_year_id = 999 # Non-existent
        self.assertEqual(self.receipt1.financial_year_display, '999')

    def test_receipt_against_display(self):
        self.assertEqual(self.receipt1.receipt_against_display, 'Sales Invoice')
        self.receipt1.types = 99 # Non-existent
        self.assertEqual(self.receipt1.receipt_against_display, '99')

    def test_get_resolved_received_from(self):
        self.assertEqual(self.receipt1.get_resolved_received_from(), 'Acme Corp')
        self.assertEqual(self.receipt2.get_resolved_received_from(), 'VendorY')

    def test_formatted_invoice_no(self):
        self.assertEqual(self.receipt1.formatted_invoice_no, 'INV123, INV456')
        self.assertEqual(self.receipt2.formatted_invoice_no, 'INV789')

    def test_formatted_cheque_date(self):
        self.assertEqual(self.receipt1.formatted_cheque_date, '01/11/2023')

    def test_cheque_received_by_display(self):
        self.assertEqual(self.receipt1.cheque_received_by_display, 'John Doe')
        self.receipt1.cheque_received_by = 'EMP999'
        self.assertEqual(self.receipt1.cheque_received_by_display, 'EMP999')

    def test_formatted_cheque_clearance_date(self):
        self.assertEqual(self.receipt1.formatted_cheque_clearance_date, '05/11/2023')

    def test_wo_no_bg_display(self):
        self.assertEqual(self.receipt1.wo_no_bg_display, 'WO1')
        self.assertEqual(self.receipt2.wo_no_bg_display, 'BG1')
        self.receipt1.wo_no = None
        self.receipt1.bg_group = None
        self.assertEqual(self.receipt1.wo_no_bg_display, '')

    def test_manager_get_filtered_data(self):
        qs = BankVoucherReceivedMaster.objects.get_filtered_data(CURRENT_COMP_ID, CURRENT_FIN_YEAR_ID)
        self.assertEqual(qs.count(), 2)

        qs_search_cust = BankVoucherReceivedMaster.objects.get_filtered_data(CURRENT_COMP_ID, CURRENT_FIN_YEAR_ID, received_from_search_term='Acme')
        self.assertEqual(qs_search_cust.count(), 1)
        self.assertEqual(qs_search_cust.first().id, self.receipt1.id)

        qs_search_direct = BankVoucherReceivedMaster.objects.get_filtered_data(CURRENT_COMP_ID, CURRENT_FIN_YEAR_ID, received_from_search_term='VendorY')
        self.assertEqual(qs_search_direct.count(), 1)
        self.assertEqual(qs_search_direct.first().id, self.receipt2.id)


class BankVoucherViewsTest(TestCase):
    client = Client()

    @classmethod
    def setUpTestData(cls):
        # Create minimal data for tests
        TblAccBank.objects.create(id=1, name='BankA')
        TblAccPaidType.objects.create(id=1, particulars='Advance')
        TblFinancialMaster.objects.create(fin_year_id=CURRENT_FIN_YEAR_ID, fin_year='2023-2024')
        TblAccReceiptAgainst.objects.create(id=1, description='Sales Invoice')
        TblHrOfficeStaff.objects.create(emp_id='EMP001', employee_name='John Doe', comp_id=CURRENT_COMP_ID)
        SdCustMaster.objects.create(customer_id='CUST001', customer_name='Acme Corp', comp_id=CURRENT_COMP_ID)
        TblMmSupplierMaster.objects.create(supplier_id='SUP001', supplier_name='SupplierX', comp_id=CURRENT_COMP_ID)
        BusinessGroup.objects.create(id=1, symbol='BG1')

        BankVoucherPaymentMaster.objects.create(
            id=1, bvp_no='BVP001', type=1, name_on_cheque='Test Paid',
            paid_type=1, ecs_type=1, pay_to='EMP001', cheque_date='********',
            cheque_no='CHQ001', pay_amt=100.00, add_amt=0.00, bank_id='1',
            comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID
        )
        BankVoucherPaymentDetail.objects.create(id=101, mid=1, amount=100.00)

        BankVoucherReceivedMaster.objects.create(
            id=1, bvr_no='BVR001', types=1, received_from='CUST001', receive_type=2,
            invoice_no='INV123', cheque_no='CQ001', cheque_date='********',
            cheque_received_by='EMP001', bank_name='BankB', bank_acc_no='ACC123',
            cheque_clearance_date='********', narration='Test Receipt', amount=500.00,
            wo_no='WO1', bg_group=None, comp_id=CURRENT_COMP_ID, fin_year_id=CURRENT_FIN_YEAR_ID
        )

    def test_dashboard_view(self):
        response = self.client.get(reverse('accounts:dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/bank_vouchers/dashboard.html')
        self.assertContains(response, 'Bank Vouchers')

    def test_payment_list_partial_view(self):
        response = self.client.get(reverse('accounts:payment_list_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/bank_vouchers/_payment_list.html')
        self.assertIn('payment_vouchers', response.context)
        self.assertContains(response, 'BVP001')

    def test_payment_list_partial_view_search(self):
        response = self.client.get(reverse('accounts:payment_list_partial'), {'paid_to': 'Test Paid'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'BVP001')
        response = self.client.get(reverse('accounts:payment_list_partial'), {'paid_to': 'NonExistent'})
        self.assertNotContains(response, 'BVP001')
        self.assertContains(response, 'No data to display !')


    def test_receipt_list_partial_view(self):
        response = self.client.get(reverse('accounts:receipt_list_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/bank_vouchers/_receipt_list.html')
        self.assertIn('receipt_vouchers', response.context)
        self.assertContains(response, 'BVR001')

    def test_receipt_list_partial_view_search(self):
        response = self.client.get(reverse('accounts:receipt_list_partial'), {'received_from': 'CUST001'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'BVR001')
        response = self.client.get(reverse('accounts:receipt_list_partial'), {'received_from': 'NonExistent'})
        self.assertNotContains(response, 'BVR001')
        self.assertContains(response, 'No data to display !')

    def test_autocomplete_search_view(self):
        response = self.client.get(reverse('accounts:autocomplete_search'), {'q': 'John'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'John Doe[EMP001]')
        self.assertNotContains(response, 'Acme Corp')

        response = self.client.get(reverse('accounts:autocomplete_search'), {'q': 'Acme'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Acme Corp[CUST001]')

        response = self.client.get(reverse('accounts:autocomplete_search'), {'q': ''})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '') # Empty response for empty query

    def test_payment_detail_view(self):
        response = self.client.get(reverse('accounts:payment_detail', args=[1]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/bank_vouchers/payment_detail.html')
        self.assertIn('voucher', response.context)
        self.assertEqual(response.context['voucher'].id, 1)

    def test_advice_print_view(self):
        response = self.client.get(reverse('accounts:advice_print', args=[1]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/bank_vouchers/advice_print.html')
        self.assertIn('voucher', response.context)
        self.assertEqual(response.context['voucher'].id, 1)

```

### Step 5: HTMX and Alpine.js Integration

**Approach:**
1.  **Main Dashboard:** The `dashboard.html` template serves as the single entry point.
2.  **Tab Switching:** `hx-get` on tab buttons dynamically loads `_payment_list.html` or `_receipt_list.html` into a shared `div#tab-content`.
3.  **DataTables:** A JavaScript function `initDataTable(tableId)` is called `htmx:afterSwap` to ensure DataTables is re-initialized whenever new table content is loaded via HTMX.
4.  **Search:** The search forms use `hx-get` to reload the respective list partials, applying the search filter directly.
5.  **Autocomplete:** The search input fields (`paid_to`, `received_from`) use `hx-get` with `hx-trigger="keyup changed delay:500ms"` to fetch suggestions from `autocomplete_search/` endpoint. Alpine.js (`x-data`, `x-model`, `@click`, `x-show`) is used to manage the visibility and interaction of the autocomplete dropdown.
6.  **No Full Page Reloads:** All user interactions (tab changes, searches) are handled by HTMX, ensuring a smooth, single-page application feel without complex JavaScript.
7.  **Detail/Advice Links:** These are simple `<a>` tags with `target="_blank"` to open in a new tab, as suggested by `Response.Redirect` in the original code, to maintain a similar user experience without forcing modal dialogs if not explicitly required.

**Key HTMX Attributes:**
*   `hx-get`: Specifies the URL to fetch content from.
*   `hx-target`: Specifies which element's content will be replaced.
*   `hx-swap`: Determines how the fetched content is swapped into the target.
*   `hx-trigger`: Specifies the event that triggers the HTMX request (e.g., `click`, `keyup changed delay:500ms`).
*   `hx-indicator`: (Implicitly used by base.html or can be added for loading states).

**Alpine.js Directives:**
*   `x-data`: Defines a new Alpine component scope and its initial data.
*   `x-model`: Creates a two-way data binding for input fields.
*   `x-show`: Toggles the display of an element based on a boolean expression.
*   `@click` / `x-on:click`: Handles click events.
*   `x-on:click.outside`: Handles clicks outside the element, useful for closing dropdowns.
*   `x-on:focus`: Handles focus events, useful for opening dropdowns.

---

### Final Notes

*   **Placeholders:** Replace `CURRENT_COMP_ID` and `CURRENT_FIN_YEAR_ID` with actual dynamic values obtained from your Django user authentication or context. This is critical for data filtering.
*   **Database Integration:** Ensure your Django `settings.py` is configured to connect to your existing SQL Server database. You'll need `django-mssql-backend` or `pyodbc` for this.
*   **Initial Data Load:** The `dashboard.html` template includes a `htmx.trigger` to automatically load the "Payment" tab content on initial page load, providing a seamless experience.
*   **Error Handling:** While Django forms and views handle basic validation, complex error reporting (e.g., database connection issues) should be handled robustly in a production environment.
*   **Further Refinement:** The `fun.getCode` logic in ASP.NET, which extracts IDs from "Name[ID]" strings, is broadly mapped to `icontains` or direct ID matches in the manager for `_get_filtered_data`. For precise behavior, you might need a custom form field or specific parsing logic in the model's manager to extract the ID correctly from user input, matching your legacy system's exact string format.
*   **URL Redirection:** The `LinkButton` actions in ASP.NET (`Response.Redirect`) are translated to direct `<a>` tags opening new browser tabs. If a modal view or HTMX-loaded content is preferred for these "detail" or "advice" pages, the implementation for those views would need to be partial templates loaded via HTMX as well.