The transition from legacy ASP.NET applications to modern Django-based solutions, particularly for complex reporting modules like the one identified, represents a significant leap forward in terms of maintainability, scalability, and developer efficiency. Our approach leverages AI-assisted automation to systematically analyze, translate, and modernize your existing codebase, minimizing manual effort and potential errors.

For this specific ASP.NET module, `Acc_Sundry_Details.aspx`, which primarily serves as a Crystal Report viewer for detailed sundry information, the modernization plan focuses on:
1.  **Replicating Data Logic:** Translating the intricate financial calculation logic from the C# code-behind's `TotInvQty2` method into a robust Django service layer, ensuring precise data generation.
2.  **Modern Web Presentation:** Replacing the Crystal Report viewer with an interactive HTML table powered by DataTables, HTMX, and Alpine.js for a seamless user experience.
3.  **Adhering to Best Practices:** Implementing a "Fat Model, Thin View" architecture in Django, ensuring strict separation of concerns, and maximizing code reusability.

This modernization will result in a more agile, performant, and future-proof application, enabling easier integration with other modern systems and reducing reliance on legacy technologies.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

The ASP.NET code performs complex joins and calculations across multiple tables rather than fetching directly from a single one. We infer the following key tables and their relationships from the `TotInvQty2` method's SQL queries and logic:

-   **`SD_Cust_master`**: Core customer information.
    -   `CustomerId` (Primary Key, e.g., `VARCHAR(255)`)
    -   `CustomerName` (`VARCHAR(255)`)
    -   `CompId` (`INT`)
    -   `FinYearId` (`INT`)
-   **`tblACC_SalesInvoice_Master`**: Master details for sales invoices.
    -   `Id` (Primary Key, `INT`)
    -   `InvoiceNo` (`VARCHAR(255)`)
    -   `CustomerCode` (`VARCHAR(255)`, Foreign Key to `SD_Cust_master.CustomerId`)
    -   `OtherAmt` (`DECIMAL(18,2)`)
    -   `AddType` (`INT`)
    -   `AddAmt` (`DECIMAL(18,2)`)
    -   `DeductionType` (`INT`)
    -   `Deduction` (`DECIMAL(18,2)`)
    -   `PFType` (`INT`)
    -   `PF` (`DECIMAL(18,2)`)
    -   `CENVAT` (`INT`, Foreign Key to `tblExciseser_Master.Id`)
    -   `FreightType` (`INT`)
    -   `Freight` (`DECIMAL(18,2)`)
    -   `InvoiceMode` (`VARCHAR(10)`)
    -   `CST` (`INT`, Foreign Key to `tblVAT_Master.Id`)
    -   `VAT` (`INT`, Foreign Key to `tblVAT_Master.Id`)
    -   `InsuranceType` (`INT`)
    -   `Insurance` (`DECIMAL(18,2)`)
-   **`tblACC_SalesInvoice_Details`**: Line item details for sales invoices.
    -   `Id` (Primary Key, `INT`)
    -   `MId` (`INT`, Foreign Key to `tblACC_SalesInvoice_Master.Id`)
    -   `ReqQty` (`DECIMAL(18,2)`)
    -   `AmtInPer` (`DECIMAL(18,2)`)
    -   `Rate` (`DECIMAL(18,2)`)
    -   `Unit` (`INT`, Foreign Key to `Unit_Master.Id`)
-   **`Unit_Master`**: Defines units and their effect on invoice calculation.
    -   `Id` (Primary Key, `INT`)
    -   `EffectOnInvoice` (`INT`)
-   **`tblExciseser_Master`**: Excise duty details.
    -   `Id` (Primary Key, `INT`)
    -   `AccessableValue` (`DECIMAL(10,2)`)
    -   `EDUCess` (`DECIMAL(10,2)`)
    -   `SHECess` (`DECIMAL(10,2)`)
-   **`tblVAT_Master`**: VAT/CST values.
    -   `Id` (Primary Key, `INT`)
    -   `Value` (`DECIMAL(10,2)`)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

The ASP.NET page `Acc_Sundry_Details.aspx` is exclusively a **Read** operation. It serves as a report viewer, displaying aggregated financial details for a specific customer based on complex calculations derived from multiple tables.

-   **Create:** Not present on this page.
-   **Read:** The core functionality involves retrieving customer details, sales invoices, and their associated line items, then performing a series of financial calculations (basic amount, additions, deductions, PF, excise, CST/VAT, insurance, other amounts) to generate a summary `DataTable`. This `DataTable` is then used to populate a Crystal Report.
-   **Update:** Not present on this page.
-   **Delete:** Not present on this page.
-   **Navigation/Redirection:** A "Cancel" button redirects the user to `Acc_Sundry_CustList.aspx`. Additionally, the generated report includes links to `SalesInvoice_Print_Details.aspx` for individual invoice details.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

The UI primarily consists of:

-   **`CR:CrystalReportViewer`**: This is the main display component, responsible for rendering the `Acc_Sundry_Dr_Details.rpt` report. In Django, this will be replaced by an interactive HTML table using DataTables to present the calculated data.
-   **`asp:Button ID="Button1"`**: Labeled "Cancel", this button triggers a server-side click event (`btnCancel_Click`) which redirects the user. In Django, this will be a simple link or a button using `hx-redirect` for navigation.
-   **Query String Parameters**: `CustId` and `Key` are passed via the URL, indicating dynamic content generation based on user selection or previous page context. These will be accessed via `request.GET` in Django views.

## Step 4: Generate Django Code

We will create a new Django application named `accounts` to house this modernized functionality.

### 4.1 Models

Task: Create Django models based on the identified database schema and a service class for the complex business logic.

## Instructions:

We'll define Django models that directly map to your existing database tables, using `managed = False` in their `Meta` class to avoid Django migrations modifying your legacy database. The complex `TotInvQty2` calculation will be refactored into a dedicated `SundryReportService` class, embodying the "Fat Model" principle by keeping business logic separate from the presentation layer (views).

```python
# accounts/models.py
from django.db import models
from django.db.models import F
from decimal import Decimal
import uuid # For generating unique keys

# Base class for existing tables to simplify Meta
class LegacyBaseModel(models.Model):
    class Meta:
        managed = False
        abstract = True # This tells Django not to create a table for this model

class Customer(LegacyBaseModel):
    # Assuming CustomerId is the primary key and char/string type in the legacy DB
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=255)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    company_id = models.IntegerField(db_column='CompId')
    financial_year_id = models.IntegerField(db_column='FinYearId')

    class Meta(LegacyBaseModel.Meta):
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customer_name

class Unit(LegacyBaseModel):
    unit_id = models.IntegerField(db_column='Id', primary_key=True)
    unit_name = models.CharField(db_column='UnitName', max_length=100, default='') # Assuming a name field exists for display
    effect_on_invoice = models.IntegerField(db_column='EffectOnInvoice', default=0) # 0 or 1

    class Meta(LegacyBaseModel.Meta):
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.unit_name if self.unit_name else f"Unit {self.unit_id}"

class ExciseService(LegacyBaseModel):
    excise_id = models.IntegerField(db_column='Id', primary_key=True)
    # Assuming there's a descriptive name for excise services
    service_name = models.CharField(db_column='ServiceName', max_length=255, default='N/A') 
    accessable_value = models.DecimalField(db_column='AccessableValue', max_digits=10, decimal_places=2, default=Decimal('0.0'))
    educess = models.DecimalField(db_column='EDUCess', max_digits=10, decimal_places=2, default=Decimal('0.0'))
    shecess = models.DecimalField(db_column='SHECess', max_digits=10, decimal_places=2, default=Decimal('0.0'))

    class Meta(LegacyBaseModel.Meta):
        db_table = 'tblExciseser_Master'
        verbose_name = 'Excise Service'
        verbose_name_plural = 'Excise Services'

    def __str__(self):
        return self.service_name

class Vat(LegacyBaseModel):
    vat_id = models.IntegerField(db_column='Id', primary_key=True)
    # Assuming there's a descriptive name for VAT rates
    vat_name = models.CharField(db_column='VATName', max_length=100, default='N/A')
    value = models.DecimalField(db_column='Value', max_digits=10, decimal_places=2, default=Decimal('0.0'))

    class Meta(LegacyBaseModel.Meta):
        db_table = 'tblVAT_Master'
        verbose_name = 'VAT'
        verbose_name_plural = 'VATs'

    def __str__(self):
        return f"{self.vat_name} ({self.value}%)"

class SalesInvoiceMaster(LegacyBaseModel):
    invoice_id = models.IntegerField(db_column='Id', primary_key=True)
    invoice_no = models.CharField(db_column='InvoiceNo', max_length=255)
    customer_code = models.ForeignKey(
        Customer, on_delete=models.DO_NOTHING, db_column='CustomerCode', 
        to_field='customer_id', related_name='sales_invoices'
    )
    other_amt = models.DecimalField(
        db_column='OtherAmt', max_digits=18, decimal_places=2, 
        null=True, blank=True, default=Decimal('0.0')
    )
    
    add_type = models.IntegerField(db_column='AddType', default=0) # 0 for fixed, 1 for percentage
    add_amt = models.DecimalField(db_column='AddAmt', max_digits=18, decimal_places=2, default=Decimal('0.0'))
    
    deduction_type = models.IntegerField(db_column='DeductionType', default=0) # 0 for fixed, 1 for percentage
    deduction = models.DecimalField(db_column='Deduction', max_digits=18, decimal_places=2, default=Decimal('0.0'))
    
    pf_type = models.IntegerField(db_column='PFType', default=0) # 0 for fixed, 1 for percentage
    pf = models.DecimalField(db_column='PF', max_digits=18, decimal_places=2, default=Decimal('0.0'))
    
    cenvat = models.ForeignKey(
        ExciseService, on_delete=models.DO_NOTHING, db_column='CENVAT', 
        null=True, blank=True
    )
    
    freight_type = models.IntegerField(db_column='FreightType', default=0) # 0 for fixed, 1 for percentage
    freight = models.DecimalField(db_column='Freight', max_digits=18, decimal_places=2, default=Decimal('0.0'))
    
    invoice_mode = models.CharField(db_column='InvoiceMode', max_length=10, default='1') # Assuming 1, 2, 3 as string or int
    cst = models.ForeignKey(
        Vat, on_delete=models.DO_NOTHING, db_column='CST', 
        related_name='cst_invoices', null=True, blank=True
    )
    vat = models.ForeignKey(
        Vat, on_delete=models.DO_NOTHING, db_column='VAT', 
        related_name='vat_invoices', null=True, blank=True
    )
    
    insurance_type = models.IntegerField(db_column='InsuranceType', default=0) # 0 for fixed, 1 for percentage
    insurance = models.DecimalField(db_column='Insurance', max_digits=18, decimal_places=2, default=Decimal('0.0'))

    class Meta(LegacyBaseModel.Meta):
        db_table = 'tblACC_SalesInvoice_Master'
        verbose_name = 'Sales Invoice Master'
        verbose_name_plural = 'Sales Invoice Masters'

    def __str__(self):
        return self.invoice_no

class SalesInvoiceDetail(LegacyBaseModel):
    detail_id = models.IntegerField(db_column='Id', primary_key=True)
    master_id = models.ForeignKey(
        SalesInvoiceMaster, on_delete=models.DO_NOTHING, db_column='MId', 
        related_name='details'
    )
    req_qty = models.DecimalField(db_column='ReqQty', max_digits=18, decimal_places=2, default=Decimal('0.0'))
    amt_in_per = models.DecimalField(db_column='AmtInPer', max_digits=18, decimal_places=2, default=Decimal('0.0'))
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=2, default=Decimal('0.0'))
    unit = models.ForeignKey(Unit, on_delete=models.DO_NOTHING, db_column='Unit')

    class Meta(LegacyBaseModel.Meta):
        db_table = 'tblACC_SalesInvoice_Details'
        verbose_name = 'Sales Invoice Detail'
        verbose_name_plural = 'Sales Invoice Details'

    def __str__(self):
        return f"Detail {self.detail_id} for Invoice {self.master_id.invoice_no}"

# --- Data Class to represent a row in the generated report ---
from dataclasses import dataclass

@dataclass
class SundryReportRow:
    customer_name: str
    total_amount: Decimal
    customer_code: str
    invoice_no: str
    company_id: int
    path: str
    invoice_id: int # Added for the path construction

# --- Service Class for Complex Business Logic (mimicking TotInvQty2) ---
class SundryReportService:
    """
    Service class to encapsulate the complex financial calculation logic
    from the original TotInvQty2 method, making it reusable and testable.
    This adheres to the fat model/thin view principle by centralizing business rules.
    """
    
    # Placeholder for auxiliary functions from clsFunctions
    # In a real system, these would be proper Django managers or utility classes
    @staticmethod
    def get_company_address(company_id):
        """Fetches the company address based on company_id."""
        # This would typically query a 'Company' model or a configuration table
        return f"ABC Corp. HQ, 123 Main St, City, Country (CompId: {company_id})"

    @staticmethod
    def get_debitors_opening_balance(company_id, customer_code):
        """Fetches the opening balance for a specific debtor."""
        # This would query an 'Accounts' or 'Ledger' model
        # Placeholder for demonstration
        return Decimal('1500.00')

    @staticmethod
    def get_debitor_credit(company_id, financial_year_id, customer_code):
        """Fetches the total credit for a specific debtor within a financial year."""
        # This would query an 'Accounts' or 'Ledger' model
        # Placeholder for demonstration
        return Decimal('500.00')
        
    @staticmethod
    def get_random_alphanumeric():
        """Generates a random alphanumeric string, mimicking GetRandomAlphaNumeric."""
        return uuid.uuid4().hex[:10].upper() # Generates a 10-char random string
        
    @staticmethod
    def calculate_invoice_total(invoice_master: SalesInvoiceMaster) -> Decimal:
        """
        Replicates the complex calculation logic from TotInvQty2 for a single invoice.
        This method aggregates details and applies various charges/deductions.
        """
        # Ensure Decimal type for all calculations to prevent floating point inaccuracies
        tot_qty = Decimal('0.0') # Basic
        addition = Decimal('0.0')
        deduction = Decimal('0.0')
        fda = Decimal('0.0')
        pf = Decimal('0.0')
        fdap = Decimal('0.0')
        excise = Decimal('0.0')
        fdap_ex = Decimal('0.0')
        p = Decimal('0.0') # CST/VAT part 1
        p1 = Decimal('0.0') # CST/VAT part 2
        fdap_exp = Decimal('0.0')
        fdap_exp1 = Decimal('0.0')
        insurance = Decimal('0.0')
        other_amt = Decimal(invoice_master.other_amt or '0.0') # Handle null OtherAmt
        final_total = Decimal('0.0')

        # 1. Calculate Basic (totQty)
        # Original: `sum(case when Unit_Master.EffectOnInvoice=1 then (ReqQty*(AmtInPer/100)*Rate) Else (ReqQty*Rate) End)`
        for detail in invoice_master.details.all():
            if detail.unit.effect_on_invoice == 1:
                tot_qty += (detail.req_qty * (detail.amt_in_per / 100) * detail.rate)
            else:
                tot_qty += (detail.req_qty * detail.rate)
        
        # 2. Calculate Addition 
        # Original: `Sum(case when AddType=0 then AddAmt Else ((totQty *AddAmt)/100)End)`
        if invoice_master.add_type == 0: # Fixed
            addition = invoice_master.add_amt
        else: # Percentage
            addition = (tot_qty * invoice_master.add_amt) / 100
        final_total = tot_qty + addition

        # 3. Calculate Deduction
        # Original: `Sum(case when DeductionType=0 then Deduction Else ((finaltot *Deduction)/100)End)`
        if invoice_master.deduction_type == 0: # Fixed
            deduction = invoice_master.deduction
        else: # Percentage
            deduction = (final_total * invoice_master.deduction) / 100
        fda = (final_total - deduction)

        # 4. Calculate Packing And Forwarding (PF)
        # Original: `Sum(case when PFType=0 then PF Else ((fda *PF)/100)End)`
        if invoice_master.pf_type == 0: # Fixed
            pf = invoice_master.pf
        else: # Percentage
            pf = (fda * invoice_master.pf) / 100
        fdap = (fda + pf)

        # 5. Calculate Excise (CENVAT)
        # Original: `Sum(((fdap)*((tblExciseser_Master.AccessableValue)/100) + ((fdap)*((tblExciseser_Master.AccessableValue)/100)*tblExciseser_Master.EDUCess/100)+( ((fdap)*((tblExciseser_Master.AccessableValue)/100)*tblExciseser_Master.SHECess/100))`
        if invoice_master.cenvat:
            accessable_value = invoice_master.cenvat.accessable_value
            educess_value = invoice_master.cenvat.educess
            shecess_value = invoice_master.cenvat.shecess
            
            excise_base = (fdap * (accessable_value / 100))
            excise_educess = (excise_base * educess_value / 100)
            excise_shecess = (excise_base * shecess_value / 100)
            excise = excise_base + excise_educess + excise_shecess
        fdap_ex = (fdap + excise)

        # 6. Calculate CSTVAT (within/out Maharashtra)
        # Original logic was complex and nested based on InvoiceMode and FreightType
        if invoice_master.invoice_mode == "2": # VAT Mode (within Maharashtra)
            if invoice_master.freight_type == 0: # Fixed Freight
                p = invoice_master.freight
            else: # Percentage Freight
                p = (fdap_ex * invoice_master.freight) / 100

            if invoice_master.vat:
                v = invoice_master.vat.value
                p1 = (fdap_ex + p) * (v / 100)

        elif invoice_master.invoice_mode == "3": # CST Mode (outside Maharashtra)
            if invoice_master.cst:
                v = invoice_master.cst.value
                p = (fdap_ex * (v / 100))
            
            if invoice_master.freight_type == 0: # Fixed Freight
                p1 = invoice_master.freight
            else: # Percentage Freight
                p1 = (fdap_ex + p) * (invoice_master.freight / 100)
        
        fdap_exp = fdap_ex + p
        fdap_exp1 = fdap_exp + p1

        # 7. Calculate Insurance (LIC)
        # Original: `Sum(case when InsuranceType=0 then Insurance Else ((fdapExp1 *Insurance)/100)End)`
        if invoice_master.insurance_type == 0: # Fixed
            insurance = invoice_master.insurance
        else: # Percentage
            insurance = (fdap_exp1 * invoice_master.insurance) / 100
        
        # 8. Calculate Other Amount and Final Total
        total_calculated_amount = fdap_exp1 + insurance + other_amt
        
        return round(total_calculated_amount, 2)

    @classmethod
    def get_sundry_details_for_customer(cls, company_id: int, financial_year_id: int, customer_code: str) -> list[SundryReportRow]:
        """
        Generates the list of SundryReportRow objects for a given customer,
        mimicking the TotInvQty2 method's output. This is the main report data generator.
        """
        # Fetch the customer, ensuring it exists for the given company and financial year context
        customer = Customer.objects.filter(
            customer_id=customer_code,
            company_id=company_id,
            financial_year_id__lte=financial_year_id # Matching original logic: FinYearId <= input
        ).first()

        if not customer:
            return [] # Return empty if customer not found in context

        report_rows = []
        # Fetch all sales invoices for the customer, eagerly loading related data to avoid N+1 queries
        sales_invoices = SalesInvoiceMaster.objects.filter(
            customer_code=customer_code
        ).select_related('customer_code', 'cenvat', 'cst', 'vat') \
         .prefetch_related('details__unit')

        for invoice in sales_invoices:
            calculated_amount = cls.calculate_invoice_total(invoice)
            
            # Construct the path similar to ASP.NET
            # This path points to a future Django view for SalesInvoice Print Details
            random_key = cls.get_random_alphanumeric()
            # The 'Key' parameter in the original was from Request.QueryString["Key"],
            # which likely represents a unique session/request identifier for the report.
            # For this example, we'll just use a fixed placeholder or a random one.
            # In a real scenario, this 'Key' would be passed from the original request.
            # For this context, we'll use a new random_key or infer it from the request if available.
            
            # Assuming 'sales_invoice_print_details' is the name of the new Django URL pattern
            path = f"/accounts/sales_invoice_print_details/{invoice.invoice_no}/{invoice.invoice_id}/{customer_code}/?PT=ORIGINAL FOR BUYER&ModId=11&SubModId=51&Key={random_key}&T=1" 
            
            report_rows.append(
                SundryReportRow(
                    customer_name=customer.customer_name,
                    total_amount=calculated_amount,
                    customer_code=customer.customer_id,
                    invoice_no=invoice.invoice_no,
                    company_id=company_id,
                    path=path,
                    invoice_id=invoice.invoice_id
                )
            )
        return report_rows

```

### 4.2 Forms

Task: Define a Django form for user input.

## Instructions:

Since this ASP.NET page is a report viewer and does not involve direct user input for creating or editing records, no Django forms are required for this specific module. Filtering or search capabilities would be handled by JavaScript (DataTables) on the client side, or if more complex server-side filtering was needed, a lightweight `forms.Form` could be introduced.

```python
# accounts/forms.py
# No forms are needed for this read-only report view.
# This file would typically contain ModelForms for CRUD operations.
```

### 4.3 Views

Task: Implement the report display using Django CBVs and HTMX for dynamic content.

## Instructions:

We'll use a `TemplateView` to render the initial page and a `ListView` (or a custom `View` returning JSON/HTML fragments) to power the DataTables via HTMX. The views will be kept extremely thin (5-15 lines) by delegating complex data retrieval and calculation to the `SundryReportService`.

```python
# accounts/views.py
from django.views.generic import TemplateView, View
from django.urls import reverse_lazy
from django.shortcuts import render, get_object_or_404
from django.http import HttpResponse, HttpResponseRedirect, Http404
from django.contrib import messages
from .models import SundryReportService, Customer # Import SundryReportService and any necessary models

class CustomerSundryDetailsView(TemplateView):
    """
    Main view to display the customer sundry details report page.
    This serves the initial HTML structure including the DataTables container.
    """
    template_name = 'accounts/customer_sundry_details/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # These would typically come from session or user profile in a real ERP
        # For demonstration, we'll use dummy values or get them from query parameters if needed
        # Original ASP.NET used Session["compid"], Session["finyear"], Session["username"]
        # In Django, these would be accessible via self.request.user or session data.
        
        # Example: Assuming user company_id and financial_year_id are available
        # from the authenticated user's profile or session.
        # For this example, we'll use placeholder values.
        context['company_id'] = self.request.session.get('company_id', 1) # Default or from user
        context['financial_year_id'] = self.request.session.get('financial_year_id', 2024) # Default or from user
        context['customer_code'] = self.request.GET.get('CustId')
        context['report_key'] = self.request.GET.get('Key', SundryReportService.get_random_alphanumeric())
        
        if not context['customer_code']:
            messages.error(self.request, "Customer ID is required to view sundry details.")
            # Redirect to customer list or an error page
            # return HttpResponseRedirect(reverse_lazy('customer_list')) # Example redirect
            # For this example, we'll just return an empty context.
            context['customer_name'] = "N/A"
            context['opening_balance'] = Decimal('0.0')
            context['credit_balance'] = Decimal('0.0')
            context['company_address'] = "N/A"
            return context

        # Fetch additional report header info using the service
        # These are parameters set on the Crystal Report in ASP.NET
        context['customer_name'] = Customer.objects.filter(
            customer_id=context['customer_code'],
            company_id=context['company_id']
        ).values_list('customer_name', flat=True).first() or "Unknown Customer"
        
        context['opening_balance'] = SundryReportService.get_debitors_opening_balance(
            context['company_id'], context['customer_code']
        )
        context['credit_balance'] = SundryReportService.get_debitor_credit(
            context['company_id'], context['financial_year_id'], context['customer_code']
        )
        context['company_address'] = SundryReportService.get_company_address(context['company_id'])

        return context

class CustomerSundryDetailsTablePartialView(View):
    """
    HTMX-driven view to render only the table content for DataTables.
    This is loaded dynamically into the main page.
    """
    def get(self, request, *args, **kwargs):
        company_id = request.session.get('company_id', 1) 
        financial_year_id = request.session.get('financial_year_id', 2024)
        customer_code = request.GET.get('CustId')

        if not customer_code:
            return HttpResponse("Customer ID not provided.", status=400) # Or render an error partial

        sundry_details = SundryReportService.get_sundry_details_for_customer(
            company_id, financial_year_id, customer_code
        )
        
        # If no details, render a message instead of an empty table
        if not sundry_details:
             return render(request, 'accounts/customer_sundry_details/_no_data_message.html')

        context = {
            'sundry_details': sundry_details
        }
        return render(request, 'accounts/customer_sundry_details/_sundry_details_table.html', context)

# Placeholder for the "Sales Invoice Print Details" view
class SalesInvoicePrintDetailsView(TemplateView):
    template_name = 'accounts/sales_invoice_print_details.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # This view would be responsible for displaying details of a specific invoice
        # based on InvNo, InvId, cid parameters.
        context['invoice_no'] = self.kwargs.get('invoice_no')
        context['invoice_id'] = self.kwargs.get('invoice_id')
        context['customer_code'] = self.kwargs.get('customer_code')
        # You'd fetch the actual invoice data here
        # Example: invoice = SalesInvoiceMaster.objects.get(invoice_id=context['invoice_id'])
        messages.info(self.request, f"Displaying print details for Invoice No: {context['invoice_no']} (ID: {context['invoice_id']}) for Customer: {context['customer_code']}.")
        return context

```

### 4.4 Templates

Task: Create templates for each view using DRY principles and HTMX/Alpine.js.

## Instructions:

Templates will extend `core/base.html` (not included here). The main list page will serve as a container for the DataTables, which will be loaded via an HTMX partial. All interactions are designed to be dynamic and without full page reloads.

**Main List Template (`accounts/customer_sundry_details/list.html`):**
This template sets up the overall page structure and the HTMX target for the table.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Customer Sundry Details Report</h2>
        
        {% if messages %}
            {% for message in messages %}
                <div class="mb-4 p-3 rounded-md {{ message.tags|yesno:'bg-red-100 text-red-700,bg-blue-100 text-blue-700' }}" role="alert">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}

        {% if customer_code %}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-700 mb-6">
                <div>
                    <p><strong>Customer Name:</strong> {{ customer_name }} ({{ customer_code }})</p>
                    <p><strong>Company ID:</strong> {{ company_id }}</p>
                    <p><strong>Financial Year ID:</strong> {{ financial_year_id }}</p>
                </div>
                <div>
                    <p><strong>Opening Balance:</strong> ₹{{ opening_balance|floatformat:2 }}</p>
                    <p><strong>Credit Balance:</strong> ₹{{ credit_balance|floatformat:2 }}</p>
                    <p><strong>Company Address:</strong> {{ company_address }}</p>
                </div>
            </div>

            <div class="flex justify-end items-center mb-6">
                <a href="{% url 'accounts:customer_list' %}" 
                   class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                   hx-boost="true" hx-target="body">
                    Cancel
                </a>
            </div>
        {% else %}
            <p class="text-gray-600">Please provide a Customer ID to view details. Example: /accounts/customer-sundry-details/?CustId=CUST001</p>
            <div class="flex justify-start items-center mt-6">
                <a href="{% url 'accounts:customer_list' %}" 
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
                   hx-boost="true" hx-target="body">
                    Back to Customer List
                </a>
            </div>
        {% endif %}
    </div>

    {% if customer_code %}
        <div id="sundryDetailsTable-container"
             hx-trigger="load"
             hx-get="{% url 'accounts:customer_sundry_details_table' %}?CustId={{ customer_code }}&Key={{ report_key }}"
             hx-swap="innerHTML"
             class="bg-white shadow-md rounded-lg p-6">
            <!-- DataTable will be loaded here via HTMX -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading Customer Sundry Details...</p>
            </div>
        </div>
    {% endif %}
    
    <!-- Modal for future use (if any forms were to be loaded) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('modal', () => ({
            open: false,
            // Additional Alpine.js state management can go here
        }));
    });
</script>
{% endblock %}
```

**Table Partial Template (`accounts/customer_sundry_details/_sundry_details_table.html`):**
This template contains only the HTML for the DataTable itself, loaded dynamically.

```html
<table id="customerSundryDetailsTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice No</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Calculated Amount</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for detail in sundry_details %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-sm text-gray-900">{{ detail.invoice_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-sm text-gray-900">₹{{ detail.total_amount|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-sm text-gray-900">
                <a href="{{ detail.path }}" 
                   class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded text-xs"
                   hx-boost="true" hx-target="body">
                    View Invoice
                </a>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// Initialize DataTables after content is loaded
$(document).ready(function() {
    $('#customerSundryDetailsTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 3] } // Disable sorting on SN and Actions columns
        ]
    });
});
</script>
```

**No Data Message Partial Template (`accounts/customer_sundry_details/_no_data_message.html`):**
Displayed if no sundry details are found.

```html
<div class="text-center py-10 bg-white">
    <p class="text-gray-600 text-lg">No sundry details found for this customer with the provided criteria.</p>
    <p class="text-gray-500 text-sm mt-2">Please check the Customer ID, Company ID, and Financial Year ID.</p>
</div>
```

**Sales Invoice Print Details Placeholder Template (`accounts/sales_invoice_print_details.html`):**
A placeholder for the linked `SalesInvoice_Print_Details` page.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Sales Invoice Print Details</h2>
        {% if messages %}
            {% for message in messages %}
                <div class="mb-4 p-3 rounded-md bg-blue-100 text-blue-700" role="alert">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}
        <p class="text-gray-700">This page would display the detailed print view for:</p>
        <ul class="list-disc list-inside mt-4 text-gray-600">
            <li><strong>Invoice No:</strong> {{ invoice_no }}</li>
            <li><strong>Invoice ID:</strong> {{ invoice_id }}</li>
            <li><strong>Customer Code:</strong> {{ customer_code }}</li>
        </ul>
        <p class="mt-4 text-gray-700">Additional parameters: PT, ModId, SubModId, Key, T from URL.</p>
        <div class="mt-6">
            <a href="{% url 'accounts:customer_sundry_details_list' %}?CustId={{ customer_code }}" 
               class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
               hx-boost="true" hx-target="body">
                Back to Sundry Details
            </a>
        </div>
    </div>
</div>
{% endblock %}
```

### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

We'll define clear URL patterns for the main report page, the HTMX partial for the table, and a placeholder for the `SalesInvoice_Print_Details` page.

```python
# accounts/urls.py
from django.urls import path, re_path
from .views import CustomerSundryDetailsView, CustomerSundryDetailsTablePartialView, SalesInvoicePrintDetailsView

app_name = 'accounts' # Namespace for URLs

urlpatterns = [
    # Main Sundry Details Report page
    path('customer-sundry-details/', CustomerSundryDetailsView.as_view(), name='customer_sundry_details_list'),
    
    # HTMX endpoint for the DataTables partial
    path('customer-sundry-details/table/', CustomerSundryDetailsTablePartialView.as_view(), name='customer_sundry_details_table'),

    # Placeholder for Sales Invoice Print Details. Matches original ASP.NET path structure.
    # Regex to capture invoice_no, invoice_id, and customer_code from the path
    re_path(r'sales_invoice_print_details/(?P<invoice_no>[^/]+)/(?P<invoice_id>\d+)/(?P<customer_code>[^/]+)/', 
            SalesInvoicePrintDetailsView.as_view(), 
            name='sales_invoice_print_details'),
    
    # Placeholder for the customer list page that the "Cancel" button would navigate to
    path('customer-list/', TemplateView.as_view(template_name="accounts/customer_list_placeholder.html"), name='customer_list'),
]

```
**`accounts/customer_list_placeholder.html` (Optional, for navigation testing):**
```html
{% extends 'core/base.html' %}
{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Customer List (Placeholder)</h2>
        <p class="text-gray-700">This page would display a list of customers.</p>
        <p class="mt-4 text-gray-700">From here you could select a customer to view their sundry details.</p>
        <div class="mt-6">
            <a href="{% url 'accounts:customer_sundry_details_list' %}?CustId=CUST001" 
               class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
               hx-boost="true" hx-target="body">
                View Sundry Details for CUST001 (Example)
            </a>
        </div>
    </div>
</div>
{% endblock %}
```

### 4.6 Tests

Task: Write tests for the models and views to ensure functionality and coverage.

## Instructions:

We will include comprehensive unit tests for model methods (especially the `SundryReportService` calculations) and integration tests for the views to verify correct data rendering and HTMX interactions. These tests are crucial for automated migration validation.

```python
# accounts/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from decimal import Decimal
import uuid

# Import all models and the service
from .models import (
    Customer, Unit, ExciseService, Vat, 
    SalesInvoiceMaster, SalesInvoiceDetail, 
    SundryReportService, SundryReportRow
)

class SundryReportServiceTest(TestCase):
    """
    Unit tests for the SundryReportService and its calculation logic.
    Focus on the accuracy of financial calculations and data aggregation.
    """
    
    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for core models
        cls.company_id = 1
        cls.financial_year_id = 2024
        cls.customer_code = 'CUST001'
        cls.customer_name = 'Test Customer'
        cls.invoice_no = 'INV001'
        cls.invoice_id = 101

        cls.customer = Customer.objects.create(
            customer_id=cls.customer_code,
            customer_name=cls.customer_name,
            company_id=cls.company_id,
            financial_year_id=cls.financial_year_id
        )

        cls.unit_effect = Unit.objects.create(unit_id=1, unit_name='KG', effect_on_invoice=1)
        cls.unit_no_effect = Unit.objects.create(unit_id=2, unit_name='EA', effect_on_invoice=0)
        cls.excise_service = ExciseService.objects.create(
            excise_id=1, service_name='CENVAT 10%', accessable_value=10, educess=2, shecess=1
        )
        cls.vat_cst = Vat.objects.create(vat_id=1, vat_name='CST 5%', value=5)
        cls.vat_vat = Vat.objects.create(vat_id=2, vat_name='VAT 12%', value=12)

        # Create a sample SalesInvoiceMaster for calculation tests
        cls.invoice_master_test = SalesInvoiceMaster.objects.create(
            invoice_id=201,
            invoice_no='CALC_INV_001',
            customer_code=cls.customer,
            other_amt=Decimal('10.00'),
            add_type=1, add_amt=Decimal('5.00'), # 5% addition
            deduction_type=0, deduction=Decimal('2.00'), # fixed deduction
            pf_type=1, pf=Decimal('3.00'), # 3% PF
            cenvat=cls.excise_service,
            freight_type=0, freight=Decimal('15.00'), # fixed freight
            invoice_mode="2", vat=cls.vat_vat, # VAT mode
            insurance_type=1, insurance=Decimal('1.00') # 1% insurance
        )
        SalesInvoiceDetail.objects.create(
            detail_id=301,
            master_id=cls.invoice_master_test,
            req_qty=Decimal('10'), amt_in_per=Decimal('10'), rate=Decimal('100'), unit=cls.unit_effect
        ) # Basic: 10 * (10/100) * 100 = 100
        SalesInvoiceDetail.objects.create(
            detail_id=302,
            master_id=cls.invoice_master_test,
            req_qty=Decimal('5'), amt_in_per=Decimal('0'), rate=Decimal('50'), unit=cls.unit_no_effect
        ) # Basic: 5 * 50 = 250
        # Total Initial Basic: 100 + 250 = 350

    def test_get_random_alphanumeric(self):
        """Test random alphanumeric generation."""
        key = SundryReportService.get_random_alphanumeric()
        self.assertEqual(len(key), 10)
        self.assertTrue(key.isalnum())

    def test_get_company_address(self):
        """Test company address placeholder."""
        address = SundryReportService.get_company_address(self.company_id)
        self.assertIn(str(self.company_id), address)
        self.assertIsInstance(address, str)

    def test_get_debitors_opening_balance(self):
        """Test opening balance placeholder."""
        balance = SundryReportService.get_debitors_opening_balance(self.company_id, self.customer_code)
        self.assertIsInstance(balance, Decimal)
        self.assertEqual(balance, Decimal('1500.00'))

    def test_get_debitor_credit(self):
        """Test credit balance placeholder."""
        credit = SundryReportService.get_debitor_credit(self.company_id, self.financial_year_id, self.customer_code)
        self.assertIsInstance(credit, Decimal)
        self.assertEqual(credit, Decimal('500.00'))

    def test_calculate_invoice_total(self):
        """
        Test the complex invoice total calculation based on defined logic.
        Recalculate expected value step-by-step to match C# logic.
        """
        # Initial Basic (tot_qty): 350.00 (from setUpTestData)
        tot_qty = Decimal('350.00')

        # 1. Addition: 5% of tot_qty = 350 * 0.05 = 17.50
        addition = (tot_qty * Decimal('5.00')) / 100
        self.assertEqual(addition, Decimal('17.50'))
        final_total = tot_qty + addition # 350 + 17.50 = 367.50
        self.assertEqual(final_total, Decimal('367.50'))

        # 2. Deduction: Fixed 2.00
        deduction = Decimal('2.00')
        fda = final_total - deduction # 367.50 - 2.00 = 365.50
        self.assertEqual(fda, Decimal('365.50'))

        # 3. PF: 3% of fda = 365.50 * 0.03 = 10.965 -> 10.97 (rounded)
        pf = (fda * Decimal('3.00')) / 100
        self.assertEqual(pf, Decimal('10.965'))
        fdap = fda + pf # 365.50 + 10.965 = 376.465
        self.assertEqual(fdap, Decimal('376.465'))

        # 4. Excise (CENVAT) - AccessableValue=10, EDUCess=2, SHECess=1
        # excise_base = fdap * (10/100) = 376.465 * 0.10 = 37.6465
        # excise_educess = excise_base * (2/100) = 37.6465 * 0.02 = 0.75293
        # excise_shecess = excise_base * (1/100) = 37.6465 * 0.01 = 0.376465
        # excise = 37.6465 + 0.75293 + 0.376465 = 38.775895
        excise_base = (fdap * (self.excise_service.accessable_value / 100))
        excise_educess = (excise_base * self.excise_service.educess / 100)
        excise_shecess = (excise_base * self.excise_service.shecess / 100)
        excise = excise_base + excise_educess + excise_shecess
        self.assertEqual(excise, Decimal('38.775895'))
        fdap_ex = fdap + excise # 376.465 + 38.775895 = 415.240895
        self.assertEqual(fdap_ex, Decimal('415.240895'))

        # 5. CSTVAT (InvoiceMode="2", FreightType=0, VAT=12%)
        # p (Freight): fixed 15.00
        p = Decimal('15.00')
        self.assertEqual(p, Decimal('15.00'))
        # p1 (VAT): (fdap_ex + p) * (12/100) = (415.240895 + 15.00) * 0.12 = 430.240895 * 0.12 = 51.6289074
        p1 = (fdap_ex + p) * (self.vat_vat.value / 100)
        self.assertEqual(p1, Decimal('51.6289074'))

        fdap_exp = fdap_ex + p # 415.240895 + 15.00 = 430.240895
        self.assertEqual(fdap_exp, Decimal('430.240895'))
        fdap_exp1 = fdap_exp + p1 # 430.240895 + 51.6289074 = 481.8698024
        self.assertEqual(fdap_exp1, Decimal('481.8698024'))

        # 6. Insurance: 1% of fdap_exp1 = 481.8698024 * 0.01 = 4.818698024
        insurance = (fdap_exp1 * Decimal('1.00')) / 100
        self.assertEqual(insurance, Decimal('4.818698024'))

        # 7. OtherAmt: 10.00
        other_amt = Decimal('10.00')

        # Final Total: fdap_exp1 + insurance + other_amt
        # 481.8698024 + 4.818698024 + 10.00 = 496.6885004
        # Rounded to 2 decimal places: 496.69
        expected_total = Decimal('496.69')
        calculated_total = SundryReportService.calculate_invoice_total(self.invoice_master_test)
        self.assertEqual(calculated_total, expected_total)

    def test_get_sundry_details_for_customer(self):
        """Test the main report data generation method."""
        # Create an additional invoice for the same customer to test aggregation
        invoice2 = SalesInvoiceMaster.objects.create(
            invoice_id=202,
            invoice_no='CALC_INV_002',
            customer_code=self.customer,
            other_amt=Decimal('5.00'),
            add_type=0, add_amt=Decimal('10.00'), # fixed addition
            deduction_type=1, deduction=Decimal('1.00'), # 1% deduction
            pf_type=0, pf=Decimal('5.00'), # fixed PF
            cenvat=None, # No excise
            freight_type=1, freight=Decimal('2.00'), # 2% freight
            invoice_mode="3", cst=self.vat_cst, # CST mode
            insurance_type=0, insurance=Decimal('2.00') # fixed insurance
        )
        SalesInvoiceDetail.objects.create(
            detail_id=303,
            master_id=invoice2,
            req_qty=Decimal('1'), amt_in_per=Decimal('0'), rate=Decimal('1000'), unit=self.unit_no_effect
        ) # Basic: 1000

        sundry_details = SundryReportService.get_sundry_details_for_customer(
            self.company_id, self.financial_year_id, self.customer_code
        )

        self.assertEqual(len(sundry_details), 2)
        self.assertIsInstance(sundry_details[0], SundryReportRow)
        self.assertEqual(sundry_details[0].customer_name, self.customer_name)
        self.assertEqual(sundry_details[0].customer_code, self.customer_code)
        self.assertEqual(sundry_details[0].company_id, self.company_id)
        self.assertIn(sundry_details[0].invoice_no, [self.invoice_master_test.invoice_no, invoice2.invoice_no])
        self.assertIsInstance(sundry_details[0].total_amount, Decimal)
        self.assertIsInstance(sundry_details[0].path, str)
        self.assertIn('/accounts/sales_invoice_print_details/', sundry_details[0].path)

    def test_get_sundry_details_for_non_existent_customer(self):
        """Test with a customer code that does not exist."""
        sundry_details = SundryReportService.get_sundry_details_for_customer(
            self.company_id, self.financial_year_id, 'NONEXISTENT'
        )
        self.assertEqual(len(sundry_details), 0)

class CustomerSundryDetailsViewsTest(TestCase):
    """
    Integration tests for the Django views.
    Verifies HTTP responses, template usage, context data, and HTMX interactions.
    """
    @classmethod
    def setUpTestData(cls):
        # Setup common test data for views
        cls.company_id = 1
        cls.financial_year_id = 2024
        cls.customer_code = 'CUSTVIEW001'
        cls.customer_name = 'View Test Customer'
        cls.invoice_id = 401
        cls.invoice_no = 'VIEWINV001'

        cls.customer = Customer.objects.create(
            customer_id=cls.customer_code,
            customer_name=cls.customer_name,
            company_id=cls.company_id,
            financial_year_id=cls.financial_year_id
        )

        cls.unit_no_effect = Unit.objects.create(unit_id=3, unit_name='PC', effect_on_invoice=0)
        cls.invoice_master = SalesInvoiceMaster.objects.create(
            invoice_id=cls.invoice_id,
            invoice_no=cls.invoice_no,
            customer_code=cls.customer,
            other_amt=Decimal('0.00'), add_amt=Decimal('0.00'), deduction=Decimal('0.00'),
            pf=Decimal('0.00'), freight=Decimal('0.00'), insurance=Decimal('0.00')
        )
        SalesInvoiceDetail.objects.create(
            detail_id=501, master_id=cls.invoice_master, 
            req_qty=Decimal('1'), amt_in_per=Decimal('0'), rate=Decimal('100'), unit=cls.unit_no_effect
        )

    def setUp(self):
        self.client = Client()
        # Set session data if needed by views
        session = self.client.session
        session['company_id'] = self.company_id
        session['financial_year_id'] = self.financial_year_id
        session.save()

    def test_customer_sundry_details_list_view_get_success(self):
        """
        Test the main report page loads successfully with a valid customer ID.
        """
        url = reverse('accounts:customer_sundry_details_list') + f'?CustId={self.customer_code}'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/customer_sundry_details/list.html')
        self.assertContains(response, self.customer_name)
        self.assertContains(response, "Loading Customer Sundry Details...") # Check for HTMX loading message
        self.assertContains(response, 'id="sundryDetailsTable-container"')

    def test_customer_sundry_details_list_view_missing_customer_id(self):
        """
        Test the main report page redirects or shows error if customer ID is missing.
        """
        url = reverse('accounts:customer_sundry_details_list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200) # Still 200, but with an error message
        self.assertTemplateUsed(response, 'accounts/customer_sundry_details/list.html')
        self.assertContains(response, "Customer ID is required to view sundry details.")
        self.assertNotContains(response, 'id="sundryDetailsTable-container"') # Table container should not be present

    def test_customer_sundry_details_table_partial_view_success(self):
        """
        Test the HTMX partial view for the table loads successfully.
        """
        url = reverse('accounts:customer_sundry_details_table') + f'?CustId={self.customer_code}'
        headers = {'HX-Request': 'true'} # Simulate an HTMX request
        response = self.client.get(url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/customer_sundry_details/_sundry_details_table.html')
        self.assertContains(response, self.invoice_no)
        self.assertContains(response, 'id="customerSundryDetailsTable"')
        self.assertContains(response, 'hx-boost="true"') # Ensure HTMX attributes are present

    def test_customer_sundry_details_table_partial_view_no_data(self):
        """
        Test the HTMX partial view handles cases with no data.
        """
        # Create a customer with no invoices
        no_invoice_customer_code = 'CUSTNOINV'
        Customer.objects.create(
            customer_id=no_invoice_customer_code,
            customer_name='No Invoice Customer',
            company_id=self.company_id,
            financial_year_id=self.financial_year_id
        )
        url = reverse('accounts:customer_sundry_details_table') + f'?CustId={no_invoice_customer_code}'
        headers = {'HX-Request': 'true'}
        response = self.client.get(url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/customer_sundry_details/_no_data_message.html')
        self.assertContains(response, "No sundry details found")

    def test_sales_invoice_print_details_view(self):
        """
        Test the placeholder view for sales invoice print details.
        """
        url = reverse('accounts:sales_invoice_print_details', kwargs={
            'invoice_no': self.invoice_no,
            'invoice_id': self.invoice_id,
            'customer_code': self.customer_code
        })
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/sales_invoice_print_details.html')
        self.assertContains(response, f"Invoice No: {self.invoice_no}")
        self.assertContains(response, f"Invoice ID: {self.invoice_id}")
        self.assertContains(response, f"Customer Code: {self.customer_code}")

    def test_cancel_button_redirect(self):
        """
        Test the cancel button redirects to the customer list.
        This is a conceptual test assuming a full page boost/redirect.
        """
        url = reverse('accounts:customer_sundry_details_list') + f'?CustId={self.customer_code}'
        response = self.client.get(url) # Load the page
        
        # Simulate clicking the cancel button (which is an <a> tag with hx-boost)
        # For actual hx-boost, Django's test client needs to handle it.
        # Here we simulate the target page being loaded directly.
        cancel_url = reverse('accounts:customer_list')
        response = self.client.get(cancel_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/customer_list_placeholder.html')

```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

-   **HTMX for dynamic updates:**
    -   The main `list.html` uses `hx-get` on `sundryDetailsTable-container` with `hx-trigger="load"` to fetch the `_sundry_details_table.html` partial upon page load. This ensures the DataTables content is loaded asynchronously.
    -   The `Cancel` button (an `<a>` tag) uses `hx-boost="true"` and `hx-target="body"` to simulate a full page navigation within HTMX, making the transition smooth without a hard reload.
    -   Invoice links generated in the `SundryReportService` are constructed to point to a new Django view (`sales_invoice_print_details`) and also use `hx-boost="true"` and `hx-target="body"` for seamless navigation.

-   **Alpine.js for UI state management:**
    -   A basic Alpine.js `x-data` component for `modal` is included in `list.html`. While not actively used for forms in this specific report, it provides the framework for future modal interactions (e.g., if "Add/Edit" forms were introduced for underlying data).

-   **DataTables for list views:**
    -   The `_sundry_details_table.html` partial includes a `<table>` with the ID `customerSundryDetailsTable`.
    -   A `<script>` block within this partial initializes DataTables on this table using `$(document).ready()`. This ensures DataTables is applied once the partial is loaded into the DOM by HTMX. This setup provides client-side searching, sorting, and pagination for the report data.

-   **Seamless interactions:**
    -   By using HTMX to fetch only the table content, the initial page load is faster. Subsequent filtering/sorting are handled by DataTables client-side. Navigation to other pages via `hx-boost` provides a Single-Page Application (SPA) like feel without the complexity of a full JavaScript framework.

## Final Notes

This comprehensive modernization plan provides a clear, actionable roadmap for transitioning your `Acc_Sundry_Details` ASP.NET module to a modern Django solution.

-   **Replace Placeholders:** In a real-world scenario, you would populate `company_id`, `financial_year_id`, and `customer_code` (and potentially `Key`) from your Django authentication system (e.g., `self.request.user.profile.company_id`) or through robust request parameter handling.
-   **DRY Templates:** The use of `_sundry_details_table.html` as a partial is a prime example of DRY principles, allowing the table to be loaded dynamically without re-rendering the entire page.
-   **Business Logic in Models:** The `SundryReportService` is critical for maintaining a clean separation of concerns, ensuring that complex calculations are isolated and testable.
-   **Comprehensive Tests:** The provided `tests.py` serves as a foundation for verifying the correctness of your data models, calculation logic, and view interactions, ensuring the migrated functionality is robust and reliable.
-   **HTMX and Alpine.js:** The chosen frontend stack dramatically simplifies client-side interactivity, avoiding the need for heavy JavaScript frameworks and keeping the codebase lean and maintainable.

This approach not only converts the legacy application but fundamentally modernizes its architecture, preparing it for future enhancements and integrations.