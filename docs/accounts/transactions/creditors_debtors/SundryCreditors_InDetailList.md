## ASP.NET to Django Conversion Script: Sundry Creditors Detail List

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

This ASP.NET page acts primarily as a filtering mechanism and a container for another detailed report view (`SundryCreditors_InDetailView.aspx`) loaded within an iframe. The core functionality is to allow users to select a date range to filter a list of sundry creditor transactions for a specific creditor and category.

Our Django modernization will replace this pattern with:
1.  A main Django page (`SundryCreditorDetailListView`) that displays the date filter form.
2.  An HTMX-loaded partial view (`CreditorTransactionTablePartialView`) that dynamically fetches and displays the filtered list of transactions within a `div` on the same page, replacing the iframe concept.
3.  All interactions (search, refresh) will be handled via HTMX without full page reloads.
4.  DataTables will manage the client-side presentation of the transaction list.

---

### Step 1: Extract Database Schema

**Analysis:** The ASP.NET code does not directly perform CRUD operations on a database table within `SundryCreditors_InDetailList.aspx.cs`. It relies on session variables and query strings (`SupId`, `Key`, `lnkFor`, `txtFrmDt`, `txtToDt`) to pass parameters to `SundryCreditors_InDetailView.aspx`. This implies that `SundryCreditors_InDetailView.aspx` is responsible for fetching and displaying the actual data. Based on the context "Sundry Creditors: In Detail List" and filtering by date, we infer a table storing individual transactions or ledger entries for sundry creditors.

**Inferred Tables and Columns:**

*   **`tblSundryCreditors` (Inferred Context Table for `SupId` and `lnkFor`):**
    *   `SupId` (PK, INTEGER)
    *   `SupName` (VARCHAR, e.g., 'Supplier A', 'Supplier B')
    *   `CategoryName` (VARCHAR, corresponding to `lnkFor`, e.g., 'Local', 'International')
    *   `Key` (VARCHAR, a unique identifier or additional filter)

*   **`tblCreditorTransactions` (Primary Data Table for "In Detail List"):**
    *   `TransactionId` (PK, INTEGER)
    *   `SupId` (FK to `tblSundryCreditors`, INTEGER)
    *   `TransactionDate` (DATE)
    *   `Amount` (DECIMAL)
    *   `Description` (VARCHAR)
    *   `ReferenceNo` (VARCHAR)
    *   `TransactionType` (VARCHAR, e.g., 'Payment', 'Invoice')
    *   `Balance` (DECIMAL, calculated or stored)

**Django Model Mapping:**
*   `[TABLE_NAME]` for the main list will be `tblCreditorTransactions`.
*   `[MODEL_NAME]` will be `CreditorTransaction`.

### Step 2: Identify Backend Functionality

**Analysis:**
*   **Read (R):** The primary function is to read and display a list of sundry creditor transactions.
*   **Filtering:** The list is filtered by:
    *   `SupId` (Supplier ID, from query string)
    *   `Key` (an additional identifier, from query string)
    *   `lnkFor` (Category/Purpose, from query string, displayed in `lblOf`)
    *   `txtFrmDt` (From Date, user input)
    *   `txtToDt` (To Date, user input)
*   **Validation:** Dates are validated for presence and format (`dd-MM-yyyy`), and `From Date` must be less than or equal to `To Date`.
*   **Navigation:** A "Cancel" button redirects to another ASP.NET page.

**No Create, Update, Delete (CUD) functionality is directly exposed by *this* ASP.NET page for the transaction data.** This page is a report viewer/filter.

### Step 3: Infer UI Components

**Analysis:**
*   **`lblOf` (Label):** Displays the category name (`lnkFor`).
*   **`txtFrmDt` (TextBox) & `txtToDt` (TextBox):** Date input fields for filtering.
*   **`CalendarExtender`:** Provides a calendar pop-up for date selection.
*   **`RegularExpressionValidator`:** Client-side date format validation.
*   **`btnSearch` (Button):** Triggers the filtering based on dates.
*   **`btnCancel` (Button):** Navigates back to a parent list.
*   **`ifrm` (iframe):** The container for the actual detailed list/report, which will be replaced by an HTMX-loaded `div`.

### Step 4: Generate Django Code

We will create a Django app named `accounts`.

#### 4.1 Models (`accounts/models.py`)

We will define two models: `SundryCreditor` for context, and `CreditorTransaction` for the list data.

```python
from django.db import models
from django.utils import timezone

class SundryCreditor(models.Model):
    """
    Represents a Sundry Creditor.
    Inferred from SupId, SupName, Key, and CategoryName context in the ASP.NET application.
    This model might not have direct CRUD operations on this page, but provides context.
    """
    sup_id = models.IntegerField(db_column='SupId', primary_key=True)
    sup_name = models.CharField(db_column='SupName', max_length=255, blank=True, null=True)
    category_name = models.CharField(db_column='CategoryName', max_length=255, blank=True, null=True)
    key_value = models.CharField(db_column='Key', max_length=255, blank=True, null=True) # Renamed to avoid Python 'key' keyword conflict

    class Meta:
        managed = False  # Set to False if mapping to an existing database table
        db_table = 'tblSundryCreditors' # Inferred table name
        verbose_name = 'Sundry Creditor'
        verbose_name_plural = 'Sundry Creditors'

    def __str__(self):
        return self.sup_name or f"Creditor {self.sup_id}"

class CreditorTransaction(models.Model):
    """
    Represents a single transaction for a Sundry Creditor.
    This is the data that would be displayed in the "In Detail List".
    """
    transaction_id = models.IntegerField(db_column='TransactionId', primary_key=True)
    creditor = models.ForeignKey(SundryCreditor, on_delete=models.DO_NOTHING, db_column='SupId', related_name='transactions')
    transaction_date = models.DateField(db_column='TransactionDate')
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=2)
    description = models.CharField(db_column='Description', max_length=500, blank=True, null=True)
    reference_no = models.CharField(db_column='ReferenceNo', max_length=100, blank=True, null=True)
    transaction_type = models.CharField(db_column='TransactionType', max_length=50, blank=True, null=True)
    balance = models.DecimalField(db_column='Balance', max_digits=18, decimal_places=2, blank=True, null=True)

    class Meta:
        managed = False  # Set to False if mapping to an existing database table
        db_table = 'tblCreditorTransactions' # Inferred table name
        verbose_name = 'Creditor Transaction'
        verbose_name_plural = 'Creditor Transactions'
        ordering = ['transaction_date']

    def __str__(self):
        return f"Transaction {self.transaction_id} for {self.creditor.sup_name}"

    @classmethod
    def get_filtered_transactions(cls, sup_id, link_for, date_from=None, date_to=None):
        """
        Business logic to filter creditor transactions.
        Moved from view to model (fat model).
        """
        queryset = cls.objects.filter(creditor__sup_id=sup_id, creditor__category_name=link_for)

        if date_from:
            queryset = queryset.filter(transaction_date__gte=date_from)
        if date_to:
            queryset = queryset.filter(transaction_date__lte=date_to)

        return queryset
```

#### 4.2 Forms (`accounts/forms.py`)

A form for the date range filter.

```python
from django import forms
import datetime

class DateRangeSearchForm(forms.Form):
    """
    Form for date range search functionality.
    Corresponds to txtFrmDt and txtToDt.
    """
    from_date = forms.DateField(
        label='Date From',
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date', # HTML5 date input
            'class': 'box3 w-[100px] block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        })
    )
    to_date = forms.DateField(
        label='To',
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date', # HTML5 date input
            'class': 'box3 w-[100px] block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        })
    )

    def clean(self):
        cleaned_data = super().clean()
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')

        if from_date and to_date:
            if from_date > to_date:
                self.add_error('from_date', 'From Date cannot be after To Date.')
        elif from_date and not to_date:
            # If only from_date is provided, set to_date to today for a valid range
            cleaned_data['to_date'] = datetime.date.today()
        elif to_date and not from_date:
            # If only to_date is provided, set from_date to a reasonable past date (e.g., 1 year ago)
            cleaned_data['from_date'] = to_date - datetime.timedelta(days=365) # Example, adjust as needed
        
        return cleaned_data

```

#### 4.3 Views (`accounts/views.py`)

Two views will be created: one for the main page displaying the filter and the container, and another for the HTMX-loaded table content.

```python
from django.views.generic import TemplateView, View
from django.http import HttpResponse, HttpResponseRedirect
from django.urls import reverse_lazy
from django.shortcuts import get_object_or_404, render
from django.contrib import messages

from .models import SundryCreditor, CreditorTransaction
from .forms import DateRangeSearchForm

class SundryCreditorDetailListView(TemplateView):
    """
    Main view for Sundry Creditors In Detail List page.
    Handles the display of the date filter form and the container for the HTMX-loaded table.
    Corresponds to SundryCreditors_InDetailList.aspx.
    """
    template_name = 'accounts/sundrycreditors/detail_list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Retrieve parameters from URL (or query string)
        sup_id = self.kwargs.get('sup_id')
        link_for = self.kwargs.get('link_for')
        # 'Key' is from Request.QueryString, so it will be in self.request.GET
        key_value = self.request.GET.get('Key') 

        try:
            creditor = get_object_or_404(SundryCreditor, sup_id=sup_id, category_name=link_for, key_value=key_value)
            context['sundry_creditor_name'] = creditor.sup_name # For lblOf
            context['link_for_category'] = link_for # For lblOf (GetCategory)
        except:
            # Handle cases where creditor doesn't exist for the given params
            messages.error(self.request, "Invalid creditor or category information.")
            context['sundry_creditor_name'] = "N/A"
            context['link_for_category'] = "N/A"

        context['form'] = DateRangeSearchForm(self.request.GET) # Pre-populate form if dates in GET
        
        # Pass parameters required for the HTMX table loading
        context['sup_id'] = sup_id
        context['link_for'] = link_for
        context['key_value'] = key_value

        return context

class CreditorTransactionTablePartialView(View):
    """
    View to render the Creditor Transaction table partial, used by HTMX.
    Replaces the content displayed by SundryCreditors_InDetailView.aspx.
    """
    def get(self, request, sup_id, link_for):
        form = DateRangeSearchForm(request.GET)
        
        from_date = None
        to_date = None
        key_value = request.GET.get('Key') # Get 'Key' from query parameters

        if form.is_valid():
            from_date = form.cleaned_data.get('from_date')
            to_date = form.cleaned_data.get('to_date')
        else:
            # If initial form is invalid, or no dates provided, still attempt to load
            # You might want to add error messages here if necessary
            pass
        
        # Call the fat model method to get filtered data
        creditor_transactions = CreditorTransaction.get_filtered_transactions(
            sup_id=sup_id, 
            link_for=link_for, 
            date_from=from_date, 
            date_to=to_date
        )

        context = {
            'creditor_transactions': creditor_transactions,
            'sup_id': sup_id,
            'link_for': link_for,
            'key_value': key_value, # Pass key_value to the template if needed
        }
        return render(request, 'accounts/sundrycreditors/_creditor_transaction_table.html', context)

    def post(self, request, sup_id, link_for):
        # This handles the form submission when 'Search' button is clicked
        form = DateRangeSearchForm(request.POST)
        
        from_date = None
        to_date = None
        key_value = request.POST.get('key_value_hidden') # Retrieve from hidden input

        if form.is_valid():
            from_date = form.cleaned_data.get('from_date')
            to_date = form.cleaned_data.get('to_date')
            messages.success(request, "Transactions filtered successfully.")
        else:
            messages.error(request, "Please correct the date range errors.")
            # If form is invalid, we might want to re-render the form with errors,
            # but for HTMX refresh, just return status 204 with trigger.
            # Errors will be handled by the client-side validation if implemented.
            # For simplicity, if errors, just don't apply the filter effectively.

        # Prepare URL for HX-Trigger to refresh the table
        # We need to construct the GET parameters for the table partial correctly
        params = {
            'from_date': from_date.strftime('%Y-%m-%d') if from_date else '',
            'to_date': to_date.strftime('%Y-%m-%d') if to_date else '',
            'Key': key_value if key_value else ''
        }
        query_string = '&'.join([f"{k}={v}" for k, v in params.items() if v])
        
        # Trigger the table refresh, passing the new filter parameters
        response = HttpResponse(
            status=204, # No content to send back, just trigger a client-side event
            headers={
                'HX-Trigger': f'refreshCreditorTransactionList:{query_string}' 
            }
        )
        return response

# Placeholder for the "Cancel" button redirection target
class SundryCreditorsParentListView(TemplateView):
    template_name = 'accounts/sundrycreditors/parent_list.html' # Assuming a parent list page

```

#### 4.4 Templates (`accounts/templates/accounts/sundrycreditors/`)

**`detail_list.html`** (Main Page, replaces `.aspx` file)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <table align="center" style="width: 100%" cellpadding="0" cellspacing="0">
        <tr>
            <td align="left" valign="middle" class="bg-gray-700 text-white p-3 font-bold style4" colspan="2">
                &nbsp;Sundry Creditors: 
                <span id="lblOf">{{ link_for_category }}</span>
            </td>
        </tr>
        <tr>
            <td align="center" class="h-[27px] py-4 bg-gray-100">
                <form 
                    hx-post="{% url 'accounts:creditor_transactions_search' sup_id=sup_id link_for=link_for %}" 
                    hx-swap="none" 
                    class="inline-block space-x-2"
                    id="dateFilterForm"
                >
                    {% csrf_token %}
                    <label for="{{ form.from_date.id_for_label }}" class="style5 text-sm font-bold">Date From:</label>
                    {{ form.from_date }}
                    {% if form.from_date.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.from_date.errors }}</p>
                    {% endif %}
                    
                    <span class="style5 font-bold">-</span>
                    <label for="{{ form.to_date.id_for_label }}" class="style5 text-sm font-bold">To:</label>
                    {{ form.to_date }}
                    {% if form.to_date.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.to_date.errors }}</p>
                    {% endif %}

                    <!-- Hidden fields to pass context parameters -->
                    <input type="hidden" name="sup_id_hidden" value="{{ sup_id }}">
                    <input type="hidden" name="link_for_hidden" value="{{ link_for }}">
                    <input type="hidden" name="key_value_hidden" value="{{ key_value }}">
                    
                    <button 
                        type="submit" 
                        class="redbox bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-3 rounded text-sm">
                        Search
                    </button>
                    <a href="{% url 'accounts:sundry_creditors_parent_list' %}" 
                       class="redbox bg-gray-500 hover:bg-gray-700 text-white font-bold py-1 px-3 rounded text-sm inline-block">
                        Cancel
                    </a>
                </form>
            </td>
        </tr>
        <tr>
            <td align="center" class="py-4">
                <div id="creditorTransactionTableContainer"
                     hx-get="{% url 'accounts:creditor_transactions_table_partial' sup_id=sup_id link_for=link_for %}?Key={{ key_value }}"
                     hx-trigger="load, refreshCreditorTransactionList from:body"
                     hx-target="this"
                     hx-swap="innerHTML">
                    <!-- Loading indicator -->
                    <div class="text-center p-8">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p class="mt-2 text-gray-600">Loading Creditor Transactions...</p>
                    </div>
                </div>
            </td>
        </tr>
    </table>
</div>
{% endblock %}

{% block extra_js %}
<!-- DataTables JS and CSS (from CDN, base.html) -->
<!-- <script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script> -->
<script>
    // This Alpine.js component manages messages and form state, if needed
    document.addEventListener('alpine:init', () => {
        Alpine.data('sundryCreditorApp', () => ({
            init() {
                // Listen for custom event triggered by HTMX on form submission
                this.$el.addEventListener('refreshCreditorTransactionList', (event) => {
                    const params = event.detail.value; // The query string from HX-Trigger
                    // Manually update the hx-get attribute of the table container to include new parameters
                    const tableContainer = document.getElementById('creditorTransactionTableContainer');
                    const baseUrl = tableContainer.getAttribute('hx-get').split('?')[0]; // Remove existing query string
                    tableContainer.setAttribute('hx-get', `${baseUrl}?${params}`);
                    htmx.trigger(tableContainer, 'refreshCreditorTransactionList'); // Retrigger hx-get on container
                });
            }
        }));
    });
</script>
{% endblock %}
```

**`_creditor_transaction_table.html`** (Partial for HTMX Loading)

```html
<table id="creditorTransactionTable" class="min-w-full bg-white border border-gray-300 shadow-sm rounded-lg overflow-hidden">
    <thead>
        <tr class="bg-gray-200 text-gray-700 uppercase text-sm leading-normal">
            <th class="py-3 px-6 text-left">SN</th>
            <th class="py-3 px-6 text-left">Date</th>
            <th class="py-3 px-6 text-left">Description</th>
            <th class="py-3 px-6 text-left">Reference No.</th>
            <th class="py-3 px-6 text-right">Amount</th>
            <th class="py-3 px-6 text-right">Balance</th>
            <!-- Add more headers as per CreditorTransaction model -->
        </tr>
    </thead>
    <tbody class="text-gray-600 text-sm font-light">
        {% for transaction in creditor_transactions %}
        <tr class="border-b border-gray-200 hover:bg-gray-100">
            <td class="py-3 px-6 text-left whitespace-nowrap">{{ forloop.counter }}</td>
            <td class="py-3 px-6 text-left">{{ transaction.transaction_date|date:"d-M-Y" }}</td>
            <td class="py-3 px-6 text-left">{{ transaction.description }}</td>
            <td class="py-3 px-6 text-left">{{ transaction.reference_no }}</td>
            <td class="py-3 px-6 text-right">{{ transaction.amount }}</td>
            <td class="py-3 px-6 text-right">{{ transaction.balance }}</td>
            <!-- Add more cells -->
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-3 px-6 text-center">No transactions found for the selected criteria.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Initialize DataTables on the loaded table
    $(document).ready(function() {
        $('#creditorTransactionTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true
        });
    });
</script>
```

#### 4.5 URLs (`accounts/urls.py`)

```python
from django.urls import path
from .views import SundryCreditorDetailListView, CreditorTransactionTablePartialView, SundryCreditorsParentListView

app_name = 'accounts' # Namespace for URLs

urlpatterns = [
    # Main page for Sundry Creditors In Detail List, accepting SupId and lnkFor
    # Example URL: /accounts/sundry-creditors/detail-list/123/Local/?Key=XYZ
    path(
        'sundry-creditors/detail-list/<int:sup_id>/<str:link_for>/', 
        SundryCreditorDetailListView.as_view(), 
        name='sundry_creditors_detail_list'
    ),
    
    # HTMX endpoint for the transaction table partial
    # This view will receive GET requests with date filters and other context
    path(
        'sundry-creditors/table-partial/<int:sup_id>/<str:link_for>/', 
        CreditorTransactionTablePartialView.as_view(), 
        name='creditor_transactions_table_partial'
    ),
    
    # HTMX POST endpoint for the search form. It triggers a refresh of the table partial.
    path(
        'sundry-creditors/search/<int:sup_id>/<str:link_for>/',
        CreditorTransactionTablePartialView.as_view(), # Re-use the view, POST method will handle trigger
        name='creditor_transactions_search'
    ),

    # Placeholder for the "Cancel" button redirection target
    path(
        'sundry-creditors/parent-list/', 
        SundryCreditorsParentListView.as_view(), 
        name='sundry_creditors_parent_list'
    ),
]
```

#### 4.6 Tests (`accounts/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date
from decimal import Decimal

from .models import SundryCreditor, CreditorTransaction
from .forms import DateRangeSearchForm

class SundryCreditorModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.creditor1 = SundryCreditor.objects.create(
            sup_id=1, sup_name='Test Creditor A', category_name='Local', key_value='KEY1'
        )
        cls.creditor2 = SundryCreditor.objects.create(
            sup_id=2, sup_name='Test Creditor B', category_name='International', key_value='KEY2'
        )

    def test_sundry_creditor_creation(self):
        self.assertEqual(self.creditor1.sup_name, 'Test Creditor A')
        self.assertEqual(self.creditor1.category_name, 'Local')
        self.assertEqual(self.creditor1.key_value, 'KEY1')

    def test_str_method(self):
        self.assertEqual(str(self.creditor1), 'Test Creditor A')
        
    def test_verbose_name(self):
        self.assertEqual(SundryCreditor._meta.verbose_name, 'Sundry Creditor')
        self.assertEqual(SundryCreditor._meta.verbose_name_plural, 'Sundry Creditors')

class CreditorTransactionModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.creditor = SundryCreditor.objects.create(
            sup_id=1, sup_name='Test Creditor', category_name='Local', key_value='ABC'
        )
        # Create test transactions
        cls.trans1 = CreditorTransaction.objects.create(
            transaction_id=101, creditor=cls.creditor, transaction_date=date(2023, 1, 15), 
            amount=Decimal('100.00'), description='Invoice 001', reference_no='INV001', 
            transaction_type='Invoice', balance=Decimal('100.00')
        )
        cls.trans2 = CreditorTransaction.objects.create(
            transaction_id=102, creditor=cls.creditor, transaction_date=date(2023, 1, 20), 
            amount=Decimal('50.00'), description='Payment 001', reference_no='PAY001', 
            transaction_type='Payment', balance=Decimal('50.00')
        )
        cls.trans3 = CreditorTransaction.objects.create(
            transaction_id=103, creditor=cls.creditor, transaction_date=date(2023, 2, 10), 
            amount=Decimal('75.00'), description='Invoice 002', reference_no='INV002', 
            transaction_type='Invoice', balance=Decimal('125.00')
        )

    def test_creditor_transaction_creation(self):
        self.assertEqual(self.trans1.amount, Decimal('100.00'))
        self.assertEqual(self.trans1.creditor, self.creditor)
        self.assertEqual(self.trans1.transaction_date, date(2023, 1, 15))

    def test_str_method(self):
        self.assertEqual(str(self.trans1), 'Transaction 101 for Test Creditor')
        
    def test_verbose_name(self):
        self.assertEqual(CreditorTransaction._meta.verbose_name, 'Creditor Transaction')
        self.assertEqual(CreditorTransaction._meta.verbose_name_plural, 'Creditor Transactions')

    def test_get_filtered_transactions_no_dates(self):
        transactions = CreditorTransaction.get_filtered_transactions(
            sup_id=self.creditor.sup_id, link_for=self.creditor.category_name
        )
        self.assertEqual(transactions.count(), 3)
        self.assertIn(self.trans1, transactions)
        self.assertIn(self.trans2, transactions)
        self.assertIn(self.trans3, transactions)

    def test_get_filtered_transactions_with_from_date(self):
        transactions = CreditorTransaction.get_filtered_transactions(
            sup_id=self.creditor.sup_id, link_for=self.creditor.category_name, 
            date_from=date(2023, 1, 20)
        )
        self.assertEqual(transactions.count(), 2)
        self.assertNotIn(self.trans1, transactions)
        self.assertIn(self.trans2, transactions)
        self.assertIn(self.trans3, transactions)

    def test_get_filtered_transactions_with_to_date(self):
        transactions = CreditorTransaction.get_filtered_transactions(
            sup_id=self.creditor.sup_id, link_for=self.creditor.category_name, 
            date_to=date(2023, 1, 18)
        )
        self.assertEqual(transactions.count(), 1)
        self.assertIn(self.trans1, transactions)
        self.assertNotIn(self.trans2, transactions)

    def test_get_filtered_transactions_with_date_range(self):
        transactions = CreditorTransaction.get_filtered_transactions(
            sup_id=self.creditor.sup_id, link_for=self.creditor.category_name, 
            date_from=date(2023, 1, 1), date_to=date(2023, 1, 25)
        )
        self.assertEqual(transactions.count(), 2)
        self.assertIn(self.trans1, transactions)
        self.assertIn(self.trans2, transactions)
        self.assertNotIn(self.trans3, transactions)

    def test_get_filtered_transactions_no_match(self):
        transactions = CreditorTransaction.get_filtered_transactions(
            sup_id=self.creditor.sup_id, link_for=self.creditor.category_name, 
            date_from=date(2024, 1, 1), date_to=date(2024, 1, 31)
        )
        self.assertEqual(transactions.count(), 0)

class DateRangeSearchFormTest(TestCase):
    def test_valid_form_empty(self):
        form = DateRangeSearchForm({})
        self.assertTrue(form.is_valid())
        self.assertIsNone(form.cleaned_data['from_date'])
        self.assertIsNone(form.cleaned_data['to_date'])

    def test_valid_form_with_dates(self):
        form = DateRangeSearchForm({
            'from_date': '2023-01-01',
            'to_date': '2023-01-31'
        })
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['from_date'], date(2023, 1, 1))
        self.assertEqual(form.cleaned_data['to_date'], date(2023, 1, 31))

    def test_invalid_form_date_order(self):
        form = DateRangeSearchForm({
            'from_date': '2023-01-31',
            'to_date': '2023-01-01'
        })
        self.assertFalse(form.is_valid())
        self.assertIn('from_date', form.errors)
        self.assertEqual(form.errors['from_date'], ['From Date cannot be after To Date.'])

    def test_form_only_from_date(self):
        form = DateRangeSearchForm({
            'from_date': '2023-01-01'
        })
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['from_date'], date(2023, 1, 1))
        self.assertEqual(form.cleaned_data['to_date'], date.today()) # Auto-filled to_date

    def test_form_only_to_date(self):
        form = DateRangeSearchForm({
            'to_date': '2023-01-31'
        })
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['to_date'], date(2023, 1, 31))
        # from_date will be filled to a year before to_date
        self.assertEqual(form.cleaned_data['from_date'], date(2023, 1, 31) - datetime.timedelta(days=365))


class SundryCreditorViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.creditor = SundryCreditor.objects.create(
            sup_id=1, sup_name='Test Creditor', category_name='Local', key_value='ABC'
        )
        CreditorTransaction.objects.create(
            transaction_id=1, creditor=cls.creditor, transaction_date=date(2023, 1, 1), 
            amount=Decimal('100.00'), description='Test Trans 1'
        )
        CreditorTransaction.objects.create(
            transaction_id=2, creditor=cls.creditor, transaction_date=date(2023, 1, 10), 
            amount=Decimal('200.00'), description='Test Trans 2'
        )

    def setUp(self):
        self.client = Client()
        self.base_url_params = {'sup_id': self.creditor.sup_id, 'link_for': self.creditor.category_name}
        self.detail_list_url = reverse('accounts:sundry_creditors_detail_list', kwargs=self.base_url_params)
        self.table_partial_url = reverse('accounts:creditor_transactions_table_partial', kwargs=self.base_url_params)
        self.search_url = reverse('accounts:creditor_transactions_search', kwargs=self.base_url_params)

    def test_detail_list_view_get(self):
        response = self.client.get(self.detail_list_url + '?Key=ABC')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/sundrycreditors/detail_list.html')
        self.assertIn('form', response.context)
        self.assertIn('sundry_creditor_name', response.context)
        self.assertEqual(response.context['sundry_creditor_name'], 'Test Creditor')
        self.assertEqual(response.context['link_for_category'], 'Local')
        self.assertEqual(response.context['key_value'], 'ABC')

    def test_detail_list_view_invalid_creditor(self):
        invalid_url = reverse('accounts:sundry_creditors_detail_list', kwargs={'sup_id': 999, 'link_for': 'NonExistent'})
        response = self.client.get(invalid_url + '?Key=XYZ')
        self.assertEqual(response.status_code, 200) # Still 200, but with error message
        messages = list(response._messages)
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Invalid creditor or category information.")
        self.assertEqual(response.context['sundry_creditor_name'], 'N/A')

    def test_table_partial_view_get_no_filter(self):
        response = self.client.get(self.table_partial_url + '?Key=ABC', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/sundrycreditors/_creditor_transaction_table.html')
        self.assertIn('creditor_transactions', response.context)
        self.assertEqual(len(response.context['creditor_transactions']), 2)

    def test_table_partial_view_get_with_date_filter(self):
        response = self.client.get(
            self.table_partial_url + '?Key=ABC&from_date=2023-01-05&to_date=2023-01-15',
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['creditor_transactions']), 1)
        self.assertEqual(response.context['creditor_transactions'][0].transaction_id, 2)

    def test_search_view_post_valid_form(self):
        data = {
            'from_date': '2023-01-05',
            'to_date': '2023-01-15',
            'sup_id_hidden': self.creditor.sup_id,
            'link_for_hidden': self.creditor.category_name,
            'key_value_hidden': 'ABC',
        }
        response = self.client.post(self.search_url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertIn('refreshCreditorTransactionList:from_date=2023-01-05&to_date=2023-01-15&Key=ABC', response.headers['HX-Trigger'])
        messages = list(response._messages)
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Transactions filtered successfully.")

    def test_search_view_post_invalid_form(self):
        data = {
            'from_date': '2023-01-15',
            'to_date': '2023-01-05', # Invalid date range
            'sup_id_hidden': self.creditor.sup_id,
            'link_for_hidden': self.creditor.category_name,
            'key_value_hidden': 'ABC',
        }
        response = self.client.post(self.search_url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX still sends 204 for trigger, but errors are in messages
        self.assertTrue('HX-Trigger' in response.headers)
        # Note: The trigger will still be sent, but the client-side form validation (if enabled)
        # would typically prevent submission or display errors. Here, the view just doesn't apply filter.
        messages = list(response._messages)
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Please correct the date range errors.")

    def test_cancel_button_redirect(self):
        response = self.client.get(reverse('accounts:sundry_creditors_parent_list'))
        self.assertEqual(response.status_code, 200) # Assuming parent_list is a TemplateView
        self.assertTemplateUsed(response, 'accounts/sundrycreditors/parent_list.html')
```

### Step 5: HTMX and Alpine.js Integration

The provided templates fully integrate HTMX and Alpine.js according to the guidelines:

*   **HTMX for Dynamic Updates:**
    *   The main `detail_list.html` uses `hx-get` with `hx-trigger="load, refreshCreditorTransactionList from:body"` on the `div#creditorTransactionTableContainer` to initially load and refresh the transaction table.
    *   The date filter form uses `hx-post` to submit to `creditor_transactions_search`. Upon successful submission, this endpoint returns an `HttpResponse` with `status=204` and `HX-Trigger='refreshCreditorTransactionList:...'`, which then causes the `creditorTransactionTableContainer` to re-fetch its content with the new filter parameters.
*   **Alpine.js for UI State Management:**
    *   An Alpine.js data component (`sundryCreditorApp`) is set up to listen for the `refreshCreditorTransactionList` custom event. This allows Alpine.js to dynamically update the `hx-get` attribute of the table container with the new query parameters before re-triggering the HTMX `get` request. This ensures the table fetches data with the latest filter values.
*   **DataTables for List Views:**
    *   The `_creditor_transaction_table.html` partial includes a `script` block that initializes DataTables on the `table#creditorTransactionTable` immediately after it's loaded into the DOM by HTMX. This provides client-side sorting, searching, and pagination.
*   **No Full Page Reloads:** All interactions (initial table load, filtering) are handled via HTMX, preventing full page reloads and providing a smoother user experience.
*   **DRY Templates:** The form and table are separate partials (`_date_range_form.html` (implicitly handled by `{{ form }}` in `detail_list.html`) and `_creditor_transaction_table.html`) which could be reused or extended if more complex forms or table rendering logic were needed elsewhere.

### Final Notes

*   **Placeholders:** Replace `tblSundryCreditors`, `tblCreditorTransactions` and their inferred columns with actual database table and column names once the database schema is fully extracted.
*   **Business Logic:** The `CreditorTransaction.get_filtered_transactions` class method encapsulates the filtering logic, adhering to the "fat model, thin view" principle. Any complex validation or data manipulation should be moved to the models or custom manager methods.
*   **Error Handling:** Basic `try-except` is present in the ASP.NET code. In Django, errors are typically handled via form validation, `messages` framework for user feedback, and proper HTTP status codes for HTMX. The provided code includes `messages` for form validation results.
*   **Styling:** Tailwind CSS classes (`box3`, `redbox`, `bg-blue-500`, etc.) have been applied to the Django templates to mimic the original ASP.NET styling and conform to modern design practices.
*   **CDN Links:** It is assumed that `core/base.html` includes necessary CDN links for HTMX, Alpine.js, jQuery, and DataTables.
*   **URL Parameter Handling:** The `sup_id`, `link_for`, and `Key` parameters are handled both in the URL paths and as query parameters (`Key`), mirroring the original ASP.NET `Request.QueryString` behavior. Hidden input fields in the form ensure these contextual parameters are passed back during `POST` submissions.