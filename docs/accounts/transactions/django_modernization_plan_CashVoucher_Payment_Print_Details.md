The current ASP.NET page is a specialized reporting view for "Cash Voucher Payment Details" that retrieves data from multiple tables and displays it using Crystal Reports. Our Django modernization plan will transform this into a dynamic, data-driven web report page leveraging Django's robust backend, HTMX for seamless frontend interactions, and DataTables for powerful data presentation.

The primary goal is to replicate the data display functionality of the Crystal Report in a modern web format, allowing for detailed viewing and potential print-friendly output. We will also provide a full CRUD implementation for the `CashVoucherPayment` entity to ensure the application is fully manageable, adhering to the principles of "Fat Models, Thin Views."

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the ASP.NET code, the core tables involved are `tblACC_CashVoucher_Payment_Master` and `tblACC_CashVoucher_Payment_Details`. Several other tables are used for lookup purposes to enrich the data display: `tblHR_OfficeStaff`, `SD_Cust_master`, `tblMM_Supplier_master`, `BusinessGroup`, `tblMIS_BudgetCode`, and `AccHead`.

**Identified Tables and Key Columns:**

*   **`tblACC_CashVoucher_Payment_Master` (Main Master Table):**
    *   `Id` (Primary Key, integer)
    *   `SysDate` (Date)
    *   `CompId` (Foreign Key to Company, integer)
    *   `CVPNo` (Cash Voucher Payment Number, string)
    *   `PaidTo` (Recipient Name, string)
    *   `ReceivedBy` (ID of receiver, string - resolved based on `CodeType`)
    *   `CodeType` (Type of receiver: 1=Employee, 2=Customer, 3=Supplier, integer)

*   **`tblACC_CashVoucher_Payment_Details` (Detail Line Items):**
    *   `MId` (Foreign Key to `tblACC_CashVoucher_Payment_Master.Id`, integer)
    *   `BillNo` (Bill Number, string)
    *   `BillDate` (Bill Date, string)
    *   `PONo` (Purchase Order Number, string)
    *   `PODate` (Purchase Order Date, string)
    *   `Particulars` (Description, string)
    *   `WONo` (Work Order Number, string)
    *   `BGGroup` (Business Group ID, integer - resolved to symbol)
    *   `AcHead` (Account Head ID, integer - resolved to symbol)
    *   `Amount` (Payment Amount, double)
    *   `BudgetCode` (Budget Code ID, integer - resolved to symbol)
    *   `PVEVNo` (Previous/Voucher Entry Number, string)

*   **Lookup Tables (with assumed key fields for Django mapping):**
    *   `tblCompany`: `CompId` (PK), `Address`
    *   `tblHR_OfficeStaff`: `EmpId` (PK), `Title`, `EmployeeName`, `CompId`
    *   `SD_Cust_master`: `CustomerId` (PK), `CustomerName`, `CompId`
    *   `tblMM_Supplier_master`: `SupplierId` (PK), `SupplierName`, `CompId`
    *   `BusinessGroup`: `Id` (PK), `Symbol`
    *   `tblMIS_BudgetCode`: `Id` (PK), `Symbol`
    *   `AccHead`: `Id` (PK), `Symbol`

### Step 2: Identify Backend Functionality

The ASP.NET page primarily performs a **Read** operation, fetching master and detail records for a specific Cash Voucher Payment ID and then enriching these records with lookup data from various auxiliary tables. It then prepares this data for a Crystal Report viewer. There is also a simple navigation action (`btnCancel`).

*   **Create**: Not present in the provided code snippet.
*   **Read**: Core functionality – fetches a single `CashVoucherPayment` master record and all its associated `CashVoucherPaymentDetail` records. Performs complex lookups for `ReceivedBy` (based on type), `BusinessGroup`, `BudgetCode`, and `AccountHead` symbols.
*   **Update**: Not present.
*   **Delete**: Not present.
*   **Navigation**: A cancel button redirects the user away from the report view.

**Business Logic:**
- Resolving "Received By" name based on `CodeType` (Employee, Customer, Supplier) and fetching their respective names.
- Resolving `BusinessGroup`, `BudgetCode`, and `AccHead` IDs to their `Symbol` (name) values.
- Obtaining the Company Address (`fun.CompAdd`).

### Step 3: Infer UI Components

The page is primarily a display of a report.

*   **`CR:CrystalReportViewer`**: This will be replaced by a structured HTML view. The master data will be presented as key-value pairs, and the detail lines will be displayed in an interactive HTML table using DataTables.
*   **`asp:Button ID="btnCancel"`**: This will be a standard Django `<a>` tag or `<button>` for navigation back to the list of cash vouchers.
*   The `Panel` control indicates a scrollable area, which will be handled by CSS for the main report content.

### Step 4: Generate Django Code

We will create a Django application, e.g., `accounts`.

#### 4.1 Models

We will create models for `CashVoucherPayment` (master) and `CashVoucherPaymentDetail` (detail). Additionally, placeholder models for the lookup tables (`Company`, `Employee`, `Customer`, `Supplier`, `BusinessGroup`, `BudgetCode`, `AccHead`) are included. The complex lookup logic from the C# code will be encapsulated as methods within the `CashVoucherPayment` and `CashVoucherPaymentDetail` models, adhering to the "fat model" principle.

```python
# accounts/models.py
from django.db import models
from django.urls import reverse

# Placeholder models for lookup tables. Assume these are managed=False
# and exist in the database, mapping to the legacy table names.
class Company(models.Model):
    id = models.IntegerField(db_column='CompId', primary_key=True)
    address = models.CharField(db_column='Address', max_length=255, blank=True, null=True) # Assuming an 'Address' column
    class Meta:
        managed = False
        db_table = 'tblCompany' # Placeholder table name for company master data
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

class Employee(models.Model):
    id = models.CharField(db_column='EmpId', max_length=50, primary_key=True)
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')
    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

class Customer(models.Model):
    id = models.CharField(db_column='CustomerId', max_length=50, primary_key=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')
    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

class Supplier(models.Model):
    id = models.CharField(db_column='SupplierId', max_length=50, primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')
    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=100)
    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

class BudgetCode(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=100)
    class Meta:
        managed = False
        db_table = 'tblMIS_BudgetCode'
        verbose_name = 'Budget Code'
        verbose_name_plural = 'Budget Codes'

class AccHead(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=100)
    class Meta:
        managed = False
        db_table = 'AccHead'
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'


class CashVoucherPayment(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='cash_voucher_payments')
    sys_date = models.DateField(db_column='SysDate')
    cvp_no = models.CharField(db_column='CVPNo', max_length=50)
    paid_to = models.CharField(db_column='PaidTo', max_length=255)
    received_by_id = models.CharField(db_column='ReceivedBy', max_length=50)
    code_type = models.IntegerField(db_column='CodeType') # 1: Employee, 2: Customer, 3: Supplier

    class Meta:
        managed = False
        db_table = 'tblACC_CashVoucher_Payment_Master'
        verbose_name = 'Cash Voucher Payment'
        verbose_name_plural = 'Cash Voucher Payments'

    def __str__(self):
        return f"{self.cvp_no} - {self.paid_to}"

    def get_absolute_url(self):
        return reverse('cashvoucherpayment_detail', kwargs={'pk': self.pk})

    def get_received_by_info(self):
        """
        Retrieves the name and type of the 'Received By' entity, replicating ASP.NET logic.
        """
        try:
            if self.code_type == 1: # Employee
                employee = Employee.objects.get(id=self.received_by_id, comp_id=self.company.id)
                return f"{employee.title}. {employee.employee_name} [Employee]"
            elif self.code_type == 2: # Customer
                customer = Customer.objects.get(id=self.received_by_id, comp_id=self.company.id)
                return f"{customer.customer_name}[ {customer.id}] [Customer]"
            elif self.code_type == 3: # Supplier
                supplier = Supplier.objects.get(id=self.received_by_id, comp_id=self.company.id)
                return f"{supplier.supplier_name}[ {supplier.id}] [Supplier]"
            else:
                return f"Unknown Type ({self.code_type}): {self.received_by_id}"
        except (Employee.DoesNotExist, Customer.DoesNotExist, Supplier.DoesNotExist):
            return f"Not Found ({self.code_type}): {self.received_by_id}"
        except Exception:
            return f"Error resolving received by: {self.received_by_id}"

    def get_company_address(self):
        """
        Retrieves the company address, replicating fun.CompAdd(cId).
        """
        try:
            return self.company.address
        except Company.DoesNotExist:
            return "Company address not available"
        except Exception:
            return "Error retrieving company address."


class CashVoucherPaymentDetail(models.Model):
    master = models.ForeignKey(CashVoucherPayment, on_delete=models.CASCADE, db_column='MId', related_name='details')
    bill_no = models.CharField(db_column='BillNo', max_length=100, blank=True, null=True)
    bill_date = models.DateField(db_column='BillDate', blank=True, null=True)
    po_no = models.CharField(db_column='PONo', max_length=100, blank=True, null=True)
    po_date = models.DateField(db_column='PODate', blank=True, null=True)
    particulars = models.CharField(db_column='Particulars', max_length=255, blank=True, null=True)
    wo_no = models.CharField(db_column='WONo', max_length=100, blank=True, null=True)
    bg_group_id = models.IntegerField(db_column='BGGroup', blank=True, null=True)
    budget_code_id = models.IntegerField(db_column='BudgetCode', blank=True, null=True)
    ac_head_id = models.IntegerField(db_column='AcHead', blank=True, null=True)
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=2)
    pvev_no = models.CharField(db_column='PVEVNo', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_CashVoucher_Payment_Details'
        verbose_name = 'Cash Voucher Payment Detail'
        verbose_name_plural = 'Cash Voucher Payment Details'

    def __str__(self):
        return f"Detail for {self.master.cvp_no} - {self.particulars}"

    def get_bg_group_symbol(self):
        try:
            return BusinessGroup.objects.get(id=self.bg_group_id).symbol
        except BusinessGroup.DoesNotExist:
            return "NA"
        except Exception:
            return "NA"

    def get_budget_code_symbol(self):
        try:
            return BudgetCode.objects.get(id=self.budget_code_id).symbol
        except BudgetCode.DoesNotExist:
            return "NA"
        except Exception:
            return "NA"

    def get_acc_head_symbol(self):
        try:
            return AccHead.objects.get(id=self.ac_head_id).symbol
        except AccHead.DoesNotExist:
            return "NA"
        except Exception:
            return "NA"

```

#### 4.2 Forms

A form for the `CashVoucherPayment` master record, following the general pattern for CRUD operations.

```python
# accounts/forms.py
from django import forms
from .models import CashVoucherPayment, Company # Assuming Company is selectable

class CashVoucherPaymentForm(forms.ModelForm):
    # This form supports adding/editing CashVoucherPayment master records.
    # Note: ReceivedBy (ID and CodeType) would typically be managed more
    # elegantly with dynamic fields or related forms in a real application.
    class Meta:
        model = CashVoucherPayment
        fields = [
            'sys_date', 'cvp_no', 'paid_to',
            'received_by_id', 'code_type', 'company'
        ]
        widgets = {
            'sys_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'cvp_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'paid_to': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'received_by_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'code_type': forms.Select(choices=[(1, 'Employee'), (2, 'Customer'), (3, 'Supplier')], attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'company': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'hx-get': '/accounts/companies/select_options/', 'hx-trigger': 'load', 'hx-swap': 'innerHTML'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate company choices if needed
        self.fields['company'].queryset = Company.objects.all()

```

#### 4.3 Views

We will implement a `DetailView` for the `CashVoucherPayment` which replicates the ASP.NET report viewing functionality. We will also provide placeholder `ListView`, `CreateView`, `UpdateView`, and `DeleteView` for the `CashVoucherPayment` entity to adhere to the comprehensive CRUD requirement, assuming this is part of a larger management system.

```python
# accounts/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from django.template.loader import render_to_string
from .models import CashVoucherPayment, CashVoucherPaymentDetail, Company # Ensure Company is imported
from .forms import CashVoucherPaymentForm

# --- Generic CRUD Views for CashVoucherPayment (Master Record) ---

class CashVoucherPaymentListView(ListView):
    model = CashVoucherPayment
    template_name = 'accounts/cashvoucherpayment/list.html'
    context_object_name = 'cashvoucherpayments'
    paginate_by = 10 # Example pagination

class CashVoucherPaymentCreateView(CreateView):
    model = CashVoucherPayment
    form_class = CashVoucherPaymentForm
    template_name = 'accounts/cashvoucherpayment/form.html'
    success_url = reverse_lazy('cashvoucherpayment_list') # Redirect to list view

    def form_valid(self, form):
        # The company should be pre-set if coming from a session or user context
        # For simplicity, if company is not set in form, assign a default or error
        if not form.instance.company_id:
            # This is a simplification. In a real app, company would be derived from session/user
            # or pre-selected in the form. For this example, assuming a default company ID.
            form.instance.company_id = 1 # Placeholder: Assign a default company ID if not provided
        response = super().form_valid(form)
        messages.success(self.request, 'Cash Voucher Payment added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCashVoucherPaymentList'
                }
            )
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Add New Cash Voucher Payment'
        return context

class CashVoucherPaymentUpdateView(UpdateView):
    model = CashVoucherPayment
    form_class = CashVoucherPaymentForm
    template_name = 'accounts/cashvoucherpayment/form.html'
    success_url = reverse_lazy('cashvoucherpayment_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Cash Voucher Payment updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCashVoucherPaymentList'
                }
            )
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Edit Cash Voucher Payment'
        return context

class CashVoucherPaymentDeleteView(DeleteView):
    model = CashVoucherPayment
    template_name = 'accounts/cashvoucherpayment/confirm_delete.html'
    success_url = reverse_lazy('cashvoucherpayment_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Cash Voucher Payment deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCashVoucherPaymentList'
                }
            )
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Confirm Delete'
        return context

# --- Specific Report/Detail View (Replaces ASP.NET Crystal Report Viewer) ---

class CashVoucherPaymentDetailView(DetailView):
    model = CashVoucherPayment
    template_name = 'accounts/cashvoucherpayment/detail.html'
    context_object_name = 'cashvoucherpayment'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        cash_voucher_payment = self.get_object()
        
        # Add company_id from session/query string, similar to ASP.NET's Session["compid"]
        # For demonstration, we'll assume a default or retrieve it from a mock session.
        # In a real application, this would come from the authenticated user's company.
        # This will be replaced by a proper session/user integration.
        company_id_from_session = self.request.session.get('compid', 1) # Default to 1 if not in session

        # Filter details by master ID and company ID (if applicable for details table)
        # The ASP.NET query filters both master and details by compid.
        # This means the details must also belong to the same company.
        details = cash_voucher_payment.details.filter(
            master__company__id=company_id_from_session # Assuming company filter cascades to details
        ).all()
        
        context['details'] = details
        context['company_address'] = cash_voucher_payment.get_company_address()
        return context

# --- HTMX Partial View for DataTables ---

class CashVoucherPaymentTablePartialView(ListView):
    model = CashVoucherPayment
    template_name = 'accounts/cashvoucherpayment/_cashvoucherpayment_table.html'
    context_object_name = 'cashvoucherpayments'
    paginate_by = 10 # Example pagination, DataTables handles its own.

    def get_queryset(self):
        # This would be where you might filter based on user's company, etc.
        # For now, it fetches all, as per list view.
        qs = super().get_queryset()
        # Example of filtering by session company ID if needed for the list view
        # company_id_from_session = self.request.session.get('compid', None)
        # if company_id_from_session:
        #     qs = qs.filter(company_id=company_id_from_session)
        return qs

# --- HTMX Partial View for company dropdown options (if dynamically loaded) ---
class CompanySelectOptionsView(ListView):
    model = Company
    template_name = 'accounts/partials/_company_select_options.html'
    context_object_name = 'companies'

    def get_queryset(self):
        return Company.objects.all().order_by('id')

```

#### 4.4 Templates

**Directory Structure:**
```
accounts/
├── templates/
│   └── accounts/
│       └── cashvoucherpayment/
│           ├── list.html
│           ├── form.html
│           ├── confirm_delete.html
│           ├── detail.html  # New template for the report view
│           └── _cashvoucherpayment_table.html
│       └── partials/
│           └── _company_select_options.html
```

**`accounts/cashvoucherpayment/list.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Cash Voucher Payments</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'cashvoucherpayment_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Cash Voucher
        </button>
    </div>
    
    <div id="cashvoucherpaymentTable-container"
         hx-trigger="load, refreshCashVoucherPaymentList from:body"
         hx-get="{% url 'cashvoucherpayment_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg overflow-hidden">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="p-6 text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Cash Voucher Payments...</p>
        </div>
    </div>
    
    <!-- Universal Modal for HTMX-loaded forms/confirms -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 hidden transition-opacity duration-300"
         _="on click if event.target.id == 'modal' remove .is-active from me also remove .is-active from #modalContent">
        <div id="modalContent" class="bg-white p-6 rounded-xl shadow-2xl max-w-3xl w-full max-h-[90vh] overflow-y-auto transform scale-95 opacity-0 transition-all duration-300 ease-out"
             _="on htmx:afterOnLoad add .is-active to #modal if not htmx:trigger and not .is-active then add .is-active to me and add .scale-100 to me and add .opacity-100 to me">
            <!-- Content loaded by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is typically initialized globally, but specific component logic can go here.
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js component needed for this page unless custom interactivity is added
    });

    // Handle modal visibility based on HTMX interactions
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.detail.target && evt.detail.target.id === 'modalContent') {
            const modal = document.getElementById('modal');
            const modalContent = document.getElementById('modalContent');
            if (evt.detail.successful) {
                modal.classList.add('is-active');
                modal.classList.remove('hidden');
                modalContent.classList.add('is-active');
                modalContent.classList.add('scale-100', 'opacity-100');
                modalContent.classList.remove('scale-95', 'opacity-0');
            }
        }
    });

    document.body.addEventListener('htmx:afterOnLoad', function(evt) {
        if (evt.detail.xhr.status === 204) { // HTMX swap="none" or form submission success
            const modal = document.getElementById('modal');
            const modalContent = document.getElementById('modalContent');
            if (modal.classList.contains('is-active')) {
                modal.classList.remove('is-active');
                modal.classList.add('hidden');
                modalContent.classList.remove('is-active', 'scale-100', 'opacity-100');
                modalContent.classList.add('scale-95', 'opacity-0');
            }
        }
    });

    // When clicking outside the modal or cancel button
    document.getElementById('modal').addEventListener('click', function(event) {
        if (event.target === this) {
            this.classList.remove('is-active');
            this.classList.add('hidden');
            document.getElementById('modalContent').classList.remove('is-active', 'scale-100', 'opacity-100');
            document.getElementById('modalContent').classList.add('scale-95', 'opacity-0');
        }
    });

    // Helper for closing modal on escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            const modal = document.getElementById('modal');
            const modalContent = document.getElementById('modalContent');
            if (modal.classList.contains('is-active')) {
                modal.classList.remove('is-active');
                modal.classList.add('hidden');
                modalContent.classList.remove('is-active', 'scale-100', 'opacity-100');
                modalContent.classList.add('scale-95', 'opacity-0');
            }
        }
    });
</script>
{% endblock %}
```

**`accounts/cashvoucherpayment/_cashvoucherpayment_table.html`** (Partial for DataTables)
```html
<div class="p-6">
    <table id="cashvoucherpaymentTable" class="min-w-full bg-white border border-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">S.N.</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">CVP No</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">System Date</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Paid To</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Received By</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% for cvp in cashvoucherpayments %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ cvp.cvp_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ cvp.sys_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ cvp.paid_to }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ cvp.get_received_by_info }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm">
                    <a href="{% url 'cashvoucherpayment_detail' cvp.pk %}" 
                       class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs leading-4 font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 mr-2 transition duration-150 ease-in-out">
                        View Report
                    </a>
                    <button 
                        class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs leading-4 font-medium rounded-md shadow-sm text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 mr-2 transition duration-150 ease-in-out"
                        hx-get="{% url 'cashvoucherpayment_edit' cvp.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs leading-4 font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-150 ease-in-out"
                        hx-get="{% url 'cashvoucherpayment_delete' cvp.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-3 px-4 text-center text-gray-500">No cash voucher payments found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Ensure DataTables is initialized only once and on new content.
    // HTMX will re-inject this script, so we need to handle destruction/re-initialization.
    if ($.fn.DataTable.isDataTable('#cashvoucherpaymentTable')) {
        $('#cashvoucherpaymentTable').DataTable().destroy();
    }
    $('#cashvoucherpaymentTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "searching": true,
        "ordering": true,
        "paging": true,
        "info": true,
        "responsive": true
    });
</script>
```

**`accounts/cashvoucherpayment/form.html`**
```html
<div class="p-6 bg-white rounded-lg shadow-xl">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ title }}</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-600 text-sm mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        {% if form.non_field_errors %}
        <div class="mt-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-md">
            {% for error in form.non_field_errors %}
            <p>{{ error }}</p>
            {% endfor %}
        </div>
        {% endif %}

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="px-5 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out">
                Save Cash Voucher
            </button>
        </div>
    </form>
</div>
```

**`accounts/cashvoucherpayment/confirm_delete.html`**
```html
<div class="p-6 bg-white rounded-lg shadow-xl">
    <h3 class="text-2xl font-semibold text-gray-900 mb-4">{{ title }}</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Cash Voucher Payment: <strong>{{ cashvoucherpayment.cvp_no }} - {{ cashvoucherpayment.paid_to }}</strong>?</p>
    
    <form hx-post="{% url 'cashvoucherpayment_delete' cashvoucherpayment.pk %}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="px-5 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-150 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

**`accounts/cashvoucherpayment/detail.html`** (The main report view)
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-lg rounded-lg p-8 mb-6">
        <h2 class="text-3xl font-bold text-gray-900 mb-6 text-center">Cash Voucher Payment Details Report</h2>
        
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-semibold text-gray-800">Master Information</h3>
            <a href="{% url 'cashvoucherpayment_list' %}" 
               class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out">
                Back to List
            </a>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4 text-lg mb-8">
            <div class="flex flex-col">
                <span class="font-medium text-gray-600">Voucher No:</span>
                <span class="text-gray-900">{{ cashvoucherpayment.cvp_no }}</span>
            </div>
            <div class="flex flex-col">
                <span class="font-medium text-gray-600">System Date:</span>
                <span class="text-gray-900">{{ cashvoucherpayment.sys_date|date:"F d, Y" }}</span>
            </div>
            <div class="flex flex-col">
                <span class="font-medium text-gray-600">Paid To:</span>
                <span class="text-gray-900">{{ cashvoucherpayment.paid_to }}</span>
            </div>
            <div class="flex flex-col">
                <span class="font-medium text-gray-600">Received By:</span>
                <span class="text-gray-900">{{ cashvoucherpayment.get_received_by_info }}</span>
            </div>
            <div class="flex flex-col md:col-span-2">
                <span class="font-medium text-gray-600">Company Address:</span>
                <span class="text-gray-900">{{ company_address }}</span>
            </div>
        </div>

        <h3 class="text-xl font-semibold text-gray-800 mb-4">Detail Items</h3>
        <div class="overflow-x-auto rounded-lg shadow-md border border-gray-200">
            <table id="cvpDetailsTable" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Bill No</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Bill Date</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">PO No</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">PO Date</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Particulars</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">WO No</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">BG Group</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Budget Code</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Acc Head</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">PVEV No</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Amount</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for detail in details %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800">{{ forloop.counter }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800">{{ detail.bill_no|default:"-" }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800">{{ detail.bill_date|date:"Y-m-d"|default:"-" }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800">{{ detail.po_no|default:"-" }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800">{{ detail.po_date|date:"Y-m-d"|default:"-" }}</td>
                        <td class="px-6 py-4 text-sm text-gray-800 max-w-xs overflow-hidden text-ellipsis">{{ detail.particulars|default:"-" }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800">{{ detail.wo_no|default:"-" }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800">{{ detail.get_bg_group_symbol }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800">{{ detail.get_budget_code_symbol }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800">{{ detail.get_acc_head_symbol }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800">{{ detail.pvev_no|default:"-" }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 text-right">{{ detail.amount|floatformat:2 }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="12" class="px-6 py-4 text-center text-gray-500">No detail items found for this cash voucher.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        $('#cvpDetailsTable').DataTable({
            "paging": false,       // No pagination for a single report details view
            "searching": false,    // No search for a single report
            "ordering": false,     // No sorting for a single report
            "info": false,         // No info text
            "responsive": true,    // Make table responsive
            "autoWidth": false,    // Disable auto-width to allow more control
            "columnDefs": [
                { "width": "5%", "targets": 0 }, // SN
                { "width": "8%", "targets": 1 }, // Bill No
                { "width": "8%", "targets": 2 }, // Bill Date
                { "width": "8%", "targets": 3 }, // PO No
                { "width": "8%", "targets": 4 }, // PO Date
                { "width": "20%", "targets": 5 },// Particulars
                { "width": "8%", "targets": 6 }, // WO No
                { "width": "8%", "targets": 7 }, // BG Group
                { "width": "8%", "targets": 8 }, // Budget Code
                { "width": "8%", "targets": 9 }, // Acc Head
                { "width": "8%", "targets": 10 },// PVEV No
                { "width": "8%", "targets": 11 },// Amount
            ]
        });
    });
</script>
{% endblock %}
```

**`accounts/partials/_company_select_options.html`** (If dynamic company selection needed)
```html
<option value="">Select Company</option>
{% for company in companies %}
<option value="{{ company.id }}">{{ company.address }}</option>
{% endfor %}
```

#### 4.5 URLs

We'll define URL patterns for all views, including the list, CRUD operations for the master record, the detail/report view, and the HTMX partial for the DataTables content.

```python
# accounts/urls.py
from django.urls import path
from .views import (
    CashVoucherPaymentListView,
    CashVoucherPaymentCreateView,
    CashVoucherPaymentUpdateView,
    CashVoucherPaymentDeleteView,
    CashVoucherPaymentDetailView,
    CashVoucherPaymentTablePartialView,
    CompanySelectOptionsView, # If using dynamic company selection
)

urlpatterns = [
    # CRUD operations for CashVoucherPayment (Master Record)
    path('cashvoucherpayments/', CashVoucherPaymentListView.as_view(), name='cashvoucherpayment_list'),
    path('cashvoucherpayments/add/', CashVoucherPaymentCreateView.as_view(), name='cashvoucherpayment_add'),
    path('cashvoucherpayments/edit/<int:pk>/', CashVoucherPaymentUpdateView.as_as_view(), name='cashvoucherpayment_edit'),
    path('cashvoucherpayments/delete/<int:pk>/', CashVoucherPaymentDeleteView.as_view(), name='cashvoucherpayment_delete'),
    
    # HTMX partials
    path('cashvoucherpayments/table/', CashVoucherPaymentTablePartialView.as_view(), name='cashvoucherpayment_table'),
    path('companies/select_options/', CompanySelectOptionsView.as_view(), name='company_select_options'), # If needed
    
    # Specific Report/Detail View (replaces ASP.NET print details page)
    # The 'Key' query string parameter from ASP.NET is often a cache key, not directly mapped.
    # The 'CVPId' (pk) is the primary identifier for the specific report.
    path('cashvoucherpayments/<int:pk>/detail/', CashVoucherPaymentDetailView.as_view(), name='cashvoucherpayment_detail'),
    # Note: If the ASP.NET 'ModId' and 'SubModId' are part of a navigation system,
    # they would be handled by Django's URL routing or a custom menu system.
]

```

#### 4.6 Tests

Comprehensive tests for both models and views are crucial to ensure correct migration and functionality.

```python
# accounts/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date
from decimal import Decimal
from .models import (
    Company, Employee, Customer, Supplier, BusinessGroup, BudgetCode, AccHead,
    CashVoucherPayment, CashVoucherPaymentDetail
)

class AccountsModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary lookup data
        cls.company = Company.objects.create(id=1, address="123 Main St, Anytown")
        cls.employee = Employee.objects.create(id='EMP001', title='Mr', employee_name='John Doe', comp_id=1)
        cls.customer = Customer.objects.create(id='CUST001', customer_name='ABC Corp', comp_id=1)
        cls.supplier = Supplier.objects.create(id='SUPP001', supplier_name='XYZ Ltd', comp_id=1)
        cls.biz_group = BusinessGroup.objects.create(id=101, symbol='FIN')
        cls.budget_code = BudgetCode.objects.create(id=201, symbol='OPEX')
        cls.acc_head = AccHead.objects.create(id=301, symbol='TRAVEL')

        # Create CashVoucherPayment master data
        cls.cvp_master = CashVoucherPayment.objects.create(
            id=1,
            company=cls.company,
            sys_date=date(2023, 1, 15),
            cvp_no='CVP-2023-001',
            paid_to='Vendor A',
            received_by_id='EMP001',
            code_type=1 # Employee
        )
        cls.cvp_master_cust = CashVoucherPayment.objects.create(
            id=2,
            company=cls.company,
            sys_date=date(2023, 1, 16),
            cvp_no='CVP-2023-002',
            paid_to='Customer B',
            received_by_id='CUST001',
            code_type=2 # Customer
        )
        cls.cvp_master_supp = CashVoucherPayment.objects.create(
            id=3,
            company=cls.company,
            sys_date=date(2023, 1, 17),
            cvp_no='CVP-2023-003',
            paid_to='Supplier C',
            received_by_id='SUPP001',
            code_type=3 # Supplier
        )

        # Create CashVoucherPaymentDetail data
        cls.cvp_detail = CashVoucherPaymentDetail.objects.create(
            master=cls.cvp_master,
            bill_no='B-001',
            bill_date=date(2023, 1, 10),
            po_no='PO-001',
            po_date=date(2023, 1, 5),
            particulars='Office Supplies',
            wo_no='WO-001',
            bg_group_id=101,
            budget_code_id=201,
            ac_head_id=301,
            amount=Decimal('150.75'),
            pvev_no='PVEV-001'
        )
        CashVoucherPaymentDetail.objects.create(
            master=cls.cvp_master,
            particulars='Travel Expenses',
            amount=Decimal('500.00'),
            bg_group_id=101,
            budget_code_id=201,
            ac_head_id=301,
        )

    def test_cashvoucherpayment_creation(self):
        self.assertEqual(self.cvp_master.cvp_no, 'CVP-2023-001')
        self.assertEqual(self.cvp_master.paid_to, 'Vendor A')
        self.assertEqual(self.cvp_master.company.address, "123 Main St, Anytown")

    def test_cashvoucherpayment_received_by_info(self):
        self.assertEqual(self.cvp_master.get_received_by_info(), f"{self.employee.title}. {self.employee.employee_name} [Employee]")
        self.assertEqual(self.cvp_master_cust.get_received_by_info(), f"{self.customer.customer_name}[ {self.customer.id}] [Customer]")
        self.assertEqual(self.cvp_master_supp.get_received_by_info(), f"{self.supplier.supplier_name}[ {self.supplier.id}] [Supplier]")
        
        # Test non-existent received by
        self.cvp_master.received_by_id = 'NONEXISTENT'
        self.assertEqual(self.cvp_master.get_received_by_info(), f"Not Found ({self.cvp_master.code_type}): NONEXISTENT")

        # Test unknown code type
        self.cvp_master.code_type = 99
        self.assertEqual(self.cvp_master.get_received_by_info(), f"Unknown Type (99): NONEXISTENT")


    def test_cashvoucherpayment_get_company_address(self):
        self.assertEqual(self.cvp_master.get_company_address(), "123 Main St, Anytown")
        
        # Test case where company might be missing or not found
        original_company = self.cvp_master.company
        self.cvp_master.company = None # Simulate missing company (ForeignKey can be null)
        self.assertEqual(self.cvp_master.get_company_address(), "Company address not available")
        self.cvp_master.company = original_company # Restore

    def test_cashvoucherpaymentdetail_creation(self):
        self.assertEqual(self.cvp_detail.particulars, 'Office Supplies')
        self.assertEqual(self.cvp_detail.amount, Decimal('150.75'))
        self.assertEqual(self.cvp_detail.master.cvp_no, 'CVP-2023-001')

    def test_cashvoucherpaymentdetail_lookup_symbols(self):
        self.assertEqual(self.cvp_detail.get_bg_group_symbol(), 'FIN')
        self.assertEqual(self.cvp_detail.get_budget_code_symbol(), 'OPEX')
        self.assertEqual(self.cvp_detail.get_acc_head_symbol(), 'TRAVEL')

        # Test with missing lookup IDs
        self.cvp_detail.bg_group_id = 999 # Non-existent ID
        self.assertEqual(self.cvp_detail.get_bg_group_symbol(), 'NA')
        self.cvp_detail.budget_code_id = None # Null ID
        self.assertEqual(self.cvp_detail.get_budget_code_symbol(), 'NA')
        self.cvp_detail.ac_head_id = 999 # Non-existent ID
        self.assertEqual(self.cvp_detail.get_acc_head_symbol(), 'NA')


class AccountsViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary lookup data
        cls.company = Company.objects.create(id=1, address="123 Main St, Anytown")
        cls.employee = Employee.objects.create(id='EMP001', title='Mr', employee_name='John Doe', comp_id=1)
        cls.customer = Customer.objects.create(id='CUST001', customer_name='ABC Corp', comp_id=1)
        cls.supplier = Supplier.objects.create(id='SUPP001', supplier_name='XYZ Ltd', comp_id=1)
        cls.biz_group = BusinessGroup.objects.create(id=101, symbol='FIN')
        cls.budget_code = BudgetCode.objects.create(id=201, symbol='OPEX')
        cls.acc_head = AccHead.objects.create(id=301, symbol='TRAVEL')

        # Create CashVoucherPayment master data
        cls.cvp_master = CashVoucherPayment.objects.create(
            id=1,
            company=cls.company,
            sys_date=date(2023, 1, 15),
            cvp_no='CVP-2023-001',
            paid_to='Test Vendor',
            received_by_id='EMP001',
            code_type=1 # Employee
        )
        # Create CashVoucherPaymentDetail data
        CashVoucherPaymentDetail.objects.create(
            master=cls.cvp_master,
            bill_no='B-001',
            bill_date=date(2023, 1, 10),
            po_no='PO-001',
            po_date=date(2023, 1, 5),
            particulars='Office Supplies',
            wo_no='WO-001',
            bg_group_id=101,
            budget_code_id=201,
            ac_head_id=301,
            amount=Decimal('150.75'),
            pvev_no='PVEV-001'
        )

    def setUp(self):
        self.client = Client()
        # Set a session compid for views that rely on it
        session = self.client.session
        session['compid'] = self.company.id
        session.save()

    def test_list_view_get(self):
        response = self.client.get(reverse('cashvoucherpayment_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/cashvoucherpayment/list.html')
        self.assertContains(response, 'Cash Voucher Payments')
        self.assertContains(response, self.cvp_master.cvp_no)

    def test_create_view_get(self):
        response = self.client.get(reverse('cashvoucherpayment_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/cashvoucherpayment/form.html')
        self.assertContains(response, 'Add New Cash Voucher Payment')

    def test_create_view_post_success(self):
        data = {
            'sys_date': '2023-02-01',
            'cvp_no': 'CVP-2023-004',
            'paid_to': 'New Vendor',
            'received_by_id': 'CUST001',
            'code_type': 2,
            'company': self.company.id,
        }
        response = self.client.post(reverse('cashvoucherpayment_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX 204 No Content for successful submission
        self.assertTrue(CashVoucherPayment.objects.filter(cvp_no='CVP-2023-004').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshCashVoucherPaymentList', response.headers['HX-Trigger'])

    def test_update_view_get(self):
        response = self.client.get(reverse('cashvoucherpayment_edit', args=[self.cvp_master.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/cashvoucherpayment/form.html')
        self.assertContains(response, 'Edit Cash Voucher Payment')
        self.assertContains(response, self.cvp_master.cvp_no)

    def test_update_view_post_success(self):
        data = {
            'sys_date': '2023-01-15', # Keep original date
            'cvp_no': 'CVP-2023-001-UPDATED',
            'paid_to': 'Updated Vendor A',
            'received_by_id': 'EMP001',
            'code_type': 1,
            'company': self.company.id,
        }
        response = self.client.post(reverse('cashvoucherpayment_edit', args=[self.cvp_master.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.cvp_master.refresh_from_db()
        self.assertEqual(self.cvp_master.cvp_no, 'CVP-2023-001-UPDATED')
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshCashVoucherPaymentList', response.headers['HX-Trigger'])

    def test_delete_view_get(self):
        response = self.client.get(reverse('cashvoucherpayment_delete', args=[self.cvp_master.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/cashvoucherpayment/confirm_delete.html')
        self.assertContains(response, 'Are you sure you want to delete')

    def test_delete_view_post_success(self):
        response = self.client.post(reverse('cashvoucherpayment_delete', args=[self.cvp_master.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(CashVoucherPayment.objects.filter(pk=self.cvp_master.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshCashVoucherPaymentList', response.headers['HX-Trigger'])
        
    def test_detail_view(self):
        response = self.client.get(reverse('cashvoucherpayment_detail', args=[self.cvp_master.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/cashvoucherpayment/detail.html')
        self.assertContains(response, self.cvp_master.cvp_no)
        self.assertContains(response, self.cvp_master.paid_to)
        self.assertContains(response, 'Office Supplies') # From detail item
        self.assertContains(response, self.cvp_master.get_company_address())

    def test_cashvoucherpayment_table_partial_view(self):
        response = self.client.get(reverse('cashvoucherpayment_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/cashvoucherpayment/_cashvoucherpayment_table.html')
        self.assertContains(response, self.cvp_master.cvp_no)
        self.assertContains(response, "S.N.") # Check for table header

    def test_company_select_options_view(self):
        response = self.client.get(reverse('company_select_options'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/partials/_company_select_options.html')
        self.assertContains(response, self.company.address)
```

### Step 5: HTMX and Alpine.js Integration

The provided templates fully integrate HTMX and Alpine.js.

*   **HTMX:**
    *   `list.html` uses `hx-get` to load the table dynamically (`hx-get="{% url 'cashvoucherpayment_table' %}"`).
    *   CRUD operations (Add, Edit, Delete buttons) use `hx-get` to load forms/confirmation dialogs into a modal (`hx-target="#modalContent"`).
    *   Form submissions (`hx-post`) have `hx-swap="none"` and trigger `HX-Trigger` headers (`refreshCashVoucherPaymentList`) to refresh the main list.
    *   The `_cashvoucherpayment_table.html` partial itself is responsible for rendering the DataTables-enabled table.
*   **Alpine.js:**
    *   The `list.html` includes `_` attributes for controlling the modal's visibility (`on click add .is-active to #modal`, `on click if event.target.id == 'modal' remove .is-active from me`).
    *   It also manages the modal content's transition (`on htmx:afterOnLoad add .is-active to #modal ...`).
*   **DataTables:**
    *   The `_cashvoucherpayment_table.html` partial initializes DataTables on the rendered table, providing client-side search, sort, and pagination.
    *   The `detail.html` also initializes DataTables for the detail lines, but with pagination/search disabled as it's a report for a single master record.

### Final Notes

This comprehensive plan addresses the migration of the ASP.NET Crystal Report viewer to a modern Django application. It demonstrates how to:
- Define database models using `managed=False` for existing schema.
- Implement complex data lookup logic within Django models, adhering to "Fat Model" principles.
- Use Django Class-Based Views (CBVs) for both generic CRUD operations and specialized report/detail views.
- Leverage HTMX for dynamic content loading and form submissions, providing a seamless user experience without full page reloads.
- Utilize DataTables for enhanced table functionality (sorting, filtering, pagination).
- Structure templates for reusability and maintainability, extending a base template and using partials.
- Provide thorough unit and integration tests to ensure correctness and stability.

This approach provides a robust and maintainable solution that aligns with modern web development best practices and allows for future expansion.