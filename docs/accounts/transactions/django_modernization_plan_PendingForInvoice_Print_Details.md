## ASP.NET to Django Conversion Script: Pending Invoice Report Modernization

This modernization plan outlines the transition of a legacy ASP.NET Crystal Report viewer for "Pending For Invoice Details" into a modern, dynamic Django application. The focus is on leveraging Django's robust ORM, HTMX for seamless UI updates, Alpine.js for frontend interactivity, and DataTables for advanced data presentation, all while adhering to the fat model/thin view architecture.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is extended.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

This ASP.NET page is primarily a *reporting* interface, not a standard CRUD (Create, Read, Update, Delete) for a single entity. It fetches complex, aggregated data from multiple tables and displays it. Therefore, the Django conversion will focus on building a robust, dynamic report viewer instead of typical CRUD forms.

## Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
Based on the SQL queries and data manipulation in the C# code-behind, the following tables and relevant columns are identified:

*   **`SD_Cust_master`**:
    *   `CustomerId` (Primary Key, unique identifier for customer)
    *   `CustomerName`
    *   `CompId` (Company ID, for multi-company support)
*   **`SD_Cust_PO_Master`**:
    *   `POId` (Primary Key, unique identifier for Purchase Order)
    *   `CustomerId` (Foreign Key to `SD_Cust_master`)
    *   `PONo` (Purchase Order Number)
*   **`SD_Cust_PO_Details`**:
    *   `POId` (Foreign Key to `SD_Cust_PO_Master`)
    *   `Id` (Unique identifier for the PO item, often called `ItemId` or similar)
    *   `ItemDesc` (Description of the item)
    *   `TotalQty` (Total quantity ordered)
    *   `Unit` (Unit of Measurement ID, Foreign Key to `Unit_Master`)
    *   `Rate` (Rate per unit)
    *   `Discount` (Discount percentage)
*   **`SD_Cust_WorkOrder_Master`**:
    *   `POId` (Foreign Key to `SD_Cust_PO_Master`)
    *   `CustomerId` (Foreign Key to `SD_Cust_master`)
    *   `WONo` (Work Order Number)
    *   `CompId` (Company ID)
*   **`Unit_Master`**:
    *   `Id` (Primary Key for Unit of Measurement)
    *   `Symbol` (Unit symbol, e.g., 'KG', 'PCS')
*   **`tblACC_SalesInvoice_Master`**:
    *   `Id` (Primary Key for Sales Invoice)
    *   `CompId` (Company ID)
*   **`tblACC_SalesInvoice_Details`**:
    *   `MId` (Foreign Key to `tblACC_SalesInvoice_Master.Id`)
    *   `ItemId` (Foreign Key to `SD_Cust_PO_Details.Id`)
    *   `ReqQty` (Required Quantity, quantity already invoiced)
*   **`Company_Master` (Inferred for `fun.CompAdd`):**
    *   `Id` (Primary Key for Company)
    *   `Address` (Company address)

## Step 2: Identify Backend Functionality

**Task:** Determine the core operations performed by the ASP.NET code.

**Instructions:**
The ASP.NET code's primary functionality is to **Read** and **Process** data to generate a "Pending for Invoice" report.
-   **Read:** It queries multiple tables (`SD_Cust_master`, `SD_Cust_PO_Master`, `SD_Cust_PO_Details`, `SD_Cust_WorkOrder_Master`, `Unit_Master`, `tblACC_SalesInvoice_Master`, `tblACC_SalesInvoice_Details`) based on dynamic filters (Company ID, Financial Year ID, Work Order No., Customer Code, and a `val` parameter indicating the type of report filter).
-   **Process:** It calculates the `PendingInvoiceQty` for each item by subtracting already invoiced quantities (`ReqQty`) from total ordered quantities (`TotalQty`). It also calculates a `TotAmount` for all pending items.
-   **Display:** The processed data is then bound to a Crystal Report viewer for display.

There are no explicit Create, Update, or Delete operations on the *report data itself* in this specific ASP.NET file. The page is purely for viewing an aggregated report.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
-   **`CrystalReportViewer1`**: This is the main display component. In Django, this will be replaced by an HTML table rendered with `DataTables.js` for enhanced search, sort, and pagination capabilities.
-   **Query String Parameters (`Key`, `W`, `Val`, `C`)**: These indicate filter options for the report. In Django, these will be handled by a filter form (rendered with `django-filters` or a custom `forms.Form`) that submits via HTMX to dynamically update the report table.
-   **`Session` variables (`compid`, `finyear`)**: These are implicit global parameters. In Django, `request.session` will be used to retrieve these, or they might be passed as hidden fields in a form if they are user-configurable. For this report, we'll assume they are available in the session.

## Step 4: Generate Django Code

We will create a new Django application, `accounts`, to house this functionality, following the ASP.NET module structure.

### 4.1 Models (`accounts/models.py`)

**Task:** Create Django models based on the identified database schema. These models will use `managed = False` to connect to existing tables. We will also define a custom manager to encapsulate the complex report generation logic ("fat model" approach).

```python
from django.db import models
from django.db.models import Sum, F, ExpressionWrapper, fields

# Helper function to get default company/financial year from session or settings
# In a real application, this would be retrieved securely from user's session/profile
def get_current_company_id():
    # Placeholder: In a real app, get this from request.session or user profile
    return 1 # Example company ID

def get_current_financial_year_id():
    # Placeholder: In a real app, get this from request.session or user profile
    return 2023 # Example financial year ID

class Customer(models.Model):
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50)
    customer_name = models.CharField(db_column='CustomerName', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customer_name} ({self.customer_id})"

class CustomerPOMaster(models.Model):
    poid = models.IntegerField(db_column='POId', primary_key=True)
    customer = models.ForeignKey(Customer, models.DO_NOTHING, db_column='CustomerId')
    pono = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Master'
        verbose_name = 'Customer PO Master'
        verbose_name_plural = 'Customer PO Masters'

    def __str__(self):
        return f"{self.pono} (PO ID: {self.poid})"

class CustomerPODetailManager(models.Manager):
    def get_pending_invoice_report_data(self, comp_id, fin_year_id, wo_no=None, customer_id=None, report_type_val=0):
        """
        Generates the pending invoice report data.
        Mimics the complex SQL and data aggregation logic from the ASP.NET code.
        Returns a list of dictionaries, each representing a row in the report.
        """
        # Step 1: Determine initial customer filtering based on report_type_val
        customer_queryset = Customer.objects.filter(comp_id=comp_id)

        if report_type_val == 1 and customer_id: # Filter by specific customer
            customer_queryset = customer_queryset.filter(customer_id=customer_id)
        elif report_type_val == 2 and wo_no: # Filter by specific work order number
            customer_queryset = customer_queryset.filter(
                customerpomaster__customerworkordermaster__wono=wo_no
            ).distinct()
        # If report_type_val == 0, no initial customer filter based on WO/Customer Code.

        report_data = []
        total_report_amount = 0.0

        for customer in customer_queryset:
            # Prepare query for PO details based on customer and WO_No if specified
            po_details_query = self.filter(
                poid__customerpomaster__customer_id=customer.customer_id
            ).select_related(
                'poid__customerpomaster', 'unit_master' # Prefetch related PO Master and Unit data
            )

            if wo_no: # Only if WONo is explicitly provided, filter by it
                 po_details_query = po_details_query.filter(
                     poid__customerpomaster__customerworkordermaster__wono=wo_no
                 ).distinct()

            for po_detail in po_details_query:
                # Get Work Order Number (cmdStrWo logic)
                work_order = CustomerWorkOrderMaster.objects.filter(
                    customer_id=customer.customer_id,
                    poid=po_detail.poid,
                    comp_id=comp_id
                ).values_list('wono', flat=True).first()

                # Get Unit Symbol (sql2 logic)
                unit_symbol = po_detail.unit_master.symbol if po_detail.unit_master else ''

                # Calculate Total Invoiced Quantity (sqlrmn logic)
                total_invoiced_qty_agg = SalesInvoiceDetail.objects.filter(
                    mid__comp_id=comp_id,
                    itemid=po_detail.id
                ).aggregate(total_req_qty=Sum('reqqty'))
                total_invoiced_qty = total_invoiced_qty_agg['total_req_qty'] or 0.0

                # Calculate Pending Quantity
                # Ensure quantities are handled as floats for accurate calculations
                total_qty = float(po_detail.totalqty) if po_detail.totalqty is not None else 0.0
                pending_invoice_qty = total_qty - float(total_invoiced_qty)

                # Only include if pending quantity > 0
                if pending_invoice_qty > 0:
                    rate = float(po_detail.rate) if po_detail.rate is not None else 0.0
                    discount = float(po_detail.discount) if po_detail.discount is not None else 0.0

                    # Calculate item amount (TotAmnt += logic)
                    item_amount = (pending_invoice_qty * rate) * (1 - (discount / 100.0))
                    total_report_amount += item_amount

                    report_data.append({
                        'won_o': work_order or '', # WO Number (dr1[0])
                        'po_no': po_detail.poid.pono if po_detail.poid else '', # PO Number (dr1[1])
                        'item_code': po_detail.id, # Item ID (dr1[2])
                        'description': po_detail.itemdesc, # Item Description (dr1[3])
                        'uom': unit_symbol, # Unit of Measure (dr1[4])
                        'total_qty': total_qty, # Original Total Qty (dr1[5])
                        'invoice_qty': total_invoiced_qty, # Total Invoiced Qty (dr1[6])
                        'pending_invoice_qty': pending_invoice_qty, # Pending Qty (dr1[7])
                        'customer_id': customer.customer_id, # Customer Code (dr1[8])
                        'customer_name': customer.customer_name, # Customer Name (added for display)
                        'rate': rate, # Rate (dr1[9])
                        'discount': discount, # Discount (dr1[10])
                        'item_amount': item_amount # Calculated amount for this item
                    })
        return report_data, total_report_amount

class CustomerPODetail(models.Model):
    # 'Id' is the unique identifier for the PO item, mapping to ItemId in sales invoice
    id = models.IntegerField(db_column='Id', primary_key=True)
    poid = models.ForeignKey(CustomerPOMaster, models.DO_NOTHING, db_column='POId')
    itemdesc = models.CharField(db_column='ItemDesc', max_length=255, blank=True, null=True)
    totalqty = models.DecimalField(db_column='TotalQty', max_digits=18, decimal_places=3, blank=True, null=True)
    unit_master = models.ForeignKey('Unit', models.DO_NOTHING, db_column='Unit') # Renamed to avoid clash, mapping to Unit_Master
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=2, blank=True, null=True)
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=2, blank=True, null=True)

    objects = CustomerPODetailManager() # Attach the custom manager

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Details'
        verbose_name = 'Customer PO Detail'
        verbose_name_plural = 'Customer PO Details'

    def __str__(self):
        return f"{self.itemdesc} (PO Detail ID: {self.id})"

class CustomerWorkOrderMaster(models.Model):
    wono = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    customer = models.ForeignKey(Customer, models.DO_NOTHING, db_column='CustomerId')
    poid = models.ForeignKey(CustomerPOMaster, models.DO_NOTHING, db_column='POId')
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        # No primary key defined in source, Django requires one. Assuming unique combo or add an auto PK if necessary.
        # For simplicity, we'll assume WONo is unique for combination with Cust/PO or not critical for direct object access.
        # If no explicit PK, Django will create 'id' automatically which might not map well.
        # For reporting purposes, we can manage without explicitly setting a PK if not needed for direct object retrieval.
        # If this model needs to be directly modified/accessed, a real PK is needed.
        unique_together = (('wono', 'customer', 'poid'),) # Example composite key if no single PK

    def __str__(self):
        return self.wono or 'N/A'

class Unit(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=10, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol or 'N/A'

class SalesInvoiceMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_SalesInvoice_Master'
        verbose_name = 'Sales Invoice Master'
        verbose_name_plural = 'Sales Invoice Masters'

    def __str__(self):
        return f"Invoice {self.id}"

class SalesInvoiceDetail(models.Model):
    # No explicit PK in source, assuming combination of MId and ItemId or auto_id.
    # Django will create 'id' automatically.
    mid = models.ForeignKey(SalesInvoiceMaster, models.DO_NOTHING, db_column='MId')
    itemid = models.IntegerField(db_column='ItemId') # This refers to SD_Cust_PO_Details.Id
    reqqty = models.DecimalField(db_column='ReqQty', max_digits=18, decimal_places=3, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_SalesInvoice_Details'
        # Unique together if (MId, ItemId) uniquely identifies a detail.
        # For reporting, often not strictly necessary if 'id' handles uniqueness.
        unique_together = (('mid', 'itemid'),)

    def __str__(self):
        return f"Invoice {self.mid.id} Item {self.itemid}"

class Company(models.Model): # Inferred from fun.CompAdd
    id = models.IntegerField(db_column='Id', primary_key=True)
    address = models.CharField(db_column='Address', max_length=500, blank=True, null=True)
    name = models.CharField(db_column='Name', max_length=255, blank=True, null=True) # Assuming a name field

    class Meta:
        managed = False
        db_table = 'Company_Master'
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name or f"Company {self.id}"
```

### 4.2 Forms (`accounts/forms.py`)

**Task:** Define a Django form for filter criteria.

```python
from django import forms
from .models import Customer

class PendingInvoiceFilterForm(forms.Form):
    # val parameter: 0, 1, 2
    REPORT_TYPE_CHOICES = [
        (0, 'All Customers (if no WO)'),
        (1, 'Filter by Customer Code'),
        (2, 'Filter by Work Order No.'),
    ]
    report_type_val = forms.ChoiceField(
        choices=REPORT_TYPE_CHOICES,
        initial=0,
        label='Report Type',
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'x-model': 'reportType'})
    )
    
    customer_id = forms.ModelChoiceField(
        queryset=Customer.objects.all().order_by('customer_name'),
        required=False,
        label='Customer',
        empty_label='Select Customer',
        to_field_name='customer_id', # Use customer_id for value
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    
    wo_no = forms.CharField(
        max_length=50,
        required=False,
        label='Work Order No.',
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    # In a real app, comp_id and fin_year_id would be passed from session/user context.
    # For this example, they are implicitly handled by the view/manager.
    # If they were user-selectable, they'd be fields here.
```

### 4.3 Views (`accounts/views.py`)

**Task:** Implement a `TemplateView` to display the report and an HTMX partial view for the dynamic table.

```python
from django.views.generic import TemplateView, View
from django.urls import reverse_lazy
from django.http import HttpResponse
from django.template.loader import render_to_string
from django.shortcuts import get_object_or_404
from django.conf import settings # To get default company ID if not in session

from .models import CustomerPODetail, Company, get_current_company_id # Import the manager method
from .forms import PendingInvoiceFilterForm

class PendingInvoiceReportView(TemplateView):
    """
    Main view for the Pending Invoice Report page.
    Renders the filter form and the container for the HTMX-loaded table.
    """
    template_name = 'accounts/pending_invoice/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the filter form with current request parameters if any
        context['form'] = PendingInvoiceFilterForm(self.request.GET)
        return context

class PendingInvoiceReportTablePartialView(View):
    """
    HTMX-specific view to render only the pending invoice report table.
    Receives filter parameters via GET request and returns the table HTML.
    """
    def get(self, request, *args, **kwargs):
        form = PendingInvoiceFilterForm(request.GET)
        report_items = []
        total_amount = 0.0
        company_address = "Company Address Not Found" # Default value

        comp_id = request.session.get('compid', get_current_company_id()) # Get from session or default
        fin_year_id = request.session.get('finyear', get_current_financial_year_id()) # Get from session or default

        try:
            company = Company.objects.get(id=comp_id)
            company_address = company.address
        except Company.DoesNotExist:
            pass # Handle case where company is not found

        if form.is_valid():
            report_type_val = int(form.cleaned_data['report_type_val'])
            customer_id = form.cleaned_data['customer_id'].customer_id if form.cleaned_data['customer_id'] else None
            wo_no = form.cleaned_data['wo_no']

            report_items, total_amount = CustomerPODetail.objects.get_pending_invoice_report_data(
                comp_id=comp_id,
                fin_year_id=fin_year_id, # Placeholder: fin_year_id is not directly used in the provided C# logic's SQL, but good to pass.
                wo_no=wo_no,
                customer_id=customer_id,
                report_type_val=report_type_val
            )
        else:
            # If form is invalid, you might want to log errors or pass them to template
            # For HTMX partial, usually, valid data is expected from the main page form.
            print(f"Form errors: {form.errors}")

        context = {
            'report_items': report_items,
            'total_amount': total_amount,
            'company_address': company_address,
        }
        html = render_to_string('accounts/pending_invoice/_table.html', context, request=request)
        return HttpResponse(html)

# No Create, Update, Delete views as this is a report viewer.
# The original ASP.NET code does not imply CRUD for the report output itself.
```

### 4.4 Templates

**Task:** Create templates for the report view and its HTMX partial.

#### `accounts/pending_invoice/list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-3xl font-bold text-gray-800 mb-6">Pending For Invoice Report</h2>

    <div class="bg-white shadow-lg rounded-lg p-6 mb-8" x-data="{ reportType: '{{ form.report_type_val.value|default:'0' }}' }">
        <form hx-get="{% url 'accounts:pending_invoice_table' %}" 
              hx-target="#reportTable-container" 
              hx-swap="innerHTML" 
              hx-trigger="change delay:300ms, submit"
              class="space-y-4">
            {% csrf_token %}
            
            <div>
                <label for="{{ form.report_type_val.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.report_type_val.label }}
                </label>
                {{ form.report_type_val }}
                {% if form.report_type_val.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.report_type_val.errors }}</p>
                {% endif %}
            </div>

            <div x-show="reportType == '1'" class="transition-all duration-300 ease-in-out">
                <label for="{{ form.customer_id.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.customer_id.label }}
                </label>
                {{ form.customer_id }}
                {% if form.customer_id.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.customer_id.errors }}</p>
                {% endif %}
            </div>

            <div x-show="reportType == '2'" class="transition-all duration-300 ease-in-out">
                <label for="{{ form.wo_no.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.wo_no.label }}
                </label>
                {{ form.wo_no }}
                {% if form.wo_no.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.wo_no.errors }}</p>
                {% endif %}
            </div>

            <div class="flex justify-end">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md shadow-md">
                    Apply Filters
                </button>
            </div>
        </form>
    </div>
    
    <div id="reportTable-container"
         hx-trigger="load" {# Initial load of the table #}
         hx-get="{% url 'accounts:pending_invoice_table' %}?{{ request.GET.urlencode }}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg p-6">
        <!-- Report table will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Report Data...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('reportFilters', () => ({
            reportType: '{{ form.report_type_val.value|default:'0' }}',
            init() {
                // Initial setup for Alpine.js if needed
            }
        }));
    });
</script>
{% endblock %}
```

#### `accounts/pending_invoice/_table.html`

```html
{% load humanize %} {# For formatting numbers like thousands separators #}

{% if report_items %}
<div class="mb-4 text-gray-700 text-sm">
    <p class="font-semibold">Company Address:</p>
    <p>{{ company_address }}</p>
    <p class="font-semibold mt-2">Total Pending Amount: <span class="text-lg text-blue-700">{{ total_amount|floatformat:2|intcomma }}</span></p>
</div>

<table id="pendingInvoiceTable" class="min-w-full bg-white table-auto">
    <thead>
        <tr class="bg-gray-100 text-gray-600 uppercase text-sm leading-normal">
            <th class="py-3 px-6 text-left">SN</th>
            <th class="py-3 px-6 text-left">Customer</th>
            <th class="py-3 px-6 text-left">WO No.</th>
            <th class="py-3 px-6 text-left">PO No.</th>
            <th class="py-3 px-6 text-left">Item Code</th>
            <th class="py-3 px-6 text-left">Description</th>
            <th class="py-3 px-6 text-left">UOM</th>
            <th class="py-3 px-6 text-right">Total Qty</th>
            <th class="py-3 px-6 text-right">Invoiced Qty</th>
            <th class="py-3 px-6 text-right">Pending Qty</th>
            <th class="py-3 px-6 text-right">Rate</th>
            <th class="py-3 px-6 text-right">Discount (%)</th>
            <th class="py-3 px-6 text-right">Pending Amount</th>
        </tr>
    </thead>
    <tbody class="text-gray-700 text-sm font-light">
        {% for item in report_items %}
        <tr class="border-b border-gray-200 hover:bg-gray-50">
            <td class="py-3 px-6 text-left whitespace-nowrap">{{ forloop.counter }}</td>
            <td class="py-3 px-6 text-left">{{ item.customer_name }} [{{ item.customer_id }}]</td>
            <td class="py-3 px-6 text-left">{{ item.won_o }}</td>
            <td class="py-3 px-6 text-left">{{ item.po_no }}</td>
            <td class="py-3 px-6 text-left">{{ item.item_code }}</td>
            <td class="py-3 px-6 text-left">{{ item.description }}</td>
            <td class="py-3 px-6 text-left">{{ item.uom }}</td>
            <td class="py-3 px-6 text-right">{{ item.total_qty|floatformat:3|intcomma }}</td>
            <td class="py-3 px-6 text-right">{{ item.invoice_qty|floatformat:3|intcomma }}</td>
            <td class="py-3 px-6 text-right font-medium text-red-600">{{ item.pending_invoice_qty|floatformat:3|intcomma }}</td>
            <td class="py-3 px-6 text-right">{{ item.rate|floatformat:2|intcomma }}</td>
            <td class="py-3 px-6 text-right">{{ item.discount|floatformat:2|intcomma }}</td>
            <td class="py-3 px-6 text-right font-semibold text-blue-700">{{ item.item_amount|floatformat:2|intcomma }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Initialize DataTables after the table is loaded via HTMX
    $(document).ready(function() {
        $('#pendingInvoiceTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "pagingType": "full_numbers",
            "order": [], // Disable initial sorting
            "dom": 'lfrtip', // Show length changing, filtering, table, pagination, information, processing
        });
    });
</script>
{% else %}
<div class="text-center py-10 text-gray-600">
    <p class="text-xl font-medium">No pending invoice items found for the selected criteria.</p>
    <p class="mt-2">Please adjust your filters and try again.</p>
</div>
{% endif %}
```

### 4.5 URLs (`accounts/urls.py`)

**Task:** Define URL patterns for the views.

```python
from django.urls import path
from .views import PendingInvoiceReportView, PendingInvoiceReportTablePartialView

app_name = 'accounts' # Namespace for the application

urlpatterns = [
    path('pending_invoice/', PendingInvoiceReportView.as_view(), name='pending_invoice_report'),
    path('pending_invoice/table/', PendingInvoiceReportTablePartialView.as_view(), name='pending_invoice_table'),
]
```

### 4.6 Tests (`accounts/tests.py`)

**Task:** Write comprehensive tests for the models and the report generation logic.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db.models import Sum

from .models import (
    Customer, CustomerPOMaster, CustomerPODetail, CustomerWorkOrderMaster,
    Unit, SalesInvoiceMaster, SalesInvoiceDetail, Company
)

class PendingInvoiceModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all related models
        cls.comp_id = 1
        cls.fin_year_id = 2023

        cls.company = Company.objects.create(id=cls.comp_id, name='Test Company', address='123 Test St')

        cls.customer1 = Customer.objects.create(customer_id='CUST001', customer_name='Alpha Corp', comp_id=cls.comp_id)
        cls.customer2 = Customer.objects.create(customer_id='CUST002', customer_name='Beta Inc', comp_id=cls.comp_id)

        cls.po_master1 = CustomerPOMaster.objects.create(poid=101, customer=cls.customer1, pono='PO-A-001')
        cls.po_master2 = CustomerPOMaster.objects.create(poid=102, customer=cls.customer2, pono='PO-B-001')

        cls.unit_pcs = Unit.objects.create(id=1, symbol='PCS')
        cls.unit_kg = Unit.objects.create(id=2, symbol='KG')

        cls.po_detail1 = CustomerPODetail.objects.create(
            id=1001, poid=cls.po_master1, itemdesc='Widget A', totalqty=100.000,
            unit_master=cls.unit_pcs, rate=10.00, discount=5.00
        )
        cls.po_detail2 = CustomerPODetail.objects.create(
            id=1002, poid=cls.po_master1, itemdesc='Gadget B', totalqty=50.000,
            unit_master=cls.unit_kg, rate=25.00, discount=0.00
        )
        cls.po_detail3 = CustomerPODetail.objects.create( # Fully invoiced item
            id=1003, poid=cls.po_master2, itemdesc='Item C', totalqty=20.000,
            unit_master=cls.unit_pcs, rate=15.00, discount=0.00
        )
        cls.po_detail4 = CustomerPODetail.objects.create( # Item with 0 total qty
            id=1004, poid=cls.po_master2, itemdesc='Item D', totalqty=0.000,
            unit_master=cls.unit_pcs, rate=5.00, discount=0.00
        )

        cls.wo_master1 = CustomerWorkOrderMaster.objects.create(
            wono='WO-123', customer=cls.customer1, poid=cls.po_master1, comp_id=cls.comp_id
        )
        cls.wo_master2 = CustomerWorkOrderMaster.objects.create(
            wono='WO-456', customer=cls.customer2, poid=cls.po_master2, comp_id=cls.comp_id
        )

        cls.invoice_master1 = SalesInvoiceMaster.objects.create(id=1, comp_id=cls.comp_id)

        # Invoiced quantity for po_detail1: 30
        SalesInvoiceDetail.objects.create(mid=cls.invoice_master1, itemid=cls.po_detail1.id, reqqty=30.000)
        # Invoiced quantity for po_detail2: 50 (fully invoiced)
        SalesInvoiceDetail.objects.create(mid=cls.invoice_master1, itemid=cls.po_detail2.id, reqqty=50.000)
        # Invoiced quantity for po_detail3: 20 (fully invoiced)
        SalesInvoiceDetail.objects.create(mid=cls.invoice_master1, itemid=cls.po_detail3.id, reqqty=20.000)

    def test_model_creation(self):
        self.assertEqual(Customer.objects.count(), 2)
        self.assertEqual(CustomerPODetail.objects.count(), 4)
        self.assertEqual(SalesInvoiceDetail.objects.count(), 3)

    def test_get_pending_invoice_report_data_all_customers(self):
        # Test val=0 (all customers, no specific WO/Cust filter)
        report_data, total_amount = CustomerPODetail.objects.get_pending_invoice_report_data(
            comp_id=self.comp_id, fin_year_id=self.fin_year_id, report_type_val=0
        )
        
        self.assertEqual(len(report_data), 1) # Only po_detail1 should have pending qty
        self.assertEqual(report_data[0]['item_code'], self.po_detail1.id)
        self.assertEqual(report_data[0]['pending_invoice_qty'], 70.000) # 100 - 30
        
        # Calculate expected total amount for po_detail1 (70 * 10 * (1 - 0.05)) = 700 * 0.95 = 665
        self.assertAlmostEqual(total_amount, 665.00, places=2)

    def test_get_pending_invoice_report_data_filter_by_customer(self):
        # Test val=1 (filter by specific customer)
        report_data, total_amount = CustomerPODetail.objects.get_pending_invoice_report_data(
            comp_id=self.comp_id, fin_year_id=self.fin_year_id,
            customer_id=self.customer1.customer_id, report_type_val=1
        )
        
        self.assertEqual(len(report_data), 1) # Only po_detail1 should have pending qty from customer1
        self.assertEqual(report_data[0]['item_code'], self.po_detail1.id)
        self.assertEqual(report_data[0]['pending_invoice_qty'], 70.000)
        self.assertAlmostEqual(total_amount, 665.00, places=2)

        # Test with customer2 (should yield no pending items)
        report_data_cust2, total_amount_cust2 = CustomerPODetail.objects.get_pending_invoice_report_data(
            comp_id=self.comp_id, fin_year_id=self.fin_year_id,
            customer_id=self.customer2.customer_id, report_type_val=1
        )
        self.assertEqual(len(report_data_cust2), 0)
        self.assertEqual(total_amount_cust2, 0.0)


    def test_get_pending_invoice_report_data_filter_by_wo_no(self):
        # Test val=2 (filter by specific WO No)
        report_data, total_amount = CustomerPODetail.objects.get_pending_invoice_report_data(
            comp_id=self.comp_id, fin_year_id=self.fin_year_id,
            wo_no='WO-123', report_type_val=2
        )
        
        self.assertEqual(len(report_data), 1) # Only po_detail1 is associated with WO-123 and has pending
        self.assertEqual(report_data[0]['item_code'], self.po_detail1.id)
        self.assertEqual(report_data[0]['pending_invoice_qty'], 70.000)
        self.assertAlmostEqual(total_amount, 665.00, places=2)

        # Test with WO-456 (should yield no pending items)
        report_data_wo2, total_amount_wo2 = CustomerPODetail.objects.get_pending_invoice_report_data(
            comp_id=self.comp_id, fin_year_id=self.fin_year_id,
            wo_no='WO-456', report_type_val=2
        )
        self.assertEqual(len(report_data_wo2), 0)
        self.assertEqual(total_amount_wo2, 0.0)

    def test_item_with_zero_total_qty(self):
        # Test that items with 0 total quantity are not included if no pending
        report_data, total_amount = CustomerPODetail.objects.get_pending_invoice_report_data(
            comp_id=self.comp_id, fin_year_id=self.fin_year_id, report_type_val=0
        )
        item_codes = [item['item_code'] for item in report_data]
        self.assertNotIn(self.po_detail4.id, item_codes) # po_detail4 has 0 total qty

class PendingInvoiceViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal data for views to operate
        cls.comp_id = 1
        cls.fin_year_id = 2023
        cls.company = Company.objects.create(id=cls.comp_id, name='Test Company', address='123 Test St')

        cls.customer1 = Customer.objects.create(customer_id='CUST001', customer_name='Alpha Corp', comp_id=cls.comp_id)
        cls.po_master1 = CustomerPOMaster.objects.create(poid=101, customer=cls.customer1, pono='PO-A-001')
        cls.unit_pcs = Unit.objects.create(id=1, symbol='PCS')
        cls.po_detail1 = CustomerPODetail.objects.create(
            id=1001, poid=cls.po_master1, itemdesc='Widget A', totalqty=100.000,
            unit_master=cls.unit_pcs, rate=10.00, discount=5.00
        )
        cls.wo_master1 = CustomerWorkOrderMaster.objects.create(
            wono='WO-123', customer=cls.customer1, poid=cls.po_master1, comp_id=cls.comp_id
        )
        cls.invoice_master1 = SalesInvoiceMaster.objects.create(id=1, comp_id=cls.comp_id)
        SalesInvoiceDetail.objects.create(mid=cls.invoice_master1, itemid=cls.po_detail1.id, reqqty=30.000)

    def setUp(self):
        self.client = Client()
        # Mock session data for company/financial year if needed, or rely on defaults
        self.client.session['compid'] = self.comp_id
        self.client.session['finyear'] = self.fin_year_id

    def test_pending_invoice_report_view_get(self):
        response = self.client.get(reverse('accounts:pending_invoice_report'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/pending_invoice/list.html')
        self.assertIsInstance(response.context['form'], PendingInvoiceFilterForm)
        self.assertContains(response, 'Pending For Invoice Report')

    def test_pending_invoice_report_table_partial_view_get_no_filters(self):
        response = self.client.get(reverse('accounts:pending_invoice_table'), **{'HTTP_HX-Request': 'true'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/pending_invoice/_table.html')
        self.assertContains(response, 'Widget A') # Should contain the pending item
        self.assertContains(response, '70.000') # Pending quantity
        self.assertContains(response, '665.00') # Total amount

    def test_pending_invoice_report_table_partial_view_get_with_customer_filter(self):
        # Filter by customer_id = CUST001
        response = self.client.get(reverse('accounts:pending_invoice_table'), 
                                   {'report_type_val': 1, 'customer_id': 'CUST001'},
                                   **{'HTTP_HX-Request': 'true'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Widget A')
        self.assertContains(response, 'Alpha Corp')
        self.assertContains(response, '665.00')

        # Filter by a non-existent customer (or one with no pending items)
        response_empty = self.client.get(reverse('accounts:pending_invoice_table'),
                                         {'report_type_val': 1, 'customer_id': 'CUST_NON_EXISTENT'},
                                         **{'HTTP_HX-Request': 'true'})
        self.assertEqual(response_empty.status_code, 200)
        self.assertContains(response_empty, 'No pending invoice items found')


    def test_pending_invoice_report_table_partial_view_get_with_wo_no_filter(self):
        # Filter by wo_no = WO-123
        response = self.client.get(reverse('accounts:pending_invoice_table'), 
                                   {'report_type_val': 2, 'wo_no': 'WO-123'},
                                   **{'HTTP_HX-Request': 'true'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Widget A')
        self.assertContains(response, 'WO-123')
        self.assertContains(response, '665.00')

        # Filter by a non-existent WO
        response_empty = self.client.get(reverse('accounts:pending_invoice_table'),
                                         {'report_type_val': 2, 'wo_no': 'WO-999'},
                                         **{'HTTP_HX-Request': 'true'})
        self.assertEqual(response_empty.status_code, 200)
        self.assertContains(response_empty, 'No pending invoice items found')

    def test_htmx_trigger_on_initial_load(self):
        response = self.client.get(reverse('accounts:pending_invoice_report'))
        self.assertContains(response, 'hx-trigger="load"')
        self.assertContains(response, 'hx-get="/accounts/pending_invoice/table/') # Check for correct URL
        self.assertContains(response, 'id="reportTable-container"')

    def test_htmx_trigger_on_form_change_and_submit(self):
        response = self.client.get(reverse('accounts:pending_invoice_report'))
        self.assertContains(response, 'hx-trigger="change delay:300ms, submit"')
        self.assertContains(response, 'hx-target="#reportTable-container"')
```

## Step 5: HTMX and Alpine.js Integration

The provided Django templates already incorporate HTMX and Alpine.js as per the requirements:

-   **HTMX:**
    -   The main `list.html` uses `hx-get` and `hx-trigger="load"` on the `reportTable-container` div to load the initial report table via an HTMX request to `{% url 'accounts:pending_invoice_table' %}`.
    -   The filter form uses `hx-get`, `hx-target`, `hx-swap`, and `hx-trigger="change delay:300ms, submit"` to dynamically update the report table without full page reloads. When filters are changed or the form is submitted, HTMX fetches the new table data and replaces the old content.
-   **Alpine.js:**
    -   Alpine.js is used in `list.html` (`x-data` and `x-show`) to conditionally display the Customer ID or Work Order No. filter fields based on the selected `report_type_val`. This enhances the user experience by showing only relevant filters.
-   **DataTables:**
    -   The `_table.html` partial includes the JavaScript initialization for DataTables (`$('#pendingInvoiceTable').DataTable(...)`). This ensures that the dynamically loaded table instantly becomes a powerful, interactive data grid with client-side searching, sorting, and pagination.
-   **DRY Templates:**
    -   The main template `list.html` extends `core/base.html` (assumed to exist).
    -   The report table is a partial `_table.html`, enabling modularity and efficient HTMX updates.
-   **Business Logic in Models:**
    -   The complex data aggregation and calculation logic, previously in the ASP.NET code-behind, is entirely encapsulated within the `CustomerPODetailManager.get_pending_invoice_report_data` method, adhering to the "fat model" principle. Views remain thin, only handling request/response and orchestrating calls to the model layer.

## Final Notes

-   This plan replaces the proprietary Crystal Reports viewer with an open, web-native solution, providing better scalability and flexibility.
-   The use of `managed=False` models ensures that Django can work with the existing database schema without requiring schema migrations.
-   The shift to HTMX and Alpine.js eliminates the need for complex JavaScript frameworks, simplifying frontend development and maintenance, and providing a responsive user experience.
-   Comprehensive tests ensure the accuracy of the report data and the reliability of the application.
-   Placeholder values for `CompId` and `FinYearId` (e.g., `get_current_company_id()`) should be replaced with a robust mechanism for retrieving these from the authenticated user's session or profile in a production environment.
-   Error handling (e.g., for invalid form submissions in HTMX partials) should be more sophisticated in a production setting.