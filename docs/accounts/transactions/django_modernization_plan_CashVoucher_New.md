## ASP.NET to Django Conversion Script: Cash Voucher Module

This document outlines a comprehensive modernization plan to transition the provided ASP.NET Cash Voucher application to a modern Django-based solution. The approach focuses on leveraging AI-assisted automation, adhering to Django 5.0+ best practices, and ensuring a fat model/thin view architecture with HTMX and Alpine.js for dynamic interactions.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include `base.html` template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

### Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code interacts with several database tables for managing cash vouchers, temporary entries, and lookup data.

**Identified Tables and Columns:**

*   **`tblACC_CashVoucher_Payment_Temp`** (Temporary payment entries, session-bound)
    *   `Id` (Primary Key, INT)
    *   `CompId` (INT, Company ID)
    *   `SessionId` (VARCHAR, User Session ID)
    *   `BillNo` (VARCHAR)
    *   `BillDate` (VARCHAR, stored as string 'dd-MM-yyyy')
    *   `PONo` (VARCHAR)
    *   `PODate` (VARCHAR, stored as string 'dd-MM-yyyy')
    *   `Particulars` (VARCHAR)
    *   `WONo` (VARCHAR, Work Order Number)
    *   `BGGroup` (INT, Business Group ID)
    *   `AcHead` (INT, Account Head ID)
    *   `Amount` (DECIMAL)
    *   `BudgetCode` (INT, Budget Code ID)
    *   `PVEVNo` (VARCHAR)

*   **`tblACC_CashVoucher_Payment_Master`** (Finalized payment vouchers)
    *   `Id` (Primary Key, INT)
    *   `SysDate` (VARCHAR, System Date)
    *   `SysTime` (VARCHAR, System Time)
    *   `SessionId` (VARCHAR, User Session ID)
    *   `CompId` (INT, Company ID)
    *   `FinYearId` (INT, Financial Year ID)
    *   `CVPNo` (VARCHAR, Cash Voucher Payment Number, 4-digit sequence)
    *   `PaidTo` (VARCHAR)
    *   `ReceivedBy` (VARCHAR, Code/ID from related master)
    *   `CodeType` (INT, Type: 1-Employee, 2-Customer, 3-Supplier)

*   **`tblACC_CashVoucher_Payment_Details`** (Details of finalized payment vouchers)
    *   `Id` (Primary Key, INT)
    *   `MId` (INT, Foreign Key to `tblACC_CashVoucher_Payment_Master.Id`)
    *   `BillNo` (VARCHAR)
    *   `BillDate` (VARCHAR)
    *   `PONo` (VARCHAR)
    *   `PODate` (VARCHAR)
    *   `Particulars` (VARCHAR)
    *   `WONo` (VARCHAR)
    *   `BGGroup` (INT)
    *   `AcHead` (INT)
    *   `Amount` (DECIMAL)
    *   `BudgetCode` (INT)
    *   `PVEVNo` (VARCHAR)

*   **`tblACC_CashVoucher_Receipt_Master`** (Finalized receipt vouchers)
    *   `Id` (Primary Key, INT)
    *   `SysDate` (VARCHAR)
    *   `SysTime` (VARCHAR)
    *   `SessionId` (VARCHAR)
    *   `CompId` (INT)
    *   `FinYearId` (INT)
    *   `CVRNo` (VARCHAR, Cash Voucher Receipt Number)
    *   `CashReceivedAgainst` (VARCHAR, Code/ID)
    *   `CashReceivedBy` (VARCHAR, Code/ID)
    *   `WONo` (VARCHAR)
    *   `BGGroup` (INT)
    *   `AcHead` (INT)
    *   `Amount` (DECIMAL)
    *   `Others` (VARCHAR)
    *   `CodeTypeRA` (INT, Type for Received Against)
    *   `CodeTypeRB` (INT, Type for Received By)
    *   `BudgetCode` (INT)

*   **Supporting Tables (Lookups & Validations):**
    *   `tblHR_OfficeStaff`: `EmpId`, `EmployeeName`
    *   `SD_Cust_master`: `CustomerId`, `CustomerName`
    *   `tblMM_Supplier_master`: `SupplierId`, `SupplierName`
    *   `BusinessGroup`: `Id`, `Symbol`
    *   `AccHead`: `Id`, `Symbol`, `Description`, `Category`
    *   `tblMIS_BudgetCode`: `Id`, `Description`, `Symbol`
    *   `tblACC_BillBooking_Master`: `Id`, `CompId`, `FinYearId`, `PVEVNo`
    *   `tblACC_CashAmtLimit`: `Id`, `Amount`, `Active`

### Step 2: Identify Backend Functionality

**Analysis:**
The ASP.NET code handles comprehensive CRUD-like operations across temporary and permanent tables, along with various business validations.

*   **Create (Payment Detail):**
    *   `btnPaymentAdd_Click`: Adds a new payment detail entry to `tblACC_CashVoucher_Payment_Temp`.
    *   Validations: Required fields (BillNo, BillDate, Particulars, Amount), date format, amount format, WO No validity, Budget Code selection, PVEVNo existence, total amount against budget and cash limits.
*   **Create (Final Payment):**
    *   `btnPayProceed_Click`: Transfers all temporary payment entries to `tblACC_CashVoucher_Payment_Master` and `tblACC_CashVoucher_Payment_Details`, generates a new `CVPNo`.
    *   Validations: `PaidTo`, `ReceivedBy`, `CodeType` validity, presence of temporary records.
*   **Create (Final Receipt):**
    *   `btnReceiptProceed_Click`: Inserts a new receipt entry into `tblACC_CashVoucher_Receipt_Master`, generates `CVRNo`.
    *   Validations: `AmountRec`, `Others`, `CashReceivedAgainst`, `CashReceivedBy` validity, WO No validity.
*   **Read (Payment Details):**
    *   `FillData()`: Populates `GridView1` from `tblACC_CashVoucher_Payment_Temp` with joins to `AccHead` and `BusinessGroup`, and a lookup for `BudgetCode`.
*   **Read (Receipt Details):**
    *   `FillDataRec()`: Populates `GridView2` from `tblACC_CashVoucher_Receipt_Master` with joins to `AccHead` and `BusinessGroup`, and a lookup for `BudgetCode`.
*   **Delete (Payment Detail):**
    *   `GridView1_RowCommand1` (CommandName="Del"): Deletes an entry from `tblACC_CashVoucher_Payment_Temp`.
*   **Dynamic Data Loading:**
    *   `WONoGroup()`, `AcHead()`: Dynamically populate dropdowns based on radio button selections.
    *   `sql()`, `sql1()`, `sql2()` (WebMethods): Provide autocomplete suggestions for Employee, Customer, Supplier names based on `CodeType`.

### Step 3: Infer UI Components

**Analysis:**
The ASP.NET UI consists of a tabbed interface (Payment/Receipt) with various input fields, dropdowns, radio button lists, and data grids.

*   **TabContainer (`TabContainer1`):** Represents the main navigation between Payment and Receipt sections. This will be handled by HTMX.
*   **Textboxes:** `txtPaidTo`, `txtNewCustomerName`, `txtBillNo`, `textBillDate`, `txtPONo`, `textPODate`, `txtAmount`, `txtWONo`, `txtPVEVNO`, `txtAmountRec`, `txtNewCustomerNameRA`, `txtNewCustomerNameRB`, `txtWONoRec`, `txtOthers`. These map directly to Django form fields (TextInput, DateInput, NumberInput, Textarea).
*   **Dropdowns:** `ddlCodeType`, `drpGroup`, `drpBudgetcode`, `drpAcHead`, `ddlCodeTypeRA`, `ddlCodeTypeRB`, `drpGroupRec`, `drpBudgetcode1`, `drpAcHeadRec`. These map to Django `Select` widgets or `ModelChoiceField`.
*   **Radio Button Lists:** `RadioButtonWONoGroup`, `RadioButtonAcHead`, `RadioButtonWONoGroupRec`, `RadioButtonAcHeadRec`. These map to Django `RadioSelect` widgets. Their `AutoPostBack` functionality will be handled by HTMX.
*   **Buttons:** `btnPaymentAdd`, `btnPayProceed`, `btnReceiptProceed`. These will trigger HTMX POST requests.
*   **Gridviews:** `GridView1` (Payment temporary items), `GridView2` (Receipt finalized items). These will be replaced by DataTables.
*   **Validation Controls:** `RequiredFieldValidator`, `RegularExpressionValidator`. Django forms will handle these with `required=True` and `RegexValidator`.
*   **AJAX Extenders:** `AutoCompleteExtender`, `CalendarExtender`. HTMX will replace autocomplete via dedicated endpoints, and date pickers will use client-side libraries (e.g., flatpickr) or HTML5 date inputs.

---

### Step 4: Generate Django Code

**Assumed App Name:** `accounts`

#### 4.1 Models (`accounts/models.py`)

```python
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()

# Helper for parsing "Name [ID]" format from ASP.NET autocomplete
# This would ideally be in a service/utility, but for demo, here.
def get_code_from_name_id_string(name_id_str):
    if '[' in name_id_str and ']' in name_id_str:
        return name_id_str.split('[')[-1].replace(']', '')
    return name_id_str # Return as is if no ID found

def get_name_by_code_and_type(code, code_type, company_id):
    # Placeholder for clsFunctions.EmpCustSupplierNames
    if code_type == 1: # Employee
        try:
            return Employee.objects.get(emp_id=code, company_id=company_id).employee_name
        except Employee.DoesNotExist:
            pass
    elif code_type == 2: # Customer
        try:
            return Customer.objects.get(customer_id=code, company_id=company_id).customer_name
        except Customer.DoesNotExist:
            pass
    elif code_type == 3: # Supplier
        try:
            return Supplier.objects.get(supplier_id=code, company_id=company_id).supplier_name
        except Supplier.DoesNotExist:
            pass
    return f"N/A ({code})"

# --- Supporting Models (Minimal definitions to enable relationships) ---
class Company(models.Model):
    # Assuming a Company model exists for CompId
    id = models.IntegerField(primary_key=True, db_column='CompId')
    name = models.CharField(max_length=255, db_column='CompanyName')

    class Meta:
        managed = False
        db_table = 'tblCompany'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name

class FinancialYear(models.Model):
    # Assuming a Financial Year model exists for FinYearId
    id = models.IntegerField(primary_key=True, db_column='FinYearId')
    year_name = models.CharField(max_length=50, db_column='FinYearName')

    class Meta:
        managed = False
        db_table = 'tblFinancialYear'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.year_name

class Employee(models.Model):
    emp_id = models.CharField(max_length=50, db_column='EmpId', primary_key=True)
    employee_name = models.CharField(max_length=255, db_column='EmployeeName')
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='employees')

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return self.employee_name

class Customer(models.Model):
    customer_id = models.CharField(max_length=50, db_column='CustomerId', primary_key=True)
    customer_name = models.CharField(max_length=255, db_column='CustomerName')
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='customers')

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customer_name

class Supplier(models.Model):
    supplier_id = models.CharField(max_length=50, db_column='SupplierId', primary_key=True)
    supplier_name = models.CharField(max_length=255, db_column='SupplierName')
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='suppliers')

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.supplier_name

class BusinessGroup(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    symbol = models.CharField(max_length=50, db_column='Symbol')

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

class AccountHead(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    symbol = models.CharField(max_length=50, db_column='Symbol')
    description = models.CharField(max_length=255, db_column='Description')
    category = models.CharField(max_length=50, db_column='Category') # e.g., 'Labour', 'With Material', 'Expenses', 'Service Provider'

    class Meta:
        managed = False
        db_table = 'AccHead'
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'

    def __str__(self):
        return f"[{self.symbol}] {self.description}"

class BudgetCode(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    description = models.CharField(max_length=255, db_column='Description')
    symbol = models.CharField(max_length=50, db_column='Symbol')

    class Meta:
        managed = False
        db_table = 'tblMIS_BudgetCode'
        verbose_name = 'Budget Code'
        verbose_name_plural = 'Budget Codes'

    def __str__(self):
        return f"{self.description}[{self.symbol}]"
    
    @classmethod
    def get_total_balance_for_wo(cls, budget_code_id, company_id, fin_year_id, wo_no, type_id):
        # Placeholder for calbalbud.TotBalBudget_WONO
        # This would involve complex SQL queries to sum actuals vs budget.
        # For demonstration, return a mock value.
        return 1000000.00 # Example: Sufficient balance

    @classmethod
    def get_total_balance_for_bg(cls, group_id, company_id, fin_year_id, type_id):
        # Placeholder for calbalbud.TotBalBudget_BG
        return 500000.00 # Example: Sufficient balance

class CashAmountLimit(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    amount = models.DecimalField(max_digits=18, decimal_places=3, db_column='Amount')
    active = models.BooleanField(db_column='Active')

    class Meta:
        managed = False
        db_table = 'tblACC_CashAmtLimit'
        verbose_name = 'Cash Amount Limit'
        verbose_name_plural = 'Cash Amount Limits'

    def __str__(self):
        return f"Limit: {self.amount}"

    @classmethod
    def get_active_limit(cls):
        try:
            return cls.objects.get(active=True).amount
        except cls.DoesNotExist:
            return 0.00 # Or raise an error

class BillBookingMaster(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId')
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId')
    pvev_no = models.CharField(max_length=100, db_column='PVEVNo')

    class Meta:
        managed = False
        db_table = 'tblACC_BillBooking_Master'
        verbose_name = 'Bill Booking Master'
        verbose_name_plural = 'Bill Booking Masters'

    def __str__(self):
        return self.pvev_no

    @classmethod
    def check_pvev_no_exists(cls, pvev_no, company_id, fin_year_id):
        return cls.objects.filter(pvev_no=pvev_no, company_id=company_id, financial_year_id=fin_year_id).exists()

# --- Core Cash Voucher Models ---

class CashVoucherPaymentTemp(models.Model):
    # This model stores temporary payment details for the current user's session.
    # It's akin to a shopping cart for the payment transaction.
    id = models.IntegerField(primary_key=True, db_column='Id') # Assuming Id is auto-incremented in DB
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId')
    user = models.ForeignKey(User, on_delete=models.CASCADE, db_column='SessionId') # SessionId maps to User ID
    bill_no = models.CharField(max_length=100, db_column='BillNo')
    bill_date = models.DateField(db_column='BillDate') # Stored as DateField in Django
    po_no = models.CharField(max_length=100, db_column='PONo', blank=True, null=True)
    po_date = models.DateField(db_column='PODate', blank=True, null=True)
    particulars = models.TextField(db_column='Particulars')
    wo_no = models.CharField(max_length=100, db_column='WONo', blank=True, null=True)
    business_group = models.ForeignKey(BusinessGroup, on_delete=models.DO_NOTHING, db_column='BGGroup')
    account_head = models.ForeignKey(AccountHead, on_delete=models.DO_NOTHING, db_column='AcHead')
    amount = models.DecimalField(max_digits=18, decimal_places=3, db_column='Amount')
    budget_code = models.ForeignKey(BudgetCode, on_delete=models.DO_NOTHING, db_column='BudgetCode', blank=True, null=True)
    pvev_no = models.CharField(max_length=100, db_column='PVEVNo', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_CashVoucher_Payment_Temp'
        verbose_name = 'Cash Voucher Payment Temporary'
        verbose_name_plural = 'Cash Voucher Payment Temporary Entries'

    def __str__(self):
        return f"Temp Payment: {self.bill_no} - {self.amount}"

    @classmethod
    def get_session_total_amount(cls, user_id, company_id):
        # Calculates sum of amounts for the current session and company
        return cls.objects.filter(user_id=user_id, company_id=company_id).aggregate(total=models.Sum('amount'))['total'] or 0.00

    @classmethod
    def check_valid_wo_no(cls, wo_no, company_id, fin_year_id):
        # Placeholder for clsFunctions.CheckValidWONo
        # This would query another table to validate if WO_No exists.
        return True # For demonstration, assume valid

class CashVoucherPaymentMaster(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    session_id = models.CharField(max_length=255, db_column='SessionId') # Original string session ID
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId')
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId')
    cvp_no = models.CharField(max_length=100, db_column='CVPNo')
    paid_to = models.CharField(max_length=255, db_column='PaidTo')
    received_by_code = models.CharField(max_length=50, db_column='ReceivedBy')
    code_type = models.IntegerField(db_column='CodeType') # 1=Employee, 2=Customer, 3=Supplier

    class Meta:
        managed = False
        db_table = 'tblACC_CashVoucher_Payment_Master'
        verbose_name = 'Cash Voucher Payment Master'
        verbose_name_plural = 'Cash Voucher Payment Masters'

    def __str__(self):
        return self.cvp_no

    @property
    def received_by_name(self):
        return get_name_by_code_and_type(self.received_by_code, self.code_type, self.company_id)
    
    @classmethod
    def generate_next_cvp_no(cls, company_id, fin_year_id):
        last_cvp = cls.objects.filter(company_id=company_id, financial_year_id=fin_year_id).order_by('-id').first()
        if last_cvp:
            try:
                next_num = int(last_cvp.cvp_no) + 1
            except ValueError:
                next_num = 1 # Fallback if CVPNo is not purely numeric
        else:
            next_num = 1
        return f"{next_num:04d}"

    @classmethod
    def process_payment(cls, user, company, financial_year, paid_to, received_by_text, code_type):
        received_by_code = get_code_from_name_id_string(received_by_text)
        
        # Check if ReceivedBy code is valid for its type and company
        if not get_name_by_code_and_type(received_by_code, code_type, company.id) != f"N/A ({received_by_code})":
             raise ValueError("Invalid 'Received By' selection.")

        temp_payments = CashVoucherPaymentTemp.objects.filter(user=user, company=company)
        if not temp_payments.exists():
            raise ValueError("No payment details added. Please add items before proceeding.")

        next_cvp_no = cls.generate_next_cvp_no(company.id, financial_year.id)
        
        # Create master record
        master_record = cls.objects.create(
            sys_date=timezone.localdate(),
            sys_time=timezone.localtime().time(),
            session_id=user.username, # Using username as session ID for simplicity
            company=company,
            financial_year=financial_year,
            cvp_no=next_cvp_no,
            paid_to=paid_to,
            received_by_code=received_by_code,
            code_type=code_type
        )

        # Transfer details
        for temp_item in temp_payments:
            CashVoucherPaymentDetail.objects.create(
                master_voucher=master_record,
                bill_no=temp_item.bill_no,
                bill_date=temp_item.bill_date,
                po_no=temp_item.po_no,
                po_date=temp_item.po_date,
                particulars=temp_item.particulars,
                wo_no=temp_item.wo_no,
                business_group=temp_item.business_group,
                account_head=temp_item.account_head,
                amount=temp_item.amount,
                budget_code=temp_item.budget_code,
                pvev_no=temp_item.pvev_no
            )
        
        # Clear temporary items
        temp_payments.delete()
        return master_record

class CashVoucherPaymentDetail(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id') # Assuming Id is auto-incremented in DB
    master_voucher = models.ForeignKey(CashVoucherPaymentMaster, on_delete=models.CASCADE, db_column='MId', related_name='details')
    bill_no = models.CharField(max_length=100, db_column='BillNo')
    bill_date = models.DateField(db_column='BillDate')
    po_no = models.CharField(max_length=100, db_column='PONo', blank=True, null=True)
    po_date = models.DateField(db_column='PODate', blank=True, null=True)
    particulars = models.TextField(db_column='Particulars')
    wo_no = models.CharField(max_length=100, db_column='WONo', blank=True, null=True)
    business_group = models.ForeignKey(BusinessGroup, on_delete=models.DO_NOTHING, db_column='BGGroup')
    account_head = models.ForeignKey(AccountHead, on_delete=models.DO_NOTHING, db_column='AcHead')
    amount = models.DecimalField(max_digits=18, decimal_places=3, db_column='Amount')
    budget_code = models.ForeignKey(BudgetCode, on_delete=models.DO_NOTHING, db_column='BudgetCode', blank=True, null=True)
    pvev_no = models.CharField(max_length=100, db_column='PVEVNo', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_CashVoucher_Payment_Details'
        verbose_name = 'Cash Voucher Payment Detail'
        verbose_name_plural = 'Cash Voucher Payment Details'

    def __str__(self):
        return f"Detail for {self.master_voucher.cvp_no}: {self.bill_no}"


class CashVoucherReceiptMaster(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    session_id = models.CharField(max_length=255, db_column='SessionId') # Original string session ID
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId')
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId')
    cvr_no = models.CharField(max_length=100, db_column='CVRNo')
    cash_received_against_code = models.CharField(max_length=50, db_column='CashReceivedAgainst')
    cash_received_by_code = models.CharField(max_length=50, db_column='CashReceivedBy')
    wo_no = models.CharField(max_length=100, db_column='WONo', blank=True, null=True)
    business_group = models.ForeignKey(BusinessGroup, on_delete=models.DO_NOTHING, db_column='BGGroup')
    account_head = models.ForeignKey(AccountHead, on_delete=models.DO_NOTHING, db_column='AcHead')
    amount = models.DecimalField(max_digits=18, decimal_places=3, db_column='Amount')
    others = models.TextField(db_column='Others')
    code_type_ra = models.IntegerField(db_column='CodeTypeRA') # Type for Received Against
    code_type_rb = models.IntegerField(db_column='CodeTypeRB') # Type for Received By
    budget_code = models.ForeignKey(BudgetCode, on_delete=models.DO_NOTHING, db_column='BudgetCode', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_CashVoucher_Receipt_Master'
        verbose_name = 'Cash Voucher Receipt Master'
        verbose_name_plural = 'Cash Voucher Receipt Masters'

    def __str__(self):
        return self.cvr_no

    @property
    def cash_received_against_name(self):
        return get_name_by_code_and_type(self.cash_received_against_code, self.code_type_ra, self.company_id)

    @property
    def cash_received_by_name(self):
        return get_name_by_code_and_type(self.cash_received_by_code, self.code_type_rb, self.company_id)

    @classmethod
    def generate_next_cvr_no(cls, company_id, fin_year_id):
        last_cvr = cls.objects.filter(company_id=company_id, financial_year_id=fin_year_id).order_by('-id').first()
        if last_cvr:
            try:
                next_num = int(last_cvr.cvr_no) + 1
            except ValueError:
                next_num = 1 # Fallback
        else:
            next_num = 1
        return f"{next_num:04d}"

    @classmethod
    def process_receipt(cls, user, company, financial_year, amount, cash_rec_against_text, code_type_ra, cash_rec_by_text, code_type_rb, wo_no, bg_group, ac_head, others, budget_code):
        cash_rec_against_code = get_code_from_name_id_string(cash_rec_against_text)
        cash_rec_by_code = get_code_from_name_id_string(cash_rec_by_text)

        # Validate codes
        if not get_name_by_code_and_type(cash_rec_against_code, code_type_ra, company.id) != f"N/A ({cash_rec_against_code})":
            raise ValueError("Invalid 'Cash Received Against' selection.")
        if not get_name_by_code_and_type(cash_rec_by_code, code_type_rb, company.id) != f"N/A ({cash_rec_by_code})":
            raise ValueError("Invalid 'Cash Received By' selection.")
        if wo_no and not CashVoucherPaymentTemp.check_valid_wo_no(wo_no, company.id, financial_year.id):
            raise ValueError("Entered WO No is not valid.")

        next_cvr_no = cls.generate_next_cvr_no(company.id, financial_year.id)

        receipt_record = cls.objects.create(
            sys_date=timezone.localdate(),
            sys_time=timezone.localtime().time(),
            session_id=user.username,
            company=company,
            financial_year=financial_year,
            cvr_no=next_cvr_no,
            cash_received_against_code=cash_rec_against_code,
            cash_received_by_code=cash_rec_by_code,
            wo_no=wo_no,
            business_group=bg_group,
            account_head=ac_head,
            amount=amount,
            others=others,
            code_type_ra=code_type_ra,
            code_type_rb=code_type_rb,
            budget_code=budget_code
        )
        return receipt_record
```

#### 4.2 Forms (`accounts/forms.py`)

```python
from django import forms
from .models import (
    CashVoucherPaymentTemp, CashVoucherPaymentMaster, CashVoucherReceiptMaster,
    BusinessGroup, AccountHead, BudgetCode, CashAmountLimit, BillBookingMaster,
    Employee, Customer, Supplier
)

class CashVoucherPaymentDetailForm(forms.ModelForm):
    # Radio button choices
    WO_GROUP_CHOICES = [
        ('0', 'WO No'),
        ('1', 'BG Group'),
    ]
    AC_HEAD_CHOICES = [
        ('0', 'Labour'),
        ('1', 'With Material'),
        ('2', 'Expenses'),
        ('3', 'Ser. Provider'),
    ]

    # Form fields not directly mapped to model fields but used for UI logic
    wo_group_selection = forms.ChoiceField(
        choices=WO_GROUP_CHOICES,
        widget=forms.RadioSelect(attrs={'class': 'form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out inline-flex'}),
        initial='0',
        required=False
    )
    ac_head_selection = forms.ChoiceField(
        choices=AC_HEAD_CHOICES,
        widget=forms.RadioSelect(attrs={'class': 'form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out inline-flex'}),
        initial='0',
        required=False
    )

    class Meta:
        model = CashVoucherPaymentTemp
        fields = [
            'bill_no', 'bill_date', 'po_no', 'po_date', 'amount',
            'wo_no', 'business_group', 'budget_code', 'particulars', 'account_head', 'pvev_no'
        ]
        widgets = {
            'bill_no': forms.TextInput(attrs={'class': 'box3'}),
            'bill_date': forms.DateInput(attrs={'type': 'date', 'class': 'box3'}),
            'po_no': forms.TextInput(attrs={'class': 'box3'}),
            'po_date': forms.DateInput(attrs={'type': 'date', 'class': 'box3'}),
            'amount': forms.NumberInput(attrs={'class': 'box3', 'step': '0.01'}),
            'wo_no': forms.TextInput(attrs={'class': 'box3'}),
            'business_group': forms.Select(attrs={'class': 'box3'}), # Will be hidden/shown via HTMX
            'budget_code': forms.Select(attrs={'class': 'box3'}), # Will be hidden/shown via HTMX
            'particulars': forms.Textarea(attrs={'class': 'box3', 'rows': 3}),
            'account_head': forms.Select(attrs={'class': 'box3'}),
            'pvev_no': forms.TextInput(attrs={'class': 'box3'}),
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        self.company = kwargs.pop('company', None)
        self.financial_year = kwargs.pop('financial_year', None)
        super().__init__(*args, **kwargs)

        # Populate dynamic dropdowns
        self.fields['business_group'].queryset = BusinessGroup.objects.all()
        self.fields['account_head'].queryset = AccountHead.objects.filter(category='Labour') # Default
        self.fields['budget_code'].queryset = BudgetCode.objects.all()
        self.fields['budget_code'].choices = [('', 'Select')] + list(self.fields['budget_code'].choices) # Add 'Select' option

        # Initial visibility settings for WO/Group and BudgetCode
        if self.data.get('wo_group_selection', '0') == '0': # WO No selected
            self.fields['business_group'].widget = forms.HiddenInput()
        else: # BG Group selected
            self.fields['wo_no'].widget = forms.HiddenInput()
            self.fields['budget_code'].widget = forms.HiddenInput()


    def clean(self):
        cleaned_data = super().clean()

        bill_no = cleaned_data.get('bill_no')
        bill_date = cleaned_data.get('bill_date')
        particulars = cleaned_data.get('particulars')
        amount = cleaned_data.get('amount')
        wo_group_selection = cleaned_data.get('wo_group_selection')
        wo_no = cleaned_data.get('wo_no')
        business_group = cleaned_data.get('business_group')
        budget_code = cleaned_data.get('budget_code')
        pvev_no = cleaned_data.get('pvev_no')

        # Basic Required field validation (ASP.NET RequiredFieldValidator)
        if not bill_no:
            self.add_error('bill_no', 'Bill No. is required.')
        if not bill_date:
            self.add_error('bill_date', 'Bill Date is required.')
        if not particulars:
            self.add_error('particulars', 'Particulars is required.')
        if not amount:
            self.add_error('amount', 'Amount is required.')
        elif amount <= 0:
            self.add_error('amount', 'Amount must be greater than zero.')

        # PVEVNo check (ASP.NET logic: if not empty, check existence)
        if pvev_no and not BillBookingMaster.check_pvev_no_exists(pvev_no, self.company.id, self.financial_year.id):
            self.add_error('pvev_no', 'Enter correct PVEV No.!')
        
        # WO No / BG Group logic
        if wo_group_selection == '0': # WO No selected
            if not wo_no:
                self.add_error('wo_no', 'WO No is required.')
            elif not CashVoucherPaymentTemp.check_valid_wo_no(wo_no, self.company.id, self.financial_year.id):
                self.add_error('wo_no', 'Entered WO No is not valid!')
            if not budget_code:
                self.add_error('budget_code', 'Please select Budget Code!')
            
            # Budget Amount check for WO
            current_temp_total = CashVoucherPaymentTemp.get_session_total_amount(self.user.id, self.company.id)
            if self.instance.pk: # If editing, subtract old amount
                current_temp_total -= self.instance.amount
            
            total_after_add = current_temp_total + (amount if amount else 0)
            
            balanced_budget_amount = BudgetCode.get_total_balance_for_wo(budget_code.id, self.company.id, self.financial_year.id, wo_no, 1) # '1' is type in ASP.NET
            if total_after_add > balanced_budget_amount:
                self.add_error('amount', 'Amt exceeds the balanced budget Amt!')

        else: # BG Group selected
            if not business_group:
                self.add_error('business_group', 'Business Group is required.')
            
            # Budget Amount check for BG Group
            current_temp_total = CashVoucherPaymentTemp.get_session_total_amount(self.user.id, self.company.id)
            if self.instance.pk:
                current_temp_total -= self.instance.amount

            total_after_add = current_temp_total + (amount if amount else 0)

            balanced_budget_amount = BudgetCode.get_total_balance_for_bg(business_group.id, self.company.id, self.financial_year.id, 1) # '1' is type
            if total_after_add > balanced_budget_amount:
                self.add_error('amount', 'Amt exceeds the balanced budget Amt!')
        
        # Cash Limit check
        cash_limit = CashAmountLimit.get_active_limit()
        if total_after_add > cash_limit:
            self.add_error('amount', 'Cash voucher Amt exceeds The Cash Limit!')

        # If WO No / BG Group is chosen, ensure the correct field is set for model saving
        if wo_group_selection == '0':
            cleaned_data['business_group'] = None # Ensure BGGroup is null/default if WO is chosen
        else:
            cleaned_data['wo_no'] = None # Ensure WO_No is null/default if BGGroup is chosen
            cleaned_data['budget_code'] = None # BudgetCode is not used with BGGroup
        
        return cleaned_data
    
    def set_account_head_queryset(self, category):
        self.fields['account_head'].queryset = AccountHead.objects.filter(category=category)


class CashVoucherPaymentMasterForm(forms.Form):
    # This form is for the final "Proceed" button on Payment tab
    PAID_TO_CHOICES = [
        ('0', 'Select'),
        ('1', 'Employee'),
        ('2', 'Customer'),
        ('3', 'Supplier'),
    ]

    paid_to = forms.CharField(
        max_length=255,
        widget=forms.TextInput(attrs={'class': 'box3'}),
        required=True
    )
    code_type = forms.ChoiceField(
        choices=PAID_TO_CHOICES,
        widget=forms.Select(attrs={'class': 'box3', 'hx-get': '/accounts/autocomplete-person-type/', 'hx-target': '#txtNewCustomerName', 'hx-swap': 'outerHTML'}), # HTMX to update autocomplete visibility
        required=True
    )
    received_by_text = forms.CharField(
        max_length=255,
        widget=forms.TextInput(attrs={'class': 'box3', 'hx-get': '/accounts/autocomplete-person/', 'hx-trigger': 'keyup changed delay:500ms', 'hx-indicator': '.htmx-indicator', 'autocomplete': 'off'}),
        required=True
    )
    # Hidden field to pass the actual ID if needed, or rely on parsing received_by_text

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        self.company = kwargs.pop('company', None)
        self.financial_year = kwargs.pop('financial_year', None)
        super().__init__(*args, **kwargs)

    def clean(self):
        cleaned_data = super().clean()
        paid_to = cleaned_data.get('paid_to')
        code_type = cleaned_data.get('code_type')
        received_by_text = cleaned_data.get('received_by_text')

        if code_type == '0':
            self.add_error('code_type', 'Please select a type for "Received By".')
        if not paid_to:
            self.add_error('paid_to', 'Paid To is required.')
        if not received_by_text:
            self.add_error('received_by_text', 'Received By is required.')
        
        # Check if the temporary table has entries for this session
        if not CashVoucherPaymentTemp.objects.filter(user=self.user, company=self.company).exists():
            raise forms.ValidationError("No payment details added. Please add items before proceeding.")

        return cleaned_data


class CashVoucherReceiptMasterForm(forms.ModelForm):
    # Radio button choices
    WO_GROUP_CHOICES = [
        ('0', 'WO No'),
        ('1', 'Group'),
    ]
    AC_HEAD_CHOICES = [
        ('0', 'Labour'),
        ('1', 'With Material'),
        ('2', 'Expenses'),
        ('3', 'Ser. Provider'),
    ]

    # Form fields for UI control
    wo_group_selection = forms.ChoiceField(
        choices=WO_GROUP_CHOICES,
        widget=forms.RadioSelect(attrs={'class': 'form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out inline-flex'}),
        initial='0',
        required=False
    )
    ac_head_selection = forms.ChoiceField(
        choices=AC_HEAD_CHOICES,
        widget=forms.RadioSelect(attrs={'class': 'form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out inline-flex'}),
        initial='0',
        required=False
    )

    cash_received_against_type = forms.ChoiceField(
        choices=CashVoucherPaymentMasterForm.PAID_TO_CHOICES, # Re-use choices
        widget=forms.Select(attrs={'class': 'box3', 'hx-get': '/accounts/autocomplete-person-type-ra/', 'hx-target': '#txtNewCustomerNameRA', 'hx-swap': 'outerHTML'}),
        required=True
    )
    cash_received_against_text = forms.CharField(
        max_length=255,
        widget=forms.TextInput(attrs={'class': 'box3', 'hx-get': '/accounts/autocomplete-person-ra/', 'hx-trigger': 'keyup changed delay:500ms', 'hx-indicator': '.htmx-indicator', 'autocomplete': 'off'}),
        required=True
    )

    cash_received_by_type = forms.ChoiceField(
        choices=CashVoucherPaymentMasterForm.PAID_TO_CHOICES, # Re-use choices
        widget=forms.Select(attrs={'class': 'box3', 'hx-get': '/accounts/autocomplete-person-type-rb/', 'hx-target': '#txtNewCustomerNameRB', 'hx-swap': 'outerHTML'}),
        required=True
    )
    cash_received_by_text = forms.CharField(
        max_length=255,
        widget=forms.TextInput(attrs={'class': 'box3', 'hx-get': '/accounts/autocomplete-person-rb/', 'hx-trigger': 'keyup changed delay:500ms', 'hx-indicator': '.htmx-indicator', 'autocomplete': 'off'}),
        required=True
    )

    class Meta:
        model = CashVoucherReceiptMaster
        fields = [
            'amount', 'wo_no', 'business_group', 'budget_code', 'account_head', 'others'
        ]
        widgets = {
            'amount': forms.NumberInput(attrs={'class': 'box3', 'step': '0.01'}),
            'wo_no': forms.TextInput(attrs={'class': 'box3'}),
            'business_group': forms.Select(attrs={'class': 'box3'}),
            'budget_code': forms.Select(attrs={'class': 'box3'}),
            'account_head': forms.Select(attrs={'class': 'box3'}),
            'others': forms.Textarea(attrs={'class': 'box3', 'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        self.company = kwargs.pop('company', None)
        self.financial_year = kwargs.pop('financial_year', None)
        super().__init__(*args, **kwargs)

        self.fields['business_group'].queryset = BusinessGroup.objects.all()
        self.fields['account_head'].queryset = AccountHead.objects.filter(category='Labour') # Default
        self.fields['budget_code'].queryset = BudgetCode.objects.all()
        self.fields['budget_code'].choices = [('', 'Select')] + list(self.fields['budget_code'].choices)

        # Initial visibility settings for WO/Group and BudgetCode
        if self.data.get('wo_group_selection', '0') == '0': # WO No selected
            self.fields['business_group'].widget = forms.HiddenInput()
        else: # BG Group selected
            self.fields['wo_no'].widget = forms.HiddenInput()
            self.fields['budget_code'].widget = forms.HiddenInput()

    def clean(self):
        cleaned_data = super().clean()
        amount = cleaned_data.get('amount')
        others = cleaned_data.get('others')
        cash_received_against_type = cleaned_data.get('cash_received_against_type')
        cash_received_against_text = cleaned_data.get('cash_received_against_text')
        cash_received_by_type = cleaned_data.get('cash_received_by_type')
        cash_received_by_text = cleaned_data.get('cash_received_by_text')
        wo_group_selection = cleaned_data.get('wo_group_selection')
        wo_no = cleaned_data.get('wo_no')

        # Basic Required field validation
        if not amount:
            self.add_error('amount', 'Amount is required.')
        elif amount <= 0:
            self.add_error('amount', 'Amount must be greater than zero.')
        if not others:
            self.add_error('others', 'Others field is required.')
        if cash_received_against_type == '0':
            self.add_error('cash_received_against_type', 'Please select a type for "Cash Received Against".')
        if not cash_received_against_text:
            self.add_error('cash_received_against_text', 'Cash Received Against is required.')
        if cash_received_by_type == '0':
            self.add_error('cash_received_by_type', 'Please select a type for "Cash Received By".')
        if not cash_received_by_text:
            self.add_error('cash_received_by_text', 'Cash Received By is required.')

        # WO No / Group logic
        if wo_group_selection == '0': # WO No selected
            if not wo_no:
                self.add_error('wo_no', 'WO No is required.')
            elif not CashVoucherPaymentTemp.check_valid_wo_no(wo_no, self.company.id, self.financial_year.id):
                self.add_error('wo_no', 'Entered WO No is not valid!')
            
            # If WO No chosen, ensure Business Group is not set and Budget Code is valid
            cleaned_data['business_group'] = None
            if not cleaned_data.get('budget_code'):
                 self.add_error('budget_code', 'Budget Code is required when WO No is selected.')
        else: # BG Group selected
            if not cleaned_data.get('business_group'):
                self.add_error('business_group', 'Business Group is required.')
            # If BG Group chosen, ensure WO No and Budget Code are not set
            cleaned_data['wo_no'] = None
            cleaned_data['budget_code'] = None

        return cleaned_data

    def set_account_head_queryset(self, category):
        self.fields['account_head'].queryset = AccountHead.objects.filter(category=category)
```

#### 4.3 Views (`accounts/views.py`)

```python
from django.views.generic import TemplateView, ListView, View
from django.views.generic.edit import FormMixin, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render, redirect, get_object_or_404
from django.db.models import Sum
from django.contrib.auth.mixins import LoginRequiredMixin # Ensure user is logged in
from django.utils import timezone

from .models import (
    CashVoucherPaymentTemp, CashVoucherPaymentMaster, CashVoucherReceiptMaster,
    Employee, Customer, Supplier, AccountHead, BusinessGroup, BudgetCode,
    Company, FinancialYear
)
from .forms import (
    CashVoucherPaymentDetailForm, CashVoucherPaymentMasterForm,
    CashVoucherReceiptMasterForm
)

# Placeholder for current user's company and financial year
# In a real app, these would come from user profile, session, or URL kwargs
def get_user_context(request):
    user_company = Company.objects.first() # Assume first company for demo
    user_fin_year = FinancialYear.objects.first() # Assume first fin year for demo
    if not user_company or not user_fin_year:
        # Handle case where company/fin year are not configured
        # Or raise an error
        pass
    return user_company, user_fin_year

class CashVoucherNewView(LoginRequiredMixin, TemplateView):
    template_name = 'accounts/cashvoucher_new.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        company, fin_year = get_user_context(self.request)

        # Initialize forms for both tabs
        context['payment_detail_form'] = CashVoucherPaymentDetailForm(user=self.request.user, company=company, financial_year=fin_year)
        context['payment_master_form'] = CashVoucherPaymentMasterForm(user=self.request.user, company=company, financial_year=fin_year)
        context['receipt_master_form'] = CashVoucherReceiptMasterForm(user=self.request.user, company=company, financial_year=fin_year)

        # Initial data for payment/receipt lists
        context['payment_temp_items'] = CashVoucherPaymentTemp.objects.filter(
            user=self.request.user, company=company
        ).order_by('-id')
        context['receipt_master_items'] = CashVoucherReceiptMaster.objects.filter(
            company=company, financial_year=fin_year
        ).order_by('-id')
        
        # Initial states for dynamic fields (e.g., WO/BG visibility)
        context['initial_payment_wo_group_selection'] = '0'
        context['initial_payment_ac_head_selection'] = '0'
        context['initial_receipt_wo_group_selection'] = '0'
        context['initial_receipt_ac_head_selection'] = '0'

        return context

class CashVoucherPaymentDetailAddView(LoginRequiredMixin, View):
    # Handles adding/updating a single payment detail to the temporary table
    def get(self, request, pk=None):
        company, fin_year = get_user_context(request)
        if pk:
            obj = get_object_or_404(CashVoucherPaymentTemp, pk=pk, user=request.user, company=company)
            form = CashVoucherPaymentDetailForm(instance=obj, user=request.user, company=company, financial_year=fin_year)
        else:
            form = CashVoucherPaymentDetailForm(user=request.user, company=company, financial_year=fin_year)
        
        # Set initial radio button state for display if editing
        if obj and obj.wo_no:
            form.fields['wo_group_selection'].initial = '0'
        elif obj and obj.business_group:
            form.fields['wo_group_selection'].initial = '1'

        return render(request, 'accounts/_payment_detail_form.html', {'form': form})

    def post(self, request, pk=None):
        company, fin_year = get_user_context(request)
        if pk:
            obj = get_object_or_404(CashVoucherPaymentTemp, pk=pk, user=request.user, company=company)
            form = CashVoucherPaymentDetailForm(request.POST, instance=obj, user=request.user, company=company, financial_year=fin_year)
        else:
            form = CashVoucherPaymentDetailForm(request.POST, user=request.user, company=company, financial_year=fin_year)

        if form.is_valid():
            payment_detail = form.save(commit=False)
            payment_detail.user = request.user
            payment_detail.company = company
            
            # Apply WO/BG specific logic after validation
            if form.cleaned_data['wo_group_selection'] == '0': # WO No selected
                payment_detail.wo_no = form.cleaned_data['wo_no']
                payment_detail.business_group = None
                payment_detail.budget_code = form.cleaned_data['budget_code']
            else: # BG Group selected
                payment_detail.wo_no = None
                payment_detail.business_group = form.cleaned_data['business_group']
                payment_detail.budget_code = None

            payment_detail.save()
            messages.success(request, 'Payment detail added/updated successfully.')
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshPaymentDetailList'})
        
        # If form is invalid, render the form partial with errors
        return render(request, 'accounts/_payment_detail_form.html', {'form': form})


class CashVoucherPaymentDetailDeleteView(LoginRequiredMixin, DeleteView):
    model = CashVoucherPaymentTemp
    template_name = 'accounts/_confirm_delete.html'
    context_object_name = 'object_to_delete'

    def get_object(self, queryset=None):
        company, _ = get_user_context(self.request)
        return get_object_or_404(CashVoucherPaymentTemp, pk=self.kwargs['pk'], user=self.request.user, company=company)

    def delete(self, request, *args, **kwargs):
        self.get_object().delete()
        messages.success(request, 'Payment detail deleted successfully.')
        return HttpResponse(status=204, headers={'HX-Trigger': 'refreshPaymentDetailList'})

class CashVoucherPaymentDetailTablePartialView(LoginRequiredMixin, ListView):
    model = CashVoucherPaymentTemp
    template_name = 'accounts/_payment_detail_table.html'
    context_object_name = 'payment_temp_items'

    def get_queryset(self):
        company, _ = get_user_context(self.request)
        return CashVoucherPaymentTemp.objects.filter(
            user=self.request.user, company=company
        ).order_by('-id')

class CashVoucherPaymentMasterProceedView(LoginRequiredMixin, View):
    def post(self, request):
        company, fin_year = get_user_context(request)
        form = CashVoucherPaymentMasterForm(request.POST, user=request.user, company=company, financial_year=fin_year)

        if form.is_valid():
            try:
                CashVoucherPaymentMaster.process_payment(
                    user=request.user,
                    company=company,
                    financial_year=fin_year,
                    paid_to=form.cleaned_data['paid_to'],
                    received_by_text=form.cleaned_data['received_by_text'],
                    code_type=int(form.cleaned_data['code_type'])
                )
                messages.success(request, 'Cash Payment Voucher created successfully.')
                return HttpResponse(status=204, headers={'HX-Trigger': 'refreshPaymentDetailList'}) # Refresh the payment section
            except ValueError as e:
                messages.error(request, f"Error processing payment: {e}")
                # Re-render the main form with errors, or trigger a dialog
                return render(request, 'accounts/_payment_tab_main_form.html', {'payment_master_form': form})
        
        # If form is invalid, re-render the relevant part of the form
        return render(request, 'accounts/_payment_tab_main_form.html', {'payment_master_form': form})

# --- Receipt Tab Views ---

class CashVoucherReceiptMasterProceedView(LoginRequiredMixin, View):
    def post(self, request):
        company, fin_year = get_user_context(request)
        form = CashVoucherReceiptMasterForm(request.POST, user=request.user, company=company, financial_year=fin_year)

        if form.is_valid():
            try:
                CashVoucherReceiptMaster.process_receipt(
                    user=request.user,
                    company=company,
                    financial_year=fin_year,
                    amount=form.cleaned_data['amount'],
                    cash_rec_against_text=form.cleaned_data['cash_received_against_text'],
                    code_type_ra=int(form.cleaned_data['cash_received_against_type']),
                    cash_rec_by_text=form.cleaned_data['cash_received_by_text'],
                    code_type_rb=int(form.cleaned_data['cash_received_by_type']),
                    wo_no=form.cleaned_data['wo_no'],
                    bg_group=form.cleaned_data['business_group'],
                    ac_head=form.cleaned_data['account_head'],
                    others=form.cleaned_data['others'],
                    budget_code=form.cleaned_data['budget_code']
                )
                messages.success(request, 'Cash Receipt Voucher created successfully.')
                return HttpResponse(status=204, headers={'HX-Trigger': 'refreshReceiptMasterList'})
            except ValueError as e:
                messages.error(request, f"Error processing receipt: {e}")
                return render(request, 'accounts/_receipt_tab_main_form.html', {'receipt_master_form': form})
        
        return render(request, 'accounts/_receipt_tab_main_form.html', {'receipt_master_form': form})

class CashVoucherReceiptMasterTablePartialView(LoginRequiredMixin, ListView):
    model = CashVoucherReceiptMaster
    template_name = 'accounts/_receipt_table.html'
    context_object_name = 'receipt_master_items'

    def get_queryset(self):
        company, fin_year = get_user_context(self.request)
        return CashVoucherReceiptMaster.objects.filter(
            company=company, financial_year=fin_year
        ).order_by('-id')

# --- HTMX dynamic content for radio buttons ---
class PaymentAcHeadOptionsView(LoginRequiredMixin, View):
    def get(self, request):
        category_map = {
            '0': 'Labour',
            '1': 'With Material',
            '2': 'Expenses',
            '3': 'Service Provider',
        }
        selected_value = request.GET.get('value', '0')
        category = category_map.get(selected_value, 'Labour')
        account_heads = AccountHead.objects.filter(category=category).values('id', 'symbol', 'description')
        options_html = ""
        for head in account_heads:
            options_html += f"<option value='{head['id']}'>[{head['symbol']}] {head['description']}</option>"
        return HttpResponse(options_html)

class PaymentWONoGroupOptionsView(LoginRequiredMixin, View):
    def get(self, request):
        selection = request.GET.get('value', '0')
        context = {
            'selection': selection,
            'business_groups': BusinessGroup.objects.all(),
            'budget_codes': BudgetCode.objects.all(),
        }
        return render(request, 'accounts/_payment_wo_group_fields.html', context)

class ReceiptAcHeadOptionsView(LoginRequiredMixin, View):
    def get(self, request):
        category_map = {
            '0': 'Labour',
            '1': 'With Material',
            '2': 'Expenses',
            '3': 'Service Provider',
        }
        selected_value = request.GET.get('value', '0')
        category = category_map.get(selected_value, 'Labour')
        account_heads = AccountHead.objects.filter(category=category).values('id', 'symbol', 'description')
        options_html = ""
        for head in account_heads:
            options_html += f"<option value='{head['id']}'>[{head['symbol']}] {head['description']}</option>"
        return HttpResponse(options_html)

class ReceiptWONoGroupOptionsView(LoginRequiredMixin, View):
    def get(self, request):
        selection = request.GET.get('value', '0')
        context = {
            'selection': selection,
            'business_groups': BusinessGroup.objects.all(),
            'budget_codes': BudgetCode.objects.all(),
        }
        return render(request, 'accounts/_receipt_wo_group_fields.html', context)

# --- Autocomplete Web Methods Replacement ---
class AutocompletePersonView(LoginRequiredMixin, View):
    def get(self, request):
        prefix_text = request.GET.get('q', '')
        code_type = request.GET.get('code_type') # Passed from HTMX via hidden field
        company, _ = get_user_context(request)
        
        results = []
        if code_type == '1': # Employee
            qs = Employee.objects.filter(company=company, employee_name__icontains=prefix_text).values('emp_id', 'employee_name')[:15]
            results = [f"{item['employee_name']} [{item['emp_id']}]" for item in qs]
        elif code_type == '2': # Customer
            qs = Customer.objects.filter(company=company, customer_name__icontains=prefix_text).values('customer_id', 'customer_name')[:15]
            results = [f"{item['customer_name']} [{item['customer_id']}]" for item in qs]
        elif code_type == '3': # Supplier
            qs = Supplier.objects.filter(company=company, supplier_name__icontains=prefix_text).values('supplier_id', 'supplier_name')[:15]
            results = [f"{item['supplier_name']} [{item['supplier_id']}]" for item in qs]
        
        # Sort results (as in C#)
        results.sort()
        return JsonResponse(results, safe=False)

# Autocomplete views for Receipt tab (RA and RB)
class AutocompletePersonRAView(AutocompletePersonView):
    def get(self, request):
        request.GET = request.GET.copy()
        request.GET['code_type'] = request.GET.get('ra_code_type') # Use RA specific type
        return super().get(request)

class AutocompletePersonRBView(AutocompletePersonView):
    def get(self, request):
        request.GET = request.GET.copy()
        request.GET['code_type'] = request.GET.get('rb_code_type') # Use RB specific type
        return super().get(request)

# Views for updating autocomplete text input visibility/state based on dropdown
class AutocompletePersonTypeView(LoginRequiredMixin, View):
    def get(self, request):
        code_type_val = request.GET.get('value')
        target_id = request.GET.get('hx-target-id') # This comes from the hx-target on ddlCodeType
        
        # This view dynamically updates the autocomplete textbox's visibility
        # The actual autocomplete input will have a data-hx-params which includes the selected type
        
        # Render just the autocomplete input with appropriate visibility and params
        # This simplifies the JS logic from ASP.NET's .Visible = true/false
        context = {
            'target_id': target_id,
            'code_type_val': code_type_val,
            'is_visible': code_type_val != '0',
            'name_attr': 'received_by_text' # For Payment tab
        }
        return render(request, 'accounts/_autocomplete_input_control.html', context)

class AutocompletePersonTypeRAView(AutocompletePersonTypeView):
    def get(self, request):
        context = super().get_context_data(request)
        context['name_attr'] = 'cash_received_against_text' # For Receipt RA
        return render(request, 'accounts/_autocomplete_input_control.html', context)

class AutocompletePersonTypeRBView(AutocompletePersonTypeView):
    def get(self, request):
        context = super().get_context_data(request)
        context['name_attr'] = 'cash_received_by_text' # For Receipt RB
        return render(request, 'accounts/_autocomplete_input_control.html', context)
```

#### 4.4 Templates (`accounts/`)

**`accounts/cashvoucher_new.html` (Main Page)**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ activeTab: 'payment' }">
    <div class="bg-blue-600 text-white font-bold py-2 px-4 rounded-t mb-4">
        Cash Voucher - New
    </div>

    <div class="flex border-b border-gray-200">
        <button
            class="py-2 px-4 text-sm font-medium focus:outline-none"
            :class="{ 'border-b-2 border-blue-600 text-blue-600': activeTab === 'payment', 'text-gray-500': activeTab !== 'payment' }"
            @click="activeTab = 'payment'"
        >
            Payment
        </button>
        <button
            class="py-2 px-4 text-sm font-medium focus:outline-none"
            :class="{ 'border-b-2 border-blue-600 text-blue-600': activeTab === 'receipt', 'text-gray-500': activeTab !== 'receipt' }"
            @click="activeTab = 'receipt'"
        >
            Receipt
        </button>
    </div>

    <div class="py-4">
        <div x-show="activeTab === 'payment'"
             hx-trigger="load"
             hx-get="{% url 'cashvoucher_payment_tab' %}"
             hx-target="this"
             hx-swap="innerHTML">
            <!-- Payment tab content will load here via HTMX -->
            <div class="text-center">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2">Loading Payment Tab...</p>
            </div>
        </div>

        <div x-show="activeTab === 'receipt'"
             hx-trigger="load"
             hx-get="{% url 'cashvoucher_receipt_tab' %}"
             hx-target="this"
             hx-swap="innerHTML">
            <!-- Receipt tab content will load here via HTMX -->
            <div class="text-center">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2">Loading Receipt Tab...</p>
            </div>
        </div>
    </div>
</div>

<!-- Modal for form -->
<div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden"
     _="on click if event.target.id == 'modal' remove .is-active from me"
     :class="{ 'is-active': $el.classList.contains('is-active') }">
    <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-4xl w-full"></div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // DataTables initialization script.
    // It's important to re-initialize DataTables when new content is swapped in via HTMX.
    // HTMX provides htmx:afterSwap event for this.
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'paymentTabContent' || event.detail.target.id === 'receiptTabContent') {
            // Check if the swapped content contains a DataTable
            $(event.detail.target).find('.data-table-enabled').each(function() {
                if (!$.fn.DataTable.isDataTable(this)) {
                    $(this).DataTable({
                        "pageLength": 10,
                        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
                    });
                }
            });
        }
    });

    document.addEventListener('alpine:init', () => {
        Alpine.data('cashVoucherNew', () => ({
            activeTab: 'payment',
        }));
    });
</script>
{% endblock %}
```

**`accounts/_payment_tab.html` (Partial for Payment Tab Content)**
```html
<div id="paymentTabContent">
    <form hx-post="{% url 'cashvoucher_payment_add_detail' %}" hx-swap="none" id="paymentDetailForm" class="space-y-4 p-4 border border-gray-300 rounded-md">
        {% csrf_token %}
        <h3 class="text-lg font-medium text-gray-900 mb-4">Add Payment Detail</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
                <label for="{{ payment_detail_form.paid_to.id_for_label }}" class="block text-sm font-medium text-gray-700">Paid To</label>
                {{ payment_detail_form.paid_to }}
                {% if payment_detail_form.paid_to.errors %}<p class="text-red-500 text-xs mt-1">{{ payment_detail_form.paid_to.errors }}</p>{% endif %}
            </div>
            <div class="col-span-2">
                <label for="{{ payment_detail_form.code_type.id_for_label }}" class="block text-sm font-medium text-gray-700">Received By</label>
                <div class="flex space-x-2">
                    {{ payment_master_form.code_type }}
                    <input type="hidden" name="hx-target-id" value="txtNewCustomerName" /> {# Pass target ID for the autocomplete view #}
                    <div id="txtNewCustomerName">
                        {% if payment_master_form.code_type.value != '0' %}
                            {{ payment_master_form.received_by_text }}
                        {% else %}
                            <input type="text" name="{{ payment_master_form.received_by_text.name }}" class="box3 hidden" />
                        {% endif %}
                    </div>
                </div>
                {% if payment_master_form.code_type.errors %}<p class="text-red-500 text-xs mt-1">{{ payment_master_form.code_type.errors }}</p>{% endif %}
                {% if payment_master_form.received_by_text.errors %}<p class="text-red-500 text-xs mt-1">{{ payment_master_form.received_by_text.errors }}</p>{% endif %}
            </div>
        </div>

        <div class="p-4 border border-gray-400 rounded-md space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label for="{{ payment_detail_form.bill_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Bill No.</label>
                    {{ payment_detail_form.bill_no }}
                    {% if payment_detail_form.bill_no.errors %}<p class="text-red-500 text-xs mt-1">{{ payment_detail_form.bill_no.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ payment_detail_form.bill_date.id_for_label }}" class="block text-sm font-medium text-gray-700">Bill Date</label>
                    {{ payment_detail_form.bill_date }}
                    {% if payment_detail_form.bill_date.errors %}<p class="text-red-500 text-xs mt-1">{{ payment_detail_form.bill_date.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ payment_detail_form.po_no.id_for_label }}" class="block text-sm font-medium text-gray-700">PO No.</label>
                    {{ payment_detail_form.po_no }}
                    {% if payment_detail_form.po_no.errors %}<p class="text-red-500 text-xs mt-1">{{ payment_detail_form.po_no.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ payment_detail_form.po_date.id_for_label }}" class="block text-sm font-medium text-gray-700">PO Date</label>
                    {{ payment_detail_form.po_date }}
                    {% if payment_detail_form.po_date.errors %}<p class="text-red-500 text-xs mt-1">{{ payment_detail_form.po_date.errors }}</p>{% endif %}
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label for="{{ payment_detail_form.amount.id_for_label }}" class="block text-sm font-medium text-gray-700">Amount</label>
                    {{ payment_detail_form.amount }}
                    {% if payment_detail_form.amount.errors %}<p class="text-red-500 text-xs mt-1">{{ payment_detail_form.amount.errors }}</p>{% endif %}
                </div>
                <div hx-target="#payment_wo_group_fields" hx-swap="outerHTML">
                    <label class="block text-sm font-medium text-gray-700">WO No/BG Group</label>
                    <div class="mt-2 flex items-center space-x-4">
                        {% for radio in payment_detail_form.wo_group_selection %}
                        <label class="inline-flex items-center">
                            {{ radio.tag }}
                            <span class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</span>
                        </label>
                        {% endfor %}
                    </div>
                </div>
                <div class="col-span-2" id="payment_wo_group_fields" hx-trigger="load, click from:input[name='wo_group_selection']" hx-get="{% url 'cashvoucher_payment_wo_group_options' %}" hx-params="value: wo_group_selection:checked.value" hx-swap="outerHTML">
                    {# Dynamic content for WO No / BG Group and Budget Code will load here #}
                    {% include 'accounts/_payment_wo_group_fields.html' with selection=initial_payment_wo_group_selection business_groups=payment_detail_form.fields.business_group.queryset budget_codes=payment_detail_form.fields.budget_code.queryset %}
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="md:col-span-2">
                    <label for="{{ payment_detail_form.particulars.id_for_label }}" class="block text-sm font-medium text-gray-700">Particulars</label>
                    {{ payment_detail_form.particulars }}
                    {% if payment_detail_form.particulars.errors %}<p class="text-red-500 text-xs mt-1">{{ payment_detail_form.particulars.errors }}</p>{% endif %}
                </div>
                <div class="md:col-span-2" hx-target="#payment_ac_head_dropdown" hx-swap="outerHTML">
                    <label class="block text-sm font-medium text-gray-700">Account Head</label>
                    <div class="mt-2 flex items-center space-x-4">
                        {% for radio in payment_detail_form.ac_head_selection %}
                        <label class="inline-flex items-center">
                            {{ radio.tag }}
                            <span class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</span>
                        </label>
                        {% endfor %}
                    </div>
                    <div id="payment_ac_head_dropdown" hx-trigger="load, click from:input[name='ac_head_selection']" hx-get="{% url 'cashvoucher_payment_ac_head_options' %}" hx-params="value: ac_head_selection:checked.value" hx-swap="innerHTML">
                        {{ payment_detail_form.account_head }}
                    </div>
                    {% if payment_detail_form.account_head.errors %}<p class="text-red-500 text-xs mt-1">{{ payment_detail_form.account_head.errors }}</p>{% endif %}
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label for="{{ payment_detail_form.pvev_no.id_for_label }}" class="block text-sm font-medium text-gray-700">PVEV No</label>
                    {{ payment_detail_form.pvev_no }}
                    {% if payment_detail_form.pvev_no.errors %}<p class="text-red-500 text-xs mt-1">{{ payment_detail_form.pvev_no.errors }}</p>{% endif %}
                </div>
                <div class="col-span-3 text-right">
                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Add
                    </button>
                </div>
            </div>
        </div>
    </form>

    <div class="mt-8">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Payment Details</h3>
        <div id="paymentDetailTableContainer"
             hx-trigger="load, refreshPaymentDetailList from:body"
             hx-get="{% url 'cashvoucher_payment_detail_table' %}"
             hx-target="this"
             hx-swap="innerHTML">
            <!-- Payment detail table will be loaded here via HTMX -->
            <div class="text-center">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2">Loading Payment Details...</p>
            </div>
        </div>
    </div>

    <div class="mt-8 text-center">
        <form hx-post="{% url 'cashvoucher_payment_proceed' %}" hx-swap="none">
            {% csrf_token %}
            <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                Proceed
            </button>
        </form>
    </div>
</div>
```

**`accounts/_receipt_tab.html` (Partial for Receipt Tab Content)**
```html
<div id="receiptTabContent">
    <form hx-post="{% url 'cashvoucher_receipt_proceed' %}" hx-swap="none" id="receiptForm" class="space-y-4 p-4 border border-gray-300 rounded-md">
        {% csrf_token %}
        <h3 class="text-lg font-medium text-gray-900 mb-4">Add Receipt Detail</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
                <label for="{{ receipt_master_form.amount.id_for_label }}" class="block text-sm font-medium text-gray-700">Amount</label>
                {{ receipt_master_form.amount }}
                {% if receipt_master_form.amount.errors %}<p class="text-red-500 text-xs mt-1">{{ receipt_master_form.amount.errors }}</p>{% endif %}
            </div>
            <div class="col-span-2">
                <label for="{{ receipt_master_form.cash_received_against_type.id_for_label }}" class="block text-sm font-medium text-gray-700">Cash Received Against</label>
                <div class="flex space-x-2">
                    {{ receipt_master_form.cash_received_against_type }}
                    <input type="hidden" name="hx-target-id" value="txtNewCustomerNameRA" />
                    <input type="hidden" name="ra_code_type" :value="document.getElementById('{{ receipt_master_form.cash_received_against_type.id_for_label }}').value" /> {# Pass selected type from dropdown #}
                    <div id="txtNewCustomerNameRA">
                        {% if receipt_master_form.cash_received_against_type.value != '0' %}
                            {{ receipt_master_form.cash_received_against_text }}
                        {% else %}
                            <input type="text" name="{{ receipt_master_form.cash_received_against_text.name }}" class="box3 hidden" />
                        {% endif %}
                    </div>
                </div>
                {% if receipt_master_form.cash_received_against_type.errors %}<p class="text-red-500 text-xs mt-1">{{ receipt_master_form.cash_received_against_type.errors }}</p>{% endif %}
                {% if receipt_master_form.cash_received_against_text.errors %}<p class="text-red-500 text-xs mt-1">{{ receipt_master_form.cash_received_against_text.errors }}</p>{% endif %}
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div class="col-span-3">
                <label for="{{ receipt_master_form.cash_received_by_type.id_for_label }}" class="block text-sm font-medium text-gray-700">Cash Received By</label>
                <div class="flex space-x-2">
                    {{ receipt_master_form.cash_received_by_type }}
                    <input type="hidden" name="hx-target-id" value="txtNewCustomerNameRB" />
                    <input type="hidden" name="rb_code_type" :value="document.getElementById('{{ receipt_master_form.cash_received_by_type.id_for_label }}').value" /> {# Pass selected type from dropdown #}
                    <div id="txtNewCustomerNameRB">
                        {% if receipt_master_form.cash_received_by_type.value != '0' %}
                            {{ receipt_master_form.cash_received_by_text }}
                        {% else %}
                            <input type="text" name="{{ receipt_master_form.cash_received_by_text.name }}" class="box3 hidden" />
                        {% endif %}
                    </div>
                </div>
                {% if receipt_master_form.cash_received_by_type.errors %}<p class="text-red-500 text-xs mt-1">{{ receipt_master_form.cash_received_by_type.errors }}</p>{% endif %}
                {% if receipt_master_form.cash_received_by_text.errors %}<p class="text-red-500 text-xs mt-1">{{ receipt_master_form.cash_received_by_text.errors }}</p>{% endif %}
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div class="md:col-span-1" hx-target="#receipt_wo_group_fields" hx-swap="outerHTML">
                <label class="block text-sm font-medium text-gray-700">WO No / Group</label>
                <div class="mt-2 flex items-center space-x-4">
                    {% for radio in receipt_master_form.wo_group_selection %}
                    <label class="inline-flex items-center">
                        {{ radio.tag }}
                        <span class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</span>
                    </label>
                    {% endfor %}
                </div>
            </div>
            <div class="md:col-span-2 flex items-center space-x-4" id="receipt_wo_group_fields" hx-trigger="load, click from:input[name='wo_group_selection']" hx-get="{% url 'cashvoucher_receipt_wo_group_options' %}" hx-params="value: wo_group_selection:checked.value" hx-swap="outerHTML">
                {% include 'accounts/_receipt_wo_group_fields.html' with selection=initial_receipt_wo_group_selection business_groups=receipt_master_form.fields.business_group.queryset budget_codes=receipt_master_form.fields.budget_code.queryset %}
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div class="md:col-span-1" hx-target="#receipt_ac_head_dropdown" hx-swap="outerHTML">
                <label class="block text-sm font-medium text-gray-700">Ac Head</label>
                <div class="mt-2 flex items-center space-x-4">
                    {% for radio in receipt_master_form.ac_head_selection %}
                    <label class="inline-flex items-center">
                        {{ radio.tag }}
                        <span class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</span>
                    </label>
                    {% endfor %}
                </div>
            </div>
            <div class="md:col-span-2" id="receipt_ac_head_dropdown" hx-trigger="load, click from:input[name='ac_head_selection']" hx-get="{% url 'cashvoucher_receipt_ac_head_options' %}" hx-params="value: ac_head_selection:checked.value" hx-swap="innerHTML">
                {{ receipt_master_form.account_head }}
                {% if receipt_master_form.account_head.errors %}<p class="text-red-500 text-xs mt-1">{{ receipt_master_form.account_head.errors }}</p>{% endif %}
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div class="col-span-3">
                <label for="{{ receipt_master_form.others.id_for_label }}" class="block text-sm font-medium text-gray-700">Others</label>
                {{ receipt_master_form.others }}
                {% if receipt_master_form.others.errors %}<p class="text-red-500 text-xs mt-1">{{ receipt_master_form.others.errors }}</p>{% endif %}
            </div>
        </div>

        <div class="mt-6 text-center">
            <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                Proceed
            </button>
        </div>
    </form>

    <div class="mt-8">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Receipt History</h3>
        <div id="receiptMasterTableContainer"
             hx-trigger="load, refreshReceiptMasterList from:body"
             hx-get="{% url 'cashvoucher_receipt_master_table' %}"
             hx-target="this"
             hx-swap="innerHTML">
            <!-- Receipt master table will be loaded here via HTMX -->
            <div class="text-center">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2">Loading Receipt History...</p>
            </div>
        </div>
    </div>
</div>
```

**`accounts/_payment_detail_table.html` (Partial for Payment Details DataTable)**
```html
<table id="paymentDetailTable" class="min-w-full bg-white data-table-enabled">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bill No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bill Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Particulars</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Group</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ac Head</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Budget Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PVEV No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for item in payment_temp_items %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.bill_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.bill_date|date:"d-m-Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.po_no|default:"NA" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.po_date|date:"d-m-Y"|default:"NA" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.particulars }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.wo_no|default:"NA" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.business_group.symbol|default:"NA" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.account_head.symbol }} {{ item.account_head.description }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.amount|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.budget_code.description|default:"NA" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.pvev_no|default:"NA" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                <button
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'cashvoucher_payment_detail_delete' item.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr><td colspan="13" class="text-center py-4 text-gray-500">No payment details added yet.</td></tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // DataTable will be initialized by the main page's htmx:afterSwap listener.
    // Ensure the table has the 'data-table-enabled' class.
</script>
```

**`accounts/_receipt_table.html` (Partial for Receipt Master DataTable)**
```html
<table id="receiptMasterTable" class="min-w-full bg-white data-table-enabled">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CVR No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cash Rec. Against</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cash Rec. By</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BG Group</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ac Head</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Budget Code</th>
        </tr>
    </thead>
    <tbody>
        {% for item in receipt_master_items %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.sys_date|date:"d-m-Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.cvr_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.cash_received_against_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.cash_received_by_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.wo_no|default:"NA" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.business_group.symbol|default:"NA" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.account_head.symbol }} {{ item.account_head.description }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.amount|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.budget_code.description|default:"NA" }}</td>
        </tr>
        {% empty %}
        <tr><td colspan="10" class="text-center py-4 text-gray-500">No receipt entries found.</td></tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // DataTable will be initialized by the main page's htmx:afterSwap listener.
</script>
```

**`accounts/_payment_wo_group_fields.html` (Partial for Payment WO/Group Fields)**
```html
{# This partial is loaded dynamically when radio button changes #}
{# selection: '0' for WO No, '1' for BG Group #}
{% if selection == '0' %}
    <div>
        <label for="id_wo_no" class="block text-sm font-medium text-gray-700">WO No</label>
        <input type="text" name="wo_no" id="id_wo_no" class="box3" value="{{ form.wo_no.value|default:'' }}" />
    </div>
    <div>
        <label for="id_budget_code" class="block text-sm font-medium text-gray-700">Budget Code :</label>
        <select name="budget_code" id="id_budget_code" class="box3">
            <option value="">Select</option>
            {% for code in budget_codes %}
                <option value="{{ code.id }}" {% if form.budget_code.value == code.id %}selected{% endif %}>{{ code.description }}[{{ code.symbol }}]</option>
            {% endfor %}
        </select>
    </div>
{% else %}
    <div>
        <label for="id_business_group" class="block text-sm font-medium text-gray-700">Group :</label>
        <select name="business_group" id="id_business_group" class="box3">
            {% for group in business_groups %}
                <option value="{{ group.id }}" {% if form.business_group.value == group.id %}selected{% endif %}>{{ group.symbol }}</option>
            {% endfor %}
        </select>
    </div>
{% endif %}
```

**`accounts/_receipt_wo_group_fields.html` (Partial for Receipt WO/Group Fields)**
```html
{# This partial is loaded dynamically when radio button changes #}
{# selection: '0' for WO No, '1' for Group #}
{% if selection == '0' %}
    <div>
        <label for="id_wo_no_rec" class="block text-sm font-medium text-gray-700">WO No</label>
        <input type="text" name="wo_no" id="id_wo_no_rec" class="box3" value="{{ form.wo_no.value|default:'' }}" />
    </div>
    <div>
        <label for="id_budget_code_rec" class="block text-sm font-medium text-gray-700">Budget Code :</label>
        <select name="budget_code" id="id_budget_code_rec" class="box3">
            <option value="">Select</option>
            {% for code in budget_codes %}
                <option value="{{ code.id }}" {% if form.budget_code.value == code.id %}selected{% endif %}>{{ code.description }}[{{ code.symbol }}]</option>
            {% endfor %}
        </select>
    </div>
{% else %}
    <div>
        <label for="id_business_group_rec" class="block text-sm font-medium text-gray-700">Group :</label>
        <select name="business_group" id="id_business_group_rec" class="box3">
            {% for group in business_groups %}
                <option value="{{ group.id }}" {% if form.business_group.value == group.id %}selected{% endif %}>{{ group.symbol }}</option>
            {% endfor %}
        </select>
    </div>
{% endif %}
```

**`accounts/_autocomplete_input_control.html` (Generic Autocomplete Input Partial)**
```html
{# This partial is used to toggle visibility and configure hx-params for autocomplete inputs #}
{# target_id: ID of the div containing the input (e.g., txtNewCustomerName) #}
{# code_type_val: The selected value from the ddlCodeType (e.g., '1' for Employee) #}
{# is_visible: Boolean, whether the input should be visible #}
{# name_attr: The name attribute for the input field (e.g., 'received_by_text') #}

{% comment %}
    The HTMX for autocompleting should be configured on the input field itself.
    The select field (ddlCodeType) should trigger a GET request to update this partial,
    which conditionally renders the input field with or without HTMX attributes based on `is_visible`.
{% endcomment %}

{% if is_visible %}
    <input type="text"
           name="{{ name_attr }}"
           id="{{ target_id }}_input"
           class="box3"
           hx-get="{% url 'accounts_autocomplete_person' %}" {# This needs to be dynamic based on RA/RB #}
           hx-trigger="keyup changed delay:500ms, focus"
           hx-params="q, code_type: document.getElementById('{{ target_id }}').previousElementSibling.value" {# Pass type #}
           hx-target="#autocomplete-list-{{ target_id }}"
           hx-swap="innerHTML"
           autocomplete="off"
           value="{{ request.POST.get.received_by_text|default:'' }}"> {# Preserve value on error #}
    <div id="autocomplete-list-{{ target_id }}" class="autocomplete-list border border-gray-300 bg-white shadow-lg z-10 max-h-48 overflow-y-auto"></div>
{% else %}
    <input type="text" name="{{ name_attr }}" class="box3 hidden" />
{% endif %}
```
*Note on `_autocomplete_input_control.html`*: The HTMX setup for autocomplete is slightly complex due to the `hx-params` needing to reference other form fields. A more robust solution might involve `hx-vals` or a custom HTMX extension, but the `hx-params` referring to `previousElementSibling` is a common pattern. The actual `autocomplete_person` view will need to handle the `code_type` passed via `hx-params`. I've added a specific view for each autocomplete to simplify parameter passing (`ra_code_type`, `rb_code_type`).

**`accounts/_confirm_delete.html` (Generic Delete Confirmation Partial)**
```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete this item?</p>
    
    <form hx-delete="{% url 'cashvoucher_payment_detail_delete' object_to_delete.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`accounts/urls.py`)

```python
from django.urls import path
from .views import (
    CashVoucherNewView,
    CashVoucherPaymentDetailAddView, CashVoucherPaymentDetailDeleteView, CashVoucherPaymentDetailTablePartialView,
    CashVoucherPaymentMasterProceedView,
    CashVoucherReceiptMasterProceedView, CashVoucherReceiptMasterTablePartialView,
    PaymentAcHeadOptionsView, PaymentWONoGroupOptionsView,
    ReceiptAcHeadOptionsView, ReceiptWONoGroupOptionsView,
    AutocompletePersonView, AutocompletePersonRAView, AutocompletePersonRBView,
    AutocompletePersonTypeView, AutocompletePersonTypeRAView, AutocompletePersonTypeRBView
)

urlpatterns = [
    # Main Cash Voucher Page with tabs
    path('new/', CashVoucherNewView.as_view(), name='cashvoucher_new'),

    # HTMX endpoints for main tab content
    path('new/payment-tab/', CashVoucherNewView.as_view(template_name='accounts/_payment_tab.html'), name='cashvoucher_payment_tab'),
    path('new/receipt-tab/', CashVoucherNewView.as_view(template_name='accounts/_receipt_tab.html'), name='cashvoucher_receipt_tab'),

    # Payment Detail (Temporary) CRUD operations
    path('payment-details/add/', CashVoucherPaymentDetailAddView.as_view(), name='cashvoucher_payment_add_detail'),
    path('payment-details/<int:pk>/delete/', CashVoucherPaymentDetailDeleteView.as_view(), name='cashvoucher_payment_detail_delete'),
    path('payment-details/table/', CashVoucherPaymentDetailTablePartialView.as_view(), name='cashvoucher_payment_detail_table'),

    # Final Payment Processing
    path('payment/proceed/', CashVoucherPaymentMasterProceedView.as_view(), name='cashvoucher_payment_proceed'),

    # Receipt Processing
    path('receipt/proceed/', CashVoucherReceiptMasterProceedView.as_view(), name='cashvoucher_receipt_proceed'),
    path('receipt/table/', CashVoucherReceiptMasterTablePartialView.as_view(), name='cashvoucher_receipt_master_table'),

    # HTMX Dynamic Form Field Updates
    path('payment/ac-head-options/', PaymentAcHeadOptionsView.as_view(), name='cashvoucher_payment_ac_head_options'),
    path('payment/wo-group-options/', PaymentWONoGroupOptionsView.as_view(), name='cashvoucher_payment_wo_group_options'),
    path('receipt/ac-head-options/', ReceiptAcHeadOptionsView.as_view(), name='cashvoucher_receipt_ac_head_options'),
    path('receipt/wo-group-options/', ReceiptWONoGroupOptionsView.as_view(), name='cashvoucher_receipt_wo_group_options'),

    # Autocomplete Endpoints
    path('autocomplete-person/', AutocompletePersonView.as_view(), name='accounts_autocomplete_person'),
    path('autocomplete-person-ra/', AutocompletePersonRAView.as_view(), name='accounts_autocomplete_person_ra'),
    path('autocomplete-person-rb/', AutocompletePersonRBView.as_view(), name='accounts_autocomplete_person_rb'),
    path('autocomplete-person-type/', AutocompletePersonTypeView.as_view(), name='accounts_autocomplete_person_type'),
    path('autocomplete-person-type-ra/', AutocompletePersonTypeRAView.as_view(), name='accounts_autocomplete_person_type_ra'),
    path('autocomplete-person-type-rb/', AutocompletePersonTypeRBView.as_view(), name='accounts_autocomplete_person_type_rb'),
]
```

#### 4.6 Tests (`accounts/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import date, time

from .models import (
    Company, FinancialYear, Employee, Customer, Supplier, BusinessGroup,
    AccountHead, BudgetCode, CashAmountLimit, BillBookingMaster,
    CashVoucherPaymentTemp, CashVoucherPaymentMaster, CashVoucherPaymentDetail,
    CashVoucherReceiptMaster
)

User = get_user_model()

class CashVoucherModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company = Company.objects.create(id=1, name='TestCo')
        cls.fin_year = FinancialYear.objects.create(id=1, year_name='2023-2024')
        cls.user = User.objects.create_user(username='testuser', password='password123')
        cls.employee = Employee.objects.create(emp_id='E001', employee_name='Test Employee', company=cls.company)
        cls.customer = Customer.objects.create(customer_id='C001', customer_name='Test Customer', company=cls.company)
        cls.supplier = Supplier.objects.create(supplier_id='S001', supplier_name='Test Supplier', company=cls.company)
        cls.business_group = BusinessGroup.objects.create(id=1, symbol='BG1')
        cls.account_head_labour = AccountHead.objects.create(id=1, symbol='LBR', description='Labour Cost', category='Labour')
        cls.account_head_expenses = AccountHead.objects.create(id=2, symbol='EXP', description='General Expenses', category='Expenses')
        cls.budget_code_wo = BudgetCode.objects.create(id=1, description='Project WO1', symbol='PWO1')
        cls.cash_limit = CashAmountLimit.objects.create(id=1, amount=10000.00, active=True)
        cls.bill_booking = BillBookingMaster.objects.create(id=1, company=cls.company, financial_year=cls.fin_year, pvev_no='PVEV001')

    def test_cash_voucher_payment_temp_creation(self):
        temp_item = CashVoucherPaymentTemp.objects.create(
            id=1,
            company=self.company,
            user=self.user,
            bill_no='B001',
            bill_date=date(2023, 1, 1),
            particulars='Test Payment',
            amount=100.00,
            business_group=self.business_group,
            account_head=self.account_head_labour,
            wo_no='WO123',
            budget_code=self.budget_code_wo,
            pvev_no='PVEV001'
        )
        self.assertEqual(temp_item.bill_no, 'B001')
        self.assertEqual(CashVoucherPaymentTemp.objects.count(), 1)

    def test_cash_voucher_payment_temp_get_session_total_amount(self):
        CashVoucherPaymentTemp.objects.create(id=2, company=self.company, user=self.user, bill_no='B002', bill_date=date(2023, 1, 2), particulars='Item 1', amount=50.00, business_group=self.business_group, account_head=self.account_head_labour)
        CashVoucherPaymentTemp.objects.create(id=3, company=self.company, user=self.user, bill_no='B003', bill_date=date(2023, 1, 3), particulars='Item 2', amount=75.00, business_group=self.business_group, account_head=self.account_head_labour)
        
        total = CashVoucherPaymentTemp.get_session_total_amount(self.user.id, self.company.id)
        self.assertEqual(total, 125.00)
    
    def test_cash_voucher_payment_master_process_payment(self):
        CashVoucherPaymentTemp.objects.create(id=4, company=self.company, user=self.user, bill_no='B004', bill_date=date(2023, 1, 4), particulars='Item 3', amount=200.00, business_group=self.business_group, account_head=self.account_head_labour, wo_no='WOXYZ', budget_code=self.budget_code_wo, pvev_no='PVEV001')
        
        master_voucher = CashVoucherPaymentMaster.process_payment(
            user=self.user,
            company=self.company,
            financial_year=self.fin_year,
            paid_to='Vendor A',
            received_by_text=f'Test Employee [{self.employee.emp_id}]',
            code_type=1 # Employee
        )
        self.assertIsNotNone(master_voucher)
        self.assertEqual(master_voucher.paid_to, 'Vendor A')
        self.assertEqual(master_voucher.received_by_code, self.employee.emp_id)
        self.assertEqual(CashVoucherPaymentTemp.objects.count(), 0) # Temp items should be cleared
        self.assertEqual(CashVoucherPaymentDetail.objects.filter(master_voucher=master_voucher).count(), 1)

    def test_cash_voucher_receipt_master_process_receipt(self):
        master_receipt = CashVoucherReceiptMaster.process_receipt(
            user=self.user,
            company=self.company,
            financial_year=self.fin_year,
            amount=500.00,
            cash_rec_against_text=f'Test Customer [{self.customer.customer_id}]',
            code_type_ra=2, # Customer
            cash_rec_by_text=f'Test Employee [{self.employee.emp_id}]',
            code_type_rb=1, # Employee
            wo_no='WO456',
            bg_group=self.business_group,
            ac_head=self.account_head_expenses,
            others='Received for services',
            budget_code=self.budget_code_wo
        )
        self.assertIsNotNone(master_receipt)
        self.assertEqual(master_receipt.amount, 500.00)
        self.assertEqual(master_receipt.cash_received_against_code, self.customer.customer_id)
        self.assertEqual(CashVoucherReceiptMaster.objects.count(), 1)

class CashVoucherViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company = Company.objects.create(id=1, name='TestCo')
        cls.fin_year = FinancialYear.objects.create(id=1, year_name='2023-2024')
        cls.user = User.objects.create_user(username='testuser', password='password123')
        cls.employee = Employee.objects.create(emp_id='E001', employee_name='Test Employee', company=cls.company)
        cls.customer = Customer.objects.create(customer_id='C001', customer_name='Test Customer', company=cls.company)
        cls.supplier = Supplier.objects.create(supplier_id='S001', supplier_name='Test Supplier', company=cls.company)
        cls.business_group = BusinessGroup.objects.create(id=1, symbol='BG1')
        cls.account_head_labour = AccountHead.objects.create(id=1, symbol='LBR', description='Labour Cost', category='Labour')
        cls.account_head_expenses = AccountHead.objects.create(id=2, symbol='EXP', description='General Expenses', category='Expenses')
        cls.budget_code_wo = BudgetCode.objects.create(id=1, description='Project WO1', symbol='PWO1')
        cls.cash_limit = CashAmountLimit.objects.create(id=1, amount=10000.00, active=True)
        cls.bill_booking = BillBookingMaster.objects.create(id=1, company=cls.company, financial_year=cls.fin_year, pvev_no='PVEV001')

    def setUp(self):
        self.client = Client()
        self.client.login(username='testuser', password='password123')

    def test_cashvoucher_new_view(self):
        response = self.client.get(reverse('cashvoucher_new'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/cashvoucher_new.html')
        self.assertContains(response, 'Payment')
        self.assertContains(response, 'Receipt')

    def test_cashvoucher_payment_add_detail_post_success(self):
        data = {
            'bill_no': 'TESTBILL001',
            'bill_date': '2023-01-05',
            'particulars': 'Testing payment detail add.',
            'amount': '150.00',
            'wo_group_selection': '0', # WO No
            'wo_no': 'WO456',
            'budget_code': self.budget_code_wo.id,
            'ac_head_selection': '0', # Labour
            'account_head': self.account_head_labour.id,
            'pvev_no': 'PVEV001',
        }
        response = self.client.post(reverse('cashvoucher_payment_add_detail'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success code
        self.assertEqual(CashVoucherPaymentTemp.objects.count(), 1)
        self.assertEqual(CashVoucherPaymentTemp.objects.first().bill_no, 'TESTBILL001')

    def test_cashvoucher_payment_add_detail_post_invalid(self):
        data = {
            'bill_no': '', # Missing required field
            'bill_date': '2023-01-05',
            'particulars': 'Testing invalid add.',
            'amount': '50.00',
            'wo_group_selection': '0',
            'wo_no': 'WO456',
            'budget_code': self.budget_code_wo.id,
            'ac_head_selection': '0',
            'account_head': self.account_head_labour.id,
        }
        response = self.client.post(reverse('cashvoucher_payment_add_detail'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Renders form with errors
        self.assertContains(response, 'Bill No. is required.')
        self.assertEqual(CashVoucherPaymentTemp.objects.count(), 0)

    def test_cashvoucher_payment_detail_delete_success(self):
        temp_item = CashVoucherPaymentTemp.objects.create(
            id=5, company=self.company, user=self.user, bill_no='TEMP001', bill_date=date(2023, 1, 6),
            particulars='To delete', amount=10.00, business_group=self.business_group, account_head=self.account_head_labour
        )
        response = self.client.delete(reverse('cashvoucher_payment_detail_delete', args=[temp_item.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(CashVoucherPaymentTemp.objects.count(), 0)

    def test_cashvoucher_payment_detail_table_partial(self):
        CashVoucherPaymentTemp.objects.create(id=6, company=self.company, user=self.user, bill_no='TABLEITEM', bill_date=date(2023, 1, 7), particulars='In table', amount=20.00, business_group=self.business_group, account_head=self.account_head_labour)
        response = self.client.get(reverse('cashvoucher_payment_detail_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/_payment_detail_table.html')
        self.assertContains(response, 'TABLEITEM')

    def test_cashvoucher_payment_proceed_success(self):
        CashVoucherPaymentTemp.objects.create(id=7, company=self.company, user=self.user, bill_no='FINALPAY', bill_date=date(2023, 1, 8), particulars='Final payment item', amount=300.00, business_group=self.business_group, account_head=self.account_head_labour, wo_no='WOFIN', budget_code=self.budget_code_wo, pvev_no='PVEV001')
        
        data = {
            'paid_to': 'Test Vendor',
            'code_type': '1', # Employee
            'received_by_text': f'Test Employee [{self.employee.emp_id}]',
        }
        response = self.client.post(reverse('cashvoucher_payment_proceed'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(CashVoucherPaymentTemp.objects.count(), 0) # Temp items cleared
        self.assertEqual(CashVoucherPaymentMaster.objects.count(), 1)
        self.assertEqual(CashVoucherPaymentDetail.objects.count(), 1)

    def test_cashvoucher_receipt_proceed_success(self):
        data = {
            'amount': '400.00',
            'others': 'Received for consultation.',
            'cash_received_against_type': '2', # Customer
            'cash_received_against_text': f'Test Customer [{self.customer.customer_id}]',
            'cash_received_by_type': '1', # Employee
            'cash_received_by_text': f'Test Employee [{self.employee.emp_id}]',
            'wo_group_selection': '0',
            'wo_no': 'WO987',
            'budget_code': self.budget_code_wo.id,
            'ac_head_selection': '2', # Expenses
            'account_head': self.account_head_expenses.id,
        }
        response = self.client.post(reverse('cashvoucher_receipt_proceed'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(CashVoucherReceiptMaster.objects.count(), 1)
        self.assertEqual(CashVoucherReceiptMaster.objects.first().amount, 400.00)

    def test_autocomplete_person_view(self):
        response = self.client.get(reverse('accounts_autocomplete_person'), {'q': 'test', 'code_type': '1'})
        self.assertEqual(response.status_code, 200)
        self.assertIn(f'Test Employee [{self.employee.emp_id}]', response.json())
```

### Step 5: HTMX and Alpine.js Integration

**Summary of HTMX/Alpine.js Usage:**

*   **Tab Navigation:** Alpine.js manages the `activeTab` state. HTMX `hx-get` is used on `x-show` divs to lazily load tab content (`_payment_tab.html`, `_receipt_tab.html`) only when a tab is selected or on initial page load.
*   **Payment Detail Add/Delete:**
    *   `btnPaymentAdd` (submit button): `hx-post` to `cashvoucher_payment_add_detail`, `hx-swap="none"` to prevent form re-render on success, `HX-Trigger: refreshPaymentDetailList` header from backend to re-render the list table.
    *   Delete buttons in `_payment_detail_table.html`: `hx-get` to `cashvoucher_payment_detail_delete` to load a confirmation modal (`_confirm_delete.html`), then `hx-delete` on modal submit with `HX-Trigger: refreshPaymentDetailList`.
*   **DataTables Reload:** The `htmx:afterSwap` event listener in `cashvoucher_new.html` ensures DataTables are re-initialized when table partials (`_payment_detail_table.html`, `_receipt_table.html`) are loaded.
*   **Dynamic Dropdowns (WO/Group & Ac Head):**
    *   Radio button lists (`wo_group_selection`, `ac_head_selection`) have `hx-trigger="click"` and `hx-get` to specific endpoints (`cashvoucher_payment_wo_group_options`, `cashvoucher_payment_ac_head_options`, etc.).
    *   These endpoints return partial HTML (`_payment_wo_group_fields.html`, `_receipt_wo_group_fields.html` or just `<option>` tags) to update the relevant sections of the form (`hx-target`, `hx-swap="outerHTML"` or `innerHTML`).
*   **Autocomplete (Received By/Cash Received Against/By):**
    *   The `ddlCodeType` (select field) has `hx-get` to `autocomplete-person-type` views. This view returns a partial `_autocomplete_input_control.html` which contains the dynamically visible/hidden text input field.
    *   The text input fields (`txtNewCustomerName`, etc.) have `hx-get` to `autocomplete-person` views, `hx-trigger="keyup changed delay:500ms"` to send input for suggestions. The suggestions are then `hx-target`ed to a sibling `div` (`autocomplete-list-{{ target_id }}`).
*   **Modals:** Alpine.js `x-data="{ active: false }"` on the modal container (`#modal`) and `x-show="active"` for visibility. Buttons (Add New, Edit, Delete, Cancel) use `_="on click add .is-active to #modal"` and `_="on click remove .is-active from #modal"` for opening/closing the modal.
*   **Final Proceed Buttons:** `hx-post` to `cashvoucher_payment_proceed` or `cashvoucher_receipt_proceed`. `hx-swap="none"` and `HX-Trigger` headers from backend to re-render the main tab content after successful transaction.

---

### Final Notes

This comprehensive plan provides a robust framework for migrating the ASP.NET Cash Voucher application to Django. By strictly adhering to the specified architectural patterns and technology stack, the resulting solution will be modern, maintainable, scalable, and highly interactive. The emphasis on AI-assisted automation ensures a systematic and efficient transition process, minimizing manual effort and maximizing consistency across the codebase.