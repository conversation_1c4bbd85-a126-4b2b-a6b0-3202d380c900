## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Based on the ASP.NET code, we identify two primary tables:

*   **`tblACC_Contra_Entry`**: This is the main table for Contra entries.
    *   **Columns:**
        *   `Id` (Primary Key, integer)
        *   `Date` (Date, stored as string in ASP.NET, should be `DateField` in Django)
        *   `Cr` (Integer, likely a Foreign Key to `tblACC_Bank.Id`, representing the Credit Account ID)
        *   `Dr` (Integer, likely a Foreign Key to `tblACC_Bank.Id`, representing the Debit Account ID)
        *   `Amount` (Decimal, representing the transaction amount)
        *   `Narration` (Text, for description)
        *   `SysDate` (Date, system entry date)
        *   `SysTime` (Time, system entry time)
        *   `CompId` (Integer, Company ID)
        *   `SessionId` (String, User Session ID)
        *   `FinYearId` (Integer, Financial Year ID)

*   **`tblACC_Bank`**: This table acts as a lookup for accounts involved in Contra entries.
    *   **Columns:**
        *   `Id` (Primary Key, integer)
        *   `Name` (String, the account name)

### Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

*   **Create:**
    *   New Contra entries are added through the `GridView1_RowCommand` event handler (specifically, `CommandName="Add"` and `CommandName="Add1"`).
    *   Input fields are `txtDate`, `DrpListCr`, `DrpListDr`, `txtAmt`, `txtNarr`.
    *   Validation is present for `txtDate`, `DrpListCr`, `DrpListDr`, `txtAmt`.
    *   The `SysDate`, `SysTime`, `CompId`, `SessionId`, `FinYearId` are inserted automatically.
*   **Read:**
    *   The `fillgrid()` method populates `GridView1` by selecting data from `tblACC_Contra_Entry`.
    *   It performs lookups (effectively joins) to get the `Name` from `tblACC_Bank` for both `Cr` and `Dr` IDs.
    *   Pagination is handled by `GridView1_PageIndexChanging`.
*   **Update:**
    *   Editing an existing entry is triggered by `CommandName="Edit"` (handled by `GridView1_RowEditing`).
    *   Updating is handled by `GridView1_RowUpdating` (after `CommandName="Update"`).
    *   Input fields for update are `txtDate0`, `DrpListCr0`, `DrpListDr0`, `txtAmt0`, `txtNarr0`.
*   **Delete:**
    *   Deletion is handled by `GridView1_RowDeleting` (after `CommandName="Delete"`).
*   **Validation:**
    *   `RequiredFieldValidator` ensures fields are not empty.
    *   `RegularExpressionValidator` checks the `Amount` format (`^\d{1,15}(\.\d{0,3})?$`).
*   **Dynamic Interactions:**
    *   The `DrpListCr_SelectedIndexChanged`, `DrpListCrF_SelectedIndexChanged`, `DrpListCrE_SelectedIndexChanged` events demonstrate dynamic filtering: when the Credit account (`DrpListCr`) is selected, the Debit account (`DrpListDr`) dropdown is repopulated to exclude the selected Credit account.

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

*   **GridView1:** This will be replaced by a Django template using a `<table>` tag, enhanced with DataTables for client-side sorting, filtering, and pagination. HTMX will be used to load this table dynamically and refresh it.
*   **asp:TemplateField:** Each template field corresponds to a column in the Django table.
    *   **Labels:** `asp:Label` for display.
    *   **Text Inputs:** `asp:TextBox` for `Date`, `Amount`, `Narration`. These will map to Django `forms.TextInput` and `forms.Textarea` with Tailwind CSS classes. `CalendarExtender` implies a date picker, which will be implemented with a simple date input or a minimal JS library (if needed, but for simplicity, we'll use a basic date input for now).
    *   **Dropdowns:** `asp:DropDownList` for `Cr` and `Dr`. These will map to Django `forms.Select`. The dynamic filtering will be handled by HTMX.
    *   **Buttons/Links:** `asp:LinkButton` and `asp:Button` for `Edit`, `Update`, `Cancel`, `Delete`, `Add`. These will be HTMX-enabled buttons triggering modal forms or direct deletions.
*   **Validation Controls:** `RequiredFieldValidator` and `RegularExpressionValidator` will be translated into Django form validation rules.
*   **Client-side JS (`PopUpMsg.js`):** `confirmationUpdate()` and `confirmationDelete()` will be replaced by HTMX modal confirmations or Alpine.js controlled dialogs.

### Step 4: Generate Django Code

We will create a new Django app, let's call it `contraentry`.

#### 4.1 Models

Task: Create Django models based on the database schema.

## Instructions:

We need two models: `AccountBank` for the lookup table and `ContraEntry` for the main entries. The `Id` field in `tblACC_Contra_Entry` is `Nos` in the UI and `Id` in the database. `Cr` and `Dr` are foreign keys to `tblACC_Bank`.

```python
# contraentry/models.py
from django.db import models
from django.utils import timezone

class AccountBank(models.Model):
    # This model maps to tblACC_Bank
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=255)

    class Meta:
        managed = False  # Set to False if the table already exists in the database
        db_table = 'tblACC_Bank'
        verbose_name = 'Bank Account'
        verbose_name_plural = 'Bank Accounts'

    def __str__(self):
        return self.name

class ContraEntry(models.Model):
    # This model maps to tblACC_Contra_Entry
    id = models.IntegerField(db_column='Id', primary_key=True) # Corresponds to 'Nos' in ASPX
    date = models.DateField(db_column='Date')
    # Cr and Dr are foreign keys to AccountBank
    credit_account = models.ForeignKey(AccountBank, on_delete=models.PROTECT, db_column='Cr', related_name='credited_entries', verbose_name='Credit Account')
    debit_account = models.ForeignKey(AccountBank, on_delete=models.PROTECT, db_column='Dr', related_name='debited_entries', verbose_name='Debit Account')
    amount = models.DecimalField(db_column='Amount', max_digits=15, decimal_places=3)
    narration = models.TextField(db_column='Narration', blank=True, null=True)

    # Fields that might be populated by the application logic or DB triggers
    sys_date = models.DateField(db_column='SysDate', auto_now_add=True) # Assuming auto-populated
    sys_time = models.TimeField(db_column='SysTime', auto_now_add=True) # Assuming auto-populated
    company_id = models.IntegerField(db_column='CompId')
    session_id = models.CharField(db_column='SessionId', max_length=255)
    financial_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False  # Set to False if the table already exists in the database
        db_table = 'tblACC_Contra_Entry'
        verbose_name = 'Contra Entry'
        verbose_name_plural = 'Contra Entries'
        # Ordering based on 'Id Desc' from ASP.NET code
        ordering = ['-id']

    def __str__(self):
        return f"Contra {self.id}: {self.credit_account.name} to {self.debit_account.name} ({self.amount})"

    # Business logic methods can go here if needed.
    # For instance, a method to get a formatted date if it was not a DateField initially
    def get_formatted_date(self):
        return self.date.strftime('%d-%m-%Y')

    # Example of a business logic method that could be in the model
    @classmethod
    def get_contra_entries_for_company_and_finyear(cls, company_id, finyear_id):
        """
        Retrieves contra entries filtered by company and financial year.
        This encapsulates the logic from the ASP.NET fillgrid method.
        """
        return cls.objects.filter(company_id=company_id, financial_year_id__lte=finyear_id) # The ASP.NET had <=, not sure if that's correct or if it implies current finyear only.
```

#### 4.2 Forms

Task: Define Django forms for user input.

## Instructions:

We will create a `ModelForm` for `ContraEntry`. The `Dr` (debit_account) dropdown needs dynamic filtering based on `Cr` (credit_account) selection. This will be handled by HTMX, so the form itself will expose choices and the HTMX part will handle the filtering.

```python
# contraentry/forms.py
from django import forms
from .models import ContraEntry, AccountBank

class ContraEntryForm(forms.ModelForm):
    # This field will be dynamically filtered by HTMX in the template
    # We define it here to ensure it's part of the form's fields, even if choices are empty initially
    debit_account = forms.ModelChoiceField(
        queryset=AccountBank.objects.all(), # Initial queryset, will be filtered by HTMX
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    class Meta:
        model = ContraEntry
        fields = ['date', 'credit_account', 'debit_account', 'amount', 'narration']
        widgets = {
            'date': forms.DateInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'type': 'date' # HTML5 date input for native date picker
            }),
            'credit_account': forms.Select(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'hx-get': '/contraentry/get-dr-options/', # HTMX endpoint for dynamic dropdown
                'hx-target': '#id_debit_account', # Target to update the Dr dropdown
                'hx-trigger': 'change', # Trigger on change
                'hx-swap': 'innerHTML' # Swap inner HTML
            }),
            'amount': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'pattern': r'^\d{1,15}(\.\d{0,3})?$', # Regex from ASP.NET for client-side validation
                'title': 'Enter a valid amount (up to 15 digits, 0-3 decimal places)'
            }),
            'narration': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'rows': 3
            }),
        }
        labels = {
            'date': 'Date',
            'credit_account': 'Cr.',
            'debit_account': 'Dr.',
            'amount': 'Amount',
            'narration': 'Narration',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['credit_account'].queryset = AccountBank.objects.all().order_by('name')
        self.fields['credit_account'].empty_label = "Select"
        
        # If editing an existing object, pre-select the debit_account based on initial data
        if self.instance and self.instance.pk:
            initial_cr_id = self.instance.credit_account.id
            self.fields['debit_account'].queryset = AccountBank.objects.exclude(id=initial_cr_id).order_by('name')
        else:
             # Initially, the debit account dropdown is empty or only shows "Select"
            self.fields['debit_account'].queryset = AccountBank.objects.none()
            # If creating a new form, ensure the initial select option for debit_account is "Select"
            self.fields['debit_account'].choices = [('', 'Select')]
            # Mark it as required, as the ASP.NET code shows a RequiredFieldValidator
            self.fields['debit_account'].required = True 

    def clean(self):
        cleaned_data = super().clean()
        credit_account = cleaned_data.get('credit_account')
        debit_account = cleaned_data.get('debit_account')
        
        # Ensure Credit and Debit accounts are not the same
        if credit_account and debit_account and credit_account == debit_account:
            self.add_error('debit_account', "Debit account cannot be the same as Credit account.")
            
        # Add server-side validation for amount regex, though HTML5 pattern does client-side
        amount = cleaned_data.get('amount')
        if amount is not None:
            # Re-check pattern in Python, though DecimalField handles basic validity
            # The DecimalField itself handles numerical validation; the regex is for format.
            # If amount isn't a valid decimal, Django's DecimalField will already raise ValidationError.
            # If more specific formatting is needed, add regex validation here.
            import re
            if not re.match(r'^\d{1,15}(\.\d{0,3})?$', str(amount)):
                 self.add_error('amount', 'Enter a valid amount (up to 15 digits, 0-3 decimal places).')
        
        return cleaned_data
```

#### 4.3 Views

Task: Implement CRUD operations using CBVs.

## Instructions:

We will define `ListView`, `CreateView`, `UpdateView`, `DeleteView`, and add a `TablePartialView` for HTMX updates and a `DrDropdownOptionsView` for dynamic dropdown filtering. We will assume `CompId`, `FinYearId`, and `SessionId` are available (e.g., from `request.user` or a session context).

```python
# contraentry/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from django.shortcuts import get_object_or_404
from .models import ContraEntry, AccountBank
from .forms import ContraEntryForm

# Dummy session data for demonstration purposes,
# In a real app, these would come from request.user or actual session management.
DEMO_COMPANY_ID = 1
DEMO_FINANCIAL_YEAR_ID = 2023 # Assuming latest financial year
DEMO_SESSION_ID = 'user123'

class ContraEntryListView(ListView):
    model = ContraEntry
    template_name = 'contraentry/contraentry_list.html'
    context_object_name = 'contraentries'

    # Filter initial list based on demo session data
    def get_queryset(self):
        # Emulating ASP.NET's fillgrid filtering: CompId and FinYearId
        return ContraEntry.get_contra_entries_for_company_and_finyear(
            company_id=DEMO_COMPANY_ID,
            finyear_id=DEMO_FINANCIAL_YEAR_ID
        )

class ContraEntryTablePartialView(ListView):
    model = ContraEntry
    template_name = 'contraentry/_contraentry_table.html'
    context_object_name = 'contraentries'

    def get_queryset(self):
        # This queryset should match the main ListView's queryset
        return ContraEntry.get_contra_entries_for_company_and_finyear(
            company_id=DEMO_COMPANY_ID,
            finyear_id=DEMO_FINANCIAL_YEAR_ID
        )

class ContraEntryCreateView(CreateView):
    model = ContraEntry
    form_class = ContraEntryForm
    template_name = 'contraentry/_contraentry_form.html'
    success_url = reverse_lazy('contraentry_list') # Redirection for non-HTMX requests

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass all bank accounts for initial credit dropdown options
        context['account_banks'] = AccountBank.objects.all()
        return context

    def form_valid(self, form):
        # Populate additional fields from "session" as in ASP.NET code
        form.instance.id = ContraEntry.objects.all().order_by('-id').first().id + 1 if ContraEntry.objects.exists() else 1 # Simple auto-increment for managed=False
        form.instance.company_id = DEMO_COMPANY_ID
        form.instance.financial_year_id = DEMO_FINANCIAL_YEAR_ID
        form.instance.session_id = DEMO_SESSION_ID # Or request.user.username, request.session.session_key etc.

        response = super().form_valid(form)
        messages.success(self.request, 'Contra Entry added successfully.')

        # HTMX-specific response: No content and trigger a refresh event
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={'HX-Trigger': 'refreshContraEntryList'}
            )
        return response

    def form_invalid(self, form):
        # For HTMX requests, render the form again with errors
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                render_to_string(self.template_name, {'form': form}, request=self.request),
                status=400 # Bad Request
            )
        return super().form_invalid(form)


class ContraEntryUpdateView(UpdateView):
    model = ContraEntry
    form_class = ContraEntryForm
    template_name = 'contraentry/_contraentry_form.html'
    context_object_name = 'contraentry'
    success_url = reverse_lazy('contraentry_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass all bank accounts for initial credit dropdown options
        context['account_banks'] = AccountBank.objects.all()
        return context

    def form_valid(self, form):
        # Ensure company and financial year are not changed during update if they are session-bound
        form.instance.company_id = DEMO_COMPANY_ID
        form.instance.financial_year_id = DEMO_FINANCIAL_YEAR_ID
        form.instance.session_id = DEMO_SESSION_ID # Keep existing or update as per logic

        response = super().form_valid(form)
        messages.success(self.request, 'Contra Entry updated successfully.')

        # HTMX-specific response
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshContraEntryList'}
            )
        return response

    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                render_to_string(self.template_name, {'form': form}, request=self.request),
                status=400
            )
        return super().form_invalid(form)

class ContraEntryDeleteView(DeleteView):
    model = ContraEntry
    template_name = 'contraentry/_contraentry_confirm_delete.html'
    context_object_name = 'contraentry'
    success_url = reverse_lazy('contraentry_list')

    def delete(self, request, *args, **kwargs):
        # ASP.NET code used CompId for deletion
        # Here we ensure the object belongs to the current company before deleting
        self.object = self.get_object()
        if self.object.company_id != DEMO_COMPANY_ID:
            messages.error(request, 'Unauthorized attempt to delete entry.')
            return HttpResponse(status=403) # Forbidden

        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Contra Entry deleted successfully.')

        # HTMX-specific response
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshContraEntryList'}
            )
        return response

class DrDropdownOptionsView(View):
    """
    Returns HTMX-swapped HTML for the Debit Account dropdown,
    excluding the selected Credit Account.
    """
    def get(self, request, *args, **kwargs):
        credit_account_id = request.GET.get('credit_account')
        options_html = ""
        # The ASP.NET code inserts "Select" if Cr is "Select", otherwise filters
        if credit_account_id and credit_account_id != "Select":
            try:
                credit_account_id = int(credit_account_id)
                # Exclude the selected credit account from the debit account options
                debit_accounts = AccountBank.objects.exclude(id=credit_account_id).order_by('name')
                options_html += '<option value="">Select</option>' # Always add "Select" option
                for account in debit_accounts:
                    options_html += f'<option value="{account.id}">{account.name}</option>'
            except ValueError:
                # Handle invalid ID gracefully
                options_html += '<option value="">Select</option>'
        else:
            options_html += '<option value="">Select</option>' # If credit account is not selected
        
        return HttpResponse(options_html) # HTMX swaps innerHTML directly
```

#### 4.4 Templates

Task: Create templates for each view.

## Instructions:

We will create `contraentry/contraentry_list.html` as the main page, and partials `_contraentry_table.html`, `_contraentry_form.html`, `_contraentry_confirm_delete.html` for HTMX.

```html
<!-- contraentry/contraentry_list.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Contra Entries</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-200"
            hx-get="{% url 'contraentry_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Contra Entry
        </button>
    </div>

    <div id="contraentryTable-container"
         hx-trigger="load, refreshContraEntryList from:body"
         hx-get="{% url 'contraentry_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Contra Entries...</p>
        </div>
    </div>

    <!-- Modal for form (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ showModal: false }" x-show="showModal"
    >
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-3xl w-full mx-4"
             _="on htmx:afterSwap remove .is-active from #modal if event.detail.xhr.status == 204"
             x-init="$watch('showModal', value => { if(value) document.body.style.overflow = 'hidden'; else document.body.style.overflow = 'auto' })"
             >
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed
    });

    // Handle HTMX events for modal toggling
    document.body.addEventListener('htmx:beforeSwap', function(evt) {
        if (evt.detail.target.id === 'modalContent' && evt.detail.xhr.status === 204) {
            // This is for successful form submissions that return 204
            const modal = document.getElementById('modal');
            modal.classList.remove('is-active');
            evt.detail.shouldSwap = false; // Prevent content swap for 204
        }
    });

    // Listener to re-initialize DataTables after HTMX loads the table content
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'contraentryTable-container') {
            $('#contraentryTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true // Destroy existing DataTable before re-initializing
            });
        }
    });

</script>
{% endblock %}
```

```html
<!-- contraentry/_contraentry_table.html -->
<div class="overflow-x-auto shadow-md sm:rounded-lg">
    <table id="contraentryTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nos</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cr.</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dr.</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Narration</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in contraentries %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ obj.id }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ obj.date|date:"d-m-Y" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ obj.credit_account.name }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ obj.debit_account.name }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900 text-right">{{ obj.amount }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ obj.narration|default:"-" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-2 rounded text-xs transition duration-200 mr-2"
                        hx-get="{% url 'contraentry_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-2 rounded text-xs transition duration-200"
                        hx-get="{% url 'contraentry_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="py-4 text-center text-gray-500">No Contra Entries found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // This script will be run every time the table is loaded via HTMX
    // The main list.html has a listener to initialize DataTables.
    // This block is left empty to avoid double initialization but demonstrates where it would go if this was the only script.
</script>
```

```html
<!-- contraentry/_contraentry_form.html -->
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Contra Entry</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}

        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="text-red-500 text-xs mt-1">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        {% if form.non_field_errors %}
        <div class="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {% for error in form.non_field_errors %}
            <p>{{ error }}</p>
            {% endfor %}
        </div>
        {% endif %}

        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded transition duration-200"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-200">
                Save
            </button>
        </div>
    </form>
</div>
```

```html
<!-- contraentry/_contraentry_confirm_delete.html -->
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Contra Entry #{{ contraentry.id }}?</p>

    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
        <strong class="font-bold">Warning!</strong>
        <span class="block sm:inline">This action cannot be undone.</span>
    </div>

    <div class="flex justify-end space-x-4">
        <button
            type="button"
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded transition duration-200"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button
            hx-post="{% url 'contraentry_delete' contraentry.pk %}"
            hx-target="body"
            hx-swap="none"
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded transition duration-200">
            Confirm Delete
        </button>
    </div>
</div>
```

#### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

Create `contraentry/urls.py` and include it in your project's `urls.py`.

```python
# contraentry/urls.py
from django.urls import path
from .views import (
    ContraEntryListView,
    ContraEntryCreateView,
    ContraEntryUpdateView,
    ContraEntryDeleteView,
    ContraEntryTablePartialView,
    DrDropdownOptionsView,
)

urlpatterns = [
    path('contraentry/', ContraEntryListView.as_view(), name='contraentry_list'),
    path('contraentry/table/', ContraEntryTablePartialView.as_view(), name='contraentry_table'), # HTMX partial for table refresh
    path('contraentry/add/', ContraEntryCreateView.as_view(), name='contraentry_add'),
    path('contraentry/edit/<int:pk>/', ContraEntryUpdateView.as_view(), name='contraentry_edit'),
    path('contraentry/delete/<int:pk>/', ContraEntryDeleteView.as_view(), name='contraentry_delete'),
    path('contraentry/get-dr-options/', DrDropdownOptionsView.as_view(), name='get_dr_options'), # HTMX endpoint for dynamic dropdown
]

# In your main project/urls.py, make sure to include this:
# from django.urls import path, include
# urlpatterns = [
#     path('accounts/', include('django.contrib.auth.urls')), # If using Django auth
#     path('', include('contraentry.urls')),
#     # ... other app urls
# ]
```

#### 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

Include comprehensive unit tests for model methods and properties, and integration tests for all views.

```python
# contraentry/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import AccountBank, ContraEntry
from datetime import date
from decimal import Decimal

# Assuming these dummy values for company_id, finyear_id, session_id
# match the ones used in views.py
DEMO_COMPANY_ID = 1
DEMO_FINANCIAL_YEAR_ID = 2023
DEMO_SESSION_ID = 'user123'

class AccountBankModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for AccountBank
        AccountBank.objects.create(id=1, name='Cash Account')
        AccountBank.objects.create(id=2, name='Bank A/C One')
        AccountBank.objects.create(id=3, name='Bank A/C Two')

    def test_account_bank_creation(self):
        account = AccountBank.objects.get(id=1)
        self.assertEqual(account.name, 'Cash Account')
        self.assertEqual(str(account), 'Cash Account')
        self.assertEqual(account._meta.db_table, 'tblACC_Bank')
        self.assertFalse(account._meta.managed)

    def test_account_bank_name_label(self):
        account = AccountBank.objects.get(id=1)
        field_label = account._meta.get_field('name').verbose_name
        self.assertEqual(field_label, 'Name')

class ContraEntryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dependent AccountBank data
        cls.cash_account = AccountBank.objects.create(id=101, name='Main Cash Account')
        cls.bank_account = AccountBank.objects.create(id=102, name='Main Bank Account')
        cls.other_bank_account = AccountBank.objects.create(id=103, name='Savings Bank Account')

        # Create test ContraEntry data
        ContraEntry.objects.create(
            id=1,
            date=date(2023, 1, 15),
            credit_account=cls.cash_account,
            debit_account=cls.bank_account,
            amount=Decimal('1000.500'),
            narration='Transfer from cash to bank',
            company_id=DEMO_COMPANY_ID,
            financial_year_id=DEMO_FINANCIAL_YEAR_ID,
            session_id=DEMO_SESSION_ID
        )
        ContraEntry.objects.create(
            id=2,
            date=date(2023, 1, 20),
            credit_account=cls.bank_account,
            debit_account=cls.other_bank_account,
            amount=Decimal('500.00'),
            narration='Transfer between bank accounts',
            company_id=DEMO_COMPANY_ID,
            financial_year_id=DEMO_FINANCIAL_YEAR_ID
        )

    def test_contra_entry_creation(self):
        entry = ContraEntry.objects.get(id=1)
        self.assertEqual(entry.date, date(2023, 1, 15))
        self.assertEqual(entry.credit_account, self.cash_account)
        self.assertEqual(entry.debit_account, self.bank_account)
        self.assertEqual(entry.amount, Decimal('1000.500'))
        self.assertEqual(entry.narration, 'Transfer from cash to bank')
        self.assertEqual(entry.company_id, DEMO_COMPANY_ID)
        self.assertEqual(entry.financial_year_id, DEMO_FINANCIAL_YEAR_ID)
        self.assertEqual(entry._meta.db_table, 'tblACC_Contra_Entry')
        self.assertFalse(entry._meta.managed)

    def test_contra_entry_verbose_names(self):
        entry = ContraEntry.objects.get(id=1)
        self.assertEqual(entry._meta.verbose_name, 'Contra Entry')
        self.assertEqual(entry._meta.verbose_name_plural, 'Contra Entries')
        self.assertEqual(entry._meta.get_field('credit_account').verbose_name, 'Credit Account')
        self.assertEqual(entry._meta.get_field('debit_account').verbose_name, 'Debit Account')

    def test_get_contra_entries_for_company_and_finyear(self):
        # Create an entry for a different company/finyear to test filtering
        ContraEntry.objects.create(
            id=3,
            date=date(2023, 2, 1),
            credit_account=self.cash_account,
            debit_account=self.bank_account,
            amount=Decimal('200.00'),
            narration='Different company entry',
            company_id=DEMO_COMPANY_ID + 1,
            financial_year_id=DEMO_FINANCIAL_YEAR_ID + 1
        )
        entries = ContraEntry.get_contra_entries_for_company_and_finyear(DEMO_COMPANY_ID, DEMO_FINANCIAL_YEAR_ID)
        self.assertEqual(entries.count(), 2)
        self.assertIn(ContraEntry.objects.get(id=1), entries)
        self.assertIn(ContraEntry.objects.get(id=2), entries)
        self.assertNotIn(ContraEntry.objects.get(id=3), entries)

class ContraEntryViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.cash_account = AccountBank.objects.create(id=101, name='Cash Account')
        cls.bank_account_one = AccountBank.objects.create(id=102, name='Bank A/C One')
        cls.bank_account_two = AccountBank.objects.create(id=103, name='Bank A/C Two')

        ContraEntry.objects.create(
            id=1,
            date=date(2023, 1, 1),
            credit_account=cls.cash_account,
            debit_account=cls.bank_account_one,
            amount=Decimal('100.00'),
            narration='Test entry 1',
            company_id=DEMO_COMPANY_ID,
            financial_year_id=DEMO_FINANCIAL_YEAR_ID,
            session_id=DEMO_SESSION_ID
        )
        ContraEntry.objects.create(
            id=2,
            date=date(2023, 1, 2),
            credit_account=cls.bank_account_one,
            debit_account=cls.cash_account,
            amount=Decimal('200.00'),
            narration='Test entry 2',
            company_id=DEMO_COMPANY_ID,
            financial_year_id=DEMO_FINANCIAL_YEAR_ID,
            session_id=DEMO_SESSION_ID
        )

    def setUp(self):
        self.client = Client()

    def test_list_view(self):
        response = self.client.get(reverse('contraentry_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'contraentry/contraentry_list.html')
        self.assertTrue('contraentries' in response.context)
        self.assertEqual(response.context['contraentries'].count(), 2) # Should show 2 for demo company/finyear

    def test_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('contraentry_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'contraentry/_contraentry_table.html')
        self.assertTrue('contraentries' in response.context)
        self.assertContains(response, 'contraentryTable') # Check for table ID
        self.assertContains(response, 'Test entry 1') # Check for content

    def test_create_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('contraentry_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'contraentry/_contraentry_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Contra Entry')

    def test_create_view_post_htmx_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'date': '2023-01-25',
            'credit_account': self.cash_account.id,
            'debit_account': self.bank_account_two.id,
            'amount': '300.00',
            'narration': 'New HTMX entry',
        }
        response = self.client.post(reverse('contraentry_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # No content on successful HTMX POST
        self.assertEqual(response.headers['HX-Trigger'], 'refreshContraEntryList')
        self.assertTrue(ContraEntry.objects.filter(narration='New HTMX entry').exists())

    def test_create_view_post_htmx_invalid(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'date': '', # Missing required field
            'credit_account': self.cash_account.id,
            'debit_account': self.cash_account.id, # Same Cr and Dr
            'amount': 'invalid_amount', # Invalid amount
            'narration': 'Invalid entry',
        }
        response = self.client.post(reverse('contraentry_add'), data, **headers)
        self.assertEqual(response.status_code, 400) # Bad Request for invalid form
        self.assertTemplateUsed(response, 'contraentry/_contraentry_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Debit account cannot be the same as Credit account.')
        self.assertContains(response, 'Enter a valid amount') # For DecimalField validation

    def test_update_view_get_htmx(self):
        entry = ContraEntry.objects.get(id=1)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('contraentry_edit', args=[entry.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'contraentry/_contraentry_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Edit Contra Entry')
        self.assertContains(response, 'Test entry 1') # Check pre-filled data

    def test_update_view_post_htmx_success(self):
        entry = ContraEntry.objects.get(id=1)
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'date': '2023-01-01', # original date
            'credit_account': self.cash_account.id,
            'debit_account': self.bank_account_two.id, # Changed debit account
            'amount': '150.00', # Changed amount
            'narration': 'Updated entry 1',
        }
        response = self.client.post(reverse('contraentry_edit', args=[entry.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshContraEntryList')
        entry.refresh_from_db()
        self.assertEqual(entry.narration, 'Updated entry 1')
        self.assertEqual(entry.amount, Decimal('150.000'))
        self.assertEqual(entry.debit_account, self.bank_account_two)

    def test_delete_view_get_htmx(self):
        entry = ContraEntry.objects.get(id=1)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('contraentry_delete', args=[entry.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'contraentry/_contraentry_confirm_delete.html')
        self.assertTrue('contraentry' in response.context)
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, f'delete the Contra Entry #{entry.id}')

    def test_delete_view_post_htmx_success(self):
        entry_to_delete = ContraEntry.objects.get(id=2)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('contraentry_delete', args=[entry_to_delete.pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshContraEntryList')
        self.assertFalse(ContraEntry.objects.filter(id=entry_to_delete.id).exists())
        self.assertEqual(ContraEntry.objects.count(), 1) # Only 1 entry remaining

    def test_delete_view_post_unauthorized(self):
        # Create an entry for a different company
        other_company_entry = ContraEntry.objects.create(
            id=99,
            date=date(2023, 3, 1),
            credit_account=self.cash_account,
            debit_account=self.bank_account_one,
            amount=Decimal('50.00'),
            narration='Other company entry',
            company_id=DEMO_COMPANY_ID + 1, # Different company
            financial_year_id=DEMO_FINANCIAL_YEAR_ID,
            session_id='other_user'
        )
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('contraentry_delete', args=[other_company_entry.pk]), **headers)
        self.assertEqual(response.status_code, 403) # Forbidden
        self.assertTrue(ContraEntry.objects.filter(id=other_company_entry.id).exists()) # Should not be deleted

    def test_dr_dropdown_options_view_valid_credit(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('get_dr_options'), {'credit_account': self.cash_account.id}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'<option value="">Select</option>', response.content)
        self.assertIn(self.bank_account_one.name.encode('utf-8'), response.content)
        self.assertIn(self.bank_account_two.name.encode('utf-8'), response.content)
        self.assertNotIn(self.cash_account.name.encode('utf-8'), response.content) # Should be excluded

    def test_dr_dropdown_options_view_no_credit_selected(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('get_dr_options'), {'credit_account': 'Select'}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'<option value="">Select</option>', response.content)
        # Should not contain other options if "Select" is chosen for credit
        self.assertEqual(len(response.content.strip().split(b'\n')), 1) # Only "Select" option
```

### Step 5: HTMX and Alpine.js Integration

## Instructions:

*   **HTMX for dynamic updates:**
    *   The `contraentry_list.html` uses `hx-get="{% url 'contraentry_table' %}" hx-target="#contraentryTable-container" hx-trigger="load, refreshContraEntryList from:body"` to load the table on page load and refresh it after CRUD operations (triggered by `HX-Trigger` from `CreateView`, `UpdateView`, `DeleteView`).
    *   Buttons for "Add", "Edit", "Delete" use `hx-get` to load forms into the modal, targeting `#modalContent`.
    *   Form submissions (`_contraentry_form.html`, `_contraentry_confirm_delete.html`) use `hx-post` to send data to the respective URLs, with `hx-swap="none"` and `HX-Trigger` on successful operations.
    *   Dynamic dropdown for `debit_account`: The `credit_account` `forms.Select` widget has `hx-get` to `{% url 'get_dr_options' %}` and `hx-target="#id_debit_account"` with `hx-trigger="change"`.
*   **Alpine.js for UI state management:**
    *   The modal in `contraentry_list.html` uses `x-data="{ showModal: false }"` and `x-show="showModal"`. The `add .is-active to #modal` from `_` (hyperscript) snippet is used to show/hide the modal based on button clicks.
    *   `_="on click if event.target.id == 'modal' remove .is-active from me"` for closing the modal by clicking outside.
    *   `_="on click remove .is-active from #modal"` on Cancel buttons.
    *   `x-init="$watch('showModal', ...)` to control body scroll when modal is active.
*   **DataTables for list views:**
    *   The `_contraentry_table.html` defines the `<table>` with `id="contraentryTable"`.
    *   The `contraentry_list.html` includes a script block that listens for `htmx:afterSwap` on the table container and re-initializes `$('#contraentryTable').DataTable()`, ensuring DataTables functionality persists after HTMX refreshes the table content. `destroy: true` is used for proper re-initialization.

## Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating the ASP.NET Contra Entry module to a modern Django application. By leveraging AI-assisted automation, the systematic breakdown into distinct Django components, combined with the strict adherence to modern best practices like fat models, thin views, HTMX, Alpine.js, and DataTables, ensures a robust, maintainable, and highly interactive solution. The focus on non-technical language makes this plan accessible to business stakeholders, enabling them to understand and oversee the modernization process effectively.