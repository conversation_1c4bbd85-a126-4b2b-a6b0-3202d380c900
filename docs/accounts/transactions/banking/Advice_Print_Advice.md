This modernization plan details the conversion of an ASP.NET Crystal Report viewer page into a modern Django-based report generation and display system. Our approach focuses on delivering a secure, scalable, and maintainable solution while adhering to the principles of "Fat Model, Thin View" and leveraging modern frontend technologies like HTMX and Alpine.js.

## ASP.NET to Django Conversion Script: Advice Print Advice

This plan transforms a legacy ASP.NET Crystal Report generation module into a performant, maintainable, and modern Django application.

### Business Benefits:

*   **Cost Reduction:** By transitioning from proprietary Crystal Reports to open-source Python libraries (like WeasyPrint for PDF generation), your organization can eliminate licensing fees and reduce vendor lock-in, leading to significant cost savings.
*   **Enhanced Performance & Scalability:** Django's efficient ORM and robust architecture provide a faster, more responsive experience for generating and viewing reports, capable of scaling to meet growing business demands.
*   **Improved Maintainability & Development Efficiency:** Adopting Django's "Fat Model, Thin View" architecture promotes cleaner, more modular code. This makes the application easier to understand, debug, and extend, reducing development time and maintenance overhead.
*   **Modern User Experience:** Leveraging HTMX and Alpine.js delivers dynamic, interactive web pages without the complexity of traditional JavaScript frameworks, resulting in a smoother and more engaging user experience for report previews.
*   **Future-Proofing:** Moving to Django, a widely adopted and actively maintained framework, ensures your application remains compatible with future technologies and benefits from a large, supportive community and continuous security updates.
*   **Automation-Ready:** The structured and modular nature of the Django code facilitates easier integration with future AI-assisted automation tools for further development and maintenance.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:

*   NEVER include `base.html` template code in your output - assume it already exists and is extended.
*   Focus ONLY on component-specific code for the current module (`accounts` in this case).
*   Always include complete unit tests for models and integration tests for views.
*   Use modern Django 5.0+ patterns and follow best practices.
*   Keep your code clean, efficient, and avoid redundancy.
*   Always generate complete, runnable Django code.

## AutoERP Guidelines:

*   Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
*   Map models to existing database using `managed = False` and `db_table`.
*   Implement DataTables for client-side searching, sorting, and pagination where applicable (for lists of data, e.g., detail lines within a report preview).
*   Use HTMX for dynamic interactions and Alpine.js for UI state management.
*   All templates should extend `core/base.html` (but DO NOT include `base.html` code).
*   Achieve at least 80% test coverage with unit and integration tests.
*   Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
*   Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

The ASP.NET code interacts with several database tables to generate the advice report. Based on the SQL queries and `DataTable` structure, we identify the following primary and inferred tables:

**Primary Tables:**

*   **`tblACC_Advice_Payment_Master`**: Stores master details of payment advice.
    *   `Id` (Primary Key, integer)
    *   `CompId` (Integer, refers to Company)
    *   `FinYearId` (Integer, refers to Financial Year)
    *   `ADNo` (String, Advice Document Number)
    *   `Type` (Integer, defines type of advice, e.g., Proforma, Bill Booking)
    *   `ECSType` (Integer, defines ECS type, e.g., Employee, Customer, Supplier)
    *   `PayTo` (String, code or ID of the payee)
    *   `ChequeDate` (Date)
    *   `ChequeNo` (String)
    *   `SysDate` (DateTime, system creation date)
*   **`tblACC_Advice_Payment_Details`**: Stores line-item details for each advice.
    *   `MId` (Foreign Key to `tblACC_Advice_Payment_Master.Id`)
    *   `Particular` (String, description of the item)
    *   `ProformaInvNo` (String, Proforma Invoice Number)
    *   `InvDate` (Date, Invoice Date)
    *   `Amount` (Decimal, amount for the detail line)
*   **`tblACC_BillBooking_Master`**: Used to fetch `BillNo` based on `MId` from `tblACC_BillBooking_Details`.
    *   `Id` (Primary Key, integer)
    *   `BillNo` (String, Bill Booking Number)
*   **`tblACC_BillBooking_Details`**: (Inferred to link `tblACC_BillBooking_Master` to `tblACC_Advice_Payment_Details` based on `DS1.Tables[0].Rows[i][0].ToString()`)
    *   `Id` (Primary Key, integer)
    *   `MId` (Foreign Key to `tblACC_BillBooking_Master.Id`)

**Inferred Supporting Tables (based on `fun.CompAdd`, `fun.ECSNames`, `fun.ECSAddress`):**

*   **`tblCompany`**: Stores company information.
    *   `Id` (Primary Key)
    *   `CompanyName`
    *   `Address`
*   **`tblFinancialYear`**: Stores financial year details.
    *   `Id` (Primary Key)
    *   `YearName`
*   **`tblEmployee`**: Stores employee details.
    *   `Id` (Primary Key)
    *   `Code` (Used as `PayTo`)
    *   `Name`
    *   `Address`
*   **`tblCustomer`**: Stores customer details.
    *   `Id` (Primary Key)
    *   `Code` (Used as `PayTo`)
    *   `Name`
    *   `Address`
*   **`tblSupplier`**: Stores supplier details.
    *   `Id` (Primary Key)
    *   `Code` (Used as `PayTo`)
    *   `Name`
    *   `Address`

### Step 2: Identify Backend Functionality

The core functionality of this ASP.NET page is to generate and display a financial advice report.

*   **Read Operations**:
    *   Retrieves a master record from `tblACC_Advice_Payment_Master` based on `Id`, `CompId`, and `FinYearId`.
    *   Retrieves associated detail records from `tblACC_Advice_Payment_Details` linked to the master record.
    *   Conditionally fetches `BillNo` from `tblACC_BillBooking_Master` for certain advice types.
    *   Performs lookups for `PaidTo` name, `Address`, and `Company Address` based on `ECSType`, `PayTo`, and `CompId` using helper functions (`ECSNames`, `ECSAddress`, `CompAdd`).
    *   Formats dates (`FromDateDMY`) and amounts.
*   **Report Generation**: Consolidates the retrieved data into a structured format (a `DataTable` in ASP.NET) and binds it to a Crystal Report.
*   **Display**: Shows the generated report in a viewer.
*   **Navigation**: A "Cancel" button to redirect to another page.

This page does not involve typical Create, Update, or Delete (CRUD) operations on the underlying data. Its sole purpose is to retrieve, format, and present a pre-existing report.

### Step 3: Infer UI Components

The ASP.NET page presents a simple user interface primarily for displaying a report.

*   **CrystalReportViewer**: The main component for displaying the rendered report. This will be replaced by an HTML table preview and a PDF generation capability in Django.
*   **Panel**: A container for the report viewer, allowing scrolling. This maps to standard HTML `div` elements with CSS styling for overflow.
*   **Button (`btnCancel`)**: A navigation button. This will be replaced by an HTMX-driven button for redirection or a standard HTML link.

### Step 4: Generate Django Code

We will create a new Django application named `accounts` to house this functionality.

#### 4.1 Models (`accounts/models.py`)

We'll define Django models corresponding to the identified database tables. The `AdvicePaymentMaster` model will encapsulate the complex business logic for preparing the report data, adhering to the "Fat Model" principle.

```python
from django.db import models
from django.utils import timezone
from datetime import datetime
from decimal import Decimal

# --- Inferred Supporting Models (Simplified for demonstration) ---
# In a real application, these would be fully defined with all relevant fields.

class Company(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    company_name = models.CharField(max_length=255, db_column='CompanyName')
    address = models.TextField(db_column='Address', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCompany'
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.company_name

class FinancialYear(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    year_name = models.CharField(max_length=50, db_column='YearName')

    class Meta:
        managed = False
        db_table = 'tblFinancialYear'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.year_name

class Employee(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    code = models.CharField(max_length=50, db_column='Code', unique=True)
    name = models.CharField(max_length=255, db_column='Name')
    address = models.TextField(db_column='Address', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblEmployee'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return self.name

class Customer(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    code = models.CharField(max_length=50, db_column='Code', unique=True)
    name = models.CharField(max_length=255, db_column='Name')
    address = models.TextField(db_column='Address', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCustomer'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.name

class Supplier(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    code = models.CharField(max_length=50, db_column='Code', unique=True)
    name = models.CharField(max_length=255, db_column='Name')
    address = models.TextField(db_column='Address', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblSupplier'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.name

# --- Primary Application Models ---

class BillBookingMaster(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    bill_no = models.CharField(max_length=100, db_column='BillNo', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_BillBooking_Master'
        verbose_name = 'Bill Booking Master'
        verbose_name_plural = 'Bill Booking Masters'

    def __str__(self):
        return self.bill_no or f"Bill ID: {self.id}"

class BillBookingDetail(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id') # Assuming an ID for this table
    master = models.ForeignKey(BillBookingMaster, models.DO_NOTHING, db_column='MId', related_name='details') # Linking to Master
    # Add other fields if necessary from tblACC_BillBooking_Details, it was only used for MId in source
    class Meta:
        managed = False
        db_table = 'tblACC_BillBooking_Details'
        verbose_name = 'Bill Booking Detail'
        verbose_name_plural = 'Bill Booking Details'

# Enum-like choices for clarity
ADVICE_TYPE_CHOICES = [
    (1, 'Proforma Invoice'),
    (2, 'Type 2'),
    (3, 'Type 3'),
    (4, 'Bill Booking'),
]

ECS_TYPE_CHOICES = [
    (1, 'Employee'),
    (2, 'Customer'),
    (3, 'Supplier'),
]

class AdvicePaymentMaster(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId')
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYearId')
    ad_no = models.CharField(max_length=100, db_column='ADNo')
    advice_type = models.IntegerField(db_column='Type', choices=ADVICE_TYPE_CHOICES)
    ecs_type = models.IntegerField(db_column='ECSType', choices=ECS_TYPE_CHOICES)
    pay_to_code = models.CharField(max_length=100, db_column='PayTo') # This is the code, not the ID
    cheque_date = models.DateField(db_column='ChequeDate')
    cheque_no = models.CharField(max_length=50, db_column='ChequeNo')
    system_date = models.DateTimeField(db_column='SysDate')

    class Meta:
        managed = False
        db_table = 'tblACC_Advice_Payment_Master'
        verbose_name = 'Advice Payment Master'
        verbose_name_plural = 'Advice Payment Masters'

    def __str__(self):
        return f"Advice No: {self.ad_no} ({self.id})"

    def _get_ecs_entity(self):
        """Helper to get the related Employee, Customer, or Supplier object."""
        if self.ecs_type == 1: # Employee
            return Employee.objects.filter(code=self.pay_to_code).first()
        elif self.ecs_type == 2: # Customer
            return Customer.objects.filter(code=self.pay_to_code).first()
        elif self.ecs_type == 3: # Supplier
            return Supplier.objects.filter(code=self.pay_to_code).first()
        return None

    def get_paid_to_name(self):
        """Replicates fun.ECSNames logic."""
        entity = self._get_ecs_entity()
        return entity.name if entity else "N/A"

    def get_paid_to_address(self):
        """Replicates fun.ECSAddress logic."""
        entity = self._get_ecs_entity()
        return entity.address if entity else "N/A"

    def generate_report_data(self):
        """
        Generates the structured data required for the advice report,
        replicating the complex logic from Page_Init.
        This method embodies the 'Fat Model' principle.
        """
        report_data = []
        details = self.details.all() # Assuming related_name='details' for AdvicePaymentDetail
        
        comp_address = self.company.address # fun.CompAdd logic
        
        for detail in details:
            dr = {
                'PaidTo': self.get_paid_to_name(),
                'CompId': self.company.id,
                'ChequeDate': self.cheque_date.strftime('%d/%m/%Y') if self.cheque_date else '', # fun.FromDateDMY
                'Amount': f"{detail.amount:.3f}", # .ToString("N3")
                'Address': self.get_paid_to_address(),
                'ADNo': self.ad_no,
                'ChequeNo': self.cheque_no,
                'SysDate': self.system_date.strftime('%d/%m/%Y') if self.system_date else '', # fun.FromDateDMY
                'BillNo': '-',
                'TypeECS': dict(ECS_TYPE_CHOICES).get(self.ecs_type, 'N/A'),
                'ECS': self.pay_to_code, # This was PayTo from master, mapped to ECS in the ASP.NET
                'InvoiceNo': '-',
                'Particulars': detail.particular,
                'InvDate': '-',
            }

            # Replicate the 'Type' switch logic
            if self.advice_type == 1: # Proforma Invoice
                dr['InvoiceNo'] = detail.proforma_invoice_no or '-'
                dr['InvDate'] = detail.invoice_date.strftime('%d/%m/%Y') if detail.invoice_date else '-'
            elif self.advice_type == 4: # Bill Booking
                # Assuming detail.bill_booking_master_id exists or can be inferred to get the BillNo
                # The original code's DS1.Tables[0].Rows[i][0] was used as Id for tblACC_BillBooking_Master
                # This implies AdvicePaymentDetail might have a link to BillBookingMaster.
                # For this example, let's assume AdvicePaymentDetail has a 'bill_booking_id'
                # or that the 'particular' field implies a BillBookingMaster.
                # Since the original code's `DS1.Tables[0].Rows[i][0].ToString()` was used directly,
                # let's assume `detail.pk` (if `tblACC_Advice_Payment_Details` has a direct link)
                # or an explicit FK in `AdvicePaymentDetail` to `BillBookingMaster`.
                # For simplicity, assuming a `bill_booking_detail_id` attribute on AdvicePaymentDetail
                # that links to BillBookingDetail's PK, which in turn links to BillBookingMaster.
                try:
                    # This part is highly dependent on the exact foreign key relationships
                    # in your original database if AdvicePaymentDetail directly refers to BillBookingMaster.
                    # We'll simulate based on the ASP.NET code's behavior:
                    # `DS1.Tables[0].Rows[i][0]` was used to fetch BillBookingMaster.
                    # Let's assume `AdvicePaymentDetail` has a field `bill_booking_master_id`
                    # that directly corresponds to `tblACC_BillBooking_Master.Id` when `advice_type == 4`.
                    # If this is incorrect, the actual FK structure needs to be mapped.
                    # For runnable code, let's assume a direct lookup from details `particular` for now or a dummy.
                    # A more robust mapping would be an FK in AdvicePaymentDetail to BillBookingMaster.
                    # Since the original code implies `DS1.Tables[0].Rows[i][0]` was the ID,
                    # let's map it via the `AdvicePaymentDetail` instance itself if possible.
                    # OR, if the `Particular` field contains the bill ID, we parse it.
                    # As a placeholder, let's assume a simplified way:
                    # `DS1.Tables[0].Rows[i][0]` was `tblACC_BillBooking_Details.Id` and then joined to `tblACC_BillBooking_Master`.
                    # Let's assume `detail.bill_booking_id` exists and points to `BillBookingMaster.id`.
                    bill_booking_detail = BillBookingDetail.objects.filter(pk=detail.id).first() # Assuming detail.id points to BillBookingDetail.Id
                    if bill_booking_detail and bill_booking_detail.master:
                        dr['BillNo'] = bill_booking_detail.master.bill_no or '-'
                    else:
                        dr['BillNo'] = '-'
                except Exception: # Handle potential errors if ID is not found or type mismatch
                    dr['BillNo'] = '-'
                dr['InvoiceNo'] = '-'
                dr['InvDate'] = '-'

            report_data.append(dr)
        
        # Add company address as a separate parameter for the report if needed
        # (similar to cryRpt.SetParameterValue("CompAdd", CompAdd);)
        report_context = {
            'company_address': comp_address,
            'report_details': report_data,
            'master_data': { # Data from AdvicePaymentMaster itself for header
                'ad_no': self.ad_no,
                'cheque_date': self.cheque_date.strftime('%d/%m/%Y'),
                'cheque_no': self.cheque_no,
                'system_date': self.system_date.strftime('%d/%m/%Y'),
                'paid_to': self.get_paid_to_name(),
                'address': self.get_paid_to_address(),
                'ecs_type_label': dict(ECS_TYPE_CHOICES).get(self.ecs_type, 'N/A'),
                'pay_to_code': self.pay_to_code,
            }
        }
        return report_context

class AdvicePaymentDetail(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id') # Assuming Id for detail lines
    master = models.ForeignKey(AdvicePaymentMaster, models.DO_NOTHING, db_column='MId', related_name='details')
    particular = models.CharField(max_length=255, db_column='Particular')
    proforma_invoice_no = models.CharField(max_length=100, db_column='ProformaInvNo', blank=True, null=True)
    invoice_date = models.DateField(db_column='InvDate', blank=True, null=True)
    amount = models.DecimalField(max_digits=18, decimal_places=3, db_column='Amount')
    # If there's a direct FK to bill booking, add it here:
    # bill_booking_master = models.ForeignKey(BillBookingMaster, models.DO_NOTHING, db_column='BillBookingMId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_Advice_Payment_Details'
        verbose_name = 'Advice Payment Detail'
        verbose_name_plural = 'Advice Payment Details'

    def __str__(self):
        return f"Detail for {self.master.ad_no}: {self.particular}"

```

#### 4.2 Forms

Since this page is solely for viewing and generating a report, there are no user input forms for CRUD operations. We will explicitly state this.

```python
# accounts/forms.py
# No forms are required for this specific 'print advice' report viewing page,
# as it's a read-only display and PDF generation module.
# If there were search filters or parameters for the report, a form would be defined here.
```

#### 4.3 Views (`accounts/views.py`)

We will use a `TemplateView` to render the report preview and a function-based view for PDF generation. The logic for gathering report data is delegated to the `AdvicePaymentMaster` model.

```python
from django.views.generic import TemplateView
from django.urls import reverse_lazy
from django.shortcuts import get_object_or_404
from django.http import HttpResponse
from django.template.loader import render_to_string
import weasyprint # For PDF generation

from .models import AdvicePaymentMaster, Company, FinancialYear # Import all necessary models

# We assume user session data (CompId, FinYearId) would be available,
# e.g., through middleware or directly from request.session.
# For demonstration, we'll use dummy session data or retrieve default/first available.
# In a real system, you'd secure this with authentication and proper session management.

class AdvicePrintReportView(TemplateView):
    """
    Displays the Advice Payment Report preview in HTML.
    This view fetches the data and renders the HTML, keeping itself thin.
    """
    template_name = 'accounts/advicepaymentmaster/advice_print_report.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        advice_id = self.kwargs.get('pk')
        
        # Simulate session data if not available or fetch from actual session
        # For a real application, ensure proper authentication and session handling.
        company_id = self.request.session.get('compid', 1) # Default to 1 if not in session
        fin_year_id = self.request.session.get('finyear', 1) # Default to 1 if not in session

        try:
            advice_master = get_object_or_404(
                AdvicePaymentMaster,
                pk=advice_id,
                company_id=company_id,
                financial_year_id__lte=fin_year_id # Matching FinYearId<= logic
            )
            report_context = advice_master.generate_report_data() # Fat model in action!
            context.update(report_context)
            context['advice_master'] = advice_master # Pass master object for general use
        except Exception as e:
            # Log the error (e.g., logger.error("Error generating report: %s", e))
            context['error_message'] = "Could not generate report data. Please check parameters."
            context['report_details'] = [] # Ensure report_details is always iterable

        return context

# Function-based view for PDF generation, or could be a method on AdvicePrintReportView
# if using a dedicated PDF rendering library more tightly integrated with CBVs.
def generate_advice_pdf(request, pk):
    """
    Generates and serves the Advice Payment Report as a PDF.
    """
    company_id = request.session.get('compid', 1)
    fin_year_id = request.session.get('finyear', 1)

    try:
        advice_master = get_object_or_404(
            AdvicePaymentMaster,
            pk=pk,
            company_id=company_id,
            financial_year_id__lte=fin_year_id
        )
        report_context = advice_master.generate_report_data()
        
        # Render the HTML content for the PDF using a specific template
        html_string = render_to_string(
            'accounts/advicepaymentmaster/advice_print_report_pdf.html',
            report_context,
            request=request
        )

        # Generate PDF using WeasyPrint
        response = HttpResponse(content_type='application/pdf')
        response['Content-Disposition'] = f'inline; filename="advice_report_{pk}.pdf"'
        weasyprint.HTML(string=html_string, base_url=request.build_absolute_uri()).write_pdf(response)
        return response

    except Exception as e:
        # Log error and return an appropriate error response
        # logger.error("Error generating PDF for advice %s: %s", pk, e)
        return HttpResponse(f"Error generating PDF: {e}", status=500)

```

#### 4.4 Templates

We'll create two templates: one for the HTML preview (`advice_print_report.html`) and another, potentially simpler one, optimized for PDF generation (`advice_print_report_pdf.html`). The main preview will use DataTables for the detail lines.

**`accounts/advicepaymentmaster/advice_print_report.html` (Main Report Preview Template)**

```html
{% extends 'core/base.html' %}

{% block title %}Advice Print Report - {{ advice_master.ad_no }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-lg rounded-lg p-6 mb-8">
        <div class="flex justify-between items-center mb-6 border-b pb-4">
            <h2 class="text-3xl font-extrabold text-gray-800">Payment Advice: {{ advice_master.ad_no }}</h2>
            <div class="flex space-x-3">
                <a href="{% url 'accounts:advice_print_report_pdf' advice_master.pk %}" 
                   class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
                   target="_blank" rel="noopener noreferrer">
                    <i class="fas fa-print mr-2"></i> Generate PDF
                </a>
                <button 
                    class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
                    hx-get="{% url 'accounts:advice_list_previous' %}" 
                    hx-trigger="click" 
                    hx-swap="outerHTML" 
                    hx-push-url="true"
                    _="on click window.location.href='{{ url 'accounts:advice_list_previous' }}'">
                    <i class="fas fa-times-circle mr-2"></i> Cancel
                </button>
            </div>
        </div>

        {% if error_message %}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <strong class="font-bold">Error!</strong>
            <span class="block sm:inline">{{ error_message }}</span>
        </div>
        {% else %}
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-gray-700 mb-6">
            <div>
                <p><strong class="font-semibold">Company Address:</strong> {{ company_address|default:"N/A" }}</p>
                <p><strong class="font-semibold">Advice No:</strong> {{ master_data.ad_no }}</p>
                <p><strong class="font-semibold">Cheque Date:</strong> {{ master_data.cheque_date }}</p>
                <p><strong class="font-semibold">Cheque No:</strong> {{ master_data.cheque_no }}</p>
            </div>
            <div>
                <p><strong class="font-semibold">System Date:</strong> {{ master_data.system_date }}</p>
                <p><strong class="font-semibold">Paid To:</strong> {{ master_data.paid_to }} ({{ master_data.ecs_type_label }} - {{ master_data.pay_to_code }})</p>
                <p><strong class="font-semibold">Address:</strong> {{ master_data.address|default:"N/A" }}</p>
            </div>
        </div>

        <h3 class="text-xl font-semibold text-gray-800 mb-4 border-b pb-2">Advice Details</h3>
        <div class="overflow-x-auto">
            <table id="adviceDetailsTable" class="min-w-full bg-white border border-gray-200">
                <thead>
                    <tr class="bg-gray-100 text-gray-600 uppercase text-sm leading-normal">
                        <th class="py-3 px-6 text-left">SN</th>
                        <th class="py-3 px-6 text-left">Particulars</th>
                        <th class="py-3 px-6 text-left">Inv. No.</th>
                        <th class="py-3 px-6 text-left">Inv. Date</th>
                        <th class="py-3 px-6 text-left">Bill No.</th>
                        <th class="py-3 px-6 text-right">Amount</th>
                    </tr>
                </thead>
                <tbody class="text-gray-700 text-sm">
                    {% for detail in report_details %}
                    <tr class="border-b border-gray-200 hover:bg-gray-50">
                        <td class="py-3 px-6 text-left whitespace-nowrap">{{ forloop.counter }}</td>
                        <td class="py-3 px-6 text-left">{{ detail.Particulars }}</td>
                        <td class="py-3 px-6 text-left">{{ detail.InvoiceNo }}</td>
                        <td class="py-3 px-6 text-left">{{ detail.InvDate }}</td>
                        <td class="py-3 px-6 text-left">{{ detail.BillNo }}</td>
                        <td class="py-3 px-6 text-right font-medium">{{ detail.Amount }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="py-4 px-6 text-center text-gray-500">No details found for this advice.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.tailwindcss.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialize DataTables for the details section
        // This will provide client-side search, sort, pagination for the report details preview.
        $('#adviceDetailsTable').DataTable({
            "paging": true,      // Enable pagination
            "ordering": true,    // Enable sorting
            "info": true,        // Show information about entries
            "searching": true,   // Enable search box
            "pageLength": 10,    // Default number of entries per page
            "lengthMenu": [[5, 10, 25, 50, -1], [5, 10, 25, 50, "All"]], // Options for number of entries
            "dom": 'lfrtip' // Layout: Length, Filter, Table, Info, Paging
        });
    });

    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
        // For simple navigation like the cancel button, HTMX `hx-push-url` and `window.location.href` is sufficient.
    });
</script>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.7/css/dataTables.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.7/css/dataTables.tailwindcss.min.css">
{% endblock %}
```

**`accounts/advicepaymentmaster/advice_print_report_pdf.html` (PDF Optimized Template)**

This template is specifically for PDF generation and should be kept clean, as `WeasyPrint` processes HTML/CSS. It will avoid client-side JavaScript (like DataTables) which is irrelevant for PDF.

```html
<!DOCTYPE html>
<html>
<head>
    <title>Payment Advice: {{ advice_master.ad_no }}</title>
    <style>
        /* Basic CSS for PDF rendering */
        body { font-family: sans-serif; margin: 2cm; font-size: 10pt; }
        h1, h2, h3 { font-family: serif; }
        h1 { font-size: 18pt; text-align: center; margin-bottom: 1cm; }
        h2 { font-size: 14pt; margin-top: 1cm; margin-bottom: 0.5cm; border-bottom: 1px solid #ccc; padding-bottom: 5px; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 1em; }
        th, td { border: 1px solid #ccc; padding: 8px; text-align: left; }
        th { background-color: #f0f0f0; font-weight: bold; }
        .header-info { margin-bottom: 1.5em; display: flex; flex-wrap: wrap;}
        .header-info div { flex: 1; min-width: 45%; margin-right: 1em;}
        .text-right { text-align: right; }
        .no-border { border: none; } /* For elements not part of the main table */
    </style>
</head>
<body>
    <h1>Payment Advice Report</h1>

    <div class="header-info">
        <div>
            <p><strong>Company Address:</strong> {{ company_address|default:"N/A" }}</p>
            <p><strong>Advice No:</strong> {{ master_data.ad_no }}</p>
            <p><strong>Cheque Date:</strong> {{ master_data.cheque_date }}</p>
            <p><strong>Cheque No:</strong> {{ master_data.cheque_no }}</p>
        </div>
        <div>
            <p><strong>System Date:</strong> {{ master_data.system_date }}</p>
            <p><strong>Paid To:</strong> {{ master_data.paid_to }} ({{ master_data.ecs_type_label }} - {{ master_data.pay_to_code }})</p>
            <p><strong>Address:</strong> {{ master_data.address|default:"N/A" }}</p>
        </div>
    </div>

    <h2>Advice Details</h2>
    <table>
        <thead>
            <tr>
                <th>SN</th>
                <th>Particulars</th>
                <th>Inv. No.</th>
                <th>Inv. Date</th>
                <th>Bill No.</th>
                <th class="text-right">Amount</th>
            </tr>
        </thead>
        <tbody>
            {% for detail in report_details %}
            <tr>
                <td>{{ forloop.counter }}</td>
                <td>{{ detail.Particulars }}</td>
                <td>{{ detail.InvoiceNo }}</td>
                <td>{{ detail.InvDate }}</td>
                <td>{{ detail.BillNo }}</td>
                <td class="text-right">{{ detail.Amount }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" style="text-align: center;">No details found for this advice.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <div style="margin-top: 2cm; font-size: 8pt; text-align: center; color: #777;">
        Generated by AutoERP on {{ "now"|date:"d M Y H:i:s" }}
    </div>
</body>
</html>
```

#### 4.5 URLs (`accounts/urls.py`)

Define the URL patterns to map requests to the appropriate views.

```python
from django.urls import path
from .views import AdvicePrintReportView, generate_advice_pdf

app_name = 'accounts' # Namespace for the application

urlpatterns = [
    # URL for displaying the HTML preview of the report
    path('advice-print/<int:pk>/', AdvicePrintReportView.as_view(), name='advice_print_report'),
    
    # URL for generating the PDF version of the report
    path('advice-print/<int:pk>/pdf/', generate_advice_pdf, name='advice_print_report_pdf'),

    # Dummy URL for the cancel button redirection (replace with actual list view URL)
    path('advice-list-previous/', AdvicePrintReportView.as_view(), name='advice_list_previous'), # Placeholder for redirection
]
```

#### 4.6 Tests (`accounts/tests.py`)

Comprehensive tests for both models and views ensure functionality and prevent regressions. We'll include unit tests for model methods and integration tests for views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import date, datetime
from decimal import Decimal

from .models import (
    Company, FinancialYear, Employee, Customer, Supplier,
    BillBookingMaster, BillBookingDetail, AdvicePaymentMaster, AdvicePaymentDetail,
    ADVICE_TYPE_CHOICES, ECS_TYPE_CHOICES
)

# --- Model Tests ---

class AdvicePaymentMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create common test data for all tests
        cls.company = Company.objects.create(id=1, company_name='Test Company', address='123 Test St')
        cls.fin_year = FinancialYear.objects.create(id=1, year_name='2023-2024')
        cls.employee = Employee.objects.create(id=101, code='EMP001', name='John Doe', address='Employee Address')
        cls.customer = Customer.objects.create(id=201, code='CUST001', name='Acme Corp', address='Customer Address')
        cls.supplier = Supplier.objects.create(id=301, code='SUPP001', name='Global Supplies', address='Supplier Address')

        cls.bill_master = BillBookingMaster.objects.create(id=1001, bill_no='BBM/2023/001')
        cls.bill_detail = BillBookingDetail.objects.create(id=2001, master=cls.bill_master) # Assuming AdvicePaymentDetail PK maps to this

        # Advice Master records for different types
        cls.advice_master_proforma = AdvicePaymentMaster.objects.create(
            id=1,
            company=cls.company,
            financial_year=cls.fin_year,
            ad_no='AD001',
            advice_type=1, # Proforma Invoice
            ecs_type=1, # Employee
            pay_to_code=cls.employee.code,
            cheque_date=date(2024, 1, 15),
            cheque_no='CHQ001',
            system_date=datetime(2024, 1, 10, 10, 0, 0, tzinfo=timezone.utc)
        )
        AdvicePaymentDetail.objects.create(
            id=1, # Assuming detail ID
            master=cls.advice_master_proforma,
            particular='Proforma Invoice 1',
            proforma_invoice_no='PI001',
            invoice_date=date(2023, 12, 20),
            amount=Decimal('100.500')
        )
        AdvicePaymentDetail.objects.create(
            id=2,
            master=cls.advice_master_proforma,
            particular='Proforma Invoice 2',
            proforma_invoice_no='PI002',
            invoice_date=date(2023, 12, 25),
            amount=Decimal('200.750')
        )

        cls.advice_master_bill_booking = AdvicePaymentMaster.objects.create(
            id=2,
            company=cls.company,
            financial_year=cls.fin_year,
            ad_no='AD002',
            advice_type=4, # Bill Booking
            ecs_type=2, # Customer
            pay_to_code=cls.customer.code,
            cheque_date=date(2024, 2, 10),
            cheque_no='CHQ002',
            system_date=datetime(2024, 2, 5, 11, 0, 0, tzinfo=timezone.utc)
        )
        # For simplicity, link this detail to the BillBookingMaster ID directly or implicitly
        # As per original code, DS1.Tables[0].Rows[i][0] was used as ID for tblACC_BillBooking_Master,
        # which implies AdvicePaymentDetail might have a field or the PK of detail is the ID.
        # Let's assume AdvicePaymentDetail PK is used to look up BillBookingDetail.
        AdvicePaymentDetail.objects.create(
            id=2001, # This ID needs to correspond to BillBookingDetail's PK (used to fetch bill_master in model)
            master=cls.advice_master_bill_booking,
            particular='Bill Payment',
            amount=Decimal('500.000')
        )

    def test_advice_master_creation(self):
        self.assertEqual(self.advice_master_proforma.ad_no, 'AD001')
        self.assertEqual(self.advice_master_proforma.advice_type, 1)
        self.assertEqual(self.advice_master_proforma.company, self.company)

    def test_get_paid_to_name(self):
        self.assertEqual(self.advice_master_proforma.get_paid_to_name(), 'John Doe')
        self.assertEqual(self.advice_master_bill_booking.get_paid_to_name(), 'Acme Corp')

        # Test for non-existent pay_to_code
        temp_master = AdvicePaymentMaster.objects.create(
            id=3, company=self.company, financial_year=self.fin_year, ad_no='AD003',
            advice_type=1, ecs_type=1, pay_to_code='NONEXISTENT', cheque_date=date(2024,3,1),
            cheque_no='CHQ003', system_date=datetime(2024,3,1,10,0,0,tzinfo=timezone.utc)
        )
        self.assertEqual(temp_master.get_paid_to_name(), 'N/A')
        temp_master.delete()

    def test_get_paid_to_address(self):
        self.assertEqual(self.advice_master_proforma.get_paid_to_address(), 'Employee Address')
        self.assertEqual(self.advice_master_bill_booking.get_paid_to_address(), 'Customer Address')

    def test_generate_report_data_proforma(self):
        report_data = self.advice_master_proforma.generate_report_data()
        self.assertIn('report_details', report_data)
        self.assertEqual(len(report_data['report_details']), 2)

        detail1 = report_data['report_details'][0]
        self.assertEqual(detail1['Particulars'], 'Proforma Invoice 1')
        self.assertEqual(detail1['InvoiceNo'], 'PI001')
        self.assertEqual(detail1['InvDate'], '20/12/2023')
        self.assertEqual(detail1['BillNo'], '-') # Should be '-' for Proforma
        self.assertEqual(detail1['Amount'], '100.500')
        self.assertEqual(detail1['PaidTo'], 'John Doe')
        self.assertEqual(detail1['ADNo'], 'AD001')
        self.assertEqual(detail1['ChequeNo'], 'CHQ001')
        self.assertEqual(detail1['SysDate'], '10/01/2024')
        self.assertEqual(detail1['TypeECS'], 'Employee')

    def test_generate_report_data_bill_booking(self):
        report_data = self.advice_master_bill_booking.generate_report_data()
        self.assertIn('report_details', report_data)
        self.assertEqual(len(report_data['report_details']), 1)

        detail1 = report_data['report_details'][0]
        self.assertEqual(detail1['Particulars'], 'Bill Payment')
        self.assertEqual(detail1['InvoiceNo'], '-')
        self.assertEqual(detail1['InvDate'], '-')
        self.assertEqual(detail1['BillNo'], 'BBM/2023/001') # Should be fetched from BillBookingMaster
        self.assertEqual(detail1['Amount'], '500.000')
        self.assertEqual(detail1['PaidTo'], 'Acme Corp')


# --- View Tests ---

class AdvicePrintReportViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create common test data for views
        cls.company = Company.objects.create(id=1, company_name='Test Company', address='123 View St')
        cls.fin_year = FinancialYear.objects.create(id=1, year_name='2023-2024')
        cls.employee = Employee.objects.create(id=101, code='EMP001', name='View User', address='View Employee Address')

        cls.advice_master = AdvicePaymentMaster.objects.create(
            id=1,
            company=cls.company,
            financial_year=cls.fin_year,
            ad_no='VIEW001',
            advice_type=1,
            ecs_type=1,
            pay_to_code=cls.employee.code,
            cheque_date=date(2024, 4, 1),
            cheque_no='CHQVIEW001',
            system_date=datetime(2024, 3, 28, 9, 30, 0, tzinfo=timezone.utc)
        )
        AdvicePaymentDetail.objects.create(
            id=1,
            master=cls.advice_master,
            particular='View Detail 1',
            proforma_invoice_no='P-VIEW-001',
            invoice_date=date(2024, 3, 20),
            amount=Decimal('75.250')
        )
        AdvicePaymentDetail.objects.create(
            id=2,
            master=cls.advice_master,
            particular='View Detail 2',
            proforma_invoice_no='P-VIEW-002',
            invoice_date=date(2024, 3, 22),
            amount=Decimal('125.000')
        )

    def setUp(self):
        self.client = Client()
        # Simulate session data for views that rely on it
        session = self.client.session
        session['compid'] = self.company.id
        session['finyear'] = self.fin_year.id
        session.save()

    def test_advice_print_report_view_success(self):
        url = reverse('accounts:advice_print_report', args=[self.advice_master.pk])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/advicepaymentmaster/advice_print_report.html')
        self.assertIn('report_details', response.context)
        self.assertEqual(len(response.context['report_details']), 2)
        self.assertContains(response, 'Payment Advice: VIEW001')
        self.assertContains(response, 'View Detail 1')
        self.assertContains(response, '75.250')

    def test_advice_print_report_view_not_found(self):
        url = reverse('accounts:advice_print_report', args=[99999]) # Non-existent PK
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404) # Should return 404 for not found

    def test_generate_advice_pdf_view_success(self):
        url = reverse('accounts:advice_print_report_pdf', args=[self.advice_master.pk])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/pdf')
        self.assertIn(f'filename="advice_report_{self.advice_master.pk}.pdf"', response['Content-Disposition'])
        # You might add a check for PDF content, but this requires parsing PDF, which is complex.
        # Simple check for content type and disposition is usually enough.

    def test_generate_advice_pdf_view_not_found(self):
        url = reverse('accounts:advice_print_report_pdf', args=[99999]) # Non-existent PK
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404) # Should return 404

    def test_cancel_button_redirection(self):
        # This test checks the _client-side_ behavior of the button
        # which involves HTMX/Alpine and then a JS redirect.
        # We can't directly test JS redirects in Django's test client.
        # However, we can ensure the URL the button points to exists.
        cancel_url = reverse('accounts:advice_list_previous')
        response = self.client.get(cancel_url)
        # Assuming the cancel button redirects to a list view that also uses AdvicePrintReportView as a placeholder
        self.assertEqual(response.status_code, 200) # Or 302 if it's a redirect to another list page.
                                                     # For the given placeholder, it's 200.

```

### Step 5: HTMX and Alpine.js Integration

For this particular report view, HTMX primarily handles user interaction for navigation (the "Cancel" button) and potentially dynamic loading of components. Alpine.js can manage simple UI states like loading indicators, though for a static report preview, it's less critical.

*   **Cancel Button (`hx-get`, `hx-trigger`, `hx-swap`, `hx-push-url`, `_` Alpine.js)**: The `Cancel` button is set up to trigger an HTMX GET request to `advice_list_previous`. The `hx-push-url` updates the browser's URL, and the `_` (Alpine.js directive) ensures a full page navigation for the cancel action, simulating the `Response.Redirect` behavior in ASP.NET.
*   **Generate PDF Button**: This is a standard `<a>` tag with `target="_blank"` to open the PDF in a new tab, as PDF generation is typically a separate download/view action rather than an in-page HTMX swap.
*   **DataTables Initialization**: The `adviceDetailsTable` is initialized with DataTables in the `extra_js` block. This provides client-side features like search, sorting, and pagination for the report details, making the preview interactive.
*   **No "Add/Edit/Delete" modals**: Since this is a report viewing page, the typical HTMX modal patterns for CRUD are not directly applied here.

By utilizing HTMX and Alpine.js, we maintain a clean separation between backend logic (Django views/models) and frontend interactivity, achieving a responsive experience without heavy JavaScript frameworks. The use of DataTables further enhances the usability of the tabular report preview.

## Final Notes

*   **Placeholder for `advice_list_previous`**: The `accounts:advice_list_previous` URL is a placeholder. In a complete application, this would point to a dedicated Django ListView for `AdvicePaymentMaster` records, from which users could select an advice to print.
*   **Session Management**: The ASP.NET code heavily relies on `Session["compid"]` and `Session["finyear"]`. In Django, these would typically be handled through robust user authentication, middleware, or by passing them as URL parameters if they change per request. For the generated code, we assume they are set in the session or default to ID `1`.
*   **Error Handling**: Basic `try-except` blocks are included. In a production system, comprehensive logging and user-friendly error pages would be implemented.
*   **Dependencies**: Ensure `weasyprint` is installed (`pip install WeasyPrint`) for PDF generation. You may also need to install its system dependencies (e.g., `libffi-dev`, `libxml2-dev`, `libxslt1-dev`, `pango1.0-dev` on Debian/Ubuntu).
*   **Data Consistency**: The logic for fetching `BillNo` (using `DS1.Tables[0].Rows[i][0]`) in the original ASP.NET code was somewhat ambiguous regarding the exact foreign key relationship. The Django model code makes an assumption (linking `AdvicePaymentDetail.id` to `BillBookingDetail.id`). This part should be carefully validated against your actual database schema during implementation to ensure correct data mapping.
*   **CSS**: Tailwind CSS is assumed to be configured in your `base.html` and project. The provided templates use Tailwind classes.