The following Django modernization plan comprehensively addresses the migration of your ASP.NET Service Tax Invoice printing module. It adheres to the 'Fat Model, Thin View' paradigm, leverages HTMX and Alpine.js for dynamic interfaces, and integrates DataTables for efficient data display.

---

## ASP.NET to Django Conversion Script: Service Tax Invoice - Print

This document outlines the modernization plan to transition the legacy ASP.NET 'Service Tax Invoice - Print' module to a modern Django-based solution. The focus is on leveraging Django 5.0+ best practices, HTMX for dynamic interactions, Alpine.js for UI state, and DataTables for efficient data presentation, all while adhering to the 'Fat Model, Thin View' architecture.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include `base.html` template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code interacts with several tables to display service tax invoice data.
- **`tblACC_ServiceTaxInvoice_Master`**: This is the primary table for invoices.
  - Columns: `Id` (PK), `FinYearId` (FK), `SysDate` (Date), `InvoiceNo` (String), `WONo` (String, comma-separated IDs), `PONo` (String), `CustomerCode` (FK), `CompId` (Integer).
- **`tblFinancial_master`**: Used to look up financial year names from `FinYearId`.
  - Columns: `FinYearId` (PK), `FinYear` (String).
- **`SD_Cust_master`**: Used to look up customer names and IDs from `CustomerCode`.
  - Columns: `CustomerId` (PK), `CustomerName` (String), `CompId` (Integer).
- **`SD_Cust_WorkOrder_Master`**: Used to look up work order numbers from `WONo` (comma-separated IDs).
  - Columns: `Id` (PK), `WONo` (String), `CompId` (Integer).

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Analysis:**
The provided ASP.NET code primarily performs **Read** operations.
- **Read (List & Search):** The page fetches and displays a list of `Service Tax Invoices`. It supports filtering by `Customer Name`, `PO No`, or `Invoice No`. Pagination is also supported.
- **No Create, Update, or Delete:** There are no explicit buttons or controls for adding, editing, or deleting invoices on this specific page. The "Select" action redirects to `ServiceTaxInvoice_Print_Details.aspx`, implying that further actions (like printing or viewing details) are handled on a different page. For this migration, we will focus solely on the listing and search functionality.

**Validation Logic:**
- The current page has no explicit input validation beyond the `AutoCompleteExtender` for customer name, which is more about suggestion than validation. The filtering logic handles empty search fields by not applying the filter.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, and infer their Django/HTMX/Alpine.js equivalents.

**Analysis:**
- **Search Filters:**
    - `DropDownList1` (Search by `Customer Name`, `PO No`, `Invoice No`): This will be a Django `forms.ChoiceField` rendered as a `<select>` tag. Its `onselectedindexchanged` `AutoPostBack` behavior will be replaced by HTMX `hx-trigger="change"` to dynamically show/hide the appropriate search input field.
    - `txtCustName` (Customer Name Search with AutoComplete): This will be an `<input type="text">` with HTMX `hx-get` to a Django view for autocomplete suggestions. Alpine.js will manage the display and selection of suggestions.
    - `txtpoNo` (PO No / Invoice No Search): A standard `<input type="text">`.
    - `btnSearch` (Search Button): A standard `<button>` with HTMX `hx-get` to trigger the table reload with search parameters.
- **Data Display:**
    - `GridView1` (Invoice List): This will be replaced by a `<table id="serviceTaxInvoiceTable">` initialized with DataTables for client-side pagination, sorting, and search. The table content will be loaded and updated via HTMX.
    - `asp:TemplateField` (SN, Id, Select, Print Type, FinYear, Invoice No, Date, Customer Name, WO No, PO No, CustomerId): These map directly to table columns.
        - "Select" `LinkButton`: Will be an HTML `<button>` with HTMX `hx-get` to redirect to or trigger a modal for the print details page. The `DrpPrintType` value will need to be passed along.
        - `DrpPrintType` (Print Type DropDownList per row): This will be a `<select>` element within the DataTables row. Its selected value will be captured using Alpine.js or by reading its value from the DOM when the "Select" button is clicked.
- **Styling:**
    - ASP.NET CSS links and inline styles will be replaced by Tailwind CSS for a modern, responsive design.

### Step 4: Generate Django Code

We will create a new Django app, named `accounts`, to house these components.

#### 4.1 Models

**Task:** Create Django models based on the identified database schema. We'll include helper methods for retrieving related data as per the C# logic (`bindgrid` function).

**Instructions:**
- Define `ServiceTaxInvoice`, `FinancialYear`, `Customer`, and `WorkOrder` models.
- Use `db_column` for fields that don't follow Django's naming conventions.
- Set `managed = False` and `db_table` in the `Meta` class to map to existing tables.
- Implement model methods for business logic, especially for the `WONo` parsing and `CustomerName` lookup from the C# `bindgrid` logic. This ensures a 'Fat Model' approach.

```python
# accounts/models.py
from django.db import models
from django.db.models import F, Value
from django.db.models.functions import Concat

class FinancialYear(models.Model):
    """
    Maps to tblFinancial_master for financial year details.
    """
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class Customer(models.Model):
    """
    Maps to SD_Cust_master for customer details.
    """
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId') # Company ID, assumes global context

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customer_name} [{self.customer_id}]"

class WorkOrder(models.Model):
    """
    Maps to SD_Cust_WorkOrder_Master for work order details.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    wo_no = models.CharField(db_column='WONo', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.wo_no

class ServiceTaxInvoice(models.Model):
    """
    Maps to tblACC_ServiceTaxInvoice_Master.
    Contains methods to replicate data processing from ASP.NET bindgrid.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    fin_year_id = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='invoices')
    sys_date = models.DateTimeField(db_column='SysDate')
    invoice_no = models.CharField(db_column='InvoiceNo', max_length=50)
    wo_no_raw = models.CharField(db_column='WONo', max_length=255, blank=True, null=True) # Storing original comma-sep string
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    customer_code = models.ForeignKey(Customer, on_delete=models.DO_NOTHING, db_column='CustomerCode', related_name='invoices_as_code') # This is actually CustomerId from SD_Cust_master
    comp_id = models.IntegerField(db_column='CompId') # Company ID

    class Meta:
        managed = False
        db_table = 'tblACC_ServiceTaxInvoice_Master'
        verbose_name = 'Service Tax Invoice'
        verbose_name_plural = 'Service Tax Invoices'
        ordering = ['-id'] # Replicate "Order By Id Desc" from ASP.NET

    def __str__(self):
        return f"Invoice {self.invoice_no} ({self.customer_code.customer_name})"

    @property
    def formatted_sys_date(self):
        """Replicates fun.FromDateDMY (DD/MM/YYYY) for date formatting."""
        return self.sys_date.strftime('%d/%m/%Y')

    @property
    def customer_full_name(self):
        """Replicates CustomerName [CustomerId] format from ASP.NET."""
        if self.customer_code:
            return f"{self.customer_code.customer_name} [{self.customer_code.customer_id}]"
        return "N/A"

    def get_work_order_numbers(self, comp_id):
        """
        Replicates the ASP.NET logic to split WONo_raw and fetch corresponding WorkOrder.wo_no.
        This is a fat model approach, keeping this complex data transformation here for efficiency.
        """
        if not self.wo_no_raw:
            return ""
        
        # Filter out empty strings from split
        wo_ids = [wo_id.strip() for wo_id in self.wo_no_raw.split(',') if wo_id.strip()]
        
        if not wo_ids:
            return ""
        
        # Fetch WorkOrder objects in one query, assuming comp_id context for WorkOrder as well
        work_orders = WorkOrder.objects.filter(
            id__in=wo_ids, 
            comp_id=comp_id  # Filter by current user's company ID
        ).values_list('wo_no', flat=True)
        
        return ",".join(work_orders)

    @classmethod
    def get_invoices_for_display(cls, current_comp_id, current_fin_year_id, search_params):
        """
        Replicates the main bindgrid logic for fetching and preparing data.
        This method returns a QuerySet, which is more efficient for Django.
        All display formatting is handled by properties on the ServiceTaxInvoice object itself.
        """
        invoices = cls.objects.filter(
            comp_id=current_comp_id, 
            fin_year_id__fin_year_id__lte=current_fin_year_id
        ).select_related('fin_year_id', 'customer_code') # Prefetch related objects for performance

        search_by = search_params.get('search_by')
        customer_id = search_params.get('customer_id')
        po_invoice_no_input = search_params.get('po_invoice_no_input')
        customer_name_input = search_params.get('customer_name_input') # For autocomplete suggestions

        if search_by == '0' and customer_id: # Search by Customer Name
            invoices = invoices.filter(customer_code__customer_id=customer_id)
        elif search_by == '2' and po_invoice_no_input: # Search by PO No
            invoices = invoices.filter(po_no=po_invoice_no_input)
        elif search_by == '3' and po_invoice_no_input: # Search by Invoice No
            invoices = invoices.filter(invoice_no=po_invoice_no_input)
        
        return invoices
```

#### 4.2 Forms

**Task:** Define a Django form for the search criteria. No ModelForm is needed as this is a search interface, not a CRUD form for `ServiceTaxInvoice`.

**Instructions:**
- Create a simple `Form` with fields for search type, customer name (with HTMX for autocomplete), and PO/Invoice number.
- Add `widgets` with Tailwind CSS classes for styling.
- Include a hidden field for `customer_id` to be populated by Alpine.js from autocomplete selections.

```python
# accounts/forms.py
from django import forms
from .models import Customer

class ServiceTaxInvoiceSearchForm(forms.Form):
    SEARCH_CHOICES = [
        ('0', 'Customer Name'),
        ('2', 'PO No'),
        ('3', 'Invoice No'),
    ]

    search_by = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        initial='0',
        widget=forms.Select(attrs={'class': 'box3 w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    customer_name_input = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter Customer Name',
            # HTMX for autocomplete suggestions
            'hx-get': '/accounts/customer-autocomplete/', 
            'hx-trigger': 'keyup changed delay:500ms from:event.target',
            'hx-target': '#customer-suggestions',
            'hx-indicator': '#customer-loading-indicator',
            'autocomplete': 'off',
            'x-on:click.outside': 'open = false', # Alpine.js for closing suggestions
            'x-on:keydown.escape': 'open = false'
        })
    )
    # This field acts as both PO No and Invoice No based on dropdown selection
    po_invoice_no_input = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter PO No / Invoice No',
            'style': 'display: none;' # Initially hidden, controlled by Alpine.js
        })
    )

    # Hidden field to store selected customer ID from autocomplete
    # This is populated by Alpine.js when a suggestion is clicked
    customer_id = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.HiddenInput()
    )

    def clean(self):
        cleaned_data = super().clean()
        # No explicit validation needed here as model method handles filtering based on presence.
        return cleaned_data
```

#### 4.3 Views

**Task:** Implement the listing and search functionality using Django Class-Based Views (CBVs). Also, create a view for HTMX-driven customer autocomplete.

**Instructions:**
- `ServiceTaxInvoiceListView`: Renders the main page with the search form and an empty container for the table.
- `ServiceTaxInvoiceTablePartialView`: Handles the search logic and renders only the table content, suitable for HTMX `hx-swap`.
- `CustomerAutocompleteView`: Provides customer suggestions for the autocomplete input via HTMX.
- Keep views thin (5-15 lines) by moving business logic to models.
- Simulate `Session["compid"]` and `Session["finyear"]` from ASP.NET for data filtering.

```python
# accounts/views.py
from django.views.generic import TemplateView, ListView
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from django.db.models import Q
from .models import ServiceTaxInvoice, Customer
from .forms import ServiceTaxInvoiceSearchForm

# Assume these are retrieved from user session/profile, simulating ASP.NET session variables
# For a real application, integrate with Django's authentication and user profiles.
DEFAULT_COMP_ID = 1 # Example: Replace with actual logic to get user's company ID
DEFAULT_FIN_YEAR_ID = 2023 # Example: Replace with actual logic to get user's financial year ID

class ServiceTaxInvoiceListView(TemplateView):
    """
    Renders the main Service Tax Invoice print page with search form.
    The invoice list table itself is loaded dynamically via HTMX.
    """
    template_name = 'accounts/servicetaxinvoice/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the search form
        context['form'] = ServiceTaxInvoiceSearchForm(self.request.GET or None)
        return context

class ServiceTaxInvoiceTablePartialView(ListView):
    """
    Renders only the Service Tax Invoice table content.
    Designed to be fetched via HTMX. Handles search and data retrieval.
    """
    model = ServiceTaxInvoice
    template_name = 'accounts/servicetaxinvoice/_servicetaxinvoice_table.html'
    context_object_name = 'servicetaxinvoices'

    def get_queryset(self):
        # Simulate retrieval of current user's company ID and financial year ID
        current_comp_id = self.request.session.get('compid', DEFAULT_COMP_ID)
        current_fin_year_id = self.request.session.get('finyear', DEFAULT_FIN_YEAR_ID)

        form = ServiceTaxInvoiceSearchForm(self.request.GET)
        search_params = {}
        if form.is_valid():
            search_params = form.cleaned_data

        # Use the fat model method to get filtered invoices
        queryset = ServiceTaxInvoice.get_invoices_for_display(
            current_comp_id,
            current_fin_year_id,
            search_params
        )

        # Apply specific logic for WONo processing, which is done per item in ASP.NET
        # This will add the processed WONo to each object for template display.
        for invoice in queryset:
            # Pass comp_id to get_work_order_numbers method as it's required for WO filtering
            invoice.display_wo_no = invoice.get_work_order_numbers(current_comp_id)

        return queryset

    def render_to_response(self, context, **response_kwargs):
        # HTMX will swap the target with this partial
        return super().render_to_response(context, **response_kwargs)

class CustomerAutocompleteView(ListView):
    """
    Provides customer suggestions for the autocomplete field.
    Returns an HTMX-compatible partial.
    """
    model = Customer
    template_name = 'accounts/servicetaxinvoice/_customer_suggestions.html'
    context_object_name = 'customers'

    def get_queryset(self):
        query = self.request.GET.get('customer_name_input', '')
        current_comp_id = self.request.session.get('compid', DEFAULT_COMP_ID) # Simulate CompId from session

        if query:
            # Filter customers by name (case-insensitive contains for broader match than startsWith)
            # and by the current company ID
            return Customer.objects.filter(
                Q(customer_name__icontains=query) | Q(customer_id__icontains=query),
                comp_id=current_comp_id
            )[:10] # Limit suggestions, similar to ASP.NET's implied limit
        return Customer.objects.none()

    def render_to_response(self, context, **response_kwargs):
        # For HTMX, we return the rendered HTML fragment
        return HttpResponse(render_to_string(self.template_name, context, self.request))

# Placeholder for the print details view, as per ASP.NET redirect
class ServiceTaxInvoicePrintDetailsView(TemplateView):
    template_name = 'accounts/servicetaxinvoice/print_details.html' # Create this template later
    def get(self, request, *args, **kwargs):
        # In a real app, retrieve invoice details and print type based on PK and PT
        invoice_id = kwargs.get('pk')
        print_type = request.GET.get('pt') # Assuming 'pt' is passed as query param
        # Logic to fetch invoice data and prepare for printing
        # For now, just render a simple page
        return super().get(request, *args, **kwargs)

```

#### 4.4 Templates

**Task:** Create templates for the list view, the table partial, the autocomplete suggestions, and the print details placeholder.

**Instructions:**
- List Template (`accounts/servicetaxinvoice/list.html`):
  - Extends `core/base.html`.
  - Includes the search form and a container for the DataTables content.
  - Uses HTMX to load the table and manage dynamic input visibility.
  - Alpine.js handles UI state for search input visibility and autocomplete dropdown.
- Table Partial Template (`accounts/servicetaxinvoice/_servicetaxinvoice_table.html`):
  - Contains the actual `<table>` structure, to be loaded by HTMX.
  - Initializes DataTables on `hx:afterSwap`.
  - Iterates through `servicetaxinvoices` to display data.
  - Includes HTMX buttons for "Select" action, passing invoice ID and selected print type.
- Autocomplete Suggestions Partial Template (`accounts/servicetaxinvoice/_customer_suggestions.html`):
  - Renders a `<ul>` of suggestions, populated by HTMX, handled by Alpine.js for selection.

**List Template:**

```html
{# accounts/templates/accounts/servicetaxinvoice/list.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 p-4 rounded-t-lg text-white font-bold mb-4">
        Service Tax Invoice - Print
    </div>

    {# Search Form Section #}
    <div x-data="{ 
        searchBy: '{{ form.search_by.value }}', 
        customerNameInput: true, 
        poInvoiceNoInput: false,
        open: false, # Alpine.js for autocomplete dropdown
        selectedCustomerId: '{{ form.customer_id.value|default:'' }}' # Initial customer ID
    }" x-init="
        $watch('searchBy', value => {
            customerNameInput = (value === '0');
            poInvoiceNoInput = (value === '2' || value === '3');
            if (value === '0') {
                $refs.poInvoiceNoInput.value = '';
                $refs.selectedCustomerId.value = ''; // Clear customer ID if switching to PO/Invoice
            } else {
                $refs.customerNameInput.value = ''; // Clear customer name
                $refs.selectedCustomerId.value = ''; // Clear customer ID
            }
        });
        customerNameInput = ('{{ form.search_by.value }}' === '0');
        poInvoiceNoInput = ('{{ form.search_by.value }}' === '2' || '{{ form.search_by.value }}' === '3');
    " class="bg-white p-6 rounded-lg shadow-md mb-6">
        <form hx-get="{% url 'servicetaxinvoice_table_partial' %}" hx-target="#servicetaxinvoice-table-container" hx-swap="innerHTML" class="space-y-4">
            {% csrf_token %}
            <div class="flex items-center space-x-4">
                <div class="w-1/3">
                    <label for="{{ form.search_by.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By</label>
                    {{ form.search_by }}
                </div>
                <div class="w-2/3 relative" x-show="customerNameInput">
                    <label for="{{ form.customer_name_input.id_for_label }}" class="block text-sm font-medium text-gray-700">Customer Name</label>
                    <input type="text" name="{{ form.customer_name_input.name }}" id="{{ form.customer_name_input.id_for_label }}" 
                           value="{{ form.customer_name_input.value|default:'' }}"
                           class="box3 w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                           placeholder="Enter Customer Name"
                           hx-get="{% url 'customer_autocomplete' %}" 
                           hx-trigger="keyup changed delay:500ms from:event.target" 
                           hx-target="#customer-suggestions" 
                           hx-swap="innerHTML"
                           hx-indicator="#customer-loading-indicator"
                           autocomplete="off"
                           x-ref="customerNameInput"
                           @focus="open = true"
                           @click.away="open = false"
                           @keydown.escape="open = false"
                           x-model="customerNameInputText">
                    <input type="hidden" name="{{ form.customer_id.name }}" id="{{ form.customer_id.id_for_label }}" 
                           x-ref="selectedCustomerId" x-model="selectedCustomerId">
                    <div id="customer-suggestions" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1" 
                         x-show="open && customerNameInputText.length > 0" x-cloak>
                        {# Autocomplete suggestions will be loaded here via HTMX #}
                    </div>
                    <div id="customer-loading-indicator" class="htmx-indicator ml-2">
                        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                    </div>
                </div>
                <div class="w-2/3" x-show="poInvoiceNoInput">
                    <label for="{{ form.po_invoice_no_input.id_for_label }}" class="block text-sm font-medium text-gray-700" 
                           x-text="searchBy === '2' ? 'PO No' : 'Invoice No'"></label>
                    <input type="text" name="{{ form.po_invoice_no_input.name }}" id="{{ form.po_invoice_no_input.id_for_label }}" 
                           value="{{ form.po_invoice_no_input.value|default:'' }}"
                           class="box3 w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                           placeholder="Enter PO No / Invoice No"
                           x-ref="poInvoiceNoInput">
                </div>
                <div>
                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mt-6">
                        Search
                    </button>
                </div>
            </div>
        </form>
    </div>
    
    {# Table Container for HTMX load #}
    <div id="servicetaxinvoice-table-container"
         hx-trigger="load, refreshServiceTaxInvoiceList from:body"
         hx-get="{% url 'servicetaxinvoice_table_partial' %}"
         hx-swap="innerHTML"
         hx-indicator="#table-loading-indicator"
         hx-push-url="true"> {# Push URL with current search params #}
        {# Loading indicator for initial load and subsequent searches #}
        <div id="table-loading-indicator" class="htmx-indicator text-center p-8">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Invoices...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('invoicePrintForm', () => ({
            searchBy: '{{ form.search_by.value }}',
            customerNameInput: true,
            poInvoiceNoInput: false,
            open: false,
            customerNameInputText: '{{ form.customer_name_input.value|default:'' }}',
            selectedCustomerId: '{{ form.customer_id.value|default:'' }}',
            
            init() {
                this.$watch('searchBy', value => {
                    this.customerNameInput = (value === '0');
                    this.poInvoiceNoInput = (value === '2' || value === '3');
                    if (value === '0') {
                        this.$refs.poInvoiceNoInput.value = '';
                    } else {
                        this.$refs.customerNameInput.value = '';
                        this.customerNameInputText = '';
                        this.selectedCustomerId = '';
                    }
                });
                this.customerNameInput = (this.searchBy === '0');
                this.poInvoiceNoInput = (this.searchBy === '2' || this.searchBy === '3');
            },
            selectCustomer(name, id) {
                this.customerNameInputText = name;
                this.selectedCustomerId = id;
                this.open = false; // Close suggestions after selection
            }
        }));
    });

    // Ensure DataTables reinitializes after HTMX swap
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'servicetaxinvoice-table-container') {
            $('#serviceTaxInvoiceTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "dom": '<"flex justify-between items-center mb-4"lf>rtip' // DataTables layout with Tailwind
            });
        }
    });
</script>
{% endblock %}
```

**Table Partial Template (`_servicetaxinvoice_table.html`):**

```html
{# accounts/templates/accounts/servicetaxinvoice/_servicetaxinvoice_table.html #}
{% comment %}
    This partial template is loaded by HTMX to display the service tax invoice table.
    It expects 'servicetaxinvoices' in context.
{% endcomment %}
<div class="overflow-x-auto bg-white rounded-lg shadow-md p-4">
    <table id="serviceTaxInvoiceTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Print Type</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">FinYear</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice No</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if servicetaxinvoices %}
                {% for invoice in servicetaxinvoices %}
                <tr x-data="{ printType: 'ORIGINAL FOR BUYER' }"> {# Alpine.js to manage print type per row #}
                    <td class="py-2 px-4 whitespace-nowrap text-right">{{ forloop.counter }}</td>
                    <td class="py-2 px-4">
                        <select x-model="printType" class="box3 w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <option value="ORIGINAL FOR BUYER">ORIGINAL FOR BUYER</option>
                            <option value="DUPLICATE FOR TRANSPORTER">DUPLICATE FOR TRANSPORTER</option>
                            <option value="TRIPLICATE FOR ASSESSEE">TRIPLICATE FOR ASSESSEE</option>
                            <option value="EXTRA COPY">EXTRA COPY [NOT FOR CENVAT PURPOSE]</option>
                        </select>
                    </td>
                    <td class="py-2 px-4 whitespace-nowrap text-center">{{ invoice.fin_year_id.fin_year }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-center">{{ invoice.invoice_no }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-center">{{ invoice.formatted_sys_date }}</td>
                    <td class="py-2 px-4">{{ invoice.customer_full_name }}</td>
                    <td class="py-2 px-4">{{ invoice.display_wo_no }}</td> {# Use processed WO number #}
                    <td class="py-2 px-4 whitespace-nowrap text-center">{{ invoice.po_no|default:"-" }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-center">
                        <a href="{% url 'servicetaxinvoice_print_details' pk=invoice.pk %}?pt={{ printType }}" 
                           class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-3 rounded text-sm">
                            Select
                        </a>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="9" class="py-4 px-4 text-center text-lg font-semibold text-red-700">
                        No data to display !
                    </td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>
```

**Autocomplete Suggestions Partial Template (`_customer_suggestions.html`):**

```html
{# accounts/templates/accounts/servicetaxinvoice/_customer_suggestions.html #}
{% comment %}
    This partial template is loaded by HTMX for customer autocomplete.
    It expects 'customers' in context.
{% endcomment %}
{% if customers %}
    <ul class="divide-y divide-gray-100">
        {% for customer in customers %}
            <li class="p-2 cursor-pointer hover:bg-gray-100" 
                x-on:click="selectCustomer('{{ customer.customer_name }}', '{{ customer.customer_id }}');">
                {{ customer.customer_name }} [{{ customer.customer_id }}]
            </li>
        {% endfor %}
    </ul>
{% else %}
    <p class="p-2 text-gray-500 text-sm">No suggestions found.</p>
{% endif %}
```

**Print Details Placeholder Template (`print_details.html`):**

```html
{# accounts/templates/accounts/servicetaxinvoice/print_details.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6">Service Tax Invoice Print Details</h2>
    <div class="bg-white p-6 rounded-lg shadow-md">
        <p class="text-lg">This page would display the detailed invoice for printing.</p>
        <p>Invoice ID: <span class="font-semibold">{{ view.kwargs.pk }}</span></p>
        <p>Print Type: <span class="font-semibold">{{ request.GET.pt|default:"N/A" }}</span></p>
        <p class="mt-4 text-gray-600">
            (Implementation for actual invoice rendering and print functionality would go here, 
            fetching all necessary details from the database.)
        </p>
        <a href="{% url 'servicetaxinvoice_list' %}" class="mt-6 inline-block bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Back to Invoice List
        </a>
    </div>
</div>
{% endblock %}
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
- Create paths for the main list view, the table partial, the customer autocomplete endpoint, and the print details placeholder.
- Use appropriate naming patterns and consistent URL structure.

```python
# accounts/urls.py
from django.urls import path
from .views import (
    ServiceTaxInvoiceListView, 
    ServiceTaxInvoiceTablePartialView, 
    CustomerAutocompleteView,
    ServiceTaxInvoicePrintDetailsView,
)

urlpatterns = [
    # Main list page for Service Tax Invoices with search
    path('servicetaxinvoice/', ServiceTaxInvoiceListView.as_view(), name='servicetaxinvoice_list'),
    
    # HTMX endpoint to load/reload the invoice table partial
    path('servicetaxinvoice/table/', ServiceTaxInvoiceTablePartialView.as_view(), name='servicetaxinvoice_table_partial'),

    # HTMX endpoint for customer name autocomplete suggestions
    path('customer-autocomplete/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),

    # Placeholder for the Service Tax Invoice print details page
    path('servicetaxinvoice/print-details/<int:pk>/', ServiceTaxInvoicePrintDetailsView.as_view(), name='servicetaxinvoice_print_details'),
]
```

#### 4.6 Tests

**Task:** Write comprehensive tests for the models and views to ensure functionality and data integrity.

**Instructions:**
- Include unit tests for model methods and properties.
- Add integration tests for all views (list, table partial, autocomplete) covering various search scenarios.
- Simulate session data (`compid`, `finyear`) for testing view logic.
- Ensure at least 80% test coverage of code.

```python
# accounts/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from datetime import datetime
from unittest.mock import patch

from .models import ServiceTaxInvoice, FinancialYear, Customer, WorkOrder
from .forms import ServiceTaxInvoiceSearchForm
from .views import DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID

class ServiceTaxInvoiceModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Set up shared test data
        cls.fin_year_2022 = FinancialYear.objects.create(fin_year_id=2022, fin_year='2022-23')
        cls.fin_year_2023 = FinancialYear.objects.create(fin_year_id=2023, fin_year='2023-24')
        cls.customer1 = Customer.objects.create(customer_id='CUST001', customer_name='Alpha Corp', comp_id=DEFAULT_COMP_ID)
        cls.customer2 = Customer.objects.create(customer_id='CUST002', customer_name='Beta Industries', comp_id=DEFAULT_COMP_ID)
        cls.work_order1 = WorkOrder.objects.create(id=101, wo_no='WO/2023/001', comp_id=DEFAULT_COMP_ID)
        cls.work_order2 = WorkOrder.objects.create(id=102, wo_no='WO/2023/002', comp_id=DEFAULT_COMP_ID)

        cls.invoice1 = ServiceTaxInvoice.objects.create(
            id=1,
            fin_year_id=cls.fin_year_2023,
            sys_date=datetime(2023, 10, 26),
            invoice_no='INV/001/23',
            wo_no_raw='101,102,', # C# code had trailing comma
            po_no='PO/ABC/001',
            customer_code=cls.customer1,
            comp_id=DEFAULT_COMP_ID
        )
        cls.invoice2 = ServiceTaxInvoice.objects.create(
            id=2,
            fin_year_id=cls.fin_year_2022,
            sys_date=datetime(2022, 11, 15),
            invoice_no='INV/002/22',
            wo_no_raw='101,',
            po_no='PO/XYZ/002',
            customer_code=cls.customer2,
            comp_id=DEFAULT_COMP_ID
        )
        cls.invoice3 = ServiceTaxInvoice.objects.create(
            id=3,
            fin_year_id=cls.fin_year_2023,
            sys_date=datetime(2023, 1, 10),
            invoice_no='INV/003/23',
            wo_no_raw='',
            po_no=None,
            customer_code=cls.customer1,
            comp_id=DEFAULT_COMP_ID
        )
        # Invoice for a different company ID
        cls.invoice_other_comp = ServiceTaxInvoice.objects.create(
            id=4,
            fin_year_id=cls.fin_year_2023,
            sys_date=datetime(2023, 12, 1),
            invoice_no='INV/004/23',
            wo_no_raw='',
            po_no='PO/DIFF/001',
            customer_code=cls.customer1, # Customer is same, but invoice belongs to other comp
            comp_id=DEFAULT_COMP_ID + 1 
        )

    def test_invoice_creation(self):
        self.assertEqual(self.invoice1.invoice_no, 'INV/001/23')
        self.assertEqual(self.invoice1.customer_code, self.customer1)
        self.assertEqual(self.invoice2.fin_year_id.fin_year, '2022-23')

    def test_formatted_sys_date_property(self):
        self.assertEqual(self.invoice1.formatted_sys_date, '26/10/2023')
        self.assertEqual(self.invoice2.formatted_sys_date, '15/11/2022')

    def test_customer_full_name_property(self):
        self.assertEqual(self.invoice1.customer_full_name, 'Alpha Corp [CUST001]')
        self.assertEqual(self.invoice2.customer_full_name, 'Beta Industries [CUST002]')

    def test_get_work_order_numbers_method(self):
        self.assertEqual(self.invoice1.get_work_order_numbers(DEFAULT_COMP_ID), 'WO/2023/001,WO/2023/002')
        self.assertEqual(self.invoice2.get_work_order_numbers(DEFAULT_COMP_ID), 'WO/2023/001')
        self.assertEqual(self.invoice3.get_work_order_numbers(DEFAULT_COMP_ID), '')

    def test_get_invoices_for_display_base_query(self):
        # Test base query filtering by comp_id and fin_year_id (lte)
        invoices = ServiceTaxInvoice.get_invoices_for_display(
            DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID, {}
        )
        self.assertIn(self.invoice1, invoices)
        self.assertIn(self.invoice2, invoices) # fin_year_id 2022 <= 2023
        self.assertIn(self.invoice3, invoices)
        self.assertNotIn(self.invoice_other_comp, invoices) # Different comp_id
        self.assertEqual(len(invoices), 3)

    def test_get_invoices_for_display_customer_filter(self):
        # Test customer filter
        search_params = {'search_by': '0', 'customer_id': 'CUST001'}
        invoices = ServiceTaxInvoice.get_invoices_for_display(
            DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID, search_params
        )
        self.assertIn(self.invoice1, invoices)
        self.assertIn(self.invoice3, invoices)
        self.assertNotIn(self.invoice2, invoices)
        self.assertEqual(len(invoices), 2)

    def test_get_invoices_for_display_po_no_filter(self):
        # Test PO No filter
        search_params = {'search_by': '2', 'po_invoice_no_input': 'PO/ABC/001'}
        invoices = ServiceTaxInvoice.get_invoices_for_display(
            DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID, search_params
        )
        self.assertIn(self.invoice1, invoices)
        self.assertNotIn(self.invoice2, invoices)
        self.assertNotIn(self.invoice3, invoices)
        self.assertEqual(len(invoices), 1)

    def test_get_invoices_for_display_invoice_no_filter(self):
        # Test Invoice No filter
        search_params = {'search_by': '3', 'po_invoice_no_input': 'INV/002/22'}
        invoices = ServiceTaxInvoice.get_invoices_for_display(
            DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID, search_params
        )
        self.assertIn(self.invoice2, invoices)
        self.assertNotIn(self.invoice1, invoices)
        self.assertNotIn(self.invoice3, invoices)
        self.assertEqual(len(invoices), 1)

    def test_get_invoices_for_display_no_filter_with_empty_input(self):
        # If search input is empty, no filter should be applied (ASP.NET behavior)
        search_params = {'search_by': '0', 'customer_id': ''}
        invoices = ServiceTaxInvoice.get_invoices_for_display(
            DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID, search_params
        )
        self.assertEqual(len(invoices), 3) # All invoices for default comp/fin year

        search_params = {'search_by': '2', 'po_invoice_no_input': ''}
        invoices = ServiceTaxInvoice.get_invoices_for_display(
            DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID, search_params
        )
        self.assertEqual(len(invoices), 3)

class ServiceTaxInvoiceViewsTest(TestCase):
    client = Client()

    @classmethod
    def setUpTestData(cls):
        # Set up shared test data, ensure IDs for sorting
        cls.fin_year_2023 = FinancialYear.objects.create(fin_year_id=2023, fin_year='2023-24')
        cls.customer1 = Customer.objects.create(customer_id='CUST001', customer_name='Alpha Corp', comp_id=DEFAULT_COMP_ID)
        cls.customer2 = Customer.objects.create(customer_id='CUST002', customer_name='Beta Industries', comp_id=DEFAULT_COMP_ID)
        cls.work_order1 = WorkOrder.objects.create(id=101, wo_no='WO/2023/001', comp_id=DEFAULT_COMP_ID)

        cls.invoice1 = ServiceTaxInvoice.objects.create(
            id=10, # Using higher ID to test ordering
            fin_year_id=cls.fin_year_2023,
            sys_date=datetime(2023, 10, 26),
            invoice_no='INV/001/23',
            wo_no_raw='101,',
            po_no='PO/ABC/001',
            customer_code=cls.customer1,
            comp_id=DEFAULT_COMP_ID
        )
        cls.invoice2 = ServiceTaxInvoice.objects.create(
            id=20,
            fin_year_id=cls.fin_year_2023,
            sys_date=datetime(2023, 11, 15),
            invoice_no='INV/002/23',
            wo_no_raw='',
            po_no='PO/XYZ/002',
            customer_code=cls.customer2,
            comp_id=DEFAULT_COMP_ID
        )

    def setUp(self):
        # Set session data for each test to simulate logged-in user context
        session = self.client.session
        session['compid'] = DEFAULT_COMP_ID
        session['finyear'] = DEFAULT_FIN_YEAR_ID
        session.save()

    def test_list_view_get(self):
        response = self.client.get(reverse('servicetaxinvoice_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/servicetaxinvoice/list.html')
        self.assertIn('form', response.context)
        self.assertIsInstance(response.context['form'], ServiceTaxInvoiceSearchForm)
        
        # Initial load should have the table container but not the actual table data yet
        self.assertContains(response, '<div id="servicetaxinvoice-table-container"')
        self.assertContains(response, 'Loading Invoices...')

    def test_table_partial_view_get_no_params(self):
        response = self.client.get(reverse('servicetaxinvoice_table_partial'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/servicetaxinvoice/_servicetaxinvoice_table.html')
        self.assertIn('servicetaxinvoices', response.context)
        self.assertEqual(len(response.context['servicetaxinvoices']), 2)
        # Check if invoices are ordered by ID Desc
        self.assertEqual(response.context['servicetaxinvoices'][0].id, self.invoice2.id)
        self.assertEqual(response.context['servicetaxinvoices'][1].id, self.invoice1.id)
        self.assertContains(response, self.invoice1.invoice_no)
        self.assertContains(response, self.invoice2.invoice_no)

    def test_table_partial_view_get_customer_search(self):
        # Search by customer name (implicitly by customer_id after autocomplete)
        response = self.client.get(
            reverse('servicetaxinvoice_table_partial'),
            {'search_by': '0', 'customer_id': 'CUST001', 'customer_name_input': 'Alpha Corp'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/servicetaxinvoice/_servicetaxinvoice_table.html')
        self.assertIn('servicetaxinvoices', response.context)
        self.assertEqual(len(response.context['servicetaxinvoices']), 1)
        self.assertEqual(response.context['servicetaxinvoices'].first(), self.invoice1)
        self.assertContains(response, self.invoice1.invoice_no)
        self.assertNotContains(response, self.invoice2.invoice_no)

    def test_table_partial_view_get_po_no_search(self):
        response = self.client.get(
            reverse('servicetaxinvoice_table_partial'),
            {'search_by': '2', 'po_invoice_no_input': 'PO/XYZ/002'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['servicetaxinvoices']), 1)
        self.assertEqual(response.context['servicetaxinvoices'].first(), self.invoice2)
        self.assertContains(response, self.invoice2.invoice_no)
        self.assertNotContains(response, self.invoice1.invoice_no)

    def test_table_partial_view_get_invoice_no_search(self):
        response = self.client.get(
            reverse('servicetaxinvoice_table_partial'),
            {'search_by': '3', 'po_invoice_no_input': 'INV/001/23'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['servicetaxinvoices']), 1)
        self.assertEqual(response.context['servicetaxinvoices'].first(), self.invoice1)
        self.assertContains(response, self.invoice1.invoice_no)
        self.assertNotContains(response, self.invoice2.invoice_no)

    def test_customer_autocomplete_view_get(self):
        # Test with a query
        response = self.client.get(
            reverse('customer_autocomplete'),
            {'customer_name_input': 'alpha'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/servicetaxinvoice/_customer_suggestions.html')
        self.assertContains(response, 'Alpha Corp [CUST001]')
        self.assertNotContains(response, 'Beta Industries [CUST002]')

        # Test with no query
        response = self.client.get(
            reverse('customer_autocomplete'),
            {'customer_name_input': ''},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No suggestions found.')

        # Test with a non-matching query
        response = self.client.get(
            reverse('customer_autocomplete'),
            {'customer_name_input': 'xyz'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No suggestions found.')

    def test_print_details_view(self):
        invoice_pk = self.invoice1.pk
        print_type = 'ORIGINAL FOR BUYER'
        response = self.client.get(
            reverse('servicetaxinvoice_print_details', kwargs={'pk': invoice_pk}),
            {'pt': print_type}
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/servicetaxinvoice/print_details.html')
        self.assertContains(response, f"Invoice ID: <span class=\"font-semibold\">{invoice_pk}</span>")
        self.assertContains(response, f"Print Type: <span class=\"font-semibold\">{print_type}</span>")

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
- **HTMX:**
  - `servicetaxinvoice-table-container` uses `hx-get` to `{% url 'servicetaxinvoice_table_partial' %}` on `load` and `refreshServiceTaxInvoiceList` trigger.
  - The search form uses `hx-get` to the same table partial endpoint to update the list dynamically.
  - The customer name input uses `hx-get` to `{% url 'customer_autocomplete' %}` for live suggestions.
  - The "Select" button directly links to the `servicetaxinvoice_print_details` URL, passing the `pk` and `printType` as query parameters.
  - HTMX `hx-indicator` is used to show loading spinners during asynchronous requests.
- **Alpine.js:**
  - `x-data` on the search form manages the `searchBy` dropdown state and dynamically shows/hides `customerNameInput` and `poInvoiceNoInput` fields using `x-show`.
  - Alpine.js handles the visibility of the autocomplete suggestions (`open` flag) and updates the `customerNameInputText` and `selectedCustomerId` on suggestion click.
  - Each row in the `_servicetaxinvoice_table.html` uses `x-data` to manage its own `printType` state for the dropdown.
- **DataTables:**
  - The JavaScript for DataTables initialization is placed directly within the `_servicetaxinvoice_table.html` partial.
  - This ensures that DataTables is re-initialized every time the table content is swapped by HTMX, which is crucial for client-side sorting, searching, and pagination to work correctly with dynamically loaded data.
  - `dom` attribute is adjusted for Tailwind CSS compatibility.

**Example `core/base.html` considerations (NOT to be included in output, but assumed for context):**
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}AutoERP{% endblock %}</title>
    {# Tailwind CSS via CDN or compiled build #}
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    {# HTMX CDN #}
    <script src="https://unpkg.com/htmx.org@1.9.10" integrity="sha384-D1Kt99CQMDuVetoL1lrYwg5t+9QdFVNHpFVYPNFXc//HDgKtReQa/N+agwGx1ac" crossorigin="anonymous"></script>
    {# Alpine.js CDN #}
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    {# jQuery for DataTables #}
    <script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    {# DataTables CSS #}
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/2.0.7/css/dataTables.dataTables.min.css">
    {# DataTables JS #}
    <script type="text/javascript" src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
    {# HTMX attributes for indicators #}
    <style>
        .htmx-indicator {
            display: none;
        }
        .htmx-request .htmx-indicator {
            display: inline-block; /* Or block, flex, etc. */
        }
        .htmx-request.htmx-indicator {
            display: inline-block;
        }
    </style>
</head>
<body class="bg-gray-100 font-sans leading-normal tracking-normal">
    <div class="min-h-screen flex flex-col">
        {% include 'core/navbar.html' %} {# Example of including a navbar #}
        <main class="flex-grow">
            {% block content %}{% endblock %}
        </main>
        {% include 'core/footer.html' %} {# Example of including a footer #}
    </div>
    {% block extra_js %}{% endblock %}
</body>
</html>
```

### Final Notes

- **Modularity:** The solution is designed to be modular. Each component (model, form, view, template) serves a specific purpose, adhering to the principle of separation of concerns.
- **Maintainability:** By using Django's ORM, CBVs, and external libraries like HTMX, Alpine.js, and DataTables, the codebase becomes significantly more maintainable and scalable than the original ASP.NET implementation.
- **Performance:** Leveraging HTMX reduces full-page reloads, providing a snappier user experience. DataTables handles client-side data manipulation efficiently.
- **Security:** Django's built-in security features (like CSRF protection) are used automatically. Passing `pk` directly is more secure than encrypting and decrypting URL parameters manually.
- **Extensibility:** The fat model approach centralizes business logic, making it easier to extend or modify data-related behaviors. New features can be added by creating new views and templates, reusing existing models and forms.
- **Business Value:** This modernized solution offers improved user experience, faster development cycles due to Django's conventions, and a more robust and scalable architecture, all contributing to reduced operational costs and increased business agility.