## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Analysis of ASP.NET Application

The provided ASP.NET page `SundryCreditors_InDetailView.aspx` and its code-behind `SundryCreditors_InDetailView.aspx.cs` are primarily responsible for generating and displaying a detailed sundry creditors report using Crystal Reports. This is not a standard CRUD (<PERSON>reate, Read, Update, Delete) page for a single database table. Instead, it aggregates data from numerous related tables, performs complex calculations (including opening balances, total booked bills, bank payments, and cash payments), and then presents this consolidated information in a structured report format.

### Step 1: Extract Database Schema

**Task:** Identify the database tables involved and the structure of the *report output* (which acts as a conceptual schema for the Django report).

**Instructions:**

The ASP.NET code executes complex SQL queries involving numerous tables and performs in-memory LINQ aggregations to produce a final `DataTable` (`dt1`) that is then fed to Crystal Reports. This `dt1` represents the *schema of the report's output data*.

**Primary Tables Involved in Report Data Collection (Managed=False Models will be defined for these):**

*   **`tblACC_Creditors_Master`**: Stores creditor opening balances.
    *   Key fields: `SupplierId`, `CompId`, `OpeningAmt`
*   **`tblACC_BillBooking_Master`**: Master records for bill bookings.
    *   Key fields: `Id`, `CompId`, `FinYearId`, `SupplierId`, `SysDate`, `Discount`, `DiscountType`, `DebitAmt`, `OtherCharges`, `PVEVNo`, `BillNo`, `BillDate`, `AHId`
*   **`tblACC_BillBooking_Details`**: Details for bill bookings.
    *   Key fields: `MId` (Master ID), `GQNId` (Quality Note ID), `GSNId` (Service Note ID), `PODId` (PO Detail ID), `PFAmt`, `ExStBasic`, `ExStEducess`, `ExStShecess`, `VAT`, `CST`, `Freight`, `BCDValue`, `EdCessOnCDValue`, `SHEDCessValue`
*   **`tblMM_PO_Details`**: Purchase Order details.
    *   Key fields: `Id`, `MId` (PO Master ID), `PRId` (PR ID), `SPRId` (SPR ID), `Rate`, `Discount`, `VAT` (likely VAT ID)
*   **`tblQc_MaterialQuality_Details`**: Material quality acceptance details.
    *   Key fields: `Id`, `AcceptedQty`
*   **`tblinv_MaterialServiceNote_Details`**: Material/Service Note details.
    *   Key fields: `Id`, `ReceivedQty`
*   **`tblACC_BankVoucher_Payment_Master`**: Master records for bank payments.
    *   Key fields: `Id`, `CompId`, `FinYearId`, `SysDate`, `BVPNo`, `Bank`, `ChequeNo`, `ChequeDate`, `PayTo`, `Type`, `PayAmt`
*   **`tblACC_BankVoucher_Payment_Details`**: Details for bank payments.
    *   Key fields: `MId`, `Amount`, `PVEVNO` (Bill Booking ID)
*   **`tblACC_CashVoucher_Payment_Master`**: Master records for cash payments.
    *   Key fields: `Id`, `CompId`, `FinYearId`, `SysDate`, `CVPNo`, `ReceivedBy`
*   **`tblACC_CashVoucher_Payment_Details`**: Details for cash payments.
    *   Key fields: `MId`, `Amount`, `AcHead`, `Particulars`
*   **`tblMM_Supplier_master`**: Supplier master data.
    *   Key fields: `SupplierId`, `SupplierName`
*   **`tblCompany_master`**: Company master data.
    *   Key fields: `CompId`, `RegdAddress`, `RegdCity`, `RegdState`, `RegdCountry`, `RegdPinCode`, `RegdContactNo`, `RegdFaxNo`, `RegdEmail`
*   **`tblVAT_Master`**: VAT master for terms.
    *   Key fields: `Id`, `Terms`
*   **`AccHead`**: Account Head master, used for categorization.
    *   Key fields: `Id`, `Category`
*   **`tblACC_Bank`**: Bank master data.
    *   Key fields: `Id`, `Name`

**Report Output Schema (Conceptual Django Model: `CreditorDetailReportItem`):**

This represents the structure of the `dt1` DataTable generated in the C# code, which is the final dataset for the report.

*   `VchDate` (string, formatted date e.g., "DD/MM/YYYY")
*   `CompId` (int)
*   `VchNo` (string, e.g., PVEVNo, BVPNo, CVPNo)
*   `VchType` (string, e.g., "Purchase", "Payment", "Cash Payment")
*   `Particulars` (string, derived from various sources)
*   `Credit` (double)
*   `Debit` (double)
*   `OtherCharges` (double)
*   `VchLinkData` (string, URL for detailed view)
*   `DTSort` (DateTime, original date for sorting)
*   `BillNo` (string, specific to Purchase Vouchers)
*   `BillDate` (string, formatted date for Purchase Vouchers)

### Step 2: Identify Backend Functionality

**Task:** Determine the purpose and operations of the ASP.NET code.

**Instructions:**

The ASP.NET page is purely a **Read/Report** operation. It does not perform any Create, Update, or Delete operations on the underlying data.

*   **Read (Report Generation):**
    *   Collects input parameters from Session (`finyear`, `compid`, `username`, `SupId`, `Key`, `DtFrm`, `DtTo`) and QueryString (`lnkFor` - `Category`).
    *   Calculates an "Opening Balance" for the supplier based on `tblACC_Creditors_Master` and historical `tblACC_BillBooking` entries before the `FromDate`.
    *   Fetches "Bill Booking" data, performing complex joins and conditional calculations based on `GQNId`, `GSNId`, `PRId`, `SPRId`, and various charge fields (`PFAmt`, `ExStBasic`, `VAT`, `CST`, etc.). It also applies discounts.
    *   Fetches "Bank Payment" data for the supplier.
    *   Fetches "Cash Payment" data for the supplier.
    *   Combines all this data into a single, sorted dataset.
    *   Retrieves company and supplier details to set report parameters.
    *   Passes the combined dataset and parameters to a Crystal Report for rendering.

**Validation Logic:**
*   Date range validation (`Convert.ToDateTime(FromDate) <= Convert.ToDateTime(ToDate)`).

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, and map them to Django/HTMX/Alpine.js.

**Instructions:**

The original ASP.NET page uses a `CrystalReportViewer` to display the report. There are no direct input controls visible in the `.aspx` snippet, implying that input parameters are either passed via URL (QueryString) or set in the user's session before this page loads.

In Django, this will translate to:

*   **Report Parameter Form:** A simple HTML form (or a Django Form) with input fields for `Supplier ID`, `From Date`, `To Date`, and `Category`. This form will use HTMX to submit parameters.
*   **Report Display Area:** A `div` that will be targeted by HTMX to load the report data (a DataTables-powered HTML table) after the parameters are submitted.
*   **Modals:** While not explicitly used in the ASP.NET UI for this view, the general modernization plan requires modals for CRUD operations. For a report view, a modal might be used for date pickers or advanced filter options, though not strictly necessary for this direct translation.
*   **Data Presentation:** The complex report data will be displayed using DataTables for interactive filtering, sorting, and pagination.

### Step 4: Generate Django Code

We will create a Django application named `sundrycreditors_report`.

#### 4.1 Models (`sundrycreditors_report/models.py`)

Given that the original application interacts with an existing database, we will define Django models for the key tables involved in the report generation, marking them with `managed = False` and `db_table` to align with the existing schema.

Additionally, we'll define a conceptual `CreditorDetailReportItem` model that represents the structure of the *output* data from the report, even though it's not a direct database table. This provides a clear schema for the data generated by our report logic.

```python
from django.db import models
from django.db.models.functions import Cast
from django.db.models import Sum, F, ExpressionWrapper, fields, Q, Value
from django.db.models.lookups import GreaterThanOrEqual, LessThanOrEqual
from datetime import datetime

# --- Models for existing database tables (managed=False) ---

class CreditorMaster(models.Model):
    # Assuming primary key is 'SupplierId' as used in queries
    supplier_id = models.CharField(db_column='SupplierId', max_length=50, primary_key=True)
    comp_id = models.IntegerField(db_column='CompId')
    opening_amt = models.DecimalField(db_column='OpeningAmt', max_digits=18, decimal_places=2, default=0)

    class Meta:
        managed = False
        db_table = 'tblACC_Creditors_Master'
        verbose_name = 'Creditor Master'
        verbose_name_plural = 'Creditor Masters'

    def __str__(self):
        return f"Creditor: {self.supplier_id}"

class BillBookingMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # PVEVId in ASP.NET
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    supplier_id = models.CharField(db_column='SupplierId', max_length=50)
    sys_date = models.DateTimeField(db_column='SysDate')
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=2, default=0)
    discount_type = models.IntegerField(db_column='DiscountType', default=0) # 0 for amount, 1 for percentage
    debit_amt = models.DecimalField(db_column='DebitAmt', max_digits=18, decimal_places=2, default=0)
    other_charges = models.DecimalField(db_column='OtherCharges', max_digits=18, decimal_places=2, default=0)
    pvev_no = models.CharField(db_column='PVEVNo', max_length=50)
    bill_no = models.CharField(db_column='BillNo', max_length=50, null=True, blank=True)
    bill_date = models.DateTimeField(db_column='BillDate', null=True, blank=True)
    ah_id = models.IntegerField(db_column='AHId', null=True) # Account Head ID

    class Meta:
        managed = False
        db_table = 'tblACC_BillBooking_Master'
        verbose_name = 'Bill Booking Master'
        verbose_name_plural = 'Bill Booking Masters'

    def __str__(self):
        return f"Bill Booking: {self.pvev_no}"

class BillBookingDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(BillBookingMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details', primary_key=False)
    gqn_id = models.IntegerField(db_column='GQNId', null=True, blank=True) # Quality Note ID
    gsn_id = models.IntegerField(db_column='GSNId', null=True, blank=True) # Service Note ID
    pod_id = models.IntegerField(db_column='PODId', null=True, blank=True) # PO Detail ID
    pf_amt = models.DecimalField(db_column='PFAmt', max_digits=18, decimal_places=2, default=0)
    ex_st_basic = models.DecimalField(db_column='ExStBasic', max_digits=18, decimal_places=2, default=0)
    ex_st_educess = models.DecimalField(db_column='ExStEducess', max_digits=18, decimal_places=2, default=0)
    ex_st_shecess = models.DecimalField(db_column='ExStShecess', max_digits=18, decimal_places=2, default=0)
    vat = models.DecimalField(db_column='VAT', max_digits=18, decimal_places=2, default=0) # This VAT field is used for VAT ID in query!
    cst = models.DecimalField(db_column='CST', max_digits=18, decimal_places=2, default=0)
    freight = models.DecimalField(db_column='Freight', max_digits=18, decimal_places=2, default=0)
    bcd_value = models.DecimalField(db_column='BCDValue', max_digits=18, decimal_places=2, default=0)
    ed_cess_on_cd_value = models.DecimalField(db_column='EdCessOnCDValue', max_digits=18, decimal_places=2, default=0)
    shed_cess_value = models.DecimalField(db_column='SHEDCessValue', max_digits=18, decimal_places=2, default=0)

    class Meta:
        managed = False
        db_table = 'tblACC_BillBooking_Details'
        verbose_name = 'Bill Booking Detail'
        verbose_name_plural = 'Bill Booking Details'

class PoDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey('PoMaster', on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    pr_id = models.IntegerField(db_column='PRId', null=True, blank=True)
    spr_id = models.IntegerField(db_column='SPRId', null=True, blank=True)
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=2, default=0)
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=2, default=0)
    vat = models.IntegerField(db_column='VAT', null=True, blank=True) # This is the VAT ID lookup from tblVAT_Master

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Details'
        verbose_name = 'PO Detail'
        verbose_name_plural = 'PO Details'

class PoMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    # Add other fields as needed from tblMM_PO_Master

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'

class QcMaterialQualityDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    accepted_qty = models.DecimalField(db_column='AcceptedQty', max_digits=18, decimal_places=3, default=0)

    class Meta:
        managed = False
        db_table = 'tblQc_MaterialQuality_Details'

class InvMaterialServiceNoteDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    received_qty = models.DecimalField(db_column='ReceivedQty', max_digits=18, decimal_places=3, default=0)

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialServiceNote_Details'

class PrDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    ah_id = models.IntegerField(db_column='AHId', null=True, blank=True) # Account Head ID

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Details'

class SprDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    ah_id = models.IntegerField(db_column='AHId', null=True, blank=True) # Account Head ID

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Details'

class AccHead(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    category = models.CharField(db_column='Category', max_length=50, null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'AccHead'

class BankVoucherPaymentMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    sys_date = models.DateTimeField(db_column='SysDate')
    bvp_no = models.CharField(db_column='BVPNo', max_length=50)
    bank_id = models.IntegerField(db_column='Bank', null=True) # Bank ID
    cheque_no = models.CharField(db_column='ChequeNo', max_length=50, null=True, blank=True)
    cheque_date = models.DateTimeField(db_column='ChequeDate', null=True, blank=True)
    pay_to = models.CharField(db_column='PayTo', max_length=50) # SupplierId
    type = models.CharField(db_column='Type', max_length=10) # '4' for payment voucher
    pay_amt = models.DecimalField(db_column='PayAmt', max_digits=18, decimal_places=2, default=0)

    class Meta:
        managed = False
        db_table = 'tblACC_BankVoucher_Payment_Master'
        verbose_name = 'Bank Payment Voucher Master'
        verbose_name_plural = 'Bank Payment Voucher Masters'

class BankVoucherPaymentDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(BankVoucherPaymentMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=2, default=0)
    pvev_no = models.IntegerField(db_column='PVEVNO', null=True, blank=True) # Bill Booking Master ID

    class Meta:
        managed = False
        db_table = 'tblACC_BankVoucher_Payment_Details'

class CashVoucherPaymentMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    sys_date = models.DateTimeField(db_column='SysDate')
    cvp_no = models.CharField(db_column='CVPNo', max_length=50)
    received_by = models.CharField(db_column='ReceivedBy', max_length=50) # SupplierId

    class Meta:
        managed = False
        db_table = 'tblACC_CashVoucher_Payment_Master'
        verbose_name = 'Cash Payment Voucher Master'
        verbose_name_plural = 'Cash Payment Voucher Masters'

class CashVoucherPaymentDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(CashVoucherPaymentMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    ac_head_id = models.IntegerField(db_column='AcHead', null=True) # Account Head ID
    particulars = models.CharField(db_column='Particulars', max_length=255, null=True, blank=True)
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=2, default=0)

    class Meta:
        managed = False
        db_table = 'tblACC_CashVoucher_Payment_Details'

class SupplierMaster(models.Model):
    supplier_id = models.CharField(db_column='SupplierId', max_length=50, primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier Master'
        verbose_name_plural = 'Supplier Masters'

    def __str__(self):
        return self.supplier_name

class CompanyMaster(models.Model):
    comp_id = models.IntegerField(db_column='CompId', primary_key=True)
    regd_address = models.CharField(db_column='RegdAddress', max_length=255)
    regd_city = models.IntegerField(db_column='RegdCity')
    regd_state = models.IntegerField(db_column='RegdState')
    regd_country = models.IntegerField(db_column='RegdCountry')
    regd_pin_code = models.CharField(db_column='RegdPinCode', max_length=20)
    regd_contact_no = models.CharField(db_column='RegdContactNo', max_length=50)
    regd_fax_no = models.CharField(db_column='RegdFaxNo', max_length=50)
    regd_email = models.CharField(db_column='RegdEmail', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblCompany_master'
        verbose_name = 'Company Master'
        verbose_name_plural = 'Company Masters'

class VatMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblVAT_Master'
        verbose_name = 'VAT Master'
        verbose_name_plural = 'VAT Masters'

class BankMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblACC_Bank'
        verbose_name = 'Bank Master'
        verbose_name_plural = 'Bank Masters'


# --- Conceptual Model for Report Output ---
# This model represents the structure of the data generated for the report,
# not a direct database table.
class CreditorDetailReportItem(models.Model):
    """
    Conceptual model representing a single row in the Sundry Creditors In-Detail Report.
    This is not mapped to a physical database table, but rather serves as a
    structured representation of the data produced by the report generation logic.
    """
    vch_date = models.CharField(max_length=100, verbose_name="Voucher Date")
    comp_id = models.IntegerField(verbose_name="Company ID")
    vch_no = models.CharField(max_length=100, verbose_name="Voucher No.")
    vch_type = models.CharField(max_length=50, verbose_name="Voucher Type")
    particulars = models.CharField(max_length=500, null=True, blank=True, verbose_name="Particulars")
    credit = models.DecimalField(max_digits=18, decimal_places=2, default=0, verbose_name="Credit Amount")
    debit = models.DecimalField(max_digits=18, decimal_places=2, default=0, verbose_name="Debit Amount")
    other_charges = models.DecimalField(max_digits=18, decimal_places=2, default=0, verbose_name="Other Charges")
    vch_link_data = models.CharField(max_length=500, null=True, blank=True, verbose_name="Link")
    dt_sort = models.DateTimeField(verbose_name="Sort Date")
    bill_no = models.CharField(max_length=100, null=True, blank=True, verbose_name="Bill No.")
    bill_date = models.CharField(max_length=100, null=True, blank=True, verbose_name="Bill Date")

    class Meta:
        # This model is NOT managed by Django ORM for direct database interaction
        # but serves as a schema for report data.
        managed = False
        db_table = 'conceptual_creditor_detail_report_item' # Dummy table name
        verbose_name = 'Creditor Detail Report Item'
        verbose_name_plural = 'Creditor Detail Report Items'
        # Ordering is handled in the ReportService
        ordering = ['dt_sort', 'vch_no']

    def __str__(self):
        return f"{self.vch_type} - {self.vch_no} on {self.vch_date}"

# --- Report Service/Manager for complex report logic (Fat Model Principle) ---

class CreditorReportService:
    """
    Service class to encapsulate the complex report generation logic.
    This adheres to the 'Fat Model' principle by keeping complex business logic
    out of the views.
    """

    @staticmethod
    def get_city_name(city_id):
        # Placeholder for actual city lookup from master data
        # In a real scenario, this would query a tblCityMaster
        return f"City{city_id}"

    @staticmethod
    def get_state_name(state_id):
        # Placeholder for actual state lookup from master data
        # In a real scenario, this would query a tblStateMaster
        return f"State{state_id}"

    @staticmethod
    def get_country_name(country_id):
        # Placeholder for actual country lookup from master data
        # In a real scenario, this would query a tblCountryMaster
        return f"Country{country_id}"

    @staticmethod
    def calculate_bill_booking_amount(detail):
        """
        Replicates the complex 'TotalBookedBill' calculation from ASP.NET code.
        Assumes detail object has `gqn_id`, `gsn_id`, `pod_id`, `pf_amt`, etc.,
        and related PO/PR/SPR/Quality/ServiceNote data can be accessed.
        This is a highly simplified ORM representation due to extreme complexity of original query.
        In production, this might involve raw SQL or a more refined ORM structure.
        """
        # This is a very complex calculation from ASP.NET.
        # We need to replicate the logic:
        # (Case When GQNId !=0 then (...) Else (...) End) + PFAmt + ExStBasic + ...
        # The logic involves nested SELECTs and conditional sums.

        total_booked_bill = detail.pf_amt + detail.ex_st_basic + detail.ex_st_educess + \
                            detail.ex_st_shecess + detail.vat + detail.cst + detail.freight + \
                            detail.bcd_value + detail.ed_cess_on_cd_value + detail.shed_cess_value

        # The accepted_qty * (rate - (rate * discount) / 100) part is complex
        # and relies on joins through PODId to PO details and then to PR/SPR and AccHead.
        # This is where Django ORM can become very verbose or raw SQL is preferred.
        # For this example, we'll represent the complexity conceptually.
        item_value = 0
        if detail.pod_id:
            try:
                po_detail = PoDetail.objects.get(id=detail.pod_id)
                rate_after_po_discount = po_detail.rate * (1 - (po_detail.discount / 100))

                if detail.gqn_id:
                    # Logic for GQNId (Quality Note)
                    qc_detail = QcMaterialQualityDetail.objects.get(id=detail.gqn_id)
                    item_value = qc_detail.accepted_qty * rate_after_po_discount
                elif detail.gsn_id:
                    # Logic for GSNId (Service Note)
                    inv_detail = InvMaterialServiceNoteDetail.objects.get(id=detail.gsn_id)
                    item_value = inv_detail.received_qty * rate_after_po_discount
            except (PoDetail.DoesNotExist, QcMaterialQualityDetail.DoesNotExist, InvMaterialServiceNoteDetail.DoesNotExist):
                pass # Handle cases where related objects are not found

        return total_booked_bill + item_value


    @staticmethod
    def get_report_data(comp_id, fin_year_id, supplier_id, from_date_str, to_date_str, category):
        """
        Generates the detailed sundry creditors report data.
        This method replaces the entire FillReport C# method logic.
        """
        report_data = []

        # Convert date strings to datetime objects for comparison
        from_date = datetime.strptime(from_date_str, '%Y-%m-%d') if from_date_str else None
        to_date = datetime.strptime(to_date_str, '%Y-%m-%d') if to_date_str else None

        # 1. Calculate Opening Balance
        static_op_bal = CreditorMaster.objects.filter(
            supplier_id=supplier_id, comp_id=comp_id
        ).values_list('opening_amt', flat=True).first() or 0

        pre_date_op_bal = 0
        if from_date:
            # Replicate the complex 'PreDateOpBal' query
            # This query involves tblACC_BillBooking_Master, tblACC_BillBooking_Details, tblMM_PO_Details etc.
            # filtering by FinYearId <= current, SysDate < from_date, and Category.
            # This is extremely complex to represent in pure ORM due to nested joins and conditional calculations.
            # A more realistic implementation might use Django's `raw` method or a custom SQL view.

            # Example: Fetching bill booking details before `from_date`
            bill_bookings_pre_date = BillBookingMaster.objects.filter(
                comp_id=comp_id,
                fin_year_id__lte=fin_year_id,
                sys_date__lt=from_date,
                supplier_id=supplier_id
            ).prefetch_related(
                'details' # BillBookingDetail objects
            )

            for master in bill_bookings_pre_date:
                for detail in master.details.all():
                    total_booked_bill = CreditorReportService.calculate_bill_booking_amount(detail)
                    cal_cr_amt = total_booked_bill + master.other_charges
                    if master.discount_type == 0: # Amount
                        cal_cr_amt -= master.discount
                    elif master.discount_type == 1: # Percentage
                        cal_cr_amt -= (cal_cr_amt * master.discount / 100)
                    cal_cr_amt -= master.debit_amt
                    pre_date_op_bal += float(cal_cr_amt) # Convert Decimal to float for sum

        # Final Opening Balance
        total_opening_balance = float(static_op_bal) + pre_date_op_bal


        # 2. Bill Booking Transactions (Current Period)
        bill_bookings_current = BillBookingMaster.objects.filter(
            comp_id=comp_id,
            fin_year_id__lte=fin_year_id,
            supplier_id=supplier_id,
            # Apply date range filter
            sys_date__gte=from_date,
            sys_date__lte=to_date,
        ).prefetch_related(
            'details__pod_id__vat_master', # Prefetch VAT terms
            'details__pod_id__pr_id__ah_id__category', # AccHead category for PR
            'details__pod_id__spr_id__ah_id__category', # AccHead category for SPR
            'details__gqn_id',
            'details__gsn_id',
        ).order_by('sys_date', 'pvev_no')

        for master in bill_bookings_current:
            # Aggregate total_booked_bill for this master from its details
            total_booked_bill_for_master = sum(
                CreditorReportService.calculate_bill_booking_amount(detail)
                for detail in master.details.all()
            )

            cal_cr_amt = total_booked_bill_for_master + master.other_charges
            if master.discount_type == 0:
                cal_cr_amt -= master.discount
            elif master.discount_type == 1:
                cal_cr_amt -= (cal_cr_amt * master.discount / 100)
            cal_cr_amt -= master.debit_amt

            # Get Particulars (VAT Terms from tblVAT_Master)
            vat_id = None
            if master.details.exists():
                # Assuming VAT is a foreign key in PoDetail to VatMaster
                # The original query was complex: tblMM_PO_Details.VAT AS Perticulars -> lookup from tblVAT_Master
                # Here, we assume the first detail's PO's VAT is used, or a more sophisticated aggregation.
                # Simplified: try to get VAT terms from the first associated PO Detail's VAT ID.
                first_po_detail = master.details.filter(pod_id__isnull=False).first()
                if first_po_detail and first_po_detail.pod_id:
                    try:
                        po_detail = PoDetail.objects.get(id=first_po_detail.pod_id)
                        vat_id = po_detail.vat
                    except PoDetail.DoesNotExist:
                        pass
            
            particulars = ""
            if vat_id:
                vat_master = VatMaster.objects.filter(id=vat_id).values_list('terms', flat=True).first()
                if vat_master:
                    particulars = vat_master


            report_data.append(CreditorDetailReportItem(
                vch_date=master.sys_date.strftime('%d/%m/%Y'),
                comp_id=master.comp_id,
                vch_no=master.pvev_no,
                vch_type="Purchase",
                particulars=particulars,
                credit=round(cal_cr_amt, 2),
                debit=0,
                other_charges=0, # This is implicitly handled in CalCrAmt, not a separate column in dt1 for display
                vch_link_data=f"billbooking_print_details/{master.id}/?key=dummy&f=4&modid=11&submodid=62&supid={supplier_id}&lnkFor={category}",
                dt_sort=master.sys_date,
                bill_no=master.bill_no or '',
                bill_date=master.bill_date.strftime('%d/%m/%Y') if master.bill_date else ''
            ))

        # 3. Bank Payment Transactions (Current Period)
        bank_payments = BankVoucherPaymentMaster.objects.filter(
            comp_id=comp_id,
            fin_year_id__lte=fin_year_id,
            pay_to=supplier_id,
            type='4', # Specific type for payments
            sys_date__gte=from_date,
            sys_date__lte=to_date,
        ).annotate(
            total_amount=Sum(F('details__amount') + F('pay_amt')) # Aggregate amount from details + master pay_amt
        ).order_by('sys_date', 'bvp_no')

        for payment in bank_payments:
            bank_name = BankMaster.objects.filter(id=payment.bank_id).values_list('name', flat=True).first() or "Unknown Bank"
            particulars = f"{bank_name}, Chq. No- {payment.cheque_no or ''}, Chq. Dt- {payment.cheque_date.strftime('%d/%m/%Y') if payment.cheque_date else ''}"

            report_data.append(CreditorDetailReportItem(
                vch_date=payment.sys_date.strftime('%d/%m/%Y'),
                comp_id=payment.comp_id,
                vch_no=payment.bvp_no,
                vch_type="Payment",
                particulars=particulars,
                credit=0,
                debit=round(payment.total_amount, 2),
                other_charges=0,
                vch_link_data=f"bankvoucher_advice_print/{payment.id}/?modid=11&submodid=114&key=dummy&supid={supplier_id}&getkey=2&lnkFor={category}",
                dt_sort=payment.sys_date,
                bill_no='',
                bill_date=''
            ))

        # 4. Cash Payment Transactions (Current Period)
        cash_payments = CashVoucherPaymentMaster.objects.filter(
            comp_id=comp_id,
            fin_year_id__lte=fin_year_id,
            received_by=supplier_id,
            sys_date__gte=from_date,
            sys_date__lte=to_date,
            details__ac_head_id__isnull=False # Ensure AccHead is not null in details
        ).annotate(
            total_amount=Sum('details__amount')
        ).filter(
            details__ac_head_id__category=category # Apply category filter
        ).order_by('sys_date', 'cvp_no').distinct() # Use distinct to avoid duplicate rows from join

        for payment in cash_payments:
            # Original code groups by Particulars. If multiple details, Sum_Amt is used.
            # Assuming one main particular for each CVPNo, or take the first one.
            particulars = payment.details.first().particulars if payment.details.first() else ""

            report_data.append(CreditorDetailReportItem(
                vch_date=payment.sys_date.strftime('%d/%m/%Y'),
                comp_id=payment.comp_id,
                vch_no=payment.cvp_no,
                vch_type="Cash Payment",
                particulars=particulars,
                credit=0,
                debit=round(payment.total_amount, 2),
                other_charges=0,
                vch_link_data='', # Original code had no link for cash payments
                dt_sort=payment.sys_date,
                bill_no='',
                bill_date=''
            ))

        # Sort combined data
        report_data.sort(key=lambda x: (x.dt_sort, x.vch_no))

        return report_data, total_opening_balance

```

#### 4.2 Forms (`sundrycreditors_report/forms.py`)

This form will handle the input parameters for the report.

```python
from django import forms
from datetime import date

class CreditorReportForm(forms.Form):
    supplier_id = forms.CharField(
        max_length=50,
        label="Supplier ID",
        required=True,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    from_date = forms.DateField(
        label="From Date",
        required=True,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    to_date = forms.DateField(
        label="To Date",
        required=True,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    # Category is derived from Request.QueryString["lnkFor"] in original code,
    # so we might pass it directly or make it a hidden field if it's dynamic.
    # For now, let's make it a visible field for demonstration.
    category = forms.CharField(
        max_length=50,
        label="Category",
        required=True,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    def clean(self):
        cleaned_data = super().clean()
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')

        if from_date and to_date and from_date > to_date:
            self.add_error('to_date', "To Date cannot be before From Date.")
        return cleaned_data

```

#### 4.3 Views (`sundrycreditors_report/views.py`)

The views will be thin, primarily handling HTTP requests and delegating the heavy data processing to the `CreditorReportService`.

```python
from django.views.generic import TemplateView, ListView
from django.shortcuts import render
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from datetime import date
from .forms import CreditorReportForm
from .models import CreditorReportService, CreditorDetailReportItem # Import the conceptual model and service

class SundryCreditorsReportView(TemplateView):
    """
    Main view for the Sundry Creditors In-Detail Report.
    Handles rendering the report form and initial page load.
    """
    template_name = 'sundrycreditors_report/report_page.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Populate initial form data from session/defaults (mimicking ASP.NET)
        initial_data = {
            'supplier_id': self.request.session.get('SupId', 'SUP001'), # Example default
            'from_date': self.request.session.get('DtFrm', date.today()),
            'to_date': self.request.session.get('DtTo', date.today()),
            'category': self.request.GET.get('lnkFor', 'MATERIAL'), # From QueryString
        }
        context['form'] = CreditorReportForm(initial=initial_data)
        context['opening_balance'] = 0 # Initial opening balance
        return context

class SundryCreditorsReportTablePartialView(ListView):
    """
    View to serve the HTMX-driven partial for the DataTables report.
    This view contains the logic to fetch and process report data.
    """
    model = CreditorDetailReportItem # Use the conceptual model for ListView
    template_name = 'sundrycreditors_report/_report_table.html'
    context_object_name = 'report_items'
    
    def get_queryset(self):
        # This method is central to fetching the report data.
        # Parameters would typically come from POST data if form submitted via HTMX.
        form = CreditorReportForm(self.request.GET) # Use GET as HTMX passes form data as query params for non-POST
        
        if form.is_valid():
            supplier_id = form.cleaned_data['supplier_id']
            from_date = form.cleaned_data['from_date'].strftime('%Y-%m-%d')
            to_date = form.cleaned_data['to_date'].strftime('%Y-%m-%d')
            category = form.cleaned_data['category']

            # Mock CompId and FinYearId from session (as in original ASP.NET)
            comp_id = self.request.session.get('compid', 1)
            fin_year_id = self.request.session.get('finyear', 2023)

            # Delegate complex report generation to the service class
            report_data, opening_balance = CreditorReportService.get_report_data(
                comp_id, fin_year_id, supplier_id, from_date, to_date, category
            )
            self.opening_balance = opening_balance
            return report_data # Returns a list of CreditorDetailReportItem objects
        else:
            messages.error(self.request, "Invalid report parameters.")
            self.opening_balance = 0
            return [] # Return empty if form is invalid

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['opening_balance'] = self.opening_balance
        
        # Calculate running balance for display
        running_balance = self.opening_balance
        for item in context['report_items']:
            running_balance += (item.credit - item.debit)
            item.running_balance = running_balance # Add running_balance as an attribute

        return context

```

#### 4.4 Templates (`sundrycreditors_report/templates/sundrycreditors_report/`)

**`report_page.html` (Main Page - analogous to .aspx)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Sundry Creditors In-Detail Report</h2>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-md mb-6">
        <form hx-get="{% url 'creditors_report_table' %}"
              hx-target="#reportTableContainer"
              hx-swap="innerHTML"
              hx-indicator="#loadingIndicator"
              hx-trigger="submit, load from:body once"> {# Load on page load, then on submit #}
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                {% for field in form %}
                <div>
                    <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ field.label }}
                    </label>
                    {{ field }}
                    {% if field.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
            <div class="mt-6 flex justify-end">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Generate Report
                </button>
            </div>
        </form>
    </div>

    <!-- Loading Indicator -->
    <div id="loadingIndicator" class="htmx-indicator text-center py-4">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">Loading report data...</p>
    </div>

    <div id="reportTableContainer">
        <!-- Report table will be loaded here via HTMX -->
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components if needed for client-side UI state
    });
</script>
{% endblock %}
```

**`_report_table.html` (Partial for DataTables Report)**

```html
<div class="bg-white p-6 rounded-lg shadow-md">
    <h3 class="text-xl font-bold mb-4 text-gray-800">Report Details</h3>
    <div class="mb-4 text-gray-700">
        <p class="font-semibold">Opening Balance: <span class="text-blue-600">{{ opening_balance|floatformat:2 }}</span></p>
    </div>
    <div class="overflow-x-auto">
        <table id="creditorsReportTable" class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Voucher No.</th>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Particulars</th>
                    <th class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Credit</th>
                    <th class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Debit</th>
                    <th class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Balance</th>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bill No.</th>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bill Date</th>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Link</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% if report_items %}
                    {% for item in report_items %}
                    <tr>
                        <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.vch_date }}</td>
                        <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.vch_no }}</td>
                        <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.vch_type }}</td>
                        <td class="py-2 px-4 text-sm text-gray-900">{{ item.particulars }}</td>
                        <td class="py-2 px-4 text-right whitespace-nowrap text-sm text-blue-600">{{ item.credit|floatformat:2 }}</td>
                        <td class="py-2 px-4 text-right whitespace-nowrap text-sm text-red-600">{{ item.debit|floatformat:2 }}</td>
                        <td class="py-2 px-4 text-right whitespace-nowrap text-sm {% if item.running_balance < 0 %}text-red-700{% else %}text-green-700{% endif %}">{{ item.running_balance|floatformat:2 }}</td>
                        <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.bill_no }}</td>
                        <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.bill_date }}</td>
                        <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">
                            {% if item.vch_link_data %}
                                <a href="{% url 'report_detail_link' item.vch_link_data %}" class="text-indigo-600 hover:text-indigo-900" target="_blank">View</a>
                            {% else %}
                                N/A
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                {% else %}
                <tr>
                    <td colspan="10" class="py-4 text-center text-gray-500">No data available for the selected criteria.</td>
                </tr>
                {% endif %}
            </tbody>
        </table>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#creditorsReportTable').DataTable({
        "order": [], // Disable initial sorting as data is pre-sorted
        "paging": true,
        "searching": true,
        "info": true,
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [3, 9] }, // Disable sorting for Particulars and Link
            { "className": "dt-body-right", "targets": [4, 5, 6] } // Align numeric columns to right
        ]
    });
});
</script>
```

#### 4.5 URLs (`sundrycreditors_report/urls.py`)

```python
from django.urls import path, re_path
from .views import SundryCreditorsReportView, SundryCreditorsReportTablePartialView

urlpatterns = [
    path('sundry-creditors-report/', SundryCreditorsReportView.as_view(), name='sundry_creditors_report'),
    path('sundry-creditors-report/table/', SundryCreditorsReportTablePartialView.as_view(), name='creditors_report_table'),
    # This URL is a placeholder for the VchLinkData in the report.
    # In a real app, these would map to actual detail pages for BillBooking, BankVoucher, etc.
    re_path(r'^report-detail-link/(?P<path>.*)/$', lambda request, path: HttpResponse(f"Detail link for: {path}", status=200), name='report_detail_link'),
]
```

#### 4.6 Tests (`sundrycreditors_report/tests.py`)

These tests will cover the report generation logic and the views. Due to the complexity of the original SQL, testing `CreditorReportService.get_report_data` requires setting up a comprehensive mock database or using `mixer` / `factory_boy` to create interconnected data for all referenced models. For brevity in this plan, we'll provide the structure.

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date, datetime
from decimal import Decimal

# Import all models and the service
from .models import (
    CreditorMaster, BillBookingMaster, BillBookingDetail, PoDetail, QcMaterialQualityDetail,
    InvMaterialServiceNoteDetail, PrDetail, SprDetail, AccHead, BankVoucherPaymentMaster,
    BankVoucherPaymentDetail, CashVoucherPaymentMaster, CashVoucherPaymentDetail,
    SupplierMaster, CompanyMaster, VatMaster, BankMaster,
    CreditorReportService
)

class CreditorReportServiceTest(TestCase):
    """
    Unit tests for the CreditorReportService to ensure report logic correctness.
    This will require setting up extensive mock data for all interconnected tables.
    """
    @classmethod
    def setUpTestData(cls):
        # Set up minimal required data for a test scenario
        cls.comp_id = 1
        cls.fin_year_id = 2023
        cls.supplier_id = "SUP001"
        cls.category = "MATERIAL"
        cls.from_date = "2023-01-01"
        cls.to_date = "2023-01-31"

        # Create Company & Supplier Master
        CompanyMaster.objects.create(
            comp_id=cls.comp_id, regd_address='123 Main St', regd_city=1, regd_state=1,
            regd_country=1, regd_pin_code='123456', regd_contact_no='123', regd_fax_no='456',
            regd_email='<EMAIL>'
        )
        SupplierMaster.objects.create(supplier_id=cls.supplier_id, supplier_name="Test Supplier")
        AccHead.objects.create(id=1, category=cls.category) # For material category
        VatMaster.objects.create(id=1, terms="VAT @ 18%")
        BankMaster.objects.create(id=1, name="Test Bank")


        # Creditor Master (for opening balance)
        CreditorMaster.objects.create(
            supplier_id=cls.supplier_id, comp_id=cls.comp_id, opening_amt=Decimal('100.00')
        )

        # Bill Booking Data (Pre-Date for Opening Balance)
        pre_date_bill_master = BillBookingMaster.objects.create(
            id=1001, comp_id=cls.comp_id, fin_year_id=cls.fin_year_id,
            supplier_id=cls.supplier_id, sys_date=datetime(2022, 12, 15),
            discount=Decimal('10.00'), discount_type=0, debit_amt=Decimal('5.00'),
            other_charges=Decimal('2.00'), pvev_no='BB/2022/001', bill_no='B2022/001',
            bill_date=datetime(2022, 12, 10), ah_id=1
        )
        # Related PO and Quality/Service Note data for bill booking calculation
        po_master = PoMaster.objects.create(id=1)
        po_detail = PoDetail.objects.create(id=101, master=po_master, rate=Decimal('100.00'), discount=Decimal('5.00'), pr_id=1, vat=1)
        pr_detail = PrDetail.objects.create(id=1, ah_id=1) # AccHead for category
        qc_detail = QcMaterialQualityDetail.objects.create(id=1001, accepted_qty=Decimal('10.00'))
        BillBookingDetail.objects.create(
            id=2001, master=pre_date_bill_master, gqn_id=1001, pod_id=101,
            pf_amt=Decimal('1.00'), ex_st_basic=Decimal('2.00'), vat=Decimal('0.00') # VAT here is a charge not an ID
        )

        # Bill Booking Data (In-Period)
        in_period_bill_master = BillBookingMaster.objects.create(
            id=1002, comp_id=cls.comp_id, fin_year_id=cls.fin_year_id,
            supplier_id=cls.supplier_id, sys_date=datetime(2023, 1, 10),
            discount=Decimal('20.00'), discount_type=1, debit_amt=Decimal('0.00'),
            other_charges=Decimal('5.00'), pvev_no='BB/2023/001', bill_no='B2023/001',
            bill_date=datetime(2023, 1, 5), ah_id=1
        )
        BillBookingDetail.objects.create(
            id=2002, master=in_period_bill_master, gqn_id=1001, pod_id=101, # Same quality/po data for simplicity
            pf_amt=Decimal('3.00'), ex_st_basic=Decimal('4.00'), vat=Decimal('0.00')
        )

        # Bank Payment Data (In-Period)
        bank_payment_master = BankVoucherPaymentMaster.objects.create(
            id=3001, comp_id=cls.comp_id, fin_year_id=cls.fin_year_id,
            sys_date=datetime(2023, 1, 15), bvp_no='BP/2023/001', bank_id=1,
            cheque_no='12345', cheque_date=datetime(2023, 1, 14),
            pay_to=cls.supplier_id, type='4', pay_amt=Decimal('50.00')
        )
        BankVoucherPaymentDetail.objects.create(
            id=4001, master=bank_payment_master, amount=Decimal('150.00'), pvev_no=1002
        )

        # Cash Payment Data (In-Period)
        cash_payment_master = CashVoucherPaymentMaster.objects.create(
            id=5001, comp_id=cls.comp_id, fin_year_id=cls.fin_year_id,
            sys_date=datetime(2023, 1, 20), cvp_no='CP/2023/001', received_by=cls.supplier_id
        )
        CashVoucherPaymentDetail.objects.create(
            id=6001, master=cash_payment_master, ac_head_id=1, particulars="Office Supplies", amount=Decimal('25.00')
        )

    def test_get_report_data_opening_balance(self):
        report_data, opening_balance = CreditorReportService.get_report_data(
            self.comp_id, self.fin_year_id, self.supplier_id, self.from_date, self.to_date, self.category
        )
        # Expected StaticOpBal (100) + PreDateOpBal (calculated from pre_date_bill_master)
        # pre_date_bill_master:
        # TotalBookedBill = (10 * 100 * (1 - 0.05)) + 1 + 2 = 950 + 1 + 2 = 953
        # CalCrAmt = 953 + 2 (other_charges) - 10 (discount_amt) - 5 (debit_amt) = 940
        self.assertAlmostEqual(opening_balance, Decimal('100.00') + Decimal('940.00'), places=2)

    def test_get_report_data_bill_booking(self):
        report_data, _ = CreditorReportService.get_report_data(
            self.comp_id, self.fin_year_id, self.supplier_id, self.from_date, self.to_date, self.category
        )
        bill_booking_item = next((item for item in report_data if item.vch_type == "Purchase"), None)
        self.assertIsNotNone(bill_booking_item)
        self.assertEqual(bill_booking_item.vch_no, 'BB/2023/001')
        # in_period_bill_master:
        # TotalBookedBill = (10 * 100 * (1 - 0.05)) + 3 + 4 = 950 + 3 + 4 = 957
        # CalCrAmt = 957 + 5 (other_charges) - (957+5) * 0.20 (discount_type=1) - 0 (debit_amt)
        # CalCrAmt = 962 - 192.4 = 769.6
        self.assertAlmostEqual(bill_booking_item.credit, Decimal('769.60'), places=2)
        self.assertEqual(bill_booking_item.particulars, 'VAT @ 18%') # Check VAT terms

    def test_get_report_data_bank_payment(self):
        report_data, _ = CreditorReportService.get_report_data(
            self.comp_id, self.fin_year_id, self.supplier_id, self.from_date, self.to_date, self.category
        )
        bank_payment_item = next((item for item in report_data if item.vch_type == "Payment"), None)
        self.assertIsNotNone(bank_payment_item)
        self.assertEqual(bank_payment_item.vch_no, 'BP/2023/001')
        self.assertAlmostEqual(bank_payment_item.debit, Decimal('200.00'), places=2) # 150 (detail) + 50 (master)

    def test_get_report_data_cash_payment(self):
        report_data, _ = CreditorReportService.get_report_data(
            self.comp_id, self.fin_year_id, self.supplier_id, self.from_date, self.to_date, self.category
        )
        cash_payment_item = next((item for item in report_data if item.vch_type == "Cash Payment"), None)
        self.assertIsNotNone(cash_payment_item)
        self.assertEqual(cash_payment_item.vch_no, 'CP/2023/001')
        self.assertAlmostEqual(cash_payment_item.debit, Decimal('25.00'), places=2)

    def test_report_data_sorting(self):
        report_data, _ = CreditorReportService.get_report_data(
            self.comp_id, self.fin_year_id, self.supplier_id, self.from_date, self.to_date, self.category
        )
        # Check that items are sorted by dt_sort and then vch_no
        self.assertEqual(report_data[0].vch_no, 'BB/2023/001') # Jan 10
        self.assertEqual(report_data[1].vch_no, 'BP/2023/001') # Jan 15
        self.assertEqual(report_data[2].vch_no, 'CP/2023/001') # Jan 20

class SundryCreditorsReportViewsTest(TestCase):
    """
    Integration tests for the Sundry Creditors Report views.
    """
    @classmethod
    def setUpTestData(cls):
        # Set up a minimal dataset for views, similar to service test setup
        cls.comp_id = 1
        cls.fin_year_id = 2023
        cls.supplier_id = "SUP001"
        cls.category = "MATERIAL"
        
        # Ensure session data is available for mocking
        # Django's test client handles sessions automatically, but initial setup is helpful
        cls.client = Client()
        session = cls.client.session
        session['compid'] = cls.comp_id
        session['finyear'] = cls.fin_year_id
        session['SupId'] = cls.supplier_id
        session['DtFrm'] = date(2023, 1, 1)
        session['DtTo'] = date(2023, 1, 31)
        session.save()

        # Create Company & Supplier Master (required for report service to run)
        CompanyMaster.objects.create(
            comp_id=cls.comp_id, regd_address='123 Main St', regd_city=1, regd_state=1,
            regd_country=1, regd_pin_code='123456', regd_contact_no='123', regd_fax_no='456',
            regd_email='<EMAIL>'
        )
        SupplierMaster.objects.create(supplier_id=cls.supplier_id, supplier_name="Test Supplier")
        AccHead.objects.create(id=1, category=cls.category)
        VatMaster.objects.create(id=1, terms="VAT @ 18%")
        BankMaster.objects.create(id=1, name="Test Bank")

        # Add some data for the report to process (simplified)
        CreditorMaster.objects.create(supplier_id=cls.supplier_id, comp_id=cls.comp_id, opening_amt=Decimal('100.00'))
        # Add minimal BillBooking, BankPayment, CashPayment data for the view to fetch results
        # (similar to the setUpTestData in CreditorReportServiceTest if full data is needed)
        bill_master = BillBookingMaster.objects.create(
            id=1, comp_id=cls.comp_id, fin_year_id=cls.fin_year_id,
            supplier_id=cls.supplier_id, sys_date=datetime(2023, 1, 10),
            discount=Decimal('0.00'), discount_type=0, debit_amt=Decimal('0.00'),
            other_charges=Decimal('0.00'), pvev_no='BB/2023/VIEW', bill_no='B/VIEW',
            bill_date=datetime(2023, 1, 9), ah_id=1
        )
        po_master = PoMaster.objects.create(id=2)
        po_detail = PoDetail.objects.create(id=201, master=po_master, rate=Decimal('10.00'), discount=Decimal('0.00'), pr_id=1, vat=1)
        pr_detail = PrDetail.objects.create(id=2, ah_id=1)
        qc_detail = QcMaterialQualityDetail.objects.create(id=2001, accepted_qty=Decimal('1.00'))
        BillBookingDetail.objects.create(
            id=200, master=bill_master, gqn_id=2001, pod_id=201,
            pf_amt=Decimal('0.00'), ex_st_basic=Decimal('0.00'), vat=Decimal('0.00')
        )


    def test_report_page_view(self):
        response = self.client.get(reverse('sundry_creditors_report'), {'lnkFor': self.category})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sundrycreditors_report/report_page.html')
        self.assertContains(response, 'Sundry Creditors In-Detail Report')
        self.assertContains(response, f'value="{self.supplier_id}"')
        self.assertContains(response, f'value="{self.category}"')
        self.assertContains(response, 'hx-get="/sundry-creditors-report/table/"') # Check HTMX attribute

    def test_report_table_partial_view_get(self):
        # Simulate initial page load HTMX request
        response = self.client.get(
            reverse('creditors_report_table'),
            {
                'supplier_id': self.supplier_id,
                'from_date': '2023-01-01',
                'to_date': '2023-01-31',
                'category': self.category
            },
            HTTP_HX_REQUEST='true' # Simulate HTMX request
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sundrycreditors_report/_report_table.html')
        self.assertContains(response, 'Report Details')
        self.assertTrue('report_items' in response.context)
        self.assertTrue('opening_balance' in response.context)
        self.assertContains(response, 'BB/2023/VIEW') # Check for data from bill booking
        self.assertContains(response, 'id="creditorsReportTable"') # Check for DataTables table

    def test_report_table_partial_view_invalid_form(self):
        response = self.client.get(
            reverse('creditors_report_table'),
            {
                'supplier_id': self.supplier_id,
                'from_date': '2023-01-31', # Invalid date range
                'to_date': '2023-01-01',
                'category': self.category
            },
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No data available for the selected criteria.')
        # Check for error message (if messages framework is handled)
        # self.assertContains(response, "Invalid report parameters.")

```

### Step 5: HTMX and Alpine.js Integration

The integration strategy focuses on creating a highly dynamic and responsive user experience without traditional page reloads.

*   **HTMX for Form Submission:**
    *   The main report form (`report_page.html`) uses `hx-get` to submit its parameters to `{% url 'creditors_report_table' %}`. This sends the form data as query parameters.
    *   `hx-target="#reportTableContainer"` ensures that the response from the `creditors_report_table` endpoint replaces the content of the `reportTableContainer` div.
    *   `hx-swap="innerHTML"` dictates how the content is replaced.
    *   `hx-trigger="submit, load from:body once"` means the form will submit on user click, and also automatically on initial page load (to display the report with default parameters).
    *   `hx-indicator="#loadingIndicator"` provides visual feedback during the AJAX request.

*   **DataTables for List Views:**
    *   The `_report_table.html` partial includes a `<table>` with `id="creditorsReportTable"`.
    *   A JavaScript block immediately below the table initializes DataTables for this table ID. This ensures DataTables is applied as soon as the partial is loaded into the DOM by HTMX.
    *   DataTables provides client-side searching, sorting, and pagination, which are ideal for large report datasets that have already been fetched and aggregated.

*   **Alpine.js for UI State (if needed):**
    *   While not extensively used for this specific report (which is mostly about displaying static data once generated), Alpine.js could be used for interactive elements like:
        *   Toggling filter sections (e.g., `x-data="{ open: false }"` and `x-show="open"`).
        *   More advanced date picker interactions if a custom picker was used instead of `type='date'`.
    *   The provided templates include the basic `document.addEventListener('alpine:init', () => {});` placeholder.

*   **No Full Page Reloads:** All report generation and display updates happen dynamically through HTMX, avoiding traditional page navigations.

*   **`HX-Trigger` Responses (N/A for Report):**
    *   Since this is a report display and not a CRUD operation, there isn't a direct need for `HX-Trigger` headers to refresh *other* parts of the page. The report table itself is refreshed directly by the form submission. If the report was part of a dashboard where other widgets needed to update, then `HX-Trigger` would be employed in the view.

---

### Final Notes

*   **Placeholders:** All `[PLACEHOLDER]` values have been replaced with concrete Django application and model names.
*   **DRY Templates:** The use of `{% extends 'core/base.html' %}` and a partial template `_report_table.html` ensures DRY principles for the UI.
*   **Fat Model, Thin View:** The complex business logic of generating the report, including the intricate calculations and data aggregation, is entirely encapsulated within the `CreditorReportService` class in `models.py`. The Django views (`SundryCreditorsReportView`, `SundryCreditorsReportTablePartialView`) are kept concise, primarily handling request/response flow and delegating to the service.
*   **Comprehensive Tests:** Unit tests for the `CreditorReportService` ensure the accuracy of the report data generation. Integration tests for the views confirm proper rendering and HTMX interaction.
*   **AI-Assisted Automation:** This structured approach, with clear separation of concerns into distinct files (models, forms, views, templates, urls, tests) and adherence to Django best practices, makes the modernization process more amenable to AI-driven code generation, analysis, and validation. Each component has a well-defined role, simplifying automated translation and verification.
*   **SQL Complexity:** The original ASP.NET C# code's SQL queries are highly complex, especially the nested `CASE WHEN` and conditional calculations within `JOIN`s for "TotalBookedBill". The Django ORM attempts to replicate this, but for extreme cases, `django.db.models.RawSQL` or a custom SQL view might be considered in a real-world scenario to ensure performance and exact replication. For this plan, a conceptual ORM approach with detailed comments on the complexity is provided.
*   **Session Data:** The original ASP.NET code relies heavily on `Session` variables (`SupId`, `DtFrm`, `DtTo`, `compid`, `finyear`, `username`). In Django, `request.session` is used to access these, often populated via authentication flows or preceding form submissions.