The following Django modernization plan outlines the conversion of your ASP.NET `Acc_Capital_Part_Details.aspx` page to a modern Django application. This plan focuses on automated, AI-assisted migration, ensuring business benefits through improved performance, maintainability, and user experience, all while adhering to "Fat Model, Thin View" principles, HTMX for dynamic interactions, Alpine.js for UI state, and DataTables for data presentation.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

- The ASP.NET code queries `tblACC_Capital_Details` and `tblACC_Capital_Master`.
- The `GridView1` binds to `Particulars`, `TotDrAmt`, `TotCrAmt`, and `Id`. The SQL query explicitly selects `CreditAmt As loan`, `Particulars`, `Id` from `tblACC_Capital_Details`, and filters based on `MId` (from `tblACC_Capital_Details`) and `CompId`, `FinYearId` from `tblACC_Capital_Master`.
- For consistency with how `TotDrAmt` and `TotCrAmt` are used for individual rows (`Eval`) and summed (`Compute`), we infer that `tblACC_Capital_Details` likely contains columns for individual debit and credit amounts. We will map `CreditAmt` to `credit_amount` and infer a `debit_amount` column.
- **Identified Tables and Columns:**
    - **`tblACC_Capital_Master`**:
        - `Id` (Primary Key, Integer)
        - `CompId` (Integer, Company Identifier)
        - `FinYearId` (Integer, Financial Year Identifier)
    - **`tblACC_Capital_Details`**:
        - `Id` (Primary Key, Integer)
        - `Particulars` (String, e.g., `VARCHAR(255)`)
        - `DebitAmt` (Decimal, inferred from `TotDrAmt` usage)
        - `CreditAmt` (Decimal, used as `TotCrAmt` and explicitly `CreditAmt` in query)
        - `MId` (Integer, Foreign Key to `tblACC_Capital_Master.Id`)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

-   **Read (Retrieve):** The core functionality is displaying a filtered list of capital transaction details. The data is retrieved from `tblACC_Capital_Details` joined with `tblACC_Capital_Master`, filtered by `CompId` and `FinYearId` (from ASP.NET Session) and `MId` (from QueryString).
-   **Aggregation:** The application calculates and displays the sum of 'Debit' and 'Credit' amounts for the entire filtered dataset in the `GridView` footer.
-   **Navigation/Action:** A "Cancel" button initiates a page redirection (`Response.Redirect`) to `Acc_Capital_Particulars.aspx`. The `Particulars` column is a `LinkButton`, suggesting a drill-down or edit action, though its `RowCommand` handler is empty. For a comprehensive Django solution, we will implement this as an edit/detail link.

**Note:** The provided ASP.NET code-behind does not show explicit Create, Update, or Delete operations. However, for a robust Django application, these functions are typically expected for managing data. Therefore, the Django plan will include these, assuming future requirements or an existing, but unprovided, ASP.NET implementation for these features.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

-   **Data Grid (`asp:GridView`):** The `GridView1` component, which displays a paginated, sortable list of capital details with a footer for totals, will be replaced by a modern HTML `<table>` element enhanced with `DataTables.js` for client-side search, sort, and pagination.
-   **Input Fields (`asp:LinkButton`, `asp:Label`):** The `Particulars` `LinkButton` will become an `<a>` tag or HTMX-triggered button for viewing/editing details. `Debit` and `Credit` `Label` controls will be standard text display within table cells.
-   **Action Button (`asp:Button`):** The "Cancel" button will be a simple HTML `<button>` or `<a>` tag, implementing a redirect behavior.
-   **Layout (`<table>`):** The original table-based layout will be modernized using Tailwind CSS for responsive design.
-   **Dynamic Interactions:** All data loading (for the table) and form interactions (for Add/Edit/Delete) will be handled via HTMX, ensuring a smooth, single-page application feel without complex JavaScript frameworks. Alpine.js will manage simple UI states like modal visibility.

## Step 4: Generate Django Code

### 4.1 Models (`accounts/models.py`)

This section defines the Django models for `CapitalMaster` and `CapitalDetail`, mapping them to the existing SQL Server tables. A custom manager `CapitalDetailManager` is introduced to encapsulate data retrieval and aggregation logic, adhering to the "Fat Model" principle.

```python
from django.db import models
from django.db.models import Sum, F
from django.urls import reverse_lazy

class CapitalMaster(models.Model):
    """
    Corresponds to tblACC_Capital_Master.
    Represents the master record for capital transactions,
    including company and financial year identifiers.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId', verbose_name="Company ID")
    fin_year_id = models.IntegerField(db_column='FinYearId', verbose_name="Financial Year ID")

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblACC_Capital_Master'
        verbose_name = 'Capital Master'
        verbose_name_plural = 'Capital Masters'

    def __str__(self):
        return f"Master {self.id} (Comp: {self.comp_id}, FinYear: {self.fin_year_id})"

class CapitalDetailManager(models.Manager):
    """
    Custom manager for CapitalDetail to encapsulate business logic
    such as filtering data based on session/query parameters and
    calculating aggregate totals, mimicking ASP.NET's Page_Load logic.
    """
    def get_queryset(self):
        return super().get_queryset()

    def filter_by_context(self, comp_id, fin_year_id, mid):
        """
        Filters CapitalDetail records based on provided company ID,
        financial year ID, and master ID. This replicates the SQL query
        logic from the ASP.NET code-behind.
        """
        # Ensure parameters are integers for filtering consistency
        try:
            comp_id = int(comp_id)
            fin_year_id = int(fin_year_id)
            mid = int(mid)
        except (ValueError, TypeError):
            # If conversion fails, return an empty queryset
            return self.none()

        return self.get_queryset().filter(
            master__comp_id=comp_id,
            master__fin_year_id__lte=fin_year_id,  # Original used <=
            master__id=mid
        ).select_related('master').order_by('id') # Order by ID for consistent SN

    def get_aggregated_totals(self, queryset):
        """
        Calculates the sum of debit and credit amounts from a given queryset,
        corresponding to the `dt.Compute` operations in ASP.NET for footer totals.
        """
        totals = queryset.aggregate(
            total_debit=Sum('debit_amount'),
            total_credit=Sum('credit_amount')
        )
        # Return 0.0 if sums are None (e.g., empty queryset)
        return {
            'total_debit': totals.get('total_debit') or 0.0,
            'total_credit': totals.get('total_credit') or 0.0
        }

class CapitalDetail(models.Model):
    """
    Corresponds to tblACC_Capital_Details.
    Represents individual capital transaction details.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    particulars = models.CharField(db_column='Particulars', max_length=255)
    # Assumed actual columns based on GridView bindings and the need for row-level display
    debit_amount = models.DecimalField(db_column='DebitAmt', max_digits=18, decimal_places=2, default=0.0)
    credit_amount = models.DecimalField(db_column='CreditAmt', max_digits=18, decimal_places=2, default=0.0)
    
    # Foreign key to CapitalMaster, using db_column for existing MId
    master = models.ForeignKey(
        CapitalMaster,
        on_delete=models.CASCADE,  # Adjust as per database's foreign key behavior
        db_column='MId',
        related_name='details'
    )

    objects = CapitalDetailManager()  # Attach the custom manager

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblACC_Capital_Details'
        verbose_name = 'Capital Detail'
        verbose_name_plural = 'Capital Details'

    def __str__(self):
        return self.particulars
        
    def get_absolute_url(self):
        """
        Returns the URL for the detail/edit view of this instance.
        Used for the 'Particulars' link.
        """
        return reverse_lazy('accounts:capitaldetail_edit', kwargs={'pk': self.pk})

    # Example of a business logic method (Fat Model)
    def calculate_balance(self):
        """Calculates the balance for this particular detail entry."""
        return self.debit_amount - self.credit_amount

```

### 4.2 Forms (`accounts/forms.py`)

A Django `ModelForm` for `CapitalDetail` to handle data creation and updates, integrated with Tailwind CSS for styling.

```python
from django import forms
from .models import CapitalDetail, CapitalMaster

class CapitalDetailForm(forms.ModelForm):
    class Meta:
        model = CapitalDetail
        # Include 'master' if it needs to be selectable/set through the form
        fields = ['particulars', 'debit_amount', 'credit_amount', 'master']
        widgets = {
            'particulars': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'debit_amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'credit_amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'master': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'particulars': 'Particulars',
            'debit_amount': 'Debit Amount',
            'credit_amount': 'Credit Amount',
            'master': 'Related Capital Master',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Optionally filter master choices based on context, e.g., company/financial year
        # self.fields['master'].queryset = CapitalMaster.objects.filter(...)

    def clean(self):
        cleaned_data = super().clean()
        # Example custom validation: ensure at least one amount is non-zero
        debit = cleaned_data.get('debit_amount')
        credit = cleaned_data.get('credit_amount')
        if not debit and not credit:
            raise forms.ValidationError("Either Debit Amount or Credit Amount must be provided.")
        return cleaned_data
```

### 4.3 Views (`accounts/views.py`)

Class-Based Views are used for brevity and adherence to Django best practices. Views are kept thin by delegating business logic to the `CapitalDetailManager`. HTMX responses (`HX-Trigger`, `HX-Redirect`) are implemented for dynamic UI updates.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, RedirectView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import redirect
from .models import CapitalDetail, CapitalMaster
from .forms import CapitalDetailForm

class CapitalDetailListView(ListView):
    """
    Main view to display the list of capital details,
    mimicking the ASP.NET GridView.
    It fetches data based on session and query string parameters.
    """
    model = CapitalDetail
    template_name = 'accounts/capitaldetail/list.html'
    context_object_name = 'capital_details' # Renamed for clarity in template

    def get_queryset(self):
        """
        Filter the queryset based on session (CompId, FinYearId)
        and query string (MId), similar to ASP.NET Page_Load logic.
        """
        comp_id = self.request.session.get('compid')
        fin_year_id = self.request.session.get('finyear')
        mid = self.request.GET.get('MId')

        if not all([comp_id, fin_year_id, mid]):
            # If critical parameters are missing, log and return empty queryset.
            # Messages.error will be shown to the user.
            messages.error(self.request, "Missing essential parameters (Company ID, Financial Year, MId).")
            return self.model.objects.none()

        # Use the custom manager method to filter data
        queryset = self.model.objects.filter_by_context(
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            mid=mid
        )
        return queryset

    def get_context_data(self, **kwargs):
        """
        Add aggregated totals to the context, mirroring ASP.NET's footer.
        Also passes MId to template for consistent links.
        """
        context = super().get_context_data(**kwargs)
        queryset = self.get_queryset()
        
        # Calculate totals using the manager method
        totals = self.model.objects.get_aggregated_totals(queryset)
        context.update(totals)
        
        # Pass MId from query string to the template for potential use in forms/links
        context['mid_param'] = self.request.GET.get('MId')

        return context

class CapitalDetailTablePartialView(CapitalDetailListView):
    """
    Renders only the table content for HTMX swaps.
    Inherits filtering and context logic from CapitalDetailListView.
    """
    template_name = 'accounts/capitaldetail/_capitaldetail_table.html'

class CapitalDetailCreateView(CreateView):
    model = CapitalDetail
    form_class = CapitalDetailForm
    template_name = 'accounts/capitaldetail/_capitaldetail_form.html' # Partial template for modal
    
    def get_initial(self):
        initial = super().get_initial()
        # Pre-fill 'master' field if MId is available from query parameters
        mid = self.request.GET.get('MId')
        if mid:
            try:
                master_instance = CapitalMaster.objects.get(id=mid)
                initial['master'] = master_instance
            except CapitalMaster.DoesNotExist:
                messages.warning(self.request, f"Master record with ID {mid} not found.")
        return initial

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Capital Detail added successfully.')
        # For HTMX requests, send 204 No Content and trigger a client-side refresh
        if self.request.headers.get('HX-Request'):
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshCapitalDetailList'})
        return response # Fallback for regular requests

    def form_invalid(self, form):
        # For HTMX requests, return the form with errors for re-rendering
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


class CapitalDetailUpdateView(UpdateView):
    model = CapitalDetail
    form_class = CapitalDetailForm
    template_name = 'accounts/capitaldetail/_capitaldetail_form.html' # Partial template
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['mid_param'] = self.request.GET.get('MId') # Pass MId to form template
        return context

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Capital Detail updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshCapitalDetailList'})
        return response

    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)

class CapitalDetailDeleteView(DeleteView):
    model = CapitalDetail
    template_name = 'accounts/capitaldetail/_capitaldetail_confirm_delete.html' # Partial template
    
    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Capital Detail deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshCapitalDetailList'})
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['mid_param'] = self.request.GET.get('MId') # Pass MId to delete confirmation template
        return context

class CapitalDetailCancelView(RedirectView):
    """
    Mimics the ASP.NET cancel button redirect.
    Redirects to the Capital Particulars page.
    """
    # Placeholder for the target URL of the original ASP.NET redirect.
    # This should be updated to the actual Django URL for Acc_Capital_Particulars.aspx.
    permanent = False
    query_string = True # Preserve query string parameters for the redirect
    
    # Assuming 'capital_particulars_list' is the Django URL name for the target page.
    url = reverse_lazy('capital_particulars_list') 

    def get_redirect_url(self, *args, **kwargs):
        messages.info(self.request, "Operation cancelled.")
        # Reconstruct the URL with any relevant query parameters if needed
        # For example, if the target page needs 'ModId' and 'SubModId'
        # mid = self.request.GET.get('MId')
        # return f"{self.url}?ModId=11&SubModId=&MId={mid}" if mid else f"{self.url}?ModId=11&SubModId="
        return super().get_redirect_url(*args, **kwargs)

    def get(self, request, *args, **kwargs):
        # For HTMX, return a HX-Redirect header instead of a full HTTP redirect
        if request.headers.get('HX-Request'):
            redirect_url = self.get_redirect_url(*args, **kwargs)
            return HttpResponse(status=200, headers={'HX-Redirect': redirect_url})
        return super().get(request, *args, **kwargs)
```

### 4.4 Templates (`accounts/templates/accounts/capitaldetail/`)

Templates are designed for HTMX partial rendering, extending a base template, and using Tailwind CSS for modern styling. DataTables is integrated for list views.

#### `list.html` (Main page for Capital Details)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-3xl font-extrabold text-gray-800">Capital (Goods) Details</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-md shadow-lg transition duration-150 ease-in-out transform hover:scale-105"
            hx-get="{% url 'accounts:capitaldetail_add' %}{% if mid_param %}?MId={{ mid_param }}{% endif %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus-circle mr-2"></i> Add New Detail
        </button>
    </div>
    
    <div id="capitaldetailTable-container"
         hx-trigger="load, refreshCapitalDetailList from:body"
         hx-get="{% url 'accounts:capitaldetail_table' %}{% if mid_param %}?MId={{ mid_param }}{% endif %}"
         hx-swap="innerHTML"
         class="min-h-[200px] flex items-center justify-center bg-gray-50 rounded-lg shadow-inner">
        <!-- Initial loading state -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-gray-600 text-lg">Loading Capital Details...</p>
        </div>
    </div>
    
    <!-- Universal Modal for forms and confirmations -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden is-active:flex"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-auto my-auto max-h-[90vh] overflow-y-auto transform scale-95 opacity-0 transition-all duration-300 is-active:scale-100 is-active:opacity-100"
             _="on htmx:afterOnLoad remove .is-active from #modal if event.detail.xhr.status === 204">
            <!-- Content loaded via HTMX -->
        </div>
    </div>

    <div class="mt-10 text-center">
        <a href="{% url 'accounts:capitaldetail_cancel' %}{% if mid_param %}?MId={{ mid_param }}{% endif %}"
           hx-get="{% url 'accounts:capitaldetail_cancel' %}{% if mid_param %}?MId={{ mid_param }}{% endif %}"
           hx-swap="outerHTML" hx-push-url="true"
           class="inline-flex items-center px-6 py-3 border border-gray-300 shadow-sm text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out">
            <i class="fas fa-arrow-left mr-2"></i> Cancel
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Minimal Alpine.js setup for modal interactivity (assuming full Alpine setup in base.html)
    document.addEventListener('alpine:init', () => {
        Alpine.data('modalController', () => ({
            open: false,
            toggle() { this.open = !this.open; },
            close() { this.open = false; }
        }));
    });

    // Custom event listener for HTMX triggers to close modal and handle messages
    document.body.addEventListener('htmx:afterSwap', function(event) {
        // If a 204 No Content response is received (e.g., successful form submission/deletion), close modal
        if (event.detail.target.id === 'modalContent' && event.detail.xhr.status === 204) {
            document.getElementById('modal').classList.remove('is-active');
        }
        // General message handling (Django messages) - often handled in base.html
        // if (event.detail.target.id === 'messages-container') {
        //     // Logic to display/hide messages if needed
        // }
    });
</script>
{% endblock %}
```

#### `_capitaldetail_table.html` (Partial for HTMX-loaded table content)

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-xl overflow-y-auto relative" style="height: 430px;">
    {% if capital_details %}
    <table id="capitaldetailTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-100 sticky top-0 z-10 shadow-sm">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Particulars</th>
                <th class="py-3 px-4 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider">Debit</th>
                <th class="py-3 px-4 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider">Credit</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-100">
            {% for detail in capital_details %}
            <tr class="hover:bg-gray-50">
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-800 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm">
                    <button 
                        class="text-blue-600 hover:text-blue-800 hover:underline focus:outline-none font-medium"
                        hx-get="{% url 'accounts:capitaldetail_edit' detail.pk %}{% if mid_param %}?MId={{ mid_param }}{% endif %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        {{ detail.particulars }}
                    </button>
                </td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-800 text-right">{{ detail.debit_amount|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-800 text-right">{{ detail.credit_amount|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md text-xs transition duration-150 ease-in-out mr-2 shadow-sm hover:shadow-md"
                        hx-get="{% url 'accounts:capitaldetail_edit' detail.pk %}{% if mid_param %}?MId={{ mid_param }}{% endif %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-xs transition duration-150 ease-in-out shadow-sm hover:shadow-md"
                        hx-get="{% url 'accounts:capitaldetail_delete' detail.pk %}{% if mid_param %}?MId={{ mid_param }}{% endif %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot class="bg-gray-200 sticky bottom-0 border-t border-gray-300">
            <tr>
                <td colspan="2" class="py-2 px-4 text-right text-base font-bold text-gray-900">Total</td>
                <td class="py-2 px-4 text-right text-base font-bold text-gray-900">{{ total_debit|floatformat:2 }}</td>
                <td class="py-2 px-4 text-right text-base font-bold text-gray-900">{{ total_credit|floatformat:2 }}</td>
                <td class="py-2 px-4"></td> {# Empty cell for actions column #}
            </tr>
        </tfoot>
    </table>
    {% else %}
    <div class="py-10 text-center text-lg text-gray-600">
        <p>No Records To Display</p>
        <p class="text-sm mt-2">Please check parameters or add new entries.</p>
    </div>
    {% endif %}
</div>

<script>
    // Initialize DataTable after content is loaded via HTMX
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists to prevent reinitialization errors
        if ($.fn.DataTable.isDataTable('#capitaldetailTable')) {
            $('#capitaldetailTable').DataTable().destroy();
        }
        $('#capitaldetailTable').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "autoWidth": false,
            "columnDefs": [
                { "orderable": false, "targets": 0 }, // SN column
                { "orderable": false, "targets": 4 }  // Actions column
            ],
            // Disable DataTables' own footer callback if Django pre-calculates totals
            "footerCallback": function ( row, data, start, end, display ) {
                // Since Django renders the totals directly into the <tfoot>,
                // we don't need DataTables to calculate them in JavaScript here.
                // This function can be left empty or removed if not needed.
            }
        });
    });
</script>
```

#### `_capitaldetail_form.html` (Partial for modal forms)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Capital Detail</h3>
    <form hx-post="{{ request.path }}{% if mid_param %}?MId={{ mid_param }}{% endif %}" hx-swap="none" class="space-y-6">
        {% csrf_token %}
        
        {% for field in form %}
        <div class="mb-4">
            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                {{ field.label }}
                {% if field.field.required %}<span class="text-red-500 ml-1">*</span>{% endif %}
            </label>
            {{ field }}
            {% if field.help_text %}
            <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
            {% endif %}
            {% if field.errors %}
            <div class="mt-2 text-red-600 text-sm">
                {% for error in field.errors %}
                <p>{{ error }}</p>
                {% endfor %}
            </div>
            {% endif %}
        </div>
        {% endfor %}
        
        <div class="flex items-center justify-end space-x-4 pt-4 border-t border-gray-200 mt-6">
            <button 
                type="button" 
                class="px-5 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="px-5 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md shadow-sm transition duration-150 ease-in-out">
                <i class="fas fa-save mr-2"></i> Save Capital Detail
            </button>
        </div>
    </form>
</div>
```

#### `_capitaldetail_confirm_delete.html` (Partial for delete confirmation modal)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Capital Detail for "<strong class="font-medium">{{ object.particulars }}</strong>" (ID: {{ object.pk }})?</p>
    <p class="text-sm text-gray-600 mb-6">This action cannot be undone.</p>
    
    <form hx-delete="{% url 'accounts:capitaldetail_delete' object.pk %}{% if mid_param %}?MId={{ mid_param }}{% endif %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4 pt-4 border-t border-gray-200 mt-6">
            <button 
                type="button" 
                class="px-5 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="px-5 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-md shadow-sm transition duration-150 ease-in-out">
                <i class="fas fa-trash-alt mr-2"></i> Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs (`accounts/urls.py`)

Defines the URL patterns for the Capital Details module, including the main list view, HTMX partials, and CRUD operations. Namespacing (`app_name = 'accounts'`) is used for clarity.

```python
from django.urls import path
from django.views.generic import TemplateView # For placeholder cancel target

from .views import (
    CapitalDetailListView, 
    CapitalDetailTablePartialView,
    CapitalDetailCreateView, 
    CapitalDetailUpdateView, 
    CapitalDetailDeleteView,
    CapitalDetailCancelView,
)

app_name = 'accounts' # Namespacing for URLs

urlpatterns = [
    # Main list view for Capital Details
    path('capital_details/', CapitalDetailListView.as_view(), name='capitaldetail_list'),
    
    # HTMX endpoint for dynamically loading the DataTables content
    path('capital_details/table/', CapitalDetailTablePartialView.as_view(), name='capitaldetail_table'),

    # CRUD operations
    path('capital_details/add/', CapitalDetailCreateView.as_view(), name='capitaldetail_add'),
    path('capital_details/edit/<int:pk>/', CapitalDetailUpdateView.as_view(), name='capitaldetail_edit'),
    path('capital_details/delete/<int:pk>/', CapitalDetailDeleteView.as_view(), name='capitaldetail_delete'),

    # Cancel button redirect. This URL should point to the target of the original ASP.NET redirect.
    # We assume 'capital_particulars_list' is the Django URL name for Acc_Capital_Particulars.aspx.
    path('capital_details/cancel/', CapitalDetailCancelView.as_view(), name='capitaldetail_cancel'),
    
    # Placeholder for the target page of the cancel button (if it's in this app).
    # In a real scenario, this would be a proper view/template for Acc_Capital_Particulars.aspx
    path('capital_particulars/', TemplateView.as_view(template_name='accounts/capital_particulars_placeholder.html'), name='capital_particulars_list'),
]

```

### 4.6 Tests (`accounts/tests.py`)

Comprehensive tests for both models (unit tests) and views (integration tests), ensuring high code coverage and validating the migration's correctness, including HTMX interactions.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from .models import CapitalMaster, CapitalDetail

class CapitalMasterModelTest(TestCase):
    """
    Unit tests for the CapitalMaster model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a CapitalMaster instance for related CapitalDetail objects
        cls.master = CapitalMaster.objects.create(
            id=101,
            comp_id=1,
            fin_year_id=2024
        )

    def test_capital_master_creation(self):
        """Test basic creation and field values."""
        self.assertEqual(self.master.comp_id, 1)
        self.assertEqual(self.master.fin_year_id, 2024)
        self.assertEqual(str(self.master), "Master 101 (Comp: 1, FinYear: 2024)")

    def test_meta_options(self):
        """Test Django Meta options like db_table and managed."""
        self.assertEqual(CapitalMaster._meta.db_table, 'tblACC_Capital_Master')
        self.assertFalse(CapitalMaster._meta.managed)
        self.assertEqual(CapitalMaster._meta.verbose_name, 'Capital Master')
        self.assertEqual(CapitalMaster._meta.verbose_name_plural, 'Capital Masters')

class CapitalDetailModelTest(TestCase):
    """
    Unit tests for the CapitalDetail model and its custom manager.
    """
    @classmethod
    def setUpTestData(cls):
        # Create master records and detail records for testing
        cls.master1 = CapitalMaster.objects.create(
            id=101,
            comp_id=1,
            fin_year_id=2024
        )
        cls.master2 = CapitalMaster.objects.create(
            id=102,
            comp_id=1,
            fin_year_id=2023
        )
        cls.detail1 = CapitalDetail.objects.create(
            id=1,
            particulars='Office Supplies',
            debit_amount=500.00,
            credit_amount=0.00,
            master=cls.master1
        )
        cls.detail2 = CapitalDetail.objects.create(
            id=2,
            particulars='Electricity Bill',
            debit_amount=0.00,
            credit_amount=750.00,
            master=cls.master1
        )
        cls.detail3 = CapitalDetail.objects.create(
            id=3,
            particulars='Rent Payment',
            debit_amount=1200.00,
            credit_amount=0.00,
            master=cls.master2 # Different master/finyear
        )

    def test_capital_detail_creation(self):
        """Test basic creation and foreign key relationship."""
        self.assertEqual(self.detail1.particulars, 'Office Supplies')
        self.assertEqual(self.detail1.debit_amount, 500.00)
        self.assertEqual(self.detail1.credit_amount, 0.00)
        self.assertEqual(self.detail1.master, self.master1)
        self.assertEqual(str(self.detail1), 'Office Supplies')

    def test_meta_options(self):
        """Test Django Meta options like db_table and managed for detail model."""
        self.assertEqual(CapitalDetail._meta.db_table, 'tblACC_Capital_Details')
        self.assertFalse(CapitalDetail._meta.managed)
        self.assertEqual(CapitalDetail._meta.verbose_name, 'Capital Detail')
        self.assertEqual(CapitalDetail._meta.verbose_name_plural, 'Capital Details')
        self.assertEqual(CapitalDetail._meta.get_field('particulars').db_column, 'Particulars')
        self.assertEqual(CapitalDetail._meta.get_field('debit_amount').db_column, 'DebitAmt')
        self.assertEqual(CapitalDetail._meta.get_field('credit_amount').db_column, 'CreditAmt')
        self.assertEqual(CapitalDetail._meta.get_field('master').db_column, 'MId')

    def test_custom_manager_filter_by_context(self):
        """Test filtering logic in custom manager."""
        # Test filtering based on comp_id, fin_year_id, MId
        queryset = CapitalDetail.objects.filter_by_context(
            comp_id=self.master1.comp_id,
            fin_year_id=self.master1.fin_year_id,
            mid=self.master1.id
        )
        self.assertEqual(queryset.count(), 2)
        self.assertIn(self.detail1, queryset)
        self.assertIn(self.detail2, queryset)
        self.assertNotIn(self.detail3, queryset) # Should be excluded due to different MId

        # Test filtering with different fin_year_id (less than or equal)
        queryset_lt_eq = CapitalDetail.objects.filter_by_context(
            comp_id=self.master2.comp_id,
            fin_year_id=self.master2.fin_year_id,
            mid=self.master2.id
        )
        self.assertEqual(queryset_lt_eq.count(), 1)
        self.assertEqual(queryset_lt_eq.first(), self.detail3)

    def test_custom_manager_get_aggregated_totals(self):
        """Test aggregate calculations from filtered queryset."""
        queryset = CapitalDetail.objects.filter_by_context(
            comp_id=self.master1.comp_id,
            fin_year_id=self.master1.fin_year_id,
            mid=self.master1.id
        )
        totals = CapitalDetail.objects.get_aggregated_totals(queryset)
        self.assertAlmostEqual(totals['total_debit'], 500.00)
        self.assertAlmostEqual(totals['total_credit'], 750.00)

        # Test with empty queryset
        empty_queryset = CapitalDetail.objects.none()
        totals_empty = CapitalDetail.objects.get_aggregated_totals(empty_queryset)
        self.assertEqual(totals_empty['total_debit'], 0.0)
        self.assertEqual(totals_empty['total_credit'], 0.0)

    def test_calculate_balance_method(self):
        """Test custom model method for balance calculation."""
        self.assertAlmostEqual(self.detail1.calculate_balance(), 500.00)
        self.assertAlmostEqual(self.detail2.calculate_balance(), -750.00)

    def test_get_absolute_url(self):
        """Test get_absolute_url method returns correct URL."""
        expected_url = reverse_lazy('accounts:capitaldetail_edit', kwargs={'pk': self.detail1.pk})
        self.assertEqual(self.detail1.get_absolute_url(), expected_url)

class CapitalDetailViewsTest(TestCase):
    """
    Integration tests for CapitalDetail views, including HTMX interactions.
    """
    @classmethod
    def setUpTestData(cls):
        # Create master and detail data for all view tests
        cls.master = CapitalMaster.objects.create(
            id=101,
            comp_id=1,
            fin_year_id=2024
        )
        CapitalDetail.objects.create(
            id=1,
            particulars='Office Supplies',
            debit_amount=500.00,
            credit_amount=0.00,
            master=cls.master
        )
        CapitalDetail.objects.create(
            id=2,
            particulars='Electricity Bill',
            debit_amount=0.00,
            credit_amount=750.00,
            master=cls.master
        )

    def setUp(self):
        self.client = Client()
        # Set session variables required by the view's get_queryset method
        session = self.client.session
        session['compid'] = self.master.comp_id
        session['finyear'] = self.master.fin_year_id
        session.save()
        self.mid_param = self.master.id # Query string parameter

    def test_list_view_get(self):
        """Test the main list view renders correctly with data and totals."""
        response = self.client.get(reverse('accounts:capitaldetail_list'), {'MId': self.mid_param})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/capitaldetail/list.html')
        self.assertIn('capital_details', response.context)
        self.assertIn('total_debit', response.context)
        self.assertIn('total_credit', response.context)
        self.assertEqual(response.context['capital_details'].count(), 2)
        self.assertContains(response, 'Office Supplies')
        self.assertContains(response, '500.00')
        self.assertContains(response, 'Electricity Bill')
        self.assertContains(response, '750.00')
        self.assertContains(response, f"Total") # Check footer text
        self.assertContains(response, f"{500.00:.2f}") # Total Debit
        self.assertContains(response, f"{750.00:.2f}") # Total Credit

    def test_list_view_missing_params(self):
        """Test list view behavior when essential session/query params are missing."""
        # Remove compid from session
        self.client.session.pop('compid')
        self.client.session.save()
        response = self.client.get(reverse('accounts:capitaldetail_list'), {'MId': self.mid_param})
        self.assertEqual(response.status_code, 200)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Missing essential parameters (Company ID, Financial Year, MId).")
        self.assertEqual(response.context['capital_details'].count(), 0) # Should return empty queryset

    def test_table_partial_view_htmx(self):
        """Test the HTMX partial for the table content."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('accounts:capitaldetail_table'), {'MId': self.mid_param}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/capitaldetail/_capitaldetail_table.html')
        self.assertContains(response, '<table id="capitaldetailTable"')
        self.assertNotContains(response, '<!DOCTYPE html>') # Ensure it's a partial, not full HTML

    def test_create_view_get(self):
        """Test GET request for the create form."""
        response = self.client.get(reverse('accounts:capitaldetail_add'), {'MId': self.mid_param})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/capitaldetail/_capitaldetail_form.html')
        self.assertIn('form', response.context)
        # Check if master is pre-filled based on MId query param
        self.assertEqual(response.context['form'].initial['master'], self.master.id)

    def test_create_view_post_htmx_success(self):
        """Test successful HTMX POST request for creating a new detail."""
        new_master = CapitalMaster.objects.create(id=103, comp_id=1, fin_year_id=2024) # Create another master for testing
        data = {
            'particulars': 'New Test Item',
            'debit_amount': '150.00',
            'credit_amount': '0.00',
            'master': new_master.id,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('accounts:capitaldetail_add'), data, **headers)
        
        self.assertEqual(response.status_code, 204) # No content on successful HTMX POST
        self.assertTrue(CapitalDetail.objects.filter(particulars='New Test Item').exists()) # Verify object creation
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Capital Detail added successfully.')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCapitalDetailList')

    def test_create_view_post_htmx_invalid(self):
        """Test invalid HTMX POST request for create view (e.g., missing data)."""
        data = {
            'particulars': '', # Invalid
            'debit_amount': '',
            'credit_amount': '',
            'master': self.master.id,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('accounts:capitaldetail_add'), data, **headers)
        
        self.assertEqual(response.status_code, 200) # Should return form with errors
        self.assertTemplateUsed(response, 'accounts/capitaldetail/_capitaldetail_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'This field is required.') # Check for validation error

    def test_update_view_get(self):
        """Test GET request for the update form."""
        detail_to_edit = CapitalDetail.objects.get(id=1)
        response = self.client.get(reverse('accounts:capitaldetail_edit', args=[detail_to_edit.pk]), {'MId': self.mid_param})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/capitaldetail/_capitaldetail_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, detail_to_edit) # Form should be pre-filled
        self.assertContains(response, 'Office Supplies')

    def test_update_view_post_htmx_success(self):
        """Test successful HTMX POST request for updating an existing detail."""
        detail_to_update = CapitalDetail.objects.get(id=1)
        data = {
            'particulars': 'Updated Office Supplies',
            'debit_amount': '600.00',
            'credit_amount': '10.00',
            'master': detail_to_update.master.id,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('accounts:capitaldetail_edit', args=[detail_to_update.pk]), data, **headers)
        
        self.assertEqual(response.status_code, 204) # No content on successful HTMX POST
        detail_to_update.refresh_from_db() # Reload object to get updated data
        self.assertEqual(detail_to_update.particulars, 'Updated Office Supplies')
        self.assertAlmostEqual(float(detail_to_update.debit_amount), 600.00)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCapitalDetailList')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Capital Detail updated successfully.')

    def test_delete_view_get(self):
        """Test GET request for delete confirmation."""
        detail_to_delete = CapitalDetail.objects.get(id=1)
        response = self.client.get(reverse('accounts:capitaldetail_delete', args=[detail_to_delete.pk]), {'MId': self.mid_param})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/capitaldetail/_capitaldetail_confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, detail_to_delete.particulars)

    def test_delete_view_post_htmx_success(self):
        """Test successful HTMX DELETE request for deleting a detail."""
        detail_to_delete_id = CapitalDetail.objects.get(id=1).pk
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.delete(reverse('accounts:capitaldetail_delete', args=[detail_to_delete_id]), **headers) # Use client.delete for DELETE method
        
        self.assertEqual(response.status_code, 204) # No content on successful HTMX DELETE
        self.assertFalse(CapitalDetail.objects.filter(pk=detail_to_delete_id).exists()) # Verify object deletion
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCapitalDetailList')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Capital Detail deleted successfully.')

    def test_cancel_view_regular_redirect(self):
        """Test cancel view performs a standard HTTP redirect."""
        response = self.client.get(reverse('accounts:capitaldetail_cancel'), {'MId': self.mid_param})
        self.assertEqual(response.status_code, 302) # Should redirect
        self.assertRedirects(response, reverse('capital_particulars_list')) # Check redirect target
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), "Operation cancelled.")

    def test_cancel_view_htmx_redirect(self):
        """Test cancel view responds with HX-Redirect header for HTMX."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('accounts:capitaldetail_cancel'), {'MId': self.mid_param}, **headers)
        self.assertEqual(response.status_code, 200) # HTMX often returns 200 with HX-Redirect header
        self.assertIn('HX-Redirect', response.headers)
        self.assertEqual(response.headers['HX-Redirect'], reverse('capital_particulars_list')) # Check HX-Redirect URL
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), "Operation cancelled.")
```

## Step 5: HTMX and Alpine.js Integration

The generated Django code thoroughly integrates HTMX and Alpine.js to deliver a modern, interactive user experience:

-   **HTMX for Dynamic Content Loading and Actions:**
    -   The main `list.html` template uses `hx-get` to initially load and subsequently refresh the DataTable content (`_capitaldetail_table.html`) into a dedicated container. This is triggered on page load and by a custom `refreshCapitalDetailList` event (sent by CRUD operations).
    -   All "Add", "Edit", and "Delete" buttons leverage `hx-get` to fetch their respective forms (`_capitaldetail_form.html`) or confirmation dialogs (`_capitaldetail_confirm_delete.html`) into a central modal.
    -   Form submissions within the modal (`hx-post` for create/update, `hx-delete` for delete) use `hx-swap="none"`. The Django views respond with an `HTTP 204 No Content` status and an `HX-Trigger` header (`refreshCapitalDetailList`), which automatically signals the client to refresh the main table without a full page reload or complex JavaScript.
    -   The "Cancel" button, mimicking the original ASP.NET redirect, uses `hx-get` and `hx-push-url="true"` combined with a `HX-Redirect` header from the Django view, allowing for seamless navigation without a traditional page refresh when an HTMX request is detected.

-   **Alpine.js for UI State Management:**
    -   A simple Alpine.js setup manages the `hidden` state of the modal. `on click` directives are used to add/remove the `is-active` class, controlling modal visibility based on user interaction or HTMX events.
    -   An `on htmx:afterOnLoad` event listener is implemented to automatically hide the modal after a successful HTMX form submission (indicated by a 204 status code), providing a smooth user flow.

-   **DataTables for List Views:**
    -   The `_capitaldetail_table.html` partial includes a JavaScript snippet that initializes jQuery DataTables on the table element (`#capitaldetailTable`) once the partial is loaded by HTMX. This automatically provides client-side features like searching, sorting, and pagination for the list of capital details, significantly improving usability. The DataTables instance is re-initialized safely using `.destroy()` to prevent errors on subsequent HTMX loads.

## Final Notes

-   **Placeholders:** All generic placeholders like `[MODEL_NAME]` have been replaced with concrete names (`CapitalDetail`, `CapitalMaster`, `capitaldetail`).
-   **DRY Templates:** The use of partial templates (`_capitaldetail_table.html`, `_capitaldetail_form.html`, `_capitaldetail_confirm_delete.html`) loaded via HTMX ensures that UI components are reusable and maintainable. All main pages extend a conceptual `core/base.html` to maintain a consistent look and feel without duplicating boilerplate HTML.
-   **Business Logic in Models:** Complex data filtering and aggregation logic, previously in the ASP.NET code-behind, is now encapsulated within the `CapitalDetailManager` class in the `models.py` file. This strictly adheres to the "Fat Model, Thin View" architecture, making the application logic easier to understand, test, and maintain.
-   **Comprehensive Tests:** The provided unit and integration tests ensure that the models behave as expected and that all view interactions (including HTMX-driven CRUD operations and redirects) function correctly, promoting high code quality and reducing post-migration issues.
-   **Context Parameters:** The handling of `CompId`, `FinYearId` from session and `MId` from query string is crucial for replicating the original filtering logic. These are passed transparently through `request.session` and `request.GET` to the Django views and then to the model manager.
-   **Scalability:** This modern Django architecture provides a solid foundation for future enhancements, improved performance, and easier scaling compared to legacy ASP.NET Web Forms.