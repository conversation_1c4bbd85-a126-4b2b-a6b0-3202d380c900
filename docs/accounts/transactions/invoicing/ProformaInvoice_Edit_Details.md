## ASP.NET to Django Conversion Script: Proforma Invoice - Edit

This modernization plan outlines the strategy to transition your ASP.NET Proforma Invoice editing module to a modern Django application. Our approach prioritizes automation-driven migration, leveraging Django's robust framework, HTMX for dynamic interactions, and Alpine.js for a clean, efficient user experience, all while adhering to the 'Fat Model, Thin View' paradigm.

### Business Value of this Modernization:

*   **Improved User Experience:** A faster, more responsive interface with instant feedback thanks to HTMX, reducing page reloads and providing a smoother workflow for your users.
*   **Reduced Development Costs:** By adopting Django's "batteries-included" philosophy and a clear separation of concerns, future development, maintenance, and bug fixing become significantly more efficient.
*   **Enhanced Scalability and Performance:** Django's architecture and ORM provide a solid foundation for handling increased data volumes and user traffic, ensuring your application grows with your business needs.
*   **Simplified Codebase:** Moving from complex ASP.NET Web Forms lifecycle and verbose C# code to Django's clean, Pythonic structure makes the application easier to understand, maintain, and extend for your development teams.
*   **Future-Proofing:** Embracing modern web standards (HTMX, Alpine.js) and a popular, actively maintained framework like Django positions your application for long-term sustainability and easier integration with other systems.

---

## Conversion Steps:

### Step 1: Extract Database Schema

We've analyzed your ASP.NET code to identify the core database tables and their relationships. This application primarily interacts with two main tables: `tblACC_ProformaInvoice_Master` for the overall invoice details and `tblACC_ProformaInvoice_Details` for the line items (goods). Several lookup tables are also used to populate dropdowns and fetch customer information.

**Identified Tables and Key Fields:**

*   **`tblACC_ProformaInvoice_Master`**
    *   **Purpose:** Stores the main details of a Proforma Invoice (e.g., invoice number, dates, buyer/consignee information, taxation).
    *   **Key Fields:** `Id` (Primary Key), `InvoiceNo`, `DateOfIssueInvoice`, `PONo`, `POId`, `WONo`, `InvoiceMode`, `Buyer_name`, `Buyer_add`, `Buyer_city`, `Buyer_state`, `Buyer_country`, `Buyer_cotper`, `Buyer_ph`, `Buyer_email`, `Buyer_ecc`, `Buyer_tin` (TIN/CST), `Buyer_mob`, `Buyer_fax`, `Buyer_vat` (TIN/VAT), `Cong_name`, `Cong_add`, `Cong_city`, `Cong_state`, `Cong_country`, `Cong_cotper`, `Cong_ph`, `Cong_email`, `Cong_ecc`, `Cong_tin` (TIN/CST), `Cong_mob`, `Cong_fax`, `Cong_vat` (TIN/VAT), `AddType`, `AddAmt`, `DeductionType`, `Deduction`, `SysDate`, `SysTime`, `CompId`, `FinYearId`, `SessionId`.

*   **`tblACC_ProformaInvoice_Details`**
    *   **Purpose:** Stores individual line items (goods) associated with a Proforma Invoice.
    *   **Key Fields:** `Id` (Primary Key), `MId` (Foreign Key to `tblACC_ProformaInvoice_Master.Id`), `ItemId`, `InvoiceNo` (redundant), `Unit`, `Qty` (original PO quantity), `ReqQty` (requested/invoiced quantity), `AmtInPer` (amount in percentage), `Rate`.

*   **Supporting/Lookup Tables:**
    *   `tblACC_InvoiceAgainst` (for `InvoiceMode`)
    *   `SD_Cust_PO_Master` (for `PODate` based on `POId`)
    *   `SD_Cust_PO_Details` (for `ItemDesc` and `Unit` based on `ItemId`)
    *   `Unit_Master` (for unit `Symbol`s)
    *   `SD_Cust_master` (for `Buyer` and `Consignee` lookup/autofill)
    *   `tblCountryMaster`, `tblStateMaster`, `tblCityMaster` (for address dropdowns)
    *   `SD_Cust_WorkOrder_Master` (for `WONo` display)

### Step 2: Identify Backend Functionality

The ASP.NET code primarily focuses on **editing** an existing Proforma Invoice.

*   **Data Retrieval (Read):**
    *   Upon page load, all existing master and detail records are fetched from the database.
    *   Related information (PO Date, Work Order numbers, Invoice Mode, Item Descriptions, Unit Symbols) is retrieved through various SQL queries to populate the UI.
    *   Dropdown lists for Country, State, City are dynamically populated.
    *   Customer search functionality allows retrieving and pre-filling Buyer/Consignee details from `SD_Cust_master`.

*   **Data Modification (Update):**
    *   The `BtnUpdate_Click` event is the central point for saving changes.
    *   It updates the main `tblACC_ProformaInvoice_Master` record with the new Buyer, Consignee, date, and taxation details.
    *   It then iterates through the "Goods" grid, updating selected `tblACC_ProformaInvoice_Details` records for `ReqQty`, `Unit`, and `AmtInPer`.
    *   Crucially, it includes validation logic to ensure quantities and percentages remain within acceptable limits.

*   **Validation:**
    *   Client-side validators (e.g., `RequiredFieldValidator`, `RegularExpressionValidator` for dates, emails, and numeric formats) are present in the ASP.NET Markup.
    *   Server-side validation in `BtnUpdate_Click` checks email formats, date formats, numeric inputs, and complex business rules for quantity (`ReqQty`) and percentage (`AmtInPer`) updates in the goods section.

*   **Dynamic UI Interactions:**
    *   Tab switching to navigate between sections (Buyer, Consignee, Goods, Taxation).
    *   Cascading dropdowns for Country -> State -> City selection.
    *   Autocomplete for Buyer/Consignee names.
    *   "Search" and "Copy from buyer" buttons to populate consignee details.
    *   Toggle functionality in the "Goods" grid to switch between display mode (labels) and edit mode (textboxes/dropdowns) for each row.

### Step 3: Infer UI Components

We've mapped the ASP.NET controls to their modern Django, HTMX, and Alpine.js equivalents:

*   **`MasterPage.master`:** Will be replaced by Django's template inheritance, extending a `core/base.html` that handles the overall layout, CSS (Tailwind CSS), and core JavaScript (HTMX, Alpine.js, DataTables).
*   **`asp:Content` Placeholders:** Replaced by Django `{% block %}` tags.
*   **`asp:Label`:** Simple Django template variables `{{ object.field }}`.
*   **`asp:TextBox`:** Django `forms.TextInput`, `forms.Textarea`, `forms.NumberInput`.
*   **`asp:DropDownList`:** Django `forms.Select`.
*   **`cc1:CalendarExtender`:** Replaced by `type="date"` on the input field, allowing the browser to handle date picking, or a lightweight Alpine.js/HTMX-compatible date picker.
*   **`cc1:TabContainer`:** Implemented using Alpine.js for managing tab state and HTMX for lazy loading of tab content if desired (though for an "edit" view, initial load is fine).
*   **`asp:Button`, `asp:LinkButton`:** Standard HTML `<button>` elements, with `hx-*` attributes for HTMX-driven actions (e.g., submitting forms, navigating tabs, triggering partial updates).
*   **`asp:GridView`:** A standard HTML `<table>` structured with DataTables.js for client-side features (search, sort, paginate). HTMX will be used for partial updates to the table rows for inline editing.
*   **`cc1:AutoCompleteExtender`:** Replaced by HTMX requests to a Django view, returning HTML fragments for autocomplete suggestions. Alpine.js can manage the display of these suggestions.
*   **`asp:RequiredFieldValidator`, `asp:RegularExpressionValidator`:** Validation logic will be moved to Django forms (`ModelForm` `clean_field` methods) and potentially client-side JavaScript for immediate feedback.

---

## Step 4: Generate Django Code

The following Django application structure will be created under an `accounts` module (e.g., `accounts/proformainvoice`).

### 4.1 Models (`accounts/proformainvoice/models.py`)

Models are designed to map directly to your existing database tables using `managed = False` and `db_table`. Business logic, such as retrieving related PO dates, work order numbers, and handling complex quantity/percentage validations, is encapsulated within model methods.

```python
from django.db import models
from django.utils import timezone
import datetime

# --- Shared Master Data Models (assuming they exist or will be migrated) ---
# For demonstration purposes, these are included here. In a real ERP,
# they might reside in a 'core' or 'masters' Django app.

class Country(models.Model):
    id = models.IntegerField(db_column='CId', primary_key=True)
    name = models.CharField(db_column='CName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblCountryMaster'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.name

class State(models.Model):
    id = models.IntegerField(db_column='SId', primary_key=True)
    name = models.CharField(db_column='SName', max_length=255)
    country = models.ForeignKey(Country, models.DO_NOTHING, db_column='CId')

    class Meta:
        managed = False
        db_table = 'tblStateMaster'
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.name

class City(models.Model):
    id = models.IntegerField(db_column='CityId', primary_key=True)
    name = models.CharField(db_column='CityName', max_length=255)
    state = models.ForeignKey(State, models.DO_NOTHING, db_column='SId')

    class Meta:
        managed = False
        db_table = 'tblCityMaster'
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.name

class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class CustomerMaster(models.Model):
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    material_del_address = models.TextField(db_column='MaterialDelAddress', blank=True, null=True)
    material_del_country = models.ForeignKey(Country, models.DO_NOTHING, db_column='MaterialDelCountry', blank=True, null=True, related_name='customer_material_del_country')
    material_del_state = models.ForeignKey(State, models.DO_NOTHING, db_column='MaterialDelState', blank=True, null=True, related_name='customer_material_del_state')
    material_del_city = models.ForeignKey(City, models.DO_NOTHING, db_column='MaterialDelCity', blank=True, null=True, related_name='customer_material_del_city')
    material_del_contact_no = models.CharField(db_column='MaterialDelContactNo', max_length=50, blank=True, null=True)
    material_del_fax_no = models.CharField(db_column='MaterialDelFaxNo', max_length=50, blank=True, null=True)
    contact_person = models.CharField(db_column='ContactPerson', max_length=255, blank=True, null=True)
    email = models.CharField(db_column='Email', max_length=255, blank=True, null=True)
    tin_vat_no = models.CharField(db_column='TinVatNo', max_length=100, blank=True, null=True)
    ecc_no = models.CharField(db_column='EccNo', max_length=100, blank=True, null=True)
    contact_no = models.CharField(db_column='ContactNo', max_length=50, blank=True, null=True)
    tin_cst_no = models.CharField(db_column='TinCstNo', max_length=100, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer Master'
        verbose_name_plural = 'Customer Masters'

    def __str__(self):
        return f"{self.customer_name} [{self.customer_id}]"

class InvoiceAgainst(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    against = models.CharField(db_column='Against', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblACC_InvoiceAgainst'
        verbose_name = 'Invoice Against'
        verbose_name_plural = 'Invoice Against'

    def __str__(self):
        return self.against

class PurchaseOrderDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    po_id = models.CharField(db_column='POId', max_length=50) 
    item_desc = models.TextField(db_column='ItemDesc')
    unit = models.ForeignKey(UnitMaster, models.DO_NOTHING, db_column='Unit')

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Details'
        verbose_name = 'Purchase Order Detail'
        verbose_name_plural = 'Purchase Order Details'

    def __str__(self):
        return f"{self.item_desc} ({self.po_id})"

# --- Core Proforma Invoice Models ---

class ProformaInvoiceMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate', blank=True, null=True)
    sys_time = models.TimeField(db_column='SysTime', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)
    invoice_no = models.CharField(db_column='InvoiceNo', max_length=50, blank=True, null=True)
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    po_id = models.CharField(db_column='POId', max_length=50, blank=True, null=True)
    wo_no = models.TextField(db_column='WONo', blank=True, null=True)
    invoice_mode = models.ForeignKey(InvoiceAgainst, models.DO_NOTHING, db_column='InvoiceMode', blank=True, null=True)
    date_of_issue_invoice = models.DateField(db_column='DateOfIssueInvoice', blank=True, null=True)
    customer_code = models.CharField(db_column='CustomerCode', max_length=50, blank=True, null=True)

    # Buyer Details
    buyer_name = models.CharField(db_column='Buyer_name', max_length=255, blank=True, null=True)
    buyer_address = models.TextField(db_column='Buyer_add', blank=True, null=True)
    buyer_city = models.ForeignKey(City, models.DO_NOTHING, db_column='Buyer_city', blank=True, null=True, related_name='proforma_buyer_city')
    buyer_state = models.ForeignKey(State, models.DO_NOTHING, db_column='Buyer_state', blank=True, null=True, related_name='proforma_buyer_state')
    buyer_country = models.ForeignKey(Country, models.DO_NOTHING, db_column='Buyer_country', blank=True, null=True, related_name='proforma_buyer_country')
    buyer_contact_person = models.CharField(db_column='Buyer_cotper', max_length=255, blank=True, null=True)
    buyer_phone = models.CharField(db_column='Buyer_ph', max_length=50, blank=True, null=True)
    buyer_email = models.CharField(db_column='Buyer_email', max_length=255, blank=True, null=True)
    buyer_ecc = models.CharField(db_column='Buyer_ecc', max_length=100, blank=True, null=True)
    buyer_tin_cst = models.CharField(db_column='Buyer_tin', max_length=100, blank=True, null=True)
    buyer_mobile = models.CharField(db_column='Buyer_mob', max_length=50, blank=True, null=True)
    buyer_fax = models.CharField(db_column='Buyer_fax', max_length=50, blank=True, null=True)
    buyer_tin_vat = models.CharField(db_column='Buyer_vat', max_length=100, blank=True, null=True)

    # Consignee Details
    consignee_name = models.CharField(db_column='Cong_name', max_length=255, blank=True, null=True)
    consignee_address = models.TextField(db_column='Cong_add', blank=True, null=True)
    consignee_city = models.ForeignKey(City, models.DO_NOTHING, db_column='Cong_city', blank=True, null=True, related_name='proforma_consignee_city')
    consignee_state = models.ForeignKey(State, models.DO_NOTHING, db_column='Cong_state', blank=True, null=True, related_name='proforma_consignee_state')
    consignee_country = models.ForeignKey(Country, models.DO_NOTHING, db_column='Cong_country', blank=True, null=True, related_name='proforma_consignee_country')
    consignee_contact_person = models.CharField(db_column='Cong_cotper', max_length=255, blank=True, null=True)
    consignee_phone = models.CharField(db_column='Cong_ph', max_length=50, blank=True, null=True)
    consignee_email = models.CharField(db_column='Cong_email', max_length=255, blank=True, null=True)
    consignee_ecc = models.CharField(db_column='Cong_ecc', max_length=100, blank=True, null=True)
    consignee_tin_cst = models.CharField(db_column='Cong_tin', max_length=100, blank=True, null=True)
    consignee_mobile = models.CharField(db_column='Cong_mob', max_length=50, blank=True, null=True)
    consignee_fax = models.CharField(db_column='Cong_fax', max_length=50, blank=True, null=True)
    consignee_tin_vat = models.CharField(db_column='Cong_vat', max_length=100, blank=True, null=True)

    # Taxation Details
    add_type = models.IntegerField(db_column='AddType', blank=True, null=True)
    add_amount = models.FloatField(db_column='AddAmt', blank=True, null=True)
    deduction_type = models.IntegerField(db_column='DeductionType', blank=True, null=True)
    deduction = models.FloatField(db_column='Deduction', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_ProformaInvoice_Master'
        verbose_name = 'Proforma Invoice'
        verbose_name_plural = 'Proforma Invoices'

    def __str__(self):
        return self.invoice_no or f"Invoice {self.id}"

    # Business logic methods (replaces ASP.NET 'fun' class functionality)
    def get_po_date(self, comp_id):
        """Retrieves the PO Date from SD_Cust_PO_Master."""
        from django.db import connection
        try:
            with connection.cursor() as cursor:
                cursor.execute(f"SELECT PODate FROM SD_Cust_PO_Master WHERE POId='{self.po_id}' AND CompId='{comp_id}'")
                result = cursor.fetchone()
                return result[0] if result else None
        except Exception:
            return None

    def get_work_order_numbers(self, comp_id):
        """Retrieves comma-separated Work Order numbers from SD_Cust_WorkOrder_Master."""
        if not self.wo_no:
            return ""
        wo_ids = [wo.strip() for wo in self.wo_no.split(',') if wo.strip()]
        if not wo_ids:
            return ""
        
        from django.db import connection
        try:
            with connection.cursor() as cursor:
                placeholders = ','.join(['%s'] * len(wo_ids))
                query = f"SELECT WONo FROM SD_Cust_WorkOrder_Master WHERE Id IN ({placeholders}) AND CompId='{comp_id}'"
                cursor.execute(query, wo_ids)
                results = cursor.fetchall()
                return ",".join([r[0] for r in results])
        except Exception:
            return ""

    def get_invoice_mode_display(self):
        """Returns the display name for the invoice mode."""
        return self.invoice_mode.against if self.invoice_mode else ""

    def load_buyer_consignee_from_customer(self, customer_code, is_buyer=True, comp_id=None):
        """Populates buyer/consignee fields from CustomerMaster data."""
        try:
            customer = CustomerMaster.objects.get(customer_id=customer_code, comp_id=comp_id)
            if is_buyer:
                self.buyer_address = customer.material_del_address
                self.buyer_country = customer.material_del_country
                self.buyer_state = customer.material_del_state
                self.buyer_city = customer.material_del_city
                self.buyer_fax = customer.material_del_fax_no
                self.buyer_contact_person = customer.contact_person
                self.buyer_phone = customer.material_del_contact_no
                self.buyer_tin_cst = customer.tin_cst_no
                self.buyer_tin_vat = customer.tin_vat_no
                self.buyer_mobile = customer.contact_no
                self.buyer_email = customer.email
                self.buyer_ecc = customer.ecc_no
            else: # Consignee
                self.consignee_name = customer.customer_name 
                self.consignee_address = customer.material_del_address
                self.consignee_country = customer.material_del_country
                self.consignee_state = customer.material_del_state
                self.consignee_city = customer.material_del_city
                self.consignee_fax = customer.material_del_fax_no
                self.consignee_contact_person = customer.contact_person
                self.consignee_phone = customer.material_del_contact_no
                self.consignee_tin_cst = customer.tin_cst_no
                self.consignee_tin_vat = customer.tin_vat_no
                self.consignee_mobile = customer.contact_no
                self.consignee_email = customer.email
                self.consignee_ecc = customer.ecc_no
            return True
        except CustomerMaster.DoesNotExist:
            return False

    def update_master_invoice_data(self, form_cleaned_data, comp_id):
        """Updates the ProformaInvoiceMaster record."""
        # Update fields from the form
        for field, value in form_cleaned_data.items():
            if hasattr(self, field):
                setattr(self, field, value)
        
        # Update system fields
        self.sys_date = timezone.now().date()
        self.sys_time = timezone.now().time()
        self.session_id = 'django_user_session' # Replace with actual session ID
        
        self.save()
        return True

class ProformaInvoiceDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(ProformaInvoiceMaster, models.DO_NOTHING, db_column='MId', related_name='details')
    item = models.ForeignKey(PurchaseOrderDetail, models.DO_NOTHING, db_column='ItemId')
    invoice_no = models.CharField(db_column='InvoiceNo', max_length=50, blank=True, null=True)
    unit = models.ForeignKey(UnitMaster, models.DO_NOTHING, db_column='Unit')
    qty = models.FloatField(db_column='Qty') # Original PO Quantity for this item from PO Details
    req_qty = models.FloatField(db_column='ReqQty')
    amt_in_per = models.FloatField(db_column='AmtInPer')
    rate = models.FloatField(db_column='Rate')

    class Meta:
        managed = False
        db_table = 'tblACC_ProformaInvoice_Details'
        verbose_name = 'Proforma Invoice Detail'
        verbose_name_plural = 'Proforma Invoice Details'

    def __str__(self):
        return f"{self.master.invoice_no} - {self.item.item_desc}"

    def get_remaining_qty(self, comp_id):
        """Calculates remaining quantity for the item, considering all invoiced quantities."""
        from django.db.models import Sum
        total_invoiced_qty = ProformaInvoiceDetail.objects.filter(
            item=self.item,
            master__comp_id=comp_id
        ).aggregate(sum_req_qty=Sum('req_qty'))['sum_req_qty'] or 0

        return self.qty - total_invoiced_qty

    def get_item_unit_symbol(self):
        """Returns the symbol for the item's original unit from PO."""
        return self.item.unit.symbol if self.item and self.item.unit else ''

    def get_current_unit_symbol(self):
        """Returns the symbol for the detail's current unit."""
        return self.unit.symbol if self.unit else ''

    def update_detail_item(self, req_qty, unit_id, amt_in_per, comp_id):
        """Updates this specific detail item with validation logic."""
        from django.db.models import Sum
        # Replicate ASP.NET validation logic
        original_qty = self.qty # Original Qty from PO
        
        # Check against total allocated quantity for this item across ALL related proforma invoices
        # (original_qty - sum_of_other_req_qty) >= new_req_qty
        other_inv_qty_for_item = ProformaInvoiceDetail.objects.filter(
            item=self.item,
            master__comp_id=comp_id
        ).exclude(id=self.id).aggregate(sum_req_qty=Sum('req_qty'))['sum_req_qty'] or 0
        
        if (other_inv_qty_for_item + req_qty) > original_qty:
            raise ValueError(f"Total requested quantity for this item ({req_qty}) exceeds available quantity. Remaining: {original_qty - other_inv_qty_for_item}")

        # Check against total percentage within THIS proforma invoice for THIS item
        # (100 - sum_of_other_amt_in_per_in_this_invoice) >= new_amt_in_per
        total_amt_in_per_for_item_in_this_invoice = ProformaInvoiceDetail.objects.filter(
            master=self.master,
            item=self.item
        ).exclude(id=self.id).aggregate(sum_amt=Sum('amt_in_per'))['sum_amt'] or 0

        if (total_amt_in_per_for_item_in_this_invoice + amt_in_per) > 100:
            raise ValueError("Total percentage for this item exceeds 100%.")

        self.req_qty = req_qty
        self.unit_id = unit_id
        self.amt_in_per = amt_in_per
        self.save()
        return True

```

### 4.2 Forms (`accounts/proformainvoice/forms.py`)

Django forms handle data validation and rendering of input fields. `ModelForm` is used for easy mapping to models, and custom validation methods are added to replicate the ASP.NET validation logic.

```python
from django import forms
from django.core.exceptions import ValidationError
import re
from .models import ProformaInvoiceMaster, ProformaInvoiceDetail, Country, State, City, UnitMaster

# Form for the main Proforma Invoice Master details (Buyer, Consignee, Taxation tabs)
class ProformaInvoiceMasterForm(forms.ModelForm):
    # Hidden field to manage current active tab in the UI
    current_tab = forms.CharField(widget=forms.HiddenInput(), required=False)

    class Meta:
        model = ProformaInvoiceMaster
        # Fields correspond to the ASPX form fields for master data
        fields = [
            'date_of_issue_invoice',
            # Buyer Fields
            'buyer_name', 'buyer_address', 'buyer_country', 'buyer_state', 'buyer_city',
            'buyer_contact_person', 'buyer_phone', 'buyer_email', 'buyer_ecc',
            'buyer_tin_cst', 'buyer_mobile', 'buyer_fax', 'buyer_tin_vat',
            # Consignee Fields
            'consignee_name', 'consignee_address', 'consignee_country', 'consignee_state', 'consignee_city',
            'consignee_contact_person', 'consignee_phone', 'consignee_email', 'consignee_ecc',
            'consignee_tin_cst', 'consignee_mobile', 'consignee_fax', 'consignee_tin_vat',
            # Taxation Fields
            'add_type', 'add_amount', 'deduction_type', 'deduction',
        ]
        widgets = {
            'date_of_issue_invoice': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            # Buyer Widgets (Tailwind CSS classes for styling, HTMX attributes for dynamic behavior)
            'buyer_name': forms.TextInput(attrs={'class': 'box3 w-full', 'placeholder': 'Buyer Name', 'autocomplete': 'off', 'hx-post': '/proformainvoice/autocomplete-customer/', 'hx-trigger': 'keyup changed delay:500ms', 'hx-target': '#autocomplete-results-buyer', 'hx-swap': 'innerHTML', 'hx-indicator': '.htmx-indicator'}),
            'buyer_address': forms.Textarea(attrs={'class': 'box3 w-full h-28'}),
            'buyer_country': forms.Select(attrs={'class': 'box3 w-full', 'hx-get': '/proformainvoice/get-states/', 'hx-target': '#id_buyer_state', 'hx-trigger': 'change', 'hx-include': '[name="buyer_country"]'}),
            'buyer_state': forms.Select(attrs={'class': 'box3 w-full', 'hx-get': '/proformainvoice/get-cities/', 'hx-target': '#id_buyer_city', 'hx-trigger': 'change', 'hx-include': '[name="buyer_state"]'}),
            'buyer_city': forms.Select(attrs={'class': 'box3 w-full'}),
            'buyer_contact_person': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'buyer_phone': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'buyer_email': forms.EmailInput(attrs={'class': 'box3 w-full'}),
            'buyer_ecc': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'buyer_tin_cst': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'buyer_mobile': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'buyer_fax': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'buyer_tin_vat': forms.TextInput(attrs={'class': 'box3 w-full'}),
            # Consignee Widgets (similar to Buyer, with HTMX for dynamics)
            'consignee_name': forms.TextInput(attrs={'class': 'box3 w-full', 'placeholder': 'Consignee Name', 'autocomplete': 'off', 'hx-post': '/proformainvoice/autocomplete-customer/', 'hx-trigger': 'keyup changed delay:500ms', 'hx-target': '#autocomplete-results-consignee', 'hx-swap': 'innerHTML', 'hx-indicator': '.htmx-indicator'}),
            'consignee_address': forms.Textarea(attrs={'class': 'box3 w-full h-28'}),
            'consignee_country': forms.Select(attrs={'class': 'box3 w-full', 'hx-get': '/proformainvoice/get-states/', 'hx-target': '#id_consignee_state', 'hx-trigger': 'change', 'hx-include': '[name="consignee_country"]'}),
            'consignee_state': forms.Select(attrs={'class': 'box3 w-full', 'hx-get': '/proformainvoice/get-cities/', 'hx-target': '#id_consignee_city', 'hx-trigger': 'change', 'hx-include': '[name="consignee_state"]'}),
            'consignee_city': forms.Select(attrs={'class': 'box3 w-full'}),
            'consignee_contact_person': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'consignee_phone': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'consignee_email': forms.EmailInput(attrs={'class': 'box3 w-full'}),
            'consignee_ecc': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'consignee_tin_cst': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'consignee_mobile': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'consignee_fax': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'consignee_tin_vat': forms.TextInput(attrs={'class': 'box3 w-full'}),
            # Taxation Widgets
            'add_type': forms.Select(attrs={'class': 'box3 w-full'}, choices=[(0, 'Amt(Rs)'), (1, 'Per(%)')]),
            'add_amount': forms.NumberInput(attrs={'class': 'box3 w-full', 'step': '0.01'}),
            'deduction_type': forms.Select(attrs={'class': 'box3 w-full'}, choices=[(0, 'Amt(Rs)'), (1, 'Per(%)')]),
            'deduction': forms.NumberInput(attrs={'class': 'box3 w-full', 'step': '0.01'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate country dropdowns with initial data
        self.fields['buyer_country'].queryset = Country.objects.all().order_by('name')
        self.fields['buyer_country'].choices = [(c.id, c.name) for c in Country.objects.all().order_by('name')]
        self.fields['buyer_country'].choices.insert(0, ('', '--- Select Country ---'))
        
        self.fields['consignee_country'].queryset = Country.objects.all().order_by('name')
        self.fields['consignee_country'].choices = [(c.id, c.name) for c in Country.objects.all().order_by('name')]
        self.fields['consignee_country'].choices.insert(0, ('', '--- Select Country ---'))

        # If editing an existing instance, pre-populate dependent dropdowns
        if self.instance and self.instance.pk:
            if self.instance.buyer_country:
                self.fields['buyer_state'].queryset = State.objects.filter(country=self.instance.buyer_country).order_by('name')
                self.fields['buyer_state'].choices = [(s.id, s.name) for s in self.fields['buyer_state'].queryset]
            if self.instance.buyer_state:
                self.fields['buyer_city'].queryset = City.objects.filter(state=self.instance.buyer_state).order_by('name')
                self.fields['buyer_city'].choices = [(c.id, c.name) for c in self.fields['buyer_city'].queryset]
            
            if self.instance.consignee_country:
                self.fields['consignee_state'].queryset = State.objects.filter(country=self.instance.consignee_country).order_by('name')
                self.fields['consignee_state'].choices = [(s.id, s.name) for s in self.fields['consignee_state'].queryset]
            if self.instance.consignee_state:
                self.fields['consignee_city'].queryset = City.objects.filter(state=self.instance.consignee_state).order_by('name')
                self.fields['consignee_city'].choices = [(c.id, c.name) for c in self.fields['consignee_city'].queryset]

        # Ensure all dependent dropdowns have a default "Select" option
        if not self.fields['buyer_state'].choices:
            self.fields['buyer_state'].choices = [('', '--- Select State ---')]
        if not self.fields['buyer_city'].choices:
            self.fields['buyer_city'].choices = [('', '--- Select City ---')]
        if not self.fields['consignee_state'].choices:
            self.fields['consignee_state'].choices = [('', '--- Select State ---')]
        if not self.fields['consignee_city'].choices:
            self.fields['consignee_city'].choices = [('', '--- Select City ---')]

    # Custom validation methods, mirroring ASP.NET validators
    def clean_add_amount(self):
        amount = self.cleaned_data['add_amount']
        if amount is not None and not re.match(r"^\d{1,15}(\.\d{0,3})?$", str(amount)):
            raise ValidationError("Invalid Add Amount format. Up to 15 digits, 3 decimal places allowed.")
        return amount

    def clean_deduction(self):
        amount = self.cleaned_data['deduction']
        if amount is not None and not re.match(r"^\d{1,15}(\.\d{0,3})?$", str(amount)):
            raise ValidationError("Invalid Deduction Amount format. Up to 15 digits, 3 decimal places allowed.")
        return amount

    def clean_date_of_issue_invoice(self):
        date_val = self.cleaned_data['date_of_issue_invoice']
        if not date_val:
            raise ValidationError("Date of Issue of Invoice is required.")
        return date_val

    def clean_buyer_email(self):
        email = self.cleaned_data['buyer_email']
        if email and not re.match(r"^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$", email):
            raise ValidationError("Invalid Buyer Email format.")
        return email

    def clean_consignee_email(self):
        email = self.cleaned_data['consignee_email']
        if email and not re.match(r"^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$", email):
            raise ValidationError("Invalid Consignee Email format.")
        return email

# Form for individual Proforma Invoice Detail items (for inline editing in DataTables)
class ProformaInvoiceDetailForm(forms.ModelForm):
    # Hidden fields to pass context needed for validation (original quantity, item ID)
    original_qty = forms.FloatField(widget=forms.HiddenInput(), required=False)
    item_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    class Meta:
        model = ProformaInvoiceDetail
        fields = ['id', 'req_qty', 'unit', 'amt_in_per']
        widgets = {
            'req_qty': forms.NumberInput(attrs={'class': 'box3 w-full', 'step': '0.001', 'min': '0'}),
            'unit': forms.Select(attrs={'class': 'box3 w-full'}),
            'amt_in_per': forms.NumberInput(attrs={'class': 'box3 w-full', 'step': '0.01', 'min': '0', 'max': '100'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['unit'].queryset = UnitMaster.objects.all().order_by('symbol')
        self.fields['unit'].choices = [(u.id, u.symbol) for u in UnitMaster.objects.all().order_by('symbol')]
        
        # Prefill hidden fields if an instance is provided (for editing)
        if self.instance and self.instance.pk:
            self.fields['original_qty'].initial = self.instance.qty
            self.fields['item_id'].initial = self.instance.item_id
        
    def clean_req_qty(self):
        req_qty = self.cleaned_data['req_qty']
        original_qty = self.cleaned_data.get('original_qty')
        item_id = self.cleaned_data.get('item_id')
        
        # Numeric format validation
        if req_qty is not None and not re.match(r"^\d{1,15}(\.\d{0,3})?$", str(req_qty)):
            raise ValidationError("Invalid Requested Quantity format. Up to 15 digits, 3 decimal places allowed.")

        # Business logic validation for quantity (replicates ASP.NET `(rmnqty + ReqQty) >= txtreqQty` logic)
        # This checks if the new `req_qty` plus all previously invoiced quantities for this item
        # (excluding the current row's existing quantity) exceeds the original PO quantity.
        if self.instance and original_qty is not None and item_id is not None:
            # Placeholder for comp_id. In a real app, retrieve from request.session.
            comp_id = 1 

            other_invoiced_qty = ProformaInvoiceDetail.objects.filter(
                item=item_id,
                master__comp_id=comp_id
            ).exclude(id=self.instance.id).aggregate(sum_req_qty=forms.models.Sum('req_qty'))['sum_req_qty'] or 0

            if (other_invoiced_qty + req_qty) > original_qty:
                raise ValidationError(f"Total requested quantity for this item ({req_qty}) exceeds available quantity. Remaining: {original_qty - other_invoiced_qty}")
        return req_qty

    def clean_amt_in_per(self):
        amt_in_per = self.cleaned_data['amt_in_per']
        # Numeric format validation
        if amt_in_per is not None and not re.match(r"^\d{1,15}(\.\d{0,3})?$", str(amt_in_per)):
            raise ValidationError("Invalid Amount In Percentage format. Up to 15 digits, 3 decimal places allowed.")

        # Business logic validation for amount in percentage (replicates ASP.NET `Amt <= (lblAmt + RemnAmt)` logic)
        # This ensures the total percentage for this item within *this* invoice does not exceed 100%.
        if self.instance:
            total_amt_for_item_in_this_invoice = ProformaInvoiceDetail.objects.filter(
                master=self.instance.master,
                item=self.instance.item
            ).exclude(id=self.instance.id).aggregate(sum_amt=forms.models.Sum('amt_in_per'))['sum_amt'] or 0

            if (total_amt_for_item_in_this_invoice + amt_in_per) > 100:
                raise ValidationError("Total percentage for this item exceeds 100%.")

        return amt_in_per

```

### 4.3 Views (`accounts/proformainvoice/views.py`)

Views are kept 'thin' by delegating complex data operations and business logic to model methods. Django's Class-Based Views (CBVs) are used for structured and efficient handling of requests. HTMX endpoints provide dynamic content updates without full page reloads.

```python
from django.views.generic import UpdateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, render
import re # For parsing customer name from autocomplete
from django.db.models import Sum

from .models import ProformaInvoiceMaster, ProformaInvoiceDetail, CustomerMaster, Country, State, City, UnitMaster, InvoiceAgainst, PurchaseOrderDetail
from .forms import ProformaInvoiceMasterForm, ProformaInvoiceDetailForm

# Helper to get session details (e.g., company ID). In a real ERP, this would come from authentication.
def get_session_details(request):
    """Placeholder for retrieving session-specific details like company ID."""
    return {
        'comp_id': request.session.get('compid', 1),  # Default to 1 for testing/demonstration
        'finyear_id': request.session.get('finyear', 1),
        'username': request.session.get('username', 'django_user')
    }

class ProformaInvoiceEditView(UpdateView):
    """
    Main view for editing Proforma Invoice Master details.
    Handles initial data loading and updates for the master record.
    """
    model = ProformaInvoiceMaster
    form_class = ProformaInvoiceMasterForm
    template_name = 'proformainvoice/proformainvoice_edit.html'
    context_object_name = 'proforma_invoice'
    success_url = reverse_lazy('proformainvoice_list_placeholder') # Redirect target after full form submission

    def get_object(self, queryset=None):
        """Retrieves the Proforma Invoice Master instance based on URL parameters."""
        pk = self.kwargs.get('pk')
        # ASP.NET used 'InvNo' from querystring; we include it here for robust lookup
        inv_no = self.request.GET.get('InvNo') 
        
        return get_object_or_404(
            ProformaInvoiceMaster,
            pk=pk,
            invoice_no=inv_no,
            comp_id=get_session_details(self.request)['comp_id']
        )

    def get_context_data(self, **kwargs):
        """Adds additional context for the template, including related data and active tab."""
        context = super().get_context_data(**kwargs)
        session_details = get_session_details(self.request)
        comp_id = session_details['comp_id']

        proforma_invoice = self.object 
        context['invoice_mode_display'] = proforma_invoice.get_invoice_mode_display()
        context['po_date'] = proforma_invoice.get_po_date(comp_id)
        context['wo_numbers'] = proforma_invoice.get_work_order_numbers(comp_id)
        
        # Initial active tab from session, simulating ASP.NET's TabContainer1.ActiveTabIndex
        context['active_tab_index'] = self.request.session.get('TabIndex', 0)
        
        return context
    
    def post(self, request, *args, **kwargs):
        """Handles POST requests for updating the Proforma Invoice Master."""
        self.object = self.get_object()
        form = self.get_form()

        if form.is_valid():
            # Update master data using the model method (fat model)
            master_data_for_update = {
                field: form.cleaned_data[field]
                for field in form.fields if field not in ['current_tab']
            }
            self.object.update_master_invoice_data(master_data_for_update, get_session_details(request)['comp_id'])
            
            messages.success(self.request, 'Proforma Invoice Master updated successfully.')
            
            # For HTMX requests, return 204 No Content and trigger a client-side event to refresh UI.
            if self.request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204,
                    headers={'HX-Trigger': 'refreshProformaInvoiceEdit'}
                )
            return super().form_valid(form) # Standard redirect for non-HTMX requests
        else:
            # If form is invalid, re-render the template with errors.
            # Store the active tab to ensure the correct tab is displayed on re-render.
            active_tab = request.POST.get('current_tab', 0)
            request.session['TabIndex'] = active_tab 
            messages.error(self.request, 'Please correct the errors in the form.')
            return self.form_invalid(form) # Render form again with errors

class GetStatesView(View):
    """HTMX endpoint to dynamically load states based on selected country."""
    def get(self, request, *args, **kwargs):
        country_id = request.GET.get('buyer_country') or request.GET.get('consignee_country')
        states = State.objects.filter(country_id=country_id).order_by('name')
        options = '<option value="">--- Select State ---</option>'
        for state in states:
            options += f'<option value="{state.id}">{state.name}</option>'
        return HttpResponse(options)

class GetCitiesView(View):
    """HTMX endpoint to dynamically load cities based on selected state."""
    def get(self, request, *args, **kwargs):
        state_id = request.GET.get('buyer_state') or request.GET.get('consignee_state')
        cities = City.objects.filter(state_id=state_id).order_by('name')
        options = '<option value="">--- Select City ---</option>'
        for city in cities:
            options += f'<option value="{city.id}">{city.name}</option>'
        return HttpResponse(options)

class AutocompleteCustomerView(View):
    """HTMX endpoint for customer name autocomplete suggestions."""
    def post(self, request, *args, **kwargs):
        prefix_text = request.POST.get('prefix_text', '')
        comp_id = get_session_details(request)['comp_id']
        
        if len(prefix_text) < 1: 
            return HttpResponse('')

        customers = CustomerMaster.objects.filter(
            customer_name__icontains=prefix_text,
            comp_id=comp_id
        ).order_by('customer_name')[:10] # Limit results for performance
        
        options = []
        for customer in customers:
            options.append(f'<div class="autocomplete-item px-3 py-2 cursor-pointer hover:bg-gray-200" data-value="{customer.customer_id}">{customer.customer_name} [{customer.customer_id}]</div>')
        return HttpResponse(''.join(options))

class GetCustomerDetailsView(View):
    """HTMX endpoint to fetch and return full customer details for form pre-filling."""
    def post(self, request, *args, **kwargs):
        customer_name_with_id = request.POST.get('customer_name', '') 
        customer_id_match = re.search(r'\[(.*?)\]', customer_name_with_id)
        customer_code = customer_id_match.group(1) if customer_id_match else None
        
        is_buyer = request.POST.get('is_buyer') == 'true' # Differentiate if populating buyer or consignee

        if not customer_code:
            return JsonResponse({'success': False, 'message': 'Invalid customer selection.'})

        comp_id = get_session_details(request)['comp_id']
        
        try:
            customer = CustomerMaster.objects.get(customer_id=customer_code, comp_id=comp_id)
            data = {
                'address': customer.material_del_address,
                'country_id': customer.material_del_country_id,
                'state_id': customer.material_del_state_id,
                'city_id': customer.material_del_city_id,
                'contact_person': customer.contact_person,
                'phone': customer.material_del_contact_no,
                'mobile': customer.contact_no,
                'fax': customer.material_del_fax_no,
                'email': customer.email,
                'tin_vat': customer.tin_vat_no,
                'tin_cst': customer.tin_cst_no,
                'ecc_no': customer.ecc_no,
                'name': customer.customer_name if not is_buyer else None # Populate consignee name when copying
            }
            return JsonResponse({'success': True, 'data': data})
        except CustomerMaster.DoesNotExist:
            return JsonResponse({'success': False, 'message': 'Customer not found.'})

class ProformaInvoiceDetailsTableView(View):
    """
    HTMX endpoint to render the partial HTML for the Goods tab's DataTables grid.
    This replaces the ASP.NET GridView data binding logic.
    """
    def get(self, request, pk, *args, **kwargs):
        proforma_invoice = get_object_or_404(ProformaInvoiceMaster, pk=pk, comp_id=get_session_details(request)['comp_id'])
        comp_id = get_session_details(request)['comp_id']

        details_data = []
        for detail in proforma_invoice.details.all():
            # Prepare data for rendering each row, including calculated fields
            row_data = {
                'id': detail.id,
                'item_desc': detail.item.item_desc if detail.item else 'N/A',
                'symbol': detail.get_current_unit_symbol(), # Unit for ReqQty
                'qty': detail.qty, # Original PO Quantity for the item
                'req_qty': detail.req_qty,
                'rem_qty': detail.get_remaining_qty(comp_id), # Calculated remaining qty
                'item_id': detail.item.id if detail.item else None,
                'amt_in_per': detail.amt_in_per,
                'rate': detail.rate,
                'unit_of_qty_symbol': detail.get_item_unit_symbol() # Unit for original PO Qty
            }
            details_data.append(row_data)

        context = {
            'proforma_invoice': proforma_invoice, # Pass master for PK in URLs
            'proforma_invoice_details': details_data,
            'unit_choices': UnitMaster.objects.all().order_by('symbol'), 
        }
        return render(request, 'proformainvoice/_goods_tab.html', context) # Render the entire goods tab partial

class ProformaInvoiceDetailEditRowView(View):
    """
    HTMX endpoint to render a single Proforma Invoice Detail row in edit mode.
    This replaces the ASP.NET CheckBox1_CheckedChanged toggle.
    """
    def get(self, request, pk, detail_pk, *args, **kwargs):
        proforma_invoice = get_object_or_404(ProformaInvoiceMaster, pk=pk, comp_id=get_session_details(request)['comp_id'])
        detail_instance = get_object_or_404(ProformaInvoiceDetail, pk=detail_pk, master=proforma_invoice)
        
        form = ProformaInvoiceDetailForm(instance=detail_instance)
        
        comp_id = get_session_details(request)['comp_id']
        row_data = {
            'id': detail_instance.id,
            'item_desc': detail_instance.item.item_desc if detail_instance.item else 'N/A',
            'symbol': detail_instance.get_current_unit_symbol(),
            'qty': detail_instance.qty,
            'req_qty': detail_instance.req_qty,
            'rem_qty': detail_instance.get_remaining_qty(comp_id),
            'item_id': detail_instance.item.id if detail_instance.item else None,
            'amt_in_per': detail_instance.amt_in_per,
            'rate': detail_instance.rate,
            'unit_of_qty_symbol': detail_instance.get_item_unit_symbol(),
            'master': proforma_invoice # Pass master instance
        }
        context = {
            'detail': row_data,
            'form': form,
            'unit_choices': UnitMaster.objects.all().order_by('symbol'),
        }
        return render(request, 'proformainvoice/_proformainvoice_detail_row_form.html', context)

class ProformaInvoiceDetailUpdateRowView(View):
    """
    HTMX endpoint to handle POST requests for updating a single row in the Goods grid.
    This captures the logic within the ASP.NET BtnUpdate_Click specific to individual detail rows.
    """
    def post(self, request, pk, detail_pk, *args, **kwargs):
        proforma_invoice = get_object_or_404(ProformaInvoiceMaster, pk=pk, comp_id=get_session_details(request)['comp_id'])
        detail_instance = get_object_or_404(ProformaInvoiceDetail, pk=detail_pk, master=proforma_invoice)
        
        form = ProformaInvoiceDetailForm(request.POST, instance=detail_instance)
        
        if form.is_valid():
            try:
                # Call model method for business logic and update
                detail_instance.update_detail_item(
                    form.cleaned_data['req_qty'],
                    form.cleaned_data['unit'].id, # Pass ID for FK
                    form.cleaned_data['amt_in_per'],
                    get_session_details(request)['comp_id']
                )
                messages.success(request, 'Proforma Invoice Detail updated successfully.')
                # Return 204 No Content and trigger table refresh for HTMX
                return HttpResponse(
                    status=204,
                    headers={'HX-Trigger': 'refreshProformaInvoiceDetailTable'}
                )
            except ValueError as e: # Catch validation errors from model
                form.add_error(None, str(e)) # Add as non-field error
                messages.error(request, 'Error updating detail: ' + str(e))
                comp_id = get_session_details(request)['comp_id']
                row_data = {
                    'id': detail_instance.id,
                    'item_desc': detail_instance.item.item_desc if detail_instance.item else 'N/A',
                    'symbol': detail_instance.get_current_unit_symbol(),
                    'qty': detail_instance.qty,
                    'req_qty': detail_instance.req_qty,
                    'rem_qty': detail_instance.get_remaining_qty(comp_id),
                    'item_id': detail_instance.item.id if detail_instance.item else None,
                    'amt_in_per': detail_instance.amt_in_per,
                    'rate': detail_instance.rate,
                    'unit_of_qty_symbol': detail_instance.get_item_unit_symbol(),
                    'master': proforma_invoice
                }
                context = {
                    'detail': row_data,
                    'form': form,
                    'unit_choices': UnitMaster.objects.all().order_by('symbol'),
                    'errors': form.errors 
                }
                return render(request, 'proformainvoice/_proformainvoice_detail_row_form.html', context)
        else:
            messages.error(request, 'Error updating detail: Please correct the form errors.')
            comp_id = get_session_details(request)['comp_id']
            row_data = {
                'id': detail_instance.id,
                'item_desc': detail_instance.item.item_desc if detail_instance.item else 'N/A',
                'symbol': detail_instance.get_current_unit_symbol(),
                'qty': detail_instance.qty,
                'req_qty': detail_instance.req_qty,
                'rem_qty': detail_instance.get_remaining_qty(comp_id),
                'item_id': detail_instance.item.id if detail_instance.item else None,
                'amt_in_per': detail_instance.amt_in_per,
                'rate': detail_instance.rate,
                'unit_of_qty_symbol': detail_instance.get_item_unit_symbol(),
                'master': proforma_invoice
            }
            context = {
                'detail': row_data,
                'form': form,
                'unit_choices': UnitMaster.objects.all().order_by('symbol'),
                'errors': form.errors 
            }
            return render(request, 'proformainvoice/_proformainvoice_detail_row_form.html', context)

```

### 4.4 Templates (`accounts/proformainvoice/templates/proformainvoice/`)

Templates are designed with DRY principles, using partials for reusable sections. HTMX attributes (`hx-get`, `hx-post`, `hx-target`, `hx-swap`, `hx-trigger`) drive the dynamic behavior, while Alpine.js manages local UI state (e.g., tab visibility, inline edit mode). DataTables is integrated for enhanced grid functionality.

**`proformainvoice_edit.html` (Main Page Template)**

```html
{% extends 'core/base.html' %}
{% load widget_tweaks %}

{% block title %}Proforma Invoice - Edit{% endblock %}

{% block content %}
<div x-data="{ activeTab: {{ active_tab_index }}, currentTabName: 'Buyer' }"
     _="on load set activeTab to (history.state.tabIndex || {{ active_tab_index }}) and 
        on hx-trigger from body[refreshProformaInvoiceEdit] 
            call Alpine.store('messages').add('success', 'Form data updated successfully.')
            # You might want to refresh only specific parts or reload the whole form if there are master data changes
            # For simplicity, if master update is successful, messages confirm, HTMX 204 means no swap.
            # A full refresh could be done with hx-redirect if needed.
        ">
    <table align="left" cellpadding="0" cellspacing="0" class="w-full mb-4 bg-white shadow rounded-lg">
        <tr>
            <td style="background:url(/static/images/hdbg.JPG);"
                class="fontcsswhite px-4 py-2 text-white font-bold text-lg rounded-t-lg" colspan="5">
                &nbsp;<b>Proforma Invoice - Edit</b>
            </td>
        </tr>
        <tr class="border-b border-gray-200">
            <td width="15%" height="24" class="py-2 px-4 text-sm whitespace-nowrap">
                &nbsp;Invoice No.&nbsp;:&nbsp;<span class="font-bold text-gray-800">{{ proforma_invoice.invoice_no }}</span>
            </td>
            <td width="17%" class="py-2 px-4 text-sm whitespace-nowrap">
                Date : <span class="font-bold text-gray-800">{{ proforma_invoice.sys_date|date:"d-m-Y" }}</span>
            </td>
            <td width="20%" class="py-2 px-4 text-sm whitespace-nowrap">
                Against : <span class="font-bold text-gray-800">{{ invoice_mode_display }}</span>
            </td>
            <td width="15%" class="py-2 px-4 text-sm"></td>
            <td width="15%" class="py-2 px-4 text-sm"></td>
        </tr>
        <tr class="border-b border-gray-200">
            <td height="24" class="py-2 px-4 text-sm whitespace-nowrap">
                &nbsp;PO No.:&nbsp;<span class="font-bold text-gray-800">{{ proforma_invoice.po_no }}</span>
            </td>
            <td class="py-2 px-4 text-sm whitespace-nowrap">
                Date : <span class="font-bold text-gray-800">{{ po_date|date:"d-m-Y" }}</span>
            </td>
            <td class="py-2 px-4 text-sm whitespace-nowrap">
                WO No. : <span class="font-bold text-gray-800">{{ wo_numbers }}</span>
            </td>
            <td width="15%" class="py-2 px-4 text-sm"></td>
            <td width="15%" class="py-2 px-4 text-sm"></td>
        </tr>
        <tr>
            <td colspan="5" class="py-2 px-4">
                <table align="left" cellpadding="0" cellspacing="0" class="w-full">
                    <tr>
                        <td height="25" class="w-1/4 text-sm font-semibold whitespace-nowrap">
                            &nbsp;Date Of Issue Of Invoice
                        </td>
                        <td class="w-1/4">
                            {% render_field form.date_of_issue_invoice %}
                            {% if form.date_of_issue_invoice.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ form.date_of_issue_invoice.errors }}</p>
                            {% endif %}
                        </td>
                        <td class="w-1/4"></td>
                        <td width="65%" class="w-1/4"></td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td colspan="5">
                <div class="tabs mt-4">
                    <ul class="flex border-b border-gray-200">
                        <li class="-mb-px mr-1">
                            <a class="bg-white inline-block py-2 px-4 text-blue-500 hover:text-blue-800 font-semibold border-l border-t border-r rounded-t"
                               :class="{ 'border-b-2 border-blue-500': activeTab === 0 }"
                               @click="activeTab = 0">Buyer</a>
                        </li>
                        <li class="-mb-px mr-1">
                            <a class="bg-white inline-block py-2 px-4 text-blue-500 hover:text-blue-800 font-semibold border-l border-t border-r rounded-t"
                               :class="{ 'border-b-2 border-blue-500': activeTab === 1 }"
                               @click="activeTab = 1">Consignee</a>
                        </li>
                        <li class="-mb-px mr-1">
                            <a class="bg-white inline-block py-2 px-4 text-blue-500 hover:text-blue-800 font-semibold border-l border-t border-r rounded-t"
                               :class="{ 'border-b-2 border-blue-500': activeTab === 2 }"
                               @click="activeTab = 2">Goods</a>
                        </li>
                        <li class="-mb-px mr-1">
                            <a class="bg-white inline-block py-2 px-4 text-blue-500 hover:text-blue-800 font-semibold border-l border-t border-r rounded-t"
                               :class="{ 'border-b-2 border-blue-500': activeTab === 3 }"
                               @click="activeTab = 3">Taxation</a>
                        </li>
                    </ul>

                    <div id="tab-content" class="p-4 border border-gray-200 bg-white rounded-b-lg">
                        <form method="post" hx-post="{{ request.path }}" hx-swap="none" id="mainForm">
                            {% csrf_token %}
                            <input type="hidden" name="current_tab" :value="activeTab">

                            <div x-show="activeTab === 0" class="tab-panel">
                                {% include 'proformainvoice/_buyer_tab.html' %}
                                <div class="mt-6 flex items-center justify-end space-x-4">
                                    <button type="button" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                                            @click="activeTab = 1">Next</button>
                                </div>
                            </div>
                            <div x-show="activeTab === 1" class="tab-panel">
                                {% include 'proformainvoice/_consignee_tab.html' %}
                                <div class="mt-6 flex items-center justify-end space-x-4">
                                    <button type="button" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                                            @click="activeTab = 2">Next</button>
                                </div>
                            </div>
                            <div x-show="activeTab === 2" class="tab-panel"
                                 hx-trigger="load, refreshProformaInvoiceDetailTable from:body"
                                 hx-get="{% url 'proformainvoice_details_table' pk=proforma_invoice.pk %}"
                                 hx-target="#goods-tab-content"
                                 hx-swap="innerHTML">
                                <div id="goods-tab-content" class="min-h-[360px]"> {# Minimum height to prevent jump #}
                                    <!-- Goods Grid will be loaded here via HTMX -->
                                    <div class="text-center py-10">
                                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                                        <p class="mt-2">Loading Goods...</p>
                                    </div>
                                </div>
                                <div class="mt-6 flex items-center justify-end space-x-4">
                                    <button type="button" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                                            @click="activeTab = 3">Next</button>
                                </div>
                            </div>
                            <div x-show="activeTab === 3" class="tab-panel">
                                {% include 'proformainvoice/_taxation_tab.html' %}
                                <div class="mt-6 flex items-center justify-center space-x-4">
                                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded" 
                                            name="action" value="update_all" onclick="return confirm('Are you sure you want to update the Proforma Invoice?');">
                                        Update All
                                    </button>
                                    <button type="button" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                                            hx-get="{% url 'proformainvoice_list_placeholder' %}" hx-swap="outerHTML" hx-push-url="true">
                                        Cancel
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </td>
        </tr>
    </table>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.store('messages', {
            items: [],
            add(type, text) {
                this.items.push({ id: Date.now(), type, text });
                setTimeout(() => this.remove(this.items[this.items.length - 1].id), 5000);
            },
            remove(id) {
                this.items = this.items.filter(item => item.id !== id);
            }
        });

        Alpine.data('proformaInvoiceEditPage', () => ({
            activeTab: {{ active_tab_index }},
            currentTabName: 'Buyer',
            init() {
                // Initialize the hidden input for current_tab when Alpine.js is ready
                this.$watch('activeTab', (value) => {
                    this.$root.querySelector('input[name="current_tab"]').value = value;
                    history.replaceState({ tabIndex: value }, '', window.location.href);
                });

                // HTMX listener for form submission on mainForm (master update)
                this.$root.querySelector('#mainForm').addEventListener('htmx:afterRequest', (evt) => {
                    if (evt.detail.xhr.status === 204) {
                        // Success handled by HTMX (no content), message added by Alpine store
                        // No need for alert as messages are handled by top-level Alpine store
                    } else if (evt.detail.xhr.status !== 200) {
                        // Error handling: Django forms validation errors will re-render the current tab
                        // so messages are handled by Django's messages framework rendering in base.html
                    }
                });
            },
            // Function to handle Search/Copy from Buyer for Consignee
            copyFromBuyer() {
                const buyerNameInput = this.$root.querySelector('#id_buyer_name');
                const buyerName = buyerNameInput.value;
                if (!buyerName) {
                    Alpine.store('messages').add('error', 'Please select a Buyer first.');
                    return;
                }
                
                // Trigger HTMX request to fetch buyer details and populate consignee fields
                // This simulates the Button6_Click in ASP.NET
                htmx.ajax('POST', '{% url "proformainvoice_get_customer_details" %}', {
                    headers: { 'HX-Request': 'true' },
                    values: { 'customer_name': buyerName, 'is_buyer': 'false' }, // is_buyer false means populate consignee
                    target: '#consignee_form_fields', 
                    swap: 'none', 
                    onSuccess: function(evt) {
                        const response = JSON.parse(evt.detail.xhr.responseText);
                        if (response.success) {
                            const data = response.data;
                            document.getElementById('id_consignee_name').value = data.name || buyerName; 
                            document.getElementById('id_consignee_address').value = data.address;
                            
                            // Update dropdowns and trigger change events for HTMX to load sub-levels
                            document.getElementById('id_consignee_country').value = data.country_id;
                            htmx.trigger(document.getElementById('id_consignee_country'), 'change'); 
                            setTimeout(() => { // Give HTMX time to load states
                                document.getElementById('id_consignee_state').value = data.state_id;
                                htmx.trigger(document.getElementById('id_consignee_state'), 'change'); 
                                setTimeout(() => { // Give HTMX time to load cities
                                    document.getElementById('id_consignee_city').value = data.city_id;
                                }, 100);
                            }, 100);

                            document.getElementById('id_consignee_contact_person').value = data.contact_person;
                            document.getElementById('id_consignee_phone').value = data.phone;
                            document.getElementById('id_consignee_mobile').value = data.mobile;
                            document.getElementById('id_consignee_fax').value = data.fax;
                            document.getElementById('id_consignee_email').value = data.email;
                            document.getElementById('id_consignee_tin_vat').value = data.tin_vat;
                            document.getElementById('id_consignee_tin_cst').value = data.tin_cst;
                            document.getElementById('id_consignee_ecc').value = data.ecc_no;
                            Alpine.store('messages').add('success', 'Consignee details copied successfully.');
                        } else {
                            Alpine.store('messages').add('error', response.message);
                        }
                    },
                    onError: function(evt) {
                        Alpine.store('messages').add('error', 'Error fetching customer details.');
                    }
                });
            }
        }));
    });

    // Handle autocomplete selection (similar to ASP.NET AutocompleteExtender behavior)
    // This uses a delegated event listener to handle dynamically added autocomplete items
    document.body.addEventListener('click', function(event) {
        if (event.target.classList.contains('autocomplete-item')) {
            const inputField = event.target.closest('.autocomplete-container').querySelector('input[type="text"]');
            inputField.value = event.target.textContent.trim(); // Set the full "Name [ID]" string
            
            // Clear autocomplete results after selection
            event.target.closest('.autocomplete-results').innerHTML = '';
            
            // Trigger fetch details (like ASP.NET Search button click)
            const isBuyer = inputField.id === 'id_buyer_name';
            htmx.ajax('POST', '{% url "proformainvoice_get_customer_details" %}', {
                headers: { 'HX-Request': 'true' },
                values: { 'customer_name': inputField.value, 'is_buyer': isBuyer },
                target: isBuyer ? '#buyer_form_fields' : '#consignee_form_fields',
                swap: 'none', // We'll handle manual population
                onSuccess: function(evt) {
                    const response = JSON.parse(evt.detail.xhr.responseText);
                    if (response.success) {
                        const data = response.data;
                        const prefix = isBuyer ? 'buyer' : 'consignee';
                        document.getElementById(`id_${prefix}_address`).value = data.address;

                        // Update dropdowns and trigger HTMX to load sub-levels
                        document.getElementById(`id_${prefix}_country`).value = data.country_id;
                        htmx.trigger(document.getElementById(`id_${prefix}_country`), 'change');
                        setTimeout(() => {
                            document.getElementById(`id_${prefix}_state`).value = data.state_id;
                            htmx.trigger(document.getElementById(`id_${prefix}_state`), 'change');
                            setTimeout(() => {
                                document.getElementById(`id_${prefix}_city`).value = data.city_id;
                            }, 100);
                        }, 100);

                        document.getElementById(`id_${prefix}_contact_person`).value = data.contact_person;
                        document.getElementById(`id_${prefix}_phone`).value = data.phone;
                        document.getElementById(`id_${prefix}_mobile`).value = data.mobile;
                        document.getElementById(`id_${prefix}_fax`).value = data.fax;
                        document.getElementById(`id_${prefix}_email`).value = data.email;
                        document.getElementById(`id_${prefix}_tin_vat`).value = data.tin_vat;
                        document.getElementById(`id_${prefix}_tin_cst`).value = data.tin_cst;
                        document.getElementById(`id_${prefix}_ecc`).value = data.ecc_no;
                        Alpine.store('messages').add('success', 'Customer details loaded.');
                    } else {
                        Alpine.store('messages').add('error', response.message);
                    }
                },
                onError: function(evt) {
                    Alpine.store('messages').add('error', 'Error fetching customer details.');
                }
            });
        }
    });

    // Clear autocomplete results when clicking outside their container
    document.body.addEventListener('click', function(event) {
        if (!event.target.closest('.autocomplete-container')) {
            document.querySelectorAll('.autocomplete-results').forEach(el => el.innerHTML = '');
        }
    });
</script>
{% endblock %}
```

**`_buyer_tab.html` (Partial Template for Buyer Tab)**

```html
{% load widget_tweaks %}
<div id="buyer_form_fields" class="space-y-4">
    <table align="left" cellpadding="0" cellspacing="0" class="w-full">
        <tr>
            <td class="w-1/5 py-2 px-4 text-sm font-semibold">Name</td>
            <td class="w-4/5 py-2 px-4 text-sm">
                <div class="autocomplete-container relative">
                    {% render_field form.buyer_name %}
                    <div id="autocomplete-results-buyer" class="autocomplete-results absolute z-10 bg-white border border-gray-300 w-full rounded shadow-lg max-h-60 overflow-y-auto mt-1"></div>
                    <span class="htmx-indicator absolute right-0 top-1/2 -translate-y-1/2 mr-2"><div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div></span>
                </div>
                {% if form.buyer_name.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.buyer_name.errors }}</p>
                {% endif %}
                <button type="button" class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded ml-2"
                        hx-post="{% url 'proformainvoice_get_customer_details' %}"
                        hx-include="#id_buyer_name" hx-vals='{"is_buyer": "true"}'
                        hx-target="#buyer_form_fields" hx-swap="none"
                        _="on htmx:afterRequest(evt) 
                            if evt.detail.xhr.status === 200 
                                const response = JSON.parse(evt.detail.xhr.responseText);
                                if (response.success) {
                                    const data = response.data;
                                    document.getElementById('id_buyer_address').value = data.address;
                                    document.getElementById('id_buyer_country').value = data.country_id;
                                    htmx.trigger(document.getElementById('id_buyer_country'), 'change'); // Trigger HTMX for states
                                    setTimeout(() => { // Give HTMX time to load states
                                        document.getElementById('id_buyer_state').value = data.state_id;
                                        htmx.trigger(document.getElementById('id_buyer_state'), 'change'); // Trigger HTMX for cities
                                        setTimeout(() => { // Give HTMX time to load cities
                                            document.getElementById('id_buyer_city').value = data.city_id;
                                        }, 100);
                                    }, 100);
                                    document.getElementById('id_buyer_contact_person').value = data.contact_person;
                                    document.getElementById('id_buyer_phone').value = data.phone;
                                    document.getElementById('id_buyer_mobile').value = data.mobile;
                                    document.getElementById('id_buyer_fax').value = data.fax;
                                    document.getElementById('id_buyer_email').value = data.email;
                                    document.getElementById('id_buyer_tin_vat').value = data.tin_vat;
                                    document.getElementById('id_buyer_tin_cst').value = data.tin_cst;
                                    document.getElementById('id_buyer_ecc').value = data.ecc_no;
                                    Alpine.store('messages').add('success', 'Buyer details loaded.');
                                } else { Alpine.store('messages').add('error', response.message); }
                            else { Alpine.store('messages').add('error', 'Error fetching customer details.'); }">
                    Search
                </button>
            </td>
        </tr>
        <tr>
            <td class="w-1/5 py-2 px-4 text-sm font-semibold align-top">Address</td>
            <td class="w-4/5 py-2 px-4 text-sm">
                {% render_field form.buyer_address %}
                {% if form.buyer_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_address.errors }}</p>{% endif %}
            </td>
        </tr>
        <tr>
            <td colspan="2">
                <table align="left" cellpadding="0" cellspacing="0" class="w-full">
                    <tr>
                        <td class="w-1/6 py-2 px-4 text-sm font-semibold">Country</td>
                        <td class="w-1/6 py-2 px-4 text-sm">{% render_field form.buyer_country %}</td>
                        <td class="w-1/6 py-2 px-4 text-sm font-semibold">State</td>
                        <td class="w-1/6 py-2 px-4 text-sm">{% render_field form.buyer_state %}</td>
                        <td class="w-1/6 py-2 px-4 text-sm font-semibold">City</td>
                        <td class="w-1/6 py-2 px-4 text-sm">{% render_field form.buyer_city %}</td>
                    </tr>
                    <tr>
                        <td class="py-2 px-4 text-sm font-semibold">Contact person</td>
                        <td class="py-2 px-4 text-sm">{% render_field form.buyer_contact_person %}</td>
                        <td class="py-2 px-4 text-sm font-semibold">Phone No.</td>
                        <td class="py-2 px-4 text-sm">{% render_field form.buyer_phone %}</td>
                        <td class="py-2 px-4 text-sm font-semibold">Mobile No.</td>
                        <td class="py-2 px-4 text-sm">{% render_field form.buyer_mobile %}</td>
                    </tr>
                    <tr>
                        <td class="py-2 px-4 text-sm font-semibold">E-mail</td>
                        <td class="py-2 px-4 text-sm">{% render_field form.buyer_email %}</td>
                        <td class="py-2 px-4 text-sm font-semibold">Fax No.</td>
                        <td class="py-2 px-4 text-sm">{% render_field form.buyer_fax %}</td>
                        <td class="py-2 px-4 text-sm font-semibold">TIN / VAT No</td>
                        <td class="py-2 px-4 text-sm">{% render_field form.buyer_tin_vat %}</td>
                    </tr>
                    <tr>
                        <td class="py-2 px-4 text-sm font-semibold">Customer&apos;s ECC.No.</td>
                        <td class="py-2 px-4 text-sm">{% render_field form.buyer_ecc %}</td>
                        <td class="py-2 px-4 text-sm font-semibold">TIN / CST No.</td>
                        <td class="py-2 px-4 text-sm">{% render_field form.buyer_tin_cst %}</td>
                        <td class="py-2 px-4 text-sm"></td>
                        <td class="py-2 px-4 text-sm">
                            {# Next button is handled by Alpine.js in the main template #}
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</div>
```

**`_consignee_tab.html` (Partial Template for Consignee Tab)**

```html
{% load widget_tweaks %}
<div id="consignee_form_fields" class="space-y-4">
    <table align="left" cellpadding="0" cellspacing="0" class="w-full">
        <tr>
            <td class="w-1/5 py-2 px-4 text-sm font-semibold">Name</td>
            <td class="w-4/5 py-2 px-4 text-sm">
                <div class="autocomplete-container relative">
                    {% render_field form.consignee_name %}
                    <div id="autocomplete-results-consignee" class="autocomplete-results absolute z-10 bg-white border border-gray-300 w-full rounded shadow-lg max-h-60 overflow-y-auto mt-1"></div>
                    <span class="htmx-indicator absolute right-0 top-1/2 -translate-y-1/2 mr-2"><div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div></span>
                </div>
                {% if form.consignee_name.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.consignee_name.errors }}</p>
                {% endif %}
                <button type="button" class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded ml-2"
                        hx-post="{% url 'proformainvoice_get_customer_details' %}"
                        hx-include="#id_consignee_name" hx-vals='{"is_buyer": "false"}'
                        hx-target="#consignee_form_fields" hx-swap="none"
                        _="on htmx:afterRequest(evt) 
                            if evt.detail.xhr.status === 200 
                                const response = JSON.parse(evt.detail.xhr.responseText);
                                if (response.success) {
                                    const data = response.data;
                                    document.getElementById('id_consignee_address').value = data.address;
                                    document.getElementById('id_consignee_country').value = data.country_id;
                                    htmx.trigger(document.getElementById('id_consignee_country'), 'change'); 
                                    setTimeout(() => { 
                                        document.getElementById('id_consignee_state').value = data.state_id;
                                        htmx.trigger(document.getElementById('id_consignee_state'), 'change'); 
                                        setTimeout(() => { 
                                            document.getElementById('id_consignee_city').value = data.city_id;
                                        }, 100);
                                    }, 100);
                                    document.getElementById('id_consignee_contact_person').value = data.contact_person;
                                    document.getElementById('id_consignee_phone').value = data.phone;
                                    document.getElementById('id_consignee_mobile').value = data.mobile;
                                    document.getElementById('id_consignee_fax').value = data.fax;
                                    document.getElementById('id_consignee_email').value = data.email;
                                    document.getElementById('id_consignee_tin_vat').value = data.tin_vat;
                                    document.getElementById('id_consignee_tin_cst').value = data.tin_cst;
                                    document.getElementById('id_consignee_ecc').value = data.ecc_no;
                                    Alpine.store('messages').add('success', 'Consignee details loaded.');
                                } else { Alpine.store('messages').add('error', response.message); }
                            else { Alpine.store('messages').add('error', 'Error fetching customer details.'); }">
                    Search
                </button>
                <button type="button" class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded ml-2"
                        @click="copyFromBuyer()">
                    Copy from buyer
                </button>
            </td>
        </tr>
        <tr>
            <td class="w-1/5 py-2 px-4 text-sm font-semibold align-top">Address</td>
            <td class="w-4/5 py-2 px-4 text-sm">
                {% render_field form.consignee_address %}
                {% if form.consignee_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_address.errors }}</p>{% endif %}
            </td>
        </tr>
        <tr>
            <td colspan="2">
                <table align="left" cellpadding="0" cellspacing="0" class="w-full">
                    <tr>
                        <td class="w-1/6 py-2 px-4 text-sm font-semibold">Country</td>
                        <td class="w-1/6 py-2 px-4 text-sm">{% render_field form.consignee_country %}</td>
                        <td class="w-1/6 py-2 px-4 text-sm font-semibold">State</td>
                        <td class="w-1/6 py-2 px-4 text-sm">{% render_field form.consignee_state %}</td>
                        <td class="w-1/6 py-2 px-4 text-sm font-semibold">City</td>
                        <td class="w-1/6 py-2 px-4 text-sm">{% render_field form.consignee_city %}</td>
                    </tr>
                    <tr>
                        <td class="py-2 px-4 text-sm font-semibold">Contact person</td>
                        <td class="py-2 px-4 text-sm">{% render_field form.consignee_contact_person %}</td>
                        <td class="py-2 px-4 text-sm font-semibold">Phone No.</td>
                        <td class="py-2 px-4 text-sm">{% render_field form.consignee_phone %}</td>
                        <td class="py-2 px-4 text-sm font-semibold">Mobile No.</td>
                        <td class="py-2 px-4 text-sm">{% render_field form.consignee_mobile %}</td>
                    </tr>
                    <tr>
                        <td class="py-2 px-4 text-sm font-semibold">E-mail</td>
                        <td class="py-2 px-4 text-sm">{% render_field form.consignee_email %}</td>
                        <td class="py-2 px-4 text-sm font-semibold">Fax No.</td>
                        <td class="py-2 px-4 text-sm">{% render_field form.consignee_fax %}</td>
                        <td class="py-2 px-4 text-sm font-semibold">TIN / VAT No.</td>
                        <td class="py-2 px-4 text-sm">{% render_field form.consignee_tin_vat %}</td>
                    </tr>
                    <tr>
                        <td class="py-2 px-4 text-sm font-semibold">Customer&apos;s ECC.No.</td>
                        <td class="py-2 px-4 text-sm">{% render_field form.consignee_ecc %}</td>
                        <td class="py-2 px-4 text-sm font-semibold">TIN / CST No.</td>
                        <td class="py-2 px-4 text-sm">{% render_field form.consignee_tin_cst %}</td>
                        <td class="py-2 px-4 text-sm"></td>
                        <td class="py-2 px-4 text-sm">
                            {# Next button is handled by Alpine.js in the main template #}
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</div>
```

**`_goods_tab.html` (Partial Template for Goods Tab - DataTable content)**

```html
{% load widget_tweaks %}
<div class="p-2 overflow-x-auto">
    <table id="proformaInvoiceDetailsTable" class="min-w-full bg-white border border-gray-200 divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-3 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-3 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider"></th> {# Checkbox column #}
                <th class="py-2 px-3 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Desc</th>
                <th class="py-2 px-3 border-b text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Unit</th>
                <th class="py-2 px-3 border-b text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                <th class="py-2 px-3 border-b text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Rem Qty</th>
                <th class="py-2 px-3 border-b text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Req Qty</th>
                <th class="py-2 px-3 border-b text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Of Qty</th>
                <th class="py-2 px-3 border-b text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amt In Per</th>
                <th class="py-2 px-3 border-b text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                <th class="py-2 px-3 border-b text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th> 
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for detail in proforma_invoice_details %}
            <tr id="detail-row-{{ detail.id }}" x-data="{ isEditing: false }">
                <td class="py-2 px-3 border-b text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-3 border-b text-center">
                    <input type="checkbox" x-model="isEditing" @change="if(isEditing) htmx.trigger('#detail-row-{{ detail.id }}', 'editRow'); else htmx.trigger('#detail-row-{{ detail.id }}', 'cancelEdit');"
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                </td>
                <td class="py-2 px-3 border-b text-left whitespace-normal">{{ detail.item_desc }}</td>
                <td class="py-2 px-3 border-b text-center whitespace-nowrap">{{ detail.symbol }}</td>
                <td class="py-2 px-3 border-b text-right whitespace-nowrap">{{ detail.qty|floatformat:3 }}</td>
                <td class="py-2 px-3 border-b text-right whitespace-nowrap">{{ detail.rem_qty|floatformat:3 }}</td>
                <td class="py-2 px-3 border-b text-right whitespace-nowrap"
                    hx-trigger="editRow from #detail-row-{{ detail.id }}"
                    hx-get="{% url 'proformainvoice_detail_edit_row' pk=proforma_invoice.pk detail_pk=detail.id %}"
                    hx-target="closest 'tr'" hx-swap="outerHTML"
                    hx-on="cancelEdit: htmx.trigger('#proformaInvoiceDetailsTable', 'load');"
                    id="req-qty-cell-{{ detail.id }}">
                    <span x-show="!isEditing">{{ detail.req_qty|floatformat:3 }}</span>
                </td>
                <td class="py-2 px-3 border-b text-center whitespace-nowrap">{{ detail.unit_of_qty_symbol }}</td>
                <td class="py-2 px-3 border-b text-right whitespace-nowrap" id="amt-in-per-cell-{{ detail.id }}">
                    <span x-show="!isEditing">{{ detail.amt_in_per|floatformat:2 }}</span>
                </td>
                <td class="py-2 px-3 border-b text-right whitespace-nowrap">{{ detail.rate|floatformat:2 }}</td>
                <td class="py-2 px-3 border-b text-center">
                    <div x-show="isEditing" class="flex justify-center items-center">
                        <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 text-xs rounded mr-1"
                                hx-post="{% url 'proformainvoice_detail_update_row' pk=proforma_invoice.pk detail_pk=detail.id %}"
                                hx-include="#detail-row-{{ detail.id }} input[type='text'], #detail-row-{{ detail.id }} input[type='number'], #detail-row-{{ detail.id }} select, #detail-row-{{ detail.id }} input[type='hidden']"
                                hx-trigger="click"
                                hx-swap="none"
                                hx-on="htmx:afterRequest: if(event.detail.xhr.status === 204 || event.detail.xhr.status === 200) { Alpine.store('messages').add('success', 'Detail updated.'); htmx.trigger('#proformaInvoiceDetailsTable', 'load'); } else { Alpine.store('messages').add('error', 'Update failed. Check inputs.'); htmx.trigger('#proformaInvoiceDetailsTable', 'load'); }">
                            Save
                        </button>
                        <button type="button" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-1 px-2 text-xs rounded"
                                @click="isEditing = false; htmx.trigger('#proformaInvoiceDetailsTable', 'load');">
                            Cancel
                        </button>
                    </div>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="11" class="py-4 text-center text-gray-500">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // DataTables initialization
    // It's important to re-initialize DataTable after HTMX swaps
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#proformaInvoiceDetailsTable')) {
            $('#proformaInvoiceDetailsTable').DataTable().destroy();
        }
        $('#proformaInvoiceDetailsTable').DataTable({
            "pageLength": 8, // Matching ASP.NET PageSize
            "lengthMenu": [[8, 10, 25, 50, -1], [8, 10, 25, 50, "All"]],
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true
        });
    });

    // Custom event listener for HTMX to trigger DataTable re-initialization
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'goods-tab-content' || $(event.detail.target).find('#proformaInvoiceDetailsTable').length) {
            if ($.fn.DataTable.isDataTable('#proformaInvoiceDetailsTable')) {
                $('#proformaInvoiceDetailsTable').DataTable().destroy();
            }
            $('#proformaInvoiceDetailsTable').DataTable({
                "pageLength": 8,
                "lengthMenu": [[8, 10, 25, 50, -1], [8, 10, 25, 50, "All"]],
                "paging": true,
                "searching": true,
                "ordering": true,
                "info": true
            });
        }
    });
</script>
```

**`_proformainvoice_detail_row_form.html` (Partial Template for an Editable Row)**

This partial is loaded when a user clicks the checkbox to edit a specific row in the `Goods` table.

```html
{# This template is rendered by HTMX when a row is put into edit mode #}
{% load widget_tweaks %}
<td class="py-2 px-3 border-b text-right">{{ detail.sn|default:forloop.counter }}</td>
<td class="py-2 px-3 border-b text-center">
    <input type="checkbox" checked x-model="isEditing" @change="if(isEditing) htmx.trigger('#detail-row-{{ detail.id }}', 'editRow'); else htmx.trigger('#detail-row-{{ detail.id }}', 'cancelEdit');"
           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
</td>
<td class="py-2 px-3 border-b text-left whitespace-normal">{{ detail.item_desc }}</td>
<td class="py-2 px-3 border-b text-center whitespace-nowrap">{{ detail.symbol }}</td>
<td class="py-2 px-3 border-b text-right whitespace-nowrap">{{ detail.qty|floatformat:3 }}</td>
<td class="py-2 px-3 border-b text-right whitespace-nowrap">{{ detail.rem_qty|floatformat:3 }}</td>
<td class="py-2 px-3 border-b text-right whitespace-nowrap">
    {% render_field form.req_qty %}
    <input type="hidden" name="{{ form.original_qty.name }}" value="{{ form.original_qty.initial }}">
    <input type="hidden" name="{{ form.item_id.name }}" value="{{ form.item_id.initial }}">
    {% if form.req_qty.errors %}<p class="text-red-500 text-xs mt-1">{{ form.req_qty.errors.as_text }}</p>{% endif %} {# Display errors for the specific field #}
</td>
<td class="py-2 px-3 border-b text-center whitespace-nowrap">
    {% render_field form.unit %}
    {% if form.unit.errors %}<p class="text-red-500 text-xs mt-1">{{ form.unit.errors.as_text }}</p>{% endif %}
</td>
<td class="py-2 px-3 border-b text-right whitespace-nowrap">
    {% render_field form.amt_in_per %}
    {% if form.amt_in_per.errors %}<p class="text-red-500 text-xs mt-1">{{ form.amt_in_per.errors.as_text }}</p>{% endif %}
</td>
<td class="py-2 px-3 border-b text-right whitespace-nowrap">{{ detail.rate|floatformat:2 }}</td>
<td class="py-2 px-3 border-b text-center">
    <div x-show="isEditing" class="flex justify-center items-center">
        <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 text-xs rounded mr-1"
                hx-post="{% url 'proformainvoice_detail_update_row' pk=detail.master.pk detail_pk=detail.id %}"
                hx-include="#detail-row-{{ detail.id }} input[type='text'], #detail-row-{{ detail.id }} input[type='number'], #detail-row-{{ detail.id }} select, #detail-row-{{ detail.id }} input[type='hidden']"
                hx-trigger="click"
                hx-swap="none"
                hx-on="htmx:afterRequest: if(event.detail.xhr.status === 204 || event.detail.xhr.status === 200) { Alpine.store('messages').add('success', 'Detail updated.'); htmx.trigger('#proformaInvoiceDetailsTable', 'load'); } else { Alpine.store('messages').add('error', 'Update failed. Check inputs.'); htmx.trigger('#proformaInvoiceDetailsTable', 'load'); }">
            Save
        </button>
        <button type="button" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-1 px-2 text-xs rounded"
                @click="isEditing = false; htmx.trigger('#proformaInvoiceDetailsTable', 'load');">
            Cancel
        </button>
    </div>
    {% if form.non_field_errors %}<p class="text-red-500 text-xs mt-1">{{ form.non_field_errors }}</p>{% endif %}
</td>
```

**`_taxation_tab.html` (Partial Template for Taxation Tab)**

```html
{% load widget_tweaks %}
<div class="space-y-4">
    <table align="left" cellpadding="0" cellspacing="0" class="w-full">
        <tr>
            <td class="w-1/5 py-2 px-4 text-sm font-semibold">&nbsp;Add&nbsp;</td>
            <td class="w-4/5 py-2 px-4 text-sm">
                {% render_field form.add_amount %}
                {% if form.add_amount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.add_amount.errors }}</p>{% endif %}
                {% render_field form.add_type %}
                {% if form.add_type.errors %}<p class="text-red-500 text-xs mt-1">{{ form.add_type.errors }}</p>{% endif %}
            </td>
        </tr>
        <tr>
            <td class="w-1/5 py-2 px-4 text-sm font-semibold">&nbsp;Deduction&nbsp;</td>
            <td class="w-4/5 py-2 px-4 text-sm">
                {% render_field form.deduction %}
                {% if form.deduction.errors %}<p class="text-red-500 text-xs mt-1">{{ form.deduction.errors }}</p>{% endif %}
                {% render_field form.deduction_type %}
                {% if form.deduction_type.errors %}<p class="text-red-500 text-xs mt-1">{{ form.deduction_type.errors }}</p>{% endif %}
            </td>
        </tr>
    </table>
</div>
```

### 4.5 URLs (`accounts/proformainvoice/urls.py`)

This file defines the URL patterns for your Django application, mapping URLs to their corresponding views.

```python
from django.urls import path
from .views import (
    ProformaInvoiceEditView,
    GetStatesView,
    GetCitiesView,
    AutocompleteCustomerView,
    GetCustomerDetailsView,
    ProformaInvoiceDetailsTableView,
    ProformaInvoiceDetailEditRowView,
    ProformaInvoiceDetailUpdateRowView,
)

urlpatterns = [
    # Main edit page for a specific Proforma Invoice
    path('edit/<int:pk>/', ProformaInvoiceEditView.as_view(), name='proformainvoice_edit'),

    # HTMX endpoints for dynamic dropdowns (Country -> State -> City)
    path('get-states/', GetStatesView.as_view(), name='get_states'),
    path('get-cities/', GetCitiesView.as_view(), name='get_cities'),

    # HTMX endpoints for customer search and detail retrieval
    path('autocomplete-customer/', AutocompleteCustomerView.as_view(), name='autocomplete_customer'),
    path('get-customer-details/', GetCustomerDetailsView.as_view(), name='get_customer_details'),

    # HTMX endpoint for the Goods tab's DataTables grid partial
    path('details-table/<int:pk>/', ProformaInvoiceDetailsTableView.as_view(), name='proformainvoice_details_table'),
    
    # HTMX endpoint to render a single detail row in edit mode
    path('details-table/<int:pk>/edit-row/<int:detail_pk>/', ProformaInvoiceDetailEditRowView.as_view(), name='proformainvoice_detail_edit_row'),

    # HTMX endpoint for updating a single detail row in the Goods grid
    path('details-table/<int:pk>/update-row/<int:detail_pk>/', ProformaInvoiceDetailUpdateRowView.as_view(), name='proformainvoice_detail_update_row'),
    
    # Placeholder for a list view or dashboard redirection if a main list page exists
    # This acts as the success_url for the main form submission.
    path('', ProformaInvoiceEditView.as_view(), name='proformainvoice_list_placeholder'), 
]

```

### 4.6 Tests (`accounts/proformainvoice/tests.py`)

Comprehensive unit tests for model methods and integration tests for all views ensure the functionality is correctly migrated and robust. This includes testing business logic, form validation, and HTMX interactions.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from django.db import connection # For raw SQL queries to mock external tables

from .models import (
    ProformaInvoiceMaster, ProformaInvoiceDetail,
    Country, State, City, UnitMaster, InvoiceAgainst, CustomerMaster, PurchaseOrderDetail
)
from .forms import ProformaInvoiceMasterForm, ProformaInvoiceDetailForm
import datetime

# Helper function to mock session data for testing
def mock_session_details():
    """Provides consistent session data for test requests."""
    return {'compid': 1, 'finyear': 1, 'username': 'test_user'}

class ProformaInvoiceModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        """Set up non-modified data for all tests in the class."""
        # Create minimal required data for FKs
        cls.country = Country.objects.create(id=1, name='India')
        cls.state = State.objects.create(id=1, name='Maharashtra', country=cls.country)
        cls.city = City.objects.create(id=1, name='Mumbai', state=cls.state)
        cls.unit_ea = UnitMaster.objects.create(id=1, symbol='EA')
        cls.unit_kg = UnitMaster.objects.create(id=2, symbol='KG')
        cls.invoice_mode_against = InvoiceAgainst.objects.create(id=1, against='Against PO')
        cls.customer = CustomerMaster.objects.create(
            customer_id='CUST001', customer_name='Test Customer', comp_id=1,
            material_del_address='123 Test St', material_del_country=cls.country,
            material_del_state=cls.state, material_del_city=cls.city,
            email='<EMAIL>', contact_person='John Doe',
            tin_cst_no='TINCST123', tin_vat_no='TINVAT456', ecc_no='ECC789',
            contact_no='9876543210', material_del_contact_no='0221234567'
        )
        cls.po_detail_item = PurchaseOrderDetail.objects.create(
            id=101, po_id='PO001', item_desc='Test Item 1', unit=cls.unit_ea
        )
        # Mock external tables that are accessed by raw SQL in models
        with connection.cursor() as cursor:
            cursor.execute("CREATE TABLE SD_Cust_WorkOrder_Master (Id INT PRIMARY KEY, WONo NVARCHAR(255), CompId INT)")
            cursor.execute("INSERT INTO SD_Cust_WorkOrder_Master (Id, WONo, CompId) VALUES (1, 'WO-001', 1)")
            cursor.execute("INSERT INTO SD_Cust_WorkOrder_Master (Id, WONo, CompId) VALUES (2, 'WO-002', 1)")
            cursor.execute("CREATE TABLE SD_Cust_PO_Master (POId NVARCHAR(50) PRIMARY KEY, PODate DATE, CompId INT)")
            cursor.execute("INSERT INTO SD_Cust_PO_Master (POId, PODate, CompId) VALUES ('PO001', '2023-01-15', 1)")

        cls.proforma_invoice = ProformaInvoiceMaster.objects.create(
            id=1,
            sys_date=datetime.date(2023, 1, 20),
            sys_time=datetime.time(10, 30, 0),
            comp_id=1,
            fin_year_id=1,
            session_id='test_session',
            invoice_no='PI-001',
            po_no='PO001',
            po_id='PO001',
            wo_no='1,2,', # Comma separated IDs
            invoice_mode=cls.invoice_mode_against,
            date_of_issue_invoice=datetime.date(2023, 1, 20),
            buyer_name='Buyer A',
            buyer_address='Buyer Address',
            buyer_country=cls.country,
            buyer_state=cls.state,
            buyer_city=cls.city,
            add_type=0, add_amount=10.00, deduction_type=0, deduction=5.00
        )
        cls.proforma_detail_1 = ProformaInvoiceDetail.objects.create(
            id=1, master=cls.proforma_invoice, item=cls.po_detail_item,
            invoice_no='PI-001', unit=cls.unit_kg, qty=100.0, req_qty=50.0, amt_in_per=10.0, rate=5.0
        )
        cls.proforma_detail_2 = ProformaInvoiceDetail.objects.create(
            id=2, master=cls.proforma_invoice, item=cls.po_detail_item, # Same item for testing total quantity
            invoice_no='PI-001', unit=cls.unit_kg, qty=100.0, req_qty=20.0, amt_in