The migration from ASP.NET to Django focuses on transforming the page-centric, stateful ASP.NET model into a stateless, resource-oriented Django architecture. We'll leverage Django's Class-Based Views (CBVs), HTMX for dynamic interactions, Alpine.js for local UI state, and DataTables for rich tabular data presentation, all while adhering to the "fat model, thin view" principle.

The original ASP.NET page, `ServiceTaxInvoice_Edit_Details.aspx`, is a complex "edit" interface with multiple tabs, dynamic dropdowns, a grid for line items with inline editing, and various search functionalities.

Our Django modernization will:
1.  **Model the Data:** Create Django models (`ServiceTaxInvoice`, `ServiceTaxInvoiceDetail`, and various lookup/master data models) that map directly to the existing SQL Server database tables using `managed = False`.
2.  **Define Forms:** Create Django `ModelForm`s for the `ServiceTaxInvoice` (covering the main invoice data across tabs) and a separate mini-form for `ServiceTaxInvoiceDetail` for inline grid editing.
3.  **Implement Views (Thin Views):** Use Django CBVs (`UpdateView`, `TemplateView` for the main page, `View` for HTMX partials/APIs). Business logic (data lookups, quantity calculations, validations) will reside primarily in the models.
4.  **Create Templates (DRY & HTMX-driven):** Design HTML templates using Tailwind CSS for styling, extending `core/base.html`. Utilize HTMX extensively for tab switching, search button actions, dynamic dropdowns, and inline grid editing, minimizing full page reloads.
5.  **Set Up URLs:** Define clear, RESTful URL patterns for the views, including specific endpoints for HTMX partials.
6.  **Write Tests:** Develop comprehensive unit tests for model methods and integration tests for view functionality.

---

## ASP.NET to Django Conversion Script: 

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

- Search for database-related elements like SqlDataSource, connection strings, or SQL commands (e.g., SELECT, INSERT, UPDATE, DELETE).
- Extract the table name and assign it to [TABLE_NAME].
- Identify column names (e.g., [COLUMN1], [COLUMN2]) and, if available, their data types from SQL statements or UI bindings (e.g., GridView columns).
- If columns are not explicitly listed (e.g., SELECT *), infer them from UI controls or other data operations.

**Analysis:**

The ASP.NET code reveals interactions with several SQL Server tables. The primary entities for this specific page are `tblACC_ServiceTaxInvoice_Master` (for the main invoice details) and `tblACC_ServiceTaxInvoice_Details` (for the invoice line items). Numerous lookup tables and related master data tables (`SD_Cust_master`, `Unit_Master`, `tblACC_Service_Category`, etc.) are queried to populate dropdowns and display related information.

**Inferred Tables and Columns:**

*   **`tblACC_ServiceTaxInvoice_Master`**:
    `Id`, `SysDate`, `SysTime`, `CompId`, `FinYearId`, `SessionId`, `InvoiceNo`, `POId`, `PONo`, `WONo`, `DateOfIssueInvoice`, `TimeOfIssueInvoice`, `DutyRate`, `CustomerCode`, `CustomerCategory`, `Buyer_name`, `Buyer_add`, `Buyer_city`, `Buyer_state`, `Buyer_country`, `Buyer_cotper`, `Buyer_ph`, `Buyer_email`, `Buyer_ecc`, `Buyer_tin`, `Buyer_mob`, `Buyer_fax`, `Buyer_vat`, `Cong_name`, `Cong_add`, `Cong_city`, `Cong_state`, `Cong_country`, `Cong_cotper`, `Cong_ph`, `Cong_email`, `Cong_ecc`, `Cong_tin`, `Cong_mob`, `Cong_fax`, `Cong_vat`, `AddType`, `AddAmt`, `DeductionType`, `Deduction`, `ServiceTax`, `TaxableServices`.

*   **`tblACC_ServiceTaxInvoice_Details`**:
    `Id`, `MId`, `InvoiceNo`, `ItemId`, `Unit`, `Qty`, `ReqQty`, `AmtInPer`, `Rate`.

*   **Auxiliary/Lookup Tables (simplified to relevant fields for this module):**
    *   `tblACC_Service_Category`: `Id`, `Description`
    *   `tblACC_TaxableServices`: `Id`, `Description`
    *   `Unit_Master`: `Id`, `Symbol`
    *   `tblExciseser_Master`: `Id`, `Terms`
    *   `SD_Cust_master`: `CustomerId`, `CustomerName`, `MaterialDelAddress`, `MaterialDelCountry`, `MaterialDelState`, `MaterialDelCity`, `MaterialDelContactNo`, `MaterialDelFaxNo`, `ContactPerson`, `Email`, `TinVatNo`, `EccNo`, `ContactNo`, `TinCstNo`, `CompId`
    *   `SD_Cust_PO_Master`: `POId`, `PONo`, `PODate`, `CompId`
    *   `SD_Cust_PO_Details`: `Id`, `POId`, `ItemDesc`, `TotalQty`, `Unit`, `Rate`
    *   `SD_Cust_WorkOrder_Master`: `Id`, `WONo`, `CompId`
    *   `tblCountry_Master`: `CId`, `CName`
    *   `tblState_Master`: `SId`, `CId`, `SName`
    *   `tblCity_Master`: `CityId`, `SId`, `CityName`
    *   `tblCompany_Master`: `CompId`, `CompName` (inferred for `CompId` relationship)
    *   `tblFinancialYear_Master`: `FinYearId`, `FinYearName` (inferred for `FinYearId` relationship)

### Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

Create: Look for insert operations (e.g., button click events, GridView footer templates).
Read: Find select statements or data binding (e.g., GridView population).
Update: Identify update commands (e.g., GridView edit mode, form submissions).
Delete: Locate delete operations (e.g., button or link triggers).
Record any validation logic (e.g., required fields) for replication in Django.

**Analysis:**

This ASP.NET page is primarily designed for **editing** an existing `ServiceTaxInvoice`.

*   **Create:** No direct creation functionality is found on this "Edit" page.
*   **Read:**
    *   On `Page_Load`, the system fetches an existing `ServiceTaxInvoice` master record using `invId` from the query string. It populates various labels, textboxes, and dropdowns with this data.
    *   It also dynamically populates dropdowns for Country, State, City, Service Category, Taxable Services, and Service Tax from their respective master tables.
    *   The `fillgrid()` method retrieves and displays `ServiceTaxInvoice_Details` (line items) from `tblACC_ServiceTaxInvoice_Details`, joining with `tblACC_ServiceTaxInvoice_Master` and further looking up `SD_Cust_PO_Details` and `Unit_Master` for display. It calculates "Remaining Qty" on the fly.
*   **Update:**
    *   The `BtnUpdate_Click` method orchestrates the update. It updates the `tblACC_ServiceTaxInvoice_Master` record with data from the various input fields across the tabs.
    *   It then iterates through `GridView1` to update selected `tblACC_ServiceTaxInvoice_Details` records, specifically `ReqQty`, `Unit`, and `AmtInPer`. Crucially, it performs a quantity validation (`(rmnqty + ReqQty1) >= ReqQty`) before allowing an update.
*   **Delete:** No delete functionality is present on this page.
*   **Validation Logic:** Extensive client-side (ASP.NET validators) and server-side validation (e.g., `fun.DateValidation`, `fun.EmailValidation`, `fun.NumberValidationQty`, quantity checks in `BtnUpdate_Click`) are implemented.

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

- Identify controls like:
  - GridView: Displays data lists.
  - TextBox, DropDownList: Captures user input.
  - Button, LinkButton: Triggers actions.

- Note field mappings (e.g., which columns are bound to which controls).
- Check for JavaScript used in client-side interactions for potential Alpine.js conversion.

**Analysis:**

The UI is structured around an ASP.NET `TabContainer` with four main panels: "Buyer", "Consignee", "Goods", and "Taxation". Data entry is done via `TextBox`es and `DropDownList`s. There's a `GridView` for "Goods" line items which allows for inline editing triggered by a checkbox. Client-side functionality includes date/time pickers and autocomplete for customer names.

**Key UI Components and Django Equivalents:**

*   **Master Page (`~/MasterPage.master`)**: This will be replaced by Django's template inheritance, specifically extending `core/base.html`. This base template will handle common layout, CSS (Tailwind CSS), and JavaScript libraries (HTMX, Alpine.js, DataTables).
*   **`TabContainer` (`TabContainer1`)**: This functionality will be reimplemented using HTMX for tab switching, where each tab's content can be loaded via a separate HTMX request or managed by Alpine.js for showing/hiding content based on state.
*   **Labels (`<asp:Label>`)**: Will be rendered using Django template variables (e.g., `{{ invoice.invoice_no }}`).
*   **Textboxes (`<asp:TextBox>`)**: Will be rendered by Django forms using appropriate input types (`text`, `number`, `email`, `date`, `time`). Tailwind CSS classes will be applied via form widgets. Autocomplete functionality will be provided by HTMX.
*   **Dropdowns (`<asp:DropDownList>`)**: Will be rendered by Django `forms.ModelChoiceField` with `<select>` widgets. Cascading dropdowns (Country -> State -> City) will be handled by HTMX requests to specific Django view endpoints.
*   **CalendarExtender / TimeSelector**: Date inputs will use HTML5 `type="date"`. The time selector will be a text input with appropriate validation or a simple Alpine.js component if a custom picker is required.
*   **Buttons (`<asp:Button>`)**: Standard HTML `<button>` elements. Navigation buttons will use HTMX to swap tab content or trigger a state change. The "Update" button will submit the main form via HTMX. "Search" and "Copy from Buyer" buttons will trigger HTMX requests to populate form fields dynamically.
*   **`GridView` (`GridView1`)**: This will be transformed into a standard HTML `<table>` managed by DataTables for client-side features like pagination, sorting, and searching. Inline editing will be implemented using HTMX. When a checkbox is clicked, HTMX will swap the read-only display of `ReqQty`, `AmtInPer`, `Unit` with editable form fields (`<input type="number">`, `<select>`), allowing updates to individual line items via HTMX.

### Step 4: Generate Django Code

The Django application for this module will be named `invoicing`.

### 4.1 Models

Task: Create Django models based on the database schema.

## Instructions:

- Name the model [MODEL_NAME] (e.g., capitalize and singularize [TABLE_NAME]).
- Define fields ([FIELD1], [FIELD2], etc.) with appropriate Django field types based on column data types.
- Use db_column if field names differ from column names.
- Set managed = False and db_table = '[TABLE_NAME]' in the Meta class.
- Include model methods for business logic (fat model approach).

**`invoicing/models.py`**
```python
from django.db import models
from django.utils import timezone
import datetime

# --- Lookup/Master Models (representing existing database tables) ---
# These models map to existing database tables. 'managed = False' means Django
# will not create/alter these tables, only map to them.

class Company(models.Model):
    id = models.IntegerField(db_column='CompId', primary_key=True)
    name = models.CharField(db_column='CompName', max_length=255) # Inferred name field

    class Meta:
        managed = False
        db_table = 'tblCompany_Master' # Example table name, adjust as per actual DB
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name

class FinancialYear(models.Model):
    id = models.IntegerField(db_column='FinYearId', primary_key=True)
    year_name = models.CharField(db_column='FinYearName', max_length=50) # Inferred name field

    class Meta:
        managed = False
        db_table = 'tblFinancialYear_Master' # Example table name, adjust as per actual DB
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.year_name

class ServiceCategory(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblACC_Service_Category'
        verbose_name = 'Service Category'
        verbose_name_plural = 'Service Categories'

    def __str__(self):
        return self.description

class TaxableService(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblACC_TaxableServices'
        verbose_name = 'Taxable Service'
        verbose_name_plural = 'Taxable Services'

    def __str__(self):
        return self.description

class Unit(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class ExciseMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblExciseser_Master'
        verbose_name = 'Excise Master'
        verbose_name_plural = 'Excise Masters'

    def __str__(self):
        return self.terms

class Country(models.Model):
    id = models.IntegerField(db_column='CId', primary_key=True)
    name = models.CharField(db_column='CName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblCountry_Master' # Example table name
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.name

class State(models.Model):
    id = models.IntegerField(db_column='SId', primary_key=True)
    country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='CId', related_name='states')
    name = models.CharField(db_column='SName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblState_Master' # Example table name
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.name

class City(models.Model):
    id = models.IntegerField(db_column='CityId', primary_key=True)
    state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='SId', related_name='cities')
    name = models.CharField(db_column='CityName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblCity_Master' # Example table name
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.name

class Customer(models.Model):
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    address = models.TextField(db_column='MaterialDelAddress', blank=True, null=True)
    country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='MaterialDelCountry', blank=True, null=True, related_name='customer_countries')
    state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='MaterialDelState', blank=True, null=True, related_name='customer_states')
    city = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='MaterialDelCity', blank=True, null=True, related_name='customer_cities')
    contact_person = models.CharField(db_column='ContactPerson', max_length=100, blank=True, null=True)
    phone_no = models.CharField(db_column='MaterialDelContactNo', max_length=50, blank=True, null=True)
    mobile_no = models.CharField(db_column='ContactNo', max_length=50, blank=True, null=True)
    fax_no = models.CharField(db_column='MaterialDelFaxNo', max_length=50, blank=True, null=True)
    email = models.CharField(db_column='Email', max_length=100, blank=True, null=True)
    tin_vat_no = models.CharField(db_column='TinVatNo', max_length=50, blank=True, null=True)
    tin_cst_no = models.CharField(db_column='TinCstNo', max_length=50, blank=True, null=True)
    ecc_no = models.CharField(db_column='EccNo', max_length=50, blank=True, null=True)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customer_name} [{self.customer_id}]"

class PurchaseOrder(models.Model):
    id = models.IntegerField(db_column='POId', primary_key=True)
    po_no = models.CharField(db_column='PONo', max_length=100)
    po_date = models.DateField(db_column='PODate')
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Master'
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'

    def __str__(self):
        return self.po_no

class PurchaseOrderDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # ItemId in ServiceTaxInvoiceDetail actually points to this Id
    po = models.ForeignKey(PurchaseOrder, on_delete=models.DO_NOTHING, db_column='POId', related_name='details')
    item_description = models.CharField(db_column='ItemDesc', max_length=255)
    total_qty = models.FloatField(db_column='TotalQty')
    unit = models.ForeignKey(Unit, on_delete=models.DO_NOTHING, db_column='Unit')
    rate = models.FloatField(db_column='Rate')

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Details'
        verbose_name = 'Purchase Order Detail'
        verbose_name_plural = 'Purchase Order Details'

    def __str__(self):
        return f"{self.item_description} ({self.po.po_no})"

class WorkOrder(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    wo_no = models.CharField(db_column='WONo', max_length=100)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.wo_no


# --- Core Service Tax Invoice Models ---

class ServiceTaxInvoice(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId')
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId')
    session_id = models.CharField(db_column='SessionId', max_length=255)
    invoice_no = models.CharField(db_column='InvoiceNo', max_length=100)
    
    po = models.ForeignKey(PurchaseOrder, on_delete=models.DO_NOTHING, db_column='POId', blank=True, null=True)
    po_no = models.CharField(db_column='PONo', max_length=100, blank=True, null=True) # Denormalized
    wo_numbers_raw = models.CharField(db_column='WONo', max_length=255, blank=True, null=True) # Comma-separated IDs

    date_of_issue_invoice = models.DateField(db_column='DateOfIssueInvoice')
    time_of_issue_invoice = models.CharField(db_column='TimeOfIssueInvoice', max_length=50) # Stored as string "HH:MM:SS AM/PM"

    duty_rate = models.FloatField(db_column='DutyRate')
    
    customer_code = models.CharField(db_column='CustomerCode', max_length=50, blank=True, null=True)
    customer_category = models.ForeignKey(ServiceCategory, on_delete=models.DO_NOTHING, db_column='CustomerCategory', blank=True, null=True)
    taxable_services = models.ForeignKey(TaxableService, on_delete=models.DO_NOTHING, db_column='TaxableServices', blank=True, null=True)

    # Buyer Details
    buyer_name = models.CharField(db_column='Buyer_name', max_length=255)
    buyer_address = models.TextField(db_column='Buyer_add')
    buyer_city = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='Buyer_city', related_name='buyer_invoices', blank=True, null=True)
    buyer_state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='Buyer_state', related_name='buyer_invoices', blank=True, null=True)
    buyer_country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='Buyer_country', related_name='buyer_invoices', blank=True, null=True)
    buyer_contact_person = models.CharField(db_column='Buyer_cotper', max_length=100)
    buyer_phone = models.CharField(db_column='Buyer_ph', max_length=50)
    buyer_email = models.CharField(db_column='Buyer_email', max_length=100)
    buyer_ecc = models.CharField(db_column='Buyer_ecc', max_length=50)
    buyer_tin_cst = models.CharField(db_column='Buyer_tin', max_length=50)
    buyer_mobile = models.CharField(db_column='Buyer_mob', max_length=50)
    buyer_fax = models.CharField(db_column='Buyer_fax', max_length=50)
    buyer_tin_vat = models.CharField(db_column='Buyer_vat', max_length=50)

    # Consignee Details
    consignee_name = models.CharField(db_column='Cong_name', max_length=255)
    consignee_address = models.TextField(db_column='Cong_add')
    consignee_city = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='Cong_city', related_name='consignee_invoices', blank=True, null=True)
    consignee_state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='Cong_state', related_name='consignee_invoices', blank=True, null=True)
    consignee_country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='Cong_country', related_name='consignee_invoices', blank=True, null=True)
    consignee_contact_person = models.CharField(db_column='Cong_cotper', max_length=100)
    consignee_phone = models.CharField(db_column='Cong_ph', max_length=50)
    consignee_email = models.CharField(db_column='Cong_email', max_length=100)
    consignee_ecc = models.CharField(db_column='Cong_ecc', max_length=50)
    consignee_tin_cst = models.CharField(db_column='Cong_tin', max_length=50)
    consignee_mobile = models.CharField(db_column='Cong_mob', max_length=50)
    consignee_fax = models.CharField(db_column='Cong_fax', max_length=50)
    consignee_tin_vat = models.CharField(db_column='Cong_vat', max_length=50)

    # Taxation Details
    add_type = models.CharField(db_column='AddType', max_length=10) # 0 for Amt(Rs), 1 for Per(%)
    add_amount = models.FloatField(db_column='AddAmt')
    deduction_type = models.CharField(db_column='DeductionType', max_length=10) # 0 for Amt(Rs), 1 for Per(%)
    deduction_amount = models.FloatField(db_column='Deduction')
    service_tax = models.ForeignKey(ExciseMaster, on_delete=models.DO_NOTHING, db_column='ServiceTax', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_ServiceTaxInvoice_Master'
        verbose_name = 'Service Tax Invoice'
        verbose_name_plural = 'Service Tax Invoices'

    def __str__(self):
        return self.invoice_no

    @property
    def formatted_sys_date(self):
        return self.sys_date.strftime('%d-%m-%Y') if self.sys_date else ''

    @property
    def formatted_date_of_issue_invoice(self):
        return self.date_of_issue_invoice.strftime('%d-%m-%Y') if self.date_of_issue_invoice else ''

    @property
    def wo_numbers_list(self):
        """
        Converts comma-separated Work Order IDs (WONo) into a list of actual WO numbers.
        """
        if not self.wo_numbers_raw:
            return []
        try:
            wo_ids = [int(x) for x in self.wo_numbers_raw.split(',') if x.strip()]
            return list(WorkOrder.objects.filter(id__in=wo_ids, company=self.company).values_list('wo_no', flat=True))
        except ValueError:
            return [] # Handle cases where raw string might not be clean integers

    def get_po_date_formatted(self):
        """Retrieves and formats the PO date from the related PurchaseOrder."""
        if self.po:
            return self.po.po_date.strftime('%d-%m-%Y')
        return ''

    def update_master_data(self, data, session_user_id):
        """
        Updates the main ServiceTaxInvoice fields from form data.
        This method encapsulates the master record update logic from BtnUpdate_Click.
        """
        self.sys_date = timezone.now().date()
        self.sys_time = timezone.now().time()
        self.session_id = session_user_id 

        self.date_of_issue_invoice = data.get('date_of_issue_invoice', self.date_of_issue_invoice)
        self.time_of_issue_invoice = data.get('time_of_issue_invoice', self.time_of_issue_invoice)
        self.duty_rate = data.get('duty_rate', self.duty_rate)
        self.customer_category_id = data.get('customer_category')
        self.taxable_services_id = data.get('taxable_services')

        self.buyer_name = data.get('buyer_name_autocomplete', self.buyer_name) # Use autocomplete field
        self.buyer_address = data.get('buyer_address', self.buyer_address)
        self.buyer_country_id = data.get('buyer_country')
        self.buyer_state_id = data.get('buyer_state')
        self.buyer_city_id = data.get('buyer_city')
        self.buyer_contact_person = data.get('buyer_contact_person', self.buyer_contact_person)
        self.buyer_phone = data.get('buyer_phone', self.buyer_phone)
        self.buyer_email = data.get('buyer_email', self.buyer_email)
        self.buyer_ecc = data.get('buyer_ecc', self.buyer_ecc)
        self.buyer_tin_cst = data.get('buyer_tin_cst', self.buyer_tin_cst)
        self.buyer_mobile = data.get('buyer_mobile', self.buyer_mobile)
        self.buyer_fax = data.get('buyer_fax', self.buyer_fax)
        self.buyer_tin_vat = data.get('buyer_tin_vat', self.buyer_tin_vat)

        self.consignee_name = data.get('consignee_name_autocomplete', self.consignee_name) # Use autocomplete field
        self.consignee_address = data.get('consignee_address', self.consignee_address)
        self.consignee_country_id = data.get('consignee_country')
        self.consignee_state_id = data.get('consignee_state')
        self.consignee_city_id = data.get('consignee_city')
        self.consignee_contact_person = data.get('consignee_contact_person', self.consignee_contact_person)
        self.consignee_phone = data.get('consignee_phone', self.consignee_phone)
        self.consignee_email = data.get('consignee_email', self.consignee_email)
        self.consignee_ecc = data.get('consignee_ecc', self.consignee_ecc)
        self.consignee_tin_cst = data.get('consignee_tin_cst', self.consignee_tin_cst)
        self.consignee_mobile = data.get('consignee_mobile', self.consignee_mobile)
        self.consignee_fax = data.get('consignee_fax', self.consignee_fax)
        self.consignee_tin_vat = data.get('consignee_tin_vat', self.consignee_tin_vat)
        
        self.add_type = data.get('add_type', self.add_type)
        self.add_amount = data.get('add_amount', self.add_amount)
        self.deduction_type = data.get('deduction_type', self.deduction_type)
        self.deduction_amount = data.get('deduction_amount', self.deduction_amount)
        self.service_tax_id = data.get('service_tax')
        self.save()

    def populate_buyer_from_customer(self, customer_code):
        """Populates buyer fields from a Customer record, simulating Button2_Click."""
        try:
            customer = Customer.objects.get(customer_id=customer_code, company=self.company)
            self.buyer_address = customer.address
            self.buyer_country = customer.country
            self.buyer_state = customer.state
            self.buyer_city = customer.city
            self.buyer_fax = customer.fax_no
            self.buyer_contact_person = customer.contact_person
            self.buyer_phone = customer.phone_no
            self.buyer_tin_cst = customer.tin_cst_no
            self.buyer_tin_vat = customer.tin_vat_no
            self.buyer_mobile = customer.mobile_no
            self.buyer_email = customer.email
            self.buyer_ecc = customer.ecc_no
            # Ensure name is updated in autocomplete field, though not saved to DB directly here.
            # This is for UI update only.
            return True, None # Success
        except Customer.DoesNotExist:
            return False, "Invalid selection of Customer data."
        except Exception as e:
            return False, str(e)

    def populate_consignee_from_customer(self, customer_code):
        """Populates consignee fields from a Customer record, simulating Button3_Click."""
        try:
            customer = Customer.objects.get(customer_id=customer_code, company=self.company)
            self.consignee_address = customer.address
            self.consignee_country = customer.country
            self.consignee_state = customer.state
            self.consignee_city = customer.city
            self.consignee_fax = customer.fax_no
            self.consignee_contact_person = customer.contact_person
            self.consignee_phone = customer.phone_no
            self.consignee_tin_cst = customer.tin_cst_no
            self.consignee_tin_vat = customer.tin_vat_no
            self.consignee_mobile = customer.mobile_no
            self.consignee_email = customer.email
            self.consignee_ecc = customer.ecc_no
            # Ensure name is updated in autocomplete field.
            return True, None
        except Customer.DoesNotExist:
            return False, "Invalid selection of Customer data."
        except Exception as e:
            return False, str(e)

    def copy_consignee_from_buyer(self):
        """Copies buyer details to consignee fields, simulating Button5_Click."""
        self.consignee_name = self.buyer_name
        self.consignee_address = self.buyer_address
        self.consignee_country = self.buyer_country
        self.consignee_state = self.buyer_state
        self.consignee_city = self.buyer_city
        self.consignee_fax = self.buyer_fax
        self.consignee_contact_person = self.buyer_contact_person
        self.consignee_phone = self.buyer_phone
        self.consignee_tin_cst = self.buyer_tin_cst
        self.consignee_tin_vat = self.buyer_tin_vat
        self.consignee_mobile = self.buyer_mobile
        self.consignee_email = self.buyer_email
        self.consignee_ecc = self.buyer_ecc
        self.save()


class ServiceTaxInvoiceDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    invoice = models.ForeignKey(ServiceTaxInvoice, on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    invoice_no = models.CharField(db_column='InvoiceNo', max_length=100) # Denormalized
    
    item_id = models.ForeignKey(PurchaseOrderDetail, on_delete=models.DO_NOTHING, db_column='ItemId', related_name='invoice_details')
    
    unit = models.ForeignKey(Unit, on_delete=models.DO_NOTHING, db_column='Unit')
    qty = models.FloatField(db_column='Qty') # Original quantity from PO Detail, as stored on invoice detail
    req_qty = models.FloatField(db_column='ReqQty') # Requested quantity for this invoice
    amt_in_per = models.FloatField(db_column='AmtInPer')
    rate = models.FloatField(db_column='Rate')

    class Meta:
        managed = False
        db_table = 'tblACC_ServiceTaxInvoice_Details'
        verbose_name = 'Service Tax Invoice Detail'
        verbose_name_plural = 'Service Tax Invoice Details'

    def __str__(self):
        return f"Detail for {self.invoice.invoice_no} - {self.item_id.item_description}"

    @property
    def item_description(self):
        return self.item_id.item_description

    @property
    def unit_symbol(self):
        return self.unit.symbol

    @property
    def po_item_rate(self):
        return self.item_id.rate

    @property
    def original_unit_symbol(self):
        # Unit for the original PO item
        return self.item_id.unit.symbol if self.item_id and self.item_id.unit else ''

    def get_remaining_qty(self):
        """
        Calculates the remaining quantity available for this item from its original PO.
        This mirrors the ASP.NET calculation by summing `ReqQty` across all
        invoice details for the same `ItemId` (PO item) and subtracting from the
        `total_qty` of that `PO_Detail`.
        """
        # Sum of *all* requested quantities for this item across *all* invoices in the same company
        total_invoiced_qty = ServiceTaxInvoiceDetail.objects.filter(
            item_id=self.item_id,
            invoice__company=self.invoice.company # Ensure same company
        ).aggregate(total=models.Sum('req_qty'))['total'] or 0.0

        # The original quantity from the Purchase Order Detail
        original_po_qty = self.item_id.total_qty

        # Remaining quantity calculation, accounting for current item's original req_qty
        # rmnqty = Qty (from PO) - TotInvQty (all invoiced for item)
        # (rmnqty + ReqQty1 (this item's old ReqQty)) >= NewReqQty
        # This means, (PO_QTY - SUM_ALL_INVOICED_QTY) + THIS_ITEM_OLD_REQ_QTY >= NEW_REQ_QTY
        # A simpler approach: available = PO_QTY - (SUM_INVOICED_EXCLUDING_THIS_ITEM)
        total_invoiced_excluding_self = ServiceTaxInvoiceDetail.objects.filter(
            item_id=self.item_id,
            invoice__company=self.invoice.company
        ).exclude(pk=self.pk).aggregate(total=models.Sum('req_qty'))['total'] or 0.0
        
        available_for_this_item = original_po_qty - total_invoiced_excluding_self

        return round(available_for_this_item, 3)

    def validate_and_update_detail(self, new_req_qty, new_unit, new_amt_in_per):
        """
        Performs the quantity validation and updates the detail record.
        Returns (True, None) on success or (False, error_message) on failure.
        """
        # Get the quantity available for this PO item (excluding what this specific detail line currently holds)
        available_qty_for_new_entry = self.get_remaining_qty()
        
        if new_req_qty > available_qty_for_new_entry:
            return False, f"Requested quantity {new_req_qty} for {self.item_description} exceeds available quantity {available_qty_for_new_entry}."
        
        self.req_qty = new_req_qty
        self.unit = new_unit # Assuming new_unit is a Unit object
        self.amt_in_per = new_amt_in_per
        self.save()
        return True, None

```

### 4.2 Forms

Task: Define Django forms for user input.

## Instructions:

Create a ModelForm for [MODEL_NAME].
Include editable fields ([FIELD1], [FIELD2], etc.).
Add widgets with Tailwind CSS classes for styling.
Implement form validation logic based on ASP.NET validation.

**`invoicing/forms.py`**
```python
from django import forms
from .models import (
    ServiceTaxInvoice, ServiceTaxInvoiceDetail, ServiceCategory, TaxableService,
    Country, State, City, ExciseMaster, Unit, Customer
)
from django.core.exceptions import ValidationError
import re

# Helper function for common widget attributes to keep forms DRY
def get_common_attrs():
    return {'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}

class ServiceTaxInvoiceForm(forms.ModelForm):
    # These fields are primarily for initial display and hidden submission
    invoice_id = forms.IntegerField(required=False, widget=forms.HiddenInput())
    company_id = forms.IntegerField(required=False, widget=forms.HiddenInput())
    financial_year_id = forms.IntegerField(required=False, widget=forms.HiddenInput())
    session_id = forms.CharField(required=False, widget=forms.HiddenInput())

    # Fields corresponding to dropdowns, populated dynamically or from DB
    customer_category = forms.ModelChoiceField(
        queryset=ServiceCategory.objects.all(),
        required=True,
        empty_label="-- Select Category --",
        widget=forms.Select(attrs=get_common_attrs())
    )
    taxable_services = forms.ModelChoiceField(
        queryset=TaxableService.objects.all(),
        required=True,
        empty_label="-- Select Taxable Service --",
        widget=forms.Select(attrs=get_common_attrs())
    )
    service_tax = forms.ModelChoiceField(
        queryset=ExciseMaster.objects.all().order_by('-id'),
        required=True,
        empty_label="-- Select Service Tax --",
        widget=forms.Select(attrs=get_common_attrs())
    )

    # Cascading Dropdowns for Buyer Address
    buyer_country = forms.ModelChoiceField(
        queryset=Country.objects.all(),
        required=True,
        empty_label="-- Select Country --",
        widget=forms.Select(attrs={
            **get_common_attrs(),
            'hx-get': '/invoicing/get-states/', # Endpoint for state options
            'hx-target': '#id_buyer_state',
            'hx-trigger': 'change',
            'hx-swap': 'outerHTML'
        })
    )
    buyer_state = forms.ModelChoiceField(
        queryset=State.objects.none(), # Populated dynamically via HTMX
        required=True,
        empty_label="-- Select State --",
        widget=forms.Select(attrs={
            **get_common_attrs(),
            'hx-get': '/invoicing/get-cities/', # Endpoint for city options
            'hx-target': '#id_buyer_city',
            'hx-trigger': 'change',
            'hx-swap': 'outerHTML'
        })
    )
    buyer_city = forms.ModelChoiceField(
        queryset=City.objects.none(), # Populated dynamically via HTMX
        required=True,
        empty_label="-- Select City --",
        widget=forms.Select(attrs=get_common_attrs())
    )

    # Cascading Dropdowns for Consignee Address
    consignee_country = forms.ModelChoiceField(
        queryset=Country.objects.all(),
        required=True,
        empty_label="-- Select Country --",
        widget=forms.Select(attrs={
            **get_common_attrs(),
            'hx-get': '/invoicing/get-states/',
            'hx-target': '#id_consignee_state',
            'hx-trigger': 'change',
            'hx-swap': 'outerHTML'
        })
    )
    consignee_state = forms.ModelChoiceField(
        queryset=State.objects.none(),
        required=True,
        empty_label="-- Select State --",
        widget=forms.Select(attrs={
            **get_common_attrs(),
            'hx-get': '/invoicing/get-cities/',
            'hx-target': '#id_consignee_city',
            'hx-trigger': 'change',
            'hx-swap': 'outerHTML'
        })
    )
    consignee_city = forms.ModelChoiceField(
        queryset=City.objects.none(),
        required=True,
        empty_label="-- Select City --",
        widget=forms.Select(attrs=get_common_attrs())
    )

    # Autocomplete fields for Buyer/Consignee Name
    buyer_name_autocomplete = forms.CharField(
        label="Name", # Label for the text input
        required=True,
        widget=forms.TextInput(attrs={
            **get_common_attrs(),
            'hx-post': '/invoicing/customer-autocomplete/',
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#buyer-name-suggestions',
            'hx-swap': 'outerHTML',
            'hx-indicator': '.htmx-indicator',
            'autocomplete': 'off',
            'placeholder': 'Start typing customer name or ID'
        })
    )
    consignee_name_autocomplete = forms.CharField(
        label="Name",
        required=True,
        widget=forms.TextInput(attrs={
            **get_common_attrs(),
            'hx-post': '/invoicing/customer-autocomplete/',
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#consignee-name-suggestions',
            'hx-swap': 'outerHTML',
            'hx-indicator': '.htmx-indicator',
            'autocomplete': 'off',
            'placeholder': 'Start typing customer name or ID'
        })
    )

    class Meta:
        model = ServiceTaxInvoice
        fields = [
            'invoice_id', 'company_id', 'financial_year_id', 'session_id', # Hidden fields
            'duty_rate', 'date_of_issue_invoice', 'time_of_issue_invoice',
            'customer_category', 'taxable_services',
            'buyer_name_autocomplete', 'buyer_address', 'buyer_country', 'buyer_state', 'buyer_city',
            'buyer_contact_person', 'buyer_phone', 'buyer_mobile', 'buyer_fax', 'buyer_email',
            'buyer_ecc', 'buyer_tin_cst', 'buyer_tin_vat',
            'consignee_name_autocomplete', 'consignee_address', 'consignee_country', 'consignee_state', 'consignee_city',
            'consignee_contact_person', 'consignee_phone', 'consignee_mobile', 'consignee_fax', 'consignee_email',
            'consignee_ecc', 'consignee_tin_cst', 'consignee_tin_vat',
            'add_type', 'add_amount', 'deduction_type', 'deduction_amount', 'service_tax',
        ]
        # Map fields to widgets with Tailwind CSS classes
        widgets = {
            'date_of_issue_invoice': forms.DateInput(attrs={**get_common_attrs(), 'type': 'date'}),
            'time_of_issue_invoice': forms.TextInput(attrs={**get_common_attrs(), 'placeholder': 'HH:MM:SS AM/PM'}),
            'duty_rate': forms.NumberInput(attrs={**get_common_attrs(), 'step': '0.01'}),
            'buyer_address': forms.Textarea(attrs={**get_common_attrs(), 'rows': 4}),
            'buyer_contact_person': forms.TextInput(attrs=get_common_attrs()),
            'buyer_phone': forms.TextInput(attrs=get_common_attrs()),
            'buyer_mobile': forms.TextInput(attrs=get_common_attrs()),
            'buyer_fax': forms.TextInput(attrs=get_common_attrs()),
            'buyer_email': forms.EmailInput(attrs=get_common_attrs()),
            'buyer_ecc': forms.TextInput(attrs=get_common_attrs()),
            'buyer_tin_cst': forms.TextInput(attrs=get_common_attrs()),
            'buyer_tin_vat': forms.TextInput(attrs=get_common_attrs()),

            'consignee_address': forms.Textarea(attrs={**get_common_attrs(), 'rows': 4}),
            'consignee_contact_person': forms.TextInput(attrs=get_common_attrs()),
            'consignee_phone': forms.TextInput(attrs=get_common_attrs()),
            'consignee_mobile': forms.TextInput(attrs=get_common_attrs()),
            'consignee_fax': forms.TextInput(attrs=get_common_attrs()),
            'consignee_email': forms.EmailInput(attrs=get_common_attrs()),
            'consignee_ecc': forms.TextInput(attrs=get_common_attrs()),
            'consignee_tin_cst': forms.TextInput(attrs=get_common_attrs()),
            'consignee_tin_vat': forms.TextInput(attrs=get_common_attrs()),
            
            'add_type': forms.Select(attrs=get_common_attrs(), choices=[('0', 'Amt(Rs)'), ('1', 'Per(%)')]),
            'add_amount': forms.NumberInput(attrs={**get_common_attrs(), 'step': '0.01'}),
            'deduction_type': forms.Select(attrs=get_common_attrs(), choices=[('0', 'Amt(Rs)'), ('1', 'Per(%)')]),
            'deduction_amount': forms.NumberInput(attrs={**get_common_attrs(), 'step': '0.01'}),
        }
        labels = {
            'duty_rate': 'Rate of Duty',
            'date_of_issue_invoice': 'Date Of Issue Of Invoice',
            'time_of_issue_invoice': 'Time Of Issue Of Invoice',
            'customer_category': 'Category',
            'taxable_services': 'Taxable Services',
            'buyer_address': 'Address', 'buyer_country': 'Country', 'buyer_state': 'State', 'buyer_city': 'City',
            'buyer_contact_person': 'Contact Person', 'buyer_phone': 'Phone No.', 'buyer_mobile': 'Mobile No.',
            'buyer_fax': 'Fax No.', 'buyer_email': 'E-mail', 'buyer_ecc': "Customer's ECC.No.",
            'buyer_tin_cst': 'TIN / CST No.', 'buyer_tin_vat': 'TIN / VAT No',

            'consignee_address': 'Address', 'consignee_country': 'Country', 'consignee_state': 'State', 'consignee_city': 'City',
            'consignee_contact_person': 'Contact Person', 'consignee_phone': 'Phone No.', 'consignee_mobile': 'Mobile No.',
            'consignee_fax': 'Fax No.', 'consignee_email': 'E-mail', 'consignee_ecc': "Customer's ECC.No.",
            'consignee_tin_cst': 'TIN / CST No.', 'consignee_tin_vat': 'TIN / VAT No.',
            'add_type': 'Add', 'add_amount': 'Add Amount', 'deduction_type': 'Deduction Type',
            'deduction_amount': 'Deduction', 'service_tax': 'Service Tax',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate dynamic dropdowns based on instance data during initialization
        if self.instance.pk:
            if self.instance.buyer_country:
                self.fields['buyer_state'].queryset = State.objects.filter(country=self.instance.buyer_country)
            if self.instance.buyer_state:
                self.fields['buyer_city'].queryset = City.objects.filter(state=self.instance.buyer_state)
            
            if self.instance.consignee_country:
                self.fields['consignee_state'].queryset = State.objects.filter(country=self.instance.consignee_country)
            if self.instance.consignee_state:
                self.fields['consignee_city'].queryset = City.objects.filter(state=self.instance.consignee_state)
            
            # Set initial values for autocomplete fields from instance's name
            self.fields['buyer_name_autocomplete'].initial = self.instance.buyer_name
            self.fields['consignee_name_autocomplete'].initial = self.instance.consignee_name

    def clean_time_of_issue_invoice(self):
        time_str = self.cleaned_data['time_of_issue_invoice']
        # Validate HH:MM:SS AM/PM format matching ASP.NET's MKB.TimePicker output
        if not re.match(r'^(0[1-9]|1[0-2]):([0-5][0-9]):([0-5][0-9]) (AM|PM)$', time_str):
            raise ValidationError("Time must be in HH:MM:SS AM/PM format (e.g., 01:30:00 PM).")
        return time_str

    def clean_duty_rate(self):
        duty_rate = self.cleaned_data['duty_rate']
        if duty_rate is not None and duty_rate < 0:
            raise ValidationError("Duty Rate cannot be negative.")
        return duty_rate

    def clean_add_amount(self):
        value = self.cleaned_data['add_amount']
        if value is not None and value < 0:
            raise ValidationError("Addition amount cannot be negative.")
        return value

    def clean_deduction_amount(self):
        value = self.cleaned_data['deduction_amount']
        if value is not None and value < 0:
            raise ValidationError("Deduction amount cannot be negative.")
        return value

    def clean(self):
        cleaned_data = super().clean()
        # Add any cross-field validation here if necessary
        return cleaned_data


class ServiceTaxInvoiceDetailForm(forms.ModelForm):
    # These fields are for editing within the DataTables row
    req_qty = forms.FloatField(
        label="Req Qty",
        widget=forms.NumberInput(attrs={
            'class': 'block w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm text-sm',
            'step': '0.001',
            'min': '0', # Requested quantity must be positive
        }),
        required=True
    )
    amt_in_per = forms.FloatField(
        label="Amt in (%)",
        widget=forms.NumberInput(attrs={
            'class': 'block w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm text-sm',
            'step': '0.01',
            'min': '0', # Amount in percentage must be positive
        }),
        required=True
    )
    unit = forms.ModelChoiceField(
        queryset=Unit.objects.all(),
        empty_label=None, # No empty label for direct selection
        widget=forms.Select(attrs={
            'class': 'block w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm text-sm',
        }),
        required=True
    )

    class Meta:
        model = ServiceTaxInvoiceDetail
        fields = ['req_qty', 'amt_in_per', 'unit']

    def clean_req_qty(self):
        req_qty = self.cleaned_data['req_qty']
        if req_qty <= 0:
            raise ValidationError("Requested Quantity must be positive.")
        return req_qty

    def clean_amt_in_per(self):
        amt_in_per = self.cleaned_data['amt_in_per']
        if amt_in_per < 0:
            raise ValidationError("Amount in Percentage cannot be negative.")
        return amt_in_per

```

### 4.3 Views

Task: Implement CRUD operations using CBVs.

## Instructions:

## Define:
- ListView: Displays all objects.
- CreateView: Adds new objects.
- UpdateView: Edits existing objects.
- DeleteView: Deletes objects.


Use [MODEL_NAME] as the model, [MODEL_NAME]Form for forms, and set appropriate template_name and success_url.
Add success messages using messages.success.
Keep views thin (5-15 lines) and move business logic to models.

**Analysis:**

Given this is an "Edit Details" page for a specific invoice, the main view will be an `UpdateView` or a `TemplateView` with manual form handling (more flexible for multi-tab forms). We will use `TemplateView` for the main page to handle various HTMX interactions and `UpdateView` logic for the form submission. Specific HTMX endpoints will be created for dropdowns, search, and detail line item updates.

**`invoicing/views.py`**
```python
from django.views.generic import TemplateView, View
from django.views.generic.edit import UpdateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.db.models import Sum

from .models import (
    ServiceTaxInvoice, ServiceTaxInvoiceDetail, ServiceCategory, TaxableService,
    Unit, ExciseMaster, Country, State, City, Customer, PurchaseOrderDetail
)
from .forms import ServiceTaxInvoiceForm, ServiceTaxInvoiceDetailForm

# Helper to get company_id and financial_year_id (simulating Session variables)
# In a real app, these would come from request.user (authenticated user's company/fiscal year)
# For demonstration, we use default values.
def get_context_data_from_session(request):
    # Dummy values for demonstration. Replace with actual session/user data.
    company_id = int(request.session.get('compid', 1)) 
    financial_year_id = int(request.session.get('finyear', 1))
    session_user_id = request.session.get('username', 'system_user') # Assuming username from session
    return company_id, financial_year_id, session_user_id

class ServiceTaxInvoiceEditView(TemplateView):
    """
    Main view for editing a Service Tax Invoice.
    Handles initial loading and renders the multi-tab form.
    """
    template_name = 'invoicing/servicetaxinvoice/edit_details.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        invoice_id = self.kwargs['pk'] # Get invoice ID from URL
        company_id, fin_year_id, session_user_id = get_context_data_from_session(self.request)

        invoice = get_object_or_404(ServiceTaxInvoice, pk=invoice_id, company_id=company_id)
        
        # Initialize the main form with instance data
        form = ServiceTaxInvoiceForm(instance=invoice, initial={
            'invoice_id': invoice.id,
            'company_id': company_id,
            'financial_year_id': fin_year_id,
            'session_id': session_user_id,
        })

        context['invoice'] = invoice
        context['form'] = form
        # Pass the current active tab index (simulating ASP.NET session state)
        context['active_tab_index'] = int(self.request.session.get('tab_index', 0))
        return context

    def post(self, request, *args, **kwargs):
        """
        Handles the main form submission (Update button).
        This will typically be triggered by HTMX.
        """
        invoice_id = self.kwargs['pk']
        company_id, _, session_user_id = get_context_data_from_session(self.request)
        invoice = get_object_or_404(ServiceTaxInvoice, pk=invoice_id, company_id=company_id)
        
        form = ServiceTaxInvoiceForm(request.POST, instance=invoice)

        if form.is_valid():
            # Update master data via model method
            invoice.update_master_data(form.cleaned_data, session_user_id)
            messages.success(request, 'Service Tax Invoice updated successfully.')
            
            # HTMX response for success - trigger list refresh and possibly redirect
            if request.headers.get('HX-Request'):
                # Simulate redirect to list page via HTMX (usually 204 response + HX-Redirect)
                # Or simply clear modal and trigger a refresh of the main table
                return HttpResponse(status=204, headers={'HX-Trigger': 'refreshServiceTaxInvoiceList', 'HX-Redirect': reverse_lazy('servicetaxinvoice_list')})
            return redirect(reverse_lazy('servicetaxinvoice_list'))
        else:
            # If form is invalid, render the form again, possibly with error messages
            # For HTMX, this might involve re-rendering the specific tab or the whole form.
            # For simplicity, we'll return the full page with errors on the relevant tab.
            messages.error(request, 'Please correct the errors in the form.')
            context = self.get_context_data(**kwargs)
            context['form'] = form # Pass form with errors
            # Determine which tab had errors and set it active
            if 'buyer_name_autocomplete' in form.errors or 'buyer_address' in form.errors:
                context['active_tab_index'] = 0 # Buyer tab
            elif 'consignee_name_autocomplete' in form.errors or 'consignee_address' in form.errors:
                context['active_tab_index'] = 1 # Consignee tab
            elif 'req_qty' in form.errors or 'amt_in_per' in form.errors: # For line item updates (not directly here)
                context['active_tab_index'] = 2 # Goods tab
            else:
                context['active_tab_index'] = 3 # Taxation tab (or default)
            return render(request, self.template_name, context)

# --- HTMX Partial Views and API Endpoints ---

class ServiceTaxInvoiceTablePartialView(View):
    """
    Renders the 'Goods' tab's table content, intended for HTMX requests.
    """
    def get(self, request, pk, *args, **kwargs):
        invoice = get_object_or_404(ServiceTaxInvoice, pk=pk)
        details = invoice.details.all().order_by('id') # Fetch all detail items
        
        # Prepare data for rendering, including calculated remaining quantity
        invoice_details_data = []
        for detail in details:
            # Get the company_id from the invoice to pass to the model method
            company_id = invoice.company_id 
            remaining_qty = detail.get_remaining_qty()
            invoice_details_data.append({
                'detail': detail,
                'remaining_qty': remaining_qty,
                'form': ServiceTaxInvoiceDetailForm(instance=detail) # Initialize form for each detail
            })

        context = {
            'invoice_details_data': invoice_details_data,
            'all_units': Unit.objects.all().order_by('id') # For dropdown in grid
        }
        return render(request, 'invoicing/servicetaxinvoice/_goods_table_partial.html', context)

class ServiceTaxInvoiceDetailUpdateView(View):
    """
    Handles HTMX requests to update a single ServiceTaxInvoiceDetail line item.
    Corresponds to the `ck_CheckedChanged` logic and part of `BtnUpdate_Click` for details.
    """
    def post(self, request, invoice_pk, detail_pk, *args, **kwargs):
        detail = get_object_or_404(ServiceTaxInvoiceDetail, pk=detail_pk, invoice_id=invoice_pk)
        
        # Get company_id from the related invoice
        company_id = detail.invoice.company_id
        
        form = ServiceTaxInvoiceDetailForm(request.POST, instance=detail)
        
        if form.is_valid():
            new_req_qty = form.cleaned_data['req_qty']
            new_unit = form.cleaned_data['unit']
            new_amt_in_per = form.cleaned_data['amt_in_per']

            success, message = detail.validate_and_update_detail(new_req_qty, new_unit, new_amt_in_per)
            
            if success:
                # Re-render the updated row
                context = {
                    'detail': detail,
                    'remaining_qty': detail.get_remaining_qty(),
                    'all_units': Unit.objects.all().order_by('id'),
                    'form': ServiceTaxInvoiceDetailForm(instance=detail) # Re-initialize form for future edits
                }
                messages.success(request, f"Item '{detail.item_description}' updated successfully.")
                # Return the updated row HTML for HTMX swap (e.g., hx-swap="outerHTML")
                return render(request, 'invoicing/servicetaxinvoice/_goods_table_row.html', context)
            else:
                # If validation fails at model level (e.g., quantity check)
                messages.error(request, message)
                # Re-render the row with the form and error message
                context = {
                    'detail': detail,
                    'remaining_qty': detail.get_remaining_qty(),
                    'all_units': Unit.objects.all().order_by('id'),
                    'form': form # Pass form with errors
                }
                return render(request, 'invoicing/servicetaxinvoice/_goods_table_row.html', context, status=400) # Bad Request status
        else:
            # If form validation fails
            messages.error(request, 'Invalid input for detail item.')
            # Re-render the row with the form and errors
            context = {
                'detail': detail,
                'remaining_qty': detail.get_remaining_qty(),
                'all_units': Unit.objects.all().order_by('id'),
                'form': form # Pass form with errors
            }
            return render(request, 'invoicing/servicetaxinvoice/_goods_table_row.html', context, status=400) # Bad Request status


class CustomerAutocompleteView(View):
    """
    Provides autocomplete suggestions for customer names, simulating ASP.NET WebMethod `sql`.
    """
    def post(self, request, *args, **kwargs):
        prefix_text = request.POST.get('hx-current-value', '').strip() # HTMX sends data this way
        company_id, _, _ = get_context_data_from_session(request)

        if not prefix_text:
            return HttpResponse("") # Return empty if no input

        customers = Customer.objects.filter(
            company_id=company_id,
            customer_name__icontains=prefix_text # Case-insensitive contains
        ).values_list('customer_name', 'customer_id')[:10] # Limit results

        suggestions = [f"{name} [{cid}]" for name, cid in customers]
        
        # Render a simple list of suggestions for HTMX
        return render(request, 'invoicing/servicetaxinvoice/_autocomplete_suggestions.html', {'suggestions': suggestions})

class CustomerSearchPopulateView(View):
    """
    Handles search button clicks to populate Buyer or Consignee fields.
    Simulates Button2_Click, Button3_Click.
    """
    def post(self, request, pk, customer_type, *args, **kwargs):
        invoice = get_object_or_404(ServiceTaxInvoice, pk=pk)
        
        # Assuming the search value comes from the hidden input field associated with autocomplete
        # The ASP.NET fun.getCode(TxtBYName.Text) means extracting code from "Name [Code]"
        search_value = request.POST.get(f'{customer_type}_name_autocomplete', '').strip()
        customer_code_match = re.search(r'\[(.*?)\]', search_value)
        customer_code = customer_code_match.group(1) if customer_code_match else None

        if not customer_code:
            messages.error(request, "Invalid customer search value. Please select from suggestions or enter a valid customer code.")
            # Re-render the specific tab to show errors, or trigger a full page refresh
            return self.render_updated_tab(request, invoice, customer_type, status=400)

        success = False
        message = None
        if customer_type == 'buyer':
            success, message = invoice.populate_buyer_from_customer(customer_code)
        elif customer_type == 'consignee':
            success, message = invoice.populate_consignee_from_customer(customer_code)
        
        if success:
            invoice.save() # Save changes made by populate method
            messages.success(request, f"{customer_type.capitalize()} details populated successfully.")
        else:
            messages.error(request, message)
        
        return self.render_updated_tab(request, invoice, customer_type)

    def render_updated_tab(self, request, invoice, customer_type, status=200):
        # Re-initialize form to reflect updated instance data and re-render the relevant tab
        form = ServiceTaxInvoiceForm(instance=invoice)
        context = {
            'form': form,
            'invoice': invoice
        }
        # Render only the specific tab's content
        if customer_type == 'buyer':
            return render(request, 'invoicing/servicetaxinvoice/_buyer_tab_partial.html', context, status=status)
        elif customer_type == 'consignee':
            return render(request, 'invoicing/servicetaxinvoice/_consignee_tab_partial.html', context, status=status)
        # Fallback for unknown customer_type or if full page update is preferred
        return render(request, 'invoicing/servicetaxinvoice/edit_details.html', context, status=status)


class CopyBuyerToConsigneeView(View):
    """
    Handles copying buyer details to consignee, simulating Button5_Click.
    """
    def post(self, request, pk, *args, **kwargs):
        invoice = get_object_or_404(ServiceTaxInvoice, pk=pk)
        invoice.copy_consignee_from_buyer()
        invoice.save() # Persist the changes
        messages.success(request, 'Buyer details copied to Consignee successfully.')
        
        # Re-render the Consignee tab content to reflect changes
        form = ServiceTaxInvoiceForm(instance=invoice)
        context = {
            'form': form,
            'invoice': invoice
        }
        return render(request, 'invoicing/servicetaxinvoice/_consignee_tab_partial.html', context)


class GetDropdownOptionsView(View):
    """
    Handles HTMX requests for cascading dropdowns (Country -> State -> City).
    """
    def get(self, request, level, *args, **kwargs):
        parent_id = request.GET.get('parent_id')
        options = []
        
        if level == 'states':
            if parent_id:
                options = State.objects.filter(country_id=parent_id).order_by('name')
            html_output = '<select name="buyer_state" id="id_buyer_state" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" hx-get="/invoicing/get-cities/" hx-target="#id_buyer_city" hx-trigger="change" hx-swap="outerHTML">'
            html_output += '<option value="">-- Select State --</option>'
            for option in options:
                html_output += f'<option value="{option.id}">{option.name}</option>'
            html_output += '</select>'
            return HttpResponse(html_output)

        elif level == 'cities':
            if parent_id:
                options = City.objects.filter(state_id=parent_id).order_by('name')
            html_output = '<select name="buyer_city" id="id_buyer_city" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">'
            html_output += '<option value="">-- Select City --</option>'
            for option in options:
                html_output += f'<option value="{option.id}">{option.name}</option>'
            html_output += '</select>'
            return HttpResponse(html_output)
            
        return HttpResponse("", status=400) # Bad request if level is invalid

```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

List Template ([MODEL_NAME]/list.html):
- Extend core/base.html (DO NOT include base.html code).
- Use DataTables for a table displaying fields plus actions column.
- Implement HTMX for dynamic updates and form loading.
- Add buttons with HTMX attributes to trigger modals.

Form Template ([MODEL_NAME]/form.html):
- Should be a partial template for HTMX loading.
- Render the form with submit button and HTMX attributes.

Delete Template ([MODEL_NAME]/confirm_delete.html):
- Should be a partial template for HTMX loading.
- Include confirmation message and buttons with HTMX attributes.

**Analysis:**

The ASP.NET page is an "Edit" page for a single invoice, not a list. So, we'll create `edit_details.html` for the main page, and several partials (`_buyer_tab_partial.html`, `_consignee_tab_partial.html`, `_goods_table_partial.html`, `_taxation_tab_partial.html`, `_goods_table_row.html`, `_autocomplete_suggestions.html`) to facilitate HTMX-driven updates of specific sections.

**`invoicing/templates/invoicing/servicetaxinvoice/edit_details.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">Service Tax Invoice - Edit</h2>

    <!-- Invoice Header Details -->
    <div class="bg-gray-100 p-4 rounded-lg shadow-sm mb-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-y-2">
            <div>
                <span class="font-semibold">Ser. Tax Invoice No.:</span> 
                <span class="font-bold text-blue-700">{{ invoice.invoice_no }}</span>
            </div>
            <div>
                <span class="font-semibold">Date:</span> 
                <span class="font-bold text-blue-700">{{ invoice.formatted_sys_date }}</span>
            </div>
            <div>
                <span class="font-semibold">WO No.:</span> 
                <span class="font-bold text-blue-700">{{ invoice.wo_numbers_list|join:", " }}</span>
            </div>
            <div>
                <span class="font-semibold">PO No.:</span> 
                <span class="font-bold text-blue-700">{{ invoice.po_no }}</span>
            </div>
            <div>
                <span class="font-semibold">Date:</span> 
                <span class="font-bold text-blue-700">{{ invoice.get_po_date_formatted }}</span>
            </div>
        </div>
    </div>

    <!-- Main Form and Tab Container -->
    <form hx-post="{% url 'servicetaxinvoice_edit' invoice.pk %}" hx-swap="none" id="invoiceEditForm"
          _="on htmx:afterRequest if event.detail.xhr.status == 204 or event.detail.xhr.status == 302 then
                call window.location.reload()
             else if event.detail.xhr.status >= 400 then
                alert('Update failed. Please check the form for errors.')">
        {% csrf_token %}
        <!-- Hidden fields for initial context -->
        {{ form.invoice_id }}
        {{ form.company_id }}
        {{ form.financial_year_id }}
        {{ form.session_id }}

        <div x-data="{ activeTab: {{ active_tab_index }} }" class="bg-white p-6 rounded-lg shadow-lg">
            <!-- Tabs Navigation -->
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                    <button type="button" @click="activeTab = 0; $dispatch('set-tab-index', { index: 0 })"
                            :class="{'border-indigo-500 text-indigo-600': activeTab === 0, 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 0}"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none">
                        Buyer
                    </button>
                    <button type="button" @click="activeTab = 1; $dispatch('set-tab-index', { index: 1 })"
                            :class="{'border-indigo-500 text-indigo-600': activeTab === 1, 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 1}"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none">
                        Consignee
                    </button>
                    <button type="button" @click="activeTab = 2; $dispatch('set-tab-index', { index: 2 })"
                            :class="{'border-indigo-500 text-indigo-600': activeTab === 2, 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 2}"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none">
                        Goods
                    </button>
                    <button type="button" @click="activeTab = 3; $dispatch('set-tab-index', { index: 3 })"
                            :class="{'border-indigo-500 text-indigo-600': activeTab === 3, 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 3}"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none">
                        Taxation
                    </button>
                </nav>
            </div>

            <!-- Tab Panels -->
            <div class="mt-6">
                <!-- Tab 0: Buyer -->
                <div x-show="activeTab === 0" id="buyer-tab-content"
                     hx-get="{% url 'servicetaxinvoice_buyer_tab' invoice.pk %}"
                     hx-trigger="load, set-tab-index from body[detail.index='0']"
                     hx-target="#buyer-tab-content"
                     hx-swap="innerHTML">
                     <!-- Content will be loaded here via HTMX -->
                     <div class="text-center py-10"><div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div><p class="mt-2">Loading Buyer details...</p></div>
                </div>

                <!-- Tab 1: Consignee -->
                <div x-show="activeTab === 1" id="consignee-tab-content"
                     hx-get="{% url 'servicetaxinvoice_consignee_tab' invoice.pk %}"
                     hx-trigger="load, set-tab-index from body[detail.index='1']"
                     hx-target="#consignee-tab-content"
                     hx-swap="innerHTML">
                     <!-- Content will be loaded here via HTMX -->
                     <div class="text-center py-10"><div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div><p class="mt-2">Loading Consignee details...</p></div>
                </div>

                <!-- Tab 2: Goods -->
                <div x-show="activeTab === 2" id="goods-tab-content"
                     hx-get="{% url 'servicetaxinvoice_goods_tab' invoice.pk %}"
                     hx-trigger="load, set-tab-index from body[detail.index='2'], refreshGoodsTable from body"
                     hx-target="#goods-tab-content"
                     hx-swap="innerHTML">
                     <!-- Content will be loaded here via HTMX -->
                     <div class="text-center py-10"><div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div><p class="mt-2">Loading Goods details...</p></div>
                </div>

                <!-- Tab 3: Taxation -->
                <div x-show="activeTab === 3" id="taxation-tab-content"
                     hx-get="{% url 'servicetaxinvoice_taxation_tab' invoice.pk %}"
                     hx-trigger="load, set-tab-index from body[detail.index='3']"
                     hx-target="#taxation-tab-content"
                     hx-swap="innerHTML">
                     <!-- Content will be loaded here via HTMX -->
                     <div class="text-center py-10"><div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div><p class="mt-2">Loading Taxation details...</p></div>
                </div>
            </div>
        </div>

        <div class="mt-8 flex justify-end space-x-4">
            <button type="button" onclick="window.location.href='{% url 'servicetaxinvoice_list' %}'"
                    class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                Cancel
            </button>
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Update
            </button>
        </div>
    </form>
</div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('editInvoice', () => ({
            activeTab: {{ active_tab_index }},
            init() {
                // Listen for custom event to set active tab
                this.$root.addEventListener('set-tab-index', (event) => {
                    this.activeTab = event.detail.index;
                });
                // Persist active tab in session storage or cookie
                this.$watch('activeTab', (value) => {
                    sessionStorage.setItem('servicetax_active_tab', value);
                });
                // Restore active tab on load
                const storedTab = sessionStorage.getItem('servicetax_active_tab');
                if (storedTab !== null) {
                    this.activeTab = parseInt(storedTab);
                }
            }
        }));
    });
</script>
{% endblock %}
```

**`invoicing/templates/invoicing/servicetaxinvoice/_buyer_tab_partial.html`**

```html
<div class="space-y-4">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div>
            <label for="{{ form.buyer_name_autocomplete.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.buyer_name_autocomplete.label }}
            </label>
            <div class="relative">
                {{ form.buyer_name_autocomplete }}
                <div id="buyer-name-suggestions" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-48 overflow-y-auto"></div>
                <span class="htmx-indicator ml-2">Loading...</span>
            </div>
            {% if form.buyer_name_autocomplete.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_name_autocomplete.errors }}</p>{% endif %}
            <button type="button" 
                    hx-post="{% url 'servicetaxinvoice_customer_search' invoice.pk 'buyer' %}" 
                    hx-include="#{{ form.buyer_name_autocomplete.id_for_label }}"
                    hx-target="#buyer-tab-content" hx-swap="outerHTML" 
                    class="mt-2 bg-indigo-500 hover:bg-indigo-600 text-white font-bold py-1 px-3 rounded text-sm">
                Search
            </button>
        </div>
        <div class="col-span-full">
            <label for="{{ form.buyer_address.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.buyer_address.label }}
            </label>
            {{ form.buyer_address }}
            {% if form.buyer_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_address.errors }}</p>{% endif %}
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
            <label for="{{ form.buyer_country.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.buyer_country.label }}
            </label>
            {{ form.buyer_country }}
            {% if form.buyer_country.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_country.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.buyer_state.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.buyer_state.label }}
            </label>
            {{ form.buyer_state }}
            {% if form.buyer_state.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_state.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.buyer_city.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.buyer_city.label }}
            </label>
            {{ form.buyer_city }}
            {% if form.buyer_city.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_city.errors }}</p>{% endif %}
        </div>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
            <label for="{{ form.buyer_contact_person.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.buyer_contact_person.label }}
            </label>
            {{ form.buyer_contact_person }}
            {% if form.buyer_contact_person.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_contact_person.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.buyer_phone.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.buyer_phone.label }}
            </label>
            {{ form.buyer_phone }}
            {% if form.buyer_phone.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_phone.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.buyer_mobile.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.buyer_mobile.label }}
            </label>
            {{ form.buyer_mobile }}
            {% if form.buyer_mobile.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_mobile.errors }}</p>{% endif %}
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
            <label for="{{ form.buyer_email.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.buyer_email.label }}
            </label>
            {{ form.buyer_email }}
            {% if form.buyer_email.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_email.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.buyer_fax.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.buyer_fax.label }}
            </label>
            {{ form.buyer_fax }}
            {% if form.buyer_fax.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_fax.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.buyer_tin_vat.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.buyer_tin_vat.label }}
            </label>
            {{ form.buyer_tin_vat }}
            {% if form.buyer_tin_vat.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_tin_vat.errors }}</p>{% endif %}
        </div>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
            <label for="{{ form.buyer_ecc.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.buyer_ecc.label }}
            </label>
            {{ form.buyer_ecc }}
            {% if form.buyer_ecc.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_ecc.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.buyer_tin_cst.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.buyer_tin_cst.label }}
            </label>
            {{ form.buyer_tin_cst }}
            {% if form.buyer_tin_cst.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_tin_cst.errors }}</p>{% endif %}
        </div>
    </div>
</div>
```

**`invoicing/templates/invoicing/servicetaxinvoice/_consignee_tab_partial.html`**

```html
<div class="space-y-4">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div>
            <label for="{{ form.consignee_name_autocomplete.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.consignee_name_autocomplete.label }}
            </label>
            <div class="relative">
                {{ form.consignee_name_autocomplete }}
                <div id="consignee-name-suggestions" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-48 overflow-y-auto"></div>
                <span class="htmx-indicator ml-2">Loading...</span>
            </div>
            {% if form.consignee_name_autocomplete.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_name_autocomplete.errors }}</p>{% endif %}
            <button type="button" 
                    hx-post="{% url 'servicetaxinvoice_customer_search' invoice.pk 'consignee' %}" 
                    hx-include="#{{ form.consignee_name_autocomplete.id_for_label }}"
                    hx-target="#consignee-tab-content" hx-swap="outerHTML" 
                    class="mt-2 bg-indigo-500 hover:bg-indigo-600 text-white font-bold py-1 px-3 rounded text-sm">
                Search
            </button>
            <button type="button" 
                    hx-post="{% url 'servicetaxinvoice_copy_buyer' invoice.pk %}" 
                    hx-target="#consignee-tab-content" hx-swap="outerHTML"
                    class="mt-2 ml-2 bg-green-500 hover:bg-green-600 text-white font-bold py-1 px-3 rounded text-sm">
                Copy from Buyer
            </button>
        </div>
        <div class="col-span-full">
            <label for="{{ form.consignee_address.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.consignee_address.label }}
            </label>
            {{ form.consignee_address }}
            {% if form.consignee_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_address.errors }}</p>{% endif %}
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
            <label for="{{ form.consignee_country.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.consignee_country.label }}
            </label>
            {{ form.consignee_country }}
            {% if form.consignee_country.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_country.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.consignee_state.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.consignee_state.label }}
            </label>
            {{ form.consignee_state }}
            {% if form.consignee_state.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_state.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.consignee_city.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.consignee_city.label }}
            </label>
            {{ form.consignee_city }}
            {% if form.consignee_city.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_city.errors }}</p>{% endif %}
        </div>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
            <label for="{{ form.consignee_contact_person.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.consignee_contact_person.label }}
            </label>
            {{ form.consignee_contact_person }}
            {% if form.consignee_contact_person.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_contact_person.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.consignee_phone.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.consignee_phone.label }}
            </label>
            {{ form.consignee_phone }}
            {% if form.consignee_phone.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_phone.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.consignee_mobile.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.consignee_mobile.label }}
            </label>
            {{ form.consignee_mobile }}
            {% if form.consignee_mobile.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_mobile.errors }}</p>{% endif %}
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
            <label for="{{ form.consignee_email.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.consignee_email.label }}
            </label>
            {{ form.consignee_email }}
            {% if form.consignee_email.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_email.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.consignee_fax.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.consignee_fax.label }}
            </label>
            {{ form.consignee_fax }}
            {% if form.consignee_fax.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_fax.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.consignee_tin_vat.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.consignee_tin_vat.label }}
            </label>
            {{ form.consignee_tin_vat }}
            {% if form.consignee_tin_vat.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_tin_vat.errors }}</p>{% endif %}
        </div>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
            <label for="{{ form.consignee_ecc.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.consignee_ecc.label }}
            </label>
            {{ form.consignee_ecc }}
            {% if form.consignee_ecc.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_ecc.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.consignee_tin_cst.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.consignee_tin_cst.label }}
            </label>
            {{ form.consignee_tin_cst }}
            {% if form.consignee_tin_cst.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_tin_cst.errors }}</p>{% endif %}
        </div>
    </div>
</div>
```

**`invoicing/templates/invoicing/servicetaxinvoice/_goods_table_partial.html`**

```html
<div class="overflow-x-auto relative shadow-md sm:rounded-lg">
    <table id="goodsTable" class="w-full text-sm text-left text-gray-500">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-4">SN</th>
                <th scope="col" class="py-3 px-4">Item Desc</th>
                <th scope="col" class="py-3 px-4">Unit</th>
                <th scope="col" class="py-3 px-4 text-right">Qty</th>
                <th scope="col" class="py-3 px-4 text-right">Rem Qty</th>
                <th scope="col" class="py-3 px-4 text-center">Edit</th>
                <th scope="col" class="py-3 px-4">Req Qty</th>
                <th scope="col" class="py-3 px-4">Unit Of Qty</th>
                <th scope="col" class="py-3 px-4 text-right">Rate</th>
                <th scope="col" class="py-3 px-4">Amt in (%)</th>
            </tr>
        </thead>
        <tbody>
            {% for data in invoice_details_data %}
                {% include 'invoicing/servicetaxinvoice/_goods_table_row.html' with detail=data.detail remaining_qty=data.remaining_qty all_units=all_units form=data.form %}
            {% empty %}
            <tr>
                <td colspan="10" class="py-4 px-4 text-center text-lg font-medium text-maroon-600">
                    No data to display !
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables if there are items, otherwise it's just the 'No data' message
    $(document).ready(function() {
        if ($('#goodsTable tbody tr').length > 0 && !$('#goodsTable tbody tr').first().hasClass('no-data-row')) {
            $('#goodsTable').DataTable({
                "paging": true,
                "searching": true,
                "info": true,
                "ordering": true,
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "autoWidth": false,
                "columnDefs": [
                    { "orderable": false, "targets": [5] } // Disable sorting for Edit checkbox column
                ]
            });
        }
    });
</script>
```

**`invoicing/templates/invoicing/servicetaxinvoice/_goods_table_row.html`**

```html
<tr id="detail-row-{{ detail.pk }}"
    hx-target="this" hx-swap="outerHTML">
    <td class="py-2 px-4 border-b border-gray-200 text-right w-10">{{ forloop.counter }}</td>
    <td class="py-2 px-4 border-b border-gray-200 text-left w-64">{{ detail.item_description }}</td>
    <td class="py-2 px-4 border-b border-gray-200 text-center w-20">{{ detail.unit_symbol }}</td>
    <td class="py-2 px-4 border-b border-gray-200 text-right w-20">{{ detail.qty|floatformat:3 }}</td>
    <td class="py-2 px-4 border-b border-gray-200 text-right w-20">{{ remaining_qty|floatformat:3 }}</td>
    <td class="py-2 px-4 border-b border-gray-200 text-center w-12">
        <input type="checkbox" id="ck-{{ detail.pk }}" 
               x-data="{ editMode: false }"
               @change="editMode = $el.checked; console.log('Edit mode for {{ detail.pk }}: ' + editMode);"
               class="form-checkbox h-4 w-4 text-indigo-600 transition duration-150 ease-in-out">
    </td>
    <td class="py-2 px-4 border-b border-gray-200 w-32">
        <span x-show="!document.getElementById('ck-{{ detail.pk }}').checked">{{ detail.req_qty|floatformat:3 }}</span>
        <div x-show="document.getElementById('ck-{{ detail.pk }}').checked" style="display: none;">
            {{ form.req_qty }}
            {% if form.req_qty.errors %}<span class="text-red-500 text-xs">{{ form.req_qty.errors }}</span>{% endif %}
            <input type="hidden" name="detail_id" value="{{ detail.pk }}">
        </div>
    </td>
    <td class="py-2 px-4 border-b border-gray-200 w-32">
        <span x-show="!document.getElementById('ck-{{ detail.pk }}').checked">{{ detail.original_unit_symbol }}</span>
        <div x-show="document.getElementById('ck-{{ detail.pk }}').checked" style="display: none;">
            {{ form.unit }}
            {% if form.unit.errors %}<span class="text-red-500 text-xs">{{ form.unit.errors }}</span>{% endif %}
        </div>
    </td>
    <td class="py-2 px-4 border-b border-gray-200 text-right w-20">{{ detail.po_item_rate|floatformat:2 }}</td>
    <td class="py-2 px-4 border-b border-gray-200 w-32">
        <span x-show="!document.getElementById('ck-{{ detail.pk }}').checked">{{ detail.amt_in_per|floatformat:2 }}</span>
        <div x-show="document.getElementById('ck-{{ detail.pk }}').checked" style="display: none;">
            {{ form.amt_in_per }}
            {% if form.amt_in_per.errors %}<span class="text-red-500 text-xs">{{ form.amt_in_per.errors }}</span>{% endif %}
        </div>
        <button type="button" 
                hx-post="{% url 'servicetaxinvoice_detail_update' invoice.pk detail.pk %}"
                hx-include="#detail-row-{{ detail.pk }} input[type=hidden], #detail-row-{{ detail.pk }} input[type=number], #detail-row-{{ detail.pk }} select"
                hx-indicator="#htmx-indicator-{{ detail.pk }}"
                hx-confirm="Are you sure you want to update this item?"
                class="ml-2 bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded text-xs"
                x-show="document.getElementById('ck-{{ detail.pk }}').checked" style="display: none;">
            Update
        </button>
        <span id="htmx-indicator-{{ detail.pk }}" class="htmx-indicator ml-2">Updating...</span>
    </td>
</tr>
```

**`invoicing/templates/invoicing/servicetaxinvoice/_taxation_tab_partial.html`**

```html
<div class="space-y-4">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
            <label for="{{ form.add_amount.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.add_amount.label }}
            </label>
            <div class="flex items-center space-x-2">
                {{ form.add_amount }}
                {{ form.add_type }}
            </div>
            {% if form.add_amount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.add_amount.errors }}</p>{% endif %}
            {% if form.add_type.errors %}<p class="text-red-500 text-xs mt-1">{{ form.add_type.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.deduction_amount.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.deduction_amount.label }}
            </label>
            <div class="flex items-center space-x-2">
                {{ form.deduction_amount }}
                {{ form.deduction_type }}
            </div>
            {% if form.deduction_amount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.deduction_amount.errors }}</p>{% endif %}
            {% if form.deduction_type.errors %}<p class="text-red-500 text-xs mt-1">{{ form.deduction_type.errors }}</p>{% endif %}
        </div>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
            <label for="{{ form.service_tax.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.service_tax.label }}
            </label>
            {{ form.service_tax }}
            {% if form.service_tax.errors %}<p class="text-red-500 text-xs mt-1">{{ form.service_tax.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.duty_rate.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.duty_rate.label }}
            </label>
            {{ form.duty_rate }}
            {% if form.duty_rate.errors %}<p class="text-red-500 text-xs mt-1">{{ form.duty_rate.errors }}</p>{% endif %}
        </div>
    </div>
</div>
```

**`invoicing/templates/invoicing/servicetaxinvoice/_autocomplete_suggestions.html`**

```html
<div id="{{ suggestions_id }}" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-48 overflow-y-auto">
    {% for suggestion in suggestions %}
        <div class="p-2 hover:bg-gray-100 cursor-pointer" 
             hx-on:click="this.closest('.relative').querySelector('input').value = '{{ suggestion }}'; this.innerHTML = '';">
            {{ suggestion }}
        </div>
    {% empty %}
        <div class="p-2 text-gray-500">No results found.</div>
    {% endfor %}
</div>
```

### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

Create paths for list, create, update, delete, and any partial views used with HTMX.
Use appropriate naming patterns and consistent URL structure.

**`invoicing/urls.py`**
```python
from django.urls import path
from .views import (
    ServiceTaxInvoiceEditView, ServiceTaxInvoiceTablePartialView,
    ServiceTaxInvoiceDetailUpdateView, CustomerAutocompleteView,
    CustomerSearchPopulateView, CopyBuyerToConsigneeView,
    GetDropdownOptionsView
)

urlpatterns = [
    # Main edit page for a specific invoice
    path('servicetaxinvoice/edit/<int:pk>/', ServiceTaxInvoiceEditView.as_view(), name='servicetaxinvoice_edit'),

    # HTMX endpoints for tab content
    path('servicetaxinvoice/edit/<int:pk>/buyer-tab/', ServiceTaxInvoiceEditView.as_view(template_name='invoicing/servicetaxinvoice/_buyer_tab_partial.html'), name='servicetaxinvoice_buyer_tab'),
    path('servicetaxinvoice/edit/<int:pk>/consignee-tab/', ServiceTaxInvoiceEditView.as_view(template_name='invoicing/servicetaxinvoice/_consignee_tab_partial.html'), name='servicetaxinvoice_consignee_tab'),
    path('servicetaxinvoice/edit/<int:pk>/goods-tab/', ServiceTaxInvoiceTablePartialView.as_view(), name='servicetaxinvoice_goods_tab'), # Uses a dedicated view for the table
    path('servicetaxinvoice/edit/<int:pk>/taxation-tab/', ServiceTaxInvoiceEditView.as_view(template_name='invoicing/servicetaxinvoice/_taxation_tab_partial.html'), name='servicetaxinvoice_taxation_tab'),

    # HTMX endpoint for updating a single invoice detail line item
    path('servicetaxinvoice/<int:invoice_pk>/details/<int:detail_pk>/update/', ServiceTaxInvoiceDetailUpdateView.as_view(), name='servicetaxinvoice_detail_update'),

    # HTMX endpoint for customer autocomplete
    path('invoicing/customer-autocomplete/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),

    # HTMX endpoints for customer search and populate
    path('servicetaxinvoice/<int:pk>/customer-search/<str:customer_type>/', CustomerSearchPopulateView.as_view(), name='servicetaxinvoice_customer_search'),
    path('servicetaxinvoice/<int:pk>/copy-buyer/', CopyBuyerToConsigneeView.as_view(), name='servicetaxinvoice_copy_buyer'),

    # HTMX endpoints for cascading dropdowns (states and cities)
    path('invoicing/get-states/', GetDropdownOptionsView.as_view(), {'level': 'states'}, name='get_states'),
    path('invoicing/get-cities/', GetDropdownOptionsView.as_view(), {'level': 'cities'}, name='get_cities'),
    
    # Placeholder for a list view if you redirect to one (e.g. from cancel button)
    path('servicetaxinvoice/list/', ServiceTaxInvoiceEditView.as_view(template_name='core/list_placeholder.html'), name='servicetaxinvoice_list'),
]

# Note: In a real application, you might have a dedicated ServiceTaxInvoiceListView for the main list page.
# For this conversion, 'servicetaxinvoice_list' is a placeholder URL for redirection.
```

### 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

Include comprehensive unit tests for model methods and properties.
Add integration tests for all views (list, create, update, delete).
Ensure at least 80% test coverage of code.

**`invoicing/tests.py`**
```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
import datetime
from .models import (
    ServiceTaxInvoice, ServiceTaxInvoiceDetail, Company, FinancialYear,
    ServiceCategory, TaxableService, Unit, ExciseMaster, Country, State, City,
    Customer, PurchaseOrder, PurchaseOrderDetail, WorkOrder
)

# --- Setup for Test Data ---
class CommonTestDataMixin:
    @classmethod
    def setUpTestData(cls):
        # Create base lookup data
        cls.company = Company.objects.create(id=1, name='Test Co.')
        cls.financial_year = FinancialYear.objects.create(id=1, year_name='2023-2024')
        cls.service_category = ServiceCategory.objects.create(id=1, description='Consulting')
        cls.taxable_service = TaxableService.objects.create(id=1, description='Software Dev')
        cls.unit_ea = Unit.objects.create(id=1, symbol='EA')
        cls.unit_hr = Unit.objects.create(id=2, symbol='HR')
        cls.excise_master = ExciseMaster.objects.create(id=1, terms='GST @ 18%')

        cls.country_in = Country.objects.create(id=101, name='India')
        cls.state_mh = State.objects.create(id=201, country=cls.country_in, name='Maharashtra')
        cls.city_mumbai = City.objects.create(id=301, state=cls.state_mh, name='Mumbai')

        cls.customer_a = Customer.objects.create(
            customer_id='CUST001', customer_name='Customer A', company=cls.company,
            address='123 Main St', country=cls.country_in, state=cls.state_mh, city=cls.city_mumbai,
            contact_person='John Doe', phone_no='*********', mobile_no='9876543210',
            fax_no='*********', email='<EMAIL>', tin_vat_no='VAT123', tin_cst_no='CST456', ecc_no='ECC789'
        )
        cls.customer_b = Customer.objects.create(
            customer_id='CUST002', customer_name='Customer B', company=cls.company,
            address='456 Oak Ave', country=cls.country_in, state=cls.state_mh, city=cls.city_mumbai,
            contact_person='Jane Smith', phone_no='*********', mobile_no='**********',
            fax_no='*********', email='<EMAIL>', tin_vat_no='VAT456', tin_cst_no='CST789', ecc_no='ECC012'
        )

        cls.po_master = PurchaseOrder.objects.create(
            id=1, po_no='PO001', po_date='2023-01-01', company=cls.company
        )
        cls.po_detail_item1 = PurchaseOrderDetail.objects.create(
            id=101, po=cls.po_master, item_description='Software License', total_qty=100.0, unit=cls.unit_ea, rate=50.0
        )
        cls.po_detail_item2 = PurchaseOrderDetail.objects.create(
            id=102, po=cls.po_master, item_description='Consulting Hours', total_qty=50.0, unit=cls.unit_hr, rate=100.0
        )
        cls.wo_master = WorkOrder.objects.create(
            id=1, wo_no='WO001', company=cls.company
        )

        # Create a sample invoice for editing
        cls.invoice = ServiceTaxInvoice.objects.create(
            id=1,
            sys_date='2023-01-15', sys_time='10:00:00', company=cls.company,
            financial_year=cls.financial_year, session_id='testuser',
            invoice_no='INV001', po=cls.po_master, po_no='PO001',
            wo_numbers_raw=str(cls.wo_master.id),
            date_of_issue_invoice='2023-01-15', time_of_issue_invoice='10:00:00 AM',
            duty_rate=10.5, customer_code='CUST001', customer_category=cls.service_category,
            taxable_services=cls.taxable_service,
            buyer_name='Customer A [CUST001]', buyer_address='123 Main St',
            buyer_city=cls.city_mumbai, buyer_state=cls.state_mh, buyer_country=cls.country_in,
            buyer_contact_person='John Doe', buyer_phone='*********', buyer_mobile='9876543210',
            buyer_fax='*********', buyer_email='<EMAIL>', buyer_ecc='ECC789',
            buyer_tin_cst='CST456', buyer_tin_vat='VAT123',
            consignee_name='Customer A [CUST001]', consignee_address='123 Main St',
            consignee_city=cls.city_mumbai, consignee_state=cls.state_mh, consignee_country=cls.country_in,
            consignee_contact_person='John Doe', consignee_phone='*********', consignee_mobile='9876543210',
            consignee_fax='*********', consignee_email='<EMAIL>', consignee_ecc='ECC789',
            consignee_tin_cst='CST456', consignee_tin_vat='VAT123',
            add_type='0', add_amount=50.0, deduction_type='0', deduction_amount=10.0,
            service_tax=cls.excise_master
        )

        ServiceTaxInvoiceDetail.objects.create(
            id=1, invoice=cls.invoice, invoice_no='INV001', item_id=cls.po_detail_item1,
            unit=cls.unit_ea, qty=100.0, req_qty=50.0, amt_in_per=5.0, rate=50.0
        )
        ServiceTaxInvoiceDetail.objects.create(
            id=2, invoice=cls.invoice, invoice_no='INV001', item_id=cls.po_detail_item2,
            unit=cls.unit_hr, qty=50.0, req_qty=20.0, amt_in_per=10.0, rate=100.0
        )

        cls.invoice_detail_1 = ServiceTaxInvoiceDetail.objects.get(id=1)
        cls.invoice_detail_2 = ServiceTaxInvoiceDetail.objects.get(id=2)

class ServiceTaxInvoiceModelTest(CommonTestDataMixin, TestCase):
    def test_invoice_creation(self):
        invoice = ServiceTaxInvoice.objects.get(id=1)
        self.assertEqual(invoice.invoice_no, 'INV001')
        self.assertEqual(invoice.company.name, 'Test Co.')
        self.assertEqual(invoice.buyer_name, 'Customer A [CUST001]')
        self.assertEqual(invoice.details.count(), 2)

    def test_formatted_sys_date_property(self):
        invoice = ServiceTaxInvoice.objects.get(id=1)
        self.assertEqual(invoice.formatted_sys_date, '15-01-2023')

    def test_wo_numbers_list_property(self):
        invoice = ServiceTaxInvoice.objects.get(id=1)
        self.assertEqual(invoice.wo_numbers_list, ['WO001'])

    def test_get_po_date_formatted_method(self):
        invoice = ServiceTaxInvoice.objects.get(id=1)
        self.assertEqual(invoice.get_po_date_formatted(), '01-01-2023')
    
    def test_update_master_data_method(self):
        invoice = ServiceTaxInvoice.objects.get(id=1)
        original_buyer_name = invoice.buyer_name
        
        new_data = {
            'duty_rate': 12.0,
            'buyer_name_autocomplete': 'New Buyer Name [NEW001]', # Simulating form input
            'buyer_email': '<EMAIL>',
            'session_id': 'updated_user'
        }
        invoice.update_master_data(new_data, new_data['session_id'])
        invoice.refresh_from_db()
        
        self.assertEqual(invoice.duty_rate, 12.0)
        self.assertEqual(invoice.buyer_name, 'New Buyer Name [NEW001]')
        self.assertEqual(invoice.buyer_email, '<EMAIL>')
        self.assertEqual(invoice.session_id, 'updated_user')
        self.assertEqual(invoice.sys_date, timezone.now().date()) # Check if sys_date is updated

    def test_populate_buyer_from_customer(self):
        invoice = ServiceTaxInvoice.objects.get(id=1)
        success, message = invoice.populate_buyer_from_customer('CUST002')
        self.assertTrue(success)
        self.assertEqual(invoice.buyer_name, 'Customer A [CUST001]') # Name from autocomplete doesn't save to DB directly
        self.assertEqual(invoice.buyer_address, '456 Oak Ave')
        self.assertEqual(invoice.buyer_email, '<EMAIL>')

        success, message = invoice.populate_buyer_from_customer('NONEXISTENT')
        self.assertFalse(success)
        self.assertIn("Invalid selection of Customer data.", message)

    def test_copy_consignee_from_buyer(self):
        invoice = ServiceTaxInvoice.objects.get(id=1)
        invoice.buyer_name = 'Custom Buyer'
        invoice.buyer_address = 'Custom Address'
        invoice.copy_consignee_from_buyer()
        invoice.refresh_from_db()
        self.assertEqual(invoice.consignee_name, 'Custom Buyer')
        self.assertEqual(invoice.consignee_address, 'Custom Address')
    
    def test_detail_properties(self):
        detail = ServiceTaxInvoiceDetail.objects.get(id=1)
        self.assertEqual(detail.item_description, 'Software License')
        self.assertEqual(detail.unit_symbol, 'EA')
        self.assertEqual(detail.po_item_rate, 50.0)
        self.assertEqual(detail.original_unit_symbol, 'EA')

    def test_get_remaining_qty(self):
        detail1 = ServiceTaxInvoiceDetail.objects.get(id=1) # Req_qty: 50.0, PO Total: 100.0
        detail2 = ServiceTaxInvoiceDetail.objects.get(id=2) # Req_qty: 20.0, PO Total: 50.0

        # For detail1: Original PO qty (100) - (sum of other details for this item (0))
        self.assertEqual(detail1.get_remaining_qty(), 100.0)

        # Create another detail for the same item_id (as if it was in another invoice)
        invoice2 = ServiceTaxInvoice.objects.create(
            id=2, sys_date='2023-01-16', sys_time='11:00:00', company=self.company,
            financial_year=self.financial_year, session_id='testuser2',
            invoice_no='INV002', po=self.po_master, po_no='PO001', wo_numbers_raw=str(self.wo_master.id),
            date_of_issue_invoice='2023-01-16', time_of_issue_invoice='11:00:00 AM',
            duty_rate=10.0, customer_code='CUST001', customer_category=self.service_category,
            taxable_services=self.taxable_service,
            buyer_name='Customer A [CUST001]', buyer_address='123 Main St',
            buyer_city=self.city_mumbai, buyer_state=self.state_mh, buyer_country=self.country_in,
            buyer_contact_person='John Doe', buyer_phone='111', buyer_mobile='222',
            buyer_fax='333', buyer_email='<EMAIL>', buyer_ecc='1', buyer_tin_cst='1', buyer_tin_vat='1',
            consignee_name='Customer A [CUST001]', consignee_address='123 Main St',
            consignee_city=self.city_mumbai, consignee_state=self.state_mh, consignee_country=self.country_in,
            consignee_contact_person='John Doe', consignee_phone='111', consignee_mobile='222',
            consignee_fax='333', consignee_email='<EMAIL>', consignee_ecc='1', consignee_tin_cst='1', consignee_tin_vat='1',
            add_type='0', add_amount=0.0, deduction_type='0', deduction_amount=0.0,
            service_tax=self.excise_master
        )
        ServiceTaxInvoiceDetail.objects.create(
            id=3, invoice=invoice2, invoice_no='INV002', item_id=self.po_detail_item1,
            unit=self.unit_ea, qty=100.0, req_qty=20.0, amt_in_per=5.0, rate=50.0
        )
        # Now, for detail1 (id=1), total invoiced for item1 is 50 (from id=1) + 20 (from id=3) = 70.
        # Remaining available for detail1's update = 100 (PO total) - 20 (from id=3) = 80.
        self.assertEqual(detail1.get_remaining_qty(), 80.0)

    def test_validate_and_update_detail(self):
        detail = ServiceTaxInvoiceDetail.objects.get(id=1) # current req_qty = 50, PO total = 100
        
        # Test valid update
        success, message = detail.validate_and_update_detail(60.0, self.unit_ea, 6.0)
        self.assertTrue(success)
        self.assertIsNone(message)
        detail.refresh_from_db()
        self.assertEqual(detail.req_qty, 60.0)
        self.assertEqual(detail.amt_in_per, 6.0)

        # Test invalid update (exceeds remaining)
        success, message = detail.validate_and_update_detail(110.0, self.unit_ea, 6.0) # PO total 100, current 60
        self.assertFalse(success)
        self.assertIn("exceeds available quantity", message)
        detail.refresh_from_db()
        self.assertEqual(detail.req_qty, 60.0) # Should not have changed


class ServiceTaxInvoiceViewsTest(CommonTestDataMixin, TestCase):
    def setUp(self):
        self.client = Client()
        # Simulate session variables (Django test client doesn't automatically manage sessions)
        session = self.client.session
        session['compid'] = self.company.id
        session['finyear'] = self.financial_year.id
        session['username'] = 'testuser'
        session.save()

    def test_edit_view_get_request(self):
        response = self.client.get(reverse('servicetaxinvoice_edit', args=[self.invoice.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'invoicing/servicetaxinvoice/edit_details.html')
        self.assertContains(response, 'Service Tax Invoice - Edit')
        self.assertIsInstance(response.context['form'], type(ServiceTaxInvoiceForm()))
        self.assertEqual(response.context['invoice'].pk, self.invoice.pk)

    def test_edit_view_post_success(self):
        initial_duty_rate = self.invoice.duty_rate
        new_duty_rate = initial_duty_rate + 1.0
        
        post_data = {
            'duty_rate': str(new_duty_rate),
            'date_of_issue_invoice': '2023-01-20',
            'time_of_issue_invoice': '02:30:00 PM',
            'customer_category': self.service_category.id,
            'taxable_services': self.taxable_service.id,
            'buyer_name_autocomplete': self.customer_a.customer_name,
            'buyer_address': 'Updated Buyer Address',
            'buyer_country': self.country_in.id,
            'buyer_state': self.state_mh.id,
            'buyer_city': self.city_mumbai.id,
            'buyer_contact_person': 'New John',
            'buyer_phone': '111', 'buyer_mobile': '222', 'buyer_fax': '333',
            'buyer_email': '<EMAIL>', 'buyer_ecc': 'N1', 'buyer_tin_cst': 'N2', 'buyer_tin_vat': 'N3',
            'consignee_name_autocomplete': self.customer_a.customer_name,
            'consignee_address': 'Updated Consignee Address',
            'consignee_country': self.country_in.id,
            'consignee_state': self.state_mh.id,
            'consignee_city': self.city_mumbai.id,
            'consignee_contact_person': 'New John C',
            'consignee_phone': '444', 'consignee_mobile': '555', 'consignee_fax': '666',
            'consignee_email': '<EMAIL>', 'consignee_ecc': 'N4', 'consignee_tin_cst': 'N5', 'consignee_tin_vat': 'N6',
            'add_type': '1', 'add_amount': '20.0',
            'deduction_type': '1', 'deduction_amount': '5.0',
            'service_tax': self.excise_master.id,
        }
        
        response = self.client.post(reverse('servicetaxinvoice_edit', args=[self.invoice.pk]), post_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HTMX success code
        self.assertIn('HX-Redirect', response.headers)
        self.assertEqual(response.headers['HX-Redirect'], reverse_lazy('servicetaxinvoice_list'))

        updated_invoice = ServiceTaxInvoice.objects.get(pk=self.invoice.pk)
        self.assertAlmostEqual(updated_invoice.duty_rate, new_duty_rate)
        self.assertEqual(updated_invoice.buyer_address, 'Updated Buyer Address')
        self.assertEqual(updated_invoice.date_of_issue_invoice, datetime.date(2023, 1, 20))


    def test_edit_view_post_invalid_data(self):
        post_data = {
            'duty_rate': '-5.0', # Invalid negative value
            'date_of_issue_invoice': '2023-01-20',
            'time_of_issue_invoice': '10:00:00 AM',
            'customer_category': self.service_category.id,
            'taxable_services': self.taxable_service.id,
            'buyer_name_autocomplete': self.customer_a.customer_name,
            'buyer_address': '123 Main St', 'buyer_country': self.country_in.id, 'buyer_state': self.state_mh.id, 'buyer_city': self.city_mumbai.id,
            'buyer_contact_person': 'John Doe', 'buyer_phone': '111', 'buyer_mobile': '222', 'buyer_fax': '333',
            'buyer_email': '<EMAIL>', 'buyer_ecc': '1', 'buyer_tin_cst': '1', 'buyer_tin_vat': '1',
            'consignee_name_autocomplete': self.customer_a.customer_name,
            'consignee_address': '123 Main St', 'consignee_country': self.country_in.id, 'consignee_state': self.state_mh.id, 'consignee_city': self.city_mumbai.id,
            'consignee_contact_person': 'John Doe', 'consignee_phone': '111', 'consignee_mobile': '222', 'consignee_fax': '333',
            'consignee_email': '<EMAIL>', 'consignee_ecc': '1', 'consignee_tin_cst': '1', 'consignee_tin_vat': '1',
            'add_type': '0', 'add_amount': '50.0', 'deduction_type': '0', 'deduction_amount': '10.0',
            'service_tax': self.excise_master.id,
        }
        response = self.client.post(reverse('servicetaxinvoice_edit', args=[self.invoice.pk]), post_data)
        
        self.assertEqual(response.status_code, 200) # Should re-render with errors
        self.assertContains(response, 'Duty Rate cannot be negative.')

    def test_goods_tab_partial_view(self):
        response = self.client.get(reverse('servicetaxinvoice_goods_tab', args=[self.invoice.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'invoicing/servicetaxinvoice/_goods_table_partial.html')
        self.assertContains(response, 'Software License')
        self.assertContains(response, 'Consulting Hours')

    def test_detail_update_view_success(self):
        detail_to_update = self.invoice_detail_1 # original req_qty=50, PO total=100
        new_req_qty = 60.0
        new_amt_in_per = 12.5
        new_unit_id = self.unit_hr.id # Change unit
        
        post_data = {
            'req_qty': str(new_req_qty),
            'amt_in_per': str(new_amt_in_per),
            'unit': str(new_unit_id),
        }
        response = self.client.post(
            reverse('servicetaxinvoice_detail_update', args=[self.invoice.pk, detail_to_update.pk]),
            post_data,
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'invoicing/servicetaxinvoice/_goods_table_row.html')
        detail_to_update.refresh_from_db()
        self.assertEqual(detail_to_update.req_qty, new_req_qty)
        self.assertEqual(detail_to_update.unit.id, new_unit_id)
        self.assertContains(response, 'Item &#x27;Software License&#x27; updated successfully.') # HTMX messages

    def test_detail_update_view_invalid_quantity(self):
        detail_to_update = self.invoice_detail_1 # original req_qty=50, PO total=100
        new_req_qty = 150.0 # Exceeds total_qty of 100
        
        post_data = {
            'req_qty': str(new_req_qty),
            'amt_in_per': '5.0',
            'unit': str(self.unit_ea.id),
        }
        response = self.client.post(
            reverse('servicetaxinvoice_detail_update', args=[self.invoice.pk, detail_to_update.pk]),
            post_data,
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 400) # Bad Request for validation error
        self.assertTemplateUsed(response, 'invoicing/servicetaxinvoice/_goods_table_row.html')
        self.assertContains(response, 'Requested quantity 150.0 for Software License exceeds available quantity') # Error message

    def test_customer_autocomplete_view(self):
        response = self.client.post(
            reverse('customer_autocomplete'),
            {'hx-current-value': 'customer a'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'invoicing/servicetaxinvoice/_autocomplete_suggestions.html')
        self.assertContains(response, 'Customer A [CUST001]')

    def test_customer_search_populate_view_buyer(self):
        response = self.client.post(
            reverse('servicetaxinvoice_customer_search', args=[self.invoice.pk, 'buyer']),
            {'buyer_name_autocomplete': 'Customer B [CUST002]'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'invoicing/servicetaxinvoice/_buyer_tab_partial.html')
        # Check if buyer details are updated in the rendered form
        self.assertContains(response, 'value="456 Oak Ave"') # Check address from Customer B

    def test_copy_buyer_to_consignee_view(self):
        # First, ensure buyer details are distinctive
        self.invoice.buyer_name = 'Unique Buyer'
        self.invoice.buyer_address = 'Unique Address'
        self.invoice.save()
        
        response = self.client.post(
            reverse('servicetaxinvoice_copy_buyer', args=[self.invoice.pk]),
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'invoicing/servicetaxinvoice/_consignee_tab_partial.html')
        self.invoice.refresh_from_db() # Refresh the invoice instance
        self.assertEqual(self.invoice.consignee_name, 'Unique Buyer')
        self.assertEqual(self.invoice.consignee_address, 'Unique Address')

    def test_get_states_dropdown_view(self):
        response = self.client.get(
            reverse('get_states'),
            {'parent_id': self.country_in.id},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, f'<option value="{self.state_mh.id}">{self.state_mh.name}</option>')

    def test_get_cities_dropdown_view(self):
        response = self.client.get(
            reverse('get_cities'),
            {'parent_id': self.state_mh.id},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, f'<option value="{self.city_mumbai.id}">{self.city_mumbai.name}</option>')

```

---

### Step 5: HTMX and Alpine.js Integration

## Instructions:

- Use HTMX for all dynamic updates and form submissions
- Use Alpine.js for client-side reactivity and modals
- Implement DataTables for all list views with sorting and filtering
- Make all interactions work without full page reloads
- Ensure proper HX-Trigger responses for list refreshes after CRUD operations

**Integration Details:**

1.  **Main Page Layout:** The `edit_details.html` serves as the main page. It uses Alpine.js (`x-data="{ activeTab: ... }"`) to manage the active tab state locally.
2.  **Tab Content Loading:** Each tab's content (`#buyer-tab-content`, etc.) uses `hx-get` to fetch its respective partial HTML from the server when the tab becomes active (`hx-trigger="load, set-tab-index from body[detail.index='X']"`). This ensures only the necessary content is loaded dynamically.
3.  **Form Submission:** The main form (`#invoiceEditForm`) uses `hx-post` to submit the entire form data. On success (204 or 302 status), it triggers `HX-Redirect` to navigate to the list page, as in the original ASP.NET.
4.  **Customer Autocomplete:** `buyer_name_autocomplete` and `consignee_name_autocomplete` fields use `hx-post` to `customer_autocomplete` endpoint, triggering a `keyup changed delay` to fetch suggestions. The suggestions are rendered into a `div` below the input. An `hx-on:click` on the suggestion items copies the full `Name [Code]` to the input field.
5.  **Search Buttons:** "Search" buttons (`Button2`, `Button3` equivalents) use `hx-post` to `servicetaxinvoice_customer_search`, including the autocomplete field's value. The response updates the relevant tab's partial (`hx-target="#buyer-tab-content" hx-swap="outerHTML"`), populating the fields.
6.  **Copy from Buyer Button:** The "Copy from Buyer" button (`Button5` equivalent) similarly uses `hx-post` to `servicetaxinvoice_copy_buyer`, targeting and swapping the `consignee-tab-content`.
7.  **Cascading Dropdowns:** `buyer_country` and `consignee_country` dropdowns use `hx-get` to `get_states` (passing `parent_id` as the selected country ID). The response (`<select name="state" id="id_state">...`) directly replaces the state dropdown. The state dropdown then triggers a similar `hx-get` to `get_cities`.
8.  **Goods Table (DataTables & Inline Edit):**
    *   The `_goods_table_partial.html` includes the `<table>` structure. DataTables is initialized on `document.ready` for client-side sorting, pagination, and searching.
    *   Each row (`_goods_table_row.html`) uses Alpine.js (`x-data="{ editMode: false }"`) and listens to the checkbox change.
    *   When a checkbox is checked, Alpine.js shows the input fields (`req_qty`, `amt_in_per`, `unit`) and hides the labels. It also reveals an "Update" button within that row.
    *   The "Update" button (in `_goods_table_row.html`) uses `hx-post` to `servicetaxinvoice_detail_update`, including only the relevant input fields for that row. On a successful update (200 OK), HTMX re-swaps `this` (the current `<tr>`), effectively replacing the row with its updated version (including potential new calculated remaining quantity and updated labels/input visibility).
    *   Error messages from `ServiceTaxInvoiceDetailUpdateView` are also rendered within the row.
9.  **Messages:** Django's `messages` framework is used for user feedback. These messages are typically rendered in `core/base.html` and might be handled by Alpine.js to show/hide notifications.

---

## Final Notes

*   **Placeholders:** Replace `tblCompany_Master`, `tblFinancialYear_Master`, `tblCountry_Master`, `tblState_Master`, `tblCity_Master` with your actual legacy database table names if they differ.
*   **Authentication:** The `session_id` and `company_id`, `financial_year_id` are currently mocked using `request.session`. In a real Django application, these would typically come from `request.user` after proper authentication and user/company management setup.
*   **Input Formatting:** The ASP.NET `fun.FromDateDMY` and `fun.TimeSelector` are handled by Django's `DateField` and `CharField` with specific input formats and validation regex to match the expected legacy formats.
*   **`clsFunctions` Logic:** All database interaction logic originally in `clsFunctions` (e.g., `select`, `update`, dropdown population, validation) has been transformed into Django ORM queries and model methods, adhering to the fat model principle.
*   **Error Handling:** Basic error messages are displayed. A more robust production system would include better error logging and user-friendly error pages.
*   **Test Coverage:** The provided tests aim for comprehensiveness, covering model logic and view interactions including HTMX. Ensure you run these tests and aim for high coverage (80%+) for a stable migration.