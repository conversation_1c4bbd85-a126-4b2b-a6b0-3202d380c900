This modernization plan details the transition of your ASP.NET Sales Invoice application to a robust, scalable, and modern Django-based solution. We will leverage AI-assisted automation to streamline this process, focusing on a "fat model, thin view" architecture, HTMX for dynamic interactions, Alpine.js for UI state, and DataTables for efficient data presentation.

## ASP.NET to Django Conversion Script: Sales Invoice Details

This plan outlines the specific Django application files required for the "Sales Invoice - New" functionality, adhering strictly to the provided guidelines.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the ASP.NET code, the following tables and their primary fields are involved in the Sales Invoice Details process:

*   **`tblExciseCommodity_Master`**: Stores excise commodity terms.
    *   Fields: `Id` (PK), `Terms`
*   **`tblACC_Service_Category`**: Stores service categories.
    *   Fields: `Id` (PK), `Description`
*   **`tblACC_TransportMode`**: Stores modes of transport.
    *   Fields: `Id` (PK), `Description`
*   **`tblACC_Removable_Nature`**: Stores nature of removal options.
    *   Fields: `Id` (PK), `Description`
*   **`tblACC_SalesInvoice_Master_Type`**: Stores invoice types (e.g., "Within Mh.", "Out of Mh.").
    *   Fields: `Id` (PK), `Description`
*   **`Unit_Master`**: Stores units of quantity.
    *   Fields: `Id` (PK), `Symbol`
*   **`tblVAT_Master`**: Stores VAT terms.
    *   Fields: `Id` (PK), `Terms`
*   **`tblExciseser_Master`**: Stores excise service terms (CENVAT).
    *   Fields: `Id` (PK), `Terms`
*   **`SD_Cust_master`**: Stores customer (buyer/consignee) details.
    *   Fields: `CustomerId` (PK), `CustomerName`, `MaterialDelAddress`, `MaterialDelCountry`, `MaterialDelState`, `MaterialDelCity`, `MaterialDelContactNo`, `MaterialDelFaxNo`, `ContactPerson`, `Email`, `TinVatNo`, `EccNo`, `ContactNo`, `TinCstNo`
*   **`SD_Cust_PO_Master`**: Stores Purchase Order master data.
    *   Fields: `POId` (PK), `PONo`, `CompId`
*   **`SD_Cust_PO_Details`**: Stores Purchase Order item details.
    *   Fields: `Id` (PK, for PO item), `POId` (FK to `SD_Cust_PO_Master.POId`), `ItemDesc`, `TotalQty`, `Unit` (FK to `Unit_Master.Id`), `Rate`
*   **`tblACC_SalesInvoice_Master`**: The main sales invoice header table.
    *   Fields: `Id` (PK), `SysDate`, `SysTime`, `CompId`, `FinYearId`, `SessionId`, `InvoiceNo`, `PONo`, `WONo`, `InvoiceMode`, `DateOfIssueInvoice`, `DateOfRemoval`, `TimeOfIssueInvoice`, `TimeOfRemoval`, `NatureOfRemoval` (FK), `Commodity` (FK), `ModeOfTransport` (FK), `RRGCNo`, `VehiRegNo`, `DutyRate`, `CustomerCode` (FK to `SD_Cust_master.CustomerId`), `CustomerCategory` (FK), `Buyer_name`, `Buyer_add`, `Buyer_country` (FK), `Buyer_state` (FK), `Buyer_city` (FK), `Buyer_cotper`, `Buyer_ph`, `Buyer_email`, `Buyer_ecc`, `Buyer_tin`, `Buyer_mob`, `Buyer_fax`, `Buyer_vat`, `Cong_name`, `Cong_add`, `Cong_Country` (FK), `Cong_state` (FK), `Cong_city` (FK), `Cong_cotper`, `Cong_ph`, `Cong_email`, `Cong_ecc`, `Cong_tin`, `Cong_mob`, `Cong_fax`, `Cong_vat`, `AddType`, `AddAmt`, `DeductionType`, `Deduction`, `PFType`, `PF`, `CENVAT` (FK), `SED`, `AED`, `VAT` (FK), `SelectedCST`, `CST` (FK), `FreightType`, `Freight`, `InsuranceType`, `Insurance`, `SEDType`, `AEDType`, `POId` (FK to `SD_Cust_PO_Master.POId`), `OtherAmt`
*   **`tblACC_SalesInvoice_Details`**: Stores sales invoice item details.
    *   Fields: `Id` (PK), `InvoiceNo` (FK to `tblACC_SalesInvoice_Master.InvoiceNo`), `MId` (FK to `tblACC_SalesInvoice_Master.Id`), `ItemId` (FK to `SD_Cust_PO_Details.Id`), `Unit` (FK to `Unit_Master.Id`), `Qty`, `ReqQty`, `AmtInPer`, `Rate`

### Step 2: Identify Backend Functionality

The ASP.NET page performs the following:

*   **Read (R):**
    *   Loads initial invoice details, PO/WO numbers, current date, and invoice mode based on query parameters.
    *   Retrieves customer details (Buyer) based on a customer code from the query string.
    *   Populates dropdowns from various lookup tables (`tblExciseCommodity_Master`, `tblACC_Service_Category`, `tblACC_TransportMode`, `tblACC_Removable_Nature`, `Unit_Master`, `tblVAT_Master`, `tblExciseser_Master`).
    *   Fills the "Goods" GridView by querying `SD_Cust_PO_Master` and `SD_Cust_PO_Details`, and calculating `RemainingQty` based on previously invoiced quantities.
    *   Customer search functionality (for Buyer and Consignee) via `AutoCompleteExtender` and explicit "Search" buttons, fetching customer details and populating form fields.
    *   Cascading dropdowns for Country -> State -> City.
    *   Updates Tariff Head No. on commodity selection.
*   **Create (C):**
    *   Generates a new sequential `InvoiceNo`.
    *   The `BtnSubmit_Click` handles the main "create" operation, inserting data into `tblACC_SalesInvoice_Master` and `tblACC_SalesInvoice_Details` after extensive validation.
*   **Update (U):** (Implicit, but not explicitly shown for *editing* an existing invoice on this page. The page title "Sales Invoice - New" suggests it's primarily for creation. The logic would be similar to `Create` but with initial data loading.)
*   **Delete (D):** Not explicitly shown on this page.
*   **Validation Logic:** Extensive server-side validation on form submission for all fields (required, regex for numbers/email, date formats). Specific quantity validation for "Goods" items (checking `ReqQty` against `RemainingQty`).
*   **Tabbed UI:** Manages different sections of the invoice (Buyer, Consignee, Goods, Taxation) using a `TabContainer`.
*   **Session Management:** Stores and retrieves `CompId`, `FinYearId`, `username`, and `TabIndex` from session.

### Step 3: Infer UI Components

The page uses various ASP.NET controls, which will be mapped to standard HTML inputs styled with Tailwind CSS, and enhanced with HTMX/Alpine.js for interactivity:

*   **Text Boxes (`asp:TextBox`):** Input fields for invoice number, dates, addresses, contact details, quantities, rates, etc.
*   **Labels (`asp:Label`):** Display static information like invoice date, PO/WO numbers, and pre-filled data.
*   **Dropdown Lists (`asp:DropDownList`):** For selecting categories, commodities, transport modes, removal nature, units, VAT/CST types, and for Country/State/City selections.
*   **Buttons (`asp:Button`):** Triggers for tab navigation ("Next"), customer search, "Copy from buyer", "Submit", and "Cancel".
*   **Check Boxes (`asp:CheckBox`):** In the "Goods" GridView to select items.
*   **Tab Container (`cc1:TabContainer`):** The primary layout control for segmenting the form.
*   **Grid View (`asp:GridView`):** Presents the "Goods" items in a tabular format, allowing input for `ReqQty` and `Amt` for each item. This will be replaced by a `<table>` enhanced with DataTables.
*   **Validators (`asp:RequiredFieldValidator`, `asp:RegularExpressionValidator`):** Client-side and server-side validation. This will be handled by Django forms and custom validators.
*   **AJAX Controls (`cc1:CalendarExtender`, `cc1:AutoCompleteExtender`):** Calendar for date picking, autocomplete for customer names. These will be replaced by Alpine.js for date pickers and HTMX + backend endpoint for autocomplete.

### Step 4: Generate Django Code

The Django application will be named `sales_invoices`.

#### 4.1 Models (`sales_invoices/models.py`)

This section defines the Django models, mapping to the existing database schema (`managed = False`) and incorporating business logic as model methods.

```python
from django.db import models
from django.db.models import Sum, F

class ExciseCommodity(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblExciseCommodity_Master'
        verbose_name = 'Excise Commodity'
        verbose_name_plural = 'Excise Commodities'

    def __str__(self):
        return self.terms or ''

    @classmethod
    def get_tariff_head(cls, commodity_id):
        """Fetches the tariff head number based on commodity ID (placeholder logic)."""
        # In a real scenario, this might be a lookup in a separate table
        # or a specific field in tblExciseCommodity_Master.
        # For now, it's a simple string based on ID.
        if commodity_id == 1:
            return "TARIFF/EXMP-1234"
        elif commodity_id == 2:
            return "TARIFF/EXMP-5678"
        return "N/A"

class ServiceCategory(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_Service_Category'
        verbose_name = 'Service Category'
        verbose_name_plural = 'Service Categories'

    def __str__(self):
        return self.description or ''

class TransportMode(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_TransportMode'
        verbose_name = 'Transport Mode'
        verbose_name_plural = 'Transport Modes'

    def __str__(self):
        return self.description or ''

class RemovalNature(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_Removable_Nature'
        verbose_name = 'Removal Nature'
        verbose_name_plural = 'Removal Natures'

    def __str__(self):
        return self.description or ''

class SalesInvoiceType(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_SalesInvoice_Master_Type'
        verbose_name = 'Sales Invoice Type'
        verbose_name_plural = 'Sales Invoice Types'

    def __str__(self):
        return self.description or ''

class Unit(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol or ''

class VATMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblVAT_Master'
        verbose_name = 'VAT Master'
        verbose_name_plural = 'VAT Masters'

    def __str__(self):
        return self.terms or ''

class ExciseServiceMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255, blank=True, null=True)
    live = models.BooleanField(db_column='Live', default=True) # Assuming Live is boolean from 'Live=1' query

    class Meta:
        managed = False
        db_table = 'tblExciseser_Master'
        verbose_name = 'Excise Service Master'
        verbose_name_plural = 'Excise Service Masters'

    def __str__(self):
        return self.terms or ''

class Country(models.Model):
    # Assuming Country, State, City models exist or are handled by a lookup service
    # For simplicity, if they map to simple lookup tables
    id = models.IntegerField(db_column='CId', primary_key=True)
    name = models.CharField(db_column='CountryName', max_length=100) # Placeholder

    class Meta:
        managed = False
        db_table = 'tblCountry_Master' # Example table name
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'
    
    def __str__(self):
        return self.name

class State(models.Model):
    id = models.IntegerField(db_column='SId', primary_key=True)
    name = models.CharField(db_column='StateName', max_length=100) # Placeholder
    country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='CId')

    class Meta:
        managed = False
        db_table = 'tblState_Master' # Example table name
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.name

class City(models.Model):
    id = models.IntegerField(db_column='CityId', primary_key=True)
    name = models.CharField(db_column='CityName', max_length=100) # Placeholder
    state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='SId')

    class Meta:
        managed = False
        db_table = 'tblCity_Master' # Example table name
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.name

class Customer(models.Model):
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    material_del_address = models.TextField(db_column='MaterialDelAddress', blank=True, null=True)
    material_del_country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='MaterialDelCountry', blank=True, null=True)
    material_del_state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='MaterialDelState', blank=True, null=True)
    material_del_city = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='MaterialDelCity', blank=True, null=True)
    material_del_contact_no = models.CharField(db_column='MaterialDelContactNo', max_length=50, blank=True, null=True)
    material_del_fax_no = models.CharField(db_column='MaterialDelFaxNo', max_length=50, blank=True, null=True)
    contact_person = models.CharField(db_column='ContactPerson', max_length=100, blank=True, null=True)
    email = models.CharField(db_column='Email', max_length=100, blank=True, null=True)
    tin_vat_no = models.CharField(db_column='TinVatNo', max_length=50, blank=True, null=True)
    ecc_no = models.CharField(db_column='EccNo', max_length=50, blank=True, null=True)
    contact_no = models.CharField(db_column='ContactNo', max_length=50, blank=True, null=True) # Mobile No
    tin_cst_no = models.CharField(db_column='TinCstNo', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId') # Assuming this is a required field

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customer_name} [{self.customer_id}]"

    @classmethod
    def search_by_name(cls, prefix_text, comp_id):
        """
        Mimics the `sql` WebMethod for autocomplete.
        Returns a list of customer names with IDs.
        """
        customers = cls.objects.filter(
            customer_name__icontains=prefix_text,
            comp_id=comp_id
        ).order_by('customer_name')[:10] # Limit results for autocomplete
        return [f"{c.customer_name} [{c.customer_id}]" for c in customers]
    
    @classmethod
    def get_customer_details(cls, customer_code, comp_id):
        """
        Fetches full customer details based on customer ID/Code.
        Mimics Button5_Click/Button4_Click logic.
        """
        try:
            return cls.objects.select_related('material_del_country', 'material_del_state', 'material_del_city').get(
                customer_id=customer_code,
                comp_id=comp_id
            )
        except cls.DoesNotExist:
            return None

class PurchaseOrder(models.Model):
    poid = models.IntegerField(db_column='POId', primary_key=True)
    pono = models.CharField(db_column='PONo', max_length=50)
    comp_id = models.IntegerField(db_column='CompId') # Assuming this is a required field

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Master'
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'

    def __str__(self):
        return self.pono

class PurchaseOrderDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # This is the PO_Details_Id
    poid = models.ForeignKey(PurchaseOrder, on_delete=models.DO_NOTHING, db_column='POId')
    item_desc = models.CharField(db_column='ItemDesc', max_length=255)
    total_qty = models.FloatField(db_column='TotalQty')
    unit = models.ForeignKey(Unit, on_delete=models.DO_NOTHING, db_column='Unit')
    rate = models.FloatField(db_column='Rate')

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Details'
        verbose_name = 'Purchase Order Detail'
        verbose_name_plural = 'Purchase Order Details'

    def __str__(self):
        return f"{self.item_desc} ({self.total_qty} {self.unit.symbol})"

    def get_remaining_qty(self, comp_id):
        """Calculates remaining quantity for this PO item."""
        total_invoiced_qty = SalesInvoiceDetail.objects.filter(
            item_id=self.id,
            invoice__comp_id=comp_id # Access through related_name 'invoice'
        ).aggregate(Sum('req_qty'))['req_qty__sum'] or 0

        return self.total_qty - total_invoiced_qty

    def get_total_invoiced_amt_per(self, comp_id):
        """Calculates total invoiced amount percentage for this PO item."""
        total_invoiced_amt_per = SalesInvoiceDetail.objects.filter(
            item_id=self.id,
            invoice__comp_id=comp_id
        ).aggregate(Sum('amt_in_per'))['amt_in_per__sum'] or 0
        return total_invoiced_amt_per

class SalesInvoice(models.Model):
    # Main Invoice Fields
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    session_id = models.CharField(db_column='SessionId', max_length=50) # Assuming SessionId is string username
    invoice_no = models.CharField(db_column='InvoiceNo', max_length=50)
    pono = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    wono = models.CharField(db_column='WONo', max_length=255, blank=True, null=True) # Comma separated WOs
    invoice_mode = models.ForeignKey(SalesInvoiceType, on_delete=models.DO_NOTHING, db_column='InvoiceMode', blank=True, null=True)
    date_of_issue_invoice = models.DateField(db_column='DateOfIssueInvoice')
    date_of_removal = models.DateField(db_column='DateOfRemoval')
    time_of_issue_invoice = models.CharField(db_column='TimeOfIssueInvoice', max_length=50, blank=True, null=True) # Stored as string
    time_of_removal = models.CharField(db_column='TimeOfRemoval', max_length=50, blank=True, null=True) # Stored as string
    nature_of_removal = models.ForeignKey(RemovalNature, on_delete=models.DO_NOTHING, db_column='NatureOfRemoval', blank=True, null=True)
    commodity = models.ForeignKey(ExciseCommodity, on_delete=models.DO_NOTHING, db_column='Commodity', blank=True, null=True)
    mode_of_transport = models.ForeignKey(TransportMode, on_delete=models.DO_NOTHING, db_column='ModeOfTransport', blank=True, null=True)
    rrgc_no = models.CharField(db_column='RRGCNo', max_length=50, blank=True, null=True)
    vehi_reg_no = models.CharField(db_column='VehiRegNo', max_length=50, blank=True, null=True)
    duty_rate = models.FloatField(db_column='DutyRate', blank=True, null=True)

    # Buyer Fields
    customer_code = models.ForeignKey(Customer, on_delete=models.DO_NOTHING, db_column='CustomerCode', blank=True, null=True, related_name='sales_invoices_as_buyer')
    customer_category = models.ForeignKey(ServiceCategory, on_delete=models.DO_NOTHING, db_column='CustomerCategory', blank=True, null=True)
    buyer_name = models.CharField(db_column='Buyer_name', max_length=255, blank=True, null=True)
    buyer_add = models.TextField(db_column='Buyer_add', blank=True, null=True)
    buyer_country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='Buyer_country', blank=True, null=True, related_name='sales_invoices_buyer_country')
    buyer_state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='Buyer_state', blank=True, null=True, related_name='sales_invoices_buyer_state')
    buyer_city = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='Buyer_city', blank=True, null=True, related_name='sales_invoices_buyer_city')
    buyer_cotper = models.CharField(db_column='Buyer_cotper', max_length=100, blank=True, null=True) # Contact Person
    buyer_ph = models.CharField(db_column='Buyer_ph', max_length=50, blank=True, null=True) # Phone
    buyer_email = models.CharField(db_column='Buyer_email', max_length=100, blank=True, null=True)
    buyer_ecc = models.CharField(db_column='Buyer_ecc', max_length=50, blank=True, null=True)
    buyer_tin = models.CharField(db_column='Buyer_tin', max_length=50, blank=True, null=True) # TIN/CST No
    buyer_mob = models.CharField(db_column='Buyer_mob', max_length=50, blank=True, null=True) # Mobile
    buyer_fax = models.CharField(db_column='Buyer_fax', max_length=50, blank=True, null=True)
    buyer_vat = models.CharField(db_column='Buyer_vat', max_length=50, blank=True, null=True) # TIN/VAT No

    # Consignee Fields
    cong_name = models.CharField(db_column='Cong_name', max_length=255, blank=True, null=True)
    cong_add = models.TextField(db_column='Cong_add', blank=True, null=True)
    cong_country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='Cong_Country', blank=True, null=True, related_name='sales_invoices_cong_country')
    cong_state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='Cong_state', blank=True, null=True, related_name='sales_invoices_cong_state')
    cong_city = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='Cong_city', blank=True, null=True, related_name='sales_invoices_cong_city')
    cong_cotper = models.CharField(db_column='Cong_cotper', max_length=100, blank=True, null=True) # Contact Person
    cong_ph = models.CharField(db_column='Cong_ph', max_length=50, blank=True, null=True) # Phone
    cong_email = models.CharField(db_column='Cong_email', max_length=100, blank=True, null=True)
    cong_ecc = models.CharField(db_column='Cong_ecc', max_length=50, blank=True, null=True)
    cong_tin = models.CharField(db_column='Cong_tin', max_length=50, blank=True, null=True) # TIN/CST No
    cong_mob = models.CharField(db_column='Cong_mob', max_length=50, blank=True, null=True) # Mobile
    cong_fax = models.CharField(db_column='Cong_fax', max_length=50, blank=True, null=True)
    cong_vat = models.CharField(db_column='Cong_vat', max_length=50, blank=True, null=True) # TIN/VAT No

    # Taxation Fields
    add_type = models.IntegerField(db_column='AddType', blank=True, null=True) # 0: Amt(Rs), 1: Per(%)
    add_amt = models.FloatField(db_column='AddAmt', blank=True, null=True)
    deduction_type = models.IntegerField(db_column='DeductionType', blank=True, null=True) # 0: Amt(Rs), 1: Per(%)
    deduction = models.FloatField(db_column='Deduction', blank=True, null=True)
    pf_type = models.IntegerField(db_column='PFType', blank=True, null=True) # 0: Amt(Rs), 1: Per(%)
    pf = models.FloatField(db_column='PF', blank=True, null=True)
    cenvat = models.ForeignKey(ExciseServiceMaster, on_delete=models.DO_NOTHING, db_column='CENVAT', blank=True, null=True)
    sed = models.FloatField(db_column='SED', blank=True, null=True)
    aed = models.FloatField(db_column='AED', blank=True, null=True)
    vat = models.ForeignKey(VATMaster, on_delete=models.DO_NOTHING, db_column='VAT', blank=True, null=True)
    selected_cst = models.IntegerField(db_column='SelectedCST', blank=True, null=True) # 0: With C Form, 1: Without C Form
    cst = models.ForeignKey(VATMaster, on_delete=models.DO_NOTHING, db_column='CST', blank=True, null=True, related_name='sales_invoices_cst') # Using VATMaster for CST also as per original, or separate CST master
    freight_type = models.IntegerField(db_column='FreightType', blank=True, null=True) # 0: Amt(Rs), 1: Per(%)
    freight = models.FloatField(db_column='Freight', blank=True, null=True)
    insurance_type = models.IntegerField(db_column='InsuranceType', blank=True, null=True) # 0: Amt(Rs), 1: Per(%)
    insurance = models.FloatField(db_column='Insurance', blank=True, null=True)
    sedtype = models.IntegerField(db_column='SEDType', blank=True, null=True) # Assuming this is a type for SED field
    aedtype = models.IntegerField(db_column='AEDType', blank=True, null=True) # Assuming this is a type for AED field
    poid = models.ForeignKey(PurchaseOrder, on_delete=models.DO_NOTHING, db_column='POId', blank=True, null=True) # Link to original PO
    other_amt = models.FloatField(db_column='OtherAmt', blank=True, null=True, default=0.0)

    class Meta:
        managed = False
        db_table = 'tblACC_SalesInvoice_Master'
        verbose_name = 'Sales Invoice'
        verbose_name_plural = 'Sales Invoices'

    def __str__(self):
        return self.invoice_no

    @classmethod
    def generate_next_invoice_no(cls, comp_id, fin_year_id):
        """Generates the next sequential invoice number."""
        last_invoice = cls.objects.filter(comp_id=comp_id, fin_year_id=fin_year_id).order_by('-invoice_no').first()
        if last_invoice and last_invoice.invoice_no:
            try:
                # Assuming InvoiceNo is purely numeric or has a numeric part
                last_num = int(last_invoice.invoice_no)
                return f"{(last_num + 1):04d}"
            except ValueError:
                # Handle cases where InvoiceNo might be alphanumeric
                return "0001" # Fallback
        return "0001"

    def save_full_invoice(self, details_data):
        """
        Saves the SalesInvoice master and its details within a transaction.
        Performs critical business validations here.
        """
        # This method encapsulates the complex logic from BtnSubmit_Click
        # It assumes details_data is a list of dictionaries for SalesInvoiceDetail
        
        # 1. Overall Form Validation (already handled by Django Forms for basic fields)
        #    Additional complex checks:
        
        # 2. Goods Quantity Validation (rmnqty >= ReqQty)
        #    This happens for each detail item.
        for detail_item in details_data:
            po_detail_id = detail_item['item_id'].id # item_id is a PurchaseOrderDetail object
            req_qty = detail_item['req_qty']
            
            po_detail = PurchaseOrderDetail.objects.get(id=po_detail_id)
            remaining_qty = po_detail.get_remaining_qty(self.comp_id)

            if req_qty > remaining_qty:
                raise ValueError(f"Required quantity ({req_qty}) for item '{po_detail.item_desc}' exceeds remaining quantity ({remaining_qty}).")
            
            # Additional check for amount percentage (if needed from original logic)
            # total_invoiced_amt_per = po_detail.get_total_invoiced_amt_per(self.comp_id)
            # if detail_item['amt_in_per'] > (100 - total_invoiced_amt_per):
            #     raise ValueError(f"Amount percentage for item '{po_detail.item_desc}' exceeds remaining percentage.")

        # If all validations pass, save
        with models.transaction.atomic():
            self.save() # Saves the master record
            
            # Save details
            for detail_item in details_data:
                SalesInvoiceDetail.objects.create(
                    invoice=self,
                    invoice_no=self.invoice_no, # Redundant if using FK, but kept for schema match
                    mid=self.id, # Redundant if using FK, but kept for schema match
                    **detail_item
                )
        return True

class SalesInvoiceDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    invoice = models.ForeignKey(SalesInvoice, on_delete=models.CASCADE, db_column='MId', related_name='details') # MId maps to Invoice Id
    invoice_no = models.CharField(db_column='InvoiceNo', max_length=50) # Redundant with FK, kept for schema match
    item_id = models.ForeignKey(PurchaseOrderDetail, on_delete=models.DO_NOTHING, db_column='ItemId') # ItemId maps to PO_Details_Id
    unit = models.ForeignKey(Unit, on_delete=models.DO_NOTHING, db_column='Unit')
    qty = models.FloatField(db_column='Qty') # TotalQty from PO_Details
    req_qty = models.FloatField(db_column='ReqQty') # Requested Qty in this invoice
    amt_in_per = models.FloatField(db_column='AmtInPer')
    rate = models.FloatField(db_column='Rate')

    class Meta:
        managed = False
        db_table = 'tblACC_SalesInvoice_Details'
        verbose_name = 'Sales Invoice Detail'
        verbose_name_plural = 'Sales Invoice Details'

    def __str__(self):
        return f"Inv: {self.invoice_no}, Item: {self.item_id.item_desc}"

```

#### 4.2 Forms (`sales_invoices/forms.py`)

We'll define forms for the main invoice details and a formset for the dynamic "Goods" section.

```python
from django import forms
from django.forms import inlineformset_factory
from .models import SalesInvoice, SalesInvoiceDetail, Customer, ServiceCategory, ExciseCommodity, TransportMode, RemovalNature, SalesInvoiceType, Unit, VATMaster, ExciseServiceMaster, Country, State, City
import datetime

class SalesInvoiceMainForm(forms.ModelForm):
    # Hidden fields to carry context from previous page/session
    po_no = forms.CharField(widget=forms.HiddenInput(), required=False)
    wo_no = forms.CharField(widget=forms.HiddenInput(), required=False)
    poid = forms.IntegerField(widget=forms.HiddenInput(), required=False)
    customer_code_initial = forms.CharField(widget=forms.HiddenInput(), required=False)
    invoice_type_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)
    po_date = forms.DateField(widget=forms.HiddenInput(), required=False)

    # Fields directly on SalesInvoice master
    invoice_no = forms.CharField(
        label="Invoice No.",
        widget=forms.TextInput(attrs={'class': 'box3', 'readonly': 'readonly'})
    )
    date_of_issue_invoice = forms.DateField(
        label="Date Of Issue Of Invoice",
        widget=forms.DateInput(attrs={'class': 'box3', 'type': 'date'}),
        error_messages={'required': '*'}
    )
    time_of_issue_invoice = forms.CharField(
        label="Time Of Issue Of Invoice",
        widget=forms.TimeInput(attrs={'class': 'box3', 'type': 'time', 'step': '60'}), # step for minute increment
        required=False
    )
    date_of_removal = forms.DateField(
        label="Date Of Removal",
        widget=forms.DateInput(attrs={'class': 'box3', 'type': 'date'}),
        error_messages={'required': '*'}
    )
    time_of_removal = forms.CharField(
        label="Time Of Removal",
        widget=forms.TimeInput(attrs={'class': 'box3', 'type': 'time', 'step': '60'}), # step for minute increment
        required=False
    )
    nature_of_removal = forms.ModelChoiceField(
        queryset=RemovalNature.objects.all().order_by('id'),
        label="Nature Of Removal",
        empty_label=None,
        widget=forms.Select(attrs={'class': 'box3'})
    )
    commodity = forms.ModelChoiceField(
        queryset=ExciseCommodity.objects.all().order_by('id'),
        label="Excisable Commodity",
        empty_label="--- Select ---",
        widget=forms.Select(attrs={'class': 'box3', 'hx-get': '/sales_invoice/get_tariff_head/', 'hx-target': '#tariffHead', 'hx-swap': 'innerHTML'})
    )
    duty_rate = forms.FloatField(
        label="Rate of Duty",
        widget=forms.TextInput(attrs={'class': 'box3', 'placeholder': '[Tariff Rate/Notifi No. & Date]'}),
        error_messages={'required': '*'}
    )
    mode_of_transport = forms.ModelChoiceField(
        queryset=TransportMode.objects.all().order_by('id'),
        label="Mode of Transport",
        empty_label=None,
        widget=forms.Select(attrs={'class': 'box3'})
    )
    rrgc_no = forms.CharField(
        label="R.R.G.C. No",
        widget=forms.TextInput(attrs={'class': 'box3'}),
        error_messages={'required': '*'}
    )
    vehi_reg_no = forms.CharField(
        label="If by motor vehicle , it's regist. number",
        widget=forms.TextInput(attrs={'class': 'box3'}),
        required=False
    )

    class Meta:
        model = SalesInvoice
        fields = [
            'invoice_no', 'date_of_issue_invoice', 'time_of_issue_invoice',
            'date_of_removal', 'time_of_removal', 'nature_of_removal',
            'commodity', 'duty_rate', 'mode_of_transport', 'rrgc_no', 'vehi_reg_no',
            'customer_category', 'other_amt', 'add_type', 'add_amt', 'deduction_type',
            'deduction', 'pf_type', 'pf', 'cenvat', 'sed', 'aed', 'vat',
            'selected_cst', 'cst', 'freight_type', 'freight', 'insurance_type',
            'insurance', 'sedtype', 'aedtype' # Ensure all fields from ASP.NET are mapped
        ]
        # Exclude fields handled by hidden inputs or dynamic setting
        exclude = ['sys_date', 'sys_time', 'comp_id', 'fin_year_id', 'session_id', 'pono', 'wono',
                   'invoice_mode', 'customer_code', 'buyer_name', 'buyer_add', 'buyer_country',
                   'buyer_state', 'buyer_city', 'buyer_cotper', 'buyer_ph', 'buyer_email',
                   'buyer_ecc', 'buyer_tin', 'buyer_mob', 'buyer_fax', 'buyer_vat', 'cong_name',
                   'cong_add', 'cong_country', 'cong_state', 'cong_city', 'cong_cotper',
                   'cong_ph', 'cong_email', 'cong_ecc', 'cong_tin', 'cong_mob', 'cong_fax',
                   'cong_vat', 'poid']
        
        widgets = {
            'customer_category': forms.Select(attrs={'class': 'box3'}),
            'other_amt': forms.NumberInput(attrs={'class': 'box3', 'value': 0}),
            'add_type': forms.Select(attrs={'class': 'box3'}, choices=((0, 'Amt(Rs)'), (1, 'Per(%)'))),
            'add_amt': forms.NumberInput(attrs={'class': 'box3'}),
            'deduction_type': forms.Select(attrs={'class': 'box3'}, choices=((0, 'Amt(Rs)'), (1, 'Per(%)'))),
            'deduction': forms.NumberInput(attrs={'class': 'box3'}),
            'pf_type': forms.Select(attrs={'class': 'box3'}, choices=((0, 'Amt(Rs)'), (1, 'Per(%)'))),
            'pf': forms.NumberInput(attrs={'class': 'box3'}),
            'cenvat': forms.Select(attrs={'class': 'box3'}),
            'sed': forms.NumberInput(attrs={'class': 'box3'}),
            'aed': forms.NumberInput(attrs={'class': 'box3'}),
            'vat': forms.Select(attrs={'class': 'box3'}),
            'selected_cst': forms.Select(attrs={'class': 'box3'}, choices=((0, 'C.S.T.(With C Form)'), (1, 'C.S.T.(Without C Form)'))),
            'cst': forms.Select(attrs={'class': 'box3'}),
            'freight_type': forms.Select(attrs={'class': 'box3'}, choices=((0, 'Amt(Rs)'), (1, 'Per(%)'))),
            'freight': forms.NumberInput(attrs={'class': 'box3'}),
            'insurance_type': forms.Select(attrs={'class': 'box3'}, choices=((0, 'Amt(Rs)'), (1, 'Per(%)'))),
            'insurance': forms.NumberInput(attrs={'class': 'box3'}),
            'sedtype': forms.Select(attrs={'class': 'box3'}, choices=((0, 'Amt(Rs)'), (1, 'Per(%)'))),
            'aedtype': forms.Select(attrs={'class': 'box3'}, choices=((0, 'Amt(Rs)'), (1, 'Per(%)'))),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate initial values for display labels
        self.fields['customer_category'].queryset = ServiceCategory.objects.all()
        self.fields['cenvat'].queryset = ExciseServiceMaster.objects.filter(live=True)
        self.fields['vat'].queryset = VATMaster.objects.all()
        self.fields['cst'].queryset = VATMaster.objects.all() # Assuming CST also from VATMaster

        # Set labels for fields if needed from original form
        self.fields['sed'].label = "SED"
        self.fields['aed'].label = "AED"
        self.fields['add_amt'].label = "Add"
        self.fields['deduction'].label = "Deduction"
        self.fields['pf'].label = "P&F"
        self.fields['freight'].label = "Freight"
        self.fields['insurance'].label = "Insurance"


class SalesInvoiceBuyerForm(forms.ModelForm):
    buyer_name_search = forms.CharField(
        label="Name",
        widget=forms.TextInput(attrs={'class': 'box3', 'hx-get': '/sales_invoice/autocomplete_customer/', 'hx-trigger': 'keyup changed delay:500ms', 'hx-target': '#buyer-name-suggestions', 'autocomplete': 'off'}),
        help_text='<div id="buyer-name-suggestions" class="autocomplete-list"></div>',
        required=False
    )
    buyer_name_selected = forms.CharField(
        label="Name", # This will be the actual name to save
        widget=forms.TextInput(attrs={'class': 'box3', 'readonly': 'readonly'}),
        required=True,
        error_messages={'required': '*'}
    )
    customer_id_hidden = forms.CharField(widget=forms.HiddenInput(), required=False) # To store the actual customer ID

    class Meta:
        model = SalesInvoice
        fields = [
            'buyer_name', 'buyer_add', 'buyer_country', 'buyer_state', 'buyer_city',
            'buyer_cotper', 'buyer_ph', 'buyer_email', 'buyer_ecc', 'buyer_tin',
            'buyer_mob', 'buyer_fax', 'buyer_vat'
        ]
        widgets = {
            'buyer_name': forms.HiddenInput(), # Use the selected_name field for display/input
            'buyer_add': forms.Textarea(attrs={'class': 'box3', 'rows': 4, 'cols': 40}),
            'buyer_country': forms.Select(attrs={'class': 'box3', 'hx-get': '/sales_invoice/get_states/', 'hx-target': '#buyer-state-select', 'hx-swap': 'outerHTML'}),
            'buyer_state': forms.Select(attrs={'class': 'box3', 'id': 'buyer-state-select', 'hx-get': '/sales_invoice/get_cities/', 'hx-target': '#buyer-city-select', 'hx-swap': 'outerHTML'}),
            'buyer_city': forms.Select(attrs={'class': 'box3', 'id': 'buyer-city-select'}),
            'buyer_cotper': forms.TextInput(attrs={'class': 'box3'}),
            'buyer_ph': forms.TextInput(attrs={'class': 'box3'}),
            'buyer_email': forms.EmailInput(attrs={'class': 'box3'}),
            'buyer_ecc': forms.TextInput(attrs={'class': 'box3'}),
            'buyer_tin': forms.TextInput(attrs={'class': 'box3'}), # Maps to Buyer_tin in DB (TinCstNo from customer)
            'buyer_mob': forms.TextInput(attrs={'class': 'box3'}),
            'buyer_fax': forms.TextInput(attrs={'class': 'box3'}),
            'buyer_vat': forms.TextInput(attrs={'class': 'box3'}), # Maps to Buyer_vat in DB (TinVatNo from customer)
        }
        labels = {
            'buyer_add': 'Address',
            'buyer_country': 'Country',
            'buyer_state': 'State',
            'buyer_city': 'City',
            'buyer_cotper': 'Contact Person',
            'buyer_ph': 'Phone No.',
            'buyer_email': 'E-mail',
            'buyer_ecc': 'Customer\'s ECC.No.',
            'buyer_tin': 'TIN / CST No.',
            'buyer_mob': 'Mobile No.',
            'buyer_fax': 'Fax No.',
            'buyer_vat': 'TIN/VAT No',
        }
        error_messages = {
            'buyer_name': {'required': '*'},
            'buyer_add': {'required': '*'},
            'buyer_cotper': {'required': '*'},
            'buyer_ph': {'required': '*'},
            'buyer_email': {'required': '*'},
            'buyer_mob': {'required': '*'},
            'buyer_fax': {'required': '*'},
            'buyer_vat': {'required': '*'},
            'buyer_ecc': {'required': '*'},
            'buyer_tin': {'required': '*'},
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['buyer_country'].queryset = Country.objects.all()
        self.fields['buyer_state'].queryset = State.objects.none()
        self.fields['buyer_city'].queryset = City.objects.none()

        if self.instance.pk:
            if self.instance.buyer_country:
                self.fields['buyer_state'].queryset = State.objects.filter(country=self.instance.buyer_country)
            if self.instance.buyer_state:
                self.fields['buyer_city'].queryset = City.objects.filter(state=self.instance.buyer_state)
            self.fields['buyer_name_selected'].initial = self.instance.buyer_name
            self.fields['customer_id_hidden'].initial = self.instance.customer_code.customer_id if self.instance.customer_code else ''
        
        # Adjusting the main 'buyer_name' field to be non-required for base model form validation
        self.fields['buyer_name'].required = False


class SalesInvoiceConsigneeForm(forms.ModelForm):
    cong_name_search = forms.CharField(
        label="Name",
        widget=forms.TextInput(attrs={'class': 'box3', 'hx-get': '/sales_invoice/autocomplete_customer/', 'hx-trigger': 'keyup changed delay:500ms', 'hx-target': '#consignee-name-suggestions', 'autocomplete': 'off'}),
        help_text='<div id="consignee-name-suggestions" class="autocomplete-list"></div>',
        required=False
    )
    cong_name_selected = forms.CharField(
        label="Name", # This will be the actual name to save
        widget=forms.TextInput(attrs={'class': 'box3', 'readonly': 'readonly'}),
        required=True,
        error_messages={'required': '*'}
    )
    customer_id_hidden = forms.CharField(widget=forms.HiddenInput(), required=False) # To store the actual customer ID

    class Meta:
        model = SalesInvoice
        fields = [
            'cong_name', 'cong_add', 'cong_country', 'cong_state', 'cong_city',
            'cong_cotper', 'cong_ph', 'cong_email', 'cong_ecc', 'cong_tin',
            'cong_mob', 'cong_fax', 'cong_vat'
        ]
        widgets = {
            'cong_name': forms.HiddenInput(), # Use the selected_name field for display/input
            'cong_add': forms.Textarea(attrs={'class': 'box3', 'rows': 4, 'cols': 40}),
            'cong_country': forms.Select(attrs={'class': 'box3', 'hx-get': '/sales_invoice/get_states/', 'hx-target': '#consignee-state-select', 'hx-swap': 'outerHTML'}),
            'cong_state': forms.Select(attrs={'class': 'box3', 'id': 'consignee-state-select', 'hx-get': '/sales_invoice/get_cities/', 'hx-target': '#consignee-city-select', 'hx-swap': 'outerHTML'}),
            'cong_city': forms.Select(attrs={'class': 'box3', 'id': 'consignee-city-select'}),
            'cong_cotper': forms.TextInput(attrs={'class': 'box3'}),
            'cong_ph': forms.TextInput(attrs={'class': 'box3'}),
            'cong_email': forms.EmailInput(attrs={'class': 'box3'}),
            'cong_ecc': forms.TextInput(attrs={'class': 'box3'}),
            'cong_tin': forms.TextInput(attrs={'class': 'box3'}), # Maps to Cong_tin in DB (TinCstNo from customer)
            'cong_mob': forms.TextInput(attrs={'class': 'box3'}),
            'cong_fax': forms.TextInput(attrs={'class': 'box3'}),
            'cong_vat': forms.TextInput(attrs={'class': 'box3'}), # Maps to Cong_vat in DB (TinVatNo from customer)
        }
        labels = {
            'cong_add': 'Address',
            'cong_country': 'Country',
            'cong_state': 'State',
            'cong_city': 'City',
            'cong_cotper': 'Contact Person',
            'cong_ph': 'Phone No.',
            'cong_email': 'E-mail',
            'cong_ecc': 'Customer\'s ECC.No.',
            'cong_tin': 'TIN / CST No.',
            'cong_mob': 'Mobile No.',
            'cong_fax': 'Fax No.',
            'cong_vat': 'TIN/VAT No.',
        }
        error_messages = {
            'cong_name': {'required': '*'},
            'cong_add': {'required': '*'},
            'cong_cotper': {'required': '*'},
            'cong_ph': {'required': '*'},
            'cong_email': {'required': '*'},
            'cong_mob': {'required': '*'},
            'cong_fax': {'required': '*'},
            'cong_vat': {'required': '*'},
            'cong_ecc': {'required': '*'},
            'cong_tin': {'required': '*'},
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['cong_country'].queryset = Country.objects.all()
        self.fields['cong_state'].queryset = State.objects.none()
        self.fields['cong_city'].queryset = City.objects.none()

        if self.instance.pk:
            if self.instance.cong_country:
                self.fields['cong_state'].queryset = State.objects.filter(country=self.instance.cong_country)
            if self.instance.cong_state:
                self.fields['cong_city'].queryset = City.objects.filter(state=self.instance.cong_state)
            self.fields['cong_name_selected'].initial = self.instance.cong_name
            self.fields['customer_id_hidden'].initial = self.instance.customer_code.customer_id if self.instance.customer_code else '' # Assuming buyer and consignee can link to the same customer code
        
        # Adjusting the main 'cong_name' field to be non-required for base model form validation
        self.fields['cong_name'].required = False

class SalesInvoiceDetailForm(forms.ModelForm):
    # These fields are pre-filled from PurchaseOrderDetail and not editable
    po_item_desc = forms.CharField(label="Description", widget=forms.TextInput(attrs={'readonly': 'readonly', 'class': 'box3'}), required=False)
    po_item_unit_symbol = forms.CharField(label="Unit", widget=forms.TextInput(attrs={'readonly': 'readonly', 'class': 'box3'}), required=False)
    po_item_total_qty = forms.FloatField(label="Qty", widget=forms.NumberInput(attrs={'readonly': 'readonly', 'class': 'box3'}), required=False)
    po_item_rate = forms.FloatField(label="Rate", widget=forms.NumberInput(attrs={'readonly': 'readonly', 'class': 'box3'}), required=False)
    po_item_remaining_qty = forms.FloatField(label="Remn Qty", widget=forms.NumberInput(attrs={'readonly': 'readonly', 'class': 'box3'}), required=False)
    
    # Checkbox for selection, similar to GridView's 'ck'
    select_item = forms.BooleanField(label="", required=False, widget=forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded'}))

    class Meta:
        model = SalesInvoiceDetail
        fields = ['select_item', 'req_qty', 'unit', 'amt_in_per', 'item_id', 'qty', 'rate']
        exclude = ['invoice', 'invoice_no', 'mid'] # Exclude fields populated by master invoice
        widgets = {
            'item_id': forms.HiddenInput(), # Hidden to store the PO_Detail ID
            'qty': forms.HiddenInput(), # Hidden to store original PO Qty
            'rate': forms.HiddenInput(), # Hidden to store original PO Rate
            'req_qty': forms.NumberInput(attrs={'class': 'box3', 'min': 0, 'step': '0.001'}),
            'unit': forms.Select(attrs={'class': 'box3'}),
            'amt_in_per': forms.NumberInput(attrs={'class': 'box3', 'min': 0, 'max': 100, 'step': '0.01'}),
        }
        labels = {
            'req_qty': 'Req Qty',
            'unit': 'Unit Of Qty',
            'amt_in_per': 'Amt in (%)',
        }
        error_messages = {
            'req_qty': {'required': '*'},
            'amt_in_per': {'required': '*'},
        }

    def __init__(self, *args, **kwargs):
        po_detail_instance = kwargs.pop('po_detail_instance', None)
        comp_id = kwargs.pop('comp_id', None)
        super().__init__(*args, **kwargs)
        
        self.fields['unit'].queryset = Unit.objects.all().order_by('id')

        if po_detail_instance:
            self.fields['po_item_desc'].initial = po_detail_instance.item_desc
            self.fields['po_item_unit_symbol'].initial = po_detail_instance.unit.symbol
            self.fields['po_item_total_qty'].initial = po_detail_instance.total_qty
            self.fields['po_item_rate'].initial = po_detail_instance.rate
            self.fields['po_item_remaining_qty'].initial = po_detail_instance.get_remaining_qty(comp_id)

            self.fields['item_id'].initial = po_detail_instance.id
            self.fields['qty'].initial = po_detail_instance.total_qty
            self.fields['rate'].initial = po_detail_instance.rate
            
            # Prefill unit with PO unit if not already set
            if not self.instance.unit_id:
                self.fields['unit'].initial = po_detail_instance.unit_id

            # Initially disable fields if remaining_qty is 0
            if po_detail_instance.get_remaining_qty(comp_id) <= 0 and not self.instance.pk: # If it's a new invoice and no remaining qty
                self.fields['select_item'].widget.attrs['disabled'] = 'disabled'
                self.fields['req_qty'].widget.attrs['disabled'] = 'disabled'
                self.fields['unit'].widget.attrs['disabled'] = 'disabled'
                self.fields['amt_in_per'].widget.attrs['disabled'] = 'disabled'
                self.fields['select_item'].initial = False
                
        # Make required fields conditionally required based on checkbox (handled in clean_formset)
        for field_name in ['req_qty', 'amt_in_per']:
            self.fields[field_name].required = False
            self.fields[field_name].widget.attrs['hx-indicator'] = '#loading-indicator' # Example indicator

    def clean(self):
        cleaned_data = super().clean()
        select_item = cleaned_data.get('select_item')
        req_qty = cleaned_data.get('req_qty')
        amt_in_per = cleaned_data.get('amt_in_per')

        if select_item:
            if not req_qty:
                self.add_error('req_qty', '*')
            if not amt_in_per:
                self.add_error('amt_in_per', '*')
            
            # This validation (qty > remaining_qty) will be done in the model's save_full_invoice
            # However, a client-side warning or a "soft" validation here could be useful.
            # po_item_remaining_qty = cleaned_data.get('po_item_remaining_qty') # This is from initial, not live
            # if req_qty is not None and po_item_remaining_qty is not None and req_qty > po_item_remaining_qty:
            #     self.add_error('req_qty', 'Required quantity exceeds remaining quantity.')

        return cleaned_data

SalesInvoiceDetailFormSet = inlineformset_factory(
    SalesInvoice, SalesInvoiceDetail, form=SalesInvoiceDetailForm,
    fields=['select_item', 'req_qty', 'unit', 'amt_in_per', 'item_id', 'qty', 'rate'],
    extra=0, can_delete=False
)

```

#### 4.3 Views (`sales_invoices/views.py`)

The core logic for handling the multi-tab form will be managed in a `SalesInvoiceCreateView`. HTMX will be used for partial updates, autocomplete, and cascading dropdowns.

```python
from django.views.generic import View
from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse, Http404
from django.db import transaction
from django.utils import timezone
import json # For HTMX autocomplete/search responses

from .models import (
    SalesInvoice, SalesInvoiceDetail, Customer, PurchaseOrder, PurchaseOrderDetail,
    Country, State, City, ExciseCommodity, SalesInvoiceType, ExciseServiceMaster
)
from .forms import (
    SalesInvoiceMainForm, SalesInvoiceBuyerForm, SalesInvoiceConsigneeForm, SalesInvoiceDetailForm, SalesInvoiceDetailFormSet
)

# Helper function to get company/financial year from session/context
# In a real app, this would come from an authenticated user's profile or a global context.
def get_user_context(request):
    comp_id = int(request.session.get('compid', 1)) # Default to 1 for testing
    fin_year_id = int(request.session.get('finyear', 1)) # Default to 1 for testing
    session_id = request.session.get('username', 'system') # Default to 'system'
    return comp_id, fin_year_id, session_id

class SalesInvoiceCreateView(View):
    template_name = 'sales_invoices/sales_invoice_form.html'

    def get_initial_data(self, request):
        # Emulating ASP.NET's QueryString parameters and initial data setup
        # WN, PN, PId, CCode, typ, date come from previous page
        initial_data = {
            'po_no': request.GET.get('pn'),
            'wo_no': request.GET.get('wn'),
            'poid': request.GET.get('poid'),
            'customer_code_initial': request.GET.get('cid'),
            'invoice_type_id': request.GET.get('ty'),
            'po_date': request.GET.get('date'),
        }
        return initial_data

    def get_context_data(self, request, **kwargs):
        context = {}
        comp_id, fin_year_id, session_id = get_user_context(request)
        initial_data = self.get_initial_data(request)

        # Main Form
        if 'main_form' not in kwargs:
            invoice_no = SalesInvoice.generate_next_invoice_no(comp_id, fin_year_id)
            context['main_form'] = SalesInvoiceMainForm(initial={
                'invoice_no': invoice_no,
                'date_of_issue_invoice': timezone.now().date(),
                'date_of_removal': timezone.now().date(),
                'po_no': initial_data['po_no'],
                'wo_no': initial_data['wo_no'],
                'poid': initial_data['poid'],
                'customer_code_initial': initial_data['customer_code_initial'],
                'invoice_type_id': initial_data['invoice_type_id'],
                'po_date': initial_data['po_date'],
            })
        else:
            context['main_form'] = kwargs['main_form']

        # Buyer Form
        if 'buyer_form' not in kwargs:
            initial_buyer_data = {}
            if initial_data['customer_code_initial']:
                customer = Customer.get_customer_details(initial_data['customer_code_initial'], comp_id)
                if customer:
                    initial_buyer_data = {
                        'buyer_name_selected': customer.customer_name + ' [' + customer.customer_id + ']',
                        'customer_id_hidden': customer.customer_id,
                        'buyer_add': customer.material_del_address,
                        'buyer_country': customer.material_del_country,
                        'buyer_state': customer.material_del_state,
                        'buyer_city': customer.material_del_city,
                        'buyer_cotper': customer.contact_person,
                        'buyer_ph': customer.material_del_contact_no,
                        'buyer_email': customer.email,
                        'buyer_ecc': customer.ecc_no,
                        'buyer_tin': customer.tin_cst_no,
                        'buyer_mob': customer.contact_no,
                        'buyer_fax': customer.material_del_fax_no,
                        'buyer_vat': customer.tin_vat_no,
                    }
            context['buyer_form'] = SalesInvoiceBuyerForm(initial=initial_buyer_data)
        else:
            context['buyer_form'] = kwargs['buyer_form']

        # Consignee Form
        context['consignee_form'] = kwargs.get('consignee_form', SalesInvoiceConsigneeForm())

        # Goods Formset
        po_details = []
        if initial_data['poid']:
            po_details = PurchaseOrderDetail.objects.filter(poid=initial_data['poid'])
        
        # Prepare initial data for the formset from PO Details
        initial_formset_data = []
        for po_detail in po_details:
            initial_formset_data.append({
                'item_id': po_detail.id, # The ItemId from PO_Details
                'qty': po_detail.total_qty,
                'rate': po_detail.rate,
                'unit': po_detail.unit_id, # Default unit for the item
                # Other fields like req_qty, amt_in_per will be empty initially for new forms
            })
        
        context['goods_formset'] = SalesInvoiceDetailFormSet(
            request.POST if request.method == 'POST' and request.headers.get('HX-Request') and 'goods_form_submit' in request.POST else None,
            initial=initial_formset_data,
            form_kwargs={'comp_id': comp_id} # Pass comp_id to form for remaining qty calculation
        )
        context['po_details'] = po_details # For rendering initial goods data

        # Dropdown lists for display labels (from ASP.NET labels)
        context['invoice_date'] = timezone.now().strftime('%d-%m-%Y')
        context['po_number'] = initial_data['po_no']
        context['po_date'] = initial_data['po_date']
        context['wo_number'] = initial_data['wo_no']
        context['invoice_mode'] = SalesInvoiceType.objects.filter(id=initial_data['invoice_type_id']).first().description if initial_data['invoice_type_id'] else ''
        context['vat_label'] = "VAT" if initial_data['invoice_type_id'] == '2' else "CST" # '2' for Within Mh., '3' for Out of Mh.
        
        # Pre-select CENVAT if a default exists (mimicking ASP.NET logic)
        default_cenvat = ExciseServiceMaster.objects.filter(live=True).first()
        if default_cenvat:
            context['main_form'].fields['cenvat'].initial = default_cenvat.id
        
        return context

    def get(self, request, *args, **kwargs):
        # 5-15 lines view method example
        context = self.get_context_data(request)
        return render(request, self.template_name, context)

    def post(self, request, *args, **kwargs):
        # This handles the final submission of the entire form.
        # It aggregates data from all forms and saves it.
        comp_id, fin_year_id, session_id = get_user_context(request)
        initial_data = self.get_initial_data(request)

        main_form = SalesInvoiceMainForm(request.POST)
        buyer_form = SalesInvoiceBuyerForm(request.POST)
        consignee_form = SalesInvoiceConsigneeForm(request.POST)
        goods_formset = SalesInvoiceDetailFormSet(request.POST, form_kwargs={'comp_id': comp_id})

        forms_valid = all([
            main_form.is_valid(),
            buyer_form.is_valid(),
            consignee_form.is_valid(),
            goods_formset.is_valid(),
        ])

        if not forms_valid:
            # 5-15 lines view method example
            messages.error(request, 'Input data is invalid. Please check all tabs.')
            context = self.get_context_data(request, main_form=main_form, buyer_form=buyer_form, consignee_form=consignee_form)
            return render(request, self.template_name, context)

        # Prepare SalesInvoice instance
        invoice_instance = main_form.save(commit=False)
        invoice_instance.sys_date = timezone.now().date()
        invoice_instance.sys_time = timezone.now().time()
        invoice_instance.comp_id = comp_id
        invoice_instance.fin_year_id = fin_year_id
        invoice_instance.session_id = session_id
        invoice_instance.pono = initial_data['po_no']
        invoice_instance.wono = initial_data['wo_no']
        invoice_instance.invoice_mode_id = initial_data['invoice_type_id']
        invoice_instance.poid_id = initial_data['poid']

        # Populate Buyer details from Buyer Form
        buyer_customer_id = buyer_form.cleaned_data.get('customer_id_hidden')
        invoice_instance.customer_code = Customer.objects.get(customer_id=buyer_customer_id) if buyer_customer_id else None
        invoice_instance.buyer_name = buyer_form.cleaned_data['buyer_name_selected'].split(' [')[0] # Extract name from "Name [ID]"
        invoice_instance.buyer_add = buyer_form.cleaned_data['buyer_add']
        invoice_instance.buyer_country = buyer_form.cleaned_data['buyer_country']
        invoice_instance.buyer_state = buyer_form.cleaned_data['buyer_state']
        invoice_instance.buyer_city = buyer_form.cleaned_data['buyer_city']
        invoice_instance.buyer_cotper = buyer_form.cleaned_data['buyer_cotper']
        invoice_instance.buyer_ph = buyer_form.cleaned_data['buyer_ph']
        invoice_instance.buyer_email = buyer_form.cleaned_data['buyer_email']
        invoice_instance.buyer_ecc = buyer_form.cleaned_data['buyer_ecc']
        invoice_instance.buyer_tin = buyer_form.cleaned_data['buyer_tin']
        invoice_instance.buyer_mob = buyer_form.cleaned_data['buyer_mob']
        invoice_instance.buyer_fax = buyer_form.cleaned_data['buyer_fax']
        invoice_instance.buyer_vat = buyer_form.cleaned_data['buyer_vat']

        # Populate Consignee details from Consignee Form
        consignee_customer_id = consignee_form.cleaned_data.get('customer_id_hidden')
        invoice_instance.cong_name = consignee_form.cleaned_data['cong_name_selected'].split(' [')[0] if consignee_form.cleaned_data['cong_name_selected'] else ''
        invoice_instance.cong_add = consignee_form.cleaned_data['cong_add']
        invoice_instance.cong_country = consignee_form.cleaned_data['cong_country']
        invoice_instance.cong_state = consignee_form.cleaned_data['cong_state']
        invoice_instance.cong_city = consignee_form.cleaned_data['cong_city']
        invoice_instance.cong_cotper = consignee_form.cleaned_data['cong_cotper']
        invoice_instance.cong_ph = consignee_form.cleaned_data['cong_ph']
        invoice_instance.cong_email = consignee_form.cleaned_data['cong_email']
        invoice_instance.cong_ecc = consignee_form.cleaned_data['cong_ecc']
        invoice_instance.cong_tin = consignee_form.cleaned_data['cong_tin']
        invoice_instance.cong_mob = consignee_form.cleaned_data['cong_mob']
        invoice_instance.cong_fax = consignee_form.cleaned_data['cong_fax']
        invoice_instance.cong_vat = consignee_form.cleaned_data['cong_vat']

        # Prepare SalesInvoiceDetail instances
        selected_goods_details = []
        for form in goods_formset:
            if form.cleaned_data.get('select_item'):
                po_detail_id = form.cleaned_data['item_id']
                # Retrieve actual PO Detail instance for validation in model
                po_detail_instance = PurchaseOrderDetail.objects.get(id=po_detail_id)
                selected_goods_details.append({
                    'item_id': po_detail_instance,
                    'unit': form.cleaned_data['unit'],
                    'qty': form.cleaned_data['qty'], # Original PO Total Qty
                    'req_qty': form.cleaned_data['req_qty'],
                    'amt_in_per': form.cleaned_data['amt_in_per'],
                    'rate': form.cleaned_data['rate'],
                })

        if not selected_goods_details:
            # 5-15 lines view method example
            messages.error(request, 'At least one item must be selected in Goods tab.')
            context = self.get_context_data(request, main_form=main_form, buyer_form=buyer_form, consignee_form=consignee_form, goods_formset=goods_formset)
            return render(request, self.template_name, context)

        try:
            invoice_instance.save_full_invoice(selected_goods_details)
            messages.success(request, 'Sales Invoice created successfully.')
            return redirect(reverse_lazy('sales_invoice_list_all')) # Redirect to a list page
        except ValueError as e:
            # 5-15 lines view method example
            messages.error(request, f'Failed to create invoice: {e}')
            context = self.get_context_data(request, main_form=main_form, buyer_form=buyer_form, consignee_form=consignee_form, goods_formset=goods_formset)
            return render(request, self.template_name, context)


# HTMX Endpoints

# Handles the "Goods" tab content, especially for initial load or refresh after formset submission
class SalesInvoiceGoodsTabPartialView(View):
    template_name = 'sales_invoices/_goods_tab.html'

    def get(self, request, *args, **kwargs):
        comp_id, _, _ = get_user_context(request)
        poid = request.GET.get('poid') # Passed from main form as a hidden input
        
        po_details = []
        if poid:
            po_details = PurchaseOrderDetail.objects.filter(poid=poid)
        
        initial_formset_data = []
        for po_detail in po_details:
            initial_formset_data.append({
                'item_id': po_detail.id,
                'qty': po_detail.total_qty,
                'rate': po_detail.rate,
                'unit': po_detail.unit_id,
            })

        goods_formset = SalesInvoiceDetailFormSet(
            initial=initial_formset_data,
            form_kwargs={'comp_id': comp_id}
        )
        context = {
            'goods_formset': goods_formset,
            'po_details': po_details,
        }
        return render(request, self.template_name, context)


class CustomerAutocomplete(View):
    def get(self, request, *args, **kwargs):
        comp_id, _, _ = get_user_context(request)
        query = request.GET.get('q', '').strip()
        suggestions = []
        if query:
            suggestions = Customer.search_by_name(query, comp_id)
        # Return HTML for autocomplete suggestions
        html_suggestions = ''.join([f'<div hx-on:click="selectCustomer(this.textContent)">{s}</div>' for s in suggestions])
        return HttpResponse(html_suggestions)

class CustomerDetailsLoad(View):
    def get(self, request, *args, **kwargs):
        comp_id, _, _ = get_user_context(request)
        customer_id_with_name = request.GET.get('customer_id_with_name', '').strip()
        customer_code = ''
        if ' [' in customer_id_with_name and ']' in customer_id_with_name:
            customer_code = customer_id_with_name.split(' [')[-1][:-1]
        
        customer_details = Customer.get_customer_details(customer_code, comp_id)
        
        if customer_details:
            response_data = {
                'address': customer_details.material_del_address,
                'country_id': customer_details.material_del_country_id,
                'state_id': customer_details.material_del_state_id,
                'city_id': customer_details.material_del_city_id,
                'contact_person': customer_details.contact_person,
                'phone_no': customer_details.material_del_contact_no,
                'mobile_no': customer_details.contact_no,
                'email': customer_details.email,
                'fax_no': customer_details.material_del_fax_no,
                'tin_vat_no': customer_details.tin_vat_no,
                'ecc_no': customer_details.ecc_no,
                'tin_cst_no': customer_details.tin_cst_no,
            }
            return JsonResponse(response_data)
        return JsonResponse({'error': 'Customer not found'}, status=404)

class GetStatesPartialView(View):
    def get(self, request, *args, **kwargs):
        country_id = request.GET.get('country_id')
        try:
            states = State.objects.filter(country_id=country_id).order_by('name')
        except ValueError:
            states = State.objects.none()
        
        # Determine target select box (buyer/consignee)
        target_id = request.GET.get('target_id', 'buyer-state-select')

        html = f'<select id="{target_id}" name="{target_id}" class="box3" hx-get="/sales_invoice/get_cities/" hx-target="#{target_id.replace("state", "city")}" hx-swap="outerHTML">'
        html += '<option value="">--- Select ---</option>'
        for state in states:
            html += f'<option value="{state.id}">{state.name}</option>'
        html += '</select>'
        return HttpResponse(html)

class GetCitiesPartialView(View):
    def get(self, request, *args, **kwargs):
        state_id = request.GET.get('state_id')
        try:
            cities = City.objects.filter(state_id=state_id).order_by('name')
        except ValueError:
            cities = City.objects.none()

        # Determine target select box (buyer/consignee)
        target_id = request.GET.get('target_id', 'buyer-city-select') # Default based on state select ID

        html = f'<select id="{target_id}" name="{target_id}" class="box3">'
        html += '<option value="">--- Select ---</option>'
        for city in cities:
            html += f'<option value="{city.id}">{city.name}</option>'
        html += '</select>'
        return HttpResponse(html)

class GetTariffHeadPartialView(View):
    def get(self, request, *args, **kwargs):
        commodity_id = request.GET.get('commodity_id')
        tariff_head = ""
        if commodity_id:
            try:
                tariff_head = ExciseCommodity.get_tariff_head(int(commodity_id))
            except ValueError:
                pass
        return HttpResponse(tariff_head) # Return plain text for the label
        
# Placeholder for a list view after successful creation
class SalesInvoiceListView(View):
    def get(self, request, *args, **kwargs):
        # This is just a placeholder to demonstrate success_url redirect
        return HttpResponse("<h1>Sales Invoices List (Placeholder)</h1><p>Invoice created successfully!</p>")

```

#### 4.4 Templates (`sales_invoices/templates/sales_invoices/`)

```html
<!-- sales_invoices/templates/sales_invoices/sales_invoice_form.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ 
    activeTab: 'main', 
    setActiveTab(tab) { this.activeTab = tab },
    populateBuyerForm(data) {
        document.getElementById('id_buyer_name_selected').value = data.name;
        document.getElementById('id_customer_id_hidden').value = data.id;
        document.getElementById('id_buyer_add').value = data.address;
        
        // Dynamically update country/state/city dropdowns
        if(data.country_id) {
            document.getElementById('id_buyer_country').value = data.country_id;
            // Trigger HTMX to load states
            htmx.trigger(document.getElementById('id_buyer_country'), 'change');
        }
        if(data.state_id) {
            // Once states loaded, Alpine.js might need to wait for HTMX swap to complete
            // Or just set the value on htmx.onLoad (simpler for this example)
            setTimeout(() => { // Small delay to ensure HTMX response has updated DOM
                document.getElementById('buyer-state-select').value = data.state_id;
                htmx.trigger(document.getElementById('buyer-state-select'), 'change');
            }, 100);
        }
        if(data.city_id) {
             setTimeout(() => {
                document.getElementById('buyer-city-select').value = data.city_id;
            }, 200);
        }
        
        document.getElementById('id_buyer_cotper').value = data.contact_person;
        document.getElementById('id_buyer_ph').value = data.phone_no;
        document.getElementById('id_buyer_email').value = data.email;
        document.getElementById('id_buyer_fax').value = data.fax_no;
        document.getElementById('id_buyer_mob').value = data.mobile_no;
        document.getElementById('id_buyer_vat').value = data.tin_vat_no;
        document.getElementById('id_buyer_ecc').value = data.ecc_no;
        document.getElementById('id_buyer_tin').value = data.tin_cst_no;
    },
    populateConsigneeForm(data) {
        document.getElementById('id_cong_name_selected').value = data.name;
        document.getElementById('id_consignee_customer_id_hidden').value = data.id;
        document.getElementById('id_cong_add').value = data.address;
        
        if(data.country_id) {
            document.getElementById('id_cong_country').value = data.country_id;
            htmx.trigger(document.getElementById('id_cong_country'), 'change');
        }
        if(data.state_id) {
            setTimeout(() => {
                document.getElementById('consignee-state-select').value = data.state_id;
                htmx.trigger(document.getElementById('consignee-state-select'), 'change');
            }, 100);
        }
        if(data.city_id) {
             setTimeout(() => {
                document.getElementById('consignee-city-select').value = data.city_id;
            }, 200);
        }

        document.getElementById('id_cong_cotper').value = data.contact_person;
        document.getElementById('id_cong_ph').value = data.phone_no;
        document.getElementById('id_cong_email').value = data.email;
        document.getElementById('id_cong_fax').value = data.fax_no;
        document.getElementById('id_cong_mob').value = data.mobile_no;
        document.getElementById('id_cong_vat').value = data.tin_vat_no;
        document.getElementById('id_cong_ecc').value = data.ecc_no;
        document.getElementById('id_cong_tin').value = data.tin_cst_no;
    },
    selectCustomer(nameWithId, formType) {
        const [name, id] = nameWithId.split(' [');
        const customerId = id.slice(0, -1); // Remove trailing ']'
        
        let targetNameFieldId = `id_${formType}_name_selected`;
        let targetHiddenIdFieldId = `id_${formType}_customer_id_hidden`;
        
        document.getElementById(targetNameFieldId).value = nameWithId;
        document.getElementById(targetHiddenIdFieldId).value = customerId;

        // Trigger HTMX call to fetch full details
        htmx.ajax('GET', '/sales_invoice/get_customer_details/?customer_id_with_name=' + encodeURIComponent(nameWithId), {
            onSuccess: function(evt) {
                const data = JSON.parse(evt.detail.xhr.responseText);
                if (formType === 'buyer') {
                    // Update the visible form fields
                    document.getElementById('id_buyer_add').value = data.address;
                    document.getElementById('id_buyer_country').value = data.country_id;
                    htmx.trigger(document.getElementById('id_buyer_country'), 'change');
                    setTimeout(() => {
                        document.getElementById('buyer-state-select').value = data.state_id;
                        htmx.trigger(document.getElementById('buyer-state-select'), 'change');
                    }, 100);
                    setTimeout(() => {
                        document.getElementById('buyer-city-select').value = data.city_id;
                    }, 200);
                    document.getElementById('id_buyer_cotper').value = data.contact_person;
                    document.getElementById('id_buyer_ph').value = data.phone_no;
                    document.getElementById('id_buyer_email').value = data.email;
                    document.getElementById('id_buyer_fax').value = data.fax_no;
                    document.getElementById('id_buyer_mob').value = data.mobile_no;
                    document.getElementById('id_buyer_vat').value = data.tin_vat_no;
                    document.getElementById('id_buyer_ecc').value = data.ecc_no;
                    document.getElementById('id_buyer_tin').value = data.tin_cst_no;
                } else if (formType === 'cong') {
                    // Update consignee fields similarly
                    document.getElementById('id_cong_add').value = data.address;
                    document.getElementById('id_cong_country').value = data.country_id;
                    htmx.trigger(document.getElementById('id_cong_country'), 'change');
                    setTimeout(() => {
                        document.getElementById('consignee-state-select').value = data.state_id;
                        htmx.trigger(document.getElementById('consignee-state-select'), 'change');
                    }, 100);
                    setTimeout(() => {
                        document.getElementById('consignee-city-select').value = data.city_id;
                    }, 200);
                    document.getElementById('id_cong_cotper').value = data.contact_person;
                    document.getElementById('id_cong_ph').value = data.phone_no;
                    document.getElementById('id_cong_email').value = data.email;
                    document.getElementById('id_cong_fax').value = data.fax_no;
                    document.getElementById('id_cong_mob').value = data.mobile_no;
                    document.getElementById('id_cong_vat').value = data.tin_vat_no;
                    document.getElementById('id_cong_ecc').value = data.ecc_no;
                    document.getElementById('id_cong_tin').value = data.tin_cst_no;
                }
            }
        });
        // Clear suggestions list
        document.getElementById(`${formType}-name-suggestions`).innerHTML = '';
    }
}"
x-init="
    // Initialize activeTab based on session or first tab
    {% if messages %}
        // If there are form errors, try to keep the active tab where the error occurred
        // This requires more sophisticated error handling to identify which tab has errors
        // For simplicity, we'll revert to default here or stay on first tab.
    {% endif %}
    // If a tab is passed in query string, activate it
    const urlParams = new URLSearchParams(window.location.search);
    const tabFromUrl = urlParams.get('tab');
    if (tabFromUrl) activeTab = tabFromUrl;
"
>
    <div class="mb-6">
        <h2 class="text-3xl font-bold text-gray-800 mb-2">Sales Invoice - New</h2>
        <div class="bg-gradient-to-r from-blue-500 to-indigo-600 h-1 w-24 rounded-full"></div>
    </div>

    <!-- Top Invoice Details Section -->
    <div class="bg-white p-6 rounded-lg shadow-md mb-8">
        <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4 text-sm">
            <div>
                <span class="font-semibold text-gray-700">Invoice No.:</span>
                <span class="text-gray-900">{{ main_form.invoice_no.value }}</span>
            </div>
            <div>
                <span class="font-semibold text-gray-700">Date:</span>
                <span class="text-gray-900">{{ invoice_date }}</span>
            </div>
            <div>
                <span class="font-semibold text-gray-700">Mode of Invoice:</span>
                <span class="text-gray-900">{{ invoice_mode }}</span>
            </div>
            <div>
                <span class="font-semibold text-gray-700">PO No.:</span>
                <span class="text-gray-900">{{ po_number }}</span>
            </div>
            <div>
                <span class="font-semibold text-gray-700">PO Date:</span>
                <span class="text-gray-900">{{ po_date|date:"d-m-Y" }}</span>
            </div>
            <div>
                <span class="font-semibold text-gray-700">WO No.:</span>
                <span class="text-gray-900">{{ wo_number }}</span>
            </div>
            <!-- Hidden fields to pass context to POST -->
            {{ main_form.po_no }}
            {{ main_form.wo_no }}
            {{ main_form.poid }}
            {{ main_form.customer_code_initial }}
            {{ main_form.invoice_type_id }}
            {{ main_form.po_date }}
        </div>
    </div>

    <!-- Tab Navigation -->
    <div class="flex border-b border-gray-200 mb-6">
        <button @click="setActiveTab('main')" :class="{ 'border-indigo-500 text-indigo-600': activeTab === 'main', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'main' }" class="py-2 px-4 border-b-2 font-medium text-sm focus:outline-none">General</button>
        <button @click="setActiveTab('buyer')" :class="{ 'border-indigo-500 text-indigo-600': activeTab === 'buyer', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'buyer' }" class="py-2 px-4 border-b-2 font-medium text-sm focus:outline-none">Buyer</button>
        <button @click="setActiveTab('consignee')" :class="{ 'border-indigo-500 text-indigo-600': activeTab === 'consignee', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'consignee' }" class="py-2 px-4 border-b-2 font-medium text-sm focus:outline-none">Consignee</button>
        <button @click="setActiveTab('goods')" :class="{ 'border-indigo-500 text-indigo-600': activeTab === 'goods', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'goods' }" class="py-2 px-4 border-b-2 font-medium text-sm focus:outline-none">Goods</button>
        <button @click="setActiveTab('taxation')" :class="{ 'border-indigo-500 text-indigo-600': activeTab === 'taxation', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'taxation' }" class="py-2 px-4 border-b-2 font-medium text-sm focus:outline-none">Taxation</button>
    </div>

    <!-- Main Form Container for all tabs -->
    <form method="POST" hx-post="{% url 'sales_invoice_create' %}" hx-swap="outerHTML show:top" hx-target="body" enctype="multipart/form-data">
        {% csrf_token %}
        
        <!-- General Tab -->
        <div x-show="activeTab === 'main'" class="bg-white p-6 rounded-lg shadow-md">
            {% include 'sales_invoices/_main_tab.html' %}
            <div class="mt-6 flex justify-end">
                <button type="button" @click="setActiveTab('buyer')" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded redbox">Next</button>
            </div>
        </div>

        <!-- Buyer Tab -->
        <div x-show="activeTab === 'buyer'" class="bg-white p-6 rounded-lg shadow-md">
            {% include 'sales_invoices/_buyer_tab.html' %}
            <div class="mt-6 flex justify-end space-x-4">
                <button type="button" @click="setActiveTab('consignee')" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded redbox">Next</button>
                <button type="button" @click="window.location.href='{% url 'sales_invoice_list_all' %}'" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded redbox">Cancel</button>
            </div>
        </div>

        <!-- Consignee Tab -->
        <div x-show="activeTab === 'consignee'" class="bg-white p-6 rounded-lg shadow-md">
            {% include 'sales_invoices/_consignee_tab.html' %}
            <div class="mt-6 flex justify-end space-x-4">
                <button type="button" @click="setActiveTab('goods')" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded redbox">Next</button>
                <button type="button" @click="window.location.href='{% url 'sales_invoice_list_all' %}'" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded redbox">Cancel</button>
            </div>
        </div>

        <!-- Goods Tab -->
        <div x-show="activeTab === 'goods'" class="bg-white p-6 rounded-lg shadow-md">
            {% include 'sales_invoices/_goods_tab.html' %}
            <div class="mt-6 flex justify-end space-x-4">
                <button type="button" @click="setActiveTab('taxation')" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded redbox">Next</button>
                <button type="button" @click="window.location.href='{% url 'sales_invoice_list_all' %}'" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded redbox">Cancel</button>
            </div>
        </div>

        <!-- Taxation Tab -->
        <div x-show="activeTab === 'taxation'" class="bg-white p-6 rounded-lg shadow-md">
            {% include 'sales_invoices/_taxation_tab.html' %}
            <div class="mt-6 flex justify-end space-x-4">
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded redbox">Submit</button>
                <button type="button" @click="window.location.href='{% url 'sales_invoice_list_all' %}'" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded redbox">Cancel</button>
            </div>
        </div>

    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is already configured in base.html.
    // Additional JS for DataTables
    document.addEventListener('htmx:afterSwap', function (event) {
        // Re-initialize DataTables if the swapped content contains a table with the ID
        if (event.target.id === 'goodsTableContainer' && $(`#goodsTable`).length) {
            $('#goodsTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
            });
        }
    });

    // Helper for customer autocomplete selection
    htmx.onLoad(function(content) {
        content.querySelectorAll('[hx-on\\:click]').forEach(el => {
            el.addEventListener('click', function() {
                // The selectCustomer function is defined in the parent Alpine.js context
                // This triggers the Alpine.js function when an HTMX-loaded suggestion is clicked.
                eval(this.getAttribute('hx-on:click')); 
            });
        });
    });

    // Event listener for populating Buyer from selected customer
    document.getElementById('id_buyer_name_search').addEventListener('input', function() {
        const selectedValue = document.getElementById('id_buyer_name_selected').value;
        if (selectedValue === '') {
            // Clear other fields if search box is cleared and selection is gone
            document.getElementById('id_customer_id_hidden').value = '';
            document.getElementById('id_buyer_add').value = '';
            document.getElementById('id_buyer_country').value = '';
            document.getElementById('buyer-state-select').innerHTML = '<option value="">--- Select ---</option>';
            document.getElementById('buyer-city-select').innerHTML = '<option value="">--- Select ---</option>';
            document.getElementById('id_buyer_cotper').value = '';
            document.getElementById('id_buyer_ph').value = '';
            document.getElementById('id_buyer_email').value = '';
            document.getElementById('id_buyer_fax').value = '';
            document.getElementById('id_buyer_mob').value = '';
            document.getElementById('id_buyer_vat').value = '';
            document.getElementById('id_buyer_ecc').value = '';
            document.getElementById('id_buyer_tin').value = '';
        }
    });

    // Event listener for populating Consignee from selected customer
    document.getElementById('id_cong_name_search').addEventListener('input', function() {
        const selectedValue = document.getElementById('id_cong_name_selected').value;
        if (selectedValue === '') {
            // Clear other fields if search box is cleared and selection is gone
            document.getElementById('id_consignee_customer_id_hidden').value = '';
            document.getElementById('id_cong_add').value = '';
            document.getElementById('id_cong_country').value = '';
            document.getElementById('consignee-state-select').innerHTML = '<option value="">--- Select ---</option>';
            document.getElementById('consignee-city-select').innerHTML = '<option value="">--- Select ---</option>';
            document.getElementById('id_cong_cotper').value = '';
            document.getElementById('id_cong_ph').value = '';
            document.getElementById('id_cong_email').value = '';
            document.getElementById('id_cong_fax').value = '';
            document.getElementById('id_cong_mob').value = '';
            document.getElementById('id_cong_vat').value = '';
            document.getElementById('id_cong_ecc').value = '';
            document.getElementById('id_cong_tin').value = '';
        }
    });

</script>
{% endblock %}

```

```html
<!-- sales_invoices/templates/sales_invoices/_main_tab.html -->
<h3 class="text-lg font-medium text-gray-900 mb-5">General Invoice Details</h3>
<div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
    <!-- Invoice No. (Readonly) -->
    <div class="field-group">
        <label for="{{ main_form.invoice_no.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ main_form.invoice_no.label }}
        </label>
        {{ main_form.invoice_no }}
        {% if main_form.invoice_no.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.invoice_no.errors }}</p>{% endif %}
    </div>

    <!-- Date Of Issue Of Invoice -->
    <div class="field-group">
        <label for="{{ main_form.date_of_issue_invoice.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ main_form.date_of_issue_invoice.label }}
        </label>
        {{ main_form.date_of_issue_invoice }}
        {% if main_form.date_of_issue_invoice.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.date_of_issue_invoice.errors }}</p>{% endif %}
    </div>

    <!-- Time Of Issue Of Invoice -->
    <div class="field-group">
        <label for="{{ main_form.time_of_issue_invoice.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ main_form.time_of_issue_invoice.label }}
        </label>
        {{ main_form.time_of_issue_invoice }}
        {% if main_form.time_of_issue_invoice.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.time_of_issue_invoice.errors }}</p>{% endif %}
    </div>

    <!-- Nature Of Removal -->
    <div class="field-group">
        <label for="{{ main_form.nature_of_removal.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ main_form.nature_of_removal.label }}
        </label>
        {{ main_form.nature_of_removal }}
        {% if main_form.nature_of_removal.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.nature_of_removal.errors }}</p>{% endif %}
    </div>

    <!-- Excisable Commodity -->
    <div class="field-group">
        <label for="{{ main_form.commodity.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ main_form.commodity.label }}
        </label>
        {{ main_form.commodity }}
        {% if main_form.commodity.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.commodity.errors }}</p>{% endif %}
    </div>

    <!-- Tariff Head No/ Exemption Notif. No. (dynamic) -->
    <div class="field-group">
        <label class="block text-sm font-medium text-gray-700">
            Tariff Head No/ Exemption Notif. No.
        </label>
        <span id="tariffHead" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-100 text-gray-800 sm:text-sm">
            {{ main_form.commodity.value|default:''|htmx_get_tariff_head }}
        </span>
    </div>

    <!-- Rate of Duty -->
    <div class="field-group">
        <label for="{{ main_form.duty_rate.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ main_form.duty_rate.label }}
        </label>
        {{ main_form.duty_rate }}
        {% if main_form.duty_rate.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.duty_rate.errors }}</p>{% endif %}
    </div>

    <!-- Mode of Transport -->
    <div class="field-group">
        <label for="{{ main_form.mode_of_transport.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ main_form.mode_of_transport.label }}
        </label>
        {{ main_form.mode_of_transport }}
        {% if main_form.mode_of_transport.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.mode_of_transport.errors }}</p>{% endif %}
    </div>

    <!-- R.R.G.C. No -->
    <div class="field-group">
        <label for="{{ main_form.rrgc_no.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ main_form.rrgc_no.label }}
        </label>
        {{ main_form.rrgc_no }}
        {% if main_form.rrgc_no.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.rrgc_no.errors }}</p>{% endif %}
    </div>

    <!-- If by motor vehicle, it's regist. number -->
    <div class="field-group">
        <label for="{{ main_form.vehi_reg_no.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ main_form.vehi_reg_no.label }}
        </label>
        {{ main_form.vehi_reg_no }}
        {% if main_form.vehi_reg_no.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.vehi_reg_no.errors }}</p>{% endif %}
    </div>

    <!-- Date Of Removal -->
    <div class="field-group">
        <label for="{{ main_form.date_of_removal.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ main_form.date_of_removal.label }}
        </label>
        {{ main_form.date_of_removal }}
        {% if main_form.date_of_removal.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.date_of_removal.errors }}</p>{% endif %}
    </div>

    <!-- Time Of Removal -->
    <div class="field-group">
        <label for="{{ main_form.time_of_removal.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ main_form.time_of_removal.label }}
        </label>
        {{ main_form.time_of_removal }}
        {% if main_form.time_of_removal.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.time_of_removal.errors }}</p>{% endif %}
    </div>
</div>
```

```html
<!-- sales_invoices/templates/sales_invoices/_buyer_tab.html -->
<h3 class="text-lg font-medium text-gray-900 mb-5">Buyer Details</h3>
<div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
    <div class="field-group">
        <label for="{{ buyer_form.buyer_name_search.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ buyer_form.buyer_name_search.label }}
        </label>
        <div class="relative">
            {{ buyer_form.buyer_name_search }}
            {{ buyer_form.buyer_name_search.help_text }}
            {% if buyer_form.buyer_name_search.errors %}<p class="text-red-500 text-xs mt-1">{{ buyer_form.buyer_name_search.errors }}</p>{% endif %}
        </div>
        <button type="button" 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded mt-2"
            hx-get="/sales_invoice/get_customer_details/?customer_id_with_name={{ buyer_form.buyer_name_selected.value }}"
            hx-target="body"
            hx-swap="none"
            _="on htmx:afterRequest(evt) if evt.detail.xhr.status === 200 populateBuyerForm(JSON.parse(evt.detail.xhr.responseText)) else alert('Customer not found')">
            Search
        </button>
        {{ buyer_form.buyer_name_selected }} {# The actual hidden input for the selected value #}
        {{ buyer_form.customer_id_hidden }} {# Hidden customer ID #}
        {% if buyer_form.buyer_name_selected.errors %}<p class="text-red-500 text-xs mt-1">{{ buyer_form.buyer_name_selected.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="{{ buyer_form.buyer_add.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ buyer_form.buyer_add.label }}
        </label>
        {{ buyer_form.buyer_add }}
        {% if buyer_form.buyer_add.errors %}<p class="text-red-500 text-xs mt-1">{{ buyer_form.buyer_add.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="{{ buyer_form.buyer_country.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ buyer_form.buyer_country.label }}
        </label>
        {{ buyer_form.buyer_country }}
        {% if buyer_form.buyer_country.errors %}<p class="text-red-500 text-xs mt-1">{{ buyer_form.buyer_country.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="buyer-state-select" class="block text-sm font-medium text-gray-700">
            {{ buyer_form.buyer_state.label }}
        </label>
        {{ buyer_form.buyer_state }}
        {% if buyer_form.buyer_state.errors %}<p class="text-red-500 text-xs mt-1">{{ buyer_form.buyer_state.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="buyer-city-select" class="block text-sm font-medium text-gray-700">
            {{ buyer_form.buyer_city.label }}
        </label>
        {{ buyer_form.buyer_city }}
        {% if buyer_form.buyer_city.errors %}<p class="text-red-500 text-xs mt-1">{{ buyer_form.buyer_city.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="{{ buyer_form.buyer_cotper.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ buyer_form.buyer_cotper.label }}
        </label>
        {{ buyer_form.buyer_cotper }}
        {% if buyer_form.buyer_cotper.errors %}<p class="text-red-500 text-xs mt-1">{{ buyer_form.buyer_cotper.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="{{ buyer_form.buyer_ph.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ buyer_form.buyer_ph.label }}
        </label>
        {{ buyer_form.buyer_ph }}
        {% if buyer_form.buyer_ph.errors %}<p class="text-red-500 text-xs mt-1">{{ buyer_form.buyer_ph.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="{{ buyer_form.buyer_mob.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ buyer_form.buyer_mob.label }}
        </label>
        {{ buyer_form.buyer_mob }}
        {% if buyer_form.buyer_mob.errors %}<p class="text-red-500 text-xs mt-1">{{ buyer_form.buyer_mob.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="{{ buyer_form.buyer_email.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ buyer_form.buyer_email.label }}
        </label>
        {{ buyer_form.buyer_email }}
        {% if buyer_form.buyer_email.errors %}<p class="text-red-500 text-xs mt-1">{{ buyer_form.buyer_email.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="{{ buyer_form.buyer_fax.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ buyer_form.buyer_fax.label }}
        </label>
        {{ buyer_form.buyer_fax }}
        {% if buyer_form.buyer_fax.errors %}<p class="text-red-500 text-xs mt-1">{{ buyer_form.buyer_fax.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="{{ buyer_form.buyer_vat.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ buyer_form.buyer_vat.label }}
        </label>
        {{ buyer_form.buyer_vat }}
        {% if buyer_form.buyer_vat.errors %}<p class="text-red-500 text-xs mt-1">{{ buyer_form.buyer_vat.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="{{ buyer_form.buyer_ecc.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ buyer_form.buyer_ecc.label }}
        </label>
        {{ buyer_form.buyer_ecc }}
        {% if buyer_form.buyer_ecc.errors %}<p class="text-red-500 text-xs mt-1">{{ buyer_form.buyer_ecc.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="{{ buyer_form.buyer_tin.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ buyer_form.buyer_tin.label }}
        </label>
        {{ buyer_form.buyer_tin }}
        {% if buyer_form.buyer_tin.errors %}<p class="text-red-500 text-xs mt-1">{{ buyer_form.buyer_tin.errors }}</p>{% endif %}
    </div>
</div>
```

```html
<!-- sales_invoices/templates/sales_invoices/_consignee_tab.html -->
<h3 class="text-lg font-medium text-gray-900 mb-5">Consignee Details</h3>
<div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
    <div class="field-group">
        <label for="{{ consignee_form.cong_name_search.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ consignee_form.cong_name_search.label }}
        </label>
        <div class="relative">
            {{ consignee_form.cong_name_search }}
            {{ consignee_form.cong_name_search.help_text|safe }}
            {% if consignee_form.cong_name_search.errors %}<p class="text-red-500 text-xs mt-1">{{ consignee_form.cong_name_search.errors }}</p>{% endif %}
        </div>
        <button type="button" 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded mt-2"
            hx-get="/sales_invoice/get_customer_details/?customer_id_with_name={{ consignee_form.cong_name_selected.value }}"
            hx-target="body"
            hx-swap="none"
            _="on htmx:afterRequest(evt) if evt.detail.xhr.status === 200 populateConsigneeForm(JSON.parse(evt.detail.xhr.responseText)) else alert('Customer not found')">
            Search
        </button>
        <button type="button" @click="populateConsigneeForm({
            name: document.getElementById('id_buyer_name_selected').value,
            id: document.getElementById('id_customer_id_hidden').value,
            address: document.getElementById('id_buyer_add').value,
            country_id: document.getElementById('id_buyer_country').value,
            state_id: document.getElementById('buyer-state-select').value,
            city_id: document.getElementById('buyer-city-select').value,
            contact_person: document.getElementById('id_buyer_cotper').value,
            phone_no: document.getElementById('id_buyer_ph').value,
            mobile_no: document.getElementById('id_buyer_mob').value,
            email: document.getElementById('id_buyer_email').value,
            fax_no: document.getElementById('id_buyer_fax').value,
            tin_vat_no: document.getElementById('id_buyer_vat').value,
            ecc_no: document.getElementById('id_buyer_ecc').value,
            tin_cst_no: document.getElementById('id_buyer_tin').value,
        })" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-1 px-2 rounded mt-2 ml-2">
            Copy from buyer
        </button>
        {{ consignee_form.cong_name_selected }} {# The actual hidden input for the selected value #}
        {{ consignee_form.customer_id_hidden|as_hidden:"id_consignee_customer_id_hidden" }} {# Hidden customer ID for consignee #}
        {% if consignee_form.cong_name_selected.errors %}<p class="text-red-500 text-xs mt-1">{{ consignee_form.cong_name_selected.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="{{ consignee_form.cong_add.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ consignee_form.cong_add.label }}
        </label>
        {{ consignee_form.cong_add }}
        {% if consignee_form.cong_add.errors %}<p class="text-red-500 text-xs mt-1">{{ consignee_form.cong_add.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="{{ consignee_form.cong_country.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ consignee_form.cong_country.label }}
        </label>
        {{ consignee_form.cong_country }}
        {% if consignee_form.cong_country.errors %}<p class="text-red-500 text-xs mt-1">{{ consignee_form.cong_country.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="consignee-state-select" class="block text-sm font-medium text-gray-700">
            {{ consignee_form.cong_state.label }}
        </label>
        {{ consignee_form.cong_state }}
        {% if consignee_form.cong_state.errors %}<p class="text-red-500 text-xs mt-1">{{ consignee_form.cong_state.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="consignee-city-select" class="block text-sm font-medium text-gray-700">
            {{ consignee_form.cong_city.label }}
        </label>
        {{ consignee_form.cong_city }}
        {% if consignee_form.cong_city.errors %}<p class="text-red-500 text-xs mt-1">{{ consignee_form.cong_city.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="{{ consignee_form.cong_cotper.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ consignee_form.cong_cotper.label }}
        </label>
        {{ consignee_form.cong_cotper }}
        {% if consignee_form.cong_cotper.errors %}<p class="text-red-500 text-xs mt-1">{{ consignee_form.cong_cotper.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="{{ consignee_form.cong_ph.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ consignee_form.cong_ph.label }}
        </label>
        {{ consignee_form.cong_ph }}
        {% if consignee_form.cong_ph.errors %}<p class="text-red-500 text-xs mt-1">{{ consignee_form.cong_ph.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="{{ consignee_form.cong_mob.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ consignee_form.cong_mob.label }}
        </label>
        {{ consignee_form.cong_mob }}
        {% if consignee_form.cong_mob.errors %}<p class="text-red-500 text-xs mt-1">{{ consignee_form.cong_mob.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="{{ consignee_form.cong_email.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ consignee_form.cong_email.label }}
        </label>
        {{ consignee_form.cong_email }}
        {% if consignee_form.cong_email.errors %}<p class="text-red-500 text-xs mt-1">{{ consignee_form.cong_email.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="{{ consignee_form.cong_fax.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ consignee_form.cong_fax.label }}
        </label>
        {{ consignee_form.cong_fax }}
        {% if consignee_form.cong_fax.errors %}<p class="text-red-500 text-xs mt-1">{{ consignee_form.cong_fax.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="{{ consignee_form.cong_vat.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ consignee_form.cong_vat.label }}
        </label>
        {{ consignee_form.cong_vat }}
        {% if consignee_form.cong_vat.errors %}<p class="text-red-500 text-xs mt-1">{{ consignee_form.cong_vat.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="{{ consignee_form.cong_ecc.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ consignee_form.cong_ecc.label }}
        </label>
        {{ consignee_form.cong_ecc }}
        {% if consignee_form.cong_ecc.errors %}<p class="text-red-500 text-xs mt-1">{{ consignee_form.cong_ecc.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="{{ consignee_form.cong_tin.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ consignee_form.cong_tin.label }}
        </label>
        {{ consignee_form.cong_tin }}
        {% if consignee_form.cong_tin.errors %}<p class="text-red-500 text-xs mt-1">{{ consignee_form.cong_tin.errors }}</p>{% endif %}
    </div>
</div>
```

```html
<!-- sales_invoices/templates/sales_invoices/_goods_tab.html -->
<h3 class="text-lg font-medium text-gray-900 mb-5">Goods Details</h3>
<div id="goodsTableContainer" class="overflow-x-auto">
    <table id="goodsTable" class="min-w-full bg-white border border-gray-200">
        <thead>
            <tr class="bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <th class="py-2 px-4 border-b">SN</th>
                <th class="py-2 px-4 border-b"></th> {# Checkbox #}
                <th class="py-2 px-4 border-b">Description</th>
                <th class="py-2 px-4 border-b">Unit</th>
                <th class="py-2 px-4 border-b">Qty</th>
                <th class="py-2 px-4 border-b">Remn Qty</th>
                <th class="py-2 px-4 border-b">Unit Of Qty</th>
                <th class="py-2 px-4 border-b">Req Qty</th>
                <th class="py-2 px-4 border-b">Rate</th>
                <th class="py-2 px-4 border-b">Amt in (%)</th>
            </tr>
        </thead>
        <tbody>
            {{ goods_formset.management_form }}
            {% for form in goods_formset %}
            <tr class="even:bg-gray-50 hover:bg-gray-100">
                <td class="py-2 px-4 border-b text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b">
                    {{ form.select_item }}
                </td>
                <td class="py-2 px-4 border-b">{{ form.po_item_desc }}</td>
                <td class="py-2 px-4 border-b text-center">{{ form.po_item_unit_symbol }}</td>
                <td class="py-2 px-4 border-b text-right">{{ form.po_item_total_qty }}</td>
                <td class="py-2 px-4 border-b text-right">{{ form.po_item_remaining_qty }}</td>
                <td class="py-2 px-4 border-b">{{ form.unit }}</td>
                <td class="py-2 px-4 border-b">
                    {{ form.req_qty }}
                    {% if form.req_qty.errors %}<p class="text-red-500 text-xs mt-1">{{ form.req_qty.errors }}</p>{% endif %}
                </td>
                <td class="py-2 px-4 border-b text-right">{{ form.po_item_rate }}</td>
                <td class="py-2 px-4 border-b">
                    {{ form.amt_in_per }}
                    {% if form.amt_in_per.errors %}<p class="text-red-500 text-xs mt-1">{{ form.amt_in_per.errors }}</p>{% endif %}
                </td>
                {# Hidden fields for formset #}
                {{ form.item_id }}
                {{ form.qty }}
                {{ form.rate }}
            </tr>
            {% empty %}
            <tr>
                <td colspan="10" class="py-4 text-center text-gray-500">No data to display!</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // DataTables initialization for goods table
    // It is triggered by htmx:afterSwap in the parent template
    // This script block ensures the DataTables function exists when the partial is loaded.
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#goodsTable')) {
            $('#goodsTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
            });
        }
    });

    // Client-side logic for conditionally required fields based on checkbox
    document.querySelectorAll('#goodsTable tbody tr').forEach(row => {
        const checkbox = row.querySelector('input[type="checkbox"]');
        const reqQtyField = row.querySelector('input[name$="-req_qty"]');
        const amtInPerField = row.querySelector('input[name$="-amt_in_per"]');
        const unitField = row.querySelector('select[name$="-unit"]');

        const toggleFields = () => {
            const isDisabled = !checkbox.checked;
            if (reqQtyField) reqQtyField.disabled = isDisabled;
            if (amtInPerField) amtInPerField.disabled = isDisabled;
            if (unitField) unitField.disabled = isDisabled;
            
            // Clear values if disabled and not checked
            if (isDisabled && !checkbox.checked) {
                if (reqQtyField) reqQtyField.value = '';
                if (amtInPerField) amtInPerField.value = '';
            }
        };

        if (checkbox) {
            checkbox.addEventListener('change', toggleFields);
            toggleFields(); // Initial state
        }
    });

</script>
```

```html
<!-- sales_invoices/templates/sales_invoices/_taxation_tab.html -->
<h3 class="text-lg font-medium text-gray-900 mb-5">Taxation Details</h3>
<div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
    <div class="field-group">
        <label for="{{ main_form.add_amt.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ main_form.add_amt.label }}
        </label>
        {{ main_form.add_amt }}
        {{ main_form.add_type }}
        {% if main_form.add_amt.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.add_amt.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="{{ main_form.other_amt.id_for_label }}" class="block text-sm font-medium text-gray-700">
            Other Amount
        </label>
        {{ main_form.other_amt }}
        {% if main_form.other_amt.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.other_amt.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="{{ main_form.deduction.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ main_form.deduction.label }}
        </label>
        {{ main_form.deduction }}
        {{ main_form.deduction_type }}
        {% if main_form.deduction.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.deduction.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="{{ main_form.pf.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ main_form.pf.label }}
        </label>
        {{ main_form.pf }}
        {{ main_form.pf_type }}
        {% if main_form.pf.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.pf.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="{{ main_form.cenvat.id_for_label }}" class="block text-sm font-medium text-gray-700">
            CENVAT/Excise
        </label>
        {{ main_form.cenvat }}
        {% if main_form.cenvat.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.cenvat.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="{{ main_form.sed.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ main_form.sed.label }}
        </label>
        {{ main_form.sed }}
        {{ main_form.sedtype }}
        {% if main_form.sed.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.sed.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="{{ main_form.aed.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ main_form.aed.label }}
        </label>
        {{ main_form.aed }}
        {{ main_form.aedtype }}
        {% if main_form.aed.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.aed.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="{{ main_form.freight.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ main_form.freight.label }}
        </label>
        {{ main_form.freight }}
        {{ main_form.freight_type }}
        {% if main_form.freight.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.freight.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label class="block text-sm font-medium text-gray-700">
            {{ vat_label }}
        </label>
        {% if vat_label == "CST" %}
        {{ main_form.selected_cst }}
        {% endif %}
        {{ main_form.vat }}
        {% if main_form.vat.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.vat.errors }}</p>{% endif %}
    </div>

    <div class="field-group">
        <label for="{{ main_form.insurance.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ main_form.insurance.label }}
        </label>
        {{ main_form.insurance }}
        {{ main_form.insurance_type }}
        {% if main_form.insurance.errors %}<p class="text-red-500 text-xs mt-1">{{ main_form.insurance.errors }}</p>{% endif %}
    </div>
</div>
```

#### 4.5 URLs (`sales_invoices/urls.py`)

```python
from django.urls import path
from .views import (
    SalesInvoiceCreateView,
    CustomerAutocomplete,
    CustomerDetailsLoad,
    GetStatesPartialView,
    GetCitiesPartialView,
    GetTariffHeadPartialView,
    SalesInvoiceGoodsTabPartialView,
    SalesInvoiceListView # Placeholder list view
)

urlpatterns = [
    path('new/', SalesInvoiceCreateView.as_view(), name='sales_invoice_create'),
    path('list/', SalesInvoiceListView.as_view(), name='sales_invoice_list_all'), # Placeholder for the list page redirect

    # HTMX Endpoints
    path('autocomplete_customer/', CustomerAutocomplete.as_view(), name='autocomplete_customer'),
    path('get_customer_details/', CustomerDetailsLoad.as_view(), name='get_customer_details'),
    path('get_states/', GetStatesPartialView.as_view(), name='get_states'),
    path('get_cities/', GetCitiesPartialView.as_view(), name='get_cities'),
    path('get_tariff_head/', GetTariffHeadPartialView.as_view(), name='get_tariff_head'),
    path('goods_tab_partial/', SalesInvoiceGoodsTabPartialView.as_view(), name='goods_tab_partial'),
]

```
**Important:** You would include `sales_invoices.urls` in your project's main `urls.py`:
`path('sales_invoice/', include('sales_invoices.urls')),`

#### 4.6 Tests (`sales_invoices/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db import models
from django.utils import timezone
import json
import datetime

from .models import (
    ExciseCommodity, ServiceCategory, TransportMode, RemovalNature, SalesInvoiceType,
    Unit, VATMaster, ExciseServiceMaster, Country, State, City,
    Customer, PurchaseOrder, PurchaseOrderDetail, SalesInvoice, SalesInvoiceDetail
)

# Helper function to mock session data for tests
def create_mock_session(client):
    session = client.session
    session['compid'] = 1
    session['finyear'] = 1
    session['username'] = 'testuser'
    session.save()

class LookupModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal data for lookup tables
        cls.excise_commodity = ExciseCommodity.objects.create(id=1, terms='Textile')
        cls.service_category = ServiceCategory.objects.create(id=1, description='General')
        cls.transport_mode = TransportMode.objects.create(id=1, description='Road')
        cls.removal_nature = RemovalNature.objects.create(id=1, description='Sale')
        cls.sales_invoice_type_mh = SalesInvoiceType.objects.create(id=2, description='Within Mh.')
        cls.sales_invoice_type_other = SalesInvoiceType.objects.create(id=3, description='Out of Mh.')
        cls.unit = Unit.objects.create(id=1, symbol='KGS')
        cls.vat_master_gst = VATMaster.objects.create(id=1, terms='GST@18%')
        cls.excise_service_master = ExciseServiceMaster.objects.create(id=1, terms='Excise@12%', live=True)

        cls.country = Country.objects.create(id=1, name='India')
        cls.state = State.objects.create(id=1, name='Maharashtra', country=cls.country)
        cls.city = City.objects.create(id=1, name='Mumbai', state=cls.state)

    def test_excise_commodity_creation(self):
        self.assertEqual(self.excise_commodity.terms, 'Textile')
        self.assertEqual(ExciseCommodity.get_tariff_head(1), "TARIFF/EXMP-1234")
        self.assertEqual(str(self.excise_commodity), 'Textile')

    def test_country_state_city_relations(self):
        self.assertEqual(self.state.country.name, 'India')
        self.assertEqual(self.city.state.name, 'Maharashtra')

class CustomerModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Setup lookup data first
        cls.country = Country.objects.create(id=1, name='India')
        cls.state = State.objects.create(id=1, name='Maharashtra', country=cls.country)
        cls.city = City.objects.create(id=1, name='Mumbai', state=cls.state)

        cls.customer = Customer.objects.create(
            customer_id='CUST001',
            customer_name='Test Buyer Inc.',
            material_del_address='123, Test Street',
            material_del_country=cls.country,
            material_del_state=cls.state,
            material_del_city=cls.city,
            contact_person='John Doe',
            email='<EMAIL>',
            comp_id=1
        )
        Customer.objects.create(
            customer_id='CUST002',
            customer_name='Another Customer',
            comp_id=1
        )

    def test_customer_creation(self):
        self.assertEqual(self.customer.customer_name, 'Test Buyer Inc.')
        self.assertEqual(str(self.customer), 'Test Buyer Inc. [CUST001]')

    def test_search_by_name(self):
        results = Customer.search_by_name('test', 1)
        self.assertIn('Test Buyer Inc. [CUST001]', results)
        self.assertNotIn('Test Buyer Inc. [CUST001]', Customer.search_by_name('test', 2)) # Wrong comp_id

    def test_get_customer_details(self):
        details = Customer.get_customer_details('CUST001', 1)
        self.assertEqual(details.customer_name, 'Test Buyer Inc.')
        self.assertEqual(details.material_del_city.name, 'Mumbai')
        self.assertIsNone(Customer.get_customer_details('NONEXISTENT', 1))

class PurchaseOrderModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Setup lookup data
        cls.unit = Unit.objects.create(id=1, symbol='KGS')
        cls.po = PurchaseOrder.objects.create(poid=1, pono='PO001', comp_id=1)
        cls.po_detail_1 = PurchaseOrderDetail.objects.create(
            id=1, poid=cls.po, item_desc='Item A', total_qty=100.0, unit=cls.unit, rate=10.5,
        )
        cls.po_detail_2 = PurchaseOrderDetail.objects.create(
            id=2, poid=cls.po, item_desc='Item B', total_qty=50.0, unit=cls.unit, rate=20.0,
        )

    def test_po_detail_creation(self):
        self.assertEqual(self.po_detail_1.item_desc, 'Item A')
        self.assertEqual(self.po_detail_1.unit.symbol, 'KGS')

    def test_get_remaining_qty(self):
        # Initially, remaining qty should be total_qty
        self.assertEqual(self.po_detail_1.get_remaining_qty(1), 100.0)

        # Create a partial invoice for Item A
        customer = Customer.objects.create(customer_id='CUST100', customer_name='Test', comp_id=1)
        sales_invoice = SalesInvoice.objects.create(
            id=1, invoice_no='INV001', sys_date=timezone.now().date(), sys_time=timezone.now().time(),
            comp_id=1, fin_year_id=1, session_id='testuser', customer_code=customer, poid=self.po
        )
        SalesInvoiceDetail.objects.create(
            invoice=sales_invoice, invoice_no='INV001', mid=sales_invoice.id,
            item_id=self.po_detail_1, unit=self.unit, qty=100.0, req_qty=20.0, amt_in_per=20.0, rate=10.5
        )
        self.assertEqual(self.po_detail_1.get_remaining_qty(1), 80.0) # 100 - 20

class SalesInvoiceModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.country = Country.objects.create(id=1, name='India')
        cls.state = State.objects.create(id=1, name='Maharashtra', country=cls.country)
        cls.city = City.objects.create(id=1, name='Mumbai', state=cls.city)
        cls.customer = Customer.objects.create(customer_id='CUST001', customer_name='Test Customer', comp_id=1)
        cls.unit = Unit.objects.create(id=1, symbol='PCS')
        cls.po = PurchaseOrder.objects.create(poid=1, pono='PO001', comp_id=1)
        cls.po_detail_1 = PurchaseOrderDetail.objects.create(id=1, poid=cls.po, item_desc='Product A', total_qty=100.0, unit=cls.unit, rate=50.0)
        cls.po_detail_2 = PurchaseOrderDetail.objects.create(id=2, poid=cls.po, item_desc='Product B', total_qty=200.0, unit=cls.unit, rate=25.0)

        cls.invoice_type = SalesInvoiceType.objects.create(id=1, description='Regular')
        cls.removal_nature = RemovalNature.objects.create(id=1, description='Sale')
        cls.excise_commodity = ExciseCommodity.objects.create(id=1, terms='ABC')
        cls.transport_mode = TransportMode.objects.create(id=1, description='Road')
        cls.service_category = ServiceCategory.objects.create(id=1, description='Type 1')
        cls.vat_master = VATMaster.objects.create(id=1, terms='VAT 10%')
        cls.excise_service_master = ExciseServiceMaster.objects.create(id=1, terms='CENVAT 12%')

    def test_generate_next_invoice_no(self):
        # No existing invoices
        self.assertEqual(SalesInvoice.generate_next_invoice_no(1, 1), '0001')

        # Existing invoices
        SalesInvoice.objects.create(id=1, invoice_no='0001', sys_date=timezone.now().date(), sys_time=timezone.now().time(), comp_id=1, fin_year_id=1, session_id='user', customer_code=self.customer)
        self.assertEqual(SalesInvoice.generate_next_invoice_no(1, 1), '0002')
        SalesInvoice.objects.create(id=2, invoice_no='0010', sys_date=timezone.now().date(), sys_time=timezone.now().time(), comp_id=1, fin_year_id=1, session_id='user', customer_code=self.customer)
        self.assertEqual(SalesInvoice.generate_next_invoice_no(1, 1), '0011')

    def test_save_full_invoice_success(self):
        invoice = SalesInvoice(
            sys_date=timezone.now().date(), sys_time=timezone.now().time(),
            comp_id=1, fin_year_id=1, session_id='testuser', invoice_no='NEWINV',
            date_of_issue_invoice=timezone.now().date(), date_of_removal=timezone.now().date(),
            customer_code=self.customer,
            buyer_name='Test Buyer', buyer_add='Addr', buyer_country=self.country, buyer_state=self.state, buyer_city=self.city,
            invoice_mode=self.invoice_type, nature_of_removal=self.removal_nature,
            commodity=self.excise_commodity, mode_of_transport=self.transport_mode,
            customer_category=self.service_category, poid=self.po
        )
        details_data = [
            {'item_id': self.po_detail_1, 'unit': self.unit, 'qty': 100.0, 'req_qty': 10.0, 'amt_in_per': 10.0, 'rate': 50.0},
        ]
        invoice.save_full_invoice(details_data)

        self.assertEqual(SalesInvoice.objects.count(), 1)
        self.assertEqual(SalesInvoiceDetail.objects.count(), 1)
        self.assertEqual(SalesInvoiceDetail.objects.first().req_qty, 10.0)

    def test_save_full_invoice_insufficient_qty(self):
        invoice = SalesInvoice(
            sys_date=timezone.now().date(), sys_time=timezone.now().time(),
            comp_id=1, fin_year_id=1, session_id='testuser', invoice_no='INV003',
            date_of_issue_invoice=timezone.now().date(), date_of_removal=timezone.now().date(),
            customer_code=self.customer,
            buyer_name='Test Buyer', buyer_add='Addr', buyer_country=self.country, buyer_state=self.state, buyer_city=self.city,
            invoice_mode=self.invoice_type, nature_of_removal=self.removal_nature,
            commodity=self.excise_commodity, mode_of_transport=self.transport_mode,
            customer_category=self.service_category, poid=self.po
        )
        details_data = [
            {'item_id': self.po_detail_1, 'unit': self.unit, 'qty': 100.0, 'req_qty': 101.0, 'amt_in_per': 10.0, 'rate': 50.0}, # Over-requested
        ]
        with self.assertRaises(ValueError):
            invoice.save_full_invoice(details_data)
        self.assertEqual(SalesInvoice.objects.count(), 0) # Should not save any
        self.assertEqual(SalesInvoiceDetail.objects.count(), 0)

class SalesInvoiceViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        create_mock_session(self.client)

        # Create necessary lookup data
        self.country = Country.objects.create(id=1, name='India')
        self.state = State.objects.create(id=1, name='Maharashtra', country=self.country)
        self.city = City.objects.create(id=1, name='Mumbai', state=self.city)
        self.customer_buyer = Customer.objects.create(customer_id='CUST001', customer_name='Test Buyer', comp_id=1, material_del_country=self.country, material_del_state=self.state, material_del_city=self.city)
        self.customer_consignee = Customer.objects.create(customer_id='CUST002', customer_name='Test Consignee', comp_id=1, material_del_country=self.country, material_del_state=self.state, material_del_city=self.city)
        self.unit = Unit.objects.create(id=1, symbol='PCS')
        self.po = PurchaseOrder.objects.create(poid=1, pono='PO001', comp_id=1)
        self.po_detail_1 = PurchaseOrderDetail.objects.create(id=1, poid=self.po, item_desc='Product A', total_qty=100.0, unit=self.unit, rate=50.0)
        self.po_detail_2 = PurchaseOrderDetail.objects.create(id=2, poid=self.po, item_desc='Product B', total_qty=50.0, unit=self.unit, rate=25.0)

        self.invoice_type = SalesInvoiceType.objects.create(id=1, description='Regular')
        self.removal_nature = RemovalNature.objects.create(id=1, description='Sale')
        self.excise_commodity = ExciseCommodity.objects.create(id=1, terms='ABC')
        self.transport_mode = TransportMode.objects.create(id=1, description='Road')
        self.service_category = ServiceCategory.objects.create(id=1, description='Type 1')
        self.vat_master = VATMaster.objects.create(id=1, terms='VAT 10%')
        self.excise_service_master = ExciseServiceMaster.objects.create(id=1, terms='CENVAT 12%', live=True)


    def test_create_view_get(self):
        # Query string parameters mimicking ASP.NET
        query_params = {
            'pn': 'PO001',
            'wn': 'WO001,WO002,',
            'poid': '1',
            'cid': 'CUST001',
            'ty': '1', # InvoiceMode_Id
            'date': '01-01-2023',
        }
        response = self.client.get(reverse('sales_invoice_create'), query_params)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_invoices/sales_invoice_form.html')
        self.assertIn('main_form', response.context)
        self.assertIn('buyer_form', response.context)
        self.assertIn('goods_formset', response.context)
        self.assertEqual(response.context['main_form'].initial['po_no'], 'PO001')
        self.assertIn('Test Buyer [CUST001]', response.context['buyer_form'].initial['buyer_name_selected'])
        self.assertEqual(response.context['goods_formset'].forms[0].initial['item_id'], self.po_detail_1.id)

    def test_create_view_post_success(self):
        # Minimal valid data to test success
        data = {
            # Main Form Data
            'invoice_no': '0001',
            'date_of_issue_invoice': '2023-01-15',
            'time_of_issue_invoice': '10:00',
            'date_of_removal': '2023-01-15',
            'time_of_removal': '11:00',
            'nature_of_removal': self.removal_nature.id,
            'commodity': self.excise_commodity.id,
            'duty_rate': 10.0,
            'mode_of_transport': self.transport_mode.id,
            'rrgc_no': 'RRGC123',
            'vehi_reg_no': 'VEHI456',
            'customer_category': self.service_category.id,
            'other_amt': 0.0,
            'add_type': 0, 'add_amt': 0.0,
            'deduction_type': 0, 'deduction': 0.0,
            'pf_type': 0, 'pf': 0.0,
            'cenvat': self.excise_service_master.id,
            'sed': 0.0, 'aed': 0.0,
            'vat': self.vat_master.id,
            'selected_cst': 0, 'cst': self.vat_master.id,
            'freight_type': 0, 'freight': 0.0,
            'insurance_type': 0, 'insurance': 0.0,
            'sedtype': 0, 'aedtype': 0,

            # Hidden Context Data (simulating QueryString)
            'po_no': 'PO001',
            'wo_no': 'WO001,WO002,',
            'poid': '1',
            'customer_code_initial': 'CUST001',
            'invoice_type_id': '1',
            'po_date': '2023-01-01',

            # Buyer Form Data
            'buyer_name_search': 'Test Buyer Inc. [CUST001]', # This field isn't saved directly
            'buyer_name_selected': 'Test Buyer [CUST001]',
            'customer_id_hidden': 'CUST001',
            'buyer_add': '123, Test Street, Mumbai',
            'buyer_country': self.country.id,
            'buyer_state': self.state.id,
            'buyer_city': self.city.id,
            'buyer_cotper': 'John Doe',
            'buyer_ph': '9876543210',
            'buyer_email': '<EMAIL>',
            'buyer_ecc': 'ECC123',
            'buyer_tin': 'TIN123',
            'buyer_mob': '9876543210',
            'buyer_fax': 'FAX123',
            'buyer_vat': 'VAT123',

            # Consignee Form Data
            'cong_name_search': 'Test Consignee [CUST002]',
            'cong_name_selected': 'Test Consignee [CUST002]',
            'consignee_customer_id_hidden': 'CUST002', # This is specific to consignee to avoid conflict with buyer
            'cong_add': '456, Consignee Road',
            'cong_country': self.country.id,
            'cong_state': self.state.id,
            'cong_city': self.city.id,
            'cong_cotper': 'Jane Smith',
            'cong_ph': '1234567890',
            'cong_email': '<EMAIL>',
            'cong_ecc': 'ECC456',
            'cong_tin': 'TIN456',
            'cong_mob': '1234567890',
            'cong_fax': 'FAX456',
            'cong_vat': 'VAT456',
            
            # Goods Formset Data
            'form-TOTAL_FORMS': '2',
            'form-INITIAL_FORMS': '2',
            'form-MIN_NUM_FORMS': '0',
            'form-MAX_NUM_FORMS': '1000', # Large number as per ASP.NET

            # Item 1
            'form-0-select_item': 'on',
            'form-0-req_qty': '10.0',
            'form-0-unit': self.unit.id,
            'form-0-amt_in_per': '10.0',
            'form-0-item_id': self.po_detail_1.id,
            'form-0-qty': self.po_detail_1.total_qty,
            'form-0-rate': self.po_detail_1.rate,
            
            # Item 2 (not selected)
            'form-1-select_item': '', # Not checked
            'form-1-req_qty': '', # Empty if not selected
            'form-1-unit': self.unit.id,
            'form-1-amt_in_per': '',
            'form-1-item_id': self.po_detail_2.id,
            'form-1-qty': self.po_detail_2.total_qty,
            'form-1-rate': self.po_detail_2.rate,
        }
        
        response = self.client.post(reverse('sales_invoice_create'), data, follow=True)
        self.assertEqual(response.status_code, 200) # Should redirect to list view
        self.assertRedirects(response, reverse('sales_invoice_list_all'))
        self.assertEqual(SalesInvoice.objects.count(), 1)
        self.assertEqual(SalesInvoiceDetail.objects.count(), 1) # Only 1 selected item
        self.assertContains(response, 'Sales Invoice created successfully.')

    def test_create_view_post_validation_error_no_goods_selected(self):
        data = {
            # Main Form Data (minimal valid)
            'invoice_no': '0001',
            'date_of_issue_invoice': '2023-01-15',
            'time_of_issue_invoice': '10:00',
            'date_of_removal': '2023-01-15',
            'time_of_removal': '11:00',
            'nature_of_removal': self.removal_nature.id,
            'commodity': self.excise_commodity.id,
            'duty_rate': 10.0,
            'mode_of_transport': self.transport_mode.id,
            'rrgc_no': 'RRGC123',
            'vehi_reg_no': 'VEHI456',
            'customer_category': self.service_category.id,
            'other_amt': 0.0,
            'add_type': 0, 'add_amt': 0.0,
            'deduction_type': 0, 'deduction': 0.0,
            'pf_type': 0, 'pf': 0.0,
            'cenvat': self.excise_service_master.id,
            'sed': 0.0, 'aed': 0.0,
            'vat': self.vat_master.id,
            'selected_cst': 0, 'cst': self.vat_master.id,
            'freight_type': 0, 'freight': 0.0,
            'insurance_type': 0, 'insurance': 0.0,
            'sedtype': 0, 'aedtype': 0,

            # Hidden Context Data
            'po_no': 'PO001',
            'wo_no': 'WO001,WO002,',
            'poid': '1',
            'customer_code_initial': 'CUST001',
            'invoice_type_id': '1',
            'po_date': '2023-01-01',

            # Buyer Form Data
            'buyer_name_selected': 'Test Buyer [CUST001]',
            'customer_id_hidden': 'CUST001',
            'buyer_add': '123, Test Street, Mumbai',
            'buyer_country': self.country.id, 'buyer_state': self.state.id, 'buyer_city': self.city.id,
            'buyer_cotper': 'John Doe', 'buyer_ph': '9876543210', 'buyer_email': '<EMAIL>',
            'buyer_ecc': 'ECC123', 'buyer_tin': 'TIN123', 'buyer_mob': '9876543210', 'buyer_fax': 'FAX123', 'buyer_vat': 'VAT123',

            # Consignee Form Data (valid minimal)
            'cong_name_selected': 'Test Consignee [CUST002]',
            'consignee_customer_id_hidden': 'CUST002',
            'cong_add': '456, Consignee Road',
            'cong_country': self.country.id, 'cong_state': self.state.id, 'cong_city': self.city.id,
            'cong_cotper': 'Jane Smith', 'cong_ph': '1234567890', 'cong_email': '<EMAIL>',
            'cong_ecc': 'ECC456', 'cong_tin': 'TIN456', 'cong_mob': '1234567890', 'cong_fax': 'FAX456', 'cong_vat': 'VAT456',
            
            # Goods Formset Data (NONE SELECTED)
            'form-TOTAL_FORMS': '2',
            'form-INITIAL_FORMS': '2',
            'form-MIN_NUM_FORMS': '0',
            'form-MAX_NUM_FORMS': '1000',

            # Item 1 (not selected)
            'form-0-select_item': '', # Not checked
            'form-0-req_qty': '',
            'form-0-unit': self.unit.id,
            'form-0-amt_in_per': '',
            'form-0-item_id': self.po_detail_1.id,
            'form-0-qty': self.po_detail_1.total_qty,
            'form-0-rate': self.po_detail_1.rate,
            
            # Item 2 (not selected)
            'form-1-select_item': '',
            'form-1-req_qty': '',
            'form-1-unit': self.unit.id,
            'form-1-amt_in_per': '',
            'form-1-item_id': self.po_detail_2.id,
            'form-1-qty': self.po_detail_2.total_qty,
            'form-1-rate': self.po_detail_2.rate,
        }
        
        response = self.client.post(reverse('sales_invoice_create'), data)
        self.assertEqual(response.status_code, 200) # Should re-render with errors
        self.assertEqual(SalesInvoice.objects.count(), 0)
        self.assertContains(response, 'At least one item must be selected in Goods tab.')

    def test_create_view_post_validation_error_insufficient_qty(self):
        # Create a pre-existing invoice for po_detail_1 to reduce remaining_qty
        customer = Customer.objects.create(customer_id='CUST_TEMP', customer_name='Temp Customer', comp_id=1)
        SalesInvoice.objects.create(
            id=100, invoice_no='PREV001', sys_date=timezone.now().date(), sys_time=timezone.now().time(),
            comp_id=1, fin_year_id=1, session_id='tempuser', customer_code=customer, poid=self.po
        )
        SalesInvoiceDetail.objects.create(
            invoice_id=100, invoice_no='PREV001', mid=100,
            item_id=self.po_detail_1, unit=self.unit, qty=100.0, req_qty=90.0, amt_in_per=90.0, rate=50.0
        )
        # Now only 10.0 qty remaining for po_detail_1

        data = {
            # ... (other valid form data similar to success test)
            'invoice_no': '0002',
            'date_of_issue_invoice': '2023-01-15', 'time_of_issue_invoice': '10:00',
            'date_of_removal': '2023-01-15', 'time_of_removal': '11:00',
            'nature_of_removal': self.removal_nature.id, 'commodity': self.excise_commodity.id,
            'duty_rate': 10.0, 'mode_of_transport': self.transport_mode.id,
            'rrgc_no': 'RRGC123', 'vehi_reg_no': 'VEHI456',
            'customer_category': self.service_category.id, 'other_amt': 0.0,
            'add_type': 0, 'add_amt': 0.0, 'deduction_type': 0, 'deduction': 0.0,
            'pf_type': 0, 'pf': 0.0, 'cenvat': self.excise_service_master.id,
            'sed': 0.0, 'aed': 0.0, 'vat': self.vat_master.id,
            'selected_cst': 0, 'cst': self.vat_master.id,
            'freight_type': 0, 'freight': 0.0, 'insurance_type': 0, 'insurance': 0.0,
            'sedtype': 0, 'aedtype': 0,

            'po_no': 'PO001', 'wo_no': 'WO001,WO002,', 'poid': '1', 'customer_code_initial': 'CUST001', 'invoice_type_id': '1', 'po_date': '2023-01-01',

            'buyer_name_selected': 'Test Buyer [CUST001]', 'customer_id_hidden': 'CUST001',
            'buyer_add': '123, Test Street, Mumbai', 'buyer_country': self.country.id, 'buyer_state': self.state.id, 'buyer_city': self.city.id,
            'buyer_cotper': 'John Doe', 'buyer_ph': '9876543210', 'buyer_email': '<EMAIL>',
            'buyer_ecc': 'ECC123', 'buyer_tin': 'TIN123', 'buyer_mob': '9876543210', 'buyer_fax': 'FAX123', 'buyer_vat': 'VAT123',

            'cong_name_selected': 'Test Consignee [CUST002]', 'consignee_customer_id_hidden': 'CUST002',
            'cong_add': '456, Consignee Road', 'cong_country': self.country.id, 'cong_state': self.state.id, 'cong_city': self.city.id,
            'cong_cotper': 'Jane Smith', 'cong_ph': '1234567890', 'cong_email': '<EMAIL>',
            'cong_ecc': 'ECC456', 'cong_tin': 'TIN456', 'cong_mob': '1234567890', 'cong_fax': 'FAX456', 'cong_vat': 'VAT456',
            
            'form-TOTAL_FORMS': '1', 'form-INITIAL_FORMS': '1', 'form-MIN_NUM_FORMS': '0', 'form-MAX_NUM_FORMS': '1000',
            'form-0-select_item': 'on',
            'form-0-req_qty': '11.0', # Requesting more than available (10.0 remaining)
            'form-0-unit': self.unit.id,
            'form-0-amt_in_per': '10.0',
            'form-0-item_id': self.po_detail_1.id,
            'form-0-qty': self.po_detail_1.total_qty,
            'form-0-rate': self.po_detail_1.rate,
        }

        response = self.client.post(reverse('sales_invoice_create'), data)
        self.assertEqual(response.status_code, 200) # Should re-render with error
        self.assertEqual(SalesInvoice.objects.exclude(id=100).count(), 0) # No new invoice should be created
        self.assertContains(response, 'Required quantity (11.0) for item \'Product A\' exceeds remaining quantity (10.0).')

    # HTMX Tests
    def test_autocomplete_customer(self):
        response = self.client.get(reverse('autocomplete_customer') + '?q=Test', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertIn('Test Buyer [CUST001]', response.content.decode())
        self.assertIn('<div hx-on:click="selectCustomer(this.textContent)">', response.content.decode())

    def test_get_customer_details_htmx(self):
        response = self.client.get(reverse('get_customer_details') + '?customer_id_with_name=Test%20Buyer%20[CUST001]', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        json_data = json.loads(response.content)
        self.assertEqual(json_data['address'], '123, Test Street')
        self.assertEqual(json_data['email'], '<EMAIL>')
        self.assertEqual(json_data['country_id'], self.country.id)

    def test_get_states_partial(self):
        response = self.client.get(reverse('get_states') + f'?country_id={self.country.id}&target_id=buyer-state-select', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertIn(f'<option value="{self.state.id}">{self.state.name}</option>', response.content.decode())
        self.assertIn('id="buyer-state-select"', response.content.decode())

    def test_get_cities_partial(self):
        response = self.client.get(reverse('get_cities') + f'?state_id={self.state.id}&target_id=buyer-city-select', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertIn(f'<option value="{self.city.id}">{self.city.name}</option>', response.content.decode())
        self.assertIn('id="buyer-city-select"', response.content.decode())

    def test_get_tariff_head_partial(self):
        response = self.client.get(reverse('get_tariff_head') + f'?commodity_id={self.excise_commodity.id}', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content.decode(), ExciseCommodity.get_tariff_head(self.excise_commodity.id))

    def test_goods_tab_partial_view(self):
        response = self.client.get(reverse('goods_tab_partial') + f'?poid={self.po.poid}', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_invoices/_goods_tab.html')
        self.assertIn('goods_formset', response.context)
        self.assertContains(response, 'Product A')
        self.assertContains(response, 'Product B')

```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for dynamic content:**
    *   **Customer Autocomplete:** `hx-get` on `buyer_name_search` and `cong_name_search` text inputs to `autocomplete_customer` URL, `hx-target` to a `div` for suggestions, `hx-trigger="keyup changed delay:500ms"`.
    *   **Customer Details Populate:** "Search" buttons trigger `hx-get` to `get_customer_details` URL, `hx-swap="none"` and `on htmx:afterRequest` to update Alpine.js data.
    *   **Cascading Dropdowns:** `hx-get` on Country/State selects to `get_states`/`get_cities` URLs, `hx-target` to the next dropdown, `hx-swap="outerHTML"`.
    *   **Tariff Head Update:** `hx-get` on `commodity` select to `get_tariff_head` URL, `hx-target` to `tariffHead` `span`, `hx-swap="innerHTML"`.
    *   **Form Submission:** The main form `hx-post` to `sales_invoice_create` URL, `hx-swap="outerHTML show:top"` on `body` for full page replacement on redirect or error messages at the top.
    *   **Goods Tab Content:** Loaded with `hx-get` to `goods_tab_partial` to render the initial table and formset.
*   **Alpine.js for UI state:**
    *   `x-data` on the main container to manage `activeTab` for the multi-tab interface.
    *   `@click` handlers on tab buttons to call `setActiveTab()`.
    *   `x-show` on tab content `div`s to show/hide based on `activeTab`.
    *   Functions like `populateBuyerForm`, `populateConsigneeForm`, `selectCustomer` within the Alpine.js data to receive HTMX JSON responses and update form fields.
*   **DataTables for List Views:**
    *   The `_goods_tab.html` template includes a `<table>` with `id="goodsTable"`.
    *   A `<script>` block within `_goods_tab.html` (or attached via `htmx:afterSwap` event) initializes DataTables on this table once it's loaded via HTMX.
    *   This provides client-side sorting, searching, and pagination for the dynamic goods list.
*   **DRY Template Inheritance:** All templates (`sales_invoice_form.html`, `_main_tab.html`, `_buyer_tab.html`, etc.) extend `core/base.html` (which is assumed to contain common HTML structure, CDN links for HTMX, Alpine.js, and DataTables, and Tailwind CSS configuration). No `base.html` code is included in the output.
*   **Strict Separation:** No business logic is in templates or views directly. All complex calculations, data lookups, and transactional saves are delegated to model methods. Views primarily handle HTTP requests, form instantiation, and rendering.
*   **Automated Conversion Focus:** This plan breaks down the complex ASP.NET page into modular Django components that are amenable to automated conversion. For instance, an AI tool could:
    *   Parse ASP.NET controls and infer Django form fields.
    *   Analyze `SqlDataSource` and `SqlCommand` to define Django models.
    *   Identify logical groupings (e.g., Buyer/Consignee sections, Goods GridView) to suggest Django formsets and partial templates.
    *   Translate `Session` and `QueryString` access into Django request handling.
    *   Convert `fun` class methods into Django model methods, form validators, or HTMX endpoints.

## Final Notes

This comprehensive plan provides a clear roadmap for migrating the ASP.NET Sales Invoice Details functionality to Django. By following the "fat model, thin view" principle, leveraging HTMX and Alpine.js for a modern frontend, and integrating DataTables for superior data presentation, the new Django application will be maintainable, scalable, and offer a significantly improved user experience. The emphasis on testing ensures code quality and reliability throughout the migration.