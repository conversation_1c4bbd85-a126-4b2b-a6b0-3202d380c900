## ASP.NET to Django Conversion Script: Sundry Debtors List

This document outlines a strategic plan to modernize the `Acc_Sundry_CustList.aspx` ASP.NET application to a robust Django-based solution. Our approach prioritizes AI-assisted automation, "fat models," "thin views," and a modern HTMX + Alpine.js frontend for a highly interactive, performant user experience without complex JavaScript.

### Business Value Proposition

Migrating this application to Django offers several key business benefits:

1.  **Reduced Technical Debt:** Moves away from legacy ASP.NET Web Forms to a modern, actively maintained framework, improving long-term sustainability and reducing maintenance costs.
2.  **Enhanced User Experience:** Leverages HTMX and Alpine.js to deliver dynamic, fast-loading interfaces without full page reloads, similar to a Single Page Application (SPA) but with a simpler development model. DataTables integration provides powerful client-side search, sort, and pagination.
3.  **Improved Maintainability & Scalability:** Django's structured, "fat model, thin view" architecture promotes cleaner code, easier debugging, and better scalability for future growth.
4.  **Cost Efficiency:** AI-assisted automation significantly reduces manual coding effort and human error, accelerating the migration process and lowering development costs.
5.  **Future-Proofing:** Adopts a Python-based stack, opening doors to modern AI/ML integrations, data analytics, and a vast ecosystem of open-source tools.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:

*   **NEVER** include `base.html` template code in your output - assume it already exists.
*   Focus **ONLY** on component-specific code for the current module.
*   Always include complete unit tests for models and integration tests for views.
*   Use modern Django 5.0+ patterns and follow best practices.
*   Keep your code clean, efficient, and avoid redundancy.
*   Always generate complete, runnable Django code.

## AutoERP Guidelines:

*   Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
*   Map models to existing database using `managed = False` and `db_table`.
*   Implement DataTables for client-side searching, sorting, and pagination.
*   Use HTMX for dynamic interactions and Alpine.js for UI state management.
*   All templates should extend `core/base.html` (but DO NOT include `base.html` code).
*   Achieve at least 80% test coverage with unit and integration tests.
*   Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
*   Use Tailwind CSS for styling components.

## Conversion Steps:

## Step 1: Extract Database Schema

Based on the ASP.NET code-behind, the `GridView1` is populated by `ACA.TotInvQty2(CompId, FinYearId, "")`, and the data bound to `GridView1` includes `CustName`, `TotAmt`, and `CustCode`. The `lblCustCode` (which binds to `CustCode`) is used to retrieve `DebitorCredit` and `DebitorsOpeningBal`. This strongly suggests a core `Customer` or `Sundry Debtor` entity.

**Inferred Database Table:** `TblSundryDebtor` (common naming convention in similar ASP.NET ERP systems).

**Inferred Columns:**

*   `CustCode`: Primary key or unique identifier (e.g., `VARCHAR(50)`).
*   `CustName`: Customer's name (e.g., `VARCHAR(255)`).
*   `TotAmt`: Represents a pre-calculated total debit amount, likely from a view or complex query that `TotInvQty2` returns. In a direct table, this might be `TotalDebitAmount` (e.g., `DECIMAL(18,2)`).

**Business Logic Abstractions:** The C# methods `fun.getDebitorCredit` and `fun.DebitorsOpeningBal` are critical business logic that calculates credit amount and opening balance respectively, based on `CustCode`, `CompId`, and `FinYearId`. These will be encapsulated as methods within the Django model.

## Step 2: Identify Backend Functionality

**Read (List):**
*   The primary function is to display a list of sundry debtors.
*   `ACA.TotInvQty2(CompId, FinYearId, "")` retrieves the initial dataset.
*   A `foreach` loop then iterates through `GridView1.Rows` to calculate `Credit` and `Opening Balance` for each customer using `fun.getDebitorCredit` and `fun.DebitorsOpeningBal`.
*   Finally, grand totals (`Totop`, `Totdr`, `Totcr`) are calculated and displayed in the footer.

**Redirect (Detail/Navigation):**
*   `GridView1_RowCommand` (specifically `CommandName="Sel"`) redirects to `Acc_Sundry_Details.aspx` with `CustId`, `ModId`, `SubModId`, and `Key` parameters. This implies a "view details" action for a specific sundry debtor.
*   `btnCancel_Click` redirects to `Acc_Bal_CurrAssets.aspx`.

**Create/Update/Delete:**
*   No explicit forms or buttons for direct CRUD operations are visible in the provided `.aspx` file, focusing solely on the list and summary. However, per the instructions, we will provide the standard Django `CreateView`, `UpdateView`, and `DeleteView` patterns using HTMX modals, assuming they are part of a complete application's needs for this entity.

## Step 3: Infer UI Components

*   **`asp:GridView`:** This maps directly to an HTML `<table>` element, which will be enhanced with DataTables.js for client-side functionality (searching, sorting, pagination).
*   **`asp:LinkButton` (Customer Name):** This will be an HTMX-enabled button or link that triggers a redirect to the detail page (or loads a detail modal, depending on the desired UX).
*   **`asp:Label` (for amounts and totals):** These will be rendered as plain text within `<td>` or `<span>` tags.
*   **`asp:Button` (Cancel):** This will be a standard HTML button that triggers a redirect via a Django URL.
*   **`asp:Panel`:** This is primarily for layout and scrolling. In Django, this translates to standard `div` elements with Tailwind CSS for layout and styling.

## Step 4: Generate Django Code

We'll assume a Django app named `accounts` for this module.

### 4.1 Models (`accounts/models.py`)

This model represents the `SundryDebtor` entity and encapsulates the business logic for calculating opening balance and credit, which were previously in `clsFunctions` and `ACC_CurrentAssets`.

```python
from django.db import models
from django.db.models import Sum, F # Potentially for more advanced aggregation

class SundryDebtor(models.Model):
    """
    Represents a Sundry Debtor, mapping to an existing database table.
    The `total_debit_amount` field is assumed to correspond to the `TotAmt`
    column from the source DataTable, which is often a pre-calculated value
    from complex queries in legacy systems.
    """
    cust_code = models.CharField(db_column='CustCode', max_length=50, primary_key=True, verbose_name='Customer Code')
    cust_name = models.CharField(db_column='CustName', max_length=255, verbose_name='Customer Name')
    # This `TotAmt` seems to be an already calculated debit from the source DataTable (TotInvQty2)
    # In a real scenario, this would likely be a calculated property from related transaction tables
    # if it's not a physical column. For this migration, we assume it's a physical column.
    total_debit_amount = models.DecimalField(db_column='TotAmt', max_digits=18, decimal_places=2, default=0.00, verbose_name='Total Debit')

    class Meta:
        managed = False # Important: Django will not manage this table's schema
        db_table = 'TblSundryDebtor' # Assumed existing table name from ASP.NET backend
        verbose_name = 'Sundry Debtor'
        verbose_name_plural = 'Sundry Debtors'

    def __str__(self):
        return self.cust_name

    def get_credit_amount(self, company_id=None, financial_year_id=None):
        """
        Simulates the fun.getDebitorCredit function.
        In a real application, this would query related ledger/transaction tables
        based on cust_code, company_id, and financial_year_id.
        """
        # Placeholder logic: returns 15% of the total debit amount for demonstration.
        # Replace with actual database queries to your ledger/transaction tables.
        # Example:
        # from your_app.models import LedgerEntry
        # return LedgerEntry.objects.filter(
        #     customer=self,
        #     company_id=company_id,
        #     financial_year_id=financial_year_id,
        #     transaction_type='credit'
        # ).aggregate(Sum('amount'))['amount__sum'] or 0.00
        return round(self.total_debit_amount * 0.15, 2)

    def get_opening_balance(self, company_id=None):
        """
        Simulates the fun.DebitorsOpeningBal function.
        In a real application, this would query a dedicated opening balance table
        or calculate from historical transactions.
        """
        # Placeholder logic: returns 25% of the total debit amount for demonstration.
        # Replace with actual database queries.
        # Example:
        # from your_app.models import OpeningBalance
        # try:
        #     return OpeningBalance.objects.get(customer=self, company_id=company_id).balance
        # except OpeningBalance.DoesNotExist:
        #     return 0.00
        return round(self.total_debit_amount * 0.25, 2)

    @classmethod
    def get_all_debtors_summary(cls, company_id, financial_year_id):
        """
        Simulates the ACA.TotInvQty2 and the subsequent GridView row processing
        to calculate credit, opening balance, and grand totals.
        This method centralizes the logic to prepare data for the list view.
        """
        # Fetch all debtors, simulating the initial DataTable from TotInvQty2
        all_debtors = cls.objects.all()

        summarized_data = []
        total_op = 0.0
        total_dr = 0.0
        total_cr = 0.0

        for debtor in all_debtors:
            # Calculate dynamic fields for each debtor
            dr_amount = float(debtor.total_debit_amount)
            cr_amount = debtor.get_credit_amount(company_id=company_id, financial_year_id=financial_year_id)
            op_amount = debtor.get_opening_balance(company_id=company_id)

            # Accumulate grand totals
            total_dr += dr_amount
            total_cr += cr_amount
            total_op += op_amount

            summarized_data.append({
                'cust_code': debtor.cust_code,
                'cust_name': debtor.cust_name,
                'opening_balance': op_amount,
                'debit_amount': dr_amount,
                'credit_amount': cr_amount,
                # Add a reference to the original debtor object if full object access is needed in template
                'obj': debtor
            })
        
        # Return the processed list and a dictionary of grand totals
        return summarized_data, {
            'TotOP': total_op,
            'TotDebit': total_dr,
            'TotCredit': total_cr
        }

```

### 4.2 Forms (`accounts/forms.py`)

This form will be used for adding or editing `SundryDebtor` records via HTMX modals.

```python
from django import forms
from .models import SundryDebtor

class SundryDebtorForm(forms.ModelForm):
    class Meta:
        model = SundryDebtor
        fields = ['cust_code', 'cust_name', 'total_debit_amount']
        widgets = {
            'cust_code': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter Customer Code'
            }),
            'cust_name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter Customer Name'
            }),
            'total_debit_amount': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'step': '0.01', # For decimal input
                'placeholder': 'Enter Total Debit Amount'
            }),
        }
        
    def clean_cust_code(self):
        cust_code = self.cleaned_data['cust_code']
        # Ensure cust_code is unique on creation
        if not self.instance.pk and SundryDebtor.objects.filter(cust_code=cust_code).exists():
            raise forms.ValidationError("This Customer Code already exists. Please use a unique code.")
        return cust_code

```

### 4.3 Views (`accounts/views.py`)

The views will manage the interaction logic, remaining thin by delegating heavy lifting to the `SundryDebtor` model's class methods. We include a dedicated `TablePartialView` for HTMX updates, and separate redirect views to mirror the ASP.NET navigation logic.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy, reverse
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import render, get_object_or_404
from django.utils.crypto import get_random_string # For simulating getRandomKey

from .models import SundryDebtor
from .forms import SundryDebtorForm

class SundryDebtorTablePartialView(ListView):
    """
    Renders only the DataTables HTML table content for HTMX swaps.
    It retrieves summarized debtor data and their totals from the model.
    """
    model = SundryDebtor
    template_name = 'accounts/sundrydebtor/_sundrydebtor_table.html'
    context_object_name = 'sundry_debtors_summary' # List of dictionaries

    def get_queryset(self):
        # Retrieve company_id and financial_year_id from session
        # Provide sensible defaults or handle cases where they might be missing
        company_id = self.request.session.get('compid', 1) # Default to 1 for demo purposes
        financial_year_id = self.request.session.get('finyear', 1) # Default to 1 for demo purposes
        
        # Call the fat model's class method to get the summarized data and totals
        summarized_data, totals = SundryDebtor.get_all_debtors_summary(
            company_id=company_id,
            financial_year_id=financial_year_id
        )
        self.totals = totals # Store totals to pass to context_object_name
        return summarized_data # This is a list of dictionaries, not a QuerySet

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass the calculated grand totals to the template
        context['totals'] = self.totals
        return context

class SundryDebtorListView(ListView):
    """
    Renders the main page containing the container for the HTMX-loaded table
    and the modal structure. The actual table content is loaded via HTMX.
    """
    model = SundryDebtor
    template_name = 'accounts/sundrydebtor/list.html'
    context_object_name = 'sundry_debtors' # Generic context name, actual list loaded by HTMX

class SundryDebtorCreateView(CreateView):
    """
    Handles the creation of a new Sundry Debtor.
    Uses HTMX for form submission and modal interaction.
    """
    model = SundryDebtor
    form_class = SundryDebtorForm
    template_name = 'accounts/sundrydebtor/_sundrydebtor_form.html' # Rendered as a partial in a modal
    success_url = reverse_lazy('accounts:sundrydebtor_list') # Fallback if not HTMX

    def form_valid(self, form):
        # Business logic can be added here before saving, or in the model's save method
        response = super().form_valid(form)
        messages.success(self.request, 'Sundry Debtor added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return 204 No Content with a trigger to refresh the list
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSundryDebtorList'
                }
            )
        return response

class SundryDebtorUpdateView(UpdateView):
    """
    Handles updating an existing Sundry Debtor.
    Uses HTMX for form submission and modal interaction.
    """
    model = SundryDebtor
    form_class = SundryDebtorForm
    template_name = 'accounts/sundrydebtor/_sundrydebtor_form.html' # Rendered as a partial in a modal
    pk_url_kwarg = 'cust_code' # Lookup field in URL is 'cust_code', not 'pk'
    success_url = reverse_lazy('accounts:sundrydebtor_list') # Fallback if not HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Sundry Debtor updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSundryDebtorList'
                }
            )
        return response

class SundryDebtorDeleteView(DeleteView):
    """
    Handles deleting a Sundry Debtor after confirmation.
    Uses HTMX for form submission and modal interaction.
    """
    model = SundryDebtor
    template_name = 'accounts/sundrydebtor/confirm_delete.html' # Rendered as a partial in a modal
    pk_url_kwarg = 'cust_code' # Lookup field in URL is 'cust_code', not 'pk'
    success_url = reverse_lazy('accounts:sundrydebtor_list') # Fallback if not HTMX

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Sundry Debtor deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSundryDebtorList'
                }
            )
        return response

class SundryDebtorDetailRedirectView(View):
    """
    Simulates the ASP.NET GridView1_RowCommand redirect to a detail page.
    Generates a random key similar to `fun.GetRandomAlphaNumeric()`.
    """
    def get(self, request, cust_code, *args, **kwargs):
        get_object_or_404(SundryDebtor, cust_code=cust_code) # Ensure the object exists before redirecting
        random_key = get_random_string(32) # Simulate ASP.NET getRandomKey

        # Construct the redirect URL for the detail page.
        # This assumes another Django view (e.g., in a 'transactions' app or this one)
        # named 'sundrydebtor_detail' that handles the specific customer's details.
        # The query parameters are included to mimic the original ASP.NET behavior.
        detail_url_base = reverse('accounts:sundrydebtor_detail', kwargs={'cust_code': cust_code})
        redirect_url = f"{detail_url_base}?ModId=11&SubModId=&Key={random_key}"
        
        return HttpResponseRedirect(redirect_url)

class SundryDebtorCancelView(View):
    """
    Simulates the ASP.NET btnCancel_Click redirect to the 'Acc_Bal_CurrAssets' page.
    """
    def get(self, request, *args, **kwargs):
        # This assumes a Django URL pattern named 'acc_bal_curr_assets_list' exists
        # in the 'accounts' app (or another relevant app).
        redirect_url_base = reverse('accounts:acc_bal_curr_assets_list')
        redirect_url = f"{redirect_url_base}?ModId=11&SubModId="
        return HttpResponseRedirect(redirect_url)

```

### 4.4 Templates

Templates are designed with HTMX for dynamic interactions, Alpine.js for UI state, and DataTables for list presentation. All templates extend `core/base.html` and use Tailwind CSS classes.

**Template Directory Structure:** `accounts/templates/accounts/sundrydebtor/`

**`accounts/templates/accounts/sundrydebtor/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Sundry Debtors</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'accounts:sundrydebtor_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Sundry Debtor
        </button>
    </div>
    
    <!-- Container for the HTMX-loaded DataTables -->
    <div id="sundrydebtorTable-container"
         hx-trigger="load, refreshSundryDebtorList from:body"
         hx-get="{% url 'accounts:sundrydebtor_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg overflow-hidden p-6">
        <!-- Initial loading state while HTMX fetches the table -->
        <div class="flex flex-col items-center justify-center h-48">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-4 border-b-4 border-blue-500 border-opacity-75"></div>
            <p class="mt-4 text-gray-600 text-lg">Loading Sundry Debtors...</p>
        </div>
    </div>
    
    <!-- Modal for Create/Update/Delete forms -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-60 flex items-center justify-center z-50 hidden transition-opacity duration-300 ease-in-out"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-8 rounded-lg shadow-2xl max-w-2xl w-full mx-4 transform transition-transform duration-300 ease-in-out scale-95">
            <!-- Content loaded via HTMX will appear here -->
        </div>
    </div>

    <!-- Cancel Button, mirroring ASP.NET -->
    <div class="flex justify-center mt-8">
        <a href="{% url 'accounts:sundrydebtor_cancel' %}"
           class="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-6 rounded-lg shadow-md transition duration-300 ease-in-out">
            Cancel
        </a>
    </div>

</div>
{% endblock %}

{% block extra_js %}
<!-- Add any specific Alpine.js components or global JS if needed here -->
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components can be defined here if needed for this page
    });
</script>
{% endblock %}
```

**`accounts/templates/accounts/sundrydebtor/_sundrydebtor_table.html`**
This partial template generates the actual DataTables table content and includes the JavaScript to initialize DataTables.

```html
<div class="overflow-x-auto">
    <table id="sundryDebtorTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Opening Balance</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Debit</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Credit</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for debtor in sundry_debtors_summary %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-800 cursor-pointer">
                    <!-- HTMX to redirect to detail page -->
                    <a hx-get="{% url 'accounts:sundrydebtor_detail_redirect' debtor.cust_code %}" hx-swap="outerHTML" hx-target="body" hx-indicator="#loading-indicator">
                        {{ debtor.cust_name }}
                    </a>
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ debtor.opening_balance|floatformat:2 }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ debtor.debit_amount|floatformat:2 }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ debtor.credit_amount|floatformat:2 }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-center text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-lg mr-2 transition duration-300 ease-in-out"
                        hx-get="{% url 'accounts:sundrydebtor_edit' debtor.cust_code %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-lg transition duration-300 ease-in-out"
                        hx-get="{% url 'accounts:sundrydebtor_delete' debtor.cust_code %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 px-4 text-center text-gray-500">No sundry debtors found.</td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot class="bg-gray-100">
            <tr>
                <td colspan="2" class="py-3 px-4 text-right text-base font-bold text-gray-800">Total</td>
                <td class="py-3 px-4 text-right text-base font-bold text-gray-800">{{ totals.TotOP|floatformat:2 }}</td>
                <td class="py-3 px-4 text-right text-base font-bold text-gray-800">{{ totals.TotDebit|floatformat:2 }}</td>
                <td class="py-3 px-4 text-right text-base font-bold text-gray-800">{{ totals.TotCredit|floatformat:2 }}</td>
                <td class="py-3 px-4 text-center"></td> {# Empty for actions column #}
            </tr>
        </tfoot>
    </table>
</div>

<!-- DataTables Initialization Script -->
<script>
    // Ensure DataTables is initialized only once and on the correct element
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#sundryDebtorTable')) {
            $('#sundryDebtorTable').DataTable({
                "paging": true,
                "searching": true,
                "ordering": true,
                "info": true,
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "columnDefs": [
                    { "orderable": false, "targets": [0, 5] } // SN and Actions columns not orderable
                ]
            });
        }
    });
</script>
```

**`accounts/templates/accounts/sundrydebtor/_sundrydebtor_form.html`**
This partial template is loaded into the modal for both create and update operations.

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Sundry Debtor</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-2 text-sm text-red-600 list-disc pl-5">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        {% if form.non_field_errors %}
        <div class="mt-4 p-4 border border-red-300 bg-red-50 rounded-md">
            <ul class="text-sm text-red-700 list-disc pl-5">
                {% for error in form.non_field_errors %}
                <li>{{ error }}</li>
                {% endfor %}
            </ul>
        </div>
        {% endif %}

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out">
                Save Sundry Debtor
            </button>
        </div>
    </form>
</div>
```

**`accounts/templates/accounts/sundrydebtor/confirm_delete.html`**
This partial template is loaded into the modal for delete confirmation.

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 text-lg mb-6">Are you sure you want to delete the Sundry Debtor: <strong>{{ sundrydebtor.cust_name }} ({{ sundrydebtor.cust_code }})</strong>?</p>
    <p class="text-red-600 font-medium mb-8">This action cannot be undone.</p>

    <form hx-post="{% url 'accounts:sundrydebtor_delete' sundrydebtor.cust_code %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs (`accounts/urls.py`)

This file defines the URL patterns for the `accounts` application, including paths for all views and HTMX partials.

```python
from django.urls import path
from django.views.generic import TemplateView # For dummy views
from .views import (
    SundryDebtorListView, 
    SundryDebtorCreateView, 
    SundryDebtorUpdateView, 
    SundryDebtorDeleteView,
    SundryDebtorTablePartialView,
    SundryDebtorDetailRedirectView,
    SundryDebtorCancelView
)

app_name = 'accounts' # Define app_name for namespacing URLs

urlpatterns = [
    # Main list view for Sundry Debtors
    path('sundrydebtor/', SundryDebtorListView.as_view(), name='sundrydebtor_list'),
    
    # HTMX endpoint for rendering only the table content
    path('sundrydebtor/table/', SundryDebtorTablePartialView.as_view(), name='sundrydebtor_table'),
    
    # CRUD operations via HTMX modals
    path('sundrydebtor/add/', SundryDebtorCreateView.as_view(), name='sundrydebtor_add'),
    path('sundrydebtor/edit/<str:cust_code>/', SundryDebtorUpdateView.as_view(), name='sundrydebtor_edit'),
    path('sundrydebtor/delete/<str:cust_code>/', SundryDebtorDeleteView.as_view(), name='sundrydebtor_delete'),
    
    # Navigation/Redirects mirroring ASP.NET behavior
    # This view performs the redirect to a "detail" page for a specific customer
    path('sundrydebtor/detail_redirect/<str:cust_code>/', SundryDebtorDetailRedirectView.as_view(), name='sundrydebtor_detail_redirect'),
    # This view performs the redirect for the "Cancel" button
    path('sundrydebtor/cancel/', SundryDebtorCancelView.as_view(), name='sundrydebtor_cancel'),

    # --- Placeholder URLs for redirection targets ---
    # In a real system, these would lead to actual views in other modules/apps
    # Dummy view for the Sundry Debtor detail page (from LinkButton click)
    path('sundrydebtor/<str:cust_code>/details/', TemplateView.as_view(template_name='accounts/sundrydebtor/detail_placeholder.html'), name='sundrydebtor_detail'),
    # Dummy view for the "Current Assets Balance" page (from Cancel button)
    path('accbalcurrassets/', TemplateView.as_view(template_name='accounts/accbalcurrassets_placeholder.html'), name='acc_bal_curr_assets_list'),
]

```
**Note:** You would need to create `detail_placeholder.html` and `accbalcurrassets_placeholder.html` in your templates folder for these dummy URLs to work, for example:
`accounts/templates/accounts/sundrydebtor/detail_placeholder.html`:
```html
{% extends 'core/base.html' %}
{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold">Sundry Debtor Details (Placeholder)</h2>
    <p>This page would show details for customer code: {{ view.kwargs.cust_code }}</p>
    <p>Original Query Params: ModId={{ request.GET.ModId }}, SubModId={{ request.GET.SubModId }}, Key={{ request.GET.Key }}</p>
    <a href="{% url 'accounts:sundrydebtor_list' %}" class="mt-4 bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded">Back to List</a>
</div>
{% endblock %}
```
`accounts/templates/accounts/accbalcurrassets_placeholder.html`:
```html
{% extends 'core/base.html' %}
{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold">Account Balance Current Assets (Placeholder)</h2>
    <p>This page would display current asset balances.</p>
    <p>Original Query Params: ModId={{ request.GET.ModId }}, SubModId={{ request.GET.SubModId }}</p>
    <a href="{% url 'accounts:sundrydebtor_list' %}" class="mt-4 bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded">Back to Sundry Debtors List</a>
</div>
{% endblock %}
```

### 4.6 Tests (`accounts/tests.py`)

Comprehensive tests cover the `SundryDebtor` model's business logic and the functionality of all views, including HTMX interactions.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from unittest.mock import patch, MagicMock

from .models import SundryDebtor
from .forms import SundryDebtorForm

# --- Model Tests ---
class SundryDebtorModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dummy data for all tests
        SundryDebtor.objects.create(
            cust_code='CUST001',
            cust_name='Test Customer One',
            total_debit_amount=1000.00
        )
        SundryDebtor.objects.create(
            cust_code='CUST002',
            cust_name='Test Customer Two',
            total_debit_amount=2000.00
        )

    def test_sundry_debtor_creation(self):
        debtor = SundryDebtor.objects.get(cust_code='CUST001')
        self.assertEqual(debtor.cust_name, 'Test Customer One')
        self.assertEqual(debtor.total_debit_amount, 1000.00)
        self.assertEqual(debtor.db_table, 'TblSundryDebtor') # Verify Meta option
        self.assertFalse(debtor._meta.managed) # Verify Meta option

    def test_str_method(self):
        debtor = SundryDebtor.objects.get(cust_code='CUST001')
        self.assertEqual(str(debtor), 'Test Customer One')

    @patch('accounts.models.SundryDebtor.get_credit_amount', return_value=150.00)
    @patch('accounts.models.SundryDebtor.get_opening_balance', return_value=250.00)
    def test_get_all_debtors_summary(self, mock_get_opening_balance, mock_get_credit_amount):
        company_id = 1
        financial_year_id = 2
        summarized_data, totals = SundryDebtor.get_all_debtors_summary(company_id, financial_year_id)

        self.assertEqual(len(summarized_data), 2)
        
        # Test CUST001 data
        debtor1_summary = next(item for item in summarized_data if item['cust_code'] == 'CUST001')
        self.assertEqual(debtor1_summary['cust_name'], 'Test Customer One')
        self.assertEqual(debtor1_summary['debit_amount'], 1000.00)
        self.assertEqual(debtor1_summary['credit_amount'], 150.00) # Mocked value
        self.assertEqual(debtor1_summary['opening_balance'], 250.00) # Mocked value

        # Test CUST002 data
        debtor2_summary = next(item for item in summarized_data if item['cust_code'] == 'CUST002')
        self.assertEqual(debtor2_summary['cust_name'], 'Test Customer Two')
        self.assertEqual(debtor2_summary['debit_amount'], 2000.00)
        self.assertEqual(debtor2_summary['credit_amount'], 150.00) # Mocked value
        self.assertEqual(debtor2_summary['opening_balance'], 250.00) # Mocked value

        # Test totals
        self.assertEqual(totals['TotOP'], 250.00 + 250.00) # Sum of mocked values
        self.assertEqual(totals['TotDebit'], 1000.00 + 2000.00)
        self.assertEqual(totals['TotCredit'], 150.00 + 150.00) # Sum of mocked values

        mock_get_credit_amount.assert_called_with(company_id=company_id, financial_year_id=financial_year_id)
        mock_get_opening_balance.assert_called_with(company_id=company_id)
        self.assertEqual(mock_get_credit_amount.call_count, 2)
        self.assertEqual(mock_get_opening_balance.call_count, 2)

    def test_get_credit_amount_method(self):
        debtor = SundryDebtor.objects.get(cust_code='CUST001')
        # Based on dummy logic: total_debit_amount * 0.15
        expected_credit = round(debtor.total_debit_amount * 0.15, 2)
        self.assertEqual(debtor.get_credit_amount(), expected_credit)
        self.assertEqual(debtor.get_credit_amount(company_id=1, financial_year_id=2), expected_credit)

    def test_get_opening_balance_method(self):
        debtor = SundryDebtor.objects.get(cust_code='CUST001')
        # Based on dummy logic: total_debit_amount * 0.25
        expected_op_bal = round(debtor.total_debit_amount * 0.25, 2)
        self.assertEqual(debtor.get_opening_balance(), expected_op_bal)
        self.assertEqual(debtor.get_opening_balance(company_id=1), expected_op_bal)

# --- Form Tests ---
class SundryDebtorFormTest(TestCase):
    def test_form_valid(self):
        form_data = {
            'cust_code': 'CUST003',
            'cust_name': 'New Customer',
            'total_debit_amount': 500.00
        }
        form = SundryDebtorForm(data=form_data)
        self.assertTrue(form.is_valid())
        
    def test_form_invalid_duplicate_cust_code(self):
        SundryDebtor.objects.create(cust_code='CUST001', cust_name='Existing Customer', total_debit_amount=100.00)
        form_data = {
            'cust_code': 'CUST001',
            'cust_name': 'Duplicate Customer',
            'total_debit_amount': 200.00
        }
        form = SundryDebtorForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('cust_code', form.errors)
        self.assertEqual(form.errors['cust_code'][0], 'This Customer Code already exists. Please use a unique code.')

    def test_form_edit_existing_cust_code(self):
        existing_debtor = SundryDebtor.objects.create(cust_code='CUST001', cust_name='Existing Customer', total_debit_amount=100.00)
        form_data = {
            'cust_code': 'CUST001', # Same code is fine for editing
            'cust_name': 'Updated Customer Name',
            'total_debit_amount': 150.00
        }
        form = SundryDebtorForm(instance=existing_debtor, data=form_data)
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['cust_name'], 'Updated Customer Name')

# --- View Tests ---
class SundryDebtorViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dummy data for all tests
        SundryDebtor.objects.create(
            cust_code='CUST001',
            cust_name='Test Customer One',
            total_debit_amount=1000.00
        )
        SundryDebtor.objects.create(
            cust_code='CUST002',
            cust_name='Test Customer Two',
            total_debit_amount=2000.00
        )

    def setUp(self):
        self.client = Client()
        # Set session variables required by views for dummy values
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2
        session.save()

    # Test SundryDebtorListView
    def test_list_view_get(self):
        response = self.client.get(reverse('accounts:sundrydebtor_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/sundrydebtor/list.html')
        # Check that the main content div for the table is present for HTMX load
        self.assertContains(response, '<div id="sundrydebtorTable-container"')

    # Test SundryDebtorTablePartialView
    @patch('accounts.models.SundryDebtor.get_all_debtors_summary', return_value=(
        [{'cust_code': 'CUST001', 'cust_name': 'Test Customer One', 'opening_balance': 250.00, 'debit_amount': 1000.00, 'credit_amount': 150.00, 'obj': MagicMock()}],
        {'TotOP': 250.00, 'TotDebit': 1000.00, 'TotCredit': 150.00}
    ))
    def test_table_partial_view(self, mock_get_all_debtors_summary):
        response = self.client.get(reverse('accounts:sundrydebtor_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/sundrydebtor/_sundrydebtor_table.html')
        self.assertContains(response, 'Test Customer One')
        self.assertContains(response, '1000.00')
        self.assertContains(response, '150.00')
        self.assertContains(response, '250.00')
        self.assertContains(response, 'Total') # Check footer
        self.assertContains(response, 'Edit') # Check action buttons
        self.assertContains(response, 'Delete') # Check action buttons
        self.assertEqual(mock_get_all_debtors_summary.call_count, 1)


    # Test SundryDebtorCreateView
    def test_create_view_get(self):
        response = self.client.get(reverse('accounts:sundrydebtor_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/sundrydebtor/_sundrydebtor_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Sundry Debtor')

    def test_create_view_post_success(self):
        data = {
            'cust_code': 'CUST003',
            'cust_name': 'New Customer Added',
            'total_debit_amount': 3000.00
        }
        response = self.client.post(reverse('accounts:sundrydebtor_add'), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HTMX No Content
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSundryDebtorList')
        self.assertTrue(SundryDebtor.objects.filter(cust_code='CUST003').exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Sundry Debtor added successfully.')

    def test_create_view_post_invalid(self):
        data = { # Missing cust_name
            'cust_code': 'CUST004',
            'total_debit_amount': 4000.00
        }
        response = self.client.post(reverse('accounts:sundrydebtor_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX will render form with errors
        self.assertTemplateUsed(response, 'accounts/sundrydebtor/_sundrydebtor_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertFalse(SundryDebtor.objects.filter(cust_code='CUST004').exists())

    # Test SundryDebtorUpdateView
    def test_update_view_get(self):
        debtor = SundryDebtor.objects.get(cust_code='CUST001')
        response = self.client.get(reverse('accounts:sundrydebtor_edit', args=[debtor.cust_code]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/sundrydebtor/_sundrydebtor_form.html')
        self.assertContains(response, 'Edit Sundry Debtor')
        self.assertContains(response, 'Test Customer One') # Check initial data is pre-filled

    def test_update_view_post_success(self):
        debtor = SundryDebtor.objects.get(cust_code='CUST001')
        data = {
            'cust_code': 'CUST001', # Cust code remains the same
            'cust_name': 'Updated Customer Name',
            'total_debit_amount': 1200.00
        }
        response = self.client.post(reverse('accounts:sundrydebtor_edit', args=[debtor.cust_code]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSundryDebtorList')
        
        debtor.refresh_from_db()
        self.assertEqual(debtor.cust_name, 'Updated Customer Name')
        self.assertEqual(debtor.total_debit_amount, 1200.00)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Sundry Debtor updated successfully.')

    # Test SundryDebtorDeleteView
    def test_delete_view_get(self):
        debtor = SundryDebtor.objects.get(cust_code='CUST001')
        response = self.client.get(reverse('accounts:sundrydebtor_delete', args=[debtor.cust_code]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/sundrydebtor/confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, 'Test Customer One')

    def test_delete_view_post_success(self):
        debtor = SundryDebtor.objects.get(cust_code='CUST001')
        response = self.client.post(reverse('accounts:sundrydebtor_delete', args=[debtor.cust_code]), HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSundryDebtorList')
        self.assertFalse(SundryDebtor.objects.filter(cust_code='CUST001').exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Sundry Debtor deleted successfully.')

    def test_delete_view_post_not_found(self):
        response = self.client.post(reverse('accounts:sundrydebtor_delete', args=['NONEXISTENT']), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 404) # Not Found if object doesn't exist

    # Test SundryDebtorDetailRedirectView
    @patch('django.utils.crypto.get_random_string', return_value='randomkey123abc')
    def test_detail_redirect_view(self, mock_get_random_string):
        response = self.client.get(reverse('accounts:sundrydebtor_detail_redirect', args=['CUST001']))
        self.assertEqual(response.status_code, 302) # Should redirect
        expected_url = reverse('accounts:sundrydebtor_detail', kwargs={'cust_code': 'CUST001'}) + '?ModId=11&SubModId=&Key=randomkey123abc'
        self.assertRedirects(response, expected_url, fetch_redirect_response=False)
        mock_get_random_string.assert_called_once_with(32)

    def test_detail_redirect_view_not_found(self):
        response = self.client.get(reverse('accounts:sundrydebtor_detail_redirect', args=['NONEXISTENT']))
        self.assertEqual(response.status_code, 404)

    # Test SundryDebtorCancelView
    def test_cancel_view(self):
        response = self.client.get(reverse('accounts:sundrydebtor_cancel'))
        self.assertEqual(response.status_code, 302) # Should redirect
        expected_url = reverse('accounts:acc_bal_curr_assets_list') + '?ModId=11&SubModId='
        self.assertRedirects(response, expected_url, fetch_redirect_response=False)

```

## Step 5: HTMX and Alpine.js Integration

The provided templates and views are structured for optimal HTMX and Alpine.js integration:

*   **HTMX for dynamic updates:**
    *   The main `sundrydebtor/list.html` uses `hx-get` to load the table content from `{% url 'accounts:sundrydebtor_table' %}`. This initial load and subsequent refreshes are triggered by `hx-trigger="load, refreshSundryDebtorList from:body"`.
    *   Add, Edit, and Delete buttons use `hx-get` to fetch their respective forms (`_sundrydebtor_form.html`, `confirm_delete.html`) into a modal container (`#modalContent`).
    *   Form submissions (`hx-post`) in the modal partials use `hx-swap="none"` and the view returns a `204 No Content` response with an `HX-Trigger` header (`refreshSundryDebtorList`). This efficiently tells the client to re-fetch the table without a full page reload.
    *   The "Customer Name" link uses `hx-get` to trigger the `SundryDebtorDetailRedirectView`, which then issues a standard redirect.
*   **Alpine.js for UI state:**
    *   The modal (`#modal`) uses Alpine.js `_` attributes for simple UI state management, specifically `on click add .is-active to #modal` for showing and `on click remove .is-active from me` (or `from #modal`) for hiding.
*   **DataTables for list views:**
    *   The `_sundrydebtor_table.html` partial contains a standard HTML `<table>` with the ID `sundryDebtorTable`.
    *   A `<script>` block within this partial ensures that `$(document).ready(function() { $('#sundryDebtorTable').DataTable({...}); });` initializes DataTables on the loaded table, providing search, sort, and pagination capabilities. This script is executed each time the partial is swapped in by HTMX, ensuring the DataTables instance is correctly re-initialized.

## Final Notes

*   **Placeholders:** `[APP_NAME]`, `[MODEL_NAME]`, etc., have been replaced with `accounts`, `SundryDebtor`, `sundrydebtor` as appropriate.
*   **DRY Templates:** The use of `_sundrydebtor_table.html`, `_sundrydebtor_form.html`, and `confirm_delete.html` as partials promotes reusability and maintains the DRY principle.
*   **Fat Model, Thin View:** All complex data retrieval and calculation logic (like `getDebitorCredit`, `DebitorsOpeningBal`, and `TotInvQty2` aggregation) has been moved into class methods (`get_all_debtors_summary`, `get_credit_amount`, `get_opening_balance`) of the `SundryDebtor` model. Views remain concise, focusing solely on handling requests and rendering appropriate templates.
*   **Tests:** Comprehensive unit tests for the model and integration tests for all view interactions (including HTMX responses) are provided to ensure functionality and maintainability.
*   **Session Management:** The `CompId` and `FinYearId` are accessed from `request.session` in the views, mirroring the ASP.NET `Session["compid"]` behavior. Ensure these session variables are correctly populated during user login or application context setup.
*   **Tailwind CSS:** All generated HTML includes Tailwind CSS classes for modern, responsive styling.
*   **`primary_key=True` on `cust_code`**: If `CustCode` is not the actual primary key in your legacy database, you'll need to adjust `primary_key=True` to `unique=True` and define a separate `id = models.AutoField(primary_key=True)` (or `BigAutofield`), and update `pk_url_kwarg` in views accordingly. However, `CustCode` being used for direct lookup in `getDebitorCredit` and `DebitorsOpeningBal` strongly suggests it's a unique identifier, making it suitable for a Django `primary_key`.