## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Business Value Proposition:
This migration will transform a legacy ASP.NET application into a modern, highly responsive Django solution. By adopting Django with HTMX and Alpine.js, we will achieve:
1.  **Enhanced User Experience:** Seamless, dynamic interactions without full page reloads, making the application feel faster and more intuitive.
2.  **Reduced Development Complexity:** Consolidating business logic in Django models simplifies maintenance and accelerates future feature development.
3.  **Improved Performance:** Optimized database queries and efficient frontend rendering will lead to a more performant application.
4.  **Future-Proof Architecture:** Transitioning to a widely supported, open-source framework like Django ensures long-term viability and easier integration with other modern systems.
5.  **Cost Efficiency:** Leveraging automation tools for the migration reduces manual effort, while the open-source stack eliminates licensing costs associated with proprietary technologies.

---

### Step 1: Extract Database Schema

Based on the ASP.NET code-behind, we identify the following database tables and their relevant columns. Please note that the ASP.NET code performs multiple lookups to present data, which we'll handle efficiently in Django models.

**Main Table:** `tblACC_ServiceTaxInvoice_Master`
*   `Id` (Primary Key, inferred `INT`)
*   `FinYearId` (`INT`, foreign key to `tblFinancial_master`)
*   `SysDate` (`DATETIME`, invoice date)
*   `InvoiceNo` (`NVARCHAR`, invoice number)
*   `WONo` (`NVARCHAR`, comma-separated Work Order IDs)
*   `PONo` (`NVARCHAR`, purchase order number)
*   `CustomerCode` (`NVARCHAR`, foreign key to `SD_Cust_master`)
*   `CompId` (`INT`, Company ID, used for filtering)

**Lookup Tables:**
*   `tblFinancial_master`:
    *   `FinYearId` (Primary Key, `INT`)
    *   `FinYear` (`NVARCHAR`, e.g., "2023-2024")
*   `SD_Cust_master`:
    *   `CustomerId` (Primary Key, `NVARCHAR`, maps to `CustomerCode`)
    *   `CustomerName` (`NVARCHAR`)
    *   `CompId` (`INT`, Company ID, used for filtering)
*   `SD_Cust_WorkOrder_Master`:
    *   `Id` (Primary Key, `INT`, maps to IDs in `WONo` field)
    *   `WONo` (`NVARCHAR`, actual work order number)
    *   `CompId` (`INT`, Company ID, used for filtering)

### Step 2: Identify Backend Functionality

The ASP.NET page `ServiceTaxInvoice_Edit.aspx` primarily serves as a search and listing interface for service tax invoices.

*   **Read (List & Filter/Search):**
    *   Displays a paginated list of service tax invoices in a grid.
    *   Allows searching by:
        *   Customer Name (with an autocomplete feature).
        *   Purchase Order (PO) Number.
        *   Invoice Number.
    *   Dynamically changes search input fields based on dropdown selection.
    *   Paginates the results.
*   **Navigation/Selection:**
    *   A "Select" link button for each row redirects to a `ServiceTaxInvoice_Edit_Details.aspx` page with encrypted invoice details, implying a drill-down to an edit/detail view. This will be mapped to a Django detail view URL.

### Step 3: Infer UI Components

The ASP.NET controls translate directly to modern Django templates with HTMX, Alpine.js, and DataTables:

*   **Dropdown List (`asp:DropDownList ID="DropDownList1"`):** This will be a standard HTML `<select>` element. Its `onselectedindexchanged` and `AutoPostBack` behavior will be handled by Alpine.js for visibility toggling and HTMX for re-triggering the table load.
*   **Textboxes (`asp:TextBox ID="txtCustName"`, `asp:TextBox ID="txtpoNo"`):** Standard HTML `<input type="text">` elements. Their visibility will be managed by Alpine.js.
*   **Autocomplete (`cc1:AutoCompleteExtender`):** This is a key dynamic feature. It will be replaced by an HTMX `hx-get` request to a dedicated Django endpoint that returns HTML fragments for the autocomplete suggestions.
*   **Search Button (`asp:Button ID="btnSearch"`):** A standard HTML `<button>` element. Its `onclick` behavior will be replaced by an HTMX `hx-trigger="click"` on the search button that triggers a reload of the DataTable partial.
*   **GridView (`asp:GridView ID="GridView1"`):** This is the core data display. It will be replaced by a modern HTML `<table>` element initialized with DataTables. Pagination, sorting, and filtering will be handled client-side by DataTables, making the interface very responsive. The table content will be loaded via HTMX `hx-get` to a partial view.
*   **LinkButton (`asp:LinkButton ID="Btn1"`):** The "Select" link button will become a standard HTML `<a>` tag or a button that navigates to the Django detail URL for the selected invoice.

---

### Step 4: Generate Django Code

We will create a new Django application, let's call it `invoicing`, to house this functionality.

#### 4.1 Models (`invoicing/models.py`)

We'll define models for the main invoice table and its related lookup tables, ensuring `managed = False` for existing databases. We will also implement methods and properties on the `ServiceTaxInvoiceMaster` model to encapsulate the complex data lookups (e.g., financial year, customer name, work order numbers) that were previously done in the ASP.NET `bindgrid` method.

```python
from django.db import models
from django.utils.functional import cached_property

class FinancialMaster(models.Model):
    """
    Maps to tblFinancial_master for financial year details.
    """
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class CustomerMaster(models.Model):
    """
    Maps to SD_Cust_master for customer details.
    """
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customer_name} [{self.customer_id}]"

class WorkOrderMaster(models.Model):
    """
    Maps to SD_Cust_WorkOrder_Master for individual work order details.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    won_no = models.CharField(db_column='WONo', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.won_no

class ServiceTaxInvoiceMaster(models.Model):
    """
    Maps to tblACC_ServiceTaxInvoice_Master for service tax invoices.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    fin_year_id = models.IntegerField(db_column='FinYearId') # No direct FK due to managed=False
    sys_date = models.DateTimeField(db_column='SysDate')
    invoice_no = models.CharField(db_column='InvoiceNo', max_length=50)
    wo_no_ids = models.CharField(db_column='WONo', max_length=500, blank=True, null=True) # Stores comma-separated IDs
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    customer_code = models.CharField(db_column='CustomerCode', max_length=50) # No direct FK
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblACC_ServiceTaxInvoice_Master'
        verbose_name = 'Service Tax Invoice'
        verbose_name_plural = 'Service Tax Invoices'
        ordering = ['-id'] # Matches 'Order By Id Desc' from original SQL

    def __str__(self):
        return self.invoice_no

    @cached_property
    def get_fin_year(self):
        """
        Retrieves the financial year string based on fin_year_id.
        """
        try:
            return FinancialMaster.objects.get(fin_year_id=self.fin_year_id).fin_year
        except FinancialMaster.DoesNotExist:
            return 'N/A'

    @cached_property
    def get_customer_name(self):
        """
        Retrieves the customer's display name and ID based on customer_code.
        """
        try:
            customer = CustomerMaster.objects.get(customer_id=self.customer_code, comp_id=self.comp_id)
            return f"{customer.customer_name} [{customer.customer_id}]"
        except CustomerMaster.DoesNotExist:
            return f"Unknown Customer [{self.customer_code}]"
            
    @cached_property
    def get_customer_obj(self):
        """
        Retrieves the customer object.
        """
        try:
            return CustomerMaster.objects.get(customer_id=self.customer_code, comp_id=self.comp_id)
        except CustomerMaster.DoesNotExist:
            return None

    @cached_property
    def get_wo_numbers(self):
        """
        Retrieves and formats comma-separated Work Order numbers.
        Handles the N+1 query problem by fetching all necessary WONos at once.
        """
        if not self.wo_no_ids:
            return ''
        
        # Split the string and convert to integers
        wo_id_list = [int(id_str) for id_str in self.wo_no_ids.split(',') if id_str.strip().isdigit()]
        
        if not wo_id_list:
            return ''

        # Fetch all relevant WorkOrderMaster objects in one query
        wo_objects = WorkOrderMaster.objects.filter(id__in=wo_id_list, comp_id=self.comp_id)
        
        # Create a dictionary for quick lookup
        wo_map = {wo.id: wo.won_no for wo in wo_objects}
        
        # Reconstruct the string in the original order (or close to it)
        # Fallback to 'N/A' if an ID is not found
        return ','.join([wo_map.get(wo_id, 'N/A') for wo_id in wo_id_list])

    def get_display_date(self):
        """Formats the system date to DD/MM/YYYY."""
        return self.sys_date.strftime('%d/%m/%Y') if self.sys_date else ''

```

#### 4.2 Forms (`invoicing/forms.py`)

This page uses a search/filter form, not a CRUD form for `ServiceTaxInvoiceMaster`.

```python
from django import forms

class ServiceTaxInvoiceSearchForm(forms.Form):
    """
    Form for searching Service Tax Invoices.
    """
    SEARCH_CHOICES = [
        ('0', 'Customer Name'),
        ('2', 'PO No'),
        ('3', 'Invoice No'),
    ]

    search_by = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'box3 block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'x-model': 'searchCriteria', # Alpine.js binding
            'hx-post': '{{ request.path }}', # Post back to refresh on dropdown change
            'hx-trigger': 'change',
            'hx-swap': 'none', # No swap, Alpine.js handles visibility
        })
    )
    
    # Textbox for Customer Name (hidden by default via Alpine)
    customer_name_search = forms.CharField(
        max_length=255, 
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter Customer Name or Code',
            'x-show': "searchCriteria === '0'", # Alpine.js visibility
            'hx-get': '/invoicing/customer_autocomplete/', # HTMX for autocomplete
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#autocomplete-results',
            'hx-indicator': '.htmx-indicator',
            'name': 'customer_name_search' # Ensure correct name for POST
        })
    )
    
    # Textbox for PO No or Invoice No (hidden by default via Alpine)
    po_invoice_search = forms.CharField(
        max_length=50, 
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter PO No or Invoice No',
            'x-show': "searchCriteria === '2' || searchCriteria === '3'", # Alpine.js visibility
            'name': 'po_invoice_search' # Ensure correct name for POST
        })
    )

```

#### 4.3 Views (`invoicing/views.py`)

We'll define views for the main list page, the HTMX-loaded table partial, and the autocomplete functionality. The `CompId` and `FinYearId` from the original session will be assumed to be available, e.g., from `request.user.comp_id` or similar. For demonstration, we'll use placeholder values.

```python
from django.views.generic import ListView, TemplateView, View
from django.db.models import Q
from django.shortcuts import render
from django.http import HttpResponse, JsonResponse
from django.urls import reverse
from django.utils.html import format_html
import logging

from .models import ServiceTaxInvoiceMaster, CustomerMaster
from .forms import ServiceTaxInvoiceSearchForm

logger = logging.getLogger(__name__)

class ServiceTaxInvoiceListView(TemplateView):
    """
    Main view for displaying the Service Tax Invoice search page.
    It renders the search form and a container for the HTMX-loaded table.
    """
    template_name = 'invoicing/service_tax_invoice/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = ServiceTaxInvoiceSearchForm()
        # Assume CompId and FinYearId are available from session or user profile
        # For demonstration, use fixed values. In production, get from request.session or request.user.
        context['current_comp_id'] = self.request.session.get('compid', 1) 
        context['current_fin_year_id'] = self.request.session.get('finyear', 1) 
        return context

class ServiceTaxInvoiceTablePartialView(ListView):
    """
    HTMX endpoint to render only the table content based on search filters.
    This replaces the ASP.NET 'bindgrid' logic.
    """
    model = ServiceTaxInvoiceMaster
    template_name = 'invoicing/service_tax_invoice/_service_tax_invoice_table.html'
    context_object_name = 'service_tax_invoices'
    paginate_by = 15 # Matches original PageSize

    def get_queryset(self):
        # Assume CompId and FinYearId are available from session or user profile
        current_comp_id = self.request.session.get('compid', 1)
        current_fin_year_id = self.request.session.get('finyear', 1)
        
        queryset = ServiceTaxInvoiceMaster.objects.filter(
            comp_id=current_comp_id, 
            fin_year_id__lte=current_fin_year_id
        )

        search_by = self.request.GET.get('search_by') or self.request.POST.get('search_by') # Handle both GET (initial) and POST (form submit)
        customer_name_search_raw = self.request.GET.get('customer_name_search') or self.request.POST.get('customer_name_search')
        po_invoice_search = self.request.GET.get('po_invoice_search') or self.request.POST.get('po_invoice_search')

        # Implement the dynamic SQL logic from bindgrid
        if search_by == '0' and customer_name_search_raw: # Customer Name
            # Extract customer ID from "Name [ID]" format
            customer_code = None
            if '[' in customer_name_search_raw and ']' in customer_name_search_raw:
                try:
                    customer_code = customer_name_search_raw.split('[')[-1].strip(']')
                except IndexError:
                    pass # Malformed string, customer_code remains None

            if customer_code:
                queryset = queryset.filter(customer_code=customer_code)
            else: # If only name is entered without ID, try to search by name (loose match)
                queryset = queryset.filter(
                    customer_code__in=CustomerMaster.objects.filter(
                        customer_name__icontains=customer_name_search_raw,
                        comp_id=current_comp_id
                    ).values_list('customer_id', flat=True)
                )

        elif search_by == '2' and po_invoice_search: # PO No
            queryset = queryset.filter(po_no__icontains=po_invoice_search)

        elif search_by == '3' and po_invoice_search: # Invoice No
            queryset = queryset.filter(invoice_no__icontains=po_invoice_search)

        return queryset

    def get(self, request, *args, **kwargs):
        # When HTMX requests the table, we'll handle the search criteria
        # and render the partial template.
        self.object_list = self.get_queryset()
        context = self.get_context_data(object_list=self.object_list)
        return self.render_to_response(context)
        
    def post(self, request, *args, **kwargs):
        # Handle form submissions for search. The `HX-Request` header
        # will tell us if it's an HTMX request.
        self.object_list = self.get_queryset()
        context = self.get_context_data(object_list=self.object_list)
        return self.render_to_response(context)


class CustomerAutoCompleteView(View):
    """
    HTMX endpoint for customer name autocomplete.
    Replaces the ASP.NET 'sql' web method.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        # Assume CompId from session or user profile
        current_comp_id = request.session.get('compid', 1) 

        if query:
            # Filter customers starting with the prefix (case-insensitive)
            customers = CustomerMaster.objects.filter(
                Q(customer_name__icontains=query) | Q(customer_id__icontains=query),
                comp_id=current_comp_id
            ).order_by('customer_name')[:10] # Limit to 10 results, similar to original

            suggestions = []
            for customer in customers:
                suggestions.append({
                    'id': customer.customer_id,
                    'name': customer.customer_name,
                    'full_display': f"{customer.customer_name} [{customer.customer_id}]"
                })
            
            # Return an HTML fragment with list items for HTMX swap
            # Example structure: <ul id="autocomplete-results"><li data-id="CUST001">Customer Name [CUST001]</li>...</ul>
            html_content = format_html(
                '<ul class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto" id="autocomplete-results">'
            )
            for s in suggestions:
                html_content += format_html(
                    '<li class="p-2 cursor-pointer hover:bg-gray-100" hx-on:click="document.getElementById(\'id_customer_name_search\').value=\'{}\'; this.closest(\'ul\').innerHTML=\'\';" data-id="{}">{}</li>',
                    s['full_display'], s['id'], s['full_display']
                )
            html_content += format_html('</ul>')
            return HttpResponse(html_content)
        
        return HttpResponse("") # Return empty if no query

```

#### 4.4 Templates (`invoicing/templates/invoicing/service_tax_invoice/`)

##### `list.html`

This is the main page template that orchestrates the search form and the HTMX-loaded DataTables.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ searchCriteria: '0' }">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Service Tax Invoice - Edit</h2>
        <!-- No direct 'Add New' button on this page per original ASPX -->
    </div>
    
    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <h3 class="text-lg font-semibold text-gray-700 mb-4">Search Invoices</h3>
        
        <form hx-get="{% url 'invoicing:service_tax_invoice_table' %}" 
              hx-target="#service-tax-invoice-table-container"
              hx-swap="innerHTML"
              hx-indicator="#table-loading-indicator"
              class="space-y-4">
            {% csrf_token %}
            <div class="flex items-end space-x-4">
                <div class="flex-grow">
                    <label for="id_search_by" class="block text-sm font-medium text-gray-700">Search By:</label>
                    {{ form.search_by }}
                </div>
                
                <div class="relative flex-grow">
                    <!-- Customer Name Search Input -->
                    <div x-show="searchCriteria === '0'">
                        <label for="id_customer_name_search" class="block text-sm font-medium text-gray-700">Customer Name:</label>
                        {{ form.customer_name_search }}
                        <div id="autocomplete-results" class="z-10"></div>
                    </div>
                    
                    <!-- PO No / Invoice No Search Input -->
                    <div x-show="searchCriteria === '2' || searchCriteria === '3'">
                        <label for="id_po_invoice_search" class="block text-sm font-medium text-gray-700" 
                               x-text="searchCriteria === '2' ? 'PO No:' : 'Invoice No:'"></label>
                        {{ form.po_invoice_search }}
                    </div>
                </div>
                
                <button type="submit" class="redbox bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md">
                    Search
                </button>
            </div>
            <div class="h-6 w-6 inline-block animate-spin rounded-full border-b-2 border-blue-500 htmx-indicator" id="table-loading-indicator"></div>
        </form>
    </div>

    <div id="service-tax-invoice-table-container" 
         hx-trigger="load, refreshServiceTaxInvoiceList from:body"
         hx-get="{% url 'invoicing:service_tax_invoice_table' %}"
         hx-swap="innerHTML">
        <!-- Initial loading state -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Invoices...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Ensure Alpine.js is initialized and functional.
    // This script block will run after Alpine.js loads if base.html includes it.
    document.addEventListener('alpine:init', () => {
        // Alpine.js components would be defined here if they were more complex.
        // For this simple case, x-data and x-show directly in HTML are sufficient.
    });

    // DataTables library inclusion (assuming it's loaded in base.html)
    // No specific JS here, as DataTables is initialized in the partial template
    // after HTMX swaps it in.
</script>
{% endblock %}
```

##### `_service_tax_invoice_table.html`

This partial template contains only the table structure, designed to be swapped in by HTMX. It includes the DataTables initialization script.

```html
<div class="bg-white shadow-md rounded-lg overflow-hidden">
    <table id="serviceTaxInvoiceTable" class="min-w-full leading-normal">
        <thead>
            <tr>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">FinYear</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Invoice No</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Date</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Customer Name</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">WO No</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">PO No</th>
                <th class="px-5 py-3 border-b-2 border-gray-200 bg-gray-100 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for invoice in service_tax_invoices %}
            <tr class="hover:bg-gray-50">
                <td class="px-5 py-2 border-b border-gray-200 bg-white text-sm">{{ forloop.counter0|add:page_obj.start_index }}</td>
                <td class="px-5 py-2 border-b border-gray-200 bg-white text-sm">{{ invoice.get_fin_year }}</td>
                <td class="px-5 py-2 border-b border-gray-200 bg-white text-sm">{{ invoice.invoice_no }}</td>
                <td class="px-5 py-2 border-b border-gray-200 bg-white text-sm">{{ invoice.get_display_date }}</td>
                <td class="px-5 py-2 border-b border-gray-200 bg-white text-sm">{{ invoice.get_customer_name }}</td>
                <td class="px-5 py-2 border-b border-gray-200 bg-white text-sm">{{ invoice.get_wo_numbers }}</td>
                <td class="px-5 py-2 border-b border-gray-200 bg-white text-sm">{{ invoice.po_no }}</td>
                <td class="px-5 py-2 border-b border-gray-200 bg-white text-sm text-center">
                    <a href="{% url 'invoicing:service_tax_invoice_select' invoice.pk invoice.invoice_no invoice.customer_code %}"
                       class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded text-xs">
                        Select
                    </a>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="px-5 py-5 border-b border-gray-200 bg-white text-lg text-center text-red-700">
                    No data to display !
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after the table has been loaded by HTMX
    $(document).ready(function() {
        $('#serviceTaxInvoiceTable').DataTable({
            "paging": true,
            "pageLength": {{ paginate_by }}, // Use Django's paginate_by for initial page size
            "searching": true, // Enable search box
            "ordering": true,  // Enable sorting
            "info": true,      // Show info (e.g., "Showing 1 to 10 of 50 entries")
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]], // Options for number of entries per page
            // Disable server-side processing, as Django query handles initial filter
            // DataTables handles client-side filtering/sorting/pagination
            "processing": false, 
            "serverSide": false,
            "destroy": true, // Allows re-initialization on HTMX swap
        });
    });
</script>
```

#### 4.5 URLs (`invoicing/urls.py`)

```python
from django.urls import path
from .views import (
    ServiceTaxInvoiceListView,
    ServiceTaxInvoiceTablePartialView,
    CustomerAutoCompleteView,
)

app_name = 'invoicing' # Namespace for URLs

urlpatterns = [
    path('service-tax-invoice/', ServiceTaxInvoiceListView.as_view(), name='service_tax_invoice_list'),
    path('service-tax-invoice/table/', ServiceTaxInvoiceTablePartialView.as_view(), name='service_tax_invoice_table'),
    path('service-tax-invoice/customer_autocomplete/', CustomerAutoCompleteView.as_view(), name='customer_autocomplete'),
    
    # Placeholder for the "Select" action, mimicking the ASP.NET redirect.
    # In a real app, this would point to an actual detail/edit view.
    path('service-tax-invoice/select/<int:invoice_id>/<str:invoice_no>/<str:customer_code>/', 
         lambda request, invoice_id, invoice_no, customer_code: HttpResponse(f"Redirecting to Invoice Details for ID: {invoice_id}, Invoice No: {invoice_no}, Customer Code: {customer_code}"), 
         name='service_tax_invoice_select'),
]
```

#### 4.6 Tests (`invoicing/tests.py`)

Comprehensive tests for models (fat model properties) and views (HTMX interactions, search filters).

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import datetime
from unittest.mock import patch

from .models import ServiceTaxInvoiceMaster, FinancialMaster, CustomerMaster, WorkOrderMaster
from .forms import ServiceTaxInvoiceSearchForm

class ModelTest(TestCase):
    """
    Unit tests for all models and their custom properties/methods.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all models
        cls.fin_year_2023 = FinancialMaster.objects.create(fin_year_id=1, fin_year='2023-2024')
        cls.fin_year_2022 = FinancialMaster.objects.create(fin_year_id=2, fin_year='2022-2023')
        
        cls.customer_alpha = CustomerMaster.objects.create(customer_id='CUST001', customer_name='Alpha Corp', comp_id=1)
        cls.customer_beta = CustomerMaster.objects.create(customer_id='CUST002', customer_name='Beta Industries', comp_id=1)
        cls.customer_gamma = CustomerMaster.objects.create(customer_id='CUST003', customer_name='Gamma Ltd', comp_id=2) # Different company

        cls.wo1 = WorkOrderMaster.objects.create(id=101, won_no='WO/2023/001', comp_id=1)
        cls.wo2 = WorkOrderMaster.objects.create(id=102, won_no='WO/2023/002', comp_id=1)
        cls.wo3 = WorkOrderMaster.objects.create(id=103, won_no='WO/2023/003', comp_id=1)

        # Create ServiceTaxInvoiceMaster instances
        cls.invoice1 = ServiceTaxInvoiceMaster.objects.create(
            id=1, fin_year_id=1, sys_date=datetime(2023, 10, 26), 
            invoice_no='INV001', wo_no_ids='101,102', po_no='PO001', 
            customer_code='CUST001', comp_id=1
        )
        cls.invoice2 = ServiceTaxInvoiceMaster.objects.create(
            id=2, fin_year_id=1, sys_date=datetime(2023, 11, 15), 
            invoice_no='INV002', wo_no_ids='103', po_no='PO002', 
            customer_code='CUST002', comp_id=1
        )
        cls.invoice3 = ServiceTaxInvoiceMaster.objects.create(
            id=3, fin_year_id=2, sys_date=datetime(2022, 12, 01), 
            invoice_no='INV003', wo_no_ids='', po_no='PO003', 
            customer_code='CUST001', comp_id=1
        )
        cls.invoice4 = ServiceTaxInvoiceMaster.objects.create(
            id=4, fin_year_id=1, sys_date=datetime(2023, 09, 01), 
            invoice_no='INV004', wo_no_ids='999', po_no='PO004', # Non-existent WO ID
            customer_code='CUST999', comp_id=1 # Non-existent Customer
        )

    def test_service_tax_invoice_master_creation(self):
        self.assertEqual(self.invoice1.invoice_no, 'INV001')
        self.assertEqual(self.invoice1.customer_code, 'CUST001')
        self.assertEqual(self.invoice1.comp_id, 1)

    def test_get_fin_year_property(self):
        self.assertEqual(self.invoice1.get_fin_year, '2023-2024')
        self.assertEqual(self.invoice3.get_fin_year, '2022-2023')
        # Test for non-existent FinYearId
        non_existent_invoice = ServiceTaxInvoiceMaster(fin_year_id=999, comp_id=1)
        self.assertEqual(non_existent_invoice.get_fin_year, 'N/A')

    def test_get_customer_name_property(self):
        self.assertEqual(self.invoice1.get_customer_name, 'Alpha Corp [CUST001]')
        self.assertEqual(self.invoice2.get_customer_name, 'Beta Industries [CUST002]')
        self.assertEqual(self.invoice4.get_customer_name, 'Unknown Customer [CUST999]')

    def test_get_customer_obj_property(self):
        self.assertEqual(self.invoice1.get_customer_obj, self.customer_alpha)
        self.assertIsNone(self.invoice4.get_customer_obj)

    def test_get_wo_numbers_property(self):
        self.assertEqual(self.invoice1.get_wo_numbers, 'WO/2023/001,WO/2023/002')
        self.assertEqual(self.invoice2.get_wo_numbers, 'WO/2023/003')
        self.assertEqual(self.invoice3.get_wo_numbers, '') # Empty wo_no_ids
        self.assertEqual(self.invoice4.get_wo_numbers, 'N/A') # Non-existent WO ID

    def test_get_display_date_method(self):
        self.assertEqual(self.invoice1.get_display_date(), '26/10/2023')
        invoice_no_date = ServiceTaxInvoiceMaster(sys_date=None)
        self.assertEqual(invoice_no_date.get_display_date(), '')


class ServiceTaxInvoiceViewsTest(TestCase):
    """
    Integration tests for Service Tax Invoice views.
    """
    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for views
        cls.fin_year_2023 = FinancialMaster.objects.create(fin_year_id=1, fin_year='2023-2024')
        cls.customer_alpha = CustomerMaster.objects.create(customer_id='CUST001', customer_name='Alpha Corp', comp_id=1)
        cls.customer_beta = CustomerMaster.objects.create(customer_id='CUST002', customer_name='Beta Industries', comp_id=1)
        cls.wo1 = WorkOrderMaster.objects.create(id=101, won_no='WO/2023/001', comp_id=1)
        
        cls.invoice1 = ServiceTaxInvoiceMaster.objects.create(
            id=1, fin_year_id=1, sys_date=datetime(2023, 10, 26), 
            invoice_no='INV001', wo_no_ids='101', po_no='PO001', 
            customer_code='CUST001', comp_id=1
        )
        cls.invoice2 = ServiceTaxInvoiceMaster.objects.create(
            id=2, fin_year_id=1, sys_date=datetime(2023, 11, 15), 
            invoice_no='INV002', wo_no_ids='', po_no='PO002', 
            customer_code='CUST002', comp_id=1
        )

    def setUp(self):
        self.client = Client()
        # Mock session variables if needed for compid/finyear
        self.session = self.client.session
        self.session['compid'] = 1
        self.session['finyear'] = 1
        self.session.save()

    def test_service_tax_invoice_list_view_get(self):
        response = self.client.get(reverse('invoicing:service_tax_invoice_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'invoicing/service_tax_invoice/list.html')
        self.assertIsInstance(response.context['form'], ServiceTaxInvoiceSearchForm)
        self.assertEqual(response.context['current_comp_id'], 1)
        self.assertEqual(response.context['current_fin_year_id'], 1)

    def test_service_tax_invoice_table_partial_view_initial_load(self):
        # Simulate HTMX initial load
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('invoicing:service_tax_invoice_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'invoicing/service_tax_invoice/_service_tax_invoice_table.html')
        self.assertIn(self.invoice1, response.context['service_tax_invoices'])
        self.assertIn(self.invoice2, response.context['service_tax_invoices'])
        self.assertContains(response, 'INV001')
        self.assertContains(response, 'INV002')
        self.assertContains(response, 'Alpha Corp [CUST001]')

    def test_service_tax_invoice_table_partial_view_search_by_customer_name(self):
        # Simulate HTMX search by customer name
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('invoicing:service_tax_invoice_table'), {
            'search_by': '0',
            'customer_name_search': 'Alpha Corp [CUST001]' # Exact match with ID
        }, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'invoicing/service_tax_invoice/_service_tax_invoice_table.html')
        self.assertIn(self.invoice1, response.context['service_tax_invoices'])
        self.assertNotIn(self.invoice2, response.context['service_tax_invoices'])
        self.assertContains(response, 'INV001')
        self.assertNotContains(response, 'INV002')

    def test_service_tax_invoice_table_partial_view_search_by_customer_name_no_id(self):
        # Simulate HTMX search by customer name (partial match)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('invoicing:service_tax_invoice_table'), {
            'search_by': '0',
            'customer_name_search': 'alpha' # Case-insensitive partial match
        }, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertIn(self.invoice1, response.context['service_tax_invoices'])
        self.assertNotIn(self.invoice2, response.context['service_tax_invoices'])

    def test_service_tax_invoice_table_partial_view_search_by_po_no(self):
        # Simulate HTMX search by PO No
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('invoicing:service_tax_invoice_table'), {
            'search_by': '2',
            'po_invoice_search': 'PO002'
        }, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'invoicing/service_tax_invoice/_service_tax_invoice_table.html')
        self.assertNotIn(self.invoice1, response.context['service_tax_invoices'])
        self.assertIn(self.invoice2, response.context['service_tax_invoices'])

    def test_service_tax_invoice_table_partial_view_search_by_invoice_no(self):
        # Simulate HTMX search by Invoice No
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('invoicing:service_tax_invoice_table'), {
            'search_by': '3',
            'po_invoice_search': 'INV001'
        }, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'invoicing/service_tax_invoice/_service_tax_invoice_table.html')
        self.assertIn(self.invoice1, response.context['service_tax_invoices'])
        self.assertNotIn(self.invoice2, response.context['service_tax_invoices'])

    def test_customer_autocomplete_view(self):
        response = self.client.get(reverse('invoicing:customer_autocomplete'), {'q': 'alpha'})
        self.assertEqual(response.status_code, 200)
        # Check if response contains HTML fragment for autocomplete
        self.assertIn(b'Alpha Corp [CUST001]', response.content)
        self.assertNotIn(b'Beta Industries [CUST002]', response.content) # Not starting with 'alpha'
        
        response_empty = self.client.get(reverse('invoicing:customer_autocomplete'), {'q': ''})
        self.assertEqual(response_empty.status_code, 200)
        self.assertEqual(response_empty.content, b'') # Empty query should return empty response

        response_no_match = self.client.get(reverse('invoicing:customer_autocomplete'), {'q': 'xyz'})
        self.assertEqual(response_no_match.status_code, 200)
        self.assertContains(response_no_match, '<ul', count=0) # Should be an empty ul if no matches
        self.assertNotContains(response_no_match, '<li')

    def test_select_invoice_url(self):
        # Test the placeholder select URL
        invoice_id = self.invoice1.pk
        invoice_no = self.invoice1.invoice_no
        customer_code = self.invoice1.customer_code
        url = reverse('invoicing:service_tax_invoice_select', args=[invoice_id, invoice_no, customer_code])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, f"Redirecting to Invoice Details for ID: {invoice_id}")
```

---

### Step 5: HTMX and Alpine.js Integration

The migration plan heavily relies on HTMX and Alpine.js to achieve the dynamic behavior previously handled by ASP.NET PostBacks and `AjaxControlToolkit`.

1.  **Dynamic Input Visibility (Alpine.js):**
    *   The `DropDownList1_SelectedIndexChanged` logic is moved to the frontend using Alpine.js `x-data` and `x-show` directives on the `list.html` template.
    *   When the `search_by` dropdown changes, Alpine.js updates `searchCriteria`, which in turn shows/hides `customer_name_search` or `po_invoice_search` inputs.

2.  **Search and Table Refresh (HTMX):**
    *   The `btnSearch_Click` event is replaced by the HTMX `hx-get` on the form itself in `list.html`.
    *   When the search button is clicked (or search fields change), HTMX sends a GET request to `{% url 'invoicing:service_tax_invoice_table' %}`.
    *   The `_service_tax_invoice_table.html` partial is returned and `hx-swap="innerHTML"` replaces the content of the `service-tax-invoice-table-container` div, effectively refreshing the table without a full page load.
    *   The `hx-trigger="load, refreshServiceTaxInvoiceList from:body"` on the container ensures the table loads on initial page load and can be refreshed by custom HTMX triggers.

3.  **Autocomplete (HTMX):**
    *   The `txtCustName_AutoCompleteExtender` is replaced by an HTMX `hx-get` on the `customer_name_search` input field.
    *   `hx-get="{% url 'invoicing:customer_autocomplete' %}"`, `hx-trigger="keyup changed delay:500ms, search"`, `hx-target="#autocomplete-results"` are used.
    *   The `CustomerAutoCompleteView` returns an HTML `<ul>` fragment with `<li>` elements.
    *   An `hx-on:click` attribute is added to each `<li>` element to populate the input field and clear the autocomplete results when a suggestion is clicked.

4.  **DataTables Integration:**
    *   DataTables is initialized in the `_service_tax_invoice_table.html` partial. This ensures that every time HTMX swaps in the new table content, DataTables is re-initialized correctly.
    *   DataTables will handle client-side pagination, sorting, and instantaneous filtering of the *currently displayed data*, providing a highly responsive user experience.

5.  **"Select" Action:**
    *   The `GridView1_RowCommand` for "Sel" is replaced by a standard Django `<a>` tag with `href="{% url 'invoicing:service_tax_invoice_select' ... %}"`. This performs a traditional redirect to the detail page, mirroring the original ASP.NET behavior. If a modal-based detail view were desired, HTMX could be used here as well (`hx-get` to a modal target).

This robust architecture ensures that the application is not only modernized but also provides a superior user experience, adhering to the "fat model, thin view" principle and leveraging the strengths of each chosen technology.

---