This document outlines a strategic plan to modernize your legacy ASP.NET application, specifically the "Bill Booking - Print" module, by migrating it to a robust and scalable Django 5.0+ framework. This transition will leverage state-of-the-art technologies like HTMX and Alpine.js for dynamic front-end interactions, DataTables for efficient data presentation, and a 'fat model, thin view' architectural pattern for maintainability and performance.

Our approach prioritizes automation and clear, actionable steps, ensuring a smooth transition that minimizes manual effort and potential errors. This plan is designed to be easily understood by both technical and non-technical stakeholders, focusing on the business benefits of a modern, efficient, and user-friendly system.

## ASP.NET to Django Conversion Script:

### Business Value of Modernization:

Migrating to Django offers significant advantages:
*   **Enhanced Performance:** Django's optimized architecture and efficient ORM provide faster data processing and page loads, improving user experience.
*   **Improved Maintainability & Scalability:** The 'fat model, thin view' paradigm, combined with Django's structured approach, makes the codebase easier to understand, maintain, and expand as your business grows.
*   **Reduced Development Costs:** Leveraging Django's extensive ecosystem, including robust libraries and built-in features, accelerates development and reduces the need for custom coding.
*   **Modern User Experience:** HTMX and Alpine.js deliver reactive and seamless interactions without complex JavaScript, creating a snappier and more intuitive interface for your users. DataTables further enhances data readability and interaction.
*   **Increased Security:** Django comes with built-in security features that protect against common web vulnerabilities, making your application more resilient.
*   **Future-Proofing:** Moving away from a legacy framework ensures your application remains compatible with modern technologies and industry standards, securing its long-term viability.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is located at `core/base.html`.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database tables and their columns from the ASP.NET code.

The ASP.NET code interacts primarily with `tblACC_BillBooking_Master` and performs lookups against `tblMM_Supplier_master`, `tblFinancial_master`, and `tblHR_OfficeStaff`.

**Identified Tables and Key Columns:**

*   **Main Table:** `tblACC_BillBooking_Master`
    *   Columns: `Id` (Primary Key), `SysDate`, `SysTime`, `SessionId`, `CompId`, `FinYearId`, `PVEVNo`, `SupplierId`, `BillNo`, `BillDate`, `CENVATEntryNo`, `CENVATEntryDate`, `OtherCharges`, `OtherChaDesc`, `Narration`, `DebitAmt`, `DiscountType`, `Discount`, `Authorize`, `AuthorizeBy`, `AuthorizeDate`, `AuthorizeTime`.
*   **Related Table 1:** `tblMM_Supplier_master`
    *   Columns: `SupplierId` (Primary Key), `SupplierName`.
*   **Related Table 2:** `tblFinancial_master`
    *   Columns: `FinYearId` (Primary Key), `FinYear`.
*   **Related Table 3:** `tblHR_OfficeStaff`
    *   Columns: `EmpId` (Primary Key), `Title`, `EmployeeName`.

### Step 2: Identify Backend Functionality

Task: Determine the core operations and data processing in the ASP.NET code.

The primary functionality of this ASP.NET page is:
*   **Read (List & Search):** Displaying a list of bill booking records from `tblACC_BillBooking_Master`. This involves filtering records based on `SupplierName` or `PVEVNo`, and joining data from supplier, financial year, and employee tables. Pagination is also handled.
*   **Autocomplete:** Providing an autocomplete feature for `SupplierName` using a WebMethod that queries `tblMM_Supplier_master`.

While this specific page is primarily for listing and searching, the overall modernization plan includes standard CRUD operations (Create, Read, Update, Delete) for full module functionality, as per the template.

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles, and map them to modern Django equivalents.

*   **Search Filters:**
    *   `asp:DropDownList` (`DropDownList1`): For selecting search criteria (Supplier Name, PVEVNo). This will be a standard HTML `<select>` element.
    *   `asp:TextBox` (`txtSupplier`, `Txtfield`): For entering search text. These will be standard HTML `<input type="text">` elements. `txtSupplier` will have HTMX-powered autocomplete.
    *   `asp:Button` (`Button1`): Triggers the search. This will be an HTMX-enhanced `<button>` that submits the search form.
*   **Data Display:**
    *   `asp:GridView` (`GridView2`): Displays the tabular data with pagination and styling. This will be replaced by a standard HTML `<table>` enhanced with DataTables for client-side functionality (sorting, filtering, pagination).
    *   `asp:HyperLinkField`: Navigates to a details page. In the Django migration, these will become standard links or buttons that trigger HTMX requests for editing or viewing details in a modal.

### Step 4: Generate Django Code

We will now generate the Django application files, assuming an application named `accounts` (mapping to the `Module_Accounts` structure).

#### 4.1 Models

Task: Create Django models based on the identified database schema. We'll use `managed = False` to connect to existing tables and define relationships.

File: `accounts/models.py`

```python
from django.db import models
from django.db.models.signals import pre_save
from django.dispatch import receiver

class BillBookingMasterManager(models.Manager):
    """
    Custom manager for BillBookingMaster to handle complex search logic
    and data enrichment that mirrors the original ASP.NET loadData method.
    """
    def search_bills(self, search_by, search_text, comp_id, fin_year_id):
        """
        Searches bill booking records based on specified criteria.
        
        Args:
            search_by (str): The field to search by ('1' for Supplier Name, '2' for PVEVNo).
            search_text (str): The text to search for.
            comp_id (int): Company ID for filtering.
            fin_year_id (int): Financial Year ID for filtering (FinYearId <= session finyear).
        
        Returns:
            QuerySet: Filtered and annotated queryset of BillBookingMaster objects.
        """
        queryset = self.get_queryset().filter(
            compid=comp_id,
            finyearid__lte=fin_year_id # Matching FinYearId<=Session["finyear"] logic
        )
        
        if search_text:
            if search_by == '1':  # Supplier Name
                # Assumes supplierid in BillBookingMaster links to SupplierMaster's supplierid
                queryset = queryset.filter(supplier__supplier_name__icontains=search_text)
            elif search_by == '2': # PVEVNo
                queryset = queryset.filter(pvevno__icontains=search_text)
            # Original ASP.NET code had a 'PO No' option (Value="3"),
            # but PONo column was not explicitly selected from tblACC_BillBooking_Master.
            # We are omitting 'PO No' search criteria to align with the provided C# select statement.
        
        # Annotate with related data for display, similar to original DataTable population
        queryset = queryset.annotate(
            supplier_name_display=models.F('supplier__supplier_name'),
            fin_year_display=models.F('financial_year__fin_year'),
            authorized_by_name_display=models.Case(
                models.When(authorized_by__isnull=False, then=models.Func(
                    models.Value(' '),
                    models.F('authorized_by__title'),
                    models.F('authorized_by__employee_name'),
                    function='CONCAT',
                    output_field=models.CharField()
                )),
                default=models.Value('')
            )
        )
        
        return queryset.order_by('-id')

class BillBookingMaster(models.Model):
    # Primary Key
    id = models.IntegerField(db_column='Id', primary_key=True)
    
    # Foreign Keys
    supplier = models.ForeignKey(
        'SupplierMaster',
        models.DO_NOTHING,
        db_column='SupplierId',
        to_field='supplier_id', # Ensure this matches the actual column name in tblMM_Supplier_master
        blank=True,
        null=True,
        related_name='bill_bookings'
    )
    financial_year = models.ForeignKey(
        'FinancialMaster',
        models.DO_NOTHING,
        db_column='FinYearId',
        to_field='fin_year_id', # Ensure this matches the actual column name in tblFinancial_master
        blank=True,
        null=True,
        related_name='bill_bookings'
    )
    authorized_by = models.ForeignKey(
        'OfficeStaff',
        models.DO_NOTHING,
        db_column='AuthorizeBy',
        to_field='emp_id', # Ensure this matches the actual column name in tblHR_OfficeStaff
        blank=True,
        null=True,
        related_name='authorized_bill_bookings'
    )

    # Core fields from tblACC_BillBooking_Master
    sys_date = models.DateField(db_column='SysDate', blank=True, null=True)
    sys_time = models.CharField(db_column='SysTime', max_length=50, blank=True, null=True)
    session_id = models.IntegerField(db_column='SessionId', blank=True, null=True)
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)
    finyearid = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    pvevno = models.CharField(db_column='PVEVNo', max_length=50, blank=True, null=True)
    bill_no = models.CharField(db_column='BillNo', max_length=50, blank=True, null=True)
    bill_date = models.DateField(db_column='BillDate', blank=True, null=True)
    cenvat_entry_no = models.CharField(db_column='CENVATEntryNo', max_length=50, blank=True, null=True)
    cenvat_entry_date = models.DateField(db_column='CENVATEntryDate', blank=True, null=True)
    other_charges = models.DecimalField(db_column='OtherCharges', max_digits=18, decimal_places=2, blank=True, null=True)
    other_cha_desc = models.CharField(db_column='OtherChaDesc', max_length=250, blank=True, null=True)
    narration = models.CharField(db_column='Narration', max_length=500, blank=True, null=True)
    debit_amt = models.DecimalField(db_column='DebitAmt', max_digits=18, decimal_places=2, blank=True, null=True)
    discount_type = models.CharField(db_column='DiscountType', max_length=50, blank=True, null=True)
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=2, blank=True, null=True)
    authorize = models.BooleanField(db_column='Authorize', blank=True, null=True) # Assuming bit/boolean
    authorize_date = models.DateField(db_column='AuthorizeDate', blank=True, null=True)
    authorize_time = models.CharField(db_column='AuthorizeTime', max_length=50, blank=True, null=True)

    objects = BillBookingMasterManager() # Attach the custom manager

    class Meta:
        managed = False
        db_table = 'tblACC_BillBooking_Master'
        verbose_name = 'Bill Booking'
        verbose_name_plural = 'Bill Bookings'

    def __str__(self):
        return f"{self.pvevno} ({self.supplier.supplier_name if self.supplier else 'N/A'})"
    
    @property
    def formatted_sys_date(self):
        """Returns SysDate in DD/MM/YYYY format."""
        return self.sys_date.strftime('%d/%m/%Y') if self.sys_date else ''

    @property
    def formatted_bill_date(self):
        """Returns BillDate in DD/MM/YYYY format."""
        return self.bill_date.strftime('%d/%m/%Y') if self.bill_date else ''

    @property
    def formatted_authorize_date(self):
        """Returns AuthorizeDate in DD/MM/YYYY format."""
        return self.authorize_date.strftime('%d/%m/%Y') if self.authorize_date else ''

    def get_supplier_display(self):
        """Returns supplier name with ID, e.g., 'Supplier Name [ID]'"""
        if self.supplier:
            return f"{self.supplier.supplier_name} [{self.supplier.supplier_id}]"
        return "N/A"
    
    def get_authorized_by_display(self):
        """Returns authorized by name, e.g., 'Title EmployeeName'"""
        if self.authorized_by:
            return f"{self.authorized_by.title} {self.authorized_by.employee_name}".strip()
        return "N/A"

@receiver(pre_save, sender=BillBookingMaster)
def convert_empty_strings_to_null(sender, instance, **kwargs):
    """
    Converts empty string fields to None (NULL in DB) for CharField and TextField.
    This helps ensure consistency with database constraints where NULL is allowed.
    """
    for field in instance._meta.fields:
        if isinstance(field, (models.CharField, models.TextField)) and field.blank:
            value = getattr(instance, field.name)
            if value == '':
                setattr(instance, field.name, None)


class SupplierMaster(models.Model):
    supplier_id = models.CharField(db_column='SupplierId', primary_key=True, max_length=50)
    supplier_name = models.CharField(db_column='SupplierName', max_length=250, blank=True, null=True)
    # Add other fields if needed from tblMM_Supplier_master

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.supplier_name or f"Supplier ID: {self.supplier_id}"

    def get_full_name_with_id(self):
        return f"{self.supplier_name} [{self.supplier_id}]"

class FinancialMaster(models.Model):
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50, blank=True, null=True)
    # Add other fields if needed from tblFinancial_master

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year or f"Financial Year ID: {self.fin_year_id}"

class OfficeStaff(models.Model):
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50)
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=250, blank=True, null=True)
    # Add other fields if needed from tblHR_OfficeStaff

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title} {self.employee_name}".strip() or f"Employee ID: {self.emp_id}"

```

#### 4.2 Forms

Task: Define Django forms for user input, including the search form and a generic CRUD form for `BillBookingMaster`.

File: `accounts/forms.py`

```python
from django import forms
from .models import BillBookingMaster, SupplierMaster

class BillBookingSearchForm(forms.Form):
    """
    Form for searching BillBookingMaster records.
    Reflects the dropdown and text fields from the ASP.NET page.
    """
    SEARCH_BY_CHOICES = [
        ('1', 'Supplier Name'),
        ('2', 'PVEV No'),
    ]
    
    search_by = forms.ChoiceField(
        choices=SEARCH_BY_CHOICES,
        widget=forms.Select(attrs={
            'class': 'block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'id': 'id_search_by', # Ensure unique ID
            'hx-get': '{{% url "accounts:billbookingmaster_table" %}}', # Target for change
            'hx-trigger': 'change',
            'hx-target': '#billbookingmasterTable-container',
            'hx-swap': 'innerHTML',
            'hx-indicator': '#loading-indicator'
        })
    )
    search_text = forms.CharField(
        max_length=250,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter search text...',
            'id': 'id_search_text', # Ensure unique ID for Alpine/HTMX
            'x-bind:placeholder': "searchBy === '1' ? 'Enter Supplier Name...' : 'Enter PVEV No...'"
        })
    )

    # Hidden field to manage which search input is active based on dropdown
    # This is handled via Alpine.js in the template
    # For supplier autocomplete, we'll use a separate field linked via HTMX
    supplier_autocomplete = forms.CharField(
        max_length=250,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Start typing supplier name...',
            'id': 'id_supplier_autocomplete',
            'hx-get': '/accounts/supplier-autocomplete/', # Endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#supplier-autocomplete-results',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off', # Prevent browser autocomplete
            'x-model': 'supplierSearchText',
            'x-show': 'searchBy === "1"', # Alpine.js to show/hide
            '@focusout': 'setTimeout(() => showAutocompleteResults = false, 100)', # Hide results on focus out
            '@focus': 'showAutocompleteResults = true' # Show results on focus
        })
    )

    def clean(self):
        cleaned_data = super().clean()
        search_by = cleaned_data.get('search_by')
        search_text = cleaned_data.get('search_text')
        supplier_autocomplete_text = cleaned_data.get('supplier_autocomplete')

        # If searching by supplier name, use the autocomplete field's value
        if search_by == '1' and supplier_autocomplete_text:
            # Extract supplier ID from 'Name [ID]' format
            if '[' in supplier_autocomplete_text and supplier_autocomplete_text.endswith(']'):
                try:
                    # Assumes format "Name [ID]"
                    supplier_id_str = supplier_autocomplete_text.split('[')[-1][:-1]
                    # We might need to validate if this ID actually exists
                    # For now, we'll just pass the whole string to the search method
                    # The search method will use `icontains` on supplier name, which is more robust
                    # Alternatively, here we could lookup `SupplierMaster.objects.get(supplier_id=supplier_id_str)`
                    # and set search_text to the actual supplier name.
                    # For simplicity and robust searching, I'll pass the full string, and the model search will handle `icontains`
                    # If specific ID matching is needed, `search_text` should be the ID
                    cleaned_data['search_text'] = supplier_autocomplete_text.split('[')[0].strip()
                except (IndexError, ValueError):
                    pass # Keep original string if format is unexpected
            else:
                cleaned_data['search_text'] = supplier_autocomplete_text
        elif search_by == '2' and search_text:
            cleaned_data['search_text'] = search_text
        else:
            cleaned_data['search_text'] = '' # Clear if no valid selection/input

        return cleaned_data


class BillBookingMasterForm(forms.ModelForm):
    """
    Standard form for BillBookingMaster for Create/Update operations.
    """
    class Meta:
        model = BillBookingMaster
        fields = [
            'sys_date', 'sys_time', 'session_id', 'compid', 'finyearid', 'pvevno',
            'supplier', 'bill_no', 'bill_date', 'cenvat_entry_no', 'cenvat_entry_date',
            'other_charges', 'other_cha_desc', 'narration', 'debit_amt',
            'discount_type', 'discount', 'authorize', 'authorized_by',
            'authorize_date', 'authorize_time'
        ]
        widgets = {
            'sys_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sys_time': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'session_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'compid': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'finyearid': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'pvevno': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'supplier': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bill_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bill_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'cenvat_entry_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'cenvat_entry_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'other_charges': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'other_cha_desc': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'narration': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'debit_amt': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'discount_type': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'discount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'authorize': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded'}),
            'authorized_by': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'authorize_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'authorize_time': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }

```

#### 4.3 Views

Task: Implement CRUD operations and search functionality using Django Class-Based Views (CBVs), keeping them thin and delegating logic to models.

File: `accounts/views.py`

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db.models import Q
from .models import BillBookingMaster, SupplierMaster # Import other models if needed
from .forms import BillBookingMasterForm, BillBookingSearchForm

class BillBookingListView(ListView):
    """
    Renders the main Bill Booking list page with search controls and an empty
    container for the DataTables partial, which will be loaded via HTMX.
    """
    model = BillBookingMaster
    template_name = 'accounts/billbookingmaster/list.html'
    context_object_name = 'bill_bookings' # Although not directly used for initial load, good practice
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = BillBookingSearchForm(self.request.GET)
        # Dummy values for comp_id and fin_year_id - replace with actual session/user data
        context['comp_id'] = 1 # Example: Get from request.user.profile.company_id
        context['fin_year_id'] = 2024 # Example: Get from request.session.get('finyear_id')
        return context

class BillBookingTablePartialView(ListView):
    """
    Renders only the table data for HTMX requests.
    Handles search filtering based on GET parameters.
    """
    model = BillBookingMaster
    template_name = 'accounts/billbookingmaster/_billbookingmaster_table.html'
    context_object_name = 'bill_bookings'

    def get_queryset(self):
        # Dummy values for comp_id and fin_year_id - replace with actual session/user data
        comp_id = self.request.session.get('compid', 1) 
        fin_year_id = self.request.session.get('finyear', 2024)

        form = BillBookingSearchForm(self.request.GET)
        search_by = None
        search_text = None

        if form.is_valid():
            search_by = form.cleaned_data.get('search_by')
            search_text = form.cleaned_data.get('search_text')
        
        # Delegate search logic to the BillBookingMaster model's custom manager
        queryset = BillBookingMaster.objects.search_bills(
            search_by=search_by,
            search_text=search_text,
            comp_id=comp_id,
            fin_year_id=fin_year_id
        )
        return queryset

class BillBookingCreateView(CreateView):
    """
    Handles creation of new Bill Booking records.
    Returns an HTMX 204 No Content response with trigger on success for modal close and list refresh.
    """
    model = BillBookingMaster
    form_class = BillBookingMasterForm
    template_name = 'accounts/billbookingmaster/_billbookingmaster_form.html'
    success_url = reverse_lazy('accounts:billbookingmaster_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        # Example: Set session/company/financial year ID before saving
        form.instance.compid = self.request.session.get('compid', 1) 
        form.instance.finyearid = self.request.session.get('finyear', 2024) 
        form.instance.session_id = self.request.session.session_key # Example
        
        response = super().form_valid(form)
        messages.success(self.request, 'Bill Booking record added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content, tells HTMX nothing to swap
                headers={
                    'HX-Trigger': 'refreshBillBookingList' # Custom event to trigger list refresh
                }
            )
        return response

class BillBookingUpdateView(UpdateView):
    """
    Handles updating existing Bill Booking records.
    Returns an HTMX 204 No Content response with trigger on success.
    """
    model = BillBookingMaster
    form_class = BillBookingMasterForm
    template_name = 'accounts/billbookingmaster/_billbookingmaster_form.html'
    success_url = reverse_lazy('accounts:billbookingmaster_list') # Fallback

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Bill Booking record updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBillBookingList'
                }
            )
        return response

class BillBookingDeleteView(DeleteView):
    """
    Handles deletion of Bill Booking records.
    Returns an HTMX 204 No Content response with trigger on success.
    """
    model = BillBookingMaster
    template_name = 'accounts/billbookingmaster/confirm_delete.html'
    success_url = reverse_lazy('accounts:billbookingmaster_list') # Fallback

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Bill Booking record deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBillBookingList'
                }
            )
        return response

class SupplierAutocompleteView(View):
    """
    Provides JSON responses for supplier autocomplete, mirroring ASP.NET's sql WebMethod.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('prefixText', '')
        comp_id = request.session.get('compid', 1) # Example: Get from session

        # Filter suppliers based on prefix_text and comp_id
        # Assuming SupplierMaster also has a CompId for filtering
        # The original C# code only filtered by CompId for the initial query,
        # then filtered results in memory based on prefixText.
        # Here, we do both in the database.
        suppliers = SupplierMaster.objects.filter(
            # comp_id=comp_id, # Uncomment if SupplierMaster has comp_id field
            supplier_name__icontains=prefix_text
        ).order_by('supplier_name')[:10] # Limit to 10 results as in original code snippet

        results = [supplier.get_full_name_with_id() for supplier in suppliers]
        return JsonResponse(results, safe=False)

```

#### 4.4 Templates

Task: Create templates for the list view, the table partial, and CRUD forms. These will leverage HTMX and Alpine.js for dynamic interactions.

File: `accounts/templates/accounts/billbookingmaster/list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ 
    searchBy: '{{ search_form.search_by.value|default:'1' }}', 
    supplierSearchText: '{{ search_form.supplier_autocomplete.value|default:'' }}',
    showAutocompleteResults: false,
    selectedAutocomplete: -1,

    init() {
        // Initialize search text based on searchBy selection
        this.$watch('searchBy', value => {
            if (value === '1') { // Supplier
                this.$refs.txtSupplier.value = this.supplierSearchText;
                this.$refs.txtPVEVNo.value = '';
            } else { // PVEVNo
                this.$refs.txtPVEVNo.value = ''; // Clear existing PVEVNo search
                this.$refs.txtSupplier.value = '';
                this.supplierSearchText = '';
                document.getElementById('supplier-autocomplete-results').innerHTML = ''; // Clear results
            }
        });
        // Initial state
        if (this.searchBy === '1') {
            this.$refs.txtPVEVNo.style.display = 'none';
            this.$refs.txtSupplier.style.display = 'block';
        } else {
            this.$refs.txtSupplier.style.display = 'none';
            this.$refs.txtPVEVNo.style.display = 'block';
        }
    },
    selectAutocompleteItem(itemText) {
        this.supplierSearchText = itemText;
        this.showAutocompleteResults = false;
        // Optionally trigger form submission here if desired, or rely on button click
    },
    handleKeydown(event) {
        const results = Array.from(this.$refs.autocompleteList.children);
        if (!results.length) return;

        if (event.key === 'ArrowDown') {
            event.preventDefault();
            this.selectedAutocomplete = Math.min(this.selectedAutocomplete + 1, results.length - 1);
            results[this.selectedAutocomplete].scrollIntoView({ block: 'nearest' });
        } else if (event.key === 'ArrowUp') {
            event.preventDefault();
            this.selectedAutocomplete = Math.max(this.selectedAutocomplete - 1, 0);
            results[this.selectedAutocomplete].scrollIntoView({ block: 'nearest' });
        } else if (event.key === 'Enter' && this.selectedAutocomplete !== -1) {
            event.preventDefault();
            this.selectAutocompleteItem(results[this.selectedAutocomplete].innerText);
            this.selectedAutocomplete = -1; // Reset selection
            this.showAutocompleteResults = false;
        } else if (event.key === 'Escape') {
            this.showAutocompleteResults = false;
        }
    }
}">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Bill Booking - Print</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'accounts:billbookingmaster_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Bill Booking
        </button>
    </div>
    
    <!-- Search Form -->
    <div class="bg-white p-6 rounded-lg shadow-md mb-8">
        <form id="billBookingSearchForm" 
              hx-get="{% url 'accounts:billbookingmaster_table' %}" 
              hx-target="#billbookingmasterTable-container" 
              hx-swap="innerHTML" 
              hx-indicator="#loading-indicator">
            {% csrf_token %}
            <div class="flex items-end space-x-4">
                <div>
                    <label for="{{ search_form.search_by.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Search By:</label>
                    <select id="{{ search_form.search_by.id_for_label }}" name="{{ search_form.search_by.name }}"
                            x-model="searchBy"
                            @change="
                                if (searchBy === '1') {
                                    $refs.txtSupplier.style.display = 'block';
                                    $refs.txtPVEVNo.style.display = 'none';
                                    $refs.txtPVEVNo.value = '';
                                } else {
                                    $refs.txtSupplier.style.display = 'none';
                                    $refs.txtPVEVNo.style.display = 'block';
                                    $refs.txtSupplier.value = '';
                                    supplierSearchText = ''; // Clear autocomplete model
                                    document.getElementById('supplier-autocomplete-results').innerHTML = ''; // Clear autocomplete results
                                }
                            "
                            class="block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        {% for value, label in search_form.search_by.field.choices %}
                            <option value="{{ value }}" {% if value == search_form.search_by.value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="relative flex-grow">
                    <!-- Supplier Autocomplete Input -->
                    <div x-show="searchBy === '1'" style="display: none;">
                        <label for="{{ search_form.supplier_autocomplete.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Supplier Name:</label>
                        <input type="text" 
                            id="{{ search_form.supplier_autocomplete.id_for_label }}" 
                            name="{{ search_form.supplier_autocomplete.name }}" 
                            x-ref="txtSupplier"
                            x-model="supplierSearchText"
                            @keydown="handleKeydown($event)"
                            class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            placeholder="Start typing supplier name..."
                            hx-get="{% url 'accounts:supplier_autocomplete' %}" 
                            hx-trigger="keyup changed delay:300ms, search"
                            hx-target="#supplier-autocomplete-results"
                            hx-swap="innerHTML"
                            autocomplete="off"
                            @focus="showAutocompleteResults = true"
                            @focusout="setTimeout(() => showAutocompleteResults = false, 150)"
                        >
                        <div id="supplier-autocomplete-results" 
                             x-show="showAutocompleteResults && supplierSearchText.length > 0" 
                             class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-60 overflow-y-auto"
                             @click.away="showAutocompleteResults = false">
                             <ul x-ref="autocompleteList"></ul>
                        </div>
                    </div>
                    
                    <!-- PVEV No Input -->
                    <div x-show="searchBy === '2'" style="display: none;">
                        <label for="{{ search_form.search_text.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">PVEV No:</label>
                        <input type="text" 
                            id="{{ search_form.search_text.id_for_label }}" 
                            name="{{ search_form.search_text.name }}" 
                            x-ref="txtPVEVNo"
                            class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            placeholder="Enter PVEV No..."
                        >
                    </div>
                </div>

                <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
                    Search
                </button>
            </div>
        </form>
    </div>

    <!-- Loading Indicator for HTMX -->
    <div id="loading-indicator" class="htmx-indicator text-center text-blue-500 mb-4">
        <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
        <p class="mt-1">Loading data...</p>
    </div>
    
    <!-- DataTable Container for Bill Booking Records -->
    <div id="billbookingmasterTable-container"
         hx-trigger="load, refreshBillBookingList from:body"
         hx-get="{% url 'accounts:billbookingmaster_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading Bill Bookings...</p>
        </div>
    </div>
    
    <!-- Modal for forms (Create/Update/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-4xl w-full mx-4 sm:mx-auto">
            <!-- Content loaded here by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Include jQuery for DataTables -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.7/css/dataTables.dataTables.min.css">
<!-- Alpine.js -->
<script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

<script>
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'billbookingmasterTable-container') {
            // DataTables re-initialization after HTMX swap
            $('#billbookingmasterTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true, // Destroy existing instance if any
                "paging": true,
                "searching": true,
                "ordering": true,
                "info": true
            });
        }
        // Handle autocomplete results rendering
        if (event.detail.target.id === 'supplier-autocomplete-results') {
            const results = JSON.parse(event.detail.xhr.responseText);
            const ul = event.detail.target.querySelector('ul');
            ul.innerHTML = ''; // Clear previous results
            if (results && results.length > 0) {
                results.forEach((item, index) => {
                    const li = document.createElement('li');
                    li.textContent = item;
                    li.classList.add('px-4', 'py-2', 'cursor-pointer', 'hover:bg-gray-100');
                    li.setAttribute('x-bind:class', '{ "bg-blue-100": selectedAutocomplete === ' + index + ' }');
                    li.setAttribute('@click', 'selectAutocompleteItem(\'' + item + '\')');
                    ul.appendChild(li);
                });
            } else {
                ul.innerHTML = '<li class="px-4 py-2 text-gray-500">No results found.</li>';
            }
        }
    });

    // Close modal and refresh list on successful form submission (HTMX trigger)
    document.body.addEventListener('refreshBillBookingList', function() {
        document.getElementById('modal').classList.remove('is-active');
        // DataTables will re-initialize on hx-trigger="refreshBillBookingList" on table container
    });
</script>
{% endblock %}

```

File: `accounts/templates/accounts/billbookingmaster/_billbookingmaster_table.html`

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-md p-4">
    <table id="billbookingmasterTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PVEV No</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bill No</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bill Date</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Supplier</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Authorize By</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Auth. Date</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Auth. Time</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if bill_bookings %}
                {% for bill in bill_bookings %}
                <tr class="hover:bg-gray-50">
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ bill.fin_year_display }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-blue-600 hover:underline">
                        <a href="#" 
                           hx-get="{% url 'accounts:billbookingmaster_edit' bill.pk %}"
                           hx-target="#modalContent"
                           hx-trigger="click"
                           _="on click add .is-active to #modal">
                           {{ bill.pvevno }}
                        </a>
                    </td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ bill.formatted_sys_date }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ bill.bill_no }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ bill.formatted_bill_date }}</td>
                    <td class="py-3 px-4 text-sm text-gray-900">{{ bill.supplier_name_display }}</td>
                    <td class="py-3 px-4 text-sm text-gray-900">{{ bill.authorized_by_name_display }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ bill.formatted_authorize_date }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ bill.authorize_time }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-right text-sm font-medium">
                        <button 
                            class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md transition duration-300 ease-in-out mr-2"
                            hx-get="{% url 'accounts:billbookingmaster_edit' bill.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            Edit
                        </button>
                        <button 
                            class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md transition duration-300 ease-in-out"
                            hx-get="{% url 'accounts:billbookingmaster_delete' bill.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            Delete
                        </button>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
            <tr>
                <td colspan="11" class="py-4 px-4 text-center text-lg text-maroon-700 font-semibold">
                    No data to display !
                </td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<!-- This script block will be re-executed by HTMX after swap, ensuring DataTables initializes on the new table -->
<script>
    // DataTables initialization is handled in list.html's htmx:afterSwap listener
    // This partial only provides the table structure.
    // However, if this partial is loaded independently and needs DataTables, 
    // an immediate init() call here could be wrapped in a conditional or listener.
    // For this setup, we rely on the parent page's HTMX event listener.
</script>

```

File: `accounts/templates/accounts/billbookingmaster/_billbookingmaster_form.html`

```html
<div class="p-6">
    <h3 class="text-2xl font-bold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Bill Booking</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-6">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {% for field in form %}
            {% if field.name != 'id' %} {# Don't render the ID field directly #}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="text-red-500 text-xs mt-1">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endif %}
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

File: `accounts/templates/accounts/billbookingmaster/confirm_delete.html`

```html
<div class="p-6">
    <h3 class="text-2xl font-bold text-red-700 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Bill Booking record for <strong>{{ object.pvevno }}</strong>?</p>
    
    <form hx-post="{% url 'accounts:billbookingmaster_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

Task: Define URL patterns for the views within the `accounts` application.

File: `accounts/urls.py`

```python
from django.urls import path
from .views import (
    BillBookingListView, 
    BillBookingTablePartialView, 
    BillBookingCreateView, 
    BillBookingUpdateView, 
    BillBookingDeleteView,
    SupplierAutocompleteView
)

app_name = 'accounts' # Namespace for URLs

urlpatterns = [
    # Main list page
    path('billbooking/', BillBookingListView.as_view(), name='billbookingmaster_list'),
    
    # HTMX partial for the table content
    path('billbooking/table/', BillBookingTablePartialView.as_view(), name='billbookingmaster_table'),

    # CRUD operations via HTMX modals
    path('billbooking/add/', BillBookingCreateView.as_view(), name='billbookingmaster_add'),
    path('billbooking/edit/<int:pk>/', BillBookingUpdateView.as_view(), name='billbookingmaster_edit'),
    path('billbooking/delete/<int:pk>/', BillBookingDeleteView.as_view(), name='billbookingmaster_delete'),

    # Autocomplete endpoint for suppliers
    path('supplier-autocomplete/', SupplierAutocompleteView.as_view(), name='supplier_autocomplete'),
]

```

You will also need to include these URLs in your project's main `urls.py` (e.g., in `myproject/urls.py`):
```python
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('accounts/', include('accounts.urls')), # Include your new app's URLs
    # Add other project-level URLs here
]
```

#### 4.6 Tests

Task: Write comprehensive tests for the models and views to ensure functionality and maintainability.

File: `accounts/tests/test_models.py`

```python
from django.test import TestCase
from accounts.models import BillBookingMaster, SupplierMaster, FinancialMaster, OfficeStaff
from datetime import date
from decimal import Decimal

class ModelTestBase(TestCase):
    """Base class for setting up common test data."""
    @classmethod
    def setUpTestData(cls):
        cls.supplier1 = SupplierMaster.objects.create(supplier_id='SUP001', supplier_name='ABC Suppliers')
        cls.supplier2 = SupplierMaster.objects.create(supplier_id='SUP002', supplier_name='XYZ Corp')
        cls.fin_year1 = FinancialMaster.objects.create(fin_year_id=2023, fin_year='2023-2024')
        cls.fin_year2 = FinancialMaster.objects.create(fin_year_id=2024, fin_year='2024-2025')
        cls.staff1 = OfficeStaff.objects.create(emp_id='EMP001', title='Mr.', employee_name='John Doe')
        cls.staff2 = OfficeStaff.objects.create(emp_id='EMP002', title='Ms.', employee_name='Jane Smith')

        cls.bill1 = BillBookingMaster.objects.create(
            id=1, sys_date=date(2024, 1, 15), sys_time='10:30', session_id=123,
            compid=1, finyearid=2024, pvevno='PVEV001', supplier=cls.supplier1,
            bill_no='BLL001', bill_date=date(2024, 1, 10), debit_amt=Decimal('100.50'),
            authorize=True, authorized_by=cls.staff1, authorize_date=date(2024, 1, 16), authorize_time='11:00'
        )
        cls.bill2 = BillBookingMaster.objects.create(
            id=2, sys_date=date(2024, 2, 20), sys_time='14:00', session_id=456,
            compid=1, finyearid=2024, pvevno='PVEV002', supplier=cls.supplier2,
            bill_no='BLL002', bill_date=date(2024, 2, 18), debit_amt=Decimal('250.75'),
            authorize=False
        )
        cls.bill3 = BillBookingMaster.objects.create(
            id=3, sys_date=date(2023, 11, 5), sys_time='09:00', session_id=789,
            compid=1, finyearid=2023, pvevno='PVEV003', supplier=cls.supplier1,
            bill_no='BLL003', bill_date=date(2023, 11, 1), debit_amt=Decimal('50.00'),
            authorize=True, authorized_by=cls.staff2, authorize_date=date(2023, 11, 6), authorize_time='09:30'
        )
        cls.bill4 = BillBookingMaster.objects.create(
            id=4, sys_date=date(2024, 3, 1), sys_time='12:00', session_id=101,
            compid=2, finyearid=2024, pvevno='PVEV004', supplier=cls.supplier2,
            bill_no='BLL004', bill_date=date(2024, 2, 25), debit_amt=Decimal('300.00'),
            authorize=True, authorized_by=cls.staff1, authorize_date=date(2024, 3, 2), authorize_time='12:30'
        )


class BillBookingMasterModelTest(ModelTestBase):
    def test_billbooking_creation(self):
        self.assertEqual(BillBookingMaster.objects.count(), 4)
        self.assertEqual(self.bill1.pvevno, 'PVEV001')
        self.assertEqual(self.bill1.supplier.supplier_name, 'ABC Suppliers')
        self.assertEqual(self.bill1.financial_year.fin_year, '2024-2025') # Auto-related via FinYearId
        self.assertEqual(self.bill1.authorized_by.employee_name, 'John Doe')

    def test_str_method(self):
        self.assertEqual(str(self.bill1), 'PVEV001 (ABC Suppliers)')
        bill_no_supplier = BillBookingMaster.objects.create(
            id=5, pvevno='PVEV005', compid=1, finyearid=2024
        )
        self.assertEqual(str(bill_no_supplier), 'PVEV005 (N/A)') # Test without supplier

    def test_formatted_date_properties(self):
        self.assertEqual(self.bill1.formatted_sys_date, '15/01/2024')
        self.assertEqual(self.bill1.formatted_bill_date, '10/01/2024')
        self.assertEqual(self.bill1.formatted_authorize_date, '16/01/2024')
        
        # Test with None dates
        bill_no_dates = BillBookingMaster.objects.create(
            id=6, pvevno='PVEV006', compid=1, finyearid=2024
        )
        self.assertEqual(bill_no_dates.formatted_sys_date, '')
        self.assertEqual(bill_no_dates.formatted_bill_date, '')
        self.assertEqual(bill_no_dates.formatted_authorize_date, '')

    def test_get_supplier_display(self):
        self.assertEqual(self.bill1.get_supplier_display(), 'ABC Suppliers [SUP001]')
        bill_no_supplier = BillBookingMaster.objects.create(
            id=7, pvevno='PVEV007', compid=1, finyearid=2024
        )
        self.assertEqual(bill_no_supplier.get_supplier_display(), 'N/A')

    def test_get_authorized_by_display(self):
        self.assertEqual(self.bill1.get_authorized_by_display(), 'Mr. John Doe')
        self.assertEqual(self.bill3.get_authorized_by_display(), 'Ms. Jane Smith')
        self.assertEqual(self.bill2.get_authorized_by_display(), 'N/A')

    def test_search_bills_no_filter(self):
        # Current user's company and financial year
        results = BillBookingMaster.objects.search_bills(None, None, comp_id=1, fin_year_id=2024)
        self.assertEqual(results.count(), 3) # bill1, bill2, bill3 (finyearid <= 2024)
        self.assertIn(self.bill1, results)
        self.assertIn(self.bill2, results)
        self.assertIn(self.bill3, results) # bill3 has finyearid 2023 which is <= 2024
        self.assertNotIn(self.bill4, results) # different company

    def test_search_bills_by_supplier_name(self):
        results = BillBookingMaster.objects.search_bills('1', 'ABC', comp_id=1, fin_year_id=2024)
        self.assertEqual(results.count(), 2) # bill1, bill3
        self.assertIn(self.bill1, results)
        self.assertIn(self.bill3, results)
        self.assertNotIn(self.bill2, results)

    def test_search_bills_by_pvevno(self):
        results = BillBookingMaster.objects.search_bills('2', 'PVEV002', comp_id=1, fin_year_id=2024)
        self.assertEqual(results.count(), 1) # bill2
        self.assertIn(self.bill2, results)
        self.assertNotIn(self.bill1, results)

    def test_search_bills_empty_search_text(self):
        results = BillBookingMaster.objects.search_bills('1', '', comp_id=1, fin_year_id=2024)
        self.assertEqual(results.count(), 3) # All bills for compid=1, finyearid<=2024

    def test_convert_empty_strings_to_null_signal(self):
        # Create a bill with an empty string for a nullable CharField
        bill_with_empty_string = BillBookingMaster.objects.create(
            id=8, pvevno='PVEV008', compid=1, finyearid=2024,
            narration='', other_cha_desc='some desc'
        )
        self.assertIsNone(bill_with_empty_string.narration)
        self.assertEqual(bill_with_empty_string.other_cha_desc, 'some desc')
        
        # Test update case
        bill_with_empty_string.narration = 'updated narration'
        bill_with_empty_string.save()
        self.assertEqual(bill_with_empty_string.narration, 'updated narration')

        bill_with_empty_string.narration = ''
        bill_with_empty_string.save()
        self.assertIsNone(bill_with_empty_string.narration)

class SupplierMasterModelTest(ModelTestBase):
    def test_supplier_creation(self):
        self.assertEqual(self.supplier1.supplier_name, 'ABC Suppliers')

    def test_str_method(self):
        self.assertEqual(str(self.supplier1), 'ABC Suppliers')

    def test_get_full_name_with_id(self):
        self.assertEqual(self.supplier1.get_full_name_with_id(), 'ABC Suppliers [SUP001]')

class FinancialMasterModelTest(ModelTestBase):
    def test_financial_creation(self):
        self.assertEqual(self.fin_year1.fin_year, '2023-2024')

    def test_str_method(self):
        self.assertEqual(str(self.fin_year1), '2023-2024')

class OfficeStaffModelTest(ModelTestBase):
    def test_officestaff_creation(self):
        self.assertEqual(self.staff1.employee_name, 'John Doe')

    def test_str_method(self):
        self.assertEqual(str(self.staff1), 'Mr. John Doe')
        staff_no_title = OfficeStaff.objects.create(emp_id='EMP003', employee_name='Alice')
        self.assertEqual(str(staff_no_title), 'Alice') # Test without title

```

File: `accounts/tests/test_views.py`

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from accounts.models import BillBookingMaster, SupplierMaster, FinancialMaster, OfficeStaff
from accounts.forms import BillBookingSearchForm, BillBookingMasterForm
from datetime import date
from decimal import Decimal

class ViewTestBase(TestCase):
    """Base class for setting up common test data and client."""
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.supplier1 = SupplierMaster.objects.create(supplier_id='SUP001', supplier_name='ABC Suppliers')
        cls.supplier2 = SupplierMaster.objects.create(supplier_id='SUP002', supplier_name='XYZ Corp')
        cls.fin_year1 = FinancialMaster.objects.create(fin_year_id=2023, fin_year='2023-2024')
        cls.fin_year2 = FinancialMaster.objects.create(fin_year_id=2024, fin_year='2024-2025')
        cls.staff1 = OfficeStaff.objects.create(emp_id='EMP001', title='Mr.', employee_name='John Doe')
        cls.staff2 = OfficeStaff.objects.create(emp_id='EMP002', title='Ms.', employee_name='Jane Smith')

        cls.bill1 = BillBookingMaster.objects.create(
            id=1, sys_date=date(2024, 1, 15), sys_time='10:30', session_id=123,
            compid=1, finyearid=2024, pvevno='PVEV001', supplier=cls.supplier1,
            bill_no='BLL001', bill_date=date(2024, 1, 10), debit_amt=Decimal('100.50'),
            authorize=True, authorized_by=cls.staff1, authorize_date=date(2024, 1, 16), authorize_time='11:00'
        )
        cls.bill2 = BillBookingMaster.objects.create(
            id=2, sys_date=date(2024, 2, 20), sys_time='14:00', session_id=456,
            compid=1, finyearid=2024, pvevno='PVEV002', supplier=cls.supplier2,
            bill_no='BLL002', bill_date=date(2024, 2, 18), debit_amt=Decimal('250.75'),
            authorize=False
        )
        cls.bill3 = BillBookingMaster.objects.create(
            id=3, sys_date=date(2023, 11, 5), sys_time='09:00', session_id=789,
            compid=1, finyearid=2023, pvevno='PVEV003', supplier=cls.supplier1,
            bill_no='BLL003', bill_date=date(2023, 11, 1), debit_amt=Decimal('50.00'),
            authorize=True, authorized_by=cls.staff2, authorize_date=date(2023, 11, 6), authorize_time='09:30'
        )
        cls.bill4 = BillBookingMaster.objects.create( # Different company
            id=4, sys_date=date(2024, 3, 1), sys_time='12:00', session_id=101,
            compid=2, finyearid=2024, pvevno='PVEV004', supplier=cls.supplier2,
            bill_no='BLL004', bill_date=date(2024, 2, 25), debit_amt=Decimal('300.00'),
            authorize=True, authorized_by=cls.staff1, authorize_date=date(2024, 3, 2), authorize_time='12:30'
        )

    def setUp(self):
        self.client = Client()
        # Set session data for views that rely on it
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2024
        session.save()

class BillBookingListViewTest(ViewTestBase):
    def test_list_view_get(self):
        response = self.client.get(reverse('accounts:billbookingmaster_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/billbookingmaster/list.html')
        self.assertIsInstance(response.context['search_form'], BillBookingSearchForm)
        self.assertEqual(response.context['comp_id'], 1)
        self.assertEqual(response.context['fin_year_id'], 2024)

class BillBookingTablePartialViewTest(ViewTestBase):
    def test_table_partial_view_get(self):
        response = self.client.get(reverse('accounts:billbookingmaster_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/billbookingmaster/_billbookingmaster_table.html')
        self.assertTrue('bill_bookings' in response.context)
        self.assertEqual(len(response.context['bill_bookings']), 3) # bill1, bill2, bill3 for compid=1, finyear<=2024

    def test_table_partial_view_search_by_supplier(self):
        response = self.client.get(reverse('accounts:billbookingmaster_table'), {'search_by': '1', 'supplier_autocomplete': 'ABC Suppliers [SUP001]'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['bill_bookings']), 2) # bill1, bill3
        self.assertContains(response, 'ABC Suppliers')
        self.assertNotContains(response, 'XYZ Corp')

    def test_table_partial_view_search_by_pvevno(self):
        response = self.client.get(reverse('accounts:billbookingmaster_table'), {'search_by': '2', 'search_text': 'PVEV002'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['bill_bookings']), 1) # bill2
        self.assertContains(response, 'PVEV002')
        self.assertNotContains(response, 'PVEV001')

    def test_table_partial_view_no_results(self):
        response = self.client.get(reverse('accounts:billbookingmaster_table'), {'search_by': '2', 'search_text': 'NONEXISTENT'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['bill_bookings']), 0)
        self.assertContains(response, 'No data to display !')

    def test_table_partial_view_comp_id_filter(self):
        # Ensure only bills for compid=1 are returned with default session
        response = self.client.get(reverse('accounts:billbookingmaster_table'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['bill_bookings']), 3) # bill1, bill2, bill3
        self.assertNotIn(self.bill4, response.context['bill_bookings'])


class BillBookingCreateViewTest(ViewTestBase):
    def test_create_view_get(self):
        response = self.client.get(reverse('accounts:billbookingmaster_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/billbookingmaster/_billbookingmaster_form.html')
        self.assertIsInstance(response.context['form'], BillBookingMasterForm)

    def test_create_view_post_success(self):
        data = {
            'sys_date': '2024-05-01',
            'sys_time': '12:00',
            'session_id': 999,
            'compid': 1,
            'finyearid': 2024,
            'pvevno': 'PVEVNEW',
            'supplier': self.supplier1.pk,
            'bill_no': 'BLLNEW',
            'bill_date': '2024-04-28',
            'debit_amt': '150.00',
            'authorize': 'on', # Checkbox value
            'authorized_by': self.staff1.pk,
            'authorize_date': '2024-05-02',
            'authorize_time': '13:00'
        }
        response = self.client.post(reverse('accounts:billbookingmaster_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX No Content
        self.assertTrue(BillBookingMaster.objects.filter(pvevno='PVEVNEW').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBillBookingList')
        
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Bill Booking record added successfully.')

    def test_create_view_post_invalid(self):
        data = {
            'pvevno': '', # Invalid, assuming it's required implicitly or by DB schema
            'compid': 1,
            'finyearid': 2024,
        }
        response = self.client.post(reverse('accounts:billbookingmaster_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-renders with errors
        self.assertTemplateUsed(response, 'accounts/billbookingmaster/_billbookingmaster_form.html')
        self.assertFalse(BillBookingMaster.objects.filter(pvevno='').exists())


class BillBookingUpdateViewTest(ViewTestBase):
    def test_update_view_get(self):
        response = self.client.get(reverse('accounts:billbookingmaster_edit', args=[self.bill1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/billbookingmaster/_billbookingmaster_form.html')
        self.assertIsInstance(response.context['form'], BillBookingMasterForm)
        self.assertEqual(response.context['object'].pvevno, 'PVEV001')

    def test_update_view_post_success(self):
        updated_pvevno = 'PVEV001_UPDATED'
        data = {
            'sys_date': '2024-01-15',
            'sys_time': '10:30',
            'session_id': 123,
            'compid': 1,
            'finyearid': 2024,
            'pvevno': updated_pvevno,
            'supplier': self.supplier1.pk,
            'bill_no': 'BLL001',
            'bill_date': '2024-01-10',
            'debit_amt': '100.50',
            'authorize': 'on',
            'authorized_by': self.staff1.pk,
            'authorize_date': '2024-01-16',
            'authorize_time': '11:00'
        }
        response = self.client.post(reverse('accounts:billbookingmaster_edit', args=[self.bill1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.bill1.refresh_from_db()
        self.assertEqual(self.bill1.pvevno, updated_pvevno)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBillBookingList')

        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Bill Booking record updated successfully.')

class BillBookingDeleteViewTest(ViewTestBase):
    def test_delete_view_get(self):
        response = self.client.get(reverse('accounts:billbookingmaster_delete', args=[self.bill1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/billbookingmaster/confirm_delete.html')
        self.assertEqual(response.context['object'], self.bill1)

    def test_delete_view_post_success(self):
        response = self.client.post(reverse('accounts:billbookingmaster_delete', args=[self.bill1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(BillBookingMaster.objects.filter(pk=self.bill1.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBillBookingList')

        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Bill Booking record deleted successfully.')

    def test_delete_view_post_not_found(self):
        response = self.client.post(reverse('accounts:billbookingmaster_delete', args=[99999]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 404) # Object not found

class SupplierAutocompleteViewTest(ViewTestBase):
    def test_autocomplete_get_results(self):
        response = self.client.get(reverse('accounts:supplier_autocomplete'), {'prefixText': 'ABC'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.headers['Content-Type'], 'application/json')
        data = response.json()
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0], 'ABC Suppliers [SUP001]')

    def test_autocomplete_get_no_results(self):
        response = self.client.get(reverse('accounts:supplier_autocomplete'), {'prefixText': 'NONEXISTENT'})
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(len(data), 0)

    def test_autocomplete_get_case_insensitive(self):
        response = self.client.get(reverse('accounts:supplier_autocomplete'), {'prefixText': 'abc'})
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0], 'ABC Suppliers [SUP001]')

    def test_autocomplete_get_all_results(self):
        response = self.client.get(reverse('accounts:supplier_autocomplete'), {'prefixText': 'Sup'})
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(len(data), 2)
        self.assertIn('ABC Suppliers [SUP001]', data)
        self.assertIn('XYZ Corp [SUP002]', data)

```

### Step 5: HTMX and Alpine.js Integration

This plan heavily relies on HTMX for dynamic content updates and Alpine.js for local UI state management (like showing/hiding search inputs and managing autocomplete results).

*   **HTMX for Page Sections:** The main list view (`list.html`) uses `hx-get` to load the table content (`_billbookingmaster_table.html`) on initial load and whenever a `refreshBillBookingList` custom event is triggered. This event is sent by CRUD views upon successful form submission.
*   **HTMX for Modals:** Buttons for "Add", "Edit", and "Delete" actions use `hx-get` to fetch the respective form/confirmation partials into a modal container (`#modalContent`). Form submissions within the modal use `hx-post` with `hx-swap="none"` and `HX-Trigger` headers to close the modal and refresh the main list upon success.
*   **HTMX for Search:** The search form uses `hx-get` to submit its parameters to `billbookingmaster_table/` endpoint, updating only the table section.
*   **HTMX for Autocomplete:** The supplier autocomplete input (`id_supplier_autocomplete`) uses `hx-get` to `supplier-autocomplete/` endpoint on `keyup` (with a delay) to fetch JSON results. These results are then rendered by Alpine.js.
*   **Alpine.js for UI Logic:**
    *   Manages the visibility of the `txtSupplier` (autocomplete) vs. `Txtfield` (PVEVNo) based on the `search_by` dropdown selection.
    *   Handles autocomplete result display and selection (`showAutocompleteResults`, `selectedAutocomplete`).
    *   Provides keyboard navigation within autocomplete results.
    *   Manages the modal's `is-active` class.
*   **DataTables:** The `_billbookingmaster_table.html` partial, once loaded by HTMX, is immediately initialized as a DataTables instance in `list.html`'s `htmx:afterSwap` event listener. This ensures client-side pagination, sorting, and searching on the displayed table.

This comprehensive approach modernizes the ASP.NET application into a modular, efficient, and user-friendly Django application with minimal reliance on traditional JavaScript, maximizing developer productivity and long-term maintainability.

## Final Notes

*   **Database Migrations:** Remember that while `managed = False` connects Django to existing tables, if you make schema changes in Django models, you'll need to manually apply them to your legacy database or use a tool for schema synchronization.
*   **Authentication and Authorization:** The current plan assumes `CompId`, `FinYearId`, and `SessionId` are available via `request.session`. In a full Django application, proper user authentication (`django.contrib.auth`) and a user profile model would manage these attributes, providing robust security and user context.
*   **Error Handling:** While `try-catch` blocks were prevalent in ASP.NET, Django's `forms.is_valid()` and built-in error handling are more Pythonic. More specific error handling (e.g., custom 404 pages, logging) can be added as needed.
*   **Logging:** Implement proper logging for application errors and critical events to monitor the health and performance of your Django application.
*   **Deployment:** Consider modern deployment strategies for Django applications (e.g., Gunicorn/Nginx, Docker, cloud platforms) for optimal performance and scalability.