```markdown
## ASP.NET to Django Conversion Script: Debit Note Module

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database tables and their columns from the ASP.NET code.

## Instructions:

Based on the ASP.NET code-behind, the primary table for this module is `tblACC_DebitNote`. Additionally, lookups are performed against `tblACC_DebitType`, `tblHR_OfficeStaff`, `SD_Cust_master`, and `tblMM_Supplier_master`.

**Primary Table:** `tblACC_DebitNote`

*   **Columns:**
    *   `Id` (Primary Key, Integer)
    *   `SysDate` (Date)
    *   `SysTime` (Time)
    *   `CompId` (Company ID, Integer)
    *   `SessionId` (User Session ID/Username, String)
    *   `FinYearId` (Financial Year ID, Integer)
    *   `Date` (Debit Note Date, String/Date)
    *   `DebitNo` (Debit Note Number, String)
    *   `SCE` (Source/Customer/Employee ID, String) - Stores the code/ID of the entity.
    *   `Amount` (Decimal)
    *   `Refrence` (Reference/Remarks, String/Text)
    *   `Particulars` (Detailed description, String/Text)
    *   `Types` (Debit Type ID, Integer) - Foreign Key to `tblACC_DebitType`.

**Related Tables:**

*   `tblACC_DebitType`
    *   `Id` (Primary Key, Integer)
    *   `Description` (String)

*   `tblHR_OfficeStaff` (for Employee)
    *   `EmpId` (Primary Key, Integer)
    *   `EmployeeName` (String)

*   `SD_Cust_master` (for Customer)
    *   `CustomerId` (Primary Key, Integer)
    *   `CustomerName` (String)

*   `tblMM_Supplier_master` (for Supplier)
    *   `SupplierId` (Primary Key, Integer)
    *   `SupplierName` (String)

### Step 2: Identify Backend Functionality

Task: Determine the CRUD operations and associated business logic in the ASP.NET code.

## Instructions:

The ASP.NET application provides a complete CRUD (Create, Read, Update, Delete) interface for Debit Notes, managed primarily through the `GridView1` control and its event handlers.

*   **Create (Add):**
    *   Handled by `GridView1_RowCommand` for `CommandName="Add"` (footer row) and `CommandName="Add1"` (empty data template).
    *   Automatically generates a sequential `DebitNo` (e.g., "0001", "0002") based on `CompId` and `FinYearId`.
    *   Validates input for Date, Debit To (SCE), Type, and Amount.
    *   Inserts data into `tblACC_DebitNote`.
*   **Read (Display):**
    *   The `fillgrid()` method populates `GridView1` with data from `tblACC_DebitNote` filtered by `CompId` and `FinYearId`.
    *   It performs lookups to display `Description` for `Types` from `tblACC_DebitType` and resolves the display name for `SCE` (Debit To) using `fun.EmpCustSupplierNames` which queries `tblHR_OfficeStaff`, `SD_Cust_master`, or `tblMM_Supplier_master` based on the `Types` value.
    *   Pagination is handled by `GridView1_PageIndexChanging`.
*   **Update (Edit):**
    *   Initiated by `GridView1_RowEditing`.
    *   Data is retrieved from `EditItemTemplate` controls in `GridView1_RowUpdating`.
    *   Validation for updated fields is applied.
    *   Updates the corresponding record in `tblACC_DebitNote`.
*   **Delete:**
    *   Triggered by `GridView1_RowDeleting`.
    *   Deletes the selected record from `tblACC_DebitNote` based on its `Id`.
*   **Validation & Business Logic:**
    *   **Date Validation:** `fun.DateValidation` (regex in ASPX).
    *   **Amount Validation:** `fun.NumberValidationQty` (regex in ASPX).
    *   **"Debit To" (SCE) Autocomplete & Lookup:** The `sql` and `sql2` WebMethods provide autocomplete suggestions (Name [ID]) by querying employee, customer, or supplier tables based on the selected "Type". `fun.getCode` extracts the ID from the `Name [ID]` string, and `fun.chkEmpCustSupplierCode` validates if the extracted ID exists for the selected type.
    *   Session variables (`username`, `compid`, `finyear`, `val1`, `valE1`) are used to maintain context (e.g., current user, company, financial year, and selected Debit Type for autocomplete).

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles, and map them to Django/HTMX/Alpine.js equivalents.

## Instructions:

*   **`GridView1`:** This will be replaced by a standard HTML `<table>` element enhanced with `DataTables.js` for client-side functionality (searching, sorting, pagination). HTMX will be used to load and refresh this table dynamically.
*   **`TextBox` controls (`txtDate1`, `txtDate2`, `txtDate3`, `txtDebitto1`, `txtDebitto2`, `TxtSCE`, `txtReference1`, `txtReference2`, `TxtRefrence`, `txtParticulars1`, `txtParticulars2`, `TxtParticulars`, `txtAmt1`, `txtAmt2`, `txtAmt3`):** These will be rendered as standard HTML `<input type="text">`, `<input type="date">`, or `<textarea>` elements within Django forms. Tailwind CSS classes will style them.
*   **`CalendarExtender`:** Replaced by `<input type="date">` (HTML5 native date picker).
*   **`DropDownList` controls (`DrpList1`, `DrpList2`, `DrpList3`):** These will be rendered as HTML `<select>` elements within Django forms. Their dynamic filtering/postbacks (`AutoPostBack="true"`, `onselectedindexchanged`) will be managed by HTMX `hx-trigger` attributes and corresponding partial views.
*   **`AutoCompleteExtender`:** Replaced by an HTMX `hx-get` request on the `input` field to a dedicated Django view that serves JSON data for autocomplete suggestions. Alpine.js can be used to manage the display of these suggestions.
*   **`LinkButton` (`Edit`, `Delete`, `Update`, `Cancel`) and `Button` (`Add`):** These will be standard HTML `<button>` elements with HTMX attributes (`hx-get`, `hx-post`, `hx-target`, `hx-swap`) to trigger modals for forms (edit, add, delete confirmation) and submit data without full page reloads.
*   **`RequiredFieldValidator`, `RegularExpressionValidator`:** Django's built-in form validation will handle these, including field-level and form-level clean methods.
*   **`OnClientClick="return confirmationAdd()"` / `confirmationDelete()`:** Simple client-side confirmations will be managed by Alpine.js in conjunction with HTMX. For example, triggering a modal or a temporary display message.
*   **`SqlDataSource1`:** The data source for `DrpList` will be replaced by Django model queries (e.g., `DebitType.objects.all()`).

### Step 4: Generate Django Code

We will create a new Django application, let's call it `accounts`, to house the Debit Note functionality.

#### 4.1 Models (`accounts/models.py`)

The models are defined to reflect the legacy database schema using `managed = False` and `db_table` to point to existing tables. Business logic like `DebitNo` generation and `SCE` resolution is encapsulated within the `DebitNote` model, following the "Fat Model" principle.

```python
from django.db import models
from django.utils import timezone
from django.db.models import Max
import re # For parsing SCE input

# These models represent existing tables in the legacy database.
# They are set to managed = False because Django will not create or manage their schema.

class DebitType(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblACC_DebitType'
        verbose_name = 'Debit Type'
        verbose_name_plural = 'Debit Types'

    def __str__(self):
        return self.description

class Employee(models.Model): # Corresponds to tblHR_OfficeStaff
    id = models.IntegerField(db_column='EmpId', primary_key=True)
    name = models.CharField(db_column='EmployeeName', max_length=255)
    
    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return self.name

class Customer(models.Model): # Corresponds to SD_Cust_master
    id = models.IntegerField(db_column='CustomerId', primary_key=True)
    name = models.CharField(db_column='CustomerName', max_length=255)
    
    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.name

class Supplier(models.Model): # Corresponds to tblMM_Supplier_master
    id = models.IntegerField(db_column='SupplierId', primary_key=True)
    name = models.CharField(db_column='SupplierName', max_length=255)
    
    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.name

class DebitNote(models.Model):
    # Mapping to tblACC_DebitNote columns
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate', default=timezone.now)
    sys_time = models.TimeField(db_column='SysTime', default=timezone.now)
    company_id = models.IntegerField(db_column='CompId')
    session_id = models.CharField(db_column='SessionId', max_length=50) # ASP.NET Session["username"]
    financial_year_id = models.IntegerField(db_column='FinYearId')
    date = models.DateField(db_column='Date')
    debit_no = models.CharField(db_column='DebitNo', max_length=10, unique=True)
    sce_code = models.CharField(db_column='SCE', max_length=50) # Stores the ID (e.g., EmpId, CustomerId)
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=2) 
    reference = models.TextField(db_column='Refrence', blank=True, null=True)
    particulars = models.TextField(db_column='Particulars', blank=True, null=True)
    debit_type = models.ForeignKey(DebitType, models.DO_NOTHING, db_column='Types') 

    class Meta:
        managed = False
        db_table = 'tblACC_DebitNote'
        verbose_name = 'Debit Note'
        verbose_name_plural = 'Debit Notes'
        ordering = ['-id'] # Matches "Order by Id Desc"

    def __str__(self):
        return f"Debit Note {self.debit_no} (Type: {self.debit_type.description})"

    # --- Business Logic Methods (Fat Model) ---

    @classmethod
    def get_next_debit_no(cls, company_id, financial_year_id):
        """
        Generates the next sequential DebitNo based on company and financial year.
        Replicates the PVEVNo generation logic from ASP.NET code.
        """
        last_debit_note = cls.objects.filter(
            company_id=company_id,
            financial_year_id=financial_year_id
        ).aggregate(Max('debit_no'))
        
        last_no_str = last_debit_note['debit_no__max']
        
        if last_no_str:
            try:
                # Assuming DebitNo is purely numeric (e.g., "0001")
                next_no = int(last_no_str) + 1
            except ValueError:
                # Fallback if DebitNo format is unexpected
                next_no = 1
        else:
            next_no = 1
            
        return f"{next_no:04d}" # Format as 0001, 0002 etc.

    def get_sce_display_name(self):
        """
        Resolves and returns the display name for the 'Debit To' (SCE) field.
        Replicates the fun.EmpCustSupplierNames logic.
        """
        type_id = self.debit_type.id
        sce_id = self.sce_code 
        
        if type_id == 1: # Employee (tblHR_OfficeStaff)
            try:
                return Employee.objects.get(id=sce_id).name
            except Employee.DoesNotExist:
                return f"Unknown Employee (ID: {sce_id})"
        elif type_id == 2: # Customer (SD_Cust_master)
            try:
                return Customer.objects.get(id=sce_id).name
            except Customer.DoesNotExist:
                return f"Unknown Customer (ID: {sce_id})"
        elif type_id == 3: # Supplier (tblMM_Supplier_master)
            try:
                return Supplier.objects.get(id=sce_id).name
            except Supplier.DoesNotExist:
                return f"Unknown Supplier (ID: {sce_id})"
        return "N/A"

    @staticmethod
    def parse_sce_input(input_string):
        """
        Extracts the ID from a string in "Name [ID]" format.
        Replicates the fun.getCode logic.
        """
        match = re.search(r'\[(\d+)\]$', input_string)
        if match:
            return match.group(1) # Return the ID as a string
        return None # No ID found

    @classmethod
    def validate_sce_code_exists(cls, sce_code, debit_type_id):
        """
        Checks if the given SCE code (ID) exists for the specified debit type.
        Replicates the fun.chkEmpCustSupplierCode logic.
        """
        if not sce_code:
            return False

        if debit_type_id == 1: # Employee
            return Employee.objects.filter(id=sce_code).exists()
        elif debit_type_id == 2: # Customer
            return Customer.objects.filter(id=sce_code).exists()
        elif debit_type_id == 3: # Supplier
            return Supplier.objects.filter(id=sce_code).exists()
        return False
```

#### 4.2 Forms (`accounts/forms.py`)

A `ModelForm` is created for `DebitNote`. Custom validation for the `sce_code` field is added to ensure it's a valid ID for the selected `debit_type`. `SCE` autocomplete input will be handled by a plain `CharField` on the form, and the parsing will happen in the form's `clean` method or directly in the view.

```python
from django import forms
from .models import DebitNote, DebitType
from django.core.exceptions import ValidationError

class DebitNoteForm(forms.ModelForm):
    # This field will be used for the autocomplete input, which displays "Name [ID]"
    # It's not directly mapped to a model column, but processed in clean()
    sce_display = forms.CharField(
        label="Debit To",
        max_length=255,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Start typing to search...'
        })
    )

    class Meta:
        model = DebitNote
        fields = ['date', 'debit_type', 'sce_code', 'amount', 'reference', 'particulars']
        widgets = {
            'date': forms.DateInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'type': 'date' # Use HTML5 date picker
            }),
            'debit_type': forms.Select(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'hx-get': '/accounts/debitnote/sce-autocomplete-input/', # Trigger autocomplete field update
                'hx-target': '#id_sce_display', # Target the SCE display field
                'hx-vals': 'js:{type_id: event.target.value}', # Pass selected type ID
                'hx-swap': 'outerHTML', # Replace the input field completely (or just its value)
                'hx-trigger': 'change', # Trigger on dropdown change
            }),
            'sce_code': forms.CharField(widget=forms.HiddenInput(), required=False), # Actual model field, hidden
            'amount': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'step': '0.01'
            }),
            'reference': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-20',
                'rows': 3
            }),
            'particulars': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-20',
                'rows': 3
            }),
        }
        # Exclude fields like debit_no, sys_date, sys_time, company_id, session_id, financial_year_id 
        # as they are auto-generated or derived from context.
        exclude = ['debit_no', 'sys_date', 'sys_time', 'company_id', 'session_id', 'financial_year_id']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # If editing an existing object, pre-fill sce_display with the resolved name
        if self.instance.pk:
            self.fields['sce_display'].initial = self.instance.get_sce_display_name()

    def clean(self):
        cleaned_data = super().clean()
        sce_display = cleaned_data.get('sce_display')
        debit_type = cleaned_data.get('debit_type')

        if debit_type and sce_display:
            sce_code = DebitNote.parse_sce_input(sce_display) # Extract ID from "Name [ID]"
            
            if not sce_code:
                raise ValidationError("Please select a valid 'Debit To' entry from the suggestions.", code='invalid_sce_format')

            if not DebitNote.validate_sce_code_exists(sce_code, debit_type.id):
                raise ValidationError("Selected 'Debit To' entry does not match the chosen Type.", code='invalid_sce_type_match')

            cleaned_data['sce_code'] = sce_code # Set the actual sce_code for the model
        else:
            # If either is missing, let required=True handle it or add custom validation
            if not sce_display:
                self.add_error('sce_display', 'This field is required.')

        return cleaned_data

```

#### 4.3 Views (`accounts/views.py`)

Views are kept thin, delegating complex logic to the `DebitNote` model. HTMX requests are handled to provide partial updates and trigger custom events for refreshing the list.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from .models import DebitNote, DebitType, Employee, Customer, Supplier
from .forms import DebitNoteForm
import re # For parsing SCE input

# Mock user/company/financial year context for demonstration.
# In a real application, these would come from the authenticated user's session or profile.
MOCK_COMPANY_ID = 1
MOCK_FINANCIAL_YEAR_ID = 1
MOCK_SESSION_ID = 'automated_user'

class DebitNoteListView(ListView):
    model = DebitNote
    template_name = 'accounts/debitnote/list.html'
    context_object_name = 'debit_notes'

    def get_queryset(self):
        # Filter by company and financial year, similar to ASP.NET fillgrid()
        return DebitNote.objects.filter(
            company_id=MOCK_COMPANY_ID,
            financial_year_id=MOCK_FINANCIAL_YEAR_ID
        )

class DebitNoteTablePartialView(ListView):
    """
    Renders only the table content for HTMX partial updates.
    """
    model = DebitNote
    template_name = 'accounts/debitnote/_debitnote_table.html'
    context_object_name = 'debit_notes'

    def get_queryset(self):
        # Filter by company and financial year, similar to ASP.NET fillgrid()
        return DebitNote.objects.filter(
            company_id=MOCK_COMPANY_ID,
            financial_year_id=MOCK_FINANCIAL_YEAR_ID
        )

class DebitNoteCreateView(CreateView):
    model = DebitNote
    form_class = DebitNoteForm
    template_name = 'accounts/debitnote/_debitnote_form.html'
    success_url = reverse_lazy('debitnote_list') # Not strictly used with HTMX hx-swap="none"

    def form_valid(self, form):
        # Set auto-generated/contextual fields (Fat Model approach for business logic)
        form.instance.company_id = MOCK_COMPANY_ID
        form.instance.financial_year_id = MOCK_FINANCIAL_YEAR_ID
        form.instance.session_id = MOCK_SESSION_ID # Or self.request.user.username
        form.instance.debit_no = DebitNote.get_next_debit_no(
            MOCK_COMPANY_ID, MOCK_FINANCIAL_YEAR_ID
        )
        
        response = super().form_valid(form)
        messages.success(self.request, 'Debit Note added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, tells HTMX nothing to swap
                headers={
                    'HX-Trigger': 'refreshDebitNoteList' # Custom event to trigger table reload
                }
            )
        return response

    def form_invalid(self, form):
        # If form is invalid, HTMX re-renders the form with errors
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form})
        return response

class DebitNoteUpdateView(UpdateView):
    model = DebitNote
    form_class = DebitNoteForm
    template_name = 'accounts/debitnote/_debitnote_form.html'
    success_url = reverse_lazy('debitnote_list') # Not strictly used with HTMX hx-swap="none"

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Debit Note updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDebitNoteList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form})
        return response

class DebitNoteDeleteView(DeleteView):
    model = DebitNote
    template_name = 'accounts/debitnote/_debitnote_confirm_delete.html'
    success_url = reverse_lazy('debitnote_list') # Not strictly used with HTMX hx-swap="none"

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Debit Note deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDebitNoteList'
                }
            )
        return response

class DebitToAutocompleteView(View):
    """
    Provides autocomplete suggestions for the SCE field based on debit type.
    Replicates the ASP.NET WebMethod 'sql' and 'sql2' logic.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        debit_type_id = request.GET.get('type_id')
        suggestions = []

        try:
            debit_type_id = int(debit_type_id)
        except (ValueError, TypeError):
            return JsonResponse([], safe=False) # Invalid type ID

        if debit_type_id == 1: # Employee
            results = Employee.objects.filter(name__icontains=query)
            for obj in results:
                suggestions.append({"value": f"{obj.name} [{obj.id}]", "id": obj.id})
        elif debit_type_id == 2: # Customer
            results = Customer.objects.filter(name__icontains=query)
            for obj in results:
                suggestions.append({"value": f"{obj.name} [{obj.id}]", "id": obj.id})
        elif debit_type_id == 3: # Supplier
            results = Supplier.objects.filter(name__icontains=query)
            for obj in results:
                suggestions.append({"value": f"{obj.name} [{obj.id}]", "id": obj.id})
        
        # Return only the 'value' part as a list of strings, similar to ASP.NET web method
        return JsonResponse([s['value'] for s in suggestions], safe=False)

class DebitToInputPartialView(View):
    """
    Dynamically renders the SCE input field. Used when the Debit Type dropdown changes.
    The ASP.NET code clears the textbox on dropdown change; this will re-render the input
    with the correct hx-get for autocomplete based on the selected type.
    """
    def get(self, request, *args, **kwargs):
        type_id = request.GET.get('type_id')
        # Create a dummy form instance to render the field with appropriate ID/name
        form = DebitNoteForm() 
        # Re-render just the sce_display field part
        # Pass the type_id to the template for use in HTMX hx-get attribute
        return render(request, 'accounts/debitnote/_sce_input_field.html', {
            'field': form['sce_display'],
            'debit_type_id': type_id
        })
```

#### 4.4 Templates (`accounts/templates/accounts/debitnote/`)

Templates are designed for HTMX partials to enable dynamic updates without full page reloads. DataTables is integrated for list display. Alpine.js manages modal visibility.

**`list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Debit Notes</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'debitnote_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Debit Note
        </button>
    </div>
    
    <div id="debitnoteTable-container"
         hx-trigger="load, refreshDebitNoteList from:body"
         hx-get="{% url 'debitnote_table_partial' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center p-4">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Debit Notes...</p>
        </div>
    </div>
    
    <!-- Modal for forms -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for broader UI state.
        // For modals, the `_` syntax in HTMX handles visibility, minimal Alpine needed here.
    });
</script>
{% endblock %}
```

**`_debitnote_table.html`** (Partial for DataTables display)

```html
<table id="debitnoteTable" class="min-w-full bg-white shadow-md rounded-lg overflow-hidden">
    <thead class="bg-gray-100">
        <tr>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Date</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Debit No</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Type</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Debit To</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Reference</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Particulars</th>
            <th class="py-3 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-600 uppercase tracking-wider">Amount</th>
            <th class="py-3 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-600 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="divide-y divide-gray-100">
        {% for debit_note in debit_notes %}
        <tr>
            <td class="py-3 px-4 whitespace-nowrap">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap">{{ debit_note.date|date:"d-m-Y" }}</td>
            <td class="py-3 px-4 whitespace-nowrap">{{ debit_note.debit_no }}</td>
            <td class="py-3 px-4 whitespace-nowrap">{{ debit_note.debit_type.description }}</td>
            <td class="py-3 px-4 whitespace-nowrap">{{ debit_note.get_sce_display_name }}</td>
            <td class="py-3 px-4 whitespace-nowrap">{{ debit_note.reference|default:"-" }}</td>
            <td class="py-3 px-4 whitespace-nowrap">{{ debit_note.particulars|default:"-" }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-right">{{ debit_note.amount|floatformat:2 }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-center">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md text-sm mr-2"
                    hx-get="{% url 'debitnote_edit' debit_note.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md text-sm"
                    hx-get="{% url 'debitnote_delete' debit_note.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="9" class="py-4 px-4 text-center text-gray-500">No Debit Notes found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists to reinitialize
        if ($.fn.DataTable.isDataTable('#debitnoteTable')) {
            $('#debitnoteTable').DataTable().destroy();
        }
        $('#debitnoteTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true
        });
    });
</script>
```

**`_debitnote_form.html`** (Partial for Create/Update Form)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">{{ form.instance.pk|yesno:'Edit,Add' }} Debit Note</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div>
                <label for="{{ form.date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.date.label }}
                </label>
                {{ form.date }}
                {% if form.date.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.date.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.debit_type.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.debit_type.label }}
                </label>
                {{ form.debit_type }}
                {% if form.debit_type.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.debit_type.errors }}</p>
                {% endif %}
            </div>

            <div class="sm:col-span-2" id="debit_to_field_container">
                <label for="{{ form.sce_display.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.sce_display.label }}
                </label>
                <!-- This is the visible autocomplete input field -->
                <input 
                    type="text" 
                    name="{{ form.sce_display.name }}" 
                    id="{{ form.sce_display.id_for_label }}" 
                    class="{{ form.sce_display.field.widget.attrs.class }}" 
                    placeholder="{{ form.sce_display.field.widget.attrs.placeholder }}"
                    value="{{ form.sce_display.value|default_if_none:'' }}"
                    hx-get="{% url 'debitnote_sce_autocomplete' %}?type_id={{ form.debit_type.value|default:'' }}"
                    hx-trigger="keyup changed delay:500ms, search"
                    hx-target="#autocomplete-results"
                    hx-swap="innerHTML"
                    autocomplete="off"
                    _="on focusout wait 200ms remove @class.opacity-100 then add @class.opacity-0 from #autocomplete-results"
                >
                <div id="autocomplete-results" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg mt-1 w-full opacity-0 transition-opacity duration-300">
                    <!-- Suggestions will be loaded here -->
                </div>
                <!-- This hidden field stores the actual SCE ID that gets validated -->
                {{ form.sce_code }}
                {% if form.sce_display.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.sce_display.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.amount.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.amount.label }}
                </label>
                {{ form.amount }}
                {% if form.amount.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.amount.errors }}</p>
                {% endif %}
            </div>
        </div>

        <div class="mt-6">
            <label for="{{ form.reference.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.reference.label }}
            </label>
            {{ form.reference }}
            {% if form.reference.errors %}
            <p class="text-red-500 text-xs mt-1">{{ form.reference.errors }}</p>
            {% endif %}
        </div>

        <div class="mt-6">
            <label for="{{ form.particulars.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.particulars.label }}
            </label>
            {{ form.particulars }}
            {% if form.particulars.errors %}
            <p class="text-red-500 text-xs mt-1">{{ form.particulars.errors }}</p>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out">
                Save
            </button>
            <span id="form-spinner" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
            </span>
        </div>
    </form>
</div>

<!-- Alpine.js to handle autocomplete suggestions and select -->
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('autocomplete', (initialValue = '') => ({
            query: initialValue,
            suggestions: [],
            selectedId: null,
            init() {
                this.$watch('query', (value) => {
                    // Update the hidden sce_code field when query changes
                    // Only update if it's not a selection from suggestions
                    if (this.selectedId === null) {
                        document.getElementById('id_sce_code').value = '';
                    } else {
                        this.selectedId = null; // Reset selection flag
                    }
                });
            },
            selectSuggestion(suggestion) {
                // Assuming suggestion is "Name [ID]"
                const match = suggestion.match(/\[(\d+)\]$/);
                if (match) {
                    this.selectedId = match[1]; // Store the ID
                    document.getElementById('id_sce_code').value = match[1]; // Set hidden field
                    this.query = suggestion; // Set visible field to full string
                    this.suggestions = []; // Clear suggestions
                    this.$el.closest('#autocomplete-results').classList.remove('opacity-100'); // Hide dropdown
                }
            }
        }));
    });
</script>

<!-- HTMX will load the results into this structure -->
<template id="autocomplete-results-template">
    <div x-data="autocomplete('{{ form.sce_display.value|default_if_none:'' }}')" x-init="query = $el.previousElementSibling.value; $watch('query', (val) => { if(val.length > 0) $el.classList.add('opacity-100'); else $el.classList.remove('opacity-100'); });"
         hx-target="#autocomplete-results" hx-swap="innerHTML"
         class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg mt-1 w-full opacity-0 transition-opacity duration-300">
        <template x-for="suggestion in suggestions" :key="suggestion">
            <div @click="selectSuggestion(suggestion)"
                 class="px-4 py-2 cursor-pointer hover:bg-blue-100">
                <span x-text="suggestion"></span>
            </div>
        </template>
        <div x-show="suggestions.length === 0 && query.length > 0 && !$el.closest('form').querySelector('#id_sce_code').value" class="px-4 py-2 text-gray-500">No results found.</div>
    </div>
</template>

<!-- HTMX script to handle rendering the autocomplete suggestions -->
<script>
    document.body.addEventListener('htmx:afterOnLoad', function(evt) {
        if (evt.detail.xhr.getResponseHeader('Content-Type').includes('application/json') && evt.detail.elt.id === 'autocomplete-results') {
            const data = JSON.parse(evt.detail.xhr.responseText);
            const inputField = document.getElementById('id_sce_display');
            const autocompleteContainer = document.getElementById('autocomplete-results');

            Alpine.raw(autocompleteContainer.__alpine_scope).suggestions = data;
        }
    });

    // Handle initial rendering of SCE display field if it was pre-filled (edit mode)
    document.addEventListener('DOMContentLoaded', () => {
        const sceDisplayInput = document.getElementById('id_sce_display');
        const sceCodeInput = document.getElementById('id_sce_code');
        if (sceDisplayInput && sceCodeInput.value) {
            // Set initial state for Alpine.js if already in edit mode
            const container = document.getElementById('debit_to_field_container');
            if (container) {
                Alpine.init(container); // Re-initialize Alpine on the container if needed
            }
        }
    });

</script>
```

**`_debitnote_confirm_delete.html`** (Partial for Delete Confirmation)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete Debit Note <strong>{{ debitnote.debit_no }}</strong> ({{ debitnote.date|date:"d-m-Y" }})?</p>
    
    <div class="mt-8 flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button 
            type="button" 
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out"
            hx-delete="{% url 'debitnote_delete' debitnote.pk %}"
            hx-swap="none"
            hx-indicator="#delete-spinner"
            _="on htmx:afterRequest remove .is-active from #modal">
            Delete
        </button>
        <span id="delete-spinner" class="htmx-indicator ml-3">
            <div class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-red-500"></div>
        </span>
    </div>
</div>
```

**`_sce_input_field.html`** (Partial for dynamically updating SCE input)

```html
{# This template renders the sce_display field. It's used by hx-target #id_sce_display #}
{# when the debit_type dropdown changes, to ensure the hx-get attribute is updated. #}
<input 
    type="text" 
    name="{{ field.name }}" 
    id="{{ field.id_for_label }}" 
    class="{{ field.field.widget.attrs.class }}" 
    placeholder="{{ field.field.widget.attrs.placeholder }}"
    value="" {# Always clear value on type change #}
    hx-get="{% url 'debitnote_sce_autocomplete' %}?type_id={{ debit_type_id }}"
    hx-trigger="keyup changed delay:500ms, search"
    hx-target="#autocomplete-results"
    hx-swap="innerHTML"
    autocomplete="off"
    _="on focusout wait 200ms remove @class.opacity-100 then add @class.opacity-0 from #autocomplete-results"
>
<div id="autocomplete-results" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg mt-1 w-full opacity-0 transition-opacity duration-300">
    <!-- Suggestions will be loaded here -->
</div>
{# Hidden field for the actual ID #}
{{ form.sce_code }}
{% if field.errors %}
<p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
{% endif %}
```

#### 4.5 URLs (`accounts/urls.py`)

Defines the URL patterns for accessing the Debit Note views and HTMX partials.

```python
from django.urls import path
from .views import (
    DebitNoteListView, DebitNoteCreateView, DebitNoteUpdateView, DebitNoteDeleteView,
    DebitNoteTablePartialView, DebitToAutocompleteView, DebitToInputPartialView
)

urlpatterns = [
    path('debitnote/', DebitNoteListView.as_view(), name='debitnote_list'),
    path('debitnote/table/', DebitNoteTablePartialView.as_view(), name='debitnote_table_partial'),
    path('debitnote/add/', DebitNoteCreateView.as_view(), name='debitnote_add'),
    path('debitnote/edit/<int:pk>/', DebitNoteUpdateView.as_view(), name='debitnote_edit'),
    path('debitnote/delete/<int:pk>/', DebitNoteDeleteView.as_view(), name='debitnote_delete'),
    path('debitnote/sce-autocomplete/', DebitToAutocompleteView.as_view(), name='debitnote_sce_autocomplete'),
    path('debitnote/sce-autocomplete-input/', DebitToInputPartialView.as_view(), name='debitnote_sce_autocomplete_input'),
]
```

#### 4.6 Tests (`accounts/tests.py`)

Comprehensive unit tests for model methods and integration tests for all views ensure robust functionality and high test coverage.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import date
from unittest.mock import patch, MagicMock

# Import all models needed for testing
from .models import DebitNote, DebitType, Employee, Customer, Supplier

class DebitNoteModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for related models (managed=False, so no automatic creation)
        # Mocking creation for testing purposes if DB is not live
        # For a live DB, these objects would exist.
        cls.debit_type_emp = DebitType.objects.create(id=1, description="Employee")
        cls.debit_type_cust = DebitType.objects.create(id=2, description="Customer")
        cls.debit_type_supp = DebitType.objects.create(id=3, description="Supplier")

        cls.employee = Employee.objects.create(id=101, name="John Doe")
        cls.customer = Customer.objects.create(id=201, name="Acme Corp")
        cls.supplier = Supplier.objects.create(id=301, name="Global Suppliers")

        # Create test DebitNote instance
        cls.debit_note1 = DebitNote.objects.create(
            id=1,
            sys_date=timezone.now().date(),
            sys_time=timezone.now().time(),
            company_id=1,
            session_id='testuser',
            financial_year_id=1,
            date=date(2023, 1, 15),
            debit_no='0001',
            sce_code='101', # Employee ID
            amount=100.50,
            reference='Test Reference',
            particulars='Test Particulars',
            debit_type=cls.debit_type_emp # Link to Employee type
        )

        cls.debit_note2 = DebitNote.objects.create(
            id=2,
            sys_date=timezone.now().date(),
            sys_time=timezone.now().time(),
            company_id=1,
            session_id='testuser',
            financial_year_id=1,
            date=date(2023, 1, 16),
            debit_no='0002',
            sce_code='201', # Customer ID
            amount=250.75,
            reference='Another Ref',
            particulars='More Particulars',
            debit_type=cls.debit_type_cust # Link to Customer type
        )
  
    def test_debit_note_creation(self):
        obj = DebitNote.objects.get(id=1)
        self.assertEqual(obj.debit_no, '0001')
        self.assertEqual(obj.amount, 100.50)
        self.assertEqual(obj.debit_type.description, 'Employee')
        self.assertEqual(obj.sce_code, '101')
        
    def test_debit_no_generation(self):
        # Simulate creating a new debit note to test generation
        next_debit_no = DebitNote.get_next_debit_no(1, 1)
        self.assertEqual(next_debit_no, '0003')

        # Test with no existing debit notes for the company/year
        with patch.object(DebitNote.objects, 'filter') as mock_filter:
            mock_filter.return_value.aggregate.return_value = {'debit_no__max': None}
            next_debit_no_fresh = DebitNote.get_next_debit_no(2, 1) # Different company
            self.assertEqual(next_debit_no_fresh, '0001')

    def test_get_sce_display_name(self):
        # Test Employee
        self.assertEqual(self.debit_note1.get_sce_display_name(), 'John Doe')
        
        # Test Customer
        self.assertEqual(self.debit_note2.get_sce_display_name(), 'Acme Corp')

        # Test with a non-existent SCE code
        non_existent_debit_note = DebitNote.objects.create(
            id=3,
            sys_date=timezone.now().date(),
            sys_time=timezone.now().time(),
            company_id=1,
            session_id='testuser',
            financial_year_id=1,
            date=date(2023, 1, 17),
            debit_no='0003',
            sce_code='999', # Non-existent ID
            amount=50.00,
            reference='',
            particulars='',
            debit_type=self.debit_type_emp
        )
        self.assertEqual(non_existent_debit_note.get_sce_display_name(), 'Unknown Employee (ID: 999)')
        non_existent_debit_note.delete() # Clean up

    def test_parse_sce_input(self):
        self.assertEqual(DebitNote.parse_sce_input("Name [123]"), "123")
        self.assertIsNone(DebitNote.parse_sce_input("Name Without ID"))
        self.assertIsNone(DebitNote.parse_sce_input(""))

    def test_validate_sce_code_exists(self):
        self.assertTrue(DebitNote.validate_sce_code_exists('101', self.debit_type_emp.id)) # Employee
        self.assertFalse(DebitNote.validate_sce_code_exists('999', self.debit_type_emp.id)) # Non-existent Employee
        self.assertFalse(DebitNote.validate_sce_code_exists('101', self.debit_type_cust.id)) # Employee ID for Customer type

        self.assertTrue(DebitNote.validate_sce_code_exists('201', self.debit_type_cust.id)) # Customer
        self.assertTrue(DebitNote.validate_sce_code_exists('301', self.debit_type_supp.id)) # Supplier
        self.assertFalse(DebitNote.validate_sce_code_exists(None, self.debit_type_emp.id)) # Null input

class DebitNoteViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Ensure related models exist for tests that query them
        DebitType.objects.create(id=1, description="Employee")
        DebitType.objects.create(id=2, description="Customer")
        Employee.objects.create(id=101, name="John Doe")
        Customer.objects.create(id=201, name="Acme Corp")

        # Create a test DebitNote instance for update/delete
        self.debit_note = DebitNote.objects.create(
            id=10,
            sys_date=timezone.now().date(),
            sys_time=timezone.now().time(),
            company_id=1,
            session_id='testuser',
            financial_year_id=1,
            date=date(2023, 1, 15),
            debit_no='0010',
            sce_code='101', # Employee ID
            amount=100.50,
            reference='Initial Ref',
            particulars='Initial Particulars',
            debit_type_id=1 # Employee type
        )
    
    def test_list_view(self):
        response = self.client.get(reverse('debitnote_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/debitnote/list.html')
        self.assertTrue('debit_notes' in response.context)
        self.assertContains(response, self.debit_note.debit_no) # Check if data is displayed
        
    def test_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('debitnote_table_partial'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/debitnote/_debitnote_table.html')
        self.assertContains(response, self.debit_note.debit_no) # Check if data is displayed
        self.assertNotContains(response, '<!DOCTYPE html>') # Ensure it's a partial

    def test_create_view_get(self):
        response = self.client.get(reverse('debitnote_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/debitnote/_debitnote_form.html')
        self.assertTrue('form' in response.context)
        
    def test_create_view_post_success_htmx(self):
        data = {
            'date': '2023-02-01',
            'debit_type': '2', # Customer type
            'sce_display': 'Acme Corp [201]', # "Name [ID]" format
            'amount': '150.00',
            'reference': 'New Debit Ref',
            'particulars': 'New Debit Particulars',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('debitnote_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX No Content response
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDebitNoteList')
        
        # Verify object was created
        new_debit_note = DebitNote.objects.filter(sce_code='201').first()
        self.assertIsNotNone(new_debit_note)
        self.assertEqual(new_debit_note.amount, 150.00)
        self.assertEqual(new_debit_note.date, date(2023, 2, 1))

    def test_create_view_post_invalid_htmx(self):
        data = {
            'date': 'invalid-date', # Invalid date
            'debit_type': '1',
            'sce_display': 'NonExistentUser [999]',
            'amount': 'abc', # Invalid amount
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('debitnote_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'accounts/debitnote/_debitnote_form.html')
        self.assertContains(response, 'Enter a valid date.')
        self.assertContains(response, 'Enter a number.')
        self.assertContains(response, 'Selected &#x27;Debit To&#x27; entry does not match the chosen Type.') # Escaped error message

    def test_update_view_get(self):
        response = self.client.get(reverse('debitnote_edit', args=[self.debit_note.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/debitnote/_debitnote_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'value="John Doe [101]"') # Check initial SCE display name

    def test_update_view_post_success_htmx(self):
        data = {
            'date': '2023-03-01',
            'debit_type': '1',
            'sce_display': 'John Doe [101]',
            'amount': '123.45',
            'reference': 'Updated Ref',
            'particulars': 'Updated Particulars',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('debitnote_edit', args=[self.debit_note.id]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDebitNoteList')

        self.debit_note.refresh_from_db()
        self.assertEqual(self.debit_note.amount, 123.45)
        self.assertEqual(self.debit_note.reference, 'Updated Ref')

    def test_delete_view_get(self):
        response = self.client.get(reverse('debitnote_delete', args=[self.debit_note.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/debitnote/_debitnote_confirm_delete.html')
        self.assertTrue('debitnote' in response.context)

    def test_delete_view_post_success_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.delete(reverse('debitnote_delete', args=[self.debit_note.id]), **headers) # Use client.delete for DELETE requests
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDebitNoteList')
        self.assertFalse(DebitNote.objects.filter(id=self.debit_note.id).exists())

    def test_debit_to_autocomplete_view(self):
        # Test Employee autocomplete
        response = self.client.get(reverse('debitnote_sce_autocomplete'), {'q': 'john', 'type_id': '1'})
        self.assertEqual(response.status_code, 200)
        self.assertJSONEqual(response.content, ['John Doe [101]'])

        # Test Customer autocomplete
        response = self.client.get(reverse('debitnote_sce_autocomplete'), {'q': 'acme', 'type_id': '2'})
        self.assertEqual(response.status_code, 200)
        self.assertJSONEqual(response.content, ['Acme Corp [201]'])

        # Test with no query
        response = self.client.get(reverse('debitnote_sce_autocomplete'), {'q': '', 'type_id': '1'})
        self.assertEqual(response.status_code, 200)
        self.assertJSONEqual(response.content, ['John Doe [101]'])

        # Test invalid type_id
        response = self.client.get(reverse('debitnote_sce_autocomplete'), {'q': 'test', 'type_id': '99'})
        self.assertEqual(response.status_code, 200)
        self.assertJSONEqual(response.content, [])

    def test_debit_to_input_partial_view(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('debitnote_sce_autocomplete_input'), {'type_id': '1'}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/debitnote/_sce_input_field.html')
        self.assertContains(response, 'hx-get="/accounts/debitnote/sce-autocomplete/?type_id=1"')

```

### Step 5: HTMX and Alpine.js Integration

## Instructions:

*   **HTMX for CRUD:** All list, add, edit, and delete operations are initiated via HTMX requests.
    *   `list.html` uses `hx-get` to load `_debitnote_table.html` initially and on `refreshDebitNoteList` custom event.
    *   Add/Edit/Delete buttons use `hx-get` to load forms/confirmation into `#modalContent`.
    *   Form submissions (`hx-post`) for add/edit/delete use `hx-swap="none"` and `hx-trigger='refreshDebitNoteList'` to signal a table refresh without direct content swapping.
    *   A spinner (`htmx-indicator`) provides visual feedback during HTMX requests.
*   **Alpine.js for Modals and Autocomplete UI:**
    *   Modals are managed with Alpine.js `x-data` and `hidden` attribute toggled by HTMX `_` syntax (`on click add .is-active to #modal`).
    *   The `sce_display` autocomplete field uses Alpine.js `x-data="autocomplete"` to manage suggestions received from HTMX and handle selection, updating the hidden `sce_code` field. It also controls the visibility of the suggestion list.
*   **DataTables for List Views:**
    *   The `_debitnote_table.html` partial directly contains the `<table>` element which is then initialized as a DataTable using jQuery. The `hx-trigger="load, refreshDebitNoteList from:body"` ensures the DataTable is re-initialized correctly whenever the table content is refreshed by HTMX.
*   **No Full Page Reloads:** All interactions (form submissions, table refreshes, modal opening/closing) occur without full page reloads, providing a smooth user experience.
*   **DRY Templates:** `list.html` includes partials like `_debitnote_table.html`, `_debitnote_form.html`, and `_debitnote_confirm_delete.html` to promote reusability. The `_sce_input_field.html` partial specifically handles the dynamic nature of the `sce_display` input field.

## Final Notes

This comprehensive plan transforms the legacy ASP.NET Debit Note module into a modern Django application. By leveraging `managed = False` models, we connect directly to the existing database, minimizing data migration complexities. The "Fat Model, Thin View" approach centralizes business logic, making the application easier to maintain and test. The frontend completely shifts to HTMX and Alpine.js, eliminating complex JavaScript frameworks and enabling a highly interactive user experience without full page reloads. DataTables provides robust list management, and the inclusion of unit and integration tests ensures the reliability and correctness of the new Django codebase, ready for AI-assisted development and deployment.