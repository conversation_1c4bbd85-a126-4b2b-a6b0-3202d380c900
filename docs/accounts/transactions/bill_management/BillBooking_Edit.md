## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:** The ASP.NET code interacts with `tblACC_BillBooking_Master` as the primary table for bill booking entries. It also performs lookups on `tblFinancial_master` for financial year details and `tblMM_Supplier_master` for supplier information. The C# `loadData` method constructs complex SQL queries and manipulates data in memory before binding to the `GridView`.

**Identified Tables and Columns:**

- **`tblACC_BillBooking_Master`** (Primary Model: `BillBookingMaster`)
    - `Id` (INT, PK)
    - `FinYearId` (INT, FK to `tblFinancial_master`)
    - `PVEVNo` (NVARCHAR)
    - `SysDate` (DATETIME, used as `PVEVDate`)
    - `BillNo` (NVARCHAR)
    - `BillDate` (DATETIME)
    - `SupplierId` (INT, FK to `tblMM_Supplier_master`)
    - `CompId` (INT, for company identification)

- **`tblFinancial_master`** (Lookup Model: `FinancialMaster`)
    - `FinYearId` (INT, PK)
    - `FinYear` (NVARCHAR)

- **`tblMM_Supplier_master`** (Lookup Model: `SupplierMaster`)
    - `SupplierId` (INT, PK)
    - `SupplierName` (NVARCHAR)

### Step 2: Identify Backend Functionality

**Analysis:** The ASP.NET page `BillBooking_Edit.aspx` serves primarily as a search and listing interface for bill booking records. It does not contain direct Create, Update, or Delete forms on the page itself. The `HyperLinkField` indicates navigation to a separate "details" page for editing.

- **Read (Listing & Search):**
    - Displays a paginated list of `BillBookingMaster` records.
    - Filters records by `CompId` and `FinYearId` (implicitly from session).
    - Supports dynamic searching by:
        - "Supplier Name" (`SupplierId` via `txtSupplier` with autocomplete).
        - "PVEVNo" (`PVEVNo` via `Txtfield`).
        - "PO No" (The ASP.NET code has an orphaned `z` variable for this, suggesting an intended filter, but it's not applied in the main query. For Django, we will assume it should filter by `PVEVNo` or require a new join/relationship if a `tblMM_PO_Master` exists and is relevant to `BillBookingMaster`). We will implement it as a string search on `PVEVNo` for now.
    - Pagination is handled server-side in ASP.NET; in Django, we will leverage client-side DataTables for this unless explicitly configured for server-side processing.

- **Create/Update/Delete:**
    - Not directly implemented on this page. The "PEVE No" column acts as a hyperlink to a `BillBooking_Edit_Details.aspx` page. In Django, this will translate to navigation to an `_edit` URL or trigger an HTMX modal for editing a specific record. For completeness, standard Django `CreateView`, `UpdateView`, and `DeleteView` patterns with HTMX modal interactions will be provided as placeholders.

### Step 3: Infer UI Components

**Analysis:** The ASP.NET page uses standard Web Forms controls for user interaction and data display.

- **Search Controls:**
    - `asp:DropDownList ID="DropDownList1"`: Maps to a `<select>` element for search type.
    - `asp:TextBox ID="txtSupplier"` and `asp:TextBox ID="Txtfield"`: Map to `<input type="text">` elements.
    - `cc1:AutoCompleteExtender`: This AJAX component will be replaced by a custom HTMX endpoint that returns JSON for a text input's autocomplete functionality.
    - `asp:Button ID="Button1"`: Maps to a `<button type="submit">` for triggering the search.
- **Data Display:**
    - `asp:GridView ID="GridView2"`: Maps to an HTML `<table>` element, enhanced by DataTables for client-side features like sorting, filtering, and pagination.
    - `asp:HyperLinkField`: Maps to a standard `<a>` tag or an HTMX-triggered button for an edit modal.
- **Dynamic Visibility:** The C# code modifies the visibility of `txtSupplier` and `Txtfield` based on the dropdown selection. This will be handled by Alpine.js in the Django frontend.

### Step 4: Generate Django Code

We will create a Django app named `accounts_transactions` to house this functionality.

#### 4.1 Models

We will define three models corresponding to the identified database tables, using `managed = False` to connect to existing tables.

```python
# accounts_transactions/models.py
from django.db import models
from django.urls import reverse

class FinancialMaster(models.Model):
    # FinYearId as PK is implicit if no primary_key=True is set on another field
    # Assuming FinYearId is the primary key as per ASP.NET
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class SupplierMaster(models.Model):
    # SupplierId as PK is implicit if no primary_key=True is set on another field
    # Assuming SupplierId is the primary key as per ASP.NET
    supplier_id = models.IntegerField(db_column='SupplierId', primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.supplier_name

class BillBookingMasterManager(models.Manager):
    def search_bills(self, company_id, fin_year_id_current, search_type=None, search_value=None):
        # Base queryset filters by company and financial year (less than or equal to current)
        queryset = self.filter(
            company_id=company_id,
            fin_year__fin_year_id__lte=fin_year_id_current
        ).order_by('-id')

        if search_type == '1' and search_value:  # Search by Supplier Name
            # Attempt to extract SupplierId if format is "Name [ID]"
            supplier_id = None
            if '[' in search_value and ']' in search_value:
                try:
                    parts = search_value.split('[')
                    if len(parts) > 1:
                        supplier_id_str = parts[-1].strip(']')
                        supplier_id = int(supplier_id_str)
                except ValueError:
                    pass # Keep supplier_id as None if parsing fails

            if supplier_id is not None:
                queryset = queryset.filter(supplier__supplier_id=supplier_id)
            else:
                # Fallback to name contains search if no valid ID or format is just name
                queryset = queryset.filter(supplier__supplier_name__icontains=search_value)
        elif search_type == '2' and search_value:  # Search by PVEV No
            queryset = queryset.filter(pvev_no__iexact=search_value)
        elif search_type == '3' and search_value:  # Search by PO No (mapping to PVEV No for now)
            # Original ASP.NET code was ambiguous here.
            # Assuming it should filter PVEVNo as a text search for now.
            queryset = queryset.filter(pvev_no__icontains=search_value)

        return queryset

class BillBookingMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    fin_year = models.ForeignKey(FinancialMaster, on_delete=models.DO_NOTHING, db_column='FinYearId') # DO_NOTHING for existing FKs
    pvev_no = models.CharField(db_column='PVEVNo', max_length=50, blank=True, null=True)
    sys_date = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    bill_no = models.CharField(db_column='BillNo', max_length=50, blank=True, null=True)
    bill_date = models.DateTimeField(db_column='BillDate', blank=True, null=True)
    supplier = models.ForeignKey(SupplierMaster, on_delete=models.DO_NOTHING, db_column='SupplierId')
    company_id = models.IntegerField(db_column='CompId', blank=True, null=True) # Assuming CompId

    objects = BillBookingMasterManager()

    class Meta:
        managed = False
        db_table = 'tblACC_BillBooking_Master'
        verbose_name = 'Bill Booking'
        verbose_name_plural = 'Bill Bookings'

    def __str__(self):
        return f"{self.pvev_no} - {self.bill_no}"

    # Helper properties for UI display, if needed
    @property
    def formatted_pvev_date(self):
        if self.sys_date:
            return self.sys_date.strftime('%d/%m/%Y')
        return ''

    @property
    def formatted_bill_date(self):
        if self.bill_date:
            return self.bill_date.strftime('%d/%m/%Y')
        return ''

    def get_absolute_url(self):
        return reverse('billbookingmaster_edit', kwargs={'pk': self.pk})

    # Example of a 'fat model' method (business logic)
    def can_be_edited_by_user(self, user):
        """
        Check if the current bill booking can be edited by a given user.
        Example business logic: only if it belongs to their company and is within the active financial year.
        """
        # Assume user has a 'company_id' and 'financial_year_id' attribute
        # This logic needs to be aligned with your authentication/authorization system
        return (self.company_id == user.company_id and
                self.fin_year.fin_year_id == user.financial_year_id)

```

#### 4.2 Forms

We will define a search form for the list view and a placeholder model form for `BillBookingMaster` CRUD operations.

```python
# accounts_transactions/forms.py
from django import forms
from .models import BillBookingMaster, SupplierMaster, FinancialMaster

class BillBookingSearchForm(forms.Form):
    SEARCH_CHOICES = [
        ('1', 'Supplier Name'),
        ('2', 'PVEV No'),
        ('3', 'PO No'), # As per original ASP.NET, though mapping to PVEVNo search for now
    ]
    search_by = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'x-model': 'searchType', 'hx-get': 'hx-get="." hx-trigger="change" hx-push-url="true" hx-swap="none"'}), # Removed hx-get/hx-trigger for simplicity, will handle via Alpine.js
        label='Search By'
    )
    supplier_search_value = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter Supplier Name...',
            'hx-get': '/accounts-transactions/billbookingmaster/supplier-autocomplete/',
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#supplier-autocomplete-results',
            'hx-indicator': '.htmx-indicator',
            'hx-swap': 'innerHTML',
            'id': 'txtSupplier', # Match original ID for easier mapping
            'x-show': "searchType === '1'", # Alpine.js control
            'x-model': 'supplierSearchInput',
            '@focusout': 'setTimeout(() => { showAutocomplete = false }, 100)', # Hide on focus out
            '@focusin': 'showAutocomplete = true',
        }),
        label='Supplier Name'
    )
    field_search_value = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter PVEV No or PO No...',
            'id': 'Txtfield', # Match original ID for easier mapping
            'x-show': "searchType === '2' || searchType === '3'", # Alpine.js control
        }),
        label='Search Value'
    )

    def clean(self):
        cleaned_data = super().clean()
        search_by = cleaned_data.get('search_by')
        supplier_value = cleaned_data.get('supplier_search_value')
        field_value = cleaned_data.get('field_search_value')

        if search_by == '1' and not supplier_value:
            self.add_error('supplier_search_value', 'Supplier name is required for this search type.')
        elif search_by in ['2', '3'] and not field_value:
            self.add_error('field_search_value', 'Search value is required for this search type.')
        return cleaned_data

class BillBookingMasterForm(forms.ModelForm):
    class Meta:
        model = BillBookingMaster
        fields = ['fin_year', 'pvev_no', 'sys_date', 'bill_no', 'bill_date', 'supplier']
        widgets = {
            'fin_year': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'pvev_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sys_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bill_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bill_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'supplier': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
    
    # Example custom validation for date range
    def clean_bill_date(self):
        bill_date = self.cleaned_data['bill_date']
        if bill_date and bill_date > models.DateField(auto_now_add=True).today():
            raise forms.ValidationError("Bill date cannot be in the future.")
        return bill_date

```

#### 4.3 Views

We will implement a `ListView` for the main page, a partial view for the DataTables table, and standard CRUD views for Bill Booking, designed to work with HTMX modals.
We need to simulate `Session["compid"]` and `Session["finyear"]`. For demonstration, we'll use placeholder values or extract them from `request.user` if an authentication system is in place.

```python
# accounts_transactions/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db.models import Q
from django.shortcuts import render
from .models import BillBookingMaster, SupplierMaster, FinancialMaster
from .forms import BillBookingMasterForm, BillBookingSearchForm
import json

# Placeholder for user-specific data (replace with actual session/user attributes)
# In a real application, these would come from request.user or session data
def get_user_company_info(request):
    # Example: If user is logged in, and user profile has these fields
    # company_id = request.user.company_id
    # fin_year_id = request.user.financial_year_id
    # For demonstration, use hardcoded values
    company_id = 1 # Example Company ID
    fin_year_id = 2023 # Example Financial Year ID

    # Fetch actual FinancialMaster instance
    try:
        current_fin_year = FinancialMaster.objects.get(fin_year_id=fin_year_id)
    except FinancialMaster.DoesNotExist:
        current_fin_year = None # Or raise an error, or pick a default

    return company_id, fin_year_id, current_fin_year

class BillBookingMasterListView(ListView):
    model = BillBookingMaster
    template_name = 'accounts_transactions/billbookingmaster/list.html'
    context_object_name = 'billbookingmasters'
    paginate_by = 20 # Although DataTables handles pagination, this is useful for initial server-side load

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        company_id, fin_year_id, current_fin_year = get_user_company_info(self.request)

        # Initialize the search form
        search_by = self.request.GET.get('search_by', '1') # Default to Supplier Name
        supplier_search_value = self.request.GET.get('supplier_search_value', '')
        field_search_value = self.request.GET.get('field_search_value', '')

        context['search_form'] = BillBookingSearchForm(initial={
            'search_by': search_by,
            'supplier_search_value': supplier_search_value,
            'field_search_value': field_search_value,
        })
        context['current_search_type'] = search_by # For Alpine.js to initialize correctly
        return context

    # This view primarily renders the base page. The table content is loaded via HTMX.
    # We will fetch data for the initial load if no HTMX header.
    def get_queryset(self):
        # Initial queryset, actual filtering happens in BillBookingMasterTablePartialView
        company_id, fin_year_id, current_fin_year = get_user_company_info(self.request)
        if not company_id or not fin_year_id:
            return BillBookingMaster.objects.none()
        
        # Load an initial small set for the table to appear, or just render placeholder
        # The main data load will be via the partial view.
        return BillBookingMaster.objects.filter(company_id=company_id, fin_year__fin_year_id__lte=fin_year_id).order_by('-id')[:20]


class BillBookingMasterTablePartialView(ListView):
    """
    Returns only the table rows for HTMX.
    """
    model = BillBookingMaster
    template_name = 'accounts_transactions/billbookingmaster/_billbookingmaster_table.html'
    context_object_name = 'billbookingmasters'

    def get_queryset(self):
        company_id, fin_year_id, current_fin_year = get_user_company_info(self.request)
        
        # Ensure we have company and financial year context
        if not company_id or not fin_year_id:
            # Log this or return an appropriate error/empty queryset
            messages.error(self.request, "Company or Financial Year context is missing.")
            return BillBookingMaster.objects.none()

        # Extract search parameters from GET request
        search_by = self.request.GET.get('search_by')
        supplier_search_value = self.request.GET.get('supplier_search_value')
        field_search_value = self.request.GET.get('field_search_value')

        # Use the BillBookingMasterManager for search logic
        # Pass the appropriate search_value based on search_by
        search_value_to_use = supplier_search_value if search_by == '1' else field_search_value

        queryset = BillBookingMaster.objects.search_bills(
            company_id=company_id,
            fin_year_id_current=fin_year_id,
            search_type=search_by,
            search_value=search_value_to_use
        )
        return queryset

class SupplierAutocompleteView(View):
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        # Simulate CompId from session (replace with actual user/company logic)
        company_id, _, _ = get_user_company_info(request) 

        # Filter suppliers belonging to the company and matching the query
        suppliers = SupplierMaster.objects.filter(
            supplier_name__icontains=query
        ).values('supplier_id', 'supplier_name')[:10] # Limit results

        results = []
        for supplier in suppliers:
            # Format: "SupplierName [SupplierId]" as per original ASP.NET
            results.append({
                'id': supplier['supplier_id'],
                'text': f"{supplier['supplier_name']} [{supplier['supplier_id']}]"
            })
        return JsonResponse(results, safe=False)

class BillBookingMasterCreateView(CreateView):
    model = BillBookingMaster
    form_class = BillBookingMasterForm
    template_name = 'accounts_transactions/billbookingmaster/_billbookingmaster_form.html'
    success_url = reverse_lazy('billbookingmaster_list') # Not strictly used for HTMX, but good practice

    def form_valid(self, form):
        company_id, fin_year_id, current_fin_year = get_user_company_info(self.request)
        form.instance.company_id = company_id
        form.instance.fin_year = current_fin_year # Assign the FK object
        
        response = super().form_valid(form)
        messages.success(self.request, 'Bill Booking added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX, return a 204 No Content and trigger a client-side event
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': json.dumps({
                        'refreshBillBookingList': None,
                        'hideModal': None # Custom event to hide the modal
                    })
                }
            )
        return response
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # Optionally, pre-populate financial year if needed
        _, fin_year_id, current_fin_year = get_user_company_info(self.request)
        if current_fin_year:
            kwargs['initial'] = {'fin_year': current_fin_year.fin_year_id}
        return kwargs

class BillBookingMasterUpdateView(UpdateView):
    model = BillBookingMaster
    form_class = BillBookingMasterForm
    template_name = 'accounts_transactions/billbookingmaster/_billbookingmaster_form.html'
    success_url = reverse_lazy('billbookingmaster_list') # Not strictly used for HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Bill Booking updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': json.dumps({
                        'refreshBillBookingList': None,
                        'hideModal': None
                    })
                }
            )
        return response

class BillBookingMasterDeleteView(DeleteView):
    model = BillBookingMaster
    template_name = 'accounts_transactions/billbookingmaster/_billbookingmaster_confirm_delete.html'
    success_url = reverse_lazy('billbookingmaster_list') # Not strictly used for HTMX

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Bill Booking deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': json.dumps({
                        'refreshBillBookingList': None,
                        'hideModal': None
                    })
                }
            )
        return response

```

#### 4.4 Templates

**`accounts_transactions/billbookingmaster/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ searchType: '{{ current_search_type }}', showAutocomplete: false, supplierSearchInput: '' }" @hide-modal.window="document.getElementById('modal').classList.add('hidden')">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Bill Booking - Edit</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'billbookingmaster_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal then remove .hidden from #modal">
            Add New Bill Booking
        </button>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <form hx-get="{% url 'billbookingmaster_table' %}" hx-target="#billbookingmasterTable-container" hx-swap="innerHTML" hx-trigger="submit, keyup from:body changed delay:500ms when event.target.id === 'Txtfield' or event.target.id === 'txtSupplier'" class="space-y-4">
            <div class="flex items-end space-x-4">
                <div class="flex-1">
                    <label for="{{ search_form.search_by.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By</label>
                    {{ search_form.search_by }}
                </div>
                <div class="flex-1 relative">
                    <div x-show="searchType === '1'">
                        <label for="{{ search_form.supplier_search_value.id_for_label }}" class="block text-sm font-medium text-gray-700">Supplier Name</label>
                        {{ search_form.supplier_search_value }}
                        <div id="supplier-autocomplete-results" class="absolute z-10 bg-white border border-gray-300 mt-1 w-full rounded-md shadow-lg" x-show="showAutocomplete && supplierSearchInput.length > 0"></div>
                    </div>
                    <div x-show="searchType === '2' || searchType === '3'">
                        <label for="{{ search_form.field_search_value.id_for_label }}" class="block text-sm font-medium text-gray-700">Search Value</label>
                        {{ search_form.field_search_value }}
                    </div>
                </div>
                <button type="submit" id="Button1" class="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
                    Search
                </button>
            </div>
            {% if search_form.errors %}
                <div class="text-red-500 text-sm mt-2">
                    {% for field, errors in search_form.errors.items %}
                        {% for error in errors %}
                            <p>{{ field }}: {{ error }}</p>
                        {% endfor %}
                    {% endfor %}
                </div>
            {% endif %}
        </form>
    </div>
    
    <div id="billbookingmasterTable-container"
         hx-trigger="load, refreshBillBookingList from:body"
         hx-get="{% url 'billbookingmaster_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg p-6">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Bill Booking records...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 z-50 bg-gray-600 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me then add .hidden to me
            on htmx:afterOnLoad from #modalContent if event.detail.xhr.status == 204 hide me"
         >
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 sm:mx-auto relative">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<script>
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.target.id === 'billbookingmasterTable-container') {
            // Re-initialize DataTable after HTMX swaps the content
            $('#billbookingmasterTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true, // Destroy existing instance if any
                "responsive": true
            });
        }
    });

    document.addEventListener('hideModal', function() {
        document.getElementById('modal').classList.add('hidden');
    });

    // Handle autocomplete selection
    document.body.addEventListener('click', function(e) {
        if (e.target.closest('#supplier-autocomplete-results')) {
            const selectedText = e.target.textContent.trim();
            const input = document.getElementById('txtSupplier');
            if (input) {
                input.value = selectedText;
                input.dispatchEvent(new Event('input', { bubbles: true })); // Trigger HTMX re-evaluation
                document.getElementById('supplier-autocomplete-results').innerHTML = ''; // Clear results
            }
        }
    });

</script>
{% endblock %}
```

**`accounts_transactions/billbookingmaster/_billbookingmaster_table.html`** (Partial for DataTables)

```html
<div class="overflow-x-auto">
    <table id="billbookingmasterTable" class="min-w-full bg-white border border-gray-200">
        <thead>
            <tr class="bg-gray-100 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">
                <th class="py-3 px-4 border-b border-gray-200">SN</th>
                <th class="py-3 px-4 border-b border-gray-200">Fin Year</th>
                <th class="py-3 px-4 border-b border-gray-200">PEVE No</th>
                <th class="py-3 px-4 border-b border-gray-200">Date</th>
                <th class="py-3 px-4 border-b border-gray-200">Bill No</th>
                <th class="py-3 px-4 border-b border-gray-200">Bill Date</th>
                <th class="py-3 px-4 border-b border-gray-200">Name of Supplier</th>
                <th class="py-3 px-4 border-b border-gray-200">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for obj in billbookingmasters %}
            <tr class="hover:bg-gray-50 text-sm text-gray-700">
                <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.fin_year.fin_year }}</td>
                <td class="py-2 px-4 border-b border-gray-200">
                    <a href="{% url 'billbookingmaster_edit' obj.pk %}" 
                       hx-get="{% url 'billbookingmaster_edit' obj.pk %}"
                       hx-target="#modalContent"
                       hx-trigger="click"
                       _="on click add .is-active to #modal then remove .hidden from #modal"
                       class="text-blue-600 hover:text-blue-800 hover:underline">
                        {{ obj.pvev_no }}
                    </a>
                </td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.formatted_pvev_date }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.bill_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.formatted_bill_date }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.supplier.supplier_name }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 transition duration-300 ease-in-out"
                        hx-get="{% url 'billbookingmaster_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal then remove .hidden from #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md transition duration-300 ease-in-out"
                        hx-get="{% url 'billbookingmaster_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal then remove .hidden from #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="py-4 px-4 text-center text-gray-500">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

{% comment %}
    The DataTable initialization script is moved to the list.html to ensure it runs after HTMX swap.
    This partial only needs the table structure.
{% endcomment %}
```

**`accounts_transactions/billbookingmaster/_billbookingmaster_form.html`** (Partial for Add/Edit Modal)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Bill Booking</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-6">
        {% csrf_token %}
        
        {% for field in form %}
        <div class="mb-4">
            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ field.label }}
                {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
            </label>
            {{ field }}
            {% if field.help_text %}
            <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
            {% endif %}
            {% if field.errors %}
            <div class="text-red-600 text-sm mt-2">
                {% for error in field.errors %}
                <p>{{ error }}</p>
                {% endfor %}
            </div>
            {% endif %}
        </div>
        {% endfor %}

        {% if form.non_field_errors %}
        <div class="text-red-600 text-sm mt-4">
            {% for error in form.non_field_errors %}
            <p>{{ error }}</p>
            {% endfor %}
        </div>
        {% endif %}
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click send hideModal to window">
                Cancel
            </button>
            <button 
                type="submit" 
                class="px-5 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Save Bill Booking
            </button>
        </div>
    </form>
</div>
```

**`accounts_transactions/billbookingmaster/_billbookingmaster_confirm_delete.html`** (Partial for Delete Modal)

```html
<div class="p-6 text-center">
    <svg class="mx-auto h-16 w-16 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
    </svg>
    <h3 class="mt-4 text-lg font-medium text-gray-900">Confirm Deletion</h3>
    <p class="mt-2 text-sm text-gray-500">
        Are you sure you want to delete the Bill Booking record for <strong>"{{ object.pvev_no }} - {{ object.supplier.supplier_name }}"</strong>? This action cannot be undone.
    </p>

    <form hx-delete="{% url 'billbookingmaster_delete' object.pk %}" hx-swap="none" class="mt-5 sm:mt-6 sm:flex sm:flex-row-reverse space-x-4">
        {% csrf_token %}
        <button type="submit" 
                class="inline-flex justify-center w-full rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
            Delete
        </button>
        <button type="button" 
                class="mt-3 inline-flex justify-center w-full rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:w-auto sm:text-sm"
                _="on click send hideModal to window">
            Cancel
        </button>
    </form>
</div>
```

#### 4.5 URLs

We will define URL patterns for the list view, the HTMX table partial, the autocomplete endpoint, and the CRUD operations.

```python
# accounts_transactions/urls.py
from django.urls import path
from .views import (
    BillBookingMasterListView,
    BillBookingMasterTablePartialView,
    BillBookingMasterCreateView,
    BillBookingMasterUpdateView,
    BillBookingMasterDeleteView,
    SupplierAutocompleteView,
)

urlpatterns = [
    path('billbookingmaster/', BillBookingMasterListView.as_view(), name='billbookingmaster_list'),
    path('billbookingmaster/table/', BillBookingMasterTablePartialView.as_view(), name='billbookingmaster_table'),
    path('billbookingmaster/add/', BillBookingMasterCreateView.as_view(), name='billbookingmaster_add'),
    path('billbookingmaster/edit/<int:pk>/', BillBookingMasterUpdateView.as_view(), name='billbookingmaster_edit'),
    path('billbookingmaster/delete/<int:pk>/', BillBookingMasterDeleteView.as_view(), name='billbookingmaster_delete'),
    path('billbookingmaster/supplier-autocomplete/', SupplierAutocompleteView.as_view(), name='supplier_autocomplete'),
]

```

#### 4.6 Tests

Comprehensive tests for models (unit) and views (integration) to ensure functionality and adhere to test coverage requirements.

```python
# accounts_transactions/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import BillBookingMaster, FinancialMaster, SupplierMaster
from .views import get_user_company_info # To mock user context

# Mock user company info for testing
def mock_get_user_company_info(request):
    return 1, 2023, FinancialMaster.objects.get(fin_year_id=2023)

class ModelTestSetup(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create Financial Years
        cls.fin_year_2022 = FinancialMaster.objects.create(fin_year_id=2022, fin_year='2022-2023')
        cls.fin_year_2023 = FinancialMaster.objects.create(fin_year_id=2023, fin_year='2023-2024')
        cls.fin_year_2024 = FinancialMaster.objects.create(fin_year_id=2024, fin_year='2024-2025')

        # Create Suppliers
        cls.supplier_alpha = SupplierMaster.objects.create(supplier_id=101, supplier_name='Alpha Corp')
        cls.supplier_beta = SupplierMaster.objects.create(supplier_id=102, supplier_name='Beta Industries')
        cls.supplier_gamma = SupplierMaster.objects.create(supplier_id=103, supplier_name='Gamma Co')

        # Create Bill Booking entries
        cls.bill1 = BillBookingMaster.objects.create(
            id=1, fin_year=cls.fin_year_2023, pvev_no='PVEV001', sys_date=timezone.now(),
            bill_no='BILL001', bill_date=timezone.now(), supplier=cls.supplier_alpha, company_id=1
        )
        cls.bill2 = BillBookingMaster.objects.create(
            id=2, fin_year=cls.fin_year_2022, pvev_no='PVEV002', sys_date=timezone.now(),
            bill_no='BILL002', bill_date=timezone.now(), supplier=cls.supplier_beta, company_id=1
        )
        cls.bill3 = BillBookingMaster.objects.create(
            id=3, fin_year=cls.fin_year_2023, pvev_no='PVEV003', sys_date=timezone.now(),
            bill_no='BILL003', bill_date=timezone.now(), supplier=cls.supplier_gamma, company_id=2 # Different company
        )
        cls.bill4 = BillBookingMaster.objects.create(
            id=4, fin_year=cls.fin_year_2023, pvev_no='PVEV004', sys_date=timezone.now(),
            bill_no='BILL004', bill_date=timezone.now(), supplier=cls.supplier_alpha, company_id=1
        )

class FinancialMasterModelTest(ModelTestSetup):
    def test_fin_year_creation(self):
        self.assertEqual(self.fin_year_2023.fin_year, '2023-2024')
        self.assertEqual(self.fin_year_2023.fin_year_id, 2023)
        self.assertEqual(str(self.fin_year_2023), '2023-2024')

class SupplierMasterModelTest(ModelTestSetup):
    def test_supplier_creation(self):
        self.assertEqual(self.supplier_alpha.supplier_name, 'Alpha Corp')
        self.assertEqual(self.supplier_alpha.supplier_id, 101)
        self.assertEqual(str(self.supplier_alpha), 'Alpha Corp')

class BillBookingMasterModelTest(ModelTestSetup):
    def test_bill_booking_creation(self):
        self.assertEqual(self.bill1.pvev_no, 'PVEV001')
        self.assertEqual(self.bill1.supplier.supplier_name, 'Alpha Corp')
        self.assertEqual(self.bill1.fin_year.fin_year, '2023-2024')
        self.assertEqual(str(self.bill1), 'PVEV001 - BILL001')

    def test_formatted_dates(self):
        self.assertRegex(self.bill1.formatted_pvev_date, r'^\d{2}/\d{2}/\d{4}$')
        self.assertRegex(self.bill1.formatted_bill_date, r'^\d{2}/\d{2}/\d{4}$')

    def test_search_bills_by_supplier_id(self):
        # Mock the get_user_company_info
        from accounts_transactions import views
        original_get_user_company_info = views.get_user_company_info
        views.get_user_company_info = mock_get_user_company_info

        # Supplier Name search with ID
        queryset = BillBookingMaster.objects.search_bills(
            company_id=1, fin_year_id_current=2023, search_type='1', search_value='Alpha Corp [101]'
        )
        self.assertIn(self.bill1, queryset)
        self.assertIn(self.bill4, queryset)
        self.assertNotIn(self.bill2, queryset)
        self.assertNotIn(self.bill3, queryset)
        self.assertEqual(queryset.count(), 2)

        # Supplier Name search without ID (fallback to contains)
        queryset = BillBookingMaster.objects.search_bills(
            company_id=1, fin_year_id_current=2023, search_type='1', search_value='alpha'
        )
        self.assertIn(self.bill1, queryset)
        self.assertIn(self.bill4, queryset)
        self.assertEqual(queryset.count(), 2) # Should find both Alpha Corp records

        # Restore original
        views.get_user_company_info = original_get_user_company_info

    def test_search_bills_by_pvev_no(self):
        from accounts_transactions import views
        original_get_user_company_info = views.get_user_company_info
        views.get_user_company_info = mock_get_user_company_info

        queryset = BillBookingMaster.objects.search_bills(
            company_id=1, fin_year_id_current=2023, search_type='2', search_value='PVEV001'
        )
        self.assertIn(self.bill1, queryset)
        self.assertNotIn(self.bill2, queryset)
        self.assertEqual(queryset.count(), 1)

        views.get_user_company_info = original_get_user_company_info

    def test_search_bills_by_po_no_ambiguous(self):
        # For the ambiguous PO No search, we mapped it to PVEVNo contains.
        from accounts_transactions import views
        original_get_user_company_info = views.get_user_company_info
        views.get_user_company_info = mock_get_user_company_info

        queryset = BillBookingMaster.objects.search_bills(
            company_id=1, fin_year_id_current=2023, search_type='3', search_value='001'
        )
        self.assertIn(self.bill1, queryset)
        self.assertEqual(queryset.count(), 1) # PVEV001 contains 001

        views.get_user_company_info = original_get_user_company_info

    def test_search_bills_company_and_fin_year_filter(self):
        from accounts_transactions import views
        original_get_user_company_info = views.get_user_company_info
        views.get_user_company_info = mock_get_user_company_info

        # Bill 3 is for company_id 2, so it should not be in results for company_id 1
        queryset = BillBookingMaster.objects.search_bills(company_id=1, fin_year_id_current=2023)
        self.assertIn(self.bill1, queryset)
        self.assertIn(self.bill2, queryset) # bill2 is fin year 2022 which is <= 2023
        self.assertIn(self.bill4, queryset)
        self.assertNotIn(self.bill3, queryset)
        self.assertEqual(queryset.count(), 3)

        views.get_user_company_info = original_get_user_company_info


class BillBookingMasterViewsTest(ModelTestSetup):
    def setUp(self):
        self.client = Client()
        # Mock the user company info for all view tests
        from accounts_transactions import views
        self.original_get_user_company_info = views.get_user_company_info
        views.get_user_company_info = mock_get_user_company_info

    def tearDown(self):
        from accounts_transactions import views
        views.get_user_company_info = self.original_get_user_company_info # Restore original

    def test_list_view_get(self):
        response = self.client.get(reverse('billbookingmaster_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_transactions/billbookingmaster/list.html')
        self.assertContains(response, 'Bill Booking - Edit')
        self.assertIsInstance(response.context['search_form'], views.BillBookingSearchForm)

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('billbookingmaster_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_transactions/billbookingmaster/_billbookingmaster_table.html')
        self.assertTrue('billbookingmasters' in response.context)
        # Check that only bills for company 1 and fin_year <= 2023 are present
        self.assertContains(response, self.bill1.pvev_no)
        self.assertContains(response, self.bill2.pvev_no)
        self.assertNotContains(response, self.bill3.pvev_no) # Different company

    def test_table_partial_view_search_supplier(self):
        response = self.client.get(
            reverse('billbookingmaster_table'),
            {'search_by': '1', 'supplier_search_value': 'Alpha Corp [101]'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.bill1.pvev_no)
        self.assertContains(response, self.bill4.pvev_no)
        self.assertNotContains(response, self.bill2.pvev_no)
        self.assertNotContains(response, self.bill3.pvev_no)

    def test_table_partial_view_search_pvevno(self):
        response = self.client.get(
            reverse('billbookingmaster_table'),
            {'search_by': '2', 'field_search_value': 'PVEV001'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.bill1.pvev_no)
        self.assertNotContains(response, self.bill2.pvev_no)

    def test_create_view_get(self):
        response = self.client.get(reverse('billbookingmaster_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_transactions/billbookingmaster/_billbookingmaster_form.html')
        self.assertContains(response, 'Add Bill Booking')
        self.assertIsInstance(response.context['form'], views.BillBookingMasterForm)

    def test_create_view_post_success(self):
        initial_count = BillBookingMaster.objects.count()
        data = {
            'fin_year': self.fin_year_2023.fin_year_id,
            'pvev_no': 'NEWPVEV',
            'sys_date': timezone.now().date(),
            'bill_no': 'NEWBILL',
            'bill_date': timezone.now().date(),
            'supplier': self.supplier_beta.supplier_id,
        }
        response = self.client.post(reverse('billbookingmaster_add'), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HTMX No Content
        self.assertEqual(BillBookingMaster.objects.count(), initial_count + 1)
        self.assertTrue(BillBookingMaster.objects.filter(pvev_no='NEWPVEV', company_id=1).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshBillBookingList', response.headers['HX-Trigger'])

    def test_create_view_post_invalid(self):
        initial_count = BillBookingMaster.objects.count()
        data = {
            'fin_year': self.fin_year_2023.fin_year_id,
            'pvev_no': '', # Invalid
            'sys_date': timezone.now().date(),
            'bill_no': 'NEWBILL',
            'bill_date': timezone.now().date(),
            'supplier': self.supplier_beta.supplier_id,
        }
        response = self.client.post(reverse('billbookingmaster_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form rendered with errors
        self.assertContains(response, 'Add Bill Booking')
        self.assertContains(response, 'This field is required.')
        self.assertEqual(BillBookingMaster.objects.count(), initial_count)

    def test_update_view_get(self):
        response = self.client.get(reverse('billbookingmaster_edit', args=[self.bill1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_transactions/billbookingmaster/_billbookingmaster_form.html')
        self.assertContains(response, 'Edit Bill Booking')
        self.assertContains(response, self.bill1.pvev_no)

    def test_update_view_post_success(self):
        updated_pvev_no = 'UPDATED_PVEV'
        data = {
            'fin_year': self.bill1.fin_year.fin_year_id,
            'pvev_no': updated_pvev_no,
            'sys_date': self.bill1.sys_date.date(),
            'bill_no': self.bill1.bill_no,
            'bill_date': self.bill1.bill_date.date(),
            'supplier': self.bill1.supplier.supplier_id,
        }
        response = self.client.post(reverse('billbookingmaster_edit', args=[self.bill1.pk]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.bill1.refresh_from_db()
        self.assertEqual(self.bill1.pvev_no, updated_pvev_no)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshBillBookingList', response.headers['HX-Trigger'])

    def test_delete_view_get(self):
        response = self.client.get(reverse('billbookingmaster_delete', args=[self.bill1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_transactions/billbookingmaster/_billbookingmaster_confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, self.bill1.pvev_no)

    def test_delete_view_post_success(self):
        initial_count = BillBookingMaster.objects.count()
        response = self.client.delete(reverse('billbookingmaster_delete', args=[self.bill1.pk]), HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertEqual(BillBookingMaster.objects.count(), initial_count - 1)
        self.assertFalse(BillBookingMaster.objects.filter(pk=self.bill1.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshBillBookingList', response.headers['HX-Trigger'])

    def test_supplier_autocomplete_view(self):
        response = self.client.get(reverse('supplier_autocomplete'), {'q': 'alpha'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = response.json()
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]['text'], f'{self.supplier_alpha.supplier_name} [{self.supplier_alpha.supplier_id}]')
        self.assertEqual(data[0]['id'], self.supplier_alpha.supplier_id)

```

### Step 5: HTMX and Alpine.js Integration

- **HTMX:**
    - The main `list.html` page uses `hx-get` on `billbookingmasterTable-container` to load `_billbookingmaster_table.html` initially and on `refreshBillBookingList` event.
    - The search form uses `hx-get` to re-fetch and swap the table partial on submit or keyup changes in search fields.
    - Add/Edit/Delete buttons use `hx-get` to fetch the respective partial forms into the modal, and `hx-post`/`hx-delete` for submission.
    - `HX-Trigger` headers (`refreshBillBookingList`, `hideModal`) are sent from `form_valid`/`delete` methods in views to update the frontend.
- **Alpine.js:**
    - Used in `list.html` (`x-data="{ searchType: '...' }"`) to control the visibility of `txtSupplier` and `Txtfield` based on the `search_by` dropdown selection (`x-show="searchType === '1'"` etc.).
    - Manages modal visibility (`x-data` on modal, `on click add/remove .hidden`, `send hideModal to window`).
    - The `supplierSearchInput` Alpine variable is used to control autocomplete visibility.
- **DataTables:**
    - Initialized in the `extra_js` block of `list.html`.
    - `htmx:afterSwap` listener ensures DataTables is re-initialized whenever the `billbookingmasterTable-container` is updated by HTMX, maintaining client-side search, sort, and pagination.
- **Autocomplete:** The `supplier_autocomplete` view returns JSON that HTMX populates into `supplier-autocomplete-results`. Alpine.js handles the `x-show` of this container. A simple JavaScript listener handles clicking on the autocomplete results to populate the input.

### Final Notes

- **Placeholders:** `[APP_NAME]` is `accounts_transactions`. `[MODEL_NAME]` is `BillBookingMaster`, `[MODEL_NAME_LOWER]` is `billbookingmaster`, `[MODEL_NAME_PLURAL_LOWER]` is `billbookingmasters`.
- **DRY Templates:** The use of `_billbookingmaster_table.html`, `_billbookingmaster_form.html`, and `_billbookingmaster_confirm_delete.html` promotes reusability and keeps the main `list.html` clean.
- **Fat Model, Thin View:** Business logic, especially the `search_bills` method, is encapsulated within the `BillBookingMasterManager` and `BillBookingMaster` model, keeping views concise and focused on request handling and template rendering.
- **Tests:** Comprehensive tests cover model behavior, view responses, form submissions, and HTMX interactions, ensuring reliability and maintainability.
- **Styling:** Tailwind CSS classes are applied directly within the HTML templates and form widgets for modern, responsive UI.