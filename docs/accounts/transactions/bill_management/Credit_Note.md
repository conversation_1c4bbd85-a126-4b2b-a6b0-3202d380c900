## ASP.NET to Django Conversion Script: Credit Note Module

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**

Based on the ASP.NET code, we identify the following tables and their relevant columns:

1.  **`tblACC_CreditNote` (Main Transaction Table)**
    *   `Id` (Primary Key, integer)
    *   `Date` (Date, format `dd-MM-yyyy`)
    *   `CreditNo` (String, e.g., "0001", sequential)
    *   `Types` (Integer, Foreign Key to `tblACC_DebitType.Id`)
    *   `SCE` (String, stores an ID/Code for Employee, Customer, or Supplier)
    *   `Refrence` (String, multi-line)
    *   `Particulars` (String, multi-line)
    *   `Amount` (Decimal, up to 15 digits before decimal, 3 after)
    *   `SysDate` (System Date, auto-generated)
    *   `SysTime` (System Time, auto-generated)
    *   `CompId` (Company ID, integer, from session)
    *   `SessionId` (User Session ID/Username, string, from session)
    *   `FinYearId` (Financial Year ID, integer, from session)

2.  **`tblACC_DebitType` (Lookup Table for Credit Types)**
    *   `Id` (Primary Key, integer)
    *   `Description` (String, e.g., "Employee Credit Note", "Customer Credit Note")

3.  **External Lookup Tables (Inferred for Autocomplete Functionality)**
    *   `tblHR_OfficeStaff` (for `SCE` when `Types` refers to Employee)
        *   `EmpId` (Employee ID/Code)
        *   `EmployeeName` (Employee Name)
    *   `SD_Cust_master` (for `SCE` when `Types` refers to Customer)
        *   `CustomerId` (Customer ID/Code)
        *   `CustomerName` (Customer Name)
    *   `tblMM_Supplier_master` (for `SCE` when `Types` refers to Supplier)
        *   `SupplierId` (Supplier ID/Code)
        *   `SupplierName` (Supplier Name)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic in the ASP.NET code.

**Instructions:**

*   **Read (List View):**
    *   The `fillgrid()` method populates the `GridView1` by selecting all records from `tblACC_CreditNote` filtered by `CompId` and `FinYearId`.
    *   It performs lookups for `Types` (using `tblACC_DebitType.Description`) and `SCE` (using `fun.EmpCustSupplierNames` which dynamically fetches Employee, Customer, or Supplier names based on the `Types` field).
    *   Paging is supported by `GridView1_PageIndexChanging`.
*   **Create (Add New Record):**
    *   Handled by `GridView1_RowCommand` with `CommandName="Add"` (footer row) and `CommandName="Add1"` (empty data template).
    *   Generates a sequential `CreditNo` (e.g., "0001") unique per `CompId` and `FinYearId`.
    *   Inserts `SysDate`, `SysTime`, `CompId`, `SessionId`, `FinYearId`, `Date`, `CreditNo`, `SCE`, `Amount`, `Refrence`, `Particulars`, `Types`.
    *   Includes validation for `Date` format, `Amount` as numeric, `Types` selected, and `SCE` (Credit To) existence via `fun.chkEmpCustSupplierCode`.
*   **Update (Edit Existing Record):**
    *   Handled by `GridView1_RowUpdating`.
    *   Updates `Date`, `SCE`, `Amount`, `Refrence`, `Particulars`, `Types` for a given `Id` and `CompId`.
    *   Similar validation as creation.
*   **Delete (Remove Record):**
    *   Handled by `GridView1_RowDeleting`.
    *   Deletes a record based on `Id` and `CompId`.
*   **Auxiliary Functions:**
    *   `fun.getCode()`: Extracts the ID/Code from a display string (e.g., "Name [Code]").
    *   `fun.EmpCustSupplierNames()`: Retrieves the full name for a given entity type and code.
    *   `fun.chkEmpCustSupplierCode()`: Validates if an entity code exists for a given type.
    *   `fun.DateValidation()`, `fun.NumberValidationQty()`: Input validation.
    *   **Dynamic Autocomplete:** `sql` and `sql2` web methods provide autocomplete suggestions for the `SCE` field based on the selected `Types` (Employee, Customer, Supplier) by querying the respective external tables.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

The primary UI component is the `asp:GridView` which acts as a comprehensive data table supporting list display, pagination, inline editing, inline adding (via footer row), and deletion.

*   **Data Display:** `GridView` rows display `SN`, `Date`, `CreditNo`, `Types`, `SCE` (Credit To), `Reference`, `Particulars`, `Amount`.
*   **Input Controls (for Add/Edit):**
    *   `asp:TextBox` for `Date`, `Credit To`, `Reference`, `Particulars`, `Amount`.
    *   `asp:DropDownList` for `Types`.
*   **Interactive Components:**
    *   `cc1:CalendarExtender`: Provides a date picker for `Date` fields.
    *   `cc1:AutoCompleteExtender`: Provides autocomplete suggestions for the `Credit To` field, dynamically populated based on the selected `Types`.
    *   `asp:LinkButton`/`asp:Button`: Trigger Edit, Update, Cancel, Delete, and Add actions.
*   **Validation Controls:** `asp:RequiredFieldValidator` and `asp:RegularExpressionValidator` enforce input constraints (e.g., date format, numeric amount, required fields).
*   **Client-side JS:** `OnClientClick` attributes call JavaScript functions like `confirmationAdd()` and `confirmationDelete()`, likely for confirmation pop-ups.

### Step 4: Generate Django Code

We will create a Django app named `credit_notes` to house this functionality.

#### 4.1 Models

**Task:** Create Django models based on the database schema.

**Instructions:**

*   Define `CreditType` and `CreditNote` models.
*   Use `managed = False` and `db_table` to map to existing database tables.
*   Implement business logic (`CreditNo` generation, `SCE` entity lookup/validation) within the `CreditNote` model or its custom manager following the "fat model" principle.
*   Mock external models (`MockEmployee`, `MockCustomer`, `MockSupplier`) are included to demonstrate the `SCE` lookup logic, assuming these tables are also managed by Django but not directly part of this `credit_notes` app's migration.

```python
# credit_notes/models.py
from django.db import models
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
import re

# Mock external models/data sources if not managed by Django within this app.
# In a real scenario, these would likely be in separate Django apps or accessed via direct database queries.
class MockEmployee(models.Model):
    employee_id = models.CharField(max_length=10, db_column='EmpId', primary_key=True)
    employee_name = models.CharField(max_length=255, db_column='EmployeeName')

    class Meta:
        managed = False  # Important: tells Django not to manage this table's schema
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.employee_name} [{self.employee_id}]"

class MockCustomer(models.Model):
    customer_id = models.CharField(max_length=10, db_column='CustomerId', primary_key=True)
    customer_name = models.CharField(max_length=255, db_column='CustomerName')

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customer_name} [{self.customer_id}]"

class MockSupplier(models.Model):
    supplier_id = models.CharField(max_length=10, db_column='SupplierId', primary_key=True)
    supplier_name = models.CharField(max_length=255, db_column='SupplierName')

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return f"{self.supplier_name} [{self.supplier_id}]"

# The core models for the Credit Note functionality
class CreditType(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)  # Assuming Id is the PK in DB
    description = models.CharField(db_column='Description', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblACC_DebitType'
        verbose_name = 'Credit Type'
        verbose_name_plural = 'Credit Types'

    def __str__(self):
        return self.description
    
    def get_entity_type(self):
        """
        Maps the CreditType ID to the corresponding entity type (Employee, Customer, Supplier).
        This mapping is inferred from the original C# code's switch statement in sql/sql2 methods.
        In a real application, CreditType might have an explicit field like 'entity_category'.
        """
        if self.id == 1:
            return 'employee'
        elif self.id == 2:
            return 'customer'
        elif self.id == 3:
            return 'supplier'
        return None

class CreditNoteManager(models.Manager):
    def get_next_credit_no(self, company_id, financial_year_id):
        """
        Generates the next sequential CreditNo based on company and financial year.
        Mimics the ASP.NET logic (e.g., "0001", "0002").
        """
        # The ASP.NET code uses FinYearId<=, which is unusual for sequential number generation.
        # Assuming it meant for the current financial year. If not, this needs adjustment.
        last_credit_note = self.filter(
            company_id=company_id, 
            financial_year_id=financial_year_id # Assuming current financial year
        ).order_by('-credit_no').first()
        
        if last_credit_note and last_credit_note.credit_no.isdigit():
            next_no = int(last_credit_note.credit_no) + 1
        else:
            next_no = 1
        return str(next_no).zfill(4) # Pad with leading zeros to 4 digits

class CreditNote(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True) # Use AutoField for Django's default PK behavior
    system_date_time = models.DateTimeField(db_column='SysDate', default=timezone.now) # Combines SysDate and SysTime
    company_id = models.IntegerField(db_column='CompId')
    session_user = models.CharField(db_column='SessionId', max_length=255) # Stores the username/session ID
    financial_year_id = models.IntegerField(db_column='FinYearId')
    entry_date = models.DateField(db_column='Date')
    credit_no = models.CharField(db_column='CreditNo', max_length=10, unique=True, blank=True) # Max length from "0001" and potential future growth. Blank=True for auto-generation.
    credit_to_code = models.CharField(db_column='SCE', max_length=255) # Stores the entity's ID/code
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=2) # Max digits 15, 3 decimal places -> 18,2 (safe bet)
    reference = models.TextField(db_column='Refrence', blank=True)
    particulars = models.TextField(db_column='Particulars', blank=True)
    credit_type = models.ForeignKey(CreditType, on_delete=models.PROTECT, db_column='Types')

    objects = CreditNoteManager() # Use our custom manager

    class Meta:
        managed = False
        db_table = 'tblACC_CreditNote'
        verbose_name = 'Credit Note'
        verbose_name_plural = 'Credit Notes'
        # Add unique_together constraint if CreditNo + CompanyId + FinancialYearId must be unique.
        # unique_together = (('credit_no', 'company_id', 'financial_year_id'),)


    def __str__(self):
        return f"Credit Note {self.credit_no} on {self.entry_date}"

    def get_credit_to_display_name(self):
        """
        Retrieves the display name for the 'Credit To' entity (Employee, Customer, or Supplier).
        Mimics fun.EmpCustSupplierNames.
        """
        entity_type_category = self.credit_type.get_entity_type()
        entity_code = self.credit_to_code

        try:
            if entity_type_category == 'employee':
                employee = MockEmployee.objects.get(employee_id=entity_code)
                return employee.employee_name
            elif entity_type_category == 'customer':
                customer = MockCustomer.objects.get(customer_id=entity_code)
                return customer.customer_name
            elif entity_type_category == 'supplier':
                supplier = MockSupplier.objects.get(supplier_id=entity_code)
                return supplier.supplier_name
        except (MockEmployee.DoesNotExist, MockCustomer.DoesNotExist, MockSupplier.DoesNotExist):
            return f"Invalid/Unknown Entity [{entity_code}]"
        
        return self.credit_to_code # Fallback if entity type is not handled

    def clean(self):
        super().clean()
        # Validate credit_to_code based on selected credit_type
        entity_type_category = self.credit_type.get_entity_type()
        entity_code = self.credit_to_code

        is_valid_entity = False
        if entity_type_category == 'employee':
            is_valid_entity = MockEmployee.objects.filter(employee_id=entity_code).exists()
        elif entity_type_category == 'customer':
            is_valid_entity = MockCustomer.objects.filter(customer_id=entity_code).exists()
        elif entity_type_category == 'supplier':
            is_valid_entity = MockSupplier.objects.filter(supplier_id=entity_code).exists()
        else:
            # This case should ideally not happen if CreditType has a valid entity_type_category
            raise ValidationError(
                {'credit_type': _('Selected Credit Type does not map to a known entity category.')}
            )

        if not is_valid_entity:
            raise ValidationError(
                {'credit_to_code': _(f'No matching {entity_type_category} found for code: "{entity_code}". Please ensure the code is correct.')}
            )

    def save(self, *args, **kwargs):
        # Auto-generate credit_no only if it's a new record and not already set
        if not self.pk and not self.credit_no:
            # Assuming company_id and financial_year_id are set by the view/caller before save
            self.credit_no = CreditNote.objects.get_next_credit_no(self.company_id, self.financial_year_id)
        
        # Ensure system_date_time is set on creation
        if not self.system_date_time:
            self.system_date_time = timezone.now()

        super().save(*args, **kwargs)

```

#### 4.2 Forms

**Task:** Define Django forms for user input.

**Instructions:**

*   Create `CreditNoteForm` using `ModelForm`.
*   Add widgets for Tailwind CSS styling.
*   Implement custom validation for date format, amount format, and `credit_to_code` (which requires real-time lookup similar to ASP.NET's `AutoCompleteExtender`). This real-time validation will be augmented by HTMX on the frontend for user experience.

```python
# credit_notes/forms.py
from django import forms
from .models import CreditNote, CreditType
import re

class CreditNoteForm(forms.ModelForm):
    # This field will capture the display name (e.g., "John Doe [E123]")
    # The actual credit_to_code will be parsed from this.
    credit_to_display_name = forms.CharField(
        max_length=255, 
        required=True,
        label="Credit To",
        help_text="Start typing employee, customer, or supplier name. Select from suggestions. Example: 'John Doe [E123]'",
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter name or code',
            'hx-get': '/creditnote/autocomplete-credit-to/', # HTMX endpoint for autocomplete suggestions
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#credit-to-suggestions',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off', # Disable browser autocomplete
            'x-on:focus': '$dispatch("open-suggestions")', # Alpine.js event
            'x-on:click': '$dispatch("open-suggestions")',
            'x-on:blur.away': '$dispatch("close-suggestions")',
            'x-model': 'creditToDisplayName' # Alpine.js binding
        })
    )

    class Meta:
        model = CreditNote
        fields = ['entry_date', 'credit_type', 'credit_to_display_name', 'reference', 'particulars', 'amount']
        widgets = {
            'entry_date': forms.DateInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'type': 'date' # HTML5 date picker
            }),
            'credit_type': forms.Select(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'hx-post': '/creditnote/update-autocomplete-session/', # HTMX endpoint to update session for autocomplete
                'hx-trigger': 'change',
                'hx-swap': 'none'
            }),
            'reference': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-20',
                'rows': 3
            }),
            'particulars': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-20',
                'rows': 3
            }),
            'amount': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
        }
        labels = {
            'entry_date': 'Date',
            'credit_type': 'Type',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate credit_to_display_name for existing instances
        if self.instance.pk:
            self.fields['credit_to_display_name'].initial = self.instance.get_credit_to_display_name()
        
        # Populate CreditType choices
        self.fields['credit_type'].queryset = CreditType.objects.all().order_by('description')

    def clean_entry_date(self):
        entry_date = self.cleaned_data['entry_date']
        # ASP.NET had a specific date regex. Django's DateField handles basic validation.
        # Additional custom validation if needed (e.g., date not in future).
        return entry_date

    def clean_amount(self):
        amount = self.cleaned_data['amount']
        # ASP.NET had regex for 15 digits before, 3 after decimal.
        # DecimalField handles this. Ensure it's positive.
        if amount <= 0:
            raise forms.ValidationError(_("Amount must be a positive value."))
        return amount

    def clean(self):
        cleaned_data = super().clean()
        credit_to_display_name = cleaned_data.get('credit_to_display_name')
        
        if credit_to_display_name:
            match = re.search(r'\[(.*?)\]$', credit_to_display_name)
            if match:
                # Extract the code from "Name [Code]" format
                cleaned_data['credit_to_code'] = match.group(1)
            else:
                # If no code in brackets, treat the whole string as the code (or raise error)
                # For safety, let's assume valid entry must have [code] as per ASP.NET
                raise forms.ValidationError(
                    {'credit_to_display_name': _("Invalid format. Please select from suggestions or enter in 'Name [Code]' format.")}
                )
        else:
             raise forms.ValidationError(
                    {'credit_to_display_name': _("This field is required.")}
                )

        # The model's clean method will perform the actual existence check
        # using cleaned_data['credit_to_code'] and cleaned_data['credit_type']
        return cleaned_data

```

#### 4.3 Views

**Task:** Implement CRUD operations using CBVs and a helper view for HTMX autocomplete.

**Instructions:**

*   Define `CreditNoteListView`, `CreditNoteCreateView`, `CreditNoteUpdateView`, `CreditNoteDeleteView`.
*   Add a `CreditNoteTablePartialView` for HTMX-driven table refreshes.
*   Implement an `autocomplete_credit_to` function or CBV for the autocomplete suggestions.
*   Keep views thin (5-15 lines) by delegating logic to models or forms.
*   Use `messages.success` for user feedback.
*   Ensure `HX-Trigger` headers for HTMX-based list refreshes.
*   Assume `company_id` and `financial_year_id` are available from the request (e.g., `request.session['company_id']`, `request.session['financial_year_id']` or from user profile). For demonstration, these will be mocked or hardcoded.

```python
# credit_notes/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from django.db.models import Q
from django.shortcuts import get_object_or_404
import re

from .models import CreditNote, CreditType, MockEmployee, MockCustomer, MockSupplier
from .forms import CreditNoteForm

# Helper to get current company_id and financial_year_id
# In a real application, these would come from the logged-in user's session or profile.
def get_user_context(request):
    # Mocking session values for demonstration
    company_id = getattr(request.user, 'company_id', 1) 
    financial_year_id = getattr(request.user, 'financial_year_id', 2024) 
    return company_id, financial_year_id

class CreditNoteListView(ListView):
    model = CreditNote
    template_name = 'credit_notes/creditnote/list.html'
    context_object_name = 'creditnotes'

    def get_queryset(self):
        # Filter by company_id and financial_year_id as per ASP.NET logic
        company_id, financial_year_id = get_user_context(self.request)
        return CreditNote.objects.filter(
            company_id=company_id,
            financial_year_id__lte=financial_year_id # ASP.NET uses <=
        ).order_by('-id') # Order by Id Desc as in ASP.NET

class CreditNoteTablePartialView(CreditNoteListView):
    """
    Renders only the table rows, to be used with HTMX for dynamic updates.
    """
    template_name = 'credit_notes/creditnote/_creditnote_table.html'

class CreditNoteCreateView(CreateView):
    model = CreditNote
    form_class = CreditNoteForm
    template_name = 'credit_notes/creditnote/form.html'
    success_url = reverse_lazy('creditnote_list') # Redirection after successful form submission

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Store selected credit_type_id in session for autocomplete context if needed
        # self.request.session['autocomplete_credit_type_id'] = self.request.POST.get('credit_type')
        return context

    def form_valid(self, form):
        # Set additional fields from session/user context before saving
        company_id, financial_year_id = get_user_context(self.request)
        form.instance.company_id = company_id
        form.instance.financial_year_id = financial_year_id
        form.instance.session_user = self.request.user.username # Or request.user.id

        # The credit_to_code is parsed in form.clean()
        # The credit_no is generated in model.save()

        response = super().form_valid(form)
        messages.success(self.request, 'Credit Note added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content to indicate success to HTMX without a full page reload
                headers={
                    'HX-Trigger': 'refreshCreditNoteList' # Trigger refresh on the list view
                }
            )
        return response
    
    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # For HTMX, return the form with errors, swapping it back into the modal
            return HttpResponse(
                render_to_string(self.template_name, self.get_context_data(form=form), request=self.request),
                status=400 # Bad Request
            )
        return response

class CreditNoteUpdateView(UpdateView):
    model = CreditNote
    form_class = CreditNoteForm
    template_name = 'credit_notes/creditnote/form.html'
    success_url = reverse_lazy('creditnote_list')

    def get_queryset(self):
        # Ensure only records for the current company/financial year can be updated
        company_id, financial_year_id = get_user_context(self.request)
        return super().get_queryset().filter(company_id=company_id)

    def form_valid(self, form):
        # No need to set company_id, fin_year_id, session_user, credit_no on update
        # as they are already set on creation.
        response = super().form_valid(form)
        messages.success(self.request, 'Credit Note updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCreditNoteList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                render_to_string(self.template_name, self.get_context_data(form=form), request=self.request),
                status=400
            )
        return response

class CreditNoteDeleteView(DeleteView):
    model = CreditNote
    template_name = 'credit_notes/creditnote/confirm_delete.html'
    success_url = reverse_lazy('creditnote_list')

    def get_queryset(self):
        # Ensure only records for the current company/financial year can be deleted
        company_id, financial_year_id = get_user_context(self.request)
        return super().get_queryset().filter(company_id=company_id)

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Credit Note deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCreditNoteList'
                }
            )
        return response

class AutocompleteCreditToView(View):
    """
    Provides autocomplete suggestions for the 'Credit To' field based on the selected Credit Type.
    Mimics the ASP.NET sql/sql2 web methods.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        credit_type_id = request.session.get('autocomplete_credit_type_id') # Get from session
        suggestions = []

        if credit_type_id:
            try:
                credit_type = CreditType.objects.get(id=credit_type_id)
                entity_type_category = credit_type.get_entity_type()

                if entity_type_category == 'employee':
                    results = MockEmployee.objects.filter(
                        Q(employee_name__icontains=query) | Q(employee_id__icontains=query)
                    ).values('employee_id', 'employee_name')
                    suggestions = [f"{r['employee_name']} [{r['employee_id']}]" for r in results]
                elif entity_type_category == 'customer':
                    results = MockCustomer.objects.filter(
                        Q(customer_name__icontains=query) | Q(customer_id__icontains=query)
                    ).values('customer_id', 'customer_name')
                    suggestions = [f"{r['customer_name']} [{r['customer_id']}]" for r in results]
                elif entity_type_category == 'supplier':
                    results = MockSupplier.objects.filter(
                        Q(supplier_name__icontains=query) | Q(supplier_id__icontains=query)
                    ).values('supplier_id', 'supplier_name')
                    suggestions = [f"{r['supplier_name']} [{r['supplier_id']}]" for r in results]

            except CreditType.DoesNotExist:
                pass # No suggestions if credit type is invalid
        
        # Limit to 10 suggestions as in typical autocomplete scenarios
        return render_to_string('credit_notes/creditnote/_autocomplete_suggestions.html', {
            'suggestions': suggestions[:10]
        }, request=request)

class UpdateAutocompleteSessionView(View):
    """
    Updates the session with the currently selected Credit Type ID to be used by autocomplete.
    Triggered by HTMX 'change' on the credit_type dropdown.
    """
    def post(self, request, *args, **kwargs):
        credit_type_id = request.POST.get('credit_type')
        request.session['autocomplete_credit_type_id'] = credit_type_id
        return HttpResponse(status=204) # No Content, just update session

```

#### 4.4 Templates

**Task:** Create templates for each view.

**Instructions:**

*   `credit_notes/creditnote/list.html`: Main page, extends `core/base.html`. Contains the container for the HTMX-loaded table and the modal.
*   `credit_notes/creditnote/_creditnote_table.html`: Partial template for the DataTables table, loaded via HTMX.
*   `credit_notes/creditnote/form.html`: Partial template for Create/Update forms, loaded into the modal.
*   `credit_notes/creditnote/confirm_delete.html`: Partial template for delete confirmation, loaded into the modal.
*   `credit_notes/creditnote/_autocomplete_suggestions.html`: Partial template for autocomplete suggestions.

```html
<!-- credit_notes/creditnote/list.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Credit Notes</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'creditnote_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Credit Note
        </button>
    </div>
    
    <div id="creditnoteTable-container"
         hx-trigger="load, refreshCreditNoteList from:body"
         hx-get="{% url 'creditnote_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading Credit Notes...</p>
        </div>
    </div>
    
    <!-- Modal for forms/confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me
            on htmx:afterSwap from #modalContent if event.detail.xhr.status == 204 or event.detail.xhr.status == 400
                if event.detail.xhr.status == 204 remove .is-active from me"
         x-data="{ show: false }"
         x-init="() => { 
             window.addEventListener('open-modal', () => show = true);
             window.addEventListener('close-modal', () => show = false);
         }"
         x-show="show"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0 scale-95"
         x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100 scale-100"
         x-transition:leave-end="opacity-0 scale-95">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"
             @click.away="show = false"> <!-- Close modal when clicking outside -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/datatables.net@2.0.7/js/dataTables.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/datatables.net-dt@2.0.7/js/dataTables.dataTables.min.js"></script>

<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('creditNoteForm', () => ({
            creditToDisplayName: '',
            autocompleteSuggestionsVisible: false,
            init() {
                this.$watch('creditToDisplayName', (value) => {
                    if (value.length > 0) {
                        this.autocompleteSuggestionsVisible = true;
                    } else {
                        this.autocompleteSuggestionsVisible = false;
                    }
                });
                // Handle HTMX autocomplete target swap
                document.body.addEventListener('htmx:afterSwap', (event) => {
                    if (event.detail.target.id === 'credit-to-suggestions') {
                        this.autocompleteSuggestionsVisible = event.detail.xhr.responseText.trim().length > 0;
                    }
                });
            },
            selectSuggestion(suggestion) {
                this.creditToDisplayName = suggestion;
                this.autocompleteSuggestionsVisible = false;
            },
            closeSuggestions() {
                setTimeout(() => { // Small delay to allow click on suggestion
                    this.autocompleteSuggestionsVisible = false;
                }, 100);
            },
            openSuggestions() {
                 this.autocompleteSuggestionsVisible = true;
            }
        }));
    });

    document.addEventListener('htmx:afterSwap', function (event) {
        if (event.detail.target.id === 'creditnoteTable-container') {
            $('#creditnoteTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true, // Destroy existing instance if any
            });
        }

        // Alpine.js modal control
        const modal = document.getElementById('modal');
        if (event.detail.target.id === 'modalContent' && event.detail.xhr.status === 200) {
            modal._x_dataStack[0].show = true;
        } else if (event.detail.xhr.status === 204) { // HTMX success response for form submissions
            modal._x_dataStack[0].show = false;
            // Additional check for messages to ensure they are shown
            if (event.detail.xhr.getResponseHeader('HX-Trigger')) {
                // messages are handled by Django's messages framework and rendered in base.html
            }
        }
    });

    // Custom events for Alpine.js modal
    document.addEventListener('open-modal', () => {
        const modal = document.getElementById('modal');
        if (modal && modal._x_dataStack && modal._x_dataStack.length > 0) {
            modal._x_dataStack[0].show = true;
        }
    });
    document.addEventListener('close-modal', () => {
        const modal = document.getElementById('modal');
        if (modal && modal._x_dataStack && modal._x_dataStack.length > 0) {
            modal._x_dataStack[0].show = false;
        }
    });
</script>
{% endblock %}
```

```html
<!-- credit_notes/creditnote/_creditnote_table.html -->
<table id="creditnoteTable" class="min-w-full bg-white border border-gray-200 shadow-sm rounded-lg overflow-hidden">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Credit No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Credit To</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reference</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Particulars</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in creditnotes %}
        <tr class="hover:bg-gray-50">
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.entry_date|date:"d-m-Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.credit_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.credit_type.description }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.get_credit_to_display_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.reference|default_if_none:"" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.particulars|default_if_none:"" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.amount|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded text-xs mr-2"
                    hx-get="{% url 'creditnote_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click trigger open-modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs"
                    hx-get="{% url 'creditnote_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click trigger open-modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="9" class="py-4 text-center text-gray-500">No credit notes found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
```

```html
<!-- credit_notes/creditnote/form.html -->
<div class="p-6" x-data="creditNoteForm">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Credit Note</h3>
    <form hx-post="{{ request.path }}" hx-swap="outerHTML" hx-target="#modalContent">
        {% csrf_token %}
        
        <div class="space-y-4">
            <div>
                <label for="{{ form.entry_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.entry_date.label }}
                </label>
                {{ form.entry_date }}
                {% if form.entry_date.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.entry_date.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.credit_type.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.credit_type.label }}
                </label>
                {{ form.credit_type }}
                {% if form.credit_type.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.credit_type.errors }}</p>
                {% endif %}
            </div>

            <div class="relative">
                <label for="{{ form.credit_to_display_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.credit_to_display_name.label }}
                </label>
                {{ form.credit_to_display_name }}
                {% if form.credit_to_display_name.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.credit_to_display_name.errors }}</p>
                {% endif %}
                <div id="credit-to-suggestions" 
                     class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1"
                     x-show="autocompleteSuggestionsVisible"
                     x-transition:enter="ease-out duration-300"
                     x-transition:enter-start="opacity-0 scale-95"
                     x-transition:enter-end="opacity-100 scale-100"
                     x-transition:leave="ease-in duration-200"
                     x-transition:leave-start="opacity-100 scale-100"
                     x-transition:leave-end="opacity-0 scale-95"
                     @click.away="closeSuggestions()">
                    <!-- Suggestions will be loaded here via HTMX -->
                </div>
                <p class="mt-1 text-sm text-gray-500">{{ form.credit_to_display_name.help_text }}</p>
            </div>

            <div>
                <label for="{{ form.reference.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.reference.label }}
                </label>
                {{ form.reference }}
                {% if form.reference.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.reference.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.particulars.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.particulars.label }}
                </label>
                {{ form.particulars }}
                {% if form.particulars.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.particulars.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.amount.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.amount.label }}
                </label>
                {{ form.amount }}
                {% if form.amount.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.amount.errors }}</p>
                {% endif %}
            </div>
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click trigger close-modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
        <!-- Display non-field errors if any -->
        {% if form.non_field_errors %}
            <div class="text-red-500 text-xs mt-4">
                {% for error in form.non_field_errors %}
                    <p>{{ error }}</p>
                {% endfor %}
            </div>
        {% endif %}
    </form>
</div>
```

```html
<!-- credit_notes/creditnote/confirm_delete.html -->
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="mb-4">Are you sure you want to delete Credit Note <strong>{{ creditnote.credit_no }}</strong> dated <strong>{{ creditnote.entry_date|date:"d-m-Y" }}</strong>?</p>
    <form hx-post="{% url 'creditnote_delete' creditnote.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click trigger close-modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

```html
<!-- credit_notes/creditnote/_autocomplete_suggestions.html -->
{% if suggestions %}
    <ul class="py-1">
        {% for suggestion in suggestions %}
            <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer" @click="selectSuggestion('{{ suggestion|escapejs }}')">
                {{ suggestion }}
            </li>
        {% endfor %}
    </ul>
{% else %}
    <div class="px-4 py-2 text-gray-500">No suggestions.</div>
{% endif %}
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**

*   Create paths for list, create, update, delete, table partial view, and autocomplete endpoints.
*   Include `app_name` for proper URL namespacing.

```python
# credit_notes/urls.py
from django.urls import path
from .views import (
    CreditNoteListView, CreditNoteCreateView, CreditNoteUpdateView, CreditNoteDeleteView,
    CreditNoteTablePartialView, AutocompleteCreditToView, UpdateAutocompleteSessionView
)

app_name = 'credit_notes'

urlpatterns = [
    path('creditnote/', CreditNoteListView.as_view(), name='creditnote_list'),
    path('creditnote/table/', CreditNoteTablePartialView.as_view(), name='creditnote_table'),
    path('creditnote/add/', CreditNoteCreateView.as_view(), name='creditnote_add'),
    path('creditnote/edit/<int:pk>/', CreditNoteUpdateView.as_view(), name='creditnote_edit'),
    path('creditnote/delete/<int:pk>/', CreditNoteDeleteView.as_view(), name='creditnote_delete'),
    path('creditnote/autocomplete-credit-to/', AutocompleteCreditToView.as_view(), name='autocomplete_credit_to'),
    path('creditnote/update-autocomplete-session/', UpdateAutocompleteSessionView.as_view(), name='update_autocomplete_session'),
]

```

#### 4.6 Tests

**Task:** Write tests for the models and views.

**Instructions:**

*   Include comprehensive unit tests for model methods and properties.
*   Add integration tests for all views (list, create, update, delete) covering HTMX interactions.
*   Ensure at least 80% test coverage of code.
*   Mock `request.user`, `request.session` as needed for context.

```python
# credit_notes/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from django.utils import timezone
from unittest.mock import patch

from .models import CreditNote, CreditType, MockEmployee, MockCustomer, MockSupplier

# Mock get_user_context function
def mock_get_user_context(request):
    return 1, 2024 # company_id=1, financial_year_id=2024

@patch('credit_notes.views.get_user_context', side_effect=mock_get_user_context)
class CreditNoteModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create mock external entities
        MockEmployee.objects.create(employee_id='E001', employee_name='John Doe')
        MockCustomer.objects.create(customer_id='C001', customer_name='Acme Corp')
        MockSupplier.objects.create(supplier_id='S001', supplier_name='Global Supplies')

        # Create CreditTypes
        CreditType.objects.create(id=1, description='Employee Credit')
        CreditType.objects.create(id=2, description='Customer Credit')
        CreditType.objects.create(id=3, description='Supplier Credit')
        CreditType.objects.create(id=99, description='Other Type')

        # Create test data for all tests
        cls.credit_type_employee = CreditType.objects.get(id=1)
        cls.credit_type_customer = CreditType.objects.get(id=2)
        cls.credit_type_supplier = CreditType.objects.get(id=3)
        cls.credit_type_other = CreditType.objects.get(id=99)
        
        CreditNote.objects.create(
            company_id=1,
            financial_year_id=2024,
            session_user='testuser',
            entry_date='2024-01-01',
            credit_type=cls.credit_type_employee,
            credit_to_code='E001',
            amount=100.50,
            reference='Test ref 1',
            particulars='Test particulars 1',
            credit_no='0001' # Manually setting for first object, auto-gen for others
        )
        CreditNote.objects.create(
            company_id=1,
            financial_year_id=2024,
            session_user='testuser',
            entry_date='2024-01-02',
            credit_type=cls.credit_type_customer,
            credit_to_code='C001',
            amount=250.75,
            reference='Test ref 2',
            particulars='Test particulars 2',
        ) # This one's credit_no will be auto-generated

    def test_credit_note_creation(self, mock_get_user_context):
        credit_note = CreditNote.objects.get(id=1)
        self.assertEqual(credit_note.credit_no, '0001')
        self.assertEqual(credit_note.entry_date.strftime('%Y-%m-%d'), '2024-01-01')
        self.assertEqual(credit_note.amount, 100.50)
        self.assertEqual(credit_note.credit_type, self.credit_type_employee)
        self.assertEqual(credit_note.credit_to_code, 'E001')

    def test_get_credit_to_display_name(self, mock_get_user_context):
        credit_note_emp = CreditNote.objects.get(credit_to_code='E001')
        self.assertEqual(credit_note_emp.get_credit_to_display_name(), 'John Doe')

        credit_note_cust = CreditNote.objects.get(credit_to_code='C001')
        self.assertEqual(credit_note_cust.get_credit_to_display_name(), 'Acme Corp')

        # Test with invalid code
        credit_note_invalid = CreditNote.objects.create(
            company_id=1,
            financial_year_id=2024,
            session_user='testuser',
            entry_date='2024-01-03',
            credit_type=self.credit_type_employee,
            credit_to_code='E999', # Non-existent
            amount=50.00
        )
        self.assertIn("Invalid/Unknown Entity [E999]", credit_note_invalid.get_credit_to_display_name())

    def test_credit_no_auto_generation(self, mock_get_user_context):
        # The second object was created without a credit_no, so it should be auto-generated
        credit_note = CreditNote.objects.get(credit_to_code='C001')
        self.assertEqual(credit_note.credit_no, '0002') # Should be next sequential

        # Create another one for the same company/financial year
        new_credit_note = CreditNote.objects.create(
            company_id=1,
            financial_year_id=2024,
            session_user='testuser',
            entry_date='2024-01-03',
            credit_type=self.credit_type_supplier,
            credit_to_code='S001',
            amount=300.00
        )
        self.assertEqual(new_credit_note.credit_no, '0003')
    
    def test_credit_note_clean_method_valid(self, mock_get_user_context):
        credit_note = CreditNote(
            company_id=1,
            financial_year_id=2024,
            session_user='testuser',
            entry_date='2024-01-04',
            credit_type=self.credit_type_employee,
            credit_to_code='E001',
            amount=400.00
        )
        try:
            credit_note.full_clean()
        except ValidationError:
            self.fail("full_clean() raised ValidationError unexpectedly for valid data.")

    def test_credit_note_clean_method_invalid_credit_to_code(self, mock_get_user_context):
        credit_note = CreditNote(
            company_id=1,
            financial_year_id=2024,
            session_user='testuser',
            entry_date='2024-01-04',
            credit_type=self.credit_type_employee,
            credit_to_code='E999', # Invalid code
            amount=400.00
        )
        with self.assertRaisesMessage(ValidationError, 'No matching employee found for code: "E999".'):
            credit_note.full_clean()

@patch('credit_notes.views.get_user_context', side_effect=mock_get_user_context)
class CreditNoteViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a test user
        cls.user = User.objects.create_user(username='testuser', password='password123')
        cls.user.company_id = 1
        cls.user.financial_year_id = 2024

        # Create mock external entities
        MockEmployee.objects.create(employee_id='E001', employee_name='John Doe')
        MockCustomer.objects.create(customer_id='C001', customer_name='Acme Corp')
        MockSupplier.objects.create(supplier_id='S001', supplier_name='Global Supplies')

        # Create CreditTypes
        CreditType.objects.create(id=1, description='Employee Credit')
        CreditType.objects.create(id=2, description='Customer Credit')
        CreditType.objects.create(id=3, description='Supplier Credit')
        
        # Create initial CreditNote for testing updates/deletions
        cls.credit_type = CreditType.objects.get(id=1)
        cls.credit_note = CreditNote.objects.create(
            company_id=1,
            financial_year_id=2024,
            session_user='testuser',
            entry_date='2024-01-10',
            credit_type=cls.credit_type,
            credit_to_code='E001',
            amount=500.00,
            credit_no='0001'
        )
    
    def setUp(self):
        self.client = Client()
        self.client.login(username='testuser', password='password123')
    
    def test_list_view(self, mock_get_user_context):
        response = self.client.get(reverse('credit_notes:creditnote_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'credit_notes/creditnote/list.html')
        self.assertTrue('creditnotes' in response.context)
        self.assertContains(response, 'Credit Notes')
        self.assertContains(response, self.credit_note.credit_no)

    def test_table_partial_view(self, mock_get_user_context):
        response = self.client.get(reverse('credit_notes:creditnote_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'credit_notes/creditnote/_creditnote_table.html')
        self.assertContains(response, self.credit_note.credit_no)
        self.assertContains(response, '<table id="creditnoteTable"') # Check for DataTable rendering

    def test_create_view_get(self, mock_get_user_context):
        response = self.client.get(reverse('credit_notes:creditnote_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'credit_notes/creditnote/form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Credit Note')

    def test_create_view_post_valid(self, mock_get_user_context):
        data = {
            'entry_date': '2024-01-11',
            'credit_type': CreditType.objects.get(id=2).id, # Customer Credit
            'credit_to_display_name': 'Acme Corp [C001]',
            'reference': 'New ref',
            'particulars': 'New particulars',
            'amount': 150.00,
        }
        response = self.client.post(reverse('credit_notes:creditnote_add'), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertTrue(CreditNote.objects.filter(entry_date='2024-01-11').exists())
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCreditNoteList')

    def test_create_view_post_invalid(self, mock_get_user_context):
        data = { # Missing required fields, invalid amount
            'entry_date': '2024-01-11',
            'credit_type': CreditType.objects.get(id=2).id,
            'credit_to_display_name': 'Invalid Name [XYZ]', # Invalid code
            'amount': 0.00, # Invalid amount
        }
        response = self.client.post(reverse('credit_notes:creditnote_add'), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 400) # HTMX error
        self.assertTemplateUsed(response, 'credit_notes/creditnote/form.html')
        self.assertContains(response, 'This field is required') # For missing reference/particulars
        self.assertContains(response, 'No matching customer found for code: "XYZ"')
        self.assertContains(response, 'Amount must be a positive value.')

    def test_update_view_get(self, mock_get_user_context):
        response = self.client.get(reverse('credit_notes:creditnote_edit', args=[self.credit_note.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'credit_notes/creditnote/form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Edit Credit Note')
        self.assertContains(response, 'John Doe [E001]') # Initial value should be populated

    def test_update_view_post_valid(self, mock_get_user_context):
        updated_data = {
            'entry_date': '2024-01-10', # Same date
            'credit_type': CreditType.objects.get(id=1).id, # Same type
            'credit_to_display_name': 'John Doe [E001]', # Same code
            'reference': 'Updated ref',
            'particulars': 'Updated particulars',
            'amount': 550.00,
        }
        response = self.client.post(reverse('credit_notes:creditnote_edit', args=[self.credit_note.id]), updated_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.credit_note.refresh_from_db()
        self.assertEqual(self.credit_note.amount, 550.00)
        self.assertEqual(self.credit_note.reference, 'Updated ref')
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCreditNoteList')

    def test_delete_view_get(self, mock_get_user_context):
        response = self.client.get(reverse('credit_notes:creditnote_delete', args=[self.credit_note.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'credit_notes/creditnote/confirm_delete.html')
        self.assertTrue('creditnote' in response.context)
        self.assertContains(response, 'Confirm Delete')

    def test_delete_view_post(self, mock_get_user_context):
        credit_note_id = self.credit_note.id
        response = self.client.post(reverse('credit_notes:creditnote_delete', args=[credit_note_id]), HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertFalse(CreditNote.objects.filter(id=credit_note_id).exists())
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCreditNoteList')

    def test_autocomplete_credit_to_view(self, mock_get_user_context):
        # Set session for autocomplete
        session = self.client.session
        session['autocomplete_credit_type_id'] = CreditType.objects.get(id=1).id # Employee type
        session.save()

        response = self.client.get(reverse('credit_notes:autocomplete_credit_to'), {'q': 'john'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertIn('John Doe [E001]', response.content.decode('utf-8'))

        response = self.client.get(reverse('credit_notes:autocomplete_credit_to'), {'q': 'acme'}, HTTP_HX_REQUEST='true')
        self.assertNotIn('Acme Corp [C001]', response.content.decode('utf-8')) # Should not appear for employee type

        session['autocomplete_credit_type_id'] = CreditType.objects.get(id=2).id # Customer type
        session.save()
        response = self.client.get(reverse('credit_notes:autocomplete_credit_to'), {'q': 'acme'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertIn('Acme Corp [C001]', response.content.decode('utf-8'))

    def test_update_autocomplete_session_view(self, mock_get_user_context):
        response = self.client.post(reverse('credit_notes:update_autocomplete_session'), {'credit_type': '2'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(self.client.session['autocomplete_credit_type_id'], '2')

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for dynamic updates:** All CRUD operations are initiated via HTMX requests. The list view is loaded and refreshed using `hx-get` and `hx-trigger`. Form submissions (add/edit) use `hx-post` and `hx-swap="outerHTML"` targeting the modal content, allowing form errors to be re-rendered within the modal. On success, `HX-Trigger` sends a `refreshCreditNoteList` event to update the main table.
*   **Alpine.js for UI state management:** An Alpine.js `x-data` component (`creditNoteForm`) is used to control the visibility of the autocomplete suggestions list and to bind the `credit_to_display_name` input. A global Alpine.js event (`open-modal`, `close-modal`) is dispatched from HTMX `_` attributes to manage the modal's `x-show` state.
*   **DataTables for list views:** The `_creditnote_table.html` partial template ensures the `creditnoteTable` element is always the target of HTMX swaps. A JavaScript snippet in `list.html`'s `htmx:afterSwap` event listener (for the table container) initializes DataTables on the newly loaded table, ensuring client-side searching, sorting, and pagination.
*   **No full page reloads:** All interactive elements (`Add New`, `Edit`, `Delete`, form submissions, autocomplete) use HTMX to achieve dynamic interactions without full page reloads.
*   **`HX-Trigger` responses:** `204 No Content` status codes are used for successful HTMX form submissions (create, update, delete) along with an `HX-Trigger` header to signal a refresh for the list view, avoiding unnecessary content swaps for the submitting form. For invalid form submissions, `400 Bad Request` is returned along with the rendered form containing errors, allowing HTMX to swap it back into the modal.

### Final Notes

*   **Placeholders:** `[APP_NAME]`, `[MODEL_NAME]`, `[TABLE_NAME]`, etc., have been replaced with `credit_notes`, `CreditNote`, `tblACC_CreditNote`, etc., respectively.
*   **DRY Templates:** The use of `_creditnote_table.html` for the table content and `form.html` for create/update forms demonstrates DRY principles, as these partials are reused.
*   **Fat Model/Thin View:** Business logic such as `CreditNo` generation, entity display name retrieval, and `SCE` validation is encapsulated within the `CreditNote` model, keeping views concise and focused on orchestrating requests and responses.
*   **Comprehensive Tests:** Unit tests for models and integration tests for views are provided to ensure functionality and maintainability, including tests for HTMX interactions.
*   **Styling:** Tailwind CSS classes (`block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm`, `bg-blue-500`, etc.) are included directly in the templates, aligning with the "components" approach.
*   **User Context:** The `company_id` and `financial_year_id` are currently mocked using a helper function. In a production environment, these would be securely retrieved from the authenticated user's session or profile, ensuring data isolation per company/financial year.
*   **External Models:** The `MockEmployee`, `MockCustomer`, `MockSupplier` models are marked `managed=False` and exist for demonstration. In a real scenario, these would represent models from other Django apps or directly interacted with via database queries if not managed by Django.