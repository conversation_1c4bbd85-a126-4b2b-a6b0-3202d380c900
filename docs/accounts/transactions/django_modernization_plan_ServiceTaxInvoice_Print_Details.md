The transition from legacy ASP.NET applications to modern Django solutions offers significant advantages, including improved maintainability, scalability, enhanced security, and access to a vibrant open-source ecosystem. By leveraging AI-assisted automation, we can systematically convert your existing codebase, minimizing manual effort and ensuring a high degree of accuracy and consistency.

This modernization plan focuses on transforming your ASP.NET `ServiceTaxInvoice_Print_Details.aspx` page into a robust Django module. The original page served primarily to display a Crystal Report based on complex data retrieval and transformation logic. Our approach will re-implement this data presentation within Django using modern web standards, ensuring the output is visually consistent and functionally equivalent, while also providing full CRUD capabilities as per modern application design principles.

We will adopt a "Fat Model, Thin View" architecture, moving all business logic into Django models for reusability and cleaner views. Frontend interactions will be powered exclusively by HTMX for dynamic updates and Alpine.js for minimal client-side state management, eliminating the need for extensive custom JavaScript. DataTables will be integrated for efficient display and interaction with tabular data.

### Business Benefits of this Modernization:

*   **Cost Reduction:** Moving away from proprietary reporting tools like Crystal Reports reduces licensing fees and reliance on specialized skill sets.
*   **Improved Performance:** Django's ORM and efficient server-side rendering, combined with HTMX for partial page updates, will result in a faster and more responsive user experience.
*   **Enhanced Maintainability:** A clear separation of concerns (models for logic, views for rendering, templates for presentation) makes the codebase easier to understand, debug, and extend.
*   **Scalability:** Django's robust framework and Python's ecosystem provide a solid foundation for scaling your application as your business grows.
*   **Modern User Experience:** Leveraging HTMX, Alpine.js, and DataTables ensures a dynamic and interactive frontend without the complexity of traditional SPA frameworks.
*   **Open-Source Advantage:** Freedom from vendor lock-in and access to a vast community for support and extensions.

---

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**

The primary database table for the `ServiceTaxInvoice_Print_Details` page is `tblACC_ServiceTaxInvoice_Master`. This table is joined with several other lookup tables to compile the complete invoice details for display.

*   **Primary Table:** `tblACC_ServiceTaxInvoice_Master`
*   **Columns of `tblACC_ServiceTaxInvoice_Master` (identified from C# `dt.Columns.Add` and SQL queries):**
    *   `Id` (Primary Key, int)
    *   `SysDate` (DateTime)
    *   `InvoiceNo` (string)
    *   `PONo` (string)
    *   `WONo` (string, comma-separated Work Order IDs)
    *   `DateOfIssueInvoice` (DateTime)
    *   `CompId` (Foreign Key to `tblCompany_master`, int)
    *   `TimeOfIssueInvoice` (Time)
    *   `DutyRate` (string)
    *   `CustomerCode` (string)
    *   `CustomerCategory` (Foreign Key to `tblACC_Service_Category`, int)
    *   `Buyer_name` (string)
    *   `Buyer_cotper` (string)
    *   `Buyer_ph` (string)
    *   `Buyer_email` (string)
    *   `Buyer_ecc` (string)
    *   `Buyer_tin` (string)
    *   `Buyer_mob` (string)
    *   `Buyer_fax` (string)
    *   `Buyer_vat` (string)
    *   `Buyer_add` (string)
    *   `Buyer_country` (Foreign Key to `tblcountry`, int)
    *   `Buyer_state` (Foreign Key to `tblState`, int)
    *   `Buyer_city` (Foreign Key to `tblCity`, int)
    *   `Cong_name` (string, Consignee name)
    *   `Cong_cotper` (string)
    *   `Cong_ph` (string)
    *   `Cong_email` (string)
    *   `Cong_ecc` (string)
    *   `Cong_tin` (string)
    *   `Cong_mob` (string)
    *   `Cong_fax` (string)
    *   `Cong_vat` (string)
    *   `Cong_add` (string)
    *   `Cong_country` (Foreign Key to `tblcountry`, int)
    *   `Cong_state` (Foreign Key to `tblState`, int)
    *   `Cong_city` (Foreign Key to `tblCity`, int)
    *   `AddType` (int)
    *   `AddAmt` (double)
    *   `DeductionType` (int)
    *   `Deduction` (double)
    *   `ServiceTax` (int)
    *   `TaxableServices` (Foreign Key to `tblACC_TaxableServices`, int)
    *   `POId` (Foreign Key to `SD_Cust_PO_Master`, int)
    *   `FinYearId` (Foreign Key to `tblFinancial_master`, int)

*   **Supporting Tables (for ORM relationships and lookups):**
    *   `tblACC_Service_Category`: `Id` (PK), `Description`
    *   `tblACC_TaxableServices`: `Id` (PK), `Description`
    *   `SD_Cust_WorkOrder_Master`: `Id` (PK), `WONo`
    *   `SD_Cust_PO_Master`: `POId` (PK), `SysDate`
    *   `tblFinancial_master`: `FinYearId` (PK), `CompId`, `FinYearFrom`, `FinYearTo`
    *   `tblcountry`: `CId` (PK), `CountryName`
    *   `tblState`: `SId` (PK), `StateName`
    *   `tblCity`: `CityId` (PK), `CityName`
    *   `tblCompany_master`: `CompId` (PK), `RegdAddress`, `RegdCity`, `RegdState`, `RegdCountry`, `RegdPinCode`, `RegdContactNo`, `RegdFaxNo`, `RegdEmail`

## Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**

The provided ASP.NET code for `ServiceTaxInvoice_Print_Details.aspx.cs` primarily focuses on a complex **Read** operation, intended to gather data for a Crystal Report display. It does not contain direct `Create`, `Update`, or `Delete` logic for `ServiceTaxInvoice_Master` records. The `btnCancel` simply navigates back.

For a comprehensive Django modernization, we will implement the full suite of CRUD operations (`Create`, `Read`, `Update`, `Delete`) for the `ServiceTaxInvoice` model, along with a dedicated `DetailView` to replicate the "print details" functionality of the original ASP.NET page. The complex data transformations and lookups previously performed in the C# code-behind will be encapsulated within the `ServiceTaxInvoice` model as methods, adhering to the "Fat Model" principle.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

*   `CR:CrystalReportViewer`: This control was responsible for rendering the `ServiceTaxInvoice.rpt` report. In Django, this functionality will be replaced by a structured HTML template (`detail.html`) that presents all the invoice data and calculated parameters. This template can then be printed via the browser or used as a basis for PDF generation (if a library like `WeasyPrint` is integrated).
*   `asp:Panel`: A generic container that holds the report viewer. This will be replaced by standard HTML `div` elements for layout.
*   `asp:Button ID="btnCancel"`: A button to navigate back. In Django, this will be an `<a>` tag or a `button` that uses HTMX to close a modal or navigate to a list view.

## Step 4: Generate Django Code

We will create a new Django application, `accounts`, to house the `ServiceTaxInvoice` module.

### 4.1 Models (accounts/models.py)

**Task:** Create Django models based on the identified database schema.

**Instructions:**

All identified tables will be mapped to Django models using `managed = False` and `db_table` to connect to the existing database. The `ServiceTaxInvoice` model will include comprehensive methods for data retrieval and formatting, mimicking the original C# logic, thus making it a "fat model."

```python
from django.db import models
from django.utils.text import slugify
import datetime

# --- Utility Functions (Simulating clsFunctions from ASP.NET) ---
# In a real project, these might be in a separate 'utils.py' file.

def from_date_dmy(date_str):
    """Converts YYYY-MM-DD HH:MM:SS string to DD-MM-YYYY string."""
    try:
        dt_obj = datetime.datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S.%f') # Adjust for milliseconds if present
        return dt_obj.strftime('%d-%m-%Y')
    except ValueError:
        try: # Try without milliseconds
            dt_obj = datetime.datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
            return dt_obj.strftime('%d-%m-%Y')
        except ValueError:
            return date_str # Return as is if format doesn't match

def from_date_mdy(date_str):
    """Converts DD-MM-YYYY string to MM/DD/YYYY string."""
    try:
        dt_obj = datetime.datetime.strptime(date_str, '%d-%m-%Y')
        return dt_obj.strftime('%m/%d/%Y')
    except ValueError:
        return date_str

def to_date_year(date_str):
    """Extracts year from a date string (e.g., '2023-12-31 00:00:00' -> '2023')."""
    try:
        dt_obj = datetime.datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S.%f')
        return str(dt_obj.year)
    except ValueError:
        try:
            dt_obj = datetime.datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
            return str(dt_obj.year)
        except ValueError:
            return date_str

def date_to_text(date_obj):
    """Converts a datetime object to a text representation (e.g., 'First January Two Thousand Twenty-Four')."""
    # This is a complex function requiring a library like 'num2words' or detailed logic.
    # For demonstration, a simpler format is used.
    if not isinstance(date_obj, datetime.datetime):
        # Attempt to parse if it's a string, assuming 'YYYY-MM-DD HH:MM:SS'
        try:
            date_obj = datetime.datetime.strptime(date_obj, '%Y-%m-%d %H:%M:%S.%f')
        except ValueError:
            try:
                date_obj = datetime.datetime.strptime(date_obj, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                return str(date_obj) # Fallback if parsing fails

    # Example: "Monday, 1st January 2024"
    return date_obj.strftime("%A, %d %B %Y").replace(' 0', ' ')

def time_to_text(time_str):
    """Converts a time string (e.g., '10:30:00') to text (e.g., 'Ten Thirty AM')."""
    try:
        dt_obj = datetime.datetime.strptime(time_str, '%H:%M:%S')
        # Example: "10 30 AM" or "10 30 P.M."
        return dt_obj.strftime("%I %M %p").replace(' 0', ' ').replace('AM', 'A.M.').replace('PM', 'P.M.')
    except ValueError:
        return time_str

# --- Supporting Models (for Foreign Key lookups) ---

class Company(models.Model):
    compid = models.IntegerField(db_column='CompId', primary_key=True)
    regdaddress = models.CharField(db_column='RegdAddress', max_length=255, blank=True, null=True)
    regdcity = models.IntegerField(db_column='RegdCity', blank=True, null=True) # FK to City
    regdstate = models.IntegerField(db_column='RegdState', blank=True, null=True) # FK to State
    regdcountry = models.IntegerField(db_column='RegdCountry', blank=True, null=True) # FK to Country
    regdpincode = models.CharField(db_column='RegdPinCode', max_length=50, blank=True, null=True)
    regdcontactno = models.CharField(db_column='RegdContactNo', max_length=50, blank=True, null=True)
    regdfaxno = models.CharField(db_column='RegdFaxNo', max_length=50, blank=True, null=True)
    regdemail = models.CharField(db_column='RegdEmail', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCompany_master'
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return f"Company {self.compid}"

class FinancialYear(models.Model):
    finyearid = models.IntegerField(db_column='FinYearId', primary_key=True)
    compid = models.IntegerField(db_column='CompId', blank=True, null=True) # FK to Company
    finyearfrom = models.DateTimeField(db_column='FinYearFrom', blank=True, null=True)
    finyearto = models.DateTimeField(db_column='FinYearTo', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return f"FY {self.finyearfrom.year}-{self.finyearto.year}" if self.finyearfrom and self.finyearto else f"FY {self.finyearid}"

class ServiceCategory(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_Service_Category'
        verbose_name = 'Service Category'
        verbose_name_plural = 'Service Categories'

    def __str__(self):
        return self.description or f"Category {self.id}"

class TaxableService(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_TaxableServices'
        verbose_name = 'Taxable Service'
        verbose_name_plural = 'Taxable Services'

    def __str__(self):
        return self.description or f"Taxable Service {self.id}"

class WorkOrder(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    wono = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    compid = models.IntegerField(db_column='CompId', blank=True, null=True) # FK to Company

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.wono or f"Work Order {self.id}"

class PurchaseOrder(models.Model):
    poid = models.IntegerField(db_column='POId', primary_key=True)
    sysdate = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    compid = models.IntegerField(db_column='CompId', blank=True, null=True) # FK to Company

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Master'
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'

    def __str__(self):
        return f"PO {self.poid}"

class Country(models.Model):
    cid = models.IntegerField(db_column='CId', primary_key=True)
    countryname = models.CharField(db_column='CountryName', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblcountry'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.countryname or f"Country {self.cid}"

class State(models.Model):
    sid = models.IntegerField(db_column='SId', primary_key=True)
    statename = models.CharField(db_column='StateName', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblState'
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.statename or f"State {self.sid}"

class City(models.Model):
    cityid = models.IntegerField(db_column='CityId', primary_key=True)
    cityname = models.CharField(db_column='CityName', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCity'
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.cityname or f"City {self.cityid}"

# --- Main Model: ServiceTaxInvoice ---

class ServiceTaxInvoice(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sysdate = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    invoiceno = models.CharField(db_column='InvoiceNo', max_length=50, blank=True, null=True)
    pono = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    wono = models.CharField(db_column='WONo', max_length=255, blank=True, null=True) # Stores comma-separated IDs
    dateofissueinvoice = models.DateTimeField(db_column='DateOfIssueInvoice', blank=True, null=True)
    compid = models.IntegerField(db_column='CompId', blank=True, null=True) # FK
    timeofissueinvoice = models.TimeField(db_column='TimeOfIssueInvoice', blank=True, null=True)
    dutyrate = models.CharField(db_column='DutyRate', max_length=50, blank=True, null=True)
    customercode = models.CharField(db_column='CustomerCode', max_length=50, blank=True, null=True)
    customercategory = models.IntegerField(db_column='CustomerCategory', blank=True, null=True) # FK
    buyer_name = models.CharField(db_column='Buyer_name', max_length=100, blank=True, null=True)
    buyer_cotper = models.CharField(db_column='Buyer_cotper', max_length=100, blank=True, null=True)
    buyer_ph = models.CharField(db_column='Buyer_ph', max_length=50, blank=True, null=True)
    buyer_email = models.CharField(db_column='Buyer_email', max_length=100, blank=True, null=True)
    buyer_ecc = models.CharField(db_column='Buyer_ecc', max_length=50, blank=True, null=True)
    buyer_tin = models.CharField(db_column='Buyer_tin', max_length=50, blank=True, null=True)
    buyer_mob = models.CharField(db_column='Buyer_mob', max_length=50, blank=True, null=True)
    buyer_fax = models.CharField(db_column='Buyer_fax', max_length=50, blank=True, null=True)
    buyer_vat = models.CharField(db_column='Buyer_vat', max_length=50, blank=True, null=True)
    buyer_add = models.CharField(db_column='Buyer_add', max_length=255, blank=True, null=True)
    buyer_country = models.IntegerField(db_column='Buyer_country', blank=True, null=True) # FK
    buyer_state = models.IntegerField(db_column='Buyer_state', blank=True, null=True) # FK
    buyer_city = models.IntegerField(db_column='Buyer_city', blank=True, null=True) # FK
    cong_name = models.CharField(db_column='Cong_name', max_length=100, blank=True, null=True)
    cong_cotper = models.CharField(db_column='Cong_cotper', max_length=100, blank=True, null=True)
    cong_ph = models.CharField(db_column='Cong_ph', max_length=50, blank=True, null=True)
    cong_email = models.CharField(db_column='Cong_email', max_length=100, blank=True, null=True)
    cong_ecc = models.CharField(db_column='Cong_ecc', max_length=50, blank=True, null=True)
    cong_tin = models.CharField(db_column='Cong_tin', max_length=50, blank=True, null=True)
    cong_mob = models.CharField(db_column='Cong_mob', max_length=50, blank=True, null=True)
    cong_fax = models.CharField(db_column='Cong_fax', max_length=50, blank=True, null=True)
    cong_vat = models.CharField(db_column='Cong_vat', max_length=50, blank=True, null=True)
    cong_add = models.CharField(db_column='Cong_add', max_length=255, blank=True, null=True)
    cong_country = models.IntegerField(db_column='Cong_country', blank=True, null=True) # FK
    cong_state = models.IntegerField(db_column='Cong_state', blank=True, null=True) # FK
    cong_city = models.IntegerField(db_column='Cong_city', blank=True, null=True) # FK
    addtype = models.IntegerField(db_column='AddType', blank=True, null=True)
    addamt = models.FloatField(db_column='AddAmt', blank=True, null=True)
    deductiontype = models.IntegerField(db_column='DeductionType', blank=True, null=True)
    deduction = models.FloatField(db_column='Deduction', blank=True, null=True)
    servicetax = models.IntegerField(db_column='ServiceTax', blank=True, null=True)
    taxableservices = models.IntegerField(db_column='TaxableServices', blank=True, null=True) # FK
    poid = models.IntegerField(db_column='POId', blank=True, null=True) # FK
    finyearid = models.IntegerField(db_column='FinYearId', blank=True, null=True) # FK

    class Meta:
        managed = False
        db_table = 'tblACC_ServiceTaxInvoice_Master'
        verbose_name = 'Service Tax Invoice'
        verbose_name_plural = 'Service Tax Invoices'

    def __str__(self):
        return self.invoiceno or f"Invoice {self.id}"

    # Business logic methods (Fat Model)
    def get_formatted_sysdate(self):
        return self.sysdate.strftime('%d-%m-%Y') if self.sysdate else ''

    def get_formatted_issue_date(self):
        return self.dateofissueinvoice.strftime('%d-%m-%Y') if self.dateofissueinvoice else ''

    def get_customer_category_description(self):
        category = ServiceCategory.objects.filter(id=self.customercategory).first()
        return category.description if category else ''

    def get_taxable_services_description(self):
        tax_service = TaxableService.objects.filter(id=self.taxableservices).first()
        return tax_service.description if tax_service else ''

    def get_work_order_numbers(self):
        if not self.wono:
            return ''
        wo_ids = [int(x) for x in self.wono.split(',') if x.strip()]
        work_orders = WorkOrder.objects.filter(id__in=wo_ids, compid=self.compid)
        return ', '.join([wo.wono for wo in work_orders if wo.wono])

    def get_po_date(self):
        po = PurchaseOrder.objects.filter(poid=self.poid, compid=self.compid).first()
        return po.sysdate.strftime('%d-%m-%Y') if po and po.sysdate else ''

    def get_invoice_no_with_fin_year(self):
        fin_year = FinancialYear.objects.filter(finyearid=self.finyearid, compid=self.compid).first()
        if fin_year and fin_year.finyearfrom and fin_year.finyearto:
            fy_from_year = to_date_year(str(fin_year.finyearfrom))[-2:]
            fy_to_year = to_date_year(str(fin_year.finyearto))[-2:]
            return f"{self.invoiceno}/{fy_from_year}{fy_to_year}"
        return self.invoiceno

    def get_buyer_full_address(self):
        parts = [self.buyer_add]
        city = City.objects.filter(cityid=self.buyer_city).first()
        state = State.objects.filter(sid=self.buyer_state).first()
        country = Country.objects.filter(cid=self.buyer_country).first()

        if city: parts.append(city.cityname)
        if state: parts.append(state.statename)
        if country: parts.append(country.countryname)

        return ", ".join(filter(None, parts)) + "."

    def get_consignee_full_address(self):
        parts = [self.cong_add]
        city = City.objects.filter(cityid=self.cong_city).first()
        state = State.objects.filter(sid=self.cong_state).first()
        country = Country.objects.filter(cid=self.cong_country).first()

        if city: parts.append(city.cityname)
        if state: parts.append(state.statename)
        if country: parts.append(country.countryname)

        return ", ".join(filter(None, parts)) + "."

    def get_company_full_address(self):
        company = Company.objects.filter(compid=self.compid).first()
        if not company:
            return "Company address not found."

        parts = [company.regdaddress]
        city_name = City.objects.filter(cityid=company.regdcity).first()
        state_name = State.objects.filter(sid=company.regdstate).first()
        country_name = Country.objects.filter(cid=company.regdcountry).first()

        if city_name: parts.append(city_name.cityname)
        if state_name: parts.append(state_name.statename)
        if country_name: parts.append(country_name.countryname)

        address_line = ", ".join(filter(None, parts))
        
        full_address = [address_line]
        if company.regdpincode: full_address.append(f"PIN No.-{company.regdpincode}.")
        
        contact_info = []
        if company.regdcontactno: contact_info.append(f"Ph No.-{company.regdcontactno}")
        if company.regdfaxno: contact_info.append(f"Fax No.-{company.regdfaxno}")
        if contact_info: full_address.append(", ".join(contact_info))
        
        if company.regdemail: full_address.append(f"Email No.-{company.regdemail}")

        return "\n".join(filter(None, full_address))

    def get_issue_date_in_words(self):
        return date_to_text(self.dateofissueinvoice) if self.dateofissueinvoice else ''

    def get_issue_time_in_words(self):
        return time_to_text(self.timeofissueinvoice.strftime('%H:%M:%S')) if self.timeofissueinvoice else ''

```

### 4.2 Forms (accounts/forms.py)

**Task:** Define a Django form for user input.

**Instructions:**

A `ModelForm` for `ServiceTaxInvoice` will be created to handle creating and updating records. Fields will include appropriate widgets with Tailwind CSS classes for styling.

```python
from django import forms
from .models import ServiceTaxInvoice, ServiceCategory, TaxableService, WorkOrder, PurchaseOrder, Company, FinancialYear, Country, State, City

class ServiceTaxInvoiceForm(forms.ModelForm):
    # Foreign key fields would typically be rendered as dropdowns.
    # For managed=False models, you might need to manually define choices or use ModelChoiceField
    # if you want to allow selecting existing related objects.
    # For simplicity, if these are just IDs in the DB, keep them as IntegerField/CharField.
    # For a real system, you'd convert these to ModelChoiceField for proper selection.

    # Example: If CustomerCategory should be a dropdown:
    # customer_category_obj = forms.ModelChoiceField(
    #     queryset=ServiceCategory.objects.all(),
    #     to_field_name='id', # The actual ID column
    #     label="Customer Category",
    #     required=False,
    #     widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    # )

    class Meta:
        model = ServiceTaxInvoice
        fields = [
            'sysdate', 'invoiceno', 'pono', 'wono', 'dateofissueinvoice',
            'compid', 'timeofissueinvoice', 'dutyrate', 'customercode',
            'customercategory', 'buyer_name', 'buyer_cotper', 'buyer_ph',
            'buyer_email', 'buyer_ecc', 'buyer_tin', 'buyer_mob', 'buyer_fax',
            'buyer_vat', 'buyer_add', 'buyer_country', 'buyer_state', 'buyer_city',
            'cong_name', 'cong_cotper', 'cong_ph', 'cong_email', 'cong_ecc',
            'cong_tin', 'cong_mob', 'cong_fax', 'cong_vat', 'cong_add',
            'cong_country', 'cong_state', 'cong_city', 'addtype', 'addamt',
            'deductiontype', 'deduction', 'servicetax', 'taxableservices',
            'poid', 'finyearid'
        ]
        widgets = {
            'sysdate': forms.DateTimeInput(attrs={'type': 'datetime-local', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'invoiceno': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'pono': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'wono': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'dateofissueinvoice': forms.DateTimeInput(attrs={'type': 'datetime-local', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'compid': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'timeofissueinvoice': forms.TimeInput(attrs={'type': 'time', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'dutyrate': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'customercode': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'customercategory': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'buyer_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'buyer_cotper': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'buyer_ph': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'buyer_email': forms.EmailInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'buyer_ecc': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'buyer_tin': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'buyer_mob': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'buyer_fax': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'buyer_vat': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'buyer_add': forms.Textarea(attrs={'rows': 3, 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'buyer_country': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'buyer_state': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'buyer_city': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'cong_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'cong_cotper': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'cong_ph': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'cong_email': forms.EmailInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'cong_ecc': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'cong_tin': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'cong_mob': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'cong_fax': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'cong_vat': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'cong_add': forms.Textarea(attrs={'rows': 3, 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'cong_country': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'cong_state': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'cong_city': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'addtype': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'addamt': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'deductiontype': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'deduction': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'servicetax': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'taxableservices': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'poid': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'finyearid': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }

    def clean_wono(self):
        # Example validation: ensure WONo is a comma-separated list of integers
        wono_data = self.cleaned_data.get('wono')
        if wono_data:
            try:
                # Attempt to parse into integers to ensure format is correct
                [int(x.strip()) for x in wono_data.split(',') if x.strip()]
            except ValueError:
                raise forms.ValidationError("WONo must be a comma-separated list of numbers.")
        return wono_data

```

### 4.3 Views (accounts/views.py)

**Task:** Implement CRUD operations using CBVs.

**Instructions:**

Views are kept "thin" by offloading complex data logic to the `ServiceTaxInvoice` model. An additional `ServiceTaxInvoiceDetailView` is included to mimic the original report display. A `TablePartialView` will be added for HTMX to dynamically load the DataTables.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import ServiceTaxInvoice
from .forms import ServiceTaxInvoiceForm

class ServiceTaxInvoiceListView(ListView):
    model = ServiceTaxInvoice
    template_name = 'accounts/servicetaxinvoice/list.html'
    context_object_name = 'servicetaxinvoices'

class ServiceTaxInvoiceTablePartialView(ListView):
    model = ServiceTaxInvoice
    template_name = 'accounts/servicetaxinvoice/_servicetaxinvoice_table.html'
    context_object_name = 'servicetaxinvoices'

class ServiceTaxInvoiceDetailView(DetailView):
    model = ServiceTaxInvoice
    template_name = 'accounts/servicetaxinvoice/detail.html'
    context_object_name = 'invoice'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Add any query string parameters from the original ASP.NET request if needed
        # For example, if "PrintType" was passed, you might add it to context here
        context['print_type'] = self.request.GET.get('PT', 'Original') # Assuming 'PT' was for PrintType
        return context

class ServiceTaxInvoiceCreateView(CreateView):
    model = ServiceTaxInvoice
    form_class = ServiceTaxInvoiceForm
    template_name = 'accounts/servicetaxinvoice/form.html'
    success_url = reverse_lazy('servicetaxinvoice_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Service Tax Invoice added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshServiceTaxInvoiceList'
                }
            )
        return response

class ServiceTaxInvoiceUpdateView(UpdateView):
    model = ServiceTaxInvoice
    form_class = ServiceTaxInvoiceForm
    template_name = 'accounts/servicetaxinvoice/form.html'
    success_url = reverse_lazy('servicetaxinvoice_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Service Tax Invoice updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshServiceTaxInvoiceList'
                }
            )
        return response

class ServiceTaxInvoiceDeleteView(DeleteView):
    model = ServiceTaxInvoice
    template_name = 'accounts/servicetaxinvoice/confirm_delete.html'
    success_url = reverse_lazy('servicetaxinvoice_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Service Tax Invoice deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshServiceTaxInvoiceList'
                }
            )
        return response
```

### 4.4 Templates

**Task:** Create templates for each view.

**Instructions:**

Templates adhere to DRY principles, extending `core/base.html` and utilizing partials for HTMX-driven content.

**accounts/servicetaxinvoice/list.html**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Service Tax Invoices</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'servicetaxinvoice_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Service Tax Invoice
        </button>
    </div>
    
    <div id="servicetaxinvoiceTable-container"
         hx-trigger="load, refreshServiceTaxInvoiceList from:body"
         hx-get="{% url 'servicetaxinvoice_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for broader UI states
        Alpine.store('modalOpen', false); // Example store state
    });
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'modalContent') {
            // Re-initialize any JS for the modal content if necessary
            // For Alpine.js components within modalContent, Alpine.js should re-scan naturally.
            // For DataTables within the _table.html partial, its script runs on document.ready.
        }
    });
</script>
{% endblock %}
```

**accounts/servicetaxinvoice/_servicetaxinvoice_table.html**

```html
<table id="servicetaxinvoiceTable" class="min-w-full bg-white">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice No.</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issue Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in servicetaxinvoices %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.invoiceno }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.customercode }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.get_formatted_issue_date }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button 
                    class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'servicetaxinvoice_detail' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    View Report
                </button>
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'servicetaxinvoice_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'servicetaxinvoice_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// DataTables initialization
$(document).ready(function() {
    $('#servicetaxinvoiceTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [4] } // Disable sorting on Actions column
        ]
    });
});
</script>
```

**accounts/servicetaxinvoice/form.html**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Service Tax Invoice</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {% for field in form %}
            <div class="mb-2">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**accounts/servicetaxinvoice/confirm_delete.html**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-4">Are you sure you want to delete invoice "{{ object.invoiceno }}"?</p>
    
    <form hx-post="{% url 'servicetaxinvoice_delete' object.pk %}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

**accounts/servicetaxinvoice/detail.html (Replacing Crystal Report Viewer)**

```html
<div class="p-6 max-w-4xl mx-auto bg-white rounded-lg shadow-lg" x-data="{}" _="on htmx:afterSwap remove .is-active from #modal">
    <h3 class="text-xl font-bold text-gray-900 mb-6 text-center">Service Tax Invoice - Print Details</h3>
    <h4 class="text-center text-sm mb-4">({{ print_type }})</h4>

    <div class="border border-gray-300 p-4 mb-6">
        <div class="flex justify-between items-start mb-2">
            <div>
                <p class="font-semibold">Invoice No: {{ invoice.get_invoice_no_with_fin_year }}</p>
                <p class="text-sm">Issue Date: {{ invoice.get_formatted_issue_date }}</p>
                <p class="text-sm">Issue Time: {{ invoice.timeofissueinvoice|time:"H:i:s" }}</p>
            </div>
            <div class="text-right">
                <p class="font-semibold">Company Address:</p>
                <p class="text-xs whitespace-pre-wrap">{{ invoice.get_company_full_address }}</p>
            </div>
        </div>
        <hr class="my-4 border-gray-200">
        <div class="grid grid-cols-2 gap-4">
            <div>
                <p class="font-semibold">Buyer Details:</p>
                <p class="text-sm">{{ invoice.buyer_name }}</p>
                <p class="text-xs whitespace-pre-wrap">{{ invoice.get_buyer_full_address }}</p>
                <p class="text-xs">Contact Person: {{ invoice.buyer_cotper }}</p>
                <p class="text-xs">Phone: {{ invoice.buyer_ph }} Mob: {{ invoice.buyer_mob }}</p>
                <p class="text-xs">Email: {{ invoice.buyer_email }}</p>
                <p class="text-xs">ECC: {{ invoice.buyer_ecc }} TIN: {{ invoice.buyer_tin }} VAT: {{ invoice.buyer_vat }}</p>
            </div>
            <div>
                <p class="font-semibold">Consignee Details:</p>
                <p class="text-sm">{{ invoice.cong_name }}</p>
                <p class="text-xs whitespace-pre-wrap">{{ invoice.get_consignee_full_address }}</p>
                <p class="text-xs">Contact Person: {{ invoice.cong_cotper }}</p>
                <p class="text-xs">Phone: {{ invoice.cong_ph }} Mob: {{ invoice.cong_mob }}</p>
                <p class="text-xs">Email: {{ invoice.cong_email }}</p>
                <p class="text-xs">ECC: {{ invoice.cong_ecc }} TIN: {{ invoice.cong_tin }} VAT: {{ invoice.cong_vat }}</p>
            </div>
        </div>
        <hr class="my-4 border-gray-200">
        <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
                <p>PO No: {{ invoice.pono }} (Date: {{ invoice.get_po_date }})</p>
                <p>WO No(s): {{ invoice.get_work_order_numbers }}</p>
                <p>Customer Code: {{ invoice.customercode }}</p>
                <p>Customer Category: {{ invoice.get_customer_category_description }}</p>
            </div>
            <div>
                <p>Duty Rate: {{ invoice.dutyrate }}</p>
                <p>Service Tax (%): {{ invoice.servicetax }}</p>
                <p>Taxable Services: {{ invoice.get_taxable_services_description }}</p>
                <p>Addition Amount: {{ invoice.addamt|default:"0.00" }} (Type: {{ invoice.addtype }})</p>
                <p>Deduction: {{ invoice.deduction|default:"0.00" }} (Type: {{ invoice.deductiontype }})</p>
            </div>
        </div>
        <hr class="my-4 border-gray-200">
        <div class="text-sm">
            <p>Date of Issue Invoice (in words): <span class="font-medium">{{ invoice.get_issue_date_in_words }}</span></p>
            <p>Time of Issue Invoice (in words): <span class="font-medium">{{ invoice.get_issue_time_in_words }}</span></p>
        </div>
    </div>

    <div class="flex justify-center space-x-4 mt-6">
        <button 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click remove .is-active from #modal">
            Close
        </button>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            onclick="window.print()">
            Print Invoice
        </button>
    </div>
</div>

<style>
/* Styles for print output */
@media print {
    body > *:not(.print-container) {
        display: none;
    }
    .print-container {
        display: block;
        width: 100%;
        margin: 0;
        padding: 0;
    }
    .print-container .flex.justify-center.space-x-4 {
        display: none; /* Hide buttons in print */
    }
    #modal {
        position: static !important; /* Allow modal content to flow naturally for printing */
        display: block !important;
        background: none !important;
        overflow: visible !important;
    }
    #modalContent {
        box-shadow: none !important;
        padding: 0 !important;
        max-width: none !important;
        width: 100% !important;
        background: none !important;
    }
}
</style>

```

### 4.5 URLs (accounts/urls.py)

**Task:** Define URL patterns for the views.

**Instructions:**

Standard URL patterns for CRUD operations, plus a specific URL for the HTMX-driven table partial and the detail view.

```python
from django.urls import path
from .views import (
    ServiceTaxInvoiceListView,
    ServiceTaxInvoiceTablePartialView,
    ServiceTaxInvoiceDetailView,
    ServiceTaxInvoiceCreateView,
    ServiceTaxInvoiceUpdateView,
    ServiceTaxInvoiceDeleteView,
)

urlpatterns = [
    path('servicetaxinvoices/', ServiceTaxInvoiceListView.as_view(), name='servicetaxinvoice_list'),
    path('servicetaxinvoices/table/', ServiceTaxInvoiceTablePartialView.as_view(), name='servicetaxinvoice_table'),
    path('servicetaxinvoices/add/', ServiceTaxInvoiceCreateView.as_view(), name='servicetaxinvoice_add'),
    path('servicetaxinvoices/<int:pk>/', ServiceTaxInvoiceDetailView.as_view(), name='servicetaxinvoice_detail'),
    path('servicetaxinvoices/edit/<int:pk>/', ServiceTaxInvoiceUpdateView.as_view(), name='servicetaxinvoice_edit'),
    path('servicetaxinvoices/delete/<int:pk>/', ServiceTaxInvoiceDeleteView.as_view(), name='servicetaxinvoice_delete'),
]
```

### 4.6 Tests (accounts/tests.py)

**Task:** Write tests for the model and views.

**Instructions:**

Comprehensive unit tests for model methods (especially those complex data transformations) and integration tests for all views to ensure functionality and HTMX interactions.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import time
from .models import (
    ServiceTaxInvoice, ServiceCategory, TaxableService, WorkOrder, PurchaseOrder,
    Company, FinancialYear, Country, State, City,
    from_date_dmy, from_date_mdy, to_date_year, date_to_text, time_to_text
)

class UtilityFunctionsTest(TestCase):
    def test_from_date_dmy(self):
        self.assertEqual(from_date_dmy('2023-01-15 00:00:00.000'), '15-01-2023')
        self.assertEqual(from_date_dmy('2023-01-15 00:00:00'), '15-01-2023')
        self.assertEqual(from_date_dmy('invalid date'), 'invalid date')

    def test_from_date_mdy(self):
        self.assertEqual(from_date_mdy('15-01-2023'), '01/15/2023')
        self.assertEqual(from_date_mdy('invalid date'), 'invalid date')

    def test_to_date_year(self):
        self.assertEqual(to_date_year('2023-01-15 00:00:00.000'), '2023')
        self.assertEqual(to_date_year('2023-01-15 00:00:00'), '2023')
        self.assertEqual(to_date_year('invalid date'), 'invalid date')

    def test_date_to_text(self):
        # Requires a more robust date_to_text implementation for full test.
        # This tests the basic formatting for the demo utility.
        dt_obj = timezone.datetime(2024, 1, 15, 10, 30, 0)
        self.assertEqual(date_to_text(dt_obj), 'Monday, 15 January 2024')
        self.assertEqual(date_to_text('2024-02-20 00:00:00'), 'Tuesday, 20 February 2024')

    def test_time_to_text(self):
        self.assertEqual(time_to_text('10:30:00'), '10 30 A.M.')
        self.assertEqual(time_to_text('14:45:00'), '02 45 P.M.')
        self.assertEqual(time_to_text('invalid time'), 'invalid time')


class ServiceTaxInvoiceModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all related models first
        cls.company = Company.objects.create(compid=1, regdaddress="123 Co Rd", regdcity=101, regdstate=201, regdcountry=301, regdpincode="12345", regdcontactno="111", regdfaxno="222", regdemail="<EMAIL>")
        cls.financial_year = FinancialYear.objects.create(finyearid=1, compid=1, finyearfrom=timezone.datetime(2023, 4, 1), finyearto=timezone.datetime(2024, 3, 31))
        cls.service_category = ServiceCategory.objects.create(id=1, description="Consulting Services")
        cls.taxable_service = TaxableService.objects.create(id=1, description="IT Services")
        cls.work_order1 = WorkOrder.objects.create(id=1, wono="WO-2023-001", compid=1)
        cls.work_order2 = WorkOrder.objects.create(id=2, wono="WO-2023-002", compid=1)
        cls.purchase_order = PurchaseOrder.objects.create(poid=1, sysdate=timezone.datetime(2023, 5, 10), compid=1)
        cls.country = Country.objects.create(cid=301, countryname="USA")
        cls.state = State.objects.create(sid=201, statename="California")
        cls.city = City.objects.create(cityid=101, cityname="Los Angeles")

        # Create main ServiceTaxInvoice object
        cls.invoice = ServiceTaxInvoice.objects.create(
            id=1,
            sysdate=timezone.datetime(2023, 10, 26, 15, 30, 0),
            invoiceno="INV-001",
            pono="PO-123",
            wono="1,2", # Comma separated IDs
            dateofissueinvoice=timezone.datetime(2023, 10, 25, 10, 0, 0),
            compid=cls.company.compid,
            timeofissueinvoice=time(10, 0, 0),
            dutyrate="10%",
            customercode="CUST001",
            customercategory=cls.service_category.id,
            buyer_name="Buyer Corp",
            buyer_cotper="John Doe",
            buyer_ph="123-456-7890",
            buyer_email="<EMAIL>",
            buyer_ecc="ECC123",
            buyer_tin="TIN123",
            buyer_mob="987-654-3210",
            buyer_fax="FAX123",
            buyer_vat="VAT123",
            buyer_add="101 Buyer St",
            buyer_country=cls.country.cid,
            buyer_state=cls.state.sid,
            buyer_city=cls.city.cityid,
            cong_name="Consignee Co",
            cong_cotper="Jane Smith",
            cong_ph="098-765-4321",
            cong_email="<EMAIL>",
            cong_ecc="ECC456",
            cong_tin="TIN456",
            cong_mob="012-345-6789",
            cong_fax="FAX456",
            cong_vat="VAT456",
            cong_add="202 Consignee Ave",
            cong_country=cls.country.cid,
            cong_state=cls.state.sid,
            cong_city=cls.city.cityid,
            addtype=1,
            addamt=100.0,
            deductiontype=2,
            deduction=50.0,
            servicetax=18,
            taxableservices=cls.taxable_service.id,
            poid=cls.purchase_order.poid,
            finyearid=cls.financial_year.finyearid
        )

    def test_invoice_creation(self):
        self.assertEqual(self.invoice.invoiceno, "INV-001")
        self.assertEqual(self.invoice.customercode, "CUST001")
        self.assertEqual(self.invoice.buyer_name, "Buyer Corp")

    def test_get_formatted_sysdate(self):
        self.assertEqual(self.invoice.get_formatted_sysdate(), "26-10-2023")

    def test_get_formatted_issue_date(self):
        self.assertEqual(self.invoice.get_formatted_issue_date(), "25-10-2023")

    def test_get_customer_category_description(self):
        self.assertEqual(self.invoice.get_customer_category_description(), "Consulting Services")

    def test_get_taxable_services_description(self):
        self.assertEqual(self.invoice.get_taxable_services_description(), "IT Services")

    def test_get_work_order_numbers(self):
        self.assertEqual(self.invoice.get_work_order_numbers(), "WO-2023-001, WO-2023-002")

    def test_get_po_date(self):
        self.assertEqual(self.invoice.get_po_date(), "10-05-2023")

    def test_get_invoice_no_with_fin_year(self):
        self.assertEqual(self.invoice.get_invoice_no_with_fin_year(), "INV-001/2324")

    def test_get_buyer_full_address(self):
        expected_address = "101 Buyer St, Los Angeles, California, USA."
        self.assertEqual(self.invoice.get_buyer_full_address(), expected_address)

    def test_get_consignee_full_address(self):
        expected_address = "202 Consignee Ave, Los Angeles, California, USA."
        self.assertEqual(self.invoice.get_consignee_full_address(), expected_address)

    def test_get_company_full_address(self):
        expected_address_parts = [
            "123 Co Rd, Los Angeles, California, USA.",
            "PIN No.-12345.",
            "Ph No.-111, Fax No.-222",
            "Email <EMAIL>"
        ]
        expected_address = "\n".join(expected_address_parts)
        self.assertEqual(self.invoice.get_company_full_address(), expected_address)

    def test_get_issue_date_in_words(self):
        self.assertEqual(self.invoice.get_issue_date_in_words(), "Wednesday, 25 October 2023")

    def test_get_issue_time_in_words(self):
        self.assertEqual(self.invoice.get_issue_time_in_words(), "10 00 A.M.")


class ServiceTaxInvoiceViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all related models first
        cls.company = Company.objects.create(compid=1, regdaddress="123 Co Rd", regdcity=101, regdstate=201, regdcountry=301, regdpincode="12345", regdcontactno="111", regdfaxno="222", regdemail="<EMAIL>")
        cls.financial_year = FinancialYear.objects.create(finyearid=1, compid=1, finyearfrom=timezone.datetime(2023, 4, 1), finyearto=timezone.datetime(2024, 3, 31))
        cls.service_category = ServiceCategory.objects.create(id=1, description="Consulting Services")
        cls.taxable_service = TaxableService.objects.create(id=1, description="IT Services")
        cls.work_order1 = WorkOrder.objects.create(id=1, wono="WO-2023-001", compid=1)
        cls.work_order2 = WorkOrder.objects.create(id=2, wono="WO-2023-002", compid=1)
        cls.purchase_order = PurchaseOrder.objects.create(poid=1, sysdate=timezone.datetime(2023, 5, 10), compid=1)
        cls.country = Country.objects.create(cid=301, countryname="USA")
        cls.state = State.objects.create(sid=201, statename="California")
        cls.city = City.objects.create(cityid=101, cityname="Los Angeles")

        # Create main ServiceTaxInvoice object
        cls.invoice = ServiceTaxInvoice.objects.create(
            id=1,
            sysdate=timezone.datetime(2023, 10, 26, 15, 30, 0),
            invoiceno="INV-001",
            pono="PO-123",
            wono="1,2", # Comma separated IDs
            dateofissueinvoice=timezone.datetime(2023, 10, 25, 10, 0, 0),
            compid=cls.company.compid,
            timeofissueinvoice=time(10, 0, 0),
            dutyrate="10%",
            customercode="CUST001",
            customercategory=cls.service_category.id,
            buyer_name="Buyer Corp",
            buyer_cotper="John Doe",
            buyer_ph="123-456-7890",
            buyer_email="<EMAIL>",
            buyer_ecc="ECC123",
            buyer_tin="TIN123",
            buyer_mob="987-654-3210",
            buyer_fax="FAX123",
            buyer_vat="VAT123",
            buyer_add="101 Buyer St",
            buyer_country=cls.country.cid,
            buyer_state=cls.state.sid,
            buyer_city=cls.city.cityid,
            cong_name="Consignee Co",
            cong_cotper="Jane Smith",
            cong_ph="098-765-4321",
            cong_email="<EMAIL>",
            cong_ecc="ECC456",
            cong_tin="TIN456",
            cong_mob="012-345-6789",
            cong_fax="FAX456",
            cong_vat="VAT456",
            cong_add="202 Consignee Ave",
            cong_country=cls.country.cid,
            cong_state=cls.state.sid,
            cong_city=cls.city.cityid,
            addtype=1,
            addamt=100.0,
            deductiontype=2,
            deduction=50.0,
            servicetax=18,
            taxableservices=cls.taxable_service.id,
            poid=cls.purchase_order.poid,
            finyearid=cls.financial_year.finyearid
        )


    def setUp(self):
        self.client = Client()

    def test_list_view(self):
        response = self.client.get(reverse('servicetaxinvoice_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/servicetaxinvoice/list.html')
        self.assertIn('servicetaxinvoices', response.context)
        self.assertContains(response, 'Add New Service Tax Invoice')

    def test_table_partial_view(self):
        response = self.client.get(reverse('servicetaxinvoice_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/servicetaxinvoice/_servicetaxinvoice_table.html')
        self.assertIn('servicetaxinvoices', response.context)
        self.assertContains(response, self.invoice.invoiceno) # Check if invoice is listed

    def test_detail_view(self):
        response = self.client.get(reverse('servicetaxinvoice_detail', args=[self.invoice.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/servicetaxinvoice/detail.html')
        self.assertIn('invoice', response.context)
        self.assertContains(response, self.invoice.invoiceno)
        self.assertContains(response, self.invoice.get_company_full_address().splitlines()[0]) # Check a part of complex address

    def test_create_view_get(self):
        response = self.client.get(reverse('servicetaxinvoice_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/servicetaxinvoice/form.html')
        self.assertIn('form', response.context)

    def test_create_view_post_success(self):
        new_id = ServiceTaxInvoice.objects.count() + 1
        data = {
            'id': new_id,
            'sysdate': timezone.datetime(2024, 1, 1, 12, 0, 0).isoformat(),
            'invoiceno': 'INV-002',
            'pono': 'PO-456',
            'wono': '1,2',
            'dateofissueinvoice': timezone.datetime(2024, 1, 1, 12, 0, 0).isoformat(),
            'compid': self.company.compid,
            'timeofissueinvoice': '12:00:00',
            'dutyrate': '5%',
            'customercode': 'CUST002',
            'customercategory': self.service_category.id,
            'buyer_name': 'New Buyer',
            'buyer_cotper': 'Contact New',
            'buyer_ph': '123', 'buyer_email': '<EMAIL>', 'buyer_ecc': 'NEW', 'buyer_tin': 'NEW', 'buyer_mob': 'NEW', 'buyer_fax': 'NEW', 'buyer_vat': 'NEW',
            'buyer_add': '101 New Rd', 'buyer_country': self.country.cid, 'buyer_state': self.state.sid, 'buyer_city': self.city.cityid,
            'cong_name': 'New Cong',
            'cong_cotper': 'Contact Consignee',
            'cong_ph': '123', 'cong_email': '<EMAIL>', 'cong_ecc': 'NEWC', 'cong_tin': 'NEWC', 'cong_mob': 'NEWC', 'cong_fax': 'NEWC', 'cong_vat': 'NEWC',
            'cong_add': '202 New Rd', 'cong_country': self.country.cid, 'cong_state': self.state.sid, 'cong_city': self.city.cityid,
            'addtype': 1, 'addamt': 200.0, 'deductiontype': 1, 'deduction': 10.0,
            'servicetax': 10, 'taxableservices': self.taxable_service.id,
            'poid': self.purchase_order.poid, 'finyearid': self.financial_year.finyearid
        }
        response = self.client.post(reverse('servicetaxinvoice_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertTrue(ServiceTaxInvoice.objects.filter(invoiceno='INV-002').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshServiceTaxInvoiceList')

    def test_create_view_post_invalid(self):
        data = {
            'invoiceno': '', # Invalid data
            'customercode': 'CUST003'
        }
        response = self.client.post(reverse('servicetaxinvoice_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-renders with errors
        self.assertContains(response, 'This field is required') # Check for error message
        self.assertFalse(ServiceTaxInvoice.objects.filter(customercode='CUST003').exists())

    def test_update_view_get(self):
        response = self.client.get(reverse('servicetaxinvoice_edit', args=[self.invoice.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/servicetaxinvoice/form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.invoice)

    def test_update_view_post_success(self):
        updated_invoiceno = "INV-001-UPDATED"
        data = {
            'id': self.invoice.id, # Must include PK for update if not auto-filled
            'sysdate': self.invoice.sysdate.isoformat(),
            'invoiceno': updated_invoiceno,
            'pono': self.invoice.pono,
            'wono': self.invoice.wono,
            'dateofissueinvoice': self.invoice.dateofissueinvoice.isoformat(),
            'compid': self.invoice.compid,
            'timeofissueinvoice': self.invoice.timeofissueinvoice.strftime('%H:%M:%S'),
            'dutyrate': self.invoice.dutyrate,
            'customercode': self.invoice.customercode,
            'customercategory': self.invoice.customercategory,
            'buyer_name': self.invoice.buyer_name,
            'buyer_cotper': self.invoice.buyer_cotper,
            'buyer_ph': self.invoice.buyer_ph, 'buyer_email': self.invoice.buyer_email, 'buyer_ecc': self.invoice.buyer_ecc, 'buyer_tin': self.invoice.buyer_tin, 'buyer_mob': self.invoice.buyer_mob, 'buyer_fax': self.invoice.buyer_fax, 'buyer_vat': self.invoice.buyer_vat,
            'buyer_add': self.invoice.buyer_add, 'buyer_country': self.invoice.buyer_country, 'buyer_state': self.invoice.buyer_state, 'buyer_city': self.invoice.buyer_city,
            'cong_name': self.invoice.cong_name,
            'cong_cotper': self.invoice.cong_cotper,
            'cong_ph': self.invoice.cong_ph, 'cong_email': self.invoice.cong_email, 'cong_ecc': self.invoice.cong_ecc, 'cong_tin': self.invoice.cong_tin, 'cong_mob': self.invoice.cong_mob, 'cong_fax': self.invoice.cong_fax, 'cong_vat': self.invoice.cong_vat,
            'cong_add': self.invoice.cong_add, 'cong_country': self.invoice.cong_country, 'cong_state': self.invoice.cong_state, 'cong_city': self.invoice.cong_city,
            'addtype': self.invoice.addtype, 'addamt': self.invoice.addamt, 'deductiontype': self.invoice.deductiontype, 'deduction': self.invoice.deduction,
            'servicetax': self.invoice.servicetax, 'taxableservices': self.invoice.taxableservices,
            'poid': self.invoice.poid, 'finyearid': self.invoice.finyearid
        }
        response = self.client.post(reverse('servicetaxinvoice_edit', args=[self.invoice.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.invoice.refresh_from_db()
        self.assertEqual(self.invoice.invoiceno, updated_invoiceno)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshServiceTaxInvoiceList')


    def test_delete_view_get(self):
        response = self.client.get(reverse('servicetaxinvoice_delete', args=[self.invoice.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/servicetaxinvoice/confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.invoice)

    def test_delete_view_post_success(self):
        invoice_count = ServiceTaxInvoice.objects.count()
        response = self.client.post(reverse('servicetaxinvoice_delete', args=[self.invoice.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(ServiceTaxInvoice.objects.count(), invoice_count - 1)
        self.assertFalse(ServiceTaxInvoice.objects.filter(pk=self.invoice.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshServiceTaxInvoiceList')

```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX:** All CRUD operations (add, edit, delete) use HTMX to load forms and confirmation dialogues into a modal, without full page reloads. The list view is dynamically refreshed after any successful CRUD operation via `HX-Trigger`.
*   **Alpine.js:** Used for simple UI state management, such as controlling the visibility of the modal. The `hidden` class is toggled based on the `is-active` class.
*   **DataTables:** Integrated into the `_servicetaxinvoice_table.html` partial for client-side sorting, searching, and pagination, providing a rich interactive list experience.
*   **Modal Management:** The modal is opened by adding `.is-active` via `_` (hyperscript, part of Alpine.js/HTMX ecosystem) and closed by removing it on cancel or successful form submission (via HTMX `hx-on::after-request` or Alpine's `on click` handlers).

## Final Notes

*   **Placeholders:** All `[PLACEHOLDER]` values have been replaced with concrete names and examples derived from the ASP.NET code.
*   **DRY Templates:** Templates extend `core/base.html` (assumed to exist) and utilize partials (`_servicetaxinvoice_table.html`, `_servicetaxinvoice_form.html`, `_servicetaxinvoice_confirm_delete.html`) to avoid code repetition and facilitate HTMX-driven updates.
*   **Fat Model, Thin View:** Complex data retrieval, formatting, and address generation logic from the C# code-behind has been moved to methods within the `ServiceTaxInvoice` model. Views are concise, primarily handling HTTP requests and delegating business logic to models.
*   **Comprehensive Tests:** Unit tests cover model methods and their logic, while integration tests validate the functionality of views and HTMX interactions, ensuring a robust and well-tested migration.
*   **Crystal Reports Replacement:** The original Crystal Report functionality is re-imagined as a `ServiceTaxInvoiceDetailView` that renders a structured HTML page with all the invoice details, which can then be printed directly from the browser. This provides functional equivalence using modern web standards.
*   **Styling:** Tailwind CSS classes are applied directly within the templates for rapid and consistent styling.
*   **Database Connection:** Remember to configure your Django `settings.py` to connect to your existing SQL Server database (e.g., using `django-mssql-b` or `pyodbc` for database backend).
*   **ID Field:** For models with `managed=False` and a primary key that is not auto-incrementing in Django (like `Id` for `ServiceTaxInvoice`), you might need to manually ensure `id` values are provided during creation or that the database handles it. The example assumes `id` is part of the `fields` list in forms, which might require `id` to be explicitly passed or generated. For existing tables, Django usually picks up the PK automatically. In this case, `id = models.IntegerField(db_column='Id', primary_key=True)` explicitly defines it.