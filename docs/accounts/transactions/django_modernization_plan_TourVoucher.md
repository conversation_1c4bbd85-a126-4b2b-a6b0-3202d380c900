This comprehensive Django modernization plan outlines the strategic transition from your legacy ASP.NET application to a modern, efficient Django-based solution. Our approach prioritizes AI-assisted automation, ensuring a systematic and streamlined migration process while focusing on business benefits and clear, non-technical instructions.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
The ASP.NET code primarily interacts with `tblACC_TourIntimation_Master`. It also performs lookups from several related tables for display purposes and an exclusion filter from `tblACC_TourVoucher_Master`.

**Identified Tables and Key Columns:**

*   **Main Table:** `tblACC_TourIntimation_Master`
    *   `Id` (Primary Key, integer)
    *   `FinYearId` (Foreign Key to `tblFinancial_master`, integer)
    *   `EmpId` (Foreign Key to `tblHR_OfficeStaff`, string/varchar in original, will map to appropriate Django field for related model)
    *   `TINo` (String)
    *   `WONo` (String)
    *   `BGGroupId` (Foreign Key to `BusinessGroup`, integer)
    *   `ProjectName` (String)
    *   `PlaceOfTourCity` (Foreign Key to `tblCity`, integer)
    *   `PlaceOfTourState` (Foreign Key to `tblState`, integer)
    *   `PlaceOfTourCountry` (Foreign Key to `tblCountry`, integer)
    *   `TourStartDate` (Date/DateTime)
    *   `TourEndDate` (Date/DateTime)
    *   `CompId` (Integer, used for company filtering)

*   **Related Lookup Tables:**
    *   `tblFinancial_master`: `FinYearId`, `FinYear`
    *   `tblHR_OfficeStaff`: `EmpId`, `Title`, `EmployeeName`
    *   `BusinessGroup`: `Id`, `Symbol`
    *   `tblCity`: `CityId`, `CityName`
    *   `tblState`: `SId`, `StateName`
    *   `tblCountry`: `CId`, `CountryName`
    *   `tblACC_TourVoucher_Master`: `Id`, `TIMId` (Foreign Key to `tblACC_TourIntimation_Master.Id`)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
The ASP.NET page primarily functions as a **Read** (List) and **Search/Filter** interface for Tour Intimation records. It does not directly provide Create, Update, or Delete functionality for Tour Intimations on this specific page. However, it links to a "details" page (`TourVoucher_Details.aspx`) which implies further actions (likely creating or viewing a Tour Voucher related to the selected Tour Intimation).

*   **Create:** Not directly on this page.
*   **Read:**
    *   Fetches data from `tblACC_TourIntimation_Master`.
    *   Performs complex lookups and joins with `tblFinancial_master`, `tblHR_OfficeStaff`, `BusinessGroup`, `tblCity`, `tblState`, `tblCountry` to enrich display data.
    *   Filters out records that already have a corresponding entry in `tblACC_TourVoucher_Master`.
    *   Applies filters based on `TINo`, `EmployeeName` (using `EmpId`), `WONo`, `BGGroup` (using `BGGroupId`), and `ProjectName`.
    *   Handles pagination for the displayed list.
*   **Update:** Not directly on this page.
*   **Delete:** Not directly on this page.
*   **Validation Logic:** None explicitly shown for data entry on this page, but `GetCompletionList` for employee name provides auto-completion.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The user interface is a classic search-and-list pattern, typical in many ERP systems.

*   **Search/Filter Controls:**
    *   `DrpField` (DropDownList): Selects the search criterion (TI No, Employee Name, WO No, BG Group, Project Name). This will be a Django `forms.ChoiceField` or `forms.Select`.
    *   `TxtMrs` (TextBox): Text input for TI No, WO No, Project Name search.
    *   `TxtEmpName` (TextBox) with `AutoCompleteExtender`: Text input for Employee Name search with auto-completion. This will be a standard `forms.TextInput` with HTMX for autocomplete.
    *   `drpGroup` (DropDownList): Dropdown for Business Group search. This will be a `forms.ModelChoiceField`.
    *   `Button1` (Button): Triggers the search.
*   **Data Display:**
    *   `GridView2` (GridView): Displays the filtered list of Tour Intimations. This will be replaced by a standard HTML `<table>` enhanced with DataTables.
    *   `LinkButton` for `TINo`: This column will be an `<a>` tag in Django, linking to the `TourVoucher_Details` page (which will become a new Django view for Tour Vouchers).
*   **Dynamic UI:**
    *   The `DrpField_SelectedIndexChanged` and `AutoPostBack` functionality (showing/hiding `TxtMrs`, `TxtEmpName`, `drpGroup` based on `DrpField` selection) will be managed using Alpine.js or HTMX partial swaps for the search form. Alpine.js is ideal for this client-side UI state.
    *   The `AutoCompleteExtender` will be handled by a dedicated HTMX endpoint that returns JSON for the employee search.

### Step 4: Generate Django Code

We will create a new Django application, let's call it `accounts`, to house this functionality.

#### 4.1 Models

**Task:** Create Django models based on the database schema.

**Instructions:**
We define models for the core `TourIntimation` table and the necessary lookup tables with `managed = False` and `db_table` to connect to the existing database structure. The `TourIntimationManager` will encapsulate the complex filtering and data enrichment logic.

```python
# accounts/models.py
from django.db import models
from django.db.models import F, Q

class FinancialYear(models.Model):
    finyear_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    finyear = models.CharField(db_column='FinYear', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.finyear

class OfficeStaff(models.Model):
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50) # Assuming EmpId is varchar
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')
    finyear_id = models.IntegerField(db_column='FinYearId') # Assuming this is not a foreign key here for simplicity

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title or ''} {self.employee_name} [{self.emp_id}]".strip()

class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=100)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

class City(models.Model):
    city_id = models.IntegerField(db_column='CityId', primary_key=True)
    city_name = models.CharField(db_column='CityName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblCity'
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.city_name

class State(models.Model):
    s_id = models.IntegerField(db_column='SId', primary_key=True)
    state_name = models.CharField(db_column='StateName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblState'
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.state_name

class Country(models.Model):
    c_id = models.IntegerField(db_column='CId', primary_key=True)
    country_name = models.CharField(db_column='CountryName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblCountry'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.country_name

class TourVoucher(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    tim_id = models.IntegerField(db_column='TIMId') # This is the FK to TourIntimation.Id

    class Meta:
        managed = False
        db_table = 'tblACC_TourVoucher_Master'
        verbose_name = 'Tour Voucher'
        verbose_name_plural = 'Tour Vouchers'

    def __str__(self):
        return f"Voucher for TIMId: {self.tim_id}"


class TourIntimationManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().select_related(
            'finyear_obj', 'emp_obj', 'bg_group_obj',
            'place_of_tour_city_obj', 'place_of_tour_state_obj', 'place_of_tour_country_obj'
        )

    def search_and_filter(self, comp_id, fin_year_id, search_field, search_text, employee_id, bg_group_id):
        qs = self.get_queryset().filter(
            comp_id=comp_id,
            finyear_id__lte=fin_year_id # Based on original FinYearId <= FyId
        )

        # Exclude records that already have a Tour Voucher
        # Assuming TourVoucher.TIMId maps to TourIntimation.id
        qs = qs.exclude(id__in=TourVoucher.objects.values_list('tim_id', flat=True))

        if search_field == '0' and search_text: # TI No
            qs = qs.filter(ti_no__icontains=search_text)
        elif search_field == '1' and employee_id: # Employee Name
            qs = qs.filter(emp_id=employee_id)
        elif search_field == '2' and search_text: # WO No
            qs = qs.filter(wo_no__icontains=search_text)
        elif search_field == '3' and bg_group_id: # BG Group
            qs = qs.filter(bg_group_id=bg_group_id)
        elif search_field == '4' and search_text: # Project Name
            qs = qs.filter(project_name__icontains=search_text)

        return qs.order_by(F('id').desc())

class TourIntimation(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    finyear_id = models.IntegerField(db_column='FinYearId')
    emp_id = models.CharField(db_column='EmpId', max_length=50) # Assuming EmpId is varchar in DB

    ti_no = models.CharField(db_column='TINo', max_length=100)
    wo_no = models.CharField(db_column='WONo', max_length=100, blank=True, null=True)
    bg_group_id = models.IntegerField(db_column='BGGroupId')
    project_name = models.CharField(db_column='ProjectName', max_length=255, blank=True, null=True)
    place_of_tour_city = models.IntegerField(db_column='PlaceOfTourCity', blank=True, null=True)
    place_of_tour_state = models.IntegerField(db_column='PlaceOfTourState', blank=True, null=True)
    place_of_tour_country = models.IntegerField(db_column='PlaceOfTourCountry', blank=True, null=True)
    tour_start_date = models.DateTimeField(db_column='TourStartDate')
    tour_end_date = models.DateTimeField(db_column='TourEndDate')
    comp_id = models.IntegerField(db_column='CompId')

    # Define explicit ForeignKey relationships for easier access and select_related
    finyear_obj = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='tour_intimations', blank=True, null=True)
    emp_obj = models.ForeignKey(OfficeStaff, on_delete=models.DO_NOTHING, db_column='EmpId', related_name='tour_intimations', blank=True, null=True)
    bg_group_obj = models.ForeignKey(BusinessGroup, on_delete=models.DO_NOTHING, db_column='BGGroupId', related_name='tour_intimations', blank=True, null=True)
    place_of_tour_city_obj = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='PlaceOfTourCity', related_name='tour_intimations_city', blank=True, null=True)
    place_of_tour_state_obj = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='PlaceOfTourState', related_name='tour_intimations_state', blank=True, null=True)
    place_of_tour_country_obj = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='PlaceOfTourCountry', related_name='tour_intimations_country', blank=True, null=True)

    objects = TourIntimationManager()

    class Meta:
        managed = False
        db_table = 'tblACC_TourIntimation_Master'
        verbose_name = 'Tour Intimation'
        verbose_name_plural = 'Tour Intimations'

    def __str__(self):
        return self.ti_no

    @property
    def display_fin_year(self):
        return self.finyear_obj.finyear if self.finyear_obj else 'N/A'

    @property
    def display_employee_name(self):
        return self.emp_obj.display_name if self.emp_obj else 'N/A'

    @property
    def display_bg_group(self):
        # Original logic: If BGGroupId is "1", then WONo is shown, BGGroup is "NA". Otherwise, BGGroup is looked up, WONo is "NA".
        # This implies BGGroupId=1 is a special case for 'WO' vs 'BG Group'.
        if self.bg_group_id == 1:
            return "NA" # This implies BGGroup is not applicable for this type of record
        return self.bg_group_obj.symbol if self.bg_group_obj else 'N/A'

    @property
    def display_wo_no(self):
        # Original logic: If BGGroupId is "1", then WONo is shown. Otherwise, WONo is "NA".
        if self.bg_group_id == 1:
            return self.wo_no or 'N/A'
        return "NA"

    @property
    def display_place_of_tour(self):
        parts = []
        if self.place_of_tour_country_obj:
            parts.append(self.place_of_tour_country_obj.country_name)
        if self.place_of_tour_state_obj:
            parts.append(self.place_of_tour_state_obj.state_name)
        if self.place_of_tour_city_obj:
            parts.append(self.place_of_tour_city_obj.city_name)
        return ", ".join(parts) if parts else 'N/A'

    @property
    def display_tour_start_date(self):
        return self.tour_start_date.strftime('%d/%m/%Y') if self.tour_start_date else 'N/A'

    @property
    def display_tour_end_date(self):
        return self.tour_end_date.strftime('%d/%m/%Y') if self.tour_end_date else 'N/A'

    # Note: No direct Create/Update/Delete methods here as the original ASP.NET page is for listing.
    # If full CRUD was needed for TourIntimation itself, these methods would be here.
    # The existing code indicates navigation to another page for 'TourVoucher' creation/details.

```

#### 4.2 Forms

**Task:** Define a Django form for user input (search).

**Instructions:**
We'll create a dedicated search form to handle the filtering logic. Standard CRUD forms for `TourIntimation` are also provided as requested.

```python
# accounts/forms.py
from django import forms
from .models import TourIntimation, BusinessGroup, OfficeStaff

class TourIntimationSearchForm(forms.Form):
    SEARCH_CHOICES = [
        ('Select', 'Select'),
        ('0', 'TI No'),
        ('1', 'Employee Name'),
        ('2', 'WO No'),
        ('3', 'BG Group'),
        ('4', 'Project Name'),
    ]

    drp_field = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        widget=forms.Select(attrs={'class': 'box3 w-48', 'x-model': 'searchField', 'x-on:change': 'resetSearchInputs'})
    )
    txt_mrs = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-48',
            'placeholder': 'Enter search text',
            'x-show': "['0', '2', '4'].includes(searchField)",
            'x-model': 'searchText'
        })
    )
    txt_emp_name = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-64',
            'placeholder': 'Enter employee name',
            'x-show': "searchField === '1'",
            'x-model': 'employeeName',
            'hx-get': '/accounts/api/employee-autocomplete/',
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#employee-suggestions',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off'
        })
    )
    drp_group = forms.ModelChoiceField(
        queryset=BusinessGroup.objects.all(),
        to_field_name='id', # To use ID as value
        required=False,
        empty_label="Select Business Group",
        widget=forms.Select(attrs={
            'class': 'box3 w-48',
            'x-show': "searchField === '3'",
            'x-model': 'bgGroupId'
        })
    )

    # Hidden field to store employee ID from autocomplete
    employee_id_hidden = forms.CharField(
        widget=forms.HiddenInput(attrs={'x-model': 'employeeId'}),
        required=False
    )

    def clean(self):
        cleaned_data = super().clean()
        drp_field = cleaned_data.get('drp_field')
        txt_mrs = cleaned_data.get('txt_mrs')
        txt_emp_name = cleaned_data.get('txt_emp_name')
        drp_group = cleaned_data.get('drp_group')
        employee_id_hidden = cleaned_data.get('employee_id_hidden')

        if drp_field == '0' and not txt_mrs:
            self.add_error('txt_mrs', 'TI No is required for search.')
        elif drp_field == '1' and not employee_id_hidden:
            self.add_error('txt_emp_name', 'Employee Name is required for search.')
        elif drp_field == '2' and not txt_mrs:
            self.add_error('txt_mrs', 'WO No is required for search.')
        elif drp_field == '3' and not drp_group:
            self.add_error('drp_group', 'Business Group is required for search.')
        elif drp_field == '4' and not txt_mrs:
            self.add_error('txt_mrs', 'Project Name is required for search.')
        
        return cleaned_data

class TourIntimationForm(forms.ModelForm):
    # This form is a placeholder for potential future CRUD operations on TourIntimation itself,
    # as the original page is only for listing/searching.
    class Meta:
        model = TourIntimation
        fields = [
            'finyear_id', 'emp_id', 'ti_no', 'wo_no', 'bg_group_id',
            'project_name', 'place_of_tour_city', 'place_of_tour_state',
            'place_of_tour_country', 'tour_start_date', 'tour_end_date'
        ]
        widgets = {
            'finyear_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'emp_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'ti_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'wo_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bg_group_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'project_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'place_of_tour_city': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'place_of_tour_state': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'place_of_tour_country': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'tour_start_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'tour_end_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
        }
```

#### 4.3 Views

**Task:** Implement Read (List/Search) operations using CBVs, and generic CRUD views for completeness.

**Instructions:**
The `TourIntimationListView` will manage the search form and display the results. A `TourIntimationTablePartialView` is introduced for HTMX to swap the table content. An `EmployeeAutocompleteView` handles the AJAX suggestions. Generic `CreateView`, `UpdateView`, and `DeleteView` are included for `TourIntimation` as per the template, though not directly used by the original ASP.NET page's functionality.

```python
# accounts/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from django.db.models import F # Import F for ordering by field
import re # For parsing employee ID from string

from .models import TourIntimation, OfficeStaff
from .forms import TourIntimationSearchForm, TourIntimationForm

class TourIntimationListView(ListView):
    model = TourIntimation
    template_name = 'accounts/tourintimation/list.html'
    context_object_name = 'tour_intimations'
    # Default initial data for the form
    initial = {
        'search_field': 'Select',
        'txt_mrs': '',
        'txt_emp_name': '',
        'drp_group': '',
        'employee_id_hidden': ''
    }

    def get_queryset(self):
        # Default empty queryset if no search parameters
        return TourIntimation.objects.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass the form to the template
        context['search_form'] = TourIntimationSearchForm(self.request.GET or self.initial)
        return context

# HTMX partial view for the table content after search or initial load
class TourIntimationTablePartialView(ListView):
    model = TourIntimation
    template_name = 'accounts/tourintimation/_tourintimation_table.html'
    context_object_name = 'tour_intimations'

    def get_queryset(self):
        comp_id = self.request.session.get('compid', 1) # Default to 1 if not in session
        fin_year_id = self.request.session.get('finyear', 2023) # Default to 2023 if not in session

        form = TourIntimationSearchForm(self.request.GET)
        if form.is_valid():
            search_field = form.cleaned_data.get('drp_field', 'Select')
            search_text = form.cleaned_data.get('txt_mrs')
            employee_name_with_id = form.cleaned_data.get('txt_emp_name')
            bg_group_obj = form.cleaned_data.get('drp_group')
            employee_id = form.cleaned_data.get('employee_id_hidden')

            # Extract employee_id from employee_name_with_id if employee_id_hidden is not set
            if search_field == '1' and employee_name_with_id and not employee_id:
                match = re.search(r'\[(.*?)\]', employee_name_with_id)
                if match:
                    employee_id = match.group(1)

            bg_group_id = bg_group_obj.id if bg_group_obj else None

            # Pass all relevant search parameters to the manager method
            queryset = TourIntimation.objects.search_and_filter(
                comp_id=comp_id,
                fin_year_id=fin_year_id,
                search_field=search_field,
                search_text=search_text,
                employee_id=employee_id,
                bg_group_id=bg_group_id
            )
            return queryset
        return TourIntimation.objects.none() # Return empty if form is not valid initially


class EmployeeAutocompleteView(View):
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        if query:
            comp_id = request.session.get('compid', 1) # Default to 1 if not in session
            # Assuming EmpId is char/varchar and EmployeeName is text
            employees = OfficeStaff.objects.filter(
                comp_id=comp_id,
                employee_name__icontains=query
            ).values('emp_id', 'employee_name')[:10] # Limit results as in original code

            # Format: "Employee Name [EmpId]"
            results = [f"{e['employee_name']} [{e['emp_id']}]" for e in employees]
            return render(request, 'accounts/tourintimation/_employee_suggestions.html', {'suggestions': results})
        return HttpResponse('') # Return empty if no query


# --- Generic CRUD Views for TourIntimation (as requested by template, though not used by original page) ---

class TourIntimationCreateView(CreateView):
    model = TourIntimation
    form_class = TourIntimationForm
    template_name = 'accounts/tourintimation/form.html'
    success_url = reverse_lazy('tourintimation_list')

    def form_valid(self, form):
        # Set default values for CompId and FinYearId from session if not already set by form
        if not form.instance.comp_id:
            form.instance.comp_id = self.request.session.get('compid', 1)
        if not form.instance.finyear_id:
            form.instance.finyear_id = self.request.session.get('finyear', 2023)

        response = super().form_valid(form)
        messages.success(self.request, 'Tour Intimation added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshTourIntimationList'
                }
            )
        return response

class TourIntimationUpdateView(UpdateView):
    model = TourIntimation
    form_class = TourIntimationForm
    template_name = 'accounts/tourintimation/form.html'
    success_url = reverse_lazy('tourintimation_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Tour Intimation updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshTourIntimationList'
                }
            )
        return response

class TourIntimationDeleteView(DeleteView):
    model = TourIntimation
    template_name = 'accounts/tourintimation/confirm_delete.html'
    success_url = reverse_lazy('tourintimation_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Tour Intimation deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshTourIntimationList'
                }
            )
        return response
```

#### 4.4 Templates

**Task:** Create templates for each view.

**Instructions:**
The main list page will use Alpine.js for dynamic form field visibility and HTMX to load the table dynamically. DataTables will handle the client-side table features.

```html
{# accounts/templates/accounts/tourintimation/list.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Tour Intimations</h2>
        <button
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'tourintimation_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Tour Intimation (Example CRUD)
        </button>
    </div>

    {# Search Form Section #}
    <div class="bg-white p-6 rounded-lg shadow-lg mb-6" x-data="{ searchField: '{{ search_form.drp_field.value|default:'Select' }}', searchText: '{{ search_form.txt_mrs.value|default:'' }}', employeeName: '{{ search_form.txt_emp_name.value|default:'' }}', bgGroupId: '{{ search_form.drp_group.value|default:'' }}', employeeId: '{{ search_form.employee_id_hidden.value|default:'' }}',
        resetSearchInputs() {
            this.searchText = '';
            this.employeeName = '';
            this.bgGroupId = '';
            this.employeeId = '';
        }}">
        <form hx-get="{% url 'tourintimation_table' %}"
              hx-target="#tourintimationTable-container"
              hx-swap="innerHTML"
              hx-trigger="submit, drp_group.change"> {# drp_group.change added for immediate search #}
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                <div>
                    <label for="{{ search_form.drp_field.id_for_label }}" class="block text-sm font-medium text-gray-700">Search Field:</label>
                    {{ search_form.drp_field }}
                </div>
                <div>
                    <label for="{{ search_form.txt_mrs.id_for_label }}" class="block text-sm font-medium text-gray-700">Search Text:</label>
                    {{ search_form.txt_mrs }}
                </div>
                <div class="relative">
                    <label for="{{ search_form.txt_emp_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Employee Name:</label>
                    {{ search_form.txt_emp_name }}
                    <div id="employee-suggestions" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1"
                         x-show="employeeName.length > 0 && searchField === '1'"
                         @click.away="employeeName = ''"> {# Clear suggestions when clicking away #}
                        {# Autocomplete suggestions will be loaded here via HTMX #}
                    </div>
                    {{ search_form.employee_id_hidden }} {# Hidden field for employee ID #}
                </div>
                <div>
                    <label for="{{ search_form.drp_group.id_for_label }}" class="block text-sm font-medium text-gray-700">Business Group:</label>
                    {{ search_form.drp_group }}
                </div>
                <div>
                    <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded w-full">Search</button>
                </div>
            </div>
            {% if search_form.errors %}
                <div class="text-red-500 text-sm mt-4">
                    {% for field in search_form %}
                        {% for error in field.errors %}
                            <p>{{ field.label }}: {{ error }}</p>
                        {% endfor %}
                    {% endfor %}
                    {% for error in search_form.non_field_errors %}
                        <p>{{ error }}</p>
                    {% endfor %}
                </div>
            {% endif %}
        </form>
    </div>

    <div id="tourintimationTable-container"
         hx-trigger="load, refreshTourIntimationList from:body"
         hx-get="{% url 'tourintimation_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading...</p>
        </div>
    </div>

    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
<script src="//cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for other global elements
    });

    // Handle clicks on autocomplete suggestions
    document.getElementById('employee-suggestions').addEventListener('click', function(event) {
        if (event.target.classList.contains('autocomplete-item')) {
            const selectedText = event.target.textContent;
            const employeeNameInput = document.getElementById('id_txt_emp_name');
            const employeeIdHiddenInput = document.getElementById('id_employee_id_hidden');

            employeeNameInput.value = selectedText;
            
            // Extract EmpId from "Employee Name [EmpId]"
            const match = selectedText.match(/\[(.*?)\]/);
            if (match && match[1]) {
                employeeIdHiddenInput.value = match[1];
            } else {
                employeeIdHiddenInput.value = '';
            }

            // Trigger htmx form submission to refresh table with selected employee
            htmx.trigger(document.querySelector('form[hx-get="{% url 'tourintimation_table' %}"]'), 'submit');
            
            // Hide suggestions
            employeeNameInput._x_data_employeeName = ''; // Directly update Alpine.js state for employeeName
            document.getElementById('employee-suggestions').innerHTML = ''; // Clear suggestions
        }
    });

    // Custom HTMX event listener for after swap to reinitialize DataTable
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'tourintimationTable-container') {
            const dataTable = $('#tourintimationTable');
            if (dataTable.length && !$.fn.DataTable.isDataTable(dataTable)) {
                 dataTable.DataTable({
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "destroy": true, // Allow reinitialization
                    "pagingType": "full_numbers"
                });
            }
        }
    });
</script>
{% endblock %}
```

```html
{# accounts/templates/accounts/tourintimation/_tourintimation_table.html #}
<table id="tourintimationTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr class="bg-gray-50">
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">TI No</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp Name</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BG Group</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project Name</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Place of Tour</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tour Start Date</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tour End Date</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in tour_intimations %}
        <tr class="hover:bg-gray-50">
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.display_fin_year }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                {# Link to Tour Voucher Details page #}
                <a href="/tourvoucher/details/{{ obj.id }}/" class="text-blue-600 hover:underline">{{ obj.ti_no }}</a>
            </td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.emp_obj.employee_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.display_wo_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.display_bg_group }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.project_name|default:'N/A' }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.display_place_of_tour }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.display_tour_start_date }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.display_tour_end_date }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'tourintimation_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'tourintimation_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="11" class="py-4 px-4 text-center text-lg text-maroon-600 font-semibold">
                No data to display !
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // DataTable initialization is handled by htmx:afterSwap event in list.html
    // This script block should be empty, as the global handler is sufficient.
    // If you need specific DataTable options, configure them in the list.html handler.
</script>
```

```html
{# accounts/templates/accounts/tourintimation/_employee_suggestions.html #}
{# Partial for HTMX-driven employee autocomplete suggestions #}
{% for suggestion in suggestions %}
    <div class="autocomplete-item py-2 px-3 cursor-pointer hover:bg-gray-100 border-b border-gray-200 last:border-b-0">
        {{ suggestion }}
    </div>
{% endfor %}
```

```html
{# accounts/templates/accounts/tourintimation/form.html #}
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Tour Intimation</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}

        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

```html
{# accounts/templates/accounts/tourintimation/confirm_delete.html #}
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="mb-4">Are you sure you want to delete Tour Intimation: <strong>{{ object.ti_no }}</strong>?</p>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
Create paths for the list view, the HTMX-loaded table partial, the employee autocomplete endpoint, and the generic CRUD views for `TourIntimation`.

```python
# accounts/urls.py
from django.urls import path
from .views import (
    TourIntimationListView, TourIntimationTablePartialView, EmployeeAutocompleteView,
    TourIntimationCreateView, TourIntimationUpdateView, TourIntimationDeleteView
)

urlpatterns = [
    path('tourintimation/', TourIntimationListView.as_view(), name='tourintimation_list'),
    path('tourintimation/table/', TourIntimationTablePartialView.as_view(), name='tourintimation_table'),
    path('api/employee-autocomplete/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),

    # Generic CRUD paths for TourIntimation (as per template)
    path('tourintimation/add/', TourIntimationCreateView.as_view(), name='tourintimation_add'),
    path('tourintimation/edit/<int:pk>/', TourIntimationUpdateView.as_view(), name='tourintimation_edit'),
    path('tourintimation/delete/<int:pk>/', TourIntimationDeleteView.as_view(), name='tourintimation_delete'),

    # Placeholder for TourVoucher Details page redirection
    # This assumes a separate 'tourvoucher' app or module will handle this.
    # For now, it's just a generic path that might link to another app.
    path('tourvoucher/details/<int:pk>/', View.as_view(), name='tourvoucher_details'), # Dummy view, replace with actual TourVoucher detail view
]

# In your project's main urls.py, include these:
# from django.urls import path, include
# urlpatterns = [
#     path('accounts/', include('accounts.urls')),
#     # ... other paths
# ]
```

#### 4.6 Tests

**Task:** Write tests for the model and views.

**Instructions:**
Include comprehensive unit tests for model methods and properties, and integration tests for all views, specifically covering HTMX interactions and search/filter logic.

```python
# accounts/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from .models import (
    TourIntimation, FinancialYear, OfficeStaff, BusinessGroup,
    City, State, Country, TourVoucher
)
from datetime import datetime
import pytz # For timezone-aware datetimes

class TourIntimationModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.fin_year = FinancialYear.objects.create(finyear_id=1, finyear='2023-24')
        cls.emp = OfficeStaff.objects.create(emp_id='E001', title='Mr.', employee_name='John Doe', comp_id=1, finyear_id=1)
        cls.bg_group_wo = BusinessGroup.objects.create(id=1, symbol='WO Group') # Special case for WONo
        cls.bg_group_other = BusinessGroup.objects.create(id=2, symbol='Other Group')
        cls.city = City.objects.create(city_id=1, city_name='Springfield')
        cls.state = State.objects.create(s_id=1, state_name='Illinois')
        cls.country = Country.objects.create(c_id=1, country_name='USA')

        cls.tour_intimation_wo = TourIntimation.objects.create(
            id=1, finyear_id=cls.fin_year.finyear_id, emp_id=cls.emp.emp_id, ti_no='TI-001',
            wo_no='WO-A1', bg_group_id=cls.bg_group_wo.id, project_name='Project X',
            place_of_tour_city=cls.city.city_id, place_of_tour_state=cls.state.s_id,
            place_of_tour_country=cls.country.c_id,
            tour_start_date=datetime(2023, 1, 10, tzinfo=pytz.UTC),
            tour_end_date=datetime(2023, 1, 15, tzinfo=pytz.UTC), comp_id=1
        )
        cls.tour_intimation_bg = TourIntimation.objects.create(
            id=2, finyear_id=cls.fin_year.finyear_id, emp_id=cls.emp.emp_id, ti_no='TI-002',
            wo_no='WO-B2', bg_group_id=cls.bg_group_other.id, project_name='Project Y',
            place_of_tour_city=cls.city.city_id, place_of_tour_state=cls.state.s_id,
            place_of_tour_country=cls.country.c_id,
            tour_start_date=datetime(2023, 2, 1, tzinfo=pytz.UTC),
            tour_end_date=datetime(2023, 2, 5, tzinfo=pytz.UTC), comp_id=1
        )
        cls.tour_intimation_existing_voucher = TourIntimation.objects.create(
            id=3, finyear_id=cls.fin_year.finyear_id, emp_id=cls.emp.emp_id, ti_no='TI-003',
            wo_no='WO-C3', bg_group_id=cls.bg_group_other.id, project_name='Project Z',
            place_of_tour_city=cls.city.city_id, place_of_tour_state=cls.state.s_id,
            place_of_tour_country=cls.country.c_id,
            tour_start_date=datetime(2023, 3, 1, tzinfo=pytz.UTC),
            tour_end_date=datetime(2023, 3, 5, tzinfo=pytz.UTC), comp_id=1
        )
        TourVoucher.objects.create(id=100, tim_id=cls.tour_intimation_existing_voucher.id)


    def test_tour_intimation_creation(self):
        obj = TourIntimation.objects.get(id=1)
        self.assertEqual(obj.ti_no, 'TI-001')
        self.assertEqual(obj.emp_id, 'E001')
        self.assertEqual(obj.tour_start_date.year, 2023)

    def test_display_properties(self):
        obj_wo = TourIntimation.objects.get(id=1)
        obj_bg = TourIntimation.objects.get(id=2)

        self.assertEqual(obj_wo.display_fin_year, '2023-24')
        self.assertEqual(obj_wo.display_employee_name, 'Mr. John Doe [E001]')
        self.assertEqual(obj_wo.display_wo_no, 'WO-A1')
        self.assertEqual(obj_wo.display_bg_group, 'NA') # Specific logic for BGGroupId=1

        self.assertEqual(obj_bg.display_wo_no, 'NA')
        self.assertEqual(obj_bg.display_bg_group, 'Other Group')

        self.assertEqual(obj_wo.display_place_of_tour, 'USA, Illinois, Springfield')
        self.assertEqual(obj_wo.display_tour_start_date, '10/01/2023')
        self.assertEqual(obj_wo.display_tour_end_date, '15/01/2023')

    def test_search_and_filter_all(self):
        # Default session values
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2023 # Used as FinYearId <= FyId
        session.save()

        # No search parameters should return all non-voucher records
        qs = TourIntimation.objects.search_and_filter(1, 2023, 'Select', None, None, None)
        self.assertEqual(qs.count(), 2) # TI-001, TI-002 (TI-003 has voucher)
        self.assertIn(self.tour_intimation_wo, qs)
        self.assertIn(self.tour_intimation_bg, qs)
        self.assertNotIn(self.tour_intimation_existing_voucher, qs)

    def test_search_and_filter_ti_no(self):
        qs = TourIntimation.objects.search_and_filter(1, 2023, '0', '001', None, None)
        self.assertEqual(qs.count(), 1)
        self.assertEqual(qs.first().ti_no, 'TI-001')

    def test_search_and_filter_employee_name(self):
        qs = TourIntimation.objects.search_and_filter(1, 2023, '1', None, 'E001', None)
        self.assertEqual(qs.count(), 2) # Both TI-001 and TI-002 are by E001
        self.assertIn(self.tour_intimation_wo, qs)
        self.assertIn(self.tour_intimation_bg, qs)

    def test_search_and_filter_wo_no(self):
        qs = TourIntimation.objects.search_and_filter(1, 2023, '2', 'A1', None, None)
        self.assertEqual(qs.count(), 1)
        self.assertEqual(qs.first().wo_no, 'WO-A1')

    def test_search_and_filter_bg_group(self):
        qs = TourIntimation.objects.search_and_filter(1, 2023, '3', None, None, self.bg_group_other.id)
        self.assertEqual(qs.count(), 1)
        self.assertEqual(qs.first().ti_no, 'TI-002') # This one uses 'Other Group'

    def test_search_and_filter_project_name(self):
        qs = TourIntimation.objects.search_and_filter(1, 2023, '4', 'Project Y', None, None)
        self.assertEqual(qs.count(), 1)
        self.assertEqual(qs.first().project_name, 'Project Y')

    def test_exclude_existing_vouchers(self):
        qs = TourIntimation.objects.search_and_filter(1, 2023, 'Select', None, None, None)
        self.assertNotIn(self.tour_intimation_existing_voucher, qs)


class TourIntimationViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.fin_year = FinancialYear.objects.create(finyear_id=1, finyear='2023-24')
        cls.emp = OfficeStaff.objects.create(emp_id='E001', title='Mr.', employee_name='John Doe', comp_id=1, finyear_id=1)
        cls.bg_group_wo = BusinessGroup.objects.create(id=1, symbol='WO Group')
        cls.bg_group_other = BusinessGroup.objects.create(id=2, symbol='Other Group')
        cls.city = City.objects.create(city_id=1, city_name='Springfield')
        cls.state = State.objects.create(s_id=1, state_name='Illinois')
        cls.country = Country.objects.create(c_id=1, country_name='USA')

        cls.tour_intimation_wo = TourIntimation.objects.create(
            id=1, finyear_id=cls.fin_year.finyear_id, emp_id=cls.emp.emp_id, ti_no='TI-001',
            wo_no='WO-A1', bg_group_id=cls.bg_group_wo.id, project_name='Project X',
            place_of_tour_city=cls.city.city_id, place_of_tour_state=cls.state.s_id,
            place_of_tour_country=cls.country.c_id,
            tour_start_date=datetime(2023, 1, 10, tzinfo=pytz.UTC),
            tour_end_date=datetime(2023, 1, 15, tzinfo=pytz.UTC), comp_id=1
        )
        cls.tour_intimation_bg = TourIntimation.objects.create(
            id=2, finyear_id=cls.fin_year.finyear_id, emp_id=cls.emp.emp_id, ti_no='TI-002',
            wo_no='WO-B2', bg_group_id=cls.bg_group_other.id, project_name='Project Y',
            place_of_tour_city=cls.city.city_id, place_of_tour_state=cls.state.s_id,
            place_of_tour_country=cls.country.c_id,
            tour_start_date=datetime(2023, 2, 1, tzinfo=pytz.UTC),
            tour_end_date=datetime(2023, 2, 5, tzinfo=pytz.UTC), comp_id=1
        )
        TourIntimation.objects.create(
            id=3, finyear_id=cls.fin_year.finyear_id, emp_id=cls.emp.emp_id, ti_no='TI-003',
            wo_no='WO-C3', bg_group_id=cls.bg_group_other.id, project_name='Project Z',
            place_of_tour_city=cls.city.city_id, place_of_tour_state=cls.state.s_id,
            place_of_tour_country=cls.country.c_id,
            tour_start_date=datetime(2023, 3, 1, tzinfo=pytz.UTC),
            tour_end_date=datetime(2023, 3, 5, tzinfo=pytz.UTC), comp_id=1
        )
        TourVoucher.objects.create(id=100, tim_id=3) # Voucher for TI-003

    def setUp(self):
        self.client = Client()
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2023
        session.save()

    def test_list_view(self):
        response = self.client.get(reverse('tourintimation_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/tourintimation/list.html')
        self.assertIsInstance(response.context['search_form'], type(object)) # Check for search form
        self.assertQuerysetEqual(response.context['tour_intimations'], TourIntimation.objects.none()) # Should be empty initially

    def test_table_partial_view_no_search(self):
        response = self.client.get(reverse('tourintimation_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/tourintimation/_tourintimation_table.html')
        # Only TI-001 and TI-002 should show (TI-003 has a voucher)
        self.assertQuerysetEqual(
            response.context['tour_intimations'],
            ['<TourIntimation: TI-002>', '<TourIntimation: TI-001>'], # Ordered by Id Desc
            transform=lambda x: str(x)
        )

    def test_table_partial_view_search_ti_no(self):
        response = self.client.get(reverse('tourintimation_table'), {'drp_field': '0', 'txt_mrs': '001'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/tourintimation/_tourintimation_table.html')
        self.assertEqual(len(response.context['tour_intimations']), 1)
        self.assertEqual(response.context['tour_intimations'].first().ti_no, 'TI-001')

    def test_table_partial_view_search_employee_name(self):
        response = self.client.get(reverse('tourintimation_table'), {'drp_field': '1', 'txt_emp_name': 'John Doe [E001]', 'employee_id_hidden': 'E001'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/tourintimation/_tourintimation_table.html')
        self.assertEqual(len(response.context['tour_intimations']), 2)
        self.assertIn(self.tour_intimation_wo, response.context['tour_intimations'])
        self.assertIn(self.tour_intimation_bg, response.context['tour_intimations'])

    def test_employee_autocomplete_view(self):
        response = self.client.get(reverse('employee_autocomplete'), {'q': 'john'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/tourintimation/_employee_suggestions.html')
        self.assertIn('John Doe [E001]', response.content.decode())

    # --- Generic CRUD view tests (as requested by template, though not used by original page) ---

    def test_create_view_get(self):
        response = self.client.get(reverse('tourintimation_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/tourintimation/form.html')
        self.assertTrue('form' in response.context)

    def test_create_view_post(self):
        data = {
            'finyear_id': self.fin_year.finyear_id,
            'emp_id': self.emp.emp_id,
            'ti_no': 'TI-NEW',
            'wo_no': 'WO-NEW',
            'bg_group_id': self.bg_group_other.id,
            'project_name': 'New Project',
            'place_of_tour_city': self.city.city_id,
            'place_of_tour_state': self.state.s_id,
            'place_of_tour_country': self.country.c_id,
            'tour_start_date': '2024-01-01',
            'tour_end_date': '2024-01-05'
        }
        response = self.client.post(reverse('tourintimation_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertTrue(TourIntimation.objects.filter(ti_no='TI-NEW').exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Tour Intimation added successfully.')

    def test_update_view_get(self):
        obj = TourIntimation.objects.get(id=1)
        response = self.client.get(reverse('tourintimation_edit', args=[obj.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/tourintimation/form.html')
        self.assertEqual(response.context['form'].instance, obj)

    def test_update_view_post(self):
        obj = TourIntimation.objects.get(id=1)
        data = {
            'finyear_id': obj.finyear_id,
            'emp_id': obj.emp_id,
            'ti_no': 'TI-001-Updated',
            'wo_no': obj.wo_no,
            'bg_group_id': obj.bg_group_id,
            'project_name': obj.project_name,
            'place_of_tour_city': obj.place_of_tour_city,
            'place_of_tour_state': obj.place_of_tour_state,
            'place_of_tour_country': obj.place_of_tour_country,
            'tour_start_date': obj.tour_start_date.strftime('%Y-%m-%d'),
            'tour_end_date': obj.tour_end_date.strftime('%Y-%m-%d')
        }
        response = self.client.post(reverse('tourintimation_edit', args=[obj.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        obj.refresh_from_db()
        self.assertEqual(obj.ti_no, 'TI-001-Updated')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Tour Intimation updated successfully.')

    def test_delete_view_get(self):
        obj = TourIntimation.objects.get(id=1)
        response = self.client.get(reverse('tourintimation_delete', args=[obj.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/tourintimation/confirm_delete.html')
        self.assertEqual(response.context['object'], obj)

    def test_delete_view_post(self):
        obj_to_delete = TourIntimation.objects.get(id=1)
        response = self.client.post(reverse('tourintimation_delete', args=[obj_to_delete.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(TourIntimation.objects.filter(id=obj_to_delete.id).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Tour Intimation deleted successfully.')

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The integration relies on declarative attributes directly in the HTML templates.

*   **HTMX for dynamic updates:**
    *   The main list page (`list.html`) uses `hx-get` on `tourintimationTable-container` to fetch the table content from `{% url 'tourintimation_table' %}`. It's triggered on `load` and a custom `refreshTourIntimationList` event.
    *   The search form uses `hx-get` to submit its parameters to `{% url 'tourintimation_table' %}` and `hx-target` to update the `tourintimationTable-container`.
    *   Employee autocomplete uses `hx-get` to fetch suggestions from `{% url 'employee_autocomplete' %}` and `hx-target` to display them. `hx-trigger="keyup changed delay:500ms"` ensures suggestions appear dynamically.
    *   CRUD buttons (Add, Edit, Delete) use `hx-get` to load forms/confirmation into `#modalContent` and `hx-target="#modalContent"`.
    *   Form submissions within modals use `hx-post` and `hx-swap="none"` to prevent full page refresh, relying on `HX-Trigger` to refresh the main list.
*   **Alpine.js for UI state management:**
    *   The search form's dynamic field visibility (`TxtMrs`, `TxtEmpName`, `drpGroup`) is controlled by `x-show` directives, linked to `x-model="searchField"`.
    *   A `resetSearchInputs` function on the Alpine component clears relevant fields when the search type changes.
    *   `x-model` also binds the inputs to Alpine state for cleaner management.
*   **DataTables for list views:**
    *   The `_tourintimation_table.html` partial contains a simple `<table>` with `id="tourintimationTable"`.
    *   The `htmx:afterSwap` event listener in `list.html` checks if the `tourintimationTable-container` has been updated and then re-initializes `$(document).ready(function() { $('#tourintimationTable').DataTable(); });` ensuring DataTables is applied to the newly loaded content. This ensures client-side pagination, sorting, and filtering work as expected.
*   **No custom JavaScript:** All interactions are handled declaratively by HTMX/Alpine.js and standard DataTables initialization.

### Final Notes

*   **Placeholders:** `[MODEL_NAME]`, `[APP_NAME]`, etc. have been replaced with `TourIntimation`, `accounts`, etc., based on the analysis.
*   **DRY Templates:** The `_tourintimation_table.html`, `_tourintimation_form.html`, and `_tourintimation_confirm_delete.html` are partials, enabling reuse.
*   **Fat Model, Thin View:** Complex data retrieval, filtering, and display logic (like `display_fin_year`, `display_employee_name`) are properties/methods within the `TourIntimation` model, keeping views concise and focused on rendering. The `TourIntimationManager` handles the complex `search_and_filter` queryset construction.
*   **Tests:** Comprehensive tests for models and views cover the core functionality and HTMX interactions, aiming for high code coverage.
*   **Database Mapping:** Explicit `db_column` and `managed = False` ensure Django interacts with the existing SQL Server schema.
*   **Session Management:** `request.session.get('compid')` and `request.session.get('finyear')` are used to retrieve company and financial year IDs, mirroring the ASP.NET session usage.
*   **Redirection Logic:** The link `a href="/tourvoucher/details/{{ obj.id }}/"` for `TINo` implies a separate `tourvoucher` application or module for handling the details of a Tour Voucher. This is a common pattern in Django. The `tourvoucher_details` URL in `accounts/urls.py` is a placeholder for this.