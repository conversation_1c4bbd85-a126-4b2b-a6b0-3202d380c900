## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

The provided ASP.NET code for "BalanceSheet.aspx" is a report page that displays aggregated financial data (Liabilities and Assets) from underlying accounting records. It does not perform direct CRUD operations on a "Balance Sheet" entity itself. Instead, it calculates and presents totals based on detailed ledger entries.

To adhere to the Django modernization principles, we will:
1.  **Infer an underlying `LedgerEntry` model:** This model will represent the individual financial transactions that are summarized to create the balance sheet report. This allows us to demonstrate CRUD operations for a common financial entity, as requested by the template.
2.  **Implement a `LedgerEntryManager`:** This custom manager will contain the business logic for aggregating ledger entries into the various balance sheet categories (e.g., Capital Goods, Loans, Current Liabilities, Assets), reflecting the `fun.FillGrid_Creditors`, `TotLoanLiability`, `TotCapitalGoods` methods from the ASP.NET code-behind. This embodies the "Fat Model" principle.
3.  **Create a dedicated `BalanceSheetReportView`:** This view will be responsible for fetching the aggregated data from the `LedgerEntryManager` and rendering the main Balance Sheet report, mimicking the original ASP.NET page's purpose.
4.  **Implement CRUD views for `LedgerEntry`:** Although not directly present on the ASP.NET Balance Sheet page, these views are necessary to manage the underlying data that feeds the report and to fulfill the template's requirements for a `[MODEL_NAME]` with CRUD operations.

We will use `accounts` as the Django application name for this module.

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

Instructions:

Based on the nature of a Balance Sheet, which summarizes various financial accounts, and the calls like `fun.FillGrid_Creditors`, `acc.TotLoanLiability`, `acc.TotCapitalGoods`, we infer the existence of a core ledger table. This table holds the individual entries that sum up to the reported figures.

*   **Inferred Table Name:** `tblLedgerEntry`
*   **Inferred Columns (and their Django equivalents):**
    *   `EntryId` (Primary Key, handled by Django's auto `id` field)
    *   `EntryDate` (Date of the transaction) -> `entry_date` (Django `DateField`)
    *   `AccountCategory` (e.g., 'LIABILITY', 'ASSET') -> `account_category` (Django `CharField` with choices)
    *   `AccountSubtype` (e.g., 'Capital Goods', 'Loan (Liability)', 'Current Liabilities', 'Fixed Asset', 'Investments', 'Current Assets', 'Branch/Division', 'Suspence A/c', 'Profit & Loss A/c') -> `account_subtype` (Django `CharField` with choices)
    *   `Amount` (Monetary value) -> `amount` (Django `DecimalField`)
    *   `CompanyId` (Inferred from `Session["compid"]`) -> `company_id` (Django `IntegerField`)
    *   `FinancialYearId` (Inferred from `Session["finyear"]`) -> `financial_year_id` (Django `IntegerField`)
    *   `Description` (Optional details for the entry) -> `description` (Django `CharField`)

### Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

Instructions:

The provided `BalanceSheet.aspx` and its code-behind primarily serve as a **Read (Aggregation)** interface.

*   **Create**: Not directly present on this page. Other pages (e.g., `Acc_Capital_Particulars.aspx`) are implied to handle input for specific account types.
*   **Read**: The C# code fetches aggregated values (`Amt_CapitalGood`, `Amt_LoanLiability`, `Amt_CurrentLiab`) using functions like `fun.FillGrid_Creditors`, `acc.TotLoanLiability`, `acc.TotCapitalGoods`. This data is then displayed on the page. This is the core functionality to be replicated.
*   **Update**: Not directly present on this page.
*   **Delete**: Not directly present on this page.

The modernization will involve a dedicated `BalanceSheetReportView` to handle the aggregation and display, and separate CRUD views for the underlying `LedgerEntry` model to manage the data that feeds the report.

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

Instructions:

*   **`asp:Label`**: Used for displaying static text like "Liabilities", "Assets", "Total", and dynamic numeric values (e.g., `Amt_CapitalGood`, `Amt_LoanLiability`).
    *   **Django Conversion**: These will be rendered as plain HTML text, with dynamic values passed from the Django view's context.
*   **`asp:LinkButton` with `PostBackUrl`**: Used for navigation to other detail pages.
    *   **Django Conversion**: These will become standard HTML `<a>` tags, utilizing Django's `{% url %}` template tag to resolve to the corresponding detail views/pages.
*   **HTML `<table>` for layout**: The ASP.NET page uses nested HTML tables for structural layout.
    *   **Django Conversion**: This will be modernized using a responsive CSS framework like Tailwind CSS, typically employing `div` elements with flexbox or grid for layout, and DataTables for actual tabular data where applicable.
*   **Styling (`<style>`, `<link rel="stylesheet">`)**: Inline styles and external CSS files are used.
    *   **Django Conversion**: Tailwind CSS will be used directly in the HTML for utility-first styling.

---

### Step 4: Generate Django Code

### 4.1 Models

Task: Create a Django model based on the database schema.

Instructions:

The `LedgerEntry` model will represent the granular financial transactions. A custom manager `LedgerEntryManager` is added to encapsulate the business logic for calculating balance sheet summaries, adhering to the "Fat Model" principle.

**File: `accounts/models.py`**

```python
from django.db import models
from django.db.models import Sum

class LedgerEntryManager(models.Manager):
    """
    Custom manager for LedgerEntry to handle balance sheet specific aggregations.
    This encapsulates the 'fat model' business logic for financial reporting.
    """
    def get_balance_sheet_summary(self, company_id: int, financial_year_id: int) -> dict:
        """
        Calculates and returns aggregated financial data for the balance sheet.
        Mimics the calculations found in the ASP.NET code-behind's functions.
        """
        # --- Liabilities ---
        # Corresponds to Amt_CapitalGood
        total_capital_goods_liability = self.filter(
            company_id=company_id,
            financial_year_id=financial_year_id,
            account_category='LIABILITY',
            account_subtype='Input Cst-Interstate Capital Goods'
        ).aggregate(Sum('amount'))['amount__sum'] or 0.0

        # Corresponds to Amt_LoanLiability
        total_loan_liability = self.filter(
            company_id=company_id,
            financial_year_id=financial_year_id,
            account_category='LIABILITY',
            account_subtype='Loan (Liability)'
        ).aggregate(Sum('amount'))['amount__sum'] or 0.0

        # Corresponds to Amt_CurrentLiab
        total_current_liabilities = self.filter(
            company_id=company_id,
            financial_year_id=financial_year_id,
            account_category='LIABILITY',
            account_subtype='Current Liabilities'
        ).aggregate(Sum('amount'))['amount__sum'] or 0.0

        total_branch_division_liability = self.filter(
            company_id=company_id,
            financial_year_id=financial_year_id,
            account_category='LIABILITY',
            account_subtype='Branch/Division'
        ).aggregate(Sum('amount'))['amount__sum'] or 0.0
        
        total_suspense_account_liability = self.filter(
            company_id=company_id,
            financial_year_id=financial_year_id,
            account_category='LIABILITY',
            account_subtype='Suspence A/c'
        ).aggregate(Sum('amount'))['amount__sum'] or 0.0
        
        total_profit_loss_account_liability = self.filter(
            company_id=company_id,
            financial_year_id=financial_year_id,
            account_category='LIABILITY',
            account_subtype='Profit & Loss A/c'
        ).aggregate(Sum('amount'))['amount__sum'] or 0.0

        total_liabilities = (
            total_capital_goods_liability +
            total_loan_liability +
            total_current_liabilities +
            total_branch_division_liability +
            total_suspense_account_liability +
            total_profit_loss_account_liability
        )

        # --- Assets ---
        total_fixed_assets = self.filter(
            company_id=company_id,
            financial_year_id=financial_year_id,
            account_category='ASSET',
            account_subtype='Fixed Asset'
        ).aggregate(Sum('amount'))['amount__sum'] or 0.0

        total_investments = self.filter(
            company_id=company_id,
            financial_year_id=financial_year_id,
            account_category='ASSET',
            account_subtype='Investments'
        ).aggregate(Sum('amount'))['amount__sum'] or 0.0
        
        total_current_assets = self.filter(
            company_id=company_id,
            financial_year_id=financial_year_id,
            account_category='ASSET',
            account_subtype='Current Assets'
        ).aggregate(Sum('amount'))['amount__sum'] or 0.0
        
        total_assets = (
            total_fixed_assets +
            total_investments +
            total_current_assets
        )

        return {
            'liabilities': {
                'capital_goods': total_capital_goods_liability,
                'loan_liability': total_loan_liability,
                'current_liabilities': total_current_liabilities,
                'branch_division': total_branch_division_liability,
                'suspense_account': total_suspense_account_liability,
                'profit_loss_account': total_profit_loss_account_liability,
                'total': total_liabilities
            },
            'assets': {
                'fixed_asset': total_fixed_assets,
                'investments': total_investments,
                'current_assets': total_current_assets,
                'total': total_assets
            }
        }

class LedgerEntry(models.Model):
    objects = LedgerEntryManager() # Attach the custom manager

    ACCOUNT_CATEGORIES = (
        ('LIABILITY', 'Liability'),
        ('ASSET', 'Asset'),
        ('EQUITY', 'Equity'), # Added for completeness in a real system
        ('REVENUE', 'Revenue'),
        ('EXPENSE', 'Expense'),
    )

    ACCOUNT_SUBTYPES = (
        ('Input Cst-Interstate Capital Goods', 'Input Cst-Interstate Capital Goods'),
        ('Loan (Liability)', 'Loan (Liability)'),
        ('Current Liabilities', 'Current Liabilities'),
        ('Branch/Division', 'Branch/Division'),
        ('Suspence A/c', 'Suspence A/c'),
        ('Profit & Loss A/c', 'Profit & Loss A/c'),
        ('Fixed Asset', 'Fixed Asset'),
        ('Investments', 'Investments'),
        ('Current Assets', 'Current Assets'),
    )

    # Inferred fields from ASP.NET context
    entry_date = models.DateField(db_column='EntryDate', help_text="Date of the ledger entry")
    account_category = models.CharField(
        db_column='AccountCategory', max_length=50, choices=ACCOUNT_CATEGORIES,
        help_text="Broad classification (e.g., Liability, Asset)"
    )
    account_subtype = models.CharField(
        db_column='AccountSubtype', max_length=100, choices=ACCOUNT_SUBTYPES,
        help_text="Specific account type (e.g., Capital Goods, Current Liabilities)"
    )
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=2, help_text="Monetary value of the entry")
    company_id = models.IntegerField(db_column='CompanyId', help_text="ID of the company this entry belongs to")
    financial_year_id = models.IntegerField(db_column='FinancialYearId', help_text="ID of the financial year this entry belongs to")
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True, help_text="Detailed description of the entry")

    class Meta:
        managed = False # Django will not create or manage this table's schema
        db_table = 'tblLedgerEntry' # Assumed table name from legacy system
        verbose_name = 'Ledger Entry'
        verbose_name_plural = 'Ledger Entries'

    def __str__(self):
        return f"{self.entry_date}: {self.account_subtype} - {self.amount}"

    def get_absolute_url(self):
        """Returns the URL to access a particular instance of LedgerEntry."""
        from django.urls import reverse
        return reverse('ledgerentry_edit', args=[str(self.id)])

    def is_valid_entry_amount(self):
        """
        Model method to validate if the amount is positive.
        This demonstrates business logic in the model.
        """
        if self.amount <= 0:
            return False, "Amount must be positive."
        return True, "Amount is valid."

```

### 4.2 Forms

Task: Define a Django form for user input.

Instructions:

A `ModelForm` is created for `LedgerEntry` to handle data entry and validation. Tailwind CSS classes are applied to widgets for consistent styling. Custom validation is added for the `amount` field.

**File: `accounts/forms.py`**

```python
from django import forms
from .models import LedgerEntry

class LedgerEntryForm(forms.ModelForm):
    """
    Form for creating and updating LedgerEntry instances.
    Includes widgets for Tailwind CSS styling.
    """
    class Meta:
        model = LedgerEntry
        fields = [
            'entry_date', 
            'account_category', 
            'account_subtype', 
            'amount', 
            'company_id', 
            'financial_year_id', 
            'description'
        ]
        widgets = {
            'entry_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'account_category': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'account_subtype': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01'}),
            'company_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'financial_year_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
        }
        
    def clean_amount(self):
        amount = self.cleaned_data['amount']
        if amount <= 0:
            raise forms.ValidationError("Amount must be a positive value.")
        return amount

    def clean(self):
        cleaned_data = super().clean()
        # Example of cross-field validation, integrating with model method
        entry_instance = LedgerEntry(**cleaned_data)
        is_valid, message = entry_instance.is_valid_entry_amount()
        if not is_valid:
            self.add_error('amount', message) # Add error to specific field
        return cleaned_data

```

### 4.3 Views

Task: Implement CRUD operations using CBVs and a dedicated report view.

Instructions:

We define `LedgerEntryListView`, `LedgerEntryCreateView`, `LedgerEntryUpdateView`, `LedgerEntryDeleteView` for managing the underlying ledger entries. Additionally, a `BalanceSheetReportView` is created to handle the aggregation and display of the balance sheet data, replicating the original ASP.NET page's functionality. HTMX headers are handled for dynamic responses.

**File: `accounts/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import render
from .models import LedgerEntry
from .forms import LedgerEntryForm

# --- LedgerEntry CRUD Views (for managing underlying data) ---

class LedgerEntryListView(ListView):
    """
    Displays a list of all LedgerEntry instances, used for DataTables.
    """
    model = LedgerEntry
    template_name = 'accounts/ledgerentry/list.html'
    context_object_name = 'ledgerentries'

class LedgerEntryTablePartialView(ListView):
    """
    Partial view to render the LedgerEntry table for HTMX requests.
    """
    model = LedgerEntry
    template_name = 'accounts/ledgerentry/_ledgerentry_table.html'
    context_object_name = 'ledgerentries'
    # In a real app, you might add filtering/pagination logic here
    # based on query parameters if not handled purely by DataTables JS.

    def get_queryset(self):
        # In a real application, you'd filter by company_id and financial_year_id
        # from the user's session or profile, similar to ASP.NET.
        # For this example, we return all entries or filtered for testing.
        return super().get_queryset()

class LedgerEntryCreateView(CreateView):
    """
    Handles creation of new LedgerEntry instances.
    Responds with HTMX trigger for dynamic list refresh.
    """
    model = LedgerEntry
    form_class = LedgerEntryForm
    template_name = 'accounts/ledgerentry/form.html'
    success_url = reverse_lazy('ledgerentry_list') # Fallback if not HTMX

    def form_valid(self, form):
        # In a real app, populate company_id and financial_year_id from session/user
        # For demonstration:
        if not form.instance.company_id:
            form.instance.company_id = self.request.session.get('compid', 1) # Default to 1
        if not form.instance.financial_year_id:
            form.instance.financial_year_id = self.request.session.get('finyear', 2023) # Default to 2023

        response = super().form_valid(form)
        messages.success(self.request, 'Ledger Entry added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content to return, just headers
                headers={
                    'HX-Trigger': 'refreshLedgerEntryList' # Custom HTMX event to trigger list update
                }
            )
        return response

class LedgerEntryUpdateView(UpdateView):
    """
    Handles updating existing LedgerEntry instances.
    Responds with HTMX trigger for dynamic list refresh.
    """
    model = LedgerEntry
    form_class = LedgerEntryForm
    template_name = 'accounts/ledgerentry/form.html'
    success_url = reverse_lazy('ledgerentry_list') # Fallback

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Ledger Entry updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshLedgerEntryList'
                }
            )
        return response

class LedgerEntryDeleteView(DeleteView):
    """
    Handles deletion of LedgerEntry instances.
    Responds with HTMX trigger for dynamic list refresh.
    """
    model = LedgerEntry
    template_name = 'accounts/ledgerentry/confirm_delete.html'
    success_url = reverse_lazy('ledgerentry_list') # Fallback

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Ledger Entry deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshLedgerEntryList'
                }
            )
        return response

# --- Balance Sheet Report View (mimics original ASP.NET page) ---

class BalanceSheetReportView(TemplateView):
    """
    Displays the aggregated Balance Sheet report.
    This view leverages the LedgerEntryManager for business logic (fat model).
    """
    template_name = 'accounts/balance_sheet/report.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # In a real application, company_id and financial_year_id would come
        # from the authenticated user's session or profile, similar to ASP.NET.
        # For this example, we'll use placeholder values.
        company_id = self.request.session.get('compid', 1) 
        financial_year_id = self.request.session.get('finyear', 2023)

        # Get the aggregated balance sheet summary from the LedgerEntryManager
        balance_sheet_summary = LedgerEntry.objects.get_balance_sheet_summary(
            company_id=company_id, 
            financial_year_id=financial_year_id
        )
        
        context.update({
            'liabilities': balance_sheet_summary['liabilities'],
            'assets': balance_sheet_summary['assets'],
        })
        return context

```

### 4.4 Templates

Task: Create templates for each view.

Instructions:

Templates adhere to DRY principles, extending `core/base.html` and using partials for reusable components like forms and tables. HTMX is integrated for dynamic interactions and DataTables for list presentation. Alpine.js is used for modal management.

**File: `accounts/ledgerentry/list.html` (Ledger Entry List View)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Ledger Entries</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'ledgerentry_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Ledger Entry
        </button>
    </div>
    
    <div id="ledgerentryTable-container"
         hx-trigger="load, refreshLedgerEntryList from:body"
         hx-get="{% url 'ledgerentry_table_partial' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading Ledger Entries...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for broader UI state
    });
</script>
{% endblock %}
```

**File: `accounts/ledgerentry/_ledgerentry_table.html` (Ledger Entry Table Partial for HTMX)**

```html
<div class="overflow-x-auto bg-white shadow-md rounded-lg">
    <table id="ledgerentryTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subtype</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company ID</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Financial Year</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in ledgerentries %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.entry_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.get_account_category_display }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.get_account_subtype_display }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500 text-right">{{ obj.amount|floatformat:2 }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.company_id }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.financial_year_id }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.description|default:"-" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                        hx-get="{% url 'ledgerentry_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                        hx-get="{% url 'ledgerentry_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="9" class="py-3 px-4 text-center text-sm text-gray-500">No ledger entries found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
$(document).ready(function() {
    $('#ledgerentryTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
    });
});
</script>
```

**File: `accounts/ledgerentry/form.html` (Ledger Entry Form Partial for HTMX)**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Ledger Entry</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="text-gray-500 text-xs mt-1">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**File: `accounts/ledgerentry/confirm_delete.html` (Ledger Entry Delete Confirmation Partial for HTMX)**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="mb-4">Are you sure you want to delete the ledger entry for "{{ object.account_subtype }}" on {{ object.entry_date }}?</p>
    
    <form hx-post="{% url 'ledgerentry_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

**File: `accounts/balance_sheet/report.html` (Balance Sheet Report View)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-4 rounded-t-lg mb-6">
        <h2 class="text-xl font-bold text-center">&nbsp;Balance Sheet</h2>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <!-- Liabilities Section -->
        <div class="bg-white p-6 rounded-lg shadow-md">
            <h3 class="text-xl font-bold underline text-gray-800 mb-4">Liabilities</h3>
            <div class="space-y-3">
                <div class="grid grid-cols-3 gap-2 py-2 px-3 bg-blue-50 rounded-md">
                    <span class="col-span-2 text-gray-700">
                        <a href="{% url 'ledgerentry_list' %}?category=LIABILITY&subtype=Input Cst-Interstate Capital Goods" class="text-blue-600 hover:underline">Input Cst-Interstate Capital Goods</a>
                    </span>
                    <span class="text-right font-bold text-gray-900">{{ liabilities.capital_goods|floatformat:2 }}</span>
                </div>
                <div class="grid grid-cols-3 gap-2 py-2 px-3">
                    <span class="col-span-2 text-gray-700">
                        <a href="{% url 'ledgerentry_list' %}?category=LIABILITY&subtype=Loan (Liability)" class="text-blue-600 hover:underline">Loan (Liability)</a>
                    </span>
                    <span class="text-right font-bold text-gray-900">{{ liabilities.loan_liability|floatformat:2 }}</span>
                </div>
                <div class="grid grid-cols-3 gap-2 py-2 px-3 bg-blue-50 rounded-md">
                    <span class="col-span-2 text-gray-700">
                        <a href="{% url 'ledgerentry_list' %}?category=LIABILITY&subtype=Current Liabilities" class="text-blue-600 hover:underline">Current Liabilities</a>
                    </span>
                    <span class="text-right font-bold text-gray-900">{{ liabilities.current_liabilities|floatformat:2 }}</span>
                </div>
                <div class="grid grid-cols-3 gap-2 py-2 px-3">
                    <span class="col-span-2 text-gray-700">
                        <a href="{% url 'ledgerentry_list' %}?category=LIABILITY&subtype=Branch/Division" class="text-blue-600 hover:underline">Branch/Division</a>
                    </span>
                    <span class="text-right font-bold text-gray-900">{{ liabilities.branch_division|floatformat:2 }}</span>
                </div>
                <div class="grid grid-cols-3 gap-2 py-2 px-3 bg-blue-50 rounded-md">
                    <span class="col-span-2 text-gray-700">
                        <a href="{% url 'ledgerentry_list' %}?category=LIABILITY&subtype=Suspence A/c" class="text-blue-600 hover:underline">Suspense A/c</a>
                    </span>
                    <span class="text-right font-bold text-gray-900">{{ liabilities.suspense_account|floatformat:2 }}</span>
                </div>
                <div class="grid grid-cols-3 gap-2 py-2 px-3">
                    <span class="col-span-2 text-gray-700">
                        <a href="{% url 'ledgerentry_list' %}?category=LIABILITY&subtype=Profit & Loss A/c" class="text-blue-600 hover:underline">Profit &amp; Loss A/c</a>
                    </span>
                    <span class="text-right font-bold text-gray-900">{{ liabilities.profit_loss_account|floatformat:2 }}</span>
                </div>
                
                <div class="grid grid-cols-3 gap-2 py-2 px-3 pt-4 border-t border-gray-200">
                    <span class="col-span-2 text-gray-800 font-bold text-lg">Total Liabilities</span>
                    <span class="text-right font-bold text-gray-900 text-lg">{{ liabilities.total|floatformat:2 }}</span>
                </div>
            </div>
        </div>

        <!-- Assets Section -->
        <div class="bg-white p-6 rounded-lg shadow-md">
            <h3 class="text-xl font-bold underline text-gray-800 mb-4">Assets</h3>
            <div class="space-y-3">
                <div class="grid grid-cols-3 gap-2 py-2 px-3 bg-blue-50 rounded-md">
                    <span class="col-span-2 text-gray-700">
                        <a href="{% url 'ledgerentry_list' %}?category=ASSET&subtype=Fixed Asset" class="text-blue-600 hover:underline">Fixed Asset</a>
                    </span>
                    <span class="text-right font-bold text-gray-900">{{ assets.fixed_asset|floatformat:2 }}</span>
                </div>
                <div class="grid grid-cols-3 gap-2 py-2 px-3">
                    <span class="col-span-2 text-gray-700">
                        <a href="{% url 'ledgerentry_list' %}?category=ASSET&subtype=Investments" class="text-blue-600 hover:underline">Investments</a>
                    </span>
                    <span class="text-right font-bold text-gray-900">{{ assets.investments|floatformat:2 }}</span>
                </div>
                <div class="grid grid-cols-3 gap-2 py-2 px-3 bg-blue-50 rounded-md">
                    <span class="col-span-2 text-gray-700">
                        <a href="{% url 'ledgerentry_list' %}?category=ASSET&subtype=Current Assets" class="text-blue-600 hover:underline">Current Assets</a>
                    </span>
                    <span class="text-right font-bold text-gray-900">{{ assets.current_assets|floatformat:2 }}</span>
                </div>
                
                <div class="grid grid-cols-3 gap-2 py-2 px-3 pt-4 border-t border-gray-200">
                    <span class="col-span-2 text-gray-800 font-bold text-lg">Total Assets</span>
                    <span class="text-right font-bold text-gray-900 text-lg">{{ assets.total|floatformat:2 }}</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
```

### 4.5 URLs

Task: Define URL patterns for the views.

Instructions:

URL patterns are set up for both the `LedgerEntry` CRUD operations and the `BalanceSheetReportView`. A partial view URL for the DataTables content is also included for HTMX.

**File: `accounts/urls.py`**

```python
from django.urls import path
from .views import (
    LedgerEntryListView, LedgerEntryCreateView, LedgerEntryUpdateView, 
    LedgerEntryDeleteView, LedgerEntryTablePartialView, BalanceSheetReportView
)

urlpatterns = [
    # URLs for LedgerEntry CRUD operations
    path('ledgerentry/', LedgerEntryListView.as_view(), name='ledgerentry_list'),
    path('ledgerentry/table/', LedgerEntryTablePartialView.as_view(), name='ledgerentry_table_partial'),
    path('ledgerentry/add/', LedgerEntryCreateView.as_view(), name='ledgerentry_add'),
    path('ledgerentry/edit/<int:pk>/', LedgerEntryUpdateView.as_view(), name='ledgerentry_edit'),
    path('ledgerentry/delete/<int:pk>/', LedgerEntryDeleteView.as_view(), name='ledgerentry_delete'),

    # URL for the Balance Sheet Report (mimicking the original ASP.NET page)
    path('balance-sheet/', BalanceSheetReportView.as_view(), name='balance_sheet_report'),
]
```

### 4.6 Tests

Task: Write tests for the model and views.

Instructions:

Comprehensive unit tests cover model methods and properties. Integration tests verify the functionality of all views (list, create, update, delete, and report) including HTMX interactions and template usage.

**File: `accounts/tests.py`**

```python       
from django.test import TestCase, Client
from django.urls import reverse
from decimal import Decimal
from .models import LedgerEntry

class LedgerEntryModelTest(TestCase):
    """
    Unit tests for the LedgerEntry model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create common test data for all tests
        cls.company_id = 1
        cls.financial_year_id = 2023
        LedgerEntry.objects.create(
            entry_date='2023-01-01',
            account_category='LIABILITY',
            account_subtype='Loan (Liability)',
            amount=Decimal('10000.00'),
            company_id=cls.company_id,
            financial_year_id=cls.financial_year_id,
            description='Test Loan Entry'
        )
        LedgerEntry.objects.create(
            entry_date='2023-01-05',
            account_category='ASSET',
            account_subtype='Fixed Asset',
            amount=Decimal('50000.00'),
            company_id=cls.company_id,
            financial_year_id=cls.financial_year_id,
            description='Test Fixed Asset'
        )
        LedgerEntry.objects.create(
            entry_date='2023-01-10',
            account_category='LIABILITY',
            account_subtype='Current Liabilities',
            amount=Decimal('2500.00'),
            company_id=cls.company_id,
            financial_year_id=cls.financial_year_id,
            description='Test Current Liability'
        )
        LedgerEntry.objects.create(
            entry_date='2023-01-15',
            account_category='LIABILITY',
            account_subtype='Input Cst-Interstate Capital Goods',
            amount=Decimal('15000.00'),
            company_id=cls.company_id,
            financial_year_id=cls.financial_year_id,
            description='Test Capital Goods Liability'
        )


    def test_ledgerentry_creation(self):
        obj = LedgerEntry.objects.get(description='Test Loan Entry')
        self.assertEqual(obj.entry_date.strftime('%Y-%m-%d'), '2023-01-01')
        self.assertEqual(obj.account_category, 'LIABILITY')
        self.assertEqual(obj.amount, Decimal('10000.00'))
        self.assertEqual(obj.company_id, self.company_id)

    def test_amount_positive_validation(self):
        obj = LedgerEntry.objects.get(description='Test Loan Entry')
        is_valid, message = obj.is_valid_entry_amount()
        self.assertTrue(is_valid)
        self.assertEqual(message, "Amount is valid.")

        # Test with zero amount
        invalid_obj_zero = LedgerEntry(
            entry_date='2023-02-01',
            account_category='ASSET',
            account_subtype='Current Assets',
            amount=Decimal('0.00'),
            company_id=self.company_id,
            financial_year_id=self.financial_year_id,
            description='Zero Amount'
        )
        is_valid, message = invalid_obj_zero.is_valid_entry_amount()
        self.assertFalse(is_valid)
        self.assertEqual(message, "Amount must be positive.")

        # Test with negative amount
        invalid_obj_negative = LedgerEntry(
            entry_date='2023-02-02',
            account_category='LIABILITY',
            account_subtype='Loan (Liability)',
            amount=Decimal('-100.00'),
            company_id=self.company_id,
            financial_year_id=self.financial_year_id,
            description='Negative Amount'
        )
        is_valid, message = invalid_obj_negative.is_valid_entry_amount()
        self.assertFalse(is_valid)
        self.assertEqual(message, "Amount must be positive.")

    def test_balance_sheet_summary_calculation(self):
        summary = LedgerEntry.objects.get_balance_sheet_summary(
            company_id=self.company_id, 
            financial_year_id=self.financial_year_id
        )
        
        self.assertEqual(summary['liabilities']['loan_liability'], Decimal('10000.00'))
        self.assertEqual(summary['liabilities']['current_liabilities'], Decimal('2500.00'))
        self.assertEqual(summary['liabilities']['capital_goods'], Decimal('15000.00'))
        self.assertEqual(summary['assets']['fixed_asset'], Decimal('50000.00'))
        
        # Verify total liabilities
        expected_total_liabilities = Decimal('10000.00') + Decimal('2500.00') + Decimal('15000.00')
        self.assertEqual(summary['liabilities']['total'], expected_total_liabilities)
        
        # Verify total assets
        expected_total_assets = Decimal('50000.00')
        self.assertEqual(summary['assets']['total'], expected_total_assets)

class LedgerEntryViewsTest(TestCase):
    """
    Integration tests for LedgerEntry and BalanceSheet views.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for views tests
        cls.company_id = 1
        cls.financial_year_id = 2023
        cls.ledger_entry1 = LedgerEntry.objects.create(
            entry_date='2023-01-01',
            account_category='LIABILITY',
            account_subtype='Loan (Liability)',
            amount=Decimal('10000.00'),
            company_id=cls.company_id,
            financial_year_id=cls.financial_year_id,
            description='View Test Loan'
        )
        cls.ledger_entry2 = LedgerEntry.objects.create(
            entry_date='2023-01-05',
            account_category='ASSET',
            account_subtype='Fixed Asset',
            amount=Decimal('50000.00'),
            company_id=cls.company_id,
            financial_year_id=cls.financial_year_id,
            description='View Test Asset'
        )
    
    def setUp(self):
        self.client = Client()
        # Set session data similar to ASP.NET environment
        session = self.client.session
        session['compid'] = self.company_id
        session['finyear'] = self.financial_year_id
        session.save()
    
    def test_ledgerentry_list_view(self):
        response = self.client.get(reverse('ledgerentry_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/ledgerentry/list.html')
        self.assertContains(response, 'Ledger Entries') # Check for page title

    def test_ledgerentry_table_partial_view_htmx(self):
        # Test for HTMX loaded content
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('ledgerentry_table_partial'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/ledgerentry/_ledgerentry_table.html')
        self.assertContains(response, self.ledger_entry1.description)
        self.assertContains(response, self.ledger_entry2.description)

    def test_ledgerentry_create_view_get(self):
        response = self.client.get(reverse('ledgerentry_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/ledgerentry/form.html')
        self.assertTrue('form' in response.context)
        
    def test_ledgerentry_create_view_post_success(self):
        data = {
            'entry_date': '2023-02-10',
            'account_category': 'EQUITY',
            'account_subtype': 'Profit & Loss A/c',
            'amount': Decimal('7500.00'),
            'company_id': self.company_id,
            'financial_year_id': self.financial_year_id,
            'description': 'New P&L Entry'
        }
        response = self.client.post(reverse('ledgerentry_add'), data)
        self.assertEqual(response.status_code, 302) # Redirect on successful non-HTMX post
        self.assertTrue(LedgerEntry.objects.filter(description='New P&L Entry').exists())
        self.assertRedirects(response, reverse('ledgerentry_list'))

    def test_ledgerentry_create_view_post_htmx_success(self):
        data = {
            'entry_date': '2023-02-11',
            'account_category': 'LIABILITY',
            'account_subtype': 'Suspence A/c',
            'amount': Decimal('1234.56'),
            'company_id': self.company_id,
            'financial_year_id': self.financial_year_id,
            'description': 'HTMX New Suspense Entry'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('ledgerentry_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshLedgerEntryList')
        self.assertTrue(LedgerEntry.objects.filter(description='HTMX New Suspense Entry').exists())

    def test_ledgerentry_create_view_post_invalid(self):
        data = {
            'entry_date': '2023-02-10',
            'account_category': 'LIABILITY',
            'account_subtype': 'Current Liabilities',
            'amount': Decimal('-500.00'), # Invalid amount
            'company_id': self.company_id,
            'financial_year_id': self.financial_year_id,
            'description': 'Invalid Entry'
        }
        response = self.client.post(reverse('ledgerentry_add'), data)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertFalse(LedgerEntry.objects.filter(description='Invalid Entry').exists())
        self.assertContains(response, 'Amount must be a positive value.')

    def test_ledgerentry_update_view_get(self):
        response = self.client.get(reverse('ledgerentry_edit', args=[self.ledger_entry1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/ledgerentry/form.html')
        self.assertContains(response, self.ledger_entry1.description)

    def test_ledgerentry_update_view_post_success(self):
        data = {
            'entry_date': '2023-01-01', # Same date
            'account_category': 'LIABILITY',
            'account_subtype': 'Loan (Liability)',
            'amount': Decimal('12000.00'), # Updated amount
            'company_id': self.company_id,
            'financial_year_id': self.financial_year_id,
            'description': 'Updated Loan Entry' # Updated description
        }
        response = self.client.post(reverse('ledgerentry_edit', args=[self.ledger_entry1.pk]), data)
        self.assertEqual(response.status_code, 302)
        self.ledger_entry1.refresh_from_db()
        self.assertEqual(self.ledger_entry1.amount, Decimal('12000.00'))
        self.assertEqual(self.ledger_entry1.description, 'Updated Loan Entry')
        self.assertRedirects(response, reverse('ledgerentry_list'))

    def test_ledgerentry_update_view_post_htmx_success(self):
        data = {
            'entry_date': '2023-01-05',
            'account_category': 'ASSET',
            'account_subtype': 'Fixed Asset',
            'amount': Decimal('60000.00'),
            'company_id': self.company_id,
            'financial_year_id': self.financial_year_id,
            'description': 'HTMX Updated Asset'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('ledgerentry_edit', args=[self.ledger_entry2.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshLedgerEntryList')
        self.ledger_entry2.refresh_from_db()
        self.assertEqual(self.ledger_entry2.amount, Decimal('60000.00'))

    def test_ledgerentry_delete_view_get(self):
        response = self.client.get(reverse('ledgerentry_delete', args=[self.ledger_entry1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/ledgerentry/confirm_delete.html')
        self.assertContains(response, f'delete the ledger entry for "{self.ledger_entry1.account_subtype}"')

    def test_ledgerentry_delete_view_post_success(self):
        entry_id = self.ledger_entry1.pk
        response = self.client.post(reverse('ledgerentry_delete', args=[entry_id]))
        self.assertEqual(response.status_code, 302)
        self.assertFalse(LedgerEntry.objects.filter(pk=entry_id).exists())
        self.assertRedirects(response, reverse('ledgerentry_list'))

    def test_ledgerentry_delete_view_post_htmx_success(self):
        entry_id = self.ledger_entry2.pk
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('ledgerentry_delete', args=[entry_id]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshLedgerEntryList')
        self.assertFalse(LedgerEntry.objects.filter(pk=entry_id).exists())

    def test_balance_sheet_report_view(self):
        response = self.client.get(reverse('balance_sheet_report'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/balance_sheet/report.html')
        self.assertContains(response, 'Balance Sheet')
        self.assertContains(response, 'Liabilities')
        self.assertContains(response, 'Assets')
        
        # Check if amounts are present in the context and rendered
        self.assertIn('liabilities', response.context)
        self.assertIn('assets', response.context)
        self.assertContains(response, str(self.ledger_entry1.amount))
        self.assertContains(response, str(self.ledger_entry2.amount))
        
        # Test total calculations
        expected_total_liabilities = LedgerEntry.objects.get_balance_sheet_summary(
            company_id=self.company_id, 
            financial_year_id=self.financial_year_id
        )['liabilities']['total']
        self.assertContains(response, f'Total Liabilities</span>\n                    <span class="text-right font-bold text-gray-900 text-lg">{expected_total_liabilities:.2f}</span>')

```

---

### Step 5: HTMX and Alpine.js Integration

Instructions:

*   **HTMX:** All CRUD operations for `LedgerEntry` (add, edit, delete) are designed to use HTMX for partial page updates.
    *   The "Add New" and "Edit" buttons trigger `hx-get` requests to load forms into a modal (`#modalContent`).
    *   Form submissions (`hx-post`) return `204 No Content` and `HX-Trigger` headers (`refreshLedgerEntryList`) to inform the client to refresh the DataTables display (`#ledgerentryTable-container`).
    *   The DataTables content itself is loaded via an `hx-get` request to a partial view (`{% url 'ledgerentry_table_partial' %}`) triggered on `load` and on the custom `refreshLedgerEntryList` event.
    *   Delete operations also use `hx-get` for the confirmation dialog and `hx-post` for the actual deletion with `HX-Trigger` for list refresh.
*   **Alpine.js:** Used for simple UI state management, specifically for showing/hiding the modal. The `_="on click add .is-active to #modal"` and `_="on click if event.target.id == 'modal' remove .is-active from me"` attributes handle the modal's visibility.
*   **DataTables:** Implemented in `_ledgerentry_table.html` for client-side searching, sorting, and pagination of the `LedgerEntry` list. The JavaScript initialization is placed within the partial template, ensuring it runs each time the table content is loaded via HTMX.
*   **Full Page Reload Avoidance:** All interactions on the `ledgerentry/list.html` page (adding, editing, deleting, refreshing the list) occur without full page reloads, providing a smooth user experience.
*   **DRY Templates:** The forms (`form.html`) and delete confirmation (`confirm_delete.html`) are designed as partials to be loaded into the modal, reducing code duplication.

---

## Final Notes

*   This modernization plan effectively transitions the display and underlying data management from ASP.NET Web Forms to a modern Django application using "Fat Model, Thin View" architecture.
*   The business logic for calculating balance sheet summaries is correctly placed within the `LedgerEntry` model's manager, keeping views concise.
*   User interface interactions are significantly enhanced through HTMX and Alpine.js, eliminating full page reloads for common operations.
*   Data presentation leverages DataTables for robust client-side features.
*   All placeholder values (e.g., `company_id`, `financial_year_id`) that were retrieved from ASP.NET `Session` would ideally be managed through Django's authentication system or a user profile associated with the request in a real application. For this example, they are passed or defaulted.
*   The provided unit and integration tests ensure robust and maintainable code.
*   Tailwind CSS, assumed to be integrated into `core/base.html`, provides utility-first styling without custom CSS.