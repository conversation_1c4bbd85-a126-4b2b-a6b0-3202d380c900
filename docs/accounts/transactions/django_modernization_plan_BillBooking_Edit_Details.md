## ASP.NET to Django Conversion Script: Bill Booking Edit Module

This document outlines a comprehensive modernization plan to transition the provided ASP.NET "Bill Booking - Edit Details" functionality to a modern Django-based solution. The focus is on leveraging Django's best practices, including a fat model/thin view architecture, HTMX for dynamic interactions, Alpine.js for lightweight frontend logic, and DataTables for efficient data presentation, all while prioritizing AI-assisted automation for implementation.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is inherited from `core/base.html`.
- Focus ONLY on component-specific code for the current module (Bill Booking).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

The ASP.NET code interacts with several database tables. We will focus on the core tables directly manipulated by this page and acknowledge the existence of other lookup/related tables which would be mapped as `managed=False` models elsewhere in the application.

**Primary Tables for this Module:**

*   `tblACC_BillBooking_Master`: Stores the main bill booking details.
*   `tblACC_BillBooking_Details`: Stores the "PO Term Details" (line items) for a bill booking.
*   `tblACC_BillBooking_Attach_Master`: Stores permanent attachments related to a bill booking.
*   `tblACC_BillBooking_Attach_Temp`: Temporary storage for attachments before final save.

**Key Columns Identified:**

*   **`tblACC_BillBooking_Master`:**
    *   `Id` (Primary Key)
    *   `SysDate` (System Date)
    *   `SysTime` (System Time)
    *   `SessionId` (User Session ID)
    *   `CompId` (Company ID)
    *   `FinYearId` (Financial Year ID)
    *   `PVEVNo` (PV/EV Number)
    *   `SupplierId` (Foreign Key to Supplier Master)
    *   `BillNo` (Bill Number)
    *   `BillDate` (Bill Date)
    *   `CENVATEntryNo` (CENVAT Entry Number)
    *   `CENVATEntryDate` (CENVAT Entry Date)
    *   `OtherCharges` (Other Charges Amount)
    *   `OtherChaDesc` (Other Charges Description)
    *   `Narration` (Narration/Remarks)
    *   `DebitAmt` (Debit Amount)
    *   `DiscountType` (Discount Type: 0 for Amount, 1 for Percentage)
    *   `Discount` (Discount Amount/Percentage)

*   **`tblACC_BillBooking_Details`:**
    *   `Id` (Primary Key)
    *   `MId` (Foreign Key to `tblACC_BillBooking_Master`)
    *   `GQNId` (FK to Material Quality Details, if applicable)
    *   `GSNId` (FK to Material Service Note Details, if applicable)
    *   `ItemId` (FK to Item Master)
    *   `PODId` (FK to PO Details)
    *   `PFAmt` (Packing & Forwarding Amount)
    *   `ExStBasic` (Basic Excise/Service Tax Amount)
    *   `ExStEducess` (Education Cess Amount)
    *   `ExStShecess` (SHE Cess Amount)
    *   `CustomDuty` (Custom Duty Amount)
    *   `VAT` (VAT Amount)
    *   `CST` (CST Amount)
    *   `Freight` (Freight Amount)
    *   `TarrifNo` (Tariff Number)
    *   `ExStBasicInPer` (Basic Excise/Service Tax Percentage)
    *   `ExStEducessInPer` (Education Cess Percentage)
    *   `ExStShecessInPer` (SHE Cess Percentage)

*   **`tblACC_BillBooking_Attach_Master` / `tblACC_BillBooking_Attach_Temp`:**
    *   `Id` (Primary Key)
    *   `MId` (Foreign Key to `tblACC_BillBooking_Master`)
    *   `CompId` (Company ID)
    *   `SessionId` (User Session ID)
    *   `FinYearId` (Financial Year ID)
    *   `FileName` (Original File Name)
    *   `FileSize` (File Size in Bytes)
    *   `ContentType` (MIME Type)
    *   `FileData` (Binary file content)

**Related Lookup Tables (to be represented as `managed=False` models elsewhere if not already present):**

*   `tblMM_Supplier_master` (Supplier details)
*   `tblcountry`, `tblState`, `tblCity` (Address components)
*   `tblMM_PO_Master`, `tblMM_PO_Details` (Purchase Order details)
*   `tblQc_MaterialQuality_Master`, `tblQc_MaterialQuality_Details` (Material Quality details)
*   `tblinv_MaterialServiceNote_Master`, `tblinv_MaterialServiceNote_Details` (Material Service Note details)
*   `tblDG_Item_Master` (Item details)
*   `Unit_Master` (Unit of Measurement details)
*   `tblHR_Departments` (Department details)

### Step 2: Identify Backend Functionality

This ASP.NET page handles the *editing* of a Bill Booking record, its associated line items (PO Term Details), and attachments.

*   **Read (R):**
    *   **Master Data Load:** On page load, it retrieves the main bill booking details from `tblACC_BillBooking_Master` based on `PVEVId` (from QueryString).
    *   **Supplier Details Load:** Fetches `tblMM_Supplier_master` and related address details (`tblcountry`, `tblState`, `tblCity`) using `SupplierId`.
    *   **PO/WO/Dept Info Load:** Retrieves related PO details from `tblMM_PO_Master` and `tblMM_PO_Details`, then further fetches WO/Dept info from `tblMM_PR_Master`/`tblMM_PR_Details` or `tblMM_SPR_Master`/`tblMM_SPR_Details` and `tblHR_Departments`.
    *   **PO Term Details (Line Items) Load:** The `loadData()` method dynamically builds a `DataTable` by querying `tblACC_BillBooking_Details`. It performs complex lookups and calculations by joining with `tblMM_PO_Details`, `tblQc_MaterialQuality_Details`, `tblinv_MaterialServiceNote_Details`, `tblDG_Item_Master`, and `Unit_Master` to display `GQNNo`, `GSNNo`, `ItemCode`, `Description`, `UOM`, and `Amt`. This `Amt` calculation (`((Rate - (Rate * Discount) / 100) * AccQty)`) is a key piece of business logic.
    *   **Attachments Load:** The `SqlDataSource1` populates `GridView2` by selecting from `tblACC_BillBooking_Attach_Master`.

*   **Update (U):**
    *   **Main Bill Booking Update:** The `btnProceed_Click` event handler updates the `tblACC_BillBooking_Master` record with values from `textBillno`, `textBillDate`, `textCVEntryNo`, `textCVEntryDate`, `txtOtherCharges`, `txtOtherChaDesc`, `txtNarration`, `txtDebitAmt`, `txtDiscount`, and `DrpAdd`.
    *   **PO Term Detail Update:** The `GridView1_RowUpdating` event handler updates a specific row in `tblACC_BillBooking_Details` with edited values like `PFAmt`, `ExStBasicInPer`, `ExStEducessInPer`, `ExStShecessInPer`, `ExStBasic`, `ExStEducess`, `ExStShecess`, `CustomDuty`, `VAT`, `CST`, `Freight`, and `TarrifNo`.

*   **Create (C):**
    *   **Attachment Upload:** The `Button1_Click` event uploads a new file directly to `tblACC_BillBooking_Attach_Master`.
    *   **Temporary Attachment Transfer:** The `btnProceed_Click` also transfers any files from `tblACC_BillBooking_Attach_Temp` to `tblACC_BillBooking_Attach_Master` and then clears the temp table. This implies a workflow where files might be uploaded temporarily before the main form is submitted.

*   **Delete (D):**
    *   **Attachment Delete:** The `SqlDataSource1` handles deletion of attachments from `tblACC_BillBooking_Attach_Master`.

*   **Validation:** Extensive validation is performed on both the client-side (via validators) and server-side (using `RequiredFieldValidator`, `RegularExpressionValidator`, and `fun.NumberValidationQty`, `fun.DateValidation`). This logic needs to be replicated in Django forms.

### Step 3: Infer UI Components

The ASP.NET UI utilizes a `TabContainer` to organize information into three main sections: "PV/EV Booking Details", "PO Term Details", and "Terms & Conditions".

*   **PV/EV Booking Details Tab:**
    *   Labels: `lblPVEVNo`, `lblPoNo`, `lblSupplierName`, `lblSupplierAdd`, `lblECCno`, `lblDivision`, `lblVatNo`, `lblRange`, `lblComm`, `lblCSTNo`, `lblServiceTax`, `lblTDS`, `lblPanNo`, `lblWoDeptNo`.
    *   Textboxes: `textBillno`, `textBillDate`, `textCVEntryNo`, `textCVEntryDate`.
    *   `CalendarExtender` for date pickers.
    *   `FileUpload`: `FileUpload1`, `Button1` for attachment upload.
    *   `GridView`: `GridView2` for displaying attachments.

*   **PO Term Details Tab:**
    *   `GridView`: `GridView1` for displaying and editing line items.
        *   Display columns: `SN`, `Id`, `GQNNo`, `GSNNo`, `ItemCode`, `Description`, `UOM`, `Amt`.
        *   Editable columns (textboxes): `PFAmt`, `ExStBasicInPer`, `EduCessInPer`, `SheCessInPer`, `ExStBasic`, `EduCess`, `SheCess`, `CustomDuty`, `VAT`, `CST`, `Freight`, `TarrifNo`.

*   **Terms & Conditions Tab:**
    *   Textboxes: `txtOtherCharges`, `txtOtherChaDesc`, `txtDebitAmt`, `txtDiscount`, `txtNarration` (multiline).
    *   Dropdown: `DrpAdd` (for Discount Type).

*   **Action Buttons:** `btnProceed` (Update), `btnCancel` (Cancel/Redirect).

### Step 4: Generate Django Code

We will create a Django application, e.g., `accounts`, to house this functionality.

#### 4.1 Models (`accounts/models.py`)

This section will define the core Django models, mapping them to the existing SQL Server tables. We'll include placeholder models for related tables (assuming `managed=False` means they exist and are handled by other parts of the system or are simple lookups). The `BillBookingDetail` model will include methods for retrieving aggregated data and performing calculations (`Amt`).

```python
from django.db import models
from django.utils import timezone
from datetime import datetime

# --- Placeholder/Lookup Models (managed=False) ---
# These models represent tables that might exist in your legacy database
# and are referenced by BillBooking models. They are assumed to be
# created elsewhere or are just for lookup purposes in this context.

class Supplier(models.Model):
    id = models.IntegerField(db_column='SupplierId', primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    # Add other fields as necessary based on tblMM_Supplier_master
    regd_address = models.CharField(db_column='RegdAddress', max_length=255, null=True, blank=True)
    regd_country_id = models.IntegerField(db_column='RegdCountry', null=True, blank=True)
    regd_state_id = models.IntegerField(db_column='RegdState', null=True, blank=True)
    regd_city_id = models.IntegerField(db_column='RegdCity', null=True, blank=True)
    regd_pin_no = models.CharField(db_column='RegdPinNo', max_length=50, null=True, blank=True)
    ecc_no = models.CharField(db_column='EccNo', max_length=50, null=True, blank=True)
    divn = models.CharField(db_column='Divn', max_length=50, null=True, blank=True)
    tin_vat_no = models.CharField(db_column='TinVatNo', max_length=50, null=True, blank=True)
    range_val = models.CharField(db_column='Range', max_length=50, null=True, blank=True)
    commissionurate = models.CharField(db_column='Commissionurate', max_length=50, null=True, blank=True)
    tin_cst_no = models.CharField(db_column='TinCstNo', max_length=50, null=True, blank=True)
    tds_code = models.CharField(db_column='TDSCode', max_length=50, null=True, blank=True)
    pan_no = models.CharField(db_column='PanNo', max_length=50, null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.supplier_name

    def get_full_address(self):
        # This would typically join with Country, State, City models
        # For simplicity, returning just parts based on available data
        city_name = City.objects.filter(id=self.regd_city_id).first().city_name if self.regd_city_id else "N/A"
        state_name = State.objects.filter(id=self.regd_state_id).first().state_name if self.regd_state_id else "N/A"
        country_name = Country.objects.filter(id=self.regd_country_id).first().country_name if self.regd_country_id else "N/A"
        
        parts = [self.regd_address, city_name, state_name, country_name, self.regd_pin_no]
        return ",<br>".join(filter(None, parts))

class Country(models.Model):
    id = models.IntegerField(db_column='CId', primary_key=True)
    country_name = models.CharField(db_column='CountryName', max_length=100)
    class Meta:
        managed = False
        db_table = 'tblcountry'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'
    def __str__(self):
        return self.country_name

class State(models.Model):
    id = models.IntegerField(db_column='SId', primary_key=True)
    state_name = models.CharField(db_column='StateName', max_length=100)
    class Meta:
        managed = False
        db_table = 'tblState'
        verbose_name = 'State'
        verbose_name_plural = 'States'
    def __str__(self):
        return self.state_name

class City(models.Model):
    id = models.IntegerField(db_column='CityId', primary_key=True)
    city_name = models.CharField(db_column='CityName', max_length=100)
    class Meta:
        managed = False
        db_table = 'tblCity'
        verbose_name = 'City'
        verbose_name_plural = 'Cities'
    def __str__(self):
        return self.city_name

class PurchaseOrderMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    po_no = models.CharField(db_column='PONo', max_length=50)
    pr_spr_flag = models.CharField(db_column='PRSPRFlag', max_length=1, null=True, blank=True)
    # payment_terms = models.IntegerField(db_column='PaymentTerms', null=True, blank=True) # Assuming this is FK
    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'
    def __str__(self):
        return self.po_no

class PurchaseOrderDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(PurchaseOrderMaster, on_delete=models.DO_NOTHING, db_column='Mid', related_name='details', null=True, blank=True)
    po_no = models.CharField(db_column='PONo', max_length=50) # Redundant, but matching legacy schema
    pr_no = models.CharField(db_column='PRNo', max_length=50, null=True, blank=True)
    pr_id = models.IntegerField(db_column='PRId', null=True, blank=True)
    spr_no = models.CharField(db_column='SPRNo', max_length=50, null=True, blank=True)
    spr_id = models.IntegerField(db_column='SPRId', null=True, blank=True)
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=3, default=0.0)
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=3, default=0.0)
    class Meta:
        managed = False
        db_table = 'tblMM_PO_Details'
    def __str__(self):
        return f"PO Detail {self.id} for {self.po_no}"

class MaterialQualityMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    gqn_no = models.CharField(db_column='GQNNo', max_length=50)
    class Meta:
        managed = False
        db_table = 'tblQc_MaterialQuality_Master'
    def __str__(self):
        return self.gqn_no

class MaterialQualityDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(MaterialQualityMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details', null=True, blank=True)
    accepted_qty = models.DecimalField(db_column='AcceptedQty', max_digits=18, decimal_places=3, default=0.0)
    class Meta:
        managed = False
        db_table = 'tblQc_MaterialQuality_Details'
    def __str__(self):
        return f"MQ Detail {self.id}"

class MaterialServiceNoteMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    gsn_no = models.CharField(db_column='GSNNo', max_length=50)
    class Meta:
        managed = False
        db_table = 'tblinv_MaterialServiceNote_Master'
    def __str__(self):
        return self.gsn_no

class MaterialServiceNoteDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(MaterialServiceNoteMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details', null=True, blank=True)
    received_qty = models.DecimalField(db_column='ReceivedQty', max_digits=18, decimal_places=3, default=0.0)
    class Meta:
        managed = False
        db_table = 'tblinv_MaterialServiceNote_Details'
    def __str__(self):
        return f"MSN Detail {self.id}"

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50)
    description = models.CharField(db_column='ManfDesc', max_length=255)
    uom_basic_id = models.IntegerField(db_column='UOMBasic', null=True, blank=True) # FK to UnitMaster
    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
    def __str__(self):
        return self.item_code

class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    class Meta:
        managed = False
        db_table = 'Unit_Master'
    def __str__(self):
        return self.symbol

class PRMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    pr_no = models.CharField(db_column='PRNo', max_length=50)
    class Meta:
        managed = False
        db_table = 'tblMM_PR_Master'
    def __str__(self):
        return self.pr_no

class PRDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(PRMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details', null=True, blank=True)
    pr_no = models.CharField(db_column='PRNo', max_length=50) # Redundant
    wo_no = models.CharField(db_column='WONo', max_length=50, null=True, blank=True)
    ah_id = models.IntegerField(db_column='AHId', null=True, blank=True)
    class Meta:
        managed = False
        db_table = 'tblMM_PR_Details'
    def __str__(self):
        return f"PR Detail {self.id} for {self.pr_no}"

class SPRMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    spr_no = models.CharField(db_column='SPRNo', max_length=50)
    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Master'
    def __str__(self):
        return self.spr_no

class SPRDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(SPRMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details', null=True, blank=True)
    spr_no = models.CharField(db_column='SPRNo', max_length=50) # Redundant
    wo_no = models.CharField(db_column='WONo', max_length=50, null=True, blank=True)
    dept_id = models.IntegerField(db_column='DeptId', null=True, blank=True)
    ah_id = models.CharField(db_column='AHId', max_length=50, null=True, blank=True)
    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Details'
    def __str__(self):
        return f"SPR Detail {self.id} for {self.spr_no}"

class Department(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    class Meta:
        managed = False
        db_table = 'tblHR_Departments'
    def __str__(self):
        return self.symbol

# --- Main Bill Booking Models ---

class BillBookingMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.CharField(db_column='SysDate', max_length=10) # Stored as string in legacy
    sys_time = models.CharField(db_column='SysTime', max_length=10) # Stored as string in legacy
    session_id = models.CharField(db_column='SessionId', max_length=50)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    pvev_no = models.CharField(db_column='PVEVNo', max_length=50)
    supplier = models.ForeignKey(Supplier, on_delete=models.DO_NOTHING, db_column='SupplierId', null=True, blank=True)
    bill_no = models.CharField(db_column='BillNo', max_length=50)
    bill_date = models.DateField(db_column='BillDate')
    cenvat_entry_no = models.CharField(db_column='CENVATEntryNo', max_length=50)
    cenvat_entry_date = models.DateField(db_column='CENVATEntryDate')
    other_charges = models.DecimalField(db_column='OtherCharges', max_digits=18, decimal_places=3, default=0.0)
    other_cha_desc = models.CharField(db_column='OtherChaDesc', max_length=255)
    narration = models.TextField(db_column='Narration', null=True, blank=True)
    debit_amt = models.DecimalField(db_column='DebitAmt', max_digits=18, decimal_places=3, default=0.0)
    discount_type = models.CharField(db_column='DiscountType', max_length=1) # '0' for Amt, '1' for Per
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=3, default=0.0)
    # po_id = models.IntegerField(db_column='POId', null=True, blank=True) # Implicitly from original code, but not directly in select statement

    class Meta:
        managed = False
        db_table = 'tblACC_BillBooking_Master'
        verbose_name = 'Bill Booking'
        verbose_name_plural = 'Bill Bookings'

    def __str__(self):
        return f"{self.pvev_no} - {self.bill_no}"

    def update_timestamp(self, user_session_id):
        """Updates system date, time, and session ID on modification."""
        self.sys_date = timezone.now().strftime('%Y-%m-%d')
        self.sys_time = timezone.now().strftime('%H:%M:%S')
        self.session_id = user_session_id
        self.save(update_fields=['sys_date', 'sys_time', 'session_id'])


class BillBookingDetailManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().select_related(
            'item_master',
            'gqn_detail__master',
            'gsn_detail__master',
            'po_detail'
        )

    def with_calculated_amt(self):
        """
        Annotates the queryset with 'Amt' as calculated in the original ASP.NET code.
        This calculation is complex and involves multiple lookups from related tables.
        For simplicity and adherence to 'fat model', this logic is placed here,
        but real-world performance might require pre-computation or materialized views.
        """
        # This is a complex calculation that joins many tables.
        # In a real Django application, it might be better to denormalize `Amt`
        # or use a database function/view for efficiency if the amount is static.
        # For demonstration, we simulate the calculation logic here, assuming
        # related objects are accessible.
        
        # This manager method would ideally leverage database joins/aggregations
        # for performance. As a direct port of C# logic, we'll demonstrate
        # the conceptual data fetching path.
        
        # The 'Amt' calculation logic from C# loadData():
        # Rate = PODetails.Rate, Discount = PODetails.Discount
        # AccQty = MaterialQualityDetail.AcceptedQty (if GQNId used)
        # AccQty = MaterialServiceNoteDetail.ReceivedQty (if GSNId used)
        # Amt = ((Rate - (Rate * Discount) / 100) * AccQty)
        
        # Due to dynamic GQNId/GSNId logic for quantity and complex joins for Rate/Discount,
        # it's hard to express purely in Django ORM annotations for a single query.
        # A more robust solution might involve:
        # 1. A custom SQL view/function.
        # 2. Pre-calculating and storing 'Amt' during PO/GQN/GSN creation.
        # 3. Performing the calculation in a method on the BillBookingDetail instance
        #    after fetching related data.
        
        # For this exercise, we will return the queryset and have the calculation
        # happen on the instance, or simulate simplified annotation.
        
        # Given the complexity, a direct ORM annotation is not straightforward
        # due to the conditional quantity source (GQN vs GSN) and nested joins.
        # We will retrieve necessary related objects and calculate 'Amt' as a
        # property on the model instance in the template or a serializer.
        
        # For simplicity, we'll add necessary selects and assume the calculation
        # happens in the model's `calculated_amt` property.
        return self.get_queryset().select_related(
            'item_master__uom_basic', # for UOM
            'po_detail', # for Rate, Discount
            'gqn_detail__master', # for GQNNo, AcceptedQty
            'gsn_detail__master' # for GSNNo, ReceivedQty
        )


class BillBookingDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(BillBookingMaster, on_delete=models.CASCADE, db_column='MId', related_name='details')
    gqn_detail = models.ForeignKey(MaterialQualityDetail, on_delete=models.DO_NOTHING, db_column='GQNId', null=True, blank=True)
    gsn_detail = models.ForeignKey(MaterialServiceNoteDetail, on_delete=models.DO_NOTHING, db_column='GSNId', null=True, blank=True)
    item_master = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId', null=True, blank=True)
    po_detail = models.ForeignKey(PurchaseOrderDetail, on_delete=models.DO_NOTHING, db_column='PODId', null=True, blank=True)
    
    pf_amt = models.DecimalField(db_column='PFAmt', max_digits=18, decimal_places=3, default=0.0)
    ex_st_basic = models.DecimalField(db_column='ExStBasic', max_digits=18, decimal_places=3, default=0.0)
    ex_st_educess = models.DecimalField(db_column='ExStEducess', max_digits=18, decimal_places=3, default=0.0)
    ex_st_shecess = models.DecimalField(db_column='ExStShecess', max_digits=18, decimal_places=3, default=0.0)
    custom_duty = models.DecimalField(db_column='CustomDuty', max_digits=18, decimal_places=3, default=0.0)
    vat = models.DecimalField(db_column='VAT', max_digits=18, decimal_places=3, default=0.0)
    cst = models.DecimalField(db_column='CST', max_digits=18, decimal_places=3, default=0.0)
    freight = models.DecimalField(db_column='Freight', max_digits=18, decimal_places=3, default=0.0)
    tarrif_no = models.CharField(db_column='TarrifNo', max_length=50, null=True, blank=True) # It was double in C#, but often tariff numbers are strings
    ex_st_basic_in_per = models.DecimalField(db_column='ExStBasicInPer', max_digits=18, decimal_places=3, default=0.0)
    ex_st_educess_in_per = models.DecimalField(db_column='ExStEducessInPer', max_digits=18, decimal_places=3, default=0.0)
    ex_st_shecess_in_per = models.DecimalField(db_column='ExStShecessInPer', max_digits=18, decimal_places=3, default=0.0)

    objects = BillBookingDetailManager()

    class Meta:
        managed = False
        db_table = 'tblACC_BillBooking_Details'
        verbose_name = 'Bill Booking Detail'
        verbose_name_plural = 'Bill Booking Details'

    def __str__(self):
        return f"Detail for Bill Booking {self.master.pvev_no} - Item: {self.item_code}"
    
    @property
    def gqn_no(self):
        return self.gqn_detail.master.gqn_no if self.gqn_detail and self.gqn_detail.master else "NA"

    @property
    def gsn_no(self):
        return self.gsn_detail.master.gsn_no if self.gsn_detail and self.gsn_detail.master else "NA"

    @property
    def item_code(self):
        return self.item_master.item_code if self.item_master else "NA"

    @property
    def description(self):
        return self.item_master.description if self.item_master else "NA"

    @property
    def uom(self):
        return UnitMaster.objects.filter(id=self.item_master.uom_basic_id).first().symbol if self.item_master and self.item_master.uom_basic_id else "NA"

    @property
    def calculated_amt(self):
        """
        Calculates the 'Amt' as per the original C# logic.
        Requires po_detail, gqn_detail, gsn_detail to be prefetched/selected.
        """
        rate = self.po_detail.rate if self.po_detail else 0.0
        discount = self.po_detail.discount if self.po_detail else 0.0
        acc_qty = 0.0

        if self.gqn_detail:
            acc_qty = self.gqn_detail.accepted_qty
        elif self.gsn_detail:
            acc_qty = self.gsn_detail.received_qty

        # Ensure decimal type for calculations
        rate = float(rate)
        discount = float(discount)
        acc_qty = float(acc_qty)
        
        amt = ((rate - (rate * discount) / 100) * acc_qty)
        return round(amt, 2) # Round to 2 decimal places as per N2 format in C#

class BillBookingAttachment(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(BillBookingMaster, on_delete=models.CASCADE, db_column='MId', related_name='attachments')
    session_id = models.CharField(db_column='SessionId', max_length=50)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    file_name = models.CharField(db_column='FileName', max_length=255)
    file_size = models.DecimalField(db_column='FileSize', max_digits=18, decimal_places=3)
    content_type = models.CharField(db_column='ContentType', max_length=100)
    file_data = models.BinaryField(db_column='FileData')

    class Meta:
        managed = False
        db_table = 'tblACC_BillBooking_Attach_Master'
        verbose_name = 'Bill Booking Attachment'
        verbose_name_plural = 'Bill Booking Attachments'

    def __str__(self):
        return self.file_name
    
    def get_download_url(self):
        return f"/accounts/billbooking/attachment/download/{self.pk}/"

class BillBookingAttachmentTemp(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    session_id = models.CharField(db_column='SessionId', max_length=50)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    file_name = models.CharField(db_column='FileName', max_length=255)
    file_size = models.DecimalField(db_column='FileSize', max_digits=18, decimal_places=3)
    content_type = models.CharField(db_column='ContentType', max_length=100)
    file_data = models.BinaryField(db_column='FileData')

    class Meta:
        managed = False
        db_table = 'tblACC_BillBooking_Attach_Temp'
        verbose_name = 'Bill Booking Attachment Temp'
        verbose_name_plural = 'Bill Booking Attachments Temp'

    def __str__(self):
        return self.file_name

    def transfer_to_master(self, master_instance):
        """Transfers this temporary attachment to the permanent master table."""
        BillBookingAttachment.objects.create(
            master=master_instance,
            session_id=self.session_id,
            comp_id=self.comp_id,
            fin_year_id=self.fin_year_id,
            file_name=self.file_name,
            file_size=self.file_size,
            content_type=self.content_type,
            file_data=self.file_data
        )
        self.delete() # Delete from temporary table after transfer

```

#### 4.2 Forms (`accounts/forms.py`)

Forms for the main Bill Booking details and the editable line items.

```python
from django import forms
from django.core.exceptions import ValidationError
from .models import BillBookingMaster, BillBookingDetail, BillBookingAttachmentTemp

class BillBookingMasterForm(forms.ModelForm):
    bill_date = forms.DateField(
        widget=forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm datepicker', 'placeholder': 'dd-MM-yyyy'}),
        input_formats=['%d-%m-%Y'],
        required=True
    )
    cenvat_entry_date = forms.DateField(
        widget=forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm datepicker', 'placeholder': 'dd-MM-yyyy'}),
        input_formats=['%d-%m-%Y'],
        required=True
    )
    
    class Meta:
        model = BillBookingMaster
        fields = [
            'bill_no', 'bill_date', 'cenvat_entry_no', 'cenvat_entry_date',
            'other_charges', 'other_cha_desc', 'narration', 'debit_amt',
            'discount_type', 'discount'
        ]
        widgets = {
            'bill_no': forms.TextInput(attrs={'class': 'box3'}),
            'cenvat_entry_no': forms.TextInput(attrs={'class': 'box3'}),
            'other_charges': forms.NumberInput(attrs={'class': 'box3'}),
            'other_cha_desc': forms.TextInput(attrs={'class': 'box3'}),
            'narration': forms.Textarea(attrs={'class': 'box3', 'rows': 4, 'cols': 50}),
            'debit_amt': forms.NumberInput(attrs={'class': 'box3'}),
            'discount_type': forms.Select(attrs={'class': 'box3'}, choices=[('0', 'Amt(Rs)'), ('1', 'Per(%)')]),
            'discount': forms.NumberInput(attrs={'class': 'box3', 'placeholder': '0.00'}),
        }

    def clean_bill_date(self):
        bill_date = self.cleaned_data['bill_date']
        # Add additional date validation if needed, e.g., not in future
        return bill_date

    def clean_cenvat_entry_date(self):
        cenvat_entry_date = self.cleaned_data['cenvat_entry_date']
        # Add additional date validation if needed
        return cenvat_entry_date

    def clean(self):
        cleaned_data = super().clean()
        # Example of cross-field validation, if any was in ASP.NET
        return cleaned_data


class BillBookingDetailForm(forms.ModelForm):
    class Meta:
        model = BillBookingDetail
        fields = [
            'pf_amt', 'ex_st_basic_in_per', 'ex_st_educess_in_per',
            'ex_st_shecess_in_per', 'ex_st_basic', 'ex_st_educess',
            'ex_st_shecess', 'custom_duty', 'vat', 'cst', 'freight',
            'tarrif_no'
        ]
        widgets = {
            'pf_amt': forms.NumberInput(attrs={'class': 'box3 w-full', 'step': '0.01'}),
            'ex_st_basic_in_per': forms.NumberInput(attrs={'class': 'box3 w-full', 'step': '0.01'}),
            'ex_st_educess_in_per': forms.NumberInput(attrs={'class': 'box3 w-full', 'step': '0.01'}),
            'ex_st_shecess_in_per': forms.NumberInput(attrs={'class': 'box3 w-full', 'step': '0.01'}),
            'ex_st_basic': forms.NumberInput(attrs={'class': 'box3 w-full', 'step': '0.01'}),
            'ex_st_educess': forms.NumberInput(attrs={'class': 'box3 w-full', 'step': '0.01'}),
            'ex_st_shecess': forms.NumberInput(attrs={'class': 'box3 w-full', 'step': '0.01'}),
            'custom_duty': forms.NumberInput(attrs={'class': 'box3 w-full', 'step': '0.01'}),
            'vat': forms.NumberInput(attrs={'class': 'box3 w-full', 'step': '0.01'}),
            'cst': forms.NumberInput(attrs={'class': 'box3 w-full', 'step': '0.01'}),
            'freight': forms.NumberInput(attrs={'class': 'box3 w-full', 'step': '0.01'}),
            'tarrif_no': forms.TextInput(attrs={'class': 'box3 w-full'}), # Changed to TextInput based on C#
        }
    
    def clean_tarrif_no(self):
        # The C# regex was commented out, implying it could be alphanumeric.
        # If it needs to be numeric, use float() conversion and catch ValueError.
        tarrif_no = self.cleaned_data['tarrif_no']
        if not tarrif_no:
            raise ValidationError("Tarrif No. is required.")
        # If it needs to be purely numeric like other fields, uncomment:
        # try:
        #     float(tarrif_no)
        # except ValueError:
        #     raise ValidationError("Invalid format for Tarrif No.")
        return tarrif_no


class BillBookingAttachmentUploadForm(forms.ModelForm):
    file = forms.FileField(
        label="Select File",
        widget=forms.FileInput(attrs={'class': 'block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'}),
        required=True
    )
    
    class Meta:
        model = BillBookingAttachmentTemp # Use temp model for upload
        fields = ['file']
        
    def save(self, commit=True, **kwargs):
        # This method is simplified. In a real application, you'd handle
        # file data reading and saving to the BinaryField properly.
        # For a BinaryField, you'd typically read the file into bytes.
        file_data = self.cleaned_data['file']
        instance = super().save(commit=False)
        instance.file_name = file_data.name
        instance.file_size = file_data.size
        instance.content_type = file_data.content_type
        
        # Read file into binary
        instance.file_data = file_data.read()
        
        # Set session, company, fin year from request (passed via kwargs)
        instance.session_id = kwargs.get('session_id')
        instance.comp_id = kwargs.get('comp_id')
        instance.fin_year_id = kwargs.get('fin_year_id')
        
        if commit:
            instance.save()
        return instance

```

#### 4.3 Views (`accounts/views.py`)

These views will handle the rendering of the main page, the dynamic table content, and the HTMX-driven form submissions for updates and file uploads/deletions.

```python
from django.views.generic import TemplateView, UpdateView, View
from django.urls import reverse_lazy
from django.shortcuts import get_object_or_404, redirect
from django.http import HttpResponse, JsonResponse, Http404
from django.template.loader import render_to_string
from django.contrib import messages
from django.db import transaction
import mimetypes

from .models import (
    BillBookingMaster, BillBookingDetail, BillBookingAttachment, BillBookingAttachmentTemp,
    Supplier, PurchaseOrderMaster, PurchaseOrderDetail, PRDetail, SPRDetail, Department
)
from .forms import BillBookingMasterForm, BillBookingDetailForm, BillBookingAttachmentUploadForm

class BillBookingEditView(TemplateView):
    """
    Main view for editing Bill Booking details.
    Loads master data, supplier info, and orchestrates partial loads via HTMX.
    """
    template_name = 'accounts/billbooking/billbooking_edit.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        bill_booking_id = self.kwargs.get('pk')
        bill_booking = get_object_or_404(BillBookingMaster, pk=bill_booking_id)
        
        context['bill_booking'] = bill_booking
        context['master_form'] = BillBookingMasterForm(instance=bill_booking)
        context['attachment_form'] = BillBookingAttachmentUploadForm()

        # Load supplier details and PO/WO/Dept info, similar to C# Page_Load
        supplier = bill_booking.supplier
        if supplier:
            context['supplier_name'] = supplier.supplier_name
            context['supplier_address'] = supplier.get_full_address()
            context['ecc_no'] = supplier.ecc_no
            context['division'] = supplier.divn
            context['vat_no'] = supplier.tin_vat_no
            context['range_val'] = supplier.range_val
            context['commissionerate'] = supplier.commissionurate
            context['cst_no'] = supplier.tin_cst_no
            context['service_tax'] = '-' # No direct mapping in models, assumed from C#
            context['tds'] = supplier.tds_code
            context['pan_no'] = supplier.pan_no

        # For PO Number and WO/Dept Number
        # This logic is complex in C# involving multiple joins and flags
        # Simplified here. In a real system, PO could link directly to BillBookingMaster.
        # Assuming we can find relevant PO/PR/SPR details through relationships or pre-existing logic
        po_master = PurchaseOrderMaster.objects.filter(details__id=bill_booking.details.first().po_detail.id if bill_booking.details.first() and bill_booking.details.first().po_detail else None).first()
        context['po_no'] = po_master.po_no if po_master else "N/A"

        wo_dept_no = "N/A"
        if po_master:
            po_detail = PurchaseOrderDetail.objects.filter(master=po_master).first()
            if po_detail:
                if po_master.pr_spr_flag == '0' and po_detail.pr_no: # PR flag
                    pr_detail = PRDetail.objects.filter(pr_no=po_detail.pr_no).first()
                    if pr_detail:
                        wo_dept_no = pr_detail.wo_no
                elif po_master.pr_spr_flag == '1' and po_detail.spr_no: # SPR flag
                    spr_detail = SPRDetail.objects.filter(spr_no=po_detail.spr_no).first()
                    if spr_detail:
                        if spr_detail.dept_id != 0:
                            dept = Department.objects.filter(pk=spr_detail.dept_id).first()
                            wo_dept_no = dept.symbol if dept else "N/A"
                        else:
                            wo_dept_no = spr_detail.wo_no

        context['wo_dept_no'] = wo_dept_no
        
        # Add necessary session variables for forms and attachments if needed later for default values
        context['comp_id'] = self.request.session.get('compid')
        context['fin_year_id'] = self.request.session.get('finyear')
        context['session_id'] = self.request.session.get('username')

        return context
    
    def post(self, request, pk):
        """Handles the main Bill Booking Master form submission."""
        bill_booking = get_object_or_404(BillBookingMaster, pk=pk)
        master_form = BillBookingMasterForm(request.POST, instance=bill_booking)

        if master_form.is_valid():
            with transaction.atomic():
                master_instance = master_form.save(commit=False)
                # Update SysDate, SysTime, SessionId as in C#
                master_instance.update_timestamp(request.session.get('username'))
                master_instance.save()

                # Transfer temporary attachments to master
                temp_attachments = BillBookingAttachmentTemp.objects.filter(
                    comp_id=request.session.get('compid'),
                    fin_year_id=request.session.get('finyear'),
                    session_id=request.session.get('username')
                )
                for temp_att in temp_attachments:
                    temp_att.transfer_to_master(master_instance)

                messages.success(request, 'Bill Booking details updated successfully.')
                # HTMX response for success, trigger client-side toast and redirect
                return HttpResponse(status=204, headers={'HX-Trigger': '{"showToast": {"message": "Bill Booking updated!", "type": "success"}, "redirect": "/accounts/billbooking/list/"}'})
        else:
            # If form is invalid, re-render the form with errors
            # This requires the main view to be able to render just the form content
            # or rely on HTMX to target specific sections.
            # For simplicity, we'll return a partial with errors, assuming
            # the HTMX request for the form was to a modal.
            
            # This is a bit tricky with the multi-tab layout. We are assuming
            # the main form is submitted, and if there are errors, they should be visible
            # on the form. If this was a full-page reload, it would just re-render.
            # With HTMX, the submission is typically handled by the element doing the hx-post.
            
            # To handle errors gracefully with HTMX, the POST should return the form
            # with errors, and HTMX should swap it back into the modal/original position.
            context = self.get_context_data(pk=pk) # Re-get context to fill labels etc.
            context['master_form'] = master_form # Replace with invalid form
            html = render_to_string(
                'accounts/billbooking/_billbooking_master_form.html', 
                context, 
                request=request
            )
            return HttpResponse(html)


class BillBookingDetailTablePartialView(View):
    """
    Renders the PO Term Details (GridView1) as an HTMX partial.
    This view will be called via HTMX to load the table.
    """
    def get(self, request, pk):
        bill_booking = get_object_or_404(BillBookingMaster, pk=pk)
        details = bill_booking.details.all().with_calculated_amt() # Using custom manager method

        context = {
            'bill_booking_details': details,
            'bill_booking_master_id': pk
        }
        return render(request, 'accounts/billbooking/_billbooking_detail_table.html', context)

class BillBookingDetailUpdatePartialView(UpdateView):
    """
    Handles inline editing for a single BillBookingDetail row.
    Receives HTMX POST requests to update a row.
    """
    model = BillBookingDetail
    form_class = BillBookingDetailForm
    template_name = 'accounts/billbooking/_billbooking_detail_row_edit.html' # Rendered on GET for edit mode
    
    def get_success_url(self):
        return reverse_lazy('billbooking_edit', kwargs={'pk': self.object.master.pk})

    def get(self, request, *args, **kwargs):
        """Render the form for editing a specific row."""
        self.object = self.get_object()
        form = self.get_form()
        return render(request, self.template_name, {'form': form, 'object': self.object})

    def form_valid(self, form):
        """
        Handles valid form submission for a single detail row.
        Updates master's timestamp and saves the detail.
        """
        response = super().form_valid(form)
        
        # Update BillBookingMaster's SysDate, SysTime, SessionId
        master_instance = self.object.master
        master_instance.update_timestamp(self.request.session.get('username'))

        messages.success(self.request, 'PO Term Detail updated successfully.')
        
        # After successful update, render the non-editable row for HTMX swap
        # This will swap the form back to the display view of the row
        context = {'obj': self.object, 'forloop_counter': self.request.GET.get('row_idx')} # row_idx passed via HTMX
        html_row = render_to_string('accounts/billbooking/_billbooking_detail_row_display.html', context, request=self.request)
        return HttpResponse(html_row, headers={'HX-Trigger': '{"showToast": {"message": "Detail updated!", "type": "success"}}'})

    def form_invalid(self, form):
        """Handles invalid form submission for a single detail row."""
        self.object = self.get_object() # Ensure object is available
        return render(self.request, self.template_name, {'form': form, 'object': self.object}, status=400)


class BillBookingAttachmentTablePartialView(View):
    """
    Renders the Attachments (GridView2) as an HTMX partial.
    """
    def get(self, request, pk):
        bill_booking = get_object_or_404(BillBookingMaster, pk=pk)
        attachments = BillBookingAttachment.objects.filter(
            master=bill_booking,
            comp_id=request.session.get('compid'),
            fin_year_id=request.session.get('finyear')
        )
        # Also include temporary attachments for display during current session
        temp_attachments = BillBookingAttachmentTemp.objects.filter(
            comp_id=request.session.get('compid'),
            fin_year_id=request.session.get('finyear'),
            session_id=request.session.get('username')
        )
        context = {
            'attachments': attachments,
            'temp_attachments': temp_attachments,
            'bill_booking_master_id': pk # Pass master ID for URL generation
        }
        return render(request, 'accounts/billbooking/_billbooking_attachment_table.html', context)

class BillBookingAttachmentUploadView(View):
    """
    Handles file uploads to temporary storage (tblACC_BillBooking_Attach_Temp) via HTMX.
    """
    def post(self, request, pk):
        form = BillBookingAttachmentUploadForm(request.POST, request.FILES)
        if form.is_valid():
            try:
                # Pass necessary session/company/fin_year data to form's save method
                form.save(
                    session_id=request.session.get('username'),
                    comp_id=request.session.get('compid'),
                    fin_year_id=request.session.get('finyear')
                )
                messages.success(request, 'File uploaded temporarily.')
                # Trigger a refresh of the attachment table
                return HttpResponse(status=204, headers={'HX-Trigger': '{"refreshAttachmentList":true, "showToast": {"message": "Attachment uploaded!", "type": "success"}}'})
            except Exception as e:
                messages.error(request, f"Error uploading file: {e}")
                return HttpResponse(f"<div class='text-red-500'>Error: {e}</div>", status=400)
        else:
            # If form is invalid, return the form with errors
            html_form = render_to_string(
                'accounts/billbooking/_billbooking_attachment_upload_form.html',
                {'attachment_form': form},
                request=request
            )
            return HttpResponse(html_form, status=400) # Bad Request

class BillBookingAttachmentDeleteView(View):
    """
    Handles deletion of attachments via HTMX.
    """
    def delete(self, request, pk, attachment_id):
        # Determine if it's a temp or master attachment
        is_temp = request.GET.get('is_temp', 'false').lower() == 'true'

        try:
            if is_temp:
                attachment = get_object_or_404(BillBookingAttachmentTemp, pk=attachment_id)
            else:
                attachment = get_object_or_404(BillBookingAttachment, pk=attachment_id)
            
            attachment.delete()
            messages.success(request, 'Attachment deleted successfully.')
            # Trigger a refresh of the attachment table
            return HttpResponse(status=204, headers={'HX-Trigger': '{"refreshAttachmentList":true, "showToast": {"message": "Attachment deleted!", "type": "success"}}'})
        except Http404:
            messages.error(request, 'Attachment not found.')
            return HttpResponse(status=404)
        except Exception as e:
            messages.error(request, f"Error deleting attachment: {e}")
            return HttpResponse(status=500)

class BillBookingAttachmentDownloadView(View):
    """
    Handles file downloads for attachments.
    Mimics ~/Controls/DownloadFile.aspx.
    """
    def get(self, request, pk, attachment_id):
        attachment = get_object_or_404(BillBookingAttachment, pk=attachment_id)
        
        response = HttpResponse(attachment.file_data, content_type=attachment.content_type)
        response['Content-Disposition'] = f'attachment; filename="{attachment.file_name}"'
        response['Content-Length'] = attachment.file_size
        return response

class BillBookingCancelView(View):
    """
    Handles the cancel action, redirecting back to the list page.
    """
    def get(self, request, *args, **kwargs):
        # Redirect to the main BillBooking list page (assuming this exists)
        messages.info(request, 'Operation cancelled.')
        return redirect(reverse_lazy('billbooking_list')) # Assuming 'billbooking_list' URL exists

```

#### 4.4 Templates (`accounts/templates/accounts/billbooking/`)

**`billbooking_edit.html` (Main Page Template)**
This is the main template that orchestrates the tabs and uses HTMX to load table data dynamically.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-gray-800 text-white p-4 rounded-t-lg mb-4">
        <h2 class="text-xl font-bold">Bill Booking - Edit</h2>
    </div>

    <div x-data="{ activeTab: 'details' }" class="bg-white p-6 rounded-b-lg shadow-md">
        <!-- Tab Headers -->
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <a @click.prevent="activeTab = 'details'"
                   :class="activeTab === 'details' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                   class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm cursor-pointer">
                    PV/EV Booking Details
                </a>
                <a @click.prevent="activeTab = 'po_terms'"
                   :class="activeTab === 'po_terms' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                   class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm cursor-pointer">
                    PO Term Details
                </a>
                <a @click.prevent="activeTab = 'terms_conditions'"
                   :class="activeTab === 'terms_conditions' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                   class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm cursor-pointer">
                    Terms &amp; Conditions
                </a>
            </nav>
        </div>

        <!-- Tab Content -->
        <div class="mt-6">
            <!-- PV/EV Booking Details Tab -->
            <div x-show="activeTab === 'details'" class="space-y-6">
                <form id="masterForm" hx-post="{% url 'billbooking_edit' bill_booking.pk %}" hx-swap="outerHTML" hx-target="#masterForm" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {% csrf_token %}
                    <div class="col-span-1 md:col-span-2 p-4 border border-gray-300 rounded-md">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label class="block text-sm font-bold text-gray-700">PV/EV No.</label>
                                <p class="mt-1 text-sm text-gray-900">{{ bill_booking.pvev_no }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-bold text-gray-700">PO No.</label>
                                <p class="mt-1 text-sm text-gray-900">{{ po_no }}</p>
                            </div>
                            <div>
                                <label for="{{ master_form.bill_no.id_for_label }}" class="block text-sm font-bold text-gray-700">Bill No.</label>
                                {{ master_form.bill_no }}
                                {% if master_form.bill_no.errors %}<p class="text-red-500 text-xs mt-1">{{ master_form.bill_no.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ master_form.bill_date.id_for_label }}" class="block text-sm font-bold text-gray-700">Bill Date</label>
                                {{ master_form.bill_date }}
                                {% if master_form.bill_date.errors %}<p class="text-red-500 text-xs mt-1">{{ master_form.bill_date.errors }}</p>{% endif %}
                            </div>
                            <div class="row-span-2">
                                <label class="block text-sm font-bold text-gray-700">Supplier</label>
                                <p class="mt-1 text-sm text-gray-900">{{ supplier_name }}</p>
                            </div>
                            <div></div>
                            <div>
                                <label for="{{ master_form.cenvat_entry_no.id_for_label }}" class="block text-sm font-bold text-gray-700">CenVat Entry No.</label>
                                {{ master_form.cenvat_entry_no }}
                                {% if master_form.cenvat_entry_no.errors %}<p class="text-red-500 text-xs mt-1">{{ master_form.cenvat_entry_no.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ master_form.cenvat_entry_date.id_for_label }}" class="block text-sm font-bold text-gray-700">CenVat Entry Date</label>
                                {{ master_form.cenvat_entry_date }}
                                {% if master_form.cenvat_entry_date.errors %}<p class="text-red-500 text-xs mt-1">{{ master_form.cenvat_entry_date.errors }}</p>{% endif %}
                            </div>
                            <div class="row-span-3">
                                <label class="block text-sm font-bold text-gray-700">Supplier Address</label>
                                <p class="mt-1 text-sm text-gray-900" x-html="`{{ supplier_address|escapejs }}`"></p>
                            </div>
                            <div></div>
                            <div>
                                <label class="block text-sm font-bold text-gray-700">WO No/ Dept. Code</label>
                                <p class="mt-1 text-sm text-gray-900">{{ wo_dept_no }}</p>
                            </div>
                            <div></div>
                        </div>
                    </div>

                    <div class="col-span-1 md:col-span-2 p-4 border border-gray-300 rounded-md">
                        <h4 class="text-md font-bold mb-2">Supplier Details</h4>
                        <div class="grid grid-cols-1 md:grid-cols-6 gap-4 text-sm text-gray-900">
                            <div>ECC No.</div><div class="font-normal">{{ ecc_no }}</div>
                            <div>Range</div><div class="font-normal">{{ range_val }}</div>
                            <div>Service Tax No.</div><div class="font-normal">{{ service_tax }}</div>

                            <div>Division</div><div class="font-normal">{{ division }}</div>
                            <div>Commissionerate</div><div class="font-normal">{{ commissionerate }}</div>
                            <div>TDS</div><div class="font-normal">{{ tds }}</div>

                            <div>Vat No.</div><div class="font-normal">{{ vat_no }}</div>
                            <div>CST No.</div><div class="font-normal">{{ cst_no }}</div>
                            <div>Pan No.</div><div class="font-normal">{{ pan_no }}</div>
                        </div>
                    </div>

                    <div class="col-span-1 md:col-span-2 p-4 border border-gray-300 rounded-md">
                        <h4 class="text-md font-bold mb-2">Attachment:</h4>
                        <div class="flex items-center space-x-2 mb-4" id="attachmentUploadForm" hx-target="#attachmentUploadForm">
                            {{ attachment_form.file }}
                            <button type="submit" 
                                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                                hx-post="{% url 'billbooking_attachment_upload' bill_booking.pk %}"
                                hx-encoding="multipart/form-data"
                                hx-swap="none"
                                hx-target="#attachmentUploadForm"
                                hx-indicator="#upload-indicator"
                                onclick="return confirm('Are you sure you want to upload this file?');"
                            >
                                Upload
                            </button>
                            <span id="upload-indicator" class="htmx-indicator ml-2">
                                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div> Uploading...
                            </span>
                            {% if attachment_form.file.errors %}
                                <p class="text-red-500 text-xs mt-1">{{ attachment_form.file.errors }}</p>
                            {% endif %}
                        </div>
                        
                        <div id="attachmentTableContainer"
                             hx-trigger="load, refreshAttachmentList from:body"
                             hx-get="{% url 'billbooking_attachment_table' bill_booking.pk %}"
                             hx-swap="innerHTML">
                            <!-- Attachments DataTable will be loaded here via HTMX -->
                            <div class="text-center">
                                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                                <p class="mt-2">Loading attachments...</p>
                            </div>
                        </div>
                    </div>
                    <!-- Master form submit buttons are outside this form. This is handled below -->
                </form>
            </div>

            <!-- PO Term Details Tab -->
            <div x-show="activeTab === 'po_terms'" class="space-y-6">
                <div id="poTermDetailsTableContainer"
                     hx-trigger="load, refreshBillBookingDetailList from:body"
                     hx-get="{% url 'billbooking_detail_table' bill_booking.pk %}"
                     hx-swap="innerHTML">
                    <!-- PO Term Details DataTable will be loaded here via HTMX -->
                    <div class="text-center">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p class="mt-2">Loading PO Term Details...</p>
                    </div>
                </div>
            </div>

            <!-- Terms & Conditions Tab -->
            <div x-show="activeTab === 'terms_conditions'" class="space-y-6">
                <form id="termsForm" hx-post="{% url 'billbooking_edit' bill_booking.pk %}" hx-swap="outerHTML" hx-target="#masterForm" class="p-4 border border-gray-300 rounded-md">
                    {% csrf_token %}
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="{{ master_form.other_charges.id_for_label }}" class="block text-sm font-bold text-gray-700">Other Charges</label>
                            {{ master_form.other_charges }}
                            {% if master_form.other_charges.errors %}<p class="text-red-500 text-xs mt-1">{{ master_form.other_charges.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ master_form.other_cha_desc.id_for_label }}" class="block text-sm font-bold text-gray-700">Other Cha. Desc.</label>
                            {{ master_form.other_cha_desc }}
                            {% if master_form.other_cha_desc.errors %}<p class="text-red-500 text-xs mt-1">{{ master_form.other_cha_desc.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ master_form.debit_amt.id_for_label }}" class="block text-sm font-bold text-gray-700">Debit Amt</label>
                            {{ master_form.debit_amt }}
                            {% if master_form.debit_amt.errors %}<p class="text-red-500 text-xs mt-1">{{ master_form.debit_amt.errors }}</p>{% endif %}
                        </div>
                        <div>
                            <label for="{{ master_form.discount.id_for_label }}" class="block text-sm font-bold text-gray-700">Discount</label>
                            <div class="flex space-x-2">
                                {{ master_form.discount }}
                                {{ master_form.discount_type }}
                            </div>
                            {% if master_form.discount.errors %}<p class="text-red-500 text-xs mt-1">{{ master_form.discount.errors }}</p>{% endif %}
                            {% if master_form.discount_type.errors %}<p class="text-red-500 text-xs mt-1">{{ master_form.discount_type.errors }}</p>{% endif %}
                        </div>
                        <div class="col-span-2">
                            <label for="{{ master_form.narration.id_for_label }}" class="block text-sm font-bold text-gray-700">Narration</label>
                            {{ master_form.narration }}
                            {% if master_form.narration.errors %}<p class="text-red-500 text-xs mt-1">{{ master_form.narration.errors }}</p>{% endif %}
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="mt-6 flex justify-center space-x-4">
        <button 
            type="submit" 
            form="masterForm" 
            class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-6 rounded"
            hx-indicator="#global-spinner"
            onclick="return confirm('Are you sure you want to update Bill Booking details?');"
            >
            Update
        </button>
        <button 
            type="button" 
            class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-6 rounded"
            onclick="window.location.href='{% url 'billbooking_cancel' %}'"
            >
            Cancel
        </button>
    </div>
</div>

<!-- Modal for detail row editing and attachment deletion -->
<div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
     _="on click if event.target.id == 'modal' remove .is-active from me">
    <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full">
        <!-- Content will be loaded here via HTMX -->
    </div>
</div>

<!-- Global spinner for HTMX -->
<div id="global-spinner" class="htmx-indicator fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
</div>

{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<link href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css" rel="stylesheet">
<link href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize date pickers for the main form
        flatpickr(".datepicker", {
            dateFormat: "d-m-Y",
            allowInput: true,
            altInput: true,
            altFormat: "d-m-Y"
        });

        // Event listener for HTMX triggers
        document.body.addEventListener('htmx:afterSwap', function(evt) {
            // Re-initialize DataTables if a table is swapped in
            if (evt.detail.target.id === 'poTermDetailsTableContainer' || evt.detail.target.id === 'attachmentTableContainer') {
                // Destroy existing DataTable instance if it exists
                if ($.fn.DataTable.isDataTable('#billBookingDetailTable')) {
                    $('#billBookingDetailTable').DataTable().destroy();
                }
                if ($.fn.DataTable.isDataTable('#billBookingAttachmentTable')) {
                    $('#billBookingAttachmentTable').DataTable().destroy();
                }
                // Initialize new DataTables
                $('#billBookingDetailTable').DataTable({
                    "pageLength": 15,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "ordering": true,
                    "searching": true,
                    "paging": true
                });
                 $('#billBookingAttachmentTable').DataTable({
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "ordering": false,
                    "searching": false,
                    "paging": false // Attachments grid from ASP.NET was fixed height and scrollable
                });
            }
            // Re-initialize date pickers if a form containing them is swapped in (e.g., in modal)
            if (evt.detail.target.id === 'modalContent') {
                flatpickr("#modalContent .datepicker", {
                    dateFormat: "d-m-Y",
                    allowInput: true,
                    altInput: true,
                    altFormat: "d-m-Y"
                });
            }
        });
        
        // HTMX on success for non-swapped elements (like main form update)
        document.body.addEventListener('htmx:afterRequest', function(evt) {
            const hxTrigger = evt.detail.xhr.getResponseHeader('HX-Trigger');
            if (hxTrigger) {
                const triggerData = JSON.parse(hxTrigger);
                if (triggerData.showToast) {
                    // Alpine.js toast display logic (assuming this is part of base.html)
                    // Example: window.dispatch('showToast', triggerData.showToast);
                    alert(`Toast: ${triggerData.showToast.message}`); // Fallback
                }
                if (triggerData.redirect) {
                    window.location.href = triggerData.redirect;
                }
            }
        });
        
        // Alpine.js for modal show/hide based on HTMX-driven content
        document.body.addEventListener('htmx:beforeSwap', function(evt) {
            if (evt.detail.target.id === 'modalContent' && evt.detail.xhr.status < 400) {
                document.getElementById('modal').classList.add('is-active');
                document.getElementById('modal').classList.remove('hidden');
            }
        });
        
        document.body.addEventListener('htmx:afterSettle', function(evt) {
            if (evt.detail.target.id === 'modalContent' && evt.detail.xhr.status >= 400) {
                 // If an HTMX request returned an error (e.g., validation errors) and it's for modal content,
                 // ensure the modal is still visible
                document.getElementById('modal').classList.add('is-active');
                document.getElementById('modal').classList.remove('hidden');
            }
        });
    });

    // Custom function to close modal if needed
    window.closeModal = () => {
        document.getElementById('modal').classList.remove('is-active');
        document.getElementById('modal').classList.add('hidden');
        document.getElementById('modalContent').innerHTML = ''; // Clear modal content
    }
</script>
{% endblock %}
```

**`_billbooking_master_form.html` (Partial for main form - used for re-rendering form with errors)**
This partial is specifically for the main form part of `billbooking_edit.html`, allowing HTMX to swap it if there are validation errors.

```html
{% comment %}
    This partial contains the main Bill Booking Master form section.
    It's intended to be swapped by HTMX if there are validation errors
    on the master form submission.
{% endcomment %}
<form id="masterForm" hx-post="{% url 'billbooking_edit' bill_booking.pk %}" hx-swap="outerHTML" hx-target="#masterForm" class="grid grid-cols-1 md:grid-cols-2 gap-4">
    {% csrf_token %}
    <div class="col-span-1 md:col-span-2 p-4 border border-gray-300 rounded-md">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-bold text-gray-700">PV/EV No.</label>
                <p class="mt-1 text-sm text-gray-900">{{ bill_booking.pvev_no }}</p>
            </div>
            <div>
                <label class="block text-sm font-bold text-gray-700">PO No.</label>
                <p class="mt-1 text-sm text-gray-900">{{ po_no }}</p>
            </div>
            <div>
                <label for="{{ master_form.bill_no.id_for_label }}" class="block text-sm font-bold text-gray-700">Bill No.</label>
                {{ master_form.bill_no }}
                {% if master_form.bill_no.errors %}<p class="text-red-500 text-xs mt-1">{{ master_form.bill_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ master_form.bill_date.id_for_label }}" class="block text-sm font-bold text-gray-700">Bill Date</label>
                {{ master_form.bill_date }}
                {% if master_form.bill_date.errors %}<p class="text-red-500 text-xs mt-1">{{ master_form.bill_date.errors }}</p>{% endif %}
            </div>
            <div class="row-span-2">
                <label class="block text-sm font-bold text-gray-700">Supplier</label>
                <p class="mt-1 text-sm text-gray-900">{{ supplier_name }}</p>
            </div>
            <div></div>
            <div>
                <label for="{{ master_form.cenvat_entry_no.id_for_label }}" class="block text-sm font-bold text-gray-700">CenVat Entry No.</label>
                {{ master_form.cenvat_entry_no }}
                {% if master_form.cenvat_entry_no.errors %}<p class="text-red-500 text-xs mt-1">{{ master_form.cenvat_entry_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ master_form.cenvat_entry_date.id_for_label }}" class="block text-sm font-bold text-gray-700">CenVat Entry Date</label>
                {{ master_form.cenvat_entry_date }}
                {% if master_form.cenvat_entry_date.errors %}<p class="text-red-500 text-xs mt-1">{{ master_form.cenvat_entry_date.errors }}</p>{% endif %}
            </div>
            <div class="row-span-3">
                <label class="block text-sm font-bold text-gray-700">Supplier Address</label>
                <p class="mt-1 text-sm text-gray-900" x-html="`{{ supplier_address|escapejs }}`"></p>
            </div>
            <div></div>
            <div>
                <label class="block text-sm font-bold text-gray-700">WO No/ Dept. Code</label>
                <p class="mt-1 text-sm text-gray-900">{{ wo_dept_no }}</p>
            </div>
            <div></div>
        </div>
    </div>

    <div class="col-span-1 md:col-span-2 p-4 border border-gray-300 rounded-md">
        <h4 class="text-md font-bold mb-2">Supplier Details</h4>
        <div class="grid grid-cols-1 md:grid-cols-6 gap-4 text-sm text-gray-900">
            <div>ECC No.</div><div class="font-normal">{{ ecc_no }}</div>
            <div>Range</div><div class="font-normal">{{ range_val }}</div>
            <div>Service Tax No.</div><div class="font-normal">{{ service_tax }}</div>

            <div>Division</div><div class="font-normal">{{ division }}</div>
            <div>Commissionerate</div><div class="font-normal">{{ commissionerate }}</div>
            <div>TDS</div><div class="font-normal">{{ tds }}</div>

            <div>Vat No.</div><div class="font-normal">{{ vat_no }}</div>
            <div>CST No.</div><div class="font-normal">{{ cst_no }}</div>
            <div>Pan No.</div><div class="font-normal">{{ pan_no }}</div>
        </div>
    </div>
    <!-- Note: Attachment and Terms & Conditions forms are handled separately as they are distinct logical sections/tabs -->
</form>
```

**`_billbooking_detail_table.html` (PO Term Details Table Partial)**
This partial will be loaded into the "PO Term Details" tab via HTMX.

```html
<div class="overflow-x-auto">
    <table id="billBookingDetailTable" class="min-w-full bg-white border-collapse border border-gray-200">
        <thead>
            <tr class="bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <th class="py-2 px-4 border border-gray-200">SN</th>
                <th class="py-2 px-4 border border-gray-200">GQN No</th>
                <th class="py-2 px-4 border border-gray-200">GSN No</th>
                <th class="py-2 px-4 border border-gray-200">Item Code</th>
                <th class="py-2 px-4 border border-gray-200">Description</th>
                <th class="py-2 px-4 border border-gray-200">UOM</th>
                <th class="py-2 px-4 border border-gray-200 text-right">Amt</th>
                <th class="py-2 px-4 border border-gray-200 text-right">PF Amt</th>
                <th class="py-2 px-4 border border-gray-200 text-right">Basic SerTax(%)</th>
                <th class="py-2 px-4 border border-gray-200 text-right">Edu Cess(%)</th>
                <th class="py-2 px-4 border border-gray-200 text-right">She Cess(%)</th>
                <th class="py-2 px-4 border border-gray-200 text-right">Ex/Ser tax</th>
                <th class="py-2 px-4 border border-gray-200 text-right">Edu Cess</th>
                <th class="py-2 px-4 border border-gray-200 text-right">She Cess</th>
                <th class="py-2 px-4 border border-gray-200 text-right">Custom Duty</th>
                <th class="py-2 px-4 border border-gray-200 text-right">VAT</th>
                <th class="py-2 px-4 border border-gray-200 text-right">CST</th>
                <th class="py-2 px-4 border border-gray-200 text-right">Freight</th>
                <th class="py-2 px-4 border border-gray-200 text-right">Tarrif No</th>
                <th class="py-2 px-4 border border-gray-200 text-center">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for obj in bill_booking_details %}
                {% include 'accounts/billbooking/_billbooking_detail_row_display.html' with obj=obj forloop_counter=forloop.counter %}
            {% empty %}
                <tr>
                    <td colspan="20" class="py-4 text-center text-gray-500 font-bold text-lg">No data to display !</td>
                </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // DataTable initialization is handled in billbooking_edit.html's extra_js block
    // triggered by htmx:afterSwap. This ensures it's initialized after the table loads.
</script>
```

**`_billbooking_detail_row_display.html` (Single Row Display Partial)**
This partial displays a single row in the PO Term Details table (non-editable).

```html
<tr id="detail-row-{{ obj.pk }}">
    <td class="py-2 px-4 border border-gray-200 text-center">{{ forloop_counter }}</td>
    <td class="py-2 px-4 border border-gray-200 text-center">{{ obj.gqn_no }}</td>
    <td class="py-2 px-4 border border-gray-200 text-center">{{ obj.gsn_no }}</td>
    <td class="py-2 px-4 border border-gray-200 text-center">{{ obj.item_code }}</td>
    <td class="py-2 px-4 border border-gray-200 text-left">{{ obj.description }}</td>
    <td class="py-2 px-4 border border-gray-200 text-center">{{ obj.uom }}</td>
    <td class="py-2 px-4 border border-gray-200 text-right">{{ obj.calculated_amt|floatformat:2 }}</td>
    <td class="py-2 px-4 border border-gray-200 text-right">{{ obj.pf_amt|floatformat:2 }}</td>
    <td class="py-2 px-4 border border-gray-200 text-right">{{ obj.ex_st_basic_in_per|floatformat:2 }}</td>
    <td class="py-2 px-4 border border-gray-200 text-right">{{ obj.ex_st_educess_in_per|floatformat:2 }}</td>
    <td class="py-2 px-4 border border-gray-200 text-right">{{ obj.ex_st_shecess_in_per|floatformat:2 }}</td>
    <td class="py-2 px-4 border border-gray-200 text-right">{{ obj.ex_st_basic|floatformat:2 }}</td>
    <td class="py-2 px-4 border border-gray-200 text-right">{{ obj.ex_st_educess|floatformat:2 }}</td>
    <td class="py-2 px-4 border border-gray-200 text-right">{{ obj.ex_st_shecess|floatformat:2 }}</td>
    <td class="py-2 px-4 border border-gray-200 text-right">{{ obj.custom_duty|floatformat:2 }}</td>
    <td class="py-2 px-4 border border-gray-200 text-right">{{ obj.vat|floatformat:2 }}</td>
    <td class="py-2 px-4 border border-gray-200 text-right">{{ obj.cst|floatformat:2 }}</td>
    <td class="py-2 px-4 border border-gray-200 text-right">{{ obj.freight|floatformat:2 }}</td>
    <td class="py-2 px-4 border border-gray-200 text-right">{{ obj.tarrif_no }}</td>
    <td class="py-2 px-4 border border-gray-200 text-center">
        <button 
            class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded text-xs"
            hx-get="{% url 'billbooking_detail_edit_row' obj.pk %}?row_idx={{ forloop_counter }}"
            hx-target="#detail-row-{{ obj.pk }}"
            hx-swap="outerHTML"
            hx-indicator="#global-spinner"
            _="on click console.log('Edit button clicked for detail row')"
            >
            Edit
        </button>
    </td>
</tr>
```

**`_billbooking_detail_row_edit.html` (Single Row Edit Partial)**
This partial will be swapped into the PO Term Details table when an "Edit" button is clicked.

```html
<tr id="detail-row-{{ object.pk }}">
    <td class="py-2 px-4 border border-gray-200 text-center">{{ request.GET.row_idx }}</td>
    <td class="py-2 px-4 border border-gray-200 text-center">{{ object.gqn_no }}</td>
    <td class="py-2 px-4 border border-gray-200 text-center">{{ object.gsn_no }}</td>
    <td class="py-2 px-4 border border-gray-200 text-center">{{ object.item_code }}</td>
    <td class="py-2 px-4 border border-gray-200 text-left">{{ object.description }}</td>
    <td class="py-2 px-4 border border-gray-200 text-center">{{ object.uom }}</td>
    <td class="py-2 px-4 border border-gray-200 text-right">{{ object.calculated_amt|floatformat:2 }}</td>
    <td class="py-2 px-4 border border-gray-200 border-l border-r">
        <form hx-put="{% url 'billbooking_detail_update_row' object.pk %}?row_idx={{ request.GET.row_idx }}" 
              hx-target="#detail-row-{{ object.pk }}" 
              hx-swap="outerHTML"
              hx-trigger="submit"
              hx-indicator="#global-spinner">
            {% csrf_token %}
            <div>{{ form.pf_amt }}</div>
            {% if form.pf_amt.errors %}<p class="text-red-500 text-xs mt-1">{{ form.pf_amt.errors }}</p>{% endif %}
            
            <div class="mt-2 flex space-x-1">
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white py-1 px-2 rounded text-xs">Update</button>
                <button type="button" class="bg-gray-500 hover:bg-gray-600 text-white py-1 px-2 rounded text-xs"
                    hx-get="{% url 'billbooking_detail_display_row' object.pk %}?row_idx={{ request.GET.row_idx }}"
                    hx-target="#detail-row-{{ object.pk }}"
                    hx-swap="outerHTML">
                    Cancel
                </button>
            </div>
        </form>
    </td>
    <td class="py-2 px-4 border border-gray-200">{{ form.ex_st_basic_in_per }}</td>
    <td class="py-2 px-4 border border-gray-200">{{ form.ex_st_educess_in_per }}</td>
    <td class="py-2 px-4 border border-gray-200">{{ form.ex_st_shecess_in_per }}</td>
    <td class="py-2 px-4 border border-gray-200">{{ form.ex_st_basic }}</td>
    <td class="py-2 px-4 border border-gray-200">{{ form.ex_st_educess }}</td>
    <td class="py-2 px-4 border border-gray-200">{{ form.ex_st_shecess }}</td>
    <td class="py-2 px-4 border border-gray-200">{{ form.custom_duty }}</td>
    <td class="py-2 px-4 border border-gray-200">{{ form.vat }}</td>
    <td class="py-2 px-4 border border-gray-200">{{ form.cst }}</td>
    <td class="py-2 px-4 border border-gray-200">{{ form.freight }}</td>
    <td class="py-2 px-4 border border-gray-200">{{ form.tarrif_no }}</td>
    <td class="py-2 px-4 border border-gray-200 text-center">
        <!-- Buttons are within the form, so no separate column for them -->
    </td>
</tr>
{% comment %}
    NOTE: For simplicity, the edit form for the detail row is rendered for the entire row.
    If only specific cells need to be editable, the `hx-target` and `hx-swap` would
    be applied to individual `<td>` elements, and each input field would be its own
    hx-trigger if live updates are desired. The current setup updates the whole row.
    Validation errors for other fields would appear if they were within this partial's form.
    For this example, all editable fields are in a single form within the row.
{% endcomment %}
```

**`_billbooking_attachment_table.html` (Attachments Table Partial)**
This partial will be loaded into the "PV/EV Booking Details" tab via HTMX.

```html
<div class="overflow-x-auto" style="max-height: 180px;">
    <table id="billBookingAttachmentTable" class="min-w-full bg-white border-collapse border border-gray-200">
        <thead>
            <tr class="bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <th class="py-2 px-4 border border-gray-200">SN</th>
                <th class="py-2 px-4 border border-gray-200">FileName</th>
                <th class="py-2 px-4 border border-gray-200">FileSize(Byte)</th>
                <th class="py-2 px-4 border border-gray-200 text-center">Download</th>
                <th class="py-2 px-4 border border-gray-200 text-center">Delete</th>
            </tr>
        </thead>
        <tbody>
            {% for obj in attachments %}
            <tr id="attachment-row-{{ obj.pk }}">
                <td class="py-2 px-4 border border-gray-200 text-center">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border border-gray-200">{{ obj.file_name }}</td>
                <td class="py-2 px-4 border border-gray-200">{{ obj.file_size|floatformat:0 }}</td>
                <td class="py-2 px-4 border border-gray-200 text-center">
                    <a href="{{ obj.get_download_url }}" class="text-blue-600 hover:underline" download>Download</a>
                </td>
                <td class="py-2 px-4 border border-gray-200 text-center">
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs"
                        hx-delete="{% url 'billbooking_attachment_delete' bill_booking_master_id obj.pk %}"
                        hx-confirm="Are you sure you want to delete this attachment?"
                        hx-target="#attachmentTableContainer"
                        hx-swap="innerHTML"
                        hx-indicator="#global-spinner"
                        _="on click console.log('Delete button clicked for attachment')"
                        >
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
            {% for obj in temp_attachments %}
            <tr id="temp-attachment-row-{{ obj.pk }}" class="bg-yellow-50">
                <td class="py-2 px-4 border border-gray-200 text-center">{{ attachments|length | add:forloop.counter }}</td>
                <td class="py-2 px-4 border border-gray-200">{{ obj.file_name }} <span class="text-xs italic text-gray-600">(Temp)</span></td>
                <td class="py-2 px-4 border border-gray-200">{{ obj.file_size|floatformat:0 }}</td>
                <td class="py-2 px-4 border border-gray-200 text-center">N/A</td> {# Temp files not downloadable until saved #}
                <td class="py-2 px-4 border border-gray-200 text-center">
                    <button 
                        class="bg-orange-500 hover:bg-orange-700 text-white font-bold py-1 px-2 rounded text-xs"
                        hx-delete="{% url 'billbooking_attachment_delete' bill_booking_master_id obj.pk %}?is_temp=true"
                        hx-confirm="Are you sure you want to delete this temporary attachment?"
                        hx-target="#attachmentTableContainer"
                        hx-swap="innerHTML"
                        hx-indicator="#global-spinner"
                        _="on click console.log('Delete button clicked for temp attachment')"
                        >
                        Delete Temp
                    </button>
                </td>
            </tr>
            {% endfor %}
            {% if not attachments and not temp_attachments %}
            <tr>
                <td colspan="5" class="py-4 text-center text-gray-500 font-bold text-lg">No data to display !</td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
    // DataTable initialization is handled in billbooking_edit.html's extra_js block
</script>
```

#### 4.5 URLs (`accounts/urls.py`)

Define the URL patterns for accessing the views.

```python
from django.urls import path
from .views import (
    BillBookingEditView, BillBookingDetailTablePartialView,
    BillBookingDetailUpdatePartialView, BillBookingAttachmentTablePartialView,
    BillBookingAttachmentUploadView, BillBookingAttachmentDeleteView,
    BillBookingAttachmentDownloadView, BillBookingCancelView
)

urlpatterns = [
    # Main Bill Booking Edit View
    path('billbooking/<int:pk>/edit/', BillBookingEditView.as_view(), name='billbooking_edit'),

    # HTMX partials for PO Term Details
    path('billbooking/<int:pk>/details/table/', BillBookingDetailTablePartialView.as_view(), name='billbooking_detail_table'),
    path('billbooking/details/edit/<int:pk>/', BillBookingDetailUpdatePartialView.as_view(), name='billbooking_detail_edit_row'),
    path('billbooking/details/update/<int:pk>/', BillBookingDetailUpdatePartialView.as_view(), name='billbooking_detail_update_row'),
    # A view to render the non-editable row after canceling edit (for HTMX swap back)
    path('billbooking/details/display/<int:pk>/', BillBookingDetailUpdatePartialView.as_view(template_name='accounts/billbooking/_billbooking_detail_row_display.html'), name='billbooking_detail_display_row'),

    # HTMX partials for Attachments
    path('billbooking/<int:pk>/attachments/table/', BillBookingAttachmentTablePartialView.as_view(), name='billbooking_attachment_table'),
    path('billbooking/<int:pk>/attachments/upload/', BillBookingAttachmentUploadView.as_view(), name='billbooking_attachment_upload'),
    path('billbooking/<int:pk>/attachments/delete/<int:attachment_id>/', BillBookingAttachmentDeleteView.as_view(), name='billbooking_attachment_delete'),
    path('billbooking/attachment/download/<int:attachment_id>/', BillBookingAttachmentDownloadView.as_view(), name='billbooking_attachment_download'),

    # Cancel action
    path('billbooking/cancel/', BillBookingCancelView.as_view(), name='billbooking_cancel'),

    # Placeholder for main list page (as per C# redirect)
    path('billbooking/list/', TemplateView.as_view(template_name='accounts/billbooking/billbooking_list_placeholder.html'), name='billbooking_list'),
]

```
*Note: You would need a `billbooking_list_placeholder.html` or an actual `BillBookingListView` as a landing page for the `billbooking_list` URL.*

#### 4.6 Tests (`accounts/tests.py`)

Comprehensive tests for models and views. This demonstrates the structure; actual test cases would be more extensive.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import date
from io import BytesIO

from .models import (
    BillBookingMaster, BillBookingDetail, BillBookingAttachment, BillBookingAttachmentTemp,
    Supplier, PurchaseOrderDetail, ItemMaster, UnitMaster, MaterialQualityDetail, MaterialQualityMaster,
    MaterialServiceNoteDetail, MaterialServiceNoteMaster, PurchaseOrderMaster, PRDetail, SPRDetail, Department
)

# Mocking session for tests
class MockSession:
    def __init__(self):
        self.data = {
            'username': 'testuser',
            'compid': 1,
            'finyear': 2023
        }
    def get(self, key, default=None):
        return self.data.get(key, default)
    def __getitem__(self, key):
        return self.data[key]
    def __setitem__(self, key, value):
        self.data[key] = value

class BillBookingModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary lookup data (mocked if not using real DB setup)
        cls.supplier = Supplier.objects.create(id=1, supplier_name='Test Supplier Co.', regd_address='123 Test St', regd_city_id=1, regd_state_id=1, regd_country_id=1)
        cls.po_master = PurchaseOrderMaster.objects.create(id=1, po_no='PO-001', pr_spr_flag='0')
        cls.po_detail = PurchaseOrderDetail.objects.create(id=1, master=cls.po_master, po_no='PO-001', rate=100.00, discount=10.00)
        cls.item_master = ItemMaster.objects.create(id=1, item_code='ITEM-001', description='Test Item', uom_basic_id=1)
        cls.unit_master = UnitMaster.objects.create(id=1, symbol='PCS')
        cls.material_quality_master = MaterialQualityMaster.objects.create(id=1, gqn_no='GQN-001')
        cls.material_quality_detail = MaterialQualityDetail.objects.create(id=1, master=cls.material_quality_master, accepted_qty=5.0)
        cls.material_service_note_master = MaterialServiceNoteMaster.objects.create(id=1, gsn_no='GSN-001')
        cls.material_service_note_detail = MaterialServiceNoteDetail.objects.create(id=1, master=cls.material_service_note_master, received_qty=10.0)
        cls.pr_detail = PRDetail.objects.create(id=1, pr_no='PR-001', wo_no='WO-001')
        cls.spr_detail = SPRDetail.objects.create(id=1, spr_no='SPR-001', dept_id=1)
        cls.department = Department.objects.create(id=1, symbol='IT')


        cls.bill_booking_master = BillBookingMaster.objects.create(
            id=1,
            sys_date='2023-01-01',
            sys_time='10:00:00',
            session_id='old_session',
            comp_id=1,
            fin_year_id=2023,
            pvev_no='PVEV-001',
            supplier=cls.supplier,
            bill_no='BILL-001',
            bill_date=date(2023, 1, 15),
            cenvat_entry_no='CEN-001',
            cenvat_entry_date=date(2023, 1, 20),
            other_charges=50.00,
            other_cha_desc='Misc Charges',
            narration='Initial booking.',
            debit_amt=1000.00,
            discount_type='0',
            discount=50.00
        )

        cls.bill_booking_detail_gqn = BillBookingDetail.objects.create(
            id=1,
            master=cls.bill_booking_master,
            gqn_detail=cls.material_quality_detail,
            po_detail=cls.po_detail,
            item_master=cls.item_master,
            pf_amt=10.00, ex_st_basic=5.00, ex_st_educess=1.00, ex_st_shecess=0.50,
            custom_duty=2.00, vat=15.00, cst=3.00, freight=20.00, tarrif_no='8479',
            ex_st_basic_in_per=5.0, ex_st_educess_in_per=1.0, ex_st_shecess_in_per=0.5
        )

        cls.bill_booking_detail_gsn = BillBookingDetail.objects.create(
            id=2,
            master=cls.bill_booking_master,
            gsn_detail=cls.material_service_note_detail,
            po_detail=cls.po_detail,
            item_master=cls.item_master,
            pf_amt=12.00, ex_st_basic=6.00, ex_st_educess=1.20, ex_st_shecess=0.60,
            custom_duty=2.50, vat=18.00, cst=3.50, freight=25.00, tarrif_no='9018',
            ex_st_basic_in_per=6.0, ex_st_educess_in_per=1.2, ex_st_shecess_in_per=0.6
        )
        
        cls.bill_booking_attachment = BillBookingAttachment.objects.create(
            id=1,
            master=cls.bill_booking_master,
            session_id='old_session',
            comp_id=1,
            fin_year_id=2023,
            file_name='test_file.txt',
            file_size=100,
            content_type='text/plain',
            file_data=b'This is test file data.'
        )

    def test_bill_booking_master_creation(self):
        self.assertEqual(self.bill_booking_master.bill_no, 'BILL-001')
        self.assertEqual(self.bill_booking_master.supplier.supplier_name, 'Test Supplier Co.')

    def test_bill_booking_master_update_timestamp(self):
        old_sys_date = self.bill_booking_master.sys_date
        old_sys_time = self.bill_booking_master.sys_time
        self.bill_booking_master.update_timestamp('new_user_session')
        self.bill_booking_master.refresh_from_db()
        self.assertNotEqual(self.bill_booking_master.sys_date, old_sys_date)
        self.assertNotEqual(self.bill_booking_master.sys_time, old_sys_time)
        self.assertEqual(self.bill_booking_master.session_id, 'new_user_session')

    def test_bill_booking_detail_calculated_amt_gqn(self):
        # Amt = ((Rate - (Rate * Discount) / 100) * AccQty)
        # Rate = 100, Discount = 10 (from po_detail), AccQty = 5 (from gqn_detail)
        # Amt = ((100 - (100 * 10) / 100) * 5) = ((100 - 10) * 5) = (90 * 5) = 450
        self.assertEqual(self.bill_booking_detail_gqn.calculated_amt, 450.00)

    def test_bill_booking_detail_calculated_amt_gsn(self):
        # Rate = 100, Discount = 10 (from po_detail), AccQty = 10 (from gsn_detail)
        # Amt = ((100 - (100 * 10) / 100) * 10) = ((100 - 10) * 10) = (90 * 10) = 900
        self.assertEqual(self.bill_booking_detail_gsn.calculated_amt, 900.00)

    def test_bill_booking_detail_properties(self):
        self.assertEqual(self.bill_booking_detail_gqn.gqn_no, 'GQN-001')
        self.assertEqual(self.bill_booking_detail_gsn.gsn_no, 'GSN-001')
        self.assertEqual(self.bill_booking_detail_gqn.item_code, 'ITEM-001')
        self.assertEqual(self.bill_booking_detail_gqn.description, 'Test Item')
        self.assertEqual(self.bill_booking_detail_gqn.uom, 'PCS')

    def test_bill_booking_attachment_transfer_to_master(self):
        temp_att = BillBookingAttachmentTemp.objects.create(
            id=1,
            session_id='temp_user',
            comp_id=1,
            fin_year_id=2023,
            file_name='temp_file.pdf',
            file_size=200,
            content_type='application/pdf',
            file_data=b'temporary pdf data'
        )
        self.assertEqual(BillBookingAttachmentTemp.objects.count(), 1)
        temp_att.transfer_to_master(self.bill_booking_master)
        self.assertEqual(BillBookingAttachmentTemp.objects.count(), 0)
        self.assertEqual(BillBookingAttachment.objects.filter(file_name='temp_file.pdf').count(), 1)


class BillBookingViewsTest(TestCase):
    client_class = Client

    @classmethod
    def setUpTestData(cls):
        # Create minimal required data for views tests
        cls.supplier = Supplier.objects.create(id=1, supplier_name='Test Supplier Co.', regd_address='123 Test St', regd_city_id=1, regd_state_id=1, regd_country_id=1)
        City.objects.create(id=1, city_name='TestCity')
        State.objects.create(id=1, state_name='TestState')
        Country.objects.create(id=1, country_name='TestCountry')
        cls.po_master = PurchaseOrderMaster.objects.create(id=1, po_no='PO-001', pr_spr_flag='0')
        cls.pr_detail = PRDetail.objects.create(id=1, pr_no='PR-001', wo_no='WO-001')
        cls.po_detail = PurchaseOrderDetail.objects.create(id=1, master=cls.po_master, po_no='PO-001', pr_id=cls.pr_detail.id, pr_no='PR-001', rate=100.00, discount=10.00)
        cls.item_master = ItemMaster.objects.create(id=1, item_code='ITEM-001', description='Test Item', uom_basic_id=1)
        cls.unit_master = UnitMaster.objects.create(id=1, symbol='PCS')
        cls.mq_master = MaterialQualityMaster.objects.create(id=1, gqn_no='GQN-001')
        cls.mq_detail = MaterialQualityDetail.objects.create(id=1, master=cls.mq_master, accepted_qty=5.0)
        cls.msn_master = MaterialServiceNoteMaster.objects.create(id=1, gsn_no='GSN-001')
        cls.msn_detail = MaterialServiceNoteDetail.objects.create(id=1, master=cls.msn_master, received_qty=10.0)


        cls.bill_booking = BillBookingMaster.objects.create(
            id=1,
            sys_date=timezone.now().strftime('%Y-%m-%d'),
            sys_time=timezone.now().strftime('%H:%M:%S'),
            session_id='testuser',
            comp_id=1,
            fin_year_id=2023,
            pvev_no='PVEV-001',
            supplier=cls.supplier,
            bill_no='BILL-001',
            bill_date=date(2023, 1, 15),
            cenvat_entry_no='CEN-001',
            cenvat_entry_date=date(2023, 1, 20),
            other_charges=50.00,
            other_cha_desc='Misc Charges',
            narration='Initial booking.',
            debit_amt=1000.00,
            discount_type='0',
            discount=50.00
        )
        cls.bill_booking_detail = BillBookingDetail.objects.create(
            id=1,
            master=cls.bill_booking,
            gqn_detail=cls.mq_detail,
            po_detail=cls.po_detail,
            item_master=cls.item_master,
            pf_amt=10.00, ex_st_basic=5.00, ex_st_educess=1.00, ex_st_shecess=0.50,
            custom_duty=2.00, vat=15.00, cst=3.00, freight=20.00, tarrif_no='8479',
            ex_st_basic_in_per=5.0, ex_st_educess_in_per=1.0, ex_st_shecess_in_per=0.5
        )
        cls.attachment = BillBookingAttachment.objects.create(
            id=1,
            master=cls.bill_booking,
            session_id='testuser',
            comp_id=1,
            fin_year_id=2023,
            file_name='existing.pdf',
            file_size=1024,
            content_type='application/pdf',
            file_data=b'PDF content'
        )

    def setUp(self):
        self.client.session = MockSession()

    def test_bill_booking_edit_view_get(self):
        response = self.client.get(reverse('billbooking_edit', args=[self.bill_booking.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/billbooking/billbooking_edit.html')
        self.assertContains(response, 'PVEV-001')
        self.assertContains(response, 'BILL-001')
        self.assertContains(response, 'Test Supplier Co.')

    def test_bill_booking_edit_view_post_success(self):
        data = {
            'bill_no': 'BILL-001-UPDATED',
            'bill_date': '16-01-2023',
            'cenvat_entry_no': 'CEN-001-UPDATED',
            'cenvat_entry_date': '21-01-2023',
            'other_charges': '60.00',
            'other_cha_desc': 'Updated Misc Charges',
            'narration': 'Updated narration.',
            'debit_amt': '1100.00',
            'discount_type': '1',
            'discount': '10.00'
        }
        response = self.client.post(reverse('billbooking_edit', args=[self.bill_booking.pk]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HTMX success implies 204 No Content
        self.bill_booking.refresh_from_db()
        self.assertEqual(self.bill_booking.bill_no, 'BILL-001-UPDATED')
        self.assertEqual(self.bill_booking.narration, 'Updated narration.')
        self.assertTrue('HX-Trigger' in response.headers)

    def test_bill_booking_edit_view_post_invalid(self):
        data = {
            'bill_no': '', # Invalid, should trigger error
            'bill_date': '16-01-2023',
            'cenvat_entry_no': 'CEN-001-UPDATED',
            'cenvat_entry_date': '21-01-2023',
            'other_charges': '60.00',
            'other_cha_desc': 'Updated Misc Charges',
            'narration': 'Updated narration.',
            'debit_amt': '1100.00',
            'discount_type': '1',
            'discount': '10.00'
        }
        response = self.client.post(reverse('billbooking_edit', args=[self.bill_booking.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX swaps back form with errors
        self.assertContains(response, 'This field is required.')

    def test_bill_booking_detail_table_partial_view(self):
        response = self.client.get(reverse('billbooking_detail_table', args=[self.bill_booking.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/billbooking/_billbooking_detail_table.html')
        self.assertContains(response, 'GQN-001')
        self.assertContains(response, '450.00') # Check calculated_amt

    def test_bill_booking_detail_edit_row_get(self):
        response = self.client.get(reverse('billbooking_detail_edit_row', args=[self.bill_booking_detail.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/billbooking/_billbooking_detail_row_edit.html')
        self.assertContains(response, '<input type="number" name="pf_amt"') # Check for form input

    def test_bill_booking_detail_update_row_put_success(self):
        data = {
            'pf_amt': '15.00', 'ex_st_basic_in_per': '5.50', 'ex_st_educess_in_per': '1.10',
            'ex_st_shecess_in_per': '0.55', 'ex_st_basic': '5.50', 'ex_st_educess': '1.10',
            'ex_st_shecess': '0.55', 'custom_duty': '2.20', 'vat': '15.50', 'cst': '3.30',
            'freight': '22.00', 'tarrif_no': '8480'
        }
        response = self.client.put(
            reverse('billbooking_detail_update_row', args=[self.bill_booking_detail.pk]),
            data,
            content_type='application/x-www-form-urlencoded', # HTMX sends form data this way
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200) # Should return updated row for swap
        self.assertTemplateUsed(response, 'accounts/billbooking/_billbooking_detail_row_display.html')
        
        self.bill_booking_detail.refresh_from_db()
        self.assertEqual(self.bill_booking_detail.pf_amt, 15.00)
        self.assertEqual(self.bill_booking_detail.tarrif_no, '8480')
        self.bill_booking.refresh_from_db()
        self.assertEqual(self.bill_booking.session_id, 'testuser') # Check master timestamp updated

    def test_bill_booking_attachment_table_partial_view(self):
        response = self.client.get(reverse('billbooking_attachment_table', args=[self.bill_booking.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/billbooking/_billbooking_attachment_table.html')
        self.assertContains(response, 'existing.pdf')

    def test_bill_booking_attachment_upload_view(self):
        file_content = b"This is a test attachment."
        uploaded_file = BytesIO(file_content)
        uploaded_file.name = 'uploaded_test.txt'
        uploaded_file.seek(0)

        data = {'file': uploaded_file}
        
        # Ensure session data is set for the upload view
        self.client.session['compid'] = 1
        self.client.session['finyear'] = 2023
        self.client.session['username'] = 'testuser'

        response = self.client.post(
            reverse('billbooking_attachment_upload', args=[self.bill_booking.pk]),
            data,
            HTTP_HX_REQUEST='true',
            format='multipart' # For file uploads
        )
        
        self.assertEqual(response.status_code, 204)
        self.assertEqual(BillBookingAttachmentTemp.objects.filter(file_name='uploaded_test.txt').count(), 1)
        self.assertTrue('HX-Trigger' in response.headers)

    def test_bill_booking_attachment_delete_view(self):
        # Create a temp attachment for testing deletion
        temp_att = BillBookingAttachmentTemp.objects.create(
            id=2, # Ensure unique ID
            session_id='testuser',
            comp_id=1,
            fin_year_id=2023,
            file_name='temp_to_delete.txt',
            file_size=50,
            content_type='text/plain',
            file_data=b'delete me'
        )
        self.assertEqual(BillBookingAttachmentTemp.objects.count(), 1) # One from test, one created now

        response = self.client.delete(
            reverse('billbooking_attachment_delete', args=[self.bill_booking.pk, temp_att.pk]) + '?is_temp=true',
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 204)
        self.assertEqual(BillBookingAttachmentTemp.objects.count(), 0) # Temp attachment should be deleted
        self.assertTrue('HX-Trigger' in response.headers)

        # Test deleting a master attachment
        response = self.client.delete(
            reverse('billbooking_attachment_delete', args=[self.bill_booking.pk, self.attachment.pk]),
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 204)
        self.assertEqual(BillBookingAttachment.objects.count(), 0) # Master attachment should be deleted

    def test_bill_booking_attachment_download_view(self):
        response = self.client.get(reverse('billbooking_attachment_download', args=[self.attachment.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/pdf')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="existing.pdf"')
        self.assertEqual(response.content, b'PDF content')

    def test_bill_booking_cancel_view(self):
        response = self.client.get(reverse('billbooking_cancel'))
        self.assertEqual(response.status_code, 302) # Redirect
        self.assertRedirects(response, reverse('billbooking_list'))

```

### Step 5: HTMX and Alpine.js Integration

As demonstrated in the templates and views:

*   **HTMX:**
    *   `hx-get` on `<div>` elements (e.g., `poTermDetailsTableContainer`, `attachmentTableContainer`) for initial loading of table content.
    *   `hx-trigger="load, refresh... from:body"` for dynamic updates.
    *   `hx-post` for main form submission (swaps `outerHTML` for error handling).
    *   `hx-put` for inline row updates (swaps `outerHTML` of the row).
    *   `hx-delete` for attachment deletion.
    *   `hx-target` and `hx-swap` for precise UI updates.
    *   `hx-encoding="multipart/form-data"` for file uploads.
    *   `HX-Trigger` headers from Django views to trigger client-side events (e.g., `showToast`, `refreshAttachmentList`).
    *   `htmx-indicator` for showing loading spinners.

*   **Alpine.js:**
    *   `x-data="{ activeTab: 'details' }"` for managing tab state.
    *   `x-show` to conditionally display tab content.
    *   `@click.prevent` to change `activeTab` on tab header clicks.
    *   Used for simple UI state management, keeping complex JavaScript logic to a minimum.
    *   The modal functionality is also driven by Alpine.js (`on click add .is-active to #modal`) combined with HTMX (`hx-get` to load content into `modalContent`).

*   **DataTables:**
    *   Integrated into `_billbooking_detail_table.html` and `_billbooking_attachment_table.html`.
    *   Initialization is handled within `billbooking_edit.html`'s `extra_js` block, using `htmx:afterSwap` event to ensure DataTables is re-initialized after HTMX replaces the table content. This ensures proper client-side searching, sorting, and pagination.

*   **DRY Templates:**
    *   `_billbooking_detail_row_display.html` and `_billbooking_detail_row_edit.html` are separate partials for the display and edit states of a table row, allowing HTMX to swap them efficiently.
    *   The `_billbooking_master_form.html` acts as a partial that can be re-rendered on form validation errors.

This detailed plan provides a clear, actionable roadmap for migrating the ASP.NET Bill Booking Edit functionality to a modern Django application, adhering to all specified guidelines and leveraging contemporary web technologies.