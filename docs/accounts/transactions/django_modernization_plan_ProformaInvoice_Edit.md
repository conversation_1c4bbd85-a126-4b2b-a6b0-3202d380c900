This comprehensive plan outlines the migration of your ASP.NET "Proforma Invoice - Edit" module to a modern Django-based solution. Our approach prioritizes automation, clean architecture, and responsive user experiences using cutting-edge tools like HTMX, Alpine.js, and DataTables.

The core business value of this modernization lies in:
*   **Enhanced Maintainability:** Django's structured framework, clear separation of concerns (Fat Model, Thin View), and modern best practices drastically reduce complexity, making the application easier to understand, debug, and evolve.
*   **Improved Performance & Responsiveness:** By leveraging HTMX, all user interactions, including search, filtering, and form submissions, will occur without full page reloads, providing a smoother, faster user experience akin to a single-page application (SPA) but with simpler development.
*   **Future-Proofing:** Transitioning to an actively maintained open-source ecosystem like Django, combined with frontend technologies like HTMX and Alpine.js, ensures your application can adapt to future technological shifts and benefit from a vibrant developer community.
*   **Cost Efficiency:** The automation-first approach reduces manual coding effort, accelerating the migration process and lowering long-term maintenance costs.
*   **Scalability:** Django is inherently scalable, capable of handling increased user loads and data volumes as your business grows.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

The analysis of the ASP.NET code reveals that the application interacts with several key database tables for fetching and displaying Proforma Invoice data. These tables, along with their primary columns relevant to this module, are:

*   **`tblACC_ProformaInvoice_Master`** (Main table for Proforma Invoices)
    *   `Id` (Integer, Primary Key)
    *   `FinYearId` (Integer, links to `tblFinancial_master`)
    *   `SysDate` (DateTime, representing the invoice creation date)
    *   `InvoiceNo` (String, the unique invoice number)
    *   `WONo` (String, a comma-separated list of Work Order IDs, linking to `SD_Cust_WorkOrder_Master`)
    *   `PONo` (String, Purchase Order Number)
    *   `CustomerCode` (String, links to `SD_Cust_master.CustomerId`)
    *   `CompId` (Integer, Company ID, used for data segmentation)

*   **`tblFinancial_master`** (Lookup table for Financial Years)
    *   `FinYearId` (Integer, Primary Key)
    *   `FinYear` (String, e.g., "2023-24")
    *   `CompId` (Integer, Company ID)

*   **`SD_Cust_master`** (Customer Master Data)
    *   `CustomerId` (String, Primary Key)
    *   `CustomerName` (String, the customer's full name)
    *   `CompId` (Integer, Company ID)

*   **`SD_Cust_WorkOrder_Master`** (Work Order Master Data)
    *   `Id` (Integer, Primary Key)
    *   `WONo` (String, the specific Work Order number)
    *   `CompId` (Integer, Company ID)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

The ASP.NET page primarily performs **Read** operations, specifically listing and searching Proforma Invoices. It also features an **Autocomplete** mechanism and a **Selection/Redirection** action.

*   **Read (List & Search):**
    *   The application displays a paginated list of Proforma Invoices.
    *   Users can filter this list using a dropdown to select a search criterion (`Customer Name`, `PO No`, or `Invoice No`).
    *   A corresponding textbox allows input for the chosen criterion.
    *   The search applies company (`CompId`) and financial year (`FinYearId`) filters, retrieved from the user's session.
    *   The process involves complex data lookups across multiple tables (Financial Year, Customer Name, and decoding comma-separated Work Order IDs) to populate the grid.

*   **Autocomplete:**
    *   The "Customer Name" search field uses an AJAX-enabled autocomplete feature to suggest customer names as the user types.

*   **Select / Redirection:**
    *   Each row in the displayed list includes a "Select" button.
    *   Clicking "Select" redirects the user to a separate "ProformaInvoice_Edit_Details.aspx" page, passing encrypted IDs for the selected invoice and customer.

While this specific ASP.NET page does not include explicit Create, Update, or Delete operations, the Django modernization plan will include generic CRUD view examples to fulfill the broader requirement for a comprehensive solution.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

The user interface of the ASP.NET page is structured around a search section and a data display grid, with dynamic elements for improved usability:

*   **Search Controls:**
    *   A `DropDownList` (`DropDownList1`) allows users to switch between search criteria (Customer Name, PO No, Invoice No).
    *   Two `TextBox` controls (`txtCustName` and `txtpoNo`) are used for input, with their visibility dynamically toggled based on the `DropDownList` selection.
    *   `txtCustName` is enhanced with an `AutoCompleteExtender` for real-time customer suggestions.
    *   A `Button` (`btnSearch`) initiates the search.

*   **Data Display Grid:**
    *   An `asp:GridView` (`GridView1`) serves as the main data display, presenting Proforma Invoice details in a tabular format.
    *   It supports client-side styling (`yui-datatable-theme`) and pagination (`PageSize="20"`).
    *   The grid displays columns for `Serial Number (SN)`, `Financial Year`, `Invoice No`, `Date`, `Customer Name`, `Work Order No`, and `PO No`.
    *   Each row includes a "Select" `LinkButton` to navigate to a detail page.

*   **Dynamic Interactions:**
    *   Changes in the `DropDownList` trigger a server-side postback, dynamically adjusting the visibility of the search textboxes and refreshing the grid.
    *   The `AutoCompleteExtender` uses AJAX calls to fetch and display customer name suggestions.
    *   The "Select" action on grid rows triggers a server-side command leading to a page redirection.

## Step 4: Generate Django Code

### 4.1 Models

Task: Create a Django model based on the database schema.

We will create Django models that directly map to your existing database tables. These models will include properties and methods (the "Fat Model" approach) to encapsulate the complex data aggregation logic previously handled in the ASP.NET code-behind, such as resolving financial year, customer details, and parsing comma-separated Work Order IDs.

```python
# proformainvoice/models.py
from django.db import models
from django.core.exceptions import ObjectDoesNotExist
import datetime

# Helper function to get current company and financial year context
# In a production application, this would typically come from the authenticated
# user's profile, session, or a request-scoped context manager.
# For this example, we assume hardcoded values for demonstration.
def get_current_company_financial_year_context():
    """Returns the current Company ID and Financial Year ID."""
    # Placeholder: Replace with actual logic to fetch from request.user, session, etc.
    return 1, 1 # Example: Assuming CompId=1, FinYearId=1

class FinancialYear(models.Model):
    # Maps to tblFinancial_master
    Id = models.IntegerField(primary_key=True, db_column='FinYearId') # Using 'Id' to align with ASP.NET's FinYearId usage
    FinYear = models.CharField(max_length=50, db_column='FinYear')
    CompId = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.FinYear

class Customer(models.Model):
    # Maps to SD_Cust_master
    CustomerId = models.CharField(primary_key=True, max_length=50, db_column='CustomerId')
    CustomerName = models.CharField(max_length=255, db_column='CustomerName')
    CompId = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.CustomerName} [{self.CustomerId}]"

class WorkOrder(models.Model):
    # Maps to SD_Cust_WorkOrder_Master
    Id = models.IntegerField(primary_key=True, db_column='Id')
    WONo = models.CharField(max_length=100, db_column='WONo')
    CompId = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.WONo

class ProformaInvoiceQuerySet(models.QuerySet):
    """Custom QuerySet for ProformaInvoice to encapsulate common filtering and search logic."""

    def for_current_company_and_financial_year(self):
        """Filters invoices by the current company and financial year."""
        comp_id, fin_year_id = get_current_company_financial_year_context()
        return self.filter(CompId=comp_id, FinYearId__lte=fin_year_id)

    def search_by_customer_name(self, customer_full_name):
        """Searches invoices by customer name (assuming 'Name [ID]' format)."""
        comp_id, _ = get_current_company_financial_year_context()
        try:
            # Extract CustomerId from the 'Name [ID]' format
            customer_id_part = customer_full_name.split('[')[-1].replace(']', '').strip()
            # Ensure the CustomerId exists and belongs to the current company
            if Customer.objects.using(self._db).filter(CustomerId=customer_id_part, CompId=comp_id).exists():
                return self.filter(CustomerCode=customer_id_part)
            else:
                return self.none() # Customer ID not found or not for this company
        except (IndexError, AttributeError):
            return self.none() # Invalid format

    def search_by_po_no(self, po_no):
        """Searches invoices by Purchase Order number."""
        return self.filter(PONo=po_no)

    def search_by_invoice_no(self, invoice_no):
        """Searches invoices by Invoice number."""
        return self.filter(InvoiceNo=invoice_no)

class ProformaInvoice(models.Model):
    # Maps to tblACC_ProformaInvoice_Master
    Id = models.IntegerField(primary_key=True, db_column='Id')
    FinYearId = models.IntegerField(db_column='FinYearId')
    SysDate = models.DateTimeField(db_column='SysDate')
    InvoiceNo = models.CharField(max_length=50, db_column='InvoiceNo')
    WONo = models.CharField(max_length=255, db_column='WONo') # Stored as comma-separated IDs
    PONo = models.CharField(max_length=100, db_column='PONo')
    CustomerCode = models.CharField(max_length=50, db_column='CustomerCode')
    CompId = models.IntegerField(db_column='CompId')

    objects = ProformaInvoiceQuerySet.as_manager() # Use the custom QuerySet

    class Meta:
        managed = False
        db_table = 'tblACC_ProformaInvoice_Master'
        verbose_name = 'Proforma Invoice'
        verbose_name_plural = 'Proforma Invoices'
        ordering = ['-Id'] # Matches ASP.NET's "Order by Id Desc"

    def __str__(self):
        return self.InvoiceNo

    # Business logic as properties (Fat Model principle)
    @property
    def financial_year(self):
        """Returns the full financial year string (e.g., '2023-24')."""
        comp_id, _ = get_current_company_financial_year_context()
        try:
            return FinancialYear.objects.using(self._state.db).get(Id=self.FinYearId, CompId=comp_id).FinYear
        except ObjectDoesNotExist:
            return "N/A"

    @property
    def formatted_sys_date(self):
        """Returns the SysDate formatted as 'DD/MM/YYYY'."""
        return self.SysDate.strftime('%d/%m/%Y') if self.SysDate else ''

    @property
    def customer_details(self):
        """Returns customer name and ID in 'CustomerName [CustomerId]' format."""
        comp_id, _ = get_current_company_financial_year_context()
        try:
            customer = Customer.objects.using(self._state.db).get(CustomerId=self.CustomerCode, CompId=comp_id)
            return f"{customer.CustomerName} [{customer.CustomerId}]"
        except ObjectDoesNotExist:
            return f"Unknown Customer [{self.CustomerCode}]"

    @property
    def customer_name_only(self):
        """Returns only the customer's name."""
        comp_id, _ = get_current_company_financial_year_context()
        try:
            customer = Customer.objects.using(self._state.db).get(CustomerId=self.CustomerCode, CompId=comp_id)
            return customer.CustomerName
        except ObjectDoesNotExist:
            return "Unknown Customer"

    @property
    def customer_id_only(self):
        """Returns only the customer's ID."""
        return self.CustomerCode

    @property
    def work_order_numbers(self):
        """Resolves comma-separated Work Order IDs into their actual WONo strings."""
        if not self.WONo:
            return ""

        comp_id, _ = get_current_company_financial_year_context()
        # Clean and convert string of IDs to a list of integers
        wo_ids = [int(wo_id.strip()) for wo_id in self.WONo.split(',') if wo_id.strip().isdigit()]

        if not wo_ids:
            return ""

        # Fetch all relevant WorkOrder objects in a single query
        work_orders = WorkOrder.objects.using(self._state.db).filter(Id__in=wo_ids, CompId=comp_id)
        # Create a dictionary for quick lookup by ID
        wo_map = {wo.Id: wo.WONo for wo in work_orders}

        # Reconstruct the string, maintaining original order and handling missing IDs
        resolved_wos = [wo_map.get(wo_id, f"Unknown WO [{wo_id}]") for wo_id in wo_ids]

        return ", ".join(resolved_wos)

```

### 4.2 Forms

Task: Define a Django form for user input.

We will create a `ProformaInvoiceSearchForm` to manage the search criteria and inputs, mimicking the ASP.NET dropdown and textboxes. Additionally, a generic `ProformaInvoiceForm` will be provided for potential Create/Update operations, aligning with the comprehensive CRUD requirement.

```python
# proformainvoice/forms.py
from django import forms
from .models import ProformaInvoice, Customer, get_current_company_financial_year_context

SEARCH_CHOICES = [
    ('0', 'Customer Name'),
    ('2', 'PO No'),
    ('3', 'Invoice No'),
]

class ProformaInvoiceSearchForm(forms.Form):
    """
    Form for handling the search criteria in the Proforma Invoice list.
    Uses HTMX to dynamically swap search input fields.
    """
    search_by = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        required=False,
        label="Search By",
        widget=forms.Select(attrs={
            'class': 'box3 w-full py-2 px-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500',
            'hx-get': '/proformainvoice/hx_update_search_field/', # HTMX endpoint to swap search field
            'hx-target': '#search-input-container',
            'hx-swap': 'innerHTML',
            'hx-trigger': 'change', # Trigger on dropdown change
        })
    )
    
    # These fields will be rendered conditionally and toggled by HTMX/Alpine.js
    customer_name = forms.CharField(
        max_length=255,
        required=False,
        label="Customer Name",
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full py-2 px-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'Enter Customer Name',
            'hx-get': '/proformainvoice/autocomplete/customer/', # HTMX for autocomplete suggestions
            'hx-trigger': 'keyup changed delay:300ms, search', # Trigger after typing stops for 300ms
            'hx-target': '#customer-autocomplete-results',
            'hx-swap': 'innerHTML',
            # Hyperscript for showing/hiding autocomplete results container
            '_': 'on keyup if event.target.value.length > 0 add .show to #customer-autocomplete-results else remove .show from #customer-autocomplete-results',
        })
    )
    po_or_invoice_no = forms.CharField(
        max_length=100,
        required=False,
        label="PO/Invoice No",
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full py-2 px-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'Enter PO No or Invoice No',
        })
    )

class ProformaInvoiceForm(forms.ModelForm):
    """
    Generic ModelForm for ProformaInvoice Create/Update operations.
    """
    class Meta:
        model = ProformaInvoice
        # Exclude 'Id' as it's typically auto-generated for new records,
        # but keep it if it's explicitly managed by the database.
        # For simplicity, we assume 'Id' is handled by the database for new records.
        # If 'Id' is truly an Identity column that is supplied by the system, it will be excluded.
        # Otherwise, if it's a natural key, it would be in fields.
        fields = ['FinYearId', 'SysDate', 'InvoiceNo', 'WONo', 'PONo', 'CustomerCode', 'CompId']
        widgets = {
            'FinYearId': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'SysDate': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'InvoiceNo': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'WONo': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'PONo': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'CustomerCode': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'CompId': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
    
    def clean_CustomerCode(self):
        """
        Custom validation to ensure the provided CustomerCode exists for the current company.
        """
        customer_code = self.cleaned_data['CustomerCode']
        comp_id, _ = get_current_company_financial_year_context()
        if not Customer.objects.filter(CustomerId=customer_code, CompId=comp_id).exists():
            raise forms.ValidationError("Invalid Customer Code. Please select an existing customer.")
        return customer_code

```

### 4.3 Views

Task: Implement CRUD operations using CBVs.

The views will be designed to be "thin," delegating business logic to the models and forms. They handle the rendering of initial pages, HTMX-driven partial updates, and form submissions.

```python
# proformainvoice/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.template.loader import render_to_string
from django.shortcuts import render # Ensure render is imported
from django.db.models import Q # For ORM complex queries

from .models import ProformaInvoice, Customer, get_current_company_financial_year_context
from .forms import ProformaInvoiceForm, ProformaInvoiceSearchForm

# Get the current company and financial year context for consistent filtering
COMP_ID, FIN_YEAR_ID = get_current_company_financial_year_context()

class ProformaInvoiceListView(ListView):
    """
    Renders the main Proforma Invoice list page, including the search form.
    The actual table content is loaded via HTMX into a separate partial.
    """
    model = ProformaInvoice
    template_name = 'proformainvoice/proformainvoice_list.html'
    context_object_name = 'proformainvoices' # Although the table content is loaded by HTMX, keep for consistency
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the search form with any existing GET parameters
        context['search_form'] = ProformaInvoiceSearchForm(self.request.GET or None)
        return context

class ProformaInvoiceTablePartialView(ListView):
    """
    Renders the Proforma Invoice table content as a partial for HTMX updates.
    Handles applying search filters based on GET parameters.
    """
    model = ProformaInvoice
    template_name = 'proformainvoice/_proformainvoice_table.html'
    context_object_name = 'proformainvoices'

    def get_queryset(self):
        """
        Applies company, financial year, and search filters to the queryset.
        """
        queryset = ProformaInvoice.objects.for_current_company_and_financial_year()
        form = ProformaInvoiceSearchForm(self.request.GET)
        
        if form.is_valid():
            search_by = form.cleaned_data.get('search_by')
            customer_name = form.cleaned_data.get('customer_name')
            po_or_invoice_no = form.cleaned_data.get('po_or_invoice_no')

            if search_by == '0' and customer_name: # Customer Name search
                queryset = queryset.search_by_customer_name(customer_name)
            elif search_by == '2' and po_or_invoice_no: # PO No search
                queryset = queryset.search_by_po_no(po_or_invoice_no)
            elif search_by == '3' and po_or_invoice_no: # Invoice No search
                queryset = queryset.search_by_invoice_no(po_or_invoice_no)
        
        return queryset

class HxUpdateSearchFieldView(View):
    """
    HTMX-triggered view that returns the appropriate search input field HTML
    (customer_name or po_or_invoice_no) based on the dropdown selection.
    """
    def get(self, request, *args, **kwargs):
        search_by = request.GET.get('search_by')
        context = {}
        
        # Render the correct field using the form's widget
        if search_by == '0': # Customer Name selected
            context['field_type'] = 'customer_name'
            context['field'] = ProformaInvoiceSearchForm().fields['customer_name'].widget.render(
                name='customer_name', value='', attrs={
                    'id': 'id_customer_name', # Ensure consistent ID for HTMX/Alpine.js
                    'class': 'box3 w-full py-2 px-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500',
                    'placeholder': 'Enter Customer Name',
                    'hx-get': '/proformainvoice/autocomplete/customer/',
                    'hx-trigger': 'keyup changed delay:300ms, search',
                    'hx-target': '#customer-autocomplete-results',
                    'hx-swap': 'innerHTML',
                    '_': 'on keyup if event.target.value.length > 0 add .show to #customer-autocomplete-results else remove .show from #customer-autocomplete-results',
                }
            )
        elif search_by in ['2', '3']: # PO No or Invoice No selected
            context['field_type'] = 'po_or_invoice_no'
            context['field'] = ProformaInvoiceSearchForm().fields['po_or_invoice_no'].widget.render(
                name='po_or_invoice_no', value='', attrs={
                    'id': 'id_po_or_invoice_no', # Ensure consistent ID
                    'class': 'box3 w-full py-2 px-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500',
                    'placeholder': 'Enter PO No or Invoice No',
                }
            )
        # Default to customer name if no valid selection (or initial load)
        else:
            context['field_type'] = 'customer_name'
            context['field'] = ProformaInvoiceSearchForm().fields['customer_name'].widget.render(
                name='customer_name', value='', attrs={
                    'id': 'id_customer_name',
                    'class': 'box3 w-full py-2 px-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500',
                    'placeholder': 'Enter Customer Name',
                    'hx-get': '/proformainvoice/autocomplete/customer/',
                    'hx-trigger': 'keyup changed delay:300ms, search',
                    'hx-target': '#customer-autocomplete-results',
                    'hx-swap': 'innerHTML',
                    '_': 'on keyup if event.target.value.length > 0 add .show to #customer-autocomplete-results else remove .show from #customer-autocomplete-results',
                }
            )
        
        return render(request, 'proformainvoice/_search_input_partial.html', context)

class CustomerAutocompleteView(View):
    """
    HTMX-triggered view that provides customer name suggestions for the autocomplete.
    Matches the functionality of the ASP.NET WebMethod.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('customer_name', '').strip()
        suggestions = []
        if prefix_text:
            comp_id, _ = get_current_company_financial_year_context()
            # Search customers by name or ID starting with the prefix, filtered by company
            customers = Customer.objects.filter(
                Q(CustomerName__istartswith=prefix_text) | Q(CustomerId__istartswith=prefix_text),
                CompId=comp_id
            ).order_by('CustomerName')[:10] # Limit suggestions for performance
            
            suggestions = [
                {'name': customer.CustomerName, 'id': customer.CustomerId}
                for customer in customers
            ]
        
        return render(request, 'proformainvoice/_customer_autocomplete_results.html', {'suggestions': suggestions, 'query': prefix_text})


# --- Generic CRUD Views (as requested by template, not directly from ASPX) ---
class ProformaInvoiceCreateView(CreateView):
    """
    Handles creating new Proforma Invoice records.
    """
    model = ProformaInvoice
    form_class = ProformaInvoiceForm
    template_name = 'proformainvoice/proformainvoice_form.html'
    success_url = reverse_lazy('proformainvoice_list')

    def form_valid(self, form):
        # Assign current company and financial year if not already set by the form
        if form.instance.CompId is None:
            form.instance.CompId = COMP_ID
        if form.instance.FinYearId is None:
            form.instance.FinYearId = FIN_YEAR_ID
        
        response = super().form_valid(form)
        messages.success(self.request, f'Proforma Invoice {self.object.InvoiceNo} added successfully.')
        
        # HTMX-specific response: No content, but trigger a refresh of the list
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # 204 No Content for successful HTMX form submission
                headers={
                    'HX-Trigger': 'refreshProformaInvoiceList' # Custom event to refresh the table
                }
            )
        return response

class ProformaInvoiceUpdateView(UpdateView):
    """
    Handles updating existing Proforma Invoice records.
    """
    model = ProformaInvoice
    form_class = ProformaInvoiceForm
    template_name = 'proformainvoice/proformainvoice_form.html'
    success_url = reverse_lazy('proformainvoice_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, f'Proforma Invoice {self.object.InvoiceNo} updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshProformaInvoiceList'
                }
            )
        return response

class ProformaInvoiceDeleteView(DeleteView):
    """
    Handles deleting Proforma Invoice records.
    """
    model = ProformaInvoice
    template_name = 'proformainvoice/proformainvoice_confirm_delete.html'
    success_url = reverse_lazy('proformainvoice_list')

    def delete(self, request, *args, **kwargs):
        obj_invoice_no = self.get_object().InvoiceNo # Get value before deletion
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, f'Proforma Invoice {obj_invoice_no} deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshProformaInvoiceList'
                }
            )
        return response

class ProformaInvoiceSelectView(View):
    """
    Simulates the "Select" action from ASP.NET,
    which typically redirects to a detail/edit page.
    In Django, this would be a simple redirect to another URL.
    """
    def get(self, request, pk, *args, **kwargs):
        try:
            proforma_invoice = ProformaInvoice.objects.get(pk=pk)
            # In a real application, you would redirect to a detail or edit page:
            # from django.shortcuts import redirect
            # return redirect('proformainvoice_detail', pk=pk)

            # For this example, we return a message for HTMX to display in the modal
            message = f"<div class='p-4 text-green-700 bg-green-100 rounded shadow-md'>Selected Proforma Invoice No: <strong>{proforma_invoice.InvoiceNo}</strong> (ID: {pk}). Details would load here.</div>"
            if request.headers.get('HX-Request'):
                return HttpResponse(message)
            return HttpResponse(f"You selected Proforma Invoice: {proforma_invoice.InvoiceNo}")
        except ProformaInvoice.DoesNotExist:
            return HttpResponse(f"<div class='p-4 text-red-700 bg-red-100 rounded'>Proforma Invoice with ID {pk} not found.</div>", status=404)

```

### 4.4 Templates

Task: Create templates for each view.

Templates are designed to be compact and reusable, utilizing HTMX for dynamic content and `core/base.html` for layout inheritance (not included in output). Tailwind CSS classes are used for styling. DataTables will be initialized on the dynamically loaded table partial.

```html
<!-- proformainvoice/proformainvoice_list.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0">
        <h2 class="text-2xl font-bold text-gray-800">Proforma Invoice - Edit</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'proformainvoice_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Proforma Invoice
        </button>
    </div>

    <!-- Search Form -->
    <div class="bg-white p-6 rounded-lg shadow-md mb-6 border border-gray-200">
        <form hx-get="{% url 'proformainvoice_table' %}" hx-target="#proformainvoiceTable-container" hx-swap="innerHTML" hx-trigger="submit, change from:#id_search_by">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                <div>
                    <label for="{{ search_form.search_by.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Search By:</label>
                    {{ search_form.search_by }}
                </div>
                <div id="search-input-container" class="relative">
                    <!-- This content will be swapped by HTMX based on search_by selection -->
                    {% if search_form.search_by.value == '0' or not search_form.search_by.value %}
                        <!-- Initial or 'Customer Name' selected -->
                        <label for="{{ search_form.customer_name.id_for_label }}" class="sr-only">Customer Name</label>
                        {{ search_form.customer_name }}
                        <div id="customer-autocomplete-results" class="absolute z-10 bg-white border border-gray-300 rounded-b-md shadow-lg max-h-60 overflow-y-auto w-full mt-1 hidden">
                            <!-- Autocomplete results loaded here -->
                        </div>
                    {% elif search_form.search_by.value in '23' %}
                        <!-- 'PO No' or 'Invoice No' selected -->
                        <label for="{{ search_form.po_or_invoice_no.id_for_label }}" class="sr-only">PO/Invoice No</label>
                        {{ search_form.po_or_invoice_no }}
                    {% endif %}
                </div>
                <div>
                    <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                        Search
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Data Table Container -->
    <div id="proformainvoiceTable-container"
         hx-trigger="load, refreshProformaInvoiceList from:body"
         hx-get="{% url 'proformainvoice_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Proforma Invoices...</p>
        </div>
    </div>

    <!-- Modal for form & delete confirmation (hidden by default) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me then remove .show from #customer-autocomplete-results">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-2xl w-full relative">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Add jQuery for DataTables before DataTables script -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.tailwindcss.min.js"></script>
<link href="https://cdn.datatables.net/2.0.7/css/dataTables.tailwindcss.min.css" rel="stylesheet">


<script>
    // Alpine.js component for modal visibility and other UI state if needed
    document.addEventListener('alpine:init', () => {
        Alpine.data('modalController', () => ({
            open: false,
            showModal() {
                this.open = true;
                document.getElementById('modal').classList.add('is-active');
                document.body.style.overflow = 'hidden'; // Prevent scrolling background
            },
            hideModal() {
                this.open = false;
                document.getElementById('modal').classList.remove('is-active');
                document.getElementById('modalContent').innerHTML = ''; // Clear modal content
                document.body.style.overflow = 'auto'; // Restore scrolling
            },
            init() {
                // Listen for HTMX events to show/hide modal
                this.$el.addEventListener('htmx:afterSwap', (evt) => {
                    // If content was swapped into modalContent and status is 200 (success)
                    if (evt.detail.target.id === 'modalContent' && evt.detail.xhr.status === 200) {
                        this.showModal();
                    }
                });
                this.$el.addEventListener('htmx:afterSettle', (evt) => {
                    // Check if a form submission caused a 204 response (successful HTMX form submit)
                    if (evt.detail.requestConfig && evt.detail.requestConfig.triggeringEvent && 
                        evt.detail.requestConfig.triggeringEvent.type === 'submit' && evt.detail.xhr.status === 204) {
                        this.hideModal();
                    }
                });
            }
        }));
    });

    // Event listener for selecting an item from autocomplete results
    document.addEventListener('DOMContentLoaded', function() {
        document.body.addEventListener('click', function(e) {
            const autocompleteItem = e.target.closest('.autocomplete-item');
            if (autocompleteItem) {
                const customerNameInput = document.getElementById('id_customer_name');
                if (customerNameInput) {
                    customerNameInput.value = autocompleteItem.dataset.value; // Use data-value for full string
                    document.getElementById('customer-autocomplete-results').classList.remove('show');
                    // Optionally trigger the search form submission after selecting
                    const searchForm = customerNameInput.closest('form');
                    if (searchForm) {
                        htmx.trigger(searchForm, 'submit');
                    }
                }
            }
        });

        // Hide autocomplete results when clicking outside the input/results area
        document.addEventListener('click', function(event) {
            const customerInputContainer = document.querySelector('#search-input-container');
            const autocompleteResults = document.getElementById('customer-autocomplete-results');
            if (customerInputContainer && autocompleteResults && !customerInputContainer.contains(event.target)) {
                autocompleteResults.classList.remove('show');
            }
        });
    });
</script>
{% endblock %}
```

```html
<!-- proformainvoice/_proformainvoice_table.html -->
<!-- This partial is loaded dynamically by HTMX into the list page -->
<div class="overflow-x-auto bg-white rounded-lg shadow-md border border-gray-200">
    <table id="proformainvoiceTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">FinYear</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Invoice No</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Date</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Customer Name</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">WO No</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">PO No</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in proformainvoices %}
            <tr class="hover:bg-gray-50 transition-colors duration-150 ease-in-out">
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ obj.financial_year }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ obj.InvoiceNo }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ obj.formatted_sys_date }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ obj.customer_details }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ obj.work_order_numbers }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ obj.PONo }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm">
                    <button
                        class="bg-green-500 hover:bg-green-600 text-white font-medium py-1 px-3 rounded-md text-xs shadow-sm transition duration-150 ease-in-out mr-2"
                        hx-get="{% url 'proformainvoice_select' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Select
                    </button>
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-medium py-1 px-3 rounded-md text-xs shadow-sm transition duration-150 ease-in-out mr-2"
                        hx-get="{% url 'proformainvoice_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-600 text-white font-medium py-1 px-3 rounded-md text-xs shadow-sm transition duration-150 ease-in-out"
                        hx-get="{% url 'proformainvoice_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="py-4 px-4 text-center text-lg text-red-700 font-semibold">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
// DataTables initialization (client-side)
$(document).ready(function() {
    // Destroy existing DataTable instance if it exists to prevent re-initialization errors
    if ($.fn.DataTable.isDataTable('#proformainvoiceTable')) {
        $('#proformainvoiceTable').DataTable().destroy();
    }
    $('#proformainvoiceTable').DataTable({
        "pageLength": 20, // Matching ASP.NET PageSize
        "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 7] } // SN and Actions columns not sortable
        ],
        "searching": true, // Enable default DataTables search box
        "paging": true,
        "info": true
    });
});
</script>
```

```html
<!-- proformainvoice/_search_input_partial.html -->
<!-- This partial is loaded dynamically by HTMX to swap search input fields -->
{% if field_type == 'customer_name' %}
    <label for="id_customer_name" class="block text-sm font-medium text-gray-700 mb-1">Customer Name:</label>
    <input type="text" name="customer_name" id="id_customer_name" value=""
           class="box3 w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="Enter Customer Name"
           hx-get="{% url 'customer_autocomplete' %}"
           hx-trigger="keyup changed delay:300ms, search"
           hx-target="#customer-autocomplete-results"
           hx-swap="innerHTML"
           _="on keyup if event.target.value.length > 0 add .show to #customer-autocomplete-results else remove .show from #customer-autocomplete-results">
    <div id="customer-autocomplete-results" class="absolute z-10 bg-white border border-gray-300 rounded-b-md shadow-lg max-h-60 overflow-y-auto w-full mt-1 hidden">
        <!-- Autocomplete results will be loaded here -->
    </div>
{% elif field_type == 'po_or_invoice_no' %}
    <label for="id_po_or_invoice_no" class="block text-sm font-medium text-gray-700 mb-1">PO/Invoice No:</label>
    <input type="text" name="po_or_invoice_no" id="id_po_or_invoice_no" value=""
           class="box3 w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="Enter PO No or Invoice No">
{% endif %}
```

```html
<!-- proformainvoice/_customer_autocomplete_results.html -->
<!-- This partial is loaded dynamically by HTMX for autocomplete suggestions -->
{% if suggestions %}
    <ul class="autocomplete-list show">
    {% for suggestion in suggestions %}
        <li class="autocomplete-item p-2 hover:bg-blue-100 cursor-pointer text-sm border-b border-gray-100 last:border-b-0"
            data-value="{{ suggestion.name }} [{{ suggestion.id }}]">
            {{ suggestion.name }} [{{ suggestion.id }}]
        </li>
    {% endfor %}
    </ul>
{% else %}
    {% if query %}
        <div class="p-2 text-gray-500 text-sm">No customers found matching "{{ query }}".</div>
    {% endif %}
{% endif %}
```

```html
<!-- proformainvoice/proformainvoice_form.html -->
<!-- This partial is loaded into the modal for Add/Edit operations -->
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Proforma Invoice</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-600 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-150 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

```html
<!-- proformainvoice/proformainvoice_confirm_delete.html -->
<!-- This partial is loaded into the modal for Delete confirmation -->
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete Proforma Invoice <strong>"{{ object.InvoiceNo }}"</strong> (ID: {{ object.pk }})?</p>
    <form hx-post="{% url 'proformainvoice_delete' object.pk %}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-150 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs

Task: Define URL patterns for the views.

The `urls.py` file defines the routes for the main list page, HTMX-specific partials (for table refresh, search field swaps, and autocomplete), and the generic CRUD operations.

```python
# proformainvoice/urls.py
from django.urls import path
from .views import (
    ProformaInvoiceListView,
    ProformaInvoiceTablePartialView,
    HxUpdateSearchFieldView,
    CustomerAutocompleteView,
    ProformaInvoiceCreateView,
    ProformaInvoiceUpdateView,
    ProformaInvoiceDeleteView,
    ProformaInvoiceSelectView,
)

urlpatterns = [
    # Main list page
    path('proformainvoice/', ProformaInvoiceListView.as_view(), name='proformainvoice_list'),
    
    # HTMX endpoints for dynamic content
    path('proformainvoice/table/', ProformaInvoiceTablePartialView.as_view(), name='proformainvoice_table'),
    path('proformainvoice/hx_update_search_field/', HxUpdateSearchFieldView.as_view(), name='hx_update_search_field'),
    path('proformainvoice/autocomplete/customer/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),
    
    # Generic CRUD operations for Proforma Invoice
    path('proformainvoice/add/', ProformaInvoiceCreateView.as_view(), name='proformainvoice_add'),
    path('proformainvoice/edit/<int:pk>/', ProformaInvoiceUpdateView.as_view(), name='proformainvoice_edit'),
    path('proformainvoice/delete/<int:pk>/', ProformaInvoiceDeleteView.as_view(), name='proformainvoice_delete'),
    
    # "Select" action (similar to original ASP.NET redirect)
    path('proformainvoice/select/<int:pk>/', ProformaInvoiceSelectView.as_view(), name='proformainvoice_select'),
]
```

### 4.6 Tests

Task: Write tests for the model and views.

Comprehensive tests ensure the reliability and correctness of the migrated application. Unit tests will cover individual model methods and properties, while integration tests will validate the behavior of views and HTMX interactions.

```python
# proformainvoice/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch
from datetime import datetime

from .models import FinancialYear, Customer, WorkOrder, ProformaInvoice, get_current_company_financial_year_context
from .forms import ProformaInvoiceSearchForm, ProformaInvoiceForm

# Mock the global company/financial year context getter for isolated tests
@patch('proformainvoice.models.get_current_company_financial_year_context', return_value=(1, 1))
@patch('proformainvoice.views.get_current_company_financial_year_context', return_value=(1, 1))
class ProformaInvoiceModelTest(TestCase):
    @classmethod
    def setUpTestData(cls, mock_models_context, mock_views_context):
        # Create test data for all tests within this class
        cls.comp_id, cls.fin_year_id = get_current_company_financial_year_context() # Get patched values
        
        FinancialYear.objects.create(Id=cls.fin_year_id, FinYear='2023-24', CompId=cls.comp_id)
        Customer.objects.create(CustomerId='CUST001', CustomerName='Test Customer One', CompId=cls.comp_id)
        Customer.objects.create(CustomerId='CUST002', CustomerName='Another Customer', CompId=cls.comp_id)
        WorkOrder.objects.create(Id=101, WONo='WO-101', CompId=cls.comp_id)
        WorkOrder.objects.create(Id=102, WONo='WO-102', CompId=cls.comp_id)

        ProformaInvoice.objects.create(
            Id=1,
            FinYearId=cls.fin_year_id,
            SysDate=datetime(2023, 1, 15),
            InvoiceNo='PI-001',
            WONo='101,102,', # Comma-separated IDs
            PONo='PO-XYZ-123',
            CustomerCode='CUST001',
            CompId=cls.comp_id
        )
        ProformaInvoice.objects.create(
            Id=2,
            FinYearId=cls.fin_year_id,
            SysDate=datetime(2023, 2, 20),
            InvoiceNo='PI-002',
            WONo='101,',
            PONo='PO-ABC-456',
            CustomerCode='CUST002',
            CompId=cls.comp_id
        )
        # Create a proforma invoice that doesn't match the current fin_year_id for filtering tests
        ProformaInvoice.objects.create(
            Id=3,
            FinYearId=999, # Different FinYearId
            SysDate=datetime(2022, 11, 1),
            InvoiceNo='PI-003',
            WONo='',
            PONo='PO-OLD-789',
            CustomerCode='CUST001',
            CompId=cls.comp_id
        )
        # Create a proforma invoice with a different company ID
        ProformaInvoice.objects.create(
            Id=4,
            FinYearId=cls.fin_year_id,
            SysDate=datetime(2023, 3, 10),
            InvoiceNo='PI-004',
            WONo='',
            PONo='PO-DIFF-000',
            CustomerCode='CUST001',
            CompId=999 # Different CompId
        )
        # Create a proforma invoice for testing unknown customer/WO
        ProformaInvoice.objects.create(
            Id=5,
            FinYearId=cls.fin_year_id,
            SysDate=datetime(2023, 4, 1),
            InvoiceNo='PI-005',
            WONo='101,999,', # 999 does not exist
            PONo='PO-UNK-111',
            CustomerCode='UNKNOWN', # Non-existent customer
            CompId=cls.comp_id
        )


    def test_proformainvoice_creation(self):
        obj = ProformaInvoice.objects.get(Id=1)
        self.assertEqual(obj.InvoiceNo, 'PI-001')
        self.assertEqual(obj.CustomerCode, 'CUST001')
        self.assertEqual(obj.CompId, self.comp_id)
        self.assertEqual(obj.FinYearId, self.fin_year_id)

    def test_financial_year_property(self):
        obj = ProformaInvoice.objects.get(Id=1)
        self.assertEqual(obj.financial_year, '2023-24')
        obj_unknown_fy = ProformaInvoice.objects.get(Id=3)
        self.assertEqual(obj_unknown_fy.financial_year, 'N/A')

    def test_formatted_sys_date_property(self):
        obj = ProformaInvoice.objects.get(Id=1)
        self.assertEqual(obj.formatted_sys_date, '15/01/2023')
        obj.SysDate = None # Test null date
        self.assertEqual(obj.formatted_sys_date, '')

    def test_customer_details_property(self):
        obj = ProformaInvoice.objects.get(Id=1)
        self.assertEqual(obj.customer_details, 'Test Customer One [CUST001]')
        
        # Test case for unknown customer
        unknown_pi = ProformaInvoice.objects.get(Id=5)
        self.assertEqual(unknown_pi.customer_details, 'Unknown Customer [UNKNOWN]')

    def test_work_order_numbers_property(self):
        obj1 = ProformaInvoice.objects.get(Id=1)
        self.assertEqual(obj1.work_order_numbers, 'WO-101, WO-102')

        obj2 = ProformaInvoice.objects.get(Id=2)
        self.assertEqual(obj2.work_order_numbers, 'WO-101')
        
        # Test empty WO No
        obj3 = ProformaInvoice.objects.get(Id=3)
        self.assertEqual(obj3.work_order_numbers, '')
        
        # Test missing WO IDs in WorkOrder master
        obj5 = ProformaInvoice.objects.get(Id=5)
        self.assertEqual(obj5.work_order_numbers, 'WO-101, Unknown WO [999]')

    def test_proformainvoice_queryset_filtering(self):
        # Test for_current_company_and_financial_year
        filtered_qs = ProformaInvoice.objects.for_current_company_and_financial_year()
        self.assertEqual(filtered_qs.count(), 3) # PI-001, PI-002, PI-005
        self.assertTrue(filtered_qs.filter(InvoiceNo='PI-001').exists())
        self.assertTrue(filtered_qs.filter(InvoiceNo='PI-002').exists())
        self.assertTrue(filtered_qs.filter(InvoiceNo='PI-005').exists())
        self.assertFalse(filtered_qs.filter(InvoiceNo='PI-003').exists()) # Wrong FinYearId
        self.assertFalse(filtered_qs.filter(InvoiceNo='PI-004').exists()) # Wrong CompId

        # Test search_by_customer_name
        search_cust_qs = filtered_qs.search_by_customer_name('Test Customer One [CUST001]')
        self.assertEqual(search_cust_qs.count(), 1) # PI-001. PI-005 has unknown customer code, not found by this.
        self.assertTrue(search_cust_qs.filter(InvoiceNo='PI-001').exists())

        search_cust_qs_no_match = filtered_qs.search_by_customer_name('NonExistent [NONEX]')
        self.assertEqual(search_cust_qs_no_match.count(), 0)
        search_cust_qs_invalid_format = filtered_qs.search_by_customer_name('Invalid Customer')
        self.assertEqual(search_cust_qs_invalid_format.count(), 0)

        # Test search_by_po_no
        search_po_qs = filtered_qs.search_by_po_no('PO-XYZ-123')
        self.assertEqual(search_po_qs.count(), 1)
        self.assertTrue(search_po_qs.filter(InvoiceNo='PI-001').exists())

        # Test search_by_invoice_no
        search_inv_qs = filtered_qs.search_by_invoice_no('PI-002')
        self.assertEqual(search_inv_qs.count(), 1)
        self.assertTrue(search_inv_qs.filter(InvoiceNo='PI-002').exists())

class ProformaInvoiceViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Set up a client for testing
        cls.client = Client()
        
        # Create test data (using the patched values)
        cls.comp_id, cls.fin_year_id = get_current_company_financial_year_context() # Get patched values
        
        FinancialYear.objects.create(Id=cls.fin_year_id, FinYear='2023-24', CompId=cls.comp_id)
        Customer.objects.create(CustomerId='CUST001', CustomerName='Test Customer One', CompId=cls.comp_id)
        Customer.objects.create(CustomerId='CUST002', CustomerName='Another Customer', CompId=cls.comp_id)
        WorkOrder.objects.create(Id=101, WONo='WO-101', CompId=cls.comp_id)
        WorkOrder.objects.create(Id=102, WONo='WO-102', CompId=cls.comp_id)

        ProformaInvoice.objects.create(
            Id=1,
            FinYearId=cls.fin_year_id,
            SysDate=datetime(2023, 1, 15),
            InvoiceNo='PI-001',
            WONo='101,102,',
            PONo='PO-XYZ-123',
            CustomerCode='CUST001',
            CompId=cls.comp_id
        )
        ProformaInvoice.objects.create(
            Id=2,
            FinYearId=cls.fin_year_id,
            SysDate=datetime(2023, 2, 20),
            InvoiceNo='PI-002',
            WONo='101,',
            PONo='PO-ABC-456',
            CustomerCode='CUST002',
            CompId=cls.comp_id
        )

    def test_list_view_get(self):
        response = self.client.get(reverse('proformainvoice_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'proformainvoice/proformainvoice_list.html')
        self.assertIn('search_form', response.context)
        # The proformainvoices context object would be empty initially, as the table is loaded by HTMX
        self.assertIsInstance(response.context['search_form'], ProformaInvoiceSearchForm)

    def test_table_partial_view_get_no_search(self):
        response = self.client.get(reverse('proformainvoice_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'proformainvoice/_proformainvoice_table.html')
        self.assertIn('proformainvoices', response.context)
        self.assertEqual(response.context['proformainvoices'].count(), 2)
        self.assertContains(response, 'PI-001')
        self.assertContains(response, 'PI-002')

    def test_table_partial_view_search_by_customer_name(self):
        # We simulate the value passed by HTMX from the autocomplete
        response = self.client.get(reverse('proformainvoice_table'), {'search_by': '0', 'customer_name': 'Test Customer One [CUST001]'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['proformainvoices'].count(), 1)
        self.assertContains(response, 'PI-001')
        self.assertNotContains(response, 'PI-002')
        response_no_match = self.client.get(reverse('proformainvoice_table'), {'search_by': '0', 'customer_name': 'NonExistent [NONEX]'})
        self.assertEqual(response_no_match.status_code, 200)
        self.assertEqual(response_no_match.context['proformainvoices'].count(), 0)

    def test_table_partial_view_search_by_po_no(self):
        response = self.client.get(reverse('proformainvoice_table'), {'search_by': '2', 'po_or_invoice_no': 'PO-XYZ-123'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['proformainvoices'].count(), 1)
        self.assertContains(response, 'PI-001')
        self.assertNotContains(response, 'PI-002')

    def test_table_partial_view_search_by_invoice_no(self):
        response = self.client.get(reverse('proformainvoice_table'), {'search_by': '3', 'po_or_invoice_no': 'PI-002'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['proformainvoices'].count(), 1)
        self.assertContains(response, 'PI-002')
        self.assertNotContains(response, 'PI-001')

    def test_hx_update_search_field_customer(self):
        response = self.client.get(reverse('hx_update_search_field'), {'search_by': '0'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'proformainvoice/_search_input_partial.html')
        self.assertIn('customer_name', response.content.decode()) # Check for input field name
        self.assertIn('hx-get="/proformainvoice/autocomplete/customer/"', response.content.decode())

    def test_hx_update_search_field_po_invoice(self):
        response = self.client.get(reverse('hx_update_search_field'), {'search_by': '2'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'proformainvoice/_search_input_partial.html')
        self.assertIn('po_or_invoice_no', response.content.decode()) # Check for input field name
        self.assertNotIn('hx-get="/proformainvoice/autocomplete/customer/"', response.content.decode())

    def test_customer_autocomplete_view(self):
        response = self.client.get(reverse('customer_autocomplete'), {'customer_name': 'test'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'proformainvoice/_customer_autocomplete_results.html')
        self.assertContains(response, 'Test Customer One [CUST001]')
        self.assertNotContains(response, 'Another Customer')

        response_no_match = self.client.get(reverse('customer_autocomplete'), {'customer_name': 'xyz'})
        self.assertEqual(response_no_match.status_code, 200)
        self.assertNotContains(response_no_match, 'Test Customer One')
        self.assertContains(response_no_match, 'No customers found matching "xyz".')
        
        response_empty_query = self.client.get(reverse('customer_autocomplete'), {'customer_name': ''})
        self.assertEqual(response_empty_query.status_code, 200)
        self.assertNotContains(response_empty_query, 'No customers found.') # Should not show 'no customers found' if query is empty

    def test_create_view_get(self):
        response = self.client.get(reverse('proformainvoice_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'proformainvoice/proformainvoice_form.html')
        self.assertTrue('form' in response.context)

    def test_create_view_post_htmx(self):
        data = {
            'FinYearId': self.fin_year_id,
            'SysDate': '2024-03-01',
            'InvoiceNo': 'PI-NEW-001',
            'WONo': '101,',
            'PONo': 'PO-NEW-001',
            'CustomerCode': 'CUST001',
            'CompId': self.comp_id
        }
        # Get initial count
        initial_count = ProformaInvoice.objects.count()
        response = self.client.post(reverse('proformainvoice_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX expects 204 for success with trigger
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshProformaInvoiceList')
        self.assertTrue(ProformaInvoice.objects.filter(InvoiceNo='PI-NEW-001').exists())
        self.assertEqual(ProformaInvoice.objects.count(), initial_count + 1)

    def test_update_view_get(self):
        obj = ProformaInvoice.objects.get(Id=1)
        response = self.client.get(reverse('proformainvoice_edit', args=[obj.Id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'proformainvoice/proformainvoice_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance.InvoiceNo, 'PI-001')

    def test_update_view_post_htmx(self):
        obj = ProformaInvoice.objects.get(Id=1)
        data = {
            'FinYearId': self.fin_year_id,
            'SysDate': '2023-01-15',
            'InvoiceNo': 'PI-001-UPDATED',
            'WONo': '101,',
            'PONo': 'PO-XYZ-123-UPDATED',
            'CustomerCode': 'CUST001',
            'CompId': self.comp_id
        }
        response = self.client.post(reverse('proformainvoice_edit', args=[obj.Id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshProformaInvoiceList')
        obj.refresh_from_db()
        self.assertEqual(obj.InvoiceNo, 'PI-001-UPDATED')

    def test_delete_view_get(self):
        obj = ProformaInvoice.objects.get(Id=1)
        response = self.client.get(reverse('proformainvoice_delete', args=[obj.Id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'proformainvoice/proformainvoice_confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'].InvoiceNo, 'PI-001')

    def test_delete_view_post_htmx(self):
        obj = ProformaInvoice.objects.get(Id=1)
        # Get initial count
        initial_count = ProformaInvoice.objects.count()
        response = self.client.post(reverse('proformainvoice_delete', args=[obj.Id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshProformaInvoiceList')
        self.assertFalse(ProformaInvoice.objects.filter(Id=obj.Id).exists())
        self.assertEqual(ProformaInvoice.objects.count(), initial_count - 1)

    def test_select_view(self):
        obj = ProformaInvoice.objects.get(Id=1)
        response = self.client.get(reverse('proformainvoice_select', args=[obj.Id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, f"Selected Proforma Invoice No: <strong>{obj.InvoiceNo}</strong>")
        self.assertContains(response, "Details would load here.")
    
    def test_select_view_not_found(self):
        response = self.client.get(reverse('proformainvoice_select', args=[99999]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 404)
        self.assertContains(response, "Proforma Invoice with ID 99999 not found.")

```

## Step 5: HTMX and Alpine.js Integration

The modernized Django application leverages HTMX and Alpine.js to create a highly dynamic and responsive user interface, minimizing full page reloads and enhancing user experience.

*   **HTMX for Dynamic Content Loading:**
    *   **Initial Table Load & Refresh:** The `proformainvoice_list.html` template uses `hx-get` on the `#proformainvoiceTable-container` to load the `_proformainvoice_table.html` partial on page load and whenever a `refreshProformaInvoiceList` custom event is triggered (e.g., after a successful CRUD operation). This ensures the list is always up-to-date without a full page refresh.
    *   **Search Field Swapping:** The `search_by` dropdown's `hx-get` and `hx-target` attributes dynamically replace the search input field within the `#search-input-container` with either the customer name input (with autocomplete) or the generic PO/Invoice number input.
    *   **Autocomplete Suggestions:** The customer name input field uses `hx-get` to fetch live suggestions from the `customer_autocomplete` endpoint. These suggestions are swapped into the `#customer-autocomplete-results` div, providing an interactive dropdown list.
    *   **Modal Form Loading:** Buttons for "Add New", "Edit", and "Delete" leverage `hx-get` to load their respective forms or confirmation messages (`proformainvoice_form.html`, `proformainvoice_confirm_delete.html`) directly into the `#modalContent` container within a hidden modal.

*   **HTMX for Form Submissions:**
    *   Forms loaded inside the modal (for Add/Edit/Delete) use `hx-post` with `hx-swap="none"` and `hx-trigger="submit"`. Upon successful submission, the Django view returns a `204 No Content` status along with an `HX-Trigger` header carrying the `refreshProformaInvoiceList` event. This allows HTMX to signal a successful operation without needing to swap new content back, while simultaneously triggering the main table to reload.

*   **Alpine.js for UI State Management:**
    *   A simple Alpine.js component (`x-data="modalController"`) is integrated with the main `#modal` element. This component manages the `open` state of the modal, toggling CSS classes (`is-active`, `hidden`) and preventing background scrolling. It listens for HTMX events to automatically show the modal when content is loaded into `#modalContent` and hide it after successful form submissions (when a `204` response is received).
    *   Hyperscript (`_`) is used for minimal inline JavaScript, such as showing/hiding the autocomplete results container based on input length and directly toggling the modal's `is-active` class.

*   **DataTables for List Views:**
    *   The `_proformainvoice_table.html` partial includes a JavaScript block that initializes DataTables on the table element (`#proformainvoiceTable`). This enables client-side features like search, sorting, and pagination (matching the original ASP.NET `PageSize="20"`), providing a rich and interactive data grid experience. jQuery and DataTables CDN links are included in the `extra_js` block of the base template.

This holistic integration of HTMX, Alpine.js, and DataTables ensures that all dynamic interactions, from simple dropdown changes to complex CRUD operations, are handled efficiently and seamlessly without requiring cumbersome custom JavaScript frameworks, aligning perfectly with the goal of an automated, modern, and performable application.