This document outlines a comprehensive modernization plan to transition your legacy ASP.NET Asset Register application to a modern, robust, and scalable Django-based solution. Our approach prioritizes automation, leverages modern web standards like HTMX and Alpine.js, and adheres to Django best practices for maintainability and performance.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination (where applicable for list views)
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

## Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code interacts with two primary database tables:
1.  `tblACC_Asset_Category`: Used to populate the main category dropdown (`ddlCategory`).
2.  `tblACC_Asset_SubCategory`: Used to populate the sub-category dropdown (`ddlSubCategory`) dynamically based on the selected category. The `MId` column in this table is used to link subcategories to their parent categories.

**Inferred Schema:**

*   **`tblACC_Asset_Category`**
    *   `Id` (Primary Key, Integer)
    *   `Abbrivation` (String, represents category name)

*   **`tblACC_Asset_SubCategory`**
    *   `Id` (Primary Key, Integer)
    *   `Abbrivation` (String, represents sub-category name)
    *   `MId` (Integer, references `Id` in `tblACC_Asset_Category` for parent category, or `0` for top-level/uncategorized subcategories).

## Step 2: Identify Backend Functionality

**Task:** Determine the operations performed by the ASP.NET code.

**Analysis:**
The ASP.NET Asset Register page primarily serves as a search and filtering interface for assets, eventually directing users to a separate report page.

*   **Data Retrieval:**
    *   Fetches all asset categories from `tblACC_Asset_Category` on page load.
    *   Dynamically fetches asset subcategories from `tblACC_Asset_SubCategory` based on the selected asset category. There's a special case where `MId='0'` is used to retrieve an initial set of subcategories, implying top-level or unassigned subcategories.
*   **User Interface Control:**
    *   Manages the visibility of category and subcategory dropdowns based on a "Search All" or "Category" selection.
*   **Report Generation Trigger:**
    *   On "Search" button click, it constructs a URL with selected category (`CAT`), subcategory (`SCAT`), and a random key (`Key`) parameters.
    *   This URL is then loaded into an `iframe` on the page, displaying an external report (`AssetRegister_Report.aspx`).
*   **Validation:**
    *   A client-side alert prompts the user to select a category if "Category" search is chosen but no category is selected.

**No Direct CRUD Operations on this page:** The page itself does not perform direct create, update, or delete operations on asset records. It serves as a data filtering and report launching mechanism.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**
The ASP.NET page uses standard web form controls for user interaction:

*   **Search Type Dropdown (`DropDownList1`):** Allows selecting between "Search All" (Value=0) and "Category" (Value=1). This controls the visibility of other filter options.
*   **Category Dropdown (`ddlCategory`):** Populated with data from `tblACC_Asset_Category`. Visible only when "Category" search type is selected.
*   **Sub-Category Dropdown (`ddlSubCategory`):** Dynamically populated based on `ddlCategory` selection. Visible only when "Category" search type is selected.
*   **Search Button (`btnSearch`):** Triggers the logic to construct and load the report URL.
*   **Report Frame (`ifrm`):** An `iframe` element used to embed the `AssetRegister_Report.aspx` page content.

**Django Equivalent Mapping:**

*   Dropdowns will be rendered using Django Forms (`forms.ChoiceField`).
*   Dynamic updates will be handled by HTMX (`hx-get`, `hx-trigger`, `hx-target`).
*   The `iframe` will be replaced with a `div` where report content (rendered as a partial HTML response from a Django view) can be loaded via HTMX, providing a more integrated user experience.

## Step 4: Generate Django Code

We will structure the Django application in an `assets` directory.

### 4.1 Models (`assets/models.py`)

**Task:** Create Django models based on the identified database schema.

**Instructions:**
We define `AssetCategory` and `AssetSubCategory` models. `managed = False` is crucial as these tables already exist in your legacy database. The `AssetSubCategory` includes a custom manager to encapsulate the logic for retrieving subcategories based on a parent category ID, handling the special `MId='0'` case identified in the ASP.NET code.

```python
from django.db import models

class AssetCategory(models.Model):
    # Map to existing 'Id' column as primary key
    id = models.IntegerField(db_column='Id', primary_key=True)
    # Map to existing 'Abbrivation' column
    abbrivation = models.CharField(db_column='Abbrivation', max_length=255) # Assuming VARCHAR or NVARCHAR

    class Meta:
        managed = False  # Tells Django not to manage table creation/deletion
        db_table = 'tblACC_Asset_Category' # Specify the exact table name
        verbose_name = 'Asset Category'
        verbose_name_plural = 'Asset Categories'

    def __str__(self):
        return self.abbrivation

    @classmethod
    def get_all_categories(cls):
        """Retrieves all asset categories for dropdowns, ordered alphabetically."""
        return cls.objects.all().order_by('abbrivation')

class AssetSubCategoryManager(models.Manager):
    def get_subcategories_by_parent(self, parent_category_id):
        """
        Retrieves asset subcategories based on a parent category ID.
        If parent_category_id is '0' (as per ASP.NET logic for initial load/top-level),
        it fetches subcategories linked to MId=0. Otherwise, it filters by the given ID.
        """
        try:
            # Convert to int, handling '0' as a specific filter value
            category_int_id = int(parent_category_id)
            return self.filter(m_id=category_int_id).order_by('abbrivation')
        except (ValueError, TypeError):
            return self.none() # Return empty queryset if ID is invalid

class AssetSubCategory(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    abbrivation = models.CharField(db_column='Abbrivation', max_length=255)
    # MId directly stores the ID of the parent category as an integer
    m_id = models.IntegerField(db_column='MId', null=True, blank=True)

    objects = AssetSubCategoryManager() # Attach the custom manager

    class Meta:
        managed = False
        db_table = 'tblACC_Asset_SubCategory'
        verbose_name = 'Asset SubCategory'
        verbose_name_plural = 'Asset SubCategories'

    def __str__(self):
        return self.abbrivation

```

### 4.2 Forms (`assets/forms.py`)

**Task:** Define a Django form for the search criteria.

**Instructions:**
We'll create a non-ModelForm to handle the search type, category, and subcategory selections. This allows us to have full control over the dropdown choices and dynamic updates via HTMX.

```python
from django import forms
from .models import AssetCategory, AssetSubCategory

class AssetRegisterSearchForm(forms.Form):
    SEARCH_TYPE_CHOICES = [
        ('0', 'Search All'),
        ('1', 'Category'),
    ]

    search_type = forms.ChoiceField(
        choices=SEARCH_TYPE_CHOICES,
        label="Search Type",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '/assets/asset_register/toggle_filters/', # HTMX endpoint for toggling visibility
            'hx-target': '#filter-options-container', # Target div for filter options
            'hx-swap': 'innerHTML',
        })
    )

    category = forms.ChoiceField(
        required=False,
        label="Category",
        choices=[('', 'Select Category')] + [(str(cat.id), cat.abbrivation) for cat in AssetCategory.get_all_categories()],
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '/assets/asset_register/get_subcategories/', # HTMX endpoint for subcategories
            'hx-target': '#subcategory-dropdown-container', # Target div for subcategory dropdown
            'hx-swap': 'innerHTML',
        })
    )

    sub_category = forms.ChoiceField(
        required=False,
        label="Sub-Category",
        choices=[('', 'Select Sub-Category')], # Initially empty, populated by HTMX
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
        })
    )

    def clean(self):
        cleaned_data = super().clean()
        search_type = cleaned_data.get('search_type')
        category = cleaned_data.get('category')

        if search_type == '1' and not category:
            # Replicate ASP.NET's "Please select Category" alert
            self.add_error('category', 'Please select Category.')
        return cleaned_data

```

### 4.3 Views (`assets/views.py`)

**Task:** Implement views for the search page, dynamic filter updates, and report generation.

**Instructions:**
We'll create a main `AssetRegisterSearchView` to render the initial page. `SubCategoryHXView` and `FilterOptionsHXView` will be HTMX-specific endpoints for dynamic dropdown updates. `AssetReportView` will handle the logic for displaying the report.

```python
from django.views.generic import FormView, View
from django.shortcuts import render
from django.http import HttpResponse
from django.urls import reverse
import uuid # For generating random keys

from .models import AssetCategory, AssetSubCategory
from .forms import AssetRegisterSearchForm

# Max line limit for views in this project is 15 lines per method.
# All complex logic must be delegated to models or helper functions.

class AssetRegisterSearchView(FormView):
    """
    Main view for the Asset Register search page.
    Handles the initial display of the search form.
    """
    template_name = 'assets/assetregister/search.html'
    form_class = AssetRegisterSearchForm

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize form fields based on initial ASP.NET behavior (ddlCategory/SubCategory hidden)
        context['show_category_filters'] = False
        return context

class FilterOptionsHXView(View):
    """
    HTMX endpoint to dynamically render filter options based on search type.
    This replaces the ASP.NET Page_Load visibility logic.
    """
    def get(self, request, *args, **kwargs):
        search_type = request.GET.get('search_type', '0')
        show_category_filters = (search_type == '1')
        
        form = AssetRegisterSearchForm()
        if show_category_filters:
            # Populate category choices directly if visible
            form.fields['category'].choices = [('', 'Select Category')] + \
                                             [(str(cat.id), cat.abbrivation) for cat in AssetCategory.get_all_categories()]
            # Populate initial subcategory choices for MId=0 if categories are shown
            form.fields['sub_category'].choices = [('', 'Select Sub-Category')] + \
                                                  [(str(sub.id), sub.abbrivation) for sub in AssetSubCategory.objects.get_subcategories_by_parent('0')]
        else:
            # Clear choices if filters are hidden
            form.fields['category'].choices = [('', 'Select Category')]
            form.fields['sub_category'].choices = [('', 'Select Sub-Category')]

        return render(request, 'assets/assetregister/_filter_options.html', {
            'form': form,
            'show_category_filters': show_category_filters,
        })


class SubCategoryHXView(View):
    """
    HTMX endpoint to dynamically populate the subcategory dropdown
    based on the selected category.
    """
    def get(self, request, *args, **kwargs):
        category_id = request.GET.get('category_id', '0')
        subcategories = AssetSubCategory.objects.get_subcategories_by_parent(category_id)

        # Create form field for sub_category to render it as a Django widget
        sub_category_field = forms.ChoiceField(
            required=False,
            label="Sub-Category",
            choices=[('', 'Select Sub-Category')] + [(str(sub.id), sub.abbrivation) for sub in subcategories],
            widget=forms.Select(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            })
        )
        return render(request, 'assets/assetregister/_subcategory_select.html', {
            'sub_category_field': sub_category_field,
        })


class AssetReportView(FormView):
    """
    Handles the search logic and prepares the report URL parameters.
    This view will render the "report content" or redirect to a full report page.
    It replaces the `btnSearch_Click` logic from ASP.NET.
    """
    form_class = AssetRegisterSearchForm
    template_name = 'assets/assetregister/_report_content.html' # Render partial for HTMX

    def form_valid(self, form):
        search_type = form.cleaned_data['search_type']
        category_id = form.cleaned_data['category'] if search_type == '1' else ''
        subcategory_id = form.cleaned_data['sub_category'] if search_type == '1' else ''

        # Generate a unique key as in ASP.NET's GetRandomAlphaNumeric()
        random_key = uuid.uuid4().hex

        # Construct the report URL, replacing AssetRegister_Report.aspx
        # with a new Django report view endpoint.
        # This assumes 'asset_report_page' is the name of a URL pattern
        # for the actual report display page (which might be a full page view).
        report_url = reverse('asset_report_page')
        report_url_params = f"?CAT={category_id}&SCAT={subcategory_id}&Key={random_key}"

        # If report content is to be embedded via HTMX (as per iframe replacement)
        # We simulate this by rendering a message or the "report" directly.
        # For this example, we'll just show the generated URL and parameters.
        context = {
            'report_url': f"{report_url}{report_url_params}",
            'category_id': category_id,
            'subcategory_id': subcategory_id,
            'random_key': random_key,
            'search_type': search_type,
            'message': 'Report parameters generated successfully.'
        }
        return render(self.request, self.template_name, context)

    def form_invalid(self, form):
        # If validation fails, re-render the form with errors
        # This will be swapped back into the form area via HTMX
        return render(self.request, 'assets/assetregister/_search_form_fields.html', {
            'form': form,
            'show_category_filters': (form.cleaned_data.get('search_type') == '1'),
            'is_initial_load': False # Indicate this is a re-render after submission
        }, status=400) # Use 400 for bad request to indicate form errors

```

### 4.4 Templates (`assets/templates/assets/assetregister/`)

**Task:** Create templates for the search page and its dynamic components.

**Instructions:**
We'll create `search.html` as the main page, `_filter_options.html` and `_subcategory_select.html` as HTMX partials for dynamic updates, and `_report_content.html` for the embedded report view.

#### `search.html` (Main Page Template)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white p-6 rounded-lg shadow-lg">
        <h2 class="text-2xl font-bold mb-6 text-gray-800">Asset Register Search</h2>

        <form id="asset-search-form" hx-post="{% url 'asset_register_report' %}" hx-target="#report-container" hx-swap="innerHTML">
            {% csrf_token %}
            <div class="space-y-4">
                <div class="mb-4">
                    <label for="{{ form.search_type.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.search_type.label }}
                    </label>
                    {{ form.search_type }}
                </div>

                <div id="filter-options-container"
                     hx-trigger="load delay:100ms"
                     hx-get="{% url 'asset_register_toggle_filters' %}?search_type={{ form.search_type.value|default:'0' }}"
                     hx-swap="innerHTML">
                    <!-- Filter options will be loaded here via HTMX -->
                    <div class="text-center">
                        <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                        <p class="mt-2 text-sm text-gray-600">Loading filters...</p>
                    </div>
                </div>
            </div>

            <div class="mt-6">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Search
                </button>
            </div>
        </form>

        <div id="report-container" class="mt-8 border-t pt-8">
            <!-- Report content will be loaded here via HTMX after search -->
            <p class="text-gray-600">Enter your search criteria and click 'Search' to view the asset report.</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('assetRegisterState', () => ({
            // Alpine.js state for any complex UI interactions if needed
            // For simple dropdown toggling, HTMX is sufficient.
        }));
    });
</script>
{% endblock %}
```

#### `_filter_options.html` (HTMX Partial for Category/Subcategory Filters)

```html
{% comment %}
    This partial renders the category and subcategory filter options.
    It's designed to be loaded dynamically via HTMX based on search_type.
{% endcomment %}

<div id="dynamic-filters">
    {% if show_category_filters %}
        <div class="mb-4">
            <label for="{{ form.category.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.category.label }}
            </label>
            {{ form.category }}
            {% if form.category.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.category.errors }}</p>
            {% endif %}
        </div>

        <div id="subcategory-dropdown-container" class="mb-4">
            <!-- Sub-category dropdown will be loaded here via HTMX -->
            {% if form.sub_category.choices|length > 1 %} {# Check if choices were pre-populated for MId=0 #}
                <label for="{{ form.sub_category.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.sub_category.label }}
                </label>
                {{ form.sub_category }}
            {% else %}
                <div class="text-center">
                    <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-sm text-gray-600">Loading subcategories...</p>
                </div>
            {% endif %}
        </div>
    {% endif %}
</div>
```

#### `_subcategory_select.html` (HTMX Partial for Subcategory Dropdown)

```html
{% comment %}
    This partial renders only the sub-category dropdown field.
    It's loaded dynamically via HTMX when the category selection changes.
{% endcomment %}

<label for="{{ sub_category_field.id_for_label }}" class="block text-sm font-medium text-gray-700">
    {{ sub_category_field.label }}
</label>
{{ sub_category_field }}
```

#### `_report_content.html` (HTMX Partial for Embedded Report)

```html
{% comment %}
    This partial simulates the content that would have been in the iframe.
    In a real scenario, this would load the actual report data or render
    a DataTables-powered report.
{% endcomment %}

<h3 class="text-xl font-semibold mb-4">Asset Report Summary</h3>
<div class="bg-gray-100 p-4 rounded-md">
    <p class="text-gray-700 mb-2"><strong>Generated Report URL:</strong> <a href="{{ report_url }}" target="_blank" class="text-blue-600 hover:underline">{{ report_url }}</a></p>
    <p class="text-gray-700 mb-2"><strong>Category ID:</strong> {{ category_id|default:"N/A" }}</p>
    <p class="text-gray-700 mb-2"><strong>Sub-Category ID:</strong> {{ subcategory_id|default:"N/A" }}</p>
    <p class="text-gray-700"><strong>Random Key:</strong> {{ random_key }}</p>
</div>

<div class="mt-4 p-4 border rounded-md bg-white">
    <h4 class="text-lg font-medium mb-3">Actual Report Content (Placeholder)</h4>
    <p class="text-gray-600">This section would dynamically load the actual report data, perhaps into a DataTables component, using AJAX requests to the generated report URL. This avoids the use of iframes for internal content.</p>
    <p class="text-gray-600">For example, if the `report_url` serves JSON data, you would fetch it here and populate a DataTables instance.</p>
    {# Example of how a DataTables component might be initialized here if `report_url` were an API endpoint #}
    {#
    <table id="assetReportTable" class="min-w-full bg-white">
        <thead>
            <tr>
                <th>Asset Name</th>
                <th>Value</th>
                <th>Location</th>
            </tr>
        </thead>
        <tbody>
            <tr><td colspan="3" class="text-center">Loading Asset Data...</td></tr>
        </tbody>
    </table>
    <script>
        $(document).ready(function() {
            $('#assetReportTable').DataTable({
                "ajax": "{{ report_url }}", // Assuming report_url is an API endpoint
                "columns": [
                    { "data": "asset_name" },
                    { "data": "value" },
                    { "data": "location" }
                ]
            });
        });
    </script>
    #}
</div>
```

### 4.5 URLs (`assets/urls.py`)

**Task:** Define URL patterns for the views.

**Instructions:**
We'll map the main search page, the HTMX endpoints for dynamic filter changes, and the report generation endpoint.

```python
from django.urls import path
from .views import AssetRegisterSearchView, SubCategoryHXView, FilterOptionsHXView, AssetReportView

urlpatterns = [
    # Main Asset Register Search page
    path('asset_register/', AssetRegisterSearchView.as_view(), name='asset_register_search'),

    # HTMX endpoint to dynamically toggle category/subcategory filter visibility and populate initial subcategories
    path('asset_register/toggle_filters/', FilterOptionsHXView.as_view(), name='asset_register_toggle_filters'),

    # HTMX endpoint to dynamically load subcategories based on category selection
    path('asset_register/get_subcategories/', SubCategoryHXView.as_view(), name='asset_register_get_subcategories'),

    # HTMX endpoint to handle search submission and generate report parameters/content
    path('asset_register/report/', AssetReportView.as_view(), name='asset_register_report'),

    # Placeholder for the actual Asset Report page (if it's a full page view)
    # This URL is constructed and linked from the AssetReportView.
    # Replace with your actual report view in the future.
    path('asset_report_page/', lambda request: HttpResponse("<h1>This is the actual Asset Report Page</h1><p>Content would be dynamically generated here based on parameters.</p>"), name='asset_report_page'),
]

```

### 4.6 Tests (`assets/tests.py`)

**Task:** Write comprehensive tests for the models and views.

**Instructions:**
We'll include unit tests for model methods and properties, and integration tests for all views to ensure functionality and HTMX interactions are correct.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import AssetCategory, AssetSubCategory
from .forms import AssetRegisterSearchForm
import json

class AssetModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        # Simulate existing database entries
        AssetCategory.objects.create(id=1, abbrivation='Category A')
        AssetCategory.objects.create(id=2, abbrivation='Category B')
        AssetCategory.objects.create(id=3, abbrivation='Category C') # For 'MId=0' scenario

        AssetSubCategory.objects.create(id=101, abbrivation='Sub A1', m_id=1)
        AssetSubCategory.objects.create(id=102, abbrivation='Sub A2', m_id=1)
        AssetSubCategory.objects.create(id=201, abbrivation='Sub B1', m_id=2)
        AssetSubCategory.objects.create(id=301, abbrivation='Global Sub 1', m_id=0) # Simulating MId=0 case
        AssetSubCategory.objects.create(id=302, abbrivation='Global Sub 2', m_id=0)

    def test_asset_category_creation(self):
        category = AssetCategory.objects.get(id=1)
        self.assertEqual(category.abbrivation, 'Category A')
        self.assertEqual(str(category), 'Category A')

    def test_asset_category_get_all_categories(self):
        categories = AssetCategory.get_all_categories()
        self.assertEqual(categories.count(), 3)
        self.assertEqual(categories.first().abbrivation, 'Category A') # Ordered alphabetically

    def test_asset_subcategory_creation(self):
        subcategory = AssetSubCategory.objects.get(id=101)
        self.assertEqual(subcategory.abbrivation, 'Sub A1')
        self.assertEqual(subcategory.m_id, 1)
        self.assertEqual(str(subcategory), 'Sub A1')

    def test_asset_subcategory_get_by_category_id(self):
        # Test with a specific category ID
        subcategories = AssetSubCategory.objects.get_subcategories_by_parent('1')
        self.assertEqual(subcategories.count(), 2)
        self.assertTrue(AssetSubCategory.objects.filter(id=101).exists())
        self.assertTrue(AssetSubCategory.objects.filter(id=102).exists())

        # Test with '0' as category ID (global subcategories)
        global_subcategories = AssetSubCategory.objects.get_subcategories_by_parent('0')
        self.assertEqual(global_subcategories.count(), 2)
        self.assertTrue(AssetSubCategory.objects.filter(id=301).exists())
        self.assertTrue(AssetSubCategory.objects.filter(id=302).exists())

        # Test with non-existent category ID
        no_subcategories = AssetSubCategory.objects.get_subcategories_by_parent('999')
        self.assertEqual(no_subcategories.count(), 0)

        # Test with invalid category ID
        invalid_subcategories = AssetSubCategory.objects.get_subcategories_by_parent('abc')
        self.assertEqual(invalid_subcategories.count(), 0)


class AssetRegisterViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        AssetCategory.objects.create(id=1, abbrivation='Category A')
        AssetCategory.objects.create(id=2, abbrivation='Category B')
        AssetSubCategory.objects.create(id=101, abbrivation='Sub A1', m_id=1)
        AssetSubCategory.objects.create(id=102, abbrivation='Sub A2', m_id=1)
        AssetSubCategory.objects.create(id=201, abbrivation='Sub B1', m_id=2)
        AssetSubCategory.objects.create(id=301, abbrivation='Global Sub 1', m_id=0)

    def setUp(self):
        self.client = Client()

    def test_asset_register_search_view_get(self):
        response = self.client.get(reverse('asset_register_search'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'assets/assetregister/search.html')
        self.assertIsInstance(response.context['form'], AssetRegisterSearchForm)
        self.assertFalse(response.context['show_category_filters']) # Initial state

    def test_filter_options_hx_view_get_search_all(self):
        # HTMX request for "Search All" should hide category filters
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('asset_register_toggle_filters'), {'search_type': '0'}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'assets/assetregister/_filter_options.html')
        self.assertContains(response, '<div id="dynamic-filters">') # Check if div exists
        self.assertNotContains(response, 'id="id_category"') # Should not contain category dropdown

    def test_filter_options_hx_view_get_category(self):
        # HTMX request for "Category" should show category filters
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('asset_register_toggle_filters'), {'search_type': '1'}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'assets/assetregister/_filter_options.html')
        self.assertContains(response, 'id="id_category"') # Should contain category dropdown
        self.assertContains(response, 'id="id_sub_category"') # Should contain sub-category dropdown
        self.assertContains(response, '<option value="">Select Category</option>')
        self.assertContains(response, '<option value="1">Category A</option>')
        self.assertContains(response, '<option value="301">Global Sub 1</option>') # Initial MId=0 subcategories

    def test_subcategory_hx_view_get(self):
        # HTMX request to get subcategories for Category A (id=1)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('asset_register_get_subcategories'), {'category_id': '1'}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'assets/assetregister/_subcategory_select.html')
        self.assertContains(response, '<option value="">Select Sub-Category</option>')
        self.assertContains(response, '<option value="101">Sub A1</option>')
        self.assertContains(response, '<option value="102">Sub A2</option>')
        self.assertNotContains(response, '<option value="201">Sub B1</option>')

        # HTMX request to get subcategories for MId=0
        response_global = self.client.get(reverse('asset_register_get_subcategories'), {'category_id': '0'}, **headers)
        self.assertEqual(response_global.status_code, 200)
        self.assertContains(response_global, '<option value="301">Global Sub 1</option>')

    def test_asset_report_view_post_search_all(self):
        # Test search with "Search All"
        data = {'search_type': '0', 'category': '', 'sub_category': ''}
        response = self.client.post(reverse('asset_register_report'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'assets/assetregister/_report_content.html')
        self.assertContains(response, 'report_url=')
        self.assertContains(response, 'CAT=&SCAT=')
        self.assertContains(response, 'Search All')

    def test_asset_report_view_post_category_selected(self):
        # Test search with "Category" and a specific category/subcategory
        data = {'search_type': '1', 'category': '1', 'sub_category': '101'}
        response = self.client.post(reverse('asset_register_report'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'assets/assetregister/_report_content.html')
        self.assertContains(response, 'CAT=1&SCAT=101')
        self.assertContains(response, 'Category')

    def test_asset_report_view_post_category_not_selected_validation(self):
        # Test validation: "Category" search type chosen but no category selected
        data = {'search_type': '1', 'category': '', 'sub_category': ''}
        response = self.client.post(reverse('asset_register_report'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400) # Bad Request due to validation error
        self.assertTemplateUsed(response, 'assets/assetregister/_search_form_fields.html')
        self.assertContains(response, 'Please select Category.')
        self.assertContains(response, 'id="id_category"') # Form should be re-rendered with errors

```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**
The Django modernization leverages HTMX extensively for dynamic interactions, eliminating full-page reloads and simplifying frontend logic. Alpine.js is included for future, more complex UI state management, though for this specific search page, HTMX handles most dynamic behaviors.

*   **Search Type Toggle:** The `search_type` dropdown (`id_search_type`) uses `hx-get` to `asset_register_toggle_filters` and `hx-target` to `#filter-options-container`. This replaces the C# `DropDownList1_SelectedIndexChanged` visibility logic.
*   **Dynamic Subcategory Loading:** The `category` dropdown (`id_category`) uses `hx-get` to `asset_register_get_subcategories` and `hx-target` to `#subcategory-dropdown-container`. This replaces the C# `ddlCategory_SelectedIndexChanged` data binding logic.
*   **Report Generation:** The main form submits to `asset_register_report` using `hx-post` and `hx-target` to `#report-container`, allowing the "report content" to be loaded directly into the page without an `iframe` or full page refresh. This replaces the C# `btnSearch_Click` updating the `iframe` source.
*   **Form Validation:** When `AssetReportView` receives an invalid form submission (e.g., category not selected), it re-renders the `_search_form_fields.html` partial with errors, which HTMX then swaps back into the original form area, providing immediate feedback.
*   **DRY Templates:** The use of partials (`_filter_options.html`, `_subcategory_select.html`, `_report_content.html`) ensures that only necessary parts of the DOM are updated, improving performance and adhering to DRY principles.
*   **DataTables:** While not explicitly implemented in this search interface, the `_report_content.html` partial includes comments on how DataTables could be initialized if the `report_url` were to provide actual JSON data, replacing the need for the external `AssetRegister_Report.aspx` page entirely with a modern API-driven reporting solution.

## Final Notes

*   **Placeholders:** All `[PLACEHOLDER]` values from the template have been replaced with concrete names inferred from the ASP.NET code and Django best practices.
*   **Clean Code:** Views are kept thin (typically 5-15 lines per method), with business logic delegated to model methods (`AssetCategory.get_all_categories()`, `AssetSubCategory.objects.get_subcategories_by_parent()`) and form validation methods.
*   **Test Coverage:** Comprehensive unit and integration tests are provided to ensure the correctness and robustness of the migrated functionality, aiming for high test coverage.
*   **Scalability:** This Django structure is highly scalable, allowing for easy addition of new features and future expansion.
*   **Maintainability:** Strict separation of concerns (models for data logic, forms for validation/rendering, views for orchestrating requests, templates for presentation) leads to a highly maintainable codebase.
*   **Performance:** HTMX-driven partial updates eliminate full page reloads, providing a fast and responsive user experience.
*   **Business Value:** This modernization moves the application from a legacy framework to a widely-used, actively-developed, and secure modern stack (Django), reducing technical debt, improving developer productivity, and enabling faster iteration on new features. The improved user experience from dynamic updates enhances user satisfaction and operational efficiency.