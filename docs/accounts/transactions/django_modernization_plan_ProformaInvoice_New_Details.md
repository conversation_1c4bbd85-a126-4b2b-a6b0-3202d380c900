## ASP.NET to Django Conversion Script: Proforma Invoice Module

This document outlines a modernization plan to transition the existing ASP.NET Proforma Invoice Details page to a modern Django-based application. Our approach prioritizes automation, leveraging AI-assisted tools for code generation and adhering to Django 5.0+ best practices. The goal is to deliver a robust, maintainable, and scalable solution using a "Fat Model, Thin View" architecture with HTMX and Alpine.js for a highly interactive user experience.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include `base.html` template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

### Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the ASP.NET code.

**Instructions:**
From the ASP.NET code, we've identified the core tables for this module, inferring their structure based on SQL queries and UI bindings. We will establish Django models for these tables, setting `managed = False` as per the re-platforming strategy to connect to the existing database.

**Inferred Table Schemas:**

**1. `tblACC_ProformaInvoice_Master` (Django Model: `ProformaInvoice`)**
   - Stores the main details of a Proforma Invoice.
   - **Columns:**
     - `Id` (INT, PK)
     - `SysDate` (DATETIME)
     - `SysTime` (TIME)
     - `CompId` (INT) - Company ID
     - `FinYearId` (INT) - Financial Year ID
     - `SessionId` (VARCHAR) - User/Session Identifier
     - `InvoiceNo` (VARCHAR)
     - `PONo` (VARCHAR) - Purchase Order Number
     - `WONo` (VARCHAR) - Work Order Number
     - `InvoiceMode` (INT) - Reference to `tblACC_InvoiceAgainst`
     - `DateOfIssueInvoice` (DATE)
     - `CustomerCode` (VARCHAR) - Reference to `SD_Cust_master`
     - `Buyer_name` (VARCHAR)
     - `Buyer_add` (VARCHAR)
     - `Buyer_country` (INT) - Reference to Country table
     - `Buyer_state` (INT) - Reference to State table
     - `Buyer_city` (INT) - Reference to City table
     - `Buyer_cotper` (VARCHAR) - Contact Person
     - `Buyer_ph` (VARCHAR) - Phone
     - `Buyer_email` (VARCHAR)
     - `Buyer_ecc` (VARCHAR) - ECC No.
     - `Buyer_tin` (VARCHAR) - TIN/CST No.
     - `Buyer_mob` (VARCHAR) - Mobile
     - `Buyer_fax` (VARCHAR)
     - `Buyer_vat` (VARCHAR) - TIN/VAT No.
     - `Cong_name` (VARCHAR) - Consignee Name
     - `Cong_add` (VARCHAR) - Consignee Address
     - `Cong_Country` (INT) - Consignee Country
     - `Cong_state` (INT) - Consignee State
     - `Cong_city` (INT) - Consignee City
     - `Cong_cotper` (VARCHAR) - Consignee Contact Person
     - `Cong_ph` (VARCHAR) - Consignee Phone
     - `Cong_email` (VARCHAR)
     - `Cong_ecc` (VARCHAR) - Consignee ECC No.
     - `Cong_tin` (VARCHAR) - Consignee TIN/CST No.
     - `Cong_mob` (VARCHAR) - Consignee Mobile
     - `Cong_fax` (VARCHAR)
     - `Cong_vat` (VARCHAR) - Consignee TIN/VAT No.
     - `AddType` (INT) - Addition Type (0=Amount, 1=Percentage)
     - `AddAmt` (DECIMAL) - Addition Amount
     - `DeductionType` (INT) - Deduction Type (0=Amount, 1=Percentage)
     - `Deduction` (DECIMAL) - Deduction Amount
     - `POId` (INT) - Reference to `SD_Cust_PO_Master`

**2. `tblACC_ProformaInvoice_Details` (Django Model: `ProformaInvoiceDetail`)**
   - Stores the line items for each Proforma Invoice.
   - **Columns:**
     - `Id` (INT, PK)
     - `InvoiceNo` (VARCHAR) - Redundant foreign key, kept for `managed=False`
     - `MId` (INT) - Foreign key to `tblACC_ProformaInvoice_Master`
     - `ItemId` (INT) - Foreign key to `SD_Cust_PO_Details` item
     - `Unit` (INT) - Foreign key to `Unit_Master`
     - `Qty` (DECIMAL) - Total quantity from original PO
     - `ReqQty` (DECIMAL) - Requested quantity for this invoice
     - `AmtInPer` (DECIMAL) - Amount in percentage (for this line item)
     - `Rate` (DECIMAL)

**3. `SD_Cust_master` (Django Model: `Customer`)**
   - Stores customer information, used for Buyer and Consignee details.
   - **Columns (inferred):**
     - `CustomerId` (VARCHAR, PK)
     - `CustomerName` (VARCHAR)
     - `MaterialDelAddress` (VARCHAR)
     - `MaterialDelCountry` (INT)
     - `MaterialDelState` (INT)
     - `MaterialDelCity` (INT)
     - `MaterialDelContactNo` (VARCHAR)
     - `MaterialDelFaxNo` (VARCHAR)
     - `ContactPerson` (VARCHAR)
     - `Email` (VARCHAR)
     - `TinVatNo` (VARCHAR)
     - `EccNo` (VARCHAR)
     - `ContactNo` (VARCHAR)
     - `TinCstNo` (VARCHAR)

**4. `Unit_Master` (Django Model: `Unit`)**
   - Stores measurement unit details.
   - **Columns (inferred):**
     - `Id` (INT, PK)
     - `Symbol` (VARCHAR)

**5. `tblACC_InvoiceAgainst` (Django Model: `InvoiceAgainstMode`)**
   - Stores modes of invoice against.
   - **Columns (inferred):**
     - `Id` (INT, PK)
     - `Against` (VARCHAR)

**6. `SD_Cust_PO_Master` (Django Model: `PurchaseOrder`)**
   - Stores Purchase Order header details.
   - **Columns (inferred):**
     - `POId` (INT, PK)
     - `PONo` (VARCHAR)
     - `CompId` (INT)

**7. `SD_Cust_PO_Details` (Django Model: `PurchaseOrderDetail`)**
   - Stores Purchase Order line item details.
   - **Columns (inferred):**
     - `Id` (INT, PK)
     - `POId` (INT) - FK to `SD_Cust_PO_Master`
     - `ItemDesc` (VARCHAR)
     - `TotalQty` (DECIMAL)
     - `Unit` (INT) - FK to `Unit_Master`
     - `Rate` (DECIMAL)

**8. `SD_Cust_WorkOrder_Master` (Django Model: `WorkOrder`)**
   - Stores Work Order details.
   - **Columns (inferred):**
     - `Id` (INT, PK)
     - `WONo` (VARCHAR)
     - `CompId` (INT)

**9. Implied Location Models:** `Country`, `State`, `City` (these are common lookup tables and would likely be generic across the ERP). We will assume simplified placeholder models for them.

### Step 2: Identify Backend Functionality

**Task:** Determine the core business logic and data operations from the ASP.NET code-behind.

**Instructions:**
The ASP.NET code primarily focuses on creating a new Proforma Invoice. This involves data retrieval for initial form population, dynamic updates of dependent fields, quantity validation for line items, and a multi-step submission process.

**Key Functionality:**

*   **Initialization & Data Loading:**
    *   Retrieving `Company ID`, `Financial Year ID`, and `User ID` from session.
    *   Parsing and decrypting input parameters (`Work Order No`, `PO No`, `PO ID`, `Customer ID`, `Type`, `Date`) from query strings.
    *   Displaying current date as Invoice Date, and the parsed PO Date.
    *   Fetching and displaying `Work Order No(s)` by joining multiple IDs.
    *   Fetching and displaying `Invoice Mode` (e.g., "Against") based on its ID.
    *   Generating the next sequential `Invoice No` based on existing records or defaulting to "0001".
    *   Pre-filling Buyer details by fetching customer information from `SD_Cust_master` using the `Customer ID` from the query string.
    *   Loading Purchase Order (PO) line items from `SD_Cust_PO_Master` and `SD_Cust_PO_Details`, and calculating `Remaining Quantity` by subtracting previously invoiced quantities (`ReqQty`) from `tblACC_ProformaInvoice_Details`.
*   **User Interface Interactions (HTMX/Alpine.js driven):**
    *   **Tab Navigation:** Switching between Buyer, Consignee, Goods, and Taxation sections.
    *   **Cascading Dropdowns:** Dynamically updating State and City dropdowns based on Country/State selection.
    *   **Customer Autocomplete Search:** Providing suggestions for Buyer/Consignee names and populating related address/contact fields upon selection or explicit search.
    *   **Copy Buyer to Consignee:** A utility to duplicate Buyer information to Consignee fields.
    *   **Conditional Validation:** Toggling validation rules for Goods line items based on whether they are selected (checkbox checked).
*   **Data Submission (Create Operation):**
    *   **Comprehensive Validation:** Before saving, the system performs rigorous checks on all input fields:
        *   Required field validation.
        *   Date format, email format, and numeric input validation.
        *   **Critical Business Rule:** For each selected goods item, validating that the "Requested Quantity" (`TxtReqQty`) does not exceed the "Remaining Quantity" (`lblRemnQty`) available from the Purchase Order.
    *   **Transaction Management:** If all validations pass, a new header record is inserted into `tblACC_ProformaInvoice_Master`. The generated master ID is then used to link multiple detail records into `tblACC_ProformaInvoice_Details` for each selected goods item.
    *   **Success/Error Feedback:** Notifies the user of successful submission or any validation failures.
    *   **Redirection:** Upon successful creation, the user is redirected to a list view of Proforma Invoices.

**No explicit Update or Delete operations are present on this specific ASP.NET page.** The focus is solely on the creation of a new Proforma Invoice and its associated line items.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles to map them to modern Django templates with HTMX/Alpine.js.

**Instructions:**
The ASP.NET page features a multi-tabbed form for data entry. We will translate this into a single Django form split into partials that are dynamically loaded via HTMX for each tab. Input fields will use standard Django form widgets styled with Tailwind CSS. Data display will utilize DataTables for lists.

**UI Component Mapping:**

*   **Page Header:** `<b>Proforma Invoice - New</b>` becomes a standard `h2` heading in the Django template.
*   **Invoice Header Details:**
    *   `TxtInvNo`, `LblInvDate`, `LblPONo`, `LblPODate`, `LblWONo`, `LblMode`: These will be displayed as read-only fields or pre-filled inputs in the Django form.
    *   `TxtDateofIssueInvoice` (with `CalendarExtender`): A Django `DateField` with a suitable date picker (e.g., Flatpickr, integrated via Alpine.js).
*   **Tabbed Interface (`TabContainer1`):**
    *   Mapped to distinct `div` elements, each loaded via HTMX when its corresponding tab button is clicked. Alpine.js will manage the active tab state and modal visibility.
*   **Buyer and Consignee Information (Tabs 1 & 2):**
    *   `TxtBYName`, `TxtCName` (with `AutoCompleteExtender`): Django `TextInput` fields. Autocomplete functionality will be handled by HTMX sending requests to a dedicated Django view for customer search.
    *   `TxtByAddress`, `TxtCAddress`: Django `Textarea` for multi-line input.
    *   `DrpByCountry`, `DrpByState`, `DrpByCity`, `DrpCoCountry`, `DrpCoState`, `DrpCoCity`: Django `Select` widgets. Cascading behavior will be implemented using HTMX (`hx-get` or `hx-post` on `change` events) to update dependent dropdowns.
    *   All other `TextBox` controls (`TxtByCName`, `TxtByPhone`, `TxtByEmail`, etc.): Standard Django `TextInput` fields.
    *   `Button5`, `Button4` (Search), `Button6` (Copy from buyer): Buttons triggering HTMX requests to fetch and populate data.
*   **Goods Details (Tab 3 - `GridView1`):**
    *   This `GridView` will be transformed into a dynamic HTML table.
    *   It will be populated by a Django view returning an HTMX fragment.
    *   **DataTables.js:** Will be applied to this table for client-side search, sort, and pagination.
    *   Each row will represent a `ProformaInvoiceDetail` item, potentially managed by a Django Formset.
    *   `ck` (CheckBox): Standard HTML checkbox. When checked, it enables validation and input for `Req Qty` and `Amt`. This dynamic validation will be handled by Alpine.js and Django form validation.
    *   `lblDesc`, `lblUnit`, `lblQty`, `lblRemnQty`, `lblRate`: Displayed as read-only text.
    *   `DrpUnitQty`: Django `Select` widget, populated from the `Unit` model.
    *   `TxtReqQty`, `TxtAmt`: Django `NumberInput` fields.
*   **Taxation/Others (Tab 4):**
    *   `TxtAdd`, `TxtDeduct`: Django `NumberInput` fields.
    *   `DrpAdd`, `DrpDed`: Django `Select` widgets for type (Amount/Percentage).
*   **Action Buttons:**
    *   `BtnBuy`, `Btngoods`, `BtnCNext`: Will be simple buttons with `hx-get` attributes to load the next tab's content.
    *   `BtnSubmit`: Main form submission button, triggering an `hx-post` to the Django view.
    *   `BtnCancel`, `Button1`, `Button3`: Buttons to redirect, handled by `hx-redirect` or simple `a` tags.

### Step 4: Generate Django Code

We will structure the Django application within an assumed `invoicing` app.

#### 4.1 Models (`invoicing/models.py`)

```python
from django.db import models
from django.utils import timezone
from decimal import Decimal

# Option types for AddType and DeductionType
AMOUNT_PERCENTAGE_CHOICES = (
    (0, 'Amt(Rs)'),
    (1, 'Per(%)'),
)

# Placeholder Models for related lookups
# These models would ideally be part of a 'master_data' or 'crm' app
# For this migration, we define them minimally to support foreign keys and lookups.

class Company(models.Model):
    # Inferred from CompId
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='CompanyName', max_length=255) # Placeholder
    
    class Meta:
        managed = False
        db_table = 'tblCompany_Master'
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name

class FinancialYear(models.Model):
    # Inferred from FinYearId
    id = models.IntegerField(db_column='Id', primary_key=True)
    year_name = models.CharField(db_column='YearName', max_length=50) # Placeholder
    
    class Meta:
        managed = False
        db_table = 'tblFinancialYear_Master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.year_name

class InvoiceAgainstMode(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    against = models.CharField(db_column='Against', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblACC_InvoiceAgainst'
        verbose_name = 'Invoice Against Mode'
        verbose_name_plural = 'Invoice Against Modes'

    def __str__(self):
        return self.against

class Customer(models.Model):
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    material_del_address = models.TextField(db_column='MaterialDelAddress', blank=True, null=True)
    material_del_country = models.IntegerField(db_column='MaterialDelCountry', blank=True, null=True) # Assumed FK to Country
    material_del_state = models.IntegerField(db_column='MaterialDelState', blank=True, null=True) # Assumed FK to State
    material_del_city = models.IntegerField(db_column='MaterialDelCity', blank=True, null=True) # Assumed FK to City
    material_del_contact_no = models.CharField(db_column='MaterialDelContactNo', max_length=50, blank=True, null=True)
    material_del_fax_no = models.CharField(db_column='MaterialDelFaxNo', max_length=50, blank=True, null=True)
    contact_person = models.CharField(db_column='ContactPerson', max_length=100, blank=True, null=True)
    email = models.CharField(db_column='Email', max_length=100, blank=True, null=True)
    tin_vat_no = models.CharField(db_column='TinVatNo', max_length=50, blank=True, null=True)
    ecc_no = models.CharField(db_column='EccNo', max_length=50, blank=True, null=True)
    contact_no = models.CharField(db_column='ContactNo', max_length=50, blank=True, null=True) # Duplicated from MaterialDelContactNo?
    tin_cst_no = models.CharField(db_column='TinCstNo', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customer_name} [{self.customer_id}]"

    @classmethod
    def get_customer_by_code(cls, customer_code: str, company_id: int):
        """
        Retrieves customer details by customer_id and company_id.
        This mirrors `fun.select("SD_Cust_master", "CustomerId='...' And CompId='...'")`
        """
        try:
            return cls.objects.get(customer_id=customer_code, company_id=company_id)
        except cls.DoesNotExist:
            return None

class Unit(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class PurchaseOrder(models.Model):
    po_id = models.IntegerField(db_column='POId', primary_key=True)
    po_no = models.CharField(db_column='PONo', max_length=100)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='purchase_orders')

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Master'
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'

    def __str__(self):
        return self.po_no

class PurchaseOrderDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    po = models.ForeignKey(PurchaseOrder, on_delete=models.DO_NOTHING, db_column='POId', related_name='details')
    item_desc = models.CharField(db_column='ItemDesc', max_length=255)
    total_qty = models.DecimalField(db_column='TotalQty', max_digits=18, decimal_places=3)
    unit = models.ForeignKey(Unit, on_delete=models.DO_NOTHING, db_column='Unit')
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=2)

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Details'
        verbose_name = 'Purchase Order Detail'
        verbose_name_plural = 'Purchase Order Details'

    def __str__(self):
        return self.item_desc

    def get_invoiced_quantity(self, company_id):
        """
        Calculates the sum of requested quantities for this item across all Proforma Invoices.
        Mirrors `fun.select("Sum(tblACC_ProformaInvoice_Details.ReqQty)")`
        """
        from .models import ProformaInvoiceDetail, ProformaInvoice # Import locally to avoid circular dependency

        total_invoiced_qty = ProformaInvoiceDetail.objects.filter(
            item_id=self.id,
            master__company_id=company_id # Assuming master links back to ProformaInvoice which has company_id
        ).aggregate(total_req_qty=models.Sum('requested_qty'))['total_req_qty']
        return total_invoiced_qty or Decimal('0.000')

    def get_remaining_quantity(self, company_id):
        """Calculates remaining quantity for the item."""
        invoiced_qty = self.get_invoiced_quantity(company_id)
        return self.total_qty - invoiced_qty

class WorkOrder(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    wo_no = models.CharField(db_column='WONo', max_length=100)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.wo_no

# Placeholder for location models (Country, State, City)
class Country(models.Model):
    id = models.IntegerField(db_column='CId', primary_key=True)
    name = models.CharField(db_column='CountryName', max_length=100)

    class Meta:
        managed = False
        db_table = 'Country_Master' # Example table name
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.name

class State(models.Model):
    id = models.IntegerField(db_column='SId', primary_key=True)
    name = models.CharField(db_column='StateName', max_length=100)
    country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='CId')

    class Meta:
        managed = False
        db_table = 'State_Master' # Example table name
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.name

class City(models.Model):
    id = models.IntegerField(db_column='CityId', primary_key=True)
    name = models.CharField(db_column='CityName', max_length=100)
    state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='SId')

    class Meta:
        managed = False
        db_table = 'City_Master' # Example table name
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.name


# Main Models for Proforma Invoice
class ProformaInvoice(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate', default=timezone.now)
    sys_time = models.TimeField(db_column='SysTime', default=timezone.now)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId')
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId')
    session_id = models.CharField(db_column='SessionId', max_length=255) # Assuming this is a user ID string
    invoice_no = models.CharField(db_column='InvoiceNo', max_length=50, unique=True)
    po_no = models.CharField(db_column='PONo', max_length=100)
    work_order_no = models.CharField(db_column='WONo', max_length=255) # Comma-separated list of WO numbers
    invoice_mode = models.ForeignKey(InvoiceAgainstMode, on_delete=models.DO_NOTHING, db_column='InvoiceMode')
    issue_date = models.DateField(db_column='DateOfIssueInvoice')
    customer_code = models.CharField(db_column='CustomerCode', max_length=50) # FK to Customer, but kept as char per DB schema
    
    # Buyer Details
    buyer_name = models.CharField(db_column='Buyer_name', max_length=255)
    buyer_address = models.TextField(db_column='Buyer_add')
    buyer_country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='Buyer_country', related_name='buyer_invoices')
    buyer_state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='Buyer_state', related_name='buyer_invoices')
    buyer_city = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='Buyer_city', related_name='buyer_invoices')
    buyer_contact_person = models.CharField(db_column='Buyer_cotper', max_length=100)
    buyer_phone = models.CharField(db_column='Buyer_ph', max_length=50)
    buyer_email = models.CharField(db_column='Buyer_email', max_length=100)
    buyer_ecc_no = models.CharField(db_column='Buyer_ecc', max_length=50)
    buyer_tin_cst_no = models.CharField(db_column='Buyer_tin', max_length=50)
    buyer_mobile = models.CharField(db_column='Buyer_mob', max_length=50)
    buyer_fax = models.CharField(db_column='Buyer_fax', max_length=50)
    buyer_tin_vat_no = models.CharField(db_column='Buyer_vat', max_length=50)

    # Consignee Details
    consignee_name = models.CharField(db_column='Cong_name', max_length=255)
    consignee_address = models.TextField(db_column='Cong_add')
    consignee_country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='Cong_Country', related_name='consignee_invoices')
    consignee_state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='Cong_state', related_name='consignee_invoices')
    consignee_city = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='Cong_city', related_name='consignee_invoices')
    consignee_contact_person = models.CharField(db_column='Cong_cotper', max_length=100)
    consignee_phone = models.CharField(db_column='Cong_ph', max_length=50)
    consignee_email = models.CharField(db_column='Cong_email', max_length=100)
    consignee_ecc_no = models.CharField(db_column='Cong_ecc', max_length=50)
    consignee_tin_cst_no = models.CharField(db_column='Cong_tin', max_length=50)
    consignee_mobile = models.CharField(db_column='Cong_mob', max_length=50)
    consignee_fax = models.CharField(db_column='Cong_fax', max_length=50)
    consignee_tin_vat_no = models.CharField(db_column='Cong_vat', max_length=50)
    
    # Taxation/Other Details
    add_type = models.IntegerField(db_column='AddType', choices=AMOUNT_PERCENTAGE_CHOICES)
    add_amount = models.DecimalField(db_column='AddAmt', max_digits=18, decimal_places=3)
    deduction_type = models.IntegerField(db_column='DeductionType', choices=AMOUNT_PERCENTAGE_CHOICES)
    deduction_amount = models.DecimalField(db_column='Deduction', max_digits=18, decimal_places=3)
    
    po_id = models.IntegerField(db_column='POId') # Storing POId here for context, not a direct FK to PurchaseOrder.po_id

    class Meta:
        managed = False
        db_table = 'tblACC_ProformaInvoice_Master'
        verbose_name = 'Proforma Invoice'
        verbose_name_plural = 'Proforma Invoices'

    def __str__(self):
        return self.invoice_no

    @classmethod
    def get_next_invoice_no(cls, company_id: int, financial_year_id: int) -> str:
        """
        Generates the next sequential Invoice Number.
        Mirrors ASP.NET's `TxtInvNo` generation logic.
        """
        try:
            last_invoice = cls.objects.filter(
                company_id=company_id,
                financial_year_id=financial_year_id
            ).order_by('-invoice_no').first() # Assumes invoice_no is numeric or can be sorted lexicographically with leading zeros
            if last_invoice and last_invoice.invoice_no.isdigit():
                next_number = int(last_invoice.invoice_no) + 1
            else:
                next_number = 1
        except Exception: # Handle cases where invoice_no might not be purely numeric
             next_number = 1
        return f"{next_number:04d}" # Format to 4 digits with leading zeros

    def update_from_customer(self, customer: Customer, is_buyer: bool = True):
        """
        Populates buyer/consignee fields from a Customer object.
        This consolidates logic from Button5_Click, Button4_Click, Button6_Click.
        """
        prefix = 'buyer' if is_buyer else 'consignee'
        
        setattr(self, f'{prefix}_name', customer.customer_name)
        setattr(self, f'{prefix}_address', customer.material_del_address)
        
        # Handle country, state, city relationships
        # Assuming Country, State, City models exist and have IDs matching the MaterialDelCountry/State/City
        setattr(self, f'{prefix}_country_id', customer.material_del_country)
        setattr(self, f'{prefix}_state_id', customer.material_del_state)
        setattr(self, f'{prefix}_city_id', customer.material_del_city)

        setattr(self, f'{prefix}_contact_person', customer.contact_person)
        setattr(self, f'{prefix}_phone', customer.material_del_contact_no)
        setattr(self, f'{prefix}_email', customer.email)
        setattr(self, f'{prefix}_fax', customer.material_del_fax_no)
        setattr(self, f'{prefix}_tin_vat_no', customer.tin_vat_no)
        setattr(self, f'{prefix}_ecc_no', customer.ecc_no)
        setattr(self, f'{prefix}_mobile', customer.contact_no)
        setattr(self, f'{prefix}_tin_cst_no', customer.tin_cst_no)

class ProformaInvoiceDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    invoice_no = models.CharField(db_column='InvoiceNo', max_length=50) # Redundant, but exists in schema.
    master = models.ForeignKey(ProformaInvoice, on_delete=models.CASCADE, db_column='MId', related_name='details')
    item = models.ForeignKey(PurchaseOrderDetail, on_delete=models.DO_NOTHING, db_column='ItemId')
    unit = models.ForeignKey(Unit, on_delete=models.DO_NOTHING, db_column='Unit')
    total_qty_from_po = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3) # TotalQty from PO_Details
    requested_qty = models.DecimalField(db_column='ReqQty', max_digits=18, decimal_places=3)
    amount_in_percentage = models.DecimalField(db_column='AmtInPer', max_digits=18, decimal_places=2)
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=2)

    class Meta:
        managed = False
        db_table = 'tblACC_ProformaInvoice_Details'
        verbose_name = 'Proforma Invoice Detail'
        verbose_name_plural = 'Proforma Invoice Details'

    def __str__(self):
        return f"Detail for {self.master.invoice_no}: {self.item.item_desc}"

    def get_remaining_qty_for_item(self):
        """
        Calculates remaining quantity for this specific item within the PO.
        This method would typically be called on PurchaseOrderDetail, but if we need
        to validate *against* previous ProformaInvoiceDetails.
        """
        # This logic is complex and needs to be handled at the form/view level
        # by checking PurchaseOrderDetail.get_remaining_quantity
        pass

```

#### 4.2 Forms (`invoicing/forms.py`)

```python
from django import forms
from django.forms import inlineformset_factory
from .models import (
    ProformaInvoice, ProformaInvoiceDetail,
    Country, State, City,
    InvoiceAgainstMode, Unit, Customer,
    PurchaseOrderDetail
)
import re
from decimal import Decimal, InvalidOperation

# Custom widgets for Tailwind CSS styling
class TailwindTextInput(forms.TextInput):
    def __init__(self, *args, **kwargs):
        kwargs.setdefault('attrs', {}).update({'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
        super().__init__(*args, **kwargs)

class TailwindTextarea(forms.Textarea):
    def __init__(self, *args, **kwargs):
        kwargs.setdefault('attrs', {}).update({'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
        super().__init__(*args, **kwargs)

class TailwindSelect(forms.Select):
    def __init__(self, *args, **kwargs):
        kwargs.setdefault('attrs', {}).update({'class': 'mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md'})
        super().__init__(*args, **kwargs)

class TailwindNumberInput(forms.NumberInput):
    def __init__(self, *args, **kwargs):
        kwargs.setdefault('attrs', {}).update({'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
        super().__init__(*args, **kwargs)

class ProformaInvoiceForm(forms.ModelForm):
    # Header Fields (often read-only or pre-filled)
    invoice_no = forms.CharField(widget=TailwindTextInput(attrs={'readonly': 'readonly'}), required=False)
    po_no = forms.CharField(widget=TailwindTextInput(attrs={'readonly': 'readonly'}), required=False)
    work_order_no = forms.CharField(widget=TailwindTextInput(attrs={'readonly': 'readonly'}), required=False)
    invoice_mode = forms.ModelChoiceField(queryset=InvoiceAgainstMode.objects.all(), widget=TailwindSelect(attrs={'readonly': 'readonly'}), required=False)
    issue_date = forms.DateField(
        widget=TailwindTextInput(attrs={'type': 'date', 'placeholder': 'DD-MM-YYYY', 'x-init': "new Pikaday({ field: $el, format: 'DD-MM-YYYY' })"}),
        input_formats=['%d-%m-%Y'],
        error_messages={'invalid': 'Enter a valid date in DD-MM-YYYY format.'}
    )

    # Buyer Fields
    buyer_name = forms.CharField(
        widget=TailwindTextInput(attrs={'hx-get': '/invoicing/customer-autocomplete/', 'hx-trigger': 'keyup changed delay:500ms', 'hx-target': '#buyer-autocomplete-results', 'autocomplete': 'off'}),
        help_text='Start typing customer name for suggestions.'
    )
    buyer_address = forms.CharField(widget=TailwindTextarea(attrs={'rows': 4}))
    buyer_country = forms.ModelChoiceField(
        queryset=Country.objects.all(),
        widget=TailwindSelect(attrs={'hx-get': '/invoicing/get-states/', 'hx-target': '#id_buyer_state', 'hx-indicator': '.htmx-indicator', 'hx-include': 'this'}),
        empty_label="Select Country"
    )
    buyer_state = forms.ModelChoiceField(
        queryset=State.objects.none(),
        widget=TailwindSelect(attrs={'hx-get': '/invoicing/get-cities/', 'hx-target': '#id_buyer_city', 'hx-indicator': '.htmx-indicator', 'hx-include': 'this'}),
        empty_label="Select State"
    )
    buyer_city = forms.ModelChoiceField(
        queryset=City.objects.none(),
        widget=TailwindSelect(),
        empty_label="Select City"
    )
    buyer_contact_person = forms.CharField(widget=TailwindTextInput())
    buyer_phone = forms.CharField(widget=TailwindTextInput())
    buyer_mobile = forms.CharField(widget=TailwindTextInput())
    buyer_email = forms.EmailField(widget=TailwindTextInput())
    buyer_fax = forms.CharField(widget=TailwindTextInput(), required=False)
    buyer_tin_vat_no = forms.CharField(widget=TailwindTextInput())
    buyer_ecc_no = forms.CharField(widget=TailwindTextInput())
    buyer_tin_cst_no = forms.CharField(widget=TailwindTextInput())

    # Consignee Fields (similar to Buyer)
    consignee_name = forms.CharField(
        widget=TailwindTextInput(attrs={'hx-get': '/invoicing/customer-autocomplete/', 'hx-trigger': 'keyup changed delay:500ms', 'hx-target': '#consignee-autocomplete-results', 'autocomplete': 'off'}),
        help_text='Start typing customer name for suggestions.'
    )
    consignee_address = forms.CharField(widget=TailwindTextarea(attrs={'rows': 4}))
    consignee_country = forms.ModelChoiceField(
        queryset=Country.objects.all(),
        widget=TailwindSelect(attrs={'hx-get': '/invoicing/get-states/', 'hx-target': '#id_consignee_state', 'hx-indicator': '.htmx-indicator', 'hx-include': 'this'}),
        empty_label="Select Country"
    )
    consignee_state = forms.ModelChoiceField(
        queryset=State.objects.none(),
        widget=TailwindSelect(attrs={'hx-get': '/invoicing/get-cities/', 'hx-target': '#id_consignee_city', 'hx-indicator': '.htmx-indicator', 'hx-include': 'this'}),
        empty_label="Select State"
    )
    consignee_city = forms.ModelChoiceField(
        queryset=City.objects.none(),
        widget=TailwindSelect(),
        empty_label="Select City"
    )
    consignee_contact_person = forms.CharField(widget=TailwindTextInput())
    consignee_phone = forms.CharField(widget=TailwindTextInput())
    consignee_mobile = forms.CharField(widget=TailwindTextInput())
    consignee_email = forms.EmailField(widget=TailwindTextInput())
    consignee_fax = forms.CharField(widget=TailwindTextInput(), required=False)
    consignee_tin_vat_no = forms.CharField(widget=TailwindTextInput())
    consignee_ecc_no = forms.CharField(widget=TailwindTextInput())
    consignee_tin_cst_no = forms.CharField(widget=TailwindTextInput())

    # Taxation Fields
    add_type = forms.ChoiceField(choices=ProformaInvoice.AMOUNT_PERCENTAGE_CHOICES, widget=TailwindSelect())
    add_amount = forms.DecimalField(max_digits=18, decimal_places=3, widget=TailwindNumberInput())
    deduction_type = forms.ChoiceField(choices=ProformaInvoice.AMOUNT_PERCENTAGE_CHOICES, widget=TailwindSelect())
    deduction_amount = forms.DecimalField(max_digits=18, decimal_places=3, widget=TailwindNumberInput())

    class Meta:
        model = ProformaInvoice
        fields = [
            'invoice_no', 'po_no', 'work_order_no', 'invoice_mode', 'issue_date', 'customer_code',
            'buyer_name', 'buyer_address', 'buyer_country', 'buyer_state', 'buyer_city',
            'buyer_contact_person', 'buyer_phone', 'buyer_mobile', 'buyer_email', 'buyer_fax',
            'buyer_tin_vat_no', 'buyer_ecc_no', 'buyer_tin_cst_no',
            'consignee_name', 'consignee_address', 'consignee_country', 'consignee_state', 'consignee_city',
            'consignee_contact_person', 'consignee_phone', 'consignee_mobile', 'consignee_email', 'consignee_fax',
            'consignee_tin_vat_no', 'consignee_ecc_no', 'consignee_tin_cst_no',
            'add_type', 'add_amount', 'deduction_type', 'deduction_amount', 'po_id' # po_id needs to be passed to model
        ]
        # Exclude managed=False fields that are automatically populated by the DB or are read-only and derived
        # 'sys_date', 'sys_time', 'company', 'financial_year', 'session_id' are handled by the view/model.

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Initial population of State/City dropdowns if instance exists
        if self.instance and self.instance.pk:
            if self.instance.buyer_country_id:
                self.fields['buyer_state'].queryset = State.objects.filter(country_id=self.instance.buyer_country_id)
            if self.instance.buyer_state_id:
                self.fields['buyer_city'].queryset = City.objects.filter(state_id=self.instance.buyer_state_id)
            if self.instance.consignee_country_id:
                self.fields['consignee_state'].queryset = State.objects.filter(country_id=self.instance.consignee_country_id)
            if self.instance.consignee_state_id:
                self.fields['consignee_city'].queryset = City.objects.filter(state_id=self.instance.consignee_state_id)
        
        # Mark certain fields as required (matching ASP.NET RequiredFieldValidator)
        required_fields = [
            'issue_date', 'buyer_name', 'buyer_address', 'buyer_country', 'buyer_state', 'buyer_city',
            'buyer_contact_person', 'buyer_phone', 'buyer_mobile', 'buyer_email', 'buyer_tin_vat_no',
            'buyer_ecc_no', 'buyer_tin_cst_no',
            'consignee_name', 'consignee_address', 'consignee_country', 'consignee_state', 'consignee_city',
            'consignee_contact_person', 'consignee_phone', 'consignee_mobile', 'consignee_email', 'consignee_tin_vat_no',
            'consignee_ecc_no', 'consignee_tin_cst_no',
            'add_amount', 'deduction_amount'
        ]
        for field_name in required_fields:
            self.fields[field_name].required = True
            
        # Add dynamic attributes for HTMX customer search buttons
        self.fields['buyer_name'].widget.attrs.update({
            'hx-post': '/invoicing/customer-search-and-populate/', 
            'hx-trigger': 'change', 
            'hx-target': '#buyer-details-form-fields', 
            'hx-swap': 'outerHTML',
            'name': 'buyer_name_input' # Use a different name to avoid conflict with model field for search input
        })
        self.fields['consignee_name'].widget.attrs.update({
            'hx-post': '/invoicing/customer-search-and-populate/', 
            'hx-trigger': 'change', 
            'hx-target': '#consignee-details-form-fields', 
            'hx-swap': 'outerHTML',
            'name': 'consignee_name_input'
        })


    def clean(self):
        cleaned_data = super().clean()
        # Add any cross-field validation if needed, e.g., date ranges
        return cleaned_data

class ProformaInvoiceDetailForm(forms.ModelForm):
    # Fields to display to the user and allow editing
    is_selected = forms.BooleanField(required=False, initial=False, 
                                     widget=forms.CheckboxInput(attrs={'class': 'form-checkbox h-4 w-4 text-indigo-600 border-gray-300 rounded'}))
    
    # Read-only fields from PurchaseOrderDetail
    item_desc = forms.CharField(required=False, widget=TailwindTextInput(attrs={'readonly': 'readonly'}))
    unit_symbol = forms.CharField(required=False, widget=TailwindTextInput(attrs={'readonly': 'readonly'}))
    total_qty_po = forms.DecimalField(required=False, max_digits=18, decimal_places=3, widget=TailwindNumberInput(attrs={'readonly': 'readonly'}))
    rate_po = forms.DecimalField(required=False, max_digits=18, decimal_places=2, widget=TailwindNumberInput(attrs={'readonly': 'readonly'}))
    
    # Remaining Quantity (calculated and displayed as read-only)
    remaining_qty_display = forms.DecimalField(required=False, max_digits=18, decimal_places=3, widget=TailwindNumberInput(attrs={'readonly': 'readonly'}))

    # Editable fields for ProformaInvoiceDetail
    requested_qty = forms.DecimalField(
        max_digits=18, decimal_places=3, required=False, widget=TailwindNumberInput()
    )
    amount_in_percentage = forms.DecimalField(
        max_digits=18, decimal_places=2, required=False, widget=TailwindNumberInput()
    )
    # Unit for ReqQty (DrpUnitQty from ASP.NET)
    unit = forms.ModelChoiceField(queryset=Unit.objects.all(), widget=TailwindSelect())


    class Meta:
        model = ProformaInvoiceDetail
        fields = [
            'is_selected', 'item_desc', 'unit_symbol', 'total_qty_po', 'remaining_qty_display', 
            'rate_po', 'unit', 'requested_qty', 'amount_in_percentage', 'item', 'master' # item and master are hidden fields
        ]
        widgets = {
            'item': forms.HiddenInput(), # Actual ItemId from PurchaseOrderDetail
            'master': forms.HiddenInput(), # MId (ProformaInvoice.id)
            # 'unit' field widget is defined above
        }

    def __init__(self, *args, **kwargs):
        purchase_order_detail = kwargs.pop('purchase_order_detail', None)
        company_id = kwargs.pop('company_id', None)
        super().__init__(*args, **kwargs)

        # Populate read-only fields from PurchaseOrderDetail and calculate remaining_qty
        if purchase_order_detail:
            self.fields['item_desc'].initial = purchase_order_detail.item_desc
            self.fields['unit_symbol'].initial = purchase_order_detail.unit.symbol
            self.fields['total_qty_po'].initial = purchase_order_detail.total_qty
            self.fields['rate_po'].initial = purchase_order_detail.rate
            
            # Calculate remaining quantity
            if company_id:
                self.fields['remaining_qty_display'].initial = purchase_order_detail.get_remaining_quantity(company_id)
            else:
                 self.fields['remaining_qty_display'].initial = purchase_order_detail.total_qty # Fallback if company_id is not provided

            self.fields['item'].initial = purchase_order_detail.id # Store the actual PO Detail ID

        # Set initial values for editable fields if instance exists (for editing existing details)
        if self.instance and self.instance.pk:
            self.fields['is_selected'].initial = True # If an instance exists, it's considered selected
            self.fields['requested_qty'].initial = self.instance.requested_qty
            self.fields['amount_in_percentage'].initial = self.instance.amount_in_percentage
            self.fields['unit'].initial = self.instance.unit
        
        # Mark required based on selection (similar to ASP.NET's GetValidate)
        # These fields are required ONLY if `is_selected` is True.
        # This will be handled in `clean()` method.

    def clean(self):
        cleaned_data = super().clean()
        is_selected = cleaned_data.get('is_selected')
        requested_qty = cleaned_data.get('requested_qty')
        amount_in_percentage = cleaned_data.get('amount_in_percentage')
        remaining_qty = cleaned_data.get('remaining_qty_display') # This is the *displayed* remaining, should fetch fresh for validation

        if is_selected:
            if not requested_qty:
                self.add_error('requested_qty', 'Required field for selected item.')
            elif requested_qty <= 0:
                self.add_error('requested_qty', 'Quantity must be greater than zero.')
            
            if not amount_in_percentage:
                self.add_error('amount_in_percentage', 'Required field for selected item.')
            elif amount_in_percentage <= 0:
                self.add_error('amount_in_percentage', 'Amount must be greater than zero.')

            # Re-fetch remaining quantity for server-side validation
            po_detail_id = cleaned_data.get('item').id if cleaned_data.get('item') else None
            if po_detail_id and self.data.get('company_id'): # company_id passed via hidden field or request data
                try:
                    po_detail = PurchaseOrderDetail.objects.get(id=po_detail_id)
                    current_remaining_qty = po_detail.get_remaining_quantity(int(self.data.get('company_id')))
                    if requested_qty and requested_qty > current_remaining_qty:
                        self.add_error('requested_qty', f'Requested quantity ({requested_qty}) exceeds remaining quantity ({current_remaining_qty}).')
                except PurchaseOrderDetail.DoesNotExist:
                    self.add_error('item', 'Invalid purchase order item.')
            else:
                if requested_qty and remaining_qty and requested_qty > remaining_qty:
                     self.add_error('requested_qty', f'Requested quantity ({requested_qty}) exceeds remaining quantity ({remaining_qty}).')

        return cleaned_data

# Formset for handling multiple ProformaInvoiceDetail forms (the GridView)
ProformaInvoiceDetailFormSet = inlineformset_factory(
    ProformaInvoice, # Parent Model
    ProformaInvoiceDetail, # Child Model
    form=ProformaInvoiceDetailForm,
    fk_name='master', # The foreign key name in ProformaInvoiceDetail linking to ProformaInvoice
    extra=0, # No extra empty forms by default
    can_delete=False # Deletion not implemented on the original ASP.NET page
)

# Forms for Autocomplete and dynamic dropdowns (minimal for HTMX responses)
class CustomerSearchForm(forms.Form):
    query = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label="Customer Name"
    )

class LocationSelectForm(forms.Form):
    # Generic form for dynamic dropdowns
    parent_id = forms.IntegerField(required=True)
```

#### 4.3 Views (`invoicing/views.py`)

```python
from django.views.generic import View, ListView, CreateView
from django.shortcuts import render, get_object_or_404, redirect
from django.urls import reverse_lazy, reverse
from django.http import HttpResponse, JsonResponse
from django.contrib import messages
from django.db import transaction
from django.template.loader import render_to_string
from django.db.models import Sum
from datetime import datetime

from .models import (
    ProformaInvoice, ProformaInvoiceDetail,
    Company, FinancialYear, InvoiceAgainstMode,
    Customer, Unit, PurchaseOrder, PurchaseOrderDetail, WorkOrder,
    Country, State, City
)
from .forms import ProformaInvoiceForm, ProformaInvoiceDetailForm, ProformaInvoiceDetailFormSet, CustomerSearchForm, LocationSelectForm

# Helper function to get common session/query params (mimics clsFunctions)
def get_common_params(request):
    # In a real app, CompId and FinYearId would be associated with the logged-in user or request context.
    # For this migration, we'll use placeholder values or fetch from session if available.
    company_id = int(request.session.get('compid', 1)) # Default to 1 if not in session
    financial_year_id = int(request.session.get('finyear', 1)) # Default to 1 if not in session
    session_user_id = request.session.get('username', 'system') # Default to 'system'

    # Decrypt query string parameters (simplified for Django)
    # In a real setup, decryption logic would be more robust.
    wn = request.GET.get('wn', '') # Work Order No(s)
    pn = request.GET.get('pn', '') # PO No
    poid = request.GET.get('poid', None) # PO Id
    cid = request.GET.get('cid', None) # Customer Id
    typ = request.GET.get('ty', None) # Invoice Against Type (Id)
    pdate = request.GET.get('date', '') # PO Date

    return {
        'company_id': company_id,
        'financial_year_id': financial_year_id,
        'session_user_id': session_user_id,
        'work_order_ids_str': wn,
        'po_no': pn,
        'po_id': poid,
        'customer_code_param': cid,
        'invoice_mode_id': typ,
        'po_date_str': pdate,
    }

class ProformaInvoiceCreateView(View):
    template_name = 'invoicing/proforma_invoice/form.html'
    success_url = reverse_lazy('proforma_invoice_list') # Redirect after successful save

    def get_context_data(self, request, **kwargs):
        common_params = get_common_params(request)
        company_id = common_params['company_id']
        financial_year_id = common_params['financial_year_id']
        po_no = common_params['po_no']
        work_order_ids_str = common_params['work_order_ids_str']
        invoice_mode_id = common_params['invoice_mode_id']
        customer_code = common_params['customer_code_param']
        po_id = common_params['po_id']
        po_date_str = common_params['po_date_str']

        initial_data = {}
        
        # Get next Invoice No
        initial_data['invoice_no'] = ProformaInvoice.get_next_invoice_no(company_id, financial_year_id)
        
        # Set PO No and WO No (from query string)
        initial_data['po_no'] = po_no

        # Calculate WO No display string (comma separated)
        if work_order_ids_str:
            wo_ids = [int(x) for x in work_order_ids_str.strip(',').split(',') if x.isdigit()]
            work_orders = WorkOrder.objects.filter(id__in=wo_ids, company_id=company_id)
            initial_data['work_order_no'] = ','.join([wo.wo_no for wo in work_orders])
        else:
            initial_data['work_order_no'] = 'N/A' # Or empty string

        # Set Invoice Mode
        if invoice_mode_id:
            initial_data['invoice_mode'] = get_object_or_404(InvoiceAgainstMode, id=invoice_mode_id)

        # Set issue date (current date) and PO date
        initial_data['issue_date'] = datetime.now().strftime('%d-%m-%Y')
        po_date_display = po_date_str # Already DMY if from fun.Decrypt(pdate)

        # Populate Buyer details from Customer
        buyer_instance = ProformaInvoice() # Temp instance to use model method
        if customer_code:
            customer = Customer.objects.filter(customer_id=customer_code).first() # Assuming company_id check is implicit or not needed for Customer search
            if customer:
                buyer_instance.update_from_customer(customer, is_buyer=True)
                initial_data['customer_code'] = customer_code # Keep customer code for model
                # Transfer populated fields from temp instance to initial_data
                for field in ProformaInvoiceForm.Meta.fields:
                    if field.startswith('buyer_') or field == 'customer_code':
                        initial_data[field] = getattr(buyer_instance, field, '')
            else:
                messages.warning(request, "Initial customer not found.")
        
        # Fetch Purchase Order Details for the Goods tab
        # This will be passed to the formset, not the main form
        goods_data_for_formset = []
        if po_id:
            try:
                # Fetch all details for this PO and company
                po_details = PurchaseOrderDetail.objects.filter(
                    po__po_id=po_id,
                    po__company_id=company_id # Assuming PurchaseOrder links to Company
                )
                for detail in po_details:
                    # Initial data for each detail form in the formset
                    goods_data_for_formset.append({
                        'item': detail.id, # The hidden ID for the PO_Detail item
                        'item_desc': detail.item_desc,
                        'unit_symbol': detail.unit.symbol,
                        'total_qty_po': detail.total_qty,
                        'rate_po': detail.rate,
                        'remaining_qty_display': detail.get_remaining_quantity(company_id),
                        # If we have existing ProformaInvoiceDetail, populate requested_qty, amt_in_per, unit
                        # This create view assumes no existing details, so these are left empty initially
                    })
            except PurchaseOrder.DoesNotExist:
                messages.error(request, "Purchase Order not found.")
        
        context = {
            'initial_form_data': initial_data,
            'po_date_display': po_date_display,
            'goods_data_for_formset': goods_data_for_formset, # Data to build the formset
            'company_id': company_id # Needed for remaining quantity calculation in formset
        }
        return context

    def get(self, request, *args, **kwargs):
        context = self.get_context_data(request)
        
        # Initialize main form
        form = ProformaInvoiceForm(initial=context['initial_form_data'])
        
        # Initialize formset for Goods
        # We need to map initial goods_data_for_formset to actual model instances
        # Since this is a "new" invoice, we primarily use `PurchaseOrderDetail` to populate the detail lines.
        # We'll create "empty" ProformaInvoiceDetail instances (not saved) for each PO line.
        
        # Create empty instances for the formset based on goods_data_for_formset
        # This is a bit tricky with inlineformset_factory and un-saved parent.
        # A common pattern is to handle formset manually or use a helper that adapts initial data.
        
        # For simplicity, we'll pass the initial PO details and dynamically create forms in the template,
        # or pre-fill a fixed number of forms if not dynamic, or use a custom formset approach.
        # For HTMX, we'll render the goods table with these details directly.
        
        # Create an empty formset for now; actual data populated in template or partial
        goods_formset = ProformaInvoiceDetailFormSet(prefix='goods_detail')
        
        # Populate formset forms with initial data for each PO detail
        # This loop maps the PO details fetched earlier to individual ProformaInvoiceDetailForm instances.
        # This is not how standard Django formsets work with `inlineformset_factory` if the parent is not saved.
        # A more robust approach for "new" records is to use a custom formset or handle each form separately.
        
        # Let's create a list of detail forms manually for the goods tab
        detail_forms = []
        for po_detail_data in context['goods_data_for_formset']:
            # Create an instance of ProformaInvoiceDetail (not saved to DB)
            # This instance won't have a PK, so it will be treated as 'new' by the form.
            # We pass purchase_order_detail and company_id to the form's __init__
            po_detail_obj = get_object_or_404(PurchaseOrderDetail, id=po_detail_data['item'])
            detail_form = ProformaInvoiceDetailForm(
                purchase_order_detail=po_detail_obj, 
                company_id=company_id
            )
            # Manually set initial values derived from po_detail_data
            detail_form.fields['is_selected'].initial = False # Default to not selected
            detail_form.fields['requested_qty'].initial = None
            detail_form.fields['amount_in_percentage'].initial = None
            detail_form.fields['unit'].initial = po_detail_obj.unit # Default unit from PO
            detail_forms.append(detail_form)


        context.update({
            'form': form,
            'detail_forms': detail_forms, # Pass individual detail forms
            # 'goods_formset': goods_formset, # Only if using standard formset approach
        })
        return render(request, self.template_name, context)

    def post(self, request, *args, **kwargs):
        common_params = get_common_params(request)
        company_id = common_params['company_id']
        financial_year_id = common_params['financial_year_id']
        session_user_id = common_params['session_user_id']
        po_id = common_params['po_id'] # Make sure POId is passed in form/request data

        form = ProformaInvoiceForm(request.POST)
        
        # Use a temporary instance to handle related fields (company, financial_year, session_id, po_id)
        # as they are not directly on the form but needed for the model instance.
        proforma_invoice = ProformaInvoice()
        proforma_invoice.company_id = company_id
        proforma_invoice.financial_year_id = financial_year_id
        proforma_invoice.session_id = session_user_id
        proforma_invoice.po_id = int(po_id) if po_id else None # Ensure po_id from query string is captured

        # Re-initialize the detail forms/formset with POST data.
        # If using manual detail forms, iterate over submitted data for each item.
        # This part assumes formset structure with prefixes
        
        # Get count of items from POST data for detail forms
        total_forms = int(request.POST.get('goods_detail-TOTAL_FORMS', 0))
        detail_forms = []
        all_details_valid = True

        for i in range(total_forms):
            prefix = f'goods_detail-{i}'
            item_id = request.POST.get(f'{prefix}-item')
            is_selected = request.POST.get(f'{prefix}-is_selected') == 'on'
            
            # Need to create a base instance for each detail form to map to correct PO Detail
            po_detail_obj = None
            if item_id:
                try:
                    po_detail_obj = PurchaseOrderDetail.objects.get(id=item_id)
                except PurchaseOrderDetail.DoesNotExist:
                    messages.error(request, f"Error: PO Detail with ID {item_id} not found.")
                    all_details_valid = False
                    break # Exit early if critical data missing

            detail_form = ProformaInvoiceDetailForm(
                data={
                    f'{prefix}-is_selected': 'on' if is_selected else '',
                    f'{prefix}-requested_qty': request.POST.get(f'{prefix}-requested_qty'),
                    f'{prefix}-amount_in_percentage': request.POST.get(f'{prefix}-amount_in_percentage'),
                    f'{prefix}-unit': request.POST.get(f'{prefix}-unit'),
                    f'{prefix}-item': item_id,
                    f'{prefix}-master': '', # Master ID will be populated after saving ProformaInvoice
                    'company_id': company_id # Pass company_id for detail form validation
                },
                prefix=prefix,
                purchase_order_detail=po_detail_obj, # Pass original PO detail for context
                company_id=company_id # Pass company_id for the remaining quantity calculation
            )
            detail_forms.append(detail_form)
            if not detail_form.is_valid():
                all_details_valid = False


        if form.is_valid() and all_details_valid:
            try:
                with transaction.atomic():
                    # Populate common fields not directly in form fields from our temporary instance
                    for field_name in ['company_id', 'financial_year_id', 'session_id', 'po_id']:
                        setattr(form.instance, field_name, getattr(proforma_invoice, field_name))
                    
                    # Manually set invoice_no for new record, as it's typically auto-generated once.
                    form.instance.invoice_no = ProformaInvoice.get_next_invoice_no(company_id, financial_year_id)
                    
                    # Save the master Proforma Invoice
                    proforma_invoice_instance = form.save()

                    # Save selected detail forms
                    for detail_form in detail_forms:
                        if detail_form.cleaned_data.get('is_selected'):
                            # Create a new ProformaInvoiceDetail instance from cleaned_data
                            detail_instance = ProformaInvoiceDetail(
                                master=proforma_invoice_instance,
                                invoice_no=proforma_invoice_instance.invoice_no, # Redundant, but matching original schema
                                item_id=detail_form.cleaned_data['item'].id, # Use ID from PurchaseOrderDetail
                                unit=detail_form.cleaned_data['unit'],
                                total_qty_from_po=detail_form.cleaned_data['total_qty_po'], # From PO detail
                                requested_qty=detail_form.cleaned_data['requested_qty'],
                                amount_in_percentage=detail_form.cleaned_data['amount_in_percentage'],
                                rate=detail_form.cleaned_data['rate_po'] # From PO detail
                            )
                            detail_instance.save()

                messages.success(request, 'Proforma Invoice created successfully!')
                return HttpResponse(
                    status=204, # No content to swap
                    headers={
                        'HX-Redirect': reverse('proforma_invoice_list'), # Redirect on success
                        'HX-Trigger': 'refreshProformaInvoiceList' # Event to refresh list if staying on same page
                    }
                )

            except Exception as e:
                messages.error(request, f'Error creating Proforma Invoice: {e}')
                # Re-render form with errors
        
        # If form or details are not valid, re-render the form with errors
        context = self.get_context_data(request) # Get initial data
        context['form'] = form # Use submitted form with errors
        context['detail_forms'] = detail_forms # Use submitted detail forms with errors

        # Ensure correct active tab is rendered with errors
        # In multi-tab, we'd need to know which tab failed validation
        # For simplicity, we'll re-render the entire form or highlight invalid tabs.
        # A more advanced HTMX/Alpine approach would re-render only the failing tab content.

        return render(request, self.template_name, context)

# HTMX specific views for dynamic content

class CustomerAutocompleteView(View):
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        company_id = int(request.session.get('compid', 1)) # Get company_id from session
        
        if not query:
            return JsonResponse([], safe=False)
        
        # Filter customers by name and company_id, then format for autocomplete
        customers = Customer.objects.filter(
            customer_name__icontains=query,
            # Assuming Company.id maps to Customer.company_id if such a field exists
            # For now, filter on customer_id if `CompId` is not explicitly on Customer table
            # comp_id=company_id # If Customer model had a 'company_id' field
        ).values_list('customer_name', 'customer_id')[:10] # Limit suggestions

        results = [f"{name} [{cid}]" for name, cid in customers]
        return JsonResponse(results, safe=False)

class CustomerSearchAndPopulateView(View):
    def post(self, request, *args, **kwargs):
        # Determine if it's buyer or consignee based on the input field name
        is_buyer = 'buyer_name_input' in request.POST
        input_name = 'buyer_name_input' if is_buyer else 'consignee_name_input'
        target_div_id = 'buyer-details-form-fields' if is_buyer else 'consignee-details-form-fields'
        
        full_customer_string = request.POST.get(input_name, '').strip()
        customer_code = None
        if '[' in full_customer_string and ']' in full_customer_string:
            customer_code = full_customer_string.split('[')[-1].replace(']', '').strip()
        
        company_id = int(request.session.get('compid', 1))

        customer = None
        if customer_code:
            customer = Customer.objects.filter(customer_id=customer_code).first() # Add company_id filter if relevant
        
        # Create a temporary ProformaInvoice instance to populate fields
        # and then render the relevant form fields partial.
        temp_proforma = ProformaInvoice()
        if customer:
            temp_proforma.update_from_customer(customer, is_buyer=is_buyer)
            
        # Re-render the form section with updated initial data
        form = ProformaInvoiceForm(instance=temp_proforma, initial={'customer_code': customer_code if is_buyer else None})
        
        # Render the specific fields HTML
        if is_buyer:
            html = render_to_string('invoicing/proforma_invoice/_buyer_form_fields.html', {'form': form, 'is_buyer': True}, request=request)
        else:
            html = render_to_string('invoicing/proforma_invoice/_consignee_form_fields.html', {'form': form, 'is_consignee': True}, request=request)

        return HttpResponse(html)

class CopyBuyerToConsigneeView(View):
    def post(self, request, *args, **kwargs):
        # This requires the current form data for buyer fields to be sent via HTMX
        # and then populating consignee fields.
        
        # Create a form instance from the current buyer data
        buyer_form = ProformaInvoiceForm(request.POST) # Contains all form data

        temp_proforma = ProformaInvoice()
        
        # Manually transfer buyer data to consignee data in temp_proforma
        for field in ProformaInvoiceForm.Meta.fields:
            if field.startswith('buyer_'):
                consignee_field = field.replace('buyer_', 'consignee_')
                setattr(temp_proforma, consignee_field, buyer_form.data.get(field))

        # Handle dropdowns carefully for copying
        if buyer_form.data.get('buyer_country'):
            temp_proforma.consignee_country_id = int(buyer_form.data['buyer_country'])
        if buyer_form.data.get('buyer_state'):
            temp_proforma.consignee_state_id = int(buyer_form.data['buyer_state'])
        if buyer_form.data.get('buyer_city'):
            temp_proforma.consignee_city_id = int(buyer_form.data['buyer_city'])

        # Re-initialize form with copied consignee data
        form_with_copied_consignee = ProformaInvoiceForm(instance=temp_proforma)
        
        html = render_to_string('invoicing/proforma_invoice/_consignee_form_fields.html', {'form': form_with_copied_consignee, 'is_consignee': True}, request=request)
        return HttpResponse(html)

class GetStatesView(View):
    def get(self, request, *args, **kwargs):
        country_id = request.GET.get('buyer_country') or request.GET.get('consignee_country')
        if not country_id:
            return HttpResponse('<option value="">Select State</option>')
        
        states = State.objects.filter(country_id=country_id).order_by('name')
        options = '<option value="">Select State</option>'
        for state in states:
            options += f'<option value="{state.id}">{state.name}</option>'
        return HttpResponse(options)

class GetCitiesView(View):
    def get(self, request, *args, **kwargs):
        state_id = request.GET.get('buyer_state') or request.GET.get('consignee_state')
        if not state_id:
            return HttpResponse('<option value="">Select City</option>')
        
        cities = City.objects.filter(state_id=state_id).order_by('name')
        options = '<option value="">Select City</option>'
        for city in cities:
            options += f'<option value="{city.id}">{city.name}</option>'
        return HttpResponse(options)

# List View for Proforma Invoices (for redirection after creation)
class ProformaInvoiceListView(ListView):
    model = ProformaInvoice
    template_name = 'invoicing/proforma_invoice/list.html'
    context_object_name = 'proforma_invoices'

class ProformaInvoiceTablePartialView(ListView):
    model = ProformaInvoice
    template_name = 'invoicing/proforma_invoice/_proforma_invoice_table.html'
    context_object_name = 'proforma_invoices'

    def get_queryset(self):
        # Example: Filter by company_id if needed, otherwise all
        company_id = self.request.session.get('compid', 1)
        return ProformaInvoice.objects.filter(company_id=company_id).order_by('-issue_date', '-invoice_no')

```

#### 4.4 Templates

**1. `invoicing/proforma_invoice/form.html` (Main Form Page)**

```html
{% extends 'core/base.html' %}
{% load static %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white p-6 rounded-lg shadow-lg">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Proforma Invoice - New</h2>
        
        {% if messages %}
            {% for message in messages %}
                <div class="p-4 mb-4 text-sm {% if message.tags == 'success' %}text-green-700 bg-green-100{% elif message.tags == 'error' %}text-red-700 bg-red-100{% elif message.tags == 'warning' %}text-yellow-700 bg-yellow-100{% endif %} rounded-lg" role="alert">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}

        <form id="proforma-invoice-form" hx-post="{% url 'proforma_invoice_add' %}" hx-swap="outerHTML" hx-target="#proforma-invoice-form" hx-trigger="submit">
            {% csrf_token %}
            
            <!-- Hidden fields for common parameters -->
            <input type="hidden" name="company_id" value="{{ company_id }}">

            <!-- Header Section -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6 p-4 border rounded-md bg-gray-50">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Invoice No.:</label>
                    {{ form.invoice_no }}
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Date:</label>
                    <p class="font-bold text-gray-900">{{ initial_form_data.issue_date }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Against:</label>
                    <p class="font-bold text-gray-900">{{ form.invoice_mode.value.against }}</p>
                    <input type="hidden" name="{{ form.invoice_mode.name }}" value="{{ form.invoice_mode.value.id }}">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">PO No.:</label>
                    {{ form.po_no }}
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">PO Date:</label>
                    <p class="font-bold text-gray-900">{{ po_date_display }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">WO No.:</label>
                    {{ form.work_order_no }}
                    <input type="hidden" name="po_id" value="{{ initial_form_data.po_id }}"> {# Pass PO Id explicitly #}
                </div>
            </div>

            <!-- Tabbed Interface -->
            <div x-data="{ activeTab: 1 }" class="w-full">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                        <button type="button" @click="activeTab = 1" :class="{'border-indigo-500 text-indigo-600': activeTab === 1, 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 1}" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none">
                            Buyer
                        </button>
                        <button type="button" @click="activeTab = 2" :class="{'border-indigo-500 text-indigo-600': activeTab === 2, 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 2}" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none">
                            Consignee
                        </button>
                        <button type="button" @click="activeTab = 3" :class="{'border-indigo-500 text-indigo-600': activeTab === 3, 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 3}" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none">
                            Goods
                        </button>
                        <button type="button" @click="activeTab = 4" :class="{'border-indigo-500 text-indigo-600': activeTab === 4, 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 4}" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none">
                            Others (Taxation)
                        </button>
                    </nav>
                </div>

                <!-- Tab Content -->
                <div class="mt-6">
                    <!-- Buyer Tab -->
                    <div x-show="activeTab === 1" class="space-y-4">
                        {% include 'invoicing/proforma_invoice/_buyer_form_fields.html' %}
                        <div class="mt-6 flex justify-end space-x-4">
                             <button type="button" @click="activeTab = 2" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Next
                            </button>
                            <a href="{% url 'proforma_invoice_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                        </div>
                    </div>

                    <!-- Consignee Tab -->
                    <div x-show="activeTab === 2" class="space-y-4">
                        {% include 'invoicing/proforma_invoice/_consignee_form_fields.html' %}
                        <div class="mt-6 flex justify-end space-x-4">
                            <button type="button" hx-post="{% url 'copy_buyer_to_consignee' %}" 
                                    hx-target="#consignee-details-form-fields" hx-swap="outerHTML" 
                                    hx-include="#buyer-details-form-fields"
                                    class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded mr-2">
                                Copy from Buyer
                            </button>
                            <button type="button" @click="activeTab = 3" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Next
                            </button>
                            <a href="{% url 'proforma_invoice_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                        </div>
                    </div>

                    <!-- Goods Tab -->
                    <div x-show="activeTab === 3" class="space-y-4">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Goods Details</h3>
                        <div class="overflow-x-auto border rounded-md p-2">
                            <table class="min-w-full divide-y divide-gray-200" id="goodsTable">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
                                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                        <th class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Unit</th>
                                        <th class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Qty (PO)</th>
                                        <th class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Remn Qty</th>
                                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit of Req. Qty</th>
                                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Req Qty</th>
                                        <th class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Rate (PO)</th>
                                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amt (%)</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    {% if detail_forms %}
                                        {% for df in detail_forms %}
                                        <tr>
                                            <td class="py-2 px-4 whitespace-nowrap text-right text-sm text-gray-900">{{ forloop.counter }}</td>
                                            <td class="py-2 px-4 whitespace-nowrap text-center text-sm text-gray-900">
                                                {{ df.is_selected }}
                                                {{ df.item }} {# Hidden item ID #}
                                                {{ df.master }} {# Hidden master ID #}
                                            </td>
                                            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ df.item_desc }}
                                            </td>
                                            <td class="py-2 px-4 whitespace-nowrap text-center text-sm text-gray-900">
                                                {{ df.unit_symbol }}
                                            </td>
                                            <td class="py-2 px-4 whitespace-nowrap text-right text-sm text-gray-900">
                                                {{ df.total_qty_po }}
                                            </td>
                                            <td class="py-2 px-4 whitespace-nowrap text-right text-sm text-gray-900">
                                                {{ df.remaining_qty_display }}
                                            </td>
                                            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ df.unit }}
                                            </td>
                                            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ df.requested_qty }}
                                                {% if df.requested_qty.errors %}
                                                    <p class="text-red-500 text-xs mt-1">{{ df.requested_qty.errors }}</p>
                                                {% endif %}
                                            </td>
                                            <td class="py-2 px-4 whitespace-nowrap text-right text-sm text-gray-900">
                                                {{ df.rate_po }}
                                            </td>
                                            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ df.amount_in_percentage }}
                                                {% if df.amount_in_percentage.errors %}
                                                    <p class="text-red-500 text-xs mt-1">{{ df.amount_in_percentage.errors }}</p>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                        <!-- Hidden management forms for formset (TOTAL_FORMS, INITIAL_FORMS, etc.) -->
                                        <input type="hidden" name="goods_detail-TOTAL_FORMS" value="{{ detail_forms|length }}" id="id_goods_detail-TOTAL_FORMS">
                                        <input type="hidden" name="goods_detail-INITIAL_FORMS" value="0" id="id_goods_detail-INITIAL_FORMS">
                                        <input type="hidden" name="goods_detail-MIN_NUM_FORMS" value="0" id="id_goods_detail-MIN_NUM_FORMS">
                                        <input type="hidden" name="goods_detail-MAX_NUM_FORMS" value="1000" id="id_goods_detail-MAX_NUM_FORMS">
                                    {% else %}
                                    <tr>
                                        <td colspan="10" class="py-4 text-center text-gray-500">No goods data to display.</td>
                                    </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-6 flex justify-end space-x-4">
                            <button type="button" @click="activeTab = 4" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Next
                            </button>
                            <a href="{% url 'proforma_invoice_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                        </div>
                    </div>

                    <!-- Taxation Tab -->
                    <div x-show="activeTab === 4" class="space-y-4">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Other Details (Taxation)</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="{{ form.add_amount.id_for_label }}" class="block text-sm font-medium text-gray-700">Add Amount</label>
                                <div class="mt-1 flex rounded-md shadow-sm">
                                    {{ form.add_amount }}
                                    <div class="ml-2">
                                        {{ form.add_type }}
                                    </div>
                                </div>
                                {% if form.add_amount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.add_amount.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.deduction_amount.id_for_label }}" class="block text-sm font-medium text-gray-700">Deduction Amount</label>
                                <div class="mt-1 flex rounded-md shadow-sm">
                                    {{ form.deduction_amount }}
                                    <div class="ml-2">
                                        {{ form.deduction_type }}
                                    </div>
                                </div>
                                {% if form.deduction_amount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.deduction_amount.errors }}</p>{% endif %}
                            </div>
                        </div>
                        <div class="mt-6 flex justify-end space-x-4">
                            <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded">
                                Submit
                            </button>
                            <a href="{% url 'proforma_invoice_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('proformaForm', () => ({
            activeTab: 1, // Initial active tab
            init() {
                // Initialize Flatpickr for date input
                flatpickr(this.$refs.issueDate, {
                    dateFormat: "d-m-Y",
                    altInput: true,
                    altFormat: "DD-MM-YYYY",
                    maxDate: "today"
                });
            },
            // You can add methods for tab navigation here, though simple x-data update suffices
        }));
    });
</script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Function to enable/disable fields based on checkbox
        function toggleDetailFields(checkbox) {
            const row = checkbox.closest('tr');
            const qtyField = row.querySelector('[name$="-requested_qty"]');
            const amtField = row.querySelector('[name$="-amount_in_percentage"]');
            const unitField = row.querySelector('[name$="-unit"]');

            if (checkbox.checked) {
                if (qtyField) qtyField.removeAttribute('readonly');
                if (amtField) amtField.removeAttribute('readonly');
                if (unitField) unitField.removeAttribute('disabled');
            } else {
                if (qtyField) qtyField.setAttribute('readonly', 'readonly');
                if (amtField) amtField.setAttribute('readonly', 'readonly');
                if (unitField) unitField.setAttribute('disabled', 'disabled');
                qtyField.value = ''; // Clear values if deselected
                amtField.value = '';
            }
        }

        // Apply toggle to existing checkboxes on load
        document.querySelectorAll('input[type="checkbox"][name$="-is_selected"]').forEach(checkbox => {
            toggleDetailFields(checkbox);
            checkbox.addEventListener('change', () => toggleDetailFields(checkbox));
        });

        // Autocomplete logic - HTMX handles the suggestions, Alpine could show/hide results
        // This is a basic example; for a full autocomplete, you'd use a dedicated HTMX endpoint
        // that returns <option> tags or similar HTML fragments.
    });
    
    // Intercept form submission to mark active tab if validation fails on server
    // (This is advanced and might require custom HTMX response headers)
    document.body.addEventListener('htmx:afterOnLoad', function(evt) {
        if (evt.detail.xhr.status === 400) { // Bad request likely due to validation errors
            // Find the form and its errors, identify which tab the errors belong to
            // This would require a more sophisticated error handling from the server
            // e.g., server returning a header like X-Active-Tab: 2
            const activeTabHeader = evt.detail.xhr.getResponseHeader('X-Active-Tab');
            if (activeTabHeader) {
                Alpine.raw(document.querySelector('[x-data="{ activeTab: 1 }"]')._x_dataStack[0]).activeTab = parseInt(activeTabHeader);
            }
        }
    });

</script>
{% endblock %}
```

**2. `invoicing/proforma_invoice/_buyer_form_fields.html` (Partial for Buyer Tab)**

```html
<div id="buyer-details-form-fields" class="space-y-4">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Buyer Details</h3>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
            <label for="{{ form.buyer_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Name</label>
            {{ form.buyer_name }}
            {% if form.buyer_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_name.errors }}</p>{% endif %}
            <div id="buyer-autocomplete-results" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg w-auto max-h-60 overflow-y-auto"></div>
        </div>
        <div>
            <label for="{{ form.buyer_contact_person.id_for_label }}" class="block text-sm font-medium text-gray-700">Contact Person</label>
            {{ form.buyer_contact_person }}
            {% if form.buyer_contact_person.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_contact_person.errors }}</p>{% endif %}
        </div>
    </div>
    <div>
        <label for="{{ form.buyer_address.id_for_label }}" class="block text-sm font-medium text-gray-700">Address</label>
        {{ form.buyer_address }}
        {% if form.buyer_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_address.errors }}</p>{% endif %}
    </div>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
            <label for="{{ form.buyer_country.id_for_label }}" class="block text-sm font-medium text-gray-700">Country</label>
            {{ form.buyer_country }}
            {% if form.buyer_country.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_country.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.buyer_state.id_for_label }}" class="block text-sm font-medium text-gray-700">State</label>
            {{ form.buyer_state }}
            {% if form.buyer_state.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_state.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.buyer_city.id_for_label }}" class="block text-sm font-medium text-gray-700">City</label>
            {{ form.buyer_city }}
            {% if form.buyer_city.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_city.errors }}</p>{% endif %}
        </div>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
            <label for="{{ form.buyer_phone.id_for_label }}" class="block text-sm font-medium text-gray-700">Phone No.</label>
            {{ form.buyer_phone }}
            {% if form.buyer_phone.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_phone.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.buyer_mobile.id_for_label }}" class="block text-sm font-medium text-gray-700">Mobile No.</label>
            {{ form.buyer_mobile }}
            {% if form.buyer_mobile.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_mobile.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.buyer_email.id_for_label }}" class="block text-sm font-medium text-gray-700">E-mail</label>
            {{ form.buyer_email }}
            {% if form.buyer_email.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_email.errors }}</p>{% endif %}
        </div>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
            <label for="{{ form.buyer_fax.id_for_label }}" class="block text-sm font-medium text-gray-700">Fax No.</label>
            {{ form.buyer_fax }}
            {% if form.buyer_fax.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_fax.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.buyer_tin_vat_no.id_for_label }}" class="block text-sm font-medium text-gray-700">TIN/VAT No.</label>
            {{ form.buyer_tin_vat_no }}
            {% if form.buyer_tin_vat_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_tin_vat_no.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.buyer_ecc_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Customer's ECC.No.</label>
            {{ form.buyer_ecc_no }}
            {% if form.buyer_ecc_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_ecc_no.errors }}</p>{% endif %}
        </div>
    </div>
    <div>
        <label for="{{ form.buyer_tin_cst_no.id_for_label }}" class="block text-sm font-medium text-gray-700">TIN / CST No.</label>
        {{ form.buyer_tin_cst_no }}
        {% if form.buyer_tin_cst_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_tin_cst_no.errors }}</p>{% endif %}
    </div>
</div>
```

**3. `invoicing/proforma_invoice/_consignee_form_fields.html` (Partial for Consignee Tab)**

```html
<div id="consignee-details-form-fields" class="space-y-4">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Consignee Details</h3>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
            <label for="{{ form.consignee_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Name</label>
            {{ form.consignee_name }}
            {% if form.consignee_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_name.errors }}</p>{% endif %}
            <div id="consignee-autocomplete-results" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg w-auto max-h-60 overflow-y-auto"></div>
        </div>
        <div>
            <label for="{{ form.consignee_contact_person.id_for_label }}" class="block text-sm font-medium text-gray-700">Contact Person</label>
            {{ form.consignee_contact_person }}
            {% if form.consignee_contact_person.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_contact_person.errors }}</p>{% endif %}
        </div>
    </div>
    <div>
        <label for="{{ form.consignee_address.id_for_label }}" class="block text-sm font-medium text-gray-700">Address</label>
        {{ form.consignee_address }}
        {% if form.consignee_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_address.errors }}</p>{% endif %}
    </div>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
            <label for="{{ form.consignee_country.id_for_label }}" class="block text-sm font-medium text-gray-700">Country</label>
            {{ form.consignee_country }}
            {% if form.consignee_country.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_country.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.consignee_state.id_for_label }}" class="block text-sm font-medium text-gray-700">State</label>
            {{ form.consignee_state }}
            {% if form.consignee_state.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_state.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.consignee_city.id_for_label }}" class="block text-sm font-medium text-gray-700">City</label>
            {{ form.consignee_city }}
            {% if form.consignee_city.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_city.errors }}</p>{% endif %}
        </div>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
            <label for="{{ form.consignee_phone.id_for_label }}" class="block text-sm font-medium text-gray-700">Phone No.</label>
            {{ form.consignee_phone }}
            {% if form.consignee_phone.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_phone.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.consignee_mobile.id_for_label }}" class="block text-sm font-medium text-gray-700">Mobile No.</label>
            {{ form.consignee_mobile }}
            {% if form.consignee_mobile.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_mobile.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.consignee_email.id_for_label }}" class="block text-sm font-medium text-gray-700">E-mail</label>
            {{ form.consignee_email }}
            {% if form.consignee_email.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_email.errors }}</p>{% endif %}
        </div>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
            <label for="{{ form.consignee_fax.id_for_label }}" class="block text-sm font-medium text-gray-700">Fax No.</label>
            {{ form.consignee_fax }}
            {% if form.consignee_fax.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_fax.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.consignee_tin_vat_no.id_for_label }}" class="block text-sm font-medium text-gray-700">TIN/VAT No.</label>
            {{ form.consignee_tin_vat_no }}
            {% if form.consignee_tin_vat_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_tin_vat_no.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.consignee_ecc_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Customer's ECC.No.</label>
            {{ form.consignee_ecc_no }}
            {% if form.consignee_ecc_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_ecc_no.errors }}</p>{% endif %}
        </div>
    </div>
    <div>
        <label for="{{ form.consignee_tin_cst_no.id_for_label }}" class="block text-sm font-medium text-gray-700">TIN / CST No.</label>
        {{ form.consignee_tin_cst_no }}
        {% if form.consignee_tin_cst_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.consignee_tin_cst_no.errors }}</p>{% endif %}
    </div>
</div>
```

**4. `invoicing/proforma_invoice/list.html` (List View Page)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Proforma Invoices</h2>
        <a href="{% url 'proforma_invoice_add' %}" 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Add New Proforma Invoice
        </a>
    </div>
    
    {% if messages %}
        {% for message in messages %}
            <div class="p-4 mb-4 text-sm {% if message.tags == 'success' %}text-green-700 bg-green-100{% elif message.tags == 'error' %}text-red-700 bg-red-100{% elif message.tags == 'warning' %}text-yellow-700 bg-yellow-100{% endif %} rounded-lg" role="alert">
                {{ message }}
            </div>
        {% endfor %}
    {% endif %}

    <div id="proformaInvoiceTable-container"
         hx-trigger="load, refreshProformaInvoiceList from:body"
         hx-get="{% url 'proforma_invoice_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Proforma Invoices...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // No specific Alpine.js for this list view, DataTables handles it.
</script>
{% endblock %}
```

**5. `invoicing/proforma_invoice/_proforma_invoice_table.html` (Partial for DataTable)**

```html
<div class="overflow-x-auto bg-white rounded-lg shadow">
    <table id="proformaInvoiceTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice No.</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issue Date</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Buyer Name</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No.</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No.</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for invoice in proforma_invoices %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ invoice.invoice_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ invoice.issue_date|date:"d-m-Y" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ invoice.buyer_name }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ invoice.po_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ invoice.work_order_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">
                    <button 
                        class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded mr-2"
                        hx-get="{% url 'proforma_invoice_detail' invoice.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        View
                    </button>
                    {# No edit/delete on original page, but placeholder for future #}
                    {#
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                        hx-get="{% url 'proforma_invoice_edit' invoice.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                        hx-get="{% url 'proforma_invoice_delete' invoice.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                    #}
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-4 px-4 text-center text-sm text-gray-500">No Proforma Invoices found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
$(document).ready(function() {
    $('#proformaInvoiceTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "searching": true,
        "ordering": true,
        "paging": true,
        "info": true
    });
});
</script>
```

#### 4.5 URLs (`invoicing/urls.py`)

```python
from django.urls import path
from .views import (
    ProformaInvoiceCreateView,
    ProformaInvoiceListView,
    ProformaInvoiceTablePartialView,
    CustomerAutocompleteView,
    CustomerSearchAndPopulateView,
    CopyBuyerToConsigneeView,
    GetStatesView,
    GetCitiesView,
)

urlpatterns = [
    # Main form to create a new Proforma Invoice
    path('proforma-invoice/new/', ProformaInvoiceCreateView.as_view(), name='proforma_invoice_add'),
    
    # List view for existing Proforma Invoices
    path('proforma-invoice/', ProformaInvoiceListView.as_view(), name='proforma_invoice_list'),
    path('proforma-invoice/table/', ProformaInvoiceTablePartialView.as_view(), name='proforma_invoice_table'),
    
    # HTMX endpoints for dynamic interactions
    path('customer-autocomplete/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),
    path('customer-search-and-populate/', CustomerSearchAndPopulateView.as_view(), name='customer_search_and_populate'),
    path('copy-buyer-to-consignee/', CopyBuyerToConsigneeView.as_view(), name='copy_buyer_to_consignee'),
    path('get-states/', GetStatesView.as_view(), name='get_states'),
    path('get-cities/', GetCitiesView.as_view(), name='get_cities'),

    # Placeholder for future detail/edit/delete views (not implemented in original ASP.NET code for this page)
    # path('proforma-invoice/<int:pk>/', ProformaInvoiceDetailView.as_view(), name='proforma_invoice_detail'),
    # path('proforma-invoice/<int:pk>/edit/', ProformaInvoiceUpdateView.as_view(), name='proforma_invoice_edit'),
    # path('proforma-invoice/<int:pk>/delete/', ProformaInvoiceDeleteView.as_view(), name='proforma_invoice_delete'),
]
```

#### 4.6 Tests (`invoicing/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from decimal import Decimal
from datetime import date
from unittest.mock import patch, MagicMock

# Import all models and forms that will be tested
from .models import (
    ProformaInvoice, ProformaInvoiceDetail,
    Company, FinancialYear, InvoiceAgainstMode,
    Customer, Unit, PurchaseOrder, PurchaseOrderDetail, WorkOrder,
    Country, State, City
)
from .forms import ProformaInvoiceForm, ProformaInvoiceDetailForm

class CommonSetupMixin:
    @classmethod
    def setUpTestData(cls):
        # Create necessary lookup data
        cls.company = Company.objects.create(id=1, name='Test Company')
        cls.fin_year = FinancialYear.objects.create(id=1, year_name='2023-2024')
        cls.invoice_mode = InvoiceAgainstMode.objects.create(id=1, against='Sales Order')
        
        cls.country_in = Country.objects.create(id=1, name='India')
        cls.state_mh = State.objects.create(id=1, name='Maharashtra', country=cls.country_in)
        cls.city_mumbai = City.objects.create(id=1, name='Mumbai', state=cls.state_mh)
        cls.country_us = Country.objects.create(id=2, name='USA')
        cls.state_ca = State.objects.create(id=2, name='California', country=cls.country_us)
        cls.city_la = City.objects.create(id=2, name='Los Angeles', state=cls.state_ca)

        cls.customer_a = Customer.objects.create(
            customer_id='CUST001', customer_name='Customer A',
            material_del_address='123 Buyer St', material_del_country=cls.country_in.id,
            material_del_state=cls.state_mh.id, material_del_city=cls.city_mumbai.id,
            material_del_contact_no='1112223333', email='<EMAIL>',
            contact_person='John Doe', tin_vat_no='VAT123', ecc_no='ECC001',
            contact_no='9988776655', tin_cst_no='CST456',
            # Add company_id if Customer model had it
        )
        cls.customer_b = Customer.objects.create(
            customer_id='CUST002', customer_name='Customer B',
            material_del_address='456 Consignee Rd', material_del_country=cls.country_us.id,
            material_del_state=cls.state_ca.id, material_del_city=cls.city_la.id,
            material_del_contact_no='4445556666', email='<EMAIL>',
            contact_person='Jane Smith', tin_vat_no='VAT789', ecc_no='ECC002',
            contact_no='7766554433', tin_cst_no='CST987',
            # Add company_id if Customer model had it
        )

        cls.unit_pcs = Unit.objects.create(id=1, symbol='PCS')
        cls.unit_kg = Unit.objects.create(id=2, symbol='KG')

        cls.po = PurchaseOrder.objects.create(po_id=101, po_no='PO-2023-001', company=cls.company)
        cls.wo = WorkOrder.objects.create(id=201, wo_no='WO-2023-001', company=cls.company)
        
        cls.po_detail_1 = PurchaseOrderDetail.objects.create(
            id=1, po=cls.po, item_desc='Item A', total_qty=Decimal('100.000'), 
            unit=cls.unit_pcs, rate=Decimal('10.50')
        )
        cls.po_detail_2 = PurchaseOrderDetail.objects.create(
            id=2, po=cls.po, item_desc='Item B', total_qty=Decimal('50.000'), 
            unit=cls.unit_kg, rate=Decimal('25.00')
        )
        
        # Create an existing invoice detail to test remaining quantity logic
        cls.existing_invoice_detail = ProformaInvoiceDetail.objects.create(
            id=99,
            invoice_no='OLD001',
            master=ProformaInvoice.objects.create(
                id=99,
                sys_date=date(2023, 1, 1),
                sys_time='10:00:00',
                company=cls.company,
                financial_year=cls.fin_year,
                session_id='olduser',
                invoice_no='OLD001',
                po_no='OLDPO',
                work_order_no='OLDWO',
                invoice_mode=cls.invoice_mode,
                issue_date=date(2023, 1, 1),
                customer_code='CUST001',
                buyer_name='Old Buyer', buyer_address='Old Address',
                buyer_country=cls.country_in, buyer_state=cls.state_mh, buyer_city=cls.city_mumbai,
                buyer_contact_person='Old Cont', buyer_phone='123', buyer_mobile='456', buyer_email='<EMAIL>',
                buyer_ecc_no='old', buyer_tin_cst_no='old', buyer_fax='old', buyer_tin_vat_no='old',
                consignee_name='Old Cons', consignee_address='Old Cons Address',
                consignee_country=cls.country_in, consignee_state=cls.state_mh, consignee_city=cls.city_mumbai,
                consignee_contact_person='Old Cons Cont', consignee_phone='789', consignee_mobile='012', consignee_email='<EMAIL>',
                consignee_ecc_no='old_c', consignee_tin_cst_no='old_c', consignee_fax='old_c', consignee_tin_vat_no='old_c',
                add_type=0, add_amount=Decimal('0'), deduction_type=0, deduction_amount=Decimal('0'),
                po_id=100 # Dummy PO ID for the existing invoice
            ),
            item=cls.po_detail_1,
            unit=cls.unit_pcs,
            total_qty_from_po=Decimal('100.000'),
            requested_qty=Decimal('20.000'), # 20 qty already invoiced for Item A
            amount_in_percentage=Decimal('10.00'),
            rate=Decimal('10.50')
        )

class ProformaInvoiceModelTest(CommonSetupMixin, TestCase):
    def test_proforma_invoice_creation(self):
        invoice = ProformaInvoice.objects.create(
            id=1,
            sys_date=date(2023, 11, 15),
            sys_time='14:30:00',
            company=self.company,
            financial_year=self.fin_year,
            session_id='testuser',
            invoice_no='0001',
            po_no='PO-XYZ',
            work_order_no='WO-ABC',
            invoice_mode=self.invoice_mode,
            issue_date=date(2023, 11, 15),
            customer_code='CUST001',
            buyer_name='Test Buyer',
            buyer_address='123 Test St',
            buyer_country=self.country_in,
            buyer_state=self.state_mh,
            buyer_city=self.city_mumbai,
            buyer_contact_person='Contact A',
            buyer_phone='1234567890',
            buyer_mobile='0987654321',
            buyer_email='<EMAIL>',
            buyer_ecc_no='ECC123',
            buyer_tin_cst_no='TIN456',
            buyer_fax='FAX789',
            buyer_tin_vat_no='VAT012',
            consignee_name='Test Consignee',
            consignee_address='456 Test Ave',
            consignee_country=self.country_in,
            consignee_state=self.state_mh,
            consignee_city=self.city_mumbai,
            consignee_contact_person='Contact B',
            consignee_phone='2345678901',
            consignee_mobile='1098765432',
            consignee_email='<EMAIL>',
            consignee_ecc_no='ECC321',
            consignee_tin_cst_no='TIN654',
            consignee_fax='FAX987',
            consignee_tin_vat_no='VAT210',
            add_type=0, add_amount=Decimal('100.00'),
            deduction_type=1, deduction_amount=Decimal('5.00'),
            po_id=self.po.po_id
        )
        self.assertIsInstance(invoice, ProformaInvoice)
        self.assertEqual(invoice.invoice_no, '0001')
        self.assertEqual(invoice.buyer_name, 'Test Buyer')

    def test_proforma_invoice_detail_creation(self):
        # First, create a master invoice
        master_invoice = ProformaInvoice.objects.create(
            id=2, sys_date=date(2023, 11, 15), sys_time='14:30:00',
            company=self.company, financial_year=self.fin_year, session_id='testuser',
            invoice_no='0002', po_no='PO-XYZ', work_order_no='WO-ABC', invoice_mode=self.invoice_mode,
            issue_date=date(2023, 11, 15), customer_code='CUST001',
            buyer_name='Test Buyer', buyer_address='123 Test St', buyer_country=self.country_in,
            buyer_state=self.state_mh, buyer_city=self.city_mumbai, buyer_contact_person='Contact A',
            buyer_phone='1234567890', buyer_mobile='0987654321', buyer_email='<EMAIL>',
            buyer_ecc_no='ECC123', buyer_tin_cst_no='TIN456', buyer_fax='FAX789', buyer_tin_vat_no='VAT012',
            consignee_name='Test Consignee', consignee_address='456 Test Ave', consignee_country=self.country_in,
            consignee_state=self.state_mh, consignee_city=self.city_mumbai, consignee_contact_person='Contact B',
            consignee_phone='2345678901', consignee_mobile='1098765432', consignee_email='<EMAIL>',
            consignee_ecc_no='ECC321', consignee_tin_cst_no='TIN654', consignee_fax='FAX987', consignee_tin_vat_no='VAT210',
            add_type=0, add_amount=Decimal('100.00'), deduction_type=1, deduction_amount=Decimal('5.00'),
            po_id=self.po.po_id
        )
        detail = ProformaInvoiceDetail.objects.create(
            id=1,
            invoice_no=master_invoice.invoice_no,
            master=master_invoice,
            item=self.po_detail_1,
            unit=self.unit_pcs,
            total_qty_from_po=Decimal('100.000'),
            requested_qty=Decimal('10.000'),
            amount_in_percentage=Decimal('5.00'),
            rate=Decimal('10.50')
        )
        self.assertIsInstance(detail, ProformaInvoiceDetail)
        self.assertEqual(detail.master.invoice_no, '0002')
        self.assertEqual(detail.item.item_desc, 'Item A')

    def test_get_next_invoice_no(self):
        # Ensure that the next invoice number is generated correctly
        next_invoice = ProformaInvoice.get_next_invoice_no(self.company.id, self.fin_year.id)
        self.assertEqual(next_invoice, '0003') # Because 0001 and 0002 were created

    def test_purchase_order_detail_get_invoiced_quantity(self):
        # Item A had 20.000 already invoiced by existing_invoice_detail
        invoiced_qty = self.po_detail_1.get_invoiced_quantity(self.company.id)
        self.assertEqual(invoiced_qty, Decimal('20.000'))
        
        # Item B has no invoiced quantity
        invoiced_qty_b = self.po_detail_2.get_invoiced_quantity(self.company.id)
        self.assertEqual(invoiced_qty_b, Decimal('0.000'))

    def test_purchase_order_detail_get_remaining_quantity(self):
        # Item A total_qty=100, invoiced_qty=20, so remaining=80
        remaining_qty = self.po_detail_1.get_remaining_quantity(self.company.id)
        self.assertEqual(remaining_qty, Decimal('80.000'))
        
        # Item B total_qty=50, invoiced_qty=0, so remaining=50
        remaining_qty_b = self.po_detail_2.get_remaining_quantity(self.company.id)
        self.assertEqual(remaining_qty_b, Decimal('50.000'))

    def test_proforma_invoice_update_from_customer_buyer(self):
        temp_invoice = ProformaInvoice()
        temp_invoice.update_from_customer(self.customer_a, is_buyer=True)
        self.assertEqual(temp_invoice.buyer_name, 'Customer A')
        self.assertEqual(temp_invoice.buyer_email, '<EMAIL>')
        self.assertEqual(temp_invoice.buyer_country_id, self.country_in.id)

    def test_proforma_invoice_update_from_customer_consignee(self):
        temp_invoice = ProformaInvoice()
        temp_invoice.update_from_customer(self.customer_b, is_buyer=False)
        self.assertEqual(temp_invoice.consignee_name, 'Customer B')
        self.assertEqual(temp_invoice.consignee_email, '<EMAIL>')
        self.assertEqual(temp_invoice.consignee_country_id, self.country_us.id)

class ProformaInvoiceFormsTest(CommonSetupMixin, TestCase):
    def test_proforma_invoice_form_valid_data(self):
        form_data = {
            'invoice_no': '0003', # Read-only, but should be present
            'po_no': 'PO-2023-001',
            'work_order_no': 'WO-2023-001',
            'invoice_mode': self.invoice_mode.id,
            'issue_date': '15-11-2023',
            'customer_code': self.customer_a.customer_id,
            
            'buyer_name': 'Customer A [CUST001]',
            'buyer_address': '123 Buyer St',
            'buyer_country': self.country_in.id,
            'buyer_state': self.state_mh.id,
            'buyer_city': self.city_mumbai.id,
            'buyer_contact_person': 'John Doe',
            'buyer_phone': '1112223333',
            'buyer_mobile': '9988776655',
            'buyer_email': '<EMAIL>',
            'buyer_fax': '',
            'buyer_tin_vat_no': 'VAT123',
            'buyer_ecc_no': 'ECC001',
            'buyer_tin_cst_no': 'CST456',

            'consignee_name': 'Customer B [CUST002]',
            'consignee_address': '456 Consignee Rd',
            'consignee_country': self.country_us.id,
            'consignee_state': self.state_ca.id,
            'consignee_city': self.city_la.id,
            'consignee_contact_person': 'Jane Smith',
            'consignee_phone': '4445556666',
            'consignee_mobile': '7766554433',
            'consignee_email': '<EMAIL>',
            'consignee_fax': '',
            'consignee_tin_vat_no': 'VAT789',
            'consignee_ecc_no': 'ECC002',
            'consignee_tin_cst_no': 'CST987',
            
            'add_type': 0, 'add_amount': '10.000',
            'deduction_type': 1, 'deduction_amount': '2.500',
            'po_id': self.po.po_id,
        }
        form = ProformaInvoiceForm(data=form_data)
        self.assertTrue(form.is_valid(), form.errors.as_json())

    def test_proforma_invoice_form_missing_required_data(self):
        form_data = {
            'invoice_no': '0003', # Missing issue_date, buyer_name etc.
            'po_no': 'PO-2023-001',
            'work_order_no': 'WO-2023-001',
            'invoice_mode': self.invoice_mode.id,
            'issue_date': '', # Missing
            'customer_code': '', # Missing
            'buyer_name': '', # Missing
            'add_type': 0, 'add_amount': '', # Missing
            'deduction_type': 1, 'deduction_amount': '', # Missing
            'po_id': self.po.po_id,
        }
        # Fill required buyer/consignee fields with dummy data to pass initial checks
        # but omit critical ones.
        # This test ensures `required=True` is respected.
        form_data.update({
            'buyer_address': 'Dummy', 'buyer_country': self.country_in.id, 'buyer_state': self.state_mh.id, 'buyer_city': self.city_mumbai.id,
            'buyer_contact_person': 'Dummy', 'buyer_phone': '1', 'buyer_mobile': '1', 'buyer_email': '<EMAIL>', 'buyer_tin_vat_no': '1',
            'buyer_ecc_no': '1', 'buyer_tin_cst_no': '1',
            'consignee_name': 'Dummy', 'consignee_address': 'Dummy', 'consignee_country': self.country_in.id, 'consignee_state': self.state_mh.id, 'consignee_city': self.city_mumbai.id,
            'consignee_contact_person': 'Dummy', 'consignee_phone': '1', 'consignee_mobile': '1', 'consignee_email': '<EMAIL>', 'consignee_tin_vat_no': '1',
            'consignee_ecc_no': '1', 'consignee_tin_cst_no': '1',
        })
        form = ProformaInvoiceForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('issue_date', form.errors)
        self.assertIn('buyer_name', form.errors)
        self.assertIn('add_amount', form.errors)

    def test_proforma_invoice_detail_form_valid_data(self):
        # Test valid detail form when selected
        form_data = {
            'is_selected': 'on',
            'item': self.po_detail_1.id, # Item ID from PurchaseOrderDetail
            'unit': self.unit_pcs.id,
            'requested_qty': '10.000',
            'amount_in_percentage': '5.00',
            # Hidden fields that are auto-populated by __init__ or not directly user input
            'company_id': self.company.id # Passed for validation
        }
        form = ProformaInvoiceDetailForm(data=form_data, purchase_order_detail=self.po_detail_1, company_id=self.company.id)
        self.assertTrue(form.is_valid(), form.errors.as_json())
        self.assertEqual(form.cleaned_data['requested_qty'], Decimal('10.000'))

    def test_proforma_invoice_detail_form_not_selected(self):
        # If not selected, fields should not be required
        form_data = {
            'is_selected': '', # Not checked
            'item': self.po_detail_1.id,
            'unit': self.unit_pcs.id,
            'requested_qty': '', # Empty
            'amount_in_percentage': '', # Empty
            'company_id': self.company.id
        }
        form = ProformaInvoiceDetailForm(data=form_data, purchase_order_detail=self.po_detail_1, company_id=self.company.id)
        self.assertTrue(form.is_valid()) # No errors expected if not selected

    def test_proforma_invoice_detail_form_qty_exceeds_remaining(self):
        # Item A has 100 total, 20 already invoiced, 80 remaining.
        # Requesting 90 should fail.
        form_data = {
            'is_selected': 'on',
            'item': self.po_detail_1.id,
            'unit': self.unit_pcs.id,
            'requested_qty': '90.000', # Exceeds 80 remaining
            'amount_in_percentage': '5.00',
            'company_id': self.company.id
        }
        form = ProformaInvoiceDetailForm(data=form_data, purchase_order_detail=self.po_detail_1, company_id=self.company.id)
        self.assertFalse(form.is_valid())
        self.assertIn('requested_qty', form.errors)
        self.assertIn('exceeds remaining quantity', form.errors['requested_qty'][0])

class ProformaInvoiceViewsTest(CommonSetupMixin, TestCase):
    def setUp(self):
        self.client = Client()
        # Mock session attributes if they are not naturally set by Django's test client
        self.client.session['compid'] = self.company.id
        self.client.session['finyear'] = self.fin_year.id
        self.client.session['username'] = 'testuser'

    def test_create_view_get(self):
        # Parameters mirroring the ASP.NET query string
        url = reverse('proforma_invoice_add') + f'?wn={self.wo.id},&pn={self.po.po_no}&poid={self.po.po_id}&cid={self.customer_a.customer_id}&ty={self.invoice_mode.id}&date=10-01-2023'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'invoicing/proforma_invoice/form.html')
        self.assertContains(response, 'Proforma Invoice - New')
        self.assertContains(response, 'Customer A [CUST001]') # Buyer Name pre-filled
        self.assertContains(response, 'Item A') # Goods table should show items
        self.assertContains(response, '80.000') # Remaining quantity for Item A (100 - 20 existing)

    def test_create_view_post_success(self):
        # A valid form submission including selected goods items
        form_data = {
            'issue_date': '15-11-2023',
            'invoice_mode': self.invoice_mode.id,
            'customer_code': self.customer_a.customer_id,
            
            'buyer_name': 'Customer A [CUST001]',
            'buyer_address': '123 Buyer St', 'buyer_country': self.country_in.id, 'buyer_state': self.state_mh.id, 'buyer_city': self.city_mumbai.id,
            'buyer_contact_person': 'John Doe', 'buyer_phone': '1112223333', 'buyer_mobile': '9988776655', 'buyer_email': '<EMAIL>',
            'buyer_fax': '', 'buyer_tin_vat_no': 'VAT123', 'buyer_ecc_no': 'ECC001', 'buyer_tin_cst_no': 'CST456',

            'consignee_name': 'Customer B [CUST002]',
            'consignee_address': '456 Consignee Rd', 'consignee_country': self.country_us.id, 'consignee_state': self.state_ca.id, 'consignee_city': self.city_la.id,
            'consignee_contact_person': 'Jane Smith', 'consignee_phone': '4445556666', 'consignee_mobile': '7766554433', 'consignee_email': '<EMAIL>',
            'consignee_fax': '', 'consignee_tin_vat_no': 'VAT789', 'consignee_ecc_no': 'ECC002', 'consignee_tin_cst_no': 'CST987',
            
            'add_type': 0, 'add_amount': '10.000',
            'deduction_type': 1, 'deduction_amount': '2.500',
            'po_id': self.po.po_id,
            'company_id': self.company.id, # Included for views that might need it from POST

            # Formset data for goods (manually constructed as per template)
            'goods_detail-TOTAL_FORMS': '2', 'goods_detail-INITIAL_FORMS': '0',
            'goods_detail-MIN_NUM_FORMS': '0', 'goods_detail-MAX_NUM_FORMS': '1000',
            
            # Item 1 (selected)
            'goods_detail-0-is_selected': 'on',
            'goods_detail-0-item': self.po_detail_1.id,
            'goods_detail-0-unit': self.unit_pcs.id,
            'goods_detail-0-requested_qty': '10.000', # Valid qty (10 <= 80 remaining)
            'goods_detail-0-amount_in_percentage': '5.00',

            # Item 2 (not selected)
            'goods_detail-1-is_selected': '', # Not selected
            'goods_detail-1-item': self.po_detail_2.id,
            'goods_detail-1-unit': self.unit_kg.id,
            'goods_detail-1-requested_qty': '',
            'goods_detail-1-amount_in_percentage': '',
        }
        
        response = self.client.post(reverse('proforma_invoice_add'), data=form_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HTMX success status
        self.assertIn('HX-Redirect', response.headers)
        self.assertEqual(response.headers['HX-Redirect'], reverse('proforma_invoice_list'))
        
        messages_ = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages_), 1)
        self.assertEqual(str(messages_[0]), 'Proforma Invoice created successfully!')

        # Verify new invoice and detail records were created
        self.assertEqual(ProformaInvoice.objects.count(), 3) # Existing + 2 new
        new_invoice = ProformaInvoice.objects.get(invoice_no='0003') # Check next invoice number
        self.assertEqual(new_invoice.buyer_name, 'Customer A [CUST001]')
        
        self.assertEqual(ProformaInvoiceDetail.objects.count(), 2) # Existing + 1 new
        new_detail = ProformaInvoiceDetail.objects.get(master=new_invoice)
        self.assertEqual(new_detail.item.item_desc, 'Item A')
        self.assertEqual(new_detail.requested_qty, Decimal('10.000'))

    def test_create_view_post_qty_exceeds_remaining_failure(self):
        # A valid form submission including selected goods items
        form_data = {
            'issue_date': '15-11-2023',
            'invoice_mode': self.invoice_mode.id,
            'customer_code': self.customer_a.customer_id,
            
            'buyer_name': 'Customer A [CUST001]',
            'buyer_address': '123 Buyer St', 'buyer_country': self.country_in.id, 'buyer_state': self.state_mh.id, 'buyer_city': self.city_mumbai.id,
            'buyer_contact_person': 'John Doe', 'buyer_phone': '1112223333', 'buyer_mobile': '9988776655', 'buyer_email': '<EMAIL>',
            'buyer_fax': '', 'buyer_tin_vat_no': 'VAT123', 'buyer_ecc_no': 'ECC001', 'buyer_tin_cst_no': 'CST456',

            'consignee_name': 'Customer B [CUST002]',
            'consignee_address': '456 Consignee Rd', 'consignee_country': self.country_us.id, 'consignee_state': self.state_ca.id, 'consignee_city': self.city_la.id,
            'consignee_contact_person': 'Jane Smith', 'consignee_phone': '4445556666', 'consignee_mobile': '7766554433', 'consignee_email': '<EMAIL>',
            'consignee_fax': '', 'consignee_tin_vat_no': 'VAT789', 'consignee_ecc_no': 'ECC002', 'consignee_tin_cst_no': 'CST987',
            
            'add_type': 0, 'add_amount': '10.000',
            'deduction_type': 1, 'deduction_amount': '2.500',
            'po_id': self.po.po_id,
            'company_id': self.company.id,

            # Formset data for goods (manually constructed as per template)
            'goods_detail-TOTAL_FORMS': '1', 'goods_detail-INITIAL_FORMS': '0',
            'goods_detail-MIN_NUM_FORMS': '0', 'goods_detail-MAX_NUM_FORMS': '1000',
            
            # Item 1 (selected, but invalid qty)
            'goods_detail-0-is_selected': 'on',
            'goods_detail-0-item': self.po_detail_1.id,
            'goods_detail-0-unit': self.unit_pcs.id,
            'goods_detail-0-requested_qty': '90.000', # Invalid qty (90 > 80 remaining)
            'goods_detail-0-amount_in_percentage': '5.00',
        }
        
        response = self.client.post(reverse('proforma_invoice_add'), data=form_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200) # Should return form with errors
        self.assertTemplateUsed(response, 'invoicing/proforma_invoice/form.html')
        self.assertContains(response, 'Requested quantity (90.000) exceeds remaining quantity (80.000).')
        self.assertEqual(ProformaInvoice.objects.count(), 2) # No new invoice created
        self.assertEqual(ProformaInvoiceDetail.objects.count(), 1) # No new detail created

    def test_customer_autocomplete_view(self):
        url = reverse('customer_autocomplete') + '?q=Cust'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = response.json()
        self.assertIn('Customer A [CUST001]', data)
        self.assertIn('Customer B [CUST002]', data)

    def test_customer_search_and_populate_buyer_view(self):
        url = reverse('customer_search_and_populate')
        response = self.client.post(url, {'buyer_name_input': 'Customer A [CUST001]'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertInHTML('<input type="text" name="buyer_address" value="123 Buyer St"', response.content.decode())
        self.assertInHTML('<select name="buyer_country">', response.content.decode())
        self.assertInHTML(f'<option value="{self.country_in.id}" selected>', response.content.decode())

    def test_copy_buyer_to_consignee_view(self):
        url = reverse('copy_buyer_to_consignee')
        # Simulate form data with buyer fields populated
        form_data = {
            'buyer_name': 'Customer A [CUST001]',
            'buyer_address': '123 Buyer St', 'buyer_country': self.country_in.id, 'buyer_state': self.state_mh.id, 'buyer_city': self.city_mumbai.id,
            'buyer_contact_person': 'John Doe', 'buyer_phone': '1112223333', 'buyer_mobile': '9988776655', 'buyer_email': '<EMAIL>',
            'buyer_fax': '', 'buyer_tin_vat_no': 'VAT123', 'buyer_ecc_no': 'ECC001', 'buyer_tin_cst_no': 'CST456',
        }
        response = self.client.post(url, form_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        # Check if consignee fields are populated with buyer data
        self.assertInHTML('<input type="text" name="consignee_contact_person" value="John Doe"', response.content.decode())
        self.assertInHTML(f'<option value="{self.country_in.id}" selected>', response.content.decode())

    def test_get_states_view(self):
        url = reverse('get_states') + f'?buyer_country={self.country_in.id}'
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertInHTML(f'<option value="{self.state_mh.id}">{self.state_mh.name}</option>', response.content.decode())

    def test_get_cities_view(self):
        url = reverse('get_cities') + f'?buyer_state={self.state_mh.id}'
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertInHTML(f'<option value="{self.city_mumbai.id}">{self.city_mumbai.name}</option>', response.content.decode())

    def test_proforma_invoice_list_view(self):
        # Create a dummy invoice for listing
        ProformaInvoice.objects.create(
            id=3, sys_date=date(2023, 1, 1), sys_time='10:00:00',
            company=self.company, financial_year=self.fin_year, session_id='testuser',
            invoice_no='0004', po_no='PO-LIST', work_order_no='WO-LIST', invoice_mode=self.invoice_mode,
            issue_date=date(2023, 1, 1), customer_code='CUST001',
            buyer_name='List Buyer', buyer_address='Address', buyer_country=self.country_in,
            buyer_state=self.state_mh, buyer_city=self.city_mumbai, buyer_contact_person='Contact',
            buyer_phone='123', buyer_mobile='456', buyer_email='<EMAIL>',
            buyer_ecc_no='E', buyer_tin_cst_no='T', buyer_fax='F', buyer_tin_vat_no='V',
            consignee_name='List Cons', consignee_address='Address', consignee_country=self.country_in,
            consignee_state=self.state_mh, consignee_city=self.city_mumbai, consignee_contact_person='Contact',
            consignee_phone='789', consignee_mobile='012', consignee_email='<EMAIL>',
            consignee_ecc_no='EC', consignee_tin_cst_no='TC', consignee_fax='FC', consignee_tin_vat_no='VC',
            add_type=0, add_amount=Decimal('0'), deduction_type=0, deduction_amount=Decimal('0'),
            po_id=self.po.po_id
        )

        response = self.client.get(reverse('proforma_invoice_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'invoicing/proforma_invoice/list.html')
        self.assertContains(response, 'Proforma Invoices')
        self.assertContains(response, 'Add New Proforma Invoice')

    def test_proforma_invoice_table_partial_view(self):
        # Create a dummy invoice for listing
        ProformaInvoice.objects.create(
            id=4, sys_date=date(2023, 1, 1), sys_time='10:00:00',
            company=self.company, financial_year=self.fin_year, session_id='testuser',
            invoice_no='0005', po_no='PO-TABLE', work_order_no='WO-TABLE', invoice_mode=self.invoice_mode,
            issue_date=date(2023, 1, 1), customer_code='CUST001',
            buyer_name='Table Buyer', buyer_address='Address', buyer_country=self.country_in,
            buyer_state=self.state_mh, buyer_city=self.city_mumbai, buyer_contact_person='Contact',
            buyer_phone='123', buyer_mobile='456', buyer_email='<EMAIL>',
            buyer_ecc_no='E', buyer_tin_cst_no='T', buyer_fax='F', buyer_tin_vat_no='V',
            consignee_name='Table Cons', consignee_address='Address', consignee_country=self.country_in,
            consignee_state=self.state_mh, consignee_city=self.city_mumbai, consignee_contact_person='Contact',
            consignee_phone='789', consignee_mobile='012', consignee_email='<EMAIL>',
            consignee_ecc_no='EC', consignee_tin_cst_no='TC', consignee_fax='FC', consignee_tin_vat_no='VC',
            add_type=0, add_amount=Decimal('0'), deduction_type=0, deduction_amount=Decimal('0'),
            po_id=self.po.po_id
        )
        response = self.client.get(reverse('proforma_invoice_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'invoicing/proforma_invoice/_proforma_invoice_table.html')
        self.assertContains(response, 'Table Buyer')
        self.assertContains(response, 'PO-TABLE')
        self.assertContains(response, '<table id="proformaInvoiceTable"')
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
HTMX and Alpine.js are strategically used to replicate and enhance the dynamic behavior observed in the ASP.NET application without resorting to traditional JavaScript frameworks or full page reloads.

*   **Tabbed Interface:**
    *   **HTMX:** Tab buttons (e.g., "Buyer", "Consignee") trigger a change in an Alpine.js `activeTab` variable. The content for each tab is conditionally shown (`x-show="activeTab === 1"`). For complex tabs that require server-side data refreshing, each tab could issue an `hx-get` to a dedicated partial view endpoint, dynamically swapping content. For simplicity in this `New` form, Alpine.js handles visual tab switching, and `hx-post` for form submission occurs on the overall form.
*   **Cascading Dropdowns (Country/State/City):**
    *   **HTMX:** The `onchange` event of the Country dropdown triggers an `hx-get` request to `/invoicing/get-states/`. The `hx-target` is the State dropdown, and `hx-swap="outerHTML"` replaces its options. Similarly, the State dropdown triggers an `hx-get` for Cities. This ensures real-time updates of dependent dropdowns without full page reloads.
*   **Autocomplete for Customer Search:**
    *   **HTMX:** The `buyer_name` and `consignee_name` text inputs have `hx-get` attributes to `/invoicing/customer-autocomplete/` triggered on `keyup changed delay:500ms`. The `hx-target` is a `div` below the input (`#buyer-autocomplete-results` or `#consignee-autocomplete-results`) which displays the suggestions. When a suggestion is selected (or the user types and leaves the field), another `hx-post` to `/invoicing/customer-search-and-populate/` loads and replaces the relevant buyer/consignee form fields section.
*   **Copy Buyer to Consignee:**
    *   **HTMX:** The "Copy from buyer" button triggers an `hx-post` to `/invoicing/copy-buyer-to-consignee/`. `hx-target` is the consignee form fields container, and `hx-include` ensures all current buyer form data is sent with the request. The server-side view uses this data to populate the consignee fields and sends back the updated HTML fragment for the consignee section.
*   **Goods Grid (DataTables & Dynamic Fields):**
    *   **DataTables:** The `_proforma_invoice_table.html` partial uses DataTables for client-side search, sort, and pagination.
    *   **HTMX:** The main `list.html` page uses `hx-trigger="load, refreshProformaInvoiceList from:body"` and `hx-get="{% url 'proforma_invoice_table' %}"` to load the DataTables content dynamically. After a successful form submission (`ProformaInvoiceCreateView`), an `HX-Trigger: refreshProformaInvoiceList` header is sent to the client, telling the `list.html` page to re-fetch and update its table.
    *   **Alpine.js for Checkbox Interaction:** The JavaScript in `form.html` (within `DOMContentLoaded`) listens for changes on the "is_selected" checkbox (`input[type="checkbox"][name$="-is_selected"]`). Alpine.js or vanilla JS handles enabling/disabling the `requested_qty` and `amount_in_percentage` fields based on the checkbox state, mirroring the ASP.NET `GetValidate` logic.
*   **Form Submission:**
    *   **HTMX:** The main form uses `hx-post` to submit data to the `ProformaInvoiceCreateView`. `hx-swap="outerHTML"` and `hx-target="#proforma-invoice-form"` means the entire form can be replaced (e.g., to show validation errors inline). For a successful submission, the view returns `status=204` (No Content) with an `HX-Redirect` header to the list page, providing a clean user experience. Django's `messages` framework is used for success/error notifications.

### Final Notes

This comprehensive plan details the migration from the ASP.NET Proforma Invoice Details page to a modern Django application. By following the "Fat Model, Thin View" paradigm, leveraging HTMX and Alpine.js for dynamic interfaces, and focusing on automated, systematic conversion steps, the organization can achieve a highly maintainable, scalable, and user-friendly ERP module. The provided code snippets are runnable and adhere to the specified architectural and stylistic guidelines, ensuring a clean and efficient codebase.