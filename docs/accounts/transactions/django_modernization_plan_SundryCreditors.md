## ASP.NET to Django Conversion Script: Sundry Creditors Module

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

This modernization plan outlines the transition of the ASP.NET "Sundry Creditors" module to a robust, maintainable, and modern Django application. By leveraging AI-assisted automation, we can systematically transform the legacy codebase into a Django 5.0+ solution, focusing on business value and future scalability. The core principle is to move complex financial calculations and data aggregation from the code-behind to Django's "fat models," ensuring "thin views" that primarily handle request/response flow. Frontend interactivity will be handled exclusively by HTMX and Alpine.js, providing a dynamic user experience without complex JavaScript frameworks.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**

From the `SqlDataSource1` in the ASPX file, we observe the `SelectCommand="SELECT Category FROM AccHead WHERE Id!=0 Group By Category Order by Category DESC"`. This clearly indicates:
- **Table Name:** `AccHead`
- **Columns Identified:** `Category`, `Id` (inferred from `WHERE Id!=0`).

The `fun.FillGrid_Creditors` function, although its source isn't provided, takes `CompId`, `FinYearId`, and a `mode` parameter, along with `Category`. This implies that `AccHead` is likely related to other tables (e.g., transactions, companies, financial years) where the actual debit/credit values are stored. For the purpose of this conversion, we will define the `AccHead` model and then implement placeholder methods for the financial calculations. In a real-world scenario, these methods would query the respective transaction tables.

**Extracted Information:**
- **[TABLE_NAME]:** `AccHead`
- **[COLUMN1]:** `Id` (Primary Key, integer)
- **[COLUMN2]:** `Category` (String/Text)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**

Analysis of the ASP.NET code reveals the following primary functionalities:

*   **Read:**
    *   The `GridView1` is populated from `SqlDataSource1` which executes a `SELECT Category FROM AccHead`.
    *   The `Page_Load` method then iterates through these categories and calls `fun.FillGrid_Creditors` to dynamically calculate and display "Debit" and "Credit" totals for each `Category`.
    *   It also calculates and displays "Opening Bal.", "Grand Total" for Debit/Credit, and "Closing Bal." in the GridView's footer.
    *   **No direct Create, Update, or Delete operations are present on this specific page.** The page is primarily for viewing aggregated financial data.
*   **Navigation / Drill-down:**
    *   Clicking on a `Category` (`lblCategory` `LinkButton`) triggers `GridView1_RowCommand` with `CommandName="gotoPage"`, redirecting to `SundryCreditors_Details.aspx` with the selected category. This indicates a drill-down to a detail page for a specific creditor category.
    *   The "Cancel" button redirects to `CurrentLiabilities.aspx`.

**Summary of Operations:**
- **Read:** Displaying a list of categories with calculated summary financial figures (debit, credit, opening, closing balances).
- **Navigation:** Redirecting to a category-specific detail page or a parent liabilities page.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

The following ASP.NET controls and their roles have been identified:

*   **`asp:GridView` (ID: `GridView1`):** This is the central data display component.
    *   **Role:** Presents a tabular list of "Sundry Creditors" by category.
    *   **Features:** Displays `Category` as a clickable link, and calculated `Debit` and `Credit` amounts. Includes a footer for aggregate totals (Opening Bal., Grand Total, Closing Bal.). It supports pagination (`PageSize="20"`) and indicates a data table theme (`yui-datatable-theme`).
    *   **Django Mapping:** Will be replaced by an HTML `<table>` element integrated with DataTables for client-side functionality, and populated via a Django ListView. HTMX will be used to load/refresh the table content dynamically.
*   **`asp:LinkButton` (ID: `lblCategory`):**
    *   **Role:** Makes each `Category` entry clickable, leading to a detail page.
    *   **Django Mapping:** An HTML `<a>` tag or `hx-get` button within the table, linking to the `sundrycreditors_details` URL with the category name.
*   **`asp:Label` (IDs: `lblDebit`, `lblCredit`, `OpTotal`, `CrTotal`, `DrTotal`, `Clbal`):**
    *   **Role:** Displays calculated numeric values.
    *   **Django Mapping:** Directly rendered within Django templates using `{{ object.field }}` or `{{ calculated_value }}`.
*   **`asp:Button` (ID: `btnCancel`):**
    *   **Role:** Provides a navigation action to return to a parent page.
    *   **Django Mapping:** A standard HTML `<a>` tag styled as a button, or a `hx-get` button for potential HTMX navigation.

**UI Interactions:**
- Client-side data table features (sorting, filtering, pagination) are implied by `yui-datatable-theme` and `PageSize`. This will be handled by DataTables.
- Navigation via button clicks (`btnCancel`) and link clicks (`lblCategory`).

### Step 4: Generate Django Code

We will create a new Django application named `accounts` to house this module.

#### 4.1 Models (`accounts/models.py`)

**Task:** Create a Django model based on the database schema and encapsulate business logic.

**Instructions:**

The `AccHead` model will map directly to the existing `AccHead` table. Crucially, the complex financial calculations performed by `fun.FillGrid_Creditors` will be encapsulated as methods within this model or a custom manager. For demonstration purposes, placeholder values are used for these methods; in a real ERP, they would involve querying related transaction tables.

```python
from django.db import models
from django.db.models import Sum # Potentially useful for real calculations

class AccHead(models.Model):
    """
    Represents an Account Head (e.g., Sundry Creditors, Debtors, etc.)
    Maps to the existing AccHead table.
    """
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming Id is the primary key
    category = models.CharField(db_column='Category', max_length=255, unique=True) # Using CharField for Category

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'AccHead'
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'
        ordering = ['category'] # Consistent ordering as per SQL query

    def __str__(self):
        return self.category

    # --- Business Logic (replacing fun.FillGrid_Creditors and related calculations) ---
    # These methods simulate the complex financial calculations.
    # In a real ERP, these would involve querying related transaction/ledger tables
    # using CompId and FinYearId, which would likely come from user session/context.

    @classmethod
    def get_overall_opening_balance(cls, comp_id, fin_year_id):
        """
        Simulates fun.FillGrid_Creditors(CompId, FinYearId, 1, "").
        Returns the overall opening balance.
        """
        # Placeholder: In reality, query related transaction tables based on comp_id, fin_year_id
        # and specific accounting logic for opening balance.
        return 15000.00 # Example value

    def get_category_credit_total(self, comp_id, fin_year_id):
        """
        Simulates fun.FillGrid_Creditors(CompId, FinYearId, 2, category_text).
        Returns the credit total for this specific category.
        """
        # Placeholder: Query transaction tables for credit entries related to this category.
        # Example logic based on category name:
        if "XYZ Corp" in self.category:
            return 7500.00
        elif "ABC Traders" in self.category:
            return 12000.00
        return 0.00 # Default if no specific logic

    def get_category_debit_total(self, comp_id, fin_year_id):
        """
        Simulates fun.FillGrid_Creditors(CompId, FinYearId, 3, category_text) +
        fun.FillGrid_Creditors(CompId, FinYearId, 5, category_text).
        Returns the debit total for this specific category.
        """
        # Placeholder: Query transaction tables for debit entries related to this category.
        # Example logic based on category name:
        if "XYZ Corp" in self.category:
            return 3000.00
        elif "ABC Traders" in self.category:
            return 5000.00
        return 0.00 # Default if no specific logic

    @classmethod
    def get_overall_debit_total(cls, comp_id, fin_year_id):
        """
        Simulates the total debit calculation across all categories.
        fun.FillGrid_Creditors(CompId, FinYearId, 3, "") + fun.FillGrid_Creditors(CompId, FinYearId, 5, "")
        """
        # Placeholder: Sum of all category debit totals or direct query.
        return 8000.00 # Example sum

    @classmethod
    def calculate_grand_totals(cls, comp_id, fin_year_id):
        """
        Calculates all grand totals required for the footer.
        This consolidates the logic from the Page_Load footer calculations.
        """
        # Fetch all categories to sum up individual category credits/debits
        categories = cls.objects.all()
        
        # Initialize grand totals
        ah_cr_grand_total = 0.0
        ah_dr_grand_total = 0.0

        for category in categories:
            ah_cr_total = category.get_category_credit_total(comp_id, fin_year_id)
            ah_dr_total = category.get_category_debit_total(comp_id, fin_year_id)
            ah_cr_grand_total += ah_cr_total
            ah_dr_grand_total += ah_dr_total
            
        op_total = cls.get_overall_opening_balance(comp_id, fin_year_id)
        
        # The DrTotal in the ASP.NET footer is the overall debit total
        dr_total_footer = cls.get_overall_debit_total(comp_id, fin_year_id)

        # Clbal (Closing Balance) calculation
        clbal = (op_total + ah_cr_grand_total) - dr_total_footer
        
        return {
            'op_total': op_total,
            'cr_grand_total': ah_cr_grand_total,
            'dr_grand_total': dr_total_footer,
            'clbal': clbal
        }

```

#### 4.2 Forms (`accounts/forms.py`)

**Task:** Define a Django form for user input.

**Instructions:**

For this specific "Sundry Creditors" list page, there are no direct user inputs to create or update `AccHead` records or sundry creditor entries. Forms would typically be used for `SundryCreditors_Details.aspx`. Since this page is a display-only summary, a form is not required here.

```python
# accounts/forms.py (No forms needed for this display-only view)
# This file can be empty or contain forms for other related modules if they exist.
```

#### 4.3 Views (`accounts/views.py`)

**Task:** Implement CRUD operations using CBVs.

**Instructions:**

We will implement a `ListView` for the main "Sundry Creditors" display, and a partial view for the DataTables content. The redirection views for "Cancel" and "gotoPage" will also be included as simple `RedirectView` or function-based views. `CompId` and `FinYearId` are hardcoded for runnable example purposes; in a real app, they would come from `request.session` or user profile.

```python
from django.views.generic import ListView, RedirectView, View
from django.urls import reverse_lazy, reverse
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import render
from .models import AccHead

# Dummy values for CompId and FinYearId, would come from session in real app
DUMMY_COMP_ID = 1
DUMMY_FIN_YEAR_ID = 1

class SundryCreditorsListView(ListView):
    """
    Displays a list of AccHead categories for Sundry Creditors
    with calculated debit/credit totals and grand totals in the footer.
    """
    model = AccHead
    template_name = 'accounts/sundrycreditors/list.html'
    context_object_name = 'accheads'

    # The view itself doesn't need to do complex calculations,
    # it primarily sets up the template and context for HTMX to load the table.
    # The table loading is deferred to SundryCreditorsTablePartialView.

    def get_queryset(self):
        # The main list view just needs to know about the categories.
        # The actual calculations for debit/credit are done in the partial view.
        return AccHead.objects.all().order_by('category') # Matches SQL Order by Category DESC (for general data)

class SundryCreditorsTablePartialView(ListView):
    """
    Renders only the table content for Sundry Creditors, designed for HTMX.
    This view performs all the data aggregation and calculations.
    """
    model = AccHead
    template_name = 'accounts/sundrycreditors/_sundrycreditors_table.html'
    context_object_name = 'accheads'

    def get_queryset(self):
        # This queryset is for the main rows of the table
        return AccHead.objects.all().order_by('category')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Simulate session values for CompId and FinYearId
        # In a real app: comp_id = self.request.session.get('compid')
        #               fin_year_id = self.request.session.get('finyear')
        comp_id = DUMMY_COMP_ID
        fin_year_id = DUMMY_FIN_YEAR_ID

        # Prepare data for each row with calculated debit/credit
        accheads_with_details = []
        for acchead in context['accheads']:
            credit_total = acchead.get_category_credit_total(comp_id, fin_year_id)
            debit_total = acchead.get_category_debit_total(comp_id, fin_year_id)
            accheads_with_details.append({
                'object': acchead,
                'category': acchead.category,
                'credit': f"{credit_total:,.2f}", # Format for display
                'debit': f"{debit_total:,.2f}",   # Format for display
            })
        context['accheads_with_details'] = accheads_with_details

        # Calculate footer grand totals using the AccHead model's class method
        grand_totals = AccHead.calculate_grand_totals(comp_id, fin_year_id)
        
        context['op_total'] = f"{grand_totals['op_total']:,.2f}"
        context['cr_grand_total'] = f"{grand_totals['cr_grand_total']:,.2f}"
        context['dr_grand_total'] = f"{grand_totals['dr_grand_total']:,.2f}"
        context['clbal'] = f"{grand_totals['clbal']:,.2f}"
        
        return context

class SundryCreditorsDetailsRedirectView(View):
    """
    Simulates the redirect to SundryCreditors_Details.aspx from GridView_RowCommand.
    """
    def get(self, request, *args, **kwargs):
        category = kwargs.get('category_name')
        # In a real app, this would redirect to a specific detail page URL
        # For demonstration, we'll just show a message.
        messages.info(request, f"Redirecting to Sundry Creditors Details for: {category}")
        return HttpResponseRedirect(reverse('accounts:dummy_details_page', args=[category]))

class CurrentLiabilitiesRedirectView(RedirectView):
    """
    Simulates the redirect from btnCancel_Click to CurrentLiabilities.aspx.
    """
    url = reverse_lazy('accounts:current_liabilities_page') # Dummy URL for redirection
    
    def get_redirect_url(self, *args, **kwargs):
        messages.info(self.request, "Redirecting to Current Liabilities page.")
        return super().get_redirect_url(*args, **kwargs)

# Dummy views for redirection targets
def dummy_details_page(request, category_name):
    return HttpResponse(f"<h1>Sundry Creditors Details Page</h1><p>Category: {category_name}</p><p>This is a placeholder for the actual detail page.</p><a href='{reverse('accounts:sundrycreditors_list')}' class='bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded'>Back to List</a>")

def current_liabilities_page(request):
    return HttpResponse(f"<h1>Current Liabilities Page</h1><p>This is a placeholder for the actual Current Liabilities page.</p><a href='{reverse('accounts:sundrycreditors_list')}' class='bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded'>Back to Sundry Creditors</a>")

```

#### 4.4 Templates (`accounts/templates/accounts/sundrycreditors/`)

**Task:** Create templates for each view.

**Instructions:**

We will create `list.html` for the main page and `_sundrycreditors_table.html` as an HTMX partial for the table content.

**`accounts/templates/accounts/sundrycreditors/list.html`**
This template serves as the main page for Sundry Creditors. It uses HTMX to load the actual table content, allowing for dynamic updates.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Balance Sheet: Current Liabilities: Sundry Creditors</h2>
        <!-- No "Add New" button here as per original ASP.NET page, 
             but if needed, it would be placed here with hx-get for a modal form -->
    </div>
    
    <div id="sundrycreditors-table-container"
         hx-trigger="load, refreshSundryCreditorsList from:body"
         hx-get="{% url 'accounts:sundrycreditors_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Sundry Creditors data...</p>
        </div>
    </div>
    
    <div class="mt-6 flex justify-center">
        <a href="{% url 'accounts:current_liabilities_page' %}" 
           class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-6 rounded-lg shadow-md transition duration-300 ease-in-out">
            Cancel
        </a>
    </div>

    <!-- Modal for future forms (e.g., if a detail page allows adding/editing) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Include jQuery and DataTables JS from CDN in base.html -->
<script>
    // Alpine.js component initialization if needed for general UI state
    document.addEventListener('alpine:init', () => {
        // e.g., Alpine.data('modal', () => ({ show: false }));
    });
</script>
{% endblock %}
```

**`accounts/templates/accounts/sundrycreditors/_sundrycreditors_table.html`**
This partial template contains the actual DataTables structure and is loaded dynamically by HTMX.

```html
<div class="overflow-x-auto shadow-md rounded-lg">
    <table id="sundryCreditorsTable" class="min-w-full bg-white border-collapse">
        <thead>
            <tr class="bg-gray-100 border-b border-gray-200 text-gray-600 uppercase text-sm leading-normal">
                <th class="py-3 px-6 text-left">SN</th>
                <th class="py-3 px-6 text-left">Particulars</th>
                <th class="py-3 px-6 text-right">Debit</th>
                <th class="py-3 px-6 text-right">Credit</th>
            </tr>
        </thead>
        <tbody class="text-gray-700 text-sm font-light">
            {% for item in accheads_with_details %}
            <tr class="border-b border-gray-200 hover:bg-gray-50">
                <td class="py-3 px-6 text-left whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-3 px-6 text-left">
                    <a href="{% url 'accounts:sundrycreditors_details' category_name=item.category %}" 
                       class="text-blue-600 hover:text-blue-800 hover:underline font-medium">
                        {{ item.category }}
                    </a>
                </td>
                <td class="py-3 px-6 text-right">{{ item.debit }}</td>
                <td class="py-3 px-6 text-right">{{ item.credit }}</td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot>
            <tr class="font-bold bg-gray-100 border-t-2 border-gray-300">
                <td class="py-3 px-6 text-right" colspan="2">Opening Bal.</td>
                <td class="py-3 px-6 text-right">&nbsp;</td> {# Debit column is empty for Opening Bal #}
                <td class="py-3 px-6 text-right">{{ op_total }}</td>
            </tr>
            <tr class="font-bold bg-gray-100 border-t border-gray-200">
                <td class="py-3 px-6 text-right" colspan="2">Grand Total</td>
                <td class="py-3 px-6 text-right">{{ dr_grand_total }}</td>
                <td class="py-3 px-6 text-right">{{ cr_grand_total }}</td>
            </tr>
            <tr class="font-bold bg-gray-100 border-t border-gray-200">
                <td class="py-3 px-6 text-right" colspan="2">Closing Bal.</td>
                <td class="py-3 px-6 text-right">&nbsp;</td> {# Debit column is empty for Closing Bal #}
                <td class="py-3 px-6 text-right">{{ clbal }}</td>
            </tr>
        </tfoot>
    </table>
</div>

<script>
    $(document).ready(function() {
        $('#sundryCreditorsTable').DataTable({
            "paging": true,      // Enable pagination
            "searching": true,   // Enable search box
            "ordering": true,    // Enable column ordering
            "info": true,        // Show "Showing X to Y of Z entries"
            "pageLength": 20,    // Default page size as per ASP.NET GridView
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]], // Options for page size
            "columnDefs": [
                { "orderable": false, "targets": 0 }, // SN column not orderable
                { "orderable": false, "targets": [2, 3] } // Debit and Credit columns not orderable for summary
            ]
        });
    });
</script>
```

#### 4.5 URLs (`accounts/urls.py`)

**Task:** Define URL patterns for the views.

**Instructions:**

We will create URL patterns for the list view, the HTMX partial table, and the redirection targets.

```python
from django.urls import path
from .views import (
    SundryCreditorsListView, 
    SundryCreditorsTablePartialView,
    SundryCreditorsDetailsRedirectView,
    CurrentLiabilitiesRedirectView,
    dummy_details_page, # Placeholder
    current_liabilities_page # Placeholder
)

app_name = 'accounts' # Namespace for URLs

urlpatterns = [
    # Main Sundry Creditors List page
    path('sundrycreditors/', SundryCreditorsListView.as_view(), name='sundrycreditors_list'),
    
    # HTMX endpoint to load/refresh the table content
    path('sundrycreditors/table/', SundryCreditorsTablePartialView.as_view(), name='sundrycreditors_table'),
    
    # Redirect for "gotoPage" command on category link
    path('sundrycreditors/details/<str:category_name>/', SundryCreditorsDetailsRedirectView.as_view(), name='sundrycreditors_details'),
    
    # Redirect for "Cancel" button
    path('currentliabilities/', CurrentLiabilitiesRedirectView.as_view(), name='current_liabilities_page'),

    # Dummy target pages for redirects (replace with real views in actual app)
    path('dummy-details/<str:category_name>/', dummy_details_page, name='dummy_details_page'),
    path('dummy-current-liabilities/', current_liabilities_page, name='dummy_current_liabilities_page'),
]

```

#### 4.6 Tests (`accounts/tests.py`)

**Task:** Write tests for the model and views.

**Instructions:**

Comprehensive unit tests will cover the `AccHead` model's structure and its financial calculation methods. Integration tests will ensure views render correctly, handle HTMX requests, and redirect appropriately.

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from .models import AccHead
from .views import DUMMY_COMP_ID, DUMMY_FIN_YEAR_ID # Access dummy IDs from views

class AccHeadModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.acchead1 = AccHead.objects.create(id=1, category='ABC Traders')
        cls.acchead2 = AccHead.objects.create(id=2, category='XYZ Corp')
        cls.acchead3 = AccHead.objects.create(id=3, category='PQR Ltd')
  
    def test_acchead_creation(self):
        obj = AccHead.objects.get(id=1)
        self.assertEqual(obj.category, 'ABC Traders')
        self.assertEqual(str(obj), 'ABC Traders')
        
    def test_category_label(self):
        obj = AccHead.objects.get(id=1)
        field_label = obj._meta.get_field('category').verbose_name
        self.assertEqual(field_label, 'Category')
        
    def test_managed_false(self):
        self.assertFalse(AccHead._meta.managed)
        self.assertEqual(AccHead._meta.db_table, 'AccHead')

    # Test business logic methods (simulated financial calculations)
    def test_get_overall_opening_balance(self):
        # Mock the underlying data if needed for a real scenario
        opening_bal = AccHead.get_overall_opening_balance(DUMMY_COMP_ID, DUMMY_FIN_YEAR_ID)
        self.assertEqual(opening_bal, 15000.00) # Based on placeholder in model

    def test_get_category_credit_total(self):
        self.assertEqual(self.acchead1.get_category_credit_total(DUMMY_COMP_ID, DUMMY_FIN_YEAR_ID), 12000.00)
        self.assertEqual(self.acchead2.get_category_credit_total(DUMMY_COMP_ID, DUMMY_FIN_YEAR_ID), 7500.00)
        self.assertEqual(self.acchead3.get_category_credit_total(DUMMY_COMP_ID, DUMMY_FIN_YEAR_ID), 0.00) # Default case

    def test_get_category_debit_total(self):
        self.assertEqual(self.acchead1.get_category_debit_total(DUMMY_COMP_ID, DUMMY_FIN_YEAR_ID), 5000.00)
        self.assertEqual(self.acchead2.get_category_debit_total(DUMMY_COMP_ID, DUMMY_FIN_YEAR_ID), 3000.00)
        self.assertEqual(self.acchead3.get_category_debit_total(DUMMY_COMP_ID, DUMMY_FIN_YEAR_ID), 0.00) # Default case
    
    def test_get_overall_debit_total(self):
        overall_debit = AccHead.get_overall_debit_total(DUMMY_COMP_ID, DUMMY_FIN_YEAR_ID)
        self.assertEqual(overall_debit, 8000.00) # Based on placeholder in model

    def test_calculate_grand_totals(self):
        # Mocking the individual category methods for predictable results
        with patch.object(AccHead, 'get_category_credit_total', side_effect=[12000.00, 7500.00, 0.00]):
            with patch.object(AccHead, 'get_category_debit_total', side_effect=[5000.00, 3000.00, 0.00]):
                with patch.object(AccHead, 'get_overall_opening_balance', return_value=15000.00):
                    with patch.object(AccHead, 'get_overall_debit_total', return_value=8000.00):
                        grand_totals = AccHead.calculate_grand_totals(DUMMY_COMP_ID, DUMMY_FIN_YEAR_ID)
                        self.assertAlmostEqual(grand_totals['op_total'], 15000.00)
                        self.assertAlmostEqual(grand_totals['cr_grand_total'], 19500.00) # 12000 + 7500 + 0
                        self.assertAlmostEqual(grand_totals['dr_grand_total'], 8000.00)
                        self.assertAlmostEqual(grand_totals['clbal'], (15000 + 19500) - 8000) # 26500.00

class SundryCreditorsViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        AccHead.objects.create(id=1, category='ABC Traders')
        AccHead.objects.create(id=2, category='XYZ Corp')
    
    def setUp(self):
        self.client = Client()
    
    @patch('accounts.models.AccHead.get_overall_opening_balance', return_value=15000.00)
    @patch('accounts.models.AccHead.get_category_credit_total', side_effect=[12000.00, 7500.00])
    @patch('accounts.models.AccHead.get_category_debit_total', side_effect=[5000.00, 3000.00])
    @patch('accounts.models.AccHead.get_overall_debit_total', return_value=8000.00)
    def test_list_view(self, mock_get_overall_debit_total, mock_get_category_debit_total, 
                       mock_get_category_credit_total, mock_get_overall_opening_balance):
        response = self.client.get(reverse('accounts:sundrycreditors_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/sundrycreditors/list.html')
        # The main list view itself might not have accheads in context,
        # as it defers table loading to HTMX.
        self.assertNotContains(response, '<table>') # The table is loaded via HTMX
        self.assertContains(response, 'id="sundrycreditors-table-container"')

    @patch('accounts.models.AccHead.get_overall_opening_balance', return_value=15000.00)
    @patch('accounts.models.AccHead.get_category_credit_total', side_effect=[12000.00, 7500.00])
    @patch('accounts.models.AccHead.get_category_debit_total', side_effect=[5000.00, 3000.00])
    @patch('accounts.models.AccHead.get_overall_debit_total', return_value=8000.00)
    def test_table_partial_view_htmx(self, mock_get_overall_debit_total, mock_get_category_debit_total, 
                                      mock_get_category_credit_total, mock_get_overall_opening_balance):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('accounts:sundrycreditors_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/sundrycreditors/_sundrycreditors_table.html')
        self.assertContains(response, '<table id="sundryCreditorsTable"')
        self.assertContains(response, 'ABC Traders')
        self.assertContains(response, 'XYZ Corp')
        self.assertContains(response, '12,000.00') # Formatted credit for ABC Traders
        self.assertContains(response, '5,000.00')  # Formatted debit for ABC Traders
        self.assertContains(response, 'Opening Bal.')
        self.assertContains(response, '15,000.00') # Formatted opening balance
        self.assertContains(response, 'Closing Bal.')
        # Calculated closing balance: (15000 + (12000 + 7500)) - 8000 = 26500
        self.assertContains(response, '26,500.00') 


    def test_sundry_creditors_details_redirect_view(self):
        response = self.client.get(reverse('accounts:sundrycreditors_details', args=['ABC Traders']))
        self.assertEqual(response.status_code, 302) # Expect redirect
        self.assertRedirects(response, reverse('accounts:dummy_details_page', args=['ABC Traders']))
        messages = list(response.wsgi_request._messages)
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Redirecting to Sundry Creditors Details for: ABC Traders")

    def test_current_liabilities_redirect_view(self):
        response = self.client.get(reverse('accounts:current_liabilities_page'))
        self.assertEqual(response.status_code, 302) # Expect redirect
        self.assertRedirects(response, reverse('accounts:dummy_current_liabilities_page'))
        messages = list(response.wsgi_request._messages)
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Redirecting to Current Liabilities page.")

    def test_dummy_details_page(self):
        response = self.client.get(reverse('accounts:dummy_details_page', args=['TestCat']))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'TestCat')

    def test_dummy_current_liabilities_page(self):
        response = self.client.get(reverse('accounts:dummy_current_liabilities_page'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Current Liabilities Page')

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

The provided templates and views already integrate HTMX and Alpine.js following the recommended patterns.

*   **HTMX for dynamic updates:**
    *   The `sundrycreditors/list.html` uses `hx-get="{% url 'accounts:sundrycreditors_table' %}"` and `hx-trigger="load, refreshSundryCreditorsList from:body"` on a `div` element. This tells HTMX to load the content of `_sundrycreditors_table.html` into this `div` on page load and whenever a `refreshSundryCreditorsList` custom event is triggered (e.g., after a CRUD operation in a future related module, though not directly applicable to this read-only page).
    *   The category links within `_sundrycreditors_table.html` are standard `<a>` tags for navigation to detail pages. If those detail pages were also HTMX-driven partials, the links would use `hx-get`.
    *   The "Cancel" button is a simple `<a>` tag, as it performs a full page redirect in the original ASP.NET application. If a soft navigation was desired, `hx-get` could be applied here.
*   **Alpine.js for UI state management:**
    *   A generic `modal` div is included in `list.html` with Alpine.js `x-data` and `x-on:click.outside` attributes for handling modal visibility. While this specific "Sundry Creditors" list doesn't have modals, this structure is prepared for future "Add/Edit" forms for individual creditor entries (e.g., on the `SundryCreditors_Details` page). The `_` (hyperscript) syntax is used to easily toggle classes for modal display (`on click add .is-active to #modal`).
*   **DataTables for list views:**
    *   The `_sundrycreditors_table.html` includes a `<script>` block that initializes jQuery DataTables on the `sundryCreditorsTable` ID. This provides client-side searching, sorting, and pagination without additional backend logic, mirroring the `yui-datatable-theme` functionality.
    *   The `pageLength` and `lengthMenu` options are set to match the original ASP.NET `PageSize="20"`.
*   **DRY Template Inheritance:** `list.html` correctly extends `core/base.html`, ensuring all CDN links for HTMX, Alpine.js, jQuery, and DataTables are loaded once in the base template, as per the guidelines.

### Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating the "Sundry Creditors" ASP.NET module to Django.

*   **Business Value:** By migrating to Django, the organization gains a modern, open-source, and highly maintainable application. The "fat model, thin view" approach improves code organization and testability. HTMX and Alpine.js offer a dynamic user experience with minimal complexity, reducing frontend development costs. DataTables provides robust data presentation capabilities.
*   **Automation Focus:** This plan is designed to be executed with AI-assisted automation in mind. Each step is broken down into specific tasks (e.g., "Identify table name," "Create Django model") that can be processed by an AI, leading to the generation of the Django code files. The structured output (e.g., clear Python and HTML blocks) is ideal for automated code generation.
*   **Maintainability & Scalability:** The strict adherence to Django best practices, separation of concerns, and comprehensive testing ensures that the new system will be easier to maintain, debug, and scale in the long run compared to the legacy ASP.NET application.
*   **Next Steps:** The logical next step would be to apply a similar analysis and modernization plan to the `SundryCreditors_Details.aspx` and `CurrentLiabilities.aspx` pages, building out the full functionality of the Accounts module. This would involve identifying the underlying CRUD operations for individual creditor entries and designing forms, views, and templates accordingly.