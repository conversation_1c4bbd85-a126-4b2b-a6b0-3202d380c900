Here's a comprehensive Django modernization plan for the provided ASP.NET application, following all specified guidelines for AI-assisted automation, fat models, thin views, HTMX, Alpine.js, and DataTables.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:** The ASP.NET code interacts with `tblACC_BankVoucher_Payment_Master`, `tblACC_BankVoucher_Payment_Details`, and `tblACC_PaidType`. It also references `fun.ECSNames` and `fun.ECSAddress` which imply lookup data from related tables (e.g., `Employee`, `Customer`, `Supplier`). For this conversion, we will focus on the explicitly queried tables and represent the lookup logic within the `BankVoucherPaymentMaster` model.

**Instructions (for AI automation):**
- Identify `tblACC_BankVoucher_Payment_Master` as the primary table for the `BankVoucherPaymentMaster` model.
- Identify `tblACC_BankVoucher_Payment_Details` as a related detail table for `BankVoucherPaymentDetail` model.
- Identify `tblACC_PaidType` as a lookup table for `PaidType` model.
- Map columns based on usage (`Id`, `CompId`, `FinYearId`, `NameOnCheque`, `PaidType`, `ECSType`, `PayTo`, `ChequeDate`, `Bank`, `PayAmt`, `AddAmt`, `BVPNo`, `ChequeNo`, `SysDate`, `Amount`, `Particulars`).
- Infer data types (e.g., `int` for IDs, `varchar` for strings, `datetime` for dates, `float` for amounts).

**Inferred Schema:**
*   **tblACC_BankVoucher_Payment_Master**:
    *   `Id` (PK, int)
    *   `CompId` (int)
    *   `FinYearId` (int)
    *   `NameOnCheque` (string)
    *   `PaidType` (string - can be integer ID or text)
    *   `ECSType` (int)
    *   `PayTo` (string)
    *   `ChequeDate` (datetime)
    *   `Bank` (int)
    *   `PayAmt` (float)
    *   `AddAmt` (float)
    *   `BVPNo` (string)
    *   `ChequeNo` (string)
    *   `SysDate` (datetime)
    *   `TransactionType` (int) - inferred from DataTable schema.
*   **tblACC_BankVoucher_Payment_Details**:
    *   `Id` (PK, int)
    *   `MId` (FK to `tblACC_BankVoucher_Payment_Master.Id`, int)
    *   `Amount` (float)
*   **tblACC_PaidType**:
    *   `Id` (PK, int)
    *   `Particulars` (string)

### Step 2: Identify Backend Functionality

**Analysis:** The ASP.NET page is a **read-only report/print view** for a specific bank voucher. It takes an `Id` from the query string, retrieves master and detail data, performs complex calculations and lookups (e.g., `PaidTo` name/address, date parsing, total amount aggregation), and then renders this processed data. It does not perform direct Create, Update, or Delete operations on the `BankVoucherPaymentMaster` record itself, but merely displays its information.

**Instructions (for AI automation):**
- Identify the primary operation as "Read/Display Report" for a single record.
- Note the dependency on an `Id` parameter.
- Extract all data retrieval logic (SQL queries) and data processing/transformation logic (date formatting, conditional lookups, sum aggregations) from the C# code-behind. This complex logic must be encapsulated within Django models.
- The "Cancel" button is a simple redirect.

### Step 3: Infer UI Components

**Analysis:** The ASP.NET page primarily uses a `CR:CrystalReportViewer` control to embed a Crystal Report. It also has a `Button` for "Cancel". The report content itself is generated by Crystal Reports based on the data provided.

**Instructions (for AI automation):**
- Recognize `CR:CrystalReportViewer` as a third-party reporting tool. Its functionality will be replaced by direct HTML rendering in Django templates, driven by processed data from Django models.
- The `asp:Button` will be replaced by a standard HTML button or anchor tag for navigation.
- The `Panel` with `ScrollBars="Auto"` suggests content that may exceed screen height, requiring scrolling, which will be handled by standard CSS in Django.
- The multiple `<asp:Content>` tags indicate a master page setup, which maps to Django's `{% extends 'core/base.html' %}`.

### Step 4: Generate Django Code

We will create a Django app named `accounts` to house these components.

#### 4.1 Models

The complex data processing logic will be moved into methods within the `BankVoucherPaymentMaster` model.

```python
# accounts/models.py
from django.db import models
from django.db.models import Sum, F
from datetime import datetime
import re

class PaidType(models.Model):
    # This maps to tblACC_PaidType
    # Primary key should explicitly match the DB column if it's not 'id'
    id = models.IntegerField(db_column='Id', primary_key=True)
    particulars = models.CharField(db_column='Particulars', max_length=255, blank=True, null=True)

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblACC_PaidType'
        verbose_name = 'Paid Type'
        verbose_name_plural = 'Paid Types'

    def __str__(self):
        return self.particulars or f"PaidType {self.id}"


class BankVoucherPaymentMaster(models.Model):
    # This maps to tblACC_BankVoucher_Payment_Master
    # Primary key should explicitly match the DB column if it's not 'id'
    id = models.IntegerField(db_column='Id', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    name_on_cheque = models.CharField(db_column='NameOnCheque', max_length=255, blank=True, null=True)
    # PaidType in C# can be int or string, so use CharField to accommodate both
    paid_type_id = models.CharField(db_column='PaidType', max_length=50, blank=True, null=True)
    ecs_type = models.IntegerField(db_column='ECSType', blank=True, null=True)
    pay_to = models.CharField(db_column='PayTo', max_length=255, blank=True, null=True)
    cheque_date = models.DateTimeField(db_column='ChequeDate', blank=True, null=True)
    bank = models.IntegerField(db_column='Bank', blank=True, null=True) # 1:Dena, 2:Dena CC, 3:Axis, 5:IDBI
    pay_amt = models.FloatField(db_column='PayAmt', blank=True, null=True)
    add_amt = models.FloatField(db_column='AddAmt', blank=True, null=True)
    bvp_no = models.CharField(db_column='BVPNo', max_length=50, blank=True, null=True)
    cheque_no = models.CharField(db_column='ChequeNo', max_length=50, blank=True, null=True)
    sys_date = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    transaction_type = models.IntegerField(db_column='TransactionType', blank=True, null=True) # From C# DataTable

    class Meta:
        managed = False
        db_table = 'tblACC_BankVoucher_Payment_Master'
        verbose_name = 'Bank Voucher Payment'
        verbose_name_plural = 'Bank Voucher Payments'

    def __str__(self):
        return f"BVP: {self.bvp_no or self.id}"

    # Business logic methods (Fat Model)
    def _get_ecs_entity_info(self, entity_type, pay_to_value, comp_id, info_type='name'):
        """
        Placeholder for fetching Employee/Customer/Supplier names/addresses.
        In a real migration, this would involve querying respective models (e.g., Employee.objects.get(...)).
        """
        if not (entity_type and pay_to_value and comp_id):
            return "N/A"
        # Example dummy logic:
        if entity_type == 1: # Employee
            return f"Employee {pay_to_value} {info_type.capitalize()}"
        elif entity_type == 2: # Customer
            return f"Customer {pay_to_value} {info_type.capitalize()}"
        elif entity_type == 3: # Supplier
            return f"Supplier {pay_to_value} {info_type.capitalize()}"
        return "N/A"

    def get_paid_to_name(self):
        """Replicates the logic for determining 'PaidTo' (Abc in C#)."""
        if self.name_on_cheque:
            return self.name_on_cheque
        
        try:
            # Try to convert PaidType to int and lookup in PaidType model
            paid_type_id_int = int(self.paid_type_id)
            paid_type_obj = PaidType.objects.get(id=paid_type_id_int)
            return paid_type_obj.particulars
        except (ValueError, PaidType.DoesNotExist):
            # Fallback to ECSNames logic if PaidType is not an int or not found
            return self._get_ecs_entity_info(self.ecs_type, self.pay_to, self.comp_id, 'name')

    def get_paid_to_address(self):
        """Replicates fun.ECSAddress logic."""
        return self._get_ecs_entity_info(self.ecs_type, self.pay_to, self.comp_id, 'address')

    def get_cheque_date_dmy_parts(self):
        """
        Replicates the DMY.Substring logic for Dena/IDBI banks to get individual date characters.
        Assumes fun.FromDateDMY creates DDMMYYYY.
        """
        if not self.cheque_date:
            return {'d1': '', 'd2': '', 'm1': '', 'm2': '', 'y1': '', 'y2': '', 'y3': '', 'y4': ''}

        dmy_str = self.cheque_date.strftime("%d%m%Y")
        
        return {
            'd1': dmy_str[0] if len(dmy_str) > 0 else '',
            'd2': dmy_str[1] if len(dmy_str) > 1 else '',
            'm1': dmy_str[2] if len(dmy_str) > 2 else '',
            'm2': dmy_str[3] if len(dmy_str) > 3 else '',
            'y1': dmy_str[4] if len(dmy_str) > 4 else '',
            'y2': dmy_str[5] if len(dmy_str) > 5 else '',
            'y3': dmy_str[6] if len(dmy_str) > 6 else '',
            'y4': dmy_str[7] if len(dmy_str) > 7 else '',
        }
    
    def get_total_amount(self):
        """Replicates the DtlsAmt + PayAmy_M + AddAmt calculation."""
        details_amount = self.bankvoucherpaymentdetail_set.aggregate(total_amount=Sum('amount'))['total_amount'] or 0.0
        
        total_amount = details_amount + (self.pay_amt or 0.0)
        
        # Add AddAmt only if PaidType was convertible to int (as per C# logic)
        try:
            int(self.paid_type_id)
            total_amount += (self.add_amt or 0.0)
        except ValueError:
            pass # PaidType not integer, AddAmt not included
        
        return total_amount

    def get_report_context_data(self):
        """
        Aggregates all data required for the report, mirroring the C# DataTable construction.
        This provides a single dictionary ready for template consumption.
        """
        context_data = {
            'PaidTo': self.get_paid_to_name(),
            'CompId': self.comp_id,
            'ChequeDate': self.cheque_date.strftime("%d/%m/%Y") if self.cheque_date else '',
            'Amount': self.get_total_amount(),
            'Address': self.get_paid_to_address(),
            'BVPNo': self.bvp_no,
            'ChequeNo': self.cheque_no,
            'SysDate': self.sys_date.strftime("%d/%m/%Y") if self.sys_date else '',
            'BillNo': '-', # C# logic for BillNo was commented out; default to '-'
            'TypeECS': '', # C# logic for TypeECS was commented out; set as blank
            'ECS': self.pay_to,
            'InvoiceNo': '-', # C# logic for InvoiceNo was commented out; default to '-'
            'Particular': '', # Not explicitly set in C# snippet
            'InvDate': '', # Not explicitly set in C# snippet
            'AddAmt': self.add_amt or 0.0,
            'TransactionType': self.transaction_type,
            'PaidTypeRaw': self.paid_type_id, # Keep raw for debugging/display if needed
            'BankId': self.bank, # To determine which "report style" to use in template
        }

        # Add Cheque Date parts for Dena/IDBI banks as per C# conditional logic
        if self.bank in [1, 2, 5]:
            context_data.update(self.get_cheque_date_dmy_parts())
        else: # For other banks (e.g., Axis Bank where ChequeDate is full string), ensure parts are empty
            context_data.update({k: '' for k in ['d1', 'd2', 'm1', 'm2', 'y1', 'y2', 'y3', 'y4']})

        return context_data


class BankVoucherPaymentDetail(models.Model):
    # This maps to tblACC_BankVoucher_Payment_Details
    id = models.IntegerField(db_column='Id', primary_key=True)
    # Define ForeignKey relationship
    mid = models.ForeignKey(BankVoucherPaymentMaster, on_delete=models.CASCADE, db_column='MId')
    amount = models.FloatField(db_column='Amount', blank=True, null=True)
    # Add other fields if they exist and are relevant, e.g.:
    # proforma_inv_no = models.CharField(db_column='ProformaInvNo', max_length=255, blank=True, null=True)
    # pvev_no = models.CharField(db_column='PVEVNO', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_BankVoucher_Payment_Details'
        verbose_name = 'Bank Voucher Payment Detail'
        verbose_name_plural = 'Bank Voucher Payment Details'

    def __str__(self):
        return f"Detail {self.id} for BVP Master {self.mid_id}: {self.amount}"

# accounts/templatetags/accounts_tags.py (Create this file and __init__.py in the directory)
from django import template
from django.db.models import Sum

register = template.Library()

@register.filter
def sum_attr(queryset, attribute):
    """
    Sums the value of a specific attribute across a queryset.
    Usage: {{ queryset|sum_attr:'amount' }}
    """
    if not queryset:
        return 0
    return queryset.aggregate(total=Sum(attribute))['total'] or 0
```

#### 4.2 Forms

A `ModelForm` for general CRUD operations on `BankVoucherPaymentMaster`. The specific `BankVoucher_Print_Details.aspx` page does not use a form, but this is provided for a complete Django application structure.

```python
# accounts/forms.py
from django import forms
from .models import BankVoucherPaymentMaster

class BankVoucherPaymentMasterForm(forms.ModelForm):
    class Meta:
        model = BankVoucherPaymentMaster
        fields = [
            'comp_id', 'fin_year_id', 'name_on_cheque', 'paid_type_id',
            'ecs_type', 'pay_to', 'cheque_date', 'bank', 'pay_amt',
            'add_amt', 'bvp_no', 'cheque_no', 'sys_date', 'transaction_type',
        ]
        widgets = {
            'comp_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'fin_year_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'name_on_cheque': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'paid_type_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'ecs_type': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'pay_to': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'cheque_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bank': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'pay_amt': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'add_amt': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bvp_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'cheque_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sys_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'transaction_type': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
```

#### 4.3 Views

We'll provide the standard CRUD views for `BankVoucherPaymentMaster` (as per the prompt's template) and then a dedicated `ReportView` which is the direct replacement for the `BankVoucher_Print_Details.aspx` page.

```python
# accounts/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import BankVoucherPaymentMaster, PaidType # Import PaidType if needed for direct lookups in views
from .forms import BankVoucherPaymentMasterForm

# --- General CRUD Views for BankVoucherPaymentMaster ---
# These are provided as per the prompt's template for a complete application structure.

class BankVoucherPaymentMasterListView(ListView):
    model = BankVoucherPaymentMaster
    template_name = 'accounts/bankvoucherpaymentmaster/list.html'
    context_object_name = 'bankvoucherpayments' # Plural lowercase for context

class BankVoucherPaymentTablePartialView(ListView):
    # This view is specifically for HTMX to fetch the DataTables content
    model = BankVoucherPaymentMaster
    template_name = 'accounts/bankvoucherpaymentmaster/_bankvoucherpaymentmaster_table.html'
    context_object_name = 'bankvoucherpayments'

class BankVoucherPaymentMasterCreateView(CreateView):
    model = BankVoucherPaymentMaster
    form_class = BankVoucherPaymentMasterForm
    template_name = 'accounts/bankvoucherpaymentmaster/form.html'
    success_url = reverse_lazy('bankvoucherpaymentmaster_list') # Redirect to list view after success

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Bank Voucher Payment added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, indicates HTMX success without needing to render HTML
                headers={
                    'HX-Trigger': 'refreshBankVoucherPaymentMasterList' # Custom event for HTMX
                }
            )
        return response

class BankVoucherPaymentMasterUpdateView(UpdateView):
    model = BankVoucherPaymentMaster
    form_class = BankVoucherPaymentMasterForm
    template_name = 'accounts/bankvoucherpaymentmaster/form.html'
    success_url = reverse_lazy('bankvoucherpaymentmaster_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Bank Voucher Payment updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBankVoucherPaymentMasterList'
                }
            )
        return response

class BankVoucherPaymentMasterDeleteView(DeleteView):
    model = BankVoucherPaymentMaster
    template_name = 'accounts/bankvoucherpaymentmaster/confirm_delete.html'
    success_url = reverse_lazy('bankvoucherpaymentmaster_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Bank Voucher Payment deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBankVoucherPaymentMasterList'
                }
            )
        return response

# --- Specific Report/Print View (Direct replacement for BankVoucher_Print_Details.aspx) ---

class BankVoucherPaymentReportView(DetailView):
    model = BankVoucherPaymentMaster
    template_name = 'accounts/bankvoucherpaymentmaster/report_view.html'
    context_object_name = 'voucher' # Renamed from 'object' for clarity in template

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        voucher_master = self.get_object() # The BankVoucherPaymentMaster instance

        # Utilize the fat model method to get all processed report data
        context['report_data'] = voucher_master.get_report_context_data()
        
        # Also pass the related BankVoucherPaymentDetail objects
        context['details'] = voucher_master.bankvoucherpaymentdetail_set.all()
        
        return context

```

#### 4.4 Templates

We'll define the templates required for a complete CRUD cycle, including the specific `report_view.html` that replaces the Crystal Report viewer.

**List Template (`accounts/bankvoucherpaymentmaster/list.html`):**
This template provides the main entry point to view and manage bank vouchers, acting as the list view where users can initiate CRUD operations or print reports.

```html
{% extends 'core/base.html' %}
{% load static %} {# To load static files like CSS/JS if not already in base.html #}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Bank Voucher Payments</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'bankvoucherpaymentmaster_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Bank Voucher
        </button>
    </div>
    
    <div id="bankvoucherpaymentmasterTable-container"
         hx-trigger="load, refreshBankVoucherPaymentMasterList from:body"
         hx-get="{% url 'bankvoucherpaymentmaster_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading...</p>
        </div>
    </div>
    
    <!-- Modal for form (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for broader UI state
    });

    // Listen for HTMX triggers for messages and modal closing
    document.body.addEventListener('htmx:afterSwap', function(event) {
        // Close modal after successful form submission/deletion (status 204 with HX-Trigger)
        if (event.detail.xhr.status === 204) {
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.remove('is-active'); // For Alpine.js/CSS modal close
            }
        }
    });

    // Optional: Re-initialize DataTables after HTMX loads content
    document.body.addEventListener('htmx:afterSettle', function(event) {
        if (event.detail.target.id === 'bankvoucherpaymentmasterTable-container') {
            $('#bankvoucherpaymentmasterTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
            });
        }
    });
</script>
{% endblock %}
```

**Table Partial Template (`accounts/bankvoucherpaymentmaster/_bankvoucherpaymentmaster_table.html`):**
This partial is loaded via HTMX into the list view to display the DataTables.

```html
{% load accounts_tags %} {# Load custom template tags for functions like sum_attr #}
<table id="bankvoucherpaymentmasterTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BVP No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cheque Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Paid To</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in bankvoucherpayments %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.bvp_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.cheque_date|date:"d/m/Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.get_paid_to_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.get_total_amount|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">
                <a href="{% url 'bankvoucherpaymentmaster_report' obj.pk %}" 
                    class="inline-flex items-center bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded text-xs mr-2"
                    target="_blank">
                    <svg class="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M5 4V3a1 1 0 011-1h8a1 1 0 011 1v1h1a2 2 0 012 2v10a2 2 0 01-2 2H3a2 2 0 01-2-2V6a2 2 0 012-2h1zm5 2a1 1 0 011 1v4a1 1 0 11-2 0V7a1 1 0 011-1z" clip-rule="evenodd"></path></svg>
                    Print
                </a>
                <button 
                    class="inline-flex items-center bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded text-xs mr-2"
                    hx-get="{% url 'bankvoucherpaymentmaster_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <svg class="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.38-2.828-2.829z"></path></svg>
                    Edit
                </button>
                <button 
                    class="inline-flex items-center bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs"
                    hx-get="{% url 'bankvoucherpaymentmaster_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <svg class="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>
                    Delete
                </button>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// DataTables initialization is handled by the htmx:afterSettle event in the parent list.html
// This script block is technically not needed here if the parent handles it, but kept for clarity
// in case DataTables needs to be initialized immediately on partial load without parent event.
$(document).ready(function() {
    if (!$.fn.DataTable.isDataTable('#bankvoucherpaymentmasterTable')) {
        $('#bankvoucherpaymentmasterTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
        });
    }
});
</script>
```

**Form Partial Template (`accounts/bankvoucherpaymentmaster/form.html`):**
Used for both create and update operations, loaded into a modal via HTMX.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Bank Voucher Payment</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-indicator">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
        <div id="form-indicator" class="htmx-indicator mt-4 text-center">
            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            Saving...
        </div>
    </form>
</div>
```

**Delete Confirmation Partial Template (`accounts/bankvoucherpaymentmaster/confirm_delete.html`):**
Loaded into a modal for delete confirmation.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="mb-4">Are you sure you want to delete Bank Voucher Payment: <strong>{{ object.bvp_no }}</strong>?</p>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#delete-indicator">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
        <div id="delete-indicator" class="htmx-indicator mt-4 text-center">
            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            Deleting...
        </div>
    </form>
</div>
```

**Report View Template (`accounts/bankvoucherpaymentmaster/report_view.html`):**
This template directly replaces the Crystal Report viewer. It uses the `report_data` dictionary generated by the `BankVoucherPaymentMaster` model's `get_report_context_data` method and conditional rendering based on `BankId`.

```html
{% extends 'core/base.html' %}
{% load accounts_tags %} {# Load custom template tags for functions like sum_attr #}

{% block content %}
<div class="container mx-auto px-4 py-8 print:p-0">
    <div class="max-w-4xl mx-auto bg-white p-8 shadow-md rounded-lg print:shadow-none print:rounded-none print:w-full print:max-w-full print:p-4 print:text-sm">
        <h2 class="text-2xl font-bold mb-6 text-center print:text-xl print:mb-4">Bank Voucher Payment Details</h2>

        <div class="border border-gray-300 p-6 mb-6 print:border print:border-gray-200 print:p-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <p class="text-sm text-gray-600">BVP No: <span class="font-semibold text-gray-800">{{ report_data.BVPNo }}</span></p>
                    <p class="text-sm text-gray-600">Cheque No: <span class="font-semibold text-gray-800">{{ report_data.ChequeNo }}</span></p>
                    <p class="text-sm text-gray-600">System Date: <span class="font-semibold text-gray-800">{{ report_data.SysDate }}</span></p>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-600">Amount: <span class="font-bold text-xl text-gray-800">{{ report_data.Amount|floatformat:2 }}</span></p>
                </div>
            </div>

            <div class="mb-4">
                <p class="text-sm text-gray-600">Paid To: <span class="font-semibold text-gray-800">{{ report_data.PaidTo }}</span></p>
                <p class="text-sm text-gray-600">Address: <span class="font-semibold text-gray-800">{{ report_data.Address }}</span></p>
                <p class="text-sm text-gray-600">Pay To (ECS Ref): <span class="font-semibold text-gray-800">{{ report_data.ECS }}</span></p>
            </div>
            
            {% if report_data.BankId in [1, 2, 5] %} {# Dena Bank, Dena CC, IDBI Bank #}
            <div class="mb-4 grid grid-cols-12 gap-1 items-center">
                <p class="text-sm text-gray-600 col-span-4">Cheque Date (DDMMYYYY):</p>
                <span class="font-semibold text-gray-800 text-center border-b border-gray-400 w-full pb-1">{{ report_data.d1 }}</span>
                <span class="font-semibold text-gray-800 text-center border-b border-gray-400 w-full pb-1">{{ report_data.d2 }}</span>
                <span class="text-sm text-gray-600 text-center">/</span>
                <span class="font-semibold text-gray-800 text-center border-b border-gray-400 w-full pb-1">{{ report_data.m1 }}</span>
                <span class="font-semibold text-gray-800 text-center border-b border-gray-400 w-full pb-1">{{ report_data.m2 }}</span>
                <span class="text-sm text-gray-600 text-center">/</span>
                <span class="font-semibold text-gray-800 text-center border-b border-gray-400 w-full pb-1">{{ report_data.y1 }}</span>
                <span class="font-semibold text-gray-800 text-center border-b border-gray-400 w-full pb-1">{{ report_data.y2 }}</span>
                <span class="font-semibold text-gray-800 text-center border-b border-gray-400 w-full pb-1">{{ report_data.y3 }}</span>
                <span class="font-semibold text-gray-800 text-center border-b border-gray-400 w-full pb-1">{{ report_data.y4 }}</span>
            </div>
            {% elif report_data.BankId == 3 %} {# Axis Bank Ltd. #}
            <div class="mb-4">
                <p class="text-sm text-gray-600">Cheque Date: <span class="font-semibold text-gray-800">{{ report_data.ChequeDate }}</span></p>
            </div>
            {% endif %}

            <div class="mb-4 border-t border-gray-200 pt-4 mt-4">
                <p class="text-sm text-gray-600">Total Details Amount: <span class="font-semibold text-gray-800">{{ details|sum_attr:'amount'|floatformat:2 }}</span></p>
                <p class="text-sm text-gray-600">Main Payment Amount: <span class="font-semibold text-gray-800">{{ voucher.pay_amt|floatformat:2 }}</span></p>
                {% if report_data.PaidTypeRaw|add:"0"|is_int %} {# Check if paid_type_id is convertible to int #}
                <p class="text-sm text-gray-600">Additional Amount: <span class="font-semibold text-gray-800">{{ voucher.add_amt|floatformat:2 }}</span></p>
                {% endif %}
                <p class="text-sm text-gray-600 font-bold text-lg border-t border-gray-300 pt-2 mt-2">Overall Total: <span class="text-xl text-blue-700">{{ report_data.Amount|floatformat:2 }}</span></p>
            </div>

            <h3 class="text-base font-semibold mt-6 mb-3 print:mt-4 print:mb-2">Voucher Details Breakdown</h3>
            {% if details %}
            <table class="min-w-full bg-white border border-gray-300">
                <thead>
                    <tr>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/12">SN</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-11/12">Amount</th>
                        <!-- Add other detail columns if applicable -->
                    </tr>
                </thead>
                <tbody>
                    {% for detail in details %}
                    <tr>
                        <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ detail.amount|floatformat:2 }}</td>
                        <!-- Add other detail cells -->
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
            <p class="text-gray-500 text-sm">No specific details found for this voucher breakdown.</p>
            {% endif %}

        </div>

        <div class="mt-8 text-center print:hidden">
            <a href="{% url 'bankvoucherpaymentmaster_list' %}" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Cancel
            </a>
            <button onclick="window.print()" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded ml-4">
                Print Report
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# No specific JS needed for this simple print view, Alpine.js could be used for more complex UI states #}
<script>
    // Custom template tag to check if a value is an integer (for PaidType check)
    // This is a workaround as sum_attr also adds a filter, needs to be handled via proper template tag
    // For Python in template, need to load a custom filter in accounts/templatetags/accounts_tags.py
    // {% load accounts_tags %}
    // @register.filter
    // def is_int(value):
    //     try:
    //         int(value)
    //         return True
    //     except (ValueError, TypeError):
    //         return False
</script>
{% endblock %}
```
*Note on `is_int` filter*: I've added a placeholder for a custom template filter `is_int` to replicate the C# `int.TryParse` logic directly in the template. This filter would need to be added to `accounts/templatetags/accounts_tags.py` alongside `sum_attr`.

#### 4.5 URLs

Define URL patterns for all views.

```python
# accounts/urls.py
from django.urls import path
from .views import (
    BankVoucherPaymentMasterListView, BankVoucherPaymentTablePartialView,
    BankVoucherPaymentMasterCreateView, BankVoucherPaymentMasterUpdateView,
    BankVoucherPaymentMasterDeleteView, BankVoucherPaymentReportView
)

urlpatterns = [
    # Standard CRUD for Bank Voucher Payments
    path('bankvoucherpayments/', BankVoucherPaymentMasterListView.as_view(), name='bankvoucherpaymentmaster_list'),
    path('bankvoucherpayments/table/', BankVoucherPaymentTablePartialView.as_view(), name='bankvoucherpaymentmaster_table'),
    path('bankvoucherpayments/add/', BankVoucherPaymentMasterCreateView.as_view(), name='bankvoucherpaymentmaster_add'),
    path('bankvoucherpayments/edit/<int:pk>/', BankVoucherPaymentMasterUpdateView.as_view(), name='bankvoucherpaymentmaster_edit'),
    path('bankvoucherpayments/delete/<int:pk>/', BankVoucherPaymentMasterDeleteView.as_view(), name='bankvoucherpaymentmaster_delete'),
    
    # Specific Report/Print View (Direct replacement for BankVoucher_Print_Details.aspx)
    # The 'Id' from QueryString becomes 'pk' in Django URL pattern
    path('bankvoucherpayments/<int:pk>/report/', BankVoucherPaymentReportView.as_view(), name='bankvoucherpaymentmaster_report'),
]
```

#### 4.6 Tests

Comprehensive tests for models (business logic) and views (HTTP responses, template usage, data in context).

```python
# accounts/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import BankVoucherPaymentMaster, BankVoucherPaymentDetail, PaidType
from datetime import datetime, timedelta

class PaidTypeModelTest(TestCase):
    def test_paid_type_creation(self):
        paid_type = PaidType.objects.create(id=1, particulars="Cash Payment")
        self.assertEqual(paid_type.particulars, "Cash Payment")
        self.assertEqual(str(paid_type), "Cash Payment")

class BankVoucherPaymentMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a PaidType instance for testing
        cls.cash_paid_type = PaidType.objects.create(id=101, particulars="Cash")
        cls.bank_paid_type = PaidType.objects.create(id=102, particulars="Bank Transfer")

        # Create master voucher objects
        cls.voucher1 = BankVoucherPaymentMaster.objects.create(
            id=1, comp_id=1, fin_year_id=2023,
            name_on_cheque="John Doe",
            paid_type_id="101", # Corresponds to cash_paid_type
            ecs_type=1, pay_to="Emp001",
            cheque_date=datetime(2023, 1, 15, 10, 0, 0), bank=1, # Dena Bank
            pay_amt=100.00, add_amt=5.00,
            bvp_no="BVP/001", cheque_no="CHQ001", sys_date=datetime(2023, 1, 14, 10, 0, 0),
            transaction_type=1
        )
        BankVoucherPaymentDetail.objects.create(id=1, mid=cls.voucher1, amount=50.00)
        BankVoucherPaymentDetail.objects.create(id=2, mid=cls.voucher1, amount=25.00)

        cls.voucher2 = BankVoucherPaymentMaster.objects.create(
            id=2, comp_id=1, fin_year_id=2023,
            name_on_cheque=None, # Name on cheque is null, use paid_type_id or ECS
            paid_type_id="NonIntType", # Non-integer PaidType
            ecs_type=2, pay_to="Cust001",
            cheque_date=datetime(2023, 2, 20, 11, 0, 0), bank=3, # Axis Bank
            pay_amt=200.00, add_amt=10.00, # AddAmt should not be included due to non-int PaidType
            bvp_no="BVP/002", cheque_no="CHQ002", sys_date=datetime(2023, 2, 19, 11, 0, 0),
            transaction_type=2
        )
        BankVoucherPaymentDetail.objects.create(id=3, mid=cls.voucher2, amount=75.00)

        cls.voucher3 = BankVoucherPaymentMaster.objects.create(
            id=3, comp_id=1, fin_year_id=2023,
            name_on_cheque=None, # Name on cheque is null, use paid_type_id or ECS
            paid_type_id="102", # Corresponds to bank_paid_type
            ecs_type=3, pay_to="Supp001",
            cheque_date=datetime(2023, 3, 1, 9, 0, 0), bank=5, # IDBI Bank
            pay_amt=500.00, add_amt=20.00,
            bvp_no="BVP/003", cheque_no="CHQ003", sys_date=datetime(2023, 2, 28, 9, 0, 0),
            transaction_type=3
        )
        BankVoucherPaymentDetail.objects.create(id=4, mid=cls.voucher3, amount=120.00)

    def test_get_paid_to_name(self):
        # Test with name_on_cheque
        self.assertEqual(self.voucher1.get_paid_to_name(), "John Doe")

        # Test with integer paid_type_id
        self.assertEqual(self.voucher3.get_paid_to_name(), "Bank Transfer")

        # Test with non-integer paid_type_id (fallback to ECSNames)
        self.assertEqual(self.voucher2.get_paid_to_name(), "Customer Cust001 Name")

    def test_get_paid_to_address(self):
        # Test ECS Address logic
        self.assertEqual(self.voucher1.get_paid_to_address(), "Employee Emp001 Address")
        self.assertEqual(self.voucher2.get_paid_to_address(), "Customer Cust001 Address")
        self.assertEqual(self.voucher3.get_paid_to_address(), "Supplier Supp001 Address")

    def test_get_cheque_date_dmy_parts(self):
        # Dena Bank (1, 2, 5) uses parts
        dmy_parts = self.voucher1.get_cheque_date_dmy_parts()
        self.assertEqual(dmy_parts, {
            'd1': '1', 'd2': '5', 'm1': '0', 'm2': '1',
            'y1': '2', 'y2': '0', 'y3': '2', 'y4': '3'
        })

        # Axis Bank (3) does not use parts (should be empty)
        dmy_parts_axis = self.voucher2.get_cheque_date_dmy_parts()
        self.assertEqual(dmy_parts_axis, {
            'd1': '', 'd2': '', 'm1': '', 'm2': '',
            'y1': '', 'y2': '', 'y3': '', 'y4': ''
        })

    def test_get_total_amount(self):
        # Voucher1: Details (50+25) + PayAmt (100) + AddAmt (5) = 180
        self.assertEqual(self.voucher1.get_total_amount(), 180.00)

        # Voucher2: Details (75) + PayAmt (200) + AddAmt (10 - but not included) = 275.00
        # AddAmt not included because PaidType is 'NonIntType'
        self.assertEqual(self.voucher2.get_total_amount(), 275.00)
        
        # Voucher3: Details (120) + PayAmt (500) + AddAmt (20) = 640.00
        self.assertEqual(self.voucher3.get_total_amount(), 640.00)

    def test_get_report_context_data(self):
        report_data = self.voucher1.get_report_context_data()
        self.assertEqual(report_data['PaidTo'], "John Doe")
        self.assertEqual(report_data['Amount'], 180.00)
        self.assertEqual(report_data['BankId'], 1)
        self.assertEqual(report_data['d1'], '1') # Check a specific date part for bank 1

        report_data_axis = self.voucher2.get_report_context_data()
        self.assertEqual(report_data_axis['BankId'], 3)
        self.assertEqual(report_data_axis['d1'], '') # Date parts should be empty for bank 3

class BankVoucherPaymentViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Create test data for views
        self.cash_paid_type = PaidType.objects.create(id=101, particulars="Cash")
        self.voucher = BankVoucherPaymentMaster.objects.create(
            id=10, comp_id=1, fin_year_id=2023,
            name_on_cheque="Test Vendor", paid_type_id="101",
            ecs_type=1, pay_to="Emp002",
            cheque_date=datetime(2023, 4, 1, 10, 0, 0), bank=1,
            pay_amt=100.00, add_amt=5.00,
            bvp_no="BVP/VIEW01", cheque_no="CHQVIEW01", sys_date=datetime(2023, 3, 31, 10, 0, 0),
            transaction_type=1
        )
        BankVoucherPaymentDetail.objects.create(id=10, mid=self.voucher, amount=50.00)


    def test_list_view(self):
        response = self.client.get(reverse('bankvoucherpaymentmaster_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/bankvoucherpaymentmaster/list.html')
        self.assertTrue('bankvoucherpayments' in response.context)
        self.assertContains(response, 'Add New Bank Voucher')

    def test_table_partial_view(self):
        response = self.client.get(reverse('bankvoucherpaymentmaster_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/bankvoucherpaymentmaster/_bankvoucherpaymentmaster_table.html')
        self.assertTrue('bankvoucherpayments' in response.context)
        self.assertContains(response, self.voucher.bvp_no) # Check if voucher is in table

    def test_create_view_get(self):
        response = self.client.get(reverse('bankvoucherpaymentmaster_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/bankvoucherpaymentmaster/form.html')
        self.assertTrue('form' in response.context)

    def test_create_view_post_success(self):
        data = {
            'comp_id': 2, 'fin_year_id': 2024,
            'name_on_cheque': 'New Supplier', 'paid_type_id': '101',
            'ecs_type': 3, 'pay_to': 'SUPP002',
            'cheque_date': '2024-05-01', 'bank': 3,
            'pay_amt': 300.00, 'add_amt': 15.00,
            'bvp_no': 'BVP/NEW', 'cheque_no': 'NEWCHQ', 'sys_date': '2024-04-30',
            'transaction_type': 1
        }
        response = self.client.post(reverse('bankvoucherpaymentmaster_add'), data)
        self.assertEqual(response.status_code, 302) # Redirect after success
        self.assertTrue(BankVoucherPaymentMaster.objects.filter(bvp_no='BVP/NEW').exists())

    def test_create_view_post_htmx_success(self):
        data = {
            'comp_id': 3, 'fin_year_id': 2025,
            'name_on_cheque': 'HTMX Test', 'paid_type_id': '101',
            'ecs_type': 1, 'pay_to': 'EMP003',
            'cheque_date': '2025-01-01', 'bank': 2,
            'pay_amt': 50.00, 'add_amt': 2.00,
            'bvp_no': 'BVP/HTMX', 'cheque_no': 'HTMXCHQ', 'sys_date': '2024-12-31',
            'transaction_type': 1
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('bankvoucherpaymentmaster_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX success with no content
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBankVoucherPaymentMasterList')
        self.assertTrue(BankVoucherPaymentMaster.objects.filter(bvp_no='BVP/HTMX').exists())

    def test_update_view_get(self):
        response = self.client.get(reverse('bankvoucherpaymentmaster_edit', args=[self.voucher.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/bankvoucherpaymentmaster/form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, self.voucher)

    def test_update_view_post_success(self):
        updated_data = {
            'comp_id': self.voucher.comp_id, 'fin_year_id': self.voucher.fin_year_id,
            'name_on_cheque': 'Updated Vendor Name', 'paid_type_id': self.voucher.paid_type_id,
            'ecs_type': self.voucher.ecs_type, 'pay_to': self.voucher.pay_to,
            'cheque_date': self.voucher.cheque_date.strftime('%Y-%m-%d'), 'bank': self.voucher.bank,
            'pay_amt': 150.00, 'add_amt': self.voucher.add_amt,
            'bvp_no': self.voucher.bvp_no, 'cheque_no': self.voucher.cheque_no, 'sys_date': self.voucher.sys_date.strftime('%Y-%m-%d'),
            'transaction_type': self.voucher.transaction_type
        }
        response = self.client.post(reverse('bankvoucherpaymentmaster_edit', args=[self.voucher.pk]), updated_data)
        self.assertEqual(response.status_code, 302)
        self.voucher.refresh_from_db()
        self.assertEqual(self.voucher.pay_amt, 150.00)
        self.assertEqual(self.voucher.name_on_cheque, 'Updated Vendor Name')

    def test_delete_view_get(self):
        response = self.client.get(reverse('bankvoucherpaymentmaster_delete', args=[self.voucher.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/bankvoucherpaymentmaster/confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'], self.voucher)

    def test_delete_view_post_success(self):
        pk_to_delete = self.voucher.pk
        response = self.client.post(reverse('bankvoucherpaymentmaster_delete', args=[pk_to_delete]))
        self.assertEqual(response.status_code, 302)
        self.assertFalse(BankVoucherPaymentMaster.objects.filter(pk=pk_to_delete).exists())

    def test_delete_view_post_htmx_success(self):
        # Create another voucher to delete with HTMX
        voucher_to_delete = BankVoucherPaymentMaster.objects.create(
            id=11, comp_id=1, fin_year_id=2023,
            name_on_cheque="To Delete HTMX", paid_type_id="101",
            ecs_type=1, pay_to="EMP004",
            cheque_date=datetime(2023, 6, 1), bank=1,
            pay_amt=10.00, add_amt=1.00,
            bvp_no="BVP/DEL", cheque_no="DELCHQ", sys_date=datetime(2023, 5, 31),
            transaction_type=1
        )
        pk_to_delete = voucher_to_delete.pk
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('bankvoucherpaymentmaster_delete', args=[pk_to_delete]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBankVoucherPaymentMasterList')
        self.assertFalse(BankVoucherPaymentMaster.objects.filter(pk=pk_to_delete).exists())

    def test_report_view(self):
        response = self.client.get(reverse('bankvoucherpaymentmaster_report', args=[self.voucher.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/bankvoucherpaymentmaster/report_view.html')
        self.assertTrue('voucher' in response.context)
        self.assertEqual(response.context['voucher'], self.voucher)
        self.assertTrue('report_data' in response.context)
        self.assertContains(response, self.voucher.bvp_no)
        self.assertContains(response, f"Amount: {self.voucher.get_total_amount():.2f}")
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
- **HTMX:** All CRUD forms (add, edit, delete) are loaded into a modal dynamically using HTMX `hx-get` and `hx-target`. Form submissions use `hx-post` with `hx-swap="none"` and trigger `HX-Trigger` events (`refreshBankVoucherPaymentMasterList`) to refresh the main DataTables list view without a full page reload. The DataTables itself is loaded via `hx-get` with `hx-trigger="load, refreshBankVoucherPaymentMasterList from:body"`.
- **Alpine.js:** Used for simple UI state management, primarily for controlling the modal's visibility (`on click add .is-active to #modal` and `on click remove .is-active from me`).
- **DataTables:** Configured on the `_bankvoucherpaymentmaster_table.html` partial to provide client-side search, sort, and pagination. Re-initialization logic is included on HTMX `afterSettle` to ensure DataTables works correctly after new content is loaded.
- **DRY Templates:** Use `{% extends 'core/base.html' %}` and break down complex UI components into smaller, reusable partials (`_bankvoucherpaymentmaster_table.html`, `form.html`, `confirm_delete.html`).

## Final Notes

*   **Placeholders:** `[MODEL_NAME]`, `[APP_NAME]`, etc., have been replaced with `BankVoucherPaymentMaster` and `accounts` respectively.
*   **Business Logic:** All complex data retrieval, calculation, and conditional formatting logic has been moved to methods within the `BankVoucherPaymentMaster` model, ensuring the "fat model, thin view" principle.
*   **Report Replacement:** The Crystal Report functionality has been successfully replaced by a Django HTML template (`report_view.html`) that dynamically renders the processed data. This provides equivalent functionality without proprietary reporting software.
*   **Scalability & Maintainability:** This structure promotes a scalable and maintainable Django application, enabling future enhancements and easier debugging compared to the original ASP.NET architecture.
*   **Automation:** The conversion process outlined (schema extraction, functionality identification, UI analysis, and code generation based on templates) can be significantly automated using AI tools, reducing manual effort and potential errors during migration.
*   **Custom Template Tags:** Ensure the `accounts/templatetags/accounts_tags.py` file is correctly created and the app `accounts` is added to `INSTALLED_APPS` in `settings.py` so that `{% load accounts_tags %}` works.
*   **Frontend Libraries:** Ensure DataTables, HTMX, and Alpine.js CDN links are included in your `core/base.html` as per the general guidelines.