## ASP.NET to Django Conversion Script: Tour Voucher Edit Details

This document outlines a comprehensive plan to modernize the `TourVoucher_Edit_Details.aspx` ASP.NET application into a robust, scalable Django 5.0+ solution. Our approach prioritizes automation-driven migration, adhering to a 'Fat Model, Thin View' architecture, and leveraging HTMX and Alpine.js for a highly interactive user experience.

### Business Benefits of Django Modernization:

*   **Enhanced Performance & Scalability**: Django's efficient design and ORM allow for faster data processing and easier scaling to handle increased user loads compared to legacy ASP.NET.
*   **Reduced Development Costs**: Python's readability and Django's "batteries-included" philosophy mean faster development cycles and easier maintenance, leading to significant cost savings.
*   **Improved User Experience**: The adoption of HTMX, Alpine.js, and DataTables delivers a modern, reactive, and intuitive user interface without full page reloads, mirroring desktop application responsiveness.
*   **Future-Proof Technology Stack**: Transitioning to Django ensures your application is built on widely adopted, actively maintained open-source technologies, mitigating vendor lock-in and simplifying future enhancements.
*   **Increased Security & Stability**: Django provides built-in security features against common web vulnerabilities, offering a more secure and stable application environment.
*   **Streamlined Data Management**: Django's powerful ORM simplifies complex database interactions, improving data integrity and development efficiency.

### Conversion Steps:

### Step 1: Extract Database Schema

Based on the ASP.NET code, we identify the following primary and related database tables. The `fun.select` and `fun.update` calls are key indicators.

*   **`tblACC_TourVoucher_Master`**: This is the central table for the "Tour Voucher Edit Details" page, handling the overall voucher and its balance amounts.
    *   Columns: `Id` (PK), `TIMId` (FK), `AmtBalTowardsCompany`, `AmtBalTowardsEmployee`, `SysDate`, `SysTime`, `SessionId`, `CompId` (FK)
*   **`tblACC_TourIntimation_Master`**: Contains read-only details of the initial tour request.
    *   Columns: `Id` (PK), `EmpId` (FK), `WONo`, `BGGroupId` (FK), `ProjectName`, `PlaceOfTourCity` (FK), `PlaceOfTourState` (FK), `PlaceOfTourCountry` (FK), `TourStartDate`, `TourStartTime`, `TourEndDate`, `TourEndTime`, `NoOfDays`, `NameAddressSerProvider`, `ContactPerson`, `ContactNo`, `Email`, `CompId` (FK)
*   **`tblACC_TourVoucherAdvance_Details`**: Stores the sanctioned amounts for specific expense types related to a tour voucher.
    *   Columns: `Id` (PK), `MId` (FK to `tblACC_TourVoucher_Master`), `TDMId` (FK to `tblACC_TourExpencessType`), `SanctionedAmount`, `Remarks`
*   **`tblACC_TourVoucherAdvance`**: Stores sanctioned amounts for advances transferred to other employees related to a tour voucher.
    *   Columns: `Id` (PK), `MId` (FK to `tblACC_TourVoucher_Master`), `TAMId` (FK to `tblACC_TourAdvance`), `SanctionedAmount`, `Remarks`
*   **`tblACC_TourExpencessType`**: A lookup table for different expense terms.
    *   Columns: `Id` (PK), `Terms`
*   **`tblACC_TourAdvance_Details`**: Original advance request details for expense types (read-only for this page).
    *   Columns: `MId` (FK to `tblACC_TourIntimation_Master`), `ExpencessId` (FK to `tblACC_TourExpencessType`), `Amount`, `Remarks`
*   **`tblACC_TourAdvance`**: Original advance request details for advances to employees (read-only for this page).
    *   Columns: `Id` (PK), `EmpId` (FK to `tblHR_OfficeStaff`), `Amount`, `Remarks`
*   **`tblHR_OfficeStaff`**: Employee master data.
    *   Columns: `EmpId` (PK), `Title`, `EmployeeName`
*   **`BusinessGroup`**: Business group master data.
    *   Columns: `Id` (PK), `Name`, `Symbol`
*   **`tblCity`, `tblState`, `tblCountry`**: Geographical lookup tables.
    *   Columns: `CityId`/`SId`/`CId` (PK), `CityName`/`StateName`/`CountryName`

### Step 2: Identify Backend Functionality

The ASP.NET page primarily serves as an **Update** interface for an existing tour voucher.

*   **Read**: Upon `Page_Load`, data from `tblACC_TourIntimation_Master`, `tblACC_TourVoucher_Master`, `tblACC_TourVoucherAdvance_Details`, and `tblACC_TourVoucherAdvance` (along with several lookup tables like `tblHR_OfficeStaff`, `BusinessGroup`, `tblCity`, `tblState`, `tblCountry`, `tblACC_TourExpencessType`, `tblACC_TourAdvance_Details`, `tblACC_TourAdvance`) is retrieved and displayed.
*   **Update**:
    *   The `btnSum_Click` event recalculates and displays the `AmtBalTowardsCompany` and `AmtBalTowardsEmployee` fields based on the user-entered sanctioned amounts in the grids. This is a client-side calculation (or a server-side preview).
    *   The `btnSubmit_Click` event saves the updated `AmtBalTowardsCompany`, `AmtBalTowardsEmployee` to `tblACC_TourVoucher_Master`, and the `SanctionedAmount`, `Remarks` for each item in `tblACC_TourVoucherAdvance_Details` and `tblACC_TourVoucherAdvance`.
*   **Validation**: Input validation for numeric fields (`SanctionedAmount`, balance amounts) using `RequiredFieldValidator` and `RegularExpressionValidator`.

### Step 3: Infer UI Components

The ASP.NET UI consists of:

*   **Static Information Display**: Labels (`<asp:Label>`) for `Employee Name`, `Project Name`, `Place of Tour`, `Tour Start/End Date/Time`, `No of Days`, `Accommodation Provider`, `Contact Person/No`, `Email`.
*   **Tabbed Interface**: `AjaxControlToolkit:TabContainer` with two `TabPanel`s: "Advance Details" and "Advance Trans. To".
*   **Data Grids**: Two `asp:GridView` controls (`GridView2` and `GridView1`) used to display and allow editing of sanctioned amounts. Each row in these grids has read-only labels for original values and editable `asp:TextBox` controls for `SanctionedAmount` and `Remarks`.
*   **Summary Fields**: `lblTAmt1` (Total Advance Amount), `lblTSAmt1` (Total Sanctioned Amount), `txtAmtBalTowardsCompany`, `txtAmtBalTowardsEmployee`. The `txtAmtBalTowardsCompany` and `txtAmtBalTowardsEmployee` are textboxes, seemingly used for display but also possibly allowing manual edits before calculation.
*   **Action Buttons**: `btnSum` (Calculate), `btnSubmit` (Update), `btnCancel`.

### Step 4: Generate Django Code

We will structure the Django application, let's call it `tour_management`, to reflect these components.

#### 4.1 Models (`tour_management/models.py`)

Models are mapped to existing tables (`managed = False`). Complex data retrieval and calculation logic from the C# code-behind's `Page_Load`, `fillgrid`, `FillGridAdvanceTo`, and `btnSum_Click` will be integrated as methods within the `TourVoucherMaster` model or its manager.

```python
from django.db import models
from django.db.models import F, Sum, Value, FloatField
from django.db.models.functions import Coalesce
from django.utils import timezone

class Company(models.Model):
    # Assuming a Company model for CompId from Session
    # Placeholder for the actual table name and fields if different
    id = models.IntegerField(db_column='CompId', primary_key=True)
    name = models.CharField(max_length=255, db_column='CompanyName')

    class Meta:
        managed = False
        db_table = 'tblCompanyMaster' # Assuming this is the company table
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name

class FinancialYear(models.Model):
    # Assuming a FinancialYear model for FinYearId from Session
    # Placeholder for the actual table name and fields if different
    id = models.IntegerField(db_column='FinYearId', primary_key=True)
    year_name = models.CharField(max_length=50, db_column='FinYearName')

    class Meta:
        managed = False
        db_table = 'tblFinancialYear' # Assuming this is the financial year table
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.year_name

class Employee(models.Model):
    id = models.IntegerField(db_column='EmpId', primary_key=True)
    title = models.CharField(db_column='Title', max_length=100, blank=True, null=True)
    name = models.CharField(db_column='EmployeeName', max_length=255)
    # Add other employee fields if necessary

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.title or ''} {self.name} [{self.id}]".strip()

class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=255)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return f"{self.name} [{self.symbol}]"

class Country(models.Model):
    id = models.IntegerField(db_column='CId', primary_key=True)
    name = models.CharField(db_column='CountryName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblCountry'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.name

class State(models.Model):
    id = models.IntegerField(db_column='SId', primary_key=True)
    name = models.CharField(db_column='StateName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblState'
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.name

class City(models.Model):
    id = models.IntegerField(db_column='CityId', primary_key=True)
    name = models.CharField(db_column='CityName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblCity'
        verbose_name = 'City'
        verbose_plural = 'Cities'

    def __str__(self):
        return self.name

class TourIntimationMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    employee = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='EmpId', related_name='tour_intimations')
    wo_no = models.CharField(db_column='WONo', max_length=255, blank=True, null=True)
    business_group = models.ForeignKey(BusinessGroup, on_delete=models.DO_NOTHING, db_column='BGGroupId', blank=True, null=True, related_name='tour_intimations')
    project_name = models.CharField(db_column='ProjectName', max_length=255, blank=True, null=True)
    place_of_tour_city = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='PlaceOfTourCity', blank=True, null=True)
    place_of_tour_state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='PlaceOfTourState', blank=True, null=True)
    place_of_tour_country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='PlaceOfTourCountry', blank=True, null=True)
    tour_start_date = models.DateField(db_column='TourStartDate', blank=True, null=True)
    tour_start_time = models.TimeField(db_column='TourStartTime', blank=True, null=True)
    tour_end_date = models.DateField(db_column='TourEndDate', blank=True, null=True)
    tour_end_time = models.TimeField(db_column='TourEndTime', blank=True, null=True)
    no_of_days = models.IntegerField(db_column='NoOfDays', blank=True, null=True)
    name_address_ser_provider = models.CharField(db_column='NameAddressSerProvider', max_length=500, blank=True, null=True)
    contact_person = models.CharField(db_column='ContactPerson', max_length=255, blank=True, null=True)
    contact_no = models.CharField(db_column='ContactNo', max_length=50, blank=True, null=True)
    email = models.CharField(db_column='Email', max_length=255, blank=True, null=True)
    compid = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', blank=True, null=True)
    # Add other fields as identified

    class Meta:
        managed = False
        db_table = 'tblACC_TourIntimation_Master'
        verbose_name = 'Tour Intimation Master'
        verbose_name_plural = 'Tour Intimation Masters'

    def __str__(self):
        return f"Tour Intimation {self.id} for {self.employee.name}"

    @property
    def formatted_place_of_tour(self):
        parts = []
        if self.place_of_tour_country:
            parts.append(self.place_of_tour_country.name)
        if self.place_of_tour_state:
            parts.append(self.place_of_tour_state.name)
        if self.place_of_tour_city:
            parts.append(self.place_of_tour_city.name)
        return ", ".join(parts) or "N/A"

    @property
    def wo_or_bg_group(self):
        if self.wo_no == "NA" and self.business_group:
            return f"BG Group: {self.business_group}"
        elif self.wo_no != "NA":
            return f"WO No: {self.wo_no}"
        return "N/A"

class TourExpenseType(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblACC_TourExpencessType'
        verbose_name = 'Tour Expense Type'
        verbose_name_plural = 'Tour Expense Types'

    def __str__(self):
        return self.terms

class TourAdvanceDetails(models.Model):
    # This represents the original requested advance for an expense type
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming 'Id' is PK for this table
    tour_intimation_master = models.ForeignKey(TourIntimationMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='advance_details')
    expense_type = models.ForeignKey(TourExpenseType, on_delete=models.DO_NOTHING, db_column='ExpencessId')
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=3)
    remarks = models.CharField(db_column='Remarks', max_length=500, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_TourAdvance_Details'
        verbose_name = 'Tour Advance Detail'
        verbose_name_plural = 'Tour Advance Details'

    def __str__(self):
        return f"Advance for {self.expense_type.terms} ({self.amount})"

class TourAdvance(models.Model):
    # This represents the original requested advance for an employee
    id = models.IntegerField(db_column='Id', primary_key=True)
    employee = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='EmpId', related_name='tour_advances')
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=3)
    remarks = models.CharField(db_column='Remarks', max_length=500, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_TourAdvance'
        verbose_name = 'Tour Advance To Employee'
        verbose_name_plural = 'Tour Advances To Employees'

    def __str__(self):
        return f"Advance to {self.employee.name} ({self.amount})"


class TourVoucherMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    tour_intimation_master = models.ForeignKey(TourIntimationMaster, on_delete=models.DO_NOTHING, db_column='TIMId', related_name='tour_vouchers')
    amt_bal_towards_company = models.DecimalField(db_column='AmtBalTowardsCompany', max_digits=18, decimal_places=3, default=0.00)
    amt_bal_towards_employee = models.DecimalField(db_column='AmtBalTowardsEmployee', max_digits=18, decimal_places=3, default=0.00)
    sys_date = models.DateField(db_column='SysDate', blank=True, null=True)
    sys_time = models.TimeField(db_column='SysTime', blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=255, blank=True, null=True)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', blank=True, null=True) # Assuming CompId exists

    class Meta:
        managed = False
        db_table = 'tblACC_TourVoucher_Master'
        verbose_name = 'Tour Voucher Master'
        verbose_name_plural = 'Tour Voucher Masters'

    def __str__(self):
        return f"Tour Voucher {self.id} for TIM {self.tour_intimation_master.id}"

    def get_advance_details_data(self):
        """
        Retrieves combined data for 'Advance Details' grid (GridView2).
        Combines original advance details with sanctioned details.
        """
        expense_types = TourExpenseType.objects.all()
        data = []
        for et in expense_types:
            original_adv = TourAdvanceDetails.objects.filter(
                tour_intimation_master=self.tour_intimation_master,
                expense_type=et
            ).first()
            sanctioned_adv = TourVoucherAdvanceDetails.objects.filter(
                tour_voucher_master=self,
                expense_type=et
            ).first()

            row = {
                'TVADId': sanctioned_adv.id if sanctioned_adv else None, # Primary Key for update
                'TDMId': et.id, # Foreign Key to TourExpenseType for mapping
                'terms': et.terms,
                'amount': original_adv.amount if original_adv else 0.00,
                'remarks': original_adv.remarks if original_adv else '',
                'sanctioned_amount': sanctioned_adv.sanctioned_amount if sanctioned_adv else 0.00,
                'sanctioned_remarks': sanctioned_adv.remarks if sanctioned_adv else '',
            }
            data.append(row)
        return data

    def get_advance_trans_to_data(self):
        """
        Retrieves combined data for 'Advance Trans. To' grid (GridView1).
        Combines original advance details with sanctioned details.
        """
        voucher_advances = TourVoucherAdvance.objects.filter(tour_voucher_master=self)
        data = []
        for va in voucher_advances:
            original_adv = va.tour_advance # Already linked via FK
            if original_adv:
                row = {
                    'TVTId': va.id, # Primary Key for update
                    'TATId': original_adv.id, # Foreign Key to TourAdvance for mapping
                    'employee_name': original_adv.employee.name,
                    'amount': original_adv.amount,
                    'remarks': original_adv.remarks,
                    'sanctioned_amount': va.sanctioned_amount,
                    'sanctioned_remarks': va.remarks,
                }
                data.append(row)
        return data

    def calculate_balance_amounts(self, advance_details_sanctioned_amounts, advance_to_sanctioned_amounts):
        """
        Calculates AmtBalTowardsCompany and AmtBalTowardsEmployee.
        Equivalent to btnSum_Click logic.
        Args:
            advance_details_sanctioned_amounts (list of dict): {id: TVADId, sanctioned_amount: value}
            advance_to_sanctioned_amounts (list of dict): {id: TVTId, sanctioned_amount: value}
        Returns:
            dict: {total_advance_amount, total_sanctioned_amount, amt_bal_company, amt_bal_employee}
        """
        total_original_advance_details = TourAdvanceDetails.objects.filter(
            tour_intimation_master=self.tour_intimation_master
        ).aggregate(Sum('amount'))['amount__sum'] or 0.00

        total_original_advance_to = TourAdvance.objects.filter(
            id__in=TourVoucherAdvance.objects.filter(tour_voucher_master=self).values_list('tour_advance__id', flat=True)
        ).aggregate(Sum('amount'))['amount__sum'] or 0.00

        total_advance_amount = round(total_original_advance_details + total_original_advance_to, 2)

        total_sanctioned_amount_details = sum(item['sanctioned_amount'] for item in advance_details_sanctioned_amounts if item['sanctioned_amount'] is not None)
        total_sanctioned_amount_to = sum(item['sanctioned_amount'] for item in advance_to_sanctioned_amounts if item['sanctioned_amount'] is not None)
        total_sanctioned_amount = round(total_sanctioned_amount_details + total_sanctioned_amount_to, 2)

        amt_bal_company = 0.00
        amt_bal_employee = 0.00

        if (total_advance_amount - total_sanctioned_amount) > 0:
            amt_bal_employee = round(total_advance_amount - total_sanctioned_amount, 2)
        else:
            amt_bal_company = round(total_sanctioned_amount - total_advance_amount, 2)
            
        return {
            'total_advance_amount': total_advance_amount,
            'total_sanctioned_amount': total_sanctioned_amount,
            'amt_bal_company': amt_bal_company,
            'amt_bal_employee': amt_bal_employee
        }

    def update_sanctioned_data(self, advance_details_data, advance_to_data, user_session_id):
        """
        Updates sanctioned amounts and remarks.
        Equivalent to btnSubmit_Click update logic.
        """
        # Update TourVoucherMaster
        self.sys_date = timezone.localdate()
        self.sys_time = timezone.localtime().time()
        self.session_id = user_session_id
        # Balance amounts are set by the view based on calculation
        self.save()

        # Update TourVoucherAdvanceDetails
        for item_data in advance_details_data:
            tvad_id = item_data.get('id') # This is the primary key from tblACC_TourVoucherAdvance_Details
            sanctioned_amount = item_data.get('sanctioned_amount')
            sanctioned_remarks = item_data.get('sanctioned_remarks')
            expense_type_id = item_data.get('tdm_id') # This is the ExpencessId (TDMId)

            if tvad_id: # Existing record
                tvad = TourVoucherAdvanceDetails.objects.get(id=tvad_id, tour_voucher_master=self)
                tvad.sanctioned_amount = sanctioned_amount
                tvad.remarks = sanctioned_remarks
                tvad.save()
            elif sanctioned_amount is not None and sanctioned_amount > 0 and expense_type_id: # New record
                TourVoucherAdvanceDetails.objects.create(
                    tour_voucher_master=self,
                    expense_type_id=expense_type_id,
                    sanctioned_amount=sanctioned_amount,
                    remarks=sanctioned_remarks
                )

        # Update TourVoucherAdvance
        for item_data in advance_to_data:
            tv_id = item_data.get('id') # Primary Key from tblACC_TourVoucherAdvance
            sanctioned_amount = item_data.get('sanctioned_amount')
            sanctioned_remarks = item_data.get('sanctioned_remarks')
            tour_advance_id = item_data.get('tat_id') # Foreign Key to TourAdvance

            if tv_id: # Existing record
                tv = TourVoucherAdvance.objects.get(id=tv_id, tour_voucher_master=self)
                tv.sanctioned_amount = sanctioned_amount
                tv.remarks = sanctioned_remarks
                tv.save()
            elif sanctioned_amount is not None and sanctioned_amount > 0 and tour_advance_id: # New record (less likely on an edit page, but good to handle)
                 TourVoucherAdvance.objects.create(
                    tour_voucher_master=self,
                    tour_advance_id=tour_advance_id,
                    sanctioned_amount=sanctioned_amount,
                    remarks=sanctioned_remarks
                )

class TourVoucherAdvanceDetails(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    tour_voucher_master = models.ForeignKey(TourVoucherMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='advance_sanction_details')
    expense_type = models.ForeignKey(TourExpenseType, on_delete=models.DO_NOTHING, db_column='TDMId') # TDMId is the ExpencessId
    sanctioned_amount = models.DecimalField(db_column='SanctionedAmount', max_digits=18, decimal_places=3, default=0.00)
    remarks = models.CharField(db_column='Remarks', max_length=500, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_TourVoucherAdvance_Details'
        verbose_name = 'Tour Voucher Advance Detail'
        verbose_name_plural = 'Tour Voucher Advance Details'

    def __str__(self):
        return f"Sanctioned: {self.expense_type.terms} for TV {self.tour_voucher_master.id}"


class TourVoucherAdvance(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    tour_voucher_master = models.ForeignKey(TourVoucherMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='advance_sanction_to')
    tour_advance = models.ForeignKey(TourAdvance, on_delete=models.DO_NOTHING, db_column='TAMId') # TAMId is the Id from tblACC_TourAdvance
    sanctioned_amount = models.DecimalField(db_column='SanctionedAmount', max_digits=18, decimal_places=3, default=0.00)
    remarks = models.CharField(db_column='Remarks', max_length=500, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_TourVoucherAdvance'
        verbose_name = 'Tour Voucher Advance To Employee'
        verbose_name_plural = 'Tour Voucher Advances To Employees'

    def __str__(self):
        return f"Sanctioned: {self.tour_advance.employee.name} for TV {self.tour_voucher_master.id}"

```

#### 4.2 Forms (`tour_management/forms.py`)

We'll use `ModelForm` for the main `TourVoucherMaster` fields and `inlineformset_factory` for the grid data.

```python
from django import forms
from django.forms import inlineformset_factory
from .models import (
    TourVoucherMaster, TourVoucherAdvanceDetails, TourVoucherAdvance,
    TourExpenseType, TourAdvance, Employee
)

# Base CSS classes for Tailwind
FORM_CONTROL_CLASSES = "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
READONLY_CONTROL_CLASSES = "block w-full px-3 py-2 bg-gray-100 border border-gray-300 rounded-md shadow-sm sm:text-sm cursor-not-allowed"
TEXTAREA_CONTROL_CLASSES = "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-20"


class TourVoucherMasterForm(forms.ModelForm):
    # These fields are primarily for display and will be updated via calculation logic
    # Make them read-only in the form, as they are calculated or displayed.
    # The actual submission logic will handle setting these values.
    amt_bal_towards_company = forms.DecimalField(
        label="Amt. Bal Towards Company",
        required=False,
        widget=forms.TextInput(attrs={'class': READONLY_CONTROL_CLASSES, 'readonly': 'readonly'}),
        initial=0.00
    )
    amt_bal_towards_employee = forms.DecimalField(
        label="Amt. Bal Towards Employee",
        required=False,
        widget=forms.TextInput(attrs={'class': READONLY_CONTROL_CLASSES, 'readonly': 'readonly'}),
        initial=0.00
    )

    class Meta:
        model = TourVoucherMaster
        fields = ['amt_bal_towards_company', 'amt_bal_towards_employee']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # If an instance exists, populate initial values for calculated fields
        if self.instance and self.instance.pk:
            self.fields['amt_bal_towards_company'].initial = self.instance.amt_bal_towards_company
            self.fields['amt_bal_towards_employee'].initial = self.instance.amt_bal_towards_employee

# Form for Advance Details Grid (Sanctioned Expenses)
class TourVoucherAdvanceDetailsForm(forms.ModelForm):
    # These fields are actually editable in the grid
    # We need to explicitly add fields for the original data (Terms, Amount, Remarks)
    # as these are not directly on TourVoucherAdvanceDetails model but are needed for display.
    # These will be read-only fields.
    original_terms = forms.CharField(label="Terms", required=False,
                                     widget=forms.TextInput(attrs={'class': READONLY_CONTROL_CLASSES, 'readonly': 'readonly'}))
    original_amount = forms.DecimalField(label="Amount", required=False,
                                         widget=forms.TextInput(attrs={'class': READONLY_CONTROL_CLASSES, 'readonly': 'readonly'}))
    original_remarks = forms.CharField(label="Remarks", required=False,
                                       widget=forms.TextInput(attrs={'class': READONLY_CONTROL_CLASSES, 'readonly': 'readonly'}))

    class Meta:
        model = TourVoucherAdvanceDetails
        fields = ['id', 'expense_type', 'sanctioned_amount', 'remarks']
        widgets = {
            'id': forms.HiddenInput(), # Use HiddenInput to pass the PK
            'expense_type': forms.HiddenInput(), # Hidden to store the TDMId for mapping
            'sanctioned_amount': forms.NumberInput(attrs={'class': FORM_CONTROL_CLASSES, 'step': '0.01', 'min': '0'}),
            'remarks': forms.TextInput(attrs={'class': FORM_CONTROL_CLASSES}),
        }

    def __init__(self, *args, **kwargs):
        original_data = kwargs.pop('original_data', None)
        super().__init__(*args, **kwargs)
        if original_data:
            self.fields['original_terms'].initial = original_data.get('terms')
            self.fields['original_amount'].initial = original_data.get('amount')
            self.fields['original_remarks'].initial = original_data.get('remarks')
        # Ensure expense_type is passed for new instances if necessary
        if 'expense_type' in self.initial:
            self.fields['expense_type'].initial = self.initial['expense_type']

# Form for Advance Trans. To Grid (Sanctioned Employee Advances)
class TourVoucherAdvanceForm(forms.ModelForm):
    # As above, add fields for original data
    original_employee_name = forms.CharField(label="Employee Name", required=False,
                                            widget=forms.TextInput(attrs={'class': READONLY_CONTROL_CLASSES, 'readonly': 'readonly'}))
    original_amount = forms.DecimalField(label="Amount", required=False,
                                         widget=forms.TextInput(attrs={'class': READONLY_CONTROL_CLASSES, 'readonly': 'readonly'}))
    original_remarks = forms.CharField(label="Remarks", required=False,
                                       widget=forms.TextInput(attrs={'class': READONLY_CONTROL_CLASSES, 'readonly': 'readonly'}))

    class Meta:
        model = TourVoucherAdvance
        fields = ['id', 'tour_advance', 'sanctioned_amount', 'remarks']
        widgets = {
            'id': forms.HiddenInput(), # Use HiddenInput to pass the PK
            'tour_advance': forms.HiddenInput(), # Hidden to store the TAMId for mapping
            'sanctioned_amount': forms.NumberInput(attrs={'class': FORM_CONTROL_CLASSES, 'step': '0.01', 'min': '0'}),
            'remarks': forms.TextInput(attrs={'class': FORM_CONTROL_CLASSES}),
        }

    def __init__(self, *args, **kwargs):
        original_data = kwargs.pop('original_data', None)
        super().__init__(*args, **kwargs)
        if original_data:
            self.fields['original_employee_name'].initial = original_data.get('employee_name')
            self.fields['original_amount'].initial = original_data.get('amount')
            self.fields['original_remarks'].initial = original_data.get('remarks')
        # Ensure tour_advance is passed for new instances if necessary
        if 'tour_advance' in self.initial:
            self.fields['tour_advance'].initial = self.initial['tour_advance']


# Formsets for the grid data
# Use `extra=0` if no new rows can be added, or `extra=1` for one empty row.
# Given the ASP.NET code, rows are pre-populated from TourExpenseType or TourAdvance.
TourVoucherAdvanceDetailsFormSet = inlineformset_factory(
    TourVoucherMaster, TourVoucherAdvanceDetails,
    form=TourVoucherAdvanceDetailsForm,
    fields=['id', 'expense_type', 'sanctioned_amount', 'remarks'],
    extra=0, # No extra blank forms for existing rows
    can_delete=False # No delete functionality seen in ASP.NET
)

TourVoucherAdvanceFormSet = inlineformset_factory(
    TourVoucherMaster, TourVoucherAdvance,
    form=TourVoucherAdvanceForm,
    fields=['id', 'tour_advance', 'sanctioned_amount', 'remarks'],
    extra=0, # No extra blank forms for existing rows
    can_delete=False
)
```

#### 4.3 Views (`tour_management/views.py`)

The main view will be a `TemplateView` or `UpdateView` that handles both the display and the submission of the main form and its formsets. The `btnSum_Click` logic will be exposed via a dedicated HTMX endpoint.

```python
from django.views.generic import TemplateView, View
from django.shortcuts import get_object_or_404
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from django.db import transaction

from .models import (
    TourVoucherMaster, TourIntimationMaster, TourExpenseType, TourAdvance,
    TourVoucherAdvanceDetails, TourVoucherAdvance
)
from .forms import (
    TourVoucherMasterForm,
    TourVoucherAdvanceDetailsFormSet,
    TourVoucherAdvanceFormSet
)

# Helper function to prepare initial data for formsets
def prepare_advance_details_initial(tour_voucher_master):
    initial_data = []
    # Get all expense types
    expense_types = TourExpenseType.objects.all().order_by('id')
    for et in expense_types:
        original_adv = tour_voucher_master.tour_intimation_master.advance_details.filter(
            expense_type=et
        ).first()
        sanctioned_adv = TourVoucherAdvanceDetails.objects.filter(
            tour_voucher_master=tour_voucher_master,
            expense_type=et
        ).first()

        initial_row = {
            'expense_type': et.id, # Store TDMId
            'original_terms': et.terms,
            'original_amount': original_adv.amount if original_adv else 0.00,
            'original_remarks': original_adv.remarks if original_adv else '',
            'sanctioned_amount': sanctioned_adv.sanctioned_amount if sanctioned_adv else 0.00,
            'remarks': sanctioned_adv.remarks if sanctioned_adv else '',
            'id': sanctioned_adv.id if sanctioned_adv else None, # ID of the existing TVAD record
        }
        initial_data.append(initial_row)
    return initial_data

def prepare_advance_trans_to_initial(tour_voucher_master):
    initial_data = []
    # Get all existing TourVoucherAdvance entries for this master
    tour_voucher_advances = TourVoucherAdvance.objects.filter(
        tour_voucher_master=tour_voucher_master
    ).order_by('id')

    for tva in tour_voucher_advances:
        original_adv = tva.tour_advance # Already linked by FK
        initial_row = {
            'tour_advance': original_adv.id, # Store TAMId
            'original_employee_name': original_adv.employee.name,
            'original_amount': original_adv.amount,
            'original_remarks': original_adv.remarks,
            'sanctioned_amount': tva.sanctioned_amount,
            'remarks': tva.remarks,
            'id': tva.id, # ID of the existing TVA record
        }
        initial_data.append(initial_row)
    return initial_data


class TourVoucherDetailEditView(TemplateView):
    template_name = 'tour_management/tourvoucher_detail_edit.html'

    def get_object(self):
        """Retrieve TourVoucherMaster based on 'Id' from query string."""
        pk = self.kwargs.get('pk')
        return get_object_or_404(TourVoucherMaster, pk=pk)

    def get_initial_formset_data(self, request, tour_voucher_master):
        """
        Extracts formset data from request POST if present,
        otherwise prepares initial data from models.
        """
        # If POST request, data comes from request.POST
        if request.method == 'POST':
            # Formsets expect data in a specific format for parsing
            # We're passing request.POST and request.FILES directly to formset constructors
            return None, None # Let formsets handle data parsing from POST

        # For GET request, prepare initial data
        advance_details_initial = prepare_advance_details_initial(tour_voucher_master)
        advance_to_initial = prepare_advance_trans_to_initial(tour_voucher_master)
        return advance_details_initial, advance_to_initial

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        tour_voucher = self.get_object()
        tour_intimation = tour_voucher.tour_intimation_master

        # Prepare initial data for formsets if not POST, else pass POST data
        advance_details_initial, advance_to_initial = self.get_initial_formset_data(self.request, tour_voucher)

        # Main form
        context['form'] = TourVoucherMasterForm(
            instance=tour_voucher,
            prefix='master',
            data=self.request.POST if self.request.method == 'POST' else None
        )

        # Formsets
        context['advance_details_formset'] = TourVoucherAdvanceDetailsFormSet(
            instance=tour_voucher,
            prefix='advance_details',
            form_kwargs={'original_data': None}, # Will be set per-form in template or via a custom formset class
            initial=advance_details_initial if self.request.method == 'GET' else None,
            data=self.request.POST if self.request.method == 'POST' else None,
        )

        # Customizing initial data for each form in the formset manually in template
        # is complex for inlineformset_factory. A better approach is to provide it as initial
        # and then let the form's __init__ method use it.
        # So we adapt the formset creation logic to pass original_data for each form

        context['advance_to_formset'] = TourVoucherAdvanceFormSet(
            instance=tour_voucher,
            prefix='advance_to',
            form_kwargs={'original_data': None}, # Will be set per-form
            initial=advance_to_initial if self.request.method == 'GET' else None,
            data=self.request.POST if self.request.method == 'POST' else None,
        )

        # Pass tour intimation details
        context['tour_intimation'] = tour_intimation
        context['tour_voucher'] = tour_voucher # Also pass the main tour voucher object

        return context

    def post(self, request, *args, **kwargs):
        tour_voucher = self.get_object()

        form = TourVoucherMasterForm(request.POST, instance=tour_voucher, prefix='master')
        advance_details_formset = TourVoucherAdvanceDetailsFormSet(request.POST, instance=tour_voucher, prefix='advance_details')
        advance_to_formset = TourVoucherAdvanceFormSet(request.POST, instance=tour_voucher, prefix='advance_to')

        if form.is_valid() and advance_details_formset.is_valid() and advance_to_formset.is_valid():
            with transaction.atomic():
                # Extract sanctioned data from formsets
                advance_details_sanctioned_data = []
                for form_ad in advance_details_formset:
                    if form_ad.has_changed(): # Only process changed forms
                        data = form_ad.cleaned_data
                        if not data.get('DELETE'): # Not marked for deletion
                            advance_details_sanctioned_data.append({
                                'id': data.get('id'), # The ID of the existing TVAD object
                                'sanctioned_amount': data.get('sanctioned_amount'),
                                'sanctioned_remarks': data.get('remarks'),
                                'tdm_id': data.get('expense_type').id if data.get('expense_type') else None # The TDMId
                            })

                advance_to_sanctioned_data = []
                for form_at in advance_to_formset:
                    if form_at.has_changed():
                        data = form_at.cleaned_data
                        if not data.get('DELETE'):
                            advance_to_sanctioned_data.append({
                                'id': data.get('id'), # The ID of the existing TVA object
                                'sanctioned_amount': data.get('sanctioned_amount'),
                                'sanctioned_remarks': data.get('remarks'),
                                'tat_id': data.get('tour_advance').id if data.get('tour_advance') else None # The TAMId
                            })
                
                # Perform calculation and update balance amounts on the model
                # This should ideally be driven by the user submitting the Calculate button,
                # but for simplicity of full page submit, we recalculate here.
                calculated_balances = tour_voucher.calculate_balance_amounts(
                    advance_details_sanctioned_data,
                    advance_to_sanctioned_data
                )
                tour_voucher.amt_bal_towards_company = calculated_balances['amt_bal_company']
                tour_voucher.amt_bal_towards_employee = calculated_balances['amt_bal_employee']
                tour_voucher.save() # Save the balance fields to the master

                # Update the detail records (sanctioned amounts and remarks)
                tour_voucher.update_sanctioned_data(
                    advance_details_sanctioned_data,
                    advance_to_sanctioned_data,
                    request.user.username # Use Django user's username for SessionId
                )

                messages.success(request, "Tour Voucher details updated successfully.")
                
                # HTMX response for success
                if request.headers.get('HX-Request'):
                    return HttpResponse(
                        status=204,
                        headers={
                            'HX-Trigger': 'refreshTourVoucherList' # Trigger refresh on parent page
                        }
                    )
                return self.form_valid(form) # Fallback for non-HTMX
        else:
            messages.error(request, "Please correct the errors in the form.")
            # If invalid, re-render the template with errors (HTMX will swap)
            context = self.get_context_data(form=form, advance_details_formset=advance_details_formset, advance_to_formset=advance_to_formset)
            return self.render_to_response(context)


# HTMX endpoint for calculating balances
class TourVoucherCalculateView(View):
    def post(self, request, pk):
        tour_voucher = get_object_or_404(TourVoucherMaster, pk=pk)

        # Re-initialize formsets with POST data to get clean_data
        advance_details_formset = TourVoucherAdvanceDetailsFormSet(request.POST, instance=tour_voucher, prefix='advance_details')
        advance_to_formset = TourVoucherAdvanceFormSet(request.POST, instance=tour_voucher, prefix='advance_to')

        if advance_details_formset.is_valid() and advance_to_formset.is_valid():
            advance_details_sanctioned_data = []
            for form_ad in advance_details_formset:
                if not form_ad.cleaned_data.get('DELETE'):
                    advance_details_sanctioned_data.append({
                        'id': form_ad.cleaned_data.get('id'),
                        'sanctioned_amount': form_ad.cleaned_data.get('sanctioned_amount'),
                        'sanctioned_remarks': form_ad.cleaned_data.get('remarks'),
                        'tdm_id': form_ad.cleaned_data.get('expense_type').id if form_ad.cleaned_data.get('expense_type') else None
                    })

            advance_to_sanctioned_data = []
            for form_at in advance_to_formset:
                if not form_at.cleaned_data.get('DELETE'):
                    advance_to_sanctioned_data.append({
                        'id': form_at.cleaned_data.get('id'),
                        'sanctioned_amount': form_at.cleaned_data.get('sanctioned_amount'),
                        'sanctioned_remarks': form_at.cleaned_data.get('remarks'),
                        'tat_id': form_at.cleaned_data.get('tour_advance').id if form_at.cleaned_data.get('tour_advance') else None
                    })

            calculated_balances = tour_voucher.calculate_balance_amounts(
                advance_details_sanctioned_data,
                advance_to_sanctioned_data
            )
            
            # Update the form instance with calculated values for rendering
            form = TourVoucherMasterForm(instance=tour_voucher, prefix='master')
            form.fields['amt_bal_towards_company'].initial = calculated_balances['amt_bal_company']
            form.fields['amt_bal_towards_employee'].initial = calculated_balances['amt_bal_employee']
            
            # Re-render just the balance section
            context = {
                'total_advance_amount': calculated_balances['total_advance_amount'],
                'total_sanctioned_amount': calculated_balances['total_sanctioned_amount'],
                'form': form # Pass the form with updated initial values
            }
            html = render_to_string('tour_management/_balance_summary.html', context, request)
            return HttpResponse(html)
        else:
            # Handle invalid formsets for calculation if needed,
            # perhaps return an error message or re-render parts.
            return HttpResponse("<p class='text-red-500'>Invalid data for calculation. Please check inputs.</p>", status=400)


# Dummy view for the initial list page redirect
class TourVoucherListView(TemplateView):
    template_name = 'tour_management/tourvoucher_list.html' # This would be the "TourVoucher_Edit.aspx" equivalent

```

#### 4.4 Templates (`tour_management/templates/tour_management/`)

*   `tourvoucher_detail_edit.html`: Main page, displaying static details and the form with HTMX sections.
*   `_advance_details_grid.html`: Partial for the "Advance Details" grid.
*   `_advance_trans_to_grid.html`: Partial for the "Advance Trans. To" grid.
*   `_balance_summary.html`: Partial for the total amount and balance fields.
*   `tourvoucher_list.html`: A placeholder for the main list page to which this page redirects.

**`tourvoucher_detail_edit.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-gray-100 p-4 rounded-t-lg shadow-md">
        <h2 class="text-xl font-bold text-gray-800">Tour Voucher Edit Details</h2>
    </div>

    <form method="post" hx-post="{% url 'tour_management:tourvoucher_edit' tour_voucher.pk %}" hx-swap="outerHTML" hx-target="#mainContent" hx-trigger="submit">
        {% csrf_token %}
        <div id="mainContent" class="bg-white p-6 rounded-b-lg shadow-lg mb-6">
            <!-- Display Basic Tour Intimation Details (Read-only) -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Employee Name:</label>
                    <p class="mt-1 text-sm text-gray-900">{{ tour_intimation.employee }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">{{ tour_intimation.wo_or_bg_group|slice:":7" }}</label> {# "WO No" or "BG Group" #}
                    <p class="mt-1 text-sm text-gray-900">{{ tour_intimation.wo_or_bg_group|slice:"9:" }}</p> {# Actual value #}
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Project Name:</label>
                    <p class="mt-1 text-sm text-gray-900">{{ tour_intimation.project_name }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Place of Tour:</label>
                    <p class="mt-1 text-sm text-gray-900">{{ tour_intimation.formatted_place_of_tour }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Tour Start Date:</label>
                    <p class="mt-1 text-sm text-gray-900">{{ tour_intimation.tour_start_date|date:"d/m/Y" }} Time: {{ tour_intimation.tour_start_time|time:"H:i" }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Tour End Date:</label>
                    <p class="mt-1 text-sm text-gray-900">{{ tour_intimation.tour_end_date|date:"d/m/Y" }} Time: {{ tour_intimation.tour_end_time|time:"H:i" }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">No of Days:</label>
                    <p class="mt-1 text-sm text-gray-900">{{ tour_intimation.no_of_days }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Name & Address of Accommodation service provider:</label>
                    <p class="mt-1 text-sm text-gray-900">{{ tour_intimation.name_address_ser_provider }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Contact Person:</label>
                    <p class="mt-1 text-sm text-gray-900">{{ tour_intimation.contact_person }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Contact No:</label>
                    <p class="mt-1 text-sm text-gray-900">{{ tour_intimation.contact_no }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Email:</label>
                    <p class="mt-1 text-sm text-gray-900">{{ tour_intimation.email }}</p>
                </div>
            </div>

            <!-- Tabbed Interface for Grids -->
            <div x-data="{ activeTab: 'advanceDetails' }" class="mb-6">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                        <button type="button" @click="activeTab = 'advanceDetails'" :class="{ 'border-indigo-500 text-indigo-600': activeTab === 'advanceDetails', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'advanceDetails' }" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                            Advance Details
                        </button>
                        <button type="button" @click="activeTab = 'advanceTransTo'" :class="{ 'border-indigo-500 text-indigo-600': activeTab === 'advanceTransTo', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'advanceTransTo' }" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                            Advance Trans. To
                        </button>
                    </nav>
                </div>
                <div class="mt-4 p-4 border border-gray-200 rounded-lg bg-gray-50">
                    <div x-show="activeTab === 'advanceDetails'">
                        {% include 'tour_management/_advance_details_grid.html' %}
                    </div>
                    <div x-show="activeTab === 'advanceTransTo'">
                        {% include 'tour_management/_advance_trans_to_grid.html' %}
                    </div>
                </div>
            </div>

            <!-- Balance Summary and Buttons -->
            <div id="balance-summary"
                 hx-trigger="load, calculateEvent from:body"
                 hx-post="{% url 'tour_management:tourvoucher_calculate' tour_voucher.pk %}"
                 hx-include="[name^='advance_details-'], [name^='advance_to-']"
                 hx-target="#balance-summary"
                 hx-swap="outerHTML">
                {% include 'tour_management/_balance_summary.html' %}
            </div>

            <div class="mt-6 flex justify-center space-x-4">
                <button type="button" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                        hx-post="{% url 'tour_management:tourvoucher_calculate' tour_voucher.pk %}"
                        hx-include="[name^='advance_details-'], [name^='advance_to-']"
                        hx-target="#balance-summary"
                        hx-swap="outerHTML"
                        hx-indicator="#loadingIndicator">
                    Calculate
                </button>
                <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
                        hx-confirm="Are you sure you want to update these details?"
                        hx-indicator="#loadingIndicator">
                    Update
                </button>
                <a href="{% url 'tour_management:tourvoucher_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                    Cancel
                </a>
            </div>
            <div id="loadingIndicator" class="htmx-indicator text-center mt-4">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2">Processing...</p>
            </div>
            {% if messages %}
                <div class="mt-4">
                    {% for message in messages %}
                    <div class="p-4 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-700{% elif message.tags == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}">
                        {{ message }}
                    </div>
                    {% endfor %}
                </div>
            {% endif %}
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.tailwindcss.min.js"></script>
<link href="https://cdn.datatables.net/2.0.8/css/dataTables.tailwindcss.min.css" rel="stylesheet">
<script>
    document.addEventListener('htmx:afterSwap', function(evt) {
        // Re-initialize DataTables if a grid partial is swapped
        if (evt.detail.target.id === 'advance-details-table-container' || evt.detail.target.id === 'advance-trans-to-table-container') {
            $('#advanceDetailsTable').DataTable();
            $('#advanceTransToTable').DataTable();
        }
    });

    document.addEventListener('DOMContentLoaded', function() {
        // Initial DataTables setup on page load
        $('#advanceDetailsTable').DataTable();
        $('#advanceTransToTable').DataTable();
    });
</script>
{% endblock %}
```

**`_advance_details_grid.html`**

```html
<div id="advance-details-table-container">
    <table id="advanceDetailsTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Terms</th>
                <th class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks (Original)</th>
                <th class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Sanctioned Amount</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks (Sanctioned)</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {{ advance_details_formset.management_form }}
            {% for form in advance_details_formset %}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap text-right text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ form.original_terms.value }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-right text-sm text-gray-900">{{ form.original_amount.value|default:"0.00" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ form.original_remarks.value }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">
                    {{ form.id }}
                    {{ form.expense_type }}
                    {{ form.sanctioned_amount }}
                    {% if form.sanctioned_amount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.sanctioned_amount.errors }}</p>{% endif %}
                </td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">
                    {{ form.remarks }}
                    {% if form.remarks.errors %}<p class="text-red-500 text-xs mt-1">{{ form.remarks.errors }}</p>{% endif %}
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 px-4 text-center text-sm font-medium text-red-500">No advance details to display!</td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot class="bg-gray-50">
            <tr>
                <th colspan="2" class="py-2 px-4 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">Total Amount:</th>
                <th class="py-2 px-4 text-right text-xs font-medium text-gray-900 uppercase tracking-wider">
                    {{ tour_voucher.tour_intimation_master.advance_details.aggregate(total=Coalesce(Sum('amount'), 0.00, output_field=FloatField()))['total']|floatformat:2 }}
                </th>
                <th colspan="3" class="py-2 px-4"></th>
            </tr>
        </tfoot>
    </table>
</div>

<script>
// To ensure the formset data for original terms/amount is properly passed
// The `formset.initial` method in Django expects flat dicts, not nested for custom fields
// We need to ensure that the `original_data` argument to the form's __init__ is passed
// This usually means creating a custom formset, or passing it in the template loop
// For simplicity, for this example, we pass `original_data` during the initial formset creation
// in the view's get_context_data (which was commented out and needs to be handled via initial argument to formset).
// The current setup assumes form.original_terms.value will fetch from the form's initial data.
</script>
```

**`_advance_trans_to_grid.html`**

```html
<div id="advance-trans-to-table-container">
    <table id="advanceTransToTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
                <th class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks (Original)</th>
                <th class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Sanctioned Amount</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks (Sanctioned)</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {{ advance_to_formset.management_form }}
            {% for form in advance_to_formset %}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap text-right text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ form.original_employee_name.value }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-right text-sm text-gray-900">{{ form.original_amount.value|default:"0.00" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ form.original_remarks.value }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">
                    {{ form.id }}
                    {{ form.tour_advance }}
                    {{ form.sanctioned_amount }}
                    {% if form.sanctioned_amount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.sanctioned_amount.errors }}</p>{% endif %}
                </td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">
                    {{ form.remarks }}
                    {% if form.remarks.errors %}<p class="text-red-500 text-xs mt-1">{{ form.remarks.errors }}</p>{% endif %}
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 px-4 text-center text-sm font-medium text-red-500">No advance transfers to display!</td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot class="bg-gray-50">
            <tr>
                <th colspan="2" class="py-2 px-4 text-right text-xs font-medium text-gray-700 uppercase tracking-wider">Total Amount:</th>
                <th class="py-2 px-4 text-right text-xs font-medium text-gray-900 uppercase tracking-wider">
                    {% with advance_ids=tour_voucher.advance_sanction_to.values_list('tour_advance__id', flat=True) %}
                        {% with total_original_amount=advance_ids|length|yesno:tour_management.TourAdvance.objects.filter(id__in=advance_ids).aggregate(total=Coalesce(Sum('amount'), 0.00, output_field=FloatField()))['total'],"0.00" %}
                            {{ total_original_amount|floatformat:2 }}
                        {% endwith %}
                    {% endwith %}
                </th>
                <th colspan="3" class="py-2 px-4"></th>
            </tr>
        </tfoot>
    </table>
</div>
```

**`_balance_summary.html`**

```html
<div class="flex flex-wrap items-center justify-start gap-4 mb-6 text-sm font-bold text-gray-800">
    <div>
        Total Advance Amt : <span class="font-normal">{{ total_advance_amount|default:"0.00"|floatformat:2 }}</span>
    </div>
    <div>
        Total Sanctioned Amount : <span class="font-normal">{{ total_sanctioned_amount|default:"0.00"|floatformat:2 }}</span>
    </div>
    <div>
        {{ form.amt_bal_towards_company.label }}: {{ form.amt_bal_towards_company }}
    </div>
    <div>
        {{ form.amt_bal_towards_employee.label }}: {{ form.amt_bal_towards_employee }}
    </div>
</div>
```

**`tourvoucher_list.html`** (Placeholder for redirect target)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6">Tour Voucher List (From TourVoucher_Edit.aspx)</h2>
    <p>This page represents the list view where tour vouchers are displayed.</p>
    <p>The previous page redirected here after a successful update.</p>
    <div class="p-4 bg-blue-100 text-blue-700 rounded-md">
        This is a placeholder. In a real application, this would be a DataTables list of Tour Vouchers with links to edit.
    </div>
</div>
{% endblock %}
```

#### 4.5 URLs (`tour_management/urls.py`)

```python
from django.urls import path
from .views import TourVoucherDetailEditView, TourVoucherCalculateView, TourVoucherListView

app_name = 'tour_management'

urlpatterns = [
    # Main edit page for a specific tour voucher
    path('tour-voucher/edit/<int:pk>/', TourVoucherDetailEditView.as_view(), name='tourvoucher_edit'),
    # HTMX endpoint for recalculating balance amounts
    path('tour-voucher/calculate/<int:pk>/', TourVoucherCalculateView.as_view(), name='tourvoucher_calculate'),
    # Placeholder for the main list page to which the application redirects after update/cancel
    path('tour-vouchers/', TourVoucherListView.as_view(), name='tourvoucher_list'),
]
```
Ensure your main `project/urls.py` includes this app's URLs:
`path('app/', include('tour_management.urls')),`

#### 4.6 Tests (`tour_management/tests.py`)

Comprehensive tests for models (fat model methods) and views (form submission, HTMX interactions).

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import date, time
from decimal import Decimal

from .models import (
    Company, FinancialYear, Employee, BusinessGroup, Country, State, City,
    TourIntimationMaster, TourExpenseType, TourAdvanceDetails, TourAdvance,
    TourVoucherMaster, TourVoucherAdvanceDetails, TourVoucherAdvance
)

class TourVoucherModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal required related data for all tests
        cls.company = Company.objects.create(id=1, name='Test Company')
        cls.fin_year = FinancialYear.objects.create(id=1, year_name='2024-2025')
        cls.employee = Employee.objects.create(id=101, title='Mr.', name='John Doe')
        cls.business_group = BusinessGroup.objects.create(id=1, name='Default Group', symbol='DG')
        cls.country = Country.objects.create(id=1, name='USA')
        cls.state = State.objects.create(id=1, name='California')
        cls.city = City.objects.create(id=1, name='San Francisco')

        # Create TourIntimationMaster
        cls.tour_intimation = TourIntimationMaster.objects.create(
            id=1001,
            employee=cls.employee,
            wo_no='WO-123',
            business_group=cls.business_group,
            project_name='Project Alpha',
            place_of_tour_city=cls.city,
            place_of_tour_state=cls.state,
            place_of_tour_country=cls.country,
            tour_start_date=date(2024, 7, 1),
            tour_start_time=time(9, 0, 0),
            tour_end_date=date(2024, 7, 5),
            tour_end_time=time(17, 0, 0),
            no_of_days=5,
            name_address_ser_provider='Grand Hotel, 123 Main St',
            contact_person='Jane Smith',
            contact_no='555-1234',
            email='<EMAIL>',
            compid=cls.company
        )

        # Create TourVoucherMaster
        cls.tour_voucher = TourVoucherMaster.objects.create(
            id=2001,
            tour_intimation_master=cls.tour_intimation,
            amt_bal_towards_company=Decimal('0.00'),
            amt_bal_towards_employee=Decimal('0.00'),
            sys_date=timezone.localdate(),
            sys_time=timezone.localtime().time(),
            session_id='testuser',
            company=cls.company
        )

        # Create TourExpenseType and TourAdvanceDetails
        cls.exp_type_food = TourExpenseType.objects.create(id=1, terms='Food')
        cls.exp_type_travel = TourExpenseType.objects.create(id=2, terms='Travel')
        TourAdvanceDetails.objects.create(
            tour_intimation_master=cls.tour_intimation,
            expense_type=cls.exp_type_food,
            amount=Decimal('100.00'),
            remarks='Original food expense'
        )
        TourAdvanceDetails.objects.create(
            tour_intimation_master=cls.tour_intimation,
            expense_type=cls.exp_type_travel,
            amount=Decimal('200.00'),
            remarks='Original travel expense'
        )

        # Create TourAdvance (for Advance Trans. To)
        cls.adv_emp_bob = TourAdvance.objects.create(
            id=3001,
            employee=Employee.objects.create(id=102, name='Bob Johnson'),
            amount=Decimal('150.00'),
            remarks='Advance to Bob'
        )
        cls.adv_emp_alice = TourAdvance.objects.create(
            id=3002,
            employee=Employee.objects.create(id=103, name='Alice Green'),
            amount=Decimal('50.00'),
            remarks='Advance to Alice'
        )

        # Create existing TourVoucherAdvanceDetails and TourVoucherAdvance (sanctioned)
        TourVoucherAdvanceDetails.objects.create(
            id=4001,
            tour_voucher_master=cls.tour_voucher,
            expense_type=cls.exp_type_food,
            sanctioned_amount=Decimal('90.00'),
            remarks='Sanctioned food'
        )
        TourVoucherAdvance.objects.create(
            id=5001,
            tour_voucher_master=cls.tour_voucher,
            tour_advance=cls.adv_emp_bob,
            sanctioned_amount=Decimal('140.00'),
            remarks='Sanctioned Bob advance'
        )


    def test_tour_voucher_creation(self):
        tv = TourVoucherMaster.objects.get(id=self.tour_voucher.id)
        self.assertEqual(tv.tour_intimation_master.id, self.tour_intimation.id)
        self.assertEqual(tv.amt_bal_towards_company, Decimal('0.00'))
        self.assertEqual(tv.session_id, 'testuser')

    def test_tour_intimation_properties(self):
        self.assertEqual(self.tour_intimation.formatted_place_of_tour, 'USA, California, San Francisco')
        self.assertEqual(self.tour_intimation.wo_or_bg_group, 'WO No: WO-123')
        # Test BG group case
        self.tour_intimation.wo_no = 'NA'
        self.tour_intimation.save()
        self.assertEqual(self.tour_intimation.wo_or_bg_group, f"BG Group: {self.business_group.name} [{self.business_group.symbol}]")


    def test_get_advance_details_data(self):
        data = self.tour_voucher.get_advance_details_data()
        self.assertEqual(len(data), 2) # Should include both Food and Travel expense types
        food_data = next((item for item in data if item['terms'] == 'Food'), None)
        travel_data = next((item for item in data if item['terms'] == 'Travel'), None)

        self.assertIsNotNone(food_data)
        self.assertEqual(food_data['amount'], Decimal('100.00'))
        self.assertEqual(food_data['sanctioned_amount'], Decimal('90.00'))
        self.assertEqual(food_data['sanctioned_remarks'], 'Sanctioned food')

        self.assertIsNotNone(travel_data)
        self.assertEqual(travel_data['amount'], Decimal('200.00'))
        self.assertEqual(travel_data['sanctioned_amount'], Decimal('0.00')) # Not sanctioned yet
        self.assertEqual(travel_data['sanctioned_remarks'], '')

    def test_get_advance_trans_to_data(self):
        data = self.tour_voucher.get_advance_trans_to_data()
        self.assertEqual(len(data), 1) # Only Bob was sanctioned

        bob_data = next((item for item in data if item['employee_name'] == 'Bob Johnson'), None)
        self.assertIsNotNone(bob_data)
        self.assertEqual(bob_data['amount'], Decimal('150.00'))
        self.assertEqual(bob_data['sanctioned_amount'], Decimal('140.00'))
        self.assertEqual(bob_data['sanctioned_remarks'], 'Sanctioned Bob advance')

    def test_calculate_balance_amounts(self):
        # Test case 1: Employee owes company
        adv_details_sanctioned = [{'sanctioned_amount': Decimal('150.00')}, {'sanctioned_amount': Decimal('250.00')}]
        adv_to_sanctioned = [{'sanctioned_amount': Decimal('100.00')}]
        
        # Original: Food (100) + Travel (200) + Bob (150) + Alice (50) = 500
        # Sanctioned: 150 + 250 + 100 = 500
        # If all items are sanctioned. For a more precise test, I'd need to mock the initial values of TourAdvanceDetails/TourAdvance.
        # But `calculate_balance_amounts` pulls actual DB data for original amounts.
        # So let's provide realistic sanctioned amounts matching original total.

        # Total original advance = 100 (food) + 200 (travel) + 150 (bob) + 50 (alice, if she was added)
        # However, `calculate_balance_amounts` only sums `TourAdvanceDetails` for the *current* tour intimation
        # and `TourAdvance` records that are linked via `TourVoucherAdvance`.
        # So original for this voucher:
        # TADM: 100 (food) + 200 (travel) = 300
        # TVA: 150 (bob) = 150
        # Total original = 450

        # Sanctioned for this test:
        # adv_details_sanctioned = Food (150), Travel (250) -> 400
        # adv_to_sanctioned = Bob (100) -> 100
        # Total sanctioned = 500

        results = self.tour_voucher.calculate_balance_amounts(adv_details_sanctioned, adv_to_sanctioned)
        self.assertAlmostEqual(results['total_advance_amount'], Decimal('450.00'))
        self.assertAlmostEqual(results['total_sanctioned_amount'], Decimal('500.00'))
        self.assertAlmostEqual(results['amt_bal_company'], Decimal('50.00'))
        self.assertAlmostEqual(results['amt_bal_employee'], Decimal('0.00'))

        # Test case 2: Company owes employee (Sanctioned < Original)
        adv_details_sanctioned_2 = [{'sanctioned_amount': Decimal('50.00')}, {'sanctioned_amount': Decimal('100.00')}]
        adv_to_sanctioned_2 = [{'sanctioned_amount': Decimal('75.00')}]
        # Total sanctioned = 50 + 100 + 75 = 225
        results_2 = self.tour_voucher.calculate_balance_amounts(adv_details_sanctioned_2, adv_to_sanctioned_2)
        self.assertAlmostEqual(results_2['total_advance_amount'], Decimal('450.00'))
        self.assertAlmostEqual(results_2['total_sanctioned_amount'], Decimal('225.00'))
        self.assertAlmostEqual(results_2['amt_bal_company'], Decimal('0.00'))
        self.assertAlmostEqual(results_2['amt_bal_employee'], Decimal('225.00'))

    def test_update_sanctioned_data(self):
        new_adv_details = [
            {'id': 4001, 'sanctioned_amount': Decimal('120.00'), 'sanctioned_remarks': 'Updated food', 'tdm_id': self.exp_type_food.id},
            {'id': None, 'sanctioned_amount': Decimal('180.00'), 'sanctioned_remarks': 'Sanctioned new travel', 'tdm_id': self.exp_type_travel.id},
        ]
        new_adv_to = [
            {'id': 5001, 'sanctioned_amount': Decimal('130.00'), 'sanctioned_remarks': 'Updated Bob', 'tat_id': self.adv_emp_bob.id},
            {'id': None, 'sanctioned_amount': Decimal('60.00'), 'sanctioned_remarks': 'Sanctioned Alice', 'tat_id': self.adv_emp_alice.id},
        ]

        self.tour_voucher.update_sanctioned_data(new_adv_details, new_adv_to, 'testupdater')

        updated_tv = TourVoucherMaster.objects.get(id=self.tour_voucher.id)
        self.assertEqual(updated_tv.session_id, 'testupdater')

        food_adv = TourVoucherAdvanceDetails.objects.get(id=4001)
        self.assertAlmostEqual(food_adv.sanctioned_amount, Decimal('120.00'))
        self.assertEqual(food_adv.remarks, 'Updated food')

        travel_adv = TourVoucherAdvanceDetails.objects.get(tour_voucher_master=self.tour_voucher, expense_type=self.exp_type_travel)
        self.assertIsNotNone(travel_adv) # Should create new if ID is None
        self.assertAlmostEqual(travel_adv.sanctioned_amount, Decimal('180.00'))
        self.assertEqual(travel_adv.remarks, 'Sanctioned new travel')

        bob_adv = TourVoucherAdvance.objects.get(id=5001)
        self.assertAlmostEqual(bob_adv.sanctioned_amount, Decimal('130.00'))
        self.assertEqual(bob_adv.remarks, 'Updated Bob')

        alice_adv = TourVoucherAdvance.objects.get(tour_voucher_master=self.tour_voucher, tour_advance=self.adv_emp_alice)
        self.assertIsNotNone(alice_adv) # Should create new
        self.assertAlmostEqual(alice_adv.sanctioned_amount, Decimal('60.00'))
        self.assertEqual(alice_adv.remarks, 'Sanctioned Alice')


class TourVoucherViewsTest(TestCase):
    client = Client()

    @classmethod
    def setUpTestData(cls):
        cls.company = Company.objects.create(id=1, name='Test Company')
        cls.employee = Employee.objects.create(id=101, title='Mr.', name='John Doe')
        cls.business_group = BusinessGroup.objects.create(id=1, name='Default Group', symbol='DG')
        cls.country = Country.objects.create(id=1, name='USA')
        cls.state = State.objects.create(id=1, name='California')
        cls.city = City.objects.create(id=1, name='San Francisco')

        cls.tour_intimation = TourIntimationMaster.objects.create(
            id=1001, employee=cls.employee, project_name='Test Project',
            tour_start_date=date(2024, 7, 1), tour_end_date=date(2024, 7, 5),
            place_of_tour_city=cls.city, place_of_tour_state=cls.state,
            place_of_tour_country=cls.country, compid=cls.company
        )
        cls.tour_voucher = TourVoucherMaster.objects.create(
            id=2001, tour_intimation_master=cls.tour_intimation, company=cls.company
        )

        cls.exp_type_food = TourExpenseType.objects.create(id=1, terms='Food')
        cls.exp_type_travel = TourExpenseType.objects.create(id=2, terms='Travel')

        TourAdvanceDetails.objects.create(
            tour_intimation_master=cls.tour_intimation, expense_type=cls.exp_type_food, amount=Decimal('100.00')
        )
        TourAdvanceDetails.objects.create(
            tour_intimation_master=cls.tour_intimation, expense_type=cls.exp_type_travel, amount=Decimal('200.00')
        )

        cls.adv_emp_bob = TourAdvance.objects.create(id=3001, employee=Employee.objects.create(id=102, name='Bob'), amount=Decimal('150.00'))
        cls.adv_emp_alice = TourAdvance.objects.create(id=3002, employee=Employee.objects.create(id=103, name='Alice'), amount=Decimal('50.00'))

        TourVoucherAdvanceDetails.objects.create(
            id=4001, tour_voucher_master=cls.tour_voucher, expense_type=cls.exp_type_food, sanctioned_amount=Decimal('90.00')
        )
        TourVoucherAdvance.objects.create(
            id=5001, tour_voucher_master=cls.tour_voucher, tour_advance=cls.adv_emp_bob, sanctioned_amount=Decimal('140.00')
        )

    def test_detail_edit_view_get(self):
        url = reverse('tour_management:tourvoucher_edit', args=[self.tour_voucher.pk])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'tour_management/tourvoucher_detail_edit.html')
        self.assertIn('tour_voucher', response.context)
        self.assertIn('tour_intimation', response.context)
        self.assertIn('form', response.context)
        self.assertIn('advance_details_formset', response.context)
        self.assertIn('advance_to_formset', response.context)

    def test_detail_edit_view_post_success(self):
        url = reverse('tour_management:tourvoucher_edit', args=[self.tour_voucher.pk])

        # Prepare form data
        form_data = {
            'master-amt_bal_towards_company': '0.00', # These will be overridden by calculation
            'master-amt_bal_towards_employee': '0.00',
            # Advance Details Formset Data
            'advance_details-TOTAL_FORMS': '2', # Food and Travel
            'advance_details-INITIAL_FORMS': '2',
            'advance_details-MIN_NUM_FORMS': '0',
            'advance_details-MAX_NUM_FORMS': '1000',
            # Form 0 (Food) - existing
            'advance_details-0-id': '4001',
            'advance_details-0-expense_type': self.exp_type_food.id,
            'advance_details-0-sanctioned_amount': '95.00',
            'advance_details-0-remarks': 'Updated food remarks',
            # Form 1 (Travel) - new, but should match existing expense type
            'advance_details-1-id': '', # New entry if not existing in TVAD
            'advance_details-1-expense_type': self.exp_type_travel.id,
            'advance_details-1-sanctioned_amount': '210.00',
            'advance_details-1-remarks': 'Sanctioned travel new',

            # Advance Trans. To Formset Data
            'advance_to-TOTAL_FORMS': '2', # Bob and Alice
            'advance_to-INITIAL_FORMS': '2',
            'advance_to-MIN_NUM_FORMS': '0',
            'advance_to-MAX_NUM_FORMS': '1000',
            # Form 0 (Bob) - existing
            'advance_to-0-id': '5001',
            'advance_to-0-tour_advance': self.adv_emp_bob.id,
            'advance_to-0-sanctioned_amount': '145.00',
            'advance_to-0-remarks': 'Updated Bob remarks',
            # Form 1 (Alice) - new
            'advance_to-1-id': '', # New entry if not existing in TVA
            'advance_to-1-tour_advance': self.adv_emp_alice.id,
            'advance_to-1-sanctioned_amount': '55.00',
            'advance_to-1-remarks': 'Sanctioned Alice new',
        }

        # Simulate HTMX request
        response = self.client.post(url, data=form_data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 204) # No content, indicates success for HTMX
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshTourVoucherList')

        # Verify data was updated in the database
        updated_voucher = TourVoucherMaster.objects.get(pk=self.tour_voucher.pk)
        self.assertNotEqual(updated_voucher.amt_bal_towards_company, Decimal('0.00')) # Should be calculated
        # Calculated based on mock data: Original = 100+200+150+50 = 500
        # Sanctioned = 95+210+145+55 = 505
        # Company owes employee: 0, Employee owes company: 5.00
        self.assertAlmostEqual(updated_voucher.amt_bal_towards_company, Decimal('5.00'))
        self.assertAlmostEqual(updated_voucher.amt_bal_towards_employee, Decimal('0.00'))

        adv_details_food = TourVoucherAdvanceDetails.objects.get(id=4001)
        self.assertAlmostEqual(adv_details_food.sanctioned_amount, Decimal('95.00'))
        self.assertEqual(adv_details_food.remarks, 'Updated food remarks')

        adv_details_travel = TourVoucherAdvanceDetails.objects.get(
            tour_voucher_master=updated_voucher, expense_type=self.exp_type_travel)
        self.assertAlmostEqual(adv_details_travel.sanctioned_amount, Decimal('210.00'))

        adv_to_bob = TourVoucherAdvance.objects.get(id=5001)
        self.assertAlmostEqual(adv_to_bob.sanctioned_amount, Decimal('145.00'))

        adv_to_alice = TourVoucherAdvance.objects.get(
            tour_voucher_master=updated_voucher, tour_advance=self.adv_emp_alice)
        self.assertAlmostEqual(adv_to_alice.sanctioned_amount, Decimal('55.00'))

    def test_calculate_view_post_success(self):
        url = reverse('tour_management:tourvoucher_calculate', args=[self.tour_voucher.pk])

        form_data = {
            # Advance Details Formset Data
            'advance_details-TOTAL_FORMS': '2',
            'advance_details-INITIAL_FORMS': '2',
            'advance_details-MIN_NUM_FORMS': '0',
            'advance_details-MAX_NUM_FORMS': '1000',
            'advance_details-0-id': '4001',
            'advance_details-0-expense_type': self.exp_type_food.id,
            'advance_details-0-sanctioned_amount': '80.00', # Changed
            'advance_details-0-remarks': 'Food rem',
            'advance_details-1-id': '',
            'advance_details-1-expense_type': self.exp_type_travel.id,
            'advance_details-1-sanctioned_amount': '190.00', # Changed
            'advance_details-1-remarks': 'Travel rem',

            # Advance Trans. To Formset Data
            'advance_to-TOTAL_FORMS': '2',
            'advance_to-INITIAL_FORMS': '2',
            'advance_to-MIN_NUM_FORMS': '0',
            'advance_to-MAX_NUM_FORMS': '1000',
            'advance_to-0-id': '5001',
            'advance_to-0-tour_advance': self.adv_emp_bob.id,
            'advance_to-0-sanctioned_amount': '100.00', # Changed
            'advance_to-0-remarks': 'Bob rem',
            'advance_to-1-id': '',
            'advance_to-1-tour_advance': self.adv_emp_alice.id,
            'advance_to-1-sanctioned_amount': '40.00', # Changed
            'advance_to-1-remarks': 'Alice rem',
        }
        
        response = self.client.post(url, data=form_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertIn('Total Advance Amt', response.content.decode())
        self.assertIn('Total Sanctioned Amount', response.content.decode())
        self.assertIn('Amt. Bal Towards Company', response.content.decode())
        self.assertIn('Amt. Bal Towards Employee', response.content.decode())

        # Original: 100+200+150+50 = 500
        # Sanctioned: 80+190+100+40 = 410
        # Employee owes company: 0, Amt. Bal Towards Employee: 90
        
        self.assertIn('<span class="font-normal">500.00</span>', response.content.decode()) # Total Advance
        self.assertIn('<span class="font-normal">410.00</span>', response.content.decode()) # Total Sanctioned
        self.assertIn('value="0.00"', response.content.decode()) # Company balance
        self.assertIn('value="90.00"', response.content.decode()) # Employee balance

    def test_calculate_view_post_invalid_data(self):
        url = reverse('tour_management:tourvoucher_calculate', args=[self.tour_voucher.pk])
        form_data = {
            'advance_details-TOTAL_FORMS': '1', # Missing forms
            'advance_details-INITIAL_FORMS': '0',
            'advance_details-0-id': '4001',
            'advance_details-0-expense_type': self.exp_type_food.id,
            'advance_details-0-sanctioned_amount': 'invalid_amount', # Invalid data
        }
        response = self.client.post(url, data=form_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400)
        self.assertIn('Invalid data for calculation.', response.content.decode())

    def test_cancel_redirect(self):
        url = reverse('tour_management:tourvoucher_edit', args=[self.tour_voucher.pk])
        response = self.client.get(url)
        # Check if the cancel button link is correct
        self.assertContains(response, reverse('tour_management:tourvoucher_list'))

```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for Form Submission**: The main `form` uses `hx-post` to submit to the `tourvoucher_edit` URL. On success, `hx-trigger="refreshTourVoucherList"` is sent to the client, which can be listened for by a parent page to refresh its content (e.g., a DataTables list of tour vouchers).
*   **HTMX for Calculation**: The "Calculate" button sends an `hx-post` request to `tourvoucher_calculate`. It includes only the form fields relevant to the calculation (`[name^='advance_details-'], [name^='advance_to-']`). The response is then swapped back into the `balance-summary` div, dynamically updating the total and balance fields.
*   **DataTables**: Integrated into `_advance_details_grid.html` and `_advance_trans_to_grid.html` for client-side searching, sorting, and pagination. The `DOMContentLoaded` and `htmx:afterSwap` listeners ensure DataTables is initialized correctly.
*   **Alpine.js for Tabs**: The tabbed interface (`AjaxControlToolkit:TabContainer`) is re-implemented using Alpine.js's `x-data` and `x-show` directives for local UI state management, completely on the frontend.
*   **No custom JavaScript**: All interactions are managed by HTMX and Alpine.js directives, reducing the need for verbose, imperative JavaScript.
*   **DRY Template Inheritance**: All templates extend `core/base.html`, ensuring consistent header, footer, and CDN includes (HTMX, Alpine.js, jQuery, DataTables).

### Final Notes

*   This plan provides a comprehensive framework for migrating the ASP.NET page. The `fillgrid()` and `FillGridAdvanceTo()` logic from the C# code-behind is now encapsulated within the `TourVoucherMaster` model's methods (`get_advance_details_data`, `get_advance_trans_to_data`) and prepared as initial data for formsets in the view.
*   The `clsFunctions fun` class from ASP.NET (e.g., `fun.Connection()`, `fun.select()`, `fun.FromDateDMY()`, `fun.getCurrDate()`, `fun.NumberValidationQty()`) is replaced by Django's ORM, built-in date/time functions, and form validation.
*   The use of `id` and `TIMId` from the query string is handled by Django's URL routing (`<int:pk>`).
*   The structure is designed for extensibility and maintainability, aligning with modern Django and web development best practices.