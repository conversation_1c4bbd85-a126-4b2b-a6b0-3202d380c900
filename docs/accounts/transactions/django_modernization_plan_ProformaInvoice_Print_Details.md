## ASP.NET to Django Conversion Script: Proforma Invoice Print Details

This modernization plan outlines the conversion of your legacy ASP.NET "Proforma Invoice - Print Details" page to a modern Django application. Our strategy prioritizes clarity, automation, and a robust, scalable Django architecture using the "fat model, thin view" approach.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is extended.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination (where applicable, for list views).
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in generating the Proforma Invoice report.

**Analysis:**
The ASP.NET code primarily fetches data from `tblACC_ProformaInvoice_Master` and then joins/relates it with several other tables to compile the complete invoice details.

**Primary Table:** `tblACC_ProformaInvoice_Master`
- `Id` (Primary Key)
- `SysDate` (System Date)
- `CompId` (Company ID)
- `InvoiceNo` (Invoice Number)
- `PONo` (Purchase Order Number)
- `WONo` (Work Order Number - comma-separated IDs)
- `InvoiceMode`
- `DateOfIssueInvoice`
- `TimeOfIssueInvoice`
- `CustomerCode`
- `Buyer_name`, `Buyer_cotper`, `Buyer_ph`, `Buyer_email`, `Buyer_ecc`, `Buyer_tin`, `Buyer_mob`, `Buyer_fax`, `Buyer_vat`, `Buyer_add`, `Buyer_country`, `Buyer_state`, `Buyer_city`
- `Cong_name`, `Cong_cotper`, `Cong_ph`, `Cong_email`, `Cong_ecc`, `Cong_tin`, `Cong_mob`, `Cong_fax`, `Cong_vat`, `Cong_add`, `Cong_country`, `Cong_state`, `Cong_city`
- `AddType`
- `AddAmt`
- `DeductionType`
- `Deduction`
- `POId` (Purchase Order ID)
- `FinYearId` (Financial Year ID)

**Related Tables (inferred from joins and `fun.select` calls):**
- `SD_Cust_WorkOrder_Master`: Contains `WONo` based on `Id`.
- `SD_Cust_PO_Master`: Contains `SysDate` (PO Date) based on `POId`.
- `tblFinancial_master`: Contains `FinYearFrom`, `FinYearTo` based on `CompId` and `FinYearId`.
- `tblcountry`: Contains `CountryName` based on `CId` (for Buyer, Consignee, Company addresses).
- `tblState`: Contains `StateName` based on `SId` (for Buyer, Consignee, Company addresses).
- `tblCity`: Contains `CityName` based on `CityId` (for Buyer, Consignee, Company addresses).
- `tblCompany_master`: Contains `RegdAddress`, `RegdCity`, `RegdState`, `RegdCountry`, `RegdPinCode`, `RegdContactNo`, `RegdFaxNo`, `RegdEmail` based on `CompId`.

### Step 2: Identify Backend Functionality

**Task:** Determine the operations performed by the ASP.NET code.

**Analysis:**
The ASP.NET page `ProformaInvoice_Print_Details.aspx` is exclusively a **Read/Display** operation. It does not perform any Create, Update, or Delete actions on the Proforma Invoice record itself. Its sole purpose is to retrieve comprehensive details for a *specific* Proforma Invoice (identified by `InvId`, `InvNo`, `CompId` from query strings and session) and present them in a report format (originally Crystal Reports). The "Cancel" button simply redirects back to a list of invoices.

**Inferred Data Flow:**
1.  Receive `Invoice ID`, `Invoice Number`, `Company ID`, `Financial Year ID`, `Print Type` as input parameters.
2.  Query `tblACC_ProformaInvoice_Master` for the main invoice details.
3.  Perform multiple sub-queries/lookups to fetch related data:
    -   Work Order Numbers (parsing a comma-separated string).
    -   Purchase Order Date.
    -   Financial Year string for invoice number suffix.
    -   Full addresses (Buyer, Consignee, Company) by looking up City, State, Country IDs.
4.  Consolidate all this data into a structured format.
5.  Pass this formatted data to a report viewer (in Django, this will be rendered to an HTML template).

**Validation Logic:** No explicit validation logic is found for input, as it's a display page. Data integrity is assumed to be handled at creation.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**
The original page is simple:
-   A `CR:CrystalReportViewer` is the primary component, displaying the generated report.
-   An `asp:Panel` contains the report viewer, providing scrollability.
-   An `asp:Button` (`btnCancel`) to navigate back.

**Django Equivalent UI Components:**
-   **Display:** A detailed HTML template will replace the Crystal Report Viewer. This template will dynamically render all the compiled invoice information retrieved from the backend.
-   **Navigation:** A simple link or button will replace the `btnCancel` functionality, leading back to a theoretical Proforma Invoice list page.
-   **Styling:** Tailwind CSS will be used for modern, responsive styling. HTMX and Alpine.js are not strictly necessary for a static print-detail view, but they are crucial for a fully dynamic CRUD application. For this specific page, HTMX could be used if print previews were loaded dynamically, but a direct render is simpler for a print view. Given the prompt's general requirement for HTMX/Alpine, we will provide the structure for other CRUD operations even if this specific page doesn't directly use them for *its core functionality*.

---

### Step 4: Generate Django Code

We will create a new Django app named `accounts` to house the Proforma Invoice functionality.

#### 4.1 Models (`accounts/models.py`)

**Task:** Create Django models for Proforma Invoice and its related entities. The `ProformaInvoice` model will include methods to aggregate data for the report, demonstrating the "fat model" principle.

**Instructions:**
-   `ProformaInvoice` model maps to `tblACC_ProformaInvoice_Master`.
-   Placeholder models are created for related tables (`Country`, `State`, `City`, `CompanyMaster`, `FinancialMaster`, `WorkOrderMaster`, `PurchaseOrderMaster`) to enable relationships and data retrieval. These should be replaced with your actual full models if they exist elsewhere in your database.
-   A `get_report_details` method is added to the `ProformaInvoice` model to encapsulate the complex data aggregation logic from the ASP.NET code-behind, including fetching related addresses, financial year, and parsing work order numbers.

```python
from django.db import models
from django.utils import timezone
from datetime import datetime

# Placeholder models for related tables.
# In a real migration, these would be your actual models for these tables.
# Assuming basic fields needed for lookups as seen in ASP.NET code.

class Country(models.Model):
    id = models.IntegerField(db_column='CId', primary_key=True)
    name = models.CharField(db_column='CountryName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblcountry'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.name

class State(models.Model):
    id = models.IntegerField(db_column='SId', primary_key=True)
    name = models.CharField(db_column='StateName', max_length=100)
    country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='CId', related_name='states', null=True)

    class Meta:
        managed = False
        db_table = 'tblState'
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.name

class City(models.Model):
    id = models.IntegerField(db_column='CityId', primary_key=True)
    name = models.CharField(db_column='CityName', max_length=100)
    state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='SId', related_name='cities', null=True)

    class Meta:
        managed = False
        db_table = 'tblCity'
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.name

class CompanyMaster(models.Model):
    id = models.IntegerField(db_column='CompId', primary_key=True)
    regd_address = models.CharField(db_column='RegdAddress', max_length=255)
    regd_city = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='RegdCity', null=True, related_name='company_regd_cities')
    regd_state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='RegdState', null=True, related_name='company_regd_states')
    regd_country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='RegdCountry', null=True, related_name='company_regd_countries')
    regd_pincode = models.CharField(db_column='RegdPinCode', max_length=20)
    regd_contact_no = models.CharField(db_column='RegdContactNo', max_length=50)
    regd_fax_no = models.CharField(db_column='RegdFaxNo', max_length=50)
    regd_email = models.CharField(db_column='RegdEmail', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblCompany_master'
        verbose_name = 'Company Master'
        verbose_name_plural = 'Company Masters'

    def __str__(self):
        return f"Company {self.id}"
        
    def get_full_address(self):
        parts = [
            self.regd_address,
            self.regd_city.name if self.regd_city else '',
            self.regd_state.name if self.regd_state else '',
            self.regd_country.name if self.regd_country else '',
            f"PIN No.-{self.regd_pincode}",
            f"Ph No.-{self.regd_contact_no}",
            f"Fax No.-{self.regd_fax_no}",
            f"Email No.-{self.regd_email}"
        ]
        return ",\n".join(filter(None, parts)) + "."

class FinancialMaster(models.Model):
    id = models.IntegerField(db_column='FinYearId', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_from = models.DateTimeField(db_column='FinYearFrom')
    fin_year_to = models.DateTimeField(db_column='FinYearTo')

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Master'
        verbose_name_plural = 'Financial Masters'

    def __str__(self):
        return f"FY {self.fin_year_from.year}-{self.fin_year_to.year}"

class WorkOrderMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    wo_no = models.CharField(db_column='WONo', max_length=100)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order Master'
        verbose_name_plural = 'Work Order Masters'

    def __str__(self):
        return self.wo_no

class PurchaseOrderMaster(models.Model):
    id = models.IntegerField(db_column='POId', primary_key=True)
    sys_date = models.DateTimeField(db_column='SysDate') # This is the PO Date
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Master'
        verbose_name = 'Purchase Order Master'
        verbose_name_plural = 'Purchase Order Masters'

    def __str__(self):
        return f"PO {self.id}"

class ProformaInvoice(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateTimeField(db_column='SysDate')
    comp_id = models.ForeignKey(CompanyMaster, on_delete=models.DO_NOTHING, db_column='CompId', related_name='proforma_invoices')
    invoice_no = models.CharField(db_column='InvoiceNo', max_length=50)
    po_no = models.CharField(db_column='PONo', max_length=50, null=True, blank=True)
    wo_no_list = models.CharField(db_column='WONo', max_length=255, null=True, blank=True) # Stored as comma-separated IDs
    invoice_mode = models.CharField(db_column='InvoiceMode', max_length=50)
    date_of_issue_invoice = models.DateTimeField(db_column='DateOfIssueInvoice')
    time_of_issue_invoice = models.CharField(db_column='TimeOfIssueInvoice', max_length=20)
    customer_code = models.CharField(db_column='CustomerCode', max_length=50)

    # Buyer Details
    buyer_name = models.CharField(db_column='Buyer_name', max_length=255)
    buyer_cotper = models.CharField(db_column='Buyer_cotper', max_length=100, null=True, blank=True)
    buyer_ph = models.CharField(db_column='Buyer_ph', max_length=50, null=True, blank=True)
    buyer_email = models.CharField(db_column='Buyer_email', max_length=100, null=True, blank=True)
    buyer_ecc = models.CharField(db_column='Buyer_ecc', max_length=50, null=True, blank=True)
    buyer_tin = models.CharField(db_column='Buyer_tin', max_length=50, null=True, blank=True)
    buyer_mob = models.CharField(db_column='Buyer_mob', max_length=50, null=True, blank=True)
    buyer_fax = models.CharField(db_column='Buyer_fax', max_length=50, null=True, blank=True)
    buyer_vat = models.CharField(db_column='Buyer_vat', max_length=50, null=True, blank=True)
    buyer_add = models.CharField(db_column='Buyer_add', max_length=255)
    buyer_country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='Buyer_country', null=True, related_name='buyer_invoices')
    buyer_state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='Buyer_state', null=True, related_name='buyer_invoices')
    buyer_city = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='Buyer_city', null=True, related_name='buyer_invoices')

    # Consignee Details
    cong_name = models.CharField(db_column='Cong_name', max_length=255)
    cong_cotper = models.CharField(db_column='Cong_cotper', max_length=100, null=True, blank=True)
    cong_ph = models.CharField(db_column='Cong_ph', max_length=50, null=True, blank=True)
    cong_email = models.CharField(db_column='Cong_email', max_length=100, null=True, blank=True)
    cong_ecc = models.CharField(db_column='Cong_ecc', max_length=50, null=True, blank=True)
    cong_tin = models.CharField(db_column='Cong_tin', max_length=50, null=True, blank=True)
    cong_mob = models.CharField(db_column='Cong_mob', max_length=50, null=True, blank=True)
    cong_fax = models.CharField(db_column='Cong_fax', max_length=50, null=True, blank=True)
    cong_vat = models.CharField(db_column='Cong_vat', max_length=50, null=True, blank=True)
    cong_add = models.CharField(db_column='Cong_add', max_length=255)
    cong_country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='Cong_country', null=True, related_name='consignee_invoices')
    cong_state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='Cong_state', null=True, related_name='consignee_invoices')
    cong_city = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='Cong_city', null=True, related_name='consignee_invoices')

    add_type = models.IntegerField(db_column='AddType')
    add_amt = models.FloatField(db_column='AddAmt')
    deduction_type = models.IntegerField(db_column='DeductionType')
    deduction = models.FloatField(db_column='Deduction')
    po_id = models.ForeignKey(PurchaseOrderMaster, on_delete=models.DO_NOTHING, db_column='POId', null=True, related_name='proforma_invoices')
    fin_year_id = models.ForeignKey(FinancialMaster, on_delete=models.DO_NOTHING, db_column='FinYearId', null=True, related_name='proforma_invoices')

    class Meta:
        managed = False
        db_table = 'tblACC_ProformaInvoice_Master'
        verbose_name = 'Proforma Invoice'
        verbose_name_plural = 'Proforma Invoices'

    def __str__(self):
        return self.invoice_no

    def get_formatted_invoice_no(self):
        """Replicates C# logic for InvoiceNo + Financial Year suffix."""
        if self.fin_year_id:
            fy_from = self.fin_year_id.fin_year_from.strftime('%y')
            fy_to = self.fin_year_id.fin_year_to.strftime('%y')
            return f"{self.invoice_no}/{fy_from}{fy_to}"
        return self.invoice_no

    def get_buyer_full_address(self):
        """Replicates C# logic for Buyer Address construction."""
        parts = [self.buyer_add]
        if self.buyer_city:
            parts.append(self.buyer_city.name)
        if self.buyer_state:
            parts.append(self.buyer_state.name)
        if self.buyer_country:
            parts.append(self.buyer_country.name)
        return ",\n".join(filter(None, parts)) + "."

    def get_consignee_full_address(self):
        """Replicates C# logic for Consignee Address construction."""
        parts = [self.cong_add]
        if self.cong_city:
            parts.append(self.cong_city.name)
        if self.cong_state:
            parts.append(self.cong_state.name)
        if self.cong_country:
            parts.append(self.cong_country.name)
        return ",\n".join(filter(None, parts)) + "."

    def get_work_order_numbers(self):
        """Replicates C# logic for parsing comma-separated WO IDs and fetching WO numbers."""
        if not self.wo_no_list:
            return ""
        
        wo_ids = [int(x.strip()) for x in self.wo_no_list.split(',') if x.strip()]
        wo_nos = WorkOrderMaster.objects.filter(id__in=wo_ids, comp_id=self.comp_id_id).values_list('wo_no', flat=True)
        return ",".join(wo_nos)

    def get_po_date(self):
        """Fetches PO date from PurchaseOrderMaster."""
        if self.po_id:
            return self.po_id.sys_date
        return None

    def get_report_details(self):
        """
        Aggregates all data required for the Proforma Invoice report,
        mimicking the DataSet construction in the ASP.NET code-behind.
        This is the core 'fat model' method.
        """
        report_data = {
            'id': self.id,
            'sys_date': self.sys_date.strftime('%d/%m/%Y'),
            'comp_id': self.comp_id_id,
            'invoice_no': self.invoice_no,
            'po_no': self.po_no,
            'invoice_mode': self.invoice_mode,
            'date_of_issue_invoice': self.date_of_issue_invoice.strftime('%d/%m/%Y'),
            'time_of_issue_invoice': self.time_of_issue_invoice,
            'customer_code': self.customer_code,

            # Buyer Details
            'buyer_name': self.buyer_name,
            'buyer_cotper': self.buyer_cotper,
            'buyer_ph': self.buyer_ph,
            'buyer_email': self.buyer_email,
            'buyer_ecc': self.buyer_ecc,
            'buyer_tin': self.buyer_tin,
            'buyer_mob': self.buyer_mob,
            'buyer_fax': self.buyer_fax,
            'buyer_vat': self.buyer_vat,
            'buyer_address': self.get_buyer_full_address(),

            # Consignee Details
            'cong_name': self.cong_name,
            'cong_cotper': self.cong_cotper,
            'cong_ph': self.cong_ph,
            'cong_email': self.cong_email,
            'cong_ecc': self.cong_ecc,
            'cong_tin': self.cong_tin,
            'cong_mob': self.cong_mob,
            'cong_fax': self.cong_fax,
            'cong_vat': self.cong_vat,
            'consignee_address': self.get_consignee_full_address(),

            'add_type': self.add_type,
            'add_amt': self.add_amt,
            'deduction_type': self.deduction_type,
            'deduction': self.deduction,

            # Derived fields
            'formatted_invoice_no': self.get_formatted_invoice_no(),
            'company_address': self.comp_id.get_full_address() if self.comp_id else '',
            'po_date': self.get_po_date().strftime('%d/%m/%Y') if self.get_po_date() else '',
            'work_order_numbers': self.get_work_order_numbers(),
            'print_type': '' # This comes from querystring, not model itself
        }
        return report_data
```

#### 4.2 Forms (`accounts/forms.py`)

**Task:** Define a Django form for `ProformaInvoice`. This form is included for a complete CRUD application, though the original ASP.NET page is read-only.

**Instructions:**
-   Create a `ModelForm` for `ProformaInvoice`.
-   Include key editable fields.
-   Add Tailwind CSS classes to widgets.

```python
from django import forms
from .models import ProformaInvoice

class ProformaInvoiceForm(forms.ModelForm):
    class Meta:
        model = ProformaInvoice
        fields = [
            'invoice_no', 'po_no', 'invoice_mode', 'date_of_issue_invoice',
            'time_of_issue_invoice', 'customer_code', 'buyer_name',
            'buyer_add', 'add_type', 'add_amt', 'deduction_type', 'deduction',
        ]
        widgets = {
            'invoice_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'po_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'invoice_mode': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'date_of_issue_invoice': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'time_of_issue_invoice': forms.TextInput(attrs={'type': 'time', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'customer_code': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'buyer_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'buyer_add': forms.Textarea(attrs={'rows': 3, 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'add_type': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'add_amt': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'deduction_type': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'deduction': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
```

#### 4.3 Views (`accounts/views.py`)

**Task:** Implement Django Class-Based Views for Proforma Invoice.
**Crucially, the `ProformaInvoiceDetailView` directly replaces the ASP.NET `ProformaInvoice_Print_Details.aspx` functionality.** The other CRUD views are provided for a complete application structure as per the prompt's template.

**Instructions:**
-   `ProformaInvoiceDetailView` for displaying report-like details. It fetches data via the model's `get_report_details` method.
-   Include `ProformaInvoiceListView`, `ProformaInvoiceCreateView`, `ProformaInvoiceUpdateView`, `ProformaInvoiceDeleteView` following the "thin view" principle.
-   Implement `ProformaInvoiceTablePartialView` for HTMX-driven DataTables.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404
from django.shortcuts import get_object_or_404
from .models import ProformaInvoice, CompanyMaster # Import CompanyMaster for getting company address
from .forms import ProformaInvoiceForm

# --- Views for Proforma Invoice CRUD (General Application Functionality) ---

class ProformaInvoiceListView(ListView):
    model = ProformaInvoice
    template_name = 'accounts/proformainvoice/list.html'
    context_object_name = 'proformainvoices'
    paginate_by = 10 # Example pagination

class ProformaInvoiceTablePartialView(ListView):
    model = ProformaInvoice
    template_name = 'accounts/proformainvoice/_proformainvoice_table.html'
    context_object_name = 'proformainvoices'

    def get_queryset(self):
        # Apply any necessary filtering/ordering for the DataTable here
        return super().get_queryset().order_by('-sys_date')

class ProformaInvoiceCreateView(CreateView):
    model = ProformaInvoice
    form_class = ProformaInvoiceForm
    template_name = 'accounts/proformainvoice/form.html'
    success_url = reverse_lazy('proformainvoice_list') # Redirect to list after success

    def form_valid(self, form):
        # Set comp_id and fin_year_id from session/context if needed,
        # e.g., form.instance.comp_id_id = self.request.session.get('compid')
        # form.instance.fin_year_id_id = self.request.session.get('finyear')
        # For this example, we'll assign a dummy company and financial year
        form.instance.comp_id = CompanyMaster.objects.first() # Or retrieve from session/user
        form.instance.fin_year_id = form.instance.comp_id.proforma_invoices.first().fin_year_id if form.instance.comp_id else None # Dummy financial year
        
        response = super().form_valid(form)
        messages.success(self.request, 'Proforma Invoice added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content to swap
                headers={
                    'HX-Trigger': 'refreshProformaInvoiceList', # Custom event to trigger table refresh
                    'HX-Redirect': reverse_lazy('proformainvoice_list') # Redirect main browser window
                }
            )
        return response

class ProformaInvoiceUpdateView(UpdateView):
    model = ProformaInvoice
    form_class = ProformaInvoiceForm
    template_name = 'accounts/proformainvoice/form.html'
    success_url = reverse_lazy('proformainvoice_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Proforma Invoice updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshProformaInvoiceList',
                    'HX-Redirect': reverse_lazy('proformainvoice_list')
                }
            )
        return response

class ProformaInvoiceDeleteView(DeleteView):
    model = ProformaInvoice
    template_name = 'accounts/proformainvoice/confirm_delete.html'
    success_url = reverse_lazy('proformainvoice_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Proforma Invoice deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshProformaInvoiceList',
                    'HX-Redirect': reverse_lazy('proformainvoice_list')
                }
            )
        return response

# --- Detail View for Print Details (Direct Replacement for ASP.NET Page) ---

class ProformaInvoiceDetailView(DetailView):
    model = ProformaInvoice
    template_name = 'accounts/proformainvoice/detail.html'
    context_object_name = 'invoice_details'

    def get_object(self, queryset=None):
        """
        Fetches the Proforma Invoice based on URL parameters (InvId, InvNo, CompId)
        and calls the fat model method to get all report-specific details.
        """
        # Retrieve parameters from URL query string, mimicking ASP.NET Request.QueryString
        invoice_id = self.request.GET.get('InvId')
        invoice_no = self.request.GET.get('InvNo')
        # comp_id and fin_year_id usually come from session in ASP.NET
        # For Django, we can get them from session or make them URL params too.
        # Assuming for now they might be hardcoded or retrieved from user session
        # For demonstration, we'll try to get it from request.GET or a dummy value
        comp_id = self.request.GET.get('cid', self.request.session.get('compid'))
        
        # Validate that required parameters are present
        if not all([invoice_id, invoice_no, comp_id]):
            raise Http404("Missing required invoice parameters (InvId, InvNo, CompId).")
        
        try:
            # Query the ProformaInvoice using the retrieved parameters
            invoice = ProformaInvoice.objects.select_related(
                'comp_id', 'buyer_country', 'buyer_state', 'buyer_city',
                'cong_country', 'cong_state', 'cong_city', 'po_id', 'fin_year_id'
            ).get(
                id=invoice_id,
                invoice_no=invoice_no,
                comp_id__id=comp_id # Use __id for foreign key matching
            )
            return invoice
        except ProformaInvoice.DoesNotExist:
            raise Http404("Proforma Invoice not found.")
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # The 'object' in context is the ProformaInvoice instance returned by get_object
        invoice = context['object']
        
        # Call the fat model method to get all compiled report details
        details = invoice.get_report_details()
        
        # Add print_type from query string, mimicking ASP.NET
        details['print_type'] = self.request.GET.get('PT', '')

        context['invoice_details'] = details
        return context

```

#### 4.4 Templates (`accounts/templates/accounts/proformainvoice/`)

**Task:** Create templates for list, form, delete confirmation, and the specific detail view for printing.

**Instructions:**
-   `list.html`: Displays all Proforma Invoices with DataTables, providing buttons to add, edit, or delete via HTMX modals.
-   `_proformainvoice_table.html`: Partial template for the DataTables content, loaded via HTMX.
-   `form.html`: Partial template for Create/Update forms, loaded into a modal.
-   `confirm_delete.html`: Partial template for delete confirmation, loaded into a modal.
-   `detail.html`: The main replacement for the ASP.NET print page, displaying the structured invoice data.

```html
<!-- accounts/templates/accounts/proformainvoice/list.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Proforma Invoices</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'proformainvoice_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Proforma Invoice
        </button>
    </div>
    
    <div id="proformainvoiceTable-container"
         hx-trigger="load, refreshProformaInvoiceList from:body"
         hx-get="{% url 'proformainvoice_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
    });

    document.body.addEventListener('htmx:afterSwap', function(event) {
        // Close modal after successful form submission and HX-Trigger redirect
        if (event.detail.target.id === 'modalContent' && event.detail.xhr.status === 204) {
            document.getElementById('modal').classList.remove('is-active');
        }
    });

    document.body.addEventListener('refreshProformaInvoiceList', function() {
        // This event is triggered by HTMX from the HX-Trigger header after CRUD operations
        // It causes the #proformainvoiceTable-container to re-fetch its content, refreshing the DataTable
        console.log('Refreshing Proforma Invoice List...');
    });
</script>
{% endblock %}
```

```html
<!-- accounts/templates/accounts/proformainvoice/_proformainvoice_table.html -->
<div class="overflow-x-auto">
    <table id="proformainvoiceTable" class="min-w-full bg-white border border-gray-200">
        <thead>
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice No.</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Code</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Buyer Name</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issue Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for obj in proformainvoices %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.invoice_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.customer_code }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.buyer_name }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.date_of_issue_invoice|date:"d M Y" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">
                    <button 
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded mr-2 text-sm"
                        onclick="window.open('{% url 'proformainvoice_detail' %}?InvId={{ obj.pk }}&InvNo={{ obj.invoice_no }}&cid={{ obj.comp_id_id }}&PT=View', '_blank');">
                        View/Print
                    </button>
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2 text-sm"
                        hx-get="{% url 'proformainvoice_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-sm"
                        hx-get="{% url 'proformainvoice_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
$(document).ready(function() {
    // Destroy existing DataTable instance before re-initializing if HTMX replaces content
    if ($.fn.DataTable.isDataTable('#proformainvoiceTable')) {
        $('#proformainvoiceTable').DataTable().destroy();
    }
    $('#proformainvoiceTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "pagingType": "full_numbers", // For full pagination controls
        "responsive": true // Make table responsive
    });
});
</script>
```

```html
<!-- accounts/templates/accounts/proformainvoice/form.html -->
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Proforma Invoice</h3>
    <form hx-post="{{ request.path }}" hx-swap="none"> {# hx-swap="none" works with HX-Trigger redirect #}
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

```html
<!-- accounts/templates/accounts/proformainvoice/confirm_delete.html -->
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="mb-5">Are you sure you want to delete the Proforma Invoice "{{ object.invoice_no }}"?</p>
    <form hx-post="{% url 'proformainvoice_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

```html
<!-- accounts/templates/accounts/proformainvoice/detail.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8 printable-area">
    <div class="flex justify-between items-center mb-6 no-print">
        <h2 class="text-2xl font-bold">Proforma Invoice - Print</h2>
        <div class="flex space-x-4">
            <a href="{% url 'proformainvoice_list' %}" 
               class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to List
            </a>
            <button onclick="window.print()" 
                    class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                Print
            </button>
        </div>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-lg">
        <h3 class="text-xl font-semibold mb-4 border-b pb-2">Proforma Invoice Details - {{ invoice_details.formatted_invoice_no }}</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
                <p class="font-bold">Invoice No:</p>
                <p>{{ invoice_details.formatted_invoice_no }}</p>
            </div>
            <div>
                <p class="font-bold">Issue Date:</p>
                <p>{{ invoice_details.date_of_issue_invoice }} {{ invoice_details.time_of_issue_invoice }}</p>
            </div>
            <div>
                <p class="font-bold">P.O. No:</p>
                <p>{{ invoice_details.po_no }}</p>
            </div>
            <div>
                <p class="font-bold">P.O. Date:</p>
                <p>{{ invoice_details.po_date }}</p>
            </div>
            <div>
                <p class="font-bold">Work Order Nos:</p>
                <p>{{ invoice_details.work_order_numbers }}</p>
            </div>
            <div>
                <p class="font-bold">Invoice Mode:</p>
                <p>{{ invoice_details.invoice_mode }}</p>
            </div>
            <div>
                <p class="font-bold">Customer Code:</p>
                <p>{{ invoice_details.customer_code }}</p>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
                <h4 class="font-bold text-lg mb-2 border-b pb-1">Buyer Details</h4>
                <p><span class="font-semibold">Name:</span> {{ invoice_details.buyer_name }}</p>
                <p><span class="font-semibold">Address:</span> <br>{{ invoice_details.buyer_address|linebreaksbr }}</p>
                <p><span class="font-semibold">Contact Person:</span> {{ invoice_details.buyer_cotper }}</p>
                <p><span class="font-semibold">Phone:</span> {{ invoice_details.buyer_ph }}</p>
                <p><span class="font-semibold">Email:</span> {{ invoice_details.buyer_email }}</p>
                <p><span class="font-semibold">ECC:</span> {{ invoice_details.buyer_ecc }}</p>
                <p><span class="font-semibold">TIN:</span> {{ invoice_details.buyer_tin }}</p>
                <p><span class="font-semibold">Mobile:</span> {{ invoice_details.buyer_mob }}</p>
                <p><span class="font-semibold">Fax:</span> {{ invoice_details.buyer_fax }}</p>
                <p><span class="font-semibold">VAT:</span> {{ invoice_details.buyer_vat }}</p>
            </div>
            <div>
                <h4 class="font-bold text-lg mb-2 border-b pb-1">Consignee Details</h4>
                <p><span class="font-semibold">Name:</span> {{ invoice_details.cong_name }}</p>
                <p><span class="font-semibold">Address:</span> <br>{{ invoice_details.consignee_address|linebreaksbr }}</p>
                <p><span class="font-semibold">Contact Person:</span> {{ invoice_details.cong_cotper }}</p>
                <p><span class="font-semibold">Phone:</span> {{ invoice_details.cong_ph }}</p>
                <p><span class="font-semibold">Email:</span> {{ invoice_details.cong_email }}</p>
                <p><span class="font-semibold">ECC:</span> {{ invoice_details.cong_ecc }}</p>
                <p><span class="font-semibold">TIN:</span> {{ invoice_details.cong_tin }}</p>
                <p><span class="font-semibold">Mobile:</span> {{ invoice_details.cong_mob }}</p>
                <p><span class="font-semibold">Fax:</span> {{ invoice_details.cong_fax }}</p>
                <p><span class="font-semibold">VAT:</span> {{ invoice_details.cong_vat }}</p>
            </div>
        </div>

        <div class="mb-6">
            <h4 class="font-bold text-lg mb-2 border-b pb-1">Additional Charges / Deductions</h4>
            <p><span class="font-semibold">Add Type:</span> {{ invoice_details.add_type }}</p>
            <p><span class="font-semibold">Additional Amount:</span> {{ invoice_details.add_amt }}</p>
            <p><span class="font-semibold">Deduction Type:</span> {{ invoice_details.deduction_type }}</p>
            <p><span class="font-semibold">Deduction Amount:</span> {{ invoice_details.deduction }}</p>
        </div>

        <div class="mb-6">
            <h4 class="font-bold text-lg mb-2 border-b pb-1">Company Details</h4>
            <p class="whitespace-pre-line">{{ invoice_details.company_address }}</p>
        </div>

        <div class="mb-6">
            <h4 class="font-bold text-lg mb-2 border-b pb-1">Print Type</h4>
            <p>{{ invoice_details.print_type }}</p>
        </div>
    </div>
</div>

<style>
    /* Styles for printing */
    @media print {
        body {
            font-size: 10pt;
            margin: 0;
            padding: 0;
        }
        .no-print {
            display: none !important;
        }
        .printable-area {
            width: 100%;
            margin: 0;
            padding: 0;
            box-shadow: none;
            border-radius: 0;
        }
        /* Further styling to optimize for paper printing */
    }
</style>
{% endblock %}
```

#### 4.5 URLs (`accounts/urls.py`)

**Task:** Define URL patterns for all views.

**Instructions:**
-   Set up paths for the main list view, CRUD operations (add, edit, delete), and the partial table view.
-   **Crucially, define a specific URL for `ProformaInvoiceDetailView` to handle the print details functionality, accepting query parameters.**

```python
from django.urls import path
from .views import (
    ProformaInvoiceListView,
    ProformaInvoiceTablePartialView,
    ProformaInvoiceCreateView,
    ProformaInvoiceUpdateView,
    ProformaInvoiceDeleteView,
    ProformaInvoiceDetailView, # Import the detail view
)

urlpatterns = [
    # General CRUD URLs for Proforma Invoices
    path('proformainvoice/', ProformaInvoiceListView.as_view(), name='proformainvoice_list'),
    path('proformainvoice/table/', ProformaInvoiceTablePartialView.as_view(), name='proformainvoice_table'),
    path('proformainvoice/add/', ProformaInvoiceCreateView.as_view(), name='proformainvoice_add'),
    path('proformainvoice/edit/<int:pk>/', ProformaInvoiceUpdateView.as_view(), name='proformainvoice_edit'),
    path('proformainvoice/delete/<int:pk>/', ProformaInvoiceDeleteView.as_view(), name='proformainvoice_delete'),
    
    # Specific URL for Proforma Invoice Print Details (replacing ASP.NET .aspx page)
    # This view expects InvId, InvNo, cid (CompId), PT (PrintType) as query parameters
    path('proformainvoice/detail/', ProformaInvoiceDetailView.as_view(), name='proformainvoice_detail'),
]
```

#### 4.6 Tests (`accounts/tests.py`)

**Task:** Write comprehensive unit tests for models and integration tests for views to ensure functionality and coverage.

**Instructions:**
-   Create mock data for related models (`Country`, `State`, `City`, `CompanyMaster`, `FinancialMaster`, `WorkOrderMaster`, `PurchaseOrderMaster`) to enable testing of the `ProformaInvoice` model's aggregated methods.
-   Test `ProformaInvoice` model methods (e.g., `get_formatted_invoice_no`, `get_buyer_full_address`, `get_work_order_numbers`, `get_report_details`).
-   Test all views (list, create, update, delete, and especially the detail view) for correct responses, template usage, and data integrity.
-   Simulate HTMX requests for relevant views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import (
    ProformaInvoice, Country, State, City, CompanyMaster,
    FinancialMaster, WorkOrderMaster, PurchaseOrderMaster
)
from django.utils import timezone
from datetime import datetime, timedelta

class ProformaInvoiceModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dependent data for relationships
        cls.country = Country.objects.create(id=1, name='India')
        cls.state = State.objects.create(id=1, name='Karnataka', country=cls.country)
        cls.city = City.objects.create(id=1, name='Bangalore', state=cls.state)
        
        cls.company = CompanyMaster.objects.create(
            id=1,
            regd_address='123 Company St',
            regd_city=cls.city,
            regd_state=cls.state,
            regd_country=cls.country,
            regd_pincode='560001',
            regd_contact_no='9876543210',
            regd_fax_no='1234567',
            regd_email='<EMAIL>'
        )
        cls.financial_year = FinancialMaster.objects.create(
            id=1, comp_id=cls.company.id,
            fin_year_from=timezone.datetime(2023, 4, 1, tzinfo=timezone.utc),
            fin_year_to=timezone.datetime(2024, 3, 31, tzinfo=timezone.utc)
        )
        cls.po_master = PurchaseOrderMaster.objects.create(
            id=101, comp_id=cls.company.id,
            sys_date=timezone.datetime(2023, 5, 10, tzinfo=timezone.utc)
        )
        cls.wo1 = WorkOrderMaster.objects.create(id=201, wo_no='WO/ABC/001', comp_id=cls.company.id)
        cls.wo2 = WorkOrderMaster.objects.create(id=202, wo_no='WO/DEF/002', comp_id=cls.company.id)

        # Create the ProformaInvoice instance
        cls.proforma_invoice = ProformaInvoice.objects.create(
            id=1,
            sys_date=timezone.datetime(2023, 6, 1, 10, 0, 0, tzinfo=timezone.utc),
            comp_id=cls.company,
            invoice_no='PI/2023/001',
            po_no='PO/XYZ/001',
            wo_no_list=f"{cls.wo1.id},{cls.wo2.id},", # Simulate comma-separated IDs
            invoice_mode='Sale',
            date_of_issue_invoice=timezone.datetime(2023, 6, 1, tzinfo=timezone.utc),
            time_of_issue_invoice='10:30:00',
            customer_code='CUST001',
            
            # Buyer details
            buyer_name='Buyer Name Inc.',
            buyer_add='456 Buyer Address',
            buyer_country=cls.country,
            buyer_state=cls.state,
            buyer_city=cls.city,
            buyer_cotper='John Doe',
            buyer_ph='111-222-3333',
            buyer_email='<EMAIL>',
            buyer_ecc='ECC123',
            buyer_tin='TIN123',
            buyer_mob='444-555-6666',
            buyer_fax='777-888-9999',
            buyer_vat='VAT123',

            # Consignee details
            cong_name='Consignee Corp.',
            cong_add='789 Consignee Address',
            cong_country=cls.country,
            cong_state=cls.state,
            cong_city=cls.city,
            cong_cotper='Jane Smith',
            cong_ph='999-888-7777',
            cong_email='<EMAIL>',
            cong_ecc='ECC456',
            cong_tin='TIN456',
            cong_mob='666-555-4444',
            cong_fax='333-222-1111',
            cong_vat='VAT456',

            add_type=1,
            add_amt=100.50,
            deduction_type=2,
            deduction=50.25,
            po_id=cls.po_master,
            fin_year_id=cls.financial_year
        )
  
    def test_proforma_invoice_creation(self):
        obj = ProformaInvoice.objects.get(id=1)
        self.assertEqual(obj.invoice_no, 'PI/2023/001')
        self.assertEqual(obj.customer_code, 'CUST001')
        self.assertEqual(obj.comp_id, self.company)
        
    def test_get_formatted_invoice_no(self):
        obj = ProformaInvoice.objects.get(id=1)
        # Assuming financial year 2023-2024, so 2324 suffix
        self.assertEqual(obj.get_formatted_invoice_no(), 'PI/2023/001/2324')

    def test_get_buyer_full_address(self):
        obj = ProformaInvoice.objects.get(id=1)
        expected_address = '456 Buyer Address,\nBangalore, Karnataka,\nIndia.'
        self.assertEqual(obj.get_buyer_full_address(), expected_address)

    def test_get_consignee_full_address(self):
        obj = ProformaInvoice.objects.get(id=1)
        expected_address = '789 Consignee Address,\nBangalore, Karnataka,\nIndia.'
        self.assertEqual(obj.get_consignee_full_address(), expected_address)
        
    def test_get_work_order_numbers(self):
        obj = ProformaInvoice.objects.get(id=1)
        expected_wo_nos = f"{self.wo1.wo_no},{self.wo2.wo_no}"
        self.assertEqual(obj.get_work_order_numbers(), expected_wo_nos)
        
    def test_get_po_date(self):
        obj = ProformaInvoice.objects.get(id=1)
        self.assertEqual(obj.get_po_date(), self.po_master.sys_date)
        
    def test_get_report_details(self):
        obj = ProformaInvoice.objects.get(id=1)
        details = obj.get_report_details()
        self.assertIn('formatted_invoice_no', details)
        self.assertEqual(details['formatted_invoice_no'], 'PI/2023/001/2324')
        self.assertIn('buyer_address', details)
        self.assertIn('company_address', details)
        self.assertIn('work_order_numbers', details)
        self.assertEqual(details['work_order_numbers'], f"{self.wo1.wo_no},{self.wo2.wo_no}")
        self.assertEqual(details['po_date'], self.po_master.sys_date.strftime('%d/%m/%Y'))
        self.assertEqual(details['add_amt'], 100.50)
        self.assertEqual(details['deduction'], 50.25)


class ProformaInvoiceViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary related objects for all tests
        cls.country = Country.objects.create(id=1, name='India')
        cls.state = State.objects.create(id=1, name='Karnataka', country=cls.country)
        cls.city = City.objects.create(id=1, name='Bangalore', state=cls.state)
        cls.company = CompanyMaster.objects.create(
            id=10, regd_address='Test Company Addr', regd_city=cls.city, 
            regd_state=cls.state, regd_country=cls.country, regd_pincode='123456',
            regd_contact_no='123', regd_fax_no='456', regd_email='<EMAIL>'
        )
        cls.financial_year = FinancialMaster.objects.create(
            id=10, comp_id=cls.company.id,
            fin_year_from=timezone.datetime(2023, 4, 1, tzinfo=timezone.utc),
            fin_year_to=timezone.datetime(2024, 3, 31, tzinfo=timezone.utc)
        )
        cls.po_master = PurchaseOrderMaster.objects.create(
            id=110, comp_id=cls.company.id, sys_date=timezone.datetime(2023, 5, 10, tzinfo=timezone.utc)
        )
        cls.wo1 = WorkOrderMaster.objects.create(id=210, wo_no='WO/ABC/010', comp_id=cls.company.id)
        cls.wo2 = WorkOrderMaster.objects.create(id=211, wo_no='WO/DEF/011', comp_id=cls.company.id)

        # Create a sample ProformaInvoice
        cls.proforma_invoice = ProformaInvoice.objects.create(
            id=100,
            sys_date=timezone.datetime(2023, 6, 1, 10, 0, 0, tzinfo=timezone.utc),
            comp_id=cls.company,
            invoice_no='PI/2023/100',
            po_no='PO/TEST/100',
            wo_no_list=f"{cls.wo1.id},{cls.wo2.id},",
            invoice_mode='Sample',
            date_of_issue_invoice=timezone.datetime(2023, 6, 1, tzinfo=timezone.utc),
            time_of_issue_invoice='10:00:00',
            customer_code='CUST100',
            buyer_name='Test Buyer',
            buyer_add='100 Test Buyer Addr',
            buyer_country=cls.country,
            buyer_state=cls.state,
            buyer_city=cls.city,
            cong_name='Test Consignee',
            cong_add='100 Test Consignee Addr',
            cong_country=cls.country,
            cong_state=cls.state,
            cong_city=cls.city,
            add_type=1, add_amt=10.00, deduction_type=1, deduction=5.00,
            po_id=cls.po_master,
            fin_year_id=cls.financial_year
        )
    
    def setUp(self):
        self.client = Client()
        # Set session data if views rely on it (e.g., compid)
        session = self.client.session
        session['compid'] = self.company.id
        session['finyear'] = self.financial_year.id
        session.save()
    
    # --- Detail View Tests (Direct Replacement) ---
    def test_detail_view_success(self):
        # Mimic ASP.NET query string
        url = reverse('proformainvoice_detail') + f'?InvId={self.proforma_invoice.id}&InvNo={self.proforma_invoice.invoice_no}&cid={self.proforma_invoice.comp_id.id}&PT=View'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/proformainvoice/detail.html')
        self.assertIn('invoice_details', response.context)
        self.assertEqual(response.context['invoice_details']['invoice_no'], self.proforma_invoice.invoice_no)
        self.assertEqual(response.context['invoice_details']['print_type'], 'View')

    def test_detail_view_missing_params(self):
        url = reverse('proformainvoice_detail') # Missing all params
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

        url = reverse('proformainvoice_detail') + f'?InvId={self.proforma_invoice.id}' # Missing other params
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

    def test_detail_view_not_found(self):
        url = reverse('proformainvoice_detail') + f'?InvId=9999&InvNo=NONEXISTENT&cid={self.proforma_invoice.comp_id.id}&PT=View'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

    # --- CRUD View Tests (General Application Functionality) ---
    def test_list_view(self):
        response = self.client.get(reverse('proformainvoice_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/proformainvoice/list.html')
        self.assertTrue('proformainvoices' in response.context)
        self.assertContains(response, self.proforma_invoice.invoice_no)

    def test_table_partial_view(self):
        response = self.client.get(reverse('proformainvoice_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/proformainvoice/_proformainvoice_table.html')
        self.assertContains(response, self.proforma_invoice.invoice_no)

    def test_create_view_get(self):
        response = self.client.get(reverse('proformainvoice_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/proformainvoice/form.html')
        self.assertTrue('form' in response.context)
        
    def test_create_view_post_htmx(self):
        data = {
            'invoice_no': 'PI/NEW/001',
            'po_no': 'PO/NEW/001',
            'invoice_mode': 'Cash',
            'date_of_issue_invoice': '2023-07-01',
            'time_of_issue_invoice': '12:00:00',
            'customer_code': 'NEWCUST',
            'buyer_name': 'New Buyer Co.',
            'buyer_add': '123 New Address',
            'add_type': 1, 'add_amt': 20.00,
            'deduction_type': 1, 'deduction': 10.00,
        }
        response = self.client.post(reverse('proformainvoice_add'), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HX-Trigger/HX-Redirect success status
        self.assertTrue(ProformaInvoice.objects.filter(invoice_no='PI/NEW/001').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('HX-Redirect', response.headers)
        self.assertEqual(response.headers['HX-Redirect'], reverse('proformainvoice_list'))

    def test_update_view_get(self):
        obj = self.proforma_invoice
        response = self.client.get(reverse('proformainvoice_edit', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/proformainvoice/form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, obj)
        
    def test_update_view_post_htmx(self):
        obj = self.proforma_invoice
        updated_invoice_no = 'PI/2023/100-UPDATED'
        data = {
            'invoice_no': updated_invoice_no,
            'po_no': obj.po_no,
            'invoice_mode': obj.invoice_mode,
            'date_of_issue_invoice': obj.date_of_issue_invoice.strftime('%Y-%m-%d'),
            'time_of_issue_invoice': obj.time_of_issue_invoice,
            'customer_code': obj.customer_code,
            'buyer_name': obj.buyer_name,
            'buyer_add': obj.buyer_add,
            'add_type': obj.add_type, 'add_amt': obj.add_amt,
            'deduction_type': obj.deduction_type, 'deduction': obj.deduction,
        }
        response = self.client.post(reverse('proformainvoice_edit', args=[obj.id]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        obj.refresh_from_db()
        self.assertEqual(obj.invoice_no, updated_invoice_no)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('HX-Redirect', response.headers)

    def test_delete_view_get(self):
        obj = self.proforma_invoice
        response = self.client.get(reverse('proformainvoice_delete', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/proformainvoice/confirm_delete.html')
        self.assertEqual(response.context['object'], obj)
        
    def test_delete_view_post_htmx(self):
        obj = self.proforma_invoice
        response = self.client.post(reverse('proformainvoice_delete', args=[obj.id]), HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertFalse(ProformaInvoice.objects.filter(id=obj.id).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('HX-Redirect', response.headers)

```

---

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
-   All dynamic forms (Create, Update, Delete) are loaded into a modal via HTMX `hx-get` attributes.
-   Form submissions use `hx-post` and `hx-swap="none"` with `HX-Trigger` headers to inform the client to refresh the list table and close the modal.
-   The `_proformainvoice_table.html` partial is loaded via `hx-get` with `hx-trigger="load, refreshProformaInvoiceList from:body"` to ensure the DataTables content is always up-to-date and refreshed after any CRUD operation.
-   Alpine.js is noted for potential future UI state management, though minimal for the current scope.
-   DataTables is initialized on the `_proformainvoice_table.html` partial once it's loaded by HTMX. A small JavaScript snippet ensures DataTables is properly re-initialized if the table content is swapped.
-   The `ProformaInvoiceDetailView` is designed for print, but buttons for "Back to List" and "Print" are provided in a non-HTMX fashion for standard browser navigation and print functionality.

---

### Final Notes

This comprehensive plan provides a clear roadmap for migrating your ASP.NET Proforma Invoice printing functionality to a modern Django application. By leveraging Django's ORM, Class-Based Views, and incorporating HTMX and Alpine.js for dynamic interactions, we achieve a robust, maintainable, and user-friendly solution. The "fat model" approach ensures business logic is centralized and testable, keeping views clean and focused on presentation. The inclusion of unit and integration tests guarantees the reliability of the migrated features. This structured approach, combined with the detailed code examples, can be effectively executed and overseen through conversational AI guidance, streamlining your modernization journey.