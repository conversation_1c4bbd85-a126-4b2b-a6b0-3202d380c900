## ASP.NET to Django Conversion Script: Bill Booking - New

This document outlines a comprehensive modernization plan to transition the existing ASP.NET Bill Booking module to a modern Django-based solution. The focus is on leveraging AI-assisted automation, adopting a "Fat Model, Thin View" architecture, and utilizing contemporary frontend technologies like HTMX, Alpine.js, and DataTables for a highly interactive user experience without traditional full-page reloads.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is located at `core/base.html`.
- Focus ONLY on component-specific code for the current module (`Bill Booking`).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code primarily interacts with `tblMM_Supplier_master` for displaying supplier information and searching.
The `loadData` function's SQL query: `fun.select("SupId,SupplierName,SupplierId", "tblMM_Supplier_master", ...)` directly specifies the columns.
The `GridView2` uses `DataKeyNames="Id"` and the `lblId` control is bound to `Eval("Id")` which comes from `SupId` of the database table. The `lblsupId` (Code) is bound to `Eval("SupId")` which comes from `SupplierId` of the database table. This implies:
- `SupId` from `tblMM_Supplier_master` acts as the primary identifier (what Django would typically use as `id`).
- `SupplierId` from `tblMM_Supplier_master` is a secondary code/identifier for the supplier.
- `SupplierName` is the name.
- Implicitly, `CompId` and `FinYearId` are used for filtering, suggesting they are also columns in `tblMM_Supplier_master`.

**Extracted Schema:**
- **Table Name:** `tblMM_Supplier_master`
- **Columns:**
    - `SupId` (Integer, acts as Primary Key for this module's context)
    - `SupplierName` (String)
    - `SupplierId` (String, representing the supplier's code)
    - `CompId` (Integer, for company ID)
    - `FinYearId` (Integer, for financial year ID)

### Step 2: Identify Backend Functionality

**Analysis:**
The module "Bill Booking - New" functions primarily as a supplier selection interface rather than a full CRUD for suppliers themselves.

-   **Read (Display/Search):**
    -   The `loadData` function retrieves and displays supplier data from `tblMM_Supplier_master` in `GridView2`.
    -   Data can be filtered by `Supplier Name` via `txtSupplier` and `DropDownList1`.
    -   Pagination is supported (`GridView2_PageIndexChanging`).
-   **Autocomplete:**
    -   The `sql` WebMethod provides real-time suggestions for `txtSupplier` using `SupplierName` and `SupplierId`.
-   **Action (Select):**
    -   The `GridView2_RowCommand` with `CommandName="Sel"` allows users to select a supplier from the list.
    -   Upon selection, values for `SupId` (supplier code), `Freight` (from `txtFreight` in grid row), and `StateType` (from `ddlMhOms` in grid row) are captured.
    -   A validation check for `ddlMhOms` ("Select type of Invoice!") is performed.
    -   The page then redirects to `BillBooking_New_Details.aspx` with these values as query parameters, along with hardcoded `ModId=11` and `SubModId=62`.
-   **Temporary Data Cleanup:**
    -   `Page_Load` includes commands to delete records from `tblACC_BillBooking_Attach_Temp` and `tblACC_BillBooking_Details_Temp` based on `CompId` and `SessionId`. This indicates a mechanism for clearing temporary data associated with a user's session when loading the page. In Django, this might imply a need for a similar session-bound temporary storage or a different approach to data management. For this conversion, we will acknowledge this, but for this specific module, it doesn't directly translate to a model/form/view.

### Step 3: Infer UI Components

**Analysis:**

-   **Search/Filter Section:**
    -   `DropDownList1`: A dropdown to select search criteria ("Supplier Name" or "Select"). This will be a `forms.ChoiceField` in Django.
    -   `txtSupplier`: A text input for the search term, linked to `AutoCompleteExtender` for supplier name suggestions. This will be a `forms.CharField` with HTMX for autocomplete.
    -   `Button1` (Search): Triggers the search action. This will be an HTMX `hx-trigger`.
-   **Data Display Grid:**
    -   `GridView2`: Displays supplier data in a tabular format. This will be replaced by a standard HTML `<table>` element enhanced with DataTables.
    -   **Columns:**
        -   SN (Serial Number)
        -   Name of Supplier (`SupplierName`)
        -   Code (`SupplierId`)
        -   Freight (input `txtFreight` with validation)
        -   Dropdown (`ddlMhOms` for MH/OMS selection)
        -   Select (LinkButton `lnkButton` for action)
-   **Client-Side Interaction:**
    -   The `AutoCompleteExtender` points to an AJAX service. This will be an HTMX `hx-get` to a `JsonResponse` view for autocomplete.
    -   `AutoPostBack="True"` on `DropDownList1` implies immediate server interaction on change. This will be an HTMX `hx-get` to update the form dynamically.
    -   `onselectedindexchanged`, `onclick`, `onpageindexchanging`, `onrowcommand` all indicate server-side events tied to UI actions. These will be converted to HTMX `hx-trigger` and `hx-post` attributes.

### Step 4: Generate Django Code

**Application Name:** `bill_booking` (reflecting the module's primary function)

---

### 4.1 Models (`bill_booking/models.py`)

This model represents the `tblMM_Supplier_master` table. We'll map `SupId` from the database to Django's `id` primary key field because it's used as the `DataKeyNames` in the original `GridView`.

```python
from django.db import models

class SupplierMaster(models.Model):
    # SupId from DB is used as 'Id' in GridView and is the logical primary key for rows.
    # Map it to Django's 'id' field, making it the primary_key for this model.
    id = models.IntegerField(db_column='SupId', primary_key=True, verbose_name="Supplier ID")
    supplier_name = models.CharField(db_column='SupplierName', max_length=255, verbose_name="Supplier Name")
    supplier_code = models.CharField(db_column='SupplierId', max_length=50, verbose_name="Supplier Code")
    # Assuming these fields exist based on their usage in the ASP.NET code-behind for filtering
    fin_year_id = models.IntegerField(db_column='FinYearId', null=True, blank=True, verbose_name="Financial Year ID")
    comp_id = models.IntegerField(db_column='CompId', null=True, blank=True, verbose_name="Company ID")

    class Meta:
        managed = False  # Django will not manage this table's creation/deletion
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier Master'
        verbose_name_plural = 'Supplier Masters'

    def __str__(self):
        return f"{self.supplier_name} [{self.supplier_code}]"

    @classmethod
    def get_filtered_suppliers(cls, company_id, financial_year_id, search_term=None, search_by_name=True):
        """
        Retrieves a queryset of suppliers based on company, financial year,
        and an optional search term. This method encapsulates the business logic
        from the ASP.NET `loadData` function.
        """
        queryset = cls.objects.filter(
            comp_id=company_id,
            fin_year_id__lte=financial_year_id
        ).exclude(
            # Exclusions from ASP.NET code
            id=417
        ).exclude(
            supplier_code='S098'
        ).order_by('supplier_code') # Matches Order by SupplierId ASC

        if search_term:
            if search_by_name: # Corresponds to DropDownList1.SelectedValue == "0"
                queryset = queryset.filter(supplier_name__icontains=search_term)
            else:
                # The original ASP.NET code doesn't filter by SupplierId (supplier_code)
                # if DropDownList1.SelectedValue is 'Select'. If it's 0 (Supplier Name)
                # and txtSupplier.Text is present, it actually applies 'And SupplierId = spid'
                # which is the _parsed_ supplier id. This is confusing.
                # For modernization, we'll assume 'search_by_name' if anything is entered
                # in txtSupplier, as it's primarily an autocomplete for names.
                pass # If not searching by name, original code doesn't filter by text

        return queryset

    @classmethod
    def get_autocomplete_suggestions(cls, prefix_text, company_id, count=10):
        """
        Provides autocomplete suggestions for supplier names, similar to the `sql` WebMethod.
        Returns formatted strings for display.
        """
        suggestions = cls.objects.filter(
            comp_id=company_id,
            supplier_name__istartswith=prefix_text
        ).values_list('supplier_name', 'supplier_code') # Get name and code

        formatted_suggestions = [f"{name} [{code}]" for name, code in suggestions[:count]]
        formatted_suggestions.sort() # Ensure sorted output as in ASP.NET
        return formatted_suggestions

    def get_absolute_url(self):
        """
        Define a canonical URL for a supplier instance, though not directly used
        for CRUD on this page, good practice for models.
        """
        return reverse('bill_booking:supplier_detail', kwargs={'pk': self.pk}) # Assuming a detail view exists
```

---

### 4.2 Forms (`bill_booking/forms.py`)

This module doesn't perform CRUD on `SupplierMaster`. Instead, it has a search form and fields that are part of the selection process within the grid.

```python
from django import forms
from .models import SupplierMaster

class SupplierSearchForm(forms.Form):
    # Mimics DropDownList1 options
    SEARCH_BY_CHOICES = [
        ('0', 'Supplier Name'),
        # ASP.NET had a 'Select' option that resulted in txtSupplier being hidden.
        # We'll handle this dynamic visibility with Alpine.js and HTMX.
    ]

    search_by = forms.ChoiceField(
        choices=SEARCH_BY_CHOICES,
        label="Search By",
        required=True,
        initial='0', # Default to 'Supplier Name'
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': "{% url 'bill_booking:toggle_supplier_input' %}", # HTMX call to update input visibility
            'hx-target': '#supplier_text_container', # Target div for HTMX swap
            'hx-swap': 'innerHTML',
            'hx-trigger': 'change',
        })
    )
    supplier_text = forms.CharField(
        required=False,
        label="Supplier",
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter supplier name',
            # HTMX attributes for autocomplete
            'hx-get': "{% url 'bill_booking:supplier_autocomplete' %}",
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#autocomplete-results', # Where suggestions will be displayed
            'hx-swap': 'innerHTML',
            'autocomplete': 'off' # Disable browser autocomplete
        })
    )
    # Note: Freight and ddlMhOms are not part of this form; they are per-row inputs.

    def clean(self):
        cleaned_data = super().clean()
        search_by = cleaned_data.get('search_by')
        supplier_text = cleaned_data.get('supplier_text')

        # In ASP.NET, if 'Select' (which we removed) was chosen, txtSupplier became hidden.
        # Here, if search_by is not '0' (Supplier Name), we might clear supplier_text
        # or handle it in the view. For simplicity, the visibility is handled in JS/HTMX.
        return cleaned_data
```

---

### 4.3 Views (`bill_booking/views.py`)

The views will handle displaying the supplier list, processing search queries, managing autocomplete suggestions, and handling the "select" action which redirects to the details page.

```python
from django.views.generic import ListView, View
from django.urls import reverse
from django.shortcuts import render, redirect
from django.http import JsonResponse, HttpResponse
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from .models import SupplierMaster
from .forms import SupplierSearchForm

# Helper function to simulate session data for CompId and FinYearId
# In a real application, these would be managed via user profiles, middleware, or proper session management.
def _get_company_and_fin_year(request):
    # Example: Retrieve from session or default values
    company_id = request.session.get('compid', 1)  # Default to 1 if not in session
    fin_year_id = request.session.get('finyear', 2023) # Default to 2023 if not in session
    return company_id, fin_year_id

class BillBookingSupplierListView(LoginRequiredMixin, ListView):
    model = SupplierMaster
    template_name = 'bill_booking/supplier_list.html'
    context_object_name = 'suppliers'
    paginate_by = 17 # Matches ASP.NET GridView PageSize

    def get_queryset(self):
        company_id, fin_year_id = _get_company_and_fin_year(self.request)
        
        # Determine search parameters from GET request
        search_form = SupplierSearchForm(self.request.GET or None)
        search_term = None
        search_by_name = True # Default to search by supplier name

        if search_form.is_valid():
            search_term = search_form.cleaned_data.get('supplier_text')
            search_by = search_form.cleaned_data.get('search_by', '0')
            search_by_name = (search_by == '0') # '0' means 'Supplier Name'

        # Use the model's business logic to get the filtered queryset
        queryset = SupplierMaster.get_filtered_suppliers(
            company_id=company_id,
            financial_year_id=fin_year_id,
            search_term=search_term,
            search_by_name=search_by_name
        )
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = SupplierSearchForm(self.request.GET or None)
        # Determine initial visibility for supplier_text based on search_by choice
        initial_search_by = self.request.GET.get('search_by', '0')
        context['show_supplier_text'] = (initial_search_by == '0')
        return context

    def render_to_response(self, context, **response_kwargs):
        # If it's an HTMX request, render only the table partial
        if self.request.headers.get('HX-Request'):
            return render(self.request, 'bill_booking/_supplier_table.html', context)
        # Otherwise, render the full page
        return super().render_to_response(context, **response_kwargs)

class SupplierAutocompleteView(LoginRequiredMixin, View):
    """
    Provides JSON responses for supplier autocomplete suggestions.
    This replaces the ASP.NET `sql` WebMethod.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        company_id, _ = _get_company_and_fin_year(request)
        suggestions = SupplierMaster.get_autocomplete_suggestions(query, company_id)
        return JsonResponse(suggestions, safe=False)

class BillBookingSelectSupplierView(LoginRequiredMixin, View):
    """
    Handles the 'Select' action from a supplier row.
    This replaces the ASP.NET `GridView2_RowCommand` logic for 'Sel'.
    """
    def post(self, request, *args, **kwargs):
        # Extract data submitted from the HTMX form/elements
        sup_id = request.POST.get('sup_id') # Corresponds to lblsupId (SupplierId in DB)
        freight = request.POST.get('freight') # Corresponds to txtFreight
        state_type = request.POST.get('state_type') # Corresponds to ddlMhOms

        if state_type == "Select":
            # Replicate ASP.NET client-side alert using Django messages and HTMX trigger
            messages.error(request, 'Select type of Invoice!')
            return HttpResponse(status=204, headers={'HX-Trigger': 'showToast'}) # Trigger Alpine.js toast notification

        # Construct the URL for the details page, matching ASP.NET's redirect behavior
        # Assuming 'bill_booking_details' is the name of the URL pattern for the next page.
        # This will be a standard Django redirect.
        redirect_url = reverse('bill_booking:bill_booking_details')
        full_redirect_url = f"{redirect_url}?SUPId={sup_id}&FGT={freight}&ST={state_type}&ModId=11&SubModId=62"

        if request.headers.get('HX-Request'):
            # If HTMX request, send a client-side redirect header
            return HttpResponse(status=204, headers={'HX-Redirect': full_redirect_url})
        else:
            # For non-HTMX requests (e.g., direct form submission), perform server-side redirect
            return redirect(full_redirect_url)

class ToggleSupplierInputView(View):
    """
    An HTMX target view to dynamically show/hide the supplier text input field
    based on the 'Search By' dropdown selection.
    """
    def get(self, request, *args, **kwargs):
        search_by = request.GET.get('search_by', '0')
        context = {
            'show_supplier_text': (search_by == '0'),
            'form': SupplierSearchForm(request.GET) # Pass form instance to access its widgets
        }
        # Render only the input field or an empty div
        return render(request, 'bill_booking/_supplier_text_input.html', context)
```

---

### 4.4 Templates (`bill_booking/templates/bill_booking/`)

We'll create three templates:
1.  `supplier_list.html`: The main page, extends `core/base.html`.
2.  `_supplier_table.html`: A partial for the DataTables grid, loaded via HTMX.
3.  `_supplier_text_input.html`: A partial for dynamic display of the supplier search input.

#### `supplier_list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Bill Booking - New</h2>
        <div class="flex items-center space-x-4 mb-6" id="search-form-container">
            <form hx-get="{% url 'bill_booking:supplier_list' %}" 
                  hx-target="#supplier-table-container"
                  hx-swap="innerHTML"
                  hx-trigger="submit, keyup changed delay:500ms from:#id_supplier_text">
                {% csrf_token %}
                <div class="flex items-center space-x-4">
                    <div>
                        <label for="{{ form.search_by.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            {{ form.search_by.label }}
                        </label>
                        {{ form.search_by }}
                    </div>
                    <div id="supplier_text_container" {% if not show_supplier_text %}class="hidden"{% endif %}>
                        {# _supplier_text_input.html will be swapped here by HTMX #}
                        {% include 'bill_booking/_supplier_text_input.html' %}
                    </div>
                    <div>
                        <button type="submit" 
                                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200 mt-5"
                                id="search_button"
                                hx-include="#search-form-container"
                                hx-target="#supplier-table-container"
                                hx-swap="innerHTML"
                                hx-indicator="#loading-spinner">
                            Search
                        </button>
                    </div>
                    <div id="loading-spinner" class="htmx-indicator ml-4">
                        <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                        <p class="text-sm text-gray-500">Loading...</p>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div id="supplier-table-container"
         hx-trigger="load, search_button from:#search_button"
         hx-get="{% url 'bill_booking:supplier_list' %}"
         hx-include="#search-form-container"
         hx-swap="innerHTML">
        <!-- Supplier table will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading supplier data...</p>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('autocomplete', () => ({
            results: [],
            selectedIndex: -1,
            init() {
                this.$watch('$el.value', (value) => {
                    if (value.length > 0 && this.results.length > 0) {
                        this.selectedIndex = -1; // Reset selection on new input
                    }
                });
            },
            selectResult(index) {
                if (this.results[index]) {
                    this.$el.value = this.results[index].split(' [')[0]; // Set text box to supplier name
                    this.results = []; // Clear results
                }
            },
            handleKeydown(event) {
                if (event.key === 'ArrowDown') {
                    event.preventDefault();
                    this.selectedIndex = Math.min(this.selectedIndex + 1, this.results.length - 1);
                } else if (event.key === 'ArrowUp') {
                    event.preventDefault();
                    this.selectedIndex = Math.max(this.selectedIndex - 1, 0);
                } else if (event.key === 'Enter') {
                    event.preventDefault();
                    this.selectResult(this.selectedIndex);
                } else if (event.key === 'Escape') {
                    this.results = []; // Clear results
                }
            },
            handleHTMXResponse(event) {
                // HTMX will put data into the #autocomplete-results div.
                // We need to parse it and make it available to Alpine.
                if (event.detail.elt.id === 'autocomplete-results' && event.detail.xhr.responseText) {
                    try {
                        this.results = JSON.parse(event.detail.xhr.responseText);
                    } catch (e) {
                        console.error('Failed to parse autocomplete JSON:', e);
                        this.results = [];
                    }
                }
            },
            clearResults() {
                // Delay clearing results to allow click event to register
                setTimeout(() => {
                    this.results = [];
                }, 100);
            }
        }));

        // Global event listener for showToast message (from views.py)
        document.body.addEventListener('showToast', function(evt) {
            // Assuming you have a toast component in your base.html or a separate JS
            // This is a placeholder for your actual toast notification system
            console.log("Invoice Type Alert Triggered!");
            alert("Select type of Invoice!"); // For quick testing
        });
    });
</script>
{% endblock %}
```

#### `_supplier_table.html`

```html
<div class="bg-white shadow-md rounded-lg overflow-hidden">
    <table id="supplierTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Supplier</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Freight</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice Type</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for supplier in suppliers %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-right text-sm text-gray-900">{{ forloop.counter0|add:page_obj.start_index }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-justify text-sm text-gray-900">{{ supplier.supplier_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-center text-sm text-gray-900">{{ supplier.supplier_code }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-center text-sm text-gray-900">
                    <input type="number" step="0.01" value="0" class="box3 w-20 text-center border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm" name="freight_{{ supplier.id }}" id="freight_{{ supplier.id }}">
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-center text-sm text-gray-900">
                    <select name="state_type_{{ supplier.id }}" id="state_type_{{ supplier.id }}" class="block w-24 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        <option value="Select">Select</option>
                        <option value="0">MH</option>
                        <option value="1">OMS</option>
                    </select>
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-center text-sm font-medium">
                    <button 
                        class="bg-green-600 hover:bg-green-700 text-white font-bold py-1 px-3 rounded transition-colors duration-200"
                        hx-post="{% url 'bill_booking:select_supplier' %}"
                        hx-include="#freight_{{ supplier.id }}, #state_type_{{ supplier.id }}"
                        hx-vals='{"sup_id": "{{ supplier.supplier_code }}"}'
                        hx-trigger="click"
                        hx-swap="none">
                        Select
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 text-center text-lg text-red-700 font-semibold">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    {# Pagination controls for DataTables, if server-side processing is needed #}
    <div class="py-3 px-4 flex justify-between items-center bg-gray-50 border-t border-gray-200">
        <div class="text-sm text-gray-700">
            Showing <span class="font-medium">{{ page_obj.start_index }}</span> to <span class="font-medium">{{ page_obj.end_index }}</span> of <span class="font-medium">{{ page_obj.paginator.count }}</span> results
        </div>
        <div class="flex items-center space-x-2">
            {% if page_obj.has_previous %}
            <button 
                hx-get="{% url 'bill_booking:supplier_list' %}?page={{ page_obj.previous_page_number }}{% if request.GET.supplier_text %}&supplier_text={{ request.GET.supplier_text }}{% endif %}{% if request.GET.search_by %}&search_by={{ request.GET.search_by }}{% endif %}"
                hx-target="#supplier-table-container"
                hx-swap="innerHTML"
                class="px-3 py-1 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors duration-200">
                Previous
            </button>
            {% endif %}
            <span class="px-3 py-1 bg-blue-600 text-white rounded-md">{{ page_obj.number }}</span>
            {% if page_obj.has_next %}
            <button 
                hx-get="{% url 'bill_booking:supplier_list' %}?page={{ page_obj.next_page_number }}{% if request.GET.supplier_text %}&supplier_text={{ request.GET.supplier_text }}{% endif %}{% if request.GET.search_by %}&search_by={{ request.GET.search_by }}{% endif %}"
                hx-target="#supplier-table-container"
                hx-swap="innerHTML"
                class="px-3 py-1 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors duration-200">
                Next
            </button>
            {% endif %}
        </div>
    </div>
</div>

<script>
    // Initialize DataTables after HTMX loads the content
    // Note: Server-side pagination is handled by Django, so DataTables client-side
    // pagination/search features should be limited or configured for server-side.
    // For this example, we'll keep it simple, letting Django handle pagination,
    // and DataTables will primarily handle client-side sorting/search *within the current page*.
    // For full server-side DataTables, you'd need a separate view returning JSON.
    $(document).ready(function() {
        $('#supplierTable').DataTable({
            "paging": false, // Disable DataTables built-in paging since Django handles it
            "info": false, // Disable info text
            "searching": false, // Disable global search input as we have custom search
            "lengthChange": false, // Disable "Show X entries" dropdown
            "columnDefs": [
                { "orderable": false, "targets": [0, 3, 4, 5] } // Disable sorting for SN, Freight, Invoice Type, Actions
            ]
        });
    });
</script>
```

#### `_supplier_text_input.html`

This partial is responsible for rendering just the `supplier_text` input, to be swapped by HTMX.

```html
{% load tailwind_filters %}

{# This block is conditional based on whether 'Supplier Name' is selected in the dropdown #}
{% if show_supplier_text %}
<div x-data="autocomplete()" @htmx:after-on-load.camel="handleHTMXResponse($event)">
    <label for="{{ form.supplier_text.id_for_label }}" class="block text-sm font-medium text-gray-700">
        {{ form.supplier_text.label }}
    </label>
    {{ form.supplier_text }}
    {% if form.supplier_text.errors %}
        <p class="text-red-500 text-xs mt-1">{{ form.supplier_text.errors }}</p>
    {% endif %}
    {# Autocomplete results container #}
    <div id="autocomplete-results" 
         class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto mt-1 w-72" 
         x-show="results.length > 0" 
         @click.away="clearResults()"
         x-transition:enter="transition ease-out duration-100"
         x-transition:enter-start="opacity-0 scale-95"
         x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-75"
         x-transition:leave-start="opacity-100 scale-100"
         x-transition:leave-end="opacity-0 scale-95">
        <ul class="py-1">
            <template x-for="(result, index) in results" :key="index">
                <li :class="{ 'bg-blue-500 text-white': index === selectedIndex, 'hover:bg-gray-100': index !== selectedIndex }"
                    class="px-4 py-2 cursor-pointer text-sm"
                    @click="selectResult(index)">
                    <span x-text="result"></span>
                </li>
            </template>
        </ul>
    </div>
</div>
{% else %}
{# If not showing, render an empty div to maintain layout stability #}
<div class="h-14"></div> {# Maintain height to prevent layout shift #}
{% endif %}

```

---

### 4.5 URLs (`bill_booking/urls.py`)

Define the URL patterns that map to the views.

```python
from django.urls import path
from .views import (
    BillBookingSupplierListView,
    SupplierAutocompleteView,
    BillBookingSelectSupplierView,
    ToggleSupplierInputView,
)

app_name = 'bill_booking' # Namespace for URLs

urlpatterns = [
    # Main page for listing and searching suppliers
    path('', BillBookingSupplierListView.as_view(), name='supplier_list'),
    
    # Endpoint for supplier autocomplete suggestions
    path('autocomplete/supplier/', SupplierAutocompleteView.as_view(), name='supplier_autocomplete'),
    
    # Endpoint to handle the selection of a supplier from the list
    path('select-supplier/', BillBookingSelectSupplierView.as_view(), name='select_supplier'),
    
    # HTMX endpoint to dynamically toggle visibility of supplier input field
    path('toggle-supplier-input/', ToggleSupplierInputView.as_view(), name='toggle_supplier_input'),

    # Placeholder for the Bill Booking Details page
    # In a real scenario, this would lead to another Django app/module.
    # We include it here to ensure the reverse() call works.
    path('bill-booking-details/', View.as_view(), name='bill_booking_details'), 
]
```

---

### 4.6 Tests (`bill_booking/tests.py`)

Comprehensive tests for both the `SupplierMaster` model and the corresponding views are crucial for ensuring the migrated functionality behaves as expected and for future maintenance.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from .models import SupplierMaster
from .forms import SupplierSearchForm
from unittest.mock import patch # For mocking session

class SupplierMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        SupplierMaster.objects.create(
            id=101,  # Corresponds to SupId in DB
            supplier_name='Test Supplier A',
            supplier_code='SA001', # Corresponds to SupplierId in DB
            fin_year_id=2023,
            comp_id=1
        )
        SupplierMaster.objects.create(
            id=102,
            supplier_name='Another Supplier B',
            supplier_code='SB002',
            fin_year_id=2023,
            comp_id=1
        )
        SupplierMaster.objects.create(
            id=417, # Excluded in business logic
            supplier_name='Excluded Supplier X',
            supplier_code='S098', # Also excluded
            fin_year_id=2023,
            comp_id=1
        )
        SupplierMaster.objects.create(
            id=103,
            supplier_name='Supplier C for Another Company',
            supplier_code='SC003',
            fin_year_id=2023,
            comp_id=2 # Different company
        )
        # Add another supplier for autocomplete test
        SupplierMaster.objects.create(
            id=104,
            supplier_name='Apple Inc.',
            supplier_code='APL01',
            fin_year_id=2023,
            comp_id=1
        )
        SupplierMaster.objects.create(
            id=105,
            supplier_name='Apple Corp.',
            supplier_code='APC01',
            fin_year_id=2023,
            comp_id=1
        )

    def test_supplier_creation(self):
        supplier = SupplierMaster.objects.get(id=101)
        self.assertEqual(supplier.supplier_name, 'Test Supplier A')
        self.assertEqual(supplier.supplier_code, 'SA001')
        self.assertEqual(supplier.fin_year_id, 2023)
        self.assertEqual(supplier.comp_id, 1)

    def test_str_representation(self):
        supplier = SupplierMaster.objects.get(id=101)
        self.assertEqual(str(supplier), 'Test Supplier A [SA001]')

    def test_get_filtered_suppliers_no_search(self):
        # Excluded suppliers: id=417, supplier_code='S098'
        # Total created: 6. Excluded: 2 (id 417 and S098, which is the same entry).
        # Plus id 103 for comp_id=2. So for comp_id=1, there should be 3 visible.
        suppliers = SupplierMaster.get_filtered_suppliers(company_id=1, financial_year_id=2023)
        self.assertEqual(suppliers.count(), 4) # 101, 102, 104, 105
        self.assertIn(SupplierMaster.objects.get(id=101), suppliers)
        self.assertIn(SupplierMaster.objects.get(id=102), suppliers)
        self.assertNotIn(SupplierMaster.objects.get(id=417), suppliers) # Excluded by ID
        self.assertNotIn(SupplierMaster.objects.get(id=103), suppliers) # Excluded by company

    def test_get_filtered_suppliers_by_name(self):
        suppliers = SupplierMaster.get_filtered_suppliers(
            company_id=1, financial_year_id=2023, search_term='test', search_by_name=True
        )
        self.assertEqual(suppliers.count(), 1)
        self.assertEqual(suppliers.first().supplier_name, 'Test Supplier A')

    def test_get_filtered_suppliers_different_fin_year(self):
        # Create a supplier with a future financial year
        SupplierMaster.objects.create(
            id=106, supplier_name='Future Supplier', supplier_code='FUT01', fin_year_id=2024, comp_id=1
        )
        suppliers = SupplierMaster.get_filtered_suppliers(company_id=1, financial_year_id=2023)
        self.assertEqual(suppliers.count(), 4) # Should not include FUT01
        self.assertNotIn(SupplierMaster.objects.get(id=106), suppliers)

        suppliers_future = SupplierMaster.get_filtered_suppliers(company_id=1, financial_year_id=2024)
        self.assertEqual(suppliers_future.count(), 5) # Should include FUT01 now

    def test_get_autocomplete_suggestions(self):
        suggestions = SupplierMaster.get_autocomplete_suggestions('app', company_id=1)
        self.assertIn('Apple Inc. [APL01]', suggestions)
        self.assertIn('Apple Corp. [APC01]', suggestions)
        self.assertEqual(len(suggestions), 2)
        self.assertEqual(suggestions[0], 'Apple Corp. [APC01]') # Verify sort order
        self.assertEqual(suggestions[1], 'Apple Inc. [APL01]')

    def test_get_autocomplete_suggestions_case_insensitive(self):
        suggestions = SupplierMaster.get_autocomplete_suggestions('Apple', company_id=1)
        self.assertEqual(len(suggestions), 2)

    def test_get_autocomplete_suggestions_no_match(self):
        suggestions = SupplierMaster.get_autocomplete_suggestions('xyz', company_id=1)
        self.assertEqual(len(suggestions), 0)


class BillBookingViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        SupplierMaster.objects.create(id=101, supplier_name='Alpha Supplier', supplier_code='A001', fin_year_id=2023, comp_id=1)
        SupplierMaster.objects.create(id=102, supplier_name='Beta Supplier', supplier_code='B002', fin_year_id=2023, comp_id=1)
        SupplierMaster.objects.create(id=103, supplier_name='Gamma Supplier', supplier_code='G003', fin_year_id=2023, comp_id=1)
        SupplierMaster.objects.create(id=417, supplier_name='Excluded Supplier', supplier_code='S098', fin_year_id=2023, comp_id=1) # Excluded

    def setUp(self):
        self.client = Client()
        # Mock login and session data for views that require it
        self.user = self.client.login(username='testuser', password='password') # Assuming a test user exists
        # Patch the internal helper function to control company_id and fin_year_id
        self.patcher = patch('bill_booking.views._get_company_and_fin_year')
        self.mock_get_session_data = self.patcher.start()
        self.mock_get_session_data.return_value = (1, 2023) # Default company 1, fin year 2023

    def tearDown(self):
        self.patcher.stop() # Stop patching after each test

    def test_supplier_list_view_get(self):
        response = self.client.get(reverse('bill_booking:supplier_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bill_booking/supplier_list.html')
        self.assertIn('suppliers', response.context)
        self.assertEqual(response.context['suppliers'].count(), 3) # Should exclude the excluded supplier

    def test_supplier_list_view_search_by_name(self):
        response = self.client.get(reverse('bill_booking:supplier_list'), {'search_by': '0', 'supplier_text': 'alpha'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bill_booking/supplier_list.html')
        self.assertEqual(response.context['suppliers'].count(), 1)
        self.assertEqual(response.context['suppliers'].first().supplier_name, 'Alpha Supplier')

    def test_supplier_list_view_htmx_partial_load(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('bill_booking:supplier_list'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bill_booking/_supplier_table.html')
        self.assertIn('suppliers', response.context)
        self.assertEqual(response.context['suppliers'].count(), 3)

    def test_supplier_autocomplete_view(self):
        response = self.client.get(reverse('bill_booking:supplier_autocomplete'), {'q': 'beta'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        self.assertEqual(response.json(), ['Beta Supplier [B002]'])

    def test_supplier_autocomplete_view_no_query(self):
        response = self.client.get(reverse('bill_booking:supplier_autocomplete'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        self.assertEqual(response.json(), []) # Empty query returns empty list

    def test_bill_booking_select_supplier_view_valid_post(self):
        select_data = {
            'sup_id': 'A001',
            'freight': '12.34',
            'state_type': '0' # MH
        }
        # Test non-HTMX request (server-side redirect)
        response = self.client.post(reverse('bill_booking:select_supplier'), select_data)
        self.assertEqual(response.status_code, 302) # Redirect
        self.assertRedirects(response, '/bill-booking/bill-booking-details/?SUPId=A001&FGT=12.34&ST=0&ModId=11&SubModId=62')

    def test_bill_booking_select_supplier_view_invalid_state_type(self):
        select_data = {
            'sup_id': 'A001',
            'freight': '12.34',
            'state_type': 'Select' # Invalid
        }
        response = self.client.post(reverse('bill_booking:select_supplier'), select_data)
        self.assertEqual(response.status_code, 204) # No Content, indicating HTMX message trigger
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('showToast', response.headers['HX-Trigger'])
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Select type of Invoice!')

    def test_bill_booking_select_supplier_view_htmx_redirect(self):
        select_data = {
            'sup_id': 'A001',
            'freight': '12.34',
            'state_type': '0' # MH
        }
        response = self.client.post(reverse('bill_booking:select_supplier'), select_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content
        self.assertIn('HX-Redirect', response.headers)
        self.assertEqual(response.headers['HX-Redirect'], '/bill-booking/bill-booking-details/?SUPId=A001&FGT=12.34&ST=0&ModId=11&SubModId=62')

    def test_toggle_supplier_input_view_show(self):
        response = self.client.get(reverse('bill_booking:toggle_supplier_input'), {'search_by': '0'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bill_booking/_supplier_text_input.html')
        self.assertIn('show_supplier_text', response.context)
        self.assertTrue(response.context['show_supplier_text'])
        # Check if the input field is rendered
        self.assertContains(response, 'name="supplier_text"')

    def test_toggle_supplier_input_view_hide(self):
        # Simulate selecting a different option that hides the input
        response = self.client.get(reverse('bill_booking:toggle_supplier_input'), {'search_by': '1'}) # Assuming '1' means hide
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bill_booking/_supplier_text_input.html')
        self.assertIn('show_supplier_text', response.context)
        self.assertFalse(response.context['show_supplier_text'])
        # Check if the input field is NOT rendered or hidden
        self.assertNotContains(response, 'name="supplier_text"') # The template renders a hidden div, so this check works
```

---

### Step 5: HTMX and Alpine.js Integration

**Instructions and Implementation Summary:**

-   **HTMX for dynamic updates:**
    -   The main `supplier_list.html` uses `hx-trigger="load"` and `hx-get` to load the `_supplier_table.html` initially and for refreshes after searches/pagination.
    -   The search form uses `hx-get` on submit and keyup (for `supplier_text`) to re-fetch the table content.
    -   The `search_by` dropdown (`form.search_by`) uses `hx-get` to `toggle_supplier_input` to dynamically show/hide the `supplier_text` input, replacing the ASP.NET `AutoPostBack` behavior.
    -   The "Select" button within each row uses `hx-post` to `select_supplier`, passing `sup_id`, `freight`, and `state_type` values from the current row using `hx-include`. For successful selection, it uses `HX-Redirect` to navigate to the details page, mimicking ASP.NET's `Response.Redirect`. For validation errors, it triggers a client-side toast.
    -   Pagination buttons use `hx-get` to fetch specific pages of the table, targeting the `supplier-table-container`.
    -   Autocomplete for `supplier_text` uses `hx-get` to `supplier_autocomplete` and `hx-target` to `#autocomplete-results`.
-   **Alpine.js for UI state management:**
    -   An `x-data="autocomplete()"` component is implemented to manage the autocomplete suggestion list, keyboard navigation (ArrowUp/Down, Enter), and selection. It listens to HTMX events (`htmx:after-on-load.camel`) to receive JSON results and update its internal `results` array.
    -   Used `x-show` to conditionally display the autocomplete results dropdown.
    -   Used `x-transition` for smoother transitions of the autocomplete dropdown.
-   **DataTables for list views:**
    -   The `_supplier_table.html` includes a `<table id="supplierTable">` element.
    -   A `<script>` block within `_supplier_table.html` initializes DataTables on `$(document).ready()`. This ensures DataTables is applied every time the partial is loaded by HTMX.
    -   Since Django handles server-side pagination, DataTables' built-in pagination, searching, and length change controls are disabled (`"paging": false`, `"searching": false`, etc.) to avoid conflict and ensure consistent behavior with the server-driven filtering. Client-side sorting is maintained for flexibility where desired.
-   **No custom JavaScript requirements beyond HTMX/Alpine/jQuery for DataTables:** Adheres to the principle of minimal custom JS. All dynamic interactions are managed by HTMX and Alpine.js.
-   **DRY template inheritance:** All templates extend `core/base.html`, ensuring all CDN links (HTMX, Alpine.js, jQuery, DataTables, Tailwind) are in one place.

---

This comprehensive plan provides a clear, actionable roadmap for migrating the ASP.NET Bill Booking module to a modern Django application. The structure facilitates automated conversion processes, while the use of contemporary technologies ensures a robust, scalable, and user-friendly solution.