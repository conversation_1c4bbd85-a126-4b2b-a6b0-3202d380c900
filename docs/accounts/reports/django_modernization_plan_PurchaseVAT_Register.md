This comprehensive plan outlines the modernization of your ASP.NET Purchase VAT Register application to a robust, maintainable, and high-performance Django solution. By leveraging AI-assisted automation, we can systematically transform your legacy code into a modern architecture, focusing on business value and seamless user experience.

## ASP.NET to Django Conversion Script:

This plan prioritizes an automation-driven approach, where the detailed breakdown of Django components can be used by conversational AI tools to generate the actual code. The focus is on translating the business logic and UI interactions into Django's "Fat Model, Thin View" paradigm, enhanced with HTMX, Alpine.js, and DataTables for a dynamic frontend.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include `base.html` template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module (`accounts` for Purchase VAT Register).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Business Value:** Understanding the underlying data structure is crucial for accurate data migration and ensuring data integrity in the new system. This step ensures that the new Django models precisely reflect your existing database, preventing data loss or corruption during the transition.

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
- From the C# code-behind, we identify the following tables and their relevant columns used in the main SQL query and subsequent lookups:
    - `tblACC_BillBooking_Master`: `Id` (PK), `SysDate`, `SupplierId` (FK), `CompId`
    - `tblACC_BillBooking_Details`: `Id` (PK - inferred, or part of composite key), `MId` (FK to `tblACC_BillBooking_Master`), `GQNId` (FK to `tblQc_MaterialQuality_Details`), `PODId` (FK to `tblMM_PO_Details`), `PFAmt`, `ExStBasic`, `ExStEducess`, `ExStShecess`, `VAT`, `CST`, `Freight`
    - `tblQc_MaterialQuality_Details`: `Id` (PK), `AcceptedQty`
    - `tblMM_PO_Details`: `Id` (PK), `PF` (FK to `tblPacking_Master`), `ExST` (FK to `tblExciseser_Master`), `VAT` (FK to `tblVAT_Master`), `Rate`, `Discount`
    - `tblMM_Supplier_master`: `SupplierId` (PK), `CompId`, `SupplierName`
    - `tblPacking_Master`: `Id` (PK), `Value`
    - `tblExciseser_Master`: `Id` (PK), `Value`, `AccessableValue`, `EDUCess`, `SHECess`
    - `tblVAT_Master`: `Id` (PK), `Value`, `IsVAT`, `IsCST`

### Step 2: Identify Backend Functionality

**Business Value:** By isolating the core business logic, we can ensure that the new Django application replicates existing functionalities accurately, providing continuity for your operations. Moving complex calculations to Django models improves maintainability and testability.

**Task:** Determine the data processing and report generation logic in the ASP.NET code.

**Instructions:**
- **Report Generation (Read Operation):** The primary functionality is to generate a "Purchase VAT Register" report.
    - Data is fetched from multiple joined tables: `tblACC_BillBooking_Details`, `tblACC_BillBooking_Master`, `tblQc_MaterialQuality_Details`, `tblMM_PO_Details`.
    - **Filtering:** The report is filtered by `CompId` (Company ID from session) and a date range (`Fdate`, `Tdate` from query string).
    - **Aggregations:** The SQL query performs `SUM` operations on quantities and various financial amounts (`qty`, `amt`, `pfamt`, `exba`, `eduBasic`, `edu`, `she`, `vat1`, `cst`, `fr`).
    - **Grouping:** Data is grouped by `PF`, `ExST`, `VAT`, `SupplierId`, `SysDate`.
    - **Post-processing and Lookups:**
        - Additional `SELECT` queries (mimicked by `fun.select` calls) are made for each row to fetch `SupplierName`, `Packing_Master.Value`, `Exciseser_Master` details, and `VAT_Master` details based on IDs.
        - Complex calculations are performed in C# for `ExciseAmt`, `VatCst`, `TotAmt`.
        - Global totals are accumulated: `VATGrossTotal`, `CSTGrossTotal`, `TotalExcise`.
        - Grouping for `MAHPurchase` string (`VATCSTTerms,VATCSTAmt`) is performed on the C# `DataTable`.
- **Navigation:** The `btnCancel` redirects the user to `Purchase_Reprt.aspx`. This will be translated to a Django URL redirect.

### Step 3: Infer UI Components

**Business Value:** Reimagining the user interface with modern web technologies like HTMX and Alpine.js ensures a more interactive and responsive user experience, boosting productivity and user satisfaction without requiring complex JavaScript frameworks.

**Task:** Analyze ASP.NET controls and their roles, and envision their Django/HTMX/Alpine.js equivalents.

**Instructions:**
- **CrystalReportViewer:** This complex reporting component will be replaced by a standard HTML `<table>` element rendered by Django, dynamically enhanced with DataTables for client-side features (search, sort, paginate). The report data will be formatted directly within Django templates.
- **`btnCancel` (ASP.NET Button):** This will be a standard HTML button or link in Django, directing to the appropriate previous page URL.
- **Input Fields (Inferred):** Although not explicitly present in the `.aspx` snippet, the presence of `Fdate` and `Tdate` query strings implies input fields (likely text boxes or date pickers) for date range selection on a preceding page or directly on this page. For Django, we will assume a simple form with date input fields.
- **Styling:** The existing `StyleSheet.css` will be replaced by Tailwind CSS.

### Step 4: Generate Django Code

We will create a new Django application named `accounts` to house this functionality.

#### 4.1 Models (`accounts/models.py`)

**Business Value:** Django models, mapped to your existing database, provide a robust and object-oriented way to interact with your data. By implementing complex report generation logic within a custom manager (`PurchaseVATReportManager`), we centralize business rules, making them easier to manage, test, and reuse. This "Fat Model" approach significantly improves code organization and maintainability.

**Task:** Create Django models based on the identified database schema and implement the report generation logic within a custom manager.

**Instructions:**
- Define models for each identified table (`tblACC_BillBooking_Master`, `tblACC_BillBooking_Details`, `tblMM_PO_Details`, `tblQc_MaterialQuality_Details`, `tblMM_Supplier_master`, `tblPacking_Master`, `tblExciseser_Master`, `tblVAT_Master`).
- Set `managed = False` and `db_table` for each model to connect to the existing database.
- Create a `PurchaseVATReportManager` for `BillBookingMaster` to encapsulate the comprehensive report logic previously in the C# `Page_Init` method. This manager will perform the joins, aggregations, and post-processing.

```python
# accounts/models.py
from django.db import models
from django.db.models import Sum, F, ExpressionWrapper, DecimalField
from datetime import date
from django.utils import timezone

# --- Core Models mapping to existing tables ---

class Supplier(models.Model):
    supplier_id = models.IntegerField(db_column='SupplierId', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId')
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.supplier_name

class Packing(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    value = models.CharField(db_column='Value', max_length=255) # Assuming 'Value' can be text like percentage

    class Meta:
        managed = False
        db_table = 'tblPacking_Master'
        verbose_name = 'Packing Term'
        verbose_name_plural = 'Packing Terms'

    def __str__(self):
        return self.value

class Excise(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    value = models.CharField(db_column='Value', max_length=255) # Assuming 'Value' can be text like percentage
    accessible_value = models.CharField(db_column='AccessableValue', max_length=255)
    edu_cess = models.CharField(db_column='EDUCess', max_length=255) # Assuming percentages as strings
    she_cess = models.CharField(db_column='SHECess', max_length=255) # Assuming percentages as strings

    class Meta:
        managed = False
        db_table = 'tblExciseser_Master'
        verbose_name = 'Excise Service'
        verbose_name_plural = 'Excise Services'

    def __str__(self):
        return self.value

class VAT(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    value = models.CharField(db_column='Value', max_length=255) # Assuming percentage as string
    is_vat = models.BooleanField(db_column='IsVAT') # 0 or 1
    is_cst = models.BooleanField(db_column='IsCST') # 0 or 1

    class Meta:
        managed = False
        db_table = 'tblVAT_Master'
        verbose_name = 'VAT/CST Term'
        verbose_name_plural = 'VAT/CST Terms'

    def __str__(self):
        return self.value

class POMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    pf_id = models.ForeignKey(Packing, on_delete=models.DO_NOTHING, db_column='PF', related_name='po_masters')
    ex_st_id = models.ForeignKey(Excise, on_delete=models.DO_NOTHING, db_column='ExST', related_name='po_masters')
    vat_id = models.ForeignKey(VAT, on_delete=models.DO_NOTHING, db_column='VAT', related_name='po_masters')
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=4)
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=4)

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Details'
        verbose_name = 'PO Detail'
        verbose_name_plural = 'PO Details'

    def __str__(self):
        return f"PO Detail {self.id}"

class MaterialQualityDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    accepted_qty = models.DecimalField(db_column='AcceptedQty', max_digits=18, decimal_places=4)

    class Meta:
        managed = False
        db_table = 'tblQc_MaterialQuality_Details'
        verbose_name = 'Material Quality Detail'
        verbose_name_plural = 'Material Quality Details'

    def __str__(self):
        return f"Quality Detail {self.id}"

# --- Custom Manager for Report Logic ---

class PurchaseVATReportManager(models.Manager):
    def get_purchase_vat_register_report(self, company_id: int, from_date: date, to_date: date):
        """
        Encapsulates the complex C# data retrieval and processing logic for the Purchase VAT Register.
        This method aims to replicate the original report generation, including joins, aggregations,
        and post-processing calculations as seen in the C# Page_Init.
        """
        # Ensure dates are timezone-aware if SysDate is a DateTimeField and DB is timezone-aware
        from_date_tz = timezone.make_aware(timezone.datetime(from_date.year, from_date.month, from_date.day, 0, 0, 0))
        to_date_tz = timezone.make_aware(timezone.datetime(to_date.year, to_date.month, to_date.day, 23, 59, 59))

        # Define the 'amt' calculation (AcceptedQty * (Rate - (Rate * Discount / 100)))
        amt_expression = ExpressionWrapper(
            F('gqn_id__accepted_qty') * (
                F('pod_id__rate') - (F('pod_id__rate') * F('pod_id__discount') / 100)
            ),
            output_field=DecimalField(max_digits=18, decimal_places=4)
        )

        # Mimic the main SQL query for grouped data using Django ORM aggregation
        # and prefetch_related for subsequent lookups to minimize queries.
        raw_report_data = BillBookingDetail.objects.filter(
            master__comp_id=company_id,
            master__sys_date__range=(from_date_tz, to_date_tz)
        ).values(
            'pod_id__pf_id',
            'pod_id__ex_st_id',
            'pod_id__vat_id',
            'master__sys_date',
            'master__supplier__supplier_id',
            'master__supplier__supplier_name', # Include supplier name for efficiency
        ).annotate(
            total_qty=Sum('gqn_id__accepted_qty'),
            total_amt=Sum(amt_expression),
            total_pf_amt=Sum('pf_amt'),
            total_ex_st_basic=Sum('ex_st_basic'),
            total_ex_st_educess=Sum('ex_st_educess'),
            total_ex_st_shecess=Sum('ex_st_shecess'),
            total_vat=Sum('vat'),
            total_cst=Sum('cst'),
            total_freight=Sum('freight'),
        ).order_by('master__sys_date', 'master__supplier__supplier_id')

        report_rows = []
        vat_gross_total = 0.0
        cst_gross_total = 0.0
        total_excise = 0.0
        
        # Cache for lookup values to minimize repeated DB queries
        # (Alternatively, prefetch_related could be used on the initial queryset,
        # but the original C# does individual lookups per row)
        packing_cache = {p.id: p.value for p in Packing.objects.all()}
        excise_cache = {e.id: e for e in Excise.objects.all()}
        vat_cache = {v.id: v for v in VAT.objects.all()}

        # Store VAT/CST terms and amounts for MAHPurchase string (similar to C# DataTable grouping)
        vat_cst_grouped_totals = {}

        # Step 2: Post-processing and lookups, similar to C# loop
        for row in raw_report_data:
            sys_date = row['master__sys_date'].strftime('%d/%m/%Y')
            supplier_id = row['master__supplier__supplier_id']
            supplier_name = row['master__supplier__supplier_name']
            
            packing_value = packing_cache.get(row['pod_id__pf_id'], 'N/A')
            
            excise_obj = excise_cache.get(row['pod_id__ex_st_id'])
            excise_value = excise_obj.value if excise_obj else 'N/A'
            excise_accessible_value = excise_obj.accessible_value if excise_obj else 'N/A'

            vat_obj = vat_cache.get(row['pod_id__vat_id'])
            vat_cst_term_value = vat_obj.value if vat_obj else 'N/A'
            is_vat = vat_obj.is_vat if vat_obj else False
            is_cst = vat_obj.is_cst if vat_obj else False
            
            current_vat_cst_amt = 0.0
            if is_vat:
                current_vat_cst_amt = float(row['total_vat'] or 0)
            elif is_cst:
                current_vat_cst_amt = float(row['total_cst'] or 0)
            else: # Matches C# where IsVAT=0 and IsCST=0 still uses 'vat1' field (total_vat)
                current_vat_cst_amt = float(row['total_vat'] or 0)

            basic_amt = float(row['total_amt'] or 0)
            pf_amt = float(row['total_pf_amt'] or 0)
            ex_st_basic = float(row['total_ex_st_basic'] or 0)
            ex_st_educess = float(row['total_ex_st_educess'] or 0)
            ex_st_shecess = float(row['total_ex_st_shecess'] or 0)
            freight_amt = float(row['total_freight'] or 0)

            calculated_excise_amt = ex_st_basic + ex_st_educess + ex_st_shecess
            
            current_row_total = basic_amt + pf_amt + ex_st_basic + ex_st_educess + ex_st_shecess + current_vat_cst_amt + freight_amt
            
            if not is_cst:
                vat_gross_total += current_row_total
            else:
                cst_gross_total += current_row_total
            
            total_excise += calculated_excise_amt

            # Accumulate VAT/CST terms for MAHPurchase string
            term_key = vat_cst_term_value
            vat_cst_grouped_totals[term_key] = vat_cst_grouped_totals.get(term_key, 0.0) + current_vat_cst_amt

            report_rows.append({
                'sys_date': sys_date,
                'comp_id': company_id,
                'supplier_name': f"{supplier_name} [{supplier_id}]",
                'basic_amt': basic_amt,
                'pf_terms': packing_value,
                'pf_amt': pf_amt,
                'excise_values': excise_value,
                'excise_amt': calculated_excise_amt,
                'edu_cess': excise_obj.edu_cess if excise_obj else 'N/A',
                'edu_value': ex_st_educess,
                'she_cess': excise_obj.she_cess if excise_obj else 'N/A',
                'she_value': ex_st_shecess,
                'vat_cst_terms': vat_cst_term_value,
                'vat_cst_amt': current_vat_cst_amt,
                'freight_amt': freight_amt,
                'total_amt': current_row_total,
                'excise_basic_term': excise_accessible_value,
                'ex_basic_amt': ex_st_basic,
            })
            
        mah_purchase_string = ""
        for term, amount in vat_cst_grouped_totals.items():
            mah_purchase_string += f"@ {term}    Amt: {amount:.2f},  "

        return {
            'report_data': report_rows,
            'vat_gross_total': round(vat_gross_total, 2),
            'cst_gross_total': round(cst_gross_total, 2),
            'total_excise': round(total_excise, 2),
            'mah_purchase': mah_purchase_string.strip(', ')
        }

# --- Main BillBookingMaster model with custom manager ---

class BillBookingMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateTimeField(db_column='SysDate')
    supplier = models.ForeignKey(Supplier, on_delete=models.DO_NOTHING, db_column='SupplierId', related_name='bill_bookings')
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblACC_BillBooking_Master'
        verbose_name = 'Bill Booking Master'
        verbose_name_plural = 'Bill Booking Masters'

    # Add the custom manager to the existing BillBookingMaster model
    objects = models.Manager() # Default manager
    purchase_vat_reports = PurchaseVATReportManager() # Custom manager for reports

    def __str__(self):
        return f"Bill Booking {self.id} on {self.sys_date.strftime('%Y-%m-%d')}"

class BillBookingDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming PK exists
    master = models.ForeignKey(BillBookingMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    gqn_id = models.ForeignKey(MaterialQualityDetail, on_delete=models.DO_NOTHING, db_column='GQNId', related_name='bill_booking_details')
    pod_id = models.ForeignKey(POMaster, on_delete=models.DO_NOTHING, db_column='PODId', related_name='bill_booking_details')
    pf_amt = models.DecimalField(db_column='PFAmt', max_digits=18, decimal_places=4)
    ex_st_basic = models.DecimalField(db_column='ExStBasic', max_digits=18, decimal_places=4)
    ex_st_educess = models.DecimalField(db_column='ExStEducess', max_digits=18, decimal_places=4)
    ex_st_shecess = models.DecimalField(db_column='ExStShecess', max_digits=18, decimal_places=4)
    vat = models.DecimalField(db_column='VAT', max_digits=18, decimal_places=4)
    cst = models.DecimalField(db_column='CST', max_digits=18, decimal_places=4)
    freight = models.DecimalField(db_column='Freight', max_digits=18, decimal_places=4)

    class Meta:
        managed = False
        db_table = 'tblACC_BillBooking_Details'
        verbose_name = 'Bill Booking Detail'
        verbose_name_plural = 'Bill Booking Details'

    def __str__(self):
        return f"Bill Booking Detail {self.id}"

```

#### 4.2 Forms (`accounts/forms.py`)

**Business Value:** Forms in Django ensure structured and validated user input. By separating form definition, we create clear boundaries for input handling and enable reusable components, contributing to a more robust and user-friendly system.

**Task:** Define a Django form for capturing date range input.

**Instructions:**
- Create a simple `Form` (not a `ModelForm` as it's not directly tied to a database model in this case, but for input parameters).
- Include `from_date` and `to_date` fields with appropriate widgets for styling.

```python
# accounts/forms.py
from django import forms
from datetime import date

class PurchaseVATReportForm(forms.Form):
    # Assuming the date format is YYYY-MM-DD
    from_date = forms.DateField(
        label='From Date',
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'required': 'required'
        }),
        initial=date.today
    )
    to_date = forms.DateField(
        label='To Date',
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'required': 'required'
        }),
        initial=date.today
    )

    def clean(self):
        cleaned_data = super().clean()
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')

        if from_date and to_date and from_date > to_date:
            raise forms.ValidationError("From Date cannot be after To Date.")
        return cleaned_data

```

#### 4.3 Views (`accounts/views.py`)

**Business Value:** By adhering to the "Thin View" principle, we keep our views concise and focused on orchestrating requests and responses. This makes the application logic easier to trace, debug, and scale, ensuring a more efficient and maintainable system. Views handle user input and data presentation, while all heavy lifting is delegated to the models.

**Task:** Implement a `TemplateView` to display the Purchase VAT Register report and an auxiliary view for the HTMX-loaded table.

**Instructions:**
- `PurchaseVATRegisterReportView`: Handles the main report page. It processes date range input from the form and retrieves the report data from the `PurchaseVATReportManager`. It is responsible for rendering the initial report page.
- `PurchaseVATRegisterTableView`: This partial view is specifically for HTMX requests. It takes the date parameters and company ID, calls the report manager, and renders *only* the table content.

```python
# accounts/views.py
from django.views.generic import TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from datetime import date
from .models import BillBookingMaster # Access the custom manager via this model
from .forms import PurchaseVATReportForm

class PurchaseVATRegisterReportView(TemplateView):
    template_name = 'accounts/purchase_vat_register/report.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize form with current date or GET parameters if available
        form = PurchaseVATReportForm(self.request.GET or {'from_date': date.today(), 'to_date': date.today()})
        context['form'] = form
        
        # Initial empty report data for the first load
        context['report_data'] = []
        context['vat_gross_total'] = 0.0
        context['cst_gross_total'] = 0.0
        context['total_excise'] = 0.0
        context['mah_purchase'] = "No data to display."
        
        return context

class PurchaseVATRegisterTableView(TemplateView):
    template_name = 'accounts/purchase_vat_register/_purchase_vat_register_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        form = PurchaseVATReportForm(self.request.GET) # Bind GET parameters to form
        
        report_results = {
            'report_data': [],
            'vat_gross_total': 0.0,
            'cst_gross_total': 0.0,
            'total_excise': 0.0,
            'mah_purchase': "No data to display for selected period."
        }

        if form.is_valid():
            from_date = form.cleaned_data['from_date']
            to_date = form.cleaned_data['to_date']
            
            # Placeholder for Company ID and Financial Year ID from session/user profile
            # In a real app, this would come from request.user or a pre-configured setting.
            # For this example, let's use a hardcoded value (similar to ASP.NET's CompId = 1).
            company_id = 1 # Example: int(self.request.session.get('compid', 1))
            
            try:
                report_results = BillBookingMaster.purchase_vat_reports.get_purchase_vat_register_report(
                    company_id, from_date, to_date
                )
            except Exception as e:
                messages.error(self.request, f"Error generating report: {e}")
                # Log the error for debugging
                print(f"Error generating report: {e}")
        else:
            messages.warning(self.request, "Please provide valid date range.")
            # If form is not valid, report_results remain empty as initialized above

        context.update(report_results)
        return context

```

#### 4.4 Templates (`accounts/templates/accounts/purchase_vat_register/`)

**Business Value:** Modern, modular templates ensure a consistent and responsive user interface. By using HTMX and Alpine.js, we eliminate full page reloads, providing a smooth, app-like experience for users and reducing server load. DataTables enhance user interaction with large datasets.

**Task:** Create templates for the report view and its HTMX-loaded table partial.

**Instructions:**
- `report.html`: The main page for the Purchase VAT Register. It will contain the date input form and a container for the report table. HTMX will be used to load the table dynamically when dates are selected or when the page loads.
- `_purchase_vat_register_table.html`: A partial template containing only the DataTables table and related JavaScript. This is loaded via HTMX into the `report.html`.

```html
<!-- accounts/templates/accounts/purchase_vat_register/report.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-3xl font-extrabold text-gray-900 mb-6 border-b pb-4">Purchase VAT Register Report</h2>

    <div class="bg-white p-6 rounded-lg shadow-md mb-8">
        <h3 class="text-xl font-semibold text-gray-800 mb-4">Select Report Period</h3>
        <form hx-get="{% url 'purchase_vat_register_table' %}" 
              hx-target="#report-table-container"
              hx-swap="innerHTML"
              hx-indicator="#report-loading-indicator"
              _="on submit log 'Form submitted' then wait 200ms then call $('#report-table').DataTable().destroy() then trigger reportTableReload">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                    <label for="{{ form.from_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.from_date.label }}
                    </label>
                    {{ form.from_date }}
                    {% if form.from_date.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.from_date.errors }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.to_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.to_date.label }}
                    </label>
                    {{ form.to_date }}
                    {% if form.to_date.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.to_date.errors }}</p>
                    {% endif %}
                </div>
            </div>
            {% if form.non_field_errors %}
                <div class="text-red-500 text-sm mb-4">{{ form.non_field_errors }}</div>
            {% endif %}
            <div class="flex justify-end space-x-4">
                <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Generate Report
                </button>
                 <a href="{% url 'home' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm">
                    Cancel
                </a>
            </div>
        </form>
    </div>

    <!-- Loading Indicator -->
    <div id="report-loading-indicator" class="htmx-indicator text-center py-4">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">Generating report...</p>
    </div>

    <!-- Report Table Container -->
    <div id="report-table-container" 
         hx-trigger="load, reportTableReload from:body"
         hx-get="{% url 'purchase_vat_register_table' %}{% if form.is_valid %}?from_date={{ form.cleaned_data.from_date|date:'Y-m-d' }}&to_date={{ form.cleaned_data.to_date|date:'Y-m-d' }}{% endif %}"
         hx-swap="innerHTML">
        <!-- The DataTables content will be loaded here via HTMX -->
        <div class="text-center py-8 text-gray-500">
            <p>Select a date range and click "Generate Report" to view data.</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
    });

    // Handle DataTables destruction before new content is loaded by HTMX
    // This is important if you reload the table container with new content.
    // The htmx.onLoad event fires after content is swapped.
    // But DataTables needs to be destroyed *before* the swap if reloading the whole table.
    // The _="on submit ... then call $('#report-table').DataTable().destroy()" is an attempt.
    // A more robust solution might be to target *only* the tbody, or use HTMX's hx-on::after.swap
    // with a specific ID for the table if it exists.
    htmx.onLoad(function(elt) {
        // This fires after new content is loaded by HTMX.
        // It's safer to initialize DataTable here if the table element is new or re-added.
        var table = $('#purchaseVATRegisterTable');
        if (table.length && !$.fn.DataTable.isDataTable(table)) {
            table.DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "dom": 'lfrtipB', // Add B for buttons
                "buttons": [
                    'copy', 'excel', 'pdf', 'print'
                ]
            });
        }
    });

</script>
{% endblock %}

```

```html
<!-- accounts/templates/accounts/purchase_vat_register/_purchase_vat_register_table.html -->
<div class="bg-white p-6 rounded-lg shadow-md overflow-x-auto">
    {% if report_data %}
        <table id="purchaseVATRegisterTable" class="min-w-full divide-y divide-gray-200 border border-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                    <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sys Date</th>
                    <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
                    <th scope="col" class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Basic Amt</th>
                    <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PF Terms</th>
                    <th scope="col" class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">PF Amt</th>
                    <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Excise Values</th>
                    <th scope="col" class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Excise Amt</th>
                    <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">EDU Cess</th>
                    <th scope="col" class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">EDU Value</th>
                    <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SHE Cess</th>
                    <th scope="col" class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">SHE Value</th>
                    <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">VAT/CST Terms</th>
                    <th scope="col" class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">VAT/CST Amt</th>
                    <th scope="col" class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Freight Amt</th>
                    <th scope="col" class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amt</th>
                    <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Excise Basic</th>
                    <th scope="col" class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Ex Basic Amt</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for row in report_data %}
                <tr>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ row.sys_date }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ row.supplier_name }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.basic_amt|floatformat:2 }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ row.pf_terms }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.pf_amt|floatformat:2 }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ row.excise_values }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.excise_amt|floatformat:2 }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ row.edu_cess }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.edu_value|floatformat:2 }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ row.she_cess }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.she_value|floatformat:2 }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ row.vat_cst_terms }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.vat_cst_amt|floatformat:2 }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.freight_amt|floatformat:2 }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.total_amt|floatformat:2 }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ row.excise_basic_term }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.ex_basic_amt|floatformat:2 }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <div class="mt-6 p-4 bg-gray-100 rounded-lg text-gray-800">
            <h4 class="text-lg font-semibold mb-2">Report Summary:</h4>
            <p><strong>VAT Gross Total:</strong> {{ vat_gross_total|floatformat:2 }}</p>
            <p><strong>CST Gross Total:</strong> {{ cst_gross_total|floatformat:2 }}</p>
            <p><strong>Total Excise:</strong> {{ total_excise|floatformat:2 }}</p>
            <p><strong>MAH Purchase:</strong> {{ mah_purchase }}</p>
        </div>
    {% else %}
        <div class="text-center py-8 text-gray-500">
            <p>No data found for the selected period.</p>
        </div>
    {% endif %}
</div>

<!-- JavaScript to initialize DataTables. This block will run after HTMX loads the content. -->
<script>
    // DataTables initialization
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists to prevent reinitialization errors
        if ($.fn.DataTable.isDataTable('#purchaseVATRegisterTable')) {
            $('#purchaseVATRegisterTable').DataTable().destroy();
        }
        $('#purchaseVATRegisterTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "searching": true,
            "ordering": true,
            "paging": true,
            "info": true,
            "dom": 'lfrtipB', // Added 'B' for buttons
            "buttons": [ // DataTables Buttons for export functionality
                'copyHtml5',
                'excelHtml5',
                'csvHtml5',
                'pdfHtml5',
                'print'
            ]
        });
    });
</script>
```

#### 4.5 URLs (`accounts/urls.py`)

**Business Value:** Clearly defined URL patterns make your application's navigation intuitive and maintainable. Separating URLs per app promotes modularity and scalability, enabling easier expansion and integration of new features.

**Task:** Define URL patterns for the report view and its HTMX partial.

**Instructions:**
- Define a URL for the main `PurchaseVATRegisterReportView`.
- Define a separate URL for `PurchaseVATRegisterTableView` which is targeted by HTMX.

```python
# accounts/urls.py
from django.urls import path
from .views import PurchaseVATRegisterReportView, PurchaseVATRegisterTableView

urlpatterns = [
    path('purchase-vat-register/', PurchaseVATRegisterReportView.as_view(), name='purchase_vat_register_report'),
    # This URL is specifically for HTMX requests to load the table content dynamically
    path('purchase-vat-register/table/', PurchaseVATRegisterTableView.as_view(), name='purchase_vat_register_table'),
]
```
Remember to include these URLs in your project's main `urls.py`:
`path('accounts/', include('accounts.urls')),`

#### 4.6 Tests (`accounts/tests.py`)

**Business Value:** Comprehensive automated tests are paramount for ensuring the reliability and correctness of your migrated application. They act as a safety net, catching regressions and validating that all business logic, especially complex calculations in models, performs as expected. Achieving high test coverage (80%+) provides confidence in the stability of your new Django system.

**Task:** Write unit tests for the `PurchaseVATReportManager` and integration tests for the `PurchaseVATRegisterReportView` and `PurchaseVATRegisterTableView`.

**Instructions:**
- Set up test data for all relevant models in `setUpTestData`.
- Test the `get_purchase_vat_register_report` method in the manager for correct calculations and data structure.
- Test that the main report view loads correctly.
- Test that the HTMX table view loads correctly with and without valid date parameters.
- Verify HTMX headers in responses if needed.

```python
# accounts/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date, timedelta
from django.utils import timezone
from decimal import Decimal

# Import all models used in the report logic
from .models import (
    Supplier, Packing, Excise, VAT, POMaster, MaterialQualityDetail,
    BillBookingMaster, BillBookingDetail, PurchaseVATReportManager
)

class PurchaseVATReportModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for the report manager
        cls.company_id = 101
        cls.from_date = date(2023, 1, 1)
        cls.to_date = date(2023, 1, 31)

        # Create related data
        cls.supplier1 = Supplier.objects.create(supplier_id=1, comp_id=cls.company_id, supplier_name="Supplier A")
        cls.supplier2 = Supplier.objects.create(supplier_id=2, comp_id=cls.company_id, supplier_name="Supplier B")

        cls.packing1 = Packing.objects.create(id=1, value="PF 10%")
        cls.packing2 = Packing.objects.create(id=2, value="PF 5%")

        cls.excise1 = Excise.objects.create(id=1, value="Excise 12%", accessable_value="Basic Value", edu_cess="2%", she_cess="1%")
        cls.excise2 = Excise.objects.create(id=2, value="Excise 10%", accessable_value="Total Value", edu_cess="3%", she_cess="1.5%")

        cls.vat1 = VAT.objects.create(id=1, value="VAT 5%", is_vat=True, is_cst=False)
        cls.vat2 = VAT.objects.create(id=2, value="CST 2%", is_vat=False, is_cst=True)
        cls.vat3 = VAT.objects.create(id=3, value="No Tax", is_vat=False, is_cst=False) # Case for IsVAT=0 and IsCST=0

        cls.po_master1 = POMaster.objects.create(id=101, pf_id=cls.packing1, ex_st_id=cls.excise1, vat_id=cls.vat1, rate=Decimal('100.00'), discount=Decimal('10.00'))
        cls.po_master2 = POMaster.objects.create(id=102, pf_id=cls.packing2, ex_st_id=cls.excise2, vat_id=cls.vat2, rate=Decimal('200.00'), discount=Decimal('5.00'))
        cls.po_master3 = POMaster.objects.create(id=103, pf_id=cls.packing1, ex_st_id=cls.excise1, vat_id=cls.vat3, rate=Decimal('50.00'), discount=Decimal('0.00')) # No tax case

        cls.material_qty1 = MaterialQualityDetail.objects.create(id=201, accepted_qty=Decimal('5.00'))
        cls.material_qty2 = MaterialQualityDetail.objects.create(id=202, accepted_qty=Decimal('10.00'))
        cls.material_qty3 = MaterialQualityDetail.objects.create(id=203, accepted_qty=Decimal('3.00'))

        # Create BillBookingMaster records
        cls.bb_master1 = BillBookingMaster.objects.create(id=301, sys_date=timezone.make_aware(timezone.datetime(2023, 1, 15, 10, 0, 0)), supplier=cls.supplier1, comp_id=cls.company_id)
        cls.bb_master2 = BillBookingMaster.objects.create(id=302, sys_date=timezone.make_aware(timezone.datetime(2023, 1, 20, 11, 0, 0)), supplier=cls.supplier2, comp_id=cls.company_id)
        cls.bb_master3 = BillBookingMaster.objects.create(id=303, sys_date=timezone.make_aware(timezone.datetime(2023, 2, 1, 9, 0, 0)), supplier=cls.supplier1, comp_id=cls.company_id) # Outside date range

        # Create BillBookingDetail records
        # Row 1 (VAT)
        BillBookingDetail.objects.create(
            id=401, master=cls.bb_master1, gqn_id=cls.material_qty1, pod_id=cls.po_master1,
            pf_amt=Decimal('5.00'), ex_st_basic=Decimal('10.00'), ex_st_educess=Decimal('0.20'), ex_st_shecess=Decimal('0.10'),
            vat=Decimal('45.00'), cst=Decimal('0.00'), freight=Decimal('2.00')
        )
        # Row 2 (CST)
        BillBookingDetail.objects.create(
            id=402, master=cls.bb_master2, gqn_id=cls.material_qty2, pod_id=cls.po_master2,
            pf_amt=Decimal('10.00'), ex_st_basic=Decimal('20.00'), ex_st_educess=Decimal('0.60'), ex_st_shecess=Decimal('0.30'),
            vat=Decimal('0.00'), cst=Decimal('30.00'), freight=Decimal('5.00')
        )
        # Row 3 (No Tax, uses VAT field as per C# logic)
        BillBookingDetail.objects.create(
            id=403, master=cls.bb_master1, gqn_id=cls.material_qty3, pod_id=cls.po_master3,
            pf_amt=Decimal('2.00'), ex_st_basic=Decimal('5.00'), ex_st_educess=Decimal('0.10'), ex_st_shecess=Decimal('0.05'),
            vat=Decimal('0.00'), cst=Decimal('0.00'), freight=Decimal('1.00')
        )


    def test_report_generation_logic(self):
        # Calculate expected values based on setup data and C# logic
        # Row 1: qty=5, rate=100, discount=10 => amt = 5 * (100 - 100*0.1) = 5 * 90 = 450
        # pf_amt=5, ex_st_basic=10, edu=0.2, she=0.1, vat=45, cst=0, fr=2
        # ExciseAmt = 10 + 0.2 + 0.1 = 10.3
        # VatCst = 45 (because is_vat is true)
        # Total = 450 + 5 + 10.3 + 45 + 2 = 512.3

        # Row 2: qty=10, rate=200, discount=5 => amt = 10 * (200 - 200*0.05) = 10 * 190 = 1900
        # pf_amt=10, ex_st_basic=20, edu=0.6, she=0.3, vat=0, cst=30, fr=5
        # ExciseAmt = 20 + 0.6 + 0.3 = 20.9
        # VatCst = 30 (because is_cst is true)
        # Total = 1900 + 10 + 20.9 + 30 + 5 = 1965.9

        # Row 3: qty=3, rate=50, discount=0 => amt = 3 * (50 - 0) = 150
        # pf_amt=2, ex_st_basic=5, edu=0.1, she=0.05, vat=0, cst=0, fr=1
        # ExciseAmt = 5 + 0.1 + 0.05 = 5.15
        # VatCst = 0 (because is_vat=0, is_cst=0, and vat field is 0)
        # Total = 150 + 2 + 5.15 + 0 + 1 = 158.15

        report_data = BillBookingMaster.purchase_vat_reports.get_purchase_vat_register_report(
            self.company_id, self.from_date, self.to_date
        )

        self.assertIsInstance(report_data, dict)
        self.assertIn('report_data', report_data)
        self.assertIn('vat_gross_total', report_data)
        self.assertIn('cst_gross_total', report_data)
        self.assertIn('total_excise', report_data)
        self.assertIn('mah_purchase', report_data)

        self.assertEqual(len(report_data['report_data']), 3) # Two rows from 2023-01-15, one from 2023-01-20

        # Verify totals
        expected_vat_gross_total = round(Decimal('512.30') + Decimal('158.15'), 2) # Row 1 (VAT) + Row 3 (No Tax, treated as VAT)
        expected_cst_gross_total = round(Decimal('1965.90'), 2) # Row 2 (CST)
        expected_total_excise = round(Decimal('10.30') + Decimal('20.90') + Decimal('5.15'), 2)

        self.assertEqual(report_data['vat_gross_total'], float(expected_vat_gross_total))
        self.assertEqual(report_data['cst_gross_total'], float(expected_cst_gross_total))
        self.assertEqual(report_data['total_excise'], float(expected_total_excise))

        # Verify MAHPurchase string content (order might vary based on dict iteration)
        mah_purchase_parts = report_data['mah_purchase'].split(',  ')
        self.assertIn(f"VAT 5%    Amt: {Decimal('45.00'):.2f}", mah_purchase_parts)
        self.assertIn(f"CST 2%    Amt: {Decimal('30.00'):.2f}", mah_purchase_parts)
        self.assertIn(f"No Tax    Amt: {Decimal('0.00'):.2f}", mah_purchase_parts)


class PurchaseVATReportViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal data for views to render
        cls.company_id = 101
        cls.supplier = Supplier.objects.create(supplier_id=1, comp_id=cls.company_id, supplier_name="Test Supplier")
        cls.packing = Packing.objects.create(id=1, value="PF 10%")
        cls.excise = Excise.objects.create(id=1, value="Excise 12%", accessable_value="Basic Value", edu_cess="2%", she_cess="1%")
        cls.vat = VAT.objects.create(id=1, value="VAT 5%", is_vat=True, is_cst=False)
        cls.po_master = POMaster.objects.create(id=1, pf_id=cls.packing, ex_st_id=cls.excise, vat_id=cls.vat, rate=Decimal('100.00'), discount=Decimal('0.00'))
        cls.material_qty = MaterialQualityDetail.objects.create(id=1, accepted_qty=Decimal('10.00'))
        
        cls.bb_master = BillBookingMaster.objects.create(
            id=1, sys_date=timezone.now(), supplier=cls.supplier, comp_id=cls.company_id
        )
        BillBookingDetail.objects.create(
            id=1, master=cls.bb_master, gqn_id=cls.material_qty, pod_id=cls.po_master,
            pf_amt=Decimal('0.00'), ex_st_basic=Decimal('0.00'), ex_st_educess=Decimal('0.00'), ex_st_shecess=Decimal('0.00'),
            vat=Decimal('0.00'), cst=Decimal('0.00'), freight=Decimal('0.00')
        )

    def setUp(self):
        self.client = Client()
        self.report_url = reverse('purchase_vat_register_report')
        self.table_url = reverse('purchase_vat_register_table')

    def test_report_view_get(self):
        response = self.client.get(self.report_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/purchase_vat_register/report.html')
        self.assertIn('form', response.context)
        # Initial table container should be present, but table content should be empty/loading
        self.assertContains(response, '<div id="report-table-container"')
        self.assertContains(response, 'Select a date range and click "Generate Report" to view data.')

    def test_table_partial_view_get_no_params(self):
        # Simulating HTMX load without initial parameters (first load)
        response = self.client.get(self.table_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/purchase_vat_register/_purchase_vat_register_table.html')
        self.assertContains(response, 'No data found for the selected period.')

    def test_table_partial_view_get_with_valid_params(self):
        today = date.today()
        # Ensure our test data falls within the date range
        BillBookingMaster.objects.filter(id=self.bb_master.id).update(sys_date=timezone.make_aware(timezone.datetime(today.year, today.month, today.day, 12, 0, 0)))

        query_params = {
            'from_date': today.strftime('%Y-%m-%d'),
            'to_date': (today + timedelta(days=1)).strftime('%Y-%m-%d')
        }
        response = self.client.get(self.table_url, query_params)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/purchase_vat_register/_purchase_vat_register_table.html')
        # Check if table content is present (implies data was generated)
        self.assertContains(response, '<table id="purchaseVATRegisterTable"')
        self.assertContains(response, self.supplier.supplier_name) # Check for supplier name in table

    def test_table_partial_view_get_with_invalid_params(self):
        query_params = {
            'from_date': '2023-01-31',
            'to_date': '2023-01-01' # Invalid range
        }
        response = self.client.get(self.table_url, query_params)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/purchase_vat_register/_purchase_vat_register_table.html')
        self.assertContains(response, 'No data found for the selected period.')
        # Check for message indicating form validation error if your view explicitly adds it
        # (Current view design doesn't display form errors for the partial, only an empty table)
        # You might need to check messages in self.client.session.get('_messages') if using messages framework.

    def test_report_form_validation(self):
        form_data = {
            'from_date': '2023-01-31',
            'to_date': '2023-01-01' # Invalid range
        }
        form = PurchaseVATReportForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('__all__', form.errors)
        self.assertIn('From Date cannot be after To Date.', form.errors['__all__'])

```

### Step 5: HTMX and Alpine.js Integration

**Business Value:** This integration creates a highly interactive and responsive user interface, similar to a single-page application (SPA), but with significantly reduced complexity. Users experience instant updates without full page reloads, leading to a smoother and more enjoyable workflow.

**Instructions:**
- **Initial Load:** The `report.html` page uses `hx-get` on `report-table-container` with `hx-trigger="load"` to automatically fetch the initial report table via HTMX when the page loads. It passes the initial form values as GET parameters.
- **Form Submission:** The date selection form uses `hx-get` to trigger a new request to `{% url 'purchase_vat_register_table' %}` when submitted. `hx-target="#report-table-container"` ensures that only the table area is updated. `hx-swap="innerHTML"` replaces the entire content of the container.
- **Loading Indicator:** `hx-indicator="#report-loading-indicator"` displays a spinner while the report data is being fetched.
- **DataTables Reinitialization:** A small `script` block within the `_purchase_vat_register_table.html` partial ensures that DataTables is correctly initialized each time the table content is swapped in by HTMX. This includes destroying any previous DataTable instance to prevent errors.
- **Cancel Button:** A simple `<a>` tag or `button` to navigate back to a home or previous report page, as specified in `btnCancel_Click`.
- **Alpine.js:** While not heavily used for this specific report page, Alpine.js remains available in `base.html` for any minor client-side state management or UI toggles (e.g., modal handling if "Add New Report" button were present). For this report, its primary role is to manage UI states not directly handled by HTMX's swaps (e.g., dynamic form elements, if they were present).

## Final Notes

This modernization plan provides a clear, actionable roadmap for transitioning your ASP.NET Purchase VAT Register to Django. By following these structured steps, you can achieve a modern, maintainable, and high-performance application with significant business benefits:

-   **Improved Performance & User Experience:** Leveraging Django's efficient backend with HTMX and DataTables for a responsive, interactive front end, eliminating full-page reloads.
-   **Enhanced Maintainability:** "Fat Models, Thin Views" centralizes business logic, making code easier to understand, debug, and extend.
-   **Increased Scalability:** Django's architecture is built for scale, allowing your application to grow with your business needs.
-   **Reduced Technical Debt:** Migrating from legacy ASP.NET to modern Django significantly reduces technical debt, improving developer productivity and reducing long-term costs.
-   **Robustness through Testing:** Comprehensive unit and integration tests ensure the reliability and correctness of the migrated functionality, minimizing errors.
-   **Future-Proofing:** Adopting modern Django 5.0+ and best practices ensures your application remains relevant and adaptable to future technological advancements.

This detailed breakdown can be directly fed into AI-assisted automation tools to generate the bulk of the code, with human oversight for fine-tuning and validation, drastically accelerating your migration process.