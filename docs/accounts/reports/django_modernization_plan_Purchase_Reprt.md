The ASP.NET application you've provided is a classic example of a reporting module, where users select date ranges and generate various purchase reports (Excise, VAT/CST, VAT/CST Labour) using Crystal Reports. The application relies heavily on server-side rendering, postbacks, and proprietary components like `AjaxControlToolkit` and `CrystalDecisions.Web`.

Our modernization plan will transition this to a highly efficient Django application, leveraging modern patterns like "Fat Models, Thin Views," HTMX for dynamic interactions, Alpine.js for lightweight UI state management, and DataTables for interactive data presentation. We will entirely replace Crystal Reports with a direct data display approach, which is more web-native and performant.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code, particularly from the SQL queries in the C# code-behind.

**Analysis:**
The C# code heavily relies on SQL queries joining multiple tables and performing aggregations to generate report data. The report's output structure (defined by the `DataTable` columns `dt` and `dt2`) is crucial.

**Identified Tables (from SQL joins and `fun.select` lookups):**
*   **`tblACC_BillBooking_Master`**: `Id`, `SysDate`, `SupplierId`, `CompId`
*   **`tblACC_BillBooking_Details`**: `Id`, `MId` (Master ID), `PODId` (PO Detail ID), `GQNId` (Material Quality Note ID), `GSNId` (Material Service Note ID), `PFAmt`, `ExStBasic`, `ExStEducess`, `ExStShecess`, `VAT`, `CST`, `Freight`
*   **`tblQc_MaterialQuality_Details`**: `Id`, `AcceptedQty`
*   **`tblinv_MaterialServiceNote_Details`**: `Id`, `ReceivedQty`
*   **`tblMM_PO_Details`**: `Id`, `Rate`, `Discount`, `PF` (Packing Master ID), `ExST` (Excise Master ID), `VAT` (VAT Master ID)
*   **`tblMM_Supplier_master`**: `SupplierId`, `SupplierName`
*   **`tblPacking_Master`**: `Id`, `Value`
*   **`tblExciseser_Master`**: `Id`, `Value`, `AccessableValue`, `EDUCess`, `SHECess`
*   **`tblVAT_Master`**: `Id`, `Value`, `IsVAT`, `IsCST`

**Inferred Report Output Structure (from `dt`/`dt2` DataTables):**
This will be the structure of data generated by our Django models/manager.
*   `SysDate` (Date)
*   `CompId` (int)
*   `SupplierName` (string)
*   `BasicAmt` (double)
*   `PFTerms` (string)
*   `PF` (double)
*   `ExciseValues` (string)
*   `ExciseAmt` (double)
*   `EDUCess` (string)
*   `EDUValue` (double)
*   `SHECess` (string)
*   `SHEValue` (double)
*   `VATCSTTerms` (string)
*   `VATCSTAmt` (double)
*   `FreightAmt` (double)
*   `TotAmt` (double)
*   `ExciseBasic` (string)
*   `ExBasicAmt` (double)

### Step 2: Identify Backend Functionality

**Task:** Determine the operations and business logic from the C# code-behind.

**Functionality Analysis:**
*   **Report Generation**: The primary function is to generate complex purchase reports. This is not a CRUD operation on a single entity, but rather a sophisticated data aggregation and presentation task.
*   **Data Retrieval**: Involves joining multiple transactional and lookup tables based on `CompId` and `SysDate` range.
*   **Conditional Queries**: Two main types of reports are generated based on `flag` (0 for VAT/CST and VAT/CST Labour, 1 for Excise, though the C# code seems to have reversed the meaning of `flag` based on `cryrpt_create` calls, and `cryrpt_create2` handles Excise. We will interpret this as 'material quality based' for excise and 'material service based' for VAT/CST).
*   **Lookup Translations**: Numeric IDs for PF, ExST, VAT are translated to descriptive `Value` strings using helper functions (`fun.select`).
*   **Complex Calculations**: `amt`, `ExciseAmt`, `TotAmt`, `VATCst` are calculated based on raw data.
*   **Data Segmentation**: Results are split into VAT (`dt`) and CST (`dt2`) specific data.
*   **Summary Aggregations**: `VATGrossTotal`, `CSTGrossTotal`, `TotalExcise`, and `MAHPurchase` (grouped summary) are calculated.
*   **Date Initialization**: Default date ranges (first/last day of month) are set on page load.
*   **Session Management (Legacy)**: Crystal Reports are stored in `Session`. This will be replaced by on-demand data fetching in Django.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, and how they map to Django UI.

**UI Component Mapping:**
*   **`cc1:TabContainer` (Tabs)**: Replaced by Alpine.js for tab state management and HTMX for dynamic content loading.
*   **`asp:TextBox` + `cc1:CalendarExtender` (Date Inputs)**: Replaced by Django `forms.DateField` with HTML5 `type="date"` and `Flatpickr` for an enhanced calendar UI, integrated with HTMX for dynamic loading.
*   **`asp:Button` (Search Buttons)**: Replaced by standard HTML `<button type="submit">` with HTMX attributes to trigger data fetches.
*   **`asp:RequiredFieldValidator`, `asp:RegularExpressionValidator`, `asp:CompareValidator` (Validation)**: Handled by Django forms' built-in validation and custom `clean` methods. Errors displayed inline.
*   **`CR:CrystalReportViewer` (Report Viewer)**: Replaced by an HTML `<table>` structured for DataTables, populated with data from the Django backend via HTMX. The "reports" are now interactive data grids.
*   **`asp:Label` (Error/Info Messages)**: Replaced by Django's messages framework, displayed as Tailwind CSS-styled alerts.

### Step 4: Generate Django Code

We'll create a new Django app named `purchasereports`.

#### 4.1 Models (`purchasereports/models.py`)

The complex SQL queries and data transformations will be encapsulated within a custom manager method on a "conceptual" `PurchaseReport` model. This adheres to the "fat model" principle by moving business logic out of views. We will define placeholder models for the underlying tables, ensuring `managed=False` as they are legacy database tables.

```python
from django.db import models
from django.db.models import Sum, F, ExpressionWrapper, DecimalField
from django.utils import timezone
from datetime import datetime

# --- Lookup Tables (Mapped from Legacy DB) ---
class PackingMaster(models.Model):
    """
    Corresponds to tblPacking_Master, used for 'PF' lookup.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    value = models.CharField(db_column='Value', max_length=255, blank=True, null=True)

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblPacking_Master'
        verbose_name = 'Packing Master'
        verbose_name_plural = 'Packing Masters'

    def __str__(self):
        return self.value or f"Packing {self.id}"

class ExciseMaster(models.Model):
    """
    Corresponds to tblExciseser_Master, used for 'ExST' lookup.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    value = models.CharField(db_column='Value', max_length=255, blank=True, null=True)
    accessable_value = models.CharField(db_column='AccessableValue', max_length=255, blank=True, null=True)
    educess = models.CharField(db_column='EDUCess', max_length=255, blank=True, null=True)
    shecess = models.CharField(db_column='SHECess', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblExciseser_Master'
        verbose_name = 'Excise Master'
        verbose_name_plural = 'Excise Masters'

    def __str__(self):
        return self.value or f"Excise {self.id}"

class VatMaster(models.Model):
    """
    Corresponds to tblVAT_Master, used for 'VAT' lookup.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    value = models.CharField(db_column='Value', max_length=255, blank=True, null=True)
    is_vat = models.BooleanField(db_column='IsVAT', default=False)
    is_cst = models.BooleanField(db_column='IsCST', default=False)

    class Meta:
        managed = False
        db_table = 'tblVAT_Master'
        verbose_name = 'VAT Master'
        verbose_name_plural = 'VAT Masters'

    def __str__(self):
        return self.value or f"VAT {self.id}"

# --- Transactional Models (Placeholder, only relevant fields for report query) ---
class SupplierMaster(models.Model):
    """
    Corresponds to tblMM_Supplier_master.
    """
    supplier_id = models.IntegerField(db_column='SupplierId', primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier Master'
        verbose_name_plural = 'Supplier Masters'

    def __str__(self):
        return self.supplier_name or f"Supplier {self.supplier_id}"

class BillBookingMaster(models.Model):
    """
    Corresponds to tblACC_BillBooking_Master.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate')
    supplier = models.ForeignKey(SupplierMaster, on_delete=models.DO_NOTHING, db_column='SupplierId', related_name='bill_bookings')
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblACC_BillBooking_Master'
        verbose_name = 'Bill Booking Master'
        verbose_name_plural = 'Bill Booking Masters'

class BillBookingDetail(models.Model):
    """
    Corresponds to tblACC_BillBooking_Details.
    Note: Foreign keys for GQNId and GSNId are implicit based on query joins.
          We'll map them as IntegerFields and rely on custom logic in the manager.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(BillBookingMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    pod_id = models.IntegerField(db_column='PODId')
    gqn_id = models.IntegerField(db_column='GQNId', null=True, blank=True)
    gsn_id = models.IntegerField(db_column='GSNId', null=True, blank=True)
    pf_amt = models.DecimalField(db_column='PFAmt', max_digits=18, decimal_places=2, default=0.0)
    ex_st_basic = models.DecimalField(db_column='ExStBasic', max_digits=18, decimal_places=2, default=0.0)
    ex_st_educess = models.DecimalField(db_column='ExStEducess', max_digits=18, decimal_places=2, default=0.0)
    ex_st_shecess = models.DecimalField(db_column='ExStShecess', max_digits=18, decimal_places=2, default=0.0)
    vat = models.DecimalField(db_column='VAT', max_digits=18, decimal_places=2, default=0.0)
    cst = models.DecimalField(db_column='CST', max_digits=18, decimal_places=2, default=0.0)
    freight = models.DecimalField(db_column='Freight', max_digits=18, decimal_places=2, default=0.0)

    class Meta:
        managed = False
        db_table = 'tblACC_BillBooking_Details'
        verbose_name = 'Bill Booking Detail'
        verbose_name_plural = 'Bill Booking Details'

class PoDetail(models.Model):
    """
    Corresponds to tblMM_PO_Details.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=2, default=0.0)
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=2, default=0.0)
    pf = models.ForeignKey(PackingMaster, on_delete=models.DO_NOTHING, db_column='PF', related_name='po_details_pf')
    ex_st = models.ForeignKey(ExciseMaster, on_delete=models.DO_NOTHING, db_column='ExST', related_name='po_details_exst')
    vat = models.ForeignKey(VatMaster, on_delete=models.DO_NOTHING, db_column='VAT', related_name='po_details_vat')

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Details'
        verbose_name = 'PO Detail'
        verbose_name_plural = 'PO Details'

class MaterialQualityDetail(models.Model):
    """
    Corresponds to tblQc_MaterialQuality_Details, used for Excise report qty.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    accepted_qty = models.DecimalField(db_column='AcceptedQty', max_digits=18, decimal_places=2, default=0.0)

    class Meta:
        managed = False
        db_table = 'tblQc_MaterialQuality_Details'
        verbose_name = 'Material Quality Detail'
        verbose_name_plural = 'Material Quality Details'

class MaterialServiceNoteDetail(models.Model):
    """
    Corresponds to tblinv_MaterialServiceNote_Details, used for VAT/CST report qty.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    received_qty = models.DecimalField(db_column='ReceivedQty', max_digits=18, decimal_places=2, default=0.0)

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialServiceNote_Details'
        verbose_name = 'Material Service Note Detail'
        verbose_name_plural = 'Material Service Note Details'

# --- Custom Manager for Purchase Reports (Fat Model Logic) ---
class PurchaseReportManager(models.Manager):
    def get_purchase_report_data(self, from_date, to_date, report_type, comp_id):
        """
        Generates structured report data by performing complex queries and calculations.
        Mimics the logic from the C# cryrpt_create and cryrpt_create2 methods.
        """
        results = []
        
        # Pre-fetch all necessary lookup values to avoid N+1 queries in the loop
        packing_values = {p.id: p.value for p in PackingMaster.objects.all()}
        excise_lookups = {e.id: {'value': e.value, 'accessable_value': e.accessable_value, 'educess': e.educess, 'shecess': e.shecess} for e in ExciseMaster.objects.all()}
        vat_lookups = {v.id: {'value': v.value, 'is_vat': v.is_vat, 'is_cst': v.is_cst} for v in VatMaster.objects.all()}

        # Build the base query based on report type
        if report_type == 'excise':
            # Corresponds to SQL query in cryrpt_create2 for Purchase_Excise.rpt
            # Joins tblQc_MaterialQuality_Details
            bill_details_qs = BillBookingDetail.objects.filter(
                master__sys_date__range=(from_date, to_date),
                master__comp_id=comp_id,
                gqn_id__isnull=False # Ensure it's material quality type
            ).annotate(
                # Link MaterialQualityDetail explicitly
                accepted_qty=F('materialqualitydetail__accepted_qty'),
                # Pre-calculate discounted rate based on PO details
                calc_rate=(F('pod_id__rate') - (F('pod_id__rate') * F('pod_id__discount') / 100)),
                # Calculate basic amount
                amt_calc=ExpressionWrapper(F('accepted_qty') * F('calc_rate'), output_field=DecimalField())
            ).values(
                'master__sys_date', 'master__comp_id', 'master__supplier__supplier_name', 'master__supplier__supplier_id',
                'pod_id__pf', 'pod_id__ex_st', 'pod_id__vat',
                'pf_amt', 'ex_st_basic', 'ex_st_educess', 'ex_st_shecess', 'vat', 'cst', 'freight',
                'amt_calc' # The calculated amount
            ).order_by(
                'master__sys_date', 'master__supplier__supplier_name', 'pod_id__pf', 'pod_id__ex_st', 'pod_id__vat'
            )
            # The original C# code for excise combined data from both GQNId and GSNId using dsrs.Tables[0].Merge(dsrs1.Tables[0])
            # This indicates 'excise' report needs to consider both material quality and service note details.
            # To strictly replicate this, we'd need to fetch both query sets and combine them before processing.
            # For simplicity, and as the original code's two primary SQLs map to either GQNId or GSNId,
            # we will assume 'excise' primarily refers to the GQNId query, but acknowledge the merge complexity.
            # For true 1:1, we'd run both queries and merge the results. Let's simplify to the GQNId path for now.
            # Or, given cryrpt_create2's SQL, it uses both. We'll simulate that for 'excise' report type.
            
            # First query (GQNId)
            query1 = BillBookingDetail.objects.filter(
                master__sys_date__range=(from_date, to_date),
                master__comp_id=comp_id,
                gqn_id__isnull=False
            ).select_related(
                'master__supplier', 'pod_id__pf', 'pod_id__ex_st', 'pod_id__vat', 'materialqualitydetail'
            ).annotate(
                qty_sum=Sum('materialqualitydetail__accepted_qty'),
                amt_sum=Sum(ExpressionWrapper(
                    F('materialqualitydetail__accepted_qty') * (F('pod_id__rate') - (F('pod_id__rate') * F('pod_id__discount') / 100)),
                    output_field=DecimalField()
                ))
            )
            # Second query (GSNId)
            query2 = BillBookingDetail.objects.filter(
                master__sys_date__range=(from_date, to_date),
                master__comp_id=comp_id,
                gsn_id__isnull=False
            ).select_related(
                'master__supplier', 'pod_id__pf', 'pod_id__ex_st', 'pod_id__vat', 'materialservicenotedetail'
            ).annotate(
                qty_sum=Sum('materialservicenotedetail__received_qty'),
                amt_sum=Sum(ExpressionWrapper(
                    F('materialservicenotedetail__received_qty') * (F('pod_id__rate') - (F('pod_id__rate') * F('pod_id__discount') / 100)),
                    output_field=DecimalField()
                ))
            )
            # Combine the results by grouping for final report rows
            # This part is tricky as Django ORM doesn't easily union and then group different annotations
            # We'll fetch the base details and then group and sum in Python, mirroring the C# logic of building DataTable from SqlDataReader.
            # This is closer to how the C# code manually aggregates and processes.
            
            # Fetch raw BillBookingDetail objects with pre-fetched related data
            # Use raw SQL if this becomes a performance bottleneck or too complex for ORM
            base_qs = BillBookingDetail.objects.filter(
                master__sys_date__range=(from_date, to_date),
                master__comp_id=comp_id
            ).select_related(
                'master__supplier', 
                'pod_id__pf', 
                'pod_id__ex_st', 
                'pod_id__vat',
                'materialqualitydetail',
                'materialservicenotedetail'
            )
            
        elif report_type in ['vatcst', 'vatcst_labour']:
            # Corresponds to SQL query in cryrpt_create for Purchase.rpt
            # Joins tblinv_MaterialServiceNote_Details for both VAT/CST and Labour
            base_qs = BillBookingDetail.objects.filter(
                master__sys_date__range=(from_date, to_date),
                master__comp_id=comp_id,
                gsn_id__isnull=False # Ensure it's material service note type
            ).select_related(
                'master__supplier', 
                'pod_id__pf', 
                'pod_id__ex_st', 
                'pod_id__vat',
                'materialservicenotedetail'
            )
        else:
            return {'vat_data': [], 'cst_data': [], 'vat_gross_total': 0, 'cst_gross_total': 0, 'total_excise': 0, 'mah_purchase': ''}

        # Process the fetched data to mimic the C# DataTable structure and calculations
        # This loop aggregates data based on the grouping keys from the original SQL
        # Grouping keys: SysDate, SupplierId, PF, ExST, VAT
        grouped_data = {}

        for detail in base_qs:
            # Determine quantity and calculated amount based on which detail type is present
            qty = Decimal('0.00')
            amt = Decimal('0.00')
            if detail.gqn_id and hasattr(detail, 'materialqualitydetail'):
                qty = detail.materialqualitydetail.accepted_qty
            elif detail.gsn_id and hasattr(detail, 'materialservicenotedetail'):
                qty = detail.materialservicenotedetail.received_qty
            
            if qty is not None:
                discounted_rate = detail.pod_id.rate - (detail.pod_id.rate * detail.pod_id.discount / 100)
                amt = qty * discounted_rate

            # Grouping key matching SQL query (SysDate, SupplierId, PF, ExST, VAT)
            key = (
                detail.master.sys_date,
                detail.master.supplier.supplier_id,
                detail.pod_id.pf_id,
                detail.pod_id.ex_st_id,
                detail.pod_id.vat_id
            )
            
            if key not in grouped_data:
                grouped_data[key] = {
                    'SysDate': detail.master.sys_date,
                    'CompId': detail.master.comp_id,
                    'Supplier': detail.master.supplier,
                    'PFId': detail.pod_id.pf_id,
                    'ExSTId': detail.pod_id.ex_st_id,
                    'VATId': detail.pod_id.vat_id,
                    'TotalAmt': Decimal('0.00'),
                    'TotalPFAmt': Decimal('0.00'),
                    'TotalExStBasic': Decimal('0.00'),
                    'TotalExStEducess': Decimal('0.00'),
                    'TotalExStShecess': Decimal('0.00'),
                    'TotalVat': Decimal('0.00'),
                    'TotalCst': Decimal('0.00'),
                    'TotalFreight': Decimal('0.00'),
                }
            
            grouped_data[key]['TotalAmt'] += amt
            grouped_data[key]['TotalPFAmt'] += detail.pf_amt
            grouped_data[key]['TotalExStBasic'] += detail.ex_st_basic
            grouped_data[key]['TotalExStEducess'] += detail.ex_st_educess
            grouped_data[key]['TotalExStShecess'] += detail.ex_st_shecess
            grouped_data[key]['TotalVat'] += detail.vat
            grouped_data[key]['TotalCst'] += detail.cst
            grouped_data[key]['TotalFreight'] += detail.freight
        
        dt_rows = []
        dt2_rows = []
        vat_gross_total = Decimal('0.00')
        cst_gross_total = Decimal('0.00')
        total_excise = Decimal('0.00')
        
        # Populate the report rows (mimicking C# DataTable structure)
        for key, row_data in grouped_data.items():
            dr = {}
            dr['SysDate'] = row_data['SysDate'].strftime('%d-%m-%Y')
            dr['CompId'] = row_data['CompId']
            dr['SupplierName'] = f"{row_data['Supplier'].supplier_name} [{row_data['Supplier'].supplier_id}]"
            dr['BasicAmt'] = float(row_data['TotalAmt'])
            
            pf_id = row_data['PFId']
            dr['PFTerms'] = packing_values.get(pf_id, '')
            dr['PF'] = float(row_data['TotalPFAmt'])

            ex_st_id = row_data['ExSTId']
            excise_info = excise_lookups.get(ex_st_id, {})
            dr['ExciseValues'] = excise_info.get('value', '')
            ex_ba = float(row_data['TotalExStBasic'])
            edu = float(row_data['TotalExStEducess'])
            she = float(row_data['TotalExStShecess'])
            dr['ExciseAmt'] = ex_ba + edu + she
            dr['EDUCess'] = excise_info.get('educess', '')
            dr['EDUValue'] = edu
            dr['SHECess'] = excise_info.get('shecess', '')
            dr['SHEValue'] = she
            
            vat_id = row_data['VATId']
            vat_info = vat_lookups.get(vat_id, {})
            dr['VATCSTTerms'] = vat_info.get('value', '')
            
            vat_cst_amount = Decimal('0.00')
            is_vat_flag = vat_info.get('is_vat', False)
            is_cst_flag = vat_info.get('is_cst', False)

            if is_vat_flag:
                vat_cst_amount = row_data['TotalVat']
            elif is_cst_flag:
                vat_cst_amount = row_data['TotalCst']
            elif not is_vat_flag and not is_cst_flag: # Defaulting to VAT if neither is true
                vat_cst_amount = row_data['TotalVat']
            
            dr['VATCSTAmt'] = float(vat_cst_amount)
            dr['FreightAmt'] = float(row_data['TotalFreight'])

            total_row_amt = row_data['TotalAmt'] + row_data['TotalPFAmt'] + Decimal(ex_ba) + Decimal(edu) + Decimal(she) + vat_cst_amount + row_data['TotalFreight']
            dr['TotAmt'] = float(total_row_amt)
            
            dr['ExciseBasic'] = excise_info.get('accessable_value', '')
            dr['ExBasicAmt'] = ex_ba # This maps to eduBasic in C# code, which was ex_st_basic

            # Accumulate totals
            total_excise += Decimal(dr['ExciseAmt']) # Sum of all exba + edu + she for total excise

            # Segregate into VAT and CST rows
            if not is_cst_flag: # If IsCST is 0 or VAT is true
                vat_gross_total += total_row_amt
                dt_rows.append(dr)
            else:
                cst_gross_total += total_row_amt
                dt2_rows.append(dr)
        
        # MAHPurchase calculation: group by VATCSTTerms,VATCSTAmt and sum VATCSTAmt
        # Only for the 'dt_rows' (non-CST) based on C# logic
        mah_purchase_groups = {}
        for row in dt_rows:
            key = (row['VATCSTTerms'], round(row['VATCSTAmt'], 2)) # Round to handle float precision
            mah_purchase_groups[key] = mah_purchase_groups.get(key, Decimal('0.00')) + Decimal(row['VATCSTAmt'])
        
        mah_purchase_str_parts = []
        for (terms, amt), total_amt in mah_purchase_groups.items():
            mah_purchase_str_parts.append(f"@ {terms}    Amt: {total_amt:.2f}")

        return {
            'vat_data': dt_rows, # Corresponds to dt in C# (non-CST items)
            'cst_data': dt2_rows, # Corresponds to dt2 in C# (CST items)
            'vat_gross_total': float(vat_gross_total),
            'cst_gross_total': float(cst_gross_total),
            'total_excise': float(total_excise),
            'mah_purchase': ",  ".join(mah_purchase_str_parts)
        }

# This "model" will not map to a database table. It serves as an interface
# to the custom manager methods that generate the report data.
class PurchaseReport(models.Model):
    objects = PurchaseReportManager()

    class Meta:
        managed = False  # This model does not correspond to a DB table
        verbose_name = 'Purchase Report'
        verbose_name_plural = 'Purchase Reports'
```

#### 4.2 Forms (`purchasereports/forms.py`)

A simple form to handle the `from_date` and `to_date` inputs and their validation.

```python
from django import forms
from django.core.exceptions import ValidationError
from datetime import date

class DateRangeForm(forms.Form):
    """
    Form for selecting a date range for reports.
    """
    from_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'type': 'date', # HTML5 date input type
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'x-init': 'flatpickr($el, { dateFormat: "d-m-Y" })' # Alpine.js integration with Flatpickr
        }),
        input_formats=['%d-%m-%Y'], # Accepts date in dd-MM-yyyy format
        label="Date From"
    )
    to_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'x-init': 'flatpickr($el, { dateFormat: "d-m-Y" })' # Alpine.js integration with Flatpickr
        }),
        input_formats=['%d-%m-%Y'],
        label="Date To"
    )

    def clean(self):
        """
        Custom validation to ensure 'Date From' is not after 'Date To'.
        Corresponds to ASP.NET CompareValidator logic.
        """
        cleaned_data = super().clean()
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')

        if from_date and to_date and from_date > to_date:
            self.add_error(None, "Invalid selected date range. 'Date From' cannot be after 'Date To'.")
        
        return cleaned_data
```

#### 4.3 Views (`purchasereports/views.py`)

We'll use a `TemplateView` for the main page and a `View` for handling HTMX requests that fetch report data for each tab. Views remain thin, delegating heavy lifting to the model manager.

```python
from django.views.generic import TemplateView, View
from django.http import HttpResponse
from django.shortcuts import render
from django.contrib import messages
from datetime import datetime, timedelta, date

from .models import PurchaseReport
from .forms import DateRangeForm

class PurchaseReportView(TemplateView):
    """
    Main view to render the purchase report page with tabbed interface.
    Initializes forms and pre-loads data for the default (Excise) tab.
    """
    template_name = 'purchasereports/purchase_report_main.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Calculate default date range (first and last day of current month)
        today = date.today()
        first_day_of_month = today.replace(day=1)
        # Handle end of month correctly for all months
        last_day_of_month = (first_day_of_month + timedelta(days=32)).replace(day=1) - timedelta(days=1)
        
        initial_data = {
            'from_date': first_day_of_month,
            'to_date': last_day_of_month,
        }
        
        # Initialize forms for each tab
        context['excise_form'] = DateRangeForm(initial=initial_data, prefix='excise')
        context['vatcst_form'] = DateRangeForm(initial=initial_data, prefix='vatcst')
        context['vatcst_labour_form'] = DateRangeForm(initial=initial_data, prefix='labour')
        
        # Mimic ASP.NET's Page_Init behavior: pre-load initial report data for Excise tab
        # Assume 'compid' is in session or a default value is used.
        # In a real app, integrate with user authentication and company selection.
        comp_id = self.request.session.get('compid', 1) 
        
        initial_excise_report_results = PurchaseReport.objects.get_purchase_report_data(
            from_date=first_day_of_month, 
            to_date=last_day_of_month, 
            report_type='excise', 
            comp_id=comp_id
        )
        # Excise report only uses 'vat_data' (our `dt` equivalent) according to C# logic
        context['excise_report_data'] = initial_excise_report_results['vat_data'] 
        context['excise_report_summary'] = { 
            'vat_gross_total': initial_excise_report_results['vat_gross_total'],
            'cst_gross_total': initial_excise_report_results['cst_gross_total'], # Will be 0 for excise as per C#
            'total_excise': initial_excise_report_results['total_excise'],
            'mah_purchase': initial_excise_report_results['mah_purchase'],
        }
        
        # Placeholder for company address, mimicking fun.CompAdd
        context['comp_address'] = "Your Company Address, City, Country"

        return context

class PurchaseReportPartialView(View):
    """
    View to handle HTMX requests for loading report data for specific tabs.
    It processes the date range form, fetches data via the model manager,
    and renders the report table partial.
    """
    def post(self, request, *args, **kwargs):
        report_type = kwargs.get('report_type') # Passed from URL: 'excise', 'vatcst', 'vatcst_labour'

        # Determine which form prefix to use based on report_type
        form_prefix = {
            'excise': 'excise',
            'vatcst': 'vatcst',
            'vatcst_labour': 'labour',
        }.get(report_type)

        if not form_prefix:
            return HttpResponse("Invalid report type", status=400)

        form = DateRangeForm(request.POST, prefix=form_prefix)

        if form.is_valid():
            from_date = form.cleaned_data['from_date']
            to_date = form.cleaned_data['to_date']
            
            # Retrieve CompId from session or use a default.
            # In a real app, this should be properly managed (e.g., from authenticated user profile).
            comp_id = request.session.get('compid', 1) 

            report_results = PurchaseReport.objects.get_purchase_report_data(
                from_date=from_date,
                to_date=to_date,
                report_type=report_type,
                comp_id=comp_id
            )
            
            context = {
                'report_type': report_type,
                'from_date': from_date.strftime('%d-%m-%Y'),
                'to_date': to_date.strftime('%d-%m-%Y'),
                'report_data': report_results['vat_data'], # Corresponds to dt (non-CST)
                'cst_report_data': report_results['cst_data'], # Corresponds to dt2 (CST)
                'vat_gross_total': report_results['vat_gross_total'],
                'cst_gross_total': report_results['cst_gross_total'],
                'total_excise': report_results['total_excise'],
                'mah_purchase': report_results['mah_purchase'],
                'comp_address': "Your Company Address, City, Country", # Mimicking fun.CompAdd
            }
            
            # Add messages if no records found
            if not report_results['vat_data'] and not report_results['cst_data']:
                messages.warning(request, "No record found for the selected criteria!")
                # Render a partial template indicating no records
                return render(request, 'purchasereports/partials/no_records.html', context)
            
            # Render the table partial with data
            return render(request, 'purchasereports/partials/purchase_report_table.html', context)
        else:
            # If form is invalid, re-render the specific form section with errors.
            # This allows HTMX to swap just the form area for error display.
            context = {
                'form': form,
                'report_type': report_type # Pass report_type to choose correct form partial
            }
            # The template for the form content (e.g., purchase_excise_form_content.html)
            # contains the form itself and the report-content div.
            # We need to re-render the *entire* form content partial including its report-content div
            # to show errors.
            # Use the correct partial name based on report_type
            partial_template_name = f'purchasereports/partials/purchase_{report_type}_form_content.html'
            return render(request, partial_template_name, context, status=400)
```

#### 4.4 Templates

Templates will be structured to support HTMX-driven partial updates. `base.html` is assumed to contain common includes like jQuery, HTMX, Alpine.js, Flatpickr, and DataTables CDNs.

**`purchasereports/templates/purchasereports/purchase_report_main.html`**
This is the main page that extends `core/base.html` and contains the tab structure.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6 text-gray-800">Purchase Report</h2>

    <div x-data="{ activeTab: 'excise' }" class="bg-white shadow rounded-lg p-6">
        <!-- Tab Headers -->
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <a href="#" 
                   @click.prevent="activeTab = 'excise'" 
                   :class="{ 'border-indigo-500 text-indigo-600': activeTab === 'excise', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'excise' }" 
                   class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Excise
                </a>
                <a href="#" 
                   @click.prevent="activeTab = 'vatcst'" 
                   :class="{ 'border-indigo-500 text-indigo-600': activeTab === 'vatcst', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'vatcst' }" 
                   class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    VAT/CST
                </a>
                <a href="#" 
                   @click.prevent="activeTab = 'vatcst_labour'" 
                   :class="{ 'border-indigo-500 text-indigo-600': activeTab === 'vatcst_labour', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'vatcst_labour' }" 
                   class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    VAT/CST (Labour)
                </a>
            </nav>
        </div>

        <!-- Tab Content Containers -->
        <div class="mt-6">
            <div x-show="activeTab === 'excise'" class="space-y-6" id="tab-content-excise">
                {% include 'purchasereports/partials/purchase_excise_form_content.html' with form=excise_form report_data=excise_report_data report_summary=excise_report_summary comp_address=comp_address %}
            </div>
            <div x-show="activeTab === 'vatcst'" class="space-y-6" id="tab-content-vatcst">
                {% include 'purchasereports/partials/purchase_vatcst_form_content.html' with form=vatcst_form %}
            </div>
            <div x-show="activeTab === 'vatcst_labour'" class="space-y-6" id="tab-content-vatcst_labour">
                {% include 'purchasereports/partials/purchase_vatcst_labour_form_content.html' with form=vatcst_labour_form %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Initializing flatpickr for the forms rendered directly on page load.
    // For forms dynamically loaded by HTMX, the partial's script block will re-initialize.
    document.addEventListener('DOMContentLoaded', function() {
        flatpickr("#excise-from_date", { dateFormat: "d-m-Y" });
        flatpickr("#excise-to_date", { dateFormat: "d-m-Y" });
        flatpickr("#vatcst-from_date", { dateFormat: "d-m-Y" });
        flatpickr("#vatcst-to_date", { dateFormat: "d-m-Y" });
        flatpickr("#labour-from_date", { dateFormat: "d-m-Y" });
        flatpickr("#labour-to_date", { dateFormat: "d-m-Y" });

        // Initial DataTables init for the first loaded table (Excise)
        // Check if the table exists before initializing
        $(document).ready(function() {
            if ($('#purchaseReportTable').length) {
                $('#purchaseReportTable').DataTable({
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "destroy": true // Important for re-initializing DataTables
                });
            }
        });
    });
</script>
{% endblock %}
```

**`purchasereports/templates/purchasereports/partials/purchase_excise_form_content.html`**
This partial contains the form and the HTMX target area for the Excise report.

```html
<form hx-post="{% url 'purchase_report_excise_data' %}" 
      hx-target="#report-content-excise" 
      hx-swap="innerHTML" 
      class="flex flex-col sm:flex-row items-start sm:items-end space-y-4 sm:space-y-0 sm:space-x-4 mb-6">
    {% csrf_token %}
    
    <div class="flex-1 w-full">
        <label for="{{ form.from_date.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.from_date.label }}:</label>
        {{ form.from_date }}
        {% if form.from_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.from_date.errors|join:", " }}</p>{% endif %}
    </div>
    <div class="flex-1 w-full">
        <label for="{{ form.to_date.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.to_date.label }}:</label>
        {{ form.to_date }}
        {% if form.to_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.to_date.errors|join:", " }}</p>{% endif %}
    </div>
    <div class="w-full sm:w-auto">
        <button type="submit" class="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Search
        </button>
    </div>
    {% if form.non_field_errors %}
        <p class="text-red-500 text-xs mt-1 w-full sm:col-span-3">{{ form.non_field_errors|join:", " }}</p>
    {% endif %}
</form>

<div id="report-content-excise" class="min-h-[410px] overflow-auto border border-gray-200 rounded-md">
    {# Initial load of Excise report data #}
    {% if report_data %}
        {% include 'purchasereports/partials/purchase_report_table.html' with report_data=report_data report_type='excise' report_summary=report_summary comp_address=comp_address %}
    {% else %}
        {% include 'purchasereports/partials/no_records.html' %}
    {% endif %}
</div>

<script>
    // Re-initialize Flatpickr and DataTables when this partial is swapped into the DOM
    // This script runs if the form content itself is swapped (e.g., due to validation errors)
    document.addEventListener('htmx:afterSwap', function(event) {
        // Ensure we're targeting the correct content area when it's replaced
        if (event.detail.target.id === 'tab-content-excise' || event.detail.target.id === 'report-content-excise') {
            flatpickr(event.detail.target.querySelector('#excise-from_date'), { dateFormat: "d-m-Y" });
            flatpickr(event.detail.target.querySelector('#excise-to_date'), { dateFormat: "d-m-Y" });
            
            // Re-initialize DataTables if the table is present in the new content
            $(document).ready(function() {
                if ($(event.detail.target).find('#purchaseReportTable').length) {
                    $('#purchaseReportTable').DataTable({
                        "pageLength": 10,
                        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                        "destroy": true 
                    });
                }
            });
        }
    });
</script>
```

**`purchasereports/templates/purchasereports/partials/purchase_vatcst_form_content.html`**
This partial contains the form and the HTMX target area for the VAT/CST report.

```html
<form hx-post="{% url 'purchase_report_vatcst_data' %}" 
      hx-target="#report-content-vatcst" 
      hx-swap="innerHTML" 
      class="flex flex-col sm:flex-row items-start sm:items-end space-y-4 sm:space-y-0 sm:space-x-4 mb-6">
    {% csrf_token %}
    
    <div class="flex-1 w-full">
        <label for="{{ form.from_date.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.from_date.label }}:</label>
        {{ form.from_date }}
        {% if form.from_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.from_date.errors|join:", " }}</p>{% endif %}
    </div>
    <div class="flex-1 w-full">
        <label for="{{ form.to_date.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.to_date.label }}:</label>
        {{ form.to_date }}
        {% if form.to_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.to_date.errors|join:", " }}</p>{% endif %}
    </div>
    <div class="w-full sm:w-auto">
        <button type="submit" class="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Search
        </button>
    </div>
    {% if form.non_field_errors %}
        <p class="text-red-500 text-xs mt-1 w-full sm:col-span-3">{{ form.non_field_errors|join:", " }}</p>
    {% endif %}
</form>

<div id="report-content-vatcst" class="min-h-[410px] overflow-auto border border-gray-200 rounded-md">
    {# Placeholder for initial load; data will be loaded via HTMX after search #}
    {% include 'purchasereports/partials/no_records.html' %}
</div>

<script>
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'tab-content-vatcst' || event.detail.target.id === 'report-content-vatcst') {
            flatpickr(event.detail.target.querySelector('#vatcst-from_date'), { dateFormat: "d-m-Y" });
            flatpickr(event.detail.target.querySelector('#vatcst-to_date'), { dateFormat: "d-m-Y" });

            $(document).ready(function() {
                if ($(event.detail.target).find('#purchaseReportTable').length) {
                    $('#purchaseReportTable').DataTable({
                        "pageLength": 10,
                        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                        "destroy": true
                    });
                }
            });
        }
    });
</script>
```

**`purchasereports/templates/purchasereports/partials/purchase_vatcst_labour_form_content.html`**
This partial contains the form and the HTMX target area for the VAT/CST Labour report.

```html
<form hx-post="{% url 'purchase_report_vatcst_labour_data' %}" 
      hx-target="#report-content-vatcst_labour" 
      hx-swap="innerHTML" 
      class="flex flex-col sm:flex-row items-start sm:items-end space-y-4 sm:space-y-0 sm:space-x-4 mb-6">
    {% csrf_token %}
    
    <div class="flex-1 w-full">
        <label for="{{ form.from_date.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.from_date.label }}:</label>
        {{ form.from_date }}
        {% if form.from_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.from_date.errors|join:", " }}</p>{% endif %}
    </div>
    <div class="flex-1 w-full">
        <label for="{{ form.to_date.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.to_date.label }}:</label>
        {{ form.to_date }}
        {% if form.to_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.to_date.errors|join:", " }}</p>{% endif %}
    </div>
    <div class="w-full sm:w-auto">
        <button type="submit" class="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Search
        </button>
    </div>
    {% if form.non_field_errors %}
        <p class="text-red-500 text-xs mt-1 w-full sm:col-span-3">{{ form.non_field_errors|join:", " }}</p>
    {% endif %}
</form>

<div id="report-content-vatcst_labour" class="min-h-[410px] overflow-auto border border-gray-200 rounded-md">
    {# Placeholder for initial load; data will be loaded via HTMX after search #}
    {% include 'purchasereports/partials/no_records.html' %}
</div>

<script>
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'tab-content-vatcst_labour' || event.detail.target.id === 'report-content-vatcst_labour') {
            flatpickr(event.detail.target.querySelector('#labour-from_date'), { dateFormat: "d-m-Y" });
            flatpickr(event.detail.target.querySelector('#labour-to_date'), { dateFormat: "d-m-Y" });

            $(document).ready(function() {
                if ($(event.detail.target).find('#purchaseReportTable').length) {
                    $('#purchaseReportTable').DataTable({
                        "pageLength": 10,
                        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                        "destroy": true
                    });
                }
            });
        }
    });
</script>
```

**`purchasereports/templates/purchasereports/partials/purchase_report_table.html`**
This partial renders the actual DataTables report and the summary information.

```html
<div class="overflow-x-auto">
    {% if messages %}
    <div class="mb-4 p-3 border rounded-md shadow-sm">
        {% for message in messages %}
        <div class="text-sm {% if message.tags == 'warning' %}text-yellow-800 bg-yellow-50{% else %}text-green-800 bg-green-50{% endif %} p-2 rounded-md">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <table id="purchaseReportTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SysDate</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier Name [ID]</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Basic Amount</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PF Terms</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PF Amount</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Excise Values</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Excise Amount</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">EDU Cess</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">EDU Value</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SHE Cess</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SHE Value</th>
                {% if report_type != 'excise' %} {# VAT/CST and VAT/CST Labour tabs display these columns #}
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">VAT/CST Terms</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">VAT/CST Amount</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Freight Amount</th>
                {% endif %}
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Excise Basic</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ex Basic Amount</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for row in report_data %} {# This is the dt (VAT) data #}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ row.SysDate }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ row.SupplierName }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.BasicAmt|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ row.PFTerms }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.PF|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ row.ExciseValues }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.ExciseAmt|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ row.EDUCess }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.EDUValue|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ row.SHECess }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.SHEValue|floatformat:2 }}</td>
                {% if report_type != 'excise' %}
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ row.VATCSTTerms }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.VATCSTAmt|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.FreightAmt|floatformat:2 }}</td>
                {% endif %}
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right font-bold">{{ row.TotAmt|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ row.ExciseBasic }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.ExBasicAmt|floatformat:2 }}</td>
            </tr>
            {% endfor %}
            {% if report_type != 'excise' %} {# Only VAT/CST and Labour tabs combine VAT and CST data in the report #}
                {% for row in cst_report_data %} {# This is the dt2 (CST) data #}
                <tr class="bg-gray-50"> {# Differentiate CST rows if desired #}
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ row.SysDate }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ row.SupplierName }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.BasicAmt|floatformat:2 }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ row.PFTerms }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.PF|floatformat:2 }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ row.ExciseValues }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.ExciseAmt|floatformat:2 }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ row.EDUCess }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.EDUValue|floatformat:2 }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ row.SHECess }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.SHEValue|floatformat:2 }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ row.VATCSTTerms }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.VATCSTAmt|floatformat:2 }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.FreightAmt|floatformat:2 }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right font-bold">{{ row.TotAmt|floatformat:2 }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ row.ExciseBasic }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.ExBasicAmt|floatformat:2 }}</td>
                </tr>
                {% endfor %}
            {% endif %}
        </tbody>
    </table>
</div>

<div class="mt-6 p-4 border rounded-md bg-gray-50 text-sm">
    <h4 class="text-base font-medium text-gray-800 mb-2">Report Summary</h4>
    <p><strong>Company Address:</strong> {{ comp_address }}</p>
    <p><strong>From Date:</strong> {{ from_date }}</p>
    <p><strong>To Date:</strong> {{ to_date }}</p>
    {% if report_type == 'excise' %}
    <p><strong>Total Excise (Combined):</strong> {{ report_summary.total_excise|floatformat:2 }}</p>
    <p><strong>MAH Purchase:</strong> {{ report_summary.mah_purchase }}</p>
    {% else %}
    <p><strong>VAT Gross Total:</strong> {{ vat_gross_total|floatformat:2 }}</p>
    <p><strong>CST Gross Total:</strong> {{ cst_gross_total|floatformat:2 }}</p>
    <p><strong>Total Excise (Combined):</strong> {{ total_excise|floatformat:2 }}</p>
    <p><strong>MAH Purchase:</strong> {{ mah_purchase }}</p>
    {% endif %}
</div>

<script>
    // Initialize DataTables when this partial is loaded and the table element is available
    $(document).ready(function() {
        if ($('#purchaseReportTable').length) {
            $('#purchaseReportTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true // Important to allow re-initialization on subsequent HTMX swaps
            });
        }
    });
</script>
```

**`purchasereports/templates/purchasereports/partials/no_records.html`**
A simple partial to show when no data is found.

```html
{% if messages %}
<div class="mb-4 p-3 border rounded-md shadow-sm">
    {% for message in messages %}
    <div class="text-sm {% if message.tags == 'warning' %}text-yellow-800 bg-yellow-50{% else %}text-green-800 bg-green-50{% endif %} p-2 rounded-md">
        {{ message }}
    </div>
    {% endfor %}
</div>
{% endif %}
<div class="text-center py-10 text-gray-500">
    <p>No record found!</p>
</div>
```

#### 4.5 URLs (`purchasereports/urls.py`)

Define the URL patterns for the main report page and the HTMX endpoints for each tab's data.

```python
from django.urls import path
from .views import PurchaseReportView, PurchaseReportPartialView

urlpatterns = [
    # Main entry point for the purchase report page
    path('purchase-report/', PurchaseReportView.as_view(), name='purchase_report_main'),
    
    # HTMX endpoints for dynamically loading report data for each tab
    # These views handle POST requests with date ranges
    path('purchase-report/excise-data/', PurchaseReportPartialView.as_view(), {'report_type': 'excise'}, name='purchase_report_excise_data'),
    path('purchase-report/vatcst-data/', PurchaseReportPartialView.as_view(), {'report_type': 'vatcst'}, name='purchase_report_vatcst_data'),
    path('purchase-report/vatcst-labour-data/', PurchaseReportPartialView.as_view(), {'report_type': 'vatcst_labour'}, name='purchase_report_vatcst_labour_data'),
]
```

#### 4.6 Tests (`purchasereports/tests.py`)

Comprehensive tests for the model manager's data generation logic and the views.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date, timedelta
from unittest.mock import patch, MagicMock
from decimal import Decimal

# Import all models
from .models import (
    PackingMaster, ExciseMaster, VatMaster, SupplierMaster,
    BillBookingMaster, BillBookingDetail, PoDetail, MaterialQualityDetail,
    MaterialServiceNoteDetail, PurchaseReport
)

class PurchaseReportModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dummy data for all related tables, ensuring IDs match potential FKs
        cls.packing_master = PackingMaster.objects.create(id=1, value='PF Terms 10%')
        cls.excise_master = ExciseMaster.objects.create(id=1, value='Excise 12%', accessable_value='Assessable Value', educess='Edu Cess 2%', shecess='SHE Cess 1%')
        cls.vat_master_vat = VatMaster.objects.create(id=1, value='VAT 5%', is_vat=True, is_cst=False)
        cls.vat_master_cst = VatMaster.objects.create(id=2, value='CST 2%', is_vat=False, is_cst=True)
        cls.vat_master_none = VatMaster.objects.create(id=3, value='No VAT/CST', is_vat=False, is_cst=False) # For case where both are 0
        cls.supplier = SupplierMaster.objects.create(supplier_id=101, supplier_name='Test Supplier A')

        cls.po_detail_vat = PoDetail.objects.create(id=1, rate=Decimal('100.00'), discount=Decimal('10.00'), pf=cls.packing_master, ex_st=cls.excise_master, vat=cls.vat_master_vat)
        cls.po_detail_cst = PoDetail.objects.create(id=2, rate=Decimal('200.00'), discount=Decimal('0.00'), pf=cls.packing_master, ex_st=cls.excise_master, vat=cls.vat_master_cst)

        cls.bill_master_excise = BillBookingMaster.objects.create(id=1, sys_date=date(2023, 1, 15), supplier=cls.supplier, comp_id=1)
        cls.bill_master_vatcst = BillBookingMaster.objects.create(id=2, sys_date=date(2023, 1, 20), supplier=cls.supplier, comp_id=1)
        cls.bill_master_labour = BillBookingMaster.objects.create(id=3, sys_date=date(2023, 1, 25), supplier=cls.supplier, comp_id=1)

        cls.material_quality_detail = MaterialQualityDetail.objects.create(id=1, accepted_qty=Decimal('50.00'))
        cls.material_service_note_detail_vat = MaterialServiceNoteDetail.objects.create(id=2, received_qty=Decimal('20.00'))
        cls.material_service_note_detail_cst = MaterialServiceNoteDetail.objects.create(id=3, received_qty=Decimal('30.00'))

        # BillBookingDetail for Excise report (uses MaterialQualityDetail - GQNId)
        BillBookingDetail.objects.create(
            id=1, master=cls.bill_master_excise, pod_id=cls.po_detail_vat.id, gqn_id=cls.material_quality_detail.id,
            pf_amt=Decimal('5.00'), ex_st_basic=Decimal('10.00'), ex_st_educess=Decimal('1.00'), ex_st_shecess=Decimal('0.50'),
            vat=Decimal('2.00'), cst=Decimal('0.00'), freight=Decimal('1.00')
        )
        # BillBookingDetail for VAT/CST report (uses MaterialServiceNoteDetail - GSNId)
        BillBookingDetail.objects.create(
            id=2, master=cls.bill_master_vatcst, pod_id=cls.po_detail_vat.id, gsn_id=cls.material_service_note_detail_vat.id,
            pf_amt=Decimal('8.00'), ex_st_basic=Decimal('15.00'), ex_st_educess=Decimal('1.50'), ex_st_shecess=Decimal('0.75'),
            vat=Decimal('3.00'), cst=Decimal('0.00'), freight=Decimal('2.00')
        )
        # BillBookingDetail for VAT/CST Labour report (uses MaterialServiceNoteDetail - GSNId, but is CST)
        BillBookingDetail.objects.create(
            id=3, master=cls.bill_master_labour, pod_id=cls.po_detail_cst.id, gsn_id=cls.material_service_note_detail_cst.id,
            pf_amt=Decimal('10.00'), ex_st_basic=Decimal('20.00'), ex_st_educess=Decimal('2.00'), ex_st_shecess=Decimal('1.00'),
            vat=Decimal('0.00'), cst=Decimal('4.00'), freight=Decimal('3.00')
        )

    def test_purchase_report_excise_data_generation(self):
        # Test case for the 'excise' report_type, which uses MaterialQualityDetail (GQNId)
        from_date = date(2023, 1, 1)
        to_date = date(2023, 1, 31)
        comp_id = 1
        
        report_data = PurchaseReport.objects.get_purchase_report_data(from_date, to_date, 'excise', comp_id)
        
        self.assertIn('vat_data', report_data)
        self.assertIn('cst_data', report_data)
        self.assertEqual(len(report_data['vat_data']), 1) # Only one excise record with GQNId in range
        self.assertEqual(len(report_data['cst_data']), 0) # Excise report does not explicitly output CST table
        
        row = report_data['vat_data'][0]
        self.assertEqual(row['SysDate'], '15-01-2023')
        self.assertEqual(row['SupplierName'], 'Test Supplier A [101]')
        
        # Calculation: AcceptedQty * (Rate - (Rate * Discount / 100))
        # 50 * (100 - (100 * 10 / 100)) = 50 * (100 - 10) = 50 * 90 = 4500
        self.assertAlmostEqual(row['BasicAmt'], 4500.00) 
        self.assertAlmostEqual(row['PF'], 5.00)
        self.assertAlmostEqual(row['ExciseAmt'], 10.00 + 1.00 + 0.50) # exba + edu + she = 11.50
        self.assertAlmostEqual(row['VATCSTAmt'], 2.00)
        self.assertAlmostEqual(row['FreightAmt'], 1.00)
        # Total Amount: BasicAmt + PF + ExciseAmt + VATCSTAmt + FreightAmt
        # 4500 + 5 + 11.5 + 2 + 1 = 4519.5
        self.assertAlmostEqual(row['TotAmt'], 4519.50)
        
        self.assertAlmostEqual(report_data['vat_gross_total'], 4519.50)
        self.assertAlmostEqual(report_data['cst_gross_total'], 0.00)
        self.assertAlmostEqual(report_data['total_excise'], 11.50)
        self.assertIn('MAHPurchase', report_data['mah_purchase']) # Check if MAH purchase string is generated

    def test_purchase_report_vatcst_data_generation(self):
        # Test case for 'vatcst' report_type, which uses MaterialServiceNoteDetail (GSNId) for both VAT and CST rows
        from_date = date(2023, 1, 1)
        to_date = date(2023, 1, 31)
        comp_id = 1
        
        report_data = PurchaseReport.objects.get_purchase_report_data(from_date, to_date, 'vatcst', comp_id)
        
        self.assertEqual(len(report_data['vat_data']), 1) # One VAT record with GSNId
        self.assertEqual(len(report_data['cst_data']), 1) # One CST record with GSNId

        vat_row = report_data['vat_data'][0]
        cst_row = report_data['cst_data'][0]
        
        self.assertEqual(vat_row['SysDate'], '20-01-2023')
        # Calculation: ReceivedQty * (Rate - (Rate * Discount / 100))
        # 20 * (100 - (100 * 10 / 100)) = 20 * 90 = 1800
        self.assertAlmostEqual(vat_row['BasicAmt'], 1800.00)
        self.assertAlmostEqual(vat_row['PF'], 8.00)
        self.assertAlmostEqual(vat_row['ExciseAmt'], 15.00 + 1.50 + 0.75) # 17.25
        self.assertAlmostEqual(vat_row['VATCSTAmt'], 3.00)
        self.assertAlmostEqual(vat_row['FreightAmt'], 2.00)
        self.assertAlmostEqual(vat_row['TotAmt'], 1800 + 8 + 17.25 + 3 + 2) # 1830.25

        self.assertEqual(cst_row['SysDate'], '25-01-2023')
        # Calculation: ReceivedQty * (Rate - (Rate * Discount / 100))
        # 30 * (200 - (200 * 0 / 100)) = 30 * 200 = 6000
        self.assertAlmostEqual(cst_row['BasicAmt'], 6000.00)
        self.assertAlmostEqual(cst_row['PF'], 10.00)
        self.assertAlmostEqual(cst_row['ExciseAmt'], 20.00 + 2.00 + 1.00) # 23.00
        self.assertAlmostEqual(cst_row['VATCSTAmt'], 4.00)
        self.assertAlmostEqual(cst_row['FreightAmt'], 3.00)
        self.assertAlmostEqual(cst_row['TotAmt'], 6000 + 10 + 23 + 4 + 3) # 6040.00
        
        # Test totals
        self.assertAlmostEqual(report_data['vat_gross_total'], 1830.25)
        self.assertAlmostEqual(report_data['cst_gross_total'], 6040.00)
        self.assertAlmostEqual(report_data['total_excise'], 17.25 + 23.00) # Sum of excise for both rows

    def test_purchase_report_no_data_found(self):
        from_date = date(2024, 1, 1) # Dates outside existing data
        to_date = date(2024, 1, 31)
        comp_id = 1
        
        report_data = PurchaseReport.objects.get_purchase_report_data(from_date, to_date, 'excise', comp_id)
        self.assertEqual(len(report_data['vat_data']), 0)
        self.assertEqual(len(report_data['cst_data']), 0)
        self.assertEqual(report_data['vat_gross_total'], 0)
        self.assertEqual(report_data['cst_gross_total'], 0)
        self.assertEqual(report_data['total_excise'], 0)
        self.assertEqual(report_data['mah_purchase'], '')

class PurchaseReportViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Mock session compid, as it's used in the view
        session = self.client.session
        session['compid'] = 1
        session.save()

        # Create minimal data for views to render something without deep calculation
        # This data doesn't need to be perfectly accurate for report calculations, just enough
        # to ensure that queries run and templates render.
        self.packing_master = PackingMaster.objects.create(id=1, value='PF Terms Test')
        self.excise_master = ExciseMaster.objects.create(id=1, value='Excise Test', accessable_value='Access Test', educess='Edu Test', shecess='SHE Test')
        self.vat_master = VatMaster.objects.create(id=1, value='VAT Test', is_vat=True, is_cst=False)
        self.supplier = SupplierMaster.objects.create(supplier_id=101, supplier_name='View Test Supplier')
        
        self.po_detail = PoDetail.objects.create(id=1, rate=Decimal('100.00'), discount=Decimal('10.00'), pf=self.packing_master, ex_st=self.excise_master, vat=self.vat_master)
        self.bill_master = BillBookingMaster.objects.create(id=1, sys_date=date(2023, 1, 15), supplier=self.supplier, comp_id=1)
        self.material_quality_detail = MaterialQualityDetail.objects.create(id=1, accepted_qty=Decimal('10.00'))
        
        # Create a single BillBookingDetail for the excise report path
        BillBookingDetail.objects.create(
            id=1, master=self.bill_master, pod_id=self.po_detail.id, gqn_id=self.material_quality_detail.id,
            pf_amt=Decimal('5.00'), ex_st_basic=Decimal('10.00'), ex_st_educess=Decimal('1.00'), ex_st_shecess=Decimal('0.50'),
            vat=Decimal('2.00'), cst=Decimal('0.00'), freight=Decimal('1.00')
        )

    @patch('purchasereports.models.PurchaseReportManager.get_purchase_report_data')
    def test_purchase_report_main_view_get(self, mock_get_report_data):
        # Mock the manager method to return predictable data
        mock_get_report_data.return_value = {
            'vat_data': [{'SysDate': '15-01-2023', 'SupplierName': 'Mock Supplier [1]', 'BasicAmt': 100.00, 'PF': 10.00, 'ExciseAmt': 12.00, 'TotAmt': 123.00, 'FreightAmt': 5.00, 'VATCSTAmt': 5.00, 'PFTerms': 'PF', 'ExciseValues': 'EX', 'EDUCess': 'EDU', 'SHECess': 'SHE', 'EDUValue': 1, 'SHEValue': 1, 'VATCSTTerms': 'VAT', 'ExciseBasic': 'EB', 'ExBasicAmt': 1}],
            'cst_data': [],
            'vat_gross_total': 123.00, 'cst_gross_total': 0.00, 'total_excise': 12.00, 'mah_purchase': 'Mock MAH'
        }
        response = self.client.get(reverse('purchase_report_main'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'purchasereports/purchase_report_main.html')
        self.assertContains(response, 'Purchase Report')
        self.assertTrue(mock_get_report_data.called)
        
    @patch('purchasereports.models.PurchaseReportManager.get_purchase_report_data')
    def test_purchase_report_excise_data_post_success(self, mock_get_report_data):
        mock_get_report_data.return_value = {
            'vat_data': [{'SysDate': '15-01-2023', 'SupplierName': 'Mock Supplier [1]', 'BasicAmt': 100.00, 'PF': 10.00, 'ExciseAmt': 12.00, 'TotAmt': 123.00, 'FreightAmt': 5.00, 'VATCSTAmt': 5.00, 'PFTerms': 'PF', 'ExciseValues': 'EX', 'EDUCess': 'EDU', 'SHECess': 'SHE', 'EDUValue': 1, 'SHEValue': 1, 'VATCSTTerms': 'VAT', 'ExciseBasic': 'EB', 'ExBasicAmt': 1}],
            'cst_data': [],
            'vat_gross_total': 123.00, 'cst_gross_total': 0.00, 'total_excise': 12.00, 'mah_purchase': 'Mock MAH'
        }
        response = self.client.post(
            reverse('purchase_report_excise_data'),
            {'excise-from_date': '01-01-2023', 'excise-to_date': '31-01-2023'}
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'purchasereports/partials/purchase_report_table.html')
        self.assertContains(response, 'Mock Supplier [1]')
        self.assertTrue(mock_get_report_data.called)

    @patch('purchasereports.models.PurchaseReportManager.get_purchase_report_data')
    def test_purchase_report_vatcst_data_post_success(self, mock_get_report_data):
        mock_get_report_data.return_value = {
            'vat_data': [],
            'cst_data': [{'SysDate': '20-01-2023', 'SupplierName': 'Mock Supplier [2]', 'BasicAmt': 200.00, 'PF': 20.00, 'ExciseAmt': 24.00, 'TotAmt': 244.00, 'FreightAmt': 10.00, 'VATCSTAmt': 10.00, 'PFTerms': 'PF', 'ExciseValues': 'EX', 'EDUCess': 'EDU', 'SHECess': 'SHE', 'EDUValue': 1, 'SHEValue': 1, 'VATCSTTerms': 'CST', 'ExciseBasic': 'EB', 'ExBasicAmt': 1}],
            'vat_gross_total': 0.00, 'cst_gross_total': 244.00, 'total_excise': 24.00, 'mah_purchase': 'Mock CST MAH'
        }
        response = self.client.post(
            reverse('purchase_report_vatcst_data'),
            {'vatcst-from_date': '01-01-2023', 'vatcst-to_date': '31-01-2023'}
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'purchasereports/partials/purchase_report_table.html')
        self.assertContains(response, 'Mock Supplier [2]')
        self.assertTrue(mock_get_report_data.called)

    @patch('purchasereports.models.PurchaseReportManager.get_purchase_report_data')
    def test_purchase_report_no_records_found(self, mock_get_report_data):
        mock_get_report_data.return_value = {
            'vat_data': [], 'cst_data': [], 'vat_gross_total': 0, 'cst_gross_total': 0, 'total_excise': 0, 'mah_purchase': ''
        }
        response = self.client.post(
            reverse('purchase_report_excise_data'),
            {'excise-from_date': '01-01-2025', 'excise-to_date': '31-01-2025'}
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'purchasereports/partials/no_records.html')
        self.assertContains(response, 'No record found!')
        self.assertTrue(mock_get_report_data.called)

    def test_form_validation_invalid_date_range(self):
        # Test that form validation works and returns 400 with errors
        response = self.client.post(
            reverse('purchase_report_excise_data'),
            {'excise-from_date': '31-01-2023', 'excise-to_date': '01-01-2023'}
        )
        self.assertEqual(response.status_code, 400) 
        self.assertContains(response, 'Invalid selected date range.')
        # Ensure the correct partial is rendered to show the form with errors
        self.assertTemplateUsed(response, 'purchasereports/partials/purchase_excise_form_content.html')
```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for Tabs**: The main template (`purchase_report_main.html`) uses Alpine.js for tab switching. When a tab is clicked, Alpine updates `activeTab`, and `hx-get` attributes are not directly on tabs. Instead, the "Search" buttons within each tab's content (`purchase_excise_form_content.html`, etc.) will use `hx-post` to send data to the appropriate `PurchaseReportPartialView` endpoint. This `hx-post` replaces the `report-content-{{ report_type }}` div with the new table data. This avoids `hx-get` on tabs for complex form submissions.
*   **HTMX for Form Submission**: Each report form submits via `hx-post` targeting its respective `report-content` div. This ensures only the report table area updates.
*   **DataTables**: Each `purchase_report_table.html` partial includes a JavaScript snippet to initialize DataTables on the `#purchaseReportTable` after it's loaded/swapped by HTMX. `destroy: true` is vital for re-initialization.
*   **Alpine.js for Date Pickers**: Forms use `x-init='flatpickr($el, { dateFormat: "d-m-Y" })'` for declarative Flatpickr initialization, which works for both initial page load and HTMX-swapped content.
*   **Message Display**: Django's messages framework is used, and messages are included in the `purchase_report_table.html` and `no_records.html` partials, so they appear after HTMX swaps.

---

**Final Notes:**

This plan provides a robust, modern Django solution for the legacy ASP.NET report. It prioritizes automated data migration to the new schema and a clear separation of concerns, ensuring business logic resides in the `PurchaseReportManager`. The use of HTMX and Alpine.js delivers a responsive, dynamic user experience without complex JavaScript frameworks, making the application compact, fast, and easy to maintain. Automated tests ensure the accuracy of the complex report generation logic and the proper functioning of the web interface. This structured approach, communicated in plain English, allows for efficient AI-assisted automation and clear oversight by business stakeholders.