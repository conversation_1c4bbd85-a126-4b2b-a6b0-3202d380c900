## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Analysis of Provided ASP.NET Code:

The provided ASP.NET code for `Dashboard.aspx` and its corresponding `Dashboard.aspx.cs` file is extremely minimal. The `.aspx` file primarily defines content placeholders, extending a master page, and includes a single JavaScript file (`loadingNotifier.js`). The `.aspx.cs` code-behind file contains only an empty `Page_Load` method, indicating no explicit server-side logic, database interactions, or UI components (like `GridView`, `TextBox`, `Button`) are defined within these specific files.

**Conclusion:** Due to the lack of concrete business logic, database schema, or interactive UI elements in the provided ASP.NET code, a direct, feature-for-feature migration is not possible. Instead, this modernization plan will demonstrate how to implement a typical CRUD (Create, Read, Update, Delete) module in Django, leveraging the core principles of fat models, thin views, HTMX, Alpine.js, and DataTables. We will assume a conceptual "Dashboard Item" entity that a dashboard might manage or display, allowing us to illustrate a complete, runnable Django component that adheres to all specified modernization guidelines.

## Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
Given the provided ASP.NET code is empty of business logic and database interactions, we must infer a plausible structure for a "Dashboard" related entity to demonstrate a full Django CRUD solution. We will define a generic entity, `DashboardItem`, which could represent any configurable or displayable item on a dashboard (e.g., a key performance indicator, a quick link, or a task summary).

*   **Assumed Table Name:** `tbl_dashboard_items`
*   **Assumed Columns:**
    *   `id` (Primary Key, integer)
    *   `item_name` (Name of the dashboard item, string)
    *   `item_description` (Description of the item, text)
    *   `item_value` (A numerical value associated with the item, e.g., a count or a metric, decimal)
    *   `is_active` (Boolean to indicate if the item is currently active/visible)

## Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
No explicit CRUD operations (Create, Read, Update, Delete) were found in the provided empty ASP.NET `Dashboard.aspx.cs` file. The file `loadingNotifier.js` suggests some client-side interaction related to loading states, but no specific data operations are present.

For the purpose of demonstrating a complete Django modernization, we will implement full CRUD operations for the `DashboardItem` entity, showcasing how to manage these items. This will include:
*   **Read:** Displaying a list of all `DashboardItem` objects.
*   **Create:** Adding new `DashboardItem` objects.
*   **Update:** Modifying existing `DashboardItem` objects.
*   **Delete:** Removing `DashboardItem` objects.
*   **Validation:** Basic form validation for required fields and data types.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The provided `Dashboard.aspx` file contains only content placeholders and a JavaScript include. No specific ASP.NET UI controls like `GridView`, `TextBox`, `DropDownList`, `Button`, or `LinkButton` were present.

For the Django implementation, we will design the UI based on the common patterns for CRUD operations:
*   A **list view** to display all `DashboardItem` objects, utilizing **DataTables** for search, sort, and pagination.
*   A **modal form** (loaded via HTMX) for creating and updating `DashboardItem` objects, containing input fields (e.g., `TextInput`, `Textarea`, `NumberInput`, `Checkbox`).
*   A **modal confirmation dialog** (loaded via HTMX) for deleting `DashboardItem` objects.
*   **HTMX** will be used for all dynamic interactions, including loading forms into modals, submitting forms without full page reloads, and refreshing the item list upon successful CRUD operations.
*   **Alpine.js** will manage the display state of the modal.
*   **Tailwind CSS** will be used for styling all components.

## Step 4: Generate Django Code

We will create a new Django application, let's call it `dashboard_app`, to house the modernized components.

### 4.1 Models

**Task:** Create a Django model based on the database schema.

**Instructions:**
The `DashboardItem` model is mapped to the `tbl_dashboard_items` table. It includes methods for business logic, adhering to the "fat model" principle.

**File: `dashboard_app/models.py`**
```python
from django.db import models

class DashboardItem(models.Model):
    """
    Represents an item or widget displayed on a dashboard.
    Mapped to an existing database table 'tbl_dashboard_items'.
    """
    id = models.AutoField(db_column='id', primary_key=True)
    name = models.CharField(db_column='item_name', max_length=255, verbose_name="Item Name")
    description = models.TextField(db_column='item_description', blank=True, verbose_name="Description")
    value = models.DecimalField(db_column='item_value', max_digits=10, decimal_places=2, null=True, blank=True, verbose_name="Value")
    is_active = models.BooleanField(db_column='is_active', default=True, verbose_name="Is Active")

    class Meta:
        managed = False  # Set to False if the table is managed externally (e.g., by an existing ASP.NET DB)
        db_table = 'tbl_dashboard_items'
        verbose_name = 'Dashboard Item'
        verbose_name_plural = 'Dashboard Items'

    def __str__(self):
        return self.name

    def get_display_status(self):
        """
        Business logic: Determine the display status based on 'is_active' and 'value'.
        """
        if not self.is_active:
            return "Inactive"
        if self.value is not None and self.value < 0:
            return "Alert (Negative Value)"
        return "Active"

    def update_item_status(self, new_status: bool):
        """
        Business logic: Updates the active status of the dashboard item.
        """
        self.is_active = new_status
        self.save()
        return self.is_active
```

### 4.2 Forms

**Task:** Define a Django form for user input.

**Instructions:**
A `ModelForm` for `DashboardItem` is created, including widgets with Tailwind CSS classes for consistent styling.

**File: `dashboard_app/forms.py`**
```python
from django import forms
from .models import DashboardItem

class DashboardItemForm(forms.ModelForm):
    """
    Form for creating and updating DashboardItem objects.
    """
    class Meta:
        model = DashboardItem
        fields = ['name', 'description', 'value', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'value': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded'}),
        }
        
    def clean_name(self):
        """
        Custom validation for the name field.
        Ensures the name is unique (case-insensitive) for new items.
        For updates, it allows the existing name.
        """
        name = self.cleaned_data.get('name')
        if name:
            query = DashboardItem.objects.filter(name__iexact=name)
            if self.instance.pk: # If it's an update, exclude the current instance
                query = query.exclude(pk=self.instance.pk)
            if query.exists():
                raise forms.ValidationError("A dashboard item with this name already exists.")
        return name
```

### 4.3 Views

**Task:** Implement CRUD operations using CBVs.

**Instructions:**
Views are kept thin, delegating business logic to the model and handling HTMX-specific responses for dynamic updates. A partial view for the DataTables content is included for HTMX.

**File: `dashboard_app/views.py`**
```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import DashboardItem
from .forms import DashboardItemForm

class DashboardItemListView(ListView):
    """
    Displays the main list page for Dashboard Items.
    The actual table content is loaded via HTMX by DashboardItemTablePartialView.
    """
    model = DashboardItem
    template_name = 'dashboard_app/dashboarditem/list.html'
    context_object_name = 'dashboard_items' # Not directly used by this view, but by the partial.

class DashboardItemTablePartialView(TemplateView):
    """
    Renders only the DataTables portion of the Dashboard Item list.
    Designed to be fetched via HTMX.
    """
    template_name = 'dashboard_app/dashboarditem/_dashboarditem_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['dashboard_items'] = DashboardItem.objects.all()
        return context

class DashboardItemCreateView(CreateView):
    """
    Handles creation of new Dashboard Items.
    Responds with HTMX headers for modal closing and list refresh.
    """
    model = DashboardItem
    form_class = DashboardItemForm
    template_name = 'dashboard_app/dashboarditem/_dashboarditem_form.html'
    success_url = reverse_lazy('dashboarditem_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Dashboard Item added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content to indicate success without navigating
                headers={
                    'HX-Trigger': 'refreshDashboardItemList,closeModal' # Custom events for HTMX
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response # Return the form with errors for HTMX to swap
        return response


class DashboardItemUpdateView(UpdateView):
    """
    Handles updating existing Dashboard Items.
    Responds with HTMX headers for modal closing and list refresh.
    """
    model = DashboardItem
    form_class = DashboardItemForm
    template_name = 'dashboard_app/dashboarditem/_dashboarditem_form.html'
    success_url = reverse_lazy('dashboarditem_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Dashboard Item updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardItemList,closeModal'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response
        return response

class DashboardItemDeleteView(DeleteView):
    """
    Handles deletion of Dashboard Items.
    Responds with HTMX headers for modal closing and list refresh.
    """
    model = DashboardItem
    template_name = 'dashboard_app/dashboarditem/_dashboarditem_confirm_delete.html'
    success_url = reverse_lazy('dashboarditem_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Dashboard Item deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardItemList,closeModal'
                }
            )
        return response
```

### 4.4 Templates

**Task:** Create templates for each view.

**Instructions:**
Templates utilize `core/base.html` for inheritance. HTMX attributes are used for dynamic loading and interactions. DataTables is initialized for the list view. Alpine.js is used for modal visibility.

**File: `dashboard_app/templates/dashboard_app/dashboarditem/list.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Dashboard Items</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'dashboarditem_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Item
        </button>
    </div>
    
    <div id="dashboarditemTable-container"
         hx-trigger="load, refreshDashboardItemList from:body"
         hx-get="{% url 'dashboarditem_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Dashboard Items...</p>
        </div>
    </div>
    
    <!-- Modal for form/confirmation -->
    <div id="modal" 
         class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         hx-on::after-request="if (event.detail.successful) remove .is-active from #modal"
         hx-on::htmx:before-swap="if (event.detail.xhr.status === 204) { event.detail.shouldSwap = false }"
         hx-on:closeModal="remove .is-active from #modal">
        <div id="modalContent" 
             class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 transform transition-all scale-95 opacity-0"
             _="on load add .scale-100 .opacity-100 wait 50ms remove .scale-95 .opacity-0
                on closeModal remove .scale-100 .opacity-100 wait 200ms add .scale-95 .opacity-0">
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is primarily for modal state management here.
    // HTMX handles form submissions and content updates.
</script>
{% endblock %}
```

**File: `dashboard_app/templates/dashboard_app/dashboarditem/_dashboarditem_table.html`**
```html
<div class="bg-white shadow-lg rounded-lg overflow-hidden">
    <table id="dashboarditemTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Name</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in dashboard_items %}
            <tr>
                <td class="py-3 px-6 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.name }}</td>
                <td class="py-3 px-6 text-sm text-gray-700 max-w-xs overflow-hidden text-ellipsis">{{ obj.description|default:"-" }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.value|default:"-" }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                        {% if obj.is_active and obj.value >= 0 %}bg-green-100 text-green-800
                        {% elif not obj.is_active %}bg-red-100 text-red-800
                        {% elif obj.value < 0 %}bg-orange-100 text-orange-800
                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                        {{ obj.get_display_status }}
                    </span>
                </td>
                <td class="py-3 px-6 whitespace-nowrap text-right text-sm font-medium">
                    <button 
                        class="text-indigo-600 hover:text-indigo-900 mr-4"
                        hx-get="{% url 'dashboarditem_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="text-red-600 hover:text-red-900"
                        hx-get="{% url 'dashboarditem_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 px-6 text-center text-gray-500">No dashboard items found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
$(document).ready(function() {
    $('#dashboarditemTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [5] } // Disable sorting on Actions column
        ]
    });
});
</script>
```

**File: `dashboard_app/templates/dashboard_app/dashboarditem/_dashboarditem_form.html`**
```html
<div class="p-6" x-data="{ open: true }"> {# x-data for Alpine.js if needed within form, not directly for modal toggling #}
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-4">{{ form.instance.pk|yesno:'Edit,Add' }} Dashboard Item</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600 space-y-1">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        {% if form.non_field_errors %}
        <div class="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded" role="alert">
            <p class="font-bold">Error:</p>
            <ul>
                {% for error in form.non_field_errors %}
                <li>{{ error }}</li>
                {% endfor %}
            </ul>
        </div>
        {% endif %}

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out"
                _="on click trigger closeModal"> {# Use custom event to close modal managed by parent #}
                Cancel
            </button>
            <button 
                type="submit" 
                class="inline-flex justify-center px-5 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                <span id="form-spinner" class="htmx-indicator inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                Save Item
            </button>
        </div>
    </form>
</div>
```

**File: `dashboard_app/templates/dashboard_app/dashboarditem/_dashboarditem_confirm_delete.html`**
```html
<div class="p-6 text-center">
    <svg class="mx-auto h-16 w-16 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
    </svg>
    <h3 class="mt-4 text-lg font-semibold text-gray-900">Delete Dashboard Item "{{ dashboarditem.name }}"?</h3>
    <p class="mt-2 text-sm text-gray-500">Are you sure you want to delete this dashboard item? This action cannot be undone.</p>
    
    <div class="mt-6 flex justify-center space-x-4">
        <button 
            type="button" 
            class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out"
            _="on click trigger closeModal"> {# Use custom event to close modal managed by parent #}
            Cancel
        </button>
        <form hx-post="{% url 'dashboarditem_delete' dashboarditem.pk %}" hx-swap="none" hx-indicator="#delete-spinner">
            {% csrf_token %}
            <button 
                type="submit" 
                class="inline-flex justify-center px-5 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-150 ease-in-out">
                <span id="delete-spinner" class="htmx-indicator inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                Delete
            </button>
        </form>
    </div>
</div>
```

### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
URL patterns are set up for the list view, CRUD operations, and the HTMX-specific partial table view.

**File: `dashboard_app/urls.py`**
```python
from django.urls import path
from .views import (
    DashboardItemListView, 
    DashboardItemCreateView, 
    DashboardItemUpdateView, 
    DashboardItemDeleteView,
    DashboardItemTablePartialView,
)

urlpatterns = [
    path('dashboarditem/', DashboardItemListView.as_view(), name='dashboarditem_list'),
    path('dashboarditem/table/', DashboardItemTablePartialView.as_view(), name='dashboarditem_table'), # HTMX partial
    path('dashboarditem/add/', DashboardItemCreateView.as_view(), name='dashboarditem_add'),
    path('dashboarditem/edit/<int:pk>/', DashboardItemUpdateView.as_view(), name='dashboarditem_edit'),
    path('dashboarditem/delete/<int:pk>/', DashboardItemDeleteView.as_view(), name='dashboarditem_delete'),
]
```

### 4.6 Tests

**Task:** Write tests for the model and views.

**Instructions:**
Comprehensive unit tests cover model methods and properties. Integration tests validate view behavior, including HTMX responses and success messages.

**File: `dashboard_app/tests.py`**
```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from .models import DashboardItem
from .forms import DashboardItemForm

class DashboardItemModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.item1 = DashboardItem.objects.create(
            name='Test Item 1',
            description='Description for Test Item 1',
            value=100.50,
            is_active=True
        )
        cls.item2 = DashboardItem.objects.create(
            name='Inactive Item',
            description='This item is not active',
            value=50.00,
            is_active=False
        )
        cls.item3 = DashboardItem.objects.create(
            name='Negative Value Item',
            description='An item with a negative value',
            value=-25.00,
            is_active=True
        )
  
    def test_dashboard_item_creation(self):
        obj = DashboardItem.objects.get(id=self.item1.id)
        self.assertEqual(obj.name, 'Test Item 1')
        self.assertEqual(obj.description, 'Description for Test Item 1')
        self.assertEqual(str(obj.value), '100.50')
        self.assertTrue(obj.is_active)
        
    def test_name_label(self):
        obj = DashboardItem.objects.get(id=self.item1.id)
        field_label = obj._meta.get_field('name').verbose_name
        self.assertEqual(field_label, 'Item Name')
        
    def test_str_representation(self):
        obj = DashboardItem.objects.get(id=self.item1.id)
        self.assertEqual(str(obj), 'Test Item 1')

    def test_get_display_status(self):
        self.assertEqual(self.item1.get_display_status(), 'Active')
        self.assertEqual(self.item2.get_display_status(), 'Inactive')
        self.assertEqual(self.item3.get_display_status(), 'Alert (Negative Value)')

    def test_update_item_status(self):
        self.assertTrue(self.item1.is_active)
        self.item1.update_item_status(False)
        self.item1.refresh_from_db()
        self.assertFalse(self.item1.is_active)
        self.item1.update_item_status(True) # Revert for other tests if needed
        self.item1.refresh_from_db()
        self.assertTrue(self.item1.is_active)

class DashboardItemViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.item1 = DashboardItem.objects.create(
            name='Initial Item',
            description='This is an initial item.',
            value=75.00,
            is_active=True
        )
    
    def setUp(self):
        # Set up data for each test method
        self.client = Client()
    
    def test_list_view_get(self):
        response = self.client.get(reverse('dashboarditem_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/dashboarditem/list.html')
        # Check that the main list view doesn't directly contain items, but loads partial
        self.assertNotContains(response, 'Initial Item')

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('dashboarditem_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/dashboarditem/_dashboarditem_table.html')
        self.assertTrue('dashboard_items' in response.context)
        self.assertContains(response, 'Initial Item') # Check for item in the partial view

    def test_create_view_get(self):
        response = self.client.get(reverse('dashboarditem_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/dashboarditem/_dashboarditem_form.html')
        self.assertTrue('form' in response.context)
        
    def test_create_view_post_success(self):
        data = {
            'name': 'New Dashboard Item',
            'description': 'A newly created item.',
            'value': 200.00,
            'is_active': 'on'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('dashboarditem_add'), data, **headers)
        
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertTrue(DashboardItem.objects.filter(name='New Dashboard Item').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshDashboardItemList', response.headers['HX-Trigger'])
        self.assertIn('closeModal', response.headers['HX-Trigger'])

        # Test success message (messages are handled separately in Django tests)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Dashboard Item added successfully.')
        
    def test_create_view_post_invalid(self):
        # Missing required name field
        data = {
            'description': 'Item without name.',
            'value': 10.00
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('dashboarditem_add'), data, **headers)
        
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors for HTMX
        self.assertContains(response, 'This field is required.')
        self.assertFalse(DashboardItem.objects.filter(description='Item without name.').exists())

    def test_update_view_get(self):
        obj = DashboardItem.objects.get(id=self.item1.id)
        response = self.client.get(reverse('dashboarditem_edit', args=[obj.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/dashboarditem/_dashboarditem_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, obj)
        
    def test_update_view_post_success(self):
        obj = DashboardItem.objects.get(id=self.item1.id)
        data = {
            'name': 'Updated Item Name',
            'description': 'Description has been updated.',
            'value': 123.45,
            'is_active': 'on'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('dashboarditem_edit', args=[obj.id]), data, **headers)
        
        self.assertEqual(response.status_code, 204)
        obj.refresh_from_db()
        self.assertEqual(obj.name, 'Updated Item Name')
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshDashboardItemList', response.headers['HX-Trigger'])
        self.assertIn('closeModal', response.headers['HX-Trigger'])
        
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Dashboard Item updated successfully.')

    def test_delete_view_get(self):
        obj = DashboardItem.objects.get(id=self.item1.id)
        response = self.client.get(reverse('dashboarditem_delete', args=[obj.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/dashboarditem/_dashboarditem_confirm_delete.html')
        self.assertTrue('dashboarditem' in response.context)
        self.assertEqual(response.context['dashboarditem'], obj)
        
    def test_delete_view_post_success(self):
        obj_to_delete = DashboardItem.objects.create(name='Item to Delete', value=10)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('dashboarditem_delete', args=[obj_to_delete.id]), **headers)
        
        self.assertEqual(response.status_code, 204)
        self.assertFalse(DashboardItem.objects.filter(id=obj_to_delete.id).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshDashboardItemList', response.headers['HX-Trigger'])
        self.assertIn('closeModal', response.headers['HX-Trigger'])

        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Dashboard Item deleted successfully.')

    def test_form_name_uniqueness_create(self):
        # Create an item with a name that will conflict (case-insensitive)
        DashboardItem.objects.create(name='Existing Item', value=1)
        
        data = {
            'name': 'existing item', # Case-insensitive duplicate
            'description': 'Another one',
            'value': 2
        }
        form = DashboardItemForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('name', form.errors)
        self.assertEqual(form.errors['name'], ['A dashboard item with this name already exists.'])

    def test_form_name_uniqueness_update(self):
        existing_item = DashboardItem.objects.create(name='Unique Item', value=1)
        another_item = DashboardItem.objects.create(name='Another Unique Item', value=2)

        # Attempt to rename 'existing_item' to 'another_item' (case-insensitive)
        data = {
            'name': 'another unique item',
            'description': 'Updated description',
            'value': 3,
            'is_active': 'on'
        }
        form = DashboardItemForm(data=data, instance=existing_item)
        self.assertFalse(form.is_valid())
        self.assertIn('name', form.errors)
        self.assertEqual(form.errors['name'], ['A dashboard item with this name already exists.'])

        # Update 'existing_item' with its own name (should be valid)
        data = {
            'name': 'Unique Item',
            'description': 'Updated description',
            'value': 3,
            'is_active': 'on'
        }
        form = DashboardItemForm(data=data, instance=existing_item)
        self.assertTrue(form.is_valid())
```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**
The modernization strictly adheres to HTMX for all dynamic content updates and form submissions, eliminating the need for complex custom JavaScript. Alpine.js provides simple UI state management for the modal. DataTables handles interactive table features client-side.

*   **HTMX for CRUD Modals:**
    *   **Add/Edit Buttons:** On the list page, "Add New Item" and "Edit" buttons use `hx-get` to fetch the `_dashboarditem_form.html` (or `_dashboarditem_confirm_delete.html`) into the `#modalContent` div.
    *   **Modal Activation:** The `_="on click add .is-active to #modal"` Alpine.js syntax (via `_` extension) ensures the modal becomes visible when the button is clicked.
    *   **Form Submission:** Forms within the modal use `hx-post` to submit data.
        *   `hx-swap="none"` prevents HTMX from swapping the current content on successful 204 responses.
        *   `hx-indicator` provides a visual spinner during AJAX requests.
    *   **Success Handling:** Django views return `HttpResponse(status=204, headers={'HX-Trigger': 'refreshDashboardItemList,closeModal'})` on successful form submissions (Create, Update, Delete).
        *   `refreshDashboardItemList`: A custom event that triggers the `hx-get` on the `dashboarditemTable-container` div, causing the DataTables to reload with the latest data.
        *   `closeModal`: A custom event caught by the parent modal div (`#modal`) using `hx-on:closeModal`, which uses Alpine.js (`_="on click remove .is-active from me"`) to hide the modal.
    *   **Error Handling:** If a form submission fails validation, the Django view simply returns the form with errors (status 200), and HTMX swaps the `#modalContent` with the updated form, displaying the errors to the user.

*   **DataTables for List Views:**
    *   The `_dashboarditem_table.html` partial contains the `<table>` element and the JavaScript initialization for DataTables.
    *   Since this partial is loaded dynamically by HTMX, the `$(document).ready(function() { ... });` block ensures DataTables is initialized each time the table content is re-loaded via HTMX. This guarantees that sorting, searching, and pagination features are always active on the fresh content.

*   **Alpine.js for UI State:**
    *   The `#modal` div uses `x-data="{ open: false }"` (implicitly via `_` for simple toggling, or explicitly if more complex state is needed) and `on click if event.target.id == 'modal' remove .is-active from me` to allow clicking outside the modal content to close it.
    *   Custom `closeModal` event triggered by HTMX on success or by "Cancel" button click, cleanly hides the modal.
    *   Transitions (e.g., `scale-95 opacity-0` on `modalContent` and `on load add .scale-100 .opacity-100`) add a smooth visual effect to the modal's appearance.

This approach creates a highly dynamic and responsive user experience without writing any complex traditional JavaScript, aligning perfectly with the modern Django + HTMX + Alpine.js stack.

## Final Notes

*   This modernization plan effectively transforms the conceptual "Dashboard" page into a fully functional CRUD module for "Dashboard Items" within a Django environment.
*   The use of `managed = False` in the model is crucial for integration with existing ASP.NET databases, as it tells Django not to manage the table's schema. You would run `python manage.py inspectdb` against your existing database to generate accurate initial models if they were more complex.
*   The fat model/thin view architecture ensures business logic remains encapsulated and testable, keeping view code concise and focused on request/response handling.
*   All code adheres to the DRY principle, with templates extending a base, and shared form/delete partials.
*   The comprehensive tests provide high confidence in the correctness and reliability of the migrated functionality.
*   This structured approach facilitates automated migration strategies, where tools can parse ASP.NET patterns and generate corresponding Django components and HTMX interactions.