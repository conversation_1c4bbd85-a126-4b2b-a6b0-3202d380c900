## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Based on the `SqlDataSource1` configuration and `DataKeyNames="Id"`:

-   **Table Name**: `tblWarrenty_Master`
-   **Columns**:
    -   `Id` (Int32, Primary Key)
    -   `Terms` (String, inferred from `[Terms]` in `InsertCommand` and `UpdateCommand`, and bound to `txtTerms1`/`txtTerms2`/`txtTerms3`).

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

-   **Create (Insert)**: Handled by `SqlDataSource1.InsertCommand="INSERT INTO [tblWarrenty_Master] ([Terms]) VALUES (@Terms)"`. Triggered by `GridView1_RowCommand` for "Add" (from `txtTerms2` in footer) and "Add1" (from `txtTerms3` in `EmptyDataTemplate`).
-   **Read (Select)**: Handled by `SqlDataSource1.SelectCommand="SELECT * FROM [tblWarrenty_Master] order by [Id] desc"`. This populates `GridView1`.
-   **Update**: Handled by `SqlDataSource1.UpdateCommand="UPDATE [tblWarrenty_Master] SET [Terms] = @Terms WHERE [Id] = @Id"`. There is also a custom `GridView1_RowUpdating` that performs a direct SQL update using `tblFreight_Master`; we will assume this was a typo and that `tblWarrenty_Master` is the correct table for updates, aligning with `SqlDataSource1`.
-   **Delete**: Handled by `SqlDataSource1.DeleteCommand="DELETE FROM [tblWarrenty_Master] WHERE [Id] = @Id"`. Triggered by `CommandField`.
-   **Validation**: `RequiredFieldValidator` ensures the `Terms` field is not empty on insert/update.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

-   **GridView1**: This will be replaced by a modern HTML `<table>` with `DataTables.js` for client-side functionality (searching, sorting, pagination) and HTMX for dynamic content loading.
-   **TextBox (txtTerms1, txtTerms2, txtTerms3)**: These input fields will be rendered as standard HTML `<input type="text">` elements managed by Django forms.
-   **Button (btnInsert)**: These trigger server-side actions in ASP.NET. In Django, they will be replaced by HTMX-enabled buttons that trigger partial HTML loads (e.g., for forms) or direct POST requests.
-   **Label (lblMessage)**: This is for displaying status messages. Django's built-in `messages` framework will be used, with HTMX to display these messages dynamically.
-   **JavaScript (`PopUpMsg.js`, `loadingNotifier.js`)**: These client-side scripts for confirmations and loading indicators will be replaced by HTMX attributes, Alpine.js for UI state, and potentially simpler loading animations managed by HTMX.
-   **CSS (`yui-datatable.css`, `StyleSheet.css`)**: Styling will be handled by Tailwind CSS classes, and `DataTables.js` will manage table appearance.

## Step 4: Generate Django Code

**Application Name**: `accounts` (derived from `Module_Accounts_Masters_WarrentyTerms`)
**Model Name**: `WarrentyTerm` (singularized and corrected spelling from 'Warrenty')
**Model Name Lower**: `warrentyterm`
**Model Name Plural**: `WarrentyTerms`
**Model Name Plural Lower**: `warrentyterms`
**Friendly Name**: `Warranty Term`
**Friendly Name Plural**: `Warranty Terms`

### 4.1 Models (accounts/models.py)

```python
from django.db import models

class WarrentyTerm(models.Model):
    # Id is the primary key as indicated by DataKeyNames="Id"
    # Assuming Id is an auto-incrementing integer in the original DB.
    # We define it explicitly here because managed=False
    # and it's the primary key used in the ASP.NET application.
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=500) # Assuming max_length 500 for terms

    class Meta:
        managed = False  # Tells Django not to manage table creation/deletion
        db_table = 'tblWarrenty_Master'  # Name of the existing database table
        verbose_name = 'Warranty Term'
        verbose_name_plural = 'Warranty Terms'

    def __str__(self):
        return self.terms

    # Business logic methods can be added here if needed,
    # e.g., validation beyond basic form validation,
    # or methods that transform the 'terms' data.
    def get_display_terms(self):
        # Example of a model method that might format terms
        return f"Terms: {self.terms}"
```

### 4.2 Forms (accounts/forms.py)

```python
from django import forms
from .models import WarrentyTerm

class WarrentyTermForm(forms.ModelForm):
    class Meta:
        model = WarrentyTerm
        fields = ['terms']
        widgets = {
            'terms': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter warranty terms'
            }),
        }
        
    def clean_terms(self):
        # ASP.NET had RequiredFieldValidator, Django's default ModelForm
        # handles required fields if the model field is not blank=True/null=True.
        # This is for additional custom validation.
        terms = self.cleaned_data.get('terms')
        if not terms or len(terms.strip()) == 0:
            raise forms.ValidationError("Warranty Terms cannot be empty.")
        return terms
```

### 4.3 Views (accounts/views.py)

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import render
from .models import WarrentyTerm
from .forms import WarrentyTermForm

class WarrentyTermListView(ListView):
    model = WarrentyTerm
    template_name = 'accounts/warrentyterm/list.html'
    context_object_name = 'warrentyterms' # Plural name for the list of objects

class WarrentyTermTablePartialView(ListView):
    model = WarrentyTerm
    template_name = 'accounts/warrentyterm/_warrentyterm_table.html'
    context_object_name = 'warrentyterms'
    
    def get_queryset(self):
        # Mimic 'order by [Id] desc' from original SqlDataSource
        return WarrentyTerm.objects.all().order_by('-id')

class WarrentyTermCreateView(CreateView):
    model = WarrentyTerm
    form_class = WarrentyTermForm
    template_name = 'accounts/warrentyterm/_warrentyterm_form.html'
    success_url = reverse_lazy('warrentyterm_list') # Redirect not used for HTMX, but good practice

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Warranty Term added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX, return a 204 No Content and trigger a client-side event
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWarrentyTermList'
                }
            )
        return response

    def form_invalid(self, form):
        # For HTMX, re-render the form with errors
        return render(self.request, self.template_name, {'form': form})

class WarrentyTermUpdateView(UpdateView):
    model = WarrentyTerm
    form_class = WarrentyTermForm
    template_name = 'accounts/warrentyterm/_warrentyterm_form.html'
    success_url = reverse_lazy('warrentyterm_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Warranty Term updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWarrentyTermList'
                }
            )
        return response

    def form_invalid(self, form):
        return render(self.request, self.template_name, {'form': form})

class WarrentyTermDeleteView(DeleteView):
    model = WarrentyTerm
    template_name = 'accounts/warrentyterm/_warrentyterm_confirm_delete.html'
    success_url = reverse_lazy('warrentyterm_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Warranty Term deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWarrentyTermList'
                }
            )
        return response
```

### 4.4 Templates

#### List Template (accounts/warrentyterm/list.html)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Warranty Terms</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'warrentyterm_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .block to #modal then remove .hidden from #modal">
            Add New Warranty Term
        </button>
    </div>

    <div id="warrentytermTable-container"
         hx-trigger="load, refreshWarrentyTermList from:body"
         hx-get="{% url 'warrentyterm_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg overflow-hidden">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Warranty Terms...</p>
        </div>
    </div>

    <!-- Modal for form (Add/Edit) and confirmation (Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .block from me then add .hidden to me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 my-8 relative">
            <!-- Content will be loaded by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is typically initiated globally via core/base.html.
    // If you need specific Alpine.js components tied to this page,
    // they would go here, often within an x-data="..." block.
    // However, for typical HTMX modal patterns, Alpine.js might not be explicitly needed beyond
    // the modal visibility toggle, which can also be done with plain JS or HTMX events.
    // The current modal visibility is handled by Hyperscript (`_`).
</script>
{% endblock %}
```

#### Table Partial Template (accounts/warrentyterm/_warrentyterm_table.html)

```html
<table id="warrentytermTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Terms</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in warrentyterms %}
        <tr>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.terms }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                <button
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 transition duration-300 ease-in-out"
                    hx-get="{% url 'warrentyterm_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .block to #modal then remove .hidden from #modal">
                    Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md transition duration-300 ease-in-out"
                    hx-get="{% url 'warrentyterm_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .block to #modal then remove .hidden from #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="3" class="py-4 px-4 text-center text-gray-500 text-base">No Warranty Terms found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Ensure DataTables is initialized only once and on element load.
    // HTMX ensures this script runs every time the partial is loaded.
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#warrentytermTable')) {
            $('#warrentytermTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "ordering": false // Disable default ordering, assuming original was simple list
            });
        }
    });
</script>
```

#### Form Partial Template (accounts/warrentyterm/_warrentyterm_form.html)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Warranty Term</h3>
    <form hx-post="{{ request.path }}" hx-swap="none"
          hx-on::after-request="if(event.detail.xhr.status === 204) {
                                  htmx.trigger(document.body, 'refreshWarrentyTermList');
                                  document.getElementById('modal').classList.remove('block');
                                  document.getElementById('modal').classList.add('hidden');
                                } else {
                                  // If form invalid (e.g., 422 Unprocessable Entity if custom status)
                                  // or other error, HTMX will swap this content.
                                  // For 200 OK with form errors, the entire form partial is re-swapped.
                                }">
        {% csrf_token %}

        <div class="space-y-5">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-600 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .block from #modal then add .hidden to #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                Save Warranty Term
            </button>
        </div>
    </form>
</div>
```

#### Delete Confirmation Partial Template (accounts/warrentyterm/_warrentyterm_confirm_delete.html)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Warranty Term: "<span class="font-medium text-blue-700">{{ object.terms }}</span>"?</p>
    <form hx-post="{% url 'warrentyterm_delete' object.pk %}" hx-swap="none"
          hx-on::after-request="if(event.detail.xhr.status === 204) {
                                  htmx.trigger(document.body, 'refreshWarrentyTermList');
                                  document.getElementById('modal').classList.remove('block');
                                  document.getElementById('modal').classList.add('hidden');
                                }">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .block from #modal then add .hidden to #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs (accounts/urls.py)

```python
from django.urls import path
from .views import (
    WarrentyTermListView,
    WarrentyTermCreateView,
    WarrentyTermUpdateView,
    WarrentyTermDeleteView,
    WarrentyTermTablePartialView
)

urlpatterns = [
    path('warrentyterms/', WarrentyTermListView.as_view(), name='warrentyterm_list'),
    path('warrentyterms/table/', WarrentyTermTablePartialView.as_view(), name='warrentyterm_table'),
    path('warrentyterms/add/', WarrentyTermCreateView.as_view(), name='warrentyterm_add'),
    path('warrentyterms/edit/<int:pk>/', WarrentyTermUpdateView.as_view(), name='warrentyterm_edit'),
    path('warrentyterms/delete/<int:pk>/', WarrentyTermDeleteView.as_view(), name='warrentyterm_delete'),
]
```

### 4.6 Tests (accounts/tests.py)

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import WarrentyTerm
from django.contrib.messages import get_messages

class WarrentyTermModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        WarrentyTerm.objects.create(id=1, terms='Standard Warranty')
        WarrentyTerm.objects.create(id=2, terms='Extended Warranty')

    def test_warrentyterm_creation(self):
        term1 = WarrentyTerm.objects.get(id=1)
        term2 = WarrentyTerm.objects.get(id=2)
        self.assertEqual(term1.terms, 'Standard Warranty')
        self.assertEqual(term2.terms, 'Extended Warranty')
        self.assertEqual(str(term1), 'Standard Warranty')

    def test_terms_label(self):
        term = WarrentyTerm.objects.get(id=1)
        field_label = term._meta.get_field('terms').verbose_name
        self.assertEqual(field_label, 'terms') # Django's default verbose_name for CharField is the field name

    def test_get_display_terms_method(self):
        term = WarrentyTerm.objects.get(id=1)
        self.assertEqual(term.get_display_terms(), 'Terms: Standard Warranty')

class WarrentyTermViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create initial test data for views
        WarrentyTerm.objects.create(id=1, terms='Base Warranty Term')
        WarrentyTerm.objects.create(id=2, terms='Advanced Warranty Term')

    def setUp(self):
        self.client = Client()

    def test_list_view_get(self):
        response = self.client.get(reverse('warrentyterm_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/warrentyterm/list.html')
        self.assertIn('warrentyterms', response.context)
        self.assertEqual(len(response.context['warrentyterms']), 2)

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('warrentyterm_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/warrentyterm/_warrentyterm_table.html')
        self.assertIn('warrentyterms', response.context)
        self.assertEqual(len(response.context['warrentyterms']), 2)
        # Ensure ordering is by id desc as in original SQL
        self.assertEqual(list(response.context['warrentyterms']), list(WarrentyTerm.objects.all().order_by('-id')))

    def test_create_view_get(self):
        response = self.client.get(reverse('warrentyterm_add'), HTTP_HX_REQUEST='true') # Simulate HTMX request
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/warrentyterm/_warrentyterm_form.html')
        self.assertIn('form', response.context)

    def test_create_view_post_valid(self):
        data = {'terms': 'New Product Warranty'}
        response = self.client.post(reverse('warrentyterm_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content for successful HTMX post
        self.assertTrue(WarrentyTerm.objects.filter(terms='New Product Warranty').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWarrentyTermList')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Warranty Term added successfully.')

    def test_create_view_post_invalid(self):
        data = {'terms': ''} # Invalid data
        response = self.client.post(reverse('warrentyterm_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Renders form with errors for HTMX
        self.assertTemplateUsed(response, 'accounts/warrentyterm/_warrentyterm_form.html')
        self.assertIn('form', response.context)
        self.assertFalse(response.context['form'].is_valid())
        self.assertIn('Warranty Terms cannot be empty.', response.context['form'].errors['terms'])
        self.assertFalse(WarrentyTerm.objects.filter(terms='').exists())

    def test_update_view_get(self):
        obj = WarrentyTerm.objects.get(id=1)
        response = self.client.get(reverse('warrentyterm_edit', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/warrentyterm/_warrentyterm_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance.terms, 'Base Warranty Term')

    def test_update_view_post_valid(self):
        obj = WarrentyTerm.objects.get(id=1)
        data = {'terms': 'Updated Warranty Term'}
        response = self.client.post(reverse('warrentyterm_edit', args=[obj.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        obj.refresh_from_db()
        self.assertEqual(obj.terms, 'Updated Warranty Term')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWarrentyTermList')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Warranty Term updated successfully.')

    def test_update_view_post_invalid(self):
        obj = WarrentyTerm.objects.get(id=1)
        data = {'terms': ''}
        response = self.client.post(reverse('warrentyterm_edit', args=[obj.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/warrentyterm/_warrentyterm_form.html')
        self.assertFalse(response.context['form'].is_valid())
        self.assertIn('Warranty Terms cannot be empty.', response.context['form'].errors['terms'])

    def test_delete_view_get(self):
        obj = WarrentyTerm.objects.get(id=1)
        response = self.client.get(reverse('warrentyterm_delete', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/warrentyterm/_warrentyterm_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'].terms, 'Base Warranty Term')

    def test_delete_view_post(self):
        obj_to_delete = WarrentyTerm.objects.get(id=1)
        response = self.client.post(reverse('warrentyterm_delete', args=[obj_to_delete.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(WarrentyTerm.objects.filter(id=1).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWarrentyTermList')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Warranty Term deleted successfully.')

    def test_delete_non_existent_object(self):
        response = self.client.post(reverse('warrentyterm_delete', args=[999]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 404) # Object not found
```

## Step 5: HTMX and Alpine.js Integration

### Instructions:

-   **HTMX for dynamic updates**: All CRUD operations (Add, Edit, Delete) are initiated via HTMX `hx-get` to load forms/confirmation modals into `#modalContent`. Form submissions (`hx-post`) return `204 No Content` and trigger a `refreshWarrentyTermList` event on the body to reload the main table via `hx-get` to `{% url 'warrentyterm_table' %}`. This ensures the table is always up-to-date without full page refreshes.
-   **Alpine.js for UI state management**: While the provided example uses Hyperscript (`_`) for simple modal toggling, Alpine.js can provide more complex UI state management. For this scenario, Hyperscript is sufficient for modal visibility.
-   **DataTables for list views**: The `_warrentyterm_table.html` partial explicitly initializes DataTables on the `warrentytermTable` element. This ensures that every time the table is updated via HTMX, DataTables is correctly applied, providing client-side searching, sorting, and pagination.
-   **No full page reloads**: All interactions are designed to swap only the necessary parts of the DOM, adhering to the single-page application feel.
-   **`HX-Trigger` for list refreshes**: After any successful create, update, or delete operation, the backend returns a `204 No Content` response with the `HX-Trigger: refreshWarrentyTermList` header. The main `list.html`'s `warrentytermTable-container` listens for this event (`hx-trigger="load, refreshWarrentyTermList from:body"`) to re-fetch and re-render the table, ensuring data consistency.

## Final Notes

-   The `id` field in the `WarrentyTerm` model was explicitly defined as `IntegerField(db_column='Id', primary_key=True)` because `DataKeyNames="Id"` implies `Id` is the primary key in the existing database. Django's ORM typically assumes `id` is the primary key, but with `managed=False`, this explicit mapping is necessary for consistency.
-   The discrepancy in the ASP.NET code (`tblFreight_Master` vs `tblWarrenty_Master` for updates) has been resolved by consistently using `tblWarrenty_Master` based on the `SqlDataSource` configuration for all other CRUD operations.
-   The ASP.NET `RequiredFieldValidator` is naturally handled by Django's ModelForm and the `clean_terms` method in the form, which ensures the field is not empty.
-   The CSS classes used in templates are placeholders for Tailwind CSS integration. Ensure Tailwind is configured in your Django project for these styles to apply correctly.
-   This plan provides a robust, modern, and automated approach to migrating the described ASP.NET functionality to Django, leveraging best practices for performance, maintainability, and user experience.