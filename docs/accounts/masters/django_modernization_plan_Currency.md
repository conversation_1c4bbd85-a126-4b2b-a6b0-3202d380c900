## ASP.NET to Django Conversion Script: Currency Management

This document outlines the modernization plan for your ASP.NET Currency management module, transitioning it to a robust and scalable Django application. Our approach prioritizes automation, clean architecture, and modern web technologies to deliver a solution that is easier to maintain, extend, and secure.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code interacts with two primary database tables:
1.  `tblACC_Currency_Master`: This is the main table for currency details.
    *   Columns identified: `Id` (Primary Key), `Country` (Foreign Key), `Name`, `Symbol`.
2.  `tblCountry`: This table is used to populate country dropdowns.
    *   Columns identified: `CId` (Primary Key), `CountryName`.

**Extracted Schema:**
*   **Primary Table:** `tblACC_Currency_Master`
    *   `Id` (Integer, Primary Key)
    *   `Country` (Integer, Foreign Key to `tblCountry.CId`)
    *   `Name` (String, e.g., 'US Dollar')
    *   `Symbol` (String, e.g., '$')
*   **Related Table:** `tblCountry`
    *   `CId` (Integer, Primary Key)
    *   `CountryName` (String, e.g., 'United States')

### Step 2: Identify Backend Functionality

**Analysis:**
The ASP.NET code-behind file `Currency.aspx.cs` demonstrates typical CRUD (Create, Read, Update, Delete) operations and basic data display.

*   **Create (Add):**
    *   Triggered by `GridView1_RowCommand` with `CommandName="Add"` (from footer) or `CommandName="Add1"` (from `EmptyDataTemplate`).
    *   Gathers `Country` (from `DropDownList`), `Name` (from `TextBox`), and `Symbol` (from `TextBox`).
    *   Inserts new records into `tblACC_Currency_Master`.
    *   Includes basic validation (fields not empty).
*   **Read (Load/Display):**
    *   `Page_Load` calls `loadata()`.
    *   `loadata()` fetches all records from `tblACC_Currency_Master` and binds them to `GridView1`.
    *   `getCnt()` custom logic populates `Country` dropdowns within existing rows.
*   **Update (Edit):**
    *   Triggered by `GridView1_RowUpdating` after an edit button click.
    *   Updates `Country`, `Name`, and `Symbol` for a specific record in `tblACC_Currency_Master` based on its `Id`.
    *   Includes basic validation (fields not empty).
*   **Delete:**
    *   While the `GridView1_RowDeleting` event handler is empty and the `CommandField` for delete is commented out in ASPX, the `SqlDataSource1` clearly defines a `DeleteCommand`. For a complete system, we will implement this.
*   **Validation:** Client-side `RequiredFieldValidator`s ensure Name and Symbol fields are not empty before submission.

### Step 3: Infer UI Components

**Analysis:**
The ASP.NET `.aspx` file uses server-side controls to render the user interface.

*   **GridView (`GridView1`):** This is the central component for displaying currency data in a tabular format. It handles:
    *   Paging (`AllowPaging="True"`)
    *   Footer for new record insertion (`ShowFooter="True"`)
    *   In-line editing (`ShowEditButton="True"`)
    *   Data binding using `SqlDataSource1` and `SqlDataSource2`.
    *   Custom styling via `CssClass="yui-datatable-theme"`, indicating a JavaScript-driven data table component.
*   **Input Controls:**
    *   `asp:TextBox`: Used for `Currency` Name and `Symbol` input in both insert (footer) and edit modes.
    *   `asp:DropDownList`: Used for selecting `Country` in insert, edit, and display modes, bound to `SqlDataSource2` (which fetches from `tblCountry`).
*   **Action Buttons:**
    *   `asp:CommandField` with `ShowEditButton="True"`: Provides "Edit" and "Update/Cancel" links.
    *   `asp:Button` (`btnInsert`): Triggers the "Add" command.
    *   Client-side JavaScript functions like `confirmationAdd()`, `confirmationUpdate()`, `confirmationDelete()` are used for confirmation pop-ups, which will be handled by HTMX/Alpine.js.

### Step 4: Generate Django Code

We will create a new Django application, e.g., `accounts_masters`, to house the Currency management module.

#### 4.1 Models (`accounts_masters/models.py`)

We'll define two models: `Country` and `Currency`, mapping directly to your existing database tables. `managed = False` is crucial as Django will not manage these tables (migrations won't apply to them).

```python
from django.db import models

class Country(models.Model):
    # CId is the primary key column in the database, mapped to 'id' in Django for convenience
    id = models.IntegerField(db_column='CId', primary_key=True, verbose_name="Country ID")
    name = models.CharField(db_column='CountryName', max_length=255, verbose_name="Country Name")

    class Meta:
        managed = False  # Django will not manage this table's schema
        db_table = 'tblCountry'  # Name of the existing database table
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'
        ordering = ['name'] # Default ordering for dropdowns

    def __str__(self):
        return self.name

class Currency(models.Model):
    # Id is the primary key column in the database, mapped to 'id' in Django
    id = models.IntegerField(db_column='Id', primary_key=True, verbose_name="Currency ID")
    # Foreign key to the Country model, linking to the 'CId' column in tblCountry
    country = models.ForeignKey(Country, on_delete=models.CASCADE, db_column='Country', verbose_name="Country")
    name = models.CharField(db_column='Name', max_length=255, verbose_name="Currency Name")
    symbol = models.CharField(db_column='Symbol', max_length=50, verbose_name="Symbol")

    class Meta:
        managed = False  # Django will not manage this table's schema
        db_table = 'tblACC_Currency_Master'  # Name of the existing database table
        verbose_name = 'Currency'
        verbose_name_plural = 'Currencies'
        ordering = ['-id'] # Order by ID descending, mirroring ASP.NET's ORDER BY Id DESC

    def __str__(self):
        return f"{self.name} ({self.symbol})"

    # No complex business logic was found in the ASP.NET code-behind,
    # so we keep model methods simple. If business rules emerge, they
    # would be placed here.
```

#### 4.2 Forms (`accounts_masters/forms.py`)

A Django `ModelForm` will handle data validation and rendering for `Currency` objects.

```python
from django import forms
from .models import Currency, Country

class CurrencyForm(forms.ModelForm):
    class Meta:
        model = Currency
        fields = ['country', 'name', 'symbol']
        widgets = {
            'country': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 'placeholder': 'e.g., US Dollar'}),
            'symbol': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 'placeholder': 'e.g., $'}),
        }
        labels = {
            'country': 'Country',
            'name': 'Currency Name',
            'symbol': 'Currency Symbol',
        }

    # Custom validation (if needed, e.g., uniqueness check, although not explicitly in ASP.NET code)
    def clean_name(self):
        name = self.cleaned_data['name']
        # Example of a custom validation if needed (not from original ASP.NET)
        # if Currency.objects.filter(name__iexact=name).exists():
        #     raise forms.ValidationError("A currency with this name already exists.")
        return name
```

#### 4.3 Views (`accounts_masters/views.py`)

We'll use Django's Class-Based Views (CBVs) for clean, concise, and reusable CRUD operations, keeping them 'thin' by delegating business logic (if any) to the models. HTMX requests will receive specific `HX-Trigger` headers for dynamic updates.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import Currency
from .forms import CurrencyForm

class CurrencyListView(ListView):
    model = Currency
    template_name = 'accounts_masters/currency/list.html'
    context_object_name = 'currencies' # Plural name for the list of objects

    # No complex logic here, just renders the main page.
    # The table content is loaded via HTMX.

class CurrencyTablePartialView(TemplateView):
    """
    Renders just the table content for HTMX requests.
    """
    template_name = 'accounts_masters/currency/_currency_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['currencies'] = Currency.objects.all() # Fetch all currencies
        return context

class CurrencyCreateView(CreateView):
    model = Currency
    form_class = CurrencyForm
    template_name = 'accounts_masters/currency/_currency_form.html' # Partial template for HTMX modal
    success_url = reverse_lazy('currency_list') # Redirect after successful POST (for non-HTMX requests)

    def form_valid(self, form):
        # Business logic can be added here or within the model's save method
        response = super().form_valid(form)
        messages.success(self.request, 'Currency added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content response
            # and trigger a custom event to refresh the list on the client side.
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCurrencyList'
                }
            )
        return response # For traditional form submissions

class CurrencyUpdateView(UpdateView):
    model = Currency
    form_class = CurrencyForm
    template_name = 'accounts_masters/currency/_currency_form.html' # Partial template for HTMX modal
    success_url = reverse_lazy('currency_list') # Redirect after successful POST

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Currency updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCurrencyList'
                }
            )
        return response

class CurrencyDeleteView(DeleteView):
    model = Currency
    template_name = 'accounts_masters/currency/_currency_confirm_delete.html' # Partial template for HTMX modal
    success_url = reverse_lazy('currency_list') # Redirect after successful POST

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Currency deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCurrencyList'
                }
            )
        return response

```

#### 4.4 Templates

Templates will be organized within `accounts_masters/templates/accounts_masters/currency/`.

**`accounts_masters/templates/accounts_masters/currency/list.html`**
This is the main page that loads the DataTables content dynamically via HTMX and handles modals with Alpine.js.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Currency Management</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'currency_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then add .opacity-100 to #modal">
            Add New Currency
        </button>
    </div>
    
    <div id="currencyTable-container"
         hx-trigger="load, refreshCurrencyList from:body"
         hx-get="{% url 'currency_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg overflow-hidden">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-gray-600 text-lg">Loading currencies...</p>
        </div>
    </div>
    
    <!-- Modal for forms (Add/Edit/Delete) -->
    <div id="modal" 
         class="fixed inset-0 bg-gray-900 bg-opacity-60 hidden items-center justify-center transition-opacity duration-300 ease-in-out opacity-0"
         _="on click if event.target.id == 'modal' remove .flex from me then remove .opacity-100 from me">
        <div id="modalContent" 
             class="bg-white p-6 rounded-lg shadow-2xl max-w-2xl w-full mx-4 transform transition-transform duration-300 ease-in-out scale-95"
             _="on htmx:afterOnLoad add .scale-100 to #modalContent">
             <!-- Form/Confirm content loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('htmx:afterRequest', function(evt) {
        // Close modal on successful form submission (status 204 from views)
        if (evt.detail.xhr.status === 204) {
            document.getElementById('modal').classList.remove('flex');
            document.getElementById('modal').classList.remove('opacity-100');
            document.getElementById('modalContent').innerHTML = ''; // Clear modal content
        }
    });

    // Event listener for message display (optional, if Django messages are used)
    document.addEventListener('DOMContentLoaded', function() {
        {% if messages %}
            {% for message in messages %}
                // Example: Display success message using a simple alert or a custom toast
                // alert("{{ message }}");
                // For a more advanced toast, you'd use Alpine.js or a dedicated library
            {% endfor %}
        {% endif %}
    });
</script>
{% endblock %}
```

**`accounts_masters/templates/accounts_masters/currency/_currency_table.html`**
This partial template is loaded by HTMX to display the DataTables instance.

```html
<table id="currencyTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Country</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Currency Name</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for currency in currencies %}
        <tr>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ currency.country.name }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ currency.name }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ currency.symbol }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                <button 
                    class="text-yellow-600 hover:text-yellow-900 mr-3 p-1 rounded-md bg-yellow-100 hover:bg-yellow-200 transition duration-150 ease-in-out"
                    hx-get="{% url 'currency_edit' currency.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .flex to #modal then add .opacity-100 to #modal">
                    Edit
                </button>
                <button 
                    class="text-red-600 hover:text-red-900 p-1 rounded-md bg-red-100 hover:bg-red-200 transition duration-150 ease-in-out"
                    hx-get="{% url 'currency_delete' currency.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .flex to #modal then add .opacity-100 to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="5" class="py-4 px-6 text-center text-gray-500">No currencies found. Click "Add New Currency" to get started.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Initialize DataTables on the loaded table
    $(document).ready(function() {
        $('#currencyTable').DataTable({
            "pageLength": 10, // Default page size
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 4] } // Disable sorting for SN and Actions columns
            ]
        });
    });
</script>
```

**`accounts_masters/templates/accounts_masters/currency/_currency_form.html`**
This partial template is loaded into the modal for both adding and editing currencies.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6 border-b pb-3">{{ form.instance.pk|yesno:'Edit,Add' }} Currency</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %}
                        <span class="text-red-500">*</span>
                    {% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                    <p class="mt-1 text-xs text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="text-red-600 text-sm mt-1 list-disc pl-5">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 pt-4 border-t flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out"
                _="on click remove .flex from #modal then remove .opacity-100 from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-150 ease-in-out">
                Save Currency
            </button>
        </div>
    </form>
</div>
```

**`accounts_masters/templates/accounts_masters/currency/_currency_confirm_delete.html`**
This partial template is loaded into the modal for delete confirmation.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6 border-b pb-3">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the currency "<strong>{{ object.name }} ({{ object.symbol }})</strong>"? This action cannot be undone.
    </p>
    <form hx-post="{% url 'currency_delete' object.pk %}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        <div class="mt-8 pt-4 border-t flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out"
                _="on click remove .flex from #modal then remove .opacity-100 from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-150 ease-in-out">
                Delete Currency
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`accounts_masters/urls.py`)

Define the URL patterns for accessing the currency views.

```python
from django.urls import path
from .views import (
    CurrencyListView,
    CurrencyTablePartialView,
    CurrencyCreateView,
    CurrencyUpdateView,
    CurrencyDeleteView
)

urlpatterns = [
    # Main list view (full page)
    path('currency/', CurrencyListView.as_view(), name='currency_list'),
    
    # HTMX partial view for the table content
    path('currency/table/', CurrencyTablePartialView.as_view(), name='currency_table'),
    
    # HTMX partial views for CRUD operations via modal
    path('currency/add/', CurrencyCreateView.as_view(), name='currency_add'),
    path('currency/edit/<int:pk>/', CurrencyUpdateView.as_view(), name='currency_edit'),
    path('currency/delete/<int:pk>/', CurrencyDeleteView.as_view(), name='currency_delete'),
]

```

#### 4.6 Tests (`accounts_masters/tests.py`)

Comprehensive tests ensure the models behave as expected and views handle requests correctly, including HTMX interactions.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import Country, Currency
from .forms import CurrencyForm

class CurrencyModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a Country object first, as Currency depends on it
        cls.country1 = Country.objects.create(id=1, name='United States')
        cls.country2 = Country.objects.create(id=2, name='Eurozone')

        # Create a Currency object
        cls.currency1 = Currency.objects.create(
            id=101, 
            country=cls.country1, 
            name='US Dollar', 
            symbol='$'
        )
        cls.currency2 = Currency.objects.create(
            id=102, 
            country=cls.country2, 
            name='Euro', 
            symbol='€'
        )
  
    def test_currency_creation(self):
        """Test that a Currency object can be created with correct attributes."""
        self.assertEqual(self.currency1.name, 'US Dollar')
        self.assertEqual(self.currency1.symbol, '$')
        self.assertEqual(self.currency1.country.name, 'United States')

    def test_country_name_label(self):
        """Test the verbose name for the country field in Currency model."""
        field_label = self.currency1._meta.get_field('country').verbose_name
        self.assertEqual(field_label, 'Country')

    def test_currency_str_method(self):
        """Test the __str__ method of the Currency model."""
        expected_str = f"{self.currency1.name} ({self.currency1.symbol})"
        self.assertEqual(str(self.currency1), expected_str)
        
    def test_country_str_method(self):
        """Test the __str__ method of the Country model."""
        self.assertEqual(str(self.country1), 'United States')

class CurrencyViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.country1 = Country.objects.create(id=1, name='United States')
        cls.country2 = Country.objects.create(id=2, name='Eurozone')
        cls.currency1 = Currency.objects.create(
            id=101, 
            country=cls.country1, 
            name='US Dollar', 
            symbol='$'
        )
        cls.currency2 = Currency.objects.create(
            id=102, 
            country=cls.country2, 
            name='Euro', 
            symbol='€'
        )
    
    def setUp(self):
        # Set up a new client for each test method
        self.client = Client()
    
    def test_currency_list_view(self):
        """Test that the currency list page loads correctly."""
        response = self.client.get(reverse('currency_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_masters/currency/list.html')
        self.assertContains(response, 'Currency Management') # Check for page title

    def test_currency_table_partial_view(self):
        """Test that the HTMX table partial view loads correctly."""
        response = self.client.get(reverse('currency_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_masters/currency/_currency_table.html')
        self.assertTrue('currencies' in response.context)
        self.assertContains(response, 'US Dollar')
        self.assertContains(response, 'Euro')

    def test_currency_create_view_get(self):
        """Test that the add currency form loads correctly via GET."""
        response = self.client.get(reverse('currency_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_masters/currency/_currency_form.html')
        self.assertTrue('form' in response.context)
        self.assertIsInstance(response.context['form'], CurrencyForm)
        self.assertContains(response, 'Add Currency')

    def test_currency_create_view_post_success(self):
        """Test successful creation of a new currency via POST."""
        new_currency_data = {
            'country': self.country1.id, 
            'name': 'Canadian Dollar', 
            'symbol': 'C$'
        }
        # Simulate HTMX request by including the HX-Request header
        response = self.client.post(reverse('currency_add'), new_currency_data, HTTP_HX_REQUEST='true')
        
        # HTMX successful submission should return 204 No Content
        self.assertEqual(response.status_code, 204) 
        # Check if HX-Trigger header is present
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCurrencyList')
        
        # Verify object was created in the database
        self.assertTrue(Currency.objects.filter(name='Canadian Dollar').exists())
        # Check if success message was added
        messages = list(response.wsgi_request._messages)
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Currency added successfully.')

    def test_currency_create_view_post_invalid(self):
        """Test invalid form submission for currency creation."""
        invalid_data = {
            'country': self.country1.id, 
            'name': '',  # Name is required
            'symbol': ''
        }
        response = self.client.post(reverse('currency_add'), invalid_data)
        self.assertEqual(response.status_code, 200) # Should render the form again with errors
        self.assertTemplateUsed(response, 'accounts_masters/currency/_currency_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertFalse(Currency.objects.filter(name='').exists())

    def test_currency_update_view_get(self):
        """Test that the edit currency form loads correctly via GET."""
        response = self.client.get(reverse('currency_edit', args=[self.currency1.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_masters/currency/_currency_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance.name, 'US Dollar')
        self.assertContains(response, 'Edit Currency')

    def test_currency_update_view_post_success(self):
        """Test successful update of an existing currency via POST."""
        updated_data = {
            'country': self.country2.id, # Change country
            'name': 'USD', 
            'symbol': 'USD' # Change symbol
        }
        response = self.client.post(reverse('currency_edit', args=[self.currency1.id]), updated_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCurrencyList')
        
        # Verify currency was updated in the database
        self.currency1.refresh_from_db()
        self.assertEqual(self.currency1.name, 'USD')
        self.assertEqual(self.currency1.symbol, 'USD')
        self.assertEqual(self.currency1.country.id, self.country2.id)
        
        messages = list(response.wsgi_request._messages)
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Currency updated successfully.')

    def test_currency_delete_view_get(self):
        """Test that the delete confirmation page loads correctly via GET."""
        response = self.client.get(reverse('currency_delete', args=[self.currency1.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_masters/currency/_currency_confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'].name, 'US Dollar')
        self.assertContains(response, 'Confirm Deletion')

    def test_currency_delete_view_post_success(self):
        """Test successful deletion of a currency via POST."""
        # Ensure the currency exists before attempting to delete
        self.assertTrue(Currency.objects.filter(id=self.currency1.id).exists())

        response = self.client.post(reverse('currency_delete', args=[self.currency1.id]), HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCurrencyList')
        
        # Verify currency was deleted from the database
        self.assertFalse(Currency.objects.filter(id=self.currency1.id).exists())
        
        messages = list(response.wsgi_request._messages)
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Currency deleted successfully.')
```

### Step 5: HTMX and Alpine.js Integration

The provided Django templates (_currency_form.html, _currency_table.html, list.html, _currency_confirm_delete.html) already demonstrate the tight integration:

*   **HTMX:**
    *   **Dynamic Table Loading:** The `list.html` uses `hx-get="{% url 'currency_table' %}" hx-trigger="load, refreshCurrencyList from:body"` to load the DataTable content initially and refresh it after any CRUD operation.
    *   **Modal Form Loading:** Buttons like "Add New Currency", "Edit", and "Delete" use `hx-get` to fetch the respective form/confirmation partials (`_currency_form.html`, `_currency_confirm_delete.html`) into the `#modalContent` div.
    *   **Form Submission:** `hx-post="{{ request.path }}" hx-swap="none"` on forms ensures submissions are handled by HTMX without full page reloads. The `hx-swap="none"` combined with the `HX-Trigger` header allows the view to signal the client to refresh the table.
    *   **Partial Updates:** The `HX-Trigger: 'refreshCurrencyList'` header in `form_valid`/`delete` methods of the views tells the client to re-request the table content, ensuring the list is always up-to-date.
*   **Alpine.js:**
    *   **Modal Control:** The `#modal` div uses Alpine.js (via `_` attribute from Hyperscript, which works alongside HTMX for simple UI state management) to control its visibility. `add .flex` and `add .opacity-100` make it visible, and `remove .flex`, `remove .opacity-100` hide it. The `on click if event.target.id == 'modal'` ensures clicking outside the modal closes it.
*   **DataTables:**
    *   The `_currency_table.html` partial includes a `script` block to initialize the DataTables library on the `currencyTable` HTML element. This provides client-side sorting, searching, and pagination automatically. The necessary DataTables CDN links are assumed to be in `core/base.html`.

### Final Notes

This modernization plan provides a clear, actionable roadmap for transitioning your ASP.NET Currency module to Django. By leveraging Django's robust ORM, efficient CBVs, and modern frontend tools like HTMX, Alpine.js, and DataTables, you will achieve:

*   **Improved Maintainability:** Cleaner, more modular code with strict separation of concerns.
*   **Enhanced User Experience:** Dynamic, responsive interactions without full page reloads.
*   **Increased Development Speed:** Django's conventions and built-in features accelerate future development.
*   **Better Testability:** Comprehensive unit and integration tests ensure application stability and reliability.
*   **Scalability:** A modern architecture that can easily adapt to growing business needs.

The automated nature of this conversion process, guided by conversational AI, significantly reduces manual effort and potential errors, ensuring a smooth and efficient transition.