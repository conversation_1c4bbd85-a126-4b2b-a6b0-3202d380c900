This comprehensive modernization plan outlines the step-by-step process for transitioning your legacy ASP.NET application, specifically the "Payment Terms" module, to a modern, efficient, and scalable Django-based solution. Our approach prioritizes AI-assisted automation, ensuring a systematic conversion with minimal manual intervention.

## ASP.NET to Django Conversion Script: Payment Terms Module

This plan focuses on migrating the "Payment Terms" functionality, which involves managing payment terms (Create, Read, Update, Delete operations) currently handled by an ASP.NET GridView and SqlDataSource.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
From the `SqlDataSource1` definition in the `.aspx` file:
-   `SelectCommand="SELECT * FROM [tblPayment_Master] order by [Id] desc"`
-   `InsertCommand="INSERT INTO [tblPayment_Master] ([Terms]) VALUES (@Terms)"`
-   `UpdateCommand="UPDATE [tblPayment_Master] SET [Terms] = @Terms WHERE [Id] = @Id"`
-   `DeleteCommand="DELETE FROM [tblPayment_Master] WHERE [Id] = @Id"`

**Extracted Information:**
-   **TABLE_NAME:** `tblPayment_Master`
-   **Columns:**
    -   `Id`: Integer (Primary Key, inferred from `DataKeyNames="Id"` and `WHERE [Id] = @Id`)
    -   `Terms`: String (inferred from `[Terms]` column in insert/update, `Type="String"` for parameter)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
-   **Create:** Handled by `GridView1_RowCommand` (commands "Add" and "Add1") which triggers `SqlDataSource1.Insert()`. User input is from `txtTerms2` (footer) or `txtTerms3` (empty data template).
-   **Read:** `GridView1` is populated by `SqlDataSource1`'s `SelectCommand`. Data is displayed in `lblTerms`. Pagination is enabled.
-   **Update:** Handled by `GridView1_RowUpdating`. This uses a direct SQL `UPDATE` command (`UPDATE tblPayment_Master SET Terms =...`) rather than `SqlDataSource1.Update()`. User input is from `txtTerms1`.
-   **Delete:** Handled by `GridView1_RowDeleted` (triggered by `SqlDataSource1.Delete()`). The `SqlDataSource1` defines the `DeleteCommand`.
-   **Validation Logic:** `RequiredFieldValidator` checks for non-empty "Terms" field during insert/update.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Inferred Components:**
-   **Data Display:** `asp:GridView` is used to display a list of payment terms with pagination, editing, and deletion capabilities. This will be replaced by an HTML table rendered with DataTables.
-   **Input Fields:** `asp:TextBox` (e.g., `txtTerms1`, `txtTerms2`, `txtTerms3`) for entering/editing payment terms. These will become Django form text inputs with appropriate styling.
-   **Action Buttons:** `asp:CommandField` (Edit, Delete) and `asp:Button` (`btnInsert`) trigger CRUD actions. These will be replaced by HTMX-enabled HTML buttons.
-   **Client-Side Interactions:** `OnClientClick` attributes (`confirmationAdd()`, `confirmationUpdate()`, `confirmationDelete()`) suggest JavaScript-driven confirmation pop-ups. These will be modernized using HTMX for dynamic content loading (e.g., modals) and Alpine.js for modal state management.
-   **Messages:** `asp:Label` (`lblMessage`) for displaying success/error messages. This will be replaced by Django's `messages` framework, often displayed as toasts or in-page alerts.

---

### Step 4: Generate Django Code

We will create a new Django application, let's call it `payment_terms`, to house this module's functionality.

#### 4.1 Models (`payment_terms/models.py`)

This model will represent the `tblPayment_Master` table in your database.

```python
from django.db import models

class PaymentTerm(models.Model):
    # 'Id' is the primary key in the ASP.NET code. Django automatically creates an 'id' field as PK.
    # We will map Django's default 'id' PK to the existing 'Id' column if it's an auto-incrementing integer.
    # If the 'Id' column is NOT auto-incrementing, you might need to explicitly define it:
    # id = models.IntegerField(db_column='Id', primary_key=True)
    # For now, assuming Django's default 'id' maps correctly or will be handled by the ORM.
    # If 'Id' is truly just a regular integer column and there's another hidden PK, that would need adjustment.
    # Given typical ASP.NET GridView setup, 'Id' is almost certainly the PK.
    
    terms = models.CharField(db_column='Terms', max_length=255) # Max length is an educated guess, adjust if needed

    class Meta:
        managed = False  # Important: tells Django not to manage this table's schema
        db_table = 'tblPayment_Master'
        verbose_name = 'Payment Term'
        verbose_name_plural = 'Payment Terms'
        ordering = ['-id'] # Matches 'order by [Id] desc' from original SELECT command

    def __str__(self):
        return self.terms

    # Business logic methods can be added here.
    # For example, validation beyond simple required fields.
    def clean(self):
        # Example of model-level validation (if needed beyond form validation)
        # from django.core.exceptions import ValidationError
        # if len(self.terms) < 3:
        #     raise ValidationError({'terms': 'Term must be at least 3 characters long.'})
        pass
```

#### 4.2 Forms (`payment_terms/forms.py`)

This form will handle the input and validation for `PaymentTerm` objects.

```python
from django import forms
from .models import PaymentTerm

class PaymentTermForm(forms.ModelForm):
    class Meta:
        model = PaymentTerm
        fields = ['terms']
        widgets = {
            'terms': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter payment terms'
            }),
        }
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Custom validation, e.g., unique terms (if applicable)
        # self.fields['terms'].validators.append(self.validate_unique_term)

    # def validate_unique_term(self, value):
    #     if PaymentTerm.objects.filter(terms=value).exclude(pk=self.instance.pk if self.instance else None).exists():
    #         raise forms.ValidationError("This payment term already exists.")

    # Custom validation methods if needed
    # def clean_terms(self):
    #     terms = self.cleaned_data['terms']
    #     if len(terms) < 5: # Example: ensure terms are at least 5 characters
    #         raise forms.ValidationError("Payment term must be at least 5 characters long.")
    #     return terms
```

#### 4.3 Views (`payment_terms/views.py`)

These Class-Based Views (CBVs) will manage the CRUD operations, keeping the view logic minimal.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import PaymentTerm
from .forms import PaymentTermForm

class PaymentTermListView(ListView):
    model = PaymentTerm
    template_name = 'payment_terms/paymentterm/list.html'
    context_object_name = 'payment_terms' # Name for the list of objects in the template

class PaymentTermTablePartialView(TemplateView):
    """
    Returns the partial HTML for the DataTables table, intended for HTMX.
    """
    template_name = 'payment_terms/paymentterm/_paymentterm_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['payment_terms'] = PaymentTerm.objects.all() # Fetch all terms for the table
        return context

class PaymentTermCreateView(CreateView):
    model = PaymentTerm
    form_class = PaymentTermForm
    template_name = 'payment_terms/paymentterm/_paymentterm_form.html' # Use partial for modal
    success_url = reverse_lazy('paymentterm_list') # Redirect after non-HTMX submission

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Payment Term added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content and trigger a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPaymentTermList'
                }
            )
        return response

    def form_invalid(self, form):
        # For HTMX requests, re-render the form with errors
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


class PaymentTermUpdateView(UpdateView):
    model = PaymentTerm
    form_class = PaymentTermForm
    template_name = 'payment_terms/paymentterm/_paymentterm_form.html' # Use partial for modal
    context_object_name = 'payment_term' # Name for the single object in the template
    success_url = reverse_lazy('paymentterm_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Payment Term updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPaymentTermList'
                }
            )
        return response
    
    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


class PaymentTermDeleteView(DeleteView):
    model = PaymentTerm
    template_name = 'payment_terms/paymentterm/_paymentterm_confirm_delete.html' # Use partial for modal
    context_object_name = 'payment_term' # Name for the single object in the template
    success_url = reverse_lazy('paymentterm_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Payment Term deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPaymentTermList'
                }
            )
        return response
```

#### 4.4 Templates (`payment_terms/templates/payment_terms/paymentterm/`)

**list.html**
This is the main page that will load the table dynamically and host the modal for forms.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Payment Terms</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-200"
            hx-get="{% url 'paymentterm_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal and remove .hidden from #modal">
            Add New Payment Term
        </button>
    </div>
    
    <div id="paymenttermTable-container"
         hx-trigger="load, refreshPaymentTermList from:body"
         hx-get="{% url 'paymentterm_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg p-6">
        <!-- Initial loading state -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Payment Terms...</p>
        </div>
    </div>
    
    <!-- Modal for forms (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 hidden items-center justify-center z-50"
         _="on click if event.target.id == 'modal' remove .flex from me and add .hidden to me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-lg w-full transform transition-all sm:w-full md:max-w-xl lg:max-w-2xl"
             _="on htmx:afterSwap remove .flex from #modal and add .hidden to #modal if not event.detail.xhr.status == 422">
             <!-- 422 is for validation errors, modal should stay open -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- DataTables JS from CDN - Ensure jQuery is loaded in base.html -->
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<!-- Assuming Alpine.js is loaded in base.html -->
<script>
    // Alpine.js or custom JS specific to this page can go here if needed.
    // However, most interactions are handled by HTMX directly.
</script>
{% endblock %}

{% block extra_css %}
<!-- DataTables CSS from CDN -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/2.0.8/css/dataTables.dataTables.min.css">
{% endblock %}
```

**\_paymentterm\_table.html**
This partial template is loaded by HTMX to display the DataTables.

```html
<table id="paymenttermTable" class="min-w-full bg-white border-collapse">
    <thead>
        <tr>
            <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Terms</th>
            <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in payment_terms %}
        <tr class="hover:bg-gray-50">
            <td class="py-3 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-3 px-4 border-b border-gray-200">{{ obj.terms }}</td>
            <td class="py-3 px-4 border-b border-gray-200">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md text-sm mr-2 shadow-sm transition duration-200"
                    hx-get="{% url 'paymentterm_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .flex to #modal and remove .hidden from #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-sm shadow-sm transition duration-200"
                    hx-get="{% url 'paymentterm_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .flex to #modal and remove .hidden from #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="3" class="py-4 px-4 text-center text-gray-500">No payment terms found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Initialize DataTables after the table is loaded by HTMX
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#paymenttermTable')) {
            $('#paymenttermTable').DataTable().destroy();
        }
        $('#paymenttermTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "searching": true,
            "ordering": true,
            "paging": true,
            "info": true
        });
    });
</script>
```

**\_paymentterm\_form.html**
This partial template is used for both Create and Update operations within the modal.

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Payment Term</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-5">
        {% csrf_token %}
        
        {% for field in form %}
        <div>
            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                {{ field.label }}
                {% if field.field.required %}
                    <span class="text-red-500">*</span>
                {% endif %}
            </label>
            {{ field }}
            {% if field.help_text %}
            <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
            {% endif %}
            {% if field.errors %}
            <p class="mt-2 text-sm text-red-600">{{ field.errors }}</p>
            {% endif %}
        </div>
        {% endfor %}
        
        {% if form.non_field_errors %}
        <div class="text-red-600 text-sm mt-2">
            {% for error in form.non_field_errors %}
            <p>{{ error }}</p>
            {% endfor %}
        </div>
        {% endif %}

        <div class="mt-8 flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-5 rounded-lg shadow-md transition duration-200"
                _="on click remove .flex from #modal and add .hidden to #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-5 rounded-lg shadow-md transition duration-200">
                Save Payment Term
            </button>
        </div>
    </form>
</div>
```

**\_paymentterm\_confirm\_delete.html**
This partial template is used for the delete confirmation within the modal.

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">Confirm Deletion</h3>
    <p class="text-gray-700 mb-8">Are you sure you want to delete the payment term: "<span class="font-bold">{{ payment_term.terms }}</span>"? This action cannot be undone.</p>
    
    <div class="flex justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-5 rounded-lg shadow-md transition duration-200"
            _="on click remove .flex from #modal and add .hidden to #modal">
            Cancel
        </button>
        <button 
            hx-post="{% url 'paymentterm_delete' payment_term.pk %}"
            hx-swap="none"
            type="submit" 
            class="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-5 rounded-lg shadow-md transition duration-200">
            Delete
        </button>
    </div>
</div>
```

#### 4.5 URLs (`payment_terms/urls.py`)

These URL patterns will map web addresses to your Django views.

```python
from django.urls import path
from .views import (
    PaymentTermListView,
    PaymentTermTablePartialView,
    PaymentTermCreateView,
    PaymentTermUpdateView,
    PaymentTermDeleteView
)

urlpatterns = [
    path('payment-terms/', PaymentTermListView.as_view(), name='paymentterm_list'),
    path('payment-terms/table/', PaymentTermTablePartialView.as_view(), name='paymentterm_table'),
    path('payment-terms/add/', PaymentTermCreateView.as_view(), name='paymentterm_add'),
    path('payment-terms/edit/<int:pk>/', PaymentTermUpdateView.as_view(), name='paymentterm_edit'),
    path('payment-terms/delete/<int:pk>/', PaymentTermDeleteView.as_view(), name='paymentterm_delete'),
]
```

#### 4.6 Tests (`payment_terms/tests.py`)

Comprehensive tests ensure the model's integrity and the views' functionality, including HTMX interactions.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import PaymentTerm
from .forms import PaymentTermForm
import json

class PaymentTermModelTest(TestCase):
    """
    Tests for the PaymentTerm model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a test instance of PaymentTerm
        cls.payment_term1 = PaymentTerm.objects.create(terms='Net 30 Days')
        cls.payment_term2 = PaymentTerm.objects.create(terms='Due on Receipt')

    def test_payment_term_creation(self):
        """Test that a PaymentTerm object can be created."""
        self.assertEqual(self.payment_term1.terms, 'Net 30 Days')
        self.assertEqual(self.payment_term2.terms, 'Due on Receipt')
        self.assertTrue(PaymentTerm.objects.filter(terms='Net 30 Days').exists())
        self.assertEqual(PaymentTerm.objects.count(), 2)

    def test_terms_label(self):
        """Test the verbose name for the 'terms' field."""
        field_label = self.payment_term1._meta.get_field('terms').verbose_name
        self.assertEqual(field_label, 'terms') # Default verbose_name for CharField

    def test_str_method(self):
        """Test the __str__ method of the model."""
        self.assertEqual(str(self.payment_term1), 'Net 30 Days')

    def test_meta_options(self):
        """Test Meta options like db_table and managed."""
        self.assertEqual(PaymentTerm._meta.db_table, 'tblPayment_Master')
        self.assertFalse(PaymentTerm._meta.managed)
        self.assertEqual(PaymentTerm._meta.verbose_name, 'Payment Term')
        self.assertEqual(PaymentTerm._meta.verbose_name_plural, 'Payment Terms')
        self.assertEqual(PaymentTerm._meta.ordering, ['-id'])


class PaymentTermViewsTest(TestCase):
    """
    Integration tests for PaymentTerm views, including HTMX interactions.
    """
    @classmethod
    def setUpTestData(cls):
        # Create initial test data for all tests
        cls.payment_term = PaymentTerm.objects.create(terms='COD')
        cls.payment_term2 = PaymentTerm.objects.create(terms='Net 60 Days')

    def setUp(self):
        # Set up a new client for each test method
        self.client = Client()

    def test_list_view_get(self):
        """Test GET request to the PaymentTerm list view."""
        response = self.client.get(reverse('paymentterm_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'payment_terms/paymentterm/list.html')
        self.assertContains(response, 'Payment Terms') # Check for page title

    def test_table_partial_view_get(self):
        """Test GET request to the HTMX table partial view."""
        response = self.client.get(reverse('paymentterm_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'payment_terms/paymentterm/_paymentterm_table.html')
        self.assertContains(response, self.payment_term.terms)
        self.assertContains(response, self.payment_term2.terms)
        self.assertTrue('payment_terms' in response.context)
        self.assertEqual(len(response.context['payment_terms']), 2)

    def test_create_view_get(self):
        """Test GET request for the create form."""
        response = self.client.get(reverse('paymentterm_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'payment_terms/paymentterm/_paymentterm_form.html')
        self.assertTrue('form' in response.context)
        self.assertIsInstance(response.context['form'], PaymentTermForm)
        self.assertContains(response, 'Add Payment Term')

    def test_create_view_post_valid(self):
        """Test POST request for valid creation."""
        data = {'terms': 'Net 15 Days'}
        initial_count = PaymentTerm.objects.count()
        response = self.client.post(reverse('paymentterm_add'), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HTMX success implies 204 No Content
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshPaymentTermList')
        self.assertEqual(PaymentTerm.objects.count(), initial_count + 1)
        self.assertTrue(PaymentTerm.objects.filter(terms='Net 15 Days').exists())
        # Check messages framework
        messages = list(response.wsgi_request._messages)
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Payment Term added successfully.')

    def test_create_view_post_invalid(self):
        """Test POST request for invalid creation (empty terms)."""
        data = {'terms': ''}
        initial_count = PaymentTerm.objects.count()
        response = self.client.post(reverse('paymentterm_add'), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200) # HTMX renders form again with errors
        self.assertTemplateUsed(response, 'payment_terms/paymentterm/_paymentterm_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertEqual(PaymentTerm.objects.count(), initial_count) # No new object created

    def test_update_view_get(self):
        """Test GET request for the update form."""
        response = self.client.get(reverse('paymentterm_edit', args=[self.payment_term.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'payment_terms/paymentterm/_paymentterm_form.html')
        self.assertTrue('form' in response.context)
        self.assertIsInstance(response.context['form'], PaymentTermForm)
        self.assertEqual(response.context['form'].instance, self.payment_term)
        self.assertContains(response, 'Edit Payment Term')
        self.assertContains(response, self.payment_term.terms)

    def test_update_view_post_valid(self):
        """Test POST request for valid update."""
        new_terms = 'Net 45 Days (Updated)'
        data = {'terms': new_terms}
        response = self.client.post(reverse('paymentterm_edit', args=[self.payment_term.pk]), data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 204) # HTMX success implies 204 No Content
        self.payment_term.refresh_from_db()
        self.assertEqual(self.payment_term.terms, new_terms)
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshPaymentTermList')
        # Check messages framework
        messages = list(response.wsgi_request._messages)
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Payment Term updated successfully.')

    def test_update_view_post_invalid(self):
        """Test POST request for invalid update (empty terms)."""
        original_terms = self.payment_term.terms
        data = {'terms': ''}
        response = self.client.post(reverse('paymentterm_edit', args=[self.payment_term.pk]), data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 200) # HTMX renders form again with errors
        self.assertTemplateUsed(response, 'payment_terms/paymentterm/_paymentterm_form.html')
        self.assertContains(response, 'This field is required.')
        self.payment_term.refresh_from_db()
        self.assertEqual(self.payment_term.terms, original_terms) # Ensure no update happened

    def test_delete_view_get(self):
        """Test GET request for the delete confirmation."""
        response = self.client.get(reverse('paymentterm_delete', args=[self.payment_term.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'payment_terms/paymentterm/_paymentterm_confirm_delete.html')
        self.assertTrue('payment_term' in response.context)
        self.assertEqual(response.context['payment_term'], self.payment_term)
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, self.payment_term.terms)

    def test_delete_view_post(self):
        """Test POST request for deletion."""
        initial_count = PaymentTerm.objects.count()
        response = self.client.post(reverse('paymentterm_delete', args=[self.payment_term.pk]), HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 204) # HTMX success implies 204 No Content
        self.assertEqual(PaymentTerm.objects.count(), initial_count - 1)
        self.assertFalse(PaymentTerm.objects.filter(pk=self.payment_term.pk).exists())
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshPaymentTermList')
        # Check messages framework
        messages = list(response.wsgi_request._messages)
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Payment Term deleted successfully.')

    def test_delete_view_post_non_existent(self):
        """Test deleting a non-existent payment term."""
        non_existent_pk = 9999
        response = self.client.post(reverse('paymentterm_delete', args=[non_existent_pk]), HTTP_HX_REQUEST='true')
        # Django's DeleteView will return 404 for non-existent object
        self.assertEqual(response.status_code, 404)

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

-   **HTMX for Dynamic Interactions:**
    -   The `list.html` uses `hx-get="{% url 'paymentterm_table' %}" hx-trigger="load, refreshPaymentTermList from:body"` to load the DataTable dynamically on page load and whenever a `refreshPaymentTermList` event is triggered (after CRUD operations).
    -   "Add New", "Edit", and "Delete" buttons use `hx-get` to fetch the respective forms (`_paymentterm_form.html` or `_paymentterm_confirm_delete.html`) into the `#modalContent` div.
    -   Form submissions (`hx-post`) are configured with `hx-swap="none"` and the Django views return `204 No Content` along with `HX-Trigger: refreshPaymentTermList` headers to refresh the table without a full page reload, providing a seamless user experience.
    -   Error handling in forms will re-render the partial with validation errors.

-   **Alpine.js for UI State Management:**
    -   The modal (`#modal`) uses Alpine.js `_` (hyperscript) attributes for simple show/hide logic: `on click add .flex to #modal and remove .hidden from #modal` to open it, and `on click if event.target.id == 'modal' remove .flex from me and add .hidden to me` to close it by clicking outside.
    -   Additional logic in `_paymentterm_form.html` ensures the modal closes on successful form submission but stays open if there are validation errors (`on htmx:afterSwap remove .flex from #modal and add .hidden to #modal if not event.detail.xhr.status == 422`).

-   **DataTables for List Views:**
    -   The `_paymentterm_table.html` partial includes the necessary JavaScript to initialize DataTables on the `paymenttermTable`. This script runs every time the partial is loaded by HTMX, ensuring the table's features (searching, sorting, pagination) are correctly applied to the newly loaded data.
    -   The DataTables CSS and JS CDN links are placed in the `extra_css` and `extra_js` blocks of `list.html`, assuming jQuery is loaded in `base.html`.

**Final Notes:**

-   This plan provides a complete, runnable Django solution for the Payment Terms module, adhering to modern best practices.
-   The conversion prioritizes automation by providing structured code and clear instructions that can be followed by an AI-assisted process.
-   The use of HTMX and Alpine.js eliminates the need for complex JavaScript frameworks, simplifying frontend development and ensuring a highly dynamic user experience without traditional single-page application complexity.
-   The fat model/thin view approach keeps business logic centralized and testable, improving maintainability and scalability.
-   Remember to integrate this new `payment_terms` app into your main Django project's `settings.py` and `urls.py`.