## ASP.NET to Django Conversion Script: Payment Mode Management

This document outlines a comprehensive modernization plan for the ASP.NET Payment Mode functionality, transitioning it to a robust Django 5.0+ application. The approach prioritizes AI-assisted automation, clean architecture (Fat Model, Thin View), and modern frontend technologies (HTMX, Alpine.js, DataTables) to deliver a highly performant and maintainable solution.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

### Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET `SqlDataSource1` explicitly defines CRUD operations for the `tblACC_PaymentMode` table.

*   `SelectCommand="SELECT * FROM [tblACC_PaymentMode] order by [Id] desc"`
*   `InsertCommand="INSERT INTO [tblACC_PaymentMode] ([Terms]) VALUES (@Terms)"`
*   `UpdateCommand="UPDATE [tblACC_PaymentMode] SET [Terms] = @Terms WHERE [Id] = @Id"`
*   `DeleteCommand="DELETE FROM [tblACC_PaymentMode] WHERE [Id] = @Id"`
*   `DataKeyNames="Id"` on the `GridView1`.

**Inferred Schema:**
*   **Table Name:** `tblACC_PaymentMode`
*   **Columns:**
    *   `Id` (Primary Key, Integer)
    *   `Terms` (String)

### Step 2: Identify Backend Functionality

**Analysis:**
The ASP.NET code implements standard CRUD operations via the `SqlDataSource` and `GridView` events.

*   **Create:** Triggered by `btnInsert` (CommandName="Add" in footer, or "Add1" in `EmptyDataTemplate`). Both insert a new record using the `Terms` value.
*   **Read:** Data is retrieved via `SqlDataSource1.SelectCommand` and bound to `GridView1`.
*   **Update:** Triggered by `CommandField`'s `ShowEditButton`. The `GridView1_RowUpdating` event handles the update, manually constructing and executing an `UPDATE` SQL command.
*   **Delete:** Triggered by `CommandField`'s `ShowDeleteButton`. The `GridView1_RowDeleted` event confirms the action, and `SqlDataSource1.DeleteCommand` performs the actual deletion.
*   **Validation:** `RequiredFieldValidator` enforces that the `Terms` field cannot be empty during insert or update.
*   **Messaging:** `lblMessage` is used to display success messages ("Record Updated", "Record Deleted", "Record Inserted").

### Step 3: Infer UI Components

**Analysis:**
The ASP.NET UI primarily uses the `GridView` for data display and inline CRUD operations.

*   **Main Display:** `asp:GridView` will be replaced by a Django template rendering a `<table>` managed by DataTables.
*   **Input Fields:** `asp:TextBox` (e.g., `txtTerms1`, `txtTerms2`, `txtTerms3`) will map to a single `forms.CharField` in Django.
*   **Action Buttons:** `asp:CommandField` (Edit, Delete) and `asp:Button` (Insert) will be replaced by standard HTML buttons/links leveraging HTMX for dynamic interactions (e.g., opening modals for forms).
*   **Validation Display:** `asp:RequiredFieldValidator` will be handled by Django form validation and displayed inline.
*   **Pop-up Messages:** `lblMessage` will be handled by Django's messages framework, displayed via HTMX triggers.
*   **Client-side interactivity:** `OnClientClick` attributes (`confirmationAdd()`, `confirmationUpdate()`, `confirmationDelete()`) will be replaced by HTMX and Alpine.js for a more modern, efficient approach without custom JS.

---

### Step 4: Generate Django Code

The Django application will be named `accounts_masters` to align with the ASP.NET module structure.

#### 4.1 Models (accounts_masters/models.py)

```python
from django.db import models

class PaymentMode(models.Model):
    """
    Represents a payment mode in the system, mapping to tblACC_PaymentMode.
    """
    # AutoField for Id is handled by Django's default 'id' primary key
    # if it's an auto-incrementing integer. If the existing Id is a non-auto PK,
    # it would be defined explicitly. Assuming auto-incrementing integer PK.
    terms = models.CharField(db_column='Terms', max_length=255, verbose_name="Payment Terms") # Max length inferred from typical string fields, adjustable

    class Meta:
        managed = False  # Tells Django not to manage table creation/deletion
        db_table = 'tblACC_PaymentMode'
        verbose_name = 'Payment Mode'
        verbose_name_plural = 'Payment Modes'
        ordering = ['-pk'] # Order by Id desc, matching ASP.NET's SELECT * ORDER BY [Id] desc

    def __str__(self):
        return self.terms

    def save(self, *args, **kwargs):
        """
        Custom save method for any pre-save business logic (Fat Model).
        """
        # Example: Ensure terms are always capitalized if needed
        # self.terms = self.terms.upper()
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        """
        Custom delete method for any pre-delete business logic (Fat Model).
        """
        # Example: Check for dependencies before deleting
        # if self.related_objects.exists():
        #     raise models.ProtectedError("Cannot delete Payment Mode with associated records.", self)
        super().delete(*args, **kwargs)

    # Example of a business logic method that could be added:
    def get_display_terms(self):
        """
        Returns a formatted string of the payment terms.
        """
        return f"Mode: {self.terms}"

```

#### 4.2 Forms (accounts_masters/forms.py)

```python
from django import forms
from .models import PaymentMode

class PaymentModeForm(forms.ModelForm):
    """
    Form for creating and updating PaymentMode objects.
    """
    class Meta:
        model = PaymentMode
        fields = ['terms']
        widgets = {
            'terms': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter payment terms'
            }),
        }
        
    def clean_terms(self):
        """
        Custom validation for the terms field.
        Ensures terms are unique (case-insensitive for better UX).
        """
        terms = self.cleaned_data['terms']
        # Check if an instance with these terms (case-insensitive) already exists
        # Exclude the current instance for updates
        qs = PaymentMode.objects.filter(terms__iexact=terms)
        if self.instance.pk: # If updating an existing object
            qs = qs.exclude(pk=self.instance.pk)
        if qs.exists():
            raise forms.ValidationError("A payment mode with these terms already exists.")
        return terms

```

#### 4.3 Views (accounts_masters/views.py)

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import PaymentMode
from .forms import PaymentModeForm
import json # Used for HTMX trigger details

class PaymentModeListView(ListView):
    """
    Displays a list of all Payment Modes.
    """
    model = PaymentMode
    template_name = 'accounts_masters/paymentmode/list.html'
    context_object_name = 'paymentmodes'

    # Views are kept thin; business logic is in models or forms.
    # No custom methods here, as data retrieval is standard.

class PaymentModeTablePartialView(ListView):
    """
    Renders only the table portion of Payment Modes for HTMX requests.
    """
    model = PaymentMode
    template_name = 'accounts_masters/paymentmode/_paymentmode_table.html'
    context_object_name = 'paymentmodes'

    # This view is purely for HTMX swaps, so it just returns the table.

class PaymentModeCreateView(CreateView):
    """
    Handles creation of new Payment Modes.
    """
    model = PaymentMode
    form_class = PaymentModeForm
    template_name = 'accounts_masters/paymentmode/_paymentmode_form.html' # Use partial for modal
    success_url = reverse_lazy('paymentmode_list') # Fallback, HTMX usually handles redirection

    def form_valid(self, form):
        # Business logic can be offloaded to model's save method
        response = super().form_valid(form)
        messages.success(self.request, 'Payment Mode added successfully.')

        # HTMX-specific response: Send 204 No Content and trigger refresh on client
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content to indicate success without full page reload
                headers={
                    # Custom event to trigger DataTables reload and modal close
                    'HX-Trigger': json.dumps({
                        'refreshPaymentModeList': None,
                        'closeModal': None, # Trigger for Alpine.js to close modal
                        'showMessage': {'level': 'success', 'message': 'Payment Mode added successfully.'}
                    })
                }
            )
        return response

    def form_invalid(self, form):
        # If form is invalid, re-render the form within the modal
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response # HTMX will swap the form content with errors
        return response


class PaymentModeUpdateView(UpdateView):
    """
    Handles updating existing Payment Modes.
    """
    model = PaymentMode
    form_class = PaymentModeForm
    template_name = 'accounts_masters/paymentmode/_paymentmode_form.html' # Use partial for modal
    success_url = reverse_lazy('paymentmode_list') # Fallback

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Payment Mode updated successfully.')
        
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': json.dumps({
                        'refreshPaymentModeList': None,
                        'closeModal': None,
                        'showMessage': {'level': 'success', 'message': 'Payment Mode updated successfully.'}
                    })
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response
        return response


class PaymentModeDeleteView(DeleteView):
    """
    Handles deletion of Payment Modes.
    """
    model = PaymentMode
    template_name = 'accounts_masters/paymentmode/_paymentmode_confirm_delete.html' # Use partial for modal
    success_url = reverse_lazy('paymentmode_list') # Fallback

    def delete(self, request, *args, **kwargs):
        # Any pre-delete business logic should be in the model's delete method.
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Payment Mode deleted successfully.')
        
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': json.dumps({
                        'refreshPaymentModeList': None,
                        'closeModal': None,
                        'showMessage': {'level': 'success', 'message': 'Payment Mode deleted successfully.'}
                    })
                }
            )
        return response

```

#### 4.4 Templates (accounts_masters/templates/accounts_masters/paymentmode/)

##### list.html

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Payment Modes</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'paymentmode_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fa-solid fa-plus mr-2"></i> Add New Payment Mode
        </button>
    </div>

    {# Success/Error Message Display Area #}
    <div id="message-container" class="mb-4">
        {% if messages %}
            {% for message in messages %}
                <div class="{% if message.tags == 'success' %}bg-green-100 border border-green-400 text-green-700{% elif message.tags == 'error' %}bg-red-100 border border-red-400 text-red-700{% elif message.tags == 'warning' %}bg-yellow-100 border border-yellow-400 text-yellow-700{% else %}bg-blue-100 border border-blue-400 text-blue-700{% endif %} px-4 py-3 rounded relative" role="alert">
                    <strong class="font-bold">{% if message.tags == 'success' %}Success!{% elif message.tags == 'error' %}Error!{% elif message.tags == 'warning' %}Warning!{% else %}Info!{% endif %}</strong>
                    <span class="block sm:inline">{{ message }}</span>
                    <span class="absolute top-0 bottom-0 right-0 px-4 py-3" onclick="this.parentElement.style.display='none';">
                        <svg class="fill-current h-6 w-6 text-current" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
                    </span>
                </div>
            {% endfor %}
        {% endif %}
    </div>

    <div id="paymentmodeTable-container"
         hx-trigger="load, refreshPaymentModeList from:body"
         hx-get="{% url 'paymentmode_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg p-6">
        {# Loading indicator for HTMX content #}
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Payment Modes...</p>
        </div>
    </div>

    {# Modal for form/delete confirmation #}
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden"
         x-data="{ showModal: false }"
         x-show="showModal"
         x-init="document.body.addEventListener('closeModal', () => showModal = false)"
         _="on click if event.target.id == 'modal' remove .is-active from me then set showModal to false">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 lg:mx-0"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# Alpine.js initialization handled by base.html or global setup. #}
<script>
    // Listener for messages from HX-Trigger
    document.body.addEventListener('showMessage', function(evt) {
        const detail = evt.detail;
        let alertClass = '';
        if (detail.level === 'success') {
            alertClass = 'bg-green-100 border border-green-400 text-green-700';
        } else if (detail.level === 'error') {
            alertClass = 'bg-red-100 border border-red-400 text-red-700';
        } else if (detail.level === 'warning') {
            alertClass = 'bg-yellow-100 border border-yellow-400 text-yellow-700';
        } else {
            alertClass = 'bg-blue-100 border border-blue-400 text-blue-700';
        }

        const messageHtml = `
            <div class="${alertClass} px-4 py-3 rounded relative mb-4" role="alert">
                <strong class="font-bold">${detail.level.charAt(0).toUpperCase() + detail.level.slice(1)}!</strong>
                <span class="block sm:inline"> ${detail.message}</span>
                <span class="absolute top-0 bottom-0 right-0 px-4 py-3 cursor-pointer" onclick="this.parentElement.remove();">
                    <svg class="fill-current h-6 w-6 text-current" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
                </span>
            </div>`;
        document.getElementById('message-container').insertAdjacentHTML('beforeend', messageHtml);
        
        // Optionally, make messages disappear after a few seconds
        setTimeout(() => {
            const addedMessage = document.getElementById('message-container').lastElementChild;
            if (addedMessage) {
                addedMessage.remove();
            }
        }, 5000);
    });

    // Event listener for showing the modal on HTMX request completion
    document.body.addEventListener('htmx:afterOnLoad', function(event) {
        if (event.detail.target.id === 'modalContent') {
            document.getElementById('modal').classList.add('is-active'); // Add the 'is-active' class to show modal
            document.getElementById('modal').setAttribute('x-show', 'true'); // Set Alpine.js state
            document.getElementById('modal').__alpine_data.showModal = true;
        }
    });
</script>
{% endblock %}
```

##### _paymentmode_table.html (Partial for HTMX)

```html
<table id="paymentmodeTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment Terms</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in paymentmodes %}
        <tr>
            <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.terms }}</td>
            <td class="py-3 px-6 whitespace-nowrap text-sm font-medium">
                <button
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 shadow-sm transition duration-300 ease-in-out"
                    hx-get="{% url 'paymentmode_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <i class="fa-solid fa-edit"></i> Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md shadow-sm transition duration-300 ease-in-out"
                    hx-get="{% url 'paymentmode_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <i class="fa-solid fa-trash-alt"></i> Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="3" class="text-center py-4 text-gray-500">No payment modes found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Re-initialize DataTable every time the table is loaded via HTMX
    $(document).ready(function() {
        $('#paymentmodeTable').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true
        });
    });
</script>
```

##### _paymentmode_form.html (Partial for HTMX)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Payment Mode</h3>
    <form hx-post="{{ request.path }}" hx-swap="outerHTML"> {# hx-swap outerHTML to replace the form itself with success/error messages #}
        {% csrf_token %}

        <div class="space-y-5">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        {% if form.non_field_errors %}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
            <strong class="font-bold">Error!</strong>
            <ul class="mt-1 text-sm">
                {% for error in form.non_field_errors %}
                <li>{{ error }}</li>
                {% endfor %}
            </ul>
        </div>
        {% endif %}

        <div class="mt-8 flex justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-300 ease-in-out"
                onclick="document.getElementById('modal').__alpine_data.showModal = false; document.getElementById('modal').classList.remove('is-active');">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                <i class="fa-solid fa-save mr-2"></i> Save
            </button>
        </div>
    </form>
</div>
```

##### _paymentmode_confirm_delete.html (Partial for HTMX)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">Confirm Deletion</h3>
    <p class="text-gray-700 mb-8">Are you sure you want to delete the payment mode "<strong>{{ object.terms }}</strong>"? This action cannot be undone.</p>

    <div class="mt-8 flex justify-end space-x-4">
        <button
            type="button"
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-300 ease-in-out"
            onclick="document.getElementById('modal').__alpine_data.showModal = false; document.getElementById('modal').classList.remove('is-active');">
            Cancel
        </button>
        <button
            hx-post="{% url 'paymentmode_delete' object.pk %}"
            hx-swap="none" {# We want HX-Trigger response, not content swap #}
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
            <i class="fa-solid fa-trash-alt mr-2"></i> Confirm Delete
        </button>
    </div>
</div>
```

#### 4.5 URLs (accounts_masters/urls.py)

```python
from django.urls import path
from .views import (
    PaymentModeListView,
    PaymentModeTablePartialView,
    PaymentModeCreateView,
    PaymentModeUpdateView,
    PaymentModeDeleteView
)

urlpatterns = [
    path('paymentmodes/', PaymentModeListView.as_view(), name='paymentmode_list'),
    path('paymentmodes/table/', PaymentModeTablePartialView.as_view(), name='paymentmode_table'), # For HTMX refresh
    path('paymentmodes/add/', PaymentModeCreateView.as_view(), name='paymentmode_add'),
    path('paymentmodes/<int:pk>/edit/', PaymentModeUpdateView.as_view(), name='paymentmode_edit'),
    path('paymentmodes/<int:pk>/delete/', PaymentModeDeleteView.as_view(), name='paymentmode_delete'),
]

```

#### 4.6 Tests (accounts_masters/tests.py)

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import PaymentMode
from .forms import PaymentModeForm
import json

class PaymentModeModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.payment_mode_1 = PaymentMode.objects.create(terms='Cash Payment')
        cls.payment_mode_2 = PaymentMode.objects.create(terms='Credit Card')

    def test_payment_mode_creation(self):
        """Test that a PaymentMode object is created correctly."""
        payment_mode = PaymentMode.objects.get(pk=self.payment_mode_1.pk)
        self.assertEqual(payment_mode.terms, 'Cash Payment')
        self.assertEqual(PaymentMode.objects.count(), 2)

    def test_terms_label(self):
        """Test the verbose name for the 'terms' field."""
        payment_mode = PaymentMode.objects.get(pk=self.payment_mode_1.pk)
        field_label = payment_mode._meta.get_field('terms').verbose_name
        self.assertEqual(field_label, 'Payment Terms')

    def test_object_name_is_terms(self):
        """Test the __str__ method of the PaymentMode model."""
        payment_mode = PaymentMode.objects.get(pk=self.payment_mode_1.pk)
        self.assertEqual(str(payment_mode), payment_mode.terms)

    def test_model_meta_options(self):
        """Test Meta options like db_table and managed."""
        self.assertEqual(PaymentMode._meta.db_table, 'tblACC_PaymentMode')
        self.assertFalse(PaymentMode._meta.managed)
        self.assertEqual(PaymentMode._meta.verbose_name, 'Payment Mode')
        self.assertEqual(PaymentMode._meta.verbose_name_plural, 'Payment Modes')
        self.assertEqual(PaymentMode._meta.ordering, ['-pk'])

    def test_unique_terms_validation(self):
        """Test that terms are unique (case-insensitive) via form validation."""
        form_data = {'terms': 'Cash Payment'} # Duplicate
        form = PaymentModeForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('terms', form.errors)
        self.assertIn('A payment mode with these terms already exists.', form.errors['terms'])

        form_data_case_insensitive = {'terms': 'cash payment'} # Duplicate, case-insensitive
        form = PaymentModeForm(data=form_data_case_insensitive)
        self.assertFalse(form.is_valid())
        self.assertIn('terms', form.errors)

        form_data_new = {'terms': 'New Mode'}
        form = PaymentModeForm(data=form_data_new)
        self.assertTrue(form.is_valid())

class PaymentModeViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a test payment mode for use across view tests
        cls.payment_mode = PaymentMode.objects.create(terms='Online Transfer')

    def setUp(self):
        self.client = Client()

    def test_list_view_get(self):
        """Test that the list view loads correctly and contains objects."""
        response = self.client.get(reverse('paymentmode_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_masters/paymentmode/list.html')
        self.assertContains(response, 'Online Transfer')
        self.assertContains(response, 'Add New Payment Mode')

    def test_table_partial_view_get(self):
        """Test that the table partial view loads correctly for HTMX."""
        response = self.client.get(reverse('paymentmode_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_masters/paymentmode/_paymentmode_table.html')
        self.assertContains(response, '<table id="paymentmodeTable"')
        self.assertContains(response, 'Online Transfer')

    def test_create_view_get(self):
        """Test that the create form loads correctly."""
        response = self.client.get(reverse('paymentmode_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_masters/paymentmode/_paymentmode_form.html')
        self.assertContains(response, 'Add Payment Mode')
        self.assertIsInstance(response.context['form'], PaymentModeForm)

    def test_create_view_post_success(self):
        """Test successful creation via POST request (HTMX)."""
        data = {'terms': 'Debit Card'}
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('paymentmode_add'), data, **headers)

        self.assertEqual(response.status_code, 204) # HTMX 204 No Content
        self.assertTrue(PaymentMode.objects.filter(terms='Debit Card').exists())
        # Check HX-Trigger header
        hx_trigger = json.loads(response.headers['HX-Trigger'])
        self.assertIn('refreshPaymentModeList', hx_trigger)
        self.assertIn('closeModal', hx_trigger)
        self.assertIn('showMessage', hx_trigger)
        self.assertEqual(hx_trigger['showMessage']['level'], 'success')
        self.assertEqual(hx_trigger['showMessage']['message'], 'Payment Mode added successfully.')

    def test_create_view_post_invalid(self):
        """Test invalid creation via POST request (HTMX)."""
        data = {'terms': ''} # Invalid, required field
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('paymentmode_add'), data, **headers)

        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'accounts_masters/paymentmode/_paymentmode_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertFalse(PaymentMode.objects.filter(terms='').exists())


    def test_update_view_get(self):
        """Test that the update form loads correctly with existing data."""
        response = self.client.get(reverse('paymentmode_edit', args=[self.payment_mode.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_masters/paymentmode/_paymentmode_form.html')
        self.assertContains(response, 'Edit Payment Mode')
        self.assertContains(response, 'value="Online Transfer"')

    def test_update_view_post_success(self):
        """Test successful update via POST request (HTMX)."""
        data = {'terms': 'Bank Transfer Updated'}
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('paymentmode_edit', args=[self.payment_mode.pk]), data, **headers)

        self.assertEqual(response.status_code, 204)
        self.payment_mode.refresh_from_db()
        self.assertEqual(self.payment_mode.terms, 'Bank Transfer Updated')
        hx_trigger = json.loads(response.headers['HX-Trigger'])
        self.assertIn('refreshPaymentModeList', hx_trigger)
        self.assertEqual(hx_trigger['showMessage']['message'], 'Payment Mode updated successfully.')


    def test_update_view_post_invalid(self):
        """Test invalid update via POST request (HTMX)."""
        initial_terms = self.payment_mode.terms
        data = {'terms': ''} # Invalid
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('paymentmode_edit', args=[self.payment_mode.pk]), data, **headers)

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_masters/paymentmode/_paymentmode_form.html')
        self.assertContains(response, 'This field is required.')
        self.payment_mode.refresh_from_db()
        self.assertEqual(self.payment_mode.terms, initial_terms) # Ensure not updated

    def test_delete_view_get(self):
        """Test that the delete confirmation loads correctly."""
        response = self.client.get(reverse('paymentmode_delete', args=[self.payment_mode.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_masters/paymentmode/_paymentmode_confirm_delete.html')
        self.assertContains(response, f'delete the payment mode "<strong>{self.payment_mode.terms}</strong>"')

    def test_delete_view_post_success(self):
        """Test successful deletion via POST request (HTMX)."""
        payment_mode_to_delete = PaymentMode.objects.create(terms='UPI Payment')
        initial_count = PaymentMode.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('paymentmode_delete', args=[payment_mode_to_delete.pk]), **headers)

        self.assertEqual(response.status_code, 204)
        self.assertFalse(PaymentMode.objects.filter(pk=payment_mode_to_delete.pk).exists())
        self.assertEqual(PaymentMode.objects.count(), initial_count - 1)
        hx_trigger = json.loads(response.headers['HX-Trigger'])
        self.assertIn('refreshPaymentModeList', hx_trigger)
        self.assertEqual(hx_trigger['showMessage']['message'], 'Payment Mode deleted successfully.')

```

### Step 5: HTMX and Alpine.js Integration

The provided templates (`list.html`, `_paymentmode_table.html`, `_paymentmode_form.html`, `_paymentmode_confirm_delete.html`) demonstrate the integration:

*   **HTMX:**
    *   `list.html` uses `hx-get` to load the table content dynamically from `paymentmode_table/` on `load` and on a custom `refreshPaymentModeList` event.
    *   Add/Edit/Delete buttons use `hx-get` to fetch modal content (`_paymentmode_form.html` or `_paymentmode_confirm_delete.html`) into `#modalContent`.
    *   Forms use `hx-post` for submission. `hx-swap="none"` or `hx-swap="outerHTML"` with `HX-Trigger` headers are used to prevent full page reloads and trigger client-side refreshes/messages.
    *   `HX-Trigger` is used in views to send custom events (`refreshPaymentModeList`, `closeModal`, `showMessage`) back to the client after successful CRUD operations, ensuring the UI stays updated without manual DOM manipulation.
*   **Alpine.js:**
    *   The `#modal` element in `list.html` uses `x-data="{ showModal: false }"` and `x-show="showModal"` to manage its visibility.
    *   `x-init="document.body.addEventListener('closeModal', () => showModal = false)"` listens for the `closeModal` event from HTMX to hide the modal.
    *   Buttons use `on click` directives (e.g., `_="on click add .is-active to #modal"`) to show the modal and also set the Alpine.js state for robust modal management.
    *   Cancel buttons directly manipulate Alpine.js state (`document.getElementById('modal').__alpine_data.showModal = false;`) to ensure the modal closes.
*   **DataTables:**
    *   The `_paymentmode_table.html` partial template includes a `<table>` with the ID `paymentmodeTable`.
    *   A `<script>` block within this partial ensures that `$(document).ready(function() { $('#paymentmodeTable').DataTable(); });` is called every time this partial is loaded/reloaded by HTMX, correctly initializing/re-initializing DataTables.
    *   The DataTables configuration includes `paging`, `searching`, `ordering`, `info`, `pageLength`, `lengthMenu`, and `responsive` for full functionality.

This setup ensures a highly interactive and responsive user experience, consistent with modern web application standards, while leveraging Django's robust backend capabilities.