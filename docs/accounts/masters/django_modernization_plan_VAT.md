## ASP.NET to Django Conversion Script: VAT Master Module

This plan outlines the automated conversion of your existing ASP.NET VAT Master module into a modern, efficient Django application. Our approach focuses on leveraging AI-assisted tools to streamline the migration process, minimizing manual coding and human error.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include `base.html` template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

### Conversion Steps:

#### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
From the `SqlDataSource1` element in your ASP.NET code, we've identified the primary table and its associated columns.

**Analysis:**
- **Table Name:** `tblVAT_Master`
- **Columns:**
    - `Id`: Primary Key (automatically handled by Django as `id` field).
    - `Terms`: Text field (string).
    - `Value`: Numeric field, allows decimals up to 3 places (e.g., 12.345).
    - `Live`: Integer (0 or 1), indicating a boolean state.
    - `IsVAT`: Integer (0 or 1), indicating a boolean state.
    - `IsCST`: Integer (0 or 1), indicating a boolean state.

#### Step 2: Identify Backend Functionality

**Task:** Determine the Create, Read, Update, and Delete (CRUD) operations present in the ASP.NET code.

**Analysis:**
- **Read (R):** The `SqlDataSource1`'s `SelectCommand` (`SELECT * FROM [tblVAT_Master]`) populates the `GridView1`, allowing display of all VAT entries.
- **Create (C):** Handled by `GridView1_RowCommand` (specifically `CommandName="Add"` and `CommandName="Add1"` in the `FooterTemplate` and `EmptyDataTemplate` respectively). New entries are inserted into `tblVAT_Master`.
- **Update (U):** Handled by `GridView1_RowUpdating` and `SqlDataSource1`'s `UpdateCommand`. Existing entries can be modified directly within the grid.
- **Delete (D):** Handled by `GridView1_RowDeleted` and `SqlDataSource1`'s `DeleteCommand`. Entries can be removed from the grid.

**Validation Logic:**
- **Required Fields:** `Terms` and `Value` are marked as required (`RequiredFieldValidator`).
- **Numeric Validation:** `Value` is validated as a number (up to 15 digits integer, 3 decimal places) using `RegularExpressionValidator` and a custom C# function `fun.NumberValidationQty()`.
- **Business Rule 1 (Live Status):** Only one VAT entry can be marked as "Live" at any given time. If an entry is set to Live, all other entries are automatically set to non-Live. This is a critical business rule implemented in the C# code-behind.
- **Business Rule 2 (IsVAT/IsCST Exclusivity):** The C# code explicitly checks `(((CheckBox)GridView1.FooterRow.FindControl("CKIsCST")).Checked != true || ((CheckBox)GridView1.FooterRow.FindControl("CKIsVAT")).Checked != true)`. This means an entry cannot be both `IsVAT` and `IsCST` simultaneously.

#### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles in user interaction.

**Analysis:**
- **Data Display:** `asp:GridView` is used for displaying a list of VAT entries, complete with pagination, sorting, and inline editing/deletion. This will be replaced by a modern HTML `<table>` managed by DataTables.
- **Input Fields:**
    - `asp:TextBox` controls for `Terms` (text) and `Value` (numeric).
    - `asp:CheckBox` controls for `Live`, `IsVAT`, and `IsCST` (boolean).
- **Action Buttons:** `asp:LinkButton` for Edit and Delete, and `asp:Button` for Insert. These will be replaced by standard HTML `buttons` using HTMX for dynamic interactions.
- **Client-Side Interactions:** `OnClientClick` attributes on buttons point to JavaScript functions (`confirmationAdd()`, `confirmationUpdate()`, `confirmationDelete()`). These will be managed directly by HTMX and Alpine.js without custom JavaScript files.
- **Status Message:** `asp:Label ID="lblMessage"` displays success/error messages. This will be replaced by Django's `messages` framework and HTMX's `HX-Trigger` to display feedback.

---

#### Step 4: Generate Django Code

We will create a new Django app, let's call it `accounts` (aligning with `Module_Accounts_Masters_VAT`), and define the necessary files within it.

##### 4.1 Models (`accounts/models.py`)

This model represents your `tblVAT_Master` table. The complex business rules regarding `Live` status and `IsVAT`/`IsCST` exclusivity are embedded directly within the model's `save` and `clean` methods, adhering to the "fat model" principle.

```python
from django.db import models
from django.core.exceptions import ValidationError

class VATMaster(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255) # Max length inferred, adjust as needed
    # Value allows up to 15 digits before decimal and 3 after, based on regex validation
    value = models.DecimalField(db_column='Value', max_digits=15, decimal_places=3)
    # Boolean fields map 0/1 to False/True automatically
    is_live = models.BooleanField(db_column='Live', default=False)
    is_vat = models.BooleanField(db_column='IsVAT', default=False)
    is_cst = models.BooleanField(db_column='IsCST', default=False)

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblVAT_Master'
        verbose_name = 'VAT Entry'
        verbose_name_plural = 'VAT Entries'
        # Order by Id in descending order, as seen in the original SELECT command
        ordering = ['-id'] 

    def __str__(self):
        return f"{self.terms} ({self.value}%)"

    def clean(self):
        """
        Custom validation for business rules.
        """
        super().clean()
        if self.is_vat and self.is_cst:
            raise ValidationError("A VAT entry cannot be both 'IsVAT' and 'IsCST'.")

    def save(self, *args, **kwargs):
        """
        Overrides save method to apply the 'only one Live' business rule.
        """
        self.full_clean() # Call full_clean() to run model-level validations (like clean() method)
        
        if self.is_live:
            # If this entry is set to 'Live', set all other entries to not 'Live'
            # Using update() for efficiency to avoid loading all objects into memory
            VATMaster.objects.exclude(pk=self.pk).update(is_live=False)
        
        super().save(*args, **kwargs)

    @property
    def display_live_status(self):
        """
        Returns 'Live' if is_live is True, otherwise an empty string.
        Matches the original ASP.NET display logic.
        """
        return "Live" if self.is_live else ""

```

##### 4.2 Forms (`accounts/forms.py`)

This form will handle data input and initial validation for `VATMaster` objects.

```python
from django import forms
from .models import VATMaster

class VATMasterForm(forms.ModelForm):
    class Meta:
        model = VATMaster
        fields = ['terms', 'value', 'is_live', 'is_vat', 'is_cst']
        widgets = {
            'terms': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'value': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.001'}), # Step for decimal places
            'is_live': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500'}),
            'is_vat': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500'}),
            'is_cst': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500'}),
        }
        labels = {
            'terms': 'Terms',
            'value': 'Value',
            'is_live': 'Default', # Mapping 'Live' to 'Default' as per ASP.NET HeaderText
            'is_vat': 'IsVAT',
            'is_cst': 'IsCST',
        }

    # Model's clean method (VATMaster.clean()) is automatically called by form.is_valid()
    # No need to duplicate the 'is_vat' and 'is_cst' mutual exclusivity here
    # as it's already in the model's clean method, which is called before save.
```

##### 4.3 Views (`accounts/views.py`)

These Class-Based Views (CBVs) handle the CRUD operations. Each view method is kept concise, delegating business logic to the `VATMaster` model. A separate view for the table partial is created for HTMX.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import VATMaster
from .forms import VATMasterForm

class VATMasterListView(ListView):
    """
    Displays the main VATMaster list page.
    """
    model = VATMaster
    template_name = 'accounts/vatmaster/list.html'
    context_object_name = 'vat_entries' # Renamed for clarity

class VATMasterTablePartialView(ListView):
    """
    Returns only the table content for HTMX requests.
    """
    model = VATMaster
    template_name = 'accounts/vatmaster/_vatmaster_table.html' # Partial template
    context_object_name = 'vat_entries'

class VATMasterCreateView(CreateView):
    """
    Handles creation of new VATMaster entries, typically within a modal via HTMX.
    """
    model = VATMaster
    form_class = VATMasterForm
    template_name = 'accounts/vatmaster/_vatmaster_form.html' # Partial template for modal
    success_url = reverse_lazy('vatmaster_list') # Redirect not used directly with HTMX swap="none"

    def form_valid(self, form):
        # Business logic for 'only one Live' and validation is handled by VATMaster.save() and VATMaster.clean()
        response = super().form_valid(form)
        messages.success(self.request, 'VAT Entry added successfully.')
        # HTMX-specific response: signal client to close modal and refresh list
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, indicates success without navigating
                headers={
                    'HX-Trigger': 'refreshVATMasterList' # Custom event to trigger list reload
                }
            )
        return response # Fallback for non-HTMX requests (though not expected for this pattern)

    def form_invalid(self, form):
        # Render the form again with errors for HTMX modal
        response = super().form_invalid(form)
        return response

class VATMasterUpdateView(UpdateView):
    """
    Handles updating existing VATMaster entries, typically within a modal via HTMX.
    """
    model = VATMaster
    form_class = VATMasterForm
    template_name = 'accounts/vatmaster/_vatmaster_form.html' # Partial template for modal
    context_object_name = 'vat_entry' # For access in template
    success_url = reverse_lazy('vatmaster_list')

    def form_valid(self, form):
        # Business logic for 'only one Live' and validation is handled by VATMaster.save() and VATMaster.clean()
        response = super().form_valid(form)
        messages.success(self.request, 'VAT Entry updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshVATMasterList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        return response

class VATMasterDeleteView(DeleteView):
    """
    Handles deletion of VATMaster entries, typically confirmed within a modal via HTMX.
    """
    model = VATMaster
    template_name = 'accounts/vatmaster/_vatmaster_confirm_delete.html' # Partial template for modal
    context_object_name = 'vat_entry' # For access in template
    success_url = reverse_lazy('vatmaster_list')

    def delete(self, request, *args, **kwargs):
        # Perform the delete operation
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'VAT Entry deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshVATMasterList'
                }
            )
        return response

```

##### 4.4 Templates (`accounts/vatmaster/`)

These templates define the user interface for VAT entries, utilizing HTMX for dynamic content loading and DataTables for enhanced table functionality.

**`accounts/vatmaster/list.html`**
This is the main page for displaying VAT entries. It sets up the HTMX `div` to load the actual table and includes the modal structure.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">VAT Entries</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'vatmaster_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New VAT Entry
        </button>
    </div>
    
    <div id="vatmasterTable-container"
         hx-trigger="load, refreshVATMasterList from:body"
         hx-get="{% url 'vatmaster_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading VAT Entries...</p>
        </div>
    </div>
    
    <!-- Modal for form/delete confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for UI state management.
        // For simple modal open/close via hx-trigger, it's often not strictly needed,
        // but useful for more complex UI states.
    });
    // Global event listener for messages, adapting from ASP.NET lblMessage
    document.body.addEventListener('htmx:afterRequest', function (evt) {
        if (evt.detail.xhr.status && evt.detail.xhr.status >= 200 && evt.detail.xhr.status < 300) {
            const hxTriggerHeader = evt.detail.xhr.getResponseHeader('HX-Trigger');
            if (hxTriggerHeader && hxTriggerHeader.includes('refreshVATMasterList')) {
                // If a form was submitted successfully and modal needs to close
                document.getElementById('modal').classList.remove('is-active');
            }
        }
    });
</script>
{% endblock %}
```

**`accounts/vatmaster/_vatmaster_table.html`**
This partial template contains the DataTables structure. It is dynamically loaded into `list.html` via HTMX.

```html
<div class="overflow-x-auto shadow-md sm:rounded-lg">
    <table id="vatmasterTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Terms</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Default</th>
                <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">IsVAT</th>
                <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">IsCST</th>
                <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in vat_entries %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.terms }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.value }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.display_live_status }}</td> {# Uses model property #}
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    {% if obj.is_vat %}✔{% else %}-{% endif %}
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    {% if obj.is_cst %}✔{% else %}-{% endif %}
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded text-xs mr-2"
                        hx-get="{% url 'vatmaster_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs"
                        hx-get="{% url 'vatmaster_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-4 px-4 text-center text-gray-500">No VAT entries found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // DataTables initialization (ensure jQuery and DataTables CDN are in base.html)
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#vatmasterTable')) {
            $('#vatmasterTable').DataTable().destroy(); // Destroy existing instance if loaded via HTMX
        }
        $('#vatmasterTable').DataTable({
            "pageLength": 20, // Matching original PageSize
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "responsive": true, // Ensure responsiveness
            "searching": true, // Enable search box
            "paging": true,    // Enable pagination
            "info": true       // Enable table information display
        });
    });
</script>
```

**`accounts/vatmaster/_vatmaster_form.html`**
This partial template is used for both creating and updating VAT entries within the modal.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} VAT Entry</h3>
    <form hx-post="{{ request.path }}" hx-swap="none"> {# hx-swap="none" allows HX-Trigger to control modal #}
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
            {% if form.non_field_errors %}
                <div class="text-red-500 text-xs mt-1">
                    {% for error in form.non_field_errors %}
                        <p>{{ error }}</p>
                    {% endfor %}
                </div>
            {% endif %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal"> {# Alpine.js/hyperscript for modal close #}
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`accounts/vatmaster/_vatmaster_confirm_delete.html`**
This partial template is used for confirming deletion within the modal.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the VAT entry "{{ vat_entry }}"?</p>
    
    <form hx-post="{% url 'vatmaster_delete' vat_entry.pk %}" hx-swap="none">
        {% csrf_token %}
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-4"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button 
            type="submit" 
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            Delete
        </button>
    </form>
</div>
```

##### 4.5 URLs (`accounts/urls.py`)

This file defines the URL patterns for accessing the VAT Master module.

```python
from django.urls import path
from .views import (
    VATMasterListView, 
    VATMasterCreateView, 
    VATMasterUpdateView, 
    VATMasterDeleteView,
    VATMasterTablePartialView # For HTMX partial table loads
)

urlpatterns = [
    path('vatmaster/', VATMasterListView.as_view(), name='vatmaster_list'),
    path('vatmaster/add/', VATMasterCreateView.as_view(), name='vatmaster_add'),
    path('vatmaster/edit/<int:pk>/', VATMasterUpdateView.as_view(), name='vatmaster_edit'),
    path('vatmaster/delete/<int:pk>/', VATMasterDeleteView.as_view(), name='vatmaster_delete'),
    # HTMX-specific endpoint for refreshing the table content
    path('vatmaster/table/', VATMasterTablePartialView.as_view(), name='vatmaster_table'),
]
```

##### 4.6 Tests (`accounts/tests.py`)

Comprehensive tests are crucial to ensure the migrated functionality is correct and robust. These cover both model logic and view interactions, including HTMX responses.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.core.exceptions import ValidationError
from .models import VATMaster

class VATMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create initial test data for all tests
        VATMaster.objects.create(terms='Standard VAT', value=10.000, is_live=True, is_vat=True, is_cst=False)
        VATMaster.objects.create(terms='Reduced VAT', value=5.000, is_live=False, is_vat=True, is_cst=False)
        VATMaster.objects.create(terms='CST Entry', value=2.000, is_live=False, is_vat=False, is_cst=True)

    def test_vatmaster_creation(self):
        obj = VATMaster.objects.get(terms='Standard VAT')
        self.assertEqual(obj.value, 10.000)
        self.assertTrue(obj.is_live)
        self.assertTrue(obj.is_vat)
        self.assertFalse(obj.is_cst)

    def test_terms_label(self):
        obj = VATMaster.objects.get(terms='Standard VAT')
        field_label = obj._meta.get_field('terms').verbose_name
        self.assertEqual(field_label, 'Terms') # Django default verbose_name from field name

    def test_display_live_status_property(self):
        live_obj = VATMaster.objects.get(is_live=True)
        non_live_obj = VATMaster.objects.get(terms='Reduced VAT')
        self.assertEqual(live_obj.display_live_status, 'Live')
        self.assertEqual(non_live_obj.display_live_status, '')

    def test_is_vat_and_is_cst_mutual_exclusivity(self):
        # This should raise a ValidationError during full_clean (called by save)
        with self.assertRaises(ValidationError) as cm:
            VATMaster.objects.create(terms='Invalid Entry', value=1.000, is_vat=True, is_cst=True)
        self.assertIn("A VAT entry cannot be both 'IsVAT' and 'IsCST'.", str(cm.exception))
        
        # Test if it saves when valid
        try:
            VATMaster.objects.create(terms='Valid Entry', value=1.000, is_vat=True, is_cst=False)
        except ValidationError:
            self.fail("ValidationError was raised for a valid entry.")

    def test_only_one_live_entry_on_create(self):
        # Create a new entry and set it to live
        new_live_entry = VATMaster.objects.create(terms='New Live VAT', value=12.000, is_live=True, is_vat=True)
        
        # Verify the new entry is live
        self.assertTrue(VATMaster.objects.get(pk=new_live_entry.pk).is_live)
        
        # Verify the previously live entry is now not live
        # We need to refresh from DB as it was updated by the new entry's save method
        old_live_entry = VATMaster.objects.get(terms='Standard VAT')
        old_live_entry.refresh_from_db() 
        self.assertFalse(old_live_entry.is_live)
        
        # Verify other non-live entries remain non-live
        other_entry = VATMaster.objects.get(terms='Reduced VAT')
        other_entry.refresh_from_db()
        self.assertFalse(other_entry.is_live)

    def test_only_one_live_entry_on_update(self):
        # Set an existing non-live entry to live
        entry_to_make_live = VATMaster.objects.get(terms='Reduced VAT')
        entry_to_make_live.is_live = True
        entry_to_make_live.save()

        # Verify the updated entry is now live
        self.assertTrue(VATMaster.objects.get(pk=entry_to_make_live.pk).is_live)

        # Verify the previously live entry is now not live
        old_live_entry = VATMaster.objects.get(terms='Standard VAT')
        old_live_entry.refresh_from_db()
        self.assertFalse(old_live_entry.is_live)

class VATMasterViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        VATMaster.objects.create(terms='Standard VAT', value=10.000, is_live=True, is_vat=True, is_cst=False)
        VATMaster.objects.create(terms='Reduced VAT', value=5.000, is_live=False, is_vat=True, is_cst=False)

    def setUp(self):
        self.client = Client()

    def test_list_view(self):
        response = self.client.get(reverse('vatmaster_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/vatmaster/list.html')
        self.assertIn('vat_entries', response.context) # Check context object name

    def test_table_partial_view(self):
        # Test HTMX-loaded table partial
        response = self.client.get(reverse('vatmaster_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/vatmaster/_vatmaster_table.html')
        self.assertContains(response, 'Standard VAT') # Check if data is rendered

    def test_create_view_get(self):
        response = self.client.get(reverse('vatmaster_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/vatmaster/_vatmaster_form.html')
        self.assertIn('form', response.context)

    def test_create_view_post_success(self):
        data = {
            'terms': 'New Entry',
            'value': '15.50',
            'is_live': 'off', # HTMX sends 'on' or 'off' for unchecked checkboxes
            'is_vat': 'on',
            'is_cst': 'off',
        }
        response = self.client.post(reverse('vatmaster_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success code for "no content"
        self.assertTrue(VATMaster.objects.filter(terms='New Entry').exists())
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshVATMasterList')

    def test_create_view_post_invalid(self):
        # Invalid data: both is_vat and is_cst are true
        data = {
            'terms': 'Invalid Entry',
            'value': '1.00',
            'is_live': 'off',
            'is_vat': 'on',
            'is_cst': 'on',
        }
        response = self.client.post(reverse('vatmaster_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX returns form with errors
        self.assertTemplateUsed(response, 'accounts/vatmaster/_vatmaster_form.html')
        self.assertContains(response, "A VAT entry cannot be both 'IsVAT' and 'IsCST'.")
        self.assertFalse(VATMaster.objects.filter(terms='Invalid Entry').exists())

    def test_update_view_get(self):
        obj = VATMaster.objects.get(terms='Standard VAT')
        response = self.client.get(reverse('vatmaster_edit', args=[obj.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/vatmaster/_vatmaster_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, obj)

    def test_update_view_post_success(self):
        obj = VATMaster.objects.get(terms='Standard VAT')
        data = {
            'terms': 'Updated VAT',
            'value': '11.123',
            'is_live': 'on', # Set this one to live
            'is_vat': 'on',
            'is_cst': 'off',
        }
        response = self.client.post(reverse('vatmaster_edit', args=[obj.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        obj.refresh_from_db()
        self.assertEqual(obj.terms, 'Updated VAT')
        self.assertEqual(obj.value, 11.123)
        self.assertTrue(obj.is_live)
        
        # Verify previous live entry is now false
        old_live = VATMaster.objects.get(terms='Reduced VAT') # This was not live initially
        old_live.refresh_from_db()
        self.assertFalse(old_live.is_live)

    def test_delete_view_get(self):
        obj = VATMaster.objects.get(terms='Standard VAT')
        response = self.client.get(reverse('vatmaster_delete', args=[obj.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/vatmaster/_vatmaster_confirm_delete.html')
        self.assertIn('vat_entry', response.context)

    def test_delete_view_post_success(self):
        obj_to_delete = VATMaster.objects.create(terms='Temp Delete', value=1.0, is_vat=True)
        response = self.client.post(reverse('vatmaster_delete', args=[obj_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(VATMaster.objects.filter(pk=obj_to_delete.pk).exists())
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshVATMasterList')

```

---

#### Step 5: HTMX and Alpine.js Integration

The generated code extensively uses HTMX attributes (`hx-get`, `hx-post`, `hx-target`, `hx-swap`, `hx-trigger`) for all dynamic interactions, ensuring no full-page reloads. Alpine.js is integrated using the `_=` (hyperscript) syntax to manage modal visibility, which is triggered by HTMX responses. DataTables is used for the list view, providing client-side search, sort, and pagination.

- **Modal Management:** The main `list.html` includes a hidden modal (`#modal`). Buttons trigger `hx-get` requests to load forms (`_vatmaster_form.html`) or delete confirmations (`_vatmaster_confirm_delete.html`) into `#modalContent`. The `_="on click add .is-active to #modal"` action makes the modal visible. Clicking outside the modal or on the "Cancel" button (`_="on click remove .is-active from #modal"`) hides it.
- **Form Submission:** Forms within the modal use `hx-post` to submit data. On successful submission, the view returns a `204 No Content` status with an `HX-Trigger: refreshVATMasterList` header. This header is caught by an event listener in `list.html` which then closes the modal and triggers an `hx-get` to `{% url 'vatmaster_table' %}` to refresh the DataTables content.
- **DataTables:** The `_vatmaster_table.html` partial includes the JavaScript to initialize DataTables on the loaded table. It's configured for pagination and searching, mirroring the ASP.NET GridView capabilities. The `destroy()` method is called before re-initialization to handle HTMX reloading gracefully.

---

### Final Notes

This comprehensive plan provides a blueprint for migrating your ASP.NET VAT Master module to Django. By following these structured steps and leveraging AI-assisted automation, your team can achieve a modern, maintainable, and high-performing application. The focus on "fat models, thin views," HTMX, Alpine.js, and DataTables ensures a truly modern web experience with efficient development and robust testing.