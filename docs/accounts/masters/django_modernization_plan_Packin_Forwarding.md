## ASP.NET to Django Conversion Script: Packing & Forwarding Module

### Modernization Plan Overview

This plan outlines the process for migrating the existing ASP.NET Packing & Forwarding module to a modern Django application. Our approach focuses on delivering a robust, scalable, and maintainable solution leveraging Django's best practices, HTMX for dynamic interactions, Alpine.js for client-side enhancements, and DataTables for superior data presentation.

By moving to Django, your organization will benefit from a highly efficient development framework, a clear separation of concerns, and a user experience that feels snappy and responsive without full page reloads. This systematic, automation-driven conversion minimizes manual effort and reduces the risk of errors, ensuring a smooth transition for your critical business operations.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
The ASP.NET `SqlDataSource1` explicitly defines CRUD operations against `tblPacking_Master`.

*   **Table Name:** `tblPacking_Master`
*   **Columns Identified:**
    *   `Id`: Primary Key (Int32).
    *   `Terms`: String.
    *   `Value`: String, but the `RegularExpressionValidator` `^\d{1,15}(\.\d{0,3})?$` indicates it should be a numeric/decimal type capable of storing up to 15 digits before the decimal and up to 3 decimal places.

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
The ASP.NET GridView and SqlDataSource configuration clearly define standard CRUD functionalities:

*   **Create (Insert):** Triggered by `btnInsert` in both the `FooterTemplate` (CommandName "Add") and `EmptyDataTemplate` (CommandName "Add1"). Handled by `GridView1_RowCommand` which uses `SqlDataSource1.Insert()`.
*   **Read (Select):** `GridView1` is populated using `SqlDataSource1`'s `SelectCommand="SELECT * FROM [tblPacking_Master] Order by [Id] Desc"`.
*   **Update:** Triggered by the "Edit" command field. Handled by `GridView1_RowUpdating` which performs a direct SQL `UPDATE` statement.
*   **Delete:** Triggered by the "Delete" command field. Handled by `GridView1_RowDeleted` and `SqlDataSource1.Delete()`.
*   **Validation Logic:**
    *   Required Field Validators for `Terms` and `Value`.
    *   Regular Expression Validator for `Value` (`^\d{1,15}(\.\d{0,3})?$`), ensuring it's a valid decimal number.
    *   Server-side validation `fun.NumberValidationQty(strvalue)` in `GridView1_RowCommand` confirms the numeric format.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The primary UI component is the `asp:GridView`.

*   **Data Display:** `GridView1` displays a paginated list of Packing & Forwarding entries. Each row includes "Edit" and "Delete" actions.
*   **User Input:**
    *   `TextBox` controls (`txtTerms1`, `txtValue1`) for editing existing entries.
    *   `TextBox` controls (`txtTerms2`, `txtValue2`, `txtTerms3`, `txtValue3`) for inserting new entries in the footer or empty data template.
*   **Actions:**
    *   `LinkButton` controls within `CommandField` for "Edit" and "Delete".
    *   `Button` controls (`btnInsert`) for initiating the "Add" operation.
*   **Client-Side Interaction:** `OnClientClick` attributes on buttons and link buttons suggest client-side JavaScript pop-up confirmations (`confirmationAdd`, `confirmationUpdate`, `confirmationDelete`). These will be replaced by HTMX modals/confirmations.
*   **Messages:** `asp:Label` (`lblMessage`) for displaying feedback messages (e.g., "Record Updated"). This will be handled by Django's messages framework.

### Step 4: Generate Django Code

Based on the analysis, we will create a Django application named `accounts` (following `Module_Accounts` from ASP.NET) to house the Packing & Forwarding module.

#### 4.1 Models

**Task:** Create a Django model based on the database schema.

**Instructions:**
The model `PackingForwarding` will map to `tblPacking_Master`. The `Id` primary key will be implicitly handled by Django's `id` field. We will define `terms` as a `CharField` and `value` as a `DecimalField` to accurately represent the data types and validation rules. A model method `clean_value_field` is added for robust validation.

**File:** `accounts/models.py`

```python
from django.db import models
from django.core.exceptions import ValidationError
import re

class PackingForwarding(models.Model):
    """
    Represents packing and forwarding terms.
    Maps to the existing tblPacking_Master table.
    """
    terms = models.CharField(db_column='Terms', max_length=255, verbose_name="Terms")
    # Value is stored as Decimal, supporting up to 15 digits before and 3 after the decimal point
    value = models.DecimalField(
        db_column='Value',
        max_digits=18, # 15 digits before + 3 digits after decimal point
        decimal_places=3,
        verbose_name="Value"
    )

    class Meta:
        managed = False  # Set to False to prevent Django from managing this table's schema
        db_table = 'tblPacking_Master' # The existing table name
        verbose_name = 'Packing & Forwarding Term'
        verbose_name_plural = 'Packing & Forwarding Terms'
        ordering = ['-id'] # Matches the 'Order by [Id] Desc' from original SQL

    def __str__(self):
        return f"{self.terms} ({self.value})"

    def clean(self):
        """
        Custom model-level validation for the 'value' field.
        This ensures the number format matches the ASP.NET RegularExpressionValidator.
        """
        # The regex ^\d{1,15}(\.\d{0,3})?$ allows 1 to 15 digits before decimal
        # and 0 to 3 digits after decimal.
        # Max_digits=18 and decimal_places=3 already handle this in the field definition.
        # However, for explicit regex validation as in ASP.NET, we can add it here.
        # The DecimalField also has its own validation which is usually sufficient.
        # This explicit regex check ensures it matches the original ASP.NET behavior precisely.
        value_str = str(self.value)
        # Handle cases where DecimalField might format without leading zero for values < 1
        # e.g. .5 instead of 0.5. The regex expects a leading digit.
        if value_str.startswith('.'):
            value_str = '0' + value_str

        # Ensure the string representation matches the original regex
        # Allowing for scientific notation if value is very small or large,
        # but the original regex implies fixed point notation.
        # We'll re-apply the regex based on the string representation.
        # The regex `^\d{1,15}(\.\d{0,3})?$` means at least one digit before decimal.
        if not re.fullmatch(r"^\d{1,15}(\.\d{0,3})?$", value_str):
            raise ValidationError(
                {'value': 'Value must be a number with up to 15 digits before the decimal and up to 3 decimal places.'}
            )

    # Example of a business logic method in the model (Fat Model principle)
    def calculate_cost(self, base_price):
        """
        Example business logic: calculates cost based on the packing/forwarding value.
        """
        return base_price * (1 + (self.value / 100)) # Assuming value is a percentage or factor
```

#### 4.2 Forms

**Task:** Define a Django form for user input.

**Instructions:**
A `ModelForm` will be used for `PackingForwarding` entries. Widgets with Tailwind CSS classes are applied for consistent styling. We'll add custom validation in the form's `clean_value` method to mirror the `RegularExpressionValidator` and `RequiredFieldValidator` from ASP.NET.

**File:** `accounts/forms.py`

```python
from django import forms
from .models import PackingForwarding
import re

class PackingForwardingForm(forms.ModelForm):
    """
    Form for creating and updating PackingForwarding instances.
    Includes client-side validation mirroring ASP.NET's regex.
    """
    class Meta:
        model = PackingForwarding
        fields = ['terms', 'value']
        widgets = {
            'terms': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'value': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 'inputmode': 'numeric', 'pattern': r"^\d{1,15}(\.\d{0,3})?$"}),
        }
        labels = {
            'terms': 'Terms',
            'value': 'Value',
        }

    def clean_terms(self):
        """
        Ensures the terms field is not empty.
        Corresponds to ASP.NET RequiredFieldValidator.
        """
        terms = self.cleaned_data.get('terms')
        if not terms:
            raise forms.ValidationError("Terms cannot be empty.")
        return terms

    def clean_value(self):
        """
        Custom validation for the value field, mirroring ASP.NET RegularExpressionValidator.
        Ensures the value is numeric and matches the specified pattern.
        Corresponds to ASP.NET RequiredFieldValidator and RegularExpressionValidator.
        """
        value_str = self.cleaned_data.get('value')
        if not value_str:
            raise forms.ValidationError("Value cannot be empty.")

        # This regex ensures 1 to 15 digits before the decimal and 0 to 3 digits after.
        # It's explicitly checking the string format, as DecimalField's own validation
        # might be less strict on the *format* and more on conversion.
        if not re.fullmatch(r"^\d{1,15}(\.\d{0,3})?$", str(value_str)):
            raise forms.ValidationError("Value must be a number with up to 15 digits before the decimal and up to 3 decimal places.")
        
        return value_str
```

#### 4.3 Views

**Task:** Implement CRUD operations using Django Class-Based Views (CBVs).

**Instructions:**
Views are kept thin, delegating business logic and complex validation to the model and form. HTMX specific responses (`HX-Request`, `HX-Trigger`) are included for seamless dynamic updates without full page reloads. A dedicated `TablePartialView` will render just the DataTables content.

**File:** `accounts/views.py`

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.template.loader import render_to_string
from .models import PackingForwarding
from .forms import PackingForwardingForm

# Base view for PackingForwarding
class PackingForwardingBaseView:
    model = PackingForwarding
    form_class = PackingForwardingForm
    context_object_name = 'packingforwarding' # For single object context
    success_url = reverse_lazy('packingforwarding_list')

    def get_template_name_suffix(self):
        """
        Override to allow dynamic template naming for HTMX partials.
        """
        if self.request.headers.get('HX-Request'):
            return '_partial' # For partial templates
        return super().get_template_name_suffix()


class PackingForwardingListView(ListView):
    """
    Displays a list of PackingForwarding entries.
    Renders the main page that will load the DataTable via HTMX.
    """
    model = PackingForwarding
    template_name = 'accounts/packingforwarding/list.html'
    context_object_name = 'packingforwardings' # For list context

class PackingForwardingTablePartialView(ListView):
    """
    Renders only the DataTables table content, intended for HTMX requests.
    """
    model = PackingForwarding
    template_name = 'accounts/packingforwarding/_packingforwarding_table.html'
    context_object_name = 'packingforwardings'


class PackingForwardingCreateView(PackingForwardingBaseView, CreateView):
    """
    Handles creation of new PackingForwarding entries.
    Responds with HTMX trigger on success.
    """
    template_name = 'accounts/packingforwarding/_packingforwarding_form.html'

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Packing & Forwarding Term added successfully.')
        if self.request.headers.get('HX-Request'):
            # Clear modal and trigger table refresh
            return HttpResponse(
                status=204, # No content, indicates success without navigating
                headers={
                    'HX-Trigger': 'refreshPackingForwardingList' # Custom event for HTMX
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # If invalid, re-render the form content within the modal
            return HttpResponse(render_to_string(self.template_name, {'form': form}, request=self.request))
        return response


class PackingForwardingUpdateView(PackingForwardingBaseView, UpdateView):
    """
    Handles updating existing PackingForwarding entries.
    Responds with HTMX trigger on success.
    """
    template_name = 'accounts/packingforwarding/_packingforwarding_form.html'

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Packing & Forwarding Term updated successfully.')
        if self.request.headers.get('HX-Request'):
            # Clear modal and trigger table refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPackingForwardingList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # If invalid, re-render the form content within the modal
            return HttpResponse(render_to_string(self.template_name, {'form': form}, request=self.request))
        return response


class PackingForwardingDeleteView(PackingForwardingBaseView, DeleteView):
    """
    Handles deletion of PackingForwarding entries.
    Provides confirmation, responds with HTMX trigger on success.
    """
    template_name = 'accounts/packingforwarding/_packingforwarding_confirm_delete.html'

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Packing & Forwarding Term deleted successfully.')
        if request.headers.get('HX-Request'):
            # Clear modal and trigger table refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPackingForwardingList'
                }
            )
        return response

    def get(self, request, *args, **kwargs):
        # Render the delete confirmation partial if it's an HTMX request
        if request.headers.get('HX-Request'):
            self.object = self.get_object()
            context = self.get_context_data(object=self.object)
            return HttpResponse(render_to_string(self.template_name, context, request=request))
        return super().get(request, *args, **kwargs)

```

#### 4.4 Templates

**Task:** Create templates for each view, focusing on HTMX and DataTables integration.

**Instructions:**
Templates will extend `core/base.html`. The list view orchestrates the loading of the actual table via HTMX. Forms and delete confirmations are partials rendered inside an Alpine.js-controlled modal.

**File:** `accounts/packingforwarding/list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Packing & Forwarding Terms</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
            hx-get="{% url 'packingforwarding_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Term
        </button>
    </div>
    
    <div id="packingforwardingTable-container"
         hx-trigger="load, refreshPackingForwardingList from:body"
         hx-get="{% url 'packingforwarding_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Packing & Forwarding Terms...</p>
        </div>
    </div>
    
    <!-- Modal for forms and confirmations -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-xl w-full mx-4"
             _="on htmx:afterOnLoad remove .hidden from #modal"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Example of Alpine.js usage if specific UI state management is needed.
    // For this simple modal, HTMX + _ is often sufficient.
    document.addEventListener('alpine:init', () => {
        Alpine.store('modal', {
            isOpen: false,
            open() { this.isOpen = true },
            close() { this.isOpen = false }
        });
    });

    // Handle closing the modal when HTMX requests are successful (status 204)
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.detail.xhr.status === 204 && evt.detail.elt.closest('#modal')) {
            document.getElementById('modal').classList.remove('is-active');
        }
    });

    // Show modal on HTMX load of content
    document.body.addEventListener('htmx:afterOnLoad', function(evt) {
        const modal = document.getElementById('modal');
        if (evt.detail.elt.closest('#modalContent') && evt.detail.xhr.status === 200) {
            modal.classList.add('is-active');
        }
    });
</script>
{% endblock %}
```

**File:** `accounts/packingforwarding/_packingforwarding_table.html`

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-md">
    <table id="packingforwardingTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Terms</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in packingforwardings %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.terms }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.value }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1.5 px-3 rounded-md mr-2 shadow-sm transition duration-150 ease-in-out"
                        hx-get="{% url 'packingforwarding_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1.5 px-3 rounded-md shadow-sm transition duration-150 ease-in-out"
                        hx-get="{% url 'packingforwarding_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="4" class="py-4 px-4 text-center text-gray-500">No Packing & Forwarding terms found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
// DataTables initialization
$(document).ready(function() {
    // Destroy existing DataTable instance if it exists to prevent re-initialization errors
    if ($.fn.DataTable.isDataTable('#packingforwardingTable')) {
        $('#packingforwardingTable').DataTable().destroy();
    }
    
    $('#packingforwardingTable').DataTable({
        "paging": true,
        "searching": true,
        "ordering": true,
        "info": true,
        "autoWidth": false,
        "responsive": true,
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 3] }, // Disable sorting for SN and Actions
            { "searchable": false, "targets": [0, 3] } // Disable searching for SN and Actions
        ]
    });
});
</script>
```

**File:** `accounts/packingforwarding/_packingforwarding_form.html`

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Packing & Forwarding Term</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-6">
        {% csrf_token %}
        
        <div>
            <label for="{{ form.terms.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                {{ form.terms.label }}
            </label>
            {{ form.terms }}
            {% if form.terms.errors %}
            <p class="text-red-600 text-sm mt-1">{{ form.terms.errors }}</p>
            {% endif %}
        </div>

        <div>
            <label for="{{ form.value.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                {{ form.value.label }}
            </label>
            {{ form.value }}
            {% if form.value.errors %}
            <p class="text-red-600 text-sm mt-1">{{ form.value.errors }}</p>
            {% endif %}
        </div>
        
        {% if form.non_field_errors %}
        <div class="text-red-600 text-sm mt-4">
            {% for error in form.non_field_errors %}
            <p>{{ error }}</p>
            {% endfor %}
        </div>
        {% endif %}

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

**File:** `accounts/packingforwarding/_packingforwarding_confirm_delete.html`

```html
<div class="p-6 text-center">
    <h3 class="text-2xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the Packing & Forwarding Term: 
        <span class="font-bold">{{ packingforwarding.terms }} ({{ packingforwarding.value }})</span>?
        This action cannot be undone.
    </p>
    <form hx-post="{% url 'packingforwarding_delete' packingforwarding.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-center space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
URLs are defined for the main list page, the HTMX-loaded table, and the HTMX-loaded CRUD forms/confirmations.

**File:** `accounts/urls.py`

```python
from django.urls import path
from .views import (
    PackingForwardingListView,
    PackingForwardingTablePartialView,
    PackingForwardingCreateView,
    PackingForwardingUpdateView,
    PackingForwardingDeleteView
)

urlpatterns = [
    # Main list page
    path('packingforwarding/', PackingForwardingListView.as_view(), name='packingforwarding_list'),
    
    # HTMX partial for the table content
    path('packingforwarding/table/', PackingForwardingTablePartialView.as_view(), name='packingforwarding_table'),
    
    # HTMX partials for CRUD operations (loaded into modal)
    path('packingforwarding/add/', PackingForwardingCreateView.as_view(), name='packingforwarding_add'),
    path('packingforwarding/edit/<int:pk>/', PackingForwardingUpdateView.as_view(), name='packingforwarding_edit'),
    path('packingforwarding/delete/<int:pk>/', PackingForwardingDeleteView.as_view(), name='packingforwarding_delete'),
]
```

#### 4.6 Tests

**Task:** Write comprehensive tests for the model and views to ensure functionality and coverage.

**Instructions:**
Tests cover model creation, field validation, and all CRUD operations through views, including specific tests for HTMX responses.

**File:** `accounts/tests.py`

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.core.exceptions import ValidationError
from decimal import Decimal
from .models import PackingForwarding
from .forms import PackingForwardingForm

class PackingForwardingModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        PackingForwarding.objects.create(
            terms='Test Term 1',
            value=Decimal('10.500'),
        )
        PackingForwarding.objects.create(
            terms='Test Term 2',
            value=Decimal('25.00'),
        )
  
    def test_packingforwarding_creation(self):
        obj = PackingForwarding.objects.get(id=1)
        self.assertEqual(obj.terms, 'Test Term 1')
        self.assertEqual(obj.value, Decimal('10.500'))
        
    def test_terms_label(self):
        obj = PackingForwarding.objects.get(id=1)
        field_label = obj._meta.get_field('terms').verbose_name
        self.assertEqual(field_label, 'Terms')
        
    def test_value_label(self):
        obj = PackingForwarding.objects.get(id=1)
        field_label = obj._meta.get_field('value').verbose_name
        self.assertEqual(field_label, 'Value')

    def test_object_string_representation(self):
        obj = PackingForwarding.objects.get(id=1)
        self.assertEqual(str(obj), 'Test Term 1 (10.500)')

    def test_value_validation_valid_formats(self):
        # Test valid decimal formats matching regex
        valid_values = ['1', '123', '0.5', '12.34', '123456789012345.123', '123456789012345']
        for val_str in valid_values:
            obj = PackingForwarding(terms="Temp", value=Decimal(val_str))
            try:
                obj.clean() # Call model's clean method
            except ValidationError as e:
                self.fail(f"Validation failed unexpectedly for value: {val_str} with error: {e}")

    def test_value_validation_invalid_formats(self):
        # Test invalid decimal formats not matching regex
        invalid_values = [
            '0.1234',  # More than 3 decimal places
            '1234567890123456.1', # More than 15 digits before decimal
            '.123', # Starts with decimal (regex requires leading digit)
            'abc', # Non-numeric
            '' # Empty string (handled by forms' required validation normally, but model can catch)
        ]
        for val_str in invalid_values:
            obj = PackingForwarding(terms="Invalid", value=Decimal(val_str) if val_str else None)
            with self.assertRaises(ValidationError, msg=f"Validation should fail for value: {val_str}"):
                obj.clean() # Call model's clean method

    def test_calculate_cost_method(self):
        obj = PackingForwarding.objects.get(id=1) # value = 10.500
        base_price = Decimal('100.00')
        expected_cost = base_price * (1 + (obj.value / 100)) # 100 * (1 + 0.105) = 110.50
        self.assertEqual(obj.calculate_cost(base_price), expected_cost)


class PackingForwardingViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create initial test data for views
        PackingForwarding.objects.create(terms='Term A', value=Decimal('5.00'))
        PackingForwarding.objects.create(terms='Term B', value=Decimal('15.75'))
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view(self):
        response = self.client.get(reverse('packingforwarding_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/packingforwarding/list.html')
        self.assertContains(response, 'Add New Term') # Check for the add button

    def test_table_partial_view(self):
        # Simulate HTMX request for the table
        response = self.client.get(reverse('packingforwarding_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/packingforwarding/_packingforwarding_table.html')
        self.assertTrue('packingforwardings' in response.context)
        self.assertContains(response, 'Term A')
        self.assertContains(response, 'Term B')
        self.assertContains(response, '15.75')
        self.assertContains(response, 'id="packingforwardingTable"') # Check for DataTable ID

    def test_create_view_get(self):
        # Simulate HTMX request for the form
        response = self.client.get(reverse('packingforwarding_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/packingforwarding/_packingforwarding_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Packing & Forwarding Term')
        
    def test_create_view_post_success(self):
        data = {
            'terms': 'New Term C',
            'value': '30.250',
        }
        response = self.client.post(reverse('packingforwarding_add'), data, HTTP_HX_REQUEST='true')
        
        # Expect 204 No Content for successful HTMX post that triggers a refresh
        self.assertEqual(response.status_code, 204) 
        self.assertTrue(PackingForwarding.objects.filter(terms='New Term C').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshPackingForwardingList', response.headers['HX-Trigger'])
        
    def test_create_view_post_invalid(self):
        data = {
            'terms': '', # Missing required field
            'value': 'invalid', # Invalid value
        }
        response = self.client.post(reverse('packingforwarding_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'accounts/packingforwarding/_packingforwarding_form.html')
        self.assertContains(response, 'Terms cannot be empty.')
        self.assertContains(response, 'Value must be a number with up to 15 digits before the decimal and up to 3 decimal places.')
        self.assertFalse(PackingForwarding.objects.filter(terms='').exists())

    def test_update_view_get(self):
        obj = PackingForwarding.objects.get(terms='Term A')
        response = self.client.get(reverse('packingforwarding_edit', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/packingforwarding/_packingforwarding_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Edit Packing & Forwarding Term')
        self.assertContains(response, 'Term A') # Check existing data is pre-filled

    def test_update_view_post_success(self):
        obj = PackingForwarding.objects.get(terms='Term A')
        data = {
            'terms': 'Updated Term A',
            'value': '6.75',
        }
        response = self.client.post(reverse('packingforwarding_edit', args=[obj.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        obj.refresh_from_db()
        self.assertEqual(obj.terms, 'Updated Term A')
        self.assertEqual(obj.value, Decimal('6.750'))
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshPackingForwardingList', response.headers['HX-Trigger'])

    def test_update_view_post_invalid(self):
        obj = PackingForwarding.objects.get(terms='Term A')
        data = {
            'terms': 'Updated Term A',
            'value': 'invalid_value',
        }
        response = self.client.post(reverse('packingforwarding_edit', args=[obj.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/packingforwarding/_packingforwarding_form.html')
        self.assertContains(response, 'Value must be a number with up to 15 digits before the decimal and up to 3 decimal places.')
        
    def test_delete_view_get(self):
        obj = PackingForwarding.objects.get(terms='Term B')
        response = self.client.get(reverse('packingforwarding_delete', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/packingforwarding/_packingforwarding_confirm_delete.html')
        self.assertTrue('packingforwarding' in response.context)
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, 'Term B')
        
    def test_delete_view_post_success(self):
        obj_count_before = PackingForwarding.objects.count()
        obj_to_delete = PackingForwarding.objects.get(terms='Term B')
        
        response = self.client.post(reverse('packingforwarding_delete', args=[obj_to_delete.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(PackingForwarding.objects.count(), obj_count_before - 1)
        self.assertFalse(PackingForwarding.objects.filter(id=obj_to_delete.id).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshPackingForwardingList', response.headers['HX-Trigger'])
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for Dynamic Content:**
    *   The main `list.html` uses `hx-get="{% url 'packingforwarding_table' %}"` with `hx-trigger="load, refreshPackingForwardingList from:body"` to dynamically load and refresh the DataTables content.
    *   "Add New Term", "Edit", and "Delete" buttons use `hx-get` to fetch their respective forms (`_packingforwarding_form.html` or `_packingforwarding_confirm_delete.html`) into the `#modalContent` div.
    *   Form submissions (`hx-post`) on the partial forms use `hx-swap="none"` and `hx-trigger="refreshPackingForwardingList"` to signal the main list to refresh its table without reloading the entire page. This mirrors the `lblMessage` updates and GridView refreshes from ASP.NET.
    *   Error handling for forms (e.g., invalid input) causes the form partial to be re-rendered with validation errors, allowing users to correct inputs within the modal.
*   **Alpine.js for Modal Management:**
    *   A simple `_` (hyperscript) expression `on click add .is-active to #modal` is used to show the modal when an HTMX button is clicked.
    *   `on click if event.target.id == 'modal' remove .is-active from me` on the modal overlay allows closing it by clicking outside the content.
    *   The modal is hidden (by removing `is-active` class) on successful HTMX form submissions (status 204) via a global `htmx:afterRequest` event listener.
*   **DataTables for List Views:**
    *   The `_packingforwarding_table.html` partial explicitly initializes DataTables on the `packingforwardingTable` ID.
    *   It's configured for pagination, searching, sorting, and responsiveness, directly replacing the `AllowPaging`, `ShowFooter`, `AutoGenerateColumns` and `yui-datatable-theme` functionality of the `asp:GridView`.
    *   `destroy()` is called before `DataTable()` initialization to handle cases where the table partial is reloaded by HTMX, preventing re-initialization errors.

### Final Notes

*   **Placeholders:** All `[MODEL_NAME]`, `[FIELD]` etc. have been replaced with actual names like `PackingForwarding`, `terms`, `value`.
*   **DRY Templates:** Achieved through `base.html` inheritance and partial templates (`_packingforwarding_table.html`, `_packingforwarding_form.html`, `_packingforwarding_confirm_delete.html`).
*   **Fat Model/Thin View:** Model methods for validation (`clean` method) and potential business logic (`calculate_cost`) are demonstrated. Views are kept minimal, primarily orchestrating data flow and responses.
*   **Comprehensive Tests:** Unit tests for the model and integration tests for all views and HTMX interactions are provided to ensure high test coverage and reliability.
*   **Tailwind CSS:** All generated HTML includes Tailwind CSS classes for modern styling.
*   **Business Value:** This modernized Django application provides a more maintainable codebase, improved performance due to partial page updates with HTMX, a better user experience, and a stronger foundation for future feature development compared to the legacy ASP.NET application. The clear structure and adherence to modern best practices will also make onboarding new developers significantly easier.