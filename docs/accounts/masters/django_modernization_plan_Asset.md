## ASP.NET to Django Conversion Script: Comprehensive Modernization Plan for Asset Management

This document outlines a strategic plan to modernize your legacy ASP.NET Asset Management module into a robust, scalable, and maintainable Django application. Our approach leverages AI-assisted automation to streamline the migration process, focusing on business benefits and delivering a modern user experience with minimal manual intervention.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is extended.
- Focus ONLY on component-specific code for the current module (`asset_management`).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
From the `SqlDataSource` definitions in the ASP.NET code, we've identified two primary tables responsible for managing assets:

*   **`tblACC_Asset_Category`**: This table stores information about different asset categories.
*   **`tblACC_Asset_SubCategory`**: This table stores information about asset sub-categories, which are related to the main asset categories.

**Inferred Schema:**

**Table 1: `tblACC_Asset_Category`**
*   `Id` (Primary Key, Integer) - Implied from `DataKeyNames="Id"` and `WHERE [Id] = @Id`.
*   `Category` (String/NVARCHAR) - Used for general category name.
*   `Abbrivation` (String/NVARCHAR) - Used for a shorter symbol or abbreviation, with a uniqueness constraint and conversion to uppercase.

**Table 2: `tblACC_Asset_SubCategory`**
*   `Id` (Primary Key, Integer) - Implied from `DataKeyNames="Id"` and `WHERE [Id] = @Id`.
*   `MId` (Foreign Key, Integer) - This is the foreign key linking to `tblACC_Asset_Category.Id`, as indicated by `[MId]` and the `SqlDataSource2` which selects from `tblACC_Asset_Category` to populate a dropdown.
*   `SubCategory` (String/NVARCHAR) - Used for the name of the sub-category.
*   `Abbrivation` (String/NVARCHAR) - Used for a shorter symbol or abbreviation for the sub-category, with a uniqueness constraint and conversion to uppercase.

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and validation logic in the ASP.NET code.

**Instructions:**
The ASP.NET code implements comprehensive CRUD (Create, Read, Update, Delete) functionality for both Asset Categories and Asset Sub-Categories using `GridView` controls and `SqlDataSource` components.

**Asset Category (`tblACC_Asset_Category`):**
*   **Create (Insert):** Triggered by "Add" or "Add1" commands.
    *   **Fields:** `Category`, `Abbrivation`.
    *   **Validation:**
        *   `Category` and `Abbrivation` are required.
        *   `Abbrivation` must be unique across all categories. An alert message "Category Abbrivation is already used." is displayed if a duplicate is found.
    *   **Post-operation:** Page refresh.
*   **Read (Select):** Displays all records from `tblACC_Asset_Category` ordered by `Id` in descending order.
*   **Update:** Triggered by "Edit" command.
    *   **Fields:** `Category`, `Abbrivation`.
    *   **Validation:**
        *   `Category` and `Abbrivation` are required.
        *   `Abbrivation` must be unique (excluding the current record being updated). An alert message "Category Abbrivation is already used." is displayed.
    *   **Post-operation:** Page refresh.
*   **Delete:** Triggered by "Delete" command.
    *   **Validation:** Client-side confirmation (via JavaScript `confirmationDelete()`).
    *   **Post-operation:** Page refresh.

**Asset Sub-Category (`tblACC_Asset_SubCategory`):**
*   **Create (Insert):** Triggered by "Add_sb" or "Add_sb1" commands.
    *   **Fields:** `MId` (Category ID), `SubCategory`, `Abbrivation`.
    *   **Validation:**
        *   `MId` (Category) must be selected and not "1" (which seems to be a placeholder for "Please select category").
        *   `SubCategory` and `Abbrivation` are required.
        *   `Abbrivation` must be unique across all sub-categories. An alert message "Subcategory Abbrivation is already used." is displayed.
    *   **Post-operation:** Page refresh.
*   **Read (Select):** Displays records from `tblACC_Asset_SubCategory`, including the associated `Category` name from `tblACC_Asset_Category` (via `MId`), ordered by `Id` in descending order.
*   **Update:** Triggered by "Edit" command.
    *   **Fields:** `MId` (Category ID), `SubCategory`, `Abbrivation`.
    *   **Validation:**
        *   `MId` (Category) must be selected and not "1".
        *   `SubCategory` and `Abbrivation` are required.
        *   `Abbrivation` must be unique (excluding the current record being updated). An alert message "Subcategory Abbrivation is already used." is displayed.
    *   **Post-operation:** Page refresh.
*   **Delete:** Triggered by "Delete" command.
    *   **Validation:** Client-side confirmation (via JavaScript `confirmationDelete()`).
    *   **Post-operation:** Page refresh.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The ASP.NET page uses a standard master page layout, common CSS for styling, and relies heavily on the `AjaxControlToolkit` `TabContainer` and `GridView` controls for presenting and interacting with data.

*   **`AjaxControlToolkit.TabContainer`:** This component creates a tabbed interface.
    *   `TabPanel1` (Master): Contains the main CRUD interfaces for categories and sub-categories.
    *   `TabPanel2` (Register): Marked "Work in Progress", indicating no functionality needs to be migrated for this tab currently.
*   **`asp:GridView` (GridView1 for Categories, GridView2 for Sub-Categories):** These are the core data display and interaction components.
    *   They display data in a tabular format.
    *   `AllowPaging`, `ShowFooter`, `AutoGenerateColumns="False"` imply custom column definitions.
    *   `DataKeyNames="Id"` is used to identify records for update/delete operations.
    *   `TemplateField`s are used to customize columns for display (`Label`), editing (`TextBox`, `DropDownList`), and insertion (`TextBox`, `DropDownList` in `FooterTemplate` or `EmptyDataTemplate`).
    *   `CommandField`s provide "Edit" and "Delete" links.
    *   `FooterTemplate` includes an "Insert" button and input fields for adding new records directly within the grid.
    *   `EmptyDataTemplate` provides an alternative input form when the grid is empty.
    *   `RequiredFieldValidator` controls are used for server-side validation of input fields.
    *   CSS classes like `yui-datatable-theme`, `fontcsswhite`, `redbox`, `box3` are applied for styling.
*   **`asp:TextBox`:** Used for text input (Category, Abbrivation, Sub-Category).
*   **`asp:DropDownList`:** Used for selecting the parent category for sub-categories (`MId`).
*   **`asp:Button`, `asp:LinkButton`:** Trigger actions (Insert, Edit, Delete).
*   **`asp:Label`:** Used for displaying data and messages (`lblMessage`, `lblMessage1`).
*   **Client-side JavaScript:** `PopUpMsg.js`, `loadingNotifier.js` are referenced, and inline `ClientScript.RegisterStartupScript` is used for `alert()` messages and `onclick` confirmations (`confirmationAdd()`, `confirmationUpdate()`, `confirmationDelete()`).

**Django Modernization Strategy:**

*   The `TabContainer` functionality will be replicated using Alpine.js for tab state management and HTMX for dynamically loading content into the active tab.
*   `GridView`s will be replaced by standard HTML tables enhanced with DataTables for pagination, sorting, and searching.
*   CRUD forms will be rendered as Django forms, loaded dynamically into modals via HTMX.
*   All user interactions (add, edit, delete) will use HTMX to avoid full page reloads, providing a more responsive user experience.
*   Server-side validation will be handled by Django Forms and Model methods. Client-side confirmation will be managed by HTMX or Alpine.js.
*   CSS will be managed with Tailwind CSS.

### Step 4: Generate Django Code

We will create a new Django application named `asset_management` to house these components.

#### 4.1 Models (`asset_management/models.py`)

The models will map directly to the existing database tables, ensuring `managed = False` for seamless integration. Business logic for case conversion and validation will be encapsulated here where appropriate, following the "Fat Model" principle.

```python
from django.db import models
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

class AssetCategory(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    category = models.CharField(db_column='Category', max_length=255, verbose_name=_('Category'))
    abbrivation = models.CharField(db_column='Abbrivation', max_length=50, unique=True, verbose_name=_('Symbol'))

    class Meta:
        managed = False
        db_table = 'tblACC_Asset_Category'
        verbose_name = _('Asset Category')
        verbose_name_plural = _('Asset Categories')
        ordering = ['-id'] # Matches ASP.NET's 'order by [Id] desc'

    def __str__(self):
        return f"{self.category} - {self.abbrivation}"

    def clean(self):
        # Ensure abbrivation is uppercase before validation
        self.abbrivation = self.abbrivation.upper()
        # Custom uniqueness check for abbrivation (optional if unique=True is used)
        # However, a clean method can add more complex logic or context-aware validation.
        # Django's unique=True handles this efficiently for single fields.
        # For cross-field unique constraints, use Meta.unique_together or a custom clean method.
        # The ASP.NET code only checked Abbrivation uniqueness on this specific field.

    def save(self, *args, **kwargs):
        self.abbrivation = self.abbrivation.upper() # Ensure abbrivation is always uppercase
        super().save(*args, **kwargs)

class AssetSubCategory(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    master_category = models.ForeignKey(AssetCategory, models.DO_NOTHING, db_column='MId', verbose_name=_('Category'))
    subcategory = models.CharField(db_column='SubCategory', max_length=255, verbose_name=_('Sub-Category'))
    abbrivation = models.CharField(db_column='Abbrivation', max_length=50, unique=True, verbose_name=_('Symbol'))

    class Meta:
        managed = False
        db_table = 'tblACC_Asset_SubCategory'
        verbose_name = _('Asset Sub-Category')
        verbose_name_plural = _('Asset Sub-Categories')
        ordering = ['-id'] # Matches ASP.NET's 'order by [Id] desc'

    def __str__(self):
        return f"{self.master_category.category} - {self.subcategory} ({self.abbrivation})"

    def clean(self):
        # Ensure abbrivation is uppercase before validation
        self.abbrivation = self.abbrivation.upper()
        # Custom validation for MId != '1' (placeholder)
        # In Django, ForeignKey fields automatically handle valid related instances.
        # If '1' was a placeholder for 'None' or an invalid choice, this would be handled by forms.
        if self.master_category_id is None: # Assuming '1' might represent no selection in ASP.NET
            raise ValidationError(_("Please select a category."))

    def save(self, *args, **kwargs):
        self.abbrivation = self.abbrivation.upper() # Ensure abbrivation is always uppercase
        super().save(*args, **kwargs)

```

#### 4.2 Forms (`asset_management/forms.py`)

Forms will handle data validation and input field presentation, including mapping to Tailwind CSS classes.

```python
from django import forms
from .models import AssetCategory, AssetSubCategory

class AssetCategoryForm(forms.ModelForm):
    class Meta:
        model = AssetCategory
        fields = ['category', 'abbrivation']
        widgets = {
            'category': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
                'placeholder': 'Enter category name'
            }),
            'abbrivation': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
                'placeholder': 'Enter symbol/abbreviation'
            }),
        }
        
    def clean_abbrivation(self):
        # Ensure abbrivation is uppercase during form cleaning
        return self.cleaned_data['abbrivation'].upper()

class AssetSubCategoryForm(forms.ModelForm):
    class Meta:
        model = AssetSubCategory
        fields = ['master_category', 'subcategory', 'abbrivation']
        widgets = {
            'master_category': forms.Select(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'
            }),
            'subcategory': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
                'placeholder': 'Enter sub-category name'
            }),
            'abbrivation': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
                'placeholder': 'Enter symbol/abbreviation'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Customize the queryset for master_category dropdown as per SqlDataSource2
        # SqlDataSource2: select Id,(CASE WHEN Id!= 0 THEN Category + ' - ' + Abbrivation ELSE Abbrivation  END) AS Expr2 from tblACC_Asset_Category
        # This implies a "dummy" 0 or 1 ID might exist, so we filter it out.
        # Assuming ID=0 is the dummy entry (as per ASP.NET's 'Id!='0' logic)
        self.fields['master_category'].queryset = AssetCategory.objects.exclude(id=0).order_by('category')
        # Add a default 'Please select' option as ASP.NET implied '1' is placeholder
        self.fields['master_category'].empty_label = "--- Please Select Category ---"

    def clean_master_category(self):
        category = self.cleaned_data.get('master_category')
        if category is None or category.id == 1: # Assuming '1' is the sentinel value for 'not selected' or invalid
            # The ASP.NET code checked for StrCategory != "1"
            # In Django, an empty or unselected ForeignKey field will usually be None.
            # If a specific ID (like 1) was used as a placeholder, validate against it.
            # For demonstration, we assume `None` or an explicitly excluded ID.
            # The empty_label added in __init__ makes this more robust.
            raise forms.ValidationError("Please select a valid category.")
        return category
        
    def clean_abbrivation(self):
        # Ensure abbrivation is uppercase during form cleaning
        return self.cleaned_data['abbrivation'].upper()

```

#### 4.3 Views (`asset_management/views.py`)

Views will be thin, relying on Django's generic class-based views for CRUD operations. HTMX headers will be used to determine the response type (full page reload vs. HTMX partial update).

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.template.loader import render_to_string # For rendering partials
from django.db import IntegrityError # For handling unique constraint errors

from .models import AssetCategory, AssetSubCategory
from .forms import AssetCategoryForm, AssetSubCategoryForm

# --- Main Dashboard View for Tabbed Interface ---
class AssetManagementDashboardView(TemplateView):
    template_name = 'asset_management/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Optionally pre-fetch data for initial tab, though HTMX will load.
        # context['asset_categories'] = AssetCategory.objects.all()
        # context['asset_sub_categories'] = AssetSubCategory.objects.all()
        return context

# --- Asset Category Views ---
class AssetCategoryListView(ListView):
    model = AssetCategory
    template_name = 'asset_management/assetcategory/_assetcategory_table.html' # This will be loaded via HTMX
    context_object_name = 'asset_categories'

    # This view is primarily for HTMX requests to refresh the table.
    # The initial page render will be handled by AssetManagementDashboardView loading this partial.

class AssetCategoryCreateView(CreateView):
    model = AssetCategory
    form_class = AssetCategoryForm
    template_name = 'asset_management/assetcategory/_assetcategory_form.html' # Partial for modal
    success_url = reverse_lazy('asset_category_list') # Not directly used for HTMX swap, but good practice

    def form_valid(self, form):
        try:
            response = super().form_valid(form)
            messages.success(self.request, 'Asset Category added successfully.')
            if self.request.headers.get('HX-Request'):
                # Return a 204 No Content for HTMX to close modal and trigger refresh
                return HttpResponse(
                    status=204,
                    headers={'HX-Trigger': 'refreshAssetCategoryList'}
                )
            return response
        except IntegrityError:
            form.add_error('abbrivation', 'This symbol/abbreviation is already in use.')
            # Re-render the form with errors for HTMX response
            return self.form_invalid(form)


    def form_invalid(self, form):
        # For HTMX requests, we re-render the form with errors
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                render_to_string(self.template_name, {'form': form}, request=self.request),
                status=400 # Indicate a client-side error
            )
        return super().form_invalid(form)


class AssetCategoryUpdateView(UpdateView):
    model = AssetCategory
    form_class = AssetCategoryForm
    template_name = 'asset_management/assetcategory/_assetcategory_form.html' # Partial for modal
    success_url = reverse_lazy('asset_category_list')

    def form_valid(self, form):
        try:
            response = super().form_valid(form)
            messages.success(self.request, 'Asset Category updated successfully.')
            if self.request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204,
                    headers={'HX-Trigger': 'refreshAssetCategoryList'}
                )
            return response
        except IntegrityError:
            form.add_error('abbrivation', 'This symbol/abbreviation is already in use by another category.')
            return self.form_invalid(form)

    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                render_to_string(self.template_name, {'form': form}, request=self.request),
                status=400
            )
        return super().form_invalid(form)

class AssetCategoryDeleteView(DeleteView):
    model = AssetCategory
    template_name = 'asset_management/assetcategory/confirm_delete.html' # Partial for modal
    success_url = reverse_lazy('asset_category_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Asset Category deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshAssetCategoryList'}
            )
        return response

# --- Asset Sub-Category Views ---
class AssetSubCategoryListView(ListView):
    model = AssetSubCategory
    template_name = 'asset_management/assetsubcategory/_assetsubcategory_table.html' # This will be loaded via HTMX
    context_object_name = 'asset_sub_categories'

class AssetSubCategoryCreateView(CreateView):
    model = AssetSubCategory
    form_class = AssetSubCategoryForm
    template_name = 'asset_management/assetsubcategory/_assetsubcategory_form.html' # Partial for modal
    success_url = reverse_lazy('asset_subcategory_list')

    def form_valid(self, form):
        try:
            response = super().form_valid(form)
            messages.success(self.request, 'Asset Sub-Category added successfully.')
            if self.request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204,
                    headers={'HX-Trigger': 'refreshAssetSubCategoryList'}
                )
            return response
        except IntegrityError:
            form.add_error('abbrivation', 'This symbol/abbreviation is already in use.')
            return self.form_invalid(form)

    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                render_to_string(self.template_name, {'form': form}, request=self.request),
                status=400
            )
        return super().form_invalid(form)


class AssetSubCategoryUpdateView(UpdateView):
    model = AssetSubCategory
    form_class = AssetSubCategoryForm
    template_name = 'asset_management/assetsubcategory/_assetsubcategory_form.html' # Partial for modal
    success_url = reverse_lazy('asset_subcategory_list')

    def form_valid(self, form):
        try:
            response = super().form_valid(form)
            messages.success(self.request, 'Asset Sub-Category updated successfully.')
            if self.request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204,
                    headers={'HX-Trigger': 'refreshAssetSubCategoryList'}
                )
            return response
        except IntegrityError:
            form.add_error('abbrivation', 'This symbol/abbreviation is already in use by another sub-category.')
            return self.form_invalid(form)

    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                render_to_string(self.template_name, {'form': form}, request=self.request),
                status=400
            )
        return super().form_invalid(form)


class AssetSubCategoryDeleteView(DeleteView):
    model = AssetSubCategory
    template_name = 'asset_management/assetsubcategory/confirm_delete.html' # Partial for modal
    success_url = reverse_lazy('asset_subcategory_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Asset Sub-Category deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshAssetSubCategoryList'}
            )
        return response

```

#### 4.4 Templates (`asset_management/templates/asset_management/`)

We'll create a main dashboard template, and partial templates for each table and CRUD form/confirmation.

**`dashboard.html` (Main page for tabbed interface):**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ activeTab: 'category' }">
    <h1 class="text-3xl font-bold text-gray-800 mb-6">Asset Management</h1>

    <!-- Tabs Navigation -->
    <div class="mb-4 border-b border-gray-200">
        <ul class="flex flex-wrap -mb-px text-sm font-medium text-center" id="myTab" role="tablist">
            <li class="mr-2" role="presentation">
                <button @click="activeTab = 'category'" :class="{ 'border-blue-500 text-blue-600': activeTab === 'category', 'border-transparent text-gray-500 hover:text-gray-600 hover:border-gray-300': activeTab !== 'category' }"
                        class="inline-block p-4 border-b-2 rounded-t-lg"
                        id="category-tab" type="button" role="tab" aria-controls="category" aria-selected="true">
                    Asset Category
                </button>
            </li>
            <li class="mr-2" role="presentation">
                <button @click="activeTab = 'subcategory'" :class="{ 'border-blue-500 text-blue-600': activeTab === 'subcategory', 'border-transparent text-gray-500 hover:text-gray-600 hover:border-gray-300': activeTab !== 'subcategory' }"
                        class="inline-block p-4 border-b-2 rounded-t-lg"
                        id="subcategory-tab" type="button" role="tab" aria-controls="subcategory" aria-selected="false">
                    Asset Sub-Category
                </button>
            </li>
            <li class="mr-2" role="presentation">
                <button @click="activeTab = 'register'" :class="{ 'border-blue-500 text-blue-600': activeTab === 'register', 'border-transparent text-gray-500 hover:text-gray-600 hover:border-gray-300': activeTab !== 'register' }"
                        class="inline-block p-4 border-b-2 rounded-t-lg"
                        id="register-tab" type="button" role="tab" aria-controls="register" aria-selected="false">
                    Register
                </button>
            </li>
        </ul>
    </div>

    <!-- Tab Content -->
    <div id="tabContent">
        <!-- Asset Category Tab -->
        <div x-show="activeTab === 'category'" id="category" role="tabpanel" aria-labelledby="category-tab">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold">Asset Categories</h2>
                <button 
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                    hx-get="{% url 'asset_category_add' %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Add New Category
                </button>
            </div>
            
            <div id="assetCategoryTable-container"
                hx-trigger="load, refreshAssetCategoryList from:body"
                hx-get="{% url 'asset_category_list' %}"
                hx-swap="innerHTML">
                <!-- DataTable will be loaded here via HTMX -->
                <div class="text-center">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading Asset Categories...</p>
                </div>
            </div>
        </div>

        <!-- Asset Sub-Category Tab -->
        <div x-show="activeTab === 'subcategory'" id="subcategory" role="tabpanel" aria-labelledby="subcategory-tab">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold">Asset Sub-Categories</h2>
                <button 
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                    hx-get="{% url 'asset_subcategory_add' %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Add New Sub-Category
                </button>
            </div>

            <div id="assetSubCategoryTable-container"
                hx-trigger="load, refreshAssetSubCategoryList from:body"
                hx-get="{% url 'asset_subcategory_list' %}"
                hx-swap="innerHTML">
                <!-- DataTable will be loaded here via HTMX -->
                <div class="text-center">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading Asset Sub-Categories...</p>
                </div>
            </div>
        </div>

        <!-- Register Tab -->
        <div x-show="activeTab === 'register'" id="register" role="tabpanel" aria-labelledby="register-tab">
            <div class="p-6 bg-white rounded-lg shadow-md">
                <p class="text-xl text-gray-700 text-center py-10">Work in Progress..............!!!</p>
            </div>
        </div>
    </div>
    
    <!-- Modal for forms -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me
            on htmx:afterOnLoad from:body remove .is-active from me"
         style="z-index: 1000;"> {# Ensure modal is above other content #}
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4 sm:mx-0"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Add DataTables and Alpine.js (if not already in base.html) -->
<script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/2.0.8/css/dataTables.dataTables.min.css">
<script type="text/javascript" src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
{% endblock %}
```

**`asset_management/assetcategory/_assetcategory_table.html`:**

```html
<div class="overflow-x-auto rounded-lg shadow overflow-y-auto relative" style="max-height: 500px;">
    <table id="assetCategoryTable" class="min-w-full bg-white divide-y divide-gray-200">
        <thead class="bg-gray-50 sticky top-0 z-10">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% for obj in asset_categories %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.category }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.abbrivation }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md text-sm mr-2"
                        hx-get="{% url 'asset_category_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md text-sm"
                        hx-get="{% url 'asset_category_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="4" class="py-4 px-4 text-center text-gray-500">No Asset Categories found. Add a new one!</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Ensure DataTables is initialized only once and after the table is in DOM
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#assetCategoryTable')) {
            $('#assetCategoryTable').DataTable().destroy();
        }
        $('#assetCategoryTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true
        });
    });
</script>
```

**`asset_management/assetcategory/_assetcategory_form.html`:**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Asset Category</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors|join:", " }}</p>
                {% endif %}
            </div>
            {% endfor %}
            {% if form.non_field_errors %}
                <div class="text-red-500 text-xs mt-1">{{ form.non_field_errors }}</div>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out">
                Save
            </button>
            <div id="form-spinner" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
            </div>
        </div>
    </form>
</div>
```

**`asset_management/assetcategory/confirm_delete.html`:**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">Confirm Deletion</h3>
    <p class="text-gray-700 mb-8">Are you sure you want to delete the Asset Category: <span class="font-bold">{{ object.category }} ({{ object.abbrivation }})</span>?</p>
    <form hx-delete="{% url 'asset_category_delete' object.pk %}" hx-swap="none" hx-indicator="#delete-spinner">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out">
                Delete
            </button>
            <div id="delete-spinner" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-red-500"></div>
            </div>
        </div>
    </form>
</div>
```

**`asset_management/assetsubcategory/_assetsubcategory_table.html`:**

```html
<div class="overflow-x-auto rounded-lg shadow overflow-y-auto relative" style="max-height: 500px;">
    <table id="assetSubCategoryTable" class="min-w-full bg-white divide-y divide-gray-200">
        <thead class="bg-gray-50 sticky top-0 z-10">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sub-Category</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% for obj in asset_sub_categories %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.master_category.category }} - {{ obj.master_category.abbrivation }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.subcategory }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.abbrivation }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md text-sm mr-2"
                        hx-get="{% url 'asset_subcategory_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md text-sm"
                        hx-get="{% url 'asset_subcategory_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5" class="py-4 px-4 text-center text-gray-500">No Asset Sub-Categories found. Add a new one!</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#assetSubCategoryTable')) {
            $('#assetSubCategoryTable').DataTable().destroy();
        }
        $('#assetSubCategoryTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true
        });
    });
</script>
```

**`asset_management/assetsubcategory/_assetsubcategory_form.html`:**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Asset Sub-Category</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors|join:", " }}</p>
                {% endif %}
            </div>
            {% endfor %}
            {% if form.non_field_errors %}
                <div class="text-red-500 text-xs mt-1">{{ form.non_field_errors }}</div>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out">
                Save
            </button>
            <div id="form-spinner" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
            </div>
        </div>
    </form>
</div>
```

**`asset_management/assetsubcategory/confirm_delete.html`:**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">Confirm Deletion</h3>
    <p class="text-gray-700 mb-8">Are you sure you want to delete the Asset Sub-Category: <span class="font-bold">{{ object.subcategory }} ({{ object.abbrivation }})</span> under <span class="font-bold">{{ object.master_category.category }}</span>?</p>
    <form hx-delete="{% url 'asset_subcategory_delete' object.pk %}" hx-swap="none" hx-indicator="#delete-spinner">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out">
                Delete
            </button>
            <div id="delete-spinner" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-red-500"></div>
            </div>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`asset_management/urls.py`)

This file defines the routes for accessing the asset management features.

```python
from django.urls import path
from .views import (
    AssetManagementDashboardView,
    AssetCategoryListView, AssetCategoryCreateView, AssetCategoryUpdateView, AssetCategoryDeleteView,
    AssetSubCategoryListView, AssetSubCategoryCreateView, AssetSubCategoryUpdateView, AssetSubCategoryDeleteView
)

urlpatterns = [
    # Main Dashboard with Tabs
    path('assets/', AssetManagementDashboardView.as_view(), name='asset_management_dashboard'),

    # Asset Category URLs (HTMX targets for table and forms)
    path('assets/categories/table/', AssetCategoryListView.as_view(), name='asset_category_list'),
    path('assets/categories/add/', AssetCategoryCreateView.as_view(), name='asset_category_add'),
    path('assets/categories/edit/<int:pk>/', AssetCategoryUpdateView.as_view(), name='asset_category_edit'),
    path('assets/categories/delete/<int:pk>/', AssetCategoryDeleteView.as_view(), name='asset_category_delete'),

    # Asset Sub-Category URLs (HTMX targets for table and forms)
    path('assets/subcategories/table/', AssetSubCategoryListView.as_view(), name='asset_subcategory_list'),
    path('assets/subcategories/add/', AssetSubCategoryCreateView.as_view(), name='asset_subcategory_add'),
    path('assets/subcategories/edit/<int:pk>/', AssetSubCategoryUpdateView.as_view(), name='asset_subcategory_edit'),
    path('assets/subcategories/delete/<int:pk>/', AssetSubCategoryDeleteView.as_view(), name='asset_subcategory_delete'),
]

```

#### 4.6 Tests (`asset_management/tests.py`)

Comprehensive tests for both models and views will ensure the integrity and functionality of the migrated application.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db import IntegrityError
from .models import AssetCategory, AssetSubCategory
from .forms import AssetCategoryForm, AssetSubCategoryForm

# --- Asset Category Model Tests ---
class AssetCategoryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        AssetCategory.objects.create(id=1, category='Electronics', abbrivation='ELEC')
        AssetCategory.objects.create(id=2, category='Furniture', abbrivation='FURN')

    def test_asset_category_creation(self):
        category = AssetCategory.objects.get(id=1)
        self.assertEqual(category.category, 'Electronics')
        self.assertEqual(category.abbrivation, 'ELEC')

    def test_abbrivation_uppercasing_on_save(self):
        new_category = AssetCategory.objects.create(id=3, category='Vehicles', abbrivation='vehi')
        self.assertEqual(new_category.abbrivation, 'VEHI')
        
    def test_abbrivation_uniqueness(self):
        # Attempt to create a category with a duplicate abbrivation
        with self.assertRaises(IntegrityError):
            AssetCategory.objects.create(id=4, category='Home Appliances', abbrivation='ELEC')

    def test_string_representation(self):
        category = AssetCategory.objects.get(id=1)
        self.assertEqual(str(category), 'Electronics - ELEC')

# --- Asset Sub-Category Model Tests ---
class AssetSubCategoryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create parent category for sub-category tests
        cls.category1 = AssetCategory.objects.create(id=10, category='IT Equipment', abbrivation='IT')
        cls.category2 = AssetCategory.objects.create(id=11, category='Office Supplies', abbrivation='OFF')
        
        AssetSubCategory.objects.create(id=1, master_category=cls.category1, subcategory='Laptops', abbrivation='LAP')
        AssetSubCategory.objects.create(id=2, master_category=cls.category1, subcategory='Monitors', abbrivation='MON')

    def test_asset_subcategory_creation(self):
        sub_category = AssetSubCategory.objects.get(id=1)
        self.assertEqual(sub_category.subcategory, 'Laptops')
        self.assertEqual(sub_category.master_category, self.category1)
        self.assertEqual(sub_category.abbrivation, 'LAP')

    def test_abbrivation_uppercasing_on_save(self):
        new_sub = AssetSubCategory.objects.create(id=3, master_category=self.category2, subcategory='Pens', abbrivation='pen')
        self.assertEqual(new_sub.abbrivation, 'PEN')
        
    def test_abbrivation_uniqueness(self):
        # Attempt to create a sub-category with a duplicate abbrivation
        with self.assertRaises(IntegrityError):
            AssetSubCategory.objects.create(id=4, master_category=self.category2, subcategory='Keyboards', abbrivation='LAP')

    def test_string_representation(self):
        sub_category = AssetSubCategory.objects.get(id=1)
        self.assertEqual(str(sub_category), 'IT Equipment - Laptops (LAP)')

# --- Asset Category Views Tests ---
class AssetCategoryViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.category1 = AssetCategory.objects.create(id=100, category='Test Category 1', abbrivation='TC1')
        self.category2 = AssetCategory.objects.create(id=101, category='Test Category 2', abbrivation='TC2')

    def test_dashboard_view(self):
        response = self.client.get(reverse('asset_management_dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'asset_management/dashboard.html')

    def test_list_view_htmx(self):
        response = self.client.get(reverse('asset_category_list'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'asset_management/assetcategory/_assetcategory_table.html')
        self.assertContains(response, 'Test Category 1')
        self.assertContains(response, 'Test Category 2')

    def test_create_view_get_htmx(self):
        response = self.client.get(reverse('asset_category_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'asset_management/assetcategory/_assetcategory_form.html')
        self.assertContains(response, 'Add Asset Category') # Check form title

    def test_create_view_post_htmx_success(self):
        data = {'category': 'New Category', 'abbrivation': 'NCAT'}
        response = self.client.post(reverse('asset_category_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # 204 No Content for successful HTMX post
        self.assertTrue(AssetCategory.objects.filter(category='New Category').exists())
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshAssetCategoryList')

    def test_create_view_post_htmx_invalid(self):
        data = {'category': '', 'abbrivation': 'INVALID'} # Invalid data
        response = self.client.post(reverse('asset_category_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400) # 400 Bad Request for invalid form
        self.assertTemplateUsed(response, 'asset_management/assetcategory/_assetcategory_form.html')
        self.assertContains(response, 'This field is required.') # Check for validation error message

    def test_create_view_post_htmx_duplicate_abbrivation(self):
        data = {'category': 'Another Category', 'abbrivation': 'TC1'} # Duplicate abbrivation
        response = self.client.post(reverse('asset_category_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400)
        self.assertTemplateUsed(response, 'asset_management/assetcategory/_assetcategory_form.html')
        self.assertContains(response, 'Asset category with this Symbol already exists.') # Django's default unique message
        self.assertContains(response, 'This symbol/abbreviation is already in use.') # Custom error message from view

    def test_update_view_get_htmx(self):
        response = self.client.get(reverse('asset_category_edit', args=[self.category1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'asset_management/assetcategory/_assetcategory_form.html')
        self.assertContains(response, 'Edit Asset Category')
        self.assertContains(response, self.category1.category)

    def test_update_view_post_htmx_success(self):
        data = {'category': 'Updated Category', 'abbrivation': 'UCAT'}
        response = self.client.post(reverse('asset_category_edit', args=[self.category1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.category1.refresh_from_db()
        self.assertEqual(self.category1.category, 'Updated Category')
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshAssetCategoryList')

    def test_update_view_post_htmx_duplicate_abbrivation(self):
        data = {'category': 'Category Duplicate', 'abbrivation': 'TC2'} # Duplicate of another category
        response = self.client.post(reverse('asset_category_edit', args=[self.category1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400)
        self.assertTemplateUsed(response, 'asset_management/assetcategory/_assetcategory_form.html')
        self.assertContains(response, 'This symbol/abbreviation is already in use by another category.')

    def test_delete_view_get_htmx(self):
        response = self.client.get(reverse('asset_category_delete', args=[self.category1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'asset_management/assetcategory/confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, self.category1.category)

    def test_delete_view_post_htmx_success(self):
        response = self.client.delete(reverse('asset_category_delete', args=[self.category1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(AssetCategory.objects.filter(pk=self.category1.pk).exists())
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshAssetCategoryList')

# --- Asset Sub-Category Views Tests ---
class AssetSubCategoryViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.category = AssetCategory.objects.create(id=200, category='Main Cat', abbrivation='MCAT')
        # Create a dummy category with ID 1, as the form validates against it
        AssetCategory.objects.create(id=1, category='Dummy', abbrivation='DMY') 
        self.sub_category1 = AssetSubCategory.objects.create(id=201, master_category=self.category, subcategory='SubCat One', abbrivation='SC1')
        self.sub_category2 = AssetSubCategory.objects.create(id=202, master_category=self.category, subcategory='SubCat Two', abbrivation='SC2')

    def test_list_view_htmx(self):
        response = self.client.get(reverse('asset_subcategory_list'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'asset_management/assetsubcategory/_assetsubcategory_table.html')
        self.assertContains(response, 'SubCat One')
        self.assertContains(response, 'SubCat Two')

    def test_create_view_get_htmx(self):
        response = self.client.get(reverse('asset_subcategory_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'asset_management/assetsubcategory/_assetsubcategory_form.html')
        self.assertContains(response, 'Add Asset Sub-Category')

    def test_create_view_post_htmx_success(self):
        data = {
            'master_category': self.category.pk,
            'subcategory': 'New SubCategory',
            'abbrivation': 'NSC'
        }
        response = self.client.post(reverse('asset_subcategory_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertTrue(AssetSubCategory.objects.filter(subcategory='New SubCategory').exists())
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshAssetSubCategoryList')

    def test_create_view_post_htmx_invalid_category(self):
        data = {
            'master_category': '1', # This is the "Please select category" value from ASP.NET inference
            'subcategory': 'Invalid SubCat',
            'abbrivation': 'IVSC'
        }
        response = self.client.post(reverse('asset_subcategory_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400)
        self.assertTemplateUsed(response, 'asset_management/assetsubcategory/_assetsubcategory_form.html')
        self.assertContains(response, 'Please select a valid category.')

    def test_create_view_post_htmx_duplicate_abbrivation(self):
        data = {
            'master_category': self.category.pk,
            'subcategory': 'Duplicate SubCat',
            'abbrivation': 'SC1' # Duplicate
        }
        response = self.client.post(reverse('asset_subcategory_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400)
        self.assertTemplateUsed(response, 'asset_management/assetsubcategory/_assetsubcategory_form.html')
        self.assertContains(response, 'Asset sub category with this Symbol already exists.') # Django's default
        self.assertContains(response, 'This symbol/abbreviation is already in use.') # Custom

    def test_update_view_get_htmx(self):
        response = self.client.get(reverse('asset_subcategory_edit', args=[self.sub_category1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'asset_management/assetsubcategory/_assetsubcategory_form.html')
        self.assertContains(response, 'Edit Asset Sub-Category')
        self.assertContains(response, self.sub_category1.subcategory)

    def test_update_view_post_htmx_success(self):
        data = {
            'master_category': self.category.pk,
            'subcategory': 'Updated SubCat',
            'abbrivation': 'USC'
        }
        response = self.client.post(reverse('asset_subcategory_edit', args=[self.sub_category1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.sub_category1.refresh_from_db()
        self.assertEqual(self.sub_category1.subcategory, 'Updated SubCat')
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshAssetSubCategoryList')

    def test_update_view_post_htmx_duplicate_abbrivation(self):
        data = {
            'master_category': self.category.pk,
            'subcategory': 'Another SubCat',
            'abbrivation': 'SC2' # Duplicate of another sub-category
        }
        response = self.client.post(reverse('asset_subcategory_edit', args=[self.sub_category1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400)
        self.assertTemplateUsed(response, 'asset_management/assetsubcategory/_assetsubcategory_form.html')
        self.assertContains(response, 'This symbol/abbreviation is already in use by another sub-category.')

    def test_delete_view_get_htmx(self):
        response = self.client.get(reverse('asset_subcategory_delete', args=[self.sub_category1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'asset_management/assetsubcategory/confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, self.sub_category1.subcategory)

    def test_delete_view_post_htmx_success(self):
        response = self.client.delete(reverse('asset_subcategory_delete', args=[self.sub_category1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(AssetSubCategory.objects.filter(pk=self.sub_category1.pk).exists())
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshAssetSubCategoryList')

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The provided templates already incorporate HTMX attributes and Alpine.js for dynamic interactions:

*   **HTMX:**
    *   `hx-get`, `hx-post`, `hx-delete`: Used for fetching and submitting forms, and deleting records.
    *   `hx-target="#modalContent"`: Ensures form/confirmation content is loaded into the modal.
    *   `hx-trigger="click"`: Triggers form/delete modal on button click.
    *   `hx-trigger="load, refresh[ModelName]List from:body"`: Automatically loads the DataTables content on page load and refreshes it after successful CRUD operations (triggered by `HX-Trigger` header from views).
    *   `hx-swap="innerHTML"`: Replaces the entire content of the target element (e.g., refreshing the table).
    *   `hx-swap="none"`: Used for form submissions to prevent target content from being swapped, allowing the server to handle the modal closing via `HX-Trigger` and 204 status.
    *   `hx-indicator`: Provides visual feedback during HTMX requests.
*   **Alpine.js:**
    *   `x-data="{ activeTab: 'category' }"`: Manages the state of the active tab.
    *   `@click="activeTab = 'category'"`: Changes the active tab on button click.
    *   `x-show="activeTab === 'category'"`: Conditionally displays tab content based on the `activeTab` state.
    *   `_ = "on click add .is-active to #modal"`: Alpine.js's "magic" syntax (via `alpine-morph` plugin or equivalent) to toggle modal visibility. `remove .is-active from me` on modal background click or after HTMX load.
*   **DataTables:**
    *   Integrated into `_assetcategory_table.html` and `_assetsubcategory_table.html` for client-side functionality.
    *   The `$(document).ready(function() { ... });` block ensures DataTables is initialized only after the table HTML is loaded into the DOM by HTMX.
    *   `pageLength` and `lengthMenu` are set for common pagination options.

**Example Flow:**
1.  User clicks "Add New Category" button.
2.  `hx-get="{% url 'asset_category_add' %}"` fetches the `_assetcategory_form.html` partial.
3.  `hx-target="#modalContent"` places the form inside the modal.
4.  `_="on click add .is-active to #modal"` shows the modal.
5.  User fills out the form and clicks "Save".
6.  `hx-post="{{ request.path }}"` submits the form.
7.  If successful, the view returns a `204 No Content` status with `HX-Trigger: refreshAssetCategoryList`.
8.  The HTMX client closes the modal (`on htmx:afterOnLoad from:body remove .is-active from me` on modal) and triggers `refreshAssetCategoryList` on the `body`.
9.  The `assetCategoryTable-container` (which has `hx-trigger="refreshAssetCategoryList from:body"`) re-fetches `{% url 'asset_category_list' %}`.
10. The updated table partial `_assetcategory_table.html` replaces the old table content, now including the new record.

This automated, component-based approach ensures a smooth, highly responsive user experience without traditional full-page reloads, significantly improving perceived performance and user satisfaction.

---

## Final Notes

This modernization plan provides a detailed, automated pathway for transitioning your ASP.NET Asset Management module to Django. By following these steps, you will achieve:

*   **Enhanced Performance:** HTMX and Alpine.js eliminate full-page reloads, leading to a snappier and more responsive user interface.
*   **Improved Maintainability:** Django's structured MVC-like architecture, combined with fat models and thin views, promotes clean code, making the application easier to understand, extend, and debug.
*   **Increased Scalability:** Django is designed for high-traffic applications, providing a robust foundation for future growth.
*   **Modern User Experience:** DataTables provides advanced data manipulation capabilities (sorting, searching, pagination) out-of-the-box, enhancing user interaction with large datasets.
*   **Reduced Development Time:** The component-based approach and reliance on Django's built-in features and robust libraries (HTMX, Alpine.js) significantly reduce manual coding efforts.
*   **Higher Code Quality:** Comprehensive testing ensures the reliability and correctness of the migrated functionality, reducing defects and improving overall application stability.

Remember to replace placeholders like `[APP_NAME]` (e.g., `asset_management`), `[FIELD1_LABEL]`, etc., with your specific values. This plan emphasizes the systematic conversion and integration of functionalities, setting the stage for an efficient and successful modernization initiative.