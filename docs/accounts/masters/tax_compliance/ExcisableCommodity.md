## ASP.NET to Django Conversion Script: Excisable Commodity Management

This modernization plan outlines the strategic transition of your ASP.NET Excisable Commodity management module to a modern Django-based solution. Our approach prioritizes automation, leveraging conversational AI, and focuses on delivering a robust, maintainable, and user-friendly application using the latest Django 5.0+ patterns.

**Business Value:**
By migrating this module to Django, you will benefit from:
*   **Enhanced Scalability:** Django's architecture is designed for growth, allowing your application to handle more users and data efficiently.
*   **Improved Maintainability:** Adopting modern Django patterns, including "fat models, thin views," simplifies code understanding, reduces bugs, and makes future development faster and less costly.
*   **Modern User Experience:** The use of HTMX and Alpine.js ensures a highly interactive and responsive interface, eliminating full-page reloads and providing a desktop-like experience in the browser.
*   **Cost Efficiency:** Automating much of the migration process and utilizing open-source technologies like Django, HTMX, Alpine.js, and DataTables significantly reduces development time and licensing costs.
*   **Future-Proofing:** Moving away from legacy ASP.NET ensures your application remains compatible with modern web standards and security practices, reducing technical debt.

This plan is structured to provide clear, actionable steps that can be communicated and executed through an AI-assisted automation pipeline, ensuring a systematic and efficient transition.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is located at `core/base.html`.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the `SqlDataSource1` definition and SQL commands within the ASP.NET code, we've identified the core database elements for this module.

*   **Table Name:** `tblExciseCommodity_Master`
*   **Columns:**
    *   `Id`: Integer, Primary Key (auto-incrementing).
    *   `Terms`: String (NVARCHAR from SQL context), representing the commodity term.

### Step 2: Identify Backend Functionality

The ASP.NET code implements standard CRUD (Create, Read, Update, Delete) operations for Excisable Commodities.

*   **Create (Add):** Triggered by the "Add" button in the `GridView` footer (`GridView1_RowCommand` for `CommandName="Add"`) and the "Add1" button in the `EmptyDataTemplate` (`CommandName="Add1"`). Both insert a new `Terms` entry into `tblExciseCommodity_Master`.
*   **Read (Select):** The `GridView` is populated using `SqlDataSource1`'s `SelectCommand="SELECT * FROM [tblExciseCommodity_Master] order by [Id] desc"`.
*   **Update (Edit):** Triggered by the "Edit" command in the `GridView`. The `GridView1_RowUpdating` event handles this, manually constructing an `UPDATE` SQL query.
*   **Delete:** Triggered by the "Delete" command in the `GridView`. `SqlDataSource1`'s `DeleteCommand` is used.
*   **Validation:** `RequiredFieldValidator` ensures the "Terms" field is not empty during insert/update operations.
*   **Message Display:** `lblMessage` is used to display success messages for record updates, deletions, and insertions.

### Step 3: Infer UI Components

The ASP.NET `.aspx` file primarily uses a `GridView` control for data display and interaction, along with standard input controls.

*   **Data Display:** `GridView1` is the main component, showing a list of excisable commodities with "SN" (Serial Number), "Terms," and "Edit"/"Delete" action columns.
*   **Input Fields:** `asp:TextBox` controls (`txtTerms1`, `txtTerms2`, `txtTerms3`) are used for entering/editing the "Terms" value.
*   **Action Buttons:** `asp:LinkButton` and `asp:Button` controls trigger the edit, delete, and insert operations.
*   **Client-Side Confirmation:** JavaScript functions (`confirmationAdd()`, `confirmationUpdate()`, `confirmationDelete()`) are called for client-side prompts before actions, which will be migrated to HTMX/Alpine.js.
*   **Styling:** References to `yui-datatable.css` and `StyleSheet.css` indicate custom styling, which will be replaced by Tailwind CSS and DataTables default styling in Django.

---

### Step 4: Generate Django Code

We will create a new Django application, for example, `accounts_master`, to house this module.

#### 4.1 Models (`accounts_master/models.py`)

This model represents the `tblExciseCommodity_Master` table. The `id` field is implicitly handled by Django, and `Terms` is mapped as a `CharField`.

```python
from django.db import models

class ExcisableCommodity(models.Model):
    """
    Represents an excisable commodity in the system.
    Maps to the existing tblExciseCommodity_Master database table.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)  # Existing primary key
    terms = models.CharField(db_column='Terms', max_length=255, unique=True, verbose_name="Terms")

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblExciseCommodity_Master'  # Maps to the existing table
        verbose_name = 'Excisable Commodity'
        verbose_name_plural = 'Excisable Commodities'
        ordering = ['-id'] # Matches the ASP.NET 'order by [Id] desc'

    def __str__(self):
        """
        String representation of the ExcisableCommodity object.
        """
        return self.terms

    def get_absolute_url(self):
        """
        Returns the URL to access a particular excisable commodity instance.
        """
        from django.urls import reverse
        return reverse('excisablecommodity_detail', args=[str(self.id)])

    # Business logic methods would go here if any complex operations were identified
    # For this simple CRUD, no complex logic is needed beyond what Django's ORM provides.
```

#### 4.2 Forms (`accounts_master/forms.py`)

A `ModelForm` is used to create and update `ExcisableCommodity` instances, automatically handling validation and saving based on the model definition.

```python
from django import forms
from .models import ExcisableCommodity

class ExcisableCommodityForm(forms.ModelForm):
    """
    Form for creating and updating ExcisableCommodity instances.
    """
    class Meta:
        model = ExcisableCommodity
        fields = ['terms'] # Only 'terms' is editable, 'id' is PK
        widgets = {
            'terms': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm '
                         'focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter commodity terms'
            }),
        }

    def clean_terms(self):
        """
        Custom validation for the 'terms' field to ensure uniqueness (case-insensitive).
        Django's unique=True handles exact matches, but this adds case-insensitivity.
        """
        terms = self.cleaned_data['terms']
        # Check for existing terms, excluding the current instance if it's an update
        qs = ExcisableCommodity.objects.filter(terms__iexact=terms)
        if self.instance.pk: # If updating an existing instance
            qs = qs.exclude(pk=self.instance.pk)
        if qs.exists():
            raise forms.ValidationError("An excisable commodity with these terms already exists.")
        return terms

```

#### 4.3 Views (`accounts_master/views.py`)

We'll use Django's Class-Based Views (CBVs) for all CRUD operations, ensuring thin views and HTMX-driven interactions. A separate view for the table partial is added for HTMX refreshing.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.template.loader import render_to_string # For HTMX partials
from .models import ExcisableCommodity
from .forms import ExcisableCommodityForm

class ExcisableCommodityListView(ListView):
    """
    Displays a list of all ExcisableCommodity instances.
    """
    model = ExcisableCommodity
    template_name = 'accounts_master/excisablecommodity/list.html'
    context_object_name = 'excisable_commodities' # Renamed for clarity

class ExcisableCommodityTablePartialView(ListView):
    """
    Renders only the table portion of the ExcisableCommodity list for HTMX updates.
    """
    model = ExcisableCommodity
    template_name = 'accounts_master/excisablecommodity/_table.html'
    context_object_name = 'excisable_commodities' # Renamed for clarity

class ExcisableCommodityCreateView(CreateView):
    """
    Handles the creation of new ExcisableCommodity instances.
    """
    model = ExcisableCommodity
    form_class = ExcisableCommodityForm
    template_name = 'accounts_master/excisablecommodity/_form.html' # This is a partial for modal
    success_url = reverse_lazy('excisablecommodity_list') # Fallback if not HTMX

    def form_valid(self, form):
        """
        Handles valid form submission, saves the object, and sends HTMX trigger.
        """
        response = super().form_valid(form)
        messages.success(self.request, 'Excisable Commodity added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content response with a trigger header
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshExcisableCommodityList'
                }
            )
        return response

    def form_invalid(self, form):
        """
        Handles invalid form submission, renders the form again with errors.
        """
        if self.request.headers.get('HX-Request'):
            return HttpResponse(render_to_string(self.template_name, {'form': form}, request=self.request))
        return super().form_invalid(form)


class ExcisableCommodityUpdateView(UpdateView):
    """
    Handles the updating of existing ExcisableCommodity instances.
    """
    model = ExcisableCommodity
    form_class = ExcisableCommodityForm
    template_name = 'accounts_master/excisablecommodity/_form.html' # This is a partial for modal
    success_url = reverse_lazy('excisablecommodity_list') # Fallback if not HTMX

    def form_valid(self, form):
        """
        Handles valid form submission, saves the object, and sends HTMX trigger.
        """
        response = super().form_valid(form)
        messages.success(self.request, 'Excisable Commodity updated successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content response with a trigger header
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshExcisableCommodityList'
                }
            )
        return response
    
    def form_invalid(self, form):
        """
        Handles invalid form submission, renders the form again with errors.
        """
        if self.request.headers.get('HX-Request'):
            return HttpResponse(render_to_string(self.template_name, {'form': form}, request=self.request))
        return super().form_invalid(form)


class ExcisableCommodityDeleteView(DeleteView):
    """
    Handles the deletion of ExcisableCommodity instances.
    """
    model = ExcisableCommodity
    template_name = 'accounts_master/excisablecommodity/_confirm_delete.html' # This is a partial for modal
    context_object_name = 'excisable_commodity'
    success_url = reverse_lazy('excisablecommodity_list') # Fallback if not HTMX

    def delete(self, request, *args, **kwargs):
        """
        Handles the deletion, sends HTMX trigger, and success message.
        """
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Excisable Commodity deleted successfully.')
        if request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content response with a trigger header
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshExcisableCommodityList'
                }
            )
        return response

```

#### 4.4 Templates (`accounts_master/templates/accounts_master/excisablecommodity/`)

All templates adhere to DRY principles, extend `core/base.html`, and heavily utilize HTMX and Alpine.js for dynamic interactions.

**`list.html`**:
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Excisable Commodities</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'excisablecommodity_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus-circle mr-2"></i> Add New Commodity
        </button>
    </div>

    <div id="excisablecommodityTable-container"
         hx-trigger="load, refreshExcisableCommodityList from:body"
         hx-get="{% url 'excisablecommodity_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="flex items-center justify-center h-48 bg-gray-50 rounded-lg shadow-sm">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="ml-4 text-gray-600">Loading Excisable Commodities...</p>
        </div>
    </div>

    <!-- Modal for form (Add/Edit) and delete confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-xl w-full mx-auto transform transition-all sm:my-8 sm:align-middle">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Include DataTables JS and its initialization -->
<script src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.3/js/dataTables.bootstrap5.min.js"></script>

<script>
    // No Alpine.js specific initialization required here, only for modal state management
    // The Alpine.js for modal behavior is inline via 'on click' _ statements
</script>
{% endblock %}

{% block extra_css %}
<!-- Include DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.3/css/dataTables.bootstrap5.min.css">
{% endblock %}
```

**`_table.html`**:
```html
<div class="overflow-x-auto shadow-md rounded-lg">
    <table id="excisablecommodityTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Terms</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in excisable_commodities %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.terms }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 transition duration-300 ease-in-out"
                        hx-get="{% url 'excisablecommodity_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md transition duration-300 ease-in-out"
                        hx-get="{% url 'excisablecommodity_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        <i class="fas fa-trash-alt"></i> Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="3" class="py-4 px-4 text-center text-gray-500">No excisable commodities found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Ensure jQuery is loaded for DataTables
    if (typeof jQuery !== 'undefined') {
        $(document).ready(function() {
            // Destroy existing DataTable instance if it exists to prevent reinitialization errors
            if ($.fn.DataTable.isDataTable('#excisablecommodityTable')) {
                $('#excisablecommodityTable').DataTable().destroy();
            }
            $('#excisablecommodityTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "searching": true,
                "ordering": true,
                "info": true,
                "paging": true
            });
        });
    } else {
        console.error("jQuery is not loaded. DataTables requires jQuery.");
    }
</script>
```

**`_form.html`**:
```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-800 mb-5 border-b pb-3">{{ form.instance.pk|yesno:'Edit,Add' }} Excisable Commodity</h3>
    <form hx-post="{{ request.path }}" hx-swap="outerHTML" hx-target="#modalContent">
        {% csrf_token %}

        <div class="mb-4">
            <label for="{{ form.terms.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                {{ form.terms.label }}
            </label>
            {{ form.terms }}
            {% if form.terms.errors %}
            <p class="text-red-600 text-sm mt-1">{{ form.terms.errors|join:", " }}</p>
            {% endif %}
            {% if form.non_field_errors %}
            <div class="text-red-600 text-sm mt-1">{{ form.non_field_errors|join:", " }}</div>
            {% endif %}
        </div>

        <div class="mt-8 flex justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
                <i class="fas fa-save mr-2"></i> Save
            </button>
        </div>
    </form>
</div>
```

**`_confirm_delete.html`**:
```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-800 mb-5 border-b pb-3">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the excisable commodity: <strong>{{ excisable_commodity.terms }}</strong>?</p>

    <div class="mt-8 flex justify-end space-x-4">
        <button
            type="button"
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-300 ease-in-out"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button
            hx-post="{% url 'excisablecommodity_delete' excisable_commodity.pk %}"
            hx-target="#modalContent"
            hx-swap="none"
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
            <i class="fas fa-trash-alt mr-2"></i> Delete
        </button>
    </div>
</div>
```

#### 4.5 URLs (`accounts_master/urls.py`)

These URL patterns define the routes for the `ExcisableCommodity` module within the `accounts_master` application.

```python
from django.urls import path
from .views import (
    ExcisableCommodityListView,
    ExcisableCommodityTablePartialView,
    ExcisableCommodityCreateView,
    ExcisableCommodityUpdateView,
    ExcisableCommodityDeleteView
)

urlpatterns = [
    # Main list view (loads the base page)
    path('excisable-commodities/', ExcisableCommodityListView.as_view(), name='excisablecommodity_list'),

    # HTMX endpoint for refreshing the table content
    path('excisable-commodities/table/', ExcisableCommodityTablePartialView.as_view(), name='excisablecommodity_table'),

    # HTMX endpoint for adding new commodity (loads form into modal)
    path('excisable-commodities/add/', ExcisableCommodityCreateView.as_view(), name='excisablecommodity_add'),

    # HTMX endpoint for editing commodity (loads form into modal)
    path('excisable-commodities/edit/<int:pk>/', ExcisableCommodityUpdateView.as_view(), name='excisablecommodity_edit'),

    # HTMX endpoint for deleting commodity (loads confirmation into modal)
    path('excisable-commodities/delete/<int:pk>/', ExcisableCommodityDeleteView.as_view(), name='excisablecommodity_delete'),
]
```

#### 4.6 Tests (`accounts_master/tests.py`)

Comprehensive unit tests for the model and integration tests for all views are provided to ensure functionality and maintain high test coverage.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from .models import ExcisableCommodity
from .forms import ExcisableCommodityForm

class ExcisableCommodityModelTest(TestCase):
    """
    Tests for the ExcisableCommodity model.
    """
    @classmethod
    def setUpTestData(cls):
        """
        Set up non-modified objects used by all test methods.
        """
        ExcisableCommodity.objects.create(id=1, terms='Test Commodity A')
        ExcisableCommodity.objects.create(id=2, terms='Test Commodity B')

    def test_id_field_properties(self):
        """
        Test that the 'id' field is correctly mapped and is the primary key.
        """
        commodity = ExcisableCommodity.objects.get(id=1)
        self.assertEqual(commodity.id, 1)
        self.assertTrue(ExcisableCommodity._meta.get_field('id').primary_key)
        self.assertEqual(ExcisableCommodity._meta.get_field('id').db_column, 'Id')

    def test_terms_field_properties(self):
        """
        Test that the 'terms' field is correctly mapped and has correct attributes.
        """
        commodity = ExcisableCommodity.objects.get(id=1)
        self.assertEqual(commodity.terms, 'Test Commodity A')
        self.assertEqual(ExcisableCommodity._meta.get_field('terms').db_column, 'Terms')
        self.assertEqual(ExcisableCommodity._meta.get_field('terms').max_length, 255)
        self.assertTrue(ExcisableCommodity._meta.get_field('terms').unique)
        self.assertEqual(ExcisableCommodity._meta.get_field('terms').verbose_name, 'Terms')

    def test_object_name_is_terms(self):
        """
        Test that the __str__ method returns the 'terms' value.
        """
        commodity = ExcisableCommodity.objects.get(id=1)
        expected_object_name = 'Test Commodity A'
        self.assertEqual(str(commodity), expected_object_name)

    def test_meta_options(self):
        """
        Test Meta class options for database mapping and verbosity.
        """
        self.assertFalse(ExcisableCommodity._meta.managed)
        self.assertEqual(ExcisableCommodity._meta.db_table, 'tblExciseCommodity_Master')
        self.assertEqual(ExcisableCommodity._meta.verbose_name, 'Excisable Commodity')
        self.assertEqual(ExcisableCommodity._meta.verbose_name_plural, 'Excisable Commodities')
        self.assertEqual(ExcisableCommodity._meta.ordering, ['-id'])

    def test_get_absolute_url(self):
        """
        Test the get_absolute_url method (though not directly used in this HTMX setup, good practice).
        """
        commodity = ExcisableCommodity.objects.get(id=1)
        self.assertEqual(commodity.get_absolute_url(), '/accounts_master/excisable-commodities/1/')


class ExcisableCommodityFormTest(TestCase):
    """
    Tests for the ExcisableCommodityForm.
    """
    @classmethod
    def setUpTestData(cls):
        ExcisableCommodity.objects.create(id=1, terms='Existing Term')

    def test_form_valid_data(self):
        """
        Test form with valid data.
        """
        form = ExcisableCommodityForm(data={'terms': 'New Term'})
        self.assertTrue(form.is_valid())

    def test_form_invalid_data_missing_terms(self):
        """
        Test form with missing 'terms' data.
        """
        form = ExcisableCommodityForm(data={})
        self.assertFalse(form.is_valid())
        self.assertIn('terms', form.errors)
        self.assertIn('This field is required.', form.errors['terms'])

    def test_form_invalid_data_duplicate_terms(self):
        """
        Test form with duplicate 'terms' data (case-insensitive).
        """
        form = ExcisableCommodityForm(data={'terms': 'existing term'}) # Case-insensitive duplicate
        self.assertFalse(form.is_valid())
        self.assertIn('terms', form.errors)
        self.assertIn('An excisable commodity with these terms already exists.', form.errors['terms'])

    def test_form_update_with_same_terms(self):
        """
        Test form update where the term remains the same (should be valid).
        """
        commodity = ExcisableCommodity.objects.get(id=1)
        form = ExcisableCommodityForm(instance=commodity, data={'terms': 'Existing Term'})
        self.assertTrue(form.is_valid())


class ExcisableCommodityViewsTest(TestCase):
    """
    Integration tests for ExcisableCommodity views.
    """
    @classmethod
    def setUpTestData(cls):
        """
        Set up test data for all view tests.
        """
        ExcisableCommodity.objects.create(id=1, terms='Test Commodity A')
        ExcisableCommodity.objects.create(id=2, terms='Test Commodity B')

    def setUp(self):
        """
        Set up client for each test method.
        """
        self.client = Client()

    def test_list_view_get(self):
        """
        Test the list view (main page) GET request.
        """
        response = self.client.get(reverse('excisablecommodity_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_master/excisablecommodity/list.html')
        # We don't check context directly in list.html because the table is loaded via HTMX

    def test_table_partial_view_get(self):
        """
        Test the HTMX partial view for the table.
        """
        response = self.client.get(reverse('excisablecommodity_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_master/excisablecommodity/_table.html')
        self.assertIn('excisable_commodities', response.context)
        self.assertEqual(len(response.context['excisable_commodities']), 2)
        self.assertContains(response, 'Test Commodity A')
        self.assertContains(response, 'Test Commodity B')

    def test_create_view_get_htmx(self):
        """
        Test GET request for create form via HTMX.
        """
        response = self.client.get(reverse('excisablecommodity_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_master/excisablecommodity/_form.html')
        self.assertIsInstance(response.context['form'], ExcisableCommodityForm)
        self.assertContains(response, 'Add Excisable Commodity') # Check for form title

    def test_create_view_post_valid_htmx(self):
        """
        Test POST request for valid create form via HTMX.
        """
        data = {'terms': 'New Commodity C'}
        response = self.client.post(reverse('excisablecommodity_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # 204 No Content for successful HTMX post
        self.assertEqual(response.headers['HX-Trigger'], 'refreshExcisableCommodityList')
        self.assertTrue(ExcisableCommodity.objects.filter(terms='New Commodity C').exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Excisable Commodity added successfully.')

    def test_create_view_post_invalid_htmx(self):
        """
        Test POST request for invalid create form via HTMX.
        """
        # Attempt to create with a duplicate term (case-insensitive)
        data = {'terms': 'test commodity A'}
        response = self.client.post(reverse('excisablecommodity_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Should re-render the form with errors
        self.assertTemplateUsed(response, 'accounts_master/excisablecommodity/_form.html')
        self.assertIn('An excisable commodity with these terms already exists.', response.content.decode())
        self.assertFalse(ExcisableCommodity.objects.filter(terms='test commodity A').exists()) # Should not have created

    def test_update_view_get_htmx(self):
        """
        Test GET request for update form via HTMX.
        """
        commodity = ExcisableCommodity.objects.get(id=1)
        response = self.client.get(reverse('excisablecommodity_edit', args=[commodity.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_master/excisablecommodity/_form.html')
        self.assertIsInstance(response.context['form'], ExcisableCommodityForm)
        self.assertEqual(response.context['form'].instance, commodity)
        self.assertContains(response, 'Edit Excisable Commodity') # Check for form title
        self.assertContains(response, 'Test Commodity A') # Check existing value

    def test_update_view_post_valid_htmx(self):
        """
        Test POST request for valid update form via HTMX.
        """
        commodity = ExcisableCommodity.objects.get(id=1)
        data = {'terms': 'Updated Commodity A'}
        response = self.client.post(reverse('excisablecommodity_edit', args=[commodity.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshExcisableCommodityList')
        commodity.refresh_from_db()
        self.assertEqual(commodity.terms, 'Updated Commodity A')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Excisable Commodity updated successfully.')

    def test_update_view_post_invalid_htmx(self):
        """
        Test POST request for invalid update form via HTMX (duplicate terms).
        """
        commodity_a = ExcisableCommodity.objects.get(id=1)
        # Try to update commodity A to have the terms of commodity B
        data = {'terms': 'Test Commodity B'}
        response = self.client.post(reverse('excisablecommodity_edit', args=[commodity_a.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Should re-render the form with errors
        self.assertTemplateUsed(response, 'accounts_master/excisablecommodity/_form.html')
        self.assertIn('An excisable commodity with these terms already exists.', response.content.decode())
        commodity_a.refresh_from_db()
        self.assertEqual(commodity_a.terms, 'Test Commodity A') # Should not have updated

    def test_delete_view_get_htmx(self):
        """
        Test GET request for delete confirmation via HTMX.
        """
        commodity = ExcisableCommodity.objects.get(id=1)
        response = self.client.get(reverse('excisablecommodity_delete', args=[commodity.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_master/excisablecommodity/_confirm_delete.html')
        self.assertIn('excisable_commodity', response.context)
        self.assertEqual(response.context['excisable_commodity'], commodity)
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, f'Are you sure you want to delete the excisable commodity: <strong>{commodity.terms}</strong>?')

    def test_delete_view_post_htmx(self):
        """
        Test POST request for delete via HTMX.
        """
        commodity = ExcisableCommodity.objects.get(id=1)
        response = self.client.post(reverse('excisablecommodity_delete', args=[commodity.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshExcisableCommodityList')
        self.assertFalse(ExcisableCommodity.objects.filter(id=1).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Excisable Commodity deleted successfully.')

    def test_delete_non_existent_commodity(self):
        """
        Test deleting a commodity that does not exist.
        """
        response = self.client.post(reverse('excisablecommodity_delete', args=[999]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 404) # Not Found
```

### Step 5: HTMX and Alpine.js Integration

As demonstrated in the templates and views above:

*   **HTMX for Dynamic Interactions:**
    *   The `list.html` uses `hx-get` to load the `_table.html` initially and on `refreshExcisableCommodityList` trigger.
    *   Add/Edit/Delete buttons on `_table.html` use `hx-get` to load `_form.html` or `_confirm_delete.html` into a modal.
    *   Forms in `_form.html` and deletion in `_confirm_delete.html` use `hx-post` for submission.
    *   Views return `HttpResponse(status=204)` with `HX-Trigger` headers to inform the client to refresh the list, avoiding full page reloads.
*   **Alpine.js for UI State Management:**
    *   A simple `_` (hyperscript, tightly integrated with HTMX) statement `on click add .is-active to #modal` is used to show the modal.
    *   Another `_` statement `on click if event.target.id == 'modal' remove .is-active from me` on the modal itself allows clicking outside to close it.
*   **DataTables for List Views:**
    *   The `_table.html` renders a standard HTML `<table>` with `id="excisablecommodityTable"`.
    *   A JavaScript block within `_table.html` initializes this table with `$(document).ready(function() { $('#excisablecommodityTable').DataTable(); });`, enabling client-side searching, sorting, and pagination.

### Final Notes

This comprehensive plan provides a clear roadmap for migrating the Excisable Commodity module to Django. By following these steps and utilizing the generated code, your organization can achieve a modern, efficient, and maintainable web application. The emphasis on AI-assisted automation means that much of this code can be generated and integrated systematically, reducing manual effort and potential for human error. The use of plain English aims to make this process transparent and understandable for all stakeholders.