This modernization plan details the transition of your ASP.NET TDS Code module to a robust, modern Django application. It emphasizes an automated, systematic approach to ensure efficiency and maintainability, delivering a significantly improved user experience and a more scalable technical foundation.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**

The ASP.NET code clearly interacts with a table named `tblAcc_TDSCode_Master`. Column details are extracted from the `GridView`'s `DataKeyNames`, `Eval()`/`Bind()` expressions, and the `INSERT`/`UPDATE` SQL commands within the C# code-behind.

**Inferred Database Details:**

- **Table Name:** `tblAcc_TDSCode_Master`
- **Columns:**
    - `Id`: Integer, Primary Key (implied from `DataKeyNames="Id"` and `WHERE Id = ...`). This will map to Django's `id` field.
    - `SectionNo`: String/Text (e.g., 'Sec 194C').
    - `NatureOfPayment`: String/Text (e.g., 'Payment to Contractors').
    - `PaymentRange`: Numeric, specifically a Decimal/Float type (as indicated by `Convert.ToDouble` and `CompareValidator Type="Double"`).
    - `PayToIndividual`: String/Text (although storing numeric values like '1%', '5%', it's treated as string in ASP.NET code).
    - `Others`: String/Text (similar to `PayToIndividual`, storing percentages like '2%', '10%').
    - `WithOutPAN`: String/Text (similar to `PayToIndividual`, storing percentages like '20%').

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**

The ASP.NET application provides a comprehensive set of data management operations:

-   **Create (Add):** New records are added via `GridView1_RowCommand` (both 'Add' from the footer and 'Add1' from the empty data template). Data is inserted into `tblAcc_TDSCode_Master`.
-   **Read (Retrieve):** Data is fetched using the `FillGrid()` method, which executes a stored procedure `[GetTDSCode]` to populate `GridView1`. This happens on page load and after any CRUD operation that causes a refresh.
-   **Update (Edit):** Existing records are modified through the `GridView1_RowUpdating` event, which executes a direct `UPDATE` SQL command on `tblAcc_TDSCode_Master`.
-   **Delete:** Records are removed via the `GridView1_RowDeleting` event, executing a `DELETE` SQL command.
-   **Validation:** Client-side and server-side validation are present using `RequiredFieldValidator` for all main input fields and `CompareValidator` to ensure `PaymentRange` is a valid number.
-   **Pagination:** The `GridView` handles pagination, indicating that large datasets are expected.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

The central UI component is the `asp:GridView` (`GridView1`), which provides a rich interactive data table experience in ASP.NET terms:

-   **Data Display:** It lists records in a tabular format.
-   **Inline Editing:** Users can edit specific rows directly within the grid.
-   **Inline Creation:** A footer row (and an `EmptyDataTemplate`) allows adding new records directly from the table interface.
-   **Actions:** Dedicated columns provide "Edit" and "Delete" buttons for each row, and an "Insert" button in the footer/empty template.
-   **Input Fields:** `asp:TextBox` controls are used for entering and editing `SectionNo`, `NatureOfPayment`, `PaymentRange`, `PayToIndividual`, `Others`, and `WithOutPAN`.
-   **Validation Indicators:** `asp:RequiredFieldValidator` and `asp:CompareValidator` provide immediate feedback on invalid input.
-   **Client-Side Scripting:** `OnClientClick` attributes on buttons point to JavaScript functions (`confirmationAdd()`, `confirmationUpdate()`, `confirmationDelete()`) for user confirmation dialogs, which will be replaced by HTMX/Alpine.js interactions.
-   **Styling:** CSS files like `yui-datatable.css` suggest a structured table presentation, which will be re-implemented using DataTables and Tailwind CSS.

### Step 4: Generate Django Code

Based on the analysis, here's the mapping to Django components for an application named `accounts`.

#### 4.1 Models
**File: `accounts/models.py`**

```python
from django.db import models

class TDSCode(models.Model):
    # 'id' is Django's default primary key, mapping to the 'Id' column in the existing table.
    # We explicitly define it with db_column='Id' and primary_key=True
    # As it's an existing auto-incrementing column, AutoField is appropriate.
    id = models.AutoField(db_column='Id', primary_key=True)
    section_no = models.CharField(db_column='SectionNo', max_length=100) # Inferred max_length
    nature_of_payment = models.CharField(db_column='NatureOfPayment', max_length=255) # Inferred max_length
    # Using DecimalField for precision, assuming PaymentRange is a monetary or rate value
    payment_range = models.DecimalField(db_column='PaymentRange', max_digits=10, decimal_places=2)
    # Keeping these as CharField as per ASP.NET's handling (e.g., '1%', '20%')
    # If they represent percentages for calculation, consider DecimalField with appropriate conversion.
    pay_to_individual = models.CharField(db_column='PayToIndividual', max_length=50)
    others = models.CharField(db_column='Others', max_length=50)
    with_out_pan = models.CharField(db_column='WithOutPAN', max_length=50)

    class Meta:
        # Crucial: Tells Django not to manage this table's schema (e.g., no migrations)
        managed = False
        # The exact database table name from ASP.NET
        db_table = 'tblAcc_TDSCode_Master'
        # Human-readable names for the Django Admin and other contexts
        verbose_name = 'TDS Code'
        verbose_name_plural = 'TDS Codes'

    def __str__(self):
        """Returns a user-friendly string representation of the TDSCode."""
        return f"{self.section_no} - {self.nature_of_payment}"

    # Example of a 'Fat Model' method for business logic, if needed in the future.
    # Currently, most logic is direct CRUD, but complex calculations or validations
    # related to TDS rates or sections would reside here.
    def calculate_tds_amount(self, taxable_amount, pan_available=True, is_individual=True):
        """
        Calculates TDS based on the code's rates and amount.
        This is a placeholder for potential complex business logic.
        """
        rate = 0.0
        if not pan_available:
            rate = float(self.with_out_pan.strip('%')) / 100
        elif is_individual:
            rate = float(self.pay_to_individual.strip('%')) / 100
        else:
            rate = float(self.others.strip('%')) / 100
        
        # Ensure threshold is met (example logic based on PaymentRange)
        if taxable_amount < self.payment_range:
            return 0.0 # No TDS if below threshold

        return taxable_amount * rate

```

#### 4.2 Forms
**File: `accounts/forms.py`**

```python
from django import forms
from .models import TDSCode
from django.core.exceptions import ValidationError

class TDSCodeForm(forms.ModelForm):
    """
    Django ModelForm for creating and updating TDSCode instances.
    Maps to ASP.NET's input fields and validation.
    """
    class Meta:
        model = TDSCode
        fields = ['section_no', 'nature_of_payment', 'payment_range', 'pay_to_individual', 'others', 'with_out_pan']
        widgets = {
            'section_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'nature_of_payment': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'payment_range': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01'}), # Added step for decimal
            'pay_to_individual': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'others': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'with_out_pan': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'section_no': 'Section No',
            'nature_of_payment': 'Nature Of Payment',
            'payment_range': 'Payment Range',
            'pay_to_individual': 'Individual/HUF',
            'others': 'Others',
            'with_out_pan': 'WithOut PAN',
        }

    # Custom validation logic can be added here, mirroring ASP.NET's RequiredFieldValidator
    # and CompareValidator. Django's form fields handle most basic validation (e.g., required, type).
    def clean_pay_to_individual(self):
        # Example: Basic validation to ensure it's a valid percentage or number string
        value = self.cleaned_data.get('pay_to_individual')
        if not value:
            raise ValidationError("This field is required.")
        # Add more specific validation if '1%' needs to be numeric.
        # e.g., try: float(value.strip('%'))
        return value

    def clean_others(self):
        value = self.cleaned_data.get('others')
        if not value:
            raise ValidationError("This field is required.")
        return value
    
    def clean_with_out_pan(self):
        value = self.cleaned_data.get('with_out_pan')
        if not value:
            raise ValidationError("This field is required.")
        return value

```

#### 4.3 Views
**File: `accounts/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.template.loader import render_to_string # Useful if rendering partials manually
from .models import TDSCode
from .forms import TDSCodeForm

# Helper function to send HTMX-specific responses and messages
def _htmx_response(request, success_message, trigger_event='refreshTDSCodeList'):
    """
    Handles HTMX responses for CRUD operations, sending a 204 No Content status
    and triggering a client-side event to refresh the table.
    Also adds a success message to Django's messages framework.
    """
    messages.success(request, success_message)
    # The X-Messages header is handled by django-htmx's HtmxMessageMiddleware
    # which captures Django messages and sends them to the client.
    return HttpResponse(
        status=204,  # No Content, tells HTMX to do nothing with the response body
        headers={
            'HX-Trigger': trigger_event # Custom event to refresh the TDSCode list
        }
    )

class TDSCodeListView(ListView):
    """
    Displays a list of all TDSCode objects.
    Serves the initial full page load with the container for the HTMX-loaded table.
    """
    model = TDSCode
    template_name = 'accounts/tdscode/list.html'
    context_object_name = 'tdscodes' # Name for the queryset in the template

class TDSCodeTablePartialView(ListView):
    """
    Renders only the TDS Code table content.
    This view is specifically targeted by HTMX requests to dynamically update the table.
    """
    model = TDSCode
    template_name = 'accounts/tdscode/_tdscode_table.html' # Partial template
    context_object_name = 'tdscodes'

    def get_queryset(self):
        # DataTables handles client-side filtering/sorting, so we just pass all objects.
        # Ensure a consistent order for "SN" (serial number) in the template.
        return super().get_queryset().order_by('id')


class TDSCodeCreateView(CreateView):
    """
    Handles the creation of new TDSCode objects.
    Renders a form within a modal and processes HTMX POST requests.
    """
    model = TDSCode
    form_class = TDSCodeForm
    template_name = 'accounts/tdscode/_tdscode_form.html' # Use partial template for modal
    # success_url is a fallback for non-HTMX requests, HTMX uses HX-Trigger
    success_url = reverse_lazy('tdscode_list')

    def form_valid(self, form):
        """Processes a valid form submission."""
        # Save the new object
        response = super().form_valid(form)
        # Return HTMX-specific response to trigger UI refresh and close modal
        return _htmx_response(self.request, 'TDS Code added successfully.')

    def form_invalid(self, form):
        """Processes an invalid form submission."""
        # If HTMX request, re-render the form with errors for the modal
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        # For non-HTMX requests, fallback to default behavior (render full page with errors)
        return super().form_invalid(form)


class TDSCodeUpdateView(UpdateView):
    """
    Handles updating existing TDSCode objects.
    Renders a form within a modal and processes HTMX POST requests.
    """
    model = TDSCode
    form_class = TDSCodeForm
    template_name = 'accounts/tdscode/_tdscode_form.html' # Use partial template for modal
    success_url = reverse_lazy('tdscode_list') # Fallback

    def form_valid(self, form):
        """Processes a valid form submission."""
        response = super().form_valid(form)
        return _htmx_response(self.request, 'TDS Code updated successfully.')

    def form_invalid(self, form):
        """Processes an invalid form submission."""
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


class TDSCodeDeleteView(DeleteView):
    """
    Handles deleting TDSCode objects.
    Renders a confirmation prompt within a modal and processes HTMX POST requests.
    """
    model = TDSCode
    template_name = 'accounts/tdscode/_tdscode_confirm_delete.html' # Use partial template for modal
    success_url = reverse_lazy('tdscode_list') # Fallback

    def delete(self, request, *args, **kwargs):
        """Performs the deletion and returns an HTMX response."""
        response = super().delete(request, *args, **kwargs)
        return _htmx_response(request, 'TDS Code deleted successfully.')

```

#### 4.4 Templates
**Directory Structure:** Create `templates/accounts/tdscode/` inside your `accounts` app.

**File: `accounts/templates/accounts/tdscode/list.html`**
This is the main page that loads the DataTables content via HTMX.

```html
{% extends 'core/base.html' %} {# Assumes core/base.html exists and sets up HTMX, Alpine.js, jQuery, and DataTables CDNs #}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-3xl font-extrabold text-gray-900">TDS Codes Management</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-lg transition duration-300 ease-in-out transform hover:scale-105"
            hx-get="{% url 'tdscode_add' %}" {# HTMX GET request to load the add form #}
            hx-target="#modalContent" {# Target the modal's content area #}
            hx-trigger="click" {# Trigger on click #}
            _="on click add .is-active to #modal"> {# Alpine.js/Hyperscript to show the modal #}
            <i class="fas fa-plus-circle mr-2"></i> Add New TDS Code
        </button>
    </div>

    {# Container for the DataTables, dynamically loaded/refreshed by HTMX #}
    <div id="tdscodeTable-container"
         hx-trigger="load, refreshTDSCodeList from:body" {# Load on page load, and whenever 'refreshTDSCodeList' event is triggered #}
         hx-get="{% url 'tdscode_table' %}" {# HTMX GET request to fetch the table partial #}
         hx-swap="innerHTML"> {# Replace the inner HTML of this div #}
        <!-- Initial loading indicator -->
        <div class="text-center p-10">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading TDS codes data...</p>
        </div>
    </div>

    {# Universal Modal Structure for Add/Edit/Delete Forms #}
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 hidden transition-opacity duration-300 ease-in-out"
         _="on click if event.target.id == 'modal' remove .is-active from me"> {# Click outside to close #}
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-3xl w-full mx-4 transform transition-all duration-300 ease-in-out scale-95 opacity-0"
             _="on load add .scale-100 .opacity-100 to #modalContent"> {# Simple animation on load #}
            <!-- HTMX will load form/confirmation content here -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# JavaScript for handling HTMX responses, particularly messages #}
<script>
    // Listen for htmx:afterSwap to initialize DataTables on dynamically loaded content
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'tdscodeTable-container') {
             // Ensure DataTables is initialized on the new table content
            var table = $('#tdscodeTable');
            if (!$.fn.DataTable.isDataTable(table)) {
                table.DataTable({
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "responsive": true, // Make table responsive
                    "searching": true // Enable search box
                });
            }
        }
        if (event.detail.target.id === 'modalContent') {
            // Re-initialize Alpine.js for newly loaded modal content if needed
            if (window.Alpine) {
                window.Alpine.initTree(event.detail.target);
            }
        }
    });

    // Custom event listener for showing Django messages from HTMX response headers
    document.body.addEventListener('htmx:afterOnLoad', function(event) {
        // Check for 'X-Messages' header, which contains JSON-encoded Django messages
        const messagesHeader = event.detail.xhr.getResponseHeader('X-Messages');
        if (messagesHeader) {
            try {
                const messages = JSON.parse(messagesHeader);
                const messagesContainer = document.getElementById('messages-container');
                if (messagesContainer) {
                    messages.forEach(msg => {
                        // msg[0] is the level tag (e.g., 'success', 'error'), msg[1] is the text
                        let alertClass = 'bg-green-100 border border-green-400 text-green-700';
                        if (msg[0] === 'error') alertClass = 'bg-red-100 border border-red-400 text-red-700';
                        if (msg[0] === 'warning') alertClass = 'bg-yellow-100 border border-yellow-400 text-yellow-700';
                        if (msg[0] === 'info') alertClass = 'bg-blue-100 border border-blue-400 text-blue-700';

                        const alertHtml = `
                            <div class="${alertClass} px-4 py-3 rounded relative mb-4 shadow-sm" role="alert">
                                <span class="block sm:inline font-medium">${msg[1]}</span>
                                <span class="absolute top-0 bottom-0 right-0 px-4 py-3">
                                    <svg class="fill-current h-6 w-6 text-gray-500 cursor-pointer hover:text-gray-700" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" onclick="this.parentElement.parentElement.remove();">
                                        <title>Close</title>
                                        <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.103l-2.651 3.746a1.2 1.2 0 0 1-1.697-1.697l3.746-2.651-3.746-2.651a1.2 1.2 0 0 1 1.697-1.697L10 8.897l2.651-3.746a1.2 1.2 0 0 1 1.697 1.697L11.103 10l3.746 2.651a1.2 1.2 0 0 1 0 1.698z"/>
                                    </svg>
                                </span>
                            </div>`;
                        messagesContainer.insertAdjacentHTML('afterbegin', alertHtml);
                    });
                }
            } catch (e) {
                console.error("Error parsing X-Messages header:", e);
            }
        }
    });
</script>
{% endblock %}
```

**File: `accounts/templates/accounts/tdscode/_tdscode_table.html`**
This partial template contains the actual DataTables structure, designed to be loaded by HTMX.

```html
<div class="bg-white shadow-lg rounded-lg overflow-hidden border border-gray-200">
    <table id="tdscodeTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-100">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Section No</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Nature Of Payment</th>
                <th class="py-3 px-4 text-right text-xs font-semibold text-gray-700 uppercase tracking-wider">Payment Range</th>
                <th class="py-3 px-4 text-center text-xs font-semibold text-gray-700 uppercase tracking-wider">Individual/HUF</th>
                <th class="py-3 px-4 text-center text-xs font-semibold text-gray-700 uppercase tracking-wider">Others</th>
                <th class="py-3 px-4 text-center text-xs font-semibold text-gray-700 uppercase tracking-wider">WithOut PAN</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in tdscodes %}
            <tr class="hover:bg-gray-50 transition-colors duration-150 ease-in-out">
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800 text-right">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ obj.section_no }}</td>
                <td class="py-3 px-4 text-sm text-gray-800 max-w-xs truncate">{{ obj.nature_of_payment }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800 text-right">{{ obj.payment_range }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800 text-center">{{ obj.pay_to_individual }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800 text-center">{{ obj.others }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800 text-center">{{ obj.with_out_pan }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1.5 px-3 rounded-md text-xs mr-2 transition duration-300 ease-in-out shadow-sm hover:shadow-md"
                        hx-get="{% url 'tdscode_edit' obj.pk %}" {# HTMX GET request to load edit form #}
                        hx-target="#modalContent" {# Target the modal's content area #}
                        hx-trigger="click"
                        _="on click add .is-active to #modal"> {# Show the modal #}
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button
                        class="bg-red-600 hover:bg-red-700 text-white font-bold py-1.5 px-3 rounded-md text-xs transition duration-300 ease-in-out shadow-sm hover:shadow-md"
                        hx-get="{% url 'tdscode_delete' obj.pk %}" {# HTMX GET request to load delete confirmation #}
                        hx-target="#modalContent" {# Target the modal's content area #}
                        hx-trigger="click"
                        _="on click add .is-active to #modal"> {# Show the modal #}
                        <i class="fas fa-trash-alt"></i> Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="text-center py-6 text-gray-500 text-lg">No TDS codes found. Click "Add New TDS Code" to get started.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

{# DataTables initialization script. This runs when the table partial is loaded into the DOM. #}
<script>
$(document).ready(function() {
    // Only initialize DataTable if it hasn't been initialized already (important for HTMX re-swaps)
    var table = $('#tdscodeTable');
    if (!$.fn.DataTable.isDataTable(table)) {
        table.DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true, // Ensures responsiveness on smaller screens
            "searching": true, // Enables the search/filter box
            "columnDefs": [
                { "orderable": false, "targets": [0, 7] } // Disable ordering for SN and Actions columns
            ]
        });
    }
});
</script>
```

**File: `accounts/templates/accounts/tdscode/_tdscode_form.html`**
This partial template renders the form for both creation and updates, loaded into the modal.

```html
<div class="p-8">
    <h3 class="text-2xl font-bold text-gray-900 mb-6 border-b pb-4">{{ form.instance.pk|yesno:'Edit,Add' }} TDS Code</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500 text-xs font-semibold ml-1">*</span>{% endif %}
                </label>
                {{ field }} {# Renders the Django form field with its widget and applied CSS classes #}
                {% if field.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600 list-disc pl-5">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="px-6 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal"> {# Closes the modal on cancel #}
                Cancel
            </button>
            <button
                type="submit"
                class="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-300 ease-in-out">
                Save
            </button>
            {# HTMX Indicator for showing loading spinner during form submission #}
            <span id="form-spinner" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
            </span>
        </div>
    </form>
</div>
```

**File: `accounts/templates/accounts/tdscode/_tdscode_confirm_delete.html`**
This partial template provides the delete confirmation, loaded into the modal.

```html
<div class="p-8 text-center">
    <h3 class="text-2xl font-bold text-gray-900 mb-4 border-b pb-4">Confirm Deletion</h3>
    <p class="text-gray-700 text-lg mb-8">
        Are you absolutely sure you want to delete the TDS Code:
        <br><span class="font-bold text-red-700">{{ object.section_no }} - {{ object.nature_of_payment }}</span>?
        <br>This action cannot be undone.
    </p>

    <form hx-post="{% url 'tdscode_delete' object.pk %}" hx-swap="none"> {# HTMX POST request to delete #}
        {% csrf_token %}
        <div class="flex justify-center space-x-4">
            <button
                type="button"
                class="px-6 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal"> {# Closes the modal on cancel #}
                Cancel
            </button>
            <button
                type="submit"
                class="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-300 ease-in-out">
                <i class="fas fa-trash-alt mr-2"></i> Confirm Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs
**File: `accounts/urls.py`**
This defines the URL routing for your TDS Code module.

```python
from django.urls import path
from .views import TDSCodeListView, TDSCodeCreateView, TDSCodeUpdateView, TDSCodeDeleteView, TDSCodeTablePartialView

urlpatterns = [
    # Main list view (full page load)
    path('tdscodes/', TDSCodeListView.as_view(), name='tdscode_list'),
    # HTMX endpoint for the table content (partial load)
    path('tdscodes/table/', TDSCodeTablePartialView.as_view(), name='tdscode_table'),
    # HTMX endpoint for the add form (loaded into modal)
    path('tdscodes/add/', TDSCodeCreateView.as_view(), name='tdscode_add'),
    # HTMX endpoint for the edit form (loaded into modal)
    path('tdscodes/edit/<int:pk>/', TDSCodeUpdateView.as_view(), name='tdscode_edit'),
    # HTMX endpoint for the delete confirmation (loaded into modal)
    path('tdscodes/delete/<int:pk>/', TDSCodeDeleteView.as_view(), name='tdscode_delete'),
]

```
**Project `urls.py` (e.g., `myproject/urls.py`)**

```python
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('accounts/', include('accounts.urls')), # Include your new accounts app's URLs
    # Add other project-level URLs here
]
```

#### 4.6 Tests
**File: `accounts/tests.py`**
Comprehensive tests for both model logic and view interactions, ensuring robust functionality.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import TDSCode
from .forms import TDSCodeForm
import json # Used to parse HTMX-specific headers like X-Messages

class TDSCodeModelTest(TestCase):
    """
    Unit tests for the TDSCode model.
    Verifies model field mapping, string representation, and custom methods.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a single TDSCode instance to be used across model tests
        cls.tds_code_instance = TDSCode.objects.create(
            section_no='Sec 194C',
            nature_of_payment='Payment to Contractors',
            payment_range=20000.00, # DecimalField value
            pay_to_individual='1.00%', # Stored as string, match inferred type
            others='2.00%',
            with_out_pan='20.00%'
        )

    def test_tds_code_creation(self):
        """Ensure a TDSCode object can be created and its attributes are correct."""
        tds_code = TDSCode.objects.get(id=self.tds_code_instance.id)
        self.assertEqual(tds_code.section_no, 'Sec 194C')
        self.assertEqual(tds_code.nature_of_payment, 'Payment to Contractors')
        self.assertEqual(str(tds_code.payment_range), '20000.00') # DecimalField stores as string representation
        self.assertEqual(tds_code.pay_to_individual, '1.00%')
        self.assertEqual(tds_code.others, '2.00%')
        self.assertEqual(tds_code.with_out_pan, '20.00%')

    def test_str_method(self):
        """Verify the __str__ method provides a meaningful representation."""
        expected_str = "Sec 194C - Payment to Contractors"
        self.assertEqual(str(self.tds_code_instance), expected_str)

    def test_verbose_names(self):
        """Check if verbose names are correctly set for model fields."""
        field_labels = {
            'section_no': 'Section No',
            'nature_of_payment': 'Nature Of Payment',
            'payment_range': 'Payment Range',
            'pay_to_individual': 'Individual/HUF',
            'others': 'Others',
            'with_out_pan': 'WithOut PAN',
        }
        for field, expected_label in field_labels.items():
            self.assertEqual(self.tds_code_instance._meta.get_field(field).verbose_name, expected_label)

    def test_calculate_tds_amount_method(self):
        """Test the example business logic method 'calculate_tds_amount'."""
        # Test case 1: Below threshold
        self.assertEqual(self.tds_code_instance.calculate_tds_amount(15000.00), 0.0)

        # Test case 2: PAN available, individual
        # 1.00% of 25000 = 250.00
        self.assertAlmostEqual(self.tds_code_instance.calculate_tds_amount(25000.00, pan_available=True, is_individual=True), 250.00)

        # Test case 3: PAN available, others
        # 2.00% of 25000 = 500.00
        self.assertAlmostEqual(self.tds_code_instance.calculate_tds_amount(25000.00, pan_available=True, is_individual=False), 500.00)

        # Test case 4: Without PAN
        # 20.00% of 25000 = 5000.00
        self.assertAlmostEqual(self.tds_code_instance.calculate_tds_amount(25000.00, pan_available=False), 5000.00)

class TDSCodeViewsTest(TestCase):
    """
    Integration tests for TDSCode views, covering HTTP responses,
    template usage, and HTMX interactions.
    """
    @classmethod
    def setUpTestData(cls):
        # Create initial data for view tests
        cls.tds_code_1 = TDSCode.objects.create(
            section_no='Sec 194A',
            nature_of_payment='Interest other than interest on securities',
            payment_range=10000.00,
            pay_to_individual='5%',
            others='10%',
            with_out_pan='20%'
        )
        cls.tds_code_2 = TDSCode.objects.create(
            section_no='Sec 194B',
            nature_of_payment='Winnings from lottery',
            payment_range=10000.00,
            pay_to_individual='30%',
            others='30%',
            with_out_pan='30%'
        )

    def setUp(self):
        """Set up a new test client for each test method."""
        self.client = Client()
        # HTMX headers are common for modal interactions
        self.htmx_headers = {'HTTP_HX_REQUEST': 'true'}

    def test_list_view_get(self):
        """Test accessing the main TDSCode list page."""
        response = self.client.get(reverse('tdscode_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/tdscode/list.html')
        self.assertContains(response, 'TDS Codes Management') # Verify content

    def test_table_partial_view_get_htmx(self):
        """Test HTMX request for the TDSCode table partial."""
        response = self.client.get(reverse('tdscode_table'), **self.htmx_headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/tdscode/_tdscode_table.html')
        self.assertContains(response, self.tds_code_1.section_no)
        self.assertContains(response, self.tds_code_2.nature_of_payment)
        self.assertIn('tdscodes', response.context)
        self.assertEqual(len(response.context['tdscodes']), 2) # Check if all objects are passed

    def test_create_view_get_htmx(self):
        """Test HTMX request for the create form modal."""
        response = self.client.get(reverse('tdscode_add'), **self.htmx_headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/tdscode/_tdscode_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add TDS Code')

    def test_create_view_post_success_htmx(self):
        """Test successful creation of a TDSCode via HTMX POST."""
        initial_count = TDSCode.objects.count()
        data = {
            'section_no': 'Sec 194J',
            'nature_of_payment': 'Fees for professional services',
            'payment_range': '30000.00',
            'pay_to_individual': '10%',
            'others': '10%',
            'with_out_pan': '20%',
        }
        response = self.client.post(reverse('tdscode_add'), data, **self.htmx_headers)

        self.assertEqual(response.status_code, 204) # HTMX success code (No Content)
        self.assertEqual(TDSCode.objects.count(), initial_count + 1)
        self.assertTrue(TDSCode.objects.filter(section_no='Sec 194J').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshTDSCodeList')

        messages = json.loads(response.headers.get('X-Messages', '[]'))
        self.assertIn(['success', 'TDS Code added successfully.'], messages)

    def test_create_view_post_invalid_htmx(self):
        """Test invalid creation of a TDSCode via HTMX POST (form errors)."""
        initial_count = TDSCode.objects.count()
        data = {
            'section_no': '', # Invalid: required field
            'nature_of_payment': 'Some Payment',
            'payment_range': 'not_a_number', # Invalid type
            'pay_to_individual': '5%',
            'others': '10%',
            'with_out_pan': '', # Invalid: required field
        }
        response = self.client.post(reverse('tdscode_add'), data, **self.htmx_headers)

        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'accounts/tdscode/_tdscode_form.html')
        self.assertIn('form', response.context)
        self.assertFormError(response, 'form', 'section_no', ['This field cannot be blank.'])
        self.assertFormError(response, 'form', 'payment_range', ['Enter a number.'])
        self.assertFormError(response, 'form', 'with_out_pan', ['This field is required.'])
        self.assertEqual(TDSCode.objects.count(), initial_count) # No object created

    def test_update_view_get_htmx(self):
        """Test HTMX request for the update form modal."""
        response = self.client.get(reverse('tdscode_edit', args=[self.tds_code_1.pk]), **self.htmx_headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/tdscode/_tdscode_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.tds_code_1)
        self.assertContains(response, 'Edit TDS Code')

    def test_update_view_post_success_htmx(self):
        """Test successful update of a TDSCode via HTMX POST."""
        updated_nature = 'Updated Nature of Payment Test'
        data = {
            'section_no': self.tds_code_1.section_no,
            'nature_of_payment': updated_nature,
            'payment_range': str(self.tds_code_1.payment_range),
            'pay_to_individual': self.tds_code_1.pay_to_individual,
            'others': self.tds_code_1.others,
            'with_out_pan': self.tds_code_1.with_out_pan,
        }
        response = self.client.post(reverse('tdscode_edit', args=[self.tds_code_1.pk]), data, **self.htmx_headers)

        self.assertEqual(response.status_code, 204)
        self.tds_code_1.refresh_from_db() # Reload object to get updated data
        self.assertEqual(self.tds_code_1.nature_of_payment, updated_nature)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshTDSCodeList')

        messages = json.loads(response.headers.get('X-Messages', '[]'))
        self.assertIn(['success', 'TDS Code updated successfully.'], messages)

    def test_update_view_post_invalid_htmx(self):
        """Test invalid update of a TDSCode via HTMX POST."""
        original_nature = self.tds_code_1.nature_of_payment
        data = {
            'section_no': self.tds_code_1.section_no,
            'nature_of_payment': '', # Invalid: required
            'payment_range': str(self.tds_code_1.payment_range),
            'pay_to_individual': self.tds_code_1.pay_to_individual,
            'others': self.tds_code_1.others,
            'with_out_pan': self.tds_code_1.with_out_pan,
        }
        response = self.client.post(reverse('tdscode_edit', args=[self.tds_code_1.pk]), data, **self.htmx_headers)

        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'accounts/tdscode/_tdscode_form.html')
        self.assertFormError(response, 'form', 'nature_of_payment', ['This field cannot be blank.'])
        self.tds_code_1.refresh_from_db() # Ensure data was NOT changed
        self.assertEqual(self.tds_code_1.nature_of_payment, original_nature)

    def test_delete_view_get_htmx(self):
        """Test HTMX request for the delete confirmation modal."""
        response = self.client.get(reverse('tdscode_delete', args=[self.tds_code_1.pk]), **self.htmx_headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/tdscode/_tdscode_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.tds_code_1)
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, self.tds_code_1.section_no)

    def test_delete_view_post_success_htmx(self):
        """Test successful deletion of a TDSCode via HTMX POST."""
        initial_count = TDSCode.objects.count()
        # Create a new object to delete so we don't interfere with other tests
        tds_to_delete = TDSCode.objects.create(
            section_no='Sec 194K', nature_of_payment='Unit income', payment_range=5000.00,
            pay_to_individual='10%', others='10%', with_out_pan='20%'
        )
        response = self.client.post(reverse('tdscode_delete', args=[tds_to_delete.pk]), **self.htmx_headers)

        self.assertEqual(response.status_code, 204)
        self.assertEqual(TDSCode.objects.count(), initial_count) # Should be initial_count (2)
        self.assertFalse(TDSCode.objects.filter(pk=tds_to_delete.pk).exists()) # Verify it's deleted

        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshTDSCodeList')

        messages = json.loads(response.headers.get('X-Messages', '[]'))
        self.assertIn(['success', 'TDS Code deleted successfully.'], messages)

    def test_delete_view_post_non_existent_htmx(self):
        """Test deleting a non-existent TDSCode via HTMX POST."""
        initial_count = TDSCode.objects.count()
        response = self.client.post(reverse('tdscode_delete', args=[999]), **self.htmx_headers) # Use a non-existent PK

        self.assertEqual(response.status_code, 404) # Django's default for object not found
        self.assertEqual(TDSCode.objects.count(), initial_count) # No change in count

```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**

-   **HTMX for Dynamic Interactions:**
    -   **Initial Load & Refresh:** The `tdscodeTable-container` in `list.html` uses `hx-trigger="load, refreshTDSCodeList from:body"` and `hx-get="{% url 'tdscode_table' %}"` to fetch and render the entire table content (`_tdscode_table.html`) dynamically. This replaces the ASP.NET `FillGrid()` postback.
    -   **Modal Forms (Add/Edit/Delete):** Buttons for "Add New", "Edit", and "Delete" (in `list.html` and `_tdscode_table.html`) use `hx-get` to load the respective partial templates (`_tdscode_form.html` or `_tdscode_confirm_delete.html`) into the `#modalContent` div.
    -   **Form Submission:** Forms within the modal use `hx-post` to submit data. `hx-swap="none"` is used because the Django views will return a `204 No Content` response, which HTMX interprets as a signal to not update the DOM, but to process headers.
    -   **Post-CRUD Refresh:** The Django views, after successful CRUD operations, include an `HX-Trigger: refreshTDSCodeList` header in their `204` response. This tells HTMX on the client side to trigger the `refreshTDSCodeList` event, which in turn causes the `tdscodeTable-container` to re-fetch and re-render the table, keeping the list up-to-date without a full page reload.
    -   **Loading Indicators:** `hx-indicator="#form-spinner"` is used on forms to show a spinning icon (`<span id="form-spinner" class="htmx-indicator">...</span>`) during submission, providing visual feedback to the user.
    -   **Messages:** Django's `messages` framework is integrated with HTMX. The `_htmx_response` helper ensures success messages are attached to `HX-Response-Headers` via `django-htmx.middleware.HtmxMessageMiddleware` (needs to be configured in `settings.py`). A JavaScript snippet in `list.html` listens for `htmx:afterOnLoad` to read these headers and display messages in a user-friendly way.

-   **Alpine.js for UI State Management:**
    -   For simple UI state like modal visibility, Hyperscript (`_`) is used directly within HTMX attributes (`_ = "on click add .is-active to #modal"`). This provides concise client-side control without writing verbose JavaScript.
    -   While not explicitly demonstrated for complex form logic here (as the ASP.NET side had minimal client-side interaction beyond confirmations), Alpine.js would be leveraged for scenarios requiring more intricate front-end reactivity (e.g., dynamic field visibility, calculated inputs) within the form partials themselves.

-   **DataTables for List Views:**
    -   The `_tdscode_table.html` partial includes a JavaScript snippet that initializes DataTables on the `<table>` element with `id="tdscodeTable"`.
    -   Configuration includes `pageLength`, `lengthMenu`, `responsive: true`, and `searching: true`, providing immediate client-side search, sorting, and pagination capabilities, mimicking and enhancing the original `GridView` functionality.

-   **DRY Template Inheritance:** All module-specific templates (`list.html`, `_tdscode_table.html`, `_tdscode_form.html`, `_tdscode_confirm_delete.html`) extend `core/base.html`. This centralizes common elements like HTML structure, CDN links for HTMX, Alpine.js, jQuery, DataTables, and Tailwind CSS, avoiding repetition.

-   **No Additional JavaScript:** The design strictly adheres to using HTMX and Alpine.js (with Hyperscript) for all dynamic interactions, eliminating the need for custom, module-specific JavaScript files like the original `PopUpMsg.js`.

## Final Notes

-   **Automation Potential:** This structured breakdown is designed for automated conversion. An AI-powered tool could analyze ASP.NET markup for `GridView` definitions, `TemplateField` structures, and C# event handlers to generate the corresponding Django model, form, views, URLs, and HTMX-enabled templates.
-   **Database Integration:** Ensure your Django project's `settings.py` is configured to connect to your existing `tblAcc_TDSCode_Master` table using the appropriate database backend (e.g., `django-pyodbc-azure` for SQL Server, `psycopg2` for PostgreSQL, `mysqlclient` for MySQL). Remember `managed = False` is crucial.
-   **Tailwind CSS:** All generated HTML includes Tailwind CSS classes for modern, utility-first styling. Ensure Tailwind CSS is properly set up in your Django project.
-   **Message Handling:** To enable `X-Messages` headers for HTMX, you need to add `django_htmx.middleware.HtmxMessageMiddleware` to your `MIDDLEWARE` list in `settings.py`.
-   **Testing:** The provided test suite aims for high coverage, verifying models, forms, and view logic, including specific tests for HTMX interactions, which is critical for a robust application.