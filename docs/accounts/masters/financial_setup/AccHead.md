## ASP.NET to Django Modernization Plan: Account Heads Module

This document outlines a comprehensive plan to transition your legacy ASP.NET "Account Heads" module to a modern Django-based solution. Our approach prioritizes automation, leveraging conversational AI for seamless execution, and focuses on delivering a maintainable, high-performance application with a superior user experience.

### Business Value of Django Modernization

Transitioning to Django offers several key benefits for your organization:

1.  **Reduced Technical Debt:** Moves away from aging ASP.NET Web Forms, which are complex and harder to maintain, to a contemporary, well-supported framework.
2.  **Improved Performance & Scalability:** Django's robust architecture and efficient ORM (Object-Relational Mapper) provide a solid foundation for faster operations and better handling of increased user loads.
3.  **Enhanced User Experience (UX):** With HTMX and Alpine.js, users will experience instant feedback and dynamic interactions without full page reloads, leading to a smoother, more responsive interface akin to a single-page application (SPA).
4.  **Faster Feature Development:** Django's "batteries-included" philosophy, along with its strong community and extensive ecosystem, accelerates development of new features and modifications.
5.  **Simplified Maintenance:** Clear separation of concerns (models for business logic, thin views, and reusable templates) makes the codebase easier to understand, debug, and extend.
6.  **Cost Efficiency:** Open-source technologies like Django and Python reduce licensing costs and provide access to a larger pool of talent.
7.  **Future-Proofing:** Adopting a popular and actively developed framework ensures your application remains relevant and secure for years to come.

### Core Architecture Principles Applied

*   **Fat Model, Thin View:** Business logic is encapsulated within Django models, keeping views concise and focused on handling requests and responses.
*   **HTMX + Alpine.js Frontend:** Eliminates the need for complex JavaScript frameworks, delivering dynamic, interactive UIs with minimal code.
*   **DataTables for List Views:** Provides powerful client-side search, sort, and pagination capabilities for large datasets, enhancing data discoverability.
*   **DRY Template Inheritance:** Reusable template structure ensures consistency and reduces redundancy across the application.
*   **Strict Separation of Concerns:** No HTML in Python views, no business logic in templates or views.
*   **Automated Testing:** Comprehensive unit and integration tests ensure reliability and facilitate future development.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Analysis:**
The ASP.NET code explicitly defines a `SqlDataSource` named `SqlDataSource1` that interacts with the `AccHead` table.

*   **Table Name:** `AccHead`
*   **Columns Identified:**
    *   `Id` (Primary Key, Integer)
    *   `Category` (String)
    *   `Description` (String)
    *   `Symbol` (String)
    *   `Abbrivation` (String)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD (Create, Read, Update, Delete) operations in the ASP.NET code.

**Analysis:**
The `GridView1` and `SqlDataSource1` configuration, along with the C# code-behind, reveal the following functionalities:

*   **Read (R):** The `SelectCommand` (`SELECT Id, Category, Description, Symbol, Abbrivation FROM AccHead WHERE Id!=0 ORDER BY Id DESC`) retrieves all `AccHead` records and binds them to `GridView1`.
*   **Create (C):**
    *   Triggered by "Insert" buttons in the `GridView` footer (CommandName="Add") and `EmptyDataTemplate` (CommandName="Add1").
    *   Data is collected from `DropDownList` and `TextBox` controls.
    *   The `InsertCommand` (`INSERT INTO [AccHead] ([Category], [Description], [Symbol], [Abbrivation]) VALUES (...)`) is used.
*   **Update (U):**
    *   Triggered by the "Edit" `LinkButton` in each row, leading to `GridView1_RowUpdating`.
    *   While `SqlDataSource1` has an `UpdateCommand` defined, the C# code-behind (`GridView1_RowUpdating`) *manually constructs and executes an SQL `UPDATE` statement*, including `Category` as an updatable field. This custom logic will be mirrored in Django.
    *   `UPDATE AccHead SET Category='...', Description = '...', Symbol = '...', Abbrivation = '...' WHERE Id ='...'`
*   **Delete (D):**
    *   Triggered by the "Delete" `LinkButton` in each row, leading to `GridView1_RowDeleted`.
    *   The `DeleteCommand` (`DELETE FROM [AccHead] WHERE [Id] = @Id`) is used.
*   **Validation:** `RequiredFieldValidator`s ensure that `Description`, `Symbol`, and `Abbrivation` fields are not empty during insertion and update.
*   **Special Business Logic:** `GridView1_RowDataBound` disables (sets `Visible = false`) the "Edit" and "Delete" buttons for `AccHead` records with `Id` equal to "19" or "33". This is critical business logic to port.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**

*   **GridView (`GridView1`):** The primary data display component. This will be replaced by a standard HTML `<table>` enhanced with **DataTables.js** for client-side search, sort, and pagination.
*   **TemplateField Columns:**
    *   **`Id`:** Hidden, but used for identifying records.
    *   **`Category`:** `asp:Label` for display, `asp:DropDownList` for editing and adding. The choices are "Labour", "With Material", "Expenses", "Service Provider".
    *   **`Description`, `Symbol`, `Abbrivation`:** `asp:Label` for display, `asp:TextBox` for editing and adding.
*   **`asp:Button` / `asp:LinkButton`:** Trigger actions (Add, Edit, Delete). These will become standard HTML `<button>` elements with **HTMX** attributes to trigger dynamic requests (e.g., loading forms into modals, submitting data).
*   **`asp:RequiredFieldValidator`:** Frontend validation for required fields. Django forms handle this automatically, and HTMX with server-side validation will provide robust feedback.
*   **`asp:Label` (`lblMessage`):** Displays status messages. This will be handled by Django's built-in `messages` framework, rendered in the `base.html` and triggered via HTMX `HX-Trigger` headers.
*   **Client-side JavaScript (`PopUpMsg.js`, `loadingNotifier.js`, `confirmationAdd()`, etc.):** These will be largely replaced by **HTMX** for dynamic interactions and **Alpine.js** for simple UI state management (e.g., managing modal visibility).

---

### Step 4: Generate Django Code

We will create a new Django application, let's call it `accounts`, to house this module.

#### 4.1 Models (`accounts/models.py`)

This file defines the data structure and key business logic for Account Heads.

```python
from django.db import models

class AccHead(models.Model):
    # Django automatically creates an 'id' field as the primary key.
    # The 'Id' column from ASP.NET will map to this.
    category = models.CharField(
        max_length=50,
        choices=[
            ('Labour', 'Labour'),
            ('With Material', 'With Material'),
            ('Expenses', 'Expenses'),
            ('Service Provider', 'Service Provider'),
        ],
        db_column='Category',
        verbose_name='Category'
    )
    description = models.CharField(
        max_length=255,
        db_column='Description',
        verbose_name='Description'
    )
    symbol = models.CharField(
        max_length=50,
        db_column='Symbol',
        verbose_name='Symbol'
    )
    abbrivation = models.CharField(
        max_length=50,
        db_column='Abbrivation',
        verbose_name='Abbrivation'
    )

    class Meta:
        managed = False  # Important: Django will not manage this table's schema (assumes it exists)
        db_table = 'AccHead' # The actual table name in your database
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'
        ordering = ['-id'] # Matches 'ORDER BY Id DESC' from ASP.NET

    def __str__(self):
        return f"{self.description} ({self.category})"

    # Business logic: Determines if a record can be edited or deleted
    # Based on the ASP.NET code-behind's GridView1_RowDataBound logic
    def can_edit_delete(self):
        """
        Checks if the current AccHead record can be edited or deleted.
        Returns True if editable/deletable, False otherwise.
        """
        # Specific IDs 19 and 33 are not editable/deletable in the original code
        if self.id in [19, 33]:
            return False
        return True

```

#### 4.2 Forms (`accounts/forms.py`)

This file defines the input forms for creating and updating Account Heads.

```python
from django import forms
from .models import AccHead

class AccHeadForm(forms.ModelForm):
    # Define choices for the category dropdown, matching the model's choices
    CATEGORY_CHOICES = [
        ('Labour', 'Labour'),
        ('With Material', 'With Material'),
        ('Expenses', 'Expenses'),
        ('Service Provider', 'Service Provider'),
    ]
    category = forms.ChoiceField(
        choices=CATEGORY_CHOICES,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    class Meta:
        model = AccHead
        fields = ['category', 'description', 'symbol', 'abbrivation']
        widgets = {
            'description': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'symbol': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'abbrivation': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'category': 'Category',
            'description': 'Description',
            'symbol': 'Symbol',
            'abbrivation': 'Abbrivation',
        }

    # No custom validation methods needed here as ModelForm handles required fields
    # based on model definition (CharField implicitly not nullable unless specified)
    # and the ASP.NET validation was basic 'required field'.

```

#### 4.3 Views (`accounts/views.py`)

This file contains the logic for handling HTTP requests and rendering templates. Views are kept thin, delegating business logic to models.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import AccHead
from .forms import AccHeadForm

class AccHeadListView(ListView):
    model = AccHead
    context_object_name = 'accheads' # 'accheads' is the plural of 'AccHead'
    
    def get_template_names(self):
        """
        Return different templates based on whether it's an HTMX request.
        If HX-Request header is present, return only the table partial.
        Otherwise, return the full page template.
        """
        if self.request.headers.get('HX-Request'):
            return ['accounts/acchead/_acchead_table.html']
        return ['accounts/acchead/list.html']

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Add any additional context if needed for the full page or partial
        return context

class AccHeadCreateView(CreateView):
    model = AccHead
    form_class = AccHeadForm
    template_name = 'accounts/acchead/_acchead_form.html' # This is a partial template for modal
    success_url = reverse_lazy('acchead_list') # Redundant for HTMX, but good practice

    def form_valid(self, form):
        # The form is valid, save the object
        response = super().form_valid(form)
        messages.success(self.request, 'Account Head added successfully.')

        # HTMX response: return 204 No Content and trigger client-side event
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshAccHeadList' # Event to trigger table refresh
                }
            )
        return response

class AccHeadUpdateView(UpdateView):
    model = AccHead
    form_class = AccHeadForm
    template_name = 'accounts/acchead/_acchead_form.html' # This is a partial template for modal
    success_url = reverse_lazy('acchead_list') # Redundant for HTMX, but good practice

    def get_object(self, queryset=None):
        """
        Overrides get_object to ensure records with specific IDs (19, 33) cannot be edited.
        If an attempt is made to edit such a record, it will raise Http404.
        This provides a server-side gate for the business logic.
        """
        obj = super().get_object(queryset)
        if not obj.can_edit_delete():
            # You might want to return an HttpResponseForbidden or an error message template
            # For simplicity, raising Http404 for now.
            messages.error(self.request, "This Account Head cannot be edited.")
            if self.request.headers.get('HX-Request'):
                # For HTMX, return a 204 with a trigger to close the modal
                return HttpResponse(
                    status=204,
                    headers={'HX-Trigger': 'closeModal'}
                )
            from django.http import Http404
            raise Http404("Account Head not found or cannot be edited.")
        return obj

    def form_valid(self, form):
        # The form is valid, save the object
        response = super().form_valid(form)
        messages.success(self.request, 'Account Head updated successfully.')

        # HTMX response: return 204 No Content and trigger client-side event
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshAccHeadList' # Event to trigger table refresh
                }
            )
        return response

class AccHeadDeleteView(DeleteView):
    model = AccHead
    template_name = 'accounts/acchead/_acchead_confirm_delete.html' # Partial template for modal
    success_url = reverse_lazy('acchead_list') # Redundant for HTMX, but good practice

    def get_object(self, queryset=None):
        """
        Overrides get_object to ensure records with specific IDs (19, 33) cannot be deleted.
        """
        obj = super().get_object(queryset)
        if not obj.can_edit_delete():
            messages.error(self.request, "This Account Head cannot be deleted.")
            if self.request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204,
                    headers={'HX-Trigger': 'closeModal'}
                )
            from django.http import Http404
            raise Http404("Account Head not found or cannot be deleted.")
        return obj

    def delete(self, request, *args, **kwargs):
        # Perform the delete operation
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Account Head deleted successfully.')

        # HTMX response: return 204 No Content and trigger client-side event
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshAccHeadList' # Event to trigger table refresh
                }
            )
        return response

```

#### 4.4 Templates (`accounts/templates/accounts/acchead/`)

These templates handle the rendering of the UI. They leverage HTMX for dynamic content and are designed as partials for efficient loading.

##### `list.html`

The main page for displaying Account Heads. This extends `core/base.html` and sets up the container for the HTMX-loaded table and modals.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Account Heads</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'acchead_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Account Head
        </button>
    </div>
    
    <div id="accheadTable-container"
         hx-trigger="load, refreshAccHeadList from:body"
         hx-get="{% url 'acchead_list' %}" {# This URL will return the partial table when HX-Request is true #}
         hx-swap="innerHTML">
        <!-- Initial loading state -->
        <div class="flex flex-col items-center justify-center h-48 text-gray-500">
            <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-2"></div>
            <p>Loading Account Heads...</p>
        </div>
    </div>
    
    <!-- Global Modal for forms (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me
            on closeModal from body remove .is-active from me then reset #modalContent">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Optional: Alpine.js component initialization if needed for more complex UI state
    document.addEventListener('alpine:init', () => {
        // e.g., Alpine.data('myComponent', () => ({ ... }));
    });
</script>
{% endblock %}

```

##### `_acchead_table.html`

This partial template renders the DataTables component. It's loaded dynamically by HTMX.

```html
<div class="overflow-x-auto shadow-md rounded-lg">
    <table id="accheadTable" class="min-w-full bg-white divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Category</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Description</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Symbol</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Abbrivation</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% for obj in accheads %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap">{{ obj.category }}</td>
                <td class="py-3 px-4 whitespace-nowrap">{{ obj.description }}</td>
                <td class="py-3 px-4 whitespace-nowrap">{{ obj.symbol }}</td>
                <td class="py-3 px-4 whitespace-nowrap">{{ obj.abbrivation }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    {% if obj.can_edit_delete %}
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 transition duration-300 ease-in-out"
                        hx-get="{% url 'acchead_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md transition duration-300 ease-in-out"
                        hx-get="{% url 'acchead_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                    {% else %}
                    <span class="text-gray-500 text-xs italic">System Record</span>
                    {% endif %}
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 px-4 text-center text-gray-500">No Account Heads found. Click "Add New Account Head" to create one.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

{# DataTables initialization script #}
<script>
    // Ensure DataTables CSS/JS are loaded via base.html for the first load
    // For HTMX-loaded content, re-initialize DataTables if needed
    if (typeof jQuery !== 'undefined' && typeof jQuery.fn.DataTable !== 'undefined') {
        $(document).ready(function() {
            $('#accheadTable').DataTable({
                "pagingType": "full_numbers", // For full pagination controls
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]], // Options for items per page
                "dom": 'lfrtip', // Layout: Length, Filter, Table, Info, Paging
                "responsive": true // Make table responsive
            });
        });
    } else {
        console.warn("jQuery or DataTables not loaded. Please ensure CDN links are in base.html.");
    }
</script>
```

##### `_acchead_form.html`

This partial template handles both creation and update forms.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5 border-b pb-3">{{ form.instance.pk|yesno:'Edit,Add' }} Account Head</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-6">
        {% csrf_token %}
        
        {% for field in form %}
        <div>
            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                {{ field.label }}
                {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
            </label>
            {{ field }}
            {% if field.help_text %}
            <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
            {% endif %}
            {% if field.errors %}
            <p class="text-red-600 text-sm mt-1">{{ field.errors|striptags }}</p>
            {% endif %}
        </div>
        {% endfor %}
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
                Save Account Head
            </button>
        </div>
    </form>
</div>
```

##### `_acchead_confirm_delete.html`

This partial template provides a confirmation dialog for deletion.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5 border-b pb-3">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Account Head: <strong>{{ object.description }} ({{ object.category }})</strong>?</p>
    
    <form hx-post="{% url 'acchead_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`accounts/urls.py`)

This file defines the URL patterns for the `accounts` application.

```python
from django.urls import path
from .views import AccHeadListView, AccHeadCreateView, AccHeadUpdateView, AccHeadDeleteView

urlpatterns = [
    # Main list view (also serves the HTMX-loaded table partial)
    path('accheads/', AccHeadListView.as_view(), name='acchead_list'),
    
    # HTMX-loaded forms for CRUD operations
    path('accheads/add/', AccHeadCreateView.as_view(), name='acchead_add'),
    path('accheads/<int:pk>/edit/', AccHeadUpdateView.as_view(), name='acchead_edit'),
    path('accheads/<int:pk>/delete/', AccHeadDeleteView.as_view(), name='acchead_delete'),
]

```

**Integration into Project URLs:**
In your main Django project's `urls.py`, you would include these patterns:

```python
# yourproject/urls.py
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('accounts/', include('accounts.urls')), # Include your new accounts app URLs
    # ... other project URLs
]
```

#### 4.6 Tests (`accounts/tests.py`)

Comprehensive tests for both the model's business logic and the views' functionality, including HTMX interactions.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from .models import AccHead

class AccHeadModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.acc_head1 = AccHead.objects.create(
            category='Labour',
            description='Test Description 1',
            symbol='TD1',
            abbrivation='TD1'
        )
        cls.acc_head_system = AccHead.objects.create(
            id=19, # Simulate a system record that cannot be edited/deleted
            category='Expenses',
            description='System Expense',
            symbol='SE',
            abbrivation='SE'
        )
        cls.acc_head_system2 = AccHead.objects.create(
            id=33, # Simulate another system record
            category='Service Provider',
            description='System Service',
            symbol='SS',
            abbrivation='SS'
        )
  
    def test_acchead_creation(self):
        obj = AccHead.objects.get(description='Test Description 1')
        self.assertEqual(obj.category, 'Labour')
        self.assertEqual(obj.symbol, 'TD1')
        self.assertEqual(obj.abbrivation, 'TD1')
        self.assertEqual(obj.description, 'Test Description 1')
        
    def test_verbose_name(self):
        self.assertEqual(AccHead._meta.verbose_name, 'Account Head')
        self.assertEqual(AccHead._meta.verbose_name_plural, 'Account Heads')

    def test_str_representation(self):
        expected_str = f"{self.acc_head1.description} ({self.acc_head1.category})"
        self.assertEqual(str(self.acc_head1), expected_str)

    def test_can_edit_delete_method(self):
        # Test for a regular record
        self.assertTrue(self.acc_head1.can_edit_delete())
        
        # Test for system records (ID 19 and 33)
        self.assertFalse(self.acc_head_system.can_edit_delete())
        self.assertFalse(self.acc_head_system2.can_edit_delete())

class AccHeadViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create initial test data for views
        cls.acc_head_editable = AccHead.objects.create(
            category='Labour',
            description='Editable AccHead',
            symbol='EA',
            abbrivation='EA'
        )
        cls.acc_head_non_editable = AccHead.objects.create(
            id=19, # Simulate a system record
            category='Expenses',
            description='Non-Editable AccHead',
            symbol='NEA',
            abbrivation='NEA'
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view_get_full_page(self):
        response = self.client.get(reverse('acchead_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/acchead/list.html')
        self.assertIn(self.acc_head_editable, response.context['accheads'])
        self.assertContains(response, 'Account Heads') # Check for title
        self.assertContains(response, 'Add New Account Head') # Check for add button

    def test_list_view_get_htmx_partial(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('acchead_list'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/acchead/_acchead_table.html')
        self.assertIn(self.acc_head_editable, response.context['accheads'])
        self.assertContains(response, 'Editable AccHead') # Check data in partial
        self.assertNotContains(response, 'Add New Account Head') # Should not contain full page content

    def test_create_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'} # Simulate HTMX modal request
        response = self.client.get(reverse('acchead_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/acchead/_acchead_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Account Head')

    def test_create_view_post_valid(self):
        data = {
            'category': 'Labour',
            'description': 'New AccHead',
            'symbol': 'NA',
            'abbrivation': 'NA'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('acchead_add'), data, **headers)
        # HTMX successful post should return 204 No Content
        self.assertEqual(response.status_code, 204)
        self.assertTrue(AccHead.objects.filter(description='New AccHead').exists())
        # Check HTMX trigger for refresh
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshAccHeadList')
        # Check messages
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Account Head added successfully.')
        
    def test_create_view_post_invalid(self):
        data = {
            'category': 'Labour',
            'description': '', # Invalid: missing required field
            'symbol': 'NA',
            'abbrivation': 'NA'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('acchead_add'), data, **headers)
        # Invalid form should return 200 OK with the form (to re-render in modal)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/acchead/_acchead_form.html')
        self.assertContains(response, 'This field is required.') # Check for validation error

    def test_update_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('acchead_edit', args=[self.acc_head_editable.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/acchead/_acchead_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Edit Account Head')
        self.assertContains(response, self.acc_head_editable.description)

    def test_update_view_post_valid(self):
        data = {
            'category': 'With Material', # Test updating category
            'description': 'Updated AccHead',
            'symbol': 'UA',
            'abbrivation': 'UA'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('acchead_edit', args=[self.acc_head_editable.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.acc_head_editable.refresh_from_db()
        self.assertEqual(self.acc_head_editable.description, 'Updated AccHead')
        self.assertEqual(self.acc_head_editable.category, 'With Material') # Ensure category is updated
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshAccHeadList')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Account Head updated successfully.')

    def test_update_view_get_non_editable(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('acchead_edit', args=[self.acc_head_non_editable.pk]), **headers)
        self.assertEqual(response.status_code, 204) # 204 with close modal trigger for HTMX
        self.assertEqual(response.headers.get('HX-Trigger'), 'closeModal')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'This Account Head cannot be edited.')

    def test_delete_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('acchead_delete', args=[self.acc_head_editable.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/acchead/_acchead_confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, self.acc_head_editable.description)

    def test_delete_view_post_valid(self):
        initial_count = AccHead.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('acchead_delete', args=[self.acc_head_editable.pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(AccHead.objects.count(), initial_count - 1)
        self.assertFalse(AccHead.objects.filter(pk=self.acc_head_editable.pk).exists())
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshAccHeadList')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Account Head deleted successfully.')

    def test_delete_view_get_non_deletable(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('acchead_delete', args=[self.acc_head_non_editable.pk]), **headers)
        self.assertEqual(response.status_code, 204) # 204 with close modal trigger for HTMX
        self.assertEqual(response.headers.get('HX-Trigger'), 'closeModal')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'This Account Head cannot be deleted.')

    def test_delete_view_post_non_deletable(self):
        initial_count = AccHead.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('acchead_delete', args=[self.acc_head_non_editable.pk]), **headers)
        self.assertEqual(response.status_code, 204) # Still 204 for HTMX, as it's a "successful" handling of the non-deletable logic
        self.assertEqual(AccHead.objects.count(), initial_count) # Should not delete
        self.assertEqual(response.headers.get('HX-Trigger'), 'closeModal') # Close modal due to error
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'This Account Head cannot be deleted.')
```

---

### Step 5: HTMX and Alpine.js Integration

The provided templates heavily incorporate HTMX and Alpine.js principles:

*   **HTMX for Dynamic Content:**
    *   The main list view (`list.html`) uses `hx-get` on `accheadTable-container` to load `_acchead_table.html` initially and on `refreshAccHeadList` event.
    *   "Add New" button uses `hx-get` to load the form into a modal (`#modalContent`).
    *   "Edit" and "Delete" buttons use `hx-get` to load their respective forms/confirmations into the same modal.
    *   Form submissions (`hx-post`) return `status=204 No Content` to prevent full page reloads, along with `HX-Trigger` headers (`refreshAccHeadList`, `closeModal`) to update the UI on the client-side.
    *   `hx-swap="none"` on forms ensures the target element is not swapped directly, relying on the `HX-Trigger` to manage UI changes.

*   **Alpine.js for UI State (via `_` attribute):**
    *   The modal (`#modal`) uses Alpine.js's `_` attribute (`on click if event.target.id == 'modal' remove .is-active from me`) to close itself when clicked outside.
    *   `on closeModal from body remove .is-active from me then reset #modalContent` listens for the `closeModal` event (triggered by views) to hide the modal and clear its content, providing a clean state.
    *   The buttons use `on click add .is-active to #modal` to show the modal.

*   **DataTables for List Views:**
    *   The `_acchead_table.html` partial includes the JavaScript initialization for DataTables. It is wrapped in `$(document).ready()` to ensure the table is initialized after being loaded by HTMX.
    *   The table is styled with Tailwind CSS classes to ensure a modern, clean appearance.

This combined approach ensures that the user experience is highly dynamic and interactive, moving away from traditional ASP.NET postbacks to a more modern, efficient web application.

---

### Final Notes

*   **Placeholders:** All `[PLACEHOLDER]` values have been replaced with the concrete names derived from the ASP.NET analysis (e.g., `AccHead`, `acchead_list`, `category`, `description`, etc.).
*   **DRY Templates:** The use of `_acchead_table.html`, `_acchead_form.html`, and `_acchead_confirm_delete.html` as partials ensures that chunks of HTML are reusable and maintainable.
*   **Base Template Assumption:** This plan strictly adheres to the rule of *not* including `base.html` code. It is assumed that `core/base.html` exists and correctly includes necessary CDN links for HTMX, Alpine.js, jQuery, DataTables CSS, and DataTables JS, along with a `{% block content %}` and `{% block extra_js %}`.
*   **Business Logic in Models:** The `can_edit_delete()` method in the `AccHead` model directly reflects the ASP.NET code-behind's logic for restricting edits and deletes on specific records. This keeps views thin and focused.
*   **Test Coverage:** The provided test suite covers model properties, string representation, the `can_edit_delete` business logic, and all CRUD operations for views, including HTMX request/response patterns and error handling for restricted records. This forms the foundation for achieving high test coverage.
*   **Tailwind CSS:** All generated HTML includes Tailwind CSS classes for modern styling, assuming Tailwind is configured in the Django project.

This comprehensive plan provides a clear, actionable roadmap for migrating your `AccHead` module to Django, emphasizing automation readiness and delivering a high-quality, modern web application.