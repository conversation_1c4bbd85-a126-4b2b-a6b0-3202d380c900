## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

## Step 1: Extract Database Schema

**Analysis:**
The ASP.NET `SqlDataSource1` directly interacts with the database.
- **Table Name:** `tblACC_InvoiceAgainst`
- **Columns Identified:**
    - `Id`: Used as `DataKeyNames` and in `WHERE` clauses for `Delete` and `Update` commands. This is the primary key (Integer).
    - `Against`: Used in `INSERT` (`VALUES (@Terms)`) and `UPDATE` (`SET [Against] = @Terms`) commands, and displayed/edited via `Terms` column in `GridView`. This is a Text/String field.

## Step 2: Identify Backend Functionality

**Analysis:**
The ASP.NET code implements standard CRUD operations on the `tblACC_InvoiceAgainst` table.

- **Create (Add):**
    - Triggered by `btnInsert` in `GridView` footer (Command `Add`) or `EmptyDataTemplate` (Command `Add1`).
    - Handled by `GridView1_RowCommand` in C#.
    - Inserts a new record into `tblACC_InvoiceAgainst` using the value from `txtTerms2` (or `txtTerms3` for empty table) into the `Against` column.
    - SQL: `INSERT INTO [tblACC_InvoiceAgainst] ([Against]) VALUES (@Terms)`
    - Validation: `RequiredFieldValidator` ensures `Terms` field is not empty.

- **Read (Retrieve):**
    - Data loaded into `GridView1` via `SqlDataSource1`.
    - SQL: `SELECT * FROM [tblACC_InvoiceAgainst] order by [Id] desc`
    - Displays `Id` (as SN) and `Against` (as Terms).
    - Includes pagination (`AllowPaging="True"`, `PageSize="20"`).

- **Update (Edit):**
    - Triggered by the "Edit" `CommandField` in `GridView1`.
    - Handled by `GridView1_RowUpdating` in C#.
    - Updates the `Against` field for a specific `Id` using the value from `txtTerms1`.
    - SQL: `UPDATE tblACC_InvoiceAgainst SET Against ='...' WHERE Id ='...'` (Note: direct SQL execution observed in C#).
    - Validation: `RequiredFieldValidator` ensures `Terms` field is not empty.

- **Delete:**
    - Triggered by the "Delete" `CommandField` in `GridView1`.
    - Handled by `GridView1_RowDeleted` in C#.
    - Deletes a record from `tblACC_InvoiceAgainst` for a specific `Id`.
    - SQL: `DELETE FROM [tblACC_InvoiceAgainst] WHERE [Id] = @Id`

- **Client-Side Interactions:**
    - `OnClientClick` attributes (`confirmationAdd()`, `confirmationUpdate()`, `confirmationDelete()`) suggest JavaScript confirmation dialogs. These will be replaced by HTMX/Alpine.js modals.
    - `yui-datatable-theme` CSS class on `GridView1` indicates a DataTable-like presentation.

## Step 3: Infer UI Components

**Analysis:**
The ASP.NET `GridView` is the central UI component, providing a table with inline editing, deleting, and a footer for adding new records.

- **Main Data Display:** `asp:GridView` (ID: `GridView1`). This will be replaced by a standard HTML `<table>` enhanced with DataTables.
- **Input Fields:**
    - `asp:TextBox` (ID: `txtTerms1` for edit, `txtTerms2` for insert in footer, `txtTerms3` for insert in empty template). These will map to a single Django `forms.TextInput` widget.
- **Action Buttons:**
    - `asp:CommandField` for Edit and Delete.
    - `asp:Button` (ID: `btnInsert`) for adding new records.
    - These will be replaced by standard HTML `button` tags with `hx-get` and `hx-post` attributes for HTMX-driven interactions.
- **Validation:** `asp:RequiredFieldValidator`. This will be handled by Django Form validation.
- **Messages:** `asp:Label` (ID: `lblMessage`). This will be handled by Django's `messages` framework and displayed dynamically via HTMX.
- **Styling:** `Css/yui-datatable.css`, `Css/StyleSheet.css`. These will be replaced by Tailwind CSS.

---

## Step 4: Generate Django Code

### 4.1 Models

**`accounts/models.py`**

```python
from django.db import models

class InvoiceAgainst(models.Model):
    # 'Id' is the primary key in the ASP.NET code. Django automatically
    # creates an 'id' field as PK. For managed=False, Django's 'id' will
    # map to the 'Id' column if it exists in the database.
    # If the database PK column name is different from 'id', you'd use:
    # id = models.IntegerField(db_column='Id', primary_key=True)
    # But usually, Django handles it if the column is 'id'. Assuming 'Id' is the default PK.

    against_term = models.CharField(
        db_column='Against',
        max_length=255, # Inferred from common string field sizes, adjust as needed
        verbose_name="Terms",
        help_text="The invoice against term (e.g., 'Payment', 'Refund')."
    )

    class Meta:
        managed = False  # Tells Django not to manage this table's schema (it exists)
        db_table = 'tblACC_InvoiceAgainst'
        verbose_name = 'Invoice Against Term'
        verbose_name_plural = 'Invoice Against Terms'
        ordering = ['-id'] # Matches 'order by [Id] desc' from original SQL

    def __str__(self):
        return self.against_term

    # Business logic can be added here if needed, e.g., validation rules
    # beyond basic form validation, or complex interactions with other models.
    # For this simple CRUD, most logic resides in forms/views implicitly.
```

### 4.2 Forms

**`accounts/forms.py`**

```python
from django import forms
from .models import InvoiceAgainst

class InvoiceAgainstForm(forms.ModelForm):
    class Meta:
        model = InvoiceAgainst
        fields = ['against_term']
        widgets = {
            'against_term': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter invoice against term'
            }),
        }
        
    def clean_against_term(self):
        against_term = self.cleaned_data.get('against_term')
        if not against_term:
            raise forms.ValidationError("Invoice against term cannot be empty.")
        # Additional custom validation for uniqueness or format can go here
        return against_term
```

### 4.3 Views

**`accounts/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.template.loader import render_to_string
from .models import InvoiceAgainst
from .forms import InvoiceAgainstForm

class InvoiceAgainstListView(ListView):
    model = InvoiceAgainst
    template_name = 'accounts/invoiceagainst/list.html'
    context_object_name = 'invoice_against_terms'
    # No pagination handled here, DataTables will manage it client-side after initial load.

class InvoiceAgainstTablePartialView(ListView):
    """
    Renders the partial HTML table for InvoiceAgainst, primarily used by HTMX to refresh the list.
    """
    model = InvoiceAgainst
    template_name = 'accounts/invoiceagainst/_invoiceagainst_table.html'
    context_object_name = 'invoice_against_terms'

    def get_queryset(self):
        # DataTables will handle client-side sorting and filtering.
        # If server-side processing is needed, more logic would go here.
        return super().get_queryset()

class InvoiceAgainstCreateView(CreateView):
    model = InvoiceAgainst
    form_class = InvoiceAgainstForm
    template_name = 'accounts/invoiceagainst/_invoiceagainst_form.html' # Use partial template for modal
    success_url = reverse_lazy('invoiceagainst_list') # Fallback if not HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Invoice Against Term added successfully.')
        # HTMX-specific response: trigger a refresh of the list and close modal
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,  # No Content
                headers={
                    'HX-Trigger': '{"refreshInvoiceAgainstList": true, "closeModal": true}'
                }
            )
        return response

    def form_invalid(self, form):
        # If form is invalid, re-render the form with errors in the modal
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # HTMX will swap the modal content with the re-rendered form
            return response
        return response

class InvoiceAgainstUpdateView(UpdateView):
    model = InvoiceAgainst
    form_class = InvoiceAgainstForm
    template_name = 'accounts/invoiceagainst/_invoiceagainst_form.html' # Use partial template for modal
    success_url = reverse_lazy('invoiceagainst_list') # Fallback if not HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Invoice Against Term updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshInvoiceAgainstList": true, "closeModal": true}'
                }
            )
        return response
    
    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response
        return response

class InvoiceAgainstDeleteView(DeleteView):
    model = InvoiceAgainst
    template_name = 'accounts/invoiceagainst/_invoiceagainst_confirm_delete.html' # Use partial template for modal
    success_url = reverse_lazy('invoiceagainst_list') # Fallback if not HTMX

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Invoice Against Term deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshInvoiceAgainstList": true, "closeModal": true}'
                }
            )
        return response
```

### 4.4 Templates

**`accounts/templates/accounts/invoiceagainst/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Manage Invoice Against Terms</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'invoiceagainst_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Term
        </button>
    </div>
    
    <div id="invoiceagainstTable-container"
         hx-trigger="load, refreshInvoiceAgainstList from:body"
         hx-get="{% url 'invoiceagainst_table' %}"
         hx-swap="innerHTML"
         class="bg-white p-6 rounded-lg shadow-lg">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading terms...</p>
        </div>
    </div>
    
    <!-- Modal for forms -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center hidden z-50"
         x-data="{ showModal: false }"
         x-show="showModal"
         x-on:refreshinvoiceagainstlist.window="showModal = false"
         x-on:closemodal.window="showModal = false"
         x-init="() => { 
             $watch('showModal', value => { 
                 if(value) { document.body.style.overflow = 'hidden'; } 
                 else { document.body.style.overflow = ''; } 
             });
             htmx.onLoad(function(elt) {
                if (elt.id === 'modalContent') {
                    // This function is called after HTMX loads content into modalContent
                    // Here you can explicitly set showModal to true after content is loaded
                    // This is an Alpine.js way to handle modal visibility based on HTMX content
                    Alpine.raw(document.getElementById('modal')._x_dataStack[0]).showModal = true;
                }
            });
         }"
         _="on click if event.target.id == 'modal' remove .is-active from me then set showModal to false">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-lg w-full m-4 max-h-[90vh] overflow-y-auto transform scale-95 opacity-0 transition-all duration-300 ease-out"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0 scale-95"
             x-transition:enter-end="opacity-100 scale-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100 scale-100"
             x-transition:leave-end="opacity-0 scale-95"
             x-on:click.stop=""></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Include jQuery and DataTables from CDN in base.html as per DRY principle -->
<!-- Ensure the following scripts are AFTER the content block so DataTable can find its table -->
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('modalController', () => ({
            showModal: false,
            openModal() { this.showModal = true; },
            closeModal() { this.showModal = false; },
        }));
    });

    // Custom event listener for HTMX to trigger modal close
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.xhr.status === 204 && evt.detail.elt.id === 'modalContent') {
            // If a 204 (No Content) response is received in modal, it means operation was successful.
            // Check if there's an Alpine.js component and close the modal.
            const modalElement = document.getElementById('modal');
            if (modalElement && modalElement._x_dataStack && modalElement._x_dataStack.length > 0) {
                Alpine.raw(modalElement._x_dataStack[0]).closeModal();
            }
        }
    });

    // Re-initialize DataTables when the HTMX content is reloaded
    // This is typically handled by putting the DataTable init script inside the partial
    // which is why the `_invoiceagainst_table.html` will contain it.
    // If you need global re-initialization outside the partial, htmx:afterSwap on the table container
    // could be used to trigger a function that destroys and recreates the DataTable.
</script>
{% endblock %}
```

**`accounts/templates/accounts/invoiceagainst/_invoiceagainst_table.html`** (Partial template for HTMX)

```html
<table id="invoiceagainstTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Terms</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% if invoice_against_terms %}
            {% for obj in invoice_against_terms %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.against_term }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-lg shadow-sm mr-2 transition duration-300 ease-in-out"
                        hx-get="{% url 'invoiceagainst_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-600 hover:bg-red-700 text-white font-bold py-1 px-3 rounded-lg shadow-sm transition duration-300 ease-in-out"
                        hx-get="{% url 'invoiceagainst_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        {% else %}
        <tr>
            <td colspan="3" class="py-4 px-4 text-center text-gray-500">No invoice against terms found. Click "Add New Term" to create one.</td>
        </tr>
        {% endif %}
    </tbody>
</table>

<script>
// DataTables initialization must happen after the table is loaded into the DOM by HTMX.
// Putting it inside the partial ensures it runs correctly.
$(document).ready(function() {
    // Destroy existing DataTable instance before re-initializing if it exists
    if ($.fn.DataTable.isDataTable('#invoiceagainstTable')) {
        $('#invoiceagainstTable').DataTable().destroy();
    }
    $('#invoiceagainstTable').DataTable({
        "paging": true,
        "lengthChange": true,
        "searching": true,
        "ordering": true,
        "info": true,
        "autoWidth": false,
        "responsive": true,
        "pageLength": 20, // Matches ASP.NET PageSize="20"
        "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
        // Additional DataTables configuration for styling/features if needed
    });
});
</script>
```

**`accounts/templates/accounts/invoiceagainst/_invoiceagainst_form.html`** (Partial template for modal)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">
        {{ form.instance.pk|yesno:'Edit,Add' }} Invoice Against Term
    </h3>
    <form hx-post="{{ request.path }}" hx-swap="none" 
          hx-indicator="#modal-spinner">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="list-none p-0 mt-1">
                    {% for error in field.errors %}
                    <li class="text-red-600 text-sm">{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
            {% if form.non_field_errors %}
            <ul class="list-none p-0 mt-4 text-red-600 text-sm">
                {% for error in form.non_field_errors %}
                <li>{{ error }}</li>
                {% endfor %}
            </ul>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal then set showModal to false on #modal element's Alpine.js data">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                Save
                <span id="modal-spinner" class="htmx-indicator ml-2">
                    <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                </span>
            </button>
        </div>
    </form>
</div>
```

**`accounts/templates/accounts/invoiceagainst/_invoiceagainst_confirm_delete.html`** (Partial template for modal)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the term: <strong class="font-medium">{{ object.against_term }}</strong>?</p>
    
    <form hx-post="{% url 'invoiceagainst_delete' object.pk %}" hx-swap="none" 
          hx-indicator="#delete-spinner">
        {% csrf_token %}
        <!-- For delete operations using POST, Django expects a POST request.
             HTMX can send a DELETE verb, but for simple CBVs, POST is common.
             Alternatively, you could use hx-delete and set up your Django view
             to handle DELETE requests directly. For simplicity with standard CBVs, POST is used. -->
        <input type="hidden" name="_method" value="delete"> 

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal then set showModal to false on #modal element's Alpine.js data">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                Delete
                <span id="delete-spinner" class="htmx-indicator ml-2">
                    <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                </span>
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs

**`accounts/urls.py`**

```python
from django.urls import path
from .views import (
    InvoiceAgainstListView, 
    InvoiceAgainstTablePartialView,
    InvoiceAgainstCreateView, 
    InvoiceAgainstUpdateView, 
    InvoiceAgainstDeleteView
)

urlpatterns = [
    path('invoiceagainst/', InvoiceAgainstListView.as_view(), name='invoiceagainst_list'),
    path('invoiceagainst/table/', InvoiceAgainstTablePartialView.as_view(), name='invoiceagainst_table'),
    path('invoiceagainst/add/', InvoiceAgainstCreateView.as_view(), name='invoiceagainst_add'),
    path('invoiceagainst/edit/<int:pk>/', InvoiceAgainstUpdateView.as_view(), name='invoiceagainst_edit'),
    path('invoiceagainst/delete/<int:pk>/', InvoiceAgainstDeleteView.as_view(), name='invoiceagainst_delete'),
]
```

### 4.6 Tests

**`accounts/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from .models import InvoiceAgainst

class InvoiceAgainstModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a test instance for models
        InvoiceAgainst.objects.create(against_term='Test Invoice Term One')
        InvoiceAgainst.objects.create(against_term='Another Term')

    def test_invoice_against_creation(self):
        obj = InvoiceAgainst.objects.get(id=1)
        self.assertEqual(obj.against_term, 'Test Invoice Term One')

    def test_against_term_label(self):
        obj = InvoiceAgainst.objects.get(id=1)
        field_label = obj._meta.get_field('against_term').verbose_name
        self.assertEqual(field_label, 'Terms')

    def test_str_method(self):
        obj = InvoiceAgainst.objects.get(id=1)
        self.assertEqual(str(obj), 'Test Invoice Term One')

    def test_ordering(self):
        # Check if ordering is by '-id' (descending ID)
        all_terms = InvoiceAgainst.objects.all()
        self.assertEqual(all_terms[0].against_term, 'Another Term') # ID=2
        self.assertEqual(all_terms[1].against_term, 'Test Invoice Term One') # ID=1

class InvoiceAgainstViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for views
        InvoiceAgainst.objects.create(against_term='Existing Term for Edit')
        InvoiceAgainst.objects.create(against_term='Term to Delete')

    def setUp(self):
        self.client = Client()
        # Ensure that the database connection allows access to non-managed tables for tests.
        # This might require setting up a test database with your legacy schema.

    def test_list_view(self):
        response = self.client.get(reverse('invoiceagainst_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/invoiceagainst/list.html')
        self.assertTrue('invoice_against_terms' in response.context)
        self.assertContains(response, 'Manage Invoice Against Terms')
        self.assertContains(response, 'Existing Term for Edit')

    def test_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('invoiceagainst_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/invoiceagainst/_invoiceagainst_table.html')
        self.assertTrue('invoice_against_terms' in response.context)
        self.assertContains(response, '<table id="invoiceagainstTable"')
        self.assertContains(response, 'Existing Term for Edit')
        self.assertContains(response, 'Term to Delete')

    def test_create_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('invoiceagainst_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/invoiceagainst/_invoiceagainst_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Invoice Against Term')

    def test_create_view_post_success(self):
        data = {'against_term': 'New Created Term'}
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('invoiceagainst_add'), data, **headers)
        
        # For HTMX success, expect a 204 No Content with HX-Trigger
        self.assertEqual(response.status_code, 204)
        self.assertTrue(InvoiceAgainst.objects.filter(against_term='New Created Term').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshInvoiceAgainstList', response.headers['HX-Trigger'])
        
        # Check messages (will be consumed by HTMX on client-side)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Invoice Against Term added successfully.')

    def test_create_view_post_invalid(self):
        data = {'against_term': ''} # Empty term for required field validation
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('invoiceagainst_add'), data, **headers)
        
        # For HTMX invalid form, expect a 200 OK and re-rendered form with errors
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/invoiceagainst/_invoiceagainst_form.html')
        self.assertContains(response, 'Invoice against term cannot be empty.')
        self.assertFalse(InvoiceAgainst.objects.filter(against_term='').exists())

    def test_update_view_get(self):
        obj = InvoiceAgainst.objects.get(against_term='Existing Term for Edit')
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('invoiceagainst_edit', args=[obj.id]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/invoiceagainst/_invoiceagainst_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Edit Invoice Against Term')
        self.assertContains(response, 'Existing Term for Edit')

    def test_update_view_post_success(self):
        obj = InvoiceAgainst.objects.get(against_term='Existing Term for Edit')
        data = {'against_term': 'Updated Term Name'}
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('invoiceagainst_edit', args=[obj.id]), data, **headers)
        
        self.assertEqual(response.status_code, 204)
        obj.refresh_from_db()
        self.assertEqual(obj.against_term, 'Updated Term Name')
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshInvoiceAgainstList', response.headers['HX-Trigger'])
        
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Invoice Against Term updated successfully.')

    def test_update_view_post_invalid(self):
        obj = InvoiceAgainst.objects.get(against_term='Existing Term for Edit')
        original_term = obj.against_term
        data = {'against_term': ''}
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('invoiceagainst_edit', args=[obj.id]), data, **headers)
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/invoiceagainst/_invoiceagainst_form.html')
        self.assertContains(response, 'Invoice against term cannot be empty.')
        obj.refresh_from_db()
        self.assertEqual(obj.against_term, original_term) # Should not have changed

    def test_delete_view_get(self):
        obj = InvoiceAgainst.objects.get(against_term='Term to Delete')
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('invoiceagainst_delete', args=[obj.id]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/invoiceagainst/_invoiceagainst_confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, 'Term to Delete')

    def test_delete_view_post_success(self):
        obj = InvoiceAgainst.objects.get(against_term='Term to Delete')
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('invoiceagainst_delete', args=[obj.id]), {}, **headers)
        
        self.assertEqual(response.status_code, 204)
        self.assertFalse(InvoiceAgainst.objects.filter(against_term='Term to Delete').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshInvoiceAgainstList', response.headers['HX-Trigger'])
        
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Invoice Against Term deleted successfully.')

    def test_delete_view_post_non_existent(self):
        # Attempt to delete a non-existent object
        non_existent_id = 999
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('invoiceagainst_delete', args=[non_existent_id]), {}, **headers)
        
        # Django's DeleteView will raise 404 if object not found
        self.assertEqual(response.status_code, 404)
        # No message should be generated in this case, as it's an error before deletion logic.

```

---

## Step 5: HTMX and Alpine.js Integration

**Instructions Compliance:**

- **HTMX for dynamic updates:**
    - The `list.html` uses `hx-get` to load the table content from `{% url 'invoiceagainst_table' %}`.
    - CRUD operations (`Add`, `Edit`, `Delete` buttons) use `hx-get` to load forms/confirmation into `#modalContent`.
    - Forms within the modal use `hx-post` (or `hx-delete` for delete if view handles it directly, but `hx-post` with hidden `_method` is used here for standard CBVs) to submit.
    - Upon successful submission, `hx-swap="none"` is used on the form, and the view responds with `status=204` and an `HX-Trigger` header (`refreshInvoiceAgainstList`, `closeModal`). This ensures the modal closes and the table automatically refreshes without a full page load.
    - Loading indicators (`htmx-indicator`) are implicitly supported by HTMX.

- **Alpine.js for UI state management:**
    - The main modal (`#modal`) uses `x-data="{ showModal: false }"` to manage its visibility.
    - `x-show="showModal"` and `x-on:click.outside="showModal = false"` provide modal control.
    - `x-on:refreshinvoiceagainstlist.window="showModal = false"` and `x-on:closemodal.window="showModal = false"` listen for custom events from `HX-Trigger` to close the modal after a successful form submission.
    - `_="on click add .is-active to #modal"` directly toggles a class on the modal, which can be hooked into Alpine.js's `showModal` state. This provides a clean separation of concerns: HTMX for content loading, Alpine.js for modal state.

- **DataTables for list views:**
    - The `_invoiceagainst_table.html` partial directly includes the JavaScript to initialize DataTables on the `invoiceagainstTable`.
    - `$(document).ready()` ensures DataTables is initialized only after the table HTML is loaded by HTMX.
    - `destroy()` method is used before re-initialization to prevent multiple instances.
    - DataTables provides client-side searching, sorting, and pagination as requested, matching the ASP.NET `GridView` capabilities.

- **No full page reloads:** All CRUD operations and list refreshes are handled dynamically via HTMX.

- **DRY template inheritance:** All templates extend `core/base.html` (as per the prompt's instruction, `base.html` code is not included here).

- **Strict separation of concerns:**
    - Models (`InvoiceAgainst`) contain data definition and potential business logic.
    - Forms (`InvoiceAgainstForm`) handle data validation and cleaning.
    - Views (`InvoiceAgainstListView`, `CreateView`, `UpdateView`, `DeleteView`) are thin, primarily orchestrating requests, forms, and responses, remaining within the 5-15 line limit for core logic.
    - Templates focus on presentation, with HTMX and Alpine.js handling dynamic UI.

- **Tailwind CSS:** All generated HTML includes Tailwind CSS classes for styling.

This comprehensive plan provides a robust and modern Django solution that fully replaces the legacy ASP.NET application's functionality, adhering to all specified constraints and best practices.