## ASP.NET to Django Conversion Script: Country Module Modernization

This document outlines the comprehensive plan for modernizing the existing ASP.NET 'Country' module into a Django 5.0+ application. Our approach prioritizes AI-assisted automation, clean architecture, and modern web standards using Django, HTMX, and Alpine.js.

The primary goal is to transition from a legacy ASP.NET Web Forms application, tightly coupled with database operations directly in the UI layer (GridView, SqlDataSource), to a robust, maintainable, and scalable Django application with a strict separation of concerns (Fat Model, Thin View). Frontend interactivity will be handled exclusively by HTMX and Alpine.js, minimizing JavaScript and ensuring a highly dynamic user experience without complex frontend frameworks.

This plan is presented in plain English, focusing on actionable steps and business outcomes to ensure clarity for all stakeholders.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
From the `asp:SqlDataSource` configuration in the `.aspx` file, we identify the database table and its associated columns used in the CRUD operations.

**Analysis:**
- **`SqlDataSource ID="LocalSqlServer"`** specifies the interactions.
- **`SelectCommand="SELECT * FROM [tblCountry]"`**: This clearly identifies the table.
- **`DeleteCommand="DELETE FROM [tblCountry] WHERE [CId] = @CId"`**
- **`InsertCommand="INSERT INTO [tblCountry] ([CountryName],[Currency],[Symbol]) VALUES (@CountryName,@Currency,@Symbol)"`**
- **`UpdateCommand="UPDATE [tblCountry] SET [CountryName] = @CountryName,[Currency] = @Currency,[Symbol] = @Symbol WHERE [CId] = @CId"`**

**Extracted Schema:**
- **Table Name:** `tblCountry`
- **Columns:**
    - `CId` (Integer, assumed Primary Key based on `DataKeyNames="CId"` and usage in WHERE clauses)
    - `CountryName` (String)
    - `Currency` (String)
    - `Symbol` (String)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic in the ASP.NET code.

**Instructions:**
We analyze the `SqlDataSource` commands and the C# code-behind event handlers (`GridView1_RowCommand`, `GridView1_RowUpdated`, `GridView1_RowDeleted`).

**Functionality Overview:**
- **Create (Add New Country):**
    - Triggered by `btnInsert` (in `FooterTemplate` or `EmptyDataTemplate`) with `CommandName="Add"` or `CommandName="Add1"`.
    - C# `GridView1_RowCommand` handles this by reading values from `TextBox` controls (`txtName`, `txtCurrency`, `txtSymbol`) and setting `LocalSqlServer.InsertParameters`.
    - `LocalSqlServer.Insert()` executes the `INSERT` command.
    - Post-insert: `lblMessage` is updated to "Record Inserted", and the page redirects, causing a full reload.
- **Read (List Countries):**
    - `GridView1` binds to `LocalSqlServer` using `SelectCommand="SELECT * FROM [tblCountry]"`.
    - Displays `CId`, `CountryName`, `Currency`, `Symbol`.
    - Paging is enabled (`AllowPaging="True"`, `PageSize="20"`).
- **Update (Edit Existing Country):**
    - Triggered by `CommandField ShowEditButton="True"`.
    - C# `GridView1_RowUpdated` updates `lblMessage` to "Record Updated". (The `GridView` typically handles the `UPDATE` operation directly when `AutoGenerateEditButton` is true and `SqlDataSource` is configured).
- **Delete (Remove Country):**
    - Triggered by `CommandField ShowDeleteButton="True"` (though `Visible="false"` in ASPX, the command is defined).
    - C# `GridView1_RowDeleted` updates `lblMessage` to "Record Deleted". (Similar to update, `GridView` handles the `DELETE` operation directly).
- **Validation:**
    - `asp:RequiredFieldValidator` is used for `CountryName`, `Currency`, and `Symbol` fields during insert and update operations, ensuring they are not empty.
- **Client-Side Confirmation:**
    - JavaScript functions `confirmationAdd()`, `confirmationUpdate()`, `confirmationDelete()` are called `OnClientClick` for respective actions.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, mapping them to Django templates, HTMX, and Alpine.js.

**Inferred UI Components and Django Equivalents:**
- **`asp:GridView`**: This will be replaced by a standard HTML `<table>` element rendered by Django, managed by DataTables for client-side features (pagination, sorting, search). HTMX will be used to load and refresh this table.
- **`asp:TemplateField` (for SN, Country Name, Currency, Symbol)**: Each field will be rendered as a table column.
    - **`asp:Label` (display mode)**: Simple Django template variable `{{ obj.field_name }}`.
    - **`asp:TextBox` (edit/insert mode)**: HTML `<input type="text">` rendered by Django forms, styled with Tailwind CSS.
    - **`asp:RequiredFieldValidator`**: Django form validation will handle required fields automatically.
- **`asp:CommandField` (Edit/Delete Buttons)**: These will become standard HTML `<button>` elements with `hx-get` attributes to load edit/delete forms into a modal, and `hx-confirm` for confirmation.
- **`asp:Button ID="btnInsert"`**: This will become an HTML `<button>` with an `hx-get` attribute to load the create form into a modal.
- **`asp:Label ID="lblMessage"`**: Django's `messages` framework will be used to display feedback messages, which will be styled with Tailwind CSS and optionally shown with Alpine.js.
- **Master Page and Content Placeholders**: Replaced by Django's template inheritance, extending `core/base.html`.
- **CSS (`yui-datatable.css`, `StyleSheet.css`) and JavaScript (`PopUpMsg.js`, `loadingNotifier.js`)**: These will be replaced by DataTables (CDN), Tailwind CSS for styling, and HTMX/Alpine.js for all dynamic interactions and modals. No custom JavaScript beyond DataTables initialization will be needed.

### Step 4: Generate Django Code

We will create a new Django application named `sysadmin` to house this module.

#### 4.1 Models (`sysadmin/models.py`)

**Task:** Create a Django model based on the identified `tblCountry` schema.

**Instructions:**
- The model `Country` will directly map to `tblCountry`.
- `CId` will be mapped to `id` (the Django default primary key) with `db_column='CId'` and `primary_key=True` to align with the existing database schema.
- `managed = False` is crucial to prevent Django from attempting to create/modify this table, as it already exists.

```python
from django.db import models

class Country(models.Model):
    # CId is the primary key in the existing database
    id = models.IntegerField(db_column='CId', primary_key=True) 
    country_name = models.CharField(db_column='CountryName', max_length=255, blank=False, null=False)
    currency = models.CharField(db_column='Currency', max_length=50, blank=False, null=False)
    symbol = models.CharField(db_column='Symbol', max_length=10, blank=False, null=False)

    class Meta:
        managed = False  # Important: Tells Django not to manage this table (it already exists)
        db_table = 'tblCountry'  # Maps to the existing database table name
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'
        ordering = ['country_name'] # Default ordering

    def __str__(self):
        return self.country_name

    # Example of a 'fat model' business logic method (if identified in original ASP.NET)
    # No complex logic identified in this simple ASP.NET example, but it would go here.
    # def get_full_country_info(self):
    #     return f"{self.country_name} ({self.currency} {self.symbol})"

```

#### 4.2 Forms (`sysadmin/forms.py`)

**Task:** Define a Django form for `Country` model, replicating input fields and validation.

**Instructions:**
- Create a `ModelForm` named `CountryForm` for `Country` model.
- Include `country_name`, `currency`, `symbol` fields.
- Apply Tailwind CSS classes via `widgets` for consistent styling.
- Django's `ModelForm` handles `RequiredFieldValidator` equivalents automatically by default for fields that are `blank=False`.

```python
from django import forms
from .models import Country

class CountryForm(forms.ModelForm):
    class Meta:
        model = Country
        fields = ['country_name', 'currency', 'symbol']
        widgets = {
            'country_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 'placeholder': 'Enter Country Name'}),
            'currency': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 'placeholder': 'Enter Currency'}),
            'symbol': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 'placeholder': 'Enter Symbol'}),
        }
        
    # Example of custom validation (if specific ASP.NET validation rules beyond required fields existed)
    # def clean_country_name(self):
    #     country_name = self.cleaned_data['country_name']
    #     if Country.objects.filter(country_name=country_name).exists():
    #         raise forms.ValidationError("This country name already exists.")
    #     return country_name
```

#### 4.3 Views (`sysadmin/views.py`)

**Task:** Implement CRUD operations using Django Class-Based Views (CBVs), keeping them thin and leveraging HTMX for dynamic partial updates.

**Instructions:**
- Define `ListView`, `CreateView`, `UpdateView`, and `DeleteView` for `Country`.
- Add a `TablePartialView` for HTMX-driven table refreshes.
- Ensure all views respect the 5-15 line limit by offloading logic to models or Django's built-in CBV functionalities.
- Implement `HX-Trigger` headers for HTMX to refresh the table after successful CRUD operations.
- Handle `form_invalid` responses for HTMX, re-rendering the form with errors.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.template.loader import render_to_string # Used for HTMX partials
from .models import Country
from .forms import CountryForm

class CountryListView(ListView):
    model = Country
    template_name = 'sysadmin/country/list.html'
    context_object_name = 'countries'

class CountryTablePartialView(ListView):
    # This view is specifically for HTMX requests to refresh the table content.
    model = Country
    template_name = 'sysadmin/country/_country_table.html'
    context_object_name = 'countries'

class CountryCreateView(CreateView):
    model = Country
    form_class = CountryForm
    template_name = 'sysadmin/country/_country_form.html' # Partial template for modal
    success_url = reverse_lazy('country_list') # Fallback if not HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Country added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, just a trigger
                headers={'HX-Trigger': 'refreshCountryList'}
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # Re-render the form with errors for HTMX to swap in
            return HttpResponse(render_to_string(self.template_name, {'form': form}, request=self.request), status=400)
        return response

class CountryUpdateView(UpdateView):
    model = Country
    form_class = CountryForm
    template_name = 'sysadmin/country/_country_form.html' # Partial template for modal
    success_url = reverse_lazy('country_list') # Fallback if not HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Country updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, just a trigger
                headers={'HX-Trigger': 'refreshCountryList'}
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # Re-render the form with errors for HTMX to swap in
            return HttpResponse(render_to_string(self.template_name, {'form': form, 'object': form.instance}, request=self.request), status=400)
        return response

class CountryDeleteView(DeleteView):
    model = Country
    template_name = 'sysadmin/country/_country_confirm_delete.html' # Partial template for modal
    success_url = reverse_lazy('country_list') # Fallback if not HTMX

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Country deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, just a trigger
                headers={'HX-Trigger': 'refreshCountryList'}
            )
        return response

    def get(self, request, *args, **kwargs):
        # This handles the GET request for showing the delete confirmation in the modal.
        self.object = self.get_object()
        context = self.get_context_data(object=self.object)
        return self.render_to_response(context)

```

#### 4.4 Templates

**Task:** Create Django HTML templates for the list view, and partial templates for forms and delete confirmation (to be loaded via HTMX into a modal).

**Instructions:**
- All main templates extend `core/base.html`.
- Use DataTables for the list view, initializing it via JavaScript.
- Use HTMX for modal loading and form submissions, ensuring `hx-trigger` for table refreshes.
- Use Alpine.js for modal visibility toggling.
- Use Tailwind CSS classes for all styling.

**`sysadmin/country/list.html`**
```html
{% extends 'core/base.html' %}
{% load static %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Countries</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
            hx-get="{% url 'country_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i>Add New Country
        </button>
    </div>
    
    <!-- Container for HTMX-loaded DataTables -->
    <div id="countryTable-container"
         hx-trigger="load, refreshCountryList from:body"
         hx-get="{% url 'country_table' %}"
         hx-swap="innerHTML">
        <!-- Loading spinner while content loads -->
        <div class="flex justify-center items-center h-48">
            <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            <p class="ml-4 text-gray-600">Loading countries...</p>
        </div>
    </div>
    
    <!-- Modal for forms (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 transition-opacity duration-300 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ show: false }" x-show="show" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
        <div id="modalContent" 
             class="bg-white p-6 rounded-lg shadow-xl max-w-xl w-full mx-4 transform transition-all sm:my-8 sm:align-middle sm:w-full"
             x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is loaded from base.html
    document.addEventListener('htmx:afterSwap', function(event) {
        // Re-initialize Alpine on modal content swap to ensure new components are processed
        if (event.detail.target.id === 'modalContent') {
            Alpine.init();
            // Show the modal if content was swapped in
            document.getElementById('modal').classList.add('is-active');
        }
    });

    document.addEventListener('htmx:beforeSwap', function(event) {
        // If content is swapped out (e.g., after form submission returns 204), hide modal
        if (event.detail.xhr.status === 204 && event.detail.target.id === 'modalContent') {
            document.getElementById('modal').classList.remove('is-active');
        }
    });
</script>
{% endblock %}

```

**`sysadmin/country/_country_table.html`** (Partial for HTMX)
```html
{% load static %}
<div class="overflow-x-auto bg-white rounded-lg shadow overflow-y-auto relative"
     x-data="{}"> {# Empty Alpine data scope for element, ensures Alpine processes it #}
    <table id="countryTable" class="min-w-full leading-normal">
        <thead>
            <tr>
                <th class="py-3 px-5 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                <th class="py-3 px-5 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Country Name</th>
                <th class="py-3 px-5 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Currency</th>
                <th class="py-3 px-5 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Symbol</th>
                <th class="py-3 px-5 border-b-2 border-gray-200 bg-gray-100 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for country in countries %}
            <tr>
                <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm">{{ forloop.counter }}</td>
                <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm text-gray-900">{{ country.country_name }}</td>
                <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm text-gray-900">{{ country.currency }}</td>
                <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm text-gray-900 text-center">{{ country.symbol }}</td>
                <td class="px-5 py-5 border-b border-gray-200 bg-white text-sm text-center">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md text-xs mr-2 transition duration-150 ease-in-out"
                        hx-get="{% url 'country_edit' country.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-xs transition duration-150 ease-in-out"
                        hx-get="{% url 'country_delete' country.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal"
                        hx-confirm="Are you sure you want to delete '{{ country.country_name }}'? This action cannot be undone.">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
            {% if not countries %}
            <tr>
                <td colspan="5" class="px-5 py-5 border-b border-gray-200 bg-white text-sm text-center text-gray-500">No countries found. Click 'Add New Country' to create one.</td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after the table has been loaded by HTMX
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#countryTable')) {
            $('#countryTable').DataTable().destroy();
        }
        $('#countryTable').DataTable({
            "pageLength": 10, // Corresponds to ASP.NET PageSize="20" but 10 is common, adjust if needed
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 4] } // Disable sorting for SN and Actions columns
            ]
        });
    });
</script>

```

**`sysadmin/country/_country_form.html`** (Partial for HTMX)
```html
{% load tailwind_filters %} {# Assuming tailwind_filters is configured #}
<div class="p-6" x-data="{}">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Country</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" 
          hx-indicator="#form-loading-indicator">
        {% csrf_token %}
        
        <div class="space-y-4">
            {{ form|crispy }} {# Renders form fields with Tailwind CSS #}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-md transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Save
            </button>
            <span id="form-loading-indicator" class="htmx-indicator ml-4 text-blue-600">
                <i class="fas fa-spinner fa-spin"></i> Saving...
            </span>
        </div>
    </form>
</div>
```

**`sysadmin/country/_country_confirm_delete.html`** (Partial for HTMX)
```html
<div class="p-6" x-data="{}">
    <h3 class="text-xl font-semibold text-red-700 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the country: <span class="font-bold">{{ object.country_name }}</span>?</p>
    <p class="text-red-600 mb-8">This action cannot be undone.</p>
    
    <form hx-post="{% url 'country_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-md transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Confirm Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`sysadmin/urls.py`)

**Task:** Define URL patterns for the `Country` module's views.

**Instructions:**
- Create paths for the main list view, the partial table view (for HTMX refresh), and CRUD operations.
- Use `int:pk` for identifying specific `Country` objects for update and delete.

```python
from django.urls import path
from .views import CountryListView, CountryCreateView, CountryUpdateView, CountryDeleteView, CountryTablePartialView

urlpatterns = [
    path('country/', CountryListView.as_view(), name='country_list'),
    path('country/add/', CountryCreateView.as_view(), name='country_add'),
    path('country/edit/<int:pk>/', CountryUpdateView.as_view(), name='country_edit'),
    path('country/delete/<int:pk>/', CountryDeleteView.as_view(), name='country_delete'),
    # HTMX-specific endpoint for refreshing the table content
    path('country/table/', CountryTablePartialView.as_view(), name='country_table'),
]

```
**Note:** Remember to include this `sysadmin/urls.py` in your project's main `urls.py` (e.g., `path('sysadmin/', include('sysadmin.urls'))`).

#### 4.6 Tests (`sysadmin/tests.py`)

**Task:** Write comprehensive unit tests for the `Country` model and integration tests for its views to ensure functionality and coverage.

**Instructions:**
- Include tests for model creation, field attributes, and any potential model methods (if business logic were added).
- Include tests for all view types: `GET` and `POST` requests for create, update, delete, and `GET` for list.
- Verify template usage, context data, and HTTP status codes.
- Test HTMX interactions by asserting `HX-Trigger` headers and status codes (e.g., 204 No Content).

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import Country
from .forms import CountryForm

class CountryModelTest(TestCase):
    """
    Unit tests for the Country model.
    """
    @classmethod
    def setUpTestData(cls):
        # Set up non-modified objects used by all test methods
        cls.country1 = Country.objects.create(
            country_name='Testland', 
            currency='TLD', 
            symbol='T'
        )
        cls.country2 = Country.objects.create(
            country_name='Otherland', 
            currency='OTL', 
            symbol='O'
        )
  
    def test_country_creation(self):
        """
        Verify that a Country object can be created correctly.
        """
        self.assertEqual(self.country1.country_name, 'Testland')
        self.assertEqual(self.country1.currency, 'TLD')
        self.assertEqual(self.country1.symbol, 'T')
        self.assertIsInstance(self.country1, Country)
        
    def test_country_str_method(self):
        """
        Verify the __str__ method returns the country name.
        """
        self.assertEqual(str(self.country1), 'Testland')

    def test_country_field_labels(self):
        """
        Verify verbose names for model fields.
        """
        field_country_name = self.country1._meta.get_field('country_name').verbose_name
        field_currency = self.country1._meta.get_field('currency').verbose_name
        field_symbol = self.country1._meta.get_field('symbol').verbose_name
        
        self.assertEqual(field_country_name, 'country name') # Django defaults to lowercase with spaces
        self.assertEqual(field_currency, 'currency')
        self.assertEqual(field_symbol, 'symbol')

    def test_country_meta_options(self):
        """
        Verify Meta options like db_table and managed.
        """
        self.assertEqual(self.country1._meta.db_table, 'tblCountry')
        self.assertFalse(self.country1._meta.managed)
        self.assertEqual(self.country1._meta.verbose_name, 'Country')
        self.assertEqual(self.country1._meta.verbose_name_plural, 'Countries')

class CountryViewsTest(TestCase):
    """
    Integration tests for Country views.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.country1 = Country.objects.create(
            country_name='ViewTestCountry',
            currency='VTC',
            symbol='V'
        )
        cls.country2 = Country.objects.create(
            country_name='AnotherViewTest',
            currency='AVT',
            symbol='A'
        )
    
    def setUp(self):
        # Set up a new client for each test method to ensure isolated state
        self.client = Client()
    
    def test_country_list_view(self):
        """
        Test the Country list view (GET request).
        """
        response = self.client.get(reverse('country_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/country/list.html')
        self.assertIn('countries', response.context)
        self.assertEqual(list(response.context['countries']), [self.country1, self.country2])

    def test_country_table_partial_view(self):
        """
        Test the HTMX partial for the country table.
        """
        response = self.client.get(reverse('country_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/country/_country_table.html')
        self.assertIn('countries', response.context)
        self.assertEqual(list(response.context['countries']), [self.country1, self.country2])
        self.assertContains(response, 'ViewTestCountry') # Check if content is present

    def test_country_create_view_get(self):
        """
        Test GET request for adding a new country.
        """
        response = self.client.get(reverse('country_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/country/_country_form.html')
        self.assertIsInstance(response.context['form'], CountryForm)

    def test_country_create_view_post_success(self):
        """
        Test POST request for adding a new country successfully (HTMX).
        """
        initial_country_count = Country.objects.count()
        data = {
            'country_name': 'NewCountry',
            'currency': 'NCU',
            'symbol': 'N'
        }
        response = self.client.post(reverse('country_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCountryList')
        self.assertEqual(Country.objects.count(), initial_country_count + 1)
        self.assertTrue(Country.objects.filter(country_name='NewCountry').exists())

    def test_country_create_view_post_invalid(self):
        """
        Test POST request for adding a new country with invalid data (HTMX).
        """
        initial_country_count = Country.objects.count()
        data = {
            'country_name': '', # Invalid data (required field empty)
            'currency': 'X',
            'symbol': 'Y'
        }
        response = self.client.post(reverse('country_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400) # Bad Request for invalid form
        self.assertTemplateUsed(response, 'sysadmin/country/_country_form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)
        self.assertEqual(Country.objects.count(), initial_country_count) # No new object created

    def test_country_update_view_get(self):
        """
        Test GET request for editing an existing country.
        """
        response = self.client.get(reverse('country_edit', args=[self.country1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/country/_country_form.html')
        self.assertIsInstance(response.context['form'], CountryForm)
        self.assertEqual(response.context['form'].instance, self.country1)

    def test_country_update_view_post_success(self):
        """
        Test POST request for updating an existing country successfully (HTMX).
        """
        data = {
            'country_name': 'UpdatedCountry',
            'currency': 'UCC',
            'symbol': 'U'
        }
        response = self.client.post(reverse('country_edit', args=[self.country1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCountryList')
        self.country1.refresh_from_db()
        self.assertEqual(self.country1.country_name, 'UpdatedCountry')

    def test_country_update_view_post_invalid(self):
        """
        Test POST request for updating an existing country with invalid data (HTMX).
        """
        original_name = self.country1.country_name
        data = {
            'country_name': '', # Invalid data
            'currency': 'X',
            'symbol': 'Y'
        }
        response = self.client.post(reverse('country_edit', args=[self.country1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400)
        self.assertTemplateUsed(response, 'sysadmin/country/_country_form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)
        self.country1.refresh_from_db()
        self.assertEqual(self.country1.country_name, original_name) # Name should not have changed

    def test_country_delete_view_get(self):
        """
        Test GET request for delete confirmation.
        """
        response = self.client.get(reverse('country_delete', args=[self.country1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/country/_country_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.country1)

    def test_country_delete_view_post_success(self):
        """
        Test POST request for deleting a country successfully (HTMX).
        """
        country_to_delete_pk = self.country2.pk
        response = self.client.post(reverse('country_delete', args=[country_to_delete_pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCountryList')
        self.assertFalse(Country.objects.filter(pk=country_to_delete_pk).exists())
        self.assertEqual(Country.objects.count(), 1) # Only country1 should remain
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
- **HTMX:**
    - Used for loading the `_country_table.html` partial (initial load and after CRUD operations).
    - Used for loading `_country_form.html` (for Add/Edit) and `_country_confirm_delete.html` into a modal.
    - Form submissions for Create/Update/Delete use `hx-post` to the respective URLs, with `hx-swap="none"` and `hx-trigger="refreshCountryList"` (via `HX-Trigger` header from views) to ensure the table refreshes and modal closes upon success.
    - `hx-confirm` is used on delete buttons for simple confirmation dialogs.
    - Loading indicators (`htmx-indicator`) are used on forms to provide feedback during submission.
- **Alpine.js:**
    - Manages the visibility of the modal (`#modal`).
    - The `on click add .is-active to #modal` syntax (using `_` for htmx.org/extensions/alpine-morph) declaratively toggles the modal's `is-active` class.
    - `x-data` and `x-show` with `x-transition` provide smooth modal animations.
- **DataTables:**
    - Initialized within the `_country_table.html` partial's `<script>` block. This ensures that DataTables is re-initialized whenever the table content is reloaded by HTMX, correctly applying its features to the new DOM elements.
    - Configured for basic pagination (`pageLength`) and responsive behavior.
    - Columns for SN and Actions are explicitly set as non-orderable.

**Final Notes:**

This comprehensive plan provides a clear, actionable roadmap for migrating the ASP.NET Country module to Django. By leveraging AI-assisted automation, fat models, thin views, HTMX, Alpine.js, and DataTables, we achieve a modern, maintainable, and highly interactive web application with minimal manual JavaScript development. The focus on test coverage ensures the reliability and robustness of the new system.