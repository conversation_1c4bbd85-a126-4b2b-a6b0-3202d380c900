## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
From the `SqlDataSource` definitions and `GridView` bindings:
- `tblState` is the primary table.
- `tblCountry` is a lookup table for Country names.
- `tblCity` is implicitly referenced for a deletion constraint, implying a foreign key relationship with `tblState`.

**Extracted Schema:**

*   **[TABLE_NAME]**: `tblState`
    *   **Columns**:
        *   `SId`: Primary Key, `Int32` (mapped to Django `IntegerField` or `AutoField` depending on actual DB setup, `models.AutoField` is typical for PK).
        *   `StateName`: `String` (mapped to Django `CharField`).
        *   `CId`: Foreign Key to `tblCountry`, `Int32` (mapped to Django `ForeignKey`).

*   **Related Table: `tblCountry`**
    *   **Columns**:
        *   `CId`: Primary Key, `Int32`.
        *   `CountryName`: `String`.

*   **Related Table: `tblCity`**
    *   **Implicit Columns**:
        *   `SId`: Foreign Key to `tblState`, `Int32`. (Presence inferred from `StrGetDeloff` in C# code, used to check for related records before deletion).

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**

*   **Create (Add New State):**
    *   Triggered by `btnInsert` in the `GridView` footer or `EmptyDataTemplate` via `GridView1_RowCommand` (CommandNames "Add" or "Add1").
    *   Inserts a new row into `tblState` with `StateName` and `CId`.
    *   Includes a `RequiredFieldValidator` for `StateName`.
    *   Redirects to the same page (`Page.Response.Redirect`).

*   **Read (Display States):**
    *   Handled by `loadata()` which fetches all records from `tblState` (`SELECT * FROM [tblState] Order By SId Desc`) and binds them to `GridView1`.
    *   Pagination is enabled (`AllowPaging="True"`).
    *   The `getCnt()` method fetches the associated `CountryName` for each state and checks if a state can be deleted (by looking up in `tblCity`).

*   **Update (Edit State):**
    *   Triggered by the `Edit` link button in `GridView` (handled by `GridView1_RowEditing` and `GridView1_RowUpdating`).
    *   Updates `StateName` and `CId` for an existing `SId` in `tblState`.
    *   Includes a `RequiredFieldValidator` for `StateName`.
    *   Redirects to the same page.

*   **Delete (Remove State):**
    *   Triggered by the `Delete` link button in `GridView` via `GridView1_RowCommand` (CommandName "Del").
    *   Deletes a row from `tblState` based on `SId`.
    *   The `getCnt()` method hides the delete button if records exist in `tblCity` with that `SId`. This is a crucial business rule.
    *   Client-side confirmation (`confirmationDelete()`).
    *   Redirects to the same page.

*   **Validation Logic:**
    *   `StateName` is a required field for both insert and update operations.
    *   Deletion is conditional: a state cannot be deleted if there are associated entries in `tblCity`.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

*   **`asp:GridView ID="GridView1"`**: This will be transformed into an HTML `<table>` element managed by DataTables. Each row will represent a `State` object.
    *   **`asp:CommandField ShowEditButton`**: Becomes an "Edit" button in a row, triggering an HTMX request to load an edit form in a modal.
    *   **`asp:LinkButton ID="btndel" ... CommandName="Del"`**: Becomes a "Delete" button, triggering an HTMX request to load a confirmation dialog in a modal.
    *   **`asp:Label ID="lblID"` (for `SId`)**: Becomes part of Django ORM's object ID, not directly displayed but used for actions.
    *   **`asp:TemplateField HeaderText="SN"`**: Handled by Django template loop `forloop.counter`.
    *   **`asp:DropDownList ID="DrpCountry", "DrpCountry1", "DrpCountry2"`**: Becomes a Django `forms.ModelChoiceField` within the `StateForm` to select a `Country`.
    *   **`asp:Label ID="lblName"` (for `StateName`)**: Displays `StateName` in table row.
    *   **`asp:TextBox ID="lblName0", "txtName"` (for `StateName`)**: Becomes a Django `forms.TextInput` within the `StateForm`.
    *   **`asp:RequiredFieldValidator`**: Handled by Django form validation.
    *   **`asp:Button ID="btnInsert"`**: Becomes an "Add New State" button outside the table, triggering an HTMX request to load an add form in a modal.
*   **`asp:Label ID="lblMessage"`**: Django's `messages` framework will be used to display success/error messages.
*   **Client-side `PopUpMsg.js`, `loadingNotifier.js`**: These will be replaced by HTMX for dynamic interactions, Alpine.js for UI state management (like modals), and DataTables for the table functionality. Explicit `confirmationDelete()` etc. will be replaced by modal confirmations.

### Step 4: Generate Django Code

#### 4.1 Models

**Task:** Create Django models based on the database schema.

**Instructions:**
- `Country` model for `tblCountry`.
- `City` model (minimal) for `tblCity` to check for deletion constraints.
- `State` model for `tblState`, with a `ForeignKey` to `Country` and a `can_be_deleted` method.

```python
# sysadmin/models.py
from django.db import models

class Country(models.Model):
    """
    Maps to the existing tblCountry table.
    Used for country lookup in State creation/editing.
    """
    cid = models.IntegerField(db_column='CId', primary_key=True)
    country_name = models.CharField(db_column='CountryName', max_length=100)

    class Meta:
        managed = False  # Tells Django not to manage this table's schema (it already exists)
        db_table = 'tblCountry'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'
        ordering = ['country_name'] # Order for dropdowns

    def __str__(self):
        return self.country_name

class City(models.Model):
    """
    Minimal model mapping to tblCity.
    Used solely to check for existing related records for deletion constraint.
    Assuming SId is the foreign key column in tblCity.
    """
    # Assuming primary key for tblCity is CId, but we don't need it.
    # We only need the SId foreign key.
    # If City had its own PK, it would be included here.
    # For now, we'll assume a dummy PK to satisfy Django's model requirements
    # if no explicit PK is available, or rely on existing PK if one is defined.
    # Let's assume a dummy PK for demonstration if PK is not needed for check.
    # If actual PK is needed for managed=False, uncomment below.
    # id = models.AutoField(primary_key=True) # Or whatever the actual PK is

    state = models.ForeignKey('State', models.DO_NOTHING, db_column='SId') # DO_NOTHING means no ON DELETE CASCADE

    class Meta:
        managed = False
        db_table = 'tblCity'
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        # Placeholder, as we don't need to display City objects from this model
        return f"City related to State {self.state.state_name}"


class State(models.Model):
    """
    Maps to the existing tblState table.
    Represents geographical states and their associated countries.
    """
    sid = models.AutoField(db_column='SId', primary_key=True) # SId is the PK from ASP.NET
    state_name = models.CharField(db_column='StateName', max_length=100)
    country = models.ForeignKey(Country, models.DO_NOTHING, db_column='CId') # CId is the FK to tblCountry

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblState'
        verbose_name = 'State'
        verbose_name_plural = 'States'
        ordering = ['-sid'] # From ASP.NET 'Order By SId Desc'

    def __str__(self):
        return self.state_name
        
    def can_be_deleted(self):
        """
        Business logic: A state cannot be deleted if it has associated cities.
        Replicates the logic from getCnt() in the ASP.NET code-behind.
        """
        return not self.city_set.exists() # Check if any related City objects exist
```

#### 4.2 Forms

**Task:** Define a Django form for user input.

**Instructions:**
- Create a `ModelForm` for the `State` model.
- Include `state_name` and `country` fields.
- Add Tailwind CSS classes for styling via `widgets`.
- Django's `RequiredFieldValidator` equivalent is handled automatically by `ModelForm` for non-blank fields.

```python
# sysadmin/forms.py
from django import forms
from .models import State, Country

class StateForm(forms.ModelForm):
    """
    Form for creating and updating State objects.
    Automatically handles required fields based on model definition.
    """
    country = forms.ModelChoiceField(
        queryset=Country.objects.all(),
        empty_label="Select Country",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label="Country Name" # Matches ASP.NET column header
    )

    class Meta:
        model = State
        fields = ['state_name', 'country']
        widgets = {
            'state_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'state_name': 'State Name', # Matches ASP.NET column header
        }
```

#### 4.3 Views

**Task:** Implement CRUD operations using CBVs.

**Instructions:**
- Define `StateListView` to display the main page.
- `StateTablePartialView` to render just the DataTables content for HTMX.
- `StateCreateView`, `StateUpdateView`, `StateDeleteView` for the respective CRUD operations, keeping them thin.
- Business logic for deletion is in the `State` model's `can_be_deleted` method.
- Use `messages.success` for feedback and `HX-Trigger` headers for HTMX refreshes.

```python
# sysadmin/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseBadRequest
from django.db.models import ProtectedError
from .models import State, Country # Import Country too for potential future direct use
from .forms import StateForm

class StateListView(ListView):
    """
    Main view to display the list of States.
    This serves the initial page load.
    """
    model = State
    template_name = 'sysadmin/state/list.html'
    context_object_name = 'states' # Renamed from [MODEL_NAME_PLURAL_LOWER] for clarity

    # No need to load data here, _state_table.html will load it via HTMX

class StateTablePartialView(ListView):
    """
    Renders only the HTML table for States.
    This view is specifically designed to be loaded via HTMX
    to refresh the table content dynamically.
    """
    model = State
    template_name = 'sysadmin/state/_state_table.html'
    context_object_name = 'states'
    # The ordering is handled by the model's Meta.ordering

class StateCreateView(CreateView):
    """
    Handles creation of new State objects.
    Uses a modal form loaded via HTMX.
    """
    model = State
    form_class = StateForm
    template_name = 'sysadmin/state/_state_form.html' # Partial template for modal
    success_url = reverse_lazy('state_list') # Fallback if not HTMX

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass a flag to the template to indicate this is an Add form
        context['is_add_form'] = True 
        return context

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'State added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content response
            # with an HX-Trigger header to signal the client to refresh the list.
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshStateList' # Event name to trigger on client
                }
            )
        return response # For non-HTMX requests (unlikely in this setup)

    def form_invalid(self, form):
        # For HTMX requests, re-render the form with errors
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


class StateUpdateView(UpdateView):
    """
    Handles updating existing State objects.
    Uses a modal form loaded via HTMX.
    """
    model = State
    form_class = StateForm
    template_name = 'sysadmin/state/_state_form.html' # Partial template for modal
    context_object_name = 'state'
    success_url = reverse_lazy('state_list') # Fallback if not HTMX

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass a flag to the template to indicate this is an Edit form
        context['is_add_form'] = False 
        return context

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'State updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshStateList'
                }
            )
        return response

    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


class StateDeleteView(DeleteView):
    """
    Handles deletion of State objects.
    Uses a confirmation modal loaded via HTMX.
    Includes business logic to prevent deletion if related City records exist.
    """
    model = State
    template_name = 'sysadmin/state/_state_confirm_delete.html' # Partial template
    context_object_name = 'state'
    success_url = reverse_lazy('state_list') # Fallback if not HTMX

    def post(self, request, *args, **kwargs):
        self.object = self.get_object()
        # Business logic from ASP.NET: check if state can be deleted
        if not self.object.can_be_deleted():
            messages.error(self.request, f'Cannot delete State "{self.object.state_name}" as it has associated cities.')
            if request.headers.get('HX-Request'):
                # For HTMX, return a 200 OK and re-render the modal with error message
                return self.render_to_response(self.get_context_data(object=self.object))
            # For non-HTMX, redirect back to list
            return HttpResponseRedirect(self.get_success_url())
        
        try:
            # Proceed with deletion if allowed
            response = self.delete(request, *args, **kwargs)
            messages.success(self.request, 'State deleted successfully.')
            if request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshStateList'
                    }
                )
            return response
        except ProtectedError:
            # Django's ProtectedError for DO_NOTHING if other relations exist
            messages.error(self.request, f'Cannot delete State "{self.object.state_name}" due to existing related records.')
            if request.headers.get('HX-Request'):
                return self.render_to_response(self.get_context_data(object=self.object))
            return HttpResponseRedirect(self.get_success_url())

    def delete(self, request, *args, **kwargs):
        # Override delete to ensure messages are handled before actual delete
        # The main logic is in post()
        return super().delete(request, *args, **kwargs)
```

#### 4.4 Templates

**Task:** Create templates for each view.

**Instructions:**
- `list.html` will be the main entry point, containing a modal structure.
- `_state_table.html` will be a partial that HTMX loads into `list.html`.
- `_state_form.html` will be a partial for add/edit operations in the modal.
- `_state_confirm_delete.html` will be a partial for delete confirmations in the modal.
- All templates will use Tailwind CSS classes and HTMX attributes.

```html
{# sysadmin/templates/sysadmin/state/list.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">States</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'state_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New State
        </button>
    </div>
    
    {# Messages display (similar to asp:Label lblMessage) #}
    {% if messages %}
    <div class="mb-4">
        {% for message in messages %}
        <div class="p-3 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-800{% elif message.tags == 'error' %}bg-red-100 text-red-800{% else %}bg-blue-100 text-blue-800{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <div id="stateTable-container"
         hx-trigger="load, refreshStateList from:body" {# Loads on page load, refreshes on custom event #}
         hx-get="{% url 'state_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading States...</p>
        </div>
    </div>
    
    <!-- Modal for form/confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4"
             _="on htmx:afterSwap if not event.detail.xhr.status == 204 then add .is-active to #modal else remove .is-active from #modal">
            <!-- Content loaded via HTMX (form or delete confirmation) -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# Alpine.js is included in base.html usually. No extra JS here. #}
{% endblock %}
```

```html
{# sysadmin/templates/sysadmin/state/_state_table.html #}
<table id="stateTable" class="min-w-full bg-white border-collapse">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Country Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">State Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for state in states %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ state.country.country_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ state.state_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2 text-sm"
                    hx-get="{% url 'state_edit' state.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                {% if state.can_be_deleted %}
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-sm"
                    hx-get="{% url 'state_delete' state.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
                {% else %}
                <span class="text-gray-500 text-sm italic py-1 px-2 rounded">Delete blocked (has cities)</span>
                {% endif %}
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="4" class="py-4 px-4 text-center text-gray-500">No states found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // DataTables initialization
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#stateTable')) {
            $('#stateTable').DataTable().destroy();
        }
        $('#stateTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "searching": true,
            "ordering": true,
            "paging": true,
            "info": true
        });
    });
</script>
```

```html
{# sysadmin/templates/sysadmin/state/_state_form.html #}
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{% if is_add_form %}Add New State{% else %}Edit State{% endif %}</h3>
    <form hx-post="{{ request.path }}" hx-swap="none"> {# hx-swap="none" for 204 No Content #}
        {% csrf_token %}
        
        <div class="space-y-4">
            {# Render all form fields #}
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
            {# Render non-field errors if any #}
            {% if form.non_field_errors %}
            <div class="text-red-500 text-xs mt-1">
                {% for error in form.non_field_errors %}
                    <p>{{ error }}</p>
                {% endfor %}
            </div>
            {% endif %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal"> {# Alpine.js/Hyperscript for modal close #}
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

```html
{# sysadmin/templates/sysadmin/state/_state_confirm_delete.html #}
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    {# Display error messages here if deletion was blocked #}
    {% if messages %}
    <div class="mb-4">
        {% for message in messages %}
        <div class="p-3 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-800{% elif message.tags == 'error' %}bg-red-100 text-red-800{% else %}bg-blue-100 text-blue-800{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <p class="text-gray-700 mb-6">Are you sure you want to delete the state "{{ state.state_name }}"?</p>
    
    <div class="mt-6 flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click remove .is-active from #modal"> {# Alpine.js/Hyperscript for modal close #}
            Cancel
        </button>
        <button 
            type="button" 
            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
            hx-post="{% url 'state_delete' state.pk %}"
            hx-swap="none" {# hx-swap="none" for 204 No Content #}
            hx-confirm="This action cannot be undone. Confirm deletion?"> {# Client-side prompt before POST #}
            Delete
        </button>
    </div>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
- Include paths for list, add, edit, delete, and the table partial.

```python
# sysadmin/urls.py
from django.urls import path
from .views import StateListView, StateTablePartialView, StateCreateView, StateUpdateView, StateDeleteView

urlpatterns = [
    # Main page for displaying states
    path('states/', StateListView.as_view(), name='state_list'),
    
    # HTMX endpoint for just the table content (for refresh)
    path('states/table/', StateTablePartialView.as_view(), name='state_table'),
    
    # HTMX endpoints for modal forms
    path('states/add/', StateCreateView.as_view(), name='state_add'),
    path('states/edit/<int:pk>/', StateUpdateView.as_view(), name='state_edit'),
    path('states/delete/<int:pk>/', StateDeleteView.as_view(), name='state_delete'),
]

```

#### 4.6 Tests

**Task:** Write tests for the model and views.

**Instructions:**
- Comprehensive unit tests for model methods (`can_be_deleted`).
- Integration tests for all views (list, create, update, delete), including HTMX interactions and deletion constraints.
- Ensure at least 80% test coverage.

```python
# sysadmin/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.db.utils import IntegrityError
from .models import State, Country, City
from .forms import StateForm

class CountryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a Country for testing
        cls.country1 = Country.objects.create(cid=1, country_name='Test Country A')
        cls.country2 = Country.objects.create(cid=2, country_name='Test Country B')

    def test_country_creation(self):
        self.assertEqual(self.country1.country_name, 'Test Country A')
        self.assertEqual(self.country1.pk, 1)

    def test_country_str_representation(self):
        self.assertEqual(str(self.country1), 'Test Country A')

    def test_country_meta_options(self):
        self.assertEqual(Country._meta.db_table, 'tblCountry')
        self.assertFalse(Country._meta.managed)
        self.assertEqual(Country._meta.verbose_name, 'Country')
        self.assertEqual(Country._meta.verbose_name_plural, 'Countries')
        self.assertEqual(Country._meta.ordering, ['country_name'])

class StateModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a Country for State objects to link to
        cls.country = Country.objects.create(cid=100, country_name='Test Country')
        # Create a State object
        cls.state1 = State.objects.create(sid=1, state_name='Test State 1', country=cls.country)
        cls.state2 = State.objects.create(sid=2, state_name='Test State 2', country=cls.country)

    def test_state_creation(self):
        self.assertEqual(self.state1.state_name, 'Test State 1')
        self.assertEqual(self.state1.country.country_name, 'Test Country')
        self.assertEqual(self.state1.pk, 1)

    def test_state_str_representation(self):
        self.assertEqual(str(self.state1), 'Test State 1')

    def test_state_meta_options(self):
        self.assertEqual(State._meta.db_table, 'tblState')
        self.assertFalse(State._meta.managed)
        self.assertEqual(State._meta.verbose_name, 'State')
        self.assertEqual(State._meta.verbose_name_plural, 'States')
        self.assertEqual(State._meta.ordering, ['-sid'])

    def test_can_be_deleted_no_cities(self):
        # State with no associated cities should be deletable
        self.assertTrue(self.state1.can_be_deleted())

    def test_can_be_deleted_with_cities(self):
        # Create a City associated with state2
        # Note: City model assumed to have 'SId' as FK to State.sid
        City.objects.create(state=self.state2) # Assuming City.id is auto-generated
        # State with associated cities should NOT be deletable
        self.assertFalse(self.state2.can_be_deleted())

class StateViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.country = Country.objects.create(cid=100, country_name='Test Country')
        cls.state1 = State.objects.create(sid=1, state_name='State A', country=cls.country)
        cls.state2 = State.objects.create(sid=2, state_name='State B', country=cls.country)
        cls.state_with_city = State.objects.create(sid=3, state_name='State With City', country=cls.country)
        City.objects.create(state=cls.state_with_city)

    def setUp(self):
        self.client = Client()

    def test_state_list_view(self):
        response = self.client.get(reverse('state_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/state/list.html')
        # We don't check for 'states' in context here because list.html uses HTMX to load it
        # The actual table content is loaded by state_table view.

    def test_state_table_partial_view(self):
        response = self.client.get(reverse('state_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/state/_state_table.html')
        self.assertIn('states', response.context)
        self.assertContains(response, 'State A')
        self.assertContains(response, 'State B')
        self.assertContains(response, 'State With City')
        # Check if delete button is disabled for state with city
        self.assertContains(response, 'Delete blocked (has cities)')
        self.assertNotContains(response, f'hx-get="{reverse("state_delete", args=[self.state_with_city.pk])}"')
        self.assertContains(response, f'hx-get="{reverse("state_delete", args=[self.state1.pk])}"')


    def test_state_create_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('state_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/state/_state_form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['is_add_form'])

    def test_state_create_view_post_htmx_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        initial_count = State.objects.count()
        data = {
            'state_name': 'New State',
            'country': self.country.pk, # Use country PK for form
        }
        response = self.client.post(reverse('state_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # 204 No Content for successful HTMX post
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshStateList')
        self.assertEqual(State.objects.count(), initial_count + 1)
        self.assertTrue(State.objects.filter(state_name='New State').exists())

    def test_state_create_view_post_htmx_invalid(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        initial_count = State.objects.count()
        data = {
            'state_name': '', # Invalid data
            'country': self.country.pk,
        }
        response = self.client.post(reverse('state_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # Re-renders form with errors
        self.assertTemplateUsed(response, 'sysadmin/state/_state_form.html')
        self.assertIn('form', response.context)
        self.assertFalse(response.context['form'].is_valid())
        self.assertIn('state_name', response.context['form'].errors)
        self.assertEqual(State.objects.count(), initial_count)

    def test_state_update_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('state_edit', args=[self.state1.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/state/_state_form.html')
        self.assertIn('form', response.context)
        self.assertFalse(response.context['is_add_form'])
        self.assertEqual(response.context['form'].instance, self.state1)

    def test_state_update_view_post_htmx_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'state_name': 'Updated State A',
            'country': self.country.pk,
        }
        response = self.client.post(reverse('state_edit', args=[self.state1.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshStateList')
        self.state1.refresh_from_db()
        self.assertEqual(self.state1.state_name, 'Updated State A')

    def test_state_delete_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('state_delete', args=[self.state1.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/state/_state_confirm_delete.html')
        self.assertIn('state', response.context)
        self.assertEqual(response.context['state'], self.state1)

    def test_state_delete_view_post_htmx_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        initial_count = State.objects.count()
        response = self.client.post(reverse('state_delete', args=[self.state1.pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshStateList')
        self.assertEqual(State.objects.count(), initial_count - 1)
        self.assertFalse(State.objects.filter(pk=self.state1.pk).exists())

    def test_state_delete_view_post_htmx_blocked_by_city(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        initial_count = State.objects.count()
        response = self.client.post(reverse('state_delete', args=[self.state_with_city.pk]), **headers)
        self.assertEqual(response.status_code, 200) # Renders confirmation modal with error
        self.assertTemplateUsed(response, 'sysadmin/state/_state_confirm_delete.html')
        self.assertContains(response, f'Cannot delete State "{self.state_with_city.state_name}" as it has associated cities.')
        self.assertEqual(State.objects.count(), initial_count) # Object not deleted
        self.assertTrue(State.objects.filter(pk=self.state_with_city.pk).exists()) # Still exists
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
- **HTMX for Dynamic Content:**
    - The `list.html` loads the table content using `hx-get="{% url 'state_table' %}"` and `hx-trigger="load, refreshStateList from:body"`. This means the table is loaded asynchronously on page load and refreshed whenever a `refreshStateList` custom event is fired on the `body` element.
    - CRUD operations (`Add`, `Edit`, `Delete` buttons) use `hx-get` to load the respective forms/confirmations into the `#modalContent` target.
    - Form submissions (`hx-post`) from the modal forms send data back to the server. On success, the server responds with `status=204` (No Content) and an `HX-Trigger` header (`HX-Trigger: refreshStateList`) to tell the client to refresh the table.
    - Delete operations also use `hx-post` and `hx-confirm` for client-side confirmation before the POST. The `StateDeleteView` handles the business logic for preventing deletion if associated cities exist.
- **Alpine.js for UI State Management (Modals):**
    - The `#modal` div uses `hidden` class by default.
    - `on click add .is-active to #modal` (hyperscript syntax used directly in HTML) is used to show the modal when an add/edit/delete button is clicked.
    - `on click if event.target.id == 'modal' remove .is-active from me` allows clicking outside the modal content to close it.
    - `on htmx:afterSwap if not event.detail.xhr.status == 204 then add .is-active to #modal else remove .is-active from #modal` ensures the modal stays open if there are form errors (status 200) or closes if the submission was successful (status 204).
- **DataTables for List Views:**
    - The `_state_table.html` partial contains the `<table id="stateTable">` element.
    - A `<script>` block within this partial initializes DataTables on the `stateTable` ID. This ensures DataTables is re-initialized correctly every time the partial is reloaded by HTMX, maintaining sorting, filtering, and pagination.
    - Basic DataTables options like `pageLength`, `lengthMenu`, `searching`, `ordering`, `paging`, `info` are included.

This comprehensive plan covers the migration of the ASP.NET functionality to a modern Django application using a fat model, thin view, HTMX, and Alpine.js architecture, focusing on automated approaches and clear, business-centric instructions.