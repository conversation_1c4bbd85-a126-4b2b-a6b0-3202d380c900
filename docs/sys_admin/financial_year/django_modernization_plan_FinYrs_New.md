## ASP.NET to Django Conversion Script: Financial Year Management

This plan outlines the modernization of your ASP.NET Financial Year Management module to a modern Django application. We will leverage Django's powerful ORM, Class-Based Views, HTMX for dynamic interactions, Alpine.js for client-side state, and DataTables for efficient data presentation, all styled with Tailwind CSS. This approach prioritizes automation and maintainability, drastically reducing future development and debugging efforts.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the ASP.NET code, we infer the following database tables and columns:

*   **Primary Table:** `tblFinancial_master`
    *   Columns:
        *   `FinYearId` (Primary Key, inferred)
        *   `CompId` (Foreign Key to Company table, inferred from `DropDownNewFYCName.SelectedValue`)
        *   `FinYearFrom` (Date, inferred from `txtFDate` and date operations)
        *   `FinYearTo` (Date, inferred from `txtTDate` and date operations)
*   **Related Table:** `tblCompany` (Inferred from `fun.dropdownCompany` and `DropDownNewFYCName`)
    *   Columns:
        *   `CompId` (Primary Key, inferred)
        *   `CompName` (Company Name, inferred from dropdown text)

### Step 2: Identify Backend Functionality

The ASP.NET page primarily performs the following operations:

*   **Read (Display Existing Financial Years):** When a company is selected from the `DropDownNewFYCName`, the `ListBoxFinYear` is populated with existing financial years for that company, fetched from `tblFinancial_master`. This is a read operation to filter and display data.
*   **Create (New Financial Year):** The `btnNewSubmit_Click` event processes the input `Date From`, `Date To`, and selected `Company` to create a new financial year entry. The redirection to `FinYear_New_Details.aspx` suggests a confirmation or detail view after creation.
*   **Validation:**
    *   Client-side and server-side validation for `Date From` and `Date To` (required and date format).
    *   Required field validation for `Company` dropdown.
    *   Custom date validation logic (`fun.DateValidation`) from `clsFunctions`.

### Step 3: Infer UI Components

*   **`DropDownNewFYCName` (Company Dropdown):** This will be a Django `ModelChoiceField` in the form. Its `AutoPostBack` functionality will be replaced by HTMX to dynamically update the list of financial years without a full page reload.
*   **`ListBoxFinYear` (Financial Year List):** This will be a dynamically updated section on the page, likely rendered as a simple unordered list or a DataTables component if more detail was required, populated via an HTMX endpoint. For this scenario, we will use a DataTables component as it is the standard.
*   **`txtFDate` and `txtTDate` (Date Inputs):** These will be Django `forms.DateField` with appropriate widgets for date selection (e.g., HTML5 `type="date"` or a JavaScript-based picker via Alpine.js). The `CalendarExtender` functionality will be replaced.
*   **`btnNewSubmit` (Submit Button):** This will trigger the form submission via HTMX.
*   **`Label1` (Message Display):** Django's `messages` framework will handle success/error notifications, displayed via Alpine.js or simple HTMX swaps.

---

### Step 4: Generate Django Code

We will create a new Django app, let's call it `financial_years`.

#### 4.1 Models (`financial_years/models.py`)

```python
from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import datetime

class Company(models.Model):
    """
    Represents a Company from the existing tblCompany.
    Set managed=False to prevent Django from creating/altering this table.
    """
    comp_id = models.IntegerField(db_column='CompId', primary_key=True)
    comp_name = models.CharField(db_column='CompName', max_length=255) # Assuming max length
    # Add other fields as identified in tblCompany

    class Meta:
        managed = False
        db_table = 'tblCompany'
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.comp_name

class FinancialYear(models.Model):
    """
    Represents a Financial Year from the existing tblFinancial_master.
    Set managed=False to prevent Django from creating/altering this table.
    """
    fin_year_id = models.AutoField(db_column='FinYearId', primary_key=True) # AutoField for inferred PK
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId') # Use DO_NOTHING for existing FK
    date_from = models.DateField(db_column='FinYearFrom')
    date_to = models.DateField(db_column='FinYearTo')

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'
        unique_together = ('company', 'date_from', 'date_to') # Prevent duplicate entries for same company/dates

    def __str__(self):
        return f"{self.company.comp_name}: {self.get_financial_year_display()}"

    def get_financial_year_display(self):
        """
        Mimics the 'string.Concat(FDY, TDY)' logic from ASP.NET.
        e.g., '20232024' for 2023-04-01 to 2024-03-31
        """
        return f"{self.date_from.year}{self.date_to.year}"

    def clean(self):
        """
        Custom validation for date range and overlap.
        This handles `fun.DateValidation` and logical checks.
        """
        if self.date_from and self.date_to:
            if self.date_from >= self.date_to:
                raise ValidationError("Financial 'Date From' must be before 'Date To'.")
            
            # Check for overlap with existing financial years for the same company
            overlapping_years = FinancialYear.objects.filter(
                company=self.company,
                date_from__lt=self.date_to,
                date_to__gt=self.date_from
            ).exclude(pk=self.pk) # Exclude self for updates

            if overlapping_years.exists():
                raise ValidationError("The proposed financial year overlaps with an existing financial year for this company.")
            
            # Additional logic from fun.DateValidation, if any specific format is needed
            # For Django DateField, default format validation is usually sufficient.
            # If `fun.DateValidation` implies business rules like fiscal year start,
            # those would go here. Assuming it was just format validation for now.
    
    def save(self, *args, **kwargs):
        self.full_clean() # Call full_clean() to run model-level validation before saving
        super().save(*args, **kwargs)

```

#### 4.2 Forms (`financial_years/forms.py`)

```python
from django import forms
from .models import FinancialYear, Company

class FinancialYearForm(forms.ModelForm):
    """
    Form for creating and updating FinancialYear instances.
    """
    company = forms.ModelChoiceField(
        queryset=Company.objects.all().order_by('comp_name'),
        empty_label="Select Company",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label="Company"
    )
    date_from = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label="Date From",
        input_formats=['%Y-%m-%d', '%d-%m-%Y'] # Allow both standard and dd-mm-yyyy from original
    )
    date_to = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label="Date To",
        input_formats=['%Y-%m-%d', '%d-%m-%Y']
    )

    class Meta:
        model = FinancialYear
        fields = ['company', 'date_from', 'date_to']
        # Widgets are defined directly in the form fields for better control and reusability.

    def clean(self):
        """
        Form-level validation to call model's clean method for comprehensive checks.
        """
        cleaned_data = super().clean()
        company = cleaned_data.get('company')
        date_from = cleaned_data.get('date_from')
        date_to = cleaned_data.get('date_to')

        if company and date_from and date_to:
            try:
                # Temporarily create a model instance to run its clean method
                # This ensures fat model validation is triggered
                instance = FinancialYear(
                    company=company,
                    date_from=date_from,
                    date_to=date_to,
                    fin_year_id=self.instance.pk if self.instance.pk else None # Pass PK for updates
                )
                instance.clean()
            except ValidationError as e:
                # Add validation errors to specific fields or non_field_errors
                for field, errors in e.message_dict.items():
                    if field in self.fields:
                        self.add_error(field, errors)
                    else:
                        self.add_error(None, errors) # Non-field errors

        return cleaned_data

```

#### 4.3 Views (`financial_years/views.py`)

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import render, get_object_or_404
from .models import FinancialYear, Company
from .forms import FinancialYearForm

class FinancialYearListView(ListView):
    """
    Displays a list of financial years with a form for adding new ones.
    This serves as the main entry point, combining the list display and the add form.
    """
    model = FinancialYear
    template_name = 'financial_years/financialyear/list.html'
    context_object_name = 'financial_years'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = FinancialYearForm() # Provide an empty form for creation
        # Initial list of financial years will be loaded by HTMX via financialyear_table
        return context

class FinancialYearCreateView(CreateView):
    """
    Handles creation of a new FinancialYear. Designed for HTMX form submission.
    """
    model = FinancialYear
    form_class = FinancialYearForm
    template_name = 'financial_years/financialyear/form.html' # Rendered as a partial for modal
    success_url = reverse_lazy('financialyear_list') # Not directly used for HTMX, but good practice

    def form_valid(self, form):
        # Calls the model's clean method before saving if not already called by self.full_clean()
        response = super().form_valid(form)
        messages.success(self.request, 'Financial Year added successfully.')
        if self.request.headers.get('HX-Request'):
            # Return 204 No Content for HTMX to indicate success and trigger a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshFinancialYearList' # Custom HTMX event to refresh the list
                }
            )
        return response

    def form_invalid(self, form):
        # If form is invalid, return the form with errors (for HTMX partial update)
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form})
        return super().form_invalid(form)

class FinancialYearUpdateView(UpdateView):
    """
    Handles updating an existing FinancialYear. Designed for HTMX form submission.
    """
    model = FinancialYear
    form_class = FinancialYearForm
    template_name = 'financial_years/financialyear/form.html' # Rendered as a partial for modal
    success_url = reverse_lazy('financialyear_list') # Not directly used for HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Financial Year updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshFinancialYearList'
                }
            )
        return response

    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form})
        return super().form_invalid(form)

class FinancialYearDeleteView(DeleteView):
    """
    Handles deletion of a FinancialYear. Designed for HTMX confirmation.
    """
    model = FinancialYear
    template_name = 'financial_years/financialyear/confirm_delete.html' # Rendered as a partial for modal
    success_url = reverse_lazy('financialyear_list') # Not directly used for HTMX

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Financial Year deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshFinancialYearList'
                }
            )
        return response
    
    def get(self, request, *args, **kwargs):
        # Render the confirmation partial for HTMX GET requests
        if request.headers.get('HX-Request'):
            self.object = self.get_object()
            context = self.get_context_data(object=self.object)
            return render(request, self.template_name, context)
        return super().get(request, *args, **kwargs)


class FinancialYearTablePartialView(ListView):
    """
    Returns the HTML for the financial years table, designed to be loaded via HTMX.
    This replaces the dynamic population of ListBoxFinYear.
    It can be filtered by company.
    """
    model = FinancialYear
    template_name = 'financial_years/financialyear/_financialyear_table.html'
    context_object_name = 'financial_years'

    def get_queryset(self):
        queryset = super().get_queryset().order_by('company__comp_name', '-date_from')
        company_id = self.request.GET.get('company_id')
        if company_id and company_id != 'Select': # 'Select' from original ASP.NET
            queryset = queryset.filter(company__comp_id=company_id)
        return queryset

```

#### 4.4 Templates

**`financial_years/financialyear/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Financial Years</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'financialyear_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Financial Year
        </button>
    </div>

    <!-- Company Filter Dropdown -->
    <div class="mb-4">
        <label for="id_company_filter" class="block text-sm font-medium text-gray-700">Filter by Company:</label>
        <select 
            id="id_company_filter" 
            name="company_filter" 
            class="block w-full md:w-1/3 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            hx-get="{% url 'financialyear_table' %}"
            hx-target="#financialyearTable-container"
            hx-trigger="change"
            hx-indicator="#table-loading-indicator"
            hx-vals='js:{company_id: document.getElementById("id_company_filter").value}'>
            <option value="Select">-- Select Company --</option>
            {% for company_obj in form.company.field.queryset %}
                <option value="{{ company_obj.comp_id }}">{{ company_obj.comp_name }}</option>
            {% endfor %}
        </select>
        <div id="table-loading-indicator" class="htmx-indicator mt-2">
            <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div> Loading...
        </div>
    </div>
    
    <div id="financialyearTable-container"
         hx-trigger="load, refreshFinancialYearList from:body"
         hx-get="{% url 'financialyear_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading financial years...</p>
        </div>
    </div>
    
    <!-- Modal for form (Add/Edit) and Delete Confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.8/css/dataTables.dataTables.min.css">
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states.
        // For simple modal show/hide, _ macro with HTMX is sufficient.
    });

    // Event listener for HTMX afterSwap to re-initialize DataTables
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.target.id === 'financialyearTable-container') {
            $('#financialyearTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true // Destroy existing DataTable instance before re-initialization
            });
        }
    });

    // Close modal on escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            document.getElementById('modal').classList.remove('is-active');
        }
    });
</script>
{% endblock %}
```

**`financial_years/financialyear/_financialyear_table.html`**

```html
<table id="financialyearTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Financial Year (Display)</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date From</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date To</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in financial_years %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.company.comp_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.get_financial_year_display }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.date_from|date:"d-m-Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.date_to|date:"d-m-Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'financialyear_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'financialyear_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-4 px-4 text-center text-gray-500">No financial years found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// DataTables initialization handled in parent list.html's htmx:afterSwap listener.
// This script block is technically redundant if parent handles it, but included for completeness.
// If this partial was loaded directly (not by hx-target from parent), it would be needed here.
// For this setup, the parent's event listener is more robust.
</script>
```

**`financial_years/financialyear/form.html`**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Financial Year</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" 
          hx-on::after-request="if(event.detail.xhr.status === 204) document.getElementById('modal').classList.remove('is-active');">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors|join:", " }}</p>
                {% endif %}
            </div>
            {% endfor %}
            {% if form.non_field_errors %}
            <div class="text-red-500 text-xs mt-1">
                {% for error in form.non_field_errors %}
                    <p>{{ error }}</p>
                {% endfor %}
            </div>
            {% endif %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`financial_years/financialyear/confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the financial year for **{{ object.company.comp_name }}** from **{{ object.date_from|date:"d-m-Y" }}** to **{{ object.date_to|date:"d-m-Y" }}**?</p>
    
    <form hx-post="{% url 'financialyear_delete' object.pk %}" hx-swap="none"
          hx-on::after-request="if(event.detail.xhr.status === 204) document.getElementById('modal').classList.remove('is-active');">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`financial_years/urls.py`)

```python
from django.urls import path
from .views import (
    FinancialYearListView, 
    FinancialYearCreateView, 
    FinancialYearUpdateView, 
    FinancialYearDeleteView,
    FinancialYearTablePartialView,
)

urlpatterns = [
    path('financial-years/', FinancialYearListView.as_view(), name='financialyear_list'),
    path('financial-years/add/', FinancialYearCreateView.as_view(), name='financialyear_add'),
    path('financial-years/edit/<int:pk>/', FinancialYearUpdateView.as_view(), name='financialyear_edit'),
    path('financial-years/delete/<int:pk>/', FinancialYearDeleteView.as_view(), name='financialyear_delete'),
    path('financial-years/table/', FinancialYearTablePartialView.as_view(), name='financialyear_table'), # HTMX endpoint for table updates
]
```

#### 4.6 Tests (`financial_years/tests.py`)

```python       
from django.test import TestCase, Client
from django.urls import reverse
from django.core.exceptions import ValidationError
from datetime import date
from .models import Company, FinancialYear

class CompanyModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a test company for related objects
        Company.objects.create(comp_id=1, comp_name='Test Company One')
        Company.objects.create(comp_id=2, comp_name='Another Company')

    def test_company_creation(self):
        company = Company.objects.get(comp_id=1)
        self.assertEqual(company.comp_name, 'Test Company One')
        self.assertEqual(str(company), 'Test Company One')
    
    def test_company_pk_is_comp_id(self):
        company = Company.objects.get(comp_id=1)
        self.assertEqual(company.pk, 1)

class FinancialYearModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.company1 = Company.objects.create(comp_id=1, comp_name='Test Company One')
        cls.company2 = Company.objects.create(comp_id=2, comp_name='Another Company')
        
        # Create initial financial year for testing overlap
        FinancialYear.objects.create(
            company=cls.company1,
            date_from=date(2023, 4, 1),
            date_to=date(2024, 3, 31)
        )
        FinancialYear.objects.create(
            company=cls.company2,
            date_from=date(2023, 1, 1),
            date_to=date(2023, 12, 31)
        )
  
    def test_financial_year_creation(self):
        obj = FinancialYear.objects.get(company=self.company1, date_from=date(2023, 4, 1))
        self.assertEqual(obj.company, self.company1)
        self.assertEqual(obj.date_from, date(2023, 4, 1))
        self.assertEqual(obj.date_to, date(2024, 3, 31))
        self.assertEqual(obj.get_financial_year_display(), '20232024')
        self.assertIn('Test Company One', str(obj))

    def test_date_from_before_date_to_validation(self):
        # Test case where date_from is not before date_to
        invalid_year = FinancialYear(
            company=self.company1,
            date_from=date(2024, 3, 31),
            date_to=date(2023, 4, 1)
        )
        with self.assertRaises(ValidationError) as cm:
            invalid_year.clean()
        self.assertIn("Financial 'Date From' must be before 'Date To'.", cm.exception.messages)
        
        invalid_year_same_day = FinancialYear(
            company=self.company1,
            date_from=date(2023, 4, 1),
            date_to=date(2023, 4, 1)
        )
        with self.assertRaises(ValidationError) as cm:
            invalid_year_same_day.clean()
        self.assertIn("Financial 'Date From' must be before 'Date To'.", cm.exception.messages)

    def test_financial_year_overlap_validation(self):
        # Test case for overlapping financial year
        overlapping_year = FinancialYear(
            company=self.company1,
            date_from=date(2023, 10, 1), # Overlaps with 2023-04-01 to 2024-03-31
            date_to=date(2024, 9, 30)
        )
        with self.assertRaises(ValidationError) as cm:
            overlapping_year.clean()
        self.assertIn("The proposed financial year overlaps with an existing financial year for this company.", cm.exception.messages)

    def test_financial_year_no_overlap(self):
        # Test case for non-overlapping financial year
        non_overlapping_year = FinancialYear(
            company=self.company1,
            date_from=date(2024, 4, 1),
            date_to=date(2025, 3, 31)
        )
        try:
            non_overlapping_year.clean() # Should not raise ValidationError
        except ValidationError as e:
            self.fail(f"ValidationError raised unexpectedly: {e.messages}")

    def test_financial_year_update_no_self_overlap(self):
        # Ensure updating an existing object doesn't trigger self-overlap error
        existing_year = FinancialYear.objects.get(company=self.company2, date_from=date(2023, 1, 1))
        existing_year.date_to = date(2023, 12, 30) # A slight change
        try:
            existing_year.clean()
        except ValidationError as e:
            self.fail(f"ValidationError raised on update unexpectedly: {e.messages}")


class FinancialYearViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company1 = Company.objects.create(comp_id=1, comp_name='View Test Company')
        cls.company2 = Company.objects.create(comp_id=2, comp_name='Another View Company')
        
        FinancialYear.objects.create(
            company=cls.company1,
            date_from=date(2023, 4, 1),
            date_to=date(2024, 3, 31)
        )
        FinancialYear.objects.create(
            company=cls.company1,
            date_from=date(2022, 4, 1),
            date_to=date(2023, 3, 31)
        )
        FinancialYear.objects.create(
            company=cls.company2,
            date_from=date(2024, 1, 1),
            date_to=date(2024, 12, 31)
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view(self):
        response = self.client.get(reverse('financialyear_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'financial_years/financialyear/list.html')
        self.assertTrue('financial_years' in response.context)
        self.assertEqual(len(response.context['financial_years']), 3) # All initial objects
        self.assertContains(response, 'Add New Financial Year') # Check for button text

    def test_financial_year_table_partial_view_no_filter(self):
        response = self.client.get(reverse('financialyear_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'financial_years/financialyear/_financialyear_table.html')
        self.assertTrue('financial_years' in response.context)
        self.assertEqual(len(response.context['financial_years']), 3)
        self.assertContains(response, 'View Test Company')
        self.assertContains(response, 'Another View Company')

    def test_financial_year_table_partial_view_filtered_by_company(self):
        response = self.client.get(reverse('financialyear_table'), {'company_id': self.company1.comp_id})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'financial_years/financialyear/_financialyear_table.html')
        self.assertTrue('financial_years' in response.context)
        self.assertEqual(len(response.context['financial_years']), 2) # Should only show for company1
        self.assertContains(response, 'View Test Company')
        self.assertNotContains(response, 'Another View Company')

    def test_create_view_get(self):
        response = self.client.get(reverse('financialyear_add'), HTTP_HX_REQUEST='true') # Simulate HTMX request
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'financial_years/financialyear/form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Financial Year')
        
    def test_create_view_post_valid(self):
        data = {
            'company': self.company1.comp_id,
            'date_from': '2025-04-01',
            'date_to': '2026-03-31',
        }
        response = self.client.post(reverse('financialyear_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertTrue(FinancialYear.objects.filter(company=self.company1, date_from=date(2025, 4, 1)).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshFinancialYearList')

    def test_create_view_post_invalid_dates(self):
        data = {
            'company': self.company1.comp_id,
            'date_from': '2026-03-31',
            'date_to': '2025-04-01', # Invalid range
        }
        response = self.client.post(reverse('financialyear_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'financial_years/financialyear/form.html')
        self.assertContains(response, "Financial &#39;Date From&#39; must be before &#39;Date To&#39;.")
        self.assertFalse(FinancialYear.objects.filter(company=self.company1, date_from=date(2026, 3, 31)).exists())

    def test_create_view_post_overlapping_dates(self):
        data = {
            'company': self.company1.comp_id,
            'date_from': '2023-05-01', # Overlaps with existing 2023-04-01 to 2024-03-31
            'date_to': '2024-05-01',
        }
        response = self.client.post(reverse('financialyear_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'financial_years/financialyear/form.html')
        self.assertContains(response, "The proposed financial year overlaps with an existing financial year for this company.")
        self.assertFalse(FinancialYear.objects.filter(company=self.company1, date_from=date(2023, 5, 1)).exists())

    def test_update_view_get(self):
        obj = FinancialYear.objects.get(company=self.company1, date_from=date(2023, 4, 1))
        response = self.client.get(reverse('financialyear_edit', args=[obj.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'financial_years/financialyear/form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Edit Financial Year')
        self.assertContains(response, 'value="2023-04-01"')
        
    def test_update_view_post_valid(self):
        obj = FinancialYear.objects.get(company=self.company1, date_from=date(2023, 4, 1))
        data = {
            'company': self.company1.comp_id,
            'date_from': '2023-04-01', # Same date_from
            'date_to': '2024-03-30', # Slightly changed date_to
        }
        response = self.client.post(reverse('financialyear_edit', args=[obj.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        obj.refresh_from_db()
        self.assertEqual(obj.date_to, date(2024, 3, 30))
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshFinancialYearList')

    def test_update_view_post_invalid(self):
        obj = FinancialYear.objects.get(company=self.company1, date_from=date(2023, 4, 1))
        data = {
            'company': self.company1.comp_id,
            'date_from': '2024-03-31', # Invalid: date_from not before date_to (same as existing date_to)
            'date_to': '2024-03-31',
        }
        response = self.client.post(reverse('financialyear_edit', args=[obj.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'financial_years/financialyear/form.html')
        self.assertContains(response, "Financial &#39;Date From&#39; must be before &#39;Date To&#39;.")

    def test_delete_view_get(self):
        obj = FinancialYear.objects.get(company=self.company2, date_from=date(2024, 1, 1))
        response = self.client.get(reverse('financialyear_delete', args=[obj.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'financial_years/financialyear/confirm_delete.html')
        self.assertContains(response, 'Confirm Delete')
        self.assertContains(response, obj.company.comp_name)

    def test_delete_view_post(self):
        obj_to_delete = FinancialYear.objects.create(
            company=self.company1,
            date_from=date(2026, 1, 1),
            date_to=date(2026, 12, 31)
        )
        response = self.client.post(reverse('financialyear_delete', args=[obj_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(FinancialYear.objects.filter(pk=obj_to_delete.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshFinancialYearList')

```

### Step 5: HTMX and Alpine.js Integration

*   **Modal Forms:** HTMX is used to fetch the add/edit/delete forms into a modal (`#modalContent`) upon button click. Alpine.js is used via the `_` macro (part of htmx.org/extensions/alpine-js/) to manage the `hidden` class of the modal, showing it on `click` and hiding it when the form is submitted successfully (handled by HTMX 204 response and `hx-on::after-request`).
*   **Dynamic List Updates:** The `financialyearTable-container` div uses `hx-trigger="load, refreshFinancialYearList from:body"` and `hx-get="{% url 'financialyear_table' %}"` to load the table content initially and refresh it after any CRUD operation. The `refreshFinancialYearList` custom event is sent by the Django views via the `HX-Trigger` header.
*   **Company Filter:** The `select` element for company filtering uses `hx-get` to `financialyear_table` and `hx-trigger="change"` to re-fetch the table content filtered by the selected company, mimicking the ASP.NET `AutoPostBack` functionality without full page reloads.
*   **DataTables:** The `_financialyear_table.html` partial uses a standard `<table>` tag which is then initialized as a DataTables instance in the `list.html`'s `htmx:afterSwap` event listener. This ensures DataTables is correctly re-initialized every time the table content is updated by HTMX.
*   **No Custom JavaScript:** All dynamic interactions are managed by HTMX attributes and simple Alpine.js `_` macros, avoiding the need for complex custom JavaScript files.

---

### Final Notes

This comprehensive plan provides a robust, modern, and maintainable Django application for financial year management. By replacing legacy ASP.NET Web Forms components with Django's MVT pattern, HTMX, Alpine.js, and DataTables, your organization benefits from:

*   **Improved Performance:** HTMX reduces server load and bandwidth by performing partial page updates instead of full page reloads.
*   **Enhanced User Experience:** Seamless, dynamic interactions provide a smoother, more responsive user interface.
*   **Simplified Frontend Development:** Minimal JavaScript required, reducing complexity and increasing developer productivity.
*   **Increased Maintainability:** Clear separation of concerns (Fat Models, Thin Views), modern framework features, and comprehensive tests make the codebase easier to understand, debug, and extend.
*   **Scalability:** Django's robust architecture supports growth and increased user loads.
*   **Automation-Ready:** The structured nature of this solution, with clear file separation and adherence to best practices, makes it highly amenable to further AI-assisted refactoring and component generation.

This systematic conversion reduces manual effort by providing a clear blueprint for automated code generation and minimizes human error through consistent patterns and thorough testing.