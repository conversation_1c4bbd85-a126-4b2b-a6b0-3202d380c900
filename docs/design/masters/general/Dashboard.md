## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

The provided ASP.NET `.aspx` and C# code-behind files for `Dashboard` are largely empty, serving primarily as a placeholder within the ASP.NET MasterPage structure with an empty `Page_Load` event. This means there is no explicit database interaction, UI controls (like GridViews, TextBoxes, or Buttons), or business logic directly present in the provided snippet to infer a concrete data model or specific CRUD operations.

In a real-world scenario, our AI-assisted automation would analyze the MasterPage structure, any associated `SqlDataSource` controls, `GridView` definitions, or explicit database calls within event handlers to determine the underlying data. Since the provided code is a barebones example, we will proceed by defining a generic `DashboardItem` module, demonstrating how a typical ASP.NET page, even if currently empty, would be modernized into a Django component following our guidelines. This `DashboardItem` will include standard CRUD operations and exemplify the use of HTMX, Alpine.js, and DataTables.

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Since the provided ASP.NET code is empty and lacks any database-related elements (like `SqlDataSource`, `GridView` bindings, or explicit SQL commands), we cannot extract a concrete table schema.

For demonstration purposes, we will assume a common scenario where a Dashboard page might display a list of "dashboard items" or "widgets". We will infer a hypothetical database table named `tbl_dashboard_item` with common fields.

**Inferred Schema:**
*   **Table Name:** `tbl_dashboard_item`
*   **Columns:**
    *   `id` (Primary Key, Auto-increment)
    *   `item_name` (e.g., `VARCHAR(255)`)
    *   `item_description` (e.g., `TEXT`)
    *   `is_active` (e.g., `BIT`/`BOOLEAN`)
    *   `created_at` (e.g., `DATETIME`)
    *   `updated_at` (e.g., `DATETIME`)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

Given the empty ASP.NET code, no specific CRUD operations are identifiable. However, a "Dashboard" often implies the ability to view, and potentially manage, its constituent items. We will assume the full suite of CRUD operations (Create, Read, Update, Delete) would be required for managing `DashboardItem` entities.

*   **Create:** Adding new dashboard items.
*   **Read:** Displaying a list of all dashboard items.
*   **Update:** Modifying existing dashboard items.
*   **Delete:** Removing dashboard items.
*   **Validation Logic:** Simple required field validation for `item_name` and `item_description`.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

With no UI controls in the provided ASP.NET code, we infer a standard web interface pattern for managing a list of entities:

*   **List View:** A table (analogous to `GridView`) to display all `DashboardItem` records, with client-side searching, sorting, and pagination (using DataTables).
*   **Action Buttons:** "Add New Item" button to open a form for creation. "Edit" and "Delete" buttons for each row in the table to open forms for updating or confirming deletion.
*   **Form Controls:** Text inputs (analogous to `TextBox`) for `item_name` and `item_description`, a checkbox for `is_active`.
*   **Interaction:** All forms will be loaded into a modal and handled via HTMX, and the list will refresh dynamically using HTMX after any CRUD operation. Alpine.js will manage the modal's visibility.

## Step 4: Generate Django Code

We will create a new Django application named `dashboard` for this module.

### 4.1 Models

**File: `dashboard/models.py`**

```python
from django.db import models
from django.utils import timezone

class DashboardItem(models.Model):
    """
    Represents an item or widget displayed on a dashboard.
    Mapped to an existing legacy database table 'tbl_dashboard_item'.
    """
    item_name = models.CharField(
        max_length=255, 
        db_column='item_name',
        verbose_name='Name',
        help_text='The name of the dashboard item.'
    )
    item_description = models.TextField(
        db_column='item_description',
        blank=True, 
        null=True,
        verbose_name='Description',
        help_text='A detailed description of the dashboard item.'
    )
    is_active = models.BooleanField(
        db_column='is_active',
        default=True,
        verbose_name='Active',
        help_text='Indicates if the item is currently active on the dashboard.'
    )
    created_at = models.DateTimeField(
        db_column='created_at',
        auto_now_add=True,
        verbose_name='Created At'
    )
    updated_at = models.DateTimeField(
        db_column='updated_at',
        auto_now=True,
        verbose_name='Updated At'
    )

    class Meta:
        managed = False  # Set to False as the table already exists in the legacy DB
        db_table = 'tbl_dashboard_item'
        verbose_name = 'Dashboard Item'
        verbose_name_plural = 'Dashboard Items'
        ordering = ['item_name']

    def __str__(self):
        """Returns the name of the dashboard item."""
        return self.item_name

    def get_summary(self, max_length=50):
        """
        Business logic: Returns a truncated description for display.
        This demonstrates a 'fat model' method.
        """
        if self.item_description:
            return (self.item_description[:max_length] + '...') if len(self.item_description) > max_length else self.item_description
        return "No description."

    def activate(self):
        """Business logic: Activates the dashboard item."""
        if not self.is_active:
            self.is_active = True
            self.save()
            return True
        return False

    def deactivate(self):
        """Business logic: Deactivates the dashboard item."""
        if self.is_active:
            self.is_active = False
            self.save()
            return True
        return False
```

### 4.2 Forms

**File: `dashboard/forms.py`**

```python
from django import forms
from .models import DashboardItem

class DashboardItemForm(forms.ModelForm):
    """
    Form for creating and updating DashboardItem instances.
    """
    class Meta:
        model = DashboardItem
        fields = ['item_name', 'item_description', 'is_active']
        widgets = {
            'item_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'item_description': forms.Textarea(attrs={'rows': 3, 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded'}),
        }
        labels = {
            'item_name': 'Item Name',
            'item_description': 'Description',
            'is_active': 'Is Active?',
        }

    def clean_item_name(self):
        """
        Custom validation for item_name to ensure it's not just whitespace.
        """
        item_name = self.cleaned_data['item_name'].strip()
        if not item_name:
            raise forms.ValidationError("Item Name cannot be empty.")
        return item_name
```

### 4.3 Views

**File: `dashboard/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import DashboardItem
from .forms import DashboardItemForm

class DashboardItemListView(ListView):
    """
    Displays a list of all Dashboard Items.
    This view is responsible for the main page that loads the HTMX table.
    """
    model = DashboardItem
    template_name = 'dashboard/dashboarditem/list.html'
    context_object_name = 'dashboard_items' # Renamed for clarity in template

class DashboardItemTablePartialView(ListView):
    """
    Returns the HTML for the Dashboard Item table, intended to be loaded via HTMX.
    """
    model = DashboardItem
    template_name = 'dashboard/dashboarditem/_dashboarditem_table.html'
    context_object_name = 'dashboard_items'

    def get_queryset(self):
        # Example: Add filtering based on request.GET if needed for DataTables server-side processing
        return super().get_queryset()

class DashboardItemCreateView(CreateView):
    """
    Handles creation of new Dashboard Items, supporting HTMX for modal forms.
    """
    model = DashboardItem
    form_class = DashboardItemForm
    template_name = 'dashboard/dashboarditem/_dashboarditem_form.html' # Partial template for HTMX
    success_url = reverse_lazy('dashboarditem_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Dashboard Item added successfully.')
        # HTMX-specific response: return 204 No Content and trigger refresh
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardItemList'
                }
            )
        return response

    def form_invalid(self, form):
        # For HTMX, re-render the form with errors
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)

class DashboardItemUpdateView(UpdateView):
    """
    Handles updating existing Dashboard Items, supporting HTMX for modal forms.
    """
    model = DashboardItem
    form_class = DashboardItemForm
    template_name = 'dashboard/dashboarditem/_dashboarditem_form.html' # Partial template for HTMX
    success_url = reverse_lazy('dashboarditem_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Dashboard Item updated successfully.')
        # HTMX-specific response: return 204 No Content and trigger refresh
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardItemList'
                }
            )
        return response

    def form_invalid(self, form):
        # For HTMX, re-render the form with errors
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)

class DashboardItemDeleteView(DeleteView):
    """
    Handles deletion of Dashboard Items, supporting HTMX for modal confirmations.
    """
    model = DashboardItem
    template_name = 'dashboard/dashboarditem/confirm_delete.html' # Partial template for HTMX
    success_url = reverse_lazy('dashboarditem_list') # Fallback for non-HTMX requests

    def delete(self, request, *args, **kwargs):
        """
        Overrides delete to add success message and HTMX trigger.
        """
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Dashboard Item deleted successfully.')
        # HTMX-specific response: return 204 No Content and trigger refresh
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardItemList'
                }
            )
        return response
```

### 4.4 Templates

**Directory: `dashboard/templates/dashboard/dashboarditem/`**

**File: `list.html`**

```html
{% extends 'core/base.html' %}

{% block title %}Dashboard Items{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Dashboard Items Management</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
            hx-get="{% url 'dashboarditem_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Dashboard Item
        </button>
    </div>

    <!-- Container for the DataTables partial, dynamically loaded via HTMX -->
    <div id="dashboarditemTable-container"
         hx-trigger="load, refreshDashboardItemList from:body"
         hx-get="{% url 'dashboarditem_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg overflow-hidden">
        <!-- Initial loading indicator -->
        <div class="p-6 text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Dashboard Items...</p>
        </div>
    </div>

    <!-- Modal for CRUD operations -->
    <div id="modal" class="fixed inset-0 z-50 bg-gray-600 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 my-8 transform transition-all sm:my-0 sm:max-w-md md:max-w-lg">
            <!-- Content loaded here by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is included in base.html. No specific Alpine component needed here
    // beyond the "_on click" for modal.
</script>
{% endblock %}
```

**File: `_dashboarditem_table.html`** (Partial for HTMX)

```html
<table id="dashboardItemTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Active</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in dashboard_items %}
        <tr>
            <td class="py-3 px-4 whitespace-nowrap">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-gray-900">{{ obj.item_name }}</td>
            <td class="py-3 px-4 text-gray-600">{{ obj.get_summary }}</td> {# Using fat model method #}
            <td class="py-3 px-4 whitespace-nowrap text-gray-900">
                {% if obj.is_active %}
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                {% else %}
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Inactive</span>
                {% endif %}
            </td>
            <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                <button
                    class="bg-yellow-500 hover:bg-yellow-600 text-white py-1 px-3 rounded-md mr-2 text-xs transition duration-150 ease-in-out"
                    hx-get="{% url 'dashboarditem_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-600 text-white py-1 px-3 rounded-md text-xs transition duration-150 ease-in-out"
                    hx-get="{% url 'dashboarditem_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="5" class="py-3 px-4 text-center text-gray-500">No dashboard items found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// Initialize DataTables
// Ensure jQuery and DataTables CDN are loaded in base.html
$(document).ready(function() {
    $('#dashboardItemTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[5, 10, 25, 50, -1], [5, 10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 4] } // Disable sorting for SN and Actions columns
        ]
    });
});
</script>
```

**File: `_dashboarditem_form.html`** (Partial for HTMX)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Dashboard Item</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-indicator">
        {% csrf_token %}

        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-600 text-sm mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="inline-flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                Save Item
            </button>
        </div>
        <div id="form-indicator" class="htmx-indicator mt-4 text-center text-blue-600">
            Saving...
        </div>
    </form>
</div>
```

**File: `confirm_delete.html`** (Partial for HTMX)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the dashboard item: <strong>{{ object.item_name }}</strong>?
        This action cannot be undone.
    </p>

    <form hx-post="{% url 'dashboarditem_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="inline-flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-150 ease-in-out">
                Delete Item
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs

**File: `dashboard/urls.py`**

```python
from django.urls import path
from .views import (
    DashboardItemListView,
    DashboardItemCreateView,
    DashboardItemUpdateView,
    DashboardItemDeleteView,
    DashboardItemTablePartialView
)

urlpatterns = [
    # Main list view (renders the container for the HTMX table)
    path('dashboard-items/', DashboardItemListView.as_view(), name='dashboarditem_list'),

    # HTMX endpoint for the table partial
    path('dashboard-items/table/', DashboardItemTablePartialView.as_view(), name='dashboarditem_table'),

    # HTMX endpoint for creating a new item (loads form into modal)
    path('dashboard-items/add/', DashboardItemCreateView.as_view(), name='dashboarditem_add'),

    # HTMX endpoint for editing an item (loads form into modal)
    path('dashboard-items/edit/<int:pk>/', DashboardItemUpdateView.as_view(), name='dashboarditem_edit'),

    # HTMX endpoint for deleting an item (loads confirmation into modal)
    path('dashboard-items/delete/<int:pk>/', DashboardItemDeleteView.as_view(), name='dashboarditem_delete'),
]
```

### 4.6 Tests

**File: `dashboard/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import DashboardItem
from .forms import DashboardItemForm
from django.contrib.messages import get_messages

class DashboardItemModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        """
        Set up non-modified objects used by all test methods.
        """
        cls.item1 = DashboardItem.objects.create(
            item_name='Test Item 1',
            item_description='Description for test item 1.',
            is_active=True
        )
        cls.item2 = DashboardItem.objects.create(
            item_name='Test Item 2',
            item_description='Short desc.',
            is_active=False
        )

    def test_dashboard_item_creation(self):
        """Test that a DashboardItem can be created and its fields are correct."""
        item = DashboardItem.objects.get(item_name='Test Item 1')
        self.assertEqual(item.item_name, 'Test Item 1')
        self.assertEqual(item.item_description, 'Description for test item 1.')
        self.assertTrue(item.is_active)
        self.assertIsNotNone(item.created_at)
        self.assertIsNotNone(item.updated_at)

    def test_string_representation(self):
        """Test the __str__ method returns the item name."""
        item = DashboardItem.objects.get(item_name='Test Item 1')
        self.assertEqual(str(item), 'Test Item 1')

    def test_verbose_name_plural(self):
        """Test verbose_name_plural is set correctly."""
        self.assertEqual(DashboardItem._meta.verbose_name_plural, 'Dashboard Items')

    def test_get_summary_method(self):
        """Test the get_summary fat model method."""
        item1 = DashboardItem.objects.get(item_name='Test Item 1')
        self.assertEqual(item1.get_summary(max_length=15), 'Description for...') # Truncated
        item2 = DashboardItem.objects.get(item_name='Test Item 2')
        self.assertEqual(item2.get_summary(), 'Short desc.') # Not truncated
        item_no_desc = DashboardItem.objects.create(item_name='No Description')
        self.assertEqual(item_no_desc.get_summary(), 'No description.')

    def test_activate_deactivate_methods(self):
        """Test activate and deactivate methods."""
        item = DashboardItem.objects.get(item_name='Test Item 2') # Initially inactive
        self.assertFalse(item.is_active)

        # Activate
        self.assertTrue(item.activate())
        item.refresh_from_db()
        self.assertTrue(item.is_active)
        self.assertFalse(item.activate()) # Should return False if already active

        # Deactivate
        self.assertTrue(item.deactivate())
        item.refresh_from_db()
        self.assertFalse(item.is_active)
        self.assertFalse(item.deactivate()) # Should return False if already inactive

class DashboardItemFormTest(TestCase):
    def test_form_valid_data(self):
        form = DashboardItemForm(data={
            'item_name': 'New Form Item',
            'item_description': 'A description.',
            'is_active': True
        })
        self.assertTrue(form.is_valid())

    def test_form_no_item_name(self):
        form = DashboardItemForm(data={
            'item_name': '',
            'item_description': 'A description.',
            'is_active': True
        })
        self.assertFalse(form.is_valid())
        self.assertIn('Item Name cannot be empty.', form.errors['item_name'])

    def test_form_whitespace_item_name(self):
        form = DashboardItemForm(data={
            'item_name': '   ',
            'item_description': 'A description.',
            'is_active': True
        })
        self.assertFalse(form.is_valid())
        self.assertIn('Item Name cannot be empty.', form.errors['item_name'])

class DashboardItemViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        """
        Create a test DashboardItem for use in multiple tests.
        """
        cls.item = DashboardItem.objects.create(
            item_name='Initial Item',
            item_description='This is an initial item for testing views.',
            is_active=True
        )

    def setUp(self):
        """Set up client for each test."""
        self.client = Client()

    def test_list_view_get(self):
        """Test that the list view renders correctly."""
        response = self.client.get(reverse('dashboarditem_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard/dashboarditem/list.html')
        # The main list view doesn't pass objects directly, but expects the partial to be loaded
        self.assertFalse('dashboard_items' in response.context)

    def test_table_partial_view_get(self):
        """Test that the HTMX table partial renders correctly with data."""
        response = self.client.get(reverse('dashboarditem_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard/dashboarditem/_dashboarditem_table.html')
        self.assertTrue('dashboard_items' in response.context)
        self.assertContains(response, self.item.item_name)
        self.assertContains(response, 'data-dt-idx') # Check for DataTables specific elements

    def test_create_view_get(self):
        """Test GET request to the create view (modal form)."""
        response = self.client.get(reverse('dashboarditem_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard/dashboarditem/_dashboarditem_form.html')
        self.assertIsInstance(response.context['form'], DashboardItemForm)

    def test_create_view_post_success(self):
        """Test POST request to create a new item."""
        data = {
            'item_name': 'New Item',
            'item_description': 'A brand new item.',
            'is_active': False
        }
        response = self.client.post(reverse('dashboarditem_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success, No Content
        self.assertTrue(DashboardItem.objects.filter(item_name='New Item').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshDashboardItemList', response.headers['HX-Trigger'])

        # Check messages (will be attached to next request, not this HX-response)
        messages = list(get_messages(self.client.session))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Dashboard Item added successfully.')

    def test_create_view_post_invalid(self):
        """Test POST request with invalid data."""
        data = {
            'item_name': ' ', # Invalid name
            'item_description': 'Invalid item description.',
            'is_active': True
        }
        response = self.client.post(reverse('dashboarditem_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Renders form with errors
        self.assertTemplateUsed(response, 'dashboard/dashboarditem/_dashboarditem_form.html')
        self.assertContains(response, 'Item Name cannot be empty.')
        self.assertFalse(DashboardItem.objects.filter(item_name=' ').exists())

    def test_update_view_get(self):
        """Test GET request to the update view."""
        response = self.client.get(reverse('dashboarditem_edit', args=[self.item.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard/dashboarditem/_dashboarditem_form.html')
        self.assertIsInstance(response.context['form'], DashboardItemForm)
        self.assertEqual(response.context['form'].instance, self.item)

    def test_update_view_post_success(self):
        """Test POST request to update an existing item."""
        new_name = 'Updated Item Name'
        data = {
            'item_name': new_name,
            'item_description': self.item.item_description,
            'is_active': self.item.is_active
        }
        response = self.client.post(reverse('dashboarditem_edit', args=[self.item.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success, No Content
        self.item.refresh_from_db()
        self.assertEqual(self.item.item_name, new_name)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshDashboardItemList', response.headers['HX-Trigger'])

    def test_delete_view_get(self):
        """Test GET request to the delete confirmation view."""
        response = self.client.get(reverse('dashboarditem_delete', args=[self.item.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard/dashboarditem/confirm_delete.html')
        self.assertEqual(response.context['object'], self.item)
        self.assertContains(response, f'Are you sure you want to delete the dashboard item: <strong>{self.item.item_name}</strong>?')

    def test_delete_view_post_success(self):
        """Test POST request to delete an item."""
        # Create an item specifically for deletion to avoid impacting other tests
        item_to_delete = DashboardItem.objects.create(item_name='Item To Delete', item_description='Will be deleted', is_active=True)
        self.assertTrue(DashboardItem.objects.filter(pk=item_to_delete.pk).exists())

        response = self.client.post(reverse('dashboarditem_delete', args=[item_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success, No Content
        self.assertFalse(DashboardItem.objects.filter(pk=item_to_delete.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshDashboardItemList', response.headers['HX-Trigger'])

        # Check messages
        messages = list(get_messages(self.client.session))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Dashboard Item deleted successfully.')
```

## Step 5: HTMX and Alpine.js Integration

The migration strategy heavily relies on HTMX and Alpine.js to deliver a modern, dynamic user experience without writing complex JavaScript.

*   **HTMX for Dynamic Content Loading:**
    *   The main `list.html` page uses `hx-get` to load the `_dashboarditem_table.html` partial. This is triggered on `load` and by a custom event `refreshDashboardItemList`.
    *   All CRUD (Create, Update, Delete) forms and confirmation dialogs are loaded into a modal using `hx-get` attributes on buttons, targeting `#modalContent`.
    *   Form submissions (`hx-post`) are configured with `hx-swap="none"` and `hx-trigger="refreshDashboardItemList"` to ensure the backend view returns a 204 No Content response, triggering the list to refresh.
    *   Error handling for forms (`form_invalid` in views) re-renders the form with errors, allowing HTMX to swap it back into the modal, displaying validation messages.

*   **Alpine.js for UI State Management (Modals):**
    *   The modal (`#modal`) visibility is controlled using Alpine.js's `x-data` and `x-show` attributes, though in this specific setup, we're using the simpler `_` attribute syntax (Hyperscript) directly with HTMX/Alpine for `add .is-active to #modal` and `remove .is-active from me` to toggle a CSS class. This aligns with the "no additional JavaScript" preference.
    *   The `on click if event.target.id == 'modal' remove .is-active from me` on the modal background allows closing it by clicking outside the content.

*   **DataTables for List Views:**
    *   The `_dashboarditem_table.html` partial includes a `<table>` with the ID `dashboardItemTable`.
    *   A `<script>` block within this partial initializes DataTables on this table (`$('#dashboardItemTable').DataTable()`). This ensures that each time the table partial is reloaded via HTMX, DataTables is re-applied, providing client-side searching, sorting, and pagination without full page refreshes. jQuery is assumed to be loaded via `core/base.html` for DataTables to function.

*   **No Additional JavaScript:**
    *   The entire frontend interaction is managed by HTMX and Alpine.js (via Hyperscript). There are no custom `*.js` files for this module beyond the DataTables initialization, which is a standard library.

## Final Notes

*   **Placeholder Replacement:** In a real AI-assisted migration, the placeholders like `[TABLE_NAME]`, `[FIELD1]`, etc., would be automatically populated based on the in-depth analysis of the ASP.NET codebase and database schema.
*   **DRY Principles:**
    *   Template inheritance (`{% extends 'core/base.html' %}`) is strictly adhered to, ensuring a consistent look and feel across the application without duplicating header, footer, or core script includes.
    *   Partial templates (`_dashboarditem_table.html`, `_dashboarditem_form.html`) are used for content that is loaded dynamically by HTMX, promoting reusability and keeping code modular.
*   **Fat Model, Thin View:**
    *   The `DashboardItem` model contains business logic (e.g., `get_summary`, `activate`, `deactivate`), keeping the views concise (typically 5-15 lines of custom logic per method).
    *   Views primarily handle HTTP request/response flow and delegate data manipulation and complex logic to the model or form.
*   **Comprehensive Tests:** The provided `tests.py` ensures good coverage for both model business logic and view interactions, including HTMX-specific responses, contributing to a robust and maintainable application.
*   **Business Value:** This modernized Django solution offers:
    *   **Improved User Experience:** Dynamic interactions (modal forms, instant list refreshes) provide a snappier, more desktop-like feel without traditional page reloads.
    *   **Reduced Development Cost:** HTMX and Alpine.js significantly cut down on the need for complex JavaScript frameworks, speeding up development and reducing frontend complexity.
    *   **Enhanced Maintainability:** Clean separation of concerns (model for logic, view for flow, template for presentation) and comprehensive testing make the codebase easier to understand, debug, and extend.
    *   **Scalability:** Django's robust ORM and architecture provide a solid foundation for future growth and integration.
    *   **Modernization:** Moves away from legacy ASP.NET Web Forms to a contemporary Python-based web framework, leveraging modern web patterns.