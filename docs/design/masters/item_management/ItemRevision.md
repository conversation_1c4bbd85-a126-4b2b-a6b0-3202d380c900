## ASP.NET to Django Conversion Script: Item Revision Type Management

This document outlines the strategic transition of your ASP.NET Item Revision Type management module to a modern Django-based solution. Our approach prioritizes automation, leveraging AI-assisted tools to streamline the migration process, and focuses on delivering business value through a more efficient, scalable, and maintainable application.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the underlying database table and its columns that the ASP.NET code interacts with.

**Instructions:**
From the `SqlDataSource1` component in your ASP.NET code, we've identified the core database elements:
- The module primarily interacts with a table named `tblDG_Revision_Type_Master`.
- It performs standard data operations (creating, reading, updating, deleting) on this table.
- The columns involved are `Id`, `Types`, `SysDate`, `SysTime`, `CompId`, `FinYearId`, and `SessionId`.

**Extracted Schema:**
- **Table Name:** `tblDG_Revision_Type_Master`
- **Columns:**
    - `Id`: Integer, Primary Key (auto-incremented by the database). This is used as the unique identifier for each revision type.
    - `Types`: String, representing the "Type of Revisions". This is the main descriptive field.
    - `SysDate`: String, stores the system date when a record was created or last updated.
    - `SysTime`: String, stores the system time when a record was created or last updated.
    - `CompId`: Integer, representing the Company ID, used for data filtering.
    - `FinYearId`: Integer, representing the Financial Year ID, also used for data filtering.
    - `SessionId`: String, likely storing the username of the user who performed the last action.

### Step 2: Identify Backend Functionality

**Task:** Determine the business operations (Create, Read, Update, Delete) performed by the ASP.NET module.

**Instructions:**
The ASP.NET code implements a comprehensive set of data management functionalities for Item Revision Types:
- **Create (Add New):**
    - New entries are added via an "Insert" button in the `GridView`'s footer row or in the `EmptyDataTemplate`.
    - The `Types` field is mandatory.
    - Audit information (`SysDate`, `SysTime`, `CompId`, `FinYearId`, `SessionId`) is automatically populated during insertion.
- **Read (Display List):**
    - All existing Item Revision Types are displayed in a `GridView`.
    - Data is filtered by `CompId` (Company ID) and `FinYearId` (Financial Year ID, including previous years).
    - Records are ordered by `Id` in descending order (most recent first).
    - The `GridView` supports pagination.
- **Update (Edit Existing):**
    - Existing entries can be edited directly within the `GridView`'s edit mode.
    - Only the `Types` field is user-editable.
    - Audit information (`SysDate`, `SysTime`, `SessionId`) is updated upon saving changes.
- **Delete (Remove Entry):**
    - Entries can be deleted directly from the `GridView`.
    - A client-side confirmation message is triggered before deletion.
- **Validation:**
    - The `Types` field has a "Required Field Validator" to ensure it's not left empty.

### Step 3: Infer UI Components

**Task:** Analyze the ASP.NET controls and their roles to map them to modern Django UI patterns.

**Instructions:**
The ASP.NET `ItemRevision.aspx` page primarily uses a `GridView` control for displaying and managing data:
- **`GridView` (GridView2):** This will be replaced by a modern HTML table enhanced with **DataTables.js** for features like search, sort, and pagination.
- **`TextBox` (txtTypes, lblRevision0):** Used for entering and editing the "Type of Revisions." In Django, these will become standard `<input type="text">` elements styled with Tailwind CSS, rendered via Django forms.
- **`Label` (lblRevision, lblID, lblmsg):** Used for displaying information and messages. These will become standard HTML `<span>` or `<p>` tags.
- **`Button` (btnInsert):** For initiating add operations. This will be a standard HTML `<button>` element with **HTMX** attributes to trigger a modal form.
- **`LinkButton` (Edit/Delete `CommandField`):** For initiating edit and delete actions. These will also be HTML `<button>` elements with **HTMX** attributes to load modal forms or trigger direct delete actions.
- **Client-side JavaScript (`PopUpMsg.js`, confirmation functions):** The existing confirmation dialogs will be replaced by **Alpine.js** driven modals that integrate seamlessly with HTMX for dynamic content and actions.

### Step 4: Generate Django Code

This step outlines the generation of the core Django application components: models, forms, views, templates, and tests. We will use a Django application named `design` for this module.

#### 4.1 Models (`design/models.py`)

**Task:** Create a Django model that accurately maps to the `tblDG_Revision_Type_Master` table, adhering to the "Fat Model" principle by including business logic within the model itself.

**Instructions:**
The `ItemRevisionType` model is defined to directly map to the legacy database table. `managed = False` is critical as Django will not manage the creation, modification, or deletion of this table; it assumes the table already exists. The `db_column` attribute is used to explicitly map Django field names to the legacy column names. A method `populate_audit_fields` is added to encapsulate the logic for setting audit-related data, demonstrating the "Fat Model" approach.

```python
# design/models.py
from django.db import models
from datetime import datetime

class ItemRevisionType(models.Model):
    # This maps to the existing 'Id' primary key in the legacy database.
    # AutoField is suitable for auto-incrementing integer primary keys.
    id = models.AutoField(db_column='Id', primary_key=True)
    
    # Maps to the 'Types' column, storing the revision type name.
    revision_type_name = models.CharField(db_column='Types', max_length=255) # Assuming NVARCHAR(255)

    # Audit fields, directly mapped as CharFields since they are passed as strings in ASP.NET.
    sys_date = models.CharField(db_column='SysDate', max_length=20) 
    sys_time = models.CharField(db_column='SysTime', max_length=20) 
    company_id = models.IntegerField(db_column='CompId')
    financial_year_id = models.IntegerField(db_column='FinYearId')
    session_user = models.CharField(db_column='SessionId', max_length=255) # Maps to Session["username"]

    class Meta:
        # 'managed = False' is crucial; Django will not create or modify this table.
        # It means the table already exists in the database.
        managed = False
        db_table = 'tblDG_Revision_Type_Master'
        verbose_name = 'Item Revision Type'
        verbose_name_plural = 'Item Revision Types'
        # Matches 'ORDER BY [Id] DESC' from the ASP.NET SqlDataSource SelectCommand.
        ordering = ['-id'] 

    def __str__(self):
        """Returns a string representation of the item revision type."""
        return self.revision_type_name

    def populate_audit_fields(self, company_id, financial_year_id, session_user, is_new_record):
        """
        Populates audit fields (sys_date, sys_time, session_user) for both
        new and existing records. For new records, also sets company_id and financial_year_id.
        This encapsulates the logic for populating these common fields,
        adhering to the "Fat Model" principle.
        """
        # Current date and time formatted to match potential legacy system string format.
        current_date = datetime.now().strftime('%Y/%m/%d') 
        current_time = datetime.now().strftime('%H:%M:%S') 

        self.sys_date = current_date
        self.sys_time = current_time
        self.session_user = session_user

        if is_new_record:
            self.company_id = company_id
            self.financial_year_id = financial_year_id
        # For existing records, company_id and financial_year_id are not updated
        # by the original ASP.NET code, so we preserve that behavior.

```

#### 4.2 Forms (`design/forms.py`)

**Task:** Define a Django form for user input, specifically for the `revision_type_name` field.

**Instructions:**
A `ModelForm` is created for `ItemRevisionType`. Only `revision_type_name` is included as it's the sole user-editable field in the ASP.NET `GridView`. Widgets are applied for Tailwind CSS styling. A custom `clean_revision_type_name` method is added for uniqueness validation, replicating and enhancing the original ASP.NET required field validation.

```python
# design/forms.py
from django import forms
from .models import ItemRevisionType

class ItemRevisionTypeForm(forms.ModelForm):
    class Meta:
        model = ItemRevisionType
        # Only 'Types' (mapped to 'revision_type_name') was user-editable in ASP.NET.
        fields = ['revision_type_name'] 
        widgets = {
            'revision_type_name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 
                'required': 'true' # Enforce required field in HTML5
            }),
        }
        
    def clean_revision_type_name(self):
        """
        Custom validation to ensure the revision type name is unique.
        This replicates and enhances the validation logic from the original system.
        """
        revision_type_name = self.cleaned_data.get('revision_type_name')
        # Check for existence of an ItemRevisionType with the same name,
        # excluding the current instance if it's an update operation.
        if ItemRevisionType.objects.filter(revision_type_name=revision_type_name).exclude(pk=self.instance.pk).exists():
            raise forms.ValidationError("This revision type name already exists. Please choose a unique name.")
        return revision_type_name

```

#### 4.3 Views (`design/views.py`)

**Task:** Implement CRUD operations using Django Class-Based Views (CBVs), ensuring thin views and HTMX integration.

**Instructions:**
The views leverage Django's generic `ListView`, `CreateView`, `UpdateView`, and `DeleteView` for concise and standardized CRUD operations. A `TablePartialView` is added specifically for HTMX to fetch and render only the table content, allowing dynamic updates without full page reloads. Audit fields (`company_id`, `financial_year_id`, `session_user`) are populated in the `form_valid` methods by calling the model's `populate_audit_fields` method, demonstrating the "Thin View" principle. HTMX headers and triggers are used for seamless frontend interactions.

```python
# design/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from django.template.loader import render_to_string
from .models import ItemRevisionType
from .forms import ItemRevisionTypeForm
from datetime import datetime 

# Helper function to get context data (e.g., from user session/profile)
# In a real application, this would fetch actual user-specific data.
# For this migration example, we mock it based on typical ASP.NET Session usage.
def get_current_context(request):
    """
    Retrieves current company ID, financial year ID, and session username.
    In a real system, these would come from the authenticated user's profile,
    session, or a multi-tenancy context.
    """
    company_id = request.session.get('compid', 1)  # Default for demonstration
    financial_year_id = request.session.get('finyear', 2024) # Default for demonstration
    session_username = request.session.get('username', 'system_user') # Default for demonstration
    return company_id, financial_year_id, session_username

class ItemRevisionTypeListView(ListView):
    """
    Displays a list of Item Revision Types.
    The primary view for the Item Revision Type management page.
    """
    model = ItemRevisionType
    template_name = 'design/itemrevisiontype/list.html'
    context_object_name = 'item_revision_types' 

    def get_queryset(self):
        """
        Filters the queryset based on company ID and financial year ID,
        mirroring the ASP.NET SqlDataSource's SELECT command filtering.
        """
        company_id, financial_year_id, _ = get_current_context(self.request)
        # ASP.NET used FinYearId <= @FinYearId.
        return ItemRevisionType.objects.filter(
            company_id=company_id, 
            financial_year_id__lte=financial_year_id 
        ).order_by('-id')

class ItemRevisionTypeTablePartialView(ListView):
    """
    Renders only the table content for Item Revision Types.
    Used by HTMX to refresh the table dynamically after CRUD operations.
    """
    model = ItemRevisionType
    template_name = 'design/itemrevisiontype/_itemrevisiontype_table.html'
    context_object_name = 'item_revision_types'

    def get_queryset(self):
        """
        Filters the queryset consistent with the main list view.
        """
        company_id, financial_year_id, _ = get_current_context(self.request)
        return ItemRevisionType.objects.filter(
            company_id=company_id, 
            financial_year_id__lte=financial_year_id
        ).order_by('-id')

class ItemRevisionTypeCreateView(CreateView):
    """
    Handles the creation of new Item Revision Type records.
    Renders a form and processes its submission.
    """
    model = ItemRevisionType
    form_class = ItemRevisionTypeForm
    template_name = 'design/itemrevisiontype/form.html'
    success_url = reverse_lazy('itemrevisiontype_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        """
        Populates audit fields via the model method before saving.
        Handles HTMX responses for dynamic UI updates.
        """
        company_id, financial_year_id, session_user = get_current_context(self.request)
        
        item_revision = form.instance
        item_revision.populate_audit_fields(company_id, financial_year_id, session_user, is_new_record=True)
        
        response = super().form_valid(form)
        messages.success(self.request, 'Item Revision Type added successfully.')
        
        # If the request came via HTMX, return a 204 No Content response
        # and trigger a custom event to refresh the list.
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshItemRevisionTypeList'
                }
            )
        return response

class ItemRevisionTypeUpdateView(UpdateView):
    """
    Handles updating existing Item Revision Type records.
    Renders a pre-filled form and processes its submission.
    """
    model = ItemRevisionType
    form_class = ItemRevisionTypeForm
    template_name = 'design/itemrevisiontype/form.html'
    success_url = reverse_lazy('itemrevisiontype_list')

    def form_valid(self, form):
        """
        Populates audit fields for updates via the model method before saving.
        Handles HTMX responses for dynamic UI updates.
        """
        company_id, financial_year_id, session_user = get_current_context(self.request)
        
        item_revision = form.instance
        item_revision.populate_audit_fields(company_id, financial_year_id, session_user, is_new_record=False)
        
        response = super().form_valid(form)
        messages.success(self.request, 'Item Revision Type updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshItemRevisionTypeList'
                }
            )
        return response

class ItemRevisionTypeDeleteView(DeleteView):
    """
    Handles the deletion of Item Revision Type records.
    Presents a confirmation dialog and processes the deletion.
    """
    model = ItemRevisionType
    template_name = 'design/itemrevisiontype/confirm_delete.html'
    success_url = reverse_lazy('itemrevisiontype_list')

    def delete(self, request, *args, **kwargs):
        """
        Performs the deletion and handles HTMX responses.
        """
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Item Revision Type deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshItemRevisionTypeList'
                }
            )
        return response

```

#### 4.4 Templates (`design/templates/design/itemrevisiontype/`)

**Task:** Create HTML templates for each view, integrating HTMX, Alpine.js, and DataTables.

**Instructions:**
- **`list.html`**: The main page, extending `core/base.html`. It uses HTMX to load the actual table content dynamically. A modal container for forms is managed by Alpine.js.
- **`_itemrevisiontype_table.html`**: A partial template rendered by HTMX. It contains the DataTables-enabled HTML table and uses Django template tags to loop through data. HTMX attributes are used on Edit/Delete buttons to open modals.
- **`form.html`**: A partial template for creating/updating forms. It renders the Django form fields and uses HTMX for submission.
- **`confirm_delete.html`**: A partial template for delete confirmation dialogs, also using HTMX for the delete action.

```html
{# design/templates/design/itemrevisiontype/list.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Item Revision Types</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'itemrevisiontype_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal"> {# Alpine.js to show modal #}
            Add New Item Revision Type
        </button>
    </div>
    
    <div id="itemrevisiontypeTable-container"
         hx-trigger="load, refreshItemRevisionTypeList from:body" {# Load on page load and on custom event #}
         hx-get="{% url 'itemrevisiontype_table' %}" {# Fetch table content #}
         hx-swap="innerHTML"> {# Replace content inside this div #}
        <!-- Initial loading state while HTMX fetches the table -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Item Revision Types...</p>
        </div>
    </div>
    
    <!-- Modal for displaying forms (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me"> {# Click outside to close modal #}
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4">
            <!-- HTMX will load form content here -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# Alpine.js is typically included in base.html. Additional component-specific JS can go here. #}
<script>
    document.addEventListener('alpine:init', () => {
        // Any Alpine.js components specific to this page can be initialized here.
        // The modal logic is handled by the _ (Hyperscript) directly in the HTML.
    });
</script>
{% endblock %}

```

```html
{# design/templates/design/itemrevisiontype/_itemrevisiontype_table.html #}
<div class="overflow-x-auto bg-white shadow-sm rounded-lg border border-gray-200">
    <table id="itemrevisiontypeTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type of Revisions</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% for obj in item_revision_types %}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-700">{{ obj.revision_type_name }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded text-xs mr-2"
                        hx-get="{% url 'itemrevisiontype_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs"
                        hx-get="{% url 'itemrevisiontype_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="3" class="py-4 text-center text-gray-500">
                    No Item Revision Types found. 
                    <button 
                        class="text-blue-600 hover:underline font-medium ml-2"
                        hx-get="{% url 'itemrevisiontype_add' %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Click here to add one.
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
// Initialize DataTables after the table has been loaded by HTMX
// Ensure jQuery and DataTables are loaded in your base.html
$(document).ready(function() {
    $('#itemrevisiontypeTable').DataTable({
        "pageLength": 10, // Default items per page
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]], // Options for items per page
        "responsive": true // Make table responsive
    });
});
</script>

```

```html
{# design/templates/design/itemrevisiontype/form.html #}
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Item Revision Type</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs italic mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal"> {# Alpine.js to hide modal #}
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                Save
            </button>
            <span id="form-spinner" class="htmx-indicator ml-2">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
            </span>
        </div>
    </form>
</div>
```

```html
{# design/templates/design/itemrevisiontype/confirm_delete.html #}
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete "<strong>{{ object.revision_type_name }}</strong>"? This action cannot be undone.</p>
    
    <div class="mt-6 flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded transition duration-150 ease-in-out"
            _="on click remove .is-active from #modal"> {# Alpine.js to hide modal #}
            Cancel
        </button>
        <button 
            hx-delete="{% url 'itemrevisiontype_delete' object.pk %}"
            hx-swap="none" {# HTMX will not swap anything, expects 204 response #}
            hx-indicator="#delete-spinner"
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
            Delete
        </button>
        <span id="delete-spinner" class="htmx-indicator ml-2">
            <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-red-500"></div>
        </span>
    </div>
</div>
```

#### 4.5 URLs (`design/urls.py`)

**Task:** Define URL patterns for all views.

**Instructions:**
URL patterns are set up for listing, creating, updating, and deleting `ItemRevisionType` records. A dedicated URL for the `_itemrevisiontype_table.html` partial view is included for HTMX to fetch table updates.

```python
# design/urls.py
from django.urls import path
from .views import (
    ItemRevisionTypeListView, 
    ItemRevisionTypeCreateView, 
    ItemRevisionTypeUpdateView, 
    ItemRevisionTypeDeleteView, 
    ItemRevisionTypeTablePartialView
)

urlpatterns = [
    # Main list view for Item Revision Types
    path('itemrevisiontype/', ItemRevisionTypeListView.as_view(), name='itemrevisiontype_list'),
    
    # HTMX endpoint for dynamically loading/refreshing the table content
    path('itemrevisiontype/table/', ItemRevisionTypeTablePartialView.as_view(), name='itemrevisiontype_table'),
    
    # Endpoint for adding a new Item Revision Type (renders form)
    path('itemrevisiontype/add/', ItemRevisionTypeCreateView.as_view(), name='itemrevisiontype_add'),
    
    # Endpoint for editing an existing Item Revision Type (renders form for specific PK)
    path('itemrevisiontype/edit/<int:pk>/', ItemRevisionTypeUpdateView.as_view(), name='itemrevisiontype_edit'),
    
    # Endpoint for deleting an Item Revision Type (renders confirmation, processes delete)
    path('itemrevisiontype/delete/<int:pk>/', ItemRevisionTypeDeleteView.as_view(), name='itemrevisiontype_delete'),
]

```

#### 4.6 Tests (`design/tests.py`)

**Task:** Write comprehensive unit tests for the model and integration tests for the views to ensure functionality and coverage.

**Instructions:**
Tests cover model creation, field values, and the `populate_audit_fields` method. View tests verify correct HTTP responses, template usage, context data, and the behavior of HTMX requests for all CRUD operations. Session mocking is included to provide `compid`, `finyear`, and `username` as used in the ASP.NET code.

```python
# design/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import ItemRevisionType
from datetime import datetime

class ItemRevisionTypeModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Setup common context data for audit fields
        cls.company_id = 1
        cls.financial_year_id = 2024
        cls.session_user = 'testuser'
        
        # Create an initial ItemRevisionType for testing
        cls.item_revision_type1 = ItemRevisionType.objects.create(
            revision_type_name='Initial Test Type',
            sys_date=datetime.now().strftime('%Y/%m/%d'),
            sys_time=datetime.now().strftime('%H:%M:%S'),
            company_id=cls.company_id,
            financial_year_id=cls.financial_year_id,
            session_user=cls.session_user
        )
        # Note: For `managed=False` and `AutoField(primary_key=True)`, 
        # Django relies on the database to auto-assign the ID. 
        # If running tests against an in-memory SQLite, this might need specific setup 
        # (e.g., using a dummy ID for testing, or ensuring SQLite has AUTOINCREMENT).
        # For this example, we assume `create` correctly populates `id` from the DB.
  
    def test_item_revision_type_creation(self):
        """Verify an ItemRevisionType object can be created and its attributes are correct."""
        obj = ItemRevisionType.objects.get(pk=self.item_revision_type1.pk)
        self.assertEqual(obj.revision_type_name, 'Initial Test Type')
        self.assertEqual(obj.company_id, self.company_id)
        self.assertEqual(obj.financial_year_id, self.financial_year_id)
        self.assertEqual(obj.session_user, self.session_user)
        
    def test_revision_type_name_label(self):
        """Verify the verbose name for 'revision_type_name' field."""
        obj = ItemRevisionType.objects.get(pk=self.item_revision_type1.pk)
        field_label = obj._meta.get_field('revision_type_name').verbose_name
        # Django automatically converts 'revision_type_name' to 'revision type name'
        self.assertEqual(field_label, 'revision type name') 
        
    def test_str_method(self):
        """Verify the __str__ method returns the revision type name."""
        obj = ItemRevisionType.objects.get(pk=self.item_revision_type1.pk)
        self.assertEqual(str(obj), 'Initial Test Type')

    def test_populate_audit_fields_new_record(self):
        """Test 'populate_audit_fields' for a new record, ensuring all fields are set."""
        new_obj = ItemRevisionType(revision_type_name='New Item')
        company = 2
        fin_year = 2025
        user = 'new_user'
        new_obj.populate_audit_fields(company, fin_year, user, is_new_record=True)
        
        self.assertEqual(new_obj.company_id, company)
        self.assertEqual(new_obj.financial_year_id, fin_year)
        self.assertEqual(new_obj.session_user, user)
        self.assertIsNotNone(new_obj.sys_date)
        self.assertIsNotNone(new_obj.sys_time)

    def test_populate_audit_fields_existing_record(self):
        """Test 'populate_audit_fields' for an existing record, ensuring only relevant fields are updated."""
        existing_obj = ItemRevisionType.objects.get(pk=self.item_revision_type1.pk)
        original_company_id = existing_obj.company_id
        original_financial_year_id = existing_obj.financial_year_id
        
        updated_user = 'updated_testuser'
        existing_obj.populate_audit_fields(100, 3000, updated_user, is_new_record=False) # Company/FinYear params passed, but should not change

        self.assertEqual(existing_obj.company_id, original_company_id) # Should NOT be updated
        self.assertEqual(existing_obj.financial_year_id, original_financial_year_id) # Should NOT be updated
        self.assertEqual(existing_obj.session_user, updated_user) # Should be updated
        self.assertIsNotNone(existing_obj.sys_date)
        self.assertIsNotNone(existing_obj.sys_time)


class ItemRevisionTypeViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create multiple test data instances for view testing, including filtering scenarios
        cls.company_id_1 = 1
        cls.financial_year_id_1 = 2024
        cls.session_user = 'testuser'
        
        cls.item_revision_type1 = ItemRevisionType.objects.create(
            revision_type_name='View Test Type A',
            sys_date=datetime.now().strftime('%Y/%m/%d'),
            sys_time=datetime.now().strftime('%H:%M:%S'),
            company_id=cls.company_id_1,
            financial_year_id=cls.financial_year_id_1,
            session_user=cls.session_user
        )
        cls.item_revision_type2 = ItemRevisionType.objects.create(
            revision_type_name='View Test Type B',
            sys_date=datetime.now().strftime('%Y/%m/%d'),
            sys_time=datetime.now().strftime('%H:%M:%S'),
            company_id=cls.company_id_1,
            financial_year_id=cls.financial_year_id_1 - 1, # Older financial year
            session_user=cls.session_user
        )
        cls.item_revision_type_other_company = ItemRevisionType.objects.create(
            revision_type_name='Other Company Type',
            sys_date=datetime.now().strftime('%Y/%m/%d'),
            sys_time=datetime.now().strftime('%H:%M:%S'),
            company_id=2, # Different company ID
            financial_year_id=cls.financial_year_id_1,
            session_user='other_user'
        )
    
    def setUp(self):
        """Set up client and mock session for each test method."""
        self.client = Client()
        # Mock session attributes that `get_current_context` in views depends on
        session = self.client.session
        session['compid'] = self.company_id_1
        session['finyear'] = self.financial_year_id_1
        session['username'] = self.session_user
        session.save()

    def test_list_view(self):
        """Test the main list page for Item Revision Types."""
        response = self.client.get(reverse('itemrevisiontype_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/itemrevisiontype/list.html')
        self.assertIn('item_revision_types', response.context)
        # Should include item_revision_type1 (current year) and item_revision_type2 (past year)
        # but NOT item_revision_type_other_company.
        self.assertEqual(response.context['item_revision_types'].count(), 2) 
        self.assertContains(response, 'View Test Type A')
        self.assertContains(response, 'View Test Type B')
        self.assertNotContains(response, 'Other Company Type')

    def test_table_partial_view_htmx(self):
        """Test the HTMX partial view for table content."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('itemrevisiontype_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/itemrevisiontype/_itemrevisiontype_table.html')
        self.assertIn('item_revision_types', response.context)
        self.assertContains(response, 'View Test Type A')
        self.assertContains(response, 'View Test Type B')

    def test_create_view_get(self):
        """Test GET request to the create form."""
        response = self.client.get(reverse('itemrevisiontype_add'), headers={'HX-Request': 'true'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/itemrevisiontype/form.html')
        self.assertIn('form', response.context)
        
    def test_create_view_post_htmx_success(self):
        """Test successful HTMX POST request for creating a new item."""
        data = {
            'revision_type_name': 'New Type Via HTMX',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('itemrevisiontype_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX expects No Content for successful trigger
        self.assertEqual(response.headers['HX-Trigger'], 'refreshItemRevisionTypeList')
        self.assertTrue(ItemRevisionType.objects.filter(revision_type_name='New Type Via HTMX').exists())
        
        # Verify audit fields were populated correctly by the model method
        new_obj = ItemRevisionType.objects.get(revision_type_name='New Type Via HTMX')
        self.assertEqual(new_obj.company_id, self.company_id_1)
        self.assertEqual(new_obj.financial_year_id, self.financial_year_id_1)
        self.assertEqual(new_obj.session_user, self.session_user)

    def test_create_view_post_validation_error(self):
        """Test HTMX POST request with invalid data (e.g., missing required field)."""
        data = {
            'revision_type_name': '', # Missing required field
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('itemrevisiontype_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # Form with errors returned
        self.assertTemplateUsed(response, 'design/itemrevisiontype/form.html')
        self.assertContains(response, 'This field is required.')
        self.assertFalse(ItemRevisionType.objects.filter(revision_type_name='').exists()) # No empty record created

    def test_create_view_post_duplicate_name(self):
        """Test HTMX POST request with a duplicate revision type name."""
        data = {
            'revision_type_name': 'Initial Test Type', # Duplicate
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('itemrevisiontype_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # Form with errors returned
        self.assertTemplateUsed(response, 'design/itemrevisiontype/form.html')
        self.assertContains(response, 'This revision type name already exists.')

    def test_update_view_get(self):
        """Test GET request to the update form."""
        obj = self.item_revision_type1
        response = self.client.get(reverse('itemrevisiontype_edit', args=[obj.pk]), headers={'HX-Request': 'true'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/itemrevisiontype/form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, obj) # Form should be pre-filled with instance data
        
    def test_update_view_post_htmx_success(self):
        """Test successful HTMX POST request for updating an item."""
        obj = self.item_revision_type1
        new_name = 'Updated Type HTMX'
        data = {
            'revision_type_name': new_name,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('itemrevisiontype_edit', args=[obj.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshItemRevisionTypeList')
        
        obj.refresh_from_db() # Reload object from DB to check updated values
        self.assertEqual(obj.revision_type_name, new_name)
        # Ensure company_id and financial_year_id are NOT updated on existing record as per legacy behavior
        self.assertEqual(obj.company_id, self.company_id_1)
        self.assertEqual(obj.financial_year_id, self.financial_year_id_1)
        self.assertEqual(obj.session_user, self.session_user) # session_user should be updated

    def test_delete_view_get(self):
        """Test GET request to the delete confirmation page."""
        obj = self.item_revision_type1
        response = self.client.get(reverse('itemrevisiontype_delete', args=[obj.pk]), headers={'HX-Request': 'true'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/itemrevisiontype/confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], obj)
        
    def test_delete_view_post_htmx_success(self):
        """Test successful HTMX DELETE request for deleting an item."""
        # Create a new object specifically for deletion test
        obj_to_delete = ItemRevisionType.objects.create(
            revision_type_name='Temporary Item to Delete',
            sys_date=datetime.now().strftime('%Y/%m/%d'),
            sys_time=datetime.now().strftime('%H:%M:%S'),
            company_id=self.company_id_1,
            financial_year_id=self.financial_year_id_1,
            session_user=self.session_user
        )
        initial_count = ItemRevisionType.objects.filter(company_id=self.company_id_1, financial_year_id__lte=self.financial_year_id_1).count()
        
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.delete(reverse('itemrevisiontype_delete', args=[obj_to_delete.pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshItemRevisionTypeList')
        # Verify that the object was indeed deleted
        self.assertEqual(ItemRevisionType.objects.filter(company_id=self.company_id_1, financial_year_id__lte=self.financial_year_id_1).count(), initial_count - 1)
        self.assertFalse(ItemRevisionType.objects.filter(pk=obj_to_delete.pk).exists())

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The migration plan fully embraces HTMX and Alpine.js to deliver a highly interactive and responsive user experience without the complexity of traditional JavaScript frameworks.

- **HTMX for Dynamic Updates:**
    - All data loading, form submissions (create/update), and delete actions are handled via HTMX requests.
    - When a form is submitted successfully, HTMX triggers a custom event (`refreshItemRevisionTypeList`) that causes the main table section (`itemrevisiontypeTable-container`) to automatically reload its content from `{% url 'itemrevisiontype_table' %}`. This ensures the table always reflects the latest data without a full page refresh.
    - Modals for add, edit, and delete operations are loaded via `hx-get` into a designated modal content area (`#modalContent`).
- **Alpine.js for UI State Management:**
    - Alpine.js (via `_` Hyperscript attributes) is used for simple UI interactions, such as showing and hiding the modal. The modal state is managed by adding/removing a CSS class (`is-active`).
    - The `on click if event.target.id == 'modal' remove .is-active from me` attribute on the modal div allows closing the modal by clicking outside its content, providing intuitive user interaction.
- **DataTables for List Views:**
    - All list views are powered by DataTables.js, which provides out-of-the-box client-side searching, sorting, and pagination capabilities.
    - The DataTables initialization script runs once the table content is loaded into the DOM by HTMX, ensuring that the dynamic content is properly enhanced.
- **Seamless User Experience:**
    - All interactions (adding, editing, deleting) occur within modals or partial view updates, avoiding disruptive full page reloads.
    - This creates a smooth, single-page application like feel without the overhead of a large JavaScript framework.

## Final Notes

This comprehensive plan provides a clear roadmap for migrating your ASP.NET Item Revision Type module to Django. By following these structured steps and leveraging AI-assisted automation, your team can efficiently transition to a modern, maintainable, and high-performance Django application. This modernization not only improves technical efficiency but also enhances user experience through dynamic and responsive interfaces, ultimately contributing to your organization's business objectives.