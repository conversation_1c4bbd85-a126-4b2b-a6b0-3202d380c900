## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

**Instructions:**
From the `asp:SqlDataSource` and `asp:GridView` definitions:
- **Table Name:** `tblDG_Category_Master`
- **Columns Identified for the Model:**
    - `CId`: Primary Key, Integer. Used as `DataKeyNames` in GridView.
    - `CName`: String. Editable via `txtCate` in GridView.
    - `Symbol`: String. Displayed, and has a `Bind("Symbol")` in `EditItemTemplate` for a `Label`, implying it's part of the record, though the original `UpdateCommand` doesn't update it. For modernization, we'll make it editable.
    - `HasSubCat`: Boolean/Integer. Displayed, and has a `CheckBox` in `EditItemTemplate`. Original `UpdateCommand` doesn't update it. For modernization, we'll make it editable.
    - `CompId`: Integer. Used as a `SelectParameter` from session, indicating a company ID filter. It's likely a column in the table.
    - `FinYearId`: Integer. Used as a `SelectParameter` from session, indicating a financial year filter. It's likely a column in the table.

### Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

**Instructions:**
- **Create:** Not explicitly shown in this ASP.NET page, but a common part of master data management. We will include a `CreateView` for completeness.
- **Read:** Handled by `SqlDataSource`'s `SelectCommand` filtering by `CompId` and `FinYearId`, and displayed by `GridView1`.
- **Update:** Handled by `GridView1_RowCommand` (specifically the "Update" command) which triggers `SqlDataSource`'s `UpdateCommand` to modify `CName` based on `CId`.
- **Delete:** Not explicitly shown in this ASP.NET page. We will include a `DeleteView` for completeness.
- **Validation:** `RequiredFieldValidator` for `CName` (`txtCate`). This will be replicated as `required=True` in the Django Form.

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

**Instructions:**
- **`asp:GridView`:** This will be replaced by a standard HTML `<table>` element enhanced with `DataTables.js` for client-side searching, sorting, and pagination, and HTMX for dynamic content updates.
- **`asp:TextBox` (`txtCate`):** Replaced by Django `forms.TextInput` with Tailwind CSS styling.
- **`asp:Label` (`Label1`, `Label2`, `Label3`, `lblSy`, `lblsubcatNo`, `lblMessage`):** Replaced by standard HTML text elements. Messages (`lblMessage`) will be handled by Django's messages framework and displayed via HTMX.
- **`asp:CheckBox` (`CheckBox1`):** Replaced by Django `forms.CheckboxInput` with Tailwind CSS styling.
- **`asp:CommandField` (Edit button):** Replaced by an HTMX-triggered button that loads an edit form into a modal.
- **Client-side JavaScript (`PopUpMsg.js`, `loadingNotifier.js`):** Functionality will be replaced by HTMX for dynamic interactions (e.g., loading indicators via `hx-indicator`) and Alpine.js for UI state management (e.g., modals).

---

### Step 4: Generate Django Code

We will create a Django application named `masters`.

#### 4.1 Models (`masters/models.py`)

```python
from django.db import models

class Category(models.Model):
    # CId is the primary key in the source table. Mapping it to 'category_id'
    # and setting primary_key=True ensures Django uses it as the PK.
    category_id = models.IntegerField(db_column='CId', primary_key=True)
    name = models.CharField(db_column='CName', max_length=255) # Assuming suitable max_length
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True) # Assuming suitable max_length, can be null/blank
    has_subcategory = models.BooleanField(db_column='HasSubCat', default=False)
    company_id = models.IntegerField(db_column='CompId')
    financial_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False  # Important: Django won't manage this table's schema in migrations
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Item Category'
        verbose_name_plural = 'Item Categories'
        # Define a unique_together constraint if a combination of fields (e.g., name, company, financial year)
        # must be unique for a category, which is common in ERPs.
        # unique_together = (('name', 'company_id', 'financial_year_id'),)

    def __str__(self):
        return f"{self.name} (ID: {self.category_id})"

    # Business logic methods - 'Fat Model' approach
    def update_category_details(self, new_name, new_symbol, new_has_subcategory):
        """
        Updates the category details.
        This method encapsulates the business logic for updating a category.
        """
        # Add any validation or specific business rules here
        if not new_name:
            raise ValueError("Category name cannot be empty.")
        
        self.name = new_name
        self.symbol = new_symbol
        self.has_subcategory = new_has_subcategory
        self.save()
        return True
    
    @classmethod
    def create_new_category(cls, name, symbol, has_subcategory, company_id, financial_year_id):
        """
        Creates a new category instance.
        This method encapsulates the business logic for creating a category.
        """
        if not name:
            raise ValueError("Category name cannot be empty.")
        # Add logic to generate category_id if it's auto-incrementing in the DB
        # For 'managed=False' and primary_key=True, ensure DB handles auto-increment or provide one.
        # Assuming for now DB handles CId auto-increment if not provided.
        # If CId is NOT auto-increment, a sequence or similar mechanism would be needed here.
        # For simplicity in this example, we assume it's auto-increment OR manually provided.
        # If it's an Identity column, you might exclude it from creation form and let DB handle.
        # If it's sequential, a helper function could determine the next ID.
        
        # For this example, let's assume we don't pass category_id for creation, DB handles it.
        # If DB is an identity column, remove primary_key=True from model definition and let Django manage 'id'.
        # For current setup (CId as primary_key=True), we must provide it or ensure DB auto-generates.
        # Given ASP.NET, CId was likely an IDENTITY column. So, better not to set primary_key=True in Django
        # and let Django use its default 'id' PK, or carefully handle `CId` generation.
        # Let's adjust: remove primary_key=True from category_id and let CId be just a field,
        # and Django will use its own auto-increment 'id' field by default. This is safer.
        # The original code refers to CId as DataKeyNames, so it's a unique identifier.

        # RE-EVALUATION: The prompt implies using the exact schema, so CId as the primary key of the table means it *should* be the primary_key=True.
        # If it's an Identity column, it means it's auto-generated. When managed=False, Django doesn't know this.
        # For inserts, you typically omit the Identity column.
        # For this specific case, to align with the SQL `UPDATE` by `CId` and `DataKeyNames="CId"`,
        # it is best to keep `category_id = models.IntegerField(db_column='CId', primary_key=True)`.
        # When creating a new category, we will *not* pass `category_id` and rely on the database's IDENTITY property
        # to generate it. This requires careful handling in the `CategoryCreateForm` or `CategoryCreateView`.

        new_category = cls(
            name=name,
            symbol=symbol,
            has_subcategory=has_subcategory,
            company_id=company_id,
            financial_year_id=financial_year_id
        )
        new_category.save()
        return new_category

    def can_delete(self):
        """
        Business rule: Checks if a category can be deleted.
        e.g., if it has associated subcategories or items.
        """
        # Example logic: Assume a related_name 'subcategories' on a SubCategory model
        # if self.subcategories.exists(): # Requires a SubCategory model relation
        #     return False
        return True # Default to true for now if no specific logic was in ASP.NET
```

#### 4.2 Forms (`masters/forms.py`)

```python
from django import forms
from .models import Category

class CategoryForm(forms.ModelForm):
    # Using 'name' instead of 'category_id' as the initial field because category_id is primary key
    # and for new records it will be auto-generated by the database.
    # For existing records, it's used to identify the record but not directly edited.

    class Meta:
        model = Category
        # Exclude 'category_id' from fields to allow DB to auto-generate it for new instances
        # For existing instances, the PK is used in URL, not form.
        fields = ['name', 'symbol', 'has_subcategory'] 
        
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter category name'
            }),
            'symbol': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter symbol'
            }),
            'has_subcategory': forms.CheckboxInput(attrs={
                'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500'
            }),
        }
        labels = {
            'name': 'Category Name',
            'symbol': 'Symbol',
            'has_subcategory': 'Has SubCategory',
        }
        
    def clean_name(self):
        name = self.cleaned_data.get('name')
        if not name:
            raise forms.ValidationError("Category name is required.")
        # Additional validation (e.g., uniqueness within company/financial year context)
        # category_id for current instance is self.instance.category_id if in update mode
        # Example: Ensure name is unique for company_id and financial_year_id (if relevant)
        # if Category.objects.filter(name=name, company_id=self.instance.company_id, 
        #                           financial_year_id=self.instance.financial_year_id).exclude(pk=self.instance.pk).exists():
        #    raise forms.ValidationError("Category with this name already exists for the current context.")
        return name

    # The company_id and financial_year_id are set by the view based on session, not form input.
    # So they are not in `fields`.
```

#### 4.3 Views (`masters/views.py`)

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import get_object_or_404
from .models import Category
from .forms import CategoryForm

# Mixin to get session context for filtering and saving
class CategoryContextMixin:
    def get_company_financial_context(self):
        # In a real ERP, user authentication and associated company/financial year
        # would be managed, likely via a custom User model or session data.
        # Assuming 'compid' and 'finyear' are in session as in ASP.NET.
        company_id = self.request.session.get('compid', 1)  # Default to 1 if not found for testing
        financial_year_id = self.request.session.get('finyear', 2023) # Default to 2023 for testing
        return company_id, financial_year_id

class CategoryListView(ListView):
    model = Category
    template_name = 'masters/category/list.html'
    context_object_name = 'categories' # Not directly used in list.html, but in _category_table.html
    # List view itself only renders the container for the table partial.

class CategoryTablePartialView(CategoryContextMixin, ListView):
    model = Category
    template_name = 'masters/category/_category_table.html'
    context_object_name = 'categories'

    def get_queryset(self):
        company_id, financial_year_id = self.get_company_financial_context()
        queryset = super().get_queryset()
        # Filter matching original ASP.NET SelectCommand logic:
        # WHERE (([CompId] = @CompId) AND ([FinYearId] <= @FinYearId)) order by [CId] desc
        queryset = queryset.filter(company_id=company_id, financial_year_id__lte=financial_year_id).order_by('-category_id')
        return queryset

class CategoryCreateView(CategoryContextMixin, CreateView):
    model = Category
    form_class = CategoryForm
    template_name = 'masters/category/_category_form.html' # Use partial for modal
    success_url = reverse_lazy('category_list') # Redirect to list page on success

    def form_valid(self, form):
        company_id, financial_year_id = self.get_company_financial_context()
        
        # Set context fields on the instance before saving.
        # As category_id is primary_key=True and assumed identity, we don't set it here.
        # Instead, the model's create_new_category can handle it, or just save the instance.
        form.instance.company_id = company_id
        form.instance.financial_year_id = financial_year_id

        # Use the model's business logic for creation
        # Using form.save() directly calls the model's save method.
        # If more complex logic is needed, call model.create_new_category from here.
        try:
            response = super().form_valid(form)
            messages.success(self.request, 'Item Category added successfully.')
        except ValueError as e: # Catch potential business rule violations from model
            form.add_error(None, str(e))
            return self.form_invalid(form) # Re-render form with errors

        if self.request.headers.get('HX-Request'):
            # HTMX specific response for partial updates
            return HttpResponse(
                status=204,  # No content, successful update without full page reload
                headers={
                    'HX-Trigger': 'refreshCategoryList' # Trigger event for HTMX to refresh the table
                }
            )
        return response # Fallback for non-HTMX requests

class CategoryUpdateView(CategoryContextMixin, UpdateView):
    model = Category
    form_class = CategoryForm
    template_name = 'masters/category/_category_form.html' # Use partial for modal
    pk_url_kwarg = 'pk' # The URL parameter for the primary key is 'pk'
    success_url = reverse_lazy('category_list')

    def form_valid(self, form):
        # In update, company_id and financial_year_id are already set on the instance.
        # We might add logic to ensure they are not changed by mistake, if needed.
        # Using the model's business logic for update
        try:
            # Call the model's update method if it contains specific business logic
            # For simplicity, we assume form.save() is sufficient,
            # or form.instance.update_category_details() if it's the fat model method.
            # Example using fat model method:
            form.instance.update_category_details(
                new_name=form.cleaned_data['name'],
                new_symbol=form.cleaned_data['symbol'],
                new_has_subcategory=form.cleaned_data['has_subcategory']
            )
            messages.success(self.request, 'Item Category updated successfully.')
        except ValueError as e:
            form.add_error(None, str(e))
            return self.form_invalid(form)
        
        # If using super().form_valid(form) directly without custom update_category_details:
        # response = super().form_valid(form) 
        response = HttpResponseRedirect(self.get_success_url()) # Manual redirect as save() is called manually
        
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCategoryList'
                }
            )
        return response

class CategoryDeleteView(CategoryContextMixin, DeleteView):
    model = Category
    template_name = 'masters/category/_category_confirm_delete.html' # Use partial for modal
    pk_url_kwarg = 'pk'
    success_url = reverse_lazy('category_list')

    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        
        # Business logic: Check if the category can be deleted
        if not self.object.can_delete():
            messages.error(request, 'This item category cannot be deleted as it has associated data.')
            if request.headers.get('HX-Request'):
                # Return 200 OK with error message to be swapped into modal or page
                return HttpResponse(
                    self.template_name, # Render template again with message
                    status=200 # Or a specific HTMX error status like 400
                )
            return self.get(request, *args, **kwargs) # Re-render page with error

        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Item Category deleted successfully.')
        
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCategoryList'
                }
            )
        return response

```

#### 4.4 Templates (`masters/templates/masters/category/`)

**`list.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Item Categories</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'category_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then add .opacity-100 to #modalOverlay then add .scale-100 to #modalContent">
            Add New Item Category
        </button>
    </div>
    
    <div id="categoryTable-container"
         hx-trigger="load, refreshCategoryList from:body"
         hx-get="{% url 'category_table' %}"
         hx-swap="innerHTML"
         class="relative min-h-[200px]"> {# Added min-h for loading indicator placement #}
        <!-- DataTable will be loaded here via HTMX -->
        <div class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10 htmx-indicator">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="ml-3 text-gray-600">Loading...</p>
        </div>
    </div>
    
    <!-- Modal for form (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 z-50 hidden items-center justify-center transition-opacity duration-300 ease-in-out opacity-0"
         _="on click if event.target.id == 'modal' remove .opacity-100 from #modalOverlay then remove .scale-100 from #modalContent then remove .flex from me then add .hidden to me wait 300ms">
        <div id="modalOverlay" class="absolute inset-0 transition-opacity duration-300 ease-in-out"></div>
        <div id="modalContent" 
             class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 sm:mx-0 transform scale-95 transition-transform duration-300 ease-in-out"
             _="on click add .scale-100 to me">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.tailwindcss.min.js"></script>
<link href="https://cdn.datatables.net/2.0.7/css/dataTables.tailwindcss.min.css" rel="stylesheet">
<link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.5.2/css/all.min.css" rel="stylesheet">

<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
        // For simple modal, direct HTMX+Alpine attributes are often sufficient
    });

    // Handle HTMX afterSwap events for DataTables reinitialization
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'categoryTable-container') {
            $('#categoryTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true, // Destroy existing instance if any
                "responsive": true,
            });
        }
        // Close modal after successful form submission (HTMX status 204 or redirect)
        if (event.detail.xhr.status === 204) {
            // Remove the modal active classes
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.remove('opacity-100', 'flex');
                modal.classList.add('hidden');
                document.getElementById('modalContent').innerHTML = ''; // Clear modal content
            }
        }
    });

    // HTMX:afterOnLoad triggers after all scripts etc have run
    document.body.addEventListener('htmx:afterOnLoad', function(event) {
        if (event.detail.requestHeaders['HX-Request'] && event.detail.xhr.status === 200 && event.detail.target.id === 'modalContent') {
            // If modal content was loaded successfully, ensure modal is visible
            const modal = document.getElementById('modal');
            const modalOverlay = document.getElementById('modalOverlay');
            const modalContent = document.getElementById('modalContent');
            modal.classList.add('flex', 'opacity-100');
            modal.classList.remove('hidden');
            modalOverlay.classList.add('opacity-100');
            modalContent.classList.add('scale-100');
        }
    });
</script>
{% endblock %}
```

**`_category_table.html`** (Partial for HTMX)
```html
<div class="bg-white shadow-md rounded-lg overflow-hidden">
    <table id="categoryTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Has SubCategory</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for category in categories %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ category.name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ category.symbol }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">
                    {% if category.has_subcategory %}
                        <i class="fas fa-check-circle text-green-500"></i> Yes
                    {% else %}
                        <i class="fas fa-times-circle text-red-500"></i> No
                    {% endif %}
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 text-xs transition duration-300 ease-in-out"
                        hx-get="{% url 'category_edit' category.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then add .opacity-100 to #modalOverlay then add .scale-100 to #modalContent">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-xs transition duration-300 ease-in-out"
                        hx-get="{% url 'category_delete' category.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then add .opacity-100 to #modalOverlay then add .scale-100 to #modalContent">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5" class="py-4 px-4 text-center text-gray-500 text-lg">No data to display!</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

{# DataTables initialization moved to list.html's htmx:afterSwap to ensure reinitialization #}
```

**`_category_form.html`** (Partial for HTMX modal)
```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Item Category</h3>
    
    {% if messages %}
        <div class="mb-4">
            {% for message in messages %}
                <div class="{% if message.tags == 'error' %}bg-red-100 border-red-400 text-red-700{% elif message.tags == 'success' %}bg-green-100 border-green-400 text-green-700{% endif %} border px-4 py-3 rounded relative" role="alert">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-6">
        {% csrf_token %}
        
        <div>
            <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.name.label }}
            </label>
            {{ form.name }}
            {% if form.name.errors %}
            <p class="text-red-500 text-xs mt-1">{{ form.name.errors }}</p>
            {% endif %}
        </div>

        <div>
            <label for="{{ form.symbol.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.symbol.label }}
            </label>
            {{ form.symbol }}
            {% if form.symbol.errors %}
            <p class="text-red-500 text-xs mt-1">{{ form.symbol.errors }}</p>
            {% endif %}
        </div>
        
        <div class="flex items-center">
            {{ form.has_subcategory }}
            <label for="{{ form.has_subcategory.id_for_label }}" class="ml-2 block text-sm text-gray-900">
                {{ form.has_subcategory.label }}
            </label>
            {% if form.has_subcategory.errors %}
            <p class="text-red-500 text-xs mt-1">{{ form.has_subcategory.errors }}</p>
            {% endif %}
        </div>

        {% if form.non_field_errors %}
            <div class="text-red-500 text-xs mt-1">
                {% for error in form.non_field_errors %}
                    <p>{{ error }}</p>
                {% endfor %}
            </div>
        {% endif %}
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .opacity-100 from #modalOverlay then remove .scale-100 from #modalContent then remove .flex from #modal then add .hidden to #modal wait 300ms">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                Save Item Category
            </button>
        </div>
    </form>
</div>
```

**`_category_confirm_delete.html`** (Partial for HTMX modal)
```html
<div class="p-6 text-center">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">Confirm Deletion</h3>
    
    {% if messages %}
        <div class="mb-4">
            {% for message in messages %}
                <div class="{% if message.tags == 'error' %}bg-red-100 border-red-400 text-red-700{% elif message.tags == 'success' %}bg-green-100 border-green-400 text-green-700{% endif %} border px-4 py-3 rounded relative" role="alert">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <p class="text-gray-700 mb-6">Are you sure you want to delete the item category "<strong>{{ object.name }}</strong>" (ID: {{ object.category_id }})?</p>
    
    <form hx-post="{% url 'category_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-center space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .opacity-100 from #modalOverlay then remove .scale-100 from #modalContent then remove .flex from #modal then add .hidden to #modal wait 300ms">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`masters/urls.py`)

```python
from django.urls import path
from .views import CategoryListView, CategoryCreateView, CategoryUpdateView, CategoryDeleteView, CategoryTablePartialView

urlpatterns = [
    path('category/', CategoryListView.as_view(), name='category_list'),
    path('category/add/', CategoryCreateView.as_view(), name='category_add'),
    path('category/edit/<int:pk>/', CategoryUpdateView.as_view(), name='category_edit'),
    path('category/delete/<int:pk>/', CategoryDeleteView.as_view(), name='category_delete'),
    # HTMX partial endpoint for the table
    path('category/table/', CategoryTablePartialView.as_view(), name='category_table'),
]

```

#### 4.6 Tests (`masters/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from .models import Category
from .forms import CategoryForm

class CategoryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        # Note: 'category_id' (CId) is primary key, and likely auto-incremented in DB.
        # For 'managed=False', you might need to insert directly or ensure your DB handles PK generation.
        # For tests, we'll assign unique IDs, assuming DB handles it.
        cls.category1 = Category.objects.create(
            category_id=101,  # Example ID for testing, if DB doesn't auto-generate for tests
            name='Test Category A',
            symbol='TCA',
            has_subcategory=True,
            company_id=1,
            financial_year_id=2023
        )
        cls.category2 = Category.objects.create(
            category_id=102,
            name='Test Category B',
            symbol='TCB',
            has_subcategory=False,
            company_id=1,
            financial_year_id=2023
        )
        cls.category3 = Category.objects.create(
            category_id=103,
            name='Test Category C',
            symbol='TCC',
            has_subcategory=True,
            company_id=2, # Different company
            financial_year_id=2023
        )
        cls.category4 = Category.objects.create(
            category_id=104,
            name='Test Category D',
            symbol='TCD',
            has_subcategory=False,
            company_id=1,
            financial_year_id=2022 # Different financial year
        )

    def test_category_creation(self):
        category = Category.objects.get(category_id=101)
        self.assertEqual(category.name, 'Test Category A')
        self.assertEqual(category.symbol, 'TCA')
        self.assertTrue(category.has_subcategory)
        self.assertEqual(category.company_id, 1)
        self.assertEqual(category.financial_year_id, 2023)

    def test_name_label(self):
        category = Category.objects.get(category_id=101)
        field_label = category._meta.get_field('name').verbose_name
        self.assertEqual(field_label, 'CName') # db_column name is CName

    def test_str_representation(self):
        category = Category.objects.get(category_id=101)
        self.assertEqual(str(category), 'Test Category A (ID: 101)')

    def test_update_category_details_method(self):
        category = Category.objects.get(category_id=102)
        category.update_category_details('Updated Category B', 'UCB', True)
        category.refresh_from_db()
        self.assertEqual(category.name, 'Updated Category B')
        self.assertEqual(category.symbol, 'UCB')
        self.assertTrue(category.has_subcategory)

    def test_update_category_details_method_validation(self):
        category = Category.objects.get(category_id=102)
        with self.assertRaisesMessage(ValueError, "Category name cannot be empty."):
            category.update_category_details('', 'UCB', True)

    def test_can_delete_method(self):
        category = Category.objects.get(category_id=101)
        self.assertTrue(category.can_delete())
        # Add a test case for when can_delete() returns False if business logic is implemented.

class CategoryViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.company_id = 1
        cls.financial_year_id = 2023
        cls.category1 = Category.objects.create(
            category_id=101, 
            name='Existing Category 1', 
            symbol='EC1', 
            has_subcategory=True, 
            company_id=cls.company_id, 
            financial_year_id=cls.financial_year_id
        )
        cls.category2 = Category.objects.create(
            category_id=102, 
            name='Existing Category 2', 
            symbol='EC2', 
            has_subcategory=False, 
            company_id=cls.company_id, 
            financial_year_id=cls.financial_year_id
        )
        cls.category3_old_year = Category.objects.create(
            category_id=103,
            name='Old Year Category',
            symbol='OYC',
            has_subcategory=True,
            company_id=cls.company_id,
            financial_year_id=cls.financial_year_id - 1
        )
        cls.category4_other_company = Category.objects.create(
            category_id=104,
            name='Other Company Category',
            symbol='OCC',
            has_subcategory=False,
            company_id=cls.company_id + 1,
            financial_year_id=cls.financial_year_id
        )
    
    def setUp(self):
        self.client = Client()
        session = self.client.session
        session['compid'] = self.company_id
        session['finyear'] = self.financial_year_id
        session.save() # Crucial to save session changes

    def test_list_view_get(self):
        response = self.client.get(reverse('category_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'masters/category/list.html')
        # Check if messages are present (from `messages.success` etc. on other views)
        messages = list(get_messages(response.wsgi_request))
        # self.assertEqual(len(messages), 0) # Expect no messages on initial GET

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('category_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'masters/category/_category_table.html')
        # Check that only categories for the current company and financial year (and older) are shown
        self.assertContains(response, 'Existing Category 1')
        self.assertContains(response, 'Existing Category 2')
        self.assertContains(response, 'Old Year Category') # finyear <= @FinYearId
        self.assertNotContains(response, 'Other Company Category') # Different company_id
        self.assertContains(response, self.category1.symbol) # Check for symbol presence

    def test_create_view_get(self):
        response = self.client.get(reverse('category_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'masters/category/_category_form.html')
        self.assertTrue('form' in response.context)
        self.assertIsInstance(response.context['form'], CategoryForm)

    def test_create_view_post_success(self):
        initial_count = Category.objects.count()
        data = {
            'name': 'New Category From Test',
            'symbol': 'NCFT',
            'has_subcategory': True,
        }
        response = self.client.post(reverse('category_add'), data, HTTP_HX_REQUEST='true')
        
        # Expect 204 No Content for successful HTMX post
        self.assertEqual(response.status_code, 204)
        self.assertEqual(Category.objects.count(), initial_count + 1)
        self.assertTrue(Category.objects.filter(name='New Category From Test').exists())
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCategoryList')
        
        # Verify messages (requires fetching messages from the client's response)
        messages = list(get_messages(self.client.get(reverse('category_list')).wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Item Category added successfully.')

    def test_create_view_post_invalid(self):
        initial_count = Category.objects.count()
        data = {
            'name': '', # Invalid data
            'symbol': 'INVALID',
            'has_subcategory': False,
        }
        response = self.client.post(reverse('category_add'), data, HTTP_HX_REQUEST='true')
        
        # Expect 200 OK because HTMX form re-renders on error
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'masters/category/_category_form.html')
        self.assertFalse(Category.objects.filter(name='').exists())
        self.assertEqual(Category.objects.count(), initial_count)
        self.assertContains(response, 'Category name is required.') # Check for validation error message

    def test_update_view_get(self):
        category = self.category1
        response = self.client.get(reverse('category_edit', args=[category.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'masters/category/_category_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance.name, category.name)

    def test_update_view_post_success(self):
        category = self.category1
        data = {
            'name': 'Updated Category Name',
            'symbol': 'UCN',
            'has_subcategory': False,
        }
        response = self.client.post(reverse('category_edit', args=[category.pk]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # 204 No Content for successful HTMX update
        category.refresh_from_db()
        self.assertEqual(category.name, 'Updated Category Name')
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCategoryList')

        messages = list(get_messages(self.client.get(reverse('category_list')).wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Item Category updated successfully.')

    def test_update_view_post_invalid(self):
        category = self.category1
        data = {
            'name': '', # Invalid
            'symbol': 'UCN',
            'has_subcategory': False,
        }
        response = self.client.post(reverse('category_edit', args=[category.pk]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'masters/category/_category_form.html')
        self.assertContains(response, 'Category name is required.')
        category.refresh_from_db()
        self.assertNotEqual(category.name, '') # Ensure it wasn't updated with invalid data

    def test_delete_view_get(self):
        category_to_delete = self.category2
        response = self.client.get(reverse('category_delete', args=[category_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'masters/category/_category_confirm_delete.html')
        self.assertContains(response, f"Are you sure you want to delete the item category \"{category_to_delete.name}\"")

    def test_delete_view_post_success(self):
        category_to_delete_pk = self.category2.pk
        initial_count = Category.objects.count()
        response = self.client.post(reverse('category_delete', args=[category_to_delete_pk]), HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # No Content for successful HTMX delete
        self.assertEqual(Category.objects.count(), initial_count - 1)
        self.assertFalse(Category.objects.filter(pk=category_to_delete_pk).exists())
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCategoryList')

        messages = list(get_messages(self.client.get(reverse('category_list')).wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Item Category deleted successfully.')

    def test_delete_view_post_failure_business_rule(self):
        # Temporarily override can_delete for a test category to simulate failure
        original_can_delete = Category.can_delete
        Category.can_delete = lambda self: False
        
        category_to_delete_pk = self.category1.pk
        initial_count = Category.objects.count()
        response = self.client.post(reverse('category_delete', args=[category_to_delete_pk]), HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200) # Still 200 OK because it re-renders the partial with error
        self.assertTemplateUsed(response, 'masters/category/_category_confirm_delete.html')
        self.assertEqual(Category.objects.count(), initial_count) # Not deleted
        self.assertTrue(Category.objects.filter(pk=category_to_delete_pk).exists())
        self.assertContains(response, 'This item category cannot be deleted as it has associated data.')

        # Restore original method
        Category.can_delete = original_can_delete

```

---

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
- **HTMX for Dynamic Content:**
    - The main `list.html` page uses `hx-get` to load the `_category_table.html` partial on `load` and `refreshCategoryList` event.
    - Buttons for "Add", "Edit", and "Delete" use `hx-get` to load forms/confirmation into a modal (`#modalContent`).
    - Form submissions (`_category_form.html`, `_category_confirm_delete.html`) use `hx-post` and `hx-swap="none"` with `HX-Trigger` to notify the main page to refresh the table.
    - `htmx-indicator` class provides visual feedback during loading.
- **Alpine.js for UI State Management:**
    - Alpine.js is used with `_=` attributes for modal state (`add .flex to #modal`, `remove .opacity-100 from #modalOverlay` etc.) to handle opening and closing the modal with CSS transitions. This provides a smoother user experience than just HTMX `hx-swap`.
- **DataTables for List Views:**
    - `DataTables.js` is initialized on the `categoryTable` in `_category_table.html` via a jQuery script.
    - The initialization is placed within an `htmx:afterSwap` event listener in `list.html` to ensure DataTables is re-initialized correctly whenever the table partial is swapped in by HTMX (e.g., after a CRUD operation triggers a refresh).
    - CDN links for DataTables, jQuery, and Font Awesome (for check/times icons) are included in `base.html` (implied) or `list.html`'s `extra_js` block.
- **No Additional JavaScript:** All interactions are handled by HTMX, Alpine.js, or DataTables, adhering to the "no additional JavaScript" rule.
- **DRY Templates:** Use of partial templates (`_category_table.html`, `_category_form.html`, `_category_confirm_delete.html`) ensures reusability and clean structure.

---

### Final Notes

- The `category_id` (CId) in the model is set as `primary_key=True`. When dealing with `managed=False` and existing databases, it's crucial to understand how the database handles this primary key (e.g., as an IDENTITY column for auto-increment). For insertion, if it's an IDENTITY column, Django's `ModelForm` will typically omit it on creation, letting the DB assign a value. Our `CategoryCreateView` implicitly handles this by not providing `category_id` in `form.instance`.
- The `company_id` and `financial_year_id` are derived from the session, mirroring the ASP.NET `SessionParameter` usage. In a production Django application, these would typically be linked to the authenticated user's profile or a selected context.
- The `CategoryForm` excludes `company_id` and `financial_year_id` from its fields as they are session-driven and not directly editable by the user on the form. They are assigned by the view.
- Comprehensive unit and integration tests are provided to ensure the correctness and robustness of the models and views, covering basic CRUD and HTMX interactions. These tests assume session variables `compid` and `finyear` are set.
- Tailwind CSS classes are applied directly within the templates for styling, ensuring a modern and responsive UI.
- The business logic (e.g., `update_category_details`, `create_new_category`, `can_delete`) resides within the `Category` model, maintaining the "fat model, thin view" principle.