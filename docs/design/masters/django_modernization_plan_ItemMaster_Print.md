## ASP.NET to Django Modernization Plan: Item Master Filtering and Reporting

This document outlines a strategic plan to migrate your legacy ASP.NET `ItemMaster_Print.aspx` module to a modern Django application. Our approach leverages AI-assisted automation, focusing on a clean, scalable, and maintainable architecture.

### Business Value Proposition

Migrating this module to Django offers significant benefits:

1.  **Enhanced User Experience:** Replace static iframes and full page postbacks with dynamic, instantaneous updates using HTMX and Alpine.js, providing a smoother, more responsive interface.
2.  **Improved Maintainability:** Transition from complex ASP.NET Web Forms lifecycle and imperative C# code to Django's structured MVC-like pattern, making the codebase easier to understand, extend, and debug.
3.  **Future-Proof Technology:** Adopt a modern, actively supported framework (Django 5.0+) with a vibrant ecosystem, ensuring your application remains secure and relevant for years to come.
4.  **Cost Efficiency:** Automate repetitive migration tasks, reducing manual coding effort and accelerating development cycles, leading to lower operational costs.
5.  **Scalability:** Django's robust architecture and Python's flexibility allow your application to scale efficiently as your business grows.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:

-   NEVER include `base.html` template code in your output - assume it already exists and `extends 'core/base.html'`.
-   Focus ONLY on component-specific code for the current module.
-   Always include complete unit tests for models and integration tests for views.
-   Use modern Django 5.0+ patterns and follow best practices.
-   Keep your code clean, efficient, and avoid redundancy.
-   Always generate complete, runnable Django code.

## AutoERP Guidelines:

-   Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
-   Map models to existing database using `managed = False` and `db_table`.
-   Implement DataTables for client-side searching, sorting, and pagination.
-   Use HTMX for dynamic interactions and Alpine.js for UI state management.
-   All templates should extend `core/base.html` (but DO NOT include `base.html` code).
-   Achieve at least 80% test coverage with unit and integration tests.
-   Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
-   Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

The ASP.NET code interacts with several tables, primarily for filtering and displaying item master data.

*   **`tblDG_Item_Master`**: This is the core table being searched.
    *   Columns inferred: `ItemCode` (likely `CharField`), `ManfDesc` (Description, `CharField`), `Location` (this appears to be a foreign key or ID referring to `tblDG_Location_Master`, so `IntegerField` or `ForeignKey`). Let's assume it's `LocationId` and links to `tblDG_Location_Master`.
*   **`tblDG_Category_Master`**: Used to populate the category dropdown.
    *   Columns inferred: `CId` (Primary Key, `IntegerField`), `Symbol` (`CharField`), `CName` (`CharField`), `CompId` (likely `IntegerField`, representing a company ID for multi-tenancy).
*   **`tblDG_Location_Master`**: Used to populate the location dropdown.
    *   Columns inferred: `Id` (Primary Key, `IntegerField`), `LocationLabel` (`CharField`), `LocationNo` (`CharField`).

### Step 2: Identify Backend Functionality

The ASP.NET page `ItemMaster_Print.aspx` acts as a search and filtering interface for item master data, which then loads a report into an `iframe`.

*   **Read/Filter Operation**: The primary functionality is to allow users to select filtering criteria (Type, Category, Search Code, Search Item Code/Location) and then display a filtered list of items.
*   **Dynamic Dropdown Population**:
    *   The "Type" dropdown (`DrpType`) controls the visibility and content of other filter fields.
    *   If "Category" is selected, the "Category" dropdown (`DrpCategory1`) is populated from `tblDG_Category_Master`.
    *   The "Location" dropdown (`DropDownList3`) is always populated from `tblDG_Location_Master` when `DrpType` is "Category", but its visibility depends on `DrpSearchCode` selection.
    *   The "Search Code" dropdown (`DrpSearchCode`) dynamically switches between a text input (`txtSearchItemCode`) and the "Location" dropdown (`DropDownList3`).
*   **Report Generation**: The `btnSearch_Click` event constructs a URL with filter parameters and assigns it to an `iframe`. In Django, this will be replaced by loading the filtered data directly into a DataTables component via HTMX.
*   **Multi-tenancy**: `CompId` from session indicates data is filtered by company.

### Step 3: Infer UI Components

The ASP.NET controls will be mapped to Django forms and HTML elements with HTMX for dynamic interactions.

*   `asp:DropDownList` (`DrpType`, `DrpCategory1`, `DrpSearchCode`, `DropDownList3`): Will be Django `forms.ChoiceField` or `forms.ModelChoiceField` rendered as `<select>` elements. Their dynamic population and visibility will be handled by HTMX.
*   `asp:TextBox` (`txtSearchItemCode`): Will be a Django `forms.CharField` rendered as `<input type="text">`. Its visibility will also be controlled by HTMX.
*   `asp:Button` (`btnSearch`): Will be a standard `<button>` with HTMX attributes to trigger data loading.
*   `asp:Label` (`lblMessage`): Will be handled by Django's `messages` framework and displayed in the template.
*   `iframe` (`Iframe1`): Will be replaced by a `div` that receives HTMX-loaded content (the filtered DataTables).

### Step 4: Generate Django Code

We will create a Django app named `item_master` to house this functionality.

#### 4.1 Models (`item_master/models.py`)

We'll define models for `Category`, `Location`, and `ItemMaster`, mapping them to the existing database tables. The `CompId` will be represented as `company_id` for categories.

```python
from django.db import models
from django.db.models import F, Q

class Company(models.Model):
    """
    Placeholder for a Company model, assuming CompId links to it.
    Not in original ASP.NET code, but inferred from CompId usage.
    """
    id = models.IntegerField(db_column='CompId', primary_key=True)
    name = models.CharField(max_length=255, db_column='CompanyName')

    class Meta:
        managed = False
        db_table = 'tblCompanyMaster' # Assuming a table name for company
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name


class Category(models.Model):
    """
    Maps to tblDG_Category_Master.
    """
    id = models.IntegerField(db_column='CId', primary_key=True)
    symbol = models.CharField(max_length=50, db_column='Symbol', blank=True, null=True)
    name = models.CharField(max_length=255, db_column='CName')
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'

    def __str__(self):
        return f"[{self.symbol}] - {self.name}" if self.symbol else self.name

    @staticmethod
    def get_filtered_categories(company_id=None):
        """Fetches categories, optionally filtered by company."""
        queryset = Category.objects.all()
        if company_id is not None:
            queryset = queryset.filter(company_id=company_id)
        return queryset.order_by('name')

class Location(models.Model):
    """
    Maps to tblDG_Location_Master.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    label = models.CharField(max_length=255, db_column='LocationLabel')
    number = models.CharField(max_length=50, db_column='LocationNo', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Location_Master'
        verbose_name = 'Location'
        verbose_name_plural = 'Locations'

    def __str__(self):
        return f"{self.label}-{self.number}" if self.number else self.label

    @staticmethod
    def get_all_locations():
        """Fetches all locations."""
        return Location.objects.all().order_by('label')


class ItemMasterManager(models.Manager):
    def filter_items(self, search_type, category_id, search_field, search_value, location_id, company_id):
        """
        Applies filtering logic to ItemMaster objects based on provided criteria.
        This method encapsulates the business logic for item searching.
        """
        queryset = self.get_queryset()

        # Assuming ItemMaster also has a company_id field
        if company_id is not None:
            queryset = queryset.filter(company_id=company_id)

        if search_type == 'Category':
            if category_id:
                queryset = queryset.filter(category_id=category_id)
            
            if search_field == 'tblDG_Item_Master.ItemCode' and search_value:
                queryset = queryset.filter(item_code__icontains=search_value)
            elif search_field == 'tblDG_Item_Master.ManfDesc' and search_value:
                queryset = queryset.filter(description__icontains=search_value)
            elif search_field == 'tblDG_Item_Master.Location' and location_id:
                queryset = queryset.filter(location_id=location_id)
            elif search_field and search_value: # Fallback for unknown search_field for category
                 # Attempt to search across common fields if specific field not matched for 'Category'
                queryset = queryset.filter(
                    Q(item_code__icontains=search_value) |
                    Q(description__icontains=search_value)
                )

        elif search_type == 'WOItems':
            if search_field == 'tblDG_Item_Master.ItemCode' and search_value:
                queryset = queryset.filter(item_code__icontains=search_value)
            elif search_field == 'tblDG_Item_Master.ManfDesc' and search_value:
                queryset = queryset.filter(description__icontains=search_value)
            elif search_field == 'tblDG_Item_Master.Location' and location_id:
                queryset = queryset.filter(location_id=location_id)
            elif search_field and search_value: # Fallback for unknown search_field for WOItems
                queryset = queryset.filter(
                    Q(item_code__icontains=search_value) |
                    Q(description__icontains=search_value)
                )
        
        # Default ordering for display
        return queryset.order_by('item_code')


class ItemMaster(models.Model):
    """
    Maps to tblDG_Item_Master.
    Assumes ItemCode is primary key or unique identifier for the model.
    """
    item_code = models.CharField(db_column='ItemCode', max_length=50, primary_key=True)
    description = models.CharField(db_column='ManfDesc', max_length=500, blank=True, null=True)
    location = models.ForeignKey(Location, on_delete=models.DO_NOTHING, db_column='Location', blank=True, null=True)
    category = models.ForeignKey(Category, on_delete=models.DO_NOTHING, db_column='Category', blank=True, null=True) # Assuming Category field exists
    company_id = models.IntegerField(db_column='CompId', blank=True, null=True) # Assuming CompId exists in ItemMaster

    objects = ItemMasterManager() # Use the custom manager

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.item_code

    # Business logic methods can be added here if needed
```

#### 4.2 Forms (`item_master/forms.py`)

We'll create a non-ModelForm for the filtering options. The choices for `category` and `location` will be dynamically provided, and the form will manage conditional visibility.

```python
from django import forms
from .models import Category, Location

class ItemMasterFilterForm(forms.Form):
    SEARCH_TYPE_CHOICES = [
        ('Select', 'Select'),
        ('Category', 'Category'),
        ('WOItems', 'WO Items'),
    ]

    SEARCH_FIELD_CHOICES = [
        ('Select', 'Select'),
        ('tblDG_Item_Master.ItemCode', 'Item Code'),
        ('tblDG_Item_Master.ManfDesc', 'Description'),
        ('tblDG_Item_Master.Location', 'Location'),
    ]

    search_type = forms.ChoiceField(
        choices=SEARCH_TYPE_CHOICES,
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-9',
            'hx-post': '{{ request.path }}', # Post back to self for form update
            'hx-target': '#filter-form-container',
            'hx-swap': 'outerHTML',
            'hx-vals': '{"is_htmx": "true"}' # Indicate an HTMX request
        }),
        initial='Select',
        label="Type"
    )

    # These fields will be dynamically enabled/disabled/populated
    category = forms.ModelChoiceField(
        queryset=Category.objects.none(), # Populated dynamically
        required=False,
        empty_label="Select",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-9',
        }),
        label="Category"
    )

    search_field = forms.ChoiceField(
        choices=SEARCH_FIELD_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-9',
            'hx-post': '{{ request.path }}', # Post back to self for form update
            'hx-target': '#filter-form-container',
            'hx-swap': 'outerHTML',
            'hx-vals': '{"is_htmx": "true"}' # Indicate an HTMX request
        }),
        initial='Select',
        label="Search By"
    )

    location = forms.ModelChoiceField(
        queryset=Location.objects.none(), # Populated dynamically
        required=False,
        empty_label="Select",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-9',
        }),
        label="Location"
    )

    search_value = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-9',
            'placeholder': 'Enter search value'
        }),
        label="Search Value"
    )

    def __init__(self, *args, **kwargs):
        company_id = kwargs.pop('company_id', None)
        super().__init__(*args, **kwargs)

        # Dynamic queryset for category and location based on form state
        if 'search_type' in self.data:
            selected_type = self.data['search_type']
            if selected_type == 'Category':
                self.fields['category'].queryset = Category.get_filtered_categories(company_id=company_id)
                self.fields['location'].queryset = Location.get_all_locations()
            else:
                self.fields['category'].queryset = Category.objects.none()
                self.fields['location'].queryset = Location.objects.none()
        else:
            # Initial load or invalid form, set empty querysets
            self.fields['category'].queryset = Category.objects.none()
            self.fields['location'].queryset = Location.objects.none()
        
        # Apply initial visibility rules (Django form field won't hide HTML, templates do this)
        # We also need to set initial disabled/enabled state based on conditional logic
        self.fields['category'].widget.attrs['disabled'] = True
        self.fields['search_field'].widget.attrs['disabled'] = True
        self.fields['location'].widget.attrs['disabled'] = True
        self.fields['search_value'].widget.attrs['disabled'] = True

        # When the form is submitted via HTMX, update states based on posted data
        if self.is_bound:
            search_type_val = self.cleaned_data.get('search_type', self.data.get('search_type'))
            search_field_val = self.cleaned_data.get('search_field', self.data.get('search_field'))

            if search_type_val == 'Category':
                self.fields['category'].widget.attrs.pop('disabled')
                self.fields['search_field'].widget.attrs.pop('disabled')
                if search_field_val == 'tblDG_Item_Master.Location':
                    self.fields['location'].widget.attrs.pop('disabled')
                    self.fields['search_value'].widget.attrs['disabled'] = True # Disable text field
                    self.fields['search_value'].widget.attrs['value'] = '' # Clear text value
                else:
                    self.fields['search_value'].widget.attrs.pop('disabled')
                    self.fields['location'].widget.attrs['disabled'] = True # Disable location dropdown
                    self.fields['location'].initial = 'Select' # Clear location value
            elif search_type_val == 'WOItems':
                self.fields['search_field'].widget.attrs.pop('disabled')
                if search_field_val == 'tblDG_Item_Master.Location':
                    self.fields['location'].widget.attrs.pop('disabled')
                    self.fields['search_value'].widget.attrs['disabled'] = True # Disable text field
                    self.fields['search_value'].widget.attrs['value'] = '' # Clear text value
                else:
                    self.fields['search_value'].widget.attrs.pop('disabled')
                    self.fields['location'].widget.attrs['disabled'] = True # Disable location dropdown
                    self.fields['location'].initial = 'Select' # Clear location value
            elif search_type_val == 'Select':
                # Re-initialize form state if "Select" is chosen
                self.fields['category'].queryset = Category.objects.none()
                self.fields['location'].queryset = Location.objects.none()
                self.fields['category'].widget.attrs['disabled'] = True
                self.fields['search_field'].widget.attrs['disabled'] = True
                self.fields['location'].widget.attrs['disabled'] = True
                self.fields['search_value'].widget.attrs['disabled'] = True
                self.data = self.initial # Reset form data


    def clean(self):
        cleaned_data = super().clean()
        search_type = cleaned_data.get('search_type')
        category = cleaned_data.get('category')
        search_field = cleaned_data.get('search_field')
        location = cleaned_data.get('location')
        search_value = cleaned_data.get('search_value')

        if search_type == 'Category':
            if not category: # ASP.NET had RequiredFieldValidator for DrpType (initial value 'Select')
                # This validation is more complex. Original code allows empty category.
                # If we want to strictly require category, add here.
                pass
            
            if search_field == 'tblDG_Item_Master.Location' and not location:
                self.add_error('location', 'Location is required for "Location" search.')
            elif search_field != 'tblDG_Item_Master.Location' and not search_value:
                # Original didn't seem to strictly validate text search empty
                pass

        elif search_type == 'WOItems':
            if search_field == 'tblDG_Item_Master.Location' and not location:
                self.add_error('location', 'Location is required for "Location" search.')
            elif search_field != 'tblDG_Item_Master.Location' and not search_value:
                 # Original didn't seem to strictly validate text search empty
                pass
        
        elif search_type == 'Select':
            # This case means the form is reset or invalid state, no further validation needed.
            # The ASP.NET code redirects to clear the page, we handle this by resetting form.
            pass

        return cleaned_data
```

#### 4.3 Views (`item_master/views.py`)

We'll use a `TemplateView` for the main page and a `View` for the HTMX partial form and table updates.

```python
from django.views.generic import TemplateView, View
from django.http import HttpResponse
from django.shortcuts import render
from django.contrib import messages
from .forms import ItemMasterFilterForm
from .models import ItemMaster, Category, Location

class ItemMasterPrintView(TemplateView):
    template_name = 'item_master/itemmaster_print.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Dummy company ID for demonstration. In a real app, get from request.user or session.
        company_id = 1 
        form = ItemMasterFilterForm(company_id=company_id)
        context['form'] = form
        context['item_masters'] = [] # Initial empty list
        return context

    def post(self, request, *args, **kwargs):
        # This POST is typically for HTMX form updates
        company_id = 1 # Dummy company ID
        form = ItemMasterFilterForm(request.POST, company_id=company_id)
        
        # If it's an HTMX request to update the form itself
        if request.headers.get('HX-Request') and request.POST.get('is_htmx') == 'true':
            # Re-render just the form partial
            return render(request, 'item_master/_itemmaster_filter_form.html', {'form': form})
        
        # If it's a full form submission (though ideally HTMX handles this for results)
        # This path might be less common with pure HTMX for results
        context = self.get_context_data()
        context['form'] = form
        return render(request, self.template_name, context)


class ItemMasterTablePartialView(View):
    def get(self, request, *args, **kwargs):
        company_id = 1 # Dummy company ID
        
        # Extract filter parameters from GET request
        search_type = request.GET.get('search_type', 'Select')
        category_id = request.GET.get('category')
        search_field = request.GET.get('search_field')
        location_id = request.GET.get('location')
        search_value = request.GET.get('search_value')

        if search_type == 'Select':
            # Handle the "Select" case which resets or shows alert in ASP.NET
            # Here, we can return an empty table and a message
            messages.info(request, "Please select a search type (Category or WO Items).")
            item_masters = []
        else:
            try:
                # Convert IDs to integers if they are not None or empty
                category_id = int(category_id) if category_id and category_id != 'Select' else None
                location_id = int(location_id) if location_id and location_id != 'Select' else None

                item_masters = ItemMaster.objects.filter_items(
                    search_type=search_type,
                    category_id=category_id,
                    search_field=search_field,
                    search_value=search_value,
                    location_id=location_id,
                    company_id=company_id
                )
            except Exception as e:
                messages.error(request, f"An error occurred while fetching data: {e}")
                item_masters = []

        context = {'item_masters': item_masters}
        return render(request, 'item_master/_itemmaster_table.html', context)

```

#### 4.4 Templates (`item_master/templates/item_master/`)

We'll have a main page template and two partials for the dynamic form and table.

**`itemmaster_print.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Item Master - Print</h2>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-md mb-8">
        <h3 class="text-lg font-medium text-gray-900 mb-5">Filter Items</h3>
        
        {# Container for the dynamically updated form #}
        <div id="filter-form-container">
            {% include 'item_master/_itemmaster_filter_form.html' %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                id="btn-search"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                hx-get="{% url 'itemmaster_table_partial' %}"
                hx-target="#item-master-table-container"
                hx-trigger="click"
                hx-indicator="#loading-indicator"
                hx-include="#filter-form-container input, #filter-form-container select"
                _="on click add .hidden to #loading-indicator then remove .hidden from #loading-indicator"
            >
                View
            </button>
        </div>
    </div>

    {# Loading indicator for HTMX #}
    <div id="loading-indicator" class="text-center py-4 hidden">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">Loading items...</p>
    </div>

    <div id="item-master-table-container" 
         class="bg-white p-6 rounded-lg shadow-md">
        {# Item master table will be loaded here via HTMX #}
        <p class="text-gray-600">Apply filters and click 'View' to see items.</p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components can be defined here for client-side interactivity not handled by HTMX
        // For example, managing modal states if you had CRUD operations on this page.
    });

    // Ensure DataTables is re-initialized when a partial is swapped in
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'item-master-table-container') {
            // Check if DataTables was loaded in the new content
            const table = document.getElementById('item-master-table');
            if (table && !$.fn.DataTable.isDataTable(table)) {
                $(table).DataTable({
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "destroy": true, // Allow re-initialization
                });
            }
        }
    });
</script>
{% endblock %}
```

**`_itemmaster_filter_form.html` (Partial for form updates)**

```html
<div id="filter-form-container" class="space-y-4">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {# Search Type Dropdown #}
        <div>
            <label for="{{ form.search_type.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.search_type.label }}
            </label>
            {{ form.search_type }}
            {% if form.search_type.errors %}
            <p class="text-red-500 text-xs mt-1">{{ form.search_type.errors }}</p>
            {% endif %}
        </div>

        {# Category Dropdown (conditionally visible/enabled) #}
        <div {% if form.search_type.value != 'Category' %}class="hidden"{% endif %}>
            <label for="{{ form.category.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.category.label }}
            </label>
            {{ form.category }}
            {% if form.category.errors %}
            <p class="text-red-500 text-xs mt-1">{{ form.category.errors }}</p>
            {% endif %}
        </div>

        {# Search Field Dropdown (conditionally visible/enabled) #}
        <div {% if form.search_type.value == 'Select' %}class="hidden"{% endif %}>
            <label for="{{ form.search_field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.search_field.label }}
            </label>
            {{ form.search_field }}
            {% if form.search_field.errors %}
            <p class="text-red-500 text-xs mt-1">{{ form.search_field.errors }}</p>
            {% endif %}
        </div>

        {# Search Value (Text Input) or Location Dropdown (conditionally visible/enabled) #}
        {% if form.search_field.value == 'tblDG_Item_Master.Location' %}
            {# Location Dropdown #}
            <div {% if form.search_type.value == 'Select' %}class="hidden"{% endif %}>
                <label for="{{ form.location.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.location.label }}
                </label>
                {{ form.location }}
                {% if form.location.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.location.errors }}</p>
                {% endif %}
            </div>
        {% else %}
            {# Search Value Text Input #}
            <div {% if form.search_type.value == 'Select' or form.search_field.value == 'Select' %}class="hidden"{% endif %}>
                <label for="{{ form.search_value.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.search_value.label }}
                </label>
                {{ form.search_value }}
                {% if form.search_value.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.search_value.errors }}</p>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>
```

**`_itemmaster_table.html` (Partial for results table)**

```html
<div class="overflow-x-auto">
    <table id="item-master-table" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for item in item_masters %}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.item_code }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.description }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.location }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.category }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5" class="py-4 px-4 text-center text-sm text-gray-500">
                    No items found matching the criteria.
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

{# DataTables initialization script, runs when this partial is swapped in #}
<script>
    // Ensure DataTables is initialized only once per table, and destroy previous instances
    $(document).ready(function() {
        const table = $('#item-master-table');
        if ($.fn.DataTable.isDataTable(table)) {
            table.DataTable().destroy();
        }
        table.DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
        });
    });
</script>
```

#### 4.5 URLs (`item_master/urls.py`)

Define the URL patterns for the main page and the HTMX partials.

```python
from django.urls import path
from .views import ItemMasterPrintView, ItemMasterTablePartialView

urlpatterns = [
    path('item-master/print/', ItemMasterPrintView.as_view(), name='itemmaster_print'),
    path('item-master/print/table/', ItemMasterTablePartialView.as_view(), name='itemmaster_table_partial'),
    # Note: The form update is handled by POSTing to itemmaster_print itself,
    # and it returns the _itemmaster_filter_form.html partial.
]
```

#### 4.6 Tests (`item_master/tests.py`)

Comprehensive tests for models and views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import Company, Category, Location, ItemMaster

class ItemMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dummy company
        cls.company1 = Company.objects.create(id=1, name='Test Company A')
        cls.company2 = Company.objects.create(id=2, name='Test Company B')

        # Create test categories
        cls.cat1 = Category.objects.create(id=1, symbol='CAT-A', name='Category A', company=cls.company1)
        cls.cat2 = Category.objects.create(id=2, symbol='CAT-B', name='Category B', company=cls.company1)
        cls.cat3 = Category.objects.create(id=3, symbol='CAT-C', name='Category C', company=cls.company2)

        # Create test locations
        cls.loc1 = Location.objects.create(id=101, label='Warehouse', number='1')
        cls.loc2 = Location.objects.create(id=102, label='Store', number='2')

        # Create test ItemMasters
        ItemMaster.objects.create(
            item_code='ITEM001', description='Widget A', location=cls.loc1, category=cls.cat1, company_id=cls.company1.id
        )
        ItemMaster.objects.create(
            item_code='ITEM002', description='Gadget B', location=cls.loc2, category=cls.cat1, company_id=cls.company1.id
        )
        ItemMaster.objects.create(
            item_code='ITEM003', description='Tool C', location=cls.loc1, category=cls.cat2, company_id=cls.company1.id
        )
        ItemMaster.objects.create(
            item_code='ITEM004', description='Component D', location=cls.loc2, category=cls.cat3, company_id=cls.company2.id
        )

    def test_category_creation(self):
        cat = Category.objects.get(id=1)
        self.assertEqual(cat.name, 'Category A')
        self.assertEqual(str(cat), '[CAT-A] - Category A')

    def test_location_creation(self):
        loc = Location.objects.get(id=101)
        self.assertEqual(loc.label, 'Warehouse')
        self.assertEqual(str(loc), 'Warehouse-1')

    def test_itemmaster_creation(self):
        item = ItemMaster.objects.get(item_code='ITEM001')
        self.assertEqual(item.description, 'Widget A')
        self.assertEqual(item.location, self.loc1)
        self.assertEqual(item.category, self.cat1)

    def test_itemmaster_filter_by_category(self):
        items = ItemMaster.objects.filter_items(
            search_type='Category', category_id=self.cat1.id, search_field=None, search_value=None, location_id=None, company_id=self.company1.id
        )
        self.assertEqual(items.count(), 2)
        self.assertIn(ItemMaster.objects.get(item_code='ITEM001'), items)
        self.assertIn(ItemMaster.objects.get(item_code='ITEM002'), items)

    def test_itemmaster_filter_by_item_code(self):
        items = ItemMaster.objects.filter_items(
            search_type='WOItems', category_id=None, search_field='tblDG_Item_Master.ItemCode', search_value='ITEM001', location_id=None, company_id=self.company1.id
        )
        self.assertEqual(items.count(), 1)
        self.assertEqual(items.first().item_code, 'ITEM001')

    def test_itemmaster_filter_by_description(self):
        items = ItemMaster.objects.filter_items(
            search_type='Category', category_id=self.cat1.id, search_field='tblDG_Item_Master.ManfDesc', search_value='gadget', location_id=None, company_id=self.company1.id
        )
        self.assertEqual(items.count(), 1)
        self.assertEqual(items.first().item_code, 'ITEM002')

    def test_itemmaster_filter_by_location(self):
        items = ItemMaster.objects.filter_items(
            search_type='Category', category_id=self.cat2.id, search_field='tblDG_Item_Master.Location', search_value=None, location_id=self.loc1.id, company_id=self.company1.id
        )
        self.assertEqual(items.count(), 1)
        self.assertEqual(items.first().item_code, 'ITEM003')
        
    def test_itemmaster_filter_by_company(self):
        items = ItemMaster.objects.filter_items(
            search_type='Category', category_id=None, search_field=None, search_value=None, location_id=None, company_id=self.company2.id
        )
        self.assertEqual(items.count(), 1)
        self.assertEqual(items.first().item_code, 'ITEM004')


class ItemMasterViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dummy company
        cls.company1 = Company.objects.create(id=1, name='Test Company A')
        # Create test categories and locations as needed for forms
        cls.cat1 = Category.objects.create(id=1, symbol='CAT-A', name='Category A', company=cls.company1)
        cls.loc1 = Location.objects.create(id=101, label='Warehouse', number='1')
        ItemMaster.objects.create(item_code='ITEM001', description='Test Item', location=cls.loc1, category=cls.cat1, company_id=cls.company1.id)

    def setUp(self):
        self.client = Client()

    def test_print_view_get(self):
        response = self.client.get(reverse('itemmaster_print'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'item_master/itemmaster_print.html')
        self.assertContains(response, 'Item Master - Print')
        self.assertIn('form', response.context)

    def test_filter_form_update_htmx(self):
        # Simulate HTMX POST for search_type change
        response = self.client.post(
            reverse('itemmaster_print'),
            data={'search_type': 'Category', 'is_htmx': 'true'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'item_master/_itemmaster_filter_form.html')
        self.assertContains(response, 'name="category"') # Check if category dropdown is present/enabled

        # Simulate HTMX POST for search_field change within 'Category' type
        response = self.client.post(
            reverse('itemmaster_print'),
            data={'search_type': 'Category', 'search_field': 'tblDG_Item_Master.Location', 'is_htmx': 'true'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'item_master/_itemmaster_filter_form.html')
        self.assertContains(response, 'name="location"') # Check if location dropdown is present/enabled
        self.assertNotContains(response, 'name="search_value"') # Check if search_value is hidden

    def test_table_partial_view_get_no_filters(self):
        response = self.client.get(reverse('itemmaster_table_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'item_master/_itemmaster_table.html')
        self.assertContains(response, 'No items found matching the criteria.') # Initial state with no filters

    def test_table_partial_view_get_with_filters(self):
        # Prepare filters for a known item
        data = {
            'search_type': 'Category',
            'category': self.cat1.id,
            'search_field': 'tblDG_Item_Master.ItemCode',
            'search_value': 'ITEM001',
            'location': '', # Not used for this filter combo
        }
        response = self.client.get(reverse('itemmaster_table_partial'), data)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'item_master/_itemmaster_table.html')
        self.assertContains(response, 'ITEM001') # Check if the item is displayed
        self.assertNotContains(response, 'No items found')

    def test_table_partial_view_invalid_filters(self):
        # Simulate "Select" type chosen
        data = {'search_type': 'Select'}
        response = self.client.get(reverse('itemmaster_table_partial'), data)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'item_master/_itemmaster_table.html')
        self.assertContains(response, 'No items found') # Should return empty or info message
```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for Dynamic Forms:** The `hx-post` attributes on the `search_type` and `search_field` dropdowns trigger a POST request to the `ItemMasterPrintView`. This view detects the HTMX request and renders only the `_itemmaster_filter_form.html` partial, which includes the logic for showing/hiding/disabling fields based on the selected values. The `hx-swap="outerHTML"` ensures the entire form container is replaced.
*   **HTMX for Search Results:** The "View" button uses `hx-get` to request data from `itemmaster_table_partial`. It includes all current form values in the GET request (`hx-include`). The `hx-target` is set to `#item-master-table-container`, and `hx-indicator` provides visual feedback during loading.
*   **DataTables:** The `_itemmaster_table.html` partial includes the JavaScript for DataTables initialization. An `htmx:afterSwap` event listener ensures that DataTables is re-initialized whenever the table partial is loaded into the `item-master-table-container` div, handling dynamic content.
*   **Alpine.js:** While not strictly necessary for this specific conditional rendering (HTMX handles it well), Alpine.js could be used for minor client-side UI states like toggling a loading spinner or managing modal states if this page had CRUD forms. For this example, HTMX's `hx-indicator` covers the loading feedback.

### Final Notes

*   **Placeholders:** Replace `company_id = 1` with logic to retrieve the actual company ID from the authenticated user or session in a real application.
*   **Error Handling:** The current views include basic `try-except` blocks and `messages` for user feedback. Robust error handling (e.g., detailed logging, custom error pages) should be implemented.
*   **Security:** Ensure proper authentication and authorization are in place for the `ItemMasterPrintView` and associated data access.
*   **Scalability:** For very large datasets, consider server-side processing for DataTables instead of client-side, which can be integrated with Django Rest Framework.
*   **Test Coverage:** Aim for higher than 80% test coverage, particularly for the filtering logic within the `ItemMasterManager`.