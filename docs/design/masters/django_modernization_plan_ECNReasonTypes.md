## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

- The `SqlDataSource1` component provides direct SQL commands for SELECT, INSERT, UPDATE, and DELETE operations.
- From these commands, we can clearly identify the table and its columns.

## Analysis:

- **TABLE_NAME:** `tblDG_ECN_Reason`
- **Columns:**
    - `Id`: Used in `WHERE` clauses for UPDATE/DELETE, `SELECT` list. Likely the primary key.
    - `Types`: Used in `INSERT`, `UPDATE`, `SELECT` list. This is the description field.
    - `CompId`: Used in `WHERE` clauses for SELECT/DELETE/UPDATE and `INSERT` list. This is a company identifier, retrieved from the session in ASP.NET. In Django, this will likely be a foreign key or a context-based filter.

### Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

- Analyze the `SqlDataSource` commands and the `GridView` event handlers in the C# code-behind.

## Analysis:

- **Create:** The `GridView2_RowCommand` method handles `CommandName="Add"` (from the footer template) and `CommandName="Add1"` (from the `EmptyDataTemplate`). Both execute `SqlDataSource1.Insert()`. This is a Create operation.
- **Read:** The `GridView2` is bound to `SqlDataSource1` using its `SelectCommand`. This populates the grid with existing data. This is a Read operation. Note the `CompId` filter in the `SelectCommand`.
- **Update:** The `GridView2_RowCommand` method handles `CommandName="Update"`. This executes `SqlDataSource1.Update()`. This is an Update operation.
- **Delete:** The `GridView2_RowDeleted` event is hooked up, and `SqlDataSource1` defines a `DeleteCommand`. This is a Delete operation.
- **Validation:** `RequiredFieldValidator` controls are used in the ASPX for the `Types` field, ensuring it's not empty on insertion. This will be handled by Django's form validation.
- **`CompId` Logic:** The `CompId` is passed to all SQL commands via `SessionParameter`. This indicates that records are tied to a specific company context.

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

- Examine the ASPX markup to understand how data is presented and user input is captured.

## Analysis:

- **GridView2:** This is the primary component for displaying data lists. It supports paging, editing, and deleting rows directly within the grid. This will be replaced by a modern HTML table, enhanced with `DataTables.js` for client-side functionality.
- **TextBox (txtTypes, txtTypes0):** Used for inputting the 'Description' (`Types`) field, both for new entries (in footer/empty template) and for editing existing ones. These will become Django form `TextInput` widgets.
- **Button (btnInsert1, btnInsert0), LinkButton (Edit, Delete command fields):** These trigger the Create, Update, and Delete actions. In Django, these will be standard HTML buttons/links with `HTMX` attributes to trigger dynamic requests.
- **Label (lblmsg):** Used for displaying success/error messages. This will be handled by Django's `messages` framework, rendered on the base template.
- **JavaScript (`PopUpMsg.js`, `loadingNotifier.js`):** The `OnClientClick` attributes on buttons suggest client-side confirmations (`confirmationAdd()`, `confirmationUpdate()`, `confirmationDelete()`). These can be elegantly handled using HTMX's `hx-confirm` attribute or simple Alpine.js modals for a more custom UI.

### Step 4: Generate Django Code

Based on the analysis, we will define an application named `design_masters` within the Django project.

**App Name:** `design_masters`
**Model Name:** `ECNReasonType`
**Model Name Lowercase:** `ecnreasontype`
**Model Name Plural:** `ECN Reason Types`
**Model Name Plural Lowercase:** `ecnreasontypes`

### 4.1 Models (`design_masters/models.py`)

This model will represent the `tblDG_ECN_Reason` table. We use `managed = False` as Django will connect to an existing database table. `CompId` will be an `IntegerField`, as it's a critical filtering and insertion parameter from the original code.

```python
from django.db import models

class ECNReasonType(models.Model):
    # Id is the primary key and corresponds to the 'Id' column in the database
    id = models.IntegerField(db_column='Id', primary_key=True)
    
    # Types corresponds to the 'Types' column (Description)
    types = models.CharField(db_column='Types', max_length=255) # Assuming a reasonable max length for 'Types'
    
    # CompId corresponds to the 'CompId' column (Company ID)
    compid = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False  # Django will not manage table creation/deletion
        db_table = 'tblDG_ECN_Reason' # The actual table name in the database
        verbose_name = 'ECN Reason Type'
        verbose_name_plural = 'ECN Reason Types'

    def __str__(self):
        """Returns the 'Types' field as the string representation of the object."""
        return self.types
        
    @classmethod
    def get_all_for_company(cls, comp_id):
        """
        Retrieves all ECNReasonType objects for a given company ID.
        This mirrors the 'Where CompId=@CompId' from the original SELECT command.
        """
        return cls.objects.filter(compid=comp_id).order_by('id')

    def update_description(self, new_types):
        """
        Updates the 'Types' (description) of the ECNReasonType.
        This encapsulates the update logic.
        """
        self.types = new_types
        self.save()

    @classmethod
    def create_new_reason(cls, types, comp_id):
        """
        Creates a new ECN Reason Type entry for a specific company.
        This encapsulates the insert logic.
        """
        # In a real scenario, 'Id' might be auto-incrementing; if not, handle ID generation.
        # For 'managed=False', we rely on the existing DB to handle ID if it's auto-increment.
        # If 'Id' is not auto-increment, you'd need to generate a unique ID here (e.g., max_id + 1)
        # For simplicity, assuming the database handles 'Id' on insert or it's not a required field for `create`.
        # However, the ASP.NET `SqlDataSource` `InsertCommand` does not specify `Id`, implying it's identity.
        # So, we omit `id` from the create.
        return cls.objects.create(types=types, compid=comp_id)

    def delete_reason(self):
        """
        Deletes the ECN Reason Type.
        This encapsulates the delete logic.
        """
        self.delete()

```

### 4.2 Forms (`design_masters/forms.py`)

A Django ModelForm will handle the input validation and data binding for the `Types` field. The `CompId` is handled at the view/model level and not directly exposed to the user in the form.

```python
from django import forms
from .models import ECNReasonType

class ECNReasonTypeForm(forms.ModelForm):
    class Meta:
        model = ECNReasonType
        fields = ['types'] # Only 'types' is directly editable by the user
        widgets = {
            'types': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
                'placeholder': 'Enter ECN Reason Description'
            }),
        }
        labels = {
            'types': 'Description'
        }
        
    def clean_types(self):
        """
        Custom validation for the 'types' field to ensure it's not empty,
        mirroring the RequiredFieldValidator.
        """
        types = self.cleaned_data.get('types')
        if not types:
            raise forms.ValidationError("Description cannot be empty.")
        return types
```

### 4.3 Views (`design_masters/views.py`)

We'll use Django's Class-Based Views (CBVs) for efficiency and separation of concerns. `CompId` will be retrieved from the request (assuming a `compid` is stored in the session or accessible via the authenticated user). Views remain thin, delegating logic to the model.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from django.template.loader import render_to_string
from .models import ECNReasonType
from .forms import ECNReasonTypeForm

# Helper function to get company ID from session (replace with actual logic if user-based)
def get_current_company_id(request):
    """
    Retrieves the current company ID from the session.
    Placeholder: In a real ERP, this would come from the logged-in user's profile
    or a validated session variable. Defaulting to 1 for demonstration.
    """
    return request.session.get('compid', 1) # Default to 1 for testing purposes

class ECNReasonTypeListView(ListView):
    model = ECNReasonType
    template_name = 'design_masters/ecnreasontype/list.html'
    context_object_name = 'ecn_reason_types' # Renamed for clarity

    def get_queryset(self):
        """
        Filters the queryset based on the company ID.
        This ensures users only see data for their associated company.
        """
        comp_id = get_current_company_id(self.request)
        return ECNReasonType.get_all_for_company(comp_id)

# View for HTMX-loaded table content
class ECNReasonTypeTablePartialView(ECNReasonTypeListView):
    template_name = 'design_masters/ecnreasontype/_ecnreasontype_table.html'

class ECNReasonTypeCreateView(CreateView):
    model = ECNReasonType
    form_class = ECNReasonTypeForm
    template_name = 'design_masters/ecnreasontype/_ecnreasontype_form.html'
    success_url = reverse_lazy('ecnreasontype_list')

    def form_valid(self, form):
        comp_id = get_current_company_id(self.request)
        # Delegate object creation to a model manager or class method
        ECNReasonType.create_new_reason(types=form.cleaned_data['types'], comp_id=comp_id)
        
        messages.success(self.request, 'ECN Reason Type added successfully.')
        
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content response
            # and trigger a refresh of the list on the client side
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshECNReasonTypeList' # Custom event to refresh the table
                }
            )
        return super().form_valid(form)

class ECNReasonTypeUpdateView(UpdateView):
    model = ECNReasonType
    form_class = ECNReasonTypeForm
    template_name = 'design_masters/ecnreasontype/_ecnreasontype_form.html'
    success_url = reverse_lazy('ecnreasontype_list')

    def get_object(self, queryset=None):
        """
        Ensures that the object being updated belongs to the current company.
        """
        comp_id = get_current_company_id(self.request)
        return get_object_or_404(ECNReasonType, pk=self.kwargs['pk'], compid=comp_id)

    def form_valid(self, form):
        # Delegate update logic to a model method
        form.instance.update_description(form.cleaned_data['types'])
        
        messages.success(self.request, 'ECN Reason Type updated successfully.')
        
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshECNReasonTypeList'
                }
            )
        return super().form_valid(form)

class ECNReasonTypeDeleteView(DeleteView):
    model = ECNReasonType
    template_name = 'design_masters/ecnreasontype/_ecnreasontype_confirm_delete.html'
    success_url = reverse_lazy('ecnreasontype_list')

    def get_object(self, queryset=None):
        """
        Ensures that the object being deleted belongs to the current company.
        """
        comp_id = get_current_company_id(self.request)
        return get_object_or_404(ECNReasonType, pk=self.kwargs['pk'], compid=comp_id)

    def delete(self, request, *args, **kwargs):
        # Delegate deletion logic to a model method
        self.get_object().delete_reason()
        
        messages.success(self.request, 'ECN Reason Type deleted successfully.')
        
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshECNReasonTypeList'
                }
            )
        return super().delete(request, *args, **kwargs)

```

### 4.4 Templates

Templates are designed for HTMX partial loading. `list.html` is the main entry point, and other templates are partials (`_*.html`) intended to be loaded into modals or specific `div`s via HTMX.

**`design_masters/templates/design_masters/ecnreasontype/list.html`**
This template sets up the main page, including the modal structure and the container for the DataTables content, which will be loaded via HTMX.

```html
{% extends 'core/base.html' %}

{% block title %}ECN Reason Types{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">ECN Reason Types</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
            hx-get="{% url 'ecnreasontype_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus-circle mr-2"></i>Add New Reason
        </button>
    </div>

    <div id="ecnreasontypeTable-container"
         hx-trigger="load, refreshECNReasonTypeList from:body" {# Loads on page load, refreshes on custom HTMX event #}
         hx-get="{% url 'ecnreasontype_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg p-4">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading ECN Reason Types...</p>
        </div>
    </div>

    <!-- Modal for forms/confirmations -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-xl w-full mx-4 sm:mx-0">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Example Alpine.js for general UI control, if needed beyond basic modal toggle
    document.addEventListener('alpine:init', () => {
        Alpine.data('modalController', () => ({
            showModal: false,
            open() { this.showModal = true },
            close() { this.showModal = false },
        }));
    });

    // Handle HTMX events for modal closing after a successful form submission
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'modalContent' && event.detail.xhr.status === 204) {
            // This indicates a successful form submission (204 No Content)
            // and the content area was swapped to 'none' or empty.
            // Close the modal.
            document.getElementById('modal').classList.remove('is-active');
        }
    });

    // Re-initialize DataTables after HTMX loads new table content
    document.body.addEventListener('htmx:afterSettle', function(event) {
        if (event.detail.target.id === 'ecnreasontypeTable-container') {
            $('#ecnreasontypeTable').DataTable({
                "pageLength": 20, // Matches original ASP.NET PageSize
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "responsive": true,
                "searching": true,
                "paging": true,
                "info": true
            });
        }
    });
</script>
{% endblock %}
```

**`design_masters/templates/design_masters/ecnreasontype/_ecnreasontype_table.html`**
This partial template renders only the table, designed to be swapped into the `ecnreasontypeTable-container` via HTMX.

```html
<div class="overflow-x-auto">
    <table id="ecnreasontypeTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in ecn_reason_types %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.types }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm font-medium">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 shadow-sm transition duration-150 ease-in-out"
                        hx-get="{% url 'ecnreasontype_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md shadow-sm transition duration-150 ease-in-out"
                        hx-get="{% url 'ecnreasontype_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal"
                        hx-confirm="Are you sure you want to delete this ECN Reason Type?"> {# HTMX confirmation #}
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="3" class="py-4 px-6 text-center text-gray-500">No ECN Reason Types found. Click "Add New Reason" to add one.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // DataTables initialization is handled by the htmx:afterSettle event listener in list.html
    // This script block ensures the DataTables function is available if the partial is loaded directly for testing.
</script>
```

**`design_masters/templates/design_masters/ecnreasontype/_ecnreasontype_form.html`**
This partial template is used for both Create and Update operations, rendered into a modal.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} ECN Reason Type</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div class="flex flex-col">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="text-red-600 text-sm mt-2">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
            
            {% if form.non_field_errors %}
            <ul class="text-red-600 text-sm mt-2">
                {% for error in form.non_field_errors %}
                <li>{{ error }}</li>
                {% endfor %}
            </ul>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Save ECN Reason
            </button>
        </div>
    </form>
</div>
```

**`design_masters/templates/design_masters/ecnreasontype/_ecnreasontype_confirm_delete.html`**
This partial template provides a confirmation dialog for deletions, loaded into a modal.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the ECN Reason Type: <strong>{{ object.types }}</strong>?</p>
    
    <form hx-post="{% url 'ecnreasontype_delete' object.pk %}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Confirm Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs (`design_masters/urls.py`)

These URL patterns map to the views and define the endpoints for our CRUD operations and HTMX partials.

```python
from django.urls import path
from .views import (
    ECNReasonTypeListView,
    ECNReasonTypeCreateView,
    ECNReasonTypeUpdateView,
    ECNReasonTypeDeleteView,
    ECNReasonTypeTablePartialView
)

urlpatterns = [
    # Main list view (initially loads the page shell)
    path('ecn-reason-types/', ECNReasonTypeListView.as_view(), name='ecnreasontype_list'),
    
    # HTMX endpoint for the table content (loaded into list.html)
    path('ecn-reason-types/table/', ECNReasonTypeTablePartialView.as_view(), name='ecnreasontype_table'),

    # HTMX endpoint for the add form (loaded into modal)
    path('ecn-reason-types/add/', ECNReasonTypeCreateView.as_view(), name='ecnreasontype_add'),
    
    # HTMX endpoint for the edit form (loaded into modal)
    path('ecn-reason-types/edit/<int:pk>/', ECNReasonTypeUpdateView.as_view(), name='ecnreasontype_edit'),
    
    # HTMX endpoint for the delete confirmation (loaded into modal)
    path('ecn-reason-types/delete/<int:pk>/', ECNReasonTypeDeleteView.as_view(), name='ecnreasontype_delete'),
]

```

### 4.6 Tests (`design_masters/tests.py`)

Comprehensive tests for both model logic and view interactions, ensuring data integrity and correct HTMX behavior.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from .models import ECNReasonType
from .views import get_current_company_id # Import the helper to mock it if necessary

class ECNReasonTypeModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Setup run once for all test methods in this class
        ECNReasonType.objects.create(id=101, types='Production Issue', compid=1)
        ECNReasonType.objects.create(id=102, types='Design Change', compid=1)
        ECNReasonType.objects.create(id=201, types='Supplier Quality', compid=2) # Different company

    def test_ecnreasontype_creation(self):
        obj = ECNReasonType.objects.get(id=101)
        self.assertEqual(obj.types, 'Production Issue')
        self.assertEqual(obj.compid, 1)

    def test_types_label(self):
        obj = ECNReasonType.objects.get(id=101)
        field_label = obj._meta.get_field('types').verbose_name
        self.assertEqual(field_label, 'types') # Default verbose name is field name

    def test_str_method(self):
        obj = ECNReasonType.objects.get(id=101)
        self.assertEqual(str(obj), 'Production Issue')

    def test_get_all_for_company_method(self):
        reasons_comp1 = ECNReasonType.get_all_for_company(1)
        self.assertEqual(len(reasons_comp1), 2)
        self.assertIn(ECNReasonType.objects.get(id=101), reasons_comp1)
        self.assertIn(ECNReasonType.objects.get(id=102), reasons_comp1)
        
        reasons_comp2 = ECNReasonType.get_all_for_company(2)
        self.assertEqual(len(reasons_comp2), 1)
        self.assertIn(ECNReasonType.objects.get(id=201), reasons_comp2)

    def test_update_description_method(self):
        obj = ECNReasonType.objects.get(id=101)
        obj.update_description('New Production Issue')
        obj.refresh_from_db()
        self.assertEqual(obj.types, 'New Production Issue')

    def test_create_new_reason_method(self):
        new_reason = ECNReasonType.create_new_reason(types='Software Bug', comp_id=1)
        self.assertIsNotNone(new_reason.id)
        self.assertEqual(new_reason.types, 'Software Bug')
        self.assertEqual(new_reason.compid, 1)

    def test_delete_reason_method(self):
        obj = ECNReasonType.objects.get(id=101)
        obj.delete_reason()
        self.assertFalse(ECNReasonType.objects.filter(id=101).exists())


class ECNReasonTypeViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for views. Assuming default company ID is 1 for tests.
        ECNReasonType.objects.create(id=101, types='Production Issue', compid=1)
        ECNReasonType.objects.create(id=102, types='Design Change', compid=1)
        ECNReasonType.objects.create(id=201, types='Supplier Quality', compid=2)

    def setUp(self):
        self.client = Client()
        # Mock the session's compid for all tests to be consistent.
        # In a real app, this would be set by a login or middleware.
        session = self.client.session
        session['compid'] = 1
        session.save()
        
    def test_list_view_get(self):
        response = self.client.get(reverse('ecnreasontype_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_masters/ecnreasontype/list.html')
        self.assertIn('ecn_reason_types', response.context)
        # Should only see records for compid=1
        self.assertEqual(len(response.context['ecn_reason_types']), 2)
        self.assertContains(response, 'Production Issue')
        self.assertNotContains(response, 'Supplier Quality') # From compid=2

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('ecnreasontype_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_masters/ecnreasontype/_ecnreasontype_table.html')
        self.assertIn('ecn_reason_types', response.context)
        self.assertEqual(len(response.context['ecn_reason_types']), 2)
        self.assertContains(response, 'Production Issue')
        self.assertContains(response, '<table id="ecnreasontypeTable"') # Check for DataTables ID

    def test_create_view_get(self):
        response = self.client.get(reverse('ecnreasontype_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_masters/ecnreasontype/_ecnreasontype_form.html')
        self.assertIn('form', response.context)

    def test_create_view_post_success(self):
        data = {'types': 'New Feature Request'}
        response = self.client.post(reverse('ecnreasontype_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content expected for HTMX success
        self.assertTrue(ECNReasonType.objects.filter(types='New Feature Request', compid=1).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshECNReasonTypeList')

        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'ECN Reason Type added successfully.')

    def test_create_view_post_invalid(self):
        data = {'types': ''} # Invalid data
        response = self.client.post(reverse('ecnreasontype_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'design_masters/ecnreasontype/_ecnreasontype_form.html')
        self.assertIn('form', response.context)
        self.assertIn('Description cannot be empty.', response.content.decode())
        self.assertFalse(ECNReasonType.objects.filter(types='').exists())


    def test_update_view_get(self):
        obj = ECNReasonType.objects.get(id=101)
        response = self.client.get(reverse('ecnreasontype_edit', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_masters/ecnreasontype/_ecnreasontype_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, obj)

    def test_update_view_post_success(self):
        obj = ECNReasonType.objects.get(id=101)
        data = {'types': 'Updated Production Issue'}
        response = self.client.post(reverse('ecnreasontype_edit', args=[obj.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        obj.refresh_from_db()
        self.assertEqual(obj.types, 'Updated Production Issue')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshECNReasonTypeList')

        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'ECN Reason Type updated successfully.')

    def test_update_view_post_invalid(self):
        obj = ECNReasonType.objects.get(id=101)
        data = {'types': ''}
        response = self.client.post(reverse('ecnreasontype_edit', args=[obj.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_masters/ecnreasontype/_ecnreasontype_form.html')
        self.assertIn('form', response.context)
        self.assertIn('Description cannot be empty.', response.content.decode())

    def test_update_view_access_other_company_data(self):
        obj_other_company = ECNReasonType.objects.get(id=201)
        response = self.client.get(reverse('ecnreasontype_edit', args=[obj_other_company.id]))
        self.assertEqual(response.status_code, 404) # Should return 404 if not found for current company

    def test_delete_view_get(self):
        obj = ECNReasonType.objects.get(id=101)
        response = self.client.get(reverse('ecnreasontype_delete', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_masters/ecnreasontype/_ecnreasontype_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], obj)

    def test_delete_view_post_success(self):
        obj_id = ECNReasonType.objects.get(id=101).id
        response = self.client.post(reverse('ecnreasontype_delete', args=[obj_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(ECNReasonType.objects.filter(id=obj_id).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshECNReasonTypeList')

        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'ECN Reason Type deleted successfully.')

    def test_delete_view_access_other_company_data(self):
        obj_other_company = ECNReasonType.objects.get(id=201)
        response = self.client.post(reverse('ecnreasontype_delete', args=[obj_other_company.id]))
        self.assertEqual(response.status_code, 404)

```

### Step 5: HTMX and Alpine.js Integration

The provided templates and views are already designed with HTMX and Alpine.js in mind:

-   **HTMX for Dynamic Updates:**
    -   The main `list.html` uses `hx-get` to load the `_ecnreasontype_table.html` partial on page load and whenever a `refreshECNReasonTypeList` event is triggered (after CUD operations).
    -   Add, Edit, and Delete buttons use `hx-get` to fetch their respective form/confirmation partials into the `#modalContent` div.
    -   Forms use `hx-post` to submit data asynchronously. Upon successful submission (204 No Content response), `HX-Trigger` causes the list to refresh.
    -   `hx-confirm` is used directly on the delete button for client-side confirmation, mimicking the original `OnClientClick` JavaScript.
    -   `htmx:afterSettle` event listener in `list.html` ensures DataTables is re-initialized after HTMX loads new table content.

-   **Alpine.js for UI State:**
    -   A simple `on click` directive is used to add/remove the `is-active` class to the `#modal` element, managing its visibility. This is a common and clean way to handle modal toggling without complex JavaScript.

-   **DataTables for List Views:**
    -   The `_ecnreasontype_table.html` partial contains a standard HTML table with `id="ecnreasontypeTable"`.
    -   The `list.html` includes JavaScript to initialize DataTables on this ID, specifically listening for the `htmx:afterSettle` event to re-apply DataTables after HTMX swaps in new content. This ensures full client-side search, sort, and pagination capabilities.
    -   The `pageLength` is set to `20` to match the original `GridView`'s `PageSize`.

### Final Notes

This comprehensive plan systematically converts the ASP.NET page's functionality to a modern Django application.

-   **Placeholders:** All `[MODEL_NAME]`, `[FIELD]` etc. placeholders have been replaced with concrete names (`ECNReasonType`, `types`, `compid`).
-   **DRY Templates:** Achieved by using partial templates (`_*.html`) for reusable components (table, form, confirmation) that are loaded dynamically.
-   **Fat Models, Thin Views:** Business logic related to filtering (`get_all_for_company`), creating (`create_new_reason`), updating (`update_description`), and deleting (`delete_reason`) records has been moved into the `ECNReasonType` model or its manager. Views primarily handle HTTP requests, form validation, and delegating to the model, staying compact.
-   **Tests:** Extensive unit tests for the model and integration tests for all view interactions are provided, covering success, validation, and HTMX-specific behaviors, ensuring high code coverage.
-   **HTMX/Alpine.js:** The entire solution emphasizes dynamic, partial updates using HTMX, reducing page reloads and providing a smoother user experience, while Alpine.js handles simple UI state for modals.
-   **Company ID (`CompId`):** The logic for handling `CompId` is implemented in the views and models to mirror the ASP.NET session parameter, ensuring data segregation by company. For a production system, this would typically involve a more robust authentication and authorization system.