## ASP.NET to Django Conversion Script: Item History (BOM) View

This modernization plan outlines the transformation of the ASP.NET Item History (BOM) View into a robust, modern Django application. Our approach leverages Django's powerful ORM, combined with HTMX and Alpine.js for dynamic frontend interactions, and DataTables for enhanced data presentation. We prioritize the "Fat Model, Thin View" paradigm, ensuring business logic resides within models and views remain concise and focused on data retrieval.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code interacts with three primary tables: `tblDG_Item_Master`, `tblDG_BOM_Master`, and `Unit_Master`. The core functionality revolves around displaying an item's details and its Bill of Material (BOM) history, which involves navigating a hierarchical BOM structure.

**Inferred Schema:**

1.  **`Unit_Master`**: Stores Units of Measurement.
    *   `Id`: Primary Key (integer)
    *   `Symbol`: Unit symbol (e.g., "KG", "PCS") (string)

2.  **`tblDG_Item_Master`**: Stores master data for items.
    *   `Id`: Primary Key (integer)
    *   `ItemCode`: Unique code for the item (string)
    *   `ManfDesc`: Manufacturing description of the item (string)
    *   `UOMBasic`: Foreign Key to `Unit_Master.Id`, representing the basic unit of measure (integer)
    *   `CompId`: Company ID (integer)
    *   `FinYearId`: Financial Year ID (integer)

3.  **`tblDG_BOM_Master`**: Stores Bill of Material entries, representing parent-child relationships between items. This table appears to represent a hierarchical structure using `PId` and `CId` for linking BOM entries themselves, where `ItemId` is the actual product or component.
    *   `Id`: Primary Key (integer)
    *   `ItemId`: Foreign Key to `tblDG_Item_Master.Id`, representing the actual item that is a component or part of the BOM (integer)
    *   `PId`: Integer, representing the `CId` of the parent BOM entry in the hierarchy. If `0`, this is a top-level assembly component.
    *   `CId`: Integer, a unique identifier for this specific BOM entry or component instance. Children will reference this `CId` in their `PId` field.
    *   `WONo`: Work Order Number (string)
    *   `Qty`: Quantity of the `ItemId` required for its direct parent (`PId`) in this BOM entry (decimal)
    *   `SysDate`: System Date of entry (string, needs conversion to DateField)
    *   `SysTime`: System Time of entry (string, needs conversion to TimeField)
    *   `CompId`: Company ID (integer)
    *   `FinYearId`: Financial Year ID (integer)

### Step 2: Identify Backend Functionality

**Analysis:**
The ASP.NET page is a **Read-only (Report)** view. It fetches and displays data related to an `Item` based on its `Id` (passed via QueryString). There are no explicit Create, Update, or Delete operations on this page itself.

*   **Read**:
    *   Displays details of a specific `Item` (Code, Description, UOM).
    *   Lists historical BOM entries where this `Item` appears as a component, along with related details (WO No, Date, Time, Assembly No, Description, UOM, BOM Qty).
    *   Calculates a "Total BOM Quantity" (`lblTot`).
    *   The core logic involves fetching data from `tblDG_Item_Master`, `tblDG_BOM_Master`, and `Unit_Master`, and performing a recursive calculation (`fun.BOMRecurQty`) to determine effective quantities within the BOM hierarchy.
*   **Navigation**:
    *   A "Cancel" button redirects the user to `ItemHistory_BOM.aspx`. This will be a simple `HttpResponseRedirect` in Django.

### Step 3: Infer UI Components

**Analysis:**
The ASP.NET page primarily uses `Label` controls for displaying item details and a `GridView` for tabular data. The `GridView` is styled with `yui-datatable-theme`, indicating a need for a client-side data table solution like DataTables in Django.

*   **Header Information**: Displays `ItemCode`, `ManfDesc`, and `UOMBasic` of the main item.
*   **Action Button**: `btnCancel` for navigation.
*   **Data Grid (`GridView2`)**:
    *   Presents a list of BOM entries.
    *   Columns: SN, WO No, Date, Time, Assembly No, Description, UOM, BOM Qty.
    *   Paging: While `AllowPaging="False"` in ASPX, `OnPageIndexChanging` is present, suggesting dynamic pagination. This will be handled entirely by DataTables on the client-side.
    *   No direct client-side JavaScript interactions are evident beyond the `loadingNotifier.js`. In Django, we will use HTMX for dynamic content loading (e.g., the table itself) and DataTables for grid functionalities. Alpine.js can be used for UI state like modal management.

---

### Step 4: Generate Django Code

We will create a Django application named `inventory_reports` to host this functionality.

#### 4.1 Models (`inventory_reports/models.py`)

This file defines the Django ORM models, mapping them to the existing database tables. Critical business logic, like the recursive BOM quantity calculation, is implemented as a method within the `BillOfMaterial` model.

```python
from django.db import models
from django.db.models import F, Sum, DecimalField
from decimal import Decimal
import datetime

# Mocking session context for demonstration purposes.
# In a real application, CompId and FYId would typically come from the authenticated user's profile
# or a request-scoped context.
MOCK_COMP_ID = 1
MOCK_FIN_YEAR_ID = 1

class Unit(models.Model):
    """
    Maps to the Unit_Master table, storing Units of Measure.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit of Measure'
        verbose_name_plural = 'Units of Measure'

    def __str__(self):
        return self.symbol

class Item(models.Model):
    """
    Maps to the tblDG_Item_Master table, storing Item master data.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=100)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255)
    uom_basic = models.ForeignKey(Unit, models.DO_NOTHING, db_column='UOMBasic')
    company_id = models.IntegerField(db_column='CompId')
    financial_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return f"{self.item_code} - {self.manf_desc}"

class BillOfMaterial(models.Model):
    """
    Maps to the tblDG_BOM_Master table, storing Bill of Material entries.
    Represents hierarchical relationships between items within a work order.
    PId is the CId of the parent BOM entry, CId is the unique ID of this BOM entry.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    item = models.ForeignKey(Item, models.DO_NOTHING, db_column='ItemId') # The actual item that is a component
    parent_bom_id = models.IntegerField(db_column='PId') # The CId of the parent BOM entry. 0 if top-level.
    component_id = models.IntegerField(db_column='CId') # Unique ID for this BOM entry/component instance.
    work_order_no = models.CharField(db_column='WONo', max_length=50)
    quantity = models.DecimalField(db_column='Qty', max_digits=10, decimal_places=3)
    sys_date = models.CharField(db_column='SysDate', max_length=20) # Store as CharField and convert via property
    sys_time = models.CharField(db_column='SysTime', max_length=20) # Store as CharField and convert via property
    company_id = models.IntegerField(db_column='CompId')
    financial_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'Bill of Material Entry'
        verbose_name_plural = 'Bill of Material Entries'

    def __str__(self):
        return f"BOM {self.work_order_no} - {self.item.item_code} (CId: {self.component_id})"

    @property
    def formatted_sys_date(self):
        """Converts 'DD-MM-YYYY' string date to YYYY-MM-DD format or similar for display."""
        try:
            # Assuming original format is DD-MM-YYYY or MM-DD-YYYY, let's try parsing both
            # and format to a standard Django date format like 'YYYY-MM-DD'
            # The ASP.NET code uses REPLACE(CONVERT(varchar, CONVERT(datetime, SUBSTRING( ... ), 103), '/', '-') AS Date
            # which implies '103' style (DD/MM/YYYY). Let's assume input is 'DD-MM-YYYY'.
            return datetime.datetime.strptime(self.sys_date, '%d-%m-%Y').strftime('%Y-%m-%d')
        except ValueError:
            return self.sys_date # Return original if parsing fails

    @property
    def formatted_sys_time(self):
        """Returns the system time as is, or can format it if needed."""
        return self.sys_time

    def get_parent_item_code(self):
        """
        Retrieves the ItemCode of the direct parent item for this BOM entry.
        Corresponds to the 'Assembly No' in the original ASP.NET GridView.
        """
        if self.parent_bom_id == 0:
            return "N/A (Top Level Assembly)"
        try:
            # Find the parent BOM entry (where its CId matches this entry's PId)
            parent_bom_entry = BillOfMaterial.objects.get(
                component_id=self.parent_bom_id,
                work_order_no=self.work_order_no,
                company_id=self.company_id, # Use current BOM entry's context
                financial_year_id=self.financial_year_id # Use current BOM entry's context
            )
            return parent_bom_entry.item.item_code
        except BillOfMaterial.DoesNotExist:
            return "Parent Missing"

    def get_recursive_bom_quantity(self, company_id=MOCK_COMP_ID, financial_year_id=MOCK_FIN_YEAR_ID):
        """
        Calculates the cumulative quantity of this BOM entry's 'item' required for
        one unit of the *root* assembly in the BOM tree for its work order.
        This is a recursive function to trace the BOM hierarchy upwards from the current component.
        """
        # Start with the direct quantity of this component for its immediate parent
        current_component_qty = self.quantity

        # If this component itself has a parent (it's not a top-level assembly component)
        if self.parent_bom_id != 0:
            try:
                # Find the immediate parent BOM entry.
                # Its `component_id` should match this entry's `parent_bom_id`.
                parent_bom_entry = BillOfMaterial.objects.get(
                    component_id=self.parent_bom_id,
                    work_order_no=self.work_order_no,
                    company_id=company_id,
                    financial_year_id=financial_year_id
                )
                # Recursively get the total quantity of the parent, then multiply
                # by the current component's direct quantity.
                # This assumes `get_recursive_bom_quantity` returns the total quantity of the parent relative to the root.
                # So total_qty = (current_qty) * (parent_total_qty)
                current_component_qty *= parent_bom_entry.get_recursive_bom_quantity(company_id, financial_year_id)
            except BillOfMaterial.DoesNotExist:
                # Parent BOM entry not found, might indicate data inconsistency.
                # In such cases, we might log an error or simply return the direct quantity.
                # For this migration, we'll return the current direct quantity, assuming it's a sub-assembly.
                pass
        return current_component_qty

```

#### 4.2 Forms (`inventory_reports/forms.py`)

No forms are required for this particular view as it's a read-only report.

```python
# No forms required for this read-only report view.
```

#### 4.3 Views (`inventory_reports/views.py`)

This file defines the Class-Based Views for displaying the item history. A `DetailView` is used for the main item information, and a separate `ListView` for the DataTables partial, adhering to HTMX patterns.

```python
from django.views.generic import DetailView, ListView
from django.http import HttpResponse, Http404
from django.shortcuts import get_object_or_404
from .models import Item, BillOfMaterial, MOCK_COMP_ID, MOCK_FIN_YEAR_ID
from decimal import Decimal

class ItemHistoryDetailView(DetailView):
    """
    Displays the main item details and serves as the entry point for the BOM history report.
    """
    model = Item
    template_name = 'inventory_reports/item_history/detail.html'
    context_object_name = 'item'

    def get_object(self, queryset=None):
        """
        Retrieves the Item object based on the 'pk' from the URL (which maps to ASP.NET QueryString 'Id').
        """
        # Ensure the item exists for the mocked company and financial year IDs
        pk = self.kwargs.get(self.pk_url_kwarg)
        if not pk:
            raise Http404("Item ID is required.")
        
        # In a real scenario, CompId and FYId would be derived from the request user.
        # For this migration, we use mock values.
        return get_object_or_404(
            Item, 
            pk=pk, 
            company_id=MOCK_COMP_ID, 
            financial_year_id=MOCK_FIN_YEAR_ID
        )

    def get_context_data(self, **kwargs):
        """
        Adds related BOM history data and total quantity to the context.
        """
        context = super().get_context_data(**kwargs)
        item = context['item']
        
        # Fetch related BOM entries where this item is a component (ItemId)
        # This corresponds to the loop and second SQL query in ASP.NET's bindData
        bom_entries = BillOfMaterial.objects.filter(
            item=item,
            company_id=MOCK_COMP_ID,
            financial_year_id=MOCK_FIN_YEAR_ID
        ).order_by('work_order_no', 'component_id')

        # Calculate total BOM quantity for display
        total_bom_qty = Decimal(0)
        for entry in bom_entries:
            # Calculate recursive BOM quantity for each entry
            entry.calculated_bom_qty = entry.get_recursive_bom_quantity(MOCK_COMP_ID, MOCK_FIN_YEAR_ID)
            total_bom_qty += entry.calculated_bom_qty

        context['bom_entries'] = bom_entries
        context['total_bom_qty'] = total_bom_qty

        return context


class ItemHistoryTablePartialView(ListView):
    """
    Serves the DataTables partial for BOM history, loaded via HTMX.
    """
    model = BillOfMaterial
    template_name = 'inventory_reports/item_history/_item_history_table.html'
    context_object_name = 'bom_entries'

    def get_queryset(self):
        """
        Filters BOM entries based on the item_id passed as a URL parameter.
        """
        item_pk = self.kwargs.get('pk')
        if not item_pk:
            return BillOfMaterial.objects.none()

        try:
            item = Item.objects.get(pk=item_pk, company_id=MOCK_COMP_ID, financial_year_id=MOCK_FIN_YEAR_ID)
        except Item.DoesNotExist:
            return BillOfMaterial.objects.none()
        
        queryset = BillOfMaterial.objects.filter(
            item=item,
            company_id=MOCK_COMP_ID,
            financial_year_id=MOCK_FIN_YEAR_ID
        ).order_by('work_order_no', 'component_id')

        # Annotate each BOM entry with its calculated recursive quantity
        for entry in queryset:
            entry.calculated_bom_qty = entry.get_recursive_bom_quantity(MOCK_COMP_ID, MOCK_FIN_YEAR_ID)

        return queryset

    def get_context_data(self, **kwargs):
        """
        Adds total BOM quantity to the context for the partial view.
        """
        context = super().get_context_data(**kwargs)
        
        total_bom_qty = Decimal(0)
        for entry in context['bom_entries']:
            total_bom_qty += entry.calculated_bom_qty

        context['total_bom_qty'] = total_bom_qty
        
        # Also pass the main item's details for header if needed (though not strictly necessary for table partial)
        item_pk = self.kwargs.get('pk')
        if item_pk:
             context['item'] = get_object_or_404(Item, pk=item_pk, company_id=MOCK_COMP_ID, financial_year_id=MOCK_FIN_YEAR_ID)

        return context

```

#### 4.4 Templates (`inventory_reports/templates/inventory_reports/item_history/`)

The templates are structured to extend a base layout and use HTMX for loading the DataTables portion dynamically.

**`detail.html` (Main Item History View)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <table width="100%" cellpadding="0" cellspacing="0" class="mb-6">
        <tr>
            <td colspan="4" class="bg-blue-600 text-white font-bold px-4 py-2 text-xl" style="background:url(/static/images/hdbg.JPG);">
                Item History
            </td>
        </tr>
        <tr>
            <td class="pt-4 pb-2 px-2" style="width: 25%;">
                <label class="font-bold text-justify">ItemCode : </label>
                <span>{{ item.item_code }}</span>
            </td>
            <td class="pt-4 pb-2 px-2" style="width: 25%;">
                <label class="font-bold">UOM :</label>
                <span>{{ item.uom_basic.symbol }}</span>
            </td>
            <td style="width: 25%;"></td>
            <td style="width: 25%;"></td>
        </tr>
        <tr>
            <td colspan="3" class="pb-2 px-2">
                <label class="font-bold">Description :</label>
                <span>{{ item.manf_desc }}</span>
            </td>
            <td></td>
        </tr>
        <tr>
            <td colspan="4" align="center" class="pt-4">
                <a href="{% url 'item_history_list' %}" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                    Cancel
                </a>
            </td>
        </tr>
        <tr>
            <td colspan="4" align="center" class="pt-4">&nbsp;</td>
        </tr>
        <tr>
            <td colspan="4">
                <div id="itemHistoryTableContainer"
                     hx-get="{% url 'item_history_table_partial' pk=item.pk %}"
                     hx-trigger="load"
                     hx-swap="innerHTML"
                     class="min-h-[340px] overflow-auto">
                    <!-- Loading spinner for HTMX content -->
                    <div class="text-center pt-20">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p class="mt-2 text-gray-600">Loading Item History...</p>
                    </div>
                </div>
            </td>
        </tr>
    </table>
</div>
{% endblock %}

{% block extra_js %}
<!-- DataTables and its dependencies will be included in core/base.html -->
<script>
    // Alpine.js or any other specific scripts for this page can go here.
    // No specific Alpine.js needed from the original ASPX logic.
</script>
{% endblock %}
```

**`_item_history_table.html` (DataTables Partial - Loaded by HTMX)**

```html
<table id="itemHistoryTable" class="min-w-full bg-white border border-gray-200 shadow-sm yui-datatable-theme">
    <thead>
        <tr class="bg-gray-50">
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assembly No</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">BOM Qty</th>
        </tr>
    </thead>
    <tbody>
        {% for entry in bom_entries %}
        <tr class="{% cycle 'bg-white' 'bg-gray-50' %} hover:bg-gray-100">
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ entry.work_order_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ entry.formatted_sys_date }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ entry.formatted_sys_time }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ entry.get_parent_item_code }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ entry.item.manf_desc }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ entry.item.uom_basic.symbol }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ entry.calculated_bom_qty|floatformat:3 }}</td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="8" class="py-4 text-center text-gray-500">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
    {% if bom_entries %}
    <tfoot>
        <tr>
            <td colspan="7" class="py-2 px-4 border-t border-gray-200 text-right font-bold">Total:</td>
            <td class="py-2 px-4 border-t border-gray-200 text-right font-bold">{{ total_bom_qty|floatformat:3 }}</td>
        </tr>
    </tfoot>
    {% endif %}
</table>

{% comment %}
    Initialize DataTables for the loaded content.
    This script will run after HTMX swaps the content into the DOM.
    Make sure jQuery and DataTables JS are included in base.html.
{% endcomment %}
<script>
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists to prevent re-initialization errors
        if ($.fn.DataTable.isDataTable('#itemHistoryTable')) {
            $('#itemHistoryTable').DataTable().destroy();
        }
        $('#itemHistoryTable').DataTable({
            "paging": true,      // Enable pagination
            "searching": true,   // Enable search box
            "ordering": true,    // Enable column sorting
            "info": true,        // Show "Showing X to Y of Z entries"
            "pageLength": 15,    // Default number of entries per page, matching ASP.NET's PageSize
            "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]], // Options for entries per page
            "columnDefs": [
                { "orderable": false, "targets": [0] } // Disable sorting for SN column
            ]
        });
    });
</script>
```

#### 4.5 URLs (`inventory_reports/urls.py`)

This file defines the URL patterns for the views, enabling easy navigation and HTMX endpoint calls.

```python
from django.urls import path
from django.views.generic import RedirectView
from .views import ItemHistoryDetailView, ItemHistoryTablePartialView
from django.urls import reverse_lazy

urlpatterns = [
    # Main Item History Detail View, accessed via item ID (pk)
    # Corresponds to ItemHistory_BOM_View.aspx?Id=X
    path('item-history/<int:pk>/', ItemHistoryDetailView.as_view(), name='item_history_detail'),
    
    # HTMX endpoint for the DataTables partial view.
    # This URL will be called by HTMX to load the table content dynamically.
    path('item-history/table-partial/<int:pk>/', ItemHistoryTablePartialView.as_view(), name='item_history_table_partial'),

    # Redirect for the "Cancel" button, mimicking the original ASP.NET behavior.
    # Assumes a new Django view 'item_history_list' (or similar) handles the main Item History list.
    # This is a placeholder; adjust 'item_history_list' to point to your actual list view.
    path('item-history/', RedirectView.as_view(url=reverse_lazy('item_history_list'), permanent=False), name='item_history_list'),
]

```
**Note:** For `item_history_list`, you would need to implement a corresponding `ListView` for the main `ItemHistory_BOM.aspx` page (which seems to be a list/search page for BOMs). This is a placeholder for demonstration purposes.

#### 4.6 Tests (`inventory_reports/tests.py`)

Comprehensive tests cover model logic, including the recursive BOM quantity calculation, and ensure that views render correctly and handle data as expected.

```python
from django.test import TestCase, Client
from django.urls import reverse
from decimal import Decimal
import datetime

# Mock MOCK_COMP_ID and MOCK_FIN_YEAR_ID for tests
from .models import Unit, Item, BillOfMaterial, MOCK_COMP_ID, MOCK_FIN_YEAR_ID

class ItemHistoryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        # Create Units
        cls.kg_unit = Unit.objects.create(id=1, symbol='KG')
        cls.pcs_unit = Unit.objects.create(id=2, symbol='PCS')

        # Create Items
        cls.item_a = Item.objects.create(
            id=101, item_code='PROD_A', manf_desc='Final Product A', 
            uom_basic=cls.pcs_unit, company_id=MOCK_COMP_ID, financial_year_id=MOCK_FIN_YEAR_ID
        )
        cls.item_b = Item.objects.create(
            id=102, item_code='COMP_B', manf_desc='Component B', 
            uom_basic=cls.kg_unit, company_id=MOCK_COMP_ID, financial_year_id=MOCK_FIN_YEAR_ID
        )
        cls.item_c = Item.objects.create(
            id=103, item_code='SUB_C', manf_desc='Sub-Component C', 
            uom_basic=cls.pcs_unit, company_id=MOCK_COMP_ID, financial_year_id=MOCK_FIN_YEAR_ID
        )
        cls.item_d = Item.objects.create(
            id=104, item_code='RAW_D', manf_desc='Raw Material D', 
            uom_basic=cls.kg_unit, company_id=MOCK_COMP_ID, financial_year_id=MOCK_FIN_YEAR_ID
        )

        # Create BillOfMaterial entries (representing A -> B -> C, A -> D)
        # WO1: Product A (id=101) is the final product.
        # It consists of 2 units of Component B (id=102) and 10 units of Raw Material D (id=104)
        # Component B (id=102) consists of 5 units of Sub-Component C (id=103)

        # BOM entry for A (top-level, CId=1) - A has no parent BOM entry (PId=0)
        cls.bom_a_root = BillOfMaterial.objects.create(
            id=1, item=cls.item_a, parent_bom_id=0, component_id=1,
            work_order_no='WO001', quantity=Decimal('1.000'), 
            sys_date='01-01-2023', sys_time='09:00:00',
            company_id=MOCK_COMP_ID, financial_year_id=MOCK_FIN_YEAR_ID
        )
        
        # BOM entry for B, a component of A (CId=2) - B's parent BOM entry is A (CId=1)
        cls.bom_b_in_a = BillOfMaterial.objects.create(
            id=2, item=cls.item_b, parent_bom_id=cls.bom_a_root.component_id, component_id=2, # PId points to A's CId
            work_order_no='WO001', quantity=Decimal('2.000'), # 2 B per A
            sys_date='01-01-2023', sys_time='09:05:00',
            company_id=MOCK_COMP_ID, financial_year_id=MOCK_FIN_YEAR_ID
        )

        # BOM entry for C, a component of B (CId=3) - C's parent BOM entry is B (CId=2)
        cls.bom_c_in_b = BillOfMaterial.objects.create(
            id=3, item=cls.item_c, parent_bom_id=cls.bom_b_in_a.component_id, component_id=3, # PId points to B's CId
            work_order_no='WO001', quantity=Decimal('5.000'), # 5 C per B
            sys_date='01-01-2023', sys_time='09:10:00',
            company_id=MOCK_COMP_ID, financial_year_id=MOCK_FIN_YEAR_ID
        )

        # BOM entry for D, a component of A (CId=4) - D's parent BOM entry is A (CId=1)
        cls.bom_d_in_a = BillOfMaterial.objects.create(
            id=4, item=cls.item_d, parent_bom_id=cls.bom_a_root.component_id, component_id=4, # PId points to A's CId
            work_order_no='WO001', quantity=Decimal('10.000'), # 10 D per A
            sys_date='01-01-2023', sys_time='09:15:00',
            company_id=MOCK_COMP_ID, financial_year_id=MOCK_FIN_YEAR_ID
        )
        
        # Another WO for Item B, but without children to test
        cls.bom_b_wo2 = BillOfMaterial.objects.create(
            id=5, item=cls.item_b, parent_bom_id=0, component_id=5,
            work_order_no='WO002', quantity=Decimal('1.000'), 
            sys_date='02-01-2023', sys_time='10:00:00',
            company_id=MOCK_COMP_ID, financial_year_id=MOCK_FIN_YEAR_ID
        )

    def test_item_creation(self):
        self.assertEqual(self.item_a.item_code, 'PROD_A')
        self.assertEqual(self.item_a.manf_desc, 'Final Product A')
        self.assertEqual(self.item_a.uom_basic, self.pcs_unit)

    def test_billofmaterial_creation(self):
        self.assertEqual(self.bom_b_in_a.item, self.item_b)
        self.assertEqual(self.bom_b_in_a.parent_bom_id, self.bom_a_root.component_id)
        self.assertEqual(self.bom_b_in_a.quantity, Decimal('2.000'))
        self.assertEqual(self.bom_c_in_b.item, self.item_c)
        self.assertEqual(self.bom_c_in_b.parent_bom_id, self.bom_b_in_a.component_id)
        self.assertEqual(self.bom_c_in_b.quantity, Decimal('5.000'))

    def test_formatted_sys_date(self):
        self.assertEqual(self.bom_a_root.formatted_sys_date, '2023-01-01')
        
    def test_get_parent_item_code(self):
        self.assertEqual(self.bom_b_in_a.get_parent_item_code(), self.item_a.item_code)
        self.assertEqual(self.bom_c_in_b.get_parent_item_code(), self.item_b.item_code)
        self.assertEqual(self.bom_a_root.get_parent_item_code(), "N/A (Top Level Assembly)")

    def test_recursive_bom_quantity(self):
        # Quantity of A per A (root)
        self.assertEqual(self.bom_a_root.get_recursive_bom_quantity(), Decimal('1.000'))
        
        # Quantity of B per A
        # B is directly part of A (2 B per A)
        self.assertEqual(self.bom_b_in_a.get_recursive_bom_quantity(), Decimal('2.000'))
        
        # Quantity of C per A (through B)
        # 5 C per B, and 2 B per A, so 5 * 2 = 10 C per A
        self.assertEqual(self.bom_c_in_b.get_recursive_bom_quantity(), Decimal('10.000')) # 5 (C/B) * 2 (B/A) = 10 (C/A)

        # Quantity of D per A
        # D is directly part of A (10 D per A)
        self.assertEqual(self.bom_d_in_a.get_recursive_bom_quantity(), Decimal('10.000'))
        
        # Test for item B in WO002 (top level)
        self.assertEqual(self.bom_b_wo2.get_recursive_bom_quantity(), Decimal('1.000'))


class ItemHistoryViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for views (similar to model test, but minimal for view checks)
        cls.kg_unit = Unit.objects.create(id=1, symbol='KG')
        cls.pcs_unit = Unit.objects.create(id=2, symbol='PCS')

        cls.item_main = Item.objects.create(
            id=201, item_code='MAIN_ITEM', manf_desc='Main Product for History', 
            uom_basic=cls.pcs_unit, company_id=MOCK_COMP_ID, financial_year_id=MOCK_FIN_YEAR_ID
        )
        cls.item_comp1 = Item.objects.create(
            id=202, item_code='COMP_01', manf_desc='Component One', 
            uom_basic=cls.kg_unit, company_id=MOCK_COMP_ID, financial_year_id=MOCK_FIN_YEAR_ID
        )
        cls.item_comp2 = Item.objects.create(
            id=203, item_code='COMP_02', manf_desc='Component Two', 
            uom_basic=cls.pcs_unit, company_id=MOCK_COMP_ID, financial_year_id=MOCK_FIN_YEAR_ID
        )

        # BOM entries where MAIN_ITEM (id=201) is the child.
        # This simulates the data populated in the GridView.
        # A fictional 'parent_item' exists for these entries to simulate AssemblyNo.
        cls.parent_item_code = 'PARENT_ASSY'
        cls.item_parent_assy = Item.objects.create( # Fictional parent item in item_master
            id=999, item_code=cls.parent_item_code, manf_desc='Parent Assembly',
            uom_basic=cls.pcs_unit, company_id=MOCK_COMP_ID, financial_year_id=MOCK_FIN_YEAR_ID
        )

        cls.bom_entry_1 = BillOfMaterial.objects.create(
            id=10, item=cls.item_main, parent_bom_id=cls.item_parent_assy.id, component_id=100, # Assuming PId points to an item ID for simplicity in test
            work_order_no='WO-ALPHA', quantity=Decimal('5.000'), 
            sys_date='15-02-2023', sys_time='11:00:00',
            company_id=MOCK_COMP_ID, financial_year_id=MOCK_FIN_YEAR_ID
        )
        cls.bom_entry_2 = BillOfMaterial.objects.create(
            id=11, item=cls.item_main, parent_bom_id=cls.item_parent_assy.id, component_id=101,
            work_order_no='WO-BETA', quantity=Decimal('3.500'), 
            sys_date='16-02-2023', sys_time='14:30:00',
            company_id=MOCK_COMP_ID, financial_year_id=MOCK_FIN_YEAR_ID
        )
        # Note: The `parent_bom_id` in BillOfMaterial is typically a `CId` of another BOM entry,
        # not necessarily an `ItemId`. For simplified test data, `cls.item_parent_assy.id` is used
        # to stand in for a `CId` that would then map to `item_parent_assy`'s item code via `get_parent_item_code`.
        # This setup is crucial for `get_parent_item_code` to work.

    def setUp(self):
        self.client = Client()

    def test_detail_view_get(self):
        url = reverse('item_history_detail', args=[self.item_main.pk])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/item_history/detail.html')
        self.assertContains(response, self.item_main.item_code)
        self.assertContains(response, self.item_main.manf_desc)
        self.assertContains(response, self.item_main.uom_basic.symbol)
        self.assertIsInstance(response.context['item'], Item)
        self.assertIn('bom_entries', response.context)
        self.assertIn('total_bom_qty', response.context)
        self.assertGreater(len(response.context['bom_entries']), 0)
        
    def test_detail_view_no_item(self):
        url = reverse('item_history_detail', args=[99999]) # Non-existent ID
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

    def test_table_partial_view_get(self):
        url = reverse('item_history_table_partial', args=[self.item_main.pk])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/item_history/_item_history_table.html')
        self.assertIn('bom_entries', response.context)
        self.assertGreater(len(response.context['bom_entries']), 0)
        self.assertContains(response, 'WO-ALPHA')
        self.assertContains(response, 'WO-BETA')
        self.assertContains(response, 'Total:')
        self.assertContains(response, Decimal('8.500').quantize(Decimal('0.001'))) # 5.000 + 3.500

    def test_table_partial_view_no_bom_entries(self):
        # Create an item with no BOM entries
        item_no_bom = Item.objects.create(
            id=300, item_code='NO_BOM', manf_desc='Item with No BOM', 
            uom_basic=self.pcs_unit, company_id=MOCK_COMP_ID, financial_year_id=MOCK_FIN_YEAR_ID
        )
        url = reverse('item_history_table_partial', args=[item_no_bom.pk])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No data to display !')

    def test_cancel_button_redirect(self):
        url = reverse('item_history_list') # This is the redirect URL from the template
        response = self.client.get(url)
        # Expect a 302 redirect for RedirectView
        self.assertEqual(response.status_code, 302)
        # Check that it redirects to the correct URL (e.g., a dummy list view URL)
        self.assertEqual(response.url, reverse('item_history_list')) # This will resolve to itself as it's a RedirectView
```

### Step 5: HTMX and Alpine.js Integration

**Instructions for Integration:**

1.  **HTMX for Dynamic Table Loading**:
    *   The main `detail.html` template uses `hx-get` on a `div` to fetch `_item_history_table.html` (the DataTables content). This `hx-get` is triggered `on load`, ensuring the table loads after the main page.
    *   `hx-target="innerHTML"` ensures only the content within the `div` is updated.
    *   This provides a smooth, dynamic user experience without full page reloads, mimicking the ASP.NET PostBack but with modern, efficient AJAX.
    *   The `loadingNotifier.js` functionality is implicitly handled by HTMX's `hx-indicator` or by manually showing/hiding a spinner during `hx-request`. Here, a simple spinner is included within the `hx-get` container as default content.

2.  **DataTables Implementation**:
    *   The `_item_history_table.html` partial contains the `<table>` element.
    *   A `<script>` block within this partial initializes DataTables on `$(document).ready()`. This ensures DataTables is applied *after* the content is loaded into the DOM by HTMX.
    *   We configure DataTables for client-side pagination, searching, sorting, and length menu, providing a rich interactive grid experience. The `pageLength` is set to 15, aligning with the `PageSize` attribute found in the ASP.NET `GridView`.
    *   Ensure that jQuery and DataTables JavaScript and CSS libraries are included in `core/base.html` (or your project's main layout file) from CDNs for proper functionality.

3.  **Alpine.js**:
    *   For this specific page, the original ASP.NET code did not show complex client-side UI logic beyond basic button clicks and grid interactions. Therefore, explicit Alpine.js components are not strictly necessary for direct conversion of the provided code.
    *   However, Alpine.js remains the recommended tool for any future UI state management, such as handling modal dialogues, dynamic form visibility, or client-side form validation messages, complementing HTMX's capabilities.
    *   A placeholder for Alpine.js is provided in `detail.html`'s `extra_js` block, and `core/base.html` should include Alpine.js CDN.

4.  **DRY Templates and Styling**:
    *   All templates extend `core/base.html` to inherit common layout, CDN links (HTMX, Alpine.js, jQuery, DataTables, Tailwind CSS), and header/footer elements. This keeps the component-specific templates clean and focused.
    *   Tailwind CSS classes are used directly within the HTML for styling, ensuring a modern and consistent look.

5.  **No Custom JavaScript (Beyond DataTables Init)**:
    *   The goal is to achieve functionality without writing extensive custom JavaScript. HTMX handles AJAX, and Alpine.js handles UI state. The DataTables initialization is an accepted pattern for integrating a feature-rich grid.

---

### Final Notes

*   **Placeholders**: Remember to replace mock `CompId` and `FYId` with actual logic to retrieve these from your authenticated user's session or profile in a production environment.
*   **Database Mapping**: The `managed = False` in models ensures Django doesn't create/manage these tables, relying on the existing database schema. Verify field types and `max_length` carefully to match your SQL Server database.
*   **Error Handling**: The provided code includes basic error handling (e.g., `get_object_or_404`), but a production system would require more robust error logging and user feedback mechanisms.
*   **Scalability**: The recursive BOM quantity calculation in the model is implemented in Python. For very deep BOMs or high transaction volumes, consider if this recursion should be offloaded to the database using Recursive Common Table Expressions (CTE) if your database supports it and it becomes a performance bottleneck.
*   **Missing Features**: This plan addresses the direct functionality of `ItemHistory_BOM_View.aspx`. Any other features or pages in the original ASP.NET application (like the `ItemHistory_BOM.aspx` page itself) would require separate migration plans.