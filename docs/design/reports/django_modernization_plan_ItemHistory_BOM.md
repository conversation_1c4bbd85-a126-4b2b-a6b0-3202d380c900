## ASP.NET to Django Conversion Script: Item History (BOM) Modernization Plan

This document outlines the strategic modernization plan for transitioning the "Item History" module from its legacy ASP.NET framework to a robust, scalable, and modern Django-based solution. Our approach prioritizes AI-assisted automation, emphasizing efficient code generation, and leveraging best practices in Django 5.0+, HTMX, Alpine.js, and DataTables for a superior user experience.

### Business Value Proposition

Migrating to Django offers significant business advantages:

1.  **Reduced Technical Debt:** Moves away from outdated ASP.NET Web Forms, streamlining maintenance and future development.
2.  **Enhanced Performance:** Django's ORM and optimized query capabilities, combined with efficient HTMX/Alpine.js frontend, deliver faster page loads and more responsive interactions.
3.  **Improved Maintainability & Scalability:** Django's clear architecture, 'Fat Model, Thin View' philosophy, and Python's readability lead to easier-to-understand and expand codebases.
4.  **Modern User Experience:** HTMX and Alpine.js provide dynamic, partial page updates without complex JavaScript frameworks, delivering a snappy, interactive UI reminiscent of SPAs, but with the simplicity of traditional web development. DataTables ensures efficient handling of large datasets on the client-side.
5.  **Cost Efficiency:** Python's vast ecosystem and Django's batteries-included approach often lead to faster development cycles and reduced operational costs.
6.  **Future-Proofing:** Adopts a modern, widely-supported, and actively developed framework, securing the application's longevity and adaptability.

---

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the ASP.NET code, particularly the `GridView` bindings, `Fillgrid` method's dynamic SQL construction, and dropdown population logic, we infer the following primary tables and their relationships:

*   **`tblDG_Item_Master`**: This is the main table for item information.
    *   **Columns:** `Id` (PK), `CId` (FK to `tblDG_Category_Master`), `PartNo`, `ItemCode`, `ManfDesc`, `UOMBasic`, `Location` (FK to `tblDG_Location_Master`), `CompId`, `FinYearId`.
*   **`tblDG_Category_Master`**: Used for populating the Category dropdown.
    *   **Columns:** `CId` (PK), `Symbol`, `CName`.
*   **`tblDG_Location_Master`**: Used for populating the Location dropdown.
    *   **Columns:** `Id` (PK), `LocationLabel`, `LocationNo`.

### Step 2: Identify Backend Functionality

The provided ASP.NET code primarily implements a **Read (List/Search)** functionality.

*   **Read (List/Search):** The core functionality involves displaying a list of "Items" (`tblDG_Item_Master`) in a `GridView`. The data is filtered dynamically based on various user selections from dropdowns (`DrpType`, `DrpCategory`, `DrpSearchCode`, `DropDownList3`) and a text input (`txtSearchItemCode`), triggered by a "Search" button click (`btnSearch_Click`) or dropdown selection changes (`DrpType_SelectedIndexChanged`, `DrpCategory_SelectedIndexChanged`, `DrpSearchCode_SelectedIndexChanged`). The `Fillgrid` method handles the complex SQL query construction, likely against a stored procedure `GetAllItem`.
*   **No explicit Create, Update, or Delete:** The current page does not expose direct CRUD operations for items. The `HyperLinkField` links to a `ItemHistory_BOM_View.aspx`, indicating a separate view page for individual item details.

### Step 3: Infer UI Components

The ASP.NET UI components and their roles are identified as:

*   **Dropdowns for Filtering:**
    *   `DrpType`: Selects search mode ("Category", "WOItems").
    *   `DrpCategory`: Filters by item category (dynamically populated).
    *   `DrpSearchCode`: Selects the search field ("Item Code", "Description", "Location").
    *   `DropDownList3`: Selects a specific location (becomes visible when `DrpSearchCode` is "Location").
*   **Text Input for Search:**
    *   `txtSearchItemCode`: Enters search text for "Item Code" or "Description".
*   **Action Button:**
    *   `btnSearch`: Triggers the search.
*   **Data Display:**
    *   `GridView2`: Displays the filtered item data with columns: SN, Select (link to detail), Category, ItemCode, ManfDesc, UOMBasic, Location.

### Step 4: Generate Django Code

We will create a new Django application, let's call it `inventory`.

#### 4.1 Models (`inventory/models.py`)

We'll define `Item`, `Category`, and `Location` models, mapping them to the existing database tables. The complex filtering logic from `Fillgrid` will be moved into a custom manager or static methods within the `Item` model, adhering to the fat model principle.

```python
from django.db import models
from django.db.models import F, Q

class Category(models.Model):
    # Assuming CId is the primary key and corresponds to the ID field
    cid = models.IntegerField(db_column='CId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=100, blank=True, null=True)
    cname = models.CharField(db_column='CName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'

    def __str__(self):
        return f"{self.symbol}-{self.cname}" if self.symbol else self.cname


class Location(models.Model):
    # Assuming Id is the primary key
    id = models.IntegerField(db_column='Id', primary_key=True)
    location_label = models.CharField(db_column='LocationLabel', max_length=100, blank=True, null=True)
    location_no = models.CharField(db_column='LocationNo', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Location_Master'
        verbose_name = 'Location'
        verbose_name_plural = 'Locations'

    def __str__(self):
        return f"{self.location_label}-{self.location_no}" if self.location_label else self.location_no


class ItemManager(models.Manager):
    def filter_by_criteria(self, search_params, comp_id, fin_year_id):
        """
        Applies filtering logic similar to the ASP.NET Fillgrid method.
        search_params is a dictionary from the search form.
        """
        qs = self.get_queryset().filter(comp_id=comp_id, fin_year_id__lte=fin_year_id)

        drp_type = search_params.get('drp_type')
        drp_category = search_params.get('drp_category')
        drp_search_code = search_params.get('drp_search_code')
        txt_search_item_code = search_params.get('txt_search_item_code', '').strip()
        drp_location = search_params.get('drp_location')

        if drp_type == 'Category':
            if drp_category and drp_category != 'Select':
                qs = qs.filter(category_id=drp_category)

            if drp_search_code != 'Select':
                if drp_search_code == 'tblDG_Item_Master.ItemCode':
                    if txt_search_item_code:
                        qs = qs.filter(item_code__istartswith=txt_search_item_code)
                elif drp_search_code == 'tblDG_Item_Master.ManfDesc':
                    if txt_search_item_code:
                        qs = qs.filter(manf_desc__icontains=txt_search_item_code)
                elif drp_search_code == 'tblDG_Item_Master.Location':
                    if drp_location and drp_location != 'Select':
                        qs = qs.filter(location_id=drp_location)
            elif not drp_category and not drp_search_code and txt_search_item_code: # Equivalent to (sd == "Select" && B == "Select" && s != string.Empty)
                qs = qs.filter(manf_desc__icontains=txt_search_item_code)

        elif drp_type == 'WOItems':
            if drp_search_code != 'Select':
                if drp_search_code == 'tblDG_Item_Master.ItemCode':
                    if txt_search_item_code:
                        qs = qs.filter(item_code__icontains=txt_search_item_code)
                elif drp_search_code == 'tblDG_Item_Master.ManfDesc':
                    if txt_search_item_code:
                        qs = qs.filter(manf_desc__icontains=txt_search_item_code)
            elif not drp_search_code and txt_search_item_code: # Equivalent to (B == "Select" && s != string.Empty)
                qs = qs.filter(manf_desc__icontains=txt_search_item_code)
        
        # If "Select" is chosen for DrpType, no filtering should apply initially.
        # The ASP.NET code redirects to URL.ToString() which reloads the page with no filters.
        # So if drp_type is "Select", we just return the base queryset.
        if drp_type == 'Select':
            qs = self.get_queryset().filter(comp_id=comp_id, fin_year_id__lte=fin_year_id)

        return qs

class Item(models.Model):
    # Field names mapped to the inferred database columns
    id = models.IntegerField(db_column='Id', primary_key=True)
    category = models.ForeignKey(Category, on_delete=models.DO_NOTHING, db_column='CId', blank=True, null=True)
    part_no = models.CharField(db_column='PartNo', max_length=255, blank=True, null=True)
    item_code = models.CharField(db_column='ItemCode', max_length=255, blank=True, null=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=500, blank=True, null=True)
    uom_basic = models.CharField(db_column='UOMBasic', max_length=50, blank=True, null=True)
    location = models.ForeignKey(Location, on_delete=models.DO_NOTHING, db_column='Location', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    objects = ItemManager()

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.item_code if self.item_code else self.manf_desc

    @property
    def full_category_display(self):
        if self.category:
            return f"{self.category.symbol}-{self.category.cname}" if self.category.symbol else self.category.cname
        return ''

    @property
    def full_location_display(self):
        if self.location:
            return f"{self.location.location_label}-{self.location.location_no}" if self.location.location_label else self.location.location_no
        return ''

    def get_absolute_url(self):
        """
        Mimics the HyperLinkField's DataNavigateUrlFormatString
        """
        # Assuming there's a Django URL pattern for item_detail
        return reverse('inventory:item_detail', args=[self.pk])
```

#### 4.2 Forms (`inventory/forms.py`)

A non-ModelForm is needed to handle the complex filtering parameters.

```python
from django import forms
from .models import Category, Location

class ItemSearchForm(forms.Form):
    DRP_TYPE_CHOICES = [
        ('Select', 'Select'),
        ('Category', 'Category'),
        ('WOItems', 'WO Items'),
    ]

    DRP_SEARCH_CODE_CHOICES = [
        ('Select', 'Select'),
        ('tblDG_Item_Master.ItemCode', 'Item Code'),
        ('tblDG_Item_Master.ManfDesc', 'Description'),
        ('tblDG_Item_Master.Location', 'Location'),
    ]

    drp_type = forms.ChoiceField(
        choices=DRP_TYPE_CHOICES,
        required=False,
        label="Type",
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': "{% url 'inventory:item_search_form_partial' %}",
            'hx-target': '#search-form-container',
            'hx-swap': 'outerHTML',
            'hx-trigger': 'change, load once', # Load once to ensure initial state is set
            'name': 'drp_type' # Explicitly set name for HTMX to pick it up
        })
    )

    drp_category = forms.ChoiceField(
        choices=[('Select', 'Select')] + [(str(c.cid), str(c)) for c in Category.objects.all()],
        required=False,
        label="Category",
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': "{% url 'inventory:item_search_form_partial' %}",
            'hx-target': '#search-form-container',
            'hx-swap': 'outerHTML',
            'hx-trigger': 'change',
            'name': 'drp_category'
        })
    )

    drp_search_code = forms.ChoiceField(
        choices=DRP_SEARCH_CODE_CHOICES,
        required=False,
        label="Search By",
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': "{% url 'inventory:item_search_form_partial' %}",
            'hx-target': '#search-form-container',
            'hx-swap': 'outerHTML',
            'hx-trigger': 'change',
            'name': 'drp_search_code'
        })
    )

    drp_location = forms.ChoiceField(
        choices=[('Select', 'Select')] + [(str(loc.id), str(loc)) for loc in Location.objects.all()],
        required=False,
        label="Location",
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'name': 'drp_location'
        })
    )

    txt_search_item_code = forms.CharField(
        max_length=200,
        required=False,
        label="Search Term",
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'name': 'txt_search_item_code'
        })
    )

    # We need to dynamically adjust visibility in the template based on form data,
    # or re-render parts of the form based on HTMX.
    # The initial visibility logic from ASP.NET (Page_Load)
    # DrpCategory.Visible = false; DropDownList3.Visible = false; DrpSearchCode.Visible = false; txtSearchItemCode.Visible = false;
    # will be handled by Alpine.js or by re-rendering the form partial.
```

#### 4.3 Views (`inventory/views.py`)

We'll use a `TemplateView` for the main page and a `ListView` (or custom `View`) for the HTMX-driven table partial. A `FormView` could also manage the form, but a `TemplateView` combined with HTMX can effectively handle the form state.

```python
from django.views.generic import TemplateView, ListView, DetailView
from django.urls import reverse_lazy
from django.http import HttpResponse
from django.contrib import messages
from .models import Item, Category, Location
from .forms import ItemSearchForm

# Assume CompId and FinYearId are obtained from the session or a user profile.
# For demonstration, we'll hardcode or pass them via context/user.
# In a real ERP system, these would likely come from request.user.profile.comp_id and fin_year_id.
# For this example, let's assume default values or fetching from session if available.
DEFAULT_COMP_ID = 1 # Replace with actual logic to get from session/user
DEFAULT_FIN_YEAR_ID = 2024 # Replace with actual logic to get from session/user

class ItemHistoryView(TemplateView):
    """
    Main view for the Item History page.
    Displays the search form and a container for the item list.
    """
    template_name = 'inventory/item/item_history.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize form with data if available from query params
        form = ItemSearchForm(self.request.GET)
        context['form'] = form
        # Pass current query parameters to the template for HTMX
        context['current_query_params'] = self.request.GET.urlencode()
        return context

class ItemSearchFormPartialView(TemplateView):
    """
    Renders only the search form for HTMX requests to handle dynamic visibility.
    """
    template_name = 'inventory/item/_item_search_form.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize form with data from request.GET
        form = ItemSearchForm(self.request.GET)
        context['form'] = form

        # Prepare initial visibility based on ASP.NET Page_Load logic
        if not self.request.GET: # Equivalent to !IsPostBack
            context['drp_category_visible'] = False
            context['drp_search_code_visible'] = False
            context['txt_search_item_code_visible'] = False
            context['drp_location_visible'] = False
        else:
            # Replicate ASP.NET logic for dynamic visibility
            drp_type = form.cleaned_data.get('drp_type') if form.is_valid() else self.request.GET.get('drp_type')
            drp_search_code = form.cleaned_data.get('drp_search_code') if form.is_valid() else self.request.GET.get('drp_search_code')

            context['drp_category_visible'] = (drp_type == 'Category')
            context['drp_search_code_visible'] = (drp_type != 'Select')

            if drp_search_code == 'tblDG_Item_Master.Location':
                context['txt_search_item_code_visible'] = False
                context['drp_location_visible'] = (drp_type != 'Select' and drp_search_code != 'Select')
            else:
                context['txt_search_item_code_visible'] = (drp_type != 'Select' and drp_search_code != 'Select')
                context['drp_location_visible'] = False
            
            # Additional logic from DrpType_SelectedIndexChanged:
            if drp_type == 'WOItems':
                context['drp_category_visible'] = False
                context['drp_location_visible'] = False
                context['txt_search_item_code_visible'] = True # Always true for WOItems if search code selected
            elif drp_type == 'Select':
                context['drp_category_visible'] = False
                context['drp_search_code_visible'] = False
                context['txt_search_item_code_visible'] = False
                context['drp_location_visible'] = False


        return context


class ItemTablePartialView(ListView):
    """
    Renders only the item list table content for HTMX requests.
    """
    model = Item
    template_name = 'inventory/item/_item_table.html'
    context_object_name = 'items'
    paginate_by = 20 # Matches ASP.NET PageSize

    def get_queryset(self):
        # Get CompId and FinYearId from session or a default for testing
        comp_id = self.request.session.get('compid', DEFAULT_COMP_ID)
        fin_year_id = self.request.session.get('finyear', DEFAULT_FIN_YEAR_ID)

        form = ItemSearchForm(self.request.GET)
        search_params = {}
        if form.is_valid():
            search_params = form.cleaned_data
        else:
            # Fallback to raw GET data if form is not valid (e.g., initial load)
            search_params = self.request.GET.dict()

        # Use the fat model for filtering
        return Item.objects.filter_by_criteria(search_params, comp_id, fin_year_id)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass page obj for pagination in DataTables
        context['page_obj'] = context['items']
        return context

class ItemDetailView(DetailView):
    """
    Placeholder for the Item Detail View, linked from the list.
    Corresponds to ItemHistory_BOM_View.aspx
    """
    model = Item
    template_name = 'inventory/item/item_detail.html'
    context_object_name = 'item'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        messages.info(self.request, f"Displaying details for Item: {self.object.item_code}")
        return context

```

#### 4.4 Templates

The main page (`item_history.html`), the search form partial (`_item_search_form.html`), and the table partial (`_item_table.html`) will be created. The `item_detail.html` is a placeholder.

**`inventory/templates/inventory/item/item_history.html`**
This template renders the main item history page, including the dynamic search form and the container for the HTMX-loaded DataTables.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Item History</h2>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-lg mb-6">
        <table cellpadding="0" cellspacing="0" width="100%">
            <tr>
                <td align="left" valign="middle" class="bg-blue-600 text-white p-2 font-bold rounded-t">
                    &nbsp;Item History
                </td>
            </tr>
            <tr>
                <td class="p-4" id="search-form-container"
                    hx-get="{% url 'inventory:item_search_form_partial' %}"
                    hx-trigger="load, change from:input[name='drp_type'], change from:input[name='drp_category'], change from:input[name='drp_search_code']"
                    hx-swap="outerHTML">
                    <!-- The search form partial will be loaded here -->
                    <div class="text-center">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p class="mt-2">Loading search form...</p>
                    </div>
                </td>
            </tr>
        </table>
    </div>

    <div id="item-history-table-container"
         hx-get="{% url 'inventory:item_table_partial' %}?{{ current_query_params }}"
         hx-trigger="load, searchItems from:body"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading item data...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('searchLogic', () => ({
            drpType: 'Select',
            drpCategory: 'Select',
            drpSearchCode: 'Select',
            txtSearchItemCode: '',
            drpLocation: 'Select',

            init() {
                // Initialize from current query params if any
                const urlParams = new URLSearchParams(window.location.search);
                this.drpType = urlParams.get('drp_type') || 'Select';
                this.drpCategory = urlParams.get('drp_category') || 'Select';
                this.drpSearchCode = urlParams.get('drp_search_code') || 'Select';
                this.txtSearchItemCode = urlParams.get('txt_search_item_code') || '';
                this.drpLocation = urlParams.get('drp_location') || 'Select';
            },

            isCategoryVisible() {
                return this.drpType === 'Category';
            },

            isSearchCodeVisible() {
                return this.drpType !== 'Select';
            },

            isTxtSearchItemCodeVisible() {
                if (this.drpType === 'Select' || this.drpSearchCode === 'Select') return false;
                if (this.drpSearchCode === 'tblDG_Item_Master.Location') return false;
                return true;
            },

            isDrpLocationVisible() {
                return this.drpType !== 'Select' && this.drpSearchCode === 'tblDG_Item_Master.Location';
            },

            resetForm() {
                this.drpType = 'Select';
                this.drpCategory = 'Select';
                this.drpSearchCode = 'Select';
                this.txtSearchItemCode = '';
                this.drpLocation = 'Select';
                // Trigger HTMX reload of the form and table
                document.body.dispatchEvent(new Event('searchItems'));
            }
        }));
    });
</script>
{% endblock %}
```

**`inventory/templates/inventory/item/_item_search_form.html`**
This partial template contains the dynamic search form elements. It is intended to be loaded by HTMX.

```html
<div id="search-form-container" class="fontcsswhite p-4" x-data="searchLogic" x-init="init()">
    <div class="flex items-center space-x-4 flex-wrap gap-y-4">
        <div>
            <label for="drp_type" class="sr-only">Type</label>
            <select id="drp_type" name="drp_type" x-model="drpType"
                    class="{{ form.drp_type.field.widget.attrs.class }}"
                    hx-get="{% url 'inventory:item_search_form_partial' %}"
                    hx-target="#search-form-container"
                    hx-swap="outerHTML"
                    hx-trigger="change">
                <option value="Select">Select</option>
                <option value="Category" {% if form.drp_type.value == "Category" %}selected{% endif %}>Category</option>
                <option value="WOItems" {% if form.drp_type.value == "WOItems" %}selected{% endif %}>WO Items</option>
            </select>
        </div>

        <div x-show="isCategoryVisible()" x-transition>
            <label for="drp_category" class="sr-only">Category</label>
            <select id="drp_category" name="drp_category" x-model="drpCategory"
                    class="{{ form.drp_category.field.widget.attrs.class }}"
                    hx-get="{% url 'inventory:item_search_form_partial' %}"
                    hx-target="#search-form-container"
                    hx-swap="outerHTML"
                    hx-trigger="change">
                <option value="Select">Select</option>
                {% for value, label in form.drp_category.field.choices %}
                    {% if value != 'Select' %}
                        <option value="{{ value }}" {% if form.drp_category.value|stringformat:"s" == value %}selected{% endif %}>{{ label }}</option>
                    {% endif %}
                {% endfor %}
            </select>
        </div>

        <div x-show="isSearchCodeVisible()" x-transition>
            <label for="drp_search_code" class="sr-only">Search Code</label>
            <select id="drp_search_code" name="drp_search_code" x-model="drpSearchCode"
                    class="{{ form.drp_search_code.field.widget.attrs.class }}"
                    hx-get="{% url 'inventory:item_search_form_partial' %}"
                    hx-target="#search-form-container"
                    hx-swap="outerHTML"
                    hx-trigger="change">
                <option value="Select">Select</option>
                <option value="tblDG_Item_Master.ItemCode" {% if form.drp_search_code.value == "tblDG_Item_Master.ItemCode" %}selected{% endif %}>Item Code</option>
                <option value="tblDG_Item_Master.ManfDesc" {% if form.drp_search_code.value == "tblDG_Item_Master.ManfDesc" %}selected{% endif %}>Description</option>
                <option value="tblDG_Item_Master.Location" {% if form.drp_search_code.value == "tblDG_Item_Master.Location" %}selected{% endif %}>Location</option>
            </select>
        </div>
        
        <div x-show="isDrpLocationVisible()" x-transition>
            <label for="drp_location" class="sr-only">Location</label>
            <select id="drp_location" name="drp_location" x-model="drpLocation"
                    class="{{ form.drp_location.field.widget.attrs.class }}">
                <option value="Select">Select</option>
                {% for value, label in form.drp_location.field.choices %}
                    {% if value != 'Select' %}
                        <option value="{{ value }}" {% if form.drp_location.value|stringformat:"s" == value %}selected{% endif %}>{{ label }}</option>
                    {% endif %}
                {% endfor %}
            </select>
        </div>

        <div x-show="isTxtSearchItemCodeVisible()" x-transition>
            <label for="txt_search_item_code" class="sr-only">Search Item Code</label>
            <input type="text" id="txt_search_item_code" name="txt_search_item_code" x-model="txtSearchItemCode"
                   placeholder="Search Item Code"
                   class="{{ form.txt_search_item_code.field.widget.attrs.class }}">
        </div>

        <button type="button" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded redbox"
                hx-get="{% url 'inventory:item_table_partial' %}"
                hx-target="#item-history-table-container"
                hx-swap="innerHTML"
                hx-include="#search-form-container input, #search-form-container select">
            Search
        </button>
        <button type="button" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded redbox"
                @click="resetForm(); window.location.href = '{% url 'inventory:item_history' %}';">
            Clear
        </button>
    </div>
</div>
```

**`inventory/templates/inventory/item/_item_table.html`**
This partial template contains the DataTables structure for the item list. It is loaded via HTMX when the search criteria change or on initial page load.

```html
<table id="itemHistoryTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Manf Desc</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
        </tr>
    </thead>
    <tbody>
        {% if items %}
            {% for item in items %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter0|add:page_obj.start_index }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <a href="{{ item.get_absolute_url }}" class="text-blue-600 hover:underline">Select</a>
                </td>
                <td class="py-2 px-4 border-b border-gray-200">{{ item.full_category_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.item_code }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ item.manf_desc }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.uom_basic }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.full_location_display }}</td>
            </tr>
            {% endfor %}
        {% else %}
        <tr>
            <td colspan="7" class="py-4 px-4 text-center text-maroon font-bold text-lg">No data to display !</td>
        </tr>
        {% endif %}
    </tbody>
</table>

<script>
    // Initialize DataTables on the loaded table
    $(document).ready(function() {
        $('#itemHistoryTable').DataTable({
            "pageLength": 20, // Matches ASP.NET PageSize
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true
        });
    });
</script>
```

**`inventory/templates/inventory/item/item_detail.html`**
This is a placeholder for the item detail view, corresponding to `ItemHistory_BOM_View.aspx`.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6">Item Detail: {{ item.item_code }}</h2>

    <div class="bg-white p-6 rounded-lg shadow-lg">
        <dl class="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-8">
            <div class="col-span-1">
                <dt class="text-sm font-medium text-gray-500">Item ID</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ item.id }}</dd>
            </div>
            <div class="col-span-1">
                <dt class="text-sm font-medium text-gray-500">Category</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ item.full_category_display }}</dd>
            </div>
            <div class="col-span-1">
                <dt class="text-sm font-medium text-gray-500">Part No</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ item.part_no }}</dd>
            </div>
            <div class="col-span-1">
                <dt class="text-sm font-medium text-gray-500">Item Code</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ item.item_code }}</dd>
            </div>
            <div class="col-span-1">
                <dt class="text-sm font-medium text-gray-500">Manufacturer Description</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ item.manf_desc }}</dd>
            </div>
            <div class="col-span-1">
                <dt class="text-sm font-medium text-gray-500">UOM Basic</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ item.uom_basic }}</dd>
            </div>
            <div class="col-span-1">
                <dt class="text-sm font-medium text-gray-500">Location</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ item.full_location_display }}</dd>
            </div>
        </dl>
    </div>

    <div class="mt-6 flex justify-end">
        <a href="{% url 'inventory:item_history' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
            Back to Item History
        </a>
    </div>
</div>
{% endblock %}
```

#### 4.5 URLs (`inventory/urls.py`)

Define the URL patterns for the views within the `inventory` app.

```python
from django.urls import path
from .views import ItemHistoryView, ItemTablePartialView, ItemDetailView, ItemSearchFormPartialView

app_name = 'inventory' # Namespace for the app

urlpatterns = [
    path('item-history/', ItemHistoryView.as_view(), name='item_history'),
    path('item-history/table/', ItemTablePartialView.as_view(), name='item_table_partial'),
    path('item-history/search-form/', ItemSearchFormPartialView.as_view(), name='item_search_form_partial'),
    path('item-history/detail/<int:pk>/', ItemDetailView.as_view(), name='item_detail'),
]
```

#### 4.6 Tests (`inventory/tests.py`)

Comprehensive tests for models and views are crucial.

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch
from .models import Item, Category, Location, DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID

class ItemModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.category1 = Category.objects.create(cid=1, symbol='CAT-A', cname='Category A')
        cls.category2 = Category.objects.create(cid=2, symbol='CAT-B', cname='Category B')
        cls.location1 = Location.objects.create(id=1, location_label='WH1', location_no='A-01')
        cls.location2 = Location.objects.create(id=2, location_label='WH2', location_no='B-02')

        Item.objects.create(
            id=1, category=cls.category1, part_no='P001', item_code='ITEM001',
            manf_desc='Test Item Description 1', uom_basic='PCS', location=cls.location1,
            comp_id=DEFAULT_COMP_ID, fin_year_id=DEFAULT_FIN_YEAR_ID
        )
        Item.objects.create(
            id=2, category=cls.category1, part_no='P002', item_code='ITEM002',
            manf_desc='Another Test Item', uom_basic='KG', location=cls.location2,
            comp_id=DEFAULT_COMP_ID, fin_year_id=DEFAULT_FIN_YEAR_ID
        )
        Item.objects.create(
            id=3, category=cls.category2, part_no='P003', item_code='BOM001',
            manf_desc='BOM Component', uom_basic='M', location=cls.location1,
            comp_id=DEFAULT_COMP_ID, fin_year_id=DEFAULT_FIN_YEAR_ID
        )
        # Item for a different company/finyear, should not appear in default queries
        Item.objects.create(
            id=4, category=cls.category2, part_no='P004', item_code='XYZ001',
            manf_desc='Other Comp Item', uom_basic='BOX', location=cls.location1,
            comp_id=DEFAULT_COMP_ID + 1, fin_year_id=DEFAULT_FIN_YEAR_ID + 1
        )

    def test_item_creation(self):
        item = Item.objects.get(id=1)
        self.assertEqual(item.item_code, 'ITEM001')
        self.assertEqual(item.category, self.category1)
        self.assertEqual(item.location, self.location1)

    def test_item_display_properties(self):
        item = Item.objects.get(id=1)
        self.assertEqual(item.full_category_display, 'CAT-A-Category A')
        self.assertEqual(item.full_location_display, 'WH1-A-01')

    def test_filter_by_criteria_no_filter(self):
        items = Item.objects.filter_by_criteria({}, DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID)
        self.assertEqual(items.count(), 3) # Should include all items for default comp/finyear

    def test_filter_by_criteria_category_type_and_category(self):
        search_params = {'drp_type': 'Category', 'drp_category': str(self.category1.cid)}
        items = Item.objects.filter_by_criteria(search_params, DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID)
        self.assertEqual(items.count(), 2)
        self.assertTrue(all(item.category == self.category1 for item in items))

    def test_filter_by_criteria_category_type_and_item_code_search(self):
        search_params = {
            'drp_type': 'Category',
            'drp_category': str(self.category1.cid),
            'drp_search_code': 'tblDG_Item_Master.ItemCode',
            'txt_search_item_code': 'ITEM001'
        }
        items = Item.objects.filter_by_criteria(search_params, DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID)
        self.assertEqual(items.count(), 1)
        self.assertEqual(items.first().item_code, 'ITEM001')

    def test_filter_by_criteria_category_type_and_description_search(self):
        search_params = {
            'drp_type': 'Category',
            'drp_category': str(self.category1.cid),
            'drp_search_code': 'tblDG_Item_Master.ManfDesc',
            'txt_search_item_code': 'test item'
        }
        items = Item.objects.filter_by_criteria(search_params, DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID)
        self.assertEqual(items.count(), 1)
        self.assertEqual(items.first().item_code, 'ITEM001')

    def test_filter_by_criteria_category_type_and_location_search(self):
        search_params = {
            'drp_type': 'Category',
            'drp_category': str(self.category1.cid),
            'drp_search_code': 'tblDG_Item_Master.Location',
            'drp_location': str(self.location1.id)
        }
        items = Item.objects.filter_by_criteria(search_params, DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID)
        self.assertEqual(items.count(), 1)
        self.assertEqual(items.first().item_code, 'ITEM001')

    def test_filter_by_criteria_woitems_type_and_item_code_search(self):
        search_params = {
            'drp_type': 'WOItems',
            'drp_search_code': 'tblDG_Item_Master.ItemCode',
            'txt_search_item_code': 'BOM'
        }
        items = Item.objects.filter_by_criteria(search_params, DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID)
        self.assertEqual(items.count(), 1)
        self.assertEqual(items.first().item_code, 'BOM001')

    def test_filter_by_criteria_woitems_type_and_description_search(self):
        search_params = {
            'drp_type': 'WOItems',
            'drp_search_code': 'tblDG_Item_Master.ManfDesc',
            'txt_search_item_code': 'component'
        }
        items = Item.objects.filter_by_criteria(search_params, DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID)
        self.assertEqual(items.count(), 1)
        self.assertEqual(items.first().item_code, 'BOM001')
    
    def test_filter_by_criteria_select_type(self):
        search_params = {'drp_type': 'Select'}
        items = Item.objects.filter_by_criteria(search_params, DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID)
        self.assertEqual(items.count(), 3) # Should return all items for the specific company/finyear

    def test_get_absolute_url(self):
        item = Item.objects.get(id=1)
        self.assertEqual(item.get_absolute_url(), reverse('inventory:item_detail', args=[item.id]))


class ItemViewsTest(TestCase):
    client = Client()

    @classmethod
    def setUpTestData(cls):
        cls.category1 = Category.objects.create(cid=1, symbol='CAT-A', cname='Category A')
        cls.location1 = Location.objects.create(id=1, location_label='WH1', location_no='A-01')
        Item.objects.create(
            id=1, category=cls.category1, part_no='P001', item_code='ITEM001',
            manf_desc='Test Item Description 1', uom_basic='PCS', location=cls.location1,
            comp_id=DEFAULT_COMP_ID, fin_year_id=DEFAULT_FIN_YEAR_ID
        )

    def test_item_history_view_get(self):
        response = self.client.get(reverse('inventory:item_history'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/item/item_history.html')
        self.assertContains(response, 'Item History') # Check for page title

    def test_item_search_form_partial_view_initial_load(self):
        response = self.client.get(reverse('inventory:item_search_form_partial'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/item/_item_search_form.html')
        # Check initial visibility (should be hidden)
        self.assertContains(response, 'x-show="isCategoryVisible()" x-transition hidden')
        self.assertContains(response, 'x-show="isSearchCodeVisible()" x-transition hidden')
        self.assertContains(response, 'x-show="isTxtSearchItemCodeVisible()" x-transition hidden')
        self.assertContains(response, 'x-show="isDrpLocationVisible()" x-transition hidden')

    def test_item_search_form_partial_view_category_type_change(self):
        # Simulate HTMX request with drp_type='Category'
        response = self.client.get(reverse('inventory:item_search_form_partial'), {'drp_type': 'Category'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/item/_item_search_form.html')
        self.assertContains(response, 'x-show="isCategoryVisible()" x-transition') # Should be visible
        self.assertContains(response, 'x-show="isSearchCodeVisible()" x-transition') # Should be visible
        self.assertContains(response, 'x-show="isTxtSearchItemCodeVisible()" x-transition') # Should be visible
        self.assertContains(response, 'x-show="isDrpLocationVisible()" x-transition hidden') # Should be hidden

    def test_item_table_partial_view_get_initial(self):
        response = self.client.get(reverse('inventory:item_table_partial'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/item/_item_table.html')
        self.assertContains(response, 'ITEM001') # Check if items are loaded

    def test_item_table_partial_view_with_search_params(self):
        query_params = {
            'drp_type': 'Category',
            'drp_category': str(self.category1.cid),
            'drp_search_code': 'tblDG_Item_Master.ItemCode',
            'txt_search_item_code': 'ITEM001'
        }
        response = self.client.get(reverse('inventory:item_table_partial'), query_params, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/item/_item_table.html')
        self.assertContains(response, 'ITEM001')
        self.assertNotContains(response, 'Another Test Item') # Ensure filtering works

    def test_item_detail_view(self):
        item = Item.objects.get(id=1)
        response = self.client.get(reverse('inventory:item_detail', args=[item.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/item/item_detail.html')
        self.assertContains(response, 'Item Detail: ITEM001')
        self.assertContains(response, 'Test Item Description 1')

    # Mock session for comp_id and fin_year_id if they were dynamically set in views
    @patch('django.contrib.sessions.backends.db.SessionStore.get')
    def test_item_table_partial_view_with_session_data(self, mock_session_get):
        # Simulate session data
        mock_session_get.side_effect = lambda key, default: {
            'compid': 99,
            'finyear': 2023
        }.get(key, default)

        # Create an item for the specific session comp_id/fin_year_id
        Item.objects.create(
            id=5, category=self.category1, item_code='SESSION_ITEM', manf_desc='Session controlled item',
            comp_id=99, fin_year_id=2023
        )

        response = self.client.get(reverse('inventory:item_table_partial'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'SESSION_ITEM')
        self.assertNotContains(response, 'ITEM001') # Should not be visible with different comp/finyear


```

### Step 5: HTMX and Alpine.js Integration

The HTMX and Alpine.js integration is embedded directly within the templates and view logic:

*   **HTMX for dynamic updates:**
    *   The `item_history.html` template uses `hx-get` to load `_item_search_form.html` on `load` and `change` of search form fields. This allows dynamic adjustments of form field visibility based on previous selections (like `DrpType` determining `DrpCategory` visibility).
    *   The `_item_search_form.html` uses `hx-get` on its own `change` events to re-render itself, effectively updating the visibility of dependent fields (e.g., `txtSearchItemCode` vs. `drp_location`).
    *   The "Search" button uses `hx-get` to `item_table_partial` and `hx-include` to send all current form parameters.
    *   The `item-history-table-container` in `item_history.html` uses `hx-trigger="load, searchItems from:body"` to load the table data initially and refresh it when a search is triggered (via `searchItems` custom event dispatched by the search button).
*   **Alpine.js for UI state management:**
    *   `_item_search_form.html` uses `x-data` and `x-show` directives to toggle the visibility of `drp_category`, `drp_search_code`, `txt_search_item_code`, and `drp_location` fields based on the selected values. This replicates the C# code's dynamic `Visible = true/false` logic.
    *   Alpine.js's `x-model` keeps the form fields' values synced with the component's state, simplifying form management.
    *   The `resetForm()` function in Alpine.js handles clearing the form inputs and triggering an HTMX reload of the table.
*   **DataTables for list views:**
    *   The `_item_table.html` partial includes the JavaScript initialization for DataTables (`$('#itemHistoryTable').DataTable(...)`). This ensures that the newly loaded table content is immediately enhanced with DataTables features like pagination, searching, and sorting.
*   **No full page reloads:** All search and table updates occur via HTMX requests, preventing full page refreshes and providing a smooth user experience.

### Final Notes

*   **Placeholders:** `DEFAULT_COMP_ID` and `DEFAULT_FIN_YEAR_ID` should be replaced with actual logic to retrieve `CompId` and `FinYearId` from the Django session or the authenticated user's profile, mirroring the ASP.NET `Session["compid"]` and `Session["finyear"]` behavior.
*   **Error Handling:** While the C# code uses `try-catch` blocks, Django handles exceptions gracefully. Custom error pages and user feedback mechanisms can be integrated.
*   **Database Schema Review:** A thorough review of the actual `tblDG_Item_Master`, `tblDG_Category_Master`, and `tblDG_Location_Master` schemas, including data types and relationships, is crucial for accurate Django model mapping.
*   **Security:** Ensure proper authentication and authorization are implemented in Django, building upon existing patterns in the `core` app if applicable.
*   **CSS:** The provided CSS classes `box3`, `redbox`, `fontcsswhite`, `fontcss` are placeholders. Tailwind CSS classes are used in the generated HTML to achieve a modern, responsive design. You'd replace/map these to your actual Tailwind setup.
*   **Testing:** The provided tests cover basic model functionality and view integration. Real-world implementation will require more exhaustive testing, including edge cases, validation, and performance benchmarks.