## ASP.NET to Django Conversion Script: Image Serving Module

This modernization plan focuses on transitioning your existing ASP.NET image serving functionality to a robust and scalable Django application. The original ASP.NET code was designed to retrieve an image directly from the database and send it as a binary response. Our Django approach will replicate this core functionality while also demonstrating how such images can be seamlessly integrated into a modern web interface using HTMX and Alpine.js.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

## Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
The ASP.NET code directly queries `tblDG_Item_Master` for an `Image` column based on an `Id`. Although not explicitly used in the `Response.BinaryWrite` for `ContentType` and `Name`, these columns were commented out, indicating their potential existence in the table and relevance to image metadata.

*   **Table Name:** `tblDG_Item_Master`
*   **Columns Identified:**
    *   `Id` (used in `WHERE Id=60`, likely the primary key)
    *   `Image` (the binary image data itself)
    *   `ContentType` (inferred from commented `Response.ContentType` line)
    *   `Name` (inferred from commented `Response.AddHeader("content-disposition", "attachment;filename=") ` line)

## Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
The provided ASP.NET code snippet's primary function is to **read** an image from the database and **serve** it as a binary stream. It does not perform any Create, Update, or Delete operations. It acts purely as an image delivery endpoint. Therefore, the Django implementation will focus on retrieving and serving the image efficiently. While we will demonstrate how to list images for a more complete Django application example, the direct translation of the `img.aspx` functionality is a read/serve operation.

*   **Read:** The `SELECT Image from tblDG_Item_Master where Id=60` query is a read operation.
*   **Serve:** `Response.BinaryWrite(bytes)` serves the retrieved image data.
*   **Validation:** No explicit validation logic was present in the ASP.NET code for this image serving function.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The `.aspx` file provided is minimal, containing only a `form` and a `div` element, without any visible ASP.NET controls (like `GridView`, `TextBox`, `Button`). The `img.aspx` page itself was designed to *be* the image (i.e., its output is binary image data, not HTML with image tags). This means there were no direct UI components on this page to display.

For our Django modernization, we will create a list view to display how these images would be presented within a standard web page, effectively demonstrating the consumption of the image serving endpoint. This list view will then use HTMX and DataTables for a rich user experience, aligning with modern web patterns.

## Step 4: Generate Django Code

We will create a Django application named `media` (or `images`, depending on your preference) for this functionality.

### 4.1 Models

**Task:** Create a Django model based on the database schema.

**Instructions:**
We will create an `ItemMaster` model that maps to `tblDG_Item_Master`. The `Image` column will be represented by a `BinaryField` in Django, which is suitable for storing raw binary data directly in the database.

```python
# media/models.py
from django.db import models

class ItemMaster(models.Model):
    # Django automatically creates an 'id' field as the primary key.
    # If your existing 'Id' column is NOT auto-incrementing or named differently,
    # you might need to specify it using primary_key=True and db_column='Id'.
    # Assuming 'Id' is the auto-incrementing primary key mapped to Django's default 'id'.

    image = models.BinaryField(db_column='Image', help_text="Binary image data")
    content_type = models.CharField(
        db_column='ContentType',
        max_length=50,
        blank=True,
        null=True,
        default='image/jpeg', # Provide a reasonable default if not always stored
        help_text="MIME type of the image (e.g., image/jpeg, image/png)"
    )
    name = models.CharField(
        db_column='Name',
        max_length=255,
        blank=True,
        null=True,
        help_text="Original filename of the image"
    )

    class Meta:
        managed = False  # Set to False as the table already exists
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master Image'
        verbose_name_plural = 'Item Master Images'

    def __str__(self):
        return self.name if self.name else f"Image ID: {self.id}"

    def get_image_url(self):
        """Returns the URL to serve this image."""
        from django.urls import reverse
        return reverse('media:image_serve', args=[self.id])

    def get_thumbnail_url(self):
        """Returns a URL for a thumbnail, if different (e.g., for list view).
        For simplicity, this example reuses the main image URL.
        In a real app, you might have a dedicated thumbnail generation service or field.
        """
        return self.get_image_url()

    # No specific business logic needed for this simple image serving model.
    # If you had complex image processing or validation, it would go here.
```

### 4.2 Forms

**Task:** Define a Django form for user input.

**Instructions:**
The original ASP.NET `img.aspx` was solely for serving an image, not for user input. Therefore, a form is not directly applicable to its core functionality. However, for a complete Django application, you might want to allow users to upload new images. Below is a placeholder for a form that could be used for image upload, demonstrating how it would integrate if such functionality were added.

```python
# media/forms.py
from django import forms
from .models import ItemMaster

class ItemMasterForm(forms.ModelForm):
    # This form is for demonstrating image upload if needed, not directly for img.aspx replacement.
    # The 'image' field is typically handled via file upload widgets in real applications.
    # For a BinaryField, you might receive a file and then read its bytes.
    uploaded_image = forms.FileField(
        label="Upload Image File",
        required=False, # Make required if this is a creation form
        widget=forms.FileInput(attrs={'class': 'block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'})
    )

    class Meta:
        model = ItemMaster
        # Exclude 'image' from fields as we handle it via 'uploaded_image'
        # Include 'content_type' and 'name' if you want them editable directly.
        fields = ['name', 'content_type']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'content_type': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        
    def save(self, commit=True):
        instance = super().save(commit=False)
        uploaded_file = self.cleaned_data.get('uploaded_image')
        if uploaded_file:
            instance.image = uploaded_file.read()
            # Try to infer content_type from uploaded file if not set
            if not instance.content_type:
                instance.content_type = uploaded_file.content_type
            if not instance.name:
                instance.name = uploaded_file.name
        if commit:
            instance.save()
        return instance

```

### 4.3 Views

**Task:** Implement image serving and list display operations using CBVs.

**Instructions:**
We will define two main views:
1.  `ImageServeView`: This view directly translates the `img.aspx` functionality, serving the binary image data.
2.  `ItemMasterListView`: This view will display a list of all images available, demonstrating how the `ImageServeView` can be consumed in a web page, and providing a clean UI for managing images (even if CRUD isn't the primary focus of the original ASP.NET code). We'll also include a `TablePartialView` for HTMX.

```python
# media/views.py
from django.views.generic import ListView, View, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404
from .models import ItemMaster
from .forms import ItemMasterForm
import mimetypes

# 1. View to serve the raw image data (direct replacement for img.aspx)
class ImageServeView(View):
    """
    Serves binary image data directly from the database, mimicking img.aspx.
    """
    def get(self, request, pk, *args, **kwargs):
        try:
            item = ItemMaster.objects.get(pk=pk)
            image_data = item.image
            content_type = item.content_type if item.content_type else (mimetypes.guess_type(item.name)[0] if item.name else 'application/octet-stream')
            
            # Ensure proper content type for image display
            if not content_type.startswith('image/'):
                 content_type = 'image/jpeg' # Fallback to a common image type

            response = HttpResponse(image_data, content_type=content_type)
            
            # Optionally add content-disposition header if you want it to download
            # response['Content-Disposition'] = f'inline; filename="{item.name or "image.jpg"}"'
            
            return response
        except ItemMaster.DoesNotExist:
            raise Http404("Image not found.")

# 2. Views for displaying/managing images in a UI (modern Django addition)
class ItemMasterListView(ListView):
    """
    Displays a list of all ItemMaster images.
    """
    model = ItemMaster
    template_name = 'media/itemmaster/list.html'
    context_object_name = 'images' # Renamed for clarity in context

class ItemMasterTablePartialView(ListView):
    """
    Returns the HTML fragment for the image table, used by HTMX.
    """
    model = ItemMaster
    template_name = 'media/itemmaster/_itemmaster_table.html'
    context_object_name = 'images' # Renamed for clarity in context

class ItemMasterCreateView(CreateView):
    """
    Handles creating a new ItemMaster record (image upload).
    """
    model = ItemMaster
    form_class = ItemMasterForm
    template_name = 'media/itemmaster/form.html'
    success_url = reverse_lazy('media:itemmaster_list') # Redirects to the list page

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Image added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content to indicate success to HTMX
                headers={
                    'HX-Trigger': 'refreshItemMasterList' # Custom event to refresh table
                }
            )
        return response

class ItemMasterUpdateView(UpdateView):
    """
    Handles updating an existing ItemMaster record.
    """
    model = ItemMaster
    form_class = ItemMasterForm
    template_name = 'media/itemmaster/form.html'
    success_url = reverse_lazy('media:itemmaster_list')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # Do not load binary data into form if it's not being re-uploaded
        # self.object.image = None # Or handle differently based on specific upload logic
        return kwargs

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Image updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshItemMasterList'
                }
            )
        return response

class ItemMasterDeleteView(DeleteView):
    """
    Handles deleting an ItemMaster record.
    """
    model = ItemMaster
    template_name = 'media/itemmaster/confirm_delete.html'
    success_url = reverse_lazy('media:itemmaster_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Image deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshItemMasterList'
                }
            )
        return response

```

### 4.4 Templates

**Task:** Create templates for each view.

**Instructions:**
Since the original `img.aspx` directly served binary content, there is no direct HTML template for it. However, to integrate this functionality into a modern Django application and demonstrate DataTables, HTMX, and Alpine.js, we will create templates for:

*   `list.html`: The main page to display a list of `ItemMaster` images.
*   `_itemmaster_table.html`: A partial template containing the DataTables table, loaded via HTMX.
*   `form.html`: A partial template for adding/editing images via modal.
*   `confirm_delete.html`: A partial template for confirming deletion via modal.

```html
{# media/templates/media/itemmaster/list.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Item Master Images</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'media:itemmaster_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Image
        </button>
    </div>
    
    <div id="itemmasterTable-container"
         hx-trigger="load, refreshItemMasterList from:body"
         hx-get="{% url 'media:itemmaster_table_partial' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading images...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me
            on keydown.escape remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full max-h-screen overflow-y-auto"
             hx-target="this" hx-swap="outerHTML">
            <!-- Content will be loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for UI state management.
        // Example: managing modal visibility if not fully handled by HTMX/_
        Alpine.data('modal', () => ({
            isOpen: false,
            open() { this.isOpen = true },
            close() { this.isOpen = false },
        }));
    });

    // Ensure DataTables are re-initialized after HTMX loads content
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'itemmasterTable-container') {
            $('#itemmasterTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
            });
        }
    });

    // Close modal on successful form submission via HTMX
    document.body.addEventListener('htmx:configRequest', function(evt) {
        // If a form inside the modal is submitted
        if (evt.detail.target.closest('#modalContent form')) {
            evt.detail.requestConfig.headers['HX-Trigger'] = 'refreshItemMasterList'; // Ensure list refreshes
            // This event happens before request, so we can't remove modal here.
            // Removal should be handled by HX-Trigger-After-Swap on a 204 response.
        }
    });
</script>
{% endblock %}
```

```html
{# media/templates/media/itemmaster/_itemmaster_table.html #}
{# This template is loaded via HTMX into list.html #}
<div class="overflow-x-auto shadow-md sm:rounded-lg">
    <table id="itemmasterTable" class="min-w-full bg-white text-sm text-left text-gray-700">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-6">SN</th>
                <th scope="col" class="py-3 px-6">Preview</th>
                <th scope="col" class="py-3 px-6">Name</th>
                <th scope="col" class="py-3 px-6">Content Type</th>
                <th scope="col" class="py-3 px-6">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for obj in images %}
            <tr class="bg-white border-b hover:bg-gray-50">
                <td class="py-4 px-6 font-medium text-gray-900 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-4 px-6">
                    <img src="{{ obj.get_image_url }}" alt="Image Preview" class="w-16 h-16 object-cover rounded-md border border-gray-200">
                </td>
                <td class="py-4 px-6">{{ obj.name|default:"N/A" }}</td>
                <td class="py-4 px-6">{{ obj.content_type|default:"N/A" }}</td>
                <td class="py-4 px-6">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2 text-xs"
                        hx-get="{% url 'media:itemmaster_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs"
                        hx-get="{% url 'media:itemmaster_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5" class="py-4 px-6 text-center text-gray-500">No images found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // DataTables initialization must happen after the table is in the DOM
    // This script block ensures it runs when _itemmaster_table.html is swapped in.
    $(document).ready(function() {
        $('#itemmasterTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "searching": true,
            "ordering": true,
            "info": true
        });
    });
</script>
```

```html
{# media/templates/media/itemmaster/form.html #}
{# This is a partial template for HTMX modal forms #}
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Image</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" 
          _="on hx-trigger[refreshItemMasterList] remove .is-active from #modal">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
            <div class="mb-4">
                <label for="{{ form.uploaded_image.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.uploaded_image.label }}
                </label>
                {{ form.uploaded_image }}
                {% if form.uploaded_image.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.uploaded_image.errors }}</p>
                {% endif %}
                <p class="mt-1 text-sm text-gray-500">
                    {% if form.instance.pk %}
                    Leave blank to keep current image. Upload new file to replace.
                    {% else %}
                    Upload an image file (e.g., .jpg, .png).
                    {% endif %}
                </p>
            </div>
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

```html
{# media/templates/media/itemmaster/confirm_delete.html #}
{# This is a partial template for HTMX modal confirmations #}
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the image "{{ object.name|default:'(Unnamed Image)' }}" (ID: {{ object.pk }})?</p>
    
    <form hx-post="{% url 'media:itemmaster_delete' object.pk %}" hx-swap="none"
          _="on hx-trigger[refreshItemMasterList] remove .is-active from #modal">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
We'll define URLs for the image serving endpoint and the CRUD operations for managing images. We'll use a `media` namespace for clarity.

```python
# media/urls.py
from django.urls import path
from .views import (
    ImageServeView,
    ItemMasterListView,
    ItemMasterTablePartialView,
    ItemMasterCreateView,
    ItemMasterUpdateView,
    ItemMasterDeleteView,
)

app_name = 'media' # Namespace for this app's URLs

urlpatterns = [
    # URL for serving the raw image data (direct replacement for img.aspx)
    path('serve/<int:pk>/', ImageServeView.as_view(), name='image_serve'),

    # URLs for the image management UI
    path('images/', ItemMasterListView.as_view(), name='itemmaster_list'),
    path('images/table/', ItemMasterTablePartialView.as_view(), name='itemmaster_table_partial'),
    path('images/add/', ItemMasterCreateView.as_view(), name='itemmaster_add'),
    path('images/<int:pk>/edit/', ItemMasterUpdateView.as_view(), name='itemmaster_edit'),
    path('images/<int:pk>/delete/', ItemMasterDeleteView.as_view(), name='itemmaster_delete'),
]

# In your project's main urls.py (e.g., myproject/urls.py), include this:
# from django.urls import path, include
# urlpatterns = [
#     path('media/', include('media.urls')),
#     # ... other project urls
# ]
```

### 4.6 Tests

**Task:** Write tests for the model and views.

**Instructions:**
We will include comprehensive unit tests for the `ItemMaster` model and integration tests for all implemented views, ensuring proper functionality and interaction, especially for the `ImageServeView` which is the direct translation of the original ASP.NET code.

```python       
# media/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import ItemMaster
from django.http import HttpResponse
from io import BytesIO
from PIL import Image # Pillow for creating dummy image data

class ItemMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a dummy image in memory
        dummy_image = Image.new('RGB', (100, 50), color = 'red')
        img_byte_arr = BytesIO()
        dummy_image.save(img_byte_arr, format='JPEG')
        img_byte_arr = img_byte_arr.getvalue()

        # Create test data for all tests
        ItemMaster.objects.create(
            id=60, # Matching the original ASP.NET hardcoded ID
            image=img_byte_arr,
            name='Test Image 60',
            content_type='image/jpeg'
        )
        ItemMaster.objects.create(
            id=61,
            image=img_byte_arr,
            name='Another Test Image',
            content_type='image/png'
        )
  
    def test_itemmaster_creation(self):
        obj = ItemMaster.objects.get(id=60)
        self.assertEqual(obj.name, 'Test Image 60')
        self.assertEqual(obj.content_type, 'image/jpeg')
        self.assertIsInstance(obj.image, bytes) # Ensure it's stored as bytes

    def test_get_image_url(self):
        obj = ItemMaster.objects.get(id=60)
        expected_url = reverse('media:image_serve', args=[obj.id])
        self.assertEqual(obj.get_image_url(), expected_url)
        
    def test_str_method(self):
        obj_named = ItemMaster.objects.get(id=60)
        self.assertEqual(str(obj_named), 'Test Image 60')
        
        # Test with no name
        obj_no_name = ItemMaster.objects.create(image=b'dummy', id=62)
        self.assertEqual(str(obj_no_name), 'Image ID: 62')


class ItemMasterViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a dummy image in memory
        dummy_image = Image.new('RGB', (100, 50), color = 'blue')
        img_byte_arr = BytesIO()
        dummy_image.save(img_byte_arr, format='PNG')
        cls.dummy_image_bytes = img_byte_arr.getvalue()

        # Create test data
        ItemMaster.objects.create(
            id=1,
            image=cls.dummy_image_bytes,
            name='View Test Image 1',
            content_type='image/png'
        )
        ItemMaster.objects.create(
            id=2,
            image=cls.dummy_image_bytes,
            name='View Test Image 2',
            content_type='image/jpeg'
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_image_serve_view_success(self):
        obj = ItemMaster.objects.get(id=1)
        response = self.client.get(reverse('media:image_serve', args=[obj.id]))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'image/png')
        self.assertEqual(response.content, obj.image)
        
    def test_image_serve_view_not_found(self):
        response = self.client.get(reverse('media:image_serve', args=[999])) # Non-existent ID
        self.assertEqual(response.status_code, 404)

    def test_itemmaster_list_view(self):
        response = self.client.get(reverse('media:itemmaster_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'media/itemmaster/list.html')
        self.assertTrue('images' in response.context)
        self.assertEqual(response.context['images'].count(), 2)
        
    def test_itemmaster_table_partial_view(self):
        response = self.client.get(reverse('media:itemmaster_table_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'media/itemmaster/_itemmaster_table.html')
        self.assertTrue('images' in response.context)
        self.assertEqual(response.context['images'].count(), 2)
        self.assertContains(response, '<table id="itemmasterTable"')

    def test_itemmaster_create_view_get(self):
        response = self.client.get(reverse('media:itemmaster_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'media/itemmaster/form.html')
        self.assertTrue('form' in response.context)
        
    def test_itemmaster_create_view_post(self):
        # Create a new dummy image for upload
        new_dummy_image = Image.new('RGB', (80, 40), color = 'green')
        new_img_byte_arr = BytesIO()
        new_dummy_image.save(new_img_byte_arr, format='JPEG')
        new_img_byte_arr.seek(0) # Rewind for reading

        data = {
            'name': 'New Uploaded Image',
            'content_type': 'image/jpeg',
            'uploaded_image': new_img_byte_arr,
        }
        # Simulate HTMX request
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('media:itemmaster_add'), data, **headers)
        
        self.assertEqual(response.status_code, 204) # HTMX success with no content
        self.assertTrue(ItemMaster.objects.filter(name='New Uploaded Image').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshItemMasterList')

    def test_itemmaster_update_view_get(self):
        obj = ItemMaster.objects.get(id=1)
        response = self.client.get(reverse('media:itemmaster_edit', args=[obj.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'media/itemmaster/form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, obj)

    def test_itemmaster_update_view_post(self):
        obj = ItemMaster.objects.get(id=1)
        updated_name = 'Updated Image Name'
        data = {
            'name': updated_name,
            'content_type': 'image/gif', # Can change content type
            # No 'uploaded_image' means original image is kept
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('media:itemmaster_edit', args=[obj.id]), data, **headers)
        
        self.assertEqual(response.status_code, 204)
        obj.refresh_from_db()
        self.assertEqual(obj.name, updated_name)
        self.assertEqual(obj.content_type, 'image/gif')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshItemMasterList')

    def test_itemmaster_delete_view_get(self):
        obj = ItemMaster.objects.get(id=1)
        response = self.client.get(reverse('media:itemmaster_delete', args=[obj.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'media/itemmaster/confirm_delete.html')
        self.assertTrue('object' in response.context)

    def test_itemmaster_delete_view_post(self):
        obj = ItemMaster.objects.get(id=2)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('media:itemmaster_delete', args=[obj.id]), **headers)
        
        self.assertEqual(response.status_code, 204)
        self.assertFalse(ItemMaster.objects.filter(id=obj.id).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshItemMasterList')

```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**
The Django conversion leverages HTMX for all dynamic UI interactions and Alpine.js for localized UI state management (though less critical for this specific module as HTMX handles most of the dynamic loading). DataTables is integrated for a rich list view experience.

*   **HTMX for Modals:** The "Add New Image", "Edit", and "Delete" buttons on the list view use `hx-get` to fetch their respective forms/confirmations into a modal (`#modalContent`).
*   **HTMX for Table Refresh:** After a successful form submission (create/update/delete), the view returns a `204 No Content` status with an `HX-Trigger: refreshItemMasterList` header. This custom event is caught by the `#itemmasterTable-container` element, which then re-fetches its content via `hx-get="{% url 'media:itemmaster_table_partial' %}"`, refreshing the DataTables table without a full page reload.
*   **DataTables:** The `_itemmaster_table.html` partial includes a `$(document).ready` script to initialize DataTables on the rendered table. This ensures DataTables features like searching, sorting, and pagination are enabled dynamically when the table content is loaded via HTMX.
*   **Alpine.js for Modal State:** While `_` (hyperscript) is used to toggle the `is-active` class on the `#modal` for simple visibility, Alpine.js can be used for more complex modal behaviors (e.g., managing multiple modals, specific state within a modal, etc.). A placeholder for `alpine:init` is provided in `list.html`.
*   **No Custom JavaScript:** Beyond the DataTables initialization script and the basic Alpine.js setup, no additional custom JavaScript is required for the dynamic CRUD operations or the image serving. All interactions are handled by HTMX and simple DOM manipulation with `_`.

## Final Notes

*   **Project Setup:** Remember to add `media` to your `INSTALLED_APPS` in `settings.py` and include the `media.urls` in your project's `urls.py`. Ensure your `DATABASES` setting correctly points to your existing SQL Server database (assuming `LocalSqlServer` connection string maps to it).
*   **Database Connectivity:** Django uses `django-mssql-backend` (or similar) to connect to SQL Server. Ensure this library is installed and configured correctly.
*   **Image Handling (BinaryField):** Storing images directly in `BinaryField` is suitable for small to medium-sized images or specific scenarios like the original ASP.NET code. For large-scale image management, consider using Django's `ImageField` which typically stores files on the filesystem (e.g., S3, local storage) and saves paths in the database. This pattern is more common for scalability and performance. This migration strictly followed the original ASP.NET pattern of database storage.
*   **Error Handling:** The `ImageServeView` includes a basic `Http404` for missing images. More robust error handling and logging would be added in a production application.
*   **Security:** Ensure proper authentication and authorization are implemented for accessing and managing images, as this plan focuses on the functional migration.
*   **Tailwind CSS:** All generated HTML includes Tailwind CSS classes. You will need to have Tailwind CSS correctly set up in your Django project to render these styles.
*   **DRY Principle:** The use of partial templates (`_itemmaster_table.html`, `form.html`, `confirm_delete.html`) and the `get_image_url` method in the model adheres to DRY principles, ensuring reusable components and logic.