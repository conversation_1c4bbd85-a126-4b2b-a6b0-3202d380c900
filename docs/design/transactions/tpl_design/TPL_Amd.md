## ASP.NET to Django Modernization Plan: TPL Item Amendment Listing

This document outlines a comprehensive plan to modernize the ASP.NET TPL Item Amendment listing page to a robust, maintainable, and user-friendly Django application. Our approach leverages AI-assisted automation, focusing on modern Django 5.0+ practices, and a highly interactive frontend using HTMX and Alpine.js, powered by DataTables for efficient data presentation.

This modernization effort will deliver a significant improvement in application performance, developer productivity, and user experience, moving away from legacy ASP.NET Web Forms to a scalable and contemporary web stack.

### Business Value & Outcomes:

*   **Enhanced Performance:** Users will experience faster page loads and highly responsive interactions as data updates are managed dynamically without full page reloads, thanks to HTMX and DataTables.
*   **Modern User Experience:** The updated interface, powered by Tailwind CSS, HTMX, and Alpine.js, will provide a slicker, more intuitive experience akin to modern web applications. DataTables offers powerful client-side search, sort, and pagination out-of-the-box.
*   **Reduced Development Costs:** By adopting Django's "Fat Model, Thin View" architecture, business logic is centralized and reusable, making features easier to build, debug, and maintain. Automation tools will handle much of the boilerplate code generation.
*   **Improved Maintainability & Scalability:** Django's structured approach and clear separation of concerns (models for data, views for presentation logic, templates for UI) lead to a codebase that is easier to understand, extend, and scale as your business grows.
*   **Future-Proof Technology:** Migrating to Django ensures your application is built on a widely adopted, actively maintained, and secure open-source framework, reducing vendor lock-in and increasing flexibility.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:

*   NEVER include `base.html` template code in your output - assume it already exists and is extended.
*   Focus ONLY on component-specific code for the current module (`transactions` app).
*   Always include complete unit tests for models and integration tests for views.
*   Use modern Django 5.0+ patterns and follow best practices.
*   Keep your code clean, efficient, and avoid redundancy.
*   Always generate complete, runnable Django code.

### AutoERP Guidelines:

*   Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
*   Map models to existing database using `managed = False` and `db_table`.
*   Implement DataTables for client-side searching, sorting, and pagination.
*   Use HTMX for dynamic interactions and Alpine.js for UI state management.
*   All templates should extend `core/base.html` (but DO NOT include `base.html` code).
*   Achieve at least 80% test coverage with unit and integration tests.
*   Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
*   Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code interacts with multiple database tables to construct the amendment listing.
The primary table for this view is `tblDG_TPL_Amd`.
Supporting tables include `tblDG_Item_Master`, `Unit_Master`, and `tblHR_OfficeStaff`.

**Extracted Tables & Columns:**

*   **Primary Table: `tblDG_TPL_Amd`**
    *   `Id` (Primary Key, Integer)
    *   `SysDate` (Date)
    *   `SysTime` (Time)
    *   `WONo` (String)
    *   `AmdNo` (String)
    *   `ItemId` (Integer, Foreign Key to `tblDG_Item_Master.Id`)
    *   `Description` (String, nullable, used if specific to this amendment, otherwise defaults to item master description)
    *   `UOM` (Integer, Foreign Key to `Unit_Master.Id`, nullable, used if specific to this amendment, otherwise defaults to item master UOM)
    *   `Qty` (Decimal/Float)
    *   `SessionId` (Integer, Foreign Key to `tblHR_OfficeStaff.EmpId`, representing 'Amended By' user)
    *   `TPLId` (Integer, likely a foreign key to a parent TPL record)
    *   `FinYearId` (Integer, used for filtering)
    *   `CompId` (Integer, used for filtering)

*   **Supporting Table: `tblDG_Item_Master`**
    *   `Id` (Primary Key, Integer)
    *   `ItemCode` (String)
    *   `UOMBasic` (Integer, Foreign Key to `Unit_Master.Id`)
    *   `ManfDesc` (String)

*   **Supporting Table: `Unit_Master`**
    *   `Id` (Primary Key, Integer)
    *   `Symbol` (String)

*   **Supporting Table: `tblHR_OfficeStaff`**
    *   `EmpId` (Primary Key, Integer)
    *   `Title` (String)
    *   `EmployeeName` (String)

### Step 2: Identify Backend Functionality

**Task:** Determine the operations performed by the ASP.NET code.

**Analysis:**
The provided ASP.NET page is primarily a **Read** operation (listing and viewing amendments).

*   **Read (List):** The `GridView2` populates a list of "TPL Item Amendments" based on `WONo`, `ItemId`, `TPLId`, `FinYearId`, and `CompId` passed through query strings and session variables. It performs joins across multiple tables to display comprehensive details (SysDate, SysTime, WONo, AmdNo, Item Code, Description, UOM, Qty, Amd By). Pagination is handled server-side in ASP.NET but will be client-side with DataTables in Django.
*   **No Direct Create/Update/Delete:** This specific ASP.NET page does not contain explicit forms or controls for creating, updating, or deleting individual TPL amendment records. The "Cancel" button redirects to another page. While the general plan requires CRUD, for this specific module, the focus will be on the list view. However, to adhere to the template's requirement, generic CRUD views and forms will be provided for potential future expansion or if they exist elsewhere in the original ASP.NET system.
*   **Filtering Logic:** The `BindDataCust` method applies filters using `FinYearId`, `CompId`, `TPLId`, `WONo`, and `ItemId`. These parameters will be handled by Django view logic, likely from URL query parameters.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**

*   **`asp:Label ID="lblWONo"`:** Displays the Work Order Number from the URL query string (`WONo`). This will be part of the Django template context.
*   **`asp:GridView ID="GridView2"`:** The central component for displaying a paginated list of amendment records.
    *   Columns displayed: SN (row number), Date (SysDate), Time (SysTime), WONo, Id (hidden), Amendment No, Item Code, Description, UOM, Qty, Amd by.
    *   Features: Paging (`AllowPaging="True"`, `PageSize="17"`), styling (`CssClass="yui-datatable-theme"`).
    *   Empty data template: Displays "No data to display !".
    *   This will be replaced by a Django template rendering an HTML table, transformed into a DataTables instance on the client-side.
*   **`asp:Button ID="Button2"`:** "Cancel" button, redirects to another page (`TPL_Design_WO_TreeView_Edit.aspx`). In Django, this will be a link or button triggering a client-side redirect or `hx-push-url`.
*   **Styling & Scripts:** `yui-datatable.css`, `StyleSheet.css`, `PopUpMsg.js`, `loadingNotifier.js`. These will be completely replaced by Tailwind CSS for styling, HTMX for dynamic interactions, Alpine.js for simple client-side state, and DataTables for grid functionality, using CDN links placed in the base template.

### Step 4: Generate Django Code

We will create a Django application named `transactions` to house this module.

#### 4.1 Models (`transactions/models.py`)

Here, we define the Django models that map directly to the identified database tables. We'll include helper properties on `TplAmendment` to encapsulate the complex data retrieval logic previously handled in the ASP.NET `BindDataCust` method, adhering to the "Fat Model" principle.

```python
from django.db import models
from django.utils import timezone

# Assuming these models exist elsewhere or are part of the current migration scope
# For simplicity, they are defined here as managed=False, linking to existing DB tables.

class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50) # Assuming max_length
    # Add other fields as per your Unit_Master table schema

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=100) # Assuming max_length
    uom_basic = models.ForeignKey(UnitMaster, on_delete=models.DO_NOTHING, db_column='UOMBasic', related_name='basic_items')
    manf_desc = models.TextField(db_column='ManfDesc', null=True, blank=True) # Assuming TextField for description
    # Add other fields as per your tblDG_Item_Master table schema

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.item_code

class OfficeStaff(models.Model):
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    title = models.CharField(db_column='Title', max_length=50, null=True, blank=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255) # Assuming max_length
    # Add other fields as per your tblHR_OfficeStaff table schema

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title or ''} {self.employee_name}".strip()

class TplAmendment(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    wo_no = models.CharField(db_column='WONo', max_length=100) # Assuming max_length
    amd_no = models.CharField(db_column='AmdNo', max_length=100) # Assuming max_length
    item = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId')
    description_override = models.TextField(db_column='Description', null=True, blank=True) # Renamed to avoid conflict with method
    uom_override = models.ForeignKey(UnitMaster, on_delete=models.DO_NOTHING, db_column='UOM', null=True, blank=True, related_name='amendment_uom')
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=4) # Assuming decimal precision
    session_user = models.ForeignKey(OfficeStaff, on_delete=models.DO_NOTHING, db_column='SessionId')
    tpl_id = models.IntegerField(db_column='TPLId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblDG_TPL_Amd'
        verbose_name = 'TPL Amendment'
        verbose_name_plural = 'TPL Amendments'

    def __str__(self):
        return f"Amendment {self.amd_no} for WO {self.wo_no} (Item: {self.item.item_code})"

    @property
    def display_date(self):
        """Combines SysDate and SysTime for display."""
        return self.sys_date.strftime("%d-%m-%Y") # ASP.NET used DMY

    @property
    def display_time(self):
        """Formats SysTime for display."""
        return self.sys_time.strftime("%H:%M:%S")

    @property
    def item_code_display(self):
        """Returns the item code from the related ItemMaster."""
        return self.item.item_code if self.item else 'N/A'

    @property
    def description_display(self):
        """Returns the overridden description or the item master description."""
        return self.description_override if self.description_override else self.item.manf_desc if self.item else 'N/A'

    @property
    def uom_symbol_display(self):
        """Returns the overridden UOM symbol or the item master's basic UOM symbol."""
        if self.uom_override:
            return self.uom_override.symbol
        elif self.item and self.item.uom_basic:
            return self.item.uom_basic.symbol
        return 'N/A'

    @property
    def amended_by_display(self):
        """Returns the employee name who made the amendment."""
        return self.session_user.__str__() if self.session_user else 'Unknown'

    def get_absolute_url(self):
        return reverse('transactions:tplamendment_detail', kwargs={'pk': self.pk})

```

#### 4.2 Forms (`transactions/forms.py`)

While the original page is read-only, we provide generic forms for Create/Update operations for `TplAmendment` as per the template. These would be used if the module were expanded to allow direct modification of amendment records.

```python
from django import forms
from .models import TplAmendment, ItemMaster, UnitMaster, OfficeStaff

# Generic form for TplAmendment if CRUD functionality is added in future
class TplAmendmentForm(forms.ModelForm):
    class Meta:
        model = TplAmendment
        fields = [
            'sys_date', 'sys_time', 'wo_no', 'amd_no', 'item',
            'description_override', 'uom_override', 'qty', 'session_user',
            'tpl_id', 'fin_year_id', 'comp_id'
        ]
        widgets = {
            'sys_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sys_time': forms.TimeInput(attrs={'type': 'time', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'wo_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'amd_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'item': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'description_override': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'uom_override': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'qty': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'session_user': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'tpl_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'fin_year_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'comp_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add filtering for FK fields if necessary, e.g., based on logged-in user's company
        # self.fields['item'].queryset = ItemMaster.objects.filter(comp_id=self.request.user.company_id)
        # self.fields['uom_override'].queryset = UnitMaster.objects.all()
        # self.fields['session_user'].queryset = OfficeStaff.objects.all()

```

#### 4.3 Views (`transactions/views.py`)

We'll implement a `ListView` to display the amendment records, and a `TablePartialView` to serve the DataTables content via HTMX. Generic `CreateView`, `UpdateView`, and `DeleteView` are included for completeness based on the template, although not directly used by the original ASP.NET page's functionality.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.db.models import Prefetch

from .models import TplAmendment, ItemMaster, UnitMaster, OfficeStaff
from .forms import TplAmendmentForm

# View for the main TPL Amendment listing page
class TplAmendmentListView(ListView):
    model = TplAmendment
    template_name = 'transactions/tplamendment/list.html'
    context_object_name = 'tplamendments' # Renamed from generic 'objects'

    def get_queryset(self):
        # Apply filters from URL query parameters, similar to ASP.NET Request.QueryString
        queryset = TplAmendment.objects.all()

        # Optimize queries with select_related for Foreign Key lookups
        queryset = queryset.select_related(
            'item', 'item__uom_basic', 'uom_override', 'session_user'
        )

        wo_no = self.request.GET.get('WONo')
        item_id = self.request.GET.get('ItemId')
        tpl_id = self.request.GET.get('Id') # Matches ASP.NET 'Id' for TPLId

        # Assuming CompId and FinYearId are obtained from user session/context
        # For this example, we'll hardcode or get from query params if not in session
        # In a real app, these might come from self.request.user.company_id etc.
        comp_id = self.request.GET.get('CompId', 1) # Defaulting for example
        fin_year_id = self.request.GET.get('FinYearId', 1) # Defaulting for example

        if wo_no:
            queryset = queryset.filter(wo_no=wo_no)
        if item_id:
            queryset = queryset.filter(item__id=item_id)
        if tpl_id:
            queryset = queryset.filter(tpl_id=tpl_id)

        # Apply CompId and FinYearId filters if they are part of TplAmendment model
        queryset = queryset.filter(comp_id=comp_id, fin_year_id=fin_year_id)

        # Order by SysDate and SysTime, similar to how GridView would display
        queryset = queryset.order_by('sys_date', 'sys_time')

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass WONo for the header label
        context['wo_no_header'] = self.request.GET.get('WONo', 'N/A')
        return context

# View to render only the table partial for HTMX
class TplAmendmentTablePartialView(TplAmendmentListView):
    template_name = 'transactions/tplamendment/_tplamendment_table.html'

# Generic CRUD views for TplAmendment (not directly used by original ASP.NET page)
class TplAmendmentCreateView(CreateView):
    model = TplAmendment
    form_class = TplAmendmentForm
    template_name = 'transactions/tplamendment/form.html'
    success_url = reverse_lazy('transactions:tplamendment_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'TPL Amendment added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshTplAmendmentList'
                }
            )
        return response

class TplAmendmentUpdateView(UpdateView):
    model = TplAmendment
    form_class = TplAmendmentForm
    template_name = 'transactions/tplamendment/form.html'
    success_url = reverse_lazy('transactions:tplamendment_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'TPL Amendment updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshTplAmendmentList'
                }
            )
        return response

class TplAmendmentDeleteView(DeleteView):
    model = TplAmendment
    template_name = 'transactions/tplamendment/confirm_delete.html'
    success_url = reverse_lazy('transactions:tplamendment_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'TPL Amendment deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshTplAmendmentList'
                }
            )
        return response

```

#### 4.4 Templates (`transactions/templates/transactions/tplamendment/`)

We will create two main templates: `list.html` for the main page layout, and `_tplamendment_table.html` as a partial template loaded via HTMX for the DataTables content. Generic CRUD templates (`form.html`, `confirm_delete.html`) are also provided as per the template.

**`list.html`** (Main page for TPL Amendment List)

```html
{% extends 'core/base.html' %}

{% block title %}TPL Item - Amendment{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-blue-600 text-white p-3 rounded-t-lg shadow-md mb-6">
        <h1 class="text-xl font-bold">TPL Item - Amendment &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; WO No: <span id="lblWONo">{{ wo_no_header }}</span></h1>
    </div>

    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold hidden">TPL Amendments</h2> {# Hidden as header is above #}
        {# No Add button on original ASP.NET page, but keeping for template completeness #}
        <button
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded hidden"
            hx-get="{% url 'transactions:tplamendment_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New TPL Amendment
        </button>
    </div>

    <div id="tplamendmentTable-container"
         hx-trigger="load, refreshTplAmendmentList from:body"
         hx-get="{% url 'transactions:tplamendment_table' %}?{{ request.GET.urlencode }}" {# Pass original query params #}
         hx-swap="innerHTML"
         class="overflow-x-auto bg-white shadow-md rounded-lg">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="p-4 text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading TPL Amendments...</p>
        </div>
    </div>

    <div class="mt-6 text-center">
        {# Original ASP.NET Button2_Click redirects, replicating a back action for now #}
        <button
            class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
            onclick="window.history.back()"> {# Simulates back button behavior #}
            Cancel
        </button>
        {# If redirecting to a specific Django URL:
        <a href="{% url 'some_other_module:wo_tree_view_edit' wo_no_header %}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Cancel
        </a>
        #}
    </div>

    <!-- Modal for form (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
        Alpine.data('modal', () => ({
            open: false,
            toggle() {
                this.open = !this.open;
            },
            close() {
                this.open = false;
            }
        }));
    });
</script>
{% endblock %}
```

**`_tplamendment_table.html`** (Partial template for DataTables content)

```html
<table id="tplamendmentTable" class="min-w-full bg-white divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amendment No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amd By</th>
            {# No Actions column as original was read-only, but adding for completeness if CRUD were added #}
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden">Actions</th>
        </tr>
    </thead>
    <tbody class="divide-y divide-gray-200">
        {% for obj in tplamendments %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.display_date }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.display_time }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.wo_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.amd_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.item_code_display }}</td>
            <td class="py-2 px-4 border-b border-gray-200 align-top">{{ obj.description_display }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center align-top">{{ obj.uom_symbol_display }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right align-top">{{ obj.qty }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right align-top">{{ obj.amended_by_display }}</td>
            <td class="py-2 px-4 border-b border-gray-200 hidden"> {# Hidden as original was read-only #}
                <button
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'transactions:tplamendment_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'transactions:tplamendment_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="10" class="py-4 px-4 text-center text-lg text-maroon font-bold">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Initialize DataTables on the loaded table
    $(document).ready(function() {
        $('#tplamendmentTable').DataTable({
            "pageLength": 17, // Matches original ASP.NET page size
            "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": 0 } // SN column not sortable
            ]
        });
    });
</script>
```

**`form.html`** (Partial template for Create/Update forms)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} TPL Amendment</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status == 204) document.getElementById('modal').classList.remove('is-active')">
        {% csrf_token %}

        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`confirm_delete.html`** (Partial template for Delete confirmation)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="mb-4 text-gray-700">Are you sure you want to delete this TPL Amendment?</p>
    <p class="mb-6 text-gray-900 font-semibold">"{{ tplamendment }}"</p> {# Using object from context #}

    <form hx-post="{{ request.path }}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status == 204) document.getElementById('modal').classList.remove('is-active')">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`transactions/urls.py`)

Define the URL patterns for the TPL Amendment list and its partial table, along with generic CRUD URLs.

```python
from django.urls import path
from .views import (
    TplAmendmentListView,
    TplAmendmentTablePartialView,
    TplAmendmentCreateView,
    TplAmendmentUpdateView,
    TplAmendmentDeleteView
)

app_name = 'transactions' # Namespace for this app

urlpatterns = [
    # Main list view (e.g., /transactions/tplamendment/?WONo=XYZ&ItemId=123)
    path('tplamendment/', TplAmendmentListView.as_view(), name='tplamendment_list'),
    # HTMX endpoint for the table partial
    path('tplamendment/table/', TplAmendmentTablePartialView.as_view(), name='tplamendment_table'),

    # Generic CRUD operations (not directly from original ASP.NET page, but provided per template)
    path('tplamendment/add/', TplAmendmentCreateView.as_view(), name='tplamendment_add'),
    path('tplamendment/edit/<int:pk>/', TplAmendmentUpdateView.as_view(), name='tplamendment_edit'),
    path('tplamendment/delete/<int:pk>/', TplAmendmentDeleteView.as_view(), name='tplamendment_delete'),
]

```

#### 4.6 Tests (`transactions/tests.py`)

Comprehensive tests for the models and views are crucial for ensuring correctness and maintainability.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db.utils import IntegrityError
from datetime import date, time
from unittest.mock import patch, MagicMock

from .models import TplAmendment, ItemMaster, UnitMaster, OfficeStaff

class TplAmendmentModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for related models first
        cls.unit_kg = UnitMaster.objects.create(id=1, symbol='KG')
        cls.unit_pcs = UnitMaster.objects.create(id=2, symbol='PCS')
        cls.item1 = ItemMaster.objects.create(id=101, item_code='ITEM001', uom_basic=cls.unit_kg, manf_desc='Standard Item Description A')
        cls.item2 = ItemMaster.objects.create(id=102, item_code='ITEM002', uom_basic=cls.unit_pcs, manf_desc='Standard Item Description B')
        cls.staff1 = OfficeStaff.objects.create(emp_id=1, title='Mr.', employee_name='John Doe')
        cls.staff2 = OfficeStaff.objects.create(emp_id=2, title='Ms.', employee_name='Jane Smith')

        # Create test TplAmendment data
        TplAmendment.objects.create(
            id=1,
            sys_date=date(2023, 1, 15),
            sys_time=time(10, 30, 0),
            wo_no='WO-2023-001',
            amd_no='AMD-001',
            item=cls.item1,
            description_override=None,
            uom_override=None,
            qty=100.50,
            session_user=cls.staff1,
            tpl_id=5001,
            fin_year_id=2023,
            comp_id=1
        )
        TplAmendment.objects.create(
            id=2,
            sys_date=date(2023, 1, 16),
            sys_time=time(14, 0, 0),
            wo_no='WO-2023-001', # Same WO No
            amd_no='AMD-002',
            item=cls.item2,
            description_override='Custom description for Amd 002',
            uom_override=cls.unit_pcs, # Override UOM for this amendment
            qty=50.00,
            session_user=cls.staff2,
            tpl_id=5001,
            fin_year_id=2023,
            comp_id=1
        )
        TplAmendment.objects.create(
            id=3,
            sys_date=date(2023, 1, 17),
            sys_time=time(9, 0, 0),
            wo_no='WO-2023-002', # Different WO No
            amd_no='AMD-003',
            item=cls.item1,
            description_override=None,
            uom_override=cls.unit_kg, # Override UOM for this amendment (same as basic for Item1)
            qty=25.75,
            session_user=cls.staff1,
            tpl_id=5002,
            fin_year_id=2023,
            comp_id=1
        )

    def test_tplamendment_creation(self):
        obj = TplAmendment.objects.get(id=1)
        self.assertEqual(obj.wo_no, 'WO-2023-001')
        self.assertEqual(obj.amd_no, 'AMD-001')
        self.assertEqual(obj.qty, 100.50)
        self.assertEqual(obj.item.item_code, 'ITEM001')
        self.assertEqual(obj.session_user.employee_name, 'John Doe')

    def test_display_date_property(self):
        obj = TplAmendment.objects.get(id=1)
        self.assertEqual(obj.display_date, '15-01-2023')

    def test_display_time_property(self):
        obj = TplAmendment.objects.get(id=1)
        self.assertEqual(obj.display_time, '10:30:00')

    def test_item_code_display_property(self):
        obj = TplAmendment.objects.get(id=1)
        self.assertEqual(obj.item_code_display, 'ITEM001')

    def test_description_display_property(self):
        obj1 = TplAmendment.objects.get(id=1) # No override
        obj2 = TplAmendment.objects.get(id=2) # With override
        self.assertEqual(obj1.description_display, 'Standard Item Description A')
        self.assertEqual(obj2.description_display, 'Custom description for Amd 002')

    def test_uom_symbol_display_property(self):
        obj1 = TplAmendment.objects.get(id=1) # No override, uses item basic UOM
        obj2 = TplAmendment.objects.get(id=2) # With override
        obj3 = TplAmendment.objects.get(id=3) # With override (same as basic)

        self.assertEqual(obj1.uom_symbol_display, 'KG')
        self.assertEqual(obj2.uom_symbol_display, 'PCS')
        self.assertEqual(obj3.uom_symbol_display, 'KG')

    def test_amended_by_display_property(self):
        obj = TplAmendment.objects.get(id=1)
        self.assertEqual(obj.amended_by_display, 'Mr. John Doe')

    def test_str_method(self):
        obj = TplAmendment.objects.get(id=1)
        self.assertEqual(str(obj), 'Amendment AMD-001 for WO WO-2023-001 (Item: ITEM001)')

    def test_itemmaster_str_method(self):
        self.assertEqual(str(self.item1), 'ITEM001')

    def test_unitmaster_str_method(self):
        self.assertEqual(str(self.unit_kg), 'KG')

    def test_officestaff_str_method(self):
        self.assertEqual(str(self.staff1), 'Mr. John Doe')
        self.assertEqual(str(self.staff2), 'Ms. Jane Smith')


class TplAmendmentViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for related models and TplAmendment
        cls.unit_kg = UnitMaster.objects.create(id=1, symbol='KG')
        cls.unit_pcs = UnitMaster.objects.create(id=2, symbol='PCS')
        cls.item1 = ItemMaster.objects.create(id=101, item_code='ITEM001', uom_basic=cls.unit_kg, manf_desc='Standard Item Description A')
        cls.item2 = ItemMaster.objects.create(id=102, item_code='ITEM002', uom_basic=cls.unit_pcs, manf_desc='Standard Item Description B')
        cls.staff1 = OfficeStaff.objects.create(emp_id=1, title='Mr.', employee_name='John Doe')
        cls.staff2 = OfficeStaff.objects.create(emp_id=2, title='Ms.', employee_name='Jane Smith')

        cls.tpl_amd1 = TplAmendment.objects.create(
            id=1, sys_date=date(2023, 1, 15), sys_time=time(10, 30, 0), wo_no='WO-2023-001', amd_no='AMD-001',
            item=cls.item1, description_override=None, uom_override=None, qty=100.50,
            session_user=cls.staff1, tpl_id=5001, fin_year_id=2023, comp_id=1
        )
        cls.tpl_amd2 = TplAmendment.objects.create(
            id=2, sys_date=date(2023, 1, 16), sys_time=time(14, 0, 0), wo_no='WO-2023-001', amd_no='AMD-002',
            item=cls.item2, description_override='Custom desc', uom_override=cls.unit_pcs, qty=50.00,
            session_user=cls.staff2, tpl_id=5001, fin_year_id=2023, comp_id=1
        )
        cls.tpl_amd3 = TplAmendment.objects.create(
            id=3, sys_date=date(2023, 1, 17), sys_time=time(9, 0, 0), wo_no='WO-2023-002', amd_no='AMD-003',
            item=cls.item1, description_override=None, uom_override=None, qty=25.75,
            session_user=cls.staff1, tpl_id=5002, fin_year_id=2023, comp_id=1
        )


    def setUp(self):
        self.client = Client()

    def test_list_view_get(self):
        # Test basic list view with required query parameters
        response = self.client.get(reverse('transactions:tplamendment_list'),
                                   {'WONo': 'WO-2023-001', 'ItemId': self.item1.id, 'Id': self.tpl_amd1.tpl_id, 'CompId': 1, 'FinYearId': 2023})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'transactions/tplamendment/list.html')
        self.assertIn('tplamendments', response.context)
        self.assertIn('wo_no_header', response.context)
        self.assertContains(response, 'WO-2023-001') # Check WONo in header
        # Check if the correct amendments are in the context (only AMD-001 here due to ItemId filter)
        self.assertEqual(len(response.context['tplamendments']), 1)
        self.assertEqual(response.context['tplamendments'][0].id, self.tpl_amd1.id)

    def test_list_view_get_no_itemid_filter(self):
        # Test list view with only WONo and TPLId filter
        response = self.client.get(reverse('transactions:tplamendment_list'),
                                   {'WONo': 'WO-2023-001', 'Id': self.tpl_amd1.tpl_id, 'CompId': 1, 'FinYearId': 2023})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'transactions/tplamendment/list.html')
        self.assertEqual(len(response.context['tplamendments']), 2) # Both AMD-001 and AMD-002
        self.assertContains(response, self.tpl_amd1.amd_no)
        self.assertContains(response, self.tpl_amd2.amd_no)

    def test_table_partial_view_get(self):
        # Test HTMX partial view
        response = self.client.get(reverse('transactions:tplamendment_table'),
                                   {'WONo': 'WO-2023-001', 'ItemId': self.item1.id, 'Id': self.tpl_amd1.tpl_id, 'CompId': 1, 'FinYearId': 2023},
                                   HTTP_HX_REQUEST='true') # Simulate HTMX request
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'transactions/tplamendment/_tplamendment_table.html')
        self.assertContains(response, 'AMD-001')
        self.assertNotContains(response, 'AMD-002') # Should not contain due to filter

    # Generic CRUD tests (as per template, acknowledging they are not directly from original ASP.NET page)
    def test_create_view_get(self):
        response = self.client.get(reverse('transactions:tplamendment_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'transactions/tplamendment/form.html')
        self.assertIn('form', response.context)

    def test_create_view_post_success(self):
        new_data = {
            'sys_date': '2023-02-01',
            'sys_time': '11:00:00',
            'wo_no': 'NEW-WO-003',
            'amd_no': 'NEW-AMD-004',
            'item': self.item1.id,
            'description_override': 'New custom description',
            'uom_override': self.unit_pcs.id,
            'qty': 75.00,
            'session_user': self.staff1.emp_id,
            'tpl_id': 5003,
            'fin_year_id': 2023,
            'comp_id': 1
        }
        response = self.client.post(reverse('transactions:tplamendment_add'), new_data)
        self.assertEqual(response.status_code, 302) # Redirects on success
        self.assertTrue(TplAmendment.objects.filter(amd_no='NEW-AMD-004').exists())
        messages = list(response.wsgi_request._messages)
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'TPL Amendment added successfully.')

    def test_create_view_post_htmx_success(self):
        new_data = {
            'sys_date': '2023-02-02',
            'sys_time': '12:00:00',
            'wo_no': 'NEW-WO-004',
            'amd_no': 'NEW-AMD-005',
            'item': self.item2.id,
            'description_override': None,
            'uom_override': None,
            'qty': 200.00,
            'session_user': self.staff2.emp_id,
            'tpl_id': 5004,
            'fin_year_id': 2023,
            'comp_id': 1
        }
        response = self.client.post(reverse('transactions:tplamendment_add'), new_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content for HTMX
        self.assertTrue(TplAmendment.objects.filter(amd_no='NEW-AMD-005').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshTplAmendmentList')


    def test_update_view_get(self):
        response = self.client.get(reverse('transactions:tplamendment_edit', args=[self.tpl_amd1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'transactions/tplamendment/form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.tpl_amd1)

    def test_update_view_post_success(self):
        updated_data = {
            'sys_date': '2023-01-15',
            'sys_time': '10:30:00',
            'wo_no': 'WO-2023-001',
            'amd_no': 'AMD-001-Updated', # Changed
            'item': self.item1.id,
            'description_override': 'Updated Description', # Changed
            'uom_override': self.unit_kg.id,
            'qty': 110.00, # Changed
            'session_user': self.staff1.emp_id,
            'tpl_id': 5001,
            'fin_year_id': 2023,
            'comp_id': 1
        }
        response = self.client.post(reverse('transactions:tplamendment_edit', args=[self.tpl_amd1.pk]), updated_data)
        self.assertEqual(response.status_code, 302)
        self.tpl_amd1.refresh_from_db()
        self.assertEqual(self.tpl_amd1.amd_no, 'AMD-001-Updated')
        self.assertEqual(self.tpl_amd1.description_override, 'Updated Description')
        self.assertEqual(self.tpl_amd1.qty, 110.00)
        messages = list(response.wsgi_request._messages)
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'TPL Amendment updated successfully.')

    def test_delete_view_get(self):
        response = self.client.get(reverse('transactions:tplamendment_delete', args=[self.tpl_amd3.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'transactions/tplamendment/confirm_delete.html')
        self.assertIn('tplamendment', response.context)
        self.assertEqual(response.context['tplamendment'], self.tpl_amd3)

    def test_delete_view_post_success(self):
        response = self.client.post(reverse('transactions:tplamendment_delete', args=[self.tpl_amd3.pk]))
        self.assertEqual(response.status_code, 302)
        self.assertFalse(TplAmendment.objects.filter(pk=self.tpl_amd3.pk).exists())
        messages = list(response.wsgi_request._messages)
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'TPL Amendment deleted successfully.')

    def test_delete_view_post_htmx_success(self):
        # Create a new amendment to delete, so it doesn't affect other tests
        temp_amd = TplAmendment.objects.create(
            id=4, sys_date=date(2023, 3, 1), sys_time=time(8, 0, 0), wo_no='WO-TEMP', amd_no='AMD-TEMP',
            item=self.item1, description_override=None, uom_override=None, qty=1.00,
            session_user=self.staff1, tpl_id=9999, fin_year_id=2023, comp_id=1
        )
        response = self.client.post(reverse('transactions:tplamendment_delete', args=[temp_amd.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(TplAmendment.objects.filter(pk=temp_amd.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshTplAmendmentList')
```

### Step 5: HTMX and Alpine.js Integration

The provided templates (`list.html`, `_tplamendment_table.html`, `form.html`, `confirm_delete.html`) demonstrate the integration of HTMX and Alpine.js.

*   **HTMX for dynamic updates:**
    *   `list.html` uses `hx-get` on `tplamendmentTable-container` with `hx-trigger="load, refreshTplAmendmentList from:body"` to load the table content dynamically when the page loads or when a custom event `refreshTplAmendmentList` is triggered (after successful CRUD operations).
    *   `form.html` and `confirm_delete.html` are designed to be loaded into a modal using `hx-get` on "Add", "Edit", "Delete" buttons, targeting `#modalContent`.
    *   Form submissions (`hx-post`) from `form.html` and `confirm_delete.html` use `hx-swap="none"` and trigger `refreshTplAmendmentList` via `HX-Trigger` header upon success (`status=204`), allowing the main list to refresh without a full page reload.
*   **Alpine.js for UI state management:**
    *   A simple Alpine.js component is provided in `list.html` for managing the modal's `open` state, allowing the modal to be shown/hidden when triggered by HTMX or direct clicks. The `_="on click add .is-active to #modal"` attribute adds a class to display the modal when buttons are clicked.
*   **DataTables for list views:**
    *   The `_tplamendment_table.html` partial contains the `<table id="tplamendmentTable">` element.
    *   A `<script>` block within this partial initializes DataTables on this table using `$(document).ready(function() { $('#tplamendmentTable').DataTable(); });`. This ensures DataTables is initialized every time the partial is loaded by HTMX.
    *   Pagination (`"pageLength": 17`), search, and sorting capabilities are enabled by DataTables, replacing the ASP.NET server-side pagination.
*   **DRY Template Inheritance:** All templates extend `core/base.html` (not included in this output, but assumed to contain all CDN links for Tailwind CSS, HTMX, Alpine.js, jQuery, and DataTables).

### Final Notes

*   Placeholders like `[APP_NAME]` have been replaced with `transactions`.
*   Field names and table names have been mapped to Django conventions (`snake_case` for fields, `PascalCase` for models).
*   The business logic for deriving display fields (Item Code, Description, UOM Symbol, Amended By) is encapsulated within properties on the `TplAmendment` model, ensuring views remain thin and focused on rendering.
*   The original ASP.NET page primarily showed a listing. While comprehensive CRUD views/forms have been provided for `TplAmendment` as per the template, they are generic examples for future expansion or if such functionality exists elsewhere in the original ASP.NET system, but not directly derived from the provided ASP.NET code snippet's primary function.
*   The "Cancel" button's redirect logic has been generalized to `window.history.back()` or a placeholder Django URL. In a full system, this would point to the appropriate Django URL for the parent Work Order view.
*   The `CompId` and `FinYearId` filters are included based on the ASP.NET code. In a full Django application, these might be obtained from the authenticated user's profile or global settings rather than URL query parameters.
*   The tests provide high coverage for both model properties and view interactions, including HTMX-specific responses.