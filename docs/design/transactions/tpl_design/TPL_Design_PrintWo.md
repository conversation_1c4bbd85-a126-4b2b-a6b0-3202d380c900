## ASP.NET to Django Conversion Script: TPL - Print Work Order

This modernization plan outlines the strategic transition of your existing ASP.NET 'TPL - Print Work Order' module to a robust, scalable, and modern Django application. Our approach prioritizes automation, clean architecture, and an enhanced user experience leveraging cutting-edge web technologies like HTMX and Alpine.js.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists in `core/base.html`.
- Focus ONLY on component-specific code for the current module (`design` app).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html`.
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
Based on the ASP.NET `BindDataCust` method's SQL queries and `GridView` bindings, we infer the following tables and their relevant columns. These will be mapped to Django models.

-   **`SD_Cust_WorkOrder_Master` (Primary Data Source)**
    -   `Id` (Primary Key, integer)
    -   `EnqId` (integer)
    -   `PONo` (varchar)
    -   `CustomerId` (integer, links to `SD_Cust_Master`)
    -   `WONo` (varchar, Work Order Number)
    -   `SessionId` (integer, links to `tblHR_OfficeStaff.EmpId`, represents the employee who generated it)
    -   `FinYearId` (integer, links to `tblFinancial_master`)
    -   `CompId` (integer, Company ID)
    -   `SysDate` (datetime, Work Order Generation Date - `WODate`)

-   **`SD_Cust_Master` (Customer Information)**
    -   `CustomerId` (Primary Key, integer)
    -   `CustomerName` (varchar)
    -   `FinYearId` (integer)
    -   `CompId` (integer)

-   **`tblDG_TPL_Master` (TPL Dates for Work Orders)**
    -   `Id` (Primary Key, integer, inferred)
    -   `WONo` (varchar, links to `SD_Cust_WorkOrder_Master.WONo`)
    -   `SysDate` (datetime, TPL Date - `TPLDate`)
    -   `FinYearId` (integer)
    -   `CompId` (integer)

-   **`tblFinancial_master` (Financial Year Information)**
    -   `FinYearId` (Primary Key, integer)
    -   `FinYear` (varchar)

-   **`tblHR_OfficeStaff` (Employee Information)**
    -   `EmpId` (Primary Key, integer)
    -   `Title` (varchar)
    -   `EmployeeName` (varchar)
    -   `FinYearId` (integer)
    -   `CompId` (integer)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and core business logic in the ASP.NET code.

**Instructions:**
The primary functionality is **Read** (displaying a list of Work Orders) and a specific **Action** (redirecting to a "print tree" view with selected work order details and dates).

-   **Read (List & Search):**
    -   The `BindDataCust` method handles fetching work order data.
    -   It supports searching by `Enquiry No`, `PO No`, `WO No` (text-based search) and `Customer Name` (autocomplete search by `CustomerId`).
    -   Pagination and sorting are handled by the `GridView`.
    -   The data retrieval involves multiple nested queries to enrich the work order data with customer name, financial year, TPL date, and employee name. This complex data aggregation will be optimized using Django ORM's `select_related` and `prefetch_related` and moved into model managers.

-   **Action (Print/Redirect):**
    -   The `SearchGridView1_RowCommand` with `CommandName="Sel"` triggers a redirect to `TPL_Design_Print_Tree.aspx` with `WONo`, `StartDate (SD)`, and `UptoDate (TD)` as query parameters.
    -   The `StartDate` and `UptoDate` are editable textboxes within the grid row, and their values are captured at the time of the "Select" button click. This will be an HTMX-driven form submission.

-   **Autocomplete:**
    -   The `sql` web method provides autocomplete suggestions for `CustomerName` by returning `CustomerName [CustomerId]`. This will be a dedicated HTMX endpoint returning JSON.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The ASP.NET controls indicate a standard search-and-list interface:

-   **Search Controls:**
    -   `DropDownList1`: Selection for search criteria (Customer, Enquiry, PO, WO).
    -   `txtSearchCustomer`: General text input for search queries (Enquiry, PO, WO).
    -   `TxtSearchValue` with `AutoCompleteExtender`: Specialized text input for Customer Name with autocomplete.
    -   `btnSearchWo`: Triggers the search.

-   **Data Display:**
    -   `SearchGridView1`: Displays the list of Work Orders.
        -   Includes columns for SN, Select (action), Fin Yrs, Customer Name, Customer Code, Enquiry No, PO No, Start Date (editable), Upto Date (editable), Id (hidden), WO No, Gen. Date, Gen. By.
        -   The "Start Date" and "Upto Date" columns contain editable textboxes with calendar pickers (`CalendarExtender`). These will be replaced by standard HTML `input type="text"` fields with a JavaScript date picker (e.g., using Alpine.js or a simple JS library).

-   **Backend Communication Pattern:** The ASP.NET `AutoPostBack` and `onclick` events, along with `AjaxControlToolkit.AutoCompleteExtender`, indicate a partial page update or asynchronous data fetching pattern. This will be replaced entirely by HTMX for dynamic content loading and form submissions, and Alpine.js for any client-side UI state management if needed.

### Step 4: Generate Django Code

We will create a new Django application named `design` to house this module.

#### 4.1 Models (design/models.py)

We define the Django models to mirror the identified database tables, ensuring `managed = False` as these tables are expected to exist in the legacy database.

```python
from django.db import models
from django.utils import timezone
from datetime import date

class FinancialYear(models.Model):
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year or f"ID: {self.fin_year_id}"

class Customer(models.Model):
    customer_id = models.IntegerField(db_column='CustomerId', primary_key=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=255, blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True) # Likely FK but not explicitly defined as such in source
    company_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_Master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customer_name or f"ID: {self.customer_id}"

class Employee(models.Model):
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    company_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.title or ''} {self.employee_name}".strip() or f"ID: {self.emp_id}"

class TPLMaster(models.Model):
    # This table maps WONo to its earliest TPL Date for a given WorkOrder
    # Assuming `Id` is the primary key in this table if not explicitly mentioned
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming a primary key for this table
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True) # Not a direct FK because WONo might not be unique in WorkOrder
    tpl_date = models.DateField(db_column='SysDate', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    company_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_TPL_Master'
        verbose_name = 'TPL Record'
        verbose_name_plural = 'TPL Records'

    def __str__(self):
        return f"TPL {self.wo_no} on {self.tpl_date}"

class WorkOrderManager(models.Manager):
    def get_queryset(self):
        # Default queryset with common related data pre-fetched for efficiency
        return super().get_queryset().select_related(
            'customer', 'generated_by', 'financial_year'
        )

    def search_work_orders(self, search_by, search_value, company_id, fin_year_id):
        queryset = self.get_queryset().filter(company_id=company_id, fin_year_id__lte=fin_year_id) # __lte is from ASP.NET logic

        if search_by == '1': # Enquiry No
            if search_value:
                queryset = queryset.filter(enq_id=search_value)
        elif search_by == '2': # PO No
            if search_value:
                queryset = queryset.filter(po_no=search_value)
        elif search_by == '3': # WO No
            if search_value:
                queryset = queryset.filter(wo_no=search_value)
        elif search_by == '0': # Customer Name (requires customer_id)
            if search_value:
                # The search_value here is the customer_id extracted from the autocomplete string
                queryset = queryset.filter(customer_id=search_value)
        
        # Order by Id Desc as per ASP.NET
        queryset = queryset.order_by('-id')

        # Annotate with TPL Date if possible. This is tricky due to earliest date logic.
        # It's more efficient to do this as a property or separate lookup on demand in a fat model.
        # For a fat model approach, we will add a property for tpl_date.
        return queryset

class WorkOrder(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    enq_id = models.IntegerField(db_column='EnqId', blank=True, null=True)
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    customer = models.ForeignKey(Customer, on_delete=models.DO_NOTHING, db_column='CustomerId', related_name='work_orders', blank=True, null=True)
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    generated_by = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='SessionId', related_name='generated_work_orders', blank=True, null=True) # SessionId is EmpId
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='work_orders', blank=True, null=True)
    company_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    wo_date = models.DateField(db_column='SysDate', blank=True, null=True)

    objects = WorkOrderManager() # Use custom manager

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.wo_no or f"WO ID: {self.id}"

    @property
    def tpl_date(self):
        """
        Retrieves the earliest TPL date associated with this work order.
        This mimics the `tblDG_TPL_Master` lookup in the ASP.NET code.
        """
        # Assume company_id and fin_year_id can be accessed from the instance or current session context
        # For simplicity in this example, we'll use instance attributes
        earliest_tpl = TPLMaster.objects.filter(
            wo_no=self.wo_no,
            company_id=self.company_id,
            fin_year_id__lte=self.financial_year.fin_year_id if self.financial_year else None
        ).order_by('tpl_date').first() # Order by date ascending and take first
        return earliest_tpl.tpl_date if earliest_tpl else None

    @property
    def current_date(self):
        """
        Provides the current date, used as the default for 'Upto Date'.
        """
        return date.today()

    def get_fin_year_display(self):
        return self.financial_year.fin_year if self.financial_year else 'N/A'

    def get_customer_name_display(self):
        return self.customer.customer_name if self.customer else 'N/A'

    def get_generated_by_display(self):
        return self.generated_by.employee_name if self.generated_by else 'N/A'

    # You can add more business logic methods here as needed, e.g., for status, calculations
```

#### 4.2 Forms (design/forms.py)

No complex forms are needed for the list view since dates are edited inline. The search functionality will be handled via request parameters directly in the view or a simple non-model form if validation is required. For the purpose of the existing ASP.NET code, no complex `ModelForm` is required. The `TxtBOMDate` and `TxtUptoDate` are simple fields submitted with `hx-post`.

```python
# design/forms.py (Empty or for future use if full form editing is added)
# No complex form needed for this specific ASP.NET migration as
# list view's date inputs are handled via HTMX directly on row selection.
from django import forms

class WorkOrderSearchForm(forms.Form):
    search_by = forms.ChoiceField(
        choices=[
            ('Select', 'Select'),
            ('0', 'Customer Name'),
            ('1', 'Enquiry No'),
            ('2', 'PO No'),
            ('3', 'WO No'),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'hx-get': "{% url 'design:workorder_table' %}", 'hx-target': '#workorderTable-container', 'hx-include': '#search_form_container', 'hx-indicator': '.htmx-indicator'})
    )
    search_text = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'id': 'txtSearchCustomer', 'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Search value'})
    )
    # This field is for the customer name search with autocomplete, its visibility is managed by Alpine.js
    customer_search_value = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'id': 'TxtSearchValue', 'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Customer Name'})
    )
```

#### 4.3 Views (design/views.py)

Views will be kept thin, delegating all complex data retrieval and business logic to the `WorkOrder` model and its manager. We'll use Class-Based Views for structure and function views for specific HTMX interactions like autocomplete.

```python
from django.views.generic import ListView, View, TemplateView
from django.http import JsonResponse, HttpResponseRedirect, HttpResponse
from django.urls import reverse
from django.db.models import Q
from django.contrib import messages
from django.utils.dateparse import parse_date
from datetime import date
import re # For parsing customer ID from autocomplete string

from .models import WorkOrder, Customer # Import other models if needed
from .forms import WorkOrderSearchForm # Only if we use it for form validation/rendering

class WorkOrderListView(ListView):
    model = WorkOrder
    template_name = 'design/workorder/list.html'
    context_object_name = 'work_orders'
    # Default initial values for company and financial year (from session in ASP.NET)
    # These should be dynamically fetched based on logged-in user in a real application.
    default_company_id = 1
    default_fin_year_id = 2024 # Example: Represents the current financial year ID

    def get_queryset(self):
        # This view primarily sets up the initial page.
        # The actual table data is loaded by HTMX via WorkOrderTablePartialView.
        # So, this initial queryset can be empty or a small default.
        return WorkOrder.objects.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass the form for initial rendering of search controls
        context['search_form'] = WorkOrderSearchForm(self.request.GET)
        # Pass initial visibility state for Alpine.js
        context['initial_txt_search_customer_visible'] = True # Default when DropDownList1 is "Select"
        context['initial_txt_search_value_visible'] = False
        return context

class WorkOrderTablePartialView(ListView):
    model = WorkOrder
    template_name = 'design/workorder/_workorder_table.html'
    context_object_name = 'work_orders'
    
    # Default initial values for company and financial year (from session in ASP.NET)
    default_company_id = 1
    default_fin_year_id = 2024 # Example: Represents the current financial year ID

    def get_queryset(self):
        search_by = self.request.GET.get('search_by', 'Select')
        search_text = self.request.GET.get('search_text', '').strip()
        customer_search_value = self.request.GET.get('customer_search_value', '').strip()

        # Extract CustomerId from "CustomerName [CustomerId]" string for search_by='0'
        customer_id_for_search = None
        if search_by == '0' and customer_search_value:
            match = re.search(r'\[(\d+)\]', customer_search_value)
            if match:
                customer_id_for_search = int(match.group(1))

        # Use the custom manager's search method
        queryset = WorkOrder.objects.search_work_orders(
            search_by=search_by,
            search_value=search_text if search_by != '0' else customer_id_for_search,
            company_id=self.default_company_id, # Replace with dynamic user/session data
            fin_year_id=self.default_fin_year_id # Replace with dynamic user/session data
        )
        return queryset

class CustomerAutocompleteView(View):
    def get(self, request, *args, **kwargs):
        query = request.GET.get('query', '')
        # Simulate CompId from session as in ASP.NET
        # In a real app, this would come from the authenticated user's company
        comp_id = 1 

        if query:
            # Filter customers by name starting with query, limited by CompId
            customers = Customer.objects.filter(
                customer_name__istartswith=query,
                company_id=comp_id # Assuming CompId applies to Customer table as well
            ).values('customer_id', 'customer_name')[:10] # Limit results for performance

            suggestions = [
                f"{c['customer_name']} [{c['customer_id']}]" for c in customers
            ]
        else:
            suggestions = []
        
        return JsonResponse(suggestions, safe=False)

class PrintWorkOrderView(View):
    def post(self, request, wo_id):
        try:
            work_order = WorkOrder.objects.get(id=wo_id)
        except WorkOrder.DoesNotExist:
            messages.error(request, 'Work Order not found.')
            return HttpResponse(status=404)

        # Retrieve start and end dates from POST data, coming from the inline textboxes
        start_date_str = request.POST.get('start_date')
        end_date_str = request.POST.get('end_date')

        # Validate and format dates if necessary
        # The ASP.NET code passes dd-MM-yyyy. We'll assume the inputs are in this format.
        # For security, you might want to parse them into datetime objects and reformat.
        # Example: parse_date(start_date_str).strftime('%d-%m-%Y') if needed for external service
        
        # Construct the target URL for the "print tree" equivalent
        # This needs to match the structure of your new reporting module/view.
        # Assuming a 'report' app with a 'print_tree_view'
        # The query parameters are directly mapped from the ASP.NET original redirect.
        target_url = reverse('report:print_tree_view') + \
                     f'?WONo={work_order.wo_no}&SD={start_date_str}&TD={end_date_str}&ModId=3&SubModId=23'

        messages.success(request, f"Redirecting to print Work Order {work_order.wo_no}.")

        # Send HX-Redirect header for HTMX to handle the browser redirect
        response = HttpResponse(status=204) # 204 No Content, HTMX will handle the redirect
        response['HX-Redirect'] = target_url
        return response

```

#### 4.4 Templates (design/workorder/)

We'll create two main templates: `list.html` for the main page and `_workorder_table.html` for the HTMX-loaded table content.

**design/workorder/list.html**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-gray-100 p-6 rounded-lg shadow-md mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">TPL - Print Work Orders</h2>
        
        <div id="search_form_container" x-data="{
            searchBy: '{{ search_form.search_by.value|default:'Select' }}',
            txtSearchCustomerVisible: {{ initial_txt_search_customer_visible|lower }},
            txtSearchValueVisible: {{ initial_txt_search_value_visible|lower }},
            updateVisibility() {
                this.txtSearchCustomerVisible = (this.searchBy === '1' || this.searchBy === '2' || this.searchBy === '3' || this.searchBy === 'Select');
                this.txtSearchValueVisible = (this.searchBy === '0');
            }
        }">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                <div>
                    <label for="id_search_by" class="block text-sm font-medium text-gray-700">Search By</label>
                    <select id="id_search_by" name="search_by" class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            x-model="searchBy" x-on:change="updateVisibility();">
                        <option value="Select">Select</option>
                        <option value="0">Customer Name</option>
                        <option value="1">Enquiry No</option>
                        <option value="2">PO No</option>
                        <option value="3">WO No</option>
                    </select>
                </div>
                
                <div x-show="txtSearchCustomerVisible" x-transition.opacity>
                    <label for="txtSearchCustomer" class="block text-sm font-medium text-gray-700">Search Value</label>
                    <input type="text" id="txtSearchCustomer" name="search_text" 
                           value="{{ search_form.search_text.value|default:'' }}"
                           class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                           placeholder="Enter search value">
                </div>

                <div x-show="txtSearchValueVisible" x-transition.opacity>
                    <label for="TxtSearchValue" class="block text-sm font-medium text-gray-700">Customer Name</label>
                    <input type="text" id="TxtSearchValue" name="customer_search_value" 
                           value="{{ search_form.customer_search_value.value|default:'' }}"
                           class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                           placeholder="Enter customer name for autocomplete"
                           hx-get="{% url 'design:customer_autocomplete' %}"
                           hx-trigger="input changed delay:500ms"
                           hx-target="#customer_suggestions"
                           hx-swap="innerHTML"
                           _="on keyup if event.key == 'Enter' then set #TxtSearchValue.value to first .suggestion.value in #customer_suggestions then hide #customer_suggestions end">
                    <div id="customer_suggestions" class="absolute z-10 w-auto bg-white border border-gray-300 rounded-md shadow-lg mt-1"></div>
                </div>
                
                <div>
                    <button id="btnSearchWo" type="button" 
                            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded w-full md:w-auto"
                            hx-get="{% url 'design:workorder_table' %}"
                            hx-target="#workorderTable-container"
                            hx-include="#search_form_container" {# Include all search inputs #}
                            hx-indicator="#loading-indicator"
                            hx-swap="innerHTML">
                        Search
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div id="workorderTable-container"
         hx-trigger="load, refreshWorkOrderList from:body"
         hx-get="{% url 'design:workorder_table' %}"
         hx-include="#search_form_container" {# Pass current search params on load #}
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div id="loading-indicator" class="text-center htmx-indicator">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading Work Orders...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.8/css/dataTables.dataTables.min.css">

<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('workOrderSearch', () => ({
            searchBy: '{{ search_form.search_by.value|default:'Select' }}',
            txtSearchCustomerVisible: {{ initial_txt_search_customer_visible|lower }},
            txtSearchValueVisible: {{ initial_txt_search_value_visible|lower }},
            updateVisibility() {
                this.txtSearchCustomerVisible = (this.searchBy === '1' || this.searchBy === '2' || this.searchBy === '3' || this.searchBy === 'Select');
                this.txtSearchValueVisible = (this.searchBy === '0');
                if (this.txtSearchCustomerVisible) {
                    document.getElementById('txtSearchCustomer').value = '';
                }
                if (this.txtSearchValueVisible) {
                    document.getElementById('TxtSearchValue').value = '';
                }
            },
        }));
    });

    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'workorderTable-container') {
            // Re-initialize DataTables after HTMX swaps the content
            $('#workorderTable').DataTable({
                "pageLength": 20, // Match ASP.NET PageSize
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]]
            });

            // Re-initialize Flatpickr for date inputs after HTMX swaps the content
            flatpickr(".datepicker", {
                dateFormat: "d-m-Y", // Match ASP.NET dd-MM-yyyy format
                allowInput: true // Allow direct input
            });
        }
    });

    // Auto-select on customer autocomplete suggestion click
    document.getElementById('customer_suggestions').addEventListener('click', function(event) {
        if (event.target.classList.contains('suggestion')) {
            document.getElementById('TxtSearchValue').value = event.target.textContent;
            this.innerHTML = ''; // Clear suggestions
            // Trigger search after selecting customer
            document.getElementById('btnSearchWo').click();
        }
    });
</script>
{% endblock %}
```

**design/workorder/_workorder_table.html**

```html
{% load humanize %} {# Optional for better number formatting, if needed #}
<div class="overflow-x-auto bg-white shadow-md rounded-lg">
    <table id="workorderTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry No</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Start Date</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Upto Date</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. Date</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for wo in work_orders %}
            <tr id="workorder-row-{{ wo.id }}">
                <td class="py-2 px-6 whitespace-nowrap text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-6 whitespace-nowrap text-center">
                    <button 
                        type="button"
                        class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded text-xs"
                        hx-post="{% url 'design:workorder_print' wo.id %}"
                        hx-include="#workorder-row-{{ wo.id }} input[name='start_date'], #workorder-row-{{ wo.id }} input[name='end_date']"
                        hx-target="body" hx-swap="none"
                        _="on htmx:afterRequest if event.detail.xhr.getResponseHeader('HX-Redirect') then window.location.href = event.detail.xhr.getResponseHeader('HX-Redirect') end">
                        Print
                    </button>
                </td>
                <td class="py-2 px-6 whitespace-nowrap text-center">{{ wo.get_fin_year_display }}</td>
                <td class="py-2 px-6 whitespace-nowrap text-left">{{ wo.get_customer_name_display }}</td>
                <td class="py-2 px-6 whitespace-nowrap text-center">{{ wo.customer.customer_id|default:'' }}</td>
                <td class="py-2 px-6 whitespace-nowrap text-center">{{ wo.enq_id|default:'' }}</td>
                <td class="py-2 px-6 whitespace-nowrap text-center">{{ wo.po_no|default:'' }}</td>
                <td class="py-2 px-6 whitespace-nowrap text-left">
                    <input type="text" name="start_date" 
                           class="datepicker box3 w-24 px-1 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" 
                           value="{{ wo.tpl_date|date:'d-m-Y'|default:'' }}">
                </td>
                <td class="py-2 px-6 whitespace-nowrap text-center">
                    <input type="text" name="end_date" 
                           class="datepicker box3 w-24 px-1 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" 
                           value="{{ wo.current_date|date:'d-m-Y' }}">
                </td>
                <td class="py-2 px-6 whitespace-nowrap text-center">{{ wo.wo_no|default:'' }}</td>
                <td class="py-2 px-6 whitespace-nowrap text-center">{{ wo.wo_date|date:'d-m-Y'|default:'' }}</td>
                <td class="py-2 px-6 whitespace-nowrap text-left">{{ wo.get_generated_by_display }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="12" class="py-4 px-6 text-center text-lg text-maroon font-semibold">
                    No data to display !
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<div id="customer_suggestions"></div>

<style>
    /* Basic styling for autocomplete suggestions */
    .autocomplete-suggestions {
        position: absolute;
        border: 1px solid #ddd;
        background-color: white;
        z-index: 1000;
        max-height: 200px;
        overflow-y: auto;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }
    .suggestion {
        padding: 8px 12px;
        cursor: pointer;
        border-bottom: 1px solid #eee;
    }
    .suggestion:hover {
        background-color: #f0f0f0;
    }
</style>
```

**design/workorder/_customer_autocomplete_suggestions.html (partial for HTMX target)**
This will be swapped into `#customer_suggestions`
```html
{% for suggestion in suggestions %}
    <div class="suggestion p-2 hover:bg-gray-100 cursor-pointer border-b border-gray-200"
         hx-on--click="document.getElementById('TxtSearchValue').value = '{{ suggestion }}'; this.parentElement.innerHTML = ''; document.getElementById('btnSearchWo').click();">
        {{ suggestion }}
    </div>
{% endfor %}
```

#### 4.5 URLs (design/urls.py)

Define URL patterns for the views, including the HTMX-specific partials and autocomplete.

```python
from django.urls import path
from .views import WorkOrderListView, WorkOrderTablePartialView, CustomerAutocompleteView, PrintWorkOrderView

app_name = 'design' # Define app_name for namespacing

urlpatterns = [
    # Main page for Work Order search and list
    path('print-wo/', WorkOrderListView.as_view(), name='workorder_list'),
    
    # HTMX endpoint to load/refresh the table content
    path('print-wo/table/', WorkOrderTablePartialView.as_view(), name='workorder_table'),
    
    # HTMX endpoint for customer autocomplete
    path('print-wo/customer-autocomplete/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),
    
    # HTMX endpoint for printing/redirecting a specific work order
    path('print-wo/print/<int:wo_id>/', PrintWorkOrderView.as_view(), name='workorder_print'),
    
    # Placeholder for the actual report view (TPL_Design_Print_Tree.aspx equivalent)
    # This URL would likely be in a separate 'report' app.
    # Example for report app:
    # path('report/print-tree/', views.PrintTreeView.as_view(), name='print_tree_view'),
]
```
*(Note: You will need to add a placeholder `report` app with a `print_tree_view` in your project's main `urls.py` for the `PrintWorkOrderView` redirect to work. E.g., `path('reports/', include('report.urls', namespace='report')),`)*

#### 4.6 Tests (design/tests.py)

Comprehensive unit tests for model methods and integration tests for all views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import date
from unittest.mock import patch

from .models import FinancialYear, Customer, Employee, TPLMaster, WorkOrder

class WorkOrderModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.fin_year = FinancialYear.objects.create(fin_year_id=2024, fin_year='2024-2025')
        cls.customer = Customer.objects.create(customer_id=101, customer_name='Test Customer A', fin_year_id=2024, company_id=1)
        cls.employee = Employee.objects.create(emp_id=501, title='Mr.', employee_name='John Doe', fin_year_id=2024, company_id=1)
        
        cls.work_order_1 = WorkOrder.objects.create(
            id=1, enq_id=1001, po_no='PO-001', customer=cls.customer, wo_no='WO-001',
            generated_by=cls.employee, financial_year=cls.fin_year, company_id=1,
            wo_date=date(2024, 1, 10)
        )
        # Create a second WO for search/list tests
        cls.work_order_2 = WorkOrder.objects.create(
            id=2, enq_id=1002, po_no='PO-002', customer=cls.customer, wo_no='WO-002',
            generated_by=cls.employee, financial_year=cls.fin_year, company_id=1,
            wo_date=date(2024, 2, 15)
        )

        # Create TPLMaster entry for WO-001
        TPLMaster.objects.create(
            id=1, wo_no='WO-001', tpl_date=date(2024, 1, 15), fin_year_id=2024, company_id=1
        )
        TPLMaster.objects.create(
            id=2, wo_no='WO-001', tpl_date=date(2024, 1, 12), fin_year_id=2024, company_id=1 # Earlier date
        )
        TPLMaster.objects.create(
            id=3, wo_no='WO-002', tpl_date=date(2024, 2, 20), fin_year_id=2024, company_id=1
        )
  
    def test_work_order_creation(self):
        obj = WorkOrder.objects.get(id=1)
        self.assertEqual(obj.enq_id, 1001)
        self.assertEqual(obj.wo_no, 'WO-001')
        self.assertEqual(obj.customer.customer_name, 'Test Customer A')
        self.assertEqual(obj.generated_by.employee_name, 'John Doe')
        
    def test_tpl_date_property(self):
        obj = WorkOrder.objects.get(id=1)
        # Should return the earliest TPL date
        self.assertEqual(obj.tpl_date, date(2024, 1, 12))

        obj_no_tpl = WorkOrder.objects.create(
            id=3, enq_id=1003, po_no='PO-003', customer=self.customer, wo_no='WO-003',
            generated_by=self.employee, financial_year=self.fin_year, company_id=1,
            wo_date=date(2024, 3, 1)
        )
        self.assertIsNone(obj_no_tpl.tpl_date)

    def test_current_date_property(self):
        obj = WorkOrder.objects.get(id=1)
        self.assertEqual(obj.current_date, date.today())
    
    def test_get_display_properties(self):
        obj = WorkOrder.objects.get(id=1)
        self.assertEqual(obj.get_fin_year_display(), '2024-2025')
        self.assertEqual(obj.get_customer_name_display(), 'Test Customer A')
        self.assertEqual(obj.get_generated_by_display(), 'John Doe')

    def test_search_work_orders_by_enquiry_no(self):
        queryset = WorkOrder.objects.search_work_orders('1', '1001', 1, 2024)
        self.assertEqual(queryset.count(), 1)
        self.assertEqual(queryset.first().wo_no, 'WO-001')

    def test_search_work_orders_by_po_no(self):
        queryset = WorkOrder.objects.search_work_orders('2', 'PO-002', 1, 2024)
        self.assertEqual(queryset.count(), 1)
        self.assertEqual(queryset.first().wo_no, 'WO-002')

    def test_search_work_orders_by_wo_no(self):
        queryset = WorkOrder.objects.search_work_orders('3', 'WO-001', 1, 2024)
        self.assertEqual(queryset.count(), 1)
        self.assertEqual(queryset.first().wo_no, 'WO-001')

    def test_search_work_orders_by_customer_id(self):
        queryset = WorkOrder.objects.search_work_orders('0', '101', 1, 2024)
        self.assertEqual(queryset.count(), 2) # Both WOs belong to customer 101

    def test_search_work_orders_select_all(self):
        queryset = WorkOrder.objects.search_work_orders('Select', '', 1, 2024)
        self.assertEqual(queryset.count(), 2) # Should return all for selected company/fin year

    def test_search_work_orders_no_match(self):
        queryset = WorkOrder.objects.search_work_orders('3', 'NONEXISTENT', 1, 2024)
        self.assertEqual(queryset.count(), 0)

class WorkOrderViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.fin_year = FinancialYear.objects.create(fin_year_id=2024, fin_year='2024-2025')
        cls.customer = Customer.objects.create(customer_id=101, customer_name='Test Customer A', fin_year_id=2024, company_id=1)
        cls.employee = Employee.objects.create(emp_id=501, title='Mr.', employee_name='John Doe', fin_year_id=2024, company_id=1)
        
        cls.work_order_1 = WorkOrder.objects.create(
            id=1, enq_id=1001, po_no='PO-001', customer=cls.customer, wo_no='WO-001',
            generated_by=cls.employee, financial_year=cls.fin_year, company_id=1,
            wo_date=date(2024, 1, 10)
        )
        cls.work_order_2 = WorkOrder.objects.create(
            id=2, enq_id=1002, po_no='PO-002', customer=cls.customer, wo_no='WO-002',
            generated_by=cls.employee, financial_year=cls.fin_year, company_id=1,
            wo_date=date(2024, 2, 15)
        )
        TPLMaster.objects.create(id=101, wo_no='WO-001', tpl_date=date(2024, 1, 12), fin_year_id=2024, company_id=1)
    
    def setUp(self):
        self.client = Client()
    
    def test_workorder_list_view_get(self):
        response = self.client.get(reverse('design:workorder_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/workorder/list.html')
        self.assertIn('search_form', response.context)
        # Should not have work_orders in context directly as it's loaded via HTMX
        self.assertNotIn('work_orders', response.context)
        self.assertContains(response, 'TPL - Print Work Orders') # Check for title

    def test_workorder_table_partial_view_get(self):
        response = self.client.get(reverse('design:workorder_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/workorder/_workorder_table.html')
        self.assertIn('work_orders', response.context)
        self.assertEqual(len(response.context['work_orders']), 2) # Should list all by default

    def test_workorder_table_partial_view_search_wo_no(self):
        response = self.client.get(reverse('design:workorder_table'), {'search_by': '3', 'search_text': 'WO-001'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/workorder/_workorder_table.html')
        self.assertIn('work_orders', response.context)
        self.assertEqual(len(response.context['work_orders']), 1)
        self.assertEqual(response.context['work_orders'].first().wo_no, 'WO-001')

    def test_customer_autocomplete_view(self):
        response = self.client.get(reverse('design:customer_autocomplete'), {'query': 'test'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        self.assertIn('Test Customer A [101]', response.json())

        response = self.client.get(reverse('design:customer_autocomplete'), {'query': 'nonexistent'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), [])

    @patch('django.urls.reverse') # Mock reverse to avoid needing a real report app for test
    def test_print_workorder_view_post_success(self, mock_reverse):
        # Mock the reverse call to simulate the existence of 'report:print_tree_view'
        mock_reverse.return_value = '/mock/report/print-tree/'

        response = self.client.post(
            reverse('design:workorder_print', args=[self.work_order_1.id]),
            {'start_date': '01-01-2024', 'end_date': '31-01-2024'},
            HTTP_HX_REQUEST='true' # Indicate HTMX request
        )
        self.assertEqual(response.status_code, 204) # No Content
        self.assertIn('HX-Redirect', response)
        self.assertEqual(response['HX-Redirect'], '/mock/report/print-tree/?WONo=WO-001&SD=01-01-2024&TD=31-01-2024&ModId=3&SubModId=23')
        messages = list(response.context['messages']) if response.context else []
        # No messages for 204, message is handled by HTMX if needed by client.
        # But for non-HTMX, if we return a full response, message would appear.
        # For 204, messages are not typically rendered on the page, but can be sent via HX-Trigger.
        # So we omit testing for it here since it's an HTMX success.

    def test_print_workorder_view_post_not_found(self):
        response = self.client.post(
            reverse('design:workorder_print', args=[9999]), # Non-existent WO ID
            {'start_date': '01-01-2024', 'end_date': '31-01-2024'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 404)
        messages = list(response.context['messages']) if response.context else []
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Work Order not found.')

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
-   **HTMX for dynamic updates:**
    -   The `workorder_list.html` loads the initial `_workorder_table.html` via `hx-get` on `load` and `refreshWorkOrderList` event.
    -   Search dropdown `select` and `Search` button trigger `hx-get` requests to `workorder_table` endpoint, including all search form parameters via `hx-include="#search_form_container"`.
    -   Customer autocomplete `input` uses `hx-get` to `customer_autocomplete` endpoint, updating a `div` with suggestions. `hx-on--click` for suggestions allows selecting and triggering search.
    -   Each "Print" button in `_workorder_table.html` uses `hx-post` to `workorder_print` endpoint, including the `start_date` and `end_date` inputs from its own row (`hx-include="closest tr input[name='start_date'], closest tr input[name='end_date']"`).
    -   Successful "Print" actions return an `HX-Redirect` header which HTMX automatically follows, eliminating full page reloads for the printing workflow.
    -   `hx-indicator` is used to show a loading spinner during HTMX requests.

-   **Alpine.js for UI state management:**
    -   `x-data` is used on the search form container to manage the visibility of the two search input fields (`txtSearchCustomer` and `TxtSearchValue`) based on the `DropDownList1` selection. `x-model` binds the dropdown value, and `x-on:change` triggers the `updateVisibility` function. `x-show` and `x-transition.opacity` provide smooth transitions.

-   **DataTables for list views:**
    -   The `_workorder_table.html` renders a standard `<table>` with a specific ID (`workorderTable`).
    -   A `script` block within `workorder_list.html` (which gets executed after HTMX swaps the content) initializes DataTables on this table, providing client-side sorting, filtering, and pagination.
    -   Flatpickr is used for date input fields to provide a calendar picker, replacing `CalendarExtender`.

-   **Seamless Interaction:** All user interactions (search, autocomplete, triggering print) are designed to function without full page reloads, providing a modern Single Page Application (SPA)-like experience. Messages (e.g., success/error) can be delivered via `HX-Trigger` to be displayed by a global notification system in `base.html`.

### Final Notes

-   **Placeholders:** Remember to replace `default_company_id` and `default_fin_year_id` in `views.py` with logic that dynamically fetches these values from the authenticated user's session or profile, mimicking the ASP.NET `Session["compid"]` and `Session["finyear"]`.
-   **DRY Templates:** The `_workorder_table.html` serves as a partial that can be re-rendered independently by HTMX, adhering to DRY principles.
-   **Fat Model, Thin View:** Complex query logic and data enrichment are encapsulated within the `WorkOrderManager` and `WorkOrder` model properties (e.g., `tpl_date`, `current_date`), keeping views concise and focused on request/response handling.
-   **Testing:** The provided tests cover model behavior and key view interactions, including HTMX requests. Aim to expand these tests to achieve comprehensive coverage and validate all edge cases.
-   **Styling:** Tailwind CSS classes are applied throughout the templates for a modern, responsive design without custom CSS files, relying on a pre-configured Tailwind setup.
-   **External Dependencies:** Ensure jQuery, DataTables, Flatpickr, HTMX, and Alpine.js CDN links are present in your `core/base.html` or served locally.