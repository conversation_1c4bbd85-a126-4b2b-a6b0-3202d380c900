## ASP.NET to Django Conversion Script: Comprehensive Modernization Plan for Work Order Grid

This document outlines a detailed strategy for migrating your ASP.NET `TPL_Design_WO_Grid_Update.aspx` application to a modern Django-based solution. Our approach prioritizes automation, leveraging AI-assisted tools for code generation, and focuses on a 'Fat Model, Thin View' architecture with a rich, interactive frontend using HTMX and Alpine.js.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- **NEVER** include `base.html` template code in your output - assume it already exists and is configured with necessary CDN links (DataTables, HTMX, Alpine.js, Tailwind CSS).
- Focus **ONLY** on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

### Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code interacts with several tables to display work order information.
- `SD_Cust_WorkOrder_Master`: The primary table for work order details.
- `SD_Cust_Master`: Stores customer information, linked via `CustomerId`.
- `tblFinancial_master`: Stores financial year details, linked via `FinYearId`.
- `tblHR_OfficeStaff`: Stores employee details, linked via `SessionId` (referred to as `EmpId` in this table).

**Inferred Schema:**

**`SD_Cust_WorkOrder_Master` (Primary Table)**
- `WONo` (Work Order Number): DataKeyNames, HyperLinkField target. Likely `CharField` and potentially a primary key or unique identifier.
- `Id`: Implicit primary key from ASP.NET's typical `Id` column conventions if `WONo` is not the actual primary key. Assuming `WONo` as PK for simplicity based on usage.
- `EnqId` (Enquiry ID): `CharField`
- `CustomerId`: `IntegerField` (Foreign Key to `SD_Cust_Master`)
- `PONo` (Purchase Order Number): `CharField`
- `SysDate` (System Date/Generation Date): `CharField` (stored as string in ASP.NET, will be `DateField` in Django after conversion)
- `FinYearId`: `IntegerField` (Foreign Key to `tblFinancial_master`)
- `CompId` (Company ID): `IntegerField` (From session)
- `SessionId` (Generated By Employee ID): `IntegerField` (Foreign Key to `tblHR_OfficeStaff.EmpId`)

**`SD_Cust_Master`**
- `CustomerId`: `IntegerField` (Primary Key)
- `CustomerName`: `CharField`

**`tblFinancial_master`**
- `FinYearId`: `IntegerField` (Primary Key)
- `FinYear`: `CharField`

**`tblHR_OfficeStaff`**
- `EmpId`: `IntegerField` (Primary Key)
- `Title`: `CharField`
- `EmployeeName`: `CharField`

### Step 2: Identify Backend Functionality

**Read Operations:**
- **Initial Load:** On `Page_Load`, `BindDataCust` fetches all work orders filtered by `CompId` and `FinYearId` if no specific search criteria are applied.
- **Search:** `btnSearch_Click` triggers `BindDataCust` based on selected dropdown criteria (Customer Name, Enquiry No, PO No, WO No) and text input.
- **Pagination:** `SearchGridView1_PageIndexChanging` re-executes `BindDataCust` for the new page.
- **Autocomplete:** The `sql` WebMethod provides customer name suggestions for `TxtSearchValue`.

**No explicit Create, Update, or Delete operations are performed by this specific ASP.NET page.** The page is for "Edit" but only shows a grid with a hyperlink to another edit page (`TPL_Design_WO_TreeView_Edit.aspx`). Thus, for this module, the focus will be on the *Read* operation with advanced filtering and display. However, to comply with the general requirement for CRUD views, placeholder CBVs will be created for `Create`, `Update`, and `Delete`, but their primary functionality will be to direct to the list view after a simulated success, as their forms are not provided. The `list.html` will contain buttons for these for completeness.

**Validation Logic:**
- The ASP.NET code does simple checks like `if (txtSearchCustomer.Text != "")` before applying filters. Django forms and ORM will handle appropriate validation and filtering.

### Step 3: Infer UI Components

**ASP.NET Controls and their Django Equivalents:**

- `<asp:DropDownList ID="DropDownList1">`: Django `forms.ChoiceField` for search type.
- `<asp:TextBox ID="txtSearchCustomer">`: Django `forms.CharField` for search input.
- `<asp:TextBox ID="TxtSearchValue">` with `AutoCompleteExtender`: Django `forms.CharField` for customer search, coupled with an HTMX + Alpine.js powered autocomplete feature and a dedicated API endpoint.
- `<asp:Button ID="btnSearch">`: HTMX `hx-get` or `hx-post` on the search form.
- `<asp:GridView ID="SearchGridView1">`: Django template rendering a `<table>` enhanced by DataTables.

**Client-side Interactions:**
- `DropDownList1_SelectedIndexChanged`: Dynamic visibility of `txtSearchCustomer` and `TxtSearchValue`. This will be handled by Alpine.js in the Django template.
- `AutoCompleteExtender`: Handled by HTMX calls to a Django view returning JSON, paired with Alpine.js to manage the suggestion list.

### Step 4: Generate Django Code

We'll organize this into a Django application named `design_wo`.

#### 4.1 Models (`design_wo/models.py`)

```python
from django.db import models
from django.utils import timezone

# Assuming these are look-up tables from the existing database
# mapped via managed=False and db_table.

class FinancialYear(models.Model):
    """
    Represents the financial year table in the existing database.
    """
    finyearid = models.IntegerField(db_column='FinYearId', primary_key=True)
    finyear = models.CharField(db_column='FinYear', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.finyear

class Customer(models.Model):
    """
    Represents the customer master table in the existing database.
    """
    customerid = models.IntegerField(db_column='CustomerId', primary_key=True)
    customername = models.CharField(db_column='CustomerName', max_length=255)

    class Meta:
        managed = False
        db_table = 'SD_Cust_Master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customername

class Employee(models.Model):
    """
    Represents the HR Office Staff table for employees.
    """
    empid = models.IntegerField(db_column='EmpId', primary_key=True)
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employeename = models.CharField(db_column='EmployeeName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.title}. {self.employeename}" if self.title else self.employeename

class WorkOrderManager(models.Manager):
    """Custom manager for WorkOrder to handle complex queries."""
    def get_queryset(self):
        # Optimize by prefetching related data to avoid N+1 queries
        return super().get_queryset().select_related(
            'customerid', 'finyearid', 'sessionid'
        )

    def search_work_orders(self, search_type, search_value, comp_id, fin_year_id):
        """
        Applies search filters based on type and value, and system context.
        This method encapsulates the C# BindDataCust logic.
        """
        queryset = self.get_queryset().filter(compid=comp_id, finyearid__lte=fin_year_id) # Use __lte for FinYearId as per original code

        if search_value:
            if search_type == '1':  # Enquiry No
                queryset = queryset.filter(enqid__icontains=search_value)
            elif search_type == '2':  # PO No
                queryset = queryset.filter(pono__icontains=search_value)
            elif search_type == '3':  # WO No
                queryset = queryset.filter(wono__icontains=search_value)
            elif search_type == '0':  # Customer Name (searches by customer ID)
                try:
                    # Assuming search_value for customer is the CustomerId itself,
                    # or needs to be parsed from "Name [ID]" format
                    # For simplicity, let's assume direct CustomerId or lookup
                    customer_id = int(search_value) # If search_value contains just ID
                    queryset = queryset.filter(customerid__customerid=customer_id)
                except ValueError:
                    # Fallback if search_value is a customer name for direct lookup
                    queryset = queryset.filter(customerid__customername__icontains=search_value)

        return queryset.order_by('-id') # Order by Id Desc as per original

class WorkOrder(models.Model):
    """
    Represents the SD_Cust_WorkOrder_Master table.
    WONo is chosen as PK based on DataKeyNames and HyperLinkField.
    """
    wono = models.CharField(db_column='WONo', primary_key=True, max_length=50) # Assuming WONo is unique and char
    id = models.IntegerField(db_column='Id', unique=True) # Retain original Id if it's the actual auto-increment PK
    enqid = models.CharField(db_column='EnqId', max_length=50, blank=True, null=True)
    customerid = models.ForeignKey(Customer, on_delete=models.DO_NOTHING, db_column='CustomerId')
    pono = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    # Original SysDate is a string in SQL, converting to DateField.
    # Assumes a function to parse string date in actual production,
    # or db column is changed to DATE type.
    sysdate = models.DateField(db_column='SysDate', blank=True, null=True)
    finyearid = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId')
    compid = models.IntegerField(db_column='CompId')
    sessionid = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='SessionId')

    objects = WorkOrderManager() # Attach custom manager

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'
        # Ensure unique_together if WONo is not truly the primary key but should be unique
        # Example: unique_together = (('WONo', 'CompId', 'FinYearId'),) if composite key

    def __str__(self):
        return self.wono

    def get_generated_by(self):
        """Fetches the full employee name who generated the WO."""
        # This relationship is already fetched by select_related in the manager.
        return str(self.sessionid)

    def get_customer_name(self):
        """Fetches the customer name."""
        return self.customerid.customername

    def get_financial_year(self):
        """Fetches the financial year."""
        return self.finyearid.finyear

    # Example of a business logic method that could be added if needed
    def is_editable(self):
        """Determines if the work order is currently editable based on its status or date."""
        # This would require additional fields or logic
        # For now, it's a placeholder to illustrate "fat model"
        return True # Default placeholder
```

#### 4.2 Forms (`design_wo/forms.py`)

```python
from django import forms
from .models import WorkOrder, Customer

class WorkOrderSearchForm(forms.Form):
    """
    Form for searching work orders based on selected criteria.
    This is not a ModelForm as it's for filtering, not direct model instance creation/editing.
    """
    SEARCH_TYPES = [
        ('Select', 'Select'),
        ('0', 'Customer Name'),
        ('1', 'Enquiry No'),
        ('2', 'PO No'),
        ('3', 'WO No'),
    ]
    search_type = forms.ChoiceField(
        choices=SEARCH_TYPES,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                                   'hx-get': '/design_wo/workorder/search_form_fields/', # Endpoint to swap relevant search input
                                   'hx-target': '#search-input-container',
                                   'hx-swap': 'innerHTML',
                                   'hx-trigger': 'change' # Trigger on change
                                   }),
        initial='Select'
    )
    # This field will be dynamically shown/hidden by Alpine.js and swapped by HTMX
    search_input = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                                       'placeholder': 'Enter search value...'}),
        label="Search Value"
    )
    # This field is for customer autocomplete specifically
    customer_search_value = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Start typing customer name...',
            'x-ref': 'customerSearchInput', # Alpine.js ref
            'hx-get': '/design_wo/customer_autocomplete/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms, search', # Trigger on keyup after delay
            'hx-target': '#customer-suggestions', # Target for autocomplete results
            'hx-swap': 'innerHTML',
            'autocomplete': 'off',
            'x-on:focus': 'showSuggestions = true',
            'x-on:click.outside': 'showSuggestions = false',
            'x-on:keyup.escape': 'showSuggestions = false',
            'x-model': 'customerSearchQuery' # Bind to Alpine.js data
        }),
        label="Customer Name"
    )

    # For simplicity, the form's `clean` method can extract the ID from "Name [ID]" format
    def clean_customer_search_value(self):
        value = self.cleaned_data.get('customer_search_value')
        if value:
            # Try to extract ID from 'Name [ID]' format or assume it's just the ID
            import re
            match = re.search(r'\[(\d+)\]$', value)
            if match:
                return match.group(1) # Return the ID as a string
            # If no ID in brackets, try to find customer by name directly
            customer = Customer.objects.filter(customername__iexact=value).first()
            if customer:
                return str(customer.customerid) # Return the ID as a string
            # If it's not a known name or has no ID, return original value or raise error
            raise forms.ValidationError("Please select a valid customer from the suggestions or enter a valid ID.")
        return value

class WorkOrderForm(forms.ModelForm):
    """
    Form for creating/updating WorkOrder objects.
    This will serve as a placeholder as the original ASP.NET page
    doesn't have explicit CRUD forms, but the prompt requires them.
    """
    class Meta:
        model = WorkOrder
        # Exclude primary_key and auto-generated fields if not meant for user input
        # Note: WONo is PK, Id is unique. Assuming user does not input ID directly.
        # SysDate might be set by system on creation.
        fields = ['wono', 'enqid', 'customerid', 'pono', 'sysdate', 'finyearid', 'compid', 'sessionid']
        widgets = {
            'wono': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'enqid': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'customerid': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'pono': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sysdate': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'finyearid': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'compid': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sessionid': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
```

#### 4.3 Views (`design_wo/views.py`)

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from django.db.models import Q # For complex queries
from .models import WorkOrder, Customer, FinancialYear, Employee
from .forms import WorkOrderSearchForm, WorkOrderForm
from datetime import datetime # For date parsing if SysDate was string
import re # For parsing customer string

class WorkOrderListView(ListView):
    """
    Main view to display the work order search form and the initial table container.
    """
    model = WorkOrder
    template_name = 'design_wo/workorder/list.html'
    context_object_name = 'workorders' # This won't be used directly by list.html, as table is loaded via HTMX
    paginate_by = 20 # Initial pagination, but DataTables will handle it on client-side or via AJAX

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass the search form to the template
        context['search_form'] = WorkOrderSearchForm(self.request.GET)
        # Assuming current_comp_id and current_fin_year_id are available
        # from user session/profile, just like in ASP.NET code-behind
        # For now, hardcode or retrieve from a mock session/config
        context['current_comp_id'] = self.request.session.get('compid', 1)
        context['current_fin_year_id'] = self.request.session.get('finyear', 1)
        return context

class WorkOrderTablePartialView(ListView):
    """
    HTMX endpoint to render just the work order table.
    Handles the search and pagination logic.
    """
    model = WorkOrder
    template_name = 'design_wo/workorder/_workorder_table.html'
    context_object_name = 'workorders'
    # DataTables will handle client-side pagination/sorting/filtering if all data is sent.
    # If server-side processing is needed for large datasets, this would need to change
    # to return JSON for DataTables. For this migration, client-side DataTables with HTMX
    # for full table refreshes on search is sufficient.

    def get_queryset(self):
        # Default values from the original ASP.NET code, assumes these are set in session
        current_comp_id = self.request.session.get('compid', 1)
        current_fin_year_id = self.request.session.get('finyear', 1) # Note: ASP.NET used <= for FinYearId

        form = WorkOrderSearchForm(self.request.GET)
        if form.is_valid():
            search_type = form.cleaned_data.get('search_type')
            search_input = form.cleaned_data.get('search_input')
            customer_search_value = form.cleaned_data.get('customer_search_value')

            # The ASP.NET logic shows that customer search uses a different input field
            # and is handled by '0'.
            if search_type == '0':
                actual_search_value = customer_search_value
            else:
                actual_search_value = search_input
            
            # Use the custom manager method to apply filters
            queryset = WorkOrder.objects.search_work_orders(
                search_type, actual_search_value, current_comp_id, current_fin_year_id
            )
        else:
            # If form is not valid (e.g., initial load without search),
            # get default queryset for current comp/fin year.
            queryset = WorkOrder.objects.search_work_orders(
                'Select', None, current_comp_id, current_fin_year_id
            )
            
        return queryset

class WorkOrderSearchFormFieldsView(View):
    """
    HTMX endpoint to dynamically swap the search input field based on dropdown selection.
    """
    def get(self, request, *args, **kwargs):
        search_type = request.GET.get('search_type', 'Select')
        context = {
            'search_type': search_type,
            'form': WorkOrderSearchForm() # Create an instance to access field properties if needed
        }
        return render(request, 'design_wo/workorder/_search_input_fields.html', context)

class CustomerAutoCompleteView(View):
    """
    HTMX endpoint for customer name autocomplete functionality.
    Mimics the `sql` WebMethod from ASP.NET.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('q', '')
        if not prefix_text:
            return JsonResponse([], safe=False)

        # Assuming current_comp_id is needed for filtering customers if applicable
        # current_comp_id = request.session.get('compid', 1) 
        # For now, let's assume all customers are global or not filtered by compid here
        
        # Original C# code uses StartsWith, case-insensitive.
        customers = Customer.objects.filter(customername__istartswith=prefix_text).order_by('customername')[:10] # Limit to 10 results

        results = [
            f"{customer.customername} [{customer.customerid}]"
            for customer in customers
        ]
        return JsonResponse(results, safe=False) # safe=False for list of strings

# Placeholder CRUD Views - as the original ASP.NET page doesn't have these,
# these are included to fulfill the requirement for a full CRUD pattern.
# They will redirect back to the list view after operation.
class WorkOrderCreateView(CreateView):
    model = WorkOrder
    form_class = WorkOrderForm
    template_name = 'design_wo/workorder/form.html'
    success_url = reverse_lazy('workorder_list') # Redirect to list view

    def form_valid(self, form):
        # Mock setting context variables if not present in form
        if not form.instance.compid:
            form.instance.compid = self.request.session.get('compid', 1)
        if not form.instance.finyearid_id: # _id for FK field
            form.instance.finyearid_id = self.request.session.get('finyear', 1)
        if not form.instance.sessionid_id: # _id for FK field
            # Assuming user's employee ID is available in session
            form.instance.sessionid_id = self.request.session.get('employee_id', 1) # Default employee ID
        if not form.instance.sysdate:
            form.instance.sysdate = datetime.now().date() # Set current date

        response = super().form_valid(form)
        messages.success(self.request, 'Work Order added successfully.')
        if self.request.headers.get('HX-Request'):
            # HTMX triggers refresh of the list without full page reload
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshWorkOrderList' # Custom event to trigger table refresh
                }
            )
        return response

class WorkOrderUpdateView(UpdateView):
    model = WorkOrder
    form_class = WorkOrderForm
    template_name = 'design_wo/workorder/form.html'
    success_url = reverse_lazy('workorder_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Work Order updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWorkOrderList'
                }
            )
        return response

class WorkOrderDeleteView(DeleteView):
    model = WorkOrder
    template_name = 'design_wo/workorder/confirm_delete.html'
    success_url = reverse_lazy('workorder_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Work Order deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWorkOrderList'
                }
            )
        return response
```

#### 4.4 Templates

**`design_wo/workorder/list.html`**

```html
{% extends 'core/base.html' %}

{% block title %}TPL Design - Work Order Edit{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">TPL Design - Work Order Edit</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'workorder_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Work Order
        </button>
    </div>

    <!-- Search Section -->
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-700 mb-4">Search Work Orders</h3>
        <form id="search-form" 
              hx-get="{% url 'workorder_table' %}" 
              hx-target="#workorderTable-container" 
              hx-swap="innerHTML"
              hx-indicator="#table-loading-indicator"
              class="space-y-4">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="{{ search_form.search_type.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By</label>
                    {{ search_form.search_type }}
                </div>
                <div id="search-input-container" 
                     x-data="{ 
                         searchType: '{{ search_form.search_type.value }}',
                         customerSearchQuery: '',
                         showSuggestions: false,
                         suggestions: [],
                         selectSuggestion: function(value) {
                             this.customerSearchQuery = value;
                             this.showSuggestions = false;
                             this.$refs.customerSearchInput.dispatchEvent(new Event('change')); // Trigger HTMX search
                         }
                     }"
                     x-init="$watch('$refs.searchTypeSelect.value', value => searchType = value)">
                    
                    {% include 'design_wo/workorder/_search_input_fields.html' with search_type=search_form.search_type.value form=search_form %}
                </div>
            </div>
            <div class="flex justify-end mt-4">
                <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded shadow-sm">
                    <span hx-indicator="#table-loading-indicator" class="htmx-indicator">Searching...</span>
                    Search
                </button>
            </div>
        </form>
    </div>

    <!-- Table Section -->
    <div id="workorderTable-container"
         hx-trigger="load, refreshWorkOrderList from:body"
         hx-get="{% url 'workorder_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div id="table-loading-indicator" class="text-center htmx-indicator">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Work Orders...</p>
        </div>
    </div>
    
    <!-- Modal for form operations -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components are already defined inline for specific elements
        // No global Alpine.js component needed here for this page.
    });

    // Handle messages (e.g., success messages) from HTMX responses
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.xhr.status === 204) { // HTMX success, no content returned
            // If the modal was open, close it
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.remove('is-active');
            }
        }
    });

    // Ensure DataTables is initialized only after the table content is loaded/swapped
    document.body.addEventListener('htmx:afterSettle', function(evt) {
        if (evt.detail.target && evt.detail.target.id === 'workorderTable-container') {
            $('#workorderTable').DataTable({
                "pageLength": 20, // Match original ASP.NET PageSize
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "responsive": true
            });
        }
    });
</script>
{% endblock %}
```

**`design_wo/workorder/_workorder_table.html`** (Partial)

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-md">
    <table id="workorderTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry No</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. Date</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if workorders %}
                {% for obj in workorders %}
                <tr>
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.get_financial_year }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.get_customer_name }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ obj.customerid.customerid }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ obj.enqid }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ obj.pono }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">
                        <a href="{% url 'workorder_edit' obj.wono %}" class="text-blue-600 hover:text-blue-800 underline">
                            {{ obj.wono }}
                        </a>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ obj.sysdate|date:"d/m/Y" }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.get_generated_by }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">
                        <button 
                            class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                            hx-get="{% url 'workorder_edit' obj.wono %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            Edit
                        </button>
                        <button 
                            class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                            hx-get="{% url 'workorder_delete' obj.wono %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            Delete
                        </button>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
            <tr>
                <td colspan="10" class="py-4 text-center text-gray-500">No data to display !</td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>

```

**`design_wo/workorder/_search_input_fields.html`** (Partial for HTMX swapping)

```html
{% load tailwind_filters %} {# Assuming you have django-tailwind-filters installed for easy styling #}

{% if search_type == '0' %} {# Customer Name #}
    <div x-data="{ customerSearchQuery: '', showSuggestions: false, suggestions: [], selectedCustomer: null }"
         x-init="
            $watch('customerSearchQuery', (query) => {
                if (query.length > 0) {
                    // HTMX will handle fetching suggestions via hx-get on customer_search_value input
                } else {
                    suggestions = [];
                }
            });
            // Listen for htmx after swap on suggestions div to update Alpine state
            document.body.addEventListener('htmx:afterOnLoad', (evt) => {
                if (evt.detail.target.id === 'customer-suggestions') {
                    showSuggestions = true;
                }
            });
         "
         @click.outside="showSuggestions = false">
        <label for="{{ form.customer_search_value.id_for_label }}" class="block text-sm font-medium text-gray-700">Customer Name</label>
        <div class="relative">
            {{ form.customer_search_value }}
            <div id="customer-suggestions" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-60 overflow-auto"
                 x-show="showSuggestions && suggestions.length > 0"
                 x-transition:enter="transition ease-out duration-100"
                 x-transition:enter-start="opacity-0 scale-95"
                 x-transition:enter-end="opacity-100 scale-100"
                 x-transition:leave="transition ease-in duration-75"
                 x-transition:leave-start="opacity-100 scale-100"
                 x-transition:leave-end="opacity-0 scale-95">
                <template x-for="suggestion in suggestions" :key="suggestion">
                    <div class="py-2 px-3 cursor-pointer hover:bg-gray-100"
                         x-text="suggestion"
                         @click="selectSuggestion(suggestion)"></div>
                </template>
            </div>
        </div>
        {# Hidden input to hold the actual customer ID if needed by the form submission for filtering #}
        <input type="hidden" name="customer_id_for_search" x-model="selectedCustomer">
    </div>
{% else %}
    {# Generic search input for Enquiry No, PO No, WO No, or Select #}
    <label for="{{ form.search_input.id_for_label }}" class="block text-sm font-medium text-gray-700">Search Value</label>
    {% if search_type == 'Select' %}
        {# Initial state, input is empty and can be generic #}
        {{ form.search_input|as_tailwind_field }}
    {% else %}
        {# Specific input field for Enquiry/PO/WO number #}
        {{ form.search_input|as_tailwind_field }}
    {% endif %}
{% endif %}

<script>
    // This script block will re-run when the partial is swapped by HTMX.
    // It's crucial for Alpine.js to re-initialize or update its data.
    // If Alpine.js is already attached to the outer div (search-input-container),
    // it will pick up changes to x-data variables directly.
    // For the customer autocomplete, need to parse the HTMX response to update suggestions.
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target && evt.detail.target.id === 'customer-suggestions') {
            const data = JSON.parse(evt.detail.xhr.responseText);
            const alpineComponent = document.getElementById('search-input-container').__alpine;
            if (alpineComponent) {
                alpineComponent.$data.suggestions = data;
                alpineComponent.$data.showSuggestions = data.length > 0;
            }
        }
    });
</script>
```

**`design_wo/workorder/form.html`** (Partial)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Work Order</h3>
    <form hx-post="{{ request.path }}" hx-swap="none"> {# hx-swap="none" + HX-Trigger header for modal closure #}
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`design_wo/workorder/confirm_delete.html`** (Partial)

```html
<div class="p-6 text-center">
    <h3 class="text-xl font-medium text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Work Order "{{ object.wono }}"?</p>
    
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-center space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`design_wo/urls.py`)

```python
from django.urls import path
from .views import (
    WorkOrderListView, WorkOrderTablePartialView, WorkOrderSearchFormFieldsView,
    CustomerAutoCompleteView,
    WorkOrderCreateView, WorkOrderUpdateView, WorkOrderDeleteView
)

urlpatterns = [
    path('workorder/', WorkOrderListView.as_view(), name='workorder_list'),
    path('workorder/table/', WorkOrderTablePartialView.as_view(), name='workorder_table'),
    path('workorder/search_form_fields/', WorkOrderSearchFormFieldsView.as_view(), name='workorder_search_form_fields'),
    path('customer_autocomplete/', CustomerAutoCompleteView.as_view(), name='customer_autocomplete'),

    # Placeholder CRUD URLs
    path('workorder/add/', WorkOrderCreateView.as_view(), name='workorder_add'),
    path('workorder/edit/<str:pk>/', WorkOrderUpdateView.as_view(), name='workorder_edit'), # Using str:pk as WONo is CharField PK
    path('workorder/delete/<str:pk>/', WorkOrderDeleteView.as_view(), name='workorder_delete'),
]
```

#### 4.6 Tests (`design_wo/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import WorkOrder, Customer, FinancialYear, Employee
from datetime import date

class WorkOrderModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests. Use existing IDs or mock them for managed=False models.
        cls.fin_year = FinancialYear.objects.create(finyearid=2023, finyear='2023-2024')
        cls.customer = Customer.objects.create(customerid=101, customername='Test Customer A')
        cls.employee = Employee.objects.create(empid=1, title='Mr', employeename='Test Employee')

        WorkOrder.objects.create(
            id=1,
            wono='WO-2023-001',
            enqid='ENQ-001',
            customerid=cls.customer,
            pono='PO-001',
            sysdate=date(2023, 1, 15),
            finyearid=cls.fin_year,
            compid=1,
            sessionid=cls.employee
        )
        WorkOrder.objects.create(
            id=2,
            wono='WO-2023-002',
            enqid='ENQ-002',
            customerid=cls.customer,
            pono='PO-002',
            sysdate=date(2023, 2, 20),
            finyearid=cls.fin_year,
            compid=1,
            sessionid=cls.employee
        )
        WorkOrder.objects.create(
            id=3,
            wono='WO-2022-001',
            enqid='ENQ-003',
            customerid=Customer.objects.create(customerid=102, customername='Test Customer B'),
            pono='PO-003',
            sysdate=date(2022, 11, 1),
            finyearid=FinancialYear.objects.create(finyearid=2022, finyear='2022-2023'),
            compid=1,
            sessionid=cls.employee
        )

    def test_workorder_creation(self):
        wo = WorkOrder.objects.get(wono='WO-2023-001')
        self.assertEqual(wo.enqid, 'ENQ-001')
        self.assertEqual(wo.customerid.customername, 'Test Customer A')
        self.assertEqual(wo.finyearid.finyear, '2023-2024')
        self.assertEqual(wo.sessionid.employeename, 'Test Employee')

    def test_get_customer_name_method(self):
        wo = WorkOrder.objects.get(wono='WO-2023-001')
        self.assertEqual(wo.get_customer_name(), 'Test Customer A')

    def test_get_financial_year_method(self):
        wo = WorkOrder.objects.get(wono='WO-2023-001')
        self.assertEqual(wo.get_financial_year(), '2023-2024')

    def test_get_generated_by_method(self):
        wo = WorkOrder.objects.get(wono='WO-2023-001')
        self.assertEqual(wo.get_generated_by(), 'Mr. Test Employee')

    def test_workorder_manager_search(self):
        # Mock session variables for search logic
        session_data = {'compid': 1, 'finyear': 2023}
        with self.settings(SESSION_ENGINE='django.contrib.sessions.backends.file'):
            self.client.session['compid'] = session_data['compid']
            self.client.session['finyear'] = session_data['finyear']
            
            # Test search by WO No
            queryset = WorkOrder.objects.search_work_orders('3', 'WO-2023-001', session_data['compid'], session_data['finyear'])
            self.assertEqual(queryset.count(), 1)
            self.assertEqual(queryset.first().wono, 'WO-2023-001')

            # Test search by Customer Name (by ID extracted from clean method or direct ID)
            queryset = WorkOrder.objects.search_work_orders('0', str(self.customer.customerid), session_data['compid'], session_data['finyear'])
            self.assertEqual(queryset.count(), 2) # WO-2023-001, WO-2023-002

            # Test search by Enquiry No
            queryset = WorkOrder.objects.search_work_orders('1', 'ENQ-002', session_data['compid'], session_data['finyear'])
            self.assertEqual(queryset.count(), 1)
            self.assertEqual(queryset.first().wono, 'WO-2023-002')

            # Test search by PO No
            queryset = WorkOrder.objects.search_work_orders('2', 'PO-001', session_data['compid'], session_data['finyear'])
            self.assertEqual(queryset.count(), 1)
            self.assertEqual(queryset.first().pono, 'PO-001')
            
            # Test empty search
            queryset = WorkOrder.objects.search_work_orders('Select', '', session_data['compid'], session_data['finyear'])
            self.assertEqual(queryset.count(), 2) # WO-2023-001, WO-2023-002 (as finyearid <= 2023)

class WorkOrderViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create common test data for all view tests
        cls.fin_year_2023 = FinancialYear.objects.create(finyearid=2023, finyear='2023-2024')
        cls.customer_a = Customer.objects.create(customerid=101, customername='Test Customer A')
        cls.customer_b = Customer.objects.create(customerid=102, customername='Test Customer B')
        cls.employee_1 = Employee.objects.create(empid=1, title='Mr', employeename='Test Employee One')
        cls.employee_2 = Employee.objects.create(empid=2, title='Ms', employeename='Test Employee Two')

        cls.wo_1 = WorkOrder.objects.create(
            id=1, wono='WO-VIEW-001', enqid='ENQ-V001', customerid=cls.customer_a, pono='PO-V001',
            sysdate=date(2023, 3, 1), finyearid=cls.fin_year_2023, compid=1, sessionid=cls.employee_1
        )
        cls.wo_2 = WorkOrder.objects.create(
            id=2, wono='WO-VIEW-002', enqid='ENQ-V002', customerid=cls.customer_b, pono='PO-V002',
            sysdate=date(2023, 4, 1), finyearid=cls.fin_year_2023, compid=1, sessionid=cls.employee_2
        )

    def setUp(self):
        self.client = Client()
        # Mock session for compid and finyear, as used in views
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2023
        session['employee_id'] = self.employee_1.empid
        session.save()

    def test_list_view(self):
        response = self.client.get(reverse('workorder_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_wo/workorder/list.html')
        self.assertContains(response, 'TPL Design - Work Order Edit') # Check for main heading
        self.assertContains(response, 'id="workorderTable-container"') # Check for table container
        self.assertContains(response, 'id="search-form"') # Check for search form

    def test_workorder_table_partial_view_no_search(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('workorder_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_wo/workorder/_workorder_table.html')
        self.assertContains(response, 'WO-VIEW-001')
        self.assertContains(response, 'WO-VIEW-002')
        self.assertEqual(response.context['workorders'].count(), 2) # Both work orders for compid=1, finyear<=2023

    def test_workorder_table_partial_view_search_wono(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('workorder_table') + '?search_type=3&search_input=WO-VIEW-001', **headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO-VIEW-001')
        self.assertNotContains(response, 'WO-VIEW-002')
        self.assertEqual(response.context['workorders'].count(), 1)

    def test_workorder_table_partial_view_search_customer_name(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('workorder_table') + f'?search_type=0&customer_search_value={self.customer_a.customerid}', **headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO-VIEW-001')
        self.assertNotContains(response, 'WO-VIEW-002')
        self.assertEqual(response.context['workorders'].count(), 1)
        
    def test_search_form_fields_view(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('workorder_search_form_fields') + '?search_type=0', **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_wo/workorder/_search_input_fields.html')
        self.assertContains(response, 'name="customer_search_value"') # Check for customer specific input
        self.assertNotContains(response, 'name="search_input"') # Check for generic input absence

        response = self.client.get(reverse('workorder_search_form_fields') + '?search_type=1', **headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'name="search_input"') # Check for generic input
        self.assertNotContains(response, 'name="customer_search_value"') # Check for customer specific input absence

    def test_customer_autocomplete_view(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('customer_autocomplete') + '?q=Test Customer', **headers)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = response.json()
        self.assertIn(f'{self.customer_a.customername} [{self.customer_a.customerid}]', data)
        self.assertIn(f'{self.customer_b.customername} [{self.customer_b.customerid}]', data)

    # Placeholder CRUD view tests
    def test_create_view_get(self):
        response = self.client.get(reverse('workorder_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_wo/workorder/form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Work Order')

    def test_create_view_post_htmx(self):
        data = {
            'wono': 'WO-NEW-001', 'id': 10, 'enqid': 'ENQ-NEW', 
            'customerid': self.customer_a.customerid, 'pono': 'PO-NEW',
            'sysdate': '2024-01-01', 'finyearid': self.fin_year_2023.finyearid,
            'compid': 1, 'sessionid': self.employee_1.empid
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('workorder_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWorkOrderList')
        self.assertTrue(WorkOrder.objects.filter(wono='WO-NEW-001').exists())

    def test_update_view_get(self):
        response = self.client.get(reverse('workorder_edit', args=[self.wo_1.wono]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_wo/workorder/form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Edit Work Order')
        self.assertContains(response, self.wo_1.wono)

    def test_update_view_post_htmx(self):
        data = {
            'wono': self.wo_1.wono, 'id': self.wo_1.id, 'enqid': 'ENQ-UPDATED', 
            'customerid': self.customer_a.customerid, 'pono': 'PO-UPDATED',
            'sysdate': '2023-03-01', 'finyearid': self.fin_year_2023.finyearid,
            'compid': 1, 'sessionid': self.employee_1.empid
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('workorder_edit', args=[self.wo_1.wono]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWorkOrderList')
        self.wo_1.refresh_from_db()
        self.assertEqual(self.wo_1.enqid, 'ENQ-UPDATED')

    def test_delete_view_get(self):
        response = self.client.get(reverse('workorder_delete', args=[self.wo_1.wono]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_wo/workorder/confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, self.wo_1.wono)

    def test_delete_view_post_htmx(self):
        wo_to_delete_wono = self.wo_2.wono # Use wo_2 to avoid affecting other tests if wo_1 is needed
        initial_count = WorkOrder.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('workorder_delete', args=[wo_to_delete_wono]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWorkOrderList')
        self.assertEqual(WorkOrder.objects.count(), initial_count - 1)
        self.assertFalse(WorkOrder.objects.filter(wono=wo_to_delete_wono).exists())
```

### Step 5: HTMX and Alpine.js Integration

The generated code extensively uses HTMX and Alpine.js for a seamless user experience:

-   **HTMX for dynamic updates:**
    -   The main list view (`list.html`) uses `hx-get` to load the `_workorder_table.html` partial on page load and on `refreshWorkOrderList` event (triggered by CRUD operations).
    -   The search form uses `hx-get` to update the `_workorder_table.html` when the form is submitted.
    -   The dropdown `search_type` uses `hx-get` to dynamically swap `_search_input_fields.html` into `search-input-container`, changing the visible search input (generic text or customer autocomplete).
    -   CRUD buttons (`Add`, `Edit`, `Delete`) use `hx-get` to fetch the respective form/confirmation partials into a modal (`#modalContent`).
    -   Form submissions (e.g., `_workorder_form.html`, `_confirm_delete.html`) use `hx-post` with `hx-swap="none"` and rely on `HX-Trigger: refreshWorkOrderList` from the server to update the main list after a successful operation, along with closing the modal.
    -   The customer autocomplete input uses `hx-get` to query `customer_autocomplete` endpoint and `hx-target` to update a suggestions `div`.

-   **Alpine.js for UI state management:**
    -   The `search-input-container` uses `x-data` to manage `searchType` (which reflects the selected dropdown value) and dynamically show/hide the correct input field.
    -   For customer autocomplete, Alpine.js manages `customerSearchQuery`, `showSuggestions`, and `suggestions` arrays, providing client-side control over the autocomplete dropdown, and a `selectSuggestion` function to populate the input and hide suggestions.

-   **DataTables for List Views:**
    -   The `_workorder_table.html` partial is designed to be initialized as a DataTables table. A `script` tag within this partial ensures DataTables is initialized every time the table content is re-loaded via HTMX. This handles client-side searching, sorting, and pagination.

-   **Seamless Interactions:** All interactions (search, filtering, CRUD form display, form submission, autocomplete) occur without full page reloads, providing a highly responsive user experience. Messages (e.g., "Work Order added successfully") would typically be handled by a global HTMX event listener or Django's `messages` framework, which is then styled by Tailwind CSS.

### Final Notes

-   **Placeholders:** `[APP_NAME]`, `[MODEL_NAME]`, etc., have been replaced with concrete values like `design_wo`, `WorkOrder`, etc.
-   **DRY Templates:** The `list.html` includes partials for the search input fields and the table itself, promoting reusability and keeping the main template clean.
-   **Fat Model, Thin View:** The `WorkOrderManager.search_work_orders` method encapsulates the complex data retrieval and filtering logic, keeping the `WorkOrderTablePartialView`'s `get_queryset` method concise.
-   **Comprehensive Tests:** Unit tests for models and integration tests for all views (including HTMX-specific interactions) are provided to ensure functionality and maintainability.
-   **`core/base.html` Assumption:** This plan assumes `core/base.html` includes all necessary CDN links for HTMX, Alpine.js, jQuery, DataTables, and Tailwind CSS, following the DRY principle.
-   **Session Context:** The original ASP.NET code relies heavily on `Session["compid"]`, `Session["finyear"]`, and `Session["username"]` (`SessionId`). In Django, these would typically be managed through the user's authenticated session or a custom middleware that sets context variables, ensuring data is filtered appropriately for the current user's company and financial year. The provided Django code uses `request.session.get('compid', 1)` and `request.session.get('finyear', 1)` as placeholders.
-   **Date Format:** The original `SysDate` parsing in ASP.NET was complex (string manipulation to convert to datetime). The Django `WorkOrder` model assumes `sysdate` is a `DateField`. If the database truly stores it as a string, a custom field or a conversion layer would be needed upon data retrieval/saving. For this migration, we simplified to `models.DateField`.
-   **Error Handling:** Basic `try-catch` was in C#. In Django, form validation and `try-except` blocks (or proper error logging) in views/models would handle this more robustly.