## ASP.NET to Django Conversion Script: Work Order Assembly Copy Module

This modernization plan outlines the strategic transition of your ASP.NET `TPL_Design_Root_Assembly_Copy_WO` module to a robust, modern Django application. Our approach leverages AI-assisted automation, focusing on a clean, scalable architecture with Django, HTMX, and Alpine.js for a seamless user experience, all while ensuring business logic is centralized and testable.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code interacts with several tables, primarily fetching data for display and search.

-   **`SD_Cust_WorkOrder_Master`**: This is the main table for work order details.
    *   `Id`: Primary key (inferred from `Order by Id Desc`).
    *   `EnqId`: Enquiry ID (string).
    *   `CustomerId`: Customer ID (string, foreign key to `SD_Cust_Master`).
    *   `WONo`: Work Order Number (string, `DataKeyNames` in GridView, unique identifier).
    *   `PONo`: Purchase Order Number (string).
    *   `SessionId`: Employee ID (string, foreign key to `tblHR_OfficeStaff`, seems to store who generated the record).
    *   `FinYearId`: Financial Year ID (integer, foreign key to `tblFinancial_master`).
    *   `SysDate`: System Date (string, e.g., "MM-DD-YYYY" or "DD-MM-YYYY" from `SysDate` parsing logic; will be mapped to `DateField` if possible or processed by a model property).
    *   `CompId`: Company ID (integer, used in WHERE clauses).

-   **`SD_Cust_Master`**: Contains customer information.
    *   `CustomerId`: Customer ID (string, primary key).
    *   `CustomerName`: Customer Name (string).
    *   `FinYearId`: Financial Year ID (integer).
    *   `CompId`: Company ID (integer).

-   **`tblFinancial_master`**: Stores financial year details.
    *   `FinYearId`: Financial Year ID (integer, primary key).
    *   `FinYear`: Financial Year (string, e.g., "2023-24").

-   **`tblHR_OfficeStaff`**: Contains employee information.
    *   `EmpId`: Employee ID (string, primary key).
    *   `EmployeeName`: Employee Name (string, potentially prefixed with `Title`).
    *   `Title`: Employee Title (string).
    *   `FinYearId`: Financial Year ID (integer).
    *   `CompId`: Company ID (integer).

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations performed by the ASP.NET code.

**Analysis:**
The current ASP.NET page (`TPL_Design_Root_Assembly_Copy_WO.aspx`) primarily functions as a **search and list interface** for existing Work Orders, with a specific focus on enabling a "copy from" operation.

-   **Read/List:** The main functionality is to display a list of work orders (`SearchGridView1`) based on various criteria.
-   **Search/Filter:** Users can search work orders by:
    *   Customer Name (with an autocomplete feature).
    *   Enquiry Number.
    *   PO Number.
    *   WO Number.
-   **Pagination:** The `GridView` supports pagination.
-   **Dynamic Link Generation:** The `WONo` column dynamically becomes a hyperlink to another page (`TPL_Design_Root_Assembly_Copy_Grid.aspx`), passing `WONoSrc` (the current WO No), `WONoDest` (from query string), `ModId`, and `SubModId`. This indicates a flow where a source work order is selected to be copied.
-   **Session Management:** `CompId` and `FinYearId` are retrieved from session for filtering data, which is typical for multi-company/multi-fiscal year ERP systems.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles to map them to modern Django templates with HTMX, Alpine.js, and Tailwind CSS.

**Analysis:**
-   **Search Controls:**
    *   `DropDownList1` (Dropdown): Maps to an HTML `<select>` element for search criteria selection.
    *   `txtSearchCustomer` (Text Box): Maps to an HTML `<input type="text">` for general search.
    *   `TxtSearchValue` (Text Box with AutoCompleteExtender): Maps to an HTML `<input type="text">` that triggers an HTMX-powered autocomplete for customer names.
    *   `btnSearch` (Button): Maps to an HTMX-triggered button that refreshes the work order list.
-   **Data Display:**
    *   `SearchGridView1` (GridView): This will be replaced by a modern HTML `<table>` integrated with DataTables.js for client-side search, sort, and pagination capabilities.
-   **Dynamic UI Elements:**
    *   The visibility toggle between `txtSearchCustomer` and `TxtSearchValue` will be handled by Alpine.js `x-show` directives.
    *   The entire search and list interaction will use HTMX to avoid full page reloads, making it highly responsive.
-   **Styling:** All styling will be done with Tailwind CSS classes, replacing the legacy `yui-datatable.css` and `StyleSheet.css`.

### Step 4: Generate Django Code

We will create a new Django application named `workorder_copy`.

#### 4.1 Models (`workorder_copy/models.py`)

**Task:** Create Django models based on the identified database schema. We'll map to existing tables using `managed = False` and `db_table`. Business logic (like parsing `SysDate` or retrieving related data) will be added as properties or methods within the models.

```python
from django.db import models
from django.utils import timezone
from datetime import datetime

# Assuming these models might exist in a core or master_data app.
# For this conversion, we'll define them here for completeness,
# assuming they map to existing legacy tables.

class FinancialYear(models.Model):
    """
    Maps to tblFinancial_master.
    Represents financial years for company records.
    """
    finyearid = models.IntegerField(db_column='FinYearId', primary_key=True)
    finyear = models.CharField(db_column='FinYear', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.finyear or f"ID: {self.finyearid}"

class Employee(models.Model):
    """
    Maps to tblHR_OfficeStaff.
    Represents employees or staff who generate records.
    """
    empid = models.CharField(db_column='EmpId', primary_key=True, max_length=50)
    employeename = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    # Assuming CompId and FinYearId are relevant here too based on ASP.NET code
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)
    finyearid = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.title or ''}.{self.employeename or ''}".strip('.')

class Customer(models.Model):
    """
    Maps to SD_Cust_Master.
    Represents customer information.
    """
    customerid = models.CharField(db_column='CustomerId', primary_key=True, max_length=50)
    customername = models.CharField(db_column='CustomerName', max_length=255, blank=True, null=True)
    # Assuming CompId and FinYearId are relevant here too
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)
    finyearid = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_Master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customername or ''} [{self.customerid}]"

class WorkOrderMaster(models.Model):
    """
    Maps to SD_Cust_WorkOrder_Master.
    Represents the main work order records.
    """
    id = models.AutoField(db_column='Id', primary_key=True) # Assuming 'Id' is the auto-incrementing PK
    enqid = models.CharField(db_column='EnqId', max_length=50, blank=True, null=True)
    # Use ForeignKey for relationships to avoid N+1 queries.
    customer = models.ForeignKey(Customer, models.DO_NOTHING, db_column='CustomerId', to_field='customerid', blank=True, null=True, related_name='work_orders')
    wono = models.CharField(db_column='WONo', max_length=50, blank=True, null=True, unique=True) # Assumed unique
    pono = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    employee = models.ForeignKey(Employee, models.DO_NOTHING, db_column='SessionId', to_field='empid', blank=True, null=True, related_name='generated_work_orders')
    fin_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYearId', to_field='finyearid', blank=True, null=True, related_name='work_orders')
    sysdate_str = models.CharField(db_column='SysDate', max_length=50, blank=True, null=True) # Original string format

    # Assuming CompId exists in the actual table for filtering
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'
        ordering = ['-id'] # Matches 'Order by Id Desc'

    @property
    def parsed_sysdate(self):
        """
        Parses the SysDate_str (MM-DD-YYYY or DD-MM-YYYY) into a datetime object.
        The ASP.NET code's substring/charindex suggests MM-DD-YYYY or DD-MM-YYYY parsing.
        Let's try a robust approach.
        """
        if self.sysdate_str:
            try:
                # Attempt MM-DD-YYYY first
                return datetime.strptime(self.sysdate_str, '%m-%d-%Y').date()
            except ValueError:
                try:
                    # Attempt DD-MM-YYYY second
                    return datetime.strptime(self.sysdate_str, '%d-%m-%Y').date()
                except ValueError:
                    # Handle other potential formats or return None
                    return None
        return None

    @staticmethod
    def get_customer_id_from_display(display_text):
        """
        Utility method to extract CustomerId from "Customer Name [CustomerId]" format,
        mimicking ASP.NET's fun.getCode().
        """
        if display_text and '[' in display_text and ']' in display_text:
            try:
                start = display_text.rfind('[') + 1
                end = display_text.rfind(']')
                return display_text[start:end]
            except IndexError:
                pass
        return None

    def __str__(self):
        return self.wono or f"Work Order {self.id}"

```

#### 4.2 Forms (`workorder_copy/forms.py`)

**Task:** Define a Django form for the search criteria. This will be a simple `Form` (not `ModelForm`) as it's purely for filtering.

```python
from django import forms

class WorkOrderCopySearchForm(forms.Form):
    """
    Form to handle the search criteria for Work Order Copy.
    Corresponds to DropDownList1, txtSearchCustomer, and TxtSearchValue.
    """
    SEARCH_BY_CHOICES = [
        ('select', 'Select'), # This is the "Select" option in ASP.NET
        ('customer_name', 'Customer Name'), # Maps to Value="0"
        ('enquiry_no', 'Enquiry No'),      # Maps to Value="1"
        ('po_no', 'PO No'),                # Maps to Value="2"
        ('wo_no', 'WO No'),                # Maps to Value="3"
    ]

    search_by = forms.ChoiceField(
        choices=SEARCH_BY_CHOICES,
        initial='select',
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                                   'x-model': 'searchByType', # Alpine.js binding
                                   'hx-get': '?', # Trigger GET request on change to update search form
                                   'hx-trigger': 'change',
                                   'hx-target': '#searchFormContent', # Target the form section for swap
                                   'hx-swap': 'outerHTML' # Swap out the entire search form content
                                   }),
        label="Search By"
    )

    search_text_general = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                                      'placeholder': 'Enter search value',
                                      'x-show': "searchByType != 'customer_name'", # Alpine.js binding
                                      'x-transition',
                                      }),
        label="Search Value"
    )

    search_text_customer_autocomplete = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                                      'placeholder': 'Start typing customer name...',
                                      'x-show': "searchByType == 'customer_name'", # Alpine.js binding
                                      'x-transition',
                                      'hx-get': '/workorder_copy/customer-autocomplete/', # HTMX endpoint for autocomplete
                                      'hx-trigger': 'keyup changed delay:500ms, search',
                                      'hx-target': '#customer-autocomplete-results',
                                      'hx-swap': 'innerHTML',
                                      'name': 'search_text_customer_autocomplete', # Explicit name for HTMX
                                      'autocomplete': 'off',
                                      }),
        label="Customer Name"
    )

    def clean(self):
        cleaned_data = super().clean()
        search_by = cleaned_data.get('search_by')
        search_text_general = cleaned_data.get('search_text_general')
        search_text_customer_autocomplete = cleaned_data.get('search_text_customer_autocomplete')

        if search_by != 'select':
            if search_by == 'customer_name' and not search_text_customer_autocomplete:
                self.add_error('search_text_customer_autocomplete', 'Please enter a customer name for search.')
            elif search_by != 'customer_name' and not search_text_general:
                self.add_error('search_text_general', 'Please enter a search value.')

        return cleaned_data

```

#### 4.3 Views (`workorder_copy/views.py`)

**Task:** Implement Django Class-Based Views for listing work orders, handling search, and providing autocomplete functionality. Views will be thin, delegating complex query logic to models or custom managers if needed.

```python
from django.views.generic import ListView, TemplateView
from django.db.models import Q
from django.http import JsonResponse, HttpResponse
from django.template.loader import render_to_string
from django.shortcuts import render
from django.conf import settings
from .models import WorkOrderMaster, Customer
from .forms import WorkOrderCopySearchForm
import logging

logger = logging.getLogger(__name__)

class WorkOrderCopyListView(TemplateView):
    """
    Main view for the TPL Root Assembly Copy From page.
    Renders the search form and sets up the container for the HTMX-loaded table.
    """
    template_name = 'workorder_copy/workordermaster/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the form with current request GET parameters
        form = WorkOrderCopySearchForm(self.request.GET)
        context['form'] = form

        # WoNoDest from query string, crucial for the copy link
        context['wono_dest'] = self.request.GET.get('WONoDest', '')
        # Assuming ModId and SubModId are also passed for the destination link
        context['mod_id'] = self.request.GET.get('ModId', '3') # Default from ASP.NET
        context['submod_id'] = self.request.GET.get('SubModId', '23') # Default from ASP.NET

        # For initial load of the search form based on dropdown selection
        context['initial_search_by'] = form.data.get('search_by', 'select')
        
        return context

class WorkOrderCopyTablePartialView(ListView):
    """
    HTMX-powered partial view to render the Work Order table.
    Handles filtering, pagination, and sorting.
    This view is specifically designed to be called by HTMX.
    """
    model = WorkOrderMaster
    template_name = 'workorder_copy/workordermaster/_workorder_table.html'
    context_object_name = 'workordermasters'
    paginate_by = 17 # Matches ASP.NET GridView PageSize

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # In a real ERP, CompId and FinYearId would come from current user's session/profile
        # For this example, let's use placeholder values or fetch from request if present.
        # ASP.NET code retrieved them from Session["compid"] and Session["finyear"]
        # Example: user_comp_id = self.request.session.get('compid')
        # user_fin_year_id = self.request.session.get('finyear')
        
        # Placeholder for dynamic CompId and FinYearId from user context
        # IMPORTANT: In a real system, retrieve these from the logged-in user's profile or session.
        # For demonstration, we'll assume they are fixed or passed via URL.
        # If not passed, the ASP.NET defaults were 0 and 0.
        user_comp_id = int(self.request.GET.get('comp_id', 1)) # Assuming a default company ID
        user_fin_year_id = int(self.request.GET.get('fin_year_id', 1)) # Assuming a default financial year ID

        # Apply CompId and FinYearId filters
        queryset = queryset.filter(compid=user_comp_id, fin_year__finyearid__lte=user_fin_year_id)

        form = WorkOrderCopySearchForm(self.request.GET)
        if form.is_valid():
            search_by = form.cleaned_data.get('search_by')
            search_text_general = form.cleaned_data.get('search_text_general')
            search_text_customer_autocomplete = form.cleaned_data.get('search_text_customer_autocomplete')
            
            if search_by == 'customer_name' and search_text_customer_autocomplete:
                customer_id = WorkOrderMaster.get_customer_id_from_display(search_text_customer_autocomplete)
                if customer_id:
                    queryset = queryset.filter(customer__customerid=customer_id)
                else:
                    # If ID not found, try matching by name directly or return empty
                    queryset = queryset.filter(customer__customername__icontains=search_text_customer_autocomplete)
            elif search_by == 'enquiry_no' and search_text_general:
                queryset = queryset.filter(enqid=search_text_general)
            elif search_by == 'po_no' and search_text_general:
                queryset = queryset.filter(pono=search_text_general)
            elif search_by == 'wo_no' and search_text_general:
                queryset = queryset.filter(wono=search_text_general)
            # If search_by is 'select' or no search text, display all (within comp/fin year filter)

        # Prefetch related data to avoid N+1 queries, mimicking the ASP.NET joins
        queryset = queryset.select_related('customer', 'fin_year', 'employee')
        
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass WONoDest for generating the "copy" link in the table
        context['wono_dest'] = self.request.GET.get('WONoDest', '')
        context['mod_id'] = self.request.GET.get('ModId', '3')
        context['submod_id'] = self.request.GET.get('SubModId', '23')
        return context

class CustomerAutoCompleteView(TemplateView):
    """
    Provides customer name and ID for the autocomplete functionality via HTMX/JSON.
    Mimics the ASP.NET WebMethod 'sql'.
    """
    template_name = 'workorder_copy/workordermaster/_customer_autocomplete_results.html'

    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('search_text_customer_autocomplete', '').strip()
        # In a real ERP, CompId would come from current user's session/profile
        user_comp_id = int(request.GET.get('comp_id', 1)) # Default if not provided

        customers = []
        if prefix_text:
            # Query customers filtering by name and company ID
            customers_qs = Customer.objects.filter(
                customername__icontains=prefix_text,
                compid=user_comp_id # Apply company ID filter
            ).values('customername', 'customerid')[:10] # Limit results as in ASP.NET

            for cust in customers_qs:
                customers.append(f"{cust['customername']} [{cust['customerid']}]")
        
        # Render a simple unordered list for HTMX to swap
        context = {'customers': customers}
        return render(request, self.template_name, context)

```

#### 4.4 Templates (`workorder_copy/templates/workorder_copy/workordermaster/`)

**Task:** Create templates for the main list page and the HTMX-loaded table partial.

**`workorder_copy/workordermaster/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ searchByType: '{{ initial_search_by }}' }">
    <div class="bg-gray-100 p-4 rounded-lg shadow-md mb-6">
        <h2 class="text-xl font-bold text-gray-800 mb-4">TPL Root Assembly Copy From</h2>
        <form id="searchForm"
              hx-get="{% url 'workorder_copy:workordermaster_table' %}"
              hx-target="#workordermasterTable-container"
              hx-trigger="submit, refreshWorkOrderList from:body"
              hx-indicator="#loadingIndicator"
              class="space-y-4">
            
            <div id="searchFormContent" class="flex flex-wrap items-center gap-4" hx-swap="outerHTML">
                {% csrf_token %}
                {# This section will be swapped when search_by dropdown changes #}
                <div class="flex-grow">
                    <label for="{{ form.search_by.id_for_label }}" class="sr-only">{{ form.search_by.label }}</label>
                    {{ form.search_by }}
                </div>
                
                <div class="relative flex-grow min-w-[300px]">
                    <label for="{{ form.search_text_general.id_for_label }}" class="sr-only">{{ form.search_text_general.label }}</label>
                    {{ form.search_text_general }}
                    
                    <label for="{{ form.search_text_customer_autocomplete.id_for_label }}" class="sr-only">{{ form.search_text_customer_autocomplete.label }}</label>
                    {{ form.search_text_customer_autocomplete }}
                    
                    {# Autocomplete results container #}
                    <div id="customer-autocomplete-results" 
                         class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg mt-1 w-full max-h-60 overflow-y-auto"
                         _="on htmx:afterOnLoad if htmx.response.length == 0 add .hidden else remove .hidden">
                        {# Autocomplete suggestions will be loaded here #}
                    </div>
                </div>

                <div class="flex-grow-0">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                        Search
                    </button>
                </div>
            </div>
            
            {# Pass WONoDest, ModId, SubModId as hidden inputs for the table partial to use #}
            <input type="hidden" name="WONoDest" value="{{ wono_dest }}">
            <input type="hidden" name="ModId" value="{{ mod_id }}">
            <input type="hidden" name="SubModId" value="{{ submod_id }}">

        </form>
    </div>
    
    <div id="workordermasterTable-container"
         hx-trigger="load delay:100ms, refreshWorkOrderList from:body" {# Initial load and refresh trigger #}
         hx-get="{% url 'workorder_copy:workordermaster_table' %}?{{ request.GET.urlencode }}" {# Pass current GET params for filtering #}
         hx-swap="innerHTML">
        {# Initial loading state #}
        <div class="text-center py-8">
            <div id="loadingIndicator" class="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] text-blue-600 motion-reduce:animate-[spin_1.5s_linear_infinite]" role="status">
                <span class="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">Loading...</span>
            </div>
            <p class="mt-4 text-gray-600">Loading Work Orders...</p>
        </div>
    </div>
    
    {# Modal for future CRUD if implemented, though not needed for this specific page #}
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me, toggle .hidden on me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('workOrderSearch', () => ({
            searchByType: '{{ initial_search_by }}', // Initial value for Alpine model
            // Add any other Alpine state or functions here if needed
        }));
    });

    // Ensure DataTables is initialized only after the HTMX swap
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.target.id === 'workordermasterTable-container') {
            $('#workordermasterTable').DataTable({
                "pageLength": {{ workordermasters.paginator.per_page }}, // Use Django's paginate_by
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "paging": true,
                "searching": true, // Enable client-side search by DataTables
                "ordering": true,  // Enable client-side ordering
                "info": true
            });
        }
    });
</script>
{% endblock %}
```

**`workorder_copy/templates/workorder_copy/workordermaster/_workorder_table.html`**

```html
<div class="overflow-x-auto bg-white rounded-lg shadow">
    <table id="workordermasterTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WONo</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
                {# No 'Actions' column as this is a copy-from page with specific link #}
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if workordermasters %}
                {% for obj in workordermasters %}
                <tr>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-right text-gray-900">{{ forloop.counter0|add:workordermasters.start_index }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-center text-gray-900">{{ obj.fin_year.finyear|default_if_none:"" }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-left text-gray-900">{{ obj.customer.customername|default_if_none:"" }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.customer.customerid|default_if_none:"" }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.enqid|default_if_none:"" }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-left text-gray-900">{{ obj.pono|default_if_none:"" }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">
                        <a href="{% url 'workorder_copy:root_assembly_copy_grid' %}?WONoSrc={{ obj.wono|urlencode }}&WONoDest={{ wono_dest|urlencode }}&ModId={{ mod_id }}&SubModId={{ submod_id }}" 
                           class="text-blue-600 hover:text-blue-800 hover:underline">
                            {{ obj.wono|default_if_none:"" }}
                        </a>
                    </td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.parsed_sysdate|date:"d/m/Y"|default_if_none:"" }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-left text-gray-900">{{ obj.employee|default_if_none:"" }}</td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="9" class="py-4 text-center text-lg text-maroon-600 font-semibold">
                        No data to display !
                    </td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

{# Pagination (optional, DataTables handles it client-side. If server-side pagination is needed, this would change) #}
{# Example for server-side pagination if DataTables is not used #}
{% if workordermasters.has_other_pages %}
<nav class="flex justify-center mt-4">
    <ul class="flex list-style-none">
        {% if workordermasters.has_previous %}
            <li><a hx-get="?page={{ workordermasters.previous_page_number }}&{{ request.GET.urlencode }}" 
                   hx-target="#workordermasterTable-container" hx-swap="innerHTML" class="relative block py-2 px-3 leading-tight bg-white border border-gray-300 text-blue-700 hover:bg-gray-100 rounded-l-md">Previous</a></li>
        {% endif %}
        {% for i in workordermasters.paginator.page_range %}
            {% if workordermasters.number == i %}
                <li><a hx-get="?page={{ i }}&{{ request.GET.urlencode }}" 
                       hx-target="#workordermasterTable-container" hx-swap="innerHTML" class="relative block py-2 px-3 leading-tight bg-blue-600 border border-blue-600 text-white hover:bg-blue-700">{{ i }}</a></li>
            {% else %}
                <li><a hx-get="?page={{ i }}&{{ request.GET.urlencode }}" 
                       hx-target="#workordermasterTable-container" hx-swap="innerHTML" class="relative block py-2 px-3 leading-tight bg-white border border-gray-300 text-blue-700 hover:bg-gray-100">{{ i }}</a></li>
            {% endif %}
        {% endfor %}
        {% if workordermasters.has_next %}
            <li><a hx-get="?page={{ workordermasters.next_page_number }}&{{ request.GET.urlencode }}" 
                   hx-target="#workordermasterTable-container" hx-swap="innerHTML" class="relative block py-2 px-3 leading-tight bg-white border border-gray-300 text-blue-700 hover:bg-gray-100 rounded-r-md">Next</a></li>
        {% endif %}
    </ul>
</nav>
{% endif %}

```

**`workorder_copy/templates/workorder_copy/workordermaster/_customer_autocomplete_results.html`**

```html
{% if customers %}
    <ul class="py-1">
        {% for customer in customers %}
            <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                hx-on:click="this.closest('#searchFormContent').querySelector('[name=search_text_customer_autocomplete]').value = '{{ customer }}'; 
                            htmx.trigger(this.closest('#searchForm'), 'submit'); 
                            this.closest('#customer-autocomplete-results').classList.add('hidden');">
                {{ customer }}
            </li>
        {% endfor %}
    </ul>
{% else %}
    <div class="px-4 py-2 text-gray-500">No results found</div>
{% endif %}
```

#### 4.5 URLs (`workorder_copy/urls.py`)

**Task:** Define URL patterns for the views, including the main list page, the HTMX table partial, and the autocomplete endpoint.

```python
from django.urls import path
from .views import WorkOrderCopyListView, WorkOrderCopyTablePartialView, CustomerAutoCompleteView

app_name = 'workorder_copy'

urlpatterns = [
    # Main page for root assembly copy from
    path('root-assembly-copy-wo/', WorkOrderCopyListView.as_view(), name='workordermaster_list'),
    
    # HTMX endpoint for the work order table partial
    path('root-assembly-copy-wo/table/', WorkOrderCopyTablePartialView.as_view(), name='workordermaster_table'),
    
    # HTMX/JSON endpoint for customer autocomplete
    path('customer-autocomplete/', CustomerAutoCompleteView.as_view(), name='customer_autocomplete'),

    # This URL is the target for the dynamic link on the WONo column.
    # It points to the next page in the workflow: TPL_Design_Root_Assembly_Copy_Grid.aspx
    # In Django, this would be another view, perhaps in the same or a different app.
    # We'll define a placeholder here for completeness, assuming its actual view exists.
    path('root-assembly-copy-grid/', TemplateView.as_view(template_name='workorder_copy/placeholder_copy_grid.html'), name='root_assembly_copy_grid'),
]

```
*(Note: You would typically include this `workorder_copy/urls.py` in your project's main `urls.py` like `path('workorder_copy/', include('workorder_copy.urls')),`)*

#### 4.6 Tests (`workorder_copy/tests.py`)

**Task:** Write comprehensive unit tests for models and integration tests for views to ensure functionality and data integrity.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import WorkOrderMaster, Customer, FinancialYear, Employee
from datetime import date

class WorkOrderCopyModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for related models
        cls.fin_year_2023 = FinancialYear.objects.create(finyearid=2023, finyear='2023-24')
        cls.fin_year_2022 = FinancialYear.objects.create(finyearid=2022, finyear='2022-23')
        cls.employee1 = Employee.objects.create(empid='EMP001', employeename='John Doe', title='Mr', compid=1, finyearid=2023)
        cls.employee2 = Employee.objects.create(empid='EMP002', employeename='Jane Smith', title='Ms', compid=1, finyearid=2023)
        cls.customer1 = Customer.objects.create(customerid='CUST001', customername='ABC Corp', compid=1, finyearid=2023)
        cls.customer2 = Customer.objects.create(customerid='CUST002', customername='XYZ Ltd', compid=1, finyearid=2023)

        # Create WorkOrderMaster test data
        cls.wo1 = WorkOrderMaster.objects.create(
            enqid='ENQ001', customer=cls.customer1, wono='WO001', pono='PO001',
            employee=cls.employee1, fin_year=cls.fin_year_2023, sysdate_str='01-15-2023', compid=1
        )
        cls.wo2 = WorkOrderMaster.objects.create(
            enqid='ENQ002', customer=cls.customer2, wono='WO002', pono='PO002',
            employee=cls.employee2, fin_year=cls.fin_year_2023, sysdate_str='12-25-2023', compid=1
        )
        cls.wo3 = WorkOrderMaster.objects.create(
            enqid='ENQ003', customer=cls.customer1, wono='WO003', pono='PO003',
            employee=cls.employee1, fin_year=cls.fin_year_2022, sysdate_str='07-01-2022', compid=1
        )

    def test_workordermaster_creation(self):
        self.assertEqual(WorkOrderMaster.objects.count(), 3)
        self.assertEqual(self.wo1.wono, 'WO001')
        self.assertEqual(self.wo1.customer.customername, 'ABC Corp')
        self.assertEqual(self.wo1.employee.employeename, 'John Doe')
        self.assertEqual(self.wo1.fin_year.finyear, '2023-24')

    def test_parsed_sysdate_property(self):
        self.assertEqual(self.wo1.parsed_sysdate, date(2023, 1, 15))
        self.assertEqual(self.wo2.parsed_sysdate, date(2023, 12, 25))
        
        # Test with DD-MM-YYYY format if applicable
        wo_dd_mm = WorkOrderMaster.objects.create(
            enqid='ENQ004', customer=self.customer1, wono='WO004', pono='PO004',
            employee=self.employee1, fin_year=self.fin_year_2023, sysdate_str='15-01-2024', compid=1
        )
        self.assertEqual(wo_dd_mm.parsed_sysdate, date(2024, 1, 15))

        # Test with invalid date string
        self.wo1.sysdate_str = 'invalid-date'
        self.assertIsNone(self.wo1.parsed_sysdate)

    def test_get_customer_id_from_display_method(self):
        self.assertEqual(WorkOrderMaster.get_customer_id_from_display("ABC Corp [CUST001]"), "CUST001")
        self.assertIsNone(WorkOrderMaster.get_customer_id_from_display("ABC Corp"))
        self.assertIsNone(WorkOrderMaster.get_customer_id_from_display("ABC Corp ["))
        self.assertIsNone(WorkOrderMaster.get_customer_id_from_display(None))
        self.assertIsNone(WorkOrderMaster.get_customer_id_from_display(""))


class WorkOrderCopyViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for views
        cls.fin_year_2023 = FinancialYear.objects.create(finyearid=2023, finyear='2023-24')
        cls.employee1 = Employee.objects.create(empid='EMP001', employeename='John Doe', title='Mr', compid=1, finyearid=2023)
        cls.customer1 = Customer.objects.create(customerid='CUST001', customername='ABC Corp', compid=1, finyearid=2023)
        WorkOrderMaster.objects.create(
            enqid='ENQ001', customer=cls.customer1, wono='WO001', pono='PO001',
            employee=cls.employee1, fin_year=cls.fin_year_2023, sysdate_str='01-15-2023', compid=1
        )
        WorkOrderMaster.objects.create(
            enqid='ENQ002', customer=cls.customer1, wono='WO002', pono='PO002',
            employee=cls.employee1, fin_year=cls.fin_year_2023, sysdate_str='01-16-2023', compid=1
        )
        WorkOrderMaster.objects.create(
            enqid='ENQ003', customer=cls.customer1, wono='WO003', pono='PO003',
            employee=cls.employee1, fin_year=cls.fin_year_2023, sysdate_str='01-17-2023', compid=2 # Different company
        )


    def setUp(self):
        self.client = Client()
        self.list_url = reverse('workorder_copy:workordermaster_list')
        self.table_url = reverse('workorder_copy:workordermaster_table')
        self.autocomplete_url = reverse('workorder_copy:customer_autocomplete')

    def test_list_view_get(self):
        # Initial page load
        response = self.client.get(self.list_url + '?WONoDest=DST001&comp_id=1&fin_year_id=2023')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorder_copy/workordermaster/list.html')
        self.assertContains(response, 'TPL Root Assembly Copy From')
        self.assertContains(response, 'id="workordermasterTable-container"') # Container for HTMX load

    def test_table_partial_view_initial_load(self):
        # HTMX request to load the table content
        response = self.client.get(self.table_url + '?WONoDest=DST001&comp_id=1&fin_year_id=2023', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorder_copy/workordermaster/_workorder_table.html')
        self.assertContains(response, 'WO001')
        self.assertContains(response, 'WO002')
        self.assertNotContains(response, 'WO003') # Should be filtered by comp_id

    def test_table_partial_view_search_by_wono(self):
        # Search by WO No
        response = self.client.get(self.table_url + '?search_by=wo_no&search_text_general=WO001&comp_id=1&fin_year_id=2023', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO001')
        self.assertNotContains(response, 'WO002')

    def test_table_partial_view_search_by_enquiry_no(self):
        # Search by Enquiry No
        response = self.client.get(self.table_url + '?search_by=enquiry_no&search_text_general=ENQ002&comp_id=1&fin_year_id=2023', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO002')
        self.assertNotContains(response, 'WO001')

    def test_table_partial_view_search_by_customer_name(self):
        # Search by Customer Name using the autocomplete display format
        response = self.client.get(self.table_url + '?search_by=customer_name&search_text_customer_autocomplete=ABC Corp [CUST001]&comp_id=1&fin_year_id=2023', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO001')
        self.assertContains(response, 'WO002') # Both are for CUST001
        
        # Test with name directly if ID parsing fails or is incomplete
        response = self.client.get(self.table_url + '?search_by=customer_name&search_text_customer_autocomplete=ABC Corp&comp_id=1&fin_year_id=2023', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO001')
        self.assertContains(response, 'WO002')


    def test_table_partial_view_no_data(self):
        # Search with no matching data
        response = self.client.get(self.table_url + '?search_by=wo_no&search_text_general=NONEXISTENT&comp_id=1&fin_year_id=2023', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No data to display !')

    def test_customer_autocomplete_view(self):
        response = self.client.get(self.autocomplete_url + '?search_text_customer_autocomplete=abc&comp_id=1', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'ABC Corp [CUST001]')
        self.assertNotContains(response, 'XYZ Ltd [CUST002]') # Should not match 'abc'

        response = self.client.get(self.autocomplete_url + '?search_text_customer_autocomplete=xyz&comp_id=1', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'XYZ Ltd [CUST002]')
        self.assertNotContains(response, 'ABC Corp [CUST001]')

        response = self.client.get(self.autocomplete_url + '?search_text_customer_autocomplete=&comp_id=1', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertNotContains(response, 'ABC Corp [CUST001]') # No results if prefix is empty

    def test_customer_autocomplete_view_no_results(self):
        response = self.client.get(self.autocomplete_url + '?search_text_customer_autocomplete=nonexistentcustomer&comp_id=1', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No results found')

    def test_form_validation(self):
        # Test initial form with select (should be valid)
        form_data = {'search_by': 'select', 'comp_id': '1', 'fin_year_id': '2023'}
        form = WorkOrderCopySearchForm(form_data)
        self.assertTrue(form.is_valid())

        # Test search by WO No without search text (should be invalid)
        form_data = {'search_by': 'wo_no', 'search_text_general': '', 'comp_id': '1', 'fin_year_id': '2023'}
        form = WorkOrderCopySearchForm(form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('search_text_general', form.errors)

        # Test search by Customer Name without autocomplete text (should be invalid)
        form_data = {'search_by': 'customer_name', 'search_text_customer_autocomplete': '', 'comp_id': '1', 'fin_year_id': '2023'}
        form = WorkOrderCopySearchForm(form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('search_text_customer_autocomplete', form.errors)
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
-   **HTMX for Search & Table Refresh:**
    -   The main search form (`id="searchForm"`) uses `hx-get="{% url 'workorder_copy:workordermaster_table' %}"` and `hx-target="#workordermasterTable-container"` with `hx-trigger="submit, refreshWorkOrderList from:body"`. This means when the form is submitted, or a custom event `refreshWorkOrderList` is triggered, HTMX will fetch the updated table.
    -   The `search_by` dropdown also triggers an `hx-get` to `?` (the current URL) with `hx-target="#searchFormContent"` to swap out the search form, allowing Alpine.js to react to the updated form with the correct text input visibility.
-   **HTMX for Autocomplete:**
    -   The `search_text_customer_autocomplete` input has `hx-get="/workorder_copy/customer-autocomplete/"`, `hx-trigger="keyup changed delay:500ms, search"`, and `hx-target="#customer-autocomplete-results"`. This fetches autocomplete suggestions as the user types.
    -   The autocomplete suggestions are rendered in a `_customer_autocomplete_results.html` partial, which uses an `hx-on:click` attribute to populate the input and hide the results.
-   **Alpine.js for UI State:**
    -   The `x-data="{ searchByType: '{{ initial_search_by }}' }"` is initialized on the main container.
    -   `x-show` directives are used on `search_text_general` and `search_text_customer_autocomplete` inputs to control their visibility based on the `searchByType` Alpine.js variable, ensuring only the relevant input is shown.
-   **DataTables for List Views:**
    -   The `_workorder_table.html` partial includes a `<table>` with `id="workordermasterTable"`.
    -   A JavaScript snippet within the `{% block extra_js %}` block listens for `htmx:afterSwap` event specifically on the `#workordermasterTable-container`. Once the table partial is loaded into the container, DataTables.js is initialized on the `workordermasterTable`, providing client-side search, sort, and pagination.

## Final Notes

This comprehensive plan provides a clear roadmap for migrating your ASP.NET `TPL_Design_Root_Assembly_Copy_WO` module to a modern Django solution. By adhering to the principles of "fat model, thin view," leveraging HTMX and Alpine.js for dynamic interfaces, and ensuring robust testing, you will achieve a scalable, maintainable, and highly responsive application. The use of automation-friendly instructions ensures that this transition can be guided effectively by conversational AI tools, minimizing manual development effort and potential errors.

The next steps would involve implementing the target `TPL_Design_Root_Assembly_Copy_Grid` page in Django, which would likely involve another Work Order-related model and a view to handle the copying logic. This modular approach ensures that each component of your ERP system can be modernized incrementally and efficiently.