This document outlines a comprehensive plan to modernize the provided ASP.NET BOM (Bill of Materials) tree copy functionality to a robust, scalable Django application. Our approach leverages AI-assisted automation, adhering to modern Django 5.0+ practices, emphasizing fat models, thin views, and a responsive frontend powered by HTMX, Alpine.js, and DataTables.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

The ASP.NET code interacts with multiple database tables. The primary table for the BOM structure is `tblDG_BOM_Master`, with auxiliary data fetched from `tblDG_Item_Master` and `Unit_Master`.

**Primary Table:** `tblDG_BOM_Master`

*   **Columns:**
    *   `CId` (int): Child ID, acts as a unique identifier for a BOM component.
    *   `PId` (int): Parent ID, links components to their parent in the BOM hierarchy.
    *   `ItemId` (int): Foreign key to `tblDG_Item_Master`.
    *   `WONo` (string): Work Order Number, grouping BOM entries.
    *   `Qty` (decimal): Quantity of the item in the BOM.
    *   `CompId` (int): Company ID (inferred from `Session["compid"]`).
    *   `FinYearId` (int): Financial Year ID (inferred from `Session["finyear"]`).

**Auxiliary Tables:**

*   `tblDG_Item_Master`
    *   `Id` (int): Primary key.
    *   `ManfDesc` (string): Manufacturer Description.
    *   `UOMBasic` (int): Foreign key to `Unit_Master` for Unit of Measure.
    *   `ItemCode_PartNo` (string): Derived/Looked up field, likely a unique identifier for the item (inferred from `fun.GetItemCode_PartNo`).
    *   `CompId` (int): Company ID.
    *   `FinYearId` (int): Financial Year ID.
*   `Unit_Master`
    *   `Id` (int): Primary key.
    *   `Symbol` (string): Unit of Measure symbol.

### Step 2: Identify Backend Functionality

The ASP.NET code primarily performs two main functions:

1.  **Read (BOM Data Retrieval):** Displays a hierarchical Bill of Materials based on a source Work Order Number (`WONoSrc`). This involves:
    *   Fetching core BOM data (`ItemId`, `WONo`, `PId`, `CId`, `Qty`) from `tblDG_BOM_Master`.
    *   Enriching this data by looking up `Item Code` (from `tblDG_Item_Master` via `fun.GetItemCode_PartNo`), `Description` (from `tblDG_Item_Master.ManfDesc`), and `UOM` (from `Unit_Master.Symbol`).
    *   Calculating a derived `BOM Qty` for each item, which involves traversing the BOM tree using the `fun.BOMTreeQty` function. This is a crucial piece of business logic.

2.  **Copy (BOM Node Copying):** Copies selected BOM components from the source Work Order (`WONoSrc`) to a destination Work Order (`WONoDest`) with specified parent and child IDs. This is triggered by `btnCopy_Click` and implemented by `fun.getBOMnode`.
    *   It checks for a selection (`RadTreeList1.SelectedItems[0]`).
    *   Includes a validation step to prevent copying an item to itself (`cid == SrcCid`).
    *   Redirects to another page (`BOM_Design_CopyWo.aspx`) after completion or error, passing status messages.

### Step 3: Infer UI Components

The ASP.NET page uses Telerik `RadTreeList` for data display, standard ASP.NET `Label`, `CheckBox`, and `Button` controls.

*   **Display Data:** `RadTreeList` will be replaced by a standard HTML `<table>` enhanced with DataTables.
    *   Columns to display: "Item Code", "Description", "UOM", "Unit Qty", "BOM Qty".
    *   Selection: `TreeListSelectColumn` will be replaced by standard checkboxes in the DataTables rows.
*   **Input/Display:**
    *   `Label2` (displaying `WONoSrc`) will be a simple Django template variable.
    *   `CheckBox1` (Expand Tree) functionality, which applies to `RadTreeList`'s hierarchical display, will be removed as we are targeting a flat DataTables representation as per the guidelines. If a true tree structure is later required, a DataTables tree extension or a separate JS tree library would be considered.
*   **Actions:**
    *   `btnCopy`: Will be an HTMX-powered button to trigger the copy operation. It requires passing selected `CId`s. The `confirmationCopy()` JavaScript will be replaced by a simple `confirm()` or an Alpine.js modal-based confirmation.
    *   `BtnCancel`: Will be a standard link or HTMX button to navigate back.
*   **Dynamic Updates:** `RadAjaxPanel` and `RadAjaxLoadingPanel` indicate partial page updates and loading indicators, perfectly aligning with HTMX's capabilities.

### Step 4: Generate Django Code

We will create a Django application named `bom_design`.

#### 4.1 Models (`bom_design/models.py`)

We'll define models for `BOMMaster`, `ItemMaster`, and `UnitMaster`, ensuring they map to the existing database tables using `managed = False`. The complex business logic for data retrieval and BOM quantity calculation will be encapsulated as class methods within `BOMMaster`.

```python
from django.db import models, connection
from decimal import Decimal

# Assuming these are context variables from the session/user
# In a real application, these would be passed from the request/session
DEFAULT_COMPANY_ID = 1
DEFAULT_FINANCIAL_YEAR_ID = 1

class UnitMaster(models.Model):
    """
    Maps to the Unit_Master table for Unit of Measure details.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit Master'
        verbose_name_plural = 'Unit Masters'

    def __str__(self):
        return self.symbol

class ItemMaster(models.Model):
    """
    Maps to the tblDG_Item_Master table for Item details.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    manufacture_description = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True)
    uom_basic = models.ForeignKey(UnitMaster, models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)
    company_id = models.IntegerField(db_column='CompId')
    financial_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.manufacture_description or f"Item {self.id}"

    @classmethod
    def get_item_code_part_no(cls, comp_id, item_id):
        """
        Replicates fun.GetItemCode_PartNo logic.
        This would typically be a database lookup or a computed property.
        For demonstration, returning a placeholder.
        """
        # Placeholder for actual logic to retrieve item code/part number
        # Example: return cls.objects.filter(id=item_id, company_id=comp_id).values_list('ItemCode', flat=True).first()
        return f"ITEM-{item_id:04d}" # Example placeholder

class BOMMaster(models.Model):
    """
    Maps to the tblDG_BOM_Master table for Bill of Materials entries.
    This model encapsulates the business logic for BOM operations.
    """
    item_id = models.IntegerField(db_column='ItemId')
    work_order_no = models.CharField(db_column='WONo', max_length=50)
    parent_id = models.IntegerField(db_column='PId')
    child_id = models.IntegerField(db_column='CId', primary_key=True) # CId seems to be the primary key/unique identifier for a BOM node
    quantity = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3)
    company_id = models.IntegerField(db_column='CompId', default=DEFAULT_COMPANY_ID)
    financial_year_id = models.IntegerField(db_column='FinYearId', default=DEFAULT_FINANCIAL_YEAR_ID)

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'BOM Master'
        verbose_name_plural = 'BOM Masters'

    def __str__(self):
        return f"BOM: {self.work_order_no} - CId:{self.child_id} PId:{self.parent_id}"

    @classmethod
    def get_bom_data_for_display(cls, wonosrc, comp_id, fin_year_id):
        """
        Replicates the complex data retrieval logic from GetDataTable() in the C# code.
        This includes fetching BOM entries, item details, UOM, and calculating BOM Qty.
        Returns a list of dictionaries, suitable for display in DataTables.
        """
        bom_data = cls.objects.filter(
            work_order_no=wonosrc,
            company_id=comp_id,
            financial_year_id__lte=fin_year_id
        ).order_by('parent_id').values(
            'item_id', 'work_order_no', 'parent_id', 'child_id', 'quantity'
        )

        display_data = []
        for bom_entry in bom_data:
            item_id = bom_entry['item_id']
            item_code = ItemMaster.get_item_code_part_no(comp_id, item_id) # Call the ItemMaster method
            item_desc = ''
            item_uom = ''
            
            try:
                # Fetch Item details
                item_obj = ItemMaster.objects.get(
                    id=item_id,
                    company_id=comp_id,
                    financial_year_id__lte=fin_year_id
                )
                item_desc = item_obj.manufacture_description or ''
                
                # Fetch UOM details
                if item_obj.uom_basic:
                    item_uom = item_obj.uom_basic.symbol or ''
            except ItemMaster.DoesNotExist:
                # Handle cases where item might not exist (e.g., data inconsistency)
                pass 
            except UnitMaster.DoesNotExist:
                # Handle cases where UOM might not exist
                pass

            # Calculate BOM Qty
            bom_qty_val = cls._calculate_bom_tree_qty_recursive(
                wonosrc,
                bom_entry['parent_id'],
                bom_entry['child_id'],
                comp_id,
                fin_year_id
            )
            
            display_data.append({
                'CId': bom_entry['child_id'],
                'PId': bom_entry['parent_id'],
                'ItemId': item_id,
                'WONo': bom_entry['work_order_no'],
                'Item Code': item_code,
                'Description': item_desc,
                'UOM': item_uom,
                'Unit Qty': bom_entry['quantity'].quantize(Decimal('0.001')), # Format to 3 decimal places
                'BOM Qty': Decimal(bom_qty_val).quantize(Decimal('0.001')) # Format to 3 decimal places
            })
        return display_data

    @classmethod
    def _calculate_bom_tree_qty_recursive(cls, wono, parent_id, child_id, comp_id, fin_year_id):
        """
        Helper method to recursively calculate BOM Qty (replicates fun.BOMTreeQty).
        This logic is complex and would require a proper understanding of the
        'fun.BOMTreeQty' implementation. This is a placeholder for that logic.
        Assumes it sums up quantities along the path from root to child.
        """
        # In the original C# code, fun.BOMTreeQty likely traverses the BOM tree
        # to calculate the cumulative quantity for a given child node relative to its parent.
        # This is a simplified representation. A true implementation would query
        # parent-child relationships iteratively or recursively.

        qty_list = []
        current_child_id = child_id
        current_parent_id = parent_id

        # Loop to traverse up the tree to gather quantities
        while current_child_id is not None:
            try:
                # Get the quantity of the current child relative to its immediate parent
                # If current_child_id is a root (PId=0), its Qty is itself.
                # Assuming BOM_Master has a unique (PId, CId) pair for the current WO.
                bom_node = cls.objects.get(
                    work_order_no=wono,
                    child_id=current_child_id,
                    company_id=comp_id,
                    financial_year_id__lte=fin_year_id
                )
                qty_list.append(float(bom_node.quantity))
                
                # If the current node is a root (PId is 0 or no parent BOM entry for it), stop
                if bom_node.parent_id == 0: # Assuming 0 indicates a top-level assembly
                    current_child_id = None
                else:
                    # Find the parent node in the BOM for the next iteration
                    try:
                        parent_bom_node = cls.objects.get(
                            work_order_no=wono,
                            child_id=bom_node.parent_id,
                            company_id=comp_id,
                            financial_year_id__lte=fin_year_id
                        )
                        current_child_id = parent_bom_node.child_id # Move up to parent
                        current_parent_id = parent_bom_node.parent_id
                    except cls.DoesNotExist:
                        # Parent not found in BOM, stop recursion
                        current_child_id = None 

            except cls.DoesNotExist:
                # Node not found, stop recursion
                current_child_id = None

        # The original fun.BOMTreeQty seemed to multiply quantities.
        # If the list is empty (e.g., node not found), return 0 or 1 depending on desired base.
        if not qty_list:
            return 0.0 # Or 1.0 if it's a default for a non-existent path. Original has 1.0 initial.
        
        # Multiply all quantities found
        cumulative_qty = 1.0
        for q in qty_list:
            cumulative_qty *= q
        return cumulative_qty


    @classmethod
    def copy_bom_node(cls, src_child_id, wonosrc, wonodest, comp_id, session_id, fin_year_id, dest_pid, dest_cid):
        """
        Replicates the fun.getBOMnode logic for copying BOM components.
        This function recursively copies a BOM node and its children.
        """
        try:
            # Fetch the source BOM node
            source_node = cls.objects.get(
                child_id=src_child_id,
                work_order_no=wonosrc,
                company_id=comp_id,
                financial_year_id=fin_year_id
            )

            # Determine the new parent ID for the copied node
            # If copying to the top level (dest_pid=0, dest_cid=0 implying new tree branch)
            # Or if copying as a child of an existing dest_cid
            new_parent_id = dest_cid # The copied node will be a child of dest_cid

            # Generate a new unique CId for the copied node
            # This is crucial. Assuming a simple auto-increment or similar logic for CId.
            # In a real system, this might involve a sequence or a more robust ID generation.
            max_child_id = cls.objects.aggregate(models.Max('child_id'))['child_id__max'] or 0
            new_child_id = max_child_id + 1

            # Create a new BOMMaster instance for the copied node
            new_node = cls(
                item_id=source_node.item_id,
                work_order_no=wonodest,
                parent_id=new_parent_id,
                child_id=new_child_id,
                quantity=source_node.quantity,
                company_id=comp_id,
                financial_year_id=fin_year_id
            )
            new_node.save()

            # Recursively copy children of the source node
            children = cls.objects.filter(
                parent_id=src_child_id,
                work_order_no=wonosrc,
                company_id=comp_id,
                financial_year_id=fin_year_id
            )
            for child in children:
                # Call this method recursively for each child
                # The 'dest_cid' for the children will be the 'new_child_id' of the current node
                cls.copy_bom_node(
                    child.child_id,
                    wonosrc,
                    wonodest,
                    comp_id,
                    session_id, # session_id (username) probably used for auditing in the original function
                    fin_year_id,
                    new_parent_id, # Parent of children is the new_child_id
                    new_child_id # Current new_child_id becomes the parent for its children
                )
            
            return True, "BOM node and its children copied successfully."

        except cls.DoesNotExist:
            return False, "Source BOM node not found."
        except Exception as e:
            # Log the exception in a real application
            return False, f"An error occurred during copy: {str(e)}"

```

#### 4.2 Forms (`bom_design/forms.py`)

For this specific "copy tree" functionality, we don't need a traditional Django `ModelForm` for CRUD operations on `BOMMaster` instances. The primary interaction is selecting existing items and performing a custom copy action. User input parameters (`WONoSrc`, `WONoDest`, `DestPId`, `DestCId`) are derived from URL query strings or session, not a form.

If there were additional fields for the copy operation (e.g., a new name for the copied assembly), we'd define a `forms.Form` here. For the current scope, no `forms.py` is explicitly required as the selected items are passed via HTMX post data.

#### 4.3 Views (`bom_design/views.py`)

We'll use a `TemplateView` for displaying the initial page and a `View` (or a mixin for custom logic) for handling the HTMX-driven copy action. The data display will be handled by a separate HTMX endpoint.

```python
from django.views.generic import TemplateView, View
from django.http import HttpResponse, JsonResponse
from django.urls import reverse
from django.contrib import messages
from django.shortcuts import redirect
from .models import BOMMaster, DEFAULT_COMPANY_ID, DEFAULT_FINANCIAL_YEAR_ID # Import defaults

# Helper to get session/context variables, replace with your actual session management
def get_user_context(request):
    """
    Placeholder for retrieving session context variables.
    In a real app, this would get actual comp_id, fin_year_id, username from request.session.
    """
    comp_id = request.session.get('compid', DEFAULT_COMPANY_ID)
    fin_year_id = request.session.get('finyear', DEFAULT_FINANCIAL_YEAR_ID)
    session_id = request.session.get('username', 'anonymous')
    return comp_id, fin_year_id, session_id

class BOMTreeCopyPageView(TemplateView):
    """
    Displays the initial BOM tree copy page.
    This corresponds to the ASP.NET page's Page_Load GET logic.
    """
    template_name = 'bom_design/copy_tree.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        wonosrc = self.request.GET.get('WONoSrc', '')
        wonodest = self.request.GET.get('WONoDest', '')
        dest_pid = self.request.GET.get('DestPId', '0')
        dest_cid = self.request.GET.get('DestCId', '0')

        context['wonosrc'] = wonosrc
        context['wonodest'] = wonodest
        context['dest_pid'] = int(dest_pid)
        context['dest_cid'] = int(dest_cid)
        context['page_title'] = 'BOM Design Copy Tree'
        
        # Initial data loading is handled by HTMX on the client side for the table.
        return context

class BOMTreeListPartialView(View):
    """
    Returns the partial HTML for the BOM tree list table,
    designed to be loaded via HTMX.
    """
    def get(self, request, *args, **kwargs):
        wonosrc = request.GET.get('wonosrc', '')
        comp_id, fin_year_id, _ = get_user_context(request)

        # Retrieve and process data using the fat model
        bom_data = BOMMaster.get_bom_data_for_display(wonosrc, comp_id, fin_year_id)
        
        # Render the partial template
        return render(request, 'bom_design/_bom_tree_table.html', {
            'bom_items': bom_data,
            'wonosrc': wonosrc, # Pass wonosrc for context in partial
        })

class BOMTreeCopyActionView(View):
    """
    Handles the POST request for copying selected BOM nodes.
    Corresponds to btnCopy_Click in the C# code.
    """
    def post(self, request, *args, **kwargs):
        src_child_ids = request.POST.getlist('selected_cids[]') # Get list of selected CIds
        
        wonosrc = request.POST.get('wonosrc', '')
        wonodest = request.POST.get('wonodest', '')
        dest_pid = int(request.POST.get('dest_pid', '0'))
        dest_cid = int(request.POST.get('dest_cid', '0'))

        comp_id, fin_year_id, session_id = get_user_context(request)

        if not src_child_ids:
            messages.error(request, "No BOM items selected for copying.")
            # HTMX triggers reload or keeps modal open with message
            return HttpResponse(status=200, headers={'HX-Trigger': 'refreshBOMTreeList'}) # Or a specific message trigger

        try:
            # Assuming only one item can be selected for copy as per RadTreeList1.SelectedItems[0]
            # If multiple selections are allowed, loop through src_child_ids
            src_cid = int(src_child_ids[0]) # Get the first selected CId

            # Replicate the validation logic
            if dest_cid == src_cid:
                messages.error(request, "Selection of Assembly/Item is incorrect. Cannot copy an item to itself.")
                return HttpResponse(status=200, headers={'HX-Trigger': 'refreshBOMTreeList'})

            # Call the fat model's business logic
            success, msg = BOMMaster.copy_bom_node(
                src_cid, wonosrc, wonodest, comp_id, session_id, fin_year_id, dest_pid, dest_cid
            )

            if success:
                messages.success(request, f"BOM is Copied successfully in WONo: {wonodest} from WONo: {wonosrc}.")
            else:
                messages.error(request, msg)

            # Replicate the redirect behavior to BOM_Design_CopyWo.aspx
            # In Django, this means redirecting to another view, passing parameters.
            # Assuming a Django view for BOM_Design_CopyWo exists.
            redirect_url = reverse('bom_design:copy_workorder_success') + \
                           f"?WONoSrc={wonosrc}&WONoDest={wonodest}&DestPId={dest_pid}&DestCId={dest_cid}" + \
                           f"&msg={messages.get_messages(request)._loaded_messages[0].message}" # Pass the last message
            
            # HTMX way: Respond with a redirect header
            return HttpResponse(status=204, headers={'HX-Redirect': redirect_url})

        except Exception as e:
            messages.error(request, f"An unexpected error occurred: {str(e)}")
            return HttpResponse(status=200, headers={'HX-Trigger': 'refreshBOMTreeList'})

class BOMCopyWorkorderSuccessView(TemplateView):
    """
    Placeholder for the success/redirect target view.
    Corresponds to BOM_Design_CopyWo.aspx.
    """
    template_name = 'bom_design/copy_workorder_success.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['wonosrc'] = self.request.GET.get('WONoSrc', '')
        context['wonodest'] = self.request.GET.get('WONoDest', '')
        context['dest_pid'] = self.request.GET.get('DestPId', '0')
        context['dest_cid'] = self.request.GET.get('DestCId', '0')
        context['message'] = self.request.GET.get('msg', 'Operation completed.')
        context['page_title'] = 'BOM Copy Result'
        return context

class BOMCancelActionView(View):
    """
    Handles the cancel button logic, redirecting to the copy workorder view.
    Corresponds to BtnCancel_Click.
    """
    def get(self, request, *args, **kwargs):
        wonosrc = request.GET.get('wonosrc', '')
        wonodest = request.GET.get('wonodest', '')
        dest_pid = request.GET.get('dest_pid', '0')
        dest_cid = request.GET.get('dest_cid', '0')

        redirect_url = reverse('bom_design:copy_workorder_success') + \
                       f"?WONoSrc={wonosrc}&WONoDest={wonodest}&DestPId={dest_pid}&DestCId={dest_cid}"
        
        # HTMX way: Respond with a redirect header
        return HttpResponse(status=204, headers={'HX-Redirect': redirect_url})

```

#### 4.4 Templates (`bom_design/templates/bom_design/`)

We'll create the main page template and a partial for the DataTables content.

**`copy_tree.html` (Main Page Template)**
This template will serve as the initial page, loading the actual BOM table content dynamically via HTMX.

```html
{% extends 'core/base.html' %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">BOM Design Copy Tree</h2>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <p class="text-lg font-medium text-gray-700">
            &nbsp;<b>Work Order (Source): </b>
            <span class="text-blue-600">{{ wonosrc }}</span>
        </p>
        <div class="flex items-center space-x-4 mt-4">
            <button
                id="btnCopy"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm"
                hx-post="{% url 'bom_design:copy_action' %}"
                hx-include="#bomTreeTable input[type='checkbox']:checked, [name='wonosrc'], [name='wonodest'], [name='dest_pid'], [name='dest_cid']"
                hx-confirm="Are you sure you want to copy the selected BOM item(s)?"
                hx-swap="none"
                hx-indicator="#loadingIndicator"
                onclick="return confirm('Are you sure you want to copy the selected BOM item(s)?')">
                Copy
            </button>
            <button
                id="btnCancel"
                class="bg-gray-400 hover:bg-gray-500 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm"
                hx-get="{% url 'bom_design:cancel_action' %}?wonosrc={{ wonosrc }}&wonodest={{ wonodest }}&dest_pid={{ dest_pid }}&dest_cid={{ dest_cid }}"
                hx-swap="none"
                hx-indicator="#loadingIndicator">
                Cancel
            </button>
            <span id="loadingIndicator" class="htmx-indicator ml-4 text-gray-600">
                <div class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
                Loading...
            </span>
        </div>
        
        <!-- Hidden inputs to pass source/destination WO and IDs to POST requests -->
        <input type="hidden" name="wonosrc" value="{{ wonosrc }}">
        <input type="hidden" name="wonodest" value="{{ wonodest }}">
        <input type="hidden" name="dest_pid" value="{{ dest_pid }}">
        <input type="hidden" name="dest_cid" value="{{ dest_cid }}">
    </div>

    <div id="bomTreeTable-container"
         hx-trigger="load, refreshBOMTreeList from:body"
         hx-get="{% url 'bom_design:bom_tree_table' %}?wonosrc={{ wonosrc }}"
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg p-6">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading BOM Data...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Add any Alpine.js components if needed for client-side state -->
<script>
    // Example Alpine.js for a more complex modal or UI state
    document.addEventListener('alpine:init', () => {
        Alpine.data('copyModal', () => ({
            isOpen: false,
            message: '',
            open(msg) {
                this.message = msg;
                this.isOpen = true;
            },
            close() {
                this.isOpen = false;
            }
        }));
    });
</script>
{% endblock %}
```

**`_bom_tree_table.html` (Partial for DataTables)**
This template will contain the actual HTML table structure and the DataTables initialization script.

```html
<table id="bomTreeTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"></th> {# Checkbox column #}
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider text-right">Unit Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider text-right">BOM Qty</th>
        </tr>
    </thead>
    <tbody>
        {% for item in bom_items %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                <input type="checkbox" name="selected_cids[]" value="{{ item.CId }}" class="form-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500">
            </td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.'Item Code' }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.Description }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.UOM }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.'Unit Qty' }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.'BOM Qty' }}</td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-4 text-center text-gray-500">No BOM data found for {{ wonosrc }}.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Ensure DataTables is initialized only once per table load
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#bomTreeTable')) {
            $('#bomTreeTable').DataTable().destroy(); // Destroy existing instance if any
        }
        $('#bomTreeTable').DataTable({
            "pageLength": 17, // Matches original PageSize
            "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
            "responsive": true,
            "order": [], // Disable initial sorting if tree order is crucial, or order by PId/CId
            "columnDefs": [
                { "orderable": false, "targets": [0] } // Disable sorting for checkbox column
            ]
        });
    });
</script>
```

**`copy_workorder_success.html` (Redirect Target/Success Page)**
This is a simple placeholder page that will be rendered after a successful copy operation or cancellation, mimicking the `Response.Redirect` behavior.

```html
{% extends 'core/base.html' %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6">
        <h2 class="text-2xl font-bold mb-4">BOM Copy Operation Result</h2>
        <p class="text-lg text-gray-700 mb-4">{{ message }}</p>
        <p class="text-md text-gray-600">
            Source Work Order: <span class="font-semibold">{{ wonosrc }}</span><br>
            Destination Work Order: <span class="font-semibold">{{ wonodest }}</span>
        </p>
        <div class="mt-6">
            <a href="{% url 'bom_design:copy_tree_page' %}?WONoSrc={{ wonosrc }}&WONoDest={{ wonodest }}&DestPId={{ dest_pid }}&DestCId={{ dest_cid }}"
               class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                Go Back
            </a>
        </div>
    </div>
</div>
{% endblock %}
```

#### 4.5 URLs (`bom_design/urls.py`)

Define the URL patterns to map to the views.

```python
from django.urls import path
from .views import (
    BOMTreeCopyPageView,
    BOMTreeListPartialView,
    BOMTreeCopyActionView,
    BOMCopyWorkorderSuccessView,
    BOMCancelActionView,
)

app_name = 'bom_design' # Namespace for this app's URLs

urlpatterns = [
    # Main page for copying BOM tree
    path('copy-tree/', BOMTreeCopyPageView.as_view(), name='copy_tree_page'),
    
    # HTMX endpoint to load the BOM tree table content
    path('copy-tree/table/', BOMTreeListPartialView.as_view(), name='bom_tree_table'),
    
    # HTMX endpoint for the copy action
    path('copy-tree/copy-action/', BOMTreeCopyActionView.as_view(), name='copy_action'),
    
    # HTMX endpoint for the cancel action (redirects)
    path('copy-tree/cancel-action/', BOMCancelActionView.as_view(), name='cancel_action'),

    # Target page after successful copy or cancel (mimicking original redirect)
    path('copy-workorder-success/', BOMCopyWorkorderSuccessView.as_view(), name='copy_workorder_success'),
]
```

#### 4.6 Tests (`bom_design/tests.py`)

Comprehensive tests for models and views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from decimal import Decimal
from .models import BOMMaster, ItemMaster, UnitMaster, DEFAULT_COMPANY_ID, DEFAULT_FINANCIAL_YEAR_ID

# Mock the database connection for controlled testing of model methods
@patch('bom_design.models.connection', new_callable=MagicMock)
class ModelTestBase(TestCase):
    """Base class for model tests with mocked database connection."""
    def setUp(self):
        super().setUp()
        self.mock_connection = self.connection_mock
        # Ensure ItemMaster and UnitMaster objects can be created for tests
        UnitMaster.objects.create(id=1, symbol='PCS')
        ItemMaster.objects.create(id=101, manufacture_description='Assembly A', uom_basic_id=1, company_id=DEFAULT_COMPANY_ID, financial_year_id=DEFAULT_FINANCIAL_YEAR_ID)
        ItemMaster.objects.create(id=102, manufacture_description='Component B', uom_basic_id=1, company_id=DEFAULT_COMPANY_ID, financial_year_id=DEFAULT_FINANCIAL_YEAR_ID)
        ItemMaster.objects.create(id=103, manufacture_description='Part C', uom_basic_id=1, company_id=DEFAULT_COMPANY_ID, financial_year_id=DEFAULT_FINANCIAL_YEAR_ID)


class BOMMasterModelTest(ModelTestBase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        super().setUpTestData() # Call base class setUpTestData

        BOMMaster.objects.create(
            child_id=1, parent_id=0, item_id=101, work_order_no='WO-SRC-001',
            quantity=Decimal('1.000'), company_id=DEFAULT_COMPANY_ID, financial_year_id=DEFAULT_FINANCIAL_YEAR_ID
        )
        BOMMaster.objects.create(
            child_id=2, parent_id=1, item_id=102, work_order_no='WO-SRC-001',
            quantity=Decimal('2.000'), company_id=DEFAULT_COMPANY_ID, financial_year_id=DEFAULT_FINANCIAL_YEAR_ID
        )
        BOMMaster.objects.create(
            child_id=3, parent_id=2, item_id=103, work_order_no='WO-SRC-001',
            quantity=Decimal('3.000'), company_id=DEFAULT_COMPANY_ID, financial_year_id=DEFAULT_FINANCIAL_YEAR_ID
        )
        BOMMaster.objects.create(
            child_id=4, parent_id=0, item_id=102, work_order_no='WO-SRC-002',
            quantity=Decimal('1.000'), company_id=DEFAULT_COMPANY_ID, financial_year_id=DEFAULT_FINANCIAL_YEAR_ID
        )
        
    def test_bom_master_creation(self):
        obj = BOMMaster.objects.get(child_id=1)
        self.assertEqual(obj.work_order_no, 'WO-SRC-001')
        self.assertEqual(obj.quantity, Decimal('1.000'))
        self.assertEqual(obj.parent_id, 0)
        self.assertEqual(obj.item_id, 101)

    def test_get_bom_data_for_display(self):
        bom_data = BOMMaster.get_bom_data_for_display('WO-SRC-001', DEFAULT_COMPANY_ID, DEFAULT_FINANCIAL_YEAR_ID)
        self.assertEqual(len(bom_data), 3)

        item1 = next(item for item in bom_data if item['CId'] == 1)
        self.assertEqual(item1['Item Code'], 'ITEM-0101')
        self.assertEqual(item1['Description'], 'Assembly A')
        self.assertEqual(item1['UOM'], 'PCS')
        self.assertEqual(item1['Unit Qty'], Decimal('1.000'))
        self.assertAlmostEqual(item1['BOM Qty'], Decimal('1.000')) # Self quantity

        item2 = next(item for item in bom_data if item['CId'] == 2)
        self.assertEqual(item2['Item Code'], 'ITEM-0102')
        self.assertEqual(item2['Description'], 'Component B')
        self.assertEqual(item2['UOM'], 'PCS')
        self.assertEqual(item2['Unit Qty'], Decimal('2.000'))
        self.assertAlmostEqual(item2['BOM Qty'], Decimal('2.000')) # Relative to parent 1, Qty is 2.000 * 1.000 (parent 1 qty) = 2.000

        item3 = next(item for item in bom_data if item['CId'] == 3)
        self.assertEqual(item3['Item Code'], 'ITEM-0103')
        self.assertEqual(item3['Description'], 'Part C')
        self.assertEqual(item3['UOM'], 'PCS')
        self.assertEqual(item3['Unit Qty'], Decimal('3.000'))
        self.assertAlmostEqual(item3['BOM Qty'], Decimal('6.000')) # Relative to parent 2, Qty is 3.000 * 2.000 (parent 2 qty) = 6.000

    def test_calculate_bom_tree_qty_recursive(self):
        # Testing specific path: 1 -> 2 -> 3
        # CId 3 has PId 2, Qty 3.000
        # CId 2 has PId 1, Qty 2.000
        # CId 1 has PId 0, Qty 1.000 (top level)
        # Expected: 3.000 * 2.000 * 1.000 = 6.000
        qty = BOMMaster._calculate_bom_tree_qty_recursive('WO-SRC-001', 2, 3, DEFAULT_COMPANY_ID, DEFAULT_FINANCIAL_YEAR_ID)
        self.assertAlmostEqual(qty, 6.000)

        # Test a direct child of root: CId 2 has PId 1, Qty 2.000
        qty = BOMMaster._calculate_bom_tree_qty_recursive('WO-SRC-001', 1, 2, DEFAULT_COMPANY_ID, DEFAULT_FINANCIAL_YEAR_ID)
        self.assertAlmostEqual(qty, 2.000)

        # Test a root node: CId 1 has PId 0, Qty 1.000
        qty = BOMMaster._calculate_bom_tree_qty_recursive('WO-SRC-001', 0, 1, DEFAULT_COMPANY_ID, DEFAULT_FINANCIAL_YEAR_ID)
        self.assertAlmostEqual(qty, 1.000)

        # Test non-existent node
        qty = BOMMaster._calculate_bom_tree_qty_recursive('WO-SRC-001', 99, 99, DEFAULT_COMPANY_ID, DEFAULT_FINANCIAL_YEAR_ID)
        self.assertEqual(qty, 0.0) # Or 1.0 depending on how base case is handled if node not found

    @patch.object(BOMMaster, 'save') # Mock save to prevent actual DB writes for recursion test
    def test_copy_bom_node(self, mock_save):
        src_child_id = 1 # Copying Assembly A (CId=1)
        wonosrc = 'WO-SRC-001'
        wonodest = 'WO-DEST-001'
        dest_pid = 0 # Copying to root level
        dest_cid = 0 # Copying to root level

        # Mock objects.create so we can control the 'new_child_id' generation
        with patch.object(BOMMaster.objects, 'aggregate', return_value={'child_id__max': 10}):
            success, msg = BOMMaster.copy_bom_node(
                src_child_id, wonosrc, wonodest,
                DEFAULT_COMPANY_ID, 'test_user', DEFAULT_FINANCIAL_YEAR_ID,
                dest_pid, dest_cid
            )
            self.assertTrue(success)
            self.assertEqual(msg, "BOM node and its children copied successfully.")

            # Verify that save was called for the parent node and its children
            # Original: CId 1 (parent 0) -> CId 2 (parent 1) -> CId 3 (parent 2)
            # Copied: New CId 11 (parent 0) -> New CId 12 (parent 11) -> New CId 13 (parent 12)

            # Check for the top-level copied node (Assembly A)
            mock_save.assert_any_call() # At least one save call happened
            # More granular checks: verify parameters of mocked save calls if possible
            # This requires inspecting mock_save.call_args_list, which can be verbose.
            
            # A more robust test would be to check the database for the new entries
            # if not mocking `save`, or to mock `BOMMaster.objects.create` directly.
            # Example: check that 3 new objects were "created"
            # Here we mock `save` so we can't query the DB, relies on mock count.
            # print(mock_save.call_count) # Should be 3 saves (1 for parent, 2 for children)

            # Assert that the new nodes have been conceptually created
            # This is hard to assert without actual DB writes, so rely on mock_save count.
            # Alternatively, test `get_bom_data_for_display` on the DEST_WO if using actual DB.
            
            # Reset mocks if running multiple tests
            mock_save.reset_mock()

    def test_copy_bom_node_not_found(self):
        success, msg = BOMMaster.copy_bom_node(
            999, 'WO-SRC-001', 'WO-DEST-001',
            DEFAULT_COMPANY_ID, 'test_user', DEFAULT_FINANCIAL_YEAR_ID, 0, 0
        )
        self.assertFalse(success)
        self.assertEqual(msg, "Source BOM node not found.")


class BOMDesignViewsTest(TestCase):
    client = Client()

    @classmethod
    def setUpTestData(cls):
        # Create minimal data required for views to function
        UnitMaster.objects.create(id=1, symbol='PCS')
        ItemMaster.objects.create(id=101, manufacture_description='Assembly A', uom_basic_id=1, company_id=DEFAULT_COMPANY_ID, financial_year_id=DEFAULT_FINANCIAL_YEAR_ID)
        BOMMaster.objects.create(
            child_id=1, parent_id=0, item_id=101, work_order_no='WO-SRC-001',
            quantity=Decimal('1.000'), company_id=DEFAULT_COMPANY_ID, financial_year_id=DEFAULT_FINANCIAL_YEAR_ID
        )

    def test_copy_tree_page_get(self):
        response = self.client.get(reverse('bom_design:copy_tree_page'), {
            'WONoSrc': 'WO-SRC-001',
            'WONoDest': 'WO-DEST-001',
            'DestPId': '0',
            'DestCId': '0'
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom_design/copy_tree.html')
        self.assertContains(response, 'WO-SRC-001')
        self.assertContains(response, 'BOM Design Copy Tree')

    def test_bom_tree_table_get(self):
        response = self.client.get(reverse('bom_design:bom_tree_table'), {
            'wonosrc': 'WO-SRC-001'
        }, HTTP_HX_REQUEST='true') # Simulate HTMX request
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom_design/_bom_tree_table.html')
        self.assertContains(response, 'ITEM-0101') # Check for item code
        self.assertContains(response, 'Assembly A') # Check for description

    @patch('bom_design.models.BOMMaster.copy_bom_node')
    def test_copy_action_post_success(self, mock_copy_bom_node):
        mock_copy_bom_node.return_value = (True, "Copy successful.")

        data = {
            'selected_cids[]': ['1'],
            'wonosrc': 'WO-SRC-001',
            'wonodest': 'WO-DEST-001',
            'dest_pid': '0',
            'dest_cid': '0'
        }
        response = self.client.post(reverse('bom_design:copy_action'), data, HTTP_HX_REQUEST='true')
        
        # Expect a 204 No Content for HTMX redirect or a 302 for normal redirect
        # HX-Redirect header is the key
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Redirect', response.headers)
        self.assertTrue(reverse('bom_design:copy_workorder_success') in response.headers['HX-Redirect'])
        mock_copy_bom_node.assert_called_once_with(
            1, 'WO-SRC-001', 'WO-DEST-001',
            DEFAULT_COMPANY_ID, 'anonymous', DEFAULT_FINANCIAL_YEAR_ID,
            0, 0
        )
        self.assertIn('BOM is Copied successfully', [m.message for m in messages.get_messages(response.wsgi_request)])


    @patch('bom_design.models.BOMMaster.copy_bom_node')
    def test_copy_action_post_failure_same_id(self, mock_copy_bom_node):
        data = {
            'selected_cids[]': ['1'],
            'wonosrc': 'WO-SRC-001',
            'wonodest': 'WO-DEST-001',
            'dest_pid': '0',
            'dest_cid': '1' # Destination CID is same as source CID
        }
        response = self.client.post(reverse('bom_design:copy_action'), data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 200) # HTMX request, will return message in body or trigger
        self.assertIn('refreshBOMTreeList', response.headers.get('HX-Trigger', ''))
        self.assertFalse(mock_copy_bom_node.called) # Should not call copy_bom_node
        self.assertIn('Selection of Assembly/Item is incorrect', [m.message for m in messages.get_messages(response.wsgi_request)])

    @patch('bom_design.models.BOMMaster.copy_bom_node')
    def test_copy_action_post_no_selection(self, mock_copy_bom_node):
        data = {
            'wonosrc': 'WO-SRC-001',
            'wonodest': 'WO-DEST-001',
            'dest_pid': '0',
            'dest_cid': '0'
        }
        response = self.client.post(reverse('bom_design:copy_action'), data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 200)
        self.assertIn('refreshBOMTreeList', response.headers.get('HX-Trigger', ''))
        self.assertFalse(mock_copy_bom_node.called)
        self.assertIn('No BOM items selected for copying.', [m.message for m in messages.get_messages(response.wsgi_request)])

    def test_copy_workorder_success_view(self):
        response = self.client.get(reverse('bom_design:copy_workorder_success'), {
            'WONoSrc': 'WO-SRC-001',
            'WONoDest': 'WO-DEST-001',
            'DestPId': '0',
            'DestCId': '0',
            'msg': 'Test message.'
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom_design/copy_workorder_success.html')
        self.assertContains(response, 'Test message.')
        self.assertContains(response, 'WO-SRC-001')
        self.assertContains(response, 'WO-DEST-001')

    def test_cancel_action_get(self):
        response = self.client.get(reverse('bom_design:cancel_action'), {
            'wonosrc': 'WO-SRC-001',
            'wonodest': 'WO-DEST-001',
            'dest_pid': '0',
            'dest_cid': '0'
        }, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Redirect', response.headers)
        self.assertTrue(reverse('bom_design:copy_workorder_success') in response.headers['HX-Redirect'])

```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for dynamic updates:**
    *   The main `copy_tree.html` uses `hx-get` on `bomTreeTable-container` to fetch the table contents from `{% url 'bom_design:bom_tree_table' %}` on `load` and `refreshBOMTreeList` event.
    *   The `Copy` button uses `hx-post` to `{% url 'bom_design:copy_action' %}`. It includes selected checkboxes and hidden input values (`wonosrc`, `wonodest`, `dest_pid`, `dest_cid`) using `hx-include`.
    *   Upon successful copy, the `copy_action` view responds with `HX-Redirect` to navigate to the success page, replicating the ASP.NET `Response.Redirect`.
    *   Upon failure (e.g., no selection, same CID), the `copy_action` view responds with `HX-Trigger: 'refreshBOMTreeList'` to re-render the table and show messages.
    *   The `Cancel` button uses `hx-get` to `{% url 'bom_design:cancel_action' %}` which also issues an `HX-Redirect`.
    *   `hx-indicator` is used to show a loading spinner during HTMX requests.
*   **Alpine.js for UI state management:** While not extensively used in this simple example (the confirmation dialog is handled by `hx-confirm`), Alpine.js is set up for future client-side interactivity, such as custom modals or dynamic form elements, as shown in the `extra_js` block comment.
*   **DataTables for list views:** The `_bom_tree_table.html` partial initializes DataTables on the `bomTreeTable`. It's configured with `pageLength`, `lengthMenu`, `responsive` options and disables sorting for the checkbox column. The `$(document).ready` wrapper ensures DataTables is properly initialized when the partial is loaded by HTMX.
*   **No full page reloads:** All interactions (table load, copy, cancel) are designed to be handled by HTMX without full page reloads, except for the final redirect, which is also signaled via `HX-Redirect`.

## Final Notes

*   This plan provides a direct conversion for the given ASP.NET functionality. The `clsFunctions` methods (`GetItemCode_PartNo`, `BOMTreeQty`, `getBOMnode`) are critical business logic and have been mapped to `classmethod` or `static_method` in the Django `BOMMaster` model. For `BOMTreeQty` and `getBOMnode`, a simplified/conceptual implementation is provided due to lack of the original C# `clsFunctions` source; these would require a detailed mapping of the original logic.
*   The `Expand Tree` checkbox functionality from `RadTreeList` was removed as it's specific to the Telerik tree component and does not directly translate to a flat DataTables view.
*   Session variables like `CompId`, `FinYearId`, `SessionId` are assumed to be accessible through Django's `request.session` and are managed by `get_user_context` helper.
*   Error handling with `messages.error` is integrated, leveraging Django's messaging framework which can be displayed in `base.html` or via HTMX event triggers.
*   All generated code adheres to the "Fat Model, Thin View" principle, pushing complex data retrieval and business logic into the `BOMMaster` model.