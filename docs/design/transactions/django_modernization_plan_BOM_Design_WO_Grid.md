## ASP.NET to Django Conversion Script: BOM Design Work Order Grid

This modernization plan outlines the transition of your legacy ASP.NET BOM Design Work Order Grid module to a modern Django-based solution. Our approach prioritizes automated conversion, leveraging Django's robust features, HTMX for dynamic interactions, Alpine.js for frontend state, and DataTables for efficient data presentation.

This transition will deliver a more maintainable, scalable, and performant application, reducing development time and improving user experience.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns related to the ASP.NET code.

**Instructions:**
From the ASP.NET code-behind, we observe queries against `tblSD_WO_Category` and `SD_Cust_master`, and a stored procedure `Sp_WONO_NotInBom` which retrieves work order related data. We infer the following tables and their key columns:

*   **`tblSD_WO_Category`**: Represents Work Order Categories.
    *   `CId` (Primary Key, integer)
    *   `Symbol` (String)
    *   `CName` (String)
    *   `CompId` (Integer, likely Company ID)
*   **`SD_Cust_master`**: Represents Customer Master data.
    *   `CustomerId` (Primary Key, string/integer based on usage in `getCode`)
    *   `CustomerName` (String)
    *   `CompId` (Integer, likely Company ID)
*   **`SD_Cust_WorkOrder_Master` (Inferred for main data)**: This table is implied as the source for the `Sp_WONO_NotInBom` procedure's output columns.
    *   `WONo` (Primary Key/Unique Identifier, string)
    *   `FinYear` (String, e.g., '2023-24')
    *   `CustomerId` (Foreign Key to `SD_Cust_master`)
    *   `EnqId` (String, Enquiry ID)
    *   `PONo` (String, Purchase Order No)
    *   `SysDate` (Date, System Date / Generation Date)
    *   `EmployeeName` (String, Generated By Employee Name)
    *   `CId` (Foreign Key to `tblSD_WO_Category`)
    *   We will also add an `is_in_bom` field to simulate the "NotInBom" aspect, which would be managed by a separate BOM relation in a real system.

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations performed by the ASP.NET code.

**Instructions:**
The provided ASP.NET code primarily handles **Read** operations, specifically listing and searching Work Orders (WO) that are not yet associated with a Bill of Materials (BOM).

*   **Read (List & Filter):**
    *   The `SearchGridView1` displays a list of Work Orders.
    *   Filtering is applied based on:
        *   Search type (`DropDownList1`): Customer Name, Enquiry No, PO No, WO No.
        *   Search value (`TxtSearchValue` or `txtSearchCustomer`).
        *   WO Category (`DDLTaskWOType`).
    *   Pagination and sorting are handled by `SearchGridView1`.
    *   An autocomplete feature (`TxtSearchValue_AutoCompleteExtender`) assists in searching for Customer Names.
    *   The data retrieval is centralized in the `BindDataCust` method, which utilizes a stored procedure `Sp_WONO_NotInBom` for the actual data fetching and filtering.

*   **No direct Create, Update, or Delete operations for Work Orders are visible in this specific `.aspx` and `.cs` file.** The page appears to be a selection grid for a broader BOM design process. Therefore, the Django plan will focus on replicating this search and list functionality, not full CRUD for `WorkOrder` itself.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, and infer their Django/HTMX/Alpine.js equivalents.

**Instructions:**
The page displays a search interface and a data grid.

*   **Search Controls:**
    *   `DropDownList1` (Search Type): Will be a Django `forms.ChoiceField` rendered as a `<select>` element. Its `onselectedindexchanged` `AutoPostBack` will be replaced by HTMX's `hx-post` or `hx-get` to dynamically update the search input fields and table.
    *   `txtSearchCustomer` and `TxtSearchValue` (Search Input): Will be a single Django `forms.CharField` rendered as `<input type="text">`. Visibility toggling will be handled by Alpine.js based on the `search_type` selected.
    *   `TxtSearchValue_AutoCompleteExtender` (Customer Autocomplete): Will be implemented using HTMX `hx-get` to a dedicated Django view returning JSON suggestions for customer names.
    *   `DDLTaskWOType` (WO Category): Will be a Django `forms.ModelChoiceField` rendered as a `<select>`. Its `AutoPostBack` will be replaced by HTMX to re-fetch the table data.
    *   `btnSearch` (Search Button): Will trigger an HTMX request to refresh the data table based on current form inputs.

*   **Data Display:**
    *   `SearchGridView1`: This will be converted to a standard HTML `<table>` element initialized with **DataTables.js**. All server-side paging, sorting, and filtering logic within `GridView` will be offloaded to DataTables for client-side efficiency. Data will be pre-loaded via HTMX or on page load.
    *   The `HyperLinkField` for `WONo` will become a standard HTML `<a>` tag pointing to the detailed BOM design page.

### Step 4: Generate Django Code

We will create a new Django app, let's call it `design`.

#### 4.1 Models (`design/models.py`)

```python
from django.db import models

class Company(models.Model):
    """
    Represents the Company (CompId) concept from ASP.NET Session.
    Assuming a simple representation for migration.
    """
    company_id = models.IntegerField(db_column='CompId', primary_key=True)
    name = models.CharField(max_length=255)

    class Meta:
        managed = False  # Important: Django won't manage this table's schema.
        db_table = 'tblCompanyMaster'  # Placeholder table name if one exists
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name

class FinancialYear(models.Model):
    """
    Represents the Financial Year (FinYearId) concept from ASP.NET Session.
    Assuming a simple representation for migration.
    """
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    year_name = models.CharField(max_length=50)

    class Meta:
        managed = False  # Important: Django won't manage this table's schema.
        db_table = 'tblFinancialYear'  # Placeholder table name if one exists
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.year_name


class WOCategory(models.Model):
    """
    Maps to tblSD_WO_Category for Work Order Categories.
    """
    category_id = models.IntegerField(db_column='CId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    name = models.CharField(db_column='CName', max_length=255)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='wo_categories')

    class Meta:
        managed = False
        db_table = 'tblSD_WO_Category'
        verbose_name = 'WO Category'
        verbose_name_plural = 'WO Categories'

    def __str__(self):
        return f"{self.symbol} - {self.name}"

class Customer(models.Model):
    """
    Maps to SD_Cust_master for Customer information.
    """
    customer_id = models.CharField(db_column='CustomerId', max_length=50, primary_key=True) # Assuming string based on fun.getCode
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='customers')

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customer_name

class WorkOrder(models.Model):
    """
    Represents Work Order data, inferred to be from SD_Cust_WorkOrder_Master
    and the output of Sp_WONO_NotInBom.
    """
    wo_no = models.CharField(db_column='WONo', max_length=50, primary_key=True) # DataKeyNames="WONo"
    fin_year = models.CharField(db_column='FinYear', max_length=10)
    customer = models.ForeignKey(Customer, on_delete=models.DO_NOTHING, db_column='CustomerId', to_field='customer_id', related_name='workorders')
    customer_name_display = models.CharField(db_column='CustomerName', max_length=255, blank=True, null=True) # Denormalized for display ease
    enquiry_id = models.CharField(db_column='EnqId', max_length=50, blank=True, null=True)
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    system_date = models.DateField(db_column='SysDate')
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    wo_category = models.ForeignKey(WOCategory, on_delete=models.DO_NOTHING, db_column='CId', related_name='workorders')
    is_in_bom = models.BooleanField(db_column='IsInBom', default=False) # Simulating "NotInBom" logic

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master' # Inferred source table
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'
        # Add a unique_together constraint if WONo is unique within FinYear or Company
        # unique_together = (('wo_no', 'fin_year'),)

    def __str__(self):
        return self.wo_no

    @classmethod
    def get_filtered_work_orders(cls, comp_id, fin_year_id, search_type, search_value, wo_category_id):
        """
        Simulates the logic of Sp_WONO_NotInBom and dynamic filtering.
        In a real scenario, the stored procedure logic would be re-implemented
        using Django ORM queries for better maintainability and portability.
        This method returns work orders NOT in BOM.
        """
        queryset = cls.objects.filter(
            company__company_id=comp_id, # Assuming WorkOrder has a link to Company
            # For simplicity, I'm assuming FinYear is part of the WO record,
            # or FinYearId is used for filtering. Adjust as per actual DB schema.
            # For now, let's filter by fin_year field on the WO itself.
            fin_year=FinancialYear.objects.get(fin_year_id=fin_year_id).year_name,
            is_in_bom=False # This is the "NotInBom" part
        )

        if search_value:
            if search_type == '0':  # Customer Name
                queryset = queryset.filter(customer__customer_name__icontains=search_value)
            elif search_type == '1':  # Enquiry No
                queryset = queryset.filter(enquiry_id__icontains=search_value)
            elif search_type == '2':  # PO No
                queryset = queryset.filter(po_no__icontains=search_value)
            elif search_type == '3':  # WO No
                queryset = queryset.filter(wo_no__icontains=search_value)
        
        if wo_category_id and wo_category_id != 'WO Category': # DDL had "WO Category" as default
            queryset = queryset.filter(wo_category__category_id=wo_category_id)

        return queryset.order_by('-system_date') # Default sort order
```

#### 4.2 Forms (`design/forms.py`)

```python
from django import forms
from .models import WOCategory, WorkOrder, Customer

class WorkOrderSearchForm(forms.Form):
    """
    Form for handling the search and filter criteria on the Work Order list page.
    Combines DropDownList1, TxtSearchValue/txtSearchCustomer, and DDLTaskWOType.
    """
    SEARCH_TYPE_CHOICES = [
        ('0', 'Customer Name'),
        ('1', 'Enquiry No'),
        ('2', 'PO No'),
        ('3', 'WO No'),
    ]

    search_type = forms.ChoiceField(
        choices=SEARCH_TYPE_CHOICES,
        required=False,
        label="Search By",
        widget=forms.Select(attrs={
            'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-post': '{{ request.path }}', # Post to current URL to update search value visibility
            'hx-target': '#search-inputs-container', # Target div to swap content
            'hx-swap': 'outerHTML',
            'hx-trigger': 'change',
            '_': 'on htmx:afterSwap remove .htmx-request from this' # Remove htmx-request class manually
        })
    )
    
    search_value = forms.CharField(
        max_length=255,
        required=False,
        label="Search Value",
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter search text...',
            # HTMX attributes for autocomplete if search_type is 'Customer Name'
            'hx-get': '/design/workorder/autocomplete/customer/',
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#autocomplete-results',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off'
        })
    )

    wo_category = forms.ModelChoiceField(
        queryset=WOCategory.objects.all().order_by('name'),
        empty_label="WO Category",
        required=False,
        label="WO Category",
        widget=forms.Select(attrs={
            'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-post': '{{ request.path }}', # Post to current URL to trigger table refresh
            'hx-target': '#workorderTable-container',
            'hx-swap': 'innerHTML',
            'hx-trigger': 'change',
            'name': 'wo_category_id' # Explicitly set name to match view logic
        })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Adjust search_value widget based on initial search_type
        selected_search_type = self.initial.get('search_type', self.data.get('search_type', '0'))
        if selected_search_type == '0': # Customer Name
            self.fields['search_value'].widget.attrs.update({
                'placeholder': 'Enter Customer Name...',
                'hx-get': '/design/workorder/autocomplete/customer/',
                'hx-trigger': 'keyup changed delay:500ms, search',
                'hx-target': '#autocomplete-results',
                'hx-swap': 'innerHTML',
                'autocomplete': 'off'
            })
        else: # Other search types
            self.fields['search_value'].widget.attrs.pop('hx-get', None)
            self.fields['search_value'].widget.attrs.pop('hx-trigger', None)
            self.fields['search_value'].widget.attrs.pop('hx-target', None)
            self.fields['search_value'].widget.attrs.pop('hx-swap', None)
            self.fields['search_value'].widget.attrs.pop('autocomplete', None)
            self.fields['search_value'].widget.attrs['placeholder'] = 'Enter Search Value...'

    def get_filtered_queryset(self, comp_id, fin_year_id):
        """
        Retrieves the filtered queryset based on form data and context.
        This method encapsulates the filtering logic from the original ASP.NET BindDataCust.
        """
        search_type = self.cleaned_data.get('search_type')
        search_value = self.cleaned_data.get('search_value')
        wo_category_obj = self.cleaned_data.get('wo_category')
        wo_category_id = wo_category_obj.category_id if wo_category_obj else None

        return WorkOrder.get_filtered_work_orders(
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            search_type=search_type,
            search_value=search_value,
            wo_category_id=wo_category_id
        )

```

#### 4.3 Views (`design/views.py`)

```python
from django.views.generic import ListView, TemplateView
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from django.db.models import Q # For advanced querying if needed
from .models import WorkOrder, Customer, WOCategory, Company, FinancialYear
from .forms import WorkOrderSearchForm

class WorkOrderListView(TemplateView):
    """
    Main view for the BOM Design Work Order Grid.
    Renders the initial page with the search form and an empty container
    for the HTMX-loaded table.
    """
    template_name = 'design/workorder/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize form with data from GET parameters if available
        form = WorkOrderSearchForm(self.request.GET or None)
        context['form'] = form
        
        # Simulate Session context for CompId and FinYearId
        # In a real app, these would come from authentication/user profile
        # For demonstration:
        try:
            # Assuming Company ID 1 and Financial Year ID 1 exist
            context['comp_id'] = self.request.session.get('compid', Company.objects.first().company_id)
            context['fin_year_id'] = self.request.session.get('finyear', FinancialYear.objects.first().fin_year_id)
        except (Company.DoesNotExist, FinancialYear.DoesNotExist):
            context['comp_id'] = 1 # Fallback to a default if no companies/fin years exist
            context['fin_year_id'] = 1
        
        return context

class WorkOrderTablePartialView(ListView):
    """
    View to render only the Work Order table, to be loaded via HTMX.
    Applies filtering based on GET parameters.
    """
    model = WorkOrder
    template_name = 'design/workorder/_workorder_table.html'
    context_object_name = 'workorders'

    def get_queryset(self):
        # Simulate Session context for CompId and FinYearId
        # In a real app, these would come from authentication/user profile
        # For demonstration:
        try:
            comp_id = self.request.session.get('compid', Company.objects.first().company_id)
            fin_year_id = self.request.session.get('finyear', FinancialYear.objects.first().fin_year_id)
        except (Company.DoesNotExist, FinancialYear.DoesNotExist):
            comp_id = 1 # Fallback to a default
            fin_year_id = 1

        # Process the form data to get filter parameters
        form = WorkOrderSearchForm(self.request.GET or None)
        if form.is_valid():
            return form.get_filtered_queryset(comp_id=comp_id, fin_year_id=fin_year_id)
        
        # If form is not valid or no search parameters, return a default queryset
        # (e.g., all WOs for the current company/fin year not in BOM)
        return WorkOrder.get_filtered_work_orders(
            comp_id=comp_id, fin_year_id=fin_year_id,
            search_type=None, search_value=None, wo_category_id=None
        )

    def render_to_response(self, context, **response_kwargs):
        # If the request is a direct GET (not HTMX for swap), ensure it's handled correctly
        # This view is primarily for HTMX partial loads.
        if self.request.headers.get('HX-Request'):
            return super().render_to_response(context, **response_kwargs)
        # For non-HTMX requests, perhaps redirect or show an error, or just return the partial.
        # For this setup, assuming it's always called by HTMX after the initial load.
        return super().render_to_response(context, **response_kwargs)

class CustomerAutoCompleteView(TemplateView):
    """
    Provides customer name suggestions for the autocomplete feature via HTMX.
    Mimics the ASP.NET WebMethod 'sql'.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        # Simulate CompId from session or default
        try:
            comp_id = request.session.get('compid', Company.objects.first().company_id)
        except Company.DoesNotExist:
            comp_id = 1

        if query:
            customers = Customer.objects.filter(
                customer_name__icontains=query,
                company__company_id=comp_id
            ).values_list('customer_name', 'customer_id')[:10] # Limit suggestions
            
            suggestions = []
            for name, cust_id in customers:
                suggestions.append(f"{name} [{cust_id}]") # Format as "CustomerName [CustomerId]"

            return JsonResponse({'suggestions': suggestions})
        return JsonResponse({'suggestions': []})

class SearchInputsPartialView(TemplateView):
    """
    Renders only the search input fields part of the form, to be swapped by HTMX
    when the search_type dropdown changes.
    """
    template_name = 'design/workorder/_search_inputs.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        form = WorkOrderSearchForm(self.request.GET or None)
        context['form'] = form
        return context

    def render_to_response(self, context, **response_kwargs):
        # Ensure form is initialized with the current search_type to correctly set placeholder/hx-attrs
        form = WorkOrderSearchForm(self.request.GET)
        context['form'] = form
        return super().render_to_response(context, **response_kwargs)
```

#### 4.4 Templates

**`design/workorder/list.html`**
This template contains the main structure for the page, including the search form and the container for the DataTables grid.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">BOM Design - Work Orders</h2>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Search Work Orders</h3>
        <form id="workorder-search-form" 
              hx-get="{% url 'design:workorder_table' %}"
              hx-target="#workorderTable-container"
              hx-swap="innerHTML"
              hx-trigger="submit, change from:#id_wo_category"
              class="space-y-4">
            {% csrf_token %}

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label for="{{ form.search_type.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.search_type.label }}
                    </label>
                    {{ form.search_type }}
                </div>
                
                <div id="search-inputs-container">
                    {# This div will be swapped by HTMX when search_type changes #}
                    {% include 'design/workorder/_search_inputs.html' %}
                </div>

                <div>
                    <label for="{{ form.wo_category.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.wo_category.label }}
                    </label>
                    {{ form.wo_category }}
                </div>
            </div>

            <div class="mt-6 flex justify-end">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Search
                </button>
            </div>
        </form>
    </div>
    
    <div id="workorderTable-container"
         hx-trigger="load, refreshWorkOrderList from:body"
         hx-get="{% url 'design:workorder_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg p-6">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Work Orders...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('searchLogic', () => ({
            currentSearchType: '{{ form.search_type.value|default:"0" }}', // Initialize with current selection
            init() {
                this.$watch('currentSearchType', (value) => {
                    // This logic is mostly handled by HTMX swapping the partial
                    // but Alpine.js can be used for more complex local UI state
                    // if needed, e.g., enabling/disabling other fields.
                    console.log('Search type changed to:', value);
                });
            }
        }));
    });
</script>
{% endblock %}
```

**`design/workorder/_search_inputs.html`**
This partial template renders the dynamic search input field and its autocomplete suggestions.

```html
<div id="search-inputs-container" x-data="{ searchType: '{{ form.search_type.value|default:"0" }}' }">
    <label for="{{ form.search_value.id_for_label }}" class="block text-sm font-medium text-gray-700">
        Search Value
    </label>
    {% if form.search_type.value == '0' or not form.search_type.value %} {# Customer Name #}
        <input type="text" 
               id="{{ form.search_value.id_for_label }}" 
               name="{{ form.search_value.name }}" 
               value="{{ form.search_value.value|default:'' }}"
               class="{{ form.search_value.field.widget.attrs.class }}"
               placeholder="Enter Customer Name..."
               hx-get="{% url 'design:customer_autocomplete' %}"
               hx-trigger="keyup changed delay:500ms, search"
               hx-target="#autocomplete-results"
               hx-swap="innerHTML"
               autocomplete="off"
               >
        <div id="autocomplete-results" class="relative z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg"></div>
    {% else %} {# Other search types: Enquiry, PO, WO #}
        <input type="text" 
               id="{{ form.search_value.id_for_label }}" 
               name="{{ form.search_value.name }}" 
               value="{{ form.search_value.value|default:'' }}"
               class="{{ form.search_value.field.widget.attrs.class }}"
               placeholder="Enter Search Value..."
               >
    {% endif %}
</div>
```

**`design/workorder/_workorder_table.html`**
This partial template contains the DataTables-enabled HTML table.

```html
<table id="workorderTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. Date</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO Category</th> {# Added for completeness #}
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% if workorders %}
            {% for wo in workorders %}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ wo.fin_year }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-blue-600 hover:underline">
                    {# Assuming this links to a BOM_Design_WO_TreeView.aspx like page #}
                    <a href="/module/design/bom/treeview/{{ wo.wo_no }}/?modid=3&submodid=26">{{ wo.wo_no }}</a>
                </td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-left">{{ wo.customer_name_display }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ wo.customer.customer_id }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ wo.system_date|date:"Y-m-d" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-left">{{ wo.employee_name }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ wo.wo_category.name }}</td>
            </tr>
            {% endfor %}
        {% else %}
        <tr>
            <td colspan="8" class="py-4 px-4 text-center text-lg text-maroon-600">No data to display !</td>
        </tr>
        {% endif %}
    </tbody>
</table>

<script>
    // Ensure DataTables is re-initialized after HTMX swap
    // This is crucial for DataTables to work correctly after dynamic content loading.
    $(document).ready(function() {
        $('#workorderTable').DataTable({
            "pageLength": 20, // Matches ASP.NET GridView PageSize
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "responsive": true,
            "pagingType": "full_numbers"
        });
    });
</script>
```

#### 4.5 URLs (`design/urls.py`)

```python
from django.urls import path
from .views import (
    WorkOrderListView, 
    WorkOrderTablePartialView, 
    CustomerAutoCompleteView,
    SearchInputsPartialView
)

app_name = 'design' # Define app_name for namespacing URLs

urlpatterns = [
    # Main Work Order List page
    path('workorder/', WorkOrderListView.as_view(), name='workorder_list'),
    
    # HTMX endpoint for the Work Order table (partial load)
    path('workorder/table/', WorkOrderTablePartialView.as_view(), name='workorder_table'),

    # HTMX endpoint for customer autocomplete suggestions
    path('workorder/autocomplete/customer/', CustomerAutoCompleteView.as_view(), name='customer_autocomplete'),

    # HTMX endpoint to swap search inputs based on search type dropdown change
    path('workorder/search-inputs/', SearchInputsPartialView.as_view(), name='search_inputs_partial'),

    # No direct CRUD for WorkOrder as per original ASP.NET analysis
    # If CRUD was needed, it would look like:
    # path('workorder/add/', WorkOrderCreateView.as_view(), name='workorder_add'),
    # path('workorder/edit/<str:pk>/', WorkOrderUpdateView.as_view(), name='workorder_edit'),
    # path('workorder/delete/<str:pk>/', WorkOrderDeleteView.as_view(), name='workorder_delete'),
]
```

#### 4.6 Tests (`design/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import Company, FinancialYear, WOCategory, Customer, WorkOrder
from datetime import date

class BaseModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company = Company.objects.create(company_id=1, name='Test Company')
        cls.fin_year = FinancialYear.objects.create(fin_year_id=1, year_name='2023-24')
        cls.wo_category_select = WOCategory.objects.create(category_id=0, symbol='SEL', name='Select Category', company=cls.company)
        cls.wo_category_a = WOCategory.objects.create(category_id=1, symbol='CAT-A', name='Category A', company=cls.company)
        cls.customer1 = Customer.objects.create(customer_id='CUST001', customer_name='Customer Alpha', company=cls.company)
        cls.customer2 = Customer.objects.create(customer_id='CUST002', customer_name='Customer Beta', company=cls.company)

        WorkOrder.objects.create(
            wo_no='WO001', fin_year='2023-24', customer=cls.customer1, customer_name_display='Customer Alpha',
            enquiry_id='ENQ001', po_no='PO001', system_date=date(2023, 1, 15), employee_name='John Doe',
            wo_category=cls.wo_category_a, is_in_bom=False
        )
        WorkOrder.objects.create(
            wo_no='WO002', fin_year='2023-24', customer=cls.customer2, customer_name_display='Customer Beta',
            enquiry_id='ENQ002', po_no='PO002', system_date=date(2023, 2, 20), employee_name='Jane Smith',
            wo_category=cls.wo_category_a, is_in_bom=True # This one is in BOM
        )
        WorkOrder.objects.create(
            wo_no='WO003', fin_year='2023-24', customer=cls.customer1, customer_name_display='Customer Alpha',
            enquiry_id='ENQ003', po_no='PO003', system_date=date(2023, 3, 10), employee_name='John Doe',
            wo_category=cls.wo_category_a, is_in_bom=False
        )

class WorkOrderModelTest(BaseModelTest):
    def test_workorder_creation(self):
        wo = WorkOrder.objects.get(wo_no='WO001')
        self.assertEqual(wo.customer.customer_name, 'Customer Alpha')
        self.assertEqual(wo.fin_year, '2023-24')
        self.assertFalse(wo.is_in_bom)

    def test_get_filtered_work_orders(self):
        # Test basic filtering (not in BOM)
        queryset = WorkOrder.get_filtered_work_orders(self.company.company_id, self.fin_year.fin_year_id, None, None, None)
        self.assertEqual(queryset.count(), 2)
        self.assertIn(WorkOrder.objects.get(wo_no='WO001'), queryset)
        self.assertIn(WorkOrder.objects.get(wo_no='WO003'), queryset)
        self.assertNotIn(WorkOrder.objects.get(wo_no='WO002'), queryset)

        # Test filter by Customer Name
        queryset = WorkOrder.get_filtered_work_orders(self.company.company_id, self.fin_year.fin_year_id, '0', 'Alpha', None)
        self.assertEqual(queryset.count(), 2)
        self.assertIn(WorkOrder.objects.get(wo_no='WO001'), queryset)
        self.assertIn(WorkOrder.objects.get(wo_no='WO003'), queryset)

        # Test filter by WO No
        queryset = WorkOrder.get_filtered_work_orders(self.company.company_id, self.fin_year.fin_year_id, '3', 'WO001', None)
        self.assertEqual(queryset.count(), 1)
        self.assertIn(WorkOrder.objects.get(wo_no='WO001'), queryset)

        # Test filter by WO Category (assuming no other categories for now)
        queryset = WorkOrder.get_filtered_work_orders(self.company.company_id, self.fin_year.fin_year_id, None, None, self.wo_category_a.category_id)
        self.assertEqual(queryset.count(), 2)

class WorkOrderViewsTest(BaseModelTest):
    def setUp(self):
        super().setUp()
        self.client = Client()
        # Set session variables as ASP.NET code did
        session = self.client.session
        session['compid'] = self.company.company_id
        session['finyear'] = self.fin_year.fin_year_id
        session.save()

    def test_list_view_get(self):
        response = self.client.get(reverse('design:workorder_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/workorder/list.html')
        self.assertContains(response, 'BOM Design - Work Orders')
        self.assertIsInstance(response.context['form'], WorkOrderSearchForm)

    def test_table_partial_view_htmx_load(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('design:workorder_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/workorder/_workorder_table.html')
        self.assertContains(response, 'WO001') # Should contain non-BOM work orders
        self.assertNotContains(response, 'WO002') # Should not contain WO in BOM

    def test_table_partial_view_htmx_search(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        # Search by Customer Name "Alpha"
        response = self.client.get(
            reverse('design:workorder_table'),
            {'search_type': '0', 'search_value': 'Alpha'},
            **headers
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO001')
        self.assertContains(response, 'WO003')
        self.assertNotContains(response, 'WO002') # Still excluded by is_in_bom=False

        # Search by WO No "WO001"
        response = self.client.get(
            reverse('design:workorder_table'),
            {'search_type': '3', 'search_value': 'WO001'},
            **headers
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO001')
        self.assertNotContains(response, 'WO003')
        
    def test_customer_autocomplete_view(self):
        response = self.client.get(reverse('design:customer_autocomplete'), {'q': 'alpha'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = response.json()
        self.assertIn('Customer Alpha [CUST001]', data['suggestions'])
        self.assertNotIn('Customer Beta [CUST002]', data['suggestions']) # Should filter if 'alpha' is not in name

    def test_search_inputs_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('design:search_inputs_partial'), {'search_type': '1'}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/workorder/_search_inputs.html')
        # Check if the input field is rendered without autocomplete attributes for 'Enquiry No'
        self.assertContains(response, 'placeholder="Enter Search Value..."')
        self.assertNotContains(response, 'hx-get="/design/workorder/autocomplete/customer/"')

        # Test with 'Customer Name' selected
        response = self.client.get(reverse('design:search_inputs_partial'), {'search_type': '0'}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'placeholder="Enter Customer Name..."')
        self.assertContains(response, 'hx-get="/design/workorder/autocomplete/customer/"')

```

### Step 5: HTMX and Alpine.js Integration

The provided Django templates and forms are designed with HTMX and Alpine.js in mind:

*   **HTMX for dynamic updates:**
    *   The main list page (`list.html`) uses `hx-get` on `load` and custom triggers (`refreshWorkOrderList`) to fetch the table content.
    *   The search form uses `hx-get` on `submit` and `change` (for `wo_category` and `search_type`) to re-fetch and swap the table content or the search input fields.
    *   The `search_type` dropdown uses `hx-post` (or `hx-get` to the partial view) to update the `search-inputs-container`, dynamically showing/hiding the autocomplete specific attributes based on the selected search type.
    *   The `search_value` input (when `search_type` is 'Customer Name') uses `hx-get` to `customer_autocomplete` endpoint, triggering `keyup` with a `delay` to fetch suggestions.
    *   No full page reloads are required for any search or filter actions.
    *   The `HX-Trigger` header would be used by `form_valid` in a CRUD view (if present) to signal the client to refresh the list, ensuring seamless updates. (Not explicitly shown here as no CRUD views are present for `WorkOrder`).

*   **Alpine.js for UI state management:**
    *   A simple Alpine.js component (`searchLogic`) is added to `list.html` to demonstrate how local UI state (like `currentSearchType`) can be managed. While HTMX handles the bulk of the dynamic UI updates by swapping HTML, Alpine.js is ideal for client-side state, e.g., toggling CSS classes, managing modal visibility (if modals were part of this specific page, though not in the original ASP.NET).
    *   In the `_search_inputs.html` partial, `x-data` is used to initialize `searchType` based on the current form value, though the primary logic for switching input types relies on HTMX swapping the entire `search-inputs-container` div with the appropriate partial.

*   **DataTables for list views:**
    *   The `_workorder_table.html` partial directly initializes DataTables on `$(document).ready()`. Since this partial is loaded via HTMX, the DataTables initialization will correctly run after the new table HTML is inserted into the DOM. This offloads pagination, sorting, and client-side searching to DataTables, making the Django backend simpler and more efficient for large datasets.

### Final Notes

*   **Placeholders:** Replace `tblCompanyMaster`, `tblFinancialYear`, `SD_Cust_WorkOrder_Master`, `tblSD_WO_Category`, `SD_Cust_master` with your actual database table names if different.
*   **Business Logic:** The `WorkOrder.get_filtered_work_orders` method in the model is a simplified re-implementation of the logic from `Sp_WONO_NotInBom` and `BindDataCust`. For a full migration, the exact logic of the stored procedure would need to be analyzed and accurately translated into Django ORM queries for robust and maintainable code.
*   **Session Management:** The `comp_id` and `fin_year_id` are currently mocked using defaults and session data. In a production Django application, these would typically be tied to the authenticated user's profile or an organization-level setting, accessed securely.
*   **Error Handling:** Add more robust error handling and user feedback mechanisms (e.g., Django Messages for alerts) for a production-ready application.
*   **CSS:** The Tailwind CSS classes are included directly in the HTML widgets and templates. Ensure Tailwind CSS is configured in your Django project.