## ASP.NET to Django Conversion Script: Dashboard_BOM Module Modernization

This document outlines a comprehensive plan to transition your legacy ASP.NET Dashboard_BOM module to a modern, robust, and scalable Django 5.0+ solution. Our approach prioritizes AI-assisted automation to streamline the migration, minimize manual coding, and ensure a high-quality, maintainable system. The focus is on business value, improved user experience, and a foundation for future enhancements.

The original ASP.NET code provided is a minimal placeholder for a page named "Dashboard_BOM". Since no specific database schema, backend logic, or UI components were present, this modernization plan makes *inferences* based on the module's name "Bill of Materials (BOM) Dashboard". We will assume a standard BOM data structure and associated CRUD (Create, Read, Update, Delete) operations commonly found in ERP systems.

### Business Value Proposition:

*   **Enhanced Performance & Scalability:** Django's efficiency and Python's flexibility provide a more responsive and scalable platform, handling increased user loads and data volumes.
*   **Modern User Experience:** Leveraging HTMX and Alpine.js delivers a highly interactive, dynamic interface without complex JavaScript frameworks, improving user satisfaction and productivity.
*   **Reduced Maintenance Costs:** A clean, well-structured Django codebase, coupled with comprehensive testing, significantly lowers ongoing maintenance and debugging efforts.
*   **Faster Feature Development:** Django's "batteries-included" philosophy and a rich ecosystem accelerate the development of new features and integrations.
*   **Improved Data Insight:** DataTables provides advanced client-side search, sort, and pagination capabilities, empowering users to quickly analyze and manage BOM information.
*   **Future-Proof Architecture:** Transitioning to a modern, open-source stack ensures your application remains adaptable to evolving business needs and technology trends.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Inference:** Given the module is named "Dashboard_BOM", we infer a database table related to Bills of Materials.

*   **Inferred Table Name:** `tbl_BOM`
*   **Inferred Columns:**
    *   `BOM_ID` (Primary Key, integer)
    *   `Product_Code` (string, e.g., for the assembly)
    *   `Component_Code` (string, e.g., for the part)
    *   `Quantity` (decimal/numeric)
    *   `Unit_of_Measure` (string, e.g., 'EA', 'KG') - *Adding this for more realism, though not explicitly in the bare ASPX.*

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Inference:** The provided C# code-behind only contains an empty `Page_Load` method, indicating no explicit functionality. However, a "Dashboard" typically implies listing data, and an enterprise module like "BOM" would inherently require full CRUD capabilities for managing records.

*   **Read (List):** Display a list of all BOM entries.
*   **Create:** Add a new BOM entry.
*   **Update:** Edit an existing BOM entry.
*   **Delete:** Remove a BOM entry.
*   **Validation Logic:** Standard field validations (e.g., required fields, data type checks for quantity).

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Inference:** With no UI controls specified, we infer standard components for displaying and managing data.

*   **Data Display:** A table/grid view to list BOM entries (Django equivalent: DataTables).
*   **Data Input:** Forms for creating and editing BOM entries (Django equivalent: `ModelForm` with `TextInput` widgets).
*   **Actions:** Buttons for Add, Edit, Delete, Save, Cancel (Django equivalent: HTML buttons with HTMX attributes).
*   **Client-Side Interactions:** HTMX for dynamic form loading, submission, and list refreshes. Alpine.js for simple UI state management (e.g., modal visibility).

### Step 4: Generate Django Code

Based on the inferences, here is the generated Django application structure. We will create a Django app named `bom_module`.

#### 4.1 Models (bom_module/models.py)

```python
from django.db import models

class BOM(models.Model):
    """
    Represents a Bill of Material entry.
    Assumed to map to an existing database table 'tbl_BOM'.
    """
    bom_id = models.AutoField(primary_key=True, db_column='BOM_ID')
    product_code = models.CharField(
        max_length=100, 
        db_column='Product_Code', 
        help_text='The code for the finished product or assembly.'
    )
    component_code = models.CharField(
        max_length=100, 
        db_column='Component_Code', 
        help_text='The code for the component part.'
    )
    quantity = models.DecimalField(
        max_digits=10, 
        decimal_places=4, 
        db_column='Quantity', 
        help_text='The quantity of the component required for the product.'
    )
    unit_of_measure = models.CharField(
        max_length=10, 
        db_column='Unit_of_Measure', 
        blank=True, 
        null=True, 
        help_text='Unit of measure (e.g., EA, KG).'
    )
    
    class Meta:
        managed = False  # Set to False if Django should not manage table creation/deletion
        db_table = 'tbl_BOM' # Name of the existing database table
        verbose_name = 'Bill of Material'
        verbose_name_plural = 'Bills of Materials'
        ordering = ['product_code', 'component_code']

    def __str__(self):
        return f"BOM for {self.product_code}: {self.component_code} ({self.quantity} {self.unit_of_measure or ''})"
        
    def get_display_name(self):
        """Returns a more descriptive name for display purposes."""
        return f"{self.product_code} - {self.component_code}"

    # Example of a fat model method for business logic
    def calculate_total_cost(self, component_cost_per_unit):
        """
        Calculates the total cost for this BOM entry based on component unit cost.
        This method assumes 'component_cost_per_unit' is passed in.
        In a real scenario, component costs might be looked up from another model.
        """
        if self.quantity is not None and component_cost_per_unit is not None:
            return self.quantity * component_cost_per_unit
        return 0.0
```

#### 4.2 Forms (bom_module/forms.py)

```python
from django import forms
from .models import BOM

class BOMForm(forms.ModelForm):
    """
    Form for creating and updating BOM entries.
    """
    class Meta:
        model = BOM
        fields = ['product_code', 'component_code', 'quantity', 'unit_of_measure']
        widgets = {
            'product_code': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter product code'}),
            'component_code': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter component code'}),
            'quantity': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.0001', 'placeholder': 'Enter quantity'}),
            'unit_of_measure': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'e.g., EA, KG'}),
        }
        
    def clean_quantity(self):
        """
        Custom validation for quantity to ensure it's positive.
        """
        quantity = self.cleaned_data.get('quantity')
        if quantity is not None and quantity <= 0:
            raise forms.ValidationError("Quantity must be a positive number.")
        return quantity
```

#### 4.3 Views (bom_module/views.py)

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import BOM
from .forms import BOMForm

class BOMListView(ListView):
    """
    Displays a list of all BOM entries.
    """
    model = BOM
    template_name = 'bom_module/bom/list.html'
    context_object_name = 'bills_of_materials'

class BOMTablePartialView(ListView):
    """
    Renders only the DataTables table for BOM entries,
    intended to be loaded via HTMX for dynamic updates.
    """
    model = BOM
    template_name = 'bom_module/bom/_bom_table.html'
    context_object_name = 'bills_of_materials'

class BOMCreateView(CreateView):
    """
    Handles creation of new BOM entries.
    Uses HTMX for form submission and modal interaction.
    """
    model = BOM
    form_class = BOMForm
    template_name = 'bom_module/bom/_bom_form.html' # Use partial template for HTMX
    success_url = reverse_lazy('bom_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Bill of Material added successfully.')
        if self.request.headers.get('HX-Request'):
            # HTMX request: return no content and trigger a list refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBOMList'
                }
            )
        return response

    def form_invalid(self, form):
        """
        Handles invalid form submission for HTMX.
        Returns the form template with errors.
        """
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response
        return response

class BOMUpdateView(UpdateView):
    """
    Handles updating existing BOM entries.
    Uses HTMX for form submission and modal interaction.
    """
    model = BOM
    form_class = BOMForm
    template_name = 'bom_module/bom/_bom_form.html' # Use partial template for HTMX
    success_url = reverse_lazy('bom_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Bill of Material updated successfully.')
        if self.request.headers.get('HX-Request'):
            # HTMX request: return no content and trigger a list refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBOMList'
                }
            )
        return response
    
    def form_invalid(self, form):
        """
        Handles invalid form submission for HTMX.
        Returns the form template with errors.
        """
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response
        return response

class BOMDeleteView(DeleteView):
    """
    Handles deletion of BOM entries.
    Uses HTMX for confirmation and interaction.
    """
    model = BOM
    template_name = 'bom_module/bom/_bom_confirm_delete.html' # Use partial template for HTMX
    success_url = reverse_lazy('bom_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Bill of Material deleted successfully.')
        if request.headers.get('HX-Request'):
            # HTMX request: return no content and trigger a list refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBOMList'
                }
            )
        return response
```

#### 4.4 Templates (bom_module/templates/bom_module/bom/)

##### list.html

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Bills of Materials Dashboard</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out transform hover:scale-105"
            hx-get="{% url 'bom_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i> Add New BOM
        </button>
    </div>
    
    <div id="bomTable-container"
         class="bg-white shadow-lg rounded-lg p-6"
         hx-trigger="load, refreshBOMList from:body"
         hx-get="{% url 'bom_table' %}"
         hx-swap="innerHTML">
        <!-- DataTables table will be loaded here via HTMX -->
        <div class="flex flex-col items-center justify-center h-48">
            <div class="animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Bills of Materials...</p>
        </div>
    </div>
    
    <!-- Universal Modal for CRUD forms -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 my-8 overflow-hidden transform transition-all sm:align-middle sm:max-w-lg sm:w-full"
             _="on load add .scale-95 then transition opacity ease-out duration-300 from-opacity-0 to-opacity-100 then scale-100 ease-out duration-300 from-scale-95 to-scale-100">
            <!-- Modal content (form/confirm_delete) loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('modal', () => ({
            isOpen: false,
            open() { this.isOpen = true },
            close() { this.isOpen = false }
        }));
    });

    // Handle HTMX events for modal close on success
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.xhr.status === 204 && evt.detail.elt.closest('#modalContent')) {
            document.getElementById('modal').classList.remove('is-active');
        }
    });

    // Ensure DataTables is re-initialized after HTMX swap
    document.body.addEventListener('htmx:afterOnLoad', function(evt) {
        if (evt.detail.path.includes('bom/table/')) {
            if ($.fn.DataTable.isDataTable('#bomTable')) {
                $('#bomTable').DataTable().destroy(); // Destroy previous instance
            }
            $('#bomTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "autoWidth": false,
                "responsive": true
            });
        }
    });
</script>
{% endblock %}
```

##### _bom_table.html (Partial)

```html
<div class="overflow-x-auto">
    <table id="bomTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product Code</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Component Code</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit of Measure</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for bom_item in bills_of_materials %}
            <tr class="hover:bg-gray-50">
                <td class="py-4 px-6 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ bom_item.product_code }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ bom_item.component_code }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ bom_item.quantity }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ bom_item.unit_of_measure|default:"N/A" }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-right text-sm font-medium">
                    <button 
                        class="text-indigo-600 hover:text-indigo-900 mr-4 transition duration-300 ease-in-out transform hover:scale-110"
                        hx-get="{% url 'bom_edit' bom_item.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button 
                        class="text-red-600 hover:text-red-900 transition duration-300 ease-in-out transform hover:scale-110"
                        hx-get="{% url 'bom_delete' bom_item.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        <i class="fas fa-trash-alt"></i> Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="text-center py-8 text-gray-500 text-lg">No Bills of Materials found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // DataTable initialization is handled by htmx:afterOnLoad event in list.html
    // This script block is primarily for structure and will be run if this partial is loaded directly.
    // However, the preferred method is the event listener in the parent.
    // If you explicitly need this to run here, ensure jQuery and DataTables are loaded before this partial.
    // $(document).ready(function() {
    //     if ($.fn.DataTable.isDataTable('#bomTable')) {
    //         $('#bomTable').DataTable().destroy();
    //     }
    //     $('#bomTable').DataTable({
    //         "pageLength": 10,
    //         "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
    //     });
    // });
</script>
```

##### _bom_form.html (Partial)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Bill of Material</h3>
    
    <form hx-post="{{ request.path }}" hx-swap="none" hx-target="#modalContent">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="text-red-600 text-xs mt-1 list-disc list-inside">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded-lg transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out transform hover:scale-105">
                Save Bill of Material
            </button>
        </div>
    </form>
</div>
```

##### _bom_confirm_delete.html (Partial)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Bill of Material for <span class="font-bold">{{ object.get_display_name }}</span>?</p>
    <p class="text-sm text-red-600 mb-6">This action cannot be undone.</p>
    
    <form hx-delete="{% url 'bom_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded-lg transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out transform hover:scale-105">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (bom_module/urls.py)

```python
from django.urls import path
from .views import BOMListView, BOMCreateView, BOMUpdateView, BOMDeleteView, BOMTablePartialView

urlpatterns = [
    path('bom/', BOMListView.as_view(), name='bom_list'),
    path('bom/add/', BOMCreateView.as_view(), name='bom_add'),
    path('bom/edit/<int:pk>/', BOMUpdateView.as_view(), name='bom_edit'),
    path('bom/delete/<int:pk>/', BOMDeleteView.as_view(), name='bom_delete'),
    # HTMX-specific endpoint to refresh only the table
    path('bom/table/', BOMTablePartialView.as_view(), name='bom_table'),
]
```
*Remember to include `path('bom_module/', include('bom_module.urls'))` in your project's main `urls.py`.*

#### 4.6 Tests (bom_module/tests.py)

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import BOM
from .forms import BOMForm
from decimal import Decimal

class BOMModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.bom1 = BOM.objects.create(
            product_code='PROD001',
            component_code='COMPA',
            quantity=Decimal('10.50'),
            unit_of_measure='EA'
        )
        cls.bom2 = BOM.objects.create(
            product_code='PROD002',
            component_code='COMPB',
            quantity=Decimal('2.00'),
            unit_of_measure='KG'
        )
  
    def test_bom_creation(self):
        """Test that BOM objects are created correctly."""
        self.assertEqual(self.bom1.product_code, 'PROD001')
        self.assertEqual(self.bom1.component_code, 'COMPA')
        self.assertEqual(self.bom1.quantity, Decimal('10.50'))
        self.assertEqual(self.bom1.unit_of_measure, 'EA')
        self.assertEqual(BOM.objects.count(), 2)

    def test_verbose_name_plural(self):
        """Test the verbose_name_plural for the BOM model."""
        self.assertEqual(BOM._meta.verbose_name_plural, 'Bills of Materials')

    def test_str_method(self):
        """Test the __str__ representation of a BOM object."""
        expected_str = "BOM for PROD001: COMPA (10.50 EA)"
        self.assertEqual(str(self.bom1), expected_str)

        bom_no_uom = BOM.objects.create(
            product_code='PROD003',
            component_code='COMPC',
            quantity=Decimal('1.00')
        )
        expected_str_no_uom = "BOM for PROD003: COMPC (1.00 )"
        self.assertEqual(str(bom_no_uom), expected_str_no_uom)

    def test_get_display_name_method(self):
        """Test the custom get_display_name method."""
        self.assertEqual(self.bom1.get_display_name(), 'PROD001 - COMPA')

    def test_calculate_total_cost_method(self):
        """Test the calculate_total_cost business logic method."""
        # Test with a valid component cost
        cost = self.bom1.calculate_total_cost(Decimal('5.00'))
        self.assertEqual(cost, Decimal('52.50')) # 10.50 * 5.00

        # Test with zero quantity
        bom_zero_qty = BOM.objects.create(
            product_code='PROD004',
            component_code='COMPD',
            quantity=Decimal('0.00'),
            unit_of_measure='EA'
        )
        cost_zero = bom_zero_qty.calculate_total_cost(Decimal('10.00'))
        self.assertEqual(cost_zero, Decimal('0.00'))

        # Test with None quantity or component cost
        bom_none_qty = BOM.objects.create(
            product_code='PROD005',
            component_code='COMPE',
            quantity=None, # Example, though quantity is usually required
            unit_of_measure='EA'
        )
        cost_none_qty = bom_none_qty.calculate_total_cost(Decimal('10.00'))
        self.assertEqual(cost_none_qty, 0.0) # Should return 0.0 as per method

        cost_none_comp_cost = self.bom1.calculate_total_cost(None)
        self.assertEqual(cost_none_comp_cost, 0.0)

class BOMFormTest(TestCase):
    def test_form_valid_data(self):
        """Test form with valid data."""
        form = BOMForm(data={
            'product_code': 'PRODABC',
            'component_code': 'COMPXYZ',
            'quantity': '15.75',
            'unit_of_measure': 'EA'
        })
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['quantity'], Decimal('15.75'))

    def test_form_invalid_quantity(self):
        """Test form with invalid (zero or negative) quantity."""
        form_zero = BOMForm(data={
            'product_code': 'PRODABC',
            'component_code': 'COMPXYZ',
            'quantity': '0.00',
            'unit_of_measure': 'EA'
        })
        self.assertFalse(form_zero.is_valid())
        self.assertIn('quantity', form_zero.errors.keys())
        self.assertIn('Quantity must be a positive number.', form_zero.errors['quantity'])

        form_negative = BOMForm(data={
            'product_code': 'PRODABC',
            'component_code': 'COMPXYZ',
            'quantity': '-5.00',
            'unit_of_measure': 'EA'
        })
        self.assertFalse(form_negative.is_valid())
        self.assertIn('quantity', form_negative.errors.keys())
        self.assertIn('Quantity must be a positive number.', form_negative.errors['quantity'])

    def test_form_missing_required_fields(self):
        """Test form with missing required fields."""
        form = BOMForm(data={
            'product_code': '', # Missing
            'component_code': 'COMPXYZ',
            'quantity': '1.00'
        })
        self.assertFalse(form.is_valid())
        self.assertIn('product_code', form.errors.keys())

class BOMViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.bom_item = BOM.objects.create(
            product_code='PRODVIEW',
            component_code='COMPVIEW',
            quantity=Decimal('5.00'),
            unit_of_measure='PC'
        )
    
    def setUp(self):
        # Client for each test method
        self.client = Client()
    
    def test_list_view_get(self):
        """Test the BOM list view (GET request)."""
        response = self.client.get(reverse('bom_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom_module/bom/list.html')
        self.assertIn('bills_of_materials', response.context)
        self.assertContains(response, self.bom_item.product_code)
        self.assertContains(response, 'Bills of Materials Dashboard')

    def test_table_partial_view_get(self):
        """Test the HTMX partial for the BOM table."""
        response = self.client.get(reverse('bom_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom_module/bom/_bom_table.html')
        self.assertIn('bills_of_materials', response.context)
        self.assertContains(response, self.bom_item.product_code)
        self.assertContains(response, 'id="bomTable"') # Check for DataTables ID

    def test_create_view_get(self):
        """Test the BOM create view (GET request - for modal load)."""
        response = self.client.get(reverse('bom_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom_module/bom/_bom_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Bill of Material')

    def test_create_view_post_success(self):
        """Test creating a new BOM entry (POST request via HTMX)."""
        data = {
            'product_code': 'NEWPROD',
            'component_code': 'NEWCOMP',
            'quantity': '7.5',
            'unit_of_measure': 'LBS'
        }
        response = self.client.post(reverse('bom_add'), data, HTTP_HX_REQUEST='true')
        # HTMX success should return 204 No Content
        self.assertEqual(response.status_code, 204)
        self.assertTrue(BOM.objects.filter(product_code='NEWPROD', component_code='NEWCOMP').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBOMList')

    def test_create_view_post_invalid(self):
        """Test creating a new BOM entry with invalid data (POST request via HTMX)."""
        data = {
            'product_code': 'INVALIDPROD',
            'component_code': 'INVALIDCOMP',
            'quantity': '0.0', # Invalid quantity
            'unit_of_measure': 'EA'
        }
        response = self.client.post(reverse('bom_add'), data, HTTP_HX_REQUEST='true')
        # HTMX invalid form should return 200 OK with form errors
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom_module/bom/_bom_form.html')
        self.assertFalse(BOM.objects.filter(product_code='INVALIDPROD').exists())
        self.assertContains(response, 'Quantity must be a positive number.')

    def test_update_view_get(self):
        """Test the BOM update view (GET request - for modal load)."""
        response = self.client.get(reverse('bom_edit', args=[self.bom_item.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom_module/bom/_bom_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.bom_item)
        self.assertContains(response, 'Edit Bill of Material')

    def test_update_view_post_success(self):
        """Test updating an existing BOM entry (POST request via HTMX)."""
        new_quantity = Decimal('12.34')
        data = {
            'product_code': self.bom_item.product_code,
            'component_code': self.bom_item.component_code,
            'quantity': str(new_quantity), # Form expects string
            'unit_of_measure': 'M'
        }
        response = self.client.post(reverse('bom_edit', args=[self.bom_item.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.bom_item.refresh_from_db()
        self.assertEqual(self.bom_item.quantity, new_quantity)
        self.assertEqual(self.bom_item.unit_of_measure, 'M')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBOMList')

    def test_update_view_post_invalid(self):
        """Test updating an existing BOM entry with invalid data (POST request via HTMX)."""
        old_quantity = self.bom_item.quantity
        data = {
            'product_code': self.bom_item.product_code,
            'component_code': self.bom_item.component_code,
            'quantity': '-1.0', # Invalid quantity
            'unit_of_measure': self.bom_item.unit_of_measure
        }
        response = self.client.post(reverse('bom_edit', args=[self.bom_item.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom_module/bom/_bom_form.html')
        self.bom_item.refresh_from_db()
        self.assertEqual(self.bom_item.quantity, old_quantity) # Ensure quantity wasn't updated
        self.assertContains(response, 'Quantity must be a positive number.')

    def test_delete_view_get(self):
        """Test the BOM delete confirmation view (GET request - for modal load)."""
        response = self.client.get(reverse('bom_delete', args=[self.bom_item.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom_module/bom/_bom_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.bom_item)
        self.assertContains(response, 'Confirm Deletion')

    def test_delete_view_post_success(self):
        """Test deleting an existing BOM entry (DELETE request via HTMX)."""
        # Create an extra item to delete without affecting setUpTestData item
        bom_to_delete = BOM.objects.create(
            product_code='TEMPPROD',
            component_code='TEMPCOMP',
            quantity=Decimal('1.0')
        )
        response = self.client.delete(reverse('bom_delete', args=[bom_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(BOM.objects.filter(pk=bom_to_delete.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBOMList')

    def test_delete_view_post_non_existent(self):
        """Test deleting a non-existent BOM entry."""
        response = self.client.delete(reverse('bom_delete', args=[99999]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 404) # Not Found
```

### Step 5: HTMX and Alpine.js Integration

The generated code demonstrates a robust HTMX and Alpine.js integration:

*   **Dynamic List Updates:** The `list.html` page uses `hx-get` on `bomTable-container` with `hx-trigger="load, refreshBOMList from:body"` to load the table content dynamically. After any CRUD operation (Create, Update, Delete), the views return `HttpResponse(status=204, headers={'HX-Trigger': 'refreshBOMList'})`, which tells HTMX to re-fetch and re-render the `_bom_table.html` partial.
*   **Modal Forms:** "Add New", "Edit", and "Delete" buttons use `hx-get` to fetch the respective partial forms (`_bom_form.html` or `_bom_confirm_delete.html`) into the `#modalContent` div.
*   **Alpine.js for Modals:** The `modal` div in `list.html` uses Alpine.js (via `_` attribute) to toggle its visibility (`is-active` class) based on button clicks and modal background clicks, providing a smooth modal experience.
*   **Form Submission:** Forms within the modal (`_bom_form.html`, `_bom_confirm_delete.html`) use `hx-post` or `hx-delete` with `hx-swap="none"` to submit data. This allows the view to handle the success (return 204 + HX-Trigger) or failure (return 200 with errors, HTMX swaps the form back in) without full page reloads.
*   **DataTables:** The `_bom_table.html` partial includes the JavaScript to initialize DataTables on the `bomTable`. The `htmx:afterOnLoad` event listener in `list.html` ensures DataTables is properly re-initialized each time the table content is refreshed via HTMX, maintaining client-side functionality (search, sort, pagination).

### Final Notes

*   **Placeholders:** `[APP_NAME]`, `[MODEL_NAME]`, etc., have been replaced with `bom_module`, `BOM` (and its variations) based on the inference.
*   **DRY Templates:** The use of `_bom_table.html`, `_bom_form.html`, and `_bom_confirm_delete.html` as partials ensures that common UI elements are not repeated.
*   **Fat Model/Thin View:** The `BOM` model includes a placeholder `calculate_total_cost` method to demonstrate where business logic resides, while views are kept concise, focusing only on request/response handling.
*   **Tests:** Comprehensive tests are provided for both the model's logic and the views' behavior, including HTMX interactions, aiming for high test coverage and ensuring the reliability of the migrated code.
*   **Tailwind CSS:** All generated HTML includes Tailwind CSS classes for a modern, responsive, and consistent UI. Remember to include Tailwind CSS configuration in your project.
*   **Icons:** The templates use Font Awesome icons (`fas fa-plus`, `fas fa-edit`, `fas fa-trash-alt`). Ensure Font Awesome CDN is linked in your `core/base.html`.