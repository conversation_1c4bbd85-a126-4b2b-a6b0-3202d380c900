## ASP.NET to Django Conversion Script: Work Order ECN Module

This document outlines a comprehensive modernization plan to transition your legacy ASP.NET Work Order ECN module to a modern Django-based solution. Our approach emphasizes AI-assisted automation, ensuring a systematic, low-effort migration, while leveraging the latest Django 5.0+ patterns, HTMX for dynamic interactions, Alpine.js for UI state management, and DataTables for superior data presentation. This plan is designed for clarity, focusing on business benefits and actionable steps that can be guided through conversational AI.

### Business Value Proposition

Migrating this Work Order ECN module to Django brings significant advantages:

*   **Enhanced User Experience:** A modern, responsive interface with instant feedback thanks to HTMX and Alpine.js, eliminating full page reloads and providing a smoother user experience.
*   **Improved Performance:** Optimized database interactions via Django ORM and efficient data retrieval, replacing inefficient looping SQL queries, resulting in faster load times and snappier searches.
*   **Reduced Technical Debt:** Moving away from outdated ASP.NET Web Forms to a structured, maintainable Django application with clear separation of concerns (fat models, thin views) drastically lowers future maintenance costs and simplifies new feature development.
*   **Scalability & Modernity:** Django provides a robust foundation for future growth, integration with other modern systems, and access to a vibrant open-source ecosystem, ensuring the application remains relevant and performant.
*   **Simplified Development:** By using Python and Django, your development team will benefit from increased productivity, cleaner code, and access to a vast array of libraries and tools.
*   **Automated Migration Potential:** The structured nature of the plan allows for significant portions of the code generation to be automated, reducing manual coding effort and potential errors.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:

*   NEVER include `base.html` template code in your output - assume it already exists (e.g., `core/base.html`).
*   Focus ONLY on component-specific code for the current module.
*   Always include complete unit tests for models and integration tests for views.
*   Use modern Django 5.0+ patterns and follow best practices.
*   Keep your code clean, efficient, and avoid redundancy.
*   Always generate complete, runnable Django code.

### AutoERP Guidelines:

*   Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
*   Map models to existing database using `managed = False` and `db_table`.
*   Implement DataTables for client-side searching, sorting, and pagination.
*   Use HTMX for dynamic interactions and Alpine.js for UI state management.
*   All templates should extend `core/base.html` (but DO NOT include `base.html` code).
*   Achieve at least 80% test coverage with unit and integration tests.
*   Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
*   Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the ASP.NET code, we identify the following database tables and their inferred columns. The core module revolves around `SD_Cust_WorkOrder_Master`, with several lookup tables for related information.

*   **`SD_Cust_WorkOrder_Master`**:
    *   `Id` (Primary Key, integer)
    *   `EnqId` (Work Order ID, string)
    *   `TaskProjectTitle` (Project Title, string)
    *   `CustomerId` (Foreign Key to `SD_Cust_Master`, string)
    *   `WONo` (Work Order Number, unique identifier, string)
    *   `SessionId` (Foreign Key to `tblHR_OfficeStaff` (EmpId), string)
    *   `FinYearId` (Foreign Key to `tblFinancial_master`, integer)
    *   `SysDate` (System Date / Generation Date, date)
    *   `CloseOpen` (Status, '0' for open, '1' for closed, single char string)
    *   `CompId` (Company ID, integer)
*   **`SD_Cust_Master`**:
    *   `CustomerId` (Primary Key, string)
    *   `CustomerName` (Customer Name, string)
    *   `CompId` (Company ID, integer)
*   **`tblFinancial_master`**:
    *   `FinYearId` (Primary Key, integer)
    *   `FinYear` (Financial Year, string)
*   **`tblHR_OfficeStaff`**:
    *   `EmpId` (Primary Key, string)
    *   `Title` (Title, string)
    *   `EmployeeName` (Employee Name, string)
    *   `CompId` (Company ID, integer)
*   **`tblSD_WO_Category`**:
    *   `CId` (Primary Key, integer)
    *   `Symbol` (Category Symbol, string)
    *   `CName` (Category Name, string)
    *   `CompId` (Company ID, integer)
*   **`tblDG_ECN_Master`**:
    *   `WONo` (Foreign Key to `SD_Cust_WorkOrder_Master` (WONo), string)
    *   `Flag` (Status Flag, integer - '0' implies active/unlocked ECN entries)

### Step 2: Identify Backend Functionality

The ASP.NET page primarily handles **Read** (List/Search) operations for Work Orders.

*   **Read (List & Search):**
    *   Displays a list of `SD_Cust_WorkOrder_Master` records in a `GridView`.
    *   Filtering/searching by:
        *   Customer Name (`TxtSearchValue`)
        *   Work Order Number (`txtEnqId`)
        *   Work Order Category (`DDLTaskWOType`)
    *   Data is filtered based on `FinYearId`, `CompId`, `CloseOpen='0'`, and `WONo` existing in `tblDG_ECN_Master` with `Flag=0`.
    *   The data displayed involves joins to `SD_Cust_Master`, `tblFinancial_master`, and `tblHR_OfficeStaff`.
    *   Pagination is handled by `GridView`.
*   **AutoComplete:**
    *   An `AutoCompleteExtender` provides suggestions for customer names from `SD_Cust_Master`.
*   **Navigation:**
    *   Hyperlink on "WO No" column to `ECNUnlock.aspx?WONo={0}` suggests a detail or modification page for ECN specific to that Work Order.

While this specific page focuses on Read, we will provide standard Django CRUD templates for a comprehensive migration blueprint.

### Step 3: Infer UI Components

The ASP.NET page uses standard Web Forms controls:

*   `asp:DropDownList` (`DropDownList1`): For selecting search criteria (Customer Name / WO No).
*   `asp:TextBox` (`txtEnqId`, `TxtSearchValue`): Text inputs for search values.
*   `cc1:AutoCompleteExtender`: AJAX control for customer name suggestions.
*   `asp:DropDownList` (`DDLTaskWOType`): For selecting Work Order Category.
*   `asp:Button` (`btnSearch`): Triggers the search.
*   `asp:GridView` (`SearchGridView1`): Displays the tabular data with pagination and sorting. This will be replaced by a DataTables implementation in Django.

### Step 4: Generate Django Code

We will create a new Django application, `workorders`, within a logical project structure (e.g., inside a `design` folder, but the actual app name is `workorders`).

#### 4.1 Models

We define Django models mapping directly to the identified database tables. We set `managed = False` and `db_table` to connect to your existing database. We will also include a custom manager for the `WorkOrder` model to encapsulate the complex data retrieval logic.

**`design/workorders/models.py`**

```python
from django.db import models
from django.db.models import F, Q

# Custom Manager for WorkOrder to encapsulate complex query logic
class WorkOrderManager(models.Manager):
    def get_work_orders_for_ecn(self, company_id, fin_year_id, search_type=None, search_value=None, category_id=None):
        """
        Retrieves work orders based on ECN criteria and search parameters.
        Mimics the BindDataCust logic with Django ORM for efficiency.
        """
        # Base query filters
        queryset = self.get_queryset().filter(
            comp_id=company_id,
            close_open='0',  # '0' for open work orders
            fin_year_id__lte=fin_year_id, # Original logic used <=
        )
        
        # Filter by WONo existing in tblDG_ECN_Master where Flag=0
        # This requires a subquery or a direct join if WONo is PK/unique in ECNMaster
        # Assuming WONo in SD_Cust_WorkOrder_Master corresponds to WONo in tblDG_ECN_Master
        queryset = queryset.filter(wono__in=ECNMaster.objects.filter(flag=0).values('wono'))

        # Apply dynamic search filters
        if search_type == '0':  # Customer Name
            if search_value:
                # Assuming fun.getCode extracts CustomerId from 'CustomerName [CustomerId]' format
                # For direct CustomerId, we'll strip ' [ID]' part or expect just ID.
                # If TxtSearchValue comes from autocomplete, it will be in "Name [ID]" format.
                customer_id_part = search_value.split(' [')[-1].replace(']', '')
                queryset = queryset.filter(customer_id=customer_id_part)
        elif search_type == '1':  # WO No
            if search_value:
                queryset = queryset.filter(wono=search_value)

        # Apply WO Category filter
        if category_id and category_id != "WO Category": # "WO Category" is the default "Select" item
            queryset = queryset.filter(wo_category__cid=category_id)

        # Order by WONo ascending
        queryset = queryset.order_by('wono')
        
        # Select related data for display efficiency
        queryset = queryset.select_related('customer', 'financial_year', 'generated_by', 'wo_category')

        return queryset

class Customer(models.Model):
    # CustomerId is the primary key in the source database
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_Master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customer_name

class FinancialYear(models.Model):
    # FinYearId is the primary key in the source database
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=10)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class Employee(models.Model):
    # EmpId is the primary key in the source database
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50)
    title = models.CharField(db_column='Title', max_length=10)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.title}.{self.employee_name}"

class WOCategory(models.Model):
    # CId is the primary key in the source database
    cid = models.IntegerField(db_column='CId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    cname = models.CharField(db_column='CName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblSD_WO_Category'
        verbose_name = 'WO Category'
        verbose_name_plural = 'WO Categories'

    def __str__(self):
        return f"{self.symbol} - {self.cname}"

class ECNMaster(models.Model):
    # WONo is the primary key/unique identifier for ECN entries
    wono = models.CharField(db_column='WONo', primary_key=True, max_length=50)
    flag = models.IntegerField(db_column='Flag') # 0 or 1

    class Meta:
        managed = False
        db_table = 'tblDG_ECN_Master'
        verbose_name = 'ECN Master Entry'
        verbose_name_plural = 'ECN Master Entries'

    def __str__(self):
        return self.wono

class WorkOrder(models.Model):
    # Id is the primary key for SD_Cust_WorkOrder_Master
    id = models.IntegerField(db_column='Id', primary_key=True)
    enq_id = models.CharField(db_column='EnqId', max_length=50, blank=True, null=True)
    task_project_title = models.CharField(db_column='TaskProjectTitle', max_length=255, blank=True, null=True)
    customer = models.ForeignKey(Customer, on_delete=models.DO_NOTHING, db_column='CustomerId', to_field='customer_id', related_name='workorders')
    
    # WONo is a unique identifier, but not the primary key of this table itself
    wo_no = models.CharField(db_column='WONo', max_length=50, unique=True)
    
    generated_by = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='SessionId', to_field='emp_id', related_name='generated_workorders')
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', to_field='fin_year_id', related_name='workorders')
    sys_date = models.DateField(db_column='SysDate')
    close_open = models.CharField(db_column='CloseOpen', max_length=1)
    comp_id = models.IntegerField(db_column='CompId')
    
    # Assuming there's a category ID in SD_Cust_WorkOrder_Master linking to tblSD_WO_Category
    # If not explicitly in the ASP.NET code, this is an assumption for completeness.
    # The DDLTaskWOType refers to CId which is likely stored on the WorkOrder table
    # I'll add a placeholder FK here.
    wo_category = models.ForeignKey(WOCategory, on_delete=models.DO_NOTHING, db_column='CId', to_field='cid', related_name='categorized_workorders', blank=True, null=True)


    objects = WorkOrderManager() # Attach the custom manager

    class Meta:
        managed = False  # Important: Django will not manage this table's creation/alteration
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.wo_no
    
    def get_fin_year_display(self):
        return self.financial_year.fin_year if self.financial_year else 'N/A'

    def get_customer_name_display(self):
        return self.customer.customer_name if self.customer else 'N/A'

    def get_employee_name_display(self):
        return self.generated_by.employee_name if self.generated_by else 'N/A'

    def get_project_title_display(self):
        return self.task_project_title if self.task_project_title else 'N/A'
    
    def get_formatted_sys_date(self):
        return self.sys_date.strftime('%d-%m-%Y') if self.sys_date else 'N/A'
```

#### 4.2 Forms

We define a Django Form for the search functionality. For completeness, a ModelForm for general Work Order CRUD operations is also provided.

**`design/workorders/forms.py`**

```python
from django import forms
from .models import WorkOrder, WOCategory, Customer

class WorkOrderSearchForm(forms.Form):
    SEARCH_TYPE_CHOICES = [
        ('0', 'Customer Name'),
        ('1', 'WO No'),
    ]

    search_by = forms.ChoiceField(
        choices=SEARCH_TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'x-model': 'searchByType'})
    )
    
    # This field is for WO No (txtEnqId) when search_by is '1'
    wo_no_search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter WO No', 'x-show': "searchByType === '1'"})
    )
    
    # This field is for Customer Name (TxtSearchValue) when search_by is '0'
    customer_search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter Customer Name', 'x-show': "searchByType === '0'", 'hx-get': '/workorders/customer-autocomplete/', 'hx-trigger': 'keyup changed delay:500ms', 'hx-target': '#customer-suggestions', 'hx-swap': 'innerHTML'})
    )

    wo_category = forms.ModelChoiceField(
        queryset=WOCategory.objects.all().order_by('cname'), # Fetch categories
        empty_label="WO Category",
        required=False,
        widget=forms.Select(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set initial visibility if needed for Alpine.js state
        if 'search_by' in self.initial and self.initial['search_by'] == '1':
             self.fields['customer_search'].widget.attrs['x-show'] = "false"
             self.fields['wo_no_search'].widget.attrs['x-show'] = "true"
        else:
             self.fields['customer_search'].widget.attrs['x-show'] = "true"
             self.fields['wo_no_search'].widget.attrs['x-show'] = "false"

class WorkOrderForm(forms.ModelForm):
    class Meta:
        model = WorkOrder
        fields = [
            'enq_id', 'task_project_title', 'customer', 'wo_no',
            'generated_by', 'financial_year', 'sys_date', 'close_open',
            'wo_category'
        ]
        widgets = {
            'enq_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'task_project_title': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'customer': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'wo_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'generated_by': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'financial_year': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sys_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'close_open': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'wo_category': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
```

#### 4.3 Views

We will implement the main Work Order list view, a partial view for the DataTables (to be updated via HTMX), a customer autocomplete view, and placeholder CRUD views for general Work Order management.

**`design/workorders/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from django.db.models import Q
from .models import WorkOrder, Customer, WOCategory
from .forms import WorkOrderForm, WorkOrderSearchForm
import json

class WorkOrderListView(ListView):
    """
    Renders the main Work Order ECN list page with search controls.
    """
    model = WorkOrder
    template_name = 'workorders/list.html'
    context_object_name = 'workorders' # Not strictly used as table content is loaded via HTMX

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Populate WO Category dropdown for the initial form render
        # Pass an initial search form for the page load
        context['search_form'] = WorkOrderSearchForm(initial={
            'search_by': self.request.GET.get('search_by', '0'), # Default to Customer Name
            'wo_no_search': self.request.GET.get('wo_no_search', ''),
            'customer_search': self.request.GET.get('customer_search', ''),
            'wo_category': self.request.GET.get('wo_category', ''),
        })
        return context

class WorkOrderTablePartialView(ListView):
    """
    Returns the HTML partial for the Work Orders table, suitable for HTMX swaps.
    This view encapsulates the actual search and data retrieval logic.
    """
    model = WorkOrder
    template_name = 'workorders/_workorder_table.html'
    context_object_name = 'workorders'

    def get_queryset(self):
        # Retrieve session variables, mimicking ASP.NET Session
        # In a real app, these would come from user authentication/profile
        company_id = self.request.session.get('compid', 1) # Default to 1 if not set
        fin_year_id = self.request.session.get('finyear', 2023) # Default to 2023 if not set
        
        search_by = self.request.GET.get('search_by', '0') # Default to Customer Name
        wo_no_search = self.request.GET.get('wo_no_search', '')
        customer_search = self.request.GET.get('customer_search', '')
        wo_category = self.request.GET.get('wo_category', '')

        # Use the custom manager to get filtered queryset
        queryset = WorkOrder.objects.get_work_orders_for_ecn(
            company_id=company_id,
            fin_year_id=fin_year_id,
            search_type=search_by,
            search_value=customer_search if search_by == '0' else wo_no_search,
            category_id=wo_category
        )
        return queryset

class WorkOrderECNDetailView(View):
    """
    Placeholder for the ECN Unlock detail page.
    This view would render ECN specific details for a given Work Order.
    """
    def get(self, request, wono):
        # In a real application, you would fetch ECN details related to this WONo
        # For now, just a simple response.
        return HttpResponse(f"Displaying ECN details for Work Order: {wono}")


class CustomerAutoCompleteView(View):
    """
    Provides customer name suggestions for the autocomplete feature via HTMX.
    Mimics the ASP.NET sql WebMethod.
    """
    def get(self, request):
        prefix_text = request.GET.get('q', '')
        company_id = request.session.get('compid', 1) # Get company ID from session

        if prefix_text:
            # Filter customers by name starting with prefix_text and by company_id
            customers = Customer.objects.filter(
                comp_id=company_id,
                customer_name__istartswith=prefix_text
            ).order_by('customer_name')[:10] # Limit to 10 results, similar to original intent

            suggestions = [
                f"{customer.customer_name} [{customer.customer_id}]"
                for customer in customers
            ]
        else:
            suggestions = []
        
        # HTMX expects a list of suggestions as plain text or JSON for specific targets
        # For simplicity, returning a list of <option> tags for a <datalist> or similar.
        # Or if the target is a <div>, just return list of <div>s.
        # For this example, we'll assume a target div and return suggested <div> elements.
        html_suggestions = "".join([
            f'<div class="p-2 hover:bg-gray-200 cursor-pointer" hx-on:click="document.getElementById(\'id_customer_search\').value=\'{s}\'; document.getElementById(\'customer-suggestions\').innerHTML=\'\';">{s}</div>'
            for s in suggestions
        ])
        return HttpResponse(html_suggestions)

# --- Standard CRUD Views (for general WorkOrder management, not directly from original ECN page) ---

class WorkOrderCreateView(CreateView):
    model = WorkOrder
    form_class = WorkOrderForm
    template_name = 'workorders/_workorder_form.html'
    success_url = reverse_lazy('workorders:workorder_list')

    def form_valid(self, form):
        # Assuming comp_id, fin_year_id, session_id are set by other means (e.g., middleware, user profile)
        # For managed=False models, you might need to manually set these if not coming from form
        # form.instance.comp_id = self.request.session.get('compid', 1) 
        # form.instance.fin_year_id = self.request.session.get('finyear', 2023)
        # form.instance.session_id = self.request.session.get('username', 'default_user_id') 

        response = super().form_valid(form)
        messages.success(self.request, 'Work Order added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, indicates success without navigating
                headers={
                    'HX-Trigger': json.dumps({'refreshWorkOrderList': None, 'closeModal': None})
                }
            )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Add New Work Order'
        return context


class WorkOrderUpdateView(UpdateView):
    model = WorkOrder
    form_class = WorkOrderForm
    template_name = 'workorders/_workorder_form.html'
    success_url = reverse_lazy('workorders:workorder_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Work Order updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': json.dumps({'refreshWorkOrderList': None, 'closeModal': None})
                }
            )
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Edit Work Order'
        return context

class WorkOrderDeleteView(DeleteView):
    model = WorkOrder
    template_name = 'workorders/_workorder_confirm_delete.html'
    success_url = reverse_lazy('workorders:workorder_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Work Order deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': json.dumps({'refreshWorkOrderList': None, 'closeModal': None})
                }
            )
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Confirm Delete'
        return context
```

#### 4.4 Templates

We will define the main list template, a partial for the search form, a partial for the DataTables content, and standard CRUD partials for form and delete confirmation.

**`design/workorders/templates/workorders/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ searchByType: '{{ search_form.search_by.value }}' }">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Work Order ECN List</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'workorders:workorder_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Work Order
        </button>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-lg mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Search Work Orders</h3>
        <form hx-get="{% url 'workorders:workorder_table' %}" hx-target="#workorderTable-container" hx-swap="innerHTML" hx-trigger="submit, change from:#id_search_by, change from:#id_wo_category">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                    <label for="{{ search_form.search_by.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By</label>
                    {{ search_form.search_by }}
                </div>
                <div>
                    <label for="{{ search_form.customer_search.id_for_label }}" class="block text-sm font-medium text-gray-700">Search Value</label>
                    {{ search_form.customer_search }}
                    <div id="customer-suggestions" class="relative bg-white border border-gray-300 rounded-md shadow-lg z-10"></div>
                    {{ search_form.wo_no_search }}
                </div>
                <div>
                    <label for="{{ search_form.wo_category.id_for_label }}" class="block text-sm font-medium text-gray-700">WO Category</label>
                    {{ search_form.wo_category }}
                </div>
            </div>
            <div class="flex justify-end">
                <button type="submit" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Search</button>
            </div>
        </form>
    </div>
    
    <div id="workorderTable-container"
         hx-trigger="load, refreshWorkOrderList from:body"
         hx-get="{% url 'workorders:workorder_table' %}?{{ request.GET.urlencode }}" {# Pass initial query params #}
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Work Orders...</p>
        </div>
    </div>
    
    <!-- Modal for forms -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on closeModal from body remove .is-active from me
            on click if event.target.id == 'modal' remove .is-active from me"
    >
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full max-h-screen overflow-y-auto"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('workOrderPage', () => ({
            searchByType: '{{ search_form.search_by.value }}',
            // Any other Alpine.js state management specific to this page
        }));
    });

    // Event listener for HTMX afterSwap to initialize DataTables
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'workorderTable-container') {
            $('#workorderTable').DataTable({
                "pageLength": 20, // Match original PageSize
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "ordering": true, // Allow sorting
                "searching": true, // Allow client-side search box
                "paging": true, // Allow pagination
            });
        }
    });
</script>
{% endblock %}
```

**`design/workorders/templates/workorders/_workorder_table.html`**

```html
<table id="workorderTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project Title</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% if workorders %}
            {% for wo in workorders %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ wo.get_fin_year_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200">
                    <a href="{% url 'workorders:workorder_ecn_detail' wo.wo_no %}" class="text-blue-600 hover:underline">
                        {{ wo.wo_no }}
                    </a>
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ wo.get_customer_name_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ wo.customer.customer_id }}</td> {# Assuming customer_id is directly accessible #}
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ wo.get_project_title_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ wo.get_formatted_sys_date }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ wo.get_employee_name_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                        hx-get="{% url 'workorders:workorder_edit' wo.id %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                        hx-get="{% url 'workorders:workorder_delete' wo.id %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        {% else %}
        <tr>
            <td colspan="9" class="py-4 text-center font-bold text-red-700">No data to display!</td>
        </tr>
        {% endif %}
    </tbody>
</table>

<script>
    // DataTables initialization is handled in list.html via htmx:afterSwap for robust re-initialization
</script>
```

**`design/workorders/templates/workorders/_workorder_form.html`**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ title }} Work Order</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="text-gray-500 text-xs mt-1">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click send closeModal to body">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`design/workorders/templates/workorders/_workorder_confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ title }}</h3>
    <p class="mb-5">Are you sure you want to delete Work Order <strong>"{{ object.wo_no }}"</strong>?</p>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click send closeModal to body">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

We define URL patterns for all views, including HTMX partials and the autocomplete endpoint.

**`design/workorders/urls.py`**

```python
from django.urls import path
from .views import (
    WorkOrderListView, WorkOrderTablePartialView, WorkOrderCreateView,
    WorkOrderUpdateView, WorkOrderDeleteView, WorkOrderECNDetailView,
    CustomerAutoCompleteView
)

app_name = 'workorders'

urlpatterns = [
    # Main list page
    path('ecn-workorders/', WorkOrderListView.as_view(), name='workorder_list'),
    
    # HTMX endpoint for the DataTables partial (search results)
    path('ecn-workorders/table/', WorkOrderTablePartialView.as_view(), name='workorder_table'),
    
    # Customer autocomplete endpoint
    path('customer-autocomplete/', CustomerAutoCompleteView.as_view(), name='customer_autocomplete'),

    # ECN Detail/Unlock page link
    path('ecn-workorders/detail/<str:wono>/', WorkOrderECNDetailView.as_view(), name='workorder_ecn_detail'),

    # Standard CRUD operations for Work Orders (opened in modal via HTMX)
    path('workorders/add/', WorkOrderCreateView.as_view(), name='workorder_add'),
    path('workorders/edit/<int:pk>/', WorkOrderUpdateView.as_view(), name='workorder_edit'),
    path('workorders/delete/<int:pk>/', WorkOrderDeleteView.as_view(), name='workorder_delete'),
]
```

**Root `urls.py` (e.g., `myproject/urls.py`)**
Include the `workorders` app's URLs:

```python
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('workorders/', include('design.workorders.urls')), # Assuming 'design' is the project structure,
                                                            # or if it's directly in project root, just 'workorders.urls'
]
```

#### 4.6 Tests

Comprehensive tests for models and views ensure code quality and functionality.

**`design/workorders/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import date
from .models import WorkOrder, Customer, FinancialYear, Employee, WOCategory, ECNMaster

class WorkOrderModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary related objects first
        cls.customer = Customer.objects.create(customer_id='CUST001', customer_name='Test Customer', comp_id=1)
        cls.fin_year = FinancialYear.objects.create(fin_year_id=2023, fin_year='2023-24')
        cls.employee = Employee.objects.create(emp_id='EMP001', title='Mr', employee_name='Test Employee', comp_id=1)
        cls.wo_category = WOCategory.objects.create(cid=101, symbol='WO', cname='General WO', comp_id=1)
        ECNMaster.objects.create(wono='WO-2023-001', flag=0) # ECN entry for one WO
        ECNMaster.objects.create(wono='WO-2023-002', flag=1) # ECN entry for another WO, flag=1 (not included)

        # Create test WorkOrder instances
        cls.wo1 = WorkOrder.objects.create(
            id=1, enq_id='ENQ001', task_project_title='Project A',
            customer=cls.customer, wo_no='WO-2023-001', generated_by=cls.employee,
            financial_year=cls.fin_year, sys_date=date(2023, 1, 15),
            close_open='0', comp_id=1, wo_category=cls.wo_category
        )
        cls.wo2 = WorkOrder.objects.create(
            id=2, enq_id='ENQ002', task_project_title='Project B',
            customer=cls.customer, wo_no='WO-2023-002', generated_by=cls.employee,
            financial_year=cls.fin_year, sys_date=date(2023, 2, 20),
            close_open='0', comp_id=1, wo_category=cls.wo_category
        )
        cls.wo3 = WorkOrder.objects.create( # This WO is not in ECNMaster with Flag=0
            id=3, enq_id='ENQ003', task_project_title='Project C',
            customer=cls.customer, wo_no='WO-2023-003', generated_by=cls.employee,
            financial_year=cls.fin_year, sys_date=date(2023, 3, 25),
            close_open='0', comp_id=1, wo_category=cls.wo_category
        )
        cls.wo4 = WorkOrder.objects.create( # Closed WO
            id=4, enq_id='ENQ004', task_project_title='Project D',
            customer=cls.customer, wo_no='WO-2023-004', generated_by=cls.employee,
            financial_year=cls.fin_year, sys_date=date(2023, 4, 1),
            close_open='1', comp_id=1, wo_category=cls.wo_category
        )
        cls.wo5 = WorkOrder.objects.create( # Different company
            id=5, enq_id='ENQ005', task_project_title='Project E',
            customer=cls.customer, wo_no='WO-2023-005', generated_by=cls.employee,
            financial_year=cls.fin_year, sys_date=date(2023, 5, 1),
            close_open='0', comp_id=2, wo_category=cls.wo_category
        )

    def test_workorder_creation(self):
        wo = WorkOrder.objects.get(id=self.wo1.id)
        self.assertEqual(wo.wo_no, 'WO-2023-001')
        self.assertEqual(wo.customer.customer_name, 'Test Customer')
        self.assertEqual(wo.financial_year.fin_year, '2023-24')
        self.assertEqual(wo.generated_by.employee_name, 'Test Employee')

    def test_workorder_display_methods(self):
        wo = WorkOrder.objects.get(id=self.wo1.id)
        self.assertEqual(wo.get_fin_year_display(), '2023-24')
        self.assertEqual(wo.get_customer_name_display(), 'Test Customer')
        self.assertEqual(wo.get_employee_name_display(), 'Test Employee')
        self.assertEqual(wo.get_project_title_display(), 'Project A')
        self.assertEqual(wo.get_formatted_sys_date(), '15-01-2023')

    def test_workorder_manager_filtering_basic(self):
        # Test basic filtering: Only WO-2023-001 should match (flag=0)
        self.client = Client()
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2023
        session.save()

        queryset = WorkOrder.objects.get_work_orders_for_ecn(
            company_id=1, fin_year_id=2023
        )
        self.assertEqual(queryset.count(), 1)
        self.assertEqual(queryset.first().wo_no, 'WO-2023-001')

    def test_workorder_manager_filtering_customer_name(self):
        self.client = Client()
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2023
        session.save()
        
        # Test customer name search, assuming it's passed as 'Name [ID]'
        queryset = WorkOrder.objects.get_work_orders_for_ecn(
            company_id=1, fin_year_id=2023,
            search_type='0', search_value='Test Customer [CUST001]'
        )
        self.assertEqual(queryset.count(), 1)
        self.assertEqual(queryset.first().wo_no, 'WO-2023-001')

    def test_workorder_manager_filtering_wo_no(self):
        self.client = Client()
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2023
        session.save()

        queryset = WorkOrder.objects.get_work_orders_for_ecn(
            company_id=1, fin_year_id=2023,
            search_type='1', search_value='WO-2023-001'
        )
        self.assertEqual(queryset.count(), 1)
        self.assertEqual(queryset.first().wo_no, 'WO-2023-001')
        
    def test_workorder_manager_filtering_wo_category(self):
        self.client = Client()
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2023
        session.save()

        queryset = WorkOrder.objects.get_work_orders_for_ecn(
            company_id=1, fin_year_id=2023,
            category_id=self.wo_category.cid
        )
        self.assertEqual(queryset.count(), 1) # Only WO-2023-001 matches ECN filter
        self.assertEqual(queryset.first().wo_no, 'WO-2023-001')

class WorkOrderViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.customer = Customer.objects.create(customer_id='CUST001', customer_name='Test Customer', comp_id=1)
        cls.fin_year = FinancialYear.objects.create(fin_year_id=2023, fin_year='2023-24')
        cls.employee = Employee.objects.create(emp_id='EMP001', title='Mr', employee_name='Test Employee', comp_id=1)
        cls.wo_category = WOCategory.objects.create(cid=101, symbol='WO', cname='General WO', comp_id=1)
        cls.wo_category_other = WOCategory.objects.create(cid=102, symbol='ECN', cname='ECN Category', comp_id=1)

        ECNMaster.objects.create(wono='WO-2023-001', flag=0)
        ECNMaster.objects.create(wono='WO-2023-006', flag=0) # For create test

        cls.wo1 = WorkOrder.objects.create(
            id=1, enq_id='ENQ001', task_project_title='Project A',
            customer=cls.customer, wo_no='WO-2023-001', generated_by=cls.employee,
            financial_year=cls.fin_year, sys_date=date(2023, 1, 15),
            close_open='0', comp_id=1, wo_category=cls.wo_category
        )
        
    def setUp(self):
        self.client = Client()
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2023
        session['username'] = 'EMP001' # For generated_by field in create test
        session.save()

    def test_list_view_get(self):
        response = self.client.get(reverse('workorders:workorder_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorders/list.html')
        self.assertContains(response, 'Work Order ECN List')
        self.assertIsInstance(response.context['search_form'], WorkOrderSearchForm)

    def test_workorder_table_partial_view_get(self):
        response = self.client.get(reverse('workorders:workorder_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorders/_workorder_table.html')
        self.assertContains(response, 'WO-2023-001') # Check if the WO is in the table

    def test_workorder_table_partial_view_search_wo_no(self):
        response = self.client.get(reverse('workorders:workorder_table'), {
            'search_by': '1',
            'wo_no_search': 'WO-2023-001',
            'customer_search': '', # Ensure other field is empty
            'wo_category': '',
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO-2023-001')
        # Ensure other WOs are not present if only one should match
        # (Assuming only WO-2023-001 matches basic ECN/company/finyear filters)
        self.assertEqual(response.context['workorders'].count(), 1) 

    def test_workorder_table_partial_view_search_customer_name(self):
        response = self.client.get(reverse('workorders:workorder_table'), {
            'search_by': '0',
            'customer_search': 'Test Customer [CUST001]',
            'wo_no_search': '',
            'wo_category': '',
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO-2023-001')
        self.assertEqual(response.context['workorders'].count(), 1)

    def test_workorder_table_partial_view_search_category(self):
        response = self.client.get(reverse('workorders:workorder_table'), {
            'search_by': '0', # Doesn't matter for category filter only
            'customer_search': '',
            'wo_no_search': '',
            'wo_category': self.wo_category.cid,
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO-2023-001')
        self.assertEqual(response.context['workorders'].count(), 1)

    def test_customer_autocomplete_view(self):
        response = self.client.get(reverse('workorders:customer_autocomplete'), {'q': 'Test'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Customer [CUST001]')

    def test_create_view_get(self):
        response = self.client.get(reverse('workorders:workorder_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorders/_workorder_form.html')
        self.assertContains(response, 'Add New Work Order')
        self.assertIsInstance(response.context['form'], WorkOrderForm)

    def test_create_view_post(self):
        data = {
            'enq_id': 'NEWENQ001',
            'task_project_title': 'New Project',
            'customer': self.customer.customer_id,
            'wo_no': 'WO-2023-006', # Must be unique, and in ECNMaster for list view to show it
            'generated_by': self.employee.emp_id,
            'financial_year': self.fin_year.fin_year_id,
            'sys_date': '2023-06-01',
            'close_open': '0',
            'comp_id': 1,
            'wo_category': self.wo_category.cid,
        }
        response = self.client.post(reverse('workorders:workorder_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertTrue(WorkOrder.objects.filter(wo_no='WO-2023-006').exists())
        # Check HX-Trigger header
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshWorkOrderList', response.headers['HX-Trigger'])

    def test_update_view_get(self):
        response = self.client.get(reverse('workorders:workorder_edit', args=[self.wo1.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorders/_workorder_form.html')
        self.assertContains(response, 'Edit Work Order')
        self.assertIsInstance(response.context['form'], WorkOrderForm)
        self.assertEqual(response.context['form'].instance.wo_no, 'WO-2023-001')

    def test_update_view_post(self):
        updated_title = 'Updated Project A'
        data = {
            'enq_id': self.wo1.enq_id,
            'task_project_title': updated_title,
            'customer': self.wo1.customer.customer_id,
            'wo_no': self.wo1.wo_no,
            'generated_by': self.wo1.generated_by.emp_id,
            'financial_year': self.wo1.financial_year.fin_year_id,
            'sys_date': self.wo1.sys_date.strftime('%Y-%m-%d'),
            'close_open': self.wo1.close_open,
            'comp_id': self.wo1.comp_id,
            'wo_category': self.wo1.wo_category.cid,
        }
        response = self.client.post(reverse('workorders:workorder_edit', args=[self.wo1.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.wo1.refresh_from_db()
        self.assertEqual(self.wo1.task_project_title, updated_title)
        self.assertIn('HX-Trigger', response.headers)

    def test_delete_view_get(self):
        response = self.client.get(reverse('workorders:workorder_delete', args=[self.wo1.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorders/_workorder_confirm_delete.html')
        self.assertContains(response, f'delete Work Order "WO-2023-001"')

    def test_delete_view_post(self):
        wo_id = self.wo1.id
        response = self.client.post(reverse('workorders:workorder_delete', args=[wo_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(WorkOrder.objects.filter(id=wo_id).exists())
        self.assertIn('HX-Trigger', response.headers)

    def test_workorder_ecn_detail_view(self):
        response = self.client.get(reverse('workorders:workorder_ecn_detail', args=['WO-2023-001']))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Displaying ECN details for Work Order: WO-2023-001')
```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for Search & Table Refresh:** The main search form uses `hx-get` to `{% url 'workorders:workorder_table' %}` to load the table content dynamically without a full page refresh. `hx-target="#workorderTable-container"` ensures only the table area is updated. `hx-trigger="submit, change from:#id_search_by, change from:#id_wo_category"` ensures the search is triggered not only by the search button but also by changes in the search type dropdown or category dropdown.
*   **HTMX for Modals (CRUD):** The "Add New", "Edit", and "Delete" buttons use `hx-get` to load their respective forms (`_workorder_form.html`, `_workorder_confirm_delete.html`) into a modal container (`#modalContent`). The `_="on click add .is-active to #modal"` Alpine.js/Hyperscript snippet ensures the modal becomes visible.
*   **HTMX for Form Submission:** The forms within the modals use `hx-post="{{ request.path }}" hx-swap="none"`. Upon successful submission (HTTP 204 No Content), the server responds with an `HX-Trigger` header (`refreshWorkOrderList`, `closeModal`).
    *   `refreshWorkOrderList`: This triggers the `hx-trigger="load, refreshWorkOrderList from:body"` on the `#workorderTable-container`, causing the table to reload and display the updated data.
    *   `closeModal`: This custom event is caught by the `div#modal` with `_="on closeModal from body remove .is-active from me"`, effectively hiding the modal.
*   **HTMX for Autocomplete:** The `customer_search` input field uses `hx-get` to `{% url 'workorders:customer_autocomplete' %}` with `hx-trigger="keyup changed delay:500ms"` to fetch suggestions. `hx-target="#customer-suggestions"` and `hx-swap="innerHTML"` replace the content of a suggestion container below the input field. A simple `hx-on:click` on the suggestion items then populates the input field and clears the suggestions.
*   **Alpine.js for UI State:** `x-data="{ searchByType: '{{ search_form.search_by.value }}' }"` is used on the main container to manage the `searchByType` state. The `x-show` directives on `wo_no_search` and `customer_search` inputs conditionally hide/show them based on `searchByType`'s value, replicating the ASP.NET `Visible` property changes.
*   **DataTables Integration:** The `_workorder_table.html` partial contains the `<table>` element. The `htmx:afterSwap` event listener in `list.html` ensures that DataTables is re-initialized *after* the new table content has been loaded into the DOM by HTMX. This guarantees correct pagination, sorting, and client-side searching.

### Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating your ASP.NET Work Order ECN module to Django. By leveraging AI-assisted automation, adhering to modern best practices, and focusing on user experience with HTMX and Alpine.js, you will achieve a robust, maintainable, and high-performing application that is ready for future development and integrations. Remember to configure your Django project's `settings.py` for database connection, static files, and include `django.contrib.messages` for success/error notifications.