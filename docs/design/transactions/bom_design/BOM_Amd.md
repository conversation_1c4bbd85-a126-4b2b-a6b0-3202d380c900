## ASP.NET to Django Conversion Script: BOM Item - Amendment

This document outlines the strategic transition of your legacy ASP.NET BOM Item Amendment display module to a modern, maintainable, and scalable Django application. Our approach prioritizes AI-assisted automation, leveraging Django's robust ORM, HTMX for dynamic interactions, Alpine.js for lightweight frontend logic, and DataTables for enhanced data presentation. This modernization will result in a more efficient, user-friendly system, significantly reducing future maintenance costs and improving overall application performance.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code interacts with multiple tables to compile the BOM amendment list.

*   **Primary Table:** `tblDG_BOM_Amd` (for amendment details)
*   **Related Tables (Lookups):**
    *   `tblDG_Item_Master` (for Item Code, Description, UOM Basic)
    *   `Unit_Master` (for UOM Symbol)
    *   `tblHR_OfficeStaff` (for Employee Name who amended)

**Inferred Schema:**

**`tblDG_BOM_Amd`:**
*   `Id` (int, primary key)
*   `SysDate` (date/datetime, used for display)
*   `SysTime` (time/datetime, used for display)
*   `WONo` (string)
*   `AmdNo` (string)
*   `ItemId` (int, foreign key to `tblDG_Item_Master.Id`)
*   `Description` (string, optional override for item description)
*   `UOM` (int, optional override for UOM, foreign key to `Unit_Master.Id`)
*   `Qty` (decimal/numeric)
*   `SessionId` (int, foreign key to `tblHR_OfficeStaff.EmpId`, represents 'Amd by')
*   `FinYearId` (int)
*   `CompId` (int)

**`tblDG_Item_Master`:**
*   `Id` (int, primary key)
*   `ItemCode` (string)
*   `UOMBasic` (int, foreign key to `Unit_Master.Id`)
*   `ManfDesc` (string, manufacturer description)
*   `FinYearId` (int)
*   `CompId` (int)

**`Unit_Master`:**
*   `Id` (int, primary key)
*   `Symbol` (string)

**`tblHR_OfficeStaff`:**
*   `EmpId` (int, primary key)
*   `Title` (string)
*   `EmployeeName` (string)
*   `FinYearId` (int)
*   `CompId` (int)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Analysis:**
The ASP.NET page `BOM_Amd.aspx` is primarily a **Read (List/Display)** operation.

*   **Create:** No explicit create functionality.
*   **Read:** The `GridView2` is populated with data from `tblDG_BOM_Amd` and joined tables. Filtering is applied based on `FinYearId`, `CompId`, `BOMId`, `WONo`, and `ItemId` from session and query string. The `BindDataCust` method performs complex joins and data transformation for display.
*   **Update:** No explicit update functionality.
*   **Delete:** No explicit delete functionality.
*   **Navigation:** A "Cancel" button redirects the user to another page (`BOM_Design_WO_TreeView_Edit.aspx`).

**Conclusion:** This module will focus solely on displaying the BOM amendment list.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**

*   **`asp:Label ID="lblWONo"`:** Displays the Work Order number. This will be a simple context variable in Django template.
*   **`asp:GridView ID="GridView2"`:** This is the core data display component. It features:
    *   AutoGenerateColumns="False": Columns are explicitly defined.
    *   DataKeyNames="Id": Row identification.
    *   OnPageIndexChanging: Server-side pagination.
    *   PageSize="17": Number of rows per page.
    *   AllowPaging="True": Enables pagination.
    *   Columns: Includes SN (row number), SysDate, SysTime, WONo, Id (hidden), AmdNo, ItemCode, Description, UOM, Qty, Amd by.
    *   EmptyDataTemplate: Message when no data is found.
*   **`asp:Button ID="Button2"`:** "Cancel" button. This will be replaced by a simple HTML anchor tag or a button with `hx-redirect` in Django.

**Conclusion:** The `GridView`'s functionality will be migrated to a DataTables implementation in the Django template, loaded dynamically via HTMX. The overall page structure will be maintained using Tailwind CSS.

### Step 4: Generate Django Code

Given the analysis, we will create the following Django application structure:

**App Name:** `bomamendment`

#### 4.1 Models (`bomamendment/models.py`)

We need models for the primary `tblDG_BOM_Amd` and its related lookup tables. The complex data transformation logic from `BindDataCust` will be encapsulated as properties within the `BOMAmendment` model to adhere to the "Fat Model, Thin View" principle.

```python
from django.db import models
from django.utils import timezone

class UnitMaster(models.Model):
    """
    Maps to Unit_Master table for UOM symbols.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class ItemMaster(models.Model):
    """
    Maps to tblDG_Item_Master table for item details.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=100)
    uom_basic = models.ForeignKey(UnitMaster, on_delete=models.DO_NOTHING, db_column='UOMBasic', related_name='basic_items', null=True, blank=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255, null=True, blank=True)
    fin_year_id = models.IntegerField(db_column='FinYearId')
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.item_code

class OfficeStaff(models.Model):
    """
    Maps to tblHR_OfficeStaff table for employee details.
    """
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    title = models.CharField(db_column='Title', max_length=50, null=True, blank=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    fin_year_id = models.IntegerField(db_column='FinYearId')
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title}.{self.employee_name}"

class BOMAmendment(models.Model):
    """
    Maps to tblDG_BOM_Amd table for BOM amendments.
    Includes properties to encapsulate complex display logic from ASP.NET.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    wo_no = models.CharField(db_column='WONo', max_length=50)
    amd_no = models.CharField(db_column='AmdNo', max_length=50)
    item = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId', related_name='bom_amendments')
    description_override = models.CharField(db_column='Description', max_length=255, null=True, blank=True)
    uom_override = models.ForeignKey(UnitMaster, on_delete=models.DO_NOTHING, db_column='UOM', related_name='bom_amendments_uom_override', null=True, blank=True)
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=4)
    session_id = models.ForeignKey(OfficeStaff, on_delete=models.DO_NOTHING, db_column='SessionId', related_name='amended_bom_entries')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Amd'
        verbose_name = 'BOM Amendment'
        verbose_name_plural = 'BOM Amendments'

    def __str__(self):
        return f"BOM Amd No: {self.amd_no} for WO: {self.wo_no}"

    @property
    def display_sys_date(self):
        """Formats SysDate as DD/MM/YYYY similar to fun.FromDateDMY."""
        return self.sys_date.strftime('%d/%m/%Y')

    @property
    def display_description(self):
        """
        Returns description from override field or item master.
        Equivalent to: if (DSAmd.Tables[0].Rows[i]["Description"].ToString() != string.Empty) ... else ...
        """
        return self.description_override if self.description_override else (self.item.manf_desc if self.item else '')

    @property
    def display_uom_symbol(self):
        """
        Returns UOM symbol from override field or item master's basic UOM.
        Equivalent to: if (DSAmd.Tables[0].Rows[i]["UOM"].ToString() != string.Empty) ... else ...
        """
        if self.uom_override:
            return self.uom_override.symbol
        elif self.item and self.item.uom_basic:
            return self.item.uom_basic.symbol
        return ''

    @property
    def amended_by_name(self):
        """
        Returns the full name of the employee who made the amendment.
        Equivalent to: Title + '.' + EmployeeName from tblHR_OfficeStaff.
        """
        return str(self.session_id) if self.session_id else ''

```

#### 4.2 Forms (`bomamendment/forms.py`)

No explicit form is needed for this page as it's a display-only view. If search/filter functionality were added, a `Form` (not a `ModelForm`) would be created.

```python
# No forms.py needed for this specific page, as it's a read-only view.
# If you were to add search/filter capabilities, a simple forms.Form would go here.
```

#### 4.3 Views (`bomamendment/views.py`)

We will use a `ListView` for the main page and a separate `TemplateView` to render the DataTables partial, which will be requested via HTMX. This keeps the main `ListView` simple and allows the table to be refreshed independently.

```python
from django.views.generic import ListView, TemplateView
from django.urls import reverse_lazy
from django.db.models import Q # For complex queries
from django.http import HttpResponseBadRequest
from .models import BOMAmendment, ItemMaster, UnitMaster, OfficeStaff

class BOMAmendmentListView(ListView):
    """
    Displays the main page for BOM Amendment list.
    The actual table content is loaded via HTMX.
    """
    model = BOMAmendment
    template_name = 'bomamendment/list.html'
    context_object_name = 'bom_amendments' # Not directly used in template, as table is HTMX loaded.

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Retrieve parameters from query string, similar to ASP.NET Request.QueryString
        context['wo_no'] = self.request.GET.get('WONo', 'N/A')
        # BOMId and ItemId might be needed for dynamic filtering if implemented later
        # context['bom_id'] = self.request.GET.get('Id')
        # context['item_id'] = self.request.GET.get('ItemId')
        return context

class BOMAmendmentTablePartialView(TemplateView):
    """
    Renders the partial HTML for the BOM Amendment DataTables,
    designed to be loaded via HTMX.
    """
    template_name = 'bomamendment/_bomamendment_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Simulate ASP.NET Session and QueryString parameters
        # In a real app, these would come from authenticated user session and URL
        comp_id = self.request.session.get('compid', 1)  # Defaulting for example
        fin_year_id = self.request.session.get('finyear', 2023) # Defaulting for example
        
        wo_no = self.request.GET.get('WONo')
        item_id_param = self.request.GET.get('ItemId')
        bom_id_param = self.request.GET.get('Id')

        if not all([wo_no, item_id_param, bom_id_param]):
            # In a real app, handle missing params more gracefully, e.g., redirect or error page
            # For this example, we'll return an empty list or an error indication.
            context['bom_amendments'] = BOMAmendment.objects.none()
            context['error_message'] = "Missing required query parameters (WONo, ItemId, Id)."
            return context

        try:
            item_id = int(item_id_param)
            bom_id = int(bom_id_param)
        except (ValueError, TypeError):
            context['bom_amendments'] = BOMAmendment.objects.none()
            context['error_message'] = "Invalid ItemId or Id parameter."
            return context

        # Replicate ASP.NET's BindDataCust logic with Django ORM for efficiency
        # Using select_related to avoid N+1 query problem
        queryset = BOMAmendment.objects.select_related(
            'item', 
            'item__uom_basic', # To access ItemMaster's UOMBasic symbol
            'uom_override', # To access BOMAmendment's UOM override symbol
            'session_id' # To access OfficeStaff details
        ).filter(
            fin_year_id__lte=fin_year_id, # ASP.NET uses <=
            comp_id=comp_id,
            wo_no=wo_no,
            item__id=item_id, # Filter by item__id instead of raw ItemId
            id=bom_id # Filter by primary key 'Id' of tblDG_BOM_Amd
        ).order_by('sys_date', 'sys_time') # Order as needed

        context['bom_amendments'] = queryset
        return context

```

#### 4.4 Templates

**`bomamendment/list.html`**
This is the main page that extends `core/base.html` and uses HTMX to load the DataTables content.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">BOM Item - Amendment</h2>
        <div class="flex items-center space-x-2 bg-gray-100 p-2 rounded-md border border-gray-200">
            <span class="text-sm font-semibold text-gray-600">WO No:</span>
            <span class="text-lg font-bold text-blue-700">{{ wo_no }}</span>
        </div>
    </div>
    
    <div id="bomamendment-table-container"
         hx-trigger="load"
         hx-get="{% url 'bomamendment_table' %}?{{ request.GET.urlencode }}" {# Pass all query params to the HTMX call #}
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg overflow-hidden">
        <!-- Loading spinner while content is fetched -->
        <div class="p-6 text-center">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading BOM Amendments...</p>
        </div>
    </div>
    
    <div class="mt-6 flex justify-center">
        <a href="{% url 'bom_design_wo_tree_view_edit' %}?WONo={{ wo_no }}&ModId=3&SubModId=26" 
           class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-6 rounded-lg shadow-md transition duration-200 ease-in-out">
            Cancel
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js is not strictly necessary for this read-only view,
        // but can be used for minor UI enhancements or future additions.
    });
</script>
{% endblock %}
```

**`bomamendment/_bomamendment_table.html`**
This partial template contains the actual DataTables structure and is loaded by HTMX.

```html
{% if bom_amendments %}
<table id="bomAmendmentTable" class="min-w-full bg-white border-collapse">
    <thead>
        <tr>
            <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Date</th>
            <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Time</th>
            <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">WO No</th>
            <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Amendment No</th>
            <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Item Code</th>
            <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Description</th>
            <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">UOM</th>
            <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Qty</th>
            <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Amd by</th>
        </tr>
    </thead>
    <tbody>
        {% for amendment in bom_amendments %}
        <tr class="hover:bg-gray-50">
            <td class="py-3 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-center">{{ amendment.display_sys_date }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-center">{{ amendment.sys_time|date:"H:i:s" }}</td> {# Format time #}
            <td class="py-3 px-4 border-b border-gray-200 text-center">{{ amendment.wo_no }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-center">{{ amendment.amd_no }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-center">{{ amendment.item.item_code }}</td>
            <td class="py-3 px-4 border-b border-gray-200">{{ amendment.display_description }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-center">{{ amendment.display_uom_symbol }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-right">{{ amendment.qty }}</td>
            <td class="py-3 px-4 border-b border-gray-200">{{ amendment.amended_by_name }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    // Destroy existing DataTable instance if it exists to prevent re-initialization errors on HTMX swap
    if ($.fn.DataTable.isDataTable('#bomAmendmentTable')) {
        $('#bomAmendmentTable').DataTable().destroy();
    }
    
    $('#bomAmendmentTable').DataTable({
        "pageLength": 17, // Replicate ASP.NET GridView's PageSize
        "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
        "pagingType": "full_numbers", // Example paging type
        "responsive": true, // Make table responsive
    });
});
</script>
{% else %}
    <div class="p-6 text-center">
        <p class="text-xl text-maroon font-semibold">No data to display !</p>
        {% if error_message %}
            <p class="text-red-500 text-sm mt-2">{{ error_message }}</p>
        {% endif %}
    </div>
{% endif %}
```

#### 4.5 URLs (`bomamendment/urls.py`)

```python
from django.urls import path
from .views import BOMAmendmentListView, BOMAmendmentTablePartialView

urlpatterns = [
    # Main page URL: e.g., /bom-amendments/?WONo=XYZ&ItemId=123&Id=456
    path('bom-amendments/', BOMAmendmentListView.as_view(), name='bom_amendment_list'),
    
    # HTMX endpoint for the DataTables content: e.g., /bom-amendments/table/?WONo=XYZ&ItemId=123&Id=456
    path('bom-amendments/table/', BOMAmendmentTablePartialView.as_view(), name='bomamendment_table'),

    # Placeholder for the "Cancel" button's redirect URL.
    # In a real scenario, map this to an actual view/URL.
    path('bom-design-wo-tree-view-edit/', lambda request: None, name='bom_design_wo_tree_view_edit'), # Dummy view for redirection target
]
```

#### 4.6 Tests (`bomamendment/tests.py`)

Comprehensive tests for models and views, covering data integrity, property methods, and view behavior.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import BOMAmendment, ItemMaster, UnitMaster, OfficeStaff
from datetime import date, time

class BOMAmendmentModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all related models first
        cls.unit_kg = UnitMaster.objects.create(id=1, symbol='Kg')
        cls.unit_mt = UnitMaster.objects.create(id=2, symbol='Mt')
        
        cls.item1 = ItemMaster.objects.create(id=101, item_code='ITEM-001', uom_basic=cls.unit_kg, manf_desc='Description for Item 1', fin_year_id=2023, comp_id=1)
        cls.item2 = ItemMaster.objects.create(id=102, item_code='ITEM-002', uom_basic=cls.unit_mt, manf_desc='Description for Item 2', fin_year_id=2023, comp_id=1)

        cls.staff1 = OfficeStaff.objects.create(emp_id=1, title='Mr', employee_name='John Doe', fin_year_id=2023, comp_id=1)
        cls.staff2 = OfficeStaff.objects.create(emp_id=2, title='Ms', employee_name='Jane Smith', fin_year_id=2023, comp_id=1)

        # Create test data for BOMAmendment
        cls.amd1 = BOMAmendment.objects.create(
            id=1, sys_date=date(2023, 1, 15), sys_time=time(10, 30, 0),
            wo_no='WO-001', amd_no='AMD-001', item=cls.item1, 
            description_override=None, uom_override=None, qty=100.50,
            session_id=cls.staff1, fin_year_id=2023, comp_id=1
        )
        cls.amd2 = BOMAmendment.objects.create(
            id=2, sys_date=date(2023, 1, 16), sys_time=time(11, 0, 0),
            wo_no='WO-001', amd_no='AMD-002', item=cls.item2, 
            description_override='Custom Description for AMD-002', uom_override=cls.unit_kg, qty=50.75,
            session_id=cls.staff2, fin_year_id=2023, comp_id=1
        )
        cls.amd3 = BOMAmendment.objects.create( # Different WO for testing filtering
            id=3, sys_date=date(2023, 1, 17), sys_time=time(12, 0, 0),
            wo_no='WO-002', amd_no='AMD-003', item=cls.item1, 
            description_override=None, uom_override=None, qty=25.00,
            session_id=cls.staff1, fin_year_id=2023, comp_id=1
        )
  
    def test_bom_amendment_creation(self):
        amd = BOMAmendment.objects.get(id=1)
        self.assertEqual(amd.wo_no, 'WO-001')
        self.assertEqual(amd.item.item_code, 'ITEM-001')
        self.assertEqual(amd.qty, 100.50)
        self.assertEqual(amd.session_id.employee_name, 'John Doe')

    def test_display_sys_date_property(self):
        amd = BOMAmendment.objects.get(id=1)
        self.assertEqual(amd.display_sys_date, '15/01/2023')

    def test_display_description_property(self):
        amd1 = BOMAmendment.objects.get(id=1) # No override
        amd2 = BOMAmendment.objects.get(id=2) # With override
        self.assertEqual(amd1.display_description, 'Description for Item 1')
        self.assertEqual(amd2.display_description, 'Custom Description for AMD-002')

    def test_display_uom_symbol_property(self):
        amd1 = BOMAmendment.objects.get(id=1) # No override, uses item basic UOM
        amd2 = BOMAmendment.objects.get(id=2) # With override
        self.assertEqual(amd1.display_uom_symbol, 'Kg')
        self.assertEqual(amd2.display_uom_symbol, 'Kg') # UOM_override is Kg

    def test_amended_by_name_property(self):
        amd1 = BOMAmendment.objects.get(id=1)
        amd2 = BOMAmendment.objects.get(id=2)
        self.assertEqual(amd1.amended_by_name, 'Mr.John Doe')
        self.assertEqual(amd2.amended_by_name, 'Ms.Jane Smith')

class BOMAmendmentViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all related models for views testing
        cls.unit_kg = UnitMaster.objects.create(id=1, symbol='Kg')
        cls.unit_mt = UnitMaster.objects.create(id=2, symbol='Mt')
        
        cls.item1 = ItemMaster.objects.create(id=101, item_code='ITEM-001', uom_basic=cls.unit_kg, manf_desc='Description for Item 1', fin_year_id=2023, comp_id=1)
        cls.item2 = ItemMaster.objects.create(id=102, item_code='ITEM-002', uom_basic=cls.unit_mt, manf_desc='Description for Item 2', fin_year_id=2023, comp_id=1)

        cls.staff1 = OfficeStaff.objects.create(emp_id=1, title='Mr', employee_name='John Doe', fin_year_id=2023, comp_id=1)
        
        cls.amd1 = BOMAmendment.objects.create(
            id=1, sys_date=date(2023, 1, 15), sys_time=time(10, 30, 0),
            wo_no='WO-TEST-001', amd_no='AMD-A', item=cls.item1, 
            description_override=None, uom_override=None, qty=100.00,
            session_id=cls.staff1, fin_year_id=2023, comp_id=1
        )
        cls.amd2 = BOMAmendment.objects.create(
            id=2, sys_date=date(2023, 1, 16), sys_time=time(11, 0, 0),
            wo_no='WO-TEST-001', amd_no='AMD-B', item=cls.item2, 
            description_override='Override Desc', uom_override=cls.unit_kg, qty=50.00,
            session_id=cls.staff1, fin_year_id=2023, comp_id=1
        )
        # Set up session defaults (e.g., compid, finyear)
        # In a real application, you'd use a user login or mock session
        cls.client = Client()
        session = cls.client.session
        session['compid'] = 1
        session['finyear'] = 2023
        session.save()
    
    def test_list_view_get(self):
        # Test main list view (loads container for HTMX table)
        response = self.client.get(reverse('bom_amendment_list'), 
                                   {'WONo': 'WO-TEST-001', 'ItemId': '101', 'Id': '1'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bomamendment/list.html')
        self.assertContains(response, 'WO-TEST-001') # Check if WO No is displayed

    def test_table_partial_view_htmx(self):
        # Test HTMX loaded table partial
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('bomamendment_table'), 
                                   {'WONo': 'WO-TEST-001', 'ItemId': '101', 'Id': '1'}, 
                                   **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bomamendment/_bomamendment_table.html')
        
        # Check if correct data is displayed
        self.assertContains(response, 'WO-TEST-001')
        self.assertContains(response, 'AMD-A')
        self.assertContains(response, 'ITEM-001')
        self.assertContains(response, 'Description for Item 1') # From item master
        self.assertContains(response, 'Kg') # From item basic UOM
        self.assertContains(response, '100.00') # Qty
        self.assertContains(response, 'Mr.John Doe') # Amd by name

        # Check for AMD-B specific details, which has overrides
        response_amd2 = self.client.get(reverse('bomamendment_table'), 
                                        {'WONo': 'WO-TEST-001', 'ItemId': '102', 'Id': '2'}, 
                                        **headers)
        self.assertContains(response_amd2, 'AMD-B')
        self.assertContains(response_amd2, 'ITEM-002')
        self.assertContains(response_amd2, 'Override Desc') # Check for override description
        self.assertContains(response_amd2, 'Kg') # Check for override UOM
        self.assertContains(response_amd2, '50.00')

    def test_table_partial_view_no_data(self):
        # Test case where no data matches the filter
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('bomamendment_table'), 
                                   {'WONo': 'NON_EXISTENT_WO', 'ItemId': '999', 'Id': '999'}, 
                                   **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bomamendment/_bomamendment_table.html')
        self.assertContains(response, 'No data to display !')

    def test_table_partial_view_missing_params(self):
        # Test behavior with missing query parameters
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('bomamendment_table'), 
                                   {'WONo': 'WO-001'}, # Missing ItemId and Id
                                   **headers)
        self.assertEqual(response.status_code, 200) # Still 200, but template shows error
        self.assertContains(response, 'No data to display !')
        self.assertContains(response, 'Missing required query parameters')

    def test_table_partial_view_invalid_params(self):
        # Test behavior with invalid numeric parameters
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('bomamendment_table'), 
                                   {'WONo': 'WO-001', 'ItemId': 'invalid', 'Id': '1'}, 
                                   **headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No data to display !')
        self.assertContains(response, 'Invalid ItemId or Id parameter.')

    def test_cancel_button_redirection(self):
        # Test the "Cancel" button link, assuming a dummy URL for now.
        response = self.client.get(reverse('bom_amendment_list'), {'WONo': 'WO-001', 'ItemId': '101', 'Id': '1'})
        self.assertContains(response, reverse('bom_design_wo_tree_view_edit') + '?WONo=WO-001&amp;ModId=3&amp;SubModId=26')

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for DataTables Loading:** The `bomamendment/list.html` uses `hx-get` to fetch the `_bomamendment_table.html` partial on page load. This means the entire table content, including its data and DataTables initialization script, is loaded separately.
*   **DataTables Initialization:** The JavaScript for DataTables initialization is embedded directly within `_bomamendment_table.html`. This ensures that DataTables is initialized *after* the HTML table content has been loaded into the DOM by HTMX.
*   **No CRUD on this page:** Since this is a display-only page, there are no HTMX triggers for forms or deletions. If this page were to become interactive, HTMX would be used for modal forms, form submissions, and table refreshes (using `HX-Trigger`).
*   **Alpine.js:** While not strictly needed for this simple display, Alpine.js is included in the `base.html` (assumed) and could be used for minor client-side UI states, e.g., showing a loading spinner more dynamically or managing a simple modal if future features required it without full HTMX requests. For this specific scope, its explicit use is minimal.
*   **URL Parameters:** The `request.GET.urlencode` in the `hx-get` ensures that all necessary query parameters (WONo, ItemId, Id) are forwarded from the main page request to the HTMX request for the table partial, maintaining the filtering context.

### Final Notes

This comprehensive plan transforms the ASP.NET BOM Amendment display into a modern Django application.

*   **Business Benefits:**
    *   **Improved Performance:** Efficient Django ORM queries with `select_related` replace inefficient N+1 queries, significantly speeding up data retrieval.
    *   **Enhanced User Experience:** DataTables provides client-side sorting, searching, and pagination, offering a highly interactive and responsive grid without full page reloads. HTMX ensures a smooth, app-like feel.
    *   **Reduced Development Time:** Django's conventions, HTMX's simplicity, and the 'Fat Model, Thin View' pattern lead to faster feature development and easier onboarding for new developers.
    *   **Increased Maintainability:** A clear separation of concerns (models for business logic, thin views, dedicated templates) makes the codebase easier to understand, debug, and extend.
    *   **Future Scalability:** Django's architecture is inherently scalable, making it easier to add new features or handle increased user load.
    *   **Modern Technology Stack:** Moving to Python/Django/HTMX/Alpine.js aligns the application with contemporary web development best practices, making it attractive for talent acquisition and easier to integrate with other modern systems.

By adopting this automated, structured migration approach, your organization can efficiently transition away from legacy ASP.NET, realizing substantial gains in application performance, maintainability, and future extensibility.