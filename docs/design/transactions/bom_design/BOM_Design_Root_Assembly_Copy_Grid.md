## ASP.NET to Django Conversion Script: BOM Root Assembly Copy Grid

This modernization plan outlines the transition of the ASP.NET BOM Root Assembly Copy Grid page to a modern Django application. The focus is on leveraging Django's robust ORM, efficient class-based views, and a dynamic frontend powered by HTMX and Alpine.js, all while adhering to the fat model/thin view paradigm and automation-first principles.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code utilizes data from `tblDG_Item_Master`, `tblDG_BOM_Master`, and `Unit_Master`. The `GridView` binds to data fields like `ItemCode`, `ManfDesc`, `UOMBasic`, `Qty`, and hidden fields `Id`, `CId`, `ItemId`. The C# `BindDataCustIMaster` function explicitly selects columns: `tblDG_BOM_Master.CId`, `tblDG_BOM_Master.Id`, `tblDG_BOM_Master.ItemId`, `tblDG_BOM_Master.WONo`, `tblDG_Item_Master.ItemCode`, `tblDG_Item_Master.ManfDesc`, `Unit_Master.Symbol As UOMBasic`, `tblDG_BOM_Master.Qty`. The WHERE clause filters by `WONo`, `PId='0'`, `CompId`, and `FinYearId`.

**Inferred Database Schema:**

*   **`tblDG_BOM_Master` (Main Table)**
    *   `Id` (Primary Key, Integer)
    *   `CId` (Integer, related to a BOM structure/transaction)
    *   `ItemId` (Foreign Key to `tblDG_Item_Master.Id`, Integer)
    *   `WONo` (Work Order Number, String)
    *   `PId` (Parent ID, Integer, '0' for root assemblies)
    *   `Qty` (Quantity, Decimal/Numeric)
    *   `CompId` (Company ID, Integer)
    *   `FinYearId` (Financial Year ID, Integer)

*   **`tblDG_Item_Master` (Lookup Table)**
    *   `Id` (Primary Key, Integer)
    *   `ItemCode` (String)
    *   `ManfDesc` (Manufacturing Description, String)
    *   `UOMBasic` (Foreign Key to `Unit_Master.Id`, Integer)

*   **`Unit_Master` (Lookup Table)**
    *   `Id` (Primary Key, Integer)
    *   `Symbol` (Unit Symbol, String)

### Step 2: Identify Backend Functionality

**Analysis:**
The ASP.NET page primarily acts as a search and "copy selected" interface for BOM (Bill of Material) root assemblies.

*   **Read (List & Search)**:
    *   Initial page load: Fetches BOM root assemblies based on `WONo` from query string, filtered by `PId='0'`, `CompId`, `FinYearId`.
    *   `btnSearch_Click`: Re-fetches data based on search criteria (dropdown selection and text box) and current `WONo`.
    *   `SearchGridView1_PageIndexChanging`: Re-fetches data for pagination.
*   **Create/Copy (Add Selected)**:
    *   `SearchGridView1_RowCommand` with `CommandName="Add"`: Iterates through selected checkboxes in the `GridView`. For each selected item, it calls `fun.getBOMCId` to get a new `CId` for the destination, and then `fun.getBOMRootNode` to copy the selected root assembly (and potentially its sub-assemblies) to a new Work Order (`WONoDest`).
*   **Navigation**:
    *   `btnCancel_Click` (and `btnCancel1_Click`): Redirects to `BOM_Design_WO_TreeView.aspx`, passing `WONo` and other module IDs.
*   **Session/Context**: `CompId`, `SessionId` (username), `FinYearId` are retrieved from ASP.NET Session. `WONoSrc` and `WONoDest` are from query strings.
*   **Messages**: `lblMsg` displays messages from query string.

### Step 3: Infer UI Components

**Analysis:**
The ASP.NET page is structured with a TabContainer. The main interactive elements are:

*   **Header Labels**: `lblWONoFrm`, `lblWONo` display the source and destination Work Order numbers.
*   **Search Controls**:
    *   `DropDownList1`: Selects search field (`Assembly No` or `Description`).
    *   `txtSearchCustomer`: Text input for search query.
    *   `btnSearch`: Triggers search.
    *   `btnCancel`: Navigates back.
    *   `lblMsg`: Displays status/error messages.
*   **Data Grid (`SearchGridView1`)**:
    *   Displays `SN`, `CheckBox` (for selection), `Assembly No`, `Description`, `UOM`, `Qty`.
    *   `chkSelectAll`: Selects/deselects all checkboxes.
    *   Footer with `btnAdd`: Triggers the "Add" (copy) operation for selected items.
    *   Hidden fields for `Id`, `CId`, `ItemId` per row.

**Django Mapping:**
*   HTML structure for layout and displaying WO numbers.
*   Django Forms for search dropdown and text input.
*   HTMX for submitting search, handling "Add selected" operation, and dynamically updating the table.
*   DataTables for client-side functionality of the grid (pagination, sorting, filtering).
*   Alpine.js for `chkSelectAll` behavior and potentially managing a confirmation modal for the Add action.
*   Django's `messages` framework for `lblMsg`.

---

### Step 4: Generate Django Code

For the purpose of this migration, we will create a Django application named `bom`.

#### 4.1 Models (`bom/models.py`)

This section defines the Django models corresponding to the extracted database schema. It also includes the business logic for copying BOM assemblies, adhering to the "fat model" principle.

```python
from django.db import models, transaction
from django.db.models import Max

# Model for Unit_Master table
class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False  # Tells Django not to manage this table's schema (it exists in the legacy DB)
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

# Model for tblDG_Item_Master table
class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=100)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=250)
    uom_basic = models.ForeignKey(UnitMaster, models.DO_NOTHING, db_column='UOMBasic', related_name='items')

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return f"{self.item_code} - {self.manf_desc}"

# Custom Manager for BOMMaster to encapsulate complex business logic
class BOMMasterManager(models.Manager):
    def get_next_bom_cid(self, wono, comp_id, fin_year_id):
        """
        Replicates the logic of C#'s fun.getBOMCId.
        Generates the next unique CId for a new BOM structure within a specific WONo, Company, and Fiscal Year.
        Assumes CId is a sequence number unique to (WONo, CompId, FinYearId).
        """
        max_cid = self.filter(
            wono=wono,
            comp_id=comp_id,
            fin_year_id=fin_year_id
        ).aggregate(Max('c_id'))['c_id__max']
        return (max_cid or 0) + 1 # Returns 1 if no records exist for this scope, otherwise max + 1

    @transaction.atomic
    def copy_bom_assembly(self, source_bom_id, source_wono, destination_wono, comp_id, session_id, fin_year_id, parent_id_in_dest_bom):
        """
        Replicates the logic of C#'s fun.getBOMRootNode, potentially recursively copying
        a BOM assembly and its children from a source WONo to a destination WONo.
        
        Args:
            source_bom_id (int): The ID of the BOMMaster entry to copy.
            source_wono (str): The Work Order Number of the source BOM.
            destination_wono (str): The Work Order Number for the new copied BOM.
            comp_id (int): Company ID for the operation.
            session_id (str): User session ID (for audit, if needed).
            fin_year_id (int): Financial Year ID for the operation.
            parent_id_in_dest_bom (int): The PId for the newly copied assembly in the destination BOM.
                                         Use 0 for root assemblies.

        Returns:
            BOMMaster: The newly created BOMMaster instance for the copied assembly.
        """
        try:
            source_assembly = self.select_related('item').get(pk=source_bom_id)
        except BOMMaster.DoesNotExist:
            raise ValueError(f"Source BOM Assembly with ID {source_bom_id} not found.")

        # Determine the CId for the new BOM structure if this is a root copy (parent_id_in_dest_bom == 0)
        # If it's a child copy, it should inherit the CId from its new parent in the destination BOM.
        # This requires passing the CId down, or retrieving it from the new parent.
        # For simplification, following C# flow, let's assume `getBOMRootNode` gets CId once for the whole tree.
        # So, the CId is calculated ONCE by the calling view for the root, and passed down for children.
        # However, the C# `getBOMCId` is called inside `SearchGridView1_RowCommand` *before* `getBOMRootNode`
        # for EACH selected item. This implies each copied root assembly gets its *own* new CId for its structure.
        # So, we'll calculate a new CId for each root copy here.

        new_dest_c_id = self.get_next_bom_cid(destination_wono, comp_id, fin_year_id)

        # Create the new BOMMaster entry for the copied assembly
        new_assembly = BOMMaster.objects.create(
            c_id=new_dest_c_id, # Each copied root gets a new CId
            item=source_assembly.item,
            wono=destination_wono,
            p_id=parent_id_in_dest_bom,
            qty=source_assembly.qty,
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            # Add any other relevant fields like created_by, created_date, modified_by etc.
        )

        # Handle recursive copying of children
        # In the original C# `getBOMRootNode`, the `PId` parameter suggests it recursively copies children.
        # We need to find all children of the source_assembly under source_wono and copy them.
        children_of_source = self.filter(p_id=source_assembly.id, wono=source_wono)
        for child_source_assembly in children_of_source:
            # Recursively call copy_bom_assembly for children, using the new_assembly's ID as their parent_id
            self.copy_bom_assembly(
                source_bom_id=child_source_assembly.id,
                source_wono=source_wono,
                destination_wono=destination_wono,
                comp_id=comp_id,
                session_id=session_id, # Pass session_id through recursion
                fin_year_id=fin_year_id,
                parent_id_in_dest_bom=new_assembly.id # New parent is the ID of the newly created parent assembly
            )

        return new_assembly

# Main BOMMaster model
class BOMMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    c_id = models.IntegerField(db_column='CId')
    item = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId', related_name='bom_masters_as_item')
    wono = models.CharField(db_column='WONo', max_length=100)
    p_id = models.IntegerField(db_column='PId') # Parent ID in BOM structure (0 for root)
    qty = models.DecimalField(db_column='Qty', max_digits=10, decimal_places=4)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    objects = BOMMasterManager() # Assign custom manager

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'BOM Master'
        verbose_name_plural = 'BOM Masters'

    def __str__(self):
        return f"BOM ID: {self.id} | Item: {self.item.item_code} | WONo: {self.wono}"

```

#### 4.2 Forms (`bom/forms.py`)

This form handles the search criteria. It's not a ModelForm as it doesn't directly map to a single model instance for creation/editing, but rather provides filtering options.

```python
from django import forms

class BOMSearchForm(forms.Form):
    SEARCH_CHOICES = [
        ('item__item_code', 'Assembly No'), # Maps to ItemMaster.ItemCode
        ('item__manf_desc', 'Description'), # Maps to ItemMaster.ManfDesc
    ]
    search_by = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    search_text = forms.CharField(
        max_length=250,
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter search text'})
    )

```

#### 4.3 Views (`bom/views.py`)

The views implement the logic for displaying the list, handling search, and performing the "add selected" (copy) operation. They are kept thin, delegating complex logic to the models.

```python
from django.views.generic import ListView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import render, redirect
from django.db.models import Q # For complex queries

from .models import BOMMaster # Import the main model
from .forms import BOMSearchForm # Import the search form

# Dummy values for CompId and FinYearId from session.
# In a real application, these would come from authenticated user's session or profile.
DUMMY_COMP_ID = 1 # Example company ID
DUMMY_FIN_YEAR_ID = 2023 # Example financial year ID
DUMMY_SESSION_ID = 'AdminUser' # Example session user

class BOMRootAssemblyListView(ListView):
    """
    Handles the initial display of the BOM Root Assembly Copy page.
    It passes initial context like WONoSrc, WONoDest and the search form.
    The actual table content is loaded via HTMX into a partial view.
    """
    model = BOMMaster
    template_name = 'bom/bom_root_assembly_list.html'
    context_object_name = 'bom_masters'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        wonosrc = self.request.GET.get('WONoSrc', 'N/A')
        wonodest = self.request.GET.get('WONoDest', 'N/A')
        msg = self.request.GET.get('msg', '')

        context['wonosrc'] = wonosrc
        context['wonodest'] = wonodest
        context['search_form'] = BOMSearchForm(self.request.GET) # Pre-populate form if GET params exist

        if msg:
            messages.success(self.request, msg) # Display messages from query string

        return context

class BOMRootAssemblyTablePartialView(ListView):
    """
    Returns the partial HTML for the BOM master table.
    This view is designed to be called via HTMX for search and initial load.
    """
    model = BOMMaster
    template_name = 'bom/_bom_root_assembly_table.html'
    context_object_name = 'bom_masters'
    paginate_by = 12 # Matches original PageSize

    def get_queryset(self):
        # Fetch WONo from query params, essential for filtering
        wono_src = self.request.GET.get('WONoSrc', '')
        
        queryset = BOMMaster.objects.select_related('item', 'item__uom_basic').filter(
            wono=wono_src,
            p_id=0, # Only root assemblies
            comp_id=DUMMY_COMP_ID,
            fin_year_id=DUMMY_FIN_YEAR_ID
        ).order_by('-id') # Matches original "Order by tblDG_BOM_Master.Id Desc"

        form = BOMSearchForm(self.request.GET)
        if form.is_valid():
            search_by = form.cleaned_data.get('search_by')
            search_text = form.cleaned_data.get('search_text')

            if search_text and search_by:
                # Apply search filter
                query = Q()
                if search_by == 'item__item_code':
                    query &= Q(item__item_code__icontains=search_text)
                elif search_by == 'item__manf_desc':
                    query &= Q(item__manf_desc__icontains=search_text)
                queryset = queryset.filter(query)
        
        return queryset

class BOMRootAssemblyAddSelectedView(View):
    """
    Handles the POST request for copying selected BOM root assemblies.
    This view is called via HTMX when the 'Add' button is clicked.
    """
    def post(self, request, *args, **kwargs):
        selected_ids_str = request.POST.getlist('selected_bom_ids') # HTMX will pass selected IDs
        wonosrc = request.POST.get('wonosrc', '')
        wonodest = request.POST.get('wonodest', '')

        selected_ids = [int(id) for id in selected_ids_str if id.isdigit()]
        
        if not selected_ids:
            messages.warning(request, "No root assemblies selected for copying.")
            # HTMX will not swap, so we rely on HX-Trigger to refresh messages or page
            return HttpResponse(status=204, headers={'HX-Trigger': 'showMessage'}) # No content, trigger message display

        if not wonosrc or not wonodest:
            messages.error(request, "Source or destination Work Order Number is missing.")
            return HttpResponse(status=204, headers={'HX-Trigger': 'showMessage'})


        copied_count = 0
        try:
            for bom_id in selected_ids:
                # Call the fat model method to perform the copy
                BOMMaster.objects.copy_bom_assembly(
                    source_bom_id=bom_id,
                    source_wono=wonosrc,
                    destination_wono=wonodest,
                    comp_id=DUMMY_COMP_ID,
                    session_id=DUMMY_SESSION_ID,
                    fin_year_id=DUMMY_FIN_YEAR_ID,
                    parent_id_in_dest_bom=0 # Always 0 for root assemblies being copied
                )
                copied_count += 1
            
            msg_text = f"Copy of {copied_count} root assemblies are done."
            messages.success(request, msg_text)

            # After successful copy, redirect to the WO TreeView page as per original ASP.NET
            # This is a full redirect as per original ASP.NET behavior after 'Add'
            # For a more modern HTMX approach, one might swap content or trigger a different event.
            # But adhering to the original logic for now.
            redirect_url = reverse_lazy('bom_design_wo_treeview') # Assuming this URL exists
            # Append WONo and other params as in original C#
            redirect_url += f"?WONo={wonodest}&ModId=3&SubModId=26&Msg={msg_text}"
            return HttpResponseRedirect(redirect_url)

        except Exception as e:
            messages.error(request, f"Error copying assemblies: {e}")
            return HttpResponse(status=500, headers={'HX-Trigger': 'showMessage'})


class BOMDesignWOTreeView(View):
    """
    Placeholder view for the BOM_Design_WO_TreeView.aspx redirect.
    In a real application, this would be the actual WO TreeView page.
    """
    def get(self, request, *args, **kwargs):
        wonosrc = request.GET.get('WONo', 'N/A')
        mod_id = request.GET.get('ModId', 'N/A')
        sub_mod_id = request.GET.get('SubModId', 'N/A')
        msg = request.GET.get('Msg', 'Redirected to WO Tree View.')
        
        messages.info(request, f"Navigated to WO Tree View for WONo: {wonosrc}, ModId: {mod_id}, SubModId: {sub_mod_id}. Message: {msg}")
        return render(request, 'bom/bom_design_wo_treeview_placeholder.html', {
            'wonosrc': wonosrc, 'mod_id': mod_id, 'sub_mod_id': sub_mod_id, 'msg': msg
        })

```

#### 4.4 Templates (`bom/templates/bom/`)

These templates define the HTML structure and integrate HTMX and Alpine.js for dynamic interactions.

**`bom_root_assembly_list.html`** (Main page template)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-gray-100 p-4 rounded-md shadow-sm mb-6 flex justify-between items-center">
        <h2 class="text-xl font-semibold text-gray-800">
            BOM Root Assembly Copy &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
            WO No From: <span class="font-bold text-blue-700">{{ wonosrc }}</span>&nbsp;&nbsp;&nbsp;
            To: <span class="font-bold text-blue-700">{{ wonodest }}</span>
        </h2>
        <div id="messages-container" class="text-red-600 font-bold"
             hx-trigger="showMessage from:body"
             hx-swap="innerHTML">
            {% if messages %}
                {% for message in messages %}
                    <div class="{{ message.tags }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        </div>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-lg mb-6">
        <form id="searchForm" hx-get="{% url 'bom_root_assembly_table' %}?WONoSrc={{ wonosrc }}&WONoDest={{ wonodest }}" hx-target="#bomTable-container" hx-swap="innerHTML">
            <div class="flex items-center space-x-4">
                <div>
                    {{ search_form.search_by }}
                </div>
                <div class="flex-grow">
                    {{ search_form.search_text }}
                </div>
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                    Search
                </button>
                <a href="{% url 'bom_design_wo_treeview' %}?WONo={{ wonodest }}&ModId=3&SubModId=26" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm">
                    Cancel
                </a>
            </div>
            {% comment %} Hidden fields to preserve WONo in subsequent HTMX requests {% endcomment %}
            <input type="hidden" name="WONoSrc" value="{{ wonosrc }}">
            <input type="hidden" name="WONoDest" value="{{ wonodest }}">
        </form>
    </div>
    
    <div id="bomTable-container"
         hx-trigger="load delay:100ms, refreshBOMList from:body"
         hx-get="{% url 'bom_root_assembly_table' %}?WONoSrc={{ wonosrc }}&WONoDest={{ wonodest }}"
         hx-swap="innerHTML">
        <!-- DataTables content will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading BOM Data...</p>
        </div>
    </div>
</div>

<!-- Placeholder template for the WO Tree View redirect -->
<template id="wo-tree-view-placeholder">
    <div class="p-6 text-center">
        <h3 class="text-xl font-medium text-gray-900 mb-4">Redirecting to WO Tree View...</h3>
        <p class="text-gray-700">This page would typically display the work order tree structure.</p>
        <p class="text-gray-700 mt-2">WONo: {{ wonosrc }}, ModId: {{ mod_id }}, SubModId: {{ sub_mod_id }}</p>
    </div>
</template>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('bomSelection', () => ({
            selectAll: false,
            init() {
                this.$watch('selectAll', (value) => {
                    document.querySelectorAll('input[name="selected_bom_ids"]').forEach(checkbox => {
                        checkbox.checked = value;
                    });
                });
            },
            toggleAll() {
                this.selectAll = !this.selectAll;
            }
        }));
    });

    document.body.addEventListener('htmx:afterSwap', function(evt) {
        // Re-initialize DataTables if the table is swapped
        if (evt.target.id === 'bomTable-container') {
            $('#bomRootAssemblyTable').DataTable({
                "pageLength": 12, // Matches original GridView PageSize
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "pagingType": "full_numbers", // Example for full pagination controls
                "dom": '<"top"lfip>rt<"bottom"ip><"clear">' // Layout for DataTables elements
            });
        }
    });

    // Function to show confirmation for 'Add' button
    window.confirmationAdd = function() {
        const selectedCheckboxes = document.querySelectorAll('input[name="selected_bom_ids"]:checked');
        if (selectedCheckboxes.length === 0) {
            alert("Please select at least one item to add.");
            return false;
        }
        return confirm("Are you sure you want to add the selected root assemblies?");
    };

    // To handle messages from HTMX requests
    document.body.addEventListener('showMessage', function(evt) {
        // Assuming messages are rendered by Django's message framework and swapped
        // This can also involve custom JS to display toast notifications etc.
        console.log("Messages updated.");
    });

</script>
{% endblock %}
```

**`_bom_root_assembly_table.html`** (Partial template for the DataTables grid)

```html
{% comment %}
This template is a partial, designed to be swapped into the main page via HTMX.
It contains the DataTables structure for displaying BOM root assemblies.
{% endcomment %}

{% if bom_masters %}
<div x-data="bomSelection" class="bg-white p-6 rounded-lg shadow-lg">
    <form id="bomCopyForm" hx-post="{% url 'bom_add_selected' %}" hx-swap="none" 
          hx-confirm="Are you sure you want to add the selected root assemblies?"
          hx-on::after-request="if(event.detail.xhr.status === 204) { alert('No assemblies selected or error.'); $dispatch('showMessage'); }">
        {% csrf_token %}
        <input type="hidden" name="wonosrc" value="{{ request.GET.WONoSrc }}">
        <input type="hidden" name="wonodest" value="{{ request.GET.WONoDest }}">
        
        <table id="bomRootAssemblyTable" class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <input type="checkbox" x-model="selectAll" @change="toggleAll" class="rounded text-blue-600 focus:ring-blue-500">
                    </th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assembly No</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                    <!-- Hidden columns for data used in operations -->
                    <th class="hidden">Id</th>
                    <th class="hidden">CId</th>
                    <th class="hidden">ItemId</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for bom_master in bom_masters %}
                <tr>
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">
                        <input type="checkbox" name="selected_bom_ids" value="{{ bom_master.id }}" class="rounded text-blue-600 focus:ring-blue-500">
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left">{{ bom_master.item.item_code }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left">{{ bom_master.item.manf_desc }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ bom_master.item.uom_basic.symbol }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ bom_master.qty }}</td>
                    <td class="hidden">{{ bom_master.id }}</td>
                    <td class="hidden">{{ bom_master.c_id }}</td>
                    <td class="hidden">{{ bom_master.item.id }}</td>
                </tr>
                {% endfor %}
            </tbody>
            <tfoot>
                <tr>
                    <td colspan="7" class="py-2 px-4 text-center">
                        <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-sm"
                                hx-on:click="if(!confirmationAdd()) event.preventDefault();">
                            Add Selected
                        </button>
                    </td>
                </tr>
            </tfoot>
        </table>
    </form>
</div>
{% else %}
<div class="bg-white p-6 rounded-lg shadow-lg text-center">
    <p class="text-lg font-semibold text-maroon-700">No data to display !</p>
</div>
{% endif %}

<script>
    // DataTables initialization is handled in the parent `bom_root_assembly_list.html`
    // within the htmx:afterSwap event listener to ensure it runs after content is loaded.
</script>
```

**`bom_design_wo_treeview_placeholder.html`** (A simple placeholder for the redirect page)
This file should exist in `bom/templates/bom/`.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8 text-center">
    <h2 class="text-2xl font-bold text-gray-800 mb-4">Work Order Tree View Placeholder</h2>
    <p class="text-lg text-gray-600">This page represents the destination after copying BOM assemblies.</p>
    <p class="text-md text-gray-500 mt-2">WO Number: <span class="font-semibold">{{ wonosrc }}</span></p>
    <p class="text-md text-gray-500">Module ID: <span class="font-semibold">{{ mod_id }}</span></p>
    <p class="text-md text-gray-500">Submodule ID: <span class="font-semibold">{{ sub_mod_id }}</span></p>
    {% if msg %}
        <p class="text-md text-green-700 font-semibold mt-4">Message: {{ msg }}</p>
    {% endif %}
    <a href="{% url 'bom_root_assembly_list' %}?WONoSrc=OLD_WO_EXAMPLE&WONoDest=NEW_WO_EXAMPLE" class="mt-8 inline-block bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
        Go Back to BOM Copy Page (Example)
    </a>
</div>
{% endblock %}
```

#### 4.5 URLs (`bom/urls.py`)

Define the URL patterns for the views.

```python
from django.urls import path
from .views import (
    BOMRootAssemblyListView,
    BOMRootAssemblyTablePartialView,
    BOMRootAssemblyAddSelectedView,
    BOMDesignWOTreeView
)

urlpatterns = [
    # Main page for BOM Root Assembly Copy
    path('bom-root-assembly-copy/', BOMRootAssemblyListView.as_view(), name='bom_root_assembly_list'),
    
    # HTMX endpoint for loading/refreshing the BOM table content
    path('bom-root-assembly-copy/table/', BOMRootAssemblyTablePartialView.as_view(), name='bom_root_assembly_table'),
    
    # HTMX endpoint for processing the 'Add Selected' action
    path('bom-root-assembly-copy/add-selected/', BOMRootAssemblyAddSelectedView.as_view(), name='bom_add_selected'),

    # Placeholder for the redirect destination (WO TreeView)
    path('bom-design-wo-treeview/', BOMDesignWOTreeView.as_view(), name='bom_design_wo_treeview'),
]
```

#### 4.6 Tests (`bom/tests.py`)

Comprehensive tests for models and views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db.models import Max
from .models import UnitMaster, ItemMaster, BOMMaster, DUMMY_COMP_ID, DUMMY_FIN_YEAR_ID, DUMMY_SESSION_ID

class BOMMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dummy UnitMaster and ItemMaster for related objects
        cls.unit_kg = UnitMaster.objects.create(id=1, symbol='KG')
        cls.unit_pcs = UnitMaster.objects.create(id=2, symbol='PCS')

        cls.item_root_a = ItemMaster.objects.create(id=101, item_code='ASM001', manf_desc='Root Assembly A', uom_basic=cls.unit_kg)
        cls.item_root_b = ItemMaster.objects.create(id=102, item_code='ASM002', manf_desc='Root Assembly B', uom_basic=cls.unit_pcs)
        cls.item_child_x = ItemMaster.objects.create(id=103, item_code='PART001', manf_desc='Part X', uom_basic=cls.unit_pcs)
        cls.item_child_y = ItemMaster.objects.create(id=104, item_code='PART002', manf_desc='Part Y', uom_basic=cls.unit_kg)

        # Create source BOM data for WONoSrc
        cls.wono_src = "WO-2023-001"
        cls.wono_dest = "WO-2023-002"

        # Root Assembly A in source WONo
        cls.bom_root_a = BOMMaster.objects.create(
            id=1, c_id=1, item=cls.item_root_a, wono=cls.wono_src, p_id=0, qty=1.00,
            comp_id=DUMMY_COMP_ID, fin_year_id=DUMMY_FIN_YEAR_ID
        )
        # Children of Root Assembly A
        BOMMaster.objects.create(
            id=2, c_id=1, item=cls.item_child_x, wono=cls.wono_src, p_id=cls.bom_root_a.id, qty=5.00,
            comp_id=DUMMY_COMP_ID, fin_year_id=DUMMY_FIN_YEAR_ID
        )
        BOMMaster.objects.create(
            id=3, c_id=1, item=cls.item_child_y, wono=cls.wono_src, p_id=cls.bom_root_a.id, qty=2.50,
            comp_id=DUMMY_COMP_ID, fin_year_id=DUMMY_FIN_YEAR_ID
        )

        # Root Assembly B in source WONo
        cls.bom_root_b = BOMMaster.objects.create(
            id=4, c_id=2, item=cls.item_root_b, wono=cls.wono_src, p_id=0, qty=3.00,
            comp_id=DUMMY_COMP_ID, fin_year_id=DUMMY_FIN_YEAR_ID
        )

    def test_bom_master_creation(self):
        bom = BOMMaster.objects.get(id=1)
        self.assertEqual(bom.item.item_code, 'ASM001')
        self.assertEqual(bom.wono, self.wono_src)
        self.assertEqual(bom.p_id, 0)
        self.assertEqual(bom.qty, 1.00)

    def test_get_next_bom_cid(self):
        # Initial call, should return 3 based on existing CIds (1 and 2)
        next_cid = BOMMaster.objects.get_next_bom_cid(self.wono_src, DUMMY_COMP_ID, DUMMY_FIN_YEAR_ID)
        self.assertEqual(next_cid, 3)

        # Test for a new WONo where no BOMs exist yet
        new_wono = "WO-2023-003"
        next_cid_new_wono = BOMMaster.objects.get_next_bom_cid(new_wono, DUMMY_COMP_ID, DUMMY_FIN_YEAR_ID)
        self.assertEqual(next_cid_new_wono, 1) # Should start from 1

    def test_copy_bom_assembly_root(self):
        # Copy bom_root_a (id=1) to new WONo
        initial_bom_count = BOMMaster.objects.count()
        
        copied_assembly = BOMMaster.objects.copy_bom_assembly(
            source_bom_id=self.bom_root_a.id,
            source_wono=self.wono_src,
            destination_wono=self.wono_dest,
            comp_id=DUMMY_COMP_ID,
            session_id=DUMMY_SESSION_ID,
            fin_year_id=DUMMY_FIN_YEAR_ID,
            parent_id_in_dest_bom=0
        )
        
        # Verify root assembly copied
        self.assertIsNotNone(copied_assembly)
        self.assertNotEqual(copied_assembly.id, self.bom_root_a.id) # New ID
        self.assertEqual(copied_assembly.item, self.item_root_a)
        self.assertEqual(copied_assembly.wono, self.wono_dest)
        self.assertEqual(copied_assembly.p_id, 0)
        
        # Verify children copied (Root A had 2 children)
        # The copy_bom_assembly method also recursively copies children.
        copied_children_count = BOMMaster.objects.filter(p_id=copied_assembly.id, wono=self.wono_dest).count()
        self.assertEqual(copied_children_count, 2)
        
        # Check total BOM count increased by 1 (root) + 2 (children) = 3
        self.assertEqual(BOMMaster.objects.count(), initial_bom_count + 3)

    def test_copy_bom_assembly_non_existent_source(self):
        with self.assertRaises(ValueError):
            BOMMaster.objects.copy_bom_assembly(
                source_bom_id=999, # Non-existent ID
                source_wono=self.wono_src,
                destination_wono=self.wono_dest,
                comp_id=DUMMY_COMP_ID,
                session_id=DUMMY_SESSION_ID,
                fin_year_id=DUMMY_FIN_YEAR_ID,
                parent_id_in_dest_bom=0
            )

class BOMRootAssemblyViewsTest(TestCase):
    client = Client()

    @classmethod
    def setUpTestData(cls):
        # Use existing data from model setup or create new minimal data
        cls.unit_pcs = UnitMaster.objects.create(id=2, symbol='PCS')
        cls.item_root_b = ItemMaster.objects.create(id=102, item_code='ASM002', manf_desc='Root Assembly B', uom_basic=cls.unit_pcs)
        
        cls.wono_src = "WO-SOURCE-001"
        cls.wono_dest = "WO-DEST-002"

        # Create some root BOMs for the source WO
        BOMMaster.objects.create(
            id=10, c_id=1, item=cls.item_root_b, wono=cls.wono_src, p_id=0, qty=1.0,
            comp_id=DUMMY_COMP_ID, fin_year_id=DUMMY_FIN_YEAR_ID
        )
        BOMMaster.objects.create(
            id=11, c_id=2, item=cls.item_root_b, wono=cls.wono_src, p_id=0, qty=2.0,
            comp_id=DUMMY_COMP_ID, fin_year_id=DUMMY_FIN_YEAR_ID
        )
        BOMMaster.objects.create( # A child for testing non-root filtering
            id=12, c_id=1, item=cls.item_root_b, wono=cls.wono_src, p_id=10, qty=0.5,
            comp_id=DUMMY_COMP_ID, fin_year_id=DUMMY_FIN_YEAR_ID
        )

    def test_list_view_get(self):
        url = reverse('bom_root_assembly_list') + f'?WONoSrc={self.wono_src}&WONoDest={self.wono_dest}'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom/bom_root_assembly_list.html')
        self.assertContains(response, self.wono_src)
        self.assertContains(response, self.wono_dest)
        self.assertIn('search_form', response.context)

    def test_table_partial_view_get(self):
        url = reverse('bom_root_assembly_table') + f'?WONoSrc={self.wono_src}&WONoDest={self.wono_dest}'
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom/_bom_root_assembly_table.html')
        # Expect 2 root BOMs for self.wono_src
        self.assertEqual(len(response.context['bom_masters']), 2) 
        self.assertContains(response, 'ASM002') # Checks if data is present

    def test_table_partial_view_get_with_search(self):
        # Create an item with a distinct description for searching
        ItemMaster.objects.create(id=105, item_code='SEARCH001', manf_desc='Unique Search Item', uom_basic=self.unit_pcs)
        BOMMaster.objects.create(
            id=13, c_id=3, item_id=105, wono=self.wono_src, p_id=0, qty=1.0,
            comp_id=DUMMY_COMP_ID, fin_year_id=DUMMY_FIN_YEAR_ID
        )

        url = reverse('bom_root_assembly_table') + f'?WONoSrc={self.wono_src}&WONoDest={self.wono_dest}&search_by=item__manf_desc&search_text=Unique'
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom/_bom_root_assembly_table.html')
        self.assertEqual(len(response.context['bom_masters']), 1)
        self.assertContains(response, 'Unique Search Item')
        self.assertNotContains(response, 'Root Assembly B')

    def test_add_selected_view_post_success(self):
        # Initially, there should be 3 BOMs for WO-SOURCE-001 (2 roots, 1 child)
        # And 0 for WO-DEST-002
        initial_dest_bom_count = BOMMaster.objects.filter(wono=self.wono_dest).count()
        self.assertEqual(initial_dest_bom_count, 0)

        # Get the IDs of the root BOMs from the source WONo
        source_root_bom_ids = list(BOMMaster.objects.filter(wono=self.wono_src, p_id=0).values_list('id', flat=True))

        post_data = {
            'selected_bom_ids': source_root_bom_ids,
            'wonosrc': self.wono_src,
            'wonodest': self.wono_dest,
            'csrfmiddlewaretoken': self.client.get(reverse('bom_root_assembly_list')).cookies['csrftoken'].value, # Fetch CSRF token
        }
        
        response = self.client.post(reverse('bom_add_selected'), post_data, HTTP_HX_REQUEST='true')
        
        # Check if it redirects as per original ASP.NET behavior
        self.assertEqual(response.status_code, 302)
        self.assertIn(reverse('bom_design_wo_treeview'), response.url)

        # Verify BOMs are copied to the destination WONo
        copied_boms = BOMMaster.objects.filter(wono=self.wono_dest)
        # Each root copy (2 roots) will create a new root + its 0 or more children
        # We copied 2 roots, where one (id=10) had no children, and the other (id=11) had no children either.
        # But in BOMMasterModelTest, we created id=10 with 2 children.
        # Let's adjust test data to ensure children exist.
        # Let's assume for this test setup, `BOMMaster.objects.filter(wono=self.wono_src, p_id=0)` gives us the roots (id=10, id=11).
        # And id=10 has a child (id=12). So copying id=10 should copy 10 and 12. Copying id=11 just copies 11.
        # So total copied = 1 (root 10) + 1 (child 12) + 1 (root 11) = 3
        
        self.assertEqual(copied_boms.count(), 3) # Expect 3 new BOM entries (2 roots + 1 child)
        self.assertTrue(copied_boms.filter(wono=self.wono_dest, p_id=0).count() == 2) # Two new roots
        
    def test_add_selected_view_post_no_selection(self):
        post_data = {
            'selected_bom_ids': [], # No IDs selected
            'wonosrc': self.wono_src,
            'wonodest': self.wono_dest,
            'csrfmiddlewaretoken': self.client.get(reverse('bom_root_assembly_list')).cookies['csrftoken'].value,
        }
        response = self.client.post(reverse('bom_add_selected'), post_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No content response for HTMX
        self.assertIn('HX-Trigger', response.headers)
        self.assertContains(response, 'No root assemblies selected for copying.', html=False) # Check for message if rendered directly, otherwise relies on trigger
```

### Step 5: HTMX and Alpine.js Integration

The templates and views are designed for a fully dynamic, single-page application feel using HTMX and Alpine.js without writing custom JavaScript.

*   **HTMX for Content Loading and Updates**:
    *   The main list page (`bom_root_assembly_list.html`) uses `hx-get` on `bomTable-container` with `hx-trigger="load, refreshBOMList from:body"` to load the table content (`_bom_root_assembly_table.html`) dynamically on page load and whenever a `refreshBOMList` custom event is triggered (e.g., after a successful operation if the original page were to refresh its own table).
    *   The search form (`#searchForm`) also uses `hx-get` to trigger a re-load of the table content with new filters.
    *   The "Add Selected" button in `_bom_root_assembly_table.html` uses `hx-post` to submit the selected item IDs to `bom_add_selected`. The `hx-swap="none"` and subsequent full redirect handle the original ASP.NET page flow.
*   **Alpine.js for UI State**:
    *   The `chkSelectAll` checkbox in the table header uses Alpine.js (`x-data="bomSelection"`, `x-model="selectAll"`) to manage the state of all individual checkboxes, eliminating the need for server-side logic for "select all".
*   **DataTables for List Views**:
    *   The `_bom_root_assembly_table.html` defines the `<table>` with `id="bomRootAssemblyTable"`.
    *   A JavaScript block in `bom_root_assembly_list.html` listens for `htmx:afterSwap` on the `#bomTable-container` and then initializes DataTables on the loaded table. This ensures DataTables is applied only after the content is in the DOM.
*   **Strict Separation**: No HTML generation in views, all business logic in models, ensuring thin views.
*   **No Additional JavaScript**: All interactivity is handled by HTMX and Alpine.js. Custom JS functions are only for DataTables initialization and confirmation dialogs.
*   **DRY Template Inheritance**: `base.html` (not included here) would contain all common CDN links for HTMX, Alpine.js, jQuery, and DataTables.

### Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating the ASP.NET BOM Root Assembly Copy Grid to Django. It adheres strictly to the defined architectural patterns, technology stack preferences, and communication style. The use of AI-assisted automation would streamline the identification and conversion of schema, UI elements, and business logic into these Django components, significantly reducing manual development effort. Remember to replace dummy IDs and ensure proper session management in a production environment. The `copy_bom_assembly` model method should be thoroughly reviewed against the exact behavior of `fun.getBOMRootNode` for complete functional parity, especially concerning recursive copying of sub-assemblies.