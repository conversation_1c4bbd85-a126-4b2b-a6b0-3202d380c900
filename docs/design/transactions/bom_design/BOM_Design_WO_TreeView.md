This comprehensive Django modernization plan addresses the migration of your ASP.NET BOM Tree View application. It leverages modern Django 5.0+ patterns, prioritizes automation-driven approaches, and employs HTMX + Alpine.js for a highly interactive user experience without excessive custom JavaScript. The business logic is strictly encapsulated within models, adhering to the "Fat Model, Thin View" philosophy.

---

## ASP.NET to Django Conversion Script:

This document outlines the modernization plan for transitioning your ASP.NET BOM Tree View application to a robust and scalable Django solution. Our focus is on clear, actionable steps that facilitate AI-assisted automation, ensuring a smooth and efficient migration process.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

We will establish a new Django application named `design` to house the modernized BOM functionality.

## Step 1: Extract Database Schema

**Business Value:** A precise understanding of the existing database schema is fundamental. By directly mapping Django models to your current tables, we ensure data integrity, minimize data transformation efforts, and reduce the risk associated with data migration, preserving your valuable historical information.

**Analysis & Instructions:**
The ASP.NET code extensively interacts with `tblDG_BOM_Master`, `tblDG_Item_Master`, and `Unit_Master`. The `GetDataTable` function's logic and UI bindings indicate the following column structures and relationships:

*   **`tblDG_BOM_Master`**: This table defines the hierarchical Bill of Materials.
    *   `CId` (Primary Key, Integer): Uniquely identifies a BOM entry within the system. This serves as the "Child ID" in the tree structure.
    *   `PId` (Foreign Key to `CId` in same table, Integer, Nullable): References the `CId` of the parent BOM entry. If null, it's a root assembly.
    *   `ItemId` (Foreign Key, Integer): Links to the actual item/part defined in `tblDG_Item_Master`.
    *   `WONo` (String): The Work Order Number associated with this BOM.
    *   `Qty` (Decimal): The quantity of the `ItemId` required for this specific BOM entry.
    *   `Revision` (String): The revision of this BOM entry.
    *   `CompId` (Integer): Company Identifier.
    *   `FinYearId` (Integer): Financial Year Identifier.

*   **`tblDG_Item_Master`**: This table holds master data for all items and parts.
    *   `Id` (Primary Key, Integer): Unique identifier for an item.
    *   `ItemCode` (String): The primary item code for purchased items.
    *   `PartNo` (String): The part number for manufactured items.
    *   `ManfDesc` (String): Manufacturer's description or general description of the item.
    *   `UOMBasic` (Foreign Key, Integer): Links to `Unit_Master` for the item's basic Unit of Measure.
    *   `FileName` (String): Name of the associated drawing/image file.
    *   `FileData` (Binary): Actual binary data for the drawing/image.
    *   `AttName` (String): Name of the associated specification sheet file.
    *   `AttData` (Binary): Actual binary data for the specification sheet.
    *   `AttContentType` (String): Content type of the specification sheet (inferred from `DownloadFile.aspx`).
    *   `CId` (Integer, Nullable): A category ID for items. Crucially, `null` signifies a "Manufacturing" item (which cannot be modified via "Select"), while a non-null value signifies a "BoughtOut" item.

*   **`Unit_Master`**: This table defines standard units of measure.
    *   `Id` (Primary Key, Integer): Unique identifier for a unit.
    *   `Symbol` (String): The common symbol for the unit (e.g., "PCS", "MTR").

## Step 2: Identify Backend Functionality

**Business Value:** Understanding the application's core functions allows us to design a Django solution that precisely meets current operational needs while offering a platform for future enhancements. By breaking down complex logic into manageable components, we ensure a more maintainable and efficient system.

**Instructions:**
The ASP.NET page primarily serves as a "Read" (display) interface for Bill of Materials data, with filtering capabilities and redirection triggers for other application modules.

*   **Core Data Display (Read):**
    *   **Data Source:** The `GetDataTable` C# method is the heart of the data retrieval. It constructs a flattened view of the BOM hierarchy.
    *   **Filtering:** Filters are applied based on `WONo` (from query string) and `DropDownList1` (`drpValue`: All, BoughtOut, Manufacturing).
    *   **Hierarchical Display:** The `Telerik:RadTreeList` renders the data in a tree-like structure, handling expand/collapse, and pagination.
    *   **Dynamic Information:** Displays calculated quantities (`BOM Qty`), item details (`Item Code`, `Description`, `UOM`), and conditional "View"/"Upload" links for attachments.

*   **Filtering & View Controls:**
    *   `DropDownList1`: Allows users to filter the BOM by "All," "BoughtOut," or "Manufacturing" items. `AutoPostBack` indicates immediate re-rendering.
    *   `CheckBox1`: Toggles expanding or collapsing all nodes in the BOM tree. `AutoPostBack` indicates immediate re-rendering.

*   **Actions (Redirections to Other Modules):**
    *   **"Select" Command (`RadTreeList1_ItemCommand`):** For a specific BOM item, if it's *not* a "Standard Item" (i.e., `tblDG_Item_Master.CId` is not null), it redirects to `BOM_WoItems.aspx` for editing. Otherwise, it shows an alert message.
    *   **"Download/Upload Image/Spec" Commands (`RadTreeList1_ItemCommand`):** Links to `DownloadFile.aspx` for file serving or `BOM_UploadDrw.aspx` for uploading new drawings/spec sheets.
    *   **"Add Assembly" Button (`btnAssembly_Click`):** Redirects to `BOM_Design_Assembly_New.aspx` to add a new top-level assembly.
    *   **"Copy From" Button (`btnCopy_Click`):** Redirects to `BOM_Design_Root_Assembly_Copy_WO.aspx` to initiate a BOM copy operation.
    *   **"Cancel" Button (`Button1_Click`):** Redirects back to the main BOM list grid (`BOM_Design_WO_Grid.aspx`).

*   **Embedded Business Logic:**
    *   The `GetDataTable` method contains complex logic for traversing the BOM hierarchy (`fun.BOMTree_Search`), calculating cumulative quantities (`fun.BOMTreeQty`), and applying conditional display rules (e.g., `ItemCode` vs. `PartNo`, "View" vs. "Upload"). This logic will be migrated to Django models.

## Step 3: Infer UI Components

**Business Value:** By translating legacy ASP.NET controls into modern web components, we simplify frontend development, enhance user experience, and ensure responsiveness across various devices. The adoption of HTMX, Alpine.js, and DataTables enables rich interactivity with minimal custom JavaScript.

**Instructions:**
The ASP.NET page uses a combination of standard HTML elements and Telerik Web Forms controls.

*   **Page Layout:**
    *   The `<table>` structure, `align` attributes, and `style` blocks (`.style3`, `.style4`, etc.) define the page's layout. This will be replaced by modern CSS frameworks (Tailwind CSS) for responsive and maintainable layouts.
*   **Header & Information Display:**
    *   `<b>BOM</b>` and `<b>Wo No:</b> <asp:Label ID="Label2" ...>`: These will be simple HTML text and a Django template variable.
    *   `<asp:Label ID="lblmsg" ...>`: For displaying messages from query strings. This will be integrated with Django's messaging framework.
*   **Input & Filters:**
    *   `<asp:DropDownList ID="DropDownList1" ...>`: This will become a standard HTML `<select>` element. Its `AutoPostBack` behavior will be replicated using HTMX's `hx-get` to dynamically reload the data table.
    *   `<asp:CheckBox ID="CheckBox1" ...>`: This will be an HTML `<input type="checkbox">`. Its `AutoPostBack` will also be managed by HTMX to trigger table refreshes.
*   **Action Buttons:**
    *   `<asp:Button ID="btnAssembly" ...>`, `<asp:Button ID="btnCopy" ...>`, `<asp:Button ID="Button1" ...>`: These will be HTML `<button>` elements. Their `onclick` redirections will be handled by HTMX's `hx-get` to trigger full-page navigations.
*   **Primary Data Display:**
    *   `<telerik:RadTreeList ID="RadTreeList1" ...>`: This complex control will be replaced by a standard HTML `<table>` element.
        *   **DataTables.js:** This library will be used to transform the static `<table>` into a dynamic, client-side searchable, sortable, and paginated grid, replacing the Telerik control's built-in features.
        *   **HTMX:** The table content (`<tbody>`) will be loaded as a partial HTML snippet via HTMX, allowing dynamic updates when filters change or data is refreshed.
        *   **Tree Structure Simulation:** While DataTables itself is flat, the presence of `PId` and `CId` in the data allows for client-side tree rendering if a DataTables extension (e.g., `rowGroup`) or a dedicated JS tree library is integrated, or by simply displaying the flattened data. For this migration, we'll focus on the DataTables list view with `PId` and `CId` included for potential future client-side tree rendering.
        *   **Column Customization:** The `onautogeneratedcolumncreated` logic for setting column visibility, width, and alignment will be handled by DataTables configuration and direct CSS/Tailwind in the HTML template.
        *   **Inline Actions:** The "Select" `RadButton` and "Download/Upload" `LinkButton` elements within the `RadTreeList` columns will become HTML `<button>` or `<a>` tags, using HTMX for their dynamic behaviors (e.g., triggering redirects or modal forms).
*   **Loading Indicators:** `RadAjaxLoadingPanel` will be replaced by HTMX's native loading indicators and custom CSS.

## Step 4: Generate Django Code

### 4.1 Models

**Business Value:** By creating Django models that directly map to your existing database tables, we ensure data integrity and reusability. The "Fat Model" approach centralizes complex business logic within the models (e.g., BOM quantity calculations, item type determination), making the system more robust, easier to test, and future-proof.

```python
# design/models.py
from django.db import models
from decimal import Decimal

# Default values for CompId and FinYearId, which would ideally come from
# the authenticated user's session or a global configuration in a real ERP system.
DEFAULT_COMPANY_ID = 1  
DEFAULT_FINANCIAL_YEAR_ID = 2023 

class Unit(models.Model):
    """
    Maps to the existing 'Unit_Master' database table.
    Manages units of measure for various items.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False  # Django will not create or delete this table
        db_table = 'Unit_Master' # Explicitly links to the existing table name
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class Item(models.Model):
    """
    Maps to the existing 'tblDG_Item_Master' database table.
    Stores master data for all items, including codes, descriptions, and attachments.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=100, blank=True, null=True)
    part_no = models.CharField(db_column='PartNo', max_length=100, blank=True, null=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True)
    uom_basic = models.ForeignKey(Unit, models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)
    file_name = models.CharField(db_column='FileName', max_length=255, blank=True, null=True)
    file_data = models.BinaryField(db_column='FileData', blank=True, null=True)
    att_name = models.CharField(db_column='AttName', max_length=255, blank=True, null=True)
    att_data = models.BinaryField(db_column='AttData', blank=True, null=True)
    att_content_type = models.CharField(db_column='AttContentType', max_length=255, blank=True, null=True)
    category_id = models.IntegerField(db_column='CId', blank=True, null=True) # Used for 'BoughtOut' vs 'Manufacturing' logic

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.item_code if self.item_code else (self.part_no if self.part_no else f"Item ID {self.id}")

    @property
    def has_drawing(self):
        """Checks if a drawing file is present and has data."""
        return bool(self.file_name and self.file_data)

    @property
    def has_spec_sheet(self):
        """Checks if a specification sheet is present and has data."""
        return bool(self.att_name and self.att_data)
        
    def get_display_item_code(self):
        """
        Business Logic: Returns ItemCode for 'BoughtOut' items (category_id not null),
        otherwise returns PartNo for 'Manufacturing' items (category_id null).
        """
        return self.item_code if self.category_id is not None else self.part_no


class Bom(models.Model):
    """
    Maps to the existing 'tblDG_BOM_Master' database table.
    Represents a single entry within a Bill of Materials structure, defining parent-child relationships.
    """
    id = models.IntegerField(db_column='CId', primary_key=True) # This is the unique ID of the BOM entry (CId)
    wo_no = models.CharField(db_column='WONo', max_length=100)
    item = models.ForeignKey(Item, models.DO_NOTHING, db_column='ItemId')
    parent = models.ForeignKey('self', models.DO_NOTHING, db_column='PId', blank=True, null=True, related_name='children')
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3)
    revision = models.CharField(db_column='Revision', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', default=DEFAULT_COMPANY_ID)
    fin_year_id = models.IntegerField(db_column='FinYearId', default=DEFAULT_FINANCIAL_YEAR_ID)

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'BOM Entry'
        verbose_name_plural = 'BOM Entries'
        # Consider adding UniqueConstraint if (wo_no, item, parent) or similar is unique in the DB

    def __str__(self):
        return f"BOM: {self.wo_no} - {self.item.get_display_item_code()} (ID: {self.id})"

    def is_standard_item(self):
        """
        Business Logic: Determines if this BOM entry's associated Item is a 'Standard Item'.
        As per the original ASP.NET logic, a 'Standard Item' is one where tblDG_Item_Master.CId is NULL.
        These items typically cannot be modified directly via the 'Sel' command.
        """
        return self.item.category_id is None

    @classmethod
    def get_filtered_bom_data(cls, wo_no, comp_id, fin_year_id, filter_type_val):
        """
        Business Logic: Mimics the complex GetDataTable function from the ASP.NET code-behind.
        It retrieves, filters, processes, and calculates hierarchical BOM data.

        This method encapsulates the recursive BOM explosion and quantity calculation,
        which was originally handled by `fun.BOMTree_Search` and `fun.BOMTreeQty`.
        It returns a flat list of dictionaries suitable for display in DataTables.
        """
        base_qs = cls.objects.select_related('item', 'item__uom_basic').filter(
            wo_no=wo_no,
            comp_id=comp_id,
            fin_year_id__lte=fin_year_id # Assuming 'FinYearId <= finyear' implies a historical or current filter
        )

        # Apply filtering based on the dropdown selection (drpValue)
        if filter_type_val == 1: # 'BoughtOut' (tblDG_Item_Master.CId IS NOT NULL)
            base_qs = base_qs.filter(item__category_id__isnull=False)
        elif filter_type_val == 2: # 'Manufacturing' (tblDG_Item_Master.CId IS NULL)
            base_qs = base_qs.filter(item__category_id__isnull=True)
        # If filter_type_val is 0 (All) or any other value, no additional item category filter is applied.
        
        # Fetch all relevant BOM entries to build the in-memory tree for calculations.
        # Ordering by parent then ID helps in consistent tree traversal.
        bom_entries = list(base_qs.order_by('parent__id', 'id'))
        
        # Create a map for quick lookup of BOM entries by their ID (CId)
        bom_map = {entry.id: entry for entry in bom_entries}
        
        # Cache for BOM Qty calculations to avoid redundant computations on recursive paths
        bom_qty_cache = {}

        def calculate_bom_qty(current_bom_entry_id):
            """
            Recursively calculates the total BOM quantity by multiplying quantities
            up the parent chain. This emulates the original `fun.BOMTreeQty` logic.
            """
            if current_bom_entry_id not in bom_qty_cache:
                current_entry = bom_map.get(current_bom_entry_id)
                if not current_entry:
                    return Decimal('0.000') # Should ideally not occur with consistent data

                current_qty = current_entry.qty
                
                # Multiply by quantities of all ancestors that are part of the current
                # filtered BOM set (i.e., in 'bom_map').
                parent_path_multiplier = Decimal('1.0')
                temp_entry = current_entry
                while temp_entry.parent_id and temp_entry.parent_id in bom_map:
                    parent_entry = bom_map[temp_entry.parent_id]
                    parent_path_multiplier *= parent_entry.qty
                    temp_entry = parent_entry # Move up the tree
                
                bom_qty_cache[current_bom_entry_id] = current_qty * parent_path_multiplier
            return bom_qty_cache[current_bom_entry_id]

        results = []
        for entry in bom_entries:
            item = entry.item
            
            # Format quantities to 3 decimal places, matching ASP.NET's output
            unit_qty_formatted = entry.qty.quantize(Decimal('0.001'))
            bom_qty_formatted = calculate_bom_qty(entry.id).quantize(Decimal('0.001'))

            results.append({
                'ItemId': item.id,
                'WONo': entry.wo_no,
                'PId': entry.parent_id,
                'CId': entry.id,
                'Item Code': item.get_display_item_code(), # Uses business logic from Item model
                'Description': item.manf_desc,
                'UOM': item.uom_basic.symbol if item.uom_basic else '',
                'Unit Qty': unit_qty_formatted,
                'BOM Qty': bom_qty_formatted,
                'FileName': item.file_name,
                'AttName': item.att_name,
                # Determine 'View' or 'Upload' text based on file presence, as in ASP.NET
                'DownloadImgText': 'View' if item.has_drawing else '',
                'UploadImgText': 'Upload' if not item.has_drawing else '',
                'DownloadSpecText': 'View' if item.has_spec_sheet else '',
                'UploadSpecText': 'Upload' if not item.has_spec_sheet else '',
                'Revision': entry.revision,
            })
        
        # Final sort by 'Item Code' as observed in the original ASP.NET application.
        results.sort(key=lambda x: x.get('Item Code', ''))
        return results

```

### 4.2 Forms

**Business Value:** Django's ModelForms automate the process of creating forms from models, significantly reducing boilerplate code. This ensures consistency between your data structure and user input forms, improving data quality and accelerating development. While the original page focuses on display, a `BomForm` is provided for potential future CRUD operations as requested.

```python
# design/forms.py
from django import forms
from .models import Bom, Item, Unit

class BomForm(forms.ModelForm):
    """
    A Django ModelForm for the Bom model.
    While the original ASP.NET page primarily displays data and redirects for actions,
    this form demonstrates how a BOM entry could be added or edited, adhering to CRUD principles.
    """
    class Meta:
        model = Bom
        fields = ['wo_no', 'item', 'parent', 'qty', 'revision']
        widgets = {
            'wo_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'item': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'parent': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'qty': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.001'}), # Ensure decimal precision
            'revision': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Dynamically filter parent choices to ensure consistency, e.g., only BOM entries for the same WO
        # In a real application, you might also prevent circular references.
        if self.instance and self.instance.wo_no:
            self.fields['parent'].queryset = Bom.objects.filter(wo_no=self.instance.wo_no).exclude(pk=self.instance.pk)
        else:
            # For new instances, limit parent options to an existing WO, or provide all valid parents.
            self.fields['parent'].queryset = Bom.objects.all() # Adjust as needed based on context

        # Ensure item choices are available
        self.fields['item'].queryset = Item.objects.all()

```

### 4.3 Views

**Business Value:** Views, as the traffic controllers of your application, are kept thin by delegating all business logic to the models. This strict separation of concerns enhances readability, simplifies debugging, and makes the system easier to scale and maintain. HTMX-driven partial updates drastically improve user experience by providing immediate feedback without full page reloads, making interactions feel fast and seamless.

```python
# design/views.py
from django.views.generic import TemplateView, View
from django.http import HttpResponse, JsonResponse
from django.urls import reverse_lazy
from django.shortcuts import redirect, render, get_object_or_404
from django.contrib import messages
import logging

from .models import Bom, Item, Unit, DEFAULT_COMPANY_ID, DEFAULT_FINANCIAL_YEAR_ID
from .forms import BomForm

logger = logging.getLogger(__name__)

# --- Main BOM Tree View Page ---
class BomTreeView(TemplateView):
    """
    Renders the primary BOM Tree View page. This view handles the initial page load,
    sets up the context for filters (Work Order No, filter dropdown, expand checkbox),
    and is responsible for the overall layout. Data for the table itself is loaded
    via an HTMX request to BomTablePartialView.
    """
    template_name = 'design/bom_tree/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Retrieve 'WONo' from the query string, mimicking ASP.NET's Request.QueryString["WONo"]
        # A default value is provided for demonstration; in production, validate this input.
        context['wo_no'] = self.request.GET.get('WONo', 'DEFAULT_WO_NUMBER') 
        
        # Retrieve 'Msg' from the query string for initial messages
        context['message'] = self.request.GET.get('Msg', '')
        
        # Prepare options for the filter dropdown, similar to ASP.NET DropDownList1
        context['filter_options'] = [
            {'value': 0, 'label': 'All'},
            {'value': 1, 'label': 'BoughtOut'},
            {'value': 2, 'label': 'Manufacturing'},
        ]
        # Set the initially selected filter value based on query string or default
        context['selected_filter'] = int(self.request.GET.get('filter', 0))
        
        # Set the initial state of the expand checkbox
        context['expand_checked'] = self.request.GET.get('expand', 'true').lower() == 'true'

        return context

# --- HTMX Partial View for the DataTables content ---
class BomTablePartialView(View):
    """
    This view is designed to be called via an HTMX request. It fetches and renders
    the dynamic content for the BOM data table, based on the provided filters.
    This ensures that only the table portion of the page is reloaded, optimizing performance.
    """
    def get(self, request, *args, **kwargs):
        # Parameters are received from the HTMX request, mimicking ASP.NET's control states
        wo_no = request.GET.get('wo_no', 'DEFAULT_WO_NUMBER') 
        filter_type = int(request.GET.get('filter', 0))
        expand_all = request.GET.get('expand', 'true').lower() == 'true' # Not directly used for flat DataTables, but passed

        # Retrieve CompId and FinYearId, mimicking ASP.NET Session variables.
        # In a production Django app, these would come from authentication context or user profile.
        comp_id = DEFAULT_COMPANY_ID # Example: request.session.get('compid', DEFAULT_COMPANY_ID)
        fin_year_id = DEFAULT_FINANCIAL_YEAR_ID # Example: request.session.get('finyear', DEFAULT_FINANCIAL_YEAR_ID)
        
        try:
            # Business logic for data retrieval and processing is delegated to the Bom model
            bom_items_data = Bom.get_filtered_bom_data(wo_no, comp_id, fin_year_id, filter_type)
        except Exception as e:
            logger.error(f"Error fetching BOM data for WONo {wo_no}: {e}")
            # Return an informative error message to the user
            return HttpResponse("<p class='text-red-500 p-4'>Error loading BOM data. Please try again.</p>", status=500)

        context = {
            'bom_items': bom_items_data,
            'expand_all': expand_all, # Can be used for future client-side tree expansion logic
            'wo_no': wo_no, # Pass back for context within the partial
        }
        # Render the partial template containing the table
        return render(request, 'design/bom_tree/_bom_table.html', context)

# --- Action Views (Redirections mimicking original ASP.NET navigation) ---
class BomItemSelectRedirectView(View):
    """
    Handles the 'Sel' command from the BOM table.
    It checks if the selected item is a 'Standard Item' (business logic from model)
    and either shows a client-side alert or redirects to the appropriate modification page.
    """
    def get(self, request, item_id, bom_entry_id, parent_id, wo_no):
        try:
            # Use get_object_or_404 for cleaner error handling if object doesn't exist
            bom_entry = get_object_or_404(Bom.objects.select_related('item'), 
                                          id=bom_entry_id, wo_no=wo_no, item__id=item_id)
            
            if bom_entry.is_standard_item(): # Business logic encapsulated in Bom model
                messages.warning(request, "This is a Standard Item. It cannot be modified.")
                # For HTMX requests, return 200 OK with HX-Trigger to inform the frontend
                return HttpResponse(status=200, headers={'HX-Trigger': 'bomItemAlert'})
            else:
                # Redirect to the equivalent Django URL for BOM_WoItems.aspx
                # Placeholder URL; replace with actual Django URL pattern for that module
                target_url = f"/bom_wo_items/edit/?WONo={wo_no}&ItemId={item_id}&PId={parent_id}&CId={bom_entry_id}&ModId=3&SubModId=26"
                return redirect(target_url)
        except Bom.DoesNotExist:
            messages.error(request, "BOM entry not found.")
            return HttpResponse(status=404)
        except Exception as e:
            logger.error(f"Error selecting BOM item (ID: {bom_entry_id}): {e}")
            messages.error(request, "An unexpected error occurred during item selection.")
            return HttpResponse(status=500)


class DownloadFileView(View):
    """
    Handles downloading of associated files (drawings or specification sheets) for an Item.
    Mimics the functionality of DownloadFile.aspx.
    """
    def get(self, request, item_id, file_type): # file_type will be 'img' or 'spec'
        try:
            item = get_object_or_404(Item, id=item_id)
            
            if file_type == 'img':
                file_data = item.file_data
                file_name = item.file_name
                content_type = 'application/octet-stream' # Default if not explicitly stored
            elif file_type == 'spec':
                file_data = item.att_data
                file_name = item.att_name
                content_type = item.att_content_type if item.att_content_type else 'application/octet-stream'
            else:
                messages.error(request, "Invalid file type requested.")
                return HttpResponse("Invalid file type.", status=400)

            if file_data and file_name:
                response = HttpResponse(file_data, content_type=content_type)
                response['Content-Disposition'] = f'attachment; filename="{file_name}"'
                return response
            else:
                messages.info(request, "The requested file is not available.")
                return HttpResponse("File not found.", status=404)
        except Item.DoesNotExist:
            messages.error(request, "Item not found for download.")
            return HttpResponse("Item not found.", status=404)
        except Exception as e:
            logger.error(f"Error downloading file for item {item_id}, type {file_type}: {e}")
            messages.error(request, "An unexpected error occurred during file download.")
            return HttpResponse(status=500)


class UploadDrawingRedirectView(View):
    """
    Redirects to the external (or another Django module's) upload page for drawings/specs.
    Mimics the redirection to BOM_UploadDrw.aspx.
    """
    def get(self, request, item_id, wo_no, img_type): # img_type: 0 for drawing, 1 for spec sheet
        # Construct the target URL. Replace with actual Django URL pattern for BOM_UploadDrw equivalent.
        target_url = f"/bom_upload_drw/?WONo={wo_no}&Id={item_id}&img={img_type}"
        return redirect(target_url)

class AddAssemblyRedirectView(View):
    """
    Redirects to the external (or another Django module's) page for adding a new assembly.
    Mimics the redirection to BOM_Design_Assembly_New.aspx.
    """
    def get(self, request):
        wo_no = request.GET.get('WONo', '')
        # Construct the target URL. Replace with actual Django URL pattern for Add Assembly.
        target_url = f"/bom_design_assembly_new/?WONo={wo_no}&ModId=3&SubModId=26"
        return redirect(target_url)

class CopyFromRedirectView(View):
    """
    Redirects to the external (or another Django module's) page for copying BOMs.
    Mimics the redirection to BOM_Design_Root_Assembly_Copy_WO.aspx.
    """
    def get(self, request):
        wo_no_dest = request.GET.get('WONoDest', '')
        # Construct the target URL. Replace with actual Django URL pattern for Copy From.
        target_url = f"/bom_design_root_assembly_copy_wo/?WONoDest={wo_no_dest}&ModId=3&SubModId=26"
        return redirect(target_url)

class CancelRedirectView(View):
    """
    Redirects back to the main BOM Work Order Grid page.
    Mimics the redirection to BOM_Design_WO_Grid.aspx.
    """
    def get(self, request):
        # Construct the target URL. Replace with actual Django URL pattern for the BOM WO Grid.
        target_url = "/bom_design_wo_grid/?ModId=3&SubModId=26"
        return redirect(target_url)

# --- Placeholder CRUD Views for future expansion/demonstration as per template ---
# These views adhere to the thin view principle by using ModelForms and delegating
# validation and persistence to the form/model. HTMX triggers facilitate dynamic UI updates.
class BomCreateView(View):
    """
    Handles the creation of new BOM entries. Designed to be loaded via HTMX into a modal.
    """
    def get(self, request, *args, **kwargs):
        form = BomForm()
        return render(request, 'design/bom_tree/form.html', {'form': form})
    
    def post(self, request, *args, **kwargs):
        form = BomForm(request.POST)
        if form.is_valid():
            bom_entry = form.save(commit=False)
            # Assign session-based fields (CompId, FinYearId) before saving
            bom_entry.comp_id = DEFAULT_COMPANY_ID 
            bom_entry.fin_year_id = DEFAULT_FINANCIAL_YEAR_ID
            try:
                bom_entry.save()
                messages.success(self.request, 'BOM Entry added successfully.')
                # Return 204 No Content for HTMX to signal success without swapping
                return HttpResponse(status=204, headers={'HX-Trigger': 'refreshBomTreeList'})
            except Exception as e:
                logger.error(f"Error saving new BOM entry: {e}")
                messages.error(self.request, 'An error occurred while saving the BOM entry.')
                return render(request, 'design/bom_tree/form.html', {'form': form}, status=500)
        else:
            messages.error(self.request, 'Please correct the errors below.')
            return render(request, 'design/bom_tree/form.html', {'form': form}, status=400)


class BomUpdateView(View):
    """
    Handles updating existing BOM entries. Designed to be loaded via HTMX into a modal.
    """
    def get(self, request, pk, *args, **kwargs):
        bom_entry = get_object_or_404(Bom, pk=pk)
        form = BomForm(instance=bom_entry)
        return render(request, 'design/bom_tree/form.html', {'form': form, 'bom_entry': bom_entry})
    
    def post(self, request, pk, *args, **kwargs):
        bom_entry = get_object_or_404(Bom, pk=pk)
        form = BomForm(request.POST, instance=bom_entry)
        if form.is_valid():
            try:
                form.save()
                messages.success(self.request, 'BOM Entry updated successfully.')
                return HttpResponse(status=204, headers={'HX-Trigger': 'refreshBomTreeList'})
            except Exception as e:
                logger.error(f"Error updating BOM entry (ID: {pk}): {e}")
                messages.error(self.request, 'An error occurred while updating the BOM entry.')
                return render(request, 'design/bom_tree/form.html', {'form': form, 'bom_entry': bom_entry}, status=500)
        else:
            messages.error(self.request, 'Please correct the errors below.')
            return render(request, 'design/bom_tree/form.html', {'form': form, 'bom_entry': bom_entry}, status=400)


class BomDeleteView(View):
    """
    Handles deleting BOM entries. Designed to be loaded via HTMX into a modal for confirmation.
    """
    def get(self, request, pk, *args, **kwargs):
        bom_entry = get_object_or_404(Bom, pk=pk)
        return render(request, 'design/bom_tree/confirm_delete.html', {'bom_entry': bom_entry})
    
    def post(self, request, pk, *args, **kwargs):
        try:
            bom_entry = get_object_or_404(Bom, pk=pk)
            bom_entry.delete()
            messages.success(self.request, 'BOM Entry deleted successfully.')
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshBomTreeList'})
        except Exception as e:
            logger.error(f"Error deleting BOM entry (ID: {pk}): {e}")
            messages.error(self.request, 'An unexpected error occurred during deletion.')
            return HttpResponse(status=500) # Or 404 if object not found

```

### 4.4 Templates

**Business Value:** Modern, responsive templates enhance user experience. Leveraging template inheritance ensures consistency and reduces code duplication (`DRY` principle). HTMX and Alpine.js create a dynamic single-page application feel, providing instant feedback without full page reloads. DataTables offer powerful client-side data manipulation, such as searching, sorting, and pagination.

```html
<!-- design/templates/design/bom_tree/list.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 space-y-4 md:space-y-0">
        <h2 class="text-2xl font-bold text-gray-800">BOM Design Work Order Tree View</h2>
        <div class="flex items-center space-x-4">
            <span class="font-semibold text-gray-700">Wo No:</span>
            <span id="woNumberLabel" class="text-blue-600 font-medium">{{ wo_no }}</span>
        </div>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 items-center">
            <div class="col-span-1">
                <label for="filterDropdown" class="block text-sm font-medium text-gray-700 mb-1">Filter Type:</label>
                <select id="filterDropdown" name="filter" 
                        class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        hx-get="{% url 'design:bom_tree_table' %}"
                        hx-target="#bomTableContainer"
                        hx-swap="innerHTML"
                        hx-indicator="#loadingIndicator"
                        hx-params="wo_no,expand,filter"
                        hx-push-url="true"
                        hx-replace-url="true">
                    {% for option in filter_options %}
                    <option value="{{ option.value }}" {% if option.value == selected_filter %}selected{% endif %}>{{ option.label }}</option>
                    {% endfor %}
                </select>
                {% if message %}
                <p id="messageLabel" class="text-red-600 font-bold mt-2 text-sm">{{ message }}</p>
                {% endif %}
            </div>
            <div class="col-span-1 md:col-span-3 flex flex-col md:flex-row items-start md:items-center justify-end space-y-3 md:space-y-0 md:space-x-4">
                <label class="flex items-center space-x-2 cursor-pointer text-gray-700">
                    <input type="checkbox" id="expandCheckbox" name="expand" class="form-checkbox h-4 w-4 text-blue-600 rounded"
                           {% if expand_checked %}checked{% endif %}
                           hx-get="{% url 'design:bom_tree_table' %}"
                           hx-target="#bomTableContainer"
                           hx-swap="innerHTML"
                           hx-indicator="#loadingIndicator"
                           hx-params="wo_no,filter,expand"
                           hx-push-url="true"
                           hx-replace-url="true">
                    <span class="font-bold">Expand Tree</span>
                </label>
                <button
                    class="redbox bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                    hx-get="{% url 'design:add_assembly_redirect' %}?WONo={{ wo_no }}"
                    hx-target="body" hx-swap="none">
                    Add Assembly
                </button>
                <button
                    class="redbox bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                    hx-get="{% url 'design:copy_from_redirect' %}?WONoDest={{ wo_no }}"
                    hx-target="body" hx-swap="none">
                    Copy From
                </button>
                <button
                    class="redbox bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                    hx-get="{% url 'design:cancel_redirect' %}"
                    hx-target="body" hx-swap="none">
                    Cancel
                </button>
            </div>
        </div>
    </div>
    
    <!-- Loading indicator for HTMX requests -->
    <div id="loadingIndicator" class="htmx-indicator flex items-center justify-center py-4">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="ml-3 text-lg text-gray-600">Loading BOM data...</p>
    </div>

    <!-- Container for the BOM DataTables content, loaded via HTMX -->
    <div id="bomTableContainer"
         hx-trigger="load, refreshBomTreeList from:body"
         hx-get="{% url 'design:bom_tree_table' %}"
         hx-target="#bomTableContainer"
         hx-swap="innerHTML"
         hx-indicator="#loadingIndicator"
         hx-params="wo_no,filter,expand">
        <!-- Initial loading state/placeholder. The DataTables content will be loaded here. -->
        <div class="text-center py-10 text-gray-500">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Fetching BOM data...</p>
        </div>
    </div>
    
    <!-- Modal for displaying forms (Add/Edit/Delete) dynamically -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden"
         x-data="{ showModal: false }"
         x-show="showModal"
         @refreshbomtreelist.window="showModal = false" {# Close modal when list refreshes #}
         @bomitemalert.window="alert('This is Standard Item. It cannot be modified.'); showModal = false;" {# Show alert and close modal #}
         x-init="$watch('$store.modal.isOpen', val => showModal = val)" {# Sync with Alpine store #}
         _="on click if event.target.id == 'modal' $store.modal.close()"> {# Click outside to close #}
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4 transform transition-all sm:my-8 sm:align-middle sm:w-full sm:max-w-md"
             _="on htmx:afterOnLoad add .is-active to #modal then $store.modal.open()"> {# Open modal after HTMX loads content #}
             <!-- Form or delete confirmation content will be loaded here -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# DataTables CDN links for client-side functionality #}
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.tailwindcss.min.js"></script>
<link href="https://cdn.datatables.net/2.0.7/css/dataTables.dataTables.min.css" rel="stylesheet" />
<link href="https://cdn.datatables.net/2.0.7/css/dataTables.tailwindcss.min.css" rel="stylesheet" />

<script>
    // Alpine.js store for modal state management
    document.addEventListener('alpine:init', () => {
        Alpine.store('modal', {
            isOpen: false,
            open() { this.isOpen = true },
            close() { this.isOpen = false }
        });
    });

    // Event listener to re-initialize DataTables after HTMX swaps content.
    // This is crucial because HTMX replaces the inner HTML, which destroys the DataTables instance.
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'bomTableContainer') {
            const table = $('#bomTreeTable'); 
            // Destroy any existing DataTables instance before re-initializing
            if ($.fn.DataTable.isDataTable(table)) {
                table.DataTable().destroy();
            }
            // Initialize DataTables with desired options
            table.DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "processing": false, // Set to true if server-side processing is used
                "responsive": true,
                "autoWidth": false,
                "ordering": true,
                "searching": true,
                // Add any column definitions for custom sorting/visibility if needed
                // "columnDefs": [ { "orderable": false, "targets": [1, 7, 8] } ] // Example: disable sorting for 'Select' and 'Drw/Image' columns
            });
        }
    });

    // Initial DataTables setup on page load for when content might be pre-rendered or for initial load
    $(document).ready(function() {
        // Only initialize if the table element actually exists within the container
        if ($('#bomTableContainer table').length) { 
            const table = $('#bomTreeTable');
            if (!$.fn.DataTable.isDataTable(table)) {
                table.DataTable({
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "processing": false,
                    "responsive": true,
                    "autoWidth": false,
                    "ordering": true,
                    "searching": true,
                });
            }
        }
    });
</script>
{% endblock %}

```

```html
<!-- design/templates/design/bom_tree/_bom_table.html -->
<!-- This partial template renders the content of the BOM data table. -->
<!-- It is intended to be loaded dynamically into `bomTableContainer` via HTMX. -->

<div class="overflow-x-auto bg-white rounded-lg shadow overflow-y-auto relative" style="height: 277px;">
    <table id="bomTreeTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50 sticky top-0">
            <tr>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">Select</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">Item Code</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16 text-center">UOM</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16 text-right">Unit Qty</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16 text-right">BOM Qty</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24 text-center">Drw/Image</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24 text-center">Spec. Sheet</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">Revision</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for item in bom_items %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900 text-center">
                    <button class="focus:outline-none p-1 rounded hover:bg-gray-100 transition-colors"
                            hx-get="{% url 'design:bom_item_select_redirect' item_id=item.ItemId bom_entry_id=item.CId parent_id=item.PId|default:0 wo_no=item.WONo %}"
                            hx-target="body" hx-swap="none" {# We want a redirect or an alert #}
                            hx-on--after-request="if (event.detail.xhr.status === 200) {$store.modal.close();}" {# Close modal if it was open #}
                            title="Select Item">
                        <img src="/static/images/Add.PNG" alt="Select" class="w-4 h-4 mx-auto" />
                    </button>
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ item.'Item Code' }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ item.Description }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900 text-center">{{ item.UOM }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900 text-right">{{ item.'Unit Qty' }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900 text-right">{{ item.'BOM Qty' }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900 text-center">
                    {% if item.DownloadImgText %}
                        <a href="{% url 'design:download_file' item_id=item.ItemId file_type='img' %}" class="text-blue-600 hover:underline">
                            {{ item.DownloadImgText }}
                        </a>
                    {% elif item.UploadImgText %}
                        <a href="{% url 'design:upload_drawing_redirect' item_id=item.ItemId wo_no=item.WONo img_type=0 %}" class="text-green-600 hover:underline">
                            {{ item.UploadImgText }}
                        </a>
                    {% endif %}
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900 text-center">
                    {% if item.DownloadSpecText %}
                        <a href="{% url 'design:download_file' item_id=item.ItemId file_type='spec' %}" class="text-blue-600 hover:underline">
                            {{ item.DownloadSpecText }}
                        </a>
                    {% elif item.UploadSpecText %}
                        <a href="{% url 'design:upload_drawing_redirect' item_id=item.ItemId wo_no=item.WONo img_type=1 %}" class="text-green-600 hover:underline">
                            {{ item.UploadSpecText }}
                        </a>
                    {% endif %}
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ item.Revision }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="10" class="py-4 text-center text-gray-500">No BOM entries found for the selected Work Order and filters.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
```

```html
<!-- design/templates/design/bom_tree/form.html -->
<!-- This is a partial template intended to be loaded into a modal via HTMX -->
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} BOM Entry</h3>
    <form hx-post="{{ request.path }}" hx-swap="none"> {# hx-swap="none" tells HTMX not to swap content #}
        {% csrf_token %} {# Django's Cross-Site Request Forgery protection #}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }} {# Renders the input widget defined in the form #}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="list-none p-0 mt-1">
                    {% for error in field.errors %}
                    <li class="text-red-500 text-xs">{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                hx-on:click="$store.modal.close()"> {# Use Alpine.js to close the modal #}
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

```html
<!-- design/templates/design/bom_tree/confirm_delete.html -->
<!-- This is a partial template for delete confirmation, loaded into a modal via HTMX -->
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the BOM entry for 
        <strong>{{ bom_entry.item.get_display_item_code }}</strong> (WONo: {{ bom_entry.wo_no }})?
        This action cannot be undone.
    </p>
    
    <form hx-post="{{ request.path }}" hx-swap="none"> {# hx-swap="none" for deletion #}
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                hx-on:click="$store.modal.close()"> {# Use Alpine.js to close the modal #}
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs

**Business Value:** Clearly defined URL patterns improve the application's navigability and maintainability. Using Django's URL namespace and named URLs prevents hardcoding paths, making your application more flexible to future changes and reducing technical debt.

```python
# design/urls.py
from django.urls import path
from .views import (
    BomTreeView, BomTablePartialView,
    BomItemSelectRedirectView, DownloadFileView, UploadDrawingRedirectView,
    AddAssemblyRedirectView, CopyFromRedirectView, CancelRedirectView,
    BomCreateView, BomUpdateView, BomDeleteView # Placeholder CRUD views
)

app_name = 'design' # Namespace for this application's URLs to prevent conflicts

urlpatterns = [
    # Main BOM Tree View
    path('bom-tree/', BomTreeView.as_view(), name='bom_tree_list'),
    path('bom-tree/table/', BomTablePartialView.as_view(), name='bom_tree_table'),

    # Action Redirections (mimicking original ASP.NET navigation patterns)
    # Note: 'parent_id' is optional as root items have no parent. Using default:0 for URL parsing flexibility.
    path('bom-tree/select-item/<str:wo_no>/<int:item_id>/<int:parent_id>/<int:bom_entry_id>/', 
         BomItemSelectRedirectView.as_view(), name='bom_item_select_redirect'),
    path('bom-tree/download-file/<int:item_id>/<str:file_type>/', 
         DownloadFileView.as_view(), name='download_file'), # file_type: 'img' or 'spec'
    path('bom-tree/upload-drawing/<str:wo_no>/<int:item_id>/<int:img_type>/', 
         UploadDrawingRedirectView.as_view(), name='upload_drawing_redirect'), # img_type: 0 (drawing), 1 (spec sheet)
    
    # Generic button click redirects
    path('bom-tree/add-assembly/', AddAssemblyRedirectView.as_view(), name='add_assembly_redirect'),
    path('bom-tree/copy-from/', CopyFromRedirectView.as_view(), name='copy_from_redirect'),
    path('bom-tree/cancel/', CancelRedirectView.as_view(), name='cancel_redirect'),

    # Placeholder CRUD operations for Bom entries (as per template instructions)
    path('bom-tree/add/', BomCreateView.as_view(), name='bom_tree_add'),
    path('bom-tree/edit/<int:pk>/', BomUpdateView.as_view(), name='bom_tree_edit'),
    path('bom-tree/delete/<int:pk>/', BomDeleteView.as_view(), name='bom_tree_delete'),
]

```

### 4.6 Tests

**Business Value:** Comprehensive unit and integration tests are critical for ensuring software quality. They validate that business logic is correctly implemented (models) and that the application behaves as expected (views), especially with dynamic HTMX interactions. This reduces bugs, simplifies refactoring, and builds confidence in the migration process, contributing to significant long-term cost savings.

```python
# design/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from decimal import Decimal

from .models import Unit, Item, Bom, DEFAULT_COMPANY_ID, DEFAULT_FINANCIAL_YEAR_ID

class UnitModelTest(TestCase):
    """
    Unit tests for the Unit model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create sample Unit instances for testing
        Unit.objects.create(id=1, symbol='MTR')
        Unit.objects.create(id=2, symbol='PCS')

    def test_unit_creation(self):
        """Verify Unit objects are created correctly."""
        unit1 = Unit.objects.get(id=1)
        self.assertEqual(unit1.symbol, 'MTR')
        self.assertEqual(str(unit1), 'MTR') # Test __str__ method

    def test_unit_verbose_name(self):
        """Verify verbose names for the Unit model."""
        self.assertEqual(Unit._meta.verbose_name, 'Unit')
        self.assertEqual(Unit._meta.verbose_name_plural, 'Units')

class ItemModelTest(TestCase):
    """
    Unit tests for the Item model, including properties and custom methods.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a Unit instance required as a ForeignKey for Item
        Unit.objects.create(id=1, symbol='PCS')
        
        # Create various Item instances to test different scenarios
        Item.objects.create(id=101, item_code='ITEM001', part_no='PART-A', manf_desc='Description A', uom_basic_id=1,
                            file_name='drawingA.pdf', file_data=b'pdf_data_a', att_name='specA.doc', att_data=b'doc_data_a', att_content_type='application/msword', category_id=1) # BoughtOut, with files
        Item.objects.create(id=102, item_code='ITEM002', part_no='PART-B', manf_desc='Description B', uom_basic_id=1,
                            file_name=None, file_data=None, att_name=None, att_data=None, category_id=None) # Manufacturing, no files
        Item.objects.create(id=103, item_code='ITEM003', part_no='PART-C', manf_desc='Description C', uom_basic_id=1,
                            file_name='drawingC.jpg', file_data=b'jpg_data_c', att_name=None, att_data=None, category_id=2) # BoughtOut, drawing only

    def test_item_creation(self):
        """Verify Item objects are created with correct attributes."""
        item1 = Item.objects.get(id=101)
        self.assertEqual(item1.item_code, 'ITEM001')
        self.assertEqual(item1.manf_desc, 'Description A')
        self.assertTrue(item1.has_drawing)
        self.assertTrue(item1.has_spec_sheet)

    def test_item_no_drawing_no_spec(self):
        """Verify `has_drawing` and `has_spec_sheet` properties when no files exist."""
        item2 = Item.objects.get(id=102)
        self.assertFalse(item2.has_drawing)
        self.assertFalse(item2.has_spec_sheet)

    def test_item_drawing_only(self):
        """Verify `has_drawing` and `has_spec_sheet` properties when only drawing exists."""
        item3 = Item.objects.get(id=103)
        self.assertTrue(item3.has_drawing)
        self.assertFalse(item3.has_spec_sheet)

    def test_get_display_item_code(self):
        """Verify `get_display_item_code` method returns correct code based on category_id."""
        item1 = Item.objects.get(id=101) # BoughtOut (category_id not null)
        item2 = Item.objects.get(id=102) # Manufacturing (category_id is null)
        self.assertEqual(item1.get_display_item_code(), 'ITEM001')
        self.assertEqual(item2.get_display_item_code(), 'PART-B')

class BomModelTest(TestCase):
    """
    Unit tests for the Bom model, focusing on hierarchical data and calculation methods.
    """
    @classmethod
    def setUpTestData(cls):
        # Create necessary related objects
        Unit.objects.create(id=1, symbol='PCS')
        Item.objects.create(id=201, item_code='PROD-ROOT', manf_desc='Root Product', uom_basic_id=1, category_id=None) # Manufacturing (Standard Item)
        Item.objects.create(id=202, item_code='COMP-A', manf_desc='Component A', uom_basic_id=1, category_id=1) # BoughtOut
        Item.objects.create(id=203, item_code='COMP-B', manf_desc='Component B', uom_basic_id=1, category_id=1) # BoughtOut
        Item.objects.create(id=204, item_code='SUB-COMP-A1', manf_desc='Sub Component A1', uom_basic_id=1, category_id=2) # BoughtOut
        
        # Create a sample BOM structure for 'WO_TEST' to test get_filtered_bom_data and quantity calculations
        # Structure:
        # ROOT (ID: 1, Item: PROD-ROOT, Qty: 1)
        #   |-- COMP-A (ID: 2, Item: COMP-A, Qty: 2)
        #   |    `-- SUB-COMP-A1 (ID: 4, Item: SUB-COMP-A1, Qty: 3)
        #   `-- COMP-B (ID: 3, Item: COMP-B, Qty: 1)
        
        cls.bom_root = Bom.objects.create(id=1, wo_no='WO_TEST', item_id=201, parent=None, qty=Decimal('1.0'), revision='R1', comp_id=DEFAULT_COMPANY_ID, fin_year_id=DEFAULT_FINANCIAL_YEAR_ID)
        cls.bom_comp_a = Bom.objects.create(id=2, wo_no='WO_TEST', item_id=202, parent=cls.bom_root, qty=Decimal('2.0'), revision='R1', comp_id=DEFAULT_COMPANY_ID, fin_year_id=DEFAULT_FINANCIAL_YEAR_ID)
        cls.bom_comp_b = Bom.objects.create(id=3, wo_no='WO_TEST', item_id=203, parent=cls.bom_root, qty=Decimal('1.0'), revision='R1', comp_id=DEFAULT_COMPANY_ID, fin_year_id=DEFAULT_FINANCIAL_YEAR_ID)
        cls.bom_sub_comp_a1 = Bom.objects.create(id=4, wo_no='WO_TEST', item_id=204, parent=cls.bom_comp_a, qty=Decimal('3.0'), revision='R1', comp_id=DEFAULT_COMPANY_ID, fin_year_id=DEFAULT_FINANCIAL_YEAR_ID)

        # Create another BOM entry for a different WO to ensure filtering works
        Bom.objects.create(id=5, wo_no='WO_OTHER', item_id=202, parent=None, qty=Decimal('1.0'), revision='R1', comp_id=DEFAULT_COMPANY_ID, fin_year_id=DEFAULT_FINANCIAL_YEAR_ID)

    def test_bom_creation(self):
        """Verify Bom objects are created with correct relationships and attributes."""
        self.assertEqual(self.bom_root.wo_no, 'WO_TEST')
        self.assertIsNone(self.bom_root.parent)
        self.assertEqual(self.bom_comp_a.parent, self.bom_root)
        self.assertEqual(self.bom_sub_comp_a1.qty, Decimal('3.0'))

    def test_is_standard_item(self):
        """Verify `is_standard_item` method correctly identifies standard vs. non-standard items."""
        # Item 201 (PROD-ROOT) has category_id=None, so its BOM entry should be standard
        self.assertTrue(self.bom_root.is_standard_item())
        # Item 202 (COMP-A) has category_id=1, so its BOM entry should not be standard
        self.assertFalse(self.bom_comp_a.is_standard_item())

    def test_get_filtered_bom_data_all_filter(self):
        """Test `get_filtered_bom_data` with 'All' filter type."""
        data = Bom.get_filtered_bom_data('WO_TEST', DEFAULT_COMPANY_ID, DEFAULT_FINANCIAL_YEAR_ID, 0)
        self.assertEqual(len(data), 4) # Should include all 4 items in 'WO_TEST'

        # Verify BOM Qty calculations (cumulative quantities from root)
        root_data = next(item for item in data if item['CId'] == self.bom_root.id)
        comp_a_data = next(item for item in data if item['CId'] == self.bom_comp_a.id)
        comp_b_data = next(item for item in data if item['CId'] == self.bom_comp_b.id)
        sub_comp_a1_data = next(item for item in data if item['CId'] == self.bom_sub_comp_a1.id)

        self.assertEqual(root_data['BOM Qty'], Decimal('1.000'))
        self.assertEqual(comp_a_data['BOM Qty'], Decimal('2.000')) # 2.0 * 1.0 (from root)
        self.assertEqual(comp_b_data['BOM Qty'], Decimal('1.000')) # 1.0 * 1.0 (from root)
        self.assertEqual(sub_comp_a1_data['BOM Qty'], Decimal('6.000')) # 3.0 * 2.0 (from COMP-A) * 1.0 (from root)

    def test_get_filtered_bom_data_bought_out_filter(self):
        """Test `get_filtered_bom_data` with 'BoughtOut' filter type."""
        # 'BoughtOut' items have non-null category_id: COMP-A (id=202), COMP-B (id=203), SUB-COMP-A1 (id=204)
        data = Bom.get_filtered_bom_data('WO_TEST', DEFAULT_COMPANY_ID, DEFAULT_FINANCIAL_YEAR_ID, 1)
        self.assertEqual(len(data), 3) # Should exclude PROD-ROOT (category_id=None)
        item_codes = {item['Item Code'] for item in data}
        self.assertIn('COMP-A', item_codes)
        self.assertIn('COMP-B', item_codes)
        self.assertIn('SUB-COMP-A1', item_codes)
        self.assertNotIn('PROD-ROOT', item_codes)

    def test_get_filtered_bom_data_manufacturing_filter(self):
        """Test `get_filtered_bom_data` with 'Manufacturing' filter type."""
        # 'Manufacturing' items have null category_id: PROD-ROOT (id=201)
        data = Bom.get_filtered_bom_data('WO_TEST', DEFAULT_COMPANY_ID, DEFAULT_FINANCIAL_YEAR_ID, 2)
        self.assertEqual(len(data), 1) # Should only include PROD-ROOT
        self.assertEqual(data[0]['Item Code'], 'PROD-ROOT')

class BomViewsTest(TestCase):
    """
    Integration tests for Bom-related views, covering page rendering, HTMX interactions,
    redirects, and basic CRUD operations.
    """
    @classmethod
    def setUpTestData(cls):
        # Setup common data needed for view tests
        Unit.objects.create(id=1, symbol='PCS')
        Item.objects.create(id=301, item_code='PROD-X', manf_desc='Product X', uom_basic_id=1, category_id=None) # Standard item
        Item.objects.create(id=302, item_code='COMP-Y', manf_desc='Component Y', uom_basic_id=1, category_id=1) # Non-standard item
        Item.objects.create(id=303, item_code='IMAGE_ITEM', manf_desc='Item with image', uom_basic_id=1, file_name='image.jpg', file_data=b'imagedata', category_id=1)
        Item.objects.create(id=304, item_code='SPEC_ITEM', manf_desc='Item with spec', uom_basic_id=1, att_name='spec.pdf', att_data=b'specdata', att_content_type='application/pdf', category_id=1)

        cls.bom_entry_standard = Bom.objects.create(id=10, wo_no='WO_VIEW', item_id=301, parent=None, qty=Decimal('1.0'), revision='A', comp_id=DEFAULT_COMPANY_ID, fin_year_id=DEFAULT_FINANCIAL_YEAR_ID)
        cls.bom_entry_non_standard = Bom.objects.create(id=11, wo_no='WO_VIEW', item_id=302, parent=None, qty=Decimal('2.0'), revision='B', comp_id=DEFAULT_COMPANY_ID, fin_year_id=DEFAULT_FINANCIAL_YEAR_ID)
        cls.bom_entry_image = Bom.objects.create(id=12, wo_no='WO_VIEW', item_id=303, parent=None, qty=Decimal('1.0'), revision='C', comp_id=DEFAULT_COMPANY_ID, fin_year_id=DEFAULT_FINANCIAL_YEAR_ID)
        cls.bom_entry_spec = Bom.objects.create(id=13, wo_no='WO_VIEW', item_id=304, parent=None, qty=Decimal('1.0'), revision='D', comp_id=DEFAULT_COMPANY_ID, fin_year_id=DEFAULT_FINANCIAL_YEAR_ID)

    def setUp(self):
        self.client = Client()

    def test_bom_tree_list_view_get(self):
        """Test the main BOM list page loads correctly and includes initial context."""
        response = self.client.get(reverse('design:bom_tree_list'), {'WONo': 'WO_VIEW', 'Msg': 'Test Message'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/bom_tree/list.html')
        self.assertContains(response, 'BOM Design Work Order Tree View')
        self.assertContains(response, 'WO_VIEW')
        self.assertContains(response, 'Test Message')
        # Verify that the HTMX container for the table is present
        self.assertContains(response, '<div id="bomTableContainer"')

    def test_bom_tree_table_partial_view_htmx(self):
        """Test that the HTMX-loaded table partial renders correctly with data."""
        response = self.client.get(
            reverse('design:bom_tree_table'),
            {'wo_no': 'WO_VIEW', 'filter': 0, 'expand': 'true'},
            HTTP_HX_REQUEST='true' # Simulate an HTMX request
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/bom_tree/_bom_table.html')
        self.assertContains(response, 'PROD-X')
        self.assertContains(response, 'COMP-Y')
        self.assertContains(response, 'image.jpg') # Verify view/upload logic for files
        self.assertContains(response, 'spec.pdf')

    def test_bom_item_select_redirect_standard_item(self):
        """Test 'Select' action for a Standard Item, expecting an alert."""
        response = self.client.get(
            reverse('design:bom_item_select_redirect', kwargs={
                'item_id': self.bom_entry_standard.item.id,
                'bom_entry_id': self.bom_entry_standard.id,
                'parent_id': 0, # Assuming 0 for root or actual PId
                'wo_no': self.bom_entry_standard.wo_no
            }),
            HTTP_HX_REQUEST='true' # Simulate HTMX request for the trigger
        )
        # Should return 200 OK and trigger a client-side alert via HX-Trigger
        self.assertEqual(response.status_code, 200)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('bomItemAlert', response.headers['HX-Trigger'])
        
        # Verify Django messages framework received the warning
        messages_list = list(response.wsgi_request._messages)
        self.assertEqual(len(messages_list), 1)
        self.assertEqual(str(messages_list[0]), "This is a Standard Item. It cannot be modified.")

    def test_bom_item_select_redirect_non_standard_item(self):
        """Test 'Select' action for a Non-Standard Item, expecting a redirect."""
        response = self.client.get(
            reverse('design:bom_item_select_redirect', kwargs={
                'item_id': self.bom_entry_non_standard.item.id,
                'bom_entry_id': self.bom_entry_non_standard.id,
                'parent_id': 0,
                'wo_no': self.bom_entry_non_standard.wo_no
            })
        )
        # Should redirect to the target URL for item modification
        self.assertEqual(response.status_code, 302)
        expected_url = f"/bom_wo_items/edit/?WONo=WO_VIEW&ItemId={self.bom_entry_non_standard.item.id}&PId=0&CId={self.bom_entry_non_standard.id}&ModId=3&SubModId=26"
        self.assertRedirects(response, expected_url, fetch_redirect_response=False)

    def test_download_file_image(self):
        """Test downloading an image file."""
        response = self.client.get(reverse('design:download_file', kwargs={'item_id': self.bom_entry_image.item.id, 'file_type': 'img'}))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/octet-stream') # Default if not explicitly stored
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="image.jpg"')
        self.assertEqual(response.content, b'imagedata')

    def test_download_file_spec(self):
        """Test downloading a specification sheet file."""
        response = self.client.get(reverse('design:download_file', kwargs={'item_id': self.bom_entry_spec.item.id, 'file_type': 'spec'}))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/pdf') # Specific content type for spec
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="spec.pdf"')
        self.assertEqual(response.content, b'specdata')

    def test_download_file_not_found(self):
        """Test downloading a file that doesn't exist for an item."""
        # Item with no image data
        response = self.client.get(reverse('design:download_file', kwargs={'item_id': self.bom_entry_non_standard.item.id, 'file_type': 'img'}))
        self.assertEqual(response.status_code, 404)
        self.assertContains(response, 'File not found.')
        messages_list = list(response.wsgi_request._messages)
        self.assertEqual(len(messages_list), 1)
        self.assertEqual(str(messages_list[0]), "The requested file is not available.")


    def test_upload_drawing_redirect(self):
        """Test redirection to the upload drawing page."""
        response = self.client.get(reverse('design:upload_drawing_redirect', kwargs={'wo_no': 'WO_UPLOAD', 'item_id': 999, 'img_type': 0}))
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, "/bom_upload_drw/?WONo=WO_UPLOAD&Id=999&img=0", fetch_redirect_response=False)

    def test_add_assembly_redirect(self):
        """Test redirection for 'Add Assembly' button."""
        response = self.client.get(reverse('design:add_assembly_redirect'), {'WONo': 'WO_ADD'})
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, "/bom_design_assembly_new/?WONo=WO_ADD&ModId=3&SubModId=26", fetch_redirect_response=False)

    def test_copy_from_redirect(self):
        """Test redirection for 'Copy From' button."""
        response = self.client.get(reverse('design:copy_from_redirect'), {'WONoDest': 'WO_COPY'})
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, "/bom_design_root_assembly_copy_wo/?WONoDest=WO_COPY&ModId=3&SubModId=26", fetch_redirect_response=False)

    def test_cancel_redirect(self):
        """Test redirection for 'Cancel' button."""
        response = self.client.get(reverse('design:cancel_redirect'))
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, "/bom_design_wo_grid/?ModId=3&SubModId=26", fetch_redirect_response=False)

    # --- CRUD View Tests (Placeholders as per template instructions) ---
    def test_bom_create_view_get(self):
        """Test GET request for the BOM creation form (modal)."""
        response = self.client.get(reverse('design:bom_tree_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/bom_tree/form.html')
        self.assertContains(response, 'Add BOM Entry')
        self.assertContains(response, '<form hx-post=')

    def test_bom_create_view_post_valid(self):
        """Test valid POST request for creating a new BOM entry via HTMX."""
        item = Item.objects.get(id=302) # Use an existing item
        initial_bom_count = Bom.objects.count()
        data = {
            'wo_no': 'WO_NEW',
            'item': item.id,
            'parent': '', # No parent for this test root item
            'qty': '1.500',
            'revision': 'R0'
        }
        response = self.client.post(reverse('design:bom_tree_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX expects 204 No Content for successful swap="none"
        self.assertEqual(Bom.objects.count(), initial_bom_count + 1)
        new_bom = Bom.objects.get(wo_no='WO_NEW', item=item, qty=Decimal('1.500'))
        self.assertEqual(new_bom.revision, 'R0')
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshBomTreeList', response.headers['HX-Trigger']) # Verify refresh trigger

    def test_bom_create_view_post_invalid(self):
        """Test invalid POST request for BOM creation."""
        initial_bom_count = Bom.objects.count()
        data = {
            'wo_no': '', # Missing required field
            'item': '', # Missing required field
            'qty': 'abc', # Invalid quantity
            'revision': 'R0'
        }
        response = self.client.post(reverse('design:bom_tree_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400) # Expect HTTP 400 Bad Request due to form errors
        self.assertEqual(Bom.objects.count(), initial_bom_count) # No new object created
        self.assertTemplateUsed(response, 'design/bom_tree/form.html') # Form re-rendered with errors
        self.assertContains(response, 'Please correct the errors below.')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Enter a number.')

    def test_bom_update_view_get(self):
        """Test GET request for the BOM update form (modal)."""
        response = self.client.get(reverse('design:bom_tree_edit', args=[self.bom_entry_non_standard.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/bom_tree/form.html')
        self.assertContains(response, 'Edit BOM Entry')
        self.assertContains(response, 'COMP-Y') # Check if existing data is pre-filled

    def test_bom_update_view_post_valid(self):
        """Test valid POST request for updating an existing BOM entry via HTMX."""
        data = {
            'wo_no': self.bom_entry_non_standard.wo_no,
            'item': self.bom_entry_non_standard.item.id,
            'parent': '',
            'qty': '2.500', # Changed quantity
            'revision': 'C' # Changed revision
        }
        response = self.client.post(reverse('design:bom_tree_edit', args=[self.bom_entry_non_standard.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        updated_bom = Bom.objects.get(id=self.bom_entry_non_standard.id)
        self.assertEqual(updated_bom.qty, Decimal('2.500'))
        self.assertEqual(updated_bom.revision, 'C')
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshBomTreeList', response.headers['HX-Trigger'])

    def test_bom_delete_view_get(self):
        """Test GET request for the delete confirmation form (modal)."""
        response = self.client.get(reverse('design:bom_tree_delete', args=[self.bom_entry_image.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/bom_tree/confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, 'IMAGE_ITEM') # Confirm item details are displayed

    def test_bom_delete_view_post_valid(self):
        """Test valid POST request for deleting a BOM entry via HTMX."""
        initial_bom_count = Bom.objects.count()
        response = self.client.post(reverse('design:bom_tree_delete', args=[self.bom_entry_image.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(Bom.objects.count(), initial_bom_count - 1)
        self.assertFalse(Bom.objects.filter(id=self.bom_entry_image.id).exists()) # Verify deletion
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshBomTreeList', response.headers['HX-Trigger'])

    def test_bom_delete_view_post_not_found(self):
        """Test POST request for deleting a non-existent BOM entry."""
        initial_bom_count = Bom.objects.count()
        response = self.client.post(reverse('design:bom_tree_delete', args=[9999]), HTTP_HX_REQUEST='true') # Non-existent PK
        self.assertEqual(response.status_code, 404)
        self.assertEqual(Bom.objects.count(), initial_bom_count) # Count should remain unchanged
        messages_list = list(response.wsgi_request._messages)
        self.assertEqual(len(messages_list), 1)
        self.assertEqual(str(messages_list[0]), "BOM entry not found for deletion.")

```

## Step 5: HTMX and Alpine.js Integration

**Business Value:** This modern frontend stack significantly enhances the user experience by delivering a highly responsive and interactive interface. Users benefit from immediate feedback and partial page updates, mimicking a single-page application (SPA) without the complexity typically associated with full JavaScript frameworks. This leads to faster perceived performance and a more fluid interaction.

*   **HTMX for Dynamic Updates:**
    *   **Initial Table Load & Refresh:** The main `list.html` uses `hx-get` on `load` and on a custom event `refreshBomTreeList` (triggered after CRUD operations) to dynamically fetch and insert the `_bom_table.html` partial into the `bomTableContainer`. This ensures the table always displays the latest data.
    *   **Filtering & Expansion:** The `filterDropdown` and `expandCheckbox` use `hx-get` to trigger new requests to `{% url 'design:bom_tree_table' %}`. They pass the current filter and expansion states as query parameters (`hx-params="wo_no,expand,filter"`), causing the table to reload dynamically based on user selection.
    *   **Action Buttons:**
        *   Buttons like "Add Assembly", "Copy From", and "Cancel" use `hx-get` with `hx-target="body"` and `hx-swap="none"` to trigger full page navigations to their respective Django redirect views, mimicking the original ASP.NET `Response.Redirect` behavior.
        *   Inline "Select", "Download", and "Upload" links within the `_bom_table.html` also use `hx-get` to trigger their respective views, handling redirects or showing modals.
    *   **Modal Forms (CRUD):** The "Add New BOM", "Edit", and "Delete" buttons trigger `hx-get` requests to their respective URLs, targeting `#modalContent` and using Alpine.js to open the modal. Form submissions within these modals use `hx-post` with `hx-swap="none"`. Upon successful submission (e.g., creating/updating/deleting a `Bom` object), the view responds with an `HTTP 204 No Content` status and an `HX-Trigger` header set to `refreshBomTreeList`. This header instructs the main `list.html` to re-fetch and re-render the `bomTableContainer`, effectively refreshing the data without a full page reload.
*   **Alpine.js for UI State Management:**
    *   A global Alpine.js store (`Alpine.store('modal', ...)`) is used to manage the visibility state of the modal.
    *   Custom HTMX lifecycle events (`htmx:afterOnLoad` on `#modalContent`) are intercepted by Alpine.js's `x-init` or `_=` attributes to automatically open the modal after HTMX has successfully loaded content into it.
    *   Alpine.js's `x-show` directive controls the modal's display. Clicking outside the modal content (`on click if event.target.id == 'modal'`) closes it gracefully.
    *   Custom events, like `bomItemAlert` triggered by `BomItemSelectRedirectView`, are listened to by Alpine.js (`@bomitemalert.window`) to display client-side alerts to the user, mimicking the ASP.NET `ClientScript.RegisterStartupScript` behavior.
*   **DataTables for List Views:**
    *   The `_bom_table.html` partial contains a standard `<table>` with the ID `bomTreeTable`.
    *   The `extra_js` block in `list.html` includes the DataTables CDN JavaScript and CSS.
    *   Crucially, a JavaScript event listener on `document.body` for `htmx:afterSwap` is implemented. This listener checks if the `bomTableContainer` was the target of the swap. If so, it destroys any existing DataTables instance on `bomTreeTable` and then re-initializes it. This ensures that DataTables functions correctly even after its content is dynamically reloaded by HTMX.
    *   DataTables provides out-of-the-box client-side searching, sorting, and pagination, completely replacing the custom pagination and filtering logic of the `RadTreeList` and `GetDataTable` method.

## Final Notes

*   **Session Management:** The original ASP.NET code relies on `Session["compid"]`, `Session["finyear"]`, and `Session["username"]`. In Django, these would typically be handled by Django's session framework, available via `self.request.session` in views, or derived from the authenticated user (`self.request.user`). The provided code uses `DEFAULT_COMPANY_ID` and `DEFAULT_FINANCIAL_YEAR_ID` placeholders; these should be replaced with actual dynamic values.
*   **External Links:** The redirects to other "pages" (e.g., `/bom_wo_items`, `/bom_upload_drw`) are currently absolute paths. In a full Django application, these should be replaced with Django's `reverse_lazy` function and named URL patterns to ensure robustness and easy modification.
*   **File Storage:** While `BinaryField` is used for `FileData` and `AttData` to match the existing `varbinary(max)`, for large-scale production systems, it's generally recommended to store files on the filesystem (local storage, cloud storage like AWS S3) and store only their paths/URLs in the database using Django's `FileField` or `ImageField`.
*   **Tree View Sophistication:** The `RadTreeList` provided rich interactive tree capabilities. While DataTables can be combined with extensions like `rowGroup` or `jsTree` for visual tree structures, the current migration focuses on providing the flat data table with `PId` and `CId` for DataTables. If a full interactive tree is a critical requirement, further frontend development leveraging DataTables tree extensions or a dedicated JavaScript tree library would be necessary.
*   **Security:** Always ensure proper authentication, authorization, and input validation. Django's ORM automatically handles SQL injection prevention.
*   **Styling:** Tailwind CSS classes are applied directly in the templates. Ensure your Django project is configured to process and serve Tailwind CSS.
*   **Conversation AI Integration:** This detailed plan, broken down into distinct files and steps, is designed for ease of use with conversational AI. Each section provides clear objectives, instructions, and business benefits, allowing non-technical stakeholders to understand and oversee the migration process. Automation tools would leverage this structure to generate code and manage deployment.