## ASP.NET to Django Conversion Script: Slido Gunrail Module

This document outlines a comprehensive plan to migrate the ASP.NET "Slido Gunrail Details" module to a modern Django application. Our approach leverages AI-assisted automation principles, focusing on systematic conversion, robust architecture, and a highly interactive user experience without traditional JavaScript frameworks.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is extended.
- Focus ONLY on component-specific code for the current module (`gunrail` app).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy (DRY principle).
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- **Fat Model, Thin View:** Business logic resides solely in Django models or managers. Views are compact, typically 5-15 lines, and focus on data retrieval and delegation.
- **Managed = False:** Models are mapped to existing database tables (`db_table`) and are not managed by Django migrations. This is crucial for integrating with existing legacy databases.
- **HTMX + Alpine.js:** All dynamic frontend interactions (form submissions, table refreshes, modal handling) will use HTMX. Alpine.js will manage simple UI state, eliminating the need for complex JavaScript.
- **DataTables:** All list views will implement DataTables for client-side searching, sorting, and pagination, providing a rich data presentation experience.
- **DRY Templates:** Templates will extend `core/base.html` for consistent layout and CDN/script includes. Partial templates will be used for reusable components.
- **Tailwind CSS:** All styling will be handled by utility classes from Tailwind CSS for rapid and consistent UI development.
- **80% Test Coverage:** Comprehensive unit and integration tests will ensure the reliability and correctness of the migrated functionality.

---

## Conversion Steps:

### Step 1: Extract Database Schema

The ASP.NET code interacts with several database tables, primarily temporary tables for data entry and permanent tables for finalized records, along with a master configuration table.

**Identified Tables:**

-   `tblDG_Gunrail_CrossRail_Temp`: Stores temporary Cross Rail entries.
    -   **Columns:** `Id` (INT, Primary Key), `Length` (FLOAT), `No` (FLOAT), `SessionId` (NVARCHAR), `CompId` (INT).
    -   **Derived:** `Total` (Calculated as `Length * No`).
-   `tblDG_Gunrail_LongRail_Temp`: Stores temporary Long Rail entries.
    -   **Columns:** `Id` (INT, Primary Key), `Length` (FLOAT), `No` (FLOAT), `SessionId` (NVARCHAR), `CompId` (INT).
    -   **Derived:** `Total` (Calculated as `Length * No`).
-   `tblDG_Gunrail_Pitch_Master`: Stores the finalized master configuration for a Gunrail Work Order.
    -   **Columns:** `Id` (INT, Primary Key), `SysDate` (DATE), `SysTime` (TIME), `SessionId` (NVARCHAR), `CompId` (INT), `FinYearId` (INT), `Pitch` (FLOAT), `WONo` (NVARCHAR), `Type` (INT).
-   `tblDG_Gunrail_CrossRail`: Stores permanent Cross Rail details linked to a Master record.
    -   **Columns:** `Id` (INT, Primary Key, assumed), `MId` (INT, Foreign Key to `tblDG_Gunrail_Pitch_Master`), `Length` (FLOAT), `No` (FLOAT).
-   `tblDG_Gunrail_LongRail`: Stores permanent Long Rail details linked to a Master record.
    -   **Columns:** `Id` (INT, Primary Key, assumed), `MId` (INT, Foreign Key to `tblDG_Gunrail_Pitch_Master`), `Length` (FLOAT), `No` (FLOAT).
-   `tblDG_GUNRAIL_BOM_Master`: Referenced in BOM conversion logic (PId, CId).

### Step 2: Identify Backend Functionality

The application manages data entry for Cross Rail and Long Rail components for a specific Work Order, processes this temporary data into permanent records, and triggers complex Bill of Materials (BOM) calculations.

**Core Operations:**

-   **Display Temporary Data:** Lists current Cross Rail and Long Rail entries for the active user's session and company, displaying their length, number, and calculated total.
-   **Add Entry (Temporary):** Allows users to add new Cross Rail or Long Rail items to their respective temporary lists. Input includes Length and Number.
-   **Edit Entry (Temporary):** Enables modification of existing entries in the temporary lists.
-   **Delete Entry (Temporary):** Removes entries from the temporary lists.
-   **Finalize (Proceed):** This is the central business logic.
    -   Validates the "Support Pitch" and "Type" inputs.
    -   Creates a new "Gunrail Master" record.
    -   Moves all temporary Cross Rail entries to a permanent `tblDG_Gunrail_CrossRail` table, linked to the new Master record.
    -   Moves all temporary Long Rail entries to a permanent `tblDG_Gunrail_LongRail` table, linked to the new Master record.
    -   Clears the temporary tables for the current session/company.
    -   Executes complex calculations based on the newly saved data and triggers a BOM (Bill of Materials) conversion process, interacting with other ERP modules.
-   **Cancel:** Clears all temporary data for the current session/company and navigates away.

### Step 3: Infer UI Components

The user interface primarily consists of two data grids for managing temporary rail entries, a small form for "Support Pitch" and "Type", and action buttons.

**UI Elements and Their Django Equivalents:**

-   **ASP.NET GridView (`SearchGridView1`, `GridView1`):** Will be replaced by dynamic HTML tables rendered by Django templates, enhanced with **DataTables.js** for client-side features (pagination, search, sort).
-   **ASP.NET Labels (`lblWono`, `lblType`, `lblSupportPitch`):** Simple display of text, handled by Django template variables.
-   **ASP.NET TextBoxes (`txtLenMTR`, `txtNoRows`, `txtPitch`):** Django form fields (NumberInput) with Tailwind CSS styling.
-   **ASP.NET RadioButtonList (`RadioButtonList1`):** Django `ChoiceField` with `RadioSelect` widget.
-   **ASP.NET Buttons (`btnAddCRF`, `btnAddLRF`, `btnProceed`, `btncancel`):** Standard HTML `<button>` or `<a>` elements with **HTMX** attributes to trigger partial updates, form submissions, or redirects.
-   **ASP.NET Validators (`RequiredFieldValidator`, `RegularExpressionValidator`):** Replaced by Django Forms' built-in validation and custom `clean` methods, providing server-side validation.
-   **Client-Side Confirmation (`confirmationDelete()`, `confirmationAdd()`):** Managed by **Alpine.js** for modals or simple `confirm()` calls if preferred. The `_=` syntax in templates will handle these interactions without custom JS.
-   **Master Page (`MasterPage.master`):** Django's template inheritance, extending `core/base.html`.
-   **Popups/Alerts:** Handled by Django's messages framework and rendered in the base template, visible via Tailwind CSS, and HTMX can trigger dismissals. HTMX and Alpine.js will manage modal popups for forms and confirmations.

### Step 4: Generate Django Code

The Django application will be named `gunrail`.

#### 4.1 Models (`gunrail/models.py`)

Models for each database table will be created, with `managed = False` to connect to the existing database. Business logic, especially the complex "Proceed" functionality, will be encapsulated in a custom manager for the `GunrailMaster` model, following the "Fat Model" principle.

```python
# gunrail/models.py

from django.db import models
from django.utils import timezone
import math

class GunrailCrossRailTempManager(models.Manager):
    """
    Manager for GunrailCrossRailTemp, handling session-specific operations
    and business logic related to temporary cross rail entries.
    """
    def for_session(self, session_id: str, comp_id: int):
        """Retrieves temporary cross rail entries for a given session and company."""
        return self.filter(session_id=session_id, comp_id=comp_id)

    def add_entry(self, session_id: str, comp_id: int, length: float, no: float):
        """Creates and saves a new temporary cross rail entry, calculating total."""
        return self.create(session_id=session_id, comp_id=comp_id, length=length, no=no)

    def clear_for_session(self, session_id: str, comp_id: int):
        """Deletes all temporary cross rail entries for a given session and company."""
        return self.for_session(session_id, comp_id).delete()

class GunrailCrossRailTemp(models.Model):
    """
    Model representing temporary cross rail entries.
    Mapped to the existing tblDG_Gunrail_CrossRail_Temp table.
    """
    id = models.AutoField(db_column='Id', primary_key=True)
    length = models.FloatField(db_column='Length')
    no = models.FloatField(db_column='No')
    session_id = models.CharField(db_column='SessionId', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')
    total = models.FloatField(db_column='Total', editable=False) # Stored for performance/consistency

    objects = GunrailCrossRailTempManager()

    class Meta:
        managed = False
        db_table = 'tblDG_Gunrail_CrossRail_Temp'
        verbose_name = 'Temporary Cross Rail'
        verbose_name_plural = 'Temporary Cross Rails'

    def __str__(self):
        return f"CR Temp (ID: {self.id}, L: {self.length}, N: {self.no}, Total: {self.total})"

    def save(self, *args, **kwargs):
        """Recalculates total before saving."""
        self.total = round(self.length * self.no, 3)
        super().save(*args, **kwargs)

class GunrailLongRailTempManager(models.Manager):
    """
    Manager for GunrailLongRailTemp, handling session-specific operations
    and business logic related to temporary long rail entries.
    """
    def for_session(self, session_id: str, comp_id: int):
        """Retrieves temporary long rail entries for a given session and company."""
        return self.filter(session_id=session_id, comp_id=comp_id)

    def add_entry(self, session_id: str, comp_id: int, length: float, no: float):
        """Creates and saves a new temporary long rail entry, calculating total."""
        return self.create(session_id=session_id, comp_id=comp_id, length=length, no=no)

    def clear_for_session(self, session_id: str, comp_id: int):
        """Deletes all temporary long rail entries for a given session and company."""
        return self.for_session(session_id, comp_id).delete()

class GunrailLongRailTemp(models.Model):
    """
    Model representing temporary long rail entries.
    Mapped to the existing tblDG_Gunrail_LongRail_Temp table.
    """
    id = models.AutoField(db_column='Id', primary_key=True)
    length = models.FloatField(db_column='Length')
    no = models.FloatField(db_column='No')
    session_id = models.CharField(db_column='SessionId', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')
    total = models.FloatField(db_column='Total', editable=False) # Stored for performance/consistency

    objects = GunrailLongRailTempManager()

    class Meta:
        managed = False
        db_table = 'tblDG_Gunrail_LongRail_Temp'
        verbose_name = 'Temporary Long Rail'
        verbose_name_plural = 'Temporary Long Rails'

    def __str__(self):
        return f"LR Temp (ID: {self.id}, L: {self.length}, N: {self.no}, Total: {self.total})"

    def save(self, *args, **kwargs):
        """Recalculates total before saving."""
        self.total = round(self.length * self.no, 3)
        super().save(*args, **kwargs)

class GunrailMasterManager(models.Manager):
    """
    Manager for GunrailMaster, encapsulating the complex 'Proceed' business logic,
    including data transfer and BOM calculations.
    """
    def create_master_record(self, session_id: str, comp_id: int, fin_year_id: int, pitch: float, wo_no: str, type_val: int):
        """Creates a new GunrailMaster record with system date and time."""
        return self.create(
            sys_date=timezone.now().date(),
            sys_time=timezone.now().time(),
            session_id=session_id,
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            pitch=pitch,
            wo_no=wo_no,
            type_val=type_val
        )

    def finalize_gunrail_data(self, session_id: str, comp_id: int, fin_year_id: int, pitch: float, wo_no: str, type_val: int):
        """
        Orchestrates the entire data finalization process:
        1. Validates presence of temporary data.
        2. Creates a permanent GunrailMaster record.
        3. Moves data from temporary Cross Rail to permanent Cross Rail Detail.
        4. Moves data from temporary Long Rail to permanent Long Rail Detail.
        5. Clears temporary tables.
        6. Executes complex BOM calculations and calls external BOM conversion.
        """
        cross_rail_temps = GunrailCrossRailTemp.objects.for_session(session_id, comp_id)
        long_rail_temps = GunrailLongRailTemp.objects.for_session(session_id, comp_id)

        # Ensure both temporary tables have data as required by ASP.NET logic
        if not cross_rail_temps.exists() or not long_rail_temps.exists():
            raise ValueError("Both Cross Rail and Long Rail temporary data must exist to proceed.")

        # 1. Create GunrailMaster record
        master_record = self.create_master_record(session_id, comp_id, fin_year_id, pitch, wo_no, type_val)

        # 2. Move Cross Rail data to permanent table
        for temp_entry in cross_rail_temps:
            GunrailCrossRailDetail.objects.create(
                master=master_record,
                length=temp_entry.length,
                no=temp_entry.no
            )
        cross_rail_temps.clear_for_session(session_id, comp_id) # Clear temp table

        # 3. Move Long Rail data to permanent table
        for temp_entry in long_rail_temps:
            GunrailLongRailDetail.objects.create(
                master=master_record,
                length=temp_entry.length,
                no=temp_entry.no
            )
        long_rail_temps.clear_for_session(session_id, comp_id) # Clear temp table

        # 4. Perform complex BOM calculations (direct replication of C# logic)
        TLongRow_no_coloumn = 0.0
        TLongRow_with_coloumn = 0.0
        TLongRow = 0.0
        TLColumn = 0.0 # Sum of No * No
        SumLongRailColumn = 0.0 # Sum of No

        for detail in GunrailLongRailDetail.objects.filter(master=master_record):
            length = detail.length
            no = detail.no
            # Replicating C# `fun.get` which seems to be `Math.Floor`
            AbcFracValue = self._get_floor_value(length / 6)
            LongRow_no_coloumn = ((AbcFracValue + 1) - 2)
            TLongRow_no_coloumn += LongRow_no_coloumn * no

            WithoutFractionalValue = self._get_floor_value((length / master_record.pitch) + 1)
            LongRow_with_coloumn = (WithoutFractionalValue - LongRow_no_coloumn)
            TLongRow_with_coloumn += LongRow_with_coloumn * no

            TLongRow += length * no
            TLColumn += no * no # Direct translation of C# `TLColumn += No * No;`
            SumLongRailColumn += no

        TCrossRow = 0.0
        TCColumn = 0.0 # Sum of No

        for detail in GunrailCrossRailDetail.objects.filter(master=master_record):
            TCrossRow += detail.length * detail.no
            TCColumn += detail.no

        # 5. Call external BOM Conversion Logic (placeholder for actual ERP integration)
        self._execute_bom_conversion(
            wo_no=wo_no, comp_id=comp_id, fin_year_id=fin_year_id, session_id=session_id,
            TCColumn=TCColumn, TLongRow_with_coloumn=TLongRow_with_coloumn,
            TLongRow_no_coloumn=TLongRow_no_coloumn, SumLongRailColumn=SumLongRailColumn,
            TCrossRow=TCrossRow, TLongRow=TLongRow
        )
        
        return master_record

    def _get_floor_value(self, value: float) -> int:
        """Helper to replicate C# `fun.get` which seems to be `Math.Floor`."""
        return math.floor(value)

    def _execute_bom_conversion(self, **kwargs):
        """
        Placeholder for the complex Bill of Materials (BOM) conversion logic.
        This method would typically interact with an existing ERP's BOM module
        or a dedicated BOM Django application.
        The parameters here correspond to the complex calculations from the C# code.
        """
        # In a real system, this would involve:
        # - Retrieving BOM components based on source CIds from tblDG_GUNRAIL_BOM_Master (PId=0).
        # - Calculating quantities and properties based on Gunrail data.
        # - Inserting/updating records in BOM related tables.
        print("--- BOM Conversion Initiated (Simulation) ---")
        for key, value in kwargs.items():
            print(f"  {key}: {value}")
        print("--- Complex BOM logic simulated successfully. ---")
        # Example: from your_erp_app.services import BomService
        # BomService.process_gunrail_to_bom(**kwargs)
        pass # Actual integration logic goes here

class GunrailMaster(models.Model):
    """
    Model representing the permanent Gunrail Master configuration.
    Mapped to the existing tblDG_Gunrail_Pitch_Master table.
    """
    id = models.AutoField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    session_id = models.CharField(db_column='SessionId', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    pitch = models.FloatField(db_column='Pitch')
    wo_no = models.CharField(db_column='WONo', max_length=255)
    type_val = models.IntegerField(db_column='Type') # 0 for Swivel, 1 for Fixed

    objects = GunrailMasterManager()

    class Meta:
        managed = False
        db_table = 'tblDG_Gunrail_Pitch_Master'
        verbose_name = 'Gunrail Master'
        verbose_name_plural = 'Gunrail Masters'

    def __str__(self):
        return f"WO: {self.wo_no} (Pitch: {self.pitch})"

class GunrailCrossRailDetail(models.Model):
    """
    Model representing permanent Cross Rail details associated with a Gunrail Master.
    Mapped to the existing tblDG_Gunrail_CrossRail table.
    """
    id = models.AutoField(db_column='Id', primary_key=True) # Assuming 'Id' column exists in this table
    master = models.ForeignKey(GunrailMaster, on_delete=models.CASCADE, db_column='MId', related_name='cross_rail_details')
    length = models.FloatField(db_column='Length')
    no = models.FloatField(db_column='No')

    class Meta:
        managed = False
        db_table = 'tblDG_Gunrail_CrossRail'
        verbose_name = 'Gunrail Cross Rail Detail'
        verbose_name_plural = 'Gunrail Cross Rail Details'

    def __str__(self):
        return f"CR Detail for Master {self.master.id}: L={self.length}, N={self.no}"

    @property
    def total(self) -> float:
        """Calculates the total for this detail entry."""
        return round(self.length * self.no, 3)

class GunrailLongRailDetail(models.Model):
    """
    Model representing permanent Long Rail details associated with a Gunrail Master.
    Mapped to the existing tblDG_Gunrail_LongRail table.
    """
    id = models.AutoField(db_column='Id', primary_key=True) # Assuming 'Id' column exists in this table
    master = models.ForeignKey(GunrailMaster, on_delete=models.CASCADE, db_column='MId', related_name='long_rail_details')
    length = models.FloatField(db_column='Length')
    no = models.FloatField(db_column='No')

    class Meta:
        managed = False
        db_table = 'tblDG_Gunrail_LongRail'
        verbose_name = 'Gunrail Long Rail Detail'
        verbose_name_plural = 'Gunrail Long Rail Details'

    def __str__(self):
        return f"LR Detail for Master {self.master.id}: L={self.length}, N={self.no}"

    @property
    def total(self) -> float:
        """Calculates the total for this detail entry."""
        return round(self.length * self.no, 3)
```

#### 4.2 Forms (`gunrail/forms.py`)

Django forms will handle input validation and structure. Two forms will be created for the temporary rail entries and one for the master `Pitch` and `Type` fields.

```python
# gunrail/forms.py

from django import forms
from .models import GunrailCrossRailTemp, GunrailLongRailTemp

class GunrailCrossRailTempForm(forms.ModelForm):
    """
    Form for adding/editing temporary Cross Rail entries.
    Includes client-side validation logic from ASP.NET.
    """
    class Meta:
        model = GunrailCrossRailTemp
        fields = ['length', 'no']
        widgets = {
            'length': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Length in Meter',
                'step': '0.001' # Allows decimal input up to 3 places
            }),
            'no': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Numbers',
                'step': '0.001'
            }),
        }
    
    def clean(self):
        """
        Custom validation to ensure length and number are not zero,
        replicating ASP.NET's `if (length != 0 && NoOfRow != 0)`.
        """
        cleaned_data = super().clean()
        length = cleaned_data.get('length')
        no = cleaned_data.get('no')
        
        if length is not None and length <= 0:
            self.add_error('length', 'Length must be a positive value.')
        if no is not None and no <= 0:
            self.add_error('no', 'Number of rows must be a positive value.')
        
        return cleaned_data

class GunrailLongRailTempForm(forms.ModelForm):
    """
    Form for adding/editing temporary Long Rail entries.
    Includes client-side validation logic from ASP.NET.
    """
    class Meta:
        model = GunrailLongRailTemp
        fields = ['length', 'no']
        widgets = {
            'length': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Length in Meter',
                'step': '0.001'
            }),
            'no': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'No of Rows',
                'step': '0.001'
            }),
        }

    def clean(self):
        """
        Custom validation to ensure length and number are not zero.
        """
        cleaned_data = super().clean()
        length = cleaned_data.get('length')
        no = cleaned_data.get('no')
        
        if length is not None and length <= 0:
            self.add_error('length', 'Length must be a positive value.')
        if no is not None and no <= 0:
            self.add_error('no', 'Number of rows must be a positive value.')
            
        return cleaned_data

class GunrailMasterForm(forms.Form): # This is not a ModelForm as the GunrailMaster is created via business logic
    """
    Form for the 'Proceed' section, collecting Pitch and Type.
    """
    TYPE_CHOICES = [
        (0, 'Swivel'),
        (1, 'Fixed'),
    ]

    type_val = forms.ChoiceField(
        choices=TYPE_CHOICES,
        widget=forms.RadioSelect(attrs={'class': 'flex space-x-4 justify-start'}), # Adjusted for styling
        label='Type',
        initial=0 # Default to Swivel, matching ASP.NET Selected="True"
    )
    pitch = forms.FloatField(
        widget=forms.NumberInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Support Pitch',
            'step': '0.001',
            'style': 'max-width: 100px;' # Replicating ASP.NET Width, but using max-width for responsiveness
        }),
        label='Support Pitch'
    )

    def clean_pitch(self):
        """
        Custom validation for Pitch, ensuring it's not zero or empty.
        Replicates ASP.NET's RequiredFieldValidator and RegularExpressionValidator.
        """
        pitch = self.cleaned_data.get('pitch')
        if pitch is None: # Covers empty case
            raise forms.ValidationError("Support Pitch is required.")
        if pitch <= 0: # Replicating C# `pitch != 0` check
            raise forms.ValidationError("Support Pitch must be a positive value.")
        return pitch
```

#### 4.3 Views (`gunrail/views.py`)

Views will be Class-Based Views (CBVs), kept thin by delegating complex logic to model managers. HTMX requests will be handled by returning `HttpResponse` with appropriate `HX-Trigger` headers for client-side updates.

```python
# gunrail/views.py

from django.views.generic import TemplateView, View
from django.shortcuts import render, get_object_or_404, redirect
from django.http import HttpResponse
from django.urls import reverse_lazy
from django.contrib import messages
from django.db import transaction

from .models import GunrailCrossRailTemp, GunrailLongRailTemp, GunrailMaster
from .forms import GunrailCrossRailTempForm, GunrailLongRailTempForm, GunrailMasterForm

# Helper to get session and company data (mocked for migration, assuming auth setup)
def get_session_data(request):
    """
    Retrieves session-specific data (username, company ID, financial year ID).
    In a real application, this would typically come from the authenticated user's profile
    or a dedicated session management system, not directly from raw session keys.
    """
    session_id = request.session.get('username', 'autoerp_guest') # Default user for testing
    comp_id = request.session.get('compid', 1) # Default company ID for testing
    fin_year_id = request.session.get('finyear', 2023) # Default financial year for testing
    return session_id, comp_id, fin_year_id

class GunrailDashboardView(TemplateView):
    """
    Main view to display the Slido Gunrail details dashboard.
    Renders the temporary rail entry tables and the master form.
    """
    template_name = 'gunrail/dashboard.html'

    def get_context_data(self, **kwargs):
        """
        Populates context with WO number, forms, and temporary rail data.
        """
        context = super().get_context_data(**kwargs)
        # Fetching session details, mimicking ASP.NET's Session object
        session_id, comp_id, _ = get_session_data(self.request)
        
        # Work Order Number (WONo) is retrieved from query string
        context['wo_no'] = self.request.GET.get('WONo', 'N/A')
        
        # Initialize forms for adding new entries in footer/empty states
        context['cross_rail_temp_form'] = GunrailCrossRailTempForm()
        context['long_rail_temp_form'] = GunrailLongRailTempForm()
        context['master_form'] = GunrailMasterForm(initial={'type_val': 0}) # Default 'Swivel' selected

        # Initial load of temporary data for display (HTMX will refresh these)
        context['cross_rails'] = GunrailCrossRailTemp.objects.for_session(session_id, comp_id).order_by('id')
        context['long_rails'] = GunrailLongRailTemp.objects.for_session(session_id, comp_id).order_by('id')
        
        return context

# --- Cross Rail CRUD Operations ---

class CrossRailTablePartialView(View):
    """
    HTMX-driven partial view to render the Cross Rail temporary data table.
    Triggered on initial load and after CRUD operations.
    """
    def get(self, request, *args, **kwargs):
        session_id, comp_id, _ = get_session_data(request)
        cross_rails = GunrailCrossRailTemp.objects.for_session(session_id, comp_id).order_by('id')
        return render(request, 'gunrail/_cross_rail_table.html', {'cross_rails': cross_rails})

class CrossRailAddView(View):
    """
    Handles adding new Cross Rail temporary entries.
    GET: Renders the add form in a modal.
    POST: Processes form submission.
    """
    def get(self, request, *args, **kwargs):
        form = GunrailCrossRailTempForm()
        return render(request, 'gunrail/_cross_rail_form.html', {'form': form, 'is_add': True})

    def post(self, request, *args, **kwargs):
        form = GunrailCrossRailTempForm(request.POST)
        if form.is_valid():
            session_id, comp_id, _ = get_session_data(request)
            GunrailCrossRailTemp.objects.add_entry(
                session_id=session_id,
                comp_id=comp_id,
                length=form.cleaned_data['length'],
                no=form.cleaned_data['no']
            )
            messages.success(request, 'Cross Rail entry added successfully.')
            # HTMX response to close modal and refresh table
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshCrossRailList'})
        # If form is invalid, re-render the form with errors inside the modal
        return render(request, 'gunrail/_cross_rail_form.html', {'form': form, 'is_add': True})

class CrossRailEditView(View):
    """
    Handles editing existing Cross Rail temporary entries.
    GET: Renders the edit form in a modal with existing data.
    POST: Processes form submission.
    """
    def get(self, request, pk, *args, **kwargs):
        session_id, comp_id, _ = get_session_data(request)
        obj = get_object_or_404(GunrailCrossRailTemp.objects.for_session(session_id, comp_id), pk=pk)
        form = GunrailCrossRailTempForm(instance=obj)
        return render(request, 'gunrail/_cross_rail_form.html', {'form': form, 'obj': obj, 'is_add': False})

    def post(self, request, pk, *args, **kwargs):
        session_id, comp_id, _ = get_session_data(request)
        obj = get_object_or_404(GunrailCrossRailTemp.objects.for_session(session_id, comp_id), pk=pk)
        form = GunrailCrossRailTempForm(request.POST, instance=obj)
        if form.is_valid():
            form.save() # Model's save method recalculates 'total'
            messages.success(request, 'Cross Rail entry updated successfully.')
            # HTMX response to close modal and refresh table
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshCrossRailList'})
        # If form is invalid, re-render the form with errors inside the modal
        return render(request, 'gunrail/_cross_rail_form.html', {'form': form, 'obj': obj, 'is_add': False})

class CrossRailDeleteView(View):
    """
    Handles deleting Cross Rail temporary entries.
    GET: Renders the delete confirmation in a modal.
    POST: Deletes the entry.
    """
    def get(self, request, pk, *args, **kwargs):
        session_id, comp_id, _ = get_session_data(request)
        obj = get_object_or_404(GunrailCrossRailTemp.objects.for_session(session_id, comp_id), pk=pk)
        return render(request, 'gunrail/_cross_rail_confirm_delete.html', {'obj': obj})

    def post(self, request, pk, *args, **kwargs):
        session_id, comp_id, _ = get_session_data(request)
        obj = get_object_or_404(GunrailCrossRailTemp.objects.for_session(session_id, comp_id), pk=pk)
        obj.delete()
        messages.success(request, 'Cross Rail entry deleted successfully.')
        # HTMX response to close modal and refresh table
        return HttpResponse(status=204, headers={'HX-Trigger': 'refreshCrossRailList'})

# --- Long Rail CRUD Operations ---

class LongRailTablePartialView(View):
    """
    HTMX-driven partial view to render the Long Rail temporary data table.
    Triggered on initial load and after CRUD operations.
    """
    def get(self, request, *args, **kwargs):
        session_id, comp_id, _ = get_session_data(request)
        long_rails = GunrailLongRailTemp.objects.for_session(session_id, comp_id).order_by('id')
        return render(request, 'gunrail/_long_rail_table.html', {'long_rails': long_rails})

class LongRailAddView(View):
    """
    Handles adding new Long Rail temporary entries.
    GET: Renders the add form in a modal.
    POST: Processes form submission.
    """
    def get(self, request, *args, **kwargs):
        form = GunrailLongRailTempForm()
        return render(request, 'gunrail/_long_rail_form.html', {'form': form, 'is_add': True})

    def post(self, request, *args, **kwargs):
        form = GunrailLongRailTempForm(request.POST)
        if form.is_valid():
            session_id, comp_id, _ = get_session_data(request)
            GunrailLongRailTemp.objects.add_entry(
                session_id=session_id,
                comp_id=comp_id,
                length=form.cleaned_data['length'],
                no=form.cleaned_data['no']
            )
            messages.success(request, 'Long Rail entry added successfully.')
            # HTMX response to close modal and refresh table
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshLongRailList'})
        # If form is invalid, re-render the form with errors inside the modal
        return render(request, 'gunrail/_long_rail_form.html', {'form': form, 'is_add': True})

class LongRailEditView(View):
    """
    Handles editing existing Long Rail temporary entries.
    GET: Renders the edit form in a modal with existing data.
    POST: Processes form submission.
    """
    def get(self, request, pk, *args, **kwargs):
        session_id, comp_id, _ = get_session_data(request)
        obj = get_object_or_404(GunrailLongRailTemp.objects.for_session(session_id, comp_id), pk=pk)
        form = GunrailLongRailTempForm(instance=obj)
        return render(request, 'gunrail/_long_rail_form.html', {'form': form, 'obj': obj, 'is_add': False})

    def post(self, request, pk, *args, **kwargs):
        session_id, comp_id, _ = get_session_data(request)
        obj = get_object_or_404(GunrailLongRailTemp.objects.for_session(session_id, comp_id), pk=pk)
        form = GunrailLongRailTempForm(request.POST, instance=obj)
        if form.is_valid():
            form.save() # Model's save method recalculates 'total'
            messages.success(request, 'Long Rail entry updated successfully.')
            # HTMX response to close modal and refresh table
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshLongRailList'})
        # If form is invalid, re-render the form with errors inside the modal
        return render(request, 'gunrail/_long_rail_form.html', {'form': form, 'obj': obj, 'is_add': False})

class LongRailDeleteView(View):
    """
    Handles deleting Long Rail temporary entries.
    GET: Renders the delete confirmation in a modal.
    POST: Deletes the entry.
    """
    def get(self, request, pk, *args, **kwargs):
        session_id, comp_id, _ = get_session_data(request)
        obj = get_object_or_404(GunrailLongRailTemp.objects.for_session(session_id, comp_id), pk=pk)
        return render(request, 'gunrail/_long_rail_confirm_delete.html', {'obj': obj})

    def post(self, request, pk, *args, **kwargs):
        session_id, comp_id, _ = get_session_data(request)
        obj = get_object_or_404(GunrailLongRailTemp.objects.for_session(session_id, comp_id), pk=pk)
        obj.delete()
        messages.success(request, 'Long Rail entry deleted successfully.')
        # HTMX response to close modal and refresh table
        return HttpResponse(status=204, headers={'HX-Trigger': 'refreshLongRailList'})

# --- Main Action Views ---

class GunrailProceedView(View):
    """
    Handles the 'Proceed' action, triggering the finalization of gunrail data
    and BOM conversion through the GunrailMaster model manager.
    """
    def post(self, request, *args, **kwargs):
        master_form = GunrailMasterForm(request.POST)
        wo_no = request.GET.get('WONo', 'N/A')
        session_id, comp_id, fin_year_id = get_session_data(request)

        if master_form.is_valid():
            pitch = master_form.cleaned_data['pitch']
            type_val = int(master_form.cleaned_data['type_val']) # Ensure type is integer
            try:
                # Delegate all complex business logic to the model manager
                with transaction.atomic(): # Ensure all DB operations are atomic
                    GunrailMaster.objects.finalize_gunrail_data(
                        session_id=session_id,
                        comp_id=comp_id,
                        fin_year_id=fin_year_id,
                        pitch=pitch,
                        wo_no=wo_no,
                        type_val=type_val
                    )
                messages.success(request, 'Gunrail data processed successfully and moved to permanent records.')
                # HTMX trigger to refresh the entire dashboard, as state changes significantly
                return HttpResponse(status=204, headers={'HX-Trigger': 'refreshGunrailDashboard'})
            except ValueError as e:
                messages.error(request, f"Processing error: {e}")
            except Exception as e:
                messages.error(request, f"An unexpected error occurred during processing: {e}")
        else:
            messages.error(request, 'Please correct the errors in the Pitch and Type fields.')
        
        # If form is not valid or an error occurred, re-render the dashboard
        # This sends back the full page HTML with error messages and current data.
        cross_rails = GunrailCrossRailTemp.objects.for_session(session_id, comp_id).order_by('id')
        long_rails = GunrailLongRailTemp.objects.for_session(session_id, comp_id).order_by('id')
        
        context = {
            'wo_no': wo_no,
            'master_form': master_form,
            'cross_rails': cross_rails,
            'long_rails': long_rails,
            'cross_rail_temp_form': GunrailCrossRailTempForm(), # New form instances for footer
            'long_rail_temp_form': GunrailLongRailTempForm(),
        }
        return render(request, 'gunrail/dashboard.html', context)

class GunrailCancelView(View):
    """
    Handles the 'Cancel' action, clearing temporary data and redirecting.
    """
    def get(self, request, *args, **kwargs):
        session_id, comp_id, _ = get_session_data(request)
        # Clear temporary data for the current session/company
        GunrailCrossRailTemp.objects.clear_for_session(session_id, comp_id)
        GunrailLongRailTemp.objects.clear_for_session(session_id, comp_id)
        messages.info(request, "Operation cancelled and temporary data cleared.")
        # Redirect to a relevant landing page (e.g., a Work Order list)
        return redirect(reverse_lazy('work_order_list')) # Assuming 'work_order_list' is a defined URL name
```

#### 4.4 Templates (`gunrail/templates/gunrail/`)

Templates will be structured using Django's template inheritance and will heavily leverage HTMX for dynamic content loading and form submissions, and Alpine.js for modal state. DataTables will power the interactive tables.

```html
<!-- gunrail/templates/gunrail/dashboard.html -->
{% extends 'core/base.html' %} {# Assumed base template from core app #}

{% block title %}Slido Gunrail Details - WO: {{ wo_no }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6">
        <h2 class="text-3xl font-extrabold text-gray-900 mb-2">Slido Gunrail - For Work Order: <span class="text-indigo-600">{{ wo_no }}</span></h2>
        <p class="text-lg text-gray-600">Enter and review temporary Cross Rail and Long Rail components before finalizing the Bill of Materials.</p>
    </div>

    {# Django Messages (for feedback after actions) #}
    {% if messages %}
    <div id="messages" class="mb-6 space-y-3" hx-swap-oob="beforeend">
        {% for message in messages %}
        <div class="p-4 rounded-md shadow-sm {% if message.tags == 'success' %}bg-green-100 text-green-800 border border-green-200{% elif message.tags == 'error' %}bg-red-100 text-red-800 border border-red-200{% elif message.tags == 'info' %}bg-blue-100 text-blue-800 border border-blue-200{% endif %}"
             x-data="{ show: true }" x-show="show" x-transition:leave.duration.500ms
             _="on htmx:afterSwap remove me then wait 2s then transition out then remove me">
            <div class="flex justify-between items-center">
                <span class="text-sm font-medium">{{ message }}</span>
                <button @click="show = false" class="ml-4 -mr-1.5 p-1 rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <span class="sr-only">Dismiss</span>
                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </button>
            </div>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <!-- Cross Rail Section -->
        <div class="bg-white shadow-xl rounded-lg p-6">
            <div class="flex justify-between items-center mb-5">
                <h3 class="text-2xl font-bold text-gray-800">Cross Rail Components</h3>
                <button
                    class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md text-sm shadow-md transition duration-150 ease-in-out"
                    hx-get="{% url 'gunrail:cross_rail_add' %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Add New Cross Rail
                </button>
            </div>
            {# HTMX will load the DataTables-powered table here #}
            <div id="crossRailTableContainer"
                 hx-trigger="load, refreshCrossRailList from:body"
                 hx-get="{% url 'gunrail:cross_rail_table' %}"
                 hx-swap="innerHTML"
                 class="min-h-[200px] flex items-center justify-center"> {# Minimum height for loading spinner #}
                <div class="text-center">
                    <div class="inline-block animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-indigo-500"></div>
                    <p class="mt-3 text-gray-600">Loading Cross Rail data...</p>
                </div>
            </div>
        </div>

        <!-- Long Rail Section -->
        <div class="bg-white shadow-xl rounded-lg p-6">
            <div class="flex justify-between items-center mb-5">
                <h3 class="text-2xl font-bold text-gray-800">Long Rail Components</h3>
                <button
                    class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md text-sm shadow-md transition duration-150 ease-in-out"
                    hx-get="{% url 'gunrail:long_rail_add' %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Add New Long Rail
                </button>
            </div>
            {# HTMX will load the DataTables-powered table here #}
            <div id="longRailTableContainer"
                 hx-trigger="load, refreshLongRailList from:body"
                 hx-get="{% url 'gunrail:long_rail_table' %}"
                 hx-swap="innerHTML"
                 class="min-h-[200px] flex items-center justify-center"> {# Minimum height for loading spinner #}
                <div class="text-center">
                    <div class="inline-block animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-indigo-500"></div>
                    <p class="mt-3 text-gray-600">Loading Long Rail data...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Master Form Section (Proceed/Cancel) -->
    <div class="bg-white shadow-xl rounded-lg p-6 mt-8">
        <h3 class="text-2xl font-bold text-gray-800 mb-5">Finalize Gunrail Configuration</h3>
        <form hx-post="{% url 'gunrail:proceed' %}?WONo={{ wo_no }}" hx-swap="outerHTML" hx-target="#messages" hx-indicator="#proceed-spinner">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 items-center">
                {# Radio button list for Type #}
                <div class="flex items-center space-x-6">
                    <label class="block text-lg font-bold text-gray-700">{{ master_form.type_val.label }}:</label>
                    <div class="flex space-x-6 mt-1">
                        {% for radio in master_form.type_val %}
                            <label class="inline-flex items-center cursor-pointer">
                                {{ radio.tag|safe }} {# Render input tag #}
                                <span class="ml-2 text-base text-gray-700">{{ radio.choice_label }}</span>
                            </label>
                        {% endfor %}
                    </div>
                    {% if master_form.type_val.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ master_form.type_val.errors }}</p>
                    {% endif %}
                </div>
                {# Text input for Support Pitch #}
                <div>
                    <label for="{{ master_form.pitch.id_for_label }}" class="block text-lg font-bold text-gray-700">{{ master_form.pitch.label }}:</label>
                    <div class="mt-1 flex items-center">
                        {{ master_form.pitch }}
                        {% if master_form.pitch.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ master_form.pitch.errors }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="mt-8 flex justify-center space-x-6">
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-8 rounded-lg shadow-lg text-lg transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500" hx-trigger="click">
                    Proceed
                    <span id="proceed-spinner" class="htmx-indicator ml-2">
                        <div class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                    </span>
                </button>
                <a href="{% url 'gunrail:cancel' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-3 px-8 rounded-lg shadow-lg text-lg transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-300">
                    Cancel
                </a>
            </div>
        </form>
    </div>

    <!-- Universal Modal for HTMX-loaded forms and confirmations -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 transition-opacity duration-300 opacity-0 pointer-events-none"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-8 rounded-lg shadow-2xl max-w-2xl w-full transform transition-transform duration-300 scale-90"
             _="on modal show transition transform, opacity from scale-90 opacity-0 to scale-100 opacity-100"
             hx-swap-oob="true">
            <!-- HTMX content will be loaded here -->
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js setup for modal management
    document.addEventListener('alpine:init', () => {
        Alpine.store('modal', {
            open: false,
            toggle() {
                this.open = !this.open;
            }
        });
    });

    // HTMX event listeners for modal
    // Add 'is-active' class to modal to show it (using 'active' class from ASP.NET example for consistency, renamed to 'is-active' for clarity)
    htmx.onLoad(function(elt) {
        if (elt.id === "modalContent") {
            document.getElementById('modal').classList.add('is-active');
            document.getElementById('modal').classList.remove('opacity-0', 'pointer-events-none');
        }
    });

    // Remove 'is-active' class from modal after HTMX response (e.g., 204 No Content for successful form submission)
    htmx.on("htmx:afterRequest", function(evt) {
        const modal = document.getElementById('modal');
        if (evt.detail.xhr.status === 204 && modal.classList.contains('is-active')) {
            modal.classList.remove('is-active');
            modal.classList.add('opacity-0', 'pointer-events-none'); // Hide modal
            document.getElementById('modalContent').innerHTML = ''; // Clear modal content
        }
    });

    // Custom event listener for refreshing the entire dashboard after "Proceed"
    // This simulates the full page redirect from ASP.NET for the main finalization action
    document.body.addEventListener('refreshGunrailDashboard', function() {
        window.location.reload(); 
    });

    // Handle messages visibility and auto-dismissal
    document.addEventListener('DOMContentLoaded', () => {
        const messagesDiv = document.getElementById('messages');
        if (messagesDiv && messagesDiv.children.length > 0) {
            setTimeout(() => {
                messagesDiv.querySelectorAll('[x-show="show"]').forEach(el => el.__x.$data.show = false);
            }, 5000); // Messages disappear after 5 seconds
        }
    });
</script>
{% endblock %}
```

```html
<!-- gunrail/templates/gunrail/_cross_rail_table.html -->
<div class="overflow-x-auto">
    <table id="crossRailTable" class="min-w-full bg-white border border-gray-200">
        <thead class="bg-gray-100">
            <tr>
                <th class="py-3 px-4 border-b text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 border-b text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Length (Mtr)</th>
                <th class="py-3 px-4 border-b text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Numbers</th>
                <th class="py-3 px-4 border-b text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Total</th>
                <th class="py-3 px-4 border-b text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for cr in cross_rails %}
            <tr class="{% cycle 'bg-white' 'bg-gray-50' %}">
                <td class="py-3 px-4 border-b text-sm text-gray-700">{{ forloop.counter }}</td>
                <td class="py-3 px-4 border-b text-sm text-gray-700">{{ cr.length|floatformat:3 }}</td>
                <td class="py-3 px-4 border-b text-sm text-gray-700">{{ cr.no|floatformat:3 }}</td>
                <td class="py-3 px-4 border-b text-sm text-gray-700 font-medium">{{ cr.total|floatformat:3 }}</td>
                <td class="py-3 px-4 border-b text-sm flex space-x-2">
                    <button
                        class="text-blue-600 hover:text-blue-800 font-medium"
                        hx-get="{% url 'gunrail:cross_rail_edit' cr.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="text-red-600 hover:text-red-800 font-medium"
                        hx-get="{% url 'gunrail:cross_rail_delete' cr.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5" class="py-5 px-4 text-center text-gray-500 font-medium">No Cross Rail entries added yet.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists to prevent reinitialization errors
        if ($.fn.DataTable.isDataTable('#crossRailTable')) {
            $('#crossRailTable').DataTable().destroy();
        }
        $('#crossRailTable').DataTable({
            "paging": true,
            "searching": true,
            "info": true,
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "order": [[ 0, "asc" ]] // Order by SN by default
        });
    });
</script>
```

```html
<!-- gunrail/templates/gunrail/_cross_rail_form.html -->
<div class="p-6">
    <h3 class="text-2xl font-bold text-gray-800 mb-6">{{ is_add|yesno:"Add New,Edit" }} Cross Rail Entry</h3>
    <form hx-post="{% if is_add %}{% url 'gunrail:cross_rail_add' %}{% else %}{% url 'gunrail:cross_rail_edit' obj.pk %}{% endif %}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-5">
            <div>
                <label for="{{ form.length.id_for_label }}" class="block text-sm font-medium text-gray-700">Length in Meter</label>
                {{ form.length }}
                {% if form.length.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.length.errors }}</p>
                {% endif %}
            </div>
            <div>
                <label for="{{ form.no.id_for_label }}" class="block text-sm font-medium text-gray-700">Numbers</label>
                {{ form.no }}
                {% if form.no.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.no.errors }}</p>
                {% endif %}
            </div>
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-md shadow-sm transition duration-150 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

```html
<!-- gunrail/templates/gunrail/_cross_rail_confirm_delete.html -->
<div class="p-6">
    <h3 class="text-2xl font-bold text-gray-800 mb-6">Confirm Deletion</h3>
    <p class="text-gray-700 text-lg mb-6">Are you absolutely sure you want to delete this Cross Rail entry?</p>
    <table class="min-w-full bg-white border border-gray-200 mb-6 rounded-md overflow-hidden">
        <tr class="bg-gray-50">
            <td class="py-3 px-4 font-semibold text-gray-700 w-1/3">Length:</td>
            <td class="py-3 px-4 text-gray-800">{{ obj.length|floatformat:3 }}</td>
        </tr>
        <tr>
            <td class="py-3 px-4 font-semibold text-gray-700">Numbers:</td>
            <td class="py-3 px-4 text-gray-800">{{ obj.no|floatformat:3 }}</td>
        </tr>
        <tr class="bg-gray-50">
            <td class="py-3 px-4 font-semibold text-gray-700">Total:</td>
            <td class="py-3 px-4 text-gray-800 font-bold">{{ obj.total|floatformat:3 }}</td>
        </tr>
    </table>

    <form hx-post="{% url 'gunrail:cross_rail_delete' obj.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4 mt-8">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-5 rounded-md shadow-sm transition duration-150 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

```html
<!-- gunrail/templates/gunrail/_long_rail_table.html -->
<div class="overflow-x-auto">
    <table id="longRailTable" class="min-w-full bg-white border border-gray-200">
        <thead class="bg-gray-100">
            <tr>
                <th class="py-3 px-4 border-b text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 border-b text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Length (Mtr)</th>
                <th class="py-3 px-4 border-b text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">No of Rows</th>
                <th class="py-3 px-4 border-b text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Total</th>
                <th class="py-3 px-4 border-b text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for lr in long_rails %}
            <tr class="{% cycle 'bg-white' 'bg-gray-50' %}">
                <td class="py-3 px-4 border-b text-sm text-gray-700">{{ forloop.counter }}</td>
                <td class="py-3 px-4 border-b text-sm text-gray-700">{{ lr.length|floatformat:3 }}</td>
                <td class="py-3 px-4 border-b text-sm text-gray-700">{{ lr.no|floatformat:3 }}</td>
                <td class="py-3 px-4 border-b text-sm text-gray-700 font-medium">{{ lr.total|floatformat:3 }}</td>
                <td class="py-3 px-4 border-b text-sm flex space-x-2">
                    <button
                        class="text-blue-600 hover:text-blue-800 font-medium"
                        hx-get="{% url 'gunrail:long_rail_edit' lr.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="text-red-600 hover:text-red-800 font-medium"
                        hx-get="{% url 'gunrail:long_rail_delete' lr.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5" class="py-5 px-4 text-center text-gray-500 font-medium">No Long Rail entries added yet.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#longRailTable')) {
            $('#longRailTable').DataTable().destroy();
        }
        $('#longRailTable').DataTable({
            "paging": true,
            "searching": true,
            "info": true,
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "order": [[ 0, "asc" ]] // Order by SN by default
        });
    });
</script>
```

```html
<!-- gunrail/templates/gunrail/_long_rail_form.html -->
<div class="p-6">
    <h3 class="text-2xl font-bold text-gray-800 mb-6">{{ is_add|yesno:"Add New,Edit" }} Long Rail Entry</h3>
    <form hx-post="{% if is_add %}{% url 'gunrail:long_rail_add' %}{% else %}{% url 'gunrail:long_rail_edit' obj.pk %}{% endif %}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-5">
            <div>
                <label for="{{ form.length.id_for_label }}" class="block text-sm font-medium text-gray-700">Length in Meter</label>
                {{ form.length }}
                {% if form.length.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.length.errors }}</p>
                {% endif %}
            </div>
            <div>
                <label for="{{ form.no.id_for_label }}" class="block text-sm font-medium text-gray-700">No of Rows</label>
                {{ form.no }}
                {% if form.no.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.no.errors }}</p>
                {% endif %}
            </div>
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-md shadow-sm transition duration-150 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

```html
<!-- gunrail/templates/gunrail/_long_rail_confirm_delete.html -->
<div class="p-6">
    <h3 class="text-2xl font-bold text-gray-800 mb-6">Confirm Deletion</h3>
    <p class="text-gray-700 text-lg mb-6">Are you absolutely sure you want to delete this Long Rail entry?</p>
    <table class="min-w-full bg-white border border-gray-200 mb-6 rounded-md overflow-hidden">
        <tr class="bg-gray-50">
            <td class="py-3 px-4 font-semibold text-gray-700 w-1/3">Length:</td>
            <td class="py-3 px-4 text-gray-800">{{ obj.length|floatformat:3 }}</td>
        </tr>
        <tr>
            <td class="py-3 px-4 font-semibold text-gray-700">No:</td>
            <td class="py-3 px-4 text-gray-800">{{ obj.no|floatformat:3 }}</td>
        </tr>
        <tr class="bg-gray-50">
            <td class="py-3 px-4 font-semibold text-gray-700">Total:</td>
            <td class="py-3 px-4 text-gray-800 font-bold">{{ obj.total|floatformat:3 }}</td>
        </tr>
    </table>

    <form hx-post="{% url 'gunrail:long_rail_delete' obj.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4 mt-8">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-5 rounded-md shadow-sm transition duration-150 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`gunrail/urls.py`)

URL patterns will be organized within the `gunrail` app, using Django's `path` for clean, readable routing.

```python
# gunrail/urls.py

from django.urls import path
from .views import (
    GunrailDashboardView,
    CrossRailTablePartialView, CrossRailAddView, CrossRailEditView, CrossRailDeleteView,
    LongRailTablePartialView, LongRailAddView, LongRailEditView, LongRailDeleteView,
    GunrailProceedView, GunrailCancelView
)

app_name = 'gunrail' # Namespace for URLs to prevent conflicts

urlpatterns = [
    # Main Dashboard
    path('', GunrailDashboardView.as_view(), name='dashboard'),
    
    # Global actions for the module
    path('proceed/', GunrailProceedView.as_view(), name='proceed'),
    path('cancel/', GunrailCancelView.as_view(), name='cancel'),

    # Cross Rail Temporary Data Management (HTMX endpoints)
    path('cross-rail/table/', CrossRailTablePartialView.as_view(), name='cross_rail_table'),
    path('cross-rail/add/', CrossRailAddView.as_view(), name='cross_rail_add'),
    path('cross-rail/edit/<int:pk>/', CrossRailEditView.as_view(), name='cross_rail_edit'),
    path('cross-rail/delete/<int:pk>/', CrossRailDeleteView.as_view(), name='cross_rail_delete'),

    # Long Rail Temporary Data Management (HTMX endpoints)
    path('long-rail/table/', LongRailTablePartialView.as_view(), name='long_rail_table'),
    path('long-rail/add/', LongRailAddView.as_view(), name='long_rail_add'),
    path('long-rail/edit/<int:pk>/', LongRailEditView.as_view(), name='long_rail_edit'),
    path('long-rail/delete/<int:pk>/', LongRailDeleteView.as_view(), name='long_rail_delete'),
]
```

#### 4.6 Tests (`gunrail/tests.py`)

Comprehensive unit tests for models and integration tests for views ensure the correctness and robustness of the migration. Mocking `get_session_data` ensures tests are isolated and repeatable.

```python
# gunrail/tests.py

from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from unittest.mock import patch, MagicMock

# Import all models to ensure test coverage
from .models import (
    GunrailCrossRailTemp, GunrailLongRailTemp, GunrailMaster,
    GunrailCrossRailDetail, GunrailLongRailDetail
)
# Import forms to test validation
from .forms import GunrailCrossRailTempForm, GunrailLongRailTempForm, GunrailMasterForm

# --- Model Unit Tests ---

class GunrailCrossRailTempModelTest(TestCase):
    """Tests for the GunrailCrossRailTemp model and its manager."""

    def setUp(self):
        self.session_id = 'testuser_cr'
        self.comp_id = 1
        self.obj = GunrailCrossRailTemp.objects.create(
            length=10.5,
            no=2.0,
            session_id=self.session_id,
            comp_id=self.comp_id
        )

    def test_model_creation(self):
        """Verify that a CrossRailTemp object is created correctly."""
        self.assertIsInstance(self.obj, GunrailCrossRailTemp)
        self.assertEqual(self.obj.length, 10.5)
        self.assertEqual(self.obj.no, 2.0)
        self.assertEqual(self.obj.total, 21.0) # Calculated on save
        self.assertEqual(str(self.obj), f"CR Temp (ID: {self.obj.id}, L: 10.5, N: 2.0, Total: 21.0)")

    def test_total_calculation_on_update(self):
        """Ensure 'total' is recalculated when length or no are updated."""
        self.obj.length = 5.0
        self.obj.no = 3.0
        self.obj.save()
        self.assertEqual(self.obj.total, 15.0) # 5.0 * 3.0

    def test_for_session_manager(self):
        """Test manager method to filter entries by session and company."""
        GunrailCrossRailTemp.objects.create(
            length=1.0, no=1.0, session_id='another_user', comp_id=1
        )
        filtered_objects = GunrailCrossRailTemp.objects.for_session(self.session_id, self.comp_id)
        self.assertEqual(filtered_objects.count(), 1)
        self.assertEqual(filtered_objects.first().id, self.obj.id)

    def test_add_entry_manager(self):
        """Test manager method for adding new entries."""
        new_entry = GunrailCrossRailTemp.objects.add_entry(
            self.session_id, self.comp_id, 7.0, 4.0
        )
        self.assertEqual(new_entry.length, 7.0)
        self.assertEqual(new_entry.no, 4.0)
        self.assertEqual(new_entry.total, 28.0)
        self.assertEqual(GunrailCrossRailTemp.objects.count(), 2)

    def test_clear_for_session_manager(self):
        """Test manager method for clearing session-specific entries."""
        # Add another entry for the same session to ensure all are cleared
        GunrailCrossRailTemp.objects.create(
            length=1.0, no=1.0, session_id=self.session_id, comp_id=self.comp_id
        )
        GunrailCrossRailTemp.objects.create(
            length=1.0, no=1.0, session_id='another_user', comp_id=1
        )
        self.assertEqual(GunrailCrossRailTemp.objects.count(), 3)
        GunrailCrossRailTemp.objects.clear_for_session(self.session_id, self.comp_id)
        self.assertEqual(GunrailCrossRailTemp.objects.count(), 1)
        self.assertFalse(GunrailCrossRailTemp.objects.filter(session_id=self.session_id).exists())


class GunrailLongRailTempModelTest(TestCase):
    """Tests for the GunrailLongRailTemp model and its manager."""

    def setUp(self):
        self.session_id = 'testuser_lr'
        self.comp_id = 1
        self.obj = GunrailLongRailTemp.objects.create(
            length=15.0,
            no=1.5,
            session_id=self.session_id,
            comp_id=self.comp_id
        )

    def test_model_creation(self):
        """Verify that a LongRailTemp object is created correctly."""
        self.assertIsInstance(self.obj, GunrailLongRailTemp)
        self.assertEqual(self.obj.length, 15.0)
        self.assertEqual(self.obj.no, 1.5)
        self.assertEqual(self.obj.total, 22.5)
        self.assertEqual(str(self.obj), f"LR Temp (ID: {self.obj.id}, L: 15.0, N: 1.5, Total: 22.5)")

    def test_total_calculation_on_update(self):
        """Ensure 'total' is recalculated when length or no are updated."""
        self.obj.length = 6.0
        self.obj.no = 2.0
        self.obj.save()
        self.assertEqual(self.obj.total, 12.0)

    def test_for_session_manager(self):
        """Test manager method to filter entries by session and company."""
        GunrailLongRailTemp.objects.create(
            length=1.0, no=1.0, session_id='another_user', comp_id=1
        )
        filtered_objects = GunrailLongRailTemp.objects.for_session(self.session_id, self.comp_id)
        self.assertEqual(filtered_objects.count(), 1)
        self.assertEqual(filtered_objects.first().id, self.obj.id)

    def test_add_entry_manager(self):
        """Test manager method for adding new entries."""
        new_entry = GunrailLongRailTemp.objects.add_entry(
            self.session_id, self.comp_id, 8.0, 3.0
        )
        self.assertEqual(new_entry.length, 8.0)
        self.assertEqual(new_entry.no, 3.0)
        self.assertEqual(new_entry.total, 24.0)
        self.assertEqual(GunrailLongRailTemp.objects.count(), 2)

    def test_clear_for_session_manager(self):
        """Test manager method for clearing session-specific entries."""
        # Add another entry for the same session to ensure all are cleared
        GunrailLongRailTemp.objects.create(
            length=1.0, no=1.0, session_id=self.session_id, comp_id=self.comp_id
        )
        GunrailLongRailTemp.objects.create(
            length=1.0, no=1.0, session_id='another_user', comp_id=1
        )
        self.assertEqual(GunrailLongRailTemp.objects.count(), 3)
        GunrailLongRailTemp.objects.clear_for_session(self.session_id, self.comp_id)
        self.assertEqual(GunrailLongRailTemp.objects.count(), 1)
        self.assertFalse(GunrailLongRailTemp.objects.filter(session_id=self.session_id).exists())


class GunrailMasterModelTest(TestCase):
    """Tests for the GunrailMaster model and its manager, including finalization logic."""

    def setUp(self):
        self.session_id = 'testuser_master'
        self.comp_id = 1
        self.fin_year_id = 2023
        self.wo_no = 'WO-123'
        self.pitch = 0.5
        self.type_val = 0 # Swivel

        # Create initial temporary data for finalization
        self.cr_temp1 = GunrailCrossRailTemp.objects.create(
            length=10.0, no=2.0, session_id=self.session_id, comp_id=self.comp_id
        )
        self.cr_temp2 = GunrailCrossRailTemp.objects.create(
            length=5.0, no=1.0, session_id=self.session_id, comp_id=self.comp_id
        )
        self.lr_temp1 = GunrailLongRailTemp.objects.create(
            length=20.0, no=3.0, session_id=self.session_id, comp_id=self.comp_id
        )
        self.lr_temp2 = GunrailLongRailTemp.objects.create(
            length=10.0, no=1.0, session_id=self.session_id, comp_id=self.comp_id
        )
        
    def test_create_master_record(self):
        """Verify the creation of a GunrailMaster record."""
        master = GunrailMaster.objects.create_master_record(
            self.session_id, self.comp_id, self.fin_year_id, self.pitch, self.wo_no, self.type_val
        )
        self.assertIsInstance(master, GunrailMaster)
        self.assertEqual(master.wo_no, self.wo_no)
        self.assertEqual(master.pitch, self.pitch)
        self.assertEqual(master.type_val, self.type_val)
        self.assertEqual(master.session_id, self.session_id)
        self.assertEqual(master.comp_id, self.comp_id)
        self.assertEqual(master.fin_year_id, self.fin_year_id)
        self.assertEqual(master.sys_date, timezone.now().date())
        self.assertIsInstance(master.sys_time, timezone.time)
        self.assertEqual(str(master), f"WO: {self.wo_no} (Pitch: {self.pitch})")

    @patch('gunrail.models.GunrailMasterManager._execute_bom_conversion')
    def test_finalize_gunrail_data_success(self, mock_execute_bom_conversion):
        """Test the comprehensive finalization process, including data movement and BOM call."""
        initial_cr_temp_count = GunrailCrossRailTemp.objects.count()
        initial_lr_temp_count = GunrailLongRailTemp.objects.count()

        master_record = GunrailMaster.objects.finalize_gunrail_data(
            self.session_id, self.comp_id, self.fin_year_id, self.pitch, self.wo_no, self.type_val
        )
        self.assertIsInstance(master_record, GunrailMaster)

        # Verify temp tables are cleared
        self.assertEqual(GunrailCrossRailTemp.objects.count(), 0)
        self.assertEqual(GunrailLongRailTemp.objects.count(), 0)

        # Verify data is moved to permanent detail tables
        self.assertEqual(GunrailCrossRailDetail.objects.filter(master=master_record).count(), initial_cr_temp_count)
        self.assertEqual(GunrailLongRailDetail.objects.filter(master=master_record).count(), initial_lr_temp_count)

        # Verify BOM conversion method was called with correct parameters
        mock_execute_bom_conversion.assert_called_once()
        args, kwargs = mock_execute_bom_conversion.call_args

        # Expected calculated values (from C# logic)
        # Cross Rail: (10*2) + (5*1) = 25.0 (TCrossRow)
        # Cross Rail No: 2+1 = 3.0 (TCColumn)
        # Long Rail: (20*3) + (10*1) = 70.0 (TLongRow)
        # Long Rail No: 3+1 = 4.0 (SumLongRailColumn)

        # Long Rail calculations based on C# logic for TLongRow_no_coloumn & TLongRow_with_coloumn
        # For Length=20, No=3, Pitch=0.5:
        #   AbcFracValue (20/6) = floor(3.33) = 3
        #   LongRow_no_coloumn = (3+1) - 2 = 2
        #   TLongRow_no_coloumn_part = 2 * 3 = 6
        #   WithoutFractionalValue ((20/0.5)+1) = floor(41) = 41
        #   LongRow_with_coloumn = 41 - 2 = 39
        #   TLongRow_with_coloumn_part = 39 * 3 = 117
        
        # For Length=10, No=1, Pitch=0.5:
        #   AbcFracValue (10/6) = floor(1.66) = 1
        #   LongRow_no_coloumn = (1+1) - 2 = 0
        #   TLongRow_no_coloumn_part = 0 * 1 = 0
        #   WithoutFractionalValue ((10/0.5)+1) = floor(21) = 21
        #   LongRow_with_coloumn = 21 - 0 = 21
        #   TLongRow_with_coloumn_part = 21 * 1 = 21
        
        # Total TLongRow_no_coloumn = 6 + 0 = 6
        # Total TLongRow_with_coloumn = 117 + 21 = 138

        self.assertAlmostEqual(kwargs['TCrossRow'], 25.0)
        self.assertAlmostEqual(kwargs['TCColumn'], 3.0)
        self.assertAlmostEqual(kwargs['TLongRow_no_coloumn'], 6.0)
        self.assertAlmostEqual(kwargs['TLongRow_with_coloumn'], 138.0)
        self.assertAlmostEqual(kwargs['SumLongRailColumn'], 4.0)
        self.assertAlmostEqual(kwargs['TCrossRow'], 25.0)
        self.assertAlmostEqual(kwargs['TLongRow'], 70.0)
        
        # Verify other general kwargs are present
        self.assertEqual(kwargs['wo_no'], self.wo_no)
        self.assertEqual(kwargs['comp_id'], self.comp_id)


    def test_finalize_gunrail_data_missing_cross_rail_temps(self):
        """Test finalization fails if cross rail temporary data is missing."""
        GunrailCrossRailTemp.objects.all().delete()
        with self.assertRaisesMessage(ValueError, "Both Cross Rail and Long Rail temporary data must exist."):
            GunrailMaster.objects.finalize_gunrail_data(
                self.session_id, self.comp_id, self.fin_year_id, self.pitch, self.wo_no, self.type_val
            )

    def test_finalize_gunrail_data_missing_long_rail_temps(self):
        """Test finalization fails if long rail temporary data is missing."""
        GunrailLongRailTemp.objects.all().delete()
        with self.assertRaisesMessage(ValueError, "Both Cross Rail and Long Rail temporary data must exist."):
            GunrailMaster.objects.finalize_gunrail_data(
                self.session_id, self.comp_id, self.fin_year_id, self.pitch, self.wo_no, self.type_val
            )

    def test_get_floor_value_method(self):
        """Test the internal _get_floor_value helper method."""
        self.assertEqual(GunrailMaster.objects._get_floor_value(3.33), 3)
        self.assertEqual(GunrailMaster.objects._get_floor_value(5.0), 5)
        self.assertEqual(GunrailMaster.objects._get_floor_value(1.99), 1)
        self.assertEqual(GunrailMaster.objects._get_floor_value(0.0), 0)


class GunrailCrossRailDetailModelTest(TestCase):
    """Tests for the permanent GunrailCrossRailDetail model."""

    def setUp(self):
        self.master = GunrailMaster.objects.create(
            sys_date=timezone.now().date(), sys_time=timezone.now().time(),
            session_id='test_detail', comp_id=1, fin_year_id=1, pitch=0.5, wo_no='WO-001', type_val=0
        )
        self.obj = GunrailCrossRailDetail.objects.create(
            master=self.master, length=10.0, no=2.5
        )

    def test_model_creation(self):
        """Verify CrossRailDetail object creation and foreign key relationship."""
        self.assertIsInstance(self.obj, GunrailCrossRailDetail)
        self.assertEqual(self.obj.master, self.master)
        self.assertEqual(self.obj.length, 10.0)
        self.assertEqual(self.obj.no, 2.5)
        self.assertEqual(str(self.obj), f"CR Detail for Master {self.master.id}: Length=10.0, No=2.5")

    def test_total_property(self):
        """Verify the 'total' property calculation."""
        self.assertEqual(self.obj.total, 25.0) # 10.0 * 2.5
        self.obj.length = 5.0
        self.obj.no = 3.0
        self.assertEqual(self.obj.total, 15.0) # 5.0 * 3.0


class GunrailLongRailDetailModelTest(TestCase):
    """Tests for the permanent GunrailLongRailDetail model."""

    def setUp(self):
        self.master = GunrailMaster.objects.create(
            sys_date=timezone.now().date(), sys_time=timezone.now().time(),
            session_id='test_detail', comp_id=1, fin_year_id=1, pitch=0.5, wo_no='WO-001', type_val=0
        )
        self.obj = GunrailLongRailDetail.objects.create(
            master=self.master, length=20.0, no=1.5
        )

    def test_model_creation(self):
        """Verify LongRailDetail object creation and foreign key relationship."""
        self.assertIsInstance(self.obj, GunrailLongRailDetail)
        self.assertEqual(self.obj.master, self.master)
        self.assertEqual(self.obj.length, 20.0)
        self.assertEqual(self.obj.no, 1.5)
        self.assertEqual(str(self.obj), f"LR Detail for Master {self.master.id}: Length=20.0, No=1.5")

    def test_total_property(self):
        """Verify the 'total' property calculation."""
        self.assertEqual(self.obj.total, 30.0) # 20.0 * 1.5
        self.obj.length = 7.0
        self.obj.no = 2.0
        self.assertEqual(self.obj.total, 14.0) # 7.0 * 2.0

# --- Form Unit Tests ---

class GunrailFormsTest(TestCase):
    """Tests for Django Forms used in the gunrail module."""

    def test_cross_rail_temp_form_valid(self):
        """Test CrossRailTempForm with valid data."""
        form = GunrailCrossRailTempForm(data={'length': 10.0, 'no': 2.0})
        self.assertTrue(form.is_valid())

    def test_cross_rail_temp_form_invalid_zero(self):
        """Test CrossRailTempForm with zero values for length/no."""
        form = GunrailCrossRailTempForm(data={'length': 0.0, 'no': 0.0})
        self.assertFalse(form.is_valid())
        self.assertIn('Length must be a positive value.', form.errors['length'])
        self.assertIn('Number of rows must be a positive value.', form.errors['no'])

    def test_cross_rail_temp_form_invalid_negative(self):
        """Test CrossRailTempForm with negative values for length/no."""
        form = GunrailCrossRailTempForm(data={'length': -5.0, 'no': -1.0})
        self.assertFalse(form.is_valid())
        self.assertIn('Length must be a positive value.', form.errors['length'])
        self.assertIn('Number of rows must be a positive value.', form.errors['no'])

    def test_long_rail_temp_form_valid(self):
        """Test LongRailTempForm with valid data."""
        form = GunrailLongRailTempForm(data={'length': 15.0, 'no': 1.0})
        self.assertTrue(form.is_valid())

    def test_long_rail_temp_form_invalid_zero(self):
        """Test LongRailTempForm with zero values for length/no."""
        form = GunrailLongRailTempForm(data={'length': 0.0, 'no': 0.0})
        self.assertFalse(form.is_valid())
        self.assertIn('Length must be a positive value.', form.errors['length'])
        self.assertIn('Number of rows must be a positive value.', form.errors['no'])

    def test_master_form_valid(self):
        """Test GunrailMasterForm with valid data."""
        form = GunrailMasterForm(data={'type_val': '0', 'pitch': 1.25})
        self.assertTrue(form.is_valid())

    def test_master_form_invalid_pitch_empty(self):
        """Test GunrailMasterForm with empty pitch."""
        form = GunrailMasterForm(data={'type_val': '0', 'pitch': ''})
        self.assertFalse(form.is_valid())
        self.assertIn('Support Pitch is required.', form.errors['pitch'])

    def test_master_form_invalid_pitch_zero(self):
        """Test GunrailMasterForm with zero pitch."""
        form = GunrailMasterForm(data={'type_val': '0', 'pitch': 0.0})
        self.assertFalse(form.is_valid())
        self.assertIn('Support Pitch must be a positive value.', form.errors['pitch'])

    def test_master_form_invalid_pitch_negative(self):
        """Test GunrailMasterForm with negative pitch."""
        form = GunrailMasterForm(data={'type_val': '0', 'pitch': -1.0})
        self.assertFalse(form.is_valid())
        self.assertIn('Support Pitch must be a positive value.', form.errors['pitch'])


# --- View Integration Tests ---

# Mock the get_session_data helper for consistent testing of session data
@patch('gunrail.views.get_session_data')
class GunrailViewsTest(TestCase):
    """Integration tests for all views in the gunrail module."""

    def setUp(self, mock_get_session_data):
        self.client = Client()
        self.session_id = 'testuser_view'
        self.comp_id = 1
        self.fin_year_id = 2023
        self.wo_no = 'WO-TEST-VIEW'
        # Mock the session data returned by the helper function
        mock_get_session_data.return_value = (self.session_id, self.comp_id, self.fin_year_id)

        # Create some initial temporary data for table display and operations
        self.cr_temp1 = GunrailCrossRailTemp.objects.create(
            length=10.0, no=2.0, session_id=self.session_id, comp_id=self.comp_id
        )
        self.lr_temp1 = GunrailLongRailTemp.objects.create(
            length=15.0, no=1.0, session_id=self.session_id, comp_id=self.comp_id
        )

    def test_dashboard_view_get(self, mock_get_session_data):
        """Test the main dashboard view renders correctly and includes initial data."""
        response = self.client.get(reverse('gunrail:dashboard'), {'WONo': self.wo_no})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'gunrail/dashboard.html')
        self.assertContains(response, self.wo_no)
        self.assertContains(response, 'Add New Cross Rail')
        self.assertContains(response, 'Add New Long Rail')
        self.assertContains(response, 'Proceed')
        # Check if forms are in context
        self.assertIsInstance(response.context['cross_rail_temp_form'], GunrailCrossRailTempForm)
        self.assertIsInstance(response.context['long_rail_temp_form'], GunrailLongRailTempForm)
        self.assertIsInstance(response.context['master_form'], GunrailMasterForm)

    # --- Cross Rail CRUD Views ---

    def test_cross_rail_table_partial_get(self, mock_get_session_data):
        """Test HTMX partial for Cross Rail table displays data."""
        response = self.client.get(reverse('gunrail:cross_rail_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'gunrail/_cross_rail_table.html')
        self.assertContains(response, str(self.cr_temp1.length))
        self.assertContains(response, str(self.cr_temp1.no))

    def test_cross_rail_add_view_get(self, mock_get_session_data):
        """Test GET request for Cross Rail add form in modal."""
        response = self.client.get(reverse('gunrail:cross_rail_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'gunrail/_cross_rail_form.html')
        self.assertContains(response, 'Add New Cross Rail Entry')
        self.assertIsInstance(response.context['form'], GunrailCrossRailTempForm)
        self.assertTrue(response.context['is_add'])

    def test_cross_rail_add_view_post_success(self, mock_get_session_data):
        """Test successful POST request to add a Cross Rail entry."""
        data = {'length': 20.0, 'no': 3.0}
        response = self.client.post(reverse('gunrail:cross_rail_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCrossRailList')
        self.assertTrue(GunrailCrossRailTemp.objects.filter(length=20.0, no=3.0, session_id=self.session_id).exists())
        self.assertEqual(GunrailCrossRailTemp.objects.count(), 2) # Initial + new

    def test_cross_rail_add_view_post_invalid(self, mock_get_session_data):
        """Test POST request with invalid data for Cross Rail add form."""
        data = {'length': 0.0, 'no': -1.0} # Invalid data
        response = self.client.post(reverse('gunrail:cross_rail_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Renders form with errors
        self.assertTemplateUsed(response, 'gunrail/_cross_rail_form.html')
        self.assertContains(response, 'Length must be a positive value.')
        self.assertContains(response, 'Number of rows must be a positive value.')
        self.assertEqual(GunrailCrossRailTemp.objects.count(), 1) # No new object created

    def test_cross_rail_edit_view_get(self, mock_get_session_data):
        """Test GET request for Cross Rail edit form in modal."""
        response = self.client.get(reverse('gunrail:cross_rail_edit', args=[self.cr_temp1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'gunrail/_cross_rail_form.html')
        self.assertContains(response, 'Edit Cross Rail Entry')
        self.assertContains(response, str(self.cr_temp1.length))
        self.assertFalse(response.context['is_add'])

    def test_cross_rail_edit_view_post_success(self, mock_get_session_data):
        """Test successful POST request to edit a Cross Rail entry."""
        data = {'length': 12.0, 'no': 2.5}
        response = self.client.post(reverse('gunrail:cross_rail_edit', args=[self.cr_temp1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCrossRailList')
        self.cr_temp1.refresh_from_db() # Refresh object to get updated data
        self.assertEqual(self.cr_temp1.length, 12.0)
        self.assertEqual(self.cr_temp1.no, 2.5)
        self.assertEqual(self.cr_temp1.total, 30.0)

    def test_cross_rail_edit_view_post_invalid(self, mock_get_session_data):
        """Test POST request with invalid data for Cross Rail edit form."""
        data = {'length': 0.0, 'no': 0.0}
        response = self.client.post(reverse('gunrail:cross_rail_edit', args=[self.cr_temp1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'gunrail/_cross_rail_form.html')
        self.assertContains(response, 'Length must be a positive value.')
        self.assertContains(response, 'Number of rows must be a positive value.')
        self.cr_temp1.refresh_from_db()
        self.assertNotEqual(self.cr_temp1.length, 0.0) # Ensure object was not changed

    def test_cross_rail_delete_view_get(self, mock_get_session_data):
        """Test GET request for Cross Rail delete confirmation in modal."""
        response = self.client.get(reverse('gunrail:cross_rail_delete', args=[self.cr_temp1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'gunrail/_cross_rail_confirm_delete.html')
        self.assertContains(response, 'Are you absolutely sure you want to delete this Cross Rail entry?')
        self.assertContains(response, str(self.cr_temp1.length))

    def test_cross_rail_delete_view_post(self, mock_get_session_data):
        """Test successful POST request to delete a Cross Rail entry."""
        response = self.client.post(reverse('gunrail:cross_rail_delete', args=[self.cr_temp1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCrossRailList')
        self.assertFalse(GunrailCrossRailTemp.objects.filter(pk=self.cr_temp1.pk).exists())
        self.assertEqual(GunrailCrossRailTemp.objects.count(), 0)

    # --- Long Rail CRUD Views (similar structure to Cross Rail) ---

    def test_long_rail_table_partial_get(self, mock_get_session_data):
        """Test HTMX partial for Long Rail table displays data."""
        response = self.client.get(reverse('gunrail:long_rail_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'gunrail/_long_rail_table.html')
        self.assertContains(response, str(self.lr_temp1.length))
        self.assertContains(response, str(self.lr_temp1.no))

    def test_long_rail_add_view_post_success(self, mock_get_session_data):
        """Test successful POST request to add a Long Rail entry."""
        data = {'length': 25.0, 'no': 4.0}
        response = self.client.post(reverse('gunrail:long_rail_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshLongRailList')
        self.assertTrue(GunrailLongRailTemp.objects.filter(length=25.0, no=4.0, session_id=self.session_id).exists())
        self.assertEqual(GunrailLongRailTemp.objects.count(), 2)

    def test_long_rail_edit_view_post_success(self, mock_get_session_data):
        """Test successful POST request to edit a Long Rail entry."""
        data = {'length': 18.0, 'no': 2.0}
        response = self.client.post(reverse('gunrail:long_rail_edit', args=[self.lr_temp1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshLongRailList')
        self.lr_temp1.refresh_from_db()
        self.assertEqual(self.lr_temp1.length, 18.0)
        self.assertEqual(self.lr_temp1.no, 2.0)

    def test_long_rail_delete_view_post(self, mock_get_session_data):
        """Test successful POST request to delete a Long Rail entry."""
        response = self.client.post(reverse('gunrail:long_rail_delete', args=[self.lr_temp1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshLongRailList')
        self.assertFalse(GunrailLongRailTemp.objects.filter(pk=self.lr_temp1.pk).exists())
        self.assertEqual(GunrailLongRailTemp.objects.count(), 0)

    # --- Main Action Views ---

    @patch('gunrail.models.GunrailMaster.objects.finalize_gunrail_data')
    def test_proceed_view_post_success(self, mock_finalize_gunrail_data, mock_get_session_data):
        """Test successful 'Proceed' action, verifying model manager call."""
        data = {'type_val': '0', 'pitch': 0.75}
        response = self.client.post(reverse('gunrail:proceed') + f'?WONo={self.wo_no}', data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshGunrailDashboard')
        mock_finalize_gunrail_data.assert_called_once_with(
            session_id=self.session_id, comp_id=self.comp_id, fin_year_id=self.fin_year_id,
            pitch=0.75, wo_no=self.wo_no, type_val=0
        )

    def test_proceed_view_post_invalid_form(self, mock_get_session_data):
        """Test 'Proceed' action with invalid form data (e.g., missing pitch)."""
        data = {'type_val': '0', 'pitch': ''} # Missing pitch
        response = self.client.post(reverse('gunrail:proceed') + f'?WONo={self.wo_no}', data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Re-renders dashboard with errors
        self.assertTemplateUsed(response, 'gunrail/dashboard.html')
        self.assertContains(response, 'Support Pitch is required.')
        self.assertContains(response, 'Please correct the errors in the Pitch and Type fields.')
        # Ensure temporary data is still present if form is invalid
        self.assertEqual(GunrailCrossRailTemp.objects.count(), 1)
        self.assertEqual(GunrailLongRailTemp.objects.count(), 1)

    @patch('gunrail.models.GunrailMaster.objects.finalize_gunrail_data', side_effect=ValueError("Test processing error"))
    def test_proceed_view_post_model_error(self, mock_finalize_gunrail_data, mock_get_session_data):
        """Test 'Proceed' action handles errors from model manager."""
        data = {'type_val': '0', 'pitch': 0.75}
        response = self.client.post(reverse('gunrail:proceed') + f'?WONo={self.wo_no}', data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Renders dashboard with errors
        self.assertTemplateUsed(response, 'gunrail/dashboard.html')
        self.assertContains(response, 'Processing error: Test processing error')
        # Ensure temporary data is NOT cleared if the `finalize_gunrail_data` failed within an atomic block.
        # This behavior depends on how the atomic block is handled in the model manager.
        # Given `transaction.atomic()` around `finalize_gunrail_data`, temp tables would not be cleared on `ValueError`.
        self.assertEqual(GunrailCrossRailTemp.objects.count(), 1)
        self.assertEqual(GunrailLongRailTemp.objects.count(), 1)


    def test_cancel_view_get(self, mock_get_session_data):
        """Test 'Cancel' action clears temporary data and redirects."""
        self.assertEqual(GunrailCrossRailTemp.objects.count(), 1)
        self.assertEqual(GunrailLongRailTemp.objects.count(), 1)
        response = self.client.get(reverse('gunrail:cancel'))
        self.assertEqual(response.status_code, 302) # Redirects
        self.assertEqual(GunrailCrossRailTemp.objects.count(), 0) # Temp data cleared
        self.assertEqual(GunrailLongRailTemp.objects.count(), 0)
        # Verify redirect URL (assuming 'work_order_list' is a real URL to redirect to)
        self.assertRedirects(response, reverse('work_order_list'))

```

### Step 5: HTMX and Alpine.js Integration

The migration emphasizes a dynamic, single-page application feel using HTMX for all server interactions and Alpine.js for lightweight client-side state management.

-   **DataTables Initialization:** JavaScript `$(document).ready()` ensures DataTables is initialized on the dynamically loaded tables. HTMX `hx-trigger="load"` and custom events (`refreshCrossRailList`, `refreshLongRailList`) re-render the table partials when data changes, automatically re-initializing DataTables.
-   **Modal Dialogs:** A single, universal modal (`#modal`) is defined in `dashboard.html`. HTMX `hx-get` attributes on "Add", "Edit", and "Delete" buttons fetch forms or confirmation messages into `#modalContent`. Alpine.js directives (`x-data`, `x-show`, `on click`) control the modal's visibility, including showing it when HTMX content is loaded and hiding it on "Cancel" or successful submission.
-   **Form Submissions:** All forms use HTMX `hx-post` to submit data asynchronously. Upon success, `hx-swap="none"` prevents default swapping, and `HX-Trigger` headers (`refreshCrossRailList`, `refreshLongRailList`, `refreshGunrailDashboard`) are sent back from the server to instruct the client to re-render relevant sections.
-   **Loading Indicators:** `hx-indicator` attributes on buttons or forms provide visual feedback (spinners) during HTMX requests.
-   **Messages:** Django's message framework is used for user feedback, displayed in `dashboard.html` and designed to auto-dismiss using Alpine.js and HTMX `hx-swap-oob` for out-of-band updates.
-   **Client-Side Confirmation:** The original ASP.NET `onclientclick="return confirmationDelete();"` is replaced by HTMX `hx-get` to fetch a confirmation partial into the modal. The user then explicitly clicks a "Delete" button within the modal to send the actual `hx-post` request.

### Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating the ASP.NET Slido Gunrail module to Django. By strictly adhering to the "Fat Model, Thin View" paradigm, leveraging HTMX and Alpine.js for a modern frontend, and ensuring robust testing, the resulting Django application will be maintainable, scalable, and provide an enhanced user experience. The emphasis on automation-friendly patterns means this blueprint can be used for rapid development and transformation across similar modules within the ERP system.