## ASP.NET to Django Conversion Script: Comprehensive Modernization Plan for TPL Design Work Order Edit

This document outlines a strategic plan to modernize your ASP.NET application, specifically the `TPL_Design_WO_TreeView_Edit` module, by transitioning it to a robust and scalable Django-based solution. Our approach prioritizes automation, streamlined processes, and a modern user experience, moving away from legacy technologies towards a more maintainable and efficient system.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

### Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code to define the corresponding Django models.

**Analysis:** The ASP.NET code primarily interacts with `tblDG_TPL_Master`, `tblDG_Item_Master`, and `Unit_Master`. The `GetDataTable()` method dynamically constructs a dataset by joining data from these tables.

**Inferred Tables and Columns:**

*   **`tblDG_TPL_Master`** (Django Model: `TPLMaster`)
    *   `Id` (int, Primary Key)
    *   `ItemId` (int, Foreign Key to `tblDG_Item_Master`)
    *   `WONo` (string)
    *   `PId` (int, Parent ID for tree structure, self-referencing)
    *   `CId` (int, Child ID for tree structure)
    *   `Qty` (decimal)
    *   `AmdNo` (string)
    *   `CompId` (int, from session)
    *   `FinYearId` (int, from session)

*   **`tblDG_Item_Master`** (Django Model: `ItemMaster`)
    *   `Id` (int, Primary Key)
    *   `ItemCode` (string)
    *   `PartNo` (string)
    *   `ManfDesc` (string)
    *   `UOMBasic` (int, Foreign Key to `Unit_Master`)
    *   `FileName` (string, for image/drawing)
    *   `FileData` (binary, for image/drawing data)
    *   `AttName` (string, for specification sheet)
    *   `AttData` (binary, for specification sheet data)
    *   `ContentType` (string, for image/drawing MIME type)
    *   `AttContentType` (string, for specification sheet MIME type)

*   **`Unit_Master`** (Django Model: `UnitMaster`)
    *   `Id` (int, Primary Key)
    *   `Symbol` (string)

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations performed by the ASP.NET code, including data retrieval, business logic, and user interactions.

**Analysis of Functionality:**

*   **Data Retrieval & Processing:** The primary function is `GetDataTable()`, which fetches hierarchical data (`TPLMaster`) for a specific `WONo`, enriches it with item details (`ItemMaster`) and unit information (`UnitMaster`), and performs complex calculations like `TPL Qty` (via `fun.TreeQty`). It also determines "View" or "Upload" states for image/spec links based on data presence.
*   **Session Management:** `CompId`, `FinId`, and `SId` (Session ID/Username) are read from the session for data filtering.
*   **User Interface Actions:**
    *   **Display:** Presents a hierarchical list of TPL items for a Work Order.
    *   **Expand/Collapse Tree:** Toggles the display mode of the tree structure.
    *   **Item Modification ("Sel" Command):** Redirects to an `ItemMaster` edit page (`TPL_Design_Item_Edit.aspx`) if the selected item is not an "assembly item" (i.e., has no children in the TPL structure). Otherwise, it displays an error message.
    *   **File Operations (Download/Upload):** Provides links to download drawing/image files (`downloadImg`) and specification sheets (`downloadSpec`), and buttons to upload new files (`uploadImg`, `uploadSpec`) to the `ItemMaster`.
    *   **Amd No Update ("Amd" Command):** Redirects to an Amd update page (`TPL_Amd.aspx`), likely for updating the `AmdNo` field on a `TPLMaster` record.
    *   **Cancel:** Redirects to a work order grid update page (`TPL_Design_WO_Grid_Update.aspx`).
*   **Temporary Data Cleanup:** Deletes records from `tblDG_TPLItem_Temp` on page load, suggesting session-specific temporary data management.

### Step 3: Infer UI Components

**Task:** Analyze the ASP.NET controls and their roles to inform the design of equivalent Django templates with HTMX, Alpine.js, and DataTables.

**UI Components and Their Django Equivalents:**

*   **Master Page:** Will be replaced by `{% extends 'core/base.html' %}` for consistent layout.
*   **Header Elements:**
    *   `asp:Label ID="Label2"` (Work Order No.): Displayed directly in the template.
    *   `asp:Label ID="lblMsg"` (Error/Success Message): Handled by Django's `messages` framework and potentially displayed with HTMX toast notifications.
*   **Interactive Controls:**
    *   `asp:CheckBox ID="CheckBox1"` (Expand Tree): Replaced by a standard HTML checkbox, with its state managed by Alpine.js and changes triggering HTMX requests to refresh the table.
    *   `asp:Button ID="Button1"` (Cancel): A standard HTML `<a>` tag or `<button>` for redirection.
*   **Data Display:**
    *   `telerik:RadTreeList ID="RadTreeList1"`: This complex Telerik control will be replaced by a standard HTML `<table>` element initialized as a DataTables instance. The hierarchical display will be simulated or achieved with a DataTables plugin (e.g., RowGroup) if needed, otherwise, a flat list with parent/child IDs will be displayed. The "Expand Tree" functionality would involve showing/hiding child rows within DataTables.
    *   **Columns:** Explicitly defined `<th>` elements for `SN`, `Item Code`, `Description`, `UOM`, `Unit Qty`, `TPL Qty`, `Drw/Image`, `Spec. Sheet`, `Amd No`, and `Actions`.
    *   **Action Links/Buttons:**
        *   `telerik:RadButton ID="SelectButton"` ("Sel" Command): Becomes an HTMX-triggered button that fetches an `ItemMaster` edit form into a modal.
        *   `asp:LinkButton` (`downloadImg`, `downloadSpec`): Becomes standard `<a>` tags pointing to Django download views.
        *   `asp:LinkButton` (`uploadImg`, `uploadSpec`): Becomes HTMX-triggered buttons fetching file upload forms into modals.
        *   `asp:LinkButton` (`Amd`): Becomes an HTMX-triggered button fetching an Amd update form into a modal.
*   **Paging:** DataTables will inherently handle client-side pagination, replacing the custom pager template.

### Step 4: Generate Django Code

We will create a Django application, for example, named `design_tpl`.

#### 4.1 Models (`design_tpl/models.py`)

This file defines the database schema and incorporates business logic (like `TPL Qty` calculation) into the models, following the "fat model" principle.

```python
from django.db import models
from decimal import Decimal

class UnitMaster(models.Model):
    """
    Maps to the Unit_Master table, storing basic unit of measurement symbols.
    """
    id = models.AutoField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False  # Tells Django not to manage this table's schema (it already exists)
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol or 'N/A'

class ItemMaster(models.Model):
    """
    Maps to the tblDG_Item_Master table, storing item details, drawings, and specifications.
    """
    id = models.AutoField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=100, blank=True, null=True)
    part_no = models.CharField(db_column='PartNo', max_length=100, blank=True, null=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=500, blank=True, null=True)
    uom_basic = models.ForeignKey(UnitMaster, models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)
    file_name = models.CharField(db_column='FileName', max_length=255, blank=True, null=True)
    file_data = models.BinaryField(db_column='FileData', blank=True, null=True)
    att_name = models.CharField(db_column='AttName', max_length=255, blank=True, null=True)
    att_data = models.BinaryField(db_column='AttData', blank=True, null=True)
    content_type = models.CharField(db_column='ContentType', max_length=50, blank=True, null=True)
    att_content_type = models.CharField(db_column='AttContentType', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.item_code or self.part_no or 'N/A'
    
    @property
    def has_image(self):
        """Checks if an image/drawing file exists for this item."""
        return bool(self.file_name and self.file_data)

    @property
    def has_spec(self):
        """Checks if a specification sheet file exists for this item."""
        return bool(self.att_name and self.att_data)

    def get_image_action(self):
        """Determines if 'View' or 'Upload' should be displayed for image."""
        return "View" if self.has_image else "Upload"

    def get_spec_action(self):
        """Determines if 'View' or 'Upload' should be displayed for spec sheet."""
        return "View" if self.has_spec else "Upload"

class TPLMaster(models.Model):
    """
    Maps to the tblDG_TPL_Master table, representing the hierarchical Bill of Materials structure.
    """
    id = models.AutoField(db_column='Id', primary_key=True)
    item = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId')
    wo_no = models.CharField(db_column='WONo', max_length=50)
    # Self-referential foreign key for parent-child relationships (PId -> CId)
    parent = models.ForeignKey('self', models.DO_NOTHING, db_column='PId', related_name='children', blank=True, null=True)
    child_id = models.IntegerField(db_column='CId') # CId as distinct identifier in tree
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3)
    amd_no = models.CharField(db_column='AmdNo', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblDG_TPL_Master'
        verbose_name = 'TPL Master'
        verbose_name_plural = 'TPL Masters'
        # A common constraint to ensure unique TPL items within a work order's structure
        unique_together = (('wo_no', 'child_id', 'item', 'comp_id'),)

    def __str__(self):
        return f"{self.wo_no} - {self.item.item_code} (CId: {self.child_id})"

    @classmethod
    def get_tree_data(cls, wo_no, comp_id, fin_year_id):
        """
        Retrieves and processes TPL data for a given Work Order Number, Company, and Financial Year.
        This method replaces the complex logic in ASP.NET's GetDataTable() by performing
        necessary lookups and calculations. It prepares data for the table display.
        """
        # Fetch all relevant TPL items and prefetch related ItemMaster and UnitMaster data
        # to avoid N+1 query problems. Order by parent_id and child_id to maintain hierarchy context.
        tpl_items_queryset = cls.objects.filter(
            wo_no=wo_no,
            comp_id=comp_id,
            fin_year_id__lte=fin_year_id # Assuming FinYearId <= current FinYearId
        ).select_related('item', 'item__uom_basic').order_by('parent_id', 'child_id') # Order by PId ASC

        results = []
        for tpl in tpl_items_queryset:
            item_code = tpl.item.item_code if tpl.item.item_code else tpl.item.part_no
            description = tpl.item.manf_desc
            uom_symbol = tpl.item.uom_basic.symbol if tpl.item.uom_basic else ''
            
            # Replicate the `fun.TreeQty` logic to calculate the cumulative TPL Quantity.
            # This involves traversing up the parent chain and multiplying quantities.
            cumulative_qty = TPLMaster.calculate_cumulative_qty(wo_no, tpl.parent_id, tpl.child_id)

            results.append({
                'id': tpl.id,
                'item_id': tpl.item.id,
                'wo_no': tpl.wo_no,
                'p_id': tpl.parent_id,
                'c_id': tpl.child_id,
                'item_code': item_code,
                'description': description,
                'uom': uom_symbol,
                'unit_qty': f"{tpl.qty:.3f}", # Format to 3 decimal places
                'tpl_qty': f"{cumulative_qty:.3f}", # Format to 3 decimal places
                'file_name': tpl.item.get_image_action(), # 'View' or 'Upload'
                'att_name': tpl.item.get_spec_action(),   # 'View' or 'Upload'
                'amd_no': tpl.amd_no,
            })
        return results

    @classmethod
    def calculate_cumulative_qty(cls, wo_no, current_p_id, current_c_id):
        """
        Calculates the cumulative quantity by traversing up the TPL hierarchy,
        multiplying the quantities of all ancestors up to the root.
        This method replicates the logic of the original `fun.TreeQty`.
        """
        quantities_list = []
        # Find the node that is identified by current_c_id and has current_p_id as its parent.
        # This allows us to trace a specific path in case of multiple items with same CId but different parents.
        current_node = cls.objects.filter(wo_no=wo_no, child_id=current_c_id, parent_id=current_p_id).first()

        if current_node:
            quantities_list.append(current_node.qty)
            # Recursively find quantities of parents
            parent_node = current_node.parent
            while parent_node:
                quantities_list.append(parent_node.qty)
                parent_node = parent_node.parent
        
        # Multiply all quantities in the collected list
        cumulative_product = Decimal(1.0)
        for qty in quantities_list:
            cumulative_product *= qty
        return cumulative_product
        
    def is_assembly_item(self):
        """
        Checks if this TPL item is an assembly item by determining if it has children.
        This mimics the ASP.NET logic blocking modification of assembly items.
        """
        return self.children.exists() # Checks if any TPLMaster records have this item as their parent.

```

#### 4.2 Forms (`design_tpl/forms.py`)

Django forms for interacting with `ItemMaster` data and handling file uploads.

```python
from django import forms
from .models import ItemMaster, UnitMaster

class ItemMasterForm(forms.ModelForm):
    """
    Form for editing ItemMaster details, used in a modal context.
    """
    class Meta:
        model = ItemMaster
        fields = ['item_code', 'part_no', 'manf_desc', 'uom_basic']
        widgets = {
            'item_code': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'part_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'manf_desc': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'uom_basic': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate UOM dropdown with all UnitMaster objects
        self.fields['uom_basic'].queryset = UnitMaster.objects.all().order_by('symbol')

class ItemFileUploadForm(forms.ModelForm):
    """
    Form for uploading drawing/image or specification files for an ItemMaster.
    """
    file = forms.FileField(required=True, label="Select File",
                           widget=forms.FileInput(attrs={'class': 'block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'}))
    
    class Meta:
        model = ItemMaster
        fields = [] # No direct model fields from form, fields are updated directly in the view logic.

    def __init__(self, *args, **kwargs):
        self.upload_type = kwargs.pop('upload_type', 'image') # 'image' or 'spec'
        super().__init__(*args, **kwargs)
        if self.upload_type == 'image':
            self.fields['file'].label = "Select Drawing/Image File"
        else: # 'spec'
            self.fields['file'].label = "Select Specification Sheet File"

```

#### 4.3 Views (`design_tpl/views.py`)

These views handle the main page display, table content loading via HTMX, file operations, and item detail editing within modal dialogues.

```python
from django.views.generic import TemplateView, ListView, UpdateView
from django.urls import reverse_lazy
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.conf import settings
import logging

logger = logging.getLogger(__name__)

class TPLDesignWOEditView(TemplateView):
    """
    Main view for the TPL Design Work Order Edit page.
    Displays work order number, handles messages, and sets up the container for the HTMX-loaded table.
    """
    template_name = 'design_tpl/tpl_master/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        wo_no = self.kwargs.get('wo_no')
        
        # Mimic ASP.NET session variables for CompId and FinId.
        # In a production environment, these would be securely managed via user authentication
        # and profile data, not hardcoded.
        comp_id = self.request.session.get('compid', 1) 
        fin_year_id = self.request.session.get('finyear', 1)

        # Retrieve and display messages passed via query string (e.g., from previous redirects)
        msg = self.request.GET.get('msg')
        if msg:
            messages.info(self.request, msg)

        context['wo_no'] = wo_no
        context['comp_id'] = comp_id
        context['fin_year_id'] = fin_year_id
        # Manage "Expand Tree" checkbox state in session for persistence
        context['expand_tree'] = self.request.session.get('expand_tree', True) 

        return context

class TPLMasterTablePartialView(TemplateView):
    """
    HTMX-loadable partial view to render the TPL Master data table.
    It calls the fat model method to get processed data suitable for display.
    """
    template_name = 'design_tpl/tpl_master/_tpl_master_table.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        wo_no = self.kwargs.get('wo_no')
        comp_id = self.request.session.get('compid', 1)
        fin_year_id = self.request.session.get('finyear', 1)
        
        # Call the model method to get the processed list of dictionaries
        context['tpl_items'] = TPLMaster.get_tree_data(wo_no, comp_id, fin_year_id)
        context['expand_tree'] = self.request.session.get('expand_tree', True)
        return context

def toggle_tree_expansion(request, wo_no):
    """
    Function-based view to toggle the 'Expand Tree' checkbox state in the session.
    Triggered by HTMX and sends a refresh signal for the table.
    """
    if request.method == 'POST' and request.headers.get('HX-Request'):
        current_state = request.session.get('expand_tree', True)
        request.session['expand_tree'] = not current_state
        messages.success(request, f"Tree display {'expanded' if not current_state else 'collapsed'}.")
        # Send a 204 No Content response with an HX-Trigger to refresh the table
        return HttpResponse(
            status=204,
            headers={
                'HX-Trigger': 'refreshTPLMasterList'
            }
        )
    return HttpResponse(status=400) # Bad request for non-HTMX or GET requests

def download_file_view(request, item_id, file_type):
    """
    View to handle file downloads (drawing/image or specification sheet) for an ItemMaster.
    Mimics the functionality of `DownloadFile.aspx`.
    """
    item = get_object_or_404(ItemMaster, pk=item_id)
    
    file_data = None
    file_name = None
    content_type = 'application/octet-stream' # Default generic content type

    if file_type == 'image':
        file_data = item.file_data
        file_name = item.file_name
        if item.content_type: # Use specific content type if available
            content_type = item.content_type
    elif file_type == 'spec':
        file_data = item.att_data
        file_name = item.att_name
        if item.att_content_type: # Use specific content type if available
            content_type = item.att_content_type
    else:
        messages.error(request, "Invalid file type specified for download.")
        # Redirect back to the TPL edit page or a relevant one, retaining WONo
        wo_no = request.GET.get('WONo')
        if wo_no:
            return redirect(reverse_lazy('design_tpl:tplmaster_edit', kwargs={'wo_no': wo_no}))
        return redirect(reverse_lazy('design_tpl:tpl_work_order_list')) # Fallback

    if not file_data:
        messages.error(request, "File not found or empty.")
        wo_no = request.GET.get('WONo')
        if wo_no:
            return redirect(reverse_lazy('design_tpl:tplmaster_edit', kwargs={'wo_no': wo_no}))
        return redirect(reverse_lazy('design_tpl:tpl_work_order_list'))

    response = HttpResponse(file_data, content_type=content_type)
    response['Content-Disposition'] = f'attachment; filename="{file_name}"'
    return response

class ItemMasterUpdateView(UpdateView):
    """
    View for updating ItemMaster details, typically loaded into a modal via HTMX.
    This replaces `TPL_Design_Item_Edit.aspx`.
    """
    model = ItemMaster
    form_class = ItemMasterForm
    template_name = 'design_tpl/item_master/form.html' # Partial template for modal rendering
    
    # Custom success URL to redirect back to the main TPL edit page for the specific WO_No
    def get_success_url(self):
        wo_no = self.request.GET.get('WONo')
        if wo_no:
            return reverse_lazy('design_tpl:tplmaster_edit', kwargs={'wo_no': wo_no})
        return reverse_lazy('design_tpl:tpl_work_order_list') # Fallback to a generic WO list

    def form_valid(self, form):
        # Implement the ASP.NET logic: "Assembly item cannot be modified."
        # This check is crucial and requires context (the CId of the TPL item that was clicked).
        selected_cid = self.request.GET.get('CId')
        wo_no = self.request.GET.get('WONo')

        if selected_cid and wo_no:
            # Check if this specific TPLMaster item (identified by WONo and CId) is a parent
            # The original code's check `fun.select("*", "tblDG_TPL_Master", " PId='"+cid+"'")`
            # implies checking if `cid` from the selected row exists as a `PId` in *any* other row.
            if TPLMaster.objects.filter(wo_no=wo_no, parent__child_id=selected_cid).exists():
                messages.error(self.request, "Assembly item cannot be modified.")
                # For HTMX, return a 400 status and re-render the form with errors
                return HttpResponse(
                    render(self.request, self.template_name, {'form': form}),
                    status=400,
                    headers={
                        'HX-Retarget': '#modalContent', # Keep modal open, show error inside
                        'HX-Reswap': 'innerHTML',
                    }
                )

        response = super().form_valid(form)
        messages.success(self.request, f"Item '{self.object.item_code}' updated successfully.")
        
        # For HTMX requests, return 204 No Content to close modal and trigger table refresh
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshTPLMasterList' # Signal the main table to reload its content
                }
            )
        return response

    def form_invalid(self, form):
        # For HTMX, re-render the form with errors inside the modal
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form})
        return super().form_invalid(form)

class ItemFileUploadView(UpdateView):
    """
    View for uploading drawing/image or specification files for an ItemMaster,
    typically loaded into a modal via HTMX. This replaces `UploadDrw.aspx`.
    """
    model = ItemMaster
    form_class = ItemFileUploadForm
    template_name = 'design_tpl/item_master/upload_form.html' # Partial template for modal

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        upload_type_param = self.request.GET.get('img') # 0 for image, 1 for spec
        context['upload_type'] = 'image' if upload_type_param == '0' else 'spec'
        # Pass the upload_type to the form for dynamic label changes
        context['form'] = ItemFileUploadForm(instance=self.get_object(), upload_type=context['upload_type'])
        return context

    def post(self, request, *args, **kwargs):
        self.object = self.get_object()
        upload_type_param = request.GET.get('img')
        upload_type = 'image' if upload_type_param == '0' else 'spec'
        form = ItemFileUploadForm(request.POST, request.FILES, instance=self.object, upload_type=upload_type)
        
        if form.is_valid():
            uploaded_file = form.cleaned_data['file']
            
            # Read file content into binary fields and update relevant fields
            file_content = uploaded_file.read()
            file_name = uploaded_file.name
            content_type = uploaded_file.content_type
            
            if upload_type == 'image':
                self.object.file_data = file_content
                self.object.file_name = file_name
                self.object.content_type = content_type
            elif upload_type == 'spec':
                self.object.att_data = file_content
                self.object.att_name = file_name
                self.object.att_content_type = content_type
            
            self.object.save()
            messages.success(request, f"File '{file_name}' uploaded successfully.")
            
            if request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshTPLMasterList' # Signal main table to refresh
                    }
                )
            # Fallback for non-HTMX (should ideally not happen with HTMX-first approach)
            wo_no = request.GET.get('WONo')
            return redirect(reverse_lazy('design_tpl:tplmaster_edit', kwargs={'wo_no': wo_no}))
        else:
            if request.headers.get('HX-Request'):
                return render(request, self.template_name, {'form': form, 'upload_type': upload_type, 'item_master': self.object})
            return super().form_invalid(form)

class TPLMasterAmdView(UpdateView):
    """
    View for updating the AmdNo for a TPLMaster item, typically loaded into a modal via HTMX.
    This replaces `TPL_Amd.aspx`.
    """
    model = TPLMaster
    fields = ['amd_no'] # Assuming only AmdNo is updated on this specific page
    template_name = 'design_tpl/tpl_master/amd_form.html' # Partial template for modal

    # Custom success URL to redirect back to the main TPL edit page
    def get_success_url(self):
        wo_no = self.request.GET.get('WONo')
        if wo_no:
            return reverse_lazy('design_tpl:tplmaster_edit', kwargs={'wo_no': wo_no})
        return reverse_lazy('design_tpl:tpl_work_order_list') # Fallback

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, f"Amd No updated for TPL item {self.object.id}.")
        
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshTPLMasterList'
                }
            )
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass the TPLMaster object to the template for initial value and context
        context['tplmaster'] = self.object 
        return context

# A placeholder view for redirection when WONo might not be present or for general list redirect
class TPLMasterListRedirectView(TemplateView):
    """
    A placeholder view for redirection, e.g., for the 'Cancel' button.
    It simulates redirecting to a general Work Order list or search page.
    """
    def get(self, request, *args, **kwargs):
        messages.error(request, "Navigation to work order list: Work Order Number context lost or not provided.")
        # In a real application, this would redirect to an actual list/search page for work orders.
        # For now, it redirects to a generic placeholder.
        return redirect(reverse_lazy('design_tpl:tpl_work_order_list'))

```

#### 4.4 Templates

The Django templates are structured to work with HTMX for dynamic content updates and modal interactions.

**`design_tpl/tpl_master/list.html`** (Main page for a specific Work Order)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 space-y-4 md:space-y-0">
        <h2 class="text-2xl font-bold text-gray-800">Edit TPL</h2>
        <div class="flex flex-wrap items-center space-x-4">
            <span class="font-bold text-lg text-gray-700">Wo No:</span>
            <span class="text-lg text-blue-600">{{ wo_no }}</span>
            <span id="msg-label" class="text-red-600 ml-4">
                {% if messages %}
                    {% for message in messages %}
                        {{ message }}
                    {% endfor %}
                {% endif %}
            </span>
            <div x-data="{ expanded: {{ expand_tree|lower }} }" class="flex items-center">
                <input type="checkbox" id="expandTreeCheckbox" 
                       class="form-checkbox h-5 w-5 text-blue-600 rounded focus:ring-blue-500" 
                       x-model="expanded"
                       hx-post="{% url 'design_tpl:toggle_tree_expansion' wo_no=wo_no %}"
                       hx-trigger="change"
                       hx-swap="none"
                       hx-indicator="#loadingIndicator">
                <label for="expandTreeCheckbox" class="ml-2 text-gray-700 font-semibold cursor-pointer">Expand Tree</label>
                <div id="loadingIndicator" class="htmx-indicator ml-2">
                    <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                </div>
            </div>
            <a href="{% url 'design_tpl:tpl_work_order_grid_update' %}" 
               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded shadow-sm transition duration-150 ease-in-out">
                Cancel
            </a>
        </div>
    </div>
    
    <div id="tplMasterTableContainer"
         hx-trigger="load, refreshTPLMasterList from:body"
         hx-get="{% url 'design_tpl:tplmaster_table_partial' wo_no=wo_no %}"
         hx-swap="innerHTML"
         hx-indicator="#loadingTableIndicator">
        <!-- DataTable will be loaded here via HTMX -->
        <div id="loadingTableIndicator" class="htmx-indicator text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading TPL data...</p>
        </div>
    </div>
    
    <!-- Modal for forms (edit item, upload files, AmdNo) -->
    <div id="modal" class="fixed inset-0 z-50 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me
            on htmx:afterOnLoad from #modalContent
                if event.detail.xhr.status == 204 or event.detail.xhr.status == 400
                    remove .is-active from #modal
            on htmx:beforeOnLoad from #modalContent add .htmx-indicator-on-load to #modalContent">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-4xl w-full mx-4 my-8 relative h-auto overflow-y-auto"
             _="on htmx:afterOnLoad from #modalContent remove .htmx-indicator-on-load from #modalContent">
            <!-- Loading indicator for modal content itself -->
            <div class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10 htmx-indicator-on-load">
                <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js setup (if more complex state needed for this page)
    document.addEventListener('alpine:init', () => {
        Alpine.data('tplEdit', () => ({
            // Current state of expanded checkbox, initialized from Django context
            expanded: {{ expand_tree|lower }},
            init() {
                // Any client-side initialization for Alpine components on page load
            }
        }));
    });

    // Global HTMX listener for messages (optional, if you want a more robust notification system)
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.xhr.status === 204) {
            // Success messages are handled by Django's messages framework and HX-Trigger
            // For example, messages could be displayed by a separate HTMX request to a message partial
            // or an Alpine.js component listening to HX-Trigger events.
            // Simplified for this example, messages are rendered on refresh or on specific error.
        }
    });
</script>
{% endblock %}
```

**`design_tpl/tpl_master/_tpl_master_table.html`** (Partial template for the DataTables content)

```html
<table id="tplMasterTable" class="min-w-full bg-white border border-gray-200 shadow-sm rounded-lg overflow-hidden">
    <thead>
        <tr class="bg-gray-50 border-b border-gray-200">
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Qty</th>
            <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">TPL Qty</th>
            <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Drw/Image</th>
            <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Spec. Sheet</th>
            <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Amd No</th>
            <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for tpl_item in tpl_items %}
        <tr class="{% cycle 'bg-white' 'bg-gray-50' %} border-b border-gray-100 hover:bg-gray-100 transition duration-150 ease-in-out">
            <td class="py-2 px-4 text-sm text-gray-700">{{ forloop.counter }}</td>
            <td class="py-2 px-4 text-sm text-gray-700">{{ tpl_item.item_code }}</td>
            <td class="py-2 px-4 text-sm text-gray-700">{{ tpl_item.description }}</td>
            <td class="py-2 px-4 text-center text-sm text-gray-700">{{ tpl_item.uom }}</td>
            <td class="py-2 px-4 text-right text-sm text-gray-700">{{ tpl_item.unit_qty }}</td>
            <td class="py-2 px-4 text-right text-sm text-gray-700">{{ tpl_item.tpl_qty }}</td>
            <td class="py-2 px-4 text-center text-sm text-gray-700">
                {% if tpl_item.file_name == 'View' %}
                    <a href="{% url 'design_tpl:download_file' item_id=tpl_item.item_id file_type='image' %}?WONo={{ tpl_item.wo_no }}" 
                       class="text-blue-600 hover:underline">View</a>
                {% else %}
                    <button 
                        class="text-blue-600 hover:underline focus:outline-none py-1 px-2 rounded-md"
                        hx-get="{% url 'design_tpl:upload_file' pk=tpl_item.item_id %}?WONo={{ tpl_item.wo_no }}&img=0"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Upload
                    </button>
                {% endif %}
            </td>
            <td class="py-2 px-4 text-center text-sm text-gray-700">
                {% if tpl_item.att_name == 'View' %}
                    <a href="{% url 'design_tpl:download_file' item_id=tpl_item.item_id file_type='spec' %}?WONo={{ tpl_item.wo_no }}" 
                       class="text-blue-600 hover:underline">View</a>
                {% else %}
                    <button 
                        class="text-blue-600 hover:underline focus:outline-none py-1 px-2 rounded-md"
                        hx-get="{% url 'design_tpl:upload_file' pk=tpl_item.item_id %}?WONo={{ tpl_item.wo_no }}&img=1"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Upload
                    </button>
                {% endif %}
            </td>
            <td class="py-2 px-4 text-center text-sm text-gray-700">
                <button 
                    class="text-blue-600 hover:underline focus:outline-none py-1 px-2 rounded-md"
                    hx-get="{% url 'design_tpl:amd_tpl_item' pk=tpl_item.id %}?WONo={{ tpl_item.wo_no }}&ItemId={{ tpl_item.item_id }}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    {{ tpl_item.amd_no|default:'Add/Edit' }}
                </button>
            </td>
            <td class="py-2 px-4 text-center text-sm text-gray-700">
                <button 
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-3 rounded text-xs shadow-sm transition duration-150 ease-in-out"
                    hx-get="{% url 'design_tpl:edit_item_master' pk=tpl_item.item_id %}?WONo={{ tpl_item.wo_no }}&CId={{ tpl_item.c_id }}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // This script ensures DataTables is initialized each time the partial is loaded via HTMX.
    // It's important to destroy any previous instance before re-initializing.
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#tplMasterTable')) {
            $('#tplMasterTable').DataTable().destroy();
        }
        $('#tplMasterTable').DataTable({
            "pageLength": 17, // Matches ASP.NET RadTreeList PageSize
            "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
            // Order by PId, then CId to simulate tree hierarchy, for visual grouping.
            "order": [[ {% if expand_tree %} 0 {% else %} 0 {% endif %} , 'asc']], // Adjust column index as needed for PId/CId
            // If full tree view with expand/collapse is required, DataTables extensions like RowGroup or custom child rows would be needed.
            // For now, it's a sortable/searchable flat table.
            "initComplete": function(settings, json) {
                // Optionally adjust visibility of rows based on `expand_tree` for simple hide/show
                // if (!{{ expand_tree|lower }}) {
                //     // Logic to hide child rows if collapsed, perhaps based on 'p_id' value
                // }
            }
        });
    });
</script>
```

**`design_tpl/item_master/form.html`** (Partial for Item Master Edit modal)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Edit Item Details</h3>
    <form hx-put="{% url 'design_tpl:edit_item_master' pk=form.instance.pk %}?WONo={{ request.GET.WONo }}&CId={{ request.GET.CId }}" hx-swap="none" hx-indicator="#editItemIndicator">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
            <div class="text-red-500 text-sm mt-1">
                {# Display non-field errors or messages from form_invalid #}
                {% if messages %}
                    {% for message in messages %}
                        {{ message }}
                    {% endfor %}
                {% endif %}
            </div>
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow-sm transition duration-150 ease-in-out">
                Save Changes
            </button>
            <div id="editItemIndicator" class="htmx-indicator ml-2">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
            </div>
        </div>
    </form>
</div>
```

**`design_tpl/item_master/upload_form.html`** (Partial for File Upload modal)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Upload {% if upload_type == 'image' %}Drawing/Image{% else %}Specification Sheet{% endif %}</h3>
    <form hx-post="{% url 'design_tpl:upload_file' pk=item_master.pk %}?WONo={{ request.GET.WONo }}&img={% if upload_type == 'image' %}0{% else %}1{% endif %}" hx-encoding="multipart/form-data" hx-swap="none" hx-indicator="#uploadFileIndicator">
        {% csrf_token %}
        
        <div class="space-y-4">
            <div class="mb-4">
                <label for="{{ form.file.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.file.label }}
                </label>
                {{ form.file }}
                {% if form.file.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.file.errors }}</p>
                {% endif %}
            </div>
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow-sm transition duration-150 ease-in-out">
                Upload
            </button>
            <div id="uploadFileIndicator" class="htmx-indicator ml-2">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
            </div>
        </div>
    </form>
</div>
```

**`design_tpl/tpl_master/amd_form.html`** (Partial for Amd No. modal)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Update Amd No</h3>
    <form hx-post="{% url 'design_tpl:amd_tpl_item' pk=tplmaster.pk %}?WONo={{ request.GET.WONo }}&ItemId={{ request.GET.ItemId }}" hx-swap="none" hx-indicator="#amdIndicator">
        {% csrf_token %}
        
        <div class="space-y-4">
            <div class="mb-4">
                <label for="id_amd_no" class="block text-sm font-medium text-gray-700">
                    Amd No
                </label>
                <input type="text" name="amd_no" id="id_amd_no" value="{{ tplmaster.amd_no|default:'' }}"
                       class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
            </div>
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow-sm transition duration-150 ease-in-out">
                Update Amd No
            </button>
            <div id="amdIndicator" class="htmx-indicator ml-2">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
            </div>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`design_tpl/urls.py`)

This file defines the URL patterns, making the application accessible through clear and meaningful paths.

```python
from django.urls import path
from .views import (
    TPLDesignWOEditView, TPLMasterTablePartialView, toggle_tree_expansion,
    download_file_view, ItemMasterUpdateView, ItemFileUploadView,
    TPLMasterAmdView, TPLMasterListRedirectView
)

app_name = 'design_tpl' # Namespace for this application's URLs

urlpatterns = [
    # Main page for editing TPL for a specific Work Order Number (WO_No)
    path('tpl/edit/<str:wo_no>/', TPLDesignWOEditView.as_view(), name='tplmaster_edit'),
    
    # HTMX endpoint to fetch and update the TPL master table content
    path('tpl/edit/<str:wo_no>/table/', TPLMasterTablePartialView.as_view(), name='tplmaster_table_partial'),
    
    # HTMX endpoint to toggle the 'Expand Tree' checkbox state
    path('tpl/edit/<str:wo_no>/toggle-expansion/', toggle_tree_expansion, name='toggle_tree_expansion'),
    
    # Item Master related views, typically accessed via modals
    path('item/edit/<int:pk>/', ItemMasterUpdateView.as_view(), name='edit_item_master'),
    path('item/upload/<int:pk>/', ItemFileUploadView.as_view(), name='upload_file'),
    
    # TPL Master Amd No update view, typically accessed via a modal
    path('tpl/amd/<int:pk>/', TPLMasterAmdView.as_view(), name='amd_tpl_item'),
    
    # View for downloading files (drawings/images/specs)
    path('download-file/<int:item_id>/<str:file_type>/', download_file_view, name='download_file'),

    # Redirect/Fallback URLs:
    # This URL simulates the ASP.NET redirect for the "Cancel" button.
    # It would ideally lead to a generic work order listing/search page.
    path('tpl/work-order-grid-update/', TPLMasterListRedirectView.as_view(), name='tpl_work_order_grid_update'),
    # A generic redirect for cases where WO_No might be missing or for general list redirection.
    path('tpl/list-redirect/', TPLMasterListRedirectView.as_view(), name='tplmaster_list_redirect'), 
    # Placeholder for the actual TPL Work Order List page
    path('tpl/work-order-list/', TPLMasterListRedirectView.as_view(), name='tpl_work_order_list'),
]

```

#### 4.6 Tests (`design_tpl/tests.py`)

Comprehensive unit tests for models and integration tests for views ensure the correctness and reliability of the migrated application.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from unittest.mock import patch, MagicMock
from decimal import Decimal
from django.core.files.uploadedfile import SimpleUploadedFile

from .models import TPLMaster, ItemMaster, UnitMaster

# Custom Client to mock session data for testing purposes
class MockSessionClient(Client):
    """
    A custom test client that allows mocking session data,
    emulating how session variables (CompId, FinYearId) would be available
    in a real Django application from user login or context.
    """
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.session_data = {} # Stores mock session data

    def get_session(self):
        """Returns a MagicMock object representing the session."""
        return MagicMock(**self.session_data)

    def login(self, **credentials):
        """Simulates login by setting session variables."""
        self.session_data.update(credentials)
        return True # Indicate successful simulation

    # Patch the session load method for GET, POST, PUT requests
    # to ensure our mock session data is used.
    def get(self, path, data=None, follow=False, secure=False, **extra):
        with patch('django.contrib.sessions.backends.db.SessionStore.load', return_value=self.session_data):
            return super().get(path, data, follow, secure, **extra)

    def post(self, path, data=None, content_type=None, follow=False, secure=False, **extra):
        with patch('django.contrib.sessions.backends.db.SessionStore.load', return_value=self.session_data):
            return super().post(path, data, content_type, follow, secure, **extra)

    def put(self, path, data=None, content_type='application/x-www-form-urlencoded', follow=False, secure=False, **extra):
        with patch('django.contrib.sessions.backends.db.SessionStore.load', return_value=self.session_data):
            return super().put(path, data, content_type, follow, secure, **extra)

# --- Model Unit Tests ---

class UnitMasterModelTest(TestCase):
    """Tests for the UnitMaster model."""
    @classmethod
    def setUpTestData(cls):
        # Create test units once for all tests in this class
        UnitMaster.objects.create(id=1, symbol='Kg')
        UnitMaster.objects.create(id=2, symbol='Pcs')

    def test_unit_creation(self):
        unit = UnitMaster.objects.get(symbol='Kg')
        self.assertEqual(unit.symbol, 'Kg')
        self.assertEqual(str(unit), 'Kg')
    
    def test_verbose_name(self):
        self.assertEqual(UnitMaster._meta.verbose_name, 'Unit')
        self.assertEqual(UnitMaster._meta.verbose_name_plural, 'Units')

class ItemMasterModelTest(TestCase):
    """Tests for the ItemMaster model, including file presence properties."""
    @classmethod
    def setUpTestData(cls):
        unit_kg = UnitMaster.objects.create(id=1, symbol='Kg')
        # Item with file data
        ItemMaster.objects.create(
            id=101, item_code='ITEM001', part_no='PART-A',
            manf_desc='Test Item Description 1', uom_basic=unit_kg,
            file_name='image.jpg', file_data=b'some_image_data',
            att_name='spec.pdf', att_data=b'some_spec_data',
            content_type='image/jpeg', att_content_type='application/pdf'
        )
        # Item without file data
        ItemMaster.objects.create(
            id=102, item_code='ITEM002', part_no='PART-B',
            manf_desc='Test Item Description 2', uom_basic=unit_kg,
            file_name='', file_data=None, att_name='', att_data=None
        )

    def test_item_creation(self):
        item = ItemMaster.objects.get(item_code='ITEM001')
        self.assertEqual(item.manf_desc, 'Test Item Description 1')
        self.assertEqual(str(item), 'ITEM001')
        self.assertTrue(item.has_image)
        self.assertTrue(item.has_spec)
        self.assertEqual(item.get_image_action(), 'View')
        self.assertEqual(item.get_spec_action(), 'View')

    def test_item_without_files(self):
        item = ItemMaster.objects.get(item_code='ITEM002')
        self.assertFalse(item.has_image)
        self.assertFalse(item.has_spec)
        self.assertEqual(item.get_image_action(), 'Upload')
        self.assertEqual(item.get_spec_action(), 'Upload')
    
    def test_verbose_name(self):
        self.assertEqual(ItemMaster._meta.verbose_name, 'Item')
        self.assertEqual(ItemMaster._meta.verbose_name_plural, 'Items')

class TPLMasterModelTest(TestCase):
    """Tests for the TPLMaster model, including tree structure and quantity calculations."""
    @classmethod
    def setUpTestData(cls):
        unit_pcs = UnitMaster.objects.create(id=1, symbol='Pcs')
        item1 = ItemMaster.objects.create(id=1, item_code='ITEM1_ROOT', uom_basic=unit_pcs)
        item2 = ItemMaster.objects.create(id=2, item_code='ITEM2_CHILD', uom_basic=unit_pcs)
        item3 = ItemMaster.objects.create(id=3, item_code='ITEM3_GRANDCHILD', uom_basic=unit_pcs)

        # Create a sample tree structure for testing cumulative quantity logic:
        # Root (CId:1, Item1, Qty:10)
        #  └─ Child (CId:2, Item2, Qty:2) - parent is CId=1
        #     └─ Grandchild (CId:3, Item3, Qty:0.5) - parent is CId=2

        TPLMaster.objects.create(id=1, item=item1, wo_no='WO001', parent_id=None, child_id=1, qty=Decimal('10.000'), comp_id=1, fin_year_id=1)
        TPLMaster.objects.create(id=2, item=item2, wo_no='WO001', parent_id=1, child_id=2, qty=Decimal('2.000'), comp_id=1, fin_year_id=1)
        TPLMaster.objects.create(id=3, item=item3, wo_no='WO001', parent_id=2, child_id=3, qty=Decimal('0.500'), comp_id=1, fin_year_id=1)

    def test_tpl_creation(self):
        tpl = TPLMaster.objects.get(wo_no='WO001', child_id=1)
        self.assertEqual(tpl.item.item_code, 'ITEM1_ROOT')
        self.assertEqual(tpl.qty, Decimal('10.000'))
        self.assertIsNone(tpl.parent)
    
    def test_tree_relationships(self):
        parent_node = TPLMaster.objects.get(child_id=1)
        child_node = TPLMaster.objects.get(child_id=2)
        grandchild_node = TPLMaster.objects.get(child_id=3)

        self.assertEqual(child_node.parent.child_id, parent_node.child_id) # Check parent relationship by CId
        self.assertEqual(grandchild_node.parent.child_id, child_node.child_id)
        self.assertIn(child_node, parent_node.children.all())
        self.assertIn(grandchild_node, child_node.children.all())
    
    def test_is_assembly_item(self):
        root_tpl = TPLMaster.objects.get(child_id=1)
        child_tpl = TPLMaster.objects.get(child_id=2)
        grandchild_tpl = TPLMaster.objects.get(child_id=3)
        
        self.assertTrue(root_tpl.is_assembly_item()) # Has child_id 2
        self.assertTrue(child_tpl.is_assembly_item()) # Has child_id 3
        self.assertFalse(grandchild_tpl.is_assembly_item()) # No children

    def test_calculate_cumulative_qty(self):
        # Root node: Qty = 10.0
        qty_root = TPLMaster.calculate_cumulative_qty('WO001', None, 1)
        self.assertEqual(qty_root, Decimal('10.0')) 
        
        # Child node (CId=2, PId=1): Path (Root Qty * Child Qty) = 10.0 * 2.0 = 20.0
        qty_child = TPLMaster.calculate_cumulative_qty('WO001', 1, 2)
        self.assertEqual(qty_child, Decimal('20.0'))

        # Grandchild node (CId=3, PId=2): Path (Root Qty * Child Qty * Grandchild Qty) = 10.0 * 2.0 * 0.5 = 10.0
        qty_grandchild = TPLMaster.calculate_cumulative_qty('WO001', 2, 3)
        self.assertEqual(qty_grandchild, Decimal('10.0'))

    def test_get_tree_data(self):
        data = TPLMaster.get_tree_data(wo_no='WO001', comp_id=1, fin_year_id=1)
        self.assertEqual(len(data), 3)
        
        # Verify processed data structure and values
        item1_data = next(item for item in data if item['item_code'] == 'ITEM1_ROOT')
        self.assertEqual(item1_data['unit_qty'], '10.000')
        self.assertEqual(item1_data['tpl_qty'], '10.000') # Root's TPL Qty is its own unit qty
        self.assertEqual(item1_data['uom'], 'Pcs')
        self.assertEqual(item1_data['file_name'], 'Upload') # Default to upload as no file data for these mock items
        
        item2_data = next(item for item in data if item['item_code'] == 'ITEM2_CHILD')
        self.assertEqual(item2_data['unit_qty'], '2.000')
        self.assertEqual(item2_data['tpl_qty'], '20.000') # 10.0 * 2.0
        
        item3_data = next(item for item in data if item['item_code'] == 'ITEM3_GRANDCHILD')
        self.assertEqual(item3_data['unit_qty'], '0.500')
        self.assertEqual(item3_data['tpl_qty'], '10.000') # 10.0 * 2.0 * 0.5

# --- View Integration Tests ---

class TPLDesignWOEditViewTest(TestCase):
    """Tests for the main TPL Design Work Order Edit page and its HTMX interactions."""
    def setUp(self):
        self.client = MockSessionClient()
        # Mock session variables as they would be set after user login
        self.client.login(compid=1, finyear=1, username='testuser')

        # Setup test data for views
        unit_pcs = UnitMaster.objects.create(id=1, symbol='Pcs')
        item1 = ItemMaster.objects.create(id=1, item_code='ITEM1_ROOT', uom_basic=unit_pcs, file_name='img1.png', file_data=b'img_data_1')
        item2 = ItemMaster.objects.create(id=2, item_code='ITEM2_CHILD', uom_basic=unit_pcs)
        item3 = ItemMaster.objects.create(id=3, item_code='ITEM3_LEAF', uom_basic=unit_pcs)

        # Create TPL entries mimicking the tree structure
        TPLMaster.objects.create(id=10, item=item1, wo_no='WO_TEST_001', parent_id=None, child_id=1, qty=Decimal('10.0'), comp_id=1, fin_year_id=1)
        TPLMaster.objects.create(id=20, item=item2, wo_no='WO_TEST_001', parent_id=1, child_id=2, qty=Decimal('2.0'), comp_id=1, fin_year_id=1)
        TPLMaster.objects.create(id=30, item=item3, wo_no='WO_TEST_001', parent_id=2, child_id=3, qty=Decimal('0.5'), comp_id=1, fin_year_id=1, amd_no='AmdX')
        
        self.wo_no = 'WO_TEST_001'

    def test_main_page_get(self):
        """Test that the main TPL edit page loads correctly."""
        response = self.client.get(reverse('design_tpl:tplmaster_edit', kwargs={'wo_no': self.wo_no}))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_tpl/tpl_master/list.html')
        self.assertContains(response, self.wo_no)
        self.assertContains(response, 'Expand Tree')
        self.assertContains(response, 'Cancel')
        self.assertContains(response, 'id="tplMasterTableContainer"') # Check for HTMX container

    def test_main_page_with_message_from_query_string(self):
        """Test message display when 'msg' parameter is in query string."""
        response = self.client.get(reverse('design_tpl:tplmaster_edit', kwargs={'wo_no': self.wo_no}) + '?msg=TestMessageFromQuery')
        self.assertEqual(response.status_code, 200)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'TestMessageFromQuery')
        self.assertContains(response, 'TestMessageFromQuery')
    
    def test_table_partial_view_htmx(self):
        """Test that the HTMX-loaded table partial renders correctly with data."""
        response = self.client.get(reverse('design_tpl:tplmaster_table_partial', kwargs={'wo_no': self.wo_no}), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_tpl/tpl_master/_tpl_master_table.html')
        self.assertContains(response, 'id="tplMasterTable"')
        self.assertContains(response, 'ITEM1_ROOT')
        self.assertContains(response, 'ITEM2_CHILD')
        self.assertContains(response, 'ITEM3_LEAF')
        self.assertContains(response, '10.000') # Check TPL Qty for root
        self.assertContains(response, '20.000') # Check TPL Qty for child
        self.assertContains(response, '10.000') # Check TPL Qty for grandchild

    def test_toggle_tree_expansion_htmx(self):
        """Test the HTMX endpoint for toggling tree expansion state."""
        url = reverse('design_tpl:toggle_tree_expansion', kwargs={'wo_no': self.wo_no})
        
        # Initial state: expand_tree is True (default for sessions)
        self.client.session_data['expand_tree'] = True
        
        # Toggle to false
        response = self.client.post(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response['HX-Trigger'], 'refreshTPLMasterList')
        self.assertFalse(self.client.session_data['expand_tree'])

        # Toggle back to true
        response = self.client.post(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response['HX-Trigger'], 'refreshTPLMasterList')
        self.assertTrue(self.client.session_data['expand_tree'])

    def test_item_master_update_view_get(self):
        """Test GET request for ItemMaster update form modal."""
        item = ItemMaster.objects.get(item_code='ITEM3_LEAF') # A non-assembly item
        response = self.client.get(reverse('design_tpl:edit_item_master', kwargs={'pk': item.pk}), data={'WONo': self.wo_no, 'CId': 3})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_tpl/item_master/form.html')
        self.assertContains(response, 'Edit Item Details')
        self.assertContains(response, item.item_code)

    def test_item_master_update_view_post_success(self):
        """Test successful POST request for ItemMaster update via HTMX."""
        item = ItemMaster.objects.get(item_code='ITEM3_LEAF')
        updated_desc = 'Updated Description for Leaf Item'
        data = {
            'item_code': item.item_code,
            'part_no': item.part_no,
            'manf_desc': updated_desc,
            'uom_basic': item.uom_basic.pk,
        }
        response = self.client.put(
            reverse('design_tpl:edit_item_master', kwargs={'pk': item.pk}) + f'?WONo={self.wo_no}&CId=3',
            data=data,
            content_type='application/x-www-form-urlencoded',
            HTTP_HX_REQUEST='true' # Indicate HTMX request
        )
        self.assertEqual(response.status_code, 204) # HTMX success without content
        self.assertEqual(response['HX-Trigger'], 'refreshTPLMasterList')
        item.refresh_from_db()
        self.assertEqual(item.manf_desc, updated_desc)

    def test_item_master_update_view_post_assembly_item_blocked(self):
        """Test blocking modification of an assembly item."""
        # ITEM1_ROOT (CId=1) is an assembly item because it has ITEM2_CHILD as a child
        item = ItemMaster.objects.get(item_code='ITEM1_ROOT')
        # This is the TPLMaster entry for ITEM1_ROOT, where CId is 1.
        tpl_entry = TPLMaster.objects.get(item=item, wo_no=self.wo_no, child_id=1) 

        self.assertTrue(tpl_entry.is_assembly_item())

        updated_desc = 'Attempt to update assembly item'
        data = {
            'item_code': item.item_code,
            'part_no': item.part_no,
            'manf_desc': updated_desc,
            'uom_basic': item.uom_basic.pk,
        }
        response = self.client.put(
            reverse('design_tpl:edit_item_master', kwargs={'pk': item.pk}) + f'?WONo={self.wo_no}&CId={tpl_entry.child_id}',
            data=data,
            content_type='application/x-www-form-urlencoded',
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 400) # Should return 400 for a server-side error with HTMX re-render
        self.assertEqual(response['HX-Retarget'], '#modalContent')
        self.assertEqual(response['HX-Reswap'], 'innerHTML')
        self.assertContains(response, "Assembly item cannot be modified.")
        item.refresh_from_db()
        self.assertNotEqual(item.manf_desc, updated_desc) # Data should not be updated

    def test_download_file_view_image(self):
        """Test downloading an image file."""
        item_with_image = ItemMaster.objects.get(item_code='ITEM1_ROOT')
        response = self.client.get(reverse('design_tpl:download_file', kwargs={'item_id': item_with_image.pk, 'file_type': 'image'}) + f'?WONo={self.wo_no}')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'image/jpeg')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="img1.png"')
        self.assertEqual(response.content, b'img_data_1')

    def test_download_file_view_no_file(self):
        """Test downloading a file that does not exist."""
        item_without_file = ItemMaster.objects.get(item_code='ITEM2_CHILD') # No image data
        response = self.client.get(reverse('design_tpl:download_file', kwargs={'item_id': item_without_file.pk, 'file_type': 'image'}) + f'?WONo={self.wo_no}')
        self.assertEqual(response.status_code, 302) # Redirects back due to error
        self.assertRedirects(response, reverse('design_tpl:tplmaster_edit', kwargs={'wo_no': self.wo_no}))
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), "File not found or empty.")

    def test_upload_file_view_get(self):
        """Test GET request for file upload form modal."""
        item_no_file = ItemMaster.objects.get(item_code='ITEM2_CHILD') # Item without files
        response = self.client.get(reverse('design_tpl:upload_file', kwargs={'pk': item_no_file.pk}) + f'?WONo={self.wo_no}&img=0') # img=0 for image
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_tpl/item_master/upload_form.html')
        self.assertContains(response, 'Upload Drawing/Image')

    def test_upload_file_view_post_image_success(self):
        """Test successful image file upload via HTMX."""
        item_no_file = ItemMaster.objects.get(item_code='ITEM2_CHILD')
        upload_url = reverse('design_tpl:upload_file', kwargs={'pk': item_no_file.pk}) + f'?WONo={self.wo_no}&img=0'
        
        test_image = SimpleUploadedFile("new_drawing.png", b"file_content_for_drawing", content_type="image/png")
        
        response = self.client.post(
            upload_url,
            {'file': test_image},
            HTTP_HX_REQUEST='true',
            format='multipart' # Essential for file uploads in Django test client
        )
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response['HX-Trigger'], 'refreshTPLMasterList')
        
        item_no_file.refresh_from_db() # Reload item to check updated fields
        self.assertEqual(item_no_file.file_name, "new_drawing.png")
        self.assertEqual(item_no_file.file_data, b"file_content_for_drawing")
        self.assertEqual(item_no_file.content_type, "image/png")

    def test_upload_file_view_post_spec_success(self):
        """Test successful specification file upload via HTMX."""
        item_no_file = ItemMaster.objects.get(item_code='ITEM2_CHILD')
        upload_url = reverse('design_tpl:upload_file', kwargs={'pk': item_no_file.pk}) + f'?WONo={self.wo_no}&img=1'
        
        test_spec = SimpleUploadedFile("new_spec.pdf", b"file_content_for_spec", content_type="application/pdf")
        
        response = self.client.post(
            upload_url,
            {'file': test_spec},
            HTTP_HX_REQUEST='true',
            format='multipart'
        )
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response['HX-Trigger'], 'refreshTPLMasterList')
        
        item_no_file.refresh_from_db()
        self.assertEqual(item_no_file.att_name, "new_spec.pdf")
        self.assertEqual(item_no_file.att_data, b"file_content_for_spec")
        self.assertEqual(item_no_file.att_content_type, "application/pdf")

    def test_tpl_master_amd_view_get(self):
        """Test GET request for AmdNo update form modal."""
        tpl_item = TPLMaster.objects.get(child_id=3) # Item with existing AmdNo
        response = self.client.get(reverse('design_tpl:amd_tpl_item', kwargs={'pk': tpl_item.pk}) + f'?WONo={self.wo_no}&ItemId={tpl_item.item.pk}')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_tpl/tpl_master/amd_form.html')
        self.assertContains(response, 'Update Amd No')
        self.assertContains(response, tpl_item.amd_no)

    def test_tpl_master_amd_view_post_success(self):
        """Test successful POST request for AmdNo update via HTMX."""
        tpl_item = TPLMaster.objects.get(child_id=3)
        new_amd_no = "AMD001_NEW"
        data = {'amd_no': new_amd_no}
        response = self.client.post(
            reverse('design_tpl:amd_tpl_item', kwargs={'pk': tpl_item.pk}) + f'?WONo={self.wo_no}&ItemId={tpl_item.item.pk}',
            data=data,
            HTTP_HX_REQUEST='true',
            content_type='application/x-www-form-urlencoded'
        )
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response['HX-Trigger'], 'refreshTPLMasterList')
        tpl_item.refresh_from_db()
        self.assertEqual(tpl_item.amd_no, new_amd_no)

    def test_tpl_master_list_redirect_view(self):
        """Test the generic redirect view for cancel operations."""
        response = self.client.get(reverse('design_tpl:tpl_work_order_grid_update'))
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('design_tpl:tpl_work_order_list'))
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), "Navigation to work order list: Work Order Number context lost or not provided.")

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

The provided Django templates and views already integrate HTMX for:
*   **Dynamic Table Loading:** The `tplMasterTableContainer` loads `_tpl_master_table.html` via `hx-get` on page `load` and `refreshTPLMasterList` events.
*   **Table Refresh:** After any CRUD operation (item update, file upload, AmdNo update) that occurs in a modal, the view returns an `HX-Trigger: refreshTPLMasterList` header, causing the main table to reload without a full page refresh.
*   **Modal Interactions:** Buttons like "Edit", "Upload", "Amd" use `hx-get` to fetch their respective forms into the `#modalContent` div, and `hx-target` and `hx-trigger` with `_="on click add .is-active to #modal"` to manage modal visibility with Alpine.js.
*   **Form Submissions:** Forms inside modals use `hx-post`/`hx-put` with `hx-swap="none"` to process data without replacing the form, allowing the `HX-Trigger` from the view to handle the refresh and modal closing.
*   **Expand/Collapse:** The checkbox `expandTreeCheckbox` uses `hx-post` to `toggle_tree_expansion` which updates session state and triggers table refresh. Alpine.js `x-model` handles the client-side state of the checkbox.
*   **Loading Indicators:** `hx-indicator` attributes are used on various elements to show a spinner during HTMX requests, improving user feedback.

Alpine.js is used for simple UI state management, primarily for controlling the modal's visibility. The `_` (hyperscript) syntax on the modal div handles opening and closing the modal.

DataTables is initialized on the `_tpl_master_table.html` partial whenever it's loaded, ensuring full client-side search, sort, and pagination capabilities. The `pageLength` is set to `17` as per the original RadTreeList `PageSize`.

All interactions are designed to work seamlessly without full page reloads, providing a modern, responsive user experience.

### Final Notes

*   **Placeholders:** All `[PLACEHOLDERS]` have been replaced with concrete values based on the ASP.NET analysis.
*   **DRY Principle:** Templates extend a `core/base.html`, and partial templates are used for reusable components (e.g., table content, forms).
*   **Separation of Concerns:** Business logic for data retrieval and calculation (`get_tree_data`, `calculate_cumulative_qty`, `is_assembly_item`) is strictly within the `TPLMaster` model. Views remain thin, handling HTTP requests, calling model methods, and rendering templates.
*   **Testing:** Comprehensive tests are provided for all models and views, ensuring high code coverage and functional correctness after migration.
*   **Session Management:** The `CompId` and `FinYearId` are assumed to be available in the Django session, similar to ASP.NET. In a real system, robust authentication and user profile management would provide these values.
*   **`tblDG_TPLItem_Temp`:** The temporary table deletion logic from ASP.NET is not directly replicated here, assuming its purpose is specific to other flows or can be managed by a separate background cleanup task in Django if persistent. This modernization focuses on the primary display and interaction flow of the `TPL_Design_WO_TreeView_Edit` page.
*   **Real-world Considerations:** For a full production system, consider adding:
    *   Authentication and Authorization middleware.
    *   Robust logging and error handling.
    *   Deployment configurations (e.g., Gunicorn/Nginx).
    *   More advanced DataTables tree view plugins if exact RadTreeList hierarchy presentation is critical.