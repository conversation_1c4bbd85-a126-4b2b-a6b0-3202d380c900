## ASP.NET to Django Conversion Script: Comprehensive Modernization Plan

This document outlines a detailed strategy for migrating your ASP.NET application, specifically focusing on the TPL Design Work Order Tree View, to a modern Django-based solution. Our approach prioritizes AI-assisted automation, emphasizing a clean, efficient, and maintainable codebase using Django 5.0+ best practices, HTMX, Alpine.js, and DataTables.

The goal is to provide a clear, actionable plan in plain English, suitable for both technical and non-technical stakeholders, ensuring a smooth transition with maximum business value.

### Business Value & Outcomes:

Migrating to Django will deliver several key benefits for your organization:

1.  **Enhanced Performance & Scalability:** Django's architecture and Python's efficiency will provide a faster, more responsive application capable of handling increased user loads.
2.  **Improved Maintainability & Development Speed:** Python's readability and Django's structured approach significantly reduce development time and simplify ongoing maintenance.
3.  **Modern User Experience:** Leveraging HTMX and Alpine.js creates highly interactive interfaces without the complexity of traditional JavaScript frameworks, leading to a smoother user experience.
4.  **Reduced Technical Debt:** Moving away from legacy ASP.NET components eliminates dependencies on outdated technologies, making the system more agile and future-proof.
5.  **Cost Efficiency:** Open-source technologies like Django, HTMX, and Alpine.js reduce licensing costs associated with proprietary frameworks and tools.
6.  **Better Data Insights:** A well-structured Django ORM integration will facilitate easier data access and reporting.

---

### Conversion Steps:

#### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the ASP.NET code.

**Instructions:**
The ASP.NET code interacts primarily with `tblDG_TPL_Master`, `tblDG_Item_Master`, and `Unit_Master`. It also uses session data (`CompId`, `FinYearId`, `username`) and query string parameters (`WONo`, `Msg`).

*   **`tblDG_TPL_Master` (Target Django Model: `TPLMaster`)**
    *   `CId` (int, primary key) -> `id`
    *   `ItemId` (int) -> `item` (Foreign Key to `tblDG_Item_Master`)
    *   `WONo` (string) -> `work_order_no`
    *   `PId` (int, nullable) -> `parent` (Self-referencing Foreign Key to `TPLMaster`)
    *   `Quantity` (decimal) -> `unit_quantity`
    *   `CompId` (int) -> `company` (Foreign Key to `Company_Master`)
    *   `FinYearId` (int) -> `financial_year` (Foreign Key to `Financial_Year_Master`)

*   **`tblDG_Item_Master` (Target Django Model: `ItemMaster`)**
    *   `Id` (int, primary key) -> `id`
    *   `ItemCode` (string, nullable) -> `item_code`
    *   `PartNo` (string, nullable) -> `part_no`
    *   `ManfDesc` (string, nullable) -> `description`
    *   `UOMBasic` (int, nullable) -> `uom` (Foreign Key to `Unit_Master`)
    *   `FileName` (string, nullable) -> `drawing_filename`
    *   `AttName` (string, nullable) -> `spec_sheet_filename`
    *   `CompId` (int) -> `company` (Foreign Key to `Company_Master`)
    *   `FinYearId` (int) -> `financial_year` (Foreign Key to `Financial_Year_Master`)

*   **`Unit_Master` (Target Django Model: `Unit`)**
    *   `Id` (int, primary key) -> `id`
    *   `Symbol` (string) -> `symbol`

*   **`Company_Master` (Target Django Model: `Company`)**
    *   `Id` (int, primary key) -> `id`
    *   `Name` (string) -> `name` (Inferred for a sensible `Company` model)

*   **`Financial_Year_Master` (Target Django Model: `FinancialYear`)**
    *   `Id` (int, primary key) -> `id`
    *   `Year` (int) -> `year` (Inferred for a sensible `FinancialYear` model)

#### Step 2: Identify Backend Functionality

**Task:** Determine the operations and business logic from the ASP.NET code.

**Instructions:**

*   **Read (Primary Focus):** The application's main purpose is to display a hierarchical "Bill of Materials" (BOM) or "Tree List" for a given Work Order.
    *   Data is fetched from `tblDG_TPL_Master` and joined with `tblDG_Item_Master` and `Unit_Master`.
    *   Filtering occurs based on `WONo` (Work Order Number), `CompId` (Company ID), and `FinYearId` (Financial Year ID).
    *   A critical piece of business logic is the `fun.BOMTreeQty` function, which recursively calculates the "BOM Qty" for each item based on its position in the assembly tree and the unit quantities of its ancestors.
    *   The "Drw/Image" and "Spec. Sheet" columns dynamically display "View" or "Upload" based on whether corresponding `FileName` or `AttName` exists for the item.

*   **Actions/Interactions:**
    *   **Expand/Collapse Tree:** The `CheckBox1` toggles between expanding and collapsing all items in the `RadTreeList`. This affects how the tree structure is displayed.
    *   **Item Selection:** Clicking on an item (via the "Select" button or a row selection) redirects the user to a detailed view (`WoItems.aspx`) for that specific item, passing `WONo`, `ItemId`, `PId`, and `CId`.
    *   **"Copy From" Button (`btnCopy`):** Redirects to another page (`TPL_Design_Root_Assembly_Copy_WO.aspx`) for copying assembly data.
    *   **"Cancel" Button (`Button1`):** Redirects back to a grid view of Work Orders (`TPL_Design_WO_Grid.aspx`).
    *   **File Download/Upload:** "View" links trigger file downloads, while "Upload" links imply a form for uploading new files.

#### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, translating them into modern web components.

**Instructions:**

*   **Page Layout (`MasterPage.master`):** Will be replaced by Django's template inheritance using `core/base.html`.
*   **Work Order Display (`Label2`):** A simple `<span>` element in the template.
*   **Message Display (`lblmsg`):** A `<span>` element, dynamically updated by HTMX if messages are exchanged.
*   **Expand Tree Checkbox (`CheckBox1`):** An `<input type="checkbox">` that uses HTMX to re-fetch and render the tree table content based on its checked state. Alpine.js can manage its local UI state if needed, but HTMX will drive the server interaction.
*   **Action Buttons (`btnCopy`, `Button1`):** Standard `<button>` elements utilizing HTMX for `hx-get` actions leading to redirects, preventing full page reloads until the final destination.
*   **Telerik RadTreeList (`RadTreeList1`):** This is the core data display.
    *   It will be replaced by a combination of:
        *   A `<table>` element initialized with **DataTables** for client-side search, sorting, and pagination of the *flat* list of tree nodes.
        *   Django's recursive template includes (`_tpl_tree_node.html`) to visually represent the parent-child hierarchy with indentation.
        *   Alpine.js for local client-side expand/collapse toggles within the recursively rendered rows (though the initial "Expand Tree" checkbox controls the *entire* table's initial state via HTMX).
    *   **Columns:** Each column (`Item Code`, `Description`, `UOM`, `Unit Qty`, `BOM Qty`, `Drw/Image`, `Spec. Sheet`, `Actions`) will correspond to `<th>` and `<td>` elements.
    *   **Action Buttons within rows:** "Select", "View" (for files), "Upload" (for files) will be `buttons` or `<a>` tags with HTMX attributes to trigger appropriate endpoints.
    *   **Pagination (`PagerTemplate`):** DataTables will handle this natively.

#### Step 4: Generate Django Code

We will create a new Django app, e.g., `design_tpl`, to house these components.

<br>

**1. `design_tpl/models.py`**

This file defines the Django models, mapping them to your existing database tables. Note the `managed = False` and `db_table` settings to ensure Django doesn't try to create or modify these tables. Business logic like BOM calculation is placed as model methods, adhering to the "fat model" principle.

```python
from django.db import models
from django.db.models import F

# Placeholder models for foreign keys.
# These assume existing tables in the database with their respective IDs.
# Ensure these match your actual database schema precisely.
class Company(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=255) # Assuming a 'Name' column for display
    class Meta:
        managed = False
        db_table = 'Company_Master' # Replace with your actual company master table name
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name

class FinancialYear(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    year = models.IntegerField(db_column='Year') # Assuming a 'Year' column for display
    class Meta:
        managed = False
        db_table = 'Financial_Year_Master' # Replace with your actual financial year table name
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return str(self.year)

class Unit(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    class Meta:
        managed = False
        db_table = 'Unit_Master' # Replace with your actual unit master table name
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=255, blank=True, null=True)
    part_no = models.CharField(db_column='PartNo', max_length=255, blank=True, null=True)
    description = models.CharField(db_column='ManfDesc', max_length=1000, blank=True, null=True)
    uom = models.ForeignKey(Unit, on_delete=models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)
    drawing_filename = models.CharField(db_column='FileName', max_length=255, blank=True, null=True)
    spec_sheet_filename = models.CharField(db_column='AttName', max_length=255, blank=True, null=True)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', blank=True, null=True)
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master' # Replace with your actual item master table name
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.item_code or self.part_no or f"Item {self.id}"

    @property
    def item_display_code(self):
        """Returns ItemCode if available, else PartNo, mimicking ASP.NET logic."""
        return self.item_code if self.item_code else self.part_no

    @property
    def drawing_status(self):
        """Returns 'View' if drawing file exists, else 'Upload'."""
        return "View" if self.drawing_filename else "Upload"

    @property
    def spec_sheet_status(self):
        """Returns 'View' if spec sheet file exists, else 'Upload'."""
        return "View" if self.spec_sheet_filename else "Upload"

class TPLMaster(models.Model):
    # CId in ASP.NET code corresponds to the primary key for the tree list node.
    # ItemId corresponds to ItemMaster.Id.
    id = models.IntegerField(db_column='CId', primary_key=True)
    item = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId', related_name='tpl_entries')
    work_order_no = models.CharField(db_column='WONo', max_length=255)
    # PId in ASP.NET code corresponds to the parent's CId (i.e., parent's ID).
    parent = models.ForeignKey('self', on_delete=models.DO_NOTHING, db_column='PId', related_name='children', null=True, blank=True)
    unit_quantity = models.DecimalField(db_column='Quantity', max_digits=18, decimal_places=3)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId')
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblDG_TPL_Master' # Replace with your actual TPL master table name
        verbose_name = 'TPL Entry'
        verbose_name_plural = 'TPL Entries'
        # Ordering by parent_id then id helps in building the tree structure
        ordering = ['parent_id', 'id'] 

    def __str__(self):
        return f"TPL {self.id} for WO {self.work_order_no} - Item {self.item.item_display_code}"

    def get_bom_quantity(self):
        """
        Calculates the Bill of Material (BOM) quantity for this item.
        This method mimics the recursive logic found in fun.BOMTreeQty,
        multiplying unit quantities up the parent chain to the root.
        """
        current_node = self
        product_of_quantities = 1.0 # Use float for intermediate calculation precision

        # Traverse up the tree to the root, collecting quantities of parent links
        # This assumes the 'unit_quantity' field represents the quantity
        # of the child item required per unit of the parent item.
        path_quantities = []
        node = self
        while node:
            path_quantities.append(float(node.unit_quantity))
            node = node.parent # Move to the parent node

        # Multiply all quantities in the path. The current node's quantity is the last one.
        # The path is collected from child to root, so multiply from root to child.
        # This requires reversing the path_quantities list.
        
        # Original C# was `h = h * g[j]` where g is a list from BOMTreeQty.
        # If BOMTreeQty returns quantities from root to current node, then direct multiplication works.
        # Assuming `path_quantities` collected from self up to root, multiplying them all is correct.
        
        total_bom_qty = 1.0
        for qty in reversed(path_quantities): # Start multiplication from the root's quantity contribution
            total_bom_qty *= qty
            
        return total_bom_qty

    @property
    def bom_quantity_display(self):
        """Returns the BOM quantity formatted to three decimal places."""
        return f"{self.get_bom_quantity():.3f}"

    @property
    def item_code_display(self):
        """Returns the display item code from the related ItemMaster."""
        return self.item.item_display_code if self.item else ""

    @property
    def description_display(self):
        """Returns the description from the related ItemMaster."""
        return self.item.description if self.item else ""

    @property
    def uom_display(self):
        """Returns the Unit of Measure symbol from the related ItemMaster's UOM."""
        return self.item.uom.symbol if self.item and self.item.uom else ""

```

<br>

**2. `design_tpl/forms.py`**

The original ASP.NET page was primarily for display. If there were forms for adding/editing `TPLMaster` entries, they would be defined here. For now, we provide a placeholder form for completeness, but it is not directly used by the listed views for this page.

```python
from django import forms
from .models import TPLMaster, ItemMaster, Unit, Company, FinancialYear

class TPLMasterForm(forms.ModelForm):
    # This form is a conceptual placeholder if CRUD operations on TPLMaster were required.
    # The current ASP.NET page primarily displays data.
    class Meta:
        model = TPLMaster
        fields = ['work_order_no', 'parent', 'item', 'unit_quantity']
        widgets = {
            'work_order_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'parent': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'item': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'unit_quantity': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.001'}),
        }
        labels = {
            'work_order_no': 'Work Order No.',
            'parent': 'Parent Assembly',
            'item': 'Item',
            'unit_quantity': 'Unit Quantity',
        }

    # Add custom validation methods here if needed
```

<br>

**3. `design_tpl/views.py`**

This file contains the Django Class-Based Views (CBVs) to handle requests, fetch data, and render templates. Views are kept "thin" by delegating business logic to models.

```python
from django.views.generic import TemplateView, ListView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import redirect
from .models import TPLMaster, ItemMaster, Company, FinancialYear, Unit # Ensure all related models are imported

# Helper function to mock session data retrieval.
# In a real application, replace this with actual session management (e.g., request.session, request.user).
def get_session_data(request):
    return {
        'comp_id': request.session.get('compid', 1), # Default Company ID
        'fin_year_id': request.session.get('finyear', 2023), # Default Financial Year ID
        'session_id': request.session.get('username', 'anonymous'), # Default username
    }

# Helper function to build the tree structure from a flat queryset
# This is data preparation for the template, keeping the view thin.
def build_tree_from_queryset(queryset):
    # Dictionary to quickly access nodes by their CId (which is 'id' in our TPLMaster model)
    nodes = {node.id: node for node in queryset}
    tree = [] # List to hold root nodes

    # First pass: Initialize children_list and level for each node
    for node_id, node in nodes.items():
        node.children_list = []
        node.level = 0 # Default level, will be updated during tree building

    # Second pass: Build the tree structure and assign children to their parents
    for node_id, node in nodes.items():
        if node.parent_id is None: # It's a root node
            tree.append(node)
        elif node.parent_id in nodes: # It has a parent within the current queryset
            parent_node = nodes[node.parent_id]
            parent_node.children_list.append(node)
    
    # Third pass: Set levels and sort children recursively
    def set_levels_and_sort(nodes_list, level):
        nodes_list.sort(key=lambda x: x.id) # Sort children by their own ID for consistent order
        for node in nodes_list:
            node.level = level
            set_levels_and_sort(node.children_list, level + 1)

    set_levels_and_sort(tree, 0) # Start with roots at level 0
    
    return tree

class TPLTreeView(TemplateView):
    """
    Renders the main TPL Work Order Tree View page.
    This view serves the initial HTML structure.
    """
    template_name = 'design_tpl/tpl_tree/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Populate initial context from URL parameters, mimicking ASP.NET QueryString.
        context['work_order_no'] = self.request.GET.get('WONo', 'N/A')
        context['message'] = self.request.GET.get('Msg', '')
        # Set initial state of the 'Expand Tree' checkbox to checked, as per ASP.NET default.
        context['expand_all_checked'] = True 
        return context

class TPLTreeTablePartialView(ListView):
    """
    Renders the partial HTML for the TPL tree table, designed to be loaded via HTMX.
    This view fetches the relevant TPL entries and structures them for tree display.
    """
    model = TPLMaster
    template_name = 'design_tpl/tpl_tree/_tpl_tree_table.html'
    context_object_name = 'tpl_entries' # Renamed for clarity, though `root_tpl_entries` is used in template

    def get_queryset(self):
        session_data = get_session_data(self.request)
        work_order_no = self.request.GET.get('WONo')
        # The 'expand_all' parameter dictates initial rendering state.
        # For a truly hierarchical tree, the queryset always fetches all relevant nodes,
        # and the template handles the visual expansion/collapse.
        # The checkbox in the front-end mostly controls the initial visible state via Alpine.js.
        
        if not work_order_no:
            return TPLMaster.objects.none() # Return empty if no work order specified

        # Fetch TPL entries filtered by work order, company, and financial year.
        # Prefetch/select_related is used for efficient access to related item and unit data.
        queryset = TPLMaster.objects.select_related('item', 'item__uom', 'company', 'financial_year', 'parent').filter(
            work_order_no=work_order_no,
            company_id=session_data['comp_id'],
            # The ASP.NET code uses FinYearId <= current FinYearId.
            financial_year_id__lte=session_data['fin_year_id']
        )
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Build the hierarchical tree structure from the fetched flat queryset.
        context['root_tpl_entries'] = build_tree_from_queryset(self.get_queryset())
        
        # Pass the initial expand_all state to the template for Alpine.js to use
        context['initial_expand_all'] = self.request.GET.get('expand_all', 'true').lower() == 'true'
        return context

# Action views mimicking ASP.NET redirects
class TPLCopyFromView(TemplateView):
    """Handles the 'Copy From' button action, redirecting to a dedicated page."""
    def get(self, request, *args, **kwargs):
        work_order_no_dest = request.GET.get('WONoDest', '')
        messages.info(request, f"Redirecting to copy from WO: {work_order_no_dest}")
        # Redirect to the equivalent Django URL for copying assemblies.
        return redirect(reverse_lazy('design_tpl:copy_from_wo_page', kwargs={'wo_no_dest': work_order_no_dest}))

class TPLCancelView(TemplateView):
    """Handles the 'Cancel' button action, redirecting to the Work Order Grid."""
    def get(self, request, *args, **kwargs):
        messages.info(request, "Redirecting to Work Order Grid.")
        # Redirect to the equivalent Django URL for the WO grid.
        return redirect(reverse_lazy('design_tpl:wo_grid_page'))

class TPLItemDetailRedirectView(TemplateView):
    """
    Handles the 'Select' item action, redirecting to a detailed item view.
    Parameters are passed via URL query string.
    """
    def get(self, request, *args, **kwargs):
        wono = request.GET.get('WONo')
        item_id = request.GET.get('ItemId')
        p_id = request.GET.get('PId') # Parent ID
        c_id = request.GET.get('CId') # Current Node ID (TPLMaster.id)
        
        if wono and item_id and c_id: # PId can be 0 or None for root, so not mandatory for basic redirect
            messages.info(request, f"Redirecting to item details for WO: {wono}, ItemId: {item_id}, CId: {c_id}")
            # Redirect to a hypothetical item details page with necessary parameters.
            return redirect(reverse_lazy('design_tpl:item_details_page', kwargs={
                'wo_no': wono,
                'item_id': item_id,
                'p_id': p_id or 0, # Pass 0 if PId is None, mimicking ASP.NET behavior
                'c_id': c_id
            }))
        messages.error(request, "Missing parameters for item details. Cannot redirect.")
        return redirect(reverse_lazy('design_tpl:tpl_list')) # Fallback to main list if params are missing

class TPLFileDownloadView(TemplateView):
    """Simulates file download for drawings or spec sheets."""
    def get(self, request, item_id, file_type, *args, **kwargs):
        # In a production environment, this would involve retrieving the actual file path
        # from the server and returning a FileResponse.
        try:
            item = ItemMaster.objects.get(id=item_id)
            if file_type == 'drawing' and item.drawing_filename:
                # Actual file serving logic here (e.g., from MEDIA_ROOT)
                messages.success(request, f"Simulating download of drawing: {item.drawing_filename}")
                return HttpResponse(f"Simulating download of {item.drawing_filename}", content_type="text/plain")
            elif file_type == 'spec_sheet' and item.spec_sheet_filename:
                messages.success(request, f"Simulating download of spec sheet: {item.spec_sheet_filename}")
                return HttpResponse(f"Simulating download of {item.spec_sheet_filename}", content_type="text/plain")
            else:
                messages.warning(request, "File not found or specified type is incorrect.")
        except ItemMaster.DoesNotExist:
            messages.error(request, "Item not found.")
        # Redirect back to the previous page if download fails or file not found
        return redirect(request.META.get('HTTP_REFERER', reverse_lazy('design_tpl:tpl_list')))

class TPLFileUploadView(TemplateView):
    """
    Renders a placeholder for a file upload form.
    This would typically be a FormView with a FileField.
    """
    template_name = 'design_tpl/tpl_tree/_file_upload_form.html' # A simple form partial

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['item_id'] = self.kwargs.get('item_id')
        context['file_type'] = self.kwargs.get('file_type')
        # In a real app, instantiate a form here
        return context

    def post(self, request, item_id, file_type, *args, **kwargs):
        # Handle file upload logic here
        messages.success(request, f"Simulating file upload for Item {item_id}, type {file_type}. File received!")
        # On success, trigger HTMX to close modal and refresh list
        return HttpResponse(
            status=204, # No content response for HTMX
            headers={
                'HX-Trigger': 'refreshTPLTreeTable, closeModal' # Custom trigger to refresh table and close modal
            }
        )

```

<br>

**4. `design_tpl/templates/design_tpl/tpl_tree/list.html`**

This is the main page template that extends your `core/base.html`. It sets up the main layout and uses HTMX to load the dynamic table content.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">TPL Design - Work Order Tree View</h2>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <div class="flex items-center justify-between mb-4 flex-wrap gap-4">
            <div class="flex items-center space-x-4">
                <span class="font-bold text-lg text-gray-700">Wo No: <span id="wo-no-label" class="text-blue-600">{{ work_order_no }}</span></span>
                <span id="lblmsg" class="text-red-600 font-medium">{{ message }}</span>
            </div>
            <div class="flex items-center space-x-4">
                <input type="checkbox" id="expandTreeCheckbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                       {% if expand_all_checked %}checked{% endif %}
                       hx-get="{% url 'design_tpl:tpl_table_partial' %}"
                       hx-target="#tplTreeTableContainer"
                       hx-indicator="#loadingIndicator"
                       hx-swap="innerHTML"
                       hx-vals='{"WONo": "{{ work_order_no }}", "expand_all": this.checked}'
                >
                <label for="expandTreeCheckbox" class="font-bold text-gray-700 select-none">Expand Tree</label>
                <button
                    class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                    hx-get="{% url 'design_tpl:copy_from_wo' %}?WONoDest={{ work_order_no }}"
                    hx-target="body" hx-swap="none">
                    Copy From
                </button>
                <button
                    class="bg-gray-400 hover:bg-gray-500 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                    hx-get="{% url 'design_tpl:cancel_action' %}"
                    hx-target="body" hx-swap="none">
                    Cancel
                </button>
            </div>
        </div>

        {# Container for the TPL Tree List table, loaded dynamically via HTMX #}
        <div id="tplTreeTableContainer"
             hx-trigger="load, refreshTPLTreeTable from:body" {# Loads on page load, and refreshes on custom event #}
             hx-get="{% url 'design_tpl:tpl_table_partial' %}?WONo={{ work_order_no }}&expand_all={{ expand_all_checked|lower }}"
             hx-target="#tplTreeTableContainer"
             hx-indicator="#loadingIndicator"
             hx-swap="innerHTML">
            <!-- Initial loading message and indicator -->
            <div id="loadingIndicator" class="text-center py-8" hx-indicator>
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading TPL data...</p>
            </div>
        </div>
    </div>
</div>

{# Modal for form submission (e.g., file upload) #}
<div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden"
     _="on click if event.target.id == 'modal' remove .is-active from me"
     x-data="{ showModal: false }" @close-modal.window="showModal = false" :class="{ 'is-active': showModal }">
    <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4"
         _="on htmx:afterOnLoad add .is-active to #modal">
        <!-- Content will be loaded here via HTMX -->
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component for modal management, if needed for complex state
        // For basic show/hide driven by HTMX/_hyperscript, the `on click` handlers are sufficient.
        // The `closeModal` event from HTMX trigger can close it.
        document.body.addEventListener('closeModal', function() {
            document.getElementById('modal').classList.remove('is-active');
        });
    });
</script>
{% endblock %}
```

<br>

**5. `design_tpl/templates/design_tpl/tpl_tree/_tpl_tree_table.html`**

This partial template displays the TPL tree as a table. It uses a recursive include (`_tpl_tree_node.html`) to build the hierarchical structure. DataTables is then applied to this overall table.

```html
{# This partial template is loaded via HTMX into the main list.html #}
<div class="overflow-x-auto">
    <table id="tplTreeListTable" class="min-w-full bg-white border border-gray-200 divide-y divide-gray-200">
        <thead>
            <tr>
                {# First column for tree expand/collapse indicators and indentation #}
                <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-10"></th> 
                <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[150px]">Item Code</th>
                <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[340px]">Description</th>
                <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-[60px]">UOM</th>
                <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-[100px]">Unit Qty</th>
                <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-[100px]">BOM Qty</th>
                <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-[80px]">Drw/Image</th>
                <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-[80px]">Spec. Sheet</th>
                <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-[80px]">Actions</th>
            </tr>
        </thead>
        <tbody x-data="{ initialExpandAll: {{ initial_expand_all|lower }} }">
            {# Include the recursive template for rendering tree nodes #}
            {% include 'design_tpl/tpl_tree/_tpl_tree_node.html' with entries=root_tpl_entries %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        // Destroy existing DataTable instance before re-initializing to prevent errors with HTMX
        if ($.fn.DataTable.isDataTable('#tplTreeListTable')) {
            $('#tplTreeListTable').DataTable().destroy();
        }

        // Initialize DataTable on the outermost table.
        // Note: DataTables' native tree-like functionality (like row grouping or child rows)
        // typically requires more custom JavaScript or specific plugins.
        // This setup applies DataTables for general search/sort/pagination on the rendered rows.
        // The actual expand/collapse of tree nodes is handled by Alpine.js and recursive template.
        $('#tplTreeListTable').DataTable({
            "pageLength": 20, // Matches PageSize="20" from ASP.NET
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "responsive": true,
            "pagingType": "full_numbers", // Mimics ASP.NET pager style
            "columnDefs": [
                { "orderable": false, "targets": [0, 6, 7, 8] }, // Disable sorting for action columns
                { "searchable": false, "targets": [0, 6, 7, 8] } // Disable searching for action columns
            ]
        });

        // If the initial_expand_all is true, expand all nodes using Alpine.js's state
        // This leverages the `x-init` in the `<tbody>` of _tpl_tree_node.html
        document.querySelectorAll('td[x-data="{ expanded: true }"]').forEach(el => {
            const alpineComponent = el.__alpine.getComponent(el);
            if (alpineComponent && !alpineComponent.expanded && el.closest('tbody').dataset.initialExpandAll === 'true') {
                alpineComponent.expanded = true;
            }
        });
    });
</script>
```

<br>

**6. `design_tpl/templates/design_tpl/tpl_tree/_tpl_tree_node.html`**

This is the recursive template used to render each node and its children in the TPL tree. Alpine.js is used to manage the local `expanded` state for each node.

```html
{# This template is included recursively to render individual tree nodes and their children. #}

{% for entry in entries %}
    <tr class="hover:bg-gray-50 group">
        {# Indentation and expand/collapse toggle for tree structure #}
        <td class="py-2 px-4 border-b border-gray-200 relative" style="padding-left: {{ entry.level|add:1 }}em;">
            {% if entry.children_list %}
                <span class="absolute top-1/2 left-0 -translate-y-1/2 inline-block text-gray-500 transform transition-transform duration-200 cursor-pointer"
                      x-data="{ expanded: $el.closest('tbody').dataset.initialExpandAll === 'true' }"
                      @click="expanded = !expanded; $nextTick(() => { 
                          let nextRow = $el.closest('tr').nextElementSibling;
                          if (nextRow && nextRow.classList.contains('child-row')) {
                              nextRow.classList.toggle('hidden', !expanded);
                          }
                      })">
                    <i class="fas fa-caret-right" :class="{ 'rotate-90': expanded }"></i>
                </span>
            {% else %}
                <span class="inline-block w-4"></span> {# Placeholder for alignment if no children #}
            {% endif %}
        </td>
        <td class="py-2 px-4 border-b border-gray-200 text-sm font-medium text-gray-900">{{ entry.item_code_display }}</td>
        <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ entry.description_display }}</td>
        <td class="py-2 px-4 border-b border-gray-200 text-center text-sm text-gray-700">{{ entry.uom_display }}</td>
        <td class="py-2 px-4 border-b border-gray-200 text-right text-sm text-gray-700">{{ entry.unit_quantity|floatformat:"3" }}</td>
        <td class="py-2 px-4 border-b border-gray-200 text-right text-sm text-gray-700">{{ entry.bom_quantity_display }}</td>
        <td class="py-2 px-4 border-b border-gray-200 text-center">
            {% if entry.item.drawing_filename %}
            <button class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-1 px-2 rounded-md text-xs transition duration-150 ease-in-out"
                    hx-get="{% url 'design_tpl:download_file' item_id=entry.item.id file_type='drawing' %}"
                    hx-target="body" hx-swap="none">
                View
            </button>
            {% else %}
            <button class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-1 px-2 rounded-md text-xs transition duration-150 ease-in-out"
                    hx-get="{% url 'design_tpl:upload_file' item_id=entry.item.id file_type='drawing' %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                Upload
            </button>
            {% endif %}
        </td>
        <td class="py-2 px-4 border-b border-gray-200 text-center">
            {% if entry.item.spec_sheet_filename %}
            <button class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-1 px-2 rounded-md text-xs transition duration-150 ease-in-out"
                    hx-get="{% url 'design_tpl:download_file' item_id=entry.item.id file_type='spec_sheet' %}"
                    hx-target="body" hx-swap="none">
                View
            </button>
            {% else %}
            <button class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-1 px-2 rounded-md text-xs transition duration-150 ease-in-out"
                    hx-get="{% url 'design_tpl:upload_file' item_id=entry.item.id file_type='spec_sheet' %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                Upload
            </button>
            {% endif %}
        </td>
        <td class="py-2 px-4 border-b border-gray-200 text-center">
            <button class="bg-indigo-500 hover:bg-indigo-600 text-white font-bold py-1 px-2 rounded-md text-xs transition duration-150 ease-in-out"
                    hx-get="{% url 'design_tpl:item_details_redirect' %}?WONo={{ entry.work_order_no }}&ItemId={{ entry.item.id }}&PId={{ entry.parent.id|default:0 }}&CId={{ entry.id }}"
                    hx-target="body" hx-swap="none">
                Select
            </button>
        </td>
    </tr>
    {# Recursively render children in a separate row, hidden by default unless expanded #}
    {% if entry.children_list %}
        <tr class="child-row {% if not initial_expand_all %}hidden{% endif %}" x-show="expanded">
            <td colspan="9" class="p-0 border-b border-gray-200">
                <table class="min-w-full">
                    <tbody>
                        {% include 'design_tpl/tpl_tree/_tpl_tree_node.html' with entries=entry.children_list initial_expand_all=initial_expand_all %}
                    </tbody>
                </table>
            </td>
        </tr>
    {% endif %}
{% endfor %}
```

<br>

**7. `design_tpl/templates/design_tpl/tpl_tree/_file_upload_form.html`**

This is a partial template for the file upload modal, loaded by HTMX.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Upload {{ file_type|title }} for Item ID: {{ item_id }}</h3>
    <form hx-post="{% url 'design_tpl:upload_file' item_id=item_id file_type=file_type %}" hx-swap="none" enctype="multipart/form-data">
        {% csrf_token %}
        
        <div class="space-y-4">
            <div>
                <label for="file_input" class="block text-sm font-medium text-gray-700">Choose File</label>
                <input type="file" id="file_input" name="uploaded_file" class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"/>
            </div>
            {# Add any additional form fields if necessary #}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow-sm transition duration-150 ease-in-out">
                Upload
            </button>
        </div>
    </form>
</div>
```

<br>

**8. `design_tpl/urls.py`**

This file defines the URL patterns for accessing the views.

```python
from django.urls import path
from django.views.generic import TemplateView # For placeholder views
from .views import (
    TPLTreeView, TPLTreeTablePartialView,
    TPLCopyFromView, TPLCancelView, TPLItemDetailRedirectView,
    TPLFileDownloadView, TPLFileUploadView
)

app_name = 'design_tpl' # Define app namespace for URL reversing

urlpatterns = [
    # Main Work Order Tree View page
    path('work_order_tree/', TPLTreeView.as_view(), name='tpl_list'),
    
    # HTMX endpoint for the tree list table partial (DataTables content)
    path('work_order_tree/table/', TPLTreeTablePartialView.as_view(), name='tpl_table_partial'),

    # Action buttons redirects
    path('copy_from_wo/', TPLCopyFromView.as_view(), name='copy_from_wo'),
    path('cancel_action/', TPLCancelView.as_view(), name='cancel_action'),
    
    # Item selection redirect (mimics WoItems.aspx navigation)
    path('item_details_redirect/', TPLItemDetailRedirectView.as_view(), name='item_details_redirect'),

    # File actions (download/upload)
    path('file/download/<int:item_id>/<str:file_type>/', TPLFileDownloadView.as_view(), name='download_file'),
    path('file/upload/<int:item_id>/<str:file_type>/', TPLFileUploadView.as_view(), name='upload_file'),

    # Placeholder URLs for target pages of redirects (these would typically be in other Django apps/modules)
    path('wo_grid_page/', TemplateView.as_view(template_name='design_tpl/placeholder_wo_grid.html'), name='wo_grid_page'),
    path('copy_from_wo_page/<str:wo_no_dest>/', TemplateView.as_view(template_name='design_tpl/placeholder_copy_form.html'), name='copy_from_wo_page'),
    path('item_details_page/<str:wo_no>/<int:item_id>/<int:p_id>/<int:c_id>/', TemplateView.as_view(template_name='design_tpl/placeholder_item_details.html'), name='item_details_page'),
]

# Create simple placeholder templates (e.g., in design_tpl/templates/design_tpl/):
# placeholder_wo_grid.html: <h1>Work Order Grid Page (Placeholder)</h1>
# placeholder_copy_form.html: <h1>Copy From Work Order Page (Placeholder)</h1><p>WO Dest: {{ wo_no_dest }}</p>
# placeholder_item_details.html: <h1>Item Details Page (Placeholder)</h1><p>WO: {{ wo_no }}, ItemId: {{ item_id }}, PId: {{ p_id }}, CId: {{ c_id }}</p>

```

<br>

**9. `design_tpl/tests.py`**

Comprehensive tests ensure the model's business logic is correct and views respond as expected, including HTMX interactions.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from .models import Company, FinancialYear, Unit, ItemMaster, TPLMaster
from django.db import connection # Used for raw SQL to populate managed=False models
from django.contrib.messages import get_messages

class TPLMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Populate managed=False tables directly using raw SQL for testing
        with connection.cursor() as cursor:
            # Insert dummy data for Company, FinancialYear, Unit
            cursor.execute("INSERT INTO Company_Master (Id, Name) VALUES (1, 'Test Company')")
            cursor.execute("INSERT INTO Financial_Year_Master (Id, Year) VALUES (2023, 2023)")
            cursor.execute("INSERT INTO Unit_Master (Id, Symbol) VALUES (101, 'PCS')")
            cursor.execute("INSERT INTO Unit_Master (Id, Symbol) VALUES (102, 'KG')")

            # Insert dummy data for ItemMaster
            cursor.execute("INSERT INTO tblDG_Item_Master (Id, ItemCode, PartNo, ManfDesc, UOMBasic, FileName, AttName, CompId, FinYearId) VALUES (1001, 'ITEM-A', NULL, 'Main Assembly', 101, 'drawing_A.pdf', 'spec_A.doc', 1, 2023)")
            cursor.execute("INSERT INTO tblDG_Item_Master (Id, ItemCode, PartNo, ManfDesc, UOMBasic, FileName, AttName, CompId, FinYearId) VALUES (1002, NULL, 'PART-B', 'Sub-component B', 102, NULL, 'spec_B.doc', 1, 2023)")
            cursor.execute("INSERT INTO tblDG_Item_Master (Id, ItemCode, PartNo, ManfDesc, UOMBasic, FileName, AttName, CompId, FinYearId) VALUES (1003, 'ITEM-C', NULL, 'Part C', 101, NULL, NULL, 1, 2023)")
            cursor.execute("INSERT INTO tblDG_Item_Master (Id, ItemCode, PartNo, ManfDesc, UOMBasic, FileName, AttName, CompId, FinYearId) VALUES (1004, 'ITEM-D', NULL, 'Part D', 101, 'drawing_D.pdf', NULL, 1, 2023)")

            # Insert dummy data for TPLMaster, forming a sample tree structure
            # Tree:
            # 1 (ITEM-A, Qty 1.0) -- WO-001 (Root)
            #   |-- 2 (PART-B, Qty 2.0) -- Child of 1
            #   |     |-- 4 (ITEM-D, Qty 3.0) -- Child of 2
            #   |-- 3 (ITEM-C, Qty 0.5) -- Child of 1
            cursor.execute("INSERT INTO tblDG_TPL_Master (CId, ItemId, WONo, PId, Quantity, CompId, FinYearId) VALUES (1, 1001, 'WO-001', NULL, 1.000, 1, 2023)") # Root: ITEM-A
            cursor.execute("INSERT INTO tblDG_TPL_Master (CId, ItemId, WONo, PId, Quantity, CompId, FinYearId) VALUES (2, 1002, 'WO-001', 1, 2.000, 1, 2023)")  # Child of 1: PART-B
            cursor.execute("INSERT INTO tblDG_TPL_Master (CId, ItemId, WONo, PId, Quantity, CompId, FinYearId) VALUES (3, 1003, 'WO-001', 1, 0.500, 1, 2023)")  # Child of 1: ITEM-C
            cursor.execute("INSERT INTO tblDG_TPL_Master (CId, ItemId, WONo, PId, Quantity, CompId, FinYearId) VALUES (4, 1004, 'WO-001', 2, 3.000, 1, 2023)")  # Child of 2: ITEM-D
            cursor.execute("INSERT INTO tblDG_TPL_Master (CId, ItemId, WONo, PId, Quantity, CompId, FinYearId) VALUES (5, 1001, 'WO-002', NULL, 1.000, 1, 2023)") # Another WO root

    def test_tpl_master_creation_and_relationships(self):
        # Verify basic model creation and relationships
        tpl1 = TPLMaster.objects.get(id=1)
        self.assertEqual(tpl1.work_order_no, 'WO-001')
        self.assertIsNone(tpl1.parent) # Root node
        self.assertEqual(tpl1.item.item_code, 'ITEM-A')
        self.assertAlmostEqual(float(tpl1.unit_quantity), 1.000)

        tpl2 = TPLMaster.objects.get(id=2)
        self.assertEqual(tpl2.parent.id, 1)
        self.assertEqual(tpl2.item.part_no, 'PART-B') # ItemCode is NULL for this one

        tpl4 = TPLMaster.objects.get(id=4)
        self.assertEqual(tpl4.parent.id, 2)
        self.assertEqual(tpl4.item.item_code, 'ITEM-D')

    def test_item_master_properties(self):
        # Test computed properties for ItemMaster
        item_a = ItemMaster.objects.get(id=1001)
        self.assertEqual(item_a.item_display_code, 'ITEM-A')
        self.assertEqual(item_a.drawing_status, 'View')
        self.assertEqual(item_a.spec_sheet_status, 'View')

        item_b = ItemMaster.objects.get(id=1002)
        self.assertEqual(item_b.item_display_code, 'PART-B')
        self.assertEqual(item_b.drawing_status, 'Upload')
        self.assertEqual(item_b.spec_sheet_status, 'View')

        item_c = ItemMaster.objects.get(id=1003)
        self.assertEqual(item_c.item_display_code, 'ITEM-C')
        self.assertEqual(item_c.drawing_status, 'Upload')
        self.assertEqual(item_c.spec_sheet_status, 'Upload')

    def test_bom_quantity_calculation(self):
        # Test the recursive BOM quantity calculation
        tpl1 = TPLMaster.objects.get(id=1) # ITEM-A (Root)
        self.assertAlmostEqual(tpl1.get_bom_quantity(), 1.0) # 1.0 (itself)

        tpl2 = TPLMaster.objects.get(id=2) # PART-B (Child of 1)
        # Path quantities: 1.0 (parent) * 2.0 (self) = 2.0
        self.assertAlmostEqual(tpl2.get_bom_quantity(), 2.0)

        tpl3 = TPLMaster.objects.get(id=3) # ITEM-C (Child of 1)
        # Path quantities: 1.0 (parent) * 0.5 (self) = 0.5
        self.assertAlmostEqual(tpl3.get_bom_quantity(), 0.5)

        tpl4 = TPLMaster.objects.get(id=4) # ITEM-D (Child of 2)
        # Path quantities: 1.0 (grandparent) * 2.0 (parent) * 3.0 (self) = 6.0
        self.assertAlmostEqual(tpl4.get_bom_quantity(), 6.0)

    def test_bom_quantity_display(self):
        # Test formatting of BOM quantity
        tpl4 = TPLMaster.objects.get(id=4)
        self.assertEqual(tpl4.bom_quantity_display, '6.000')

class TPLTreeViewsTest(TestCase):
    client = Client()

    @classmethod
    def setUpTestData(cls):
        # Re-populate database for view tests, including session data
        with connection.cursor() as cursor:
            cursor.execute("INSERT INTO Company_Master (Id, Name) VALUES (1, 'Test Company')")
            cursor.execute("INSERT INTO Financial_Year_Master (Id, Year) VALUES (2023, 2023)")
            cursor.execute("INSERT INTO Unit_Master (Id, Symbol) VALUES (101, 'PCS')")
            cursor.execute("INSERT INTO Unit_Master (Id, Symbol) VALUES (102, 'KG')")

            cursor.execute("INSERT INTO tblDG_Item_Master (Id, ItemCode, PartNo, ManfDesc, UOMBasic, FileName, AttName, CompId, FinYearId) VALUES (1001, 'ITEM-A', NULL, 'Main Assembly', 101, 'drawing_A.pdf', 'spec_A.doc', 1, 2023)")
            cursor.execute("INSERT INTO tblDG_Item_Master (Id, ItemCode, PartNo, ManfDesc, UOMBasic, FileName, AttName, CompId, FinYearId) VALUES (1002, NULL, 'PART-B', 'Sub-component B', 102, NULL, 'spec_B.doc', 1, 2023)")
            cursor.execute("INSERT INTO tblDG_Item_Master (Id, ItemCode, PartNo, ManfDesc, UOMBasic, FileName, AttName, CompId, FinYearId) VALUES (1003, 'ITEM-C', NULL, 'Part C', 101, NULL, NULL, 1, 2023)")
            cursor.execute("INSERT INTO tblDG_Item_Master (Id, ItemCode, PartNo, ManfDesc, UOMBasic, FileName, AttName, CompId, FinYearId) VALUES (1004, 'ITEM-D', NULL, 'Part D', 101, 'drawing_D.pdf', NULL, 1, 2023)")

            cursor.execute("INSERT INTO tblDG_TPL_Master (CId, ItemId, WONo, PId, Quantity, CompId, FinYearId) VALUES (1, 1001, 'WO-001', NULL, 1.000, 1, 2023)")
            cursor.execute("INSERT INTO tblDG_TPL_Master (CId, ItemId, WONo, PId, Quantity, CompId, FinYearId) VALUES (2, 1002, 'WO-001', 1, 2.000, 1, 2023)")
            cursor.execute("INSERT INTO tblDG_TPL_Master (CId, ItemId, WONo, PId, Quantity, CompId, FinYearId) VALUES (3, 1003, 'WO-001', 1, 0.500, 1, 2023)")
            cursor.execute("INSERT INTO tblDG_TPL_Master (CId, ItemId, WONo, PId, Quantity, CompId, FinYearId) VALUES (4, 1004, 'WO-001', 2, 3.000, 1, 2023)")
            cursor.execute("INSERT INTO tblDG_TPL_Master (CId, ItemId, WONo, PId, Quantity, CompId, FinYearId) VALUES (5, 1001, 'WO-002', NULL, 1.000, 1, 2023)")
        
        # Set session data directly on the client to simulate user session
        cls.client.session['compid'] = 1
        cls.client.session['finyear'] = 2023
        cls.client.session['username'] = 'testuser'
        cls.client.session.save() # Crucial to save the session state

    def test_tpl_tree_view_renders_correctly(self):
        # Test the main page load
        response = self.client.get(reverse('design_tpl:tpl_list') + '?WONo=WO-001&Msg=TestMessage')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_tpl/tpl_tree/list.html')
        self.assertContains(response, 'WO-001')
        self.assertContains(response, 'TestMessage')
        self.assertContains(response, 'Expand Tree')
        self.assertContains(response, 'Loading TPL data...') # Checks for HTMX loading message

    def test_tpl_tree_table_partial_view_htmx_load(self):
        # Test that the partial table loads correctly via HTMX
        response = self.client.get(reverse('design_tpl:tpl_table_partial') + '?WONo=WO-001&expand_all=true',
                                   HTTP_HX_REQUEST='true') # Simulate HTMX request
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_tpl/tpl_tree/_tpl_tree_table.html')
        
        # Verify content from fetched TPL entries
        self.assertContains(response, 'ITEM-A')
        self.assertContains(response, 'PART-B')
        self.assertContains(response, 'ITEM-D')
        self.assertContains(response, '6.000') # Check for BOM quantity
        self.assertContains(response, 'View') # Check for file status
        self.assertContains(response, 'Upload') # Check for file status

    def test_tpl_tree_table_partial_view_no_work_order(self):
        # Test fetching partial with no WONo
        response = self.client.get(reverse('design_tpl:tpl_table_partial'),
                                   HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_tpl/tpl_tree/_tpl_tree_table.html')
        self.assertContains(response, 'No TPL entries found for this Work Order.')

    def test_copy_from_wo_view_redirects(self):
        # Test "Copy From" action
        response = self.client.get(reverse('design_tpl:copy_from_wo') + '?WONoDest=WO-001')
        self.assertEqual(response.status_code, 302) # Expect redirect
        self.assertRedirects(response, reverse('design_tpl:copy_from_wo_page', kwargs={'wo_no_dest': 'WO-001'}))
        
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertIn("Redirecting to copy from WO: WO-001", str(messages[0]))

    def test_cancel_view_redirects(self):
        # Test "Cancel" action
        response = self.client.get(reverse('design_tpl:cancel_action'))
        self.assertEqual(response.status_code, 302) # Expect redirect
        self.assertRedirects(response, reverse('design_tpl:wo_grid_page'))
        
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertIn("Redirecting to Work Order Grid.", str(messages[0]))

    def test_item_details_redirect_view_valid_params(self):
        # Test "Select" item action with valid parameters
        params = '?WONo=WO-001&ItemId=1001&PId=0&CId=1' # PId=0 mimics ASP.NET for root
        response = self.client.get(reverse('design_tpl:item_details_redirect') + params)
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('design_tpl:item_details_page', kwargs={
            'wo_no': 'WO-001', 'item_id': 1001, 'p_id': 0, 'c_id': 1
        }))
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertIn("Redirecting to item details for WO: WO-001, ItemId: 1001, CId: 1", str(messages[0]))

    def test_item_details_redirect_view_missing_params(self):
        # Test "Select" item action with missing parameters
        response = self.client.get(reverse('design_tpl:item_details_redirect'))
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('design_tpl:tpl_list'))
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertIn("Missing parameters for item details. Cannot redirect.", str(messages[0]))

    def test_file_download_view_drawing(self):
        # Test downloading an existing drawing
        response = self.client.get(reverse('design_tpl:download_file', args=[1001, 'drawing']))
        self.assertEqual(response.status_code, 200) # Simulating file response
        self.assertContains(response, 'Simulating download of drawing_A.pdf')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertIn("Simulating download of drawing: drawing_A.pdf", str(messages[0]))

    def test_file_download_view_spec_sheet_missing(self):
        # Test downloading a missing spec sheet
        response = self.client.get(reverse('design_tpl:download_file', args=[1003, 'spec_sheet']))
        self.assertEqual(response.status_code, 302) # Redirect back because file is missing
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertIn("File not found or specified type is incorrect.", str(messages[0]))

    def test_file_upload_view_get(self):
        # Test fetching the file upload form via HTMX
        response = self.client.get(reverse('design_tpl:upload_file', args=[1002, 'drawing']),
                                   HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_tpl/tpl_tree/_file_upload_form.html')
        self.assertContains(response, 'Upload Drawing for Item ID: 1002')

    def test_file_upload_view_post_htmx(self):
        # Test submitting a file upload via HTMX
        from django.core.files.uploadedfile import SimpleUploadedFile
        uploaded_file = SimpleUploadedFile("test.txt", b"file content", content_type="text/plain")
        
        response = self.client.post(reverse('design_tpl:upload_file', args=[1002, 'drawing']), 
                                    {'uploaded_file': uploaded_file},
                                    HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HTMX expects 204 No Content on success
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshTPLTreeTable', response.headers['HX-Trigger'])
        self.assertIn('closeModal', response.headers['HX-Trigger'])

```

#### Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for dynamic content:**
    *   The main `list.html` uses `hx-get` to load the `_tpl_tree_table.html` partial into a container on page load and on a custom `refreshTPLTreeTable` event.
    *   The "Expand Tree" checkbox triggers an `hx-get` to the `tpl_table_partial` view, passing its `checked` state, causing the entire table to re-render.
    *   Buttons for "Copy From", "Cancel", "Select", "View" (download), and "Upload" (`hx-get`) are used to trigger actions or load modal forms.
    *   The file upload form in the modal (`_file_upload_form.html`) uses `hx-post` to submit. On successful submission, the view returns a `204 No Content` response with `HX-Trigger` headers (`refreshTPLTreeTable`, `closeModal`) to update the main table and close the modal.

*   **Alpine.js for UI state:**
    *   Each `<tr>` in the recursive `_tpl_tree_node.html` has an `x-data="{ expanded: ... }"` attribute, allowing individual tree nodes to manage their expanded/collapsed state on the client side.
    *   The `x-show` directive dynamically hides/shows the nested table containing children based on the `expanded` state.
    *   The initial expansion state is passed from the server-side `expand_all_checked` variable to the Alpine.js component.
    *   Alpine.js, combined with simple `_hyperscript` (`on click add .is-active to #modal`), manages the modal visibility for forms like file upload.

*   **DataTables for List Views:**
    *   `_tpl_tree_table.html` includes a `<script>` block that initializes DataTables on the main `<table>` element (`#tplTreeListTable`).
    *   It's configured for pagination (`pageLength: 20`, `lengthMenu`) and disables sorting/searching for specific action columns.
    *   **Important Note on Tree View and DataTables:** While DataTables provides excellent client-side features for flat lists, it doesn't natively handle recursive tree structures with interactive collapse/expand within its core. The recursive template with Alpine.js handles the *visual* tree hierarchy. DataTables then operates on the *rendered rows* for search, sort, and pagination. For a truly robust, interactive tree grid where DataTables manages the hierarchy directly, a specific DataTables plugin (e.g., DataTables with TreeTable or a custom JS component) would typically be required, which conflicts with the "no additional JavaScript" directive. This implementation provides a practical compromise that leverages both DataTables and HTMX/Alpine.js for a performant and interactive experience within the given constraints.

## Final Notes

*   **Placeholder Replacement:** Remember to replace `[TABLE_NAME]`, `[MODEL_NAME]`, `[FIELD_TYPE]`, etc., with your actual database and application-specific values derived from your ASP.NET analysis.
*   **Django Setup:** Ensure your Django project is correctly configured (e.g., `settings.py` includes `design_tpl` in `INSTALLED_APPS`, database settings are pointing to your legacy SQL Server database via `django-mssql-pyodbc` or `django-pyodbc-azure` drivers, `TEMPLATES` settings correctly point to your template directories, and `MESSAGE_TAGS` are configured for `django.contrib.messages`).
*   **Static Files:** For icons (like `Add.PNG`) and CSS, ensure they are served correctly as Django static files (e.g., collected to `STATIC_ROOT` and served via your web server). Font Awesome is recommended for icons (`fas fa-caret-right` in the tree view).
*   **Security:** Always implement robust authentication and authorization mechanisms (e.g., Django's built-in `django.contrib.auth`, permissions, or custom middleware) to secure your application, as session data was implicitly used in ASP.NET.
*   **Error Handling & Logging:** Implement comprehensive error handling and logging in your Django application for production readiness.
*   **File Management:** The `TPLFileDownloadView` and `TPLFileUploadView` are simplified. For a real application, proper file storage (e.g., `django-storages` for cloud storage) and management would be necessary.