This modernization plan outlines the strategic transition from your legacy ASP.NET application, specifically the `BOM_UploadDrw` module, to a robust and modern Django 5.0+ solution. Our approach prioritizes automation, fat models, thin views, and leverages HTMX + Alpine.js for a highly responsive user experience without traditional JavaScript frameworks.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination (N/A for this module's UI, which is a single form)
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
From the C# code-behind, we observe `fun.update("tblDG_Item_Master", ...)` which directly refers to the database table. The update statements provide the column names.

**Inferred Database Details:**

*   **Table Name:** `tblDG_Item_Master`
*   **Primary Key (Inferred):** `Id` (used in `WHERE CompId='...' AND Id='...'`)
*   **Columns:**
    *   `Id` (Integer): Unique identifier for the item.
    *   `CompId` (Integer): Company identifier.
    *   `SysDate` (Date): System date of the last update.
    *   `SysTime` (Time): System time of the last update.
    *   `SessionId` (String): Identifier of the user session (username).
    *   `FileName` (String): Name of the drawing/image file (used when `img == "0"`).
    *   `FileSize` (Integer/Long): Size of the drawing/image file (used when `img == "0"`).
    *   `ContentType` (String): MIME type of the drawing/image file (used when `img == "0"`).
    *   `FileData` (Binary): Binary content of the drawing/image file (used when `img == "0"`).
    *   `AttName` (String): Name of the attachment file (used when `img != "0"`).
    *   `AttSize` (Integer/Long): Size of the attachment file (used when `img != "0"`).
    *   `AttContentType` (String): MIME type of the attachment file (used when `img != "0"`).
    *   `AttData` (Binary): Binary content of the attachment file (used when `img != "0"`).

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations and business logic of the ASP.NET code.

**Instructions:**
The ASP.NET page is dedicated to uploading a single file (either a drawing/image or a general attachment) and updating an existing `tblDG_Item_Master` record with that file's data.

*   **Operation Type:** Primarily an **Update** operation on an existing database record. There is no creation or deletion of `tblDG_Item_Master` records on this page.
*   **Input Parameters:**
    *   `WONo` (Work Order Number): From URL query string (`Request.QueryString`). Used for redirection.
    *   `Id` (Item ID): From URL query string (`Request.QueryString`). Maps to the `Id` column in `tblDG_Item_Master` for identifying the record to update.
    *   `img` (Image Type Indicator): From URL query string (`Request.QueryString`). Determines whether `FileData` fields (`img == "0"`) or `AttData` fields (`img != "0"`) are updated.
    *   `username`, `compid`, `finyear`: From ASP.NET Session. `compid` is used with `Id` to uniquely identify the record, `username` is stored as `SessionId`.
*   **Business Logic:**
    *   Reads file properties (name, size, content type) and binary data from the uploaded file.
    *   Conditionally updates specific columns (`FileName`/`FileData` or `AttName`/`AttData`) in `tblDG_Item_Master` based on the `img` parameter.
    *   Updates `SysDate`, `SysTime`, and `SessionId` with current system time and user.
*   **Redirection:** After a "Cancel" action or successful "Upload", the page redirects to `BOM_Design_WO_TreeView.aspx`, passing `WONo` and other module identifiers.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, and their Django equivalents.

**Inferred UI Components:**

*   **File Upload:** `asp:FileUpload ID="FileUpload1"`
    *   **Django Equivalent:** `forms.FileField` in a Django Form, rendered with a standard `<input type="file">` tag.
*   **Upload Button:** `asp:Button ID="bynUpload"`
    *   **Django Equivalent:** `<button type="submit">` with HTMX `hx-post` and `hx-confirm` for client-side validation.
*   **Cancel Button:** `asp:Button ID="btnCancel"`
    *   **Django Equivalent:** A standard HTML `<a>` tag or a button with `hx-redirect` to navigate back, passing the `WONo` parameter. For simplicity and direct translation of `Response.Redirect` on cancel, a standard `<a>` tag is sufficient here.
*   **Client-Side Confirmation:** `OnClientClick=" return confirmationUpload()"`
    *   **Django Equivalent:** HTMX `hx-confirm` attribute on the submit button. This provides a native browser confirmation dialog before the form submission.

### Step 4: Generate Django Code

We will create a new Django application named `design` to encapsulate this functionality.

#### 4.1 Models (`design/models.py`)

**Task:** Create a Django model based on the database schema, including business logic methods.

**Instructions:**
The `ItemMaster` model maps directly to `tblDG_Item_Master`. The core file upload logic from the ASP.NET `bynUpload_Click` method is moved into a `upload_drawing_or_attachment` method within the `ItemMaster` model, adhering to the "fat model" principle.

```python
# design/models.py
from django.db import models
from django.utils import timezone
from django.core.files.uploadedfile import InMemoryUploadedFile # To handle uploaded files directly

class ItemMaster(models.Model):
    """
    Django model for the tblDG_Item_Master table.
    Configured as unmanaged to connect to an existing database table.
    """
    # Explicit primary key as per ASP.NET usage
    id = models.IntegerField(db_column='Id', primary_key=True) 
    comp_id = models.IntegerField(db_column='CompId')
    sys_date = models.DateField(db_column='SysDate', null=True, blank=True)
    sys_time = models.TimeField(db_column='SysTime', null=True, blank=True)
    session_id = models.CharField(db_column='SessionId', max_length=255, null=True, blank=True)

    # Fields for 'img == 0' (Drawing/Image)
    file_name = models.CharField(db_column='FileName', max_length=255, null=True, blank=True)
    file_size = models.BigIntegerField(db_column='FileSize', null=True, blank=True)
    content_type = models.CharField(db_column='ContentType', max_length=255, null=True, blank=True)
    # BinaryField stores the raw file data directly in the database, mimicking ASP.NET behavior.
    # For large files, storing paths on the filesystem (using FileField) is generally recommended.
    file_data = models.BinaryField(db_column='FileData', null=True, blank=True) 

    # Fields for 'img != 0' (Attachment)
    att_name = models.CharField(db_column='AttName', max_length=255, null=True, blank=True)
    att_size = models.BigIntegerField(db_column='AttSize', null=True, blank=True)
    att_content_type = models.CharField(db_column='AttContentType', max_length=255, null=True, blank=True)
    att_data = models.BinaryField(db_column='AttData', null=True, blank=True) 

    class Meta:
        managed = False  # Django will not manage this table's creation, alteration, or deletion
        db_table = 'tblDG_Item_Master'  # Maps to the existing SQL Server table
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        """
        Returns a human-readable representation of the ItemMaster object.
        """
        return f"Item {self.id} (Company: {self.comp_id})"

    def upload_drawing_or_attachment(self, uploaded_file: InMemoryUploadedFile, img_type: str, session_id: str) -> bool:
        """
        Updates the ItemMaster record with drawing or attachment data based on img_type.
        This method encapsulates the core file upload business logic from the ASP.NET code-behind.
        
        Args:
            uploaded_file: The uploaded file object (from request.FILES).
            img_type: A string ('0' for drawing, any other value for attachment).
            session_id: The ID of the current user session (e.g., username).
            
        Returns:
            True if the update was successful.
        """
        curr_date = timezone.now().date()
        curr_time = timezone.now().time()

        # Clear both sets of file fields before populating to ensure data consistency
        # based on the new upload type.
        self.file_name = None
        self.file_size = None
        self.content_type = None
        self.file_data = None
        self.att_name = None
        self.att_size = None
        self.att_content_type = None
        self.att_data = None

        if img_type == '0':  # Drawing/Image upload
            self.file_name = uploaded_file.name
            self.file_size = uploaded_file.size
            self.content_type = uploaded_file.content_type
            self.file_data = uploaded_file.read()  # Read binary content into the database field
        else:  # Attachment upload
            self.att_name = uploaded_file.name
            self.att_size = uploaded_file.size
            self.att_content_type = uploaded_file.content_type
            self.att_data = uploaded_file.read()  # Read binary content into the database field
        
        self.session_id = session_id
        self.sys_date = curr_date
        self.sys_time = curr_time
        
        self.save()  # Persist changes to the database
        return True

```

#### 4.2 Forms (`design/forms.py`)

**Task:** Define a Django form for user input, specifically for the file upload.

**Instructions:**
A simple `forms.Form` is sufficient here, as we are only capturing a file, not directly creating or updating an entire `ItemMaster` instance through the form itself.

```python
# design/forms.py
from django import forms

class DrawingUploadForm(forms.Form):
    """
    Form for handling the drawing/file upload.
    This is a non-ModelForm as it only captures the file for an update operation
    on an existing ItemMaster record.
    """
    drawing_file = forms.FileField(
        label="Select Drawing/File",
        widget=forms.FileInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'size': '39' # Replicates the 'size' attribute from the ASP.NET FileUpload control
        })
    )

```

#### 4.3 Views (`design/views.py`)

**Task:** Implement the file upload functionality using a Django Class-Based View.

**Instructions:**
A `View` class handles both GET (display form) and POST (process upload) requests. Business logic (file saving) is delegated to the `ItemMaster` model. The view ensures session data and existing `ItemMaster` record validity.

```python
# design/views.py
from django.views import View
from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from .models import ItemMaster
from .forms import DrawingUploadForm

class UploadDrawingView(View):
    """
    Django Class-Based View for uploading drawings or attachments to an existing
    ItemMaster record. It handles GET (display form) and POST (process upload).
    """
    template_name = 'design/upload_drw.html'

    def _get_context_and_item(self, request, pk, img_type, form=None):
        """
        Helper method to prepare context data and fetch the ItemMaster object.
        Ensures Company ID and Item ID are valid.
        """
        wono = request.GET.get('WONo', '') if request.method == 'GET' else request.POST.get('wono', '')
        
        if not form:
            form = DrawingUploadForm()
        
        comp_id = request.session.get('compid')
        if not comp_id:
            messages.error(request, "Company ID not found in session. Please log in again or contact support.")
            return None, HttpResponseRedirect(reverse_lazy('some_error_page_or_login')) # Redirect to appropriate page for session error

        try:
            item = ItemMaster.objects.get(id=pk, comp_id=comp_id)
        except ItemMaster.DoesNotExist:
            messages.error(request, f"Item with ID {pk} and Company ID {comp_id} not found.")
            return None, HttpResponseRedirect(reverse_lazy('some_error_page_or_list')) # Redirect if item not found

        context = {
            'form': form,
            'wono': wono,
            'item_id': pk,
            'img_type': img_type,
            'item': item, # Pass the item object for potential display
        }
        return context, None

    def get(self, request, pk: int, img_type: str):
        """
        Handles GET requests: displays the file upload form.
        """
        context, redirect_response = self._get_context_and_item(request, pk, img_type)
        if redirect_response: 
            return redirect_response # Redirect if session/item lookup failed
        return render(request, self.template_name, context)

    def post(self, request, pk: int, img_type: str):
        """
        Handles POST requests: processes the file upload.
        """
        form = DrawingUploadForm(request.POST, request.FILES)
        context, redirect_response = self._get_context_and_item(request, pk, img_type, form=form)
        if redirect_response: 
            return redirect_response # Redirect if session/item lookup failed
        
        item = context['item']
        wono = context['wono']
        session_id = request.session.get('username')

        if not session_id:
            messages.error(request, "User session ID not found. Cannot process upload.")
            return HttpResponse(status=400, content="Username missing.") if request.headers.get('HX-Request') else redirect_response

        if form.is_valid():
            try:
                uploaded_file = form.cleaned_data['drawing_file']
                item.upload_drawing_or_attachment(uploaded_file, img_type, session_id)
                messages.success(request, f"File '{uploaded_file.name}' uploaded successfully.")
                
                # Construct the redirection URL based on ASP.NET's Response.Redirect logic
                success_redirect_url = f"{reverse_lazy('design:bom_design_wo_treeview')}?WONo={wono}&ModId=3&SubModId=26"

                # Handle HTMX vs. standard redirection
                if request.headers.get('HX-Request'):
                    return HttpResponse(status=204, headers={'HX-Redirect': success_redirect_url})
                else:
                    return HttpResponseRedirect(success_redirect_url)

            except Exception as e:
                messages.error(request, f"An error occurred during file upload: {e}")
                if request.headers.get('HX-Request'):
                    return HttpResponse(status=500, content=f"Internal server error: {e}")
                return render(request, self.template_name, context) # Re-render form with error message
        else:
            messages.error(request, "Please correct the errors in the form.")
            return render(request, self.template_name, context) # Re-render form with validation errors

# Placeholder view for the ASP.NET redirection target: BOM_Design_WO_TreeView.aspx
# In a full migration, this would be a proper Django view for the tree view.
class BOMDesignWoTreeView(View):
    """
    A placeholder view simulating the target page BOM_Design_WO_TreeView.aspx.
    In a real application, this would be a fully implemented Django view for the BOM tree.
    """
    def get(self, request):
        wono = request.GET.get('WONo', 'N/A')
        mod_id = request.GET.get('ModId', 'N/A')
        sub_mod_id = request.GET.get('SubModId', 'N/A')
        return HttpResponse(f"""
            <div style="padding: 20px; font-family: sans-serif;">
                <h2>Redirected to BOM Design WO Tree View</h2>
                <p><strong>Work Order No:</strong> {wono}</p>
                <p><strong>Module ID:</strong> {mod_id}</p>
                <p><strong>Sub-Module ID:</strong> {sub_mod_id}</p>
                <p>This is a placeholder page. The actual BOM tree view functionality would be implemented here.</p>
            </div>
        """)

```

#### 4.4 Templates (`design/templates/design/upload_drw.html`)

**Task:** Create the HTML template for the file upload form.

**Instructions:**
The template extends `core/base.html` and uses Tailwind CSS for styling. HTMX handles the form submission and the `hx-confirm` attribute replaces the client-side JavaScript confirmation.

```html
{# design/templates/design/upload_drw.html #}
{% extends 'core/base.html' %}

{% block title %}BOM Design - Upload Drawing/File{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col items-center justify-center min-h-screen-minus-header">
        <div class="bg-white p-8 rounded-lg shadow-xl max-w-md w-full">
            <h2 class="text-2xl font-bold text-gray-800 mb-6 text-center">BOM Design - Upload Drw</h2>
            
            {# Django messages for success/error feedback #}
            {% if messages %}
                <div class="mb-4 space-y-2">
                    {% for message in messages %}
                        <div class="p-3 rounded-md text-sm {% if message.tags == 'error' %}bg-red-100 text-red-700{% else %}bg-green-100 text-green-700{% endif %}" role="alert">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}

            <form hx-post="{% url 'design:upload_drw' item_id img_type %}" 
                  hx-encoding="multipart/form-data" {# Essential for file uploads with HTMX #}
                  class="space-y-6">
                {% csrf_token %}
                
                {# Hidden field to pass WONo back to the view on POST #}
                <input type="hidden" name="wono" value="{{ wono }}">
                
                <div>
                    <label for="{{ form.drawing_file.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.drawing_file.label }}
                    </label>
                    {{ form.drawing_file }}
                    {# Display form field errors #}
                    {% if form.drawing_file.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.drawing_file.errors }}</p>
                    {% endif %}
                </div>
                
                <div class="flex justify-between mt-6">
                    {# Cancel button acts as a direct link, mimicking ASP.NET's Response.Redirect on cancel #}
                    <a href="{% url 'design:bom_design_wo_treeview' %}?WONo={{ wono }}&ModId=3&SubModId=26" 
                       class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                        Cancel
                    </a>
                    
                    {# Upload button with HTMX and a confirmation dialog #}
                    <button type="submit" 
                            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                            hx-confirm="Are you sure you want to upload this file?"> {# Replaces ASP.NET's OnClientClick confirmation #}
                        Upload
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# No additional JavaScript needed for this specific functionality, as HTMX handles interactions. #}
{# Alpine.js would be used for more complex UI state management if required. #}
{% endblock %}

```

#### 4.5 URLs (`design/urls.py`)

**Task:** Define URL patterns for the upload view and the placeholder redirection target.

**Instructions:**
The URL path captures the `pk` (Item ID) and `img_type` from the URL, allowing for dynamic behavior based on these parameters.

```python
# design/urls.py
from django.urls import path
from .views import UploadDrawingView, BOMDesignWoTreeView # Import the main view and placeholder

app_name = 'design'  # Namespace for this Django application's URLs

urlpatterns = [
    # URL pattern for the file upload page:
    # Example: /design/upload_drw/101/0/?WONo=WO456
    # <int:pk> captures the 'Id' of the ItemMaster record.
    # <str:img_type> captures the 'img' parameter ('0' for drawing, '1' for attachment).
    path('upload_drw/<int:pk>/<str:img_type>/', UploadDrawingView.as_view(), name='upload_drw'),
    
    # Placeholder URL for the redirection target from the ASP.NET code.
    # In a full migration, this would point to the actual BOM tree view page.
    path('bom_design_wo_treeview/', BOMDesignWoTreeView.as_view(), name='bom_design_wo_treeview'),
]

```

#### 4.6 Tests (`design/tests.py`)

**Task:** Write comprehensive unit tests for the model and integration tests for the view.

**Instructions:**
Tests cover:
*   `ItemMaster` model: Creation, and the `upload_drawing_or_attachment` method for both drawing and attachment types.
*   `UploadDrawingView`: GET requests (displaying the form, handling missing items/session data), POST requests (successful upload for both types, form validation failures, HTMX-specific responses, error handling).

```python
# design/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.core.files.uploadedfile import SimpleUploadedFile
from django.utils import timezone
from django.contrib.messages import get_messages

from .models import ItemMaster
from .forms import DrawingUploadForm

class ItemMasterModelTest(TestCase):
    """
    Unit tests for the ItemMaster model and its business logic methods.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a test ItemMaster instance to upload files to.
        # This will be used across all model tests.
        ItemMaster.objects.create(
            id=101,
            comp_id=1,
            sys_date=timezone.now().date(),
            sys_time=timezone.now().time(),
            session_id='initial_user',
        )
        # Create another item for testing different company IDs if needed
        ItemMaster.objects.create(
            id=102,
            comp_id=2,
            sys_date=timezone.now().date(),
            sys_time=timezone.now().time(),
            session_id='another_initial_user',
        )
  
    def setUp(self):
        """Set up data for each test method."""
        self.item = ItemMaster.objects.get(id=101, comp_id=1)
        self.dummy_session_id = 'testuser_session_123'

    def test_item_master_creation(self):
        """Tests that an ItemMaster object can be created correctly."""
        obj = ItemMaster.objects.get(id=101)
        self.assertEqual(obj.id, 101)
        self.assertEqual(obj.comp_id, 1)
        self.assertEqual(obj.session_id, 'initial_user')

    def test_upload_drawing_successfully(self):
        """Tests the upload_drawing_or_attachment method for drawing type ('0')."""
        test_file_content = b"This is dummy drawing content for a test file."
        uploaded_file = SimpleUploadedFile(
            "test_drawing.drw", 
            test_file_content, 
            content_type="application/octet-stream"
        )
        
        self.item.upload_drawing_or_attachment(uploaded_file, '0', self.dummy_session_id)
        self.item.refresh_from_db() # Reload object from DB to get latest saved data

        self.assertEqual(self.item.file_name, "test_drawing.drw")
        self.assertEqual(self.item.file_size, len(test_file_content))
        self.assertEqual(self.item.content_type, "application/octet-stream")
        self.assertEqual(self.item.file_data, test_file_content)
        self.assertEqual(self.item.session_id, self.dummy_session_id)
        self.assertIsNotNone(self.item.sys_date)
        self.assertIsNotNone(self.item.sys_time)
        
        # Ensure attachment-specific fields are cleared
        self.assertIsNone(self.item.att_name)
        self.assertIsNone(self.item.att_size)
        self.assertIsNone(self.item.att_content_type)
        self.assertIsNone(self.item.att_data)

    def test_upload_attachment_successfully(self):
        """Tests the upload_drawing_or_attachment method for attachment type (non-'0')."""
        test_file_content = b"This is dummy attachment content for a test PDF."
        uploaded_file = SimpleUploadedFile(
            "test_document.pdf", 
            test_file_content, 
            content_type="application/pdf"
        )
        
        self.item.upload_drawing_or_attachment(uploaded_file, '1', self.dummy_session_id)
        self.item.refresh_from_db()

        self.assertEqual(self.item.att_name, "test_document.pdf")
        self.assertEqual(self.item.att_size, len(test_file_content))
        self.assertEqual(self.item.att_content_type, "application/pdf")
        self.assertEqual(self.item.att_data, test_file_content)
        self.assertEqual(self.item.session_id, self.dummy_session_id)

        # Ensure drawing-specific fields are cleared
        self.assertIsNone(self.item.file_name)
        self.assertIsNone(self.item.file_size)
        self.assertIsNone(self.item.content_type)
        self.assertIsNone(self.item.file_data)

class UploadDrawingViewsTest(TestCase):
    """
    Integration tests for the UploadDrawingView.
    """
    @classmethod
    def setUpTestData(cls):
        # Create ItemMaster instances for testing view interactions.
        ItemMaster.objects.create(id=101, comp_id=1, sys_date=timezone.now().date(), sys_time=timezone.now().time(), session_id='user1')
        ItemMaster.objects.create(id=102, comp_id=1, sys_date=timezone.now().date(), sys_time=timezone.now().time(), session_id='user1')
        ItemMaster.objects.create(id=103, comp_id=99, sys_date=timezone.now().date(), sys_time=timezone.now().time(), session_id='user99')
    
    def setUp(self):
        """Set up client and session for each test method."""
        self.client = Client()
        # Simulate Django session data corresponding to ASP.NET session
        session = self.client.session
        session['compid'] = 1
        session['username'] = 'testuser_django' # Matches ASP.NET's Session["username"]
        session.save()
        
        self.item_id = 101
        self.img_type_drawing = '0'
        self.img_type_attachment = '1'
        self.wono = 'WO-TEST-001'
        
        # Base URL for the upload view
        self.upload_url_drawing = reverse('design:upload_drw', args=[self.item_id, self.img_type_drawing]) + f'?WONo={self.wono}'
        self.upload_url_attachment = reverse('design:upload_drw', args=[self.item_id, self.img_type_attachment]) + f'?WONo={self.wono}'
        
        # Expected redirection target URL
        self.redirect_target_url = reverse('design:bom_design_wo_treeview') + f'?WONo={self.wono}&ModId=3&SubModId=26'

    def test_get_view_success(self):
        """Tests accessing the upload form page successfully."""
        response = self.client.get(self.upload_url_drawing)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/upload_drw.html')
        self.assertContains(response, "BOM Design - Upload Drw")
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['wono'], self.wono)
        self.assertEqual(response.context['item_id'], self.item_id)
        self.assertEqual(response.context['img_type'], self.img_type_drawing)
        self.assertIsNotNone(response.context['item'])

    def test_get_view_item_not_found(self):
        """Tests GET request when the ItemMaster object does not exist for the given ID and Comp ID."""
        url_non_existent_item = reverse('design:upload_drw', args=[999, self.img_type_drawing]) + f'?WONo={self.wono}'
        response = self.client.get(url_non_existent_item)
        # Should redirect to a configured error/fallback page
        self.assertEqual(response.status_code, 302) 
        self.assertRedirects(response, reverse_lazy('some_error_page_or_list')) # Check redirect target

    def test_get_view_no_compid_in_session(self):
        """Tests GET request when company ID is missing from session."""
        session = self.client.session
        del session['compid'] # Remove compid from session
        session.save()
        response = self.client.get(self.upload_url_drawing)
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse_lazy('some_error_page_or_login'))

    def test_post_upload_drawing_success(self):
        """Tests successful drawing file upload (img_type='0')."""
        test_file_content = b"Binary data for drawing upload test."
        uploaded_file = SimpleUploadedFile("drawing.png", test_file_content, "image/png")
        data = {'drawing_file': uploaded_file, 'wono': self.wono}
        
        # Use follow=True to follow the redirection after POST
        response = self.client.post(self.upload_url_drawing, data, follow=True) 

        self.assertEqual(response.status_code, 200) # Status code after following redirect
        # Check if we landed on the correct placeholder redirection page
        self.assertContains(response, f"Redirected to BOM Design WO Tree View for WONo: {self.wono}") 

        # Verify the database record was updated
        item = ItemMaster.objects.get(id=self.item_id)
        self.assertEqual(item.file_name, "drawing.png")
        self.assertEqual(item.file_size, len(test_file_content))
        self.assertEqual(item.content_type, "image/png")
        self.assertEqual(item.file_data, test_file_content)
        self.assertEqual(item.session_id, 'testuser_django')
        
        # Check for success message
        messages_list = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages_list), 1)
        self.assertEqual(str(messages_list[0]), "File 'drawing.png' uploaded successfully.")

    def test_post_upload_attachment_success(self):
        """Tests successful attachment file upload (img_type='1')."""
        test_file_content = b"Binary data for attachment upload test."
        uploaded_file = SimpleUploadedFile("document.pdf", test_file_content, "application/pdf")
        data = {'drawing_file': uploaded_file, 'wono': self.wono}
        
        response = self.client.post(self.upload_url_attachment, data, follow=True)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, f"Redirected to BOM Design WO Tree View for WONo: {self.wono}")

        # Verify the database record was updated
        item = ItemMaster.objects.get(id=self.item_id)
        self.assertEqual(item.att_name, "document.pdf")
        self.assertEqual(item.att_size, len(test_file_content))
        self.assertEqual(item.att_content_type, "application/pdf")
        self.assertEqual(item.att_data, test_file_content)
        self.assertEqual(item.session_id, 'testuser_django')

        # Check for success message
        messages_list = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages_list), 1)
        self.assertEqual(str(messages_list[0]), "File 'document.pdf' uploaded successfully.")

    def test_post_invalid_form_no_file(self):
        """Tests POST request with an invalid form (no file selected)."""
        data = {'wono': self.wono} # Missing 'drawing_file'
        response = self.client.post(self.upload_url_drawing, data)
        
        self.assertEqual(response.status_code, 200) # Should render the page again with form errors
        self.assertTemplateUsed(response, 'design/upload_drw.html')
        self.assertContains(response, "This field is required.") # Error message for missing file
        
        # Check for error message
        messages_list = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages_list), 1)
        self.assertEqual(str(messages_list[0]), "Please correct the errors in the form.")

    def test_post_htmx_upload_success(self):
        """Tests successful HTMX file upload (expects 204 status with HX-Redirect header)."""
        test_file_content = b"HTMX specific test data."
        uploaded_file = SimpleUploadedFile("htmx_test.txt", test_file_content, "text/plain")
        data = {'drawing_file': uploaded_file, 'wono': self.wono}
        headers = {'HTTP_HX_REQUEST': 'true'} # Indicate this is an HTMX request
        
        response = self.client.post(self.upload_url_drawing, data, **headers)
        
        self.assertEqual(response.status_code, 204) # HTMX success implies no content, just headers
        self.assertIn('HX-Redirect', response.headers)
        self.assertEqual(response.headers['HX-Redirect'], self.redirect_target_url)

        # Verify data was saved (need to check separately as no content returned)
        item = ItemMaster.objects.get(id=self.item_id)
        self.assertEqual(item.file_name, "htmx_test.txt")
        self.assertEqual(item.file_size, len(test_file_content))
        
        # Check for success message (stored in session for next request after redirect)
        messages_list = list(get_messages(self.client.session._request))
        self.assertEqual(len(messages_list), 1)
        self.assertEqual(str(messages_list[0]), "File 'htmx_test.txt' uploaded successfully.")

    def test_post_htmx_invalid_form(self):
        """Tests HTMX POST request with an invalid form."""
        data = {'wono': self.wono} # Missing 'drawing_file'
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(self.upload_url_drawing, data, **headers)
        
        self.assertEqual(response.status_code, 200) # HTMX typically re-renders the form with errors
        self.assertTemplateUsed(response, 'design/upload_drw.html')
        self.assertContains(response, "This field is required.")

    def test_post_htmx_no_session_username(self):
        """Tests HTMX POST request when username (session_id) is missing from session."""
        session = self.client.session
        del session['username']
        session.save()
        
        test_file_content = b"HTMX test data."
        uploaded_file = SimpleUploadedFile("htmx_test.txt", test_file_content, "text/plain")
        data = {'drawing_file': uploaded_file, 'wono': self.wono}
        headers = {'HTTP_HX_REQUEST': 'true'}
        
        response = self.client.post(self.upload_url_drawing, data, **headers)
        
        self.assertEqual(response.status_code, 400) # Bad Request for missing session data
        self.assertEqual(response.content.decode(), "Username missing.")

    def test_post_htmx_item_not_found(self):
        """Tests HTMX POST request when the ItemMaster object is not found."""
        url_non_existent_item = reverse('design:upload_drw', args=[999, self.img_type_drawing]) + f'?WONo={self.wono}'
        test_file_content = b"HTMX test data."
        uploaded_file = SimpleUploadedFile("htmx_test.txt", test_file_content, "text/plain")
        data = {'drawing_file': uploaded_file, 'wono': self.wono}
        headers = {'HTTP_HX_REQUEST': 'true'}
        
        response = self.client.post(url_non_existent_item, data, **headers)
        
        self.assertEqual(response.status_code, 404) # Not Found for HTMX
        self.assertEqual(response.content.decode(), "Item not found.")

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
*   **HTMX for Form Submission:** The `<form>` element in `upload_drw.html` uses `hx-post` for asynchronous submission and `hx-encoding="multipart/form-data"` to handle file uploads.
*   **HTMX for Confirmation:** The `hx-confirm="Are you sure you want to upload this file?"` attribute on the "Upload" button provides a native confirmation dialog, replacing the original `OnClientClick` JavaScript.
*   **HTMX for Redirection:** Upon successful upload via HTMX, the Django view sends an `HX-Redirect` header. This instructs the browser to perform a full page redirect to the specified URL (the BOM tree view), maintaining the original application's navigation flow.
*   **Alpine.js:** For this specific page, Alpine.js is not strictly required as HTMX handles the primary interactions and form validation. If more complex client-side UI states or interactions were identified (e.g., dynamically showing/hiding fields, intricate modal logic beyond simple confirmation), Alpine.js would be integrated via `x-data` attributes directly in the HTML. The provided template `extra_js` block includes a placeholder.
*   **DataTables:** Not applicable for this specific ASP.NET page as it's a single form, not a list view. DataTables would be utilized in a separate Django List View template for the BOM items.

---

### Final Notes

*   **Placeholders:** Replace `some_error_page_or_login`, `some_error_page_or_list` with actual URLs in your Django project, typically pointing to a login page or an error handling view.
*   **Binary Data Storage:** The current solution mirrors ASP.NET's approach of storing binary file data directly in the database (`BinaryField`). For large files or high-volume uploads, a more scalable approach is to store files on the file system (e.g., AWS S3, Azure Blob Storage) and save only the file path/URL in the database using Django's `FileField` or `ImageField`. This is a recommended modernization step after the initial migration.
*   **Session Management:** The `request.session.get('compid')` and `request.session.get('username')` mimic the ASP.NET `Session` object. Ensure your Django authentication and session middleware are configured to populate these session variables or map them to `request.user` attributes.
*   **Error Handling:** The C# code-behind had empty `catch` blocks. The Django solution provides explicit error messages and redirects/HTMX responses for robustness.
*   **DRY Principle:** Business logic for file handling is centralized in the `ItemMaster` model's `upload_drawing_or_attachment` method, making the view thin and reusable.
*   **Automation:** This structured plan, with clearly defined steps and complete code snippets, is designed to be easily followed and integrated into an AI-assisted automation pipeline, reducing manual effort and potential errors in the migration process.