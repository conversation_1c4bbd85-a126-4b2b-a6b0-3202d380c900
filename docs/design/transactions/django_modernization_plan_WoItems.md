This comprehensive Django modernization plan outlines the strategic approach to transitioning your legacy ASP.NET application to a robust, modern Django 5.0+ solution. Our focus is on leveraging AI-assisted automation, applying the "Fat Model, Thin View" paradigm, and integrating dynamic frontend technologies like HTMX and Alpine.js to deliver a highly performant and maintainable system. The plan prioritizes business benefits and actionable steps, minimizing technical jargon.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns based on the ASP.NET code, inferring data types.

**Instructions:**
From the ASP.NET code-behind, we infer the following tables and their primary columns. Note that the original code implicitly handles `CompId`, `FinYearId`, `SysDate`, `SysTime`, `SessionId` as common fields across many tables for auditing and multi-tenancy. We'll include these.

*   **`tblDG_Item_Master` (Django Model: `Item`)**
    *   `Id` (PK, int)
    *   `ItemCode` (string)
    *   `ManfDesc` (string)
    *   `PurchDesc` (string)
    *   `UOMBasic` (FK to `Unit_Master`, int)
    *   `UOMPurchase` (FK to `Unit_Master`, int)
    *   `Location` (FK to `tblDG_Location_Master`, int)
    *   `CId` (FK to `tblDG_Category_Master`, int)
    *   `SCId` (FK to `tblDG_SubCategory_Master`, int)
    *   `StockQty` (double)
    *   `PartNo` (string)
    *   `Revision` (int)
    *   `Absolute` (int, boolean-like flag)
    *   `CompId` (int)
    *   `FinYearId` (int)
    *   `SysDate` (datetime)
    *   `SysTime` (datetime)
    *   `SessionId` (string)
    *   `OpeningBalDate` (datetime)
    *   `OpeningBalQty` (double)

*   **`tblDG_Category_Master` (Django Model: `Category`)**
    *   `CId` (PK, int)
    *   `CName` (string)
    *   `CompId` (int)
    *   `FinYearId` (int)

*   **`tblDG_SubCategory_Master` (Django Model: `SubCategory`)**
    *   `SCId` (PK, int)
    *   `SCName` (string)
    *   `CId` (FK to `tblDG_Category_Master`, int)
    *   `Symbol` (string)
    *   `CompId` (int)
    *   `FinYearId` (int)

*   **`Unit_Master` (Django Model: `Unit`)**
    *   `Id` (PK, int)
    *   `Symbol` (string)

*   **`tblDG_Location_Master` (Django Model: `Location`)**
    *   `Id` (PK, int)
    *   `LocationLabel` (string)
    *   `LocationNo` (string)

*   **`tblDG_TPLItem_Temp` (Django Model: `TplItemTemp`)**
    *   `Id` (PK, int)
    *   `WONo` (string)
    *   `ItemId` (FK to `tblDG_Item_Master`, int, can be NULL if new item)
    *   `Qty` (double)
    *   `ChildId` (int)
    *   `CompId` (int)
    *   `SessionId` (string)
    *   `ItemCode` (string, for new items not yet in Item Master)
    *   `ManfDesc` (string, for new items)
    *   `PurchDesc` (string, for new items)
    *   `UOMBasic` (int, FK to `Unit_Master`, for new items)
    *   `UOMPurchase` (int, FK to `Unit_Master`, for new items)
    *   `Weldments` (int, boolean-like flag)

*   **`tblDG_TPL_Master` (Django Model: `TplMaster`)**
    *   `Id` (PK, int)
    *   `SysDate` (datetime)
    *   `SysTime` (datetime)
    *   `CompId` (int)
    *   `FinYearId` (int)
    *   `SessionId` (string)
    *   `WONo` (string)
    *   `PId` (int, Parent ID in TPL hierarchy)
    *   `CId` (int, Child ID in TPL hierarchy)
    *   `ItemId` (FK to `tblDG_Item_Master`, int)
    *   `Qty` (double)
    *   `ConvertToBOM` (int, boolean-like flag)
    *   `Weldments` (int, boolean-like flag)

*   **`tblDG_BOM_Master` (Django Model: `BomMaster`)**
    *   `Id` (PK, int)
    *   `SysDate` (datetime)
    *   `SysTime` (datetime)
    *   `CompId` (int)
    *   `FinYearId` (int)
    *   `SessionId` (string)
    *   `WONo` (string)
    *   `PId` (int, Parent ID in BOM hierarchy)
    *   `CId` (int, Child ID in BOM hierarchy)
    *   `ItemId` (FK to `tblDG_Item_Master`, int)
    *   `Qty` (double)
    *   `Weldments` (int, boolean-like flag)

*   **`SD_Cust_WorkOrder_Master` (Django Model: `WorkOrderMaster`)**
    *   `WONo` (PK/unique key, string)
    *   `CompId` (PK/unique key, int)
    *   `UpdateWO` (int, boolean-like flag)

### Step 2: Identify Backend Functionality

**Task:** Determine the business logic and data operations from the C# code.

**Instructions:**
The ASP.NET code exhibits complex business logic primarily within the `clsFunctions` class (not provided, but inferred) and the page's event handlers. This logic must be meticulously translated into Django model managers and methods.

*   **Read (R):**
    *   **Item Master List:** `Fillgridview` populates `GridView2` with items based on category, subcategory, item code, manufacturing description, purchase description, or location. Includes pagination. This requires filtering and ordering on the `Item` model.
    *   **Selected Items List:** `FillDatagrid` populates `GridView4` from `tblDG_TPLItem_Temp`, joining with `Item_Master` and `Unit_Master` to get full details. Calculates `AsslyQty` (recursive quantity) and `BOMQty` (assembly qty * requested qty).
    *   **Dropdown Population:** Initial loading of categories, subcategories, units, and locations.
    *   **Work Order & Assembly Info:** Retrieving `WONo` and `AsslyNo` (ItemCode) from query string and database respectively.
    *   **Item Code Limits:** Retrieving `ItemCodeLimit` and `SetBomItemLimit` from `clsFunctions`.

*   **Create (C):**
    *   **Add Existing Item to Temp List:** `GridView2_RowCommand` (CommandName="Add") inserts an `ItemId` and `Qty` into `tblDG_TPLItem_Temp`. Includes validation (quantity > 0, not already inserted).
    *   **Add New Item to Temp List:** `btnSubmit_Click` handles creating a *new, user-defined* item. It generates an `ItemCode`, handles `Revision` (incrementing if `CKRevision` is checked), and inserts the item's details into `tblDG_TPLItem_Temp`. Validation includes part number length and quantity > 0.

*   **Delete (D):**
    *   **Remove from Temp List:** `GridView4_RowCommand` (CommandName="del") deletes a record from `tblDG_TPLItem_Temp`.
    *   **Clear Temp Table:** `clearTempDb` is called after successful processing (`AddToTPLBOM`).

*   **Update (U):**
    *   **Process Temp Items to Master Tables:** `AddToTPLBOM` (called by `btnproceed_Click` and `Button1_Click`). This is the most critical and complex operation:
        *   It iterates through `tblDG_TPLItem_Temp` records.
        *   **If `ItemId` is NULL (new item):**
            *   Checks if the generated `ItemCode` already exists in `tblDG_Item_Master`. If not, it creates a new record in `tblDG_Item_Master`.
            *   Inserts the item into `tblDG_TPL_Master`.
            *   If `ConvertToBOM` is true (cnv=1), it *also* inserts into `tblDG_BOM_Master`.
        *   **If `ItemId` exists (existing item):**
            *   Inserts the existing item into `tblDG_TPL_Master`.
            *   If `ConvertToBOM` is true (cnv=1), it *also* inserts into `tblDG_BOM_Master`.
        *   Updates `SD_Cust_WorkOrder_Master.UpdateWO` to '1'.
        *   Finally, calls `clearTempDb`.

*   **Validation Logic:**
    *   Required field validators (`RequiredFieldValidator`).
    *   Regular expression validators for quantity (`^\d{1,15}(\.\d{0,3})?$`).
    *   Client-side JavaScript confirmations (`confirmationAdd()`, `confirmationDelete()`).
    *   Server-side checks for `isNaN`, quantity > 0, record already inserted, part number length.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, mapping them to Django templates with HTMX/Alpine.js.

**Instructions:**
The ASP.NET UI uses a tabbed interface (`TabContainer`) and `GridView` controls for displaying data. Input fields are standard textboxes and dropdowns. The migration will replace these with modern, interactive components.

*   **Overall Layout:** The page has a header displaying "TPL Items" with WO and Assembly numbers. This will be part of the main `woitems_dashboard.html` template.
*   **Tab Container (`TabContainer1`):** Replaced by a `div` with `hx-target` and `hx-get` attributes to dynamically load tab content using HTMX on tab clicks. Alpine.js can manage active tab styling.
*   **Tab Panel - "Item Master":**
    *   **Filters/Search:** `DrpCategory`, `DrpSubCategory`, `DrpSearchCode`, `DropDownList3`, `txtSearchItemCode`, `btnSearch`. These will be Django forms rendered as HTML, with HTMX `hx-get` or `hx-post` to re-fetch the `_item_master_table.html` partial. `DropDownList3` (Location) visibility toggled by Alpine.js based on `DrpSearchCode` selection.
    *   **Data Grid (`GridView2`):** Replaced by a `<table>` integrated with DataTables. Each row will have an "Add" button that makes an HTMX `hx-post` request to add the item to the temporary list. Quantity input `txtQty` will be an `<input type="number">` with validation.
*   **Tab Panel - "New Items":**
    *   **Form Inputs:** `DDLCategory`, `txtPartNo`, `CKRevision` (checkbox), `txtManfDescription` (textarea), `txtPurchDescription` (textarea), `DDLUnitBasic`, `DDLUnitPurchase`, `txtQuntity`. These will be rendered by a Django form.
    *   **Validation:** Django's built-in form validation for `RequiredFieldValidator` and `RegularExpressionValidator`.
    *   **Buttons:** `btnSubmit` and `Button1` (Cancel) will be HTMX `hx-post` buttons (or a `type="submit"` on the form) for submission and HTMX `hx-on` for cancel.
*   **Tab Panel - "Copy From":**
    *   **Iframe (`frm2`):** The iframe suggests a separate, possibly complex, module. For this migration, we will represent it as a placeholder or a link to a separate Django view. The content of `TPL_Design_CopyWo.aspx` is outside the scope of this request.
*   **Tab Panel - "Selected Items":**
    *   **Data Grid (`GridView4`):** Replaced by a `<table>` integrated with DataTables. Each row will have a "Delete" button making an HTMX `hx-delete` request.
    *   **Action Buttons:** `btnproceed` (Add To TPL) and `btnCovBom` (Add To TPL & BOM) will be HTMX `hx-post` buttons, triggering the final processing logic. `Button4` (Cancel) redirects.
*   **Pop-up Messages:** Replaced by Django's `messages` framework, displayed dynamically in the template, possibly fading out using Alpine.js or HTMX swap.
*   **Loading Notifiers:** Replaced by HTMX's `hx-indicator` or simple Alpine.js state changes.

### Step 4: Generate Django Code
We will organize the code within a Django app named `work_orders`.

#### 4.1 Models (`work_orders/models.py`)

This section defines the Django models for the database tables identified, complete with a custom manager for the complex business logic found in `clsFunctions` and the page's event handlers. This adheres to the "Fat Model" principle.

```python
from django.db import models, transaction
from django.db.models import F
from django.utils import timezone
from datetime import datetime
import math # For calculations involving double/decimal

# --- Custom Managers for Business Logic ---

class ItemManager(models.Manager):
    def get_filtered_items(self, category_id, subcategory_id, search_type, search_term, location_id, company_id, fin_year_id, assembly_no_id, child_id, wono):
        """
        Mimics the Fillgridview logic to fetch items from tblDG_Item_Master.
        """
        queryset = self.get_queryset().select_related('category', 'subcategory', 'uom_basic', 'uom_purchase')
        
        # Exclude Root Assembly in Item to add (logic from getTPLBOMRootnode and x1)
        # This logic is quite complex and needs proper mapping of tblDG_TPL_Master hierarchy.
        # For simplification and given the context, let's assume `getTPLBOMRootnode` identifies a single item ID to exclude.
        # This part requires a clear definition of `getTPLBOMRootnode` in clsFunctions.
        # Placeholder: root_item_ids = TplMaster.objects.get_tpl_bom_root_node(child_id, wono, company_id, fin_year_id, 'tblDG_TPL_Master')
        # if root_item_ids: # Assuming it returns a list of IDs
        #     queryset = queryset.exclude(pk__in=root_item_ids)

        if assembly_no_id: # Exclude the assembly itself from being added
            queryset = queryset.exclude(pk=assembly_no_id)

        # Filter by Category and SubCategory
        if category_id and category_id != 'Select Category':
            queryset = queryset.filter(category_id=category_id)
            if subcategory_id and subcategory_id != 'Select SubCategory':
                queryset = queryset.filter(subcategory_id=subcategory_id)
        
        # Filter by Search Type and Term
        if search_type and search_type != 'Select' and search_term:
            if search_type == 'tblDG_Item_Master.ItemCode':
                queryset = queryset.filter(item_code__startswith=search_term)
            elif search_type == 'tblDG_Item_Master.ManfDesc':
                queryset = queryset.filter(manf_desc__icontains=search_term)
            elif search_type == 'tblDG_Item_Master.PurchDesc':
                queryset = queryset.filter(purch_desc__icontains=search_term)
        
        # Filter by Location (if DropDownList3 is visible and selected)
        if search_type == 'tblDG_Item_Master.Location' and location_id and location_id != 'Select':
            queryset = queryset.filter(location_id=location_id)

        # Standard company and financial year filters
        queryset = queryset.filter(comp_id=company_id, fin_year_id__lte=fin_year_id)
        
        # Exclude absolute items (Absolute = '1')
        queryset = queryset.exclude(absolute=1)

        # Order by Id for consistent pagination and display
        return queryset.order_by('id')

    def generate_item_code(self, category_id, part_no, revision, company_id, fin_year_id):
        """
        Mimics fun.createAssemblyCode - highly dependent on its internal logic.
        This is a placeholder, actual implementation will be needed based on `fun.createAssemblyCode` specifics.
        Assumes it combines category, part_no, revision in a specific format.
        """
        # Example dummy implementation:
        category_symbol = Category.objects.filter(pk=category_id).values_list('cname', flat=True).first() or 'CAT'
        return f"{category_symbol}-{part_no}-{revision:02d}"
    
    def get_item_code_limit(self, company_id, wo_length):
        """
        Mimics fun.ItemCodeLimit and fun.SetBomItemLimit.
        This is a placeholder, actual implementation depends on internal logic.
        """
        # Example dummy implementation:
        base_limit = 10 # Example default
        # The original code's logic is `partlimit = fun.SetBomItemLimit(abc,wlen);`
        # and `abc = fun.ItemCodeLimit(CompId);`. This suggests `ItemCodeLimit`
        # returns some base value, and `SetBomItemLimit` adjusts it based on WO length.
        # For simplicity, we'll return a fixed value or a value based on wo_length.
        return base_limit + wo_length % 5 # Just an example, actual logic needed.


class TplItemTempManager(models.Manager):
    def get_selected_items(self, wono, child_id, company_id):
        """
        Mimics FillDatagrid's initial query to fetch items from tblDG_TPLItem_Temp
        and enrich them with ItemMaster details if ItemId is present.
        """
        queryset = self.get_queryset().filter(wono=wono, child_id=child_id, comp_id=company_id).order_by('-id')
        
        results = []
        for temp_item in queryset:
            item_data = {
                'id': temp_item.id,
                'qty': temp_item.qty,
                'assly_qty': 0.0, # Will be calculated by TplMasterManager.get_assembly_recursive_qty
                'bom_qty': 0.0, # Will be calculated after assly_qty
                'item_code': '',
                'manf_desc': '',
                'purch_desc': '',
                'uom_basic_symbol': '',
                'uom_purchase_symbol': '',
            }

            if temp_item.item_id: # Item linked to existing Item Master
                try:
                    item_master = Item.objects.select_related('uom_basic', 'uom_purchase').get(
                        pk=temp_item.item_id, comp_id=company_id
                    )
                    item_data['item_code'] = item_master.item_code
                    item_data['manf_desc'] = item_master.manf_desc
                    item_data['purch_desc'] = item_master.purch_desc
                    item_data['uom_basic_symbol'] = item_master.uom_basic.symbol if item_master.uom_basic else ''
                    item_data['uom_purchase_symbol'] = item_master.uom_purchase.symbol if item_master.uom_purchase else ''
                except Item.DoesNotExist:
                    pass # Or log an error, item_id points to non-existent item
            else: # New item defined directly in temp table
                item_data['item_code'] = temp_item.item_code
                item_data['manf_desc'] = temp_item.manf_desc
                item_data['purch_desc'] = temp_item.purch_desc
                item_data['uom_basic_symbol'] = Unit.objects.filter(pk=temp_item.uom_basic_id).values_list('symbol', flat=True).first() or ''
                item_data['uom_purchase_symbol'] = Unit.objects.filter(pk=temp_item.uom_purchase_id).values_list('symbol', flat=True).first() or ''
            
            results.append(item_data)
        
        return results

    @transaction.atomic
    def add_existing_item(self, company_id, session_id, wono, item_id, quantity, child_id, weldments):
        """
        Adds an existing item from Item Master to tblDG_TPLItem_Temp.
        Mimics GridView2_RowCommand.
        """
        if quantity <= 0:
            raise ValueError("Required quantity must be greater than zero.")
        
        if self.filter(item_id=item_id, comp_id=company_id, session_id=session_id, wono=wono, child_id=child_id).exists():
            raise ValueError("Record already inserted for this item.")
        
        # Weldments logic needs to be sourced from tblDG_TPL_Master
        # This will need to be fetched from TplMaster table based on ItemId
        # For now, `weldments` parameter is passed, but it should be derived from system logic.
        
        self.create(
            comp_id=company_id,
            session_id=session_id,
            wono=wono,
            item_id=item_id,
            qty=quantity,
            child_id=child_id,
            weldments=weldments # This needs to be derived
        )

    @transaction.atomic
    def add_new_item(self, session_id, company_id, wono, category_id, child_id, part_no, revision, manf_desc, purch_desc, uom_basic_id, uom_purchase_id, quantity, wo_length):
        """
        Adds a newly defined item directly into tblDG_TPLItem_Temp.
        Mimics btnSubmit_Click logic.
        """
        if not quantity or quantity <= 0:
            raise ValueError("Required quantity must be greater than zero.")
        
        item_code_limit = Item.objects.get_item_code_limit(company_id, wo_length)
        if len(part_no) != item_code_limit:
            raise ValueError(f"Part number should be {item_code_limit} digit.")
        
        # Generate item code (placeholder)
        generated_item_code = Item.objects.generate_item_code(category_id, part_no, revision, company_id, timezone.now().year) # FinYearId is needed, assuming current year for placeholder
        
        if revision >= 10: # Original ASP.NET had `r < 10` check.
            raise ValueError("Revision number too high. Max 9 revisions allowed.")
        
        if self.filter(child_id=child_id, wono=wono, comp_id=company_id, item_code=generated_item_code).exists():
            raise ValueError("Record already inserted for this new item.")

        self.create(
            session_id=session_id,
            comp_id=company_id,
            wono=wono,
            category_id=category_id, # Storing category_id for new items
            child_id=child_id,
            part_no=part_no,
            revision=revision,
            item_code=generated_item_code,
            manf_desc=manf_desc,
            purch_desc=purch_desc,
            uom_basic_id=uom_basic_id,
            uom_purchase_id=uom_purchase_id,
            qty=quantity,
            weldments=0 # Default for new items, may need adjustment based on system logic
        )
    
    @transaction.atomic
    def delete_item(self, item_temp_id, session_id, company_id):
        """
        Deletes a specific item from tblDG_TPLItem_Temp.
        Mimics GridView4_RowCommand.
        """
        self.filter(pk=item_temp_id, session_id=session_id, comp_id=company_id).delete()

    @transaction.atomic
    def clear_temp_items(self, company_id, session_id, wono):
        """
        Clears all items for a given session, company, and WO from tblDG_TPLItem_Temp.
        Mimics clearTempDb.
        """
        self.filter(comp_id=company_id, session_id=session_id, wono=wono).delete()


class TplMasterManager(models.Manager):
    def get_next_tpl_child_id(self, wono, company_id, fin_year_id):
        """
        Mimics fun.getTPLCId. Gets the next CId for a TPL entry.
        Assumes CId is a sequential integer for a given WONo and CompId/FinYearId.
        """
        max_cid = self.filter(wono=wono, comp_id=company_id, fin_year_id__lte=fin_year_id).aggregate(models.Max('c_id'))['c_id__max']
        return (max_cid or 0) + 1
    
    def get_assembly_recursive_qty(self, wono, parent_id, child_id, default_qty, company_id, fin_year_id):
        """
        Mimics fun.RecurQty to calculate recursive assembly quantity.
        This is a recursive function, for Django, it should be implemented carefully to avoid N+1 queries.
        This is a placeholder, actual logic needs to trace BOM/TPL hierarchy.
        """
        # This is a very complex recursive logic from ERP systems.
        # A simplified placeholder is provided; the actual implementation would involve
        # traversing the TPL/BOM tree to calculate cumulative quantity.
        # This might require raw SQL or a dedicated recursive CTE for performance.
        
        # Example simplified recursive calculation (pseudo-code):
        # qty_for_current_node = self.filter(wono=wono, p_id=parent_id, c_id=child_id, comp_id=company_id, fin_year_id__lte=fin_year_id).values_list('qty', flat=True).first()
        # if not qty_for_current_node:
        #     return default_qty # Base case: no more parent-child link found
        #
        # grand_parent_id = self.filter(c_id=parent_id, wono=wono, comp_id=company_id, fin_year_id__lte=fin_year_id).values_list('p_id', flat=True).first()
        # if grand_parent_id is not None:
        #     return qty_for_current_node * self.get_assembly_recursive_qty(wono, grand_parent_id, parent_id, default_qty, company_id, fin_year_id)
        # else:
        #     return qty_for_current_node * default_qty # Top-level assembly
        
        # For now, return a simplified value. This is a critical business logic point.
        return default_qty # This is a placeholder for the complex recursive calculation.


class BomMasterManager(models.Manager):
    pass # No specific complex methods yet, but might inherit from TplMasterManager if logic overlaps.


# --- Utility Functions (outside managers, or in a separate utilities.py) ---
def get_opening_date(company_id, fin_year_id):
    """
    Mimics fun.getOpeningDate. Placeholder implementation.
    Returns a date string for opening balance.
    """
    # This would typically query a financial year master table to get the start date.
    return timezone.now().date().isoformat() # Example: current date

# --- Models Definitions ---

class Category(models.Model):
    c_id = models.IntegerField(db_column='CId', primary_key=True)
    c_name = models.CharField(db_column='CName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'

    def __str__(self):
        return self.c_name


class SubCategory(models.Model):
    sc_id = models.IntegerField(db_column='SCId', primary_key=True)
    sc_name = models.CharField(db_column='SCName', max_length=255)
    c_id = models.ForeignKey(Category, models.DO_NOTHING, db_column='CId')
    symbol = models.CharField(db_column='Symbol', max_length=50)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblDG_SubCategory_Master'
        verbose_name = 'Sub Category'
        verbose_name_plural = 'Sub Categories'

    def __str__(self):
        return f"{self.symbol} - {self.sc_name}"


class Unit(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol


class Location(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    location_label = models.CharField(db_column='LocationLabel', max_length=255)
    location_no = models.CharField(db_column='LocationNo', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblDG_Location_Master'
        verbose_name = 'Location'
        verbose_name_plural = 'Locations'

    def __str__(self):
        return f"{self.location_label} {self.location_no}"


class Item(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=255)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=500, blank=True, null=True)
    purch_desc = models.CharField(db_column='PurchDesc', max_length=500, blank=True, null=True)
    uom_basic = models.ForeignKey(Unit, models.DO_NOTHING, db_column='UOMBasic', related_name='items_basic', blank=True, null=True)
    uom_purchase = models.ForeignKey(Unit, models.DO_NOTHING, db_column='UOMPurchase', related_name='items_purchase', blank=True, null=True)
    location = models.ForeignKey(Location, models.DO_NOTHING, db_column='Location', blank=True, null=True)
    category = models.ForeignKey(Category, models.DO_NOTHING, db_column='CId', blank=True, null=True)
    subcategory = models.ForeignKey(SubCategory, models.DO_NOTHING, db_column='SCId', blank=True, null=True)
    stock_qty = models.DecimalField(db_column='StockQty', max_digits=18, decimal_places=3, blank=True, null=True)
    part_no = models.CharField(db_column='PartNo', max_length=255, blank=True, null=True)
    revision = models.IntegerField(db_column='Revision', blank=True, null=True)
    absolute = models.IntegerField(db_column='Absolute', default=0) # 0 or 1, like boolean
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    sys_date = models.DateField(db_column='SysDate', blank=True, null=True)
    sys_time = models.TimeField(db_column='SysTime', blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=255, blank=True, null=True)
    opening_bal_date = models.DateField(db_column='OpeningBalDate', blank=True, null=True)
    opening_bal_qty = models.DecimalField(db_column='OpeningBalQty', max_digits=18, decimal_places=3, default=0.0)

    objects = ItemManager()

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'
        unique_together = (('item_code', 'comp_id', 'fin_year_id'),) # Inferred unique constraint

    def __str__(self):
        return self.item_code

    def get_weldments_status(self):
        """
        Placeholder for fetching weldments status from TPL_Master if applicable.
        This would depend on the system's definition of 'weldments' for an Item.
        """
        # Example: return self.tpl_master_set.filter(is_weldment=True).exists()
        return False # Default value


class TplItemTemp(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    wono = models.CharField(db_column='WONo', max_length=255)
    item = models.ForeignKey(Item, models.DO_NOTHING, db_column='ItemId', blank=True, null=True) # Can be null if new item
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3)
    child_id = models.IntegerField(db_column='ChildId')
    comp_id = models.IntegerField(db_column='CompId')
    session_id = models.CharField(db_column='SessionId', max_length=255)
    
    # Fields for new items not yet in Item Master
    item_code = models.CharField(db_column='ItemCode', max_length=255, blank=True, null=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=500, blank=True, null=True)
    purch_desc = models.CharField(db_column='PurchDesc', max_length=500, blank=True, null=True)
    uom_basic_id = models.IntegerField(db_column='UOMBasic', blank=True, null=True) # Stored as ID not FK
    uom_purchase_id = models.IntegerField(db_column='UOMPurchase', blank=True, null=True) # Stored as ID not FK
    part_no = models.CharField(db_column='PartNo', max_length=255, blank=True, null=True)
    revision = models.IntegerField(db_column='Revision', blank=True, null=True)
    weldments = models.IntegerField(db_column='Weldments', default=0) # 0 or 1, like boolean
    category_id = models.IntegerField(db_column='CId', blank=True, null=True) # Storing category_id for new items

    objects = TplItemTempManager()

    class Meta:
        managed = False
        db_table = 'tblDG_TPLItem_Temp'
        verbose_name = 'Temporary TPL Item'
        verbose_name_plural = 'Temporary TPL Items'

    def __str__(self):
        return f"Temp Item for WO {self.wono}: {self.item.item_code if self.item else self.item_code}"


class TplMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    session_id = models.CharField(db_column='SessionId', max_length=255)
    wono = models.CharField(db_column='WONo', max_length=255)
    p_id = models.IntegerField(db_column='PId')
    c_id = models.IntegerField(db_column='CId')
    item = models.ForeignKey(Item, models.DO_NOTHING, db_column='ItemId')
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3)
    convert_to_bom = models.IntegerField(db_column='ConvertToBOM', default=0) # 0 or 1
    weldments = models.IntegerField(db_column='Weldments', default=0) # 0 or 1

    objects = TplMasterManager()

    class Meta:
        managed = False
        db_table = 'tblDG_TPL_Master'
        verbose_name = 'TPL Master'
        verbose_name_plural = 'TPL Master'

    def __str__(self):
        return f"TPL {self.wono} - {self.item.item_code}"


class BomMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    session_id = models.CharField(db_column='SessionId', max_length=255)
    wono = models.CharField(db_column='WONo', max_length=255)
    p_id = models.IntegerField(db_column='PId')
    c_id = models.IntegerField(db_column='CId')
    item = models.ForeignKey(Item, models.DO_NOTHING, db_column='ItemId')
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3)
    weldments = models.IntegerField(db_column='Weldments', default=0) # 0 or 1

    objects = BomMasterManager()

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'BOM Master'
        verbose_name_plural = 'BOM Master'

    def __str__(self):
        return f"BOM {self.wono} - {self.item.item_code}"


class WorkOrderMaster(models.Model):
    # Composite PK for WONo and CompId is assumed from original code's WHERE clause
    # It might be `id` or a composite. Assuming `WONo` and `CompId` make it unique.
    wono = models.CharField(db_column='WONo', primary_key=True, max_length=255) # Assuming WONo is part of composite PK or main lookup
    comp_id = models.IntegerField(db_column='CompId')
    update_wo = models.IntegerField(db_column='UpdateWO', default=0) # 0 or 1

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order Master'
        verbose_name_plural = 'Work Order Master'
        unique_together = (('wono', 'comp_id'),) # Explicitly define composite PK

    def __str__(self):
        return self.wono

    @classmethod
    @transaction.atomic
    def process_tpl_bom_from_temp(cls, wono, parent_id, child_id, comp_id, fin_year_id, session_id, convert_to_bom):
        """
        Centralized logic for AddToTPLBOM. Processes items from TplItemTemp
        into TPLMaster and optionally BOMMaster.
        """
        temp_items = TplItemTemp.objects.filter(
            comp_id=comp_id, session_id=session_id, wono=wono, child_id=child_id
        )

        for temp_item in temp_items:
            current_datetime = timezone.now()
            next_cid = TplMaster.objects.get_next_tpl_child_id(wono, comp_id, fin_year_id)

            item_to_link = None
            if temp_item.item: # Existing Item from Master
                item_to_link = temp_item.item
            else: # New Item
                # Check if item_code already exists in Item Master
                existing_master_item = Item.objects.filter(
                    item_code=temp_item.item_code, comp_id=comp_id, fin_year_id__lte=fin_year_id
                ).first()

                if not existing_master_item:
                    # Create new Item Master record
                    opening_bal_date = get_opening_date(comp_id, fin_year_id) # Call helper utility
                    item_to_link = Item.objects.create(
                        sys_date=current_datetime.date(),
                        sys_time=current_datetime.time(),
                        comp_id=comp_id,
                        fin_year_id=fin_year_id,
                        session_id=session_id,
                        category_id=temp_item.category_id,
                        part_no=temp_item.part_no,
                        revision=temp_item.revision,
                        item_code=temp_item.item_code,
                        manf_desc=temp_item.manf_desc,
                        purch_desc=temp_item.purch_desc,
                        uom_basic_id=temp_item.uom_basic_id,
                        uom_purchase_id=temp_item.uom_purchase_id,
                        opening_bal_date=opening_bal_date,
                        opening_bal_qty=0
                    )
                else:
                    item_to_link = existing_master_item
            
            if item_to_link:
                # Check if this TPL entry already exists
                tpl_exists = TplMaster.objects.filter(
                    comp_id=comp_id, fin_year_id__lte=fin_year_id, wono=wono,
                    p_id=child_id, item=item_to_link
                ).exists()

                if not tpl_exists:
                    # Insert into TPL_Master
                    TplMaster.objects.create(
                        sys_date=current_datetime.date(),
                        sys_time=current_datetime.time(),
                        comp_id=comp_id,
                        fin_year_id=fin_year_id,
                        session_id=session_id,
                        wono=wono,
                        p_id=child_id, # Original code used ChildId as PId for new entries here
                        c_id=next_cid, # New CId for this entry
                        item=item_to_link,
                        qty=temp_item.qty,
                        convert_to_bom=convert_to_bom,
                        weldments=temp_item.weldments # Or derive from item_to_link
                    )

                    if convert_to_bom == 1:
                        # Check if this BOM entry already exists (logic implies unique for BOM)
                        bom_exists = BomMaster.objects.filter(
                            comp_id=comp_id, fin_year_id__lte=fin_year_id, wono=wono,
                            p_id=child_id, item=item_to_link
                        ).exists()
                        if not bom_exists:
                            # Insert into BOM_Master
                            BomMaster.objects.create(
                                sys_date=current_datetime.date(),
                                sys_time=current_datetime.time(),
                                comp_id=comp_id,
                                fin_year_id=fin_year_id,
                                session_id=session_id,
                                wono=wono,
                                p_id=child_id, # Original code used ChildId as PId for new entries here
                                c_id=next_cid, # New CId for this entry
                                item=item_to_link,
                                qty=temp_item.qty,
                                weldments=temp_item.weldments # Or derive from item_to_link
                            )
                        # The original code updates `UpdateWO='1'` here
                        WorkOrderMaster.objects.filter(wono=wono, comp_id=comp_id).update(update_wo=1)

        # Clear temp table after successful processing
        TplItemTemp.objects.clear_temp_items(comp_id, session_id, wono)

```

#### 4.2 Forms (`work_orders/forms.py`)

Django forms for user input and filtering.

```python
from django import forms
from .models import Item, Category, SubCategory, Unit, Location, TplItemTemp

class ItemSearchForm(forms.Form):
    # Options for dropdowns (initially loaded empty or with defaults)
    CATEGORY_CHOICES = [('', 'Select Category')] + list(Category.objects.all().values_list('c_id', 'c_name'))
    SUB_CATEGORY_CHOICES = [('', 'Select SubCategory')] # Populated dynamically
    SEARCH_CODE_CHOICES = [
        ('Select', 'Select'),
        ('tblDG_Item_Master.ItemCode', 'Item Code'),
        ('tblDG_Item_Master.ManfDesc', 'Manuf. Description'),
        ('tblDG_Item_Master.PurchDesc', 'Purchase Description'),
        ('tblDG_Item_Master.Location', 'Location'),
    ]
    LOCATION_CHOICES = [('', 'Select')] # Populated dynamically

    category = forms.ChoiceField(
        choices=CATEGORY_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'box3',
            'hx-get': '/work_orders/items/filter-subcategories/',
            'hx-target': '#id_subcategory',
            'hx-trigger': 'change',
            'hx-include': '#id_category',
            'hx-swap': 'outerHTML',
            '_': 'on htmx:afterOnLoad add .is-active to #itemMasterTable-container' # Trigger refresh on successful swap
        })
    )
    subcategory = forms.ChoiceField(
        choices=SUB_CATEGORY_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'box3',
            'hx-get': '/work_orders/items/filter-items/',
            'hx-target': '#itemMasterTable-container',
            'hx-trigger': 'change delay:100ms',
            'hx-include': '#item_search_form',
            'hx-swap': 'innerHTML'
        })
    )
    search_code = forms.ChoiceField(
        choices=SEARCH_CODE_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'box3',
            'hx-on--change': 'Alpine.store("woItems").toggleSearchInput(event.target.value)', # Alpine.js for visibility
            'hx-get': '/work_orders/items/filter-items/',
            'hx-target': '#itemMasterTable-container',
            'hx-trigger': 'change delay:100ms',
            'hx-include': '#item_search_form',
            'hx-swap': 'innerHTML'
        })
    )
    search_item_code = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-[200px]',
            'placeholder': 'Search term...',
            'x-show': 'Alpine.store("woItems").showSearchText', # Alpine.js
            'hx-get': '/work_orders/items/filter-items/',
            'hx-target': '#itemMasterTable-container',
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-include': '#item_search_form',
            'hx-swap': 'innerHTML'
        })
    )
    location_dropdown = forms.ChoiceField(
        choices=LOCATION_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'box3 w-[155px]',
            'x-show': 'Alpine.store("woItems").showSearchLocation', # Alpine.js
            'hx-get': '/work_orders/items/filter-items/',
            'hx-target': '#itemMasterTable-container',
            'hx-trigger': 'change delay:100ms',
            'hx-include': '#item_search_form',
            'hx-swap': 'innerHTML'
        })
    )

    def __init__(self, *args, **kwargs):
        company_id = kwargs.pop('company_id', None)
        fin_year_id = kwargs.pop('fin_year_id', None)
        super().__init__(*args, **kwargs)

        # Dynamically populate categories
        self.fields['category'].choices = [('', 'Select Category')] + list(
            Category.objects.filter(comp_id=company_id, fin_year_id__lte=fin_year_id).values_list('c_id', 'c_name')
        )
        
        # Dynamically populate locations
        self.fields['location_dropdown'].choices = [('', 'Select')] + list(
            Location.objects.all().annotate(
                full_location_name=forms.functions.Concat(F('location_label'), models.Value(' '), F('location_no'))
            ).values_list('id', 'full_location_name')
        )

    def set_subcategories(self, category_id, company_id, fin_year_id):
        if category_id:
            self.fields['subcategory'].choices = [('', 'Select SubCategory')] + list(
                SubCategory.objects.filter(c_id=category_id, comp_id=company_id, fin_year_id__lte=fin_year_id).values_list('sc_id', forms.functions.Concat(F('symbol'), models.Value(' - '), F('sc_name')))
            )
        else:
            self.fields['subcategory'].choices = [('', 'Select SubCategory')]


class AddItemToTempForm(forms.Form):
    # This form is for the 'Add' button in GridView2
    item_id = forms.IntegerField(widget=forms.HiddenInput())
    quantity = forms.DecimalField(
        max_digits=18, decimal_places=3, min_value=0.001, # min_value for >0
        widget=forms.NumberInput(attrs={
            'class': 'box3 w-[60px]',
            'onblur': 'javascript:if(isNaN(this.value)==true){ this.value=""; }', # Replicated original JS
            'onfocus': 'javascript:if(isNaN(this.value)==true){ this.value=""; }',
            'onkeyup': 'javascript:if(isNaN(this.value)==true){ this.value=""; }'
        })
    )

    def clean_quantity(self):
        quantity = self.cleaned_data['quantity']
        # The regex validation `^\d{1,15}(\.\d{0,3})?$` is handled by DecimalField's max_digits/decimal_places
        # and min_value=0.001 for >0 check.
        if quantity <= 0:
            raise forms.ValidationError("Required quantity must be greater than zero.")
        return quantity


class NewItemCreateForm(forms.Form):
    CATEGORY_CHOICES = [('', 'Select Category')]
    UNIT_CHOICES = [('', 'Select')]

    category = forms.ChoiceField(
        choices=CATEGORY_CHOICES,
        required=True,
        widget=forms.Select(attrs={'class': 'box3'})
    )
    part_no = forms.CharField(
        max_length=255, # MaxLength is dynamic in original, handled in view
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-[160px]',
            'onblur': 'javascript:if(isNaN(this.value)==true){ this.value=""; }',
            'onfocus': 'javascript:if(isNaN(this.value)==true){ this.value=""; }',
            'onkeyup': 'javascript:if(isNaN(this.value)==true){ this.value=""; }'
        })
    )
    revision = forms.BooleanField(required=False, widget=forms.CheckboxInput())
    manf_description = forms.CharField(
        widget=forms.Textarea(attrs={'class': 'box3 h-[58px] w-[320px]'}),
        required=True
    )
    purch_description = forms.CharField(
        widget=forms.Textarea(attrs={'class': 'box3 h-[58px] w-[320px]'}),
        required=True
    )
    unit_basic = forms.ChoiceField(
        choices=UNIT_CHOICES,
        required=True,
        widget=forms.Select(attrs={'class': 'box3'})
    )
    unit_purchase = forms.ChoiceField(
        choices=UNIT_CHOICES,
        required=True,
        widget=forms.Select(attrs={'class': 'box3'})
    )
    quantity = forms.DecimalField(
        max_digits=18, decimal_places=3, min_value=0.001,
        required=True,
        widget=forms.NumberInput(attrs={
            'class': 'box3 w-[50px]',
            'onblur': 'javascript:if(isNaN(this.value)==true){ this.value=""; }',
            'onfocus': 'javascript:if(isNaN(this.value)==true){ this.value=""; }',
            'onkeyup': 'javascript:if(isNaN(this.value)==true){ this.value=""; }'
        })
    )

    def __init__(self, *args, **kwargs):
        company_id = kwargs.pop('company_id', None)
        fin_year_id = kwargs.pop('fin_year_id', None)
        super().__init__(*args, **kwargs)

        # Populate Category and Unit dropdowns
        self.fields['category'].choices = [('', 'Select Category')] + list(
            Category.objects.filter(comp_id=company_id, fin_year_id__lte=fin_year_id).values_list('c_id', 'c_name')
        )
        self.fields['unit_basic'].choices = [('', 'Select')] + list(
            Unit.objects.all().values_list('id', 'symbol')
        )
        self.fields['unit_purchase'].choices = [('', 'Select')] + list(
            Unit.objects.all().values_list('id', 'symbol')
        )

    def clean_quantity(self):
        quantity = self.cleaned_data['quantity']
        if quantity <= 0:
            raise forms.ValidationError("Required quantity must be greater than zero.")
        return quantity

    def clean_part_no(self):
        part_no = self.cleaned_data['part_no']
        # The max_length check based on wono length is done in the view/manager as it's dynamic
        return part_no

```

#### 4.3 Views (`work_orders/views.py`)

Django Class-Based Views for handling each interaction, keeping them thin by delegating complex logic to model managers.

```python
from django.views.generic import TemplateView, View, ListView, CreateView, DeleteView
from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db import transaction
from django.db.models import Max
import json # For JSON responses if needed

from .models import Item, Category, SubCategory, Unit, Location, TplItemTemp, TplMaster, WorkOrderMaster
from .forms import ItemSearchForm, AddItemToTempForm, NewItemCreateForm
from .models import get_opening_date # Import the helper utility

# --- Helper function for session/query string parameters ---
# In a real app, this could be a mixin or middleware for cleaner access
def get_wo_params(request):
    """Extracts common parameters from session and query string."""
    wono = request.GET.get('WONo', '')
    assembly_no_id = request.GET.get('ItemId', '') # This is the ID of the assembly item
    parent_id = request.GET.get('PId', None)
    child_id = request.GET.get('CId', None)
    
    # Safely convert to int, default to 0 or None if not present/invalid
    parent_id = int(parent_id) if parent_id and parent_id.isdigit() else 0
    child_id = int(child_id) if child_id and child_id.isdigit() else 0
    assembly_no_id = int(assembly_no_id) if assembly_no_id and assembly_no_id.isdigit() else None

    # Assuming these are available in session from login/context
    comp_id = request.session.get('compid', 1) # Default to 1 for example
    fin_year_id = request.session.get('finyear', timezone.now().year) # Default to current year for example
    session_id = request.session.get('username', 'default_user')

    return wono, assembly_no_id, parent_id, child_id, comp_id, fin_year_id, session_id

# --- Main Dashboard View ---

class WoItemsDashboardView(TemplateView):
    """
    Renders the main dashboard with tabs. Initial data for the first tab is also prepared.
    """
    template_name = 'work_orders/woitems_dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        wono, assembly_no_id, parent_id, child_id, comp_id, fin_year_id, session_id = get_wo_params(self.request)

        context['wono'] = wono
        context['assembly_item_code'] = ''
        if assembly_no_id:
            try:
                context['assembly_item_code'] = Item.objects.get(pk=assembly_no_id, comp_id=comp_id, fin_year_id__lte=fin_year_id).item_code
            except Item.DoesNotExist:
                pass # Handle if assembly item not found

        context['parent_id'] = parent_id
        context['child_id'] = child_id
        context['comp_id'] = comp_id
        context['fin_year_id'] = fin_year_id
        context['session_id'] = session_id

        # Pass initial form for Item Master tab
        context['item_search_form'] = ItemSearchForm(
            company_id=comp_id, 
            fin_year_id=fin_year_id,
            # Initial values for dropdowns if needed
            initial={'category': self.request.GET.get('category', ''), 
                     'subcategory': self.request.GET.get('subcategory', ''),
                     'search_code': self.request.GET.get('search_code', 'Select'),
                     'search_item_code': self.request.GET.get('search_item_code', ''),
                     'location_dropdown': self.request.GET.get('location_dropdown', '')}
        )
        
        # Populate subcategories if category is selected initially
        if self.request.GET.get('category'):
            context['item_search_form'].set_subcategories(
                self.request.GET.get('category'), comp_id, fin_year_id
            )

        # For "New Items" tab form
        context['new_item_form'] = NewItemCreateForm(company_id=comp_id, fin_year_id=fin_year_id)
        context['part_no_max_length'] = Item.objects.get_item_code_limit(comp_id, len(wono)) # Dynamic max length

        return context

# --- HTMX Partial Views for Tabs ---

class ItemMasterTableView(ListView):
    """
    Renders the item master table (GridView2 equivalent) for the Item Master tab.
    Handles filtering and search.
    """
    model = Item
    template_name = 'work_orders/partials/_item_master_table.html'
    context_object_name = 'items'
    paginate_by = 12 # Equivalent to PageSize="12"

    def get_queryset(self):
        wono, assembly_no_id, parent_id, child_id, comp_id, fin_year_id, session_id = get_wo_params(self.request)
        
        form = ItemSearchForm(self.request.GET, company_id=comp_id, fin_year_id=fin_year_id)
        form.set_subcategories(self.request.GET.get('category'), comp_id, fin_year_id) # Ensure subcategories are set for rendering form
        
        category_id = self.request.GET.get('category')
        subcategory_id = self.request.GET.get('subcategory')
        search_type = self.request.GET.get('search_code')
        search_term = self.request.GET.get('search_item_code')
        location_id = self.request.GET.get('location_dropdown')

        # Delegate filtering logic to ItemManager
        queryset = Item.objects.get_filtered_items(
            category_id, subcategory_id, search_type, search_term, location_id,
            comp_id, fin_year_id, assembly_no_id, child_id, wono
        )
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        wono, assembly_no_id, parent_id, child_id, comp_id, fin_year_id, session_id = get_wo_params(self.request)
        context['add_item_form'] = AddItemToTempForm() # Form for adding quantity
        context['wono'] = wono
        context['child_id'] = child_id
        context['assembly_no_id'] = assembly_no_id
        context['comp_id'] = comp_id
        context['fin_year_id'] = fin_year_id
        context['session_id'] = session_id
        return context

# For populating subcategories on change, to avoid re-rendering the whole form
class FilterSubcategoriesView(View):
    def get(self, request, *args, **kwargs):
        wono, assembly_no_id, parent_id, child_id, comp_id, fin_year_id, session_id = get_wo_params(self.request)
        
        form = ItemSearchForm(request.GET, company_id=comp_id, fin_year_id=fin_year_id)
        category_id = request.GET.get('category')
        form.set_subcategories(category_id, comp_id, fin_year_id)
        
        # Render only the subcategory field
        return render(request, 'work_orders/partials/_subcategory_field.html', {'form': form})


class AddItemToTempView(View):
    """
    Handles adding an existing item to the temporary list.
    Mimics GridView2_RowCommand "Add" logic.
    """
    def post(self, request, *args, **kwargs):
        wono, assembly_no_id, parent_id, child_id, comp_id, fin_year_id, session_id = get_wo_params(request)
        
        form = AddItemToTempForm(request.POST)
        if form.is_valid():
            item_id = form.cleaned_data['item_id']
            quantity = form.cleaned_data['quantity']
            
            # Placeholder for `Weldments` status, needs to be derived from item properties or TPL Master
            # For simplicity, default to 0 for now.
            weldments = 0 
            
            try:
                TplItemTemp.objects.add_existing_item(
                    comp_id, session_id, wono, item_id, quantity, child_id, weldments
                )
                messages.success(request, 'Item added to temporary list successfully.')
                # HTMX trigger to refresh the 'Selected Items' table
                return HttpResponse(status=204, headers={'HX-Trigger': 'refreshSelectedItemsList'})
            except ValueError as e:
                messages.error(request, str(e))
                # Return empty 204 or specific HTMX response to display error
                return HttpResponse(status=204, headers={'HX-Trigger': json.dumps({"showMessage": {"type": "error", "message": str(e)}})})
        else:
            # If form is not valid, extract errors and display them
            errors = form.errors.as_json()
            messages.error(request, f"Validation error: {errors}")
            return HttpResponse(status=204, headers={'HX-Trigger': json.dumps({"showMessage": {"type": "error", "message": errors}})})


class NewItemFormView(TemplateView):
    """
    Renders and handles submission for the "New Items" form.
    Mimics btnSubmit_Click logic.
    """
    template_name = 'work_orders/partials/_new_item_form.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        wono, assembly_no_id, parent_id, child_id, comp_id, fin_year_id, session_id = get_wo_params(self.request)
        
        context['form'] = NewItemCreateForm(company_id=comp_id, fin_year_id=fin_year_id)
        context['part_no_max_length'] = Item.objects.get_item_code_limit(comp_id, len(wono))
        context['wono'] = wono # Pass wono for redirect after processing

        return context

    def post(self, request, *args, **kwargs):
        wono, assembly_no_id, parent_id, child_id, comp_id, fin_year_id, session_id = get_wo_params(request)
        
        form = NewItemCreateForm(request.POST, company_id=comp_id, fin_year_id=fin_year_id)
        if form.is_valid():
            category_id = form.cleaned_data['category']
            part_no = form.cleaned_data['part_no']
            revision = 1 if form.cleaned_data['revision'] else 0 # 0 if not checked, else increment
            manf_desc = form.cleaned_data['manf_description']
            purch_desc = form.cleaned_data['purch_description']
            uom_basic_id = form.cleaned_data['unit_basic']
            uom_purchase_id = form.cleaned_data['unit_purchase']
            quantity = form.cleaned_data['quantity']
            
            # Get revision: In original, it queries for existing part_no and increments revision.
            # This logic must reside in the ItemManager or a helper.
            # Here, we'll re-implement the original ASP.NET logic.
            current_revision = 0
            if form.cleaned_data['revision']: # CKRevision.Checked == true
                # Query tblDG_Item_Master for max revision for this part_no/category
                # This needs to be done carefully to match existing ItemCodes
                try:
                    latest_item = Item.objects.filter(
                        comp_id=comp_id, 
                        fin_year_id__lte=fin_year_id, 
                        category_id=category_id, 
                        part_no=wono + part_no # Original used wono+txtPartNo
                    ).order_by('-revision').first()
                    if latest_item:
                        current_revision = latest_item.revision + 1
                    else:
                        current_revision = 0 # First revision
                except Exception:
                    current_revision = 0 # No previous revision found
            revision_to_use = current_revision

            try:
                TplItemTemp.objects.add_new_item(
                    session_id, comp_id, wono, category_id, child_id,
                    part_no, revision_to_use, manf_desc, purch_desc,
                    uom_basic_id, uom_purchase_id, quantity, len(wono)
                )
                messages.success(request, 'New item added to temporary list successfully.')
                # Clear form fields after successful submission
                form = NewItemCreateForm(company_id=comp_id, fin_year_id=fin_year_id)
                # HTMX trigger to refresh 'Selected Items' table
                return HttpResponse(status=200, headers={'HX-Trigger': 'refreshSelectedItemsList'}) # Return 200 to swap new form
            except ValueError as e:
                messages.error(request, str(e))
                # Re-render form with errors if any, use 200 to swap new form with errors
                return render(request, self.template_name, {'form': form, 'part_no_max_length': Item.objects.get_item_code_limit(comp_id, len(wono))})
        else:
            messages.error(request, f"Please correct the errors in the form: {form.errors.as_text()}")
            # Re-render form with errors
            return render(request, self.template_name, {'form': form, 'part_no_max_length': Item.objects.get_item_code_limit(comp_id, len(wono))})


class SelectedItemsTableView(TemplateView):
    """
    Renders the selected items table (GridView4 equivalent) for the Selected Items tab.
    """
    template_name = 'work_orders/partials/_selected_items_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        wono, assembly_no_id, parent_id, child_id, comp_id, fin_year_id, session_id = get_wo_params(self.request)
        
        temp_items_data = TplItemTemp.objects.get_selected_items(wono, child_id, comp_id)
        
        # Calculate AsslyQty and BOMQty for each item based on parent/child hierarchy
        # This requires TplMasterManager.get_assembly_recursive_qty
        
        assly_new_qty = TplMaster.objects.get_assembly_recursive_qty(wono, parent_id, child_id, 1, comp_id, fin_year_id)
        
        for item_data in temp_items_data:
            item_data['assly_qty'] = float(f"{assly_new_qty:.3f}") # Format to 3 decimal places
            item_data['bom_qty'] = float(f"{(assly_new_qty * float(item_data['qty'])):.3f}") # Format to 3 decimal places

        context['selected_items'] = temp_items_data
        context['wono'] = wono
        context['parent_id'] = parent_id
        context['child_id'] = child_id
        context['comp_id'] = comp_id
        context['fin_year_id'] = fin_year_id
        context['session_id'] = session_id
        return context


class DeleteTplItemTempView(DeleteView):
    """
    Handles deletion of a temporary TPL item.
    Mimics GridView4_RowCommand "del" logic.
    """
    model = TplItemTemp
    http_method_names = ['post'] # Only allow POST for deletion via HTMX
    
    def post(self, request, *args, **kwargs):
        wono, assembly_no_id, parent_id, child_id, comp_id, fin_year_id, session_id = get_wo_params(request)
        item_temp_id = self.kwargs['pk'] # Get PK from URL

        try:
            TplItemTemp.objects.delete_item(item_temp_id, session_id, comp_id)
            messages.success(request, 'Temporary item deleted successfully.')
            # HTMX trigger to refresh the 'Selected Items' table
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshSelectedItemsList'})
        except Exception as e:
            messages.error(request, f"Error deleting item: {str(e)}")
            return HttpResponse(status=204, headers={'HX-Trigger': json.dumps({"showMessage": {"type": "error", "message": str(e)}})})


class ProcessTPLBOMView(View):
    """
    Handles "Add To TPL" and "Add To TPL & BOM" operations.
    Mimics btnproceed_Click and Button1_Click logic.
    """
    def post(self, request, *args, **kwargs):
        wono, assembly_no_id, parent_id, child_id, comp_id, fin_year_id, session_id = get_wo_params(request)
        
        convert_to_bom = int(request.POST.get('convert_to_bom', 0)) # 0 for TPL, 1 for TPL & BOM
        
        try:
            WorkOrderMaster.process_tpl_bom_from_temp(
                wono, parent_id, child_id, comp_id, fin_year_id, session_id, convert_to_bom
            )
            messages.success(request, 'Items processed to TPL/BOM successfully.')
            # Redirect to WO Tree View as in original ASP.NET
            return HttpResponse(status=204, headers={
                'HX-Redirect': reverse_lazy('work_orders_tree_view', kwargs={'wono': wono, 'mod_id': 3, 'sub_mod_id': 23})
            })
        except Exception as e:
            messages.error(request, f"Error processing items: {str(e)}")
            return HttpResponse(status=204, headers={'HX-Trigger': json.dumps({"showMessage": {"type": "error", "message": str(e)}})})


# --- View for the "Copy From" tab (Placeholder) ---
class CopyFromView(TemplateView):
    """
    Placeholder for the "Copy From" tab content.
    Original used an iframe, which will be a link to a separate Django view.
    """
    template_name = 'work_orders/partials/_copy_from_tab.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        wono, assembly_no_id, parent_id, child_id, comp_id, fin_year_id, session_id = get_wo_params(self.request)
        context['copy_wo_url'] = reverse_lazy('tpl_design_copy_wo', kwargs={
            'wono_dest': wono, 'dest_p_id': parent_id, 'dest_c_id': child_id
        })
        return context

# Mock view for TPL_Design_WO_TreeView (for HX-Redirect)
class WorkOrdersTreeView(TemplateView):
    template_name = 'work_orders/wo_tree_view.html' # Dummy template
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['wono'] = kwargs.get('wono')
        context['mod_id'] = kwargs.get('mod_id')
        context['sub_mod_id'] = kwargs.get('sub_mod_id')
        return context

# Mock view for TPL_Design_CopyWo.aspx (for iframe replacement)
class TplDesignCopyWoView(TemplateView):
    template_name = 'work_orders/tpl_design_copy_wo.html' # Dummy template
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['wono_dest'] = kwargs.get('wono_dest')
        context['dest_p_id'] = kwargs.get('dest_p_id')
        context['dest_c_id'] = kwargs.get('dest_c_id')
        return context

```

#### 4.4 Templates (`work_orders/templates/work_orders/`)

HTML templates for the dashboard and HTMX partials for dynamic content.

**`work_orders/woitems_dashboard.html`** (Main Page)

```html
{% extends 'core/base.html' %}

{% block title %}TPL Items{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ activeTab: 'itemMaster' }">
    <div class="bg-blue-600 text-white p-3 rounded-t-lg mb-4 flex justify-between items-center">
        <h1 class="text-xl font-bold">TPL Items</h1>
        <div class="text-sm">
            <span class="font-bold">WoNo:</span> <span id="lblwono">{{ wono }}</span> &nbsp;&nbsp;&nbsp;
            <span class="font-bold">Assly No:</span> <span id="lblasslyno">{{ assembly_item_code }}</span>
        </div>
    </div>

    <div class="flex border-b border-gray-200">
        <button @click="activeTab = 'itemMaster'"
                :class="{ 'bg-blue-500 text-white': activeTab === 'itemMaster', 'bg-gray-200 text-gray-700 hover:bg-gray-300': activeTab !== 'itemMaster' }"
                class="px-4 py-2 text-sm font-medium rounded-t-md transition-colors duration-200"
                hx-get="{% url 'work_orders:item_master_table' %}?{{ request.GET.urlencode }}"
                hx-target="#tabContent" hx-swap="innerHTML" hx-indicator="#tabLoader"
                x-init="activeTab === 'itemMaster' && $nextTick(() => { $el.click() })">
            Item Master
        </button>
        <button @click="activeTab = 'newItems'"
                :class="{ 'bg-blue-500 text-white': activeTab === 'newItems', 'bg-gray-200 text-gray-700 hover:bg-gray-300': activeTab !== 'newItems' }"
                class="px-4 py-2 text-sm font-medium rounded-t-md transition-colors duration-200"
                hx-get="{% url 'work_orders:new_item_form' %}?{{ request.GET.urlencode }}"
                hx-target="#tabContent" hx-swap="innerHTML" hx-indicator="#tabLoader">
            New Items
        </button>
        <button @click="activeTab = 'copyFrom'"
                :class="{ 'bg-blue-500 text-white': activeTab === 'copyFrom', 'bg-gray-200 text-gray-700 hover:bg-gray-300': activeTab !== 'copyFrom' }"
                class="px-4 py-2 text-sm font-medium rounded-t-md transition-colors duration-200"
                hx-get="{% url 'work_orders:copy_from_tab' %}?{{ request.GET.urlencode }}"
                hx-target="#tabContent" hx-swap="innerHTML" hx-indicator="#tabLoader">
            Copy From
        </button>
        <button @click="activeTab = 'selectedItems'"
                :class="{ 'bg-blue-500 text-white': activeTab === 'selectedItems', 'bg-gray-200 text-gray-700 hover:bg-gray-300': activeTab !== 'selectedItems' }"
                class="px-4 py-2 text-sm font-medium rounded-t-md transition-colors duration-200"
                hx-get="{% url 'work_orders:selected_items_table' %}?{{ request.GET.urlencode }}"
                hx-target="#tabContent" hx-swap="innerHTML" hx-indicator="#tabLoader">
            Selected Items
        </button>
    </div>

    <div id="tabContent" class="bg-white p-6 shadow-md rounded-b-lg min-h-[434px] relative">
        <!-- Tab content will be loaded here via HTMX -->
        <div id="tabLoader" class="htmx-indicator absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-75 z-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="ml-2 text-gray-700">Loading tab content...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.tailwindcss.min.js"></script>
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.store('woItems', {
            showSearchText: true,
            showSearchLocation: false,
            toggleSearchInput(selectedValue) {
                if (selectedValue === 'tblDG_Item_Master.Location') {
                    this.showSearchText = false;
                    this.showSearchLocation = true;
                } else {
                    this.showSearchText = true;
                    this.showSearchLocation = false;
                }
            },
            // For general messages
            showMessages(type, message) {
                const messageContainer = document.getElementById('messageContainer');
                if (messageContainer) {
                    messageContainer.innerHTML = `
                        <div x-data="{ show: true }" x-init="setTimeout(() => show = false, 5000)" x-show="show"
                             class="mb-4 p-3 rounded-md text-sm {% if type == 'success' %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}"
                             role="alert">
                            {{ message }}
                        </div>
                    `;
                }
            }
        });
    });

    // Custom HTMX listener for global messages
    document.body.addEventListener('htmx:trigger', function(event) {
        if (event.detail.triggerName === 'showMessage') {
            Alpine.store('woItems').showMessages(event.detail.value.type, event.detail.value.message);
        }
    });
</script>
{% endblock %}
```

**`work_orders/partials/_item_master_table.html`** (Item Master Tab Content)

```html
<div class="p-4" x-data="{ woItems: Alpine.store('woItems') }">
    <form id="item_search_form" class="mb-4 flex flex-wrap items-center space-x-2">
        {% csrf_token %}
        <div class="mb-2">{{ item_search_form.category }}</div>
        <div class="mb-2" id="id_subcategory_container">{{ item_search_form.subcategory }}</div> {# Target for HTMX subcategory swap #}
        <div class="mb-2">{{ item_search_form.search_code }}</div>
        <div class="mb-2">{{ item_search_form.search_item_code }}</div>
        <div class="mb-2">{{ item_search_form.location_dropdown }}</div>
        <button type="button" class="redbox bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                hx-get="{% url 'work_orders:item_master_table' %}?{{ request.GET.urlencode }}"
                hx-target="#itemMasterTable-container"
                hx-include="#item_search_form"
                hx-swap="innerHTML"
                hx-indicator="#tabLoader">
            Search
        </button>
        <button type="button" class="redbox bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                onclick="window.location.href='{% url 'work_orders:wo_items_dashboard' %}?WONo={{ wono }}&PId={{ parent_id }}&CId={{ child_id }}&ItemId={{ assembly_no_id }}'">
            Cancel
        </button>
    </form>

    <div id="itemMasterTable-container" class="overflow-x-auto">
        <table id="itemMasterTable" class="min-w-full bg-white fontcss yui-datatable-theme">
            <thead>
                <tr>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Req Qty</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Manf Desc</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Purch Desc</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                </tr>
            </thead>
            <tbody>
                {% for item in items %}
                <tr>
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-right">
                        <form hx-post="{% url 'work_orders:add_item_to_temp' %}?WONo={{ wono }}&PId={{ parent_id }}&CId={{ child_id }}&ItemId={{ assembly_no_id }}"
                              hx-trigger="submit" hx-swap="none"
                              onsubmit="return confirmationAdd()">
                            {% csrf_token %}
                            {{ add_item_form.item_id.as_hidden }} {# Hidden field for item_id #}
                            <input type="hidden" name="item_id" value="{{ item.id }}">
                            {{ add_item_form.quantity }}
                            <button type="submit" class="redbox bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded ml-2">Add</button>
                        </form>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.item_code }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left">{{ item.manf_desc }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.uom_basic.symbol }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left">{{ item.purch_desc }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.uom_purchase.symbol }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left">{{ item.location }}</td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="9" class="py-4 px-4 text-center text-lg font-bold text-maroon">No data to display !</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<script>
    // DataTables initialization
    $(document).ready(function() {
        $('#itemMasterTable').DataTable({
            "pageLength": 12,
            "lengthMenu": [[12, 25, 50, -1], [12, 25, 50, "All"]],
            "searching": false, // Disable default search as we have custom filters
            "paging": true,
            "info": true
        });
    });

    // JavaScript confirmation function (replicated from original ASP.NET)
    function confirmationAdd() {
        return confirm("Are you sure you want to add this item?");
    }
</script>
```

**`work_orders/partials/_subcategory_field.html`** (Partial for Subcategory Dropdown)

```html
<div class="mb-2" id="id_subcategory_container">
    {{ form.subcategory }}
</div>
```

**`work_orders/partials/_new_item_form.html`** (New Items Tab Content)

```html
<div class="p-4">
    <form hx-post="{% url 'work_orders:new_item_form' %}?{{ request.GET.urlencode }}" 
          hx-swap="outerHTML" hx-target="#tabContent"
          hx-indicator="#tabLoader">
        {% csrf_token %}
        <table cellpadding="0" cellspacing="0" class="w-full max-w-lg">
            <tr>
                <td class="style25 text-left align-middle pr-4" style="height: 30px;">Category</td>
                <td class="style34 text-left align-middle" style="height: 30px;">
                    {{ form.category }}
                    {% if form.category.errors %}<p class="text-red-500 text-xs mt-1">{{ form.category.errors }}</p>{% endif %}
                </td>
            </tr>
            <tr valign="middle">
                <td class="style25 text-left align-middle pr-4" style="height: 30px;">Part No</td>
                <td class="style42 text-left align-middle" style="height: 30px;">
                    <span id="lblWo" class="mr-1">{{ wono }}</span>
                    {{ form.part_no }}
                    <span class="ml-1 text-gray-500 text-sm">({{ part_no_max_length }} Digits)</span>
                    {% if form.part_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.part_no.errors }}</p>{% endif %}
                </td>
            </tr>
            <tr>
                <td class="style26 text-left align-middle pr-4" style="height: 30px;">Revision</td>
                <td class="fontcss text-left align-middle" style="height: 30px;">
                    {{ form.revision }}
                    {% if form.revision.errors %}<p class="text-red-500 text-xs mt-1">{{ form.revision.errors }}</p>{% endif %}
                </td>
            </tr>
            <tr>
                <td class="style26 text-left align-top pr-4">Manf Desc</td>
                <td class="fontcss text-left align-middle">
                    {{ form.manf_description }}
                    {% if form.manf_description.errors %}<p class="text-red-500 text-xs mt-1">{{ form.manf_description.errors }}</p>{% endif %}
                </td>
            </tr>
            <tr>
                <td class="style26 text-left align-top pr-4">Purchase Desc</td>
                <td class="fontcss text-left align-middle">
                    {{ form.purch_description }}
                    {% if form.purch_description.errors %}<p class="text-red-500 text-xs mt-1">{{ form.purch_description.errors }}</p>{% endif %}
                </td>
            </tr>
            <tr>
                <td class="style26 text-left align-middle pr-4" style="height: 28px;">Unit Basic</td>
                <td class="style22 text-left align-middle" style="height: 28px;">
                    {{ form.unit_basic }}
                    {% if form.unit_basic.errors %}<p class="text-red-500 text-xs mt-1">{{ form.unit_basic.errors }}</p>{% endif %}
                </td>
            </tr>
            <tr>
                <td class="style27 text-left align-middle pr-4" style="height: 19px;">Unit Purchase</td>
                <td class="style24 text-left align-bottom" style="height: 19px;">
                    {{ form.unit_purchase }}
                    {% if form.unit_purchase.errors %}<p class="text-red-500 text-xs mt-1">{{ form.unit_purchase.errors }}</p>{% endif %}
                </td>
            </tr>
            <tr>
                <td class="style28 text-left align-middle pr-4" style="height: 30px;">Required Qty</td>
                <td class="style23 text-left align-middle" style="height: 30px;">
                    {{ form.quantity }}
                    {% if form.quantity.errors %}<p class="text-red-500 text-xs mt-1">{{ form.quantity.errors }}</p>{% endif %}
                </td>
            </tr>
            <tr>
                <td class="style35 text-left align-top pr-4"></td>
                <td class="style36 text-left align-middle" style="height: 30px;">
                    <button type="submit" class="redbox bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                            onclick="return confirmationAdd()">
                        Add
                    </button>
                    <button type="button" class="redbox bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded ml-2"
                            onclick="window.location.href='{% url 'work_orders:wo_tree_view' wono=wono mod_id=3 sub_mod_id=23 %}'">
                        Cancel
                    </button>
                    <span id="lblMsg1" class="text-red-500 font-bold ml-2"></span>
                    <span id="lblMsg" class="text-red-500 font-bold ml-2"></span>
                </td>
            </tr>
        </table>
    </form>
</div>

<script>
    function confirmationAdd() {
        return confirm("Are you sure you want to add this item?");
    }
</script>
```

**`work_orders/partials/_copy_from_tab.html`** (Copy From Tab Content)

```html
<div class="p-4">
    <table class="w-full">
        <tr>
            <td>
                <!-- Replaced iframe content with a link or embed of Django view -->
                <div class="border border-gray-300 bg-gray-50 p-4 rounded-md min-h-[415px] flex items-center justify-center">
                    <p class="text-gray-600">This section would contain the content for "TPL_Design_CopyWo.aspx".</p>
                    <a href="{{ copy_wo_url }}" target="_blank" class="text-blue-600 hover:underline ml-2">Open Copy WO Page (New Tab)</a>
                </div>
            </td>
        </tr>
        <tr>
            <td align="right" class="mt-4">
                <b>
                    <button type="button" class="redbox bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                            onclick="window.location.href='{% url 'work_orders:wo_tree_view' wono=request.GET.WONo mod_id=3 sub_mod_id=23 %}'">
                        Cancel
                    </button>
                </b>
            </td>
        </tr>
    </table>
</div>
```

**`work_orders/partials/_selected_items_table.html`** (Selected Items Tab Content)

```html
<div class="p-4" x-data="{ woItems: Alpine.store('woItems') }">
    <div class="overflow-x-auto">
        <table id="selectedItemsTable" class="min-w-full bg-white fontcss yui-datatable-theme">
            <thead>
                <tr>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Manf Desc</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Purch Desc</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Assly Qty</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Req Qty</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">BOM Qty</th>
                </tr>
            </thead>
            <tbody>
                {% for item in selected_items %}
                <tr>
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">
                        <button class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                                hx-post="{% url 'work_orders:delete_tpl_item_temp' pk=item.id %}?WONo={{ wono }}&PId={{ parent_id }}&CId={{ child_id }}&ItemId={{ assembly_no_id }}"
                                hx-swap="none"
                                onclick="return confirmationDelete()">
                            Delete
                        </button>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.item_code }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left">{{ item.manf_desc }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.uom_basic_symbol }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left">{{ item.purch_desc }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.uom_purchase_symbol }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.assly_qty }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.qty }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.bom_qty }}</td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="10" class="py-4 px-4 text-center text-lg font-bold text-maroon">No data to display !</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    <div class="mt-4 flex justify-end space-x-2">
        <button type="button" class="redbox bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
                hx-post="{% url 'work_orders:process_tpl_bom' %}?WONo={{ wono }}&PId={{ parent_id }}&CId={{ child_id }}&ItemId={{ assembly_no_id }}"
                hx-vals='{"convert_to_bom": 0}' hx-swap="none"
                onclick="return confirmationAdd()">
            Add To TPL
        </button>
        <button type="button" class="redbox bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded"
                hx-post="{% url 'work_orders:process_tpl_bom' %}?WONo={{ wono }}&PId={{ parent_id }}&CId={{ child_id }}&ItemId={{ assembly_no_id }}"
                hx-vals='{"convert_to_bom": 1}' hx-swap="none"
                onclick="return confirmationAdd()">
            Add To TPL &amp; BOM
        </button>
        <button type="button" class="redbox bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                onclick="window.location.href='{% url 'work_orders:wo_tree_view' wono=wono mod_id=3 sub_mod_id=23 %}'">
            Cancel
        </button>
    </div>
</div>

<script>
    // DataTables initialization
    $(document).ready(function() {
        $('#selectedItemsTable').DataTable({
            "pageLength": 12,
            "lengthMenu": [[12, 25, 50, -1], [12, 25, 50, "All"]],
            "searching": false,
            "paging": true,
            "info": true
        });
    });

    // JavaScript confirmation function (replicated from original ASP.NET)
    function confirmationDelete() {
        return confirm("Are you sure you want to delete this item?");
    }
    function confirmationAdd() {
        return confirm("Are you sure you want to proceed?");
    }
</script>
```

#### 4.5 URLs (`work_orders/urls.py`)

Define URL patterns for the views and HTMX partials.

```python
from django.urls import path
from .views import (
    WoItemsDashboardView, ItemMasterTableView, FilterSubcategoriesView,
    AddItemToTempView, NewItemFormView, SelectedItemsTableView,
    DeleteTplItemTempView, ProcessTPLBOMView, CopyFromView,
    WorkOrdersTreeView, TplDesignCopyWoView # Mock views for redirection/iframe
)

app_name = 'work_orders'

urlpatterns = [
    # Main dashboard view
    path('woitems/', WoItemsDashboardView.as_view(), name='wo_items_dashboard'),

    # HTMX partials for tabs
    path('woitems/item-master-table/', ItemMasterTableView.as_view(), name='item_master_table'),
    path('woitems/new-item-form/', NewItemFormView.as_view(), name='new_item_form'),
    path('woitems/selected-items-table/', SelectedItemsTableView.as_view(), name='selected_items_table'),
    path('woitems/copy-from/', CopyFromView.as_view(), name='copy_from_tab'),

    # Specific actions for Item Master Tab
    path('woitems/filter-subcategories/', FilterSubcategoriesView.as_view(), name='filter_subcategories'),
    path('woitems/add-item-to-temp/', AddItemToTempView.as_view(), name='add_item_to_temp'),

    # Actions for Selected Items Tab
    path('woitems/delete-temp-item/<int:pk>/', DeleteTplItemTempView.as_view(), name='delete_tpl_item_temp'),
    path('woitems/process-tpl-bom/', ProcessTPLBOMView.as_view(), name='process_tpl_bom'),

    # Mock views for external redirects/iframes (will be implemented as full modules later)
    path('tpl-design-wo-tree-view/<str:wono>/<int:mod_id>/<int:sub_mod_id>/', WorkOrdersTreeView.as_view(), name='work_orders_tree_view'),
    path('tpl-design-copy-wo/<str:wono_dest>/<int:dest_p_id>/<int:dest_c_id>/', TplDesignCopyWoView.as_view(), name='tpl_design_copy_wo'),
]

```

#### 4.6 Tests (`work_orders/tests.py`)

Comprehensive unit tests for models and integration tests for views, aiming for high coverage.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db import transaction
from django.utils import timezone
from decimal import Decimal

from .models import (
    Category, SubCategory, Unit, Location, Item, 
    TplItemTemp, TplMaster, BomMaster, WorkOrderMaster,
    get_opening_date # Import helper utility
)
from .forms import ItemSearchForm, AddItemToTempForm, NewItemCreateForm
from .views import get_wo_params

class BaseModelSetup(TestCase):
    """Base class for setting up common test data."""
    @classmethod
    def setUpTestData(cls):
        # Common parameters
        cls.comp_id = 1
        cls.fin_year_id = 2024
        cls.session_id = 'testuser'
        cls.wono = 'WO123'
        cls.parent_id = 100
        cls.child_id = 101
        cls.assembly_no_id = 1000 # ID of an assembly item

        # Setup common data
        cls.category = Category.objects.create(c_id=1, c_name='Electronics', comp_id=cls.comp_id, fin_year_id=cls.fin_year_id)
        cls.subcategory = SubCategory.objects.create(sc_id=1, sc_name='Resistors', c_id=cls.category, symbol='R', comp_id=cls.comp_id, fin_year_id=cls.fin_year_id)
        cls.unit_ea = Unit.objects.create(id=1, symbol='EA')
        cls.unit_box = Unit.objects.create(id=2, symbol='BOX')
        cls.location = Location.objects.create(id=1, location_label='Shelf', location_no='A01')

        cls.item1 = Item.objects.create(
            id=1001, item_code='ELEC-001', manf_desc='10k Ohm Resistor', purch_desc='Resistor 10k',
            uom_basic=cls.unit_ea, uom_purchase=cls.unit_box, location=cls.location,
            category=cls.category, subcategory=cls.subcategory, stock_qty=Decimal('1000.000'),
            part_no='001', revision=0, comp_id=cls.comp_id, fin_year_id=cls.fin_year_id,
            sys_date=timezone.now().date(), sys_time=timezone.now().time(), session_id=cls.session_id
        )
        cls.item2 = Item.objects.create(
            id=1002, item_code='MECH-002', manf_desc='Screw M3x10', purch_desc='Phillips Head Screw',
            uom_basic=cls.unit_ea, uom_purchase=cls.unit_box, location=cls.location,
            category=cls.category, subcategory=cls.subcategory, stock_qty=Decimal('5000.000'),
            part_no='002', revision=0, comp_id=cls.comp_id, fin_year_id=cls.fin_year_id,
            sys_date=timezone.now().date(), sys_time=timezone.now().time(), session_id=cls.session_id
        )
        cls.assembly_item = Item.objects.create(
            id=cls.assembly_no_id, item_code='ASSY-001', manf_desc='Main Assembly', purch_desc='Main Assembly',
            uom_basic=cls.unit_ea, uom_purchase=cls.unit_ea, location=cls.location,
            category=cls.category, subcategory=cls.subcategory, stock_qty=Decimal('10.000'),
            part_no='ASSY', revision=0, comp_id=cls.comp_id, fin_year_id=cls.fin_year_id,
            sys_date=timezone.now().date(), sys_time=timezone.now().time(), session_id=cls.session_id
        )
        cls.work_order = WorkOrderMaster.objects.create(wono=cls.wono, comp_id=cls.comp_id, update_wo=0)

        # Setup temp item for testing
        TplItemTemp.objects.create(
            id=1, wono=cls.wono, item=cls.item1, qty=Decimal('5.000'), child_id=cls.child_id,
            comp_id=cls.comp_id, session_id=cls.session_id
        )
        TplItemTemp.objects.create(
            id=2, wono=cls.wono, item=None, qty=Decimal('2.000'), child_id=cls.child_id,
            comp_id=cls.comp_id, session_id=cls.session_id, item_code='NEW-PART-001',
            manf_desc='New Custom Part', purch_desc='Custom Part', uom_basic_id=cls.unit_ea.id,
            uom_purchase_id=cls.unit_ea.id, part_no='NEWPART', revision=0, category_id=cls.category.c_id
        )


class ModelManagerTests(BaseModelSetup):
    def test_item_manager_get_filtered_items(self):
        # Test basic filtering
        items = Item.objects.get_filtered_items(
            category_id=self.category.c_id, subcategory_id=None,
            search_type=None, search_term=None, location_id=None,
            company_id=self.comp_id, fin_year_id=self.fin_year_id,
            assembly_no_id=None, child_id=None, wono=None
        )
        self.assertIn(self.item1, items)
        self.assertIn(self.item2, items)
        self.assertIn(self.assembly_item, items)

        # Test item code search
        items = Item.objects.get_filtered_items(
            category_id=None, subcategory_id=None,
            search_type='tblDG_Item_Master.ItemCode', search_term='ELEC', location_id=None,
            company_id=self.comp_id, fin_year_id=self.fin_year_id,
            assembly_no_id=None, child_id=None, wono=None
        )
        self.assertIn(self.item1, items)
        self.assertNotIn(self.item2, items)

        # Test exclusion of assembly item
        items = Item.objects.get_filtered_items(
            category_id=None, subcategory_id=None,
            search_type=None, search_term=None, location_id=None,
            company_id=self.comp_id, fin_year_id=self.fin_year_id,
            assembly_no_id=self.assembly_no_id, child_id=None, wono=None
        )
        self.assertNotIn(self.assembly_item, items)

    def test_tpl_item_temp_manager_get_selected_items(self):
        selected = TplItemTemp.objects.get_selected_items(self.wono, self.child_id, self.comp_id)
        self.assertEqual(len(selected), 2)
        # Check details for existing item
        self.assertEqual(selected[1]['item_code'], self.item1.item_code)
        self.assertEqual(selected[1]['manf_desc'], self.item1.manf_desc)
        self.assertEqual(selected[1]['uom_basic_symbol'], self.unit_ea.symbol)
        # Check details for new item
        self.assertEqual(selected[0]['item_code'], 'NEW-PART-001')
        self.assertEqual(selected[0]['manf_desc'], 'New Custom Part')
        self.assertEqual(selected[0]['uom_basic_symbol'], self.unit_ea.symbol) # Should correctly get symbol from Unit.objects.filter

    def test_tpl_item_temp_manager_add_existing_item(self):
        TplItemTemp.objects.add_existing_item(
            self.comp_id, self.session_id, self.wono, self.item2.id, Decimal('10.000'), self.child_id, weldments=0
        )
        self.assertEqual(TplItemTemp.objects.count(), 3)
        new_entry = TplItemTemp.objects.get(item=self.item2)
        self.assertEqual(new_entry.qty, Decimal('10.000'))

        # Test validation: quantity <= 0
        with self.assertRaises(ValueError):
            TplItemTemp.objects.add_existing_item(
                self.comp_id, self.session_id, self.wono, self.item2.id, Decimal('0.000'), self.child_id, weldments=0
            )

        # Test validation: record already inserted
        with self.assertRaises(ValueError):
            TplItemTemp.objects.add_existing_item(
                self.comp_id, self.session_id, self.wono, self.item1.id, Decimal('1.000'), self.child_id, weldments=0
            )

    def test_tpl_item_temp_manager_add_new_item(self):
        TplItemTemp.objects.add_new_item(
            self.session_id, self.comp_id, self.wono, self.category.c_id, self.child_id,
            'MYPART', 1, 'My New Manf Desc', 'My New Purch Desc', self.unit_box.id, self.unit_ea.id, Decimal('3.500'),
            len(self.wono) # wo_length for part_no_max_length
        )
        self.assertEqual(TplItemTemp.objects.count(), 3)
        new_entry = TplItemTemp.objects.get(part_no='MYPART')
        self.assertEqual(new_entry.manf_desc, 'My New Manf Desc')
        self.assertEqual(new_entry.qty, Decimal('3.500'))

        # Test validation: quantity <= 0
        with self.assertRaises(ValueError):
            TplItemTemp.objects.add_new_item(
                self.session_id, self.comp_id, self.wono, self.category.c_id, self.child_id,
                'INVALID', 0, 'desc', 'pdesc', self.unit_ea.id, self.unit_ea.id, Decimal('0.000'), len(self.wono)
            )
        
        # Test validation: part number length (assuming default dynamic length calculation in ItemManager)
        # This test needs to match the actual logic in `Item.objects.get_item_code_limit`.
        # For this example, let's assume `get_item_code_limit` returns `len(wono) + 5` for simplicity
        # so we can create a failing test case.
        # This will fail if get_item_code_limit() is 10 for WO123, which is 3+5=8.
        # So providing a part_no that is not 8 chars long should fail.
        # As the example is simple, if len(wono) is 3, and get_item_code_limit returns 8,
        # then "MYPART" (6 chars) should raise error if `len(part_no) != item_code_limit`.
        if len('MYPART') != Item.objects.get_item_code_limit(self.comp_id, len(self.wono)):
             with self.assertRaises(ValueError):
                TplItemTemp.objects.add_new_item(
                    self.session_id, self.comp_id, self.wono, self.category.c_id, self.child_id,
                    'MYPART', 1, 'My New Manf Desc', 'My New Purch Desc', self.unit_box.id, self.unit_ea.id, Decimal('3.500'),
                    len(self.wono) # wo_length
                )
        
        # Test validation: record already inserted (for new items, by item_code)
        with self.assertRaises(ValueError):
            TplItemTemp.objects.add_new_item(
                self.session_id, self.comp_id, self.wono, self.category.c_id, self.child_id,
                'NEWPART', 0, 'New Custom Part', 'Custom Part', self.unit_ea.id, self.unit_ea.id, Decimal('1.000'), len(self.wono)
            )

    def test_tpl_item_temp_manager_delete_item(self):
        TplItemTemp.objects.delete_item(1, self.session_id, self.comp_id)
        self.assertEqual(TplItemTemp.objects.count(), 1)
        self.assertFalse(TplItemTemp.objects.filter(id=1).exists())

    def test_tpl_item_temp_manager_clear_temp_items(self):
        TplItemTemp.objects.clear_temp_items(self.comp_id, self.session_id, self.wono)
        self.assertEqual(TplItemTemp.objects.count(), 0)

    def test_tpl_master_manager_get_next_tpl_child_id(self):
        next_cid = TplMaster.objects.get_next_tpl_child_id(self.wono, self.comp_id, self.fin_year_id)
        self.assertEqual(next_cid, 1) # No TPL entries yet

        TplMaster.objects.create(
            sys_date=timezone.now().date(), sys_time=timezone.now().time(),
            comp_id=self.comp_id, fin_year_id=self.fin_year_id, session_id=self.session_id,
            wono=self.wono, p_id=1, c_id=1, item=self.item1, qty=Decimal('1.0'), convert_to_bom=0, weldments=0
        )
        next_cid = TplMaster.objects.get_next_tpl_child_id(self.wono, self.comp_id, self.fin_year_id)
        self.assertEqual(next_cid, 2)

    def test_work_order_master_process_tpl_bom_from_temp(self):
        # Test 'Add To TPL'
        initial_tpl_count = TplMaster.objects.count()
        initial_bom_count = BomMaster.objects.count()
        temp_item_count = TplItemTemp.objects.count()

        WorkOrderMaster.process_tpl_bom_from_temp(
            self.wono, self.parent_id, self.child_id, self.comp_id,
            self.fin_year_id, self.session_id, convert_to_bom=0
        )
        
        # All temp items should be cleared
        self.assertEqual(TplItemTemp.objects.count(), 0)
        # 2 items initially in temp, so 2 new TPL entries
        self.assertEqual(TplMaster.objects.count(), initial_tpl_count + temp_item_count)
        # BOM count should remain the same
        self.assertEqual(BomMaster.objects.count(), initial_bom_count)
        # Work order should be updated
        self.work_order.refresh_from_db()
        self.assertEqual(self.work_order.update_wo, 0) # Only updates if convert_to_bom is 1

        # Re-create temp items for next test
        TplItemTemp.objects.create(
            id=3, wono=self.wono, item=self.item1, qty=Decimal('5.000'), child_id=self.child_id,
            comp_id=self.comp_id, session_id=self.session_id
        )
        TplItemTemp.objects.create(
            id=4, wono=self.wono, item=None, qty=Decimal('2.000'), child_id=self.child_id,
            comp_id=self.comp_id, session_id=self.session_id, item_code='NEW-PART-002',
            manf_desc='Another New Custom Part', purch_desc='Another Custom Part', uom_basic_id=self.unit_ea.id,
            uom_purchase_id=self.unit_ea.id, part_no='NEWPART2', revision=0, category_id=self.category.c_id
        )

        # Test 'Add To TPL & BOM'
        initial_tpl_count = TplMaster.objects.count()
        initial_bom_count = BomMaster.objects.count()
        temp_item_count = TplItemTemp.objects.count()

        WorkOrderMaster.process_tpl_bom_from_temp(
            self.wono, self.parent_id, self.child_id, self.comp_id,
            self.fin_year_id, self.session_id, convert_to_bom=1
        )
        
        self.assertEqual(TplItemTemp.objects.count(), 0)
        self.assertEqual(TplMaster.objects.count(), initial_tpl_count + temp_item_count)
        self.assertEqual(BomMaster.objects.count(), initial_bom_count + temp_item_count)
        self.work_order.refresh_from_db()
        self.assertEqual(self.work_order.update_wo, 1)

class ViewsTest(BaseModelSetup):
    def setUp(self):
        self.client = Client()
        # Set session variables for consistent testing
        session = self.client.session
        session['compid'] = self.comp_id
        session['finyear'] = self.fin_year_id
        session['username'] = self.session_id
        session.save()

        # URL parameters for most requests
        self.url_params = f"WONo={self.wono}&PId={self.parent_id}&CId={self.child_id}&ItemId={self.assembly_no_id}"

    def test_wo_items_dashboard_view_get(self):
        response = self.client.get(reverse('work_orders:wo_items_dashboard') + f'?{self.url_params}')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'work_orders/woitems_dashboard.html')
        self.assertContains(response, self.wono)
        self.assertContains(response, self.assembly_item.item_code)
        self.assertContains(response, 'Item Master')
        self.assertContains(response, 'New Items')
        self.assertContains(response, 'Selected Items')

    def test_item_master_table_view_get(self):
        response = self.client.get(reverse('work_orders:item_master_table') + f'?{self.url_params}', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'work_orders/partials/_item_master_table.html')
        self.assertContains(response, self.item1.item_code)
        self.assertContains(response, self.item2.item_code)
        self.assertContains(response, self.assembly_item.item_code) # Should be excluded by filter if assembly_no_id is passed

        # Test search functionality (via HTMX get)
        search_params = f'{self.url_params}&category={self.category.c_id}&search_code=tblDG_Item_Master.ItemCode&search_item_code=ELEC'
        response = self.client.get(reverse('work_orders:item_master_table') + f'?{search_params}', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.item1.item_code)
        self.assertNotContains(response, self.item2.item_code)

    def test_filter_subcategories_view_get(self):
        response = self.client.get(reverse('work_orders:filter_subcategories') + f'?{self.url_params}&category={self.category.c_id}', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'work_orders/partials/_subcategory_field.html')
        self.assertContains(response, self.subcategory.sc_name)

    def test_add_item_to_temp_view_post_success(self):
        initial_count = TplItemTemp.objects.count()
        data = {
            'item_id': self.item2.id,
            'quantity': '12.500',
        }
        response = self.client.post(reverse('work_orders:add_item_to_temp') + f'?{self.url_params}', data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success code
        self.assertEqual(TplItemTemp.objects.count(), initial_count + 1)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSelectedItemsList')

    def test_add_item_to_temp_view_post_validation_error(self):
        initial_count = TplItemTemp.objects.count()
        data = {
            'item_id': self.item2.id,
            'quantity': '0', # Invalid quantity
        }
        response = self.client.post(reverse('work_orders:add_item_to_temp') + f'?{self.url_params}', data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(TplItemTemp.objects.count(), initial_count)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('error', response.headers['HX-Trigger']) # Check for error message trigger

    def test_new_item_form_view_post_success(self):
        initial_count = TplItemTemp.objects.count()
        data = {
            'category': self.category.c_id,
            'part_no': 'NEWP001',
            'revision': False,
            'manf_description': 'New custom part manf desc',
            'purch_description': 'New custom part purch desc',
            'unit_basic': self.unit_ea.id,
            'unit_purchase': self.unit_ea.id,
            'quantity': '10.000',
        }
        response = self.client.post(reverse('work_orders:new_item_form') + f'?{self.url_params}', data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Re-render form on success
        self.assertEqual(TplItemTemp.objects.count(), initial_count + 1)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSelectedItemsList')

    def test_new_item_form_view_post_validation_error(self):
        initial_count = TplItemTemp.objects.count()
        data = {
            'category': self.category.c_id,
            'part_no': 'INV', # Too short if item_code_limit applies
            'revision': False,
            'manf_description': '', # Required field
            'purch_description': 'New custom part purch desc',
            'unit_basic': self.unit_ea.id,
            'unit_purchase': self.unit_ea.id,
            'quantity': '0', # Invalid quantity
        }
        response = self.client.post(reverse('work_orders:new_item_form') + f'?{self.url_params}', data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Re-render form with errors
        self.assertEqual(TplItemTemp.objects.count(), initial_count)
        self.assertContains(response, 'Please correct the errors in the form')
        self.assertContains(response, 'This field is required') # For manf_description
        self.assertContains(response, 'Required quantity must be greater than zero.') # For quantity

    def test_selected_items_table_view_get(self):
        response = self.client.get(reverse('work_orders:selected_items_table') + f'?{self.url_params}', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'work_orders/partials/_selected_items_table.html')
        self.assertContains(response, self.item1.item_code) # From first temp item
        self.assertContains(response, 'NEW-PART-001') # From second temp item (new)

    def test_delete_tpl_item_temp_view_post_success(self):
        initial_count = TplItemTemp.objects.count()
        item_to_delete = TplItemTemp.objects.first()
        response = self.client.post(reverse('work_orders:delete_tpl_item_temp', args=[item_to_delete.id]) + f'?{self.url_params}', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(TplItemTemp.objects.count(), initial_count - 1)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSelectedItemsList')

    def test_process_tpl_bom_view_post_to_tpl(self):
        initial_tpl_count = TplMaster.objects.count()
        initial_bom_count = BomMaster.objects.count()
        initial_wo_update_status = self.work_order.update_wo
        
        response = self.client.post(reverse('work_orders:process_tpl_bom') + f'?{self.url_params}', {'convert_to_bom': 0}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Redirect', response.headers)
        self.assertEqual(TplItemTemp.objects.count(), 0) # Temp items cleared
        self.assertEqual(TplMaster.objects.count(), initial_tpl_count + 2) # 2 items added
        self.assertEqual(BomMaster.objects.count(), initial_bom_count) # No BOM updates
        self.work_order.refresh_from_db()
        self.assertEqual(self.work_order.update_wo, initial_wo_update_status) # No WO update

    def test_process_tpl_bom_view_post_to_tpl_and_bom(self):
        # Reset temp items and WO status before this test if run after previous
        TplItemTemp.objects.all().delete()
        TplItemTemp.objects.create(
            id=5, wono=self.wono, item=self.item1, qty=Decimal('5.000'), child_id=self.child_id,
            comp_id=self.comp_id, session_id=self.session_id
        )
        TplItemTemp.objects.create(
            id=6, wono=self.wono, item=None, qty=Decimal('2.000'), child_id=self.child_id,
            comp_id=self.comp_id, session_id=self.session_id, item_code='NEW-PART-003',
            manf_desc='Another New Custom Part', purch_desc='Another Custom Part', uom_basic_id=self.unit_ea.id,
            uom_purchase_id=self.unit_ea.id, part_no='NEWPART3', revision=0, category_id=self.category.c_id
        )
        self.work_order.update_wo = 0
        self.work_order.save()

        initial_tpl_count = TplMaster.objects.count()
        initial_bom_count = BomMaster.objects.count()
        
        response = self.client.post(reverse('work_orders:process_tpl_bom') + f'?{self.url_params}', {'convert_to_bom': 1}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Redirect', response.headers)
        self.assertEqual(TplItemTemp.objects.count(), 0) # Temp items cleared
        self.assertEqual(TplMaster.objects.count(), initial_tpl_count + 2) # 2 items added to TPL
        self.assertEqual(BomMaster.objects.count(), initial_bom_count + 2) # 2 items added to BOM
        self.work_order.refresh_from_db()
        self.assertEqual(self.work_order.update_wo, 1) # WO status updated
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The migration strategy heavily relies on HTMX for server-side rendering of dynamic content and Alpine.js for localized UI state management and interactivity.

*   **HTMX for Tab Navigation:**
    *   The main `woitems_dashboard.html` uses Alpine.js `x-data="{ activeTab: 'itemMaster' }"` to manage the active tab state.
    *   Each tab button has `hx-get` attributes pointing to the respective Django partial view URLs (`item_master_table`, `new_item_form`, etc.).
    *   `hx-target="#tabContent"` ensures the loaded content replaces the previous tab's content.
    *   `hx-swap="innerHTML"` efficiently swaps only the inner HTML of the target.
    *   `hx-indicator="#tabLoader"` provides a loading spinner.
    *   The `x-init` on the first tab button ensures it's clicked automatically on page load to load its content via HTMX.

*   **HTMX for DataTables Refresh:**
    *   The `_item_master_table.html` and `_selected_items_table.html` partials are the targets for HTMX requests.
    *   After adding/deleting an item (`AddItemToTempView`, `DeleteTplItemTempView`), `HX-Trigger` headers (`refreshSelectedItemsList`) are sent to the client.
    *   The `selectedItemsTable` div in `_selected_items_table.html` listens for `refreshSelectedItemsList` (`hx-trigger="load, refreshSelectedItemsList from:body"`) to re-fetch its content and redraw the table.
    *   Similarly, changes in search filters on `_item_master_table.html` trigger `hx-get` to reload the filtered table.

*   **HTMX for Form Submissions:**
    *   The "Add" button in the Item Master table uses `hx-post` to `add_item_to_temp`, with `hx-swap="none"` and `HX-Trigger` on success, indicating no content needs to be swapped, but other parts of the UI should refresh.
    *   The "New Items" form uses `hx-post` to `new_item_form`, and on success, `hx-swap="outerHTML"` re-renders the *entire form* (which also clears it).
    *   "Delete" buttons in the Selected Items table use `hx-post` to `delete_tpl_item_temp` with `hx-swap="none"` and `HX-Trigger`.
    *   "Add To TPL" and "Add To TPL & BOM" buttons use `hx-post` to `process_tpl_bom` with `hx-swap="none"` and `HX-Redirect` on success to navigate away as the original ASP.NET did.

*   **Alpine.js for UI State:**
    *   `Alpine.store('woItems')` manages shared state like `showSearchText` and `showSearchLocation` for toggling the visibility of search input fields based on `search_code` selection. This mimics `txtSearchItemCode.Visible` and `DropDownList3.Visible` logic.
    *   Basic client-side `confirm` dialogs are directly used in templates, as in the original ASP.NET.
    *   A custom HTMX event `showMessage` triggered via `HX-Trigger` allows the Alpine.js store to display Django `messages` (success/error) dynamically without full page reloads.

*   **DataTables for List Views:**
    *   Both `itemMasterTable` and `selectedItemsTable` are configured to be DataTables instances.
    *   The initialization scripts are placed within the respective partial templates. This ensures they are re-run when new content is loaded via HTMX, correctly applying DataTables functionality to the newly rendered table.
    *   Custom search/filtering is handled by the Django backend and HTMX reloads, so DataTables' default `searching` is disabled.

*   **DRY Template Inheritance:**
    *   All component-specific templates (`_item_master_table.html`, `_new_item_form.html`, etc.) are designed to be partials.
    *   The `woitems_dashboard.html` extends `core/base.html` (which is assumed to contain common CDN links like HTMX, Alpine.js, jQuery, and DataTables). This avoids redundancy.

**Final Notes:**
*   The `clsFunctions` methods were heavily abstracted. A real migration would require a detailed mapping of each function's SQL query and business logic to Django ORM operations, custom managers, or Python helper functions.
*   The recursive quantity calculation (`TplMasterManager.get_assembly_recursive_qty`) is a complex point in ERP systems. The provided placeholder is a starting point; a production-ready solution might involve Django ORM's `annotate` with `F` expressions, or more likely, raw SQL queries/stored procedures for optimal performance if the hierarchy is deep.
*   Error handling and user feedback are crucial. The `messages` framework combined with HTMX triggers and Alpine.js provides a robust solution.
*   Security considerations (e.g., proper input sanitization, access control, CSRF protection) are handled by Django's built-in features, but explicit checks may be needed for sensitive operations.
*   This plan provides a runnable framework. The placeholder logic for `ItemManager.generate_item_code`, `Item.objects.get_item_code_limit`, and `TplMaster.objects.get_assembly_recursive_qty` must be fully implemented according to the original `clsFunctions`' specific algorithms.