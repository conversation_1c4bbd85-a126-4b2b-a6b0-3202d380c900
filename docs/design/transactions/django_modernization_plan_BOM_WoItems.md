This modernization plan details the transition of your ASP.NET BOM Items module to a modern Django application, focusing on automation-driven approaches, clear business benefits, and adherence to "fat model, thin view" architecture with HTMX and Alpine.js for a highly interactive user experience.

## ASP.NET to Django Conversion Script: BOM Items Module

This document outlines the systematic conversion of your existing ASP.NET "BOM Items" module, encompassing its UI interactions, data management, and business logic, into a robust and maintainable Django application.

### Business Benefits of Django Modernization:

*   **Enhanced User Experience:** A switch to HTMX and Alpine.js ensures a smooth, app-like feel with instant updates and no full-page reloads, significantly improving user satisfaction and reducing cognitive load.
*   **Reduced Development Costs:** Django's "batteries-included" philosophy, combined with HTMX for frontend, minimizes the need for complex JavaScript frameworks, leading to faster development cycles and easier maintenance.
*   **Improved Performance:** Efficient data handling through Django ORM and server-side rendering for initial page loads, coupled with HTMX for partial updates, results in a snappier application.
*   **Scalability and Maintainability:** Django's modular structure and emphasis on best practices (e.g., fat models, thin views) ensure the application is easier to scale, debug, and extend as your business grows.
*   **Increased Security:** Django's built-in security features, such as CSRF protection and SQL injection prevention, provide a more secure foundation than legacy ASP.NET.
*   **Future-Proofing:** Moving to a widely adopted, open-source framework like Django offers a larger talent pool, continuous community support, and a vibrant ecosystem for ongoing innovation.
*   **Automation-Ready:** The structured nature of Django and clear separation of concerns make it highly amenable to AI-assisted code generation and automated migration processes, reducing manual effort and potential errors.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:** The ASP.NET code interacts with several tables. For the scope of this page, the core tables involved in its functionality are identified below. We will use the `tblDG_BOMItem_Temp` as the primary model for the current page's temporary item management. Other tables are related entities for lookups and final data persistence.

**Identified Tables and Inferred Columns:**

*   **`tblDG_BOMItem_Temp` (Temporary BOM Items):**
    *   `Id` (int, PK)
    *   `CompId` (int)
    *   `SessionId` (varchar)
    *   `WONo` (varchar)
    *   `ItemId` (int, FK to `tblDG_Item_Master.Id` - can be NULL for new items)
    *   `Qty` (float)
    *   `ChildId` (int)
    *   `PartNo` (varchar) - For new items
    *   `EquipmentNo` (varchar) - For new items
    *   `UnitNo` (varchar) - For new items
    *   `Process` (int)
    *   `ManfDesc` (varchar) - For new items
    *   `UOMBasic` (int, FK to `Unit_Master.Id`) - For new items
    *   `ImgFile` (varbinary) - For new items
    *   `ImgName` (varchar) - For new items
    *   `ImgSize` (float) - For new items
    *   `ImgContentType` (varchar) - For new items
    *   `SpecSheetName` (varchar) - For new items
    *   `SpecSheetSize` (float) - For new items
    *   `SpecSheetContentType` (varchar) - For new items
    *   `SpecSheetData` (varbinary) - For new items
    *   `ECNFlag` (int)

*   **`tblDG_Item_Master` (Item Catalog):**
    *   `Id` (int, PK)
    *   `CId` (int, FK to `tblDG_Category_Master.CId`)
    *   `PartNo` (varchar)
    *   `ItemCode` (varchar)
    *   `ManfDesc` (varchar)
    *   `UOMBasic` (int, FK to `Unit_Master.Id`)
    *   `Location` (varchar)
    *   `SysDate` (datetime)
    *   `SysTime` (varchar)
    *   `CompId` (int)
    *   `FinYearId` (int)
    *   `SessionId` (varchar)
    *   `OpeningBalDate` (datetime)
    *   `FileName` (varchar)
    *   `FileSize` (float)
    *   `ContentType` (varchar)
    *   `FileData` (varbinary)
    *   `AttName` (varchar)
    *   `AttSize` (float)
    *   `AttContentType` (varchar)
    *   `AttData` (varbinary)

*   **`Unit_Master` (Units of Measurement):**
    *   `Id` (int, PK)
    *   `Symbol` (varchar)

*   **`tblDG_Category_Master` (Item Categories):**
    *   `CId` (int, PK)
    *   `Symbol` (varchar)
    *   `CName` (varchar)

*   **`tblDG_ECN_Reason` (ECN Reasons):**
    *   `Id` (int, PK)
    *   `Types` (varchar)
    *   `CompId` (int)

*   **`SD_Cust_WorkOrder_Master` (Work Order Master):**
    *   `WONo` (varchar, PK)
    *   `TaskDesignFinalization_TDate` (datetime)
    *   `UpdateWO` (int/bool)

*   **`tblDG_BOM_Master` (Permanent BOM Master):**
    *   `Id` (int, PK)
    *   `SysDate` (datetime)
    *   `SysTime` (varchar)
    *   `CompId` (int)
    *   `FinYearId` (int)
    *   `SessionId` (varchar)
    *   `WONo` (varchar)
    *   `PId` (int)
    *   `CId` (int)
    *   `ItemId` (int, FK to `tblDG_Item_Master.Id`)
    *   `Qty` (float)
    *   `PartNo` (varchar)
    *   `EquipmentNo` (varchar)
    *   `UnitNo` (varchar)
    *   `ECNFlag` (int)

*   **`tblDG_ECN_Master_Temp` (Temporary ECN Master):**
    *   `Id` (int, PK)
    *   `MId` (int, FK to `tblDG_BOMItem_Temp.Id` for linking temporary item)
    *   `SysDate` (datetime)
    *   `SysTime` (varchar)
    *   `CompId` (int)
    *   `FinYearId` (int)
    *   `SessionId` (varchar)
    *   `ItemId` (int, FK to `tblDG_Item_Master.Id` - 0 if new custom item)
    *   `WONo` (varchar)
    *   `PId` (int)
    *   `CId` (int)
    *   `ItemCode` (varchar)

*   **`tblDG_ECN_Details_Temp` (Temporary ECN Details):**
    *   `Id` (int, PK)
    *   `MId` (int, FK to `tblDG_ECN_Master_Temp.Id`)
    *   `ECNReason` (int, FK to `tblDG_ECN_Reason.Id`)
    *   `Remarks` (varchar)

*   **`tblDG_ECN_Master` (Permanent ECN Master):**
    *   `Id` (int, PK)
    *   `SysDate` (datetime)
    *   `SysTime` (varchar)
    *   `CompId` (int)
    *   `FinYearId` (int)
    *   `SessionId` (varchar)
    *   `ItemId` (int, FK to `tblDG_Item_Master.Id`)
    *   `WONo` (varchar)
    *   `PId` (int)
    *   `CId` (int)

*   **`tblDG_ECN_Details` (Permanent ECN Details):**
    *   `Id` (int, PK)
    *   `MId` (int, FK to `tblDG_ECN_Master.Id`)
    *   `ECNReason` (int, FK to `tblDG_ECN_Reason.Id`)
    *   `Remarks` (varchar)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic in the ASP.NET code.

**Instructions:** The ASP.NET page performs the following key functions, which will be migrated to Django models and views.

*   **Read Operations:**
    *   Displaying Work Order Number, Assembly Number, and Equipment Number from query parameters.
    *   Listing existing items from `tblDG_Item_Master` (Item Master tab) based on filters (Category, Item Code, Description, Location) and search terms.
    *   Listing ECN reasons from `tblDG_ECN_Reason` (New Items tab).
    *   Listing currently selected/added items from `tblDG_BOMItem_Temp` (Selected Items tab).
    *   Retrieving `TaskDesignFinalization_TDate` from `SD_Cust_WorkOrder_Master`.
    *   Calculating Assembly Quantity (`AsslyNewqty`) and BOM Quantity (`BOMQty`) for selected items.
    *   Generating the next `UnitNo` and `PartNo` for new items based on existing BOM data.

*   **Create Operations:**
    *   Adding an existing item from `tblDG_Item_Master` to `tblDG_BOMItem_Temp` (Item Master tab).
    *   Adding a brand new, user-defined item (with description, UOM, quantity, image, and spec sheet) to `tblDG_BOMItem_Temp` (New Items tab).
    *   Conditional creation of temporary ECN records (`tblDG_ECN_Master_Temp`, `tblDG_ECN_Details_Temp`) if `TaskDesignFinalization_TDate` is older than the current date for newly added items (both existing and new).

*   **Delete Operations:**
    *   Removing an item from `tblDG_BOMItem_Temp` and its associated temporary ECN records (`tblDG_ECN_Master_Temp`, `tblDG_ECN_Details_Temp`) (Selected Items tab).

*   **Transactional/Complex Logic:**
    *   **Finalizing BOM (`btnaddtobom_Click` / `AddToTPLBOM`):** This is a critical atomic operation. It iterates through all items in `tblDG_BOMItem_Temp` and performs the following:
        *   If the item is a new, user-defined item (no `ItemId`): Inserts it into `tblDG_Item_Master`.
        *   Inserts the item into the permanent `tblDG_BOM_Master`.
        *   Updates the `UpdateWO` flag in `SD_Cust_WorkOrder_Master`.
        *   Transfers any associated temporary ECN records (`tblDG_ECN_Master_Temp`, `tblDG_ECN_Details_Temp`) to their permanent counterparts (`tblDG_ECN_Master`, `tblDG_ECN_Details`).
        *   Clears all temporary BOM and ECN records for the current session and work order.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:** The ASP.NET UI components will be replaced with standard HTML elements enhanced with HTMX for dynamic interactions and Alpine.js for local UI state management. DataTables will be used for all tabular data presentation.

*   **Information Display:** `asp:Label` elements (`lblwono`, `lblasslyno`, `lblEquipNo`) will be standard HTML spans or divs with Django template context.
*   **Tabular Data:** `asp:GridView` controls (`GridView2`, `GridView1`, `GridView3`) will be replaced by `<table>` elements initialized with DataTables for rich client-side features like sorting, searching, and pagination. Content will be loaded dynamically via HTMX.
*   **User Input & Selection:**
    *   `asp:DropDownList` (`DrpType`, `DrpCategory1`, `DrpSearchCode`, `DropDownList3`, `DDLUnitBasic`) will become HTML `<select>` elements. Their `AutoPostBack="True"` behavior will be replicated using HTMX's `hx-trigger="change"` and `hx-post` or `hx-get` to trigger partial updates.
    *   `asp:TextBox` (`txtSearchItemCode`, `txtUnitNo`, `txtPartNo`, `txtManfDescription`, `txtQuntity`, `txtQty`, `TxtRemarks`) will become HTML `<input type="text">` or `<textarea>`. Client-side `onblur`/`onfocus`/`onkeyup` JavaScript for `isNaN` checks will be replaced by Django form validation and, if necessary, Alpine.js for immediate feedback or `pattern` attributes for regex.
*   **Actions:** `asp:Button` (`btnSearch`, `btnadd`, `btnSubmit`, `Button1`, `btnaddtobom`, `Button2`) and `asp:LinkButton` (`btndelete`) will be HTML `<button>` or `<a>` elements with HTMX attributes (`hx-post`, `hx-delete`, `hx-get`, `hx-target`, `hx-swap`, `hx-trigger`) to perform actions without full page reloads.
*   **File Uploads:** `asp:FileUpload` (`FileUpload1`, `FileUpload2`) will be standard HTML `<input type="file">` handled by Django forms.
*   **Validation:** `asp:RequiredFieldValidator`, `asp:RegularExpressionValidator` will be handled by Django's form validation system on the backend and HTML5 `required` and `pattern` attributes on the frontend. Error messages will be displayed inline.
*   **Modals/Popups:** Client-side `alert()` calls will be replaced by Django messages framework combined with HTMX `HX-Trigger` to display messages in a modern, non-blocking way (e.g., toast notifications) or via Alpine.js-driven modals.

---

### Step 4: Generate Django Code

We will create a new Django app, let's call it `bom_management`, to encapsulate this module.

#### 4.1 Models (`bom_management/models.py`)

**Task:** Create Django models based on the identified database schema. We'll use `managed = False` as these tables likely exist in a legacy database.

```python
from django.db import models
from django.db.models import F, Sum, Value, FloatField
from django.db.models.functions import Coalesce
from django.conf import settings
from datetime import date, datetime
import math # For decimal precision, though DecimalField is preferred for currency/quantities

class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit of Measure'
        verbose_name_plural = 'Units of Measure'

    def __str__(self):
        return self.symbol or 'N/A'

class CategoryMaster(models.Model):
    cid = models.IntegerField(db_column='CId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)
    cname = models.CharField(db_column='CName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Category Master'
        verbose_name_plural = 'Category Masters'

    def __str__(self):
        return f"[{self.symbol}] - {self.cname}" if self.symbol else self.cname

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    cid = models.ForeignKey(CategoryMaster, models.DO_NOTHING, db_column='CId', blank=True, null=True)
    part_no = models.CharField(db_column='PartNo', max_length=255, blank=True, null=True)
    item_code = models.CharField(db_column='ItemCode', max_length=255, blank=True, null=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=500, blank=True, null=True)
    uom_basic = models.ForeignKey(UnitMaster, models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)
    location = models.CharField(db_column='Location', max_length=255, blank=True, null=True)
    sys_date = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    sys_time = models.CharField(db_column='SysTime', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=255, blank=True, null=True)
    opening_bal_date = models.DateTimeField(db_column='OpeningBalDate', blank=True, null=True)
    file_name = models.CharField(db_column='FileName', max_length=255, blank=True, null=True)
    file_size = models.FloatField(db_column='FileSize', blank=True, null=True)
    content_type = models.CharField(db_column='ContentType', max_length=255, blank=True, null=True)
    file_data = models.BinaryField(db_column='FileData', blank=True, null=True)
    att_name = models.CharField(db_column='AttName', max_length=255, blank=True, null=True)
    att_size = models.FloatField(db_column='AttSize', blank=True, null=True)
    att_content_type = models.CharField(db_column='AttContentType', max_length=255, blank=True, null=True)
    att_data = models.BinaryField(db_column='AttData', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.item_code or self.part_no or self.manf_desc or 'N/A'

class BomItemTempManager(models.Manager):
    def get_items_for_display(self, wono, childid, session_id, comp_id, fin_year_id):
        # This replicates the logic from FillDataGrid()
        # It fetches items from tblDG_BOMItem_Temp and enriches them
        # with details from ItemMaster or self-defined new item properties.
        from .models import WorkOrderMaster # Import here to avoid circular dependency

        # Get the current assembly quantity
        # This RecurQty logic is complex and needs a dedicated helper or manager method
        # For simplicity, we'll mock or assume a simple fetch here.
        # Original: fun.RecurQty(wono, parentid, childid, 1, CompId, FinYearId)
        # Assuming WorkOrderMaster has a method or related data to calculate this.
        # For a full migration, this would need a recursive CTE or complex ORM.
        # For now, let's assume it's directly available or calculable:
        try:
            work_order = WorkOrderMaster.objects.get(wono=wono)
            # This is a placeholder; actual RecurQty needs proper implementation
            # It's usually a recursive quantity calculation based on BOM structure
            assembly_new_qty = work_order.calculate_assembly_qty(childid) # Example method
        except WorkOrderMaster.DoesNotExist:
            assembly_new_qty = 1.0 # Default or error handling

        bom_items = self.filter(
            wono=wono,
            child_id=childid,
            session_id=session_id,
            comp_id=comp_id
        ).order_by('-id')

        result_data = []
        for item in bom_items:
            item_code = ""
            manf_desc = ""
            uom_basic_symbol = ""
            if item.item_id: # Existing item from Item Master
                try:
                    master_item = ItemMaster.objects.select_related('uom_basic').get(
                        id=item.item_id,
                        comp_id=comp_id,
                        fin_year_id__lte=fin_year_id
                    )
                    item_code = master_item.item_code if master_item.cid else master_item.part_no
                    manf_desc = master_item.manf_desc
                    uom_basic_symbol = master_item.uom_basic.symbol if master_item.uom_basic else ""
                except ItemMaster.DoesNotExist:
                    pass # Item not found, handle gracefully
            else: # New user-defined item (from tblDG_BOMItem_Temp itself)
                item_code = item.part_no
                manf_desc = item.manf_desc
                if item.uom_basic_id: # Use _id for FK field if not directly ItemMaster
                    try:
                        uom_symbol = UnitMaster.objects.get(id=item.uom_basic_id).symbol
                        uom_basic_symbol = uom_symbol
                    except UnitMaster.DoesNotExist:
                        pass

            req_qty = float(item.qty) if item.qty is not None else 0.0
            bom_qty = float(assembly_new_qty * req_qty)

            result_data.append({
                'id': item.id,
                'item_id': item.item_id, # This is ItemMaster.Id for existing items
                'item_code': item_code,
                'manf_desc': manf_desc,
                'uom_basic': uom_basic_symbol,
                'assly_qty': round(float(assembly_new_qty), 3), # N3 format implies 3 decimal places
                'qty': round(req_qty, 3),
                'bom_qty': round(bom_qty, 3),
            })
        return result_data

class BomItemTemp(models.Model):
    # Id is the primary key and autoincrements in SQL Server, Django will handle this automatically.
    # For managed=False, it's critical to ensure 'id' maps correctly to the DB PK.
    id = models.IntegerField(db_column='Id', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=255, blank=True, null=True)
    wono = models.CharField(db_column='WONo', max_length=255, blank=True, null=True)
    item_id = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId', blank=True, null=True) # FK to tblDG_Item_Master.Id
    qty = models.FloatField(db_column='Qty', blank=True, null=True) # Required Quantity
    child_id = models.IntegerField(db_column='ChildId', blank=True, null=True) # CId from QueryString
    part_no = models.CharField(db_column='PartNo', max_length=255, blank=True, null=True) # For new item
    equipment_no = models.CharField(db_column='EquipmentNo', max_length=255, blank=True, null=True) # For new item
    unit_no = models.CharField(db_column='UnitNo', max_length=50, blank=True, null=True) # For new item
    process = models.IntegerField(db_column='Process', blank=True, null=True) # Defaulted to 0
    manf_desc = models.CharField(db_column='ManfDesc', max_length=500, blank=True, null=True) # For new item
    uom_basic = models.ForeignKey(UnitMaster, models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True) # FK to Unit_Master.Id for new item
    img_file = models.BinaryField(db_column='ImgFile', blank=True, null=True)
    img_name = models.CharField(db_column='ImgName', max_length=255, blank=True, null=True)
    img_size = models.FloatField(db_column='ImgSize', blank=True, null=True)
    img_content_type = models.CharField(db_column='ImgContentType', max_length=255, blank=True, null=True)
    spec_sheet_name = models.CharField(db_column='SpecSheetName', max_length=255, blank=True, null=True)
    spec_sheet_size = models.FloatField(db_column='SpecSheetSize', blank=True, null=True)
    spec_sheet_content_type = models.CharField(db_column='SpecSheetContentType', max_length=255, blank=True, null=True)
    spec_sheet_data = models.BinaryField(db_column='SpecSheetData', blank=True, null=True)
    ecn_flag = models.IntegerField(db_column='ECNFlag', blank=True, null=True) # Indicates if ECN is required

    objects = BomItemTempManager()

    class Meta:
        managed = False
        db_table = 'tblDG_BOMItem_Temp'
        verbose_name = 'BOM Temporary Item'
        verbose_name_plural = 'BOM Temporary Items'

    def __str__(self):
        return f"{self.wono} - {self.item_id or self.part_no} (Temp)"

    def get_display_item_code(self):
        """Returns the item code for display, preferring ItemMaster's code if available."""
        if self.item_id:
            return self.item_id.item_code if self.item_id.cid else self.item_id.part_no
        return self.part_no

    def get_display_manf_desc(self):
        """Returns the manufacturing description for display."""
        if self.item_id:
            return self.item_id.manf_desc
        return self.manf_desc

    def get_display_uom_basic(self):
        """Returns the UOM symbol for display."""
        if self.item_id and self.item_id.uom_basic:
            return self.item_id.uom_basic.symbol
        if self.uom_basic:
            return self.uom_basic.symbol
        return ''

class EcnReason(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    types = models.CharField(db_column='Types', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_ECN_Reason'
        verbose_name = 'ECN Reason'
        verbose_name_plural = 'ECN Reasons'

    def __str__(self):
        return self.types or 'N/A'

class WorkOrderMaster(models.Model):
    wono = models.CharField(db_column='WONo', primary_key=True, max_length=255)
    task_design_finalization_tdate = models.DateTimeField(db_column='TaskDesignFinalization_TDate', blank=True, null=True)
    update_wo = models.IntegerField(db_column='UpdateWO', blank=True, null=True)
    # Add other fields as necessary from SD_Cust_WorkOrder_Master

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order Master'
        verbose_name_plural = 'Work Order Masters'

    def __str__(self):
        return self.wono

    def is_design_finalized_before_current_date(self):
        """Checks if design finalization date is older than current date."""
        if self.task_design_finalization_tdate:
            return self.task_design_finalization_tdate.date() < date.today()
        return False
        
    def calculate_assembly_qty(self, child_id):
        """
        Placeholder for recursive assembly quantity calculation.
        This would typically involve traversing the BOM structure.
        """
        # Original C# used fun.RecurQty(wono, parentid, childid, 1, CompId, FinYearId)
        # This is a complex recursive function to determine the effective quantity of an assembly.
        # For a full migration, this needs to be re-implemented carefully, possibly with a CTE or
        # by building a recursive logic within Python if the BOM structure is loaded.
        # For now, returning a default.
        return 1.0 # Placeholder value


class BomMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    sys_time = models.CharField(db_column='SysTime', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=255, blank=True, null=True)
    wono = models.CharField(db_column='WONo', max_length=255, blank=True, null=True)
    pid = models.IntegerField(db_column='PId', blank=True, null=True) # Parent ID in BOM
    cid = models.IntegerField(db_column='CId', blank=True, null=True) # Child ID in BOM
    item_id = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId', blank=True, null=True)
    qty = models.FloatField(db_column='Qty', blank=True, null=True)
    part_no = models.CharField(db_column='PartNo', max_length=255, blank=True, null=True)
    equipment_no = models.CharField(db_column='EquipmentNo', max_length=255, blank=True, null=True)
    unit_no = models.CharField(db_column='UnitNo', max_length=50, blank=True, null=True)
    ecn_flag = models.IntegerField(db_column='ECNFlag', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'BOM Master'
        verbose_name_plural = 'BOM Masters'

    def __str__(self):
        return f"{self.wono} - {self.item_id or self.part_no}"

    def get_next_bom_cid(self, wono, comp_id, fin_year_id):
        """
        Replicates fun.getBOMCId().
        Finds the maximum CId for a given WONo, CompId, FinYearId in BOM_Master and BOMItem_Temp
        and returns the next available CId.
        """
        max_bom_cid = BomMaster.objects.filter(
            wono=wono,
            comp_id=comp_id,
            fin_year_id__lte=fin_year_id # Assuming FinYearId <= current for historical
        ).aggregate(max_cid=Coalesce(models.Max('cid'), 0))['max_cid']

        max_temp_cid = BomItemTemp.objects.filter(
            wono=wono,
            comp_id=comp_id,
            # No FinYearId for temp table as per original code, or if temp is session specific
            # session_id=session_id # if temp table is session specific
        ).aggregate(max_cid=Coalesce(models.Max('child_id'), 0))['max_cid'] # child_id in temp is CId in BOM

        return max(max_bom_cid, max_temp_cid) + 1


class EcnMasterTemp(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    mid = models.ForeignKey(BomItemTemp, models.DO_NOTHING, db_column='MId', blank=True, null=True) # MId links to temp BOM item
    sys_date = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    sys_time = models.CharField(db_column='SysTime', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=255, blank=True, null=True)
    item_id = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId', blank=True, null=True) # Original Item Id, 0 if new custom item
    wono = models.CharField(db_column='WONo', max_length=255, blank=True, null=True)
    pid = models.IntegerField(db_column='PId', blank=True, null=True)
    cid = models.IntegerField(db_column='CId', blank=True, null=True)
    item_code = models.CharField(db_column='ItemCOde', max_length=255, blank=True, null=True) # For new custom items

    class Meta:
        managed = False
        db_table = 'tblDG_ECN_Master_Temp'
        verbose_name = 'ECN Temporary Master'
        verbose_name_plural = 'ECN Temporary Masters'

class EcnDetailsTemp(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    mid = models.ForeignKey(EcnMasterTemp, models.DO_NOTHING, db_column='MId', blank=True, null=True)
    ecn_reason = models.ForeignKey(EcnReason, models.DO_NOTHING, db_column='ECNReason', blank=True, null=True)
    remarks = models.CharField(db_column='Remarks', max_length=500, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_ECN_Details_Temp'
        verbose_name = 'ECN Temporary Detail'
        verbose_name_plural = 'ECN Temporary Details'

class EcnMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    sys_time = models.CharField(db_column='SysTime', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=255, blank=True, null=True)
    item_id = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId', blank=True, null=True)
    wono = models.CharField(db_column='WONo', max_length=255, blank=True, null=True)
    pid = models.IntegerField(db_column='PId', blank=True, null=True)
    cid = models.IntegerField(db_column='CId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_ECN_Master'
        verbose_name = 'ECN Master'
        verbose_name_plural = 'ECN Masters'

class EcnDetails(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    mid = models.ForeignKey(EcnMaster, models.DO_NOTHING, db_column='MId', blank=True, null=True)
    ecn_reason = models.ForeignKey(EcnReason, models.DO_NOTHING, db_column='ECNReason', blank=True, null=True)
    remarks = models.CharField(db_column='Remarks', max_length=500, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_ECN_Details'
        verbose_name = 'ECN Detail'
        verbose_name_plural = 'ECN Details'

```

#### 4.2 Forms (`bom_management/forms.py`)

**Task:** Define Django forms for user input and filtering.

```python
from django import forms
from django.core.exceptions import ValidationError
from django.db import transaction
from .models import BomItemTemp, ItemMaster, UnitMaster, CategoryMaster, EcnReason, WorkOrderMaster, BomMaster, EcnMasterTemp, EcnDetailsTemp, EcnMaster, EcnDetails
from django.conf import settings
from datetime import datetime, date
import math # For precision rounding

class ItemMasterSearchForm(forms.Form):
    SEARCH_TYPE_CHOICES = [
        ('Select', 'Select'),
        ('Category', 'Category'),
        ('WOItems', 'WO Items'),
    ]
    SEARCH_CODE_CHOICES = [
        ('Select', 'Select'),
        ('tblDG_Item_Master.ItemCode', 'Item Code'),
        ('tblDG_Item_Master.ManfDesc', 'Description'),
        ('tblDG_Item_Master.Location', 'Location'),
    ]

    type = forms.ChoiceField(
        choices=SEARCH_TYPE_CHOICES,
        widget=forms.Select(attrs={'class': 'box3', 'hx-get': '/bom-management/item-master/search-options/', 'hx-target': '#item-master-filters', 'hx-swap': 'innerHTML', 'hx-trigger': 'change'}),
        label='Type'
    )
    category = forms.ModelChoiceField(
        queryset=CategoryMaster.objects.all().order_by('cname'),
        required=False,
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3'}),
        label='Category'
    )
    search_code = forms.ChoiceField(
        choices=SEARCH_CODE_CHOICES,
        required=False,
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3'}),
        label='Search By'
    )
    search_text = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3', 'placeholder': 'Enter search term'}),
        label='Search Value'
    )
    location = forms.CharField( # This should probably be a ModelChoiceField to LocationMaster
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3'}),
        label='Location'
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Apply initial visibility based on default 'Select'
        if self.initial.get('type') != 'Category':
            self.fields['category'].widget.attrs['style'] = 'display:none;'
        if self.initial.get('search_code') != 'tblDG_Item_Master.Location':
            self.fields['location'].widget.attrs['style'] = 'display:none;'
        if self.initial.get('search_code') not in ['tblDG_Item_Master.ItemCode', 'tblDG_Item_Master.ManfDesc']:
            self.fields['search_text'].widget.attrs['style'] = 'display:none;'


class AddExistingItemForm(forms.Form):
    # This form is for the quantity input on the GridView2 rows
    qty = forms.FloatField(
        min_value=0.001, # Req. Qty must be greater than zero.
        required=True,
        widget=forms.NumberInput(attrs={
            'class': 'box3',
            'placeholder': 'Req Qty',
            'onblur': 'if(isNaN(this.value)){ this.value=""; }', # Replicate JS validation, but backend will also validate
            'onfocus': 'if(isNaN(this.value)){ this.value=""; }',
            'onkeyup': 'if(isNaN(this.value)){ this.value=""; }',
            'step': '0.001', # Allow decimals up to 3 places
            'pattern': r'^\d{1,15}(\.\d{0,3})?$', # Regex from ASP.NET, enforce with HTML5
            'title': 'Numbers only, up to 15 digits, 3 decimal places.'
        }),
        error_messages={'required': 'Req. Qty should not be blank.', 'min_value': 'Req. Qty must be greater than zero.'}
    )
    item_master_id = forms.IntegerField(widget=forms.HiddenInput()) # To pass the ID of the item to add

    def clean_qty(self):
        qty = self.cleaned_data['qty']
        # Additional backend validation for number format if needed, but FloatField handles most.
        # Ensure it's not NaN (handled by FloatField conversion) and positive (min_value).
        return qty

class NewBOMItemForm(forms.ModelForm):
    # Fields that are specifically for new items and potentially nullable in model but required here
    unit_no = forms.CharField(
        max_length=2,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'box3',
            'onblur': 'if(isNaN(this.value)){ this.value=""; }',
            'onfocus': 'if(isNaN(this.value)){ this.value=""; }',
            'onkeyup': 'if(isNaN(this.value)){ this.value=""; }',
            'pattern': r'^\d{1,2}$', # Assume 2 digits based on MaxLength="2"
            'title': 'Numbers only, up to 2 digits.'
        })
    )
    part_no = forms.CharField(
        max_length=2,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'box3',
            'onblur': 'if(isNaN(this.value)){ this.value=""; }',
            'onfocus': 'if(isNaN(this.value)){ this.value=""; }',
            'onkeyup': 'if(isNaN(this.value)){ this.value=""; }',
            'pattern': r'^\d{1,2}$',
            'title': 'Numbers only, up to 2 digits.'
        })
    )
    manf_desc = forms.CharField(
        max_length=500,
        required=True,
        widget=forms.Textarea(attrs={'class': 'box3', 'rows': 4, 'cols': 40})
    )
    uom_basic = forms.ModelChoiceField(
        queryset=UnitMaster.objects.all().order_by('symbol'),
        empty_label="Select",
        required=True,
        widget=forms.Select(attrs={'class': 'box3'})
    )
    qty = forms.FloatField(
        min_value=0.001,
        required=True,
        widget=forms.NumberInput(attrs={
            'class': 'box3',
            'onblur': 'if(isNaN(this.value)){ this.value=""; }',
            'onfocus': 'if(isNaN(this.value)){ this.value=""; }',
            'onkeyup': 'if(isNaN(this.value)){ this.value=""; }',
            'step': '0.001',
            'pattern': r'^\d{1,15}(\.\d{0,3})?$',
            'title': 'Numbers only, up to 15 digits, 3 decimal places.'
        })
    )
    img_file_upload = forms.FileField(
        required=False,
        label='Image',
        widget=forms.ClearableFileInput(attrs={'class': 'block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'})
    )
    spec_sheet_upload = forms.FileField(
        required=False,
        label='Spec. Sheet',
        widget=forms.ClearableFileInput(attrs={'class': 'block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'})
    )

    class Meta:
        model = BomItemTemp
        fields = [
            'unit_no', 'part_no', 'manf_desc', 'uom_basic', 'qty',
        ]

    def __init__(self, *args, **kwargs):
        self.wono = kwargs.pop('wono', None)
        self.child_id = kwargs.pop('child_id', None)
        self.parent_id = kwargs.pop('parent_id', None)
        self.equip_no = kwargs.pop('equip_no', None)
        self.comp_id = kwargs.pop('comp_id', None)
        self.fin_year_id = kwargs.pop('fin_year_id', None)
        self.session_id = kwargs.pop('session_id', None)
        super().__init__(*args, **kwargs)

        # Pre-fill unit_no, part_no if provided by `CreateNextUnitPartNo`
        if self.equip_no and self.instance and not self.instance.pk: # Only for new instances
            next_unit_part = self.calculate_next_unit_part_no(self.equip_no, self.comp_id, self.fin_year_id)
            self.initial['unit_no'] = next_unit_part['unit_no']
            self.initial['part_no'] = next_unit_part['part_no']


        # Apply Tailwind classes to all fields
        for field_name, field in self.fields.items():
            if field_name not in ['img_file_upload', 'spec_sheet_upload']: # File fields styled separately
                if isinstance(field.widget, forms.TextInput) or isinstance(field.widget, forms.Textarea) or isinstance(field.widget, forms.NumberInput):
                    field.widget.attrs.update({'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
                elif isinstance(field.widget, forms.Select):
                    field.widget.attrs.update({'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})

    def calculate_next_unit_part_no(self, equip_no, comp_id, fin_year_id):
        """
        Replicates CreateNextUnitPartNo() logic.
        Generates the next available UnitNo and PartNo for a new item.
        """
        # This needs the initial assembly number to derive unit_no
        # Assuming lblasslyno.Text was passed as `equip_no` parameter.
        # Example: 'EQP-01-02' => equip_no='EQP', unit_no='01', part_no='02'
        # This is a simplification. Original C# split lblasslyno.Text
        # wono in QueryString is different from asslyno (ItemId in QueryString).
        # We need the full assembly number (lblasslyno.Text) from the parent context.
        # Let's assume `assly_no` is passed as a string in format 'EQP-UN-PN'
        assly_no_str = self.instance.assly_no if self.instance.pk else self.equip_no # Using equip_no as temp storage for the full assly_no
        if not assly_no_str or len(assly_no_str.split('-')) < 3:
            # Fallback or error if assly_no is not in expected format
            return {'unit_no': '01', 'part_no': '01'} # Default initial values

        parts = assly_no_str.split('-')
        current_unit_no = parts[1]
        current_part_no_int = int(parts[2])

        next_part_no_from_assly = current_part_no_int + 1
        next_part_no_str = str(next_part_no_from_assly).zfill(2) # D2 format

        un = f"{parts[0]}-{parts[1]}" # e.g., 'EQP-01'

        # Check existing PartNo in permanent BOM_Master and temporary BOMItem_Temp
        max_pn_master = BomMaster.objects.filter(
            comp_id=comp_id,
            part_no__startswith=un
        ).order_by('-part_no').values_list('part_no', flat=True).first()

        max_pn_temp = BomItemTemp.objects.filter(
            comp_id=comp_id,
            part_no__startswith=un
        ).order_by('-part_no').values_list('part_no', flat=True).first()

        pn1 = 0
        if max_pn_master:
            try:
                pn1 = int(max_pn_master.split('-')[2]) + 1
            except (IndexError, ValueError):
                pass # Handle malformed part numbers

        pn3 = 0
        if max_pn_temp:
            try:
                pn3 = int(max_pn_temp.split('-')[2]) + 1
            except (IndexError, ValueError):
                pass

        if pn1 > pn3:
            final_part_no = str(pn1).zfill(2)
        else:
            final_part_no = str(pn3).zfill(2)

        # Ensure it's at least the next from current assembly if others are lower
        if int(final_part_no) < next_part_no_from_assly:
            final_part_no = next_part_no_str

        return {'unit_no': current_unit_no, 'part_no': final_part_no}

    def clean(self):
        cleaned_data = super().clean()
        unit_no = cleaned_data.get('unit_no')
        part_no = cleaned_data.get('part_no')
        
        # Combine equipment_no, unit_no, part_no to form the full part_no string 'EQP-UN-PN'
        # The `equip_no` is assumed to be the first part of the assembly number (e.g., 'EQP')
        # This needs to be passed into the form's __init__ for proper context.
        if self.equip_no and unit_no and part_no:
            combined_part_no = f"{self.equip_no}-{unit_no}-{part_no}"
            
            # Check for existence in ItemMaster and BOM_Master/BOMItem_Temp
            # This replicates the "Record Already Exists!" logic
            item_master_exists = ItemMaster.objects.filter(
                part_no=combined_part_no,
                comp_id=self.comp_id,
                fin_year_id__lte=self.fin_year_id
            ).exists()

            bom_master_exists = BomMaster.objects.filter(
                part_no=combined_part_no,
                comp_id=self.comp_id
            ).exists()

            bom_item_temp_exists = BomItemTemp.objects.filter(
                part_no=combined_part_no,
                comp_id=self.comp_id,
                session_id=self.session_id # Should only check current session temp items
            ).exists()

            if item_master_exists or bom_master_exists or bom_item_temp_exists:
                # Add to validation errors if any exist.
                raise ValidationError("Record Already Exists! This Part No. is already in use.")

        return cleaned_data


class EcnReasonForm(forms.ModelForm):
    # For Checkbox1 and TxtRemarks in GridView1
    is_selected = forms.BooleanField(required=False, label='Select')
    remarks = forms.CharField(max_length=500, required=False, widget=forms.TextInput(attrs={'class': 'box3', 'placeholder': 'Remarks'}))

    class Meta:
        model = EcnReason
        fields = [] # No model fields directly used here, just for context

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Assuming the 'instance' is an EcnReason object
        if self.instance:
            self.fields['is_selected'].label = self.instance.types # Display reason as label
```

#### 4.3 Views (`bom_management/views.py`)

**Task:** Implement CRUD operations and logic using Django Class-Based Views (CBVs) and HTMX.

```python
from django.views.generic import TemplateView, ListView, CreateView, DeleteView, View
from django.urls import reverse_lazy
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db import transaction, connection
from django.db.models import Q
from datetime import datetime, date
import math # For rounding
import base64 # For file data encoding/decoding

from .models import (
    BomItemTemp, ItemMaster, UnitMaster, CategoryMaster, EcnReason,
    WorkOrderMaster, BomMaster, EcnMasterTemp, EcnDetailsTemp, EcnMaster, EcnDetails
)
from .forms import ItemMasterSearchForm, AddExistingItemForm, NewBOMItemForm, EcnReasonForm


# Helper function to get common session/company/financial year context
def get_common_context(request):
    # These values would typically come from user session, company profile, or URL parameters
    # For this migration, we'll assume they are available from the request or session,
    # replicating the ASP.NET global variables (CompId, FinYearId, SId, wono, parentid, childid, AsslyNo).
    comp_id = int(request.session.get('compid', 1)) # Default or get from session
    fin_year_id = int(request.session.get('finyear', 1)) # Default or get from session
    session_id = request.session.session_key # Django's session key
    user_id = request.user.id if request.user.is_authenticated else session_id # Use user ID if logged in, else session

    wono = request.GET.get('WONo')
    assly_item_id = request.GET.get('ItemId') # This is the ItemMaster.Id of the assembly
    parent_id = request.GET.get('PId')
    child_id = request.GET.get('CId')

    # Fetch assembly details and work order design date
    assly_no = ''
    equip_no = ''
    design_finalization_date = None
    is_design_finalized_before_current_date = False

    if assly_item_id:
        try:
            # Replicate fun.GetItemCode_PartNo(CompId, Convert.ToInt32(AsslyNo))
            assembly_item = ItemMaster.objects.get(id=int(assly_item_id), comp_id=comp_id)
            assly_no = assembly_item.item_code if assembly_item.cid else assembly_item.part_no
            if assly_no and '-' in assly_no:
                equip_no = assly_no.split('-')[0] # First part of assembly number
        except ItemMaster.DoesNotExist:
            pass

    if wono:
        try:
            work_order = WorkOrderMaster.objects.get(wono=wono)
            design_finalization_date = work_order.task_design_finalization_tdate
            is_design_finalized_before_current_date = work_order.is_design_finalized_before_current_date()
        except WorkOrderMaster.DoesNotExist:
            pass

    return {
        'comp_id': comp_id,
        'fin_year_id': fin_year_id,
        'session_id': user_id, # Use user ID for data persistence, session ID for temporary
        'wono': wono,
        'assly_item_id': assly_item_id,
        'parent_id': parent_id,
        'child_id': child_id,
        'assly_no': assly_no,
        'equip_no': equip_no,
        'design_finalization_date': design_finalization_date,
        'is_design_finalized_before_current_date': is_design_finalized_before_current_date,
        'current_date': date.today(),
        'current_time': datetime.now().strftime('%H:%M:%S'),
        'user_session_id': session_id # Raw Django session key for temporary records
    }

class BOMWoItemsDashboardView(TemplateView):
    template_name = 'bom_management/bomitemtemp/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        common_ctx = get_common_context(self.request)

        context.update(common_ctx)

        # Initial form for Item Master search
        context['item_master_search_form'] = ItemMasterSearchForm(
            initial={'type': 'Select'} # Set initial value for dropdown
        )

        # Initial form for New Item creation
        context['new_bom_item_form'] = NewBOMItemForm(
            wono=common_ctx['wono'],
            child_id=common_ctx['child_id'],
            parent_id=common_ctx['parent_id'],
            equip_no=common_ctx['assly_no'], # Pass the full assembly number for part no generation
            comp_id=common_ctx['comp_id'],
            fin_year_id=common_ctx['fin_year_id'],
            session_id=common_ctx['user_session_id']
        )

        # ECN Reasons for the 'New Items' tab
        context['ecn_reasons'] = EcnReason.objects.filter(comp_id=common_ctx['comp_id'])
        context['can_add_ecn'] = common_ctx['is_design_finalized_before_current_date'] # True if design date < current date

        return context

class ItemMasterTablePartialView(ListView):
    model = ItemMaster
    template_name = 'bom_management/bomitemtemp/_item_master_table.html'
    context_object_name = 'item_masters'
    paginate_by = 14 # PageSize from GridView2

    def get_queryset(self):
        common_ctx = get_common_context(self.request)
        comp_id = common_ctx['comp_id']
        fin_year_id = common_ctx['fin_year_id']
        queryset = ItemMaster.objects.filter(comp_id=comp_id, fin_year_id__lte=fin_year_id).order_by('id')

        # Replicate Fillgrid() logic for filtering and searching
        form = ItemMasterSearchForm(self.request.GET)
        if form.is_valid():
            search_type = form.cleaned_data.get('type')
            category = form.cleaned_data.get('category')
            search_code = form.cleaned_data.get('search_code')
            search_text = form.cleaned_data.get('search_text')
            location = form.cleaned_data.get('location') # This is problematic if 'location' is from DropDownList3

            if search_type == "Category":
                if category and category != "Select": # category is a CategoryMaster object
                    queryset = queryset.filter(cid=category.cid)

                if search_code != "Select":
                    if search_code == "tblDG_Item_Master.ItemCode" and search_text:
                        queryset = queryset.filter(item_code__startswith=search_text)
                    elif search_code == "tblDG_Item_Master.ManfDesc" and search_text:
                        queryset = queryset.filter(manf_desc__icontains=search_text)
                    elif search_code == "tblDG_Item_Master.Location" and location:
                        queryset = queryset.filter(location=location)
                elif not category and search_text: # sd == "Select" && B == "Select" && s != string.Empty
                     queryset = queryset.filter(manf_desc__icontains=search_text)

            elif search_type == "WOItems":
                # Assuming WOItems means items related to existing Work Orders or BOMs
                # The original code just changes the filter types without specifying WHERE clause differences.
                # This might require querying tblDG_BOM_Master to get relevant items.
                # For now, it will apply the same search filters to ItemMaster.
                if search_code != "Select":
                    if search_code == "tblDG_Item_Master.ItemCode" and search_text:
                        queryset = queryset.filter(item_code__icontains=search_text) # Original used Like '%s%', so icontains
                    elif search_code == "tblDG_Item_Master.ManfDesc" and search_text:
                        queryset = queryset.filter(manf_desc__icontains=search_text)
                elif search_text: # B == "Select" && s != string.Empty
                    queryset = queryset.filter(manf_desc__icontains=search_text)
            # If search_type is 'Select', no filtering happens by type, just default queryset.

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # This form is for the search inputs, not for adding an item
        context['item_master_search_form'] = ItemMasterSearchForm(self.request.GET)
        context['add_existing_item_form'] = AddExistingItemForm() # Pass empty form for row-level quantity input
        return context


class ItemMasterSearchOptionsPartialView(View):
    """
    Handles dynamic updates to search form fields (category, search_code, search_text, location)
    based on the selected 'type' and 'search_code' in ItemMasterSearchForm.
    """
    def get(self, request, *args, **kwargs):
        form = ItemMasterSearchForm(request.GET)
        context = get_common_context(request)
        
        # Render the form fields dynamically
        # This is a bit tricky with HTMX, usually we'd render the whole form again.
        # But if only parts need to change, you need a partial that contains just those parts.
        # For simplicity, we'll return a re-rendered form, assuming the JS handles replacements.
        # A more robust solution might render specific `<template>` elements for swaps.

        # Re-initialize the form based on current selection to apply correct visibility.
        form_reinitialized = ItemMasterSearchForm(request.GET, initial=request.GET)
        
        # Manually adjust field visibility based on selection as in ASP.NET code
        type_selected = form_reinitialized['type'].value
        search_code_selected = form_reinitialized['search_code'].value

        # Re-rendering only the filter options section, which is wrapped in a div.
        # This requires a dedicated partial template or careful rendering.
        # For now, let's render the entire form and rely on hx-swap to replace the 'item-master-filters' div
        # or use specific ids for each field that need to be swapped.
        
        categories = CategoryMaster.objects.filter(comp_id=context['comp_id']).order_by('cname')
        units = UnitMaster.objects.all().order_by('symbol') # For location if it becomes a dropdown

        # Build form for rendering based on rules
        form_html = render(request, 'bom_management/bomitemtemp/_item_master_search_form_fields.html', {
            'form': form_reinitialized,
            'categories': categories,
            'units': units, # If location is also a dropdown based on unit master
        }).content.decode()

        return HttpResponse(form_html)


class AddExistingItemToTempView(View):
    """
    Handles adding an item from Item Master grid (GridView2) to BomItemTemp.
    Replicates GridView2_RowCommand (Add) logic.
    """
    def post(self, request, *args, **kwargs):
        common_ctx = get_common_context(request)
        wono = common_ctx['wono']
        child_id = int(common_ctx['child_id'])
        parent_id = int(common_ctx['parent_id']) # Not directly used here but for ECN_Master.aspx redirect
        assly_item_id = common_ctx['assly_item_id'] # AsslyNo for ECN_Master.aspx redirect

        form = AddExistingItemForm(request.POST)

        if form.is_valid():
            item_master_id = form.cleaned_data['item_master_id']
            qty = round(form.cleaned_data['qty'], 3) # N3 format

            # Check if record already exists in temp or permanent BOM
            exists_in_temp = BomItemTemp.objects.filter(
                item_id=item_master_id,
                comp_id=common_ctx['comp_id'],
                wono=wono,
                child_id=child_id,
                session_id=common_ctx['user_session_id']
            ).exists()

            exists_in_bom_master = BomMaster.objects.filter(
                item_id=item_master_id,
                comp_id=common_ctx['comp_id'],
                wono=wono,
                pid=child_id # In BOM_Master, ChildId is PId (Parent ID)
            ).exists()

            if exists_in_temp or exists_in_bom_master:
                messages.error(request, "Record Already Inserted")
                return HttpResponse(status=200, headers={'HX-Trigger': 'showMessage'}) # Trigger a JS alert or toast

            if qty <= 0:
                messages.error(request, "Req. Qty must be greater than zero.")
                return HttpResponse(status=200, headers={'HX-Trigger': 'showMessage'})

            # Conditional logic for ECN: Design date vs current date
            if common_ctx['is_design_finalized_before_current_date']: # Designdate < CurrentDate
                # Redirect to ECN_Master.aspx equivalent
                # For this migration, we will log or indicate that ECN is required,
                # as the ECN_Master.aspx page logic is outside this scope.
                # If ECN_Master is part of this module, we'd trigger a modal or new tab.
                # As per C# logic, it redirects, so we simulate this by providing a trigger.
                # If this were a full-blown migration and ECN handled in Django,
                # we'd redirect to a Django ECN creation view.
                
                # For now, we'll mark it as requiring ECN and proceed to temp table.
                # A more accurate migration would be to render a modal or partial
                # that acts as the "ECN_Master.aspx" if it's tightly coupled.
                # The original C# actually REDIRECTS to ECN_Master.aspx.
                # For a full HTMX flow, we should probably trigger a modal for ECN reasons.
                # Let's adjust to trigger a modal.
                messages.warning(request, "ECN required for this item. Please provide reasons.")
                # Return a response that triggers an ECN modal for the given item_master_id
                return HttpResponse(
                    status=200,
                    headers={
                        'HX-Trigger': 'showEcnModal',
                        'HX-Redirect': reverse_lazy('bom_management:ecn_master_add') + \
                                       f"?ItemId={item_master_id}&WONo={wono}&CId={child_id}&ParentId={parent_id}&Qty={qty}&asslyNo={assly_item_id}"
                    }
                )

            # If no ECN required, add directly to temp table
            try:
                with transaction.atomic():
                    BomItemTemp.objects.create(
                        comp_id=common_ctx['comp_id'],
                        session_id=common_ctx['user_session_id'],
                        wono=wono,
                        item_id_id=item_master_id, # Use _id for ForeignKey direct assignment
                        qty=qty,
                        child_id=child_id,
                        ecn_flag=0 # 0 if no ECN needed at this stage
                    )
                    messages.success(request, "Record has been Inserted.")
            except Exception as e:
                messages.error(request, f"Error adding item: {e}")
                return HttpResponse(status=200, headers={'HX-Trigger': 'showMessage'})

            # Refresh both Item Master and Selected Items grids
            return HttpResponse(
                status=204, # No content, indicates success without changing current page
                headers={
                    'HX-Trigger': 'refreshItemMasterTable, refreshSelectedItemsTable, showMessage'
                }
            )
        else:
            # Form validation failed
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f"{field}: {error}")
            return HttpResponse(status=200, headers={'HX-Trigger': 'showMessage'})


class NewItemFormPartialView(View):
    """
    Handles the "New Items" tab content. Renders the form and handles its submission.
    Replicates btnSubmit_Click logic.
    """
    def get(self, request, *args, **kwargs):
        common_ctx = get_common_context(request)
        form = NewBOMItemForm(
            wono=common_ctx['wono'],
            child_id=common_ctx['child_id'],
            parent_id=common_ctx['parent_id'],
            equip_no=common_ctx['assly_no'], # Pass the full assembly number for part no generation
            comp_id=common_ctx['comp_id'],
            fin_year_id=common_ctx['fin_year_id'],
            session_id=common_ctx['user_session_id']
        )
        ecn_reasons = EcnReason.objects.filter(comp_id=common_ctx['comp_id'])
        can_add_ecn = common_ctx['is_design_finalized_before_current_date']

        return render(request, 'bom_management/bomitemtemp/_new_item_form.html', {
            'form': form,
            'equip_no_label': common_ctx['equip_no'],
            'ecn_reasons': ecn_reasons,
            'can_add_ecn': can_add_ecn,
            'is_design_finalized_before_current_date': can_add_ecn
        })

    def post(self, request, *args, **kwargs):
        common_ctx = get_common_context(request)
        form = NewBOMItemForm(
            request.POST, request.FILES,
            wono=common_ctx['wono'],
            child_id=common_ctx['child_id'],
            parent_id=common_ctx['parent_id'],
            equip_no=common_ctx['assly_no'], # Pass the full assembly number for part no generation
            comp_id=common_ctx['comp_id'],
            fin_year_id=common_ctx['fin_year_id'],
            session_id=common_ctx['user_session_id']
        )
        
        if form.is_valid():
            unit_no = form.cleaned_data['unit_no']
            part_no_short = form.cleaned_data['part_no']
            manf_desc = form.cleaned_data['manf_desc']
            uom_basic = form.cleaned_data['uom_basic'] # UnitMaster object
            qty = round(form.cleaned_data['qty'], 3)

            img_file = request.FILES.get('img_file_upload')
            spec_sheet = request.FILES.get('spec_sheet_upload')

            # Construct full PartNo (e.g., EQP-01-02) and ItemCode (EQP-01-020)
            # equip_no is the first part of the assembly number (lblasslyno.Text split)
            full_part_no = f"{common_ctx['equip_no']}-{unit_no}-{part_no_short}"
            item_code_full = f"{full_part_no}0" # As per original logic (P + '0')

            ecn_flag = 1 if common_ctx['is_design_finalized_before_current_date'] else 0

            try:
                with transaction.atomic():
                    new_bom_item_temp = BomItemTemp(
                        comp_id=common_ctx['comp_id'],
                        session_id=common_ctx['user_session_id'],
                        wono=common_ctx['wono'],
                        equipment_no=common_ctx['equip_no'],
                        unit_no=unit_no,
                        child_id=common_ctx['child_id'],
                        part_no=full_part_no,
                        item_code=item_code_full, # This field exists in BomItemTemp, but also in ItemMaster
                        process=0,
                        manf_desc=manf_desc,
                        uom_basic=uom_basic,
                        qty=qty,
                        ecn_flag=ecn_flag
                    )
                    
                    if img_file:
                        new_bom_item_temp.img_file = img_file.read()
                        new_bom_item_temp.img_name = img_file.name
                        new_bom_item_temp.img_size = img_file.size
                        new_bom_item_temp.img_content_type = img_file.content_type
                    if spec_sheet:
                        new_bom_item_temp.spec_sheet_data = spec_sheet.read()
                        new_bom_item_temp.spec_sheet_name = spec_sheet.name
                        new_bom_item_temp.spec_sheet_size = spec_sheet.size
                        new_bom_item_temp.spec_sheet_content_type = spec_sheet.content_type
                    
                    new_bom_item_temp.save() # Save the temporary item

                    # Handle ECN reasons if ECNFlag is 1
                    if ecn_flag == 1:
                        ecn_reason_data = []
                        # Iterate through submitted ECN reason checkboxes and remarks
                        for key, value in request.POST.items():
                            if key.startswith('ecn_reason_') and value == 'on': # Checkbox is checked
                                reason_id = key.replace('ecn_reason_', '')
                                remarks_key = f'remarks_{reason_id}'
                                remarks = request.POST.get(remarks_key, '')
                                ecn_reason_data.append({'reason_id': reason_id, 'remarks': remarks})

                        if not ecn_reason_data:
                            # Replicate "Enter reasons in Grid!"
                            messages.warning(request, "Please enter reasons in ECN Grid if ECN is required.")
                            # Re-render the form with validation errors and current data
                            # Need to refresh the form to show initial next part no.
                            # And ensure ECN grid is visible
                            return HttpResponse(status=200, headers={'HX-Trigger': 'showMessage, refreshEcnReasons'}) # Trigger refresh
                        
                        ecn_master_temp = EcnMasterTemp.objects.create(
                            mid=new_bom_item_temp, # Link to the newly created BomItemTemp
                            sys_date=date.today(),
                            sys_time=datetime.now().strftime('%H:%M:%S'),
                            comp_id=common_ctx['comp_id'],
                            fin_year_id=common_ctx['fin_year_id'],
                            session_id=common_ctx['user_session_id'],
                            item_id=None, # ItemId is 0 (DBNull.Value) for new items
                            wono=common_ctx['wono'],
                            pid=common_ctx['parent_id'],
                            cid=common_ctx['child_id'],
                            item_code=item_code_full # Custom item code
                        )

                        for reason in ecn_reason_data:
                            EcnDetailsTemp.objects.create(
                                mid=ecn_master_temp,
                                ecn_reason_id=int(reason['reason_id']),
                                remarks=reason['remarks']
                            )
                    
                    messages.success(request, "New Item added successfully.")

                    # Clear form fields after successful submission and refresh grids
                    # This involves telling the client to reset the form and refresh.
                    # For a clean form, we can swap in a new empty form.
                    # HX-Trigger with specific events for dashboard refresh and form reset
                    return HttpResponse(
                        status=204,
                        headers={
                            'HX-Trigger': 'refreshSelectedItemsTable, refreshNewItemForm, showMessage',
                            # Redirect if ECN was triggered and the original page redirects.
                            # Or just trigger the form to reset.
                            # 'HX-Redirect': request.path # Not ideal for HTMX; prefer partial swaps/resets
                        }
                    )

            except Exception as e:
                messages.error(request, f"Error adding new item: {e}")
                return HttpResponse(status=200, headers={'HX-Trigger': 'showMessage'})
        else:
            # Form validation failed
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f"{form.fields[field].label}: {error}")
            ecn_reasons = EcnReason.objects.filter(comp_id=common_ctx['comp_id'])
            can_add_ecn = common_ctx['is_design_finalized_before_current_date']
            return render(request, 'bom_management/bomitemtemp/_new_item_form.html', {
                'form': form, # Re-render form with errors
                'equip_no_label': common_ctx['equip_no'],
                'ecn_reasons': ecn_reasons,
                'can_add_ecn': can_add_ecn,
                'is_design_finalized_before_current_date': can_add_ecn
            }, status=400) # Indicate bad request


class ECNReasonsPartialView(ListView):
    """
    Renders the ECN reasons grid for the "New Items" tab.
    Replicates loaddata() logic.
    """
    model = EcnReason
    template_name = 'bom_management/bomitemtemp/_ecn_reasons_grid.html'
    context_object_name = 'ecn_reasons'

    def get_queryset(self):
        common_ctx = get_common_context(self.request)
        return EcnReason.objects.filter(comp_id=common_ctx['comp_id'])

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        common_ctx = get_common_context(self.request)
        context['is_design_finalized_before_current_date'] = common_ctx['is_design_finalized_before_current_date']
        return context


class SelectedItemsTablePartialView(ListView):
    """
    Renders the "Selected Items" grid (GridView3).
    Replicates FillDataGrid() logic.
    """
    model = BomItemTemp
    template_name = 'bom_management/bomitemtemp/_selected_items_table.html'
    context_object_name = 'selected_items'
    paginate_by = 30 # PageSize from GridView3

    def get_queryset(self):
        common_ctx = get_common_context(self.request)
        # Call the custom manager method to get enriched data
        return BomItemTemp.objects.get_items_for_display(
            common_ctx['wono'],
            int(common_ctx['child_id']),
            common_ctx['user_session_id'], # Use Django session key for temp items
            common_ctx['comp_id'],
            common_ctx['fin_year_id']
        )
    # The queryset here is a list of dicts, so pagination and sorting
    # will need to be handled manually or use a custom list-based paginator.
    # For DataTables, it's often simpler to just return all data and let DT handle it.
    # If paginate_by is used, it should be a real Django QuerySet.
    # For now, let's remove paginate_by and return the raw list of dicts to DT.


class DeleteTempBOMItemView(DeleteView):
    """
    Handles deletion of an item from BomItemTemp.
    Replicates GridView3_RowCommand (del) logic.
    """
    model = BomItemTemp
    template_name = 'bom_management/bomitemtemp/_confirm_delete_temp_item.html'
    
    def get_object(self, queryset=None):
        common_ctx = get_common_context(self.request)
        pk = self.kwargs.get(self.pk_url_kwarg)
        return get_object_or_404(BomItemTemp, id=pk, session_id=common_ctx['user_session_id'], comp_id=common_ctx['comp_id'])

    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        common_ctx = get_common_context(self.request)
        item_id = self.object.item_id.id if self.object.item_id else None # Get ItemMaster.Id if linked

        try:
            with transaction.atomic():
                # Delete related ECN Temp records first
                # The original C# first finds ECN_Master_Temp by ItemId. This is problematic if ItemId=0 for new items.
                # It should link by BomItemTemp.Id (MId in ECN_Master_Temp)
                EcnMasterTemp.objects.filter(
                    mid=self.object,
                    session_id=common_ctx['user_session_id'],
                    comp_id=common_ctx['comp_id']
                ).delete()
                
                self.object.delete() # Delete the BomItemTemp object

                messages.success(request, "Item deleted successfully from temporary list.")
        except Exception as e:
            messages.error(request, f"Error deleting item: {e}")
            return HttpResponse(status=200, headers={'HX-Trigger': 'showMessage'})

        return HttpResponse(
            status=204, # No content, indicates success without changing current page
            headers={
                'HX-Trigger': 'refreshSelectedItemsTable, showMessage'
            }
        )

class FinalizeBOMView(View):
    """
    Handles the "Add to BOM" button click.
    Replicates AddToTPLBOM(1) logic.
    """
    def post(self, request, *args, **kwargs):
        common_ctx = get_common_context(request)
        wono = common_ctx['wono']
        parent_id = int(common_ctx['parent_id']) # PId from QueryString
        child_id = int(common_ctx['child_id']) # CId from QueryString (becomes PId in permanent BOM)
        comp_id = common_ctx['comp_id']
        fin_year_id = common_ctx['fin_year_id']
        session_id = common_ctx['user_session_id']
        current_date = common_ctx['current_date']
        current_time = common_ctx['current_time']

        try:
            with transaction.atomic():
                bom_items_temp = BomItemTemp.objects.filter(
                    comp_id=comp_id,
                    session_id=session_id,
                    wono=wono,
                    child_id=child_id # Filter by ChildId of the parent assembly
                )

                if not bom_items_temp.exists():
                    messages.warning(request, "No items to add to BOM.")
                    return HttpResponse(status=200, headers={'HX-Trigger': 'showMessage'})


                for temp_item in bom_items_temp:
                    next_cid = BomMaster().get_next_bom_cid(wono, comp_id, fin_year_id)

                    item_to_insert_id = None
                    if temp_item.item_id: # Existing item from Item Master
                        item_to_insert_id = temp_item.item_id
                        # Check if item already exists in permanent BOM_Master for this parent
                        # This check is implicitly handled by `get_next_bom_cid` if we assume
                        # CId is unique per PId or item uniqueness is handled by database constraint.
                        # Original C#: cmdtplId = fun.select("ItemId", "tblDG_BOM_Master", "... AND ItemId='" + sqlDsget.Tables[0].Rows[0]["Id"].ToString() + "'");
                        already_in_bom = BomMaster.objects.filter(
                            wono=wono,
                            pid=child_id, # PId in BOM_Master is the current child_id
                            item_id=item_to_insert_id,
                            comp_id=comp_id,
                            fin_year_id__lte=fin_year_id
                        ).exists()
                        if already_in_bom:
                            messages.warning(request, f"Item {temp_item.get_display_item_code()} already exists in BOM.")
                            continue # Skip to next item

                        BomMaster.objects.create(
                            sys_date=current_date,
                            sys_time=current_time,
                            comp_id=comp_id,
                            fin_year_id=fin_year_id,
                            session_id=session_id,
                            wono=wono,
                            pid=child_id, # PId in BOM_Master is the current child_id
                            cid=next_cid, # New CId for this item
                            item_id=item_to_insert_id,
                            qty=temp_item.qty,
                            ecn_flag=temp_item.ecn_flag
                        )
                    else: # New user-defined item (no ItemId in BomItemTemp)
                        # Check if it already exists in ItemMaster
                        existing_master_item = ItemMaster.objects.filter(
                            part_no=temp_item.part_no,
                            comp_id=comp_id,
                            fin_year_id__lte=fin_year_id
                        ).first()

                        if not existing_master_item:
                            # Insert into tblDG_Item_Master
                            ItemMaster.objects.create(
                                sys_date=current_date,
                                sys_time=current_time,
                                comp_id=comp_id,
                                fin_year_id=fin_year_id,
                                session_id=session_id,
                                part_no=temp_item.part_no,
                                item_code=temp_item.item_code,
                                manf_desc=temp_item.manf_desc,
                                uom_basic=temp_item.uom_basic,
                                opening_bal_date=datetime.strptime(settings.DEFAULT_OPENING_DATE, '%Y-%m-%d').date(), # Assumed from fun.getOpeningDate()
                                file_name=temp_item.img_name,
                                file_size=temp_item.img_size,
                                content_type=temp_item.img_content_type,
                                file_data=temp_item.img_file,
                                att_name=temp_item.spec_sheet_name,
                                att_size=temp_item.spec_sheet_size,
                                att_content_type=temp_item.spec_sheet_content_type,
                                att_data=temp_item.spec_sheet_data
                            )
                            item_to_insert_id = ItemMaster.objects.filter(
                                part_no=temp_item.part_no, comp_id=comp_id
                            ).order_by('-id').first() # Get the newly created item

                        else:
                            item_to_insert_id = existing_master_item

                        if item_to_insert_id:
                            # Check if item already exists in permanent BOM_Master for this parent
                            already_in_bom = BomMaster.objects.filter(
                                wono=wono,
                                pid=child_id,
                                item_id=item_to_insert_id,
                                comp_id=comp_id,
                                fin_year_id__lte=fin_year_id
                            ).exists()
                            if already_in_bom:
                                messages.warning(request, f"New Item {temp_item.part_no} already exists in BOM.")
                                continue # Skip to next item

                            BomMaster.objects.create(
                                sys_date=current_date,
                                sys_time=current_time,
                                comp_id=comp_id,
                                fin_year_id=fin_year_id,
                                session_id=session_id,
                                wono=wono,
                                pid=child_id,
                                cid=next_cid,
                                item_id=item_to_insert_id,
                                qty=temp_item.qty,
                                part_no=temp_item.part_no,
                                equipment_no=temp_item.equipment_no,
                                unit_no=temp_item.unit_no,
                                ecn_flag=temp_item.ecn_flag
                            )

                    # Update WorkOrderMaster 'UpdateWO' field
                    WorkOrderMaster.objects.filter(wono=wono, comp_id=comp_id).update(update_wo=1)

                    # --- ECN Transfer Logic ---
                    ecn_master_temp_records = EcnMasterTemp.objects.filter(
                        mid=temp_item, # Linked by BomItemTemp instance
                        session_id=session_id,
                        comp_id=comp_id
                    )

                    for ecn_temp_master in ecn_master_temp_records:
                        permanent_ecn_item_id = item_to_insert_id.id if item_to_insert_id else 0 # 0 if not inserted
                        
                        ecn_master_perm = EcnMaster.objects.create(
                            sys_date=current_date,
                            sys_time=current_time,
                            comp_id=comp_id,
                            fin_year_id=fin_year_id,
                            session_id=session_id,
                            item_id_id=permanent_ecn_item_id, # Use _id for direct assignment
                            wono=ecn_temp_master.wono,
                            pid=ecn_temp_master.pid,
                            cid=ecn_temp_master.cid
                        )

                        ecn_details_temp_records = EcnDetailsTemp.objects.filter(mid=ecn_temp_master)
                        for ecn_temp_detail in ecn_details_temp_records:
                            EcnDetails.objects.create(
                                mid=ecn_master_perm,
                                ecn_reason=ecn_temp_detail.ecn_reason,
                                remarks=ecn_temp_detail.remarks
                            )
                    
                    # Delete temporary ECN records
                    ecn_master_temp_records.delete() # Cascade delete should handle details

                # Clear all temporary BomItemTemp records for this session/WO/ChildId
                BomItemTemp.objects.filter(
                    comp_id=comp_id,
                    session_id=session_id,
                    wono=wono,
                    child_id=child_id # Clear only items related to this specific assembly's childid
                ).delete()
                
                messages.success(request, "All selected items added to BOM successfully and temporary data cleared.")
                return HttpResponse(
                    status=200,
                    headers={
                        'HX-Redirect': reverse_lazy('bom_management:bom_design_wo_tree_view') + f"?WONo={wono}&ModId=3&SubModId=26",
                        'HX-Trigger': 'showMessage'
                    }
                ) # Redirect to the tree view as in ASP.NET
        except Exception as e:
            messages.error(request, f"Failed to add items to BOM: {e}")
            return HttpResponse(status=200, headers={'HX-Trigger': 'showMessage'})

class CancelBOMView(View):
    """
    Handles Cancel button click.
    Replicates btnCancel_Click and Button1_Click1.
    """
    def get(self, request, *args, **kwargs):
        common_ctx = get_common_context(self.request)
        wono = common_ctx['wono']
        messages.info(request, "Operation cancelled.")
        return HttpResponse(
            status=200,
            headers={
                'HX-Redirect': reverse_lazy('bom_management:bom_design_wo_tree_view') + f"?WONo={wono}&ModId=3&SubModId=26",
                'HX-Trigger': 'showMessage'
            }
        )

# Placeholder for BOM_Design_WO_TreeView.aspx
# In a real migration, this would be another Django view.
class BOMDesignWoTreeView(TemplateView):
    template_name = 'bom_management/bom_design_wo_tree_view.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['wono'] = self.request.GET.get('WONo')
        context['mod_id'] = self.request.GET.get('ModId')
        context['sub_mod_id'] = self.request.GET.get('SubModId')
        return context

# Placeholder for ECN_Master.aspx
# If this is a separate module, we'd have a dedicated Django app/views for it.
class ECNMasterAddView(TemplateView):
    template_name = 'bom_management/ecn_master/add.html' # Placeholder template

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['item_id'] = self.request.GET.get('ItemId')
        context['wono'] = self.request.GET.get('WONo')
        context['child_id'] = self.request.GET.get('CId')
        context['parent_id'] = self.request.GET.get('ParentId')
        context['qty'] = self.request.GET.get('Qty')
        context['assly_no'] = self.request.GET.get('asslyNo')
        messages.info(self.request, f"Redirected to ECN for Item ID: {context['item_id']}")
        return context

```

#### 4.4 Templates (`bom_management/templates/bom_management/bomitemtemp/`)

**Task:** Create HTML templates for each view and partials.

**`dashboard.html`** (Main Page)

```html
{% extends 'core/base.html' %}
{% load static %}

{% block head %}
    <style>
        .is-active {
            display: flex !important; /* Override 'hidden' for modal */
        }
    </style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-4">
    <div class="bg-blue-800 text-white p-2 rounded-t-lg font-bold text-lg flex justify-between items-center">
        <span>&nbsp;BOM Items</span>
        <span>
            WoNo:&nbsp;{{ wono }}&nbsp;&nbsp;&nbsp;
            Assly No:&nbsp;{{ assly_no }}
        </span>
    </div>

    <div x-data="{ activeTab: 'item_master' }" class="bg-white shadow-lg rounded-b-lg p-2">
        <!-- Tab Headers -->
        <div class="flex border-b border-gray-200">
            <button @click="activeTab = 'item_master'"
                    :class="{ 'border-b-2 border-blue-500 text-blue-700': activeTab === 'item_master', 'text-gray-600 hover:text-blue-500': activeTab !== 'item_master' }"
                    class="py-2 px-4 text-sm font-medium focus:outline-none"
                    hx-get="{% url 'bom_management:item_master_table_partial' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
                    hx-target="#tab-content"
                    hx-swap="innerHTML"
                    hx-trigger="click once"
                    _="on htmx:afterOnLoad add .is-active to #modal if event.detail.xhr.getResponseHeader('HX-Trigger') == 'showEcnModal'">
                Item Master
            </button>
            <button @click="activeTab = 'new_items'"
                    :class="{ 'border-b-2 border-blue-500 text-blue-700': activeTab === 'new_items', 'text-gray-600 hover:text-blue-500': activeTab !== 'new_items' }"
                    class="py-2 px-4 text-sm font-medium focus:outline-none"
                    hx-get="{% url 'bom_management:new_item_form_partial' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
                    hx-target="#tab-content"
                    hx-swap="innerHTML"
                    hx-trigger="click once">
                New Items
            </button>
            <button @click="activeTab = 'copy_from'"
                    :class="{ 'border-b-2 border-blue-500 text-blue-700': activeTab === 'copy_from', 'text-gray-600 hover:text-blue-500': activeTab !== 'copy_from' }"
                    class="py-2 px-4 text-sm font-medium focus:outline-none">
                Copy From
            </button>
            <button @click="activeTab = 'selected_items'"
                    :class="{ 'border-b-2 border-blue-500 text-blue-700': activeTab === 'selected_items', 'text-gray-600 hover:text-blue-500': activeTab !== 'selected_items' }"
                    class="py-2 px-4 text-sm font-medium focus:outline-none"
                    hx-get="{% url 'bom_management:selected_items_table_partial' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
                    hx-target="#tab-content"
                    hx-swap="innerHTML"
                    hx-trigger="click once, refreshSelectedItemsTable from:body">
                Selected Items
            </button>
        </div>

        <!-- Tab Content -->
        <div id="tab-content" class="py-4">
            <!-- Initial content for the first tab (Item Master) -->
            {% include 'bom_management/bomitemtemp/_item_master_table.html' with item_masters=item_master_search_form.initial_queryset add_existing_item_form=add_existing_item_form %}
        </div>
    </div>

    <div class="mt-4 py-2 flex justify-center space-x-4">
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-post="{% url 'bom_management:finalize_bom' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
            hx-confirm="Are you sure you want to finalize BOM?"
            hx-indicator="#loadingSpinner"
            hx-swap="none"
            _="on htmx:afterOnLoad if event.detail.xhr.getResponseHeader('HX-Redirect') then window.location.href = event.detail.xhr.getResponseHeader('HX-Redirect')"
            >
            Add to BOM
        </button>
        <button
            class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'bom_management:cancel_bom' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
            hx-indicator="#loadingSpinner"
            hx-swap="none"
            _="on htmx:afterOnLoad if event.detail.xhr.getResponseHeader('HX-Redirect') then window.location.href = event.detail.xhr.getResponseHeader('HX-Redirect')">
            Cancel
        </button>
        <div id="loadingSpinner" class="htmx-indicator ml-3">
            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <span class="ml-2 text-gray-700">Processing...</span>
        </div>
    </div>

    <!-- Modal for ECN or other dynamic forms -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-xl w-full">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.tailwindcss.min.js"></script>
<link href="https://cdn.datatables.net/2.0.7/css/dataTables.tailwindcss.min.css" rel="stylesheet">
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('bomApp', () => ({
            activeTab: 'item_master',
            init() {
                // Initial load for item_master tab
                htmx.trigger(this.$el.querySelector('button[hx-target="#tab-content"][hx-get*="item-master-table"]'), 'click');
            },
            // You can add more Alpine.js state or methods here if needed
        }));
    });

    // Custom event listener for showing messages
    document.body.addEventListener('showMessage', function(evt) {
        // This assumes Django messages are handled elsewhere (e.g., in base.html)
        // or you implement a custom toast notification system here.
        // For now, it simply implies that a message has been added to Django's messages framework.
        console.log('HTMX triggered showMessage event');
        // You might want to re-render the messages block in base.html or trigger a toast.
        // Example: htmx.ajax('GET', '/messages/', { target: '#message-container', swap: 'outerHTML' });
    });

    // Custom event listener for showing ECN modal (redirect logic)
    document.body.addEventListener('showEcnModal', function(evt) {
        const redirectUrl = evt.detail.xhr.getResponseHeader('HX-Redirect');
        if (redirectUrl) {
            window.location.href = redirectUrl; // Perform the redirect
        }
    });

    // Custom event listener for refreshing new item form after successful submission
    document.body.addEventListener('refreshNewItemForm', function(evt) {
        htmx.ajax('GET', '{% url "bom_management:new_item_form_partial" %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}', { target: '#tab-content', swap: 'innerHTML' });
        // After form refresh, ensure the 'new_items' tab is visually active if it wasn't already.
        // This might require more advanced Alpine.js integration or a direct DOM manipulation.
        // For now, it assumes the user is on the "New Items" tab.
    });

</script>
{% endblock %}
```

**`_item_master_table.html`** (Partial for Item Master Tab Grid)

```html
{% load static %}

<div class="space-y-4">
    <!-- Item Master Search Form Filters -->
    <div id="item-master-filters"
         hx-target="#item-master-table-container"
         hx-swap="outerHTML">
        <form hx-get="{% url 'bom_management:item_master_table_partial' %}"
              hx-target="#item-master-table-container"
              hx-swap="outerHTML"
              hx-trigger="submit">
            {% csrf_token %}
            <div class="flex flex-wrap items-end gap-4 p-4 bg-gray-50 rounded-lg shadow-sm">
                <div class="flex-shrink-0">
                    <label for="{{ item_master_search_form.type.id_for_label }}" class="block text-sm font-medium text-gray-700">Type</label>
                    {{ item_master_search_form.type }}
                </div>
                {% if item_master_search_form.type.value == 'Category' %}
                <div class="flex-shrink-0" id="category-field">
                    <label for="{{ item_master_search_form.category.id_for_label }}" class="block text-sm font-medium text-gray-700">Category</label>
                    {{ item_master_search_form.category }}
                </div>
                {% endif %}
                <div class="flex-shrink-0">
                    <label for="{{ item_master_search_form.search_code.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By</label>
                    {{ item_master_search_form.search_code }}
                </div>
                {% if item_master_search_form.search_code.value == 'tblDG_Item_Master.Location' %}
                <div class="flex-shrink-0" id="location-field">
                    <label for="{{ item_master_search_form.location.id_for_label }}" class="block text-sm font-medium text-gray-700">Location</label>
                    {{ item_master_search_form.location }}
                </div>
                {% elif item_master_search_form.search_code.value and item_master_search_form.search_code.value != 'Select' %}
                <div class="flex-shrink-0" id="search-text-field">
                    <label for="{{ item_master_search_form.search_text.id_for_label }}" class="block text-sm font-medium text-gray-700">Search Term</label>
                    {{ item_master_search_form.search_text }}
                </div>
                {% endif %}
                <div class="flex-shrink-0">
                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Search
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Item Master Data Table -->
    <div id="item-master-table-container" class="overflow-x-auto">
        <table id="itemMasterTable" class="min-w-full bg-white border border-gray-200">
            <thead>
                <tr class="bg-gray-50">
                    <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                    <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Req Qty</th>
                    <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                    <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                    <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                </tr>
            </thead>
            <tbody>
                {% if item_masters %}
                    {% for item in item_masters %}
                    <tr class="hover:bg-gray-50">
                        <td class="py-2 px-4 border-b">{{ forloop.counter }}</td>
                        <td class="py-2 px-4 border-b">
                            <form hx-post="{% url 'bom_management:add_existing_item_to_temp' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
                                  hx-swap="none"
                                  hx-indicator="#loadingSpinner">
                                {% csrf_token %}
                                {{ add_existing_item_form.item_master_id.tag|attr:"value:"|add:item.id }}
                                {{ add_existing_item_form.qty }}
                                {% if add_existing_item_form.qty.errors %}
                                    <p class="text-red-500 text-xs mt-1">{{ add_existing_item_form.qty.errors|striptags }}</p>
                                {% endif %}
                                <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-3 mt-1 rounded text-xs">Add</button>
                            </form>
                        </td>
                        <td class="py-2 px-4 border-b">
                            <!-- Button for adding will be part of the form for qty -->
                        </td>
                        <td class="py-2 px-4 border-b">{{ item.item_code }}</td>
                        <td class="py-2 px-4 border-b">{{ item.manf_desc }}</td>
                        <td class="py-2 px-4 border-b">{{ item.uom_basic.symbol }}</td>
                        <td class="py-2 px-4 border-b">{{ item.location }}</td>
                    </tr>
                    {% endfor %}
                {% else %}
                    <tr>
                        <td colspan="7" class="py-4 text-center text-gray-500">No data to display !</td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>
</div>

<script>
    $(document).ready(function() {
        $('#itemMasterTable').DataTable({
            "pageLength": 14, // As per ASP.NET GridView2 PageSize
            "lengthMenu": [[10, 14, 25, 50, -1], [10, 14, 25, 50, "All"]],
            "searching": false, // Disable default search, using custom form
            "paging": true,
            "info": true,
            "ordering": true // Allow sorting
        });
    });
</script>
```

**`_new_item_form.html`** (Partial for New Items Tab Form)

```html
<div class="p-4 bg-white space-y-4">
    <div id="new-item-form-container">
        <form hx-post="{% url 'bom_management:new_item_form_partial' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
              hx-target="#tab-content"
              hx-swap="innerHTML"
              hx-encoding="multipart/form-data" {# Important for file uploads #}
              hx-indicator="#loadingSpinner">
            {% csrf_token %}
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="md:col-span-1">
                    <div class="mb-4">
                        <label for="id_equip_no_label" class="block text-sm font-medium text-gray-700">Equipment No :</label>
                        <span id="id_equip_no_label" class="text-gray-900 font-semibold">{{ equip_no_label }}</span>
                    </div>
                    <div class="mb-4">
                        <label for="{{ form.unit_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Unit No</label>
                        {{ form.unit_no }}
                        {% if form.unit_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.unit_no.errors }}</p>{% endif %}
                    </div>
                    <div class="mb-4">
                        <label for="{{ form.part_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Part No</label>
                        {{ form.part_no }}
                        {% if form.part_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.part_no.errors }}</p>{% endif %}
                    </div>
                    <div class="mb-4">
                        <label for="{{ form.manf_desc.id_for_label }}" class="block text-sm font-medium text-gray-700">Description</label>
                        {{ form.manf_desc }}
                        {% if form.manf_desc.errors %}<p class="text-red-500 text-xs mt-1">{{ form.manf_desc.errors }}</p>{% endif %}
                    </div>
                    <div class="mb-4">
                        <label for="{{ form.uom_basic.id_for_label }}" class="block text-sm font-medium text-gray-700">UOM</label>
                        {{ form.uom_basic }}
                        {% if form.uom_basic.errors %}<p class="text-red-500 text-xs mt-1">{{ form.uom_basic.errors }}</p>{% endif %}
                    </div>
                    <div class="mb-4">
                        <label for="{{ form.qty.id_for_label }}" class="block text-sm font-medium text-gray-700">Required Qty</label>
                        {{ form.qty }}
                        {% if form.qty.errors %}<p class="text-red-500 text-xs mt-1">{{ form.qty.errors }}</p>{% endif %}
                    </div>
                    <div class="mb-4">
                        <label for="{{ form.img_file_upload.id_for_label }}" class="block text-sm font-medium text-gray-700">Image</label>
                        {{ form.img_file_upload }}
                        {% if form.img_file_upload.errors %}<p class="text-red-500 text-xs mt-1">{{ form.img_file_upload.errors }}</p>{% endif %}
                    </div>
                    <div class="mb-4">
                        <label for="{{ form.spec_sheet_upload.id_for_label }}" class="block text-sm font-medium text-gray-700">Spec. Sheet</label>
                        {{ form.spec_sheet_upload }}
                        {% if form.spec_sheet_upload.errors %}<p class="text-red-500 text-xs mt-1">{{ form.spec_sheet_upload.errors }}</p>{% endif %}
                    </div>
                </div>

                <div class="md:col-span-1">
                    {% if is_design_finalized_before_current_date %}
                        <h4 class="text-md font-semibold text-gray-800 mb-3">ECN Reasons (Required)</h4>
                        <div id="ecn-reasons-grid-container" hx-trigger="load, refreshEcnReasons from:body"
                             hx-get="{% url 'bom_management:ecn_reasons_partial' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
                             hx-target="this" hx-swap="innerHTML">
                            <!-- ECN Reasons grid will be loaded here via HTMX -->
                            <div class="text-center">
                                <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                                <p class="mt-2">Loading ECN Reasons...</p>
                            </div>
                        </div>
                    {% else %}
                        <p class="text-gray-500 text-sm italic">ECN reasons not required as design is not yet finalized.</p>
                    {% endif %}
                </div>
            </div>

            <div class="mt-6 flex items-center justify-end space-x-4">
                <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Add
                </button>
                <button type="button" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                        hx-get="{% url 'bom_management:cancel_bom' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
                        hx-swap="none"
                        _="on click if event.detail.xhr.getResponseHeader('HX-Redirect') then window.location.href = event.detail.xhr.getResponseHeader('HX-Redirect')">
                    Cancel
                </button>
            </div>
             {% if form.non_field_errors %}
                <div class="text-red-500 text-sm mt-3">{{ form.non_field_errors }}</div>
            {% endif %}
        </form>
    </div>
</div>
```

**`_ecn_reasons_grid.html`** (Partial for ECN Reasons in New Items Tab)

```html
<div class="overflow-x-auto">
    <table id="ecnReasonsGrid" class="min-w-full bg-white border border-gray-200">
        <thead>
            <tr class="bg-gray-50">
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
            </tr>
        </thead>
        <tbody>
            {% if ecn_reasons %}
                {% for reason in ecn_reasons %}
                <tr class="hover:bg-gray-50">
                    <td class="py-2 px-4 border-b">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b text-center">
                        {% if is_design_finalized_before_current_date %}
                            <input type="checkbox" name="ecn_reason_{{ reason.id }}" class="form-checkbox h-4 w-4 text-blue-600">
                        {% else %}
                            <input type="checkbox" disabled class="form-checkbox h-4 w-4 text-gray-400">
                        {% endif %}
                    </td>
                    <td class="py-2 px-4 border-b">{{ reason.types }}</td>
                    <td class="py-2 px-4 border-b">
                        {% if is_design_finalized_before_current_date %}
                            <input type="text" name="remarks_{{ reason.id }}" class="box3 w-full text-sm" placeholder="Remarks">
                        {% else %}
                            <input type="text" disabled class="box3 w-full text-sm bg-gray-100" placeholder="Remarks (disabled)">
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="4" class="py-4 text-center text-gray-500">No ECN reasons to display.</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        $('#ecnReasonsGrid').DataTable({
            "paging": false,
            "info": false,
            "searching": false,
            "ordering": false // No sorting for this grid per original
        });
    });
</script>
```

**`_selected_items_table.html`** (Partial for Selected Items Tab Grid)

```html
<div class="overflow-x-auto">
    <table id="selectedItemsTable" class="min-w-full bg-white border border-gray-200">
        <thead>
            <tr class="bg-gray-50">
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th class="py-2 px-4 border-b text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Assly Qty</th>
                <th class="py-2 px-4 border-b text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Req Qty</th>
                <th class="py-2 px-4 border-b text-right text-xs font-medium text-gray-500 uppercase tracking-wider">BOM Qty</th>
            </tr>
        </thead>
        <tbody>
            {% if selected_items %}
                {% for item in selected_items %}
                <tr class="hover:bg-gray-50">
                    <td class="py-2 px-4 border-b">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b text-center">
                        <button
                            class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs"
                            hx-get="{% url 'bom_management:delete_temp_bom_item' pk=item.id %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            Delete
                        </button>
                    </td>
                    <td class="py-2 px-4 border-b">{{ item.item_code }}</td>
                    <td class="py-2 px-4 border-b">{{ item.manf_desc }}</td>
                    <td class="py-2 px-4 border-b">{{ item.uom_basic }}</td>
                    <td class="py-2 px-4 border-b text-right">{{ item.assly_qty|floatformat:"3" }}</td> {# Display with 3 decimal places #}
                    <td class="py-2 px-4 border-b text-right">{{ item.qty|floatformat:"3" }}</td>
                    <td class="py-2 px-4 border-b text-right">{{ item.bom_qty|floatformat:"3" }}</td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="8" class="py-4 text-center text-gray-500">No data to display !</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        $('#selectedItemsTable').DataTable({
            "pageLength": 30, // As per ASP.NET GridView3 PageSize
            "lengthMenu": [[10, 25, 30, 50, -1], [10, 25, 30, 50, "All"]],
            "searching": true, // Allow client-side search
            "paging": true,
            "info": true,
            "ordering": true // Allow sorting
        });
    });
</script>
```

**`_confirm_delete_temp_item.html`** (Partial for Delete Confirmation Modal)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete this item "{{ object.get_display_manf_desc }}" from the temporary list?</p>
    <form hx-delete="{% url 'bom_management:delete_temp_bom_item' pk=object.id %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                _="on htmx:afterRequest remove .is-active from #modal">
                Delete
            </button>
        </div>
    </form>
</div>
```

**`_item_master_search_form_fields.html`** (Partial for dynamic search options in `ItemMasterTablePartialView`)
This is needed because `ItemMasterSearchOptionsPartialView` only renders portions.

```html
{# This template is rendered by ItemMasterSearchOptionsPartialView for HTMX swaps #}
{# It assumes it's being swapped into the #item-master-filters div #}
<form hx-get="{% url 'bom_management:item_master_table_partial' %}"
      hx-target="#item-master-table-container"
      hx-swap="outerHTML"
      hx-trigger="submit">
    {% csrf_token %}
    <div class="flex flex-wrap items-end gap-4 p-4 bg-gray-50 rounded-lg shadow-sm">
        <div class="flex-shrink-0">
            <label for="{{ form.type.id_for_label }}" class="block text-sm font-medium text-gray-700">Type</label>
            {{ form.type }}
        </div>
        {% if form.type.value == 'Category' %}
        <div class="flex-shrink-0" id="category-field">
            <label for="{{ form.category.id_for_label }}" class="block text-sm font-medium text-gray-700">Category</label>
            {{ form.category }}
        </div>
        {% endif %}
        <div class="flex-shrink-0">
            <label for="{{ form.search_code.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By</label>
            {{ form.search_code }}
        </div>
        {% if form.search_code.value == 'tblDG_Item_Master.Location' %}
        <div class="flex-shrink-0" id="location-field">
            <label for="{{ form.location.id_for_label }}" class="block text-sm font-medium text-gray-700">Location</label>
            {{ form.location }}
        </div>
        {% elif form.search_code.value and form.search_code.value != 'Select' %}
        <div class="flex-shrink-0" id="search-text-field">
            <label for="{{ form.search_text.id_for_label }}" class="block text-sm font-medium text-gray-700">Search Term</label>
            {{ form.search_text }}
        </div>
        {% endif %}
        <div class="flex-shrink-0">
            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Search
            </button>
        </div>
    </div>
</form>

<script>
    // Re-initialize select2/chosen if used, or bind any other JS if fields are dynamically added/removed
    // This script runs after HTMX swaps the content.
</script>
```

#### 4.5 URLs (`bom_management/urls.py`)

**Task:** Define URL patterns for the views.

```python
from django.urls import path
from .views import (
    BOMWoItemsDashboardView, ItemMasterTablePartialView,
    AddExistingItemToTempView, NewItemFormPartialView, ECNReasonsPartialView,
    SelectedItemsTablePartialView, DeleteTempBOMItemView,
    FinalizeBOMView, CancelBOMView,
    BOMDesignWoTreeView, ECNMasterAddView, ItemMasterSearchOptionsPartialView
)

app_name = 'bom_management' # Namespace for URLs

urlpatterns = [
    # Main Dashboard View
    path('bom-items/', BOMWoItemsDashboardView.as_view(), name='bom_items_dashboard'),

    # HTMX Partials and Actions for Item Master Tab
    path('bom-items/item-master/table/', ItemMasterTablePartialView.as_view(), name='item_master_table_partial'),
    path('bom-items/item-master/add/', AddExistingItemToTempView.as_view(), name='add_existing_item_to_temp'),
    path('bom-items/item-master/search-options/', ItemMasterSearchOptionsPartialView.as_view(), name='item_master_search_options'),

    # HTMX Partials and Actions for New Items Tab
    path('bom-items/new-items/form/', NewItemFormPartialView.as_view(), name='new_item_form_partial'),
    path('bom-items/new-items/ecn-reasons/', ECNReasonsPartialView.as_view(), name='ecn_reasons_partial'),

    # HTMX Partials and Actions for Selected Items Tab
    path('bom-items/selected-items/table/', SelectedItemsTablePartialView.as_view(), name='selected_items_table_partial'),
    path('bom-items/selected-items/delete/<int:pk>/', DeleteTempBOMItemView.as_view(), name='delete_temp_bom_item'),

    # Finalization and Cancel Actions
    path('bom-items/finalize/', FinalizeBOMView.as_view(), name='finalize_bom'),
    path('bom-items/cancel/', CancelBOMView.as_view(), name='cancel_bom'),

    # Placeholder for Redirects (assuming these are other modules/pages)
    path('bom-design-wo-tree-view/', BOMDesignWoTreeView.as_view(), name='bom_design_wo_tree_view'),
    path('ecn-master-add/', ECNMasterAddView.as_view(), name='ecn_master_add'), # Target for the ECN redirect
]
```

**Include these URLs in your project's `urls.py`:**

```python
# In your main project's urls.py
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('bom-management/', include('bom_management.urls')), # Your new app's URLs
    # ... other project urls
]
```

#### 4.6 Tests (`bom_management/tests.py`)

**Task:** Write comprehensive unit and integration tests.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from django.db import connection # For database transaction testing if needed
from datetime import date, datetime
from io import BytesIO

from .models import (
    UnitMaster, CategoryMaster, ItemMaster, BomItemTemp, EcnReason, WorkOrderMaster,
    BomMaster, EcnMasterTemp, EcnDetailsTemp, EcnMaster, EcnDetails
)

# Mock user session data and global context variables
# In a real application, you'd likely have a custom test client or mock authentication
# to set these session variables.
SESSION_DATA = {
    'compid': 1,
    'finyear': 1,
}
MOCK_USERNAME = 'testuser'
MOCK_ASSEMBLY_ID = 100 # Example ItemMaster ID for an assembly
MOCK_ASSEMBLY_NO = 'EQP-01-01' # Example assembly number format
MOCK_WO_NO = 'WO-2023-001'
MOCK_PARENT_ID = 1
MOCK_CHILD_ID = 2

class BOMManagementSetupMixin:
    """Mixin for setting up common test data."""
    @classmethod
    def setUpTestData(cls):
        # Create necessary master data
        cls.unit_ea = UnitMaster.objects.create(id=1, symbol='EA')
        cls.unit_kg = UnitMaster.objects.create(id=2, symbol='KG')
        
        cls.cat_mech = CategoryMaster.objects.create(cid=1, symbol='MECH', cname='Mechanical')
        cls.cat_elec = CategoryMaster.objects.create(cid=2, symbol='ELEC', cname='Electrical')

        cls.item_master_1 = ItemMaster.objects.create(
            id=101, comp_id=SESSION_DATA['compid'], fin_year_id=SESSION_DATA['finyear'],
            item_code='ITEM-001', manf_desc='Standard Bolt', uom_basic=cls.unit_ea, location='Warehouse A',
            sys_date=datetime.now(), sys_time='10:00:00'
        )
        cls.item_master_2 = ItemMaster.objects.create(
            id=102, comp_id=SESSION_DATA['compid'], fin_year_id=SESSION_DATA['finyear'],
            item_code='ITEM-002', manf_desc='Custom Wire', uom_basic=cls.unit_kg, location='Warehouse B',
            sys_date=datetime.now(), sys_time='10:00:00'
        )
        cls.item_master_assembly = ItemMaster.objects.create(
            id=MOCK_ASSEMBLY_ID, comp_id=SESSION_DATA['compid'], fin_year_id=SESSION_DATA['finyear'],
            item_code=MOCK_ASSEMBLY_NO, manf_desc='Main Assembly', uom_basic=cls.unit_ea, location='Main',
            sys_date=datetime.now(), sys_time='10:00:00'
        )

        cls.ecn_reason_1 = EcnReason.objects.create(id=1, types='Design Change', comp_id=SESSION_DATA['compid'])
        cls.ecn_reason_2 = EcnReason.objects.create(id=2, types='Material Change', comp_id=SESSION_DATA['compid'])

        cls.work_order_active = WorkOrderMaster.objects.create(
            wono=MOCK_WO_NO,
            task_design_finalization_tdate=date.today() + timedelta(days=7), # Future date, no ECN
            update_wo=0
        )
        cls.work_order_finalized = WorkOrderMaster.objects.create(
            wono='WO-FINALIZED',
            task_design_finalization_tdate=date.today() - timedelta(days=7), # Past date, ECN required
            update_wo=0
        )

class BomItemTempModelTest(BOMManagementSetupMixin, TestCase):
    def test_bom_item_temp_creation(self):
        item_temp = BomItemTemp.objects.create(
            id=1,
            comp_id=SESSION_DATA['compid'],
            session_id=self.client.session.session_key,
            wono=MOCK_WO_NO,
            item_id=self.item_master_1,
            qty=5.5,
            child_id=MOCK_CHILD_ID,
            ecn_flag=0
        )
        self.assertEqual(item_temp.wono, MOCK_WO_NO)
        self.assertEqual(item_temp.item_id, self.item_master_1)
        self.assertEqual(item_temp.qty, 5.5)

    def test_get_items_for_display_existing_item(self):
        BomItemTemp.objects.create(
            id=2,
            comp_id=SESSION_DATA['compid'],
            session_id=self.client.session.session_key,
            wono=MOCK_WO_NO,
            item_id=self.item_master_1,
            qty=5.5,
            child_id=MOCK_CHILD_ID,
            ecn_flag=0
        )
        # Mock calculate_assembly_qty for this test
        # Dynamically patch the method if it's external or complex
        # For simplicity, assume it's always 1.0 for this test
        with patch('bom_management.models.WorkOrderMaster.calculate_assembly_qty', return_value=1.0):
            displayed_items = BomItemTemp.objects.get_items_for_display(
                MOCK_WO_NO, MOCK_CHILD_ID, self.client.session.session_key,
                SESSION_DATA['compid'], SESSION_DATA['finyear']
            )
            self.assertEqual(len(displayed_items), 1)
            self.assertEqual(displayed_items[0]['item_code'], self.item_master_1.item_code)
            self.assertEqual(displayed_items[0]['manf_desc'], self.item_master_1.manf_desc)
            self.assertEqual(displayed_items[0]['uom_basic'], self.item_master_1.uom_basic.symbol)
            self.assertEqual(displayed_items[0]['assly_qty'], 1.0)
            self.assertEqual(displayed_items[0]['qty'], 5.5)
            self.assertEqual(displayed_items[0]['bom_qty'], 5.5) # 1.0 * 5.5

    def test_get_items_for_display_new_item(self):
        new_item_temp = BomItemTemp.objects.create(
            id=3,
            comp_id=SESSION_DATA['compid'],
            session_id=self.client.session.session_key,
            wono=MOCK_WO_NO,
            qty=10.0,
            child_id=MOCK_CHILD_ID,
            part_no='EQP-01-05',
            manf_desc='New Custom Part',
            uom_basic=self.unit_kg,
            ecn_flag=1
        )
        with patch('bom_management.models.WorkOrderMaster.calculate_assembly_qty', return_value=2.0):
            displayed_items = BomItemTemp.objects.get_items_for_display(
                MOCK_WO_NO, MOCK_CHILD_ID, self.client.session.session_key,
                SESSION_DATA['compid'], SESSION_DATA['finyear']
            )
            self.assertEqual(len(displayed_items), 1)
            self.assertEqual(displayed_items[0]['item_code'], new_item_temp.part_no)
            self.assertEqual(displayed_items[0]['manf_desc'], new_item_temp.manf_desc)
            self.assertEqual(displayed_items[0]['uom_basic'], new_item_temp.uom_basic.symbol)
            self.assertEqual(displayed_items[0]['assly_qty'], 2.0)
            self.assertEqual(displayed_items[0]['qty'], 10.0)
            self.assertEqual(displayed_items[0]['bom_qty'], 20.0) # 2.0 * 10.0


class BOMWoItemsViewsTest(BOMManagementSetupMixin, TestCase):
    def setUp(self):
        self.client = Client()
        session = self.client.session
        session.update(SESSION_DATA)
        session.save()

        # Common query parameters for BOM context
        self.common_query_params = {
            'WONo': MOCK_WO_NO,
            'ItemId': MOCK_ASSEMBLY_ID, # ItemMaster ID of the assembly
            'PId': MOCK_PARENT_ID,
            'CId': MOCK_CHILD_ID,
        }
        self.dashboard_url = reverse('bom_management:bom_items_dashboard') + '?' + urlencode(self.common_query_params)
        self.item_master_table_url = reverse('bom_management:item_master_table_partial') + '?' + urlencode(self.common_query_params)
        self.add_existing_item_url = reverse('bom_management:add_existing_item_to_temp') + '?' + urlencode(self.common_query_params)
        self.new_item_form_url = reverse('bom_management:new_item_form_partial') + '?' + urlencode(self.common_query_params)
        self.selected_items_table_url = reverse('bom_management:selected_items_table_partial') + '?' + urlencode(self.common_query_params)
        self.finalize_bom_url = reverse('bom_management:finalize_bom') + '?' + urlencode(self.common_query_params)
        self.cancel_bom_url = reverse('bom_management:cancel_bom') + '?' + urlencode(self.common_query_params)

    def test_dashboard_view_get(self):
        response = self.client.get(self.dashboard_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom_management/bomitemtemp/dashboard.html')
        self.assertContains(response, f"WoNo:&nbsp;{MOCK_WO_NO}")
        self.assertContains(response, f"Assly No:&nbsp;{MOCK_ASSEMBLY_NO}")
        self.assertIsInstance(response.context['item_master_search_form'], ItemMasterSearchForm)
        self.assertIsInstance(response.context['new_bom_item_form'], NewBOMItemForm)

    def test_item_master_table_partial_view_get(self):
        response = self.client.get(self.item_master_table_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom_management/bomitemtemp/_item_master_table.html')
        self.assertContains(response, 'Item Code') # Check for table headers
        self.assertContains(response, self.item_master_1.item_code) # Check for existing item

    def test_item_master_table_partial_view_search(self):
        # Search by Description
        search_params = self.common_query_params.copy()
        search_params.update({
            'type': 'Category',
            'search_code': 'tblDG_Item_Master.ManfDesc',
            'search_text': 'Bolt'
        })
        response = self.client.get(reverse('bom_management:item_master_table_partial') + '?' + urlencode(search_params), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.item_master_1.item_code)
        self.assertNotContains(response, self.item_master_2.item_code)

    def test_add_existing_item_to_temp_success(self):
        response = self.client.post(
            self.add_existing_item_url,
            {'item_master_id': self.item_master_1.id, 'qty': 1.0},
            HTTP_HX_REQUEST='true',
            HTTP_X_CSRFTOKEN='test-token' # Django's CSRF token
        )
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertTrue(BomItemTemp.objects.filter(item_id=self.item_master_1, wono=MOCK_WO_NO).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertGreater(len(messages), 0)
        self.assertEqual(str(messages[0]), "Record has been Inserted.")
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshItemMasterTable', response.headers['HX-Trigger'])

    def test_add_existing_item_to_temp_qty_validation(self):
        response = self.client.post(
            self.add_existing_item_url,
            {'item_master_id': self.item_master_1.id, 'qty': 0.0},
            HTTP_HX_REQUEST='true',
            HTTP_X_CSRFTOKEN='test-token'
        )
        self.assertEqual(response.status_code, 200) # Form errors handled by 200 OK
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), "Req. Qty must be greater than zero.")
        self.assertFalse(BomItemTemp.objects.filter(item_id=self.item_master_1, wono=MOCK_WO_NO).exists())

    def test_add_existing_item_to_temp_already_inserted(self):
        # First add the item
        BomItemTemp.objects.create(
            comp_id=SESSION_DATA['compid'], session_id=self.client.session.session_key,
            wono=MOCK_WO_NO, item_id=self.item_master_1, qty=1.0, child_id=MOCK_CHILD_ID, ecn_flag=0
        )
        # Try to add again
        response = self.client.post(
            self.add_existing_item_url,
            {'item_master_id': self.item_master_1.id, 'qty': 1.0},
            HTTP_HX_REQUEST='true',
            HTTP_X_CSRFTOKEN='test-token'
        )
        self.assertEqual(response.status_code, 200)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), "Record Already Inserted")

    def test_add_existing_item_to_temp_ecn_required(self):
        # Set work order to be finalized (ECN required)
        self.work_order_finalized.wono = MOCK_WO_NO # Use same WO_NO for simplicity in test
        self.work_order_finalized.save()
        
        response = self.client.post(
            self.add_existing_item_url,
            {'item_master_id': self.item_master_1.id, 'qty': 1.0},
            HTTP_HX_REQUEST='true',
            HTTP_X_CSRFTOKEN='test-token'
        )
        self.assertEqual(response.status_code, 200) # HTMX might send 200 with headers for redirect
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), "ECN required for this item. Please provide reasons.")
        self.assertIn('HX-Redirect', response.headers)
        self.assertIn(reverse('bom_management:ecn_master_add'), response.headers['HX-Redirect'])
        
        # Verify item was NOT added to BomItemTemp if redirect happens *before* adding
        # The C# redirects first, so BomItemTemp would not be created yet.
        self.assertFalse(BomItemTemp.objects.filter(item_id=self.item_master_1).exists())


    def test_new_item_form_partial_get(self):
        response = self.client.get(self.new_item_form_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom_management/bomitemtemp/_new_item_form.html')
        self.assertContains(response, 'Unit No')
        self.assertContains(response, self.item_master_assembly.item_code.split('-')[0]) # Equip No label

    def test_new_item_form_partial_post_success(self):
        data = {
            'unit_no': '01',
            'part_no': '02',
            'manf_desc': 'New Gearbox Component',
            'uom_basic': self.unit_ea.id,
            'qty': 7.5,
        }
        response = self.client.post(
            self.new_item_form_url,
            data,
            HTTP_HX_REQUEST='true',
            HTTP_X_CSRFTOKEN='test-token'
        )
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertTrue(BomItemTemp.objects.filter(manf_desc='New Gearbox Component').exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), "New Item added successfully.")
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshSelectedItemsTable', response.headers['HX-Trigger'])

    def test_new_item_form_partial_post_ecn_required(self):
        # Set work order to be finalized (ECN required)
        wo_params = self.common_query_params.copy()
        wo_params['WONo'] = 'WO-FINALIZED' # Use the finalized WO
        finalized_new_item_form_url = reverse('bom_management:new_item_form_partial') + '?' + urlencode(wo_params)

        data = {
            'unit_no': '01',
            'part_no': '03', # Ensure unique
            'manf_desc': 'ECN Required Part',
            'uom_basic': self.unit_ea.id,
            'qty': 1.0,
            f'ecn_reason_{self.ecn_reason_1.id}': 'on', # Select ECN reason
            f'remarks_{self.ecn_reason_1.id}': 'Test remarks for ECN',
        }
        response = self.client.post(
            finalized_new_item_form_url,
            data,
            HTTP_HX_REQUEST='true',
            HTTP_X_CSRFTOKEN='test-token'
        )
        self.assertEqual(response.status_code, 204)
        new_item_temp = BomItemTemp.objects.get(manf_desc='ECN Required Part')
        self.assertTrue(new_item_temp.ecn_flag == 1)
        self.assertTrue(EcnMasterTemp.objects.filter(mid=new_item_temp).exists())
        self.assertTrue(EcnDetailsTemp.objects.filter(mid__mid=new_item_temp, ecn_reason=self.ecn_reason_1).exists())

    def test_new_item_form_partial_post_ecn_no_reasons(self):
        # Set work order to be finalized (ECN required)
        wo_params = self.common_query_params.copy()
        wo_params['WONo'] = 'WO-FINALIZED'
        finalized_new_item_form_url = reverse('bom_management:new_item_form_partial') + '?' + urlencode(wo_params)

        data = {
            'unit_no': '01',
            'part_no': '04',
            'manf_desc': 'ECN Required Part No Reasons',
            'uom_basic': self.unit_ea.id,
            'qty': 1.0,
            # No ECN reasons selected
        }
        response = self.client.post(
            finalized_new_item_form_url,
            data,
            HTTP_HX_REQUEST='true',
            HTTP_X_CSRFTOKEN='test-token'
        )
        self.assertEqual(response.status_code, 200) # Should return 200 OK with messages due to validation/warning
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), "Please enter reasons in ECN Grid if ECN is required.")
        self.assertFalse(EcnMasterTemp.objects.filter(mid__manf_desc='ECN Required Part No Reasons').exists()) # No ECN created

    def test_selected_items_table_partial_view_get(self):
        BomItemTemp.objects.create(
            id=4, comp_id=SESSION_DATA['compid'], session_id=self.client.session.session_key,
            wono=MOCK_WO_NO, item_id=self.item_master_1, qty=2.0, child_id=MOCK_CHILD_ID, ecn_flag=0
        )
        with patch('bom_management.models.WorkOrderMaster.calculate_assembly_qty', return_value=1.0):
            response = self.client.get(self.selected_items_table_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom_management/bomitemtemp/_selected_items_table.html')
        self.assertContains(response, self.item_master_1.item_code)
        self.assertContains(response, '2.000') # Qty
        self.assertContains(response, '2.000') # BOM Qty (1.0 * 2.0)

    def test_delete_temp_bom_item_get(self):
        item_temp = BomItemTemp.objects.create(
            id=5, comp_id=SESSION_DATA['compid'], session_id=self.client.session.session_key,
            wono=MOCK_WO_NO, item_id=self.item_master_2, qty=1.0, child_id=MOCK_CHILD_ID, ecn_flag=0
        )
        response = self.client.get(reverse('bom_management:delete_temp_bom_item', kwargs={'pk': item_temp.id}) + '?' + urlencode(self.common_query_params), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom_management/bomitemtemp/_confirm_delete_temp_item.html')
        self.assertContains(response, 'Confirm Delete')

    def test_delete_temp_bom_item_delete(self):
        item_temp = BomItemTemp.objects.create(
            id=6, comp_id=SESSION_DATA['compid'], session_id=self.client.session.session_key,
            wono=MOCK_WO_NO, item_id=self.item_master_2, qty=1.0, child_id=MOCK_CHILD_ID, ecn_flag=1
        )
        ecn_master_temp = EcnMasterTemp.objects.create(
            mid=item_temp, sys_date=date.today(), sys_time='12:00:00',
            comp_id=SESSION_DATA['compid'], fin_year_id=SESSION_DATA['finyear'],
            session_id=self.client.session.session_key, wono=MOCK_WO_NO,
            pid=MOCK_PARENT_ID, cid=MOCK_CHILD_ID, item_id=self.item_master_2
        )
        EcnDetailsTemp.objects.create(
            mid=ecn_master_temp, ecn_reason=self.ecn_reason_1, remarks='Delete Test'
        )

        response = self.client.delete(reverse('bom_management:delete_temp_bom_item', kwargs={'pk': item_temp.id}) + '?' + urlencode(self.common_query_params), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(BomItemTemp.objects.filter(id=item_temp.id).exists())
        self.assertFalse(EcnMasterTemp.objects.filter(id=ecn_master_temp.id).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), "Item deleted successfully from temporary list.")
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshSelectedItemsTable', response.headers['HX-Trigger'])

    def test_finalize_bom_empty_temp(self):
        response = self.client.post(self.finalize_bom_url, HTTP_HX_REQUEST='true', HTTP_X_CSRFTOKEN='test-token')
        self.assertEqual(response.status_code, 200)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), "No items to add to BOM.")

    def test_finalize_bom_success(self):
        BomItemTemp.objects.create(
            id=7, comp_id=SESSION_DATA['compid'], session_id=self.client.session.session_key,
            wono=MOCK_WO_NO, item_id=self.item_master_1, qty=1.5, child_id=MOCK_CHILD_ID, ecn_flag=0
        )
        BomItemTemp.objects.create(
            id=8, comp_id=SESSION_DATA['compid'], session_id=self.client.session.session_key,
            wono=MOCK_WO_NO, qty=2.0, child_id=MOCK_CHILD_ID, part_no='EQP-01-06',
            manf_desc='New Finalized Part', uom_basic=self.unit_ea, ecn_flag=1
        )
        # Add ECN temp for the new part
        new_item_temp_with_ecn = BomItemTemp.objects.get(id=8)
        ecn_master_temp = EcnMasterTemp.objects.create(
            mid=new_item_temp_with_ecn, sys_date=date.today(), sys_time='12:00:00',
            comp_id=SESSION_DATA['compid'], fin_year_id=SESSION_DATA['finyear'],
            session_id=self.client.session.session_key, wono=MOCK_WO_NO,
            pid=MOCK_PARENT_ID, cid=MOCK_CHILD_ID, item_id=None, item_code='EQP-01-060'
        )
        EcnDetailsTemp.objects.create(
            mid=ecn_master_temp, ecn_reason=self.ecn_reason_2, remarks='Finalization Test'
        )

        response = self.client.post(self.finalize_bom_url, HTTP_HX_REQUEST='true', HTTP_X_CSRFTOKEN='test-token')
        self.assertEqual(response.status_code, 200)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), "All selected items added to BOM successfully and temporary data cleared.")
        self.assertIn('HX-Redirect', response.headers)
        self.assertIn(reverse('bom_management:bom_design_wo_tree_view'), response.headers['HX-Redirect'])

        # Verify items moved to permanent tables
        self.assertTrue(BomMaster.objects.filter(item_id=self.item_master_1, wono=MOCK_WO_NO).exists())
        self.assertTrue(BomMaster.objects.filter(part_no='EQP-01-06', wono=MOCK_WO_NO).exists())
        self.assertTrue(ItemMaster.objects.filter(part_no='EQP-01-06').exists()) # New item now in master
        self.assertTrue(EcnMaster.objects.filter(wono=MOCK_WO_NO, item_id__part_no='EQP-01-06').exists())
        self.assertFalse(BomItemTemp.objects.filter(wono=MOCK_WO_NO, session_id=self.client.session.session_key).exists())
        self.assertFalse(EcnMasterTemp.objects.filter(wono=MOCK_WO_NO, session_id=self.client.session.session_key).exists())

    def test_cancel_bom_view(self):
        response = self.client.get(self.cancel_bom_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), "Operation cancelled.")
        self.assertIn('HX-Redirect', response.headers)
        self.assertIn(reverse('bom_management:bom_design_wo_tree_view'), response.headers['HX-Redirect'])

```

---

### Step 5: HTMX and Alpine.js Integration

**Instructions:** All interactions are designed to leverage HTMX for partial updates and Alpine.js for minimal client-side state/interactions.

*   **HTMX for dynamic content:**
    *   **Tab Switching:** `hx-get` on tab buttons fetches and `hx-target="#tab-content"` `hx-swap="innerHTML"` replaces the content of the tab area. `hx-trigger="click once"` ensures initial load, then `click` for subsequent.
    *   **Search and Filter:** The `ItemMasterSearchForm` uses `hx-get` on `submit` to reload the `_item_master_table.html` partial, which includes the refreshed search form and the DataTables table. Dynamic visibility of search fields (`category`, `search_text`, `location`) is handled in the `_item_master_search_form_fields.html` partial and re-rendered by `ItemMasterSearchOptionsPartialView`.
    *   **Adding Existing Item:** The "Add" button inside `_item_master_table.html` on each row is part of a `form` that `hx-post`s to `add_existing_item_to_temp`. `hx-swap="none"` prevents direct DOM changes, relying on `HX-Trigger` to refresh tables and show messages.
    *   **Adding New Item:** The `NewBOMItemForm` in `_new_item_form.html` uses `hx-post` to submit. On success, `HX-Trigger` `refreshSelectedItemsTable, refreshNewItemForm, showMessage` events. `refreshNewItemForm` will reload the form with fresh initial data (e.g., next part number).
    *   **Deleting Selected Item:** The "Delete" button in `_selected_items_table.html` uses `hx-get` to fetch the `_confirm_delete_temp_item.html` into a modal. The modal's form then uses `hx-delete` to perform the actual deletion. On success, `HX-Trigger` `refreshSelectedItemsTable, showMessage`.
    *   **Finalizing BOM / Cancel:** These buttons `hx-post` or `hx-get` to their respective views. They use `hx-swap="none"` and rely on `HX-Redirect` headers to navigate to the final BOM Tree View page.
    *   **Messages:** `HX-Trigger` on successful/failed operations includes `showMessage` to indicate that a Django message has been added, which can be picked up by a listener in `base.html` to display toast notifications.
    *   **Loading Indicators:** `hx-indicator="#loadingSpinner"` is used on relevant forms/buttons.

*   **Alpine.js for UI state:**
    *   **Modal Management:** An Alpine.js `x-data` component manages the visibility of the modal (`#modal`). Buttons use `_="on click add .is-active to #modal"` to show it and `_="on click remove .is-active from me"` (on the modal overlay itself) to hide it.
    *   **Tab Visual State:** `x-data="{ activeTab: 'item_master' }"` in `dashboard.html` visually highlights the active tab. HTMX handles loading content.

*   **DataTables for List Views:**
    *   Both `itemMasterTable` and `selectedItemsTable` are initialized with jQuery DataTables.
    *   `pageLength` and `lengthMenu` are set according to the original ASP.NET `PageSize`.
    *   Client-side searching and pagination are enabled. For `itemMasterTable`, `searching` is disabled because server-side filtering is driven by the custom form.

---

### Final Notes

*   **`clsFunctions` Reimplementation:** The `clsFunctions` object in ASP.NET, which handled database operations, date/time functions, and specific business logic (like `RecurQty`, `getBOMCId`, `GetItemCode_PartNo`, `drpunit`), has been re-architected. Database interactions are replaced by Django's ORM, and complex business logic is moved into custom model managers or model methods, adhering to the "fat model" principle. Utility functions like `get_common_context` abstract common session/request parameters.
*   **Transactional Integrity:** The critical `AddToTPLBOM` logic (migrated to `FinalizeBOMView`) is wrapped in `django.db.transaction.atomic()` to ensure all database operations are atomic, preventing data inconsistencies.
*   **File Handling:** Binary file data (`ImgFile`, `SpecSheetData`) is handled using `BinaryField` in models and `FileField` in forms.
*   **Security:** Django's built-in CSRF protection is automatically applied with `{% csrf_token %}` and verified by Django. HTMX requests are designed to leverage this.
*   **Placeholders:** `settings.DEFAULT_OPENING_DATE` is a placeholder for `fun.getOpeningDate()`. `WorkOrderMaster.calculate_assembly_qty` is a placeholder for `fun.RecurQty()`. These would need concrete implementations based on your ERP's specific logic.
*   **UI Details:** CSS classes like `box3`, `redbox`, `fontcsswhite` need to be mapped to your Tailwind CSS configuration for consistent styling. The provided code assumes Tailwind CSS is configured.
*   **`HTMX-Trigger` and `showMessage`:** For displaying messages, you'd typically have a small Alpine.js component or JavaScript in your `base.html` that listens for the `showMessage` event and displays Django's `messages` in a toast or similar non-blocking notification.
*   **IFrame for "Copy From":** The original `<iframe>` in `TabPanel3` is noted. If `BOM_Design_CopyWo.aspx` were also migrated to Django, it would become a Django view rendered within a similar `<iframe>` or ideally, converted to a HTMX/Alpine.js partial as well for a seamless experience. For this migration, it's identified as an external dependency.