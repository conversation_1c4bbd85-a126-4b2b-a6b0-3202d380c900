## ASP.NET to Django Modernization Plan: ECN Master Management

This document outlines a strategic plan to transition your legacy ASP.NET ECN Master management module to a modern Django-based solution. Our approach prioritizes automated conversion, business value, and a streamlined user experience, moving away from complex, tightly coupled code towards a clean, maintainable, and scalable architecture.

### Business Value & Modernization Goals:

*   **Enhanced Maintainability:** Break down monolithic ASP.NET code into modular Django components (models, views, templates), simplifying debugging and future development.
*   **Improved User Experience:** Implement dynamic, responsive interfaces using HTMX and Alpine.js, eliminating full page reloads and providing a smoother workflow.
*   **Scalability & Performance:** Leverage Django's robust ORM and efficient server-side processing, coupled with client-side DataTables, to handle growing data volumes and user traffic.
*   **Reduced Development Costs:** By automating migration steps and adopting standardized patterns, we significantly lower the manual effort and error rate associated with traditional code rewrites.
*   **Future-Proofing:** Transition to an actively maintained, open-source framework with a thriving community, ensuring long-term viability and access to modern tools and practices.

---

### Conversion Steps:

#### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns based on the ASP.NET code's data interactions.

**Instructions:**
The ASP.NET code interacts with several key tables for data retrieval and modification. We've identified the following:

*   **`tblDG_ECN_Reason`**: This table provides the list of reasons displayed in the grid (`GridView1`).
    *   Columns inferred: `Id` (Primary Key, integer), `Types` (text, for 'Description'), `CompId` (integer).
*   **`tblDG_BOM_Master`**: This table stores Bill of Material (BOM) master records and is updated during the ECN process.
    *   Columns inferred: `Id` (Primary Key, integer), `CompId` (integer), `FinYearId` (integer), `ItemId` (integer), `WONo` (text), `CId` (integer), `AmdNo` (integer), `Qty` (text/decimal), `Revision` (text), `ECNFlag` (boolean/integer, `1` indicates true), `SysDate` (date), `SysTime` (time), `SessionId` (text), `PId` (integer).
*   **`tblDG_BOM_Amd`**: This table logs amendments made to BOMs.
    *   Columns inferred: `SysDate` (date), `SysTime` (time), `SessionId` (text), `CompId` (integer), `FinYearId` (integer), `WONo` (text), `BOMId` (integer, likely foreign key to `tblDG_BOM_Master`), `PId` (integer), `CId` (integer), `ItemId` (integer), `AmdNo` (integer), `Qty` (text/decimal).
*   **`tblDG_ECN_Master`**: This table stores the main ECN (Engineering Change Notice) records.
    *   Columns inferred: `Id` (Primary Key, integer), `SysDate` (date), `SysTime` (time), `CompId` (integer), `FinYearId` (integer), `SessionId` (text), `ItemId` (integer), `WONo` (text), `PId` (integer), `CId` (integer).
*   **`tblDG_ECN_Details`**: This table stores the details of each ECN, linking to reasons and remarks.
    *   Columns inferred: `Id` (Primary Key, integer), `MId` (integer, likely foreign key to `tblDG_ECN_Master`), `ECNReason` (integer, likely foreign key to `tblDG_ECN_Reason`), `Remarks` (text).

#### Step 2: Identify Backend Functionality

**Task:** Determine the core operations (Create, Read, Update, Delete) and business logic.

**Instructions:**
The ASP.NET page primarily facilitates a complex "creation" process that involves multiple database interactions:

*   **Read (`loaddata()`):** The page reads a list of ECN Reasons from `tblDG_ECN_Reason` to display in a selectable grid.
*   **Create/Update (`GridView1_RowCommand` with `CommandName="Ins"`):** This is the central operation. When the "Insert" button is clicked:
    *   It iterates through selected ECN reasons (`tblDG_ECN_Reason`).
    *   For the *first* selected reason, it initiates a complex BOM amendment process:
        *   Retrieves existing BOM data from `tblDG_BOM_Master`.
        *   Calculates a new `AmendmentNo`.
        *   Inserts a new record into `tblDG_BOM_Amd` (logging the original BOM state).
        *   Updates the `tblDG_BOM_Master` record with new `Qty`, `AmendmentNo`, `Revision`, and sets `ECNFlag` to `1` (True).
    *   It then inserts a new master ECN record into `tblDG_ECN_Master`, capturing system details and parameters from the original request (`WONo`, `ItemId`, `CId`, `PId`).
    *   For *every* selected ECN reason (including the first), it inserts a corresponding detail record into `tblDG_ECN_Details`, linking it to the newly created ECN Master and including the user-provided `Remarks`.
*   **Navigation/Cancellation (`BtnCancel_Click`):** This button simply redirects the user to another page, effectively cancelling the current operation.

**Key Business Logic:** The intricate sequence of BOM amendments and ECN master/detail creation, triggered by the selection of ECN reasons, is the core business logic. This will be encapsulated within Django models using the "Fat Model" approach.

#### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The user interface is centered around a `GridView` for displaying selectable ECN reasons:

*   **`GridView1`:** This control serves as the primary data display and interaction component. In Django, this will be replaced by a standard HTML `<table>` enhanced with **DataTables** for sorting, searching, and pagination.
    *   **Checkbox (`CheckBox1`):** Allows users to select specific ECN reasons. This will be a standard HTML checkbox.
    *   **Label (`lblId`, `lblDesc`):** Displays data like `Id` (hidden) and `Description` (`Types`). These will be standard `<td>` elements in Django.
    *   **Textbox (`TxtRemarks`):** Allows users to input remarks for each selected reason. This will be an `<input type="text">` element.
*   **`BtnInsert`:** A button in the `GridView` footer that triggers the "Insert" (ECN creation/BOM amendment) process. This will be an HTMX-powered `<button type="submit">` within a form.
*   **`BtnCancel`:** A standard button for navigating away from the page. This will be an HTMX-powered `<button>` or a direct `<a href>` link.
*   **Styling:** The ASP.NET code uses `Css/styles.css`, `Css/StyleSheet.css`, `Css/yui-datatable.css`. In Django, we will replace these with **Tailwind CSS** for modern styling and **DataTables** CSS for grid presentation.

---

### Step 4: Generate Django Code

We will create a Django application named `ecn_management` to house the new components.

#### 4.1 Models (`ecn_management/models.py`)

This section defines the Django models, mapping them to your existing database tables. The complex business logic for processing ECN requests, which was previously in the C# code-behind, is now encapsulated as a class method within the `ECNMaster` model. This adheres strictly to the "Fat Model" principle.

```python
from django.db import models
from django.utils import timezone
from django.db import transaction

class ECNReason(models.Model):
    # Id is the primary key and auto-managed by Django if not explicitly defined
    # but we will map it explicitly for clarity and consistency with existing DB.
    id = models.IntegerField(db_column='Id', primary_key=True)
    types = models.CharField(db_column='Types', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_ECN_Reason'
        verbose_name = 'ECN Reason'
        verbose_name_plural = 'ECN Reasons'

    def __str__(self):
        return self.types or f"ECN Reason {self.id}"

class BOMMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    item_id = models.IntegerField(db_column='ItemId', blank=True, null=True)
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    c_id = models.IntegerField(db_column='CId', blank=True, null=True)
    amd_no = models.CharField(db_column='AmdNo', max_length=50, blank=True, null=True) # Changed from int to char based on usage in C#
    qty = models.CharField(db_column='Qty', max_length=50, blank=True, null=True) # Assuming string based on C#
    revision = models.CharField(db_column='Revision', max_length=50, blank=True, null=True)
    ecn_flag = models.BooleanField(db_column='ECNFlag', blank=True, null=True) # 1 in ASP.NET usually means bool
    sys_date = models.CharField(db_column='SysDate', max_length=20, blank=True, null=True) # Date stored as string in ASP.NET
    sys_time = models.CharField(db_column='SysTime', max_length=20, blank=True, null=True) # Time stored as string in ASP.NET
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)
    p_id = models.IntegerField(db_column='PId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'BOM Master'
        verbose_name_plural = 'BOM Masters'

    def __str__(self):
        return f"BOM Master {self.wo_no} - {self.item_id}"

class BOMAmendment(models.Model):
    # No Id column in ASP.NET insert, assuming it's auto-increment PK
    id = models.AutoField(db_column='Id', primary_key=True) # Assuming auto-increment
    sys_date = models.CharField(db_column='SysDate', max_length=20, blank=True, null=True)
    sys_time = models.CharField(db_column='SysTime', max_length=20, blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    bom_id = models.IntegerField(db_column='BOMId', blank=True, null=True) # FK to BOMMaster.Id
    p_id = models.IntegerField(db_column='PId', blank=True, null=True)
    c_id = models.IntegerField(db_column='CId', blank=True, null=True)
    item_id = models.IntegerField(db_column='ItemId', blank=True, null=True)
    amd_no = models.CharField(db_column='AmdNo', max_length=50, blank=True, null=True)
    qty = models.CharField(db_column='Qty', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Amd'
        verbose_name = 'BOM Amendment'
        verbose_name_plural = 'BOM Amendments'

    def __str__(self):
        return f"BOM Amendment for {self.wo_no} (BOM ID: {self.bom_id})"

class ECNMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.CharField(db_column='SysDate', max_length=20, blank=True, null=True)
    sys_time = models.CharField(db_column='SysTime', max_length=20, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)
    item_id = models.IntegerField(db_column='ItemId', blank=True, null=True)
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    p_id = models.IntegerField(db_column='PId', blank=True, null=True)
    c_id = models.IntegerField(db_column='CId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_ECN_Master'
        verbose_name = 'ECN Master'
        verbose_name_plural = 'ECN Masters'

    def __str__(self):
        return f"ECN Master {self.id} for {self.wo_no}"

    @classmethod
    @transaction.atomic # Ensure all operations in this method are atomic
    def process_ecn_request(cls, current_session, item_id, wo_no, child_id, quantity, revision, parent_id, ass_id, selected_reasons_data):
        """
        Processes the ECN request, handling BOM amendments and ECN master/detail creation.
        This method encapsulates the complex business logic from the ASP.NET GridView1_RowCommand.

        Args:
            current_session (dict): Dictionary containing 'compid', 'finyear', 'username'
            item_id (int): ItemId from query string
            wo_no (str): WONo from query string
            child_id (int): CId from query string
            quantity (str): Qty from query string
            revision (str): Revision from query string
            parent_id (int): ParentId from query string
            ass_id (int): Id from query string (used for BOMMaster update)
            selected_reasons_data (list of dict): Each dict has 'id' (ECNReasonId) and 'remarks'
        """
        comp_id = current_session.get('compid')
        fin_year_id = current_session.get('finyear')
        session_id = current_session.get('username')

        now = timezone.now()
        # ASP.NET stored these as strings, so we will follow that for now.
        # In a new Django project, it's best to use DateTimeField.
        sys_date_str = now.strftime('%Y-%m-%d')
        sys_time_str = now.strftime('%H:%M:%S')

        # --- BOM Amendment Logic (matches ASP.NET cmdbom and subsequent updates) ---
        bom_master_qs = BOMMaster.objects.filter(
            comp_id=comp_id,
            fin_year_id__lte=fin_year_id,
            item_id=item_id,
            wo_no=wo_no,
            c_id=child_id
        ).first()

        amendment_no = "0"
        if bom_master_qs and bom_master_qs.amd_no is not None:
            try:
                amendment_no = str(int(bom_master_qs.amd_no) + 1)
            except (ValueError, TypeError):
                # Handle cases where AmdNo might not be a valid integer
                amendment_no = "1" # Start from 1 if conversion fails, or handle as per business rule

        if bom_master_qs:
            # Insert into tblDG_BOM_Amd (logs original BOM state)
            BOMAmendment.objects.create(
                sys_date=sys_date_str,
                sys_time=sys_time_str,
                session_id=session_id,
                comp_id=comp_id,
                fin_year_id=fin_year_id,
                wo_no=wo_no,
                bom_id=bom_master_qs.id,
                p_id=bom_master_qs.p_id,
                c_id=bom_master_qs.c_id,
                item_id=item_id,
                amd_no=bom_master_qs.amd_no, # Store original AmdNo
                qty=bom_master_qs.qty # Store original Qty
            )

            # Update tblDG_BOM_Master:
            # The ASP.NET code had two main updates to tblDG_BOM_Master for the same conditions.
            # We'll consolidate into one logical update that reflects the final state.
            BOMMaster.objects.filter(
                comp_id=comp_id,
                item_id=item_id,
                wo_no=wo_no,
                id=ass_id, # This 'Id' from query string corresponds to 'AssId' in C#
                c_id=child_id
            ).update(
                sys_date=sys_date_str,
                sys_time=sys_time_str,
                session_id=session_id,
                qty=quantity,
                amd_no=amendment_no,
                revision=revision,
                ecn_flag=True
            )
            
            # The ASP.NET code also had a separate update for 'Revision' on broader criteria.
            # This is applied if the initial BOMMaster query (DS10) had results.
            # This logic suggests that the `revision` applies to all BOM Masters linked to this WONo and ItemId that have ECNFlag=1.
            # We ensure this broader update also happens.
            BOMMaster.objects.filter(
                comp_id=comp_id,
                item_id=item_id,
                wo_no=wo_no,
                ecn_flag=True
            ).update(
                revision=revision
            )


        # --- Insert into tblDG_ECN_Master ---
        ecn_master_instance = cls.objects.create(
            sys_date=sys_date_str,
            sys_time=sys_time_str,
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            session_id=session_id,
            item_id=item_id,
            wo_no=wo_no,
            p_id=parent_id,
            c_id=child_id
        )

        # --- Insert into tblDG_ECN_Details for each selected reason ---
        ecn_details_to_create = []
        for reason_data in selected_reasons_data:
            ecn_details_to_create.append(
                ECNDetail(
                    m_id=ecn_master_instance, # Foreign key to ECNMaster
                    ecn_reason=ECNReason.objects.get(pk=reason_data['id']), # Foreign key to ECNReason
                    remarks=reason_data['remarks']
                )
            )
        ECNDetail.objects.bulk_create(ecn_details_to_create) # Efficiently create multiple details

        return ecn_master_instance

class ECNDetail(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True) # Assuming auto-increment
    m_id = models.ForeignKey(ECNMaster, on_delete=models.CASCADE, db_column='MId', related_name='details') # Foreign key to ECNMaster
    ecn_reason = models.ForeignKey(ECNReason, on_delete=models.SET_NULL, db_column='ECNReason', blank=True, null=True, related_name='ecn_details') # Foreign key to ECNReason
    remarks = models.CharField(db_column='Remarks', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_ECN_Details'
        verbose_name = 'ECN Detail'
        verbose_name_plural = 'ECN Details'

    def __str__(self):
        return f"Detail for ECN {self.m_id_id} - Reason: {self.ecn_reason.types}"

```

#### 4.2 Forms (`ecn_management/forms.py`)

Since the "form" is embedded within a list and collects data from multiple rows, we won't use a standard `ModelForm`. Instead, we'll define a simple `Form` that facilitates data validation after the POST request. This form will not render any fields, but rather validate the structure of the data submitted from the HTMX table.

```python
from django import forms

class ECNReasonProcessForm(forms.Form):
    # This form doesn't define fields for rendering,
    # but for validating the data received from the HTMX submission.
    # The actual fields (checkboxes, remarks) are rendered directly in the template.
    
    # We expect 'selected_reasons' to be a list of dicts, each with 'id' and 'remarks'
    selected_reasons = forms.JSONField() 
    
    # Query string parameters that are passed as hidden fields
    won_o = forms.CharField(max_length=50)
    item_id = forms.IntegerField()
    child_id = forms.IntegerField()
    quantity = forms.CharField(max_length=50) # Matching existing DB schema
    revision = forms.CharField(max_length=50)
    parent_id = forms.IntegerField()
    ass_id = forms.IntegerField() # This is the 'Id' from query string in C#

    def clean_selected_reasons(self):
        data = self.cleaned_data['selected_reasons']
        if not isinstance(data, list):
            raise forms.ValidationError("Invalid data format for selected reasons.")
        
        cleaned_reasons = []
        for item in data:
            if not isinstance(item, dict) or 'id' not in item or 'remarks' not in item:
                raise forms.ValidationError("Each selected reason must have 'id' and 'remarks'.")
            try:
                reason_id = int(item['id'])
            except ValueError:
                raise forms.ValidationError(f"Invalid reason ID: {item['id']}.")
            remarks = str(item['remarks'])
            cleaned_reasons.append({'id': reason_id, 'remarks': remarks})
        
        if not cleaned_reasons:
            raise forms.ValidationError("At least one ECN reason must be selected.")
            
        return cleaned_reasons

```

#### 4.3 Views (`ecn_management/views.py`)

We'll use a `TemplateView` for the main page to display the ECN reasons and handle the submission, and a `ListView` for the HTMX-driven table partial. This keeps the view logic concise and focused on orchestrating interactions.

```python
from django.views.generic import TemplateView, ListView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import redirect
import json # For handling JSON data from HTMX

from .models import ECNReason, ECNMaster
from .forms import ECNReasonProcessForm

class ECNReasonSelectionView(TemplateView):
    template_name = 'ecn_management/ecn_reason_selection.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass query string parameters to the template for use in hidden fields
        # This mirrors ASP.NET's Request.QueryString access
        context['wo_no'] = self.request.GET.get('WONo')
        context['item_id'] = self.request.GET.get('ItemId')
        context['child_id'] = self.request.GET.get('CId')
        context['quantity'] = self.request.GET.get('Qty')
        context['revision'] = self.request.GET.get('Revision')
        context['parent_id'] = self.request.GET.get('ParentId')
        context['ass_id'] = self.request.GET.get('Id') # This is 'AssId' in C#
        
        # Ensure all required query parameters are present before proceeding.
        # In a production app, you'd add more robust error handling or default values.
        if not all([context['wo_no'], context['item_id'], context['child_id'],
                     context['quantity'], context['revision'], context['parent_id'],
                     context['ass_id']]):
            messages.error(self.request, "Missing required URL parameters. Cannot process ECN.")
            # Optionally redirect to an error page or a default list view
            return redirect('some_error_or_list_page') # Placeholder for proper redirect

        return context

    def post(self, request, *args, **kwargs):
        # DataTables form submission will send data in a standard POST
        # We need to parse selected reasons and remarks
        
        # Query string parameters are available from the URL or hidden fields
        form_data = {
            'won_o': request.POST.get('wo_no_hidden'),
            'item_id': request.POST.get('item_id_hidden'),
            'child_id': request.POST.get('child_id_hidden'),
            'quantity': request.POST.get('quantity_hidden'),
            'revision': request.POST.get('revision_hidden'),
            'parent_id': request.POST.get('parent_id_hidden'),
            'ass_id': request.POST.get('ass_id_hidden'),
            'selected_reasons': json.dumps([
                {'id': r_id, 'remarks': request.POST.get(f'remarks_{r_id}', '')}
                for r_id in request.POST.getlist('selected_reason_ids')
            ])
        }

        form = ECNReasonProcessForm(form_data)
        
        if form.is_valid():
            try:
                # Session variables from ASP.NET context
                # Replace with actual Django session data from request.session
                current_session = {
                    'compid': request.session.get('compid'), 
                    'finyear': request.session.get('finyear'), 
                    'username': request.session.get('username')
                }

                ECNMaster.process_ecn_request(
                    current_session=current_session,
                    item_id=form.cleaned_data['item_id'],
                    wo_no=form.cleaned_data['won_o'],
                    child_id=form.cleaned_data['child_id'],
                    quantity=form.cleaned_data['quantity'],
                    revision=form.cleaned_data['revision'],
                    parent_id=form.cleaned_data['parent_id'],
                    ass_id=form.cleaned_data['ass_id'],
                    selected_reasons_data=form.cleaned_data['selected_reasons']
                )
                messages.success(request, 'ECN processed successfully.')
                
                # Mimic ASP.NET redirect after success
                # This matches Response.Redirect("BOM_Design_WO_TreeView_Edit.aspx?WONo=" + WONo + "&ModId=3&SubModId=26");
                # You'll need to define this URL in your Django project's urls.py
                redirect_url = reverse_lazy('bom_design_wo_tree_view_edit') + f"?WONo={form.cleaned_data['won_o']}&ModId=3&SubModId=26"
                
                # If this is an HTMX request, we can trigger a client-side redirect
                if request.headers.get('HX-Request'):
                    response = HttpResponse(status=204) # No content to swap
                    response['HX-Redirect'] = redirect_url
                    return response
                else:
                    return redirect(redirect_url)

            except Exception as e:
                # Log the exception for debugging
                import logging
                logger = logging.getLogger(__name__)
                logger.exception("Error processing ECN request")
                messages.error(request, f'Failed to process ECN: {str(e)}')
        else:
            # If form is not valid, re-render the table with errors (if applicable)
            # This is tricky for a multi-row form. Simplest might be to show a general error
            # or dynamically add error messages to specific rows if HTMX allows.
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f"Error with {field}: {error}")
            
        # If form is not valid, or an exception occurred, return to the current page
        # or re-render the table if HTMX
        if request.headers.get('HX-Request'):
            # Re-fetch the table content to show state, potentially with errors if passed
            # A more advanced solution would return a fragment with errors highlighted.
            return ECNReasonTablePartialView.as_view()(request) # Re-render the partial table
        
        # For non-HTMX requests or if partial re-render is not desired on error
        return redirect(request.path_info + f"?WONo={request.GET.get('WONo')}&ItemId={request.GET.get('ItemId')}&CId={request.GET.get('CId')}&Qty={request.GET.get('Qty')}&Revision={request.GET.get('Revision')}&ParentId={request.GET.get('ParentId')}&Id={request.GET.get('Id')}")


class ECNReasonTablePartialView(ListView):
    model = ECNReason
    template_name = 'ecn_management/_ecn_reason_table.html'
    context_object_name = 'ecn_reasons' # Renamed from 'ecn_reason_plural_lower'

    def get_queryset(self):
        # Filter based on CompId from session, mimicking loaddata()
        comp_id = self.request.session.get('compid')
        if comp_id:
            return ECNReason.objects.filter(comp_id=comp_id)
        return ECNReason.objects.all() # Fallback or handle missing comp_id

```

#### 4.4 Templates (`ecn_management/templates/ecn_management/`)

These templates demonstrate the integration of DataTables for list views, HTMX for dynamic interactions, and Alpine.js for UI state management, all leveraging Tailwind CSS for a modern look.

**`ecn_reason_selection.html`** (Main page extending `core/base.html`)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">ECN Master Edit: Select Reasons</h2>
        <!-- No direct add/edit for ECNReason on this page, as it's a selection list -->
    </div>
    
    {% if messages %}
    <div class="mb-4">
        {% for message in messages %}
        <div class="p-3 rounded-md {% if message.tags == 'error' %}bg-red-100 text-red-700{% elif message.tags == 'success' %}bg-green-100 text-green-700{% else %}bg-blue-100 text-blue-700{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Pass query string parameters as hidden inputs for POST request -->
    <input type="hidden" name="wo_no_hidden" value="{{ wo_no }}">
    <input type="hidden" name="item_id_hidden" value="{{ item_id }}">
    <input type="hidden" name="child_id_hidden" value="{{ child_id }}">
    <input type="hidden" name="quantity_hidden" value="{{ quantity }}">
    <input type="hidden" name="revision_hidden" value="{{ revision }}">
    <input type="hidden" name="parent_id_hidden" value="{{ parent_id }}">
    <input type="hidden" name="ass_id_hidden" value="{{ ass_id }}">

    <!-- HTMX container for the DataTables partial -->
    <form id="ecnProcessForm" hx-post="{% url 'ecn_management:ecn_reason_select' %}?WONo={{ wo_no }}&ItemId={{ item_id }}&CId={{ child_id }}&Qty={{ quantity }}&Revision={{ revision }}&ParentId={{ parent_id }}&Id={{ ass_id }}"
          hx-trigger="submit from #ecnProcessForm" hx-swap="none"> {# swap none for HTMX-Redirect #}
        {% csrf_token %}
        <input type="hidden" name="wo_no_hidden" value="{{ wo_no }}">
        <input type="hidden" name="item_id_hidden" value="{{ item_id }}">
        <input type="hidden" name="child_id_hidden" value="{{ child_id }}">
        <input type="hidden" name="quantity_hidden" value="{{ quantity }}">
        <input type="hidden" name="revision_hidden" value="{{ revision }}">
        <input type="hidden" name="parent_id_hidden" value="{{ parent_id }}">
        <input type="hidden" name="ass_id_hidden" value="{{ ass_id }}">

        <div id="ecnReasonTable-container"
             hx-trigger="load"
             hx-get="{% url 'ecn_management:ecn_reason_table_partial' %}?WONo={{ wo_no }}&ItemId={{ item_id }}&CId={{ child_id }}&Qty={{ quantity }}&Revision={{ revision }}&ParentId={{ parent_id }}&Id={{ ass_id }}"
             hx-swap="innerHTML">
            <!-- Loading indicator for HTMX -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                <p class="mt-4 text-lg text-gray-600">Loading ECN Reasons...</p>
            </div>
        </div>

        <div class="mt-6 flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded transition duration-150 ease-in-out"
                hx-get="{% url 'bom_design_item_edit' %}?ItemId={{ item_id }}&WONo={{ wo_no }}&PId={{ parent_id }}&CId={{ child_id }}&Id={{ ass_id }}&PgUrl=BOM_Design_WO_TreeView_Edit.aspx&ModId=3&SubModId=26"
                hx-swap="outerHTML" hx-target="body">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                Process ECN
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('htmx:afterSwap', function (evt) {
        if (evt.detail.target.id === 'ecnReasonTable-container') {
            // Re-initialize DataTables after HTMX swaps in new content
            $('#ecnReasonTable').DataTable({
                "pageLength": 15, // Match ASP.NET GridView PageSize
                "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]]
            });
        }
    });

    // Alpine.js for handling the collection of selected reasons and remarks
    document.addEventListener('alpine:init', () => {
        Alpine.data('ecnHandler', () => ({
            selectedReasons: {}, // {reason_id: {checked: bool, remarks: string}}

            init() {
                // Initialize selectedReasons from existing HTML if any, or prepare structure
                document.querySelectorAll('#ecnReasonTable tbody tr').forEach(row => {
                    const reasonId = row.dataset.reasonId;
                    const checkbox = row.querySelector(`input[type="checkbox"][name="checkbox_${reasonId}"]`);
                    const remarksInput = row.querySelector(`input[type="text"][name="remarks_${reasonId}"]`);
                    
                    if (reasonId) {
                        this.selectedReasons[reasonId] = {
                            checked: checkbox ? checkbox.checked : false,
                            remarks: remarksInput ? remarksInput.value : ''
                        };
                    }
                });
            },

            updateSelection(event, reasonId) {
                if (!this.selectedReasons[reasonId]) {
                    this.selectedReasons[reasonId] = {checked: false, remarks: ''};
                }
                this.selectedReasons[reasonId].checked = event.target.checked;
            },

            updateRemarks(event, reasonId) {
                if (!this.selectedReasons[reasonId]) {
                    this.selectedReasons[reasonId] = {checked: false, remarks: ''};
                }
                this.selectedReasons[reasonId].remarks = event.target.value;
            },

            // This function will be called on form submission to populate the hidden input
            prepareSubmit(event) {
                const form = document.getElementById('ecnProcessForm');
                const selectedReasonsInput = document.createElement('input');
                selectedReasonsInput.type = 'hidden';
                selectedReasonsInput.name = 'selected_reason_ids'; // A list of IDs
                
                const selectedReasonsData = [];
                for (const id in this.selectedReasons) {
                    if (this.selectedReasons[id].checked) {
                        selectedReasonsData.push({
                            id: id,
                            remarks: this.selectedReasons[id].remarks
                        });
                        // Also add individual hidden inputs for HTMX non-JS fallback
                        const idInput = document.createElement('input');
                        idInput.type = 'hidden';
                        idInput.name = 'selected_reason_ids';
                        idInput.value = id;
                        form.appendChild(idInput);

                        const remarksInput = document.createElement('input');
                        remarksInput.type = 'hidden';
                        remarksInput.name = `remarks_${id}`;
                        remarksInput.value = this.selectedReasons[id].remarks;
                        form.appendChild(remarksInput);
                    }
                }
                // For Django JSONField processing, we could also send a single JSON string
                const jsonInput = document.createElement('input');
                jsonInput.type = 'hidden';
                jsonInput.name = 'selected_reasons'; // Name matches the JSONField in ECNReasonProcessForm
                jsonInput.value = JSON.stringify(selectedReasonsData);
                form.appendChild(jsonInput);
                
                // Allow the form to submit
            }
        }));
    });
</script>
{% endblock %}
```

**`_ecn_reason_table.html`** (Partial template for DataTables, loaded via HTMX)

```html
<div x-data="ecnHandler" x-init="init">
    <table id="ecnReasonTable" class="min-w-full bg-white border-collapse shadow-sm">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
            </tr>
        </thead>
        <tbody>
            {% for reason in ecn_reasons %}
            <tr data-reason-id="{{ reason.id }}" class="hover:bg-gray-50">
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <input type="checkbox" 
                           name="checkbox_{{ reason.id }}" 
                           value="{{ reason.id }}" 
                           x-model="selectedReasons['{{ reason.id }}'].checked"
                           @change="updateSelection($event, '{{ reason.id }}')"
                           class="form-checkbox h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ reason.types }}</td>
                <td class="py-2 px-4 border-b border-gray-200">
                    <input type="text" 
                           name="remarks_{{ reason.id }}" 
                           value="" 
                           x-model="selectedReasons['{{ reason.id }}'].remarks"
                           @input="updateRemarks($event, '{{ reason.id }}')"
                           class="block w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="4" class="py-4 text-center text-red-500 font-bold">No data found to display</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- DataTables initialization script -->
<!-- It is crucial to re-initialize DataTables after content is loaded by HTMX -->
<!-- This script block will run each time the partial is swapped in -->
<script>
    // This script should only run once DataTables libraries are loaded.
    // The main page's htmx:afterSwap listener handles the initialization.
    // This script block ensures the DataTables instance is created for the swapped content.
    // If the DataTables library itself is not loaded, it might throw an error.
    // Ensure jQuery and DataTables CDN links are in base.html.
    // console.log("Initializing DataTables for _ecn_reason_table.html");
    // The actual initialization is handled in the htmx:afterSwap listener in the parent template.
    // This script is effectively a placeholder here to remind of the DataTables setup.
</script>
```

#### 4.5 URLs (`ecn_management/urls.py`)

This file defines the URL patterns for accessing the ECN management views.

```python
from django.urls import path
from .views import ECNReasonSelectionView, ECNReasonTablePartialView

app_name = 'ecn_management' # Define app_name for namespacing URLs

urlpatterns = [
    # Main view for ECN reason selection and processing
    path('ecn-reasons/select/', ECNReasonSelectionView.as_view(), name='ecn_reason_select'),
    
    # HTMX endpoint for rendering the DataTables partial
    path('ecn-reasons/table-partial/', ECNReasonTablePartialView.as_view(), name='ecn_reason_table_partial'),
    
    # Placeholder for the redirect URL from ASP.NET
    # You would need to define this view in your actual project's BOM app
    # For demonstration, we'll assume a dummy view or include it for context
    path('bom-design/wo-tree-view-edit/', TemplateView.as_view(template_name='dummy_redirect_page.html'), name='bom_design_wo_tree_view_edit'),
    path('bom-design/item-edit/', TemplateView.as_view(template_name='dummy_redirect_page.html'), name='bom_design_item_edit'),
]

```
**Note:** You would need to include these URLs in your project's main `urls.py` (e.g., `path('ecn/', include('ecn_management.urls')),`).

#### 4.6 Tests (`ecn_management/tests.py`)

Comprehensive tests are crucial to ensure the correctness of the migrated functionality. We will include unit tests for the models (especially the complex `process_ecn_request` method) and integration tests for the views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch
from .models import ECNReason, BOMMaster, BOMAmendment, ECNMaster, ECNDetail

class ECNReasonModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dummy data for tblDG_ECN_Reason
        ECNReason.objects.create(id=1, types='Design Change', comp_id=101)
        ECNReason.objects.create(id=2, types='Material Substitution', comp_id=101)
        ECNReason.objects.create(id=3, types='Process Improvement', comp_id=102)

    def test_ecn_reason_creation(self):
        reason = ECNReason.objects.get(id=1)
        self.assertEqual(reason.types, 'Design Change')
        self.assertEqual(reason.comp_id, 101)

    def test_ecn_reason_str_representation(self):
        reason = ECNReason.objects.get(id=1)
        self.assertEqual(str(reason), 'Design Change')

class ECNMasterModelBusinessLogicTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Setup session data
        cls.session_data = {
            'compid': 101,
            'finyear': 2023,
            'username': 'testuser'
        }

        # Setup related BOM data
        BOMMaster.objects.create(
            id=1001, comp_id=101, fin_year_id=2023, item_id=1, wo_no='WO001', c_id=10,
            amd_no='0', qty='100', revision='A', ecn_flag=False,
            sys_date='2023-01-01', sys_time='10:00:00', session_id='olduser', p_id=1
        )
        ECNReason.objects.create(id=1, types='Reason One', comp_id=101)
        ECNReason.objects.create(id=2, types='Reason Two', comp_id=101)

    @patch('django.utils.timezone.now')
    def test_process_ecn_request_success(self, mock_now):
        from datetime import datetime
        mock_now.return_value = datetime(2024, 7, 15, 14, 30, 0)

        selected_reasons = [
            {'id': 1, 'remarks': 'Initial remarks for reason 1'},
            {'id': 2, 'remarks': 'Further remarks for reason 2'}
        ]

        # Call the fat model method
        ecn_master_instance = ECNMaster.process_ecn_request(
            current_session=self.session_data,
            item_id=1,
            wo_no='WO001',
            child_id=10,
            quantity='150',
            revision='B',
            parent_id=1,
            ass_id=1001, # Matches BOMMaster.id
            selected_reasons_data=selected_reasons
        )

        self.assertIsNotNone(ecn_master_instance)
        self.assertEqual(ecn_master_instance.comp_id, 101)
        self.assertEqual(ecn_master_instance.wo_no, 'WO001')
        self.assertEqual(ecn_master_instance.sys_date, '2024-07-15')
        self.assertEqual(ecn_master_instance.sys_time, '14:30:00')

        # Verify BOMMaster was updated
        updated_bom = BOMMaster.objects.get(id=1001)
        self.assertEqual(updated_bom.qty, '150')
        self.assertEqual(updated_bom.revision, 'B')
        self.assertEqual(updated_bom.amd_no, '1') # Original was '0', so incremented to '1'
        self.assertTrue(updated_bom.ecn_flag)

        # Verify BOMAmendment was created
        bom_amendment = BOMAmendment.objects.get(bom_id=1001)
        self.assertEqual(bom_amendment.amd_no, '0') # Original AMD number
        self.assertEqual(bom_amendment.qty, '100') # Original Qty

        # Verify ECN details were created
        ecn_details = ECNDetail.objects.filter(m_id=ecn_master_instance)
        self.assertEqual(ecn_details.count(), 2)
        self.assertTrue(ecn_details.filter(ecn_reason__id=1, remarks='Initial remarks for reason 1').exists())
        self.assertTrue(ecn_details.filter(ecn_reason__id=2, remarks='Further remarks for reason 2').exists())
        
        # Test broad BOMMaster revision update
        BOMMaster.objects.create(
            id=1002, comp_id=101, fin_year_id=2023, item_id=1, wo_no='WO001', c_id=11,
            amd_no='0', qty='50', revision='X', ecn_flag=True, # Pre-existing ECNFlag=True
            sys_date='2023-01-01', sys_time='10:00:00', session_id='olduser', p_id=1
        )
        
        # Trigger the process again with same item_id and wo_no
        # This time, we just check the revision update on a different BOM entry
        ecn_master_instance_2 = ECNMaster.process_ecn_request(
            current_session=self.session_data,
            item_id=1,
            wo_no='WO001',
            child_id=10, # Same child_id as before, but the broad update also hits 1002
            quantity='160',
            revision='C', # New revision
            parent_id=1,
            ass_id=1001,
            selected_reasons_data=[{'id': 1, 'remarks': 'More changes'}]
        )
        
        updated_bom_2 = BOMMaster.objects.get(id=1002)
        self.assertEqual(updated_bom_2.revision, 'C') # Should be updated by the broad query

    def test_process_ecn_request_no_reasons_selected(self):
        with self.assertRaises(Exception) as cm: # Expect an exception from the form validation
            ECNMaster.process_ecn_request(
                current_session=self.session_data,
                item_id=1, wo_no='WO001', child_id=10, quantity='150',
                revision='B', parent_id=1, ass_id=1001, selected_reasons_data=[]
            )
        # Verify no ECNMaster or BOMAmendment records were created due to transaction rollback
        self.assertEqual(ECNMaster.objects.count(), 0)
        self.assertEqual(BOMAmendment.objects.count(), 0)


class ECNReasonViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Set up a dummy session
        session = self.client.session
        session['compid'] = 101
        session['finyear'] = 2023
        session['username'] = 'testuser'
        session.save()

        # Create dummy data for ECNReason
        ECNReason.objects.create(id=1, types='Reason A', comp_id=101)
        ECNReason.objects.create(id=2, types='Reason B', comp_id=101)
        ECNReason.objects.create(id=3, types='Reason C', comp_id=102) # Different comp_id

        # Create dummy BOMMaster for POST test
        BOMMaster.objects.create(
            id=1001, comp_id=101, fin_year_id=2023, item_id=1, wo_no='WO001', c_id=10,
            amd_no='0', qty='100', revision='A', ecn_flag=False,
            sys_date='2023-01-01', sys_time='10:00:00', session_id='olduser', p_id=1
        )


        # Base query parameters for the view
        self.query_params = {
            'WONo': 'WO001',
            'ItemId': 1,
            'CId': 10,
            'Qty': '150',
            'Revision': 'B',
            'ParentId': 1,
            'Id': 1001 # This is 'AssId'
        }
        self.url = reverse('ecn_management:ecn_reason_select') + '?' + '&'.join(f'{k}={v}' for k,v in self.query_params.items())
        self.table_partial_url = reverse('ecn_management:ecn_reason_table_partial') + '?' + '&'.join(f'{k}={v}' for k,v in self.query_params.items())


    def test_ecn_reason_selection_view_get(self):
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'ecn_management/ecn_reason_selection.html')
        self.assertContains(response, 'ECN Master Edit: Select Reasons')
        self.assertContains(response, 'id="ecnReasonTable-container"') # Check HTMX container

    def test_ecn_reason_table_partial_view_get(self):
        response = self.client.get(self.table_partial_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'ecn_management/_ecn_reason_table.html')
        
        # Only reasons for comp_id=101 should be visible
        self.assertContains(response, 'Reason A')
        self.assertContains(response, 'Reason B')
        self.assertNotContains(response, 'Reason C') # Excluded by comp_id filter

    @patch('django.utils.timezone.now')
    def test_ecn_reason_selection_view_post_success(self, mock_now):
        from datetime import datetime
        mock_now.return_value = datetime(2024, 7, 15, 14, 30, 0)
        
        post_data = {
            'selected_reason_ids': ['1', '2'], # Simulate checkbox names
            'remarks_1': 'Remark for reason A',
            'remarks_2': 'Remark for reason B',
            'selected_reasons': json.dumps([
                {'id': 1, 'remarks': 'Remark for reason A'},
                {'id': 2, 'remarks': 'Remark for reason B'}
            ]),
            # Hidden fields mimicking query parameters
            'wo_no_hidden': 'WO001',
            'item_id_hidden': '1',
            'child_id_hidden': '10',
            'quantity_hidden': '150',
            'revision_hidden': 'B',
            'parent_id_hidden': '1',
            'ass_id_hidden': '1001',
        }
        
        # Simulate HTMX request
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(self.url, data=post_data, **headers)
        
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertIn('HX-Redirect', response)
        self.assertTrue('bom-design/wo-tree-view-edit/' in response['HX-Redirect'])
        
        # Verify ECNMaster and ECNDetail creation
        self.assertEqual(ECNMaster.objects.count(), 1)
        self.assertEqual(ECNDetail.objects.count(), 2)
        self.assertTrue(ECNDetail.objects.filter(ecn_reason__id=1, remarks='Remark for reason A').exists())
        self.assertTrue(ECNDetail.objects.filter(ecn_reason__id=2, remarks='Remark for reason B').exists())

        # Verify BOMMaster and BOMAmendment updates
        updated_bom = BOMMaster.objects.get(id=1001)
        self.assertEqual(updated_bom.qty, '150')
        self.assertEqual(updated_bom.revision, 'B')
        self.assertTrue(updated_bom.ecn_flag)
        self.assertEqual(BOMAmendment.objects.count(), 1)


    def test_ecn_reason_selection_view_post_no_reasons_selected(self):
        post_data = {
            'selected_reason_ids': [], # No reasons selected
            'selected_reasons': json.dumps([]),
            # Hidden fields mimicking query parameters
            'wo_no_hidden': 'WO001',
            'item_id_hidden': '1',
            'child_id_hidden': '10',
            'quantity_hidden': '150',
            'revision_hidden': 'B',
            'parent_id_hidden': '1',
            'ass_id_hidden': '1001',
        }
        
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(self.url, data=post_data, **headers)
        
        # For HTMX, it might re-render the table with errors or return a different status
        # If the view re-renders the partial on error, status will be 200.
        # If validation fails, it might return a message via hx-trigger.
        self.assertEqual(response.status_code, 200) 
        self.assertTemplateUsed(response, 'ecn_management/_ecn_reason_table.html') # View re-renders partial
        self.assertContains(response, 'No data found to display') # Assuming empty list leads to this, or error message

        # Verify no ECNMaster or ECNDetail records were created
        self.assertEqual(ECNMaster.objects.count(), 0)
        self.assertEqual(ECNDetail.objects.count(), 0)
        self.assertEqual(BOMAmendment.objects.count(), 0) # No BOM amendment either
```

---

### Step 5: HTMX and Alpine.js Integration

The templates provided above (`ecn_reason_selection.html` and `_ecn_reason_table.html`) fully integrate HTMX and Alpine.js.

*   **HTMX:**
    *   The main page (`ecn_reason_selection.html`) loads the DataTables content dynamically using `hx-get` on `ecnReasonTable-container`.
    *   The form submission for processing ECN reasons uses `hx-post` and `hx-swap="none"`. Upon successful processing, the view returns an `HX-Redirect` header to navigate to the next page, mimicking the ASP.NET `Response.Redirect`.
    *   The "Cancel" button also uses `hx-get` and `hx-swap` to navigate seamlessly without full page reloads.
*   **Alpine.js:**
    *   The `_ecn_reason_table.html` uses an `x-data="ecnHandler"` component to manage the state of checkboxes and remarks.
    *   It binds `x-model` to track whether each reason is `checked` and its corresponding `remarks` text.
    *   The `prepareSubmit` function in the Alpine.js component ensures that all selected reasons and their remarks are correctly formatted and sent as hidden inputs within the form when the "Process ECN" button is clicked. This allows the Django form to validate and process the multi-row input.
*   **DataTables:**
    *   The `_ecn_reason_table.html` defines the `<table>` with a specific ID (`ecnReasonTable`).
    *   The JavaScript within `ecn_reason_selection.html` (in `extra_js` block) listens for the `htmx:afterSwap` event on the `ecnReasonTable-container`. Once the new table HTML is loaded, it re-initializes DataTables for the new content, ensuring client-side sorting, searching, and pagination work seamlessly.

This comprehensive approach leverages modern frontend technologies to deliver a highly interactive and efficient user experience, replacing the postback-heavy nature of the original ASP.NET application with a nimble, responsive design.