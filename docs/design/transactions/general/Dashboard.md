## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

Based on the ASP.NET code-behind, the core tables involved in the "Convert TPL Items into BOM" functionality are:

*   **`tblDG_TPL_Master`**: This is the primary table for the dashboard data.
    *   Columns: `WONo`, `CompId`, `FinYearId`, `ConvertToBOM`, `PId`, `CId`, `ItemId`, `Qty`, `Weldments`, `LH`, `RH`. (Assumed `Id` as primary key).
*   **`tblDG_Item_Master`**: Used for item details like code, description, and UOM.
    *   Columns: `Id`, `CompId`, `FinYearId`, `ItemCode`, `ManfDesc`, `UOMBasic`.
*   **`Unit_Master`**: Used for Unit of Measure symbols.
    *   Columns: `Id`, `Symbol`.
*   **`tblDG_BOM_Master`**: Target table for inserting BOM records.
    *   Columns: `SysDate`, `SysTime`, `CompId`, `FinYearId`, `SessionId`, `WONo`, `PId`, `CId`, `ItemId`, `Qty`. (Assumed `Id` as primary key).
*   **`SD_Cust_WorkOrder_Master`**: Updated after converting to BOM.
    *   Columns: `WONo`, `CompId`, `UpdateWO`. (Assumed `Id` as primary key).

### Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

*   **Read (Dashboard Display):** The `makeGrid()` method retrieves data from `tblDG_TPL_Master`, `tblDG_Item_Master`, and `Unit_Master`. It performs complex joins and calculations (like `Assly No` and `TPL Qty` via `fun.RecurQty`) to prepare data for `GridView2`. This will be handled by custom manager methods or static methods within Django models.
*   **Update/Create (Add To BOM):** The `btncnv_Click()` method handles the "Add To BOM" action. For selected items, it:
    *   Inserts new records into `tblDG_BOM_Master`.
    *   Updates the `ConvertToBOM` flag in `tblDG_TPL_Master` to `1` (true) for the processed items.
    *   Updates the `UpdateWO` flag in `SD_Cust_WorkOrder_Master` to `1` (true) for the corresponding Work Order.
    *   The conditional logic based on `PId` and existing `ConvertToBOM` status indicates a business rule for BOM conversion. This will be encapsulated in a model method.
*   **No explicit Delete functionality** identified from the provided code.

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

*   **`GridView2` (ASP.NET GridView):** Displays a list of TPL items with columns like `WO No`, `Assly No`, `ItemCode`, `Description`, `UOM`, `Unit Qty`, `TPL Qty`, `Weld`, `LH`, `RH`. It supports pagination and row selection via checkboxes (including a "Select All" checkbox).
    *   **Django Equivalent:** A standard HTML table within a template (`design/tplitem/list.html`) rendered with data from `TplMaster` model. DataTables.js will provide client-side pagination, sorting, and searching. Row selection will use standard HTML checkboxes.
*   **`btncnv` (ASP.NET Button):** "Add To BOM" button to initiate the conversion process for selected items.
    *   **Django Equivalent:** An HTML button with `hx-post` attributes to trigger an HTMX request to a dedicated view for batch processing. Alpine.js can manage the "Select All" functionality and client-side confirmation.
*   **`lblmsg` (ASP.NET Label):** Used for displaying messages.
    *   **Django Equivalent:** Django's built-in `messages` framework for success/error notifications.

---

### Step 4: Generate Django Code

We will create a new Django app named `design`.

#### 4.1 Models

Task: Create Django models based on the identified database schema.
The most complex logic (data retrieval for dashboard, recursive quantity calculation, and batch BOM conversion) will be handled within the `TplMaster` model and its manager.

**File:** `design/models.py`

```python
from django.db import models, transaction
from django.utils import timezone
from datetime import date, time

# Assuming CompId, FinYearId, SessionId are global or context-based.
# For demonstration, hardcoding or using placeholders.
DEFAULT_COMP_ID = 1
DEFAULT_FIN_YEAR_ID = 2023
DEFAULT_SESSION_ID = 'system_user' # Or current request.user.username

class UnitMaster(models.Model):
    """
    Maps to Unit_Master table for Unit of Measure details.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit Master'
        verbose_name_plural = 'Unit Masters'

    def __str__(self):
        return self.symbol

class ItemMaster(models.Model):
    """
    Maps to tblDG_Item_Master table for Item details.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    compid = models.IntegerField(db_column='CompId')
    finyearid = models.IntegerField(db_column='FinYearId')
    item_code = models.CharField(db_column='ItemCode', max_length=100)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255)
    uom_basic = models.ForeignKey(UnitMaster, on_delete=models.DO_NOTHING, db_column='UOMBasic')

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.item_code

class BomMaster(models.Model):
    """
    Maps to tblDG_BOM_Master table for Bill of Materials records.
    """
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming auto-increment
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    compid = models.IntegerField(db_column='CompId')
    finyearid = models.IntegerField(db_column='FinYearId')
    sessionid = models.CharField(db_column='SessionId', max_length=100)
    wono = models.CharField(db_column='WONo', max_length=100)
    pid = models.IntegerField(db_column='PId')
    cid = models.IntegerField(db_column='CId')
    item = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId')
    qty = models.FloatField(db_column='Qty')

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'BOM Master'
        verbose_name_plural = 'BOM Masters'

    def __str__(self):
        return f"BOM for WO: {self.wono}, Item: {self.item.item_code}"

class WorkOrderMaster(models.Model):
    """
    Maps to SD_Cust_WorkOrder_Master table for Work Order updates.
    """
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming auto-increment
    wono = models.CharField(db_column='WONo', max_length=100)
    compid = models.IntegerField(db_column='CompId')
    update_wo = models.BooleanField(db_column='UpdateWO', default=False) # Original used '1', converting to bool

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order Master'
        verbose_name_plural = 'Work Order Masters'

    def __str__(self):
        return self.wono

class TplMasterQuerySet(models.QuerySet):
    def with_dashboard_data(self, comp_id=DEFAULT_COMP_ID, fin_year_id=DEFAULT_FIN_YEAR_ID):
        """
        Annotates the queryset with derived fields needed for the dashboard display,
        similar to the makeGrid method in ASP.NET.
        This includes 'Assly No', 'ItemCode', 'Description', 'UOM', 'TPL Qty'.
        """
        # Optimized query to fetch all related data efficiently.
        # This is a simplified interpretation of the complex 'makeGrid' logic.
        # The actual 'fun.RecurQty' would require a recursive CTE or specific algorithm.
        # For demonstration, 'Assly No' and 'TPL Qty' calculation is simplified.
        return self.filter(
            compid=comp_id,
            finyearid__lte=fin_year_id, # Original code used <=
            convert_to_bom=False
        ).select_related(
            'itemid', # Related ItemMaster for current TPL item
            'itemid__uom_basic' # Related UnitMaster for current TPL item's UOM
        ).annotate(
            # item_code: from the current TPL's ItemMaster
            dashboard_item_code=models.F('itemid__item_code'),
            # description: from the current TPL's ItemMaster's ManfDesc
            dashboard_description=models.F('itemid__manf_desc'),
            # uom_symbol: from the current TPL's ItemMaster's UOMBasic's Symbol
            dashboard_uom=models.F('itemid__uom_basic__symbol'),
            # Assly_No: This is complex. If PId refers to another TPLMaster entry's ItemId.
            # Assuming it's the ItemCode of the parent assembly.
            # This would need a custom JOIN or a subquery if it's not a direct FK.
            # Here, a simplified approach by joining back to ItemMaster for PId's item code.
            dashboard_assly_no=models.Subquery(
                TplMaster.objects.filter(
                    compid=models.OuterRef('compid'),
                    finyearid__lte=models.OuterRef('finyearid'),
                    cid=models.OuterRef('pid') # Assuming PId in current TPL is CId in parent TPL
                ).select_related('itemid').values('itemid__item_code')[:1],
                output_field=models.CharField()
            ),
            # TPL_Qty: This is the 'fun.RecurQty' calculation.
            # This cannot be easily done with ORM annotations directly for a complex recursive function.
            # It's better as a property method on the TplMaster instance,
            # or pre-calculated and cached if performance is critical for large datasets.
            # For demonstration, we'll expose the raw quantity and calculate the TPL Qty
            # using a property method on the model instance itself (Fat Model).
        )

    def convert_selected_to_bom(self, selected_ids, session_id, comp_id, fin_year_id):
        """
        Processes selected TPL items to convert them into BOM records.
        Encapsulates the btncnv_Click logic.
        """
        converted_count = 0
        with transaction.atomic():
            for tpl_item_id in selected_ids:
                try:
                    tpl_item = self.select_for_update().get(pk=tpl_item_id, compid=comp_id, finyearid__lte=fin_year_id)

                    # Check business rule: if PId > 0, check if parent is already converted.
                    # This implies parent TPL item's ConvertToBOM status should be 1.
                    # Original logic was: if (Convert.ToInt32(dsCheck.Tables[0].Rows[0]["ConvertToBOM"]) == 1)
                    # dsCheck was based on PId matching CId in TPL_Master.
                    is_parent_converted = True
                    if tpl_item.pid != 0:
                        parent_tpl_qs = TplMaster.objects.filter(
                            wono=tpl_item.wono,
                            compid=tpl_item.compid,
                            finyearid__lte=tpl_item.finyearid,
                            cid=tpl_item.pid # Assuming PId of child is CId of parent
                        )
                        if not parent_tpl_qs.exists() or not parent_tpl_qs.first().convert_to_bom:
                             is_parent_converted = False

                    if tpl_item.pid == 0 or is_parent_converted:
                        # 1. Insert into tblDG_BOM_Master
                        BomMaster.objects.create(
                            sys_date=timezone.localdate(),
                            sys_time=timezone.localtime().time(),
                            compid=comp_id,
                            finyearid=fin_year_id,
                            sessionid=session_id,
                            wono=tpl_item.wono,
                            pid=tpl_item.pid,
                            cid=tpl_item.cid,
                            item=tpl_item.itemid, # This is a ForeignKey
                            qty=tpl_item.qty
                        )

                        # 2. Update SD_Cust_WorkOrder_Master
                        # Note: This might update multiple times for the same WO if multiple items from one WO are selected.
                        # Original code only uses WONo and CompId, so it's a batch update for the WO.
                        WorkOrderMaster.objects.filter(
                            wono=tpl_item.wono,
                            compid=comp_id
                        ).update(update_wo=True)

                        # 3. Update tblDG_TPL_Master
                        tpl_item.convert_to_bom = True
                        tpl_item.save(update_fields=['convert_to_bom'])
                        converted_count += 1
                except TplMaster.DoesNotExist:
                    # Item not found or already processed (e.g., race condition)
                    continue
                except Exception as e:
                    # Log error, potentially re-raise or handle gracefully
                    print(f"Error converting TPL item {tpl_item_id}: {e}")
                    raise # Re-raise to trigger rollback if necessary

        return converted_count

class TplMaster(models.Model):
    """
    Maps to tblDG_TPL_Master table for TPL (Temporary Parts List) items.
    """
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming 'Id' is the PK
    wono = models.CharField(db_column='WONo', max_length=100)
    compid = models.IntegerField(db_column='CompId')
    finyearid = models.IntegerField(db_column='FinYearId')
    convert_to_bom = models.BooleanField(db_column='ConvertToBOM', default=False) # Assuming 0/1 for bool
    pid = models.IntegerField(db_column='PId')
    cid = models.IntegerField(db_column='CId')
    itemid = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId') # Linking to ItemMaster
    qty = models.FloatField(db_column='Qty')
    weldments = models.IntegerField(db_column='Weldments')
    lh = models.IntegerField(db_column='LH')
    rh = models.IntegerField(db_column='RH')

    objects = TplMasterQuerySet.as_manager()

    class Meta:
        managed = False
        db_table = 'tblDG_TPL_Master'
        verbose_name = 'TPL Item'
        verbose_name_plural = 'TPL Items'
        # Consider adding unique_together if WONo, PId, CId, ItemId forms a unique constraint
        # unique_together = (('wono', 'pid', 'cid', 'itemid'),)

    def __str__(self):
        return f"TPL: {self.wono} - {self.itemid.item_code}"

    # --- Business Logic Methods (Fat Model) ---

    @property
    def dashboard_assly_no(self):
        """
        Calculates 'Assly No' based on the complex logic from ASP.NET 'makeGrid'.
        If PId is 0, Assly No is ItemCode of CId. Otherwise, it's ItemCode of PId's ItemId.
        This assumes recursive relation or direct lookup as per ASP.NET logic.
        """
        # Simplified interpretation of original logic
        # Original: string StrWO = fun.select("ItemId", "tblDG_TPL_Master", "WONo='" + ds.Tables[0].Rows[k]["WONo"].ToString() + "' AND CompId='" + CompId + "'AND FinYearId<='" + FinYear + "' AND CId='" + Convert.ToInt32(ds.Tables[0].Rows[k]["PId"]) + "'");
        # This fetches an ItemId from TPL_Master where CId matches current TPL's PId.
        # Then, it gets ItemCode from tblDG_Item_Master using that ItemId.
        
        if self.pid == 0:
            # If PId is 0, original code sets Assly No to ItemCode of current item and ItemCode to empty.
            # This seems contradictory. Reinterpreting: If PId is 0, it means the item itself is an assembly.
            # Based on original, it implies `dr[1] = ds2.Tables[0].Rows[0]["ItemCode"].ToString();`
            # and `dr[2] = "";` when `ds4.Tables[0].Rows.Count == 0`.
            # This is complex, and the original code has `dr[1] = ds5.Tables[0].Rows[0]["AsslyNo"].ToString();`
            # and `dr[2] = ds2.Tables[0].Rows[0]["ItemCode"].ToString();` when `ds4` has rows.
            # Assuming 'Assly No' means the parent item's code.
            return self.itemid.item_code if self.itemid else "" # This is ItemCode for the current item
        
        # Look up the parent TPL item based on PId
        parent_item_tpl = TplMaster.objects.filter(
            wono=self.wono,
            compid=self.compid,
            finyearid__lte=self.finyearid,
            cid=self.pid # Find a TPL entry where its CId is our PId
        ).first()

        if parent_item_tpl and parent_item_tpl.itemid:
            # Then retrieve the ItemCode of that parent's item
            return parent_item_tpl.itemid.item_code
        return "" # Default if no assembly found

    @property
    def dashboard_tpl_qty(self):
        """
        Calculates 'TPL Qty' using the recursive logic from fun.RecurQty.
        This is a placeholder for the actual recursive BOM quantity calculation.
        """
        # The original fun.RecurQty is a recursive function.
        # This cannot be directly translated to a simple Django ORM property without
        # a recursive query (e.g., using CTEs in raw SQL) or a custom recursive Python function.
        # For a truly recursive BOM quantity, you'd need to trace up or down the BOM tree.
        
        # Placeholder: returning the original qty, as a complex recursive function needs
        # full context of the BOM structure which isn't fully implied by just PId/CId.
        # A full implementation would involve:
        # 1. Starting from the current item.
        # 2. Recursively finding all its child components and their quantities.
        # 3. Multiplying quantities at each level and summing them up.
        # This requires a proper BOM structure and potentially a stored procedure or recursive CTE.
        # For simplicity, returning `qty` directly or a static value.
        # If the `AsslyNewqty` in ASP.NET was `RecurQty(WONo, PId, CId, 1, CompId, FinYear)`,
        # it probably calculates the *total* quantity of *this* item needed for the top-level assembly.

        # Example of a simplified recursive lookup (conceptual, might need optimization):
        def _calculate_recursive_qty(item_id, current_wono, comp_id, fin_year_id, current_multiplier=1.0):
            # Base case: if it's a raw material or bottom of the tree
            # This logic needs to be precise based on how RecurQty works.
            # Assuming it computes quantity for a *specific* component in the context of a *specific* assembly.
            
            # This is a highly complex function to reverse engineer without source.
            # Let's assume for now, it's just the base quantity, or a simpler interpretation.
            # The original code passed `1` as the `CurrentQty`. This might mean `RecurQty`
            # computes how many of the *current* item are needed for *one* of its immediate parents.
            return self.qty # Placeholder: returning the base quantity for now

        return _calculate_recursive_qty(self.itemid.id, self.wono, self.compid, self.finyearid)

    @property
    def dashboard_item_code(self):
        return self.itemid.item_code if self.itemid else ""

    @property
    def dashboard_description(self):
        return self.itemid.manf_desc if self.itemid else ""

    @property
    def dashboard_uom(self):
        return self.itemid.uom_basic.symbol if self.itemid and self.itemid.uom_basic else ""

```

#### 4.2 Forms

Task: Define a Django form for user input.
In this case, the "form" is primarily for selection (checkboxes) rather than data entry for `TplMaster`. Thus, a ModelForm isn't strictly necessary for the list view itself, but for the "Add to BOM" action, we'll use a hidden form to send selected IDs. We won't have a typical CRUD form for `TplMaster` in this specific dashboard context.

For the purpose of illustrating `forms.py`, if we *were* to have a form for editing a `TplMaster` (even though not explicitly shown in the ASP.NET UI), it would look like this. Since the primary action is "Add to BOM" for *multiple* items, a form isn't directly needed for *creating/updating* a single `TplItem`. Instead, we'll handle the selection via POST data.

**File:** `design/forms.py`

```python
from django import forms
from .models import TplMaster

# While a ModelForm for TplMaster isn't directly used for the "Add to BOM" dashboard,
# if we were to have a form for individual TplMaster editing, it would look like this:
class TplMasterForm(forms.ModelForm):
    class Meta:
        model = TplMaster
        # Fields for TplMaster if it were editable via a form.
        # These are hypothetical as per the ASP.NET code, the grid was for display+selection.
        fields = ['wono', 'pid', 'cid', 'itemid', 'qty', 'weldments', 'lh', 'rh']
        widgets = {
            'wono': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'pid': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'cid': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'itemid': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'qty': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'weldments': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'lh': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'rh': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }

# Form for batch processing "Add To BOM"
# This form will not be directly rendered, but used to validate incoming POST data.
class TplItemConvertForm(forms.Form):
    selected_ids = forms.MultipleChoiceField(
        widget=forms.MultipleHiddenInput(),
        required=True,
        error_messages={'required': 'Please select at least one item to convert.'}
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate choices if necessary, though it's assumed IDs will be sent directly
        # from the frontend via HTMX on button click.
        # choices = [(item.id, item.id) for item in TplMaster.objects.all()]
        # self.fields['selected_ids'].choices = choices

```

#### 4.3 Views

Task: Implement CRUD operations using CBVs.
For this dashboard, we need a `ListView` to display the items and a custom `View` (or `FormView`) to handle the "Add To BOM" batch operation via HTMX POST. We also need a partial view for the DataTables content.

**File:** `design/views.py`

```python
from django.views.generic import ListView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.template.loader import render_to_string
from django.shortcuts import redirect
from .models import TplMaster, DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID, DEFAULT_SESSION_ID
from .forms import TplItemConvertForm # Import the form for validation

class TplMasterDashboardView(ListView):
    """
    Main dashboard view to display TPL items awaiting BOM conversion.
    This replaces the ASP.NET Dashboard.aspx page.
    """
    model = TplMaster
    template_name = 'design/tplitem/dashboard.html' # Renamed for clarity vs general 'list'
    context_object_name = 'tpl_items' # Renamed for clarity

    def get_queryset(self):
        """
        Fetches TPL items and annotates them with dashboard-specific data.
        This leverages the TplMasterQuerySet method.
        """
        # You might pass CompId/FinYearId from session/user profile in a real app
        comp_id = self.request.session.get('compid', DEFAULT_COMP_ID)
        fin_year_id = self.request.session.get('finyear', DEFAULT_FIN_YEAR_ID)
        
        return TplMaster.objects.with_dashboard_data(comp_id, fin_year_id)

    # Views should be thin, so the complex data gathering is in the model/manager.
    # No need for a separate get_context_data unless adding more context variables.

class TplMasterTablePartialView(TplMasterDashboardView):
    """
    Renders only the table portion for HTMX requests to update the list.
    """
    template_name = 'design/tplitem/_tplitem_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Ensure 'tpl_items' is always in the context
        context['tpl_items'] = self.get_queryset()
        return context

    def get(self, request, *args, **kwargs):
        # HTMX requests only swap the innerHTML, so render only the table template
        self.object_list = self.get_queryset()
        context = self.get_context_data()
        return HttpResponse(render_to_string(self.template_name, context, request))

class ConvertTplItemsToBomView(View):
    """
    Handles the batch conversion of selected TPL items to BOM.
    This replaces the btncnv_Click logic.
    """
    def post(self, request, *args, **kwargs):
        form = TplItemConvertForm(request.POST)
        if form.is_valid():
            selected_ids = [int(item_id) for item_id in form.cleaned_data['selected_ids']]
            
            # Retrieve session-specific parameters (CompId, FinYear, SessionId)
            comp_id = request.session.get('compid', DEFAULT_COMP_ID)
            fin_year_id = request.session.get('finyear', DEFAULT_FIN_YEAR_ID)
            session_id = request.session.get('username', DEFAULT_SESSION_ID) # ASP.NET used Session["username"]

            try:
                converted_count = TplMaster.objects.convert_selected_to_bom(
                    selected_ids, session_id, comp_id, fin_year_id
                )
                messages.success(request, f"{converted_count} TPL items converted to BOM successfully.")
            except Exception as e:
                messages.error(request, f"Error converting TPL items: {e}")
                # Log the full exception for debugging
                import traceback
                print(f"Error converting TPL items: {traceback.format_exc()}")
            
        else:
            # If form is not valid (e.g., no items selected)
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f"Error: {error}")
            
        # HTMX response for a non-full page reload action
        if request.headers.get('HX-Request'):
            # Trigger refresh of the table and display messages
            return HttpResponse(
                status=204, # No content, indicates success without body
                headers={
                    'HX-Trigger': 'refreshTplItemList' # Custom event to re-fetch table
                }
            )
        return redirect(reverse_lazy('tplitem_dashboard')) # Fallback for non-HTMX requests

```

#### 4.4 Templates

Task: Create templates for each view.
The `dashboard.html` will contain the main page structure and a placeholder for the `_tplitem_table.html` partial, which will be loaded via HTMX.

**File:** `design/templates/design/tplitem/dashboard.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-3xl font-extrabold text-gray-900 mb-6">Convert TPL Items into BOM</h2>

    <div class="flex items-center justify-end mb-4 space-x-2" x-data="{ selectedCount: 0 }">
        <span x-show="selectedCount > 0" class="text-sm font-medium text-gray-700">Selected: <span x-text="selectedCount"></span></span>
        <button
            type="button"
            x-ref="convertButton"
            @click="if (selectedCount > 0) {
                if (confirm('Are you sure you want to convert the selected items to BOM?')) {
                    $dispatch('convert-selected-items');
                }
            } else {
                alert('Please select at least one item to convert.');
            }"
            class="px-5 py-2 border border-transparent text-base font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
            :disabled="selectedCount === 0">
            Add To BOM
        </button>
    </div>

    <!-- HTMX target for the data table -->
    <div id="tplitemTable-container"
         hx-trigger="load, refreshTplItemList from:body"
         hx-get="{% url 'tplitem_table_partial' %}"
         hx-swap="innerHTML">
        <!-- Loading spinner while data loads -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading TPL Items...</p>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js for managing checkbox states and selected item count
    document.addEventListener('alpine:init', () => {
        Alpine.data('tplItemSelection', () => ({
            selectedItems: new Set(),

            init() {
                this.$watch('selectedItems.size', value => {
                    this.$dispatch('update-selected-count', { count: value });
                });
                // Ensure the 'Select All' checkbox state is correctly reflected on load/refresh
                this.$nextTick(() => {
                    this.updateSelectAllCheckbox();
                });
            },

            toggleSelectAll(event) {
                const isChecked = event.target.checked;
                this.selectedItems.clear();
                if (isChecked) {
                    document.querySelectorAll('input[type="checkbox"][data-item-id]').forEach(checkbox => {
                        checkbox.checked = true;
                        this.selectedItems.add(checkbox.dataset.itemId);
                    });
                } else {
                    document.querySelectorAll('input[type="checkbox"][data-item-id]').forEach(checkbox => {
                        checkbox.checked = false;
                    });
                }
            },

            toggleSelectItem(event) {
                const itemId = event.target.dataset.itemId;
                if (event.target.checked) {
                    this.selectedItems.add(itemId);
                } else {
                    this.selectedItems.delete(itemId);
                }
                this.updateSelectAllCheckbox();
            },

            updateSelectAllCheckbox() {
                const allCheckboxes = document.querySelectorAll('input[type="checkbox"][data-item-id]');
                const selectAllCheckbox = document.getElementById('chkSelectAll');
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = this.selectedItems.size > 0 && this.selectedItems.size === allCheckboxes.length;
                }
                this.$dispatch('update-selected-count', { count: this.selectedItems.size });
            },

            // Listener for the 'convert-selected-items' event
            convertItems(event) {
                if (this.selectedItems.size === 0) {
                    alert('No items selected for conversion.');
                    return;
                }

                const formData = new FormData();
                this.selectedItems.forEach(id => {
                    formData.append('selected_ids', id);
                });
                formData.append('csrfmiddlewaretoken', '{{ csrf_token }}'); // Add CSRF token

                // Use HTMX to send the form data
                htmx.trigger(this.$refs.convertButton, 'submitForm', {
                    target: htmx.find('#hiddenForm'), // Use a hidden form for submitting
                    swap: 'none',
                    method: 'POST',
                    url: '{% url "convert_tplitems_to_bom" %}',
                    headers: { 'X-CSRFToken': '{{ csrf_token }}' },
                    body: formData
                });
                
                // Clear selections after initiating conversion
                this.selectedItems.clear();
                document.querySelectorAll('input[type="checkbox"][data-item-id]').forEach(checkbox => {
                    checkbox.checked = false;
                });
                this.updateSelectAllCheckbox();
            }
        }));

        // Listener for selected item count update to bind to the button
        document.addEventListener('update-selected-count', event => {
            const buttonElement = document.querySelector('[x-ref="convertButton"]');
            if (buttonElement) {
                buttonElement.__alpine.$data.selectedCount = event.detail.count;
            }
        });
        
        // Listener for the custom event to trigger conversion
        document.addEventListener('convert-selected-items', e => {
            // Find the Alpine component responsible for selection
            const selectionComponent = document.querySelector('[x-data="tplItemSelection"]');
            if (selectionComponent && selectionComponent.__alpine) {
                selectionComponent.__alpine.$data.convertItems();
            }
        });
    });

    // Initialize DataTables after HTMX loads the table content
    document.body.addEventListener('htmx:afterSwap', function (event) {
        if (event.target.id === 'tplitemTable-container') {
            $('#tplitemTable').DataTable({
                "pageLength": 10, // Default page size
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "pagingType": "full_numbers", // For full pagination controls
                "dom": '<"flex justify-between items-center mb-4"lf>rtip', // DataTables layout
                "language": { // Optional: Customize DataTables labels
                    "search": "Search:",
                    "lengthMenu": "Show _MENU_ entries"
                }
            });
        }
    });

</script>
{% endblock %}
```

**File:** `design/templates/design/tplitem/_tplitem_table.html`

```html
<div x-data="tplItemSelection">
    <table id="tplitemTable" class="min-w-full divide-y divide-gray-200 shadow-md rounded-lg overflow-hidden">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <input type="checkbox" id="chkSelectAll" class="form-checkbox h-4 w-4 text-blue-600 border-gray-300 rounded"
                           @change="toggleSelectAll($event)">
                </th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assly No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Qty</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">TPL Qty</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Weld</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">LH</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">RH</th>
                <!-- Hidden IDs -->
                <th class="hidden">PId</th>
                <th class="hidden">CId</th>
                <th class="hidden">ItemId</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for item in tpl_items %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-center">
                    <input type="checkbox" data-item-id="{{ item.id }}" class="form-checkbox h-4 w-4 text-blue-600 border-gray-300 rounded"
                           @change="toggleSelectItem($event)"
                           :checked="selectedItems.has('{{ item.id }}')">
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.wono }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.dashboard_assly_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.dashboard_item_code }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.dashboard_description }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.dashboard_uom }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ item.qty|floatformat:2 }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ item.dashboard_tpl_qty|floatformat:2 }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ item.weldments }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ item.lh }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ item.rh }}</td>
                <!-- Hidden IDs -->
                <td class="hidden">{{ item.pid }}</td>
                <td class="hidden">{{ item.cid }}</td>
                <td class="hidden">{{ item.itemid.id }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="12" class="py-3 px-4 text-center text-sm text-gray-500">No TPL items found for conversion.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <!-- Hidden form for HTMX POST to convert items -->
    <form id="hiddenForm"
          hx-post="{% url 'convert_tplitems_to_bom' %}"
          hx-target="#tplitemTable-container"
          hx-swap="innerHTML"
          hx-trigger="submitForm from:#tplitemTable-container"
          hx-confirm="Are you sure you want to convert the selected items to BOM?"
          class="hidden">
        {% csrf_token %}
        <input type="hidden" name="selected_ids" :value="Array.from(selectedItems).join(',')">
    </form>
</div>
```

#### 4.5 URLs

Task: Define URL patterns for the views.

**File:** `design/urls.py`

```python
from django.urls import path
from .views import TplMasterDashboardView, TplMasterTablePartialView, ConvertTplItemsToBomView

urlpatterns = [
    path('tpl-items/dashboard/', TplMasterDashboardView.as_view(), name='tplitem_dashboard'),
    path('tpl-items/table/', TplMasterTablePartialView.as_view(), name='tplitem_table_partial'),
    path('tpl-items/convert-to-bom/', ConvertTplItemsToBomView.as_view(), name='convert_tplitems_to_bom'),
]

```

#### 4.6 Tests

Task: Write tests for the model and views.

**File:** `design/tests.py`

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db import connection, transaction
from datetime import date, time
from .models import TplMaster, ItemMaster, UnitMaster, BomMaster, WorkOrderMaster, DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID

class TplMasterModelTest(TestCase):
    # Use a dummy database for testing if not using actual legacy DB
    databases = {'default'} 

    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for related models first
        cls.unit_kg = UnitMaster.objects.create(id=1, symbol='KG')
        cls.unit_pc = UnitMaster.objects.create(id=2, symbol='PC')

        cls.item_assy1 = ItemMaster.objects.create(id=101, compid=DEFAULT_COMP_ID, finyearid=DEFAULT_FIN_YEAR_ID, item_code='ASSY001', manf_desc='Main Assembly 1', uom_basic=cls.unit_pc)
        cls.item_partA = ItemMaster.objects.create(id=102, compid=DEFAULT_COMP_ID, finyearid=DEFAULT_FIN_YEAR_ID, item_code='PARTA', manf_desc='Component A', uom_basic=cls.unit_kg)
        cls.item_partB = ItemMaster.objects.create(id=103, compid=DEFAULT_COMP_ID, finyearid=DEFAULT_FIN_YEAR_ID, item_code='PARTB', manf_desc='Component B', uom_basic=cls.unit_pc)
        cls.item_subassy = ItemMaster.objects.create(id=104, compid=DEFAULT_COMP_ID, finyearid=DEFAULT_FIN_YEAR_ID, item_code='SUBASSY01', manf_desc='Sub Assembly', uom_basic=cls.unit_pc)

        # Create test TplMaster data
        # Top-level assembly (PId=0, CId=0 implies it's the main item itself)
        cls.tpl_assy_main = TplMaster.objects.create(
            id=1, wono='WO001', compid=DEFAULT_COMP_ID, finyearid=DEFAULT_FIN_YEAR_ID,
            convert_to_bom=False, pid=0, cid=0, itemid=cls.item_assy1, qty=1.0, weldments=0, lh=0, rh=0
        )
        # Component of main assembly (PId refers to ItemId of TPL_ASSY_MAIN)
        cls.tpl_part_a = TplMaster.objects.create(
            id=2, wono='WO001', compid=DEFAULT_COMP_ID, finyearid=DEFAULT_FIN_YEAR_ID,
            convert_to_bom=False, pid=cls.item_assy1.id, cid=cls.item_partA.id, itemid=cls.item_partA, qty=5.0, weldments=1, lh=0, rh=0
        )
        # Another component, part of a sub-assembly
        cls.tpl_sub_assy = TplMaster.objects.create(
            id=3, wono='WO001', compid=DEFAULT_COMP_ID, finyearid=DEFAULT_FIN_YEAR_ID,
            convert_to_bom=False, pid=cls.item_assy1.id, cid=cls.item_subassy.id, itemid=cls.item_subassy, qty=1.0, weldments=0, lh=0, rh=0
        )
        cls.tpl_part_b = TplMaster.objects.create(
            id=4, wono='WO001', compid=DEFAULT_COMP_ID, finyearid=DEFAULT_FIN_YEAR_ID,
            convert_to_bom=False, pid=cls.item_subassy.id, cid=cls.item_partB.id, itemid=cls.item_partB, qty=2.0, weldments=0, lh=1, rh=1
        )
        # Already converted item for testing logic
        cls.tpl_converted = TplMaster.objects.create(
            id=5, wono='WO002', compid=DEFAULT_COMP_ID, finyearid=DEFAULT_FIN_YEAR_ID,
            convert_to_bom=True, pid=0, cid=0, itemid=cls.item_assy1, qty=1.0, weldments=0, lh=0, rh=0
        )

        # Ensure WorkOrderMaster exists for testing updates
        WorkOrderMaster.objects.create(id=1, wono='WO001', compid=DEFAULT_COMP_ID, update_wo=False)
        WorkOrderMaster.objects.create(id=2, wono='WO002', compid=DEFAULT_COMP_ID, update_wo=True)


    def test_tplmaster_creation(self):
        self.assertEqual(TplMaster.objects.count(), 5)
        self.assertEqual(self.tpl_assy_main.wono, 'WO001')
        self.assertFalse(self.tpl_part_a.convert_to_bom)

    def test_related_item_fields(self):
        self.assertEqual(self.tpl_part_a.itemid.item_code, 'PARTA')
        self.assertEqual(self.tpl_part_a.itemid.manf_desc, 'Component A')
        self.assertEqual(self.tpl_part_a.itemid.uom_basic.symbol, 'KG')

    def test_dashboard_assly_no_property(self):
        # Top-level item, Assly No should be its own item code (simplified logic based on original)
        self.assertEqual(self.tpl_assy_main.dashboard_assly_no, 'ASSY001')
        
        # Component 'PARTA' of 'ASSY001', PId is ASSY001's ItemId
        self.assertEqual(self.tpl_part_a.dashboard_assly_no, 'ASSY001')

        # Component 'PARTB' of 'SUBASSY01', PId is SUBASSY01's ItemId
        self.assertEqual(self.tpl_part_b.dashboard_assly_no, 'SUBASSY01')


    def test_dashboard_tpl_qty_property(self):
        # As per notes, this is a placeholder for complex recursive logic.
        # It should return the base quantity for now.
        self.assertEqual(self.tpl_assy_main.dashboard_tpl_qty, 1.0)
        self.assertEqual(self.tpl_part_a.dashboard_tpl_qty, 5.0)

    def test_with_dashboard_data_queryset(self):
        # Ensure the queryset includes the annotated fields
        qs = TplMaster.objects.with_dashboard_data()
        self.assertEqual(qs.count(), 4) # WO001 items (4) + WO002 converted (1), so 4 unconverted
        
        # Check an item from the annotated queryset
        tpl_item = qs.get(id=self.tpl_part_a.id)
        self.assertEqual(tpl_item.dashboard_item_code, 'PARTA')
        self.assertEqual(tpl_item.dashboard_description, 'Component A')
        self.assertEqual(tpl_item.dashboard_uom, 'KG')
        self.assertEqual(tpl_item.dashboard_assly_no, 'ASSY001') # From the subquery annotation


    def test_convert_selected_to_bom_pid_0(self):
        # Convert the top-level assembly
        selected_ids = [self.tpl_assy_main.id]
        session_id = 'test_user_session'
        
        converted_count = TplMaster.objects.convert_selected_to_bom(selected_ids, session_id, DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID)
        self.assertEqual(converted_count, 1)

        # Check BomMaster record
        bom_entry = BomMaster.objects.get(wono='WO001', item=self.item_assy1)
        self.assertEqual(bom_entry.pid, 0)
        self.assertEqual(bom_entry.qty, 1.0)
        self.assertEqual(bom_entry.sessionid, session_id)

        # Check TplMaster update
        self.tpl_assy_main.refresh_from_db()
        self.assertTrue(self.tpl_assy_main.convert_to_bom)

        # Check WorkOrderMaster update
        wo_master = WorkOrderMaster.objects.get(wono='WO001')
        self.assertTrue(wo_master.update_wo)
    
    def test_convert_selected_to_bom_pid_not_0(self):
        # First convert the parent `tpl_sub_assy` so its `convert_to_bom` is true for child check
        TplMaster.objects.convert_selected_to_bom([self.tpl_sub_assy.id], 'test_user', DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID)
        self.tpl_sub_assy.refresh_from_db()
        self.assertTrue(self.tpl_sub_assy.convert_to_bom)

        # Now convert a child item `tpl_part_b` (PId is `item_subassy.id`)
        selected_ids = [self.tpl_part_b.id]
        session_id = 'test_user_session_2'
        
        converted_count = TplMaster.objects.convert_selected_to_bom(selected_ids, session_id, DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID)
        self.assertEqual(converted_count, 1)

        bom_entry = BomMaster.objects.get(wono='WO001', item=self.item_partB)
        self.assertEqual(bom_entry.pid, self.item_subassy.id)
        self.assertEqual(bom_entry.qty, 2.0)

        self.tpl_part_b.refresh_from_db()
        self.assertTrue(self.tpl_part_b.convert_to_bom)


    def test_convert_selected_to_bom_parent_not_converted(self):
        # Try to convert a child without its parent being converted first
        initial_bom_count = BomMaster.objects.count()
        selected_ids = [self.tpl_part_a.id] # Parent is tpl_assy_main (item_assy1.id), not converted yet
        session_id = 'test_user_session_3'

        converted_count = TplMaster.objects.convert_selected_to_bom(selected_ids, session_id, DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID)
        self.assertEqual(converted_count, 0) # Should not convert if parent not converted (based on original logic)
        self.assertEqual(BomMaster.objects.count(), initial_bom_count)
        self.tpl_part_a.refresh_from_db()
        self.assertFalse(self.tpl_part_a.convert_to_bom)


class TplMasterViewsTest(TestCase):
    # Use a dummy database for testing
    databases = {'default'}

    @classmethod
    def setUpTestData(cls):
        # Set up test data as in model test
        cls.unit_kg = UnitMaster.objects.create(id=1, symbol='KG')
        cls.item_assy1 = ItemMaster.objects.create(id=101, compid=DEFAULT_COMP_ID, finyearid=DEFAULT_FIN_YEAR_ID, item_code='ASSY001', manf_desc='Main Assembly 1', uom_basic=cls.unit_kg)
        cls.item_partA = ItemMaster.objects.create(id=102, compid=DEFAULT_COMP_ID, finyearid=DEFAULT_FIN_YEAR_ID, item_code='PARTA', manf_desc='Component A', uom_basic=cls.unit_kg)
        
        cls.tpl_item1 = TplMaster.objects.create(id=1, wono='WO001', compid=DEFAULT_COMP_ID, finyearid=DEFAULT_FIN_YEAR_ID, convert_to_bom=False, pid=0, cid=0, itemid=cls.item_assy1, qty=1.0, weldments=0, lh=0, rh=0)
        cls.tpl_item2 = TplMaster.objects.create(id=2, wono='WO001', compid=DEFAULT_COMP_ID, finyearid=DEFAULT_FIN_YEAR_ID, convert_to_bom=False, pid=cls.item_assy1.id, cid=cls.item_partA.id, itemid=cls.item_partA, qty=2.0, weldments=0, lh=0, rh=0)
        cls.tpl_item_converted = TplMaster.objects.create(id=3, wono='WO002', compid=DEFAULT_COMP_ID, finyearid=DEFAULT_FIN_YEAR_ID, convert_to_bom=True, pid=0, cid=0, itemid=cls.item_assy1, qty=1.0, weldments=0, lh=0, rh=0)
        
        WorkOrderMaster.objects.create(id=1, wono='WO001', compid=DEFAULT_COMP_ID, update_wo=False)
        WorkOrderMaster.objects.create(id=2, wono='WO002', compid=DEFAULT_COMP_ID, update_wo=True)


    def setUp(self):
        self.client = Client()
        # Set session variables as they are used in views and models
        session = self.client.session
        session['compid'] = DEFAULT_COMP_ID
        session['finyear'] = DEFAULT_FIN_YEAR_ID
        session['username'] = 'test_user_session'
        session.save()

    def test_dashboard_view_get(self):
        response = self.client.get(reverse('tplitem_dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/tplitem/dashboard.html')
        self.assertIn('tpl_items', response.context)
        # Should only see unconverted items
        self.assertEqual(response.context['tpl_items'].count(), 2) # tpl_item1, tpl_item2

    def test_table_partial_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('tplitem_table_partial'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/tplitem/_tplitem_table.html')
        self.assertIn('tpl_items', response.context)
        self.assertEqual(response.context['tpl_items'].count(), 2) # Should only see unconverted items

    def test_convert_tplitems_to_bom_post_success(self):
        # Check initial state
        self.assertFalse(self.tpl_item1.convert_to_bom)
        self.assertFalse(self.tpl_item2.convert_to_bom)
        self.assertFalse(WorkOrderMaster.objects.get(wono='WO001').update_wo)
        initial_bom_count = BomMaster.objects.count()

        data = {'selected_ids': [self.tpl_item1.id, self.tpl_item2.id]}
        headers = {'HTTP_HX_REQUEST': 'true'}

        response = self.client.post(reverse('convert_tplitems_to_bom'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshTplItemList')

        # Verify database changes
        self.tpl_item1.refresh_from_db()
        self.tpl_item2.refresh_from_db()
        self.assertTrue(self.tpl_item1.convert_to_bom)
        self.assertTrue(self.tpl_item2.convert_to_bom)
        self.assertEqual(BomMaster.objects.count(), initial_bom_count + 2)
        self.assertTrue(WorkOrderMaster.objects.get(wono='WO001').update_wo)

        # Check messages (requires MessagesMiddleware in settings)
        messages = list(response.context.get('messages')) if response.context else []
        if not messages: # For HTMX 204, messages are handled client-side via HX-Trigger/Swap
             # We rely on the client-side JS to show message triggered by HX-Trigger
             pass
        else:
            self.assertEqual(len(messages), 1)
            self.assertEqual(str(messages[0]), '2 TPL items converted to BOM successfully.')


    def test_convert_tplitems_to_bom_post_no_selection(self):
        initial_bom_count = BomMaster.objects.count()
        data = {} # No selected_ids
        headers = {'HTTP_HX_REQUEST': 'true'}

        response = self.client.post(reverse('convert_tplitems_to_bom'), data, **headers)
        self.assertEqual(response.status_code, 204) # Still 204 for HTMX, error messages are in headers
        self.assertIn('HX-Trigger', response.headers) # Should still trigger refresh

        # No changes should occur
        self.assertEqual(BomMaster.objects.count(), initial_bom_count)
        
        # Messages should indicate error
        messages = list(self.client.get(reverse('tplitem_dashboard')).context['messages'])
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Error: Please select at least one item to convert.')

    def test_convert_tplitems_to_bom_post_non_htmx(self):
        initial_bom_count = BomMaster.objects.count()
        data = {'selected_ids': [self.tpl_item1.id]}

        response = self.client.post(reverse('convert_tplitems_to_bom'), data)
        self.assertEqual(response.status_code, 302) # Redirect for non-HTMX
        self.assertRedirects(response, reverse('tplitem_dashboard'))

        self.tpl_item1.refresh_from_db()
        self.assertTrue(self.tpl_item1.convert_to_bom)
        self.assertEqual(BomMaster.objects.count(), initial_bom_count + 1)

```

---

### Step 5: HTMX and Alpine.js Integration

The templates and views already integrate HTMX for dynamic content loading and form submissions, and Alpine.js for client-side interactivity like managing checkbox states and the "Select All" functionality.

*   **HTMX:**
    *   `hx-get` on `tplitemTable-container` to fetch `_tplitem_table.html` on `load` and `refreshTplItemList`.
    *   `hx-post` on a hidden form (triggered by Alpine.js for the "Add To BOM" button) to `convert_tplitems_to_bom`.
    *   `HX-Trigger` header from `ConvertTplItemsToBomView` (`refreshTplItemList`) to tell the client to reload the table after conversion.
    *   `HX-Confirm` is used for client-side confirmation on the "Add To BOM" button.

*   **Alpine.js:**
    *   Manages the `selectedItems` Set to keep track of checked items.
    *   `toggleSelectAll` and `toggleSelectItem` methods handle checkbox interactions.
    *   Dynamically updates the `selectedCount` on the "Add To BOM" button to enable/disable it.
    *   Handles the confirmation prompt using `confirm()`.
    *   Dispatches a custom event `convert-selected-items` to trigger the HTMX POST.

*   **DataTables:**
    *   The `_tplitem_table.html` uses `id="tplitemTable"`.
    *   A JavaScript snippet in `dashboard.html` (`htmx:afterSwap` listener) initializes DataTables on the loaded table content.

*   **DRY Templates:**
    *   `dashboard.html` extends `core/base.html`.
    *   `_tplitem_table.html` is a partial template loaded dynamically, avoiding repetition of boilerplate HTML.

*   **Fat Model, Thin View:**
    *   All complex data retrieval (`with_dashboard_data`) and business logic (`convert_selected_to_bom`) are within the `TplMaster` model and its custom manager.
    *   Views (`TplMasterDashboardView`, `ConvertTplItemsToBomView`) are concise, primarily orchestrating requests, calling model methods, and rendering responses.

---

### Final Notes

This comprehensive plan addresses the ASP.NET code by translating its functionality into a modern Django stack. Placeholders for `CompId`, `FinYearId`, and `SessionId` should be replaced with actual session management or user profile integration in a production environment. The `dashboard_tpl_qty` (recursive quantity) is identified as a complex business logic element that would require a detailed understanding of `fun.RecurQty` for a precise Django implementation, but a placeholder has been provided demonstrating how such a method would reside in the model. The focus remains on automated conversion patterns and clear, non-technical communication for business stakeholders.