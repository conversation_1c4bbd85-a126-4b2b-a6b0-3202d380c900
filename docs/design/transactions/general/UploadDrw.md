## ASP.NET to Django Conversion Script: 

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
The ASP.NET code explicitly performs `fun.update("tblDG_Item_Master", ...)` which indicates the primary database table being interacted with. The update statements reveal the columns being modified and used in the `WHERE` clause.

- **Table Name:** `tblDG_Item_Master`
- **Identified Columns:**
    - `Id` (Used in `WHERE` clause, assumed to be the primary key)
    - `CompId` (Used in `WHERE` clause, likely Company ID)
    - `SysDate` (System Date, updated upon file upload)
    - `SysTime` (System Time, updated upon file upload)
    - `SessionId` (User session ID, updated upon file upload)
    - `FileName` (Name of the drawing/main file)
    - `FileSize` (Size of the drawing/main file)
    - `ContentType` (MIME type of the drawing/main file)
    - `FileData` (Binary content of the drawing/main file, stored directly in DB)
    - `AttName` (Name of the attachment file)
    - `AttSize` (Size of the attachment file)
    - `AttContentType` (MIME type of the attachment file)
    - `AttData` (Binary content of the attachment file, stored directly in DB)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
The provided ASP.NET page (`UploadDrw.aspx`) focuses specifically on updating an existing record in the `tblDG_Item_Master` table with file data. It does not perform creation, reading for display, or deletion of records on this particular page.

- **Create:** Not present. This page does not create new `ItemMaster` records.
- **Read:** Implicitly, a record is "read" to identify it using `Id` and `CompId` for the update operation. However, no data is retrieved and displayed on the page.
- **Update:** This is the core functionality. An existing `tblDG_Item_Master` record is updated based on the `Id` and `CompId` provided. The update action is conditional:
    - If a query string parameter `img` is `'0'`, it updates the `FileName`, `FileSize`, `ContentType`, and `FileData` columns.
    - If `img` is anything else (implicitly `'1'` in the original context), it updates `AttName`, `AttSize`, `AttContentType`, and `AttData` columns.
    - `SysDate`, `SysTime`, and `SessionId` are updated in both cases.
- **Delete:** Not present.

**Validation Logic:**
- The ASP.NET code implicitly handles cases where no file is uploaded (the `if (FileUpload1.PostedFile != null)` check). In Django, this will be handled by the `FileField`'s required validation.
- The `try-catch` blocks in the original code suggest basic error handling. Django's form validation and view error handling will replace this.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

- **File Upload:** An `<asp:FileUpload>` control (`FileUpload1`) is used to select a file from the user's local system.
- **Buttons:**
    - An `<asp:Button>` (`bynUpload`) triggers the file upload and update process. It has a client-side JavaScript confirmation (`confirmationUpload()`) and a server-side click handler (`bynUpload_Click`).
    - An `<asp:Button>` (`btnCancel`) cancels the operation and redirects the user. It has a server-side click handler (`btnCancel_Click`).
- **Layout:** The layout uses basic HTML tables for positioning elements. This will be replaced with modern CSS (Tailwind CSS).
- **Client-Side JavaScript:** `PopUpMsg.js` and `loadingNotifier.js` are mentioned, suggesting pop-up messages and loading indicators. This functionality will be replaced by Django's `messages` framework, HTMX, and Alpine.js.

### Step 4: Generate Django Code

For this migration, we will create a new Django application named `design` to contain this functionality, mapping to the `Module_Design` part of the original ASP.NET path.

#### 4.1 Models (`design/models.py`)

This model directly maps to the `tblDG_Item_Master` table. The `FileData` and `AttData` columns, which store binary content, are mapped to `BinaryField`. Business logic for updating file data is encapsulated in model methods.

```python
# design/models.py
from django.db import models
from django.utils import timezone # Used for getting current date/time

class ItemMaster(models.Model):
    # Core identifying fields based on the WHERE clause
    id = models.IntegerField(db_column='Id', primary_key=True) # Assumed PK based on ASP.NET usage
    comp_id = models.IntegerField(db_column='CompId') # Company ID

    # Fields for the main drawing/file (used when 'img' flag is '0')
    sys_date = models.DateField(db_column='SysDate', null=True, blank=True)
    sys_time = models.TimeField(db_column='SysTime', null=True, blank=True)
    session_id = models.CharField(db_column='SessionId', max_length=255, null=True, blank=True)
    file_name = models.CharField(db_column='FileName', max_length=255, null=True, blank=True)
    file_size = models.BigIntegerField(db_column='FileSize', null=True, blank=True)
    content_type = models.CharField(db_column='ContentType', max_length=100, null=True, blank=True)
    file_data = models.BinaryField(db_column='FileData', null=True, blank=True) # Stores binary content

    # Fields for attachments (used when 'img' flag is not '0')
    att_name = models.CharField(db_column='AttName', max_length=255, null=True, blank=True)
    att_size = models.BigIntegerField(db_column='AttSize', null=True, blank=True)
    att_content_type = models.CharField(db_column='AttContentType', max_length=100, null=True, blank=True)
    att_data = models.BinaryField(db_column='AttData', null=True, blank=True) # Stores binary content

    class Meta:
        managed = False  # Critical: Tells Django not to manage this table (it exists)
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master Record'
        verbose_name_plural = 'Item Master Records'

    def __str__(self):
        return f"Item Master {self.id} (Company: {self.comp_id})"
        
    def update_drawing_data(self, file_object, session_user_id):
        """
        Updates the drawing-related fields (FileData, FileName, FileSize, ContentType).
        Encapsulates the business logic for updating the main drawing.
        """
        now = timezone.now()
        self.sys_date = now.date()
        self.sys_time = now.time()
        self.session_id = session_user_id
        self.file_name = file_object.name
        self.file_size = file_object.size
        self.content_type = file_object.content_type
        self.file_data = file_object.read() # Read file content into binary
        self.save()

    def update_attachment_data(self, file_object, session_user_id):
        """
        Updates the attachment-related fields (AttData, AttName, AttSize, AttContentType).
        Encapsulates the business logic for updating an attachment.
        """
        now = timezone.now()
        self.sys_date = now.date() # Original ASP.NET code updates these for attachments too
        self.sys_time = now.time()
        self.session_id = session_user_id
        self.att_name = file_object.name
        self.att_size = file_object.size
        self.att_content_type = file_object.content_type
        self.att_data = file_object.read() # Read file content into binary
        self.save()

```

#### 4.2 Forms (`design/forms.py`)

A simple `forms.Form` is used because the form's purpose is solely to capture a file upload. The decision of which `ItemMaster` fields to update (`FileData` vs. `AttData`) is handled in the view, not directly by the form.

```python
# design/forms.py
from django import forms

class DrawingUploadForm(forms.Form):
    uploaded_file = forms.FileField(
        label='Select Drawing/File',
        widget=forms.ClearableFileInput(attrs={
            'class': 'block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 '
                     'focus:outline-none file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 '
                     'file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'
        })
    )

```

#### 4.3 Views (`design/views.py`)

This section replaces the ASP.NET code-behind logic. A `FormView` is chosen as it's primarily handling a single form submission. The view extracts necessary context (query parameters, session data) and delegates the update logic to the `ItemMaster` model methods.

```python
# design/views.py
from django.views.generic.edit import FormView
from django.urls import reverse
from django.contrib import messages
from django.http import HttpResponseRedirect
from django.shortcuts import get_object_or_404, redirect

from .models import ItemMaster
from .forms import DrawingUploadForm

class UploadDrawingView(FormView):
    template_name = 'design/upload/form.html'
    form_class = DrawingUploadForm

    def get_context_data(self, **kwargs):
        """
        Pulls data from query parameters to pass to the template, similar to ASP.NET Page_Load.
        """
        context = super().get_context_data(**kwargs)
        context['wono'] = self.request.GET.get('WONo', '') # Work Order Number
        context['item_id'] = self.request.GET.get('Id', '') # Item ID
        context['img_flag'] = self.request.GET.get('img', '0') # Image flag (default '0')
        return context

    def form_valid(self, form):
        """
        Handles valid form submission, retrieves the target record, and updates based on 'img' flag.
        Delegates core update logic to the model.
        """
        item_id = self.request.GET.get('Id')
        img_flag = self.request.GET.get('img', '0')
        
        # Retrieve necessary session data (mimicking ASP.NET Session variables)
        # In a real app, 'compid' and 'username' would likely come from an authenticated user object.
        # Example: self.request.user.company.id, self.request.user.username
        comp_id = self.request.session.get('compid') # Assumes session has 'compid'
        session_user_id = self.request.session.get('username') # Assumes session has 'username'
        
        if not comp_id or not session_user_id:
            messages.error(self.request, "Session information (Company ID or User ID) is missing. Please log in.")
            return self.form_invalid(form) # Re-render form with error

        # Get the ItemMaster record to be updated
        item_master_record = get_object_or_404(ItemMaster, id=item_id)
        
        uploaded_file = form.cleaned_data['uploaded_file']

        if img_flag == '0':
            item_master_record.update_drawing_data(uploaded_file, session_user_id)
            messages.success(self.request, "Drawing uploaded and updated successfully.")
        else:
            item_master_record.update_attachment_data(uploaded_file, session_user_id)
            messages.success(self.request, "Attachment uploaded and updated successfully.")
        
        # Mimic the ASP.NET Response.Redirect behavior after successful upload or cancellation.
        # Construct the redirect URL with original query parameters.
        wono = self.request.GET.get('WONo', '')
        original_id = self.request.GET.get('Id', '')
        
        # Placeholder for the actual target URL from the ASP.NET redirect
        # (e.g., TPL_Design_WO_TreeView_Edit.aspx)
        redirect_url = reverse('design:work_order_tree_view_edit') 
        redirect_url += f"?WONo={wono}&PgUrl=TPL_Design_WO_TreeView.aspx&ModId=3&SubModId=23&id={original_id}"
        
        return HttpResponseRedirect(redirect_url)

    def form_invalid(self, form):
        """Handles invalid form submission, adding an error message."""
        messages.error(self.request, "File upload failed. Please correct the errors below.")
        return super().form_invalid(form)

class WorkOrderTreeViewEditView(FormView):
    """
    Placeholder view for the ASP.NET redirect target: TPL_Design_WO_TreeView_Edit.aspx.
    In a full migration, this would be a detailed view for Work Order Tree.
    """
    template_name = 'design/work_order/tree_view_edit.html'
    form_class = DrawingUploadForm # Dummy form if not relevant
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['wono'] = self.request.GET.get('WONo', '')
        context['item_id'] = self.request.GET.get('Id', '')
        return context

```

#### 4.4 Templates

The templates are designed to integrate with Tailwind CSS and use Alpine.js for simple UI state management (like a loading indicator) and HTMX where appropriate, though for this specific page, a full form submission and redirect mimics the original ASP.NET behavior.

**`design/templates/design/upload/form.html`**

```html
<!-- design/templates/design/upload/form.html -->
{% extends 'core/base.html' %}
{% load tailwind_filters %} {# Assuming you have django-tailwind-filters installed #}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6 text-gray-800">Upload Drawing/File</h2>

    <div class="bg-white p-6 rounded-lg shadow-xl max-w-lg mx-auto border border-gray-200">
        <h3 class="text-xl font-semibold text-gray-900 mb-5 border-b pb-3">TPL Design - Upload Drw</h3>
        
        <form x-data="{ uploading: false }" 
              @submit.prevent="if (confirm('Are you sure you want to upload?')) { $el.submit(); uploading = true; }"
              method="post" 
              enctype="multipart/form-data" 
              class="space-y-6">
            {% csrf_token %}
            
            <div class="mb-4">
                <label for="{{ form.uploaded_file.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.uploaded_file.label }}
                </label>
                {{ form.uploaded_file }}
                {% if form.uploaded_file.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.uploaded_file.errors.as_text }}</p>
                {% endif %}
            </div>
            
            <div class="mt-8 flex items-center justify-end space-x-4">
                <button 
                    type="button" 
                    class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                    onclick="window.location.href='{% url 'design:work_order_tree_view_edit' %}?WONo={{ wono }}&PgUrl=TPL_Design_WO_TreeView.aspx&ModId=3&SubModId=23&id={{ item_id }}'">
                    Cancel
                </button>
                <button 
                    type="submit" 
                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                    :disabled="uploading">
                    <span x-show="!uploading">Upload</span>
                    <span x-show="uploading" class="flex items-center">
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Uploading...
                    </span>
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
```

**`design/templates/design/work_order/tree_view_edit.html` (Placeholder for Redirect Target)**

```html
<!-- design/templates/design/work_order/tree_view_edit.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6 text-gray-800">Work Order Tree View Edit (Placeholder)</h2>
    <p class="text-gray-700">This page serves as a placeholder for the Work Order Tree View, the target of the redirect after an upload or cancellation.</p>
    <div class="mt-4 p-4 bg-gray-100 rounded-lg shadow-sm">
        <p class="text-gray-600"><strong>Work Order No (WONo):</strong> <span class="font-semibold text-blue-700">{{ wono }}</span></p>
        <p class="text-gray-600"><strong>Item ID (Id):</strong> <span class="font-semibold text-blue-700">{{ item_id }}</span></p>
    </div>
    <div class="mt-6 text-gray-700">
        <p>In a fully migrated system, this page would render the detailed work order structure, allowing further editing or viewing of the newly uploaded file.</p>
        <p>This demonstrates the seamless transition and continuity of user flow from the legacy ASP.NET application.</p>
    </div>
</div>
{% endblock %}
```

#### 4.5 URLs (`design/urls.py`)

Defines the URL patterns for accessing the upload functionality and the placeholder redirect target.

```python
# design/urls.py
from django.urls import path
from .views import UploadDrawingView, WorkOrderTreeViewEditView

app_name = 'design' # Namespace for this application

urlpatterns = [
    # URL for the file upload page, accepting query parameters for context
    path('upload-drawing/', UploadDrawingView.as_view(), name='upload_drawing'),

    # Placeholder URL for the page the original ASP.NET application redirects to
    path('work-order-tree-view-edit/', WorkOrderTreeViewEditView.as_view(), name='work_order_tree_view_edit'),
]

```

#### 4.6 Tests (`design/tests.py`)

Comprehensive unit tests for the `ItemMaster` model methods and integration tests for the `UploadDrawingView` to ensure proper file handling, database updates, and redirection logic.

```python
# design/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.core.files.uploadedfile import SimpleUploadedFile
from unittest.mock import patch
from datetime import date, time
from django.utils import timezone

from .models import ItemMaster

class ItemMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a single ItemMaster record to be used across all tests for update operations.
        # This simulates an existing record in the tblDG_Item_Master table.
        ItemMaster.objects.create(
            id=1,
            comp_id=101,
            sys_date=date(2023, 1, 1),
            sys_time=time(10, 0, 0),
            session_id='initial_user',
            file_name='initial_drawing.pdf',
            file_size=100,
            content_type='application/pdf',
            file_data=b'initial_drawing_data',
            att_name='initial_attachment.txt',
            att_size=50,
            att_content_type='text/plain',
            att_data=b'initial_attachment_data'
        )

    def setUp(self):
        # Ensure each test starts with a clean state of the object, if it's modified in a test.
        # Re-fetch the object or reset its attributes as needed.
        self.item = ItemMaster.objects.get(id=1)

    def test_item_master_creation(self):
        """Verify the initial creation and basic attributes of the ItemMaster record."""
        self.assertEqual(self.item.id, 1)
        self.assertEqual(self.item.comp_id, 101)
        self.assertEqual(self.item.file_name, 'initial_drawing.pdf')
        self.assertEqual(self.item.att_name, 'initial_attachment.txt')
        self.assertEqual(str(self.item), "Item Master 1 (Company: 101)")

    @patch('django.utils.timezone.now')
    def test_update_drawing_data(self, mock_now):
        """Test the model method for updating drawing-specific fields."""
        # Mock timezone.now() to ensure deterministic date/time values for testing.
        mock_now.return_value = timezone.datetime(2024, 7, 15, 12, 30, 0, tzinfo=timezone.utc)
        
        test_file = SimpleUploadedFile("new_drawing.jpg", b"new_drawing_content", content_type="image/jpeg")
        test_session_id = "drawing_uploader"
        
        self.item.update_drawing_data(test_file, test_session_id)
        self.item.refresh_from_db() # Reload from DB to get the updated state

        self.assertEqual(self.item.file_name, "new_drawing.jpg")
        self.assertEqual(self.item.file_size, len(b"new_drawing_content"))
        self.assertEqual(self.item.content_type, "image/jpeg")
        self.assertEqual(self.item.file_data, b"new_drawing_content")
        self.assertEqual(self.item.session_id, test_session_id)
        self.assertEqual(self.item.sys_date, date(2024, 7, 15))
        self.assertEqual(self.item.sys_time, time(12, 30, 0))

        # Ensure attachment fields remain unchanged if not targeted by this method
        self.assertEqual(self.item.att_name, 'initial_attachment.txt')

    @patch('django.utils.timezone.now')
    def test_update_attachment_data(self, mock_now):
        """Test the model method for updating attachment-specific fields."""
        # Mock timezone.now() for deterministic date/time values.
        mock_now.return_value = timezone.datetime(2024, 7, 16, 14, 0, 0, tzinfo=timezone.utc)

        test_file = SimpleUploadedFile("report.pdf", b"new_attachment_content", content_type="application/pdf")
        test_session_id = "attachment_uploader"
        
        self.item.update_attachment_data(test_file, test_session_id)
        self.item.refresh_from_db() # Reload from DB to get the updated state

        self.assertEqual(self.item.att_name, "report.pdf")
        self.assertEqual(self.item.att_size, len(b"new_attachment_content"))
        self.assertEqual(self.item.att_content_type, "application/pdf")
        self.assertEqual(self.item.att_data, b"new_attachment_content")
        self.assertEqual(self.item.session_id, test_session_id)
        self.assertEqual(self.item.sys_date, date(2024, 7, 16))
        self.assertEqual(self.item.sys_time, time(14, 0, 0))

        # Ensure drawing fields remain unchanged if not targeted by this method
        self.assertEqual(self.item.file_name, 'initial_drawing.pdf') # This might fail if test_update_drawing_data ran first and changed it.
                                                                    # This is why setUp() resets the object state.

class UploadDrawingViewTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a dummy ItemMaster record for view tests.
        # This ensures the target record exists for updates.
        ItemMaster.objects.create(id=1, comp_id=101)

    def setUp(self):
        self.client = Client()
        # Mock Django session variables to simulate ASP.NET Session.
        session = self.client.session
        session['compid'] = 101
        session['username'] = 'authenticated_user'
        session.save()
        
        # Prepare a mock uploaded file for tests
        self.test_drawing_file = SimpleUploadedFile(
            "my_design.png",
            b"fake_image_content",
            content_type="image/png"
        )
        self.test_attachment_file = SimpleUploadedFile(
            "doc_attach.pdf",
            b"fake_pdf_content",
            content_type="application/pdf"
        )
        
        # Define expected redirect base URL for cleaner tests
        self.expected_redirect_base_url = reverse('design:work_order_tree_view_edit')

    def test_get_upload_drawing_view(self):
        """Test that the GET request renders the correct template and passes context."""
        query_params = {'WONo': 'WO_TEST_1', 'Id': '1', 'img': '0'}
        response = self.client.get(reverse('design:upload_drawing'), query_params)
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/upload/form.html')
        self.assertContains(response, 'Upload Drawing/File')
        self.assertContains(response, 'WO_TEST_1') # Check if WONo is in context
        self.assertContains(response, '1') # Check if Id is in context

    @patch('django.utils.timezone.now')
    def test_post_upload_drawing_view_img_0(self, mock_now):
        """Test POST request for drawing upload (img=0) and database update."""
        mock_now.return_value = timezone.datetime(2024, 7, 17, 9, 0, 0, tzinfo=timezone.utc)

        item_before_update = ItemMaster.objects.get(id=1)
        self.assertIsNone(item_before_update.file_data) # Ensure it's empty initially

        query_params = {'WONo': 'WO_DRAW_1', 'Id': '1', 'img': '0'}
        response = self.client.post(
            reverse('design:upload_drawing') + f"?WONo={query_params['WONo']}&Id={query_params['Id']}&img={query_params['img']}",
            {'uploaded_file': self.test_drawing_file}
        )
        
        # Verify successful redirect (HTTP 302)
        expected_redirect_url = (f"{self.expected_redirect_base_url}?"
                                 f"WONo={query_params['WONo']}&PgUrl=TPL_Design_WO_TreeView.aspx&ModId=3&SubModId=23&id={query_params['Id']}")
        self.assertRedirects(response, expected_redirect_url, status_code=302, target_status_code=200)
        
        # Verify ItemMaster record was updated correctly
        item_after_update = ItemMaster.objects.get(id=1)
        self.assertEqual(item_after_update.file_name, "my_design.png")
        self.assertEqual(item_after_update.file_size, len(b"fake_image_content"))
        self.assertEqual(item_after_update.content_type, "image/png")
        self.assertEqual(item_after_update.file_data, b"fake_image_content")
        self.assertEqual(item_after_update.session_id, 'authenticated_user')
        self.assertEqual(item_after_update.sys_date, date(2024, 7, 17))
        self.assertEqual(item_after_update.sys_time, time(9, 0, 0))
        self.assertFalse(item_after_update.att_name) # Ensure attachment fields were not touched

    @patch('django.utils.timezone.now')
    def test_post_upload_drawing_view_img_non_0(self, mock_now):
        """Test POST request for attachment upload (img!=0) and database update."""
        mock_now.return_value = timezone.datetime(2024, 7, 18, 10, 0, 0, tzinfo=timezone.utc)

        item_before_update = ItemMaster.objects.get(id=1)
        self.assertIsNone(item_before_update.att_data) # Ensure it's empty initially

        query_params = {'WONo': 'WO_ATTACH_1', 'Id': '1', 'img': '1'}
        response = self.client.post(
            reverse('design:upload_drawing') + f"?WONo={query_params['WONo']}&Id={query_params['Id']}&img={query_params['img']}",
            {'uploaded_file': self.test_attachment_file}
        )
        
        # Verify successful redirect (HTTP 302)
        expected_redirect_url = (f"{self.expected_redirect_base_url}?"
                                 f"WONo={query_params['WONo']}&PgUrl=TPL_Design_WO_TreeView.aspx&ModId=3&SubModId=23&id={query_params['Id']}")
        self.assertRedirects(response, expected_redirect_url, status_code=302, target_status_code=200)
        
        # Verify ItemMaster record was updated correctly
        item_after_update = ItemMaster.objects.get(id=1)
        self.assertEqual(item_after_update.att_name, "doc_attach.pdf")
        self.assertEqual(item_after_update.att_size, len(b"fake_pdf_content"))
        self.assertEqual(item_after_update.att_content_type, "application/pdf")
        self.assertEqual(item_after_update.att_data, b"fake_pdf_content")
        self.assertEqual(item_after_update.session_id, 'authenticated_user')
        self.assertEqual(item_after_update.sys_date, date(2024, 7, 18))
        self.assertEqual(item_after_update.sys_time, time(10, 0, 0))
        self.assertFalse(item_after_update.file_name) # Ensure drawing fields were not touched

    def test_post_upload_drawing_view_no_file_uploaded(self):
        """Test form submission without a file, expecting validation error."""
        query_params = {'WONo': 'WO_NOFILE', 'Id': '1', 'img': '0'}
        response = self.client.post(
            reverse('design:upload_drawing') + f"?WONo={query_params['WONo']}&Id={query_params['Id']}&img={query_params['img']}",
            {} # No file provided
        )
        
        # Should render the form again with errors (HTTP 200)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/upload/form.html')
        self.assertContains(response, 'This field is required.') # Django's default FileField validation
        self.assertContains(response, 'File upload failed. Please correct the errors below.') # Custom message

    def test_post_upload_drawing_view_missing_item_id(self):
        """Test submission with a non-existent Item ID."""
        query_params = {'WONo': 'WO_MISSING', 'Id': '999', 'img': '0'} # ID 999 does not exist
        response = self.client.post(
            reverse('design:upload_drawing') + f"?WONo={query_params['WONo']}&Id={query_params['Id']}&img={query_params['img']}",
            {'uploaded_file': self.test_drawing_file}
        )
        
        # Expect a 404 Not Found error from get_object_or_404
        self.assertEqual(response.status_code, 404)

    def test_post_upload_drawing_view_missing_session_data(self):
        """Test submission when required session data (compid, username) is missing."""
        del self.client.session['compid'] # Remove one required session variable
        self.client.session.save()

        query_params = {'WONo': 'WO_SES_MISS', 'Id': '1', 'img': '0'}
        response = self.client.post(
            reverse('design:upload_drawing') + f"?WONo={query_params['WONo']}&Id={query_params['Id']}&img={query_params['img']}",
            {'uploaded_file': self.test_drawing_file}
        )
        
        # Should render the form again with an error message
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/upload/form.html')
        self.assertContains(response, "Session information (Company ID or User ID) is missing. Please log in.")

    def test_cancel_button_redirection(self):
        """
        Verify the cancel button's `onclick` attribute points to the correct redirect URL.
        This test checks the HTML output, as the button itself performs a client-side redirect.
        """
        query_params = {'WONo': 'WO_CANCEL', 'Id': '1', 'img': '0'}
        response = self.client.get(reverse('design:upload_drawing'), query_params)
        self.assertEqual(response.status_code, 200)
        
        expected_redirect_url_on_cancel = (f"{self.expected_redirect_base_url}?"
                                           f"WONo={query_params['WONo']}&PgUrl=TPL_Design_WO_TreeView.aspx&ModId=3&SubModId=23&id={query_params['Id']}")
        
        # Check if the generated HTML for the cancel button contains the correct onclick attribute
        self.assertContains(response, f"onclick=\"window.location.href='{expected_redirect_url_on_cancel}'\"", html=False)

```

### Step 5: HTMX and Alpine.js Integration

While the primary interaction of this specific ASP.NET page is a full form submission leading to a redirect, we leverage Alpine.js for enhanced user experience (loading state, client-side confirmation) and assume HTMX is available in `core/base.html` for any potential future dynamic interactions (though not directly used for this form's main submit action to preserve original redirect behavior).

**Instructions:**
- **Alpine.js for UI State and Confirmation:** The upload button now has an Alpine.js `x-data` attribute to manage a `uploading` state. When the form is submitted, `uploading` becomes true, disabling the button and showing a spinner. The `confirm` dialog (`OnClientClick=" return confirmationUpload()"`) is implemented directly with `x-data` and `@click.prevent` on the submit button.
- **HTMX Availability:** It's assumed that `htmx.org` and `alpinejs.dev` CDN links are included in the `core/base.html` template. No specific `hx-*` attributes are used on the form itself for its primary submission, aligning with the original ASP.NET's `Response.Redirect`. If this page were to be loaded as a modal or dynamically updated a list via HTMX, then the `hx-post` and `HX-Trigger` headers would be used.

### Final Notes

This comprehensive plan covers the migration of the ASP.NET `UploadDrw` functionality to a modern Django application.

- **Placeholders:** Remember to replace placeholder URLs (like `work_order_tree_view_edit`) with the actual Django URLs once the corresponding pages are migrated.
- **Session Data:** The migration assumes that `compid` and `username` session data are available in Django's session. In a full system, this would typically come from an authenticated user object (e.g., `request.user.profile.company_id`, `request.user.username`) managed by Django's authentication system.
- **Database Schema:** The `ItemMaster` model is configured with `managed = False` to ensure Django works with the existing database schema without attempting to modify it.
- **Business Logic Delegation:** All file handling and database update logic is contained within the `ItemMaster` model, upholding the "fat model, thin view" principle. The view focuses on request handling, data retrieval, and delegating business decisions.
- **User Experience:** The use of Tailwind CSS ensures a modern, responsive design, while Alpine.js provides immediate feedback during the upload process, enhancing the user experience beyond the legacy ASP.NET application.
- **Automated Process:** This breakdown can serve as a detailed blueprint for an AI-assisted automation tool. The tool would parse the ASP.NET code, identify the patterns, and generate these Django components, filling in the blanks based on inferred data and predefined templates. This minimizes manual coding and reduces human error.