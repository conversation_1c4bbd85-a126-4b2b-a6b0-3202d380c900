This Django modernization plan outlines the strategic transition from your legacy ASP.NET application to a robust, modern Django-based solution. Our approach prioritizes automation, emphasizes a "Fat Model, Thin View" architecture, and leverages HTMX for dynamic, efficient user interfaces, ensuring a seamless and future-proof migration.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**

Based on the ASP.NET code-behind, the following tables and their relevant columns for this module have been identified:

*   **`tblDG_Category_Master` (Category Information)**
    *   `CId` (Primary Key, Integer)
    *   `CName` (Category Name, Inferred, String) - Used for `DrpCategory`.
*   **`tblDG_SubCategory_Master` (Sub-Category Information)**
    *   `SCId` (Primary Key, Integer)
    *   `CId` (Foreign Key to `tblDG_Category_Master.CId`, Integer)
    *   `Symbol` (String)
    *   `SCName` (String) - Used for `DrpSubCategory` as `Symbol + ' - ' + SCName`.
*   **`tblDG_Item_Master` (Machine/Item Details)**
    *   `Id` (Primary Key, Integer)
    *   `ItemCode` (Machine Code, String)
    *   `ManfDesc` (Description, String)
    *   `StockQty` (Stock Quantity, Decimal)
    *   `Location` (Location, String)
    *   `UOMBasic` (Unit of Measure, String)
    *   `CId` (Foreign Key to `tblDG_Category_Master.CId`, Integer)
    *   `SCId` (Foreign Key to `tblDG_SubCategory_Master.SCId`, Integer)
    *   `CompId` (Company ID, Integer)
    *   `Absolute` (Boolean/String, `!='1'` implies '0' or `False`)
*   **`tblMS_Master` (Machine Maintenance Master)**
    *   `Id` (Primary Key, Integer) - Referred to as `MSId` in queries.
    *   `ItemId` (Foreign Key to `tblDG_Item_Master.Id`, Integer)
    *   `SysDate` (System Date / Last PM/BM Date, Date)
*   **`tblMS_PMBM_Master` (Preventive/Breakdown Maintenance Records)**
    *   `Id` (Primary Key, Integer)
    *   `MachineId` (Foreign Key to `tblMS_Master.Id`, Integer)
    *   `SysDate` (PM/BM Date, Date)
    *   `PMBM` (Type, Integer, `0` for Preventive, `1` for Breakdown)
    *   `NameOfAgency` (Name of Agency, String)
    *   `NameOfEngineer` (Name of Engineer, String)
    *   `CompId` (Company ID, Integer)
    *   `FinYearId` (Financial Year ID, Integer)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**

Analysis of the provided ASP.NET code indicates that this specific page (`PMBM_Edit.aspx`) primarily focuses on **Read** operations, specifically:

*   **Reading (Listing & Filtering):**
    *   Displaying a list of machines (`GridView2`) based on various filter criteria (Category, SubCategory, Search Code, Search Text).
    *   Displaying a list of Preventive/Breakdown Maintenance records (`GridView1`) for a selected machine from `GridView2`.
    *   Populating dropdowns (`DrpCategory`, `DrpSubCategory`) with initial and dependent data.

*   **Navigation to Detail/Edit:**
    *   Clicking a "Select" link in `GridView1` (PM/BM Records) redirects to `PMBM_Edit_Details.aspx`, indicating that actual *editing* of individual PM/BM records occurs on a separate page.

There are no direct "Create", "Update", or "Delete" operations for the primary entities on this page. The focus is entirely on data retrieval and presentation.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

The following UI components and their roles have been identified:

*   **Dropdowns for Filtering:**
    *   `DrpCategory` (ASP.NET `DropDownList`): Selects the machine category. Triggers `AutoPostBack` and `DrpCategory_SelectedIndexChanged` to update subcategories and machine list.
    *   `DrpSubCategory` (ASP.NET `DropDownList`): Selects the machine sub-category. Triggers `AutoPostBack` and `DrpSubCategory_SelectedIndexChanged` to update the machine list.
    *   `DrpSearchCode` (ASP.NET `DropDownList`): Defines the search field (`Machine Code` or `Description`). Triggers `AutoPostBack` and `DrpSearchCode_SelectedIndexChanged`.
*   **Search Input:**
    *   `txtSearchItemCode` (ASP.NET `TextBox`): Input field for search text.
    *   `btnSearch` (ASP.NET `Button`): Triggers the search functionality to filter the machine list.
*   **Machine List (`GridView2`):**
    *   Displays `SN` (serial number), `Machine Code` (`ItemCode`), `Description` (`ManfDesc`), and `Last PM Date` (`LastPMBMDate`).
    *   `ItemCode` is a `LinkButton` with `CommandName="Sel"`, which selects a machine and triggers the display of its maintenance records in `GridView1`.
    *   Supports pagination (`AllowPaging`, `OnPageIndexChanging`).
*   **PM/BM Records List (`GridView1`):**
    *   Wrapped in an `UpdatePanel` (`Up`) for partial page updates.
    *   Displays `SN`, `Select` (LinkButton), `PM Date`, `Type`, `Name of Agency`, `Name of Engineer`.
    *   `Select` is a `LinkButton` with `CommandName="Sel"`, which redirects to `PMBM_Edit_Details.aspx` for viewing/editing specific PM/BM records.
    *   Supports pagination (`AllowPaging`, `OnPageIndexChanging`).
*   **JavaScript:** The page includes `loadingNotifier.js`, `PopUpMsg.js`, and `styles.css`, `StyleSheet.css`, `yui-datatable.css`. These will be replaced by HTMX, Alpine.js, Tailwind CSS, and standard DataTables.

### Step 4: Generate Django Code

We will create a new Django application named `maintenance` for this module.

#### 4.1 Models (`maintenance/models.py`)

This file will define the Django models corresponding to the identified database tables, using `managed = False` to connect to the existing schema and implementing a "Fat Model" approach by including query logic in custom managers.

```python
from django.db import models
from django.db.models import F, OuterRef, Subquery, Max

# Custom Managers for Fat Models
class ItemManager(models.Manager):
    def get_items_with_last_maintenance(self, comp_id, category_id=None, subcategory_id=None, search_type=None, search_text=None):
        # Subquery to get the latest sys_date and corresponding MSId from tblMS_Master for each Item
        # This simulates the join and selection of 'LastPMBMDate' and 'MSId' from tblMS_Master
        latest_maintenance_subquery = MachineMaintenanceMaster.objects.filter(
            item=OuterRef('pk')
        ).order_by('-sys_date').values('sys_date', 'id')[:1]

        queryset = self.get_queryset().annotate(
            last_pmbm_date=Subquery(latest_maintenance_subquery.values('sys_date')),
            ms_id=Subquery(latest_maintenance_subquery.values('id'))
        ).filter(
            comp_id=comp_id,
            absolute='0' # Assuming 'Absolute' != '1' means '0'
        )

        # Apply filters based on search criteria
        if category_id and category_id != "Select Category": # Assuming "Select Category" is the default unselected value
            queryset = queryset.filter(category__cid=category_id)
            if subcategory_id and subcategory_id != "Select SubCategory": # Assuming "Select SubCategory" is the default
                queryset = queryset.filter(sub_category__scid=subcategory_id)
        
        if search_text and search_text.strip():
            # ASP.NET used 'Like s%' for ItemCode and '%s%' for ManfDesc
            if search_type == "tblDG_Item_Master.ItemCode":
                queryset = queryset.filter(item_code__istartswith=search_text)
            elif search_type == "tblDG_Item_Master.ManfDesc":
                queryset = queryset.filter(manf_desc__icontains=search_text)
        
        # Order by Id Desc as in ASP.NET
        queryset = queryset.order_by('-id')

        return queryset

class PMBMRecordManager(models.Manager):
    def get_records_for_machine_master(self, machine_master_id, comp_id, fin_year_id):
        # This simulates the PMBMGrid function logic
        return self.get_queryset().filter(
            machine_master__id=machine_master_id,
            comp_id=comp_id,
            fin_year_id__lte=fin_year_id # finYearId <=
        ).order_by('sys_date') # Ordering by date is a reasonable default

# Models Definition
class Category(models.Model):
    cid = models.IntegerField(db_column='CId', primary_key=True)
    cname = models.CharField(db_column='CName', max_length=255) # Inferred column name

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'

    def __str__(self):
        return self.cname

class SubCategory(models.Model):
    scid = models.IntegerField(db_column='SCId', primary_key=True)
    cid = models.ForeignKey(Category, on_delete=models.DO_NOTHING, db_column='CId', related_name='subcategories_rel')
    symbol = models.CharField(db_column='Symbol', max_length=50)
    scname = models.CharField(db_column='SCName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblDG_SubCategory_Master'
        verbose_name = 'Sub-Category'
        verbose_name_plural = 'Sub-Categories'

    def __str__(self):
        return f"{self.symbol} - {self.scname}"

class Item(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=100)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255)
    stock_qty = models.DecimalField(db_column='StockQty', max_digits=10, decimal_places=2, null=True, blank=True)
    location = models.CharField(db_column='Location', max_length=255, null=True, blank=True)
    uom_basic = models.CharField(db_column='UOMBasic', max_length=50, null=True, blank=True)
    category = models.ForeignKey(Category, on_delete=models.DO_NOTHING, db_column='CId', related_name='items_rel')
    sub_category = models.ForeignKey(SubCategory, on_delete=models.DO_NOTHING, db_column='SCId', related_name='items_rel')
    comp_id = models.IntegerField(db_column='CompId')
    absolute = models.CharField(db_column='Absolute', max_length=1) # Stored as '1' or '0'

    objects = ItemManager() # Attach custom manager

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return f"{self.item_code} - {self.manf_desc}"
    
    @property
    def manf_desc_truncated(self):
        # Replicates SUBSTRING(tblDG_Item_Master.ManfDesc,0,80)+'...'
        if self.manf_desc and len(self.manf_desc) > 77: # 80 chars total, so up to 77 + '...'
            return self.manf_desc[:77] + '...'
        return self.manf_desc

class MachineMaintenanceMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Corresponds to MSId in original code
    item = models.ForeignKey(Item, on_delete=models.DO_NOTHING, db_column='ItemId', related_name='maintenance_masters')
    sys_date = models.DateField(db_column='SysDate') # Corresponds to LastPMBMDate

    class Meta:
        managed = False
        db_table = 'tblMS_Master'
        verbose_name = 'Machine Maintenance Master'
        verbose_name_plural = 'Machine Maintenance Masters'

    def __str__(self):
        return f"Master for {self.item.item_code} - {self.sys_date}"

class PMBMRecord(models.Model):
    PMBM_CHOICES = (
        (0, 'Preventive'),
        (1, 'Breakdown'),
    )

    id = models.IntegerField(db_column='Id', primary_key=True)
    machine_master = models.ForeignKey(MachineMaintenanceMaster, on_delete=models.DO_NOTHING, db_column='MachineId', related_name='pmbm_records')
    sys_date = models.DateField(db_column='SysDate') # Corresponds to PMBMDate
    pmbm = models.IntegerField(db_column='PMBM', choices=PMBM_CHOICES)
    name_of_agency = models.CharField(db_column='NameOfAgency', max_length=255, null=True, blank=True)
    name_of_engineer = models.CharField(db_column='NameOfEngineer', max_length=255, null=True, blank=True)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    objects = PMBMRecordManager() # Attach custom manager

    class Meta:
        managed = False
        db_table = 'tblMS_PMBM_Master'
        verbose_name = 'PM/BM Record'
        verbose_name_plural = 'PM/BM Records'

    def __str__(self):
        return f"{self.get_pmbm_display()} for {self.machine_master.item.item_code} on {self.sys_date}"
    
    @property
    def pmbm_type_display(self):
        # Replicates the "Preventive" / "Breakdown" logic
        return self.get_pmbm_display()
```

#### 4.2 Forms (`maintenance/forms.py`)

This file will contain a form for the search/filter criteria. Since there are no direct CRUD operations on this page for the primary entities, we only need a search form.

```python
from django import forms
from .models import Category, SubCategory

class MachineMaintenanceSearchForm(forms.Form):
    # Category dropdown, populated dynamically
    category = forms.ChoiceField(
        choices=[('', 'Select Category')],
        required=False,
        widget=forms.Select(attrs={
            'class': 'box3 w-full',
            'hx-get': '/maintenance/subcategories/', # HTMX endpoint for subcategories
            'hx-target': '#id_subcategory',
            'hx-swap': 'outerHTML',
            'hx-trigger': 'change',
            'hx-include': '#id_category',
            'name': 'category_id', # Name for GET parameter
        })
    )

    # SubCategory dropdown, populated dynamically based on category
    subcategory = forms.ChoiceField(
        choices=[('', 'Select SubCategory')],
        required=False,
        widget=forms.Select(attrs={
            'class': 'box3 w-full',
            'name': 'subcategory_id', # Name for GET parameter
        })
    )

    # Search Code dropdown
    search_code = forms.ChoiceField(
        choices=[
            ('Select', 'Select'),
            ('tblDG_Item_Master.ItemCode', 'Machine Code'),
            ('tblDG_Item_Master.ManfDesc', 'Description'),
        ],
        required=False,
        widget=forms.Select(attrs={
            'class': 'box3 w-full',
            'name': 'search_type', # Name for GET parameter
        })
    )

    # Search Textbox
    search_text = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full',
            'placeholder': 'Enter search text...',
            'name': 'search_text', # Name for GET parameter
        })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate Category dropdown
        categories = Category.objects.all().order_by('cname')
        self.fields['category'].choices = [('', 'Select Category')] + [(c.cid, c.cname) for c in categories]

        # If a category is pre-selected (e.g., on initial load or form re-submission),
        # populate subcategories accordingly.
        if 'category_id' in self.initial and self.initial['category_id']:
            subcategories = SubCategory.objects.filter(cid=self.initial['category_id']).order_by('scname')
            self.fields['subcategory'].choices = [('', 'Select SubCategory')] + [(sc.scid, sc.symbol + ' - ' + sc.scname) for sc in subcategories]
        else:
            self.fields['subcategory'].choices = [('', 'Select SubCategory')] # Default empty
```

#### 4.3 Views (`maintenance/views.py`)

This file will contain the main view for the PM/BM Edit page and additional HTMX partial views for dynamically loading table content and dropdown options. Views will be thin, delegating complex logic to models.

```python
from django.views.generic import TemplateView, View
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from django.template.loader import render_to_string
from .models import Item, PMBMRecord, Category, SubCategory, MachineMaintenanceMaster
from .forms import MachineMaintenanceSearchForm
from django.conf import settings # For accessing SESSION variables like COMP_ID, FIN_YEAR_ID

class MachineMaintenanceListView(TemplateView):
    """
    Main view for the PM/BM Edit page. Handles initial display and search form.
    Machine table and PM/BM records table are loaded via HTMX.
    """
    template_name = 'maintenance/pmbm_edit_list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the search form with any GET parameters
        # This will be used to pre-populate dropdowns/search text on page load/refresh
        context['form'] = MachineMaintenanceSearchForm(self.request.GET)
        context['selected_ms_id'] = self.request.GET.get('ms_id', None) # To highlight selected row in machine list
        return context

# HTMX partial view for the Machine Table (GridView2 equivalent)
class MachineTablePartialView(View):
    def get(self, request, *args, **kwargs):
        # Get session variables, mimicking ASP.NET Session["compid"] etc.
        # In a real app, these would come from authentication or user profile.
        # For demonstration, we use placeholder values or assume settings.
        comp_id = request.session.get('company_id', 1) # Default to 1 if not in session
        
        # Get filter parameters from GET request
        category_id = request.GET.get('category_id')
        subcategory_id = request.GET.get('subcategory_id')
        search_type = request.GET.get('search_type')
        search_text = request.GET.get('search_text')
        selected_ms_id = request.GET.get('ms_id', None) # Passed from main view or PMBM selection

        # Use the ItemManager to get filtered and annotated items
        machines = Item.objects.get_items_with_last_maintenance(
            comp_id=comp_id,
            category_id=category_id,
            subcategory_id=subcategory_id,
            search_type=search_type,
            search_text=search_text
        )

        context = {
            'machines': machines,
            'selected_ms_id': selected_ms_id, # Pass the selected MSId to highlight the row
        }
        return render(request, 'maintenance/_machine_table.html', context)

# HTMX partial view for PM/BM Records Table (GridView1 equivalent)
class PMBMRecordsPartialView(View):
    def get(self, request, *args, **kwargs):
        machine_master_id = kwargs.get('ms_id') # MSId is passed as a URL parameter

        # Get session variables
        comp_id = request.session.get('company_id', 1) # Default to 1 if not in session
        fin_year_id = request.session.get('financial_year_id', 2024) # Default to 2024 if not in session

        pmbm_records = PMBMRecord.objects.get_records_for_machine_master(
            machine_master_id=machine_master_id,
            comp_id=comp_id,
            fin_year_id=fin_year_id
        )

        context = {
            'pmbm_records': pmbm_records,
            'machine_master_id': machine_master_id, # Pass it to the template if needed
        }
        return render(request, 'maintenance/_pmbm_records_table.html', context)

# HTMX endpoint for dynamic subcategory dropdown population
class SubCategoryOptionsView(View):
    def get(self, request, *args, **kwargs):
        category_id = request.GET.get('category_id')
        
        # Initial choices including the "Select SubCategory" option
        subcategories = [('', 'Select SubCategory')]
        if category_id and category_id != 'Select Category': # Ensure category_id is valid
            try:
                subcategories_qs = SubCategory.objects.filter(cid=category_id).order_by('scname')
                subcategories.extend([(sc.scid, f"{sc.symbol} - {sc.scname}") for sc in subcategories_qs])
            except ValueError:
                # Handle invalid category_id if necessary
                pass

        # Render just the select tag for the subcategory dropdown
        context = {'subcategories': subcategories, 'selected_subcategory_id': request.GET.get('subcategory_id')}
        return render(request, 'maintenance/_subcategory_options.html', context)

# Placeholder for the detail view (PMBM_Edit_Details.aspx equivalent)
# This would be a separate view, likely in a different app or a dedicated detail view.
class PMBMDetailView(View):
    def get(self, request, pmbm_id):
        # Redirect to actual detail page URL or render a simple response
        return HttpResponse(f"Redirecting to PM/BM Record Details for ID: {pmbm_id}")

```

#### 4.4 Templates (`maintenance/templates/maintenance/`)

We will create three template files: `pmbm_edit_list.html` (main page), `_machine_table.html` (partial for the machine list), `_pmbm_records_table.html` (partial for the PM/BM records list), and `_subcategory_options.html` (partial for dynamically updating subcategory dropdown).

**`pmbm_edit_list.html` (Main Page Template)**

```html
{% extends 'core/base.html' %}

{% block title %}Preventive / Breakdown Maintenance - Edit{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-blue-600 text-white p-3 mb-6 rounded-t-lg">
        <h2 class="text-xl font-bold">Preventive / Breakdown Maintenance - Edit</h2>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <form id="searchForm" hx-get="{% url 'maintenance:machine_table' %}" hx-target="#machine-table-container" hx-swap="innerHTML" hx-trigger="submit, change from:select[name^='category']">
            {% csrf_token %} {# CSRF token is not strictly necessary for GET requests with HTMX, but good practice for forms #}
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                <div class="col-span-1">
                    <label for="{{ form.category.id_for_label }}" class="block text-sm font-medium text-gray-700">Category</label>
                    {{ form.category }}
                </div>
                <div class="col-span-1">
                    <label for="{{ form.subcategory.id_for_label }}" class="block text-sm font-medium text-gray-700">SubCategory</label>
                    {# The subcategory select will be replaced by HTMX #}
                    <div id="id_subcategory" hx-swap="outerHTML">
                        {{ form.subcategory }}
                    </div>
                </div>
                <div class="col-span-1">
                    <label for="{{ form.search_code.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By</label>
                    {{ form.search_code }}
                </div>
                <div class="col-span-1">
                    <label for="{{ form.search_text.id_for_label }}" class="block text-sm font-medium text-gray-700">Search Text</label>
                    {{ form.search_text }}
                </div>
                <div class="col-span-4 flex justify-end">
                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Search
                    </button>
                </div>
            </div>
        </form>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Machine List Table Container -->
        <div class="bg-white shadow-md rounded-lg p-6">
            <h3 class="text-xl font-semibold text-gray-800 mb-4">Machine List</h3>
            <div id="machine-table-container"
                 hx-get="{% url 'maintenance:machine_table' %}?{{ request.GET.urlencode }}" {# Initial load of machines with current GET params #}
                 hx-trigger="load, reloadMachineTable from:body, click from:#searchForm button[type='submit']"
                 hx-swap="innerHTML">
                <!-- Loading indicator -->
                <div class="text-center py-10">
                    <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                    <p class="mt-4 text-gray-600">Loading Machines...</p>
                </div>
            </div>
        </div>

        <!-- PM/BM Records Table Container -->
        <div class="bg-white shadow-md rounded-lg p-6">
            <h3 class="text-xl font-semibold text-gray-800 mb-4">PM/BM Records</h3>
            <div id="pmbm-records-container" hx-swap="innerHTML">
                <!-- PM/BM records will be loaded here dynamically by HTMX -->
                <p class="text-gray-500 text-center py-10">Select a machine to view its PM/BM records.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.tailwindcss.min.js"></script>
<script>
    // Alpine.js component initialization if needed for more complex UI states
    document.addEventListener('alpine:init', () => {
        Alpine.data('pmbmApp', () => ({
            // Example of Alpine state if needed
            selectedMachineId: null,
            init() {
                // Initialize any state based on URL params or initial data
            }
        }));
    });

    // Event listener for HTMX afterSwap to initialize DataTables
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'machine-table-container' || evt.detail.target.id === 'pmbm-records-container') {
            // Check if the table element exists within the swapped content
            const tableElement = evt.detail.target.querySelector('table');
            if (tableElement && !$.fn.DataTable.isDataTable(tableElement)) {
                $(tableElement).DataTable({
                    "pageLength": 20, // ASP.NET GridView PageSize
                    "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                    "destroy": true, // Allow reinitialization
                    "searching": true,
                    "ordering": true,
                    "paging": true,
                    "info": true
                });
            }
        }
    });

    // Listen for the initial load of the machine table to initialize DataTables
    document.addEventListener('DOMContentLoaded', () => {
        const initialMachineTable = document.getElementById('machineTable'); // Assuming ID within the partial
        if (initialMachineTable && !$.fn.DataTable.isDataTable(initialMachineTable)) {
            $(initialMachineTable).DataTable({
                "pageLength": 20,
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "destroy": true,
                "searching": true,
                "ordering": true,
                "paging": true,
                "info": true
            });
        }
    });

</script>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.7/css/dataTables.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.7/css/dataTables.tailwindcss.min.css">
{% endblock %}
```

**`_machine_table.html` (Partial for Machine List)**

```html
<table id="machineTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Machine Code</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last PM Date</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for machine in machines %}
        <tr class="{% if machine.ms_id == selected_ms_id %}bg-blue-50{% endif %}">
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm">
                <a href="#"
                   class="text-blue-600 hover:text-blue-900"
                   hx-get="{% url 'maintenance:pmbm_records_table' ms_id=machine.ms_id %}"
                   hx-target="#pmbm-records-container"
                   hx-swap="innerHTML"
                   hx-indicator="#pmbm-records-container"
                   hx-push-url="?ms_id={{ machine.ms_id }}" {# Update URL for deep linking/browser history #}
                   _="on click add .bg-blue-50 to closest <tr/> then remove .bg-blue-50 from previous .bg-blue-50"
                   >
                    {{ machine.item_code }}
                </a>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ machine.manf_desc_truncated }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                {% if machine.last_pmbm_date %}
                    {{ machine.last_pmbm_date|date:"d/m/Y" }} {# Format as DD/MM/YYYY #}
                {% else %}
                    N/A
                {% endif %}
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="4" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

{# DataTables initialization happens in the main template's htmx:afterSwap listener #}
```

**`_pmbm_records_table.html` (Partial for PM/BM Records List)**

```html
<table id="pmbmRecordsTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PM Date</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Agency</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Engineer</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for record in pmbm_records %}
        <tr>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm">
                <a href="{% url 'maintenance:pmbm_detail' pmbm_id=record.id %}" class="text-blue-600 hover:text-blue-900">
                    Select
                </a>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                {{ record.sys_date|date:"d/m/Y" }} {# Format as DD/MM/YYYY #}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                {{ record.pmbm_type_display }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ record.name_of_agency }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ record.name_of_engineer }}
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

{# DataTables initialization happens in the main template's htmx:afterSwap listener #}
```

**`_subcategory_options.html` (Partial for Subcategory Dropdown)**

```html
<select name="subcategory_id" id="id_subcategory" class="box3 w-full">
    {% for value, label in subcategories %}
        <option value="{{ value }}" {% if value == selected_subcategory_id|floatformat:0 %}selected{% endif %}>{{ label }}</option>
    {% endfor %}
</select>
```

#### 4.5 URLs (`maintenance/urls.py`)

This file defines the URL patterns for the `maintenance` application.

```python
from django.urls import path
from .views import (
    MachineMaintenanceListView, 
    MachineTablePartialView, 
    PMBMRecordsPartialView,
    SubCategoryOptionsView,
    PMBMDetailView, # Placeholder for redirect
)

app_name = 'maintenance' # Define app_name for namespacing URLs

urlpatterns = [
    # Main page for PM/BM Edit
    path('pmbm-edit/', MachineMaintenanceListView.as_view(), name='pmbm_edit_list'),
    
    # HTMX endpoints for partial table updates
    path('pmbm-edit/machines/', MachineTablePartialView.as_view(), name='machine_table'),
    path('pmbm-edit/records/<int:ms_id>/', PMBMRecordsPartialView.as_view(), name='pmbm_records_table'),
    
    # HTMX endpoint for populating subcategory dropdown
    path('subcategories/', SubCategoryOptionsView.as_view(), name='subcategories_options'),

    # Placeholder for the detail view (redirect target)
    path('pmbm-details/<int:pmbm_id>/', PMBMDetailView.as_view(), name='pmbm_detail'),
]
```

To integrate these URLs into your Django project, you would include them in your project's `urls.py`:
`path('maintenance/', include('maintenance.urls')),`

#### 4.6 Tests (`maintenance/tests.py`)

Comprehensive tests for models (unit tests) and views (integration tests) are crucial for ensuring the migrated application functions correctly and is maintainable.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import Category, SubCategory, Item, MachineMaintenanceMaster, PMBMRecord
from datetime import date

class MaintenanceModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for models
        cls.company_id = 1
        cls.financial_year_id = 2024

        cls.category1 = Category.objects.create(cid=1, cname='Electronics')
        cls.category2 = Category.objects.create(cid=2, cname='Heavy Machinery')

        cls.subcategory1_1 = SubCategory.objects.create(scid=101, cid=cls.category1, symbol='EL', scname='Computers')
        cls.subcategory1_2 = SubCategory.objects.create(scid=102, cid=cls.category1, symbol='EL', scname='Printers')
        cls.subcategory2_1 = SubCategory.objects.create(scid=201, cid=cls.category2, symbol='HM', scname='Excavators')

        cls.item1 = Item.objects.create(
            id=1, item_code='MC001', manf_desc='HP LaserJet Printer 400', stock_qty=5.0, location='Warehouse A',
            uom_basic='Pcs', category=cls.category1, sub_category=cls.subcategory1_2, comp_id=cls.company_id, absolute='0'
        )
        cls.item2 = Item.objects.create(
            id=2, item_code='MC002', manf_desc='Caterpillar Excavator 320', stock_qty=1.0, location='Yard B',
            uom_basic='Unit', category=cls.category2, sub_category=cls.subcategory2_1, comp_id=cls.company_id, absolute='0'
        )
        cls.item3 = Item.objects.create(
            id=3, item_code='MC003', manf_desc='Dell XPS Desktop', stock_qty=10.0, location='Office',
            uom_basic='Pcs', category=cls.category1, sub_category=cls.subcategory1_1, comp_id=cls.company_id, absolute='0'
        )
        cls.item_absolute = Item.objects.create( # Should be excluded by query
            id=4, item_code='MC004', manf_desc='Old Generator', stock_qty=1.0, location='Scrap',
            uom_basic='Unit', category=cls.category2, sub_category=cls.subcategory2_1, comp_id=cls.company_id, absolute='1'
        )

        cls.master1 = MachineMaintenanceMaster.objects.create(id=1, item=cls.item1, sys_date=date(2023, 10, 15))
        cls.master2 = MachineMaintenanceMaster.objects.create(id=2, item=cls.item2, sys_date=date(2023, 11, 20))
        cls.master3 = MachineMaintenanceMaster.objects.create(id=3, item=cls.item3, sys_date=date(2024, 1, 5))

        PMBMRecord.objects.create(id=101, machine_master=cls.master1, sys_date=date(2023, 10, 15), pmbm=0,
                                 name_of_agency='Tech Repair Inc.', name_of_engineer='John Doe',
                                 comp_id=cls.company_id, fin_year_id=cls.financial_year_id)
        PMBMRecord.objects.create(id=102, machine_master=cls.master1, sys_date=date(2023, 7, 10), pmbm=1,
                                 name_of_agency='Quick Fix Co.', name_of_engineer='Jane Smith',
                                 comp_id=cls.company_id, fin_year_id=cls.financial_year_id) # Older fin year
        PMBMRecord.objects.create(id=103, machine_master=cls.master2, sys_date=date(2023, 11, 20), pmbm=0,
                                 name_of_agency='Heavy Duty Svc.', name_of_engineer='Bob Johnson',
                                 comp_id=cls.company_id, fin_year_id=cls.financial_year_id)
        PMBMRecord.objects.create(id=104, machine_master=cls.master3, sys_date=date(2024, 1, 5), pmbm=0,
                                 name_of_agency='IT Solutions', name_of_engineer='Alice Brown',
                                 comp_id=cls.company_id, fin_year_id=cls.financial_year_id + 1) # Newer fin year

    def test_item_creation(self):
        self.assertEqual(Item.objects.count(), 4)
        self.assertEqual(self.item1.item_code, 'MC001')
        self.assertEqual(self.item1.category.cname, 'Electronics')
        self.assertEqual(self.item1.manf_desc_truncated, 'HP LaserJet Printer 400')

    def test_item_truncated_manf_desc(self):
        long_desc_item = Item.objects.create(
            id=5, item_code='MC005', manf_desc='This is a very very long description for a machine that should be truncated to fit the display area.', stock_qty=1.0, location='Test',
            uom_basic='Unit', category=self.category1, sub_category=self.subcategory1_1, comp_id=self.company_id, absolute='0'
        )
        self.assertEqual(len(long_desc_item.manf_desc_truncated), 80)
        self.assertTrue(long_desc_item.manf_desc_truncated.endswith('...'))

    def test_pmbm_record_type_display(self):
        record_preventive = PMBMRecord.objects.get(id=101)
        record_breakdown = PMBMRecord.objects.get(id=102)
        self.assertEqual(record_preventive.pmbm_type_display, 'Preventive')
        self.assertEqual(record_breakdown.pmbm_type_display, 'Breakdown')

    def test_get_items_with_last_maintenance_manager(self):
        # Test basic query
        items = Item.objects.get_items_with_last_maintenance(comp_id=self.company_id)
        self.assertEqual(items.count(), 3) # Excludes item_absolute
        self.assertEqual(items.first().item_code, 'MC003') # Ordered by -id

        # Test category filter
        items_cat1 = Item.objects.get_items_with_last_maintenance(comp_id=self.company_id, category_id=self.category1.cid)
        self.assertEqual(items_cat1.count(), 2)
        self.assertIn(self.item1, items_cat1)
        self.assertIn(self.item3, items_cat1)

        # Test subcategory filter
        items_subcat1_2 = Item.objects.get_items_with_last_maintenance(comp_id=self.company_id, category_id=self.category1.cid, subcategory_id=self.subcategory1_2.scid)
        self.assertEqual(items_subcat1_2.count(), 1)
        self.assertIn(self.item1, items_subcat1_2)

        # Test item code search
        items_search_code = Item.objects.get_items_with_last_maintenance(comp_id=self.company_id, search_type='tblDG_Item_Master.ItemCode', search_text='MC001')
        self.assertEqual(items_search_code.count(), 1)
        self.assertIn(self.item1, items_search_code)

        # Test description search
        items_search_desc = Item.objects.get_items_with_last_maintenance(comp_id=self.company_id, search_type='tblDG_Item_Master.ManfDesc', search_text='Printer')
        self.assertEqual(items_search_desc.count(), 1)
        self.assertIn(self.item1, items_search_desc)

        # Test annotations (last_pmbm_date and ms_id)
        retrieved_item1 = items.get(id=self.item1.id)
        self.assertEqual(retrieved_item1.last_pmbm_date, self.master1.sys_date)
        self.assertEqual(retrieved_item1.ms_id, self.master1.id)

    def test_get_records_for_machine_master_manager(self):
        # Test basic retrieval for master1
        records_master1 = PMBMRecord.objects.get_records_for_machine_master(
            machine_master_id=self.master1.id,
            comp_id=self.company_id,
            fin_year_id=self.financial_year_id
        )
        self.assertEqual(records_master1.count(), 2) # Should include both 101 and 102 as 2023 <= 2024, 2024 <= 2024
        self.assertIn(PMBMRecord.objects.get(id=101), records_master1)
        self.assertIn(PMBMRecord.objects.get(id=102), records_master1)

        # Test fin_year_id filter
        records_master3 = PMBMRecord.objects.get_records_for_machine_master(
            machine_master_id=self.master3.id,
            comp_id=self.company_id,
            fin_year_id=self.financial_year_id
        )
        self.assertEqual(records_master3.count(), 0) # Record 104 has fin_year_id=2025 > 2024

class MaintenanceViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Set up data for all tests
        cls.company_id = 1
        cls.financial_year_id = 2024

        cls.category1 = Category.objects.create(cid=1, cname='Electronics')
        cls.subcategory1_1 = SubCategory.objects.create(scid=101, cid=cls.category1, symbol='EL', scname='Computers')
        cls.item1 = Item.objects.create(
            id=1, item_code='TEST001', manf_desc='Test Machine 1', stock_qty=1.0, location='A',
            uom_basic='Unit', category=cls.category1, sub_category=cls.subcategory1_1, comp_id=cls.company_id, absolute='0'
        )
        cls.master1 = MachineMaintenanceMaster.objects.create(id=1, item=cls.item1, sys_date=date(2023, 1, 1))
        cls.pmbm_record1 = PMBMRecord.objects.create(id=101, machine_master=cls.master1, sys_date=date(2023, 1, 15), pmbm=0,
                                                    name_of_agency='Agency X', name_of_engineer='Engineer Y',
                                                    comp_id=cls.company_id, fin_year_id=cls.financial_year_id)

    def setUp(self):
        self.client = Client()
        # Mock session variables for company_id and financial_year_id
        session = self.client.session
        session['company_id'] = self.company_id
        session['financial_year_id'] = self.financial_year_id
        session.save()

    def test_machine_maintenance_list_view(self):
        url = reverse('maintenance:pmbm_edit_list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'maintenance/pmbm_edit_list.html')
        self.assertContains(response, 'Preventive / Breakdown Maintenance - Edit')
        self.assertIsInstance(response.context['form'], MachineMaintenanceSearchForm)

    def test_machine_table_partial_view_initial_load(self):
        url = reverse('maintenance:machine_table')
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'maintenance/_machine_table.html')
        self.assertContains(response, self.item1.item_code)
        self.assertContains(response, self.item1.manf_desc)
        self.assertContains(response, self.master1.sys_date.strftime("%d/%m/%Y"))

    def test_machine_table_partial_view_with_filters(self):
        url = reverse('maintenance:machine_table')
        # Simulate a search with category and item code
        params = {
            'category_id': self.category1.cid,
            'search_type': 'tblDG_Item_Master.ItemCode',
            'search_text': 'TEST001'
        }
        response = self.client.get(url, params, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'maintenance/_machine_table.html')
        self.assertContains(response, 'TEST001') # Should contain the filtered item
        
        # Test a filter that should yield no results
        params_no_results = {
            'search_type': 'tblDG_Item_Master.ItemCode',
            'search_text': 'NONEXISTENT'
        }
        response_no_results = self.client.get(url, params_no_results, HTTP_HX_REQUEST='true')
        self.assertEqual(response_no_results.status_code, 200)
        self.assertTemplateUsed(response_no_results, 'maintenance/_machine_table.html')
        self.assertContains(response_no_results, 'No data to display !')

    def test_pmbm_records_partial_view(self):
        url = reverse('maintenance:pmbm_records_table', args=[self.master1.id])
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'maintenance/_pmbm_records_table.html')
        self.assertContains(response, self.pmbm_record1.sys_date.strftime("%d/%m/%Y"))
        self.assertContains(response, self.pmbm_record1.pmbm_type_display)
        self.assertContains(response, self.pmbm_record1.name_of_agency)

    def test_subcategory_options_view(self):
        url = reverse('maintenance:subcategories_options')
        response = self.client.get(url, {'category_id': self.category1.cid}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'maintenance/_subcategory_options.html')
        self.assertContains(response, '<option value="">Select SubCategory</option>')
        self.assertContains(response, f'<option value="{self.subcategory1_1.scid}">{self.subcategory1_1.symbol} - {self.subcategory1_1.scname}</option>')

        # Test with no category selected
        response_no_cat = self.client.get(url, {'category_id': ''}, HTTP_HX_REQUEST='true')
        self.assertEqual(response_no_cat.status_code, 200)
        self.assertContains(response_no_cat, '<option value="">Select SubCategory</option>')
        self.assertNotContains(response_no_cat, f'<option value="{self.subcategory1_1.scid}">')

    def test_pmbm_detail_view_redirect(self):
        url = reverse('maintenance:pmbm_detail', args=[self.pmbm_record1.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, f"Redirecting to PM/BM Record Details for ID: {self.pmbm_record1.id}")

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
    
The modernized solution heavily relies on HTMX and Alpine.js to provide a dynamic and responsive user experience, replacing ASP.NET's `AutoPostBack` and `UpdatePanel` mechanisms.

*   **HTMX for Dynamic Content Loading:**
    *   **Search and Filter:** The `searchForm` (containing Category, SubCategory, Search Code, Search Text, and Search Button) uses `hx-get="{% url 'maintenance:machine_table' %}"`, `hx-target="#machine-table-container"`, and `hx-swap="innerHTML"`.
        *   When any dropdown value changes (`hx-trigger="change from:select[name^='category']"`) or the Search button is clicked (`hx-trigger="submit"`), an HTMX GET request is sent with the current form data (implicitly included in GET requests).
        *   The `machine_table` endpoint (`MachineTablePartialView`) returns the `_machine_table.html` partial, which entirely replaces the content of `#machine-table-container`.
    *   **Dependent Dropdowns (Subcategory):** The `DrpCategory` (`category` field in the form) has `hx-get="/maintenance/subcategories/"`, `hx-target="#id_subcategory"`, and `hx-swap="outerHTML"`. When the category changes, an HTMX request fetches the new `<select>` element for subcategories, replacing the old one.
    *   **Selecting a Machine to View Records:** The "Machine Code" link in `_machine_table.html` uses `hx-get="{% url 'maintenance:pmbm_records_table' ms_id=machine.ms_id %}"`, `hx-target="#pmbm-records-container"`, and `hx-swap="innerHTML"`. Clicking a machine's code dynamically loads its associated PM/BM records into the right-hand table, without a full page reload.
    *   **URL Management:** `hx-push-url="?ms_id={{ machine.ms_id }}"` on the machine selection link updates the browser's URL, allowing for deep linking and proper browser history navigation (back/forward buttons).
    *   **Loading Indicators:** The main `pmbm_edit_list.html` includes a spinner in `#machine-table-container` that is visible while HTMX loads the content. HTMX's `hx-indicator` can be used for more specific loading indicators on individual elements.

*   **Alpine.js for UI State and Interactions:**
    *   While much of the dynamic behavior is handled by HTMX, Alpine.js is included in `base.html` and `pmbm_edit_list.html` for any finer-grained client-side UI states or interactions that HTMX alone cannot cover (e.g., managing complex modals, tabbed interfaces, or local component state). For this particular page, Alpine.js is primarily illustrative as HTMX + DataTables handle most requirements.
    *   The `_` attribute (hyperscript) on the machine selection link (`_="on click add .bg-blue-50 to closest <tr/> then remove .bg-blue-50 from previous .bg-blue-50"`) demonstrates a simple client-side interaction (highlighting the selected row) without requiring custom JavaScript files, enhancing immediate feedback.

*   **DataTables for List Views:**
    *   Both the machine table (`_machine_table.html`) and the PM/BM records table (`_pmbm_records_table.html`) are designed to be initialized with DataTables.
    *   The DataTables initialization (`$(tableElement).DataTable({...})`) is triggered by an HTMX `htmx:afterSwap` event listener in the main template. This ensures that whenever HTMX injects new table content, DataTables is correctly applied, providing client-side search, sorting, and pagination capabilities.
    *   This eliminates the need for server-side pagination logic within Django views for simple list displays, offloading that work to the client and reducing server load.

### Final Notes

*   **Placeholders:** Replace `company_id` and `financial_year_id` session values with actual authentication-derived values in your production Django application.
*   **DRY Principles:** Templates are designed to be partials (`_machine_table.html`, `_pmbm_records_table.html`) and extend a common `base.html`, promoting reusability and reducing code duplication.
*   **Fat Model, Thin View:** All complex query logic and data manipulation are encapsulated within Django model managers (`ItemManager`, `PMBMRecordManager`), keeping the views concise and focused on orchestrating data flow and rendering.
*   **Completeness:** The generated code provides a runnable foundation for the described functionality, including models, forms, views, templates, and tests, ready for integration into a Django project.
*   **Modularity:** The migration is structured into a dedicated `maintenance` application, promoting modularity and easier management of different functional areas.
*   **Business Value:** This modernization streamlines the application, improving responsiveness and user experience through HTMX, reducing server load with client-side DataTables, and enhancing maintainability with a clean, testable Django architecture. The transition to Python also opens doors for AI/ML integrations and leverages a thriving ecosystem.