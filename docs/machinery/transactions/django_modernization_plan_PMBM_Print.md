## ASP.NET to Django Conversion Script: Comprehensive Modernization Plan

This document outlines a strategic plan to migrate your existing ASP.NET application, specifically the Preventive/Breakdown Maintenance Print module, to a modern Django-based solution. Our approach prioritizes automation, leveraging conversational AI for systematic conversion, and focuses on delivering business value through a robust, maintainable, and scalable architecture.

### Business Value Proposition of Django Modernization

Transitioning to Django offers significant benefits:

1.  **Reduced Maintenance Costs:** Django's clean, organized structure and strong community support lead to fewer bugs and easier updates, minimizing long-term operational expenses.
2.  **Enhanced Performance & Scalability:** Django is designed for high-traffic applications, providing a solid foundation for future growth and ensuring your system can handle increased user loads without performance degradation.
3.  **Improved Developer Productivity:** With Django's "batteries included" philosophy, common tasks are streamlined, allowing your development team to build new features faster and more efficiently.
4.  **Modern User Experience:** By adopting HTMX and Alpine.js, we create highly interactive and responsive interfaces without the complexity of traditional JavaScript frameworks, improving user satisfaction and operational efficiency.
5.  **Future-Proof Technology:** Django is actively maintained and widely adopted, ensuring your application remains relevant and secure, ready to integrate with emerging technologies.
6.  **Cost-Effective Development:** Python's readability and Django's rapid development capabilities translate to faster project delivery and lower development costs compared to legacy systems.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code to accurately map them to Django models.

**Instructions:**
From the ASP.NET code-behind, we've identified the following tables and their relevant columns based on SQL queries and `GridView` bindings:

*   **`tblDG_Category_Master` (Category Information):**
    *   `CId` (Primary Key, Category ID)
    *   `CategoryName` (Name of the category)

*   **`tblDG_SubCategory_Master` (SubCategory Information):**
    *   `SCId` (Primary Key, SubCategory ID)
    *   `CId` (Foreign Key to `tblDG_Category_Master`, Category ID)
    *   `SCName` (Name of the subcategory)
    *   `Symbol` (Prefix or symbol for the subcategory)

*   **`tblDG_Item_Master` (Machine/Item Information):**
    *   `Id` (Primary Key, Item ID)
    *   `ItemCode` (Unique code for the machine)
    *   `ManfDesc` (Description of the machine/item)
    *   `StockQty` (Quantity in stock, used in query but not displayed)
    *   `Location` (Storage location, used in query but not displayed)
    *   `UOMBasic` (Unit of Measure, used in query but not displayed)
    *   `CId` (Foreign Key to `tblDG_Category_Master`, Category ID)
    *   `SCId` (Foreign Key to `tblDG_SubCategory_Master`, SubCategory ID)
    *   `CompId` (Company ID, used for filtering)
    *   `Absolute` (Flag indicating item status, `Absolute != '1'` for active items)

*   **`tblMS_Master` (Machine Last Maintenance Record):**
    *   `Id` (Primary Key, Machine Maintenance Master ID, referred to as `MSId`)
    *   `SysDate` (System Date, representing the `LastPMBMDate`)
    *   `ItemId` (Foreign Key to `tblDG_Item_Master.Id`, linking to the machine)

*   **`tblMS_PMBM_Master` (Preventive/Breakdown Maintenance Details):**
    *   `Id` (Primary Key, PMBM Detail ID)
    *   `MachineId` (Foreign Key to `tblMS_Master.Id`, linking to the machine's maintenance master record)
    *   `SysDate` (System Date, representing the `PMBMDate` for this specific maintenance)
    *   `PMBM` (Maintenance type: `0` for Preventive, `1` for Breakdown)
    *   `NameOfAgency` (Name of the agency performing maintenance)
    *   `NameOfEngineer` (Name of the engineer performing maintenance)
    *   `CompId` (Company ID, used for filtering)
    *   `FinYearId` (Financial Year ID, used for filtering)

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations and data flow within the ASP.NET application.

**Instructions:**
The primary functionality of this ASP.NET module is **Read** operations, specifically for listing and filtering machine records and their associated maintenance details. There are no direct Create, Update, or Delete (CRUD) operations on this page itself; instead, it provides a "print" or "view" interface and a redirect to a details page.

*   **Read (List & Filter Machines):**
    *   Initial page load: Displays a list of machines based on `CompId` and `Absolute` status, ordered by `Id Desc`. This corresponds to `GridView2`.
    *   Filtering by `Category`, `SubCategory`, `Search Code` (`ItemCode` or `ManfDesc`), and `Search Text`: These dropdowns and the search button dynamically re-filter `GridView2`.
    *   Data is fetched from `tblDG_Category_Master`, `tblDG_Item_Master`, and `tblMS_Master`.
*   **Read (List Maintenance Details):**
    *   Selecting a "Machine Code" from `GridView2` populates `GridView1` with specific preventive/breakdown maintenance details for that machine.
    *   Data is fetched from `tblMS_PMBM_Master` using `MachineId` (from `tblMS_Master`), `CompId`, and `FinYearId`.
*   **Navigation/Redirect:**
    *   Selecting a row in `GridView1` (maintenance detail) redirects the user to `PMBM_Print_Details.aspx` with relevant IDs in the URL.

### Step 3: Infer UI Components

**Task:** Analyze the ASP.NET controls and their interactive roles to design the modern Django frontend using HTMX and Alpine.js.

**Instructions:**
The ASP.NET UI consists of:

*   **Filtering Controls:**
    *   `DrpCategory` (DropDownList): For selecting a machine category. Triggers a postback on change.
    *   `DrpSubCategory` (DropDownList): For selecting a machine subcategory, dynamically populated based on category selection. Triggers a postback on change.
    *   `DrpSearchCode` (DropDownList): For selecting the search field (Machine Code or Description). Triggers a postback on change (but doesn't actually trigger `Fillgridview` directly, just affects `txtSearchItemCode` visibility in ASP.NET, though not in the provided C#).
    *   `txtSearchItemCode` (TextBox): Text input for the search query.
    *   `btnSearch` (Button): Triggers the search functionality.
*   **Data Display Grids:**
    *   `GridView2` (Machine List):
        *   Displays `SN`, `Machine Code` (LinkButton), `Description`, `Last PM Date`.
        *   Supports pagination.
        *   Clicking `Machine Code` (`LinkButton1`) triggers loading of `GridView1`.
    *   `GridView1` (Maintenance Details):
        *   Displays `SN`, `Select` (LinkButton), `PM Date`, `Type`, `Name of Agency`, `Name of Engineer`.
        *   Supports pagination.
        *   Clicking `Select` (`LinkButton1`) redirects to a detail page.
*   **UpdatePanel (`Up`):** Wraps `GridView1`, indicating partial page updates for the maintenance details grid. This is a direct candidate for HTMX's `hx-target` and `hx-swap` attributes.

**Django Frontend Strategy:**
*   **HTMX:** All filtering actions (dropdown changes, search button) and table row selections (`GridView2` to `GridView1` interaction) will be handled via HTMX. This will fetch and swap partial HTML fragments, eliminating full page reloads.
*   **Alpine.js:** Can be used for minor client-side UI states, such as managing modal visibility (if adding future CRUD modals) or simple form interactions not covered by HTMX. For this view, its role is minimal beyond the `base.html` structure.
*   **DataTables:** Both list views (`GridView2` and `GridView1` equivalents) will use DataTables for client-side sorting, searching, and pagination, providing a rich user experience.

### Step 4: Generate Django Code

We will create a Django application named `machinery` to encapsulate this module.

#### 4.1 Models (`machinery/models.py`)

**Task:** Create Django models that accurately reflect the extracted database schema and incorporate any necessary business logic as methods. These models will use `managed = False` to connect to existing tables.

**Instructions:**
*   Map `tblDG_Category_Master` to `Category`.
*   Map `tblDG_SubCategory_Master` to `SubCategory`.
*   Map `tblDG_Item_Master` to `Machine`. Add a property for the truncated description.
*   Map `tblMS_Master` to `MachineLastMaintenance`.
*   Map `tblMS_PMBM_Master` to `PMBMDetail`. Add a property for maintenance type text.
*   Implement `get_machines_for_filter` and `get_pmbm_details` as static methods or manager methods to encapsulate query logic, following the "fat model" principle.
*   Assume `COMPANY_ID` and `FINANCIAL_YEAR_ID` are globally accessible (e.g., from Django settings or a request context that needs to be passed). For now, we will use placeholders.

```python
from django.db import models
from django.db.models import F
from datetime import datetime

# Assuming these are configured globally or derived from user session
# For demonstration, we'll use placeholder values.
# In a real application, these would come from request.user, settings, etc.
DEFAULT_COMPANY_ID = 1
DEFAULT_FINANCIAL_YEAR_ID = 2023 # Example, adjust as per your system


class Category(models.Model):
    category_id = models.IntegerField(db_column='CId', primary_key=True)
    name = models.CharField(db_column='CategoryName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'

    def __str__(self):
        return self.name


class SubCategory(models.Model):
    subcategory_id = models.IntegerField(db_column='SCId', primary_key=True)
    category = models.ForeignKey(Category, models.DO_NOTHING, db_column='CId')
    name = models.CharField(db_column='SCName', max_length=255)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblDG_SubCategory_Master'
        verbose_name = 'SubCategory'
        verbose_name_plural = 'SubCategories'

    def __str__(self):
        return f"{self.symbol} - {self.name}"


class Machine(models.Model):
    item_id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50)
    description = models.CharField(db_column='ManfDesc', max_length=255)
    stock_qty = models.DecimalField(db_column='StockQty', max_digits=18, decimal_places=4, blank=True, null=True)
    location = models.CharField(db_column='Location', max_length=100, blank=True, null=True)
    uom_basic = models.CharField(db_column='UOMBasic', max_length=50, blank=True, null=True)
    category = models.ForeignKey(Category, models.DO_NOTHING, db_column='CId', blank=True, null=True)
    subcategory = models.ForeignKey(SubCategory, models.DO_NOTHING, db_column='SCId', blank=True, null=True)
    company_id = models.IntegerField(db_column='CompId')
    is_absolute = models.CharField(db_column='Absolute', max_length=1) # '1' for Absolute, other for not

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Machine'
        verbose_name_plural = 'Machines'

    def __str__(self):
        return f"{self.item_code} - {self.truncated_description}"

    @property
    def truncated_description(self):
        """Returns a truncated version of the description."""
        if self.description and len(self.description) > 80:
            return self.description[:80] + '...'
        return self.description

    @staticmethod
    def get_filtered_machines(category_id, subcategory_id, search_field, search_text, company_id=DEFAULT_COMPANY_ID):
        """
        Retrieves filtered machine data, similar to ASP.NET's Fillgridview logic.
        Includes the last maintenance date from MachineLastMaintenance.
        """
        queryset = Machine.objects.filter(
            company_id=company_id,
            is_absolute='0' # Assuming '0' means not absolute/active
        ).order_by('-item_id').annotate(
            last_pmbm_date=F('machinelastmaintenance__sys_date'),
            master_id=F('machinelastmaintenance__master_id')
        )

        if category_id and category_id != 'Select Category':
            queryset = queryset.filter(category_id=category_id)
            if subcategory_id and subcategory_id != 'Select SubCategory':
                queryset = queryset.filter(subcategory_id=subcategory_id)

        if search_field and search_text:
            if search_field == 'tblDG_Item_Master.ItemCode':
                queryset = queryset.filter(item_code__startswith=search_text)
            elif search_field == 'tblDG_Item_Master.ManfDesc':
                queryset = queryset.filter(description__icontains=search_text)
        
        # Ensure we only get machines with a last maintenance record, as per ASP.NET's join logic
        queryset = queryset.filter(machinelastmaintenance__isnull=False)

        return queryset


class MachineLastMaintenance(models.Model):
    master_id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateTimeField(db_column='SysDate')
    item = models.ForeignKey(Machine, models.DO_NOTHING, db_column='ItemId')

    class Meta:
        managed = False
        db_table = 'tblMS_Master'
        verbose_name = 'Machine Last Maintenance'
        verbose_name_plural = 'Machine Last Maintenances'

    def __str__(self):
        return f"Maintenance for {self.item.item_code} on {self.sys_date.strftime('%d/%m/%Y')}"


class PMBMDetail(models.Model):
    pmbm_id = models.IntegerField(db_column='Id', primary_key=True)
    machine_master = models.ForeignKey(MachineLastMaintenance, models.DO_NOTHING, db_column='MachineId')
    sys_date = models.DateTimeField(db_column='SysDate')
    pmbm_type_code = models.IntegerField(db_column='PMBM') # 0 for Preventive, 1 for Breakdown
    agency_name = models.CharField(db_column='NameOfAgency', max_length=255, blank=True, null=True)
    engineer_name = models.CharField(db_column='NameOfEngineer', max_length=255, blank=True, null=True)
    company_id = models.IntegerField(db_column='CompId')
    financial_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblMS_PMBM_Master'
        verbose_name = 'PM/BM Detail'
        verbose_name_plural = 'PM/BM Details'

    def __str__(self):
        return f"{self.maintenance_type} for {self.machine_master.item.item_code} on {self.formatted_pmbm_date}"

    @property
    def maintenance_type(self):
        """Converts the PMBM integer code to a descriptive string."""
        return "Preventive" if self.pmbm_type_code == 0 else "Breakdown"

    @property
    def formatted_pmbm_date(self):
        """Formats the sys_date to DD/MM/YYYY string."""
        return self.sys_date.strftime('%d/%m/%Y')

    @staticmethod
    def get_pmbm_details_for_machine(machine_master_id, company_id=DEFAULT_COMPANY_ID, fin_year_id=DEFAULT_FINANCIAL_YEAR_ID):
        """
        Retrieves PM/BM details for a specific machine maintenance master ID.
        Corresponds to ASP.NET's PMBMGrid logic.
        """
        return PMBMDetail.objects.filter(
            machine_master_id=machine_master_id,
            company_id=company_id,
            financial_year_id__lte=fin_year_id # ASP.NET uses <= finyearid
        ).order_by('sys_date') # Order by date, assuming no explicit order in ASP.NET beyond implicit DB order
```

#### 4.2 Forms (`machinery/forms.py`)

**Task:** Define a Django form for the filtering controls, allowing for robust validation and cleaner template rendering.

**Instructions:**
*   Create a simple `Form` (not `ModelForm` as it's purely for filtering).
*   Include `Category`, `SubCategory`, `Search By`, and `Search Text` fields.
*   Populate `Category` and `SubCategory` choices dynamically.

```python
from django import forms
from .models import Category, SubCategory

class MachineFilterForm(forms.Form):
    category = forms.ChoiceField(
        choices=[('Select Category', 'Select Category')] + [(c.category_id, c.name) for c in Category.objects.all()],
        required=False,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                                   'hx-get': "{% url 'machinery:machines_table' %}",
                                   'hx-target': '#machine-list-container',
                                   'hx-swap': 'innerHTML',
                                   'hx-indicator': '#machine-loader',
                                   'hx-trigger': 'change, refreshMachineList from:body' # Added refresh trigger for category/subcategory changes
                                   }),
        label="Category"
    )
    
    subcategory = forms.ChoiceField(
        choices=[('Select SubCategory', 'Select SubCategory')], # Will be updated dynamically via HTMX
        required=False,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                                   'hx-get': "{% url 'machinery:machines_table' %}",
                                   'hx-target': '#machine-list-container',
                                   'hx-swap': 'innerHTML',
                                   'hx-indicator': '#machine-loader',
                                   'hx-trigger': 'change, refreshMachineList from:body' # Added refresh trigger
                                   }),
        label="Sub Category"
    )

    search_field = forms.ChoiceField(
        choices=[
            ('Select', 'Select'),
            ('tblDG_Item_Master.ItemCode', 'Machine Code'),
            ('tblDG_Item_Master.ManfDesc', 'Description')
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label="Search By"
    )

    search_text = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Search term'}),
        label="Search Text"
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Dynamically load categories on form init
        self.fields['category'].choices = [('Select Category', 'Select Category')] + \
                                          [(c.category_id, c.name) for c in Category.objects.all().order_by('name')]
        
        # Populate subcategories if category is initially selected
        if 'category' in self.data and self.data['category'] != 'Select Category':
            try:
                selected_category_id = int(self.data['category'])
                self.fields['subcategory'].choices = [('Select SubCategory', 'Select SubCategory')] + \
                                                     [(s.subcategory_id, f"{s.symbol} - {s.name}") for s in SubCategory.objects.filter(category_id=selected_category_id).order_by('name')]
            except (ValueError, TypeError):
                pass
        
        # Add a placeholder for initial subcategory population (e.g., from DB) if needed
        # self.fields['subcategory'].initial = self.data.get('subcategory', 'Select SubCategory')


class SubCategoryChoicesForm(forms.Form):
    """
    A minimal form to get subcategory choices, used when category changes.
    """
    subcategory = forms.ChoiceField(
        choices=[('Select SubCategory', 'Select SubCategory')],
        required=False,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                                   'hx-get': "{% url 'machinery:machines_table' %}",
                                   'hx-target': '#machine-list-container',
                                   'hx-swap': 'innerHTML',
                                   'hx-indicator': '#machine-loader',
                                   'hx-trigger': 'change, refreshMachineList from:body' # Added refresh trigger
                                   }),
        label="Sub Category"
    )

    def __init__(self, *args, **kwargs):
        category_id = kwargs.pop('category_id', None)
        super().__init__(*args, **kwargs)
        if category_id:
            self.fields['subcategory'].choices = [('Select SubCategory', 'Select SubCategory')] + \
                                                 [(s.subcategory_id, f"{s.symbol} - {s.name}") for s in SubCategory.objects.filter(category_id=category_id).order_by('name')]
```

#### 4.3 Views (`machinery/views.py`)

**Task:** Implement Django Class-Based Views for rendering the main page, handling HTMX requests for table updates, and managing the redirect. Strict adherence to the 15-line view method limit is crucial, pushing logic to models.

**Instructions:**
*   `MachineMaintenancePrintView`: Renders the main template, provides the initial filter form.
*   `MachineListTablePartialView`: Handles the HTMX request for the main machine list, filters data using `Machine.get_filtered_machines`, and renders the `_machine_table.html` partial. This will also handle updating the SubCategory dropdown based on the selected Category.
*   `PMBMDetailTablePartialView`: Handles the HTMX request for machine maintenance details, filters data using `PMBMDetail.get_pmbm_details_for_machine`, and renders `_pmbm_detail_table.html` partial.
*   `PMBMDetailRedirectView`: Handles the redirect to the detailed page.

```python
from django.views.generic import TemplateView, ListView, RedirectView
from django.urls import reverse_lazy
from django.http import HttpResponse, JsonResponse
from django.db.models import F

from .models import Category, SubCategory, Machine, PMBMDetail, DEFAULT_COMPANY_ID, DEFAULT_FINANCIAL_YEAR_ID
from .forms import MachineFilterForm, SubCategoryChoicesForm

# Helper to keep views thin and manage common query parameters
def _get_query_params(request):
    return {
        'category_id': request.GET.get('category', 'Select Category'),
        'subcategory_id': request.GET.get('subcategory', 'Select SubCategory'),
        'search_field': request.GET.get('search_field', 'Select'),
        'search_text': request.GET.get('search_text', ''),
        # Assume company_id and fin_year_id are derived from request.user or settings
        'company_id': DEFAULT_COMPANY_ID, # Replace with dynamic acquisition
        'fin_year_id': DEFAULT_FINANCIAL_YEAR_ID # Replace with dynamic acquisition
    }


class MachineMaintenancePrintView(TemplateView):
    template_name = 'machinery/pmbm_print/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['filter_form'] = MachineFilterForm(self.request.GET)
        return context


class MachineListTablePartialView(ListView):
    template_name = 'machinery/pmbm_print/_machine_table.html'
    context_object_name = 'machines'

    def get_queryset(self):
        params = _get_query_params(self.request)
        return Machine.get_filtered_machines(
            params['category_id'],
            params['subcategory_id'],
            params['search_field'],
            params['search_text'],
            params['company_id']
        )

    def render_to_response(self, context, **response_kwargs):
        # Handle dynamic subcategory population for HTMX
        if self.request.headers.get('HX-Request') and 'category' in self.request.GET:
            category_id = self.request.GET.get('category')
            if category_id and category_id != 'Select Category':
                try:
                    category_id = int(category_id)
                    subcategories = SubCategory.objects.filter(category_id=category_id).order_by('name')
                    options_html = '<option value="Select SubCategory">Select SubCategory</option>'
                    for sc in subcategories:
                        options_html += f'<option value="{sc.subcategory_id}">{sc.symbol} - {sc.name}</option>'
                    
                    # This is a specific HTMX target swap for subcategory dropdown
                    return HttpResponse(f'<select id="id_subcategory" name="subcategory" hx-get="{% url "machinery:machines_table" %}" hx-target="#machine-list-container" hx-swap="innerHTML" hx-indicator="#machine-loader" hx-trigger="change, refreshMachineList from:body" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">{options_html}</select>', headers={'HX-Retarget': '#id_subcategory', 'HX-Reswap': 'outerHTML'})
                except ValueError:
                    pass # Invalid category ID
        
        return super().render_to_response(context, **response_kwargs)


class PMBMDetailTablePartialView(ListView):
    template_name = 'machinery/pmbm_print/_pmbm_detail_table.html'
    context_object_name = 'pmbm_details'

    def get_queryset(self):
        machine_master_id = self.kwargs['machine_master_id']
        params = _get_query_params(self.request) # Accessing company_id, fin_year_id from shared params
        return PMBMDetail.get_pmbm_details_for_machine(
            machine_master_id,
            params['company_id'],
            params['fin_year_id']
        )


class PMBMDetailRedirectView(RedirectView):
    permanent = False
    query_string = True

    def get_redirect_url(self, *args, **kwargs):
        pmbm_id = self.kwargs['pmbm_id']
        # In ASP.NET, it redirects to PMBM_Print_Details.aspx with params
        # This assumes a similar Django view exists for PMBM_Print_Details
        # For this example, we'll redirect to a dummy URL or an actual detail view.
        # The 'Key' param is typically for cache busting or security in ASP.NET,
        # often not needed directly in Django. ModId and SubModId are also app-specific.
        return reverse_lazy('machinery:pmbm_print_details', kwargs={'pk': pmbm_id})
        # You'd likely add query params like '?ModId=15&SubModId=68' if required by the target
```

#### 4.4 Templates (`machinery/templates/machinery/pmbm_print/`)

**Task:** Create the main page and partial templates for HTMX-driven table updates. Ensure they extend `core/base.html` and use DataTables for display.

**Instructions:**
*   `list.html`: The main page, containing the filter form, and placeholders for the two tables.
*   `_machine_table.html`: The partial for the first table (machines), rendered by `MachineListTablePartialView`.
*   `_pmbm_detail_table.html`: The partial for the second table (maintenance details), rendered by `PMBMDetailTablePartialView`.

**`list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 class="text-xl font-bold text-gray-800 mb-4">Preventive / Breakdown Maintenance - Print</h2>
        
        <form id="filter-form" hx-get="{% url 'machinery:machines_table' %}" hx-target="#machine-list-container" hx-swap="innerHTML" hx-indicator="#machine-loader">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                <div>
                    <label for="{{ filter_form.category.id_for_label }}" class="block text-sm font-medium text-gray-700">Category</label>
                    {{ filter_form.category }}
                </div>
                <div>
                    <label for="{{ filter_form.subcategory.id_for_label }}" class="block text-sm font-medium text-gray-700">Sub Category</label>
                    <div id="subcategory-wrapper">
                        {# Subcategory dropdown will be swapped here by HTMX when category changes #}
                        {{ filter_form.subcategory }}
                    </div>
                </div>
                <div>
                    <label for="{{ filter_form.search_field.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By</label>
                    {{ filter_form.search_field }}
                </div>
                <div>
                    <label for="{{ filter_form.search_text.id_for_label }}" class="block text-sm font-medium text-gray-700">Search Text</label>
                    {{ filter_form.search_text }}
                </div>
                <div class="md:col-span-1">
                    <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow-sm">
                        Search
                    </button>
                </div>
            </div>
            {# Hidden input to propagate the search query on category/subcategory change #}
            <input type="hidden" name="search_text" value="{{ filter_form.search_text.value|default:'' }}" />
            <input type="hidden" name="search_field" value="{{ filter_form.search_field.value|default:'Select' }}" />
        </form>
        
        <div class="htmx-indicator" id="machine-loader">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mt-4"></div>
            <p class="mt-2 text-gray-600">Loading machines...</p>
        </div>
    </div>

    <div class="flex flex-wrap -mx-3">
        <div class="w-full lg:w-1/2 px-3 mb-6 lg:mb-0">
            <div class="bg-white shadow-md rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Machine List</h3>
                <div id="machine-list-container" hx-trigger="load, refreshMachineList from:body" hx-get="{% url 'machinery:machines_table' %}" hx-swap="innerHTML">
                    {# Machine table will be loaded here via HTMX #}
                    <div class="text-center">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p class="mt-2">Loading...</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="w-full lg:w-1/2 px-3">
            <div class="bg-white shadow-md rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Maintenance Details</h3>
                <div id="pmbm-detail-container" hx-trigger="load, refreshPMBMList from:body" hx-get="" hx-swap="innerHTML">
                    {# PM/BM detail table will be loaded here via HTMX #}
                    <div class="text-center text-gray-500">
                        Select a machine to view its maintenance details.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    // Handle dynamic subcategory population for initial load or manual refresh if needed
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.target.id === 'subcategory-wrapper') {
            // Re-initialize any Alpine.js components if they were within the swapped content
            if (typeof Alpine !== 'undefined') {
                Alpine.initTree(event.target);
            }
        }
    });

    document.addEventListener('DOMContentLoaded', function() {
        // Handle dropdown dependencies for initial load.
        // On category change, HTMX will re-render the subcategory dropdown and then reload the machine table.
        document.getElementById('id_category').addEventListener('change', function() {
            var selectedCategoryId = this.value;
            var subcategoryWrapper = document.getElementById('subcategory-wrapper');
            if (selectedCategoryId && selectedCategoryId !== 'Select Category') {
                // Manually trigger HTMX to update subcategory dropdown options
                htmx.ajax('GET', '{% url "machinery:get_subcategories" %}' + '?category_id=' + selectedCategoryId, {
                    target: '#id_subcategory',
                    swap: 'outerHTML', // Replace the entire select element
                    indicator: '#machine-loader'
                });
            } else {
                // If category is "Select Category", reset subcategory to default
                document.getElementById('id_subcategory').innerHTML = '<option value="Select SubCategory">Select SubCategory</option>';
            }
            // Trigger the main machine table refresh (HTMX on form handles this automatically)
            // htmx.trigger('#filter-form', 'submit');
        });
    });
</script>
{% endblock %}
```

**`_machine_table.html`**

```html
<table id="machineTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Machine Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last PM Date</th>
        </tr>
    </thead>
    <tbody>
        {% for machine in machines %}
        <tr class="hover:bg-gray-50 cursor-pointer" 
            hx-get="{% url 'machinery:pmbm_details_table' machine.master_id %}" 
            hx-target="#pmbm-detail-container" 
            hx-swap="innerHTML" 
            hx-indicator="#pmbm-detail-loader">
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-blue-600 hover:underline">{{ machine.item_code }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ machine.truncated_description }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ machine.last_pmbm_date|date:"d/m/Y" }}</td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="4" class="py-4 px-4 text-center text-gray-500">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<div class="htmx-indicator" id="pmbm-detail-loader">
    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mt-4"></div>
    <p class="mt-2 text-gray-600">Loading maintenance details...</p>
</div>

<script>
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#machineTable')) {
            $('#machineTable').DataTable().destroy();
        }
        $('#machineTable').DataTable({
            "pageLength": 10, // Default page size
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true
        });
    });
</script>
```

**`_pmbm_detail_table.html`**

```html
<table id="pmbmDetailTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PM Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Agency</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Engineer</th>
        </tr>
    </thead>
    <tbody>
        {% for detail in pmbm_details %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <a href="{% url 'machinery:pmbm_detail_redirect' pmbm_id=detail.pmbm_id %}" class="text-blue-600 hover:underline">Select</a>
            </td>
            <td class="py-2 px-4 border-b border-gray-200">{{ detail.formatted_pmbm_date }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ detail.maintenance_type }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ detail.agency_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ detail.engineer_name }}</td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-4 px-4 text-center text-gray-500">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#pmbmDetailTable')) {
            $('#pmbmDetailTable').DataTable().destroy();
        }
        $('#pmbmDetailTable').DataTable({
            "pageLength": 10, // Default page size
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true
        });
    });
</script>
```

#### 4.5 URLs (`machinery/urls.py`)

**Task:** Define the URL patterns for accessing the main page, the HTMX partials for tables, and the redirect view.

**Instructions:**
*   Create distinct paths for the main view and each HTMX endpoint.
*   Ensure clear, descriptive names for URL patterns.

```python
from django.urls import path
from .views import (
    MachineMaintenancePrintView,
    MachineListTablePartialView,
    PMBMDetailTablePartialView,
    PMBMDetailRedirectView,
)
from .models import SubCategory # For dynamic subcategory options
from .forms import SubCategoryChoicesForm
from django.http import HttpResponse

app_name = 'machinery'

urlpatterns = [
    path('pmbm-print/', MachineMaintenancePrintView.as_view(), name='pmbm_print_list'),
    path('pmbm-print/machines-table/', MachineListTablePartialView.as_view(), name='machines_table'),
    path('pmbm-print/details-table/<int:machine_master_id>/', PMBMDetailTablePartialView.as_view(), name='pmbm_details_table'),
    path('pmbm-print/detail/<int:pmbm_id>/', PMBMDetailRedirectView.as_view(), name='pmbm_detail_redirect'),
    # Assuming a target view for PMBM_Print_Details.aspx
    path('pmbm-print/print-details/<int:pk>/', TemplateView.as_view(template_name='machinery/pmbm_print/details_page.html'), name='pmbm_print_details'),

    # HTMX endpoint for dynamic subcategory dropdown
    path('pmbm-print/get-subcategories/', lambda request: HttpResponse(SubCategoryChoicesForm(category_id=request.GET.get('category_id')).as_widget()), name='get_subcategories'),
]
```
*Note: The `get_subcategories` URL uses a simple lambda function to return the `select` widget for the subcategory. This is a very thin view, adhering to the principle.*

#### 4.6 Tests (`machinery/tests.py`)

**Task:** Write comprehensive unit tests for models and integration tests for views to ensure functionality and data integrity. Aim for at least 80% test coverage.

**Instructions:**
*   **Model Tests:** Verify model field mappings, `Meta` options, and custom properties/methods (`truncated_description`, `maintenance_type`, `formatted_pmbm_date`). Test the `get_filtered_machines` and `get_pmbm_details_for_machine` static methods.
*   **View Tests:** Test HTTP status codes, template usage, and context data for `MachineMaintenancePrintView`, `MachineListTablePartialView`, `PMBMDetailTablePartialView`, and `PMBMDetailRedirectView`. Include tests for HTMX requests and proper content rendering.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db.models import QuerySet
from datetime import datetime
from unittest.mock import patch

from .models import (
    Category,
    SubCategory,
    Machine,
    MachineLastMaintenance,
    PMBMDetail,
    DEFAULT_COMPANY_ID,
    DEFAULT_FINANCIAL_YEAR_ID
)

# Mock global constants for testing
# In a real app, these might be in settings or derived from request.user
class MockConstants:
    DEFAULT_COMPANY_ID = 123
    DEFAULT_FINANCIAL_YEAR_ID = 2024

@patch('machinery.models.DEFAULT_COMPANY_ID', MockConstants.DEFAULT_COMPANY_ID)
@patch('machinery.models.DEFAULT_FINANCIAL_YEAR_ID', MockConstants.DEFAULT_FINANCIAL_YEAR_ID)
class ModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.category = Category.objects.create(category_id=1, name='Test Category')
        cls.subcategory = SubCategory.objects.create(subcategory_id=101, category=cls.category, name='Test SubCategory', symbol='TSC')
        
        cls.machine1 = Machine.objects.create(
            item_id=1001,
            item_code='M-001',
            description='This is a long description for Machine 1 to test truncation logic.',
            category=cls.category,
            subcategory=cls.subcategory,
            company_id=MockConstants.DEFAULT_COMPANY_ID,
            is_absolute='0'
        )
        cls.machine2 = Machine.objects.create(
            item_id=1002,
            item_code='M-002',
            description='Short description.',
            category=cls.category,
            subcategory=cls.subcategory,
            company_id=MockConstants.DEFAULT_COMPANY_ID,
            is_absolute='0'
        )
        cls.machine3 = Machine.objects.create( # Absolute machine, should be filtered out
            item_id=1003,
            item_code='M-003',
            description='Absolute machine.',
            category=cls.category,
            subcategory=cls.subcategory,
            company_id=MockConstants.DEFAULT_COMPANY_ID,
            is_absolute='1'
        )

        cls.maint_master1 = MachineLastMaintenance.objects.create(
            master_id=1,
            sys_date=datetime(2023, 10, 26, 10, 0, 0),
            item=cls.machine1
        )
        cls.maint_master2 = MachineLastMaintenance.objects.create(
            master_id=2,
            sys_date=datetime(2023, 10, 25, 10, 0, 0),
            item=cls.machine2
        )

        cls.pmbm_detail1 = PMBMDetail.objects.create(
            pmbm_id=1,
            machine_master=cls.maint_master1,
            sys_date=datetime(2023, 10, 27, 12, 0, 0),
            pmbm_type_code=0, # Preventive
            agency_name='Test Agency A',
            engineer_name='Eng. John Doe',
            company_id=MockConstants.DEFAULT_COMPANY_ID,
            financial_year_id=MockConstants.DEFAULT_FINANCIAL_YEAR_ID
        )
        cls.pmbm_detail2 = PMBMDetail.objects.create(
            pmbm_id=2,
            machine_master=cls.maint_master1,
            sys_date=datetime(2023, 9, 15, 9, 0, 0),
            pmbm_type_code=1, # Breakdown
            agency_name='Test Agency B',
            engineer_name='Eng. Jane Smith',
            company_id=MockConstants.DEFAULT_COMPANY_ID,
            financial_year_id=MockConstants.DEFAULT_FINANCIAL_YEAR_ID - 1 # Older financial year
        )
        cls.pmbm_detail3 = PMBMDetail.objects.create(
            pmbm_id=3,
            machine_master=cls.maint_master2,
            sys_date=datetime(2023, 11, 1, 14, 0, 0),
            pmbm_type_code=0, # Preventive
            agency_name='Test Agency C',
            engineer_name='Eng. Bob Johnson',
            company_id=MockConstants.DEFAULT_COMPANY_ID,
            financial_year_id=MockConstants.DEFAULT_FINANCIAL_YEAR_ID
        )

    def test_category_creation(self):
        self.assertEqual(self.category.name, 'Test Category')
        self.assertEqual(str(self.category), 'Test Category')
        self.assertEqual(Category._meta.db_table, 'tblDG_Category_Master')

    def test_subcategory_creation(self):
        self.assertEqual(self.subcategory.name, 'Test SubCategory')
        self.assertEqual(str(self.subcategory), 'TSC - Test SubCategory')
        self.assertEqual(self.subcategory.category, self.category)
        self.assertEqual(SubCategory._meta.db_table, 'tblDG_SubCategory_Master')

    def test_machine_creation(self):
        self.assertEqual(self.machine1.item_code, 'M-001')
        self.assertEqual(self.machine1.description, 'This is a long description for Machine 1 to test truncation logic.')
        self.assertEqual(self.machine1.truncated_description, 'This is a long description for Machine 1 to test truncation logic....')
        self.assertEqual(self.machine2.truncated_description, 'Short description.')
        self.assertEqual(str(self.machine1), 'M-001 - This is a long description for Machine 1 to test truncation logic....')
        self.assertEqual(Machine._meta.db_table, 'tblDG_Item_Master')
        self.assertEqual(self.machine1.is_absolute, '0')

    def test_machinelastmaintenance_creation(self):
        self.assertEqual(self.maint_master1.item, self.machine1)
        self.assertEqual(self.maint_master1.sys_date, datetime(2023, 10, 26, 10, 0, 0))
        self.assertEqual(str(self.maint_master1), 'Maintenance for M-001 on 26/10/2023')
        self.assertEqual(MachineLastMaintenance._meta.db_table, 'tblMS_Master')

    def test_pmbm_detail_creation(self):
        self.assertEqual(self.pmbm_detail1.machine_master, self.maint_master1)
        self.assertEqual(self.pmbm_detail1.pmbm_type_code, 0)
        self.assertEqual(self.pmbm_detail1.maintenance_type, 'Preventive')
        self.assertEqual(self.pmbm_detail2.maintenance_type, 'Breakdown')
        self.assertEqual(self.pmbm_detail1.formatted_pmbm_date, '27/10/2023')
        self.assertEqual(str(self.pmbm_detail1), 'Preventive for M-001 on 27/10/2023')
        self.assertEqual(PMBMDetail._meta.db_table, 'tblMS_PMBM_Master')

    # Test static methods
    def test_get_filtered_machines_no_filter(self):
        machines = Machine.get_filtered_machines(None, None, None, None, MockConstants.DEFAULT_COMPANY_ID)
        self.assertIsInstance(machines, QuerySet)
        self.assertEqual(machines.count(), 2) # machine1, machine2 (machine3 is absolute)
        self.assertIn(self.machine1, machines)
        self.assertIn(self.machine2, machines)
        self.assertNotIn(self.machine3, machines) # Absolute machine should be filtered out

    def test_get_filtered_machines_by_category(self):
        machines = Machine.get_filtered_machines(self.category.category_id, None, None, None, MockConstants.DEFAULT_COMPANY_ID)
        self.assertEqual(machines.count(), 2) # machine1, machine2

    def test_get_filtered_machines_by_subcategory(self):
        machines = Machine.get_filtered_machines(self.category.category_id, self.subcategory.subcategory_id, None, None, MockConstants.DEFAULT_COMPANY_ID)
        self.assertEqual(machines.count(), 2)

    def test_get_filtered_machines_by_item_code(self):
        machines = Machine.get_filtered_machines(None, None, 'tblDG_Item_Master.ItemCode', 'M-001', MockConstants.DEFAULT_COMPANY_ID)
        self.assertEqual(machines.count(), 1)
        self.assertEqual(machines.first(), self.machine1)

    def test_get_filtered_machines_by_description(self):
        machines = Machine.get_filtered_machines(None, None, 'tblDG_Item_Master.ManfDesc', 'long description', MockConstants.DEFAULT_COMPANY_ID)
        self.assertEqual(machines.count(), 1)
        self.assertEqual(machines.first(), self.machine1)
        
        machines = Machine.get_filtered_machines(None, None, 'tblDG_Item_Master.ManfDesc', 'Short', MockConstants.DEFAULT_COMPANY_ID)
        self.assertEqual(machines.count(), 1)
        self.assertEqual(machines.first(), self.machine2)

    def test_get_filtered_machines_no_last_maintenance(self):
        # Create a machine with no last maintenance record
        no_maint_machine = Machine.objects.create(
            item_id=1004, item_code='M-004', description='No maint',
            company_id=MockConstants.DEFAULT_COMPANY_ID, is_absolute='0'
        )
        machines = Machine.get_filtered_machines(None, None, None, None, MockConstants.DEFAULT_COMPANY_ID)
        self.assertNotIn(no_maint_machine, machines)
        
    def test_get_pmbm_details_for_machine(self):
        details = PMBMDetail.get_pmbm_details_for_machine(self.maint_master1.master_id, MockConstants.DEFAULT_COMPANY_ID, MockConstants.DEFAULT_FINANCIAL_YEAR_ID)
        self.assertEqual(details.count(), 1) # Only pmbm_detail1, pmbm_detail2 is from previous FY
        self.assertIn(self.pmbm_detail1, details)
        self.assertNotIn(self.pmbm_detail2, details)

        details = PMBMDetail.get_pmbm_details_for_machine(self.maint_master2.master_id, MockConstants.DEFAULT_COMPANY_ID, MockConstants.DEFAULT_FINANCIAL_YEAR_ID)
        self.assertEqual(details.count(), 1)
        self.assertIn(self.pmbm_detail3, details)

    def test_get_pmbm_details_for_machine_different_finyear(self):
        details = PMBMDetail.get_pmbm_details_for_machine(self.maint_master1.master_id, MockConstants.DEFAULT_COMPANY_ID, MockConstants.DEFAULT_FINANCIAL_YEAR_ID - 1)
        self.assertEqual(details.count(), 1) # Only pmbm_detail2
        self.assertIn(self.pmbm_detail2, details)
        self.assertNotIn(self.pmbm_detail1, details)


@patch('machinery.models.DEFAULT_COMPANY_ID', MockConstants.DEFAULT_COMPANY_ID)
@patch('machinery.models.DEFAULT_FINANCIAL_YEAR_ID', MockConstants.DEFAULT_FINANCIAL_YEAR_ID)
class ViewTest(TestCase):
    client = Client()

    @classmethod
    def setUpTestData(cls):
        # Create test data (same as ModelTest for consistency)
        cls.category = Category.objects.create(category_id=1, name='Test Category')
        cls.subcategory = SubCategory.objects.create(subcategory_id=101, category=cls.category, name='Test SubCategory', symbol='TSC')
        
        cls.machine1 = Machine.objects.create(
            item_id=1001,
            item_code='M-001',
            description='Machine One Desc',
            category=cls.category,
            subcategory=cls.subcategory,
            company_id=MockConstants.DEFAULT_COMPANY_ID,
            is_absolute='0'
        )
        cls.maint_master1 = MachineLastMaintenance.objects.create(
            master_id=1,
            sys_date=datetime(2023, 10, 26, 10, 0, 0),
            item=cls.machine1
        )
        cls.pmbm_detail1 = PMBMDetail.objects.create(
            pmbm_id=1,
            machine_master=cls.maint_master1,
            sys_date=datetime(2023, 10, 27, 12, 0, 0),
            pmbm_type_code=0, # Preventive
            agency_name='Test Agency',
            engineer_name='Test Engineer',
            company_id=MockConstants.DEFAULT_COMPANY_ID,
            financial_year_id=MockConstants.DEFAULT_FINANCIAL_YEAR_ID
        )

    def test_machine_maintenance_print_view_get(self):
        response = self.client.get(reverse('machinery:pmbm_print_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/pmbm_print/list.html')
        self.assertIn('filter_form', response.context)
        self.assertContains(response, 'Preventive / Breakdown Maintenance - Print')

    def test_machine_list_table_partial_view_get(self):
        response = self.client.get(reverse('machinery:machines_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/pmbm_print/_machine_table.html')
        self.assertIn('machines', response.context)
        self.assertContains(response, self.machine1.item_code) # Check if machine is rendered

    def test_machine_list_table_partial_view_get_filtered(self):
        # Test filtering by category
        response = self.client.get(reverse('machinery:machines_table'), {'category': self.category.category_id})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.machine1.item_code)

        # Test filtering by item code search
        response = self.client.get(reverse('machinery:machines_table'), {'search_field': 'tblDG_Item_Master.ItemCode', 'search_text': 'M-001'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.machine1.item_code)
        
        # Test HTMX request for subcategory update
        response = self.client.get(reverse('machinery:machines_table'), {'category': self.category.category_id}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertIn('HX-Retarget', response.headers)
        self.assertIn('HX-Reswap', response.headers)
        self.assertContains(response, '<option value="101">TSC - Test SubCategory</option>')


    def test_pmbm_detail_table_partial_view_get(self):
        response = self.client.get(reverse('machinery:pmbm_details_table', args=[self.maint_master1.master_id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/pmbm_print/_pmbm_detail_table.html')
        self.assertIn('pmbm_details', response.context)
        self.assertContains(response, self.pmbm_detail1.agency_name)

    def test_pmbm_detail_redirect_view(self):
        response = self.client.get(reverse('machinery:pmbm_detail_redirect', args=[self.pmbm_detail1.pmbm_id]))
        self.assertEqual(response.status_code, 302) # Redirect expected
        # Verify the redirect URL. You'd need to mock the target if it doesn't exist.
        expected_url = reverse('machinery:pmbm_print_details', kwargs={'pk': self.pmbm_detail1.pmbm_id})
        self.assertRedirects(response, expected_url, fetch_redirect_response=False)

    def test_get_subcategories_view(self):
        response = self.client.get(reverse('machinery:get_subcategories'), {'category_id': self.category.category_id})
        self.assertEqual(response.status_code, 200)
        # Check if the HTML for the select widget is returned
        self.assertIn('<select name="subcategory" id="id_subcategory"', response.content.decode())
        self.assertContains(response, '<option value="101">TSC - Test SubCategory</option>')

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The Django code provided in the templates and views already incorporates HTMX and Alpine.js best practices:

*   **HTMX for Dynamic Updates:**
    *   The `list.html` uses `hx-get` on `machine-list-container` to load the initial machine table.
    *   The filter `form` uses `hx-get` to refresh the `machine-list-container` when search parameters change.
    *   Category dropdown has `hx-get` to `get_subcategories` URL, and also `hx-trigger="change"` to refresh the `machines_table` when selected.
    *   Rows in `_machine_table.html` use `hx-get` to `pmbm_details_table` to load the maintenance details when a machine is selected.
    *   `hx-indicator` is used to show loading spinners during HTMX requests.
*   **Alpine.js for UI State:**
    *   While explicit Alpine.js components are not defined in this specific module (as per instructions focusing on HTMX first), the `base.html` (which this module extends) would already include Alpine.js.
    *   The modal example in the template instructions would typically be where Alpine.js shines, managing `x-data` and `x-show` for modal visibility. For this print view, it's not strictly necessary.
*   **DataTables for List Views:**
    *   Both `_machine_table.html` and `_pmbm_detail_table.html` include `<script>` tags to initialize DataTables on their respective tables. This ensures client-side sorting, searching, and pagination are available immediately upon HTMX swapping in the table.
*   **No Full Page Reloads:** All filtering and data loading interactions are designed to be partial updates using HTMX, providing a smooth user experience.
*   **`HX-Trigger` Responses:** While direct `HX-Trigger` headers are not explicitly set in the views for *this* specific module's list updates (as `hx-get` on `form` and `table rows` implicitly manage target and swap), the `CreateView`/`UpdateView` patterns would typically use `HX-Trigger` for `refreshList` events (as demonstrated in the template instructions) if such CRUD operations were present in this module.

### Final Notes

*   **Placeholders:** Replace `DEFAULT_COMPANY_ID` and `DEFAULT_FINANCIAL_YEAR_ID` with actual dynamic values derived from your authentication system (e.g., `request.user.profile.company_id`).
*   **`PMBM_Print_Details.aspx`:** The redirection to `PMBM_Print_Details.aspx` is mapped to `machinery:pmbm_print_details`. You will need to implement this target Django view and its associated template separately.
*   **Error Handling & User Feedback:** While the provided code focuses on core functionality, robust error handling, and more detailed user feedback (beyond simple loading indicators) would be implemented in a full production system.
*   **Database Migrations:** Before running, ensure your Django `settings.py` is configured to connect to your existing SQL Server database. Run `python manage.py inspectdb > models.py` initially to verify table mappings, then integrate the custom logic (properties, static methods) into the generated models.
*   **Static/Media Files:** Ensure Django's static files configuration correctly serves CSS/JS for DataTables, Tailwind CSS, HTMX, and Alpine.js. `core/base.html` should handle CDN links or local static file serving.
*   **Authentication/Authorization:** This plan assumes the user is already authenticated and their `CompId`/`FinYearId` are known. Implement Django's authentication and authorization mechanisms (e.g., `LoginRequiredMixin`) as needed.