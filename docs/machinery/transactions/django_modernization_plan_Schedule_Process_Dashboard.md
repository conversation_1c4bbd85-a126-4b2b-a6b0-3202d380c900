## ASP.NET to Django Conversion Script: Machinery Process Dashboard

This document outlines a strategic plan to modernize your legacy ASP.NET Machinery Process Dashboard to a robust, scalable, and maintainable Django application. Our approach leverages state-of-the-art AI-assisted automation to streamline the migration, focusing on systematic conversion rather than extensive manual code rewriting. This ensures efficiency, reduces human error, and delivers a modern solution that aligns with current web development best practices.

### Business Value Proposition:

Migrating to Django 5.0+ with HTMX and Alpine.js offers significant business advantages:

1.  **Reduced Maintenance Costs**: Django's structured framework and Python's readability inherently lower the cost of maintaining and debugging applications compared to legacy ASP.NET Web Forms.
2.  **Enhanced Scalability**: Django is built for scalability, allowing your application to handle increased user loads and data volumes without significant performance degradation.
3.  **Improved User Experience (UX)**: HTMX and Alpine.js deliver a responsive, dynamic user interface without the complexity of traditional JavaScript frameworks, leading to faster interactions and a smoother user experience.
4.  **Accelerated Feature Development**: Django's "batteries-included" philosophy, combined with a clean architecture, enables your team to develop and deploy new features much faster.
5.  **Future-Proof Technology**: Moving to a modern, open-source stack ensures your application remains relevant and adaptable to evolving business needs and technological advancements.
6.  **Simplified Data Management**: By adopting Django's ORM and a 'Fat Model, Thin View' approach, business logic is centralized, making data interactions more secure, efficient, and easier to manage.

---

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the ASP.NET code.

**Instructions:**
The ASP.NET code interacts with several database tables to retrieve machine and process information:
-   `tblDG_Item_Master`: This table stores the main details of machine items.
    -   `Id` (Primary Key, integer)
    -   `ManfDesc` (Machine Name, string)
    -   `CompId` (Company ID, integer)
    -   `FinYearId` (Financial Year ID, integer)
-   `tblMS_Master`: This table seems to link machine items to broader machine configurations or master data.
    -   `Id` (Primary Key, integer)
    -   `ItemId` (Foreign Key to `tblDG_Item_Master.Id`, integer)
-   `tblPln_Process_Master`: This table holds the definitions of various processes.
    -   `Id` (Primary Key, integer)
    -   `ProcessName` (Name of the process, string)
-   `tblMS_Process`: This is an association table linking machine configurations (`tblMS_Master`) to processes (`tblPln_Process_Master`).
    -   `MId` (Foreign Key to `tblMS_Master.Id`, integer)
    -   `PId` (Foreign Key to `tblPln_Process_Master.Id`, integer)

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations performed by the ASP.NET code.

**Instructions:**
The ASP.NET `Schedule_Process_Dashboard` page primarily performs **Read** operations and **Navigation**:
-   **Display Machine Name**: It fetches the `ManfDesc` (Machine Name) from `tblDG_Item_Master` based on a `MachineId` provided in the URL.
-   **List Processes**: It retrieves a list of `ProcessName` values from `tblPln_Process_Master` that are associated with the given `MachineId` through `tblMS_Master` and `tblMS_Process`. This involves joining multiple tables to filter and display the relevant processes.
-   **Drill-down Navigation**: Clicking a "Process" link redirects the user to `Schedule_Dashboard.aspx`, passing the `ProcessId` and `MachineId` as query parameters. This is a navigation action to a related dashboard.
-   **Cancel Navigation**: The "Cancel" button redirects the user to `Schedule_Machine_Dashboard.aspx`.

No explicit Create, Update, or Delete (CRUD) operations are identified for the "process dashboard" entity itself.

### Step 3: Infer UI Components

**Task:** Analyze the ASP.NET controls and their roles in the user interface.

**Instructions:**
-   `lblMachine`: This ASP.NET Label control displays the retrieved machine name. In Django, this will be a context variable rendered directly in the template.
-   `GridView2`: This is the primary data display component, showing a list of processes. It features server-side pagination and handles row commands. In Django, this will be replaced by a DataTables-powered HTML table, dynamically loaded and updated using HTMX.
-   `asp:LinkButton` (within `GridView2` columns): This control allows users to click on a "Process" name, triggering a command that leads to a new page. In Django, this will be a standard HTML `<a>` tag or an HTMX-powered button/link that triggers a redirect.
-   `asp:Button ID="btnCancel"`: This button initiates a redirect to a different dashboard page. In Django, this will be an HTMX-powered button or a standard link that navigates to the cancellation destination.

### Step 4: Generate Django Code

We will create a new Django application, `machinery_process`, to house the modernized code for this dashboard.

#### 4.1 Models

**Task:** Create Django models that map to your existing database tables.

**Instructions:**
Each identified database table will have a corresponding Django model. We'll use `managed = False` to ensure Django does not attempt to create or modify these tables, as they already exist in your database. Business logic for fetching dashboard data will be placed within the `MSProcess` model as class methods, adhering to the 'Fat Model' principle.

**File: `machinery_process/models.py`**

```python
from django.db import models

class DGItemMaster(models.Model):
    """
    Maps to tblDG_Item_Master, representing machine definitions.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    class Meta:
        managed = False  # Django will not manage this table's schema
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Machine Item'
        verbose_name_plural = 'Machine Items'

    def __str__(self):
        return self.manf_desc or f"Machine Item {self.id}"

class MSMaster(models.Model):
    """
    Maps to tblMS_Master, representing machine master configurations.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    item = models.ForeignKey(DGItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId', related_name='ms_masters')

    class Meta:
        managed = False
        db_table = 'tblMS_Master'
        verbose_name = 'Machine Master'
        verbose_name_plural = 'Machine Masters'

    def __str__(self):
        return f"MS Master for {self.item.manf_desc} (ID: {self.id})"

class PlnProcessMaster(models.Model):
    """
    Maps to tblPln_Process_Master, representing the master list of processes.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    process_name = models.CharField(db_column='ProcessName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblPln_Process_Master'
        verbose_name = 'Process Master'
        verbose_name_plural = 'Process Masters'

    def __str__(self):
        return self.process_name or f"Process {self.id}"

class MSProcess(models.Model):
    """
    Maps to tblMS_Process, associating processes with machine master configurations.
    We are assuming 'MId' (mid) forms part of a composite key or is the primary key for Django's ORM.
    """
    mid = models.ForeignKey(MSMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='ms_processes', primary_key=True)
    pid = models.ForeignKey(PlnProcessMaster, on_delete=models.DO_NOTHING, db_column='PId', related_name='ms_processes')

    class Meta:
        managed = False
        db_table = 'tblMS_Process'
        verbose_name = 'Machine Process Association'
        verbose_name_plural = 'Machine Process Associations'
        unique_together = (('mid', 'pid'),) # Ensures the combination of mid and pid is unique

    def __str__(self):
        return f"Machine: {self.mid.item.manf_desc} - Process: {self.pid.process_name}"

    @classmethod
    def get_processes_for_machine(cls, machine_id: int, comp_id: int, fin_year_id: int) -> list[dict]:
        """
        Retrieves the list of processes for a given machine ID, company ID, and financial year ID.
        This method mirrors the data retrieval logic from the ASP.NET fillGrid() function.
        It encapsulates complex database queries within the model.
        """
        try:
            # 1. Find the MSMaster configuration ID related to the machine item
            ms_master_configs = MSMaster.objects.filter(
                item__id=machine_id,
                item__comp_id=comp_id,
                item__fin_year_id__lte=fin_year_id
            ).values_list('id', flat=True)

            if not ms_master_configs:
                return []

            # 2. Get processes associated with these MSMaster configurations
            #    and select their process names.
            processes_data = cls.objects.filter(
                mid__in=ms_master_configs
            ).select_related('pid').order_by('pid__process_name')

            # 3. Format the data to match the expected output structure (Id, MachineId, Process)
            #    from the ASP.NET GridView.
            formatted_processes = [
                {
                    'Id': p.pid.id,
                    'MachineId': machine_id,
                    'Process': p.pid.process_name
                }
                for p in processes_data
            ]
            return formatted_processes
        except Exception as e:
            # In a production environment, this should be logged using Django's logging system.
            print(f"Error fetching processes for machine {machine_id}: {e}")
            return []

    @classmethod
    def get_machine_name(cls, machine_id: int, comp_id: int, fin_year_id: int) -> str:
        """
        Retrieves the machine name for a given machine ID, company ID, and financial year ID.
        This method mirrors the lblMachine.Text population logic from the ASP.NET Page_Load.
        """
        try:
            machine_item = DGItemMaster.objects.filter(
                id=machine_id,
                comp_id=comp_id,
                fin_year_id__lte=fin_year_id
            ).first()
            return machine_item.manf_desc if machine_item else "Unknown Machine"
        except Exception as e:
            print(f"Error fetching machine name for ID {machine_id}: {e}")
            return "Error retrieving machine name"

```

#### 4.2 Forms

**Task:** Define Django forms for user input.

**Instructions:**
The original ASP.NET dashboard is a display-only page with navigation features, not a data entry or modification form. Therefore, no Django `forms.ModelForm` or `forms.Form` is required for this specific view. If the drill-down pages (`Schedule_Dashboard.aspx`) involve data entry, their corresponding Django forms would be created in their respective applications.

**File: `machinery_process/forms.py` (Not required for this view)**

```python
# No form definition is required for the Machinery Process Dashboard
# as it's a read-only display page.
# If future modifications require data input, forms would be defined here.
```

#### 4.3 Views

**Task:** Implement the dashboard's display and navigation logic using Django Class-Based Views (CBVs).

**Instructions:**
We'll use a `TemplateView` for the main dashboard and a separate `TemplateView` for the HTMX-loaded table partial. Redirects will be handled by simple `View` classes. This keeps the views thin and delegates business logic to the models.

**File: `machinery_process/views.py`**

```python
from django.views.generic import TemplateView, View
from django.urls import reverse
from django.shortcuts import redirect
from django.contrib import messages
from django.http import HttpResponse
from urllib.parse import urlencode # For building query strings
from .models import MSProcess # Import MSProcess for its class methods

class MachineProcessDashboardView(TemplateView):
    """
    Main view for the Machinery Process Dashboard.
    Displays the machine name and loads the processes table via HTMX.
    """
    template_name = 'machinery_process/dashboard/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Extract parameters mirroring ASP.NET's Session and QueryString
        # In a real application, 'compid' and 'finyear' would typically come
        # from the authenticated user's profile or global settings.
        # For migration, we assume their presence.
        machine_id_str = self.request.GET.get('Id')
        comp_id = int(self.request.session.get('compid', 1)) # Default to 1 for testing
        fin_year_id = int(self.request.session.get('finyear', 2023)) # Default to 2023 for testing
        
        machine_name = "N/A"
        machine_id_int = None
        
        if machine_id_str:
            try:
                machine_id_int = int(machine_id_str)
                machine_name = MSProcess.get_machine_name(machine_id_int, comp_id, fin_year_id)
            except ValueError:
                messages.error(self.request, "Invalid Machine ID provided in URL.")
            except Exception as e:
                messages.error(self.request, f"Error initializing dashboard: {e}")

        context['machine_id'] = machine_id_int # Pass integer ID for URL building
        context['machine_name'] = machine_name
        return context

class MachineProcessTablePartialView(TemplateView):
    """
    HTMX-enabled partial view to render the DataTables for processes.
    This view is specifically designed to be loaded dynamically.
    """
    template_name = 'machinery_process/dashboard/_processes_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        machine_id_str = self.request.GET.get('Id')
        comp_id = int(self.request.session.get('compid', 1))
        fin_year_id = int(self.request.session.get('finyear', 2023))
        
        processes = []
        if machine_id_str:
            try:
                machine_id_int = int(machine_id_str)
                processes = MSProcess.get_processes_for_machine(machine_id_int, comp_id, fin_year_id)
            except ValueError:
                # Log this in a real application
                pass # Error message handled in main view, or table will simply be empty
            except Exception as e:
                # Log this in a real application
                pass # Error message handled in main view, or table will simply be empty
        
        context['processes'] = processes
        return context

class ProcessSelectionRedirectView(View):
    """
    Handles the redirection when a user selects a process, mimicking
    the ASP.NET GridView2_RowCommand action.
    """
    def get(self, request, process_id, machine_id, *args, **kwargs):
        # The ModId and SubModId are hardcoded as in the original ASP.NET redirect.
        # In a fully migrated system, these might map to more meaningful Django concepts
        # like specific app names or view logic flags.
        params = {
            'ProcessId': process_id,
            'MachineId': machine_id,
            'ModId': 15,
            'SubModId': 70
        }
        query_string = urlencode(params)
        # Assuming 'schedule_dashboard' is the Django URL name for the target page.
        # This will trigger a full page reload as per the original ASP.NET behavior.
        return redirect(f"{reverse('schedule_dashboard')}?{query_string}")

class CancelRedirectView(View):
    """
    Handles the redirection when the cancel button is clicked, mimicking
    the ASP.NET btnCancel_Click action.
    """
    def get(self, request, *args, **kwargs):
        params = {
            'ModId': 15,
            'SubModId': 70
        }
        query_string = urlencode(params)
        # Assuming 'schedule_machine_dashboard' is the Django URL name for the target page.
        return redirect(f"{reverse('schedule_machine_dashboard')}?{query_string}")

```

#### 4.4 Templates

**Task:** Create HTML templates for the dashboard display and the DataTables partial, integrating HTMX and Alpine.js.

**Instructions:**
-   `list.html` will be the main template, extending `core/base.html` and containing the `lblMachine` equivalent and an HTMX-powered container for the processes table.
-   `_processes_table.html` will be a partial template, containing only the DataTables structure and its initialization script, loaded via HTMX.
-   Tailwind CSS classes are used for styling.

**File: `machinery_process/dashboard/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white p-6 rounded-lg shadow-lg">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-bold text-gray-800">Machinery - Process Dashboard</h2>
        </div>

        <div class="mb-4">
            <p class="text-gray-700"><strong>Machine Name:</strong> <span class="font-bold text-blue-700">{{ machine_name }}</span></p>
        </div>

        <!-- HTMX container for the processes table -->
        <!-- The table will be loaded here dynamically on page load and on trigger -->
        <div id="processes-table-container"
             hx-trigger="load, refreshProcessesList from:body"
             hx-get="{% url 'machinery_process:dashboard_table' %}?Id={{ machine_id }}"
             hx-swap="innerHTML">
            <!-- Loading indicator while HTMX fetches the table content -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading Processes...</p>
            </div>
        </div>

        <div class="mt-6 text-center">
            <button 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                hx-get="{% url 'machinery_process:cancel_redirect' %}"
                hx-target="body" hx-swap="outerHTML"> {# hx-target and hx-swap for full page redirect via HTMX #}
                Cancel
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# Add any Alpine.js data or components specific to this dashboard if needed #}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('dashboardData', () => ({
            // Example: A reactive property for showing/hiding a custom modal
            // showModal: false,
            // openModal() { this.showModal = true; },
            // closeModal() { this.showModal = false; }
        }));
    });
</script>
{% endblock %}
```

**File: `machinery_process/dashboard/_processes_table.html`**

```html
<table id="processesTable" class="min-w-full bg-white border border-gray-200 shadow-sm rounded-lg overflow-hidden">
    <thead>
        <tr class="bg-gray-100">
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Process</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% if processes %}
            {% for proc in processes %}
            <tr class="{% cycle 'bg-white' 'bg-gray-50' %} hover:bg-blue-50 transition duration-150 ease-in-out">
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">
                    {# Mimics the ASP.NET LinkButton redirect #}
                    <a href="{% url 'machinery_process:process_select_redirect' process_id=proc.Id machine_id=proc.MachineId %}" 
                       class="text-blue-600 hover:text-blue-800 font-semibold cursor-pointer">
                        {{ proc.Process }}
                    </a>
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">
                    {# Placeholder for any future row-specific actions #}
                </td>
            </tr>
            {% endfor %}
        {% else %}
            <tr>
                <td colspan="3" class="py-4 text-center text-gray-500 text-lg font-medium">No data to display !</td>
            </tr>
        {% endif %}
    </tbody>
</table>

<script>
    // DataTables initialization script
    $(document).ready(function() {
        $('#processesTable').DataTable({
            "pageLength": 15, // Matches ASP.NET GridView PageSize
            "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 2] } // SN and Actions columns are not sortable
            ]
        });
    });
</script>
```

#### 4.5 URLs

**Task:** Define URL patterns to map incoming requests to the appropriate views.

**Instructions:**
URLs are defined within the `machinery_process` app's `urls.py`. We'll include patterns for the main dashboard, the HTMX table partial, and the redirect actions.

**File: `machinery_process/urls.py`**

```python
from django.urls import path
from django.views.generic import TemplateView # Used for dummy redirect targets
from .views import (
    MachineProcessDashboardView,
    MachineProcessTablePartialView,
    ProcessSelectionRedirectView,
    CancelRedirectView
)

app_name = 'machinery_process' # Namespace for this application's URLs

urlpatterns = [
    # Main dashboard view for displaying machine details and processes
    path('dashboard/', MachineProcessDashboardView.as_view(), name='dashboard'),
    
    # HTMX endpoint to dynamically load the processes table
    # This is called by hx-get from the main dashboard template
    path('dashboard/table/', MachineProcessTablePartialView.as_view(), name='dashboard_table'),

    # URL pattern for when a user clicks on a 'Process' link
    # This mimics the ASP.NET GridView RowCommand redirect
    path('dashboard/select-process/<int:process_id>/machine/<int:machine_id>/', 
         ProcessSelectionRedirectView.as_view(), name='process_select_redirect'),
         
    # URL pattern for the 'Cancel' button
    # This mimics the ASP.NET btnCancel_Click redirect
    path('dashboard/cancel/', CancelRedirectView.as_view(), name='cancel_redirect'),

    # Placeholder URLs for the redirect targets.
    # In a real migration, these would point to the actual Django views
    # for 'Schedule_Dashboard.aspx' and 'Schedule_Machine_Dashboard.aspx'.
    path('schedule-dashboard/', TemplateView.as_view(template_name='dummy_page.html'), name='schedule_dashboard'),
    path('schedule-machine-dashboard/', TemplateView.as_view(template_name='dummy_page.html'), name='schedule_machine_dashboard'),
]
```
*(Note: A `dummy_page.html` would be a simple HTML file in your project's `templates` directory, e.g., `templates/dummy_page.html`, containing just an `<h1>Dummy Page</h1>` to satisfy the placeholder URL.)*

#### 4.6 Tests

**Task:** Write comprehensive unit tests for the model methods and integration tests for all views to ensure functionality and maintainability.

**Instructions:**
Tests will cover the data retrieval logic within models and the rendering/redirection behavior of views, including mocking session data for accurate simulation of the ASP.NET environment.

**File: `machinery_process/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch # Used for mocking external calls
from .models import DGItemMaster, MSMaster, MSProcess, PlnProcessMaster

class MachineryProcessModelTest(TestCase):
    """
    Unit tests for the model methods that encapsulate business logic.
    Ensures data retrieval and formatting are correct.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for managed=False models directly in the database
        # These reflect hypothetical entries in your existing SQL Server database.
        cls.comp_id = 1
        cls.fin_year_id = 2023

        # Create DGItemMaster (Machine definitions)
        cls.machine_alpha = DGItemMaster.objects.create(
            id=101, manf_desc='Machine Alpha', comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )
        cls.machine_beta = DGItemMaster.objects.create(
            id=102, manf_desc='Machine Beta', comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )
        cls.machine_gamma_old_finyear = DGItemMaster.objects.create(
            id=103, manf_desc='Machine Gamma (Old FY)', comp_id=cls.comp_id, fin_year_id=cls.fin_year_id - 1
        )
        cls.machine_delta_diff_comp = DGItemMaster.objects.create(
            id=104, manf_desc='Machine Delta (Diff Comp)', comp_id=cls.comp_id + 1, fin_year_id=cls.fin_year_id
        )

        # Create MSMaster (Machine configurations)
        cls.ms_master_alpha = MSMaster.objects.create(id=1, item=cls.machine_alpha)
        cls.ms_master_beta = MSMaster.objects.create(id=2, item=cls.machine_beta)
        cls.ms_master_gamma = MSMaster.objects.create(id=3, item=cls.machine_gamma_old_finyear)


        # Create PlnProcessMaster (Process definitions)
        cls.process_cutting = PlnProcessMaster.objects.create(id=1001, process_name='Cutting')
        cls.process_drilling = PlnProcessMaster.objects.create(id=1002, process_name='Drilling')
        cls.process_welding = PlnProcessMaster.objects.create(id=1003, process_name='Welding')
        cls.process_painting = PlnProcessMaster.objects.create(id=1004, process_name='Painting')

        # Create MSProcess (Machine Process Associations)
        MSProcess.objects.create(mid=cls.ms_master_alpha, pid=cls.process_cutting)
        MSProcess.objects.create(mid=cls.ms_master_alpha, pid=cls.process_drilling)
        MSProcess.objects.create(mid=cls.ms_master_beta, pid=cls.process_welding)
        MSProcess.objects.create(mid=cls.ms_master_gamma, pid=cls.process_cutting) # Old FY machine with a process

    def test_get_machine_name_success(self):
        """Test retrieving a valid machine name."""
        name = MSProcess.get_machine_name(self.machine_alpha.id, self.comp_id, self.fin_year_id)
        self.assertEqual(name, 'Machine Alpha')

    def test_get_machine_name_old_fin_year(self):
        """Test retrieving machine name from an older financial year (should still work due to <=)."""
        name = MSProcess.get_machine_name(self.machine_gamma_old_finyear.id, self.comp_id, self.fin_year_id)
        self.assertEqual(name, 'Machine Gamma (Old FY)')

    def test_get_machine_name_non_existent(self):
        """Test retrieving a non-existent machine name."""
        name = MSProcess.get_machine_name(9999, self.comp_id, self.fin_year_id)
        self.assertEqual(name, 'Unknown Machine')

    def test_get_machine_name_wrong_company(self):
        """Test retrieving machine name with incorrect company ID."""
        name = MSProcess.get_machine_name(self.machine_alpha.id, self.comp_id + 1, self.fin_year_id)
        self.assertEqual(name, 'Unknown Machine')

    def test_get_processes_for_machine_multiple_processes(self):
        """Test retrieving multiple processes for a machine."""
        processes = MSProcess.get_processes_for_machine(self.machine_alpha.id, self.comp_id, self.fin_year_id)
        self.assertEqual(len(processes), 2)
        expected_processes = [
            {'Id': self.process_cutting.id, 'MachineId': self.machine_alpha.id, 'Process': 'Cutting'},
            {'Id': self.process_drilling.id, 'MachineId': self.machine_alpha.id, 'Process': 'Drilling'}
        ]
        # Sort lists of dicts for consistent comparison
        self.assertEqual(sorted(processes, key=lambda x: x['Process']), sorted(expected_processes, key=lambda x: x['Process']))

    def test_get_processes_for_machine_single_process(self):
        """Test retrieving a single process for a machine."""
        processes = MSProcess.get_processes_for_machine(self.machine_beta.id, self.comp_id, self.fin_year_id)
        self.assertEqual(len(processes), 1)
        self.assertIn({'Id': self.process_welding.id, 'MachineId': self.machine_beta.id, 'Process': 'Welding'}, processes)

    def test_get_processes_for_machine_old_fin_year(self):
        """Test retrieving processes for a machine from an older financial year."""
        processes = MSProcess.get_processes_for_machine(self.machine_gamma_old_finyear.id, self.comp_id, self.fin_year_id)
        self.assertEqual(len(processes), 1)
        self.assertIn({'Id': self.process_cutting.id, 'MachineId': self.machine_gamma_old_finyear.id, 'Process': 'Cutting'}, processes)

    def test_get_processes_for_machine_non_existent_machine(self):
        """Test retrieving processes for a machine that does not exist."""
        processes = MSProcess.get_processes_for_machine(9999, self.comp_id, self.fin_year_id)
        self.assertEqual(len(processes), 0)

    def test_get_processes_for_machine_no_config_entry(self):
        """Test retrieving processes for a machine with no MSMaster entry."""
        # Create a machine with no MSMaster entry
        machine_no_ms_master = DGItemMaster.objects.create(id=105, manf_desc='Machine No MS Master', comp_id=self.comp_id, fin_year_id=self.fin_year_id)
        processes = MSProcess.get_processes_for_machine(machine_no_ms_master.id, self.comp_id, self.fin_year_id)
        self.assertEqual(len(processes), 0)

    def test_get_processes_for_machine_no_ms_process_entry(self):
        """Test retrieving processes for a machine with MSMaster but no MSProcess entries."""
        # Create a machine and MSMaster but no MSProcess association
        machine_no_ms_process = DGItemMaster.objects.create(id=106, manf_desc='Machine No MS Process', comp_id=self.comp_id, fin_year_id=self.fin_year_id)
        MSMaster.objects.create(id=4, item=machine_no_ms_process)
        processes = MSProcess.get_processes_for_machine(machine_no_ms_process.id, self.comp_id, self.fin_year_id)
        self.assertEqual(len(processes), 0)


class MachineryProcessViewsTest(TestCase):
    """
    Integration tests for the Django views, covering HTTP responses,
    template usage, context data, and redirections.
    """
    def setUp(self):
        self.client = Client()
        # Mock session attributes to simulate ASP.NET's session behavior
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2023
        session.save()

        # Create base test data needed for view interactions
        self.comp_id = 1
        self.fin_year_id = 2023
        self.machine1 = DGItemMaster.objects.create(
            id=101, manf_desc='Test Machine Alpha', comp_id=self.comp_id, fin_year_id=self.fin_year_id
        )
        self.ms_master1 = MSMaster.objects.create(id=1, item=self.machine1)
        self.process_cut = PlnProcessMaster.objects.create(id=1001, process_name='Cutting Process')
        MSProcess.objects.create(mid=self.ms_master1, pid=self.process_cut)

    @patch('machinery_process.models.MSProcess.get_machine_name')
    @patch('machinery_process.models.MSProcess.get_processes_for_machine')
    def test_dashboard_view_get(self, mock_get_processes, mock_get_machine_name):
        """Test the main dashboard view GET request."""
        mock_get_machine_name.return_value = 'Mocked Machine Name'
        mock_get_processes.return_value = [] # Processes loaded via HTMX, so initially empty in main context

        response = self.client.get(reverse('machinery_process:dashboard'), {'Id': self.machine1.id})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_process/dashboard/list.html')
        self.assertContains(response, 'Mocked Machine Name')
        self.assertContains(response, 'Loading Processes...') # HTMX placeholder
        
        mock_get_machine_name.assert_called_with(self.machine1.id, self.comp_id, self.fin_year_id)
        # Note: get_processes_for_machine is not called directly by the main view,
        # but by the partial view loaded via HTMX.

    def test_dashboard_view_no_machine_id(self):
        """Test dashboard view when no machine ID is provided."""
        response = self.client.get(reverse('machinery_process:dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Machine Name: N/A')
        # The partial table will show 'No data to display!'
        
    def test_dashboard_view_invalid_machine_id(self):
        """Test dashboard view when an invalid machine ID is provided."""
        response = self.client.get(reverse('machinery_process:dashboard'), {'Id': 'abc'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Invalid Machine ID provided in URL.')
        self.assertContains(response, 'Machine Name: N/A')

    @patch('machinery_process.models.MSProcess.get_processes_for_machine')
    def test_dashboard_table_partial_view_get(self, mock_get_processes):
        """Test the HTMX-loaded processes table partial view."""
        mock_get_processes.return_value = [
            {'Id': 1001, 'MachineId': self.machine1.id, 'Process': 'Cutting Process'},
            {'Id': 1002, 'MachineId': self.machine1.id, 'Process': 'Drilling Process'}
        ]
        # Simulate an HTMX request by adding the HX-Request header
        response = self.client.get(
            reverse('machinery_process:dashboard_table'), 
            {'Id': self.machine1.id}, 
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_process/dashboard/_processes_table.html')
        self.assertContains(response, 'id="processesTable"') # Check for DataTables ID
        self.assertContains(response, 'Cutting Process')
        self.assertContains(response, 'Drilling Process')
        mock_get_processes.assert_called_with(self.machine1.id, self.comp_id, self.fin_year_id)

    @patch('machinery_process.models.MSProcess.get_processes_for_machine')
    def test_dashboard_table_partial_view_no_data(self, mock_get_processes):
        """Test processes table partial when no data is returned."""
        mock_get_processes.return_value = []
        response = self.client.get(
            reverse('machinery_process:dashboard_table'), 
            {'Id': self.machine1.id}, 
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No data to display !')
        mock_get_processes.assert_called_with(self.machine1.id, self.comp_id, self.fin_year_id)

    def test_process_selection_redirect_view(self):
        """Test the redirection logic for process selection."""
        process_id = 1001
        machine_id = 101
        response = self.client.get(
            reverse('machinery_process:process_select_redirect', args=[process_id, machine_id])
        )
        self.assertEqual(response.status_code, 302) # Expect a redirect
        expected_url = reverse('schedule_dashboard') + f'?ProcessId={process_id}&MachineId={machine_id}&ModId=15&SubModId=70'
        self.assertRedirects(response, expected_url, fetch_redirect_response=False)

    def test_cancel_redirect_view(self):
        """Test the redirection logic for the cancel button."""
        response = self.client.get(reverse('machinery_process:cancel_redirect'))
        self.assertEqual(response.status_code, 302) # Expect a redirect
        expected_url = reverse('schedule_machine_dashboard') + '?ModId=15&SubModId=70'
        self.assertRedirects(response, expected_url, fetch_redirect_response=False)

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
-   **HTMX for Dynamic Content**: The main dashboard page (`list.html`) uses `hx-get` to load the DataTables content from `dashboard_table` URL. `hx-trigger="load"` ensures it loads on page open, and `refreshProcessesList from:body` allows other parts of the application (if any CRUD were added) to trigger a refresh.
-   **DataTables for List Views**: The `_processes_table.html` partial contains the `<table id="processesTable">` and a `<script>` block that initializes jQuery DataTables on that table ID. This provides client-side pagination, searching, and sorting.
-   **Alpine.js for UI State**: A basic Alpine.js `x-data` block is included in `list.html` to demonstrate where component-specific UI state management would reside. No complex Alpine.js logic is strictly necessary for this read-only dashboard, but it's available for future enhancements.
-   **HTMX-only Interactions**: All form submissions (if applicable) and dynamic content loads are designed to use HTMX, eliminating the need for custom, imperative JavaScript. Redirects are also handled with `hx-get` and `hx-swap` on the `cancel` button, ensuring a consistent HTMX-driven flow even for full-page navigation.

### Final Notes

This comprehensive plan provides a clear roadmap for migrating the `Schedule_Process_Dashboard` from ASP.NET to Django. By following these AI-assisted, automation-focused steps, you can achieve a modern, efficient, and easily maintainable application. Remember to replace placeholder values like `[TABLE_NAME]`, `[FIELD_TYPE]`, etc., with actual values derived from your legacy ASP.NET application and database schema. Ensure your `settings.py` is configured to connect to your existing SQL Server database (using `django-mssql-backend` or similar). The unit and integration tests are crucial for verifying the correctness of the migration and ensuring the stability of your new Django application.