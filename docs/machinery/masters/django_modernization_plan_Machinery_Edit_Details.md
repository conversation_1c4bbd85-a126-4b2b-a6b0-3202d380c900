This comprehensive Django modernization plan outlines the strategic transition from your legacy ASP.NET application to a robust, maintainable, and modern Django solution. We will leverage AI-assisted automation to streamline the conversion process, focusing on business value and actionable steps.

### ASP.NET to Django Conversion Script:

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code interacts with multiple database tables, primarily:

*   `tblMS_Master`: This is the main table for "Machinery" details.
*   `tblDG_Item_Master`: Contains details about items, including those that can be machinery or spares.
*   `Unit_Master`: Stores Unit of Measurement (UOM) symbols.
*   `tblHR_OfficeStaff`: Holds employee details (used for "Incharge").
*   `tblMM_Supplier_master`: Stores supplier information.
*   `tblPln_Process_Master`: Defines master processes.
*   `tblMS_Process`: Links machines to processes.
*   `tblMS_Spares`: Links machines to spare items with quantities.
*   `tblDG_Category_Master`: For item categories.
*   `tblDG_SubCategory_Master`: For item subcategories.
*   `tblDG_Location_Master`: For item locations.

**Identified Tables and Key Columns:**

*   **[TABLE_NAME]: `tblMS_Master`** (Primary Entity: Machine)
    *   `Id` (Primary Key, implicitly `pk` in Django)
    *   `CompId` (Company ID)
    *   `FinYearId` (Financial Year ID)
    *   `ItemId` (Foreign Key to `tblDG_Item_Master`)
    *   `Model` (String)
    *   `PurchaseDate` (Date)
    *   `LifeDate` (Date)
    *   `Puttouse` (Date)
    *   `WarrantyExpiryDate` (Date)
    *   `ReceivedDate` (Date)
    *   `Insurance` (Integer: 0 for No, 1 for Yes)
    *   `InsuranceExpiryDate` (Date, nullable)
    *   `Cost` (Decimal/Float)
    *   `Incharge` (Foreign Key to `tblHR_OfficeStaff` EmpId)
    *   `Make` (String)
    *   `Capacity` (String)
    *   `SupplierName` (Foreign Key to `tblMM_Supplier_master` SupplierId)
    *   `Location` (Integer/String, likely Foreign Key to `tblDG_Location_Master` Id)
    *   `PMDays` (Preventive Maintenance in Days, Integer)
    *   `SysDate`, `SysTime`, `SessionId` (System fields, typically handled differently in Django)
    *   `FileName`, `FileSize`, `ContentType`, `FileData` (For file uploads)

*   **[TABLE_NAME]: `tblDG_Item_Master`** (Auxiliary Entity: Item)
    *   `Id` (Primary Key)
    *   `ItemCode` (String)
    *   `ManfDesc` (String - Manufacturer Description/Name)
    *   `UOMBasic` (Foreign Key to `Unit_Master` Id)
    *   `CId` (Foreign Key to `tblDG_Category_Master` CId)
    *   `SCId` (Foreign Key to `tblDG_SubCategory_Master` SCId)
    *   `StockQty` (Decimal/Integer)
    *   `Location` (Foreign Key to `tblDG_Location_Master` Id)
    *   `Absolute` (Boolean/Integer)
    *   `CompId` (Company ID)

*   **[TABLE_NAME]: `Unit_Master`** (Auxiliary Entity: Unit)
    *   `Id` (Primary Key)
    *   `Symbol` (String)

*   **[TABLE_NAME]: `tblHR_OfficeStaff`** (Auxiliary Entity: Employee)
    *   `EmpId` (Primary Key)
    *   `EmployeeName` (String)
    *   `CompId` (Company ID)

*   **[TABLE_NAME]: `tblMM_Supplier_master`** (Auxiliary Entity: Supplier)
    *   `SupplierId` (Primary Key)
    *   `SupplierName` (String)
    *   `CompId` (Company ID)

*   **[TABLE_NAME]: `tblPln_Process_Master`** (Auxiliary Entity: ProcessMaster)
    *   `Id` (Primary Key)
    *   `Symbol` (String)
    *   `ProcessName` (String)

*   **[TABLE_NAME]: `tblMS_Process`** (Intermediate Entity: MachineProcess)
    *   `Id` (Primary Key)
    *   `MId` (Foreign Key to `tblMS_Master` Id)
    *   `PId` (Foreign Key to `tblPln_Process_Master` Id)

*   **[TABLE_NAME]: `tblMS_Spares`** (Intermediate Entity: MachineSpare)
    *   `Id` (Primary Key)
    *   `MId` (Foreign Key to `tblMS_Master` Id)
    *   `ItemId` (Foreign Key to `tblDG_Item_Master` Id)
    *   `Qty` (Decimal/Float)

*   **[TABLE_NAME]: `tblDG_Category_Master`** (Auxiliary Entity: Category)
    *   `CId` (Primary Key)
    *   `CName` (String)

*   **[TABLE_NAME]: `tblDG_SubCategory_Master`** (Auxiliary Entity: SubCategory)
    *   `SCId` (Primary Key)
    *   `SCName` (String)
    *   `CId` (Foreign Key to `tblDG_Category_Master` CId)

*   **[TABLE_NAME]: `tblDG_Location_Master`** (Auxiliary Entity: Location)
    *   `Id` (Primary Key)
    *   `LocationLabel` (String)
    *   `LocationNo` (String)

*   **Note on Temporary Tables (`tblMS_Process_Temp`, `tblMS_Spares_Temp`):** These temporary tables are used in the ASP.NET code to stage new additions before the main `btnProceed` (Update) action. In Django with HTMX, we will handle these additions and deletions directly on the `MachineProcess` and `MachineSpare` models using HTMX requests, eliminating the need for separate `_Temp` models or complex session management for temporary data. This simplifies the backend and makes the UI more reactive.

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic in the ASP.NET code.

**Analysis:**

*   **Read (Load Data):**
    *   Initial load (`Page_Load`): Retrieves primary `tblDG_Item_Master` details (ItemCode, ManfDesc, UOM) for the `itemId` passed in the query string.
    *   Populates main `tblMS_Master` fields (Model, Dates, Cost, Make, Capacity, Incharge, Supplier, Location, PMDays, Insurance status, File details).
    *   `fillProcess()`: Loads all available processes from `tblPln_Process_Master` into `GridView1`.
    *   `Fillgridview()`: Loads available `tblDG_Item_Master` items (spares) based on category/subcategory/search into `GridView2`.
    *   `LoadProcessMaster()`: Loads existing processes associated with the current `Machine` from `tblMS_Process` into `GridView6`.
    *   `LoadDataSpareMaster()`: Loads existing spares associated with the current `Machine` from `tblMS_Spares` into `GridView5`.
    *   `LoadProcess()`: Loads processes from `tblMS_Process_Temp` into `GridView4` (for new additions in current session).
    *   `LoadDataSpare()`: Loads spares from `tblMS_Spares_Temp` into `GridView3` (for new additions in current session).

*   **Update (Edit/Save):**
    *   `btnProceed_Click`: This is the primary save action.
        *   Updates the main `tblMS_Master` record with all form field values.
        *   Handles file upload (`FileUpload1`) for the machine image.
        *   Copies all records from `tblMS_Process_Temp` to `tblMS_Process` (associating them with the `tblMS_Master` ID).
        *   Copies all records from `tblMS_Spares_Temp` to `tblMS_Spares`.
        *   Clears `tblMS_Process_Temp` and `tblMS_Spares_Temp`.
        *   Redirects to a list page upon success.

*   **Create (Add related items, implicitly):**
    *   `btnProcessAdd_Click`: Inserts selected processes from `GridView1` into `tblMS_Process_Temp`.
    *   `btnSpareAdd_Click`: Inserts selected spares (with quantities) from `GridView2` into `tblMS_Spares_Temp`.

*   **Delete (Remove related items):**
    *   `GridView4_RowCommand` (delete): Deletes a process from `tblMS_Process_Temp`.
    *   `GridView3_RowCommand` (delete): Deletes a spare from `tblMS_Spares_Temp`.
    *   `GridView6_RowCommand` (delete): Deletes an *existing* process from `tblMS_Process`.
    *   `GridView5_RowCommand` (delete): Deletes an *existing* spare from `tblMS_Spares`.
    *   `ImageButton1_Click`: Deletes the uploaded file from `tblMS_Master` (sets fields to NULL).

*   **Validation:**
    *   `RequiredFieldValidator`, `RegularExpressionValidator`: Client-side and server-side validation for mandatory fields and date/numeric formats.
    *   `ValidateTextBox()`: Programmatically makes `ReqQty` visible for spares.
    *   Business logic: Checks if at least one process and one spare is selected before `btnProceed_Click`.

*   **Dynamic UI (AjaxControlToolkit):**
    *   `TabContainer`: Manages different sections of the form.
    *   `UpdatePanel`: Allows partial page updates.
    *   `CalendarExtender`: Date picker for date inputs.
    *   `AutoCompleteExtender`: Provides auto-completion for Supplier Name and Incharge fields.
    *   `RadiobtnInsurance_SelectedIndexChanged` / `EnableDisableInsurance()`: Shows/hides Insurance Expiry Date field based on radio button selection.
    *   `DrpCategory_SelectedIndexChanged`, `DrpSubCategory_SelectedIndexChanged`, `btnSearch_Click`: Filter/search logic for available spares.

**Modernization Strategy (Django with HTMX/Alpine.js):**

1.  **Main Machine Form:** A Django `UpdateView` will handle the primary `Machine` model details. Form fields will map to Django form fields, with custom validators for regex patterns and date formats.
2.  **Related Items (Processes & Spares):**
    *   The "temporary" table logic (`_Temp` suffix) will be replaced by direct manipulation of the `MachineProcess` and `MachineSpare` tables via HTMX. When a user "adds" an item, an HTMX request will create/delete the relationship directly. This makes the UI immediately consistent with the database state.
    *   The lists of "Available" items (`GridView1`, `GridView2`) and "Associated/Master" items (`GridView6`, `GridView5`) will be rendered as HTMX partials. Actions (add/delete) will trigger HTMX requests to endpoints that modify the relationships, then re-render the affected partials.
3.  **Dynamic UI:**
    *   `TabContainer` will be implemented using HTMX tabs, loading content on demand.
    *   `UpdatePanel` functionality is natively handled by HTMX's partial updates.
    *   `CalendarExtender`: HTML5 `type="date"` input fields will be used. For specific `dd-MM-yyyy` format, Django forms can handle input parsing and output formatting.
    *   `AutoCompleteExtender`: Custom HTMX endpoints will provide search suggestions, with Alpine.js managing the display of the dropdown.
    *   `RadiobtnInsurance_SelectedIndexChanged`: Alpine.js will manage the visibility of the "Insurance Expires on" field based on the radio button selection.
4.  **File Upload/Delete:** Django's `FileField` handles uploads. A separate HTMX endpoint will be created for the "delete image" action.
5.  **Validation:** Django forms provide robust validation. Custom `clean` methods in the form or `form_valid` in the view will handle complex validation rules (e.g., "at least one process/spare required").
6.  **DataTables:** All `GridView` instances will be rendered as standard HTML tables and then enhanced with DataTables.js for client-side pagination, searching, and sorting.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis of UI Controls and Django/HTMX Mapping:**

*   **Main Page Layout:**
    *   `MasterPageFile="~/MasterPage.master"`: Maps to `{% extends 'core/base.html' %}`.
    *   `cc1:TabContainer ID="TabContainer1"`: Will be a set of HTML `div` elements, with HTMX `hx-get` attributes on tab headers to load content into a target `div`. Alpine.js can manage active tab state.
    *   `asp:UpdatePanel ID="Up" UpdateMode="Conditional"`: Replaced by targeted HTMX updates (e.g., `hx-swap="outerHTML"`, `hx-target`).

*   **Machine Details Tab (TabPanel1):**
    *   `lblItemCode`, `lblunit`, `lblManfDesc`: Displayed via Django template variables.
    *   `txtModel`, `txtMake`, `txtCapacity`, `txtCost`, `txtLocation`, `txtPreMaintInDays`: Django `forms.TextInput` with Tailwind CSS classes.
    *   `txtPurchaseDate`, `txtLifeDate`, `txtPutToUse`, `txtWarrantyExpireson`, `txtReceivedDate`, `txtInsuranceExpiresOn`: Django `forms.DateInput(attrs={'type': 'date'})` with `format` set in settings/form for `dd-MM-yyyy`.
    *   `RadiobtnInsurance`: Django `forms.RadioSelect`. Alpine.js will control visibility of `txtInsuranceExpiresOn`.
    *   `txtSupplierName`, `txtIncharge`: Django `forms.TextInput`. HTMX + Alpine.js will provide auto-completion.
    *   `FileUpload1`: Django `forms.FileField`.
    *   `lbldownload`, `ImageButton1`: Django template logic for displaying link/button based on file presence. `ImageButton1` will be an HTMX button for deletion.
    *   `RequiredFieldValidator`, `RegularExpressionValidator`: Handled by Django form validation and regex validators.

*   **Functions Tab (TabPanel2):**
    *   `GridView1` (Process selection): HTML table with checkboxes. `hx-post` on "Add" button sends selected IDs to an endpoint.
    *   `GridView4` (Temp Process list): HTML table. Each row will have an HTMX `hx-delete` link for removal.
    *   `GridView6` (Process Master list): HTML table. Each row will have an HTMX `hx-delete` link for removal.
    *   `btnProcessAdd`: HTMX button to submit selected processes.
    *   `lblprocessmsg`: Django messages framework will show errors, or Alpine.js can control visibility.

*   **Spare Tab (TabPanel3):**
    *   `DrpCategory`, `DrpSubCategory`, `DrpSearchCode`, `txtSearchItemCode`, `btnSearch`: Django `forms.Select`, `forms.TextInput`, `forms.Button`. HTMX `hx-get` to a search endpoint to update `GridView2`.
    *   `GridView2` (Spare selection): HTML table with checkboxes and quantity input. `hx-post` on "Add" button.
    *   `GridView3` (Temp Spare list): HTML table. Each row will have an HTMX `hx-delete` link.
    *   `GridView5` (Spare Master list): HTML table. Each row will have an HTMX `hx-delete` link.
    *   `btnSpareAdd`: HTMX button to submit selected spares.
    *   `lblsparemsg`: Django messages framework or Alpine.js.

*   **Action Buttons:**
    *   `btnProceed` (Update): Main form submit button.
    *   `btnCancel`: Redirect to main list page (Django `HttpResponseRedirect` or `reverse_lazy`).

### Step 4: Generate Django Code

**Application Name:** `machinery`

This will reside in `machinery/` directory within your Django project.

#### 4.1 Models (`machinery/models.py`)

```python
from django.db import models
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
import os

# Helper to validate date formats if needed, though forms should handle it.
def validate_date_format(value):
    # This might not be strictly needed if using DateInput with format in forms
    # but good to have if direct model creation bypasses form validation
    if value is None:
        return
    if not isinstance(value, (models.DateField, models.DateTimeField)):
        try:
            # Attempt to parse to date to ensure it's valid, format 'dd-MM-yyyy' for display
            value.strftime('%d-%m-%Y')
        except ValueError:
            raise ValidationError(
                _('%(value)s is not a valid date in DD-MM-YYYY format.'),
                params={'value': value},
            )

class Company(models.Model):
    # Placeholder for Company, assuming it's a separate master table
    # This model is inferred from CompId usage, assuming it's a FK.
    # Replace with actual fields if you have a real tblCompany_Master.
    id = models.IntegerField(db_column='CompId', primary_key=True)
    name = models.CharField(db_column='CompName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblCompany_Master' # Example table name
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name

class FinancialYear(models.Model):
    # Placeholder for FinancialYear, assuming it's a separate master table
    id = models.IntegerField(db_column='FinYearId', primary_key=True)
    year = models.CharField(db_column='FinYear', max_length=9)

    class Meta:
        managed = False
        db_table = 'tblFinancialYear_Master' # Example table name
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.year

class Unit(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class Employee(models.Model):
    empid = models.IntegerField(db_column='EmpId', primary_key=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='employees') # Assuming CompId links to Company
    
    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.employee_name} [{self.empid}]"

class Supplier(models.Model):
    supplierid = models.IntegerField(db_column='SupplierId', primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='suppliers') # Assuming CompId links to Company

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return f"{self.supplier_name} [{self.supplierid}]"

class Category(models.Model):
    cid = models.IntegerField(db_column='CId', primary_key=True)
    cname = models.CharField(db_column='CName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'

    def __str__(self):
        return self.cname

class SubCategory(models.Model):
    scid = models.IntegerField(db_column='SCId', primary_key=True)
    scname = models.CharField(db_column='SCName', max_length=255)
    category = models.ForeignKey(Category, on_delete=models.DO_NOTHING, db_column='CId', related_name='subcategories')

    class Meta:
        managed = False
        db_table = 'tblDG_SubCategory_Master'
        verbose_name = 'SubCategory'
        verbose_name_plural = 'SubCategories'

    def __str__(self):
        return f"{self.category.symbol} - {self.scname}" # Assuming category has a symbol like the original code

class Location(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    location_label = models.CharField(db_column='LocationLabel', max_length=100)
    location_no = models.CharField(db_column='LocationNo', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblDG_Location_Master'
        verbose_name = 'Location'
        verbose_name_plural = 'Locations'

    def __str__(self):
        return f"{self.location_label}-{self.location_no}"

class Item(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255) # Manufacturer Description
    uom_basic = models.ForeignKey(Unit, on_delete=models.DO_NOTHING, db_column='UOMBasic', related_name='items')
    category = models.ForeignKey(Category, on_delete=models.DO_NOTHING, db_column='CId', related_name='items')
    subcategory = models.ForeignKey(SubCategory, on_delete=models.DO_NOTHING, db_column='SCId', related_name='items', null=True, blank=True)
    stock_qty = models.DecimalField(db_column='StockQty', max_digits=10, decimal_places=2, null=True, blank=True)
    location = models.ForeignKey(Location, on_delete=models.DO_NOTHING, db_column='Location', related_name='items', null=True, blank=True)
    absolute = models.IntegerField(db_column='Absolute', default=0) # Typically 0 or 1 for boolean
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='items')

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.manf_desc

class ProcessMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, null=True, blank=True)
    process_name = models.CharField(db_column='ProcessName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblPln_Process_Master'
        verbose_name = 'Process Master'
        verbose_name_plural = 'Process Masters'

    def __str__(self):
        if self.symbol and self.symbol != '0':
            return f"[{self.symbol}] {self.process_name}"
        return self.process_name


class Machine(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='machines')
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='machines')
    item = models.ForeignKey(Item, on_delete=models.DO_NOTHING, db_column='ItemId', related_name='machinery_items') # The Item record this machine is based on
    model = models.CharField(db_column='Model', max_length=100)
    purchase_date = models.DateField(db_column='PurchaseDate', validators=[validate_date_format])
    life_date = models.DateField(db_column='LifeDate', validators=[validate_date_format])
    put_to_use_date = models.DateField(db_column='Puttouse', validators=[validate_date_format])
    warranty_expiry_date = models.DateField(db_column='WarrantyExpiryDate', validators=[validate_date_format])
    received_date = models.DateField(db_column='ReceivedDate', validators=[validate_date_format])
    insurance = models.IntegerField(db_column='Insurance', choices=[(0, 'NO'), (1, 'YES')], default=0)
    insurance_expiry_date = models.DateField(db_column='InsuranceExpiryDate', null=True, blank=True, validators=[validate_date_format])
    cost = models.DecimalField(db_column='Cost', max_digits=18, decimal_places=2) # Check typical currency precision
    incharge = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='Incharge', related_name='machines_incharge', null=True, blank=True)
    make = models.CharField(db_column='Make', max_length=100)
    capacity = models.CharField(db_column='Capacity', max_length=100)
    supplier = models.ForeignKey(Supplier, on_delete=models.DO_NOTHING, db_column='SupplierName', related_name='machines_supplied', null=True, blank=True) # Check if SupplierName stores ID or Name
    location = models.CharField(db_column='Location', max_length=255, null=True, blank=True) # Original was int, but UI implies text input, so keep as char for now or map to FK to Location
    pm_days = models.IntegerField(db_column='PMDays') # Preventive Maintenance Days
    
    # File upload fields
    file_name = models.CharField(db_column='FileName', max_length=255, null=True, blank=True)
    file_size = models.BigIntegerField(db_column='FileSize', null=True, blank=True)
    content_type = models.CharField(db_column='ContentType', max_length=255, null=True, blank=True)
    file_data = models.BinaryField(db_column='FileData', null=True, blank=True) # For storing binary data (files)

    sys_date = models.DateField(db_column='SysDate', auto_now_add=True) # Django handles this automatically
    sys_time = models.TimeField(db_column='SysTime', auto_now_add=True) # Django handles this automatically
    session_id = models.CharField(db_column='SessionId', max_length=255, null=True, blank=True) # Can be managed by Django sessions

    class Meta:
        managed = False # Django will not manage this table's schema
        db_table = 'tblMS_Master'
        verbose_name = 'Machine'
        verbose_name_plural = 'Machines'

    def __str__(self):
        return f"Machine: {self.make} {self.model} (Item: {self.item.manf_desc})"

    def delete_file(self):
        """Deletes the associated file data."""
        self.file_name = None
        self.file_size = None
        self.content_type = None
        self.file_data = None
        self.save()

    @property
    def has_image(self):
        return self.file_name and self.file_data

    def is_valid_for_proceed(self):
        """
        Business logic to check if required related items are present before proceeding.
        Maps to the ASP.NET check for 'Atleast one process/spare is required'.
        """
        if not self.processes.exists():
            raise ValidationError('At least one process is required for Machinery.')
        if not self.spares.exists():
            raise ValidationError('At least one spare is required for Machinery.')
        return True


class MachineProcess(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    machine = models.ForeignKey(Machine, on_delete=models.CASCADE, db_column='MId', related_name='processes')
    process = models.ForeignKey(ProcessMaster, on_delete=models.CASCADE, db_column='PId', related_name='machine_links')

    class Meta:
        managed = False
        db_table = 'tblMS_Process'
        verbose_name = 'Machine Process'
        verbose_name_plural = 'Machine Processes'
        unique_together = ('machine', 'process') # Prevents duplicate machine-process links

    def __str__(self):
        return f"{self.machine.item.manf_desc} - {self.process.process_name}"

class MachineSpare(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    machine = models.ForeignKey(Machine, on_delete=models.CASCADE, db_column='MId', related_name='spares')
    item = models.ForeignKey(Item, on_delete=models.CASCADE, db_column='ItemId', related_name='machine_spares_links')
    quantity = models.DecimalField(db_column='Qty', max_digits=10, decimal_places=2)

    class Meta:
        managed = False
        db_table = 'tblMS_Spares'
        verbose_name = 'Machine Spare'
        verbose_name_plural = 'Machine Spares'
        unique_together = ('machine', 'item') # Prevents duplicate machine-item links

    def __str__(self):
        return f"{self.item.manf_desc} ({self.quantity} {self.item.uom_basic.symbol})"

```

#### 4.2 Forms (`machinery/forms.py`)

```python
from django import forms
from django.core.validators import RegexValidator
from django.forms.widgets import NumberInput
from .models import Machine, Employee, Supplier, Item, Category, SubCategory, Location
from django.utils.translation import gettext_lazy as _

class DateInput(forms.DateInput):
    input_type = 'date'
    # Adding a custom format for display, though 'date' type handles it for browser UI
    format = '%Y-%m-%d' # ISO 8601 for HTML5 date input

class MachineForm(forms.ModelForm):
    # These fields are part of the main machine details, but are displayed as labels
    # in the ASP.NET code, often derived from the 'item' foreign key.
    # We will make them read-only or display them directly from the Machine.item instance.
    # For form submission, we only need editable fields.

    # Regex validators for date format and numeric fields, as in ASP.NET
    # Note: HTML5 type="date" and forms.DecimalField handle much of this natively.
    # These are mostly for server-side validation on forms.TextInput if type="text" was used.
    # Here, for DateField and DecimalField, Django's default validation is often sufficient.
    # The ASP.NET regexes for dates were: ^([1-9]|0[1-9]|[12][0-9]|3[01])[- /.]([1-9]|0[1-9]|1[012])[- /.][0-9]{4}$
    # and for numbers: ^[0-9]\d*(\.\d+)?$
    # We'll rely on Django's DateField and DecimalField for type validation and apply specific regex for format if needed.
    # However, since we're using type="date", the browser's date picker will enforce format.
    # Server-side, Django's DateField handles parsing.

    # Re-adding RegexValidator for Cost and PMDays if they were text inputs in ASP.NET,
    # ensuring they only contain numbers (including decimals for cost).
    # For Django DecimalField, `min_value` and `max_value` are also good.
    cost = forms.DecimalField(
        max_digits=18,
        decimal_places=2,
        required=True,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        validators=[RegexValidator(r"^[0-9]\d*(\.\d+)?$", message="Enter a valid numeric value.")]
    )
    pm_days = forms.IntegerField(
        required=True,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        validators=[RegexValidator(r"^[0-9]\d*$", message="Enter a valid integer value.")]
    )

    # FileField needs to be explicitly defined if it's not a direct model field in the form
    # but rather handled as a separate field or if it's binary in DB.
    # For binary data, we often handle file upload in view.
    # If the file_name field is populated from the uploaded file's name and file_data stores the actual content:
    uploaded_file = forms.FileField(required=False, label="Upload Image", widget=forms.FileInput(attrs={'class': 'block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 focus:outline-none'}))


    class Meta:
        model = Machine
        fields = [
            'model', 'make', 'capacity', 'purchase_date', 'life_date',
            'put_to_use_date', 'warranty_expiry_date', 'received_date',
            'insurance', 'insurance_expiry_date', 'cost', 'supplier',
            'incharge', 'location', 'pm_days'
        ]
        # Note: 'item', 'company', 'financial_year' are not editable via this form,
        # they are set on creation or are fixed for the machine instance.
        
        widgets = {
            'model': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'make': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'capacity': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'purchase_date': DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'life_date': DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'put_to_use_date': DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'warranty_expiry_date': DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'received_date': DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'insurance': forms.RadioSelect(choices=Machine.insurance.field.choices, attrs={'class': 'ml-2'}), # Tailwind for radio button group
            'insurance_expiry_date': DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            # For ForeignKey fields like supplier and incharge, we need to map to text inputs for auto-complete.
            # We'll use a custom field or override clean method to resolve text to FK ID.
            # For now, let's keep them as Select for simplicity unless custom fields are explicitly requested.
            'supplier': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'incharge': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'location': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate supplier and incharge dropdowns based on current company.
        # This assumes company is available in request or session.
        # For simplicity, we'll populate all, but in a real app, filter by company.
        self.fields['supplier'].queryset = Supplier.objects.all()
        self.fields['incharge'].queryset = Employee.objects.all()

        # Initial state for insurance expiry date
        if self.instance and not self.instance.insurance: # If insurance is 'NO' (0)
            self.fields['insurance_expiry_date'].widget.attrs['x-show'] = 'insurance_selected == "1"'
            self.fields['insurance_expiry_date'].required = False
        else:
             self.fields['insurance_expiry_date'].widget.attrs['x-show'] = 'insurance_selected == "1"'
             self.fields['insurance_expiry_date'].required = True

    def clean(self):
        cleaned_data = super().clean()
        insurance_selected = cleaned_data.get('insurance')
        insurance_expiry_date = cleaned_data.get('insurance_expiry_date')

        if insurance_selected == 1 and not insurance_expiry_date:
            self.add_error('insurance_expiry_date', _('Insurance Expiry Date is required when Insurance is selected.'))
        
        # Handle file upload separately or here, since it's a temp field.
        # For now, let's process it in the view.
        return cleaned_data

class MachineSpareAddForm(forms.Form):
    # Form for adding a spare to the temporary list in the UI
    # This is not a ModelForm because it's for a single row in the selection grid.
    item_id = forms.IntegerField(widget=forms.HiddenInput())
    quantity = forms.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        min_value=0.01,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-24 text-right', # Matches original ASP.NET styling attempts
            'placeholder': 'Qty',
            'x-mask:dynamic': '$money($clean($event.target.value))', # Alpine.js masking
        }),
        validators=[RegexValidator(r"^[0-9]\d*(\.\d+)?$", message="Enter a valid numeric quantity.")]
    )

    def clean_quantity(self):
        quantity = self.cleaned_data['quantity']
        if quantity <= 0:
            raise forms.ValidationError("Quantity must be greater than zero.")
        return quantity

class ProcessSearchForm(forms.Form):
    # This form is for the filter options on the "Spare" tab, not for processes itself.
    # It dynamically filters the `GridView2` (spare selection)
    category = forms.ModelChoiceField(
        queryset=Category.objects.all(),
        required=False,
        empty_label="Select Category",
        widget=forms.Select(attrs={'class': 'box3'}),
    )
    sub_category = forms.ModelChoiceField(
        queryset=SubCategory.objects.none(), # Populated dynamically via HTMX
        required=False,
        empty_label="Select SubCategory",
        widget=forms.Select(attrs={'class': 'box3', 'hx-get': "{% url 'machinery:get_subcategories' %}", 'hx-target': '#id_sub_category', 'hx-swap': 'outerHTML', 'hx-trigger': 'change from:#id_category'}),
    )
    search_by = forms.ChoiceField(
        choices=[
            ('Select', 'Select'),
            ('ItemCode', 'Item Code'),
            ('ManfDesc', 'Description')
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'box3'})
    )
    search_text = forms.CharField(
        max_length=200, 
        required=False, 
        widget=forms.TextInput(attrs={'class': 'box3', 'placeholder': 'Search text'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if 'category' in self.data:
            try:
                category_id = int(self.data.get('category'))
                self.fields['sub_category'].queryset = SubCategory.objects.filter(category__cid=category_id).order_by('scname')
            except (ValueError, TypeError):
                pass  # invalid input in category field
        elif self.instance and self.instance.category: # For pre-populating on initial load if needed
             self.fields['sub_category'].queryset = SubCategory.objects.filter(category=self.instance.category).order_by('scname')

```

#### 4.3 Views (`machinery/views.py`)

```python
from django.views.generic import (
    TemplateView, UpdateView, DeleteView, ListView, View
)
from django.urls import reverse_lazy, reverse
from django.contrib import messages
from django.http import HttpResponse, JsonResponse, Http404
from django.shortcuts import get_object_or_404, render
from django.utils import timezone
from datetime import datetime
import os
import io

from .models import (
    Machine, Item, Employee, Supplier, ProcessMaster,
    MachineProcess, MachineSpare, Category, SubCategory, Unit, Location, Company, FinancialYear
)
from .forms import MachineForm, MachineSpareAddForm, ProcessSearchForm

# Mocking current user's company and financial year for demonstration
# In a real application, these would come from authentication context
MOCK_COMP_ID = 1
MOCK_FIN_YEAR_ID = 1

class MachineryEditDetailsView(UpdateView):
    model = Machine
    form_class = MachineForm
    template_name = 'machinery/machine_edit_details.html'
    context_object_name = 'machine'

    def get_success_url(self):
        # Redirect to a generic list page after successful update
        messages.success(self.request, 'Machinery details updated successfully.')
        return reverse_lazy('machinery_list_page') # Assuming you have a list page for machinery

    def get_object(self, queryset=None):
        # Get machine object based on 'Id' from query string as in ASP.NET
        item_id = self.request.GET.get('Id')
        if not item_id:
            raise Http404("Machine ID (Id) not provided in query string.")
        
        # Original ASP.NET code retrieves machine based on item_id, compid, and finyearid
        # We find the Machine (tblMS_Master) linked to this ItemId
        master_obj = get_object_or_404(
            Machine, 
            item__id=item_id, 
            company__id=MOCK_COMP_ID, 
            financial_year__id=MOCK_FIN_YEAR_ID # Assuming FinYearId is always <= current
        )
        return master_obj

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        machine = context['machine'] # The Machine instance being edited

        # Details from associated Item Master
        # This maps to lblItemCode, lblunit, lblManfDesc
        context['item_details'] = machine.item
        context['item_uom'] = machine.item.uom_basic.symbol

        # Initial data for the form (optional, ModelForm usually handles this)
        # context['initial_data_for_form'] = { ... }

        # Processes and Spares search/master forms
        context['process_search_form'] = ProcessSearchForm(initial={'category': Category.objects.first()}) # Pre-select first category
        context['spare_search_form'] = ProcessSearchForm(self.request.GET or None) # Re-using for spare search for now, will need specific one.
        
        # Control tab index on load if 'm' query param is present (as in ASP.NET)
        if self.request.GET.get('m') == '1':
            context['active_tab_index'] = 1 # Functions tab
        else:
            context['active_tab_index'] = 0 # Default to Machine tab

        # Set x-data for Alpine.js for insurance visibility
        context['alpine_data'] = {
            'insurance_selected': str(machine.insurance), # Convert to string for Alpine radio bind
        }
        
        return context

    def form_valid(self, form):
        machine = form.save(commit=False)
        
        # Handle file upload as in ASP.NET code-behind
        uploaded_file = form.cleaned_data.get('uploaded_file')
        if uploaded_file:
            machine.file_name = uploaded_file.name
            machine.file_size = uploaded_file.size
            machine.content_type = uploaded_file.content_type
            machine.file_data = uploaded_file.read() # Reads binary data
        
        # Update system fields (SysDate, SysTime, SessionId)
        machine.sys_date = timezone.localdate()
        machine.sys_time = timezone.localtime().time()
        machine.session_id = self.request.session.session_key

        # Validate required related items (processes and spares)
        try:
            machine.is_valid_for_proceed() # Fat model approach: logic in model
        except ValidationError as e:
            messages.error(self.request, e.message)
            return self.form_invalid(form) # Re-render form with errors

        machine.save() # Save the main machine object

        # The ASP.NET logic with _Temp tables is bypassed here.
        # Instead, MachineProcess and MachineSpare are managed via HTMX actions
        # directly in their respective partial views.
        
        response = super().form_valid(form)
        if self.request.headers.get('HX-Request'):
            # If HTMX request, respond with a trigger to close modal or refresh page
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPage' # Custom trigger to refresh the whole page or redirect
                }
            )
        return response

    def form_invalid(self, form):
        # Add error messages to Django messages framework
        for field, errors in form.errors.items():
            for error in errors:
                messages.error(self.request, f"{form.fields[field].label}: {error}")
        
        # If it's an HTMX request, re-render the form content
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, self.get_context_data(form=form))
        return super().form_invalid(form)

# HTMX PARTIAL VIEWS AND ACTIONS

# --- Machine Details Tab Partials (for HTMX content loading) ---
class MachineDetailsFormPartial(UpdateView):
    model = Machine
    form_class = MachineForm
    template_name = 'machinery/_machine_details_form.html' # This is a partial
    context_object_name = 'machine'

    def get_object(self, queryset=None):
        item_id = self.kwargs.get('item_id') # Item ID from URL for partial
        return get_object_or_404(
            Machine, 
            item__id=item_id, 
            company__id=MOCK_COMP_ID, 
            financial_year__id=MOCK_FIN_YEAR_ID
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        machine = context['machine']
        context['item_details'] = machine.item
        context['item_uom'] = machine.item.uom_basic.symbol
        
        context['alpine_data'] = {
            'insurance_selected': str(machine.insurance),
        }
        return context

    def form_valid(self, form):
        machine = form.save(commit=False)
        uploaded_file = form.cleaned_data.get('uploaded_file')
        if uploaded_file:
            machine.file_name = uploaded_file.name
            machine.file_size = uploaded_file.size
            machine.content_type = uploaded_file.content_type
            file_stream = io.BytesIO()
            for chunk in uploaded_file.chunks():
                file_stream.write(chunk)
            machine.file_data = file_stream.getvalue()

        machine.sys_date = timezone.localdate()
        machine.sys_time = timezone.localtime().time()
        machine.session_id = self.request.session.session_key
        
        # Validate required related items (processes and spares)
        try:
            machine.is_valid_for_proceed() # Fat model approach: logic in model
        except ValidationError as e:
            messages.error(self.request, e.message)
            return render(self.request, self.template_name, self.get_context_data(form=form)) # Re-render form with errors
        
        machine.save()
        messages.success(self.request, 'Machinery details updated successfully.')
        return HttpResponse(
            status=204,
            headers={
                'HX-Trigger': 'refreshPage', # Trigger for full page reload or list refresh
            }
        )
    
    def form_invalid(self, form):
        # Re-render the form partial with errors for HTMX
        return render(self.request, self.template_name, self.get_context_data(form=form))


class MachineFileDeleteView(DeleteView):
    model = Machine
    success_url = reverse_lazy('machinery_list_page') # Redirect after file delete
    
    def get_object(self, queryset=None):
        item_id = self.kwargs.get('item_id') # Item ID from URL
        return get_object_or_404(
            Machine, 
            item__id=item_id, 
            company__id=MOCK_COMP_ID, 
            financial_year__id=MOCK_FIN_YEAR_ID
        )

    def post(self, request, *args, **kwargs):
        self.object = self.get_object()
        self.object.delete_file() # Call model method to clear file fields
        messages.success(self.request, 'Machine image deleted successfully.')
        return HttpResponse(
            status=204, # No content, indicates success without navigating
            headers={
                'HX-Trigger': 'refreshMachineDetails' # Trigger to refresh the form partial
            }
        )

# --- Processes Tab Partials and Actions ---
class ProcessTabPartial(TemplateView):
    template_name = 'machinery/_process_tab_content.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        machine_id = self.kwargs['pk'] # Machine ID from URL
        machine = get_object_or_404(Machine, pk=machine_id)
        
        context['machine'] = machine
        context['all_processes'] = ProcessMaster.objects.filter(symbol__isnull=False).exclude(symbol='0').order_by('process_name')
        context['machine_processes'] = machine.processes.all().order_by('process__process_name') # Associated processes
        
        return context

class AddProcessToMachine(View):
    def post(self, request, pk):
        machine = get_object_or_404(Machine, pk=pk)
        process_ids = request.POST.getlist('selected_processes') # Get list of checked process IDs

        added_count = 0
        for pid in process_ids:
            process_master = get_object_or_404(ProcessMaster, pk=pid)
            # Create or get_or_create the MachineProcess relationship
            _, created = MachineProcess.objects.get_or_create(
                machine=machine, 
                process=process_master
            )
            if created:
                added_count += 1
        
        if added_count > 0:
            messages.success(request, f"{added_count} process(es) added.")
        else:
            messages.info(request, "No new processes selected or already added.")
            
        # Re-render the process tab content via HTMX
        return render(request, 'machinery/_process_tab_content.html', {
            'machine': machine,
            'all_processes': ProcessMaster.objects.filter(symbol__isnull=False).exclude(symbol='0').order_by('process_name'),
            'machine_processes': machine.processes.all().order_by('process__process_name'),
        })

class RemoveProcessFromMachine(DeleteView):
    model = MachineProcess
    
    def get_object(self, queryset=None):
        # Get MachineProcess by its ID, ensuring it belongs to the correct machine
        machine_id = self.kwargs['machine_pk']
        process_id = self.kwargs['pk'] # This pk is MachineProcess.id
        return get_object_or_404(MachineProcess, pk=process_id, machine__pk=machine_id)

    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        # Validation: Ensure at least one process remains if this is the last one
        if self.object.machine.processes.count() <= 1:
            messages.error(request, 'At least one process is required for Machinery.')
            # Re-render the process tab to show the error and prevent deletion
            return render(request, 'machinery/_process_tab_content.html', {
                'machine': self.object.machine,
                'all_processes': ProcessMaster.objects.filter(symbol__isnull=False).exclude(symbol='0').order_by('process_name'),
                'machine_processes': self.object.machine.processes.all().order_by('process__process_name'),
            })

        self.object.delete()
        messages.success(request, 'Process removed successfully.')
        
        # Re-render the process tab content via HTMX
        machine = get_object_or_404(Machine, pk=self.object.machine.pk)
        return render(request, 'machinery/_process_tab_content.html', {
            'machine': machine,
            'all_processes': ProcessMaster.objects.filter(symbol__isnull=False).exclude(symbol='0').order_by('process_name'),
            'machine_processes': machine.processes.all().order_by('process__process_name'),
        })

# --- Spares Tab Partials and Actions ---
class SparesTabPartial(TemplateView):
    template_name = 'machinery/_spares_tab_content.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        machine_id = self.kwargs['pk'] # Machine ID from URL
        machine = get_object_or_404(Machine, pk=machine_id)
        
        context['machine'] = machine
        context['search_form'] = ProcessSearchForm(self.request.GET or None) # Using ProcessSearchForm for item filtering
        
        # Filter available items based on search form
        available_items_queryset = Item.objects.filter(
            company__id=MOCK_COMP_ID, 
            absolute=0 # Not absolute items
        ).exclude(pk=machine.item.pk).order_by('manf_desc') # Exclude the machine item itself

        if context['search_form'].is_valid():
            category = context['search_form'].cleaned_data.get('category')
            sub_category = context['search_form'].cleaned_data.get('sub_category')
            search_by = context['search_form'].cleaned_data.get('search_by')
            search_text = context['search_form'].cleaned_data.get('search_text')

            if category:
                available_items_queryset = available_items_queryset.filter(category=category)
            if sub_category:
                available_items_queryset = available_items_queryset.filter(subcategory=sub_category)
            
            if search_text and search_by and search_by != 'Select':
                if search_by == 'ItemCode':
                    available_items_queryset = available_items_queryset.filter(item_code__icontains=search_text)
                elif search_by == 'ManfDesc':
                    available_items_queryset = available_items_queryset.filter(manf_desc__icontains=search_text)

        context['available_items'] = available_items_queryset
        context['machine_spares'] = machine.spares.all().order_by('item__manf_desc') # Associated spares

        # Prepare forms for each available item for quantity input
        item_forms = []
        for item in context['available_items']:
            initial_qty = 0
            existing_spare = machine.spares.filter(item=item).first()
            if existing_spare:
                initial_qty = existing_spare.quantity
            item_forms.append({
                'item': item,
                'form': MachineSpareAddForm(initial={'item_id': item.pk, 'quantity': initial_qty})
            })
        context['item_forms'] = item_forms
        
        return context

class AddSpareToMachine(View):
    def post(self, request, pk):
        machine = get_object_or_404(Machine, pk=pk)
        
        # Process multiple items submitted from the grid
        items_to_add = []
        for key, value in request.POST.items():
            if key.startswith('item_id_') and request.POST.get(f'chkSpare_{value}') == 'on':
                item_id = value
                qty_key = f'quantity_{item_id}' # Assuming 'quantity_ITEMID' for the quantity field
                quantity_value = request.POST.get(qty_key)
                
                form = MachineSpareAddForm(data={'item_id': item_id, 'quantity': quantity_value})
                if form.is_valid():
                    items_to_add.append(form.cleaned_data)
                else:
                    # If any form is invalid, re-render the whole page or send specific error for HTMX
                    messages.error(request, f"Error for Item ID {item_id}: {form.errors.as_text()}")
                    return render(request, 'machinery/_spares_tab_content.html', self.get_context_data(pk=pk)) # Re-render with errors

        added_count = 0
        updated_count = 0
        for data in items_to_add:
            item = get_object_or_404(Item, pk=data['item_id'])
            # Use update_or_create to handle existing spares or create new ones
            machine_spare, created = MachineSpare.objects.update_or_create(
                machine=machine,
                item=item,
                defaults={'quantity': data['quantity']}
            )
            if created:
                added_count += 1
            else:
                updated_count += 1
        
        if added_count > 0 or updated_count > 0:
            messages.success(request, f"{added_count} spare(s) added, {updated_count} updated.")
        else:
            messages.info(request, "No new spares selected or quantities changed.")
            
        # Re-render the spares tab content via HTMX
        return render(request, 'machinery/_spares_tab_content.html', self.get_context_data(pk=pk))

    def get_context_data(self, pk): # Helper to re-use context generation for re-rendering
        context = {}
        machine = get_object_or_404(Machine, pk=pk)
        context['machine'] = machine
        context['search_form'] = ProcessSearchForm(self.request.GET or None)

        available_items_queryset = Item.objects.filter(
            company__id=MOCK_COMP_ID, 
            absolute=0 
        ).exclude(pk=machine.item.pk).order_by('manf_desc') 

        if context['search_form'].is_valid():
            category = context['search_form'].cleaned_data.get('category')
            sub_category = context['search_form'].cleaned_data.get('sub_category')
            search_by = context['search_form'].cleaned_data.get('search_by')
            search_text = context['search_form'].cleaned_data.get('search_text')

            if category:
                available_items_queryset = available_items_queryset.filter(category=category)
            if sub_category:
                available_items_queryset = available_items_queryset.filter(subcategory=sub_category)
            if search_text and search_by and search_by != 'Select':
                if search_by == 'ItemCode':
                    available_items_queryset = available_items_queryset.filter(item_code__icontains=search_text)
                elif search_by == 'ManfDesc':
                    available_items_queryset = available_items_queryset.filter(manf_desc__icontains=search_text)

        context['available_items'] = available_items_queryset
        context['machine_spares'] = machine.spares.all().order_by('item__manf_desc')

        item_forms = []
        for item in context['available_items']:
            initial_qty = 0
            existing_spare = machine.spares.filter(item=item).first()
            if existing_spare:
                initial_qty = existing_spare.quantity
            item_forms.append({
                'item': item,
                'form': MachineSpareAddForm(initial={'item_id': item.pk, 'quantity': initial_qty})
            })
        context['item_forms'] = item_forms
        return context

class RemoveSpareFromMachine(DeleteView):
    model = MachineSpare
    
    def get_object(self, queryset=None):
        # Get MachineSpare by its ID, ensuring it belongs to the correct machine
        machine_id = self.kwargs['machine_pk']
        spare_pk = self.kwargs['pk'] # This pk is MachineSpare.id
        return get_object_or_404(MachineSpare, pk=spare_pk, machine__pk=machine_id)

    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        # Validation: Ensure at least one spare remains if this is the last one
        if self.object.machine.spares.count() <= 1:
            messages.error(request, 'At least one spare is required for Machinery.')
            # Re-render the spares tab to show the error and prevent deletion
            return render(request, 'machinery/_spares_tab_content.html', {
                'machine': self.object.machine,
                'search_form': ProcessSearchForm(request.GET or None), # Need to pass request.GET for search form
                'available_items': Item.objects.filter(company__id=MOCK_COMP_ID, absolute=0).exclude(pk=self.object.machine.item.pk).order_by('manf_desc'),
                'machine_spares': self.object.machine.spares.all().order_by('item__manf_desc'),
            })

        self.object.delete()
        messages.success(request, 'Spare removed successfully.')
        
        # Re-render the spares tab content via HTMX
        machine = get_object_or_404(Machine, pk=self.object.machine.pk)
        return render(request, 'machinery/_spares_tab_content.html', {
            'machine': machine,
            'search_form': ProcessSearchForm(request.GET or None),
            'available_items': Item.objects.filter(company__id=MOCK_COMP_ID, absolute=0).exclude(pk=machine.item.pk).order_by('manf_desc'),
            'machine_spares': machine.spares.all().order_by('item__manf_desc'),
        })

# --- Auto-complete / Dynamic Dropdown Endpoints ---
class SupplierAutocomplete(View):
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        # Filter suppliers by company, replace MOCK_COMP_ID with actual user company ID
        suppliers = Supplier.objects.filter(
            supplier_name__icontains=query, 
            company__id=MOCK_COMP_ID
        ).order_by('supplier_name')[:10] # Limit to 10 suggestions

        results = [{'id': s.supplierid, 'text': str(s)} for s in suppliers]
        return JsonResponse(results, safe=False)

class EmployeeAutocomplete(View):
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        # Filter employees by company
        employees = Employee.objects.filter(
            employee_name__icontains=query, 
            company__id=MOCK_COMP_ID
        ).order_by('employee_name')[:10]

        results = [{'id': e.empid, 'text': str(e)} for e in employees]
        return JsonResponse(results, safe=False)

class GetSubcategoriesView(View):
    def get(self, request, *args, **kwargs):
        category_id = request.GET.get('category')
        subcategories = SubCategory.objects.none()
        if category_id:
            try:
                subcategories = SubCategory.objects.filter(category__cid=category_id).order_by('scname')
            except ValueError:
                pass
        
        # Render a new select input for subcategories
        return render(request, 'machinery/_subcategory_dropdown.html', {'subcategories': subcategories})

# Placeholder for the list page to redirect to
class MachineryListView(ListView):
    model = Machine
    template_name = 'machinery/machinery_list.html' # Create a simple list template for redirect target
    context_object_name = 'machines'

```

#### 4.4 Templates (`machinery/templates/machinery/`)

**`machinery/machine_edit_details.html` (Main Page Template)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Machinery - Edit</h2>
    </div>

    <div x-data="{ active_tab_index: {{ active_tab_index|default:0 }}, insurance_selected: '{{ alpine_data.insurance_selected }}' }">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <a href="#" 
                   @click.prevent="active_tab_index = 0" 
                   :class="{'border-indigo-500 text-indigo-600': active_tab_index === 0, 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700': active_tab_index !== 0}" 
                   class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Machine
                </a>
                <a href="#" 
                   @click.prevent="active_tab_index = 1" 
                   hx-get="{% url 'machinery:machine_processes_tab' machine.pk %}" 
                   hx-target="#tab-content-1" 
                   hx-trigger="click once" 
                   :class="{'border-indigo-500 text-indigo-600': active_tab_index === 1, 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700': active_tab_index !== 1}" 
                   class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Functions
                </a>
                <a href="#" 
                   @click.prevent="active_tab_index = 2" 
                   hx-get="{% url 'machinery:machine_spares_tab' machine.pk %}" 
                   hx-target="#tab-content-2" 
                   hx-trigger="click once" 
                   :class="{'border-indigo-500 text-indigo-600': active_tab_index === 2, 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700': active_tab_index !== 2}" 
                   class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Spare
                </a>
            </nav>
        </div>

        <div id="tab-content-0" x-show="active_tab_index === 0" class="mt-6">
            {% include 'machinery/_machine_details_form.html' %}
        </div>

        <div id="tab-content-1" x-show="active_tab_index === 1" class="mt-6">
            <!-- Content loaded via HTMX -->
            <div class="text-center p-8">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading Processes...</p>
            </div>
        </div>

        <div id="tab-content-2" x-show="active_tab_index === 2" class="mt-6">
            <!-- Content loaded via HTMX -->
            <div class="text-center p-8">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading Spares...</p>
            </div>
        </div>
    </div>
</div>

<script>
    // HTMX listener to refresh page after main form update
    document.body.addEventListener('refreshPage', function(evt) {
        window.location.href = "{% url 'machinery_list_page' %}"; // Redirect to list page
    });
</script>
{% endblock %}

{% block extra_js %}
<!-- Add any specific Alpine.js or other JS needed for this page -->
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components specific to this page
    });
</script>
{% endblock %}
```

**`machinery/_machine_details_form.html` (Partial for Machine Details Tab)**

```html
<div class="bg-white p-6 rounded-lg shadow">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Machine Details</h3>
    <form hx-post="{% url 'machinery:machine_details_form' machine.item.pk %}" hx-target="#tab-content-0" hx-swap="outerHTML">
        {% csrf_token %}
        <fieldset>
            <table class="w-full text-sm">
                <tr>
                    <td class="w-1/6 font-semibold py-2">Machine Code</td>
                    <td class="w-1/4 py-2">: <span class="font-bold text-sm">{{ item_details.item_code }}</span></td>
                    <td class="w-1/6 py-2">UOM</td>
                    <td class="w-1/4 py-2">: <span class="font-bold">{{ item_uom }}</span></td>
                </tr>
                <tr>
                    <td class="font-semibold py-2">Name</td>
                    <td colspan="3" class="py-2">: {{ item_details.manf_desc }}</td>
                </tr>
                <tr>
                    <td class="font-semibold py-2">Model</td>
                    <td class="py-2">: {{ form.model }} {% if form.model.errors %}<span class="text-red-500 text-xs">{{ form.model.errors|striptags }}</span>{% endif %}</td>
                    <td class="font-semibold py-2">Make</td>
                    <td class="py-2">: {{ form.make }} {% if form.make.errors %}<span class="text-red-500 text-xs">{{ form.make.errors|striptags }}</span>{% endif %}</td>
                </tr>
                <tr>
                    <td class="font-semibold py-2">Purchase Date</td>
                    <td class="py-2">: {{ form.purchase_date }} {% if form.purchase_date.errors %}<span class="text-red-500 text-xs">{{ form.purchase_date.errors|striptags }}</span>{% endif %}</td>
                    <td class="font-semibold py-2">Capacity</td>
                    <td class="py-2">: {{ form.capacity }} {% if form.capacity.errors %}<span class="text-red-500 text-xs">{{ form.capacity.errors|striptags }}</span>{% endif %}</td>
                </tr>
                <tr>
                    <td class="font-semibold py-2">Cost</td>
                    <td class="py-2">: {{ form.cost }} {% if form.cost.errors %}<span class="text-red-500 text-xs">{{ form.cost.errors|striptags }}</span>{% endif %}</td>
                    <td class="font-semibold py-2">Supplier Name</td>
                    <td class="py-2">: 
                        <input type="text" 
                               name="{{ form.supplier.name }}" 
                               id="{{ form.supplier.id_for_label }}" 
                               class="box3 w-64 {% if form.supplier.errors %}border-red-500{% endif %}"
                               value="{{ form.supplier.value|default:'' }}"
                               hx-get="{% url 'machinery:supplier_autocomplete' %}"
                               hx-trigger="keyup changed delay:500ms"
                               hx-target="#supplier-suggestions"
                               hx-indicator=".htmx-indicator"
                               autocomplete="off"
                               placeholder="Start typing supplier name..."
                               x-data="{ selectedSupplierId: '{{ form.supplier.value|default:'' }}' }"
                               @input="selectedSupplierId = ''">
                        <input type="hidden" name="supplier" x-model="selectedSupplierId">
                        <div id="supplier-suggestions" 
                            class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg"
                            x-show="selectedSupplierId == ''">
                            <!-- Suggestions loaded here -->
                        </div>
                        {% if form.supplier.errors %}<span class="text-red-500 text-xs">{{ form.supplier.errors|striptags }}</span>{% endif %}
                    </td>
                </tr>
                <tr>
                    <td class="font-semibold py-2">Life Date</td>
                    <td class="py-2">: {{ form.life_date }} {% if form.life_date.errors %}<span class="text-red-500 text-xs">{{ form.life_date.errors|striptags }}</span>{% endif %}</td>
                    <td class="font-semibold py-2">Warranty Expires on</td>
                    <td class="py-2">: {{ form.warranty_expiry_date }} {% if form.warranty_expiry_date.errors %}<span class="text-red-500 text-xs">{{ form.warranty_expiry_date.errors|striptags }}</span>{% endif %}</td>
                </tr>
                <tr>
                    <td class="font-semibold py-2">Insurance</td>
                    <td class="py-2">
                        <div x-model="insurance_selected" class="flex space-x-4">
                            {% for radio in form.insurance %}
                                <label class="inline-flex items-center">
                                    {{ radio.tag }}
                                    <span class="ml-2 text-gray-700">{{ radio.choice_label }}</span>
                                </label>
                            {% endfor %}
                        </div>
                    </td>
                    <td class="font-semibold py-2">
                        <label x-show="insurance_selected == '1'">Insurance Expires on</label>
                    </td>
                    <td class="py-2">
                        <span x-show="insurance_selected == '1'">: </span>
                        <div x-show="insurance_selected == '1'">
                            {{ form.insurance_expiry_date }}
                            {% if form.insurance_expiry_date.errors %}<span class="text-red-500 text-xs">{{ form.insurance_expiry_date.errors|striptags }}</span>{% endif %}
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="font-semibold py-2">Put to use</td>
                    <td class="py-2">: {{ form.put_to_use_date }} {% if form.put_to_use_date.errors %}<span class="text-red-500 text-xs">{{ form.put_to_use_date.errors|striptags }}</span>{% endif %}</td>
                    <td class="font-semibold py-2">Received Date</td>
                    <td class="py-2">: {{ form.received_date }} {% if form.received_date.errors %}<span class="text-red-500 text-xs">{{ form.received_date.errors|striptags }}</span>{% endif %}</td>
                </tr>
                <tr>
                    <td class="font-semibold py-2">Incharge</td>
                    <td class="py-2">: 
                        <input type="text" 
                               name="{{ form.incharge.name }}" 
                               id="{{ form.incharge.id_for_label }}" 
                               class="box3 w-64 {% if form.incharge.errors %}border-red-500{% endif %}"
                               value="{{ form.incharge.value|default:'' }}"
                               hx-get="{% url 'machinery:employee_autocomplete' %}"
                               hx-trigger="keyup changed delay:500ms"
                               hx-target="#incharge-suggestions"
                               hx-indicator=".htmx-indicator"
                               autocomplete="off"
                               placeholder="Start typing employee name..."
                               x-data="{ selectedEmployeeId: '{{ form.incharge.value|default:'' }}' }"
                               @input="selectedEmployeeId = ''">
                        <input type="hidden" name="incharge" x-model="selectedEmployeeId">
                        <div id="incharge-suggestions" 
                            class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg"
                            x-show="selectedEmployeeId == ''">
                            <!-- Suggestions loaded here -->
                        </div>
                        {% if form.incharge.errors %}<span class="text-red-500 text-xs">{{ form.incharge.errors|striptags }}</span>{% endif %}
                    </td>
                    <td class="font-semibold py-2">Location</td>
                    <td class="py-2">: {{ form.location }} {% if form.location.errors %}<span class="text-red-500 text-xs">{{ form.location.errors|striptags }}</span>{% endif %}</td>
                </tr>
                <tr>
                    <td class="font-semibold py-2" valign="bottom">Upload Image</td>
                    <td class="py-2" valign="bottom">
                        : 
                        {% if machine.has_image %}
                            <a href="#" class="text-blue-600 hover:underline">{{ machine.file_name }}</a>
                            <button type="button" 
                                class="bg-red-500 hover:bg-red-700 text-white font-bold py-0.5 px-1 rounded-full text-xs"
                                hx-post="{% url 'machinery:machine_file_delete' machine.item.pk %}"
                                hx-confirm="Are you sure you want to delete this image?"
                                hx-target="#tab-content-0" hx-swap="outerHTML">
                                X
                            </button>
                        {% else %}
                            {{ form.uploaded_file }}
                        {% endif %}
                        {% if form.uploaded_file.errors %}<span class="text-red-500 text-xs">{{ form.uploaded_file.errors|striptags }}</span>{% endif %}
                    </td>
                    <td class="font-semibold py-2">Preventive Maintenance in days</td>
                    <td class="py-2">: {{ form.pm_days }} {% if form.pm_days.errors %}<span class="text-red-500 text-xs">{{ form.pm_days.errors|striptags }}</span>{% endif %}</td>
                </tr>
            </table>
        </fieldset>
        
        <div class="mt-6 flex justify-end space-x-4">
            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Update
            </button>
            <a href="{% url 'machinery_list_page' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                Cancel
            </a>
        </div>
    </form>
</div>

<script>
    // Alpine.js logic for auto-complete suggestions
    function selectSuggestion(id, text, inputId, hiddenInputName) {
        document.getElementById(inputId).value = text;
        document.querySelector(`input[name="${hiddenInputName}"]`).value = id;
        document.getElementById(`${inputId.replace('id_', '')}-suggestions`).innerHTML = ''; // Clear suggestions
    }

    // Auto-complete suggestion rendering logic
    document.body.addEventListener('htmx:afterOnLoad', function(evt) {
        if (evt.detail.target.id === 'supplier-suggestions' || evt.detail.target.id === 'incharge-suggestions') {
            const suggestions = JSON.parse(evt.detail.xhr.responseText);
            let html = '';
            const inputId = evt.detail.target.id.replace('-suggestions', ''); // e.g., 'id_supplier'
            const hiddenInputName = inputId.replace('id_', ''); // e.g., 'supplier'
            suggestions.forEach(s => {
                html += `<div class="px-4 py-2 hover:bg-gray-100 cursor-pointer" 
                             onclick="selectSuggestion('${s.id}', '${s.text}', '${inputId}', '${hiddenInputName}')">${s.text}</div>`;
            });
            evt.detail.target.innerHTML = html;
        }
    });

    // Custom HTMX event listener for refreshing main details form after file deletion
    document.body.addEventListener('refreshMachineDetails', function(evt) {
        const targetElement = document.getElementById('tab-content-0');
        if (targetElement) {
            htmx.trigger(targetElement, 'refresh'); // Trigger a refresh on the form container
        }
    });
</script>
```

**`machinery/_process_tab_content.html` (Partial for Functions Tab)**

```html
<div class="bg-white p-6 rounded-lg shadow" 
     hx-target="this" hx-swap="outerHTML">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">Available Processes</h3>
            <form hx-post="{% url 'machinery:add_process' machine.pk %}" 
                  hx-target="#process-tab-content" 
                  hx-swap="outerHTML">
                {% csrf_token %}
                <table id="allProcessesTable" class="min-w-full bg-white border border-gray-300 datatable">
                    <thead>
                        <tr>
                            <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase">SN</th>
                            <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-500 uppercase">Select</th>
                            <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase">Process</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for process in all_processes %}
                        <tr>
                            <td class="py-2 px-4 border-b">{{ forloop.counter }}</td>
                            <td class="py-2 px-4 border-b text-center">
                                <input type="checkbox" name="selected_processes" value="{{ process.pk }}" class="form-checkbox h-4 w-4 text-blue-600">
                            </td>
                            <td class="py-2 px-4 border-b">{{ process.process_name }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="3" class="py-4 text-center text-gray-500">No processes available.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                <div class="mt-4 text-center">
                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Add Selected
                    </button>
                </div>
            </form>
        </div>

        <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">Associated Processes</h3>
            <table id="machineProcessesTable" class="min-w-full bg-white border border-gray-300 datatable">
                <thead>
                    <tr>
                        <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase">SN</th>
                        <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-500 uppercase">Action</th>
                        <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase">Process</th>
                    </tr>
                </thead>
                <tbody>
                    {% for machine_process in machine_processes %}
                    <tr>
                        <td class="py-2 px-4 border-b">{{ forloop.counter }}</td>
                        <td class="py-2 px-4 border-b text-center">
                            <button type="button" 
                                class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs"
                                hx-delete="{% url 'machinery:remove_process' machine.pk machine_process.pk %}"
                                hx-confirm="Are you sure you want to remove this process?"
                                hx-target="#process-tab-content"
                                hx-swap="outerHTML">
                                Delete
                            </button>
                        </td>
                        <td class="py-2 px-4 border-b">{{ machine_process.process.process_name }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="3" class="py-4 text-center text-gray-500">No processes associated.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% if messages %}
                <div class="mt-4">
                    {% for message in messages %}
                        <div class="p-3 {% if message.tags %}bg-red-100 text-red-700{% else %}bg-green-100 text-green-700{% endif %} rounded-md" role="alert">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
            {% if not machine_processes %}
                <p class="mt-4 text-red-500">* Atleast one process is required for Machinery.</p>
            {% endif %}
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('.datatable').DataTable({
        "paging": true,
        "searching": true,
        "info": false,
        "lengthChange": false,
        "pageLength": 10
    });
});
</script>
```

**`machinery/_spares_tab_content.html` (Partial for Spares Tab)**

```html
<div class="bg-white p-6 rounded-lg shadow"
     hx-target="this" hx-swap="outerHTML">
    <div class="flex items-center space-x-4 mb-4">
        <form hx-get="{% url 'machinery:machine_spares_tab' machine.pk %}" 
              hx-target="#spare-tab-content" 
              hx-swap="outerHTML" 
              class="flex items-center space-x-4 w-full">
            {{ search_form.category.label_tag }} {{ search_form.category }}
            <span id="id_sub_category_container">
                {{ search_form.sub_category.label_tag }} {{ search_form.sub_category }}
            </span>
            {{ search_form.search_by.label_tag }} {{ search_form.search_by }}
            {{ search_form.search_text }}
            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Search</button>
        </form>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">Available Spares</h3>
            <form hx-post="{% url 'machinery:add_spare' machine.pk %}" 
                  hx-target="#spare-tab-content" 
                  hx-swap="outerHTML">
                {% csrf_token %}
                <table id="availableSparesTable" class="min-w-full bg-white border border-gray-300 datatable">
                    <thead>
                        <tr>
                            <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase">SN</th>
                            <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-500 uppercase">Select</th>
                            <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase">Item Code</th>
                            <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                            <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-500 uppercase">UOM</th>
                            <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase">Qty</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item_form_pair in item_forms %}
                        {% with item=item_form_pair.item form=item_form_pair.form %}
                        <tr>
                            <td class="py-2 px-4 border-b">{{ forloop.counter }}</td>
                            <td class="py-2 px-4 border-b text-center">
                                <input type="checkbox" name="chkSpare_{{ item.pk }}" class="form-checkbox h-4 w-4 text-blue-600">
                                <input type="hidden" name="item_id_{{ item.pk }}" value="{{ item.pk }}">
                            </td>
                            <td class="py-2 px-4 border-b">{{ item.item_code }}</td>
                            <td class="py-2 px-4 border-b">{{ item.manf_desc }}</td>
                            <td class="py-2 px-4 border-b text-center">{{ item.uom_basic.symbol }}</td>
                            <td class="py-2 px-4 border-b">
                                <input type="text" 
                                       name="quantity_{{ item.pk }}" 
                                       id="id_quantity_{{ item.pk }}" 
                                       value="{{ form.quantity.value|default:'' }}"
                                       class="box3 w-24 text-right"
                                       placeholder="Qty">
                                {% if form.quantity.errors %}<span class="text-red-500 text-xs">{{ form.quantity.errors|striptags }}</span>{% endif %}
                            </td>
                        </tr>
                        {% endwith %}
                        {% empty %}
                        <tr>
                            <td colspan="6" class="py-4 text-center text-gray-500">No spares available.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                <div class="mt-4 text-center">
                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Add Selected
                    </button>
                </div>
            </form>
        </div>

        <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">Associated Spares</h3>
            <table id="machineSparesTable" class="min-w-full bg-white border border-gray-300 datatable">
                <thead>
                    <tr>
                        <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase">SN</th>
                        <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-500 uppercase">Action</th>
                        <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase">Item Code</th>
                        <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                        <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-500 uppercase">UOM</th>
                        <th class="py-2 px-4 border-b text-right text-xs font-medium text-gray-500 uppercase">Quantity</th>
                    </tr>
                </thead>
                <tbody>
                    {% for machine_spare in machine_spares %}
                    <tr>
                        <td class="py-2 px-4 border-b">{{ forloop.counter }}</td>
                        <td class="py-2 px-4 border-b text-center">
                            <button type="button" 
                                class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs"
                                hx-delete="{% url 'machinery:remove_spare' machine.pk machine_spare.pk %}"
                                hx-confirm="Are you sure you want to remove this spare?"
                                hx-target="#spare-tab-content"
                                hx-swap="outerHTML">
                                Delete
                            </button>
                        </td>
                        <td class="py-2 px-4 border-b">{{ machine_spare.item.item_code }}</td>
                        <td class="py-2 px-4 border-b">{{ machine_spare.item.manf_desc }}</td>
                        <td class="py-2 px-4 border-b text-center">{{ machine_spare.item.uom_basic.symbol }}</td>
                        <td class="py-2 px-4 border-b text-right">{{ machine_spare.quantity|floatformat:"2" }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="py-4 text-center text-gray-500">No spares associated.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% if messages %}
                <div class="mt-4">
                    {% for message in messages %}
                        <div class="p-3 {% if message.tags %}bg-red-100 text-red-700{% else %}bg-green-100 text-green-700{% endif %} rounded-md" role="alert">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
            {% if not machine_spares %}
                <p class="mt-4 text-red-500">* Atleast one spare is required for Machinery.</p>
            {% endif %}
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('.datatable').DataTable({
        "paging": true,
        "searching": true,
        "info": false,
        "lengthChange": false,
        "pageLength": 10
    });
});
</script>
```

**`machinery/_subcategory_dropdown.html` (Partial for Subcategory Dropdown)**

```html
<label for="id_sub_category">SubCategory:</label> 
<select name="sub_category" id="id_sub_category" class="box3">
    <option value="">Select SubCategory</option>
    {% for subcategory in subcategories %}
        <option value="{{ subcategory.scid }}">{{ subcategory.scname }}</option>
    {% endfor %}
</select>
```

**`machinery/machinery_list.html` (Placeholder List Page)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6">Machinery List</h2>
    
    {% if messages %}
        <div class="mb-4">
            {% for message in messages %}
                <div class="p-3 {% if message.tags %}bg-{{ message.tags }}-100 text-{{ message.tags }}-700{% else %}bg-green-100 text-green-700{% endif %} rounded-md" role="alert">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <table id="machineryListTable" class="min-w-full bg-white border border-gray-300 datatable">
        <thead>
            <tr>
                <th class="py-2 px-4 border-b">SN</th>
                <th class="py-2 px-4 border-b">Machine Make</th>
                <th class="py-2 px-4 border-b">Model</th>
                <th class="py-2 px-4 border-b">Item Name</th>
                <th class="py-2 px-4 border-b">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for machine in machines %}
            <tr>
                <td class="py-2 px-4 border-b">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b">{{ machine.make }}</td>
                <td class="py-2 px-4 border-b">{{ machine.model }}</td>
                <td class="py-2 px-4 border-b">{{ machine.item.manf_desc }}</td>
                <td class="py-2 px-4 border-b">
                    <a href="{% url 'machinery:machine_edit' Id=machine.item.pk %}" class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded text-xs">Edit</a>
                    <!-- Add Delete if needed here -->
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5" class="py-4 text-center text-gray-500">No machinery records found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
$(document).ready(function() {
    $('#machineryListTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
    });
});
</script>
{% endblock %}

```

#### 4.5 URLs (`machinery/urls.py`)

```python
from django.urls import path
from .views import (
    MachineryEditDetailsView, MachineryListView, 
    MachineDetailsFormPartial, MachineFileDeleteView,
    ProcessTabPartial, AddProcessToMachine, RemoveProcessFromMachine,
    SparesTabPartial, AddSpareToMachine, RemoveSpareFromMachine,
    SupplierAutocomplete, EmployeeAutocomplete, GetSubcategoriesView
)

app_name = 'machinery' # Namespace for URLs

urlpatterns = [
    # Main Edit/Detail View (uses Item.Id from query string as in ASP.NET)
    path('machinery-edit/', MachineryEditDetailsView.as_view(), name='machine_edit'),
    
    # Placeholder for the list page redirect
    path('machinery-list/', MachineryListView.as_view(), name='machinery_list_page'),

    # HTMX partials for tabs and specific actions
    path('machine-details-form/<int:item_id>/', MachineDetailsFormPartial.as_view(), name='machine_details_form'),
    path('machine-file-delete/<int:item_id>/', MachineFileDeleteView.as_view(), name='machine_file_delete'),

    # Process Tab URLs
    path('<int:pk>/processes-tab/', ProcessTabPartial.as_view(), name='machine_processes_tab'),
    path('<int:pk>/add-process/', AddProcessToMachine.as_view(), name='add_process'),
    path('<int:machine_pk>/remove-process/<int:pk>/', RemoveProcessFromMachine.as_view(), name='remove_process'),

    # Spares Tab URLs
    path('<int:pk>/spares-tab/', SparesTabPartial.as_view(), name='machine_spares_tab'),
    path('<int:pk>/add-spare/', AddSpareToMachine.as_view(), name='add_spare'),
    path('<int:machine_pk>/remove-spare/<int:pk>/', RemoveSpareFromMachine.as_view(), name='remove_spare'),

    # Auto-complete & Dynamic Dropdown
    path('autocomplete/suppliers/', SupplierAutocomplete.as_view(), name='supplier_autocomplete'),
    path('autocomplete/employees/', EmployeeAutocomplete.as_view(), name='employee_autocomplete'),
    path('get-subcategories/', GetSubcategoriesView.as_view(), name='get_subcategories'),
]

```

#### 4.6 Tests (`machinery/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
import io
import os
from .models import (
    Machine, Item, Employee, Supplier, ProcessMaster,
    MachineProcess, MachineSpare, Unit, Category, SubCategory, Location, Company, FinancialYear
)
from .forms import MachineForm, MachineSpareAddForm, ProcessSearchForm

# Mocking current user's company and financial year for demonstration
# In a real application, these would come from authentication context
MOCK_COMP_ID = 1
MOCK_FIN_YEAR_ID = 1

class MasterDataSetupMixin:
    @classmethod
    def setUpTestData(cls):
        # Create base master data required for all tests
        cls.company = Company.objects.create(id=MOCK_COMP_ID, name='Test Company')
        cls.fin_year = FinancialYear.objects.create(id=MOCK_FIN_YEAR_ID, year='2023-2024')
        cls.unit = Unit.objects.create(id=1, symbol='PCS')
        cls.employee = Employee.objects.create(empid=101, employee_name='John Doe', company=cls.company)
        cls.supplier = Supplier.objects.create(supplierid=201, supplier_name='ABC Suppliers', company=cls.company)
        cls.category = Category.objects.create(cid=1, cname='Machines')
        cls.subcategory = SubCategory.objects.create(scid=1, scname='Heavy Duty', category=cls.category)
        cls.location = Location.objects.create(id=1, location_label='WH', location_no='A1')
        cls.item = Item.objects.create(
            id=1001, 
            item_code='MC001', 
            manf_desc='Excavator', 
            uom_basic=cls.unit, 
            category=cls.category, 
            subcategory=cls.subcategory, 
            company=cls.company,
            stock_qty=1,
            location=cls.location,
            absolute=0
        )
        cls.machine = Machine.objects.create(
            id=1,
            company=cls.company,
            financial_year=cls.fin_year,
            item=cls.item,
            model='X-200',
            purchase_date='2022-01-01',
            life_date='2032-01-01',
            put_to_use_date='2022-02-01',
            warranty_expiry_date='2024-01-01',
            received_date='2021-12-25',
            insurance=0,
            cost=150000.00,
            incharge=cls.employee,
            make='Caterpillar',
            capacity='20 Ton',
            supplier=cls.supplier,
            location='WH-A1-Bay2',
            pm_days=30,
            session_id='testsession123'
        )
        cls.process1 = ProcessMaster.objects.create(id=1, symbol='P1', process_name='Inspection')
        cls.process2 = ProcessMaster.objects.create(id=2, symbol='P2', process_name='Maintenance')
        cls.process3 = ProcessMaster.objects.create(id=3, symbol='P3', process_name='Calibration')
        
        cls.spare_item1 = Item.objects.create(
            id=1002, item_code='SP001', manf_desc='Oil Filter', uom_basic=cls.unit, 
            category=cls.category, subcategory=cls.subcategory, company=cls.company, stock_qty=10
        )
        cls.spare_item2 = Item.objects.create(
            id=1003, item_code='SP002', manf_desc='Fuel Filter', uom_basic=cls.unit, 
            category=cls.category, subcategory=cls.subcategory, company=cls.company, stock_qty=5
        )

class MachineModelTest(MasterDataSetupMixin, TestCase):
    def test_machine_creation(self):
        self.assertEqual(self.machine.model, 'X-200')
        self.assertEqual(self.machine.item.item_code, 'MC001')
        self.assertEqual(self.machine.incharge.employee_name, 'John Doe')

    def test_machine_has_image_property(self):
        self.assertFalse(self.machine.has_image)
        self.machine.file_name = 'test.jpg'
        self.machine.file_data = b'somebinarydata'
        self.assertTrue(self.machine.has_image)

    def test_delete_file_method(self):
        self.machine.file_name = 'test.pdf'
        self.machine.file_size = 12345
        self.machine.content_type = 'application/pdf'
        self.machine.file_data = b'pdfdata'
        self.machine.save()

        self.machine.delete_file()
        self.assertIsNone(self.machine.file_name)
        self.assertIsNone(self.machine.file_size)
        self.assertIsNone(self.machine.content_type)
        self.assertIsNone(self.machine.file_data)

    def test_is_valid_for_proceed_method(self):
        # Test with no processes or spares initially
        with self.assertRaises(ValidationError) as cm:
            self.machine.is_valid_for_proceed()
        self.assertEqual(str(cm.exception), 'At least one process is required for Machinery.')

        # Add a process
        MachineProcess.objects.create(machine=self.machine, process=self.process1)
        # Should still fail because no spares
        with self.assertRaises(ValidationError) as cm:
            self.machine.is_valid_for_proceed()
        self.assertEqual(str(cm.exception), 'At least one spare is required for Machinery.')

        # Add a spare
        MachineSpare.objects.create(machine=self.machine, item=self.spare_item1, quantity=5)
        # Should now pass
        self.assertTrue(self.machine.is_valid_for_proceed())

class MachineFormTest(MasterDataSetupMixin, TestCase):
    def test_form_valid_data(self):
        data = {
            'model': 'X-300',
            'make': 'Komatsu',
            'capacity': '30 Ton',
            'purchase_date': '2023-01-01',
            'life_date': '2033-01-01',
            'put_to_use_date': '2023-02-01',
            'warranty_expiry_date': '2025-01-01',
            'received_date': '2022-12-25',
            'insurance': '0', # No insurance
            'cost': '200000.00',
            'supplier': self.supplier.supplierid,
            'incharge': self.employee.empid,
            'location': 'WH-B1-Bay3',
            'pm_days': '45',
        }
        form = MachineForm(data=data)
        self.assertTrue(form.is_valid(), form.errors)

    def test_form_insurance_required_validation(self):
        data = {
            'model': 'X-300', 'make': 'Komatsu', 'capacity': '30 Ton',
            'purchase_date': '2023-01-01', 'life_date': '2033-01-01',
            'put_to_use_date': '2023-02-01', 'warranty_expiry_date': '2025-01-01',
            'received_date': '2022-12-25',
            'insurance': '1', # Yes insurance, but no date
            'insurance_expiry_date': '', 
            'cost': '200000.00', 'supplier': self.supplier.supplierid,
            'incharge': self.employee.empid, 'location': 'WH-B1-Bay3', 'pm_days': '45',
        }
        form = MachineForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('insurance_expiry_date', form.errors)
        self.assertIn('Insurance Expiry Date is required when Insurance is selected.', form.errors['insurance_expiry_date'])

    def test_machine_spare_add_form_valid_data(self):
        data = {'item_id': self.spare_item1.pk, 'quantity': '10.5'}
        form = MachineSpareAddForm(data=data)
        self.assertTrue(form.is_valid(), form.errors)
        self.assertEqual(form.cleaned_data['quantity'], 10.5)

    def test_machine_spare_add_form_invalid_quantity(self):
        data = {'item_id': self.spare_item1.pk, 'quantity': 'invalid'}
        form = MachineSpareAddForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('quantity', form.errors)

        data = {'item_id': self.spare_item1.pk, 'quantity': '-5'}
        form = MachineSpareAddForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('quantity', form.errors)
        
        data = {'item_id': self.spare_item1.pk, 'quantity': '0'}
        form = MachineSpareAddForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('quantity', form.errors)


class MachineryViewsTest(MasterDataSetupMixin, TestCase):
    def setUp(self):
        self.client = Client()
        # Ensure a machine exists to edit
        self.machine_item_id = self.machine.item.pk
        self.machine_pk = self.machine.pk
        self.edit_url = reverse('machinery:machine_edit') + f'?Id={self.machine_item_id}'

    def test_edit_view_get(self):
        response = self.client.get(self.edit_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/machine_edit_details.html')
        self.assertContains(response, self.machine.model)
        self.assertContains(response, self.machine.item.item_code)

    def test_edit_view_post_success(self):
        # Add minimal processes/spares to satisfy validation
        MachineProcess.objects.create(machine=self.machine, process=self.process1)
        MachineSpare.objects.create(machine=self.machine, item=self.spare_item1, quantity=1)

        data = {
            'model': 'Updated Model',
            'make': 'Updated Make',
            'capacity': 'Updated Capacity',
            'purchase_date': '2022-01-01',
            'life_date': '2032-01-01',
            'put_to_use_date': '2022-02-01',
            'warranty_expiry_date': '2024-01-01',
            'received_date': '2021-12-25',
            'insurance': '0',
            'cost': '160000.00',
            'supplier': self.supplier.supplierid,
            'incharge': self.employee.empid,
            'location': 'Updated Location',
            'pm_days': '35',
            # Add file upload to data (requires BytesIO)
            'uploaded_file': io.BytesIO(b"test_file_content"),
        }
        
        # Simulating file upload needs special handling with TestCase.client.post
        file_obj = io.BytesIO(b"test_file_content")
        file_obj.name = 'test_document.pdf'
        file_obj.content_type = 'application/pdf'
        data['uploaded_file'] = file_obj

        response = self.client.post(self.edit_url, data, follow=True) # follow=True to follow redirect
        self.assertEqual(response.status_code, 200) # Should be 200 after redirect
        self.assertRedirects(response, reverse('machinery:machinery_list_page'))
        
        self.machine.refresh_from_db()
        self.assertEqual(self.machine.model, 'Updated Model')
        self.assertEqual(self.machine.file_name, 'test_document.pdf')
        self.assertEqual(self.machine.file_data, b"test_file_content")

    def test_edit_view_post_validation_failure(self):
        data = {
            'model': '', # Missing required field
            'make': 'Updated Make',
            'capacity': '30 Ton',
            'purchase_date': '2022-01-01',
            'life_date': '2032-01-01',
            'put_to_use_date': '2022-02-01',
            'warranty_expiry_date': '2024-01-01',
            'received_date': '2021-12-25',
            'insurance': '0',
            'cost': '160000.00',
            'supplier': self.supplier.supplierid,
            'incharge': self.employee.empid,
            'location': 'Updated Location',
            'pm_days': '35',
        }
        response = self.client.post(self.edit_url, data)
        self.assertEqual(response.status_code, 200) # Should render with errors
        self.assertContains(response, 'This field is required.')

    def test_file_delete_view_post(self):
        # Add a file first
        self.machine.file_name = 'old_file.txt'
        self.machine.file_data = b'old_content'
        self.machine.save()

        delete_url = reverse('machinery:machine_file_delete', kwargs={'item_id': self.machine_item_id})
        response = self.client.post(delete_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success code
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMachineDetails', response.headers['HX-Trigger'])
        
        self.machine.refresh_from_db()
        self.assertIsNone(self.machine.file_name)
        self.assertIsNone(self.machine.file_data)

    # --- HTMX Interactions Tests ---
    def test_processes_tab_load(self):
        url = reverse('machinery:machine_processes_tab', kwargs={'pk': self.machine_pk})
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/_process_tab_content.html')
        self.assertContains(response, self.process1.process_name) # Should list available processes

    def test_add_process_to_machine(self):
        add_url = reverse('machinery:add_process', kwargs={'pk': self.machine_pk})
        
        # Initially no processes for this machine
        self.assertEqual(self.machine.processes.count(), 0)

        data = {'selected_processes': [self.process1.pk, self.process2.pk]}
        response = self.client.post(add_url, data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/_process_tab_content.html')
        self.machine.refresh_from_db()
        self.assertEqual(self.machine.processes.count(), 2)
        self.assertTrue(self.machine.processes.filter(process=self.process1).exists())
        self.assertTrue(self.machine.processes.filter(process=self.process2).exists())
        self.assertContains(response, '2 process(es) added.')


    def test_remove_process_from_machine(self):
        mp1 = MachineProcess.objects.create(machine=self.machine, process=self.process1)
        mp2 = MachineProcess.objects.create(machine=self.machine, process=self.process2)
        self.assertEqual(self.machine.processes.count(), 2)

        remove_url = reverse('machinery:remove_process', kwargs={'machine_pk': self.machine_pk, 'pk': mp1.pk})
        response = self.client.delete(remove_url, HTTP_HX_REQUEST='true') # DELETE method for DeleteView

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/_process_tab_content.html')
        self.machine.refresh_from_db()
        self.assertEqual(self.machine.processes.count(), 1)
        self.assertFalse(self.machine.processes.filter(process=self.process1).exists())
        self.assertTrue(self.machine.processes.filter(process=self.process2).exists())
        self.assertContains(response, 'Process removed successfully.')

    def test_remove_last_process_from_machine_failure(self):
        mp1 = MachineProcess.objects.create(machine=self.machine, process=self.process1)
        self.assertEqual(self.machine.processes.count(), 1)

        remove_url = reverse('machinery:remove_process', kwargs={'machine_pk': self.machine_pk, 'pk': mp1.pk})
        response = self.client.delete(remove_url, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/_process_tab_content.html')
        self.machine.refresh_from_db()
        self.assertEqual(self.machine.processes.count(), 1) # Still exists
        self.assertContains(response, 'At least one process is required for Machinery.')


    def test_spares_tab_load(self):
        url = reverse('machinery:machine_spares_tab', kwargs={'pk': self.machine_pk})
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/_spares_tab_content.html')
        self.assertContains(response, self.spare_item1.manf_desc) # Should list available spares

    def test_add_spare_to_machine(self):
        add_url = reverse('machinery:add_spare', kwargs={'pk': self.machine_pk})
        
        self.assertEqual(self.machine.spares.count(), 0)

        data = {
            f'chkSpare_{self.spare_item1.pk}': 'on',
            f'item_id_{self.spare_item1.pk}': self.spare_item1.pk,
            f'quantity_{self.spare_item1.pk}': '5.0',
            f'chkSpare_{self.spare_item2.pk}': 'on',
            f'item_id_{self.spare_item2.pk}': self.spare_item2.pk,
            f'quantity_{self.spare_item2.pk}': '2.5',
        }
        response = self.client.post(add_url, data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/_spares_tab_content.html')
        self.machine.refresh_from_db()
        self.assertEqual(self.machine.spares.count(), 2)
        ms1 = self.machine.spares.get(item=self.spare_item1)
        self.assertEqual(ms1.quantity, 5.0)
        ms2 = self.machine.spares.get(item=self.spare_item2)
        self.assertEqual(ms2.quantity, 2.5)
        self.assertContains(response, '2 spare(s) added, 0 updated.')

    def test_add_spare_to_machine_update_quantity(self):
        MachineSpare.objects.create(machine=self.machine, item=self.spare_item1, quantity=1.0)
        self.assertEqual(self.machine.spares.count(), 1)

        add_url = reverse('machinery:add_spare', kwargs={'pk': self.machine_pk})
        data = {
            f'chkSpare_{self.spare_item1.pk}': 'on',
            f'item_id_{self.spare_item1.pk}': self.spare_item1.pk,
            f'quantity_{self.spare_item1.pk}': '10.0', # New quantity
        }
        response = self.client.post(add_url, data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200)
        self.machine.refresh_from_db()
        self.assertEqual(self.machine.spares.count(), 1)
        ms1 = self.machine.spares.get(item=self.spare_item1)
        self.assertEqual(ms1.quantity, 10.0)
        self.assertContains(response, '0 spare(s) added, 1 updated.')


    def test_remove_spare_from_machine(self):
        ms1 = MachineSpare.objects.create(machine=self.machine, item=self.spare_item1, quantity=1)
        ms2 = MachineSpare.objects.create(machine=self.machine, item=self.spare_item2, quantity=1)
        self.assertEqual(self.machine.spares.count(), 2)

        remove_url = reverse('machinery:remove_spare', kwargs={'machine_pk': self.machine_pk, 'pk': ms1.pk})
        response = self.client.delete(remove_url, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/_spares_tab_content.html')
        self.machine.refresh_from_db()
        self.assertEqual(self.machine.spares.count(), 1)
        self.assertFalse(self.machine.spares.filter(item=self.spare_item1).exists())
        self.assertTrue(self.machine.spares.filter(item=self.spare_item2).exists())
        self.assertContains(response, 'Spare removed successfully.')

    def test_remove_last_spare_from_machine_failure(self):
        ms1 = MachineSpare.objects.create(machine=self.machine, item=self.spare_item1, quantity=1)
        self.assertEqual(self.machine.spares.count(), 1)

        remove_url = reverse('machinery:remove_spare', kwargs={'machine_pk': self.machine_pk, 'pk': ms1.pk})
        response = self.client.delete(remove_url, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/_spares_tab_content.html')
        self.machine.refresh_from_db()
        self.assertEqual(self.machine.spares.count(), 1) # Still exists
        self.assertContains(response, 'At least one spare is required for Machinery.')

    # --- Autocomplete Tests ---
    def test_supplier_autocomplete(self):
        url = reverse('machinery:supplier_autocomplete') + '?q=ABC'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = response.json()
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]['text'], f'{self.supplier.supplier_name} [{self.supplier.supplierid}]')

    def test_employee_autocomplete(self):
        url = reverse('machinery:employee_autocomplete') + '?q=John'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = response.json()
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]['text'], f'{self.employee.employee_name} [{self.employee.empid}]')

    def test_get_subcategories_view(self):
        url = reverse('machinery:get_subcategories') + f'?category={self.category.cid}'
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/_subcategory_dropdown.html')
        self.assertContains(response, f'<option value="{self.subcategory.scid}">{self.subcategory.scname}</option>')

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for Tab Navigation:** The main `machine_edit_details.html` uses `hx-get` on tab headers to load `_process_tab_content.html` and `_spares_tab_content.html` only once (`hx-trigger="click once"`), preventing redundant loads. `hx-target` and `hx-swap` ensure only the relevant content area is updated.
*   **HTMX for Form Submission:** The main machine details form within `_machine_details_form.html` uses `hx-post` to submit. On success (`status=204`), it triggers `refreshPage` to redirect, or on error, the view re-renders the form with errors (`hx-target="this" hx-swap="outerHTML"` for error display).
*   **HTMX for Related List Updates:**
    *   `_process_tab_content.html` and `_spares_tab_content.html` use `hx-post` for adding and `hx-delete` for removing items.
    *   After an add/delete operation, the views re-render the entire tab content (e.g., `hx-target="this" hx-swap="outerHTML"`) to ensure both the "Available" and "Associated" lists are refreshed, and messages are displayed.
    *   DataTables initialization is within the partials using `$(document).ready()`, ensuring DataTables is applied when the new content is swapped in.
*   **HTMX for Auto-complete:**
    *   Supplier and Employee name inputs use `hx-get` to query `supplier_autocomplete` and `employee_autocomplete` endpoints respectively.
    *   `hx-trigger="keyup changed delay:500ms"` provides a smooth search experience.
    *   `hx-target` and `hx-indicator` manage where suggestions are loaded and show loading states.
    *   Alpine.js's `x-show` manages the visibility of the suggestion dropdowns.
    *   A custom JavaScript function `selectSuggestion` is used to handle selecting a suggestion and updating the hidden input field that actually holds the ID.
*   **HTMX for Dynamic Dropdowns:** The `category` dropdown in `_spares_tab_content.html` uses `hx-get` to `get_subcategories` endpoint, which renders a new `select` element for `sub_category`, replacing the old one (`hx-target`, `hx-swap`).
*   **Alpine.js for UI State:**
    *   `x-data` is used in `machine_edit_details.html` to manage `active_tab_index` for visual tab switching.
    *   `x-model="insurance_selected"` is used on the insurance radio buttons and `x-show="insurance_selected == '1'"` on the insurance expiry date field to control its visibility dynamically.
    *   Alpine.js can also be used for input masking (e.g., `x-mask:dynamic` for quantity inputs, though not fully implemented in provided code for brevity).
*   **DataTables:** All `GridView` elements are converted to HTML tables with `id` attributes. The `machinery/_process_tab_content.html` and `machinery/_spares_tab_content.html` partials include a `$(document).ready()` block to initialize DataTables on these tables after they are loaded via HTMX. The `machinery_list.html` also uses DataTables.
*   **DRY Templates:** The `_machine_details_form.html`, `_process_tab_content.html`, `_spares_tab_content.html`, and `_subcategory_dropdown.html` are partials, designed to be loaded by HTMX, ensuring no redundant HTML. All extend `core/base.html`.
*   **Strict Separation:** No HTML in views, all business logic in models (`is_valid_for_proceed`, `delete_file`), views are thin.

---

This plan outlines a robust, modern, and maintainable Django application that leverages best practices, HTMX, and Alpine.js for a highly interactive user experience, minimizing traditional JavaScript and full page reloads. The use of a "Fat Model, Thin View" architecture ensures business logic is encapsulated where it belongs, leading to cleaner, more testable code.