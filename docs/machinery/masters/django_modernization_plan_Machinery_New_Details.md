## ASP.NET to Django Conversion Script: Machinery Details Modernization

This plan outlines the strategic migration of your legacy ASP.NET machinery details page to a modern Django application, leveraging automated processes and current best practices. The goal is to provide a comprehensive, actionable blueprint that can be executed with AI-assisted tools, minimizing manual development and maximizing efficiency.

The core benefit of this modernization is transforming a complex, monolithic ASP.NET page into a modular, highly performant Django application with a dynamic, responsive user experience. This transition significantly reduces maintenance overhead, improves scalability, and enhances development agility for future enhancements.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is located at `core/base.html`.
- Focus ONLY on component-specific code for the current `machinery` module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

## Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:** The ASP.NET code interacts with multiple tables to manage machinery details, processes, and spares. We've inferred the following core tables and their approximate columns:

*   **`tblMS_Master` (Machinery Details)**
    *   `Id` (PK, INT)
    *   `SysDate` (DATETIME)
    *   `SysTime` (TIME)
    *   `CompId` (INT, Company ID)
    *   `FinYearId` (INT, Financial Year ID)
    *   `SessionId` (NVARCHAR, maps to User ID/username)
    *   `ItemId` (INT, FK to `tblDG_Item_Master` - the main item for which machinery details are being entered)
    *   `Make` (NVARCHAR)
    *   `Model` (NVARCHAR)
    *   `Capacity` (NVARCHAR)
    *   `PurchaseDate` (DATETIME)
    *   `SupplierName` (NVARCHAR, FK to `tblMM_Supplier_master` - stores SupplierId)
    *   `Cost` (FLOAT)
    *   `WarrantyExpiryDate` (DATETIME)
    *   `LifeDate` (DATETIME)
    *   `ReceivedDate` (DATETIME)
    *   `Insurance` (BIT, 0 for NO, 1 for YES)
    *   `InsuranceExpiryDate` (DATETIME)
    *   `Puttouse` (DATETIME)
    *   `Incharge` (NVARCHAR, FK to `tblHR_OfficeStaff` - stores EmpId)
    *   `Location` (NVARCHAR, FK to `tblDG_Location_Master` - stores LocationId)
    *   `PMDays` (INT, Preventive Maintenance Days)
    *   `FileName` (NVARCHAR)
    *   `FileSize` (INT)
    *   `ContentType` (NVARCHAR)
    *   `FileData` (VARBINARY(MAX)) - stores image data

*   **`tblDG_Item_Master` (General Item/Machinery Item)**
    *   `Id` (PK, INT)
    *   `ItemCode` (NVARCHAR)
    *   `ManfDesc` (NVARCHAR, Manufacturer Description)
    *   `UOMBasic` (INT, FK to `Unit_Master`)
    *   `CId` (INT, FK to `tblDG_Category_Master`)
    *   `SCId` (INT, FK to `tblDG_SubCategory_Master`)
    *   `StockQty` (FLOAT)
    *   `Location` (NVARCHAR, FK to `tblDG_Location_Master` - stores LocationId)
    *   `Absolute` (BIT)
    *   `CompId` (INT)

*   **`Unit_Master` (Unit of Measure)**
    *   `Id` (PK, INT)
    *   `Symbol` (NVARCHAR)

*   **`tblDG_Category_Master` (Item Category)**
    *   `CId` (PK, INT)
    *   `CategoryName` (Inferred: from `DrpCategory` population)

*   **`tblDG_SubCategory_Master` (Item SubCategory)**
    *   `SCId` (PK, INT)
    *   `CId` (INT, FK to `tblDG_Category_Master`)
    *   `Symbol` (NVARCHAR)
    *   `SCName` (NVARCHAR, SubCategory Name)

*   **`tblMM_Supplier_master` (Supplier)**
    *   `SupplierId` (PK, INT)
    *   `SupplierName` (NVARCHAR)
    *   `CompId` (INT)

*   **`tblHR_OfficeStaff` (Employee/Office Staff)**
    *   `EmpId` (PK, INT)
    *   `EmployeeName` (NVARCHAR)
    *   `CompId` (INT)

*   **`tblPln_Process_Master` (Planning Process)**
    *   `Id` (PK, INT)
    *   `Symbol` (NVARCHAR)
    *   `ProcessName` (NVARCHAR)

*   **`tblDG_Location_Master` (Location)**
    *   `Id` (PK, INT)
    *   `LocationLabel` (NVARCHAR)
    *   `LocationNo` (NVARCHAR)

*   **`tblMS_Process` (Machinery Processes Link)**
    *   `Id` (PK, INT)
    *   `MId` (INT, FK to `tblMS_Master`)
    *   `PId` (INT, FK to `tblPln_Process_Master`)

*   **`tblMS_Spares` (Machinery Spares Link)**
    *   `Id` (PK, INT)
    *   `MId` (INT, FK to `tblMS_Master`)
    *   `ItemId` (INT, FK to `tblDG_Item_Master`)
    *   `Qty` (FLOAT)

*Temporary tables `tblMS_Process_Temp` and `tblMS_Spares_Temp` will be replaced by in-session lists in Django for a more modern, efficient approach, eliminating unnecessary database writes for transient data.*

## Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and data handling logic in the ASP.NET code.

**Instructions:**
*   **Create/Update (Main Machinery Details):**
    *   The primary `btnProceed_Click` handles saving the machinery details (`tblMS_Master`).
    *   It retrieves values from `TextBox` and `RadioButtonList` controls.
    *   Performs conditional logic based on `RadiobtnInsurance.SelectedValue` (whether insurance is "YES" or "NO").
    *   Handles `FileUpload1` for image storage directly in the database (`FileData`).
    *   Inserts or updates records into `tblMS_Master`.
    *   **Related Data Handling:** Before saving the main machinery record, it fetches selected processes from `tblMS_Process_Temp` and selected spares from `tblMS_Spares_Temp`. After `tblMS_Master` is saved, it inserts these into `tblMS_Process` and `tblMS_Spares`, respectively.
    *   **Temporary Data Cleanup:** Finally, it deletes records from the temporary tables (`tblMS_Process_Temp`, `tblMS_Spares_Temp`).
    *   **Validation:** Extensive client-side (via validators) and server-side validation for required fields, date formats, and numeric inputs.

*   **Read (Displaying Data):**
    *   `Page_Load` fetches initial machinery item details (`lblItemCode`, `lblunit`, `lblManfDesc`) based on a `QueryString` `Id`.
    *   `fillProcess()` populates `GridView1` (list of all available processes).
    *   `LoadProcess()` populates `GridView4` (list of currently selected processes from `tblMS_Process_Temp`).
    *   `Fillgridview()` populates `GridView2` (list of available spare items) with filtering based on category, subcategory, and search criteria.
    *   `LoadDataSpare()` populates `GridView3` (list of currently selected spares from `tblMS_Spares_Temp`).
    *   Autocomplete functionality for `SupplierName` and `Incharge` via `WebMethod` calls.
    *   Dropdown changes (`DrpCategory`, `DrpSubCategory`, `DrpSearchCode`, `DropDownList3`) trigger `GridView2` refreshes.
    *   GridViews support paging (`OnPageIndexChanging`).

*   **Update (Temporary Data):**
    *   `btnProcessAdd_Click`: Adds selected processes (checkboxes in `GridView1`) to `tblMS_Process_Temp`.
    *   `btnSpareAdd_Click`: Adds selected spares (checkboxes and quantity textboxes in `GridView2`) to `tblMS_Spares_Temp`.
    *   These actions trigger refreshes of `GridView4` and `GridView3` respectively.

*   **Delete (Temporary Data):**
    *   `GridView4_RowCommand` (for "del" command): Deletes a process from `tblMS_Process_Temp`.
    *   `GridView3_RowCommand` (for "del" command): Deletes a spare from `tblMS_Spares_Temp`.
    *   These actions trigger refreshes of the respective GridViews.

*   **Other:**
    *   `EnableDisableInsurance()`: Toggles visibility and required validation for `txtInsuranceExpiresOn` based on `RadiobtnInsurance` selection.
    *   `btnCancel_Click`: Redirects the user away from the page.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, noting mappings and dynamic interactions.

**Instructions:**
*   **Master Page & ContentPlaceHolders:** Django's template inheritance will replace this, with `core/base.html` serving as the master page.
*   **`TabContainer` (`TabPanel`):** Replaced by HTMX-driven tab navigation. Each tab content will be a separate HTMX-loaded partial. Alpine.js can manage active tab styling.
*   **`UpdatePanel`:** Functionality will be natively handled by HTMX `hx-target` and `hx-swap` attributes on relevant elements (buttons, forms, links).
*   **`asp:Label`:** Display static text or data from the backend.
*   **`asp:TextBox`:** User input fields. Will be mapped to Django form fields, styled with Tailwind CSS. Dates will use HTML5 `type="date"` or a custom JS-based date picker (e.g., Flatpickr, but Alpine.js can also handle simple date pickers if needed).
*   **`asp:RequiredFieldValidator`, `asp:RegularExpressionValidator`:** Replaced by Django form validation (clean methods, `RegexValidator`) and HTML5 client-side validation, potentially enhanced with Alpine.js feedback.
*   **`cc1:CalendarExtender`:** Replaced by an HTML5 date input type (`<input type="date">`) or a lightweight Alpine.js/HTMX integrated date picker solution (e.g., custom component, Flatpickr).
*   **`cc1:AutoCompleteExtender`:** Replaced by HTMX AJAX calls to Django views that return filtered JSON data for autocomplete suggestions, consumed by Alpine.js for interactive dropdowns.
*   **`asp:RadioButtonList`:** `RadiobtnInsurance` will be a Django `forms.ChoiceField` rendered as radio buttons, with `x-show` in Alpine.js for conditional visibility.
*   **`asp:FileUpload`:** Replaced by Django `FileField` and `ClearableFileInput` widget.
*   **`asp:GridView`:** This is a major component.
    *   Will be replaced by HTML `<table>` elements.
    *   Client-side DataTables.js library will provide sorting, searching, and pagination.
    *   HTMX will load these tables as partials and handle "Add" and "Delete" actions.
    *   Checkboxes and textboxes within GridView rows will be standard HTML inputs.
*   **`asp:Button`, `asp:LinkButton`:** Replaced by HTML `<button>` and `<a>` elements with HTMX attributes (`hx-post`, `hx-get`, `hx-delete`, `hx-target`, `hx-swap`, `hx-trigger`).
*   **`asp:DropDownList`:** Replaced by HTML `<select>` elements. Changes will trigger HTMX `hx-get` to refresh relevant data (e.g., `GridView2`).
*   **`ClientScript.RegisterStartupScript` (for alerts):** Replaced by Django messages framework, with HTMX `hx-trigger` to display messages dynamically.

---

## Step 4: Generate Django Code

### App Name: `machinery`

This Django application will manage all machinery-related data and functionalities.

### 4.1 Models (`machinery/models.py`)

**Task:** Create Django models based on the identified database schema.

**Instructions:**
- Each model corresponds to a database table.
- Use `managed = False` and `db_table` to map to existing tables.
- Foreign keys are defined with `models.ForeignKey`.
- The `SessionId` in `tblMS_Master` will be mapped to a `ForeignKey` to Django's `User` model for proper user association.
- `FileData` as `BinaryField` matches the ASP.NET `VARBINARY(MAX)`.
- Model methods are added for business logic, following the "fat model" principle.

```python
from django.db import models
from django.conf import settings
from datetime import date, time

# Helper functions for common logic (e.g., date parsing, ID extraction)
# In a real system, clsFunctions would be a service layer or utility module.
# For demonstration, we'll place critical parts as static methods or model methods.
class UtilityFunctions:
    @staticmethod
    def get_code_from_name_id(text_with_id):
        """Extracts the ID from a string like 'Name [ID]'."""
        if text_with_id and '[' in text_with_id and ']' in text_with_id:
            try:
                return int(text_with_id.split('[')[-1].replace(']', '').strip())
            except ValueError:
                pass
        return None # Or raise an error if ID is mandatory
    
    @staticmethod
    def parse_date_from_ddmmyyyy(date_string):
        """Parses a date string in dd-MM-yyyy format to a date object."""
        try:
            return date.strptime(date_string, '%d-%m-%Y')
        except (ValueError, TypeError):
            return None # Or raise an error

# --- Core Master Data Models ---

class Unit(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit of Measure'
        verbose_name_plural = 'Units of Measure'

    def __str__(self):
        return self.symbol

class Category(models.Model):
    cid = models.IntegerField(db_column='CId', primary_key=True)
    category_name = models.CharField(db_column='CategoryName', max_length=255, blank=True, null=True) # Inferred

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Item Category'
        verbose_name_plural = 'Item Categories'

    def __str__(self):
        return self.category_name or f"Category {self.cid}"

class SubCategory(models.Model):
    scid = models.IntegerField(db_column='SCId', primary_key=True)
    cid = models.ForeignKey(Category, models.DO_NOTHING, db_column='CId')
    symbol = models.CharField(db_column='Symbol', max_length=50)
    sc_name = models.CharField(db_column='SCName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblDG_SubCategory_Master'
        verbose_name = 'Item Sub Category'
        verbose_name_plural = 'Item Sub Categories'

    def __str__(self):
        return f"{self.symbol} - {self.sc_name}"

class Supplier(models.Model):
    supplier_id = models.IntegerField(db_column='SupplierId', primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.supplier_name

class Employee(models.Model):
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return self.employee_name

class Process(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    process_name = models.CharField(db_column='ProcessName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblPln_Process_Master'
        verbose_name = 'Planning Process'
        verbose_name_plural = 'Planning Processes'

    def __str__(self):
        return f"[{self.symbol}] {self.process_name}"

class Location(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    location_label = models.CharField(db_column='LocationLabel', max_length=255)
    location_no = models.CharField(db_column='LocationNo', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblDG_Location_Master'
        verbose_name = 'Location'
        verbose_name_plural = 'Locations'

    def __str__(self):
        return f"{self.location_label} {self.location_no}"

class Item(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=255)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255)
    uom_basic = models.ForeignKey(Unit, models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)
    cid = models.ForeignKey(Category, models.DO_NOTHING, db_column='CId', blank=True, null=True)
    scid = models.ForeignKey(SubCategory, models.DO_NOTHING, db_column='SCId', blank=True, null=True)
    stock_qty = models.FloatField(db_column='StockQty', blank=True, null=True)
    location = models.ForeignKey(Location, models.DO_NOTHING, db_column='Location', blank=True, null=True)
    _absolute = models.BooleanField(db_column='Absolute', default=False) # Renamed to avoid keyword conflict
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.item_code

# --- Main Machinery Application Models ---

class Machinery(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate', default=date.today)
    sys_time = models.TimeField(db_column='SysTime', default=time(0,0)) # Default time, adjust as needed
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    session_user = models.ForeignKey(settings.AUTH_USER_MODEL, models.DO_NOTHING, db_column='SessionId', related_name='machinery_records')
    item = models.ForeignKey(Item, models.DO_NOTHING, db_column='ItemId')
    make = models.CharField(db_column='Make', max_length=255)
    model_name = models.CharField(db_column='Model', max_length=255) # Renamed to avoid keyword conflict
    capacity = models.CharField(db_column='Capacity', max_length=255)
    purchase_date = models.DateField(db_column='PurchaseDate')
    supplier = models.ForeignKey(Supplier, models.DO_NOTHING, db_column='SupplierName') # Stores SupplierId
    cost = models.FloatField(db_column='Cost')
    warranty_expiry_date = models.DateField(db_column='WarrantyExpiryDate')
    life_date = models.DateField(db_column='LifeDate')
    received_date = models.DateField(db_column='ReceivedDate')
    insurance = models.BooleanField(db_column='Insurance')
    insurance_expiry_date = models.DateField(db_column='InsuranceExpiryDate', blank=True, null=True)
    put_to_use = models.DateField(db_column='Puttouse')
    incharge = models.ForeignKey(Employee, models.DO_NOTHING, db_column='Incharge') # Stores EmpId
    location_fk = models.ForeignKey(Location, models.DO_NOTHING, db_column='Location', blank=True, null=True) # Stores LocationId
    pm_days = models.IntegerField(db_column='PMDays')
    file_name = models.CharField(db_column='FileName', max_length=255, blank=True, null=True)
    file_size = models.IntegerField(db_column='FileSize', blank=True, null=True)
    content_type = models.CharField(db_column='ContentType', max_length=255, blank=True, null=True)
    file_data = models.BinaryField(db_column='FileData', blank=True, null=True) # Stores raw binary data

    class Meta:
        managed = False
        db_table = 'tblMS_Master'
        verbose_name = 'Machinery'
        verbose_name_plural = 'Machinery'

    def __str__(self):
        return f"{self.item.manf_desc} ({self.model_name})"

    @classmethod
    def create_new_machinery(cls, user, comp_id, fin_year_id, item_id, form_data, processes_data, spares_data, uploaded_file=None):
        """
        Business logic to create a new machinery record,
        including associated processes and spares.
        This method replaces much of btnProceed_Click logic.
        """
        # Parse form data and lookup FK objects
        item = Item.objects.get(id=item_id)
        supplier_id = UtilityFunctions.get_code_from_name_id(form_data['supplier_name_autocomplete'])
        supplier = Supplier.objects.get(supplier_id=supplier_id) if supplier_id else None
        incharge_id = UtilityFunctions.get_code_from_name_id(form_data['incharge_autocomplete'])
        incharge = Employee.objects.get(emp_id=incharge_id) if incharge_id else None
        location_id = UtilityFunctions.get_code_from_name_id(form_data['location_autocomplete']) # Inferred: if location is also autocomplete
        location_obj = Location.objects.get(id=location_id) if location_id else None

        # Convert date strings to date objects
        purchase_date = UtilityFunctions.parse_date_from_ddmmyyyy(form_data['purchase_date'])
        life_date = UtilityFunctions.parse_date_from_ddmmyyyy(form_data['life_date'])
        received_date = UtilityFunctions.parse_date_from_ddmmyyyy(form_data['received_date'])
        warranty_expiry_date = UtilityFunctions.parse_date_from_ddmmyyyy(form_data['warranty_expiry_date'])
        put_to_use = UtilityFunctions.parse_date_from_ddmmyyyy(form_data['put_to_use'])
        
        insurance = form_data['insurance'] == '1'
        insurance_expiry_date = None
        if insurance:
            insurance_expiry_date = UtilityFunctions.parse_date_from_ddmmyyyy(form_data.get('insurance_expiry_date'))

        machinery = cls(
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            session_user=user,
            item=item,
            make=form_data['make'],
            model_name=form_data['model_name'],
            capacity=form_data['capacity'],
            purchase_date=purchase_date,
            supplier=supplier,
            cost=float(form_data['cost']),
            warranty_expiry_date=warranty_expiry_date,
            life_date=life_date,
            received_date=received_date,
            insurance=insurance,
            insurance_expiry_date=insurance_expiry_date,
            put_to_use=put_to_use,
            incharge=incharge,
            location_fk=location_obj,
            pm_days=int(form_data['pm_days']),
        )

        if uploaded_file:
            machinery.file_name = uploaded_file.name
            machinery.file_size = uploaded_file.size
            machinery.content_type = uploaded_file.content_type
            machinery.file_data = uploaded_file.read() # Read binary data from file
            # Modern approach: Save file to disk and store path
            # from django.core.files.storage import default_storage
            # file_path = default_storage.save(f'machinery_images/{uploaded_file.name}', uploaded_file)
            # machinery.file_data = file_path # Store path instead of binary data

        machinery.save() # Save the main machinery record

        # Save associated processes
        MachineryProcess.create_multiple_processes(machinery, processes_data)

        # Save associated spares
        MachinerySpare.create_multiple_spares(machinery, spares_data)

        return machinery

class MachineryProcess(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    machinery = models.ForeignKey(Machinery, models.DO_NOTHING, db_column='MId', related_name='processes')
    process = models.ForeignKey(Process, models.DO_NOTHING, db_column='PId')

    class Meta:
        managed = False
        db_table = 'tblMS_Process'
        verbose_name = 'Machinery Process'
        verbose_name_plural = 'Machinery Processes'

    def __str__(self):
        return f"{self.machinery.model_name} - {self.process.process_name}"

    @classmethod
    def create_multiple_processes(cls, machinery_instance, process_ids):
        """Creates multiple MachineryProcess entries."""
        for pid in process_ids:
            process_obj = Process.objects.get(id=pid)
            cls.objects.create(machinery=machinery_instance, process=process_obj)
        # Assuming no Id is provided for new entries, database auto-increments.
        # If Id needs to be manually set or derived, additional logic is needed.

class MachinerySpare(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    machinery = models.ForeignKey(Machinery, models.DO_NOTHING, db_column='MId', related_name='spares')
    item = models.ForeignKey(Item, models.DO_NOTHING, db_column='ItemId')
    qty = models.FloatField(db_column='Qty')

    class Meta:
        managed = False
        db_table = 'tblMS_Spares'
        verbose_name = 'Machinery Spare'
        verbose_name_plural = 'Machinery Spares'

    def __str__(self):
        return f"{self.machinery.model_name} - {self.item.item_code} ({self.qty})"

    @classmethod
    def create_multiple_spares(cls, machinery_instance, spares_data):
        """Creates multiple MachinerySpare entries."""
        for spare_item in spares_data:
            item_obj = Item.objects.get(id=spare_item['item_id'])
            cls.objects.create(machinery=machinery_instance, item=item_obj, qty=float(spare_item['qty']))
        # Assuming no Id is provided for new entries, database auto-increments.
        # If Id needs to be manually set or derived, additional logic is needed.
```

### 4.2 Forms (`machinery/forms.py`)

**Task:** Define Django forms for user input.

**Instructions:**
- Create a `ModelForm` for `Machinery`.
- Add custom fields for autocomplete inputs (Supplier, Incharge) which will take text input but internally resolve to FK IDs.
- Date fields use `DateInput` with `type="date"` for modern browser support.
- Widgets are styled with Tailwind CSS classes.
- Custom validation for date and numeric inputs, reflecting ASP.NET validators.

```python
from django import forms
from .models import Machinery, Category, SubCategory, Item, Supplier, Employee, Location, Process
from django.core.validators import RegexValidator
from django.forms.widgets import ClearableFileInput

class MachineryForm(forms.ModelForm):
    # Custom fields for autocomplete, as they display name but store ID (or resolve to FK)
    supplier_name_autocomplete = forms.CharField(
        label="Supplier Name", 
        max_length=255, 
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '/machinery/api/suppliers-autocomplete/',
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#supplier-suggestions',
            'hx-swap': 'innerHTML',
            'placeholder': 'Start typing supplier name...',
            'autocomplete': 'off', # Prevent browser autocomplete
            'x-on:click.outside': 'open_suggestions = false',
            'x-on:focus': 'open_suggestions = true',
            'x-on:keydown.arrow-down.prevent': '$refs.suggestions_list.children[0].focus()',
        })
    )
    incharge_autocomplete = forms.CharField(
        label="Incharge", 
        max_length=255, 
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '/machinery/api/employees-autocomplete/',
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#incharge-suggestions',
            'hx-swap': 'innerHTML',
            'placeholder': 'Start typing employee name...',
            'autocomplete': 'off',
            'x-on:click.outside': 'open_suggestions = false',
            'x-on:focus': 'open_suggestions = true',
            'x-on:keydown.arrow-down.prevent': '$refs.suggestions_list.children[0].focus()',
        })
    )
    
    # File upload field. Use default Django FileInput for now.
    uploaded_image = forms.FileField(
        label="Upload Image", 
        required=False,
        widget=ClearableFileInput(attrs={'class': 'block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'})
    )

    # Temporary fields to hold selected processes and spares
    # These will be populated by Alpine.js/HTMX from the client side
    selected_processes_json = forms.CharField(widget=forms.HiddenInput(), required=False)
    selected_spares_json = forms.CharField(widget=forms.HiddenInput(), required=False)

    class Meta:
        model = Machinery
        fields = [
            'make', 'model_name', 'capacity', 'purchase_date', 'cost', 
            'life_date', 'warranty_expiry_date', 'insurance', 'insurance_expiry_date',
            'put_to_use', 'received_date', 'pm_days',
        ]
        
        widgets = {
            'make': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'model_name': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'capacity': forms.TextInput(attrs={'class': 'box3 w-full'}),
            'purchase_date': forms.DateInput(attrs={'class': 'box3', 'type': 'date'}),
            'cost': forms.TextInput(attrs={'class': 'box3'}),
            'life_date': forms.DateInput(attrs={'class': 'box3', 'type': 'date'}),
            'warranty_expiry_date': forms.DateInput(attrs={'class': 'box3', 'type': 'date'}),
            'insurance': forms.RadioSelect(choices=[(False, 'NO'), (True, 'YES')], attrs={'x-model': 'insurance_enabled', 'class': 'flex space-x-4'}),
            'insurance_expiry_date': forms.DateInput(attrs={'class': 'box3', 'type': 'date', 'x-show': 'insurance_enabled'}),
            'put_to_use': forms.DateInput(attrs={'class': 'box3', 'type': 'date'}),
            'received_date': forms.DateInput(attrs={'class': 'box3', 'type': 'date'}),
            'pm_days': forms.TextInput(attrs={'class': 'box3'}),
        }
        labels = {
            'model_name': 'Model',
            'pm_days': 'Preventive Maintenance in days',
            'put_to_use': 'Put to use',
            'insurance': 'Insurance',
            'insurance_expiry_date': 'Insurance Expires on',
        }
        
    # Custom validations
    cost_validator = RegexValidator(r'^[0-9]\d*(\.\d+)?$', "Enter a valid numeric value for Cost.")
    pm_days_validator = RegexValidator(r'^[0-9]\d*(\.\d+)?$', "Enter a valid numeric value for PM Days.")

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Apply validators
        self.fields['cost'].validators.append(self.cost_validator)
        self.fields['pm_days'].validators.append(self.pm_days_validator)
        
        # Initial values for autocomplete fields if instance exists (for update)
        if self.instance and self.instance.pk:
            if self.instance.supplier:
                self.fields['supplier_name_autocomplete'].initial = f"{self.instance.supplier.supplier_name} [{self.instance.supplier.supplier_id}]"
            if self.instance.incharge:
                self.fields['incharge_autocomplete'].initial = f"{self.instance.incharge.employee_name} [{self.instance.incharge.emp_id}]"
            # The original ASP.NET code shows lblLocation is static text for Item,
            # but txtLocation is an input for Machinery.
            # Assuming txtLocation is a simple text input for location name.
            # If it's a FK to Location model, then it needs to be an autocomplete too.
            # Based on the ASP.NET code, txtLocation is just a text field.
            self.fields['location_fk'].widget = forms.TextInput(attrs={'class': 'box3 w-full'})
            self.fields['location_fk'].label = 'Location'
            self.fields['location_fk'].required = True # Based on RequiredFieldValidator
            
            # Initial state for insurance radio button
            self.initial['insurance'] = self.instance.insurance
            
    def clean(self):
        cleaned_data = super().clean()
        insurance = cleaned_data.get('insurance')
        insurance_expiry_date = cleaned_data.get('insurance_expiry_date')

        if insurance and not insurance_expiry_date:
            self.add_error('insurance_expiry_date', 'Insurance Expires on date is required when insurance is enabled.')
        
        # Validate that IDs are extracted from autocomplete fields
        supplier_id = UtilityFunctions.get_code_from_name_id(cleaned_data.get('supplier_name_autocomplete'))
        if not supplier_id:
            self.add_error('supplier_name_autocomplete', 'Please select a valid Supplier.')
        else:
            try:
                Supplier.objects.get(supplier_id=supplier_id)
            except Supplier.DoesNotExist:
                self.add_error('supplier_name_autocomplete', 'Selected supplier does not exist.')
                
        incharge_id = UtilityFunctions.get_code_from_name_id(cleaned_data.get('incharge_autocomplete'))
        if not incharge_id:
            self.add_error('incharge_autocomplete', 'Please select a valid Incharge.')
        else:
            try:
                Employee.objects.get(emp_id=incharge_id)
            except Employee.DoesNotExist:
                self.add_error('incharge_autocomplete', 'Selected incharge does not exist.')

        return cleaned_data

class SpareQuantityForm(forms.Form):
    # This form is used for validation of quantity in the spares selection table.
    qty = forms.FloatField(
        label="Quantity", 
        min_value=0.01, # Assuming quantity must be positive
        required=True,
        widget=forms.NumberInput(attrs={'class': 'box3 w-full', 'placeholder': 'Qty'})
    )
    
    # Custom validation for quantity (matches ASP.NET RegularExpressionValidator)
    qty_validator = RegexValidator(r'^[0-9]\d*(\.\d+)?$', "Enter a valid numeric value for Quantity.")

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['qty'].validators.append(self.qty_validator)

```

### 4.3 Views (`machinery/views.py`)

**Task:** Implement CRUD and dynamic content operations using CBVs and Django's session.

**Instructions:**
- One main view for the form (`MachineryDetailView`).
- Separate views for HTMX-driven partials (tables, autocomplete suggestions).
- Session is used to temporarily store selected processes and spares.
- `login_required` decorator ensures user context (CompId, FinYearId, SessionId).
- `messages` framework for feedback.
- `HttpResponse` with `HX-Trigger` for HTMX-specific responses.
- Views are kept thin (5-15 lines) by offloading logic to models or helper functions.

```python
from django.shortcuts import render, get_object_or_404, redirect
from django.views import View
from django.views.generic import ListView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.contrib.auth.mixins import LoginRequiredMixin
import json
from datetime import date, time

from .models import Machinery, Item, Unit, Category, SubCategory, Supplier, Employee, Process, Location, MachineryProcess, MachinerySpare, UtilityFunctions
from .forms import MachineryForm, SpareQuantityForm

# --- Main Machinery Detail View (Create/Update Hybrid) ---

class MachineryDetailView(LoginRequiredMixin, View):
    template_name = 'machinery/machinery_detail.html'

    def get_initial_data(self, request, item_id):
        # Initial data for the main form
        initial_data = {}
        item_obj = get_object_or_404(Item, id=item_id, comp_id=request.session.get('compid'))
        
        # If editing an existing machinery record linked to this item
        # Assuming only one machinery record per item for simplicity, or handle multiple.
        # Original code implies item is a specific machinery ID rather than item_id from item_master.
        # Let's adjust to be a Create/Update on Machinery record.
        machinery_instance = None
        if item_id: # Interpreting itemId as an existing machinery record ID now
             try:
                 machinery_instance = Machinery.objects.get(id=item_id, comp_id=request.session.get('compid'))
                 # Initialize form with existing data
                 initial_data = {
                     'make': machinery_instance.make,
                     'model_name': machinery_instance.model_name,
                     'capacity': machinery_instance.capacity,
                     'purchase_date': machinery_instance.purchase_date.strftime('%d-%m-%Y') if machinery_instance.purchase_date else '',
                     'cost': machinery_instance.cost,
                     'life_date': machinery_instance.life_date.strftime('%d-%m-%Y') if machinery_instance.life_date else '',
                     'warranty_expiry_date': machinery_instance.warranty_expiry_date.strftime('%d-%m-%Y') if machinery_instance.warranty_expiry_date else '',
                     'insurance': '1' if machinery_instance.insurance else '0',
                     'insurance_expiry_date': machinery_instance.insurance_expiry_date.strftime('%d-%m-%Y') if machinery_instance.insurance_expiry_date else '',
                     'put_to_use': machinery_instance.put_to_use.strftime('%d-%m-%Y') if machinery_instance.put_to_use else '',
                     'received_date': machinery_instance.received_date.strftime('%d-%m-%Y') if machinery_instance.received_date else '',
                     'pm_days': machinery_instance.pm_days,
                     'supplier_name_autocomplete': f"{machinery_instance.supplier.supplier_name} [{machinery_instance.supplier.supplier_id}]" if machinery_instance.supplier else '',
                     'incharge_autocomplete': f"{machinery_instance.incharge.employee_name} [{machinery_instance.incharge.emp_id}]" if machinery_instance.incharge else '',
                     'location_fk': machinery_instance.location_fk, # This is the FK object, form will handle.
                 }
             except Machinery.DoesNotExist:
                 pass # No existing machinery record for this item_id, so it's a create

        # Initial labels from Item Master (if creating new or fetching associated item for existing machinery)
        # Original ASP.NET used `itemId` from query string to fetch `ItemCode`, `UOMBasic`, `ManfDesc`
        # Let's assume `item_id` in URL is for the `Item` this machinery record is about.
        if not machinery_instance: # If we are creating a new machinery record
            try:
                main_item = Item.objects.get(id=item_id, comp_id=request.session.get('compid'))
                initial_data['main_item_code'] = main_item.item_code
                initial_data['main_item_uom'] = main_item.uom_basic.symbol if main_item.uom_basic else ''
                initial_data['main_item_desc'] = main_item.manf_desc
            except Item.DoesNotExist:
                messages.error(request, "Machinery Item not found.")
                return redirect(reverse_lazy('machinery_list')) # Redirect to a list page
        else: # If editing, populate labels from the associated item
            initial_data['main_item_code'] = machinery_instance.item.item_code
            initial_data['main_item_uom'] = machinery_instance.item.uom_basic.symbol if machinery_instance.item.uom_basic else ''
            initial_data['main_item_desc'] = machinery_instance.item.manf_desc
            
        return initial_data, machinery_instance # Return both initial data and potential instance

    def get(self, request, item_id=None, *args, **kwargs):
        # item_id here refers to the initial Item from tblDG_Item_Master, not the Machinery ID
        initial_data, machinery_instance = self.get_initial_data(request, item_id)
        
        # Initialize form with data
        form = MachineryForm(initial=initial_data, instance=machinery_instance)
        
        # Initialize session for temp processes and spares if not already present
        # This is for a new record, or loading existing records for editing
        if not machinery_instance: # New machinery record, clear session temp data
            request.session['selected_processes'] = []
            request.session['selected_spares'] = []
        else: # Existing machinery record, populate session temp data from DB
            request.session['selected_processes'] = list(MachineryProcess.objects.filter(machinery=machinery_instance).values_list('process__id', flat=True))
            request.session['selected_spares'] = list(MachinerySpare.objects.filter(machinery=machinery_instance).values('item__id', 'qty'))

        context = {
            'form': form,
            'main_item_code': initial_data.get('main_item_code', ''),
            'main_item_uom': initial_data.get('main_item_uom', ''),
            'main_item_desc': initial_data.get('main_item_desc', ''),
            'machinery_instance_id': machinery_instance.id if machinery_instance else None, # Pass ID for update context
        }
        return render(request, self.template_name, context)

    def post(self, request, item_id=None, *args, **kwargs):
        initial_data, machinery_instance = self.get_initial_data(request, item_id)
        form = MachineryForm(request.POST, request.FILES, instance=machinery_instance)

        if form.is_valid():
            try:
                # Extract processes and spares from hidden fields (populated by Alpine.js from client state)
                processes_data = json.loads(request.POST.get('selected_processes_json', '[]'))
                spares_data = json.loads(request.POST.get('selected_spares_json', '[]'))

                if not processes_data or not spares_data:
                    messages.error(request, "Functionality and spare details are not found.")
                    return render(request, self.template_name, {'form': form, **initial_data})

                # Move saving logic to model's class method (Fat Model)
                machinery_obj = Machinery.create_new_machinery(
                    user=request.user,
                    comp_id=request.session.get('compid'),
                    fin_year_id=request.session.get('finyear'),
                    item_id=item_id, # This is the item from Item Master
                    form_data=form.cleaned_data,
                    processes_data=processes_data,
                    spares_data=spares_data,
                    uploaded_file=request.FILES.get('uploaded_image')
                )
                messages.success(request, f"Machinery '{machinery_obj.model_name}' saved successfully.")

                # Clear temporary session data after successful save
                del request.session['selected_processes']
                del request.session['selected_spares']

                return redirect(reverse_lazy('machinery_list')) # Redirect to the main machinery list page

            except Exception as e:
                messages.error(request, f"Error saving machinery details: {e}")
                # Re-render the form with errors
                return render(request, self.template_name, {'form': form, **initial_data})
        else:
            # Form is not valid, re-render with errors
            messages.error(request, "Please correct the errors in the form.")
            return render(request, self.template_name, {'form': form, **initial_data})

# --- HTMX Partial Views ---

class ProcessesAvailableTableView(LoginRequiredMixin, ListView):
    model = Process
    template_name = 'machinery/_processes_available_table.html'
    context_object_name = 'processes'

    def get_queryset(self):
        # Implement the ASP.NET fillProcess() logic
        return Process.objects.filter(symbol__isnull=False).exclude(symbol='0').order_by('process_name')

class ProcessesSelectedTableView(LoginRequiredMixin, ListView):
    template_name = 'machinery/_processes_selected_table.html'
    context_object_name = 'selected_processes_data'

    def get_queryset(self):
        # Fetch processes from session.
        selected_pids = self.request.session.get('selected_processes', [])
        # Get full Process objects for rendering
        return Process.objects.filter(id__in=selected_pids).order_by('process_name')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass the raw IDs as well for Alpine.js state management
        context['selected_process_ids_json'] = json.dumps(self.request.session.get('selected_processes', []))
        return context

class ProcessAddHTMXView(LoginRequiredMixin, View):
    # This view adds selected processes to the session and returns updated selected processes table
    def post(self, request, *args, **kwargs):
        selected_process_ids_raw = request.POST.getlist('process_ids[]') # From checkboxes
        selected_process_ids = [int(p_id) for p_id in selected_process_ids_raw if p_id.isdigit()]
        
        current_session_processes = request.session.get('selected_processes', [])
        for p_id in selected_process_ids:
            if p_id not in current_session_processes:
                current_session_processes.append(p_id)
        
        request.session['selected_processes'] = current_session_processes
        request.session.modified = True
        
        # Trigger an HTMX event to refresh the selected processes table
        response = HttpResponse(status=204) # No content, just trigger
        response['HX-Trigger'] = 'refreshSelectedProcesses'
        return response

class ProcessDeleteHTMXView(LoginRequiredMixin, View):
    # This view deletes a process from the session and returns updated selected processes table
    def delete(self, request, pk, *args, **kwargs):
        current_session_processes = request.session.get('selected_processes', [])
        if pk in current_session_processes:
            current_session_processes.remove(pk)
            request.session['selected_processes'] = current_session_processes
            request.session.modified = True
        
        response = HttpResponse(status=204)
        response['HX-Trigger'] = 'refreshSelectedProcesses'
        return response

class SparesAvailableTableView(LoginRequiredMixin, ListView):
    template_name = 'machinery/_spares_available_table.html'
    context_object_name = 'available_spares'
    paginate_by = 15 # Matching ASP.NET PageSize

    def get_queryset(self):
        comp_id = self.request.session.get('compid')
        item_id_exclude = self.kwargs.get('item_id_exclude', None) # The main item ID being detailed

        queryset = Item.objects.filter(
            comp_id=comp_id,
            _absolute=False # 'Absolute' != 1
        ).select_related('uom_basic').order_by('-id')

        # Exclude the main machinery item itself from spares list
        if item_id_exclude:
            queryset = queryset.exclude(id=item_id_exclude)

        # Filtering logic from ASP.NET's Fillgridview
        category_id = self.request.GET.get('category_id')
        subcategory_id = self.request.GET.get('subcategory_id')
        search_field = self.request.GET.get('search_field')
        search_term = self.request.GET.get('search_term')

        if category_id and category_id != "Select Category":
            queryset = queryset.filter(cid__cid=category_id)
            if subcategory_id and subcategory_id != "Select SubCategory":
                queryset = queryset.filter(scid__scid=subcategory_id)
        
        if search_field and search_term:
            if search_field == "tblDG_Item_Master.ItemCode":
                queryset = queryset.filter(item_code__icontains=search_term)
            elif search_field == "tblDG_Item_Master.ManfDesc":
                queryset = queryset.filter(manf_desc__icontains=search_term)
            # Assuming Location search would be an ID lookup or specific field match
            # if search_field == "tblDG_Item_Master.Location":
            #    queryset = queryset.filter(location__id=search_term) # Assuming search_term is location ID

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        comp_id = self.request.session.get('compid')
        
        # Populate filter dropdowns
        context['categories'] = Category.objects.all().order_by('category_name')
        context['subcategories'] = []
        if self.request.GET.get('category_id') and self.request.GET.get('category_id') != "Select Category":
            context['subcategories'] = SubCategory.objects.filter(cid__cid=self.request.GET.get('category_id')).order_by('sc_name')
            
        context['search_options'] = [
            {'value': 'Select', 'label': 'Select'},
            {'value': 'tblDG_Item_Master.ItemCode', 'label': 'Item Code'},
            {'value': 'tblDG_Item_Master.ManfDesc', 'label': 'Description'},
            # {'value': 'tblDG_Item_Master.Location', 'label': 'Location'}, # If locations are searchable by ID
        ]
        context['locations'] = Location.objects.all().order_by('location_label', 'location_no')

        # Preserve selected filter values
        context['selected_category'] = self.request.GET.get('category_id', 'Select Category')
        context['selected_subcategory'] = self.request.GET.get('subcategory_id', 'Select SubCategory')
        context['selected_search_field'] = self.request.GET.get('search_field', 'Select')
        context['selected_search_term'] = self.request.GET.get('search_term', '')

        return context

class SparesSelectedTableView(LoginRequiredMixin, ListView):
    template_name = 'machinery/_spares_selected_table.html'
    context_object_name = 'selected_spares_data'
    paginate_by = 15 # Matching ASP.NET PageSize

    def get_queryset(self):
        # Fetch spares from session. Session stores list of {'item_id': id, 'qty': qty}
        selected_spares_list = self.request.session.get('selected_spares', [])
        
        # Build a list of Item objects with quantities
        items_with_qty = []
        item_ids = [s['item_id'] for s in selected_spares_list]
        items_map = {item.id: item for item in Item.objects.filter(id__in=item_ids).select_related('uom_basic')}

        for spare_data in selected_spares_list:
            item = items_map.get(spare_data['item_id'])
            if item:
                items_with_qty.append({
                    'item': item,
                    'qty': spare_data['qty']
                })
        return items_with_qty

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass the raw list as JSON for Alpine.js state management
        context['selected_spares_json'] = json.dumps(self.request.session.get('selected_spares', []))
        return context

class SpareAddHTMXView(LoginRequiredMixin, View):
    # This view adds selected spares to the session and returns updated selected spares table
    def post(self, request, *args, **kwargs):
        # Extract data from POST - multiple checkboxes and associated quantities
        # This requires careful handling as data comes as POST data.
        selected_spares_raw = {}
        for key, value in request.POST.items():
            if key.startswith('chkSpare_') and value == 'on':
                item_id = int(key.replace('chkSpare_', ''))
                qty_key = f'txtQty_{item_id}' # Assuming input name is like txtQty_[item_id]
                qty = request.POST.get(qty_key)
                
                form = SpareQuantityForm({'qty': qty})
                if form.is_valid():
                    selected_spares_raw[item_id] = form.cleaned_data['qty']
                else:
                    # Handle validation error for quantity. Can send a message or partial HTML with error.
                    messages.error(request, f"Invalid quantity for item {item_id}: {form.errors['qty'][0]}")
                    response = HttpResponse(status=204) # Send no content, but trigger message
                    response['HX-Trigger'] = 'showMessage'
                    return response
        
        current_session_spares = request.session.get('selected_spares', [])
        
        # Add/update selected spares
        for item_id, qty in selected_spares_raw.items():
            # Check if item already exists in session, update quantity
            found = False
            for spare in current_session_spares:
                if spare['item_id'] == item_id:
                    spare['qty'] = qty # Update quantity
                    found = True
                    break
            if not found:
                current_session_spares.append({'item_id': item_id, 'qty': qty})
        
        request.session['selected_spares'] = current_session_spares
        request.session.modified = True
        
        response = HttpResponse(status=204)
        response['HX-Trigger'] = 'refreshSelectedSpares'
        return response

class SpareDeleteHTMXView(LoginRequiredMixin, View):
    # This view deletes a spare from the session and returns updated selected spares table
    def delete(self, request, pk, *args, **kwargs):
        current_session_spares = request.session.get('selected_spares', [])
        
        # Remove the spare by item_id
        request.session['selected_spares'] = [
            s for s in current_session_spares if s['item_id'] != pk
        ]
        request.session.modified = True
        
        response = HttpResponse(status=204)
        response['HX-Trigger'] = 'refreshSelectedSpares'
        return response

# --- Autocomplete API Views ---

class SupplierAutocompleteView(LoginRequiredMixin, View):
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        comp_id = request.session.get('compid')
        
        suppliers = Supplier.objects.filter(
            comp_id=comp_id,
            supplier_name__icontains=query
        ).order_by('supplier_name')[:10] # Limit to 10 for performance

        # Return as HTML list for HTMX hx-target dropdown
        html_suggestions = ""
        for supplier in suppliers:
            html_suggestions += f"<a href='#' class='block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100' " \
                                f"x-on:click.prevent=\"$event.target.closest('div[x-data]').querySelector('input').value = '{supplier.supplier_name} [{supplier.supplier_id}]'; open_suggestions = false;\">" \
                                f"{supplier.supplier_name} [{supplier.supplier_id}]</a>"
        return HttpResponse(html_suggestions)


class EmployeeAutocompleteView(LoginRequiredMixin, View):
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        comp_id = request.session.get('compid')

        employees = Employee.objects.filter(
            comp_id=comp_id,
            employee_name__icontains=query
        ).order_by('employee_name')[:10]

        html_suggestions = ""
        for employee in employees:
            html_suggestions += f"<a href='#' class='block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100' " \
                                f"x-on:click.prevent=\"$event.target.closest('div[x-data]').querySelector('input').value = '{employee.employee_name} [{employee.emp_id}]'; open_suggestions = false;\">" \
                                f"{employee.employee_name} [{employee.emp_id}]</a>"
        return HttpResponse(html_suggestions)

# Main machinery list view (for redirection from proceed/cancel)
class MachineryListView(LoginRequiredMixin, ListView):
    model = Machinery
    template_name = 'machinery/machinery_list.html' # This is a new list page
    context_object_name = 'machinery_items'
    paginate_by = 10 # Example pagination
    
    def get_queryset(self):
        comp_id = self.request.session.get('compid')
        return Machinery.objects.filter(comp_id=comp_id).order_by('-id').select_related('item', 'supplier', 'incharge')

# Helper for initial categories and subcategories for filtering
class FilterDropdownsView(LoginRequiredMixin, TemplateView):
    # This view would be part of an AJAX call to refresh subcategories based on category selection
    def get(self, request, *args, **kwargs):
        category_id = request.GET.get('category_id')
        subcategories = []
        if category_id and category_id != "Select Category":
            subcategories = SubCategory.objects.filter(cid__cid=category_id).order_by('sc_name')
        
        options_html = '<option value="Select SubCategory">Select SubCategory</option>'
        for subcat in subcategories:
            options_html += f'<option value="{subcat.scid}">{subcat.symbol} - {subcat.sc_name}</option>'
        
        return HttpResponse(options_html)

```

### 4.4 Templates (`machinery/templates/machinery/`)

**Task:** Create templates for each view, leveraging HTMX and Alpine.js.

**Instructions:**
- `machinery_detail.html`: Main page, uses Alpine.js for tab switching and HTMX for partial content loading.
- `_machinery_master_form.html`: Partial for the "Machine" tab.
- `_processes_available_table.html`: Partial for left processes table.
- `_processes_selected_table.html`: Partial for right processes table.
- `_spares_available_table.html`: Partial for left spares table with filters.
- `_spares_selected_table.html`: Partial for right spares table.
- `machinery_list.html`: Simple list page for redirection.
- Modals handled by Alpine.js in `machinery_detail.html`.

**`machinery/templates/machinery/machinery_detail.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6">
        <div class="flex justify-between items-center mb-6 border-b pb-4">
            <h2 class="text-2xl font-bold text-gray-800">Machinery Details - New</h2>
            {% if machinery_instance_id %}
            <span class="text-lg font-semibold text-blue-600">Editing Machine ID: {{ machinery_instance_id }}</span>
            {% endif %}
        </div>

        <div class="mb-4 bg-gray-100 p-3 rounded-md">
            <h3 class="text-lg font-semibold text-gray-700">Item Details:</h3>
            <div class="flex flex-wrap text-sm text-gray-600">
                <p class="mr-6"><strong>Machine Code:</strong> <span class="font-bold">{{ main_item_code }}</span></p>
                <p class="mr-6"><strong>UOM:</strong> <span class="font-bold">{{ main_item_uom }}</span></p>
                <p><strong>Name:</strong> {{ main_item_desc }}</p>
            </div>
        </div>

        <form method="post" enctype="multipart/form-data" 
              hx-post="{% if machinery_instance_id %}{% url 'machinery_detail' machinery_instance_id %}{% else %}{% url 'machinery_detail_new' item.id %}{% endif %}" 
              hx-swap="outerHTML" 
              hx-target="#main-content-area"
              hx-indicator="#loading-indicator">
            {% csrf_token %}

            <div x-data="{ activeTab: 'machine', insurance_enabled: '{{ form.insurance.initial|yesno:"True","False" }}' === 'True' }" class="w-full">
                <!-- Tab Headers -->
                <div class="flex border-b border-gray-200">
                    <button type="button" @click="activeTab = 'machine'" :class="{'border-b-2 border-blue-500 text-blue-600': activeTab === 'machine'}" class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-blue-600 focus:outline-none">Machine</button>
                    <button type="button" @click="activeTab = 'functions'" :class="{'border-b-2 border-blue-500 text-blue-600': activeTab === 'functions'}" class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-blue-600 focus:outline-none">Functions</button>
                    <button type="button" @click="activeTab = 'spare'" :class="{'border-b-2 border-blue-500 text-blue-600': activeTab === 'spare'}" class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-blue-600 focus:outline-none">Spare</button>
                </div>

                <!-- Tab Contents -->
                <div class="py-4">
                    <!-- Machine Tab -->
                    <div x-show="activeTab === 'machine'" class="space-y-4">
                        {% include 'machinery/_machinery_master_form.html' %}
                        
                        <!-- Hidden fields to pass selected processes/spares to the main form -->
                        <input type="hidden" name="selected_processes_json" :value="JSON.stringify(window.selectedProcesses)" x-ref="processesInput">
                        <input type="hidden" name="selected_spares_json" :value="JSON.stringify(window.selectedSpares)" x-ref="sparesInput">
                    </div>

                    <!-- Functions Tab -->
                    <div x-show="activeTab === 'functions'" class="space-y-4"
                         hx-trigger="load once, refreshSelectedProcesses from:body" 
                         hx-get="{% url 'processes_selected_table' %}" 
                         hx-target="#selected-processes-container" 
                         hx-swap="innerHTML">
                        <fieldset class="border border-gray-300 p-4 rounded-md">
                            <legend class="px-2 text-lg font-semibold text-gray-700">Manage Functions</legend>
                            <div class="flex flex-wrap -mx-2">
                                <div class="w-full lg:w-1/2 px-2 mb-4 lg:mb-0">
                                    <h4 class="font-semibold text-gray-700 mb-2">Available Processes</h4>
                                    <div id="available-processes-container"
                                         hx-get="{% url 'processes_available_table' %}"
                                         hx-trigger="load once"
                                         hx-target="this"
                                         hx-swap="innerHTML">
                                        <div class="text-center">Loading Available Processes...</div>
                                    </div>
                                </div>
                                <div class="w-full lg:w-1/2 px-2">
                                    <h4 class="font-semibold text-gray-700 mb-2">Selected Processes</h4>
                                    <div id="selected-processes-container">
                                        <div class="text-center">Loading Selected Processes...</div>
                                    </div>
                                </div>
                            </div>
                        </fieldset>
                    </div>

                    <!-- Spare Tab -->
                    <div x-show="activeTab === 'spare'" class="space-y-4"
                         hx-trigger="load once, refreshSelectedSpares from:body" 
                         hx-get="{% url 'spares_selected_table' %}" 
                         hx-target="#selected-spares-container" 
                         hx-swap="innerHTML">
                        <fieldset class="border border-gray-300 p-4 rounded-md">
                            <legend class="px-2 text-lg font-semibold text-gray-700">Manage Spares</legend>
                            <div class="flex flex-wrap -mx-2">
                                <div class="w-full lg:w-1/2 px-2 mb-4 lg:mb-0">
                                    <h4 class="font-semibold text-gray-700 mb-2">Available Spares</h4>
                                    <div id="available-spares-container"
                                         hx-get="{% url 'spares_available_table' item.id %}" {# Pass main item ID for exclusion #}
                                         hx-trigger="load once, reloadAvailableSpares from:body"
                                         hx-target="this"
                                         hx-swap="innerHTML">
                                        <div class="text-center">Loading Available Spares...</div>
                                    </div>
                                </div>
                                <div class="w-full lg:w-1/2 px-2">
                                    <h4 class="font-semibold text-gray-700 mb-2">Selected Spares</h4>
                                    <div id="selected-spares-container">
                                        <div class="text-center">Loading Selected Spares...</div>
                                    </div>
                                </div>
                            </div>
                        </fieldset>
                    </div>
                </div>
            </div>

            <!-- Global Action Buttons -->
            <div class="mt-6 flex justify-end space-x-4">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow-md">
                    Proceed
                </button>
                <a href="{% url 'machinery_list' %}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded shadow-md">
                    Cancel
                </a>
            </div>
        </form>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    // Global Alpine.js data for selected processes and spares
    // These will be synchronized by HTMX and submitted with the main form
    document.addEventListener('alpine:init', () => {
        // Initialize global store for processes
        Alpine.store('processes', {
            items: [],
            init() {
                // Initialize from the hidden input if it exists (e.g., on edit)
                const initialProcessesJson = document.querySelector('input[name="selected_processes_json_from_server"]');
                if (initialProcessesJson && initialProcessesJson.value) {
                    this.items = JSON.parse(initialProcessesJson.value);
                }
                window.selectedProcesses = this.items; // Expose to global scope for form submission
            },
            add(processId) {
                if (!this.items.includes(processId)) {
                    this.items.push(processId);
                    window.selectedProcesses = this.items;
                }
            },
            remove(processId) {
                this.items = this.items.filter(id => id !== processId);
                window.selectedProcesses = this.items;
            }
        });

        // Initialize global store for spares
        Alpine.store('spares', {
            items: [], // Stores objects like {item_id: X, qty: Y}
            init() {
                const initialSparesJson = document.querySelector('input[name="selected_spares_json_from_server"]');
                if (initialSparesJson && initialSparesJson.value) {
                    this.items = JSON.parse(initialSparesJson.value);
                }
                window.selectedSpares = this.items; // Expose to global scope for form submission
            },
            add(item_id, qty) {
                const existingIndex = this.items.findIndex(s => s.item_id === item_id);
                if (existingIndex > -1) {
                    this.items[existingIndex].qty = qty; // Update quantity
                } else {
                    this.items.push({ item_id: item_id, qty: qty });
                }
                window.selectedSpares = this.items;
            },
            remove(item_id) {
                this.items = this.items.filter(s => s.item_id !== item_id);
                window.selectedSpares = this.items;
            }
        });
    });

    // Event listener for HTMX to initialize DataTables on dynamically loaded content
    document.body.addEventListener('htmx:afterSwap', function (evt) {
        if (evt.detail.target.id.includes('Table')) {
            $(evt.detail.target).find('table').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
            });
        }
        // Specific listener for selected processes/spares tables to update Alpine store
        if (evt.detail.target.id === 'selected-processes-container') {
             const selectedIdsElement = evt.detail.target.querySelector('input[name="selected_process_ids_json"]');
             if (selectedIdsElement && selectedIdsElement.value) {
                 Alpine.store('processes').items = JSON.parse(selectedIdsElement.value);
                 window.selectedProcesses = Alpine.store('processes').items; // Update global reference
             }
        }
        if (evt.detail.target.id === 'selected-spares-container') {
             const selectedSparesElement = evt.detail.target.querySelector('input[name="selected_spares_json"]');
             if (selectedSparesElement && selectedSparesElement.value) {
                 Alpine.store('spares').items = JSON.parse(selectedSparesElement.value);
                 window.selectedSpares = Alpine.store('spares').items; // Update global reference
             }
        }
    });

    // HTMX triggered event for showing messages (from Django messages framework)
    document.body.addEventListener('showMessage', function(evt) {
        // You'd typically have a dedicated message display area
        // For simplicity, using alert or a temporary div
        if (evt.detail && evt.detail.message) {
            alert(evt.detail.message); // Replace with a more sophisticated message display
        }
        // You can also use Django messages with HTMX
        // messages.success(request, 'Item added!') -> hx-trigger="messages"
        // And then a listener for 'messages' event to show them
    });

    // Function to handle autocomplete item selection
    window.selectAutocompleteItem = function(event, inputElement, suggestionList) {
        inputElement.value = event.target.innerText;
        suggestionList.innerHTML = ''; // Clear suggestions
    };

</script>
{% endblock %}
```

**`machinery/templates/machinery/_machinery_master_form.html`**
This partial only contains the main form fields.

```html
<div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
    <div>
        <label for="{{ form.model_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.model_name.label }}
        </label>
        {{ form.model_name }}
        {% if form.model_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.model_name.errors }}</p>{% endif %}
    </div>
    <div>
        <label for="{{ form.make.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.make.label }}
        </label>
        {{ form.make }}
        {% if form.make.errors %}<p class="text-red-500 text-xs mt-1">{{ form.make.errors }}</p>{% endif %}
    </div>
    <div>
        <label for="{{ form.purchase_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.purchase_date.label }}
        </label>
        {{ form.purchase_date }}
        {% if form.purchase_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.purchase_date.errors }}</p>{% endif %}
    </div>
    <div>
        <label for="{{ form.capacity.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.capacity.label }}
        </label>
        {{ form.capacity }}
        {% if form.capacity.errors %}<p class="text-red-500 text-xs mt-1">{{ form.capacity.errors }}</p>{% endif %}
    </div>
    <div>
        <label for="{{ form.cost.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.cost.label }}
        </label>
        {{ form.cost }}
        {% if form.cost.errors %}<p class="text-red-500 text-xs mt-1">{{ form.cost.errors }}</p>{% endif %}
    </div>
    <div x-data="{ open_suggestions: false, debounce_timeout: null }">
        <label for="{{ form.supplier_name_autocomplete.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.supplier_name_autocomplete.label }}
        </label>
        {{ form.supplier_name_autocomplete }}
        <div id="supplier-suggestions" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg mt-1 w-auto max-h-48 overflow-y-auto"
             x-show="open_suggestions" x-ref="suggestions_list" @click.outside="open_suggestions = false">
             <!-- Suggestions loaded here by HTMX -->
        </div>
        {% if form.supplier_name_autocomplete.errors %}<p class="text-red-500 text-xs mt-1">{{ form.supplier_name_autocomplete.errors }}</p>{% endif %}
    </div>
    <div>
        <label for="{{ form.life_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.life_date.label }}
        </label>
        {{ form.life_date }}
        {% if form.life_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.life_date.errors }}</p>{% endif %}
    </div>
    <div>
        <label for="{{ form.warranty_expiry_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.warranty_expiry_date.label }}
        </label>
        {{ form.warranty_expiry_date }}
        {% if form.warranty_expiry_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.warranty_expiry_date.errors }}</p>{% endif %}
    </div>
    <div>
        <label class="block text-sm font-medium text-gray-700">{{ form.insurance.label }}</label>
        {{ form.insurance }}
        {% if form.insurance.errors %}<p class="text-red-500 text-xs mt-1">{{ form.insurance.errors }}</p>{% endif %}
    </div>
    <div>
        <label for="{{ form.insurance_expiry_date.id_for_label }}" class="block text-sm font-medium text-gray-700" x-show="insurance_enabled">
            {{ form.insurance_expiry_date.label }}
        </label>
        {{ form.insurance_expiry_date }}
        {% if form.insurance_expiry_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.insurance_expiry_date.errors }}</p>{% endif %}
    </div>
    <div>
        <label for="{{ form.put_to_use.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.put_to_use.label }}
        </label>
        {{ form.put_to_use }}
        {% if form.put_to_use.errors %}<p class="text-red-500 text-xs mt-1">{{ form.put_to_use.errors }}</p>{% endif %}
    </div>
    <div>
        <label for="{{ form.received_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.received_date.label }}
        </label>
        {{ form.received_date }}
        {% if form.received_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.received_date.errors }}</p>{% endif %}
    </div>
    <div x-data="{ open_suggestions: false, debounce_timeout: null }">
        <label for="{{ form.incharge_autocomplete.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.incharge_autocomplete.label }}
        </label>
        {{ form.incharge_autocomplete }}
        <div id="incharge-suggestions" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg mt-1 w-auto max-h-48 overflow-y-auto"
             x-show="open_suggestions" x-ref="suggestions_list" @click.outside="open_suggestions = false">
             <!-- Suggestions loaded here by HTMX -->
        </div>
        {% if form.incharge_autocomplete.errors %}<p class="text-red-500 text-xs mt-1">{{ form.incharge_autocomplete.errors }}</p>{% endif %}
    </div>
    <div>
        <label for="{{ form.location_fk.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.location_fk.label }}
        </label>
        {{ form.location_fk }}
        {% if form.location_fk.errors %}<p class="text-red-500 text-xs mt-1">{{ form.location_fk.errors }}</p>{% endif %}
    </div>
    <div>
        <label for="{{ form.uploaded_image.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.uploaded_image.label }}
        </label>
        {{ form.uploaded_image }}
        {% if form.uploaded_image.errors %}<p class="text-red-500 text-xs mt-1">{{ form.uploaded_image.errors }}</p>{% endif %}
    </div>
    <div>
        <label for="{{ form.pm_days.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.pm_days.label }}
        </label>
        {{ form.pm_days }}
        {% if form.pm_days.errors %}<p class="text-red-500 text-xs mt-1">{{ form.pm_days.errors }}</p>{% endif %}
    </div>
</div>
```

**`machinery/templates/machinery/_processes_available_table.html`**

```html
<table id="availableProcessesTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Process</th>
        </tr>
    </thead>
    <tbody>
        {% for process in processes %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                <input type="checkbox" name="process_ids[]" value="{{ process.id }}" id="chk_process_{{ process.id }}"
                       x-bind:checked="$store.processes.items.includes({{ process.id }})"
                       x-on:change="$event.target.checked ? $store.processes.add({{ process.id }}) : $store.processes.remove({{ process.id }})"
                       class="rounded text-blue-600 focus:ring-blue-500">
            </td>
            <td class="py-2 px-4 border-b border-gray-200">{{ process.process_name }}</td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="3" class="py-4 px-4 text-center text-red-700 font-semibold text-lg">No data to display!</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
<div class="mt-4 text-center">
    <button type="button" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-post="{% url 'process_add_htmx' %}"
            hx-include="#availableProcessesTable input[type=checkbox]:checked"
            hx-target="#selected-processes-container"
            hx-swap="innerHTML"
            _="on click call Alpine.store('processes').items = [] then reset form with id 'availableProcessesTable'
            then send refreshSelectedProcesses"
            >
        Add Selected Processes
    </button>
</div>

<script>
    $(document).ready(function() {
        $('#availableProcessesTable').DataTable({
            "paging": false,
            "info": false,
            "searching": true,
            "order": [[2, 'asc']] // Order by Process column
        });
    });
</script>
```

**`machinery/templates/machinery/_processes_selected_table.html`**

```html
<table id="selectedProcessesTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Process</th>
        </tr>
    </thead>
    <tbody>
        {% for process_obj in selected_processes_data %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button type="button" 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                        hx-delete="{% url 'process_delete_htmx' process_obj.id %}"
                        hx-target="#selected-processes-container"
                        hx-swap="innerHTML"
                        hx-confirm="Are you sure you want to delete this process?"
                        hx-trigger="click"
                        _="on click call Alpine.store('processes').remove({{ process_obj.id }}) then send refreshSelectedProcesses">
                    Delete
                </button>
            </td>
            <td class="py-2 px-4 border-b border-gray-200">{{ process_obj.process_name }}</td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="3" class="py-4 px-4 text-center text-red-700 font-semibold text-lg">No data to display!</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<!-- Hidden input to pass selected IDs for Alpine.js state init -->
<input type="hidden" name="selected_process_ids_json_from_server" value="{{ selected_process_ids_json }}">

<script>
    $(document).ready(function() {
        $('#selectedProcessesTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "order": [] // Disable initial sorting if order is handled by session
        });
    });
</script>
```

**`machinery/templates/machinery/_spares_available_table.html`**

```html
<div class="mb-4 flex flex-wrap gap-2 items-center">
    <select name="category_id" 
            class="box3" 
            hx-get="{% url 'filter_subcategories' %}"
            hx-target="#subcategory-dropdown"
            hx-swap="innerHTML"
            hx-trigger="change"
            >
        <option value="Select Category">Select Category</option>
        {% for category in categories %}
        <option value="{{ category.cid }}" {% if selected_category == category.cid|stringformat:"s" %}selected{% endif %}>{{ category.category_name }}</option>
        {% endfor %}
    </select>

    <select name="subcategory_id" id="subcategory-dropdown" class="box3">
        <option value="Select SubCategory">Select SubCategory</option>
        {% for subcategory in subcategories %}
        <option value="{{ subcategory.scid }}" {% if selected_subcategory == subcategory.scid|stringformat:"s" %}selected{% endif %}>{{ subcategory.symbol }} - {{ subcategory.sc_name }}</option>
        {% endfor %}
    </select>

    <select name="search_field" class="box3" x-data="{ search_by_location: false }" @change="search_by_location = ($event.target.value === 'tblDG_Item_Master.Location')">
        {% for option in search_options %}
        <option value="{{ option.value }}" {% if selected_search_field == option.value %}selected{% endif %}>{{ option.label }}</option>
        {% endfor %}
    </select>

    <input type="text" name="search_term" value="{{ selected_search_term }}" class="box3" placeholder="Search term..." x-show="!search_by_location">
    <select name="search_location_id" class="box3" x-show="search_by_location">
        <option value="Select Location">Select Location</option>
        {% for location in locations %}
        <option value="{{ location.id }}">{{ location.location_label }} {{ location.location_no }}</option>
        {% endfor %}
    </select>

    <button type="button" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'spares_available_table' item_id_exclude %}"
            hx-include="closest form"
            hx-target="#available-spares-container"
            hx-swap="innerHTML">
        Search
    </button>
</div>

<table id="availableSparesTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
        </tr>
    </thead>
    <tbody>
        {% for spare in available_spares %}
        <tr x-data="{ qty: '' }">
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                <input type="checkbox" name="chkSpare_{{ spare.id }}" value="on" id="chk_spare_{{ spare.id }}"
                       x-bind:checked="$store.spares.items.some(s => s.item_id === {{ spare.id }})"
                       x-on:change="if (!$event.target.checked) $store.spares.remove({{ spare.id }})"
                       class="rounded text-blue-600 focus:ring-blue-500">
            </td>
            <td class="py-2 px-4 border-b border-gray-200">{{ spare.item_code }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ spare.manf_desc|truncatechars:80 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ spare.uom_basic.symbol }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">
                <input type="number" name="txtQty_{{ spare.id }}" 
                       x-model="qty"
                       x-init="qty = $store.spares.items.find(s => s.item_id === {{ spare.id }})?.qty || ''"
                       x-on:input="if($event.target.closest('tr').querySelector('input[type=checkbox]').checked) $store.spares.add({{ spare.id }}, parseFloat(qty) || 0)"
                       class="box3 w-20 text-right border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-4 px-4 text-center text-red-700 font-semibold text-lg">No data to display!</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
<div class="mt-4 text-center">
    <button type="button" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-post="{% url 'spare_add_htmx' %}"
            hx-include="#availableSparesTable input[type=checkbox]:checked, #availableSparesTable input[type=number]"
            hx-target="#selected-spares-container"
            hx-swap="innerHTML"
            hx-trigger="click"
            _="on click send refreshSelectedSpares">
        Add Selected Spares
    </button>
</div>

<script>
    $(document).ready(function() {
        $('#availableSparesTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "order": [[0, 'asc']] // SN column
        });
    });
</script>
```

**`machinery/templates/machinery/_spares_selected_table.html`**

```html
<table id="selectedSparesTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Unit</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
        </tr>
    </thead>
    <tbody>
        {% for spare_data in selected_spares_data %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button type="button" 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                        hx-delete="{% url 'spare_delete_htmx' spare_data.item.id %}"
                        hx-target="#selected-spares-container"
                        hx-swap="innerHTML"
                        hx-confirm="Are you sure you want to delete this spare?"
                        hx-trigger="click"
                        _="on click call Alpine.store('spares').remove({{ spare_data.item.id }}) then send refreshSelectedSpares">
                    Delete
                </button>
            </td>
            <td class="py-2 px-4 border-b border-gray-200">{{ spare_data.item.item_code }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ spare_data.item.manf_desc }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ spare_data.item.uom_basic.symbol }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ spare_data.qty }}</td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-4 px-4 text-center text-red-700 font-semibold text-lg">No data to display!</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<!-- Hidden input to pass selected IDs for Alpine.js state init -->
<input type="hidden" name="selected_spares_json" value="{{ selected_spares_json }}">

<script>
    $(document).ready(function() {
        $('#selectedSparesTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "order": [] // Disable initial sorting if order is handled by session
        });
    });
</script>
```

**`machinery/templates/machinery/machinery_list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Machinery List</h2>
        <a href="{% url 'machinery_detail_new' 1 %}" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow-md">
            Add New Machinery
        </a>
    </div>

    {% if messages %}
    <div class="mb-4">
        {% for message in messages %}
        <div class="bg-{{ message.tags }}-100 border border-{{ message.tags }}-400 text-{{ message.tags }}-700 px-4 py-3 rounded relative" role="alert">
            <span class="block sm:inline">{{ message }}</span>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <div class="bg-white shadow-md rounded-lg p-6">
        <table id="machineryTable" class="min-w-full bg-white">
            <thead>
                <tr>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Model</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Make</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for machine in machinery_items %}
                <tr>
                    <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ machine.item.item_code }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ machine.item.manf_desc }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ machine.model_name }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ machine.make }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">
                        <a href="{% url 'machinery_detail' machine.id %}" class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2">Edit</a>
                        <!-- Add a delete button with HTMX if needed for direct delete from list -->
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="6" class="py-4 px-4 text-center text-red-700 font-semibold text-lg">No machinery records found.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        $('#machineryTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "order": [[0, 'asc']] // Order by SN column
        });
    });
</script>
{% endblock %}
```

### 4.5 URLs (`machinery/urls.py`)

**Task:** Define URL patterns for the views.

**Instructions:**
- Use clear, descriptive URL names.
- Separate URLs for HTMX partials.

```python
from django.urls import path
from .views import (
    MachineryDetailView,
    MachineryListView,
    ProcessesAvailableTableView,
    ProcessesSelectedTableView,
    ProcessAddHTMXView,
    ProcessDeleteHTMXView,
    SparesAvailableTableView,
    SparesSelectedTableView,
    SpareAddHTMXView,
    SpareDeleteHTMXView,
    SupplierAutocompleteView,
    EmployeeAutocompleteView,
    FilterDropdownsView,
)

urlpatterns = [
    # Main Machinery Detail Page (for New/Edit based on URL param)
    # The 'item_id' here refers to the Item (tblDG_Item_Master.Id) for new, or Machinery (tblMS_Master.Id) for edit.
    # We use a single view and logic handles if it's creating new or editing existing based on presence of MId.
    path('detail/new/<int:item_id>/', MachineryDetailView.as_view(), name='machinery_detail_new'), # For creating a new machine for an Item
    path('detail/<int:item_id>/', MachineryDetailView.as_view(), name='machinery_detail'), # For editing an existing machine (item_id is MId here)

    # HTMX Endpoints for Processes Tab
    path('processes/available-table/', ProcessesAvailableTableView.as_view(), name='processes_available_table'),
    path('processes/selected-table/', ProcessesSelectedTableView.as_view(), name='processes_selected_table'),
    path('processes/add/', ProcessAddHTMXView.as_view(), name='process_add_htmx'),
    path('processes/delete/<int:pk>/', ProcessDeleteHTMXView.as_view(), name='process_delete_htmx'),

    # HTMX Endpoints for Spares Tab
    path('spares/available-table/<int:item_id_exclude>/', SparesAvailableTableView.as_view(), name='spares_available_table'),
    path('spares/selected-table/', SparesSelectedTableView.as_view(), name='spares_selected_table'),
    path('spares/add/', SpareAddHTMXView.as_view(), name='spare_add_htmx'),
    path('spares/delete/<int:pk>/', SpareDeleteHTMXView.as_view(), name='spare_delete_htmx'),

    # Autocomplete API Endpoints
    path('api/suppliers-autocomplete/', SupplierAutocompleteView.as_view(), name='suppliers_autocomplete'),
    path('api/employees-autocomplete/', EmployeeAutocompleteView.as_view(), name='employees_autocomplete'),
    
    # Filter dropdowns for spares (e.g., subcategory refresh)
    path('api/filter-subcategories/', FilterDropdownsView.as_view(), name='filter_subcategories'),

    # Main Machinery List View
    path('list/', MachineryListView.as_view(), name='machinery_list'),
]
```

### 4.6 Tests (`machinery/tests.py`)

**Task:** Write comprehensive tests for the model and views.

**Instructions:**
- Include unit tests for model methods and properties.
- Add integration tests for all views, covering GET and POST requests, including HTMX interactions.
- Mock session data for `compid`, `finyear`, and `username` as required.
- Ensure test coverage for all critical paths.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from datetime import date
import json
from unittest.mock import patch

from .models import (
    Machinery, Item, Unit, Category, SubCategory, Supplier, Employee, Process, Location,
    MachineryProcess, MachinerySpare
)
from .forms import MachineryForm, SpareQuantityForm

User = get_user_model()

# --- Model Unit Tests ---

class MachineryModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary dependent objects first
        cls.user = User.objects.create_user(username='testuser', password='password123')
        cls.comp_id = 1
        cls.fin_year_id = 2023

        cls.unit = Unit.objects.create(id=101, symbol='PCS')
        cls.category = Category.objects.create(cid=1, category_name='Heavy Machinery')
        cls.subcategory = SubCategory.objects.create(scid=1, cid=cls.category, symbol='HM', sc_name='Excavators')
        cls.supplier = Supplier.objects.create(supplier_id=1, supplier_name='ABC Suppliers', comp_id=cls.comp_id)
        cls.employee = Employee.objects.create(emp_id=1, employee_name='John Doe', comp_id=cls.comp_id)
        cls.process1 = Process.objects.create(id=1, symbol='A', process_name='Assembly')
        cls.process2 = Process.objects.create(id=2, symbol='B', process_name='Calibration')
        cls.location = Location.objects.create(id=1, location_label='Warehouse', location_no='1A')

        cls.item = Item.objects.create(
            id=1, item_code='MCH001', manf_desc='Excavator XYZ', uom_basic=cls.unit,
            cid=cls.category, scid=cls.subcategory, stock_qty=5.0, location=cls.location,
            _absolute=False, comp_id=cls.comp_id
        )
        cls.spare_item1 = Item.objects.create(
            id=2, item_code='SPR001', manf_desc='Hydraulic Pump', uom_basic=cls.unit,
            cid=cls.category, scid=cls.subcategory, stock_qty=10.0, location=cls.location,
            _absolute=False, comp_id=cls.comp_id
        )
        cls.spare_item2 = Item.objects.create(
            id=3, item_code='SPR002', manf_desc='Air Filter', uom_basic=cls.unit,
            cid=cls.category, scid=cls.subcategory, stock_qty=20.0, location=cls.location,
            _absolute=False, comp_id=cls.comp_id
        )

        cls.machinery_data = {
            'item_id': cls.item.id,
            'make': 'Caterpillar',
            'model_name': '320D',
            'capacity': '20 Tons',
            'purchase_date': '01-01-2022',
            'supplier_name_autocomplete': f"{cls.supplier.supplier_name} [{cls.supplier.supplier_id}]",
            'cost': '150000.00',
            'warranty_expiry_date': '01-01-2025',
            'life_date': '01-01-2032',
            'received_date': '01-02-2022',
            'insurance': '1', # '1' for YES, '0' for NO
            'insurance_expiry_date': '01-01-2023',
            'put_to_use': '01-03-2022',
            'incharge_autocomplete': f"{cls.employee.employee_name} [{cls.employee.emp_id}]",
            'location_fk': cls.location.id,
            'pm_days': '90',
        }
        cls.processes_data = [cls.process1.id, cls.process2.id]
        cls.spares_data = [{'item_id': cls.spare_item1.id, 'qty': 2.0}]

    def test_machinery_creation(self):
        with patch('machinery.models.UtilityFunctions.get_code_from_name_id', side_effect=[self.supplier.supplier_id, self.employee.emp_id]):
            machinery = Machinery.create_new_machinery(
                self.user, self.comp_id, self.fin_year_id, self.item.id,
                self.machinery_data, self.processes_data, self.spares_data
            )

        self.assertIsNotNone(machinery)
        self.assertEqual(machinery.make, 'Caterpillar')
        self.assertEqual(machinery.item, self.item)
        self.assertEqual(machinery.supplier, self.supplier)
        self.assertTrue(machinery.insurance)
        
        self.assertEqual(MachineryProcess.objects.filter(machinery=machinery).count(), 2)
        self.assertEqual(MachinerySpare.objects.filter(machinery=machinery).count(), 1)
        self.assertTrue(MachineryProcess.objects.filter(machinery=machinery, process=self.process1).exists())
        self.assertTrue(MachinerySpare.objects.filter(machinery=machinery, item=self.spare_item1, qty=2.0).exists())

    def test_insurance_no_expiry_date(self):
        data = self.machinery_data.copy()
        data['insurance'] = '0' # No insurance
        data['insurance_expiry_date'] = '' # Ensure it's empty
        
        with patch('machinery.models.UtilityFunctions.get_code_from_name_id', side_effect=[self.supplier.supplier_id, self.employee.emp_id]):
            machinery = Machinery.create_new_machinery(
                self.user, self.comp_id, self.fin_year_id, self.item.id,
                data, self.processes_data, self.spares_data
            )
        self.assertFalse(machinery.insurance)
        self.assertIsNone(machinery.insurance_expiry_date)

    def test_file_upload(self):
        from django.core.files.uploadedfile import SimpleUploadedFile
        test_image = SimpleUploadedFile("test_image.jpg", b"file_content", "image/jpeg")

        with patch('machinery.models.UtilityFunctions.get_code_from_name_id', side_effect=[self.supplier.supplier_id, self.employee.emp_id]):
            machinery = Machinery.create_new_machinery(
                self.user, self.comp_id, self.fin_year_id, self.item.id,
                self.machinery_data, self.processes_data, self.spares_data,
                uploaded_file=test_image
            )
        
        self.assertEqual(machinery.file_name, "test_image.jpg")
        self.assertEqual(machinery.file_size, len(b"file_content"))
        self.assertEqual(machinery.content_type, "image/jpeg")
        self.assertEqual(machinery.file_data, b"file_content")

# --- Form Unit Tests ---

class MachineryFormTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.category = Category.objects.create(cid=1, category_name='Heavy Machinery')
        cls.subcategory = SubCategory.objects.create(scid=1, cid=cls.category, symbol='HM', sc_name='Excavators')
        cls.unit = Unit.objects.create(id=101, symbol='PCS')
        cls.location = Location.objects.create(id=1, location_label='Warehouse', location_no='1A')
        cls.item = Item.objects.create(
            id=1, item_code='MCH001', manf_desc='Excavator XYZ', uom_basic=cls.unit,
            cid=cls.category, scid=cls.subcategory, stock_qty=5.0, location=cls.location,
            _absolute=False, comp_id=1
        )
        cls.supplier = Supplier.objects.create(supplier_id=1, supplier_name='Test Supplier', comp_id=1)
        cls.employee = Employee.objects.create(emp_id=1, employee_name='Test Employee', comp_id=1)
    
    def test_form_valid_data(self):
        data = {
            'make': 'Test Make',
            'model_name': 'Test Model',
            'capacity': '10 Ton',
            'purchase_date': '10-01-2023',
            'cost': '10000.00',
            'life_date': '10-01-2033',
            'warranty_expiry_date': '10-01-2026',
            'insurance': '0',
            'insurance_expiry_date': '', # Not required when insurance is 0
            'put_to_use': '15-01-2023',
            'received_date': '05-01-2023',
            'pm_days': '30',
            'supplier_name_autocomplete': f"{self.supplier.supplier_name} [{self.supplier.supplier_id}]",
            'incharge_autocomplete': f"{self.employee.employee_name} [{self.employee.emp_id}]",
            'location_fk': self.location.id,
            'selected_processes_json': '[]',
            'selected_spares_json': '[]'
        }
        form = MachineryForm(data=data)
        self.assertTrue(form.is_valid(), form.errors)

    def test_form_invalid_insurance_date(self):
        data = {
            'make': 'Test Make', 'model_name': 'Test Model', 'capacity': '10 Ton',
            'purchase_date': '10-01-2023', 'cost': '10000.00', 'life_date': '10-01-2033',
            'warranty_expiry_date': '10-01-2026',
            'insurance': '1', # Insurance enabled
            'insurance_expiry_date': '', # But no date provided
            'put_to_use': '15-01-2023', 'received_date': '05-01-2023', 'pm_days': '30',
            'supplier_name_autocomplete': f"{self.supplier.supplier_name} [{self.supplier.supplier_id}]",
            'incharge_autocomplete': f"{self.employee.employee_name} [{self.employee.emp_id}]",
            'location_fk': self.location.id,
            'selected_processes_json': '[]',
            'selected_spares_json': '[]'
        }
        form = MachineryForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('insurance_expiry_date', form.errors)
        self.assertIn('Insurance Expires on date is required when insurance is enabled.', form.errors['insurance_expiry_date'])

    def test_form_invalid_autocomplete(self):
        data = {
            'make': 'Test Make', 'model_name': 'Test Model', 'capacity': '10 Ton',
            'purchase_date': '10-01-2023', 'cost': '10000.00', 'life_date': '10-01-2033',
            'warranty_expiry_date': '10-01-2026', 'insurance': '0', 'insurance_expiry_date': '',
            'put_to_use': '15-01-2023', 'received_date': '05-01-2023', 'pm_days': '30',
            'supplier_name_autocomplete': 'Invalid Supplier [999]', # Invalid ID
            'incharge_autocomplete': f"{self.employee.employee_name} [{self.employee.emp_id}]",
            'location_fk': self.location.id,
            'selected_processes_json': '[]',
            'selected_spares_json': '[]'
        }
        form = MachineryForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('supplier_name_autocomplete', form.errors)
        self.assertIn('Selected supplier does not exist.', form.errors['supplier_name_autocomplete'])
        
    def test_spare_quantity_form_valid(self):
        form = SpareQuantityForm(data={'qty': 5.5})
        self.assertTrue(form.is_valid())

    def test_spare_quantity_form_invalid(self):
        form = SpareQuantityForm(data={'qty': 'abc'})
        self.assertFalse(form.is_valid())
        self.assertIn('qty', form.errors)
        self.assertIn('Enter a valid numeric value for Quantity.', form.errors['qty'])
        
        form = SpareQuantityForm(data={'qty': 0})
        self.assertFalse(form.is_valid())
        self.assertIn('qty', form.errors)
        self.assertIn('Ensure this value is greater than or equal to 0.01.', form.errors['qty'])

# --- View Integration Tests ---

class MachineryViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.user = User.objects.create_user(username='testuser', password='password123')
        cls.comp_id = 1
        cls.fin_year_id = 2023

        cls.unit = Unit.objects.create(id=101, symbol='PCS')
        cls.category = Category.objects.create(cid=1, category_name='Heavy Machinery')
        cls.subcategory = SubCategory.objects.create(scid=1, cid=cls.category, symbol='HM', sc_name='Excavators')
        cls.supplier = Supplier.objects.create(supplier_id=1, supplier_name='ABC Suppliers', comp_id=cls.comp_id)
        cls.employee = Employee.objects.create(emp_id=1, employee_name='John Doe', comp_id=cls.comp_id)
        cls.process1 = Process.objects.create(id=1, symbol='A', process_name='Assembly')
        cls.process2 = Process.objects.create(id=2, symbol='B', process_name='Calibration')
        cls.location = Location.objects.create(id=1, location_label='Warehouse', location_no='1A')

        cls.item = Item.objects.create(
            id=1, item_code='MCH001', manf_desc='Excavator XYZ', uom_basic=cls.unit,
            cid=cls.category, scid=cls.subcategory, stock_qty=5.0, location=cls.location,
            _absolute=False, comp_id=cls.comp_id
        )
        cls.spare_item1 = Item.objects.create(
            id=2, item_code='SPR001', manf_desc='Hydraulic Pump', uom_basic=cls.unit,
            cid=cls.category, scid=cls.subcategory, stock_qty=10.0, location=cls.location,
            _absolute=False, comp_id=cls.comp_id
        )
        cls.spare_item2 = Item.objects.create(
            id=3, item_code='SPR002', manf_desc='Air Filter', uom_basic=cls.unit,
            cid=cls.category, scid=cls.subcategory, stock_qty=20.0, location=cls.location,
            _absolute=False, comp_id=cls.comp_id
        )

        cls.existing_machinery = Machinery.objects.create(
            id=100, sys_date=date.today(), sys_time=date.today().time(), comp_id=cls.comp_id, fin_year_id=cls.fin_year_id,
            session_user=cls.user, item=cls.item, make='Old Make', model_name='Old Model', capacity='5 Tons',
            purchase_date=date(2021,1,1), supplier=cls.supplier, cost=50000.0, warranty_expiry_date=date(2024,1,1),
            life_date=date(2031,1,1), received_date=date(2021,1,10), insurance=False, put_to_use=date(2021,2,1),
            incharge=cls.employee, location_fk=cls.location, pm_days=100
        )
        MachineryProcess.objects.create(id=100, machinery=cls.existing_machinery, process=cls.process1)
        MachinerySpare.objects.create(id=100, machinery=cls.existing_machinery, item=cls.spare_item1, qty=1.0)


    def setUp(self):
        self.client = Client()
        self.client.login(username='testuser', password='password123')
        # Simulate session variables being set from login or middleware
        session = self.client.session
        session['compid'] = self.comp_id
        session['finyear'] = self.fin_year_id
        session.save()

    def test_machinery_detail_new_get(self):
        response = self.client.get(reverse('machinery_detail_new', args=[self.item.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/machinery_detail.html')
        self.assertContains(response, 'Machinery Details - New')
        self.assertContains(response, self.item.item_code) # Check if item details are displayed
        self.assertIn('selected_processes', self.client.session)
        self.assertIn('selected_spares', self.client.session)
        self.assertEqual(self.client.session['selected_processes'], []) # Should be empty for new
        self.assertEqual(self.client.session['selected_spares'], []) # Should be empty for new

    def test_machinery_detail_edit_get(self):
        response = self.client.get(reverse('machinery_detail', args=[self.existing_machinery.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/machinery_detail.html')
        self.assertContains(response, 'Editing Machine ID:')
        self.assertContains(response, self.existing_machinery.model_name)
        self.assertIn('selected_processes', self.client.session)
        self.assertIn('selected_spares', self.client.session)
        self.assertEqual(self.client.session['selected_processes'], [self.process1.id]) # Should load existing
        self.assertEqual(self.client.session['selected_spares'], [{'item_id': self.spare_item1.id, 'qty': 1.0}]) # Should load existing

    @patch('machinery.models.UtilityFunctions.get_code_from_name_id', side_effect=[1, 1, 1]) # Mock valid IDs
    def test_machinery_detail_new_post_success(self, mock_get_code):
        post_data = {
            'make': 'New Make',
            'model_name': 'New Model',
            'capacity': '10 Ton',
            'purchase_date': '10-01-2023',
            'cost': '10000.00',
            'life_date': '10-01-2033',
            'warranty_expiry_date': '10-01-2026',
            'insurance': '0',
            'insurance_expiry_date': '',
            'put_to_use': '15-01-2023',
            'received_date': '05-01-2023',
            'pm_days': '30',
            'supplier_name_autocomplete': f"{self.supplier.supplier_name} [{self.supplier.supplier_id}]",
            'incharge_autocomplete': f"{self.employee.employee_name} [{self.employee.emp_id}]",
            'location_fk': self.location.id,
            'selected_processes_json': json.dumps([self.process1.id]),
            'selected_spares_json': json.dumps([{'item_id': self.spare_item2.id, 'qty': 3.0}])
        }
        response = self.client.post(reverse('machinery_detail_new', args=[self.item.id]), post_data, follow=True)
        self.assertEqual(response.status_code, 200) # Should redirect to list view on success
        self.assertRedirects(response, reverse('machinery_list'))
        self.assertTrue(Machinery.objects.filter(model_name='New Model').exists())
        self.assertTrue(MachineryProcess.objects.filter(process=self.process1).exists())
        self.assertTrue(MachinerySpare.objects.filter(item=self.spare_item2, qty=3.0).exists())
        self.assertNotIn('selected_processes', self.client.session) # Session should be cleared
        self.assertNotIn('selected_spares', self.client.session) # Session should be cleared

    @patch('machinery.models.UtilityFunctions.get_code_from_name_id', side_effect=[1, 1, 1]) # Mock valid IDs
    def test_machinery_detail_new_post_validation_failure(self, mock_get_code):
        post_data = {
            'make': 'New Make',
            'model_name': 'New Model',
            'capacity': '10 Ton',
            'purchase_date': 'invalid-date', # Invalid date to trigger form error
            'cost': '10000.00',
            'life_date': '10-01-2033',
            'warranty_expiry_date': '10-01-2026',
            'insurance': '0',
            'insurance_expiry_date': '',
            'put_to_use': '15-01-2023',
            'received_date': '05-01-2023',
            'pm_days': '30',
            'supplier_name_autocomplete': f"{self.supplier.supplier_name} [{self.supplier.supplier_id}]",
            'incharge_autocomplete': f"{self.employee.employee_name} [{self.employee.emp_id}]",
            'location_fk': self.location.id,
            'selected_processes_json': json.dumps([self.process1.id]),
            'selected_spares_json': json.dumps([{'item_id': self.spare_item2.id, 'qty': 3.0}])
        }
        response = self.client.post(reverse('machinery_detail_new', args=[self.item.id]), post_data)
        self.assertEqual(response.status_code, 200)
        self.assertFormError(response, 'form', 'purchase_date', 'Enter a valid date.')
        self.assertContains(response, 'Please correct the errors in the form.') # Check for messages

    def test_processes_available_table_get(self):
        response = self.client.get(reverse('processes_available_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/_processes_available_table.html')
        self.assertContains(response, self.process1.process_name)

    def test_processes_selected_table_get(self):
        self.client.session['selected_processes'] = [self.process1.id]
        self.client.session.save()
        response = self.client.get(reverse('processes_selected_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/_processes_selected_table.html')
        self.assertContains(response, self.process1.process_name)
        self.assertContains(response, json.dumps([self.process1.id])) # Check JSON data for Alpine.js

    def test_process_add_htmx(self):
        response = self.client.post(reverse('process_add_htmx'), {'process_ids[]': [self.process2.id]}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content
        self.assertEqual(response['HX-Trigger'], 'refreshSelectedProcesses')
        self.assertEqual(self.client.session['selected_processes'], [self.process2.id])

    def test_process_delete_htmx(self):
        self.client.session['selected_processes'] = [self.process1.id, self.process2.id]
        self.client.session.save()
        response = self.client.delete(reverse('process_delete_htmx', args=[self.process1.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response['HX-Trigger'], 'refreshSelectedProcesses')
        self.assertEqual(self.client.session['selected_processes'], [self.process2.id])

    def test_spares_available_table_get(self):
        response = self.client.get(reverse('spares_available_table', args=[self.item.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/_spares_available_table.html')
        self.assertContains(response, self.spare_item1.item_code)
        self.assertNotContains(response, self.item.item_code) # Should exclude main item

    def test_spares_selected_table_get(self):
        self.client.session['selected_spares'] = [{'item_id': self.spare_item2.id, 'qty': 5.0}]
        self.client.session.save()
        response = self.client.get(reverse('spares_selected_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery/_spares_selected_table.html')
        self.assertContains(response, self.spare_item2.item_code)
        self.assertContains(response, "5.0")
        self.assertContains(response, json.dumps([{'item_id': self.spare_item2.id, 'qty': 5.0}])) # Check JSON data

    def test_spare_add_htmx(self):
        post_data = {
            f'chkSpare_{self.spare_item1.id}': 'on',
            f'txtQty_{self.spare_item1.id}': '2.5'
        }
        response = self.client.post(reverse('spare_add_htmx'), post_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response['HX-Trigger'], 'refreshSelectedSpares')
        self.assertEqual(self.client.session['selected_spares'], [{'item_id': self.spare_item1.id, 'qty': 2.5}])

    def test_spare_delete_htmx(self):
        self.client.session['selected_spares'] = [{'item_id': self.spare_item1.id, 'qty': 2.5}, {'item_id': self.spare_item2.id, 'qty': 3.0}]
        self.client.session.save()
        response = self.client.delete(reverse('spare_delete_htmx', args=[self.spare_item1.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response['HX-Trigger'], 'refreshSelectedSpares')
        self.assertEqual(self.client.session['selected_spares'], [{'item_id': self.spare_item2.id, 'qty': 3.0}])

    def test_supplier_autocomplete_view(self):
        response = self.client.get(reverse('suppliers_autocomplete'), {'q': 'ABC'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, f"{self.supplier.supplier_name} [{self.supplier.supplier_id}]")

    def test_employee_autocomplete_view(self):
        response = self.client.get(reverse('employees_autocomplete'), {'q': 'John'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, f"{self.employee.employee_name} [{self.employee.emp_id}]")
```

---

## Step 5: HTMX and Alpine.js Integration

**Instructions:** The Django code generated above fully integrates HTMX and Alpine.js for dynamic interactions, fulfilling the following requirements:

*   **HTMX for Dynamic Updates:**
    *   **Tab Switching:** `x-show` in Alpine.js hides/shows tab content. `hx-get` is used on the containing `div` for each tab to load its content (processes, spares tables) via HTMX on `load once` and custom `HX-Trigger` events.
    *   **Form Submission:** The main form for `Machinery` uses `hx-post` to submit data without full page refresh. Success/error messages are handled via Django's `messages` and displayed on redirect or re-render.
    *   **Table Reloads:** "Add Process", "Add Spare", "Delete Process", "Delete Spare" buttons/links trigger `hx-post`/`hx-delete` requests. The response from the server includes `HX-Trigger` headers (`refreshSelectedProcesses`, `refreshSelectedSpares`) that tell the client to re-fetch and swap the content of the relevant tables, ensuring real-time updates without full page reloads.
    *   **Filtering/Searching:** The filter dropdowns and search button for available spares (`_spares_available_table.html`) use `hx-get` to reload only the available spares table, passing filter parameters.
    *   **Autocomplete:** `hx-get` on the autocomplete input fields (`supplier_name_autocomplete`, `incharge_autocomplete`) sends requests to dedicated Django views. The response (`html_suggestions`) is `hx-target`ed to a hidden `div` where Alpine.js then renders and manages the clickable suggestions.

*   **Alpine.js for UI State Management:**
    *   **Tab Active State:** An `x-data` object at the top level of the `machinery_detail.html` manages `activeTab`.
    *   **Insurance Visibility:** `x-data` on the form manages `insurance_enabled` which controls `x-show` for the `insurance_expiry_date` field.
    *   **Selected Processes/Spares (Client-side temp data):**
        *   `Alpine.store('processes')` and `Alpine.store('spares')` are used as global stores to keep track of selected processes (list of IDs) and spares (list of {item\_id, qty} objects) on the client side.
        *   Checkboxes and quantity inputs in the available tables (`_processes_available_table.html`, `_spares_available_table.html`) are `x-bind:checked` to and `x-on:change`/`x-on:input` from these Alpine stores.
        *   Hidden input fields in the main form (`selected_processes_json`, `selected_spares_json`) are `x-ref`'ed and `x-bind:value` to the `JSON.stringify` version of these Alpine stores, ensuring the latest client-side selections are sent with the main form submission.
        *   When selected tables are loaded via HTMX, a hidden input within them (`selected_process_ids_json_from_server`, `selected_spares_json`) passes the *initial* session state for Alpine.js to synchronize its store.
    *   **Autocomplete Dropdown Control:** `x-data` on the autocomplete input's parent `div` manages `open_suggestions` to control visibility of the suggestion list. `x-on:click.outside` and `x-on:focus` manage this state.

*   **DataTables for List Views:**
    *   `machinery_list.html` and all partial table templates (`_processes_available_table.html`, `_processes_selected_table.html`, etc.) contain standard HTML `<table>` elements.
    *   A JavaScript block after each table (`<script>$(document).ready(function() { $('#tableName').DataTable({}); });</script>`) initializes DataTables.
    *   A global `htmx:afterSwap` event listener ensures that DataTables is re-initialized whenever new table content is swapped into the DOM by HTMX.

This approach ensures that all interactions are highly dynamic and responsive, avoiding full page reloads for common actions, providing a smooth user experience akin to a Single Page Application (SPA) without the complexity of a full JavaScript framework.

---

## Final Notes

This comprehensive plan provides a roadmap for migrating the ASP.NET machinery details page to a robust, modern Django application.

*   **Placeholders:** Replace `[MODEL_NAME_LOWER]` with `machinery`, `[APP_NAME]` with `machinery`, and other placeholders with actual values from your database and business logic.
*   **Database Mapping:** Ensure that your `settings.py` is configured correctly for your existing database, and Django's `inspectdb` utility (run `python manage.py inspectdb > machinery/models.py` initially) can be a good starting point to generate the initial model structure, which then needs manual refinement as done in this plan.
*   **Authentication:** This plan assumes Django's built-in authentication and `LoginRequiredMixin` are in use. `request.session.get('compid')` and `request.session.get('finyear')` imply these are set in a middleware or a custom user object after login, which is typical for ERP systems.
*   **Error Handling:** The provided code includes basic error handling and messaging. For a production system, implement more robust logging and user-friendly error displays.
*   **File Storage:** While `BinaryField` for `FileData` matches the ASP.NET code, for large files, it's generally best practice to store files on disk and save their paths in the database (e.g., using `models.FileField`). This reduces database load and can improve performance.
*   **Security:** Always implement proper CSRF protection (Django handles this with `{% csrf_token %}`), input validation, and access control.
*   **Scalability:** The HTMX and Alpine.js approach reduces server load compared to traditional full-page reloads, contributing to better scalability for highly interactive applications.
*   **Business Logic:** The "fat model" approach centralizes business rules, making the application easier to understand, test, and maintain.