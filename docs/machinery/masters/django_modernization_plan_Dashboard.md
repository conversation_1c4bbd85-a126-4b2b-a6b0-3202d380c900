## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

The provided ASP.NET `Dashboard.aspx` and its corresponding C# code-behind file are minimal, serving primarily as a placeholder or a shell for a master page. They contain no specific UI controls, data binding, or business logic within their content sections or `Page_Load` event.

Given this lack of specific functionality, we cannot directly extract concrete database schemas, CRUD operations, or detailed UI components. Instead, we will proceed by demonstrating a common scenario for a "Machinery Masters" module: managing `Machine` entities. This approach allows us to illustrate the full Django modernization process as requested, adhering to all specified guidelines, using a relevant, hypothetical entity.

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Since the provided ASP.NET code is empty, we infer a typical table structure for a "Machinery Masters" module. We will assume a `Machine` entity.

-   **Inferred Table Name:** `tbl_machinery_machine`
-   **Inferred Columns:**
    *   `machine_id` (Primary Key, integer)
    *   `machine_name` (string)
    *   `serial_number` (string)
    *   `purchase_date` (date)
    *   `status` (string, e.g., 'Operational', 'Maintenance', 'Retired')
    *   `created_at` (datetime, audit field)
    *   `updated_at` (datetime, audit field)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

As the ASP.NET code is a blank page, no CRUD operations are explicitly defined. For the purpose of demonstrating a complete modernization, we will implement standard CRUD (Create, Read, Update, Delete) operations for the inferred `Machine` entity.

-   **Create:** Functionality to add new machine records.
-   **Read:** Display a list of all machines, with details for individual machines.
-   **Update:** Modify existing machine records.
-   **Delete:** Remove machine records.
-   **Validation Logic:** Simple required field validation for `machine_name` and `serial_number`.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

With no controls in the ASP.NET page, we will implement the following standard Django UI components:

-   **List View:** A table (DataTables powered) to display a list of all `Machine` records, including search, sort, and pagination. It will feature "Edit" and "Delete" buttons for each row, and an "Add New" button.
-   **Form View (Create/Update):** A modal form containing fields for `machine_name`, `serial_number`, `purchase_date`, and `status`.
-   **Delete Confirmation View:** A modal confirmation dialog for deleting a `Machine` record.

All interactions will be driven by HTMX for dynamic partial updates, and Alpine.js for modal management, eliminating full page reloads. Tailwind CSS will be used for styling.

## Step 4: Generate Django Code

### 4.1 Models
**File:** `machinery_masters/models.py`

```python
from django.db import models
from django.utils import timezone

class Machine(models.Model):
    machine_id = models.AutoField(db_column='machine_id', primary_key=True)
    machine_name = models.CharField(db_column='machine_name', max_length=255, verbose_name='Machine Name')
    serial_number = models.CharField(db_column='serial_number', max_length=100, unique=True, verbose_name='Serial Number')
    purchase_date = models.DateField(db_column='purchase_date', null=True, blank=True, verbose_name='Purchase Date')
    status = models.CharField(db_column='status', max_length=50, default='Operational', verbose_name='Status')
    created_at = models.DateTimeField(db_column='created_at', auto_now_add=True, verbose_name='Created At')
    updated_at = models.DateTimeField(db_column='updated_at', auto_now=True, verbose_name='Updated At')

    class Meta:
        managed = False  # Set to True if Django should manage the table (e.g., if creating migrations)
        db_table = 'tbl_machinery_machine'
        verbose_name = 'Machine'
        verbose_name_plural = 'Machines'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.machine_name} ({self.serial_number})"
        
    def is_operational(self):
        """
        Business logic: Checks if the machine is operational.
        """
        return self.status == 'Operational'
        
    def update_status(self, new_status):
        """
        Business logic: Updates the machine's status.
        """
        if new_status in ['Operational', 'Maintenance', 'Retired', 'Damaged']:
            self.status = new_status
            self.save()
            return True
        return False
```

### 4.2 Forms
**File:** `machinery_masters/forms.py`

```python
from django import forms
from .models import Machine

class MachineForm(forms.ModelForm):
    STATUS_CHOICES = [
        ('Operational', 'Operational'),
        ('Maintenance', 'Maintenance'),
        ('Retired', 'Retired'),
        ('Damaged', 'Damaged'),
    ]
    status = forms.ChoiceField(
        choices=STATUS_CHOICES,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    class Meta:
        model = Machine
        fields = ['machine_name', 'serial_number', 'purchase_date', 'status']
        widgets = {
            'machine_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'serial_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'purchase_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            # 'status' widget defined explicitly above for choice field
        }
        
    def clean_serial_number(self):
        """
        Custom validation to ensure serial number is unique (case-insensitive).
        This handles updates where the object's own serial number is allowed.
        """
        serial_number = self.cleaned_data.get('serial_number')
        if serial_number:
            query = Machine.objects.filter(serial_number__iexact=serial_number)
            if self.instance.pk:  # If updating an existing instance
                query = query.exclude(pk=self.instance.pk)
            if query.exists():
                raise forms.ValidationError("This serial number is already in use.")
        return serial_number
```

### 4.3 Views
**File:** `machinery_masters/views.py`

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import Machine
from .forms import MachineForm
from django.db.models import Count

class MachineListView(TemplateView):
    """
    Displays the main page for machines, which will contain the HTMX-loaded table.
    """
    template_name = 'machinery_masters/machine/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Optional: Add dashboard-like summaries if needed
        context['operational_machines_count'] = Machine.objects.filter(status='Operational').count()
        context['total_machines_count'] = Machine.objects.count()
        return context

class MachineTablePartialView(ListView):
    """
    Renders only the DataTables table for machines, designed to be loaded via HTMX.
    """
    model = Machine
    template_name = 'machinery_masters/machine/_machine_table.html'
    context_object_name = 'machines' # This name is used in the template

class MachineCreateView(CreateView):
    """
    Handles creation of new machine records via a modal form.
    """
    model = Machine
    form_class = MachineForm
    template_name = 'machinery_masters/machine/_machine_form.html' # Partial template for HTMX
    success_url = reverse_lazy('machine_list') # Not directly used for HTMX, but good practice

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Machine added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content to return, just trigger client-side
                headers={
                    'HX-Trigger': 'refreshMachineList' # Custom event to refresh the table
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # For HTMX, return the form with errors to be swapped back
            return response # Rendered template with form and errors
        return response

class MachineUpdateView(UpdateView):
    """
    Handles updates to existing machine records via a modal form.
    """
    model = Machine
    form_class = MachineForm
    template_name = 'machinery_masters/machine/_machine_form.html' # Partial template for HTMX
    success_url = reverse_lazy('machine_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Machine updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMachineList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response
        return response

class MachineDeleteView(DeleteView):
    """
    Handles deletion of machine records after confirmation via a modal.
    """
    model = Machine
    template_name = 'machinery_masters/machine/_machine_confirm_delete.html' # Partial template for HTMX
    success_url = reverse_lazy('machine_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Machine deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMachineList'
                }
            )
        return response
```

### 4.4 Templates

**File:** `machinery_masters/machine/list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Machines Dashboard</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'machine_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Machine
        </button>
    </div>

    <div class="bg-white rounded-lg shadow-xl p-6 mb-8">
        <h3 class="text-lg font-semibold text-gray-700 mb-4">Summary Statistics</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="p-4 bg-indigo-50 rounded-lg flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-indigo-700">Total Machines</p>
                    <p class="text-2xl font-bold text-indigo-900">{{ total_machines_count }}</p>
                </div>
                <i class="fa-solid fa-industry text-indigo-500 text-3xl"></i>
            </div>
            <div class="p-4 bg-green-50 rounded-lg flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-green-700">Operational Machines</p>
                    <p class="text-2xl font-bold text-green-900">{{ operational_machines_count }}</p>
                </div>
                <i class="fa-solid fa-check-circle text-green-500 text-3xl"></i>
            </div>
        </div>
    </div>
    
    <div id="machineTable-container"
         hx-trigger="load, refreshMachineList from:body"
         hx-get="{% url 'machine_table' %}"
         hx-swap="innerHTML"
         class="bg-white rounded-lg shadow-xl p-6">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading machines data...</p>
        </div>
    </div>
    
    <!-- Modal for forms and confirmations -->
    <div id="modal"
         class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 my-8"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
    });
</script>
{% endblock %}
```

**File:** `machinery_masters/machine/_machine_table.html` (Partial)

```html
<div class="overflow-x-auto">
    <table id="machineTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Machine Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Serial Number</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Purchase Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for machine in machines %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ machine.machine_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ machine.serial_number }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ machine.purchase_date|default_if_none:"N/A" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                        {% if machine.status == 'Operational' %}bg-green-100 text-green-800
                        {% elif machine.status == 'Maintenance' %}bg-yellow-100 text-yellow-800
                        {% elif machine.status == 'Retired' %}bg-red-100 text-red-800
                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                        {{ machine.status }}
                    </span>
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-semibold py-1 px-3 rounded-md mr-2 shadow-sm transition duration-300 ease-in-out"
                        hx-get="{% url 'machine_edit' machine.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-600 text-white font-semibold py-1 px-3 rounded-md shadow-sm transition duration-300 ease-in-out"
                        hx-get="{% url 'machine_delete' machine.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-3 px-4 text-center text-sm text-gray-500">No machines found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after content is loaded via HTMX
    $(document).ready(function() {
        $('#machineTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "autoWidth": false,
            "dom": '<"flex flex-col md:flex-row justify-between items-center mb-4"lf><"block"t><"flex flex-col md:flex-row justify-between items-center mt-4"ip>',
            "language": {
                "lengthMenu": "Show _MENU_ entries",
                "search": "Search:",
                "paginate": {
                    "first": "First",
                    "last": "Last",
                    "next": "Next",
                    "previous": "Previous"
                }
            }
        });
    });
</script>
```

**File:** `machinery_masters/machine/_machine_form.html` (Partial)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Machine</h3>
    <form hx-post="{{ request.path }}" hx-swap="outerHTML" hx-target="#modalContent" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-300 ease-in-out">
                <span id="form-spinner" class="htmx-indicator mr-2">
                    <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                </span>
                Save Machine
            </button>
        </div>
    </form>
</div>
```

**File:** `machinery_masters/machine/_machine_confirm_delete.html` (Partial)

```html
<div class="p-6 text-center">
    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
        <i class="fa-solid fa-triangle-exclamation text-red-600 text-2xl"></i>
    </div>
    <h3 class="mt-5 text-lg font-medium text-gray-900">Delete Machine</h3>
    <p class="mt-2 text-sm text-gray-500">
        Are you sure you want to delete the machine "<strong>{{ object.machine_name }} ({{ object.serial_number }})</strong>"?
        This action cannot be undone.
    </p>

    <form hx-post="{% url 'machine_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex justify-center space-x-4">
            <button
                type="button"
                class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs
**File:** `machinery_masters/urls.py`

```python
from django.urls import path
from .views import (
    MachineListView,
    MachineCreateView,
    MachineUpdateView,
    MachineDeleteView,
    MachineTablePartialView
)

urlpatterns = [
    # Main list page for machines
    path('machines/', MachineListView.as_view(), name='machine_list'),
    
    # HTMX-specific endpoint for the table content
    path('machines/table/', MachineTablePartialView.as_view(), name='machine_table'),

    # HTMX-specific endpoints for modal forms
    path('machines/add/', MachineCreateView.as_view(), name='machine_add'),
    path('machines/edit/<int:pk>/', MachineUpdateView.as_view(), name='machine_edit'),
    path('machines/delete/<int:pk>/', MachineDeleteView.as_view(), name='machine_delete'),
]
```

### 4.6 Tests
**File:** `machinery_masters/tests.py`

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import Machine
from datetime import date

class MachineModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        Machine.objects.create(
            machine_name='CNC Mill',
            serial_number='SN-001-XYZ',
            purchase_date=date(2020, 1, 15),
            status='Operational'
        )
        Machine.objects.create(
            machine_name='Lathe Machine',
            serial_number='LT-002-ABC',
            purchase_date=date(2018, 5, 20),
            status='Maintenance'
        )
  
    def test_machine_creation(self):
        machine = Machine.objects.get(pk=1) # Using pk=1 as machine_id is AutoField
        self.assertEqual(machine.machine_name, 'CNC Mill')
        self.assertEqual(machine.serial_number, 'SN-001-XYZ')
        self.assertEqual(machine.purchase_date, date(2020, 1, 15))
        self.assertEqual(machine.status, 'Operational')
        self.assertIsNotNone(machine.created_at)
        self.assertIsNotNone(machine.updated_at)
        
    def test_machine_name_label(self):
        machine = Machine.objects.get(pk=1)
        field_label = machine._meta.get_field('machine_name').verbose_name
        self.assertEqual(field_label, 'Machine Name')
        
    def test_serial_number_unique(self):
        with self.assertRaises(Exception): # Expect an integrity error or similar during save
            Machine.objects.create(
                machine_name='Another CNC Mill',
                serial_number='SN-001-XYZ', # Duplicate serial number
                status='Operational'
            )
            
    def test_str_method(self):
        machine = Machine.objects.get(pk=1)
        self.assertEqual(str(machine), 'CNC Mill (SN-001-XYZ)')

    def test_is_operational_method(self):
        operational_machine = Machine.objects.get(serial_number='SN-001-XYZ')
        maintenance_machine = Machine.objects.get(serial_number='LT-002-ABC')
        self.assertTrue(operational_machine.is_operational())
        self.assertFalse(maintenance_machine.is_operational())

    def test_update_status_method(self):
        machine = Machine.objects.get(pk=1)
        self.assertTrue(machine.update_status('Maintenance'))
        self.assertEqual(machine.status, 'Maintenance')
        self.assertFalse(machine.update_status('InvalidStatus')) # Test invalid status
        self.assertEqual(machine.status, 'Maintenance') # Status should not change

class MachineViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        Machine.objects.create(
            machine_name='Test Machine 1',
            serial_number='TEST-001',
            purchase_date=date(2021, 1, 1),
            status='Operational'
        )
        Machine.objects.create(
            machine_name='Test Machine 2',
            serial_number='TEST-002',
            purchase_date=date(2022, 2, 2),
            status='Maintenance'
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view(self):
        response = self.client.get(reverse('machine_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_masters/machine/list.html')
        self.assertIn('total_machines_count', response.context)
        self.assertEqual(response.context['total_machines_count'], 2)
        
    def test_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('machine_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_masters/machine/_machine_table.html')
        self.assertContains(response, 'TEST-001') # Check if data is rendered

    def test_create_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('machine_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_masters/machine/_machine_form.html')
        self.assertContains(response, 'Add Machine') # Check form title
        self.assertTrue('form' in response.context)
        
    def test_create_view_post_htmx_success(self):
        data = {
            'machine_name': 'New Machine',
            'serial_number': 'NEW-003',
            'purchase_date': '2023-03-03',
            'status': 'Operational',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('machine_add'), data, **headers)
        
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMachineList')
        self.assertTrue(Machine.objects.filter(serial_number='NEW-003').exists())
        self.assertGreaterEqual(len(list(messages.get_messages(response.wsgi_request))), 1) # Check for messages

    def test_create_view_post_htmx_invalid(self):
        data = {
            'machine_name': '', # Invalid data
            'serial_number': 'TEST-001', # Duplicate serial number
            'purchase_date': '2023-03-03',
            'status': 'Operational',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('machine_add'), data, **headers)
        
        self.assertEqual(response.status_code, 200) # Should return form with errors
        self.assertTemplateUsed(response, 'machinery_masters/machine/_machine_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'This serial number is already in use.')
        self.assertFalse(Machine.objects.filter(machine_name='').exists())

    def test_update_view_get_htmx(self):
        machine = Machine.objects.get(serial_number='TEST-001')
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('machine_edit', args=[machine.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_masters/machine/_machine_form.html')
        self.assertContains(response, 'Edit Machine')
        self.assertContains(response, 'TEST-001') # Check if current data is pre-filled

    def test_update_view_post_htmx_success(self):
        machine = Machine.objects.get(serial_number='TEST-001')
        data = {
            'machine_name': 'Updated Machine Name',
            'serial_number': 'TEST-001', # Keep same serial number
            'purchase_date': '2021-01-01',
            'status': 'Maintenance',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('machine_edit', args=[machine.pk]), data, **headers)
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMachineList')
        machine.refresh_from_db()
        self.assertEqual(machine.machine_name, 'Updated Machine Name')
        self.assertEqual(machine.status, 'Maintenance')

    def test_update_view_post_htmx_invalid(self):
        machine = Machine.objects.get(serial_number='TEST-001')
        data = {
            'machine_name': 'Updated Machine Name',
            'serial_number': 'TEST-002', # Attempt to use an existing serial number
            'purchase_date': '2021-01-01',
            'status': 'Maintenance',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('machine_edit', args=[machine.pk]), data, **headers)
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_masters/machine/_machine_form.html')
        self.assertContains(response, 'This serial number is already in use.')

    def test_delete_view_get_htmx(self):
        machine = Machine.objects.get(serial_number='TEST-001')
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('machine_delete', args=[machine.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'machinery_masters/machine/_machine_confirm_delete.html')
        self.assertContains(response, 'Delete Machine')
        self.assertContains(response, 'TEST-001')

    def test_delete_view_post_htmx(self):
        machine_to_delete = Machine.objects.get(serial_number='TEST-001')
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('machine_delete', args=[machine_to_delete.pk]), **headers)
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMachineList')
        self.assertFalse(Machine.objects.filter(serial_number='TEST-001').exists())
        self.assertGreaterEqual(len(list(messages.get_messages(response.wsgi_request))), 1)

```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

-   **HTMX for Dynamic Content:**
    *   The `machineTable-container` `div` on `list.html` uses `hx-get` to load `{% url 'machine_table' %}` on `load` and on a custom `refreshMachineList` event. This ensures the table is always up-to-date without a full page refresh.
    *   Add, Edit, and Delete buttons use `hx-get` to fetch their respective forms (`_machine_form.html` or `_machine_confirm_delete.html`) into the `#modalContent` target.
    *   Form submissions (`hx-post`) on `_machine_form.html` and `_machine_confirm_delete.html` target `hx-swap="outerHTML" hx-target="#modalContent"` (for forms to show errors or close on success) or `hx-swap="none"` (for delete) and return `204 No Content` with an `HX-Trigger: refreshMachineList` header to tell the client to refresh the table.
    *   Loading indicators (`hx-indicator`) are used for forms to show spinner during submission.
-   **Alpine.js for Modals:**
    *   The `#modal` element uses `_="on click add .is-active to #modal"` to open the modal when a button is clicked and `_="on click if event.target.id == 'modal' remove .is-active from me"` to close the modal when clicking outside. This is a simple, effective way to manage modal visibility without custom JavaScript.
-   **DataTables for List Views:**
    *   The `_machine_table.html` partial includes a `script` block that initializes DataTables on the `#machineTable`. This script runs every time the partial is loaded by HTMX, ensuring the DataTables functionality is applied to the new content.
    *   DataTables configuration includes `pageLength`, `lengthMenu`, `responsive`, `autoWidth`, and `dom` for a modern, responsive layout.
-   **Seamless Interaction:** All CRUD operations are performed without full page reloads, providing a smooth, single-page application feel while maintaining server-side rendering for initial load and core logic.

## Final Notes

-   The provided Django code includes the application `machinery_masters`. You would need to add this app to your `INSTALLED_APPS` in `settings.py` and include its `urls.py` in your project's main `urls.py`.
-   Remember to configure your database connection in `settings.py` so Django can connect to your existing `tbl_machinery_machine` table. Since `managed = False` is set, Django will not try to create or modify this table via migrations, assuming it already exists.
-   Ensure you have `django-htmx`, `django-crispy-forms` (or similar for form rendering if not using manual rendering), `jQuery`, and `DataTables` CDN links included in your `core/base.html` template.
-   Tailwind CSS classes are extensively used for styling, assuming Tailwind CSS is set up in your Django project.
-   This plan successfully demonstrates a full migration flow for a typical business entity, even starting from an empty ASP.NET page, by inferring logical components based on the module name.