## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database tables and their columns from the ASP.NET code.

Based on the ASP.NET code and common ERP patterns, we infer the following primary tables and their relevant columns. The `clsFunctions.fun.getCode` and `clsFunctions.fun.select` indicate direct database interactions for `tblSD_WO_Category`, `SD_Cust_master`, and filtering on `SD_Cust_WorkOrder_Master`. Stored procedures `Sp_WONO_NotInBom` and `Sp_ForeCast` suggest a Work Order-centric data structure.

**Inferred Tables and Columns:**

*   **`tblSD_WO_Category`** (Work Order Category)
    *   `CId` (Primary Key, Integer)
    *   `CName` (Category Name, String)
    *   `Symbol` (Category Symbol, String)
    *   `CompId` (Company ID, Integer)
*   **`SD_Cust_master`** (Customer Master)
    *   `CustomerId` (Primary Key, Integer)
    *   `CustomerName` (Customer Name, String)
    *   `CompId` (Company ID, Integer)
*   **`SD_Cust_WorkOrder_Master`** (Work Order Master)
    *   `WOId` (Primary Key, Integer) - Inferred from `DataKeyNames="WOId"`
    *   `WONo` (Work Order Number, String) - Crucial identifier
    *   `FinYear` (Financial Year, String)
    *   `CustomerName` (Customer Name, String)
    *   `CustomerId` (Foreign Key to `SD_Cust_master`, Integer)
    *   `EnqId` (Enquiry Number, String)
    *   `PONo` (Purchase Order Number, String)
    *   `POId` (Purchase Order ID, Integer)
    *   `SysDate` (System Date / Generation Date, DateTime)
    *   `EmployeeName` (Generated By Employee Name, String)
    *   `TaskProjectTitle` (Project Title, String) - Inferred from `drpfield` search on "Project Title"
    *   `CId` (Foreign Key to `tblSD_WO_Category`, Integer) - Inferred from `DDLTaskWOType` binding.

### Step 2: Identify Backend Functionality

Task: Determine the core operations and logic within the ASP.NET code.

The application primarily offers complex data retrieval and filtering across four different views of "Project Summary" data, presented via tabs.

*   **Read (Extensive Filtering/Search):**
    *   The `BindDataCust`, `loaddata`, `Bindload`, and `BindSup` methods are responsible for fetching work order data from the database, applying various filters based on dropdown selections (Work Order Category, Search Field type: Customer Name, Enquiry No, PO No, WO No, Project Title), and text box inputs.
    *   These methods dynamically construct SQL queries or pass parameters to stored procedures (`Sp_WONO_NotInBom`, `Sp_ForeCast`).
    *   Pagination is handled by `GridView` controls.
*   **Auto-completion:**
    *   `sql` and `GetCompletionList` WebMethods provide customer name suggestions for search fields.
*   **Navigation/Redirection:**
    *   `SearchGridView1_RowCommand`, `SearchGridView2_RowCommand`, `GridViewSup_RowCommand`: Redirects to detailed reports (`ProjectSummary_Details_Grid.aspx`, `ProjectSummary_Details_Bought.aspx`, `ProjectSummary_Shortage_M.aspx`, etc.) based on selected Work Order and a "SwitchTo" parameter (Bought Out/Manufacturing).
    *   `btnPrint_Click`: Collects selected Work Order Numbers from the "Quantity Wise" tab and redirects to `ProjectSummary_WONo.aspx`.
*   **Session Management:** The application relies heavily on `Session["username"]`, `Session["compid"]`, `Session["finyear"]` for context. This will be mapped to Django's authentication and session system, likely accessed via request context.
*   **UI State Management:** Visibility of textboxes (`txtEnqId`, `TxtSearchValue`, `txtSupplier`, `txtPONo`, etc.) is managed based on dropdown selections, indicating client-side (or postback-driven server-side) UI logic.

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles, mapping them to modern web components.

*   **`TabContainer` (`TabContainer1`):** Will be replaced by a set of `div` elements representing tabs, controlled by HTMX for dynamic loading of content. Each tab will have a unique ID for HTMX targeting.
*   **`GridView` (`SearchGridView1`, `GridView1`, `SearchGridView2`, `GridViewSup`):** These are prime candidates for **DataTables.js**. The data will be loaded via HTMX `hx-get` calls, and DataTables will provide client-side search, sort, and pagination.
*   **`DropDownList` (`DDLTaskWOType`, `DropDownList1`, `DDLTaskWOType1`, `drpfield`, etc.):** Will be standard HTML `<select>` elements. Changes will trigger HTMX `hx-get` or `hx-post` requests to refresh the DataTables.
*   **`TextBox` (`txtEnqId`, `TxtSearchValue`, `txtSupplier`, `txtPONo`, etc.):** Standard HTML `<input type="text">` fields. Auto-completion will be handled by HTMX.
*   **`AutoCompleteExtender` (`TxtSearchValue_AutoCompleteExtender`, etc.):** Replaced by HTMX `hx-get` to a JSON endpoint that provides suggestions as the user types. Alpine.js can manage the display of suggestions.
*   **`Button` (`btnSearch`, `Button1`, `btnPrint`, `btnSearchSH`, `BtnSup`):** Standard HTML `<button>` elements, using HTMX `hx-post` or `hx-get` to trigger data fetches.
*   **`LinkButton` (`BtnWONo`, `BtnWONoSH`, `BtnWONoSup`):** Standard HTML `<a href>` or `<button>` elements that trigger navigation, likely to detail pages.
*   **`CheckBox` (`SelectAll`):** Standard HTML `<input type="checkbox">`, potentially managed by Alpine.js for client-side state and HTMX for server-side processing of selections.
*   **Modal Popups (`ModalPopupExtender`):** While commented out, the presence suggests a need for modal dialogs. Alpine.js is ideal for managing modal visibility, and HTMX for loading content into them.

### Step 4: Generate Django Code

We will create a Django application named `project_summary`.

#### 4.1 Models (`project_summary/models.py`)

This section defines Django models that map to the existing database tables. We set `managed = False` because Django will not manage these tables (they already exist), and `db_table` explicitly points to the legacy table names. Business logic for filtering and data retrieval is encapsulated in custom manager methods.

```python
from django.db import models

# Utility function to simulate fun.getCode for Customer ID extraction
def get_customer_id_from_name_code(name_code_string):
    """
    Extracts customer ID from a string like "Customer Name [CustomerId]".
    Returns None if not found or malformed.
    """
    if not name_code_string:
        return None
    try:
        start_index = name_code_string.rfind('[')
        end_index = name_code_string.rfind(']')
        if start_index != -1 and end_index != -1 and end_index > start_index:
            return name_code_string[start_index + 1:end_index]
    except Exception:
        pass
    return None

class WOCategory(models.Model):
    """Maps to tblSD_WO_Category for Work Order Categories."""
    cid = models.IntegerField(db_column='CId', primary_key=True)
    cname = models.CharField(db_column='CName', max_length=255)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    compid = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblSD_WO_Category'
        verbose_name = 'WO Category'
        verbose_name_plural = 'WO Categories'

    def __str__(self):
        return f"{self.symbol} - {self.cname}"

class Customer(models.Model):
    """Maps to SD_Cust_master for Customer details."""
    customerid = models.IntegerField(db_column='CustomerId', primary_key=True)
    customername = models.CharField(db_column='CustomerName', max_length=255)
    compid = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customername

class WorkOrderManager(models.Manager):
    """Custom manager for WorkOrder to encapsulate complex search logic."""

    def filter_work_orders(self, company_id, fin_year_id, wo_category_id=None,
                           search_field=None, search_value=None):
        """
        Mimics the logic of BindDataCust, Bindload, BindSup methods for filtering Work Orders.
        Uses raw SQL/stored procedures if direct ORM translation is too complex or slow.
        For this example, we'll simulate ORM queries based on parameters.
        The original ASP.NET code calls Sp_WONO_NotInBom multiple times with different filters.
        We'll assume Sp_WONO_NotInBom returns data from SD_Cust_WorkOrder_Master.
        """
        queryset = self.filter(compid=company_id, finyearid=fin_year_id)

        # Apply WO Category filter if provided
        if wo_category_id and wo_category_id != "WO Category": # "WO Category" is placeholder for "Select"
            queryset = queryset.filter(cid=int(wo_category_id))

        # Apply search field and value
        if search_field is not None and search_value:
            if search_field == '0':  # Customer Name
                cust_id = get_customer_id_from_name_code(search_value)
                if cust_id:
                    queryset = queryset.filter(customerid=cust_id)
            elif search_field == '1': # Enquiry No
                queryset = queryset.filter(enqid=search_value)
            elif search_field == '2': # PO No
                queryset = queryset.filter(pono=search_value)
            elif search_field == '3': # WO No
                queryset = queryset.filter(wono=search_value)
            elif search_field == 'project_title': # Project Title (from Quantity Wise tab)
                queryset = queryset.filter(taskprojecttitle__icontains=search_value)

        # In a real scenario, this is where you'd call a stored procedure
        # For instance:
        # with connection.cursor() as cursor:
        #     cursor.execute("EXEC Sp_WONO_NotInBom @CompId=%s, @FinId=%s, @x=%s, @y=%s, @z=%s, @l=%s",
        #                    [company_id, fin_year_id, x_param, y_param, z_param, l_param])
        #     # Then map results to WorkOrder objects or return raw data
        # For now, assuming ORM can handle the general filtering.

        return queryset.order_by('-sysdate') # Order by latest date

class WorkOrder(models.Model):
    """Maps to SD_Cust_WorkOrder_Master for Work Order details."""
    woid = models.IntegerField(db_column='WOId', primary_key=True) # Assuming WOId is the PK
    wono = models.CharField(db_column='WONo', max_length=100, unique=True)
    finyear = models.CharField(db_column='FinYear', max_length=10)
    customername = models.CharField(db_column='CustomerName', max_length=255)
    customerid = models.IntegerField(db_column='CustomerId') # Foreign key to Customer
    enqid = models.CharField(db_column='EnqId', max_length=100, null=True, blank=True)
    pono = models.CharField(db_column='PONo', max_length=100, null=True, blank=True)
    poid = models.IntegerField(db_column='POId', null=True, blank=True)
    sysdate = models.DateField(db_column='SysDate')
    employeename = models.CharField(db_column='EmployeeName', max_length=255, null=True, blank=True)
    taskprojecttitle = models.CharField(db_column='TaskProjectTitle', max_length=500, null=True, blank=True)
    cid = models.IntegerField(db_column='CId') # Foreign key to WOCategory
    compid = models.IntegerField(db_column='CompId')
    finyearid = models.IntegerField(db_column='FinYearId') # Inferred from usage in C#

    objects = WorkOrderManager() # Attach custom manager

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.wono

    # Example of a business logic method
    def get_details_url(self, switch_to_type):
        """
        Determines the appropriate detail URL based on the switch_to_type (1: Bought Out, 2: Manufacturing).
        This replaces the ASP.NET Response.Redirect logic within the model, making views thinner.
        """
        if switch_to_type == "1":
            return f"/project-summary/details/bought/{self.wono}/"
        elif switch_to_type == "2":
            return f"/project-summary/details/manufactured/{self.wono}/"
        return "#" # Default or error URL

    def get_shortage_url(self, switch_to_type):
        """
        Determines the appropriate shortage report URL.
        """
        if switch_to_type == "1":
            return f"/project-summary/shortage/bought/{self.wono}/"
        elif switch_to_type == "2":
            return f"/project-summary/shortage/manufactured/{self.wono}/"
        return "#"

    def get_supplier_url(self, switch_to_type):
        """
        Determines the appropriate supplier report URL.
        """
        if switch_to_type == "1":
            return f"/project-summary/supplier/bought/{self.wono}/"
        elif switch_to_type == "2":
            return f"/project-summary/supplier/manufactured/{self.wono}/"
        return "#"

```

#### 4.2 Forms (`project_summary/forms.py`)

We'll define simple forms for the dropdowns and text inputs, as they are primarily for search/filter and not full CRUD on a single model instance. The dropdowns will be populated dynamically.

```python
from django import forms
from .models import WOCategory, Customer, WorkOrder # Import necessary models

class WOCategoryChoiceField(forms.ModelChoiceField):
    """Custom ModelChoiceField to display 'WO Category' as default option."""
    def __init__(self, *args, **kwargs):
        super().__init__(queryset=WOCategory.objects.all().order_by('cname'),
                         empty_label="WO Category",
                         to_field_name='cid',
                         label="Work Order Type",
                         *args, **kwargs)
        self.widget.attrs.update({'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                                   'hx-get': 'this.closest("form").action',
                                   'hx-trigger': 'change',
                                   'hx-target': '#itemWiseTable-container',
                                   'hx-swap': 'innerHTML'})

    def label_from_instance(self, obj):
        return f"{obj.symbol} - {obj.cname}"


class SearchForm(forms.Form):
    """
    A generic search form for the Project Summary page.
    Fields are conditionally visible in templates using Alpine.js or HTMX.
    """
    wo_category = WOCategoryChoiceField(required=False)
    search_by_field = forms.ChoiceField(
        choices=[
            ('3', 'WO No'),
            ('0', 'Customer Name'),
            ('1', 'Enquiry No'),
            ('2', 'PO No'),
        ],
        required=False,
        label="Search By",
        widget=forms.Select(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                                   'hx-get': 'this.closest("form").action',
                                   'hx-trigger': 'change',
                                   'hx-target': '#itemWiseTable-container',
                                   'hx-swap': 'innerHTML',
                                   'x-on:change': 'if ($el.value === "0") { document.getElementById("id_enq_id").classList.add("hidden"); document.getElementById("id_search_value").classList.remove("hidden"); } else { document.getElementById("id_enq_id").classList.remove("hidden"); document.getElementById("id_search_value").classList.add("hidden"); }'
                                   })
    )
    enq_id = forms.CharField(
        max_length=150,
        required=False,
        label="Enquiry/PO/WO No",
        widget=forms.TextInput(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                                       'placeholder': 'Enter value'})
    )
    search_value = forms.CharField(
        max_length=350,
        required=False,
        label="Customer Name",
        widget=forms.TextInput(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm hidden',
                                       'placeholder': 'Start typing customer name',
                                       'hx-get': '/project-summary/autocomplete/customer/',
                                       'hx-trigger': 'keyup changed delay:500ms',
                                       'hx-target': '#autocomplete-results',
                                       'hx-swap': 'outerHTML'
                                       })
    )
    # This field is for displaying autocomplete results, not a form field itself
    # autocomplete_results is handled in the template for HTMX output
    # `x-model` for Alpine.js will control this input's value

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Ensure search_value is hidden by default, as it was in ASP.NET
        self.fields['search_value'].widget.attrs['class'] += ' hidden'


class QuantityWiseSearchForm(forms.Form):
    wo_category = WOCategoryChoiceField(required=False)
    search_field = forms.ChoiceField(
        choices=[
            ('0', 'Customer'),
            ('1', 'WO No'),
            ('2', 'Project Title'),
        ],
        required=False,
        label="Search By",
        widget=forms.Select(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                                   'x-on:change': 'if ($el.value === "0") { document.getElementById("id_po_no").classList.add("hidden"); document.getElementById("id_supplier").classList.remove("hidden"); } else { document.getElementById("id_po_no").classList.remove("hidden"); document.getElementById("id_supplier").classList.add("hidden"); }'})
    )
    supplier = forms.CharField(
        max_length=250,
        required=False,
        label="Customer/Project/WO No",
        widget=forms.TextInput(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                                       'placeholder': 'Enter value'})
    )
    po_no = forms.CharField( # Renamed from txtPONo to reflect multiple uses in ASP.NET
        max_length=250,
        required=False,
        label="WO No/Project Title",
        widget=forms.TextInput(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm hidden',
                                       'placeholder': 'Enter value'})
    )
    select_all_work_order = forms.BooleanField(
        required=False,
        label="Select All Work Order",
        widget=forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded',
                                           'x-on:change': 'if ($el.checked) { Array.from(document.querySelectorAll("#quantityWiseTable input[type=checkbox]")).forEach(cb => cb.checked = true); } else { Array.from(document.querySelectorAll("#quantityWiseTable input[type=checkbox]")).forEach(cb => cb.checked = false); }'})
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['po_no'].widget.attrs['class'] += ' hidden'


class ShortageWiseSearchForm(forms.Form):
    # This form is identical in structure to SearchForm for Item Wise.
    # It demonstrates the DRY principle and potential for component reuse.
    wo_category = WOCategoryChoiceField(required=False)
    search_by_field = forms.ChoiceField(
        choices=[
            ('3', 'WO No'),
            ('0', 'Customer Name'),
            ('1', 'Enquiry No'),
            ('2', 'PO No'),
        ],
        required=False,
        label="Search By",
        widget=forms.Select(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                                   'hx-get': 'this.closest("form").action',
                                   'hx-trigger': 'change',
                                   'hx-target': '#shortageWiseTable-container',
                                   'hx-swap': 'innerHTML',
                                   'x-on:change': 'if ($el.value === "0") { document.getElementById("id_enq_sh").classList.add("hidden"); document.getElementById("id_search_sh").classList.remove("hidden"); } else { document.getElementById("id_enq_sh").classList.remove("hidden"); document.getElementById("id_search_sh").classList.add("hidden"); }'
                                   })
    )
    enq_sh = forms.CharField(
        max_length=150,
        required=False,
        label="Enquiry/PO/WO No",
        widget=forms.TextInput(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                                       'placeholder': 'Enter value'})
    )
    search_sh = forms.CharField(
        max_length=350,
        required=False,
        label="Customer Name",
        widget=forms.TextInput(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm hidden',
                                       'placeholder': 'Start typing customer name',
                                       'hx-get': '/project-summary/autocomplete/customer/',
                                       'hx-trigger': 'keyup changed delay:500ms',
                                       'hx-target': '#autocomplete-results-sh',
                                       'hx-swap': 'outerHTML'
                                       })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['search_sh'].widget.attrs['class'] += ' hidden'


class SupplierWiseSearchForm(forms.Form):
    # This form is identical in structure to SearchForm for Item Wise.
    # It demonstrates the DRY principle and potential for component reuse.
    wo_category = WOCategoryChoiceField(required=False)
    search_by_field = forms.ChoiceField(
        choices=[
            ('3', 'WO No'),
            ('0', 'Customer Name'),
            ('1', 'Enquiry No'),
            ('2', 'PO No'),
        ],
        required=False,
        label="Search By",
        widget=forms.Select(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                                   'hx-get': 'this.closest("form").action',
                                   'hx-trigger': 'change',
                                   'hx-target': '#supplierWiseTable-container',
                                   'hx-swap': 'innerHTML',
                                   'x-on:change': 'if ($el.value === "0") { document.getElementById("id_text_sup_wo_no").classList.add("hidden"); document.getElementById("id_text_sup_cust").classList.remove("hidden"); } else { document.getElementById("id_text_sup_wo_no").classList.remove("hidden"); document.getElementById("id_text_sup_cust").classList.add("hidden"); }'
                                   })
    )
    text_sup_wo_no = forms.CharField(
        max_length=150,
        required=False,
        label="Enquiry/PO/WO No",
        widget=forms.TextInput(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                                       'placeholder': 'Enter value'})
    )
    text_sup_cust = forms.CharField(
        max_length=350,
        required=False,
        label="Customer Name",
        widget=forms.TextInput(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm hidden',
                                       'placeholder': 'Start typing customer name',
                                       'hx-get': '/project-summary/autocomplete/customer/',
                                       'hx-trigger': 'keyup changed delay:500ms',
                                       'hx-target': '#autocomplete-results-sup',
                                       'hx-swap': 'outerHTML'
                                       })
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['text_sup_cust'].widget.attrs['class'] += ' hidden'
```

#### 4.3 Views (`project_summary/views.py`)

The `ProjectSummaryView` will render the main page with the tab structure. Each tab's content (the DataTables and their respective search forms) will be loaded dynamically via HTMX from separate `ListView` classes. This keeps the views thin and focused on data retrieval for their specific tab.

```python
from django.views.generic import TemplateView, ListView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import redirect
from django.template.loader import render_to_string
from django.conf import settings # Assuming settings contains company_id and fin_year_id

from .models import WorkOrder, Customer, WOCategory, get_customer_id_from_name_code
from .forms import SearchForm, QuantityWiseSearchForm, ShortageWiseSearchForm, SupplierWiseSearchForm

# Placeholder for session data (replace with actual session/auth system)
# In a real app, this would come from request.session or request.user profile
CURRENT_COMPANY_ID = 1
CURRENT_FIN_YEAR_ID = 1

class ProjectSummaryView(TemplateView):
    """
    Main view for the Project Summary page, rendering the tab container.
    Initial load will fetch data for the first tab.
    """
    template_name = 'project_summary/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize forms for initial rendering of the first tab's search area
        context['item_wise_form'] = SearchForm()
        context['quantity_wise_form'] = QuantityWiseSearchForm()
        context['shortage_wise_form'] = ShortageWiseSearchForm()
        context['supplier_wise_form'] = SupplierWiseSearchForm()
        return context

class WorkOrderTableViewBase(ListView):
    """Base class for Work Order table views."""
    model = WorkOrder
    paginate_by = 15 # ASP.NET GridView PageSize
    context_object_name = 'work_orders' # Consistent context name
    # template_name will be set by inheriting classes

    def get_queryset(self):
        # Default empty queryset if no search parameters are given
        # This prevents loading all data on initial page load if not desired
        return self.model.objects.none()

    def get_company_context(self):
        # This would typically get company_id and fin_year_id from user session
        # or profile, similar to ASP.NET Session["compid"] and Session["finyear"].
        # For demonstration, using placeholders.
        company_id = self.request.session.get('compid', CURRENT_COMPANY_ID)
        fin_year_id = self.request.session.get('finyear', CURRENT_FIN_YEAR_ID)
        return company_id, fin_year_id

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass page obj to template for pagination links if needed
        context['page_obj'] = context['paginator'].get_page(context['page_obj'].number)
        return context


class ItemWiseTableView(WorkOrderTableViewBase):
    """
    Handles search and display for the 'Item Wise' tab.
    Returns the partial HTML for the DataTable.
    """
    template_name = 'project_summary/_item_wise_table.html'

    def get_queryset(self):
        company_id, fin_year_id = self.get_company_context()
        form = SearchForm(self.request.GET)
        
        # Keep views thin: delegate complex filtering to the model manager
        if form.is_valid():
            wo_category_id = form.cleaned_data.get('wo_category').cid if form.cleaned_data.get('wo_category') else None
            search_field = form.cleaned_data.get('search_by_field')
            search_value = form.cleaned_data.get('enq_id') or form.cleaned_data.get('search_value') # Use correct field based on dropdown

            return WorkOrder.objects.filter_work_orders(
                company_id=company_id,
                fin_year_id=fin_year_id,
                wo_category_id=wo_category_id,
                search_field=search_field,
                search_value=search_value
            )
        return super().get_queryset()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['item_wise_form'] = SearchForm(self.request.GET or None) # Re-render form with current search
        return context


class QuantityWiseTableView(WorkOrderTableViewBase):
    """
    Handles search and display for the 'Quantity Wise' tab.
    Returns the partial HTML for the DataTable.
    """
    template_name = 'project_summary/_quantity_wise_table.html'

    def get_queryset(self):
        company_id, fin_year_id = self.get_company_context()
        form = QuantityWiseSearchForm(self.request.GET)
        
        if form.is_valid():
            wo_category_id = form.cleaned_data.get('wo_category').cid if form.cleaned_data.get('wo_category') else None
            search_field = form.cleaned_data.get('search_field')
            search_value = form.cleaned_data.get('supplier') or form.cleaned_data.get('po_no')

            # Special handling for project title search which uses LIKE '%...%'
            if search_field == '2': # Project Title
                search_field = 'project_title' # Map to internal model logic

            return WorkOrder.objects.filter_work_orders(
                company_id=company_id,
                fin_year_id=fin_year_id,
                wo_category_id=wo_category_id,
                search_field=search_field,
                search_value=search_value
            )
        return super().get_queryset()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['quantity_wise_form'] = QuantityWiseSearchForm(self.request.GET or None)
        return context


class ShortageWiseTableView(WorkOrderTableViewBase):
    """
    Handles search and display for the 'Shortage Wise' tab.
    Returns the partial HTML for the DataTable.
    """
    template_name = 'project_summary/_shortage_wise_table.html'

    def get_queryset(self):
        company_id, fin_year_id = self.get_company_context()
        form = ShortageWiseSearchForm(self.request.GET)
        
        if form.is_valid():
            wo_category_id = form.cleaned_data.get('wo_category').cid if form.cleaned_data.get('wo_category') else None
            search_field = form.cleaned_data.get('search_by_field')
            search_value = form.cleaned_data.get('enq_sh') or form.cleaned_data.get('search_sh')

            return WorkOrder.objects.filter_work_orders(
                company_id=company_id,
                fin_year_id=fin_year_id,
                wo_category_id=wo_category_id,
                search_field=search_field,
                search_value=search_value
            )
        return super().get_queryset()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['shortage_wise_form'] = ShortageWiseSearchForm(self.request.GET or None)
        return context


class SupplierWiseTableView(WorkOrderTableViewBase):
    """
    Handles search and display for the 'Supplier Wise' tab.
    Returns the partial HTML for the DataTable.
    """
    template_name = 'project_summary/_supplier_wise_table.html'

    def get_queryset(self):
        company_id, fin_year_id = self.get_company_context()
        form = SupplierWiseSearchForm(self.request.GET)
        
        if form.is_valid():
            wo_category_id = form.cleaned_data.get('wo_category').cid if form.cleaned_data.get('wo_category') else None
            search_field = form.cleaned_data.get('search_by_field')
            search_value = form.cleaned_data.get('text_sup_wo_no') or form.cleaned_data.get('text_sup_cust')

            return WorkOrder.objects.filter_work_orders(
                company_id=company_id,
                fin_year_id=fin_year_id,
                wo_category_id=wo_category_id,
                search_field=search_field,
                search_value=search_value
            )
        return super().get_queryset()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['supplier_wise_form'] = SupplierWiseSearchForm(self.request.GET or None)
        return context


class CustomerAutocompleteView(View):
    """
    Provides customer names for autocomplete functionality via HTMX.
    Mimics the ASP.NET WebMethod `sql` and `GetCompletionList`.
    """
    def get(self, request, *args, **kwargs):
        company_id = request.session.get('compid', CURRENT_COMPANY_ID)
        prefix_text = request.GET.get('prefix_text', '')
        
        if not prefix_text:
            return HttpResponse("", status=204) # No content to return if no prefix

        customers = Customer.objects.filter(
            compid=company_id,
            customername__istartswith=prefix_text
        ).order_by('customername')[:10] # Limit to 10 suggestions

        suggestions = [
            f"{customer.customername} [{customer.customerid}]"
            for customer in customers
        ]
        
        # HTMX expects a list of options wrapped in HTML or a JSON response.
        # Returning HTML for HTMX target to display suggestions.
        # This will be replaced into a <div> next to the input.
        response_html = render_to_string(
            'project_summary/_customer_autocomplete_results.html',
            {'suggestions': suggestions},
            request=request
        )
        return HttpResponse(response_html)

class WorkOrderSelectionProceedView(View):
    """
    Handles the 'Proceed' button click from the 'Quantity Wise' tab.
    Collects selected Work Order Numbers and redirects to a report page.
    Mimics btnPrint_Click.
    """
    def post(self, request, *args, **kwargs):
        selected_wos = request.POST.getlist('selected_wonos') # Expects a list of WONos from checkboxes

        if not selected_wos:
            messages.warning(request, 'Please select at least one Work Order to proceed.')
            return HttpResponse(
                status=200, # OK, but client-side alert
                headers={'HX-Trigger': 'showMessage'} # Custom HTMX trigger for Alpine.js message
            )
        
        # Store selected WONos in session, similar to Session["Wono"]
        request.session['selected_wonos_for_report'] = ','.join(selected_wos)

        # Redirect to the report page
        # In a real app, 'project_summary_wonos_report' would be a defined URL name
        redirect_url = reverse_lazy('project_summary_wonos_report') # Assuming this URL exists
        
        # HTMX redirection: Use HX-Redirect header for full page navigation
        return HttpResponse(status=204, headers={'HX-Redirect': str(redirect_url)})

# Placeholder views for navigation targets (replace with actual report views)
class ProjectSummaryDetailsView(TemplateView):
    template_name = 'project_summary/detail_report.html'
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['wono'] = self.kwargs.get('wo_no')
        context['switch_to_type'] = self.kwargs.get('switch_to_type')
        messages.info(self.request, f"Displaying details for WO: {context['wono']} ({context['switch_to_type']})")
        return context

class ProjectSummaryShortageView(TemplateView):
    template_name = 'project_summary/shortage_report.html'
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['wonos'] = self.kwargs.get('wo_no')
        context['switch_to_type'] = self.kwargs.get('switch_to_type')
        messages.info(self.request, f"Displaying shortage report for WO: {context['wonos']} ({context['switch_to_type']})")
        return context

class ProjectSummarySupplierView(TemplateView):
    template_name = 'project_summary/supplier_report.html'
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['wonos'] = self.kwargs.get('wo_no')
        context['switch_to_type'] = self.kwargs.get('switch_to_type')
        messages.info(self.request, f"Displaying supplier report for WO: {context['wonos']} ({context['switch_to_type']})")
        return context

class ProjectSummaryWONoReportView(TemplateView):
    template_name = 'project_summary/multiple_wo_report.html'
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['selected_wonos'] = self.request.session.get('selected_wonos_for_report', 'None')
        messages.info(self.request, f"Report for selected WOs: {context['selected_wonos']}")
        return context
```

#### 4.4 Templates (`project_summary/templates/project_summary/`)

These templates will be organized to extend `core/base.html` and use partials for the dynamic content loaded by HTMX.

**`list.html` (Main Page with Tabs)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ activeTab: 'item_wise', showModal: false, modalContent: '' }">
    <h2 class="text-3xl font-bold text-gray-800 mb-6">Project Summary</h2>

    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-4 py-3" aria-label="Tabs">
                <button 
                    @click="activeTab = 'item_wise'"
                    :class="{'border-indigo-500 text-indigo-600': activeTab === 'item_wise', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'item_wise'}"
                    class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none">
                    Item Wise
                </button>
                <button 
                    @click="activeTab = 'quantity_wise'"
                    :class="{'border-indigo-500 text-indigo-600': activeTab === 'quantity_wise', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'quantity_wise'}"
                    class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none">
                    Quantity Wise
                </button>
                <button 
                    @click="activeTab = 'shortage_wise'"
                    :class="{'border-indigo-500 text-indigo-600': activeTab === 'shortage_wise', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'shortage_wise'}"
                    class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none">
                    Shortage Wise
                </button>
                <button 
                    @click="activeTab = 'supplier_wise'"
                    :class="{'border-indigo-500 text-indigo-600': activeTab === 'supplier_wise', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'supplier_wise'}"
                    class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none">
                    Supplier Wise
                </button>
            </nav>
        </div>

        <div id="tab-content" class="p-6">
            <div x-show="activeTab === 'item_wise'" class="tab-pane">
                <form hx-get="{% url 'project_summary:item_wise_table' %}" hx-target="#itemWiseTable-container" hx-swap="innerHTML" hx-trigger="submit, change from:select, click from:#btnItemSearch">
                    <div class="mb-4 flex space-x-4 items-center">
                        <div>
                            {{ item_wise_form.wo_category.label_tag }}
                            {{ item_wise_form.wo_category }}
                        </div>
                        <div>
                            {{ item_wise_form.search_by_field.label_tag }}
                            {{ item_wise_form.search_by_field }}
                        </div>
                        <div>
                            {{ item_wise_form.enq_id.label_tag }}
                            {{ item_wise_form.enq_id }}
                        </div>
                        <div>
                            {{ item_wise_form.search_value.label_tag }}
                            {{ item_wise_form.search_value }}
                            <div id="autocomplete-results" class="absolute bg-white border border-gray-300 rounded shadow-lg z-10 w-auto max-h-48 overflow-y-auto"></div>
                        </div>
                        <button type="submit" id="btnItemSearch" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mt-auto">Search</button>
                    </div>
                </form>
                <div id="itemWiseTable-container" hx-trigger="load delay:10ms, refreshWorkOrders from:body" hx-get="{% url 'project_summary:item_wise_table' %}" hx-swap="innerHTML">
                    <!-- Item Wise DataTables will be loaded here via HTMX -->
                    <div class="text-center py-8">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p class="mt-2 text-gray-600">Loading Item Wise data...</p>
                    </div>
                </div>
            </div>

            <div x-show="activeTab === 'quantity_wise'" class="tab-pane">
                <form hx-get="{% url 'project_summary:quantity_wise_table' %}" hx-target="#quantityWiseTable-container" hx-swap="innerHTML" hx-trigger="submit, change from:select, click from:#btnQuantitySearch, change from:#id_select_all_work_order">
                    <div class="mb-4 flex space-x-4 items-center">
                        <div>
                            {{ quantity_wise_form.wo_category.label_tag }}
                            {{ quantity_wise_form.wo_category }}
                        </div>
                        <div>
                            {{ quantity_wise_form.search_field.label_tag }}
                            {{ quantity_wise_form.search_field }}
                        </div>
                        <div>
                            {{ quantity_wise_form.supplier.label_tag }}
                            {{ quantity_wise_form.supplier }}
                        </div>
                        <div>
                            {{ quantity_wise_form.po_no.label_tag }}
                            {{ quantity_wise_form.po_no }}
                        </div>
                        <div class="flex items-center space-x-2 pt-2">
                             {{ quantity_wise_form.select_all_work_order }}
                             {{ quantity_wise_form.select_all_work_order.label_tag }}
                        </div>
                        <button type="submit" id="btnQuantitySearch" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mt-auto">Search</button>
                    </div>
                    <div id="quantityWiseTable-container" hx-trigger="load delay:10ms, refreshWorkOrders from:body" hx-get="{% url 'project_summary:quantity_wise_table' %}" hx-swap="innerHTML">
                        <!-- Quantity Wise DataTables will be loaded here via HTMX -->
                        <div class="text-center py-8">
                            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                            <p class="mt-2 text-gray-600">Loading Quantity Wise data...</p>
                        </div>
                    </div>
                    <div class="mt-6 text-center">
                        <button type="submit"
                                hx-post="{% url 'project_summary:proceed_selection' %}"
                                hx-include="#quantityWiseTable-container input[name='selected_wonos']:checked"
                                hx-trigger="click"
                                hx-target="body" hx-swap="none"
                                class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-6 rounded">
                            Proceed
                        </button>
                    </div>
                </form>
            </div>

            <div x-show="activeTab === 'shortage_wise'" class="tab-pane">
                <form hx-get="{% url 'project_summary:shortage_wise_table' %}" hx-target="#shortageWiseTable-container" hx-swap="innerHTML" hx-trigger="submit, change from:select, click from:#btnShortageSearch">
                    <div class="mb-4 flex space-x-4 items-center">
                        <div>
                            {{ shortage_wise_form.wo_category.label_tag }}
                            {{ shortage_wise_form.wo_category }}
                        </div>
                        <div>
                            {{ shortage_wise_form.search_by_field.label_tag }}
                            {{ shortage_wise_form.search_by_field }}
                        </div>
                        <div>
                            {{ shortage_wise_form.enq_sh.label_tag }}
                            {{ shortage_wise_form.enq_sh }}
                        </div>
                        <div>
                            {{ shortage_wise_form.search_sh.label_tag }}
                            {{ shortage_wise_form.search_sh }}
                            <div id="autocomplete-results-sh" class="absolute bg-white border border-gray-300 rounded shadow-lg z-10 w-auto max-h-48 overflow-y-auto"></div>
                        </div>
                        <button type="submit" id="btnShortageSearch" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mt-auto">Search</button>
                    </div>
                </form>
                <div id="shortageWiseTable-container" hx-trigger="load delay:10ms, refreshWorkOrders from:body" hx-get="{% url 'project_summary:shortage_wise_table' %}" hx-swap="innerHTML">
                    <!-- Shortage Wise DataTables will be loaded here via HTMX -->
                    <div class="text-center py-8">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p class="mt-2 text-gray-600">Loading Shortage Wise data...</p>
                    </div>
                </div>
            </div>

            <div x-show="activeTab === 'supplier_wise'" class="tab-pane">
                <form hx-get="{% url 'project_summary:supplier_wise_table' %}" hx-target="#supplierWiseTable-container" hx-swap="innerHTML" hx-trigger="submit, change from:select, click from:#btnSupplierSearch">
                    <div class="mb-4 flex space-x-4 items-center">
                        <div>
                            {{ supplier_wise_form.wo_category.label_tag }}
                            {{ supplier_wise_form.wo_category }}
                        </div>
                        <div>
                            {{ supplier_wise_form.search_by_field.label_tag }}
                            {{ supplier_wise_form.search_by_field }}
                        </div>
                        <div>
                            {{ supplier_wise_form.text_sup_wo_no.label_tag }}
                            {{ supplier_wise_form.text_sup_wo_no }}
                        </div>
                        <div>
                            {{ supplier_wise_form.text_sup_cust.label_tag }}
                            {{ supplier_wise_form.text_sup_cust }}
                            <div id="autocomplete-results-sup" class="absolute bg-white border border-gray-300 rounded shadow-lg z-10 w-auto max-h-48 overflow-y-auto"></div>
                        </div>
                        <button type="submit" id="btnSupplierSearch" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mt-auto">Search</button>
                    </div>
                </form>
                <div id="supplierWiseTable-container" hx-trigger="load delay:10ms, refreshWorkOrders from:body" hx-get="{% url 'project_summary:supplier_wise_table' %}" hx-swap="innerHTML">
                    <!-- Supplier Wise DataTables will be loaded here via HTMX -->
                    <div class="text-center py-8">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p class="mt-2 text-gray-600">Loading Supplier Wise data...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/datatables.net@2.0.7/js/jquery.dataTables.min.js"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/datatables.net-dt@2.0.7/css/dataTables.dataTables.min.css">

<script>
    // Alpine.js for managing tab state and modal state
    document.addEventListener('alpine:init', () => {
        Alpine.data('projectSummaryData', () => ({
            activeTab: 'item_wise',
            // Existing modal logic if any
        }));
    });

    // HTMX lifecycle event for DataTables initialization
    document.body.addEventListener('htmx:afterSwap', function (event) {
        // Check if the swapped content contains a DataTable
        if (event.detail.target.id.includes('Table-container')) {
            const tableId = event.detail.target.querySelector('table')?.id;
            if (tableId && !$.fn.DataTable.isDataTable(`#${tableId}`)) {
                $(`#${tableId}`).DataTable({
                    "pageLength": 15, // Default page size from ASP.NET GridView
                    "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
                    "destroy": true, // Allow re-initialization if content changes
                    "searching": true,
                    "ordering": true,
                    "paging": true
                });
            }
        }
    });

    // Custom event listener for showing messages
    document.body.addEventListener('showMessage', function (event) {
        // Implement your message display logic here, e.g., using Alpine.js or a simple alert
        // Django messages framework integrates automatically with HTMX, but for custom triggers
        // like "showMessage", you might need a dedicated Alpine component.
        // For simplicity, a basic alert for now:
        const messages = {{ messages|json_script:"django-messages" }};
        if (messages) {
            JSON.parse(messages.textContent).forEach(msg => {
                alert(msg.message); // Replace with a nicer notification system
            });
        }
    });
</script>
{% endblock %}
```

**`_item_wise_table.html` (Partial for Item Wise Tab)**

```html
<table id="itemWiseTable" class="min-w-full bg-white border border-gray-300">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in work_orders %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.finyear }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.customername }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.customerid }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.enqid }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.pono }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                <select class="block w-full border border-gray-300 rounded-md py-1 px-2 text-sm" 
                        id="dropdown_type_{{ obj.pk }}">
                    <option value="1">Bought Out</option>
                    <option value="2" selected>Manufacturing</option>
                </select>
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                <a href="#"
                   hx-get="{% url 'project_summary:details_report' wo_no=obj.wono switch_to_type='2' %}" 
                   hx-target="body" hx-swap="none" 
                   hx-on:click="this.setAttribute('hx-get', '{{ obj.get_details_url(this.closest('tr').querySelector(\\'[id^=dropdown_type_]\\').value) }}');">
                    {{ obj.wono }}
                </a>
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.sysdate|date:"Y-m-d" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.employeename }}</td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="10" class="py-4 px-4 text-center text-red-500 font-bold text-lg">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
```

**`_quantity_wise_table.html` (Partial for Quantity Wise Tab)**

```html
<table id="quantityWiseTable" class="min-w-full bg-white border border-gray-300">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project Title</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in work_orders %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                <input type="checkbox" name="selected_wonos" value="{{ obj.wono }}" class="h-4 w-4 text-indigo-600 border-gray-300 rounded">
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.wono }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.taskprojecttitle }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.customername }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.customerid }}</td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-4 px-4 text-center text-red-500 font-bold text-lg">No data found to display</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
```

**`_shortage_wise_table.html` (Partial for Shortage Wise Tab)**

```html
<table id="shortageWiseTable" class="min-w-full bg-white border border-gray-300">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in work_orders %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.finyear }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.customername }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.customerid }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.enqid }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.pono }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                <select class="block w-full border border-gray-300 rounded-md py-1 px-2 text-sm" 
                        id="dropdown_sh_type_{{ obj.pk }}">
                    <option value="1">Bought Out</option>
                    <option value="2" selected>Manufacturing</option>
                </select>
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                <a href="#"
                   hx-get="{% url 'project_summary:shortage_report' wo_no=obj.wono switch_to_type='2' %}" 
                   hx-target="body" hx-swap="none"
                   hx-on:click="this.setAttribute('hx-get', '{{ obj.get_shortage_url(this.closest('tr').querySelector(\\'[id^=dropdown_sh_type_]\\').value) }}');">
                    {{ obj.wono }}
                </a>
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.sysdate|date:"Y-m-d" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.employeename }}</td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="10" class="py-4 px-4 text-center text-red-500 font-bold text-lg">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
```

**`_supplier_wise_table.html` (Partial for Supplier Wise Tab)**

```html
<table id="supplierWiseTable" class="min-w-full bg-white border border-gray-300">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in work_orders %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.finyear }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.customername }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.customerid }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.enqid }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.pono }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                <select class="block w-full border border-gray-300 rounded-md py-1 px-2 text-sm" 
                        id="dropdown_sup_type_{{ obj.pk }}">
                    <option value="1">Bought Out</option>
                    <option value="2" selected>Manufacturing</option>
                </select>
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                <a href="#"
                   hx-get="{% url 'project_summary:supplier_report' wo_no=obj.wono switch_to_type='2' %}" 
                   hx-target="body" hx-swap="none"
                   hx-on:click="this.setAttribute('hx-get', '{{ obj.get_supplier_url(this.closest('tr').querySelector(\\'[id^=dropdown_sup_type_]\\').value) }}');">
                    {{ obj.wono }}
                </a>
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.sysdate|date:"Y-m-d" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.employeename }}</td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="10" class="py-4 px-4 text-center text-red-500 font-bold text-lg">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
```

**`_customer_autocomplete_results.html` (Partial for AutoComplete Suggestions)**

```html
<ul id="autocomplete-results" class="bg-white border border-gray-300 rounded shadow-lg z-10 w-full max-h-48 overflow-y-auto">
    {% for suggestion in suggestions %}
    <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer"
        onclick="this.closest('div').previousElementSibling.value = this.innerText; this.closest('div').innerHTML = '';">
        {{ suggestion }}
    </li>
    {% empty %}
    <li class="px-4 py-2 text-gray-500">No suggestions</li>
    {% endfor %}
</ul>
```

**Placeholder Report Templates (`detail_report.html`, `shortage_report.html`, `supplier_report.html`, `multiple_wo_report.html`)**

```html
{% extends 'core/base.html' %}
{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold">Report Page</h2>
    <p>This is a placeholder for your report content.</p>
    {% if wono %}
        <p>Work Order Number: {{ wono }}</p>
        <p>Switch Type: {{ switch_to_type }}</p>
    {% elif wonos %}
        <p>Work Order Number(s): {{ wonos }}</p>
        <p>Switch Type: {{ switch_to_type }}</p>
    {% elif selected_wonos %}
        <p>Selected Work Orders: {{ selected_wonos }}</p>
    {% endif %}
    <a href="{% url 'project_summary:list' %}" class="mt-4 inline-block bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Back to Project Summary</a>
</div>
{% endblock %}
```

#### 4.5 URLs (`project_summary/urls.py`)

This file defines the URL patterns for accessing the main page, individual tab content, autocomplete endpoints, and report redirects.

```python
from django.urls import path
from .views import (
    ProjectSummaryView,
    ItemWiseTableView,
    QuantityWiseTableView,
    ShortageWiseTableView,
    SupplierWiseTableView,
    CustomerAutocompleteView,
    WorkOrderSelectionProceedView,
    ProjectSummaryDetailsView,
    ProjectSummaryShortageView,
    ProjectSummarySupplierView,
    ProjectSummaryWONoReportView,
)

app_name = 'project_summary'

urlpatterns = [
    # Main project summary page with tabs
    path('', ProjectSummaryView.as_view(), name='list'),

    # HTMX endpoints for loading table content for each tab
    path('item-wise-table/', ItemWiseTableView.as_view(), name='item_wise_table'),
    path('quantity-wise-table/', QuantityWiseTableView.as_view(), name='quantity_wise_table'),
    path('shortage-wise-table/', ShortageWiseTableView.as_view(), name='shortage_wise_table'),
    path('supplier-wise-table/', SupplierWiseTableView.as_view(), name='supplier_wise_table'),

    # Autocomplete endpoint for customer names
    path('autocomplete/customer/', CustomerAutocompleteView.as_view(), name='autocomplete_customer'),

    # Endpoint for handling the "Proceed" button for selected work orders
    path('proceed-selection/', WorkOrderSelectionProceedView.as_view(), name='proceed_selection'),

    # Placeholder URLs for detail reports (replace with actual report generation views)
    path('details/manufactured/<str:wo_no>/', ProjectSummaryDetailsView.as_view(), {'switch_to_type': '2'}, name='details_report'),
    path('details/bought/<str:wo_no>/', ProjectSummaryDetailsView.as_view(), {'switch_to_type': '1'}, name='details_report_bought'),
    path('shortage/manufactured/<str:wo_no>/', ProjectSummaryShortageView.as_view(), {'switch_to_type': '2'}, name='shortage_report'),
    path('shortage/bought/<str:wo_no>/', ProjectSummaryShortageView.as_view(), {'switch_to_type': '1'}, name='shortage_report_bought'),
    path('supplier/manufactured/<str:wo_no>/', ProjectSummarySupplierView.as_view(), {'switch_to_type': '2'}, name='supplier_report'),
    path('supplier/bought/<str:wo_no>/', ProjectSummarySupplierView.as_view(), {'switch_to_type': '1'}, name='supplier_report_bought'),
    path('wonos-report/', ProjectSummaryWONoReportView.as_view(), name='project_summary_wonos_report'),
]
```

#### 4.6 Tests (`project_summary/tests.py`)

Comprehensive tests will cover the model's business logic and all view interactions, including HTMX requests.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import date
import json

from .models import WorkOrder, WOCategory, Customer, get_customer_id_from_name_code
from .forms import SearchForm, QuantityWiseSearchForm # Import forms for validation testing

class WOCategoryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.category1 = WOCategory.objects.create(cid=1, cname='Manufacturing', symbol='MFG', compid=1)
        cls.category2 = WOCategory.objects.create(cid=2, cname='Bought Out', symbol='BO', compid=1)

    def test_wocategory_creation(self):
        self.assertEqual(self.category1.cname, 'Manufacturing')
        self.assertEqual(self.category1.symbol, 'MFG')
        self.assertEqual(str(self.category1), 'MFG - Manufacturing')

    def test_wocategory_verbose_name(self):
        self.assertEqual(WOCategory._meta.verbose_name, 'WO Category')
        self.assertEqual(WOCategory._meta.verbose_name_plural, 'WO Categories')

class CustomerModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.customer1 = Customer.objects.create(customerid=101, customername='Alpha Corp', compid=1)
        cls.customer2 = Customer.objects.create(customerid=102, customername='Beta Industries', compid=1)

    def test_customer_creation(self):
        self.assertEqual(self.customer1.customername, 'Alpha Corp')
        self.assertEqual(str(self.customer1), 'Alpha Corp')

    def test_get_customer_id_from_name_code(self):
        self.assertEqual(get_customer_id_from_name_code("Test Customer [123]"), "123")
        self.assertIsNone(get_customer_id_from_name_code("Test Customer"))
        self.assertIsNone(get_customer_id_from_name_code(None))


class WorkOrderModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.customer1 = Customer.objects.create(customerid=101, customername='Alpha Corp', compid=1)
        cls.category1 = WOCategory.objects.create(cid=1, cname='Manufacturing', symbol='MFG', compid=1)
        
        cls.wo1 = WorkOrder.objects.create(
            woid=1, wono='WO-001', finyear='2023-24', customername='Alpha Corp', customerid=101,
            enqid='ENQ-001', pono='PO-001', poid=1, sysdate=date(2023, 1, 1),
            employeename='John Doe', taskprojecttitle='Project A', cid=1, compid=1, finyearid=1
        )
        cls.wo2 = WorkOrder.objects.create(
            woid=2, wono='WO-002', finyear='2023-24', customername='Beta Corp', customerid=102,
            enqid='ENQ-002', pono='PO-002', poid=2, sysdate=date(2023, 1, 15),
            employeename='Jane Smith', taskprojecttitle='Project B', cid=1, compid=1, finyearid=1
        )
        cls.wo3 = WorkOrder.objects.create(
            woid=3, wono='WO-003', finyear='2023-24', customername='Alpha Corp', customerid=101,
            enqid='ENQ-003', pono='PO-003', poid=3, sysdate=date(2023, 2, 1),
            employeename='John Doe', taskprojecttitle='Another Project', cid=2, compid=1, finyearid=1
        )
        # Additional work order for different company/fin year to test filtering
        WorkOrder.objects.create(
            woid=4, wono='WO-004', finyear='2023-24', customername='Gamma Ltd', customerid=103,
            enqid='ENQ-004', pono='PO-004', poid=4, sysdate=date(2023, 2, 10),
            employeename='Alice Brown', taskprojecttitle='Project C', cid=1, compid=2, finyearid=2
        )

    def test_workorder_creation(self):
        self.assertEqual(self.wo1.wono, 'WO-001')
        self.assertEqual(str(self.wo1), 'WO-001')

    def test_filter_work_orders_all(self):
        # Default filter (no search params) should return all for the company/fin year
        work_orders = WorkOrder.objects.filter_work_orders(company_id=1, fin_year_id=1)
        self.assertEqual(work_orders.count(), 3)
        self.assertIn(self.wo1, work_orders)
        self.assertIn(self.wo2, work_orders)
        self.assertIn(self.wo3, work_orders)

    def test_filter_work_orders_by_wo_category(self):
        work_orders = WorkOrder.objects.filter_work_orders(company_id=1, fin_year_id=1, wo_category_id=1)
        self.assertEqual(work_orders.count(), 2)
        self.assertIn(self.wo1, work_orders)
        self.assertIn(self.wo2, work_orders)
        self.assertNotIn(self.wo3, work_orders)

    def test_filter_work_orders_by_customer_name(self):
        work_orders = WorkOrder.objects.filter_work_orders(company_id=1, fin_year_id=1, search_field='0', search_value='Alpha Corp [101]')
        self.assertEqual(work_orders.count(), 2)
        self.assertIn(self.wo1, work_orders)
        self.assertIn(self.wo3, work_orders)

    def test_filter_work_orders_by_enq_id(self):
        work_orders = WorkOrder.objects.filter_work_orders(company_id=1, fin_year_id=1, search_field='1', search_value='ENQ-001')
        self.assertEqual(work_orders.count(), 1)
        self.assertIn(self.wo1, work_orders)

    def test_filter_work_orders_by_po_no(self):
        work_orders = WorkOrder.objects.filter_work_orders(company_id=1, fin_year_id=1, search_field='2', search_value='PO-002')
        self.assertEqual(work_orders.count(), 1)
        self.assertIn(self.wo2, work_orders)

    def test_filter_work_orders_by_wo_no(self):
        work_orders = WorkOrder.objects.filter_work_orders(company_id=1, fin_year_id=1, search_field='3', search_value='WO-003')
        self.assertEqual(work_orders.count(), 1)
        self.assertIn(self.wo3, work_orders)

    def test_filter_work_orders_by_project_title(self):
        work_orders = WorkOrder.objects.filter_work_orders(company_id=1, fin_year_id=1, search_field='project_title', search_value='Project A')
        self.assertEqual(work_orders.count(), 1)
        self.assertIn(self.wo1, work_orders)

    def test_get_details_url(self):
        self.assertEqual(self.wo1.get_details_url("2"), "/project-summary/details/manufactured/WO-001/")
        self.assertEqual(self.wo1.get_details_url("1"), "/project-summary/details/bought/WO-001/")
        self.assertEqual(self.wo1.get_details_url("invalid"), "#")

    def test_get_shortage_url(self):
        self.assertEqual(self.wo1.get_shortage_url("2"), "/project-summary/shortage/manufactured/WO-001/")
        self.assertEqual(self.wo1.get_shortage_url("1"), "/project-summary/shortage/bought/WO-001/")

    def test_get_supplier_url(self):
        self.assertEqual(self.wo1.get_supplier_url("2"), "/project-summary/supplier/manufactured/WO-001/")
        self.assertEqual(self.wo1.get_supplier_url("1"), "/project-summary/supplier/bought/WO-001/")


class ProjectSummaryViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.category1 = WOCategory.objects.create(cid=1, cname='Manufacturing', symbol='MFG', compid=1)
        cls.category2 = WOCategory.objects.create(cid=2, cname='Bought Out', symbol='BO', compid=1)
        cls.customer1 = Customer.objects.create(customerid=101, customername='Alpha Corp', compid=1)
        cls.customer2 = Customer.objects.create(customerid=102, customername='Beta Industries', compid=1)
        cls.wo1 = WorkOrder.objects.create(
            woid=1, wono='WO-001', finyear='2023-24', customername='Alpha Corp', customerid=101,
            enqid='ENQ-001', pono='PO-001', poid=1, sysdate=date(2023, 1, 1),
            employeename='John Doe', taskprojecttitle='Project A', cid=1, compid=1, finyearid=1
        )
        cls.wo2 = WorkOrder.objects.create(
            woid=2, wono='WO-002', finyear='2023-24', customername='Beta Industries', customerid=102,
            enqid='ENQ-002', pono='PO-002', poid=2, sysdate=date(2023, 1, 15),
            employeename='Jane Smith', taskprojecttitle='Project B', cid=1, compid=1, finyearid=1
        )
        cls.wo3 = WorkOrder.objects.create(
            woid=3, wono='WO-003', finyear='2023-24', customername='Alpha Corp', customerid=101,
            enqid='ENQ-003', pono='PO-003', poid=3, sysdate=date(2023, 2, 1),
            employeename='John Doe', taskprojecttitle='Another Project', cid=2, compid=1, finyearid=1
        )

    def setUp(self):
        self.client = Client()
        # Set session data for compid and finyearid
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 1
        session.save()

    def test_project_summary_view_get(self):
        response = self.client.get(reverse('project_summary:list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_summary/list.html')
        self.assertContains(response, 'Item Wise')
        self.assertContains(response, 'Quantity Wise')
        self.assertContains(response, 'Shortage Wise')
        self.assertContains(response, 'Supplier Wise')
        self.assertIsInstance(response.context['item_wise_form'], SearchForm)

    def test_item_wise_table_view_get_no_params(self):
        response = self.client.get(reverse('project_summary:item_wise_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_summary/_item_wise_table.html')
        self.assertContains(response, 'No data to display !') # Should initially be empty if no search

    def test_item_wise_table_view_get_with_search(self):
        # Search by WO No
        response = self.client.get(reverse('project_summary:item_wise_table'), {
            'wo_category': '',
            'search_by_field': '3',
            'enq_id': 'WO-001',
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_summary/_item_wise_table.html')
        self.assertContains(response, 'WO-001')
        self.assertNotContains(response, 'WO-002')

        # Search by Customer Name
        response = self.client.get(reverse('project_summary:item_wise_table'), {
            'wo_category': '',
            'search_by_field': '0',
            'search_value': 'Alpha Corp [101]',
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO-001')
        self.assertContains(response, 'WO-003')
        self.assertNotContains(response, 'WO-002')

    def test_quantity_wise_table_view_get_with_search(self):
        response = self.client.get(reverse('project_summary:quantity_wise_table'), {
            'wo_category': '',
            'search_field': '2', # Project Title
            'po_no': 'Project A', # This field name is used for project title search in this form
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_summary/_quantity_wise_table.html')
        self.assertContains(response, 'WO-001')
        self.assertNotContains(response, 'WO-002')

    def test_customer_autocomplete_view(self):
        response = self.client.get(reverse('project_summary:autocomplete_customer'), {'prefix_text': 'Alp'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Alpha Corp [101]')
        self.assertNotContains(response, 'Beta Industries')

        response = self.client.get(reverse('project_summary:autocomplete_customer'), {'prefix_text': 'XYZ'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No suggestions')

    def test_work_order_selection_proceed_view_post_success(self):
        response = self.client.post(reverse('project_summary:proceed_selection'), {
            'selected_wonos': ['WO-001', 'WO-002'],
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX No Content
        self.assertTrue('HX-Redirect' in response.headers)
        self.assertEqual(response.headers['HX-Redirect'], reverse('project_summary:project_summary_wonos_report'))
        self.assertEqual(self.client.session['selected_wonos_for_report'], 'WO-001,WO-002')

    def test_work_order_selection_proceed_view_post_no_selection(self):
        response = self.client.post(reverse('project_summary:proceed_selection'), {}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # OK, but with message
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertIn('showMessage', response.headers['HX-Trigger'])
        # Verify a message was added (check if messages framework is working)
        # self.assertTrue(len(list(messages.get_messages(response.wsgi_request))) > 0) # This needs proper setup for messages in tests
        
    def test_detail_report_views(self):
        response = self.client.get(reverse('project_summary:details_report', args=['WO-001']), {'switch_to_type': '2'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_summary/detail_report.html')
        self.assertContains(response, 'WO-001')
        self.assertContains(response, 'Switch Type: 2')

        response = self.client.get(reverse('project_summary:shortage_report_bought', args=['WO-002']))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_summary/shortage_report.html')
        self.assertContains(response, 'WO-002')
        self.assertContains(response, 'Switch Type: 1')
```

## Step 5: HTMX and Alpine.js Integration

The provided templates and views already demonstrate HTMX and Alpine.js integration:

*   **HTMX for Tab Switching:** The main `list.html` uses Alpine.js `x-show` to control tab visibility. When a tab button is clicked, it changes `activeTab`, showing the correct container. The content *within* each tab container (the DataTables) is loaded via `hx-get` on `load` and `refreshWorkOrders` triggers. This ensures dynamic loading of table data without full page reloads.
*   **HTMX for Search/Filter:** Each tab's search form uses `hx-get` to submit filter parameters. The `hx-target` is set to the respective `_table.html` container, and `hx-swap="innerHTML"` replaces the table content. The `hx-trigger` combines `submit`, `change` from dropdowns, and `click` from the search button for responsiveness.
*   **HTMX for DataTables Refresh:** A custom `refreshWorkOrders` HTMX event is triggered (e.g., from a successful form submission on a modal if applicable, or from the `proceed_selection` view) to reload the DataTables without a full page refresh.
*   **HTMX for Auto-completion:** The customer name search inputs (`search_value`, `search_sh`, `text_sup_cust`) use `hx-get` to `autocomplete_customer/` endpoint. `hx-trigger="keyup changed delay:500ms"` provides a smooth typing experience. The `hx-target` points to a `div` where the `_customer_autocomplete_results.html` partial is swapped, displaying clickable suggestions.
*   **HTMX for `Proceed` Button:** The `Proceed` button on the "Quantity Wise" tab uses `hx-post` to send selected `WONos` to the `proceed_selection/` endpoint. Upon success, `HX-Redirect` is used for a full page navigation to the report. If there's an error (e.g., no WO selected), a custom `HX-Trigger='showMessage'` is sent to the client, which can then be handled by Alpine.js for a more user-friendly notification.
*   **Alpine.js for UI State:** Alpine.js (`x-data`, `x-show`, `@click`, `x-on:change`) is used to manage:
    *   The `activeTab` state in `list.html` to visually highlight the current tab and show/hide tab content.
    *   Dynamic visibility of search input fields (`enq_id` vs `search_value`, `supplier` vs `po_no`) based on dropdown selection.
    *   The "Select All Work Order" checkbox's client-side behavior to check/uncheck all checkboxes in the table.
    *   Potential for future modal handling (though not explicitly required by the original code, it's a common pattern for HTMX/Alpine.js for CRUD).
*   **DataTables.js Integration:** The `_item_wise_table.html` (and others) are set up to be initialized as DataTables upon HTMX `htmx:afterSwap` event. This ensures that when new table content is loaded, DataTables is correctly applied, providing client-side search, sort, and pagination as in the original ASP.NET `GridView`.

## Final Notes

*   **Company/Financial Year Context:** The original ASP.NET code used `Session["compid"]` and `Session["finyear"]`. In Django, these would typically come from the authenticated user's profile or a globally set context (e.g., via middleware or a custom context processor). For demonstration, placeholders `CURRENT_COMPANY_ID` and `CURRENT_FIN_YEAR_ID` are used. A robust authentication and user profile system would be implemented as part of the broader modernization.
*   **Error Handling:** The original ASP.NET code had `try-catch` blocks that often silently consumed exceptions. In Django, error handling would involve proper logging, user-friendly error messages (e.g., via Django's `messages` framework), and appropriate HTTP status codes.
*   **Report Generation:** The redirection to various report pages (e.g., `ProjectSummary_Details_Grid.aspx`) indicates that there's a reporting module. These reports would need to be migrated as separate Django views, potentially using a reporting library or custom data rendering. The provided solution includes placeholder views and URLs for these.
*   **CSS Classes:** The provided CSS classes like `box3`, `redbox`, `fontcss`, `yui-datatable-theme` are ASP.NET-specific. The Django templates are updated to use basic Tailwind CSS classes for styling, demonstrating the shift to a modern CSS framework.
*   **"Select All" Checkbox:** The ASP.NET logic directly manipulates `GridViewRow` controls. In Django/HTMX/Alpine.js, this is handled via Alpine.js for client-side toggling and the `hx-include` attribute to send selected `WONos` on the "Proceed" button click.