## ASP.NET to Django Conversion Script:

This document outlines a strategic plan to modernize your legacy ASP.NET application, specifically the "Project Management" module, by migrating it to a robust and scalable Django framework. Our approach prioritizes AI-assisted automation, minimizing manual coding and maximizing efficiency.

The current ASP.NET code provides a basic structure but lacks specific database interactions or UI elements. Therefore, this plan will infer typical "Project Management" functionalities (like managing projects) and demonstrate how to implement these using modern Django patterns. This systematic conversion process can be applied to other modules in your application.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module (`Project` in this case).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
The provided ASP.NET code snippet (both `.aspx` and `.cs`) is a placeholder for a "Project Management" module and does not explicitly define database tables or columns. For this modernization plan, we will *infer* a common structure for a `Project` entity within a project management system.

**Inferred Database Table:** `tbl_project`

**Inferred Columns:**
*   `ProjectID` (Primary Key, integer)
*   `ProjectName` (String, e.g., `VARCHAR(255)`)
*   `Description` (Text, e.g., `TEXT`)
*   `StartDate` (Date, e.g., `DATE`)
*   `EndDate` (Date, e.g., `DATE`)
*   `Status` (String, e.g., `VARCHAR(50)`, for statuses like 'Active', 'Completed', 'On Hold')

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
Given the placeholder nature of the ASP.NET code, we infer standard CRUD (Create, Read, Update, Delete) operations for `Project` entities, which are fundamental to any project management module.

*   **Create:** Ability to add new projects.
*   **Read:** Ability to view a list of all projects and individual project details.
*   **Update:** Ability to modify existing project information.
*   **Delete:** Ability to remove projects.

We will not infer specific validation logic as none is present, but the Django forms will include basic required field validation.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
Based on typical ASP.NET "Project Management" interfaces and the general structure of a dashboard, we infer the following UI components for Django:

*   **Project List View (equivalent to a `GridView`):**
    *   Displays all `Project` records in a tabular format.
    *   Features pagination, searching, and sorting (handled by DataTables).
    *   Includes action buttons (Edit, Delete) for each row.
*   **Project Form (equivalent to `TextBoxes`, `DropDownLists`, `Buttons`):**
    *   A form for entering new project details (Create).
    *   The same form repurposed for editing existing project details (Update).
    *   Fields for `Project Name`, `Description`, `Start Date`, `End Date`, `Status`.
    *   Submit and Cancel buttons.
*   **Confirmation Dialog (for Delete):**
    *   A simple modal to confirm project deletion.

All dynamic interactions (loading forms, submitting data, refreshing lists) will be powered by HTMX, with Alpine.js managing local UI state (like modal visibility).

### Step 4: Generate Django Code

We will create a new Django application named `project_management` to house these components, adhering to the modular structure.

#### 4.1 Models

**Task:** Create a Django model based on the database schema.

**Instructions:**
The `Project` model will be mapped directly to the `tbl_project` table in your existing database.

```python
# project_management/models.py
from django.db import models
from django.utils import timezone

class Project(models.Model):
    # Assuming ProjectID is the primary key in tbl_project.
    # Django will automatically create an 'id' field if not specified,
    # mapping to the existing PK is often done by setting pk=True on the actual PK field.
    # However, if 'ProjectID' is an auto-incrementing PK, Django's default 'id' usually aligns.
    # For simplicity, we'll let Django manage 'id' and map 'ProjectID' if needed as a regular field,
    # or assume 'id' internally maps to 'ProjectID' if it's the actual PK.
    # For existing DBs, Django's 'inspectdb' can generate this.
    # Here, we'll assume Django's default 'id' maps to the PK and ProjectID is a regular field.
    # If ProjectID is the *actual* primary key from the existing DB, define it as:
    # project_id = models.IntegerField(db_column='ProjectID', primary_key=True)
    # For this example, we'll assume Django's default 'id' as the PK.

    project_name = models.CharField(db_column='ProjectName', max_length=255, verbose_name="Project Name")
    description = models.TextField(db_column='Description', blank=True, null=True, verbose_name="Description")
    start_date = models.DateField(db_column='StartDate', verbose_name="Start Date")
    end_date = models.DateField(db_column='EndDate', blank=True, null=True, verbose_name="End Date")
    
    # Define choices for status for better data integrity
    STATUS_CHOICES = [
        ('Active', 'Active'),
        ('Completed', 'Completed'),
        ('On Hold', 'On Hold'),
        ('Cancelled', 'Cancelled'),
    ]
    status = models.CharField(db_column='Status', max_length=50, choices=STATUS_CHOICES, default='Active', verbose_name="Status")

    class Meta:
        managed = False  # Important: Tells Django not to manage table creation/deletion
        db_table = 'tbl_project' # Name of your existing database table
        verbose_name = 'Project'
        verbose_name_plural = 'Projects'

    def __str__(self):
        return self.project_name
        
    # Business logic methods (Fat Model approach)
    def is_overdue(self):
        """
        Checks if the project is overdue based on its end date and current status.
        A project is overdue if its end_date has passed and its status is not 'Completed' or 'Cancelled'.
        """
        if self.end_date and self.end_date < timezone.now().date() and self.status not in ['Completed', 'Cancelled']:
            return True
        return False

    def get_status_color(self):
        """
        Returns a Tailwind CSS color class based on the project status.
        """
        if self.status == 'Active':
            return 'bg-green-100 text-green-800'
        elif self.status == 'Completed':
            return 'bg-blue-100 text-blue-800'
        elif self.status == 'On Hold':
            return 'bg-yellow-100 text-yellow-800'
        elif self.status == 'Cancelled':
            return 'bg-red-100 text-red-800'
        return 'bg-gray-100 text-gray-800'
```

#### 4.2 Forms

**Task:** Define a Django form for user input.

**Instructions:**
We'll create a `ModelForm` for the `Project` model, including fields for user input and applying Tailwind CSS classes for styling.

```python
# project_management/forms.py
from django import forms
from .models import Project

class ProjectForm(forms.ModelForm):
    class Meta:
        model = Project
        fields = ['project_name', 'description', 'start_date', 'end_date', 'status']
        widgets = {
            'project_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'start_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'end_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'status': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        
    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date and start_date > end_date:
            self.add_error('end_date', 'End date cannot be before start date.')
        
        return cleaned_data
```

#### 4.3 Views

**Task:** Implement CRUD operations using Django Class-Based Views (CBVs).

**Instructions:**
We will define `ListView`, `CreateView`, `UpdateView`, and `DeleteView` for the `Project` model. An additional view for the DataTables partial will be included. All views will remain thin, delegating business logic to the model.

```python
# project_management/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import Project
from .forms import ProjectForm

# Base view for project list, includes the overall structure
class ProjectListView(ListView):
    model = Project
    template_name = 'project_management/project/list.html'
    context_object_name = 'projects'

# Partial view for the DataTables table content, loaded via HTMX
class ProjectTablePartialView(ListView):
    model = Project
    template_name = 'project_management/project/_project_table.html'
    context_object_name = 'projects'

class ProjectCreateView(CreateView):
    model = Project
    form_class = ProjectForm
    template_name = 'project_management/project/_project_form.html' # Use partial template for HTMX modal
    success_url = reverse_lazy('project_list') # Fallback for non-HTMX request

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Project added successfully.')
        if self.request.headers.get('HX-Request'):
            # Return a 204 No Content response for HTMX, along with a trigger header
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshProjectList' # Custom HTMX event to refresh the list
                }
            )
        return response

class ProjectUpdateView(UpdateView):
    model = Project
    form_class = ProjectForm
    template_name = 'project_management/project/_project_form.html' # Use partial template for HTMX modal
    context_object_name = 'project'
    success_url = reverse_lazy('project_list') # Fallback for non-HTMX request

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Project updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshProjectList'
                }
            )
        return response

class ProjectDeleteView(DeleteView):
    model = Project
    template_name = 'project_management/project/_project_confirm_delete.html' # Use partial template for HTMX modal
    context_object_name = 'project'
    success_url = reverse_lazy('project_list') # Fallback for non-HTMX request

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Project deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshProjectList'
                }
            )
        return response
```

#### 4.4 Templates

**Task:** Create templates for each view, ensuring DRY principles and HTMX integration.

**Instructions:**
Templates will extend `core/base.html` and use HTMX for dynamic content loading, specifically for the DataTables table and modal forms.

**List Template (`project_management/project/list.html`):**
This template acts as the main page for listing projects.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Projects</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'project_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus-circle mr-2"></i>Add New Project
        </button>
    </div>
    
    <div id="projectTable-container"
         hx-trigger="load, refreshProjectList from:body"
         hx-get="{% url 'project_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg p-6">
        <!-- DataTables will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading projects...</p>
        </div>
    </div>
    
    <!-- Modal for form (Add/Edit) and Delete confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 transition-opacity duration-300 opacity-0 pointer-events-none"
         _="on click if event.target.id == 'modal' remove .opacity-0 from me then add .pointer-events-none to me then add .hidden to #modal also remove .opacity-100 from me when target is #modal else add .opacity-100 to me then remove .pointer-events-none from me then remove .hidden from me when target is #modal">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 transition-all duration-300 transform scale-95 opacity-0"
             _="on load transition my opacity to 100% and scale to 100%">
            <!-- Content loaded by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js can be used here for any client-side state management
        // For example, if you had a complex toggle or dynamic form fields not handled by HTMX
    });

    // Handle modal visibility with Alpine.js if more complex interaction is needed
    // or simply use _ from Hyperscript. The current setup uses Hyperscript.
</script>
{% endblock %}
```

**Table Partial Template (`project_management/project/_project_table.html`):**
This partial is loaded by HTMX to display the DataTables content.

```html
<div class="overflow-x-auto">
    <table id="projectTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project Name</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Start Date</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">End Date</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Overdue?</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for project in projects %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ project.project_name }}</td>
                <td class="py-3 px-4 text-sm text-gray-500 truncate max-w-xs">{{ project.description|default:"N/A" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ project.start_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ project.end_date|date:"Y-m-d"|default:"N/A" }}</td>
                <td class="py-3 px-4 whitespace-nowrap">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ project.get_status_color }}">
                        {{ project.status }}
                    </span>
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm {% if project.is_overdue %}text-red-600 font-semibold{% else %}text-gray-500{% endif %}">
                    {{ project.is_overdue|yesno:"Yes,No" }}
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="text-indigo-600 hover:text-indigo-900 mr-3 p-1 rounded-md hover:bg-gray-100"
                        hx-get="{% url 'project_edit' project.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .opacity-100 to #modal then remove .hidden from #modal and add .scale-100 .opacity-100 to #modalContent">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button 
                        class="text-red-600 hover:text-red-900 p-1 rounded-md hover:bg-gray-100"
                        hx-get="{% url 'project_delete' project.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .opacity-100 to #modal then remove .hidden from #modal and add .scale-100 .opacity-100 to #modalContent">
                        <i class="fas fa-trash-alt"></i> Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="py-4 px-4 text-center text-gray-500">No projects found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
// DataTables initialization
// Assumes jQuery and DataTables JS/CSS are loaded in core/base.html
$(document).ready(function() {
    // Destroy existing DataTable instance if it exists to prevent reinitialization errors
    if ($.fn.DataTable.isDataTable('#projectTable')) {
        $('#projectTable').DataTable().destroy();
    }
    $('#projectTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 7] } // Disable ordering for SN and Actions columns
        ]
    });
});
</script>
```

**Form Partial Template (`project_management/project/_project_form.html`):**
This partial is used for both adding and editing projects, loaded into the modal.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Project</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#loadingIndicator">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
                <label for="{{ form.project_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.project_name.label }}
                </label>
                {{ form.project_name }}
                {% if form.project_name.errors %}
                <p class="text-red-600 text-xs mt-1">{{ form.project_name.errors.as_text }}</p>
                {% endif %}
            </div>
            <div>
                <label for="{{ form.status.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.status.label }}
                </label>
                {{ form.status }}
                {% if form.status.errors %}
                <p class="text-red-600 text-xs mt-1">{{ form.status.errors.as_text }}</p>
                {% endif %}
            </div>
            <div>
                <label for="{{ form.start_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.start_date.label }}
                </label>
                {{ form.start_date }}
                {% if form.start_date.errors %}
                <p class="text-red-600 text-xs mt-1">{{ form.start_date.errors.as_text }}</p>
                {% endif %}
            </div>
            <div>
                <label for="{{ form.end_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.end_date.label }}
                </label>
                {{ form.end_date }}
                {% if form.end_date.errors %}
                <p class="text-red-600 text-xs mt-1">{{ form.end_date.errors.as_text }}</p>
                {% endif %}
            </div>
        </div>
        
        <div class="mb-6">
            <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                {{ form.description.label }}
            </label>
            {{ form.description }}
            {% if form.description.errors %}
            <p class="text-red-600 text-xs mt-1">{{ form.description.errors.as_text }}</p>
            {% endif %}
        </div>

        {% if form.non_field_errors %}
            <div class="mb-4 text-red-600 text-sm">
                {{ form.non_field_errors }}
            </div>
        {% endif %}
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .opacity-100 from #modalContent then remove .scale-100 from #modalContent then wait 300ms then remove .opacity-100 from #modal then remove .pointer-events-none from #modal then add .hidden to #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

**Delete Confirmation Partial Template (`project_management/project/_project_confirm_delete.html`):**
This partial is used for deleting projects, loaded into the modal.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the project: <strong>"{{ project.project_name }}"</strong>?</p>
    
    <form hx-post="{% url 'project_delete' project.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .opacity-100 from #modalContent then remove .scale-100 from #modalContent then wait 300ms then remove .opacity-100 from #modal then remove .pointer-events-none from #modal then add .hidden to #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
URLs will be set up for the main list page, and separate endpoints for adding, editing, deleting, and fetching the DataTables content via HTMX.

```python
# project_management/urls.py
from django.urls import path
from .views import (
    ProjectListView, 
    ProjectCreateView, 
    ProjectUpdateView, 
    ProjectDeleteView,
    ProjectTablePartialView
)

urlpatterns = [
    # Main project list page
    path('projects/', ProjectListView.as_view(), name='project_list'),
    
    # HTMX endpoints for modal forms and table content
    path('projects/add/', ProjectCreateView.as_view(), name='project_add'),
    path('projects/edit/<int:pk>/', ProjectUpdateView.as_view(), name='project_edit'),
    path('projects/delete/<int:pk>/', ProjectDeleteView.as_view(), name='project_delete'),
    path('projects/table/', ProjectTablePartialView.as_view(), name='project_table'), # For HTMX to fetch table data
]
```

#### 4.6 Tests

**Task:** Write tests for the model and views to ensure functionality and coverage.

**Instructions:**
Comprehensive unit tests for the `Project` model and integration tests for all `Project` related views will be included.

```python       
# project_management/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import Project
from .forms import ProjectForm

class ProjectModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        Project.objects.create(
            project_name='Test Project 1',
            description='Description for Test Project 1',
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timezone.timedelta(days=30),
            status='Active'
        )
        Project.objects.create(
            project_name='Completed Project',
            description='This project is completed.',
            start_date=timezone.now().date() - timezone.timedelta(days=60),
            end_date=timezone.now().date() - timezone.timedelta(days=30),
            status='Completed'
        )
        Project.objects.create(
            project_name='Overdue Project',
            description='This project should be overdue.',
            start_date=timezone.now().date() - timezone.timedelta(days=60),
            end_date=timezone.now().date() - timezone.timedelta(days=1),
            status='Active'
        )
  
    def test_project_creation(self):
        project = Project.objects.get(project_name='Test Project 1')
        self.assertEqual(project.description, 'Description for Test Project 1')
        self.assertEqual(project.status, 'Active')
        self.assertFalse(project.is_overdue())

    def test_project_name_label(self):
        project = Project.objects.get(project_name='Test Project 1')
        field_label = project._meta.get_field('project_name').verbose_name
        self.assertEqual(field_label, 'Project Name')
        
    def test_is_overdue_method(self):
        overdue_project = Project.objects.get(project_name='Overdue Project')
        self.assertTrue(overdue_project.is_overdue())

        completed_project = Project.objects.get(project_name='Completed Project')
        self.assertFalse(completed_project.is_overdue()) # Completed projects are not overdue

        active_project = Project.objects.get(project_name='Test Project 1')
        self.assertFalse(active_project.is_overdue()) # Not past end date

    def test_get_status_color_method(self):
        active_project = Project.objects.get(project_name='Test Project 1')
        self.assertEqual(active_project.get_status_color(), 'bg-green-100 text-green-800')

        completed_project = Project.objects.get(project_name='Completed Project')
        self.assertEqual(completed_project.get_status_color(), 'bg-blue-100 text-blue-800')

class ProjectViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        Project.objects.create(
            project_name='Initial Project',
            description='Initial description',
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timezone.timedelta(days=10),
            status='Active'
        )
    
    def setUp(self):
        # Set up data for each test method
        self.client = Client()
    
    def test_list_view(self):
        response = self.client.get(reverse('project_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/project/list.html')
        self.assertIn('projects', response.context)
        self.assertQuerysetEqual(response.context['projects'], Project.objects.all(), transform=lambda x: x)
        
    def test_project_table_partial_view(self):
        response = self.client.get(reverse('project_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/project/_project_table.html')
        self.assertIn('projects', response.context)
        self.assertContains(response, 'Initial Project') # Check if project name is in the rendered table

    def test_create_view_get(self):
        response = self.client.get(reverse('project_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/project/_project_form.html')
        self.assertIn('form', response.context)
        self.assertIsInstance(response.context['form'], ProjectForm)
        
    def test_create_view_post_success(self):
        data = {
            'project_name': 'New Project',
            'description': 'Description for new project',
            'start_date': (timezone.now() + timezone.timedelta(days=1)).strftime('%Y-%m-%d'),
            'end_date': (timezone.now() + timezone.timedelta(days=31)).strftime('%Y-%m-%d'),
            'status': 'Active',
        }
        response = self.client.post(reverse('project_add'), data)
        # Check for redirect after successful creation (for non-HTMX)
        self.assertEqual(response.status_code, 302) 
        self.assertTrue(Project.objects.filter(project_name='New Project').exists())
        
    def test_create_view_post_htmx_success(self):
        data = {
            'project_name': 'HTMX New Project',
            'description': 'Description for HTMX new project',
            'start_date': (timezone.now() + timezone.timedelta(days=1)).strftime('%Y-%m-%d'),
            'end_date': (timezone.now() + timezone.timedelta(days=31)).strftime('%Y-%m-%d'),
            'status': 'Active',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('project_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshProjectList')
        self.assertTrue(Project.objects.filter(project_name='HTMX New Project').exists())

    def test_create_view_post_invalid(self):
        data = { # Missing required fields
            'project_name': '', 
            'start_date': 'invalid-date',
        }
        response = self.client.post(reverse('project_add'), data)
        self.assertEqual(response.status_code, 200) # Form re-renders with errors
        self.assertFalse(Project.objects.filter(project_name='').exists())
        self.assertContains(response, 'This field is required.')

    def test_update_view_get(self):
        project = Project.objects.get(project_name='Initial Project')
        response = self.client.get(reverse('project_edit', args=[project.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/project/_project_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, project)
        
    def test_update_view_post_success(self):
        project = Project.objects.get(project_name='Initial Project')
        new_name = 'Updated Project Name'
        data = {
            'project_name': new_name,
            'description': project.description,
            'start_date': project.start_date.strftime('%Y-%m-%d'),
            'end_date': project.end_date.strftime('%Y-%m-%d'),
            'status': 'Completed',
        }
        response = self.client.post(reverse('project_edit', args=[project.pk]), data)
        self.assertEqual(response.status_code, 302)
        project.refresh_from_db()
        self.assertEqual(project.project_name, new_name)
        self.assertEqual(project.status, 'Completed')

    def test_update_view_post_htmx_success(self):
        project = Project.objects.get(project_name='Initial Project')
        new_name = 'HTMX Updated Project Name'
        data = {
            'project_name': new_name,
            'description': project.description,
            'start_date': project.start_date.strftime('%Y-%m-%d'),
            'end_date': project.end_date.strftime('%Y-%m-%d'),
            'status': 'On Hold',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('project_edit', args=[project.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshProjectList')
        project.refresh_from_db()
        self.assertEqual(project.project_name, new_name)
        self.assertEqual(project.status, 'On Hold')
        
    def test_delete_view_get(self):
        project = Project.objects.get(project_name='Initial Project')
        response = self.client.get(reverse('project_delete', args=[project.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/project/_project_confirm_delete.html')
        self.assertIn('project', response.context)
        self.assertEqual(response.context['project'], project)
        
    def test_delete_view_post_success(self):
        project = Project.objects.get(project_name='Initial Project')
        response = self.client.post(reverse('project_delete', args=[project.pk]))
        self.assertEqual(response.status_code, 302)
        self.assertFalse(Project.objects.filter(pk=project.pk).exists())

    def test_delete_view_post_htmx_success(self):
        project = Project.objects.get(project_name='Initial Project')
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('project_delete', args=[project.pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshProjectList')
        self.assertFalse(Project.objects.filter(pk=project.pk).exists())
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated Django templates and views are already designed for seamless HTMX and Alpine.js integration:

*   **HTMX for dynamic updates:**
    *   `hx-get` attributes on "Add New Project", "Edit", and "Delete" buttons fetch forms/confirmation dialogs into the `#modalContent` div.
    *   `hx-post` on forms submits data without full page reloads.
    *   `hx-swap="none"` on form submissions prevents content replacement, relying on `HX-Trigger` for updates.
    *   `HX-Trigger: 'refreshProjectList'` header from `CreateView`, `UpdateView`, and `DeleteView` signals the list container (`#projectTable-container`) to re-fetch its content, ensuring the DataTables table is always up-to-date.
    *   `hx-trigger="load, refreshProjectList from:body"` on the main list container ensures it loads on page load and refreshes whenever the `refreshProjectList` event is triggered from anywhere on the body.
*   **Alpine.js for UI state management:**
    *   The `_` (Hyperscript) syntax, which works alongside HTMX, is used to manage the modal's visibility. It adds/removes CSS classes (`is-active`, `opacity-100`, `hidden`, `pointer-events-none`) to control the modal's display and transitions, creating a smooth user experience.
*   **DataTables for list views:**
    *   The `_project_table.html` partial includes the JavaScript initialization for DataTables. It assumes jQuery and DataTables libraries are included in `core/base.html` (as is common for DataTables). The initialization is wrapped in `$(document).ready()` to ensure it runs after the DOM is fully loaded, and `destroy()` is called first to prevent re-initialization issues when HTMX re-swaps the table content.
*   **No custom JavaScript:** All dynamic interactions are handled through HTMX attributes, Hyperscript (`_`), or the DataTables library itself, eliminating the need for separate, complex JavaScript files.

---

## Final Notes

This comprehensive plan provides a clear, actionable roadmap for modernizing the "Project Management" module. By leveraging Django's robust features, HTMX, Alpine.js, and DataTables, we aim to deliver a highly performant, user-friendly, and maintainable application. The emphasis on AI-assisted automation means that these patterns and code structures can be systematically applied across other modules of your ASP.NET application, significantly reducing migration time and cost while improving overall code quality and consistency. This structured approach ensures business continuity and a smooth transition to a modern, future-proof platform.