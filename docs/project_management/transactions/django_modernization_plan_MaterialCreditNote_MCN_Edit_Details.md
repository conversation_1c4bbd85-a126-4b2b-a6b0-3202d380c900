## ASP.NET to Django Conversion Script: Material Credit Note - Edit Details

This document outlines a comprehensive modernization plan to transition your ASP.NET Material Credit Note (MCN) Edit Details page to a modern Django application. Our approach leverages Django's robust ORM, efficient Class-Based Views (CBVs), HTMX for dynamic interactions, Alpine.js for lightweight UI logic, and DataTables for advanced table functionalities. This strategy prioritizes automation and maintainability, moving complex business logic into the Django models ("Fat Model, Thin View") and keeping frontend interactions streamlined with minimal JavaScript.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
From the ASP.NET code-behind, we've identified the following tables and their key columns involved in displaying and updating Material Credit Note details:

*   **`tblPM_MaterialCreditNote_Details`** (Primary focus for `GridView1` rows)
    *   `Id` (PK)
    *   `MId` (Foreign Key to `tblPM_MaterialCreditNote_Master.Id`)
    *   `PId` (Part ID, used with `CId` and `WONo` to identify BOM item)
    *   `CId` (Component ID, used with `PId` and `WONo` to identify BOM item)
    *   `MCNQty` (Material Credit Note Quantity)

*   **`tblPM_MaterialCreditNote_Master`** (Parent for `tblPM_MaterialCreditNote_Details`)
    *   `Id` (PK)
    *   `SysDate` (System Date, for `MCNDate`)
    *   `MCNNo` (Material Credit Note Number)
    *   `WONo` (Work Order Number, for filtering)
    *   `FinYearId` (Financial Year ID)
    *   `CompId` (Company ID)

*   **`SD_Cust_WorkOrder_Master`** (Provides Work Order details)
    *   `Id` (PK, referenced by `WOId` query param)
    *   `TaskProjectTitle` (Project Name)
    *   `CustomerId` (Foreign Key to `SD_Cust_Master.CustomerId`)
    *   `WONo` (Work Order Number, referenced by `WONo` query param)
    *   `CompId` (Company ID)
    *   `FinYearId` (Financial Year ID)

*   **`SD_Cust_Master`** (Provides Customer details)
    *   `CustomerId` (PK)
    *   `CustomerName`
    *   `CompId` (Company ID)

*   **`tblDG_BOM_Master`** (Bill of Material, provides `BOMQty` and `ItemId`)
    *   `WONo`
    *   `FinYearId`
    *   `CompId`
    *   `PId`
    *   `CId`
    *   `ItemId` (Foreign Key to `tblDG_Item_Master.Id`)
    *   `Qty` (This is the `BOMQty`)

*   **`tblDG_Item_Master`** (Provides item details, drawing/spec files)
    *   `Id` (PK)
    *   `ItemCode`
    *   `PartNo`
    *   `ManfDesc` (Description)
    *   `UOMBasic` (Foreign Key to `Unit_Master.Id`)
    *   `FileName` (File name for drawing/image)
    *   `AttName` (File name for specification sheet)
    *   `FileData` (Binary data for drawing/image)
    *   `AttData` (Binary data for specification sheet)
    *   `ContentType` (MIME type for drawing/image)
    *   `AttContentType` (MIME type for specification sheet)

*   **`Unit_Master`** (Provides Unit of Measure symbol)
    *   `Id` (PK)
    *   `Symbol` (UOM symbol)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD and auxiliary operations in the ASP.NET code.

**Instructions:**
The ASP.NET page primarily handles the following functionality:

*   **Read (Display)**:
    *   Displays header information for a specific Work Order: Work Order No., Project Name, and Customer Name, derived from `WOId` and `WONo` query parameters.
    *   Presents a detailed list of Material Credit Note items in a grid (`GridView1`). This involves fetching data from `tblPM_MaterialCreditNote_Master`, `tblPM_MaterialCreditNote_Details`, `tblDG_BOM_Master`, `tblDG_Item_Master`, and `Unit_Master`.
    *   Calculates a `TotMCNQty` (Total MCN Quantity for an item) by summing `MCNQty` across *all* MCN details for a given `PId` and `CId` pair.

*   **Update**:
    *   Allows editing the `MCNQty` for individual MCN detail line items within the grid.
    *   **Validation Logic**: A critical business rule is applied: the `new MCNQty` for a line item must not exceed `(original MCNQty for this line item + remaining BOM Qty)`. The remaining BOM Qty is calculated as `BOMQty` (from `tblDG_BOM_Master`) minus `Total MCN Qty` (sum of MCNs for this `PId`/`CId` across all MCNs). This ensures that the total credited quantity for an item never surpasses its BOM quantity.
    *   Input validation for `MCNQty` ensures it's a valid number with up to 3 decimal places and greater than zero.

*   **Download**:
    *   Provides "View" links to download associated drawing/image and specification sheet files from `tblDG_Item_Master` for each item. These files are stored as binary data in the database.

*   **Navigation**:
    *   A "Cancel" button redirects the user back to the main MCN list page.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The UI components translate as follows:

*   **Header Labels:**
    *   `asp:Label` controls (`lblWono`, `lblProjectTitle`, `lblCustName`) display static text derived from the Work Order and Customer data. In Django, these will be rendered directly from context variables in the template.
*   **Data Grid (`asp:GridView`):**
    *   `GridView1` is the central component for displaying and interacting with MCN detail line items. This will be replaced by a `<table>` element initialized as a [DataTables.js](https://datatables.net/) instance for enhanced client-side features like searching, sorting, and pagination.
    *   **Columns:** Each `asp:TemplateField` corresponds to a `<th>` in the Django template.
        *   Text-based fields (`MCN No`, `Date`, `Item Code`, `Description`, `UOM`) will be `<td>` elements rendering Django model properties.
        *   Hidden fields (`Id`, `ItemId`, `PId`, `CId`, `BOM Qty`, `Tot MCN Qty`) will be available in the underlying data but not displayed in the table, or retrieved via HTMX.
        *   `Draw/Img` and `Spec.sheet` `asp:LinkButton`s will become standard HTML `<a>` tags or HTMX-triggered `button`s that link to a Django view for file download.
        *   `MCN Qty` `asp:Label` (for display) and `asp:TextBox` (for edit) will be managed by HTMX to swap between display and an editable input field.
    *   **`PagerSettings`:** DataTables will handle pagination directly.
    *   **`EmptyDataTemplate`:** Django templates will use `{% empty %}` block with an `{% if %}` check on the queryset.
*   **Input Controls:**
    *   `asp:TextBox ID="txtqty"` for `MCN Qty` editing will become a standard HTML `<input type="text">` element.
    *   `asp:RegularExpressionValidator` will be replaced by Django Form validation and `DecimalField` properties.
*   **Action Buttons:**
    *   `asp:Button ID="btnCancel"` will be a standard HTML `<button>` or `<a>` element, linking to the appropriate Django URL.
    *   `CommandField ShowEditButton="True"` will be a dedicated "Edit" button within the DataTables actions column, using HTMX to trigger the edit mode for a row.

### Step 4: Generate Django Code

The Django application will be named `project_management`.

#### 4.1 Models

We will define models for all related tables. The `MaterialCreditNoteDetail` model will include methods and properties to encapsulate the complex data retrieval and validation logic identified in the ASP.NET `loaddata` and `GridView1_RowUpdating` methods.

**`project_management/models.py`**

```python
from django.db import models
from django.db.models import Sum
from django.core.validators import MinValueValidator
from django.core.exceptions import ValidationError
from datetime import datetime

class Unit(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol or f"Unit {self.id}"

class Item(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=100, blank=True, null=True)
    part_no = models.CharField(db_column='PartNo', max_length=100, blank=True, null=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=250, blank=True, null=True)
    uom_basic = models.ForeignKey(Unit, models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)
    file_name = models.CharField(db_column='FileName', max_length=250, blank=True, null=True)
    att_name = models.CharField(db_column='AttName', max_length=250, blank=True, null=True)
    # File data fields are typically handled by Django's FileField/ImageField,
    # but since it's in DB, we use BinaryField and a separate download view.
    file_data = models.BinaryField(db_column='FileData', blank=True, null=True)
    content_type = models.CharField(db_column='ContentType', max_length=100, blank=True, null=True)
    att_data = models.BinaryField(db_column='AttData', blank=True, null=True)
    att_content_type = models.CharField(db_column='AttContentType', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.item_code or self.part_no or f"Item {self.id}"

    @property
    def effective_item_code(self):
        """Returns ItemCode if available, else PartNo."""
        return self.item_code if self.item_code else self.part_no

class BomItem(models.Model):
    # This model represents an item in the Bill of Material.
    # Assuming a composite primary key or an auto-incrementing ID.
    # If there's no explicit PK in DB, Django will add 'id' implicitly.
    # For managed=False, it's crucial to map existing PK.
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming an 'Id' column exists for BOM master records
    wo_no = models.CharField(db_column='WONo', max_length=50)
    fin_year_id = models.IntegerField(db_column='FinYearId')
    comp_id = models.IntegerField(db_column='CompId')
    p_id = models.IntegerField(db_column='PId')
    c_id = models.IntegerField(db_column='CId')
    item = models.ForeignKey(Item, models.DO_NOTHING, db_column='ItemId')
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3) # This is BOM Qty

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'BOM Item'
        verbose_name_plural = 'BOM Items'
        unique_together = (('wo_no', 'fin_year_id', 'comp_id', 'p_id', 'c_id'),) # Inferring from queries

    def __str__(self):
        return f"BOM for WO {self.wo_no} (PId:{self.p_id}, CId:{self.c_id})"

class Customer(models.Model):
    customer_id = models.IntegerField(db_column='CustomerId', primary_key=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=250)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_Master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customer_name

class WorkOrder(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    task_project_title = models.CharField(db_column='TaskProjectTitle', max_length=250)
    customer = models.ForeignKey(Customer, models.DO_NOTHING, db_column='CustomerId')
    wo_no = models.CharField(db_column='WONo', max_length=50)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.wo_no

class MaterialCreditNote(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # MId
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime', blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    mcn_no = models.CharField(db_column='MCNNo', max_length=50)
    wo_no = models.CharField(db_column='WONo', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblPM_MaterialCreditNote_Master'
        verbose_name = 'Material Credit Note'
        verbose_name_plural = 'Material Credit Notes'

    def __str__(self):
        return self.mcn_no

class MaterialCreditNoteDetailManager(models.Manager):
    def with_aggregated_data(self, wo_no, comp_id, fin_year_id):
        """
        Retrieves MaterialCreditNoteDetail records along with all
        the necessary aggregated and related data for display.
        This mimics the complex joins and lookups in the ASP.NET loaddata().
        """
        # Join to MaterialCreditNote (MId), BOMMaster (PId, CId, WONo), ItemMaster (ItemId), UnitMaster (UOMBasic)
        # and prefetch sum of MCNQty for each PId, CId pair.
        # This requires a subquery or annotation for TotMCNQty due to different aggregation criteria.
        
        # Calculate TotMCNQty for each unique PId, CId pair associated with a WO.
        # We need this total across ALL MCNs, not just the current one.
        total_mcn_qty_subquery = MaterialCreditNoteDetail.objects.filter(
            p_id=models.OuterRef('p_id'),
            c_id=models.OuterRef('c_id'),
            material_credit_note__wo_no=wo_no, # Ensure it's for the same WO context
            material_credit_note__comp_id=comp_id,
            material_credit_note__fin_year_id__lte=fin_year_id
        ).values('p_id', 'c_id').annotate(
            total_qty=Sum('mcn_qty')
        ).values('total_qty')

        return self.get_queryset().filter(
            material_credit_note__wo_no=wo_no,
            material_credit_note__comp_id=comp_id,
            material_credit_note__fin_year_id__lte=fin_year_id # Assuming <= finyear based on ASP.NET
        ).select_related(
            'material_credit_note'
        ).annotate(
            bom_item_id=models.Subquery(
                BomItem.objects.filter(
                    wo_no=wo_no,
                    comp_id=comp_id,
                    fin_year_id__lte=fin_year_id,
                    p_id=models.OuterRef('p_id'),
                    c_id=models.OuterRef('c_id')
                ).values('item_id')[:1] # Get the first (and likely only) item_id
            ),
            bom_qty=models.Subquery(
                BomItem.objects.filter(
                    wo_no=wo_no,
                    comp_id=comp_id,
                    fin_year_id__lte=fin_year_id,
                    p_id=models.OuterRef('p_id'),
                    c_id=models.OuterRef('c_id')
                ).values('qty')[:1]
            )
        ).annotate(
            # This 'item' property will be an Item object, allowing chaining
            item_obj=models.Subquery(
                Item.objects.filter(id=models.OuterRef('bom_item_id')).values('id','item_code', 'part_no', 'manf_desc', 'uom_basic', 'file_name', 'att_name') # Select required fields
            )
        ).annotate(
            uom_symbol=models.Subquery(
                Unit.objects.filter(id=models.OuterRef('item_obj__uom_basic')).values('symbol')[:1]
            )
        ).annotate(
            total_mcn_qty_for_item=models.Subquery(total_mcn_qty_subquery)
        ).order_by('id') # Ensure consistent ordering

class MaterialCreditNoteDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    material_credit_note = models.ForeignKey(MaterialCreditNote, models.DO_NOTHING, db_column='MId')
    p_id = models.IntegerField(db_column='PId')
    c_id = models.IntegerField(db_column='CId')
    mcn_qty = models.DecimalField(db_column='MCNQty', max_digits=18, decimal_places=3,
                                  validators=[MinValueValidator(0.001)]) # Quantity must be positive

    objects = MaterialCreditNoteDetailManager()

    class Meta:
        managed = False
        db_table = 'tblPM_MaterialCreditNote_Details'
        verbose_name = 'Material Credit Note Detail'
        verbose_name_plural = 'Material Credit Note Details'

    def __str__(self):
        return f"MCN Detail {self.id} for MCN {self.material_credit_note.mcn_no}"

    @property
    def mcn_no(self):
        return self.material_credit_note.mcn_no

    @property
    def mcn_date(self):
        # Format date as 'DD-MM-YYYY' like fun.FromDateDMY
        return self.material_credit_note.sys_date.strftime('%d-%m-%Y') if self.material_credit_note.sys_date else ''

    @property
    def item_code(self):
        # Access through annotated item_obj or direct lookup if not annotated
        if hasattr(self, 'item_obj'):
            item_id = self.item_obj.get('id')
            if item_id:
                item = Item.objects.get(id=item_id)
                return item.effective_item_code
        # Fallback if not annotated (e.g. from an instance not loaded via the manager)
        bom_item = BomItem.objects.filter(
            wo_no=self.material_credit_note.wo_no,
            p_id=self.p_id, c_id=self.c_id,
            comp_id=self.material_credit_note.comp_id,
            fin_year_id__lte=self.material_credit_note.fin_year_id
        ).select_related('item').first()
        return bom_item.item.effective_item_code if bom_item and bom_item.item else None

    @property
    def description(self):
        if hasattr(self, 'item_obj'):
            return self.item_obj.get('manf_desc')
        bom_item = BomItem.objects.filter(p_id=self.p_id, c_id=self.c_id, wo_no=self.material_credit_note.wo_no).select_related('item').first()
        return bom_item.item.manf_desc if bom_item and bom_item.item else None

    @property
    def uom(self):
        if hasattr(self, 'uom_symbol'):
            return self.uom_symbol
        bom_item = BomItem.objects.filter(p_id=self.p_id, c_id=self.c_id, wo_no=self.material_credit_note.wo_no).select_related('item__uom_basic').first()
        return bom_item.item.uom_basic.symbol if bom_item and bom_item.item and bom_item.item.uom_basic else None

    @property
    def bom_qty(self):
        if hasattr(self, 'bom_qty'):
            return self.bom_qty
        bom_item = BomItem.objects.filter(p_id=self.p_id, c_id=self.c_id, wo_no=self.material_credit_note.wo_no).first()
        return bom_item.qty if bom_item else None

    @property
    def total_mcn_qty_for_item(self):
        # This aggregates MCNQty for the specific PId/CId across all MCNs
        if hasattr(self, 'total_mcn_qty_for_item'):
            return self.total_mcn_qty_for_item
        
        # Fallback if not annotated (e.g., in save method context)
        return MaterialCreditNoteDetail.objects.filter(
            p_id=self.p_id,
            c_id=self.c_id,
            material_credit_note__wo_no=self.material_credit_note.wo_no,
            material_credit_note__comp_id=self.material_credit_note.comp_id,
            material_credit_note__fin_year_id__lte=self.material_credit_note.fin_year_id
        ).aggregate(total_qty=Sum('mcn_qty'))['total_qty'] or 0

    def get_item_id(self):
        # Helper to get the item_id for download links
        bom_item = BomItem.objects.filter(
            wo_no=self.material_credit_note.wo_no,
            p_id=self.p_id, c_id=self.c_id,
            comp_id=self.material_credit_note.comp_id,
            fin_year_id__lte=self.material_credit_note.fin_year_id
        ).first()
        return bom_item.item_id if bom_item else None

    def clean(self):
        # Custom validation logic mirroring ASP.NET's GridView1_RowUpdating
        original_mcn_qty = MaterialCreditNoteDetail.objects.get(id=self.id).mcn_qty if self.pk else 0
        
        # Calculate total MCN qty for this item (PId, CId) across all MCNs, excluding this record's original qty
        current_total_mcn_qty = self.total_mcn_qty_for_item
        
        # Remove the current record's original quantity from the sum to get "other" MCN qty
        total_mcn_excluding_self = current_total_mcn_qty - original_mcn_qty
        
        bom_qty_for_item = self.bom_qty # Get BOM Qty for this PId, CId pair
        
        if bom_qty_for_item is None:
            raise ValidationError("BOM quantity not found for this item.")
        
        # This is the remaining quantity allowed for this item across all MCNs
        remaining_bom_qty = bom_qty_for_item - total_mcn_excluding_self

        # The new quantity must not exceed the remaining allowed quantity, including the current line item's original quantity
        # Effectively, (new_qty - original_mcn_qty) <= (bom_qty - total_mcn_qty_for_item)
        if self.mcn_qty > remaining_bom_qty:
            raise ValidationError(
                f"MCN Quantity {self.mcn_qty:.3f} exceeds allowed limit. "
                f"BOM Qty: {bom_qty_for_item:.3f}, Total Credited (excluding this): {total_mcn_excluding_self:.3f}, "
                f"Remaining allowed for this item: {remaining_bom_qty:.3f}. Please enter a quantity less than or equal to {remaining_bom_qty:.3f}."
            )
        
        if self.mcn_qty <= 0:
            raise ValidationError("MCN Quantity must be greater than zero.")

```

#### 4.2 Forms

A `ModelForm` for `MaterialCreditNoteDetail` to handle the `MCNQty` field.

**`project_management/forms.py`**

```python
from django import forms
from .models import MaterialCreditNoteDetail
from django.core.validators import RegexValidator

class MaterialCreditNoteDetailForm(forms.ModelForm):
    # Regex validator for numeric input with up to 3 decimal places
    qty_regex = RegexValidator(
        regex=r'^\d{1,15}(\.\d{0,3})?$',
        message="Quantity must be a number with up to 15 digits before and 3 digits after the decimal point."
    )
    
    mcn_qty = forms.DecimalField(
        label="MCN Qty",
        max_digits=18,
        decimal_places=3,
        validators=[qty_regex],
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm box3',
            'hx-post': 'this.dataset.hxPost', # Dynamically set by HTMX if needed
            'hx-trigger': 'change', # Trigger validation on change
            'hx-target': 'this.nextElementSibling', # Target the error div
            'hx-swap': 'outerHTML',
            'type': 'number', # Hint for mobile keyboards
            'step': '0.001' # Allows 3 decimal places
        })
    )

    class Meta:
        model = MaterialCreditNoteDetail
        fields = ['mcn_qty'] # Only MCNQty is editable on this page

    def clean_mcn_qty(self):
        mcn_qty = self.cleaned_data['mcn_qty']
        
        # Apply the model's clean method for the complex business validation
        instance = self.instance
        if not instance:
            raise forms.ValidationError("Cannot validate quantity without an instance.")
            
        instance.mcn_qty = mcn_qty # Update instance with new qty before calling clean
        
        try:
            instance.full_clean(exclude=['id', 'material_credit_note', 'p_id', 'c_id']) # Only validate mcn_qty
        except forms.ValidationError as e:
            raise forms.ValidationError(e.messages) # Re-raise as form validation error
        
        return mcn_qty

```

#### 4.3 Views

The views will handle displaying the MCN details for a specific Work Order, as well as updating individual MCN detail line items. A separate view is provided for file downloads.

**`project_management/views.py`**

```python
from django.views.generic import View, ListView, UpdateView
from django.shortcuts import get_object_or_404, redirect
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404
from django.db.models import Sum
from datetime import datetime
import mimetypes

from .models import (
    WorkOrder, Customer, MaterialCreditNote,
    MaterialCreditNoteDetail, BomItem, Item, Unit
)
from .forms import MaterialCreditNoteDetailForm


class MaterialCreditNoteEditDetailsView(ListView):
    """
    Displays the header information (Work Order, Project, Customer)
    and loads the Material Credit Note details grid for editing.
    """
    model = MaterialCreditNoteDetail
    template_name = 'project_management/materialcreditnotedetail/list_and_header.html'
    context_object_name = 'mcn_details'

    def get_queryset(self):
        wo_id = self.kwargs.get('wo_id')
        wo_no = self.kwargs.get('wo_no')
        comp_id = self.request.session.get('compid') # From session
        fin_year_id = self.request.session.get('finyear') # From session

        if not all([wo_id, wo_no, comp_id, fin_year_id]):
            # Handle missing parameters gracefully, perhaps redirect or show error
            messages.error(self.request, "Missing work order details or session information.")
            return MaterialCreditNoteDetail.objects.none() # Return empty queryset

        # Fetch basic Work Order and Customer info for header
        self.work_order = get_object_or_404(
            WorkOrder,
            id=wo_id,
            wo_no=wo_no,
            comp_id=comp_id,
            fin_year_id=fin_year_id
        )
        self.customer = get_object_or_404(
            Customer,
            customer_id=self.work_order.customer_id,
            comp_id=comp_id
        )
        
        # Use the custom manager to load data with all required annotations
        return MaterialCreditNoteDetail.objects.with_aggregated_data(
            wo_no=self.work_order.wo_no,
            comp_id=comp_id,
            fin_year_id=fin_year_id
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['work_order'] = getattr(self, 'work_order', None)
        context['customer'] = getattr(self, 'customer', None)
        return context

class MaterialCreditNoteDetailTablePartialView(ListView):
    """
    Returns only the DataTables partial for HTMX updates.
    This view is called via HTMX for initial load and subsequent refreshes.
    """
    model = MaterialCreditNoteDetail
    template_name = 'project_management/materialcreditnotedetail/_table_partial.html'
    context_object_name = 'mcn_details'

    def get_queryset(self):
        # Parameters should be passed via URL kwargs or HTMX headers/params
        # For simplicity, assuming same kwargs as main view or passed by HTMX.
        wo_id = self.kwargs.get('wo_id')
        wo_no = self.kwargs.get('wo_no')
        comp_id = self.request.session.get('compid')
        fin_year_id = self.request.session.get('finyear')

        if not all([wo_id, wo_no, comp_id, fin_year_id]):
            return MaterialCreditNoteDetail.objects.none()

        work_order = get_object_or_404(
            WorkOrder, id=wo_id, wo_no=wo_no, comp_id=comp_id, fin_year_id=fin_year_id
        )
        
        return MaterialCreditNoteDetail.objects.with_aggregated_data(
            wo_no=work_order.wo_no,
            comp_id=comp_id,
            fin_year_id=fin_year_id
        )

class MaterialCreditNoteDetailUpdateView(UpdateView):
    """
    Handles inline editing of a single Material Credit Note Detail's quantity.
    Returns partial HTML for HTMX to swap the edited row.
    """
    model = MaterialCreditNoteDetail
    form_class = MaterialCreditNoteDetailForm
    template_name = 'project_management/materialcreditnotedetail/_edit_form_partial.html' # For inline editing
    context_object_name = 'mcn_detail'

    def get_object(self, queryset=None):
        # The object to be updated is determined by the PK in the URL
        return get_object_or_404(MaterialCreditNoteDetail, pk=self.kwargs['pk'])

    def get_success_url(self):
        # After successful update, typically re-render the row or trigger a table refresh.
        # HTMX will handle refreshing the relevant part of the UI.
        return reverse_lazy('mcn_edit_details_table_partial', kwargs={
            'wo_id': self.kwargs['wo_id'],
            'wo_no': self.kwargs['wo_no']
        })

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, "MCN Quantity updated successfully.")
        
        # After successful update, render the non-editable row content for HTMX to swap in.
        # The MaterialCreditNoteDetailTablePartialView can handle rendering a single row if passed context,
        # but for simplicity, we'll just re-render the entire table via HX-Trigger
        
        # If HTMX request, return 204 No Content with a trigger to refresh the table.
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMcnDetailList' # Trigger the main list view to re-fetch and render
                }
            )
        return response # Fallback for non-HTMX, though this page is designed for HTMX

    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            # For HTMX, return the form with errors directly to be swapped in
            self.object = self.get_object() # Ensure object is set for context in template
            context = self.get_context_data(form=form)
            return self.render_to_response(context)
        return super().form_invalid(form)


class MaterialCreditNoteDownloadFileView(View):
    """
    Handles downloading files (drawing/image or spec sheet) from tblDG_Item_Master.
    """
    def get(self, request, item_id, file_type):
        item = get_object_or_404(Item, id=item_id)

        file_data = None
        file_name = None
        content_type = None

        if file_type == 'drawing':
            file_data = item.file_data
            file_name = item.file_name
            content_type = item.content_type
        elif file_type == 'spec_sheet':
            file_data = item.att_data
            file_name = item.att_name
            content_type = item.att_content_type
        else:
            raise Http404("Invalid file type specified.")

        if not file_data:
            messages.error(request, f"No {file_type} file found for item {item.effective_item_code}.")
            # Redirect back to the MCN edit details page or a generic error page
            # Assuming wo_id and wo_no are available in session or referrer for redirect
            # For simplicity, returning 404 or an empty response for HTMX
            raise Http404(f"No {file_type} file found.")

        if not content_type:
            content_type, _ = mimetypes.guess_type(file_name or 'file')
            if not content_type:
                content_type = 'application/octet-stream'

        response = HttpResponse(file_data, content_type=content_type)
        response['Content-Disposition'] = f'attachment; filename="{file_name}"'
        return response

class MaterialCreditNoteCancelView(View):
    """
    Handles the "Cancel" button click, redirecting to the main MCN Edit list.
    """
    def get(self, request, *args, **kwargs):
        # This URL should point to the MCN main list or previous page.
        # Assuming '/project_management/materialcreditnote/' is the target.
        # The ModId and SubModId from ASP.NET likely controlled menu navigation
        # in their system, not directly relevant to Django URL.
        messages.info(request, "Operation cancelled.")
        return redirect(reverse_lazy('materialcreditnote_list')) # Placeholder for actual MCN list URL

```

#### 4.4 Templates

Templates will be structured for HTMX partials.

**`project_management/templates/project_management/materialcreditnotedetail/list_and_header.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Material Credit Note [MCN] - Edit Details</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-gray-700">
            <div>
                <strong class="font-semibold">WO No:</strong> {{ work_order.wo_no }}
            </div>
            <div>
                <strong class="font-semibold">Project Name:</strong> {{ work_order.task_project_title }}
            </div>
            <div>
                <strong class="font-semibold">Customer Name:</strong> {{ customer.customer_name }} [ {{ customer.customer_id }} ]
            </div>
        </div>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6">
        <h3 class="text-xl font-semibold text-gray-800 mb-4">MCN Items</h3>
        
        <div id="mcnDetailTable-container"
             hx-trigger="load, refreshMcnDetailList from:body"
             hx-get="{% url 'mcn_edit_details_table_partial' wo_id=work_order.id wo_no=work_order.wo_no %}"
             hx-swap="innerHTML">
            <!-- DataTable will be loaded here via HTMX -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading MCN Details...</p>
            </div>
        </div>
    </div>

    <div class="flex justify-center mt-6">
        <a href="{% url 'materialcreditnote_cancel' %}" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50">
            Cancel
        </a>
    </div>
</div>

<!-- Modal for form (though not directly used for this page's inline edit, keeping for common pattern) -->
<div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
     _="on click if event.target.id == 'modal' remove .is-active from me">
    <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.tailwindcss.min.js"></script>
<script>
    // Alpine.js component initialization if needed for broader UI state.
    // For inline edit, HTMX and minimal Alpine.js within the row should suffice.
</script>
{% endblock %}
```

**`project_management/templates/project_management/materialcreditnotedetail/_table_partial.html`**

```html
<div class="overflow-x-auto">
    <table id="mcnDetailTable" class="min-w-full bg-white border border-gray-200">
        <thead>
            <tr class="bg-gray-50">
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MCN No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Draw/Img</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Spec.sheet</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">BOM Qty</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">MCN Qty</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Tot MCN Qty</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for detail in mcn_details %}
            <tr id="mcn-detail-{{ detail.pk }}" hx-target="this" hx-swap="outerHTML">
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ detail.mcn_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ detail.mcn_date }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    {% if detail.get_item_id %}
                        {% with item=detail.item %}
                            {% if item.file_name %}
                                <a href="{% url 'mcn_download_file' item_id=item.id file_type='drawing' %}" target="_blank" class="text-blue-600 hover:underline">View</a>
                            {% endif %}
                        {% endwith %}
                    {% endif %}
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    {% if detail.get_item_id %}
                        {% with item=detail.item %}
                            {% if item.att_name %}
                                <a href="{% url 'mcn_download_file' item_id=item.id file_type='spec_sheet' %}" target="_blank" class="text-blue-600 hover:underline">View</a>
                            {% endif %}
                        {% endwith %}
                    {% endif %}
                </td>
                <td class="py-2 px-4 border-b border-gray-200">{{ detail.item_code }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ detail.description }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ detail.uom }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ detail.bom_qty|floatformat:3 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">
                    <span>{{ detail.mcn_qty|floatformat:3 }}</span>
                    <!-- Hidden fields for logic in JS/HTMX if needed, or rely on properties -->
                    <span class="hidden" data-bom-qty="{{ detail.bom_qty }}"></span>
                    <span class="hidden" data-tot-mcn-qty="{{ detail.total_mcn_qty_for_item }}"></span>
                    <span class="hidden" data-original-mcn-qty="{{ detail.mcn_qty }}"></span>
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ detail.total_mcn_qty_for_item|floatformat:3 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded text-xs"
                        hx-get="{% url 'mcn_edit_details_update' wo_id=detail.material_credit_note.work_order.id wo_no=detail.material_credit_note.wo_no pk=detail.pk %}"
                        hx-swap="outerHTML"
                        hx-target="#mcn-detail-{{ detail.pk }}"
                        _="on click add .htmx-indicator to #mcn-detail-{{ detail.pk }}">
                        Edit
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="12" class="py-4 px-4 text-center text-red-500 font-bold">No data found to display</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables only if it hasn't been initialized
    if (!$.fn.DataTable.isDataTable('#mcnDetailTable')) {
        $('#mcnDetailTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true
        });
    }
</script>
```

**`project_management/templates/project_management/materialcreditnotedetail/_edit_form_partial.html`**

```html
<td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.parentloop.counter }}</td> {# SN is parentloop counter #}
<td class="py-2 px-4 border-b border-gray-200">{{ mcn_detail.mcn_no }}</td>
<td class="py-2 px-4 border-b border-gray-200">{{ mcn_detail.mcn_date }}</td>
<td class="py-2 px-4 border-b border-gray-200 text-center">
    {% if mcn_detail.get_item_id %}
        {% with item=mcn_detail.item %}
            {% if item.file_name %}
                <a href="{% url 'mcn_download_file' item_id=item.id file_type='drawing' %}" target="_blank" class="text-blue-600 hover:underline">View</a>
            {% endif %}
        {% endwith %}
    {% endif %}
</td>
<td class="py-2 px-4 border-b border-gray-200 text-center">
    {% if mcn_detail.get_item_id %}
        {% with item=mcn_detail.item %}
            {% if item.att_name %}
                <a href="{% url 'mcn_download_file' item_id=item.id file_type='spec_sheet' %}" target="_blank" class="text-blue-600 hover:underline">View</a>
            {% endif %}
        {% endwith %}
    {% endif %}
</td>
<td class="py-2 px-4 border-b border-gray-200">{{ mcn_detail.item_code }}</td>
<td class="py-2 px-4 border-b border-gray-200">{{ mcn_detail.description }}</td>
<td class="py-2 px-4 border-b border-gray-200 text-center">{{ mcn_detail.uom }}</td>
<td class="py-2 px-4 border-b border-gray-200 text-right">{{ mcn_detail.bom_qty|floatformat:3 }}</td>
<td class="py-2 px-4 border-b border-gray-200 text-right">
    <form hx-put="{% url 'mcn_edit_details_update' wo_id=mcn_detail.material_credit_note.work_order.id wo_no=mcn_detail.material_credit_note.wo_no pk=mcn_detail.pk %}"
          hx-swap="outerHTML"
          hx-target="closest tr"> {# Target the closest <tr> to swap out the entire row for the updated one #}
        {% csrf_token %}
        {{ form.mcn_qty.errors }} {# Display form-level errors for this field #}
        {{ form.mcn_qty }}
        <button type="submit" class="hidden"></button> {# Hidden submit button to trigger form submission on enter #}
    </form>
    <div class="text-red-500 text-xs mt-1" id="id_mcn_qty_error_{{ mcn_detail.pk }}">
        {% if form.mcn_qty.errors %}
            {{ form.mcn_qty.errors }}
        {% elif form.non_field_errors %}
            {{ form.non_field_errors }}
        {% endif %}
    </div>
</td>
<td class="py-2 px-4 border-b border-gray-200 text-right">{{ mcn_detail.total_mcn_qty_for_item|floatformat:3 }}</td>
<td class="py-2 px-4 border-b border-gray-200 text-center">
    <button
        class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded text-xs mr-1"
        hx-put="{% url 'mcn_edit_details_update' wo_id=mcn_detail.material_credit_note.work_order.id wo_no=mcn_detail.material_credit_note.wo_no pk=mcn_detail.pk %}"
        hx-include="#mcn-detail-{{ mcn_detail.pk }} input[name='mcn_qty']"
        hx-swap="outerHTML"
        hx-target="closest tr"
        _="on click add .htmx-indicator to closest td.htmx-indicator">
        Save
    </button>
    <button
        class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-1 px-2 rounded text-xs"
        hx-get="{% url 'mcn_edit_details_table_partial' wo_id=mcn_detail.material_credit_note.work_order.id wo_no=mcn_detail.material_credit_note.wo_no %}"
        hx-swap="outerHTML"
        hx-target="closest tr"
        _="on click add .htmx-indicator to closest td.htmx-indicator">
        Cancel
    </button>
</td>
```

#### 4.5 URLs

**`project_management/urls.py`**

```python
from django.urls import path
from .views import (
    MaterialCreditNoteEditDetailsView,
    MaterialCreditNoteDetailTablePartialView,
    MaterialCreditNoteDetailUpdateView,
    MaterialCreditNoteDownloadFileView,
    MaterialCreditNoteCancelView,
)

urlpatterns = [
    # Main page to display MCN edit details for a specific Work Order
    path('material-credit-note/<int:wo_id>/<str:wo_no>/edit-details/',
         MaterialCreditNoteEditDetailsView.as_view(),
         name='mcn_edit_details'),

    # HTMX endpoint to fetch the DataTable content
    path('material-credit-note/<int:wo_id>/<str:wo_no>/table-partial/',
         MaterialCreditNoteDetailTablePartialView.as_view(),
         name='mcn_edit_details_table_partial'),

    # HTMX endpoint for inline update of a single MCN detail row
    path('material-credit-note/<int:wo_id>/<str:wo_no>/detail/<int:pk>/update/',
         MaterialCreditNoteDetailUpdateView.as_view(),
         name='mcn_edit_details_update'),

    # Endpoint for file downloads (drawing/image and spec sheet)
    path('items/<int:item_id>/download/<str:file_type>/',
         MaterialCreditNoteDownloadFileView.as_view(),
         name='mcn_download_file'),

    # Placeholder for the cancel action (redirects to main MCN list)
    path('material-credit-note/cancel/',
         MaterialCreditNoteCancelView.as_view(),
         name='materialcreditnote_cancel'),
    
    # Placeholder for the main MCN list page (assuming this is where cancel goes)
    # This URL needs to be defined in your main project_management app's urls.py
    path('material-credit-note/',
         lambda request: HttpResponse("This is the Material Credit Note List Page (placeholder)."),
         name='materialcreditnote_list'), # Define your actual MCN list view here
]
```

#### 4.6 Tests

Comprehensive tests for models, forms, and views ensure robustness.

**`project_management/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db.utils import IntegrityError
from django.core.exceptions import ValidationError
from datetime import date, time
from decimal import Decimal
import json

from .models import (
    Unit, Item, BomItem, Customer, WorkOrder,
    MaterialCreditNote, MaterialCreditNoteDetail
)

class MaterialCreditNoteModelsTest(TestCase):
    """
    Tests for all related models and MaterialCreditNoteDetail's properties/methods.
    """
    @classmethod
    def setUpTestData(cls):
        # Create common test data for all tests
        cls.comp_id = 1
        cls.fin_year_id = 2023
        cls.wo_id = 101
        cls.wo_no = "WO-001"
        cls.customer_id = 201

        cls.unit = Unit.objects.create(id=1, symbol="PCS")
        cls.item_drawing = Item.objects.create(
            id=1, item_code="ITEM-A", manf_desc="Product A", uom_basic=cls.unit,
            file_name="drawing_a.pdf", file_data=b"drawing_content", content_type="application/pdf"
        )
        cls.item_spec = Item.objects.create(
            id=2, item_code="ITEM-B", manf_desc="Product B", uom_basic=cls.unit,
            att_name="spec_b.docx", att_data=b"spec_content", att_content_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        )
        cls.item_no_files = Item.objects.create(
            id=3, item_code="ITEM-C", manf_desc="Product C", uom_basic=cls.unit
        )

        cls.customer = Customer.objects.create(
            customer_id=cls.customer_id, customer_name="Test Customer", comp_id=cls.comp_id
        )
        cls.work_order = WorkOrder.objects.create(
            id=cls.wo_id, task_project_title="Test Project", customer=cls.customer,
            wo_no=cls.wo_no, comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )

        cls.bom_item1 = BomItem.objects.create(
            id=1, wo_no=cls.wo_no, fin_year_id=cls.fin_year_id, comp_id=cls.comp_id,
            p_id=1, c_id=10, item=cls.item_drawing, qty=Decimal('100.000') # BOM Qty
        )
        cls.bom_item2 = BomItem.objects.create(
            id=2, wo_no=cls.wo_no, fin_year_id=cls.fin_year_id, comp_id=cls.comp_id,
            p_id=2, c_id=20, item=cls.item_spec, qty=Decimal('50.000')
        )
        cls.bom_item3 = BomItem.objects.create(
            id=3, wo_no=cls.wo_no, fin_year_id=cls.fin_year_id, comp_id=cls.comp_id,
            p_id=3, c_id=30, item=cls.item_no_files, qty=Decimal('200.000')
        )

        # MCN 1
        cls.mcn1 = MaterialCreditNote.objects.create(
            id=1, sys_date=date(2024, 1, 15), sys_time=time(10, 0, 0),
            session_id="testuser", comp_id=cls.comp_id, fin_year_id=cls.fin_year_id,
            mcn_no="MCN-001", wo_no=cls.wo_no
        )
        cls.mcn1_detail1 = MaterialCreditNoteDetail.objects.create(
            id=1, material_credit_note=cls.mcn1, p_id=1, c_id=10, mcn_qty=Decimal('20.000')
        )
        cls.mcn1_detail2 = MaterialCreditNoteDetail.objects.create(
            id=2, material_credit_note=cls.mcn1, p_id=2, c_id=20, mcn_qty=Decimal('10.000')
        )

        # MCN 2 for the same WO/items to test TotMCNQty
        cls.mcn2 = MaterialCreditNote.objects.create(
            id=2, sys_date=date(2024, 2, 1), sys_time=time(11, 0, 0),
            session_id="testuser", comp_id=cls.comp_id, fin_year_id=cls.fin_year_id,
            mcn_no="MCN-002", wo_no=cls.wo_no
        )
        cls.mcn2_detail1 = MaterialCreditNoteDetail.objects.create(
            id=3, material_credit_note=cls.mcn2, p_id=1, c_id=10, mcn_qty=Decimal('15.000')
        )

    def test_material_credit_note_detail_creation(self):
        self.assertEqual(MaterialCreditNoteDetail.objects.count(), 3)
        detail = MaterialCreditNoteDetail.objects.get(id=1)
        self.assertEqual(detail.mcn_qty, Decimal('20.000'))
        self.assertEqual(detail.material_credit_note.mcn_no, "MCN-001")

    def test_mcn_detail_properties(self):
        detail = MaterialCreditNoteDetail.objects.get(id=1)
        self.assertEqual(detail.mcn_no, "MCN-001")
        self.assertEqual(detail.mcn_date, "15-01-2024")
        self.assertEqual(detail.item_code, "ITEM-A")
        self.assertEqual(detail.description, "Product A")
        self.assertEqual(detail.uom, "PCS")
        self.assertEqual(detail.bom_qty, Decimal('100.000'))
        self.assertEqual(detail.get_item_id(), 1) # Item ID for ITEM-A

        detail_from_qset = MaterialCreditNoteDetail.objects.with_aggregated_data(
            wo_no=self.wo_no, comp_id=self.comp_id, fin_year_id=self.fin_year_id
        ).get(id=1)
        self.assertEqual(detail_from_qset.item_code, "ITEM-A")
        self.assertEqual(detail_from_qset.description, "Product A")
        self.assertEqual(detail_from_qset.uom, "PCS")
        self.assertEqual(detail_from_qset.bom_qty, Decimal('100.000'))

    def test_total_mcn_qty_for_item_property(self):
        # MCN1_detail1 (PId 1, CId 10) has 20.000, MCN2_detail1 (PId 1, CId 10) has 15.000
        # Total for PId 1, CId 10 should be 35.000
        detail1 = MaterialCreditNoteDetail.objects.get(id=1)
        self.assertEqual(detail1.total_mcn_qty_for_item, Decimal('35.000'))

        # MCN1_detail2 (PId 2, CId 20) has 10.000. No other MCN for this item.
        detail2 = MaterialCreditNoteDetail.objects.get(id=2)
        self.assertEqual(detail2.total_mcn_qty_for_item, Decimal('10.000'))

    def test_material_credit_note_detail_manager_with_aggregated_data(self):
        details_with_data = MaterialCreditNoteDetail.objects.with_aggregated_data(
            wo_no=self.wo_no, comp_id=self.comp_id, fin_year_id=self.fin_year_id
        ).order_by('id') # Ensure order
        
        self.assertEqual(len(details_with_data), 3)

        detail1 = details_with_data[0] # MCN1_detail1
        self.assertEqual(detail1.id, self.mcn1_detail1.id)
        self.assertEqual(detail1.mcn_qty, Decimal('20.000'))
        self.assertEqual(detail1.mcn_no, "MCN-001")
        self.assertEqual(detail1.item_code, "ITEM-A")
        self.assertEqual(detail1.bom_qty, Decimal('100.000'))
        self.assertEqual(detail1.total_mcn_qty_for_item, Decimal('35.000'))

        detail2 = details_with_data[1] # MCN1_detail2
        self.assertEqual(detail2.id, self.mcn1_detail2.id)
        self.assertEqual(detail2.mcn_qty, Decimal('10.000'))
        self.assertEqual(detail2.mcn_no, "MCN-001")
        self.assertEqual(detail2.item_code, "ITEM-B")
        self.assertEqual(detail2.bom_qty, Decimal('50.000'))
        self.assertEqual(detail2.total_mcn_qty_for_item, Decimal('10.000'))

        detail3 = details_with_data[2] # MCN2_detail1
        self.assertEqual(detail3.id, self.mcn2_detail1.id)
        self.assertEqual(detail3.mcn_qty, Decimal('15.000'))
        self.assertEqual(detail3.mcn_no, "MCN-002")
        self.assertEqual(detail3.item_code, "ITEM-A")
        self.assertEqual(detail3.bom_qty, Decimal('100.000'))
        self.assertEqual(detail3.total_mcn_qty_for_item, Decimal('35.000'))


    def test_mcn_qty_validation_valid(self):
        # Current: id=1, mcn_qty=20, bom_qty=100, total_mcn_qty_for_item=35
        # Remaining allowed for item (P1,C10) is 100 - (35 - 20) = 100 - 15 = 85
        # So new qty can be up to 85.
        detail = MaterialCreditNoteDetail.objects.get(id=1)
        detail.mcn_qty = Decimal('30.000') # Valid, 30 <= 85
        try:
            detail.full_clean()
        except ValidationError as e:
            self.fail(f"Validation failed unexpectedly: {e.messages}")

    def test_mcn_qty_validation_exceeds_bom_limit(self):
        # Current: id=1, mcn_qty=20, bom_qty=100, total_mcn_qty_for_item=35
        # Remaining allowed for item (P1,C10) is 100 - (35 - 20) = 85
        detail = MaterialCreditNoteDetail.objects.get(id=1)
        detail.mcn_qty = Decimal('90.000') # Invalid, 90 > 85
        with self.assertRaises(ValidationError) as cm:
            detail.full_clean()
        self.assertIn("MCN Quantity 90.000 exceeds allowed limit.", cm.exception.messages[0])

    def test_mcn_qty_validation_zero_or_negative(self):
        detail = MaterialCreditNoteDetail.objects.get(id=1)
        detail.mcn_qty = Decimal('0.000')
        with self.assertRaises(ValidationError) as cm:
            detail.full_clean()
        self.assertIn("MCN Quantity must be greater than zero.", cm.exception.messages[0])

        detail.mcn_qty = Decimal('-5.000')
        with self.assertRaises(ValidationError) as cm:
            detail.full_clean()
        self.assertIn("MCN Quantity must be greater than zero.", cm.exception.messages[0])


class MaterialCreditNoteDetailFormTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Set up minimal data for form tests
        cls.comp_id = 1
        cls.fin_year_id = 2023
        cls.wo_id = 101
        cls.wo_no = "WO-001"
        cls.customer_id = 201

        cls.unit = Unit.objects.create(id=1, symbol="PCS")
        cls.item = Item.objects.create(id=1, item_code="ITEM-A", manf_desc="Product A", uom_basic=cls.unit)

        cls.customer = Customer.objects.create(
            customer_id=cls.customer_id, customer_name="Test Customer", comp_id=cls.comp_id
        )
        cls.work_order = WorkOrder.objects.create(
            id=cls.wo_id, task_project_title="Test Project", customer=cls.customer,
            wo_no=cls.wo_no, comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )
        cls.bom_item = BomItem.objects.create(
            id=1, wo_no=cls.wo_no, fin_year_id=cls.fin_year_id, comp_id=cls.comp_id,
            p_id=1, c_id=10, item=cls.item, qty=Decimal('100.000') # BOM Qty
        )
        cls.mcn = MaterialCreditNote.objects.create(
            id=1, sys_date=date(2024, 1, 15), mcn_no="MCN-001", wo_no=cls.wo_no,
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )
        cls.mcn_detail = MaterialCreditNoteDetail.objects.create(
            id=1, material_credit_note=cls.mcn, p_id=1, c_id=10, mcn_qty=Decimal('20.000')
        )

    def test_mcn_qty_field_valid_data(self):
        form_data = {'mcn_qty': '30.000'}
        form = MaterialCreditNoteDetailForm(data=form_data, instance=self.mcn_detail)
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['mcn_qty'], Decimal('30.000'))

    def test_mcn_qty_field_invalid_format(self):
        form_data = {'mcn_qty': 'abc'}
        form = MaterialCreditNoteDetailForm(data=form_data, instance=self.mcn_detail)
        self.assertFalse(form.is_valid())
        self.assertIn('mcn_qty', form.errors)
        self.assertIn("Enter a number.", form.errors['mcn_qty'])

    def test_mcn_qty_field_invalid_decimal_places(self):
        form_data = {'mcn_qty': '10.1234'}
        form = MaterialCreditNoteDetailForm(data=form_data, instance=self.mcn_detail)
        self.assertFalse(form.is_valid())
        self.assertIn('mcn_qty', form.errors)
        self.assertIn("Quantity must be a number with up to 15 digits before and 3 digits after the decimal point.", form.errors['mcn_qty'])

    def test_mcn_qty_field_exceeds_bom_limit(self):
        # Current: mcn_qty=20, bom_qty=100, total_mcn_qty_for_item=20 (only one MCN for this item)
        # Remaining allowed for item (P1,C10) is 100 - (20 - 20) = 100
        # New MCN Qty for this record.
        # If we try to set it to 101, it should fail
        form_data = {'mcn_qty': '101.000'} # Invalid: exceeds BOMQty
        form = MaterialCreditNoteDetailForm(data=form_data, instance=self.mcn_detail)
        self.assertFalse(form.is_valid())
        self.assertIn('mcn_qty', form.errors)
        self.assertIn("MCN Quantity 101.000 exceeds allowed limit.", form.errors['mcn_qty'][0])

    def test_mcn_qty_field_zero_or_negative(self):
        form_data = {'mcn_qty': '0.000'}
        form = MaterialCreditNoteDetailForm(data=form_data, instance=self.mcn_detail)
        self.assertFalse(form.is_valid())
        self.assertIn('mcn_qty', form.errors)
        self.assertIn("MCN Quantity must be greater than zero.", form.errors['mcn_qty'][0])


class MaterialCreditNoteViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.comp_id = 1
        cls.fin_year_id = 2023
        cls.wo_id = 101
        cls.wo_no = "WO-001"
        cls.customer_id = 201

        cls.unit = Unit.objects.create(id=1, symbol="PCS")
        cls.item = Item.objects.create(
            id=1, item_code="ITEM-A", manf_desc="Product A", uom_basic=cls.unit,
            file_name="drawing_a.pdf", file_data=b"drawing_content", content_type="application/pdf"
        )
        cls.customer = Customer.objects.create(
            customer_id=cls.customer_id, customer_name="Test Customer", comp_id=cls.comp_id
        )
        cls.work_order = WorkOrder.objects.create(
            id=cls.wo_id, task_project_title="Test Project", customer=cls.customer,
            wo_no=cls.wo_no, comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )
        cls.bom_item = BomItem.objects.create(
            id=1, wo_no=cls.wo_no, fin_year_id=cls.fin_year_id, comp_id=cls.comp_id,
            p_id=1, c_id=10, item=cls.item, qty=Decimal('100.000')
        )
        cls.mcn = MaterialCreditNote.objects.create(
            id=1, sys_date=date(2024, 1, 15), mcn_no="MCN-001", wo_no=cls.wo_no,
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )
        cls.mcn_detail = MaterialCreditNoteDetail.objects.create(
            id=1, material_credit_note=cls.mcn, p_id=1, c_id=10, mcn_qty=Decimal('20.000')
        )

    def setUp(self):
        self.client = Client()
        # Set session variables required by views
        session = self.client.session
        session['compid'] = self.comp_id
        session['finyear'] = self.fin_year_id
        session.save()

    def test_edit_details_view_get(self):
        url = reverse('mcn_edit_details', kwargs={'wo_id': self.wo_id, 'wo_no': self.wo_no})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/materialcreditnotedetail/list_and_header.html')
        self.assertIn('work_order', response.context)
        self.assertIn('customer', response.context)
        self.assertEqual(response.context['work_order'].wo_no, self.wo_no)
        self.assertEqual(response.context['customer'].customer_name, self.customer.customer_name)

    def test_edit_details_view_get_missing_params(self):
        url = reverse('mcn_edit_details', kwargs={'wo_id': self.wo_id, 'wo_no': 'INVALID_WO_NO'})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404) # Due to get_object_or_404

    def test_table_partial_view_get(self):
        url = reverse('mcn_edit_details_table_partial', kwargs={'wo_id': self.wo_id, 'wo_no': self.wo_no})
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/materialcreditnotedetail/_table_partial.html')
        self.assertIn('mcn_details', response.context)
        self.assertContains(response, 'ITEM-A') # Check if item is in the table

    def test_detail_update_view_get(self):
        url = reverse('mcn_edit_details_update', kwargs={'wo_id': self.wo_id, 'wo_no': self.wo_no, 'pk': self.mcn_detail.pk})
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/materialcreditnotedetail/_edit_form_partial.html')
        self.assertIn('form', response.context)
        self.assertContains(response, '<input type="number" name="mcn_qty" value="20.000"') # Check if form is rendered with current value

    def test_detail_update_view_put_valid(self):
        url = reverse('mcn_edit_details_update', kwargs={'wo_id': self.wo_id, 'wo_no': self.wo_no, 'pk': self.mcn_detail.pk})
        new_qty = Decimal('25.000')
        response = self.client.put(
            url,
            json.dumps({'mcn_qty': str(new_qty)}), # Convert Decimal to string for JSON
            content_type='application/json',
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 204) # No content, HTMX trigger
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMcnDetailList')

        self.mcn_detail.refresh_from_db()
        self.assertEqual(self.mcn_detail.mcn_qty, new_qty)

    def test_detail_update_view_put_invalid(self):
        url = reverse('mcn_edit_details_update', kwargs={'wo_id': self.wo_id, 'wo_no': self.wo_no, 'pk': self.mcn_detail.pk})
        invalid_qty = Decimal('101.000') # Exceeds BOM Qty (100)
        response = self.client.put(
            url,
            json.dumps({'mcn_qty': str(invalid_qty)}),
            content_type='application/json',
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200) # Returns partial HTML with errors
        self.assertTemplateUsed(response, 'project_management/materialcreditnotedetail/_edit_form_partial.html')
        self.assertContains(response, 'MCN Quantity 101.000 exceeds allowed limit.') # Check for error message

        self.mcn_detail.refresh_from_db()
        self.assertEqual(self.mcn_detail.mcn_qty, Decimal('20.000')) # Quantity should not have changed

    def test_download_file_drawing(self):
        url = reverse('mcn_download_file', kwargs={'item_id': self.item.id, 'file_type': 'drawing'})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/pdf')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="drawing_a.pdf"')
        self.assertEqual(response.content, b"drawing_content")

    def test_download_file_no_file(self):
        url = reverse('mcn_download_file', kwargs={'item_id': self.item.id, 'file_type': 'spec_sheet'}) # Item has no spec sheet
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

    def test_download_file_invalid_type(self):
        url = reverse('mcn_download_file', kwargs={'item_id': self.item.id, 'file_type': 'invalid'})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

    def test_cancel_view(self):
        url = reverse('materialcreditnote_cancel')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302) # Redirects
        self.assertRedirects(response, reverse('materialcreditnote_list'))
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
HTMX and Alpine.js are integrated as follows:

*   **List View Loading:** The main `list_and_header.html` template uses `hx-get` with `hx-trigger="load, refreshMcnDetailList from:body"` on a container `div` (`#mcnDetailTable-container`) to load the DataTables partial (`_table_partial.html`) dynamically. This ensures the table is loaded via AJAX after the initial page load and refreshes whenever a `refreshMcnDetailList` event is triggered (e.g., after an update).
*   **DataTables Initialization:** The `_table_partial.html` template includes a `<script>` block that initializes the DataTables library on the `mcnDetailTable` once the partial is loaded, providing client-side searching, sorting, and pagination.
*   **Inline Editing (HTMX Swap):**
    *   The "Edit" button in each row within `_table_partial.html` uses `hx-get` to fetch the `_edit_form_partial.html` for that specific `mcn_detail.pk`.
    *   `hx-target="this"` and `hx-swap="outerHTML"` ensure that the entire `<tr>` element is replaced by the form partial, making the row editable inline.
    *   The form within `_edit_form_partial.html` uses `hx-put` to submit the updated quantity.
    *   `hx-target="closest tr"` and `hx-swap="outerHTML"` on the form ensure that upon successful submission, the updated read-only row replaces the edit form.
    *   A hidden submit button in the form allows pressing Enter to submit.
    *   "Save" and "Cancel" buttons within the edit form also use HTMX to manage submission or cancellation.
    *   On successful save (`PUT` request returning 204 No Content), the `HX-Trigger: refreshMcnDetailList` header is sent, causing the main `mcnDetailTable-container` to re-fetch and re-render the entire table, ensuring all aggregate calculations (`Tot MCN Qty`) are up-to-date across all rows.
*   **Form Validation:** Django Forms handle server-side validation. When an `hx-put` request for updating is invalid, the `MaterialCreditNoteDetailUpdateView` returns the `_edit_form_partial.html` with errors, which HTMX swaps back into the row, showing the validation messages directly.
*   **File Downloads:** The "View" links for drawing/image and spec sheets are standard `<a>` tags targeting a new tab (`target="_blank"`), which link to dedicated Django views (`mcn_download_file`) that serve the binary file data from the database.
*   **Alpine.js:** While not strictly necessary for the core CRUD and table interactions (HTMX handles these), Alpine.js is recommended for minor client-side UI states, such as displaying and hiding modals (if a modal approach were used for forms, which is commented out in `list_and_header.html` but kept for illustration of the pattern). In this specific inline edit scenario, Alpine.js could manage temporary UI states like showing a loading spinner on individual row edits if HTMX's `htmx-indicator` is not sufficient, or more complex interactive validation feedback before server submission.

---

## Final Notes

*   **Placeholders:** `[APP_NAME]` is `project_management`. `[MODEL_NAME]` is `MaterialCreditNoteDetail`, `[MODEL_NAME_LOWER]` is `materialcreditnotedetail`, etc. These have been replaced with the specific values in the generated code.
*   **DRY Principles:** Templates are kept DRY by using partials (`_table_partial.html`, `_edit_form_partial.html`). The `base.html` is assumed to exist and is extended.
*   **Fat Model, Thin View:** All complex business logic (e.g., calculating `TotMCNQty`, validating `MCNQty` against `BOMQty` and existing MCNs) is encapsulated within the `MaterialCreditNoteDetail` model and its custom manager. Views remain concise, primarily handling HTTP requests and responses, and delegating data manipulation to the model layer.
*   **Test Coverage:** Comprehensive unit tests for the models and integration tests for all views are provided to ensure functionality and data integrity.
*   **Database Migrations:** Remember to use `python manage.py inspectdb` as a starting point, then manually refine the generated models based on the schema and relationships inferred from the ASP.NET code, especially for `managed = False` and correct `db_column` mappings.
*   **Session Management:** The original ASP.NET code uses `Session["compid"]` and `Session["finyear"]`. In Django, these are accessed via `request.session.get('compid')`. Ensure your Django project's session middleware is configured correctly.
*   **Error Handling:** Basic error handling is included (e.g., 404 for missing objects). In a production environment, robust error logging and user-friendly error pages should be implemented.
*   **Styling:** Tailwind CSS classes are applied directly in the templates for responsive and modern styling. You will need to ensure Tailwind CSS is properly set up in your Django project.