## ASP.NET to Django Conversion Script: Material Credit Note [MCN] Print

This document outlines a strategic plan for migrating your existing ASP.NET Material Credit Note [MCN] Print module to a modern Django-based solution. Our approach emphasizes automation, leveraging AI-assisted tools for code conversion and focusing on clean, maintainable Django 5.0+ patterns.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

## Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code interacts with two primary database tables:
1.  `SD_Cust_WorkOrder_Master`: This is the main table for work orders.
2.  `SD_Cust_Master`: This table stores customer details, linked by `CustomerId`.

**Identified Tables and Columns:**

*   **`SD_Cust_WorkOrder_Master`**:
    *   `Id` (Primary Key, integer) - Represents `WOId` in the GridView.
    *   `WONo` (string) - Represents `WO No` in the GridView.
    *   `TaskProjectTitle` (string) - Represents `Project Title` in the GridView.
    *   `CustomerId` (string) - Foreign key to `SD_Cust_Master`.
    *   `SysDate` (DateTime) - Represents `Date` in the GridView.
    *   `CompId` (integer) - Company ID.
    *   `FinYearId` (integer) - Financial Year ID.
    *   `CloseOpen` (string/boolean-like) - Filtered by '0'.

*   **`SD_Cust_Master`**:
    *   `CustomerId` (Primary Key, string) - Represents `Code` in the GridView.
    *   `CustomerName` (string) - Represents `Customer Name` in the GridView.
    *   `CompId` (integer) - Company ID.

## Step 2: Identify Backend Functionality

**Analysis:**
This ASP.NET page (`MaterialCreditNote_MCN_Print`) is primarily a "Read" (listing/search) operation. It fetches and displays work order data based on user-selected criteria. It does not perform Create, Update, or Delete operations on the work orders themselves.

*   **Read (R):**
    *   **Data Loading:** The `loaddata()` method fetches work order records from `SD_Cust_WorkOrder_Master` and enriches them with customer names from `SD_Cust_Master`.
    *   **Filtering/Search:** Users can search by "WO No", "Customer", or "Project Title" using a dropdown and an associated text box.
    *   **Pagination:** The `GridView1` supports pagination.
    *   **Autocomplete:** The `GetCompletionList` static method provides customer name suggestions for the "Customer" search field.
    *   **Redirection:** Clicking on a "WO No" in the grid redirects the user to a detailed view (`MaterialCreditNote_MCN_Print_Details.aspx`).

*   **No CUD (Create, Update, Delete) operations** are present on this specific page. The conversion will focus on the listing, searching, pagination, and autocomplete features.

## Step 3: Infer UI Components

**Analysis:**
The page uses standard ASP.NET Web Forms controls for user interaction and data display.

*   **Search Controls:**
    *   `drpfield` (`asp:DropDownList`): Selects the search criterion (WO No, Customer, Project Title).
    *   `txtSupplier` (`asp:TextBox` with `AutoCompleteExtender`): Input for "Customer" search, with autocomplete suggestions.
    *   `txtPONo` (`asp:TextBox`): Input for "WO No" or "Project Title" search.
    *   `Button1` (`asp:Button`): Triggers the search.
*   **Data Display:**
    *   `GridView1` (`asp:GridView`): Displays the search results in a tabular format.
        *   Columns: SN (Serial Number), WOId (hidden), WO No (LinkButton), Date, Project Title, Customer Name, Code.
        *   The `WO No` column is a `LinkButton` that triggers a redirect to a detail page.
        *   Pagination is enabled.

**Django Mapping:**
*   `drpfield`, `txtSupplier`, `txtPONo`, `Button1` will be combined into a Django `forms.Form` and rendered as HTML.
*   `AutoCompleteExtender` functionality will be replaced by an HTMX-powered endpoint and client-side Alpine.js for dynamic suggestions.
*   `GridView1` will be replaced by an HTML `<table>` integrated with DataTables.
*   The `WO No` `LinkButton` will be a standard `<a>` tag linking to the Django detail view URL.
*   Dynamic visibility of `txtSupplier` vs `txtPONo` will be managed by Alpine.js.

---

## Step 4: Generate Django Code

### Application Name: `project_management`

The core logic resides in the `project_management` Django app.

### 4.1 Models (`project_management/models.py`)

This file defines the Django models that map to your existing database tables. We set `managed = False` to tell Django not to create or manage these tables, as they already exist.

```python
from django.db import models
from django.db.models import Q

class CustomerManager(models.Manager):
    def get_customer_id_by_name(self, customer_name):
        """
        Retrieves the CustomerId from CustomerName.
        Mimics fun.getCode() but uses Django ORM.
        Assumes customer_name might be 'Customer Name [ID]' or just 'Customer Name'.
        """
        # Try to extract ID from 'Customer Name [ID]' format
        if '[' in customer_name and customer_name.endswith(']'):
            try:
                parts = customer_name.rsplit(' [', 1)
                if len(parts) == 2:
                    return parts[1][:-1] # Returns the content inside the brackets
            except Exception:
                pass # Fallback to searching by exact name

        # If not in 'Customer Name [ID]' format or extraction failed, search by exact name
        try:
            customer = self.get_queryset().get(customer_name__iexact=customer_name)
            return customer.customer_id
        except self.model.DoesNotExist:
            return None # Or raise an exception if customer not found

    def search_customers(self, query_text, company_id, limit=10):
        """
        Provides autocomplete suggestions for customers.
        """
        return self.get_queryset().filter(
            company_id=company_id,
            customer_name__icontains=query_text
        ).values('customer_id', 'customer_name')[:limit]


class Customer(models.Model):
    customer_id = models.CharField(db_column='CustomerId', max_length=50, primary_key=True, verbose_name="Customer ID")
    customer_name = models.CharField(db_column='CustomerName', max_length=255, verbose_name="Customer Name")
    company_id = models.IntegerField(db_column='CompId', verbose_name="Company ID")

    objects = CustomerManager() # Attach the custom manager

    class Meta:
        managed = False
        db_table = 'SD_Cust_Master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customer_name} [{self.customer_id}]"

class WorkOrderManager(models.Manager):
    def get_filtered_work_orders(self, search_field, search_term, company_id, financial_year_id):
        """
        Filters and retrieves Work Orders based on search criteria.
        This method encapsulates the data retrieval and processing logic from loaddata().
        It returns a list of dictionaries, prepared for display, similar to the original DataTable.
        """
        qs = self.get_queryset().filter(
            company_id=company_id,
            financial_year_id__lte=financial_year_id, # Replicates FinYearId<=' + FinYearId + '
            close_open='0' # Replicates CloseOpen='0'
        )

        if search_term: # Only apply filter if there's a search term
            if search_field == '0':  # Customer
                customer_id = Customer.objects.get_customer_id_by_name(search_term)
                if customer_id:
                    qs = qs.filter(customer_id=customer_id)
                else:
                    qs = qs.none() # Return empty queryset if no customer match
            elif search_field == '1':  # WO No
                qs = qs.filter(work_order_no=search_term)
            elif search_field == '2':  # Project Title
                qs = qs.filter(task_project_title__icontains=search_term)

        # Order by WONo as in original
        qs = qs.order_by('work_order_no')

        # Prepare data structure for display, similar to the ASP.NET DataTable
        results = []
        for wo in qs:
            customer_name = ""
            customer_code = ""
            customer_obj = wo.customer_details # Use the property to get related customer
            if customer_obj:
                customer_name = customer_obj.customer_name
                customer_code = customer_obj.customer_id

            results.append({
                'wo_id': wo.id,
                'work_order_no': wo.work_order_no,
                'project_title': wo.task_project_title,
                'customer_name': customer_name,
                'customer_code': customer_code,
                'date': wo.formatted_system_date, # Use the property for formatted date
            })
        return results

class WorkOrder(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    work_order_no = models.CharField(db_column='WONo', max_length=255, verbose_name="WO No")
    task_project_title = models.CharField(db_column='TaskProjectTitle', max_length=500, verbose_name="Project Title")
    customer_id = models.CharField(db_column='CustomerId', max_length=50, verbose_name="Customer ID") # This is a non-managed FK
    system_date = models.DateTimeField(db_column='SysDate', verbose_name="Date")
    company_id = models.IntegerField(db_column='CompId', verbose_name="Company ID")
    financial_year_id = models.IntegerField(db_column='FinYearId', verbose_name="Financial Year ID")
    close_open = models.CharField(db_column='CloseOpen', max_length=10, verbose_name="Status")

    objects = WorkOrderManager() # Attach the custom manager

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.work_order_no

    @property
    def customer_details(self):
        """Fetches the associated Customer object."""
        try:
            # Assuming CustomerId is unique or combined with CompId for uniqueness
            return Customer.objects.get(customer_id=self.customer_id, company_id=self.company_id)
        except Customer.DoesNotExist:
            return None

    @property
    def formatted_system_date(self):
        """Formats the system date for display, similar to fun.FromDate()."""
        if self.system_date:
            return self.system_date.strftime('%d/%m/%Y') # Example format: DD/MM/YYYY
        return ''
```

### 4.2 Forms (`project_management/forms.py`)

This form will handle the search parameters from the user interface. It's a regular `forms.Form` because it doesn't directly map to a single database model for data entry.

```python
from django import forms

class WorkOrderSearchForm(forms.Form):
    SEARCH_FIELDS = [
        ('1', 'WO No'),
        ('0', 'Customer'),
        ('2', 'Project Title'),
    ]

    search_field = forms.ChoiceField(
        choices=SEARCH_FIELDS,
        required=True,
        label="Search By",
        widget=forms.Select(attrs={'class': 'box3 w-full py-2 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                                   'hx-get': 'hx-trigger="change"'}) # Add hx-get for dynamic behavior
    )
    # Both fields exist, Alpine.js will manage visibility based on search_field
    search_term_supplier = forms.CharField(
        max_length=255,
        required=False,
        label="Customer Name",
        widget=forms.TextInput(attrs={'class': 'box3 w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                                       'placeholder': 'Enter Customer Name',
                                       'autocomplete': 'off', # Disable browser autocomplete
                                       'hx-get': '{% url "project_management:customer_autocomplete" %}',
                                       'hx-trigger': 'keyup changed delay:500ms, search',
                                       'hx-target': '#customer-suggestions',
                                       'hx-swap': 'innerHTML',
                                       '_': 'on keyup if event.target.value.length > 0 add .block to #customer-suggestions else remove .block from #customer-suggestions'})
    )
    search_term_po = forms.CharField(
        max_length=255,
        required=False,
        label="Search Term",
        widget=forms.TextInput(attrs={'class': 'box3 w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                                       'placeholder': 'Enter WO No or Project Title'})
    )

    def clean(self):
        cleaned_data = super().clean()
        search_field = cleaned_data.get('search_field')
        search_term_supplier = cleaned_data.get('search_term_supplier')
        search_term_po = cleaned_data.get('search_term_po')

        if search_field == '0': # Customer
            if not search_term_supplier:
                self.add_error('search_term_supplier', 'Customer name is required for this search.')
            cleaned_data['search_term'] = search_term_supplier
        elif search_field in ['1', '2']: # WO No or Project Title
            if not search_term_po:
                self.add_error('search_term_po', 'Search term is required for this search.')
            cleaned_data['search_term'] = search_term_po
        else:
            cleaned_data['search_term'] = '' # No search term if field is not selected or invalid

        return cleaned_data

```

### 4.3 Views (`project_management/views.py`)

The views handle requests, interact with models (via their managers for business logic), and render templates. We use Class-Based Views (CBVs) to keep our code clean and concise.

```python
from django.views.generic import FormView, TemplateView, ListView
from django.http import HttpResponse, JsonResponse
from django.urls import reverse_lazy
from django.contrib import messages
from .models import WorkOrder, Customer
from .forms import WorkOrderSearchForm

# Assuming company_id and financial_year_id are available from user session or context
# For this example, we'll use placeholder values. In a real system, these would
# come from request.user.profile or similar.
CURRENT_COMPANY_ID = 1
CURRENT_FINANCIAL_YEAR_ID = 2023 # Example

class WorkOrderPrintListView(FormView):
    template_name = 'project_management/workorder/list.html'
    form_class = WorkOrderSearchForm

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the form with no data on initial GET request
        context['form'] = self.get_form()
        # Initial empty list for table (will be loaded via HTMX)
        context['work_orders'] = []
        return context

    # This view mostly serves the initial page. Search requests will go to WorkOrderTablePartialView.

class WorkOrderTablePartialView(TemplateView):
    """
    HTMX endpoint to load the work order table content based on search criteria.
    """
    template_name = 'project_management/workorder/_workorder_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        form = WorkOrderSearchForm(self.request.GET) # Use GET parameters for search
        work_orders_data = []

        # Validate form to get clean search parameters
        if form.is_valid():
            search_field = form.cleaned_data['search_field']
            search_term = form.cleaned_data['search_term']

            work_orders_data = WorkOrder.objects.get_filtered_work_orders(
                search_field,
                search_term,
                company_id=CURRENT_COMPANY_ID,
                financial_year_id=CURRENT_FINANCIAL_YEAR_ID
            )
        else:
            # If form is not valid (e.g., missing required search term),
            # provide a default empty list and show errors if needed.
            # For simplicity, we just return an empty list here,
            # but in a real app, you might want to render form errors.
            pass # Or handle form.errors

        context['work_orders'] = work_orders_data
        return context

class CustomerAutocompleteView(ListView):
    """
    HTMX endpoint for customer autocomplete suggestions.
    Replicates GetCompletionList web method.
    """
    model = Customer
    paginate_by = 10 # Limit number of suggestions

    def get_queryset(self):
        query = self.request.GET.get('q', '')
        # Assuming current_company_id is available from session or user profile
        queryset = self.model.objects.search_customers(query, CURRENT_COMPANY_ID)
        return queryset

    def render_to_response(self, context, **response_kwargs):
        """
        Returns a JSON response suitable for HTMX autocomplete.
        """
        suggestions = []
        for customer in context['object_list']:
            suggestions.append(str(customer)) # Uses __str__ method: "Customer Name [ID]"
        return JsonResponse(suggestions, safe=False)

```

### 4.4 Templates

Templates render the HTML structure for your application. We will create three main templates:
1.  `list.html`: The main page for the Material Credit Note Print module.
2.  `_search_form.html`: A partial template for the search form, including Alpine.js for dynamic input visibility and HTMX for form submission.
3.  `_workorder_table.html`: A partial template for the DataTables table, loaded dynamically via HTMX.

#### `project_management/templates/project_management/workorder/list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4 md:mb-0">Material Credit Note [MCN] - Print</h2>
    </div>

    <!-- Search Form Area -->
    <div class="bg-white p-6 rounded-lg shadow-md mb-8">
        <div id="search-form-container"
             hx-trigger="load"
             hx-get="{% url 'project_management:workorder_search_form' %}"
             hx-swap="innerHTML">
            <!-- Search form will be loaded here via HTMX -->
            <div class="text-center py-4">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading search form...</p>
            </div>
        </div>
    </div>

    <!-- Data Table Area -->
    <div class="bg-white p-6 rounded-lg shadow-md">
        <div id="workorder-table-container"
             hx-trigger="load, refreshWorkOrderList from:body"
             hx-get="{% url 'project_management:workorder_table' %}"
             hx-swap="innerHTML">
            <!-- Work order table will be loaded here via HTMX -->
            <div class="text-center py-4">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading work orders...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component for managing search input visibility
        Alpine.data('searchLogic', () => ({
            selectedField: '1', // Default to 'WO No'
            init() {
                // Set initial value based on form's initial state if available
                const initialSearchField = document.getElementById('id_search_field');
                if (initialSearchField) {
                    this.selectedField = initialSearchField.value;
                }
            },
            updateField(event) {
                this.selectedField = event.target.value;
            },
            showSupplierInput() {
                return this.selectedField === '0';
            },
            showPOInput() {
                return this.selectedField === '1' || this.selectedField === '2';
            }
        }));
    });
</script>
{% endblock %}
```

#### `project_management/templates/project_management/workorder/_search_form.html` (Partial)

```html
<div x-data="searchLogic">
    <form hx-get="{% url 'project_management:workorder_table' %}"
          hx-target="#workorder-table-container"
          hx-swap="innerHTML"
          hx-trigger="submit">
        {% csrf_token %}
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
            <div class="col-span-1">
                <label for="{{ form.search_field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.search_field.label }}
                </label>
                {{ form.search_field }}
            </div>

            <div class="col-span-2">
                <div x-show="showSupplierInput()">
                    <label for="{{ form.search_term_supplier.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.search_term_supplier.label }}
                    </label>
                    {{ form.search_term_supplier }}
                    <!-- Autocomplete suggestions container -->
                    <div id="customer-suggestions" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg mt-1 w-64 max-h-48 overflow-y-auto hidden">
                        <!-- Suggestions will be loaded here by HTMX -->
                    </div>
                </div>

                <div x-show="showPOInput()">
                    <label for="{{ form.search_term_po.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.search_term_po.label }}
                    </label>
                    {{ form.search_term_po }}
                </div>
            </div>

            <div class="col-span-1 flex items-end">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded w-full">
                    Search
                </button>
            </div>
        </div>
        {% if form.errors %}
            <div class="text-red-500 text-xs mt-2">
                {% for field in form %}
                    {% for error in field.errors %}
                        <p>{{ field.label }}: {{ error }}</p>
                    {% endfor %}
                {% endfor %}
                {% for error in form.non_field_errors %}
                    <p>{{ error }}</p>
                {% endfor %}
            </div>
        {% endif %}
    </form>
</div>
```

#### `project_management/templates/project_management/workorder/_workorder_table.html` (Partial)

```html
<div class="overflow-x-auto">
    <table id="workOrderTable" class="min-w-full bg-white table-auto border-collapse border border-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project Title</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
            </tr>
        </thead>
        <tbody>
            {% if work_orders %}
                {% for wo in work_orders %}
                <tr class="{% cycle 'bg-white' 'bg-gray-50' %} hover:bg-gray-100">
                    <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-sm text-blue-600 hover:underline">
                        <!-- This links to a detail page, similar to original LinkButton -->
                        <a href="/project_management/materialcreditnote/details/{{ wo.wo_id }}/?wono={{ wo.work_order_no }}&ModId=7&SubModId=127">
                            {{ wo.work_order_no }}
                        </a>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ wo.date }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ wo.project_title }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ wo.customer_name }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ wo.customer_code }}</td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="6" class="py-4 text-center text-gray-500">No data found to display.</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
    // Ensure jQuery and DataTables scripts are loaded in base.html
    // This script will re-initialize DataTables when the partial is loaded/swapped by HTMX.
    $(document).ready(function() {
        $('#workOrderTable').DataTable({
            "pageLength": 23, // Replicate original PageSize
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "pagingType": "full_numbers"
        });
    });
</script>
```

### 4.5 URLs (`project_management/urls.py`)

This file defines the URL patterns that map incoming web requests to the appropriate Django views.

```python
from django.urls import path
from .views import WorkOrderPrintListView, WorkOrderTablePartialView, CustomerAutocompleteView

app_name = 'project_management' # Namespace for URLs

urlpatterns = [
    # Main page for Material Credit Note Print (initial load of search form and table container)
    path('materialcreditnote/print/', WorkOrderPrintListView.as_view(), name='mcn_print_list'),

    # HTMX endpoint to load just the search form content
    path('materialcreditnote/print/search-form/', WorkOrderPrintListView.as_view(template_name='project_management/workorder/_search_form.html'), name='workorder_search_form'),

    # HTMX endpoint to load just the table content (used for initial load and search submissions)
    path('materialcreditnote/print/table/', WorkOrderTablePartialView.as_view(), name='workorder_table'),

    # HTMX endpoint for customer autocomplete suggestions
    path('materialcreditnote/print/autocomplete-customer/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),

    # Placeholder for the detail page. This URL pattern should be defined in a separate module if it's complex.
    # For now, it's just to make the link in the table work.
    path('materialcreditnote/details/<int:pk>/', WorkOrderPrintListView.as_view(), name='mcn_details_placeholder'), # Placeholder view, replace with actual detail view
]
```

### 4.6 Tests (`project_management/tests.py`)

Thorough testing ensures the reliability and correctness of the migrated application. We include unit tests for models and integration tests for views, aiming for high test coverage.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import WorkOrder, Customer, CURRENT_COMPANY_ID, CURRENT_FINANCIAL_YEAR_ID

class CustomerModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for Customer model
        cls.customer1 = Customer.objects.create(
            customer_id='CUST001',
            customer_name='Acme Corp',
            company_id=CURRENT_COMPANY_ID
        )
        cls.customer2 = Customer.objects.create(
            customer_id='CUST002',
            customer_name='Beta Industries',
            company_id=CURRENT_COMPANY_ID
        )
        cls.customer3 = Customer.objects.create(
            customer_id='CUST003',
            customer_name='Test Customer',
            company_id=999 # Different company
        )

    def test_customer_creation(self):
        self.assertEqual(self.customer1.customer_name, 'Acme Corp')
        self.assertEqual(self.customer1.customer_id, 'CUST001')
        self.assertEqual(str(self.customer1), 'Acme Corp [CUST001]')

    def test_get_customer_id_by_name(self):
        # Test exact name
        self.assertEqual(Customer.objects.get_customer_id_by_name('Acme Corp'), 'CUST001')
        # Test name with ID format
        self.assertEqual(Customer.objects.get_customer_id_by_name('Beta Industries [CUST002]'), 'CUST002')
        # Test non-existent name
        self.assertIsNone(Customer.objects.get_customer_id_by_name('NonExistent Customer'))
        # Test non-existent name with ID format
        self.assertIsNone(Customer.objects.get_customer_id_by_name('NonExistent Customer [NONEX]'))

    def test_search_customers(self):
        # Test search by partial name for current company
        results = Customer.objects.search_customers('acme', CURRENT_COMPANY_ID)
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]['customer_name'], 'Acme Corp')

        # Test search returning multiple results
        results = Customer.objects.search_customers('ind', CURRENT_COMPANY_ID) # No 'ind' in default set. Add another one
        # Let's add another customer for 'ind'
        Customer.objects.create(
            customer_id='CUST004',
            customer_name='Indigo Supplies',
            company_id=CURRENT_COMPANY_ID
        )
        results = Customer.objects.search_customers('ind', CURRENT_COMPANY_ID)
        self.assertEqual(len(results), 2)
        self.assertIn('Beta Industries', [r['customer_name'] for r in results])
        self.assertIn('Indigo Supplies', [r['customer_name'] for r in results])


        # Test search for different company
        results = Customer.objects.search_customers('Test', 999)
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]['customer_name'], 'Test Customer')

        # Test search with no results
        results = Customer.objects.search_customers('xyz', CURRENT_COMPANY_ID)
        self.assertEqual(len(results), 0)

class WorkOrderModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary Customer data first
        cls.customer1 = Customer.objects.create(
            customer_id='CUST001', customer_name='Acme Corp', company_id=CURRENT_COMPANY_ID)
        cls.customer2 = Customer.objects.create(
            customer_id='CUST002', customer_name='Beta Industries', company_id=CURRENT_COMPANY_ID)

        # Create test data for WorkOrder model
        cls.wo1 = WorkOrder.objects.create(
            id=1, work_order_no='WO1001', task_project_title='Project Alpha',
            customer_id='CUST001', system_date=timezone.now(),
            company_id=CURRENT_COMPANY_ID, financial_year_id=CURRENT_FINANCIAL_YEAR_ID,
            close_open='0'
        )
        cls.wo2 = WorkOrder.objects.create(
            id=2, work_order_no='WO1002', task_project_title='Project Beta',
            customer_id='CUST002', system_date=timezone.now(),
            company_id=CURRENT_COMPANY_ID, financial_year_id=CURRENT_FINANCIAL_YEAR_ID,
            close_open='0'
        )
        cls.wo_old_fin_year = WorkOrder.objects.create(
            id=3, work_order_no='WO1003', task_project_title='Project Gamma',
            customer_id='CUST001', system_date=timezone.now(),
            company_id=CURRENT_COMPANY_ID, financial_year_id=CURRENT_FINANCIAL_YEAR_ID - 1, # Older FY
            close_open='0'
        )
        cls.wo_closed = WorkOrder.objects.create(
            id=4, work_order_no='WO1004', task_project_title='Project Delta',
            customer_id='CUST002', system_date=timezone.now(),
            company_id=CURRENT_COMPANY_ID, financial_year_id=CURRENT_FINANCIAL_YEAR_ID,
            close_open='1' # Closed
        )
        cls.wo_other_company = WorkOrder.objects.create(
            id=5, work_order_no='WO2001', task_project_title='Project Epsilon',
            customer_id='CUST001', system_date=timezone.now(),
            company_id=CURRENT_COMPANY_ID + 1, financial_year_id=CURRENT_FINANCIAL_YEAR_ID,
            close_open='0'
        )


    def test_work_order_creation(self):
        self.assertEqual(self.wo1.work_order_no, 'WO1001')
        self.assertEqual(self.wo1.task_project_title, 'Project Alpha')
        self.assertEqual(self.wo1.customer_id, 'CUST001')

    def test_customer_details_property(self):
        customer = self.wo1.customer_details
        self.assertIsNotNone(customer)
        self.assertEqual(customer.customer_name, 'Acme Corp')
        self.assertEqual(customer.customer_id, 'CUST001')

        wo_no_customer = WorkOrder.objects.create(
            id=6, work_order_no='WO9999', task_project_title='No Customer',
            customer_id='NONEXISTENT', system_date=timezone.now(),
            company_id=CURRENT_COMPANY_ID, financial_year_id=CURRENT_FINANCIAL_YEAR_ID,
            close_open='0'
        )
        self.assertIsNone(wo_no_customer.customer_details)

    def test_formatted_system_date_property(self):
        # Format should match what's expected from loaddata's fun.FromDate
        expected_date = timezone.now().strftime('%d/%m/%Y')
        self.assertEqual(self.wo1.formatted_system_date, expected_date)

    def test_get_filtered_work_orders_no_filter(self):
        # Should return all open work orders for the current company and relevant financial years
        results = WorkOrder.objects.get_filtered_work_orders(
            search_field=None, search_term=None,
            company_id=CURRENT_COMPANY_ID, financial_year_id=CURRENT_FINANCIAL_YEAR_ID
        )
        # Should include wo1, wo2, wo_old_fin_year
        self.assertEqual(len(results), 3)
        wo_numbers = [r['work_order_no'] for r in results]
        self.assertIn('WO1001', wo_numbers)
        self.assertIn('WO1002', wo_numbers)
        self.assertIn('WO1003', wo_numbers)
        self.assertNotIn('WO1004', wo_numbers) # Closed
        self.assertNotIn('WO2001', wo_numbers) # Other company

    def test_get_filtered_work_orders_by_wo_no(self):
        results = WorkOrder.objects.get_filtered_work_orders(
            search_field='1', search_term='WO1001',
            company_id=CURRENT_COMPANY_ID, financial_year_id=CURRENT_FINANCIAL_YEAR_ID
        )
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]['work_order_no'], 'WO1001')

    def test_get_filtered_work_orders_by_customer(self):
        results = WorkOrder.objects.get_filtered_work_orders(
            search_field='0', search_term='Acme Corp',
            company_id=CURRENT_COMPANY_ID, financial_year_id=CURRENT_FINANCIAL_YEAR_ID
        )
        # Should include wo1 and wo_old_fin_year
        self.assertEqual(len(results), 2)
        wo_numbers = [r['work_order_no'] for r in results]
        self.assertIn('WO1001', wo_numbers)
        self.assertIn('WO1003', wo_numbers)

        # Test with customer name + ID format
        results_beta = WorkOrder.objects.get_filtered_work_orders(
            search_field='0', search_term='Beta Industries [CUST002]',
            company_id=CURRENT_COMPANY_ID, financial_year_id=CURRENT_FINANCIAL_YEAR_ID
        )
        self.assertEqual(len(results_beta), 1)
        self.assertEqual(results_beta[0]['work_order_no'], 'WO1002')

        # Test with non-existent customer
        results_non_existent = WorkOrder.objects.get_filtered_work_orders(
            search_field='0', search_term='Non Existent',
            company_id=CURRENT_COMPANY_ID, financial_year_id=CURRENT_FINANCIAL_YEAR_ID
        )
        self.assertEqual(len(results_non_existent), 0)

    def test_get_filtered_work_orders_by_project_title(self):
        results = WorkOrder.objects.get_filtered_work_orders(
            search_field='2', search_term='Alpha',
            company_id=CURRENT_COMPANY_ID, financial_year_id=CURRENT_FINANCIAL_YEAR_ID
        )
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]['project_title'], 'Project Alpha')

        results_project = WorkOrder.objects.get_filtered_work_orders(
            search_field='2', search_term='Project',
            company_id=CURRENT_COMPANY_ID, financial_year_id=CURRENT_FINANCIAL_YEAR_ID
        )
        self.assertEqual(len(results_project), 3) # Alpha, Beta, Gamma
        project_titles = [r['project_title'] for r in results_project]
        self.assertIn('Project Alpha', project_titles)
        self.assertIn('Project Beta', project_titles)
        self.assertIn('Project Gamma', project_titles)


class WorkOrderViewsTest(TestCase):
    client = Client()

    @classmethod
    def setUpTestData(cls):
        # Create necessary Customer data first
        cls.customer1 = Customer.objects.create(
            customer_id='CUST001', customer_name='Acme Corp', company_id=CURRENT_COMPANY_ID)
        cls.customer2 = Customer.objects.create(
            customer_id='CUST002', customer_name='Beta Industries', company_id=CURRENT_COMPANY_ID)

        # Create WorkOrder test data
        cls.wo1 = WorkOrder.objects.create(
            id=1, work_order_no='WO1001', task_project_title='Project Alpha',
            customer_id='CUST001', system_date=timezone.now(),
            company_id=CURRENT_COMPANY_ID, financial_year_id=CURRENT_FINANCIAL_YEAR_ID,
            close_open='0'
        )
        cls.wo2 = WorkOrder.objects.create(
            id=2, work_order_no='WO1002', task_project_title='Project Beta',
            customer_id='CUST002', system_date=timezone.now(),
            company_id=CURRENT_COMPANY_ID, financial_year_id=CURRENT_FINANCIAL_YEAR_ID,
            close_open='0'
        )

    def test_mcn_print_list_view(self):
        response = self.client.get(reverse('project_management:mcn_print_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/workorder/list.html')
        self.assertContains(response, 'Material Credit Note [MCN] - Print') # Check page title

    def test_workorder_search_form_partial_view(self):
        response = self.client.get(reverse('project_management:workorder_search_form'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/workorder/_search_form.html')
        self.assertContains(response, '<form hx-get=') # Check HTMX attributes

    def test_workorder_table_partial_view_no_filter(self):
        response = self.client.get(reverse('project_management:workorder_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/workorder/_workorder_table.html')
        self.assertContains(response, 'WO1001')
        self.assertContains(response, 'WO1002')
        self.assertContains(response, 'Acme Corp')
        self.assertContains(response, 'Beta Industries')
        # Check if DataTables script init is present
        self.assertContains(response, "$(document).ready(function() {    $('#workOrderTable').DataTable({")

    def test_workorder_table_partial_view_with_filter_wo_no(self):
        response = self.client.get(reverse('project_management:workorder_table'), {
            'search_field': '1',
            'search_term_po': 'WO1001' # Field used by form
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO1001')
        self.assertNotContains(response, 'WO1002')

    def test_workorder_table_partial_view_with_filter_customer(self):
        response = self.client.get(reverse('project_management:workorder_table'), {
            'search_field': '0',
            'search_term_supplier': 'Acme Corp' # Field used by form
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO1001')
        self.assertNotContains(response, 'WO1002')

    def test_workorder_table_partial_view_with_filter_project_title(self):
        response = self.client.get(reverse('project_management:workorder_table'), {
            'search_field': '2',
            'search_term_po': 'Beta' # Field used by form
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO1002')
        self.assertNotContains(response, 'WO1001')

    def test_customer_autocomplete_view(self):
        response = self.client.get(reverse('project_management:customer_autocomplete'), {'q': 'acme'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = response.json()
        self.assertIsInstance(data, list)
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0], 'Acme Corp [CUST001]')

        response_multiple = self.client.get(reverse('project_management:customer_autocomplete'), {'q': 'industries'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response_multiple.status_code, 200)
        data_multiple = response_multiple.json()
        self.assertIsInstance(data_multiple, list)
        self.assertGreaterEqual(len(data_multiple), 1)
        self.assertIn('Beta Industries [CUST002]', data_multiple)

        response_no_match = self.client.get(reverse('project_management:customer_autocomplete'), {'q': 'xyz'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response_no_match.status_code, 200)
        data_no_match = response_no_match.json()
        self.assertEqual(len(data_no_match), 0)

```

## Step 5: HTMX and Alpine.js Integration

**Instructions Implemented:**

*   **HTMX for dynamic updates:**
    *   The main `list.html` uses `hx-get` on `load` to fetch the search form (`_search_form.html`) and the table content (`_workorder_table.html`) into their respective containers (`#search-form-container`, `#workorder-table-container`).
    *   The search form (`_search_form.html`) uses `hx-get` with `hx-target` and `hx-swap` to submit the search parameters and update only the table content, avoiding full page reloads.
    *   The `hx-trigger="refreshWorkOrderList from:body"` is set on the table container, allowing other parts of the application (e.g., successful form submissions if this were a CRUD page) to trigger a table refresh.
    *   Customer autocomplete uses `hx-get`, `hx-target`, `hx-trigger="keyup changed delay:500ms, search"` to fetch suggestions dynamically.
*   **Alpine.js for UI state management:**
    *   An `Alpine.data('searchLogic', ...)` component is defined in `list.html` to manage the visibility of the `search_term_supplier` and `search_term_po` input fields based on the `search_field` dropdown selection (`x-show` directives).
    *   Alpine.js (combined with HTMX's `_` attribute) is used to control the visibility of the autocomplete suggestions dropdown (`#customer-suggestions`).
*   **DataTables for list views:**
    *   The `_workorder_table.html` partial includes the JavaScript to initialize DataTables on the `workOrderTable` element when the content is loaded by HTMX. This provides client-side sorting, filtering, and pagination.
*   **No custom JavaScript requirements:** All dynamic interactions are handled purely through HTMX attributes and Alpine.js, eliminating the need for traditional jQuery AJAX calls or complex custom JavaScript.
*   **DRY template inheritance:** All templates extend `core/base.html` (as per the strict instruction), ensuring a consistent layout and shared resources (like CDN links for Tailwind CSS, HTMX, Alpine.js, jQuery, and DataTables).

## Final Notes

This comprehensive plan provides a robust, modern Django solution for the Material Credit Note Print module. By following these automated conversion steps and adhering to best practices, your organization can significantly reduce manual effort, improve code quality, and modernize your application infrastructure. The emphasis on 'Fat Models, Thin Views,' HTMX, Alpine.js, and DataTables ensures a highly interactive and efficient user experience with maintainable backend code. The clear, non-technical explanations make this plan accessible to business stakeholders, facilitating a smooth transition process.