This document outlines a comprehensive plan for modernizing your legacy ASP.NET Man Power Planning application to a modern Django-based solution. Our approach prioritizes AI-assisted automation, fat models, thin views, and modern frontend technologies like HTMX and Alpine.js to deliver a robust, maintainable, and scalable system.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
From the provided ASP.NET `.aspx` and C# code-behind, we infer the following database schema. Note that primary keys (`Id`) and foreign key relationships are inferred based on common patterns and how data is joined and filtered. The specific data types are approximated for Django's `models` based on common database usage (e.g., `VARCHAR` to `CharField`, `INT` to `IntegerField`, `DATETIME` to `DateTimeField`).

**Inferred Tables and Columns:**

*   **`BusinessGroup` (from `SqlBGGroup`, `SqlDept`):**
    *   `Id` (PK, Integer)
    *   `Symbol` (String, e.g., 'BG Group Name', 'Department Name')
*   **`tblHR_Designation` (from `tblHR_Designation.Type` in `LoadData`):**
    *   `Id` (PK, Integer)
    *   `Type` (String, e.g., 'Designation Name')
*   **`tblHR_OfficeStaff` (from `tblHR_OfficeStaff` in `LoadData`):**
    *   `EmpId` (PK, Integer) - used as `Id` in `GridView1`
    *   `EmployeeName` (String)
    *   `Designation` (FK to `tblHR_Designation.Id`, Integer)
    *   `BGGroup` (FK to `BusinessGroup.Id`, Integer)
    *   `CompId` (Integer)
    *   `ResignationDate` (Date, possibly nullable)
*   **`tblMIS_BudgetHrs_Field_Category` (from `SqlCategory`):**
    *   `Id` (PK, Integer)
    *   `Category` (String)
*   **`tblPM_ManPowerPlanning` (Master table, from `btnSelect_Click` insert):**
    *   `Id` (PK, Integer)
    *   `SysDate` (Date)
    *   `SysTime` (Time)
    *   `FinYearId` (Integer)
    *   `SessionId` (String)
    *   `CompId` (Integer)
    *   `EmpId` (FK to `tblHR_OfficeStaff.EmpId`, Integer)
    *   `Date` (Date - this is the planning date)
    *   `WONo` (String, Work Order Number, can be empty if `Dept` is used)
    *   `Dept` (FK to `BusinessGroup.Id`, Integer, can be 0 if `WONo` is used)
    *   `Types` (Integer, mapping to status like Present, Absent, etc.)
*   **`tblPM_ManPowerPlanning_Details` (Detail table, from `btnSelect_Click` insert):**
    *   `Id` (PK, Integer) - inferred
    *   `MId` (FK to `tblPM_ManPowerPlanning.Id`, Integer)
    *   `EquipId` (Integer, inferred as FK to an `Equip` table, from `HrsBudgetBOMEquipNo` sproc `ItemCode` and `ItemId`)
    *   `Category` (FK to `tblMIS_BudgetHrs_Field_Category.Id`, Integer)
    *   `SubCategory` (Integer, inferred as FK to a `SubCategory` table or just ID, from `HrsBudgetSubCategory` sproc)
    *   `PlannedDesc` (Text)
    *   `ActualDesc` (Text)
    *   `Hour` (Decimal/Float)
*   **`Equip` (Inferred from `lblEquipId`, `ItemCode` in `GridView3`, `HrsBudgetBOMEquipNo` sproc):**
    *   `Id` (PK, Integer)
    *   `ItemCode` (String, Equip No)
    *   `ManfDesc` (String, Description)
    *   *(Other fields not explicitly shown but typical for equipment)*
*   **`SubCategory` (Inferred from `drpSubCat` and `HrsBudgetSubCategory` sproc):**
    *   `Id` (PK, Integer)
    *   `SubCategory` (String)
    *   `CategoryId` (FK to `tblMIS_BudgetHrs_Field_Category.Id`, Integer)
*   **`tblPM_ManPowerPlanning_Temp`:** This table is for temporary session storage and will not be replicated in Django, as Django's session management and HTMX will handle this data flow.

## Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic in the ASP.NET code.

**Instructions:**
The ASP.NET application provides a complex workflow focused on "Man Power Planning" rather than simple CRUD for a single entity. It's a master-detail entry process.

*   **Read (Display Employee List):**
    *   The `GridView1` populates from `tblHR_OfficeStaff` and `tblHR_Designation`, filtered by `BusinessGroup` (`DrpCategory`).
    *   Pagination is handled (`OnPageIndexChanging`).
*   **Read (Display Equipment List):**
    *   `GridView3` populates dynamically using the `HrsBudgetBOMEquipNo` stored procedure based on the selected employee's WO/Dept details.
*   **Create (Man Power Planning Entry):**
    *   The `btnSelect_Click` event handles the primary "Add" action.
    *   It inserts a new record into `tblPM_ManPowerPlanning` (master).
    *   Then, for each selected row in `GridView3`, it inserts into `tblPM_ManPowerPlanning_Details` (details), linking them via `MId`.
*   **Update/Delete:** No explicit update or delete operations are shown for the `ManPowerPlanning` entries themselves in the provided code. The process appears to be primarily for new entries. We will assume for this conversion that the primary focus is on the "New" aspect as per the page title. If updates/deletes are needed, they would involve retrieving `ManPowerPlanning` records and their details for editing.
*   **Business Logic & Validation:**
    *   **Filtering:** `DrpCategory_SelectedIndexChanged` filters `GridView1`.
    *   **Dynamic UI:** `Drpwodept_SelectedIndexChanged` and `ChkSelect_CheckedChanged` dynamically show/hide fields and enable/disable dropdowns.
    *   **Date Validation:** `fun.DateValidation` is used for `TxtDate`.
    *   **Number Validation:** `fun.NumberValidation` for `TxtHrs`.
    *   **WO No Validation:** `fun.CheckValidWONo` validates Work Order Numbers.
    *   **Budget Calculation & Validation:** The most critical business logic resides in the `Cal_Used_Hours` (`CUH`) class, with methods like `BalanceHours_WONO`, `BalanceHours`, `TotFillPart`, `AllocatedHrs_WONo`, `UtilizeHrs_WONo`. These calculate and validate available hours against planned/actual entries. This logic must be replicated in Django models.
    *   **Flow Control:** The `GridView1_RowCommand` is not a simple "add" but a "select and populate" action, transitioning to the "Details" tab and loading the equipment grid.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The UI is built with ASP.NET Web Forms controls, heavily reliant on server-side postbacks and ViewState.

*   **Master Page:** `MasterPage.master` implies a shared layout. In Django, this maps to `{% extends 'core/base.html' %}`.
*   **Tabbed Interface:** `cc1:TabContainer` will be replaced by HTMX and Alpine.js for dynamic tab switching without full page reloads, using partial template swaps.
*   **Dropdowns:**
    *   `DrpCategory` (`DropDownList`): Selects BG Group to filter employees. HTMX `hx-get` to refresh the employee table.
    *   `Drpwodept` (`DropDownList`): Selects "WO No" or "BG" type for an employee entry. Alpine.js `x-model` or HTMX swaps within each row.
    *   `DrpDepartment` (`DropDownList`): Selects Department if "BG" type is chosen.
    *   `Drptype` (`DropDownList`): Selects "Status" (Present, Absent, etc.).
    *   `drpCat` (`DropDownList`): Selects Category for equipment. HTMX `hx-get` to populate sub-category.
    *   `drpSubCat` (`DropDownList`): Selects Sub-Category.
*   **Text Inputs:**
    *   `TxtDate` (`TextBox` with `CalendarExtender`): For planning date. Will map to `input type="date"` or an Alpine.js-controlled date picker.
    *   `TxtWONo` (`TextBox`): For Work Order Number.
    *   `txtPlanned`, `txtActual`, `txtHrs` (`TextBox`): For equipment planning details and hours.
*   **Data Grids:**
    *   `GridView1` (`asp:GridView`): Displays employee list. Will be replaced by a Django template with `DataTables.js` for client-side functionality and HTMX for server-side filtering.
    *   `GridView3` (`asp:GridView`): Displays associated equipment. Will be a partial template loaded dynamically via HTMX.
*   **Buttons:**
    *   `BtnAdd` (`asp:Button` in `GridView1`): Labeled "Select", used to select an employee and populate the "Details" tab, and load the associated equipment list. This will trigger an HTMX POST request to process the selection.
    *   `btnSelect` (`asp:Button` on "Details" tab): Labeled "Add", this is the final submit button to save the entire Man Power Planning record and its details. Will trigger an HTMX POST request for saving.
*   **Labels:** Many `asp:Label` controls (`lblEmployeeName`, `lblId`, `lblEmpName`, etc.) are used for displaying data. These will map to `{{ object.field_name }}` in Django templates.
*   **Panels:** `asp:Panel` with `ScrollBars="Auto"` indicates scrollable content areas. These will be simple `div` elements with Tailwind CSS `overflow-auto`.
*   **Client-side scripts:** `loadingNotifier.js`, `PopUpMsg.js` for messages, `yui-datatable.css` for grid styling. These will be replaced by HTMX, Alpine.js, `messages` framework in Django, and `DataTables.js`.

## Step 4: Generate Django Code

The Django application will be named `project_management`.

### 4.1 Models

**Task:** Create Django models based on the database schema, incorporating inferred relationships and business logic.

**Instructions:**
The `Cal_Used_Hours` logic is critical. I'll include a `BudgetManager` class and methods on `ManPowerPlanningDetail` to encapsulate budget validation and calculations, adhering to the fat model principle.

```python
# project_management/models.py
from django.db import models, transaction
from django.db.models import Sum
from datetime import date, time

class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=255)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

class Designation(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    type = models.CharField(db_column='Type', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblHR_Designation'
        verbose_name = 'Designation'
        verbose_name_plural = 'Designations'

    def __str__(self):
        return self.type

class OfficeStaff(models.Model):
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    designation = models.ForeignKey(Designation, on_delete=models.DO_NOTHING, db_column='Designation', related_name='staff')
    bg_group = models.ForeignKey(BusinessGroup, on_delete=models.DO_NOTHING, db_column='BGGroup', related_name='staff')
    comp_id = models.IntegerField(db_column='CompId') # Company ID from session
    resignation_date = models.DateField(db_column='ResignationDate', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return self.employee_name

class ManPowerCategory(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    category = models.CharField(db_column='Category', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblMIS_BudgetHrs_Field_Category'
        verbose_name = 'Man Power Category'
        verbose_name_plural = 'Man Power Categories'

    def __str__(self):
        return self.category

class SubCategory(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sub_category = models.CharField(db_column='SubCategory', max_length=255)
    category = models.ForeignKey(ManPowerCategory, on_delete=models.DO_NOTHING, db_column='CategoryId', related_name='subcategories')

    class Meta:
        managed = False
        db_table = 'SubCategory' # Inferred table name
        verbose_name = 'Sub Category'
        verbose_name_plural = 'Sub Categories'

    def __str__(self):
        return self.sub_category

class Equip(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Inferred PK
    item_code = models.CharField(db_column='ItemCode', max_length=255)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255)

    class Meta:
        managed = False
        db_table = 'Equip' # Inferred table name from ItemId
        verbose_name = 'Equipment'
        verbose_name_plural = 'Equipment'

    def __str__(self):
        return f"{self.item_code} - {self.manf_desc}"

    # Replicate HrsBudgetBOMEquipNo logic here if it's based on Equip model.
    # For now, assuming a simple fetch for the list.
    @classmethod
    def get_budget_items_for_wono(cls, comp_id, wo_no):
        """
        Simulates the HrsBudgetBOMEquipNo stored procedure.
        This would typically involve more complex logic or direct DB calls for SPs.
        For demonstration, returning dummy data or actual Equip objects.
        """
        # Example: Fetching Equip items related to a WO (simplified)
        # In a real scenario, this would execute the SP or a complex JOIN query.
        return cls.objects.filter(id__in=[1, 2, 3]) # Placeholder, replace with actual logic

class ManPowerPlanning(models.Model):
    WO_DEPT_CHOICES = [
        (1, 'WO No'),
        (2, 'BG'),
    ]
    STATUS_CHOICES = [
        (1, 'Present'),
        (2, 'Absent'),
        (3, 'Onsite'),
        (4, 'PL'),
    ]

    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate', default=date.today)
    sys_time = models.TimeField(db_column='SysTime', default=time.now)
    fin_year_id = models.IntegerField(db_column='FinYearId') # Financial Year ID from session
    session_id = models.CharField(db_column='SessionId', max_length=255) # User session ID
    comp_id = models.IntegerField(db_column='CompId') # Company ID from session
    employee = models.ForeignKey(OfficeStaff, on_delete=models.DO_NOTHING, db_column='EmpId', related_name='manpower_plans')
    planning_date = models.DateField(db_column='Date')
    wo_no = models.CharField(db_column='WONo', max_length=255, blank=True, null=True)
    department = models.ForeignKey(BusinessGroup, on_delete=models.DO_NOTHING, db_column='Dept', related_name='manpower_plans_by_dept', blank=True, null=True)
    planning_type = models.IntegerField(db_column='Types', choices=STATUS_CHOICES) # Renamed 'Types' to 'planning_type' for clarity

    class Meta:
        managed = False
        db_table = 'tblPM_ManPowerPlanning'
        verbose_name = 'Man Power Planning'
        verbose_name_plural = 'Man Power Planning'

    def __str__(self):
        return f"Plan for {self.employee.employee_name} on {self.planning_date}"

    # Business logic for budget validation (replicated from Cal_Used_Hours)
    @staticmethod
    def _get_allocated_hours(comp_id, wo_no, equip_id, category_id, sub_category_id):
        """Simulates CUH.AllocatedHrs_WONo logic."""
        # This should query a budget allocation table or view.
        # For demonstration, a placeholder.
        return 100.0 # Example value

    @staticmethod
    def _get_utilized_hours(comp_id, wo_no, equip_id, category_id, sub_category_id):
        """Simulates CUH.UtilizeHrs_WONo logic."""
        # This should query ManPowerPlanningDetail for actual utilized hours.
        # Sum of 'Hour' for existing plans.
        # For demonstration, a placeholder.
        return 50.0 # Example value

    def check_valid_wo_no(self, wo_no):
        """Simulates fun.CheckValidWONo - checks if WO No is valid for CompId, FinYearId."""
        # This would typically involve querying a WorkOrder table.
        # For demonstration, a simple check.
        return wo_no.startswith('WO') # Example validation

    def calculate_balance_hours(self, equip_id, category_id, sub_category_id):
        """Calculates balance hours for a specific equipment/category/subcategory under this plan's WO/Dept."""
        allocated_hours = self._get_allocated_hours(self.comp_id, self.wo_no, equip_id, category_id, sub_category_id)
        utilized_hours = self._get_utilized_hours(self.comp_id, self.wo_no, equip_id, category_id, sub_category_id)
        return round(allocated_hours - utilized_hours, 2)

class ManPowerPlanningDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Inferred PK
    manpower_plan = models.ForeignKey(ManPowerPlanning, on_delete=models.CASCADE, db_column='MId', related_name='details')
    equip = models.ForeignKey(Equip, on_delete=models.DO_NOTHING, db_column='EquipId', related_name='manpower_details')
    category = models.ForeignKey(ManPowerCategory, on_delete=models.DO_NOTHING, db_column='Category', related_name='manpower_details')
    sub_category_id = models.IntegerField(db_column='SubCategory') # Assuming int and not a FK to a table as `drpSubCat` is populated based on `drpCat` and `HrsBudgetSubCategory` SP, not a direct FK to SubCategory model here as it's just an Id. If `SubCategory` had a table, it would be a FK.
    planned_desc = models.TextField(db_column='PlannedDesc', blank=True, null=True)
    actual_desc = models.TextField(db_column='ActualDesc', blank=True, null=True)
    hour = models.DecimalField(db_column='Hour', max_digits=10, decimal_places=3)

    class Meta:
        managed = False
        db_table = 'tblPM_ManPowerPlanning_Details'
        verbose_name = 'Man Power Planning Detail'
        verbose_name_plural = 'Man Power Planning Details'

    def __str__(self):
        return f"Detail for {self.manpower_plan.employee.employee_name}: {self.equip.item_code} - {self.hour} hrs"

    # Business logic for detail-level validation
    def validate_hours_against_budget(self, new_hours):
        """
        Validates if the new_hours for this detail can be added without exceeding budget.
        Replicates the logic from `btnSelect_Click` inside `ManPowerPlanningDetail` model.
        """
        # This will use the related ManPowerPlanning instance to get WO/Dept details
        wo_no_or_dept_id = self.manpower_plan.wo_no if self.manpower_plan.wo_no else self.manpower_plan.department_id

        # Calculate balance hours using the parent ManPowerPlanning's method
        balance_hours = self.manpower_plan.calculate_balance_hours(
            self.equip_id, self.category_id, self.sub_category_id
        )

        if not (balance_hours > 0 and balance_hours >= new_hours):
            raise ValueError(f"Only {balance_hours} hours are remaining for this budget item.")
        return True

```

### 4.2 Forms

**Task:** Define Django forms for user input and data validation.

**Instructions:**
We'll create forms for the main `OfficeStaff` filter, and a form for the `ManPowerPlanningDetail` to be used in a formset. The main `ManPowerPlanning` record will be populated from data passed via HTMX from the `OfficeStaff` selection.

```python
# project_management/forms.py
from django import forms
from django.forms import inlineformset_factory
from .models import BusinessGroup, OfficeStaff, ManPowerPlanning, ManPowerPlanningDetail, ManPowerCategory, SubCategory, Equip
import datetime

class OfficeStaffFilterForm(forms.Form):
    bg_group = forms.ModelChoiceField(
        queryset=BusinessGroup.objects.all(),
        empty_label="Select BG Group",
        required=True,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'hx-get': '/manpowerplanning/staff-table/', 'hx-target': '#officestaffTable-container', 'hx-indicator': '#loading-indicator', 'hx-swap': 'innerHTML'})
    )

    class Media:
        # This allows Alpine.js to be used directly in the template if needed
        # and doesn't require a separate JS file for simple dropdowns.
        pass

class ManPowerPlanningDetailForm(forms.ModelForm):
    # These fields are pre-filled or handled by HTMX from the selection
    # They are not direct user input in this form, but rather represent a single row in GridView3
    # Use hidden fields if part of a formset or pass data separately.
    equip_id = forms.IntegerField(widget=forms.HiddenInput())
    equip_item_code = forms.CharField(max_length=255, required=False, widget=forms.TextInput(attrs={'readonly': 'readonly', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-100'}))
    equip_manf_desc = forms.CharField(max_length=255, required=False, widget=forms.TextInput(attrs={'readonly': 'readonly', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-100'}))
    
    # Custom checkbox to enable/disable category/subcategory
    select_item = forms.BooleanField(required=False, widget=forms.CheckboxInput(attrs={
        'class': 'h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded',
        'x-model': 'isSelected', # Alpine.js model for checkbox state
    }))

    category = forms.ModelChoiceField(
        queryset=ManPowerCategory.objects.all(),
        empty_label="Select Category",
        required=False,
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'x-bind:disabled': '!isSelected', # Disable if not selected
            'hx-get': '/manpowerplanning/subcategories/',
            'hx-target': '.subcategory-target', # Target a div/span to swap the next select
            'hx-swap': 'outerHTML', # Swap the whole element (or innerHTML for just options)
            'hx-vals': '{"category_id": event.target.value, "prefix": "{{ form.prefix }}"}', # Pass category_id and form prefix
            'hx-trigger': 'change'
        })
    )
    
    # SubCategory field will be rendered dynamically, so it's not a ModelChoiceField here.
    # It will be a dynamically loaded select box or rendered as a simple IntegerField
    # with a select widget from an HTMX partial.
    sub_category_id = forms.IntegerField(required=False, widget=forms.Select(attrs={
        'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm subcategory-select',
        'x-bind:disabled': '!isSelected', # Disable if not selected
    }))

    planned_desc = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'rows': 2, 'x-bind:disabled': '!isSelected'
        })
    )
    actual_desc = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'rows': 2, 'x-bind:disabled': '!isSelected'
        })
    )
    hour = forms.DecimalField(
        max_digits=10,
        decimal_places=3,
        required=False, # Make required=True after validation
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'x-bind:disabled': '!isSelected'
        })
    )

    class Meta:
        model = ManPowerPlanningDetail
        fields = ['id', 'equip', 'category', 'sub_category_id', 'planned_desc', 'actual_desc', 'hour']
        # 'equip' and 'manpower_plan' will be handled outside the formset or via initial data

    def __init__(self, *args, **kwargs):
        initial_equip = kwargs.pop('initial_equip', None)
        super().__init__(*args, **kwargs)
        if initial_equip:
            self.fields['equip_id'].initial = initial_equip.id
            self.fields['equip_item_code'].initial = initial_equip.item_code
            self.fields['equip_manf_desc'].initial = initial_equip.manf_desc
            self.fields['equip'].initial = initial_equip # Set the actual FK object if needed
            self.fields['equip'].widget = forms.HiddenInput() # Hide the actual FK field

    def clean(self):
        cleaned_data = super().clean()
        select_item = cleaned_data.get('select_item')
        category = cleaned_data.get('category')
        sub_category_id = cleaned_data.get('sub_category_id')
        hour = cleaned_data.get('hour')

        if select_item:
            if not category:
                self.add_error('category', 'This field is required when item is selected.')
            if not sub_category_id:
                self.add_error('sub_category_id', 'This field is required when item is selected.')
            if hour is None:
                self.add_error('hour', 'This field is required when item is selected and must be a number.')
            elif hour <= 0:
                self.add_error('hour', 'Hours must be greater than Zero.')

        return cleaned_data

ManPowerPlanningDetailFormset = inlineformset_factory(
    ManPowerPlanning, # Parent model (ManPowerPlanning)
    ManPowerPlanningDetail, # Child model (ManPowerPlanningDetail)
    form=ManPowerPlanningDetailForm,
    fields=['id', 'equip', 'category', 'sub_category_id', 'planned_desc', 'actual_desc', 'hour'],
    extra=0, # No extra empty forms, they are all pre-populated from initial Equip list
    can_delete=False,
    # This formset will be populated dynamically, so we handle `equip` via `initial_equip` in __init__
)
```

### 4.3 Views

**Task:** Implement CRUD operations using CBVs, focusing on HTMX-driven interactions.

**Instructions:**
Views will be thin, delegating complex logic to models. HTMX will handle partial updates, and Alpine.js will manage local UI state.

```python
# project_management/views.py
from django.views.generic import ListView, TemplateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render, get_object_or_404, redirect
from django.db import transaction

from .models import OfficeStaff, BusinessGroup, ManPowerPlanning, ManPowerPlanningDetail, Equip, ManPowerCategory, SubCategory
from .forms import OfficeStaffFilterForm, ManPowerPlanningDetailFormset, ManPowerPlanningDetailForm

class ManPowerPlanningMainView(TemplateView):
    """
    Main view for Man Power Planning. Displays the initial employee filter and
    containers for the employee list and detail form.
    """
    template_name = 'project_management/manpowerplanning/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['filter_form'] = OfficeStaffFilterForm()
        # Initial empty values for the detail panel labels
        context['selected_employee'] = None
        context['selected_designation'] = None
        context['selected_planning_date'] = None
        context['selected_wo_dept_type'] = None
        context['selected_wo_dept_value'] = None
        context['selected_status'] = None
        return context

class OfficeStaffTablePartialView(ListView):
    """
    Returns the partial HTML for the Office Staff table, filtered by BG Group.
    This is loaded via HTMX when the BG Group dropdown changes.
    """
    model = OfficeStaff
    template_name = 'project_management/manpowerplanning/_officestaff_table.html'
    context_object_name = 'officestaff_list'
    paginate_by = 20 # Matches PageSize="20" from GridView

    def get_queryset(self):
        queryset = super().get_queryset()
        bg_group_id = self.request.GET.get('bg_group')
        comp_id = self.request.session.get('compid', 1) # Get from session, default 1
        # Filter logic from ASP.NET's LoadData:
        # "tblHR_Designation.Id=tblHR_OfficeStaff.Designation And tblHR_OfficeStaff.CompId='" + CompId + "' AND tblHR_OfficeStaff.ResignationDate=''" + y
        queryset = queryset.filter(
            comp_id=comp_id,
            resignation_date__isnull=True # Assumes empty string means no resignation date
        )
        if bg_group_id and bg_group_id != 'Select' and bg_group_id != '0': # '1' was special in ASP.NET, assuming '0' or 'Select' is default/all
            queryset = queryset.filter(bg_group_id=bg_group_id)
        return queryset.select_related('designation', 'bg_group').order_by('employee_name')

class ManPowerPlanningDetailView(View):
    """
    Handles the selection of an employee from the staff list (BtnAdd click)
    and renders the 'Details' tab content with equipment list.
    Also handles the final save action (btnSelect click) for Man Power Planning.
    """
    def post(self, request, *args, **kwargs):
        # This handles the 'select' action from BtnAdd in GridView1
        # It means an employee has been selected, and we need to display the details tab.
        employee_id = request.POST.get('employee_id')
        planning_date_str = request.POST.get('planning_date')
        wo_dept_type = request.POST.get('wo_dept_type') # 1 for WONo, 2 for BG
        wo_no = request.POST.get('wo_no')
        department_id = request.POST.get('department_id')
        status_value = request.POST.get('status_value') # Numeric ID for status
        status_text = request.POST.get('status_text') # Text for status

        try:
            employee = get_object_or_404(OfficeStaff, emp_id=employee_id)
            planning_date = datetime.datetime.strptime(planning_date_str, '%d-%m-%Y').date()
            comp_id = request.session.get('compid', 1)
            fin_year_id = request.session.get('finyear', 2024) # Placeholder

            selected_wo_dept_value = ""
            if wo_dept_type == "1": # WO No
                selected_wo_dept_value = wo_no
                if not ManPowerPlanning().check_valid_wo_no(wo_no):
                    messages.error(request, 'Work Order Number is invalid.')
                    return HttpResponse(status=204, headers={'HX-Trigger': 'errorMessage'}) # Trigger client-side error

            elif wo_dept_type == "2": # BG (Department)
                department = get_object_or_404(BusinessGroup, id=department_id)
                selected_wo_dept_value = department.symbol

            # Prepare initial data for ManPowerPlanning record (not saved yet)
            # This is essentially the state for the 'Details' tab.
            request.session['manpower_plan_temp_data'] = {
                'employee_id': employee.emp_id,
                'planning_date': planning_date.isoformat(),
                'wo_dept_type': int(wo_dept_type),
                'wo_no': wo_no if wo_dept_type == '1' else None,
                'department_id': int(department_id) if wo_dept_type == '2' else None,
                'status_value': int(status_value),
            }

            # Populate GridView3 (Equipment list)
            # This logic simulates FillEquDrp. Equip.get_budget_items_for_wono needs to be robust.
            equipment_list = Equip.get_budget_items_for_wono(comp_id, selected_wo_dept_value if wo_dept_type == "1" else None)

            # Create initial formset for ManPowerPlanningDetailForm
            # Each Equip item gets a form
            initial_forms = []
            for equip in equipment_list:
                initial_forms.append({'equip': equip})

            formset = ManPowerPlanningDetailFormset(prefix='details', initial=[
                {'equip': equip, 'equip_id': equip.id, 'equip_item_code': equip.item_code, 'equip_manf_desc': equip.manf_desc}
                for equip in equipment_list
            ])

            context = {
                'selected_employee': employee,
                'selected_designation': employee.designation.type,
                'selected_planning_date': planning_date.strftime('%d-%m-%Y'),
                'selected_wo_dept_type': 'WO No' if wo_dept_type == '1' else 'BG',
                'selected_wo_dept_value': selected_wo_dept_value,
                'selected_status': status_text,
                'manpower_detail_formset': formset,
            }
            # Return the details tab content. This will replace the content of TabContainer1.
            return render(request, 'project_management/manpowerplanning/_detail_tab_content.html', context)

        except (ValueError, TypeError) as e:
            messages.error(request, f'Invalid input: {e}')
            return HttpResponse(status=204, headers={'HX-Trigger': 'errorMessage'})
        except Exception as e:
            messages.error(request, f'An unexpected error occurred: {e}')
            return HttpResponse(status=204, headers={'HX-Trigger': 'errorMessage'})

    def put(self, request, *args, **kwargs):
        # This handles the final 'Add' (Save) action from btnSelect in the 'Details' tab
        temp_data = request.session.get('manpower_plan_temp_data')
        if not temp_data:
            messages.error(request, 'No employee selected for planning. Please select an employee first.')
            return HttpResponse(status=204, headers={'HX-Trigger': 'errorMessage'})

        # Reconstruct base ManPowerPlanning data
        employee = get_object_or_404(OfficeStaff, emp_id=temp_data['employee_id'])
        planning_date = datetime.date.fromisoformat(temp_data['planning_date'])
        wo_no = temp_data['wo_no']
        department = None
        if temp_data['department_id']:
            department = get_object_or_404(BusinessGroup, id=temp_data['department_id'])

        comp_id = request.session.get('compid', 1)
        fin_year_id = request.session.get('finyear', 2024)
        session_id = request.session.session_key # Django's session ID

        # Manually parse formset data from request.PUT for HTMX
        # HTMX puts form data into request.body if not a file upload
        from urllib.parse import parse_qs
        put_data = parse_qs(request.body.decode('utf-8'))
        
        # Convert parsed data to a mutable QueryDict-like structure
        formset_data = {}
        for key, value_list in put_data.items():
            # For simplicity, if multiple values exist, take the last one, or first.
            # In real formsets, you might need to handle lists more carefully.
            formset_data[key] = value_list[0] if len(value_list) == 1 else value_list

        formset = ManPowerPlanningDetailFormset(data=formset_data, prefix='details')
        
        selected_details_forms = []
        if formset.is_valid():
            # Filter only checked forms
            for form in formset:
                if form.cleaned_data.get('select_item'):
                    selected_details_forms.append(form)

            if not selected_details_forms:
                messages.error(request, "No equipment selected or valid data entered for selected items.")
                return HttpResponse(status=204, headers={'HX-Trigger': 'errorMessage'})
            
            # Additional validation (e.g., hours budget) before saving
            validation_errors = []
            for form in selected_details_forms:
                equip_id = form.cleaned_data['equip_id']
                category = form.cleaned_data['category']
                sub_category_id = form.cleaned_data['sub_category_id']
                hour = form.cleaned_data['hour']

                # Create a temporary ManPowerPlanning instance for validation logic
                temp_manpower_plan = ManPowerPlanning(
                    comp_id=comp_id,
                    wo_no=wo_no,
                    department=department,
                    planning_date=planning_date,
                    employee=employee,
                    planning_type=temp_data['status_value'],
                    fin_year_id=fin_year_id,
                    session_id=session_id
                )
                try:
                    # Validate individual hour entry against budget
                    # Create a dummy detail object to call its validation method
                    dummy_detail = ManPowerPlanningDetail(
                        manpower_plan=temp_manpower_plan,
                        equip_id=equip_id,
                        category=category,
                        sub_category_id=sub_category_id
                    )
                    dummy_detail.validate_hours_against_budget(hour)

                except ValueError as e:
                    validation_errors.append(f"Error for {form.cleaned_data['equip_item_code']}: {e}")
            
            if validation_errors:
                messages.error(request, "Please correct the following issues: " + " ".join(validation_errors))
                # Re-render form with errors if possible, or just trigger an alert
                # For simplicity, we just trigger alert, in real app, might want to re-render the formset
                return HttpResponse(status=204, headers={'HX-Trigger': 'errorMessage'})


            # All validations pass, proceed with saving
            try:
                with transaction.atomic():
                    # Create the master ManPowerPlanning record
                    manpower_plan = ManPowerPlanning.objects.create(
                        sys_date=datetime.date.today(),
                        sys_time=datetime.datetime.now().time(),
                        fin_year_id=fin_year_id,
                        session_id=session_id,
                        comp_id=comp_id,
                        employee=employee,
                        planning_date=planning_date,
                        wo_no=wo_no,
                        department=department,
                        planning_type=temp_data['status_value'],
                    )
                    # Use manpower_plan.id as MId (PK is auto-incremented in Django)
                    # We need to map `manpower_plan.id` to `MId` in the `tblPM_ManPowerPlanning_Details` table.
                    # Since we set `managed = False`, Django won't auto-increment `Id`,
                    # so we need to ensure the `Id` for new `ManPowerPlanning` comes from the DB (after insert)
                    # or is manually set if it's not auto-incremented.
                    # Assuming Id is auto-incremented on the DB side and Django handles it on .create()
                    # For `tblPM_ManPowerPlanning_Details`, we insert `MId` which is the PK of `manpower_plan`.

                    for form in selected_details_forms:
                        ManPowerPlanningDetail.objects.create(
                            manpower_plan=manpower_plan, # Link to the newly created master
                            equip_id=form.cleaned_data['equip_id'],
                            category=form.cleaned_data['category'],
                            sub_category_id=form.cleaned_data['sub_category_id'],
                            planned_desc=form.cleaned_data['planned_desc'],
                            actual_desc=form.cleaned_data['actual_desc'],
                            hour=form.cleaned_data['hour']
                        )

                messages.success(request, 'Man Power Planning entry added successfully.')
                # Clear session data for next entry
                if 'manpower_plan_temp_data' in request.session:
                    del request.session['manpower_plan_temp_data']
                
                # Signal client to refresh the page or redirect
                return HttpResponse(status=204, headers={'HX-Trigger': 'refreshPage'})

            except Exception as e:
                messages.error(request, f'Failed to save entry: {e}')
                return HttpResponse(status=204, headers={'HX-Trigger': 'errorMessage'})
        else:
            # Formset is invalid, re-render with errors
            messages.error(request, 'Please correct the errors in the form.')
            context = {
                'selected_employee': employee,
                'selected_designation': employee.designation.type,
                'selected_planning_date': planning_date.strftime('%d-%m-%Y'),
                'selected_wo_dept_type': 'WO No' if temp_data['wo_dept_type'] == 1 else 'BG',
                'selected_wo_dept_value': wo_no if temp_data['wo_dept_type'] == 1 else department.symbol,
                'selected_status': [text for val, text in ManPowerPlanning.STATUS_CHOICES if val == temp_data['status_value']][0],
                'manpower_detail_formset': formset, # Pass back the formset with errors
            }
            return render(request, 'project_management/manpowerplanning/_detail_tab_content.html', context)


class SubCategoryDropdownPartialView(View):
    """
    Returns the <select> options for subcategories based on the selected category.
    """
    def get(self, request, *args, **kwargs):
        category_id = request.GET.get('category_id')
        prefix = request.GET.get('prefix', '') # Get formset prefix
        
        if not category_id or category_id == '1': # '1' is "Select Category" in ASP.NET
            subcategories = SubCategory.objects.none()
        else:
            subcategories = SubCategory.objects.filter(category_id=category_id)
        
        context = {
            'subcategories': subcategories,
            'prefix': prefix,
        }
        return render(request, 'project_management/manpowerplanning/_subcategory_options.html', context)


```

### 4.4 Templates

**Task:** Create templates for each view, leveraging HTMX, Alpine.js, and DataTables.

**Instructions:**
Base template (`core/base.html`) is assumed to exist with CDN links for jQuery, DataTables, HTMX, Alpine.js, and Tailwind CSS.

```html
{# project_management/templates/project_management/manpowerplanning/list.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Man Power Planning - New</h2>
    </div>
    
    <div x-data="{ activeTab: 'planning' }">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <button 
                    @click="activeTab = 'planning'" 
                    :class="activeTab === 'planning' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                    class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none"
                    hx-get="{% url 'manpowerplanning_main' %}" {# Refresh planning tab if needed #}
                    hx-target="#planningTabContent"
                    hx-swap="innerHTML"
                    hx-trigger="click"
                    >
                    Planning
                </button>
                <button 
                    @click="activeTab = 'details'" 
                    :class="activeTab === 'details' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                    class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none"
                    >
                    Details
                </button>
            </nav>
        </div>

        {# Planning Tab Content #}
        <div id="planningTabContent" x-show="activeTab === 'planning'" class="p-4 bg-white shadow-sm rounded-b-lg">
            <div class="mb-4">
                <label for="{{ filter_form.bg_group.id_for_label }}" class="block text-sm font-medium text-gray-700 font-bold">Select BG Group:</label>
                {{ filter_form.bg_group }}
                {% if filter_form.bg_group.errors %}
                <p class="text-red-500 text-xs mt-1">{{ filter_form.bg_group.errors }}</p>
                {% endif %}
            </div>

            <div id="officestaffTable-container"
                 hx-trigger="load, refreshManPowerPlanningList from:body"
                 hx-get="{% url 'officestaff_table' %}"
                 hx-target="this"
                 hx-indicator="#loading-indicator"
                 hx-swap="innerHTML">
                {# Initial loading state #}
                <div id="loading-indicator" class="flex items-center justify-center space-x-2 p-8" hx-indicator="on" style="display:none;">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading Employees...</p>
                </div>
                {# DataTable will be loaded here via HTMX #}
            </div>
        </div>

        {# Details Tab Content #}
        <div id="detailsTabContent" x-show="activeTab === 'details'" class="p-4 bg-white shadow-sm rounded-b-lg">
            {% include 'project_management/manpowerplanning/_detail_tab_content.html' with selected_employee=selected_employee selected_designation=selected_designation selected_planning_date=selected_planning_date selected_wo_dept_type=selected_wo_dept_type selected_wo_dept_value=selected_wo_dept_value selected_status=selected_status manpower_detail_formset=None %}
        </div>
    </div>
</div>

<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('manPowerPlanningApp', () => ({
            activeTab: 'planning',
            showModal: false,
            // Function to handle global HTMX triggers
            init() {
                document.body.addEventListener('refreshManPowerPlanningList', () => {
                    this.activeTab = 'planning'; // Ensure planning tab is active on list refresh
                });
                document.body.addEventListener('errorMessage', (event) => {
                    // Assuming Django messages are handled by a global script in base.html
                    // Or you can display a specific modal here
                    console.error('Error triggered from server');
                });
                document.body.addEventListener('successMessage', (event) => {
                    // Assuming Django messages are handled by a global script in base.html
                    console.log('Success triggered from server');
                });
                document.body.addEventListener('refreshPage', () => {
                    window.location.reload(); // Full page reload as in ASP.NET
                });
            }
        }));
    });
</script>
{% endblock %}
```

```html
{# project_management/templates/project_management/manpowerplanning/_officestaff_table.html #}
<div class="overflow-x-auto rounded-md shadow-sm border border-gray-200">
    <table id="officestaffTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Designation</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No/Dept</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th scope="col" class="relative px-6 py-3"><span class="sr-only">Actions</span></th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for staff in officestaff_list %}
            <tr x-data="{ woDeptType: '1', dateValue: '' }"> {# Alpine.js for per-row state #}
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ staff.employee_name }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ staff.designation.type }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <input type="date" x-model="dateValue" 
                           class="block w-[120px] px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                           required>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <select x-model="woDeptType" class="block w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        <option value="1">WONo</option>
                        <option value="2">BG</option>
                    </select>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div x-show="woDeptType === '1'">
                        <input type="text" placeholder="WO No" class="block w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>
                    <div x-show="woDeptType === '2'">
                        <select class="block w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            {% for dept in businessgroup_list %}
                            <option value="{{ dept.id }}">{{ dept.symbol }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <select class="block w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        {% for value, text in staff.STATUS_CHOICES %} {# Using model choices #}
                        <option value="{{ value }}">{{ text }}</option>
                        {% endfor %}
                    </select>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                        hx-post="{% url 'manpowerplanning_detail_view' %}"
                        hx-target="#detailsTabContent" {# Target the details tab content #}
                        hx-swap="innerHTML"
                        hx-include="closest('tr')" {# Include all form fields in the current row #}
                        hx-vals='{
                            "employee_id": "{{ staff.emp_id }}",
                            "planning_date": dateValue,
                            "wo_dept_type": woDeptType,
                            "wo_no": woDeptType === "1" ? $el.closest("tr").querySelector("input[type=text]").value : "",
                            "department_id": woDeptType === "2" ? $el.closest("tr").querySelector("select:not([x-model])").value : "",
                            "status_value": $el.closest("tr").querySelector("select:last-of-type").value,
                            "status_text": $el.closest("tr").querySelector("select:last-of-type").options[$el.closest("tr").querySelector("select:last-of-type").selectedIndex].text
                        }'
                        _="on htmx:afterRequest set activeTab to 'details' in manPowerPlanningApp"
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-3 rounded text-xs"
                    >
                        Select
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="px-6 py-4 whitespace-nowrap text-center text-sm text-red-500">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        // Initialize DataTables if it hasn't been initialized
        if (!$.fn.DataTable.isDataTable('#officestaffTable')) {
            $('#officestaffTable').DataTable({
                "pageLength": 10, // Default page length
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "paging": true,
                "searching": true,
                "ordering": true,
                "info": true
            });
        }
    });
</script>
```

```html
{# project_management/templates/project_management/manpowerplanning/_detail_tab_content.html #}
<div class="px-4 py-2">
    <div class="bg-gray-50 p-4 rounded-md shadow-sm mb-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Selected Employee Details</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 font-bold">Name of Employee:</label>
                <p class="mt-1 text-sm text-gray-900">{{ selected_employee.employee_name|default:"N/A" }}</p>
                <input type="hidden" name="employee_id" value="{{ selected_employee.emp_id|default:"" }}">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 font-bold">Designation:</label>
                <p class="mt-1 text-sm text-gray-900">{{ selected_designation|default:"N/A" }}</p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 font-bold">Date:</label>
                <p class="mt-1 text-sm text-gray-900">{{ selected_planning_date|default:"N/A" }}</p>
                <input type="hidden" name="planning_date" value="{{ selected_planning_date|default:"" }}">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 font-bold">{{ selected_wo_dept_type|default:"WO No/Dept" }}:</label>
                <p class="mt-1 text-sm text-gray-900">{{ selected_wo_dept_value|default:"N/A" }}</p>
                <input type="hidden" name="wo_dept_type" value="{{ selected_wo_dept_type|default:"" }}">
                <input type="hidden" name="wo_no" value="{{ selected_wo_dept_type == 'WO No' and selected_wo_dept_value|default:'' or '' }}">
                <input type="hidden" name="department_id" value="{{ selected_wo_dept_type == 'BG' and selected_employee.bg_group.id|default:'' or '' }}">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 font-bold">Status:</label>
                <p class="mt-1 text-sm text-gray-900">{{ selected_status|default:"N/A" }}</p>
                <input type="hidden" name="status_value" value="{{ selected_status_id|default:"" }}"> {# Pass status ID here #}
            </div>
        </div>
    </div>

    {% if manpower_detail_formset %}
    <form hx-put="{% url 'manpowerplanning_detail_view' %}" hx-target="#detailsTabContent" hx-swap="innerHTML" hx-indicator="#saving-indicator">
        {% csrf_token %}
        
        {{ manpower_detail_formset.management_form }}
        
        <div class="overflow-x-auto rounded-md shadow-sm border border-gray-200 mb-6">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="p-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
                        <th scope="col" class="p-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Equip No</th>
                        <th scope="col" class="p-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Desc</th>
                        <th scope="col" class="p-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                        <th scope="col" class="p-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sub-Category</th>
                        <th scope="col" class="p-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Planned</th>
                        <th scope="col" class="p-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actual</th>
                        <th scope="col" class="p-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hrs</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for form in manpower_detail_formset %}
                    <tr x-data="{ isSelected: {% if form.instance.pk %}true{% else %}false{% endif %} }">
                        <td class="p-3 whitespace-nowrap text-center text-sm font-medium">
                            {{ form.select_item }}
                            {% if form.select_item.errors %}<p class="text-red-500 text-xs mt-1">{{ form.select_item.errors }}</p>{% endif %}
                        </td>
                        <td class="p-3 whitespace-nowrap text-sm text-gray-900">
                            {{ form.equip_item_code }}
                            {{ form.equip_id }}
                            {{ form.equip }} {# The actual hidden FK field #}
                        </td>
                        <td class="p-3 whitespace-nowrap text-sm text-gray-900">{{ form.equip_manf_desc }}</td>
                        <td class="p-3 whitespace-nowrap text-sm text-gray-900">
                            {{ form.category }}
                            {% if form.category.errors %}<p class="text-red-500 text-xs mt-1">{{ form.category.errors }}</p>{% endif %}
                        </td>
                        <td class="p-3 whitespace-nowrap text-sm text-gray-900">
                            <span class="subcategory-target">
                                {# This will be swapped by HTMX to update the subcategory select #}
                                <select x-bind:disabled="!isSelected"
                                        class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm subcategory-select"
                                        name="{{ form.prefix }}-sub_category_id" id="{{ form.prefix }}-sub_category_id">
                                    <option value="">Select Sub-Category</option>
                                    {# Pre-populate if editing or previously selected #}
                                    {% if form.cleaned_data.sub_category_id %}
                                        {% for subcat in form.category.field.queryset.get(id=form.cleaned_data.category.id).subcategories.all %}
                                            <option value="{{ subcat.id }}" {% if subcat.id == form.cleaned_data.sub_category_id %}selected{% endif %}>{{ subcat.sub_category }}</option>
                                        {% endfor %}
                                    {% endif %}
                                </select>
                            </span>
                            {% if form.sub_category_id.errors %}<p class="text-red-500 text-xs mt-1">{{ form.sub_category_id.errors }}</p>{% endif %}
                        </td>
                        <td class="p-3 whitespace-nowrap text-sm text-gray-900">
                            {{ form.planned_desc }}
                            {% if form.planned_desc.errors %}<p class="text-red-500 text-xs mt-1">{{ form.planned_desc.errors }}</p>{% endif %}
                        </td>
                        <td class="p-3 whitespace-nowrap text-sm text-gray-900">
                            {{ form.actual_desc }}
                            {% if form.actual_desc.errors %}<p class="text-red-500 text-xs mt-1">{{ form.actual_desc.errors }}</p>{% endif %}
                        </td>
                        <td class="p-3 whitespace-nowrap text-sm text-gray-900">
                            {{ form.hour }}
                            {% if form.hour.errors %}<p class="text-red-500 text-xs mt-1">{{ form.hour.errors }}</p>{% endif %}
                        </td>
                        {# Render non-field errors for the form #}
                        {% if form.non_field_errors %}
                        <td colspan="8" class="text-red-500 text-xs mt-1">{{ form.non_field_errors }}</td>
                        {% endif %}
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <div class="flex justify-end mt-4">
            <button type="submit" 
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            >
                Add Man Power Planning
            </button>
            <div id="saving-indicator" class="flex items-center justify-center space-x-2 ml-4" hx-indicator="on" style="display:none;">
                <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
                <p class="text-gray-600 text-sm">Saving...</p>
            </div>
        </div>
    </form>
    {% else %}
    <div class="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded relative" role="alert">
        <strong class="font-bold">Info!</strong>
        <span class="block sm:inline">Please select an employee from the "Planning" tab to proceed with Man Power Planning.</span>
    </div>
    {% endif %}
</div>
```

```html
{# project_management/templates/project_management/manpowerplanning/_subcategory_options.html #}
<select x-bind:disabled="!isSelected"
        class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm subcategory-select"
        name="{{ prefix }}-sub_category_id" id="{{ prefix }}-sub_category_id">
    <option value="">Select Sub-Category</option>
    {% for subcat in subcategories %}
        <option value="{{ subcat.id }}">{{ subcat.sub_category }}</option>
    {% endfor %}
</select>
```

### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
The URL structure will reflect the HTMX-driven partial loading.

```python
# project_management/urls.py
from django.urls import path
from .views import (
    ManPowerPlanningMainView,
    OfficeStaffTablePartialView,
    ManPowerPlanningDetailView,
    SubCategoryDropdownPartialView
)

urlpatterns = [
    path('manpowerplanning/', ManPowerPlanningMainView.as_view(), name='manpowerplanning_main'),
    path('manpowerplanning/staff-table/', OfficeStaffTablePartialView.as_view(), name='officestaff_table'),
    path('manpowerplanning/select-staff/', ManPowerPlanningDetailView.as_view(), name='manpowerplanning_detail_view'),
    path('manpowerplanning/subcategories/', SubCategoryDropdownPartialView.as_view(), name='subcategory_dropdown'),
]

```

### 4.6 Tests

**Task:** Write comprehensive unit tests for models and integration tests for views.

**Instructions:**
We'll include tests for model methods (especially the budget logic) and view interactions.

```python
# project_management/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
import datetime
from decimal import Decimal

from .models import (
    BusinessGroup,
    Designation,
    OfficeStaff,
    ManPowerCategory,
    SubCategory,
    Equip,
    ManPowerPlanning,
    ManPowerPlanningDetail,
)

class ManPowerPlanningModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create common test data
        cls.bg_group_a = BusinessGroup.objects.create(id=1, symbol='BG-A')
        cls.bg_group_b = BusinessGroup.objects.create(id=2, symbol='BG-B')
        cls.designation_eng = Designation.objects.create(id=1, type='Engineer')
        cls.designation_tech = Designation.objects.create(id=2, type='Technician')

        cls.staff1 = OfficeStaff.objects.create(
            emp_id=101,
            employee_name='Alice Smith',
            designation=cls.designation_eng,
            bg_group=cls.bg_group_a,
            comp_id=1,
            resignation_date=None
        )
        cls.staff2 = OfficeStaff.objects.create(
            emp_id=102,
            employee_name='Bob Johnson',
            designation=cls.designation_tech,
            bg_group=cls.bg_group_b,
            comp_id=1,
            resignation_date=None
        )

        cls.category_a = ManPowerCategory.objects.create(id=1, category='Electrical')
        cls.category_b = ManPowerCategory.objects.create(id=2, category='Mechanical')
        cls.sub_category_a1 = SubCategory.objects.create(id=1, sub_category='Wiring', category=cls.category_a)
        cls.sub_category_a2 = SubCategory.objects.create(id=2, sub_category='Circuitry', category=cls.category_a)

        cls.equip1 = Equip.objects.create(id=1, item_code='EQP001', manf_desc='Drill Machine')
        cls.equip2 = Equip.objects.create(id=2, item_code='EQP002', manf_desc='Welding Tool')

    def setUp(self):
        # Clear any existing ManPowerPlanning and Details records for each test
        ManPowerPlanning.objects.all().delete()
        ManPowerPlanningDetail.objects.all().delete()

    def test_business_group_creation(self):
        bg = BusinessGroup.objects.get(id=1)
        self.assertEqual(bg.symbol, 'BG-A')

    def test_office_staff_relationships(self):
        staff = OfficeStaff.objects.get(emp_id=101)
        self.assertEqual(staff.designation.type, 'Engineer')
        self.assertEqual(staff.bg_group.symbol, 'BG-A')

    def test_man_power_planning_creation(self):
        plan = ManPowerPlanning.objects.create(
            id=1, # Manually set ID for unmanaged model testing
            sys_date=datetime.date.today(),
            sys_time=datetime.datetime.now().time(),
            fin_year_id=2024,
            session_id='testsession',
            comp_id=1,
            employee=self.staff1,
            planning_date=datetime.date(2024, 7, 1),
            wo_no='WO-ABC-001',
            department=None,
            planning_type=ManPowerPlanning.STATUS_CHOICES[0][0] # Present
        )
        self.assertEqual(plan.employee.employee_name, 'Alice Smith')
        self.assertEqual(plan.wo_no, 'WO-ABC-001')

    def test_man_power_planning_detail_creation(self):
        plan = ManPowerPlanning.objects.create(
            id=2, # Manually set ID
            sys_date=datetime.date.today(),
            sys_time=datetime.datetime.now().time(),
            fin_year_id=2024,
            session_id='testsession',
            comp_id=1,
            employee=self.staff1,
            planning_date=datetime.date(2024, 7, 1),
            wo_no='WO-ABC-001',
            department=None,
            planning_type=ManPowerPlanning.STATUS_CHOICES[0][0]
        )
        detail = ManPowerPlanningDetail.objects.create(
            id=1, # Manually set ID
            manpower_plan=plan,
            equip=self.equip1,
            category=self.category_a,
            sub_category_id=self.sub_category_a1.id,
            planned_desc='Install wiring',
            actual_desc='Completed wiring',
            hour=Decimal('8.5')
        )
        self.assertEqual(detail.manpower_plan.employee.employee_name, 'Alice Smith')
        self.assertEqual(detail.equip.item_code, 'EQP001')
        self.assertEqual(detail.hour, Decimal('8.5'))

    def test_check_valid_wo_no(self):
        plan = ManPowerPlanning() # Dummy instance for method call
        self.assertTrue(plan.check_valid_wo_no('WO-123'))
        self.assertFalse(plan.check_valid_wo_no('XYZ-456'))

    def test_calculate_balance_hours(self):
        # Mock the static methods for predictable testing
        ManPowerPlanning._get_allocated_hours = lambda comp_id, wo_no, equip_id, category_id, sub_category_id: 50.0
        ManPowerPlanning._get_utilized_hours = lambda comp_id, wo_no, equip_id, category_id, sub_category_id: 20.0

        plan = ManPowerPlanning(comp_id=1, wo_no='WO-TEST')
        balance = plan.calculate_balance_hours(1, 1, 1)
        self.assertEqual(balance, 30.0)

    def test_validate_hours_against_budget_success(self):
        # Mock methods for success scenario
        ManPowerPlanning._get_allocated_hours = lambda comp_id, wo_no, equip_id, category_id, sub_category_id: 50.0
        ManPowerPlanning._get_utilized_hours = lambda comp_id, wo_no, equip_id, category_id, sub_category_id: 20.0

        plan = ManPowerPlanning.objects.create(
            id=3, # Manually set ID
            sys_date=datetime.date.today(),
            sys_time=datetime.datetime.now().time(),
            fin_year_id=2024,
            session_id='testsession',
            comp_id=1,
            employee=self.staff1,
            planning_date=datetime.date(2024, 7, 1),
            wo_no='WO-ABC-001',
            department=None,
            planning_type=ManPowerPlanning.STATUS_CHOICES[0][0]
        )
        detail = ManPowerPlanningDetail(
            manpower_plan=plan,
            equip=self.equip1,
            category=self.category_a,
            sub_category_id=self.sub_category_a1.id,
        )
        self.assertTrue(detail.validate_hours_against_budget(Decimal('25.0')))

    def test_validate_hours_against_budget_failure(self):
        # Mock methods for failure scenario
        ManPowerPlanning._get_allocated_hours = lambda comp_id, wo_no, equip_id, category_id, sub_category_id: 50.0
        ManPowerPlanning._get_utilized_hours = lambda comp_id, wo_no, equip_id, category_id, sub_category_id: 40.0 # Only 10 left

        plan = ManPowerPlanning.objects.create(
            id=4, # Manually set ID
            sys_date=datetime.date.today(),
            sys_time=datetime.datetime.now().time(),
            fin_year_id=2024,
            session_id='testsession',
            comp_id=1,
            employee=self.staff1,
            planning_date=datetime.date(2024, 7, 1),
            wo_no='WO-ABC-001',
            department=None,
            planning_type=ManPowerPlanning.STATUS_CHOICES[0][0]
        )
        detail = ManPowerPlanningDetail(
            manpower_plan=plan,
            equip=self.equip1,
            category=self.category_a,
            sub_category_id=self.sub_category_a1.id,
        )
        with self.assertRaisesRegex(ValueError, "Only 10.0 hours are remaining"):
            detail.validate_hours_against_budget(Decimal('15.0'))


class ManPowerPlanningViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Setup common data for views tests
        cls.bg_group_a = BusinessGroup.objects.create(id=1, symbol='BG-A')
        cls.bg_group_b = BusinessGroup.objects.create(id=2, symbol='BG-B')
        cls.designation_eng = Designation.objects.create(id=1, type='Engineer')
        cls.designation_tech = Designation.objects.create(id=2, type='Technician')

        cls.staff1 = OfficeStaff.objects.create(
            emp_id=101, employee_name='Alice Smith', designation=cls.designation_eng,
            bg_group=cls.bg_group_a, comp_id=1, resignation_date=None
        )
        cls.staff2 = OfficeStaff.objects.create(
            emp_id=102, employee_name='Bob Johnson', designation=cls.designation_tech,
            bg_group=cls.bg_group_b, comp_id=1, resignation_date=None
        )
        cls.category_a = ManPowerCategory.objects.create(id=1, category='Electrical')
        cls.sub_category_a1 = SubCategory.objects.create(id=1, sub_category='Wiring', category=cls.category_a)
        cls.equip1 = Equip.objects.create(id=1, item_code='EQP001', manf_desc='Drill Machine')
        cls.equip2 = Equip.objects.create(id=2, item_code='EQP002', manf_desc='Welding Tool')

    def setUp(self):
        self.client = Client()
        # Set session data
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2024
        session.save()
        
        # Mock Equip.get_budget_items_for_wono for predictable equipment lists
        Equip.get_budget_items_for_wono = lambda comp_id, wo_no: [self.equip1, self.equip2]

    def test_main_view_get(self):
        response = self.client.get(reverse('manpowerplanning_main'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/manpowerplanning/list.html')
        self.assertContains(response, 'Select BG Group')

    def test_office_staff_table_partial_view(self):
        response = self.client.get(reverse('officestaff_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/manpowerplanning/_officestaff_table.html')
        self.assertContains(response, self.staff1.employee_name)
        self.assertContains(response, self.staff2.employee_name)

    def test_office_staff_table_partial_view_filtered(self):
        response = self.client.get(reverse('officestaff_table') + f'?bg_group={self.bg_group_a.id}', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.staff1.employee_name)
        self.assertNotContains(response, self.staff2.employee_name)

    def test_manpower_planning_detail_view_select_employee(self):
        # Simulate HTMX POST for selecting an employee
        data = {
            'employee_id': self.staff1.emp_id,
            'planning_date': '01-07-2024',
            'wo_dept_type': '1', # WO No
            'wo_no': 'WO-TEST-001',
            'department_id': '', # Not used for WO No type
            'status_value': '1', # Present
            'status_text': 'Present'
        }
        response = self.client.post(reverse('manpowerplanning_detail_view'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/manpowerplanning/_detail_tab_content.html')
        self.assertContains(response, self.staff1.employee_name)
        self.assertContains(response, 'WO-TEST-001')
        self.assertContains(response, self.equip1.item_code) # Check if equipment list is loaded

    def test_manpower_planning_detail_view_save_valid(self):
        # First, simulate selecting an employee to populate session data
        select_data = {
            'employee_id': self.staff1.emp_id,
            'planning_date': '01-07-2024',
            'wo_dept_type': '1',
            'wo_no': 'WO-TEST-001',
            'department_id': '',
            'status_value': '1',
            'status_text': 'Present'
        }
        self.client.post(reverse('manpowerplanning_detail_view'), select_data, HTTP_HX_REQUEST='true')

        # Mock budget validation for success
        ManPowerPlanning._get_allocated_hours = lambda comp_id, wo_no, equip_id, category_id, sub_category_id: 50.0
        ManPowerPlanning._get_utilized_hours = lambda comp_id, wo_no, equip_id, category_id, sub_category_id: 10.0

        # Simulate HTMX PUT for saving the formset
        save_data = {
            'details-TOTAL_FORMS': '2',
            'details-INITIAL_FORMS': '2',
            'details-MIN_NUM_FORMS': '0',
            'details-MAX_NUM_FORMS': '1000',

            'details-0-id': '', # No ID for new detail
            'details-0-select_item': 'on',
            'details-0-equip_id': self.equip1.id,
            'details-0-category': self.category_a.id,
            'details-0-sub_category_id': self.sub_category_a1.id,
            'details-0-planned_desc': 'Planned for EQP001',
            'details-0-actual_desc': 'Actual for EQP001',
            'details-0-hour': '8.0',

            'details-1-id': '',
            'details-1-select_item': '', # Not selected
            'details-1-equip_id': self.equip2.id,
            'details-1-category': '',
            'details-1-sub_category_id': '',
            'details-1-planned_desc': '',
            'details-1-actual_desc': '',
            'details-1-hour': '',
        }
        response = self.client.put(reverse('manpowerplanning_detail_view'), save_data, content_type='application/x-www-form-urlencoded', HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HTMX 204 No Content expected on success trigger
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshPage', response.headers['HX-Trigger'])
        self.assertEqual(ManPowerPlanning.objects.count(), 1)
        self.assertEqual(ManPowerPlanningDetail.objects.count(), 1) # Only one selected detail should be saved

        messages_list = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages_list), 1)
        self.assertEqual(str(messages_list[0]), 'Man Power Planning entry added successfully.')

    def test_manpower_planning_detail_view_save_invalid_hours(self):
        # First, simulate selecting an employee to populate session data
        select_data = {
            'employee_id': self.staff1.emp_id,
            'planning_date': '01-07-2024',
            'wo_dept_type': '1',
            'wo_no': 'WO-TEST-001',
            'department_id': '',
            'status_value': '1',
            'status_text': 'Present'
        }
        self.client.post(reverse('manpowerplanning_detail_view'), select_data, HTTP_HX_REQUEST='true')

        # Mock budget validation for failure (not enough hours)
        ManPowerPlanning._get_allocated_hours = lambda comp_id, wo_no, equip_id, category_id, sub_category_id: 10.0
        ManPowerPlanning._get_utilized_hours = lambda comp_id, wo_no, equip_id, category_id, sub_category_id: 5.0 # Only 5 left

        # Simulate HTMX PUT for saving the formset with too many hours
        save_data = {
            'details-TOTAL_FORMS': '1',
            'details-INITIAL_FORMS': '1',
            'details-MIN_NUM_FORMS': '0',
            'details-MAX_NUM_FORMS': '1000',

            'details-0-id': '',
            'details-0-select_item': 'on',
            'details-0-equip_id': self.equip1.id,
            'details-0-category': self.category_a.id,
            'details-0-sub_category_id': self.sub_category_a1.id,
            'details-0-planned_desc': 'Planned for EQP001',
            'details-0-actual_desc': 'Actual for EQP001',
            'details-0-hour': '8.0', # Requesting 8, but only 5 available
        }
        response = self.client.put(reverse('manpowerplanning_detail_view'), save_data, content_type='application/x-www-form-urlencoded', HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('errorMessage', response.headers['HX-Trigger'])
        self.assertEqual(ManPowerPlanning.objects.count(), 0) # No record should be saved
        self.assertEqual(ManPowerPlanningDetail.objects.count(), 0)

        messages_list = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages_list), 1)
        self.assertIn("Error for EQP001: Only 5.0 hours are remaining", str(messages_list[0]))

    def test_subcategory_dropdown_partial_view(self):
        response = self.client.get(reverse('subcategory_dropdown') + f'?category_id={self.category_a.id}&prefix=details-0', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/manpowerplanning/_subcategory_options.html')
        self.assertContains(response, self.sub_category_a1.sub_category)
        self.assertContains(response, self.sub_category_a2.sub_category)
        self.assertContains(response, 'name="details-0-sub_category_id"')

    def test_subcategory_dropdown_partial_view_no_category(self):
        response = self.client.get(reverse('subcategory_dropdown') + f'?category_id=1&prefix=details-0', HTTP_HX_REQUEST='true') # Category '1' was special in ASP.NET for "Select"
        self.assertEqual(response.status_code, 200)
        self.assertNotContains(response, self.sub_category_a1.sub_category) # Should return no subcategories
```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated templates and views already incorporate HTMX for all dynamic updates and form submissions. Alpine.js is used for managing the active tab state and per-row checkbox logic within the `_officestaff_table.html` and `_detail_tab_content.html` to enable/disable fields. `DataTables.js` is used for the employee list.

*   **HTMX:**
    *   `hx-get` on `DrpCategory` (BG Group dropdown) to filter and reload `officestaffTable-container`.
    *   `hx-post` on the "Select" button within `officestaffTable` rows to send selected employee data and load `detailsTabContent`.
    *   `hx-put` on the final "Add Man Power Planning" button (`btnSelect`) to submit the `ManPowerPlanningDetailFormset` for saving.
    *   `hx-get` on `drpCat` to fetch sub-categories for `drpSubCat`.
    *   `hx-trigger` headers are used to signal client-side actions like `refreshPage`, `errorMessage`, and `successMessage`.
*   **Alpine.js:**
    *   `x-data` is used for managing component-specific states.
    *   `activeTab` in `list.html` controls which tab content (`planning` or `details`) is visible.
    *   `woDeptType` in `_officestaff_table.html` controls the visibility of WO No text input vs. Department dropdown per row.
    *   `isSelected` in `_detail_tab_content.html` controls the `x-bind:disabled` attribute for fields related to an equipment item, based on the `ChkSelect` checkbox state.
*   **DataTables:**
    *   Applied to `#officestaffTable` in `_officestaff_table.html` for client-side search, sort, and pagination.

## Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating your ASP.NET Man Power Planning application to Django. By leveraging AI-assisted analysis and adhering to modern architectural patterns, we ensure:

*   **Business Agility:** A flexible Django backend with clean APIs allows for faster development of new features and integrations.
*   **Enhanced User Experience:** HTMX and Alpine.js provide a dynamic, responsive user interface without the complexity of traditional JavaScript frameworks, leading to smoother interactions.
*   **Scalability & Performance:** Django's robust framework and ORM, combined with efficient database interaction patterns (fat models, optimized queries), ensure the application can handle growing data and user loads.
*   **Maintainability:** Strict separation of concerns (fat models, thin views, componentized templates) and comprehensive test coverage make the codebase easier to understand, debug, and extend.
*   **Cost Efficiency:** Automation-driven migration reduces manual effort and potential errors, minimizing project timelines and overall development costs.

This systematic approach minimizes risks associated with legacy system modernization and positions your organization for future growth with a modern, maintainable technology stack.