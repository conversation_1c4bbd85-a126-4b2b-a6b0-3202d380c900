## ASP.NET to Django Conversion Script: Man Power Planning Report Modernization

This modernization plan outlines the strategy to transition your ASP.NET Crystal Reports viewer for Man Power Planning into a modern, Django-based web application. The focus is on leveraging Django's robust ORM, HTMX for dynamic interactions, Alpine.js for client-side logic, and DataTables for interactive data presentation, ensuring a highly performant and user-friendly experience.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

The ASP.NET code interacts with multiple database tables to generate the report. Based on the SQL queries and data population logic, we identify the following tables and their inferred columns:

*   **`tblPM_ManPowerPlanning_Amd`**: This is the central table for man power planning amendments.
    *   `MId` (int, queried by `Id` from query string)
    *   `Id` (int, unique identifier for each amendment record, used in `dt`)
    *   `EmpId` (string, Employee ID)
    *   `Date` (datetime)
    *   `WONo` (string, Work Order Number)
    *   `Dept` (int, Department ID)
    *   `Types` (int, type of activity, e.g., Present, Absent)
    *   `Description` (string)
    *   `Hours` (double, recorded hours)
    *   `CompId` (int, Company ID)
    *   `AmendmentNo` (string)
    *   `ActualDesc` (string)

*   **`tblHR_OfficeStaff`**: Stores employee details.
    *   `EmpId` (string, primary key)
    *   `Title` (string, e.g., Mr., Ms.)
    *   `EmployeeName` (string)
    *   `Designation` (int, Designation ID)
    *   `OfferId` (string)
    *   *(Inferred: `empSalary` and `DutyHrs` fields are needed for calculations, though not explicitly in the provided C# code. We will add placeholder fields for these on this model.)*

*   **`tblHR_Designation`**: Stores designation details.
    *   `Id` (int, primary key)
    *   `Symbol` (string, designation symbol)
    *   `Type` (string, designation type)

*   **`BusinessGroup`**: Stores department/business group details.
    *   `Id` (int, primary key)
    *   `Symbol` (string, department symbol)

### Step 2: Identify Backend Functionality

The ASP.NET page is purely a **Read/Display** functionality. It gathers data from multiple tables, performs calculations, and presents it as a formatted report. There are no direct Create, Update, or Delete (CRUD) operations initiated from *this specific page*. The "Cancel" button is a navigation action.

To meet the requirement for comprehensive Django code including CRUD, we will provide a full set of CRUD operations for the primary model, `ManPowerPlanningAmendment`, in addition to the specific report display functionality.

### Step 3: Infer UI Components

The original ASP.NET page uses a `CrystalReportViewer` and a `Button`.
In Django, this will be transformed as follows:

*   **Report Display:** The Crystal Report will be replaced by a structured HTML table, powered by **DataTables** for interactive features (search, sort, pagination). This table will be loaded dynamically using **HTMX**.
*   **Cancel Button:** A standard HTML button styled with Tailwind CSS, triggering a client-side redirect.
*   **Modals for CRUD:** For the general CRUD functionality of `ManPowerPlanningAmendment`, HTMX will be used to load forms (add/edit) and confirmation dialogues (delete) into modal windows, managed by **Alpine.js** for UI state.

---

### Step 4: Generate Django Code

The application name will be `project_management` based on the ASP.NET module path.

#### 4.1 Models (`project_management/models.py`)

Here, we define the Django models that map to your existing database tables. The complex report generation logic is encapsulated as a class method within the `ManPowerPlanningAmendment` model, adhering to the "fat model" principle.

```python
from django.db import models
from django.db.models import F
import datetime

class BusinessGroup(models.Model):
    """
    Maps to the existing 'BusinessGroup' table, storing department details.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

class Designation(models.Model):
    """
    Maps to the existing 'tblHR_Designation' table, storing employee designation details.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    type = models.CharField(db_column='Type', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblHR_Designation'
        verbose_name = 'Designation'
        verbose_name_plural = 'Designations'

    def __str__(self):
        return f"{self.symbol} - {self.type}"

class OfficeStaff(models.Model):
    """
    Maps to the existing 'tblHR_OfficeStaff' table, storing office staff details.
    Includes inferred fields for salary and duty hours for report calculations.
    """
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=255)
    title = models.CharField(db_column='Title', max_length=50, null=True, blank=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    designation = models.ForeignKey(Designation, on_delete=models.DO_NOTHING, db_column='Designation', related_name='staff_members')
    offer_id = models.CharField(db_column='OfferId', max_length=255, null=True, blank=True)
    # Inferred fields for report calculation based on ASP.NET usage
    # These would typically come from more detailed salary/HR tables,
    # but are added here for demonstration of calculation.
    emp_salary = models.DecimalField(max_digits=10, decimal_places=2, default=0.00) # Assumed
    duty_hours = models.DecimalField(max_digits=5, decimal_places=2, default=8.00) # Assumed daily duty hours

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title or ''} {self.employee_name}"

class ManPowerPlanningAmendment(models.Model):
    """
    Maps to the existing 'tblPM_ManPowerPlanning_Amd' table.
    Contains the complex report generation logic as a class method.
    """
    mid = models.IntegerField(db_column='MId', unique=True) # MId is often a FK to a Master table, but here it's used as a primary query key.
    id = models.IntegerField(db_column='Id', primary_key=True) # Primary key for this specific amendment record.
    emp = models.ForeignKey(OfficeStaff, on_delete=models.DO_NOTHING, db_column='EmpId', to_field='emp_id', related_name='manpower_amendments')
    date = models.DateField(db_column='Date')
    wo_no = models.CharField(db_column='WONo', max_length=255, null=True, blank=True)
    dept = models.ForeignKey(BusinessGroup, on_delete=models.DO_NOTHING, db_column='Dept', related_name='manpower_amendments')
    types = models.IntegerField(db_column='Types')
    description = models.TextField(db_column='Description', null=True, blank=True)
    hours = models.FloatField(db_column='Hours', null=True, blank=True)
    comp_id = models.IntegerField(db_column='CompId')
    amendment_no = models.CharField(db_column='AmendmentNo', max_length=255, null=True, blank=True)
    actual_desc = models.TextField(db_column='ActualDesc', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblPM_ManPowerPlanning_Amd'
        verbose_name = 'Manpower Planning Amendment'
        verbose_name_plural = 'Manpower Planning Amendments'
        # Ordering based on the ASP.NET query 'Order by Id Desc'
        ordering = ['-id']

    def __str__(self):
        return f"MPP Amendment {self.id} for {self.emp.employee_name}"

    @classmethod
    def get_manpower_report_data(cls, mid):
        """
        Fetches and processes data to generate the Man Power Planning Report,
        mimicking the complex logic from the ASP.NET code-behind.
        This method ensures all necessary joins and calculations are performed efficiently.
        """
        report_data = []

        # Optimize queries using select_related to fetch related objects in one go
        amendments = cls.objects.filter(mid=mid).select_related(
            'emp__designation', # Join OfficeStaff and its Designation
            'dept' # Join BusinessGroup
        )

        for amendment in amendments:
            employee_name_full = f"{amendment.emp.title or ''}. {amendment.emp.employee_name}"
            employee_emp_id = amendment.emp.emp_id
            
            designation_str = f"{amendment.emp.designation.symbol} - {amendment.emp.designation.type}"

            report_date = amendment.date.strftime('%d-%m-%Y') # Format as DD-MM-YYYY

            wo_no_display = amendment.wo_no if amendment.wo_no and amendment.wo_no != '' else 'NA'
            
            dept_symbol = amendment.dept.symbol if amendment.dept else 'NA' # Handle case where dept might be null

            types_display = {
                1: "Present",
                2: "Absent",
                3: "Onsite",
                4: "PL",
            }.get(amendment.types, "Unknown")

            description_val = amendment.description or ''
            
            total_hours_recorded = amendment.hours if amendment.hours is not None else 0.0

            # Calculations mimicking ASP.NET logic:
            # Need empSalary and DutyHrs from OfficeStaff model for calculation
            emp_salary = float(amendment.emp.emp_salary) if amendment.emp.emp_salary else 0.0
            duty_hours_per_day = float(amendment.emp.duty_hours) if amendment.emp.duty_hours else 8.0 # Default to 8 if not set

            total_days_in_month = datetime.date(amendment.date.year, amendment.date.month, 1).day + (datetime.date(amendment.date.year, amendment.date.month, 1).replace(day=28) + datetime.timedelta(days=4)).day - 28 # Pythonic way to get days in month
            
            if total_days_in_month > 0 and duty_hours_per_day > 0:
                total_hrs_rate = emp_salary / (total_days_in_month * duty_hours_per_day)
            else:
                total_hrs_rate = 0.0
            
            total_amount = total_hrs_rate * total_hours_recorded

            amendment_no_display = amendment.amendment_no if amendment.amendment_no and amendment.amendment_no != '0' else '0'
            actual_desc_val = amendment.actual_desc or ''

            report_data.append({
                'employee_name': employee_name_full,
                'emp_id': employee_emp_id,
                'designation': designation_str,
                'date': report_date,
                'wo_no': wo_no_display,
                'dept': dept_symbol,
                'types': types_display,
                'description': description_val,
                'tot_hours': round(total_hours_recorded, 2),
                'record_id': amendment.id, # Using this as 'Id' in the report table
                'comp_id': amendment.comp_id,
                'total_hrs_rate': round(total_hrs_rate, 2),
                'total_amount': round(total_amount, 2),
                'amendment_no': amendment_no_display,
                'actual_desc': actual_desc_val,
            })
        
        return report_data
```

#### 4.2 Forms (`project_management/forms.py`)

This section defines a form for the `ManPowerPlanningAmendment` model, which would be used for its general CRUD operations (Add/Edit). Since the original ASP.NET page was a report viewer, it didn't have input forms for `ManPowerPlanningAmendment` directly, but this is included for comprehensive modernization.

```python
from django import forms
from .models import ManPowerPlanningAmendment, OfficeStaff, BusinessGroup, Designation

class ManPowerPlanningAmendmentForm(forms.ModelForm):
    """
    Form for creating and updating ManPowerPlanningAmendment records.
    Uses ModelChoiceField for related models (emp, dept).
    """
    class Meta:
        model = ManPowerPlanningAmendment
        fields = [
            'mid', 'emp', 'date', 'wo_no', 'dept', 'types',
            'description', 'hours', 'comp_id', 'amendment_no', 'actual_desc'
        ]
        widgets = {
            'mid': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'emp': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'wo_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'dept': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'types': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'hours': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'comp_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'amendment_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'actual_desc': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
        }

    # Custom validation example (optional, but good practice)
    def clean_mid(self):
        mid = self.cleaned_data['mid']
        if mid < 0:
            raise forms.ValidationError("MId cannot be negative.")
        return mid

```

#### 4.3 Views (`project_management/views.py`)

This section provides both the standard CRUD views for `ManPowerPlanningAmendment` (as per the comprehensive requirement) and the specific report view. Views are kept thin, delegating business logic to models.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from .models import ManPowerPlanningAmendment
from .forms import ManPowerPlanningAmendmentForm
import json

# --- General CRUD Views for ManPowerPlanningAmendment ---

class ManPowerPlanningAmendmentListView(ListView):
    """
    Displays a list of ManPowerPlanningAmendment objects, rendered via DataTables.
    This serves as the primary list management page for the amendments.
    """
    model = ManPowerPlanningAmendment
    template_name = 'project_management/manpowerplanningamendment/list.html'
    context_object_name = 'manpowerplanningamendments'

class ManPowerPlanningAmendmentTablePartialView(ListView):
    """
    Partial view to load the DataTables content for ManPowerPlanningAmendment list,
    used by HTMX to refresh the table without full page reload.
    """
    model = ManPowerPlanningAmendment
    template_name = 'project_management/manpowerplanningamendment/_table.html'
    context_object_name = 'manpowerplanningamendments'

    def get_queryset(self):
        # Example of adding order_by from model Meta
        return super().get_queryset()

class ManPowerPlanningAmendmentCreateView(CreateView):
    """
    Handles creation of new ManPowerPlanningAmendment records via a modal form.
    """
    model = ManPowerPlanningAmendment
    form_class = ManPowerPlanningAmendmentForm
    template_name = 'project_management/manpowerplanningamendment/_form.html'
    success_url = reverse_lazy('manpowerplanningamendment_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Manpower Planning Amendment added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshManPowerPlanningAmendmentList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # For HTMX, re-render the form with errors
            return response
        return response

class ManPowerPlanningAmendmentUpdateView(UpdateView):
    """
    Handles updating existing ManPowerPlanningAmendment records via a modal form.
    """
    model = ManPowerPlanningAmendment
    form_class = ManPowerPlanningAmendmentForm
    template_name = 'project_management/manpowerplanningamendment/_form.html'
    success_url = reverse_lazy('manpowerplanningamendment_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Manpower Planning Amendment updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshManPowerPlanningAmendmentList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # For HTMX, re-render the form with errors
            return response
        return response

class ManPowerPlanningAmendmentDeleteView(DeleteView):
    """
    Handles deletion of ManPowerPlanningAmendment records via a confirmation modal.
    """
    model = ManPowerPlanningAmendment
    template_name = 'project_management/manpowerplanningamendment/_confirm_delete.html'
    success_url = reverse_lazy('manpowerplanningamendment_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Manpower Planning Amendment deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshManPowerPlanningAmendmentList'
                }
            )
        return response

# --- Man Power Planning Report Specific View ---

class ManPowerPlanningReportView(TemplateView):
    """
    Displays the Man Power Planning Report for a given MId,
    using data prepared by the ManPowerPlanningAmendment model's class method.
    This replaces the Crystal Report Viewer.
    """
    template_name = 'project_management/manpowerplanningamendment/report_details.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        mid = self.kwargs['mid'] # Get MId from URL
        context['mid'] = mid
        # The actual report data will be loaded via HTMX into a partial
        return context

class ManPowerPlanningReportTablePartialView(View):
    """
    HTMX endpoint to serve the report data as a JSON for DataTables or
    render a partial HTML table. For simplicity with DataTables, we'll
    prepare data for client-side DataTables initialization.
    Alternatively, this could render the _report_table.html directly if
    DataTables is configured for server-side processing.
    For this example, we will render a table that DataTables will initialize.
    """
    def get(self, request, mid, *args, **kwargs):
        report_data = ManPowerPlanningAmendment.get_manpower_report_data(mid)
        context = {
            'report_entries': report_data
        }
        return HttpResponse(
            self.render_to_string('project_management/manpowerplanningamendment/_report_table.html', context),
            headers={'HX-Trigger': 'reportTableLoaded'} # Trigger client-side JS for DataTable init
        )

    # Helper method to render template content (often from TemplateResponseMixin)
    def render_to_string(self, template_name, context):
        from django.template.loader import render_to_string
        return render_to_string(template_name, context, request=self.request)

```

#### 4.4 Templates (`project_management/templates/project_management/manpowerplanningamendment/`)

Here are the templates required. Remember, `base.html` is assumed to exist and is not included.

**`list.html` (Main CRUD List Page for ManPowerPlanningAmendment)**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Manpower Planning Amendments</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'manpowerplanningamendment_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Amendment
        </button>
    </div>
    
    <div id="manpowerplanningamendmentTable-container"
         hx-trigger="load, refreshManPowerPlanningAmendmentList from:body"
         hx-get="{% url 'manpowerplanningamendment_table_partial' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for broader modal management
    });
</script>
{% endblock %}
```

**`_table.html` (Partial for ManPowerPlanningAmendment List - HTMX loaded)**
```html
<table id="manpowerplanningamendmentTable" class="min-w-full bg-white border border-gray-200 shadow-sm rounded-lg overflow-hidden">
    <thead>
        <tr class="bg-gray-50">
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MId</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Work Order No.</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hours</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in manpowerplanningamendments %}
        <tr class="hover:bg-gray-50">
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ forloop.counter }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.mid }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.emp.employee_name }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.date|date:"d-m-Y" }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.wo_no|default:"N/A" }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.dept.symbol|default:"N/A" }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">
                {% if obj.types == 1 %}Present{% elif obj.types == 2 %}Absent{% elif obj.types == 3 %}Onsite{% elif obj.types == 4 %}PL{% else %}Unknown{% endif %}
            </td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.hours|default:"0.00" }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2 text-xs"
                    hx-get="{% url 'manpowerplanningamendment_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs"
                    hx-get="{% url 'manpowerplanningamendment_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
                <a 
                    href="{% url 'manpowerplanningamendment_report_view' obj.mid %}" 
                    class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded ml-2 text-xs">
                    View Report
                </a>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Initialize DataTables on the loaded table
    $(document).ready(function() {
        $('#manpowerplanningamendmentTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true
        });
    });
</script>
```

**`_form.html` (Partial for ManPowerPlanningAmendment Add/Edit Form - HTMX loaded)**
```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Manpower Planning Amendment</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-target="#modalContent" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
            {% for field in form %}
            <div class="mb-2">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
            <span id="form-spinner" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
            </span>
        </div>
    </form>
</div>
```

**`_confirm_delete.html` (Partial for ManPowerPlanningAmendment Delete Confirmation - HTMX loaded)**
```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="mb-6 text-gray-700">Are you sure you want to delete Manpower Planning Amendment for <strong>{{ object.emp.employee_name }}</strong> dated <strong>{{ object.date|date:"d-m-Y" }}</strong>?</p>
    <form hx-post="{% url 'manpowerplanningamendment_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

**`report_details.html` (Main Report Display Page)**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Manpower Planning Report for MId: {{ mid }}</h2>
        <a href="{% url 'manpowerplanningamendment_list' %}"
           class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            Cancel
        </a>
    </div>
    
    <div id="manpowerReportTable-container"
         hx-trigger="load, reportTableLoaded from:body"
         hx-get="{% url 'manpowerplanningamendment_report_table_partial' mid=mid %}"
         hx-swap="innerHTML">
        <!-- Report DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading Manpower Report...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components if needed for report specific UI elements
    });
</script>
{% endblock %}
```

**`_report_table.html` (Partial for Report DataTables - HTMX loaded)**
```html
<table id="manpowerReportDataTable" class="min-w-full bg-white border border-gray-200 shadow-sm rounded-lg overflow-hidden">
    <thead>
        <tr class="bg-gray-50">
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Emp Id</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Designation</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dept</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Hours</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Record Id</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company Id</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate/Hr</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amt</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amendment No</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actual Desc</th>
        </tr>
    </thead>
    <tbody>
        {% for entry in report_entries %}
        <tr class="hover:bg-gray-50">
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ forloop.counter }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ entry.employee_name }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ entry.emp_id }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ entry.designation }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ entry.date }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ entry.wo_no }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ entry.dept }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ entry.types }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ entry.description }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ entry.tot_hours }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ entry.record_id }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ entry.comp_id }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ entry.total_hrs_rate }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ entry.total_amount }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ entry.amendment_no }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ entry.actual_desc }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Initialize DataTables on the loaded table
    $(document).ready(function() {
        $('#manpowerReportDataTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true
        });
    });
</script>
```

#### 4.5 URLs (`project_management/urls.py`)

This defines the URL patterns for accessing the CRUD pages and the specific report page.

```python
from django.urls import path
from .views import (
    ManPowerPlanningAmendmentListView, ManPowerPlanningAmendmentCreateView,
    ManPowerPlanningAmendmentUpdateView, ManPowerPlanningAmendmentDeleteView,
    ManPowerPlanningAmendmentTablePartialView,
    ManPowerPlanningReportView, ManPowerPlanningReportTablePartialView
)

urlpatterns = [
    # URLs for general ManPowerPlanningAmendment CRUD
    path('manpowerplanningamendment/', ManPowerPlanningAmendmentListView.as_view(), name='manpowerplanningamendment_list'),
    path('manpowerplanningamendment/add/', ManPowerPlanningAmendmentCreateView.as_view(), name='manpowerplanningamendment_add'),
    path('manpowerplanningamendment/edit/<int:pk>/', ManPowerPlanningAmendmentUpdateView.as_view(), name='manpowerplanningamendment_edit'),
    path('manpowerplanningamendment/delete/<int:pk>/', ManPowerPlanningAmendmentDeleteView.as_view(), name='manpowerplanningamendment_delete'),
    # HTMX partial endpoint for the list table
    path('manpowerplanningamendment/table/', ManPowerPlanningAmendmentTablePartialView.as_view(), name='manpowerplanningamendment_table_partial'),

    # URL for the specific Man Power Planning Report
    path('manpowerplanningamendment/report/<int:mid>/', ManPowerPlanningReportView.as_view(), name='manpowerplanningamendment_report_view'),
    # HTMX partial endpoint for the report table
    path('manpowerplanningamendment/report/<int:mid>/table/', ManPowerPlanningReportTablePartialView.as_view(), name='manpowerplanningamendment_report_table_partial'),
]
```

#### 4.6 Tests (`project_management/tests.py`)

Comprehensive unit tests for the models (especially the report generation method) and integration tests for all views.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from .models import ManPowerPlanningAmendment, OfficeStaff, Designation, BusinessGroup
from datetime import date

class ManPowerPlanningAmendmentModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.business_group = BusinessGroup.objects.create(id=1, symbol='DEP1')
        cls.designation = Designation.objects.create(id=1, symbol='DEV', type='Developer')
        cls.office_staff = OfficeStaff.objects.create(
            emp_id='EMP001',
            title='Mr',
            employee_name='John Doe',
            designation=cls.designation,
            emp_salary=50000.00,
            duty_hours=8.00
        )
        cls.amendment1 = ManPowerPlanningAmendment.objects.create(
            id=101, # This 'id' is the primary key for the record itself
            mid=1,
            emp=cls.office_staff,
            date=date(2023, 10, 26),
            wo_no='WO-001',
            dept=cls.business_group,
            types=1, # Present
            description='Project Alpha work',
            hours=8.0,
            comp_id=1,
            amendment_no='0',
            actual_desc='Actual work on Alpha'
        )
        cls.amendment2 = ManPowerPlanningAmendment.objects.create(
            id=102,
            mid=1,
            emp=cls.office_staff,
            date=date(2023, 10, 27),
            wo_no=None, # Test null WO
            dept=cls.business_group,
            types=2, # Absent
            description='Sick leave',
            hours=0.0,
            comp_id=1,
            amendment_no='1',
            actual_desc='Sick'
        )
        cls.amendment3 = ManPowerPlanningAmendment.objects.create(
            id=103,
            mid=2, # Different MId
            emp=cls.office_staff,
            date=date(2023, 10, 28),
            wo_no='WO-002',
            dept=cls.business_group,
            types=3, # Onsite
            description='Client visit',
            hours=6.5,
            comp_id=2,
            amendment_no='0',
            actual_desc='Onsite visit'
        )
  
    def test_manpowerplanningamendment_creation(self):
        obj = ManPowerPlanningAmendment.objects.get(id=101)
        self.assertEqual(obj.mid, 1)
        self.assertEqual(obj.emp.employee_name, 'John Doe')
        self.assertEqual(obj.date, date(2023, 10, 26))
        self.assertEqual(obj.wo_no, 'WO-001')
        self.assertEqual(obj.dept.symbol, 'DEP1')
        self.assertEqual(obj.types, 1)
        self.assertEqual(obj.hours, 8.0)
        
    def test_report_data_generation(self):
        report_data = ManPowerPlanningAmendment.get_manpower_report_data(mid=1)
        self.assertEqual(len(report_data), 2) # Should fetch 2 records for mid=1

        # Test first entry
        entry1 = report_data[0]
        self.assertEqual(entry1['employee_name'], 'Mr. John Doe')
        self.assertEqual(entry1['emp_id'], 'EMP001')
        self.assertEqual(entry1['designation'], 'DEV - Developer')
        self.assertEqual(entry1['date'], '26-10-2023')
        self.assertEqual(entry1['wo_no'], 'WO-001')
        self.assertEqual(entry1['dept'], 'DEP1')
        self.assertEqual(entry1['types'], 'Present')
        self.assertEqual(entry1['tot_hours'], 8.0)
        self.assertEqual(entry1['record_id'], 101)
        self.assertAlmostEqual(entry1['total_hrs_rate'], 50000 / (31 * 8), places=2) # Oct has 31 days
        self.assertAlmostEqual(entry1['total_amount'], (50000 / (31 * 8)) * 8, places=2)

        # Test second entry (with null WO_No, AmendmentNo, zero hours)
        entry2 = report_data[1]
        self.assertEqual(entry2['wo_no'], 'NA')
        self.assertEqual(entry2['types'], 'Absent')
        self.assertEqual(entry2['tot_hours'], 0.0)
        self.assertEqual(entry2['record_id'], 102)
        self.assertEqual(entry2['amendment_no'], '1') # Test amendment_no non-zero case

    def test_report_data_for_different_mid(self):
        report_data = ManPowerPlanningAmendment.get_manpower_report_data(mid=2)
        self.assertEqual(len(report_data), 1)
        self.assertEqual(report_data[0]['record_id'], 103)

    def test_field_labels(self):
        obj = ManPowerPlanningAmendment.objects.get(id=101)
        field_label = obj._meta.get_field('emp').verbose_name
        self.assertEqual(field_label, 'emp') # Default verbose name if not specified

class ManPowerPlanningAmendmentViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.business_group = BusinessGroup.objects.create(id=1, symbol='DEP1')
        cls.designation = Designation.objects.create(id=1, symbol='DEV', type='Developer')
        cls.office_staff = OfficeStaff.objects.create(
            emp_id='EMP001',
            title='Mr',
            employee_name='John Doe',
            designation=cls.designation,
            emp_salary=50000.00,
            duty_hours=8.00
        )
        cls.office_staff_2 = OfficeStaff.objects.create(
            emp_id='EMP002',
            title='Ms',
            employee_name='Jane Smith',
            designation=cls.designation,
            emp_salary=60000.00,
            duty_hours=7.50
        )
        cls.amendment = ManPowerPlanningAmendment.objects.create(
            id=101, mid=1, emp=cls.office_staff, date=date(2023, 10, 26),
            wo_no='WO-001', dept=cls.business_group, types=1, description='Test Desc',
            hours=8.0, comp_id=1, amendment_no='0', actual_desc='Actual'
        )
        cls.amendment_report = ManPowerPlanningAmendment.objects.create(
            id=102, mid=100, emp=cls.office_staff_2, date=date(2023, 11, 15),
            wo_no='REP-001', dept=cls.business_group, types=1, description='Report Test',
            hours=7.0, comp_id=1, amendment_no='0', actual_desc='Report Actual'
        )
    
    def setUp(self):
        self.client = Client()
    
    # --- CRUD Views Tests ---

    def test_list_view_get(self):
        response = self.client.get(reverse('manpowerplanningamendment_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/manpowerplanningamendment/list.html')
        self.assertIn('manpowerplanningamendments', response.context)

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('manpowerplanningamendment_table_partial'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/manpowerplanningamendment/_table.html')
        self.assertIn('manpowerplanningamendments', response.context)
        self.assertTrue(b'<table id="manpowerplanningamendmentTable"' in response.content)

    def test_create_view_get(self):
        response = self.client.get(reverse('manpowerplanningamendment_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/manpowerplanningamendment/_form.html')
        self.assertIn('form', response.context)
        
    def test_create_view_post_success(self):
        data = {
            'mid': 3,
            'emp': self.office_staff.pk,
            'date': '2024-01-01',
            'wo_no': 'NEW-WO',
            'dept': self.business_group.pk,
            'types': 1,
            'description': 'New entry',
            'hours': 7.5,
            'comp_id': 1,
            'amendment_no': '0',
            'actual_desc': 'Actual new'
        }
        response = self.client.post(reverse('manpowerplanningamendment_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertEqual(response.headers['HX-Trigger'], 'refreshManPowerPlanningAmendmentList')
        self.assertTrue(ManPowerPlanningAmendment.objects.filter(mid=3).exists())
        
    def test_create_view_post_invalid(self):
        data = {
            'mid': -1, # Invalid mid
            'emp': self.office_staff.pk,
            'date': '2024-01-01',
            'wo_no': 'NEW-WO',
            'dept': self.business_group.pk,
            'types': 1,
            'description': 'New entry',
            'hours': 7.5,
            'comp_id': 1,
            'amendment_no': '0',
            'actual_desc': 'Actual new'
        }
        response = self.client.post(reverse('manpowerplanningamendment_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX re-renders form with errors
        self.assertIn(b'MId cannot be negative.', response.content)
        self.assertFalse(ManPowerPlanningAmendment.objects.filter(mid=-1).exists())


    def test_update_view_get(self):
        obj = self.amendment
        response = self.client.get(reverse('manpowerplanningamendment_edit', args=[obj.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/manpowerplanningamendment/_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance.pk, obj.pk)
        
    def test_update_view_post_success(self):
        obj = self.amendment
        data = {
            'mid': obj.mid,
            'emp': obj.emp.pk,
            'date': obj.date.isoformat(),
            'wo_no': 'UPDATED-WO',
            'dept': obj.dept.pk,
            'types': obj.types,
            'description': 'Updated description',
            'hours': obj.hours,
            'comp_id': obj.comp_id,
            'amendment_no': obj.amendment_no,
            'actual_desc': obj.actual_desc
        }
        response = self.client.post(reverse('manpowerplanningamendment_edit', args=[obj.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshManPowerPlanningAmendmentList')
        obj.refresh_from_db()
        self.assertEqual(obj.wo_no, 'UPDATED-WO')

    def test_delete_view_get(self):
        obj = self.amendment
        response = self.client.get(reverse('manpowerplanningamendment_delete', args=[obj.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/manpowerplanningamendment/_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'].pk, obj.pk)
        
    def test_delete_view_post_success(self):
        obj_to_delete = ManPowerPlanningAmendment.objects.create(
            id=999, mid=999, emp=self.office_staff, date=date(2023, 1, 1),
            dept=self.business_group, types=1, comp_id=1
        )
        response = self.client.post(reverse('manpowerplanningamendment_delete', args=[obj_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshManPowerPlanningAmendmentList')
        self.assertFalse(ManPowerPlanningAmendment.objects.filter(pk=obj_to_delete.pk).exists())

    # --- Report Views Tests ---

    def test_report_view_get(self):
        response = self.client.get(reverse('manpowerplanningamendment_report_view', args=[self.amendment_report.mid]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/manpowerplanningamendment/report_details.html')
        self.assertIn('mid', response.context)
        self.assertEqual(response.context['mid'], self.amendment_report.mid)

    def test_report_table_partial_view_get(self):
        response = self.client.get(reverse('manpowerplanningamendment_report_table_partial', args=[self.amendment_report.mid]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/manpowerplanningamendment/_report_table.html')
        self.assertIn('report_entries', response.context)
        self.assertIsInstance(response.context['report_entries'], list)
        self.assertEqual(len(response.context['report_entries']), 1) # Only one entry for mid=100
        self.assertEqual(response.context['report_entries'][0]['record_id'], self.amendment_report.id)
        self.assertTrue(b'<table id="manpowerReportDataTable"' in response.content)
        self.assertEqual(response.headers['HX-Trigger'], 'reportTableLoaded')

```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for dynamic updates:**
    *   The `list.html` page uses `hx-get` to load the `_table.html` partial (containing the DataTables) on page load and whenever `refreshManPowerPlanningAmendmentList` is triggered (after Add/Edit/Delete).
    *   Buttons for "Add", "Edit", and "Delete" use `hx-get` to fetch their respective `_form.html` or `_confirm_delete.html` partials into a modal.
    *   Form submissions (`hx-post`) from the modals target `hx-swap="none"` and rely on `HX-Trigger` to refresh the main list.
*   **Alpine.js for UI state management:**
    *   Used to control the visibility of the modal (`on click add .is-active to #modal` and `on click remove .is-active from me`). This keeps the UI logic separate from HTMX.
*   **DataTables for list views:**
    *   Both `_table.html` (for CRUD list) and `_report_table.html` (for the report) initialize DataTables on their respective tables using a `script` tag within the partial. This ensures DataTables is re-initialized correctly whenever the partial is loaded/refreshed by HTMX.
*   **No custom JavaScript:** All dynamic interactions are handled through HTMX attributes and Alpine.js directives, avoiding the need for large, custom JavaScript files.
*   **DRY Templates:** Use of partials (`_table.html`, `_form.html`, `_confirm_delete.html`, `_report_table.html`) ensures reusability and maintainability.

### Final Notes

This comprehensive plan provides a clear pathway for migrating the specified ASP.NET Crystal Report viewer to a modern Django application. It not only re-implements the report's complex data aggregation logic but also extends the functionality to include full CRUD capabilities for the core `ManPowerPlanningAmendment` model, adhering strictly to the "fat model, thin view" principle, HTMX/Alpine.js for frontend, and DataTables for superior data presentation.

The most critical aspect was translating the multi-query, programmatic `DataTable` construction into an efficient Django ORM `select_related` and `prefetch_related` approach within a model class method. The assumption of `empSalary` and `DutyHrs` being part of `OfficeStaff` for calculation purposes would need to be verified against the actual database schema.

This structured approach, with its focus on automated generation and clear architectural patterns, is designed for efficient execution and easy understanding by both technical and non-technical stakeholders.