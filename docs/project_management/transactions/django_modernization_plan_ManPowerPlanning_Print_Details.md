## ASP.NET to Django Conversion Script: Manpower Planning Print Details

This modernization plan outlines the automated conversion of the legacy ASP.NET Manpower Planning Print Details module into a modern Django 5.0+ application. The approach prioritizes automation, leveraging AI-assisted tools to transform existing logic into Django's "Fat Model, Thin View" architecture, utilizing HTMX, Alpine.js, and DataTables for a highly interactive and efficient user experience.

### Business Value Proposition:

Migrating this module to Django offers significant benefits:
*   **Reduced Operational Costs:** Eliminates reliance on proprietary technologies like Crystal Reports and legacy ASP.NET, leading to lower licensing fees and easier maintenance.
*   **Improved Scalability & Performance:** Django's robust architecture and efficient ORM provide a scalable foundation, capable of handling increased data volumes and user loads more effectively than the legacy system.
*   **Enhanced User Experience:** Interactive DataTables for data display, coupled with HTMX and Alpine.js, delivers a responsive, modern web interface with no full page reloads, improving user productivity and satisfaction.
*   **Increased Development Agility:** Modern Django patterns and a vibrant open-source ecosystem simplify future development, allowing for faster feature delivery and easier integration with other systems.
*   **Better Data Insights:** By transforming report generation into interactive data views, users gain immediate access to filtered and sorted information, facilitating quicker decision-making.

### Conversion Steps:

### Step 1: Extract Database Schema

Based on the SQL queries in the ASP.NET code-behind, the following database tables and their inferred relationships are identified:

*   **`tblPM_ManPowerPlanning`**: Core Manpower Planning records.
    *   `Id` (PK, INT)
    *   `CompId` (INT, FK to Company/BusinessUnit)
    *   `Date` (DateTime)
    *   `EmpId` (VARCHAR, FK to `tblHR_OfficeStaff`)
    *   `WONo` (VARCHAR, Work Order Number, nullable)
    *   `Dept` (INT, FK to `BusinessGroup`)
    *   `Types` (INT, Planning Type: Present, Absent, Onsite, PL)

*   **`tblPM_ManPowerPlanning_Details`**: Details associated with Manpower Planning entries.
    *   `Id` (PK, INT)
    *   `MId` (INT, FK to `tblPM_ManPowerPlanning.Id`)
    *   `EquipId` (INT, FK to `tblDG_Item_Master`)
    *   `PlannedDesc` (VARCHAR, Planned Description)
    *   `ActualDesc` (VARCHAR, Actual Description)
    *   `Hour` (FLOAT, Hours)
    *   `Category` (INT, FK to `tblMIS_BudgetHrs_Field_Category`)
    *   `SubCategory` (INT, FK to `tblMIS_BudgetHrs_Field_SubCategory`)

*   **`tblHR_OfficeStaff`**: Employee Master Data.
    *   `EmpId` (PK, VARCHAR)
    *   `Title` (VARCHAR)
    *   `EmployeeName` (VARCHAR)
    *   `Designation` (INT, FK to `tblHR_Designation`)
    *   `OfferId` (INT, related to HR offers)

*   **`tblHR_Designation`**: Employee Designation Types.
    *   `Id` (PK, INT)
    *   `Type` (VARCHAR, e.g., "Engineer")
    *   `Symbol` (VARCHAR, e.g., "ENGR")

*   **`BusinessGroup`**: Department/Business Unit information.
    *   `Id` (PK, INT)
    *   `Symbol` (VARCHAR, e.g., "HR", "PROJ")

*   **`tblDG_Item_Master`**: Equipment/Item Master.
    *   `Id` (PK, INT)
    *   `ItemCode` (VARCHAR)
    *   `ManfDesc` (VARCHAR, Manufacturer Description)

*   **`tblMIS_BudgetHrs_Field_Category`**: Budget Hours Categories.
    *   `Id` (PK, INT)
    *   `Category` (VARCHAR)

*   **`tblMIS_BudgetHrs_Field_SubCategory`**: Budget Hours Subcategories.
    *   `Id` (PK, INT)
    *   `SubCategory` (VARCHAR)
    *   `MId` (INT, FK to `tblMIS_BudgetHrs_Field_Category.Id`)

### Step 2: Identify Backend Functionality

The ASP.NET code primarily focuses on **Read** operations, specifically for generating a detailed report using Crystal Reports.
*   **Read:** Data is retrieved from `tblPM_ManPowerPlanning` based on various query string parameters (`x`, `y`, `w`, `z`, `r`, `date`, `CompId`, `FinYearId`). For each `ManPowerPlanning` record, related `tblPM_ManPowerPlanning_Details` are fetched. Additionally, employee details (`tblHR_OfficeStaff`, `tblHR_Designation`), department (`BusinessGroup`), and equipment/category details (`tblDG_Item_Master`, `tblMIS_BudgetHrs_Field_Category`, `tblMIS_BudgetHrs_Field_SubCategory`) are joined to enrich the data.
*   **No explicit Create, Update, or Delete operations are visible in this specific `.aspx` and `.cs` pair**, as it's a "print details" page. However, for a complete Django modernization, standard CRUD views will be generated for the main `ManPowerPlanning` entity, as per the automation template. The report generation logic will be transformed into an interactive `ListView` displaying aggregated data.

### Step 3: Infer UI Components

The ASP.NET page predominantly uses a `CrystalReportViewer` control to display a pre-designed report (`ManPowerPlanning.rpt`).
*   **CrystalReportViewer:** This indicates a display-only component for presenting complex, aggregated data. In Django, this will be replaced by a `ListView` utilizing DataTables to present the same data interactively, allowing for client-side search, sort, and pagination.
*   **Query String Parameters:** The presence of various query string parameters suggests that the report can be filtered dynamically. In Django, this implies using a `django-filter` FilterSet or a custom filter form rendered with the list view.

### Step 4: Generate Django Code

The following Django application structure (`project_management`) will be generated.

#### 4.1 Models (`project_management/models.py`)

This section defines Django models for all identified database tables, following the `managed = False` and `db_table` convention to map to an existing legacy database.

```python
from django.db import models
from django.db.models import F, Case, When, Value, CharField, Sum, Prefetch

class Company(models.Model):
    """
    Placeholder for the Company table if it exists.
    Assumed to be linked by CompId.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='CompanyName', max_length=255) # Inferred name

    class Meta:
        managed = False
        db_table = 'Company' # Or appropriate table name for companies
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name

class BusinessGroup(models.Model):
    """Corresponds to BusinessGroup table for Departments."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50) # Dept symbol

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

class Designation(models.Model):
    """Corresponds to tblHR_Designation."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    type_name = models.CharField(db_column='Type', max_length=100) # Designation type
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_Designation'
        verbose_name = 'Designation'
        verbose_name_plural = 'Designations'

    def __str__(self):
        return f"{self.symbol} - {self.type_name}" if self.symbol else self.type_name

class OfficeStaff(models.Model):
    """Corresponds to tblHR_OfficeStaff for Employee Master."""
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50)
    title = models.CharField(db_column='Title', max_length=10, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    designation = models.ForeignKey(Designation, models.DO_NOTHING, db_column='Designation', blank=True, null=True)
    offer_id = models.IntegerField(db_column='OfferId', blank=True, null=True) # Used in commented C# code

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title}. {self.employee_name}" if self.title else self.employee_name

class ItemMaster(models.Model):
    """Corresponds to tblDG_Item_Master for Equipment."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=100)
    manufacturer_description = models.CharField(db_column='ManfDesc', max_length=500)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return f"{self.item_code} - {self.manufacturer_description}"

class BudgetHrsFieldCategory(models.Model):
    """Corresponds to tblMIS_BudgetHrs_Field_Category."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    category_name = models.CharField(db_column='Category', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblMIS_BudgetHrs_Field_Category'
        verbose_name = 'Budget Category'
        verbose_name_plural = 'Budget Categories'

    def __str__(self):
        return self.category_name

class BudgetHrsFieldSubCategory(models.Model):
    """Corresponds to tblMIS_BudgetHrs_Field_SubCategory."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    subcategory_name = models.CharField(db_column='SubCategory', max_length=255)
    category = models.ForeignKey(BudgetHrsFieldCategory, models.DO_NOTHING, db_column='MId') # MId maps to Category Id

    class Meta:
        managed = False
        db_table = 'tblMIS_BudgetHrs_Field_SubCategory'
        verbose_name = 'Budget Subcategory'
        verbose_name_plural = 'Budget Subcategories'

    def __str__(self):
        return self.subcategory_name

class ManPowerPlanningManager(models.Manager):
    """Custom manager for ManPowerPlanning to handle report-specific data fetching."""
    def get_manpower_report_data(self, comp_id, start_date=None, end_date=None, emp_id=None, work_order_no=None, dept_id=None, planning_type=None):
        """
        Fetches combined master and detail data for the ManPowerPlanning report.
        This method encapsulates the complex JOINs and data preparation logic from the ASP.NET code.
        """
        queryset = self.get_queryset().filter(company_id=comp_id)

        # Apply filters based on query string parameters in ASP.NET
        # The exact mapping of x, y, w, z, r, date is inferred.
        if start_date:
            queryset = queryset.filter(planning_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(planning_date__lte=end_date)
        if emp_id:
            queryset = queryset.filter(employee__emp_id=emp_id)
        if work_order_no:
            queryset = queryset.filter(work_order_no=work_order_no)
        if dept_id:
            queryset = queryset.filter(department_id=dept_id)
        if planning_type:
            queryset = queryset.filter(planning_type=planning_type)

        # Prefetch related data to minimize queries (equivalent to multiple SQLDataReaders)
        queryset = queryset.select_related(
            'employee', 'employee__designation', 'department'
        ).prefetch_related(
            Prefetch(
                'details',
                queryset=ManPowerPlanningDetail.objects.select_related(
                    'equipment', 'category', 'subcategory'
                )
            )
        ).order_by('planning_date')

        # Annotate with derived fields or apply business logic as seen in C#
        queryset = queryset.annotate(
            employee_name_full=Case(
                When(employee__title__isnull=False, then=F('employee__title')),
                default=Value('')
            ) + Value('. ') + F('employee__employee_name'),
            designation_full=F('employee__designation__type_name'), # Simplified, C# was more complex
            dept_symbol=Case(
                When(department__symbol__isnull=False, then=F('department__symbol')),
                default=Value('NA'),
                output_field=CharField()
            ),
            # Map 'Types' integer to descriptive strings as in C# switch
            planning_type_display=Case(
                When(planning_type=1, then=Value('Present')),
                When(planning_type=2, then=Value('Absent')),
                When(planning_type=3, then=Value('Onsite')),
                When(planning_type=4, then=Value('PL')),
                default=Value('Unknown'),
                output_field=CharField()
            ),
            work_order_no_display=Case(
                When(work_order_no__isnull=True, then=Value('NA')),
                When(work_order_no='', then=Value('NA')),
                default=F('work_order_no'),
                output_field=CharField()
            )
        )
        return queryset

class ManPowerPlanning(models.Model):
    """Corresponds to tblPM_ManPowerPlanning (Master Data)."""
    PLANNING_TYPES = (
        (1, 'Present'),
        (2, 'Absent'),
        (3, 'Onsite'),
        (4, 'PL'), # Planned Leave
    )

    id = models.IntegerField(db_column='Id', primary_key=True)
    company_id = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', blank=True, null=True)
    planning_date = models.DateField(db_column='Date')
    employee = models.ForeignKey(OfficeStaff, models.DO_NOTHING, db_column='EmpId')
    work_order_no = models.CharField(db_column='WONo', max_length=255, blank=True, null=True)
    department = models.ForeignKey(BusinessGroup, models.DO_NOTHING, db_column='Dept', blank=True, null=True)
    planning_type = models.IntegerField(db_column='Types', choices=PLANNING_TYPES)
    # Add other fields as identified from the table if necessary, e.g., Description, Hours from commented code

    objects = ManPowerPlanningManager() # Custom manager for report data

    class Meta:
        managed = False
        db_table = 'tblPM_ManPowerPlanning'
        verbose_name = 'Manpower Planning'
        verbose_name_plural = 'Manpower Planning'
        ordering = ['planning_date']

    def __str__(self):
        return f"MPP {self.id} - {self.employee.employee_name} on {self.planning_date.strftime('%Y-%m-%d')}"

    def get_display_type(self):
        """Business logic for display type, moved to model."""
        return dict(self.PLANNING_TYPES).get(self.planning_type, "Unknown")
        
    # Additional business logic methods could go here if needed for report processing
    # For example, calculating total hours from details, etc.

class ManPowerPlanningDetail(models.Model):
    """Corresponds to tblPM_ManPowerPlanning_Details (Detail Data)."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    manpower_planning = models.ForeignKey(ManPowerPlanning, models.DO_NOTHING, db_column='MId', related_name='details')
    equipment = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='EquipId', blank=True, null=True)
    planned_description = models.CharField(db_column='PlannedDesc', max_length=500, blank=True, null=True)
    actual_description = models.CharField(db_column='ActualDesc', max_length=500, blank=True, null=True)
    hours = models.FloatField(db_column='Hour', blank=True, null=True)
    category = models.ForeignKey(BudgetHrsFieldCategory, models.DO_NOTHING, db_column='Category', blank=True, null=True)
    subcategory = models.ForeignKey(BudgetHrsFieldSubCategory, models.DO_NOTHING, db_column='SubCategory', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblPM_ManPowerPlanning_Details'
        verbose_name = 'Manpower Planning Detail'
        verbose_name_plural = 'Manpower Planning Details'

    def __str__(self):
        return f"Detail for MPP {self.manpower_planning.id} - {self.equipment.item_code if self.equipment else 'N/A'}"

```

#### 4.2 Forms (`project_management/forms.py`)

For the primary list view, a filter form is crucial to replicate the query string parameter filtering. For CRUD, a basic `ManPowerPlanningForm` is provided.

```python
from django import forms
from .models import ManPowerPlanning, OfficeStaff, BusinessGroup, ManPowerPlanningDetail

class ManPowerPlanningForm(forms.ModelForm):
    """Form for creating/updating ManPowerPlanning records."""
    class Meta:
        model = ManPowerPlanning
        fields = ['planning_date', 'employee', 'work_order_no', 'department', 'planning_type']
        widgets = {
            'planning_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'employee': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'work_order_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'department': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'planning_type': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }

class ManPowerPlanningReportFilterForm(forms.Form):
    """
    Form to capture filtering parameters for the Manpower Planning Report.
    Corresponds to the query string parameters (x, y, w, z, r, date, Key) from ASP.NET.
    """
    start_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label="From Date"
    )
    end_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label="To Date"
    )
    employee = forms.ModelChoiceField(
        queryset=OfficeStaff.objects.all(),
        required=False,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label="Employee"
    )
    department = forms.ModelChoiceField(
        queryset=BusinessGroup.objects.all(),
        required=False,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label="Department"
    )
    planning_type = forms.ChoiceField(
        choices=[('', 'All')] + list(ManPowerPlanning.PLANNING_TYPES),
        required=False,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label="Planning Type"
    )
    work_order_no = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label="Work Order No."
    )
    # The ASP.NET "Key" parameter storing the report document in session is no longer needed.
    # "CompId" and "FinYearId" from session will be accessed directly in the view.

```

#### 4.3 Views (`project_management/views.py`)

The views will handle the list display, including the "report" data aggregation, and basic CRUD operations. The `ManPowerPlanningReportListView` is the core replacement for the ASP.NET report page. A `ManPowerPlanningDetailPartialView` is added to demonstrate fetching nested details via HTMX.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404
from django.conf import settings # To access settings for Company ID, e.g., settings.CURRENT_COMPANY_ID
from .models import ManPowerPlanning, ManPowerPlanningDetail, Company # Import Company model
from .forms import ManPowerPlanningForm, ManPowerPlanningReportFilterForm
import json # For JSON response of details


class ManPowerPlanningReportListView(ListView):
    """
    Displays a list of ManPowerPlanning records, acting as the primary report view.
    Includes filtering capabilities and prepares data for DataTables.
    """
    model = ManPowerPlanning
    template_name = 'project_management/manpowerplanning/report_list.html'
    context_object_name = 'manpower_records'
    paginate_by = 10 # Example pagination, DataTables handles its own client-side pagination

    def get_queryset(self):
        # Placeholder for dynamic company/financial year. In a real app,
        # this would come from the logged-in user's profile or session.
        # Assuming a default company ID for now for demonstration.
        current_company_id = self.request.session.get('compid', 1) # Get from session, default to 1

        form = ManPowerPlanningReportFilterForm(self.request.GET)
        if form.is_valid():
            # Pass filters from form to the custom manager method
            queryset = ManPowerPlanning.objects.get_manpower_report_data(
                comp_id=current_company_id,
                start_date=form.cleaned_data.get('start_date'),
                end_date=form.cleaned_data.get('end_date'),
                emp_id=form.cleaned_data.get('employee'),
                work_order_no=form.cleaned_data.get('work_order_no'),
                dept_id=form.cleaned_data.get('department'),
                planning_type=form.cleaned_data.get('planning_type'),
            )
        else:
            # If form is not valid (e.g., initial load), fetch default data
            queryset = ManPowerPlanning.objects.get_manpower_report_data(comp_id=current_company_id)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['filter_form'] = ManPowerPlanningReportFilterForm(self.request.GET)
        context['page_title'] = "Manpower Planning Report"
        return context

class ManPowerPlanningTablePartialView(ManPowerPlanningReportListView):
    """
    HTMX-specific view to render only the table content for dynamic updates.
    Inherits filtering and queryset logic from ManPowerPlanningReportListView.
    """
    template_name = 'project_management/manpowerplanning/_report_table.html'

    def get(self, request, *args, **kwargs):
        # Ensure we are requesting only the table content
        if not request.headers.get('HX-Request'):
            return HttpResponse(status=400, content="Invalid request")
        return super().get(request, *args, **kwargs)

class ManPowerPlanningDetailPartialView(View):
    """
    HTMX-specific view to render details for a specific ManPowerPlanning record.
    Accessed when expanding a row in the DataTables.
    """
    def get(self, request, pk, *args, **kwargs):
        if not request.headers.get('HX-Request'):
            return HttpResponse(status=400, content="Invalid request")

        manpower_record = get_object_or_404(ManPowerPlanning, pk=pk)
        details = manpower_record.details.select_related(
            'equipment', 'category', 'subcategory'
        )

        # Prepare details in a serializable format
        details_data = []
        for detail in details:
            details_data.append({
                'equip_no': detail.equipment.item_code if detail.equipment else 'N/A',
                'description': detail.equipment.manufacturer_description if detail.equipment else 'N/A',
                'category': detail.category.category_name if detail.category else 'N/A',
                'subcategory': detail.subcategory.subcategory_name if detail.subcategory else 'N/A',
                'planned_desc': detail.planned_description or 'N/A',
                'actual_desc': detail.actual_description or 'N/A',
                'hours': detail.hours if detail.hours is not None else 0.0,
            })
        
        # Render a simple partial template for the details
        # For simplicity, returning JSON here. A template could be rendered instead.
        return JsonResponse({'details': details_data})
        # Alternatively, render to template:
        # from django.template.loader import render_to_string
        # html_content = render_to_string('project_management/manpowerplanning/_details_row.html', {'details': details})
        # return HttpResponse(html_content)


# --- CRUD Views for ManPowerPlanning (as per automation template) ---

class ManPowerPlanningCreateView(CreateView):
    model = ManPowerPlanning
    form_class = ManPowerPlanningForm
    template_name = 'project_management/manpowerplanning/form.html'
    success_url = reverse_lazy('manpower_planning_report') # Redirect to report list after action

    def form_valid(self, form):
        # In a real app, company_id should come from current user's context
        form.instance.company_id = self.request.session.get('compid', 1)
        response = super().form_valid(form)
        messages.success(self.request, 'Manpower Planning record added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, just signal HTMX to trigger
                headers={
                    'HX-Trigger': json.dumps({'refreshManPowerPlanningList': None, 'closeModal': None})
                }
            )
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = "Add Manpower Planning"
        return context

class ManPowerPlanningUpdateView(UpdateView):
    model = ManPowerPlanning
    form_class = ManPowerPlanningForm
    template_name = 'project_management/manpowerplanning/form.html'
    success_url = reverse_lazy('manpower_planning_report')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Manpower Planning record updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': json.dumps({'refreshManPowerPlanningList': None, 'closeModal': None})
                }
            )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = "Edit Manpower Planning"
        return context

class ManPowerPlanningDeleteView(DeleteView):
    model = ManPowerPlanning
    template_name = 'project_management/manpowerplanning/confirm_delete.html'
    success_url = reverse_lazy('manpower_planning_report')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Manpower Planning record deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': json.dumps({'refreshManPowerPlanningList': None, 'closeModal': None})
                }
            )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = "Delete Manpower Planning"
        return context
```

#### 4.4 Templates (`project_management/templates/project_management/manpowerplanning/`)

The templates are designed for HTMX interactions and DataTables integration, adhering to DRY principles by using partials.

**`report_list.html`** (Main report view, replaces ASPX page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div x-data="{ openModal: false }" class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">{{ page_title }}</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'manpower_planning_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            @click="openModal = true">
            Add New Record
        </button>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Filter Records</h3>
        <form hx-get="{% url 'manpower_planning_report_table' %}" hx-target="#manpower-report-table-container" hx-swap="innerHTML" hx-trigger="submit, change delay:300ms from:input, change from:select" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {% csrf_token %}
            {% for field in filter_form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
            </div>
            {% endfor %}
            <div class="md:col-span-2 lg:col-span-3 flex justify-end">
                <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded">
                    Apply Filters
                </button>
            </div>
        </form>
    </div>
    
    <div id="manpower-report-table-container"
         hx-trigger="load, refreshManPowerPlanningList from:body"
         hx-get="{% url 'manpower_planning_report_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Report Data...</p>
        </div>
    </div>
    
    <!-- Modal for form/delete -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 transition-opacity duration-300"
         :class="{ 'opacity-100 visible': openModal, 'opacity-0 invisible': !openModal }"
         x-cloak>
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4 transition-transform duration-300 transform"
             :class="{ 'scale-100': openModal, 'scale-95': !openModal }"
             @click.away="openModal = false">
             <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component for modal management
        Alpine.data('modalManager', () => ({
            openModal: false,
            init() {
                this.$watch('openModal', value => {
                    if (value) {
                        document.body.classList.add('overflow-hidden');
                    } else {
                        document.body.classList.remove('overflow-hidden');
                    }
                });
                document.body.addEventListener('closeModal', () => {
                    this.openModal = false;
                });
            }
        }));
    });
</script>
{% endblock %}
```

**`_report_table.html`** (Partial for DataTables content)

```html
<div class="overflow-x-auto bg-white shadow-md rounded-lg p-4">
    <table id="manpowerReportTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"></th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Designation</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No.</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for record in manpower_records %}
            <tr class="master-row">
                <td class="py-2 px-4 border-b border-gray-200 cursor-pointer control" 
                    hx-get="{% url 'manpower_planning_details_partial' record.pk %}"
                    hx-swap="afterend"
                    hx-target="closest tr"
                    onclick="toggleDetails(this)">
                    <i class="fa-solid fa-plus-circle text-green-500"></i>
                </td>
                <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ record.planning_date|date:"d-M-Y" }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ record.employee_name_full }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ record.designation_full }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ record.work_order_no_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ record.dept_symbol }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ record.planning_type_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                        hx-get="{% url 'manpower_planning_edit' record.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        @click="openModal = true">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                        hx-get="{% url 'manpower_planning_delete' record.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        @click="openModal = true">
                        Delete
                    </button>
                </td>
            </tr>
            <!-- Details row will be injected here via HTMX -->
            {% empty %}
            <tr>
                <td colspan="9" class="py-4 text-center text-gray-500">No manpower planning records found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#manpowerReportTable')) {
            $('#manpowerReportTable').DataTable().destroy();
        }
        $('#manpowerReportTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "searching": true,
            "ordering": true,
            "info": true,
            "paging": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 8] } // Disable ordering on expand/action columns
            ]
        });
    });

    function toggleDetails(element) {
        const row = element.closest('tr');
        const icon = element.querySelector('i');
        const isExpanded = icon.classList.contains('fa-minus-circle');

        if (isExpanded) {
            // Collapse: remove the next row (details row)
            if (row.nextElementSibling && row.nextElementSibling.classList.contains('details-row')) {
                row.nextElementSibling.remove();
            }
            icon.classList.remove('fa-minus-circle', 'text-red-500');
            icon.classList.add('fa-plus-circle', 'text-green-500');
        } else {
            // Expand: fetch details via HTMX
            htmx.trigger(element, 'hx-get'); // Trigger the hx-get on the element itself
            icon.classList.remove('fa-plus-circle', 'text-green-500');
            icon.classList.add('fa-minus-circle', 'text-red-500');
        }
    }

    // HTMX listener for after details are loaded
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'manpowerReportTable-container') {
            // Re-initialize DataTable after main table swap
            if ($.fn.DataTable.isDataTable('#manpowerReportTable')) {
                $('#manpowerReportTable').DataTable().destroy();
            }
            $('#manpowerReportTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "searching": true,
                "ordering": true,
                "info": true,
                "paging": true,
                "columnDefs": [
                    { "orderable": false, "targets": [0, 8] }
                ]
            });
        }
        // If a details row was just loaded, initialize DataTables for it
        if (evt.detail.request.url.includes('/details/')) { // Check if it's a details request
             const detailsRow = evt.detail.target.closest('tr').nextElementSibling;
             if (detailsRow && detailsRow.querySelector('.details-table')) {
                $(detailsRow.querySelector('.details-table')).DataTable({
                    "paging": false,
                    "searching": false,
                    "info": false,
                    "ordering": false
                });
             }
        }
    });
</script>
```

**`_details_row.html`** (Partial for injecting manpower planning details, referenced by `ManPowerPlanningDetailPartialView` in `views.py`)

*Note: The `ManPowerPlanningDetailPartialView` in `views.py` is currently returning JSON. To use this template, the view would need to render this template explicitly with the `details` context.*

```html
<tr class="details-row bg-gray-50">
    <td colspan="9" class="p-4 border-b border-gray-200">
        <h4 class="font-semibold text-gray-800 mb-3">Associated Details:</h4>
        {% if manpower_record.details.exists %}
        <table class="min-w-full divide-y divide-gray-200 details-table ml-8">
            <thead>
                <tr>
                    <th class="py-1 px-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Equip No.</th>
                    <th class="py-1 px-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    <th class="py-1 px-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                    <th class="py-1 px-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subcategory</th>
                    <th class="py-1 px-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Planned Desc</th>
                    <th class="py-1 px-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actual Desc</th>
                    <th class="py-1 px-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hours</th>
                </tr>
            </thead>
            <tbody>
                {% for detail in manpower_record.details.all %}
                <tr>
                    <td class="py-1 px-2 text-sm text-gray-700">{{ detail.equipment.item_code|default:'N/A' }}</td>
                    <td class="py-1 px-2 text-sm text-gray-700">{{ detail.equipment.manufacturer_description|default:'N/A' }}</td>
                    <td class="py-1 px-2 text-sm text-gray-700">{{ detail.category.category_name|default:'N/A' }}</td>
                    <td class="py-1 px-2 text-sm text-gray-700">{{ detail.subcategory.subcategory_name|default:'N/A' }}</td>
                    <td class="py-1 px-2 text-sm text-gray-700">{{ detail.planned_description|default:'N/A' }}</td>
                    <td class="py-1 px-2 text-sm text-gray-700">{{ detail.actual_description|default:'N/A' }}</td>
                    <td class="py-1 px-2 text-sm text-gray-700">{{ detail.hours|default:'0.0' }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <p class="text-sm text-gray-600 ml-8">No details found for this record.</p>
        {% endif %}
    </td>
</tr>
```

**`form.html`** (Partial for Add/Edit forms)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Manpower Planning Record</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-trigger="submit"
        hx-on--after-request="if(event.detail.successful) htmx.trigger(document.body, 'closeModal')">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                @click="openModal = false">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`confirm_delete.html`** (Partial for Delete confirmation)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="mb-4 text-gray-700">Are you sure you want to delete this Manpower Planning record for <strong>{{ object.employee_name_full }} on {{ object.planning_date|date:"d-M-Y" }}</strong>?</p>
    <form hx-post="{% url 'manpower_planning_delete' object.pk %}" hx-swap="none" hx-trigger="submit"
          hx-on--after-request="if(event.detail.successful) htmx.trigger(document.body, 'closeModal')">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                @click="openModal = false">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`project_management/urls.py`)

URL patterns for the report view and standard CRUD operations.

```python
from django.urls import path
from .views import (
    ManPowerPlanningReportListView, ManPowerPlanningTablePartialView, ManPowerPlanningDetailPartialView,
    ManPowerPlanningCreateView, ManPowerPlanningUpdateView, ManPowerPlanningDeleteView
)

urlpatterns = [
    # Main report view
    path('manpower-planning-report/', ManPowerPlanningReportListView.as_view(), name='manpower_planning_report'),
    # HTMX endpoint for filtering/refreshing the table content
    path('manpower-planning-report/table/', ManPowerPlanningTablePartialView.as_view(), name='manpower_planning_report_table'),
    # HTMX endpoint for loading individual record details
    path('manpower-planning-report/details/<int:pk>/', ManPowerPlanningDetailPartialView.as_view(), name='manpower_planning_details_partial'),

    # Standard CRUD operations for ManPowerPlanning
    path('manpower-planning/add/', ManPowerPlanningCreateView.as_view(), name='manpower_planning_add'),
    path('manpower-planning/edit/<int:pk>/', ManPowerPlanningUpdateView.as_view(), name='manpower_planning_edit'),
    path('manpower-planning/delete/<int:pk>/', ManPowerPlanningDeleteView.as_view(), name='manpower_planning_delete'),
]
```

#### 4.6 Tests (`project_management/tests.py`)

Comprehensive unit tests for models and integration tests for views, aiming for high coverage.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import date
from .models import (
    Company, BusinessGroup, Designation, OfficeStaff, ItemMaster,
    BudgetHrsFieldCategory, BudgetHrsFieldSubCategory, ManPowerPlanning, ManPowerPlanningDetail
)
import json

class ManPowerPlanningModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal required data for FKs
        cls.company = Company.objects.create(id=1, name='Test Company')
        cls.business_group = BusinessGroup.objects.create(id=1, symbol='PROJ')
        cls.designation = Designation.objects.create(id=1, type_name='Engineer', symbol='ENGR')
        cls.employee = OfficeStaff.objects.create(
            emp_id='EMP001',
            title='Mr',
            employee_name='John Doe',
            designation=cls.designation
        )
        cls.item_master = ItemMaster.objects.create(id=1, item_code='EQP001', manufacturer_description='Drill Machine')
        cls.budget_category = BudgetHrsFieldCategory.objects.create(id=1, category_name='Maintenance')
        cls.budget_subcategory = BudgetHrsFieldSubCategory.objects.create(
            id=1, subcategory_name='Daily Check', category=cls.budget_category
        )

        # Create test ManPowerPlanning records
        cls.mpp1 = ManPowerPlanning.objects.create(
            id=101,
            company_id=cls.company,
            planning_date=date(2023, 1, 15),
            employee=cls.employee,
            work_order_no='WO-2023-001',
            department=cls.business_group,
            planning_type=1 # Present
        )
        ManPowerPlanningDetail.objects.create(
            id=1,
            manpower_planning=cls.mpp1,
            equipment=cls.item_master,
            planned_description='Daily equipment inspection',
            actual_description='Completed',
            hours=8.0,
            category=cls.budget_category,
            subcategory=cls.budget_subcategory
        )
        cls.mpp2 = ManPowerPlanning.objects.create(
            id=102,
            company_id=cls.company,
            planning_date=date(2023, 1, 16),
            employee=cls.employee,
            work_order_no='', # Empty WO
            department=cls.business_group,
            planning_type=2 # Absent
        )
        cls.mpp3 = ManPowerPlanning.objects.create(
            id=103,
            company_id=cls.company,
            planning_date=date(2023, 1, 17),
            employee=cls.employee,
            work_order_no=None, # Null WO
            department=cls.business_group,
            planning_type=3 # Onsite
        )


    def test_manpower_planning_creation(self):
        self.assertEqual(ManPowerPlanning.objects.count(), 3)
        self.assertEqual(self.mpp1.employee.employee_name, 'John Doe')
        self.assertEqual(self.mpp1.planning_date, date(2023, 1, 15))

    def test_manpower_planning_detail_creation(self):
        detail = ManPowerPlanningDetail.objects.get(id=1)
        self.assertEqual(detail.manpower_planning, self.mpp1)
        self.assertEqual(detail.equipment.item_code, 'EQP001')
        self.assertEqual(detail.hours, 8.0)

    def test_get_display_type_method(self):
        self.assertEqual(self.mpp1.get_display_type(), 'Present')
        self.assertEqual(self.mpp2.get_display_type(), 'Absent')
        self.assertEqual(self.mpp3.get_display_type(), 'Onsite')

    def test_manpower_report_data_manager(self):
        # Test basic retrieval
        report_data = ManPowerPlanning.objects.get_manpower_report_data(self.company.id)
        self.assertEqual(report_data.count(), 3)

        # Test filtering by date
        filtered_data = ManPowerPlanning.objects.get_manpower_report_data(
            self.company.id, start_date=date(2023, 1, 16)
        )
        self.assertEqual(filtered_data.count(), 2)
        self.assertEqual(filtered_data.first().id, self.mpp2.id)

        # Test filtering by employee
        filtered_data = ManPowerPlanning.objects.get_manpower_report_data(
            self.company.id, emp_id=self.employee.emp_id
        )
        self.assertEqual(filtered_data.count(), 3)

        # Test annotations (derived fields)
        record1 = report_data.get(id=self.mpp1.id)
        self.assertEqual(record1.employee_name_full, 'Mr. John Doe')
        self.assertEqual(record1.designation_full, 'Engineer')
        self.assertEqual(record1.dept_symbol, 'PROJ')
        self.assertEqual(record1.planning_type_display, 'Present')
        self.assertEqual(record1.work_order_no_display, 'WO-2023-001')

        record2 = report_data.get(id=self.mpp2.id)
        self.assertEqual(record2.work_order_no_display, 'NA') # Test empty WO
        record3 = report_data.get(id=self.mpp3.id)
        self.assertEqual(record3.work_order_no_display, 'NA') # Test null WO

        # Test prefetching of details
        record_with_details = report_data.get(id=self.mpp1.id)
        self.assertGreater(record_with_details.details.count(), 0)
        self.assertEqual(record_with_details.details.first().hours, 8.0)


class ManPowerPlanningViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal required data for FKs
        cls.company = Company.objects.create(id=1, name='Test Company')
        cls.business_group = BusinessGroup.objects.create(id=1, symbol='PROJ')
        cls.designation = Designation.objects.create(id=1, type_name='Engineer', symbol='ENGR')
        cls.employee = OfficeStaff.objects.create(
            emp_id='EMP001',
            title='Mr',
            employee_name='John Doe',
            designation=cls.designation
        )
        cls.employee2 = OfficeStaff.objects.create(
            emp_id='EMP002',
            title='Ms',
            employee_name='Jane Smith',
            designation=cls.designation
        )
        cls.item_master = ItemMaster.objects.create(id=1, item_code='EQP001', manufacturer_description='Drill Machine')
        cls.budget_category = BudgetHrsFieldCategory.objects.create(id=1, category_name='Maintenance')
        cls.budget_subcategory = BudgetHrsFieldSubCategory.objects.create(
            id=1, subcategory_name='Daily Check', category=cls.budget_category
        )

        cls.mpp1 = ManPowerPlanning.objects.create(
            id=101, company_id=cls.company, planning_date=date(2023, 1, 15),
            employee=cls.employee, work_order_no='WO-2023-001',
            department=cls.business_group, planning_type=1 # Present
        )
        cls.mpp2 = ManPowerPlanning.objects.create(
            id=102, company_id=cls.company, planning_date=date(2023, 1, 16),
            employee=cls.employee2, work_order_no='WO-2023-002',
            department=cls.business_group, planning_type=2 # Absent
        )
        ManPowerPlanningDetail.objects.create(
            id=1, manpower_planning=cls.mpp1, equipment=cls.item_master,
            planned_description='Inspection', actual_description='Done', hours=4.0,
            category=cls.budget_category, subcategory=cls.budget_subcategory
        )
        ManPowerPlanningDetail.objects.create(
            id=2, manpower_planning=cls.mpp1, equipment=cls.item_master,
            planned_description='Cleaning', actual_description='Done', hours=2.0,
            category=cls.budget_category, subcategory=cls.budget_subcategory
        )
        
    def setUp(self):
        self.client = Client()
        self.client.session['compid'] = self.company.id # Simulate session company ID

    def test_report_list_view_get(self):
        response = self.client.get(reverse('manpower_planning_report'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/manpowerplanning/report_list.html')
        self.assertTrue('manpower_records' in response.context)
        self.assertEqual(response.context['manpower_records'].count(), 2) # Both MPP records exist

    def test_report_list_view_htmx_table_partial(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('manpower_planning_report_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/manpowerplanning/_report_table.html')
        self.assertTrue('manpower_records' in response.context)

    def test_report_list_view_filtering(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('manpower_planning_report_table') + f'?employee={self.employee.pk}', **headers)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['manpower_records'].count(), 1)
        self.assertEqual(response.context['manpower_records'].first().employee, self.employee)

        response = self.client.get(reverse('manpower_planning_report_table') + f'?start_date=2023-01-16', **headers)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['manpower_records'].count(), 1)
        self.assertEqual(response.context['manpower_records'].first().id, self.mpp2.id)

    def test_manpower_planning_details_partial_view(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('manpower_planning_details_partial', args=[self.mpp1.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertTrue('details' in data)
        self.assertEqual(len(data['details']), 2)
        self.assertEqual(data['details'][0]['hours'], 4.0)


    def test_create_view_get(self):
        response = self.client.get(reverse('manpower_planning_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/manpowerplanning/form.html')
        self.assertTrue('form' in response.context)
        
    def test_create_view_post_success(self):
        new_employee = OfficeStaff.objects.create(emp_id='EMP003', employee_name='Bob', designation=self.designation)
        data = {
            'planning_date': '2023-02-01',
            'employee': new_employee.emp_id,
            'work_order_no': 'WO-2023-003',
            'department': self.business_group.id,
            'planning_type': 4 # PL
        }
        response = self.client.post(reverse('manpower_planning_add'), data)
        self.assertEqual(response.status_code, 302) # Redirect on successful POST
        self.assertTrue(ManPowerPlanning.objects.filter(employee=new_employee, planning_date='2023-02-01').exists())
        
        # Test HTMX success response
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('manpower_planning_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # No Content
        self.assertTrue('HX-Trigger' in response.headers)
        trigger = json.loads(response.headers['HX-Trigger'])
        self.assertIn('refreshManPowerPlanningList', trigger)
        self.assertIn('closeModal', trigger)

    def test_update_view_get(self):
        response = self.client.get(reverse('manpower_planning_edit', args=[self.mpp1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/manpowerplanning/form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, self.mpp1)
        
    def test_update_view_post_success(self):
        data = {
            'planning_date': '2023-01-15', # Same date
            'employee': self.employee.emp_id, # Same employee
            'work_order_no': 'WO-UPDATED', # Changed
            'department': self.business_group.id,
            'planning_type': 1
        }
        response = self.client.post(reverse('manpower_planning_edit', args=[self.mpp1.pk]), data)
        self.assertEqual(response.status_code, 302)
        self.mpp1.refresh_from_db()
        self.assertEqual(self.mpp1.work_order_no, 'WO-UPDATED')

        # Test HTMX success response
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('manpower_planning_edit', args=[self.mpp1.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertTrue('HX-Trigger' in response.headers)

    def test_delete_view_get(self):
        response = self.client.get(reverse('manpower_planning_delete', args=[self.mpp1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/manpowerplanning/confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'], self.mpp1)
        
    def test_delete_view_post_success(self):
        mpp_count_before = ManPowerPlanning.objects.count()
        response = self.client.post(reverse('manpower_planning_delete', args=[self.mpp1.pk]))
        self.assertEqual(response.status_code, 302)
        self.assertEqual(ManPowerPlanning.objects.count(), mpp_count_before - 1)
        self.assertFalse(ManPowerPlanning.objects.filter(pk=self.mpp1.pk).exists())

        # Test HTMX success response
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('manpower_planning_delete', args=[self.mpp2.pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertTrue('HX-Trigger' in response.headers)

```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for dynamic filtering and table refresh:** The `report_list.html` uses `hx-get` on the filter form to target the `manpower-report-table-container` for `innerHTML` swap. This means when filters are applied, only the table content reloads, not the entire page.
*   **HTMX for CRUD forms:** "Add New Record", "Edit", and "Delete" buttons use `hx-get` to fetch their respective forms (`form.html` or `confirm_delete.html`) into the `#modalContent` div. Form submissions use `hx-post` with `hx-swap="none"` and `hx-on--after-request` to trigger custom events (`refreshManPowerPlanningList`, `closeModal`) after successful operations.
*   **HTMX for Master-Detail expansion:** The `_report_table.html` includes a `hx-get` on the expand icon (`.control` class) to fetch details for a specific record via `manpower_planning_details_partial` URL. The `htmx:afterSwap` event listener dynamically initializes DataTables on the main table after a filter/refresh and also on newly loaded detail tables.
*   **Alpine.js for Modal Management:** The `report_list.html` uses Alpine.js (`x-data="{ openModal: false }"`) to control the visibility and behavior of the modal, ensuring it appears and disappears smoothly and correctly handles clicks outside the modal to close it. `HX-Trigger` combined with Alpine's `@click` on buttons allows seamless integration.
*   **DataTables:** The `_report_table.html` uses jQuery DataTables for enhanced client-side table functionality (searching, sorting, pagination). The JavaScript for DataTables is within a `<script>` block in the partial template, ensuring it's re-initialized whenever the table content is loaded or refreshed via HTMX. The nested detail table also gets a simplified DataTables initialization.
*   **Dry Template Inheritance:** All templates extend `core/base.html` (which is assumed to exist and contain shared HTML structure, CDN links for Tailwind CSS, HTMX, Alpine.js, and jQuery/DataTables). This promotes code reuse and maintainability.

### Final Notes

*   **Placeholders:** Replace `[TABLE_NAME]`, `[MODEL_NAME]`, `[FIELD_NAME]`, etc., with actual values during the automated conversion. For this specific output, I've used concrete names based on the ASP.NET code.
*   **Company/Financial Year Context:** The ASP.NET code used `Session["compid"]` and `Session["finyear"]`. In Django, `request.session` can be used as a direct replacement. For a more robust enterprise application, these would typically be tied to the authenticated user's profile or a middleware that sets the current context. For this plan, `self.request.session.get('compid', 1)` is used as a placeholder.
*   **Error Handling:** The original C# `try-catch` block is very generic. In Django, specific exceptions should be caught and handled gracefully, providing meaningful feedback to the user, either through Django's messages framework or specific HTMX error handling.
*   **Security:** Input validation and parameter sanitization (implicitly handled by Django Forms and ORM) are crucial. Session management in Django is built-in and secure.
*   **Report Export:** If PDF/Excel export of the report data is required, a separate view using libraries like `WeasyPrint` (for PDF) or `openpyxl` (for Excel) would be implemented, potentially triggered by a button on the `report_list.html` page. This is a common post-migration enhancement.
*   **Tailwind CSS:** All generated HTML includes Tailwind CSS classes for styling, ensuring a modern and consistent UI.