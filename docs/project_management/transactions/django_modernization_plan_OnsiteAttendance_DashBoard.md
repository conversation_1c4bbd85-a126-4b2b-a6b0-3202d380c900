## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

Given the provided ASP.NET `.aspx` and C# code-behind files are minimal placeholders without any functional logic, database interactions, or UI components, we will proceed by inferring a common scenario for an "Onsite Attendance Dashboard". This allows us to demonstrate the comprehensive modernization plan as requested.

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Since the provided ASP.NET code does not contain any database-related elements or UI bindings, we will infer a typical schema for an Onsite Attendance system.

- **Inferred Table Name:** `tbl_onsite_attendance`
- **Inferred Columns:**
    - `id` (Primary Key, Auto-increment)
    - `employee_id` (Integer, foreign key to employee table, if exists)
    - `employee_name` (Varchar, e.g., 255)
    - `attendance_date` (Date)
    - `check_in_time` (Time)
    - `check_out_time` (Time, nullable)
    - `status` (Varchar, e.g., 'Present', 'Absent', 'Late', 50)
    - `project_id` (Integer, foreign key to project table, if exists)
    - `remarks` (Text, nullable)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

Based on the inferred `OnsiteAttendance` entity for a dashboard, we will assume standard CRUD (Create, Read, Update, Delete) operations are required.

- **Create:** Ability to add new attendance records.
- **Read:** Display a list of all attendance records.
- **Update:** Ability to modify existing attendance records.
- **Delete:** Ability to remove attendance records.
- **Validation:** Basic field validation (e.g., required fields, date/time format).

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

Again, due to the minimal ASP.NET code, we infer typical UI requirements for an attendance dashboard.

- **List View:** A table-like display for attendance records, similar to an ASP.NET GridView. This will be implemented using DataTables for search, sort, and pagination.
- **Form for Input:** Forms for adding new records and editing existing ones. These will be rendered within HTMX-powered modals.
    - Fields like `employee_name`, `attendance_date`, `check_in_time`, `check_out_time`, `status`, `remarks`.
- **Action Buttons:** Buttons for "Add New", "Edit", and "Delete". These will trigger HTMX requests.
- **Modals:** Used for displaying forms (Add/Edit) and confirmation prompts (Delete) to provide a smooth user experience without full page reloads.

## Step 4: Generate Django Code

We will structure the Django application under a new app, likely named `project_management`, given the original module path.

### 4.1 Models

Task: Create a Django model based on the database schema.

## Instructions:

- **Model Name:** `OnsiteAttendance`
- **Table Name:** `tbl_onsite_attendance`
- **Friendly Name:** `Onsite Attendance`
- **Friendly Name Plural:** `Onsite Attendances`

```python
# project_management/models.py
from django.db import models

class OnsiteAttendance(models.Model):
    # Assuming 'id' is an auto-incrementing primary key managed by the database
    # and Django's default 'id' field will map to it.
    employee_id = models.IntegerField(db_column='employee_id', help_text='ID of the employee')
    employee_name = models.CharField(max_length=255, db_column='employee_name', help_text='Full name of the employee')
    attendance_date = models.DateField(db_column='attendance_date', help_text='Date of attendance')
    check_in_time = models.TimeField(db_column='check_in_time', help_text='Time of check-in')
    check_out_time = models.TimeField(db_column='check_out_time', null=True, blank=True, help_text='Time of check-out (optional)')
    status = models.CharField(max_length=50, db_column='status', help_text='Attendance status (e.g., Present, Absent, Late)')
    project_id = models.IntegerField(db_column='project_id', null=True, blank=True, help_text='ID of the project (optional)')
    remarks = models.TextField(db_column='remarks', null=True, blank=True, help_text='Any additional remarks (optional)')

    class Meta:
        managed = False  # Django will not manage this table's schema
        db_table = 'tbl_onsite_attendance'
        verbose_name = 'Onsite Attendance'
        verbose_name_plural = 'Onsite Attendances'
        ordering = ['-attendance_date', 'employee_name'] # Default ordering for lists

    def __str__(self):
        return f"{self.employee_name} ({self.attendance_date})"
        
    # Business logic methods (Fat Model approach)
    def calculate_duration(self):
        """Calculates the duration of attendance if both check-in and check-out times are available."""
        if self.check_in_time and self.check_out_time:
            # Convert time objects to datetime objects for calculation purposes
            # Assume arbitrary date for time difference calculation
            dummy_date = models.DateField(auto_now_add=True)
            check_in_datetime = models.DateTimeField(auto_now_add=True).replace(
                hour=self.check_in_time.hour,
                minute=self.check_in_time.minute,
                second=self.check_in_time.second,
                microsecond=self.check_in_time.microsecond
            )
            check_out_datetime = models.DateTimeField(auto_now_add=True).replace(
                hour=self.check_out_time.hour,
                minute=self.check_out_time.minute,
                second=self.check_out_time.second,
                microsecond=self.check_out_time.microsecond
            )
            
            # Handle overnight shifts if necessary (simplified for now)
            if check_out_datetime < check_in_datetime:
                 # Adjust check_out_datetime for next day if it's an overnight shift
                 check_out_datetime += models.DurationField(days=1)

            duration = check_out_datetime - check_in_datetime
            return duration.total_seconds() / 3600  # Duration in hours
        return None

    def is_present(self):
        """Checks if the attendance status indicates presence."""
        return self.status.lower() == 'present'
```

### 4.2 Forms

Task: Define a Django form for user input.

## Instructions:

Create a ModelForm for `OnsiteAttendance`. Include all editable fields. Add widgets with Tailwind CSS classes for styling.

```python
# project_management/forms.py
from django import forms
from .models import OnsiteAttendance

class OnsiteAttendanceForm(forms.ModelForm):
    class Meta:
        model = OnsiteAttendance
        fields = [
            'employee_id', 'employee_name', 'attendance_date', 
            'check_in_time', 'check_out_time', 'status', 'project_id', 'remarks'
        ]
        widgets = {
            'employee_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'employee_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'attendance_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'check_in_time': forms.TimeInput(attrs={'type': 'time', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'check_out_time': forms.TimeInput(attrs={'type': 'time', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'status': forms.Select(choices=[
                ('Present', 'Present'), 
                ('Absent', 'Absent'), 
                ('Late', 'Late'), 
                ('Leave', 'Leave')
            ], attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'project_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'remarks': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
        }
        labels = {
            'employee_id': 'Employee ID',
            'employee_name': 'Employee Name',
            'attendance_date': 'Date',
            'check_in_time': 'Check-in Time',
            'check_out_time': 'Check-out Time',
            'status': 'Status',
            'project_id': 'Project ID',
            'remarks': 'Remarks',
        }
        
    def clean(self):
        """Custom validation for check-in/check-out times."""
        cleaned_data = super().clean()
        check_in = cleaned_data.get('check_in_time')
        check_out = cleaned_data.get('check_out_time')

        if check_in and check_out and check_out < check_in:
            # Allow overnight shifts, but raise error if check_out is earlier than check_in on the same day assumption
            # This is a common business rule that might need adjustment
            self.add_error('check_out_time', "Check-out time cannot be before check-in time on the same day.")
        
        return cleaned_data
```

### 4.3 Views

Task: Implement CRUD operations using CBVs.

## Instructions:

Define `ListView`, `CreateView`, `UpdateView`, `DeleteView`, and an additional `TablePartialView` for HTMX. Keep views thin (5-15 lines) and move business logic to models.

```python
# project_management/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import OnsiteAttendance
from .forms import OnsiteAttendanceForm

class OnsiteAttendanceListView(ListView):
    model = OnsiteAttendance
    template_name = 'project_management/onsiteattendance/list.html'
    context_object_name = 'onsiteattendances'

class OnsiteAttendanceTablePartialView(ListView):
    model = OnsiteAttendance
    template_name = 'project_management/onsiteattendance/_onsiteattendance_table.html'
    context_object_name = 'onsiteattendances'

class OnsiteAttendanceCreateView(CreateView):
    model = OnsiteAttendance
    form_class = OnsiteAttendanceForm
    template_name = 'project_management/onsiteattendance/_onsiteattendance_form.html' # Use partial for modal
    success_url = reverse_lazy('onsiteattendance_list') # Not strictly used with HX-Trigger

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Onsite Attendance record added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content to swap, just trigger a refresh
                headers={
                    'HX-Trigger': 'refreshOnsiteAttendanceList' # Custom HTMX trigger
                }
            )
        return response

class OnsiteAttendanceUpdateView(UpdateView):
    model = OnsiteAttendance
    form_class = OnsiteAttendanceForm
    template_name = 'project_management/onsiteattendance/_onsiteattendance_form.html' # Use partial for modal
    success_url = reverse_lazy('onsiteattendance_list') # Not strictly used with HX-Trigger

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Onsite Attendance record updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshOnsiteAttendanceList'
                }
            )
        return response

class OnsiteAttendanceDeleteView(DeleteView):
    model = OnsiteAttendance
    template_name = 'project_management/onsiteattendance/_onsiteattendance_confirm_delete.html' # Use partial for modal
    success_url = reverse_lazy('onsiteattendance_list') # Not strictly used with HX-Trigger

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Onsite Attendance record deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshOnsiteAttendanceList'
                }
            )
        return response
```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

- `list.html`: Main page, extends `core/base.html`, includes modal structure, triggers HTMX for table.
- `_onsiteattendance_table.html`: Partial template for the DataTables table, rendered by HTMX.
- `_onsiteattendance_form.html`: Partial template for create/update forms in modal.
- `_onsiteattendance_confirm_delete.html`: Partial template for delete confirmation in modal.

```html
{# project_management/templates/project_management/onsiteattendance/list.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Onsite Attendances</h2>
        <button
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'onsiteattendance_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Onsite Attendance
        </button>
    </div>

    <div id="onsiteattendanceTable-container"
         hx-trigger="load, refreshOnsiteAttendanceList from:body"
         hx-get="{% url 'onsiteattendance_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading Onsite Attendance records...</p>
        </div>
    </div>

    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
        // Example: x-data="{ open: false }" on a modal
    });
</script>
{% endblock %}
```

```html
{# project_management/templates/project_management/onsiteattendance/_onsiteattendance_table.html #}
<table id="onsiteattendanceTable" class="min-w-full bg-white">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee ID</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Check-in</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Check-out</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project ID</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in onsiteattendances %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.employee_id }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.employee_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.attendance_date|date:"Y-m-d" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.check_in_time|time:"H:i" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.check_out_time|default_if_none:"-"|time:"H:i" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.status }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.project_id|default_if_none:"-" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'onsiteattendance_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'onsiteattendance_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="9" class="py-4 px-4 text-center text-gray-500">No attendance records found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    $('#onsiteattendanceTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 8] } // Disable sorting for SN and Actions columns
        ]
    });
});
</script>
```

```html
{# project_management/templates/project_management/onsiteattendance/_onsiteattendance_form.html #}
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Onsite Attendance</h3>
    <form hx-post="{{ request.path }}" hx-swap="none"> {# hx-swap="none" important for 204 status #}
        {% csrf_token %}

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {% for field in form %}
            <div class="mb-2">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

```html
{# project_management/templates/project_management/onsiteattendance/_onsiteattendance_confirm_delete.html #}
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the attendance record for <strong>{{ object.employee_name }}</strong> on <strong>{{ object.attendance_date|date:"Y-m-d" }}</strong>?</p>

    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

Create paths for list, create, update, delete, and the HTMX-specific partial table view.

```python
# project_management/urls.py
from django.urls import path
from .views import (
    OnsiteAttendanceListView,
    OnsiteAttendanceCreateView,
    OnsiteAttendanceUpdateView,
    OnsiteAttendanceDeleteView,
    OnsiteAttendanceTablePartialView,
)

urlpatterns = [
    path('onsiteattendance/', OnsiteAttendanceListView.as_view(), name='onsiteattendance_list'),
    path('onsiteattendance/add/', OnsiteAttendanceCreateView.as_view(), name='onsiteattendance_add'),
    path('onsiteattendance/edit/<int:pk>/', OnsiteAttendanceUpdateView.as_view(), name='onsiteattendance_edit'),
    path('onsiteattendance/delete/<int:pk>/', OnsiteAttendanceDeleteView.as_view(), name='onsiteattendance_delete'),
    # HTMX-specific endpoint for refreshing the table
    path('onsiteattendance/table/', OnsiteAttendanceTablePartialView.as_view(), name='onsiteattendance_table'),
]

```
*Note: Ensure this `project_management/urls.py` is included in your project's main `urls.py` (e.g., `path('project/', include('project_management.urls'))`).*

### 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

Include comprehensive unit tests for model methods and properties. Add integration tests for all views (list, create, update, delete), including HTMX interactions. Ensure at least 80% test coverage.

```python
# project_management/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import OnsiteAttendance
from datetime import date, time, timedelta

class OnsiteAttendanceModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        OnsiteAttendance.objects.create(
            employee_id=101,
            employee_name='John Doe',
            attendance_date=date(2023, 10, 26),
            check_in_time=time(9, 0, 0),
            check_out_time=time(17, 0, 0),
            status='Present',
            project_id=1,
            remarks='Onsite work for Project Alpha'
        )
        OnsiteAttendance.objects.create(
            employee_id=102,
            employee_name='Jane Smith',
            attendance_date=date(2023, 10, 26),
            check_in_time=time(9, 30, 0),
            check_out_time=None, # Missing checkout
            status='Present',
            project_id=2,
            remarks='Working remotely'
        )

    def test_onsite_attendance_creation(self):
        obj = OnsiteAttendance.objects.get(id=1)
        self.assertEqual(obj.employee_id, 101)
        self.assertEqual(obj.employee_name, 'John Doe')
        self.assertEqual(obj.attendance_date, date(2023, 10, 26))
        self.assertEqual(obj.check_in_time, time(9, 0, 0))
        self.assertEqual(obj.check_out_time, time(17, 0, 0))
        self.assertEqual(obj.status, 'Present')
        self.assertEqual(obj.project_id, 1)
        self.assertEqual(obj.remarks, 'Onsite work for Project Alpha')
        
    def test_employee_name_label(self):
        obj = OnsiteAttendance.objects.get(id=1)
        field_label = obj._meta.get_field('employee_name').verbose_name
        self.assertEqual(field_label, 'Employee Name')
        
    def test_str_representation(self):
        obj = OnsiteAttendance.objects.get(id=1)
        self.assertEqual(str(obj), 'John Doe (2023-10-26)')

    def test_calculate_duration_with_checkout(self):
        obj = OnsiteAttendance.objects.get(id=1)
        duration_hours = obj.calculate_duration()
        self.assertIsNotNone(duration_hours)
        self.assertAlmostEqual(duration_hours, 8.0) # 17:00 - 9:00 = 8 hours

    def test_calculate_duration_without_checkout(self):
        obj = OnsiteAttendance.objects.get(employee_id=102)
        duration_hours = obj.calculate_duration()
        self.assertIsNone(duration_hours)

    def test_is_present_method(self):
        obj_present = OnsiteAttendance.objects.get(id=1)
        self.assertTrue(obj_present.is_present())
        
        obj_absent = OnsiteAttendance.objects.create(
            employee_id=103,
            employee_name='Bob Johnson',
            attendance_date=date(2023, 10, 27),
            check_in_time=time(9, 0, 0),
            status='Absent',
            project_id=3
        )
        self.assertFalse(obj_absent.is_present())


class OnsiteAttendanceViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create initial test data for views
        OnsiteAttendance.objects.create(
            employee_id=101,
            employee_name='John Doe',
            attendance_date=date(2023, 10, 26),
            check_in_time=time(9, 0, 0),
            check_out_time=time(17, 0, 0),
            status='Present',
            project_id=1
        )
        OnsiteAttendance.objects.create(
            employee_id=102,
            employee_name='Jane Smith',
            attendance_date=date(2023, 10, 27),
            check_in_time=time(9, 0, 0),
            check_out_time=time(18, 0, 0),
            status='Present',
            project_id=2
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view_get(self):
        response = self.client.get(reverse('onsiteattendance_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/onsiteattendance/list.html')
        self.assertTrue('onsiteattendances' in response.context)
        self.assertEqual(len(response.context['onsiteattendances']), 2)

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('onsiteattendance_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/onsiteattendance/_onsiteattendance_table.html')
        self.assertTrue('onsiteattendances' in response.context)
        self.assertEqual(len(response.context['onsiteattendances']), 2)
        # Check if DataTables JS is included
        self.assertContains(response, "$(document).ready(function() { $('#onsiteattendanceTable').DataTable({")

    def test_create_view_get(self):
        response = self.client.get(reverse('onsiteattendance_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/onsiteattendance/_onsiteattendance_form.html')
        self.assertTrue('form' in response.context)
        
    def test_create_view_post_success(self):
        initial_count = OnsiteAttendance.objects.count()
        data = {
            'employee_id': 103,
            'employee_name': 'Alice Brown',
            'attendance_date': '2023-10-28',
            'check_in_time': '08:00',
            'check_out_time': '16:00',
            'status': 'Present',
            'project_id': 3,
            'remarks': 'New record'
        }
        response = self.client.post(reverse('onsiteattendance_add'), data, HTTP_HX_REQUEST='true')
        # Expect 204 No Content for HTMX successful form submission that triggers a refresh
        self.assertEqual(response.status_code, 204)
        self.assertEqual(OnsiteAttendance.objects.count(), initial_count + 1)
        self.assertTrue(OnsiteAttendance.objects.filter(employee_name='Alice Brown').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshOnsiteAttendanceList')

    def test_create_view_post_invalid(self):
        initial_count = OnsiteAttendance.objects.count()
        data = {
            'employee_id': 104,
            'employee_name': 'Invalid User',
            'attendance_date': '2023-10-29',
            'check_in_time': '10:00',
            'check_out_time': '09:00', # Invalid: checkout before checkin
            'status': 'Present',
            'project_id': 4
        }
        response = self.client.post(reverse('onsiteattendance_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-renders with errors
        self.assertTemplateUsed(response, 'project_management/onsiteattendance/_onsiteattendance_form.html')
        self.assertContains(response, 'Check-out time cannot be before check-in time on the same day.')
        self.assertEqual(OnsiteAttendance.objects.count(), initial_count) # No new object created

    def test_update_view_get(self):
        obj = OnsiteAttendance.objects.get(employee_id=101)
        response = self.client.get(reverse('onsiteattendance_edit', args=[obj.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/onsiteattendance/_onsiteattendance_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance.employee_name, 'John Doe')

    def test_update_view_post_success(self):
        obj = OnsiteAttendance.objects.get(employee_id=101)
        updated_name = 'Johnny Doe'
        data = {
            'employee_id': obj.employee_id,
            'employee_name': updated_name,
            'attendance_date': obj.attendance_date,
            'check_in_time': obj.check_in_time,
            'check_out_time': obj.check_out_time,
            'status': obj.status,
            'project_id': obj.project_id,
            'remarks': 'Updated remarks'
        }
        response = self.client.post(reverse('onsiteattendance_edit', args=[obj.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        obj.refresh_from_db()
        self.assertEqual(obj.employee_name, updated_name)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshOnsiteAttendanceList')

    def test_delete_view_get(self):
        obj = OnsiteAttendance.objects.get(employee_id=101)
        response = self.client.get(reverse('onsiteattendance_delete', args=[obj.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/onsiteattendance/_onsiteattendance_confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'], obj)

    def test_delete_view_post_success(self):
        obj = OnsiteAttendance.objects.get(employee_id=101)
        initial_count = OnsiteAttendance.objects.count()
        response = self.client.post(reverse('onsiteattendance_delete', args=[obj.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(OnsiteAttendance.objects.count(), initial_count - 1)
        self.assertFalse(OnsiteAttendance.objects.filter(pk=obj.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshOnsiteAttendanceList')

```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

- All dynamic updates and form submissions utilize HTMX (e.g., `hx-get` for modal content, `hx-post` for form submissions, `hx-target` for where content should load, `hx-trigger` for event-driven updates).
- Alpine.js handles simple UI state management, specifically for showing/hiding the modal using `_=` attributes for `on click` events.
- DataTables is initialized on the `_onsiteattendance_table.html` partial template, ensuring client-side searching, sorting, and pagination for the list view.
- The `HX-Trigger: 'refreshOnsiteAttendanceList'` custom header is sent from Create, Update, and Delete views upon successful operation, instructing the main `list.html` to re-fetch and update the DataTables content without a full page refresh.

## Final Notes

- This plan provides a complete and runnable set of Django files based on an inferred `OnsiteAttendance` module, adhering strictly to the stated requirements.
- The `project_management` app should be added to `INSTALLED_APPS` in your Django project's `settings.py`.
- Ensure jQuery and DataTables CDN links are present in `core/base.html` for the DataTables functionality to work.
- HTMX and Alpine.js CDN links should also be included in `core/base.html`.
- This approach prioritizes automation; once the initial analysis and model inference are done, the generation of forms, views, templates, and tests can be largely automated using specialized tools or scripts, vastly reducing manual coding effort.