## ASP.NET to Django Conversion Script: On-Site Attendance Management

This document outlines a comprehensive modernization plan to transition the legacy ASP.NET On-Site Attendance management module to a modern Django-based solution. Our approach emphasizes automation, clean architecture, and a superior user experience, moving away from fragmented ASP.NET Web Forms to a unified, scalable Django application using HTMX and Alpine.js.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Business Value:** This step ensures data integrity and a seamless transition, preventing data loss and establishing a clear data foundation for the new system. Automating this extraction minimizes human error and accelerates the initial setup.

**Analysis:**
The ASP.NET code interacts with at least two database tables:
1.  **`tblOnSiteAttendance_Master`**: This is the primary table for attendance details, identified by `GridView4.DataKeyNames="Id"` and update statements in `GridView4_RowUpdating`.
2.  **`BusinessGroup`**: Used for the dropdown list (`drpGroupF`) with `SqlDataBG`.
3.  **Inferred `Employee` table**: The `GetOnSiteEmp` stored procedure and `EmpName` / `Hours` fields suggest a linked `Employee` table, though not explicitly defined in the provided ASP.NET code. We will create a proxy model for it to represent the relationship.

**Identified Tables and Columns:**

*   **`tblOnSiteAttendance_Master`** (Primary Table, Aliased as `OnSiteAttendance` in Django)
    *   `Id` (Primary Key, Integer) -> `id`
    *   `Shift` (Integer/SmallInt, `0` for Day, `1` for Night) -> `shift_type`
    *   `Status` (Integer/SmallInt, `0` for Present, `1` for Absent) -> `status_type`
    *   `Onsite` (Text) -> `on_site_remarks`
    *   `FromTime` (Time) -> `from_time`
    *   `ToTime` (Time) -> `to_time`
    *   `UpSysDate` (Date) -> `updated_date`
    *   `UpSessionId` (String) -> `updated_by_session_id`
    *   `UpSysTime` (Time) -> `updated_time`
    *   `EmpName` (String, inferred from `GetOnSiteEmp` and `GridView4`) -> This field is likely a join, so we'll add an `employee_id` Foreign Key.
    *   `BG` (String/Integer, inferred from `GetOnSiteEmp` and `GridView4`) -> This field is likely a join, so we'll add a `business_group_id` Foreign Key.
    *   `OnSiteDate` (Date, inferred from `textChequeDate` and `GetOnSiteEmp`) -> `attendance_date`
    *   `Hours` (Decimal/Float, inferred from `GetOnSiteEmp` and `GridView4`) -> This is likely a read-only field from the joined `Employee` table. We'll access it via the `Employee` model.

*   **`BusinessGroup`** (Lookup Table)
    *   `Id` (Primary Key, Integer) -> `id`
    *   `Symbol` (String) -> `symbol`

*   **`Employee`** (Inferred Lookup Table)
    *   `Id` (Primary Key, Integer) -> `id`
    *   `EmpName` (String) -> `name`
    *   `Hours` (Decimal/Float, likely `shift_hours` or similar) -> `shift_hours`

### Step 2: Identify Backend Functionality

**Business Value:** This step ensures that all existing business logic is accurately translated and potentially improved in the new Django system. Automating this mapping guarantees functional parity and reduces manual analysis effort.

**Identified Operations:**
*   **Read (List & Filter):**
    *   Initial page load: `FillGrid(0)` fetches data.
    *   Search/Filter: `btnProceed_Click` calls `FillGrid` based on selected `Date` (`textChequeDate`) and `BG Group` (`drpGroupF`).
    *   Pagination: `GridView4_PageIndexChanging` re-fetches data for the new page.
    *   Data is retrieved via a stored procedure `GetOnSiteEmp` which filters by Business Group, Company ID, and OnSiteDate.
*   **Update (Inline Edit):**
    *   `GridView4_RowEditing`: Puts a row into edit mode, pre-populating fields (radio buttons, text boxes, time pickers) from the current data.
    *   `GridView4_RowUpdating`: Saves changes from the edited row back to `tblOnSiteAttendance_Master`.
        *   **Validation:** `FromTime Should Be Less than To Time` (time range validation).
        *   Updates fields: `Shift`, `Status`, `Onsite`, `FromTime`, `ToTime`, `UpSysDate`, `UpSessionId`, `UpSysTime`.
*   **Validation Logic:**
    *   Date format validation (`RegularExpressionValidator`).
    *   Required fields (`RequiredFieldValidator`).
    *   Business logic for date validity in `FillGrid`: allows current date, yesterday, or day before yesterday if it was a Sunday. This ensures attendance is recorded for valid recent periods.

### Step 3: Infer UI Components

**Business Value:** This step ensures a familiar yet modernized user interface. By inferring the components, we can automate the generation of HTML templates, leveraging modern web technologies like HTMX and Alpine.js for a more responsive and efficient user experience.

**Identified UI Components and Django Equivalents:**
*   **Date Selection (Search Filter):**
    *   `asp:TextBox ID="textChequeDate"` with `cc1:CalendarExtender`:
        *   **Django:** `forms.DateField` with `type="date"` HTML input, or a custom widget for a more robust date picker if needed, but standard `type="date"` is often sufficient and native.
        *   **HTMX/Alpine:** `type="date"` input for direct user input.
*   **BG Group Selection (Search Filter):**
    *   `asp:DropDownList ID="drpGroupF"`:
        *   **Django:** `forms.ModelChoiceField` for `BusinessGroup` model.
*   **Search Button:**
    *   `asp:Button ID="btnProceed"`:
        *   **Django:** HTML `<button type="submit">` within a form, with HTMX attributes (`hx-get`, `hx-target`, `hx-swap`).
*   **Attendance Data Grid:**
    *   `asp:GridView ID="GridView4"`:
        *   **Django:** HTML `<table>` structured for DataTables.
        *   **HTMX:** The entire table content will be loaded as a partial using `hx-get` on page load and `hx-trigger` for refreshes after CRUD.
        *   **DataTables:** JavaScript library for client-side pagination, sorting, and filtering.
*   **In-line Editing (within GridView):**
    *   `asp:TemplateField` for `Edit/Update/Cancel` buttons:
        *   **Django:** HTMX `hx-get` buttons in each row to load an edit form into a modal. `hx-post` for form submission.
    *   `asp:TextBox`, `asp:RadioButtonList`, `MKB:TimeSelector` in `EditItemTemplate`:
        *   **Django:** Standard Django form fields (`forms.CharField`, `forms.ChoiceField` with `RadioSelect`, `forms.TimeField`) rendered within a modal partial template.
*   **Alert Messages:**
    *   `ClientScript.RegisterStartupScript`:
        *   **Django:** Django `messages` framework for server-side messages, often displayed in base template. For HTMX-triggered alerts, custom HTMX headers (`HX-Trigger`) can be used to display toast notifications or Alpine.js controlled alerts.

### Step 4: Generate Django Code

**Business Value:** This step is the core of the automated migration, transforming legacy logic into modern, maintainable Django code. By providing complete, runnable components, it drastically reduces manual coding and testing time, accelerating deployment and improving code quality.

The Django application will be named `attendance`.

#### 4.1 Models (`attendance/models.py`)

**Business Value:** Centralizing database schema and business logic into 'fat models' ensures data consistency and reusability. It promotes a modular design that is easier to maintain and test, making future enhancements simpler and less prone to errors.

```python
from django.db import models
from datetime import date, time, timedelta
from django.utils import timezone
from django.core.exceptions import ValidationError

class BusinessGroup(models.Model):
    # This model maps to the existing BusinessGroup table
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=100) # Assuming max_length for Symbol

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

class Employee(models.Model):
    # This model is inferred from the ASP.NET code's use of EmpName and Hours.
    # It assumes an existing Employee table or view that OnSiteAttendance links to.
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='EmpName', max_length=255) # Assuming EmpName field
    shift_hours = models.DecimalField(db_column='Hours', max_digits=5, decimal_places=2, null=True, blank=True) # Assuming Hours field

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'Employee' # Placeholder, replace with actual Employee table name
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return self.name

class OnSiteAttendance(models.Model):
    # This model maps to the existing tblOnSiteAttendance_Master table.
    id = models.IntegerField(db_column='Id', primary_key=True)
    employee = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='EmployeeId', related_name='attendances', null=True) # Assuming EmployeeId FK
    business_group = models.ForeignKey(BusinessGroup, on_delete=models.DO_NOTHING, db_column='BusinessGroupId', related_name='attendances', null=True) # Assuming BusinessGroupId FK
    attendance_date = models.DateField(db_column='OnSiteDate')
    shift_type = models.SmallIntegerField(db_column='Shift', choices=[(0, 'Day'), (1, 'Night')])
    status_type = models.SmallIntegerField(db_column='Status', choices=[(0, 'Present'), (1, 'Absent')])
    on_site_remarks = models.TextField(db_column='Onsite', blank=True, null=True)
    from_time = models.TimeField(db_column='FromTime', null=True, blank=True)
    to_time = models.TimeField(db_column='ToTime', null=True, blank=True)
    updated_date = models.DateField(db_column='UpSysDate', auto_now=True)
    updated_by_session_id = models.CharField(db_column='UpSessionId', max_length=255, blank=True, null=True)
    updated_time = models.TimeField(db_column='UpSysTime', auto_now=True)

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblOnSiteAttendance_Master'
        verbose_name = 'On-Site Attendance'
        verbose_name_plural = 'On-Site Attendances'
        ordering = ['-attendance_date', 'employee__name']

    def __str__(self):
        return f"{self.employee.name if self.employee else 'N/A'} - {self.attendance_date} ({self.get_shift_type_display()})"

    @property
    def employee_name(self):
        return self.employee.name if self.employee else 'N/A'
        
    @property
    def bg_symbol(self):
        return self.business_group.symbol if self.business_group else 'N/A'

    @property
    def employee_shift_hours(self):
        return self.employee.shift_hours if self.employee else 0

    def clean(self):
        # Time validation: "From Time Should Be Less than To Time."
        if self.from_time and self.to_time and self.from_time >= self.to_time:
            raise ValidationError('From Time should be less than To Time.')
        super().clean() # Call the parent clean method to ensure other validations are performed

    def save(self, *args, **kwargs):
        self.full_clean() # Ensures model-level validation before saving
        # Populate session ID based on current user or a default value
        # In a real app, this would come from request.user or a dedicated system user
        # For demonstration, setting a placeholder.
        self.updated_by_session_id = 'SYSTEM' # Replace with actual session/user ID
        super().save(*args, **kwargs)

    @classmethod
    def is_valid_attendance_date(cls, check_date: date) -> bool:
        """
        Replicates the ASP.NET FillGrid date validation logic.
        Validates if the attendance date is within acceptable range:
        - Today or future (diff.Days >= 0)
        - Yesterday (diff.Days == -1)
        - Two days ago AND that date was a Sunday (diff.Days == -2 and dayOfWeek == Sunday)
        """
        sys_dt = timezone.localdate() # Current date
        entered_dt = check_date

        diff = entered_dt - sys_dt

        if diff.days >= -1: # Includes today, future, and yesterday
            return True
        elif diff.days == -2:
            # Check if entered_dt was a Sunday
            if entered_dt.weekday() == 6: # Monday is 0, Sunday is 6
                return True
        return False

    @classmethod
    def get_on_site_attendance_data(cls, attendance_date: date, business_group_id: int = 0, company_id: int = None):
        """
        Mimics the GetOnSiteEmp stored procedure logic.
        Filters attendance records based on date and optionally business group and company ID.
        Assumes company_id filtering would be part of a multi-tenancy setup or global scope.
        For this example, company_id is optional and not directly applied to attendance records
        unless the model or DB has a 'company_id' field.
        """
        if not cls.is_valid_attendance_date(attendance_date):
            return [] # Return empty if date is invalid as per ASP.NET logic

        queryset = cls.objects.select_related('employee', 'business_group').filter(attendance_date=attendance_date)

        if business_group_id and business_group_id != 0:
            queryset = queryset.filter(business_group__id=business_group_id)

        # Assuming 'company_id' filtering is handled externally or through a session-based approach
        # If 'CompId' maps to a field in tblOnSiteAttendance_Master, add:
        # if company_id:
        #     queryset = queryset.filter(company_id=company_id)

        # For the purpose of replicating the GridView output, we need to ensure
        # all fields (including inferred ones like EmpName, BG, Hours) are available.
        # This will return OnSiteAttendance objects, from which related employee/bg data can be accessed.
        return queryset

```

#### 4.2 Forms (`attendance/forms.py`)

**Business Value:** Django forms streamline user input validation and rendering, drastically reducing boilerplate code. This automation translates directly to fewer bugs, faster development, and a more robust application, while maintaining Tailwind CSS styling for a modern look.

```python
from django import forms
from .models import OnSiteAttendance, BusinessGroup
from django.utils import timezone
from django.core.exceptions import ValidationError

class OnSiteAttendanceSearchForm(forms.Form):
    """
    Form for searching/filtering On-Site Attendance records.
    Corresponds to textChequeDate and drpGroupF in ASP.NET.
    """
    attendance_date = forms.DateField(
        label='Select Date',
        initial=timezone.localdate(), # Default to today's date
        widget=forms.DateInput(attrs={
            'type': 'date', # HTML5 date input
            'class': 'box3 block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm w-full',
            'required': 'true' # Based on RequiredFieldValidator
        })
    )
    business_group = forms.ModelChoiceField(
        queryset=BusinessGroup.objects.all(),
        label='Select BG Group',
        required=False, # Allows searching without a specific group
        empty_label="All Groups",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'
        })
    )

    def clean_attendance_date(self):
        # Replicate ASP.NET date validation logic for search
        attendance_date = self.cleaned_data['attendance_date']
        if not OnSiteAttendance.is_valid_attendance_date(attendance_date):
            raise ValidationError("Invalid date! Attendance can only be entered for today, yesterday, or the day before if it was a Sunday.")
        return attendance_date


class OnSiteAttendanceForm(forms.ModelForm):
    """
    Form for creating/updating On-Site Attendance records.
    Corresponds to the GridView's EditItemTemplate fields.
    """
    # Custom fields to match ASP.NET radio buttons and ensure correct values
    shift_type = forms.ChoiceField(
        choices=OnSiteAttendance.shift_type.field.choices,
        widget=forms.RadioSelect(attrs={'class': 'inline-flex items-center space-x-4'}),
        initial=0 # Day is default (0)
    )
    status_type = forms.ChoiceField(
        choices=OnSiteAttendance.status_type.field.choices,
        widget=forms.RadioSelect(attrs={'class': 'inline-flex items-center space-x-4'}),
        initial=0 # Present is default (0)
    )

    class Meta:
        model = OnSiteAttendance
        fields = [
            'shift_type',
            'status_type',
            'on_site_remarks',
            'from_time',
            'to_time',
        ]
        widgets = {
            'on_site_remarks': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
                'rows': 3 # MultiLine equivalent
            }),
            'from_time': forms.TimeInput(attrs={
                'type': 'time', # HTML5 time input
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'
            }),
            'to_time': forms.TimeInput(attrs={
                'type': 'time', # HTML5 time input
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'
            }),
        }

    def clean(self):
        cleaned_data = super().clean()
        from_time = cleaned_data.get('from_time')
        to_time = cleaned_data.get('to_time')

        # Replicate "From Time Should Be Less than To Time." validation
        if from_time and to_time and from_time >= to_time:
            self.add_error('to_time', 'To Time must be after From Time.')
        
        # Ensure on_site_remarks is required if status is "Present" and shift is "On-Site" (if applicable)
        # The ASP.NET code had ReqtxtOnsite validator on txtOnsite, implying it's always required in edit.
        # So we ensure it's not empty if present.
        on_site_remarks = cleaned_data.get('on_site_remarks')
        if not on_site_remarks:
            self.add_error('on_site_remarks', 'This field is required.')

        return cleaned_data

```

#### 4.3 Views (`attendance/views.py`)

**Business Value:** Django Class-Based Views (CBVs) simplify complex web logic by abstracting common patterns. By keeping views thin and pushing logic to models, we improve code clarity, testability, and reusability. HTMX integration enables dynamic partial updates, eliminating full page reloads and delivering a much smoother, app-like user experience.

```python
from django.views.generic import TemplateView, ListView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import render
from .models import OnSiteAttendance, BusinessGroup, Employee
from .forms import OnSiteAttendanceSearchForm, OnSiteAttendanceForm
from django.db.models import Q # For complex queries
from django.utils import timezone # For current date

# This view is a placeholder for the initial data fetch and display
# The original ASP.NET had a single page handling search and the grid.
# This Django approach separates the main page from the HTMX-loaded table.
class OnSiteAttendanceMainView(TemplateView):
    template_name = 'attendance/on_site_attendance/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = OnSiteAttendanceSearchForm(initial={'attendance_date': timezone.localdate()})
        return context

class OnSiteAttendanceTablePartialView(ListView):
    """
    Renders the DataTables partial, to be loaded via HTMX.
    This replaces the ASP.NET GridView's data binding and pagination.
    """
    model = OnSiteAttendance
    template_name = 'attendance/on_site_attendance/_on_site_attendance_table.html'
    context_object_name = 'on_site_attendances'
    paginate_by = 17 # Matches ASP.NET PageSize

    def get_queryset(self):
        # Retrieve search parameters from GET request (simulating btnProceed_Click)
        search_date_str = self.request.GET.get('attendance_date')
        business_group_id = self.request.GET.get('business_group')

        # Default to today's date if not provided or invalid
        search_date = timezone.localdate()
        if search_date_str:
            try:
                search_date = timezone.datetime.strptime(search_date_str, '%Y-%m-%d').date()
            except ValueError:
                messages.error(self.request, "Invalid date format provided.")
                search_date = timezone.localdate() # Fallback to today

        # Replicate ASP.NET's FillGrid business logic for date validity
        if not OnSiteAttendance.is_valid_attendance_date(search_date):
            messages.error(self.request, "Invalid date! Attendance can only be entered for today, yesterday, or the day before if it was a Sunday.")
            return OnSiteAttendance.objects.none() # Return empty queryset

        # Mimic GetOnSiteEmp stored procedure with Django ORM
        queryset = OnSiteAttendance.objects.select_related('employee', 'business_group').filter(attendance_date=search_date)

        if business_group_id and business_group_id != '0': # ASP.NET uses 0 for default/all
            try:
                bg_id = int(business_group_id)
                queryset = queryset.filter(business_group__id=bg_id)
            except ValueError:
                pass # Ignore if invalid group ID

        # Order by a meaningful field, e.g., employee name or ID
        queryset = queryset.order_by('employee__name')
        
        if not queryset.exists():
            messages.info(self.request, "No data to display for the selected criteria.")

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Manually paginate the queryset for DataTables if not using server-side processing
        # DataTables usually handles client-side pagination, sorting, filtering automatically.
        # Django's ListView pagination is for server-side. For HTMX + DataTables, we just pass the full queryset
        # if DataTables handles pagination, or pass the paginated objects if Django does it.
        # Since DataTables is for client-side, we pass the full list from get_queryset without Django's ListView pagination.
        context[self.context_object_name] = self.get_queryset()
        return context

class OnSiteAttendanceUpdateView(UpdateView):
    """
    Handles editing of an existing OnSiteAttendance record.
    Corresponds to GridView4_RowEditing and GridView4_RowUpdating.
    Uses HTMX for partial updates.
    """
    model = OnSiteAttendance
    form_class = OnSiteAttendanceForm
    template_name = 'attendance/on_site_attendance/_on_site_attendance_form.html'
    
    # We don't set a success_url here because HTMX handles the redirection/UI update
    # The success_url will be handled by the HX-Trigger response.

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # Pass the attendance_date to the form for any date-related logic if needed.
        # Not strictly necessary for this specific form based on current logic, but good practice.
        return kwargs

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Attendance record updated successfully.')
        
        # This header tells HTMX to trigger a refresh event on the client side
        # which will cause the attendance table to reload its content.
        return HttpResponse(
            status=204, # No Content - tells HTMX not to swap anything
            headers={
                'HX-Trigger': 'refreshOnSiteAttendanceList'
            }
        )

    def form_invalid(self, form):
        # If form is invalid, render the form again within the modal content
        # HTMX will swap the modal content with the rendered form including errors
        messages.error(self.request, 'Please correct the errors in the form.')
        return render(self.request, self.template_name, {'form': form})

class OnSiteAttendanceDeleteView(DeleteView):
    """
    Handles deleting an OnSiteAttendance record.
    Uses HTMX for partial updates.
    """
    model = OnSiteAttendance
    template_name = 'attendance/on_site_attendance/_on_site_attendance_confirm_delete.html'
    
    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Attendance record deleted successfully.')
        return HttpResponse(
            status=204, # No Content
            headers={
                'HX-Trigger': 'refreshOnSiteAttendanceList'
            }
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['on_site_attendance'] = self.get_object() # Ensure object is available in template
        return context
```

#### 4.4 Templates (`attendance/templates/attendance/on_site_attendance/`)

**Business Value:** Automating template generation ensures a consistent, modern UI with dynamic interactions. HTMX eliminates full page reloads for common actions, dramatically improving user perception of speed and responsiveness. Alpine.js provides simple client-side state management for elements like modals, contributing to a fluid user experience without complex JavaScript frameworks.

**`list.html`** (Main page for On-Site Attendance)

```html
{% extends 'core/base.html' %}

{% block title %}On-Site Attendance Details{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-gradient-to-r from-blue-500 to-blue-700 text-white font-bold py-3 px-4 rounded-t-lg mb-4">
        <h1 class="text-xl">Attendance Onsite Details</h1>
        <p class="text-sm">For Date: <span id="displayAttendanceDate"></span></p>
    </div>

    <div class="bg-white shadow-lg rounded-lg p-6 mb-6">
        <form hx-get="{% url 'attendance:on_site_attendance_table' %}" 
              hx-target="#onSiteAttendanceTable-container"
              hx-swap="innerHTML"
              hx-indicator="#loadingIndicator"
              class="space-y-4">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-6 gap-4 items-end">
                <div class="md:col-span-2">
                    <label for="{{ search_form.attendance_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ search_form.attendance_date.label }} :
                    </label>
                    {{ search_form.attendance_date }}
                    {% if search_form.attendance_date.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ search_form.attendance_date.errors }}</p>
                    {% endif %}
                </div>
                <div class="md:col-span-2">
                    <label for="{{ search_form.business_group.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ search_form.business_group.label }} :
                    </label>
                    {{ search_form.business_group }}
                    {% if search_form.business_group.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ search_form.business_group.errors }}</p>
                    {% endif %}
                </div>
                <div class="md:col-span-2">
                    <button type="submit" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded shadow">
                        Search
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Loading Indicator -->
    <div id="loadingIndicator" class="htmx-indicator text-center py-4 hidden">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500"></div>
        <p class="mt-2 text-indigo-700">Loading attendance data...</p>
    </div>

    <!-- Attendance Table Container (HTMX will load content here) -->
    <div id="onSiteAttendanceTable-container"
         hx-trigger="load, refreshOnSiteAttendanceList from:body"
         hx-get="{% url 'attendance:on_site_attendance_table' %}"
         hx-target="#onSiteAttendanceTable-container"
         hx-swap="innerHTML">
        <!-- Initial content or loading spinner can go here -->
        <div class="text-center py-4">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-blue-700">Loading initial attendance data...</p>
        </div>
    </div>
    
    <!-- Modal for form (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .flex from me then add .hidden to me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const attendanceDateInput = document.getElementById('{{ search_form.attendance_date.id_for_label }}');
        const displayLabel = document.getElementById('displayAttendanceDate');

        // Function to format date as dd-MM-yyyy
        function formatDate(dateString) {
            if (!dateString) return '';
            const [year, month, day] = dateString.split('-');
            return `${day}-${month}-${year}`;
        }

        // Set initial display date
        displayLabel.textContent = formatDate(attendanceDateInput.value);

        // Update display date on input change
        attendanceDateInput.addEventListener('change', function() {
            displayLabel.textContent = formatDate(this.value);
        });

        // Event listener for HTMX afterSwap to re-initialize DataTables
        document.body.addEventListener('htmx:afterSwap', function(event) {
            if (event.detail.target.id === 'onSiteAttendanceTable-container') {
                // Destroy existing DataTable instance if it exists
                if ($.fn.DataTable.isDataTable('#onSiteAttendanceTable')) {
                    $('#onSiteAttendanceTable').DataTable().destroy();
                }
                // Initialize DataTables on the newly loaded table
                $('#onSiteAttendanceTable').DataTable({
                    "pageLength": 17, // Matches ASP.NET PageSize
                    "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
                    "responsive": true
                });
            }
        });

        // Alpine.js setup for modal (if not already handled by _ on click)
        document.body.addEventListener('htmx:afterOnLoad', function(event) {
            const modal = document.getElementById('modal');
            if (event.detail.elt.closest('#modalContent') && event.detail.xhr.status === 200) {
                modal.classList.remove('hidden');
                modal.classList.add('flex');
            }
        });
    });
</script>
{% endblock %}
```

**`_on_site_attendance_table.html`** (Partial for DataTables, loaded via HTMX)

```html
<div class="bg-white shadow-lg rounded-lg overflow-hidden">
    <table id="onSiteAttendanceTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name Of Employee</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BG Group</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Shift</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">If On Site</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Shift Hrs</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">From Time</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">To Time</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if on_site_attendances %}
                {% for attendance in on_site_attendances %}
                <tr>
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ attendance.employee_name }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ attendance.bg_symbol }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ attendance.get_shift_type_display }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ attendance.get_status_type_display }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ attendance.on_site_remarks|default:"-" }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ attendance.employee_shift_hours|default:"-" }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ attendance.from_time|date:"H:i"|default:"-" }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ attendance.to_time|date:"H:i"|default:"-" }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">
                        <button 
                            class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md text-xs mr-2"
                            hx-get="{% url 'attendance:on_site_attendance_edit' attendance.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .flex to #modal then remove .hidden from #modal">
                            Edit
                        </button>
                        <button 
                            class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md text-xs"
                            hx-get="{% url 'attendance:on_site_attendance_delete' attendance.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .flex to #modal then remove .hidden from #modal">
                            Delete
                        </button>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="10" class="py-4 text-center text-gray-500">No data to display !</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<!-- DataTables initialization script. This runs AFTER HTMX loads the table. -->
<script>
    // This script will be re-run whenever the partial is loaded via HTMX
    // The main list.html handles destroying and re-initializing the DataTable
    // in the htmx:afterSwap event. This script simply confirms the table exists.
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#onSiteAttendanceTable')) {
            $('#onSiteAttendanceTable').DataTable({
                "pageLength": 17, // Matches ASP.NET PageSize
                "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
                "responsive": true
            });
        }
    });
</script>
```

**`_on_site_attendance_form.html`** (Partial for Edit/Add Form, loaded via HTMX modal)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} On-Site Attendance</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-trigger="submit" hx-indicator="#form-indicator">
        {% csrf_token %}
        
        <div class="space-y-5">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Shift Type:</label>
                <div class="flex space-x-6">
                    {% for radio in form.shift_type %}
                        <div class="flex items-center">
                            {{ radio.tag }}
                            <label for="{{ radio.id_for_label }}" class="ml-2 text-sm font-medium text-gray-700">{{ radio.choice_label }}</label>
                        </div>
                    {% endfor %}
                </div>
                {% if form.shift_type.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.shift_type.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Status Type:</label>
                <div class="flex space-x-6">
                    {% for radio in form.status_type %}
                        <div class="flex items-center">
                            {{ radio.tag }}
                            <label for="{{ radio.id_for_label }}" class="ml-2 text-sm font-medium text-gray-700">{{ radio.choice_label }}</label>
                        </div>
                    {% endfor %}
                </div>
                {% if form.status_type.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.status_type.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.on_site_remarks.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.on_site_remarks.label }}
                </label>
                {{ form.on_site_remarks }}
                {% if form.on_site_remarks.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.on_site_remarks.errors }}</p>
                {% endif %}
            </div>

            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label for="{{ form.from_time.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.from_time.label }}
                    </label>
                    {{ form.from_time }}
                    {% if form.from_time.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.from_time.errors }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.to_time.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.to_time.label }}
                    </label>
                    {{ form.to_time }}
                    {% if form.to_time.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.to_time.errors }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-md shadow"
                _="on click remove .flex from #modal then add .hidden to #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md shadow">
                Save
            </button>
        </div>
        
        <!-- Loading Indicator for Form Submission -->
        <div id="form-indicator" class="htmx-indicator fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
            <p class="mt-2 text-white ml-3">Saving...</p>
        </div>
    </form>
</div>
```

**`_on_site_attendance_confirm_delete.html`** (Partial for Delete Confirmation, loaded via HTMX modal)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the attendance record for 
        <span class="font-medium">{{ on_site_attendance.employee_name }}</span> on 
        <span class="font-medium">{{ on_site_attendance.attendance_date|date:"F d, Y" }}</span> (Shift: {{ on_site_attendance.get_shift_type_display }})?
    </p>
    <form hx-post="{% url 'attendance:on_site_attendance_delete' on_site_attendance.pk %}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-md shadow"
                _="on click remove .flex from #modal then add .hidden to #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-md shadow">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`attendance/urls.py`)

**Business Value:** A well-defined URL structure makes the application navigable and SEO-friendly. By centralizing URL patterns, we ensure consistency and simplify the routing of requests to the correct views, crucial for an automation-driven deployment.

```python
from django.urls import path
from .views import (
    OnSiteAttendanceMainView,
    OnSiteAttendanceTablePartialView,
    OnSiteAttendanceUpdateView,
    OnSiteAttendanceDeleteView,
)

app_name = 'attendance' # Namespace for this app's URLs

urlpatterns = [
    # Main attendance list page
    path('on-site/', OnSiteAttendanceMainView.as_view(), name='on_site_attendance_list'),
    
    # HTMX partial for the attendance table (search results and dynamic updates)
    path('on-site/table/', OnSiteAttendanceTablePartialView.as_view(), name='on_site_attendance_table'),
    
    # HTMX partial for editing an attendance record
    path('on-site/edit/<int:pk>/', OnSiteAttendanceUpdateView.as_view(), name='on_site_attendance_edit'),
    
    # HTMX partial for confirming deletion of an attendance record
    path('on-site/delete/<int:pk>/', OnSiteAttendanceDeleteView.as_view(), name='on_site_attendance_delete'),
]
```

#### 4.6 Tests (`attendance/tests.py`)

**Business Value:** Comprehensive automated testing is critical for ensuring the reliability and correctness of the migrated application. By achieving high test coverage, we minimize regressions, validate business logic, and build confidence in the new system, allowing for faster iterations and continuous delivery.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import date, time, timedelta
from django.core.exceptions import ValidationError
from unittest.mock import patch

from .models import OnSiteAttendance, BusinessGroup, Employee
from .forms import OnSiteAttendanceSearchForm, OnSiteAttendanceForm

class OnSiteAttendanceModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.business_group_day = BusinessGroup.objects.create(id=101, symbol='Day Shift BG')
        cls.business_group_night = BusinessGroup.objects.create(id=102, symbol='Night Shift BG')
        
        cls.employee1 = Employee.objects.create(id=1, name='Alice Smith', shift_hours=8.0)
        cls.employee2 = Employee.objects.create(id=2, name='Bob Johnson', shift_hours=10.0)

        # Create an attendance record for testing
        cls.attendance1 = OnSiteAttendance.objects.create(
            id=1,
            employee=cls.employee1,
            business_group=cls.business_group_day,
            attendance_date=timezone.localdate() - timedelta(days=1), # Yesterday
            shift_type=0, # Day
            status_type=0, # Present
            on_site_remarks='Working on project Alpha',
            from_time=time(9, 0, 0),
            to_time=time(17, 0, 0),
            updated_date=timezone.localdate(),
            updated_by_session_id='test_user',
            updated_time=timezone.now().time()
        )
        cls.attendance2 = OnSiteAttendance.objects.create(
            id=2,
            employee=cls.employee2,
            business_group=cls.business_group_night,
            attendance_date=timezone.localdate(), # Today
            shift_type=1, # Night
            status_type=1, # Absent
            on_site_remarks='Sick Leave',
            from_time=time(22, 0, 0),
            to_time=time(6, 0, 0), # Invalid time for testing validation
            updated_date=timezone.localdate(),
            updated_by_session_id='test_user',
            updated_time=timezone.now().time()
        )

    def test_on_site_attendance_creation(self):
        obj = OnSiteAttendance.objects.get(id=1)
        self.assertEqual(obj.employee, self.employee1)
        self.assertEqual(obj.business_group, self.business_group_day)
        self.assertEqual(obj.attendance_date, timezone.localdate() - timedelta(days=1))
        self.assertEqual(obj.get_shift_type_display(), 'Day')
        self.assertEqual(obj.get_status_type_display(), 'Present')
        self.assertEqual(obj.on_site_remarks, 'Working on project Alpha')
        self.assertEqual(obj.from_time, time(9, 0, 0))
        self.assertEqual(obj.to_time, time(17, 0, 0))

    def test_employee_name_property(self):
        self.assertEqual(self.attendance1.employee_name, 'Alice Smith')
        
    def test_bg_symbol_property(self):
        self.assertEqual(self.attendance1.bg_symbol, 'Day Shift BG')

    def test_employee_shift_hours_property(self):
        self.assertEqual(self.attendance1.employee_shift_hours, 8.0)
    
    def test_time_validation_less_than(self):
        # Test valid time range
        obj = OnSiteAttendance(
            employee=self.employee1,
            attendance_date=timezone.localdate(),
            shift_type=0, status_type=0,
            from_time=time(9,0,0), to_time=time(17,0,0)
        )
        try:
            obj.clean()
        except ValidationError:
            self.fail("ValidationError raised unexpectedly for valid times.")

    def test_time_validation_from_greater_than_to(self):
        # Test invalid time range (from_time >= to_time)
        obj = OnSiteAttendance(
            employee=self.employee1,
            attendance_date=timezone.localdate(),
            shift_type=0, status_type=0,
            from_time=time(17,0,0), to_time=time(9,0,0)
        )
        with self.assertRaisesMessage(ValidationError, 'From Time should be less than To Time.'):
            obj.clean()

    def test_time_validation_from_equal_to_to(self):
        # Test invalid time range (from_time == to_time)
        obj = OnSiteAttendance(
            employee=self.employee1,
            attendance_date=timezone.localdate(),
            shift_type=0, status_type=0,
            from_time=time(9,0,0), to_time=time(9,0,0)
        )
        with self.assertRaisesMessage(ValidationError, 'From Time should be less than To Time.'):
            obj.clean()

    def test_is_valid_attendance_date_today(self):
        self.assertTrue(OnSiteAttendance.is_valid_attendance_date(timezone.localdate()))

    def test_is_valid_attendance_date_yesterday(self):
        self.assertTrue(OnSiteAttendance.is_valid_attendance_date(timezone.localdate() - timedelta(days=1)))

    @patch('django.utils.timezone.localdate')
    def test_is_valid_attendance_date_two_days_ago_sunday(self, mock_localdate):
        # Simulate current date as Monday, then check date 2 days ago (Sunday)
        mock_localdate.return_value = date(2023, 10, 23) # A Monday
        self.assertTrue(OnSiteAttendance.is_valid_attendance_date(date(2023, 10, 21))) # Saturday
        self.assertTrue(OnSiteAttendance.is_valid_attendance_date(date(2023, 10, 22))) # Sunday

    @patch('django.utils.timezone.localdate')
    def test_is_valid_attendance_date_two_days_ago_not_sunday(self, mock_localdate):
        # Simulate current date as Tuesday, then check date 2 days ago (Sunday is not 2 days ago)
        mock_localdate.return_value = date(2023, 10, 24) # A Tuesday
        self.assertFalse(OnSiteAttendance.is_valid_attendance_date(date(2023, 10, 22))) # Sunday was 2 days before Monday
        self.assertFalse(OnSiteAttendance.is_valid_attendance_date(date(2023, 10, 21))) # Friday was 3 days before Monday

    def test_is_valid_attendance_date_old_date(self):
        self.assertFalse(OnSiteAttendance.is_valid_attendance_date(timezone.localdate() - timedelta(days=5)))

    def test_get_on_site_attendance_data_valid_date(self):
        # Ensure a record exists for today
        OnSiteAttendance.objects.create(
            id=3, employee=self.employee1, business_group=self.business_group_day,
            attendance_date=timezone.localdate(), shift_type=0, status_type=0
        )
        data = OnSiteAttendance.get_on_site_attendance_data(timezone.localdate())
        self.assertGreater(len(data), 0)
        self.assertTrue(any(att.attendance_date == timezone.localdate() for att in data))

    def test_get_on_site_attendance_data_invalid_date(self):
        data = OnSiteAttendance.get_on_site_attendance_data(timezone.localdate() - timedelta(days=10))
        self.assertEqual(len(data), 0) # Should return empty list due to date validation

class OnSiteAttendanceFormsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.business_group = BusinessGroup.objects.create(id=1, symbol='General')

    def test_search_form_valid(self):
        today = timezone.localdate().isoformat()
        form = OnSiteAttendanceSearchForm(data={'attendance_date': today, 'business_group': self.business_group.id})
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['attendance_date'], timezone.localdate())
        self.assertEqual(form.cleaned_data['business_group'], self.business_group)

    def test_search_form_invalid_date_validation(self):
        old_date = (timezone.localdate() - timedelta(days=5)).isoformat()
        form = OnSiteAttendanceSearchForm(data={'attendance_date': old_date, 'business_group': self.business_group.id})
        self.assertFalse(form.is_valid())
        self.assertIn('attendance_date', form.errors)
        self.assertIn('Invalid date!', form.errors['attendance_date'][0])

    def test_attendance_form_valid(self):
        form = OnSiteAttendanceForm(data={
            'shift_type': '0', # Day
            'status_type': '0', # Present
            'on_site_remarks': 'Attended meeting',
            'from_time': '09:00',
            'to_time': '17:00'
        })
        self.assertTrue(form.is_valid(), form.errors.as_json())

    def test_attendance_form_invalid_time_range(self):
        form = OnSiteAttendanceForm(data={
            'shift_type': '0',
            'status_type': '0',
            'on_site_remarks': 'Remarks',
            'from_time': '17:00',
            'to_time': '09:00'
        })
        self.assertFalse(form.is_valid())
        self.assertIn('to_time', form.errors)
        self.assertIn('To Time must be after From Time.', form.errors['to_time'][0])

    def test_attendance_form_on_site_remarks_required(self):
        form = OnSiteAttendanceForm(data={
            'shift_type': '0',
            'status_type': '0',
            'on_site_remarks': '', # Empty
            'from_time': '09:00',
            'to_time': '17:00'
        })
        self.assertFalse(form.is_valid())
        self.assertIn('on_site_remarks', form.errors)
        self.assertIn('This field is required.', form.errors['on_site_remarks'][0])


class OnSiteAttendanceViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.business_group = BusinessGroup.objects.create(id=101, symbol='Testing BG')
        cls.employee = Employee.objects.create(id=1, name='Test Employee', shift_hours=8.0)
        
        # Create an attendance record for update/delete tests
        cls.attendance_obj = OnSiteAttendance.objects.create(
            id=1,
            employee=cls.employee,
            business_group=cls.business_group,
            attendance_date=timezone.localdate(),
            shift_type=0, # Day
            status_type=0, # Present
            on_site_remarks='Initial remarks',
            from_time=time(8, 0, 0),
            to_time=time(16, 0, 0),
            updated_date=timezone.localdate(),
            updated_by_session_id='setup_user',
            updated_time=timezone.now().time()
        )

    def setUp(self):
        self.client = Client()

    def test_main_list_view_get(self):
        response = self.client.get(reverse('attendance:on_site_attendance_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'attendance/on_site_attendance/list.html')
        self.assertIn('search_form', response.context)
        self.assertTrue(isinstance(response.context['search_form'], OnSiteAttendanceSearchForm))

    def test_table_partial_view_get_initial_load(self):
        # Simulate initial HTMX load
        response = self.client.get(reverse('attendance:on_site_attendance_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'attendance/on_site_attendance/_on_site_attendance_table.html')
        self.assertIn('on_site_attendances', response.context)
        # Check if the created attendance object is in the context
        self.assertTrue(self.attendance_obj in response.context['on_site_attendances'])

    def test_table_partial_view_get_search(self):
        # Simulate search with specific date and business group
        search_date = timezone.localdate().isoformat()
        response = self.client.get(
            reverse('attendance:on_site_attendance_table'),
            {'attendance_date': search_date, 'business_group': str(self.business_group.id)},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'attendance/on_site_attendance/_on_site_attendance_table.html')
        self.assertIn('on_site_attendances', response.context)
        self.assertTrue(self.attendance_obj in response.context['on_site_attendances'])
        
        # Test with a different date, expecting no results
        yesterday = (timezone.localdate() - timedelta(days=1)).isoformat()
        response = self.client.get(
            reverse('attendance:on_site_attendance_table'),
            {'attendance_date': yesterday, 'business_group': str(self.business_group.id)},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['on_site_attendances']), 0) # No attendance for yesterday initially setup.

    def test_table_partial_view_get_invalid_date(self):
        old_date = (timezone.localdate() - timedelta(days=5)).isoformat()
        response = self.client.get(
            reverse('attendance:on_site_attendance_table'),
            {'attendance_date': old_date},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['on_site_attendances']), 0)
        # Check for error message
        messages = list(response.context['messages'])
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Invalid date! Attendance can only be entered for today, yesterday, or the day before if it was a Sunday.")

    def test_update_view_get_htmx(self):
        response = self.client.get(
            reverse('attendance:on_site_attendance_edit', args=[self.attendance_obj.id]),
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'attendance/on_site_attendance/_on_site_attendance_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.attendance_obj)

    def test_update_view_post_valid_htmx(self):
        data = {
            'shift_type': '1', # Night
            'status_type': '1', # Absent
            'on_site_remarks': 'Updated remarks for project Beta',
            'from_time': '18:00',
            'to_time': '02:00' # This will be invalid, handled by form clean.
        }
        # Correcting to_time for valid test
        data['to_time'] = '23:00'

        response = self.client.post(
            reverse('attendance:on_site_attendance_edit', args=[self.attendance_obj.id]),
            data,
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshOnSiteAttendanceList')

        # Verify the object was updated in the database
        self.attendance_obj.refresh_from_db()
        self.assertEqual(self.attendance_obj.get_shift_type_display(), 'Night')
        self.assertEqual(self.attendance_obj.on_site_remarks, 'Updated remarks for project Beta')
        self.assertEqual(self.attendance_obj.from_time, time(18, 0, 0))
        self.assertEqual(self.attendance_obj.to_time, time(23, 0, 0))

    def test_update_view_post_invalid_htmx(self):
        data = {
            'shift_type': '1',
            'status_type': '1',
            'on_site_remarks': 'Invalid time test',
            'from_time': '18:00',
            'to_time': '09:00' # Invalid time
        }
        response = self.client.post(
            reverse('attendance:on_site_attendance_edit', args=[self.attendance_obj.id]),
            data,
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200) # Should render form again with errors
        self.assertTemplateUsed(response, 'attendance/on_site_attendance/_on_site_attendance_form.html')
        self.assertIn('form', response.context)
        self.assertFalse(response.context['form'].is_valid())
        self.assertIn('to_time', response.context['form'].errors)
        # Ensure HX-Trigger is NOT present on invalid form submission
        self.assertNotIn('HX-Trigger', response.headers)

    def test_delete_view_get_htmx(self):
        response = self.client.get(
            reverse('attendance:on_site_attendance_delete', args=[self.attendance_obj.id]),
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'attendance/on_site_attendance/_on_site_attendance_confirm_delete.html')
        self.assertIn('on_site_attendance', response.context)
        self.assertEqual(response.context['on_site_attendance'], self.attendance_obj)

    def test_delete_view_post_htmx(self):
        initial_count = OnSiteAttendance.objects.count()
        response = self.client.post(
            reverse('attendance:on_site_attendance_delete', args=[self.attendance_obj.id]),
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshOnSiteAttendanceList')

        # Verify the object was deleted from the database
        self.assertEqual(OnSiteAttendance.objects.count(), initial_count - 1)
        self.assertFalse(OnSiteAttendance.objects.filter(id=self.attendance_obj.id).exists())

```

### Step 5: HTMX and Alpine.js Integration

**Business Value:** This integration transforms the user experience from a traditional, page-reloading web application to a dynamic, single-page application (SPA) like feel without the complexity of a full JavaScript framework. This leads to faster interactions, reduced server load, and a more modern, intuitive interface for end-users, directly impacting productivity and satisfaction.

**Implementation Details:**

1.  **Main Page (`list.html`):**
    *   **Initial Load:** The `#onSiteAttendanceTable-container` `div` uses `hx-trigger="load"` and `hx-get="{% url 'attendance:on_site_attendance_table' %}"` to fetch the table content immediately when the page loads.
    *   **Search/Filter:** The search `form` uses `hx-get="{% url 'attendance:on_site_attendance_table' %}"` to submit its parameters to the `OnSiteAttendanceTablePartialView`. `hx-target` and `hx-swap` ensure only the table container is updated.
    *   **Refresh After CRUD:** `hx-trigger="refreshOnSiteAttendanceList from:body"` on the table container listens for a custom event. This event is sent as an `HX-Trigger` header from the `UpdateView` and `DeleteView` after successful operations. This ensures the table refreshes without a full page reload, displaying the latest data.
    *   **Modals (Alpine.js / HTMX):** The `#modal` div is the main modal container, initially `hidden`.
        *   Edit/Delete buttons in the table use `hx-get` to fetch the respective form/confirmation partials (`_on_site_attendance_form.html`, `_on_site_attendance_confirm_delete.html`) into `#modalContent`.
        *   `_="on click add .flex to #modal then remove .hidden from #modal"` (or similar Alpine.js `x-data` logic) is used to show the modal when the buttons are clicked.
        *   The "Cancel" button within the modal partials uses `_="on click remove .flex from #modal then add .hidden to #modal"` to close the modal.
        *   The modal also closes if the user clicks outside its content area (on the grey overlay).

2.  **Table Partial (`_on_site_attendance_table.html`):**
    *   **DataTables Initialization:** A `<script>` block within this partial handles the DataTables initialization for `#onSiteAttendanceTable`. This script will execute every time the partial is loaded by HTMX. The `list.html` has an `htmx:afterSwap` listener to ensure proper destruction and re-initialization of DataTables to avoid issues.
    *   **Edit/Delete Buttons:** Each row's "Edit" and "Delete" buttons use `hx-get` to dynamically load the form/confirmation partials into the main page's modal (`#modalContent`), enabling in-place editing.

3.  **Form Partial (`_on_site_attendance_form.html`):**
    *   **Form Submission:** The `<form>` uses `hx-post="{{ request.path }}"` to submit data back to the `UpdateView` (which serves this partial). `hx-swap="none"` prevents any immediate UI swap, as the view will return a `204 No Content` status with an `HX-Trigger` header to refresh the main table.
    *   **Validation Errors:** If the form submission is invalid, the `UpdateView` re-renders the form partial with errors. HTMX swaps the modal content with this new form, displaying the validation messages immediately to the user without leaving the modal.

4.  **Delete Confirmation Partial (`_on_site_attendance_confirm_delete.html`):**
    *   **Form Submission:** Similar to the form partial, `hx-post` is used for deletion, `hx-swap="none"`, and an `HX-Trigger` is sent back to refresh the main table after successful deletion.

**Key HTMX Attributes Used:**
*   `hx-get`: Fetches content via a GET request.
*   `hx-post`: Submits form data via a POST request.
*   `hx-target`: Specifies which element's content will be replaced.
*   `hx-swap`: Defines how the new content replaces the target (e.g., `innerHTML`, `none`).
*   `hx-trigger`: Specifies the event that triggers the HTMX request (e.g., `load`, `click`, `submit`, `refreshOnSiteAttendanceList`).
*   `htmx-indicator`: Shows a loading spinner during HTMX requests.

This combination provides a dynamic, responsive, and efficient user experience, closely mimicking single-page application behavior without the overhead of complex JavaScript frameworks.

---

### Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating the ASP.NET On-Site Attendance module to Django.

*   **Automation Focus:** The entire structure is designed for automated conversion. Tools can parse ASP.NET controls, infer database schema, and then generate these Django models, forms, views, URLs, and templates based on predefined patterns.
*   **Business Benefits:** The modernized application will offer:
    *   **Improved User Experience:** Faster interactions, no full page reloads, and a more intuitive interface due to HTMX/Alpine.js.
    *   **Enhanced Maintainability:** Clean separation of concerns (Fat Models, Thin Views), adherence to Django best practices, and comprehensive tests reduce technical debt.
    *   **Scalability:** Django's robust framework and ORM provide a solid foundation for future growth and higher user loads.
    *   **Cost Efficiency:** Reduced development and debugging time through automation and streamlined architecture.
    *   **Future-Proofing:** Moving from a legacy framework to a widely supported, open-source technology reduces vendor lock-in and ensures long-term viability.
*   **Next Steps:** Implement the generated code, ensure database connectivity is configured correctly in Django settings, and run the provided tests to confirm functionality and coverage. Further integrations with Django's authentication system and global context for `CompId`, `FinYearId`, and `sId` would be part of a broader enterprise integration plan.