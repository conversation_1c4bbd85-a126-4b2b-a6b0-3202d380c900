## ASP.NET to Django Conversion Script: Project Planning Module

This comprehensive modernization plan outlines the strategic transition of your legacy ASP.NET Project Planning module to a robust, modern Django 5.0+ application. Our approach prioritizes AI-assisted automation, ensuring a smooth, efficient, and maintainable outcome with minimal manual intervention. We focus on delivering business value through a scalable, high-performance solution that leverages the latest web technologies.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the ASP.NET code, we identify the following database tables and their implied structures. These tables will be mapped directly to Django models for seamless interaction with your existing data.

- **`SD_Cust_WorkOrder_Master`**: This table stores information about customer work orders.
    - **Inferred Columns**: `Id` (Primary Key), `FinYearId`, `WONo`, `TaskWorkOrderDate`, `TaskProjectTitle`, `CloseOpen`, `CompId`.
- **`tblSD_WO_Category`**: This table holds categories for work orders.
    - **Inferred Columns**: `CId` (Primary Key), `Symbol`, `CName`, `CompId`.
- **`tblFinancial_master`**: This table stores financial year details.
    - **Inferred Columns**: `FinYearId` (Primary Key), `FinYear`.
- **`tblPM_ProjectPlanning_Master`**: This table stores project planning documents/files associated with work orders.
    - **Inferred Columns**: `Id` (Primary Key), `SysDate`, `SysTime`, `CompId`, `FinYearId`, `SessionId`, `WONo`, `FileName`, `FileSize`, `ContentType`, `FileData` (Binary data of the file).

### Step 2: Identify Backend Functionality

The ASP.NET module primarily performs data retrieval, filtering, and file management.

**Read Operations (Data Display & Filtering):**
-   **Work Order List (`GridView2`):**
    -   Displays a paginated list of work orders.
    -   Filters by `WONo` (Work Order Number) from `txtWono`.
    -   Filters by `WO Category` from `DDLTaskWOType`.
    -   Filters by `FinYearId` and `CompId` (session-based).
    -   Orders by `WONo`.
-   **Project Planning Files List (`GridView3`):**
    -   Displays a paginated list of files associated with a *selected* Work Order (`WONo`).
    -   Filters by `WONo`, `FinYearId`, `CompId` (session-based).

**Create Operations (File Upload):**
-   New files can be added to `tblPM_ProjectPlanning_Master`.
-   The upload process captures file data, name, size, and content type.
-   Validation: Checks if a file is uploaded and `WONo` is present.

**Delete Operations (File Management):**
-   Files can be deleted from `tblPM_ProjectPlanning_Master` based on their `Id`.

**Download Operations (File Retrieval):**
-   Allows downloading of files by redirecting to a dedicated download handler.

### Step 3: Infer UI Components

The ASP.NET UI uses standard web forms controls which will be mapped to Django templates with HTMX and Tailwind CSS for a modern, dynamic, and responsive user experience.

-   **Input Fields**: `asp:TextBox` (`txtWono`) will become a simple HTML `<input type="text">`.
-   **Dropdowns**: `asp:DropDownList` (`DDLTaskWOType`) will become an HTML `<select>`.
-   **Buttons**: `asp:Button` (`btnSearch`, `btnFAdd`, `btnFAdd1`) and `asp:LinkButton` (`lbtnWONo`, `linkBtn`, `btnlnkImg`) will be HTML `<button>` or `<a>` elements with HTMX attributes for dynamic interactions.
-   **Data Grids**: `asp:GridView` (`GridView2`, `GridView3`) will be replaced by standard HTML `<table>` elements enhanced with DataTables.js for pagination, sorting, and client-side search.
-   **Panels**: `asp:Panel` (`Panel1`, `Panel2`) are structural containers. In Django, these will be `<div>` elements, often controlled by HTMX `hx-target` and `hx-swap` for conditional visibility and content updates.
-   **File Upload**: `asp:FileUpload` (`FileUpload1`, `FileUpload2`) will be an HTML `<input type="file">`.
-   **Messages**: Client-side alerts (`ClientScript.RegisterStartupScript`) will be replaced by Django's `messages` framework and potentially displayed dynamically using HTMX/Alpine.js.

---

### Step 4: Generate Django Code

We will structure the Django application as `project_management`.

#### 4.1 Models (`project_management/models.py`)

This section defines the Django models that directly map to your existing database tables. We use `managed = False` to ensure Django works with your existing schema without attempting to modify it, and `db_table` to specify the exact table names. Business logic for data retrieval and manipulation is encapsulated within these models.

```python
from django.db import models
from django.utils import timezone
import io

# Helper constants for session/company/financial year context.
# In a real application, these would typically be derived from the
# authenticated user's profile (e.g., request.user.company.id).
CURRENT_COMPANY_ID = 1
CURRENT_FINANCIAL_YEAR_ID = 1

class FinancialYear(models.Model):
    """
    Maps to tblFinancial_master.
    Stores financial year details.
    """
    finyearid = models.IntegerField(db_column='FinYearId', primary_key=True)
    finyear = models.CharField(db_column='FinYear', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.finyear

class WOCategory(models.Model):
    """
    Maps to tblSD_WO_Category.
    Stores Work Order categories.
    """
    cid = models.IntegerField(db_column='CId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)
    cname = models.CharField(db_column='CName', max_length=255)
    compid = models.IntegerField(db_column='CompId', default=CURRENT_COMPANY_ID)

    class Meta:
        managed = False
        db_table = 'tblSD_WO_Category'
        verbose_name = 'WO Category'
        verbose_name_plural = 'WO Categories'

    def __str__(self):
        return f"{self.symbol} - {self.cname}"

class WorkOrder(models.Model):
    """
    Maps to SD_Cust_WorkOrder_Master.
    Stores details of customer work orders.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    finyearid = models.IntegerField(db_column='FinYearId', default=CURRENT_FINANCIAL_YEAR_ID)
    wono = models.CharField(db_column='WONo', max_length=50)
    taskworkorderdate = models.DateTimeField(db_column='TaskWorkOrderDate')
    taskprojecttitle = models.CharField(db_column='TaskProjectTitle', max_length=500)
    closeopen = models.IntegerField(db_column='CloseOpen', default=0) # 0 for open, 1 for closed
    compid = models.IntegerField(db_column='CompId', default=CURRENT_COMPANY_ID)

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.wono

    @property
    def formatted_date(self):
        return self.taskworkorderdate.strftime('%d-%m-%Y')

    @property
    def financial_year(self):
        try:
            return FinancialYear.objects.get(finyearid=self.finyearid).finyear
        except FinancialYear.DoesNotExist:
            return 'N/A'

    @classmethod
    def filter_work_orders(cls, wono_filter=None, category_id=None):
        """
        Retrieves work orders based on provided filters,
        mimicking the FillData method.
        """
        qs = cls.objects.filter(
            finyearid__lte=CURRENT_FINANCIAL_YEAR_ID,
            closeopen=0,
            compid=CURRENT_COMPANY_ID
        ).order_by('wono')

        if wono_filter:
            qs = qs.filter(wono__iexact=wono_filter) # Use iexact for case-insensitive match

        if category_id and category_id != 'WO Category': # Assuming 'WO Category' is the default
            # Assuming a CId column exists in WorkOrder table or there's a join.
            # If CId is in WorkOrder table:
            # qs = qs.filter(cid=category_id)
            # Otherwise, a more complex join or pre-filtering might be needed.
            # For this example, assuming CId on WorkOrder for simplicity, if not then
            # an intermediary table/FK needs to be established.
            # Based on the ASP.NET code, CId is filtered on SD_Cust_WorkOrder_Master,
            # which implies a direct relationship or a custom `fun.select` handles the join.
            # If CId is not directly on WorkOrder, this would require custom SQL or a specific
            # FK relationship if it's implicitly part of the WorkOrder definition.
            # Let's assume there is a 'CId' foreign key for now if the DB schema supports it.
            # If not, this is a point for review during the DB schema analysis.
            # For now, I'll add a dummy cid field to the model for demonstration if needed,
            # or remove the filter if the DB doesn't support it directly on WorkOrder.
            # Re-evaluating: The ASP.NET `fun.select("*", "SD_Cust_WorkOrder_Master", ... + Z + "Order by WONo ASC");`
            # and `Z = " AND CId='" + Convert.ToInt32(DDLTaskWOType.SelectedValue) + "'";` implies `CId` is a direct
            # column on `SD_Cust_WorkOrder_Master`. We need to add it to the model.
            qs = qs.filter(cid=category_id)

        # Annotate with financial year string for display
        # This approach avoids repeated DB queries in template, but assumes FinYear mapping
        # If FinYear is complex, it might need a separate model method or select_related
        # For this example, we'll fetch FinYear in a property for simplicity, or prefetch for performance.
        # Adding a dummy cid field to the WorkOrder model temporarily for filter to work:
        # In a real migration, if `CId` is not in `SD_Cust_WorkOrder_Master`, it would indicate
        # a more complex join or a missing FK. I will add it as an IntegerField.
        pass # The actual `cid` field will be added below.

    # Adding cid based on ASP.NET analysis
    cid = models.IntegerField(db_column='CId', null=True, blank=True) # Added based on ASP.NET code for DDLTaskWOType filter

class ProjectPlanningFile(models.Model):
    """
    Maps to tblPM_ProjectPlanning_Master.
    Stores project planning files/documents.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    sysdate = models.DateField(db_column='SysDate')
    systime = models.TimeField(db_column='SysTime')
    compid = models.IntegerField(db_column='CompId', default=CURRENT_COMPANY_ID)
    finyearid = models.IntegerField(db_column='FinYearId', default=CURRENT_FINANCIAL_YEAR_ID)
    sessionid = models.CharField(db_column='SessionId', max_length=255)
    wono = models.CharField(db_column='WONo', max_length=50)
    filename = models.CharField(db_column='FileName', max_length=255)
    filesize = models.IntegerField(db_column='FileSize')
    contenttype = models.CharField(db_column='ContentType', max_length=255)
    filedata = models.BinaryField(db_column='FileData') # For storing binary file data directly in DB

    class Meta:
        managed = False
        db_table = 'tblPM_ProjectPlanning_Master'
        verbose_name = 'Project Planning File'
        verbose_name_plural = 'Project Planning Files'

    def __str__(self):
        return self.filename

    @property
    def formatted_date(self):
        return self.sysdate.strftime('%d-%m-%Y')

    @classmethod
    def get_files_for_work_order(cls, wono_val):
        """
        Retrieves project planning files for a specific work order,
        mimicking FillDataUpLoad.
        """
        return cls.objects.filter(
            finyearid=CURRENT_FINANCIAL_YEAR_ID,
            compid=CURRENT_COMPANY_ID,
            wono=wono_val
        ).order_by('-id')

    @classmethod
    def upload_file(cls, wono, uploaded_file, session_id):
        """
        Handles the logic for uploading and saving a file.
        Mimics the "Add" command in GridView3.
        """
        if not uploaded_file:
            raise ValueError("No file uploaded.")

        if not wono:
            raise ValueError("Work Order Number is required.")

        # Read file data into memory
        file_data = uploaded_file.read()

        # Create new ProjectPlanningFile instance
        new_file = cls(
            sysdate=timezone.now().date(),
            systime=timezone.now().time(),
            compid=CURRENT_COMPANY_ID,
            finyearid=CURRENT_FINANCIAL_YEAR_ID,
            sessionid=session_id, # This would typically be request.user.username or similar
            wono=wono,
            filename=uploaded_file.name,
            filesize=uploaded_file.size,
            contenttype=uploaded_file.content_type,
            filedata=file_data
        )
        new_file.save()
        return new_file

    def delete_file(self):
        """
        Handles the logic for deleting a specific file.
        Mimics the "del" command in GridView3.
        """
        self.delete() # Django's built-in delete method

    def get_download_url(self):
        """
        Returns the URL for downloading this specific file.
        """
        from django.urls import reverse
        return reverse('projectplanningfile_download', args=[self.pk])

```

#### 4.2 Forms (`project_management/forms.py`)

Django forms provide a powerful way to handle user input, validation, and rendering. We define forms for searching Work Orders and for uploading Project Planning files.

```python
from django import forms
from .models import WorkOrder, WOCategory, ProjectPlanningFile

class WorkOrderSearchForm(forms.Form):
    """
    Form for searching and filtering Work Orders.
    Corresponds to txtWono and DDLTaskWOType.
    """
    wono = forms.CharField(
        max_length=50,
        required=False,
        label="WONo:",
        widget=forms.TextInput(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    wo_category = forms.ModelChoiceField(
        queryset=WOCategory.objects.filter(compid=models.Q(compid=CURRENT_COMPANY_ID)), # Filter by current company ID
        required=False,
        empty_label="WO Category",
        label="", # Label handled in template
        widget=forms.Select(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    def clean(self):
        cleaned_data = super().clean()
        # Add any cross-field validation if necessary
        return cleaned_data

class ProjectPlanningFileUploadForm(forms.ModelForm):
    """
    Form for uploading a new Project Planning file.
    Corresponds to FileUpload1/FileUpload2 in GridView3 footer/empty template.
    """
    # Override filedata to use a FileInput widget, but ensure it's saved as binary in model.
    # We will handle the file data conversion in the view's form_valid.
    uploaded_file = forms.FileField(
        label="File",
        required=True,
        widget=forms.FileInput(attrs={'class': 'block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'})
    )

    # Use a hidden input for WONo, which will be passed from the selected WorkOrder
    wono_field = forms.CharField(
        widget=forms.HiddenInput(),
        required=True # WONo is essential for associating the file
    )

    class Meta:
        model = ProjectPlanningFile
        # Only specify fields that directly map from form to model.
        # filedata will be handled manually from 'uploaded_file'.
        # wono will be handled manually from 'wono_field'.
        fields = [] # No direct fields, as we handle via uploaded_file and wono_field

    def save(self, commit=True, session_id=None):
        """
        Custom save method to handle file upload and binary data storage.
        """
        if not self.is_valid():
            raise ValueError("Form is not valid for saving.")

        wono = self.cleaned_data['wono_field']
        uploaded_file = self.cleaned_data['uploaded_file']

        if not wono:
            raise ValueError("Work Order Number is missing for file upload.")
        if not uploaded_file:
            raise ValueError("No file provided for upload.")
        if not session_id:
            raise ValueError("Session ID (user) is required for file upload.")

        # Call the model's class method to handle the actual upload logic
        return ProjectPlanningFile.upload_file(wono, uploaded_file, session_id)

```

#### 4.3 Views (`project_management/views.py`)

Django Class-Based Views (CBVs) simplify the implementation of CRUD operations. We maintain "thin views" by delegating business logic to the models and using HTMX for dynamic content updates, avoiding full page reloads.

```python
from django.views.generic import TemplateView, ListView, View
from django.views.generic.edit import FormView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, FileResponse, Http404
from django.shortcuts import get_object_or_404
from .models import WorkOrder, WOCategory, ProjectPlanningFile, CURRENT_COMPANY_ID, CURRENT_FINANCIAL_YEAR_ID
from .forms import WorkOrderSearchForm, ProjectPlanningFileUploadForm
import mimetypes

# Base view for the Project Planning dashboard, combining GridView2 and GridView3 functionality
class ProjectPlanningDashboardView(TemplateView):
    template_name = 'project_management/projectplanning/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = WorkOrderSearchForm()
        # Initialize selected_wono for the second panel, it will be populated via HTMX
        context['selected_wono'] = self.request.GET.get('selected_wono', '')
        return context

# HTMX partial view for the Work Order table (GridView2 equivalent)
class WorkOrderTablePartialView(ListView):
    model = WorkOrder
    template_name = 'project_management/projectplanning/_workorder_table.html'
    context_object_name = 'work_orders'
    paginate_by = 20 # Corresponds to PageSize="20"

    def get_queryset(self):
        # Default filters from Page_Load and search/DDL change
        wono_filter = self.request.GET.get('wono_search', '')
        category_id = self.request.GET.get('wo_category', '')

        # Use the model's static method to filter data
        queryset = WorkOrder.objects.filter(
            finyearid__lte=CURRENT_FINANCIAL_YEAR_ID,
            closeopen=0,
            compid=CURRENT_COMPANY_ID
        ).order_by('wono')

        if wono_filter:
            queryset = queryset.filter(wono__iexact=wono_filter)

        if category_id and category_id != 'WO Category': # Assuming 'WO Category' is the default value
            queryset = queryset.filter(cid=category_id) # Filter by WO Category ID

        return queryset

# HTMX partial view for Project Planning Files table (GridView3 equivalent)
class ProjectPlanningFilesTablePartialView(ListView):
    model = ProjectPlanningFile
    template_name = 'project_management/projectplanning/_projectplanning_files_table.html'
    context_object_name = 'project_planning_files'
    paginate_by = 23 # Corresponds to PageSize="23"

    def get_queryset(self):
        wono_selected = self.kwargs.get('wono', '')
        if not wono_selected:
            return ProjectPlanningFile.objects.none() # No WO selected, show empty

        return ProjectPlanningFile.get_files_for_work_order(wono_selected)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['selected_wono_for_upload'] = self.kwargs.get('wono', '')
        # Pass the form for file upload in the footer/empty template
        context['file_upload_form'] = ProjectPlanningFileUploadForm(initial={'wono_field': self.kwargs.get('wono', '')})
        return context

# View for handling file uploads (Add/Add1 command in GridView3)
class ProjectPlanningFileCreateView(FormView):
    form_class = ProjectPlanningFileUploadForm
    template_name = 'project_management/projectplanning/_file_upload_form.html' # A dummy template or not used for direct render
    success_url = reverse_lazy('projectplanning_dashboard') # Not directly used for HTMX

    def post(self, request, *args, **kwargs):
        # We need to explicitly pass wono_field from the POST data to the form
        form = self.get_form() # form instance is created with request.POST, request.FILES
        form.data = form.data.copy() # Make data mutable
        form.data['wono_field'] = request.POST.get('wono_field') # Get WONo from hidden field

        if form.is_valid():
            # Use a generic session ID, replace with actual user ID if available
            session_id = request.user.username if request.user.is_authenticated else 'anonymous'
            try:
                # Delegate file saving logic to the model
                form.save(session_id=session_id)
                messages.success(self.request, 'File uploaded successfully.')
            except ValueError as e:
                messages.error(self.request, str(e))
                # For HTMX, if an error, we might want to return 200 with error HTML
                return HttpResponse(
                    f'<div class="text-red-600 p-2">{e}</div>',
                    status=200,
                    headers={'HX-Trigger': 'refreshProjectPlanningFilesTable'} # Trigger refresh anyway on error?
                )
            
            # HTMX response for success: just refresh the relevant part of the page
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshProjectPlanningFilesTable'
                }
            )
        else:
            # If form is not valid, re-render the form with errors
            # This requires the partial template to handle error rendering
            # For simplicity, we just trigger a refresh, or render the form partial.
            # A more robust solution would be to return the form HTML with errors.
            error_messages = [f"{field}: {', '.join(errors)}" for field, errors in form.errors.items()]
            messages.error(self.request, f"Please correct the following errors: {'; '.join(error_messages)}")
            return HttpResponse(
                status=200, # OK, but with content, or specific error headers
                headers={
                    'HX-Trigger': 'refreshProjectPlanningFilesTable'
                }
            )


# View for handling file deletion (del command in GridView3)
class ProjectPlanningFileDeleteView(DeleteView):
    model = ProjectPlanningFile
    template_name = 'project_management/projectplanning/_confirm_delete_file.html'
    success_url = reverse_lazy('projectplanning_dashboard') # Not directly used for HTMX

    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        # Retrieve WONo before deleting for refreshing the correct table
        wono_to_refresh = self.object.wono
        try:
            self.object.delete_file() # Call model method for deletion
            messages.success(self.request, 'File deleted successfully.')
        except Exception as e:
            messages.error(self.request, f"Error deleting file: {e}")

        # HTMX response: Trigger refresh for the specific Work Order's file list
        # And potentially close the modal using Alpine.js from base.html (not shown here)
        return HttpResponse(
            status=204, # No Content
            headers={
                'HX-Trigger': 'refreshProjectPlanningFilesTable'
            }
        )

# View for handling file downloads (downloadImg command in GridView3)
class ProjectPlanningFileDownloadView(View):
    def get(self, request, pk):
        planning_file = get_object_or_404(ProjectPlanningFile, pk=pk)

        # Infer content type if not explicitly set, or use stored type
        content_type, _ = mimetypes.guess_type(planning_file.filename)
        if not content_type:
            content_type = planning_file.contenttype or 'application/octet-stream'

        response = HttpResponse(planning_file.filedata, content_type=content_type)
        response['Content-Disposition'] = f'attachment; filename="{planning_file.filename}"'
        response['Content-Length'] = planning_file.filesize
        return response

```

#### 4.4 Templates (`project_management/templates/project_management/projectplanning/`)

Templates are designed with DRY principles, utilizing partials for reusable components and HTMX for dynamic content. Tailwind CSS is used for styling.

**`dashboard.html` (Main Page - replaces .aspx structure)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-gradient-to-r from-blue-700 to-indigo-700 text-white p-4 rounded-t-lg shadow-md mb-6">
        <h1 class="text-2xl font-bold">Project Planning</h1>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-md mb-8">
        <h3 class="text-xl font-semibold text-gray-800 mb-4">Search Work Orders</h3>
        <form id="searchForm" class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end"
              hx-get="{% url 'workorder_table_partial' %}"
              hx-target="#workOrderTableContainer"
              hx-swap="innerHTML"
              hx-trigger="submit, change from:#id_wo_category"
              hx-indicator="#loadingIndicator">
            {% csrf_token %}
            <div class="form-group">
                <label for="{{ search_form.wono.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">{{ search_form.wono.label }}</label>
                {{ search_form.wono }}
            </div>
            <div class="form-group">
                <label for="{{ search_form.wo_category.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">WO Category:</label>
                {{ search_form.wo_category }}
            </div>
            <div class="form-group">
                <button type="submit" class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                    Search
                </button>
            </div>
            <div id="loadingIndicator" class="htmx-indicator flex items-center justify-center col-span-full">
                <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                <p class="ml-2 text-gray-600">Loading...</p>
            </div>
        </form>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Work Order List Panel (Left - GridView2 equivalent) -->
        <div class="bg-white p-6 rounded-lg shadow-md min-h-[500px]">
            <h3 class="text-xl font-semibold text-gray-800 mb-4">Work Order List</h3>
            <div id="workOrderTableContainer"
                 hx-get="{% url 'workorder_table_partial' %}"
                 hx-trigger="load, refreshWorkOrderList from:body"
                 hx-swap="innerHTML"
                 hx-indicator="#loadingIndicatorWorkOrder">
                <!-- Work Order table will be loaded here via HTMX -->
                <div id="loadingIndicatorWorkOrder" class="htmx-indicator text-center">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading Work Orders...</p>
                </div>
            </div>
        </div>

        <!-- Project Planning Files Panel (Right - GridView3 equivalent) -->
        <div id="projectPlanningFilesPanel" class="bg-white p-6 rounded-lg shadow-md min-h-[500px]"
             hx-trigger="refreshProjectPlanningFilesTable from:body"
             hx-target="this"
             hx-get="{% if selected_wono %}{% url 'projectplanningfiles_table_partial' wono=selected_wono %}{% endif %}"
             hx-swap="innerHTML"
             {% if not selected_wono %}hx-preserve="true"{% endif %}> {# Keep content if no wono is selected initially #}
            <h3 class="text-xl font-semibold text-gray-800 mb-4">Project Planning Files (Selected WO: <span id="selectedWONoDisplay">{{ selected_wono }}</span>)</h3>
            {% if selected_wono %}
                <div class="text-center">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading Project Planning Files...</p>
                </div>
            {% else %}
                <p class="text-gray-600 text-center py-10">Select a Work Order from the left panel to view its planning files.</p>
            {% endif %}
        </div>
    </div>
</div>

<!-- Global Modal for forms (Delete/Add confirmation) -->
<div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 hidden flex items-center justify-center z-50"
     x-data="{ showModal: false }" x-show="showModal"
     @close-modal.window="showModal = false"
     @open-modal.window="showModal = true">
    <div id="modalContent" class="bg-white rounded-lg shadow-xl max-w-lg w-full overflow-hidden"
         @click.away="showModal = false">
        <!-- Content loaded by HTMX -->
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('modalHandler', () => ({
            showModal: false,
            openModal() {
                this.showModal = true;
                document.getElementById('modal').classList.remove('hidden');
                document.getElementById('modal').classList.add('flex'); // Ensure flex for centering
            },
            closeModal() {
                this.showModal = false;
                document.getElementById('modal').classList.add('hidden');
                document.getElementById('modal').classList.remove('flex');
                document.getElementById('modalContent').innerHTML = ''; // Clear modal content
            }
        }));

        // Event listener for HTMX triggering modal open
        document.body.addEventListener('htmx:afterOnLoad', function(event) {
            if (event.detail.target.id === 'modalContent') {
                const modal = document.getElementById('modal');
                if (modal) {
                    Alpine.raw(modal.__alpine.data).openModal();
                }
            }
        });

        // Event listener for HTMX success triggers to close modal
        document.body.addEventListener('htmx:beforeSwap', function(event) {
            // Check for HX-Trigger header that signals a modal close
            if (event.detail.xhr.getResponseHeader('HX-Trigger') &&
                event.detail.xhr.getResponseHeader('HX-Trigger').includes('refreshProjectPlanningFilesTable')) {
                const modal = document.getElementById('modal');
                if (modal) {
                    Alpine.raw(modal.__alpine.data).closeModal();
                }
            }
        });
    });

    // Ensure DataTables is initialized only after HTMX loads content
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'workOrderTableContainer') {
            $('#workOrdersTable').DataTable({
                "pageLength": 20,
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "destroy": true, // Re-initialize if already exists
                "responsive": true
            });
        }
        if (event.detail.target.id === 'projectPlanningFilesPanel') {
            $('#projectPlanningFilesTable').DataTable({
                "pageLength": 23,
                "lengthMenu": [[10, 23, 50, -1], [10, 23, 50, "All"]],
                "destroy": true, // Re-initialize if already exists
                "responsive": true
            });
        }
    });

    // Handle messages (e.g., success/error alerts from Django)
    document.body.addEventListener('htmx:afterSwap', function(event) {
        const messagesDiv = document.getElementById('messages-container'); // Assuming this exists in base.html
        if (messagesDiv) {
            // Optionally, add logic here to display new messages received via HTMX
            // For now, Django's messages framework typically flashes them on redirects/full page loads.
            // For HTMX, you'd typically return partial HTML containing the message or use HX-Trigger to display client-side.
            // Example: on HX-Trigger, check for 'message' key and use Alpine.js to show a toast.
            // For this project, Django messages flash, if HTMX, it might be part of the swapped content.
        }
    });

    // Helper to update the displayed WONo when a row is clicked
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.elt.id === 'lbtnWONo') { // Check if the event was triggered by the WONo link button
            const wono = event.detail.elt.dataset.wono;
            document.getElementById('selectedWONoDisplay').innerText = wono;
            // Also update a hidden input if needed for future POSTs, though HTMX usually handles this
            // For example, if a form in the ProjectPlanningFiles panel needs the selected wono
        }
    });

</script>
{% endblock %}
```

**`_workorder_table.html` (Partial for Work Order List)**

```html
<div class="overflow-x-auto">
    {% if work_orders %}
    <table id="workOrdersTable" class="min-w-full bg-white border border-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project Title</th>
            </tr>
        </thead>
        <tbody>
            {% for wo in work_orders %}
            <tr class="{% cycle 'bg-white' 'bg-gray-50' %} hover:bg-gray-100">
                <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ wo.financial_year }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <button class="text-blue-600 hover:text-blue-800 font-semibold"
                            hx-get="{% url 'projectplanningfiles_table_partial' wono=wo.wono %}"
                            hx-target="#projectPlanningFilesPanel"
                            hx-swap="innerHTML"
                            hx-indicator="#projectPlanningFilesPanel .htmx-indicator"
                            id="lbtnWONo"
                            data-wono="{{ wo.wono }}"
                            _="on click document.getElementById('selectedWONoDisplay').innerText = '{{ wo.wono }}'">
                        {{ wo.wono }}
                    </button>
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ wo.formatted_date }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ wo.taskprojecttitle }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <div class="text-center py-10">
        <p class="text-gray-600 text-lg font-medium">No data to display!</p>
    </div>
    {% endif %}
</div>

<!-- DataTables initialization will be handled in the main dashboard.html after HTMX swap -->
```

**`_projectplanning_files_table.html` (Partial for Project Planning Files List)**

```html
<h3 class="text-xl font-semibold text-gray-800 mb-4">Project Planning Files (Selected WO: <span id="selectedWONoDisplay">{{ selected_wono_for_upload }}</span>)</h3>
<div class="overflow-x-auto">
    {% if project_planning_files %}
    <table id="projectPlanningFilesTable" class="min-w-full bg-white border border-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File</th>
            </tr>
        </thead>
        <tbody>
            {% for pp_file in project_planning_files %}
            <tr class="{% cycle 'bg-white' 'bg-gray-50' %} hover:bg-gray-100">
                <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <button class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                            hx-get="{% url 'projectplanningfile_delete' pp_file.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click document.getElementById('modal').__alpine.data.openModal()">
                        Delete
                    </button>
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ pp_file.formatted_date }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ pp_file.wono }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <a href="{{ pp_file.get_download_url }}" class="text-blue-600 hover:text-blue-800 font-semibold">
                        View ({{ pp_file.filename }})
                    </a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot>
            <tr>
                <td colspan="4" class="py-2 px-4 border-t border-gray-200 text-right font-medium"></td>
                <td class="py-2 px-4 border-t border-gray-200 text-center">
                    <form hx-post="{% url 'projectplanningfile_add' %}" hx-encoding="multipart/form-data" hx-swap="none" hx-trigger="submit" hx-indicator="#fileUploadIndicator" hx-on::after-request="this.reset();" >
                        {% csrf_token %}
                        {{ file_upload_form.wono_field }} {# Hidden field for WONo #}
                        <div class="mb-2">
                             {{ file_upload_form.uploaded_file }}
                        </div>
                        <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                            Add File
                        </button>
                        <div id="fileUploadIndicator" class="htmx-indicator mt-2 text-center">
                            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                            <p class="ml-2 text-gray-600">Uploading...</p>
                        </div>
                    </form>
                </td>
            </tr>
        </tfoot>
    </table>
    {% else %}
    <div class="text-center py-10">
        <p class="text-gray-600 text-lg font-medium mb-4">No planning files found for this Work Order.</p>
        <form hx-post="{% url 'projectplanningfile_add' %}" hx-encoding="multipart/form-data" hx-swap="none" hx-trigger="submit" hx-indicator="#fileUploadIndicatorEmpty" hx-on::after-request="this.reset();" >
            {% csrf_token %}
            {{ file_upload_form.wono_field }} {# Hidden field for WONo #}
            <div class="mb-4">
                {{ file_upload_form.uploaded_file }}
            </div>
            <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                Add First File
            </button>
            <div id="fileUploadIndicatorEmpty" class="htmx-indicator mt-2 text-center">
                <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                <p class="ml-2 text-gray-600">Uploading...</p>
            </div>
        </form>
    </div>
    {% endif %}
</div>
<!-- DataTables initialization will be handled in the main dashboard.html after HTMX swap -->
```

**`_confirm_delete_file.html` (Partial for Delete Confirmation)**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the file "{{ object.filename }}" for WO "{{ object.wono }}"?</p>
    <form hx-delete="{% url 'projectplanningfile_delete' object.pk %}" hx-swap="none" hx-trigger="submit" hx-indicator="#deleteIndicator">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button type="button" 
                    class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                    _="on click document.getElementById('modal').__alpine.data.closeModal()">
                Cancel
            </button>
            <button type="submit" 
                    class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
        <div id="deleteIndicator" class="htmx-indicator mt-2 text-center">
            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <p class="ml-2 text-gray-600">Deleting...</p>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`project_management/urls.py`)

URL patterns define how requests are routed to specific views.

```python
from django.urls import path
from .views import (
    ProjectPlanningDashboardView,
    WorkOrderTablePartialView,
    ProjectPlanningFilesTablePartialView,
    ProjectPlanningFileCreateView,
    ProjectPlanningFileDeleteView,
    ProjectPlanningFileDownloadView,
)

urlpatterns = [
    # Main dashboard view
    path('project-planning/', ProjectPlanningDashboardView.as_view(), name='projectplanning_dashboard'),

    # HTMX partials for lists
    path('project-planning/workorders/table/', WorkOrderTablePartialView.as_view(), name='workorder_table_partial'),
    path('project-planning/files/<str:wono>/table/', ProjectPlanningFilesTablePartialView.as_view(), name='projectplanningfiles_table_partial'),

    # File management operations
    path('project-planning/files/add/', ProjectPlanningFileCreateView.as_view(), name='projectplanningfile_add'),
    path('project-planning/files/delete/<int:pk>/', ProjectPlanningFileDeleteView.as_view(), name='projectplanningfile_delete'),
    path('project-planning/files/download/<int:pk>/', ProjectPlanningFileDownloadView.as_view(), name='projectplanningfile_download'),
]

```

#### 4.6 Tests (`project_management/tests.py`)

Comprehensive tests are crucial for verifying functionality and ensuring maintainability. We include unit tests for model methods and integration tests for all view interactions, including HTMX-driven requests.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.core.files.uploadedfile import SimpleUploadedFile
from django.db import connection
from datetime import date, time
import os

from .models import WorkOrder, WOCategory, FinancialYear, ProjectPlanningFile, CURRENT_COMPANY_ID, CURRENT_FINANCIAL_YEAR_ID

# Mocking database interactions for testing managed=False models
# This setup assumes a clean state for each test run.
# For production-like testing, one might use a test database or fixtures.
def create_mock_db_data():
    # Use raw SQL to insert data into tables for managed=False models
    with connection.cursor() as cursor:
        # Clear existing data to ensure test isolation
        cursor.execute(f"DELETE FROM tblFinancial_master WHERE CompId = {CURRENT_COMPANY_ID}")
        cursor.execute(f"DELETE FROM tblSD_WO_Category WHERE CompId = {CURRENT_COMPANY_ID}")
        cursor.execute(f"DELETE FROM SD_Cust_WorkOrder_Master WHERE CompId = {CURRENT_COMPANY_ID}")
        cursor.execute(f"DELETE FROM tblPM_ProjectPlanning_Master WHERE CompId = {CURRENT_COMPANY_ID}")

        # Insert FinancialYear data
        cursor.execute(f"INSERT INTO tblFinancial_master (FinYearId, FinYear) VALUES ({CURRENT_FINANCIAL_YEAR_ID}, '2023-2024')")
        cursor.execute(f"INSERT INTO tblFinancial_master (FinYearId, FinYear) VALUES (2, '2022-2023')")

        # Insert WOCategory data
        cursor.execute(f"INSERT INTO tblSD_WO_Category (CId, Symbol, CName, CompId) VALUES (101, 'SW', 'Software', {CURRENT_COMPANY_ID})")
        cursor.execute(f"INSERT INTO tblSD_WO_Category (CId, Symbol, CName, CompId) VALUES (102, 'HW', 'Hardware', {CURRENT_COMPANY_ID})")

        # Insert WorkOrder data
        cursor.execute(f"INSERT INTO SD_Cust_WorkOrder_Master (Id, FinYearId, WONo, TaskWorkOrderDate, TaskProjectTitle, CloseOpen, CompId, CId) VALUES (1, {CURRENT_FINANCIAL_YEAR_ID}, 'WO-2023-001', '2023-01-15 10:00:00', 'ERP System Implementation', 0, {CURRENT_COMPANY_ID}, 101)")
        cursor.execute(f"INSERT INTO SD_Cust_WorkOrder_Master (Id, FinYearId, WONo, TaskWorkOrderDate, TaskProjectTitle, CloseOpen, CompId, CId) VALUES (2, {CURRENT_FINANCIAL_YEAR_ID}, 'WO-2023-002', '2023-02-20 11:30:00', 'Network Upgrade Project', 0, {CURRENT_COMPANY_ID}, 102)")
        cursor.execute(f"INSERT INTO SD_Cust_WorkOrder_Master (Id, FinYearId, WONo, TaskWorkOrderDate, TaskProjectTitle, CloseOpen, CompId, CId) VALUES (3, {CURRENT_FINANCIAL_YEAR_ID}, 'WO-2023-003', '2023-03-01 09:00:00', 'Mobile App Development', 0, {CURRENT_COMPANY_ID}, 101)")
        cursor.execute(f"INSERT INTO SD_Cust_WorkOrder_Master (Id, FinYearId, WONo, TaskWorkOrderDate, TaskProjectTitle, CloseOpen, CompId, CId) VALUES (4, 2, 'WO-2022-004', '2022-10-05 14:00:00', 'Legacy System Migration', 0, {CURRENT_COMPANY_ID}, 101)") # Old financial year
        cursor.execute(f"INSERT INTO SD_Cust_WorkOrder_Master (Id, FinYearId, WONo, TaskWorkOrderDate, TaskProjectTitle, CloseOpen, CompId, CId) VALUES (5, {CURRENT_FINANCIAL_YEAR_ID}, 'WO-2023-005', '2023-04-10 16:00:00', 'Closed Project', 1, {CURRENT_COMPANY_ID}, 101)") # Closed project

        # Insert ProjectPlanningFile data
        file_data_1 = b"This is content for planning file 1."
        cursor.execute(f"INSERT INTO tblPM_ProjectPlanning_Master (Id, SysDate, SysTime, CompId, FinYearId, SessionId, WONo, FileName, FileSize, ContentType, FileData) VALUES (1, '2023-01-15', '10:05:00', {CURRENT_COMPANY_ID}, {CURRENT_FINANCIAL_YEAR_ID}, 'testuser', 'WO-2023-001', 'plan1.pdf', {len(file_data_1)}, 'application/pdf', '{file_data_1.hex}')")
        file_data_2 = b"Another file for WO-2023-001."
        cursor.execute(f"INSERT INTO tblPM_ProjectPlanning_Master (Id, SysDate, SysTime, CompId, FinYearId, SessionId, WONo, FileName, FileSize, ContentType, FileData) VALUES (2, '2023-01-20', '14:30:00', {CURRENT_COMPANY_ID}, {CURRENT_FINANCIAL_YEAR_ID}, 'testuser', 'WO-2023-001', 'report.docx', {len(file_data_2)}, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', '{file_data_2.hex}')")
        file_data_3 = b"File for WO-2023-002."
        cursor.execute(f"INSERT INTO tblPM_ProjectPlanning_Master (Id, SysDate, SysTime, CompId, FinYearId, SessionId, WONo, FileName, FileSize, ContentType, FileData) VALUES (3, '2023-02-25', '09:15:00', {CURRENT_COMPANY_ID}, {CURRENT_FINANCIAL_YEAR_ID}, 'testuser', 'WO-2023-002', 'spec.txt', {len(file_data_3)}, 'text/plain', '{file_data_3.hex}')")

class ProjectPlanningModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data using raw SQL for managed=False models
        create_mock_db_data()

    def test_financial_year_model(self):
        fy = FinancialYear.objects.get(finyearid=CURRENT_FINANCIAL_YEAR_ID)
        self.assertEqual(fy.finyear, '2023-2024')
        self.assertEqual(str(fy), '2023-2024')

    def test_wo_category_model(self):
        cat = WOCategory.objects.get(cid=101)
        self.assertEqual(cat.cname, 'Software')
        self.assertEqual(str(cat), 'SW - Software')

    def test_work_order_model(self):
        wo = WorkOrder.objects.get(id=1)
        self.assertEqual(wo.wono, 'WO-2023-001')
        self.assertEqual(wo.taskprojecttitle, 'ERP System Implementation')
        self.assertEqual(wo.formatted_date, '15-01-2023')
        self.assertEqual(wo.financial_year, '2023-2024')

    def test_work_order_filter_by_wono(self):
        work_orders = WorkOrder.filter_work_orders(wono_filter='WO-2023-001')
        self.assertEqual(work_orders.count(), 1)
        self.assertEqual(work_orders.first().wono, 'WO-2023-001')

    def test_work_order_filter_by_category(self):
        work_orders = WorkOrder.objects.filter_work_orders(category_id='102')
        self.assertEqual(work_orders.count(), 1)
        self.assertEqual(work_orders.first().wono, 'WO-2023-002')

    def test_work_order_filter_excludes_closed_and_old_fy(self):
        work_orders = WorkOrder.objects.filter_work_orders()
        self.assertEqual(work_orders.count(), 3) # WO-2023-001, WO-2023-002, WO-2023-003

    def test_project_planning_file_model(self):
        pp_file = ProjectPlanningFile.objects.get(id=1)
        self.assertEqual(pp_file.filename, 'plan1.pdf')
        self.assertEqual(pp_file.wono, 'WO-2023-001')
        self.assertEqual(pp_file.formatted_date, '15-01-2023')
        self.assertIsInstance(pp_file.filedata, bytes)
        self.assertEqual(pp_file.filedata, b"This is content for planning file 1.")

    def test_project_planning_file_get_files_for_work_order(self):
        files = ProjectPlanningFile.get_files_for_work_order('WO-2023-001')
        self.assertEqual(files.count(), 2)
        self.assertTrue(files.filter(filename='plan1.pdf').exists())
        self.assertTrue(files.filter(filename='report.docx').exists())

    def test_project_planning_file_upload(self):
        initial_count = ProjectPlanningFile.objects.count()
        test_file = SimpleUploadedFile("new_doc.txt", b"This is a test document.", "text/plain")
        uploaded_file = ProjectPlanningFile.upload_file(
            wono='WO-2023-003',
            uploaded_file=test_file,
            session_id='testuser_upload'
        )
        self.assertEqual(ProjectPlanningFile.objects.count(), initial_count + 1)
        self.assertEqual(uploaded_file.filename, "new_doc.txt")
        self.assertEqual(uploaded_file.wono, 'WO-2023-003')
        self.assertEqual(uploaded_file.filedata, b"This is a test document.")
        uploaded_file.delete() # Clean up

    def test_project_planning_file_delete(self):
        pp_file = ProjectPlanningFile.objects.get(id=3)
        initial_count = ProjectPlanningFile.objects.count()
        pp_file.delete_file()
        self.assertEqual(ProjectPlanningFile.objects.count(), initial_count - 1)
        self.assertFalse(ProjectPlanningFile.objects.filter(id=3).exists())

    def test_project_planning_file_get_download_url(self):
        pp_file = ProjectPlanningFile.objects.get(id=1)
        expected_url = reverse('projectplanningfile_download', args=[pp_file.pk])
        self.assertEqual(pp_file.get_download_url(), expected_url)


class ProjectPlanningViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        create_mock_db_data()

    def setUp(self):
        self.client = Client()
        self.dashboard_url = reverse('projectplanning_dashboard')
        self.workorder_table_url = reverse('workorder_table_partial')
        self.file_add_url = reverse('projectplanningfile_add')

    def test_dashboard_view_get(self):
        response = self.client.get(self.dashboard_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/projectplanning/dashboard.html')
        self.assertContains(response, 'Project Planning')
        self.assertContains(response, '<form id="searchForm"')
        self.assertContains(response, '<div id="workOrderTableContainer"')
        self.assertContains(response, '<div id="projectPlanningFilesPanel"')

    def test_work_order_table_partial_view_get(self):
        response = self.client.get(self.workorder_table_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/projectplanning/_workorder_table.html')
        self.assertContains(response, 'WO-2023-001')
        self.assertContains(response, 'WO-2023-002')
        self.assertContains(response, 'WO-2023-003')
        self.assertNotContains(response, 'WO-2022-004') # Old financial year
        self.assertNotContains(response, 'WO-2023-005') # Closed project
        self.assertEqual(len(response.context['work_orders']), 3)

    def test_work_order_table_partial_view_search_wono(self):
        response = self.client.get(self.workorder_table_url, {'wono_search': 'WO-2023-001'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO-2023-001')
        self.assertNotContains(response, 'WO-2023-002')
        self.assertEqual(len(response.context['work_orders']), 1)

    def test_work_order_table_partial_view_search_category(self):
        cat = WOCategory.objects.get(symbol='HW')
        response = self.client.get(self.workorder_table_url, {'wo_category': cat.cid})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO-2023-002')
        self.assertNotContains(response, 'WO-2023-001')
        self.assertEqual(len(response.context['work_orders']), 1)

    def test_project_planning_files_table_partial_view_get(self):
        response = self.client.get(reverse('projectplanningfiles_table_partial', args=['WO-2023-001']))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/projectplanning/_projectplanning_files_table.html')
        self.assertContains(response, 'plan1.pdf')
        self.assertContains(response, 'report.docx')
        self.assertNotContains(response, 'spec.txt')
        self.assertEqual(len(response.context['project_planning_files']), 2)
        self.assertContains(response, '<form hx-post') # Check for file upload form

    def test_project_planning_file_create_view_post_success(self):
        initial_count = ProjectPlanningFile.objects.count()
        test_file_content = b"Content for the new file."
        uploaded_file = SimpleUploadedFile("test_file.txt", test_file_content, "text/plain")
        data = {
            'wono_field': 'WO-2023-001',
            'uploaded_file': uploaded_file,
        }
        # Mimic HTMX request headers
        headers = {'HTTP_HX_REQUEST': 'true', 'HTTP_HX_TARGET': 'projectPlanningFilesPanel', 'HTTP_HX_SWAP': 'innerHTML'}
        response = self.client.post(self.file_add_url, data, **headers)
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertEqual(ProjectPlanningFile.objects.count(), initial_count + 1)
        self.assertTrue(ProjectPlanningFile.objects.filter(filename='test_file.txt', wono='WO-2023-001').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshProjectPlanningFilesTable', response.headers['HX-Trigger'])

    def test_project_planning_file_create_view_post_no_file(self):
        initial_count = ProjectPlanningFile.objects.count()
        data = {
            'wono_field': 'WO-2023-001',
            # No 'uploaded_file'
        }
        headers = {'HTTP_HX_REQUEST': 'true', 'HTTP_HX_TARGET': 'projectPlanningFilesPanel', 'HTTP_HX_SWAP': 'innerHTML'}
        response = self.client.post(self.file_add_url, data, **headers)
        self.assertEqual(response.status_code, 200) # Form validation error returns 200 with message
        self.assertEqual(ProjectPlanningFile.objects.count(), initial_count)
        self.assertContains(response, 'No file uploaded.', html=False) # Check for message in the response
        self.assertIn('HX-Trigger', response.headers) # Should still trigger refresh

    def test_project_planning_file_delete_view_get(self):
        file_to_delete = ProjectPlanningFile.objects.get(id=1)
        response = self.client.get(reverse('projectplanningfile_delete', args=[file_to_delete.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/projectplanning/_confirm_delete_file.html')
        self.assertContains(response, f'Are you sure you want to delete the file "{file_to_delete.filename}"')

    def test_project_planning_file_delete_view_post_success(self):
        file_to_delete = ProjectPlanningFile.objects.get(id=1)
        initial_count = ProjectPlanningFile.objects.count()
        wono_of_deleted_file = file_to_delete.wono # Get WONo before deletion
        
        headers = {'HTTP_HX_REQUEST': 'true', 'HTTP_HX_TARGET': 'modalContent', 'HTTP_HX_SWAP': 'outerHTML'} # HTMX typically targets modal content
        response = self.client.delete(reverse('projectplanningfile_delete', args=[file_to_delete.pk]), **headers) # Use client.delete for DELETE requests
        
        self.assertEqual(response.status_code, 204)
        self.assertEqual(ProjectPlanningFile.objects.count(), initial_count - 1)
        self.assertFalse(ProjectPlanningFile.objects.filter(id=1).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshProjectPlanningFilesTable', response.headers['HX-Trigger'])

    def test_project_planning_file_download_view(self):
        download_file = ProjectPlanningFile.objects.get(id=1)
        response = self.client.get(reverse('projectplanningfile_download', args=[download_file.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], download_file.contenttype)
        self.assertEqual(response['Content-Length'], str(download_file.filesize))
        self.assertIn(f'attachment; filename="{download_file.filename}"', response['Content-Disposition'])
        self.assertEqual(response.content, download_file.filedata)

    def test_project_planning_file_download_view_not_found(self):
        response = self.client.get(reverse('projectplanningfile_download', args=[9999])) # Non-existent ID
        self.assertEqual(response.status_code, 404)

```

---

### Step 5: HTMX and Alpine.js Integration

The core of this modernization lies in the seamless integration of HTMX and Alpine.js to deliver a highly interactive and responsive user experience without the need for complex JavaScript frameworks.

-   **HTMX for Dynamic Content**:
    -   **Search and Filter**: The `searchForm` (containing `txtWono` and `DDLTaskWOType`) uses `hx-get` to trigger a request to `workorder_table_partial` on `submit` or `change` of the dropdown. The response (`_workorder_table.html`) is swapped into `#workOrderTableContainer`.
    -   **Work Order Selection**: Clicking a "WONo" link in the Work Order table triggers an `hx-get` to `projectplanningfiles_table_partial` (passing the selected `WONo`). The response (`_projectplanning_files_table.html`) is swapped into `#projectPlanningFilesPanel`.
    -   **File Upload**: The file upload form in `_projectplanning_files_table.html` uses `hx-post` with `hx-encoding="multipart/form-data"` to send the file. On success, a `204 No Content` response with `HX-Trigger: refreshProjectPlanningFilesTable` is sent, prompting the files table to reload.
    -   **File Deletion**: The "Delete" button triggers an `hx-get` to `projectplanningfile_delete` to load the confirmation modal. The confirmation form within the modal uses `hx-delete` (for RESTful deletion). Upon successful deletion, a `204 No Content` with `HX-Trigger: refreshProjectPlanningFilesTable` is sent, refreshing the list and closing the modal.
    -   **Loading Indicators**: `hx-indicator` is used to show a spinning loader while HTMX requests are in progress, providing visual feedback.
    -   **Table Refresh**: `HX-Trigger` events (e.g., `refreshWorkOrderList`, `refreshProjectPlanningFilesTable`) are broadcast from the server after successful CRUD operations. These events trigger the `hx-get` on the respective table containers, ensuring data consistency across the UI.

-   **Alpine.js for UI State Management**:
    -   **Modals**: Alpine.js is used to manage the visibility of the global modal (`#modal`). HTMX is configured to load partial HTML content into `#modalContent`, and Alpine.js then handles the opening and closing of the modal in response to HTMX actions (e.g., `on click add .is-active to #modal` using `_` attribute, or `htmx:afterOnLoad` listener). The `close-modal` event can be dispatched or directly called via `Alpine.raw(modal.__alpine.data).closeModal()` from HTMX success triggers.
    -   **Dynamic Text Updates**: `_` attributes (hyperscript syntax) are used for simple DOM manipulations like updating the `selectedWONoDisplay` text when a Work Order is selected, directly tying UI changes to HTMX events without complex JavaScript.

-   **DataTables for List Views**:
    -   Both the `workOrdersTable` and `projectPlanningFilesTable` are initialized using DataTables.js on `htmx:afterSwap` events. This ensures that DataTables is applied to the newly loaded content, providing instant client-side sorting, searching, and pagination without requiring server-side re-rendering for these operations. The `destroy: true` option ensures proper re-initialization if the table content is swapped multiple times.

-   **DRY Template Inheritance**:
    -   All module-specific templates (`dashboard.html`, partials) extend `core/base.html` (not included here), which is responsible for importing necessary CDN links for Tailwind CSS, HTMX, Alpine.js, and DataTables. This keeps each component's template focused on its unique content.

-   **No Custom JavaScript (Beyond HTMX/Alpine Init)**:
    -   The entire dynamic interaction layer is built using HTMX attributes and minimal Alpine.js directives, eliminating the need for custom, imperative JavaScript code for AJAX calls or DOM manipulation. This dramatically simplifies frontend development and maintenance.

---

## Final Notes

This comprehensive plan provides a clear roadmap for migrating your ASP.NET Project Planning module to a modern Django application. By adhering to the principles of fat models, thin views, and embracing HTMX and Alpine.js for dynamic interactions, you will achieve:

-   **Improved Maintainability**: Clear separation of concerns and a strong emphasis on testability make the codebase easier to understand and evolve.
-   **Enhanced Performance**: HTMX delivers a snappy, app-like feel by updating only necessary parts of the page, reducing network overhead.
-   **Simplified Development**: Leveraging Django's robust features and the elegance of HTMX/Alpine.js streamlines future development efforts.
-   **Scalability**: A well-structured Django application is inherently more scalable, capable of handling increased user loads and data volumes.
-   **Business Agility**: Faster development cycles and easier maintenance enable your organization to respond more quickly to changing business requirements.

This structured, automation-driven approach ensures a predictable and successful modernization, empowering your team to build upon a future-proof foundation.