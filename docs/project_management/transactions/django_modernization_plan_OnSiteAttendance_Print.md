This modernization plan details the conversion of a legacy ASP.NET Onsite Attendance report page to a modern Django-based solution. The focus is on leveraging Django 5.0+ features, a "fat model, thin view" architecture, and dynamic frontend interactions using HTMX and Alpine.js, all while ensuring data presentation via DataTables.

## ASP.NET to Django Conversion Script:

This document provides a comprehensive migration plan focusing on automating the transition from the given ASP.NET component to a modern Django application.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:** The ASP.NET code interacts with several database tables through SQL commands and data binding. The `fun.select` and `fun.select1` methods abstract the SQL queries, but the table names are evident.

**Identified Tables and Inferred Columns:**

*   **`tblFinancial_master`**: This table is used to populate the "Year" dropdown and determine month ranges.
    *   Columns: `FinYearId` (Primary Key), `FinYear`, `FinYearFrom` (Date), `FinYearTo` (Date), `CompId` (Company ID).
*   **`BusinessGroup`**: Used for the "BG Group" dropdown.
    *   Columns: `Id` (Primary Key), `Symbol`.
*   **`tblHR_OfficeStaff`**: Used for the "Employee Name" autocomplete.
    *   Columns: `EmpId` (Primary Key), `EmployeeName`, `CompId`, `BGGroup` (Foreign Key to `BusinessGroup.Id`).
*   **`tblOnSiteAttendance_Master`**: This is the core table for attendance records, filtered and displayed.
    *   Columns: `id` (Assumed AutoField/Primary Key for Django's ORM, though not explicitly mentioned in ASP.NET), `OnSiteDate` (Date), `FinYearId` (Foreign Key to `tblFinancial_master.FinYearId`), `EmpId` (Foreign Key to `tblHR_OfficeStaff.EmpId`). A `description` field is added for typical data entry, as the original page is a report, not a CRUD interface for this table, but the prompt requires CRUD examples.

### Step 2: Identify Backend Functionality

**Analysis:** The ASP.NET page is primarily a "Print" (reporting/filtering) page. It allows users to filter attendance records based on various criteria and then displays a report (originally via an `iframe` with a Crystal Report).

**Core Functionality:**

*   **Read/Filter Data:** Display a list of on-site attendance records based on selected filters (Year, Month, From Date, To Date, Employee Name, BG Group).
*   **Dropdown Population:** Dynamically populate "Year" and "BG Group" dropdowns upon page load. Dynamically update "Month" dropdown based on the selected "Year" (financial year range).
*   **Autocomplete:** Provide employee name suggestions as the user types.
*   **Session Management:** `CompId` and `FinYearId` are retrieved from the session, which will be mapped to Django's session or user context.
*   **Utility Functions:** Date formatting (`fun.FromDate`), month range calculation (`fun.MonthRange`), and employee ID extraction (`fun.getCode`).

**Note on CRUD:** The original ASP.NET code does *not* exhibit direct Create, Update, or Delete operations for `OnSiteAttendance`. However, as per the prompt's instructions to provide CRUD operations, generic CRUD views and templates for `OnSiteAttendance` will be included as illustrative examples. The primary focus of the migration will be on the filtering and display (Read) functionality derived directly from the provided ASP.NET code.

### Step 3: Infer UI Components

**Analysis:** The ASP.NET UI consists of standard form controls and an `iframe` for report display.

**Identified UI Components and Their Django/HTMX/Alpine.js Equivalents:**

*   **Dropdowns (`<asp:DropDownList>`):** For Year, Month, and BG Group. These will be Django `forms.ChoiceField` rendered as `<select>` elements, dynamically updated using HTMX.
*   **Textboxes (`<asp:TextBox>`):** For From Date, To Date, and Employee Name. These will be Django `forms.CharField` rendered as `<input type="text">`.
    *   **Calendar Extender (`<cc1:CalendarExtender>`):** For date selection. This will be replaced by a modern JavaScript date picker integrated with Alpine.js or a simple HTML5 `type="date"` input (though for browser compatibility and specified format, a JS library is better). We'll assume a lightweight date picker script is available (e.g., flatpickr, initialized by Alpine.js).
    *   **Autocomplete Extender (`<cc1:AutoCompleteExtender>`):** For employee name suggestions. This will be replaced by HTMX `hx-post` or `hx-get` to a Django view returning a list of suggestions.
*   **Search Button (`<asp:Button>`):** Triggers the report/filter. This will trigger an HTMX request (`hx-get` or `hx-post`) to refresh the DataTables content.
*   **Iframe (`<iframe>`):** Originally used to display the Crystal Report. This will be replaced by a `div` element where the filtered attendance data (rendered as a DataTables table) will be loaded dynamically via HTMX.

---

### Step 4: Generate Django Code

We will create a Django application named `attendancereport`.

#### 4.1 Models (`attendancereport/models.py`)

Models are mapped to the existing database tables using `managed = False`. Business logic for data retrieval and filtering is encapsulated here.

```python
from django.db import models
from datetime import datetime, date
from dateutil.relativedelta import relativedelta # Requires python-dateutil package
import re

class FinancialYear(models.Model):
    """
    Maps to tblFinancial_master for managing financial year details.
    """
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=100)
    fin_year_from = models.DateField(db_column='FinYearFrom')
    fin_year_to = models.DateField(db_column='FinYearTo')
    company_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False # Django won't manage table creation/deletion
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

    @classmethod
    def get_month_range(cls, start_date_str, end_date_str):
        """
        Replicates the fun.MonthRange logic to get a list of months
        within a financial year range.
        Input date strings are expected in 'DD-MM-YYYY' format.
        Returns a list of tuples (month_number, month_name).
        """
        try:
            start_date = datetime.strptime(start_date_str, '%d-%m-%Y').date()
            end_date = datetime.strptime(end_date_str, '%d-%m-%Y').date()
        except ValueError:
            # Handle cases where dates might be in a different format or invalid
            return []

        months_list = []
        current_date = start_date
        while current_date <= end_date:
            # Value for dropdown is month number (1-12), text is full month name
            months_list.append((str(current_date.month), current_date.strftime('%B')))
            current_date += relativedelta(months=1)
        return months_list

class BusinessGroup(models.Model):
    """
    Maps to BusinessGroup table.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=100)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

class HROfficeStaff(models.Model):
    """
    Maps to tblHR_OfficeStaff for employee details.
    """
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    company_id = models.IntegerField(db_column='CompId')
    # Assuming BGGroup in tblHR_OfficeStaff is a foreign key to BusinessGroup.Id
    bg_group = models.ForeignKey(BusinessGroup, on_delete=models.SET_NULL, db_column='BGGroup', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'HR Office Staff'
        verbose_name_plural = 'HR Office Staff'

    def __str__(self):
        # Format for autocomplete: "Name [ID]"
        return f"{self.employee_name} [{self.emp_id}]"

    @staticmethod
    def get_emp_id_from_name_code(name_with_code):
        """
        Replicates fun.getCode logic, which extracts the employee ID
        from a string like "EmployeeName [EmpId]".
        """
        match = re.search(r'\[(.*?)\]$', name_with_code)
        if match:
            return match.group(1)
        return None

class OnSiteAttendance(models.Model):
    """
    Maps to tblOnSiteAttendance_Master for on-site attendance records.
    Assumed 'id' as primary key for Django's ORM operations.
    """
    id = models.AutoField(db_column='Id', primary_key=True) # Assuming an auto-incrementing PK
    on_site_date = models.DateField(db_column='OnSiteDate')
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.CASCADE, db_column='FinYearId')
    employee = models.ForeignKey(HROfficeStaff, on_delete=models.CASCADE, db_column='EmpId')
    description = models.TextField(blank=True, null=True, verbose_name="Description / Remarks") # Added for CRUD example

    class Meta:
        managed = False
        db_table = 'tblOnSiteAttendance_Master'
        verbose_name = 'OnSite Attendance'
        verbose_name_plural = 'OnSite Attendance'

    def __str__(self):
        return f"Attendance for {self.employee.employee_name} on {self.on_site_date.strftime('%d-%m-%Y')}"

    @classmethod
    def filter_attendance(cls, company_id, fin_year_id=None, month=None, from_date=None, to_date=None, emp_id=None, bg_group_id=None):
        """
        Applies filtering logic based on the parameters provided by the search form.
        This consolidates the filtering logic from BtnSearch_Click.
        """
        queryset = cls.objects.all().select_related('financial_year', 'employee', 'employee__bg_group')

        # Filter by company_id, as all data (financial years, employees) are tied to it
        queryset = queryset.filter(financial_year__company_id=company_id, employee__company_id=company_id)

        if fin_year_id:
            queryset = queryset.filter(financial_year__fin_year_id=fin_year_id)

        if month:
            # Original used LIKE '%-D2-%%', which is inefficient for dates. Proper date filtering is used here.
            queryset = queryset.filter(on_site_date__month=int(month))

        if from_date and to_date:
            queryset = queryset.filter(on_site_date__range=(from_date, to_date))

        if emp_id:
            queryset = queryset.filter(employee__emp_id=emp_id)

        if bg_group_id:
            queryset = queryset.filter(employee__bg_group__id=bg_group_id)

        return queryset.order_by('on_site_date', 'employee__employee_name')

```

#### 4.2 Forms (`attendancereport/forms.py`)

Two forms are defined: one for the filter criteria (`OnSiteAttendanceFilterForm`) and one for the hypothetical CRUD operations (`OnSiteAttendanceForm`).

```python
from django import forms
from .models import FinancialYear, BusinessGroup, HROfficeStaff, OnSiteAttendance
from datetime import datetime, date

class OnSiteAttendanceFilterForm(forms.Form):
    """
    Form for filtering Onsite Attendance records.
    Corresponds to the search controls in the ASP.NET page.
    """
    year = forms.ChoiceField(
        label="Year",
        required=False,
        choices=[('', 'Select')],
        widget=forms.Select(attrs={
            'class': 'box3',
            'hx-get': '/attendancereport/get-months/', # HTMX to update month dropdown
            'hx-target': '#id_month',
            'hx-swap': 'innerHTML',
            'hx-trigger': 'change' # Trigger on year selection change
        })
    )
    month = forms.ChoiceField(
        label="Month",
        required=False,
        choices=[('', 'Select')],
        widget=forms.Select(attrs={'class': 'box3'})
    )
    from_date = forms.CharField(
        label="From Date",
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 datepicker', # 'datepicker' class for Alpine.js/JS init
            'placeholder': 'DD-MM-YYYY',
            'autocomplete': 'off', # Prevent browser autocomplete
        })
    )
    to_date = forms.CharField(
        label="To Date",
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 datepicker', # 'datepicker' class for Alpine.js/JS init
            'placeholder': 'DD-MM-YYYY',
            'autocomplete': 'off', # Prevent browser autocomplete
        })
    )
    employee_name = forms.CharField(
        label="Employee Name",
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3',
            'hx-post': '/attendancereport/autocomplete-employee/', # HTMX for autocomplete
            'hx-trigger': 'keyup changed delay:500ms, search', # Trigger on keyup after delay
            'hx-target': '#employee-suggestions', # Target div for suggestions
            'hx-swap': 'outerHTML',
            'hx-indicator': '.htmx-indicator' # Show loading indicator
        })
    )
    bg_group = forms.ChoiceField(
        label="BG Group",
        required=False,
        choices=[('', 'Select')],
        widget=forms.Select(attrs={'class': 'box3'})
    )

    def __init__(self, *args, **kwargs):
        company_id = kwargs.pop('company_id', None)
        super().__init__(*args, **kwargs)

        # Populate Year dropdown options
        if company_id:
            years = FinancialYear.objects.filter(company_id=company_id).order_by('-fin_year_id').values_list('fin_year_id', 'fin_year')
            self.fields['year'].choices = [('', 'Select')] + list(years)
        else:
            self.fields['year'].choices = [('', 'Select')]

        # Populate BG Group dropdown options
        bg_groups = BusinessGroup.objects.all().values_list('id', 'symbol')
        self.fields['bg_group'].choices = [('', 'Select')] + list(bg_groups)

        # Handle initial month population if a year is pre-selected (e.g., from query params)
        if self.is_bound and 'year' in self.data and self.data['year']:
            selected_year_id = self.data['year']
            try:
                fin_year_obj = FinancialYear.objects.get(fin_year_id=selected_year_id)
                months_data = FinancialYear.get_month_range(fin_year_obj.fin_year_from.strftime('%d-%m-%Y'), fin_year_obj.fin_year_to.strftime('%d-%m-%Y'))
                self.fields['month'].choices = [('', 'Select')] + months_data
            except FinancialYear.DoesNotExist:
                self.fields['month'].choices = [('', 'Select')]
        else:
            self.fields['month'].choices = [('', 'Select')] # Default if no year is selected

    def clean_from_date(self):
        from_date_str = self.cleaned_data.get('from_date')
        if from_date_str:
            try:
                return datetime.strptime(from_date_str, '%d-%m-%Y').date()
            except ValueError:
                raise forms.ValidationError("Invalid date format. Use DD-MM-YYYY.")
        return None

    def clean_to_date(self):
        to_date_str = self.cleaned_data.get('to_date')
        if to_date_str:
            try:
                return datetime.strptime(to_date_str, '%d-%m-%Y').date()
            except ValueError:
                raise forms.ValidationError("Invalid date format. Use DD-MM-YYYY.")
        return None

    def clean(self):
        cleaned_data = super().clean()
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')

        if from_date and to_date and from_date > to_date:
            self.add_error('to_date', "To Date cannot be before From Date.")

        return cleaned_data

class OnSiteAttendanceForm(forms.ModelForm):
    """
    Form for OnSiteAttendance CRUD operations.
    Includes special fields for date string input and employee autocomplete.
    """
    on_site_date_str = forms.CharField(
        label="On Site Date",
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm datepicker', 'placeholder': 'DD-MM-YYYY'})
    )
    employee_full_name = forms.CharField(
        label="Employee Name",
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-post': '/attendancereport/autocomplete-employee/', # Reuse autocomplete endpoint
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#employee-full-suggestions',
            'hx-swap': 'outerHTML',
            'hx-indicator': '.htmx-indicator'
        })
    )

    class Meta:
        model = OnSiteAttendance
        fields = ['financial_year', 'description'] # Actual fields to handle after cleaning
        widgets = {
            'financial_year': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Hide model fields, as their values will be set from string fields after cleaning
        self.fields['financial_year'].queryset = FinancialYear.objects.all().order_by('-fin_year_id')
        
        # Populate initial values for string fields if editing an existing instance
        if self.instance.pk:
            self.initial['on_site_date_str'] = self.instance.on_site_date.strftime('%d-%m-%Y')
            self.initial['employee_full_name'] = str(self.instance.employee) # "Name [ID]" format

    def clean_on_site_date_str(self):
        date_str = self.cleaned_data.get('on_site_date_str')
        if date_str:
            try:
                return datetime.strptime(date_str, '%d-%m-%Y').date()
            except ValueError:
                raise forms.ValidationError("Invalid date format. Use DD-MM-YYYY.")
        return None

    def clean_employee_full_name(self):
        employee_name_with_code = self.cleaned_data.get('employee_full_name')
        if employee_name_with_code:
            emp_id = HROfficeStaff.get_emp_id_from_name_code(employee_name_with_code)
            if emp_id:
                try:
                    return HROfficeStaff.objects.get(emp_id=emp_id)
                except HROfficeStaff.DoesNotExist:
                    raise forms.ValidationError("Selected employee does not exist.")
            else:
                raise forms.ValidationError("Invalid employee format. Must be 'Name [ID]'.")
        return None

    def clean(self):
        cleaned_data = super().clean()
        # Assign cleaned data from string fields to model fields
        cleaned_data['on_site_date'] = cleaned_data.get('on_site_date_str')
        cleaned_data['employee'] = cleaned_data.get('employee_full_name')
        return cleaned_data

```

#### 4.3 Views (`attendancereport/views.py`)

Views are kept thin, delegating complex logic to models and forms.

```python
from django.views.generic import ListView, TemplateView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db.models import Q
from datetime import datetime, date

from .models import FinancialYear, BusinessGroup, HROfficeStaff, OnSiteAttendance
from .forms import OnSiteAttendanceFilterForm, OnSiteAttendanceForm

# --- Views for Onsite Attendance Filtering/Reporting (Core Functionality) ---

class OnSiteAttendanceListView(TemplateView):
    """
    Main view for displaying the Onsite Attendance report page.
    Renders the filter form and the container for the HTMX-loaded attendance table.
    """
    template_name = 'attendancereport/onsiteattendance/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # In a real app, company_id would come from authenticated user or settings
        # For this example, assuming it's in session or defaulting
        company_id = self.request.session.get('compid', 1) 
        
        # Populate the filter form. If GET parameters are present, bind them.
        context['filter_form'] = OnSiteAttendanceFilterForm(self.request.GET or None, company_id=company_id)
        return context

class OnSiteAttendanceTablePartialView(ListView):
    """
    HTMX endpoint to render the filtered Onsite Attendance table.
    This view contains the primary filtering logic derived from BtnSearch_Click.
    """
    model = OnSiteAttendance
    template_name = 'attendancereport/onsiteattendance/_attendance_table.html'
    context_object_name = 'onsite_attendances' # Renamed for clarity

    def get_queryset(self):
        # In a real app, company_id would come from authenticated user or settings
        company_id = self.request.session.get('compid', 1)

        # Bind the form with current GET parameters to validate and get cleaned data
        form = OnSiteAttendanceFilterForm(self.request.GET, company_id=company_id)
        
        if form.is_valid():
            # Extract cleaned data for filtering
            fin_year_id = form.cleaned_data.get('year')
            month = form.cleaned_data.get('month')
            from_date = form.cleaned_data.get('from_date')
            to_date = form.cleaned_data.get('to_date')
            employee_name = form.cleaned_data.get('employee_name')
            bg_group_id = form.cleaned_data.get('bg_group')

            emp_id = None
            if employee_name:
                emp_id = HROfficeStaff.get_emp_id_from_name_code(employee_name)
                
            return OnSiteAttendance.filter_attendance(
                company_id=company_id,
                fin_year_id=fin_year_id, # Can be None
                month=month,             # Can be None
                from_date=from_date,     # Can be None
                to_date=to_date,         # Can be None
                emp_id=emp_id,           # Can be None
                bg_group_id=bg_group_id  # Can be None
            )
        # If form is invalid or not submitted with valid data, return an empty queryset
        return OnSiteAttendance.objects.none()

class MonthDropdownOptionsView(TemplateView):
    """
    HTMX endpoint to dynamically populate the month dropdown based on the selected financial year.
    Replicates DropDownList2_SelectedIndexChanged logic.
    """
    template_name = 'attendancereport/onsiteattendance/_month_options.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        selected_year_id = self.request.GET.get('year')
        months_choices = []
        if selected_year_id:
            try:
                fin_year_obj = FinancialYear.objects.get(fin_year_id=selected_year_id)
                months_data = FinancialYear.get_month_range(
                    fin_year_obj.fin_year_from.strftime('%d-%m-%Y'),
                    fin_year_obj.fin_year_to.strftime('%d-%m-%Y')
                )
                months_choices = months_data
            except FinancialYear.DoesNotExist:
                pass
        context['months'] = months_choices
        return context

class EmployeeAutocompleteView(TemplateView):
    """
    HTMX endpoint for employee name autocomplete functionality.
    Replicates sql3 WebMethod logic.
    """
    template_name = 'attendancereport/onsiteattendance/_employee_suggestions.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        prefix_text = self.request.POST.get('employee_name', '') # Use POST as per original WebMethod
        company_id = self.request.session.get('compid', 1) # Context key from original: CompId
        
        suggestions = []
        if prefix_text:
            # Search by name or employee ID
            employees = HROfficeStaff.objects.filter(
                Q(employee_name__icontains=prefix_text) | Q(emp_id__icontains=prefix_text),
                company_id=company_id
            ).order_by('employee_name')[:15] # Limit to 15 suggestions
            
            suggestions = [str(emp) for emp in employees] # Format "Name [ID]"
        
        context['suggestions'] = suggestions
        return context

# --- Placeholder CRUD Views for OnSiteAttendance (as per prompt, not from original ASP.NET) ---

class OnSiteAttendanceCreateView(CreateView):
    """
    View to create a new OnSiteAttendance record.
    """
    model = OnSiteAttendance
    form_class = OnSiteAttendanceForm
    template_name = 'attendancereport/onsiteattendance/form.html'
    success_url = reverse_lazy('onsiteattendance_list') # Redirect to the report page after action

    def form_valid(self, form):
        # Assign model fields from cleaned string fields
        form.instance.on_site_date = form.cleaned_data['on_site_date']
        form.instance.employee = form.cleaned_data['employee']
        
        response = super().form_valid(form)
        messages.success(self.request, 'OnSite Attendance added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, tells HTMX success without page reload
                headers={
                    'HX-Trigger': 'refreshOnSiteAttendanceList' # Custom event to refresh table
                }
            )
        return response

class OnSiteAttendanceUpdateView(UpdateView):
    """
    View to update an existing OnSiteAttendance record.
    """
    model = OnSiteAttendance
    form_class = OnSiteAttendanceForm
    template_name = 'attendancereport/onsiteattendance/form.html'
    success_url = reverse_lazy('onsiteattendance_list')

    def form_valid(self, form):
        # Assign model fields from cleaned string fields
        form.instance.on_site_date = form.cleaned_data['on_site_date']
        form.instance.employee = form.cleaned_data['employee']

        response = super().form_valid(form)
        messages.success(self.request, 'OnSite Attendance updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshOnSiteAttendanceList'
                }
            )
        return response

class OnSiteAttendanceDeleteView(DeleteView):
    """
    View to delete an OnSiteAttendance record.
    """
    model = OnSiteAttendance
    template_name = 'attendancereport/onsiteattendance/confirm_delete.html'
    success_url = reverse_lazy('onsiteattendance_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'OnSite Attendance deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshOnSiteAttendanceList'
                }
            )
        return response

```

#### 4.4 Templates (`attendancereport/templates/attendancereport/onsiteattendance/`)

Templates are designed for HTMX and Alpine.js interactions, assuming `core/base.html` provides the main structure and includes necessary CDN links for HTMX, Alpine.js, and DataTables.

**`list.html`** (Main page for Onsite Attendance Report)

```html
{% extends 'core/base.html' %}

{% block title %}Onsite Attendance Report{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Onsite Attendance Report</h2>
        <!-- Add New button as per general prompt requirement for CRUD example -->
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mt-4 md:mt-0"
            hx-get="{% url 'onsiteattendance_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Attendance
        </button>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <h3 class="text-lg font-semibold text-gray-700 mb-4">Filter Attendance</h3>
        <form id="filter-form" hx-get="{% url 'onsiteattendance_table' %}" hx-target="#onsiteattendance-table-container" hx-swap="innerHTML" hx-indicator="#table-loading-indicator">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {% for field in filter_form %}
                <div class="mb-2">
                    <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">{{ field.label }}:</label>
                    {{ field }}
                    {% if field.help_text %}
                    <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                    {% endif %}
                    {% if field.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
            
            <div id="employee-suggestions" class="absolute z-10 bg-white border border-gray-300 shadow-lg rounded-md mt-1 w-64">
                <!-- Autocomplete suggestions will be loaded here via HTMX -->
            </div>

            <div class="mt-6 flex justify-end">
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    <span class="htmx-indicator animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                    Search
                </button>
            </div>
        </form>
    </div>

    <div id="onsiteattendance-table-container" 
         hx-trigger="load, refreshOnSiteAttendanceList from:body" 
         hx-get="{% url 'onsiteattendance_table' %}?{{ request.GET.urlencode }}" 
         hx-swap="innerHTML">
        <!-- Initial loading indicator for the table -->
        <div id="table-loading-indicator" class="text-center htmx-indicator">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading attendance data...</p>
        </div>
    </div>
    
    <!-- Modal for CRUD operations -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <!-- Form or confirmation will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize date pickers using Alpine.js or a small JS snippet
    document.addEventListener('alpine:init', () => {
        Alpine.data('datePicker', () => ({
            init() {
                flatpickr(this.$el, {
                    dateFormat: "d-m-Y",
                    altInput: true,
                    altFormat: "d-m-Y",
                    // You might need to adjust locale settings for month names etc.
                });
            }
        }));
    });

    // Re-initialize DataTables when HTMX swaps in new content
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'onsiteattendance-table-container') {
            $('#onsiteAttendanceTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true, // Destroy existing instance if it exists
                "responsive": true,
            });
        }
    });

    // Listen for select event on autocomplete suggestions (e.g., if rendering list of buttons/divs)
    document.body.addEventListener('click', function(evt) {
        if (evt.target.matches('.autocomplete-suggestion')) {
            const employeeNameInput = document.getElementById('id_employee_name');
            if (employeeNameInput) {
                employeeNameInput.value = evt.target.dataset.value;
                document.getElementById('employee-suggestions').innerHTML = ''; // Clear suggestions
            }
        }
    });

    // Close modal on escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            const modal = document.getElementById('modal');
            if (modal && modal.classList.contains('is-active')) {
                modal.classList.remove('is-active');
            }
        }
    });
</script>
{% endblock %}
```

**`_attendance_table.html`** (Partial for HTMX-loaded DataTables)

```html
<div class="bg-white shadow-md rounded-lg p-6">
    <div class="overflow-x-auto">
        <table id="onsiteAttendanceTable" class="min-w-full bg-white table-auto">
            <thead>
                <tr class="bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <th class="py-3 px-4 border-b border-gray-200">SN</th>
                    <th class="py-3 px-4 border-b border-gray-200">Date</th>
                    <th class="py-3 px-4 border-b border-gray-200">Employee Name</th>
                    <th class="py-3 px-4 border-b border-gray-200">Employee ID</th>
                    <th class="py-3 px-4 border-b border-gray-200">Business Group</th>
                    <th class="py-3 px-4 border-b border-gray-200">Financial Year</th>
                    <th class="py-3 px-4 border-b border-gray-200">Description</th>
                    <th class="py-3 px-4 border-b border-gray-200">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for attendance in onsite_attendances %}
                <tr class="hover:bg-gray-50">
                    <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ attendance.on_site_date|date:"d-m-Y" }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ attendance.employee.employee_name }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ attendance.employee.emp_id }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ attendance.employee.bg_group.symbol|default:"N/A" }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ attendance.financial_year.fin_year }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ attendance.description|default:"-" }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">
                        <button 
                            class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md text-xs mr-2"
                            hx-get="{% url 'onsiteattendance_edit' attendance.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            Edit
                        </button>
                        <button 
                            class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md text-xs"
                            hx-get="{% url 'onsiteattendance_delete' attendance.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            Delete
                        </button>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="8" class="py-4 px-4 text-center text-gray-500">No attendance records found matching your criteria.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<script>
    // Initialize DataTables on the loaded table
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#onsiteAttendanceTable')) {
            $('#onsiteAttendanceTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true, // Important for HTMX reloads
                "responsive": true,
            });
        }
    });
</script>
```

**`_month_options.html`** (Partial for HTMX-loaded month dropdown options)

```html
<option value="">Select</option>
{% for value, text in months %}
<option value="{{ value }}">{{ text }}</option>
{% endfor %}
```

**`_employee_suggestions.html`** (Partial for HTMX-loaded autocomplete suggestions)

```html
{% if suggestions %}
    <div class="p-2 border-t border-gray-200">
        {% for suggestion in suggestions %}
            <button type="button" class="autocomplete-suggestion block w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100" data-value="{{ suggestion }}">
                {{ suggestion }}
            </button>
        {% endfor %}
    </div>
{% endif %}
```

**`form.html`** (Partial for CRUD forms loaded in modal)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} OnSite Attendance</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-submit-indicator">
        {% csrf_token %}
        
        <div class="space-y-4">
            <div class="mb-4">
                <label for="{{ form.on_site_date_str.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.on_site_date_str.label }}
                </label>
                <input type="text" id="{{ form.on_site_date_str.id_for_label }}" name="{{ form.on_site_date_str.name }}" value="{{ form.on_site_date_str.value|default_if_none:'' }}" class="{{ form.on_site_date_str.css_classes }} datepicker" placeholder="{{ form.on_site_date_str.field.widget.attrs.placeholder }}" x-data="datePicker">
                {% if form.on_site_date_str.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.on_site_date_str.errors }}</p>
                {% endif %}
            </div>
            
            <div class="mb-4">
                <label for="{{ form.employee_full_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.employee_full_name.label }}
                </label>
                <input type="text" id="{{ form.employee_full_name.id_for_label }}" name="{{ form.employee_full_name.name }}" value="{{ form.employee_full_name.value|default_if_none:'' }}" class="{{ form.employee_full_name.css_classes }}" 
                    hx-post="{{ form.employee_full_name.field.widget.attrs.hx-post }}" 
                    hx-trigger="{{ form.employee_full_name.field.widget.attrs.hx-trigger }}" 
                    hx-target="{{ form.employee_full_name.field.widget.attrs.hx-target }}" 
                    hx-swap="{{ form.employee_full_name.field.widget.attrs.hx-swap }}" 
                    hx-indicator="{{ form.employee_full_name.field.widget.attrs.hx-indicator }}">
                <div id="employee-full-suggestions" class="absolute z-10 bg-white border border-gray-300 shadow-lg rounded-md mt-1 w-64">
                    <!-- Autocomplete suggestions for employee -->
                </div>
                {% if form.employee_full_name.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.employee_full_name.errors }}</p>
                {% endif %}
            </div>

            {% for field in form %}
            {% if field.name != 'on_site_date_str' and field.name != 'employee_full_name' %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endif %}
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                <span id="form-submit-indicator" class="htmx-indicator animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                Save
            </button>
        </div>
    </form>
</div>
<script>
    // Re-initialize date picker for fields in the modal
    document.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'modalContent') {
            document.querySelectorAll('#modalContent .datepicker').forEach(el => {
                flatpickr(el, {
                    dateFormat: "d-m-Y",
                    altInput: true,
                    altFormat: "d-m-Y",
                });
            });
             // Re-attach autocomplete suggestion click listener for the form
            document.querySelectorAll('#employee-full-suggestions .autocomplete-suggestion').forEach(button => {
                button.addEventListener('click', function() {
                    const employeeFullNameInput = document.getElementById('id_employee_full_name');
                    if (employeeFullNameInput) {
                        employeeFullNameInput.value = this.dataset.value;
                        document.getElementById('employee-full-suggestions').innerHTML = '';
                    }
                });
            });
        }
    });

    // Listener for autocomplete suggestions within the modal form itself
    document.body.addEventListener('click', function(evt) {
        if (evt.target.matches('#employee-full-suggestions .autocomplete-suggestion')) {
            const employeeFullNameInput = document.getElementById('id_employee_full_name');
            if (employeeFullNameInput) {
                employeeFullNameInput.value = evt.target.dataset.value;
                document.getElementById('employee-full-suggestions').innerHTML = ''; // Clear suggestions
            }
        }
    });
</script>
```

**`confirm_delete.html`** (Partial for delete confirmation in modal)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the attendance record for <strong>{{ object.employee.employee_name }}</strong> on <strong>{{ object.on_site_date|date:"d-m-Y" }}</strong>?</p>
    
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`attendancereport/urls.py`)

Defines URL patterns for all views, including HTMX-specific endpoints.

```python
from django.urls import path
from .views import (
    OnSiteAttendanceListView,
    OnSiteAttendanceTablePartialView,
    MonthDropdownOptionsView,
    EmployeeAutocompleteView,
    OnSiteAttendanceCreateView,
    OnSiteAttendanceUpdateView,
    OnSiteAttendanceDeleteView
)

urlpatterns = [
    # Main reporting/filtering page
    path('onsiteattendance/', OnSiteAttendanceListView.as_view(), name='onsiteattendance_list'),
    
    # HTMX endpoints for dynamic updates and partials
    path('onsiteattendance/table/', OnSiteAttendanceTablePartialView.as_view(), name='onsiteattendance_table'),
    path('onsiteattendance/get-months/', MonthDropdownOptionsView.as_view(), name='get_months_options'),
    path('onsiteattendance/autocomplete-employee/', EmployeeAutocompleteView.as_view(), name='autocomplete_employee'),

    # CRUD paths for OnSiteAttendance (as per prompt's general requirements)
    path('onsiteattendance/add/', OnSiteAttendanceCreateView.as_view(), name='onsiteattendance_add'),
    path('onsiteattendance/edit/<int:pk>/', OnSiteAttendanceUpdateView.as_view(), name='onsiteattendance_edit'),
    path('onsiteattendance/delete/<int:pk>/', OnSiteAttendanceDeleteView.as_view(), name='onsiteattendance_delete'),
]
```

#### 4.6 Tests (`attendancereport/tests.py`)

Comprehensive tests for model methods and view functionality, including HTMX interactions.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date
from unittest.mock import patch

from .models import FinancialYear, BusinessGroup, HROfficeStaff, OnSiteAttendance

class OnSiteAttendanceModelTest(TestCase):
    """
    Unit tests for the OnSiteAttendance models and their methods.
    """
    @classmethod
    def setUpTestData(cls):
        # Create dummy data for all related models
        cls.company_id = 101
        cls.fin_year_obj1 = FinancialYear.objects.create(
            fin_year_id=2023, fin_year="2023-24", fin_year_from=date(2023, 4, 1), fin_year_to=date(2024, 3, 31), company_id=cls.company_id
        )
        cls.fin_year_obj2 = FinancialYear.objects.create(
            fin_year_id=2022, fin_year="2022-23", fin_year_from=date(2022, 4, 1), fin_year_to=date(2023, 3, 31), company_id=cls.company_id
        )
        cls.bg_group_obj = BusinessGroup.objects.create(id=1, symbol="DEV")
        cls.emp_obj1 = HROfficeStaff.objects.create(emp_id="EMP001", employee_name="John Doe", company_id=cls.company_id, bg_group=cls.bg_group_obj)
        cls.emp_obj2 = HROfficeStaff.objects.create(emp_id="EMP002", employee_name="Jane Smith", company_id=cls.company_id, bg_group=cls.bg_group_obj)
        
        # Create test OnSiteAttendance instances
        OnSiteAttendance.objects.create(
            on_site_date=date(2023, 5, 10), financial_year=cls.fin_year_obj1, employee=cls.emp_obj1, description="Client visit"
        )
        OnSiteAttendance.objects.create(
            on_site_date=date(2023, 5, 15), financial_year=cls.fin_year_obj1, employee=cls.emp_obj2, description="Project meeting"
        )
        OnSiteAttendance.objects.create(
            on_site_date=date(2022, 12, 20), financial_year=cls.fin_year_obj2, employee=cls.emp_obj1, description="Year end review"
        )

    def test_financial_year_creation(self):
        self.assertEqual(FinancialYear.objects.count(), 2)
        fin_year = FinancialYear.objects.get(fin_year_id=2023)
        self.assertEqual(fin_year.fin_year, "2023-24")
        self.assertEqual(fin_year.company_id, self.company_id)

    def test_business_group_creation(self):
        self.assertEqual(BusinessGroup.objects.count(), 1)
        bg = BusinessGroup.objects.get(id=1)
        self.assertEqual(bg.symbol, "DEV")

    def test_hr_office_staff_creation(self):
        self.assertEqual(HROfficeStaff.objects.count(), 2)
        emp = HROfficeStaff.objects.get(emp_id="EMP001")
        self.assertEqual(emp.employee_name, "John Doe")
        self.assertEqual(emp.bg_group, self.bg_group_obj)

    def test_onsite_attendance_creation(self):
        self.assertEqual(OnSiteAttendance.objects.count(), 3)
        attendance = OnSiteAttendance.objects.get(on_site_date=date(2023, 5, 10))
        self.assertEqual(attendance.employee, self.emp_obj1)
        self.assertEqual(attendance.financial_year, self.fin_year_obj1)

    def test_financial_year_get_month_range(self):
        months = FinancialYear.get_month_range("01-04-2023", "31-03-2024")
        self.assertEqual(len(months), 12)
        self.assertEqual(months[0], ('4', 'April'))
        self.assertEqual(months[-1], ('3', 'March'))

    def test_hr_office_staff_get_emp_id_from_name_code(self):
        emp_id = HROfficeStaff.get_emp_id_from_name_code("John Doe [EMP001]")
        self.assertEqual(emp_id, "EMP001")
        self.assertIsNone(HROfficeStaff.get_emp_id_from_name_code("John Doe"))

    def test_onsite_attendance_filter_attendance_no_filters(self):
        filtered = OnSiteAttendance.filter_attendance(company_id=self.company_id)
        self.assertEqual(filtered.count(), 3) # All attendances for the company

    def test_onsite_attendance_filter_attendance_by_year(self):
        filtered = OnSiteAttendance.filter_attendance(company_id=self.company_id, fin_year_id=self.fin_year_obj1.fin_year_id)
        self.assertEqual(filtered.count(), 2)

    def test_onsite_attendance_filter_attendance_by_month(self):
        filtered = OnSiteAttendance.filter_attendance(company_id=self.company_id, month=5)
        self.assertEqual(filtered.count(), 2)

    def test_onsite_attendance_filter_attendance_by_date_range(self):
        filtered = OnSiteAttendance.filter_attendance(company_id=self.company_id, from_date=date(2023, 5, 1), to_date=date(2023, 5, 31))
        self.assertEqual(filtered.count(), 2)

    def test_onsite_attendance_filter_attendance_by_employee(self):
        filtered = OnSiteAttendance.filter_attendance(company_id=self.company_id, emp_id=self.emp_obj1.emp_id)
        self.assertEqual(filtered.count(), 2)

    def test_onsite_attendance_filter_attendance_by_bg_group(self):
        filtered = OnSiteAttendance.filter_attendance(company_id=self.company_id, bg_group_id=self.bg_group_obj.id)
        self.assertEqual(filtered.count(), 3) # Both employees are in DEV group

class OnSiteAttendanceViewsTest(TestCase):
    """
    Integration tests for OnSiteAttendance views.
    """
    @classmethod
    def setUpTestData(cls):
        # Setup common data
        cls.company_id = 101
        cls.fin_year_obj1 = FinancialYear.objects.create(
            fin_year_id=2023, fin_year="2023-24", fin_year_from=date(2023, 4, 1), fin_year_to=date(2024, 3, 31), company_id=cls.company_id
        )
        cls.fin_year_obj2 = FinancialYear.objects.create(
            fin_year_id=2022, fin_year="2022-23", fin_year_from=date(2022, 4, 1), fin_year_to=date(2023, 3, 31), company_id=cls.company_id
        )
        cls.bg_group_obj = BusinessGroup.objects.create(id=1, symbol="DEV")
        cls.emp_obj1 = HROfficeStaff.objects.create(emp_id="EMP001", employee_name="John Doe", company_id=cls.company_id, bg_group=cls.bg_group_obj)
        cls.emp_obj2 = HROfficeStaff.objects.create(emp_id="EMP002", employee_name="Jane Smith", company_id=cls.company_id, bg_group=cls.bg_group_obj)
        cls.attendance1 = OnSiteAttendance.objects.create(
            on_site_date=date(2023, 5, 10), financial_year=cls.fin_year_obj1, employee=cls.emp_obj1, description="Client visit"
        )
        cls.attendance2 = OnSiteAttendance.objects.create(
            on_site_date=date(2023, 5, 15), financial_year=cls.fin_year_obj1, employee=cls.emp_obj2, description="Project meeting"
        )

    def setUp(self):
        self.client = Client()
        # Mock session compid as it's used in views
        session = self.client.session
        session['compid'] = self.company_id
        session.save()

    def test_list_view_get(self):
        response = self.client.get(reverse('onsiteattendance_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'attendancereport/onsiteattendance/list.html')
        self.assertIn('filter_form', response.context)
        self.assertIsInstance(response.context['filter_form'], OnSiteAttendanceFilterForm)

    def test_table_partial_view_no_filters(self):
        response = self.client.get(reverse('onsiteattendance_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'attendancereport/onsiteattendance/_attendance_table.html')
        self.assertEqual(len(response.context['onsite_attendances']), 2) # Both attendance records

    def test_table_partial_view_with_filters(self):
        response = self.client.get(reverse('onsiteattendance_table'), {
            'year': self.fin_year_obj1.fin_year_id,
            'month': 5,
            'employee_name': 'John Doe [EMP001]' # Test employee filter
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['onsite_attendances']), 1)
        self.assertEqual(response.context['onsite_attendances'][0], self.attendance1)
        self.assertContains(response, 'John Doe')

    def test_month_dropdown_options_view(self):
        response = self.client.get(reverse('get_months_options'), {'year': self.fin_year_obj1.fin_year_id}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'attendancereport/onsiteattendance/_month_options.html')
        self.assertContains(response, '<option value="4">April</option>')
        self.assertContains(response, '<option value="3">March</option>') # For 2023-24 financial year

    def test_employee_autocomplete_view(self):
        # HTMX requests with POST for autocomplete as per original
        response = self.client.post(reverse('autocomplete_employee'), {'employee_name': 'john'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'attendancereport/onsiteattendance/_employee_suggestions.html')
        self.assertContains(response, 'John Doe [EMP001]')
        self.assertNotContains(response, 'Jane Smith')

    # --- CRUD View Tests (as per prompt's general requirements) ---
    def test_create_view_get(self):
        response = self.client.get(reverse('onsiteattendance_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'attendancereport/onsiteattendance/form.html')
        self.assertIn('form', response.context)

    def test_create_view_post_success(self):
        data = {
            'on_site_date_str': '01-01-2024',
            'financial_year': self.fin_year_obj1.fin_year_id,
            'employee_full_name': 'John Doe [EMP001]',
            'description': 'New attendance record'
        }
        response = self.client.post(reverse('onsiteattendance_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX expects 204 No Content
        self.assertTrue(OnSiteAttendance.objects.filter(on_site_date=date(2024, 1, 1), employee=self.emp_obj1).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshOnSiteAttendanceList', response.headers['HX-Trigger'])

    def test_update_view_get(self):
        response = self.client.get(reverse('onsiteattendance_edit', args=[self.attendance1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'attendancereport/onsiteattendance/form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.attendance1)

    def test_update_view_post_success(self):
        new_desc = "Updated client visit details"
        data = {
            'on_site_date_str': self.attendance1.on_site_date.strftime('%d-%m-%Y'),
            'financial_year': self.attendance1.financial_year.fin_year_id,
            'employee_full_name': str(self.attendance1.employee),
            'description': new_desc
        }
        response = self.client.post(reverse('onsiteattendance_edit', args=[self.attendance1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.attendance1.refresh_from_db()
        self.assertEqual(self.attendance1.description, new_desc)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshOnSiteAttendanceList', response.headers['HX-Trigger'])

    def test_delete_view_get(self):
        response = self.client.get(reverse('onsiteattendance_delete', args=[self.attendance1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'attendancereport/onsiteattendance/confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.attendance1)

    def test_delete_view_post_success(self):
        initial_count = OnSiteAttendance.objects.count()
        response = self.client.post(reverse('onsiteattendance_delete', args=[self.attendance1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(OnSiteAttendance.objects.count(), initial_count - 1)
        self.assertFalse(OnSiteAttendance.objects.filter(pk=self.attendance1.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshOnSiteAttendanceList', response.headers['HX-Trigger'])

```

### Step 5: HTMX and Alpine.js Integration

**Instructions for Integration:**

1.  **Base Template (`core/base.html` - NOT PROVIDED IN OUTPUT):**
    *   Ensure `core/base.html` includes CDN links for HTMX, Alpine.js, jQuery (for DataTables), DataTables CSS and JS.
    *   Example snippets for `base.html` HEAD:
        ```html
        <!-- In your core/base.html HEAD -->
        <script src="https://unpkg.com/htmx.org@1.9.10" integrity="sha384-P8Qthm2t6fUqYg3s5zD3c4D7Yp4Y4U4s4+g4s4H4o4H4" crossorigin="anonymous"></script>
        <script src="https://unpkg.com/alpinejs" defer></script>
        <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
        <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
        <!-- For datepicker, e.g., flatpickr -->
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
        <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
        <!-- Tailwind CSS assumed to be configured in your project -->
        <link href="/static/css/main.css" rel="stylesheet"> <!-- or via CDN -->
        ```
    *   Add `htmx-indicator` styling in your CSS for visual feedback during HTMX requests.

2.  **Filter Form Submission (Search Button):**
    *   The `form` element in `list.html` uses `hx-get="{% url 'onsiteattendance_table' %}"`, `hx-target="#onsiteattendance-table-container"`, and `hx-swap="innerHTML"`. This means when the form is submitted, an HTMX GET request will be made to the `onsiteattendance_table` URL, and the response (the `_attendance_table.html` partial) will replace the content of the `onsiteattendance-table-container` div.
    *   The `hx-trigger="load, refreshOnSiteAttendanceList from:body"` on the `onsiteattendance-table-container` div ensures that the table is loaded automatically on page load and refreshed whenever a `refreshOnSiteAttendanceList` custom event is triggered (e.g., after a CRUD operation).

3.  **Dynamic Month Dropdown:**
    *   The "Year" dropdown (`id_year`) in `list.html` has `hx-get="{% url 'get_months_options' %}"`, `hx-target="#id_month"`, `hx-swap="innerHTML"`, and `hx-trigger="change"`. When the user selects a year, an HTMX GET request fetches new month options from `get_months_options` view, and these options replace the content of the "Month" dropdown (`id_month`).

4.  **Employee Autocomplete:**
    *   The "Employee Name" text input in `list.html` (and `form.html`) uses `hx-post="{% url 'autocomplete_employee' %}"`, `hx-trigger="keyup changed delay:500ms, search"`, `hx-target="#employee-suggestions"`, `hx-swap="outerHTML"`. As the user types, suggestions are dynamically fetched and displayed in the `#employee-suggestions` div.
    *   Alpine.js (or simple JavaScript) is used to capture clicks on the autocomplete suggestions and populate the input field, then clear the suggestions.

5.  **Date Pickers:**
    *   The `from_date` and `to_date` (and `on_site_date_str` in CRUD form) text inputs have `class="datepicker"`. Alpine.js `x-data="datePicker"` is used in `list.html` to initialize the `flatpickr` library on these elements, providing a modern calendar UI. The `datepicker` function is defined in `extra_js` block.

6.  **DataTables Initialization:**
    *   The `_attendance_table.html` partial contains the `<table id="onsiteAttendanceTable">`.
    *   A JavaScript block within this partial initializes DataTables on this table using `$(document).ready(function() { $('#onsiteAttendanceTable').DataTable({...}); });`.
    *   Crucially, `destroy: true` is used in the DataTables initialization to ensure that if the table is swapped out and re-inserted by HTMX, the old DataTables instance is properly destroyed before a new one is created, preventing errors.
    *   A `htmx:afterSwap` event listener in `list.html` ensures that DataTables is re-initialized correctly when the table content is updated via HTMX.

7.  **CRUD Modals (for `OnSiteAttendance`):**
    *   Buttons for "Add New", "Edit", and "Delete" use HTMX `hx-get` to fetch the respective form/confirmation partials (`form.html` or `confirm_delete.html`) into the `#modalContent` div.
    *   Alpine.js (via `_="on click add .is-active to #modal"`) shows the modal.
    *   Form submissions within the modal use `hx-post` and `hx-swap="none"` (for 204 No Content response) combined with `HX-Trigger: refreshOnSiteAttendanceList` in the view's response headers. This cleanly closes the modal and refreshes the main table.
    *   Modal closing on click outside (`on click if event.target.id == 'modal' remove .is-active from me`) or 'Cancel' button is handled by Alpine.js.

### Final Notes

*   This plan provides a robust, modern, and maintainable Django solution, replacing legacy ASP.NET Web Forms postbacks with dynamic, efficient HTMX interactions.
*   The "Fat Model, Thin View" principle ensures business logic is centralized in models, making views concise and focused on orchestrating requests.
*   The emphasis on DataTables ensures excellent client-side performance for data presentation and filtering.
*   All interactions are designed to avoid full page reloads, providing a smooth user experience.
*   Placeholder `CompId` and session handling are used. In a real-world scenario, these would be integrated with Django's authentication system (e.g., storing `CompId` in the `User` profile or a related model).