## ASP.NET to Django Conversion Script: Supplier Master

This document outlines a strategic plan to modernize the existing ASP.NET Supplier Master module into a robust, scalable, and maintainable Django application. Our approach leverages automation-driven tools and methodologies to minimize manual coding, ensuring a smooth and efficient transition. We prioritize a "Fat Model, Thin View" architecture, emphasizing HTMX and Alpine.js for dynamic frontend interactions, and DataTables for enhanced data presentation.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Analysis:**
The primary table involved is `tblMM_Supplier_master`, evident from the `fun.insert` statement and the `fun.select` query for `SupplierId` generation.
Several other tables are used for lookup values via `SqlDataSource` components and cascading dropdowns:
- `tblPacking_Master` (for `DDLPF`)
- `tblExciseser_Master` (for `DDLExcies`)
- `tblVAT_Master` (for `DDLVat`)
- `tblMM_Supplier_ServiceCoverage` (for `DDLServiceCoverage`)
- `tblMM_Supplier_BusinessType` (for `CBLBusinessType`)
- `tblMM_Supplier_BusinessNature` (for `CBLBusinessNature`)
- Implicitly, for Country, State, and City dropdowns, common tables like `tblCountry`, `tblState`, `tblCity` are expected.

**Identified Table and Columns for `tblMM_Supplier_master`:**

**[TABLE_NAME] = `tblMM_Supplier_master`**

**Columns (inferred from `fun.insert` and control IDs):**
- `SysDate` (DateTime)
- `SysTime` (Time)
- `SessionId` (String)
- `CompId` (Integer)
- `FinYearId` (Integer)
- `SupplierId` (String, unique, generated by application logic)
- `SupplierName` (String)
- `ScopeOfSupply` (Text)
- `RegdAddress` (Text)
- `RegdCountry` (Integer, FK to `tblCountry`)
- `RegdState` (Integer, FK to `tblState`)
- `RegdCity` (Integer, FK to `tblCity`)
- `RegdPinNo` (String)
- `RegdContactNo` (String)
- `RegdFaxNo` (String)
- `WorkAddress` (Text)
- `WorkCountry` (Integer, FK to `tblCountry`)
- `WorkState` (Integer, FK to `tblState`)
- `WorkCity` (Integer, FK to `tblCity`)
- `WorkPinNo` (String)
- `WorkContactNo` (String)
- `WorkFaxNo` (String)
- `MaterialDelAddress` (Text)
- `MaterialDelCountry` (Integer, FK to `tblCountry`)
- `MaterialDelState` (Integer, FK to `tblState`)
- `MaterialDelCity` (Integer, FK to `tblCity`)
- `MaterialDelPinNo` (String)
- `MaterialDelContactNo` (String)
- `MaterialDelFaxNo` (String)
- `ContactPerson` (String)
- `JuridictionCode` (String)
- `Commissionurate` (String)
- `TinVatNo` (String)
- `Email` (String)
- `EccNo` (String)
- `Divn` (String)
- `TinCstNo` (String)
- `ContactNo` (String, for Contact Person)
- `Range` (String)
- `PanNo` (String)
- `TDSCode` (String)
- `Remark` (Text)
- `ModVatApplicable` (Integer, 0 or 1)
- `ModVatInvoice` (Integer, 0 or 1)
- `BankAccNo` (String)
- `BankName` (String)
- `BankBranch` (String)
- `BankAddress` (Text)
- `BankAccType` (String)
- `BusinessType` (String, comma-separated IDs)
- `BusinessNature` (String, comma-separated IDs)
- `ServiceCoverage` (Integer, FK to `tblMM_Supplier_ServiceCoverage`)
- `PF` (Integer, FK to `tblPacking_Master`)
- `ExST` (Integer, FK to `tblExciseser_Master`)
- `VAT` (Integer, FK to `tblVAT_Master`)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Analysis:**
This module is primarily for **Create** operations for new suppliers.
- **Create:** The `Submit_Click` event handler clearly performs an `INSERT` into `tblMM_Supplier_master` after generating a `SupplierId` and performing numerous validations.
- **Read:** There is no explicit list view or read operation for existing suppliers in the provided ASPX/C# code, but it's implied this form is part of a larger ERP system where suppliers would eventually be listed or managed. The `fun.select` for `SupplierId` generation is a form of read. For the Django modernization, we will implement a list view to allow management of the created suppliers.
- **Update/Delete:** Not present in the provided code, but crucial for a complete master data module. We will include placeholder Django components for these for a full CRUD implementation.

**Validation Logic:**
- **Required Fields:** `RequiredFieldValidator`s are used for almost all fields.
- **Email Format:** `RegularExpressionValidator` for `txtNewEmail`.
- **`SupplierId` Generation:** Custom logic in `Submit_Click` to generate a unique ID based on the first few characters of the supplier name and an incrementing number (e.g., "SUP001", "SUP002").
- **Cascading Dropdowns:** `DDListNewRegdCountry_SelectedIndexChanged`, etc., trigger state/city population based on parent selection.
- **Checkbox Lists:** `CBLBusinessNature_SelectedIndexChanged` and `CBLBusinessType_SelectedIndexChanged` update hidden fields with comma-separated IDs.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**
The ASPX page presents a comprehensive form for entering supplier details.

- **Input Fields:**
    - `asp:TextBox`: Used for single-line text (`txtNewSupplierName`, `txtNewRegdPinNo`, etc.) and multi-line text (`txtScopeofSupply`, `txtNewRegdAdd`, etc.).
    - `asp:DropDownList`: Used for single selection from pre-defined lists (Country, State, City, Service Coverage, PF, Excies/Service Tax, VAT). Many have `AutoPostBack="True"` indicating cascading behavior.
    - `asp:RadioButton`: Used for boolean choices (`rbMVAYes`/`rbMVANo`, `rbMVIYes`/`rbMVINo`).
    - `asp:CheckBoxList`: Used for multi-selection from pre-defined lists (Business Nature, Business Type). `AutoPostBack="True"` for updating hidden fields.
    - `asp:Button`: The `Submit` button for form submission.

- **Validation Controls:** `asp:RequiredFieldValidator` and `asp:RegularExpressionValidator` for client-side and server-side validation.

- **Hidden Fields:** `hfBusinessNature`, `hfBusinessType` store comma-separated IDs from checkbox lists.

- **Styling:** Inline CSS and references to `yui-datatable.css`, `StyleSheet.css`.

**Mapping to Django/HTMX/Alpine.js:**
- All `asp:TextBox`, `asp:DropDownList`, `asp:RadioButton`, `asp:CheckBoxList` will be replaced by standard Django form fields rendered with Tailwind CSS for styling.
- `asp:RequiredFieldValidator` will be handled by `blank=False` in models and `required=True` in forms. `asp:RegularExpressionValidator` will be handled by Django's `EmailField` validation or custom form field validators.
- Cascading dropdowns and checkbox list updates will be managed dynamically using HTMX, fetching partial HTML responses.
- The `Submit` button will trigger an HTMX form submission.
- Client-side confirmation (`confirmationAdd()`) will be replaced by an Alpine.js modal or a standard Django/HTMX confirmation pattern.
- The success alert message via `ClientScript.RegisterStartupScript` will be replaced by Django's messages framework, combined with HTMX triggers.

---

### Step 4: Generate Django Code

We will create a new Django app, let's call it `suppliers`.

#### 4.1 Models

**Task:** Create Django models based on the identified database schema. This includes the main `Supplier` model and its related lookup tables. We will implement `managed = False` to connect to existing tables and add model methods for business logic.

**File: `suppliers/models.py`**

```python
from django.db import models
from django.utils import timezone
from django.db.models.signals import pre_save
from django.dispatch import receiver
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
import re # For email validation if not using EmailField directly

# Helper Models for Lookups (assuming existing database tables)
class Country(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    country_name = models.CharField(db_column='CountryName', max_length=100) # Assuming CountryName column
    
    class Meta:
        managed = False
        db_table = 'tblCountry' # Assuming this table name for countries
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.country_name

class State(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    state_name = models.CharField(db_column='StateName', max_length=100) # Assuming StateName column
    country = models.ForeignKey(Country, models.DO_NOTHING, db_column='CountryId') # Assuming CountryId FK
    
    class Meta:
        managed = False
        db_table = 'tblState' # Assuming this table name for states
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.state_name

class City(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    city_name = models.CharField(db_column='CityName', max_length=100) # Assuming CityName column
    state = models.ForeignKey(State, models.DO_NOTHING, db_column='StateId') # Assuming StateId FK
    
    class Meta:
        managed = False
        db_table = 'tblCity' # Assuming this table name for cities
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.city_name

class PackingTerm(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)
    
    class Meta:
        managed = False
        db_table = 'tblPacking_Master'
        verbose_name = 'Packing Term'
        verbose_name_plural = 'Packing Terms'

    def __str__(self):
        return self.terms

class ExciseServiceTaxTerm(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)
    
    class Meta:
        managed = False
        db_table = 'tblExciseser_Master'
        verbose_name = 'Excise/Service Tax Term'
        verbose_name_plural = 'Excise/Service Tax Terms'

    def __str__(self):
        return self.terms

class VatTerm(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)
    
    class Meta:
        managed = False
        db_table = 'tblVAT_Master'
        verbose_name = 'VAT Term'
        verbose_name_plural = 'VAT Terms'

    def __str__(self):
        return self.terms

class ServiceCoverageType(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    type_name = models.CharField(db_column='Type', max_length=255) # Assuming 'Type' is the column name
    
    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_ServiceCoverage'
        verbose_name = 'Service Coverage Type'
        verbose_name_plural = 'Service Coverage Types'

    def __str__(self):
        return self.type_name

class BusinessType(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    type_name = models.CharField(db_column='Type', max_length=255) # Assuming 'Type' is the column name
    
    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_BusinessType'
        verbose_name = 'Business Type'
        verbose_name_plural = 'Business Types'

    def __str__(self):
        return self.type_name

class BusinessNature(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    nature_name = models.CharField(db_column='Nature', max_length=255) # Assuming 'Nature' is the column name
    
    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_BusinessNature'
        verbose_name = 'Business Nature'
        verbose_name_plural = 'Business Natures'

    def __str__(self):
        return self.nature_name

# Main Supplier Model
class Supplier(models.Model):
    # System/Audit Fields
    sys_date = models.DateField(db_column='SysDate', auto_now_add=True)
    sys_time = models.TimeField(db_column='SysTime', auto_now_add=True)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True) # Assuming string
    comp_id = models.IntegerField(db_column='CompId', default=0) # Assuming integer, default 0 or from session
    fin_year_id = models.IntegerField(db_column='FinYearId', default=0) # Assuming integer, default 0 or from session

    # Basic Supplier Info
    supplier_id = models.CharField(db_column='SupplierId', max_length=50, unique=True, blank=True, null=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    scope_of_supply = models.TextField(db_column='ScopeOfSupply')

    # Registered Office Address
    regd_address = models.TextField(db_column='RegdAddress')
    regd_country = models.ForeignKey(Country, models.DO_NOTHING, db_column='RegdCountry', related_name='regd_suppliers')
    regd_state = models.ForeignKey(State, models.DO_NOTHING, db_column='RegdState', related_name='regd_suppliers')
    regd_city = models.ForeignKey(City, models.DO_NOTHING, db_column='RegdCity', related_name='regd_suppliers')
    regd_pin_no = models.CharField(db_column='RegdPinNo', max_length=20)
    regd_contact_no = models.CharField(db_column='RegdContactNo', max_length=50)
    regd_fax_no = models.CharField(db_column='RegdFaxNo', max_length=50)

    # Works/Factory Address
    work_address = models.TextField(db_column='WorkAddress')
    work_country = models.ForeignKey(Country, models.DO_NOTHING, db_column='WorkCountry', related_name='work_suppliers')
    work_state = models.ForeignKey(State, models.DO_NOTHING, db_column='WorkState', related_name='work_suppliers')
    work_city = models.ForeignKey(City, models.DO_NOTHING, db_column='WorkCity', related_name='work_suppliers')
    work_pin_no = models.CharField(db_column='WorkPinNo', max_length=20)
    work_contact_no = models.CharField(db_column='WorkContactNo', max_length=50)
    work_fax_no = models.CharField(db_column='WorkFaxNo', max_length=50)

    # Material Delivery Address
    material_del_address = models.TextField(db_column='MaterialDelAddress')
    material_del_country = models.ForeignKey(Country, models.DO_NOTHING, db_column='MaterialDelCountry', related_name='material_del_suppliers')
    material_del_state = models.ForeignKey(State, models.DO_NOTHING, db_column='MaterialDelState', related_name='material_del_suppliers')
    material_del_city = models.ForeignKey(City, models.DO_NOTHING, db_column='MaterialDelCity', related_name='material_del_suppliers')
    material_del_pin_no = models.CharField(db_column='MaterialDelPinNo', max_length=20)
    material_del_contact_no = models.CharField(db_column='MaterialDelContactNo', max_length=50)
    material_del_fax_no = models.CharField(db_column='MaterialDelFaxNo', max_length=50)

    # Contact Person & Statutory Details
    contact_person = models.CharField(db_column='ContactPerson', max_length=255)
    juridiction_code = models.CharField(db_column='JuridictionCode', max_length=50)
    commissionurate = models.CharField(db_column='Commissionurate', max_length=50)
    tin_vat_no = models.CharField(db_column='TinVatNo', max_length=50)
    email = models.CharField(db_column='Email', max_length=255) # Use CharField to directly map, validation in form
    ecc_no = models.CharField(db_column='EccNo', max_length=50)
    divn = models.CharField(db_column='Divn', max_length=50)
    tin_cst_no = models.CharField(db_column='TinCstNo', max_length=50)
    contact_no = models.CharField(db_column='ContactNo', max_length=50) # Contact person's contact no
    range_field = models.CharField(db_column='Range', max_length=50) # Renamed to avoid 'range' keyword conflict
    pan_no = models.CharField(db_column='PanNo', max_length=50)
    tds_code = models.CharField(db_column='TDSCode', max_length=50)

    # VAT/Modvat details
    mod_vat_applicable = models.BooleanField(db_column='ModVatApplicable', default=False)
    mod_vat_invoice = models.BooleanField(db_column='ModVatInvoice', default=False)

    # Bank Details
    bank_acc_no = models.CharField(db_column='BankAccNo', max_length=50)
    bank_name = models.CharField(db_column='BankName', max_length=100)
    bank_branch = models.CharField(db_column='BankBranch', max_length=100)
    bank_address = models.TextField(db_column='BankAddress')
    bank_acc_type = models.CharField(db_column='BankAccType', max_length=50)

    # Business/Service Categories (stored as comma-separated IDs)
    business_type_ids = models.CharField(db_column='BusinessType', max_length=255, blank=True, null=True)
    business_nature_ids = models.CharField(db_column='BusinessNature', max_length=255, blank=True, null=True)

    # Foreign Keys to lookup tables
    service_coverage = models.ForeignKey(ServiceCoverageType, models.DO_NOTHING, db_column='ServiceCoverage')
    pf = models.ForeignKey(PackingTerm, models.DO_NOTHING, db_column='PF')
    ex_st = models.ForeignKey(ExciseServiceTaxTerm, models.DO_NOTHING, db_column='ExST')
    vat = models.ForeignKey(VatTerm, models.DO_NOTHING, db_column='VAT')
    
    remark = models.TextField(db_column='Remark')


    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.supplier_name or 'New Supplier'

    @classmethod
    def generate_supplier_id(cls, supplier_name, comp_id):
        """
        Generates a unique SupplierId based on supplier name and company ID,
        mimicking the original ASP.NET logic.
        """
        if not supplier_name:
            return None

        # Extract first few non-space characters from supplier name, up to 3 chars
        char_str = ''.join(c for c in supplier_name if c.isalnum())[:3].upper()

        # Query for existing SupplierIds with the same prefix and company ID
        # Order by SupplierId desc to get the highest existing number
        try:
            latest_supplier = cls.objects.filter(
                supplier_id__startswith=char_str,
                comp_id=comp_id
            ).order_by('-supplier_id').first()

            if latest_supplier and latest_supplier.supplier_id:
                # Extract numeric part, increment, and format
                numeric_part_str = re.sub(r'[^0-9]', '', latest_supplier.supplier_id)
                if numeric_part_str:
                    inc_str = int(numeric_part_str) + 1
                else:
                    inc_str = 1 # If numeric part is missing, start from 1
            else:
                inc_str = 1 # No existing suppliers with this prefix, start from 1

            return f"{char_str}{inc_str:03d}"
        except Exception as e:
            # Log the error and return None or raise a specific exception
            print(f"Error generating supplier ID: {e}")
            return None

    def get_business_types(self):
        """Returns a list of BusinessType objects selected by the supplier."""
        if not self.business_type_ids:
            return []
        ids = [int(i) for i in self.business_type_ids.split(',') if i.strip().isdigit()]
        return list(BusinessType.objects.filter(id__in=ids))

    def get_business_natures(self):
        """Returns a list of BusinessNature objects selected by the supplier."""
        if not self.business_nature_ids:
            return []
        ids = [int(i) for i in self.business_nature_ids.split(',') if i.strip().isdigit()]
        return list(BusinessNature.objects.filter(id__in=ids))

    def clean_email(self):
        """
        Custom email validation to match the ASP.NET regex,
        though Django's EmailField does this by default if used.
        The regex is: \w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*
        """
        email_regex = r"^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$"
        if not re.match(email_regex, self.email):
            raise ValidationError(_('Enter a valid email address using <NAME_EMAIL>'))

# Signal to generate SupplierId before saving
@receiver(pre_save, sender=Supplier)
def set_supplier_id(sender, instance, **kwargs):
    if not instance.supplier_id:  # Only generate if not already set (e.g., for new objects)
        # TODO: Replace with actual comp_id from session/user profile in a real application
        # For demonstration, using a placeholder comp_id
        placeholder_comp_id = 1 
        instance.supplier_id = Supplier.generate_supplier_id(instance.supplier_name, placeholder_comp_id)
        if not instance.supplier_id:
            raise ValidationError("Failed to generate a unique Supplier ID.")

```

#### 4.2 Forms

**Task:** Define a Django form for user input, including custom widgets for styling and handling complex fields like checkbox lists and cascading dropdowns.

**File: `suppliers/forms.py`**

```python
from django import forms
from .models import (
    Supplier, Country, State, City, PackingTerm, ExciseServiceTaxTerm,
    VatTerm, ServiceCoverageType, BusinessType, BusinessNature
)

class SupplierForm(forms.ModelForm):
    # Separate fields for ModVatApplicable and ModVatInvoice as Radio buttons
    mod_vat_applicable = forms.BooleanField(
        label="Mod Vat Applicable",
        required=False, # Required=False because RadioSelect defaults to no selection if not True
        widget=forms.RadioSelect(choices=[(True, 'Yes'), (False, 'No')], attrs={'class': 'inline-flex items-center space-x-4'}),
        initial=False # Equivalent to Checked="True" for "No"
    )
    mod_vat_invoice = forms.BooleanField(
        label="Mod Vat Invoice",
        required=False,
        widget=forms.RadioSelect(choices=[(True, 'Yes'), (False, 'No')], attrs={'class': 'inline-flex items-center space-x-4'}),
        initial=False
    )
    
    # Checkbox lists for many-to-many like fields, data comes from related models
    business_nature = forms.ModelMultipleChoiceField(
        queryset=BusinessNature.objects.all(),
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'space-y-1'}),
        required=False,
        label="Business Nature"
    )
    business_type = forms.ModelMultipleChoiceField(
        queryset=BusinessType.objects.all(),
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'space-y-1'}),
        required=False,
        label="Business Type"
    )

    class Meta:
        model = Supplier
        fields = [
            'supplier_name', 'scope_of_supply',
            'regd_address', 'regd_country', 'regd_state', 'regd_city', 'regd_pin_no',
            'regd_contact_no', 'regd_fax_no',
            'work_address', 'work_country', 'work_state', 'work_city', 'work_pin_no',
            'work_contact_no', 'work_fax_no',
            'material_del_address', 'material_del_country', 'material_del_state', 'material_del_city',
            'material_del_pin_no', 'material_del_contact_no', 'material_del_fax_no',
            'contact_person', 'juridiction_code', 'commissionurate', 'tin_vat_no', 'email',
            'ecc_no', 'divn', 'tin_cst_no', 'contact_no', 'range_field', 'pan_no', 'tds_code',
            'mod_vat_applicable', 'mod_vat_invoice', # These will be overridden by custom fields above
            'bank_acc_no', 'bank_name', 'bank_branch', 'bank_address', 'bank_acc_type',
            'business_nature', 'business_type', # These will be overridden by custom fields above
            'service_coverage', 'pf', 'ex_st', 'vat', 'remark'
        ]
        
        widgets = {
            'supplier_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'scope_of_supply': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-24'}),
            'regd_address': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-16'}),
            'regd_pin_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'regd_contact_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'regd_fax_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'work_address': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-16'}),
            'work_pin_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'work_contact_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'work_fax_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'material_del_address': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-16'}),
            'material_del_pin_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'material_del_contact_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'material_del_fax_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'contact_person': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'juridiction_code': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'commissionurate': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'tin_vat_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'email': forms.EmailInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'ecc_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'divn': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'tin_cst_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'contact_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'range_field': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'pan_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'tds_code': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bank_acc_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bank_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bank_branch': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bank_address': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-16'}),
            'bank_acc_type': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'remark': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-24'}),
            
            # Dropdowns for Foreign Keys
            'regd_country': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'regd_state': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'regd_city': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'work_country': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'work_state': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'work_city': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'material_del_country': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'material_del_state': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'material_del_city': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'service_coverage': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'pf': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'ex_st': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'vat': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate initial choices for Country, State, City.
        # States/Cities will be filtered via HTMX on country/state selection.
        self.fields['regd_country'].queryset = Country.objects.all()
        self.fields['work_country'].queryset = Country.objects.all()
        self.fields['material_del_country'].queryset = Country.objects.all()

        # Set initial data for checkboxes from comma-separated string if instance exists
        if self.instance.pk:
            initial_business_nature_ids = [int(i) for i in self.instance.business_nature_ids.split(',') if i.strip().isdigit()]
            self.fields['business_nature'].initial = BusinessNature.objects.filter(id__in=initial_business_nature_ids)

            initial_business_type_ids = [int(i) for i in self.instance.business_type_ids.split(',') if i.strip().isdigit()]
            self.fields['business_type'].initial = BusinessType.objects.filter(id__in=initial_business_type_ids)
            
            # Set initial for radio buttons
            self.fields['mod_vat_applicable'].initial = self.instance.mod_vat_applicable
            self.fields['mod_vat_invoice'].initial = self.instance.mod_vat_invoice

        # Set empty choices for States and Cities initially,
        # they will be populated by HTMX or on instance load.
        # This is crucial for cascading dropdowns in a create scenario.
        if not self.instance.pk: # For new forms
            self.fields['regd_state'].queryset = State.objects.none()
            self.fields['regd_city'].queryset = City.objects.none()
            self.fields['work_state'].queryset = State.objects.none()
            self.fields['work_city'].queryset = City.objects.none()
            self.fields['material_del_state'].queryset = State.objects.none()
            self.fields['material_del_city'].queryset = City.objects.none()
        else: # For edit forms, populate based on existing instance
            self.fields['regd_state'].queryset = State.objects.filter(country=self.instance.regd_country)
            self.fields['regd_city'].queryset = City.objects.filter(state=self.instance.regd_state)
            self.fields['work_state'].queryset = State.objects.filter(country=self.instance.work_country)
            self.fields['work_city'].queryset = City.objects.filter(state=self.instance.work_state)
            self.fields['material_del_state'].queryset = State.objects.filter(country=self.instance.material_del_country)
            self.fields['material_del_city'].queryset = City.objects.filter(state=self.instance.material_del_state)

    def clean(self):
        cleaned_data = super().clean()
        # Collect selected checkbox IDs as comma-separated strings for model saving
        business_nature_selected_ids = [str(obj.id) for obj in cleaned_data.get('business_nature', [])]
        self.instance.business_nature_ids = ','.join(business_nature_selected_ids)

        business_type_selected_ids = [str(obj.id) for obj in cleaned_data.get('business_type', [])]
        self.instance.business_type_ids = ','.join(business_type_selected_ids)
        
        # Manually assign boolean values from custom form fields to model instance
        self.instance.mod_vat_applicable = cleaned_data.get('mod_vat_applicable', False)
        self.instance.mod_vat_invoice = cleaned_data.get('mod_vat_invoice', False)

        return cleaned_data

```

#### 4.3 Views

**Task:** Implement CRUD operations using Django Class-Based Views (CBVs), ensuring thin views and business logic residing in models. Additional views for HTMX partials will be created for dynamic content.

**File: `suppliers/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import render
from django.template.loader import render_to_string
from .models import Supplier, State, City, Country # Import Country, State, City
from .forms import SupplierForm

# Base context mixin for common form data
class BaseSupplierContextMixin:
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Populate initial dropdowns for new forms. For existing, form init handles it.
        if not self.object: # For CreateView
            context['regd_countries'] = Country.objects.all()
            context['work_countries'] = Country.objects.all()
            context['material_del_countries'] = Country.objects.all()
        return context

class SupplierListView(ListView):
    model = Supplier
    template_name = 'suppliers/supplier/list.html'
    context_object_name = 'suppliers'

    def get_queryset(self):
        # Implement filtering/searching logic if needed later
        return Supplier.objects.all().order_by('-supplier_name') # Or by PK, based on business needs

class SupplierCreateView(BaseSupplierContextMixin, CreateView):
    model = Supplier
    form_class = SupplierForm
    template_name = 'suppliers/supplier/_supplier_form.html' # Use partial for modal
    success_url = reverse_lazy('supplier_list') # Redirect after non-HTMX submission

    def get_initial(self):
        # Set initial values for system/audit fields if needed
        # In a real app, comp_id and fin_year_id would come from the current user/session
        return {
            'session_id': self.request.user.username if self.request.user.is_authenticated else 'anonymous',
            'comp_id': 1, # Placeholder
            'fin_year_id': 1 # Placeholder
        }

    def form_valid(self, form):
        # The pre_save signal handles supplier_id generation
        # The form's clean method handles business_nature_ids and business_type_ids string conversion
        
        # This will trigger the pre_save signal where supplier_id is set.
        # Also handles session_id, comp_id, fin_year_id from initial data.
        response = super().form_valid(form)
        messages.success(self.request, 'Supplier added successfully.')
        if self.request.headers.get('HX-Request'):
            # Return 204 No Content for HTMX to trigger a client-side event
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSupplierList'
                }
            )
        return response

class SupplierUpdateView(BaseSupplierContextMixin, UpdateView):
    model = Supplier
    form_class = SupplierForm
    template_name = 'suppliers/supplier/_supplier_form.html' # Use partial for modal
    context_object_name = 'supplier'
    success_url = reverse_lazy('supplier_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Supplier updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSupplierList'
                }
            )
        return response

class SupplierDeleteView(DeleteView):
    model = Supplier
    template_name = 'suppliers/supplier/_supplier_confirm_delete.html' # Use partial for modal
    context_object_name = 'supplier'
    success_url = reverse_lazy('supplier_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Supplier deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSupplierList'
                }
            )
        return response

class SupplierTablePartialView(ListView):
    model = Supplier
    template_name = 'suppliers/supplier/_supplier_table.html'
    context_object_name = 'suppliers'

    def get_queryset(self):
        # Fetch all suppliers for DataTables
        return Supplier.objects.all().order_by('-sys_date', '-sys_time')

# HTMX endpoint for cascading dropdowns
class LoadStatesView(View):
    def get(self, request):
        country_id = request.GET.get('country_id')
        states = State.objects.filter(country_id=country_id).order_by('state_name')
        context = {'states': states}
        return render(request, 'suppliers/partials/_state_options.html', context)

class LoadCitiesView(View):
    def get(self, request):
        state_id = request.GET.get('state_id')
        cities = City.objects.filter(state_id=state_id).order_by('city_name')
        context = {'cities': cities}
        return render(request, 'suppliers/partials/_city_options.html', context)

```

#### 4.4 Templates

**Task:** Create templates for each view, leveraging `base.html` inheritance, HTMX for dynamic content, and DataTables for list presentation.

**File: `suppliers/templates/suppliers/supplier/list.html`**

```html
{% extends 'core/base.html' %}

{% block title %}Supplier Master - New{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Supplier Master - New</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'supplier_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Supplier
        </button>
    </div>
    
    <div id="supplierTable-container"
         hx-trigger="load, refreshSupplierList from:body"
         hx-get="{% url 'supplier_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="flex justify-center items-center h-48">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="ml-4 text-gray-600">Loading Suppliers...</p>
        </div>
    </div>
    
    <!-- Modal for form (Add/Edit) and Delete Confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-4xl w-full mx-4 my-8 overflow-y-auto max-h-[90vh]">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components can be defined here if needed for more complex UI state
        // Currently, direct HTMX + _hyperscript handles modal visibility
    });

    // Handle messages after HTMX forms submission
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target && event.detail.target.id === 'modalContent') {
            // Re-initialize any JS components (like DataTables if used in modal, or form-specific JS)
            // For example, if you had date pickers in the form.
        }
    });

    // Close modal when HTMX triggers a refresh or redirect (status 204 typically)
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.xhr.status === 204) {
            document.getElementById('modal').classList.remove('is-active');
            // If the modal is hidden by default, ensure it is hidden after successful form submission
            document.getElementById('modal').classList.add('hidden'); // Ensure hidden is applied
        }
    });
</script>
{% endblock %}
```

**File: `suppliers/templates/suppliers/supplier/_supplier_table.html`**

```html
<div class="overflow-x-auto shadow-md sm:rounded-lg">
    <table id="supplierTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier ID</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier Name</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Scope of Supply</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                <th scope="col" class="relative py-3 px-6">
                    <span class="sr-only">Actions</span>
                </th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in suppliers %}
            <tr>
                <td class="py-4 px-6 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ obj.supplier_id }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ obj.supplier_name }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ obj.scope_of_supply|truncatechars:50 }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ obj.email }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-right text-sm font-medium">
                    <button 
                        class="text-yellow-600 hover:text-yellow-900 mr-4"
                        hx-get="{% url 'supplier_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="text-red-600 hover:text-red-900"
                        hx-get="{% url 'supplier_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 px-6 text-center text-gray-500">No suppliers found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#supplierTable')) {
            $('#supplierTable').DataTable().destroy();
        }
        $('#supplierTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "searching": true,
            "ordering": true
        });
    });
</script>
```

**File: `suppliers/templates/suppliers/supplier/_supplier_form.html`**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Supplier</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-6">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {# Supplier Info #}
            <div class="col-span-full border-b pb-4 mb-4">
                <h4 class="text-lg font-medium text-gray-700">Basic Supplier Information</h4>
            </div>
            <div>
                <label for="{{ form.supplier_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Supplier's Name <span class="text-red-500">*</span></label>
                {{ form.supplier_name }}
                {% if form.supplier_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.supplier_name.errors }}</p>{% endif %}
            </div>
            <div class="col-span-full">
                <label for="{{ form.scope_of_supply.id_for_label }}" class="block text-sm font-medium text-gray-700">Scope of Supply <span class="text-red-500">*</span></label>
                {{ form.scope_of_supply }}
                {% if form.scope_of_supply.errors %}<p class="text-red-500 text-xs mt-1">{{ form.scope_of_supply.errors }}</p>{% endif %}
            </div>

            {# Address Details #}
            <div class="col-span-full border-b pb-4 mb-4 mt-6">
                <h4 class="text-lg font-medium text-gray-700">Address/Details</h4>
                <div class="grid grid-cols-3 gap-4 mt-2">
                    <div class="font-bold text-gray-700">REGD. OFFICE</div>
                    <div class="font-bold text-gray-700">WORKS/FACTORY</div>
                    <div class="font-bold text-gray-700">MATERIAL DELIVERY</div>
                </div>
            </div>

            {# Address Fields (Repeated for 3 sections) #}
            {# Registered Office #}
            <div>
                <label for="{{ form.regd_address.id_for_label }}" class="block text-sm font-medium text-gray-700">Regd. Address <span class="text-red-500">*</span></label>
                {{ form.regd_address }}
                {% if form.regd_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_address.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.work_address.id_for_label }}" class="block text-sm font-medium text-gray-700">Work Address <span class="text-red-500">*</span></label>
                {{ form.work_address }}
                {% if form.work_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_address.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.material_del_address.id_for_label }}" class="block text-sm font-medium text-gray-700">Material Delivery Address <span class="text-red-500">*</span></label>
                {{ form.material_del_address }}
                {% if form.material_del_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_address.errors }}</p>{% endif %}
            </div>

            {# Country Fields #}
            <div>
                <label for="{{ form.regd_country.id_for_label }}" class="block text-sm font-medium text-gray-700">Regd. Country <span class="text-red-500">*</span></label>
                <select name="{{ form.regd_country.name }}" id="{{ form.regd_country.id_for_label }}"
                        class="{{ form.regd_country.field.widget.attrs.class }}"
                        hx-get="{% url 'load_states' %}" hx-target="#{{ form.regd_state.id_for_label }}" hx-trigger="change"
                        hx-include="this">
                    <option value="">Select</option>
                    {% for country in regd_countries %} {# Use `regd_countries` from context mixin #}
                        <option value="{{ country.id }}" {% if form.regd_country.value == country.id %}selected{% endif %}>{{ country.country_name }}</option>
                    {% endfor %}
                </select>
                {% if form.regd_country.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_country.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.work_country.id_for_label }}" class="block text-sm font-medium text-gray-700">Work Country <span class="text-red-500">*</span></label>
                <select name="{{ form.work_country.name }}" id="{{ form.work_country.id_for_label }}"
                        class="{{ form.work_country.field.widget.attrs.class }}"
                        hx-get="{% url 'load_states' %}" hx-target="#{{ form.work_state.id_for_label }}" hx-trigger="change"
                        hx-include="this">
                    <option value="">Select</option>
                    {% for country in work_countries %}
                        <option value="{{ country.id }}" {% if form.work_country.value == country.id %}selected{% endif %}>{{ country.country_name }}</option>
                    {% endfor %}
                </select>
                {% if form.work_country.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_country.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.material_del_country.id_for_label }}" class="block text-sm font-medium text-gray-700">Material Del. Country <span class="text-red-500">*</span></label>
                <select name="{{ form.material_del_country.name }}" id="{{ form.material_del_country.id_for_label }}"
                        class="{{ form.material_del_country.field.widget.attrs.class }}"
                        hx-get="{% url 'load_states' %}" hx-target="#{{ form.material_del_state.id_for_label }}" hx-trigger="change"
                        hx-include="this">
                    <option value="">Select</option>
                    {% for country in material_del_countries %}
                        <option value="{{ country.id }}" {% if form.material_del_country.value == country.id %}selected{% endif %}>{{ country.country_name }}</option>
                    {% endfor %}
                </select>
                {% if form.material_del_country.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_country.errors }}</p>{% endif %}
            </div>

            {# State Fields #}
            <div>
                <label for="{{ form.regd_state.id_for_label }}" class="block text-sm font-medium text-gray-700">Regd. State <span class="text-red-500">*</span></label>
                <select name="{{ form.regd_state.name }}" id="{{ form.regd_state.id_for_label }}"
                        class="{{ form.regd_state.field.widget.attrs.class }}"
                        hx-get="{% url 'load_cities' %}" hx-target="#{{ form.regd_city.id_for_label }}" hx-trigger="change"
                        hx-include="this">
                    {% if form.regd_state.field.queryset %}
                        <option value="">Select</option>
                        {% for state in form.regd_state.field.queryset %}
                            <option value="{{ state.id }}" {% if form.regd_state.value == state.id %}selected{% endif %}>{{ state.state_name }}</option>
                        {% endfor %}
                    {% else %}
                        <option value="">Select Country First</option>
                    {% endif %}
                </select>
                {% if form.regd_state.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_state.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.work_state.id_for_label }}" class="block text-sm font-medium text-gray-700">Work State <span class="text-red-500">*</span></label>
                <select name="{{ form.work_state.name }}" id="{{ form.work_state.id_for_label }}"
                        class="{{ form.work_state.field.widget.attrs.class }}"
                        hx-get="{% url 'load_cities' %}" hx-target="#{{ form.work_city.id_for_label }}" hx-trigger="change"
                        hx-include="this">
                    {% if form.work_state.field.queryset %}
                        <option value="">Select</option>
                        {% for state in form.work_state.field.queryset %}
                            <option value="{{ state.id }}" {% if form.work_state.value == state.id %}selected{% endif %}>{{ state.state_name }}</option>
                        {% endfor %}
                    {% else %}
                        <option value="">Select Country First</option>
                    {% endif %}
                </select>
                {% if form.work_state.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_state.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.material_del_state.id_for_label }}" class="block text-sm font-medium text-gray-700">Material Del. State <span class="text-red-500">*</span></label>
                <select name="{{ form.material_del_state.name }}" id="{{ form.material_del_state.id_for_label }}"
                        class="{{ form.material_del_state.field.widget.attrs.class }}"
                        hx-get="{% url 'load_cities' %}" hx-target="#{{ form.material_del_city.id_for_label }}" hx-trigger="change"
                        hx-include="this">
                    {% if form.material_del_state.field.queryset %}
                        <option value="">Select</option>
                        {% for state in form.material_del_state.field.queryset %}
                            <option value="{{ state.id }}" {% if form.material_del_state.value == state.id %}selected{% endif %}>{{ state.state_name }}</option>
                        {% endfor %}
                    {% else %}
                        <option value="">Select Country First</option>
                    {% endif %}
                </select>
                {% if form.material_del_state.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_state.errors }}</p>{% endif %}
            </div>

            {# City Fields #}
            <div>
                <label for="{{ form.regd_city.id_for_label }}" class="block text-sm font-medium text-gray-700">Regd. City <span class="text-red-500">*</span></label>
                <select name="{{ form.regd_city.name }}" id="{{ form.regd_city.id_for_label }}"
                        class="{{ form.regd_city.field.widget.attrs.class }}">
                    {% if form.regd_city.field.queryset %}
                        <option value="">Select</option>
                        {% for city in form.regd_city.field.queryset %}
                            <option value="{{ city.id }}" {% if form.regd_city.value == city.id %}selected{% endif %}>{{ city.city_name }}</option>
                        {% endfor %}
                    {% else %}
                        <option value="">Select State First</option>
                    {% endif %}
                </select>
                {% if form.regd_city.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_city.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.work_city.id_for_label }}" class="block text-sm font-medium text-gray-700">Work City <span class="text-red-500">*</span></label>
                <select name="{{ form.work_city.name }}" id="{{ form.work_city.id_for_label }}"
                        class="{{ form.work_city.field.widget.attrs.class }}">
                    {% if form.work_city.field.queryset %}
                        <option value="">Select</option>
                        {% for city in form.work_city.field.queryset %}
                            <option value="{{ city.id }}" {% if form.work_city.value == city.id %}selected{% endif %}>{{ city.city_name }}</option>
                        {% endfor %}
                    {% else %}
                        <option value="">Select State First</option>
                    {% endif %}
                </select>
                {% if form.work_city.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_city.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.material_del_city.id_for_label }}" class="block text-sm font-medium text-gray-700">Material Del. City <span class="text-red-500">*</span></label>
                <select name="{{ form.material_del_city.name }}" id="{{ form.material_del_city.id_for_label }}"
                        class="{{ form.material_del_city.field.widget.attrs.class }}">
                    {% if form.material_del_city.field.queryset %}
                        <option value="">Select</option>
                        {% for city in form.material_del_city.field.queryset %}
                            <option value="{{ city.id }}" {% if form.material_del_city.value == city.id %}selected{% endif %}>{{ city.city_name }}</option>
                        {% endfor %}
                    {% else %}
                        <option value="">Select State First</option>
                    {% endif %}
                </select>
                {% if form.material_del_city.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_city.errors }}</p>{% endif %}
            </div>

            {# PIN, Contact, Fax Numbers #}
            <div>
                <label for="{{ form.regd_pin_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Regd. PIN No. <span class="text-red-500">*</span></label>
                {{ form.regd_pin_no }}
                {% if form.regd_pin_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_pin_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.work_pin_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Work PIN No. <span class="text-red-500">*</span></label>
                {{ form.work_pin_no }}
                {% if form.work_pin_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_pin_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.material_del_pin_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Material Del. PIN No. <span class="text-red-500">*</span></label>
                {{ form.material_del_pin_no }}
                {% if form.material_del_pin_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_pin_no.errors }}</p>{% endif %}
            </div>

            <div>
                <label for="{{ form.regd_contact_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Regd. Contact No. <span class="text-red-500">*</span></label>
                {{ form.regd_contact_no }}
                {% if form.regd_contact_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_contact_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.work_contact_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Work Contact No. <span class="text-red-500">*</span></label>
                {{ form.work_contact_no }}
                {% if form.work_contact_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_contact_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.material_del_contact_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Material Del. Contact No. <span class="text-red-500">*</span></label>
                {{ form.material_del_contact_no }}
                {% if form.material_del_contact_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_contact_no.errors }}</p>{% endif %}
            </div>

            <div>
                <label for="{{ form.regd_fax_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Regd. Fax No. <span class="text-red-500">*</span></label>
                {{ form.regd_fax_no }}
                {% if form.regd_fax_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.regd_fax_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.work_fax_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Work Fax No. <span class="text-red-500">*</span></label>
                {{ form.work_fax_no }}
                {% if form.work_fax_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.work_fax_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.material_del_fax_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Material Del. Fax No. <span class="text-red-500">*</span></label>
                {{ form.material_del_fax_no }}
                {% if form.material_del_fax_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.material_del_fax_no.errors }}</p>{% endif %}
            </div>
            
            {# Contact Person and Statutory Details #}
            <div class="col-span-full border-b pb-4 mb-4 mt-6">
                <h4 class="text-lg font-medium text-gray-700">Contact & Statutory Details</h4>
            </div>

            <div>
                <label for="{{ form.contact_person.id_for_label }}" class="block text-sm font-medium text-gray-700">Contact Person <span class="text-red-500">*</span></label>
                {{ form.contact_person }}
                {% if form.contact_person.errors %}<p class="text-red-500 text-xs mt-1">{{ form.contact_person.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700">E-mail <span class="text-red-500">*</span></label>
                {{ form.email }}
                {% if form.email.errors %}<p class="text-red-500 text-xs mt-1">{{ form.email.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.contact_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Contact No. <span class="text-red-500">*</span></label>
                {{ form.contact_no }}
                {% if form.contact_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.contact_no.errors }}</p>{% endif %}
            </div>

            <div>
                <label for="{{ form.juridiction_code.id_for_label }}" class="block text-sm font-medium text-gray-700">Juridiction Code <span class="text-red-500">*</span></label>
                {{ form.juridiction_code }}
                {% if form.juridiction_code.errors %}<p class="text-red-500 text-xs mt-1">{{ form.juridiction_code.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.ecc_no.id_for_label }}" class="block text-sm font-medium text-gray-700">ECC.No. <span class="text-red-500">*</span></label>
                {{ form.ecc_no }}
                {% if form.ecc_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.ecc_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.range_field.id_for_label }}" class="block text-sm font-medium text-gray-700">Range <span class="text-red-500">*</span></label>
                {{ form.range_field }}
                {% if form.range_field.errors %}<p class="text-red-500 text-xs mt-1">{{ form.range_field.errors }}</p>{% endif %}
            </div>

            <div>
                <label for="{{ form.commissionurate.id_for_label }}" class="block text-sm font-medium text-gray-700">Commissionurate <span class="text-red-500">*</span></label>
                {{ form.commissionurate }}
                {% if form.commissionurate.errors %}<p class="text-red-500 text-xs mt-1">{{ form.commissionurate.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.divn.id_for_label }}" class="block text-sm font-medium text-gray-700">Divn <span class="text-red-500">*</span></label>
                {{ form.divn }}
                {% if form.divn.errors %}<p class="text-red-500 text-xs mt-1">{{ form.divn.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.pan_no.id_for_label }}" class="block text-sm font-medium text-gray-700">PAN No. <span class="text-red-500">*</span></label>
                {{ form.pan_no }}
                {% if form.pan_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.pan_no.errors }}</p>{% endif %}
            </div>

            <div>
                <label for="{{ form.tin_vat_no.id_for_label }}" class="block text-sm font-medium text-gray-700">TIN/VAT No. <span class="text-red-500">*</span></label>
                {{ form.tin_vat_no }}
                {% if form.tin_vat_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.tin_vat_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.tin_cst_no.id_for_label }}" class="block text-sm font-medium text-gray-700">TIN/CST No. <span class="text-red-500">*</span></label>
                {{ form.tin_cst_no }}
                {% if form.tin_cst_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.tin_cst_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.tds_code.id_for_label }}" class="block text-sm font-medium text-gray-700">TDS Code. <span class="text-red-500">*</span></label>
                {{ form.tds_code }}
                {% if form.tds_code.errors %}<p class="text-red-500 text-xs mt-1">{{ form.tds_code.errors }}</p>{% endif %}
            </div>

            {# Mod Vat Radio Buttons #}
            <div class="col-span-full md:col-span-1">
                <label class="block text-sm font-medium text-gray-700">Mod Vat Applicable</label>
                <div class="mt-1 space-x-4">
                    {% for radio in form.mod_vat_applicable %}
                        <label class="inline-flex items-center">
                            {{ radio.tag }}
                            <span class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</span>
                        </label>
                    {% endfor %}
                </div>
            </div>
            <div class="col-span-full md:col-span-1">
                <label class="block text-sm font-medium text-gray-700">Mod Vat Invoice</label>
                <div class="mt-1 space-x-4">
                    {% for radio in form.mod_vat_invoice %}
                        <label class="inline-flex items-center">
                            {{ radio.tag }}
                            <span class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</span>
                        </label>
                    {% endfor %}
                </div>
            </div>
            <div class="col-span-full md:col-span-1"></div> {# Spacer #}

            {# Bank Details #}
            <div class="col-span-full border-b pb-4 mb-4 mt-6">
                <h4 class="text-lg font-medium text-gray-700">Bank Details</h4>
            </div>
            <div>
                <label for="{{ form.bank_acc_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Bank Acc No <span class="text-red-500">*</span></label>
                {{ form.bank_acc_no }}
                {% if form.bank_acc_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.bank_acc_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.bank_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Bank Name <span class="text-red-500">*</span></label>
                {{ form.bank_name }}
                {% if form.bank_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.bank_name.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.bank_branch.id_for_label }}" class="block text-sm font-medium text-gray-700">Bank Branch <span class="text-red-500">*</span></label>
                {{ form.bank_branch }}
                {% if form.bank_branch.errors %}<p class="text-red-500 text-xs mt-1">{{ form.bank_branch.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.bank_address.id_for_label }}" class="block text-sm font-medium text-gray-700">Bank Address <span class="text-red-500">*</span></label>
                {{ form.bank_address }}
                {% if form.bank_address.errors %}<p class="text-red-500 text-xs mt-1">{{ form.bank_address.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.bank_acc_type.id_for_label }}" class="block text-sm font-medium text-gray-700">Bank Acc Type <span class="text-red-500">*</span></label>
                {{ form.bank_acc_type }}
                {% if form.bank_acc_type.errors %}<p class="text-red-500 text-xs mt-1">{{ form.bank_acc_type.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.service_coverage.id_for_label }}" class="block text-sm font-medium text-gray-700">Service Coverage</label>
                {{ form.service_coverage }}
                {% if form.service_coverage.errors %}<p class="text-red-500 text-xs mt-1">{{ form.service_coverage.errors }}</p>{% endif %}
            </div>

            {# Other dropdowns #}
            <div>
                <label for="{{ form.pf.id_for_label }}" class="block text-sm font-medium text-gray-700">P & F</label>
                {{ form.pf }}
                {% if form.pf.errors %}<p class="text-red-500 text-xs mt-1">{{ form.pf.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.ex_st.id_for_label }}" class="block text-sm font-medium text-gray-700">Excise / Service Tax</label>
                {{ form.ex_st }}
                {% if form.ex_st.errors %}<p class="text-red-500 text-xs mt-1">{{ form.ex_st.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.vat.id_for_label }}" class="block text-sm font-medium text-gray-700">VAT/CST</label>
                {{ form.vat }}
                {% if form.vat.errors %}<p class="text-red-500 text-xs mt-1">{{ form.vat.errors }}</p>{% endif %}
            </div>

            {# Checkbox Lists #}
            <div class="col-span-full md:col-span-1">
                <label class="block text-sm font-medium text-gray-700">Business Nature</label>
                <div class="mt-1 p-2 border border-gray-300 rounded-md shadow-sm h-32 overflow-y-auto">
                    {% for checkbox in form.business_nature %}
                        <div class="flex items-center">
                            {{ checkbox.tag }}
                            <label for="{{ checkbox.id_for_label }}" class="ml-2 text-sm text-gray-700">{{ checkbox.choice_label }}</label>
                        </div>
                    {% endfor %}
                </div>
                {% if form.business_nature.errors %}<p class="text-red-500 text-xs mt-1">{{ form.business_nature.errors }}</p>{% endif %}
            </div>
            <div class="col-span-full md:col-span-1">
                <label class="block text-sm font-medium text-gray-700">Business Type</label>
                <div class="mt-1 p-2 border border-gray-300 rounded-md shadow-sm h-32 overflow-y-auto">
                    {% for checkbox in form.business_type %}
                        <div class="flex items-center">
                            {{ checkbox.tag }}
                            <label for="{{ checkbox.id_for_label }}" class="ml-2 text-sm text-gray-700">{{ checkbox.choice_label }}</label>
                        </div>
                    {% endfor %}
                </div>
                {% if form.business_type.errors %}<p class="text-red-500 text-xs mt-1">{{ form.business_type.errors }}</p>{% endif %}
            </div>
            <div class="col-span-full md:col-span-1"></div> {# Spacer #}

            {# Remarks #}
            <div class="col-span-full">
                <label for="{{ form.remark.id_for_label }}" class="block text-sm font-medium text-gray-700">Remarks <span class="text-red-500">*</span></label>
                {{ form.remark }}
                {% if form.remark.errors %}<p class="text-red-500 text-xs mt-1">{{ form.remark.errors }}</p>{% endif %}
            </div>
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
                Save Supplier
            </button>
        </div>
    </form>
</div>
```

**File: `suppliers/templates/suppliers/supplier/_supplier_confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the supplier: <strong>"{{ supplier.supplier_name }}"</strong>?</p>
    <form hx-post="{% url 'supplier_delete' supplier.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

**File: `suppliers/templates/suppliers/partials/_state_options.html`**

```html
<option value="">Select</option>
{% for state in states %}
    <option value="{{ state.id }}">{{ state.state_name }}</option>
{% endfor %}
```

**File: `suppliers/templates/suppliers/partials/_city_options.html`**

```html
<option value="">Select</option>
{% for city in cities %}
    <option value="{{ city.id }}">{{ city.city_name }}</option>
{% endfor %}
```

#### 4.5 URLs

**Task:** Define URL patterns for the views, including paths for standard CRUD operations and HTMX partials.

**File: `suppliers/urls.py`**

```python
from django.urls import path
from .views import (
    SupplierListView, SupplierCreateView, SupplierUpdateView, SupplierDeleteView,
    SupplierTablePartialView, LoadStatesView, LoadCitiesView
)

urlpatterns = [
    path('suppliers/', SupplierListView.as_view(), name='supplier_list'),
    path('suppliers/add/', SupplierCreateView.as_view(), name='supplier_add'),
    path('suppliers/edit/<int:pk>/', SupplierUpdateView.as_view(), name='supplier_edit'),
    path('suppliers/delete/<int:pk>/', SupplierDeleteView.as_view(), name='supplier_delete'),
    
    # HTMX specific endpoints
    path('suppliers/table/', SupplierTablePartialView.as_view(), name='supplier_table'),
    path('suppliers/load-states/', LoadStatesView.as_view(), name='load_states'),
    path('suppliers/load-cities/', LoadCitiesView.as_view(), name='load_cities'),
]
```

#### 4.6 Tests

**Task:** Write comprehensive unit tests for the models and integration tests for the views to ensure at least 80% test coverage.

**File: `suppliers/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch
from .models import (
    Supplier, Country, State, City, PackingTerm, ExciseServiceTaxTerm,
    VatTerm, ServiceCoverageType, BusinessType, BusinessNature
)
from .forms import SupplierForm
import datetime

class LookupModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.country1 = Country.objects.create(id=1, country_name='Country A')
        cls.state1 = State.objects.create(id=101, state_name='State A', country=cls.country1)
        cls.city1 = City.objects.create(id=1001, city_name='City A', state=cls.state1)
        cls.packing_term1 = PackingTerm.objects.create(id=1, terms='Term P1')
        cls.excise_term1 = ExciseServiceTaxTerm.objects.create(id=1, terms='Term E1')
        cls.vat_term1 = VatTerm.objects.create(id=1, terms='Term V1')
        cls.service_coverage1 = ServiceCoverageType.objects.create(id=1, type_name='Type S1')
        cls.business_type1 = BusinessType.objects.create(id=1, type_name='Type B1')
        cls.business_type2 = BusinessType.objects.create(id=2, type_name='Type B2')
        cls.business_nature1 = BusinessNature.objects.create(id=1, nature_name='Nature N1')
        cls.business_nature2 = BusinessNature.objects.create(id=2, nature_name='Nature N2')

    def test_country_str_representation(self):
        self.assertEqual(str(self.country1), 'Country A')

    def test_state_str_representation(self):
        self.assertEqual(str(self.state1), 'State A')

    def test_city_str_representation(self):
        self.assertEqual(str(self.city1), 'City A')
    
    # Add more tests for other lookup models' __str__ methods and field attributes
    def test_packing_term_str(self):
        self.assertEqual(str(self.packing_term1), 'Term P1')

    def test_business_type_str(self):
        self.assertEqual(str(self.business_type1), 'Type B1')


class SupplierModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dependent lookup objects for FKs
        cls.country = Country.objects.create(id=1, country_name='Test Country')
        cls.state = State.objects.create(id=1, state_name='Test State', country=cls.country)
        cls.city = City.objects.create(id=1, city_name='Test City', state=cls.state)
        cls.packing_term = PackingTerm.objects.create(id=1, terms='Test Packing')
        cls.excise_term = ExciseServiceTaxTerm.objects.create(id=1, terms='Test Excise')
        cls.vat_term = VatTerm.objects.create(id=1, terms='Test VAT')
        cls.service_coverage = ServiceCoverageType.objects.create(id=1, type_name='Test Service')
        cls.business_type_1 = BusinessType.objects.create(id=1, type_name='Business Type One')
        cls.business_type_2 = BusinessType.objects.create(id=2, type_name='Business Type Two')
        cls.business_nature_1 = BusinessNature.objects.create(id=1, nature_name='Business Nature One')
        cls.business_nature_2 = BusinessNature.objects.create(id=2, nature_name='Business Nature Two')

        # Create a sample supplier instance
        cls.supplier_data = {
            'supplier_name': 'Test Supplier Inc',
            'scope_of_supply': 'Various materials',
            'regd_address': '123 Regd St', 'regd_country': cls.country, 'regd_state': cls.state, 'regd_city': cls.city,
            'regd_pin_no': '10001', 'regd_contact_no': '111-222-3333', 'regd_fax_no': '111-222-3334',
            'work_address': '456 Work Ave', 'work_country': cls.country, 'work_state': cls.state, 'work_city': cls.city,
            'work_pin_no': '20002', 'work_contact_no': '444-555-6666', 'work_fax_no': '444-555-6667',
            'material_del_address': '789 Del Rd', 'material_del_country': cls.country, 'material_del_state': cls.state, 'material_del_city': cls.city,
            'material_del_pin_no': '30003', 'material_del_contact_no': '777-888-9999', 'material_del_fax_no': '777-888-9990',
            'contact_person': 'John Doe', 'juridiction_code': 'JUR1', 'commissionurate': 'COM1',
            'tin_vat_no': 'TIN1', 'email': '<EMAIL>', 'ecc_no': 'ECC1', 'divn': 'DIV1',
            'tin_cst_no': 'CST1', 'contact_no': '**********', 'range_field': 'RNG1', 'pan_no': 'PAN1',
            'tds_code': 'TDS1', 'mod_vat_applicable': True, 'mod_vat_invoice': False,
            'bank_acc_no': 'ACC123', 'bank_name': 'Bank A', 'bank_branch': 'Branch A',
            'bank_address': 'Bank Addr A', 'bank_acc_type': 'Savings',
            'business_type_ids': '1,2', # Comma separated IDs
            'business_nature_ids': '1',
            'service_coverage': cls.service_coverage, 'pf': cls.packing_term,
            'ex_st': cls.excise_term, 'vat': cls.vat_term, 'remark': 'Initial remarks'
        }
        cls.supplier = Supplier.objects.create(pk=1, **cls.supplier_data)
        
        # Create another supplier for ID generation tests
        cls.supplier2 = Supplier.objects.create(
            pk=2,
            supplier_name='Another Test Supplier',
            scope_of_supply='More materials',
            regd_address='Regd Addr', regd_country=cls.country, regd_state=cls.state, regd_city=cls.city,
            regd_pin_no='123', regd_contact_no='123', regd_fax_no='123',
            work_address='Work Addr', work_country=cls.country, work_state=cls.state, work_city=cls.city,
            work_pin_no='123', work_contact_no='123', work_fax_no='123',
            material_del_address='Mat Del Addr', material_del_country=cls.country, material_del_state=cls.state, material_del_city=cls.city,
            material_del_pin_no='123', material_del_contact_no='123', material_del_fax_no='123',
            contact_person='Jane Doe', juridiction_code='JUR2', commissionurate='COM2',
            tin_vat_no='TIN2', email='<EMAIL>', ecc_no='ECC2', divn='DIV2',
            tin_cst_no='CST2', contact_no='**********', range_field='RNG2', pan_no='PAN2',
            tds_code='TDS2', mod_vat_applicable=False, mod_vat_invoice=True,
            bank_acc_no='ACC456', bank_name='Bank B', bank_branch='Branch B',
            bank_address='Bank Addr B', bank_acc_type='Current',
            business_type_ids='1',
            business_nature_ids='2',
            service_coverage=cls.service_coverage, pf=cls.packing_term,
            ex_st=cls.excise_term, vat=cls.vat_term, remark='Another remarks',
            supplier_id='ANO001', # Manually set for testing next generation
            comp_id=1
        )

    def test_supplier_creation(self):
        supplier = Supplier.objects.get(pk=1)
        self.assertEqual(supplier.supplier_name, 'Test Supplier Inc')
        self.assertEqual(supplier.email, '<EMAIL>')
        self.assertTrue(supplier.mod_vat_applicable)
        self.assertFalse(supplier.mod_vat_invoice)
        self.assertEqual(supplier.regd_country.country_name, 'Test Country')

    def test_supplier_str_representation(self):
        supplier = Supplier.objects.get(pk=1)
        self.assertEqual(str(supplier), 'Test Supplier Inc')

    def test_get_business_types(self):
        supplier = Supplier.objects.get(pk=1)
        business_types = supplier.get_business_types()
        self.assertEqual(len(business_types), 2)
        self.assertIn(self.business_type_1, business_types)
        self.assertIn(self.business_type_2, business_types)
        
    def test_get_business_natures(self):
        supplier = Supplier.objects.get(pk=1)
        business_natures = supplier.get_business_natures()
        self.assertEqual(len(business_natures), 1)
        self.assertIn(self.business_nature_1, business_natures)

    def test_generate_supplier_id(self):
        # Test case 1: First supplier with prefix
        generated_id_1 = Supplier.generate_supplier_id('New Corp', 1)
        self.assertEqual(generated_id_1, 'NEW001')

        # Test case 2: Next supplier with same prefix
        # Need to create an existing supplier with the same prefix first
        Supplier.objects.create(
            pk=3,
            supplier_name='New Company One',
            scope_of_supply='dummy', regd_address='dummy', regd_country=self.country, regd_state=self.state, regd_city=self.city,
            regd_pin_no='1', regd_contact_no='1', regd_fax_no='1', work_address='dummy', work_country=self.country, work_state=self.state, work_city=self.city,
            work_pin_no='1', work_contact_no='1', work_fax_no='1', material_del_address='dummy', material_del_country=self.country, material_del_state=self.state, material_del_city=self.city,
            material_del_pin_no='1', material_del_contact_no='1', material_del_fax_no='1', contact_person='dummy', juridiction_code='dummy', commissionurate='dummy',
            tin_vat_no='dummy', email='<EMAIL>', ecc_no='dummy', divn='dummy', tin_cst_no='dummy', contact_no='1', range_field='dummy', pan_no='dummy',
            tds_code='dummy', mod_vat_applicable=False, mod_vat_invoice=False, bank_acc_no='dummy', bank_name='dummy', bank_branch='dummy',
            bank_address='dummy', bank_acc_type='dummy', business_type_ids='1', business_nature_ids='1',
            service_coverage=self.service_coverage, pf=self.packing_term, ex_st=self.excise_term, vat=self.vat_term, remark='dummy',
            supplier_id='NEW001',
            comp_id=1
        )
        generated_id_2 = Supplier.generate_supplier_id('New Enterprise', 1)
        self.assertEqual(generated_id_2, 'NEW002')

        # Test case 3: Different company ID
        generated_id_3 = Supplier.generate_supplier_id('New Company', 2)
        self.assertEqual(generated_id_3, 'NEW001') # Should start from 001 for a new comp_id

        # Test case 4: Supplier name with special characters
        generated_id_4 = Supplier.generate_supplier_id('My_Test-Supplier!', 1)
        self.assertEqual(generated_id_4, 'MYT001') # Assuming 3rd supplier with MYT prefix
        
        # Test case 5: Supplier name with less than 3 relevant characters
        generated_id_5 = Supplier.generate_supplier_id('A B', 1)
        self.assertEqual(generated_id_5, 'AB001')


    def test_clean_email_valid(self):
        supplier = Supplier(email='<EMAIL>')
        try:
            supplier.clean_email()
            self.assertTrue(True) # No exception means valid
        except ValidationError:
            self.fail("clean_email raised ValidationError for a valid email.")

    def test_clean_email_invalid(self):
        supplier = Supplier(email='invalid-email')
        with self.assertRaises(ValidationError):
            supplier.clean_email()

class SupplierViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Create dependent lookup objects for FKs
        self.country = Country.objects.create(id=1, country_name='Test Country')
        self.state = State.objects.create(id=1, state_name='Test State', country=self.country)
        self.city = City.objects.create(id=1, city_name='Test City', state=self.state)
        self.packing_term = PackingTerm.objects.create(id=1, terms='Test Packing')
        self.excise_term = ExciseServiceTaxTerm.objects.create(id=1, terms='Test Excise')
        self.vat_term = VatTerm.objects.create(id=1, terms='Test VAT')
        self.service_coverage = ServiceCoverageType.objects.create(id=1, type_name='Test Service')
        self.business_type_1 = BusinessType.objects.create(id=1, type_name='Business Type One')
        self.business_type_2 = BusinessType.objects.create(id=2, type_name='Business Type Two')
        self.business_nature_1 = BusinessNature.objects.create(id=1, nature_name='Business Nature One')
        self.business_nature_2 = BusinessNature.objects.create(id=2, nature_name='Business Nature Two')

        self.supplier_data = {
            'supplier_name': 'Existing Supplier Corp',
            'scope_of_supply': 'Various existing materials',
            'regd_address': 'Existing Regd St', 'regd_country': self.country, 'regd_state': self.state, 'regd_city': self.city,
            'regd_pin_no': '11111', 'regd_contact_no': '111-111-1111', 'regd_fax_no': '111-111-1112',
            'work_address': 'Existing Work Ave', 'work_country': self.country, 'work_state': self.state, 'work_city': self.city,
            'work_pin_no': '22222', 'work_contact_no': '222-222-2222', 'work_fax_no': '222-222-2223',
            'material_del_address': 'Existing Del Rd', 'material_del_country': self.country, 'material_del_state': self.state, 'material_del_city': self.city,
            'material_del_pin_no': '33333', 'material_del_contact_no': '333-333-3333', 'material_del_fax_no': '333-333-3334',
            'contact_person': 'Jane Doe', 'juridiction_code': 'EXJUR', 'commissionurate': 'EXCOM',
            'tin_vat_no': 'EXTIN', 'email': '<EMAIL>', 'ecc_no': 'EXECC', 'divn': 'EXDIV',
            'tin_cst_no': 'EXCST', 'contact_no': '**********', 'range_field': 'EXRNG', 'pan_no': 'EXPAN',
            'tds_code': 'EXTDS', 'mod_vat_applicable': True, 'mod_vat_invoice': False,
            'bank_acc_no': 'EXACC', 'bank_name': 'ExBank', 'bank_branch': 'ExBranch',
            'bank_address': 'ExBank Addr', 'bank_acc_type': 'ExType',
            'business_type_ids': '1',
            'business_nature_ids': '2',
            'service_coverage': self.service_coverage, 'pf': self.packing_term,
            'ex_st': self.excise_term, 'vat': self.vat_term, 'remark': 'Existing remarks',
            'supplier_id': 'EXS001',
            'comp_id': 1
        }
        self.supplier = Supplier.objects.create(pk=1, **self.supplier_data)

    def test_supplier_list_view(self):
        response = self.client.get(reverse('supplier_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'suppliers/supplier/list.html')
        self.assertIn('suppliers', response.context)
        self.assertContains(response, 'Existing Supplier Corp')

    def test_supplier_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('supplier_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'suppliers/supplier/_supplier_table.html')
        self.assertContains(response, 'Existing Supplier Corp')
        self.assertContains(response, 'id="supplierTable"')

    def test_supplier_create_view_get(self):
        response = self.client.get(reverse('supplier_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'suppliers/supplier/_supplier_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Supplier')

    @patch('suppliers.models.Supplier.generate_supplier_id', return_value='NEW001')
    def test_supplier_create_view_post_htmx_success(self, mock_generate_id):
        # Create a new dictionary for post data to avoid modifying self.supplier_data
        new_supplier_data = self.supplier_data.copy()
        new_supplier_data['supplier_name'] = 'New Test Supplier'
        new_supplier_data.pop('supplier_id') # Should be generated
        # Convert FK objects to their IDs for form submission
        for field in ['regd_country', 'regd_state', 'regd_city', 'work_country', 'work_state', 'work_city',
                     'material_del_country', 'material_del_state', 'material_del_city',
                     'service_coverage', 'pf', 'ex_st', 'vat']:
            new_supplier_data[field] = new_supplier_data[field].id
        
        # Checkbox values as lists of IDs
        new_supplier_data['business_nature'] = [self.business_nature_1.id]
        new_supplier_data['business_type'] = [self.business_type_1.id, self.business_type_2.id]

        response = self.client.post(reverse('supplier_add'), new_supplier_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX No Content response
        self.assertFalse(Supplier.objects.filter(supplier_name='Existing Supplier Corp').exists()) # Ensure it's not the old one
        self.assertTrue(Supplier.objects.filter(supplier_name='New Test Supplier').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshSupplierList', response.headers['HX-Trigger'])
        mock_generate_id.assert_called_once_with('New Test Supplier', 1) # Check if supplier ID was generated

        # Verify complex fields saved correctly
        created_supplier = Supplier.objects.get(supplier_name='New Test Supplier')
        self.assertEqual(created_supplier.business_type_ids, '1,2')
        self.assertEqual(created_supplier.mod_vat_applicable, new_supplier_data['mod_vat_applicable'])


    def test_supplier_create_view_post_invalid(self):
        invalid_data = self.supplier_data.copy()
        invalid_data['supplier_name'] = ''  # Missing required field
        response = self.client.post(reverse('supplier_add'), invalid_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Should render form again with errors
        self.assertTemplateUsed(response, 'suppliers/supplier/_supplier_form.html')
        self.assertContains(response, 'This field is required')
        self.assertFalse(Supplier.objects.filter(supplier_name='').exists())


    def test_supplier_update_view_get(self):
        response = self.client.get(reverse('supplier_edit', args=[self.supplier.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'suppliers/supplier/_supplier_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Edit Supplier')
        self.assertContains(response, 'Existing Supplier Corp')

    def test_supplier_update_view_post_htmx_success(self):
        updated_data = self.supplier_data.copy()
        updated_data['supplier_name'] = 'Updated Supplier Name'
        updated_data['email'] = '<EMAIL>'
        # Convert FK objects to their IDs for form submission
        for field in ['regd_country', 'regd_state', 'regd_city', 'work_country', 'work_state', 'work_city',
                     'material_del_country', 'material_del_state', 'material_del_city',
                     'service_coverage', 'pf', 'ex_st', 'vat']:
            updated_data[field] = updated_data[field].id
        
        # Checkbox values as lists of IDs
        updated_data['business_nature'] = [self.business_nature_2.id] # Changed
        updated_data['business_type'] = [self.business_type_1.id] # Changed

        response = self.client.post(reverse('supplier_edit', args=[self.supplier.pk]), updated_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        
        self.supplier.refresh_from_db()
        self.assertEqual(self.supplier.supplier_name, 'Updated Supplier Name')
        self.assertEqual(self.supplier.email, '<EMAIL>')
        self.assertEqual(self.supplier.business_nature_ids, '2')


    def test_supplier_delete_view_get(self):
        response = self.client.get(reverse('supplier_delete', args=[self.supplier.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'suppliers/supplier/_supplier_confirm_delete.html')
        self.assertIn('supplier', response.context)
        self.assertContains(response, 'Confirm Delete')

    def test_supplier_delete_view_post_htmx_success(self):
        initial_count = Supplier.objects.count()
        response = self.client.post(reverse('supplier_delete', args=[self.supplier.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(Supplier.objects.count(), initial_count - 1)
        self.assertFalse(Supplier.objects.filter(pk=self.supplier.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshSupplierList', response.headers['HX-Trigger'])

    def test_load_states_view(self):
        new_country = Country.objects.create(id=2, country_name='Country B')
        state_b = State.objects.create(id=201, state_name='State B1', country=new_country)
        response = self.client.get(reverse('load_states'), {'country_id': new_country.id})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'suppliers/partials/_state_options.html')
        self.assertContains(response, '<option value="">Select</option>')
        self.assertContains(response, f'<option value="{state_b.id}">{state_b.state_name}</option>')

    def test_load_cities_view(self):
        new_country = Country.objects.create(id=3, country_name='Country C')
        new_state = State.objects.create(id=301, state_name='State C1', country=new_country)
        city_c = City.objects.create(id=3001, city_name='City C1', state=new_state)
        response = self.client.get(reverse('load_cities'), {'state_id': new_state.id})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'suppliers/partials/_city_options.html')
        self.assertContains(response, '<option value="">Select</option>')
        self.assertContains(response, f'<option value="{city_c.id}">{city_c.city_name}</option>')

```

---

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated Django templates (`_supplier_form.html`, `_supplier_table.html`, `list.html`, `_supplier_confirm_delete.html`) are designed with HTMX and Alpine.js for a modern user experience:

1.  **HTMX for CRUD operations:**
    *   **Add New Supplier:** The "Add New Supplier" button uses `hx-get` to fetch the `_supplier_form.html` (rendered by `SupplierCreateView`) into the `#modalContent` div.
    *   **Edit Supplier:** The "Edit" button in the DataTables row uses `hx-get` to fetch the `_supplier_form.html` (rendered by `SupplierUpdateView`) with the specific supplier's data into `#modalContent`.
    *   **Delete Supplier:** The "Delete" button uses `hx-get` to fetch `_supplier_confirm_delete.html` (rendered by `SupplierDeleteView`) into `#modalContent`.
    *   **Form Submission:** All forms within the modal use `hx-post` with `hx-swap="none"`. Upon successful submission (HTTP 204 No Content from Django views), the `HX-Trigger: refreshSupplierList` header is sent.
    *   **List Refresh:** The `#supplierTable-container` div in `list.html` listens for `load, refreshSupplierList from:body` and performs an `hx-get` to `{% url 'supplier_table' %}` to refresh the DataTables content without a full page reload.
    *   **Cascading Dropdowns:** Country dropdowns use `hx-get` to `{% url 'load_states' %}` and `hx-target` to update the corresponding State dropdown. Similarly, State dropdowns update City dropdowns.

2.  **Alpine.js for Modals:**
    *   The main `#modal` div is managed by a simple `_hyperscript` inline script: `_="on click if event.target.id == 'modal' remove .is-active from me"`. This allows clicking outside the modal content to close it.
    *   Buttons to open the modal (Add/Edit/Delete) use `_="on click add .is-active to #modal"`.
    *   Cancel/Close buttons in the modal forms use `_="on click remove .is-active from #modal"` to dismiss the modal.
    *   The `list.html` includes an `extra_js` block where Alpine.js could be initialized for more complex component logic, though for basic modal control, `_hyperscript` is sufficient and concise.

3.  **DataTables for List Views:**
    *   The `_supplier_table.html` partial is designed to contain a `<table>` with the ID `supplierTable`.
    *   A JavaScript snippet within this partial initializes DataTables on `$(document).ready()`, ensuring proper client-side searching, sorting, and pagination. `destroy()` is called first to handle re-initialization by HTMX.

4.  **No Full Page Reloads:** All user interactions (Add, Edit, Delete, form submissions, dropdown selections) are managed by HTMX to provide a seamless, single-page application feel without writing complex JavaScript.

This comprehensive plan addresses the migration from ASP.NET to Django, focusing on modern architecture, automation-friendly steps, and clear business value through an enhanced user experience and improved maintainability.