## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Based on the `SqlDataSource1` definition:
- The table name is `tblMM_Supplier_BusinessNature`.
- Columns identified: `Id` (used as `DataKeyNames` and in DELETE/UPDATE/SELECT WHERE clauses, implying Primary Key) and `Nature` (used in INSERT/UPDATE/SELECT/UI bindings).

### Extracted Schema:

- **[TABLE_NAME]**: `tblMM_Supplier_BusinessNature`
- **Columns**:
    - `Id`: Integer (Primary Key)
    - `Nature`: String

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

The ASP.NET code demonstrates full CRUD capabilities.

-   **Create (Insert):**
    -   Triggered by `btnInsert` buttons in the `GridView`'s `FooterTemplate` (command "Add") and `EmptyDataTemplate` (command "Add1").
    -   Handled by `GridView1_RowCommand` in the C# code-behind.
    -   Uses `SqlDataSource1.InsertCommand`: `INSERT INTO [tblMM_Supplier_BusinessNature] ([Nature]) VALUES (@Nature)`.
    -   A `RequiredFieldValidator` ensures `Nature` is not empty.

-   **Read (Select):**
    -   Data displayed in `GridView1`.
    -   Uses `SqlDataSource1.SelectCommand`: `SELECT * FROM [tblMM_Supplier_BusinessNature] ORDER BY [Id] DESC`.
    -   Paging is enabled (`AllowPaging="True"`).

-   **Update (Edit):**
    -   Triggered by the `CommandField` with `ShowEditButton="True"` in `GridView1`.
    -   Handled by `GridView1_RowUpdated` event in C# (though the actual update logic is handled by `SqlDataSource1` directly).
    -   Uses `SqlDataSource1.UpdateCommand`: `UPDATE [tblMM_Supplier_BusinessNature] SET [Nature] = @Nature WHERE [Id] = @Id`.
    -   A `RequiredFieldValidator` ensures `Nature` is not empty during edit.

-   **Delete (Delete):**
    -   Triggered by the `CommandField` with `ShowDeleteButton="True"` in `GridView1`.
    -   Handled by `GridView1_RowDeleted` event in C# (actual deletion by `SqlDataSource1`).
    -   Uses `SqlDataSource1.DeleteCommand`: `DELETE FROM [tblMM_Supplier_BusinessNature] WHERE [Id] = @Id`.
    -   Client-side confirmation via `confirmationDelete()` is present.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

-   **`GridView1`**: The primary data display control. This will be replaced by a Django template rendering an HTML table integrated with **DataTables.js** for client-side pagination, sorting, and searching.
-   **`TextBox` (`txtBusinessNature`, `lblBusinessNature0`)**: Used for inputting/editing the "Nature" field. This will map to a `forms.TextInput` widget in a Django `ModelForm`.
-   **`Button` (`btnInsert`), `LinkButton` (Edit/Delete command fields)**: These buttons trigger CRUD actions. In Django, these will be HTML `<button>` elements with **HTMX** attributes (`hx-get`, `hx-post`, `hx-target`, `hx-swap`) to facilitate dynamic form loading (for Add/Edit/Delete confirmation) and submission without full page reloads.
-   **`RequiredFieldValidator`**: Client-side and server-side validation for the "Nature" field. This will be handled by Django's `ModelForm` validation and the `required=True` attribute on the model field.
-   **`lblMessage`**: Displays success/error messages. This will be replaced by Django's built-in messages framework, displayed prominently (e.g., using Tailwind CSS alerts).
-   **Client-side JavaScript (`PopUpMsg.js`, `loadingNotifier.js`)**: These imply custom pop-up messages and loading indicators. In the modernized Django app, these functionalities will be managed by **Alpine.js** for UI state (e.g., modal visibility) and **HTMX** for loading states and dynamic content updates, eliminating the need for custom JS.
-   **`yui-datatable-theme`**: Indicates the original use of a data table library. This reinforces the recommendation to use **DataTables.js** in Django.

## Step 4: Generate Django Code

### 4.1 Models

Task: Create a Django model based on the database schema.

## Instructions:

The model `BusinessNature` will map to the `tblMM_Supplier_BusinessNature` table. Since `Id` is the primary key in the existing table, Django's default `id` field will handle this mapping automatically. We only need to explicitly define `Nature`.

**File: `sysadmin/models.py`**

```python
from django.db import models

class BusinessNature(models.Model):
    # Django's default 'id' field (AutoField) will map to the 'Id' PK column
    # in 'tblMM_Supplier_BusinessNature' automatically.
    nature = models.CharField(db_column='Nature', max_length=255) # Assuming a reasonable max_length

    class Meta:
        managed = False  # Important: Tells Django not to manage this table's schema
        db_table = 'tblMM_Supplier_BusinessNature' # Explicitly map to the existing table
        verbose_name = 'Business Nature'
        verbose_name_plural = 'Business Natures'
        ordering = ['-id'] # To match the ORDER BY [Id] DESC from the original SelectCommand

    def __str__(self):
        return self.nature

    # Business logic methods can be added here if needed, e.g.:
    def get_summary(self):
        return f"Nature: {self.nature}"

    def can_be_deleted(self):
        # Example business logic: Check if this business nature is associated with other records
        # return not self.related_objects.exists() # Placeholder for actual logic
        return True # For now, assume it can always be deleted

```

### 4.2 Forms

Task: Define a Django form for user input.

## Instructions:

A `ModelForm` will be created for `BusinessNature` to handle input for the `nature` field.

**File: `sysadmin/forms.py`**

```python
from django import forms
from .models import BusinessNature

class BusinessNatureForm(forms.ModelForm):
    class Meta:
        model = BusinessNature
        fields = ['nature']
        widgets = {
            'nature': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter Business Nature'
            }),
        }
        
    def clean_nature(self):
        # Example: Add custom validation, e.g., uniqueness check
        nature = self.cleaned_data['nature']
        # For updates, exclude the current instance from the uniqueness check
        if self.instance.pk:
            if BusinessNature.objects.filter(nature=nature).exclude(pk=self.instance.pk).exists():
                raise forms.ValidationError("This business nature already exists.")
        else: # For creation
            if BusinessNature.objects.filter(nature=nature).exists():
                raise forms.ValidationError("This business nature already exists.")
        return nature

```

### 4.3 Views

Task: Implement CRUD operations using CBVs.

## Instructions:

Standard Django Class-Based Views for ListView, CreateView, UpdateView, and DeleteView. A `TablePartialView` will be added to serve the HTMX-loaded DataTables content. Views will be thin, delegating complex logic to models or forms.

**File: `sysadmin/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import render # Used for TablePartialView
from .models import BusinessNature
from .forms import BusinessNatureForm

# This view is for the main page that loads the table via HTMX
class BusinessNatureListView(ListView):
    model = BusinessNature
    template_name = 'sysadmin/businessnature/list.html'
    context_object_name = 'businessnatures' # This is not strictly needed for the main view but good practice.

# This view serves the actual DataTables content, loaded via HTMX
class BusinessNatureTablePartialView(ListView):
    model = BusinessNature
    template_name = 'sysadmin/businessnature/_businessnature_table.html'
    context_object_name = 'businessnatures'
    # The `ordering` in the model's Meta class handles the default sort order
    # For DataTables, initial sorting can be configured in JS, but backend ordering helps for initial load.

class BusinessNatureCreateView(CreateView):
    model = BusinessNature
    form_class = BusinessNatureForm
    template_name = 'sysadmin/businessnature/_businessnature_form.html' # Partial for modal
    success_url = reverse_lazy('businessnature_list') # Fallback, HTMX will handle it

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Business Nature added successfully.')
        if self.request.headers.get('HX-Request'):
            # Return a 204 No Content response for HTMX and trigger a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBusinessNatureList'
                }
            )
        return response # Non-HTMX request fallback

    def form_invalid(self, form):
        # Render the form again if validation fails, HTMX will swap this back into the modal
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form})
        return response

class BusinessNatureUpdateView(UpdateView):
    model = BusinessNature
    form_class = BusinessNatureForm
    template_name = 'sysadmin/businessnature/_businessnature_form.html' # Partial for modal
    success_url = reverse_lazy('businessnature_list') # Fallback, HTMX will handle it

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Business Nature updated successfully.')
        if self.request.headers.get('HX-Request'):
            # Return a 204 No Content response for HTMX and trigger a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBusinessNatureList'
                }
            )
        return response # Non-HTMX request fallback

    def form_invalid(self, form):
        # Render the form again if validation fails, HTMX will swap this back into the modal
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form})
        return response

class BusinessNatureDeleteView(DeleteView):
    model = BusinessNature
    template_name = 'sysadmin/businessnature/_businessnature_confirm_delete.html' # Partial for modal
    success_url = reverse_lazy('businessnature_list') # Fallback, HTMX will handle it

    def delete(self, request, *args, **kwargs):
        # Business logic for deletion can go here in a model method
        # if not self.get_object().can_be_deleted():
        #    messages.error(self.request, "This business nature cannot be deleted.")
        #    return HttpResponse(status=400) # Or appropriate error response

        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Business Nature deleted successfully.')
        if request.headers.get('HX-Request'):
            # Return a 204 No Content response for HTMX and trigger a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBusinessNatureList'
                }
            )
        return response # Non-HTMX request fallback

```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

Templates will use HTMX for dynamic interactions and Alpine.js for modal behavior. DataTables will be initialized on the `_businessnature_table.html` partial.

**File: `sysadmin/businessnature/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Business Natures</h2>
        <button
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'businessnature_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Business Nature
        </button>
    </div>
    
    {% include 'sysadmin/messages.html' %} {# Include Django messages partial #}

    <div id="businessnatureTable-container"
         hx-trigger="load, refreshBusinessNatureList from:body"
         hx-get="{% url 'businessnature_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Business Natures...</p>
        </div>
    </div>
    
    <!-- Modal for forms/confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me
            on htmx:afterOnLoad from #modalContent if find .errorlist in #modalContent hide #modal">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4 sm:mx-0">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI state
        // For simple modal, _hyperscript handles it directly
    });

    // Handle HTMX events to hide modal after successful form submission/deletion
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.detail.xhr.status === 204) { // No Content status indicates successful HTMX action
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.remove('is-active');
            }
        }
    });

    // Close modal if escape key is pressed
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            const modal = document.getElementById('modal');
            if (modal && modal.classList.contains('is-active')) {
                modal.classList.remove('is-active');
            }
        }
    });
</script>
{% endblock %}
```

**File: `sysadmin/businessnature/_businessnature_table.html`**

```html
<div class="overflow-x-auto shadow-md sm:rounded-lg p-1">
    <table id="businessnatureTable" class="min-w-full bg-white divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nature</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% for obj in businessnatures %}
            <tr class="hover:bg-gray-50">
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.nature }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm font-medium">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md text-xs mr-2"
                        hx-get="{% url 'businessnature_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md text-xs"
                        hx-get="{% url 'businessnature_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="3" class="py-4 px-4 text-center text-sm text-gray-500">No business natures found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Ensure DataTables is initialized only once per table load
    if ($.fn.DataTable.isDataTable('#businessnatureTable')) {
        $('#businessnatureTable').DataTable().destroy();
    }
    $(document).ready(function() {
        $('#businessnatureTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "pagingType": "full_numbers"
        });
    });
</script>
```

**File: `sysadmin/businessnature/_businessnature_form.html`**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Business Nature</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-4">
        {% csrf_token %}
        
        <div>
            <label for="{{ form.nature.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.nature.label }}
            </label>
            {{ form.nature }}
            {% if form.nature.errors %}
            <p class="text-red-500 text-xs mt-1">{{ form.nature.errors }}</p>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-md text-sm"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md text-sm">
                Save
            </button>
        </div>
    </form>
</div>
```

**File: `sysadmin/businessnature/_businessnature_confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete "<strong>{{ object.nature }}</strong>"? This action cannot be undone.</p>
    
    <form hx-post="{% url 'businessnature_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-md text-sm"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-md text-sm">
                Delete
            </button>
        </div>
    </form>
</div>
```

**File: `sysadmin/messages.html` (Optional, but good for displaying Django messages)**

```html
{% if messages %}
<div id="messages-container" class="mb-4 space-y-2"
     hx-swap-oob="outerHTML:#messages-container"> {# OOB swap to refresh messages #}
    {% for message in messages %}
    <div class="p-3 rounded-md text-sm
        {% if message.tags == 'success' %}bg-green-100 text-green-700 border border-green-200
        {% elif message.tags == 'error' %}bg-red-100 text-red-700 border border-red-200
        {% elif message.tags == 'warning' %}bg-yellow-100 text-yellow-700 border border-yellow-200
        {% else %}bg-blue-100 text-blue-700 border border-blue-200{% endif %}"
        role="alert">
        {{ message }}
        <button type="button" class="ml-auto -mx-1.5 -my-1.5 bg-transparent text-gray-700 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex h-8 w-8" onclick="this.closest('div').remove();">
            <span class="sr-only">Close</span>
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
        </button>
    </div>
    {% endfor %}
</div>
{% endif %}
```

### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

Define paths for the main list view, and the HTMX endpoints for adding, editing, deleting, and loading the table partial.

**File: `sysadmin/urls.py`**

```python
from django.urls import path
from .views import (
    BusinessNatureListView,
    BusinessNatureCreateView,
    BusinessNatureUpdateView,
    BusinessNatureDeleteView,
    BusinessNatureTablePartialView,
)

urlpatterns = [
    # Main page for displaying business natures
    path('businessnature/', BusinessNatureListView.as_view(), name='businessnature_list'),
    
    # HTMX endpoints for CRUD operations (rendered in modals)
    path('businessnature/add/', BusinessNatureCreateView.as_view(), name='businessnature_add'),
    path('businessnature/edit/<int:pk>/', BusinessNatureUpdateView.as_view(), name='businessnature_edit'),
    path('businessnature/delete/<int:pk>/', BusinessNatureDeleteView.as_view(), name='businessnature_delete'),
    
    # HTMX endpoint for refreshing the DataTables content
    path('businessnature/table/', BusinessNatureTablePartialView.as_view(), name='businessnature_table'),
]
```

### 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

Comprehensive unit tests for the `BusinessNature` model and integration tests for all view functionalities, including HTMX interactions.

**File: `sysadmin/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import BusinessNature
from .forms import BusinessNatureForm

class BusinessNatureModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.business_nature_1 = BusinessNature.objects.create(nature='Finance')
        cls.business_nature_2 = BusinessNature.objects.create(nature='Manufacturing')
  
    def test_business_nature_creation(self):
        self.assertEqual(self.business_nature_1.nature, 'Finance')
        self.assertTrue(isinstance(self.business_nature_1, BusinessNature))

    def test_nature_label(self):
        field_label = self.business_nature_1._meta.get_field('nature').verbose_name
        self.assertEqual(field_label, 'Nature')

    def test_str_method(self):
        self.assertEqual(str(self.business_nature_1), 'Finance')

    def test_db_table_and_managed(self):
        self.assertEqual(BusinessNature._meta.db_table, 'tblMM_Supplier_BusinessNature')
        self.assertFalse(BusinessNature._meta.managed)

    def test_verbose_names(self):
        self.assertEqual(BusinessNature._meta.verbose_name, 'Business Nature')
        self.assertEqual(BusinessNature._meta.verbose_name_plural, 'Business Natures')

class BusinessNatureFormTest(TestCase):
    def test_valid_form(self):
        form = BusinessNatureForm(data={'nature': 'Retail'})
        self.assertTrue(form.is_valid())

    def test_invalid_form_no_nature(self):
        form = BusinessNatureForm(data={})
        self.assertFalse(form.is_valid())
        self.assertIn('nature', form.errors)

    def test_unique_nature_creation(self):
        BusinessNature.objects.create(nature='Existing Nature')
        form = BusinessNatureForm(data={'nature': 'Existing Nature'})
        self.assertFalse(form.is_valid())
        self.assertIn('This business nature already exists.', form.errors['nature'])

    def test_unique_nature_update(self):
        existing_nature = BusinessNature.objects.create(nature='Existing Nature')
        another_nature = BusinessNature.objects.create(nature='Another Nature')
        
        # Try to update 'another_nature' to 'Existing Nature'
        form = BusinessNatureForm(instance=another_nature, data={'nature': 'Existing Nature'})
        self.assertFalse(form.is_valid())
        self.assertIn('This business nature already exists.', form.errors['nature'])
        
        # Update 'existing_nature' to itself should be valid
        form = BusinessNatureForm(instance=existing_nature, data={'nature': 'Existing Nature'})
        self.assertTrue(form.is_valid())


class BusinessNatureViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.business_nature = BusinessNature.objects.create(nature='Software')
        cls.client = Client() # Initialize client for all tests

    def test_list_view_get(self):
        response = self.client.get(reverse('businessnature_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/businessnature/list.html')
        self.assertContains(response, 'Add New Business Nature') # Check for specific text

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('businessnature_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/businessnature/_businessnature_table.html')
        self.assertIn('businessnatures', response.context)
        self.assertContains(response, self.business_nature.nature) # Check if data is present

    def test_create_view_get(self):
        response = self.client.get(reverse('businessnature_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/businessnature/_businessnature_form.html')
        self.assertIsInstance(response.context['form'], BusinessNatureForm)

    def test_create_view_post_htmx_success(self):
        initial_count = BusinessNature.objects.count()
        data = {'nature': 'Consulting'}
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('businessnature_add'), data, **headers)
        
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertTrue(BusinessNature.objects.filter(nature='Consulting').exists())
        self.assertEqual(BusinessNature.objects.count(), initial_count + 1)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshBusinessNatureList', response.headers['HX-Trigger'])

    def test_create_view_post_htmx_failure(self):
        initial_count = BusinessNature.objects.count()
        BusinessNature.objects.create(nature='Existing Business') # Create a duplicate
        data = {'nature': 'Existing Business'}
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('businessnature_add'), data, **headers)
        
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'sysadmin/businessnature/_businessnature_form.html')
        self.assertContains(response, 'This business nature already exists.')
        self.assertEqual(BusinessNature.objects.count(), initial_count + 1) # No new object created

    def test_update_view_get(self):
        response = self.client.get(reverse('businessnature_edit', args=[self.business_nature.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/businessnature/_businessnature_form.html')
        self.assertIsInstance(response.context['form'], BusinessNatureForm)
        self.assertEqual(response.context['form'].instance, self.business_nature)

    def test_update_view_post_htmx_success(self):
        new_name = 'Updated Software'
        data = {'nature': new_name}
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('businessnature_edit', args=[self.business_nature.pk]), data, **headers)
        
        self.assertEqual(response.status_code, 204)
        self.business_nature.refresh_from_db()
        self.assertEqual(self.business_nature.nature, new_name)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshBusinessNatureList', response.headers['HX-Trigger'])

    def test_delete_view_get(self):
        response = self.client.get(reverse('businessnature_delete', args=[self.business_nature.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/businessnature/_businessnature_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.business_nature)

    def test_delete_view_post_htmx_success(self):
        object_to_delete_pk = self.business_nature.pk
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('businessnature_delete', args=[object_to_delete_pk]), **headers)
        
        self.assertEqual(response.status_code, 204)
        self.assertFalse(BusinessNature.objects.filter(pk=object_to_delete_pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshBusinessNatureList', response.headers['HX-Trigger'])

    def test_delete_view_post_regular_success(self):
        # Create an extra object for regular POST delete test to avoid interfering with other tests
        obj_to_delete_regular = BusinessNature.objects.create(nature='Temporary to Delete')
        initial_count = BusinessNature.objects.count()
        response = self.client.post(reverse('businessnature_delete', args=[obj_to_delete_regular.pk]))
        
        self.assertEqual(response.status_code, 302) # Redirect for non-HTMX request
        self.assertFalse(BusinessNature.objects.filter(pk=obj_to_delete_regular.pk).exists())
        self.assertEqual(BusinessNature.objects.count(), initial_count - 1)
```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

-   **HTMX for Dynamic Updates:**
    -   The `list.html` template uses `hx-get` to load the `_businessnature_table.html` partial on page load and on a custom `refreshBusinessNatureList` event. This ensures the table is always up-to-date without full page refreshes.
    -   CRUD buttons (`Add`, `Edit`, `Delete`) use `hx-get` to load form/confirmation partials into a modal (`#modalContent`).
    -   Form submissions (`_businessnature_form.html`, `_businessnature_confirm_delete.html`) use `hx-post` and `hx-swap="none"`. The views return `HttpResponse(status=204)` with `HX-Trigger` headers (`refreshBusinessNatureList`) to notify the `list.html` to reload the table and close the modal.
    -   Django's messages are integrated to appear after operations, potentially using HTMX's Out-of-Band (OOB) swap feature for persistent alerts.

-   **Alpine.js for UI State Management:**
    -   The modal (`#modal`) uses `_hyperscript` (`_="on click add .is-active to #modal"`, `_="on click if event.target.id == 'modal' remove .is-active from me"`) for simple show/hide logic. More complex UI states (e.g., dynamic form fields, conditional visibility) would leverage Alpine.js's `x-data` directives.

-   **DataTables for List Views:**
    -   The `_businessnature_table.html` partial contains a standard HTML `<table>` element.
    -   A `script` block within this partial initializes `DataTables` on `$(document).ready()`. This ensures that DataTables is correctly applied each time the table content is reloaded via HTMX. The `destroy()` method is called before re-initialization to prevent issues on subsequent HTMX swaps.

-   **No Full Page Reloads:** All user interactions (adding, editing, deleting records) trigger HTMX requests that update only the necessary parts of the page (the table, the modal) rather than forcing a full page reload, leading to a much smoother user experience.

## Final Notes

The provided Django modernization plan comprehensively covers the migration from the ASP.NET `BusinessNature` module. It replaces traditional ASP.NET Web Forms paradigms (GridView, SqlDataSource, Code-behind events) with modern Django patterns focusing on:

-   **Separation of Concerns**: Business logic resides in models, forms handle validation, and views are thin orchestrators.
-   **Modern Frontend**: Leveraging HTMX and Alpine.js for a dynamic, interactive user experience without heavy JavaScript frameworks, aligning with the "HTML over the wire" philosophy.
-   **Data Presentation**: Standardizing on DataTables for robust list view functionality.
-   **Maintainability**: Adhering to DRY principles and structured code within a Django application.
-   **Automated Testing**: Ensuring a high level of test coverage for both backend logic and user interactions.

This plan can be systematically implemented, with each component (model, form, view, template, URL, test) serving a clear, defined purpose, enabling efficient development and a robust, modern application. The AI-assisted automation approach would focus on converting the identified ASP.NET patterns into these structured Django components.