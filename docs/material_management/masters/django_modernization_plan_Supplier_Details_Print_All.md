## ASP.NET to Django Conversion Script:

This plan outlines the automated conversion of your ASP.NET Crystal Report viewer page into a modern Django web application. Our approach leverages artificial intelligence to analyze your existing code and generate the necessary Django components, significantly reducing manual effort and potential errors.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
Our AI analysis identified the following database tables and their implied structure based on SQL queries and data population logic:

*   **`tblMM_Supplier_master` (will become `Supplier` model):**
    *   `SupId` (Primary Key, integer)
    *   `CompId` (Foreign Key to `tblcompany_master`, integer)
    *   `SupplierName` (string)
    *   `SupplierId` (used as `Code`, string)
    *   `RegdAddress` (Street Address, string)
    *   `RegdCountry` (Foreign Key to `tblcountry`, integer)
    *   `RegdState` (Foreign Key to `tblState`, integer)
    *   `RegdCity` (Foreign Key to `tblCity`, integer)
    *   `RegdPinNo` (string)
    *   `ContactPerson` (string)
    *   `ContactNo` (used as `MobileNo`, string)
    *   `Email` (string)

*   **`tblcountry` (will become `Country` model):**
    *   `CId` (Primary Key, integer)
    *   `CountryName` (string)

*   **`tblState` (will become `State` model):**
    *   `SId` (Primary Key, integer)
    *   `StateName` (string)

*   **`tblCity` (will become `City` model):**
    *   `CityId` (Primary Key, integer)
    *   `CityName` (string)

*   **`tblcompany_master` (inferred, will become `Company` model):**
    *   `CompId` (Primary Key, integer)
    *   `CompAddress` (string, for `fun.CompAdd`)
    *   *(Other fields not relevant to this report)*

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations performed by the ASP.NET code.

**Instructions:**
The ASP.NET page `Supplier_Details_Print_All.aspx` is exclusively a **read/report generation** page. It does not perform any Create, Update, or Delete (CRUD) operations on supplier data directly. Its primary functions are:

*   **Read Operation:** Retrieve all supplier records from `tblMM_Supplier_master` based on a company ID (`CompId`).
*   **Data Transformation/Formatting:** Construct a comprehensive "Registered Address" string for each supplier by combining their street address with country, state, and city names looked up from related tables.
*   **Report Generation:** Present this formatted supplier data in a printable report format (originally Crystal Reports).
*   **Navigation:** A "Cancel" button redirects the user away from the report.

There is no user input form on this page for adding or modifying supplier details.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The user interface for this page is minimal and report-centric:

*   **CrystalReportViewer:** This control is the main display area, rendering the supplier report. In Django, this will be replaced by an HTML table, enhanced with DataTables for interactive filtering, sorting, and pagination, providing a modern and dynamic view of the data.
*   **Button:** A "Cancel" button (`Button1`) is present, which navigates the user back to a previous page (likely the main supplier list). In Django, this will be a simple link or button that redirects to the appropriate URL.

The original page includes a loading notifier, which will be handled gracefully by HTMX's inherent loading indicators or Alpine.js for more complex UI states.

### Step 4: Generate Django Code

We will now generate the corresponding Django code, organized into distinct files within a `material_management` application.

#### 4.1 Models

**Task:** Create Django models based on the identified database schema.

**Instructions:**
The following models will be defined, mapping directly to your existing database tables. `managed = False` ensures Django doesn't try to create or alter these tables, as they already exist. The `get_full_address` property is a crucial "fat model" component, encapsulating the address formatting logic previously in your C# code.

```python
# material_management/models.py
from django.db import models

class Company(models.Model):
    comp_id = models.IntegerField(db_column='CompId', primary_key=True)
    comp_address = models.CharField(db_column='CompAddress', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblcompany_master'
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return f"Company {self.comp_id}"

    def get_company_address(self):
        """Retrieves the formatted company address, mirroring fun.CompAdd."""
        return self.comp_address if self.comp_address else "Company address not available."


class Country(models.Model):
    cid = models.IntegerField(db_column='CId', primary_key=True)
    country_name = models.CharField(db_column='CountryName', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblcountry'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.country_name if self.country_name else "Unknown Country"


class State(models.Model):
    sid = models.IntegerField(db_column='SId', primary_key=True)
    state_name = models.CharField(db_column='StateName', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblState'
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.state_name if self.state_name else "Unknown State"


class City(models.Model):
    city_id = models.IntegerField(db_column='CityId', primary_key=True)
    city_name = models.CharField(db_column='CityName', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCity'
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.city_name if self.city_name else "Unknown City"


class Supplier(models.Model):
    sup_id = models.IntegerField(db_column='SupId', primary_key=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', blank=True, null=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255, blank=True, null=True)
    code = models.CharField(db_column='SupplierId', max_length=50, blank=True, null=True) # Mapped from SupplierId
    regd_street_address = models.CharField(db_column='RegdAddress', max_length=500, blank=True, null=True)
    regd_country = models.ForeignKey(Country, models.DO_NOTHING, db_column='RegdCountry', blank=True, null=True)
    regd_state = models.ForeignKey(State, models.DO_NOTHING, db_column='RegdState', blank=True, null=True)
    regd_city = models.ForeignKey(City, models.DO_NOTHING, db_column='RegdCity', blank=True, null=True)
    regd_pin_no = models.CharField(db_column='RegdPinNo', max_length=20, blank=True, null=True)
    contact_person = models.CharField(db_column='ContactPerson', max_length=100, blank=True, null=True)
    contact_no = models.CharField(db_column='ContactNo', max_length=20, blank=True, null=True) # Mapped from ContactNo (used as MobileNo)
    email = models.CharField(db_column='Email', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.supplier_name if self.supplier_name else f"Supplier {self.sup_id}"

    @property
    def get_full_address(self):
        """
        Constructs the full registered address string.
        This mirrors the address concatenation logic from the ASP.NET code-behind.
        """
        parts = []
        if self.regd_street_address:
            parts.append(self.regd_street_address)
        if self.regd_city and self.regd_city.city_name:
            parts.append(self.regd_city.city_name)
        if self.regd_state and self.regd_state.state_name:
            parts.append(self.regd_state.state_name)
        
        country_pin_part = []
        if self.regd_country and self.regd_country.country_name:
            country_pin_part.append(self.regd_country.country_name)
        if self.regd_pin_no:
            country_pin_part.append(f"- {self.regd_pin_no}")
        
        full_address_parts = [", ".join(parts)]
        if country_pin_part:
            full_address_parts.append(",\n" + " ".join(country_pin_part))
        
        return "".join(full_address_parts) + "." if full_address_parts else "Address not available."

```

#### 4.2 Forms

**Task:** Define Django forms for user input.

**Instructions:**
The original ASP.NET page is purely a report viewer and does not involve user input forms for creating or updating suppliers. Therefore, no specific forms are needed for this direct migration. If broader CRUD functionality were to be implemented for suppliers, `SupplierForm` would be created here.

*(No forms.py generated for this specific report viewing component, as it does not involve data entry.)*

#### 4.3 Views

**Task:** Implement the report display using Django Class-Based Views.

**Instructions:**
We will create two views: a `ListView` to serve the main report page, and a `TemplateView` (or a specialized `ListView` for partials) to provide the HTMX-loaded table content. This keeps the views thin and focused.

```python
# material_management/views.py
from django.views.generic import ListView, TemplateView
from django.urls import reverse_lazy
from django.http import HttpResponse
from .models import Supplier, Company # Import Company for company address
from django.shortcuts import get_object_or_404
from django.db.models import Prefetch

class SupplierReportListView(ListView):
    model = Supplier
    template_name = 'material_management/supplier/report_list.html'
    context_object_name = 'suppliers'

    def get_queryset(self):
        # The C# code filtered by 'CompId' from session.
        # Assuming compid comes from a logged-in user's profile or session.
        # For demonstration, we'll use a hardcoded default or query param.
        # In a real app, this would be: current_company_id = self.request.user.profile.comp_id
        
        # Example: Get company ID from session or a default for testing
        current_company_id = self.request.session.get('compid', 1) # Default to 1 if not in session

        # Pre-fetch related objects to avoid N+1 queries when accessing
        # country, state, city names and company details for address formatting.
        queryset = Supplier.objects.filter(company_id=current_company_id).prefetch_related(
            'regd_country', 'regd_state', 'regd_city', 'company'
        )
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Retrieve company address, mirroring fun.CompAdd(cId)
        current_company_id = self.request.session.get('compid', 1)
        try:
            company = Company.objects.get(comp_id=current_company_id)
            context['company_address'] = company.get_company_address()
        except Company.DoesNotExist:
            context['company_address'] = "Company address not available."
        
        return context

class SupplierReportTablePartialView(SupplierReportListView):
    """
    A partial view designed to be loaded via HTMX, providing only the table content.
    Inherits get_queryset from SupplierReportListView.
    """
    template_name = 'material_management/supplier/_supplier_report_table.html'

    def render_to_response(self, context, **response_kwargs):
        # This view specifically returns the table HTML for HTMX swap
        return super().render_to_response(context, **response_kwargs)

```

#### 4.4 Templates

**Task:** Create templates for the report view.

**Instructions:**
The main report page (`report_list.html`) will set up the container for the DataTables, which will be loaded dynamically via HTMX. The actual table structure will reside in a partial template (`_supplier_report_table.html`) for efficient HTMX updates.

**`material_management/supplier/report_list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Supplier Details Report</h2>
        <a href="{% url 'supplier_master_list' %}" 
           class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded shadow-md transition duration-300 ease-in-out">
            Cancel
        </a>
    </div>

    <div class="mb-4 text-gray-700 text-sm">
        <p class="font-semibold">Company Address:</p>
        <p class="whitespace-pre-line">{{ company_address }}</p>
    </div>
    
    <div id="supplier-report-table-container"
         hx-trigger="load"
         hx-get="{% url 'supplier_report_table_partial' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Supplier Data...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for broader UI interactions
    });
</script>
{% endblock %}
```

**`material_management/supplier/_supplier_report_table.html`**

```html
<div class="overflow-x-auto shadow-md rounded-lg">
    <table id="supplierReportTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Registered Address</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact Person</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mobile No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for supplier in suppliers %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ supplier.supplier_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ supplier.code }}</td>
                <td class="py-3 px-4 whitespace-pre-line text-sm text-gray-700">{{ supplier.get_full_address }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ supplier.contact_person }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ supplier.contact_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ supplier.email }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-3 px-4 text-center text-sm text-gray-500">No suppliers found for this company.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after the content is loaded by HTMX
    $(document).ready(function() {
        $('#supplierReportTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0] } // Disable sorting for SN column
            ]
        });
    });
</script>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
We'll define URLs for the main report page and the HTMX-loaded table partial.

```python
# material_management/urls.py
from django.urls import path
from .views import SupplierReportListView, SupplierReportTablePartialView

urlpatterns = [
    # URL for the main supplier report page
    path('supplier/report/all/', SupplierReportListView.as_view(), name='supplier_report_all'),
    # HTMX endpoint for the DataTables content
    path('supplier/report/table/', SupplierReportTablePartialView.as_view(), name='supplier_report_table_partial'),
    # Placeholder for the main supplier list page, matching the ASP.NET redirect
    path('supplier/master/list/', SupplierReportListView.as_view(), name='supplier_master_list'), # Redirect target from ASP.NET
]
```

#### 4.6 Tests

**Task:** Write comprehensive tests for the models and views.

**Instructions:**
These tests ensure the data models behave as expected, especially the `get_full_address` property, and that the views correctly fetch and render the data, including HTMX interactions.

```python
# material_management/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import Company, Country, State, City, Supplier
from django.db import connection

class DataSetupMixin:
    """Mixin to set up mock data for tests."""
    @classmethod
    def setUpTestData(cls):
        # Ensure tables are empty before creating test data, as they are managed=False
        with connection.cursor() as cursor:
            cursor.execute("DELETE FROM tblcompany_master;")
            cursor.execute("DELETE FROM tblcountry;")
            cursor.execute("DELETE FROM tblState;")
            cursor.execute("DELETE FROM tblCity;")
            cursor.execute("DELETE FROM tblMM_Supplier_master;")

        # Create mock data for FKs first
        cls.company1 = Company.objects.create(comp_id=1, comp_address="123 ERP St, Tech City")
        cls.country1 = Country.objects.create(cid=1, country_name="India")
        cls.state1 = State.objects.create(sid=1, state_name="Gujarat")
        cls.city1 = City.objects.create(city_id=1, city_name="Ahmedabad")

        cls.supplier1 = Supplier.objects.create(
            sup_id=101,
            company=cls.company1,
            supplier_name="Alpha Supplies Pvt. Ltd.",
            code="ASP001",
            regd_street_address="45, Industrial Area",
            regd_country=cls.country1,
            regd_state=cls.state1,
            regd_city=cls.city1,
            regd_pin_no="380001",
            contact_person="Mr. Raj Patel",
            contact_no="9876543210",
            email="<EMAIL>"
        )

        cls.company2 = Company.objects.create(comp_id=2, comp_address="456 Dev Ave, Codeville")
        cls.country2 = Country.objects.create(cid=2, country_name="USA")
        cls.state2 = State.objects.create(sid=2, state_name="California")
        cls.city2 = City.objects.create(city_id=2, city_name="San Francisco")

        cls.supplier2 = Supplier.objects.create(
            sup_id=102,
            company=cls.company2,
            supplier_name="Beta Solutions Inc.",
            code="BSI001",
            regd_street_address="100 Tech Park",
            regd_country=cls.country2,
            regd_state=cls.state2,
            regd_city=cls.city2,
            regd_pin_no="94105",
            contact_person="Ms. Jane Doe",
            contact_no="1234567890",
            email="<EMAIL>"
        )
        
        # Supplier for different company to test filtering
        cls.supplier3 = Supplier.objects.create(
            sup_id=103,
            company=cls.company2,
            supplier_name="Gamma Distributors",
            code="GD001",
            regd_street_address="789 Business Road",
            regd_country=cls.country2,
            regd_state=cls.state2,
            regd_city=cls.city2,
            regd_pin_no="94103",
            contact_person="Mr. John Smith",
            contact_no="0987654321",
            email="<EMAIL>"
        )


class CompanyModelTest(DataSetupMixin, TestCase):
    def test_company_creation(self):
        self.assertEqual(self.company1.comp_id, 1)
        self.assertEqual(self.company1.comp_address, "123 ERP St, Tech City")
        self.assertEqual(str(self.company1), "Company 1")

    def test_get_company_address(self):
        self.assertEqual(self.company1.get_company_address(), "123 ERP St, Tech City")
        
        # Test with no address
        no_address_company = Company.objects.create(comp_id=99)
        self.assertEqual(no_address_company.get_company_address(), "Company address not available.")

class CountryModelTest(DataSetupMixin, TestCase):
    def test_country_creation(self):
        self.assertEqual(self.country1.cid, 1)
        self.assertEqual(self.country1.country_name, "India")
        self.assertEqual(str(self.country1), "India")

    def test_country_name_null(self):
        country = Country.objects.create(cid=3, country_name=None)
        self.assertEqual(str(country), "Unknown Country")

class StateModelTest(DataSetupMixin, TestCase):
    def test_state_creation(self):
        self.assertEqual(self.state1.sid, 1)
        self.assertEqual(self.state1.state_name, "Gujarat")
        self.assertEqual(str(self.state1), "Gujarat")

    def test_state_name_null(self):
        state = State.objects.create(sid=3, state_name=None)
        self.assertEqual(str(state), "Unknown State")

class CityModelTest(DataSetupMixin, TestCase):
    def test_city_creation(self):
        self.assertEqual(self.city1.city_id, 1)
        self.assertEqual(self.city1.city_name, "Ahmedabad")
        self.assertEqual(str(self.city1), "Ahmedabad")

    def test_city_name_null(self):
        city = City.objects.create(city_id=3, city_name=None)
        self.assertEqual(str(city), "Unknown City")

class SupplierModelTest(DataSetupMixin, TestCase):
    def test_supplier_creation(self):
        self.assertEqual(self.supplier1.sup_id, 101)
        self.assertEqual(self.supplier1.supplier_name, "Alpha Supplies Pvt. Ltd.")
        self.assertEqual(self.supplier1.code, "ASP001")
        self.assertEqual(self.supplier1.contact_no, "9876543210")
        self.assertEqual(self.supplier1.company, self.company1)
        self.assertEqual(str(self.supplier1), "Alpha Supplies Pvt. Ltd.")

    def test_get_full_address(self):
        expected_address = "45, Industrial Area, Ahmedabad, Gujarat,\nIndia - 380001."
        self.assertEqual(self.supplier1.get_full_address, expected_address)

        expected_address_usa = "100 Tech Park, San Francisco, California,\nUSA - 94105."
        self.assertEqual(self.supplier2.get_full_address, expected_address_usa)

        # Test with missing related objects / partial data
        supplier_partial_address = Supplier.objects.create(
            sup_id=104,
            company=self.company1,
            supplier_name="Partial Address Co.",
            regd_street_address="123 Main St",
            regd_pin_no="12345"
            # No city, state, country
        )
        self.assertEqual(supplier_partial_address.get_full_address, "123 Main St,\n - 12345.")
        
        supplier_no_address = Supplier.objects.create(
            sup_id=105,
            company=self.company1,
            supplier_name="No Address Co."
        )
        self.assertEqual(supplier_no_address.get_full_address, "Address not available.")


class SupplierViewsTest(DataSetupMixin, TestCase):
    def setUp(self):
        self.client = Client()
        # Simulate ASP.NET session 'compid'
        session = self.client.session
        session['compid'] = self.company1.comp_id
        session.save()

    def test_report_list_view_get(self):
        response = self.client.get(reverse('supplier_report_all'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/supplier/report_list.html')
        self.assertIn('suppliers', response.context)
        self.assertIn('company_address', response.context)
        self.assertContains(response, 'Supplier Details Report')
        self.assertContains(response, 'Cancel')
        self.assertContains(response, self.company1.get_company_address())

    def test_report_list_view_filters_by_company(self):
        # By default, session has company1's compid
        response = self.client.get(reverse('supplier_report_all'))
        self.assertEqual(response.status_code, 200)
        
        # Only supplier1 should be in the context for company1
        suppliers_in_context = response.context['suppliers']
        self.assertIn(self.supplier1, suppliers_in_context)
        self.assertNotIn(self.supplier2, suppliers_in_context) # From company2
        self.assertNotIn(self.supplier3, suppliers_in_context) # Also from company2

    def test_report_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('supplier_report_table_partial'), **headers)
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/supplier/_supplier_report_table.html')
        self.assertIn('suppliers', response.context)
        
        # Check if supplier data is present in the partial HTML
        self.assertContains(response, self.supplier1.supplier_name)
        self.assertContains(response, self.supplier1.get_full_address)
        self.assertNotContains(response, self.supplier2.supplier_name) # Should not contain supplier from different company

    def test_report_table_partial_view_no_htmx(self):
        # Test direct access without HTMX header (should still work but not typical)
        response = self.client.get(reverse('supplier_report_table_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/supplier/_supplier_report_table.html')

    def test_cancel_button_redirect(self):
        # The cancel button's target URL
        response = self.client.get(reverse('supplier_master_list'))
        self.assertEqual(response.status_code, 200) # Assuming supplier_master_list is also a ListView
        self.assertTemplateUsed(response, 'material_management/supplier/report_list.html') # As per urls.py setup

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The Django conversion strategy focuses on modern web technologies to replace the legacy Crystal Report Viewer with an interactive and responsive HTML table.

*   **HTMX for Dynamic Content:**
    *   The `supplier_report_all` page (`report_list.html`) uses `hx-get` to load the supplier table content from `{% url 'supplier_report_table_partial' %}` into the `supplier-report-table-container` div upon page load. This keeps the initial page load light and loads dynamic content asynchronously.
    *   Any future interactions (e.g., refreshing the list after a CRUD operation, if they were added later) would use `hx-trigger` with custom events (e.g., `refreshSupplierList`) to re-fetch and re-render the table.

*   **DataTables for List Views:**
    *   The `_supplier_report_table.html` partial explicitly initializes DataTables on the `supplierReportTable` element using a simple JavaScript snippet. This provides:
        *   Client-side searching (a search box appears automatically).
        *   Client-side sorting (columns are sortable by clicking headers).
        *   Client-side pagination (data is broken into pages).
        *   Control over the number of entries displayed per page.
    *   This completely replaces the interactive features that a Crystal Report Viewer might offer for browsing data, in a web-native and performant manner.

*   **Alpine.js for UI State (Optional/Future):**
    *   While not strictly required for this simple report page, `Alpine.js` is included in the `extra_js` block within `base.html` (as assumed). If more complex client-side UI states were needed (e.g., dynamically showing/hiding elements, client-side validation feedback, managing modal visibility beyond HTMX's basic swap), Alpine.js would be the tool of choice. For this report, HTMX handles the loading and the existing base structure handles the modal (if it were present, which it is not for a pure report view).

*   **No Custom JavaScript (Beyond DataTables Init):**
    *   As per guidelines, no additional complex JavaScript is written. HTMX handles the dynamic interactions, and DataTables is a pre-built library.

*   **HTML Template Structure (DRY):**
    *   The `report_list.html` extends `core/base.html` for layout inheritance.
    *   The `_supplier_report_table.html` is a reusable partial, ensuring the table rendering logic is isolated and can be fetched independently.

### Final Notes

This comprehensive plan provides a systematic, automation-friendly approach to migrating your ASP.NET Crystal Report page to a modern Django application. By focusing on the `Supplier` model's `get_full_address` property, we ensure business logic is correctly moved to the "fat model," leaving views thin and concise. The use of HTMX and DataTables delivers a superior user experience, offering interactive data display without complex JavaScript. This modernization reduces reliance on legacy reporting tools, improves maintainability, and aligns with current web development best practices, enabling easier future enhancements and scalability.