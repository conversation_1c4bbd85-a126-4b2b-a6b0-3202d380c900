## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code interacts with three primary tables: `tblMM_Supplier_master`, `tblFinancial_master`, and `tblHR_OfficeStaff`. It retrieves `SupplierId`, `SupplierName`, `SysDate`, `FinYear`, and `EmployeeName`. The `SupplierId` is a primary key for `tblMM_Supplier_master`.

**Identified Tables and Columns:**

*   **`tblMM_Supplier_master`**:
    *   `SupplierId` (Primary Key, CharField)
    *   `SupplierName` (CharField)
    *   `SysDate` (CharField, stores date as string)
    *   `FinYearId` (IntegerField, Foreign Key to `tblFinancial_master`)
    *   `SessionId` (IntegerField, Foreign Key to `tblHR_OfficeStaff.EmpId`)
    *   `CompId` (IntegerField, Foreign Key to `tblCompany_master`, derived from `Session["compid"]`)
*   **`tblFinancial_master`**:
    *   `FinYearId` (Primary Key, IntegerField)
    *   `FinYear` (CharField)
*   **`tblHR_OfficeStaff`**:
    *   `EmpId` (Primary Key, IntegerField)
    *   `EmployeeName` (CharField)
*   **`tblCompany_master`** (Inferred from `Session["compid"]` usage):
    *   `CompId` (Primary Key, IntegerField)
    *   `CompanyName` (CharField - assumed)

### Step 2: Identify Backend Functionality

**Analysis:**
The ASP.NET page is primarily a "read" (display) page with search and pagination capabilities. It also provides links to detailed views and a "print all" feature.

*   **Read/List:** Displays a list of suppliers in a `GridView`, with pagination.
*   **Search/Filter:** Allows searching by `SupplierName` (with autocomplete) and filters the displayed list. Filtering also applies based on `CompId` and `FinYearId` from the user's session.
*   **Detail Link:** Clicking on a `SupplierName` in the list redirects to a supplier-specific detail page.
*   **Print All:** A button to generate a comprehensive printout of all suppliers.
*   **CRUD Operations:** Although not explicitly implemented in this specific ASP.NET page, standard Django modernization involves full CRUD for data entities. Therefore, Create, Update, and Delete views will be provided.

### Step 3: Infer UI Components

**Analysis:**
The ASP.NET controls are mapped to Django equivalents with modern frontend practices (HTMX, Alpine.js, DataTables, Tailwind CSS).

*   **`SearchGridView1` (GridView):** Will be replaced by an HTML `<table>` structured for DataTables, loaded and refreshed dynamically using HTMX.
*   **`TxtSearchValue` (TextBox with AutoCompleteExtender):** Will become a standard HTML `<input type="text">` with HTMX attributes for autocomplete suggestions and a `hx-get` to trigger table filtering. Alpine.js can manage the display of autocomplete suggestions.
*   **`Search` (Button):** Will be a standard HTML `<button>` with `hx-get` to trigger the table refresh.
*   **`btnPrintAll` (Button):** Will be a standard HTML `<button>` or `<a>` tag linking to the "Print All" view.
*   **`LinkButton` (`lnkBtn` on `SupplierName`):** Will become an `<a>` tag navigating to the supplier's detail page.
*   **HiddenFields (`hfSearchText`, `hfSort`):** These are ASP.NET specific for managing state across postbacks. In Django with HTMX, state is managed via URL parameters, HTMX request headers, or Alpine.js.

### Step 4: Generate Django Code

**App Name:** `material_management`

#### 4.1 Models (`material_management/models.py`)

```python
from django.db import models
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

# Assuming these related tables exist and are also managed=False if coming from legacy DB
# And assuming primary keys as inferred from the ASP.NET code
class FinancialYear(models.Model):
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=100) # inferred from usage
    
    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class Employee(models.Model):
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255) # inferred
    
    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return self.employee_name

class Company(models.Model):
    comp_id = models.IntegerField(db_column='CompId', primary_key=True)
    company_name = models.CharField(db_column='CompanyName', max_length=255) # placeholder, inferred
    
    class Meta:
        managed = False
        db_table = 'tblCompany_master' # Inferred table name
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.company_name

class SupplierMaster(models.Model):
    # SupplierId used as DataKeyNames and passed as 'spid', suggesting it's the PK.
    supplier_id = models.CharField(db_column='SupplierId', max_length=50, primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    
    # SysDate is stored as a string, e.g., 'MM-DD-YYYY' or similar.
    # We store it as is and provide a property for datetime conversion.
    sys_date_str = models.CharField(db_column='SysDate', max_length=20) 
    
    # Foreign keys to related tables, assuming they exist and are mapped.
    fin_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='suppliers')
    created_by_employee = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='SessionId', related_name='created_suppliers')
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='company_suppliers')

    class Meta:
        managed = False # Crucial for mapping to existing legacy database
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.supplier_name

    # Business logic methods
    @property
    def formatted_sys_date(self):
        """Converts the SysDate_str to a datetime object in DD/MM/YYYY format."""
        try:
            # Attempt to parse MM-DD-YYYY or DD-MM-YYYY, which seems implied by original logic.
            # The ASP.NET code was very complex for date parsing, indicating potential inconsistencies.
            # We'll try the most common formats that match the original logic's outcome.
            dt_obj = None
            try:
                dt_obj = datetime.strptime(self.sys_date_str, '%m-%d-%Y')
            except ValueError:
                dt_obj = datetime.strptime(self.sys_date_str, '%d-%m-%Y')
            return dt_obj.strftime('%d/%m/%Y')
        except ValueError:
            logger.warning(f"Could not parse SysDate_str: {self.sys_date_str} for Supplier ID: {self.supplier_id}")
            return self.sys_date_str # Return original string if unparseable

    def get_details_url(self):
        """Generates the URL for the supplier details page."""
        return f"/material_management/supplier/{self.supplier_id}/details/"

    @classmethod
    def get_filtered_suppliers(cls, search_value=None, company_id=None, financial_year_id=None):
        """
        Retrieves and filters suppliers based on search criteria, company, and financial year.
        This method encapsulates the 'BindData' and 'Search' logic from the ASP.NET code.
        """
        queryset = cls.objects.all()

        if company_id is not None:
            queryset = queryset.filter(company__comp_id=company_id)
        
        # The ASP.NET code uses `FinYearId<='FyId'` where FyId is a string.
        # Assuming `FinYearId` in the DB is an integer or comparable numeric value.
        if financial_year_id is not None:
            # We're converting FyId to int before passing to filter to ensure proper comparison
            try:
                numeric_fin_year_id = int(financial_year_id)
                queryset = queryset.filter(fin_year__fin_year_id__lte=numeric_fin_year_id)
            except (ValueError, TypeError):
                logger.warning(f"Invalid financial_year_id '{financial_year_id}' provided. Skipping filter.")
                pass # Skip filter if FyId is not a valid integer

        if search_value:
            # The ASP.NET AutoCompleteExtender returns "SupplierName [SupplierId]".
            # We parse this to extract SupplierId if present, otherwise search by name.
            supplier_id_from_search = None
            if '[' in search_value and ']' in search_value:
                try:
                    supplier_id_from_search = search_value.split('[')[-1].strip(']')
                except IndexError:
                    pass

            if supplier_id_from_search:
                queryset = queryset.filter(supplier_id=supplier_id_from_search)
            else:
                queryset = queryset.filter(supplier_name__icontains=search_value)
                
        # Use select_related to efficiently fetch data from foreign key tables
        queryset = queryset.select_related('fin_year', 'created_by_employee', 'company')
        
        return queryset.order_by('supplier_name') # Default sort order
```

#### 4.2 Forms (`material_management/forms.py`)

```python
from django import forms
from .models import SupplierMaster, FinancialYear, Employee, Company

class SupplierMasterForm(forms.ModelForm):
    class Meta:
        model = SupplierMaster
        fields = ['supplier_id', 'supplier_name', 'sys_date_str', 'fin_year', 'created_by_employee', 'company']
        # The 'supplier_id' is the PK, usually not editable on Update, but may be on Create.
        # If it's an auto-generated code, it would be excluded from fields for Create.
        # For this example, assuming manual entry for create.
        widgets = {
            'supplier_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'supplier_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sys_date_str': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'MM-DD-YYYY'}),
            'fin_year': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'created_by_employee': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'company': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        
    def clean_sys_date_str(self):
        # Validate that sys_date_str can be parsed into a date
        date_str = self.cleaned_data['sys_date_str']
        if not date_str:
            raise forms.ValidationError("System Date is required.")
        try:
            datetime.strptime(date_str, '%m-%d-%Y')
        except ValueError:
            try: # Try alternative format
                datetime.strptime(date_str, '%d-%m-%Y')
            except ValueError:
                raise forms.ValidationError("Invalid date format. Please use MM-DD-YYYY or DD-MM-YYYY.")
        return date_str

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate dropdowns for ForeignKey fields
        self.fields['fin_year'].queryset = FinancialYear.objects.all().order_by('fin_year')
        self.fields['created_by_employee'].queryset = Employee.objects.all().order_by('employee_name')
        self.fields['company'].queryset = Company.objects.all().order_by('company_name')

```

#### 4.3 Views (`material_management/views.py`)

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View, DetailView
from django.urls import reverse_lazy, reverse
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from .models import SupplierMaster, FinancialYear, Employee, Company
from .forms import SupplierMasterForm
import secrets # For generating random keys, mimicking fun.GetRandomAlphaNumeric()

class SupplierMasterListView(ListView):
    model = SupplierMaster
    template_name = 'material_management/supplier_master/list.html'
    context_object_name = 'suppliers'

    def get_queryset(self):
        # The main list view will initially render an empty table container for HTMX to fill.
        return SupplierMaster.objects.none() # Return an empty queryset initially

class SupplierMasterTablePartialView(ListView):
    """
    Renders the DataTables portion of the supplier list.
    Accessed via HTMX to update the table dynamically based on search and filters.
    """
    model = SupplierMaster
    template_name = 'material_management/supplier_master/_supplier_table.html'
    context_object_name = 'suppliers'

    def get_queryset(self):
        search_value = self.request.GET.get('search_value', '').strip()
        # Retrieve company_id and financial_year_id from session, mirroring ASP.NET behavior
        company_id = self.request.session.get('compid') 
        financial_year_id = self.request.session.get('finyear') 
        
        queryset = SupplierMaster.get_filtered_suppliers(
            search_value=search_value, 
            company_id=company_id, 
            financial_year_id=financial_year_id
        )
        return queryset

class SupplierMasterCreateView(CreateView):
    model = SupplierMaster
    form_class = SupplierMasterForm
    template_name = 'material_management/supplier_master/_supplier_form.html'

    def form_valid(self, form):
        # Mimic ASP.NET session data if fields aren't explicitly set in form
        if not form.instance.company_id and self.request.session.get('compid'):
            form.instance.company_id = self.request.session['compid']
        if not form.instance.fin_year_id and self.request.session.get('finyear'):
            # Assuming 'finyear' from session is directly usable as fin_year_id
            form.instance.fin_year_id = self.request.session['finyear']
        if not form.instance.created_by_employee_id and self.request.session.get('sessionid'):
             # Assuming 'sessionid' from session is directly usable as EmpId
            form.instance.created_by_employee_id = self.request.session['sessionid']

        response = super().form_valid(form)
        messages.success(self.request, 'Supplier added successfully.')
        # HTMX-specific response for modal and list refresh
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content response to close modal and trigger refresh
                headers={
                    'HX-Trigger': 'refreshSupplierMasterList' # Custom event to trigger table reload
                }
            )
        return response # Fallback for non-HTMX requests (e.g., direct form submission without HTMX)

    def form_invalid(self, form):
        response = super().form_invalid(form)
        # For HTMX, return the form with errors to be swapped back into the modal
        if self.request.headers.get('HX-Request'):
            return HttpResponse(response.render().content, status=400) # Status 400 indicates form errors
        return response

class SupplierMasterUpdateView(UpdateView):
    model = SupplierMaster
    form_class = SupplierMasterForm
    template_name = 'material_management/supplier_master/_supplier_form.html'
    pk_url_kwarg = 'pk' # Ensure PK matches URL pattern 'pk'

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Supplier updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSupplierMasterList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return HttpResponse(response.render().content, status=400)
        return response

class SupplierMasterDeleteView(DeleteView):
    model = SupplierMaster
    template_name = 'material_management/supplier_master/_supplier_confirm_delete.html'
    pk_url_kwarg = 'pk' # Ensure PK matches URL pattern 'pk'

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Supplier deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSupplierMasterList'
                }
            )
        return response

class SupplierMasterDetailView(DetailView):
    """
    View for displaying individual supplier details.
    Corresponds to the `SupplierMaster_Print_Details.aspx` link.
    """
    model = SupplierMaster
    template_name = 'material_management/supplier_master/detail.html'
    context_object_name = 'supplier'
    pk_url_kwarg = 'supplier_id' # Match the URL pattern's kwarg

    def get_queryset(self):
        # Ensure related data is fetched efficiently for display
        return super().get_queryset().select_related('fin_year', 'created_by_employee', 'company')

class SupplierMasterPrintAllView(ListView):
    """
    View for printing all suppliers.
    Corresponds to `Supplier_Details_Print_All.aspx`.
    This could render a report, or trigger a PDF generation service.
    For simplicity, it will list all relevant data.
    """
    model = SupplierMaster
    template_name = 'material_management/supplier_master/print_all.html'
    context_object_name = 'suppliers'

    def get_queryset(self):
        # Apply filters based on session or other criteria if 'Print All' has context
        company_id = self.request.session.get('compid')
        financial_year_id = self.request.session.get('finyear')
        
        queryset = SupplierMaster.get_filtered_suppliers(
            company_id=company_id, 
            financial_year_id=financial_year_id
        )
        return queryset

class SupplierMasterAutocompleteView(View):
    """
    Provides autocomplete suggestions for supplier names via HTMX.
    Mimics the AjaxControlToolkit AutoCompleteExtender ServiceMethod 'sql'.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('prefixText', '')
        company_id = request.session.get('compid')

        # Filter by company ID if available
        if company_id:
            suggestions = SupplierMaster.objects.filter(
                company__comp_id=company_id
            ).filter(
                supplier_name__icontains=query 
            ).values_list('supplier_name', 'supplier_id')[:10] # Limit suggestions as in original

        else:
            # If no company ID, search across all
            suggestions = SupplierMaster.objects.filter(
                supplier_name__icontains=query
            ).values_list('supplier_name', 'supplier_id')[:10]

        # Format as "SupplierName [SupplierId]" as in original WebMethod
        formatted_suggestions = [f"{name} [{sup_id}]" for name, sup_id in suggestions]
        
        return JsonResponse(formatted_suggestions, safe=False)

```

#### 4.4 Templates

**`material_management/supplier_master/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Supplier List</h2>
        <div class="flex items-center space-x-4">
            <div x-data="{ searchTerm: '' }" class="relative">
                <input type="text"
                       id="txtSearchValue"
                       x-model="searchTerm"
                       hx-get="{% url 'material_management:supplier_autocomplete' %}"
                       hx-target="#autocomplete-suggestions"
                       hx-trigger="keyup changed delay:300ms, search"
                       name="prefixText"
                       placeholder="Search Supplier Name [ID]"
                       class="box3 block w-64 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                <div id="autocomplete-suggestions" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1" hx-swap="innerHTML"></div>
            </div>
            <button 
                id="Search" 
                class="redbox bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                hx-get="{% url 'material_management:supplier_table' %}"
                hx-target="#supplierMasterTable-container"
                hx-vals="js:{search_value: document.getElementById('txtSearchValue').value}"
                hx-trigger="click">
                Search
            </button>
            <a href="{% url 'material_management:supplier_print_all' %}" target="_blank"
                class="redbox bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                Print All
            </a>
            <button 
                class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
                hx-get="{% url 'material_management:supplier_add' %}"
                hx-target="#modalContent"
                hx-trigger="click"
                _="on click add .is-active to #modal">
                Add New Supplier
            </button>
        </div>
    </div>
    
    <div id="supplierMasterTable-container"
         hx-trigger="load, refreshSupplierMasterList from:body"
         hx-get="{% url 'material_management:supplier_table' %}"
         hx-swap="innerHTML">
        <!-- DataTables will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Suppliers...</p>
        </div>
    </div>
    
    <!-- Modal for form (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto relative">
            <button type="button" class="absolute top-3 right-3 text-gray-500 hover:text-gray-700"
                    _="on click remove .is-active from #modal">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize DataTables on the HTMX-loaded table
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.target.id === 'supplierMasterTable-container') {
            $('#supplierMasterTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "responsive": true
            });
        }
    });

    // Handle autocomplete selection and trigger search
    document.body.addEventListener('click', function(event) {
        if (event.target.matches('.autocomplete-suggestion-item')) {
            const selectedValue = event.target.textContent;
            document.getElementById('txtSearchValue').value = selectedValue;
            document.getElementById('autocomplete-suggestions').innerHTML = ''; // Clear suggestions
            // Manually trigger the search button's HTMX request
            htmx.trigger(document.getElementById('Search'), 'click');
        }
    });

    // Clear suggestions when input loses focus
    document.getElementById('txtSearchValue').addEventListener('blur', function() {
        setTimeout(() => { // Small delay to allow click on suggestion to register
            document.getElementById('autocomplete-suggestions').innerHTML = '';
        }, 100);
    });
</script>
{% endblock %}
```

**`material_management/supplier_master/_supplier_table.html` (Partial for HTMX)**

```html
<table id="supplierMasterTable" class="min-w-full bg-white border border-gray-200 shadow-sm rounded-lg overflow-hidden">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in suppliers %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.fin_year.fin_year }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">
                <a href="{% url 'material_management:supplier_detail' supplier_id=obj.supplier_id %}" class="text-blue-600 hover:underline">
                    {{ obj.supplier_name }}
                </a>                                
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.supplier_id }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.formatted_sys_date }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.created_by_employee.employee_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md text-xs mr-2"
                    hx-get="{% url 'material_management:supplier_edit' pk=obj.supplier_id %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md text-xs"
                    hx-get="{% url 'material_management:supplier_delete' pk=obj.supplier_id %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="7" class="py-4 text-center font-bold text-lg text-maroon-600">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

{% comment %}
    This script block will be evaluated when HTMX swaps the content.
    It re-initializes DataTables, ensuring client-side features are applied.
{% endcomment %}
<script>
    // This script will run every time this partial is loaded by HTMX.
    // The main list.html has the body event listener for htmx:afterSwap to initialize.
    // This is a fallback or for direct render testing.
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#supplierMasterTable')) {
            $('#supplierMasterTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "responsive": true
            });
        }
    });
</script>
```

**`material_management/supplier_master/_supplier_form.html` (Partial for HTMX)**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Supplier</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-indicator">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
            <span class="htmx-indicator" id="form-indicator">Saving...</span>
        </div>
    </form>
</div>
```

**`material_management/supplier_master/_supplier_confirm_delete.html` (Partial for HTMX)**

```html
<div class="p-6 text-center">
    <h3 class="text-xl font-bold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete supplier "<span class="font-semibold">{{ object.supplier_name }}</span>" (ID: {{ object.supplier_id }})?</p>
    
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#delete-indicator">
        {% csrf_token %}
        <div class="flex justify-center space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
            <span class="htmx-indicator" id="delete-indicator">Deleting...</span>
        </div>
    </form>
</div>
```

**`material_management/supplier_master/detail.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">Supplier Details: {{ supplier.supplier_name }}</h2>
    
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <dl class="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-6">
            <div>
                <dt class="text-sm font-medium text-gray-500">Supplier ID:</dt>
                <dd class="mt-1 text-lg text-gray-900">{{ supplier.supplier_id }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">Supplier Name:</dt>
                <dd class="mt-1 text-lg text-gray-900">{{ supplier.supplier_name }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">Generated Date:</dt>
                <dd class="mt-1 text-lg text-gray-900">{{ supplier.formatted_sys_date }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">Financial Year:</dt>
                <dd class="mt-1 text-lg text-gray-900">{{ supplier.fin_year.fin_year }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">Generated By:</dt>
                <dd class="mt-1 text-lg text-gray-900">{{ supplier.created_by_employee.employee_name }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">Company:</dt>
                <dd class="mt-1 text-lg text-gray-900">{{ supplier.company.company_name }}</dd>
            </div>
        </dl>
    </div>

    <div class="mt-8 flex justify-end space-x-4">
        <a href="{% url 'material_management:supplier_list' %}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded">
            Back to List
        </a>
        <button 
            class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'material_management:supplier_edit' pk=supplier.supplier_id %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Edit Supplier
        </button>
         <button 
            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'material_management:supplier_delete' pk=supplier.supplier_id %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Delete Supplier
        </button>
    </div>

    <!-- Modal for form (Add/Edit/Delete) - same as list.html -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto relative">
            <button type="button" class="absolute top-3 right-3 text-gray-500 hover:text-gray-700"
                    _="on click remove .is-active from #modal">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
        </div>
    </div>
</div>
{% endblock %}
```

**`material_management/supplier_master/print_all.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8 print:py-0">
    <h2 class="text-2xl font-bold text-gray-800 mb-6 print:text-xl print:text-center print:mb-4">All Suppliers Report</h2>
    
    <div class="print:hidden flex justify-end mb-6 space-x-4">
        <button onclick="window.print()" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Print This Page
        </button>
        <a href="{% url 'material_management:supplier_list' %}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded">
            Back to List
        </a>
    </div>

    <div class="bg-white shadow-md rounded-lg overflow-hidden print:shadow-none print:rounded-none">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50 print:bg-white">
                <tr>
                    <th class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider print:text-sm">SN</th>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider print:text-sm">Fin Yrs</th>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider print:text-sm">Supplier Name</th>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider print:text-sm">Code</th>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider print:text-sm">Gen. Date</th>
                    <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider print:text-sm">Gen. By</th>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
                {% for supplier in suppliers %}
                <tr>
                    <td class="py-2 px-4 whitespace-nowrap text-right text-sm text-gray-900">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-left text-sm text-gray-900">{{ supplier.fin_year.fin_year }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-left text-sm text-gray-900">{{ supplier.supplier_name }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-left text-sm text-gray-900">{{ supplier.supplier_id }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-left text-sm text-gray-900">{{ supplier.formatted_sys_date }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-left text-sm text-gray-900">{{ supplier.created_by_employee.employee_name }}</td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="6" class="py-4 text-center text-gray-500">No suppliers found for this report.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}
```

#### 4.5 URLs (`material_management/urls.py`)

```python
from django.urls import path
from .views import (
    SupplierMasterListView, SupplierMasterTablePartialView,
    SupplierMasterCreateView, SupplierMasterUpdateView, SupplierMasterDeleteView,
    SupplierMasterDetailView, SupplierMasterPrintAllView, SupplierMasterAutocompleteView
)

app_name = 'material_management' # Define app_name for namespacing URLs

urlpatterns = [
    # Main list view
    path('supplier/', SupplierMasterListView.as_view(), name='supplier_list'),
    
    # HTMX partial for the table (DataTables)
    path('supplier/table/', SupplierMasterTablePartialView.as_view(), name='supplier_table'), 
    
    # CRUD operations
    path('supplier/add/', SupplierMasterCreateView.as_view(), name='supplier_add'),
    path('supplier/edit/<str:pk>/', SupplierMasterUpdateView.as_view(), name='supplier_edit'), 
    path('supplier/delete/<str:pk>/', SupplierMasterDeleteView.as_view(), name='supplier_delete'),
    
    # Detail view (linked from supplier name)
    path('supplier/<str:supplier_id>/details/', SupplierMasterDetailView.as_view(), name='supplier_detail'),
    
    # Print All view
    path('supplier/print_all/', SupplierMasterPrintAllView.as_view(), name='supplier_print_all'),

    # Autocomplete endpoint for search box
    path('supplier/autocomplete/', SupplierMasterAutocompleteView.as_view(), name='supplier_autocomplete'),
]
```

#### 4.6 Tests (`material_management/tests.py`)

```python       
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from .models import SupplierMaster, FinancialYear, Employee, Company
from datetime import datetime

class SupplierMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dummy related data first
        cls.company = Company.objects.create(comp_id=1, company_name="Test Company")
        cls.fin_year_2023 = FinancialYear.objects.create(fin_year_id=2023, fin_year="2023-2024")
        cls.fin_year_2022 = FinancialYear.objects.create(fin_year_id=2022, fin_year="2022-2023")
        cls.employee = Employee.objects.create(emp_id=101, employee_name="Test Employee")

        # Create test supplier data
        cls.supplier1 = SupplierMaster.objects.create(
            supplier_id='SUP001',
            supplier_name='Alpha Supplier',
            sys_date_str='01-15-2023',
            fin_year=cls.fin_year_2023,
            created_by_employee=cls.employee,
            company=cls.company
        )
        cls.supplier2 = SupplierMaster.objects.create(
            supplier_id='SUP002',
            supplier_name='Beta Industries',
            sys_date_str='02-20-2022',
            fin_year=cls.fin_year_2022,
            created_by_employee=cls.employee,
            company=cls.company
        )
  
    def test_supplier_creation(self):
        supplier = SupplierMaster.objects.get(supplier_id='SUP001')
        self.assertEqual(supplier.supplier_name, 'Alpha Supplier')
        self.assertEqual(supplier.sys_date_str, '01-15-2023')
        self.assertEqual(supplier.fin_year, self.fin_year_2023)
        self.assertEqual(supplier.created_by_employee, self.employee)
        self.assertEqual(supplier.company, self.company)
        
    def test_formatted_sys_date_property(self):
        supplier = SupplierMaster.objects.get(supplier_id='SUP001')
        self.assertEqual(supplier.formatted_sys_date, '15/01/2023')

        # Test with alternative date format (DD-MM-YYYY)
        supplier_alt_date = SupplierMaster.objects.create(
            supplier_id='SUP003', supplier_name='Gamma Co', sys_date_str='28-12-2023',
            fin_year=self.fin_year_2023, created_by_employee=self.employee, company=self.company
        )
        self.assertEqual(supplier_alt_date.formatted_sys_date, '28/12/2023')

        # Test with unparseable date
        supplier_bad_date = SupplierMaster.objects.create(
            supplier_id='SUP004', supplier_name='Delta Inc', sys_date_str='NOT-A-DATE',
            fin_year=self.fin_year_2023, created_by_employee=self.employee, company=self.company
        )
        self.assertEqual(supplier_bad_date.formatted_sys_date, 'NOT-A-DATE')
        
    def test_get_details_url(self):
        supplier = SupplierMaster.objects.get(supplier_id='SUP001')
        expected_url = f"/material_management/supplier/{supplier.supplier_id}/details/"
        self.assertEqual(supplier.get_details_url(), expected_url)

    def test_get_filtered_suppliers_no_filter(self):
        suppliers = SupplierMaster.get_filtered_suppliers()
        self.assertEqual(suppliers.count(), 2) # Only the two created in setUpTestData (SUP001, SUP002)

    def test_get_filtered_suppliers_by_name(self):
        suppliers = SupplierMaster.get_filtered_suppliers(search_value='Alpha')
        self.assertEqual(suppliers.count(), 1)
        self.assertEqual(suppliers.first().supplier_id, 'SUP001')

    def test_get_filtered_suppliers_by_id_in_search(self):
        suppliers = SupplierMaster.get_filtered_suppliers(search_value='Beta Industries [SUP002]')
        self.assertEqual(suppliers.count(), 1)
        self.assertEqual(suppliers.first().supplier_id, 'SUP002')

    def test_get_filtered_suppliers_by_company_id(self):
        suppliers = SupplierMaster.get_filtered_suppliers(company_id=self.company.comp_id)
        self.assertEqual(suppliers.count(), 2)

    def test_get_filtered_suppliers_by_financial_year_lte(self):
        suppliers = SupplierMaster.get_filtered_suppliers(financial_year_id=self.fin_year_2023.fin_year_id)
        # Should include both 2022 and 2023 financial years
        self.assertEqual(suppliers.count(), 2) 
        suppliers_2022 = SupplierMaster.get_filtered_suppliers(financial_year_id=self.fin_year_2022.fin_year_id)
        self.assertEqual(suppliers_2022.count(), 1)
        self.assertEqual(suppliers_2022.first().supplier_id, 'SUP002')

class SupplierMasterViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dummy related data
        cls.company = Company.objects.create(comp_id=1, company_name="Test Company")
        cls.fin_year_2023 = FinancialYear.objects.create(fin_year_id=2023, fin_year="2023-2024")
        cls.fin_year_2022 = FinancialYear.objects.create(fin_year_id=2022, fin_year="2022-2023")
        cls.employee = Employee.objects.create(emp_id=101, employee_name="Test Employee")

        # Create test supplier data
        cls.supplier1 = SupplierMaster.objects.create(
            supplier_id='SUP001',
            supplier_name='Alpha Supplier',
            sys_date_str='01-15-2023',
            fin_year=cls.fin_year_2023,
            created_by_employee=cls.employee,
            company=cls.company
        )
        cls.supplier2 = SupplierMaster.objects.create(
            supplier_id='SUP002',
            supplier_name='Beta Industries',
            sys_date_str='02-20-2022',
            fin_year=cls.fin_year_2022,
            created_by_employee=cls.employee,
            company=cls.company
        )
    
    def setUp(self):
        self.client = Client()
        # Set session data to mimic ASP.NET behavior
        session = self.client.session
        session['compid'] = self.company.comp_id
        session['finyear'] = self.fin_year_2023.fin_year_id
        session['sessionid'] = self.employee.emp_id # For created_by_employee_id
        session.save()
    
    def test_list_view(self):
        response = self.client.get(reverse('material_management:supplier_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/supplier_master/list.html')
        # Initial list view should return an empty queryset as the table is HTMX loaded
        self.assertQuerysetEqual(response.context['suppliers'], [])
        
    def test_table_partial_view(self):
        # Test HTMX request for the table
        response = self.client.get(reverse('material_management:supplier_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/supplier_master/_supplier_table.html')
        self.assertContains(response, 'Alpha Supplier')
        self.assertContains(response, 'Beta Industries')
        # Check filtering by search value
        response_filtered = self.client.get(reverse('material_management:supplier_table'), {'search_value': 'Alpha'}, HTTP_HX_REQUEST='true')
        self.assertContains(response_filtered, 'Alpha Supplier')
        self.assertNotContains(response_filtered, 'Beta Industries')

    def test_create_view_get(self):
        response = self.client.get(reverse('material_management:supplier_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/supplier_master/_supplier_form.html')
        self.assertTrue('form' in response.context)
        
    def test_create_view_post_success(self):
        data = {
            'supplier_id': 'SUP003',
            'supplier_name': 'New Supplier Ltd.',
            'sys_date_str': '03-01-2024',
            'fin_year': self.fin_year_2023.fin_year_id,
            'created_by_employee': self.employee.emp_id,
            'company': self.company.comp_id,
        }
        response = self.client.post(reverse('material_management:supplier_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX No Content response
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertIn('refreshSupplierMasterList', response.headers['HX-Trigger'])
        self.assertTrue(SupplierMaster.objects.filter(supplier_id='SUP003').exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Supplier added successfully.')

    def test_create_view_post_invalid(self):
        data = {
            'supplier_id': 'SUP004',
            'supplier_name': 'Invalid Date Supplier',
            'sys_date_str': 'invalid-date', # Invalid format
            'fin_year': self.fin_year_2023.fin_year_id,
            'created_by_employee': self.employee.emp_id,
            'company': self.company.comp_id,
        }
        response = self.client.post(reverse('material_management:supplier_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400) # Bad Request for form errors
        self.assertTemplateUsed(response, 'material_management/supplier_master/_supplier_form.html')
        self.assertContains(response, 'Invalid date format.') # Check for error message in returned form HTML

    def test_update_view_get(self):
        response = self.client.get(reverse('material_management:supplier_edit', args=[self.supplier1.supplier_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/supplier_master/_supplier_form.html')
        self.assertContains(response, 'Alpha Supplier')
        
    def test_update_view_post_success(self):
        data = {
            'supplier_id': self.supplier1.supplier_id, # Must include PK for update
            'supplier_name': 'Alpha Corp Updated',
            'sys_date_str': '01-15-2023',
            'fin_year': self.fin_year_2023.fin_year_id,
            'created_by_employee': self.employee.emp_id,
            'company': self.company.comp_id,
        }
        response = self.client.post(reverse('material_management:supplier_edit', args=[self.supplier1.supplier_id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertIn('refreshSupplierMasterList', response.headers['HX-Trigger'])
        self.supplier1.refresh_from_db()
        self.assertEqual(self.supplier1.supplier_name, 'Alpha Corp Updated')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Supplier updated successfully.')

    def test_delete_view_get(self):
        response = self.client.get(reverse('material_management:supplier_delete', args=[self.supplier1.supplier_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/supplier_master/_supplier_confirm_delete.html')
        self.assertContains(response, 'Alpha Supplier')
        
    def test_delete_view_post_success(self):
        response = self.client.post(reverse('material_management:supplier_delete', args=[self.supplier1.supplier_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertIn('refreshSupplierMasterList', response.headers['HX-Trigger'])
        self.assertFalse(SupplierMaster.objects.filter(supplier_id='SUP001').exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Supplier deleted successfully.')

    def test_detail_view(self):
        response = self.client.get(reverse('material_management:supplier_detail', args=[self.supplier1.supplier_id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/supplier_master/detail.html')
        self.assertContains(response, 'Alpha Supplier')
        self.assertEqual(response.context['supplier'].supplier_id, 'SUP001')

    def test_print_all_view(self):
        response = self.client.get(reverse('material_management:supplier_print_all'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/supplier_master/print_all.html')
        self.assertContains(response, 'All Suppliers Report')
        self.assertContains(response, 'Alpha Supplier')
        self.assertContains(response, 'Beta Industries')
        # Check filtered context based on session (e.g., if finyear_id filtered)
        # Note: Current get_queryset in PrintAll uses all active session filters

    def test_autocomplete_view(self):
        response = self.client.get(reverse('material_management:supplier_autocomplete'), {'prefixText': 'al'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        self.assertIn('Alpha Supplier [SUP001]', response.json())
        self.assertNotIn('Beta Industries [SUP002]', response.json())

    def test_autocomplete_view_no_match(self):
        response = self.client.get(reverse('material_management:supplier_autocomplete'), {'prefixText': 'xyz'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        self.assertEqual(response.json(), [])

    def test_autocomplete_view_with_company_filter(self):
        # Create a supplier for a different company
        other_company = Company.objects.create(comp_id=2, company_name="Other Company")
        SupplierMaster.objects.create(
            supplier_id='SUP005', supplier_name='Gamma Corp', sys_date_str='04-01-2024',
            fin_year=self.fin_year_2023, created_by_employee=self.employee, company=other_company
        )
        
        # Ensure session compid is set for this test
        session = self.client.session
        session['compid'] = self.company.comp_id
        session.save()

        response = self.client.get(reverse('material_management:supplier_autocomplete'), {'prefixText': 'gamm'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), []) # Gamma Corp should not be returned as it's from another company

        # Change session to the other company
        session['compid'] = other_company.comp_id
        session.save()
        response_other_comp = self.client.get(reverse('material_management:supplier_autocomplete'), {'prefixText': 'gamm'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response_other_comp.status_code, 200)
        self.assertIn('Gamma Corp [SUP005]', response_other_comp.json())
```

### Step 5: HTMX and Alpine.js Integration

*   **List View:**
    *   The main `list.html` uses `hx-get` on `div#supplierMasterTable-container` with `hx-trigger="load, refreshSupplierMasterList from:body"` to load the table content initially and refresh it after CRUD operations (triggered by `HX-Trigger` header from form submissions).
    *   Buttons for "Add New Supplier", "Edit", and "Delete" use `hx-get` to fetch the respective forms into `div#modalContent`, and Alpine.js `x-data` and `_=` directives are used to toggle the modal's `is-active` class.
*   **Forms (Add/Edit/Delete):**
    *   The form templates (`_supplier_form.html`, `_supplier_confirm_delete.html`) are partials.
    *   Forms use `hx-post` to submit data back to the same URL (`request.path`).
    *   `hx-swap="none"` and a `204 No Content` response (with `HX-Trigger`) on successful submission ensure the modal closes and the list updates without full page reload.
    *   `hx-indicator` is added for visual feedback during form submission/deletion.
    *   `400 Bad Request` responses on form invalidation ensure the form with errors is swapped back into the modal for correction.
*   **DataTables:**
    *   DataTables initialization script is placed within the `list.html` and re-initializes `$('#supplierMasterTable').DataTable()` using an `htmx:afterSwap` event listener on `document.body` or a `$(document).ready` inside the partial, ensuring DataTables is applied when the table content is loaded via HTMX.
*   **Autocomplete Search:**
    *   `input#txtSearchValue` uses `hx-get` to `{% url 'material_management:supplier_autocomplete' %}` and `hx-target="#autocomplete-suggestions"` with `hx-trigger="keyup changed delay:300ms, search"` for live suggestions.
    *   A simple `div` (`#autocomplete-suggestions`) acts as the target for the autocomplete results (which are plain HTML `div`s as `autocomplete-suggestion-item`s).
    *   JavaScript handles the click event on a suggestion to populate the search box and trigger the `Search` button's HTMX request.

## Final Notes

This comprehensive plan transforms the legacy ASP.NET Web Forms functionality into a modern Django application, leveraging Django's ORM, Class-Based Views, and a dynamic frontend powered by HTMX, Alpine.js, and DataTables. The structure ensures maintainability, scalability, and a significantly improved user experience with minimal traditional JavaScript, focusing on server-rendered HTML and client-side partial updates. The provided tests ensure the integrity and functionality of the migrated components.