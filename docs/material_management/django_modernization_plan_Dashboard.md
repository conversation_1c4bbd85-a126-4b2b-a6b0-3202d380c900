## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

The provided ASP.NET code for `Dashboard.aspx` and its code-behind `Dashboard.aspx.cs` is very minimal, essentially an empty page with content placeholders and an empty `Page_Load` method. This indicates a placeholder or a page with no immediately visible direct data interactions or complex logic within these specific files.

For the purpose of demonstrating a comprehensive Django modernization plan that leverages AI-assisted automation, we will infer a typical dashboard functionality based on its name (`Module_MaterialManagement_DashBoard`). A dashboard in an ERP's Material Management module would likely display a list of material items or key metrics. Since no explicit database or UI elements are present, we will *hypothesize* a core entity: `Material`. This allows us to illustrate the full conversion process for a common business object.

This modernization strategy focuses on automating the creation of robust, maintainable, and high-performance Django components. By adopting this approach, your organization will benefit from:

*   **Reduced Development Time:** AI-assisted generation of boilerplate code for models, forms, views, templates, and tests significantly speeds up development and migration.
*   **Improved Code Quality:** Enforcing strict architectural patterns like "Fat Models, Thin Views" and best practices (DRY, test coverage) leads to more reliable and easier-to-maintain applications.
*   **Enhanced User Experience:** Utilizing HTMX and Alpine.js provides a responsive, app-like feel without complex JavaScript frameworks, leading to faster interactions and happier users.
*   **Scalability and Performance:** Django's robust framework combined with efficient database interaction and client-side rendering ensures the application can handle growing demands.
*   **Simplified Maintenance:** A consistent, well-tested codebase reduces the effort and cost associated with future updates and bug fixes.

---

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Since the provided ASP.NET code does not explicitly define database interactions (like `SqlDataSource` or direct ADO.NET calls), we will infer a common table structure for a "Material Management Dashboard."

**Inferred Database Table:** `tblMaterial`

**Inferred Columns:**
*   `MaterialID` (Primary Key, e.g., INT)
*   `MaterialName` (e.g., NVARCHAR)
*   `Description` (e.g., NVARCHAR)
*   `Quantity` (e.g., INT)
*   `UnitOfMeasure` (e.g., NVARCHAR)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

Given the minimal ASP.NET code, no explicit CRUD operations are visible. However, a dashboard for `MaterialManagement` typically provides at least a "Read" (listing) functionality, and often links to "Create," "Update," and "Delete" actions for managing the underlying data. For a complete modernization plan, we will generate the full CRUD suite for the inferred `Material` entity.

*   **Create:** Ability to add new material items.
*   **Read:** Display a list of all material items.
*   **Update:** Ability to modify existing material item details.
*   **Delete:** Ability to remove material items.

No specific validation logic is present in the ASP.NET snippet, so we will implement standard form validation in Django.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

No UI controls are explicitly defined in the provided `Dashboard.aspx` beyond the content placeholders. Based on the inferred functionality:

*   **Data Display:** A `GridView` (ASP.NET) equivalent for displaying a list of `Material` items will be replaced by a Django template rendering a table enhanced with **DataTables**.
*   **Data Entry/Edit:** `TextBox` controls (ASP.NET) for `MaterialName`, `Description`, `Quantity`, `UnitOfMeasure` would typically be used, which will be replaced by standard Django form fields rendered with Tailwind CSS styling and presented in HTMX-powered modals.
*   **Actions:** `Button` or `LinkButton` controls for "Add New Material," "Edit," and "Delete" will be replaced by HTML buttons/links with **HTMX** attributes to trigger dynamic content loading and form submissions without full page reloads.

---

## Step 4: Generate Django Code

We will create a new Django app, perhaps named `material_management`, to house the converted components.

### 4.1 Models (`material_management/models.py`)

Task: Create a Django model based on the database schema.

## Instructions:

The `Material` model will map to the `tblMaterial` table. We'll include a method for business logic, demonstrating the "Fat Model" principle.

```python
from django.db import models
from django.urls import reverse

class Material(models.Model):
    # It's good practice to explicitly define primary key if it's not 'id' or if db_column is needed.
    # Assuming MaterialID is the primary key and automatically mapped by Django if named 'id'.
    # If the actual DB column is MaterialID and not auto-incrementing 'id', adjust:
    # material_id = models.IntegerField(db_column='MaterialID', primary_key=True)
    
    # Assuming 'id' is the default primary key column managed by Django, if not, adjust.
    # If MaterialID is an auto-incrementing primary key on the database, Django will handle it automatically.
    
    name = models.CharField(max_length=255, db_column='MaterialName', verbose_name="Material Name")
    description = models.TextField(db_column='Description', blank=True, null=True, verbose_name="Description")
    quantity = models.IntegerField(db_column='Quantity', verbose_name="Quantity", default=0)
    unit_of_measure = models.CharField(max_length=50, db_column='UnitOfMeasure', verbose_name="Unit of Measure", blank=True, null=True)

    class Meta:
        managed = False  # Django will not manage this table's schema (it exists externally)
        db_table = 'tblMaterial'
        verbose_name = 'Material Item'
        verbose_name_plural = 'Material Items'
        ordering = ['name'] # Default ordering for lists

    def __str__(self):
        """Returns the material name for easy identification."""
        return self.name
        
    def get_absolute_url(self):
        """Returns the URL to view a specific material item."""
        return reverse('material_edit', kwargs={'pk': self.pk})

    def get_display_summary(self):
        """
        Example of business logic in the model:
        Generates a summary string for display purposes.
        """
        return f"{self.name} ({self.quantity} {self.unit_of_measure})"
```

### 4.2 Forms (`material_management/forms.py`)

Task: Define a Django form for user input.

## Instructions:

A `ModelForm` for `Material` will handle input and validation. Tailwind CSS classes are applied via `widgets`.

```python
from django import forms
from .models import Material

class MaterialForm(forms.ModelForm):
    class Meta:
        model = Material
        fields = ['name', 'description', 'quantity', 'unit_of_measure']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'quantity': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'unit_of_measure': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        
    def clean_quantity(self):
        """Custom validation for quantity to ensure it's not negative."""
        quantity = self.cleaned_data.get('quantity')
        if quantity is not None and quantity < 0:
            raise forms.ValidationError("Quantity cannot be negative.")
        return quantity
```

### 4.3 Views (`material_management/views.py`)

Task: Implement CRUD operations using CBVs.

## Instructions:

Views are kept concise, typically 5-15 lines, delegating business logic and complex data handling to the model or forms. HTMX responses are managed for dynamic updates. A partial view for the table is added to support HTMX reloads.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import Material
from .forms import MaterialForm

# Main view to display the list of materials (uses HTMX to load the table)
class MaterialListView(ListView):
    model = Material
    template_name = 'material_management/material/list.html'
    context_object_name = 'materials' # Renamed for clarity in template

# Partial view to render the material table, used by HTMX
class MaterialTablePartialView(ListView):
    model = Material
    template_name = 'material_management/material/_material_table.html' # Use partial template
    context_object_name = 'materials'

class MaterialCreateView(CreateView):
    model = Material
    form_class = MaterialForm
    template_name = 'material_management/material/_material_form.html' # Use partial template for modal
    success_url = reverse_lazy('material_list') # Redirect after non-HTMX submission

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Material Item added successfully.')
        # If HTMX request, trigger client-side event to refresh list and close modal
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, tells HTMX to do nothing with the response body
                headers={
                    'HX-Trigger': '{"refreshMaterialList": "", "closeModal": ""}' # Trigger multiple events
                }
            )
        return response

class MaterialUpdateView(UpdateView):
    model = Material
    form_class = MaterialForm
    template_name = 'material_management/material/_material_form.html' # Use partial template for modal
    context_object_name = 'material'
    success_url = reverse_lazy('material_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Material Item updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshMaterialList": "", "closeModal": ""}'
                }
            )
        return response

class MaterialDeleteView(DeleteView):
    model = Material
    template_name = 'material_management/material/_material_confirm_delete.html' # Use partial template for modal
    context_object_name = 'material'
    success_url = reverse_lazy('material_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Material Item deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshMaterialList": "", "closeModal": ""}'
                }
            )
        return response
```

### 4.4 Templates (`material_management/templates/material_management/material/`)

Task: Create templates for each view.

## Instructions:

Templates are designed for modularity and HTMX integration. `_material_table.html`, `_material_form.html`, and `_material_confirm_delete.html` are partials to be loaded dynamically. The main `list.html` includes the modal structure and triggers for HTMX.

#### `list.html` (Main Material List Page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Material Items</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'material_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus-circle mr-2"></i> Add New Material
        </button>
    </div>
    
    {# Container for the DataTables table, loaded via HTMX #}
    <div id="materialTable-container"
         hx-trigger="load, refreshMaterialList from:body"
         hx-get="{% url 'material_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg p-6">
        {# Loading indicator #}
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Material Data...</p>
        </div>
    </div>
    
    {# Modal for forms (Add/Edit/Delete) #}
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me
            on closeModal from body remove .is-active from me"
         x-data="{ show: false }" x-show="show" x-transition.opacity
         @keydown.escape.window="show = false"
         hx-on::after-request="if (event.detail.successful) show = true"> {# Show modal after content is loaded #}

        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-2xl w-full mx-auto"
             @click.stop> {# Prevent clicks inside modal from closing it #}
            {# Content loaded here via HTMX #}
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-gray-400"></div>
                <p class="mt-2 text-gray-500">Loading form...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# Ensure Alpine.js and Font Awesome are included in core/base.html #}
<script>
    // Alpine.js component for modal state, if more complex state management is needed
    // Otherwise, htmx + _hyperscript handles it directly as shown above.
    document.addEventListener('alpine:init', () => {
        Alpine.data('modalController', () => ({
            isOpen: false,
            open() { this.isOpen = true },
            close() { this.isOpen = false },
        }));
    });
</script>
{% endblock %}
```

#### `_material_table.html` (Partial for DataTables)

```html
<table id="materialTable" class="min-w-full bg-white border border-gray-200 rounded-lg">
    <thead>
        <tr>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Material Name</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Description</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Quantity</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Unit of Measure</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for material in materials %}
        <tr class="hover:bg-gray-50">
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ forloop.counter }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ material.name }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ material.description|default_if_none:"N/A" }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ material.quantity }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ material.unit_of_measure|default_if_none:"N/A" }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 text-xs transition duration-300 ease-in-out"
                    hx-get="{% url 'material_edit' material.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-xs transition duration-300 ease-in-out"
                    hx-get="{% url 'material_delete' material.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <i class="fas fa-trash-alt"></i> Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-4 px-4 text-center text-gray-600">No material items found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Initialize DataTables after the content is loaded via HTMX
    $(document).ready(function() {
        $('#materialTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "language": {
                "search": "Search:"
            },
            "dom": '<"flex flex-col sm:flex-row justify-between items-center mb-4"lf><"block w-full overflow-x-auto"t><"flex flex-col sm:flex-row justify-between items-center mt-4"ip>'
        });
    });
</script>
```

#### `_material_form.html` (Partial for Add/Edit Forms)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Material Item</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" 
          hx-on::after-request="if(event.detail.successful) htmx.trigger(this.closest('#modal'), 'closeModal')">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click htmx.trigger(this.closest('#modal'), 'closeModal')">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                <i class="fas fa-save mr-2"></i> Save Material
            </button>
        </div>
    </form>
</div>
```

#### `_material_confirm_delete.html` (Partial for Delete Confirmation)

```html
<div class="p-6 text-center">
    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
        <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.174 3.353 1.94 3.353h12.126c1.766 0 2.806-1.853 1.94-3.353L12 3.373zM12 20.25a.75.75 0 100-1.5.75.75 0 000 1.5z" />
        </svg>
    </div>
    <h3 class="mt-5 text-xl font-semibold text-gray-900">Delete Material Item</h3>
    <p class="mt-2 text-sm text-gray-500">Are you sure you want to delete "{{ material.name }}"? This action cannot be undone.</p>

    <div class="mt-6 flex justify-center space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
            _="on click htmx.trigger(this.closest('#modal'), 'closeModal')">
            Cancel
        </button>
        <form hx-post="{{ request.path }}" hx-swap="none" 
              hx-on::after-request="if(event.detail.successful) htmx.trigger(this.closest('#modal'), 'closeModal')"
              class="inline-block">
            {% csrf_token %}
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                <i class="fas fa-trash-alt mr-2"></i> Delete
            </button>
        </form>
    </div>
</div>
```

### 4.5 URLs (`material_management/urls.py`)

Task: Define URL patterns for the views.

## Instructions:

This maps the views to specific URLs, providing clean and consistent routing.

```python
from django.urls import path
from .views import (
    MaterialListView, 
    MaterialCreateView, 
    MaterialUpdateView, 
    MaterialDeleteView,
    MaterialTablePartialView, # For HTMX loaded table
)

urlpatterns = [
    path('materials/', MaterialListView.as_view(), name='material_list'),
    path('materials/add/', MaterialCreateView.as_view(), name='material_add'),
    path('materials/<int:pk>/edit/', MaterialUpdateView.as_view(), name='material_edit'),
    path('materials/<int:pk>/delete/', MaterialDeleteView.as_view(), name='material_delete'),
    # HTMX partial endpoint for the table reload
    path('materials/table/', MaterialTablePartialView.as_view(), name='material_table'),
]
```
Remember to include this `urls.py` in your project's main `urls.py`:
`path('material-management/', include('material_management.urls')),`

### 4.6 Tests (`material_management/tests.py`)

Task: Write tests for the model and views.

## Instructions:

Comprehensive tests ensure that the new Django application is robust and functions as expected. This includes unit tests for the model's logic and integration tests for the views, including HTMX specific interactions.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from .models import Material

class MaterialModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.material_data = {
            'name': 'Test Material 1',
            'description': 'A general purpose test material.',
            'quantity': 100,
            'unit_of_measure': 'pcs'
        }
        cls.material = Material.objects.create(**cls.material_data)
  
    def test_material_creation(self):
        """Test that a Material object can be created correctly."""
        self.assertEqual(self.material.name, 'Test Material 1')
        self.assertEqual(self.material.quantity, 100)
        self.assertEqual(self.material.unit_of_measure, 'pcs')
        self.assertTrue(isinstance(self.material, Material))

    def test_str_method(self):
        """Test the __str__ method returns the material name."""
        self.assertEqual(str(self.material), 'Test Material 1')

    def test_get_absolute_url(self):
        """Test get_absolute_url returns the correct edit URL."""
        expected_url = reverse('material_edit', kwargs={'pk': self.material.pk})
        self.assertEqual(self.material.get_absolute_url(), expected_url)

    def test_get_display_summary(self):
        """Test the custom display summary method."""
        expected_summary = 'Test Material 1 (100 pcs)'
        self.assertEqual(self.material.get_display_summary(), expected_summary)

    def test_meta_options(self):
        """Test Meta options like db_table and verbose_name."""
        self.assertEqual(self.material._meta.db_table, 'tblMaterial')
        self.assertEqual(self.material._meta.verbose_name, 'Material Item')
        self.assertEqual(self.material._meta.verbose_name_plural, 'Material Items')
        # Check if managed is False
        self.assertFalse(self.material._meta.managed)

class MaterialViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create initial test data for views
        cls.material1 = Material.objects.create(
            name='Material A', description='Desc A', quantity=50, unit_of_measure='kg'
        )
        cls.material2 = Material.objects.create(
            name='Material B', description='Desc B', quantity=20, unit_of_measure='liters'
        )
    
    def setUp(self):
        # Set up a new client for each test method to ensure isolation
        self.client = Client()
    
    def test_list_view_get(self):
        """Test Material list view loads correctly."""
        response = self.client.get(reverse('material_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/material/list.html')
        self.assertContains(response, 'Material Items') # Check for title

    def test_table_partial_view_get(self):
        """Test HTMX loaded table partial view."""
        response = self.client.get(reverse('material_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/material/_material_table.html')
        self.assertContains(response, self.material1.name)
        self.assertContains(response, self.material2.name)
        self.assertTrue('materials' in response.context)
        self.assertContains(response, '<table id="materialTable"') # Check if DataTables table is present

    def test_create_view_get(self):
        """Test GET request to create view loads the form."""
        response = self.client.get(reverse('material_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/material/_material_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Material Item')

    def test_create_view_post_success(self):
        """Test POST request to create view successfully creates material."""
        data = {
            'name': 'New Material C',
            'description': 'Description for C',
            'quantity': 75,
            'unit_of_measure': 'grams'
        }
        response = self.client.post(reverse('material_add'), data)
        # For non-HTMX requests, it redirects
        self.assertEqual(response.status_code, 302) 
        self.assertTrue(Material.objects.filter(name='New Material C').exists())
        self.assertEqual(Material.objects.count(), 3) # Two existing + one new

    def test_create_view_post_htmx_success(self):
        """Test POST request for create view with HTMX headers."""
        data = {
            'name': 'New Material D',
            'description': 'Description for D',
            'quantity': 10,
            'unit_of_measure': 'units'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('material_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertEqual(response.headers.get('HX-Trigger'), '{"refreshMaterialList": "", "closeModal": ""}')
        self.assertTrue(Material.objects.filter(name='New Material D').exists())
        self.assertEqual(Material.objects.count(), 3)

    def test_create_view_post_invalid(self):
        """Test POST request with invalid data (e.g., negative quantity)."""
        data = {
            'name': 'Invalid Material',
            'description': 'Invalid',
            'quantity': -5, # Invalid quantity
            'unit_of_measure': 'pcs'
        }
        response = self.client.post(reverse('material_add'), data)
        self.assertEqual(response.status_code, 200) # Should render form again with errors
        self.assertContains(response, 'Quantity cannot be negative.')
        self.assertFalse(Material.objects.filter(name='Invalid Material').exists())

    def test_update_view_get(self):
        """Test GET request to update view loads the form with existing data."""
        response = self.client.get(reverse('material_edit', args=[self.material1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/material/_material_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, self.material1.name) # Check if existing data is pre-filled
        self.assertContains(response, 'Edit Material Item')

    def test_update_view_post_success(self):
        """Test POST request to update view successfully updates material."""
        updated_name = 'Material A Updated'
        data = {
            'name': updated_name,
            'description': self.material1.description,
            'quantity': 55,
            'unit_of_measure': self.material1.unit_of_measure
        }
        response = self.client.post(reverse('material_edit', args=[self.material1.pk]), data)
        self.assertEqual(response.status_code, 302)
        self.material1.refresh_from_db()
        self.assertEqual(self.material1.name, updated_name)
        self.assertEqual(self.material1.quantity, 55)

    def test_update_view_post_htmx_success(self):
        """Test POST request for update view with HTMX headers."""
        updated_name = 'Material B HTMX Update'
        data = {
            'name': updated_name,
            'description': self.material2.description,
            'quantity': 25,
            'unit_of_measure': self.material2.unit_of_measure
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('material_edit', args=[self.material2.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), '{"refreshMaterialList": "", "closeModal": ""}')
        self.material2.refresh_from_db()
        self.assertEqual(self.material2.name, updated_name)

    def test_delete_view_get(self):
        """Test GET request to delete view loads the confirmation."""
        response = self.client.get(reverse('material_delete', args=[self.material1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/material/_material_confirm_delete.html')
        self.assertTrue('material' in response.context)
        self.assertContains(response, 'Delete Material Item')
        self.assertContains(response, f'Are you sure you want to delete "{self.material1.name}"?')

    def test_delete_view_post_success(self):
        """Test POST request to delete view successfully deletes material."""
        material_to_delete_pk = self.material1.pk
        response = self.client.post(reverse('material_delete', args=[material_to_delete_pk]))
        self.assertEqual(response.status_code, 302)
        self.assertFalse(Material.objects.filter(pk=material_to_delete_pk).exists())
        self.assertEqual(Material.objects.count(), 1) # Only material2 should remain

    def test_delete_view_post_htmx_success(self):
        """Test POST request for delete view with HTMX headers."""
        material_to_delete_pk = self.material2.pk
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('material_delete', args=[material_to_delete_pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), '{"refreshMaterialList": "", "closeModal": ""}')
        self.assertFalse(Material.objects.filter(pk=material_to_delete_pk).exists())
        self.assertEqual(Material.objects.count(), 1) # Only material1 should remain if test isolation works
```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

The provided Django code snippets demonstrate the integration of HTMX and Alpine.js for a modern, dynamic user interface without writing significant custom JavaScript.

*   **HTMX for CRUD Operations:**
    *   **Adding New Materials:** The "Add New Material" button uses `hx-get` to fetch the form template (`_material_form.html`) into a modal container (`#modalContent`). When the form within the modal is submitted via `hx-post`, the view returns a `204 No Content` status with an `HX-Trigger` header. This header tells HTMX to dispatch a `refreshMaterialList` event, which re-triggers the `hx-get` on the main table container (`#materialTable-container`), effectively refreshing the DataTables without a full page reload. It also triggers `closeModal` to hide the modal.
    *   **Editing Materials:** Similar to adding, "Edit" buttons use `hx-get` to load the pre-filled form into the same modal. Submission via `hx-post` follows the same `204` + `HX-Trigger` pattern for refreshing the list and closing the modal.
    *   **Deleting Materials:** "Delete" buttons fetch a confirmation partial (`_material_confirm_delete.html`) into the modal. Upon confirmation (via `hx-post`), the material is deleted, and the list is refreshed via `HX-Trigger`.
    *   **Dynamic Table Loading:** The `#materialTable-container` div uses `hx-trigger="load, refreshMaterialList from:body"` to ensure the DataTables content is loaded when the page first loads and whenever the `refreshMaterialList` event is triggered (after any CRUD operation). This keeps the list up-to-date automatically.

*   **Alpine.js for UI State Management:**
    *   While much of the modal logic is handled by HTMX and `_hyperscript` (`_="on click add .is-active to #modal"`), Alpine.js is present in the `list.html` block (`x-data="{ show: false }" x-show="show" x-transition.opacity`) to provide a more robust way to control the modal's visibility, especially with transitions and keyboard accessibility (`@keydown.escape.window="show = false"`). The `closeModal` event triggered by HTMX is also listened for by Alpine.js.
    *   Alpine.js complements HTMX by handling local UI state (like `show` for modal visibility) and simple client-side interactivity, keeping complex JavaScript frameworks unnecessary.

*   **DataTables for List Views:**
    *   The `_material_table.html` partial includes the `<table id="materialTable">` structure.
    *   A small `script` block within this partial (executed when the partial is loaded by HTMX) initializes DataTables on this table ID. This provides client-side searching, sorting, and pagination capabilities for the `Material` list, offering a powerful and user-friendly data presentation experience without additional backend complexity.

This approach ensures that all interactions are smooth and fast, improving the user experience significantly compared to traditional full page reloads, while maintaining a clean and manageable codebase.

## Final Notes

This comprehensive plan provides a blueprint for migrating the inferred `MaterialManagement` module from ASP.NET to Django. By following these structured, automated steps, your organization can efficiently modernize its legacy applications, achieving a more maintainable, performant, and user-friendly system. The focus on AI-assisted automation ensures that the conversion process is systematic, reduces manual effort, and promotes consistent adherence to modern best practices.