## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Given the minimal ASP.NET code for a "Dashboard" within "MaterialManagement_Reports" that lacks explicit database interactions or UI controls, we infer the primary entity. A common scenario for a dashboard related to material management would involve tracking `Material` items.

*   **Inferred Table Name**: `tblMaterial`
*   **Inferred Columns**:
    *   `MaterialName` (likely string type, e.g., VARCHAR)
    *   `Quantity` (likely numeric type, e.g., INT)
    *   `Description` (optional, string type, e.g., TEXT)
    *   `LastUpdated` (optional, datetime type)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

The provided `Page_Load` method is empty, and the `.aspx` contains only content placeholders, indicating no explicit backend functionality is defined in the snippets. However, as this is a "Dashboard" in a "MaterialManagement" module, standard CRUD (Create, Read, Update, Delete) operations for the inferred `Material` entity are typical and will be implemented to provide practical management capabilities for the dashboard.

*   **Create**: Add new material entries.
*   **Read**: Display a list of all materials.
*   **Update**: Modify existing material details.
*   **Delete**: Remove material entries.
*   **Validation**: Basic validation for required fields and data types (e.g., material name is not empty, quantity is a positive number).

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

The ASP.NET code uses master pages (`MasterPage.master`) and references `loadingNotifier.js`. This implies a structured layout and some form of client-side feedback for operations. Without specific ASP.NET controls (like `GridView`, `TextBox`, `Button`), we will infer the necessary UI components based on modern Django/HTMX/Alpine.js patterns for a CRUD dashboard:

*   **List View**: A main page displaying a list of `Material` records, implemented using DataTables for search, sort, and pagination.
*   **Add/Edit Form**: A modal form for creating new materials or editing existing ones, dynamically loaded via HTMX.
*   **Delete Confirmation**: A modal confirmation dialog for deleting materials, also loaded via HTMX.
*   **Client-Side Interactions**: HTMX will handle all dynamic content loading and form submissions without full page reloads. Alpine.js will manage the modal's open/close state.
*   **Styling**: Tailwind CSS will be used for a clean, modern look, aligning with the "modernization" goal.

## Step 4: Generate Django Code

We will create a new Django application named `material_management` to house these components.

### 4.1 Models (material_management/models.py)

```python
from django.db import models
from django.utils import timezone

class Material(models.Model):
    # Maps to tblMaterial in the existing database
    material_id = models.AutoField(db_column='MaterialID', primary_key=True) # Assuming an auto-incrementing ID
    name = models.CharField(db_column='MaterialName', max_length=255, verbose_name="Material Name")
    quantity = models.IntegerField(db_column='Quantity', verbose_name="Quantity")
    description = models.TextField(db_column='Description', blank=True, null=True, verbose_name="Description")
    last_updated = models.DateTimeField(db_column='LastUpdated', auto_now=True, verbose_name="Last Updated")

    class Meta:
        managed = False  # Tells Django not to manage table creation/deletion
        db_table = 'tblMaterial' # Explicitly names the table
        verbose_name = 'Material'
        verbose_name_plural = 'Materials'
        ordering = ['name'] # Default ordering for list views

    def __str__(self):
        return self.name

    # Business logic methods (Fat Model approach)
    def is_low_stock(self, threshold=10):
        """Checks if the material's quantity is below a given threshold."""
        return self.quantity < threshold

    def update_quantity(self, change_amount):
        """Increments or decrements the material quantity."""
        if self.quantity + change_amount < 0:
            raise ValueError("Quantity cannot be negative.")
        self.quantity += change_amount
        self.save()
        return self.quantity

    def get_absolute_url(self):
        # Useful for reverse lookups in some Django contexts, though not strictly needed for HTMX modals
        from django.urls import reverse
        return reverse('material_edit', kwargs={'pk': self.pk})

```

### 4.2 Forms (material_management/forms.py)

```python
from django import forms
from .models import Material

class MaterialForm(forms.ModelForm):
    class Meta:
        model = Material
        fields = ['name', 'quantity', 'description']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'quantity': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'description': forms.Textarea(attrs={'rows': 3, 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'name': 'Material Name',
            'quantity': 'Current Quantity',
            'description': 'Additional Details',
        }
        
    def clean_quantity(self):
        quantity = self.cleaned_data.get('quantity')
        if quantity is not None and quantity < 0:
            raise forms.ValidationError("Quantity cannot be negative.")
        return quantity

```

### 4.3 Views (material_management/views.py)

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from .models import Material
from .forms import MaterialForm

# Base class for HTMX-enabled form views to abstract common logic
class HtmxFormViewMixin:
    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, f'{self.model._meta.verbose_name} saved successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, tells HTMX to do nothing more than trigger
                headers={
                    'HX-Trigger': 'refreshMaterialList' # Custom event to trigger table refresh
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # If HTMX request, return the form with errors
            return response
        return response

class MaterialListView(ListView):
    model = Material
    template_name = 'material_management/material/list.html'
    context_object_name = 'materials' # Renamed for clarity in template

class MaterialTablePartialView(ListView):
    # This view is specifically for HTMX to fetch and swap the table content
    model = Material
    template_name = 'material_management/material/_material_table.html' # Partial template
    context_object_name = 'materials'
    # No success_url needed as it's a partial render

class MaterialCreateView(HtmxFormViewMixin, CreateView):
    model = Material
    form_class = MaterialForm
    template_name = 'material_management/material/_material_form.html' # Partial template for modal
    success_url = reverse_lazy('material_list') # Fallback for non-HTMX requests

class MaterialUpdateView(HtmxFormViewMixin, UpdateView):
    model = Material
    form_class = MaterialForm
    template_name = 'material_management/material/_material_form.html' # Partial template for modal
    success_url = reverse_lazy('material_list') # Fallback for non-HTMX requests

class MaterialDeleteView(DeleteView):
    model = Material
    template_name = 'material_management/material/_material_confirm_delete.html' # Partial template for modal
    success_url = reverse_lazy('material_list') # Fallback

    def delete(self, request, *args, **kwargs):
        messages.success(self.request, f'{self.get_object()._meta.verbose_name} deleted successfully.')
        response = super().delete(request, *args, **kwargs)
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialList'
                }
            )
        return response

```

### 4.4 Templates (material_management/templates/material_management/material/)

#### list.html

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Material Dashboard</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'material_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Material
        </button>
    </div>

    {# Success/Error Messages #}
    {% if messages %}
        <div class="mb-4">
            {% for message in messages %}
                <div class="p-3 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-800{% elif message.tags == 'error' %}bg-red-100 text-red-800{% else %}bg-blue-100 text-blue-800{% endif %}">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}
    
    <div id="materialTable-container"
         hx-trigger="load, refreshMaterialList from:body"
         hx-get="{% url 'material_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading materials data...</p>
        </div>
    </div>
    
    <!-- Modal for forms/confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-60 flex items-center justify-center p-4 z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ showModal: false }" x-init="$watch('showModal', value => { if(value) { $el.classList.remove('hidden'); } else { $el.classList.add('hidden'); } })">
        <div id="modalContent" class="bg-white p-6 rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto transform transition-all sm:my-8 sm:align-middle sm:max-w-xl sm:w-full">
            {# Content loaded by HTMX #}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.store('modal', {
            isOpen: false,
            open() { this.isOpen = true },
            close() { this.isOpen = false }
        });

        // Close modal on HTMX 204 response
        document.body.addEventListener('htmx:afterRequest', function(event) {
            if (event.detail.xhr.status === 204) {
                document.getElementById('modal').classList.remove('is-active');
            }
        });
        
        // Open modal when HTMX loads content into it
        document.body.addEventListener('htmx:afterSwap', function(event) {
            if (event.detail.target.id === 'modalContent') {
                 document.getElementById('modal').classList.add('is-active');
            }
        });
    });
</script>
{% endblock %}
```

#### _material_table.html (Partial for DataTables)

```html
<div class="bg-white shadow-md rounded-lg overflow-hidden">
    <table id="materialTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Material Name</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Quantity</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Description</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Last Updated</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for material in materials %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ material.name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ material.quantity }} {% if material.is_low_stock %}<span class="text-red-500 text-xs font-medium ml-1">(Low Stock!)</span>{% endif %}</td>
                <td class="py-3 px-4 text-sm text-gray-700 max-w-xs truncate" title="{{ material.description }}">{{ material.description|default:"-" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ material.last_updated|date:"Y-m-d H:i" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-medium py-1.5 px-3 rounded-md shadow-sm mr-2 transition duration-300 ease-in-out"
                        hx-get="{% url 'material_edit' material.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-600 text-white font-medium py-1.5 px-3 rounded-md shadow-sm transition duration-300 ease-in-out"
                        hx-get="{% url 'material_delete' material.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 text-center text-gray-500">No materials found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#materialTable')) {
            $('#materialTable').DataTable().destroy();
        }
        $('#materialTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "searching": true,
            "ordering": true,
            "info": true,
            "paging": true,
            "responsive": true
        });
    });
</script>
```

#### _material_form.html (Partial for Add/Edit Form)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">{{ form.instance.pk|yesno:'Edit,Add' }} Material</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-5">
        {% csrf_token %}
        
        {% for field in form %}
        <div>
            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                {{ field.label }}
                {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
            </label>
            {{ field }}
            {% if field.help_text %}
            <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
            {% endif %}
            {% if field.errors %}
            <p class="text-red-600 text-xs mt-1">{{ field.errors }}</p>
            {% endif %}
        </div>
        {% endfor %}
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="px-5 py-2.5 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="px-5 py-2.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out">
                Save Material
            </button>
        </div>
    </form>
</div>
```

#### _material_confirm_delete.html (Partial for Delete Confirmation)

```html
<div class="p-6 text-center">
    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
        <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
    </div>
    <h3 class="mt-4 text-xl leading-6 font-semibold text-gray-900">Delete Material?</h3>
    <div class="mt-2">
        <p class="text-md text-gray-600">
            Are you sure you want to delete <strong>"{{ object.name }}"</strong>? This action cannot be undone.
        </p>
    </div>
    <div class="mt-6 flex justify-center space-x-4">
        <button
            type="button"
            class="px-5 py-2.5 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button
            hx-delete="{% url 'material_delete' object.pk %}"
            hx-swap="none"
            hx-target="body"
            class="px-5 py-2.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-150 ease-in-out">
            Delete
        </button>
    </div>
</div>
```

### 4.5 URLs (material_management/urls.py)

```python
from django.urls import path
from .views import (
    MaterialListView,
    MaterialTablePartialView,
    MaterialCreateView,
    MaterialUpdateView,
    MaterialDeleteView
)

urlpatterns = [
    # Main list view (full page load, or target for HX-Location)
    path('materials/', MaterialListView.as_view(), name='material_list'),
    
    # HTMX partials for table content and modal forms
    path('materials/table/', MaterialTablePartialView.as_view(), name='material_table'),
    path('materials/add/', MaterialCreateView.as_view(), name='material_add'),
    path('materials/<int:pk>/edit/', MaterialUpdateView.as_view(), name='material_edit'),
    path('materials/<int:pk>/delete/', MaterialDeleteView.as_view(), name='material_delete'),
]

```

### 4.6 Tests (material_management/tests.py)

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import Material

class MaterialModelTest(TestCase):
    """
    Unit tests for the Material model.
    Ensures model fields, string representation, and business logic methods work as expected.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a test Material instance for use across all model tests
        cls.material1 = Material.objects.create(
            name='Test Material A',
            quantity=100,
            description='A standard test material.',
        )
        cls.material2 = Material.objects.create(
            name='Low Stock Material',
            quantity=5,
            description='Material running low.',
        )
        cls.material3 = Material.objects.create(
            name='Another Material',
            quantity=0,
            description='Out of stock.',
        )
  
    def test_material_creation(self):
        """Verify Material instance is created correctly."""
        self.assertEqual(self.material1.name, 'Test Material A')
        self.assertEqual(self.material1.quantity, 100)
        self.assertEqual(self.material1.description, 'A standard test material.')
        self.assertIsNotNone(self.material1.last_updated)

    def test_name_label(self):
        """Check verbose name for 'name' field."""
        field_label = self.material1._meta.get_field('name').verbose_name
        self.assertEqual(field_label, 'Material Name')

    def test_quantity_label(self):
        """Check verbose name for 'quantity' field."""
        field_label = self.material1._meta.get_field('quantity').verbose_name
        self.assertEqual(field_label, 'Quantity')

    def test_str_method(self):
        """Verify __str__ method returns the material's name."""
        self.assertEqual(str(self.material1), 'Test Material A')

    def test_is_low_stock_method(self):
        """Test the is_low_stock business logic method."""
        self.assertFalse(self.material1.is_low_stock()) # 100 is not low stock (default threshold 10)
        self.assertTrue(self.material2.is_low_stock()) # 5 is low stock
        self.assertTrue(self.material3.is_low_stock(threshold=1)) # 0 is low stock even with threshold 1

    def test_update_quantity_method(self):
        """Test updating material quantity."""
        initial_quantity = self.material1.quantity
        self.material1.update_quantity(20)
        self.assertEqual(self.material1.quantity, initial_quantity + 20)
        
        initial_quantity_low = self.material2.quantity
        self.material2.update_quantity(-3)
        self.assertEqual(self.material2.quantity, initial_quantity_low - 3)

        with self.assertRaises(ValueError):
            self.material3.update_quantity(-5) # Should not allow negative quantity

class MaterialViewsTest(TestCase):
    """
    Integration tests for Material CRUD views.
    Covers HTTP responses, template usage, form submission, and HTMX interactions.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a single test material for views that need an existing object
        cls.material = Material.objects.create(
            name='Initial Test Material',
            quantity=50,
            description='For testing view operations.',
        )
    
    def setUp(self):
        # Set up a new client for each test method to avoid side effects
        self.client = Client()
    
    def test_list_view_get(self):
        """Test GET request to material list view."""
        response = self.client.get(reverse('material_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/material/list.html')
        self.assertIn('materials', response.context)
        self.assertQuerySetEqual(response.context['materials'], [self.material])

    def test_table_partial_view_get(self):
        """Test GET request to material table partial view (HTMX)."""
        response = self.client.get(reverse('material_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/material/_material_table.html')
        self.assertIn('materials', response.context)
        self.assertQuerySetEqual(response.context['materials'], [self.material])
        self.assertContains(response, 'id="materialTable"') # Check for table element

    def test_create_view_get(self):
        """Test GET request to material create view (modal form)."""
        response = self.client.get(reverse('material_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/material/_material_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Material')

    def test_create_view_post_success(self):
        """Test successful POST request to create a new material."""
        data = {
            'name': 'New Product X',
            'quantity': 200,
            'description': 'Newly added product.',
        }
        initial_count = Material.objects.count()
        response = self.client.post(reverse('material_add'), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HTMX success code
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMaterialList', response.headers['HX-Trigger'])
        self.assertEqual(Material.objects.count(), initial_count + 1)
        self.assertTrue(Material.objects.filter(name='New Product X').exists())

    def test_create_view_post_invalid(self):
        """Test POST request with invalid data to create a material."""
        data = {
            'name': '', # Invalid: required field
            'quantity': -10, # Invalid: negative quantity
            'description': 'Invalid attempt.',
        }
        initial_count = Material.objects.count()
        response = self.client.post(reverse('material_add'), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200) # HTMX returns form with errors
        self.assertTemplateUsed(response, 'material_management/material/_material_form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)
        self.assertIn('name', response.context['form'].errors)
        self.assertIn('quantity', response.context['form'].errors)
        self.assertEqual(Material.objects.count(), initial_count) # No new object created

    def test_update_view_get(self):
        """Test GET request to material update view (modal form)."""
        response = self.client.get(reverse('material_edit', args=[self.material.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/material/_material_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.material)
        self.assertContains(response, 'Edit Material')

    def test_update_view_post_success(self):
        """Test successful POST request to update an existing material."""
        data = {
            'name': 'Updated Material Name',
            'quantity': 75,
            'description': 'Description has been updated.',
        }
        response = self.client.post(reverse('material_edit', args=[self.material.pk]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMaterialList', response.headers['HX-Trigger'])
        
        self.material.refresh_from_db()
        self.assertEqual(self.material.name, 'Updated Material Name')
        self.assertEqual(self.material.quantity, 75)

    def test_update_view_post_invalid(self):
        """Test POST request with invalid data to update a material."""
        data = {
            'name': '',
            'quantity': -5,
        }
        original_name = self.material.name
        original_quantity = self.material.quantity
        response = self.client.post(reverse('material_edit', args=[self.material.pk]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/material/_material_form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)
        
        self.material.refresh_from_db() # Ensure data was NOT changed
        self.assertEqual(self.material.name, original_name)
        self.assertEqual(self.material.quantity, original_quantity)

    def test_delete_view_get(self):
        """Test GET request to material delete confirmation view (modal)."""
        response = self.client.get(reverse('material_delete', args=[self.material.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/material/_material_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.material)
        self.assertContains(response, 'Delete Material?')

    def test_delete_view_post_success(self):
        """Test successful DELETE request to delete a material."""
        # Create an object specifically for deletion to avoid impacting other tests
        material_to_delete = Material.objects.create(name='To Be Deleted', quantity=1)
        initial_count = Material.objects.count()
        
        response = self.client.delete(reverse('material_delete', args=[material_to_delete.pk]), HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMaterialList', response.headers['HX-Trigger'])
        self.assertEqual(Material.objects.count(), initial_count - 1)
        self.assertFalse(Material.objects.filter(pk=material_to_delete.pk).exists())

    def test_delete_view_post_non_existent(self):
        """Test DELETE request for a non-existent material."""
        response = self.client.delete(reverse('material_delete', args=[999]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 404) # Not Found
```

## Step 5: HTMX and Alpine.js Integration

The generated code incorporates HTMX and Alpine.js as follows:

*   **HTMX for Dynamic Content**:
    *   `list.html` uses `hx-get` on `materialTable-container` to load `_material_table.html` initially and on `refreshMaterialList` custom event.
    *   Buttons for "Add New", "Edit", and "Delete" use `hx-get` to fetch the respective `_material_form.html` or `_material_confirm_delete.html` into `#modalContent`.
    *   Form submissions (`_material_form.html`) use `hx-post` and `hx-swap="none"` with `hx-trigger="refreshMaterialList"` on successful submission to clear the modal (via HTTP 204) and trigger a table refresh.
    *   Delete submissions (`_material_confirm_delete.html`) use `hx-delete` and `hx-swap="none"` with `hx-trigger="refreshMaterialList"` to achieve the same.
    *   The `HtmxFormViewMixin` and `DeleteView.delete` method ensure the `HX-Trigger` header is sent with the `refreshMaterialList` event, allowing the table container to re-fetch its content.
    *   A loading spinner is shown while the table content loads.

*   **Alpine.js for UI State Management (Modals)**:
    *   The main modal div (`#modal`) in `list.html` uses `x-data` and `_=` (Hyperscript syntax, which works well with HTMX and Alpine) to control its `hidden` class based on user clicks (outside the modal content) or when HTMX injects new content.
    *   A global Alpine.js store `Alpine.store('modal')` is defined, though the current `_=` attributes directly control the modal's `hidden` class. The Alpine store provides a more structured way for complex modal logic if needed.
    *   JavaScript event listeners (`htmx:afterRequest` and `htmx:afterSwap`) are used to automatically close the modal on HTMX 204 responses (success for forms/delete) and open it when HTMX loads content into `modalContent`.

*   **DataTables for List Views**:
    *   `_material_table.html` contains the `<table>` element with `id="materialTable"`.
    *   A `<script>` block within `_material_table.html` initializes DataTables on this table ID. `$.fn.DataTable.isDataTable` and `destroy()` are used to correctly re-initialize DataTables when the partial is swapped by HTMX. This ensures sorting, filtering, and pagination work correctly after dynamic table updates.

*   **No Full Page Reloads**: All CRUD operations and list refreshes are handled dynamically using HTMX, providing a seamless user experience without full page reloads.

## Final Notes

This comprehensive plan provides a robust framework for migrating the "Dashboard" component to Django, focusing on a modern stack (Django 5.0+, HTMX, Alpine.js, Tailwind CSS) and emphasizing automation-driven development by providing complete, runnable code snippets for each architectural layer. The fat model/thin view approach ensures business logic is encapsulated, and the extensive use of partial templates and HTMX promotes a highly interactive, efficient user interface. The detailed tests ensure maintainability and correctness during the migration process.