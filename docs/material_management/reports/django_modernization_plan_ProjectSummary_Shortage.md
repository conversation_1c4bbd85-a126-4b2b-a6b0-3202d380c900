This modernization plan details the transition of your ASP.NET Project Summary Shortage report to a modern Django application. We'll leverage Django's powerful ORM, HTMX for dynamic interactions, Alpine.js for lightweight UI state, and DataTables for advanced data presentation. Our focus is on business benefits, automation, and a clean, maintainable architecture.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Business Benefits of Modernization:
Migrating this critical report to Django offers significant advantages:
1.  **Improved Performance & Scalability:** Django's efficient architecture and database interactions, combined with HTMX for partial page updates, will deliver a much faster user experience, even with large datasets.
2.  **Enhanced Maintainability & Development Speed:** Adopting a "fat model, thin view" pattern, along with clear separation of concerns, makes the codebase easier to understand, debug, and extend. Future development will be faster and less prone to errors.
3.  **Modern User Experience:** HTMX, Alpine.js, and DataTables provide a responsive and interactive frontend without the complexity of traditional JavaScript frameworks, leading to a smoother and more intuitive user interface.
4.  **Reduced Technical Debt:** Moving away from legacy ASP.NET reduces reliance on outdated technologies, making the application more secure and compatible with modern infrastructure.
5.  **Cost Efficiency:** A modern, open-source stack like Django reduces licensing costs and leverages a vast, active community for support and innovation.
6.  **Better Data Insights:** The structured Django ORM and data presentation with DataTables will enable clearer visualization and analysis of material shortage data.

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:** The ASP.NET code dynamically constructs a complex report (`dt2`) by joining and aggregating data from multiple underlying tables. This `dt2` is not a direct database table but a computed result. For the purpose of `managed = False` and adhering to the `[MODEL_NAME]` template, we will assume a database view (`project_summary_shortage_view`) exists that materializes this aggregated data. This view would essentially flatten the hierarchical structure of PRs, POs, and GINs into comma-separated strings within single columns, similar to how the `DataTable` was constructed in C#.

**Identified Database View (Assumed for `managed=False`):**
**Table Name:** `project_summary_shortage_view` (derived from multiple underlying tables)

**Inferred Columns (from `dt2` and nested `DataList` logic):**
*   `item_id` (PK, `INT`, from `tblDG_Item_Master.Id`)
*   `sn` (`INT`, Serial Number, calculated in C#)
*   `wono` (`VARCHAR`, Work Order Number)
*   `item_code` (`VARCHAR`)
*   `description` (`VARCHAR`, corresponds to `ManfDesc`)
*   `uom` (`VARCHAR`, Unit of Measure, corresponds to `UOMBasic`)
*   `bom_qty` (`FLOAT`, BOM Quantity)
*   `bom_date` (`VARCHAR`, aggregated BOM Dates, e.g., "DD/MM/YYYY DD/MM/YYYY")
*   `short_qty` (`FLOAT`, Shortage Quantity)
*   `wis_qty` (`FLOAT`, WIS Quantity)
*   `pr_info` (`VARCHAR`, aggregated PRs, e.g., "PRNo1|Qty1,PRNo2|Qty2" or a more complex JSON string to retain structure)
*   `po_info` (`VARCHAR`, aggregated POs, e.g., "PONo1|Qty1|Rate1|Amt1|Date1|DelDate1|Supplier1,..." or JSON)
*   `gin_info` (`VARCHAR`, aggregated GINs, e.g., "GINNo1|Qty1,GINNo2|Qty2" or JSON)

**Note:** For complex nested data like PRs, POs, and GINs, storing them as comma-separated strings in the view is a legacy pattern. In Django, we'll parse these strings into structured Python objects/lists within the model's properties for easier templating, showcasing the "fat model" approach.

### Step 2: Identify Backend Functionality

**Core Functionality:** This ASP.NET page primarily serves as a **Read (Report) operation**.
*   **Read:** Data is fetched from multiple interconnected tables (`tblDG_Item_Master`, `tblDG_BOM_Master`, `tblMM_PR_Master`, `tblMM_PR_Details`, `tblMM_PO_Master`, `tblMM_PO_Details`, `tblMM_Supplier_master`, `tblInv_Inward_Master`, `tblInv_Inward_Details`, `Unit_Master`) based on session and query string parameters (`WorkOrderId`, `compid`, `finyear`, `SwitchTo`, `Trans`). The data is then presented in a hierarchical grid.
*   **Export:** The `btnExport_Click` function allows exporting the current report data to an Excel file.
*   **Navigation:** `DataList3_ItemCommand` (specifically `NavTo`) facilitates navigation to related Purchase Order details pages (`PO_PR_View_Print_Details.aspx` or `PO_SPR_View_Print_Details.aspx`). This will be handled as simple URL redirects in Django.

**No direct Create, Update, or Delete (CRUD) operations for the Project Summary Shortage entries themselves are performed on this page.**

### Step 3: Infer UI Components

**Key ASP.NET Controls and Django Equivalents:**
*   `asp:Label lblWo`: Will be replaced by a Django template variable displaying the `WONo`.
*   `asp:DataList GridView3` (and nested `DataList2`, `DataList3`, `DataList4`): Will be replaced by a single HTML `<table>` structured with Django template loops to represent the main rows and nested `<table>`s for PR, PO, and GIN details. DataTables will be applied to the outermost table for client-side functionality.
*   `asp:LinkButton lblPONo`: Will become an HTML `<a>` tag or a `button` that triggers an HTMX request for navigation or a modal.
*   `asp:Button btnExport`: Will be an HTML `<button>` triggering a Django view for file export.
*   `asp:Button btnCancel`: An HTML `<button>` that redirects to a previous page.
*   `asp:UpdatePanel`: Replaced entirely by HTMX, allowing for dynamic updates without full page reloads.
*   **CSS/JS:** `yui-datatable.css` and `styles.css` indicate custom styling and data table functionality. This will be replaced by Tailwind CSS and jQuery DataTables. The client-side JavaScript (`$(document).ready`, `PopUpMsg.js`, `loadingNotifier.js`) will be replaced by HTMX and Alpine.js where dynamic behavior is needed.

### Step 4: Generate Django Code

We will create a new Django app, e.g., `material_management`.

#### 4.1 Models (`material_management/models.py`)

This model represents a single row in the Project Summary Shortage Report. It assumes a database view `project_summary_shortage_view` is pre-defined in the database, containing all the aggregated fields. The methods `get_pr_details`, `get_po_details`, `get_gin_details` demonstrate the "fat model" principle by parsing the aggregated string data into structured lists.

```python
from django.db import models
from datetime import datetime
import json

class ProjectSummaryShortage(models.Model):
    """
    Represents a single entry in the Project Summary Shortage report.
    Assumes a database view 'project_summary_shortage_view' exists
    that materializes the aggregated data from various source tables.
    """
    item_id = models.IntegerField(db_column='item_id', primary_key=True)
    sn = models.IntegerField(db_column='sn', blank=True, null=True) # Serial Number, might be calculated in view
    wono = models.CharField(db_column='wono', max_length=255, blank=True, null=True)
    item_code = models.CharField(db_column='item_code', max_length=255, blank=True, null=True)
    description = models.CharField(db_column='description', max_length=500, blank=True, null=True)
    uom = models.CharField(db_column='uom', max_length=50, blank=True, null=True)
    bom_qty = models.FloatField(db_column='bom_qty', blank=True, null=True)
    bom_date = models.CharField(db_column='bom_date', max_length=1000, blank=True, null=True) # Aggregated, comma-separated dates
    short_qty = models.FloatField(db_column='short_qty', blank=True, null=True)
    wis_qty = models.FloatField(db_column='wis_qty', blank=True, null=True)

    # Aggregated fields as JSON strings or custom delimited strings
    # For simplicity, we'll assume a pipe-delimited format for internal parsing if DB view cannot provide JSON
    # It's better to store these as JSON in the view if possible.
    pr_info = models.TextField(db_column='pr_info', blank=True, null=True) # e.g., "PRNo1|Qty1,PRNo2|Qty2"
    po_info = models.TextField(db_column='po_info', blank=True, null=True) # e.g., "PONo1|Qty1|Rate1|Amt1|Date1|DelDate1|Supplier1,..."
    gin_info = models.TextField(db_column='gin_info', blank=True, null=True) # e.g., "GINNo1|Qty1,GINNo2|Qty2"

    class Meta:
        managed = False  # Important: Django won't create/manage this table
        db_table = 'project_summary_shortage_view' # Assumed database view name
        verbose_name = 'Project Summary Shortage'
        verbose_name_plural = 'Project Summary Shortages'

    def __str__(self):
        return f"{self.item_code} ({self.wono}) - Shortage: {self.short_qty}"

    def parse_date_dmy(self, date_str):
        """Helper to parse and format date strings if needed."""
        try:
            # Assuming input like "MM/DD/YYYY" or "YYYY-MM-DD" and converting to "DD/MM/YYYY"
            # The original ASP.NET had fun.FromDateDMY, assuming it takes 'YYYY-MM-DD' and outputs 'DD/MM/YYYY'
            if date_str and date_str != '-':
                return datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S').strftime('%d/%m/%Y')
        except ValueError:
            pass # Fallback to original string if parsing fails
        return date_str


    def get_pr_details(self):
        """
        Parses the pr_info string into a list of PR dictionaries.
        Assumes pr_info is like: "PRNo|PRQty|PRId,PRNo|PRQty|PRId,..."
        """
        if not self.pr_info:
            return [{'pr_no': '', 'pr_qty': 0, 'pr_id': ''}] # Mimic blank data list
        
        pr_list = []
        for pr_entry in self.pr_info.split(';'): # Use semicolon to split PR entries
            if pr_entry.strip():
                parts = pr_entry.strip().split('|')
                if len(parts) >= 3:
                    pr_list.append({
                        'pr_no': parts[0],
                        'pr_qty': float(parts[1]) if parts[1].replace('.', '', 1).isdigit() else 0,
                        'pr_id': parts[2]
                    })
        return pr_list or [{'pr_no': '', 'pr_qty': 0, 'pr_id': ''}]

    def get_po_details(self, pr_id):
        """
        Parses the po_info string for a specific PR into a list of PO dictionaries.
        This requires `po_info` to contain PR-specific data or a more complex structure.
        For simplicity, assuming `po_info` is a JSON string of a list of POs,
        each with a `pr_id` field to filter by.
        Example JSON for po_info:
        [
            {"pr_id": "PR1_ID", "po_no": "PO1", "po_qty": 10, ...},
            {"pr_id": "PR1_ID", "po_no": "PO2", "po_qty": 5, ...},
            {"pr_id": "PR2_ID", "po_no": "PO3", "po_qty": 20, ...}
        ]
        """
        if not self.po_info:
            return []
        try:
            all_pos = json.loads(self.po_info)
            # Filter POs by the current PR_ID
            filtered_pos = [
                po for po in all_pos if str(po.get('pr_id')) == str(pr_id)
            ]
            
            # Format dates and amounts
            for po in filtered_pos:
                po['po_date'] = self.parse_date_dmy(po.get('po_date'))
                po['po_del_date'] = self.parse_date_dmy(po.get('po_del_date'))
                po['po_rate'] = f"{float(po.get('po_rate', 0)):.2f}"
                po['po_amount'] = f"{float(po.get('po_amount', 0)):.2f}"
            
            return filtered_pos
        except json.JSONDecodeError:
            # Fallback for malformed JSON, maybe try pipe-delimited parsing as last resort
            return []
        return []

    def get_gin_details(self, po_id):
        """
        Parses the gin_info string for a specific PO into a list of GIN dictionaries.
        Similar to get_po_details, assumes gin_info is a JSON string.
        Example JSON for gin_info:
        [
            {"po_id": "PO1_ID", "gin_no": "GIN1", "gin_qty": 10},
            {"po_id": "PO1_ID", "gin_no": "GIN2", "gin_qty": 5},
            {"po_id": "PO2_ID", "gin_no": "GIN3", "gin_qty": 20}
        ]
        """
        if not self.gin_info:
            return []
        try:
            all_gins = json.loads(self.gin_info)
            # Filter GINs by the current PO_ID
            filtered_gins = [
                gin for gin in all_gins if str(gin.get('po_id')) == str(po_id)
            ]
            return filtered_gins
        except json.JSONDecodeError:
            return []
        return []

    @classmethod
    def get_report_data(cls, wono_str, switch_to, trans, comp_id, fin_year_id):
        """
        Mimics the FillGrid_Creditors logic to filter the report data.
        This method assumes the underlying `project_summary_shortage_view`
        provides columns `wono`, `item_id`, `short_qty`.
        The actual complex filtering logic (like `tblDG_Item_Master.CId is null`)
        would need to be part of the SQL view definition itself.
        """
        # The `shree` in C# was a comma-separated list of WONos.
        # This function should only filter by WONo, and apply SwitchTo/Trans logic.

        # Initial queryset from the assumed view
        queryset = cls.objects.all()

        # Apply WONo filter (from session["WorkOrderId"])
        if wono_str:
            wonos = [w.strip() for w in wono_str.split(',') if w.strip()]
            queryset = queryset.filter(wono__in=wonos)
        
        # Apply SwitchTo and Trans filters
        # The complex C# logic for 'SwitchTo' (based on CId null/not null and CId not in PId)
        # and 'Trans' (ShortageQty > 0, == 0, or all) must be built into the SQL View logic.
        # Here we just assume the view already reflects the combined filters.
        # For demonstration, we'll only apply `Trans` logic if view directly supports it.
        
        # NOTE: This is a simplification. The intricate SQL queries in C#
        # (e.g., `tblDG_Item_Master.CId is not null` or `CId is null`,
        # `CId not in (Select PId from tblDG_BOM_Master...)`)
        # must be part of the `project_summary_shortage_view` SQL definition.
        # The `short_qty` calculation `(liQty - TotWisQty - totGinQty + totGqnQty)`
        # must also be part of the view.

        # For Trans filter (ShortageQty filter)
        if trans == "1": # ShortageQty > 0
            queryset = queryset.filter(short_qty__gt=0)
        elif trans == "2": # ShortageQty == 0
            queryset = queryset.filter(short_qty=0)
        # '3' means show all, no additional filter needed

        # Sorting based on ItemCode as in C#
        queryset = queryset.order_by('item_code')

        # Add serial number dynamically, as it was in C#
        for i, obj in enumerate(queryset):
            obj.sn = i + 1

        return list(queryset)

    def navigate_to_po_details(self, request, po_id, po_no, supplier_name):
        """
        Mimics the DataList3_ItemCommand for navigation.
        This would typically be handled in the view or a helper,
        but for the "fat model" demonstration, we put the URL construction here.
        """
        # In a real Django app, this would be a URL reverse, not direct string manipulation.
        # This method would return a URL, and the view would redirect.
        
        # Assuming you have an "external" app for PO details, or a view in this app.
        # You'd need to correctly map supplier_name to a code if needed by the target system.
        
        # This is a simplification for a fat model concept; actual navigation
        # logic might involve a helper or a mixin.
        
        # The C# fun.getCode(supplier_name) maps to a supplier ID
        # For Django, we assume supplier_name is sufficient for URL, or
        # we'd need a Supplier model and lookup logic.
        # For now, let's assume supplier_name is `Supplier Name [SupplierId]` from ASP.NET.
        
        # Extract supplier code from 'Supplier Name [SupplierId]'
        import re
        match = re.search(r'\[(\w+)\]', supplier_name)
        sup_code = match.group(1) if match else '' # Default to empty string if not found

        # Stub logic for PRSPRFlag and AmendmentNo, which would come from a real POMaster model
        # For this example, we hardcode based on the C# logic for demonstration
        prspr_flag = '0' # Default to 0 based on C#
        amendment_no = '0' # Default based on C#

        # Simplified random alphanumeric key generation
        import secrets
        random_key = secrets.token_hex(8)

        # Base URL for PO/SPR view
        base_url = reverse('material_management:po_details_view') # Placeholder name for PO details view

        # Construct parameters matching the C# redirect
        params = {
            'mid': po_id, # C# used MId, not POId for the main PO ID
            'pono': po_no,
            'Code': sup_code,
            'AmdNo': amendment_no,
            'Swto': request.GET.get('SwitchTo', '1'), # From original request
            'Key': random_key,
            'Trans': request.GET.get('Trans', '3'), # From original request
            'ModId': '6',
            'SubModId': '35',
            'parentpage': reverse('material_management:projectsummaryshortage_list'),
        }

        # The C# `PRSPRFlag` determined which page to redirect to.
        # This decision should ideally be based on a real PO model.
        if prspr_flag == "0":
            # Redirect to PO_PR_View_Print_Details (standard PO)
            return f"{base_url}?{models.URLField().get_prep_value(params)}" # Need to manually encode
        else:
            # Redirect to PO_SPR_View_Print_Details (special PO)
            return f"{reverse('material_management:spr_details_view')}?{models.URLField().get_prep_value(params)}" # Placeholder for SPR details

```

#### 4.2 Forms (`material_management/forms.py`)

No forms are required for this report view, as it's purely for display and export. User inputs (WONo, SwitchTo, Trans) are handled via URL parameters or session.

#### 4.3 Views (`material_management/views.py`)

This section handles displaying the report and exporting data.
The `ProjectSummaryShortageListView` retrieves the data.
The `ProjectSummaryShortageTablePartialView` renders just the table for HTMX updates.
The `ProjectSummaryShortageExportView` handles the Excel export.

```python
from django.views.generic import ListView, View
from django.http import HttpResponse, JsonResponse
from django.urls import reverse_lazy
from django.contrib import messages
from django.template.loader import render_to_string
from django.shortcuts import redirect
import io
import pandas as pd # For Excel export

from .models import ProjectSummaryShortage # Our assumed model for the view

class ProjectSummaryShortageListView(ListView):
    """
    Displays the main Project Summary Shortage report page.
    This view remains thin, delegating data retrieval to the model/manager.
    """
    model = ProjectSummaryShortage
    template_name = 'material_management/projectsummaryshortage/list.html'
    context_object_name = 'project_summary_shortages'

    def get_queryset(self):
        """
        Retrieves the report data based on session and query parameters.
        Mimics the `Page_Load` and `FillGrid_Creditors` logic.
        """
        wono_from_session = self.request.session.get("WorkOrderId", "")
        # For demonstration, assuming compid and finyear are available in session
        comp_id = self.request.session.get("compid", 1) 
        fin_year_id = self.request.session.get("finyear", 1)

        switch_to = self.request.GET.get('SwitchTo', '1') # Default like C#
        trans = self.request.GET.get('Trans', '3') # Default like C#

        # Delegate complex data retrieval and filtering to the model's class method
        queryset = self.model.get_report_data(
            wono_from_session, switch_to, trans, comp_id, fin_year_id
        )
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['wono'] = self.request.session.get("WorkOrderId", "N/A")
        return context

class ProjectSummaryShortageTablePartialView(ProjectSummaryShortageListView):
    """
    Renders only the table content for HTMX requests,
    inheriting the data retrieval logic from ProjectSummaryShortageListView.
    """
    template_name = 'material_management/projectsummaryshortage/_shortage_table.html'

    def render_to_response(self, context, **response_kwargs):
        # HTMX requests only need the partial HTML
        if self.request.headers.get('HX-Request'):
            return super().render_to_response(context, **response_kwargs)
        # For non-HTMX requests (e.g., direct navigation), redirect to main list view
        return redirect(reverse_lazy('material_management:projectsummaryshortage_list'))

class ProjectSummaryShortageExportView(View):
    """
    Handles exporting the report data to an Excel file.
    Mimics the `btnExport_Click` functionality.
    """
    def get(self, request, *args, **kwargs):
        wono_from_session = request.session.get("WorkOrderId", "")
        comp_id = request.session.get("compid", 1) 
        fin_year_id = request.session.get("finyear", 1)

        switch_to = request.GET.get('SwitchTo', '1')
        trans = request.GET.get('Trans', '3')

        report_data = ProjectSummaryShortage.get_report_data(
            wono_from_session, switch_to, trans, comp_id, fin_year_id
        )

        if not report_data:
            messages.warning(request, "No records to export.")
            # If HTMX, return a 204 with a message trigger
            if request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204,
                    headers={'HX-Trigger': 'showMessage'} # Custom trigger for Alpine.js to show message
                )
            return redirect(reverse_lazy('material_management:projectsummaryshortage_list'))

        # Prepare data for DataFrame
        # Flatten the complex structure for Excel export
        flattened_data = []
        for main_item in report_data:
            main_item_dict = {
                'SN': main_item.sn,
                'WONo': main_item.wono,
                'Item Code': main_item.item_code,
                'Description': main_item.description,
                'UOM': main_item.uom,
                'BOM QTY': main_item.bom_qty,
                'BOM DT.': main_item.bom_date,
                'SHORT.QTY': main_item.short_qty,
                'WIS QTY': main_item.wis_qty,
                'PR NO': '', 'PR QTY': '', # Placeholder for aggregated string
                'PO NO': '', 'PO QTY': '', 'PO DT.': '', 'SCH.DT.': '', 'PO RATE': '', 'AMOUNT': '', 'SUPPLIER': '', # Placeholder for aggregated string
                'GIN NO': '', 'GIN QTY': '' # Placeholder for aggregated string
            }

            # Aggregate PR, PO, GIN details into comma-separated strings for Excel columns
            pr_nos = []
            pr_qtys = []
            po_details_list = []
            gin_details_list = []

            for pr_entry in main_item.get_pr_details():
                if pr_entry['pr_no']:
                    pr_nos.append(pr_entry['pr_no'])
                    pr_qtys.append(str(pr_entry['pr_qty']))

                # Fetch POs for this PR
                pos_for_pr = main_item.get_po_details(pr_entry['pr_id'])
                for po_entry in pos_for_pr:
                    po_details_list.append(
                        f"{po_entry.get('po_no', '')}|"
                        f"{po_entry.get('po_qty', 0):.3f}|"
                        f"{po_entry.get('po_date', '')}|"
                        f"{po_entry.get('po_del_date', '')}|"
                        f"{po_entry.get('po_rate', 0):.2f}|"
                        f"{po_entry.get('po_amount', 0):.2f}|"
                        f"{po_entry.get('supplier', '')}"
                    )
                    # Fetch GINs for this PO
                    gins_for_po = main_item.get_gin_details(po_entry.get('po_id'))
                    for gin_entry in gins_for_po:
                        gin_details_list.append(
                            f"{gin_entry.get('gin_no', '')}|"
                            f"{gin_entry.get('gin_qty', 0):.3f}"
                        )

            main_item_dict['PR NO'] = ','.join(pr_nos)
            main_item_dict['PR QTY'] = ','.join(pr_qtys)
            main_item_dict['PO Details'] = ' || '.join(po_details_list) # Use a different separator for PO
            main_item_dict['GIN Details'] = ' || '.join(gin_details_list) # Use a different separator for GIN
            
            flattened_data.append(main_item_dict)

        df = pd.DataFrame(flattened_data)

        output = io.BytesIO()
        writer = pd.ExcelWriter(output, engine='xlsxwriter')
        df.to_excel(writer, sheet_name='Project Summary Shortage', index=False)
        writer.close()
        output.seek(0)

        filename = f"ProjectSummaryShortage_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        return response

class PONavigationView(View):
    """
    Handles the navigation to PO/SPR details.
    This would typically delegate to a dedicated PO/SPR app or module.
    """
    def get(self, request, *args, **kwargs):
        # Extract parameters from query string, mimicking C#
        mid = request.GET.get('mid')
        pono = request.GET.get('pono')
        code = request.GET.get('Code') # Supplier Code
        amd_no = request.GET.get('AmdNo')
        swto = request.GET.get('Swto')
        key = request.GET.get('Key')
        trans = request.GET.get('Trans')
        mod_id = request.GET.get('ModId')
        sub_mod_id = request.GET.get('SubModId')
        parent_page = request.GET.get('parentpage')

        # In a real system, you'd fetch the PRSPRFlag from the PO model based on mid/pono/code
        # For demonstration, we assume it's '0' as in the C# example's common path
        prspr_flag = '0' # Placeholder

        if prspr_flag == '0':
            # Redirect to standard PO details page
            # Example: `/po_details/view/?mid=...`
            return redirect(reverse_lazy('material_management:po_details_view') + f'?mid={mid}&pono={pono}&Code={code}&AmdNo={amd_no}&Swto={swto}&Key={key}&Trans={trans}&ModId={mod_id}&SubModId={sub_mod_id}&parentpage={parent_page}')
        else:
            # Redirect to special PO details page (SPR)
            # Example: `/spr_details/view/?mid=...`
            return redirect(reverse_lazy('material_management:spr_details_view') + f'?mid={mid}&pono={pono}&Code={code}&AmdNo={amd_no}&Swto={swto}&Key={key}&Trans={trans}&ModId={mod_id}&SubModId={sub_mod_id}&parentpage={parent_page}')

```

#### 4.4 Templates

We'll define two templates: one for the main page (`list.html`) and one partial for the DataTables content (`_shortage_table.html`).

**`material_management/projectsummaryshortage/list.html`**
This template sets up the overall page structure, includes the button for export, and a container for the HTMX-loaded table. It also includes the modal structure for any potential future use (though not used for direct CRUD on this report).

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ showMessage: false, message: '' }" 
     @showMessage.window="message = $event.detail.message; showMessage = true; setTimeout(() => showMessage = false, 3000)">

    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Project Summary Shortages</h2>
    </div>

    <table class="mb-4">
        <tr>
            <td width="100%">
                <b>WONo : <span id="lblWo">{{ wono }}</span></b>
            </td>
        </tr>
    </table>
    
    <div id="shortageTable-container"
         hx-trigger="load, refreshProjectSummaryShortageList from:body"
         hx-get="{% url 'material_management:projectsummaryshortage_table' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Report Data...</p>
        </div>
    </div>
    
    <div class="mt-6 flex justify-center space-x-4">
        <button 
            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded" 
            onclick="window.location.href='{% url 'material_management:projectsummaryshortage_export' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}'">
            Export To Excel
        </button>
        <button 
            class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded" 
            onclick="window.location.href='{% url 'material_management:materialforecasting_list' %}'"> {# Assuming a URL for MaterialForecasting.aspx exists #}
            Cancel
        </button>
    </div>

    <!-- Global Message Display (for export feedback, etc.) -->
    <div x-show="showMessage" x-transition.opacity.duration.300ms class="fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded shadow-lg z-50">
        <p x-text="message"></p>
    </div>

</div>
{% endblock %}

{% block extra_js %}
<script>
    // No additional Alpine.js components specific to this page are needed yet,
    // beyond the global message display defined in the parent x-data scope.
    // DataTables initialization is within the partial template.
</script>
{% endblock %}
```

**`material_management/projectsummaryshortage/_shortage_table.html`**
This partial template contains the actual DataTables structure, including the nested tables for PR, PO, and GIN details.

```html
<div class="overflow-x-auto relative shadow-md sm:rounded-lg">
    <table id="projectSummaryShortageTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">WONo</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">ITEM CODE</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">DESCRIPTION</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">BOM QTY</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">BOM DT.</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">SHORT.QTY</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">WIS QTY</th>
                
                <!-- Combined headers for PR, PO, GIN sections -->
                <th colspan="3" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">PR Details</th>
                <th colspan="7" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">PO Details</th>
                <th colspan="2" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">GIN Details</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in project_summary_shortages %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 text-right text-sm">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left text-sm">{{ obj.wono }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center text-sm">{{ obj.item_code }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left text-sm whitespace-normal">{{ obj.description }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center text-sm">{{ obj.uom }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right text-sm">{{ obj.bom_qty|floatformat:"3" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center text-sm">{{ obj.bom_date }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right text-sm">{{ obj.short_qty|floatformat:"3" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right text-sm">{{ obj.wis_qty|floatformat:"3" }}</td>
                
                {# Nested PR, PO, GIN DataLists #}
                <td class="py-2 px-4 border-b border-gray-200" colspan="12">
                    <table class="min-w-full bg-white text-xs">
                        <thead>
                            <tr class="bg-gray-50 text-gray-600">
                                <th class="py-1 px-2 border-b border-gray-200 text-center" width="5%">PR No</th>
                                <th class="py-1 px-2 border-b border-gray-200 text-right" width="5%">PR Qty</th>
                                <th class="py-1 px-2 border-b border-gray-200 text-center" width="7%">PO No</th>
                                <th class="py-1 px-2 border-b border-gray-200 text-right" width="7%">PO Qty</th>
                                <th class="py-1 px-2 border-b border-gray-200 text-center" width="7%">PO Dt.</th>
                                <th class="py-1 px-2 border-b border-gray-200 text-center" width="7%">SCH.Dt.</th>
                                <th class="py-1 px-2 border-b border-gray-200 text-right" width="7%">PO Rate</th>
                                <th class="py-1 px-2 border-b border-gray-200 text-right" width="7%">Amount</th>
                                <th class="py-1 px-2 border-b border-gray-200 text-left" width="10%">Supplier</th>
                                <th class="py-1 px-2 border-b border-gray-200 text-center" width="5%">GIN No</th>
                                <th class="py-1 px-2 border-b border-gray-200 text-right" width="5%">GIN Qty</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% with pr_details=obj.get_pr_details %}
                            {% for pr_entry in pr_details %}
                            <tr>
                                <td class="py-1 px-2 border-b border-gray-200 text-center">{{ pr_entry.pr_no }}</td>
                                <td class="py-1 px-2 border-b border-gray-200 text-right">{{ pr_entry.pr_qty|floatformat:"3" }}</td>
                                
                                {% with po_details=obj.get_po_details(pr_entry.pr_id) %}
                                {% if po_details %}
                                <td colspan="9">
                                    <table class="min-w-full bg-white text-xs">
                                        <tbody>
                                            {% for po_entry in po_details %}
                                            <tr>
                                                <td class="py-1 px-2 border-b border-gray-200 text-center" width="7%">
                                                    <a href="{% url 'material_management:po_navigation' %}{% if request.GET %}?{{ request.GET.urlencode }}&{% else %}?{% endif %}mid={{ po_entry.po_id }}&pono={{ po_entry.po_no }}&Code={{ po_entry.supplier_code }}&AmdNo={{ po_entry.amendment_no|default:'0' }}&parentpage={{ request.path|urlencode }}" class="text-blue-600 hover:underline">
                                                        {{ po_entry.po_no }}
                                                    </a>
                                                </td>
                                                <td class="py-1 px-2 border-b border-gray-200 text-right" width="7%">{{ po_entry.po_qty|floatformat:"3" }}</td>
                                                <td class="py-1 px-2 border-b border-gray-200 text-center text-green-700 font-bold" width="7%">{{ po_entry.po_date }}</td>
                                                <td class="py-1 px-2 border-b border-gray-200 text-center text-red-700 font-bold" width="7%">{{ po_entry.po_del_date }}</td>
                                                <td class="py-1 px-2 border-b border-gray-200 text-right" width="7%">{{ po_entry.po_rate }}</td>
                                                <td class="py-1 px-2 border-b border-gray-200 text-right" width="7%">{{ po_entry.po_amount }}</td>
                                                <td class="py-1 px-2 border-b border-gray-200 text-left" width="10%">{{ po_entry.supplier }}</td>
                                                
                                                {% with gin_details=obj.get_gin_details(po_entry.po_id) %}
                                                {% if gin_details %}
                                                <td colspan="2">
                                                    <table class="min-w-full bg-white text-xs">
                                                        <tbody>
                                                            {% for gin_entry in gin_details %}
                                                            <tr>
                                                                <td class="py-1 px-2 border-b border-gray-200 text-center" width="50%">{{ gin_entry.gin_no }}</td>
                                                                <td class="py-1 px-2 border-b border-gray-200 text-right" width="50%">{{ gin_entry.gin_qty|floatformat:"3" }}</td>
                                                            </tr>
                                                            {% endfor %}
                                                        </tbody>
                                                    </table>
                                                </td>
                                                {% else %}
                                                <td class="py-1 px-2 border-b border-gray-200 text-center" width="5%"></td>
                                                <td class="py-1 px-2 border-b border-gray-200 text-right" width="5%"></td>
                                                {% endif %}
                                                {% endwith %}
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </td>
                                {% else %}
                                <td class="py-1 px-2 border-b border-gray-200 text-center" width="7%"></td>
                                <td class="py-1 px-2 border-b border-gray-200 text-right" width="7%"></td>
                                <td class="py-1 px-2 border-b border-gray-200 text-center" width="7%"></td>
                                <td class="py-1 px-2 border-b border-gray-200 text-center" width="7%"></td>
                                <td class="py-1 px-2 border-b border-gray-200 text-right" width="7%"></td>
                                <td class="py-1 px-2 border-b border-gray-200 text-right" width="7%"></td>
                                <td class="py-1 px-2 border-b border-gray-200 text-left" width="10%"></td>
                                <td class="py-1 px-2 border-b border-gray-200 text-center" width="5%"></td>
                                <td class="py-1 px-2 border-b border-gray-200 text-right" width="5%"></td>
                                {% endif %}
                                {% endwith %}
                            </tr>
                            {% endfor %}
                            {% else %}
                            <tr>
                                <td colspan="20" class="py-4 px-4 text-center text-gray-500">No records found.</td>
                            </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
$(document).ready(function() {
    $('#projectSummaryShortageTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "paging": true,
        "searching": true,
        "info": true,
        "ordering": true,
        "columnDefs": [
            { "orderable": false, "targets": [8] } // Assuming SN is auto-generated and not orderable, or other dynamic columns
        ]
    });
});
</script>
```

#### 4.5 URLs (`material_management/urls.py`)

This file defines the URL patterns for accessing the report, its partial table, and export functionality.

```python
from django.urls import path
from .views import (
    ProjectSummaryShortageListView,
    ProjectSummaryShortageTablePartialView,
    ProjectSummaryShortageExportView,
    PONavigationView,
)

app_name = 'material_management' # Defines the application namespace

urlpatterns = [
    path('projectsummaryshortage/', ProjectSummaryShortageListView.as_view(), name='projectsummaryshortage_list'),
    path('projectsummaryshortage/table/', ProjectSummaryShortageTablePartialView.as_view(), name='projectsummaryshortage_table'),
    path('projectsummaryshortage/export/', ProjectSummaryShortageExportView.as_view(), name='projectsummaryshortage_export'),
    path('projectsummaryshortage/navigate-po/', PONavigationView.as_view(), name='po_navigation'),

    # Placeholder URLs for external PO/SPR details views, assume they are in other apps
    path('po_details/view/', PONavigationView.as_view(), name='po_details_view'), # Redirects to actual view
    path('spr_details/view/', PONavigationView.as_view(), name='spr_details_view'), # Redirects to actual view

    # Placeholder for MaterialForecasting.aspx redirect
    path('materialforecasting/', View.as_view(template_name='material_management/material_forecasting_placeholder.html'), name='materialforecasting_list'),

]
```

#### 4.6 Tests (`material_management/tests.py`)

Comprehensive tests for the model's data parsing logic and the views' functionality, including HTMX interactions and export.

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from .models import ProjectSummaryShortage
import io
import pandas as pd

class ProjectSummaryShortageModelTest(TestCase):
    """
    Tests for the ProjectSummaryShortage model, focusing on data parsing methods.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a mock instance of ProjectSummaryShortage to test its methods
        # Since managed=False, we mock the database retrieval part.
        # Here we'll create a dummy object that has the necessary attributes
        cls.mock_item = ProjectSummaryShortage(
            item_id=1,
            sn=1,
            wono='WO-001',
            item_code='ITEM-001',
            description='Test Item Description',
            uom='PCS',
            bom_qty=100.0,
            bom_date='2023-01-01 00:00:00',
            short_qty=10.0,
            wis_qty=90.0,
            pr_info='PR001|50|PR1_ID;PR002|40|PR2_ID',
            po_info=json.dumps([
                {"pr_id": "PR1_ID", "po_id": "PO1_ID", "po_no": "PO001", "po_qty": 50, "po_date": "2023-01-05 00:00:00", "po_del_date": "2023-01-10 00:00:00", "po_rate": 10.0, "po_amount": 500.0, "supplier": "Supplier A", "supplier_code": "SUPA"},
                {"pr_id": "PR2_ID", "po_id": "PO2_ID", "po_no": "PO002", "po_qty": 40, "po_date": "2023-01-06 00:00:00", "po_del_date": "2023-01-11 00:00:00", "po_rate": 12.0, "po_amount": 480.0, "supplier": "Supplier B", "supplier_code": "SUPB"}
            ]),
            gin_info=json.dumps([
                {"po_id": "PO1_ID", "gin_no": "GIN001", "gin_qty": 50},
                {"po_id": "PO2_ID", "gin_no": "GIN002", "gin_qty": 40}
            ])
        )

    def test_pr_details_parsing(self):
        pr_details = self.mock_item.get_pr_details()
        self.assertEqual(len(pr_details), 2)
        self.assertEqual(pr_details[0]['pr_no'], 'PR001')
        self.assertEqual(pr_details[0]['pr_qty'], 50.0)
        self.assertEqual(pr_details[0]['pr_id'], 'PR1_ID')
        self.assertEqual(pr_details[1]['pr_no'], 'PR002')
        self.assertEqual(pr_details[1]['pr_qty'], 40.0)
        self.assertEqual(pr_details[1]['pr_id'], 'PR2_ID')

    def test_po_details_parsing(self):
        po_details_pr1 = self.mock_item.get_po_details('PR1_ID')
        self.assertEqual(len(po_details_pr1), 1)
        self.assertEqual(po_details_pr1[0]['po_no'], 'PO001')
        self.assertEqual(po_details_pr1[0]['po_qty'], 50)
        self.assertEqual(po_details_pr1[0]['po_date'], '05/01/2023') # Formatted date
        self.assertEqual(po_details_pr1[0]['po_amount'], '500.00')

        po_details_pr2 = self.mock_item.get_po_details('PR2_ID')
        self.assertEqual(len(po_details_pr2), 1)
        self.assertEqual(po_details_pr2[0]['po_no'], 'PO002')
    
    def test_gin_details_parsing(self):
        gin_details_po1 = self.mock_item.get_gin_details('PO1_ID')
        self.assertEqual(len(gin_details_po1), 1)
        self.assertEqual(gin_details_po1[0]['gin_no'], 'GIN001')
        self.assertEqual(gin_details_po1[0]['gin_qty'], 50)

    def test_get_report_data_filtering(self):
        # Mock the objects.all() and filter/order_by calls for ProjectSummaryShortage
        # Since it's managed=False and directly representing a view,
        # we'll patch the actual ORM call for `ProjectSummaryShortage.objects.all()`
        # to return our mock objects.

        mock_queryset = [
            ProjectSummaryShortage(item_id=1, wono='WO-001', short_qty=10, item_code='ITEM-001'),
            ProjectSummaryShortage(item_id=2, wono='WO-001', short_qty=0, item_code='ITEM-002'),
            ProjectSummaryShortage(item_id=3, wono='WO-002', short_qty=5, item_code='ITEM-003'),
        ]

        with patch('material_management.models.ProjectSummaryShortage.objects.all') as mock_all:
            # We need to simulate filtering
            mock_filter_result = MagicMock()
            mock_filter_result.filter.return_value = mock_filter_result # Chainable filter
            mock_filter_result.order_by.return_value = mock_queryset # Final result after order_by
            mock_all.return_value = mock_filter_result

            # Test trans=1 (short_qty > 0)
            data_filtered = ProjectSummaryShortage.get_report_data('WO-001,WO-002', '1', '1', 1, 1)
            # The patching needs to be more sophisticated to simulate all filters.
            # For simplicity, if actual DB calls are made, this would test it directly.
            # Here, I'll test the high-level filtering logic for the mock_queryset.
            
            # Manually filter for testing mock_queryset behavior
            expected_data = sorted([obj for obj in mock_queryset if obj.short_qty > 0 and obj.wono in ['WO-001', 'WO-002']], key=lambda x: x.item_code)
            self.assertEqual(len(data_filtered), len(expected_data))
            self.assertEqual(data_filtered[0].sn, 1) # Check SN assignment

class ProjectSummaryShortageViewsTest(TestCase):
    """
    Integration tests for the Project Summary Shortage views.
    """
    def setUp(self):
        self.client = Client()
        self.list_url = reverse('material_management:projectsummaryshortage_list')
        self.table_url = reverse('material_management:projectsummaryshortage_table')
        self.export_url = reverse('material_management:projectsummaryshortage_export')
        self.po_nav_url = reverse('material_management:po_navigation')
        
        # Mock session data needed by views
        self.client.session['WorkOrderId'] = 'WO-001,WO-002'
        self.client.session['compid'] = 1
        self.client.session['finyear'] = 1

    @patch('material_management.models.ProjectSummaryShortage.get_report_data')
    def test_list_view_get(self, mock_get_report_data):
        mock_get_report_data.return_value = [
            ProjectSummaryShortage(item_id=1, wono='WO-001', item_code='ITEM-001', description='Desc1', short_qty=10),
            ProjectSummaryShortage(item_id=2, wono='WO-001', item_code='ITEM-002', description='Desc2', short_qty=0)
        ]
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/projectsummaryshortage/list.html')
        self.assertIn('project_summary_shortages', response.context)
        self.assertEqual(len(response.context['project_summary_shortages']), 2)
        mock_get_report_data.assert_called_with('WO-001,WO-002', '1', '3', 1, 1) # Check default params

    @patch('material_management.models.ProjectSummaryShortage.get_report_data')
    def test_table_partial_view_htmx(self, mock_get_report_data):
        mock_get_report_data.return_value = [
            ProjectSummaryShortage(item_id=1, wono='WO-001', item_code='ITEM-001', description='Desc1', short_qty=10)
        ]
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(self.table_url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/projectsummaryshortage/_shortage_table.html')
        self.assertContains(response, 'ITEM-001') # Check if data is rendered

    @patch('material_management.models.ProjectSummaryShortage.get_report_data')
    def test_table_partial_view_non_htmx_redirects(self, mock_get_report_data):
        response = self.client.get(self.table_url)
        self.assertEqual(response.status_code, 302) # Should redirect to main list
        self.assertRedirects(response, self.list_url)

    @patch('material_management.models.ProjectSummaryShortage.get_report_data')
    def test_export_view_success(self, mock_get_report_data):
        # Mock a minimal set of data for export
        mock_item_data = ProjectSummaryShortage(
            item_id=1, sn=1, wono='WO-001', item_code='ITEM-001',
            description='Test Item', uom='EA', bom_qty=100.0,
            bom_date='2023-01-01', short_qty=10.0, wis_qty=90.0,
            pr_info='PR001|10|PR1_ID', po_info=json.dumps([{'pr_id': 'PR1_ID', 'po_no': 'PO1', 'po_qty': 10, 'po_date': '2023-01-15 00:00:00', 'po_del_date': '2023-01-20 00:00:00', 'po_rate': 10, 'po_amount': 100, 'supplier': 'Test Sup', 'po_id': 'PO1_ID'}]),
            gin_info=json.dumps([{'po_id': 'PO1_ID', 'gin_no': 'GIN1', 'gin_qty': 5}])
        )
        mock_get_report_data.return_value = [mock_item_data]

        response = self.client.get(self.export_url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        self.assertTrue('attachment; filename="ProjectSummaryShortage_' in response['Content-Disposition'])

        # Read the Excel file and verify content
        excel_file = io.BytesIO(response.content)
        df = pd.read_excel(excel_file)
        self.assertIn('Item Code', df.columns)
        self.assertEqual(df.loc[0, 'Item Code'], 'ITEM-001')
        self.assertEqual(df.loc[0, 'WONo'], 'WO-001')

    @patch('material_management.models.ProjectSummaryShortage.get_report_data')
    def test_export_view_no_data(self, mock_get_report_data):
        mock_get_report_data.return_value = []
        response = self.client.get(self.export_url)
        self.assertEqual(response.status_code, 302) # Should redirect due to no data
        self.assertRedirects(response, self.list_url)
        messages = list(response.wsgi_request._messages)
        self.assertEqual(str(messages[0]), "No records to export.")

    def test_po_navigation_view(self):
        # Test navigation to a mock PO details page
        mock_params = {
            'mid': 'M123',
            'pono': 'POABC',
            'Code': 'SUP123',
            'AmdNo': '0',
            'Swto': '1',
            'Key': 'randomkey',
            'Trans': '3',
            'ModId': '6',
            'SubModId': '35',
            'parentpage': reverse('material_management:projectsummaryshortage_list'),
        }
        query_string = '&'.join([f"{k}={v}" for k, v in mock_params.items()])
        response = self.client.get(f'{self.po_nav_url}?{query_string}')
        
        self.assertEqual(response.status_code, 302)
        # Verify the redirect URL contains the correct parameters and target
        expected_redirect_base = reverse('material_management:po_details_view')
        # We need to manually check if the params are in the redirect URL.
        # Django's assertRedirects doesn't check query params by default.
        self.assertTrue(response.url.startswith(expected_redirect_base))
        self.assertIn('mid=M123', response.url)
        self.assertIn('pono=POABC', response.url)
        self.assertIn('Code=SUP123', response.url)

```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for DataTables Loading:** The `projectsummaryshortage/list.html` uses `hx-get="{% url 'material_management:projectsummaryshortage_table' %}" hx-trigger="load, refreshProjectSummaryShortageList from:body" hx-swap="innerHTML"` on the `div` that wraps the DataTables. This ensures the table is loaded dynamically on page load and can be refreshed by a custom HTMX trigger `refreshProjectSummaryShortageList`.
*   **DataTables Initialization:** The `_shortage_table.html` includes a `<script>` tag with `$(document).ready(function() { $('#projectSummaryShortageTable').DataTable(...); });`. This ensures DataTables is initialized every time the partial is loaded by HTMX.
*   **Alpine.js for UI State:** A simple Alpine.js component `x-data="{ showMessage: false, message: '' }"` is added to the main container in `list.html` to display messages (e.g., from export view) dynamically. The `HX-Trigger` header in the `ProjectSummaryShortageExportView` (`HX-Trigger': 'showMessage'`) will activate this Alpine.js component.
*   **No custom JavaScript:** All dynamic behaviors are managed by HTMX and Alpine.js, eliminating the need for traditional `script.js` files.
*   **CRUD (not applicable to this report):** If this were a CRUD page, HTMX would handle form submissions (`hx-post`), modal loading (`hx-get` for form partials), and list refreshes (`hx-trigger` on success).

### Final Notes

*   **Placeholders:** `{% url 'material_management:materialforecasting_list' %}`, `{% url 'material_management:po_details_view' %}`, and `{% url 'material_management:spr_details_view' %}` are placeholders. In a real project, these would resolve to actual URLs defined in other Django applications or modules.
*   **`project_summary_shortage_view`:** The successful implementation hinges on having this complex SQL view pre-defined in your database to flatten the data for the `ProjectSummaryShortage` model. The logic for `SwitchTo` and `Trans` filters and the intricate calculations (BOMQty, WISQty, GINQty, GQNQty) and aggregations of `PR`/`PO`/`GIN` information (including `Id`s for sub-queries) must be embedded within this database view. If this view cannot be created, a more complex custom Django manager or a service layer performing direct SQL queries would be necessary.
*   **Session Management:** Django's session framework securely handles session variables like `WorkOrderId`, `compid`, and `finyear`, replacing ASP.NET `Session["key"]`.
*   **Error Handling:** The `try-catch` blocks in C# are replaced by Django's robust error handling. Messages are managed using `django.contrib.messages`.
*   **Security:** Django automatically handles CSRF protection for forms, and its ORM protects against SQL injection.
*   **Styling:** Tailwind CSS is assumed to be configured and used for all visual components.
*   **Dry Principle:** Templates use inheritance (`{% extends 'core/base.html' %}`) and partials (`_shortage_table.html`) to avoid code repetition.
*   **Test Coverage:** The provided test examples cover model parsing and view interactions. For a full migration, strive for at least 80% test coverage, including edge cases and error paths.