## ASP.NET to Django Conversion Script: Inward/Outward Register

This document outlines the comprehensive modernization plan for the ASP.NET "Inward/Outward Register" module, transforming it into a modern Django-based solution. The focus is on leveraging AI-assisted automation, adhering to a "fat model, thin view" architecture, and utilizing HTMX, Alpine.js, and DataTables for a highly interactive and efficient user experience.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:** The ASP.NET code utilizes `tblMM_PR_Master`, `tblMM_PR_Details`, `tblMM_SPR_Master`, `tblMM_SPR_Details`, `tblMM_PO_Master`, and `tblMM_PO_Details`. The report aggregates data from these tables. We infer the primary keys to be `Id` and foreign keys as `MId`. Dates are stored as `SysDate`. Amounts are calculated from `Qty`, `Rate`, and `Discount`. `FinYearId` and `CompId` are used for filtering.

**Identified Tables and Columns:**

*   **`tblMM_PR_Master`**:
    *   `Id` (Primary Key, integer)
    *   `SysDate` (DateTime/Date)
    *   `PRNo` (string/text)
    *   `FinYearId` (integer)
    *   `CompId` (integer)
*   **`tblMM_PR_Details`**:
    *   `Id` (Primary Key, integer)
    *   `MId` (Foreign Key to `tblMM_PR_Master.Id`, integer)
    *   `Qty` (decimal/float)
    *   `Rate` (decimal/float)
    *   `Discount` (decimal/float)
*   **`tblMM_SPR_Master`**:
    *   `Id` (Primary Key, integer)
    *   `SysDate` (DateTime/Date)
    *   `SPRNo` (string/text)
    *   `FinYearId` (integer)
    *   `CompId` (integer)
*   **`tblMM_SPR_Details`**:
    *   `Id` (Primary Key, integer)
    *   `MId` (Foreign Key to `tblMM_SPR_Master.Id`, integer)
    *   `Qty` (decimal/float)
    *   `Rate` (decimal/float)
    *   `Discount` (decimal/float)
*   **`tblMM_PO_Master`**:
    *   `Id` (Primary Key, integer)
    *   `SysDate` (DateTime/Date)
    *   `PONo` (string/text)
    *   `FinYearId` (integer)
    *   `CompId` (integer)
*   **`tblMM_PO_Details`**:
    *   `Id` (Primary Key, integer)
    *   `MId` (Foreign Key to `tblMM_PO_Master.Id`, integer)
    *   `Qty` (decimal/float)
    *   `Rate` (decimal/float)
    *   `Discount` (decimal/float)

### Step 2: Identify Backend Functionality

**Analysis:** The ASP.NET page is a read-only reporting interface. It fetches and displays aggregated data based on a date range.

*   **Create:** Not present.
*   **Read:** This is the core functionality. Data is read for PR, SPR, and PO, then aggregated (sum of amounts).
*   **Update:** Not present.
*   **Delete:** Not present.
*   **Validation Logic:** Date format validation (`dd-MM-yyyy`). This will be handled by Django forms.

### Step 3: Infer UI Components

**Analysis:** The ASP.NET page uses `TextBox` for dates with a `CalendarExtender`, a `Button` for search, and three `GridView` controls to display data.

*   **Date Inputs (`TxtFromDt`, `TxtToDt`):** Will be converted to HTML `input type="date"` fields.
*   **Search Button (`Button1`):** Will trigger an HTMX request to dynamically load/refresh the report tables.
*   **Data Grids (`GridView2`, `GridView3`, `GridView4`):** Will be replaced by three separate DataTables instances within a single HTMX-controlled partial template.
*   **Total Labels (`lblPRTot`, `lblSPRTot`, `lblPOTot`):** These sums will be calculated in the model methods and displayed directly in the template.

### Step 4: Generate Django Code

We will create a Django application named `material_management` to house these components.

#### 4.1 Models (`material_management/models.py`)

To adhere to the fat model principle and integrate with existing databases, we define models for each master and detail table. The complex report aggregation logic will be implemented as static methods within the `_Master` models. We assume `FinYearId` and `CompId` are available (e.g., from user session or configuration). For demonstration, we'll use placeholder values.

```python
from django.db import models
from django.db import connection # For raw SQL queries
from datetime import datetime, date

class MaterialReportManager(models.Manager):
    """
    Manager for handling complex material movement report queries.
    This demonstrates centralizing report logic, adhering to 'fat model'.
    """

    def _execute_report_query(self, sql_query, params):
        """Helper to execute raw SQL and return results as list of dicts."""
        with connection.cursor() as cursor:
            cursor.execute(sql_query, params)
            columns = [col[0] for col in cursor.description]
            return [
                dict(zip(columns, row))
                for row in cursor.fetchall()
            ]

    def get_pr_details_for_report(self, from_date: date, to_date: date, fin_year_id: int, comp_id: int):
        """
        Retrieves aggregated PR (Purchase Requisition) details for the report.
        """
        sql = """
        SELECT
            tblMM_PR_Master.SysDate AS RDate,
            tblMM_PR_Master.PRNo AS PRNo,
            SUM(tblMM_PR_Details.Qty * (tblMM_PR_Details.Rate - (tblMM_PR_Details.Rate * tblMM_PR_Details.Discount / 100))) AS PRAmt
        FROM
            tblMM_PR_Details
        INNER JOIN
            tblMM_PR_Master ON tblMM_PR_Details.MId = tblMM_PR_Master.Id
        WHERE
            tblMM_PR_Master.FinYearId = %s AND
            tblMM_PR_Master.CompId = %s AND
            tblMM_PR_Master.SysDate BETWEEN %s AND %s
        GROUP BY
            tblMM_PR_Master.PRNo, tblMM_PR_Master.SysDate
        ORDER BY
            tblMM_PR_Master.SysDate DESC;
        """
        params = [fin_year_id, comp_id, from_date, to_date]
        return self._execute_report_query(sql, params)

    def get_spr_details_for_report(self, from_date: date, to_date: date, fin_year_id: int, comp_id: int):
        """
        Retrieves aggregated SPR (Store Purchase Requisition) details for the report.
        """
        sql = """
        SELECT
            tblMM_SPR_Master.SysDate AS RDate,
            tblMM_SPR_Master.SPRNo AS SPRNo,
            SUM(tblMM_SPR_Details.Qty * (tblMM_SPR_Details.Rate - (tblMM_SPR_Details.Rate * tblMM_SPR_Details.Discount / 100))) AS SPRAmt
        FROM
            tblMM_SPR_Details
        INNER JOIN
            tblMM_SPR_Master ON tblMM_SPR_Details.MId = tblMM_SPR_Master.Id
        WHERE
            tblMM_SPR_Master.FinYearId = %s AND
            tblMM_SPR_Master.CompId = %s AND
            tblMM_SPR_Master.SysDate BETWEEN %s AND %s
        GROUP BY
            tblMM_SPR_Master.SPRNo, tblMM_SPR_Master.SysDate
        ORDER BY
            tblMM_SPR_Master.SysDate DESC;
        """
        params = [fin_year_id, comp_id, from_date, to_date]
        return self._execute_report_query(sql, params)

    def get_po_details_for_report(self, from_date: date, to_date: date, fin_year_id: int, comp_id: int):
        """
        Retrieves aggregated PO (Purchase Order) details for the report.
        """
        sql = """
        SELECT
            tblMM_PO_Master.SysDate AS RDate,
            tblMM_PO_Master.PONo AS PONo,
            SUM(tblMM_PO_Details.Qty * (tblMM_PO_Details.Rate - (tblMM_PO_Details.Rate * tblMM_PO_Details.Discount / 100))) AS POAmt
        FROM
            tblMM_PO_Details
        INNER JOIN
            tblMM_PO_Master ON tblMM_PO_Details.MId = tblMM_PO_Master.Id
        WHERE
            tblMM_PO_Master.FinYearId = %s AND
            tblMM_PO_Master.CompId = %s AND
            tblMM_PO_Master.SysDate BETWEEN %s AND %s
        GROUP BY
            tblMM_PO_Master.PONo, tblMM_PO_Master.SysDate
        ORDER BY
            tblMM_PO_Master.SysDate DESC;
        """
        params = [fin_year_id, comp_id, from_date, to_date]
        return self._execute_report_query(sql, params)


class PRMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate')
    pr_no = models.CharField(db_column='PRNo', max_length=50)
    fin_year_id = models.IntegerField(db_column='FinYearId')
    comp_id = models.IntegerField(db_column='CompId')

    # Assign the custom manager
    reports = MaterialReportManager()

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblMM_PR_Master'
        verbose_name = 'PR Master'
        verbose_name_plural = 'PR Masters'

    def __str__(self):
        return self.pr_no

class PRDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    m_id = models.ForeignKey(PRMaster, models.DO_NOTHING, db_column='MId')
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=4)
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=4)
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=4)

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Details'
        verbose_name = 'PR Detail'
        verbose_name_plural = 'PR Details'

    def __str__(self):
        return f"Detail for PR {self.m_id.pr_no}"

class SPRMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate')
    spr_no = models.CharField(db_column='SPRNo', max_length=50)
    fin_year_id = models.IntegerField(db_column='FinYearId')
    comp_id = models.IntegerField(db_column='CompId')

    # Assign the custom manager
    reports = MaterialReportManager()

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Master'
        verbose_name = 'SPR Master'
        verbose_name_plural = 'SPR Masters'

    def __str__(self):
        return self.spr_no

class SPRDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    m_id = models.ForeignKey(SPRMaster, models.DO_NOTHING, db_column='MId')
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=4)
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=4)
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=4)

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Details'
        verbose_name = 'SPR Detail'
        verbose_name_plural = 'SPR Details'

    def __str__(self):
        return f"Detail for SPR {self.m_id.spr_no}"

class POMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate')
    po_no = models.CharField(db_column='PONo', max_length=50)
    fin_year_id = models.IntegerField(db_column='FinYearId')
    comp_id = models.IntegerField(db_column='CompId')

    # Assign the custom manager
    reports = MaterialReportManager()

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'
        verbose_name = 'PO Master'
        verbose_name_plural = 'PO Masters'

    def __str__(self):
        return self.po_no

class PODetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    m_id = models.ForeignKey(POMaster, models.DO_NOTHING, db_column='MId')
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=4)
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=4)
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=4)

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Details'
        verbose_name = 'PO Detail'
        verbose_name_plural = 'PO Details'

    def __str__(self):
        return f"Detail for PO {self.m_id.po_no}"

```

#### 4.2 Forms (`material_management/forms.py`)

A simple form to capture the date range for the report.

```python
from django import forms
from datetime import date, timedelta

class DateRangeForm(forms.Form):
    from_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'value': date.today().strftime('%Y-%m-%d') # Default to current date
        }),
        label="From Date"
    )
    to_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'value': date.today().strftime('%Y-%m-%d') # Default to current date
        }),
        label="To Date"
    )

    def clean(self):
        cleaned_data = super().clean()
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')

        if from_date and to_date and from_date > to_date:
            self.add_error('from_date', 'From date cannot be after To date.')
            self.add_error('to_date', 'To date cannot be before From date.')
        return cleaned_data
```

#### 4.3 Views (`material_management/views.py`)

We'll use a `TemplateView` for the main page and a separate `TemplateView` (or `View`) for the HTMX-loaded partial containing the tables, ensuring thin views.

```python
from django.views.generic import TemplateView, View
from django.http import HttpResponseBadRequest, JsonResponse
from django.shortcuts import render
from datetime import date
from decimal import Decimal, InvalidOperation

from .forms import DateRangeForm
from .models import PRMaster, SPRMaster, POMaster # Using the models for their static report methods

# Assuming FinYearId and CompId are available from session or user profile
# For demonstration, hardcode them. In production, derive from request.user or session.
DEFAULT_FIN_YEAR_ID = 1
DEFAULT_COMP_ID = 1

class InwardOutwardRegisterView(TemplateView):
    """
    Main view for the Inward/Outward Register page.
    Renders the date input form and initial container for HTMX-loaded tables.
    """
    template_name = 'material_management/inward_outward_register.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = DateRangeForm(initial={
            'from_date': date.today(),
            'to_date': date.today()
        })
        return context

class InwardOutwardRegisterTablePartialView(View):
    """
    HTMX endpoint to fetch and render the PR, SPR, and PO tables and their totals.
    This view remains thin by delegating data retrieval to model managers.
    """
    def get(self, request, *args, **kwargs):
        # Default to current date if no parameters are provided (initial load)
        from_date_str = request.GET.get('from_date', date.today().strftime('%Y-%m-%d'))
        to_date_str = request.GET.get('to_date', date.today().strftime('%Y-%m-%d'))

        try:
            from_date = datetime.strptime(from_date_str, '%Y-%m-%d').date()
            to_date = datetime.strptime(to_date_str, '%Y-%m-%d').date()
        except ValueError:
            return HttpResponseBadRequest("Invalid date format. Dates must be YYYY-MM-DD.")

        # Re-initialize the form with the fetched dates for display purposes
        form = DateRangeForm(initial={'from_date': from_date, 'to_date': to_date})

        # Delegate data fetching to model managers (fat model)
        # Using the MaterialReportManager directly from one of the models, as it's common.
        pr_details = PRMaster.reports.get_pr_details_for_report(
            from_date, to_date, DEFAULT_FIN_YEAR_ID, DEFAULT_COMP_ID
        )
        spr_details = SPRMaster.reports.get_spr_details_for_report(
            from_date, to_date, DEFAULT_FIN_YEAR_ID, DEFAULT_COMP_ID
        )
        po_details = POMaster.reports.get_po_details_for_report(
            from_date, to_date, DEFAULT_FIN_YEAR_ID, DEFAULT_COMP_ID
        )

        # Calculate totals
        pr_total = sum(Decimal(d.get('PRAmt', 0) or 0) for d in pr_details)
        spr_total = sum(Decimal(d.get('SPRAmt', 0) or 0) for d in spr_details)
        po_total = sum(Decimal(d.get('POAmt', 0) or 0) for d in po_details)

        context = {
            'pr_records': pr_details,
            'spr_records': spr_details,
            'po_records': po_details,
            'pr_total': pr_total,
            'spr_total': spr_total,
            'po_total': po_total,
            'form': form # Pass the form to render the date inputs with current values
        }
        return render(request, 'material_management/_inward_outward_table.html', context)

```

#### 4.4 Templates (`material_management/templates/material_management/`)

We'll have a main page template and a partial template that HTMX swaps in.

**`inward_outward_register.html`**

```html
{% extends 'core/base.html' %}

{% block title %}Inward/Outward Register - ERP{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6 bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Inward/Outward Register</h2>
        
        <form id="dateFilterForm" 
              hx-get="{% url 'material_management:inward_outward_table' %}"
              hx-target="#reportTablesContainer"
              hx-indicator="#loadingIndicator"
              hx-swap="innerHTML"
              class="flex flex-wrap items-end gap-4">
            
            <div>
                <label for="{{ form.from_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.from_date.label }}: From
                </label>
                {{ form.from_date }}
                {% if form.from_date.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.from_date.errors }}</p>
                {% endif %}
            </div>
            
            <div>
                <label for="{{ form.to_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.to_date.label }}: To
                </label>
                {{ form.to_date }}
                {% if form.to_date.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.to_date.errors }}</p>
                {% endif %}
            </div>
            
            <button type="submit" 
                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                Search
            </button>
            
            <div id="loadingIndicator" class="htmx-indicator ml-4">
                <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                <span class="ml-2 text-gray-600">Loading...</span>
            </div>
        </form>
    </div>

    <div id="reportTablesContainer"
         hx-trigger="load delay:100ms"
         hx-get="{% url 'material_management:inward_outward_table' %}"
         hx-include="#dateFilterForm" {# Includes form fields for initial load #}
         hx-swap="innerHTML">
        <!-- Initial loading state -->
        <div class="text-center py-12">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Report Data...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.tailwindcss.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.7/css/dataTables.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.7/css/dataTables.tailwindcss.min.css">
<script>
    document.addEventListener('htmx:afterSwap', function(event) {
        // Initialize DataTables on the new content after HTMX swap
        if (event.detail.target.id === 'reportTablesContainer') {
            $('#prTable').DataTable({
                "paging": true,
                "searching": true,
                "ordering": true,
                "info": true,
                "pageLength": 10
            });
            $('#sprTable').DataTable({
                "paging": true,
                "searching": true,
                "ordering": true,
                "info": true,
                "pageLength": 10
            });
            $('#poTable').DataTable({
                "paging": true,
                "searching": true,
                "ordering": true,
                "info": true,
                "pageLength": 10
            });
        }
    });

    document.addEventListener('alpine:init', () => {
        Alpine.data('datePicker', () => ({
            // Basic Alpine.js for any simple UI state, if needed.
            // For example, if we were using a custom calendar UI library.
            // With input type="date", Alpine.js is less critical for date selection.
        }));
    });
</script>
{% endblock %}
```

**`_inward_outward_table.html`**

```html
<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <!-- PR Table -->
    <div class="bg-white p-6 rounded-lg shadow-md">
        <h3 class="text-xl font-semibold text-gray-800 mb-4">PR (Purchase Requisition)</h3>
        <div class="overflow-x-auto">
            <table id="prTable" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PR No</th>
                        <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for record in pr_records %}
                    <tr>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-right">{{ forloop.counter }}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-center">{{ record.RDate|date:"d-m-Y" }}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-center">{{ record.PRNo }}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-right">{{ record.PRAmt|floatformat:2 }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="4" class="px-4 py-2 text-center text-sm text-gray-500">No PR records found for the selected date range.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        <div class="text-right mt-4 text-lg font-bold text-gray-800">
            Total : <span class="text-blue-600">{{ pr_total|floatformat:2 }}</span>
        </div>
    </div>

    <!-- SPR Table -->
    <div class="bg-white p-6 rounded-lg shadow-md">
        <h3 class="text-xl font-semibold text-gray-800 mb-4">SPR (Store Purchase Requisition)</h3>
        <div class="overflow-x-auto">
            <table id="sprTable" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SPR No</th>
                        <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for record in spr_records %}
                    <tr>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-right">{{ forloop.counter }}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-center">{{ record.RDate|date:"d-m-Y" }}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-center">{{ record.SPRNo }}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-right">{{ record.SPRAmt|floatformat:2 }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="4" class="px-4 py-2 text-center text-sm text-gray-500">No SPR records found for the selected date range.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        <div class="text-right mt-4 text-lg font-bold text-gray-800">
            Total : <span class="text-blue-600">{{ spr_total|floatformat:2 }}</span>
        </div>
    </div>

    <!-- PO Table -->
    <div class="bg-white p-6 rounded-lg shadow-md">
        <h3 class="text-xl font-semibold text-gray-800 mb-4">PO (Purchase Order)</h3>
        <div class="overflow-x-auto">
            <table id="poTable" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
                        <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for record in po_records %}
                    <tr>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-right">{{ forloop.counter }}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-center">{{ record.RDate|date:"d-m-Y" }}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-center">{{ record.PONo }}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-right">{{ record.POAmt|floatformat:2 }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="4" class="px-4 py-2 text-center text-sm text-gray-500">No PO records found for the selected date range.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        <div class="text-right mt-4 text-lg font-bold text-gray-800">
            Total : <span class="text-blue-600">{{ po_total|floatformat:2 }}</span>
        </div>
    </div>
</div>
```

#### 4.5 URLs (`material_management/urls.py`)

Define the URL patterns for the main page and the HTMX partial view.

```python
from django.urls import path
from .views import InwardOutwardRegisterView, InwardOutwardRegisterTablePartialView

app_name = 'material_management' # Namespace for URLs

urlpatterns = [
    path('inward-outward-register/', InwardOutwardRegisterView.as_view(), name='inward_outward_register'),
    path('inward-outward-register/table/', InwardOutwardRegisterTablePartialView.as_view(), name='inward_outward_table'),
]
```

#### 4.6 Tests (`material_management/tests.py`)

Comprehensive tests for models (data retrieval methods) and views (rendering and HTMX interactions).

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date, timedelta
from decimal import Decimal

# Import models for testing, even if they are managed=False.
# We need to simulate the database state for our report methods.
from .models import PRMaster, PRDetail, SPRMaster, SPRDetail, POMaster, PODetail, MaterialReportManager

class MaterialReportManagerTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Set up a clean test database for managed=False models
        # This requires direct SQL insertions or a mock for test environment
        # For a full test, you'd ensure these tables exist and have data.
        # Here, we'll simulate the data that would be returned by the raw queries.
        
        # We need a way to mock the raw SQL queries or populate the external DB.
        # For this example, we'll focus on testing the manager's logic assuming valid DB data.
        # In a real scenario with managed=False, you'd use a transactional test case
        # and pre-populate the external database or use a mocking library for db connection.
        
        # Dummy values for FinYearId and CompId for testing
        cls.FIN_YEAR_ID = 1
        cls.COMP_ID = 1

        cls.today = date.today()
        cls.yesterday = cls.today - timedelta(days=1)
        cls.tomorrow = cls.today + timedelta(days=1)
        
        # Simulate data for PR
        cls.pr_data = [
            {'RDate': cls.yesterday, 'PRNo': 'PR001', 'PRAmt': Decimal('100.50')},
            {'RDate': cls.today, 'PRNo': 'PR002', 'PRAmt': Decimal('200.00')},
        ]
        
        # Simulate data for SPR
        cls.spr_data = [
            {'RDate': cls.yesterday, 'SPRNo': 'SPR001', 'SPRAmt': Decimal('50.25')},
            {'RDate': cls.today, 'SPRNo': 'SPR002', 'SPRAmt': Decimal('150.00')},
        ]

        # Simulate data for PO
        cls.po_data = [
            {'RDate': cls.yesterday, 'PONo': 'PO001', 'POAmt': Decimal('300.75')},
            {'RDate': cls.today, 'PONo': 'PO002', 'POAmt': Decimal('400.00')},
        ]

    def setUp(self):
        self.manager = MaterialReportManager()
        # Mock the _execute_report_query method for isolated testing of manager methods
        # This prevents actual DB calls during unit tests if managed=False tables aren't setup
        # For integration tests, you'd need the actual database setup.
        
        # Patch for PR report
        self.manager._execute_report_query_original_pr = self.manager._execute_report_query
        self.manager._execute_report_query = lambda sql_query, params: \
            self.pr_data if "tblMM_PR_Master" in sql_query else \
            self.spr_data if "tblMM_SPR_Master" in sql_query else \
            self.po_data if "tblMM_PO_Master" in sql_query else []

    def tearDown(self):
        # Restore the original method after each test
        self.manager._execute_report_query = self.manager._execute_report_query_original_pr

    def test_get_pr_details_for_report(self):
        # Test with the range including all dummy data
        results = self.manager.get_pr_details_for_report(
            self.yesterday, self.today, self.FIN_YEAR_ID, self.COMP_ID
        )
        self.assertEqual(len(results), 2)
        self.assertEqual(results[0]['PRNo'], 'PR001')
        self.assertEqual(results[1]['PRAmt'], Decimal('200.00'))

        # Test with a date range that yields no results
        results_empty = self.manager.get_pr_details_for_report(
            self.tomorrow, self.tomorrow, self.FIN_YEAR_ID, self.COMP_ID
        )
        self.assertEqual(len(results_empty), 0)

    def test_get_spr_details_for_report(self):
        results = self.manager.get_spr_details_for_report(
            self.yesterday, self.today, self.FIN_YEAR_ID, self.COMP_ID
        )
        self.assertEqual(len(results), 2)
        self.assertEqual(results[0]['SPRNo'], 'SPR001')
        self.assertEqual(results[1]['SPRAmt'], Decimal('150.00'))

    def test_get_po_details_for_report(self):
        results = self.manager.get_po_details_for_report(
            self.yesterday, self.today, self.FIN_YEAR_ID, self.COMP_ID
        )
        self.assertEqual(len(results), 2)
        self.assertEqual(results[0]['PONo'], 'PO001')
        self.assertEqual(results[1]['POAmt'], Decimal('400.00'))

class InwardOutwardRegisterViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.main_url = reverse('material_management:inward_outward_register')
        self.table_url = reverse('material_management:inward_outward_table')
        self.today = date.today()
        self.yesterday = self.today - timedelta(days=1)
        
        # Mock the MaterialReportManager's report methods to control data for view tests
        # This is crucial for isolated view testing without requiring external DB setup.
        self.original_get_pr = PRMaster.reports.get_pr_details_for_report
        self.original_get_spr = SPRMaster.reports.get_spr_details_for_report
        self.original_get_po = POMaster.reports.get_po_details_for_report
        
        PRMaster.reports.get_pr_details_for_report = self.mock_get_pr_details
        SPRMaster.reports.get_spr_details_for_report = self.mock_get_spr_details
        POMaster.reports.get_po_details_for_report = self.mock_get_po_details
        
        self.mock_data = {
            'pr': [{'RDate': self.today, 'PRNo': 'TEST_PR1', 'PRAmt': Decimal('10.00')}],
            'spr': [{'RDate': self.today, 'SPRNo': 'TEST_SPR1', 'SPRAmt': Decimal('20.00')}],
            'po': [{'RDate': self.today, 'PONo': 'TEST_PO1', 'POAmt': Decimal('30.00')}]
        }

    def tearDown(self):
        PRMaster.reports.get_pr_details_for_report = self.original_get_pr
        SPRMaster.reports.get_spr_details_for_report = self.original_get_spr
        POMaster.reports.get_po_details_for_report = self.original_get_po

    def mock_get_pr_details(self, from_date, to_date, fin_year_id, comp_id):
        return self.mock_data['pr']

    def mock_get_spr_details(self, from_date, to_date, fin_year_id, comp_id):
        return self.mock_data['spr']

    def mock_get_po_details(self, from_date, to_date, fin_year_id, comp_id):
        return self.mock_data['po']

    def test_main_view_get(self):
        response = self.client.get(self.main_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/inward_outward_register.html')
        self.assertIn('form', response.context)
        # Check initial form values are today's date
        self.assertEqual(response.context['form']['from_date'].value(), self.today.strftime('%Y-%m-%d'))
        self.assertEqual(response.context['form']['to_date'].value(), self.today.strftime('%Y-%m-%d'))
        self.assertContains(response, 'Inward/Outward Register')

    def test_table_partial_view_get_initial_load(self):
        # Simulate initial HTMX load without explicit date params
        response = self.client.get(self.table_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/_inward_outward_table.html')
        self.assertIn('pr_records', response.context)
        self.assertIn('spr_records', response.context)
        self.assertIn('po_records', response.context)
        self.assertGreater(len(response.context['pr_records']), 0) # Should get mock data
        self.assertGreater(len(response.context['spr_records']), 0)
        self.assertGreater(len(response.context['po_records']), 0)
        self.assertEqual(response.context['pr_total'], Decimal('10.00'))
        self.assertEqual(response.context['spr_total'], Decimal('20.00'))
        self.assertEqual(response.context['po_total'], Decimal('30.00'))

    def test_table_partial_view_get_with_dates(self):
        # Simulate HTMX request with specific date range
        params = {
            'from_date': self.yesterday.strftime('%Y-%m-%d'),
            'to_date': self.today.strftime('%Y-%m-%d')
        }
        response = self.client.get(self.table_url, params)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/_inward_outward_table.html')
        
        # Verify that mock data was returned
        self.assertEqual(response.context['pr_records'], self.mock_data['pr'])
        self.assertEqual(response.context['spr_records'], self.mock_data['spr'])
        self.assertEqual(response.context['po_records'], self.mock_data['po'])
        
        # Verify form in context also has updated dates
        self.assertEqual(response.context['form']['from_date'].value(), self.yesterday.strftime('%Y-%m-%d'))
        self.assertEqual(response.context['form']['to_date'].value(), self.today.strftime('%Y-%m-%d'))


    def test_table_partial_view_invalid_date_format(self):
        params = {
            'from_date': 'invalid-date',
            'to_date': self.today.strftime('%Y-%m-%d')
        }
        response = self.client.get(self.table_url, params)
        self.assertEqual(response.status_code, 400) # Bad Request for invalid date

    def test_form_validation_from_date_after_to_date(self):
        form_data = {
            'from_date': (date.today() + timedelta(days=1)).strftime('%Y-%m-%d'),
            'to_date': date.today().strftime('%Y-%m-%d')
        }
        form = DateRangeForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('from_date', form.errors)
        self.assertIn('to_date', form.errors)
        self.assertIn('From date cannot be after To date.', form.errors['from_date'])

    def test_form_validation_valid_dates(self):
        form_data = {
            'from_date': self.yesterday.strftime('%Y-%m-%d'),
            'to_date': self.today.strftime('%Y-%m-%d')
        }
        form = DateRangeForm(data=form_data)
        self.assertTrue(form.is_valid())
```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for Date Range Search:** The `Search` button in `inward_outward_register.html` uses `hx-get` to submit the form data (including `from_date` and `to_date`) to the `inward_outward_table` URL. The response swaps the content of `#reportTablesContainer`, completely refreshing the tables without a full page reload. An `hx-trigger="load"` on the container ensures the tables are loaded on the initial page view.
*   **DataTables for List Views:** All three tables (`prTable`, `sprTable`, `poTable`) are initialized as DataTables instances using jQuery in the `extra_js` block of the main template. The `htmx:afterSwap` event listener ensures that DataTables is re-initialized every time the `#reportTablesContainer` content is updated by HTMX.
*   **Alpine.js for UI State:** While `input type="date"` provides native date pickers, Alpine.js is included as a foundational element for any future client-side interactivity, such as custom modals or complex UI elements (though not strictly necessary for this particular report's functionality).
*   **No Custom JavaScript:** All dynamic interactions are handled by HTMX, and for table enhancements, jQuery DataTables is used. No additional custom JavaScript is written beyond their standard initialization.

---

## Final Notes

This modernization plan provides a robust, maintainable, and high-performance solution for the "Inward/Outward Register" module. By adhering to Django best practices, separating concerns, and embracing modern frontend techniques, the new application will be significantly more efficient and user-friendly. The emphasis on AI-assisted automation and clear, step-by-step instructions ensures that this complex migration can be managed effectively, reducing manual effort and potential errors.

The `DEFAULT_FIN_YEAR_ID` and `DEFAULT_COMP_ID` in `views.py` are placeholders. In a real ERP system, these values would be dynamically retrieved from the current user's session or profile, reflecting the active financial year and company. This setup provides a clean foundation for integrating with user and organization management modules.