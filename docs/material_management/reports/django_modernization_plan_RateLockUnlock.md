This document outlines a comprehensive plan for modernizing your existing ASP.NET "Rate Lock Unlock" application to a modern Django-based solution. Our approach prioritizes automation, leverages contemporary web technologies like HTMX and Alpine.js, and adheres to Django's best practices, ensuring a robust, maintainable, and scalable system.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists in `core/base.html`.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the ASP.NET code, we infer the following database tables used for data retrieval and dropdown population. The main report data table is assumed, as it's not explicitly defined but implied by the report's purpose.

**Instructions:**
- Identify database-related elements like SQL commands (`fun.select`), `SqlDataAdapter`, and `DataSet`.
- Extract table and column names from these operations.
- Infer data types based on common usage or UI bindings.

**Inferred Tables and Columns:**

1.  **`tblDG_Category_Master` (for `DrpCategory1`)**
    *   `CId` (Primary Key, integer)
    *   `Symbol` (String)
    *   `CName` (String)
    *   `CompId` (Integer, likely foreign key to a Company table)

2.  **`tblHR_OfficeStaff` (for `TxtEmpName` AutoComplete)**
    *   `EmpId` (Primary Key, integer)
    *   `EmployeeName` (String)
    *   `CompId` (Integer, likely foreign key to a Company table)

3.  **`tblRateLockUnlockReportData` (Inferred for the report's underlying data)**
    *   This table represents the actual entries displayed in the "Rate Lock Unlock" report. The fields are derived from the search parameters and typical report columns.
    *   `RateLockEntryId` (Primary Key, integer)
    *   `ItemCode` (String)
    *   `Description` (String)
    *   `CategoryId` (Integer, foreign key to `tblDG_Category_Master`)
    *   `RecordType` (String, e.g., 'Category', 'WOItems')
    *   `LockDate` (Date)
    *   `UnlockDate` (Date)
    *   `LockedByEmpId` (Integer, foreign key to `tblHR_OfficeStaff`)
    *   `Rate` (Decimal/Float)
    *   `Status` (String, e.g., 'Locked', 'Unlocked')
    *   `CompanyId` (Integer, `CompId` from session)
    *   `FinancialYearId` (Integer, `FinYearId` from session)

### Step 2: Identify Backend Functionality

The ASP.NET page primarily functions as a **report generation interface** with dynamic filtering capabilities, rather than a direct CRUD page for `RateLockUnlockEntry` records.

**Instructions:**
- Analyze `Page_Load`, `btnSearch_Click`, `DrpType_SelectedIndexChanged`, and `GetCompletionList` methods.
- Identify data retrieval, filtering, UI state management, and external service calls.

**Identified Functionality:**

*   **Read (Search/Filter):** The core function is to allow users to search and filter `tblRateLockUnlockReportData` records based on:
    *   `DrpType` (Category / WOItems)
    *   `DrpCategory1` (dynamically populated based on `DrpType`)
    *   `DrpSearchCode` (Item Code / Description) and `txtSearchItemCode`
    *   `Txtfromdate` and `TxtTodate` (date range)
    *   `TxtEmpName` (employee via autocomplete)
*   **Dynamic UI Updates:**
    *   Visibility of `DrpCategory1`, `DrpSearchCode`, `txtSearchItemCode` changes based on `DrpType` selection.
    *   `DrpCategory1` is populated with data from `tblDG_Category_Master` when `DrpType` is "Category".
*   **Autocomplete for Employee Name:** `GetCompletionList` services `TxtEmpName` by querying `tblHR_OfficeStaff`.
*   **Date Validation:** Client-side and server-side validation for date formats.
*   **Session Management:** `CompId` and `FinYearId` are retrieved from session variables for data filtering.
*   **Reporting:** The search results are displayed in an `iframe` pointing to `RateLockUnlock_Details.aspx`. In Django, this will be replaced by loading the results table via HTMX into a `div`.

### Step 3: Infer UI Components

The ASP.NET page uses various WebControls to capture user input and trigger actions.

**Instructions:**
- Map ASP.NET WebControls to their equivalent HTML elements or Django form widgets.
- Note any client-side JavaScript or AJAX behaviors that need to be replicated with HTMX/Alpine.js.

**Inferred UI Components and Django Equivalents:**

*   **Dropdowns (`asp:DropDownList`)**:
    *   `DrpType`: `forms.ChoiceField` (HTML `<select>`)
    *   `DrpCategory1`: `forms.ModelChoiceField` (HTML `<select>`), dynamically updated via HTMX.
    *   `DrpSearchCode`: `forms.ChoiceField` (HTML `<select>`)
*   **Textboxes (`asp:TextBox`)**:
    *   `txtSearchItemCode`: `forms.CharField` (HTML `<input type="text">`)
    *   `Txtfromdate`, `TxtTodate`: `forms.DateField` (HTML `<input type="date">`), potentially with a JS date picker (Alpine.js integration).
    *   `TxtEmpName`: `forms.CharField` (HTML `<input type="text">`), with HTMX for autocomplete suggestions.
*   **Buttons (`asp:Button`)**:
    *   `btnSearch`: HTML `<button type="submit">` or `hx-post` trigger.
*   **Date Pickers (`cc1:CalendarExtender`)**: Replaced by HTML5 `<input type="date">` or a simple Alpine.js date picker for improved UX.
*   **Autocomplete (`cc1:AutoCompleteExtender`)**: Replaced by HTMX `hx-get` to a backend view for suggestions, and Alpine.js for displaying the list.
*   **Data Display (`iframe`)**: Replaced by a `div` element where the DataTables content will be loaded via HTMX `hx-get` requests.
*   **Validation (`asp:RequiredFieldValidator`, `asp:RegularExpressionValidator`)**: Handled by Django forms validation.

### Step 4: Generate Django Code

We will create a new Django application named `reports` to house the Rate Lock Unlock functionality.

#### 4.1 Models (`reports/models.py`)

We'll define models for `Category`, `Employee`, and `RateLockEntry` (representing a row in the report). These models will be set to `managed = False` to integrate with existing database tables.

```python
from django.db import models
from django.utils.translation import gettext_lazy as _

# Placeholder for a Company model if it exists, otherwise use IntegerField
# class Company(models.Model):
#     comp_id = models.IntegerField(primary_key=True, db_column='CompId')
#     name = models.CharField(max_length=255, db_column='CompanyName')
#     class Meta:
#         managed = False
#         db_table = 'tblComp_Master'

class Category(models.Model):
    """
    Maps to tblDG_Category_Master for populating the category dropdown.
    """
    id = models.IntegerField(primary_key=True, db_column='CId')
    symbol = models.CharField(max_length=50, db_column='Symbol', blank=True, null=True)
    name = models.CharField(max_length=255, db_column='CName')
    company_id = models.IntegerField(db_column='CompId') # Assuming CompId is an int, not FK to Company model

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = _('Category')
        verbose_name_plural = _('Categories')

    def __str__(self):
        return f"{self.symbol or ''}-{self.name}"

class Employee(models.Model):
    """
    Maps to tblHR_OfficeStaff for employee autocomplete.
    """
    id = models.IntegerField(primary_key=True, db_column='EmpId')
    name = models.CharField(max_length=255, db_column='EmployeeName')
    company_id = models.IntegerField(db_column='CompId') # Assuming CompId is an int, not FK to Company model

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = _('Employee')
        verbose_name_plural = _('Employees')

    def __str__(self):
        return self.name
        
    def get_full_name_with_id(self):
        """
        Returns employee name and ID for autocomplete display (e.g., 'John Doe [123]').
        """
        return f"{self.name} [{self.id}]"

class RateLockEntry(models.Model):
    """
    Represents a single entry in the Rate Lock Unlock report.
    This model is inferred from the search parameters and report purpose.
    It's assumed to be a table that holds the details of rate lock/unlock events.
    """
    id = models.AutoField(primary_key=True, db_column='RateLockEntryId') # Assuming an ID for report entries
    item_code = models.CharField(max_length=100, db_column='ItemCode', blank=True, null=True)
    description = models.CharField(max_length=500, db_column='Description', blank=True, null=True)
    category = models.ForeignKey(Category, on_delete=models.DO_NOTHING, db_column='CategoryId', related_name='rate_lock_entries', blank=True, null=True)
    record_type = models.CharField(max_length=50, db_column='RecordType') # 'Category' or 'WOItems'
    lock_date = models.DateField(db_column='LockDate')
    unlock_date = models.DateField(db_column='UnlockDate', blank=True, null=True)
    locked_by = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='LockedByEmpId', related_name='locked_rate_entries', blank=True, null=True)
    rate = models.DecimalField(max_digits=10, decimal_places=4, db_column='Rate')
    status = models.CharField(max_length=50, db_column='Status') # e.g., 'Locked', 'Unlocked'
    company_id = models.IntegerField(db_column='CompanyId') # CompId from session
    financial_year_id = models.IntegerField(db_column='FinancialYearId') # FinYearId from session

    class Meta:
        managed = False
        db_table = 'tblRateLockUnlockReportData' # Placeholder table name
        verbose_name = _('Rate Lock Entry')
        verbose_name_plural = _('Rate Lock Entries')

    def __str__(self):
        return f"Rate Lock Entry for {self.item_code or self.description} (Locked: {self.lock_date})"

    # Example business logic method (Fat Model principle)
    def is_currently_locked(self):
        """
        Checks if the rate lock entry is currently active (locked).
        """
        return self.status == 'Locked' and (self.unlock_date is None or self.unlock_date > models.functions.Now())

    def get_display_item_info(self, search_code_type):
        """
        Returns item information based on search code type.
        """
        if search_code_type == '1': # Item Code
            return self.item_code
        elif search_code_type == '2': # Description
            return self.description
        return ''

```

#### 4.2 Forms (`reports/forms.py`)

We'll create a `RateLockUnlockSearchForm` for the main search interface and a `RateLockEntryForm` for potential (though not directly implemented here) CRUD operations on `RateLockEntry`.

```python
from django import forms
from django.utils.translation import gettext_lazy as _
from .models import Category, RateLockEntry

class RateLockUnlockSearchForm(forms.Form):
    """
    Form for Rate Lock Unlock report search parameters.
    """
    TYPE_CHOICES = [
        ('', _('Select')),
        ('Category', _('Category')),
        ('WOItems', _('WO Items')),
    ]
    SEARCH_CODE_CHOICES = [
        ('', _('Select')),
        ('1', _('Item Code')),
        ('2', _('Description')),
    ]

    type = forms.ChoiceField(
        choices=TYPE_CHOICES,
        label=_('Type'),
        required=False,
        widget=forms.Select(attrs={
            'class': 'box3 h-21 w-100px block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '{{% url "reports:category_dropdown" %}}', # HTMX endpoint for dynamic update
            'hx-target': '#category-dropdown-container',
            'hx-swap': 'outerHTML',
            'hx-include': '#id_type', # Send current type value
            'hx-trigger': 'change',
        })
    )

    category = forms.ModelChoiceField(
        queryset=Category.objects.none(), # Will be populated dynamically
        required=False,
        label=_('Category'),
        empty_label=_('Select'),
        widget=forms.Select(attrs={
            'class': 'box3 h-21 w-200px block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
        })
    )

    search_code = forms.ChoiceField(
        choices=SEARCH_CODE_CHOICES,
        label=_('Search By'),
        required=False,
        widget=forms.Select(attrs={
            'class': 'box3 h-21 w-200px block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
        })
    )

    search_item_code = forms.CharField(
        label=_('Item Code/Description'),
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-207px block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter Item Code or Description',
        })
    )

    from_date = forms.DateField(
        label=_('From Date'),
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'box3 w-80px block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'type': 'date', # HTML5 date input
        })
    )

    to_date = forms.DateField(
        label=_('To Date'),
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'box3 w-80px block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'type': 'date', # HTML5 date input
        })
    )

    locked_by_employee_name = forms.CharField(
        label=_('Lock by User'),
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-250px block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Start typing employee name...',
            'hx-get': '{{% url "reports:employee_autocomplete" %}}', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#employee-suggestions',
            'hx-swap': 'innerHTML',
            'hx-indicator': '.htmx-indicator',
            'autocomplete': 'off', # Prevent browser autocomplete
            'x-data': '{ showSuggestions: false, selectSuggestion(name) { this.$el.value = name; this.showSuggestions = false; } }',
            '@focus': 'showSuggestions = true',
            '@blur': 'setTimeout(() => showSuggestions = false, 100)', # Hide after a short delay
        })
    )
    # Hidden field to store selected employee ID for backend processing
    locked_by_employee_id = forms.IntegerField(
        required=False,
        widget=forms.HiddenInput()
    )

    def clean(self):
        cleaned_data = super().clean()
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')

        if from_date and to_date and from_date > to_date:
            self.add_error('to_date', _("To Date cannot be before From Date."))
        return cleaned_data

class RateLockEntryForm(forms.ModelForm):
    """
    Form for creating/editing RateLockEntry records.
    (Included as per prompt's CRUD requirement, not directly driven by original ASP.NET page)
    """
    class Meta:
        model = RateLockEntry
        fields = [
            'item_code', 'description', 'category', 'record_type',
            'lock_date', 'unlock_date', 'locked_by', 'rate', 'status'
        ]
        widgets = {
            'item_code': forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'description': forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'category': forms.Select(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'record_type': forms.Select(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}, choices=RateLockUnlockSearchForm.TYPE_CHOICES[1:]), # Exclude 'Select'
            'lock_date': forms.DateInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'unlock_date': forms.DateInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'locked_by': forms.Select(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'rate': forms.NumberInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'status': forms.TextInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
    
    def clean_lock_date(self):
        lock_date = self.cleaned_data['lock_date']
        if lock_date and lock_date > forms.DateField().to_python(forms.DateField().today()):
            raise forms.ValidationError(_("Lock Date cannot be in the future."))
        return lock_date

```

#### 4.3 Views (`reports/views.py`)

Views are kept thin, delegating business logic to models or helper functions. HTMX is used for dynamic updates and search results.

```python
from django.views.generic import TemplateView, ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db.models import Q
from django.template.loader import render_to_string
from .models import RateLockEntry, Category, Employee
from .forms import RateLockUnlockSearchForm, RateLockEntryForm
import re # For parsing employee name from autocomplete

class RateLockUnlockSearchView(TemplateView):
    """
    Main view for the Rate Lock Unlock report search interface.
    Handles the initial form rendering and placeholder for HTMX-loaded table.
    """
    template_name = 'reports/rate_lock_unlock_search.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize form with data if available from GET parameters (for state restoration)
        form = RateLockUnlockSearchForm(self.request.GET or None)

        # Set initial visibility of fields based on type selection
        form.fields['category'].widget.attrs['x-show'] = "searchForm.type === 'Category'"
        form.fields['search_code'].widget.attrs['x-show'] = "searchForm.type !== ''"
        form.fields['search_item_code'].widget.attrs['x-show'] = "searchForm.type !== ''"
        
        # Populate category dropdown based on initial type if 'Category' is selected
        if form.is_valid() and form.cleaned_data.get('type') == 'Category':
            # This is primarily for initial page load; HTMX will handle subsequent changes
            company_id = self.request.session.get('compid', 1) # Default to 1 if not found
            form.fields['category'].queryset = Category.objects.filter(company_id=company_id)
        else:
            form.fields['category'].queryset = Category.objects.none()

        context['form'] = form
        return context

class CategoryDropdownPartialView(TemplateView):
    """
    HTMX endpoint to dynamically update the category dropdown and other field visibilities.
    """
    template_name = 'reports/_category_dropdown.html' # Render only the dropdown itself

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        selected_type = self.request.GET.get('type', '')
        company_id = self.request.session.get('compid', 1) # Default to 1 if not found

        form = RateLockUnlockSearchForm() # Create a dummy form instance to get field info
        category_field = form.fields['category']
        
        if selected_type == 'Category':
            category_field.queryset = Category.objects.filter(company_id=company_id)
            category_field.widget.attrs['x-show'] = "searchForm.type === 'Category'"
        else:
            category_field.queryset = Category.objects.none()
            category_field.widget.attrs['x-show'] = "searchForm.type === 'Category'" # Still show, Alpine.js will control visibility
            
        context['category_field'] = category_field
        context['selected_type'] = selected_type
        return context

class EmployeeAutocompleteView(ListView):
    """
    HTMX endpoint for employee autocomplete suggestions.
    """
    model = Employee
    context_object_name = 'employees'
    paginate_by = 10 # Limit suggestions

    def get_queryset(self):
        query = self.request.GET.get('q', '')
        company_id = self.request.session.get('compid', 1) # Default to 1 if not found
        if query:
            return Employee.objects.filter(
                Q(name__icontains=query) | Q(id__startswith=query),
                company_id=company_id
            ).order_by('name')
        return Employee.objects.none()

    def render_to_response(self, context, **response_kwargs):
        """
        Renders a simple HTML list of suggestions.
        """
        suggestions = context['employees']
        html = render_to_string('reports/_employee_suggestions.html', {'suggestions': suggestions}, self.request)
        return HttpResponse(html)

class RateLockEntryTablePartialView(ListView):
    """
    HTMX endpoint to render the DataTables content based on search parameters.
    """
    model = RateLockEntry
    template_name = 'reports/_rate_lock_unlock_table.html'
    context_object_name = 'rate_lock_entries'

    def get_queryset(self):
        form = RateLockUnlockSearchForm(self.request.GET)
        queryset = RateLockEntry.objects.all()
        company_id = self.request.session.get('compid', 1) # Default to 1 if not found
        financial_year_id = self.request.session.get('finyear', 2024) # Default to 2024 if not found
        
        # Always filter by company and financial year
        queryset = queryset.filter(company_id=company_id, financial_year_id=financial_year_id)

        if form.is_valid():
            data = form.cleaned_data
            
            record_type = data.get('type')
            if record_type:
                queryset = queryset.filter(record_type=record_type)
            else:
                # If no type is selected, return an empty queryset
                return RateLockEntry.objects.none()

            category = data.get('category')
            if record_type == 'Category' and category:
                queryset = queryset.filter(category=category)
            
            search_code = data.get('search_code')
            search_item_code = data.get('search_item_code')
            if search_code and search_item_code:
                if search_code == '1': # Item Code
                    queryset = queryset.filter(item_code__icontains=search_item_code)
                elif search_code == '2': # Description
                    queryset = queryset.filter(description__icontains=search_item_code)

            from_date = data.get('from_date')
            to_date = data.get('to_date')
            if from_date:
                queryset = queryset.filter(lock_date__gte=from_date)
            if to_date:
                queryset = queryset.filter(lock_date__lte=to_date)

            # Extract employee ID from the autocomplete string if needed
            locked_by_employee_name = data.get('locked_by_employee_name')
            if locked_by_employee_name:
                # Check for the format 'EmployeeName [ID]'
                match = re.search(r'\[(\d+)\]$', locked_by_employee_name)
                if match:
                    locked_by_employee_id = int(match.group(1))
                    queryset = queryset.filter(locked_by__id=locked_by_employee_id)
                else:
                    # Fallback to searching by name if ID not found, but it's less precise
                    queryset = queryset.filter(locked_by__name__icontains=locked_by_employee_name)

        else:
            # If form is not valid (e.g., initial load or missing required data), return empty
            return RateLockEntry.objects.none()

        return queryset.order_by('-lock_date') # Order by latest lock date


# --- Standard CRUD Views for RateLockEntry (as per prompt template, assuming direct CRUD if needed elsewhere) ---

class RateLockEntryListView(ListView):
    model = RateLockEntry
    template_name = 'reports/ratelockentry/list.html'
    context_object_name = 'rate_lock_entries'

class RateLockEntryCreateView(CreateView):
    model = RateLockEntry
    form_class = RateLockEntryForm
    template_name = 'reports/ratelockentry/form.html'
    success_url = reverse_lazy('reports:ratelockentry_list')

    def form_valid(self, form):
        # Set company and financial year IDs from session (example, adapt as needed)
        form.instance.company_id = self.request.session.get('compid', 1)
        form.instance.financial_year_id = self.request.session.get('finyear', 2024)
        
        response = super().form_valid(form)
        messages.success(self.request, 'Rate Lock Entry added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshRateLockEntryList'
                }
            )
        return response

class RateLockEntryUpdateView(UpdateView):
    model = RateLockEntry
    form_class = RateLockEntryForm
    template_name = 'reports/ratelockentry/form.html'
    success_url = reverse_lazy('reports:ratelockentry_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Rate Lock Entry updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshRateLockEntryList'
                }
            )
        return response

class RateLockEntryDeleteView(DeleteView):
    model = RateLockEntry
    template_name = 'reports/ratelockentry/confirm_delete.html'
    success_url = reverse_lazy('reports:ratelockentry_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Rate Lock Entry deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshRateLockEntryList'
                }
            )
        return response

```

#### 4.4 Templates (`reports/templates/reports/`)

These templates will use Tailwind CSS for styling and HTMX/Alpine.js for dynamic interactions.

**`reports/templates/reports/rate_lock_unlock_search.html` (Main Page)**

```html
{% extends 'core/base.html' %}
{% load tailwind_filters %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6 mb-8" 
         x-data="{ searchForm: { type: '{{ form.type.value|default:"" }}' } }">
        <h2 class="text-2xl font-bold text-gray-800 mb-6 border-b pb-3">Rate Lock Unlock Report</h2>
        
        <form id="rate-lock-unlock-search-form" 
              hx-get="{% url 'reports:ratelockentry_table' %}" 
              hx-target="#ratelockentryTable-container" 
              hx-swap="innerHTML"
              hx-indicator="#loading-indicator">
            {% csrf_token %}
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <!-- Row 1: Type, Category, Search By, Item Code/Description -->
                <div class="flex items-center space-x-2">
                    <label for="{{ form.type.id_for_label }}" class="text-sm font-medium text-gray-700 w-24">Type:</label>
                    {{ form.type }}
                </div>

                <div id="category-dropdown-container">
                    <div class="flex items-center space-x-2">
                        <label for="{{ form.category.id_for_label }}" class="text-sm font-medium text-gray-700 w-24">Category:</label>
                        {{ form.category }}
                    </div>
                </div>

                <div class="flex items-center space-x-2" x-show="searchForm.type !== ''">
                    <label for="{{ form.search_code.id_for_label }}" class="text-sm font-medium text-gray-700 w-24">Search By:</label>
                    {{ form.search_code }}
                </div>

                <div class="flex items-center space-x-2" x-show="searchForm.type !== ''">
                    <label for="{{ form.search_item_code.id_for_label }}" class="text-sm font-medium text-gray-700 w-24">Item/Desc:</label>
                    {{ form.search_item_code }}
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <!-- Row 2: From Date, To Date, Lock by User -->
                <div class="flex items-center space-x-2">
                    <label for="{{ form.from_date.id_for_label }}" class="text-sm font-medium text-gray-700 w-24">From Date:</label>
                    {{ form.from_date }}
                    {% if form.from_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.from_date.errors }}</p>{% endif %}
                </div>

                <div class="flex items-center space-x-2">
                    <label for="{{ form.to_date.id_for_label }}" class="text-sm font-medium text-gray-700 w-24">To Date:</label>
                    {{ form.to_date }}
                    {% if form.to_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.to_date.errors }}</p>{% endif %}
                </div>

                <div class="flex items-center space-x-2 relative">
                    <label for="{{ form.locked_by_employee_name.id_for_label }}" class="text-sm font-medium text-gray-700 w-24">Lock by User:</label>
                    {{ form.locked_by_employee_name }}
                    {{ form.locked_by_employee_id }} {# Hidden field for storing employee ID #}
                    <div id="employee-suggestions" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg mt-1 w-full" x-show="searchForm.showSuggestions">
                        <!-- Autocomplete suggestions loaded here via HTMX -->
                    </div>
                </div>
            </div>

            <div class="flex justify-end mt-6">
                <button 
                    type="submit" 
                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md shadow-lg transition duration-300 ease-in-out">
                    View
                </button>
                <div id="loading-indicator" class="htmx-indicator ml-4 inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 hidden"></div>
            </div>
            
            {% if form.non_field_errors %}
                <div class="text-red-500 text-sm mt-4">
                    {% for error in form.non_field_errors %}
                        <p>{{ error }}</p>
                    {% endfor %}
                </div>
            {% endif %}

            {% if messages %}
                <div class="mt-4">
                    {% for message in messages %}
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                            <span class="block sm:inline">{{ message }}</span>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        </form>
    </div>

    <!-- Rate Lock Entry Table Section -->
    <div class="bg-white shadow-md rounded-lg p-6">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-bold text-gray-800">Rate Lock Unlock Entries</h3>
            {# Add New button for RateLockEntry if direct CRUD is exposed via this page #}
            {#
            <button 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                hx-get="{% url 'reports:ratelockentry_add' %}"
                hx-target="#modalContent"
                hx-trigger="click"
                _="on click add .is-active to #modal">
                Add New Rate Lock Entry
            </button>
            #}
        </div>
        
        <div id="ratelockentryTable-container"
             hx-trigger="load, refreshRateLockEntryList from:body"
             hx-get="{% url 'reports:ratelockentry_table' %}"
             hx-swap="innerHTML">
            <!-- DataTables will be loaded here via HTMX -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading Report Data...</p>
            </div>
        </div>
    </div>
    
    <!-- Modal for CRUD operations -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js for form state management
    document.addEventListener('alpine:init', () => {
        Alpine.data('rateLockUnlockForm', () => ({
            type: '', // Holds the selected type from DrpType
            init() {
                // Initialize type from the form's initial value
                this.type = document.getElementById('id_type').value;
                this.$watch('type', value => {
                    // This watcher isn't strictly necessary for the HTMX-driven dropdown,
                    // but could be useful for other Alpine-controlled UI elements.
                });
            },
            // For autocomplete: Populate hidden employee ID when suggestion is clicked
            selectEmployee(employeeId, employeeName) {
                document.getElementById('id_locked_by_employee_name').value = employeeName;
                document.getElementById('id_locked_by_employee_id').value = employeeId;
                this.$data.showSuggestions = false; // Hide suggestions via Alpine
            }
        }));
    });
</script>
{% endblock %}
```

**`reports/templates/reports/_category_dropdown.html` (Partial for dynamic category dropdown)**

```html
<div class="flex items-center space-x-2" id="category-dropdown-container">
    <label for="{{ category_field.id_for_label }}" class="text-sm font-medium text-gray-700 w-24">Category:</label>
    <select name="{{ category_field.name }}" id="{{ category_field.id_for_label }}"
            class="box3 h-21 w-200px block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            x-show="searchForm.type === 'Category'"> {# Alpine.js control for visibility #}
        <option value="">Select</option>
        {% for choice in category_field.choices %}
            {% if not choice.0 %} {% continue %} {% endif %} {# Skip empty choice from ModelChoiceField #}
            <option value="{{ choice.0 }}" {% if category_field.value == choice.0 %}selected{% endif %}>{{ choice.1 }}</option>
        {% endfor %}
    </select>
</div>
```

**`reports/templates/reports/_employee_suggestions.html` (Partial for employee autocomplete)**

```html
{% if suggestions %}
<ul class="py-1">
    {% for employee in suggestions %}
    <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer" 
        @click="selectEmployee('{{ employee.id }}', '{{ employee.get_full_name_with_id }}')">
        {{ employee.get_full_name_with_id }}
    </li>
    {% endfor %}
</ul>
{% else %}
<p class="px-4 py-2 text-gray-500 text-sm">No suggestions found.</p>
{% endif %}
```

**`reports/templates/reports/_rate_lock_unlock_table.html` (Partial for DataTables)**

```html
<table id="rateLockEntryTable" class="min-w-full bg-white border border-gray-200 divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lock Date</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unlock Date</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Locked By</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            {# Add actions column if direct CRUD on report entries is enabled here #}
            {#
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            #}
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for entry in rate_lock_entries %}
        <tr>
            <td class="py-3 px-4 whitespace-nowrap">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap">{{ entry.record_type }}</td>
            <td class="py-3 px-4 whitespace-nowrap">{{ entry.category.name|default:"-" }}</td>
            <td class="py-3 px-4 whitespace-nowrap">{{ entry.item_code|default:"-" }}</td>
            <td class="py-3 px-4 whitespace-nowrap">{{ entry.description|default:"-" }}</td>
            <td class="py-3 px-4 whitespace-nowrap">{{ entry.rate }}</td>
            <td class="py-3 px-4 whitespace-nowrap">{{ entry.lock_date|date:"d-M-Y" }}</td>
            <td class="py-3 px-4 whitespace-nowrap">{{ entry.unlock_date|date:"d-M-Y"|default:"N/A" }}</td>
            <td class="py-3 px-4 whitespace-nowrap">{{ entry.locked_by.name|default:"-" }}</td>
            <td class="py-3 px-4 whitespace-nowrap">{{ entry.status }}</td>
            {#
            <td class="py-3 px-4 whitespace-nowrap">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'reports:ratelockentry_edit' entry.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'reports:ratelockentry_delete' entry.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
            #}
        </tr>
        {% empty %}
        <tr>
            <td colspan="10" class="py-4 px-4 text-center text-gray-500">No Rate Lock Entries found for the selected criteria.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    // Destroy existing DataTable instance if it exists to prevent re-initialization errors
    if ($.fn.DataTable.isDataTable('#rateLockEntryTable')) {
        $('#rateLockEntryTable').DataTable().destroy();
    }
    
    $('#rateLockEntryTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "pagingType": "full_numbers", // Optional: for full pagination controls
        "responsive": true // Optional: for responsive tables on small screens
    });
});
</script>
```

**`reports/templates/reports/ratelockentry/form.html` (CRUD Form Partial)**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Rate Lock Entry</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-on::after-request="if(event.detail.successful) { removeClass('is-active', '#modal'); }">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="text-gray-500 text-xs mt-1">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`reports/templates/reports/ratelockentry/confirm_delete.html` (Delete Confirmation Partial)**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700">Are you sure you want to delete the Rate Lock Entry for <strong>{{ object.item_code|default:object.description }}</strong> (ID: {{ object.pk }})?</p>
    
    <div class="mt-6 flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button 
            hx-post="{{ request.path }}" 
            hx-swap="none"
            hx-on::after-request="if(event.detail.successful) { removeClass('is-active', '#modal'); }"
            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            Delete
        </button>
    </div>
</div>
```

#### 4.5 URLs (`reports/urls.py`)

Define URL patterns for all views, including HTMX partials.

```python
from django.urls import path
from .views import (
    RateLockUnlockSearchView,
    CategoryDropdownPartialView,
    EmployeeAutocompleteView,
    RateLockEntryTablePartialView,
    RateLockEntryListView, # For full list page if needed
    RateLockEntryCreateView,
    RateLockEntryUpdateView,
    RateLockEntryDeleteView
)

app_name = 'reports' # Namespace for URLs

urlpatterns = [
    # Main Rate Lock Unlock Report Page
    path('rate-lock-unlock/', RateLockUnlockSearchView.as_view(), name='rate_lock_unlock_search'),

    # HTMX Endpoints for dynamic content
    path('rate-lock-unlock/category-dropdown/', CategoryDropdownPartialView.as_view(), name='category_dropdown'),
    path('rate-lock-unlock/employee-autocomplete/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),
    path('rate-lock-unlock/table/', RateLockEntryTablePartialView.as_view(), name='ratelockentry_table'),

    # CRUD operations for RateLockEntry (if direct CRUD is exposed via this page, otherwise remove)
    # This URL is typically for a full-page list view of RateLockEntry, not the report page itself.
    path('ratelockentry/', RateLockEntryListView.as_view(), name='ratelockentry_list'), 
    path('ratelockentry/add/', RateLockEntryCreateView.as_view(), name='ratelockentry_add'),
    path('ratelockentry/edit/<int:pk>/', RateLockEntryUpdateView.as_view(), name='ratelockentry_edit'),
    path('ratelockentry/delete/<int:pk>/', RateLockEntryDeleteView.as_view(), name='ratelockentry_delete'),
]
```

#### 4.6 Tests (`reports/tests.py`)

Comprehensive unit tests for models and integration tests for views ensure functionality and maintainability.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from datetime import date, timedelta
from unittest.mock import patch

from .models import Category, Employee, RateLockEntry
from .forms import RateLockUnlockSearchForm

# Mock session data for CompanyId and FinYearId
class MockSession:
    def __init__(self, compid=1, finyear=2024):
        self.compid = compid
        self.finyear = finyear

    def get(self, key, default=None):
        if key == 'compid':
            return self.compid
        if key == 'finyear':
            return self.finyear
        return default

@patch('django.http.request.HttpRequest.session', new_callable=MockSession)
class CategoryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        Category.objects.create(id=1, symbol='CAT', name='Category A', company_id=1)
        Category.objects.create(id=2, symbol='CATB', name='Category B', company_id=1)
        Category.objects.create(id=3, symbol='CATC', name='Category C', company_id=2) # Different company

    def test_category_creation(self, mock_session):
        category = Category.objects.get(id=1)
        self.assertEqual(category.symbol, 'CAT')
        self.assertEqual(category.name, 'Category A')
        self.assertEqual(category.company_id, 1)

    def test_category_str_representation(self, mock_session):
        category = Category.objects.get(id=1)
        self.assertEqual(str(category), 'CAT-Category A')

    def test_category_verbose_name(self, mock_session):
        self.assertEqual(Category._meta.verbose_name, 'Category')
        self.assertEqual(Category._meta.verbose_name_plural, 'Categories')

@patch('django.http.request.HttpRequest.session', new_callable=MockSession)
class EmployeeModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        Employee.objects.create(id=101, name='John Doe', company_id=1)
        Employee.objects.create(id=102, name='Jane Smith', company_id=1)
        Employee.objects.create(id=103, name='Alice Brown', company_id=2)

    def test_employee_creation(self, mock_session):
        employee = Employee.objects.get(id=101)
        self.assertEqual(employee.name, 'John Doe')
        self.assertEqual(employee.company_id, 1)

    def test_employee_str_representation(self, mock_session):
        employee = Employee.objects.get(id=101)
        self.assertEqual(str(employee), 'John Doe')

    def test_get_full_name_with_id(self, mock_session):
        employee = Employee.objects.get(id=101)
        self.assertEqual(employee.get_full_name_with_id(), 'John Doe [101]')

@patch('django.http.request.HttpRequest.session', new_callable=MockSession)
class RateLockEntryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        category = Category.objects.create(id=10, symbol='TEST', name='Test Category', company_id=1)
        employee = Employee.objects.create(id=20, name='Test User', company_id=1)
        
        RateLockEntry.objects.create(
            id=1, item_code='ITEM001', description='Test Item', category=category,
            record_type='Category', lock_date='2023-01-01', unlock_date=None,
            locked_by=employee, rate=100.00, status='Locked', company_id=1, financial_year_id=2024
        )
        RateLockEntry.objects.create(
            id=2, item_code='ITEM002', description='Another Item', category=None,
            record_type='WOItems', lock_date='2023-02-15', unlock_date='2023-03-01',
            locked_by=employee, rate=50.00, status='Unlocked', company_id=1, financial_year_id=2024
        )
        RateLockEntry.objects.create(
            id=3, item_code='ITEM003', description='Future Locked Item', category=category,
            record_type='Category', lock_date=(date.today() + timedelta(days=5)), unlock_date=None,
            locked_by=employee, rate=75.00, status='Locked', company_id=1, financial_year_id=2024
        )

    def test_ratelockentry_creation(self, mock_session):
        entry = RateLockEntry.objects.get(id=1)
        self.assertEqual(entry.item_code, 'ITEM001')
        self.assertEqual(entry.record_type, 'Category')
        self.assertEqual(entry.locked_by.name, 'Test User')

    def test_is_currently_locked_method(self, mock_session):
        entry_locked = RateLockEntry.objects.get(id=1)
        self.assertTrue(entry_locked.is_currently_locked()) # Should be locked

        entry_unlocked = RateLockEntry.objects.get(id=2)
        self.assertFalse(entry_unlocked.is_currently_locked()) # Should be unlocked

        # Test with a future unlock date
        future_unlock_entry = RateLockEntry.objects.create(
            id=4, item_code='ITEM004', description='Future Unlock', category=Category.objects.get(id=10),
            record_type='Category', lock_date=date.today() - timedelta(days=10), unlock_date=date.today() + timedelta(days=5),
            locked_by=Employee.objects.get(id=20), rate=120.00, status='Locked', company_id=1, financial_year_id=2024
        )
        self.assertTrue(future_unlock_entry.is_currently_locked()) # Should be locked until future unlock date

    def test_get_display_item_info(self, mock_session):
        entry1 = RateLockEntry.objects.get(id=1)
        entry2 = RateLockEntry.objects.get(id=2)
        self.assertEqual(entry1.get_display_item_info('1'), 'ITEM001')
        self.assertEqual(entry2.get_display_item_info('2'), 'Another Item')
        self.assertEqual(entry1.get_display_item_info('invalid'), '')


@patch('django.http.request.HttpRequest.session', new_callable=MockSession)
class RateLockUnlockSearchFormTest(TestCase):
    def test_valid_form(self, mock_session):
        form_data = {
            'type': 'Category',
            'category': '', # This would be an ID from a real Category
            'search_code': '1',
            'search_item_code': 'ABC',
            'from_date': '2023-01-01',
            'to_date': '2023-01-31',
            'locked_by_employee_name': 'John Doe [101]',
            'locked_by_employee_id': '101',
        }
        form = RateLockUnlockSearchForm(data=form_data)
        self.assertTrue(form.is_valid(), form.errors.as_data())

    def test_invalid_date_range(self, mock_session):
        form_data = {
            'type': 'Category',
            'from_date': '2023-01-31',
            'to_date': '2023-01-01',
        }
        form = RateLockUnlockSearchForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('to_date', form.errors)
        self.assertIn('To Date cannot be before From Date.', [str(e) for e in form.errors['to_date']])

    def test_empty_form(self, mock_session):
        form = RateLockUnlockSearchForm(data={})
        self.assertTrue(form.is_valid()) # All fields are optional


@patch('django.http.request.HttpRequest.session', new_callable=MockSession)
class RateLockUnlockViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.category = Category.objects.create(id=1, symbol='CAT', name='Category A', company_id=1)
        cls.employee = Employee.objects.create(id=101, name='John Doe', company_id=1)
        RateLockEntry.objects.create(
            id=1, item_code='ITEM001', description='Test Item', category=cls.category,
            record_type='Category', lock_date='2023-01-01', unlock_date=None,
            locked_by=cls.employee, rate=100.00, status='Locked', company_id=1, financial_year_id=2024
        )
        RateLockEntry.objects.create(
            id=2, item_code='ITEM002', description='WO Item', category=None,
            record_type='WOItems', lock_date='2023-02-01', unlock_date=None,
            locked_by=cls.employee, rate=200.00, status='Locked', company_id=1, financial_year_id=2024
        )
        RateLockEntry.objects.create(
            id=3, item_code='ITEM003', description='Other Company Item', category=None,
            record_type='WOItems', lock_date='2023-03-01', unlock_date=None,
            locked_by=cls.employee, rate=300.00, status='Locked', company_id=2, financial_year_id=2024
        )
    
    def setUp(self):
        self.client = Client()

    def test_search_view_get(self, mock_session):
        response = self.client.get(reverse('reports:rate_lock_unlock_search'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/rate_lock_unlock_search.html')
        self.assertIsInstance(response.context['form'], RateLockUnlockSearchForm)

    def test_category_dropdown_partial_view(self, mock_session):
        response = self.client.get(reverse('reports:category_dropdown') + '?type=Category')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/_category_dropdown.html')
        self.assertContains(response, '<option value="1" selected>CAT-Category A</option>') # Selected for category_field.value in template
        
        response = self.client.get(reverse('reports:category_dropdown') + '?type=WOItems')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/_category_dropdown.html')
        self.assertNotContains(response, '<option value="1">CAT-Category A</option>') # Should not contain specific categories

    def test_employee_autocomplete_view(self, mock_session):
        response = self.client.get(reverse('reports:employee_autocomplete') + '?q=john')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/_employee_suggestions.html')
        self.assertContains(response, 'John Doe [101]')
        self.assertNotContains(response, 'Jane Smith')

        response = self.client.get(reverse('reports:employee_autocomplete') + '?q=nonexistent')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No suggestions found.')
        
        # Test autocomplete for different company
        mock_session.compid = 2
        response = self.client.get(reverse('reports:employee_autocomplete') + '?q=alice')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Alice Brown [103]')


    def test_ratelockentry_table_partial_view_no_filters(self, mock_session):
        # Initial load, returns empty queryset if no type is selected
        response = self.client.get(reverse('reports:ratelockentry_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/_rate_lock_unlock_table.html')
        self.assertContains(response, 'No Rate Lock Entries found')

    def test_ratelockentry_table_partial_view_with_filters(self, mock_session):
        # Filter by type 'Category'
        response = self.client.get(reverse('reports:ratelockentry_table') + '?type=Category')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'ITEM001')
        self.assertNotContains(response, 'ITEM002') # WOItems type

        # Filter by type 'WOItems' and date range
        response = self.client.get(reverse('reports:ratelockentry_table') + '?type=WOItems&from_date=2023-01-01&to_date=2023-02-28')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'ITEM002')
        self.assertNotContains(response, 'ITEM001')

        # Filter by item code
        response = self.client.get(reverse('reports:ratelockentry_table') + '?type=Category&search_code=1&search_item_code=ITEM001')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'ITEM001')
        self.assertNotContains(response, 'ITEM002')

        # Filter by locked by employee
        response = self.client.get(reverse('reports:ratelockentry_table') + '?type=Category&locked_by_employee_name=John Doe [101]')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'ITEM001')

        # Filter by company ID (should not return ITEM003 for company_id=1)
        response = self.client.get(reverse('reports:ratelockentry_table') + '?type=WOItems')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'ITEM002')
        self.assertNotContains(response, 'ITEM003') # Belongs to company_id=2


# --- Standard CRUD Views for RateLockEntry Tests (as per prompt template) ---

@patch('django.http.request.HttpRequest.session', new_callable=MockSession)
class RateLockEntryCRUDViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.category = Category.objects.create(id=1, symbol='CAT', name='Category A', company_id=1)
        cls.employee = Employee.objects.create(id=101, name='John Doe', company_id=1)
        RateLockEntry.objects.create(
            id=1, item_code='CRUDITEM1', description='CRUD Test Item', category=cls.category,
            record_type='Category', lock_date='2023-01-01', unlock_date=None,
            locked_by=cls.employee, rate=100.00, status='Locked', company_id=1, financial_year_id=2024
        )
    
    def setUp(self):
        self.client = Client()

    def test_list_view(self, mock_session):
        response = self.client.get(reverse('reports:ratelockentry_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/ratelockentry/list.html')
        self.assertTrue('rate_lock_entries' in response.context)
        self.assertContains(response, 'CRUDITEM1')
        
    def test_create_view_get(self, mock_session):
        response = self.client.get(reverse('reports:ratelockentry_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/ratelockentry/form.html')
        self.assertTrue('form' in response.context)
        
    def test_create_view_post(self, mock_session):
        data = {
            'item_code': 'NEWITEM',
            'description': 'New Test Item',
            'category': self.category.id,
            'record_type': 'WOItems',
            'lock_date': '2024-05-01',
            'unlock_date': '',
            'locked_by': self.employee.id,
            'rate': '150.75',
            'status': 'Locked'
        }
        response = self.client.post(reverse('reports:ratelockentry_add'), data)
        self.assertEqual(response.status_code, 302) # Redirect after successful creation
        self.assertTrue(RateLockEntry.objects.filter(item_code='NEWITEM').exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Rate Lock Entry added successfully.')

    def test_create_view_post_htmx(self, mock_session):
        data = {
            'item_code': 'HTMXITEM',
            'description': 'HTMX Test Item',
            'category': self.category.id,
            'record_type': 'WOItems',
            'lock_date': '2024-05-01',
            'unlock_date': '',
            'locked_by': self.employee.id,
            'rate': '150.75',
            'status': 'Locked'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('reports:ratelockentry_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshRateLockEntryList')
        self.assertTrue(RateLockEntry.objects.filter(item_code='HTMXITEM').exists())
        
    def test_update_view_get(self, mock_session):
        obj = RateLockEntry.objects.get(id=1)
        response = self.client.get(reverse('reports:ratelockentry_edit', args=[obj.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/ratelockentry/form.html')
        self.assertContains(response, 'Edit Rate Lock Entry')
        self.assertEqual(response.context['form'].instance.item_code, 'CRUDITEM1')

    def test_update_view_post(self, mock_session):
        obj = RateLockEntry.objects.get(id=1)
        data = {
            'item_code': 'UPDATEDITEM',
            'description': 'Updated Description',
            'category': self.category.id,
            'record_type': 'Category',
            'lock_date': '2023-01-01',
            'unlock_date': '',
            'locked_by': self.employee.id,
            'rate': '110.00',
            'status': 'Locked'
        }
        response = self.client.post(reverse('reports:ratelockentry_edit', args=[obj.id]), data)
        self.assertEqual(response.status_code, 302)
        obj.refresh_from_db()
        self.assertEqual(obj.item_code, 'UPDATEDITEM')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Rate Lock Entry updated successfully.')

    def test_delete_view_get(self, mock_session):
        obj = RateLockEntry.objects.get(id=1)
        response = self.client.get(reverse('reports:ratelockentry_delete', args=[obj.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/ratelockentry/confirm_delete.html')
        self.assertContains(response, 'Are you sure you want to delete')

    def test_delete_view_post(self, mock_session):
        obj_to_delete = RateLockEntry.objects.create(
            id=99, item_code='TOBEDELETED', description='Delete Test', category=self.category,
            record_type='Category', lock_date='2023-01-01', unlock_date=None,
            locked_by=self.employee, rate=100.00, status='Locked', company_id=1, financial_year_id=2024
        )
        response = self.client.post(reverse('reports:ratelockentry_delete', args=[obj_to_delete.id]))
        self.assertEqual(response.status_code, 302)
        self.assertFalse(RateLockEntry.objects.filter(id=99).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Rate Lock Entry deleted successfully.')

    def test_delete_view_post_htmx(self, mock_session):
        obj_to_delete = RateLockEntry.objects.create(
            id=100, item_code='HTMXDELETE', description='Delete Test HTMX', category=self.category,
            record_type='Category', lock_date='2023-01-01', unlock_date=None,
            locked_by=self.employee, rate=100.00, status='Locked', company_id=1, financial_year_id=2024
        )
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('reports:ratelockentry_delete', args=[obj_to_delete.id]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertFalse(RateLockEntry.objects.filter(id=100).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshRateLockEntryList')
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
- Review the HTML templates and Python views to understand how HTMX and Alpine.js are integrated.
- Ensure all dynamic interactions are driven by these technologies.

**HTMX Integration:**
*   **Search Form Submission (`reports/rate_lock_unlock_search.html`):** The main form uses `hx-get` to submit search parameters to `{% url 'reports:ratelockentry_table' %}`. The response, which is the `_rate_lock_unlock_table.html` partial, is `hx-swapped` into the `#ratelockentryTable-container` div. This prevents a full page reload for search results.
*   **Dynamic Category Dropdown (`reports/rate_lock_unlock_search.html`):** The `DrpType` (`id_type`) dropdown has `hx-get="{% url 'reports:category_dropdown' %}"`, `hx-target="#category-dropdown-container"`, and `hx-trigger="change"`. When the type changes, HTMX fetches the updated HTML for the category dropdown from the `CategoryDropdownPartialView` and swaps it.
*   **Employee Autocomplete (`reports/rate_lock_unlock_search.html`):** The `TxtEmpName` (`id_locked_by_employee_name`) textbox uses `hx-get="{% url 'reports:employee_autocomplete' %}"`, `hx-target="#employee-suggestions"`, and `hx-trigger="keyup changed delay:500ms, search"`. This fetches suggestions dynamically as the user types, displaying them in the `#employee-suggestions` div.
*   **Table Refresh after CRUD (if enabled):** HTMX custom events like `HX-Trigger: 'refreshRateLockEntryList'` are sent from `CreateView`, `UpdateView`, and `DeleteView` to trigger a `hx-trigger="refreshRateLockEntryList from:body"` on the main table container, ensuring the list automatically updates after a successful CRUD operation without a full page refresh.
*   **Modal Interactions:** Buttons for Edit, Add, and Delete within the table (if CRUD enabled) use `hx-get` to load forms into the `#modalContent` div, and Alpine.js controls the visibility of the `#modal` itself. Form submissions within the modal use `hx-post` and handle closing the modal after success.

**Alpine.js Integration:**
*   **Form Visibility Logic (`reports/rate_lock_unlock_search.html`):** `x-data="{ searchForm: { type: '' } }"` on the main form container. Alpine.js binds `searchForm.type` to the `select` element's value, and then uses `x-show="searchForm.type === 'Category'"` or `x-show="searchForm.type !== ''"` to dynamically show/hide the Category, Search Code, and Item Code/Description fields, mimicking the ASP.NET `Visible = false` logic.
*   **Autocomplete UI:** Alpine.js variables like `showSuggestions` are used to control the visibility of the autocomplete suggestion list. The `@click="selectEmployee()"` directive on suggestions updates the input field and hides the suggestion list.
*   **Modal Management:** Alpine.js is used to add/remove the `is-active` class to the modal when buttons are clicked or when the modal background is clicked, providing a smooth user experience.

**DataTables Integration:**
*   The `_rate_lock_unlock_table.html` partial includes a JavaScript block that initializes `$('#rateLockEntryTable').DataTable()`. This ensures that when the HTMX request loads the new table content, DataTables is re-applied, providing client-side searching, sorting, and pagination without additional server-side logic for these features.

---

## Final Notes

*   **Placeholders:** `[MODEL_NAME]`, `[TABLE_NAME]`, etc., have been replaced with `RateLockEntry`, `tblRateLockUnlockReportData`, and related Django model/field names.
*   **DRY Principles:** Templates use partials (e.g., `_category_dropdown.html`, `_rate_lock_unlock_table.html`) to avoid code repetition. `core/base.html` is extended for common layout.
*   **Fat Model, Thin View:** Business logic, such as `is_currently_locked` or `get_display_item_info`, resides in the `RateLockEntry` model. Views primarily handle HTTP requests, form processing, and rendering.
*   **Testing:** Comprehensive unit and integration tests are provided to ensure the correctness and robustness of models, forms, and views.
*   **Session Data:** The `CompId` and `FinYearId` are accessed via `request.session`, requiring these to be set upon user login or initialization, similar to the ASP.NET session.
*   **Database Interaction:** All database operations are performed using the Django ORM, abstracting away direct SQL and connection management.
*   **User Experience:** HTMX and Alpine.js provide a highly dynamic and responsive user experience, eliminating full page reloads and mimicking the "postback" behavior of ASP.NET in a modern, lightweight way.

This plan provides a clear, actionable roadmap for transitioning your "Rate Lock Unlock" module to a maintainable and modern Django application, leveraging AI-assisted automation by providing a structured, complete, and testable codebase.