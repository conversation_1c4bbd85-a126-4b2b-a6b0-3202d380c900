The transition from legacy ASP.NET applications to modern Django solutions offers significant improvements in maintainability, scalability, and developer productivity. By leveraging modern patterns like Django's "Fat Model, Thin View" architecture, HTMX for dynamic interactions, Alpine.js for lightweight frontend logic, and DataTables for robust data presentation, we can create a highly efficient and user-friendly system. This modernization plan is designed for AI-assisted automation, providing clear, actionable steps that minimize manual coding and streamline the migration process.

The original ASP.NET application provides a complex reporting interface where users dynamically select columns from various underlying database views (PO, PR, SPR reports) and apply extensive filters via URL parameters. This functionality is heavily reliant on dynamic SQL generation and `ViewState` management, which are common patterns in older ASP.NET but are less secure and maintainable in modern web frameworks.

Our Django modernization will address these challenges by:
1.  **Modeling Database Views:** Explicitly defining Django models for the relevant database views to provide a clear ORM interface.
2.  **Centralized Report Logic (Fat Model/Service):** Encapsulating the complex filtering and dynamic column selection logic within a dedicated `ReportService` class. This ensures "thin views" by keeping business logic separate from presentation logic.
3.  **HTMX-Driven UI:** Using HTMX to handle dynamic form submissions and table reloads, eliminating full page postbacks and providing a smooth user experience.
4.  **DataTables Integration:** Utilizing DataTables for all tabular data displays, offering out-of-the-box sorting, searching, and pagination capabilities.
5.  **Robust Filtering:** Implementing Django's filtering capabilities to safely handle the numerous query parameters, replacing the insecure dynamic SQL string concatenation.
6.  **Modern Excel Export:** Providing a proper Excel export using Python libraries, rather than rendering HTML to an `.xls` file.

This approach not only replicates the existing functionality but significantly enhances the application's performance, security, and maintainability, aligning it with current best practices in web development.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code dynamically generates SQL queries based on user selections and URL parameters, pulling data from several SQL Server views. The core data retrieval happens from:
- `View_PO_PR_SPR_Item` (when `flag` is 1, combining PO, PR, and SPR data)
- `View_PR_Item` (when `flag` is 2, for PR specific reports)
- `View_SPR_Item` (when `flag` is 3, for SPR specific reports)

The column names are dynamically selected from these views using `sp_columns` and then optionally renamed for display in the UI. For Django, we will define explicit models for each of these underlying database views. The complex filtering and dynamic column selection logic will be handled by a dedicated service layer that interacts with these models.

**Inferred Tables/Views and Core Columns:**
We identify three primary data views. Many fields are common or appear with different names across views. `CompId` is consistently used for filtering. Date fields are stored as strings in the source, requiring careful handling or conversion in Django.

1.  **`View_PO_PR_SPR_Item`**: This is the most comprehensive view, used for `flag=1` (PO Reports). It contains fields related to Purchase Orders, and also joins in related Purchase Requisition (PR) and Supplier Purchase Requisition (SPR) data.
    *   **Core Fields (Example):** `ItemCode`, `Description`, `UOM`, `StockQty`, `Date` (PO Date), `PONo`, `WONO`, `AmdNo`, `SupplierName`, `Qty`, `Rate`, `Discount`, `PO_Amount`, `DelDate`, `Remarks`, various approval/authorization fields, `Reference`, `ShipTo`, `PaymentTerms`, `ExciseSerTax`, `VAT`, `PF`, `ACHead`.
    *   **Joined PR Fields (Example):** `PRNo`, `PRDate`, `PRWO`, `PRSupplier`, `PRQty`, `PRRate`, `PRDiscount`, `PRAmount`, `PRACHead`, `PRDelDate`, `PRRemarks`.
    *   **Joined SPR Fields (Example):** `SPRNo`, `SPRDate`, `SPRWO`, `SPRSupplier`, `SPRQty`, `SPRRate`, `SPRDiscount`, `SPRAmount`, `SPRACHead`, `SPRDelDate`, `SPRRemarks`, `SPRBGGroup`, various SPR approval/authorization fields.
    *   **Filter Fields:** `CompId`, `type`, `No`, `FDate`, `TDate`, `SupId`, `Code`, `WONo`, `EX`, `accval`, `Status`.

2.  **`View_PR_Item`**: Used for `flag=2` (PR Reports).
    *   **Core Fields (Example):** `ItemCode`, `Description`, `UOM`, `StockQty`, `Date` (PR Date), `WONo`, `PRNo`, `PLNo`, `SupplierName`, `Qty`, `Rate`, `Discount`, `PR_Amount`, `AcHead`, `DelDate`, `Remarks`.
    *   **Filter Fields:** Similar to PO report, but specific to PR context.

3.  **`View_SPR_Item`**: Used for `flag=3` (SPR Reports).
    *   **Core Fields (Example):** `ItemCode`, `Description`, `UOM`, `StockQty`, `Date` (SPR Date), `SPRNo`, `WONo`, `SupplierName`, `Qty`, `Rate`, `Discount`, `SPR_Amount`, `AcHead`, `DelDate`, `Remarks`, `BG`, various SPR approval/authorization fields.
    *   **Filter Fields:** Similar to PO report, but specific to SPR context.

### Step 2: Identify Backend Functionality

**Read Operations:**
-   The primary function is to display filtered and user-selected columnar data from the database views.
-   Filters are applied based on a multitude of URL query parameters (`type`, `No`, `FDate`, `TDate`, `SupId`, `Code`, `WONo`, `EX`, `accval`, `Status`).
-   The `flag` parameter is crucial as it determines which underlying database view to query and which sets of columns are available for selection.
-   Conditional filtering also applies to "PO Complete/Pending" status.

**Export Operations:**
-   The displayed filtered data can be exported to an Excel file (`.xls`). The original implementation uses a problematic method of rendering HTML to a file.

**Absence of CRUD:**
-   No Create, Update, or Delete operations are present on this specific ASP.NET page. It is exclusively a reporting and search interface.

### Step 3: Infer UI Components

-   **Report Type Selection:** Implicitly handled by the `type` and `RAd` query string parameters, which dictate the visible panels and labels.
-   **Column Selection Checkboxes:** Three distinct `asp:CheckBoxList` controls (`chkFields`, `chkFields2`, `chkFields3`) which dynamically retrieve their options from the database schema and have hardcoded text remapping logic. A "Check All" checkbox toggles all selections.
-   **Dynamic Labels:** `asp:Label` controls (`lblPOPRSPR`, `lblSPR`, `lblPR`) whose text changes based on the report type (`flag`).
-   **Form Submission:** An `asp:Button` labeled "Show" triggers the data retrieval and display.
-   **Data Export:** An `asp:Button` labeled "Export To Excel" initiates the data export.
-   **Cancellation/Navigation:** An `asp:Button` labeled "Cancel" redirects the user to a different page (`Search.aspx`).
-   **Data Display Grid:** An `asp:GridView` (`GridView1`) dynamically populates with the selected columns and the retrieved data. Custom JavaScript was used for grid styling, which will be replaced by DataTables.
-   **Conditional Panels:** `asp:Panel` controls (`Panel2`, `Panel3`, `Panel4`) control the visibility of checkbox groups based on the report type.
-   **URL Parameters:** The application state (report type, filters) is largely driven by URL query string parameters.

---

### Step 4: Generate Django Code

We will create a Django application named `material_reports`.

#### 4.1 Models (`material_reports/models.py`)

To adhere to the "fat model" principle, we define Django models that map to the underlying database views. These models serve as the schema for Django's ORM, allowing us to query the views as if they were regular tables. The complex logic for dynamic column selection and filtering based on report types will be handled in a dedicated `ReportService` module, ensuring that our views remain thin.

The `get_report_columns_metadata` utility function replaces the ASP.NET's `sp_columns` and manual label renaming, providing a structured way to define available columns and their display names for each report type.

```python
# material_reports/models.py
from django.db import models

# Define a base class for common report view characteristics
class BaseReportModel(models.Model):
    # Common fields that might appear across different views
    item_code = models.CharField(db_column='ItemCode', max_length=255, blank=True, null=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)
    uom = models.CharField(db_column='UOM', max_length=50, blank=True, null=True)
    stock_qty = models.DecimalField(db_column='StockQty', max_digits=18, decimal_places=4, blank=True, null=True)
    report_date = models.CharField(db_column='Date', max_length=50, blank=True, null=True) # Stored as string in source
    supplier_name = models.CharField(db_column='SupplierName', max_length=255, blank=True, null=True)
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=4, blank=True, null=True)
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=4, blank=True, null=True)
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=4, blank=True, null=True)
    ac_head = models.CharField(db_column='AcHead', max_length=100, blank=True, null=True)
    delivery_date = models.CharField(db_column='DelDate', max_length=50, blank=True, null=True) # Stored as string in source
    remarks = models.CharField(db_column='Remarks', max_length=500, blank=True, null=True)
    company_id = models.IntegerField(db_column='CompId', blank=True, null=True) # Used for filtering

    class Meta:
        abstract = True # This is not a concrete model, it's a base for other models

    def __str__(self):
        return f"{self.item_code or 'N/A'} - {self.description or 'N/A'}"

# Model for View_PO_PR_SPR_Item (flag = 1)
class PurchaseOrderReportItem(BaseReportModel):
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    wo_no_po = models.CharField(db_column='WONO', max_length=50, blank=True, null=True) # Specific WO No for PO context
    amendment_no = models.CharField(db_column='AmdNo', max_length=50, blank=True, null=True)
    po_amount = models.DecimalField(db_column='PO_Amount', max_digits=18, decimal_places=4, blank=True, null=True)
    checked_po = models.CharField(db_column='Checked', max_length=50, blank=True, null=True)
    checked_by_po = models.CharField(db_column='CheckedBy', max_length=100, blank=True, null=True)
    checked_date_po = models.CharField(db_column='CheckedDate', max_length=50, blank=True, null=True)
    checked_time_po = models.CharField(db_column='CheckedTime', max_length=50, blank=True, null=True)
    approved_po = models.CharField(db_column='Approved', max_length=50, blank=True, null=True)
    approved_by_po = models.CharField(db_column='ApprovedBy', max_length=100, blank=True, null=True)
    approve_date_po = models.CharField(db_column='ApproveDate', max_length=50, blank=True, null=True)
    approve_time_po = models.CharField(db_column='ApproveTime', max_length=50, blank=True, null=True)
    authorized_po = models.CharField(db_column='Authorized', max_length=50, blank=True, null=True)
    authorized_by_po = models.CharField(db_column='AuthorizedBy', max_length=100, blank=True, null=True)
    authorize_date_po = models.CharField(db_column='AuthorizeDate', max_length=50, blank=True, null=True)
    authorize_time_po = models.CharField(db_column='AuthorizeTime', max_length=50, blank=True, null=True)
    reference = models.CharField(db_column='Reference', max_length=255, blank=True, null=True)
    reference_date = models.CharField(db_column='ReferenceDate', max_length=50, blank=True, null=True)
    ref_desc = models.CharField(db_column='RefDesc', max_length=500, blank=True, null=True)
    mod = models.CharField(db_column='MOD', max_length=100, blank=True, null=True)
    inspection = models.CharField(db_column='Inspection', max_length=100, blank=True, null=True)
    ship_to = models.CharField(db_column='ShipTo', max_length=255, blank=True, null=True)
    warranty = models.CharField(db_column='Warrenty', max_length=100, blank=True, null=True)
    freight = models.CharField(db_column='Freight', max_length=100, blank=True, null=True)
    octroi = models.CharField(db_column='Octri', max_length=100, blank=True, null=True)
    insurance = models.CharField(db_column='Insurance', max_length=100, blank=True, null=True)
    payment_terms = models.CharField(db_column='PaymentTerms', max_length=255, blank=True, null=True)
    additional_desc = models.CharField(db_column='AddDesc', max_length=500, blank=True, null=True)
    excise_service_tax = models.CharField(db_column='ExciseSerTax', max_length=100, blank=True, null=True)
    vat = models.CharField(db_column='VAT', max_length=100, blank=True, null=True)
    pf = models.CharField(db_column='PF', max_length=100, blank=True, null=True)

    # Joined PR fields (prefixed with pr_ to avoid clashes)
    pr_no = models.CharField(db_column='PRNo', max_length=50, blank=True, null=True)
    pr_date = models.CharField(db_column='PRDate', max_length=50, blank=True, null=True)
    pr_wo = models.CharField(db_column='PRWO', max_length=50, blank=True, null=True)
    pr_supplier = models.CharField(db_column='PRSupplier', max_length=255, blank=True, null=True)
    pr_qty = models.DecimalField(db_column='PRQty', max_digits=18, decimal_places=4, blank=True, null=True)
    pr_rate = models.DecimalField(db_column='PRRate', max_digits=18, decimal_places=4, blank=True, null=True)
    pr_discount = models.DecimalField(db_column='PRDiscount', max_digits=18, decimal_places=4, blank=True, null=True)
    pr_amount = models.DecimalField(db_column='PRAmount', max_digits=18, decimal_places=4, blank=True, null=True)
    pr_ac_head = models.CharField(db_column='PRACHead', max_length=100, blank=True, null=True)
    pr_delivery_date = models.CharField(db_column='PRDelDate', max_length=50, blank=True, null=True)
    pr_remarks = models.CharField(db_column='PRRemarks', max_length=500, blank=True, null=True)

    # Joined SPR fields
    spr_no = models.CharField(db_column='SPRNo', max_length=50, blank=True, null=True)
    spr_date = models.CharField(db_column='SPRDate', max_length=50, blank=True, null=True)
    spr_wo = models.CharField(db_column='SPRWO', max_length=50, blank=True, null=True)
    spr_supplier = models.CharField(db_column='SPRSupplier', max_length=255, blank=True, null=True)
    spr_qty = models.DecimalField(db_column='SPRQty', max_digits=18, decimal_places=4, blank=True, null=True)
    spr_rate = models.DecimalField(db_column='SPRRate', max_digits=18, decimal_places=4, blank=True, null=True)
    spr_discount = models.DecimalField(db_column='SPRDiscount', max_digits=18, decimal_places=4, blank=True, null=True)
    spr_amount = models.DecimalField(db_column='SPRAmount', max_digits=18, decimal_places=4, blank=True, null=True)
    spr_ac_head = models.CharField(db_column='SPRACHead', max_length=100, blank=True, null=True)
    spr_delivery_date = models.CharField(db_column='SPRDelDate', max_length=50, blank=True, null=True)
    spr_remarks = models.CharField(db_column='SPRRemarks', max_length=500, blank=True, null=True)
    spr_bg_group = models.CharField(db_column='SPRBGGroup', max_length=100, blank=True, null=True)
    spr_checked = models.CharField(db_column='SPRChecked', max_length=50, blank=True, null=True)
    spr_checked_by = models.CharField(db_column='SPRCheckedBy', max_length=100, blank=True, null=True)
    spr_check_date = models.CharField(db_column='SPRCheckDate', max_length=50, blank=True, null=True)
    spr_check_time = models.CharField(db_column='SPRCheckTime', max_length=50, blank=True, null=True)
    spr_approved = models.CharField(db_column='SPRApproved', max_length=50, blank=True, null=True)
    spr_approved_by = models.CharField(db_column='SPRApprovedBy', max_length=100, blank=True, null=True)
    spr_approve_date = models.CharField(db_column='SPRApproveDate', max_length=50, blank=True, null=True)
    spr_approve_time = models.CharField(db_column='SPRApproveTime', max_length=50, blank=True, null=True)
    spr_authorized = models.CharField(db_column='SPRAuthorized', max_length=50, blank=True, null=True)
    spr_authorized_by = models.CharField(db_column='SPRAuthorizedBy', max_length=100, blank=True, null=True)
    spr_authorize_date = models.CharField(db_column='SPRAuthorizeDate', max_length=50, blank=True, null=True)
    spr_authorize_time = models.CharField(db_column='SPRAuthorizeTime', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'View_PO_PR_SPR_Item'
        verbose_name = 'PO/PR/SPR Report Item'
        verbose_name_plural = 'PO/PR/SPR Report Items'

    def __str__(self):
        return f"PO: {self.po_no or 'N/A'}, PR: {self.pr_no or 'N/A'}, SPR: {self.spr_no or 'N/A'} - {self.item_code or 'N/A'}"

# Model for View_PR_Item (flag = 2)
class PurchaseRequisitionReportItem(BaseReportModel):
    pr_no = models.CharField(db_column='PRNo', max_length=50, blank=True, null=True)
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True) # Generic WO No for PR context
    pl_no = models.CharField(db_column='PLNo', max_length=50, blank=True, null=True)
    pr_amount = models.DecimalField(db_column='PR_Amount', max_digits=18, decimal_places=4, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'View_PR_Item'
        verbose_name = 'PR Report Item'
        verbose_name_plural = 'PR Report Items'

    def __str__(self):
        return f"PR No: {self.pr_no or 'N/A'} - {self.item_code or 'N/A'}"

# Model for View_SPR_Item (flag = 3)
class SupplierPurchaseRequisitionReportItem(BaseReportModel):
    spr_no = models.CharField(db_column='SPRNo', max_length=50, blank=True, null=True)
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True) # Generic WO No for SPR context
    spr_amount = models.DecimalField(db_column='SPR_Amount', max_digits=18, decimal_places=4, blank=True, null=True)
    bg = models.CharField(db_column='BG', max_length=100, blank=True, null=True)
    checked_spr = models.CharField(db_column='Checked', max_length=50, blank=True, null=True)
    checked_by_spr = models.CharField(db_column='CheckedBy', max_length=100, blank=True, null=True)
    checked_date_spr = models.CharField(db_column='CheckedDate', max_length=50, blank=True, null=True)
    checked_time_spr = models.CharField(db_column='CheckedTime', max_length=50, blank=True, null=True)
    approved_spr = models.CharField(db_column='Approved', max_length=50, blank=True, null=True)
    approved_by_spr = models.CharField(db_column='ApprovedBy', max_length=100, blank=True, null=True)
    approve_date_spr = models.CharField(db_column='ApproveDate', max_length=50, blank=True, null=True)
    approve_time_spr = models.CharField(db_column='ApproveTime', max_length=50, blank=True, null=True)
    authorized_spr = models.CharField(db_column='Authorized', max_length=50, blank=True, null=True)
    authorized_by_spr = models.CharField(db_column='AuthorizedBy', max_length=100, blank=True, null=True)
    authorize_date_spr = models.CharField(db_column='AuthorizeDate', max_length=50, blank=True, null=True)
    authorize_time_spr = models.CharField(db_column='AuthorizeTime', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'View_SPR_Item'
        verbose_name = 'SPR Report Item'
        verbose_name_plural = 'SPR Report Items'

    def __str__(self):
        return f"SPR No: {self.spr_no or 'N/A'} - {self.item_code or 'N/A'}"

# Utility to get available columns and their display names for each report type
# This replaces the ASP.NET's sp_columns and hardcoded label renaming logic
# In a real scenario, this might be loaded from a config file or a dedicated DB table for report definitions.
def get_report_columns_metadata(report_type):
    """
    Returns a dictionary of available columns and their display names for a given report type.
    Key: Django model field name, Value: user-friendly display name.
    """
    # Common fields that can be selected, mapping model field name to display name
    common_fields = {
        'item_code': 'Item Code', 'description': 'Description', 'uom': 'UOM',
        'stock_qty': 'Stock Qty', 'report_date': 'Date', 'supplier_name': 'Supplier Name',
        'qty': 'Qty', 'rate': 'Rate', 'discount': 'Discount',
        'remarks': 'Remarks', 'company_id': 'Company ID' # CompId is generally not shown but used for filtering
    }

    # Column definitions specific to each report type (flag)
    report_type_columns = {
        1: { # PO Report (View_PO_PR_SPR_Item)
            'report_date': 'PO Date', # Rename common field for PO context
            'po_no': 'PO No', 'wo_no_po': 'WO NO', 'amendment_no': 'Amd No',
            'po_amount': 'Amount', 'delivery_date': 'Del. Date',
            'checked_po': 'Checked', 'checked_by_po': 'Checked By', 'checked_date_po': 'Checked Date', 'checked_time_po': 'Checked Time',
            'approved_po': 'Approved', 'approved_by_po': 'Approved By', 'approve_date_po': 'Approved Date', 'approve_time_po': 'Approved Time',
            'authorized_po': 'Authorized', 'authorized_by_po': 'Authorized By', 'authorize_date_po': 'Authorized Date', 'authorize_time_po': 'Authorized Time',
            'reference': 'Reference', 'reference_date': 'Reference Date', 'ref_desc': 'Ref. Desc',
            'mod': 'MOD', 'inspection': 'Inspection', 'ship_to': 'Ship To', 'warranty': 'Warrenty',
            'freight': 'Freight', 'octroi': 'Octri', 'insurance': 'Insurance',
            'payment_terms': 'Payment Terms', 'additional_desc': 'Add. Desc',
            'excise_service_tax': 'Excise/Ser.Tax', 'vat': 'VAT', 'pf': 'PF',
            'ac_head': 'Ac Head', # Renamed common field for PO context
            # Joined PR fields for PO report
            'pr_no': 'PR No (PO)', 'pr_date': 'PR Date (PO)', 'pr_wo': 'PR WO No (PO)', 'pr_supplier': 'PR Supplier Name (PO)',
            'pr_qty': 'PR Qty (PO)', 'pr_rate': 'PR Rate (PO)', 'pr_discount': 'PR Discount (PO)',
            'pr_amount': 'PR Amount (PO)', 'pr_ac_head': 'PR Ac Head (PO)', 'pr_delivery_date': 'PR Del. Date (PO)', 'pr_remarks': 'PR Remarks (PO)',
            # Joined SPR fields for PO report
            'spr_no': 'SPR No (PO)', 'spr_date': 'SPR Date (PO)', 'spr_wo': 'SPR WO No (PO)', 'spr_supplier': 'SPR Supplier Name (PO)',
            'spr_qty': 'SPR Qty (PO)', 'spr_rate': 'SPR Rate (PO)', 'spr_discount': 'SPR Discount (PO)',
            'spr_amount': 'SPR Amount (PO)', 'spr_ac_head': 'SPR Ac Head (PO)', 'spr_delivery_date': 'SPR Del. Date (PO)', 'spr_remarks': 'SPR Remarks (PO)',
            'spr_bg_group': 'SPR BG (PO)', 'spr_checked': 'SPR Checked (PO)', 'spr_checked_by': 'SPR Checked By (PO)',
            'spr_check_date': 'SPR Checked Date (PO)', 'spr_check_time': 'SPR Checked Time (PO)', 'spr_approved': 'SPR Approved (PO)',
            'spr_approved_by': 'SPR Approved By (PO)', 'spr_approve_date': 'SPR Approved Date (PO)', 'spr_approve_time': 'SPR Approved Time (PO)',
            'spr_authorized': 'SPR Authorized (PO)', 'spr_authorized_by': 'SPR Authorized By (PO)',
            'spr_authorize_date': 'SPR Authorized Date (PO)', 'spr_authorize_time': 'SPR Authorized Time (PO)',
        },
        2: { # PR Report (View_PR_Item)
            'report_date': 'Date', # No rename from common
            'wo_no': 'WO No', 'pr_no': 'PR No', 'pl_no': 'PLN No',
            'pr_amount': 'Amount', 'ac_head': 'Ac Head', 'delivery_date': 'Del. Date',
        },
        3: { # SPR Report (View_SPR_Item)
            'report_date': 'Date', # No rename from common
            'spr_no': 'SPR No', 'wo_no': 'WO No', 'spr_amount': 'Amount',
            'ac_head': 'Ac Head', 'delivery_date': 'Del. Date',
            'bg': 'BG', 'checked_spr': 'Checked', 'checked_by_spr': 'Checked By', 'checked_date_spr': 'Checked Date', 'checked_time_spr': 'Checked Time',
            'approved_spr': 'Approved', 'approved_by_spr': 'Approved By', 'approve_date_spr': 'Approved Date', 'approve_time_spr': 'Approved Time',
            'authorized_spr': 'Authorized', 'authorized_by_spr': 'Authorized By', 'authorize_date_spr': 'Authorized Date', 'authorize_time_spr': 'Authorized Time',
        }
    }

    # Combine common and type-specific fields. Type-specific overrides common.
    # Note: 'Sr No' is always present and generated dynamically, not from DB column.
    # We will generate this on the Python side before rendering for consistency with original.
    columns = {}
    columns.update(common_fields)
    columns.update(report_type_columns.get(report_type, {}))

    # Default hidden columns from ASP.NET for flag 1: CompId, ExST, Code
    # For flag 2, CompId was hidden.
    # We will filter these out from the list of selectable columns if they aren't explicitly needed by the user.
    excluded_default_columns = {
        'company_id', # CompId in original code
        'excise_service_tax', # ExST in original code
        # 'code' # If 'Code' (ItemCode or SupCode) is a filter param, it's not a selectable display column typically.
    }

    # Create a list of (field_name, display_name) tuples for the checkboxes
    selectable_columns = []
    # Add 'Sr No' as a virtual column
    selectable_columns.append(('sr_no', 'Sr No', '')) # field_name, display_name, original_db_column_name

    # Helper mapping original DB Column Name to Django Model Field Name
    # This is crucial for the UI checkboxes whose values were DB column names.
    db_col_to_model_field = {}
    for model_class in [PurchaseOrderReportItem, PurchaseRequisitionReportItem, SupplierPurchaseRequisitionReportItem]:
        for field in model_class._meta.get_fields():
            if hasattr(field, 'db_column') and field.db_column:
                db_col_to_model_field[field.db_column] = field.name
            else:
                db_col_to_model_field[field.name] = field.name # For fields where db_column is not explicitly set (e.g., primary key)

    # ASP.NET used "Column_name" as the value for checkboxes, and then manually remapped display text.
    # We need to provide the Django field name as value, and the correct display text.
    # This list maps (original_aspnet_value, friendly_label_for_checkbox, corresponding_django_field_name)
    # This mapping is an inference from the C# code's `chkFields.Items[X].Text = "Label"` lines.
    # It needs to be precise based on the actual `sp_columns` output and the manual text remapping.

    # PO Report Columns (flag=1) - derived from chkFields, chkFields2, chkFields3
    po_report_selectable_columns_map = {
        "Sr No": "sr_no", "Item Code": "item_code", "Description": "description", "UOM": "uom", "Stock Qty": "stock_qty", "Date": "report_date",
        "PO No": "po_no", "WO NO": "wo_no_po", "Amd No": "amendment_no", "Supplier Name": "supplier_name", "Qty": "qty", "Rate": "rate",
        "Discount": "discount", "Amount": "po_amount", "Del. Date": "delivery_date", "Remarks": "remarks",
        "Checked": "checked_po", "Checked By": "checked_by_po", "Checked Date": "checked_date_po", "Checked Time": "checked_time_po",
        "Approved": "approved_po", "Approved By": "approved_by_po", "Approved Date": "approve_date_po", "Approved Time": "approve_time_po",
        "Authorized": "authorized_po", "Authorized By": "authorized_by_po", "Authorized Date": "authorize_date_po", "Authorized Time": "authorize_time_po",
        "Reference": "reference", "Reference Date": "reference_date", "Ref. Desc": "ref_desc", "MOD": "mod",
        "Inspection": "inspection", "Ship To": "ship_to", "Warrenty": "warranty", "Freight": "freight",
        "Octri": "octroi", "Insurance": "insurance", "Payment Terms": "payment_terms", "Add. Desc": "additional_desc",
        "Excise/Ser.Tax": "excise_service_tax", "VAT": "vat", "PF": "pf", "Ac Head": "ac_head",
        # PR Joined fields
        "PR No": "pr_no", "PR Date": "pr_date", "PR WO No": "pr_wo", "PR Supplier Name": "pr_supplier",
        "PR Qty": "pr_qty", "PR Rate": "pr_rate", "PR Discount": "pr_discount", "PR Amount": "pr_amount",
        "PR Ac Head": "pr_ac_head", "PR Del. Date": "pr_delivery_date", "PR Remarks": "pr_remarks",
        # SPR Joined fields
        "SPR No": "spr_no", "SPR Date": "spr_date", "SPR WO No": "spr_wo", "SPR Supplier Name": "spr_supplier",
        "SPR Qty": "spr_qty", "SPR Rate": "spr_rate", "SPR Discount": "spr_discount", "SPR Amount": "spr_amount",
        "SPR Ac Head": "spr_ac_head", "SPR Del. Date": "spr_delivery_date", "SPR Remarks": "spr_remarks",
        "SPR BG": "spr_bg_group", "SPR Checked": "spr_checked", "SPR Checked By": "spr_checked_by",
        "SPR Checked Date": "spr_check_date", "SPR Checked Time": "spr_check_time", "SPR Approved": "spr_approved",
        "SPR Approved By": "spr_approved_by", "SPR Approved Date": "spr_approve_date", "SPR Approved Time": "spr_approve_time",
        "SPR Authorized": "spr_authorized", "SPR Authorized By": "spr_authorized_by",
        "SPR Authorized Date": "spr_authorize_date", "SPR Authorized Time": "spr_authorize_time",
    }
    # PR Report Columns (flag=2)
    pr_report_selectable_columns_map = {
        "Sr No": "sr_no", "Item Code": "item_code", "Description": "description", "UOM": "uom", "Stock Qty": "stock_qty",
        "Date": "report_date", "WO No": "wo_no", "PR No": "pr_no", "PLN No": "pl_no", "Supplier Name": "supplier_name",
        "Qty": "qty", "Rate": "rate", "Discount": "discount", "Amount": "pr_amount", "Ac Head": "ac_head",
        "Del. Date": "delivery_date", "Remarks": "remarks",
    }
    # SPR Report Columns (flag=3)
    spr_report_selectable_columns_map = {
        "Sr No": "sr_no", "Item Code": "item_code", "Description": "description", "UOM": "uom", "Stock Qty": "stock_qty",
        "Date": "report_date", "SPR No": "spr_no", "WO No": "wo_no", "Supplier Name": "supplier_name",
        "Qty": "qty", "Rate": "rate", "Discount": "discount", "Amount": "spr_amount", "Ac Head": "ac_head",
        "Del. Date": "delivery_date", "Remarks": "remarks", "BG": "bg",
        "Checked": "checked_spr", "Checked By": "checked_by_spr", "Checked Date": "checked_date_spr", "Checked Time": "checked_time_spr",
        "Approved": "approved_spr", "Approved By": "approved_by_spr", "Approved Date": "approve_date_spr", "Approved Time": "approve_time_spr",
        "Authorized": "authorized_spr", "Authorized By": "authorized_by_spr", "Authorized Date": "authorize_date_spr", "Authorized Time": "authorize_time_spr",
    }

    if report_type == 1:
        selected_map = po_report_selectable_columns_map
        model_class = PurchaseOrderReportItem
    elif report_type == 2:
        selected_map = pr_report_selectable_columns_map
        model_class = PurchaseRequisitionReportItem
    elif report_type == 3:
        selected_map = spr_report_selectable_columns_map
        model_class = SupplierPurchaseRequisitionReportItem
    else:
        return [] # Return empty if report_type is invalid

    # Construct the final list of (django_field_name, display_label) tuples
    final_columns = []
    # Add Sr No separately as it's a derived field, not directly from DB.
    final_columns.append({'field_name': 'sr_no', 'display_name': 'Sr No', 'default_selected': True}) # Always selected by default

    for label, field_name in selected_map.items():
        if field_name == 'sr_no': # Skip if it's the serial number, already added
            continue

        # Determine default selection based on original ASP.NET behavior
        # In ASP.NET, items 0,1,2,3 for chkFields were default selected.
        # This translates to 'Sr No', 'Item Code', 'Description', 'UOM' and 'Stock Qty'
        # based on typical order for PR/SPR reports in C#.
        # For PO reports (flag=1), items 0,1,2,3 for chkFields were also default selected.
        # Which map to 'Sr No', 'Item Code', 'Description', 'UOM'.
        default_selected = False
        if field_name in ['item_code', 'description', 'uom', 'stock_qty', 'report_date', 'po_no', 'pr_no', 'spr_no']:
            default_selected = True
        
        if field_name not in excluded_default_columns:
            final_columns.append({
                'field_name': field_name,
                'display_name': label,
                'default_selected': default_selected
            })
    
    return final_columns

```

#### 4.1.1 Services (`material_reports/services.py`)

This file will contain the `ReportService` class, which encapsulates the complex business logic for fetching and filtering report data based on the chosen report type and user-defined parameters. This adheres to the "Fat Model" principle by keeping data-intensive operations outside the views.

```python
# material_reports/services.py
from datetime import datetime
from django.db.models import QuerySet
from material_reports.models import (
    PurchaseOrderReportItem,
    PurchaseRequisitionReportItem,
    SupplierPurchaseRequisitionReportItem,
    get_report_columns_metadata
)

class ReportService:
    """
    Service class to encapsulate business logic for fetching and filtering report data.
    """

    REPORT_TYPE_MAP = {
        1: {'model': PurchaseOrderReportItem, 'label': 'PO'},
        2: {'model': PurchaseRequisitionReportItem, 'label': 'PR'},
        3: {'model': SupplierPurchaseRequisitionReportItem, 'label': 'SPR'},
    }

    def __init__(self, request_params, company_id):
        self.request_params = request_params
        self.company_id = company_id
        self.report_type_flag = int(request_params.get('report_type_flag', 1)) # Default to PO report (flag 1)

        self.first_cond = ""
        self.sup_code = ""
        self.item_code = ""
        self.excise_ser_tax = ""
        self.ac_head = ""
        self.wo_no = ""
        self.date_range_cond = ""
        self.po_complete_pending = ""
        
        self._parse_query_params()

    def _parse_query_params(self):
        """
        Parses URL query string parameters and builds filtering conditions.
        Mimics the ASP.NET Page_Load logic.
        """
        req_type = self.request_params.get('type')
        req_no = self.request_params.get('No')
        if req_type and req_no:
            try:
                type_int = int(req_type)
                if type_int == 1:
                    self.first_cond = f"po_no='{req_no}'"
                elif type_int == 2:
                    self.first_cond = f"pr_no='{req_no}'"
                elif type_int == 3:
                    self.first_cond = f"spr_no='{req_no}'"
                elif type_int == 4:
                    # 'WONo' for PO report refers to wo_no_po, for others it's wo_no
                    if self.report_type_flag == 1:
                        self.first_cond = f"wo_no_po='{req_no}'"
                    else:
                        self.first_cond = f"wo_no='{req_no}'"
                elif type_int == 5:
                    self.first_cond = f"item_code='{req_no}'"
                elif type_int == 6:
                    self.first_cond = f"supplier_code='{req_no}'" # Assuming a 'supplier_code' field exists
            except ValueError:
                pass # Handle invalid 'type' parameter

        if self.request_params.get('FDate') and self.request_params.get('TDate'):
            from_date_str = self._format_aspnet_date(self.request_params['FDate'])
            to_date_str = self._format_aspnet_date(self.request_params['TDate'])
            if from_date_str and to_date_str:
                # Use report_date (string) for direct comparison as per original SQL
                self.date_range_cond = f"report_date BETWEEN '{from_date_str}' AND '{to_date_str}'"
            
        if self.request_params.get('SupId'):
            self.sup_code = f"supplier_code='{self.request_params['SupId']}'" # Assuming 'supplier_code' for SupId
        if self.request_params.get('Code'):
            self.item_code = f"item_code='{self.request_params['Code']}'"
        if self.request_params.get('WONo'):
            # Specific WO No for PO report vs general WO No for others
            if self.report_type_flag == 1:
                self.wo_no = f"wo_no_po='{self.request_params['WONo']}'"
            else:
                self.wo_no = f"wo_no='{self.request_params['WONo']}'"
        if self.request_params.get('EX'):
            ex_val = self.request_params['EX']
            if ex_val != '1':
                self.excise_ser_tax = f"excise_service_tax='{ex_val}'"
        if self.request_params.get('accval'):
            acc_val = self.request_params['accval']
            # Original code queries AccHead table for Symbol. We need a way to get 'AHSymb'.
            # For now, we assume 'ACHead' column directly stores the symbol or we can map it.
            # This would require a separate lookup or a pre-defined mapping.
            # Placeholder:
            if acc_val != '0':
                # This would need a DB lookup for AHSymb similar to ASP.NET
                # For demo, assume acc_val can be used directly or map it if known
                # In a real scenario, this would be a lookup in a master data table.
                # Example: AHSymb = AccHead.objects.get(id=acc_val).symbol
                # For now, let's just use acc_val if it's a direct match or skip for simplicity.
                self.ac_head = f"ac_head='{acc_val}'" # Simplified: assuming accval is the actual ACHead value

        status = self.request_params.get('Status')
        if status:
            if self.report_type_flag == 1: # PO Status
                # Original logic: "PONo not in (Select PONo from tblInv_Inward_Master where CompId=...)"
                # This needs a subquery or join in Django ORM.
                if status == '0': # Pending
                    # Subquery to find PONos that have not been in inward master
                    # This would require a Django model for tblInv_Inward_Master
                    # For simplicity, we'll abstract this with a placeholder filter.
                    # self.po_complete_pending = Q(po_no__in=Subquery(InwardMaster.objects.filter(company_id=self.company_id).values('po_no')))
                    pass # Complex, needs external model
                elif status == '1': # Complete
                    # self.po_complete_pending = ~Q(po_no__in=Subquery(InwardMaster.objects.filter(company_id=self.company_id).values('po_no')))
                    pass # Complex, needs external model
            elif self.report_type_flag == 2: # PR Status
                if status == '0': # Pending
                    pass # Similar complex logic for PRs
                elif status == '1': # Complete
                    pass # Similar complex logic for PRs
            elif self.report_type_flag == 3: # SPR Status
                if status == '0': # Pending
                    pass # Similar complex logic for SPRs
                elif status == '1': # Complete
                    pass # Similar complex logic for SPRs

    def _format_aspnet_date(self, date_str):
        """Converts ASP.NET date format (M-D-YYYY) to a standard format if necessary for querying."""
        # The ASP.NET code had complex `CONVERT(datetime, SUBSTRING(Date, CHARINDEX('-', Date) + 1, 2) + '-' + LEFT(Date, CHARINDEX('-', Date) - 1) + '-' + RIGHT(Date, CHARINDEX('-',REVERSE(Date)) - 1)), 103)`
        # Which suggests a M-D-YYYY or D-M-YYYY format being converted.
        # Assuming input is 'MM-DD-YYYY' or 'DD-MM-YYYY' and we need 'YYYY-MM-DD' for DB
        try:
            # Attempt M-D-YYYY (e.g., 10-25-2023)
            dt_obj = datetime.strptime(date_str, '%m-%d-%Y')
        except ValueError:
            try:
                # Attempt D-M-YYYY (e.g., 25-10-2023)
                dt_obj = datetime.strptime(date_str, '%d-%m-%Y')
            except ValueError:
                return None # Invalid date format
        return dt_obj.strftime('%Y-%m-%d') # Standard format for DB queries

    def get_report_data(self, selected_columns):
        """
        Fetches filtered data and formats it for DataTables, including dynamic columns.
        """
        model_info = self.REPORT_TYPE_MAP.get(self.report_type_flag)
        if not model_info:
            return {'headers': [], 'data': [], 'message': 'Invalid report type selected.'}

        ReportModel = model_info['model']
        base_queryset = ReportModel.objects.all().filter(company_id=self.company_id)

        # Apply general filters
        filters = {}
        if self.first_cond:
            # Need to convert string condition to Q object if possible
            # Example: filters['po_no'] = 'SomeNo' if first_cond was for po_no
            # This requires parsing self.first_cond string. For simplicity, direct mapping here.
            # A more robust solution might use django-filter or custom Q objects.
            # For now, let's assume direct mapping if the string is simple enough to parse,
            # or rely on direct Q object building if more complex.
            # Example:
            if 'po_no' in self.first_cond:
                filters['po_no'] = self.first_cond.split('=')[1].strip("'")
            elif 'pr_no' in self.first_cond:
                filters['pr_no'] = self.first_cond.split('=')[1].strip("'")
            # ... and so on for other fields in first_cond
            pass # Implement robust string to Q object parsing for all self.first_cond variants

        if self.date_range_cond:
            # This is complex because `report_date` is CHAR.
            # Direct ORM filter on CHAR date fields with BETWEEN like `report_date__range=('2023-01-01', '2023-12-31')`
            # might not work as expected with string comparison.
            # It's safer to query using raw SQL if performance is critical or convert in Python if dataset is small.
            # OR, fix the DB schema to use DATE/DATETIME fields.
            pass # Needs careful handling for CHAR date field ranges

        # Apply other filters from self.sup_code, self.item_code, etc.
        # This also requires parsing string conditions into Q objects.
        # Example: if self.item_code: base_queryset = base_queryset.filter(item_code=self.item_code.split('=')[1].strip("'"))

        # For demonstration, let's assume simplified direct filters:
        try:
            if self.request_params.get('type') and self.request_params.get('No'):
                req_type = int(self.request_params['type'])
                req_no = self.request_params['No']
                if req_type == 1: filters['po_no'] = req_no
                elif req_type == 2: filters['pr_no'] = req_no
                elif req_type == 3: filters['spr_no'] = req_no
                elif req_type == 4: filters['wo_no'] = req_no # Handles both wo_no and wo_no_po depending on model
                elif req_type == 5: filters['item_code'] = req_no
                elif req_type == 6: filters['supplier_code'] = req_no # Assumes supplier_code field
            
            if self.request_params.get('SupId'): filters['supplier_code'] = self.request_params['SupId']
            if self.request_params.get('Code'): filters['item_code'] = self.request_params['Code']
            if self.request_params.get('WONo'): filters['wo_no'] = self.request_params['WONo'] # General WONo filter
            if self.request_params.get('EX') and self.request_params['EX'] != '1':
                filters['excise_service_tax'] = self.request_params['EX']
            if self.request_params.get('accval') and self.request_params['accval'] != '0':
                filters['ac_head'] = self.request_params['accval'] # Simplified ACHead

            # Apply date range filtering if from_date_str and to_date_str are valid
            from_date_str = self._format_aspnet_date(self.request_params.get('FDate', ''))
            to_date_str = self._format_aspnet_date(self.request_params.get('TDate', ''))
            if from_date_str and to_date_str:
                # This filtering on CHAR fields is problematic. For a real system,
                # convert DB column to actual date or use a more robust lookup.
                # For now, a simple string range.
                # If we were converting 'Date' to a DateField in model, this would be `report_date__range`.
                # For CHAR fields, it depends on string format. Assuming 'YYYY-MM-DD' comparison works.
                base_queryset = base_queryset.extra(where=[f"Date BETWEEN '{from_date_str}' AND '{to_date_str}'"])

            # Apply all collected filters
            base_queryset = base_queryset.filter(**filters)
            
            # Add complex PO Complete/Pending logic if needed, this requires additional models/joins
            # For flag 1 (PO report):
            # if status == '0': base_queryset = base_queryset.exclude(po_no__in=InwardMaster.objects.filter(company_id=self.company_id).values('po_no'))
            # if status == '1': base_queryset = base_queryset.filter(po_no__in=InwardMaster.objects.filter(company_id=self.company_id).values('po_no'))

        except Exception as e:
            # Log the error for debugging
            print(f"Error applying filters: {e}") # Replace with logging
            return {'headers': [], 'data': [], 'message': f'Error fetching data: {e}'}


        # Prepare headers for DataTables
        headers = []
        # Filter selected columns to only include those available for the current report type
        available_columns_metadata = get_report_columns_metadata(self.report_type_flag)
        available_field_names = {col['field_name'] for col in available_columns_metadata}

        # Ensure 'sr_no' is always the first header if selected
        if 'sr_no' in selected_columns:
            headers.append({'data': 'sr_no', 'title': 'Sr No'})
            selected_columns.remove('sr_no') # Remove it so it's not processed as a model field

        # Add headers for the actual model fields
        fields_to_select = [col for col in selected_columns if col in available_field_names]

        # Get display names for the selected fields
        field_display_names = {col['field_name']: col['display_name'] for col in available_columns_metadata}

        for field_name in fields_to_select:
            headers.append({
                'data': field_name, # DataTables column name
                'title': field_display_names.get(field_name, field_name.replace('_', ' ').title()) # Display header
            })

        # Fetch data
        # Use .values() to select only the required fields from the database
        # For fields that don't exist in the current model, .values() would raise error.
        # Need to ensure fields_to_select only contains fields valid for ReportModel.
        valid_model_fields = [f.name for f in ReportModel._meta.get_fields()]
        final_fields_for_query = [f for f in fields_to_select if f in valid_model_fields]

        data = list(base_queryset.values(*final_fields_for_query))

        # Add Sr No dynamically to each row
        if 'sr_no' in selected_columns: # If Sr No was requested, add it back to each row
            for i, row in enumerate(data):
                row['sr_no'] = i + 1

        return {
            'headers': headers,
            'data': data,
            'report_type_label': model_info['label']
        }

    def get_selectable_columns(self):
        """Returns the list of all available columns for the current report type."""
        return get_report_columns_metadata(self.report_type_flag)

    def get_initial_selected_columns(self):
        """Returns the list of columns that should be selected by default."""
        return [col['field_name'] for col in get_report_columns_metadata(self.report_type_flag) if col['default_selected']]

    def get_report_type_label(self):
        """Returns the user-friendly label for the current report type."""
        return self.REPORT_TYPE_MAP.get(self.report_type_flag, {}).get('label', 'Report')

```

#### 4.2 Forms (`material_reports/forms.py`)

Django forms are used to represent the filters and column selection checkboxes. Since the column selection is dynamic, we won't use a standard `ModelForm` for the main data. Instead, we'll create a custom `Form` for the filter criteria and column selection.

```python
# material_reports/forms.py
from django import forms
from material_reports.services import ReportService # Import to access column metadata

class ReportFilterForm(forms.Form):
    """
    Form for dynamic report filtering and column selection.
    """
    report_type_flag = forms.IntegerField(
        widget=forms.HiddenInput(),
        required=False # This will be set by URL, or default.
    )

    # General search filters (mapping to original ASP.NET QueryString params)
    type = forms.IntegerField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Type'})
    )
    No = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'No'})
    )
    FDate = forms.CharField( # Stored as string in source, so char field
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'From Date (MM-DD-YYYY)'})
    )
    TDate = forms.CharField( # Stored as string in source, so char field
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'To Date (MM-DD-YYYY)'})
    )
    SupId = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Supplier ID'})
    )
    Code = forms.CharField( # ItemCode in original
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Item Code'})
    )
    WONo = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'WO No'})
    )
    EX = forms.CharField( # ExciseSerTax in original
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Excise/Ser.Tax'})
    )
    accval = forms.CharField( # ACHead in original
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Account Value'})
    )
    Status = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}, choices=[('', 'All'), ('0', 'Pending'), ('1', 'Completed')])
    )

    # Dynamic CheckboxList for columns
    selected_columns = forms.MultipleChoiceField(
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'grid grid-cols-6 gap-x-4'}), # Mimic RepeatColumns="6"
        choices=[], # Populated dynamically in __init__
    )

    def __init__(self, *args, **kwargs):
        report_type_flag = kwargs.pop('report_type_flag', None)
        initial_selected_columns = kwargs.pop('initial_selected_columns', None)
        super().__init__(*args, **kwargs)

        self.report_service = ReportService(self.data, company_id=1) # Company ID is hardcoded in ASP.NET as 1. Adjust as needed.

        # Set initial report_type_flag if provided (from URL)
        if report_type_flag is not None:
            self.fields['report_type_flag'].initial = report_type_flag
            self.report_service.report_type_flag = report_type_flag

        # Dynamically set choices for selected_columns
        choices = []
        for col_meta in self.report_service.get_selectable_columns():
            choices.append((col_meta['field_name'], col_meta['display_name']))
        self.fields['selected_columns'].choices = choices

        # Set initial selections for checkboxes based on default behavior or previous submission
        if self.is_bound:
            # If form is submitted, use submitted data
            pass
        elif initial_selected_columns is not None:
            self.fields['selected_columns'].initial = initial_selected_columns
        else:
            # Default selections from service metadata
            self.fields['selected_columns'].initial = self.report_service.get_initial_selected_columns()
            
        # Add a "check all" pseudo-field for UI convenience (not submitted with form)
        self.fields['check_all'] = forms.BooleanField(
            required=False,
            widget=forms.CheckboxInput(attrs={'class': 'form-checkbox h-4 w-4 text-blue-600', 'id': 'checkAll'}),
            label='Check All'
        )

```

#### 4.3 Views (`material_reports/views.py`)

The views will be thin, primarily coordinating requests between the `ReportService` and templates. They will handle GET requests for initial display and POST requests (via HTMX) for dynamic updates and Excel exports.

```python
# material_reports/views.py
from django.views.generic import TemplateView, View
from django.shortcuts import render
from django.http import HttpResponse, JsonResponse
from django.contrib import messages
from material_reports.forms import ReportFilterForm
from material_reports.services import ReportService
import pandas as pd # For Excel export

class ReportSearchView(TemplateView):
    """
    Main view for the dynamic report search page. Handles initial page load and
    renders the search form and an empty container for the report table.
    """
    template_name = 'material_reports/search_report.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        report_type_flag = int(self.request.GET.get('RAd', 1)) # Mimic ASP.NET's RAd parameter, default 1 (PO)
        
        # Initialize ReportService for initial column metadata
        report_service = ReportService(self.request.GET, company_id=self.request.session.get('compid', 1))
        report_service.report_type_flag = report_type_flag # Ensure service uses correct flag

        form = ReportFilterForm(
            self.request.GET,
            report_type_flag=report_type_flag,
            initial_selected_columns=report_service.get_initial_selected_columns()
        )
        
        context['form'] = form
        context['report_type_label'] = report_service.get_report_type_label()
        context['flag'] = report_type_flag # Pass flag to template for conditional display
        return context

class ReportTablePartialView(View):
    """
    Handles HTMX requests to fetch and render the report table partial.
    This view is responsible for retrieving filtered data and preparing it for DataTables.
    """
    def get(self, request, *args, **kwargs):
        # Company ID should come from user's session or authentication system
        company_id = request.session.get('compid', 1) # Default to 1 if not in session

        report_service = ReportService(request.GET, company_id=company_id)
        
        # Get selected columns from request, default to initial if none selected (first load)
        selected_columns = request.GET.getlist('selected_columns')
        if not selected_columns:
            selected_columns = report_service.get_initial_selected_columns()

        report_data = report_service.get_report_data(selected_columns)
        
        context = {
            'headers': report_data['headers'],
            'data': report_data['data'],
            'report_type_label': report_data['report_type_label'],
            'has_data': bool(report_data['data'])
        }

        # Render the partial template
        return render(request, 'material_reports/_report_table.html', context)

class ReportExportExcelView(View):
    """
    Handles the request to export the currently displayed report data to Excel.
    """
    def get(self, request, *args, **kwargs):
        company_id = request.session.get('compid', 1)

        report_service = ReportService(request.GET, company_id=company_id)

        selected_columns = request.GET.getlist('selected_columns')
        if not selected_columns:
            selected_columns = report_service.get_initial_selected_columns()

        report_data = report_service.get_report_data(selected_columns)

        if not report_data['data']:
            messages.warning(request, "No records to export.")
            # For HTMX, trigger a message display
            return HttpResponse(status=204, headers={'HX-Trigger': 'showMessage'})

        # Convert list of dicts to DataFrame
        df = pd.DataFrame(report_data['data'])

        # Rename columns to their display names
        header_map = {h['data']: h['title'] for h in report_data['headers']}
        df = df.rename(columns=header_map)

        # Ensure columns are in the correct order as per headers
        ordered_columns = [h['title'] for h in report_data['headers']]
        df = df[ordered_columns]

        # Create an Excel file in-memory
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            headers={'Content-Disposition': 'attachment; filename="report.xlsx"'},
        )
        df.to_excel(response, index=False, engine='openpyxl')
        return response

# Note: The ASP.NET had a btnCancel_Click which redirected.
# In Django, this is typically handled by a simple anchor tag or
# a button that uses `window.history.back()` or redirects to a known URL.
```

#### 4.4 Templates (`material_reports/templates/material_reports/`)

Templates are designed for HTMX partial loading. `search_report.html` is the main page, and `_report_table.html` is dynamically loaded to update the DataTables. The form for filters and column selection can also be a partial `_search_form.html` if it needs to be dynamically loaded.

**`material_reports/templates/material_reports/search_report.html` (Main Page)**

```html
{% extends 'core/base.html' %}

{% block title %}Dynamic Report Search - AutoERP{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-3xl font-extrabold text-gray-900 mb-6">Material Management Reports</h2>
    
    <div class="bg-white shadow-lg rounded-lg p-6 mb-8">
        <h3 class="text-xl font-semibold text-gray-800 mb-4">Report Selection & Filters: <span class="text-blue-600">{{ report_type_label }}</span></h3>
        
        <form id="report-filter-form"
              hx-get="{% url 'material_reports:report_table_partial' %}"
              hx-target="#report-table-container"
              hx-swap="innerHTML"
              hx-indicator="#loading-indicator"
              class="space-y-6">
            
            {# Hidden input for report type flag from URL #}
            {{ form.report_type_flag.as_hidden }}

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {# Render common filter fields. Ensure field names match form.py #}
                <div>
                    <label for="{{ form.type.id_for_label }}" class="block text-sm font-medium text-gray-700">Type</label>
                    {{ form.type }}
                </div>
                <div>
                    <label for="{{ form.No.id_for_label }}" class="block text-sm font-medium text-gray-700">No</label>
                    {{ form.No }}
                </div>
                <div>
                    <label for="{{ form.FDate.id_for_label }}" class="block text-sm font-medium text-gray-700">From Date</label>
                    {{ form.FDate }}
                </div>
                <div>
                    <label for="{{ form.TDate.id_for_label }}" class="block text-sm font-medium text-gray-700">To Date</label>
                    {{ form.TDate }}
                </div>
                <div>
                    <label for="{{ form.SupId.id_for_label }}" class="block text-sm font-medium text-gray-700">Supplier ID</label>
                    {{ form.SupId }}
                </div>
                <div>
                    <label for="{{ form.Code.id_for_label }}" class="block text-sm font-medium text-gray-700">Item Code</label>
                    {{ form.Code }}
                </div>
                 <div>
                    <label for="{{ form.WONo.id_for_label }}" class="block text-sm font-medium text-gray-700">WO No</label>
                    {{ form.WONo }}
                </div>
                <div>
                    <label for="{{ form.EX.id_for_label }}" class="block text-sm font-medium text-gray-700">Excise/Ser.Tax</label>
                    {{ form.EX }}
                </div>
                <div>
                    <label for="{{ form.accval.id_for_label }}" class="block text-sm font-medium text-gray-700">Account Value</label>
                    {{ form.accval }}
                </div>
                <div>
                    <label for="{{ form.Status.id_for_label }}" class="block text-sm font-medium text-gray-700">Status</label>
                    {{ form.Status }}
                </div>
            </div>

            <div class="mt-6">
                <strong class="block text-md font-medium text-gray-700 mb-2">Select columns to show in the GridView:</strong>
                <div class="flex items-center mb-4">
                    {{ form.check_all.label_tag }}
                    {{ form.check_all }}
                </div>

                <div id="column-checkboxes" class="border border-gray-300 rounded-md p-4">
                    {{ form.selected_columns }}
                </div>
            </div>
            
            <div class="mt-6 flex justify-end space-x-4">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md">
                    Show Report
                </button>
                <a href="{% url 'material_reports:report_export_excel' %}"
                   hx-boost="true"
                   hx-target="body"
                   hx-swap="none"
                   hx-vals="js:htmx.serialize(htmx.find('#report-filter-form'))"
                   class="inline-flex items-center justify-center bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-md">
                    Export To Excel
                </a>
                <a href="#" onclick="window.history.back(); return false;" class="inline-flex items-center justify-center bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-md shadow-md">
                    Cancel
                </a>
            </div>
        </form>
    </div>

    {# Loading indicator for HTMX requests #}
    <div id="loading-indicator" class="htmx-indicator text-center py-4">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">Loading Report...</p>
    </div>

    {# Report table container - will be populated by HTMX #}
    <div id="report-table-container" class="bg-white shadow-lg rounded-lg p-6">
        <div class="text-center text-gray-500 py-10">
            <p>Select filter options and click "Show Report" to view data.</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.tailwindcss.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.7/css/dataTables.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.7/css/dataTables.tailwindcss.min.css">


<script>
    // Alpine.js for check-all behavior (simpler than complex JS from ASP.NET)
    document.addEventListener('alpine:init', () => {
        Alpine.data('reportForm', () => ({
            init() {
                // Attach event listener for checkAll checkbox
                document.getElementById('checkAll').addEventListener('change', (event) => {
                    const isChecked = event.target.checked;
                    const checkboxes = document.querySelectorAll('#column-checkboxes input[type="checkbox"]');
                    checkboxes.forEach(checkbox => {
                        checkbox.checked = isChecked;
                    });
                });
            }
        }));
    });

    // Handle messages triggered from Django views (e.g., no data for export)
    document.body.addEventListener('showMessage', function(evt) {
        // Assuming Django's messages framework renders HTML directly.
        // For HTMX, you might want to show a custom alert or modal.
        // For simplicity, we can just use a JS alert for demonstration.
        // In production, integrate with a notification system like Toastify or Alpine.js state.
        const messagesContainer = document.getElementById('messages-container'); // Need a container in base.html
        if (messagesContainer) {
            // This assumes `messages` is a Django context variable rendered in base.html
            // For HTMX, you'd trigger custom data, e.g., `HX-Trigger: {"message": "No records to export"}`
            alert("No records to export.");
        }
    });

    // Re-initialize DataTables when HTMX swaps content
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'report-table-container') {
            // Check if DataTable exists and destroy it before re-initializing
            if ($.fn.DataTable.isDataTable('#report-data-table')) {
                $('#report-data-table').DataTable().destroy();
            }
            // Initialize new DataTable only if data exists
            if (document.getElementById('report-data-table')) {
                $('#report-data-table').DataTable({
                    "pageLength": 10, // Default page length
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "columnDefs": [
                        { "orderable": false, "targets": 0 } // Sr No column often not sortable
                    ]
                });
            }
        }
    });

    // Trigger initial report load on page load
    document.addEventListener('DOMContentLoaded', function() {
        htmx.trigger('#report-filter-form', 'submit');
    });

</script>
{% endblock %}
```

**`material_reports/templates/material_reports/_report_table.html` (Partial for DataTables)**

```html
{% comment %}
    This partial template is loaded dynamically by HTMX.
    It contains the DataTable structure and is re-rendered on filter changes.
{% endcomment %}

{% if has_data %}
<div class="overflow-x-auto">
    <table id="report-data-table" class="min-w-full divide-y divide-gray-200 shadow-sm rounded-lg">
        <thead class="bg-gray-50">
            <tr>
                {% for header in headers %}
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {{ header.title }}
                </th>
                {% endfor %}
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for row in data %}
            <tr>
                {% for header in headers %}
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ row|get_item:header.data }} {# Custom filter to get dictionary item by key #}
                </td>
                {% endfor %}
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% else %}
<div class="text-center text-gray-500 py-10">
    <p>No records found matching your criteria.</p>
</div>
{% endif %}

{# Custom template filter to access dictionary items by key, if needed #}
{# (Add this to your material_reports/templatetags/material_reports_tags.py if not already present) #}
{% load material_reports_tags %} 
```

**`material_reports/templatetags/material_reports_tags.py` (Custom Template Filter)**

```python
# material_reports/templatetags/material_reports_tags.py
from django import template

register = template.Library()

@register.filter
def get_item(dictionary, key):
    """
    Retrieves an item from a dictionary given its key.
    Useful for accessing dynamic fields in templates.
    """
    return dictionary.get(key)

```

#### 4.5 URLs (`material_reports/urls.py`)

Define URL patterns for the views, including the main page and the HTMX partial for the table.

```python
# material_reports/urls.py
from django.urls import path
from .views import ReportSearchView, ReportTablePartialView, ReportExportExcelView

app_name = 'material_reports'

urlpatterns = [
    path('search-report/', ReportSearchView.as_view(), name='search_report'),
    path('search-report/table/', ReportTablePartialView.as_view(), name='report_table_partial'),
    path('search-report/export/', ReportExportExcelView.as_view(), name='report_export_excel'),
]

```

**Include these URLs in your project's main `urls.py`:**
```python
# your_project/urls.py
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('material-management/', include('material_reports.urls')), # Main app URL
]
```

#### 4.6 Tests (`material_reports/tests.py`)

Comprehensive tests are crucial. We'll include unit tests for the `ReportService` (fat model logic) and integration tests for the views, including HTMX interactions.

```python
# material_reports/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from material_reports.models import (
    PurchaseOrderReportItem,
    PurchaseRequisitionReportItem,
    SupplierPurchaseRequisitionReportItem,
    get_report_columns_metadata
)
from material_reports.services import ReportService

# Mock data for database views (since we don't manage them)
MOCK_PO_DATA = [
    {'id': 1, 'company_id': 1, 'item_code': 'ITEM001', 'description': 'Item A', 'po_no': 'PO001', 'report_date': '2023-01-15', 'supplier_name': 'Supp1', 'qty': 10.0, 'po_amount': 100.0, 'wo_no_po': 'WO001', 'pr_no': 'PR001'},
    {'id': 2, 'company_id': 1, 'item_code': 'ITEM002', 'description': 'Item B', 'po_no': 'PO002', 'report_date': '2023-02-20', 'supplier_name': 'Supp2', 'qty': 20.0, 'po_amount': 200.0, 'wo_no_po': 'WO002', 'pr_no': 'PR002'},
    {'id': 3, 'company_id': 2, 'item_code': 'ITEM003', 'description': 'Item C', 'po_no': 'PO003', 'report_date': '2023-03-10', 'supplier_name': 'Supp1', 'qty': 5.0, 'po_amount': 50.0, 'wo_no_po': 'WO003', 'pr_no': 'PR003'},
]

MOCK_PR_DATA = [
    {'id': 101, 'company_id': 1, 'item_code': 'ITEM001', 'description': 'Item A (PR)', 'pr_no': 'PR001', 'report_date': '2023-01-01', 'supplier_name': 'Supp1'},
    {'id': 102, 'company_id': 1, 'item_code': 'ITEM004', 'description': 'Item D (PR)', 'pr_no': 'PR004', 'report_date': '2023-04-05', 'supplier_name': 'Supp3'},
]

MOCK_SPR_DATA = [
    {'id': 201, 'company_id': 1, 'item_code': 'ITEM002', 'description': 'Item B (SPR)', 'spr_no': 'SPR002', 'report_date': '2023-02-01', 'supplier_name': 'Supp2'},
]


# Patch the model objects manager to return mock data for queries
@patch('material_reports.models.PurchaseOrderReportItem.objects')
@patch('material_reports.models.PurchaseRequisitionReportItem.objects')
@patch('material_reports.models.SupplierPurchaseRequisitionReportItem.objects')
class ReportServiceTest(TestCase):
    def setUp(self):
        self.company_id = 1
        self.mock_po_manager = MagicMock()
        self.mock_pr_manager = MagicMock()
        self.mock_spr_manager = MagicMock()

        self.mock_po_manager.filter.return_value.values.return_value = MOCK_PO_DATA
        self.mock_pr_manager.filter.return_value.values.return_value = MOCK_PR_DATA
        self.mock_spr_manager.filter.return_value.values.return_value = MOCK_SPR_DATA

    def test_get_report_data_po_type(self, mock_spr, mock_pr, mock_po):
        # Configure mocks to return data specific to `values()` calls
        mock_po.all.return_value.filter.return_value.values.return_value = MOCK_PO_DATA
        
        request_params = {'report_type_flag': '1'}
        selected_columns = ['sr_no', 'item_code', 'po_no', 'report_date']
        service = ReportService(request_params, self.company_id)
        result = service.get_report_data(selected_columns)
        
        self.assertIn('headers', result)
        self.assertIn('data', result)
        self.assertEqual(len(result['data']), 2) # Only records for company_id 1
        self.assertIn('sr_no', result['data'][0])
        self.assertEqual(result['headers'][0]['title'], 'Sr No')
        self.assertEqual(result['report_type_label'], 'PO')
        
        # Verify correct model was used
        mock_po.all.assert_called_once()
        mock_po.all.return_value.filter.assert_called_once_with(company_id=self.company_id)

    def test_get_report_data_pr_type(self, mock_spr, mock_pr, mock_po):
        mock_pr.all.return_value.filter.return_value.values.return_value = MOCK_PR_DATA
        
        request_params = {'report_type_flag': '2'}
        selected_columns = ['sr_no', 'item_code', 'pr_no', 'report_date']
        service = ReportService(request_params, self.company_id)
        result = service.get_report_data(selected_columns)
        
        self.assertEqual(len(result['data']), 2)
        self.assertEqual(result['headers'][0]['title'], 'Sr No')
        self.assertEqual(result['report_type_label'], 'PR')
        mock_pr.all.assert_called_once()

    def test_get_report_data_spr_type(self, mock_spr, mock_pr, mock_po):
        mock_spr.all.return_value.filter.return_value.values.return_value = MOCK_SPR_DATA
        
        request_params = {'report_type_flag': '3'}
        selected_columns = ['sr_no', 'item_code', 'spr_no', 'report_date']
        service = ReportService(request_params, self.company_id)
        result = service.get_report_data(selected_columns)
        
        self.assertEqual(len(result['data']), 1)
        self.assertEqual(result['headers'][0]['title'], 'Sr No')
        self.assertEqual(result['report_type_label'], 'SPR')
        mock_spr.all.assert_called_once()

    def test_get_report_data_with_filters(self, mock_spr, mock_pr, mock_po):
        mock_po.all.return_value.filter.return_value.values.return_value = [MOCK_PO_DATA[0]]
        
        request_params = {'report_type_flag': '1', 'type': '1', 'No': 'PO001'} # Filter by PO No
        selected_columns = ['sr_no', 'item_code', 'po_no']
        service = ReportService(request_params, self.company_id)
        result = service.get_report_data(selected_columns)
        
        self.assertEqual(len(result['data']), 1)
        self.assertEqual(result['data'][0]['po_no'], 'PO001')
        mock_po.all.return_value.filter.assert_called_with(company_id=self.company_id, po_no='PO001') # Verify filter is passed

    def test_get_selectable_columns(self, mock_spr, mock_pr, mock_po):
        service = ReportService({'report_type_flag': '1'}, self.company_id)
        columns = service.get_selectable_columns()
        self.assertGreater(len(columns), 0)
        self.assertIn({'field_name': 'sr_no', 'display_name': 'Sr No', 'default_selected': True}, columns)
        self.assertIn({'field_name': 'po_no', 'display_name': 'PO No', 'default_selected': True}, columns)

    def test_get_initial_selected_columns(self, mock_spr, mock_pr, mock_po):
        service = ReportService({'report_type_flag': '1'}, self.company_id)
        initial_cols = service.get_initial_selected_columns()
        self.assertIn('sr_no', initial_cols)
        self.assertIn('item_code', initial_cols)
        self.assertIn('description', initial_cols)
        self.assertIn('uom', initial_cols)


# Integration Tests for Views
@patch('material_reports.services.ReportService.get_report_data')
@patch('material_reports.services.ReportService.get_selectable_columns')
@patch('material_reports.services.ReportService.get_initial_selected_columns')
@patch('material_reports.services.ReportService.get_report_type_label')
class ReportViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.report_search_url = reverse('material_reports:search_report')
        self.report_table_partial_url = reverse('material_reports:report_table_partial')
        self.report_export_excel_url = reverse('material_reports:report_export_excel')

        # Set a dummy session company ID
        session = self.client.session
        session['compid'] = 1
        session.save()
        
        self.mock_report_data = {
            'headers': [{'data': 'sr_no', 'title': 'Sr No'}, {'data': 'item_code', 'title': 'Item Code'}],
            'data': [{'sr_no': 1, 'item_code': 'TEST001'}, {'sr_no': 2, 'item_code': 'TEST002'}],
            'report_type_label': 'PO'
        }
        self.mock_empty_report_data = {
            'headers': [], 'data': [], 'report_type_label': 'PO'
        }
        self.mock_selectable_columns = [
            {'field_name': 'sr_no', 'display_name': 'Sr No', 'default_selected': True},
            {'field_name': 'item_code', 'display_name': 'Item Code', 'default_selected': True},
            {'field_name': 'description', 'display_name': 'Description', 'default_selected': False}
        ]
        self.mock_initial_selected_columns = ['sr_no', 'item_code']
        self.mock_report_type_label = 'PO'

    def test_report_search_view_get(self, mock_get_label, mock_get_initial, mock_get_selectable, mock_get_data):
        mock_get_selectable.return_value = self.mock_selectable_columns
        mock_get_initial.return_value = self.mock_initial_selected_columns
        mock_get_label.return_value = self.mock_report_type_label

        response = self.client.get(self.report_search_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_reports/search_report.html')
        self.assertContains(response, 'Material Management Reports')
        self.assertContains(response, 'id="report-filter-form"')
        self.assertIn('form', response.context)
        self.assertIn('report_type_label', response.context)
        self.assertEqual(response.context['report_type_label'], 'PO')
        self.assertEqual(response.context['form'].fields['selected_columns'].initial, ['sr_no', 'item_code'])
        self.assertEqual(response.context['form'].fields['selected_columns'].choices[0][0], 'sr_no') # Check choices populated

    def test_report_table_partial_view_get(self, mock_get_label, mock_get_initial, mock_get_selectable, mock_get_data):
        mock_get_data.return_value = self.mock_report_data
        mock_get_selectable.return_value = self.mock_selectable_columns
        mock_get_initial.return_value = self.mock_initial_selected_columns
        mock_get_label.return_value = self.mock_report_type_label
        
        # Simulate HTMX request for the table
        response = self.client.get(
            self.report_table_partial_url,
            {'report_type_flag': '1', 'selected_columns': ['sr_no', 'item_code']},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_reports/_report_table.html')
        self.assertContains(response, '<table id="report-data-table"')
        self.assertContains(response, 'TEST001')
        mock_get_data.assert_called_once()
        args, kwargs = mock_get_data.call_args
        self.assertEqual(args[0], ['sr_no', 'item_code']) # Check selected_columns passed

    def test_report_table_partial_view_get_no_data(self, mock_get_label, mock_get_initial, mock_get_selectable, mock_get_data):
        mock_get_data.return_value = self.mock_empty_report_data
        mock_get_selectable.return_value = self.mock_selectable_columns
        mock_get_initial.return_value = self.mock_initial_selected_columns
        mock_get_label.return_value = self.mock_report_type_label

        response = self.client.get(
            self.report_table_partial_url,
            {'report_type_flag': '1', 'selected_columns': ['sr_no', 'item_code']},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_reports/_report_table.html')
        self.assertContains(response, 'No records found matching your criteria.')
        self.assertNotContains(response, '<table id="report-data-table"')

    def test_report_export_excel_view(self, mock_get_label, mock_get_initial, mock_get_selectable, mock_get_data):
        mock_get_data.return_value = self.mock_report_data
        mock_get_selectable.return_value = self.mock_selectable_columns
        mock_get_initial.return_value = self.mock_initial_selected_columns
        mock_get_label.return_value = self.mock_report_type_label

        response = self.client.get(
            self.report_export_excel_url,
            {'report_type_flag': '1', 'selected_columns': ['sr_no', 'item_code']},
            HTTP_HX_REQUEST='true' # Simulating HTMX boost
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        self.assertIn('attachment; filename="report.xlsx"', response['Content-Disposition'])
        mock_get_data.assert_called_once()
        
    def test_report_export_excel_view_no_data(self, mock_get_label, mock_get_initial, mock_get_selectable, mock_get_data):
        mock_get_data.return_value = self.mock_empty_report_data
        mock_get_selectable.return_value = self.mock_selectable_columns
        mock_get_initial.return_value = self.mock_initial_selected_columns
        mock_get_label.return_value = self.mock_report_type_label

        response = self.client.get(
            self.report_export_excel_url,
            {'report_type_flag': '1', 'selected_columns': ['sr_no', 'item_code']},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 204) # HTTP 204 No Content for HTMX message
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('showMessage', response.headers['HX-Trigger']) # Ensure trigger for message is sent

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
-   **HTMX for Dynamic Updates:**
    -   The main form (`#report-filter-form`) uses `hx-get` to submit filter parameters and selected columns to `{% url 'material_reports:report_table_partial' %}`.
    -   `hx-target="#report-table-container"` and `hx-swap="innerHTML"` ensure that only the table section of the page is reloaded.
    -   A loading indicator (`hx-indicator="#loading-indicator"`) provides visual feedback during data fetching.
    -   The "Export To Excel" button uses `hx-boost="true"` and `hx-vals="js:htmx.serialize(htmx.find('#report-filter-form'))"` to include current form data in the export request without a full page load. `hx-target="body" hx-swap="none"` prevents UI changes, just triggers download.
    -   `HX-Trigger` headers are used in `ReportExportExcelView` to send a `showMessage` event if there's no data to export, allowing client-side JS to react.

-   **Alpine.js for UI State Management:**
    -   Alpine.js is used to manage the "Check All" checkbox functionality, ensuring that all `selected_columns` checkboxes are toggled correctly, mirroring the ASP.NET behavior. This is a simple client-side interaction that doesn't require server roundtrips.

-   **DataTables for List Views:**
    -   The `_report_table.html` partial renders the data within a standard `<table>` element.
    -   In `search_report.html`, after HTMX swaps the content, JavaScript code in `htmx:afterSwap` event listener detects the new table and initializes DataTables on `#report-data-table`.
    -   This handles all client-side searching, sorting, and pagination efficiently without additional server calls for these operations.

-   **DRY Template Inheritance:**
    -   All templates (`search_report.html`, `_report_table.html`) extend `core/base.html` (which is assumed to exist and provides common layout, CDN links for Tailwind CSS, HTMX, Alpine.js, jQuery, and DataTables). This adheres to the DRY principle.

**Final Notes:**
-   The migration prioritizes a phased approach, starting with read-only functionality and progressively adding more complex features if they were present in the original application.
-   The current solution directly translates the dynamic column selection logic, which is powerful but should be reviewed for potential performance bottlenecks with extremely large datasets or very complex queries. For such cases, server-side DataTables processing (via a separate API endpoint) might be considered.
-   The `company_id` is retrieved from `request.session`, mimicking the ASP.NET `Session["compid"]`. Proper authentication and authorization for Django users would be crucial in a full system.
-   The date handling for string-based date columns in the database is a direct translation but should ideally be refactored to use proper Django `DateField` or `DateTimeField` and `datetime` objects for robustness and locale-awareness. This would require database schema changes if dates are not stored in a standard format or changing the view logic.
-   The Excel export now uses `pandas` and `openpyxl` to generate a proper `.xlsx` file, which is a significant improvement over rendering HTML.