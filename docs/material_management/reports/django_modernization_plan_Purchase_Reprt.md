This comprehensive modernization plan will guide the transition of your ASP.NET Purchase Report application to a robust, modern Django solution. We will leverage AI-assisted automation by defining clear, systematic steps and reusable components, minimizing manual coding where possible. Our approach prioritizes a "Fat Model, Thin View" architecture, dynamic frontend interactions using HTMX and Alpine.js, and efficient data presentation with DataTables.

## ASP.NET to Django Conversion Script: Purchase Report

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

The ASP.NET Purchase Report is not directly tied to a single database table for CRUD operations. Instead, it aggregates and processes data from multiple interconnected tables to generate comprehensive reports. For this migration, we will define Django models for these underlying source tables, which will maintain `managed = False` to connect to your existing database.

The key tables involved in the report generation, as inferred from the SQL queries in the C# code-behind, are:

*   **`tblACC_BillBooking_Master`**: Contains high-level bill booking information, including system date, supplier, and company.
*   **`tblACC_BillBooking_Details`**: Holds detailed line items for bill bookings, linking to POs, quality checks, service notes, and containing various financial amounts (PF, Excise, VAT, CST, Freight).
*   **`tblQc_MaterialQuality_Details`**: Tracks accepted quantities for quality-controlled materials (used in Excise and one VAT/CST report).
*   **`tblinv_MaterialServiceNote_Details`**: Tracks received quantities for material/service notes (used in the VAT/CST Labour report).
*   **`tblMM_PO_Details`**: Stores details about purchase order line items, including rate, discount, and references to tax/charge masters.
*   **`tblMM_Supplier_master`**: Provides supplier information like names and IDs.
*   **`tblPacking_Master`**: Stores terms for Packing & Forwarding (PF).
*   **`tblExciseser_Master`**: Defines Excise and Service Tax terms, including cess details.
*   **`tblVAT_Master`**: Contains VAT and CST terms, including flags to determine if it's a VAT or CST type.
*   **`tblCompany_Master`**: (Inferred) Likely holds company information for `CompId` and `Address` lookup.

These tables will be mapped to individual Django models in `material_management/models.py`.

### Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

The provided ASP.NET code specifically implements a **Reporting** functionality. It focuses on **Reading** and aggregating data from the identified tables based on user-provided date ranges to generate different types of purchase reports (Excise, VAT/CST, VAT/CST Labour).

**No explicit Create, Update, or Delete (CRUD) operations are performed on this specific ASP.NET page.** This means that in the Django migration, we will focus on building a robust reporting system rather than standard CRUD forms and views for these underlying tables on this specific page.

**Validation Logic**: The ASP.NET page includes client-side validation for date inputs (required fields, date format, and date range comparison). This validation will be replicated in Django Forms to ensure data integrity.

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

The UI components in the ASP.NET page will be translated to modern web standards, emphasizing dynamic interactions and a seamless user experience:

*   **`cc1:TabContainer` (AJAX Control Toolkit Tabs)**: This will be replaced by a modern tabbed interface built using standard HTML and enhanced with **HTMX**. Each tab will dynamically load its content (the date selection form and report table) via an HTMX `GET` request.
*   **`asp:TextBox` with `cc1:CalendarExtender`**: These date input fields will be converted to standard HTML5 `<input type="date">` elements. Tailwind CSS will style them, and if advanced date picker features are required, a lightweight library integrated with Alpine.js can be used, though the browser's native date picker offers good usability.
*   **`asp:Button` (Search Buttons)**: These will become simple HTML `<button>` elements. Their functionality will be driven by **HTMX** `hx-get` requests, sending the selected date range to the Django backend to fetch and display the report data without a full page reload.
*   **`CR:CrystalReportViewer` (Crystal Reports)**: This is a proprietary server-side reporting tool. In Django, this functionality will be replaced by:
    *   **"Fat Model" Logic**: The complex SQL queries and data aggregation/calculation logic (previously in the C# code-behind) will be moved into a dedicated service class or custom manager within the Django `models.py` file. This centralizes the business logic.
    *   **Dynamic HTML DataTables**: The processed report data will be rendered into structured HTML `<table>` elements. These tables will then be initialized with the **DataTables.js** library on the client-side to provide interactive features like searching, sorting, pagination, and export options, offering a highly responsive user experience.

### Step 4: Generate Django Code

We will create a Django application named `material_management` to encapsulate all the necessary code for this Purchase Report module.

#### 4.1 Models (`material_management/models.py`)

We define models for each underlying database table, setting `managed = False` to connect to your existing schema. We also include a `PurchaseReportService` class that encapsulates the complex data retrieval and aggregation logic, demonstrating the "Fat Model" principle.

```python
from django.db import models
from django.db.models import Sum, F, ExpressionWrapper, fields, Case, When
from django.db.models.functions import Coalesce
from datetime import datetime, date

class Company(models.Model):
    """
    Represents the Company Master table.
    Used for retrieving company-specific details like address.
    """
    id = models.IntegerField(db_column='CompId', primary_key=True)
    name = models.CharField(db_column='CompanyName', max_length=255)
    # Add other fields like address if available in tblCompany_Master
    address = models.CharField(db_column='Address', max_length=500, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCompany_Master' # Placeholder table name, adjust if different
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name

    @classmethod
    def get_company_address(cls, company_id):
        """
        Fetches company address.
        Corresponds to the 'fun.CompAdd' logic in ASP.NET.
        """
        try:
            company = cls.objects.get(id=company_id)
            return company.address if company.address else "Company address not available."
        except cls.DoesNotExist:
            return "Company Address Not Found."


class Supplier(models.Model):
    """Represents the Supplier Master table."""
    supplier_id = models.IntegerField(db_column='SupplierId', primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.supplier_name


class PackingMaster(models.Model):
    """Represents the Packing Master table for PF terms."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    value = models.CharField(db_column='Value', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblPacking_Master'
        verbose_name = 'Packing Term'
        verbose_name_plural = 'Packing Terms'

    def __str__(self):
        return self.value


class ExciseServiceMaster(models.Model):
    """Represents the Excise/Service Master table for tax terms."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    value = models.CharField(db_column='Value', max_length=255)
    accessable_value = models.CharField(db_column='AccessableValue', max_length=255, blank=True, null=True)
    edu_cess = models.CharField(db_column='EDUCess', max_length=255, blank=True, null=True)
    she_cess = models.CharField(db_column='SHECess', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblExciseser_Master'
        verbose_name = 'Excise/Service Term'
        verbose_name_plural = 'Excise/Service Terms'

    def __str__(self):
        return self.value


class VatMaster(models.Model):
    """Represents the VAT Master table for VAT/CST terms."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    value = models.CharField(db_column='Value', max_length=255)
    is_vat = models.BooleanField(db_column='IsVAT')
    is_cst = models.BooleanField(db_column='IsCST')

    class Meta:
        managed = False
        db_table = 'tblVAT_Master'
        verbose_name = 'VAT Term'
        verbose_name_plural = 'VAT Terms'

    def __str__(self):
        return self.value


class PoDetail(models.Model):
    """Represents the Purchase Order Details table."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    pf = models.ForeignKey(PackingMaster, models.DO_NOTHING, db_column='PF', related_name='po_details_pf')
    ex_st = models.ForeignKey(ExciseServiceMaster, models.DO_NOTHING, db_column='ExST', related_name='po_details_ex_st')
    vat = models.ForeignKey(VatMaster, models.DO_NOTHING, db_column='VAT', related_name='po_details_vat')
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=4)
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=4)

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Details'
        verbose_name = 'PO Detail'
        verbose_name_plural = 'PO Details'

    def __str__(self):
        return f"PO Detail {self.id}"


class BillBookingMaster(models.Model):
    """Represents the Bill Booking Master table."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate')
    supplier = models.ForeignKey(Supplier, models.DO_NOTHING, db_column='SupplierId')
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblACC_BillBooking_Master'
        verbose_name = 'Bill Booking Master'
        verbose_name_plural = 'Bill Booking Masters'

    def __str__(self):
        return f"Bill Booking {self.id} on {self.sys_date}"


class QcMaterialQualityDetail(models.Model):
    """Represents Quality Control Material Quality Details."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    accepted_qty = models.DecimalField(db_column='AcceptedQty', max_digits=18, decimal_places=4)

    class Meta:
        managed = False
        db_table = 'tblQc_MaterialQuality_Details'
        verbose_name = 'QC Material Quality Detail'
        verbose_name_plural = 'QC Material Quality Details'

    def __str__(self):
        return f"QC Detail {self.id}"


class InvMaterialServiceNoteDetail(models.Model):
    """Represents Inventory Material Service Note Details (for labour/service type)."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    received_qty = models.DecimalField(db_column='ReceivedQty', max_digits=18, decimal_places=4)

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialServiceNote_Details'
        verbose_name = 'Inventory Material Service Note Detail'
        verbose_name_plural = 'Inventory Material Service Note Details'

    def __str__(self):
        return f"Inv Service Note Detail {self.id}"


class BillBookingDetail(models.Model):
    """Represents the Bill Booking Details table, linking various components."""
    mid = models.ForeignKey(BillBookingMaster, models.DO_NOTHING, db_column='MId', related_name='details')
    gqn_id = models.ForeignKey(QcMaterialQualityDetail, models.DO_NOTHING, db_column='GQNId', blank=True, null=True, related_name='bill_booking_details_qc')
    gsn_id = models.ForeignKey(InvMaterialServiceNoteDetail, models.DO_NOTHING, db_column='GSNId', blank=True, null=True, related_name='bill_booking_details_inv')
    pod_id = models.ForeignKey(PoDetail, models.DO_NOTHING, db_column='PODId', related_name='bill_booking_details_po')
    pf_amt = models.DecimalField(db_column='PFAmt', max_digits=18, decimal_places=4)
    ex_st_basic = models.DecimalField(db_column='ExStBasic', max_digits=18, decimal_places=4)
    ex_st_educess = models.DecimalField(db_column='ExStEducess', max_digits=18, decimal_places=4)
    ex_st_shecess = models.DecimalField(db_column='ExStShecess', max_digits=18, decimal_places=4)
    vat = models.DecimalField(db_column='VAT', max_digits=18, decimal_places=4)
    cst = models.DecimalField(db_column='CST', max_digits=18, decimal_places=4)
    freight = models.DecimalField(db_column='Freight', max_digits=18, decimal_places=4)

    class Meta:
        managed = False
        db_table = 'tblACC_BillBooking_Details'
        verbose_name = 'Bill Booking Detail'
        verbose_name_plural = 'Bill Booking Details'

    def __str__(self):
        return f"Detail for Bill Booking {self.mid.id}"


class PurchaseReportService:
    """
    A service class encapsulating the complex logic for generating various purchase reports.
    This adheres to the "Fat Model" principle by keeping sophisticated business logic
    out of the views, making them thin and focused on orchestrating requests.
    """

    @staticmethod
    def _calculate_base_amount(po_detail, quantity):
        """Calculates accepted/received amount based on PO rate and discount."""
        if po_detail and quantity is not None:
            return quantity * (po_detail.rate - (po_detail.rate * po_detail.discount / 100))
        return 0.0

    @staticmethod
    def _get_common_report_lines(from_date, to_date, company_id):
        """
        Retrieves and processes common raw data for all report types.
        This method mirrors the base SQL query and initial data mapping
        from the C# code's data reader loop.
        """
        # Optimized query using select_related for joins
        bill_details_qs = BillBookingDetail.objects.filter(
            mid__company__id=company_id,
            mid__sys_date__range=(from_date, to_date)
        ).select_related(
            'mid__supplier', 'mid__company',
            'pod_id__pf', 'pod_id__ex_st', 'pod_id__vat',
            'gqn_id',
            'gsn_id'
        )

        report_data = []
        for detail in bill_details_qs:
            po_detail = detail.pod_id
            
            # Determine quantity based on which FK is present
            quantity = 0
            if detail.gqn_id:
                quantity = detail.gqn_id.accepted_qty
            elif detail.gsn_id:
                quantity = detail.gsn_id.received_qty

            basic_amt = PurchaseReportService._calculate_base_amount(po_detail, quantity)

            # Retrieve lookup values for PF, Excise, VAT/CST terms
            pf_terms = po_detail.pf.value if po_detail.pf else ''
            ex_st_terms = po_detail.ex_st.value if po_detail.ex_st else ''
            ex_st_accessable_value = po_detail.ex_st.accessable_value if po_detail.ex_st else ''
            edu_cess_rate = po_detail.ex_st.edu_cess if po_detail.ex_st else ''
            she_cess_rate = po_detail.ex_st.she_cess if po_detail.ex_st else ''
            vat_cst_terms = po_detail.vat.value if po_detail.vat else ''
            is_vat = po_detail.vat.is_vat if po_detail.vat else False
            is_cst = po_detail.vat.is_cst if po_detail.vat else False

            # Calculate line item excise amount
            excise_amt = detail.ex_st_basic + detail.ex_st_educess + detail.ex_st_shecess

            # Determine actual VAT/CST amount for the line
            line_vat_cst_amt = detail.vat if is_vat else detail.cst # As per C# logic

            row_data = {
                'sys_date': detail.mid.sys_date,
                'comp_id': company_id,
                'supplier_info': f"{detail.mid.supplier.supplier_name} [{detail.mid.supplier.supplier_id}]",
                'basic_amt': float(basic_amt),
                'pf_terms': pf_terms,
                'pf_amt': float(detail.pf_amt),
                'excise_values': ex_st_terms,
                'excise_amt': float(excise_amt),
                'edu_cess': edu_cess_rate,
                'edu_value': float(detail.ex_st_educess),
                'she_cess': she_cess_rate,
                'she_value': float(detail.ex_st_shecess),
                'vat_cst_terms': vat_cst_terms,
                'vat_cst_amt': float(line_vat_cst_amt),
                'freight_amt': float(detail.freight),
                'excise_basic_value': ex_st_accessable_value,
                'ex_basic_amt': float(detail.ex_st_basic), # This seems to be eduBasic in C#
                'is_vat_entry': is_vat, # Keep original vat/cst flag for later filtering
                'is_cst_entry': is_cst,
                'gqn_id_exists': detail.gqn_id is not None, # Helper flag for report filtering
                'gsn_id_exists': detail.gsn_id is not None, # Helper flag for report filtering
            }
            report_data.append(row_data)
        return report_data

    @staticmethod
    def _finalize_report_output(report_lines, include_vat_cst_and_freight_in_total=True):
        """
        Calculates totals and MAHPurchase string for a given set of report lines.
        This consolidates common Crystal Reports parameter logic.
        """
        vat_gross_total = 0.0
        cst_gross_total = 0.0
        total_excise = 0.0
        vat_cst_grouping = {}
        
        filtered_vat_lines = []
        filtered_cst_lines = []

        for row in report_lines:
            # Total calculation logic from C# differs based on report type
            row_total_amt = row['basic_amt'] + row['pf_amt'] + row['excise_amt']
            if include_vat_cst_and_freight_in_total:
                row_total_amt += row['vat_cst_amt'] + row['freight_amt']
            
            row['total_amt'] = row_total_amt
            total_excise += row['excise_amt']

            # Categorize into VAT or CST for gross totals and separate tables
            if row['is_cst_entry']:
                cst_gross_total += row_total_amt
                filtered_cst_lines.append(row)
            else: # If not CST, it's considered VAT by C# logic (even if IsVAT is false)
                vat_gross_total += row_total_amt
                filtered_vat_lines.append(row)

            # Accumulate for MAHPurchase grouping (VATCSTTerms, VATCSTAmt)
            # This aggregates the actual VAT/CST value, not the total line amount
            term = row['vat_cst_terms']
            vat_cst_grouping.setdefault(term, 0.0)
            vat_cst_grouping[term] += row['vat_cst_amt']

        # Format MAHPurchase string
        mah_purchase = ", ".join([f"@ {term} Amt: {amt:.2f}" for term, amt in vat_cst_grouping.items()])

        return {
            'report_data_vat': filtered_vat_lines,
            'report_data_cst': filtered_cst_lines,
            'vat_gross_total': vat_gross_total,
            'cst_gross_total': cst_gross_total,
            'total_excise': total_excise,
            'mah_purchase': mah_purchase,
        }

    @staticmethod
    def get_excise_report(from_date: date, to_date: date, company_id: int):
        """
        Generates the Excise report data.
        Equivalent to cryrpt_create2() in C#, which merges data from both QC and Inv service notes.
        """
        common_lines = PurchaseReportService._get_common_report_lines(from_date, to_date, company_id)
        # For excise, the C# code's total calculation does NOT include VAT/CST or Freight.
        # It's basic_amt + pf_amt + exba + edu + she.
        return PurchaseReportService._finalize_report_output(
            common_lines,
            include_vat_cst_and_freight_in_total=False # Crucial difference for Excise
        )

    @staticmethod
    def get_vat_cst_report(from_date: date, to_date: date, company_id: int):
        """
        Generates the VAT/CST report data (excluding Labour).
        Equivalent to cryrpt_create(0) in C#, focusing on tblQc_MaterialQuality_Details data.
        """
        common_lines = PurchaseReportService._get_common_report_lines(from_date, to_date, company_id)
        # Filter for entries that originated from tblQc_MaterialQuality_Details (GQNId).
        # This is a simplification; a more precise ORM query with conditional joins would be ideal.
        filtered_lines = [row for row in common_lines if row['gqn_id_exists']]
        
        # For VAT/CST, the C# code's total calculation INCLUDES VAT/CST and Freight.
        return PurchaseReportService._finalize_report_output(
            filtered_lines,
            include_vat_cst_and_freight_in_total=True
        )

    @staticmethod
    def get_vat_cst_labour_report(from_date: date, to_date: date, company_id: int):
        """
        Generates the VAT/CST (Labour) report data.
        Equivalent to cryrpt_create(1) in C#, focusing on tblinv_MaterialServiceNote_Details data.
        """
        common_lines = PurchaseReportService._get_common_report_lines(from_date, to_date, company_id)
        # Filter for entries that originated from tblinv_MaterialServiceNote_Details (GSNId).
        filtered_lines = [row for row in common_lines if row['gsn_id_exists']]

        # For VAT/CST Labour, the C# code's total calculation INCLUDES VAT/CST and Freight.
        return PurchaseReportService._finalize_report_output(
            filtered_lines,
            include_vat_cst_and_freight_in_total=True
        )

```

#### 4.2 Forms (`material_management/forms.py`)

A simple form for capturing the date range inputs, including validation logic.

```python
from django import forms
from datetime import date, timedelta

class PurchaseReportForm(forms.Form):
    """
    Form to handle date range selection for purchase reports.
    Includes validation to ensure date logic and format are correct.
    """
    from_date = forms.DateField(
        label="Date From",
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        }),
        # Set initial value to first day of current month for convenience
        initial=date.today().replace(day=1)
    )
    to_date = forms.DateField(
        label="To",
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        }),
        # Set initial value to last day of current month for convenience
        initial=(date.today().replace(day=1) + timedelta(days=32)).replace(day=1) - timedelta(days=1)
    )

    def clean(self):
        """
        Custom clean method for cross-field validation, specifically for date range.
        Mirrors ASP.NET's CompareValidator.
        """
        cleaned_data = super().clean()
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')

        if from_date and to_date and from_date > to_date:
            self.add_error('from_date', "Invalid date range: 'Date From' cannot be after 'To Date'.")
            self.add_error('to_date', "Invalid date range: 'Date From' cannot be after 'To Date'.")
        return cleaned_data

```

#### 4.3 Views (`material_management/views.py`)

We will use a `TemplateView` for the main page and separate `TemplateView`s or `View`s for the HTMX-loaded partials. Views will be thin, delegating all complex reporting logic to `PurchaseReportService` in `models.py`.

```python
from django.views.generic import TemplateView, View
from django.http import JsonResponse, HttpResponse
from django.shortcuts import render
from datetime import date
from django.contrib import messages
from .forms import PurchaseReportForm
from .models import PurchaseReportService, Company # Import Company for address lookup
import json # For potential JSON responses in HTMX

class PurchaseReportMainView(TemplateView):
    """
    Main view for the Purchase Report page.
    Renders the initial page with date range forms and sets up HTMX tabs.
    Corresponds to the overall ASP.NET Purchase_Reprt.aspx page.
    """
    template_name = 'material_management/purchase_report/main.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize forms with default values for the current month, as in ASP.NET Page_Init
        context['excise_form'] = PurchaseReportForm(
            initial={
                'from_date': date.today().replace(day=1),
                'to_date': (date.today().replace(day=1) + timedelta(days=32)).replace(day=1) - timedelta(days=1)
            }
        )
        context['vat_cst_form'] = PurchaseReportForm(
            initial={
                'from_date': date.today().replace(day=1),
                'to_date': (date.today().replace(day=1) + timedelta(days=32)).replace(day=1) - timedelta(days=1)
            }
        )
        context['vat_cst_labour_form'] = PurchaseReportForm(
            initial={
                'from_date': date.today().replace(day=1),
                'to_date': (date.today().replace(day=1) + timedelta(days=32)).replace(day=1) - timedelta(days=1)
            }
        )
        return context

class ExciseReportPartialView(View):
    """
    Handles fetching and rendering the Excise report data via HTMX.
    Corresponds to the logic in cryrpt_create2().
    """
    def get(self, request, *args, **kwargs):
        form = PurchaseReportForm(request.GET)
        report_data = None
        company_address = ""
        report_params = {}

        if form.is_valid():
            from_date = form.cleaned_data['from_date']
            to_date = form.cleaned_data['to_date']
            # Assuming company ID is retrieved from session or user profile, similar to ASP.NET
            company_id = request.session.get('compid', 1) # Default to 1 if not in session

            report_output = PurchaseReportService.get_excise_report(from_date, to_date, company_id)
            report_data = report_output['report_data_vat'] + report_output['report_data_cst'] # Excise report merges both
            
            # Fetch company address
            company_address = Company.get_company_address(company_id)

            report_params = {
                'FDate': from_date.strftime('%d-%m-%Y'),
                'TDate': to_date.strftime('%d-%m-%Y'),
                'VATGrossTotal': f"{report_output['vat_gross_total']:.2f}",
                'CSTGrossTotal': f"{report_output['cst_gross_total']:.2f}",
                'TotalExcise': f"{report_output['total_excise']:.2f}",
                'MAHPurchase': report_output['mah_purchase'],
                'Address': company_address,
            }
            messages.success(request, "Excise Report generated successfully.")
        else:
            messages.error(request, "Please correct the date errors to generate the Excise report.")

        context = {
            'form': form,
            'report_data': report_data,
            'report_params': report_params,
            'report_type': 'Excise',
            'show_no_record_message': not (report_data), # Similar to ASP.NET Visible=False
        }
        return render(request, 'material_management/purchase_report/_excise_report.html', context)

class VatCstReportPartialView(View):
    """
    Handles fetching and rendering the VAT/CST report data via HTMX.
    Corresponds to cryrpt_create(0) in C#.
    """
    def get(self, request, *args, **kwargs):
        form = PurchaseReportForm(request.GET)
        report_data_vat = []
        report_data_cst = []
        company_address = ""
        report_params = {}

        if form.is_valid():
            from_date = form.cleaned_data['from_date']
            to_date = form.cleaned_data['to_date']
            company_id = request.session.get('compid', 1)

            report_output = PurchaseReportService.get_vat_cst_report(from_date, to_date, company_id)
            report_data_vat = report_output['report_data_vat']
            report_data_cst = report_output['report_data_cst']

            company_address = Company.get_company_address(company_id)

            report_params = {
                'FDate': from_date.strftime('%d-%m-%Y'),
                'TDate': to_date.strftime('%d-%m-%Y'),
                'VATGrossTotal': f"{report_output['vat_gross_total']:.2f}",
                'CSTGrossTotal': f"{report_output['cst_gross_total']:.2f}",
                'TotalExcise': f"{report_output['total_excise']:.2f}",
                'MAHPurchase': report_output['mah_purchase'],
                'Address': company_address,
            }
            messages.success(request, "VAT/CST Report generated successfully.")
        else:
            messages.error(request, "Please correct the date errors to generate the VAT/CST report.")

        context = {
            'form': form,
            'report_data_vat': report_data_vat,
            'report_data_cst': report_data_cst,
            'report_params': report_params,
            'report_type': 'VAT/CST',
            'show_no_record_message': not (report_data_vat or report_data_cst),
        }
        return render(request, 'material_management/purchase_report/_vat_cst_report.html', context)

class VatCstLabourReportPartialView(View):
    """
    Handles fetching and rendering the VAT/CST (Labour) report data via HTMX.
    Corresponds to cryrpt_create(1) in C#.
    """
    def get(self, request, *args, **kwargs):
        form = PurchaseReportForm(request.GET)
        report_data_vat = []
        report_data_cst = []
        company_address = ""
        report_params = {}

        if form.is_valid():
            from_date = form.cleaned_data['from_date']
            to_date = form.cleaned_data['to_date']
            company_id = request.session.get('compid', 1)

            report_output = PurchaseReportService.get_vat_cst_labour_report(from_date, to_date, company_id)
            report_data_vat = report_output['report_data_vat']
            report_data_cst = report_output['report_data_cst']
            
            company_address = Company.get_company_address(company_id)

            report_params = {
                'FDate': from_date.strftime('%d-%m-%Y'),
                'TDate': to_date.strftime('%d-%m-%Y'),
                'VATGrossTotal': f"{report_output['vat_gross_total']:.2f}",
                'CSTGrossTotal': f"{report_output['cst_gross_total']:.2f}",
                'TotalExcise': f"{report_output['total_excise']:.2f}",
                'MAHPurchase': report_output['mah_purchase'],
                'Address': company_address,
            }
            messages.success(request, "VAT/CST (Labour) Report generated successfully.")
        else:
            messages.error(request, "Please correct the date errors to generate the VAT/CST (Labour) report.")

        context = {
            'form': form,
            'report_data_vat': report_data_vat,
            'report_data_cst': report_data_cst,
            'report_params': report_params,
            'report_type': 'VAT/CST (Labour)',
            'show_no_record_message': not (report_data_vat or report_data_cst),
        }
        return render(request, 'material_management/purchase_report/_vat_cst_labour_report.html', context)

```

#### 4.4 Templates (`material_management/templates/material_management/purchase_report/`)

We will create a main template for the page structure and partial templates for each report tab, loaded dynamically via HTMX.

**`main.html`** (Main page structure for the Purchase Report)

```html
{% extends 'core/base.html' %}
{% load static %}

{% block head %}
    {{ block.super }}
    {# Any module-specific CSS/JS if not globally managed by base.html #}
    {# <link href="{% static 'material_management/css/report.css' %}" rel="stylesheet" type="text/css" /> #}
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Purchase Report</h2>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6">
        <div x-data="{ activeTab: 'excise' }">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                    <button @click="activeTab = 'excise'"
                            :class="{'border-indigo-500 text-indigo-600': activeTab === 'excise', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'excise'}"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                            hx-get="{% url 'material_management:excise_report_partial' %}"
                            hx-target="#report-content"
                            hx-swap="innerHTML"
                            hx-trigger="click, load once delay:100ms"
                            hx-indicator="#report-loader">
                        Excise
                    </button>
                    <button @click="activeTab = 'vat_cst'"
                            :class="{'border-indigo-500 text-indigo-600': activeTab === 'vat_cst', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'vat_cst'}"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                            hx-get="{% url 'material_management:vat_cst_report_partial' %}"
                            hx-target="#report-content"
                            hx-swap="innerHTML"
                            hx-indicator="#report-loader">
                        VAT/CST
                    </button>
                    <button @click="activeTab = 'vat_cst_labour'"
                            :class="{'border-indigo-500 text-indigo-600': activeTab === 'vat_cst_labour', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'vat_cst_labour'}"
                            class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                            hx-get="{% url 'material_management:vat_cst_labour_report_partial' %}"
                            hx-target="#report-content"
                            hx-swap="innerHTML"
                            hx-indicator="#report-loader">
                        VAT/CST (Labour)
                    </button>
                </nav>
            </div>

            <div class="mt-6">
                <!-- Loading indicator for HTMX content -->
                <div id="report-loader" class="htmx-indicator text-center py-4">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading report...</p>
                </div>
                <!-- Report content loaded via HTMX -->
                <div id="report-content">
                    {# Initial content will be loaded by hx-get on the first tab #}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('purchaseReportTabs', () => ({
            activeTab: 'excise', // Default active tab
            init() {
                // No explicit initial load needed here, HTMX handles it via hx-trigger="load once"
            }
        }));
    });

    // Event listener for HTMX after swap, to re-initialize DataTables
    document.body.addEventListener('htmx:afterSwap', function(event) {
        // Check if the swapped content contains DataTables-enabled tables
        if (event.detail.target.id === 'report-content') {
            $('.data-table').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true // Destroy existing DataTable instance if any
            });
        }
    });

    // Handle messages after HTMX requests
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.xhr.status === 200 && event.detail.xhr.getResponseHeader('HX-Trigger')) {
            const hxTrigger = JSON.parse(event.detail.xhr.getResponseHeader('HX-Trigger'));
            if (hxTrigger.showMessage) {
                // This would be handled by a global Alpine.js/HTMX message component
                // For now, it means a message was triggered successfully
                console.log("Message triggered:", hxTrigger.showMessage);
            }
        }
    });
</script>
{% endblock %}
```

**`_excise_report.html`** (Partial template for Excise report tab)

```html
<div class="p-4" x-data="{ reportForm: { from_date: '{{ excise_form.from_date.initial|date:"Y-m-d" }}', to_date: '{{ excise_form.to_date.initial|date:"Y-m-d" }}' } }">
    <form hx-get="{% url 'material_management:excise_report_partial' %}"
          hx-target="#report-content"
          hx-swap="innerHTML"
          hx-indicator="#report-loader">
        {% csrf_token %}
        <div class="flex items-center space-x-4 mb-6 fontcss">
            <div class="flex items-center space-x-2">
                <b>Date From:</b>
                <input type="date" name="from_date" 
                       value="{{ excise_form.from_date.value|default:excise_form.from_date.initial|date:'Y-m-d' }}" 
                       class="box3 block w-40 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                       x-model="reportForm.from_date">
                {% if excise_form.from_date.errors %}
                    <span class="text-red-500 text-xs">{{ excise_form.from_date.errors|striptags }}</span>
                {% endif %}
            </div>
            <div class="flex items-center space-x-2">
                <b>To:</b>
                <input type="date" name="to_date" 
                       value="{{ excise_form.to_date.value|default:excise_form.to_date.initial|date:'Y-m-d' }}" 
                       class="box3 block w-40 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                       x-model="reportForm.to_date">
                {% if excise_form.to_date.errors %}
                    <span class="text-red-500 text-xs">{{ excise_form.to_date.errors|striptags }}</span>
                {% endif %}
            </div>
            <b>
                <button type="submit" class="redbox bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Search
                </button>
            </b>
        </div>
        {% if excise_form.non_field_errors %}
            <p class="text-red-500 text-sm mb-4 style2">{{ excise_form.non_field_errors }}</p>
        {% endif %}
    </form>

    {% if show_no_record_message %}
        <p class="text-red-500 text-lg font-bold mt-4 style2">No record found!</p>
    {% else %}
        <div class="overflow-x-auto mt-4">
            <table class="data-table min-w-full bg-white border border-gray-200">
                <thead>
                    <tr>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sys Date</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Basic Amount</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PF Terms</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PF Amount</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Excise Values</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Excise Amount</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">EDU Cess</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">EDU Value</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SHE Cess</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SHE Value</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
                    </tr>
                </thead>
                <tbody>
                    {% for row in report_data %}
                    <tr>
                        <td class="py-2 px-4 border-b border-gray-200">{{ row.sys_date|date:"d-m-Y" }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ row.supplier_info }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.basic_amt|floatformat:2 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ row.pf_terms }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.pf_amt|floatformat:2 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ row.excise_values }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.excise_amt|floatformat:2 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ row.edu_cess }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.edu_value|floatformat:2 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ row.she_cess }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.she_value|floatformat:2 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right font-bold">{{ row.total_amt|floatformat:2 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <div class="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-md">
            <h4 class="text-lg font-semibold mb-2">Report Summary</h4>
            <p><strong>Period:</strong> {{ report_params.FDate }} - {{ report_params.TDate }}</p>
            <p><strong>Company Address:</strong> {{ report_params.Address }}</p>
            <p><strong>VAT Gross Total:</strong> {{ report_params.VATGrossTotal }}</p>
            <p><strong>CST Gross Total:</strong> {{ report_params.CSTGrossTotal }}</p>
            <p><strong>Total Excise:</strong> {{ report_params.TotalExcise }}</p>
            <p><strong>MAH Purchase:</strong> {{ report_params.MAHPurchase }}</p>
            <button class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded mt-4" 
                    onclick="window.print();">
                Print Report
            </button>
        </div>
    {% endif %}
</div>

```

**`_vat_cst_report.html`** (Partial template for VAT/CST report tab)

```html
<div class="p-4" x-data="{ reportForm: { from_date: '{{ vat_cst_form.from_date.initial|date:"Y-m-d" }}', to_date: '{{ vat_cst_form.to_date.initial|date:"Y-m-d" }}' } }">
    <form hx-get="{% url 'material_management:vat_cst_report_partial' %}"
          hx-target="#report-content"
          hx-swap="innerHTML"
          hx-indicator="#report-loader">
        {% csrf_token %}
        <div class="flex items-center space-x-4 mb-6 fontcss">
            <div class="flex items-center space-x-2">
                <b>Date From:</b>
                <input type="date" name="from_date" 
                       value="{{ vat_cst_form.from_date.value|default:vat_cst_form.from_date.initial|date:'Y-m-d' }}" 
                       class="box3 block w-40 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                       x-model="reportForm.from_date">
                {% if vat_cst_form.from_date.errors %}
                    <span class="text-red-500 text-xs">{{ vat_cst_form.from_date.errors|striptags }}</span>
                {% endif %}
            </div>
            <div class="flex items-center space-x-2">
                <b>To:</b>
                <input type="date" name="to_date" 
                       value="{{ vat_cst_form.to_date.value|default:vat_cst_form.to_date.initial|date:'Y-m-d' }}" 
                       class="box3 block w-40 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                       x-model="reportForm.to_date">
                {% if vat_cst_form.to_date.errors %}
                    <span class="text-red-500 text-xs">{{ vat_cst_form.to_date.errors|striptags }}</span>
                {% endif %}
            </div>
            <b>
                <button type="submit" class="redbox bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Search
                </button>
            </b>
        </div>
        {% if vat_cst_form.non_field_errors %}
            <p class="text-red-500 text-sm mb-4 style2">{{ vat_cst_form.non_field_errors }}</p>
        {% endif %}
    </form>

    {% if show_no_record_message %}
        <p class="text-red-500 text-lg font-bold mt-4 style2">No record found!</p>
    {% else %}
        <div class="overflow-x-auto mt-4">
            <h3 class="text-lg font-semibold mb-2">VAT Entries</h3>
            <table class="data-table min-w-full bg-white border border-gray-200 mb-6">
                <thead>
                    <tr>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sys Date</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Basic Amount</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PF Terms</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PF Amount</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Excise Values</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Excise Amount</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">VAT/CST Terms</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">VAT/CST Amount</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Freight Amount</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
                    </tr>
                </thead>
                <tbody>
                    {% for row in report_data_vat %}
                    <tr>
                        <td class="py-2 px-4 border-b border-gray-200">{{ row.sys_date|date:"d-m-Y" }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ row.supplier_info }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.basic_amt|floatformat:2 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ row.pf_terms }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.pf_amt|floatformat:2 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ row.excise_values }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.excise_amt|floatformat:2 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ row.vat_cst_terms }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.vat_cst_amt|floatformat:2 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.freight_amt|floatformat:2 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right font-bold">{{ row.total_amt|floatformat:2 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>

            <h3 class="text-lg font-semibold mb-2">CST Entries</h3>
            <table class="data-table min-w-full bg-white border border-gray-200">
                <thead>
                    <tr>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sys Date</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Basic Amount</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PF Terms</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PF Amount</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Excise Values</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Excise Amount</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">VAT/CST Terms</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">VAT/CST Amount</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Freight Amount</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
                    </tr>
                </thead>
                <tbody>
                    {% for row in report_data_cst %}
                    <tr>
                        <td class="py-2 px-4 border-b border-gray-200">{{ row.sys_date|date:"d-m-Y" }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ row.supplier_info }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.basic_amt|floatformat:2 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ row.pf_terms }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.pf_amt|floatformat:2 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ row.excise_values }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.excise_amt|floatformat:2 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ row.vat_cst_terms }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.vat_cst_amt|floatformat:2 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.freight_amt|floatformat:2 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right font-bold">{{ row.total_amt|floatformat:2 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <div class="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-md">
            <h4 class="text-lg font-semibold mb-2">Report Summary</h4>
            <p><strong>Period:</strong> {{ report_params.FDate }} - {{ report_params.TDate }}</p>
            <p><strong>Company Address:</strong> {{ report_params.Address }}</p>
            <p><strong>VAT Gross Total:</strong> {{ report_params.VATGrossTotal }}</p>
            <p><strong>CST Gross Total:</strong> {{ report_params.CSTGrossTotal }}</p>
            <p><strong>Total Excise:</strong> {{ report_params.TotalExcise }}</p>
            <p><strong>MAH Purchase:</strong> {{ report_params.MAHPurchase }}</p>
            <button class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded mt-4" 
                    onclick="window.print();">
                Print Report
            </button>
        </div>
    {% endif %}
</div>
```

**`_vat_cst_labour_report.html`** (Partial template for VAT/CST (Labour) report tab)

```html
<div class="p-4" x-data="{ reportForm: { from_date: '{{ vat_cst_labour_form.from_date.initial|date:"Y-m-d" }}', to_date: '{{ vat_cst_labour_form.to_date.initial|date:"Y-m-d" }}' } }">
    <form hx-get="{% url 'material_management:vat_cst_labour_report_partial' %}"
          hx-target="#report-content"
          hx-swap="innerHTML"
          hx-indicator="#report-loader">
        {% csrf_token %}
        <div class="flex items-center space-x-4 mb-6 fontcss">
            <div class="flex items-center space-x-2">
                <b>Date From:</b>
                <input type="date" name="from_date" 
                       value="{{ vat_cst_labour_form.from_date.value|default:vat_cst_labour_form.from_date.initial|date:'Y-m-d' }}" 
                       class="box3 block w-40 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                       x-model="reportForm.from_date">
                {% if vat_cst_labour_form.from_date.errors %}
                    <span class="text-red-500 text-xs">{{ vat_cst_labour_form.from_date.errors|striptags }}</span>
                {% endif %}
            </div>
            <div class="flex items-center space-x-2">
                <b>To:</b>
                <input type="date" name="to_date" 
                       value="{{ vat_cst_labour_form.to_date.value|default:vat_cst_labour_form.to_date.initial|date:'Y-m-d' }}" 
                       class="box3 block w-40 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                       x-model="reportForm.to_date">
                {% if vat_cst_labour_form.to_date.errors %}
                    <span class="text-red-500 text-xs">{{ vat_cst_labour_form.to_date.errors|striptags }}</span>
                {% endif %}
            </div>
            <b>
                <button type="submit" class="redbox bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Search
                </button>
            </b>
        </div>
        {% if vat_cst_labour_form.non_field_errors %}
            <p class="text-red-500 text-sm mb-4 style2">{{ vat_cst_labour_form.non_field_errors }}</p>
        {% endif %}
    </form>

    {% if show_no_record_message %}
        <p class="text-red-500 text-lg font-bold mt-4 style2">No record found!</p>
    {% else %}
        <div class="overflow-x-auto mt-4">
            <h3 class="text-lg font-semibold mb-2">VAT Entries (Labour)</h3>
            <table class="data-table min-w-full bg-white border border-gray-200 mb-6">
                <thead>
                    <tr>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sys Date</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Basic Amount</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PF Terms</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PF Amount</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Excise Values</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Excise Amount</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">VAT/CST Terms</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">VAT/CST Amount</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Freight Amount</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
                    </tr>
                </thead>
                <tbody>
                    {% for row in report_data_vat %}
                    <tr>
                        <td class="py-2 px-4 border-b border-gray-200">{{ row.sys_date|date:"d-m-Y" }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ row.supplier_info }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.basic_amt|floatformat:2 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ row.pf_terms }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.pf_amt|floatformat:2 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ row.excise_values }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.excise_amt|floatformat:2 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ row.vat_cst_terms }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.vat_cst_amt|floatformat:2 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.freight_amt|floatformat:2 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right font-bold">{{ row.total_amt|floatformat:2 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>

            <h3 class="text-lg font-semibold mb-2">CST Entries (Labour)</h3>
            <table class="data-table min-w-full bg-white border border-gray-200">
                <thead>
                    <tr>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sys Date</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Basic Amount</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PF Terms</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PF Amount</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Excise Values</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Excise Amount</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">VAT/CST Terms</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">VAT/CST Amount</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Freight Amount</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
                    </tr>
                </thead>
                <tbody>
                    {% for row in report_data_cst %}
                    <tr>
                        <td class="py-2 px-4 border-b border-gray-200">{{ row.sys_date|date:"d-m-Y" }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ row.supplier_info }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.basic_amt|floatformat:2 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ row.pf_terms }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.pf_amt|floatformat:2 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ row.excise_values }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.excise_amt|floatformat:2 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ row.vat_cst_terms }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.vat_cst_amt|floatformat:2 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.freight_amt|floatformat:2 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right font-bold">{{ row.total_amt|floatformat:2 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <div class="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-md">
            <h4 class="text-lg font-semibold mb-2">Report Summary</h4>
            <p><strong>Period:</strong> {{ report_params.FDate }} - {{ report_params.TDate }}</p>
            <p><strong>Company Address:</strong> {{ report_params.Address }}</p>
            <p><strong>VAT Gross Total:</strong> {{ report_params.VATGrossTotal }}</p>
            <p><strong>CST Gross Total:</strong> {{ report_params.CSTGrossTotal }}</p>
            <p><strong>Total Excise:</strong> {{ report_params.TotalExcise }}</p>
            <p><strong>MAH Purchase:</strong> {{ report_params.MAHPurchase }}</p>
            <button class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded mt-4" 
                    onclick="window.print();">
                Print Report
            </button>
        </div>
    {% endif %}
</div>
```

#### 4.5 URLs (`material_management/urls.py`)

Define the URL patterns for the main page and the HTMX-loaded partials.

```python
from django.urls import path
from .views import PurchaseReportMainView, ExciseReportPartialView, VatCstReportPartialView, VatCstLabourReportPartialView

app_name = 'material_management'

urlpatterns = [
    path('purchase_report/', PurchaseReportMainView.as_view(), name='purchase_report_main'),
    path('purchase_report/excise_partial/', ExciseReportPartialView.as_view(), name='excise_report_partial'),
    path('purchase_report/vat_cst_partial/', VatCstReportPartialView.as_view(), name='vat_cst_report_partial'),
    path('purchase_report/vat_cst_labour_partial/', VatCstLabourReportPartialView.as_view(), name='vat_cst_labour_report_partial'),
]
```

#### 4.6 Tests (`material_management/tests.py`)

Comprehensive tests will be crucial for verifying the correctness of data retrieval, calculations, and UI interactions.

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date, timedelta
from .models import (
    Company, Supplier, PackingMaster, ExciseServiceMaster, VatMaster,
    PoDetail, BillBookingMaster, QcMaterialQualityDetail, InvMaterialServiceNoteDetail,
    BillBookingDetail, PurchaseReportService
)

class ReportModelsTest(TestCase):
    """
    Unit tests for the underlying database models and the PurchaseReportService.
    Ensures data integrity and business logic correctness.
    """

    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for all related models
        cls.company = Company.objects.create(id=1, name='Test Company', address='123 Test St, Test City')
        cls.supplier = Supplier.objects.create(supplier_id=1, supplier_name='Test Supplier')
        cls.packing_master_pf = PackingMaster.objects.create(id=1, value='PF10%')
        cls.excise_master_exst = ExciseServiceMaster.objects.create(id=1, value='EXC12%', accessable_value='Assessable100', edu_cess='2%', she_cess='1%')
        cls.vat_master_vat = VatMaster.objects.create(id=1, value='VAT5%', is_vat=True, is_cst=False)
        cls.vat_master_cst = VatMaster.objects.create(id=2, value='CST2%', is_vat=False, is_cst=True)
        cls.vat_master_none = VatMaster.objects.create(id=3, value='NONE', is_vat=False, is_cst=False)

        cls.po_detail = PoDetail.objects.create(
            id=1, pf=cls.packing_master_pf, ex_st=cls.excise_master_exst, vat=cls.vat_master_vat,
            rate=100.00, discount=10.00
        )
        cls.po_detail_cst = PoDetail.objects.create(
            id=2, pf=cls.packing_master_pf, ex_st=cls.excise_master_exst, vat=cls.vat_master_cst,
            rate=200.00, discount=5.00
        )
        cls.po_detail_none = PoDetail.objects.create(
            id=3, pf=cls.packing_master_pf, ex_st=cls.excise_master_exst, vat=cls.vat_master_none,
            rate=50.00, discount=0.00
        )

        cls.qc_detail = QcMaterialQualityDetail.objects.create(id=1, accepted_qty=50.00)
        cls.inv_service_note_detail = InvMaterialServiceNoteDetail.objects.create(id=1, received_qty=20.00)

        # BillBookingMaster instances
        cls.bill_master_1 = BillBookingMaster.objects.create(
            id=1, sys_date=date(2023, 1, 15), supplier=cls.supplier, company=cls.company
        )
        cls.bill_master_2 = BillBookingMaster.objects.create(
            id=2, sys_date=date(2023, 1, 20), supplier=cls.supplier, company=cls.company
        )
        cls.bill_master_3 = BillBookingMaster.objects.create( # For labour report
            id=3, sys_date=date(2023, 1, 25), supplier=cls.supplier, company=cls.company
        )
        cls.bill_master_4 = BillBookingMaster.objects.create( # Out of date range
            id=4, sys_date=date(2023, 2, 1), supplier=cls.supplier, company=cls.company
        )

        # BillBookingDetail instances
        cls.bill_detail_vat = BillBookingDetail.objects.create(
            mid=cls.bill_master_1, gqn_id=cls.qc_detail, pod_id=cls.po_detail,
            pf_amt=10.00, ex_st_basic=5.00, ex_st_educess=1.00, ex_st_shecess=0.50,
            vat=2.50, cst=0.00, freight=3.00
        )
        cls.bill_detail_cst = BillBookingDetail.objects.create(
            mid=cls.bill_master_2, gqn_id=cls.qc_detail, pod_id=cls.po_detail_cst,
            pf_amt=20.00, ex_st_basic=10.00, ex_st_educess=2.00, ex_st_shecess=1.00,
            vat=0.00, cst=4.00, freight=5.00
        )
        cls.bill_detail_labour = BillBookingDetail.objects.create(
            mid=cls.bill_master_3, gsn_id=cls.inv_service_note_detail, pod_id=cls.po_detail_none,
            pf_amt=5.00, ex_st_basic=2.00, ex_st_educess=0.40, ex_st_shecess=0.20,
            vat=0.00, cst=0.00, freight=1.00
        )
        cls.bill_detail_out_of_range = BillBookingDetail.objects.create(
            mid=cls.bill_master_4, gqn_id=cls.qc_detail, pod_id=cls.po_detail,
            pf_amt=1.00, ex_st_basic=1.00, ex_st_educess=0.10, ex_st_shecess=0.05,
            vat=0.00, cst=0.00, freight=0.00
        )
        
    def test_company_address_retrieval(self):
        self.assertEqual(Company.get_company_address(self.company.id), self.company.address)
        self.assertEqual(Company.get_company_address(999), "Company Address Not Found.")

    def test_purchase_report_service_base_amount_calculation(self):
        # basic_amt = quantity * (rate - (rate * discount / 100))
        # 50 * (100 - (100 * 10 / 100)) = 50 * (100 - 10) = 50 * 90 = 4500
        self.assertAlmostEqual(
            PurchaseReportService._calculate_base_amount(self.po_detail, self.qc_detail.accepted_qty),
            4500.00
        )
        # 20 * (50 - (50 * 0 / 100)) = 20 * 50 = 1000
        self.assertAlmostEqual(
            PurchaseReportService._calculate_base_amount(self.po_detail_none, self.inv_service_note_detail.received_qty),
            1000.00
        )
        self.assertEqual(PurchaseReportService._calculate_base_amount(None, 10), 0.0)
        self.assertEqual(PurchaseReportService._calculate_base_amount(self.po_detail, None), 0.0)

    def test_get_common_report_lines(self):
        start_date = date(2023, 1, 1)
        end_date = date(2023, 1, 31)
        lines = PurchaseReportService._get_common_report_lines(start_date, end_date, self.company.id)
        
        self.assertEqual(len(lines), 3) # Excludes out_of_range
        
        # Verify a VAT line
        vat_line = next(item for item in lines if item['vat_cst_terms'] == 'VAT5%')
        self.assertIsNotNone(vat_line)
        self.assertAlmostEqual(vat_line['basic_amt'], 4500.00)
        self.assertAlmostEqual(vat_line['pf_amt'], 10.00)
        self.assertAlmostEqual(vat_line['excise_amt'], 6.50) # 5+1+0.5
        self.assertAlmostEqual(vat_line['vat_cst_amt'], 2.50)
        self.assertAlmostEqual(vat_line['freight_amt'], 3.00)
        self.assertTrue(vat_line['is_vat_entry'])
        self.assertFalse(vat_line['is_cst_entry'])
        self.assertTrue(vat_line['gqn_id_exists'])
        self.assertFalse(vat_line['gsn_id_exists'])


    def test_get_excise_report(self):
        # cryrpt_create2: Total = basic_amt + pf_amt + exba + edu + she (no VAT/CST/Freight)
        start_date = date(2023, 1, 1)
        end_date = date(2023, 1, 31)
        report = PurchaseReportService.get_excise_report(start_date, end_date, self.company.id)

        self.assertEqual(len(report['report_data_vat'] + report['report_data_cst']), 3) # All 3 in range
        
        # Expected totals:
        # Bill_detail_vat: basic=4500, pf=10, ex_amt=6.5. Total=4516.5. VAT-type.
        # Bill_detail_cst: basic=1900, pf=20, ex_amt=13. Total=1933. CST-type.
        # Bill_detail_labour: basic=1000, pf=5, ex_amt=2.6. Total=1007.6. VAT-type (as IsCST=0).

        # VAT Gross Total (vat_line + labour_line)
        self.assertAlmostEqual(report['vat_gross_total'], 4516.5 + 1007.6) # 5524.1
        # CST Gross Total (cst_line)
        self.assertAlmostEqual(report['cst_gross_total'], 1933.0)
        # Total Excise (sum of excise_amt for all 3 lines)
        self.assertAlmostEqual(report['total_excise'], 6.5 + 13.0 + 2.6) # 22.1

        # MAHPurchase: Sum of vat_cst_amt for each term
        # VAT5%: 2.50 (from bill_detail_vat)
        # CST2%: 4.00 (from bill_detail_cst)
        # NONE: 0.00 (from bill_detail_labour)
        # Order is not guaranteed in MAHPurchase string, so check presence.
        self.assertIn("@ VAT5% Amt: 2.50", report['mah_purchase'])
        self.assertIn("@ CST2% Amt: 4.00", report['mah_purchase'])
        self.assertIn("@ NONE Amt: 0.00", report['mah_purchase'])


    def test_get_vat_cst_report(self):
        # cryrpt_create(0): uses GQNId, Total = basic_amt + pf_amt + ex_amt + vat_cst_amt + freight_amt
        start_date = date(2023, 1, 1)
        end_date = date(2023, 1, 31)
        report = PurchaseReportService.get_vat_cst_report(start_date, end_date, self.company.id)

        self.assertEqual(len(report['report_data_vat'] + report['report_data_cst']), 2) # bill_detail_vat and bill_detail_cst

        # Expected totals:
        # Bill_detail_vat: basic=4500, pf=10, ex=6.5, vat_cst=2.5, fr=3. Total=4522. VAT-type.
        # Bill_detail_cst: basic=1900, pf=20, ex=13, vat_cst=4, fr=5. Total=1942. CST-type.

        self.assertAlmostEqual(report['vat_gross_total'], 4522.0)
        self.assertAlmostEqual(report['cst_gross_total'], 1942.0)
        self.assertAlmostEqual(report['total_excise'], 6.5 + 13.0) # 19.5
        self.assertIn("@ VAT5% Amt: 2.50", report['mah_purchase'])
        self.assertIn("@ CST2% Amt: 4.00", report['mah_purchase'])
        self.assertNotIn("@ NONE", report['mah_purchase']) # Labour entry is filtered out


    def test_get_vat_cst_labour_report(self):
        # cryrpt_create(1): uses GSNId, Total = basic_amt + pf_amt + ex_amt + vat_cst_amt + freight_amt
        start_date = date(2023, 1, 1)
        end_date = date(2023, 1, 31)
        report = PurchaseReportService.get_vat_cst_labour_report(start_date, end_date, self.company.id)

        self.assertEqual(len(report['report_data_vat'] + report['report_data_cst']), 1) # Only bill_detail_labour

        # Expected totals for bill_detail_labour:
        # basic=1000, pf=5, ex=2.6, vat_cst=0, fr=1. Total=1008.6. VAT-type (as IsCST=0).
        self.assertAlmostEqual(report['vat_gross_total'], 1008.6)
        self.assertAlmostEqual(report['cst_gross_total'], 0.0)
        self.assertAlmostEqual(report['total_excise'], 2.6)
        self.assertIn("@ NONE Amt: 0.00", report['mah_purchase'])


class PurchaseReportViewsTest(TestCase):
    """
    Integration tests for the Django views, ensuring correct rendering,
    form submission, and HTMX interactions.
    """

    def setUp(self):
        self.client = Client()
        # Set up necessary test data as in ModelTest, or load from fixtures
        self.company = Company.objects.create(id=1, name='Test Company', address='123 Test St, Test City')
        self.supplier = Supplier.objects.create(supplier_id=1, supplier_name='Test Supplier')
        self.packing_master_pf = PackingMaster.objects.create(id=1, value='PF10%')
        self.excise_master_exst = ExciseServiceMaster.objects.create(id=1, value='EXC12%', accessable_value='Assessable100', edu_cess='2%', she_cess='1%')
        self.vat_master_vat = VatMaster.objects.create(id=1, value='VAT5%', is_vat=True, is_cst=False)
        self.vat_master_cst = VatMaster.objects.create(id=2, value='CST2%', is_vat=False, is_cst=True)
        self.vat_master_none = VatMaster.objects.create(id=3, value='NONE', is_vat=False, is_cst=False)

        self.po_detail = PoDetail.objects.create(id=1, pf=self.packing_master_pf, ex_st=self.excise_master_exst, vat=self.vat_master_vat, rate=100.00, discount=10.00)
        self.po_detail_cst = PoDetail.objects.create(id=2, pf=self.packing_master_pf, ex_st=self.excise_master_exst, vat=self.vat_master_cst, rate=200.00, discount=5.00)
        self.po_detail_none = PoDetail.objects.create(id=3, pf=self.packing_master_pf, ex_st=self.excise_master_exst, vat=self.vat_master_none, rate=50.00, discount=0.00)

        self.qc_detail = QcMaterialQualityDetail.objects.create(id=1, accepted_qty=50.00)
        self.inv_service_note_detail = InvMaterialServiceNoteDetail.objects.create(id=1, received_qty=20.00)

        self.bill_master_1 = BillBookingMaster.objects.create(id=1, sys_date=date(2023, 1, 15), supplier=self.supplier, company=self.company)
        self.bill_master_2 = BillBookingMaster.objects.create(id=2, sys_date=date(2023, 1, 20), supplier=self.supplier, company=self.company)
        self.bill_master_3 = BillBookingMaster.objects.create(id=3, sys_date=date(2023, 1, 25), supplier=self.supplier, company=self.company)
        self.bill_master_4 = BillBookingMaster.objects.create(id=4, sys_date=date(2023, 2, 1), supplier=self.supplier, company=self.company)

        self.bill_detail_vat = BillBookingDetail.objects.create(mid=self.bill_master_1, gqn_id=self.qc_detail, pod_id=self.po_detail, pf_amt=10.00, ex_st_basic=5.00, ex_st_educess=1.00, ex_st_shecess=0.50, vat=2.50, cst=0.00, freight=3.00)
        self.bill_detail_cst = BillBookingDetail.objects.create(mid=self.bill_master_2, gqn_id=self.qc_detail, pod_id=self.po_detail_cst, pf_amt=20.00, ex_st_basic=10.00, ex_st_educess=2.00, ex_st_shecess=1.00, vat=0.00, cst=4.00, freight=5.00)
        self.bill_detail_labour = BillBookingDetail.objects.create(mid=self.bill_master_3, gsn_id=self.inv_service_note_detail, pod_id=self.po_detail_none, pf_amt=5.00, ex_st_basic=2.00, ex_st_educess=0.40, ex_st_shecess=0.20, vat=0.00, cst=0.00, freight=1.00)
        self.bill_detail_out_of_range = BillBookingDetail.objects.create(mid=self.bill_master_4, gqn_id=self.qc_detail, pod_id=self.po_detail, pf_amt=1.00, ex_st_basic=1.00, ex_st_educess=0.10, ex_st_shecess=0.05, vat=0.00, cst=0.00, freight=0.00)
        
        self.client.session['compid'] = self.company.id # Simulate session company ID


    def test_main_report_view_get(self):
        response = self.client.get(reverse('material_management:purchase_report_main'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchase_report/main.html')
        self.assertContains(response, 'Purchase Report')
        self.assertContains(response, 'id="report-content"') # Placeholder for HTMX content
        self.assertContains(response, 'hx-get="{% url \'material_management:excise_report_partial\' %}"')


    def test_excise_report_partial_view_get_valid_dates(self):
        from_date = date(2023, 1, 1).strftime('%Y-%m-%d')
        to_date = date(2023, 1, 31).strftime('%Y-%m-%d')
        response = self.client.get(
            reverse('material_management:excise_report_partial'),
            {'from_date': from_date, 'to_date': to_date},
            HTTP_HX_REQUEST='true' # Simulate HTMX request
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchase_report/_excise_report.html')
        self.assertContains(response, 'Excise Report')
        self.assertNotContains(response, 'No record found!') # Should have records
        # Check for specific data points if possible
        self.assertContains(response, '4516.50') # Example total from excise report
        self.assertContains(response, self.company.address) # Check if address is present

    def test_excise_report_partial_view_get_no_records(self):
        # Pick a date range with no data
        from_date = date(2024, 1, 1).strftime('%Y-%m-%d')
        to_date = date(2024, 1, 31).strftime('%Y-%m-%d')
        response = self.client.get(
            reverse('material_management:excise_report_partial'),
            {'from_date': from_date, 'to_date': to_date},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No record found!')

    def test_excise_report_partial_view_get_invalid_dates(self):
        from_date = date(2023, 1, 31).strftime('%Y-%m-%d')
        to_date = date(2023, 1, 1).strftime('%Y-%m-%d')
        response = self.client.get(
            reverse('material_management:excise_report_partial'),
            {'from_date': from_date, 'to_date': to_date},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Invalid date range: 'Date From' cannot be after 'To Date'.")
        self.assertContains(response, "Please correct the date errors to generate the Excise report.")


    def test_vat_cst_report_partial_view_get_valid_dates(self):
        from_date = date(2023, 1, 1).strftime('%Y-%m-%d')
        to_date = date(2023, 1, 31).strftime('%Y-%m-%d')
        response = self.client.get(
            reverse('material_management:vat_cst_report_partial'),
            {'from_date': from_date, 'to_date': to_date},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchase_report/_vat_cst_report.html')
        self.assertContains(response, 'VAT Entries')
        self.assertContains(response, 'CST Entries')
        self.assertNotContains(response, 'No record found!')
        self.assertContains(response, '4522.00') # Example VAT total from vat/cst report

    def test_vat_cst_labour_report_partial_view_get_valid_dates(self):
        from_date = date(2023, 1, 1).strftime('%Y-%m-%d')
        to_date = date(2023, 1, 31).strftime('%Y-%m-%d')
        response = self.client.get(
            reverse('material_management:vat_cst_labour_report_partial'),
            {'from_date': from_date, 'to_date': to_date},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchase_report/_vat_cst_labour_report.html')
        self.assertContains(response, 'VAT Entries (Labour)')
        self.assertContains(response, 'CST Entries (Labour)')
        self.assertNotContains(response, 'No record found!')
        self.assertContains(response, '1008.60') # Example total from labour report

```

### Step 5: HTMX and Alpine.js Integration

The provided Django templates already embed the HTMX and Alpine.js logic:

*   **HTMX for dynamic content**:
    *   The `main.html` template uses `hx-get` on tab buttons to fetch the content for each report partial (`_excise_report.html`, `_vat_cst_report.html`, `_vat_cst_labour_report.html`).
    *   The `hx-target` and `hx-swap` attributes ensure the report content is loaded into the `#report-content` div without a full page refresh.
    *   The "Search" buttons within each partial also use `hx-get` to refresh only the report content when dates are changed.
    *   `hx-trigger="load once delay:100ms"` on the first tab button ensures the initial report content is loaded when the page first loads.
    *   `hx-indicator` is used to show a loading spinner while HTMX requests are in progress.
*   **Alpine.js for UI state**:
    *   `x-data="{ activeTab: 'excise' }"` on the main container manages the active tab state, dynamically applying Tailwind CSS classes for visual indication.
    *   `x-model` is used to bind the date input values to Alpine.js data, although for simple HTMX `hx-get` forms, this is mostly for local UI state.
*   **DataTables for list views**:
    *   The `data-table` class is applied to the HTML tables in the report partials.
    *   The JavaScript within `main.html` (in `extra_js` block) listens for `htmx:afterSwap` event and initializes DataTables on the newly loaded tables. `destroy: true` is crucial to correctly re-initialize DataTables when content changes.
*   **No custom JavaScript requirements**: The entire dynamic interaction is handled by HTMX and Alpine.js, minimizing the need for complex custom JavaScript.

### Final Notes

*   **Placeholder Replacement**: Remember to replace placeholder table names (`tblCompany_Master`) and adjust field names/data types in `models.py` to precisely match your existing SQL Server database schema.
*   **Authentication & Session**: The ASP.NET code retrieves `CompId` and `FinYearId` from `Session`. In Django, `request.session.get('compid')` is used as a direct equivalent. Ensure your Django authentication system populates the session with necessary user/company context.
*   **Error Handling**: Basic error handling for date validation is included. More robust error handling, logging, and user feedback mechanisms should be considered for a production system.
*   **Reporting Enhancements**: While DataTables provides excellent client-side interactivity, if server-side PDF generation or more complex print layouts (like Crystal Reports offered) are strictly required, you might need to integrate a Python PDF generation library (e.g., ReportLab, WeasyPrint) and create a separate endpoint for generating such files. For most web applications, the print-to-PDF feature of modern browsers on the DataTables view is sufficient.
*   **Business Logic Location**: The `PurchaseReportService` in `models.py` is a prime example of putting business logic in the "Fat Model." If these reports become very complex or require a dedicated service layer, this service could be refactored into a separate `services.py` file, imported and utilized by the models and views.