## ASP.NET to Django Conversion Script: PO Approval Module Modernization

This document outlines the strategic approach for migrating the ASP.NET PO Approval module to a modern Django-based solution. Our focus is on delivering business value through automation-driven processes, leveraging current best practices in Django, HTMX, and Alpine.js to create a highly responsive and maintainable application.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:** The ASP.NET code interacts primarily with `tblMM_PO_Master` for purchase order data, `tblMM_Supplier_master` for supplier details, `tblHR_OfficeStaff` for employee information, and `tblFinancial_master` for financial year data.

**Inferred Schema:**

**`tblMM_PO_Master` (Mapped to Django Model: `PoMaster`)**
- `Id` (Primary Key, Integer)
- `PONo` (String)
- `SysDate` (Date, PO Generation Date)
- `AmendmentNo` (String)
- `SessionId` (Integer, Employee ID who generated the PO)
- `Checked` (Boolean/Integer, 1 for checked)
- `CheckedDate` (Date, Date of checking)
- `Approve` (Boolean/Integer, 1 for approved)
- `ApproveDate` (Date, Date of approval)
- `ApproveTime` (Time, Time of approval)
- `ApprovedBy` (String, User ID/Name who approved)
- `Authorize` (Boolean/Integer)
- `AuthorizeDate` (Date)
- `FinYearId` (Integer, Financial Year ID)
- `SupplierId` (String, Supplier Code)
- `CompId` (Integer, Company ID)
- `PRSPRFlag` (String, Flag for PO type)

**`tblMM_Supplier_master` (Mapped to Django Model: `SupplierMaster`)**
- `SupplierId` (Primary Key, String)
- `SupplierName` (String)
- `CompId` (Integer, Company ID)

**`tblHR_OfficeStaff` (Mapped to Django Model: `OfficeStaff`)**
- `EmpId` (Primary Key, Integer)
- `Title` (String)
- `EmployeeName` (String)
- `CompId` (Integer, Company ID)

**`tblFinancial_master` (Mapped to Django Model: `FinancialYear`)**
- `FinYearId` (Primary Key, Integer)
- `FinYear` (String, e.g., "2023-2024")

## Step 2: Identify Backend Functionality

**Task:** Determine the business logic and operations in the ASP.NET code.

**Instructions:**
- **Read (List/Filter):** The primary functionality is to display a list of Purchase Orders (POs) that are `Checked='1'` and `Approve='0'` (i.e., pending approval). This list can be filtered by `Supplier` name/code or `PO Number`. The `makegrid()` method dynamically fetches data from `tblMM_PO_Master` and performs lookups for supplier name, employee name, and financial year.
- **Update (Approve):** Users can select one or more POs from the list using checkboxes and click an "Approved" button. This action updates the `tblMM_PO_Master` records, setting `Approve='1'`, `ApprovedBy` (current user), `ApproveDate`, and `ApproveTime`.
- **View Details (Redirect):** Each PO row has a "View" link that redirects to a different ASP.NET page (`PO_PR_View_Print_Details.aspx` or `PO_SPR_View_Print_Details.aspx`) to show detailed PO information. This will be a simple link in Django, not a full CRUD operation within this module.
- **Search Type Toggle:** The dropdown for "Supplier" or "PO No" dynamically shows/hides the respective text input fields.
- **Auto-completion:** The supplier input field has an auto-completion feature for supplier names.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
- **Data Display:** `asp:GridView ID="GridView2"` for presenting a tabular list of POs. This will be replaced by an HTML `<table>` with DataTables.
- **Search Filters:**
    - `asp:DropDownList ID="drpfield"`: For selecting search criteria (Supplier or PO No).
    - `asp:TextBox ID="txtSupplier"`: Text input for Supplier Name (with `AjaxControlToolkit:AutoCompleteExtender`).
    - `asp:TextBox ID="txtPONo"`: Text input for PO Number.
    - `asp:Button ID="Button1"`: "Search" button to apply filters.
- **Action Buttons:**
    - `asp:Button ID="App"`: "Approved" button to process selected POs.
    - `asp:LinkButton ID="lnkbtn"`: "View" link within each row to navigate to PO details.
- **Panels:** `asp:Panel ID="Panel1"` for scrollable grid container.

## Step 4: Generate Django Code
The Django application will be named `po_approve`.

### 4.1 Models

**Task:** Create Django models based on the identified database schema. We'll include relationships for easier querying even with `managed=False`.

**`po_approve/models.py`**
```python
from django.db import models
from django.utils import timezone

class FinancialYear(models.Model):
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class OfficeStaff(models.Model):
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    title = models.CharField(db_column='Title', max_length=50)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title}. {self.employee_name}"

class SupplierMaster(models.Model):
    supplier_id = models.CharField(db_column='SupplierId', primary_key=True, max_length=50)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.supplier_name

class PoMasterManager(models.Manager):
    def get_approval_list(self, comp_id, fin_year_id, search_type=None, search_value=None):
        # Base query for POs awaiting approval, optimized with select_related
        queryset = self.filter(
            comp_id=comp_id,
            fin_year_id__lte=fin_year_id, # Original logic was <= FyId
            checked=True, # Assuming '1' in DB means True
            approve=False # Assuming '0' in DB means False
        ).order_by('-id').select_related(
            'supplier', 'generated_by_staff', 'financial_year'
        )

        if search_type == '1' and search_value: # Filter by PO Number
            queryset = queryset.filter(po_no__icontains=search_value) # Using icontains for partial match
        elif search_type == '0' and search_value: # Filter by Supplier
            # The original code inferred SupplierId from "SupplierName [Code]"
            # We'll try to extract the code first, then search by name if not found.
            supplier_code = None
            if '[' in search_value and ']' in search_value:
                try:
                    supplier_code = search_value.split('[')[-1].strip(']')
                except IndexError:
                    pass # Not a valid code format
            
            if supplier_code:
                queryset = queryset.filter(supplier__supplier_id__iexact=supplier_code)
            else:
                # If no code or invalid format, search by supplier name contains
                queryset = queryset.filter(supplier__supplier_name__icontains=search_value)
                
        return queryset

class PoMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    po_no = models.CharField(db_column='PONo', max_length=255)
    sys_date = models.DateField(db_column='SysDate')
    amendment_no = models.CharField(db_column='AmendmentNo', max_length=50)
    session_id = models.IntegerField(db_column='SessionId') # Represents EmpId of the generator
    checked = models.BooleanField(db_column='Checked')
    checked_date = models.DateField(db_column='CheckedDate', null=True, blank=True)
    approve = models.BooleanField(db_column='Approve')
    approve_date = models.DateField(db_column='ApproveDate', null=True, blank=True)
    approve_time = models.TimeField(db_column='ApproveTime', null=True, blank=True)
    approved_by = models.CharField(db_column='ApprovedBy', max_length=255, null=True, blank=True) # Storing username string as per original
    authorize = models.BooleanField(db_column='Authorize')
    authorize_date = models.DateField(db_column='AuthorizeDate', null=True, blank=True)
    fin_year_id = models.IntegerField(db_column='FinYearId') # Represents FinYearId
    supplier_id = models.CharField(db_column='SupplierId', max_length=50) # Represents SupplierId
    comp_id = models.IntegerField(db_column='CompId')
    pr_spr_flag = models.CharField(db_column='PRSPRFlag', max_length=1)

    # Define explicit relationships for ORM joins, even with managed=False
    supplier = models.ForeignKey(
        SupplierMaster,
        on_delete=models.DO_NOTHING, # Or models.SET_NULL, etc., based on DB constraints
        db_column='SupplierId',
        to_field='supplier_id',
        related_name='pos_as_supplier',
        null=True, blank=True # Allow null/blank if foreign key can be missing in DB
    )
    generated_by_staff = models.ForeignKey(
        OfficeStaff,
        on_delete=models.DO_NOTHING,
        db_column='SessionId',
        to_field='emp_id',
        related_name='generated_pos',
        null=True, blank=True
    )
    financial_year = models.ForeignKey(
        FinancialYear,
        on_delete=models.DO_NOTHING,
        db_column='FinYearId',
        to_field='fin_year_id',
        related_name='pos_by_year',
        null=True, blank=True
    )

    objects = PoMasterManager()

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'

    def __str__(self):
        return self.po_no

    def approve_po(self, approved_by_username):
        """
        Approves the purchase order.
        Moves business logic from views to the model.
        """
        if not self.approve: # Only approve if not already approved
            self.approve = True
            self.approved_by = approved_by_username
            self.approve_date = timezone.localdate()
            self.approve_time = timezone.localtime().time()
            self.save()
            return True
        return False
```

### 4.2 Forms

**Task:** Define a Django form for search input. No complex ModelForms are needed for this approval list.

**`po_approve/forms.py`**
```python
from django import forms

class PoApproveSearchForm(forms.Form):
    SEARCH_TYPE_CHOICES = [
        ('0', 'Supplier'),
        ('1', 'PO No'),
    ]
    search_type = forms.ChoiceField(
        choices=SEARCH_TYPE_CHOICES,
        initial='0', # Default to Supplier
        widget=forms.Select(attrs={'class': 'box3', 'x-model': 'searchType', 'hx-get': "{% url 'po_approve_table' %}", 'hx-target': "#po-approve-table-container", 'hx-swap': "innerHTML", 'hx-trigger': "change throttle:500ms", 'hx-indicator': "#loading-spinner"})
    )
    supplier_text = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3',
            'placeholder': 'Enter Supplier Name or Code',
            'x-show': "searchType == '0'",
            'hx-get': "{% url 'po_approve_supplier_autocomplete' %}",
            'hx-trigger': "keyup changed delay:500ms",
            'hx-target': "#supplier-suggestions",
            'hx-swap': "innerHTML",
            'hx-indicator': "#loading-spinner",
            'autocomplete': 'off'
        })
    )
    po_no_text = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3',
            'placeholder': 'Enter PO Number',
            'x-show': "searchType == '1'"
        })
    )

    def clean(self):
        cleaned_data = super().clean()
        search_type = cleaned_data.get('search_type')
        supplier_text = cleaned_data.get('supplier_text')
        po_no_text = cleaned_data.get('po_no_text')

        if search_type == '0' and not supplier_text:
            # Optionally add validation if supplier field is required for supplier search
            # Or allow empty search to show all for that type.
            pass
        elif search_type == '1' and not po_no_text:
            # Optionally add validation if PO No field is required for PO No search
            pass
        return cleaned_data
```

### 4.3 Views

**Task:** Implement the necessary views using Django's Class-Based Views (CBVs), keeping them thin and delegating business logic to models.

**`po_approve/views.py`**
```python
from django.views.generic import ListView, View, TemplateView
from django.http import JsonResponse, HttpResponse
from django.urls import reverse_lazy
from django.contrib import messages
from django.db.models import Q # For complex queries if needed
from django.shortcuts import get_object_or_404
import json # For parsing HTMX JSON body

from .models import PoMaster, SupplierMaster
from .forms import PoApproveSearchForm

# Assume user session data for comp_id and fin_year_id (example placeholders)
# In a real ERP, these would come from request.user profile or session management.
def get_user_context(request):
    # Placeholder for company ID and financial year ID
    # In a real app, these would come from request.user.profile or session
    # For this example, we'll use hardcoded values or derive from user.
    # From ASP.NET: Session["compid"], Session["finyear"]
    comp_id = 1 # Example Company ID
    fin_year_id = 1 # Example Financial Year ID (or latest based on current date)
    username = request.user.username if request.user.is_authenticated else "Guest"
    return {'comp_id': comp_id, 'fin_year_id': fin_year_id, 'username': username}

class PoApproveListView(ListView):
    """
    Main view to display the PO approval page with search controls.
    """
    model = PoMaster
    template_name = 'po_approve/list.html'
    context_object_name = 'pos_pending_approval'
    # No need to query data here, as it will be loaded by HTMX via PoApproveTablePartialView
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the search form without data initially, or with request GET data
        context['form'] = PoApproveSearchForm(self.request.GET)
        return context

class PoApproveTablePartialView(TemplateView):
    """
    Partial view to render the DataTables content, driven by HTMX.
    This replaces the 'makegrid' logic from ASP.NET.
    """
    template_name = 'po_approve/_po_approve_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        search_form = PoApproveSearchForm(self.request.GET)
        
        comp_id = get_user_context(self.request)['comp_id']
        fin_year_id = get_user_context(self.request)['fin_year_id']

        search_type = None
        search_value = None

        if search_form.is_valid():
            search_type = search_form.cleaned_data.get('search_type')
            if search_type == '0': # Supplier search
                search_value = search_form.cleaned_data.get('supplier_text')
            elif search_type == '1': # PO No search
                search_value = search_form.cleaned_data.get('po_no_text')

        context['pos_pending_approval'] = PoMaster.objects.get_approval_list(
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            search_type=search_type,
            search_value=search_value
        )
        return context

class PoApproveActionView(View):
    """
    Handles the POST request for approving selected Purchase Orders.
    This replaces the App_Click and GridView2_RowCommand (App) logic.
    """
    def post(self, request, *args, **kwargs):
        selected_po_ids = request.POST.getlist('selected_pos') # Get IDs from checkboxes
        
        if not selected_po_ids:
            messages.warning(request, "No records found to approve. Please select at least one PO.")
            # HTMX needs to trigger a message display and possibly refresh the table
            return HttpResponse(
                status=200, # OK
                headers={
                    'HX-Trigger': json.dumps({
                        'showMessage': 'No records found to approve. Please select at least one PO.',
                        'refreshPoApproveList': True # Trigger table refresh
                    })
                }
            )

        comp_id = get_user_context(self.request)['comp_id']
        approved_by_username = get_user_context(self.request)['username']
        
        approved_count = 0
        for po_id in selected_po_ids:
            po = get_object_or_404(PoMaster, id=po_id, comp_id=comp_id) # Ensure PO belongs to company
            if po.approve_po(approved_by_username):
                approved_count += 1
        
        if approved_count > 0:
            messages.success(request, f"{approved_count} Purchase Order(s) approved successfully.")
            # HTMX response to refresh the list and show success message
            return HttpResponse(
                status=204, # No Content, but triggers HX-Trigger
                headers={
                    'HX-Trigger': 'refreshPoApproveList'
                }
            )
        else:
            messages.info(request, "No new Purchase Orders were approved (they might have been approved already).")
            return HttpResponse(
                status=200,
                headers={
                    'HX-Trigger': json.dumps({
                        'showMessage': 'No new Purchase Orders were approved.',
                        'refreshPoApproveList': True
                    })
                }
            )

class SupplierAutoCompleteView(View):
    """
    Provides supplier name and code suggestions for the autocomplete field.
    Replaces AjaxControlToolkit:AutoCompleteExtender.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        comp_id = get_user_context(self.request)['comp_id']
        
        if query:
            suppliers = SupplierMaster.objects.filter(
                Q(supplier_name__icontains=query) | Q(supplier_id__icontains=query),
                comp_id=comp_id
            )[:10] # Limit to 10 suggestions
            
            # Format: "SupplierName [SupplierId]" as in original ASP.NET
            suggestions = [f"{s.supplier_name} [{s.supplier_id}]" for s in suppliers]
        else:
            suggestions = []
        
        # Return as JSON (or HTML fragment for HTMX)
        # For simplicity, returning HTML fragment that HTMX can insert directly
        return HttpResponse(
            "<div class='autocomplete-results' x-data x-ref='autocompleteResults'>" +
            "".join([
                f"<div class='p-2 hover:bg-gray-200 cursor-pointer' @click=\"$dispatch('set-supplier-text', '{s.replace('\'', '\\\'')}')\">{s}</div>"
                for s in suggestions
            ]) +
            "</div>"
        )
```

### 4.4 Templates

**Task:** Create templates for the main view and partials, integrating HTMX and Alpine.js.

**`po_approve/templates/po_approve/list.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">PO Approve</h2>
        <div id="loading-spinner" class="htmx-indicator ml-4 inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 hidden"></div>
    </div>
    
    <div class="bg-white p-6 rounded-lg shadow-lg mb-6" x-data="{ searchType: '{{ form.search_type.value }}', supplierText: '{{ form.supplier_text.value|default:'' }}' }">
        <form id="search-form" 
              hx-get="{% url 'po_approve_table' %}" 
              hx-target="#po-approve-table-container" 
              hx-swap="innerHTML" 
              hx-indicator="#loading-spinner">
            {% csrf_token %}
            <div class="flex items-center space-x-4 mb-4">
                <label for="{{ form.search_type.id_for_label }}" class="sr-only">Search Type</label>
                {{ form.search_type }} {# This handles x-model and hx-get/hx-trigger for dropdown #}

                <div class="relative w-full">
                    <div x-show="searchType == '0'">
                        {{ form.supplier_text }}
                        <div id="supplier-suggestions" class="absolute z-10 bg-white border border-gray-300 w-full mt-1 rounded-md shadow-lg">
                            {# Autocomplete results will be loaded here by HTMX #}
                        </div>
                    </div>
                    <div x-show="searchType == '1'">
                        {{ form.po_no_text }}
                    </div>
                </div>

                <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded redbox">
                    Search
                </button>
                <button type="button" 
                    class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded redbox"
                    hx-post="{% url 'po_approve_action' %}"
                    hx-include="#po-approve-table-container" {# Include checkboxes from the table #}
                    hx-confirm="Are you sure you want to approve the selected Purchase Orders?"
                    hx-indicator="#loading-spinner">
                    Approved
                </button>
            </div>
            {% if form.errors %}
                <div class="text-red-500 text-sm mt-2">
                    {% for field_errors in form.errors.values %}{% for error in field_errors %}{{ error }} {% endfor %}{% endfor %}
                </div>
            {% endif %}
        </form>
    </div>

    <div id="po-approve-table-container"
         hx-trigger="load, refreshPoApproveList from:body"
         hx-get="{% url 'po_approve_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Purchase Orders...</p>
        </div>
    </div>
</div>

<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('poApproveData', () => ({
            searchType: '{{ form.search_type.value }}',
            supplierText: '{{ form.supplier_text.value|default:'' }}',
            init() {
                // Event listener to set supplier text from autocomplete
                this.$root.addEventListener('set-supplier-text', (event) => {
                    this.supplierText = event.detail;
                    this.$refs.supplierInput.value = event.detail; // Update hidden input if needed
                    document.getElementById('supplier-suggestions').innerHTML = ''; // Clear suggestions
                    // Automatically trigger search after selection if desired
                    htmx.trigger(document.getElementById('search-form'), 'submit');
                });
            }
        }));
    });

    // Handle messages triggered by HX-Trigger
    document.body.addEventListener('showMessage', function(evt) {
        alert(evt.detail); // Replace with a more sophisticated notification system
    });
    
    // Initialize DataTables after HTMX loads the content
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.target.id === 'po-approve-table-container') {
            $('#poApproveTable').DataTable({
                "pageLength": 20,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true // Destroy previous instance if it exists
            });
        }
    });
</script>
{% endblock %}
```

**`po_approve/templates/po_approve/_po_approve_table.html`**
```html
<div class="overflow-x-auto">
    <table id="poApproveTable" class="min-w-full bg-white border border-gray-300 yui-datatable-theme">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">AmdNo</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
                <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Checked Date</th>
                <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Approved Date</th>
                <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Authorized Date</th>
                <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">For Approve</th>
                <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                <th class="hidden">Id</th> {# Hidden column for DataKeyNames equivalent #}
                <th class="hidden">AmendmentNo</th> {# Hidden column for GridView2_RowCommand #}
            </tr>
        </thead>
        <tbody>
            {% for po in pos_pending_approval %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ po.po_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ po.sys_date|date:"d-m-Y" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ po.amendment_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ po.generated_by_staff.get_full_name|default:po.session_id }}</td> {# Or po.generated_by_staff.employee_name #}
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ po.supplier.supplier_name|default:po.supplier_id }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ po.supplier_id }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ po.financial_year.fin_year|default:po.fin_year_id }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ po.checked_date|date:"d-m-Y"|default:"" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ po.approve_date|date:"d-m-Y"|default:"" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ po.authorize_date|date:"d-m-Y"|default:"" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    {% if not po.approve %}
                        <input type="checkbox" name="selected_pos" value="{{ po.id }}" class="form-checkbox h-5 w-5 text-blue-600">
                    {% else %}
                        <span class="text-gray-500">Approved</span>
                    {% endif %}
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <a href="{% url 'po_approve_view_details' po.id po.po_no po.supplier_id po.amendment_no %}" 
                       class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2">
                        View
                    </a>
                </td>
                <td class="hidden">{{ po.id }}</td>
                <td class="hidden">{{ po.amendment_no }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="13" class="py-4 px-4 text-center text-lg text-maroon font-bold">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // This script will be re-executed on HTMX swap
    $(document).ready(function() {
        // DataTables initialization
        // This is handled by the htmx:afterSwap listener in list.html now
    });
</script>
```

### 4.5 URLs

**Task:** Define URL patterns for the views, including HTMX-specific endpoints.

**`po_approve/urls.py`**
```python
from django.urls import path
from .views import PoApproveListView, PoApproveTablePartialView, PoApproveActionView, SupplierAutoCompleteView

urlpatterns = [
    # Main PO approval list view
    path('po-approve/', PoApproveListView.as_view(), name='po_approve_list'),
    
    # HTMX endpoint for the DataTables content (table partial)
    path('po-approve/table/', PoApproveTablePartialView.as_view(), name='po_approve_table'),
    
    # HTMX endpoint for approving selected POs
    path('po-approve/action/', PoApproveActionView.as_view(), name='po_approve_action'),
    
    # HTMX endpoint for supplier autocomplete suggestions
    path('po-approve/autocomplete/supplier/', SupplierAutoCompleteView.as_view(), name='po_approve_supplier_autocomplete'),

    # Placeholder for 'View' functionality (redirects to another module)
    # This URL needs to match the actual target system's URL structure or a Django equivalent.
    # For demonstration, we'll just define a dummy path.
    path('po-view-details/<int:pk>/<str:po_no>/<str:supplier_id>/<str:amd_no>/', 
         lambda request, pk, po_no, supplier_id, amd_no: HttpResponse(f"Redirecting to PO details for ID: {pk}, PO No: {po_no}, Supplier: {supplier_id}, Amd No: {amd_no}"), 
         name='po_approve_view_details'),
    # You would typically replace the lambda with a proper Django view that handles the redirect
    # or renders the details page within Django itself.
]
```
**Note**: Remember to include `po_approve.urls` in your project's main `urls.py`.
Example: `path('material-management/', include('po_approve.urls')),`

### 4.6 Tests

**Task:** Write comprehensive unit tests for models and integration tests for views, ensuring high test coverage.

**`po_approve/tests.py`**
```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from unittest.mock import patch, MagicMock

from .models import PoMaster, SupplierMaster, OfficeStaff, FinancialYear

# Helper to mock user context for tests
def mock_get_user_context(request):
    return {'comp_id': 1, 'fin_year_id': 1, 'username': 'testuser'}

class PoMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.financial_year = FinancialYear.objects.create(fin_year_id=1, fin_year='2023-2024')
        cls.supplier = SupplierMaster.objects.create(supplier_id='SUP001', supplier_name='Test Supplier Inc.', comp_id=1)
        cls.staff = OfficeStaff.objects.create(emp_id=101, title='Mr', employee_name='John Doe', comp_id=1)

        cls.po_pending = PoMaster.objects.create(
            id=1,
            po_no='PO001',
            sys_date=timezone.localdate(),
            amendment_no='0',
            session_id=cls.staff.emp_id,
            checked=True,
            checked_date=timezone.localdate(),
            approve=False,
            fin_year_id=cls.financial_year.fin_year_id,
            supplier_id=cls.supplier.supplier_id,
            comp_id=1,
            pr_spr_flag='0',
            supplier=cls.supplier,
            generated_by_staff=cls.staff,
            financial_year=cls.financial_year
        )
        cls.po_approved = PoMaster.objects.create(
            id=2,
            po_no='PO002',
            sys_date=timezone.localdate(),
            amendment_no='0',
            session_id=cls.staff.emp_id,
            checked=True,
            checked_date=timezone.localdate(),
            approve=True,
            approve_date=timezone.localdate(),
            approved_by='anotheruser',
            fin_year_id=cls.financial_year.fin_year_id,
            supplier_id=cls.supplier.supplier_id,
            comp_id=1,
            pr_spr_flag='0',
            supplier=cls.supplier,
            generated_by_staff=cls.staff,
            financial_year=cls.financial_year
        )
        # PO for a different company
        cls.po_other_comp = PoMaster.objects.create(
            id=3,
            po_no='PO003',
            sys_date=timezone.localdate(),
            amendment_no='0',
            session_id=cls.staff.emp_id,
            checked=True,
            checked_date=timezone.localdate(),
            approve=False,
            fin_year_id=cls.financial_year.fin_year_id,
            supplier_id=cls.supplier.supplier_id,
            comp_id=2, # Different company
            pr_spr_flag='0',
            supplier=cls.supplier,
            generated_by_staff=cls.staff,
            financial_year=cls.financial_year
        )
        # PO not checked
        cls.po_not_checked = PoMaster.objects.create(
            id=4,
            po_no='PO004',
            sys_date=timezone.localdate(),
            amendment_no='0',
            session_id=cls.staff.emp_id,
            checked=False, # Not checked
            approve=False,
            fin_year_id=cls.financial_year.fin_year_id,
            supplier_id=cls.supplier.supplier_id,
            comp_id=1,
            pr_spr_flag='0',
            supplier=cls.supplier,
            generated_by_staff=cls.staff,
            financial_year=cls.financial_year
        )
        # Supplier for autocomplete test
        SupplierMaster.objects.create(supplier_id='SUP002', supplier_name='Another Supplier Co.', comp_id=1)


    def test_po_master_creation(self):
        po = PoMaster.objects.get(id=1)
        self.assertEqual(po.po_no, 'PO001')
        self.assertFalse(po.approve)
        self.assertEqual(po.supplier.supplier_name, 'Test Supplier Inc.')
        self.assertEqual(po.generated_by_staff.employee_name, 'John Doe')

    def test_po_approve_method(self):
        po = PoMaster.objects.get(id=1)
        self.assertFalse(po.approve)
        
        with patch('django.utils.timezone.localdate', return_value=timezone.localdate()):
            with patch('django.utils.timezone.localtime', return_value=MagicMock(time=lambda: timezone.now().time())):
                approved = po.approve_po('testuser')
        
        self.assertTrue(approved)
        po.refresh_from_db()
        self.assertTrue(po.approve)
        self.assertEqual(po.approved_by, 'testuser')
        self.assertEqual(po.approve_date, timezone.localdate())

    def test_po_approve_already_approved(self):
        po = PoMaster.objects.get(id=2)
        self.assertTrue(po.approve)
        
        approved = po.approve_po('newuser')
        self.assertFalse(approved) # Should not approve again
        po.refresh_from_db()
        self.assertEqual(po.approved_by, 'anotheruser') # Should remain unchanged

    def test_get_approval_list_no_filters(self):
        pos = PoMaster.objects.get_approval_list(comp_id=1, fin_year_id=1)
        self.assertEqual(pos.count(), 1) # Only po_pending should be returned
        self.assertEqual(pos.first().id, self.po_pending.id)

    def test_get_approval_list_po_no_filter(self):
        pos = PoMaster.objects.get_approval_list(comp_id=1, fin_year_id=1, search_type='1', search_value='PO001')
        self.assertEqual(pos.count(), 1)
        self.assertEqual(pos.first().id, self.po_pending.id)

        pos = PoMaster.objects.get_approval_list(comp_id=1, fin_year_id=1, search_type='1', search_value='NonExistentPO')
        self.assertEqual(pos.count(), 0)
        
    def test_get_approval_list_supplier_filter_by_code(self):
        pos = PoMaster.objects.get_approval_list(comp_id=1, fin_year_id=1, search_type='0', search_value='Test Supplier Inc. [SUP001]')
        self.assertEqual(pos.count(), 1)
        self.assertEqual(pos.first().id, self.po_pending.id)

    def test_get_approval_list_supplier_filter_by_name(self):
        pos = PoMaster.objects.get_approval_list(comp_id=1, fin_year_id=1, search_type='0', search_value='Test Supplier')
        self.assertEqual(pos.count(), 1)
        self.assertEqual(pos.first().id, self.po_pending.id)

        pos = PoMaster.objects.get_approval_list(comp_id=1, fin_year_id=1, search_type='0', search_value='NonExistentSupplier')
        self.assertEqual(pos.count(), 0)

    def test_get_approval_list_different_company(self):
        pos = PoMaster.objects.get_approval_list(comp_id=2, fin_year_id=1)
        self.assertEqual(pos.count(), 1)
        self.assertEqual(pos.first().id, self.po_other_comp.id)

    def test_get_approval_list_not_checked(self):
        pos = PoMaster.objects.get_approval_list(comp_id=1, fin_year_id=1)
        self.assertNotIn(self.po_not_checked, pos)


@patch('po_approve.views.get_user_context', side_effect=mock_get_user_context)
class PoApproveViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data (same as model tests)
        cls.financial_year = FinancialYear.objects.create(fin_year_id=1, fin_year='2023-2024')
        cls.supplier = SupplierMaster.objects.create(supplier_id='SUP001', supplier_name='Test Supplier Inc.', comp_id=1)
        cls.staff = OfficeStaff.objects.create(emp_id=101, title='Mr', employee_name='John Doe', comp_id=1)
        cls.po_pending = PoMaster.objects.create(
            id=1,
            po_no='PO001', sys_date=timezone.localdate(), amendment_no='0',
            session_id=cls.staff.emp_id, checked=True, checked_date=timezone.localdate(),
            approve=False, fin_year_id=cls.financial_year.fin_year_id,
            supplier_id=cls.supplier.supplier_id, comp_id=1, pr_spr_flag='0',
            supplier=cls.supplier, generated_by_staff=cls.staff, financial_year=cls.financial_year
        )
        cls.po_approved = PoMaster.objects.create(
            id=2,
            po_no='PO002', sys_date=timezone.localdate(), amendment_no='0',
            session_id=cls.staff.emp_id, checked=True, checked_date=timezone.localdate(),
            approve=True, fin_year_id=cls.financial_year.fin_year_id,
            supplier_id=cls.supplier.supplier_id, comp_id=1, pr_spr_flag='0',
            supplier=cls.supplier, generated_by_staff=cls.staff, financial_year=cls.financial_year
        )
        SupplierMaster.objects.create(supplier_id='SUP002', supplier_name='Another Supplier', comp_id=1)

    def setUp(self):
        self.client = Client()

    def test_list_view_get(self, mock_get_user_context):
        response = self.client.get(reverse('po_approve_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'po_approve/list.html')
        self.assertIsInstance(response.context['form'], PoApproveSearchForm)

    def test_table_partial_view_get(self, mock_get_user_context):
        response = self.client.get(reverse('po_approve_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'po_approve/_po_approve_table.html')
        self.assertTrue('pos_pending_approval' in response.context)
        self.assertEqual(response.context['pos_pending_approval'].count(), 1) # Only PO001 is pending

    def test_table_partial_view_get_with_po_no_filter(self, mock_get_user_context):
        response = self.client.get(reverse('po_approve_table'), {'search_type': '1', 'po_no_text': 'PO001'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['pos_pending_approval'].count(), 1)
        self.assertEqual(response.context['pos_pending_approval'].first().po_no, 'PO001')

    def test_table_partial_view_get_with_supplier_filter(self, mock_get_user_context):
        response = self.client.get(reverse('po_approve_table'), {'search_type': '0', 'supplier_text': 'Test Supplier Inc.'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['pos_pending_approval'].count(), 1)
        self.assertEqual(response.context['pos_pending_approval'].first().supplier.supplier_name, 'Test Supplier Inc.')
        
    def test_po_approve_action_post_success(self, mock_get_user_context):
        self.assertFalse(self.po_pending.approve)
        response = self.client.post(reverse('po_approve_action'), {'selected_pos': [self.po_pending.id]}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content expected for HTMX success
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshPoApproveList')
        
        self.po_pending.refresh_from_db()
        self.assertTrue(self.po_pending.approve)
        self.assertEqual(self.po_pending.approved_by, 'testuser')

    def test_po_approve_action_post_no_selection(self, mock_get_user_context):
        response = self.client.post(reverse('po_approve_action'), {}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # OK for message/trigger
        self.assertTrue('HX-Trigger' in response.headers)
        # Check if the trigger contains the showMessage part
        trigger_data = json.loads(response.headers['HX-Trigger'])
        self.assertIn('showMessage', trigger_data)
        self.assertIn('No records found to approve', trigger_data['showMessage'])

    def test_supplier_autocomplete_view(self, mock_get_user_context):
        response = self.client.get(reverse('po_approve_supplier_autocomplete'), {'q': 'Test'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertIn('Test Supplier Inc. [SUP001]', response.content.decode())
        self.assertNotIn('Another Supplier Co. [SUP002]', response.content.decode()) # Should only show 'Test'

        response = self.client.get(reverse('po_approve_supplier_autocomplete'), {'q': 'Another'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertIn('Another Supplier [SUP002]', response.content.decode())

        response = self.client.get(reverse('po_approve_supplier_autocomplete'), {'q': ''}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertNotIn('Test Supplier Inc. [SUP001]', response.content.decode())
        self.assertNotIn('Another Supplier Co. [SUP002]', response.content.decode())

```

## Step 5: HTMX and Alpine.js Integration

**Instructions:** All dynamic interactions will leverage HTMX for server communication and Alpine.js for local UI state management.

-   **Initial Page Load:** The `list.html` loads the `_po_approve_table.html` partial via `hx-get` on `load`.
-   **Search Functionality:**
    -   The entire search form (`id="search-form"`) is an HTMX form.
    -   Changing the `search_type` dropdown (`x-model="searchType"`) dynamically shows/hides the respective text inputs (`supplier_text` or `po_no_text`) using `x-show` from Alpine.js.
    -   Submitting the form (e.g., clicking the "Search" button or changing the `search_type` dropdown with `hx-trigger="change throttle:500ms"`) triggers an `hx-get` request to `{% url 'po_approve_table' %}`. The response replaces the `po-approve-table-container` div, effectively refreshing the DataTables content without a full page reload.
-   **Supplier Autocomplete:**
    -   The `supplier_text` input field has `hx-get` to `{% url 'po_approve_supplier_autocomplete' %}`.
    -   `hx-trigger="keyup changed delay:500ms"` ensures requests are sent only after typing pauses and when the value actually changes.
    -   `hx-target="#supplier-suggestions"` specifies where the autocomplete results (an HTML fragment containing suggestions) will be swapped.
    -   An Alpine.js event listener (`@click="$dispatch('set-supplier-text', ...)"`) on suggestion items allows clicking a suggestion to populate the input field and trigger a search.
-   **Approve Action:**
    -   The "Approved" button uses `hx-post` to `{% url 'po_approve_action' %}`.
    -   `hx-include="#po-approve-table-container"` ensures all selected checkboxes (`name="selected_pos"`) within the table are included in the POST request.
    -   `hx-confirm` provides a client-side confirmation dialog.
    -   On successful approval, the `PoApproveActionView` returns a `204 No Content` status with an `HX-Trigger: refreshPoApproveList` header. This instructs HTMX to re-fetch the `po-approve-table-container`, refreshing the list of pending POs.
    -   Error/Warning messages are sent via `HX-Trigger` JSON, which is then handled by a generic JavaScript listener in `base.html` (or `list.html`) to display `alert()` messages.
-   **DataTables:** The `_po_approve_table.html` partial contains the HTML table structure. An `htmx:afterSwap` event listener in `list.html` ensures that DataTables is initialized on the newly swapped content, providing client-side sorting, searching, and pagination.

## Final Notes

This modernization plan transitions the core functionality of the ASP.NET PO Approval module to a robust, modern Django application. By adhering to the principles of fat models, thin views, and exclusive use of HTMX/Alpine.js for interactivity, we achieve a highly maintainable and performant solution. The emphasis on AI-assisted automation in conversion steps ensures consistency and reduces manual effort, while the clear, non-technical language facilitates understanding for all stakeholders.