## ASP.NET to Django Conversion Script:

The provided ASP.NET code for `PR_Dashboard.aspx` and its C# code-behind `PR_Dashboard.aspx.cs` is very minimal. It primarily defines content placeholders within a master page and an empty `Page_Load` event. This means there is no explicit database interaction, UI controls, or business logic defined directly within these files.

Therefore, to provide a comprehensive Django modernization plan that includes runnable code, I must make informed assumptions based on the naming convention "PR_Dashboard" (likely Purchase Request Dashboard) and general patterns of enterprise applications. The following plan will demonstrate how such a module would be migrated to Django, adhering to all specified best practices, using a hypothetical `PurchaseRequest` entity.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
Given the provided ASP.NET code is empty of data access logic, we cannot directly extract the database schema. However, based on the `PR_Dashboard` name (Purchase Request Dashboard), we infer the existence of a table related to "Purchase Requests."

**Inferred Database Table and Columns:**
*   **[TABLE_NAME]:** `tblPurchaseRequests` (assuming a common `tbl` prefix)
*   **Columns:**
    *   `PRNumber` (e.g., `varchar(50)`) - Unique identifier for the Purchase Request
    *   `RequestDate` (e.g., `datetime`) - Date the request was made
    *   `RequestedBy` (e.g., `varchar(100)`) - User who initiated the request
    *   `Status` (e.g., `varchar(50)`) - Current status (e.g., 'Pending', 'Approved', 'Rejected')
    *   `TotalAmount` (e.g., `decimal(18,2)`) - Estimated total amount of the request
    *   `Description` (e.g., `nvarchar(MAX)`) - Detailed description of the request

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
Since the provided ASP.NET code has no explicit UI controls or event handlers for data operations, no CRUD functionality can be identified. For a "Dashboard" typically implies "Read" (listing), but a comprehensive system would include "Create", "Update", and "Delete" actions.

**Assumed Functionality for Django Migration:**
*   **Read:** Displaying a list of Purchase Requests.
*   **Create:** Adding new Purchase Requests.
*   **Update:** Editing existing Purchase Requests.
*   **Delete:** Removing Purchase Requests.
*   **Validation:** Basic field validation (e.g., required fields, date formats) will be implemented in the Django form.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The ASP.NET file contains only `Content` tags, so no specific UI controls are present.

**Assumed UI Components for Django Migration:**
*   **List View:** A table (Django template with DataTables) to display all `PurchaseRequest` records. This replaces the common ASP.NET `GridView`.
*   **Form for CRUD:** A form (Django `ModelForm`) for creating and updating `PurchaseRequest` records. This replaces ASP.NET `TextBox`, `DropDownList`, `Button` components.
*   **Action Buttons:** Buttons for "Add New," "Edit," and "Delete" for each record, triggering modals via HTMX.

### Step 4: Generate Django Code

We will create a new Django app named `material_management` to encapsulate this module.

#### 4.1 Models

**Task:** Create a Django model based on the database schema.

**Instructions:**
The model `PurchaseRequest` will map to `tblPurchaseRequests`. `managed = False` is crucial as we are assuming the table already exists in the legacy database. We'll add a simple business logic method `is_pending()` and a `status_display_class` to demonstrate the "fat model" approach.

**File: `material_management/models.py`**
```python
from django.db import models

class PurchaseRequest(models.Model):
    """
    Represents a Purchase Request from the legacy tblPurchaseRequests table.
    """
    pr_number = models.CharField(db_column='PRNumber', max_length=50, unique=True, verbose_name="PR Number")
    request_date = models.DateField(db_column='RequestDate', verbose_name="Request Date")
    requested_by = models.CharField(db_column='RequestedBy', max_length=100, verbose_name="Requested By")
    status = models.CharField(db_column='Status', max_length=50, default='Pending', verbose_name="Status")
    total_amount = models.DecimalField(db_column='TotalAmount', max_digits=18, decimal_places=2, null=True, blank=True, verbose_name="Total Amount")
    description = models.TextField(db_column='Description', null=True, blank=True, verbose_name="Description")

    class Meta:
        managed = False  # Important: Django will not manage this table's creation/deletion
        db_table = 'tblPurchaseRequests'
        verbose_name = 'Purchase Request'
        verbose_name_plural = 'Purchase Requests'
        ordering = ['-request_date', 'pr_number'] # Default ordering

    def __str__(self):
        return f"{self.pr_number} - {self.status}"
        
    def is_pending(self):
        """
        Business logic: Checks if the purchase request is in a 'Pending' status.
        """
        return self.status == 'Pending'
        
    def get_status_display_class(self):
        """
        Business logic: Returns a CSS class based on the status for UI styling.
        """
        if self.status == 'Approved':
            return 'text-green-600 bg-green-100'
        elif self.status == 'Rejected':
            return 'text-red-600 bg-red-100'
        elif self.status == 'Pending':
            return 'text-yellow-600 bg-yellow-100'
        else:
            return 'text-gray-600 bg-gray-100'

    # Example of a more complex business method
    def approve_request(self):
        """
        Business logic: Changes the status to 'Approved'.
        """
        if self.status == 'Pending':
            self.status = 'Approved'
            self.save()
            return True
        return False
```

#### 4.2 Forms

**Task:** Define a Django form for user input.

**Instructions:**
A `ModelForm` will be created for `PurchaseRequest`. Widgets will be styled with Tailwind CSS classes.

**File: `material_management/forms.py`**
```python
from django import forms
from .models import PurchaseRequest

class PurchaseRequestForm(forms.ModelForm):
    class Meta:
        model = PurchaseRequest
        fields = ['pr_number', 'request_date', 'requested_by', 'status', 'total_amount', 'description']
        widgets = {
            'pr_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'PR-YYYY-MM-DD-001'}),
            'request_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'requested_by': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'John Doe'}),
            'status': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}, choices=[('Pending', 'Pending'), ('Approved', 'Approved'), ('Rejected', 'Rejected')]),
            'total_amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01'}),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
        }
        labels = {
            'pr_number': 'PR Number',
            'request_date': 'Request Date',
            'requested_by': 'Requested By',
            'status': 'Status',
            'total_amount': 'Total Amount',
            'description': 'Description',
        }

    # Custom validation example (e.g., PR number format)
    def clean_pr_number(self):
        pr_number = self.cleaned_data['pr_number']
        # Example: Ensure PR number starts with 'PR-'
        if not pr_number.startswith('PR-'):
            raise forms.ValidationError("PR Number must start with 'PR-'.")
        return pr_number
```

#### 4.3 Views

**Task:** Implement CRUD operations using CBVs.

**Instructions:**
Views are kept thin, delegating business logic to the model. HTMX headers are used to trigger client-side refreshes without full page reloads after CRUD operations. A `TablePartialView` is added for HTMX-driven table refreshes.

**File: `material_management/views.py`**
```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import PurchaseRequest
from .forms import PurchaseRequestForm

class PurchaseRequestListView(ListView):
    """
    Displays the main dashboard for Purchase Requests.
    The actual table content is loaded via HTMX.
    """
    model = PurchaseRequest
    template_name = 'material_management/purchaserequest/list.html'
    context_object_name = 'purchase_requests' # Not strictly needed as table is loaded via HTMX, but good practice.

class PurchaseRequestTablePartialView(ListView):
    """
    Returns the partial HTML for the Purchase Request table, used by HTMX.
    """
    model = PurchaseRequest
    template_name = 'material_management/purchaserequest/_purchaserequest_table.html'
    context_object_name = 'purchase_requests'
    # No complex query here, as per fat model/thin view. Filtering/sorting would be in model managers.

class PurchaseRequestCreateView(CreateView):
    """
    Handles creation of new Purchase Requests, typically in a modal.
    """
    model = PurchaseRequest
    form_class = PurchaseRequestForm
    template_name = 'material_management/purchaserequest/_purchaserequest_form.html' # Use partial template for modal
    success_url = reverse_lazy('purchaserequest_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Purchase Request added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content to swap, just trigger events
                headers={
                    'HX-Trigger': 'refreshPurchaseRequestList' # Trigger custom event on client
                }
            )
        return response

class PurchaseRequestUpdateView(UpdateView):
    """
    Handles updating existing Purchase Requests, typically in a modal.
    """
    model = PurchaseRequest
    form_class = PurchaseRequestForm
    template_name = 'material_management/purchaserequest/_purchaserequest_form.html' # Use partial template for modal
    success_url = reverse_lazy('purchaserequest_list')

    def form_valid(self, form):
        # Example of calling a model method if there was complex logic before saving
        # if form.instance.some_condition_changed():
        #     form.instance.update_associated_data()
        response = super().form_valid(form)
        messages.success(self.request, 'Purchase Request updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPurchaseRequestList'
                }
            )
        return response

class PurchaseRequestDeleteView(DeleteView):
    """
    Handles deletion of Purchase Requests, typically in a confirmation modal.
    """
    model = PurchaseRequest
    template_name = 'material_management/purchaserequest/_purchaserequest_confirm_delete.html' # Use partial template
    success_url = reverse_lazy('purchaserequest_list')

    def delete(self, request, *args, **kwargs):
        # Example of calling a model method before deletion
        # if self.get_object().is_linked_to_active_order():
        #     messages.error(request, 'Cannot delete an active purchase request.')
        #     return HttpResponse(status=400) # Or render form with error
        
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Purchase Request deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPurchaseRequestList'
                }
            )
        return response

```

#### 4.4 Templates

**Task:** Create templates for each view.

**Instructions:**
Templates utilize `core/base.html` for inheritance (not included here), HTMX for dynamic content loading, Alpine.js for modal behavior, and DataTables for list presentation.

**File: `material_management/templates/material_management/purchaserequest/list.html`**
```html
{% extends 'core/base.html' %}
{% load static %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Purchase Requests Dashboard</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-150 ease-in-out"
            hx-get="{% url 'purchaserequest_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal"> {# 'is-active' for Bulma-like or custom CSS that shows the modal #}
            <i class="fas fa-plus mr-2"></i> Add New Purchase Request
        </button>
    </div>
    
    <div id="purchaserequestTable-container"
         hx-trigger="load, refreshPurchaseRequestList from:body" {# Listen for custom event to refresh table #}
         hx-get="{% url 'purchaserequest_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg overflow-hidden p-6">
        <!-- Initial loading indicator -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Purchase Requests...</p>
        </div>
    </div>
    
    <!-- Universal Modal for forms and confirmations -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-2xl w-full relative transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
             _="on click if event.target.closest('form') or event.target.tagName == 'BUTTON' then halt else wait 20ms end"> {# Prevent closing modal when clicking inside form #}
            {# Modal content loaded via HTMX #}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Example Alpine.js component if needed for more complex UI state
    document.addEventListener('alpine:init', () => {
        Alpine.data('purchaseRequestDashboard', () => ({
            // No specific state needed for this simple case, as HTMX handles most interactions
        }));
    });

    // Handle HTMX after swapping content to re-initialize DataTables
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'purchaserequestTable-container') {
            // Check if DataTables instance already exists and destroy it first to prevent re-initialization errors
            if ($.fn.DataTable.isDataTable('#purchaseRequestTable')) {
                $('#purchaseRequestTable').DataTable().destroy();
            }
            $('#purchaseRequestTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "dom": 'lfrtip', // Show length changing, filtering, processing, info, pagination, table
                "responsive": true, // DataTables responsiveness
            });
        }
    });
</script>
{% endblock %}

```

**File: `material_management/templates/material_management/purchaserequest/_purchaserequest_table.html`**
```html
<div class="overflow-x-auto">
    <table id="purchaseRequestTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PR Number</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Request Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Requested By</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for pr in purchase_requests %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ pr.pr_number }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ pr.request_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ pr.requested_by }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ pr.get_status_display_class }}">{{ pr.status }}</span>
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">${{ pr.total_amount|default:"0.00"|floatformat:2 }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 shadow-sm transition duration-150 ease-in-out"
                        hx-get="{% url 'purchaserequest_edit' pr.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md shadow-sm transition duration-150 ease-in-out"
                        hx-get="{% url 'purchaserequest_delete' pr.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-4 px-6 text-center text-gray-500">No Purchase Requests found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

{# The DataTables initialization is now handled in the main list.html via htmx:afterSwap listener #}

```

**File: `material_management/templates/material_management/purchaserequest/_purchaserequest_form.html`**
```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Purchase Request</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" 
          hx-on::after-request="if(event.detail.xhr.status === 204) remove .is-active from #modal; else console.error('Form submission failed:', event.detail.xhr.statusText);">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {% for field in form %}
            <div class="col-span-1">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %} <span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="text-red-600 text-xs mt-1 list-disc list-inside">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

**File: `material_management/templates/material_management/purchaserequest/_purchaserequest_confirm_delete.html`**
```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Purchase Request <strong>"{{ object.pr_number }}"</strong>?</p>
    <p class="text-sm text-red-600 mb-6">This action cannot be undone.</p>
    
    <form hx-post="{% url 'purchaserequest_delete' object.pk %}" hx-swap="none" 
          hx-on::after-request="if(event.detail.xhr.status === 204) remove .is-active from #modal;">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
URLs are defined for the main list, CRUD operations, and the HTMX-specific table partial.

**File: `material_management/urls.py`**
```python
from django.urls import path
from .views import (
    PurchaseRequestListView, 
    PurchaseRequestTablePartialView,
    PurchaseRequestCreateView, 
    PurchaseRequestUpdateView, 
    PurchaseRequestDeleteView
)

urlpatterns = [
    path('purchaserequests/', PurchaseRequestListView.as_view(), name='purchaserequest_list'),
    path('purchaserequests/add/', PurchaseRequestCreateView.as_view(), name='purchaserequest_add'),
    path('purchaserequests/edit/<int:pk>/', PurchaseRequestUpdateView.as_view(), name='purchaserequest_edit'),
    path('purchaserequests/delete/<int:pk>/', PurchaseRequestDeleteView.as_view(), name='purchaserequest_delete'),
    # HTMX-specific endpoint for refreshing the table content
    path('purchaserequests/table/', PurchaseRequestTablePartialView.as_view(), name='purchaserequest_table'),
]

```
**Integration into Project `urls.py` (e.g., `myproject/urls.py`)**
```python
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('material_management/', include('material_management.urls')), # Include your new app's URLs
    # ... other project URLs
]
```

#### 4.6 Tests

**Task:** Write tests for the model and views.

**Instructions:**
Comprehensive unit tests for model methods and properties, and integration tests for all views. This ensures functionality and helps maintain quality during migration.

**File: `material_management/tests.py`**
```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import PurchaseRequest
from .forms import PurchaseRequestForm

class PurchaseRequestModelTest(TestCase):
    """
    Unit tests for the PurchaseRequest model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        PurchaseRequest.objects.create(
            pr_number='PR-2023-10-001',
            request_date='2023-10-26',
            requested_by='Alice Johnson',
            status='Pending',
            total_amount=1500.75,
            description='Test PR for office supplies'
        )
        PurchaseRequest.objects.create(
            pr_number='PR-2023-10-002',
            request_date='2023-10-25',
            requested_by='Bob Williams',
            status='Approved',
            total_amount=500.00,
            description='Test PR for software license renewal'
        )
  
    def test_purchaserequest_creation(self):
        """
        Verify that a PurchaseRequest object can be created and its attributes are correct.
        """
        pr1 = PurchaseRequest.objects.get(pr_number='PR-2023-10-001')
        pr2 = PurchaseRequest.objects.get(pr_number='PR-2023-10-002')

        self.assertEqual(pr1.pr_number, 'PR-2023-10-001')
        self.assertEqual(pr1.requested_by, 'Alice Johnson')
        self.assertEqual(str(pr1.request_date), '2023-10-26')
        self.assertEqual(pr1.status, 'Pending')
        self.assertEqual(pr1.total_amount, 1500.75)
        self.assertEqual(pr1.description, 'Test PR for office supplies')

        self.assertEqual(pr2.pr_number, 'PR-2023-10-002')
        self.assertEqual(pr2.status, 'Approved')

    def test_pr_number_label(self):
        """
        Verify the verbose name for the 'pr_number' field.
        """
        pr = PurchaseRequest.objects.get(id=1)
        field_label = pr._meta.get_field('pr_number').verbose_name
        self.assertEqual(field_label, 'PR Number')

    def test_is_pending_method(self):
        """
        Test the custom 'is_pending' model method.
        """
        pr_pending = PurchaseRequest.objects.get(pr_number='PR-2023-10-001')
        pr_approved = PurchaseRequest.objects.get(pr_number='PR-2023-10-002')
        self.assertTrue(pr_pending.is_pending())
        self.assertFalse(pr_approved.is_pending())

    def test_get_status_display_class_method(self):
        """
        Test the custom 'get_status_display_class' model method.
        """
        pr_pending = PurchaseRequest.objects.get(pr_number='PR-2023-10-001')
        pr_approved = PurchaseRequest.objects.get(pr_number='PR-2023-10-002')
        
        self.assertEqual(pr_pending.get_status_display_class(), 'text-yellow-600 bg-yellow-100')
        self.assertEqual(pr_approved.get_status_display_class(), 'text-green-600 bg-green-100')

        # Test for a 'Rejected' status (create a temporary object)
        pr_rejected = PurchaseRequest.objects.create(
            pr_number='PR-2023-10-003',
            request_date='2023-10-24',
            requested_by='Charlie Brown',
            status='Rejected',
            total_amount=75.50
        )
        self.assertEqual(pr_rejected.get_status_display_class(), 'text-red-600 bg-red-100')
        pr_rejected.delete() # Clean up temporary object


    def test_approve_request_method(self):
        """
        Test the custom 'approve_request' model method.
        """
        pr_to_approve = PurchaseRequest.objects.get(pr_number='PR-2023-10-001')
        self.assertTrue(pr_to_approve.approve_request())
        pr_to_approve.refresh_from_db()
        self.assertEqual(pr_to_approve.status, 'Approved')

        pr_already_approved = PurchaseRequest.objects.get(pr_number='PR-2023-10-002')
        self.assertFalse(pr_already_approved.approve_request()) # Should not change if already approved


class PurchaseRequestFormTest(TestCase):
    """
    Unit tests for the PurchaseRequestForm.
    """
    def test_form_valid_data(self):
        form_data = {
            'pr_number': 'PR-2023-11-001',
            'request_date': '2023-11-01',
            'requested_by': 'Test User',
            'status': 'Pending',
            'total_amount': 100.00,
            'description': 'New item request'
        }
        form = PurchaseRequestForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_form_invalid_pr_number_format(self):
        form_data = {
            'pr_number': 'INVALID-001', # Does not start with 'PR-'
            'request_date': '2023-11-01',
            'requested_by': 'Test User',
            'status': 'Pending',
            'total_amount': 100.00,
            'description': 'New item request'
        }
        form = PurchaseRequestForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('PR Number must start with \'PR-\'.', form.errors['pr_number'][0])

    def test_form_missing_required_fields(self):
        form_data = {
            'pr_number': 'PR-2023-11-002',
            # Missing request_date and requested_by
        }
        form = PurchaseRequestForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('request_date', form.errors)
        self.assertIn('requested_by', form.errors)


class PurchaseRequestViewsTest(TestCase):
    """
    Integration tests for PurchaseRequest views.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        PurchaseRequest.objects.create(
            pr_number='PR-VIEW-001',
            request_date='2023-10-20',
            requested_by='View Test User',
            status='Pending',
            total_amount=99.99
        )
    
    def setUp(self):
        # Set up data for each test method
        self.client = Client()
        self.list_url = reverse('purchaserequest_list')
        self.add_url = reverse('purchaserequest_add')
        self.edit_url = reverse('purchaserequest_edit', args=[1]) # Assuming ID 1 exists
        self.delete_url = reverse('purchaserequest_delete', args=[1]) # Assuming ID 1 exists
        
    def test_list_view_get(self):
        """
        Test that the list view renders correctly and contains purchase requests.
        """
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaserequest/list.html')
        # We don't check context_object_name directly here as the list is loaded via HTMX

    def test_table_partial_view_get(self):
        """
        Test that the HTMX table partial view renders correctly.
        """
        response = self.client.get(reverse('purchaserequest_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaserequest/_purchaserequest_table.html')
        self.assertContains(response, 'PR-VIEW-001') # Check if existing data is present

    def test_create_view_get(self):
        """
        Test that the create form view renders correctly.
        """
        response = self.client.get(self.add_url, HTTP_HX_REQUEST='true') # Simulate HTMX request
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaserequest/_purchaserequest_form.html')
        self.assertContains(response, 'Add Purchase Request')
        self.assertTrue('form' in response.context)
        
    def test_create_view_post_success(self):
        """
        Test successful creation of a new purchase request via POST.
        """
        initial_count = PurchaseRequest.objects.count()
        new_data = {
            'pr_number': 'PR-NEW-001',
            'request_date': '2024-01-01',
            'requested_by': 'New Creator',
            'status': 'Pending',
            'total_amount': 200.00,
            'description': 'New PR test'
        }
        response = self.client.post(self.add_url, new_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success response (No Content)
        self.assertTrue(PurchaseRequest.objects.filter(pr_number='PR-NEW-001').exists())
        self.assertEqual(PurchaseRequest.objects.count(), initial_count + 1)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshPurchaseRequestList')

    def test_create_view_post_invalid(self):
        """
        Test invalid form submission for creation.
        """
        initial_count = PurchaseRequest.objects.count()
        invalid_data = {
            'pr_number': 'INVALID-001', # Invalid format
            'request_date': '2024-01-01',
            'requested_by': 'Test User',
            'status': 'Pending'
            # Missing total_amount, description
        }
        response = self.client.post(self.add_url, invalid_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertFalse(PurchaseRequest.objects.filter(pr_number='INVALID-001').exists())
        self.assertEqual(PurchaseRequest.objects.count(), initial_count)
        self.assertTemplateUsed(response, 'material_management/purchaserequest/_purchaserequest_form.html')
        self.assertContains(response, 'PR Number must start with &#x27;PR-&#x27;.')


    def test_update_view_get(self):
        """
        Test that the update form view renders correctly for an existing object.
        """
        obj = PurchaseRequest.objects.get(id=1)
        response = self.client.get(reverse('purchaserequest_edit', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaserequest/_purchaserequest_form.html')
        self.assertContains(response, 'Edit Purchase Request')
        self.assertEqual(response.context['form'].instance, obj)
        
    def test_update_view_post_success(self):
        """
        Test successful update of an existing purchase request via POST.
        """
        obj = PurchaseRequest.objects.get(id=1)
        updated_data = {
            'pr_number': obj.pr_number, # Keep the same valid PR number
            'request_date': obj.request_date,
            'requested_by': 'Updated User Name',
            'status': 'Approved',
            'total_amount': obj.total_amount,
            'description': 'Updated description'
        }
        response = self.client.post(reverse('purchaserequest_edit', args=[obj.id]), updated_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        obj.refresh_from_db()
        self.assertEqual(obj.requested_by, 'Updated User Name')
        self.assertEqual(obj.status, 'Approved')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshPurchaseRequestList')

    def test_delete_view_get(self):
        """
        Test that the delete confirmation view renders correctly.
        """
        obj = PurchaseRequest.objects.get(id=1)
        response = self.client.get(reverse('purchaserequest_delete', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaserequest/_purchaserequest_confirm_delete.html')
        self.assertContains(response, f'delete the Purchase Request &quot;{obj.pr_number}&quot;')
        
    def test_delete_view_post_success(self):
        """
        Test successful deletion of a purchase request via POST.
        """
        # Create a new object to delete to avoid interfering with other tests' assumptions
        pr_to_delete = PurchaseRequest.objects.create(
            pr_number='PR-DELETE-001',
            request_date='2023-12-01',
            requested_by='Delete Test',
            status='Pending',
            total_amount=10.00
        )
        initial_count = PurchaseRequest.objects.count()
        response = self.client.post(reverse('purchaserequest_delete', args=[pr_to_delete.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(PurchaseRequest.objects.filter(id=pr_to_delete.id).exists())
        self.assertEqual(PurchaseRequest.objects.count(), initial_count - 1)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshPurchaseRequestList')

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for dynamic updates:**
    *   The main `list.html` uses `hx-get="{% url 'purchaserequest_table' %}"` with `hx-trigger="load, refreshPurchaseRequestList from:body"` to load and refresh the table content dynamically.
    *   "Add New", "Edit", and "Delete" buttons use `hx-get` to fetch the form/confirmation partials into a modal.
    *   Form submissions (`hx-post`) from the modal are handled via HTMX, with `hx-swap="none"` and `HX-Trigger` headers (`refreshPurchaseRequestList`) to close the modal and refresh the main table after successful operations.
*   **Alpine.js for UI state management:**
    *   Alpine.js (`_` attributes) is used to toggle the `is-active` class on the `#modal` element, controlling its visibility. This is a compact way to manage simple UI interactions without writing extensive custom JavaScript.
*   **DataTables for list views:**
    *   The `_purchaserequest_table.html` partial contains a `<table id="purchaseRequestTable">`.
    *   The `list.html` template includes a JavaScript snippet in its `extra_js` block that listens for the `htmx:afterSwap` event. When the table partial is loaded, it re-initializes `$('#purchaseRequestTable').DataTable()`, ensuring that DataTables functionality (searching, sorting, pagination) works correctly even after HTMX updates.
*   **No full page reloads:** All CRUD operations and table refreshes are performed without full page reloads, providing a smooth user experience.
*   **Strict separation:** HTML is in templates, business logic is in models, and views are simple orchestrators.

## Final Notes

*   This plan provides a complete Django implementation based on the inferred functionality of a "PR_Dashboard."
*   Replace placeholders like `material_management` (for app name) and `PurchaseRequest` (for model) with actual names from your ASP.NET application where appropriate.
*   Ensure your `core/base.html` includes necessary CDN links for HTMX, Alpine.js, Tailwind CSS, jQuery, and DataTables (and its CSS).
*   Remember to configure your Django project's `settings.py` to include the `material_management` app and set up your database connection to point to your existing legacy database (e.g., SQL Server using `django-pyodbc-azure` or `mssql-django`).
*   The `managed = False` setting in the `PurchaseRequest` model is critical to prevent Django from attempting to create or modify the `tblPurchaseRequests` table in your legacy database.