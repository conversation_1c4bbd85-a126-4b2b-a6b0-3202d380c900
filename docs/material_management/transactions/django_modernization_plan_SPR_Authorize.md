## ASP.NET to Django Conversion Script: SPR Authorization Module

This document outlines a comprehensive modernization plan to transition your legacy ASP.NET SPR Authorization module to a modern Django-based solution. Our approach prioritizes AI-assisted automation, ensuring a smooth, efficient, and maintainable transition. We focus on transforming the existing functionality into a robust, scalable, and user-friendly Django application leveraging current best practices.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is extended.
- Focus ONLY on component-specific code for the current module (`spr_authorization`).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

### Conversion Steps:

#### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
From the ASP.NET code-behind, we identify the following key tables and their inferred columns based on SQL queries and `DataTable` definitions:

1.  **`tblMM_SPR_Master`**: This is the primary table for SPR (Store Purchase Requisition) records.
    *   `Id` (PK, int)
    *   `SPRNo` (string)
    *   `SysDate` (date)
    *   `SysTime` (time)
    *   `SessionId` (int) - Refers to the Employee ID who generated the SPR.
    *   `Checked` (int/boolean)
    *   `CheckedDate` (date)
    *   `Approve` (int/boolean)
    *   `ApproveDate` (date)
    *   `Authorize` (int/boolean) - The core status for this page (0 for pending, 1 for authorized).
    *   `AuthorizeDate` (date)
    *   `AuthorizedBy` (string/int) - User ID who authorized.
    *   `AuthorizeTime` (time)
    *   `FinYearId` (int) - Foreign key to `tblFinancial_master`.
    *   `CompId` (int) - Foreign key to a Company table (assuming `tblCompanyMaster`).

2.  **`tblHR_OfficeStaff`**: Used for employee details (generator and authorizer).
    *   `EmpId` (PK, int)
    *   `EmployeeName` (string)
    *   `Title` (string)
    *   `CompId` (int)

3.  **`tblFinancial_master`**: Used for financial year details.
    *   `FinYearId` (PK, int)
    *   `FinYear` (string)
    *   `CompId` (int)

#### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic in the ASP.NET code.

**Instructions:**
The ASP.NET module primarily performs "Read" and "Update" operations:

*   **Read (Display List):**
    *   Retrieves a list of SPRs from `tblMM_SPR_Master`.
    *   Filters records where `Approve` is `1` (approved) and `Authorize` is `0` (pending authorization).
    *   Applies filters based on user input: `SPRNo` or `EmployeeName` (generator).
    *   Joins with `tblHR_OfficeStaff` to display "Generated By" (GenBy) employee name.
    *   Joins with `tblFinancial_master` to display "Fin Year".
    *   Displays various dates (Date, Time, Checked, Approved, Authorized).
    *   Handles pagination and sorting (implied by `GridView`).
*   **Update (Authorize SPRs):**
    *   Allows users to select multiple SPRs via checkboxes.
    *   Updates the `Authorize` status to `1` for selected SPRs.
    *   Records `AuthorizedBy` (current user), `AuthorizeDate`, and `AuthorizeTime`.
*   **View Detail:**
    *   A "View" link for each SPR, which redirects to a separate print/view page (`SPR_View_Print.aspx`). This will be a simple link to a new Django view in the modernized application.
*   **Autocomplete:**
    *   Provides autocomplete suggestions for Employee Names using `tblHR_OfficeStaff`.

#### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The user interface comprises:

*   **Search/Filter Section:**
    *   A `DropDownList` (`drpfield`) to select search criteria: "Employee Name" or "SPR No".
    *   Two `TextBox` controls (`txtEmpName`, `txtSprNo`) for input, one visible at a time based on the dropdown selection.
    *   An `AutoCompleteExtender` for `txtEmpName` for employee name suggestions.
    *   A "Search" `Button` to apply filters.
*   **Data Display:**
    *   A `GridView` (`GridView2`) to display the list of SPRs in a tabular format.
    *   Columns include: SN (Serial Number), Id (hidden), Fin Year, SPR No, Date, Time, Gen. By, Checked Date, Approved Date, and a "For Authorize" column with a `CheckBox` and `Label` for the authorized date.
    *   Pagination controls (handled by `GridView`).
*   **Action Button:**
    *   An "Authorized" `Button` (`Auth`) to trigger the bulk authorization action.
*   **Client-Side Behavior:**
    *   The `drpfield` `AutoPostBack` toggles visibility of search textboxes. This will be handled by Alpine.js in Django.
    *   The `AutoCompleteExtender` suggests employee names, which will be an HTMX-powered autocomplete in Django.
    *   The "Authorized" button uses `OnClientClick=" return confirmation()"`, implying a client-side confirmation dialog before submission. This will be replaced by an Alpine.js-driven modal or HTMX confirmation.

---

#### Step 4: Generate Django Code

We will create a new Django application named `spr_authorization`.

##### 4.1 Models

**Task:** Create Django models based on the identified database schema, adhering to `managed = False` for existing tables.

**Instructions:**
Define `SprMaster`, `HrOfficeStaff`, and `FinancialYear` models. Implement a class method `get_pending_for_authorization` in `SprMaster` to encapsulate the complex data retrieval logic, including joins and dynamic filtering.

```python
# spr_authorization/models.py
from django.db import models
from django.db.models import Q
from django.utils import timezone

class HrOfficeStaff(models.Model):
    EmpId = models.IntegerField(db_column='EmpId', primary_key=True)
    EmployeeName = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)
    Title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    CompId = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'HR Office Staff'
        verbose_name_plural = 'HR Office Staff'

    def __str__(self):
        return f"{self.Title or ''} {self.EmployeeName or ''}".strip()

class FinancialYear(models.Model):
    FinYearId = models.IntegerField(db_column='FinYearId', primary_key=True)
    FinYear = models.CharField(db_column='FinYear', max_length=50, blank=True, null=True)
    CompId = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.FinYear or ''

class SprMaster(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    SPRNo = models.CharField(db_column='SPRNo', max_length=50, blank=True, null=True)
    SysDate = models.DateField(db_column='SysDate', blank=True, null=True)
    SysTime = models.TimeField(db_column='SysTime', blank=True, null=True)
    SessionId = models.IntegerField(db_column='SessionId', blank=True, null=True) # Employee ID who generated
    Checked = models.IntegerField(db_column='Checked', blank=True, null=True) # 0 or 1
    CheckedDate = models.DateField(db_column='CheckedDate', blank=True, null=True)
    Approve = models.IntegerField(db_column='Approve', blank=True, null=True) # 0 or 1
    ApproveDate = models.DateField(db_column='ApproveDate', blank=True, null=True)
    Authorize = models.IntegerField(db_column='Authorize', blank=True, null=True) # 0 or 1
    AuthorizeDate = models.DateField(db_column='AuthorizeDate', blank=True, null=True)
    AuthorizedBy = models.CharField(db_column='AuthorizedBy', max_length=50, blank=True, null=True) # User ID who authorized
    AuthorizeTime = models.TimeField(db_column='AuthorizeTime', blank=True, null=True)
    FinYearId = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    CompId = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Master'
        verbose_name = 'SPR Master'
        verbose_name_plural = 'SPR Masters'

    def __str__(self):
        return self.SPRNo or f"SPR {self.Id}"

    @property
    def generator_name(self):
        """Returns the full name of the employee who generated this SPR."""
        try:
            staff = HrOfficeStaff.objects.get(EmpId=self.SessionId, CompId=self.CompId)
            return str(staff)
        except HrOfficeStaff.DoesNotExist:
            return "N/A"

    @property
    def financial_year_display(self):
        """Returns the display name of the financial year."""
        try:
            fin_year = FinancialYear.objects.get(FinYearId=self.FinYearId, CompId=self.CompId)
            return str(fin_year)
        except FinancialYear.DoesNotExist:
            return "N/A"

    @classmethod
    def get_pending_for_authorization(cls, current_company_id, current_financial_year_id, search_type=None, search_query=None):
        """
        Retrieves SPRs pending authorization, with optional search filters.
        This method replaces the 'makegrid' logic.
        """
        # Base query: approved and not yet authorized for current company and financial year
        queryset = cls.objects.filter(
            CompId=current_company_id,
            FinYearId__lte=current_financial_year_id, # Original logic was FinYearId<=FyId
            Approve=1,
            Authorize=0
        ).order_by('-Id')

        if search_query:
            if search_type == '1':  # Search by SPR No
                queryset = queryset.filter(SPRNo__iexact=search_query)
            elif search_type == '0':  # Search by Employee Name (Generator)
                # Need to find EmpId from EmployeeName
                try:
                    # In a real scenario, this should be pre-fetched or an actual join.
                    # For `managed=False` and complex lookups, it's often done with raw SQL
                    # or by first querying the lookup table.
                    # Simulating the original C# fun.getCode(txtEmpName.Text) behavior:
                    staff_query = HrOfficeStaff.objects.filter(EmployeeName__iexact=search_query, CompId=current_company_id)
                    if staff_query.exists():
                        employee_id = staff_query.first().EmpId
                        queryset = queryset.filter(SessionId=employee_id)
                    else:
                        queryset = queryset.none() # No matching employee, so no results
                except Exception: # Handle potential issues if employee name is not unique or not found
                    queryset = queryset.none()

        return queryset

    @classmethod
    def authorize_sps_by_ids(cls, spr_ids, authorized_by_user_id, current_company_id):
        """
        Authorizes a list of SPRs by their IDs.
        This method encapsulates the bulk update logic from 'Auth_Click'.
        """
        now = timezone.now()
        updated_count = cls.objects.filter(
            Id__in=spr_ids,
            CompId=current_company_id,
            Authorize=0 # Ensure only pending ones are authorized
        ).update(
            Authorize=1,
            AuthorizedBy=str(authorized_by_user_id), # Store as string, match original logic
            AuthorizeDate=now.date(),
            AuthorizeTime=now.time()
        )
        return updated_count
```

##### 4.2 Forms

**Task:** Define a Django form for user input. In this case, we'll primarily use it for the "Authorize" action confirmation in a modal. The search parameters are handled directly via GET requests in the views.

**Instructions:**
A simple form might be needed if we want more complex validation for bulk authorization, but for just confirming, a hidden input for IDs is enough. No specific `ModelForm` is required for the main display or search.

```python
# spr_authorization/forms.py
from django import forms

class AuthorizeSprForm(forms.Form):
    # This form will be used to pass the selected SPR IDs for authorization.
    # The IDs will be collected from checkboxes on the client-side.
    spr_ids = forms.CharField(
        widget=forms.HiddenInput(attrs={'id': 'selectedSprIds'}),
        required=True
    )
    # We could add a confirm message field if we wanted user input
    # confirm_message = forms.CharField(
    #     widget=forms.Textarea(attrs={'class': 'w-full p-2 border rounded'}),
    #     required=False
    # )

    def clean_spr_ids(self):
        ids_str = self.cleaned_data['spr_ids']
        if not ids_str:
            raise forms.ValidationError("No SPRs selected for authorization.")
        try:
            # Convert comma-separated string of IDs to a list of integers
            return [int(s.strip()) for s in ids_str.split(',') if s.strip()]
        except ValueError:
            raise forms.ValidationError("Invalid SPR IDs provided.")

```

##### 4.3 Views

**Task:** Implement the list view, search, autocomplete, and bulk authorization using CBVs and HTMX.

**Instructions:**
Views will be thin, delegating heavy lifting to model methods. We'll use `ListView` for the main page and specialized `View` for HTMX partials and actions.

```python
# spr_authorization/views.py
from django.views.generic import ListView, View
from django.http import JsonResponse, HttpResponse
from django.urls import reverse_lazy
from django.contrib import messages
from django.db.models import F # For atomic updates potentially
import json

from .models import SprMaster, HrOfficeStaff
from .forms import AuthorizeSprForm # For the bulk authorization form

class SprAuthorizationListView(ListView):
    """
    Displays the main SPR Authorization page.
    This view handles the initial page load and overall structure.
    """
    model = SprMaster
    template_name = 'spr_authorization/spr_authorization_list.html'
    context_object_name = 'sprauths' # Will be used by the table partial view
    
    def get_queryset(self):
        # Initial queryset, actual data will be loaded via HTMX to _spr_authorization_table.html
        return SprMaster.objects.none() # Don't load data on initial page load

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_type'] = self.request.GET.get('search_type', '0') # Default to Employee Name
        context['search_query'] = self.request.GET.get('search_query', '')
        return context

class SprAuthorizationTablePartialView(ListView):
    """
    HTMX endpoint to load/refresh the SPR authorization table content.
    This view encapsulates the 'makegrid' logic.
    """
    model = SprMaster
    template_name = 'spr_authorization/_spr_authorization_table.html'
    context_object_name = 'sprauth_list'

    def get_queryset(self):
        # These would ideally come from the logged-in user's profile or session.
        # For demonstration, using placeholders.
        current_company_id = self.request.session.get('compid', 1) # Example, replace with actual way to get CompId
        current_financial_year_id = self.request.session.get('finyear', 2023) # Example, replace with actual way to get FinYearId

        search_type = self.request.GET.get('search_type')
        search_query = self.request.GET.get('search_query')

        queryset = SprMaster.get_pending_for_authorization(
            current_company_id,
            current_financial_year_id,
            search_type,
            search_query
        )
        return queryset

class EmployeeAutocompleteView(View):
    """
    HTMX endpoint for employee name autocomplete.
    Replaces GetCompletionList WebMethod.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        current_company_id = self.request.session.get('compid', 1) # Example

        if len(query) < 1: # Minimum prefix length
            return JsonResponse([], safe=False)

        employees = HrOfficeStaff.objects.filter(
            EmployeeName__icontains=query,
            CompId=current_company_id
        ).values('EmpId', 'EmployeeName')[:10] # Limit to 10 suggestions

        results = [
            f"{emp['EmployeeName']} [{emp['EmpId']}]" for emp in employees
        ]
        return JsonResponse(results, safe=False)

class SprAuthorizeActionView(View):
    """
    Handles the bulk authorization of selected SPRs.
    Replaces Auth_Click logic.
    """
    def post(self, request, *args, **kwargs):
        # We expect spr_ids as a comma-separated string from the form
        form = AuthorizeSprForm(request.POST)

        if form.is_valid():
            spr_ids_to_authorize = form.cleaned_data['spr_ids']
            
            # These would ideally come from the logged-in user's profile.
            authorized_by_user_id = self.request.session.get('username', 'SYSTEM_USER') # Example, replace with actual user ID
            current_company_id = self.request.session.get('compid', 1) # Example

            try:
                updated_count = SprMaster.authorize_sps_by_ids(
                    spr_ids_to_authorize,
                    authorized_by_user_id,
                    current_company_id
                )
                if updated_count > 0:
                    messages.success(request, f"{updated_count} SPR(s) authorized successfully.")
                else:
                    messages.info(request, "No new SPRs were authorized. They might already be authorized or criteria mismatch.")

                # Trigger HTMX refresh for the table
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': json.dumps({
                            'refreshSprAuthorizationList': {},
                            'hideModal': {} # Custom event to hide modal
                        })
                    }
                )
            except Exception as e:
                messages.error(request, f"An error occurred during authorization: {e}")
        else:
            # If form is not valid, re-render the modal content with errors
            messages.error(request, "Please select at least one SPR to authorize.")
            # If it's an HTMX request, we want to return the form with errors
            if request.headers.get('HX-Request'):
                # In a real scenario, you might want to return the modal partial
                # with form errors. For simplicity, we just send a 200 and error message.
                # A more robust solution would be to re-render the confirmation modal with form errors.
                return HttpResponse(
                    status=200, # Return 200 OK
                    headers={
                        'HX-Trigger': json.dumps({
                            'displayErrorMessage': form.errors.as_json(),
                        })
                    }
                )
        
        # Fallback for non-HTMX requests (though all interactions are HTMX based)
        return HttpResponse(status=400) # Bad request if form invalid and not HTMX

class SprAuthorizationConfirmAuthorizeView(View):
    """
    HTMX endpoint to render the confirmation modal for bulk authorization.
    """
    def get(self, request, *args, **kwargs):
        spr_ids_str = request.GET.get('spr_ids', '')
        if not spr_ids_str:
            return HttpResponse("No SPRs selected for authorization.", status=400)
        
        try:
            spr_ids = [int(s.strip()) for s in spr_ids_str.split(',') if s.strip()]
            selected_sps = SprMaster.objects.filter(Id__in=spr_ids, Authorize=0)
            
            context = {
                'selected_sps': selected_sps,
                'form': AuthorizeSprForm(initial={'spr_ids': spr_ids_str})
            }
            return self.render_to_response(context)
        except ValueError:
            return HttpResponse("Invalid SPR IDs provided.", status=400)
            
    def render_to_response(self, context):
        from django.template.loader import render_to_string
        html = render_to_string('spr_authorization/_spr_authorization_confirm_authorize.html', context, self.request)
        return HttpResponse(html)

```

##### 4.4 Templates

**Task:** Create templates for each view, ensuring HTMX and Alpine.js integration, and using DataTables.

**Instructions:**
-   `spr_authorization/spr_authorization_list.html`: Main page, extends `core/base.html`, holds search form, and a container for the HTMX-loaded table. Includes an Alpine.js modal.
-   `spr_authorization/_spr_authorization_table.html`: Partial for the DataTables table content, loaded via HTMX.
-   `spr_authorization/_spr_authorization_confirm_authorize.html`: Partial for the confirmation modal for bulk authorization.

```html
{# spr_authorization/templates/spr_authorization/spr_authorization_list.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">SPR Authorize</h2>
        <div class="mt-4 md:mt-0 flex flex-col md:flex-row items-center space-y-3 md:space-y-0 md:space-x-4 w-full md:w-auto">
            <div x-data="{ selectedIds: [] }" class="flex-grow flex items-center justify-end space-x-2">
                <input type="hidden" x-model="selectedIds" id="selectedSprIdsInput" />
                <button
                    id="authorizeButton"
                    class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out disabled:opacity-50 disabled:cursor-not-allowed"
                    :disabled="selectedIds.length === 0"
                    hx-get="{% url 'sprauth_confirm_authorize' %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    hx-vals="js:{spr_ids: selectedIds.join(',')}"
                    _="on click add .is-active to #modal"
                    title="Authorize selected SPRs">
                    Authorize Selected ({{ selectedIds.length }})
                </button>
            </div>
        </div>
    </div>

    {# Search/Filter Section #}
    <div x-data="{ searchType: '{{ search_type }}', empName: '{{ search_query }}', sprNo: '{{ search_query }}' }"
         class="bg-white p-6 rounded-lg shadow-md mb-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
            <div>
                <label for="id_search_type" class="block text-sm font-medium text-gray-700">Search By</label>
                <select id="id_search_type" x-model="searchType" @change="searchType === '1' ? empName = '' : sprNo = ''"
                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <option value="0">Employee Name</option>
                    <option value="1">SPR No</option>
                </select>
            </div>
            <div x-show="searchType === '0'">
                <label for="id_emp_name" class="block text-sm font-medium text-gray-700">Employee Name</label>
                <input type="text" id="id_emp_name" x-model="empName"
                       class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                       placeholder="Enter Employee Name"
                       name="search_query" {# This is the name for the HTMX request #}
                       hx-trigger="keyup changed delay:500ms, search" {# Autocomplete trigger #}
                       hx-get="{% url 'employee_autocomplete' %}" {# Autocomplete URL #}
                       hx-vals="js:{q: $el.value}" {# Pass current value as 'q' #}
                       autocomplete="off" {# Disable browser autocomplete #}
                       >
                {# Add a results container for autocomplete if needed, often managed by a JS library #}
            </div>
            <div x-show="searchType === '1'">
                <label for="id_spr_no" class="block text-sm font-medium text-gray-700">SPR No</label>
                <input type="text" id="id_spr_no" x-model="sprNo"
                       class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                       placeholder="Enter SPR Number"
                       name="search_query" {# This is the name for the HTMX request #}
                       >
            </div>
            <div class="col-span-1 md:col-span-1 flex justify-end items-end">
                <button
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out w-full md:w-auto"
                    hx-get="{% url 'sprauth_table_partial' %}"
                    hx-target="#sprAuthorizationTable-container"
                    hx-trigger="click, searchType, empName, sprNo" {# Trigger on click or input changes #}
                    hx-swap="innerHTML"
                    hx-vals="js:{search_type: searchType, search_query: searchType === '0' ? empName : sprNo}"
                    >
                    Search
                </button>
            </div>
        </div>
    </div>

    <div id="sprAuthorizationTable-container"
         hx-trigger="load, refreshSprAuthorizationList from:body"
         hx-get="{% url 'sprauth_table_partial' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-12">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading SPRs...</p>
        </div>
    </div>

    {# Modal for confirmation or forms #}
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on hx:afterOnLoad add .is-active to me
            on htmx:beforeRequest remove .is-active from me if event.detail.xhr.status == 204
            on htmx:responseError remove .is-active from me
            on hideModal from body remove .is-active from me"
         >
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4 sm:mx-0 transform transition-all scale-95 opacity-0"
             _="on hx:afterOnLoad transition ease-out duration-300 transform scale-100 opacity-100
                on hideModal from body transition ease-in duration-200 transform scale-95 opacity-0"
             >
            <!-- Content will be loaded here via HTMX -->
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('mainData', () => ({
            selectedIds: [],
            // Initialize selectedIds based on previous state if available or empty
            init() {
                // This function runs when the component is initialized.
                // It can be used to load initial data or set up listeners.
                console.log('Alpine.js initialized for SPR Authorization.');

                // Listen for a custom event to update selected IDs, e.g., after table refresh
                document.body.addEventListener('updateSelectedIds', (event) => {
                    this.selectedIds = event.detail.ids;
                });
            },
            toggleSelection(event) {
                const checkbox = event.target;
                const sprId = parseInt(checkbox.value);
                if (checkbox.checked) {
                    if (!this.selectedIds.includes(sprId)) {
                        this.selectedIds.push(sprId);
                    }
                } else {
                    this.selectedIds = this.selectedIds.filter(id => id !== sprId);
                }
                // Update the hidden input to be sent with HTMX requests
                document.getElementById('selectedSprIdsInput').value = this.selectedIds.join(',');
                console.log("Selected SPR IDs:", this.selectedIds);
            },
            toggleAllSelection(event) {
                const masterCheckbox = event.target;
                const checkboxes = document.querySelectorAll('#sprAuthorizationTable input[type="checkbox"][name="spr_select"]');
                this.selectedIds = [];
                checkboxes.forEach(checkbox => {
                    checkbox.checked = masterCheckbox.checked;
                    if (masterCheckbox.checked) {
                        this.selectedIds.push(parseInt(checkbox.value));
                    }
                });
                document.getElementById('selectedSprIdsInput').value = this.selectedIds.join(',');
                console.log("Selected SPR IDs:", this.selectedIds);
            }
        }));
    });

    // Custom HTMX event listener for messages
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'sprAuthorizationTable-container') {
            // Re-initialize DataTable after content swap
            $('#sprAuthorizationTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
            });
            // Re-dispatch selected IDs to Alpine component after table loads
            const selectedSprIdsInput = document.getElementById('selectedSprIdsInput');
            const currentSelectedIds = selectedSprIdsInput ? selectedSprIdsInput.value.split(',').map(Number).filter(Boolean) : [];
            document.body.dispatchEvent(new CustomEvent('updateSelectedIds', { detail: { ids: currentSelectedIds } }));

            // Ensure checkboxes reflect current selectedIds
            const checkboxes = document.querySelectorAll('#sprAuthorizationTable input[type="checkbox"][name="spr_select"]');
            checkboxes.forEach(checkbox => {
                const sprId = parseInt(checkbox.value);
                if (currentSelectedIds.includes(sprId)) {
                    checkbox.checked = true;
                }
            });
        }
    });

    // Handle display of error messages from HTMX requests
    document.body.addEventListener('displayErrorMessage', function(event) {
        const errors = JSON.parse(event.detail);
        let errorHtml = '<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">';
        errorHtml += '<strong class="font-bold">Error!</strong>';
        errorHtml += '<span class="block sm:inline"> ';
        for (const field in errors) {
            errors[field].forEach(error => {
                errorHtml += `${field}: ${error.message} `;
            });
        }
        errorHtml += '</span></div>';
        // Prepend error messages to modalContent or specific error div
        const modalContent = document.getElementById('modalContent');
        if (modalContent) {
            modalContent.insertAdjacentHTML('afterbegin', errorHtml);
        }
    });
</script>
{% endblock %}
```

```html
{# spr_authorization/templates/spr_authorization/_spr_authorization_table.html #}
{# This partial template is loaded via HTMX #}

<div x-data="mainData"> {# Re-initialize Alpine.js data on table reload #}
    {% if sprauth_list %}
    <table id="sprAuthorizationTable" class="min-w-full bg-white border border-gray-200 divide-y divide-gray-200 shadow-sm rounded-lg overflow-hidden">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 border-b-2 border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                    <input type="checkbox" @click="toggleAllSelection($event)"
                           class="form-checkbox h-4 w-4 text-blue-600 transition duration-150 ease-in-out rounded">
                </th>
                <th class="py-3 px-4 border-b-2 border-gray-200 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Fin Year</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">SPR No</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Date</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Time</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Gen. By</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Checked</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Approved</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Authorized</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% for spr in sprauth_list %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 whitespace-nowrap text-center">
                    {% if not spr.Authorize %}
                    <input type="checkbox" name="spr_select" value="{{ spr.Id }}"
                           @click="toggleSelection($event)"
                           class="form-checkbox h-4 w-4 text-blue-600 transition duration-150 ease-in-out rounded">
                    {% endif %}
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-right text-sm text-gray-800">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-center text-sm text-gray-800">{{ spr.financial_year_display }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-center text-sm text-gray-800">{{ spr.SPRNo }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-center text-sm text-gray-800">{{ spr.SysDate|date:"d M Y"|default:"N/A" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-center text-sm text-gray-800">{{ spr.SysTime|time:"H:i"|default:"N/A" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-left text-sm text-gray-800">{{ spr.generator_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-center text-sm text-gray-800">{{ spr.CheckedDate|date:"d M Y"|default:"N/A" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-center text-sm text-gray-800">{{ spr.ApproveDate|date:"d M Y"|default:"N/A" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-center text-sm text-gray-800">{{ spr.AuthorizeDate|date:"d M Y"|default:"N/A" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-center text-sm font-medium">
                    <a href="{% url 'spr_detail_view' spr.Id %}" target="_blank"
                       class="text-blue-600 hover:text-blue-900 transition duration-150 ease-in-out">View</a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <div class="text-center py-8">
        <p class="text-lg text-gray-600">No data to display !</p>
    </div>
    {% endif %}
</div>

<script>
    // DataTables initialization (ensure jQuery and DataTables are loaded in base.html)
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#sprAuthorizationTable')) {
            $('#sprAuthorizationTable').DataTable().destroy(); // Destroy existing instance
        }
        $('#sprAuthorizationTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "searching": true, // Enable search box
            "paging": true,    // Enable pagination
            "info": true,      // Enable info (Showing 1 to X of Y entries)
            "ordering": true   // Enable sorting
            // Note: Server-side processing would be preferred for very large datasets
            // "processing": true,
            // "serverSide": true,
            // "ajax": "{% url 'your_server_side_ajax_endpoint' %}"
        });
    });
</script>
```

```html
{# spr_authorization/templates/spr_authorization/_spr_authorization_confirm_authorize.html #}
{# This partial template is loaded into the modal via HTMX #}

<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">Confirm SPR Authorization</h3>
    <p class="text-gray-700 mb-4">Are you sure you want to authorize the following SPR(s)?</p>
    
    <div class="max-h-60 overflow-y-auto border border-gray-200 rounded-md bg-gray-50 p-4 mb-6">
        {% if selected_sps %}
        <ul class="list-disc pl-5 space-y-1 text-gray-700">
            {% for spr in selected_sps %}
            <li><strong>SPR No:</strong> {{ spr.SPRNo }} (ID: {{ spr.Id }}) - Generated by {{ spr.generator_name }}</li>
            {% endfor %}
        </ul>
        {% else %}
        <p class="text-gray-600 italic">No SPRs selected for authorization or selected SPRs are already authorized.</p>
        {% endif %}
    </div>

    <form hx-post="{% url 'sprauth_authorize_action' %}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        {# Use the form to pass selected IDs. The hidden input was passed from Alpine #}
        {# And validated by the form #}
        {{ form.spr_ids }} 
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                {% if not selected_sps %}disabled{% endif %}
            >
                Confirm Authorize
            </button>
        </div>
    </form>
</div>
```

##### 4.5 URLs

**Task:** Define URL patterns for the views, including HTMX-specific endpoints.

**Instructions:**
Create URL patterns in `spr_authorization/urls.py`. Remember to include this app's URLs in your project's main `urls.py`.

```python
# spr_authorization/urls.py
from django.urls import path
from .views import (
    SprAuthorizationListView,
    SprAuthorizationTablePartialView,
    EmployeeAutocompleteView,
    SprAuthorizeActionView,
    SprAuthorizationConfirmAuthorizeView,
)

urlpatterns = [
    # Main page for SPR authorization list
    path('spr-authorization/', SprAuthorizationListView.as_view(), name='sprauth_list'),
    
    # HTMX endpoint for the searchable/paginatable table content
    path('spr-authorization/table/', SprAuthorizationTablePartialView.as_view(), name='sprauth_table_partial'),
    
    # HTMX endpoint for employee name autocomplete suggestions
    path('employee-autocomplete/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),
    
    # HTMX endpoint for handling the bulk authorization action
    path('spr-authorization/authorize/', SprAuthorizeActionView.as_view(), name='sprauth_authorize_action'),

    # HTMX endpoint to load the confirmation modal for authorization
    path('spr-authorization/confirm-authorize/', SprAuthorizationConfirmAuthorizeView.as_view(), name='sprauth_confirm_authorize'),

    # Placeholder for the SPR View/Print page (assuming it's a separate module)
    path('spr-view/<int:pk>/', View.as_view(template_name='spr_authorization/spr_detail_placeholder.html'), name='spr_detail_view'),
]
```

##### 4.6 Tests

**Task:** Write comprehensive tests for the model and views, ensuring good test coverage.

**Instructions:**
Include unit tests for model methods and integration tests for all views, simulating HTMX requests where applicable.

```python
# spr_authorization/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
import json
from unittest.mock import patch # For mocking session data if needed

from .models import SprMaster, HrOfficeStaff, FinancialYear
from .forms import AuthorizeSprForm

# Mocking session data for tests
def mock_get_session(session_key, default_value):
    if session_key == 'compid':
        return 1
    elif session_key == 'finyear':
        return 2023
    elif session_key == 'username':
        return 'test_user_id' # Example authorizedBy ID
    return default_value

class SprMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.company = 1
        cls.fin_year = 2023

        # Create supporting data
        HrOfficeStaff.objects.create(EmpId=101, EmployeeName='John Doe', Title='Mr.', CompId=cls.company)
        HrOfficeStaff.objects.create(EmpId=102, EmployeeName='Jane Smith', Title='Ms.', CompId=cls.company)
        FinancialYear.objects.create(FinYearId=cls.fin_year, FinYear='2023-2024', CompId=cls.company)
        FinancialYear.objects.create(FinYearId=cls.fin_year - 1, FinYear='2022-2023', CompId=cls.company)

        # Create SPR Master records
        cls.spr1 = SprMaster.objects.create(
            Id=1, SPRNo='SPR001', SysDate=timezone.now().date(), SysTime=timezone.now().time(),
            SessionId=101, Checked=1, CheckedDate=timezone.now().date(), Approve=1, ApproveDate=timezone.now().date(),
            Authorize=0, AuthorizeDate=None, AuthorizedBy=None, AuthorizeTime=None, FinYearId=cls.fin_year, CompId=cls.company
        )
        cls.spr2 = SprMaster.objects.create(
            Id=2, SPRNo='SPR002', SysDate=timezone.now().date(), SysTime=timezone.now().time(),
            SessionId=102, Checked=1, CheckedDate=timezone.now().date(), Approve=1, ApproveDate=timezone.now().date(),
            Authorize=0, AuthorizeDate=None, AuthorizedBy=None, AuthorizeTime=None, FinYearId=cls.fin_year, CompId=cls.company
        )
        cls.spr_authorized = SprMaster.objects.create(
            Id=3, SPRNo='SPR003', SysDate=timezone.now().date(), SysTime=timezone.now().time(),
            SessionId=101, Checked=1, CheckedDate=timezone.now().date(), Approve=1, ApproveDate=timezone.now().date(),
            Authorize=1, AuthorizeDate=timezone.now().date(), AuthorizedBy='old_auth', AuthorizeTime=timezone.now().time(), FinYearId=cls.fin_year, CompId=cls.company
        )
        cls.spr_not_approved = SprMaster.objects.create(
            Id=4, SPRNo='SPR004', SysDate=timezone.now().date(), SysTime=timezone.now().time(),
            SessionId=101, Checked=1, CheckedDate=timezone.now().date(), Approve=0, ApproveDate=None,
            Authorize=0, FinYearId=cls.fin_year, CompId=cls.company
        )
        cls.spr_old_finyear = SprMaster.objects.create(
            Id=5, SPRNo='SPR005', SysDate=timezone.now().date(), SysTime=timezone.now().time(),
            SessionId=101, Checked=1, CheckedDate=timezone.now().date(), Approve=1, ApproveDate=timezone.now().date(),
            Authorize=0, FinYearId=cls.fin_year - 1, CompId=cls.company
        )


    def test_spr_creation(self):
        spr = SprMaster.objects.get(Id=1)
        self.assertEqual(spr.SPRNo, 'SPR001')
        self.assertEqual(spr.SessionId, 101)
        self.assertEqual(spr.Authorize, 0)

    def test_generator_name_property(self):
        spr = SprMaster.objects.get(Id=1)
        self.assertEqual(spr.generator_name, 'Mr. John Doe')
        
        # Test case where generator doesn't exist
        spr_no_gen = SprMaster.objects.create(Id=6, SPRNo='SPR006', SessionId=999, Approve=1, Authorize=0, CompId=self.company, FinYearId=self.fin_year)
        self.assertEqual(spr_no_gen.generator_name, 'N/A')

    def test_financial_year_display_property(self):
        spr = SprMaster.objects.get(Id=1)
        self.assertEqual(spr.financial_year_display, '2023-2024')
        
        # Test case where financial year doesn't exist
        spr_no_finyear = SprMaster.objects.create(Id=7, SPRNo='SPR007', SessionId=101, Approve=1, Authorize=0, CompId=self.company, FinYearId=999)
        self.assertEqual(spr_no_finyear.financial_year_display, 'N/A')

    @patch('django.contrib.sessions.backends.db.SessionStore.get', side_effect=mock_get_session)
    def test_get_pending_for_authorization_no_search(self, mock_session_get):
        sps = SprMaster.get_pending_for_authorization(self.company, self.fin_year)
        self.assertEqual(sps.count(), 3) # SPR001, SPR002, SPR005 (FinYearId <= current_financial_year_id)
        self.assertIn(self.spr1, sps)
        self.assertIn(self.spr2, sps)
        self.assertIn(self.spr_old_finyear, sps)
        self.assertNotIn(self.spr_authorized, sps)
        self.assertNotIn(self.spr_not_approved, sps)

    @patch('django.contrib.sessions.backends.db.SessionStore.get', side_effect=mock_get_session)
    def test_get_pending_for_authorization_by_spr_no(self, mock_session_get):
        sps = SprMaster.get_pending_for_authorization(self.company, self.fin_year, search_type='1', search_query='SPR001')
        self.assertEqual(sps.count(), 1)
        self.assertEqual(sps.first().SPRNo, 'SPR001')

    @patch('django.contrib.sessions.backends.db.SessionStore.get', side_effect=mock_get_session)
    def test_get_pending_for_authorization_by_employee_name(self, mock_session_get):
        sps = SprMaster.get_pending_for_authorization(self.company, self.fin_year, search_type='0', search_query='John Doe')
        self.assertEqual(sps.count(), 2) # SPR001, SPR005
        self.assertIn(self.spr1, sps)
        self.assertIn(self.spr_old_finyear, sps)
        self.assertNotIn(self.spr2, sps)

    @patch('django.contrib.sessions.backends.db.SessionStore.get', side_effect=mock_get_session)
    def test_get_pending_for_authorization_no_match(self, mock_session_get):
        sps = SprMaster.get_pending_for_authorization(self.company, self.fin_year, search_type='1', search_query='NONEXISTENT')
        self.assertEqual(sps.count(), 0)

    @patch('django.contrib.sessions.backends.db.SessionStore.get', side_effect=mock_get_session)
    def test_authorize_sps_by_ids(self, mock_session_get):
        # Authorize spr1 and spr2
        spr_ids = [self.spr1.Id, self.spr2.Id]
        authorized_by = 'test_user_id'
        
        updated_count = SprMaster.authorize_sps_by_ids(spr_ids, authorized_by, self.company)
        self.assertEqual(updated_count, 2)

        self.spr1.refresh_from_db()
        self.spr2.refresh_from_db()
        self.assertEqual(self.spr1.Authorize, 1)
        self.assertEqual(self.spr1.AuthorizedBy, authorized_by)
        self.assertEqual(self.spr1.AuthorizeDate, timezone.now().date())
        self.assertEqual(self.spr2.Authorize, 1)
        self.assertEqual(self.spr2.AuthorizedBy, authorized_by)
        self.assertEqual(self.spr2.AuthorizeDate, timezone.now().date())

        # Try to authorize already authorized SPR
        updated_count_again = SprMaster.authorize_sps_by_ids([self.spr_authorized.Id], authorized_by, self.company)
        self.assertEqual(updated_count_again, 0) # Should not update as it's already authorized


class SprAuthorizationViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.company = 1
        cls.fin_year = 2023

        HrOfficeStaff.objects.create(EmpId=101, EmployeeName='John Doe', Title='Mr.', CompId=cls.company)
        HrOfficeStaff.objects.create(EmpId=102, EmployeeName='Jane Smith', Title='Ms.', CompId=cls.company)
        FinancialYear.objects.create(FinYearId=cls.fin_year, FinYear='2023-2024', CompId=cls.company)
        FinancialYear.objects.create(FinYearId=cls.fin_year - 1, FinYear='2022-2023', CompId=cls.company)

        cls.spr1 = SprMaster.objects.create(
            Id=1, SPRNo='SPR001', SysDate=timezone.now().date(), SysTime=timezone.now().time(),
            SessionId=101, Checked=1, CheckedDate=timezone.now().date(), Approve=1, ApproveDate=timezone.now().date(),
            Authorize=0, FinYearId=cls.fin_year, CompId=cls.company
        )
        cls.spr2 = SprMaster.objects.create(
            Id=2, SPRNo='SPR002', SysDate=timezone.now().date(), SysTime=timezone.now().time(),
            SessionId=102, Checked=1, CheckedDate=timezone.now().date(), Approve=1, ApproveDate=timezone.now().date(),
            Authorize=0, FinYearId=cls.fin_year, CompId=cls.company
        )
        cls.spr_old_finyear = SprMaster.objects.create(
            Id=5, SPRNo='SPR005', SysDate=timezone.now().date(), SysTime=timezone.now().time(),
            SessionId=101, Checked=1, CheckedDate=timezone.now().date(), Approve=1, ApproveDate=timezone.now().date(),
            Authorize=0, FinYearId=cls.fin_year - 1, CompId=cls.company
        )
    
    def setUp(self):
        self.client = Client()
        # Mock session data for all requests if needed
        self.session = self.client.session
        self.session['compid'] = 1
        self.session['finyear'] = 2023
        self.session['username'] = 'test_user_id'
        self.session.save()

    def test_list_view(self):
        response = self.client.get(reverse('sprauth_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'spr_authorization/spr_authorization_list.html')
        # Initial list view should not contain objects directly, only the container
        self.assertContains(response, '<div id="sprAuthorizationTable-container"')

    def test_table_partial_view_no_search(self):
        response = self.client.get(reverse('sprauth_table_partial'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'spr_authorization/_spr_authorization_table.html')
        self.assertContains(response, 'SPR001')
        self.assertContains(response, 'SPR002')
        self.assertContains(response, 'SPR005')
        self.assertContains(response, 'John Doe')
        self.assertContains(response, 'Jane Smith')
        self.assertEqual(response.context['sprauth_list'].count(), 3)

    def test_table_partial_view_search_by_spr_no(self):
        response = self.client.get(reverse('sprauth_table_partial'), {'search_type': '1', 'search_query': 'SPR001'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'SPR001')
        self.assertNotContains(response, 'SPR002')
        self.assertEqual(response.context['sprauth_list'].count(), 1)

    def test_table_partial_view_search_by_employee_name(self):
        response = self.client.get(reverse('sprauth_table_partial'), {'search_type': '0', 'search_query': 'John Doe'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'SPR001')
        self.assertContains(response, 'SPR005') # John Doe generated this too
        self.assertNotContains(response, 'SPR002')
        self.assertEqual(response.context['sprauth_list'].count(), 2)

    def test_employee_autocomplete_view(self):
        response = self.client.get(reverse('employee_autocomplete'), {'q': 'john'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = json.loads(response.content)
        self.assertIn('John Doe [101]', data)
        self.assertNotIn('Jane Smith [102]', data)

    def test_employee_autocomplete_view_min_prefix_length(self):
        response = self.client.get(reverse('employee_autocomplete'), {'q': ''}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertEqual(len(data), 0)

    def test_confirm_authorize_view_get(self):
        response = self.client.get(reverse('sprauth_confirm_authorize'), {'spr_ids': f'{self.spr1.Id},{self.spr2.Id}'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'spr_authorization/_spr_authorization_confirm_authorize.html')
        self.assertContains(response, 'SPR001')
        self.assertContains(response, 'SPR002')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].initial['spr_ids'], f'{self.spr1.Id},{self.spr2.Id}')
    
    def test_confirm_authorize_view_get_no_ids(self):
        response = self.client.get(reverse('sprauth_confirm_authorize'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.content.decode(), 'No SPRs selected for authorization.')

    def test_authorize_action_view_post_success(self):
        initial_spr1_auth = SprMaster.objects.get(Id=self.spr1.Id).Authorize
        self.assertEqual(initial_spr1_auth, 0)

        data = {'spr_ids': f'{self.spr1.Id},{self.spr2.Id}'}
        response = self.client.post(reverse('sprauth_authorize_action'), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HTMX No Content response
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshSprAuthorizationList', response.headers['HX-Trigger'])
        self.assertIn('hideModal', response.headers['HX-Trigger'])

        self.spr1.refresh_from_db()
        self.spr2.refresh_from_db()
        self.assertEqual(self.spr1.Authorize, 1)
        self.assertEqual(self.spr2.Authorize, 1)
        self.assertEqual(self.spr1.AuthorizedBy, 'test_user_id') # From mocked session

    def test_authorize_action_view_post_no_ids(self):
        data = {'spr_ids': ''}
        response = self.client.post(reverse('sprauth_authorize_action'), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200) # Form validation error returns 200 with message
        self.assertIn('displayErrorMessage', response.headers['HX-Trigger'])
        self.assertContains(response, 'No SPRs selected for authorization.', html=False) # Check for message content
        
        # Verify SPRs are not authorized
        self.spr1.refresh_from_db()
        self.assertEqual(self.spr1.Authorize, 0)

    def test_authorize_action_view_post_invalid_ids(self):
        data = {'spr_ids': '1,abc'}
        response = self.client.post(reverse('sprauth_authorize_action'), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200) # Form validation error returns 200 with message
        self.assertIn('displayErrorMessage', response.headers['HX-Trigger'])
        self.assertContains(response, 'Invalid SPR IDs provided.', html=False)
        
        # Verify SPRs are not authorized
        self.spr1.refresh_from_db()
        self.assertEqual(self.spr1.Authorize, 0)

    def test_authorize_action_view_post_already_authorized(self):
        # Authorize SPR1 manually first
        SprMaster.authorize_sps_by_ids([self.spr1.Id], 'pre_auth_user', self.company)
        self.spr1.refresh_from_db()
        self.assertEqual(self.spr1.Authorize, 1)

        data = {'spr_ids': f'{self.spr1.Id},{self.spr2.Id}'} # Try to authorize SPR1 again
        response = self.client.post(reverse('sprauth_authorize_action'), data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshSprAuthorizationList', response.headers['HX-Trigger'])
        self.assertIn('hideModal', response.headers['HX-Trigger'])

        self.spr1.refresh_from_db()
        self.spr2.refresh_from_db()
        self.assertEqual(self.spr1.Authorize, 1) # Still 1
        self.assertEqual(self.spr2.Authorize, 1) # Now 1
        # Check that the messages framework reflects that only one was authorized
        messages = list(response.wsgi_request._messages)
        self.assertEqual(len(messages), 1)
        self.assertIn("1 SPR(s) authorized successfully.", str(messages[0]))
```

---

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated templates and views fully embrace HTMX and Alpine.js:

*   **HTMX:**
    *   The main list view (`sprauth_list`) initially loads an empty container for the table.
    *   The table content (`_sprauth_table.html`) is fetched via `hx-get` on `load` and `refreshSprAuthorizationList` events.
    *   Search filtering is performed by `hx-get` on button clicks or input changes, updating `sprauth_table_partial`.
    *   The "Authorize Selected" button uses `hx-get` to fetch the confirmation modal (`_spr_authorization_confirm_authorize.html`) into `#modalContent`.
    *   The confirmation form inside the modal uses `hx-post` to submit to `sprauth_authorize_action`.
    *   Successful authorization triggers an `HX-Trigger` to `refreshSprAuthorizationList` (to update the table) and `hideModal` (to close the modal), swapping `none` on the form submission itself.
    *   Autocomplete for employee names uses `hx-get` to `employee_autocomplete` and `hx-trigger="keyup changed delay:500ms, search"`.
*   **Alpine.js:**
    *   Manages the `selectedIds` array for checkboxes, allowing for dynamic updates to the "Authorize Selected" button text and `disabled` state.
    *   Toggles the visibility of the "Employee Name" and "SPR No" search fields based on the `searchType` dropdown.
    *   Controls the visibility and transitions of the modal using `x-data` and `_` (hyperscript).
    *   Listens for `updateSelectedIds` event to keep `selectedIds` synchronized when the table content is reloaded by HTMX.
*   **DataTables:**
    *   Initialized on the `_spr_authorization_table.html` partial after it's loaded by HTMX (`htmx:afterSwap` event). This ensures DataTables correctly applies its features to the dynamically loaded table.

This architecture ensures a highly interactive user experience without full page reloads, making the application feel modern and responsive.

---

### Final Notes

*   **Placeholders:** `current_company_id`, `current_financial_year_id`, and `authorized_by_user_id` are placeholders. In a real application, these would be retrieved from the Django `request.user` object (e.g., `request.user.profile.company.id`, `request.user.id`) after proper user authentication and profiling are implemented.
*   **Error Handling:** Basic error handling is present in views (e.g., `try-except` blocks, `messages` framework). For production, robust error logging and user feedback mechanisms are essential.
*   **`spr_detail_view`:** A placeholder URL and empty view (`View.as_view(template_name='spr_authorization/spr_detail_placeholder.html')`) are included for the "View" link, indicating it would be a separate, more detailed view to be developed.
*   **Styling:** Tailwind CSS classes are used extensively in the templates for a modern, responsive design.
*   **Scalability:** Moving business logic to models ensures that the application remains scalable and maintainable. DataTables handles client-side performance, but for very large datasets, server-side processing with DataTables would be the next step.
*   **Security:** Always ensure proper input validation, sanitization, and Django's built-in CSRF protection (`{% csrf_token %}`) are in place. Authentication and authorization would be managed by Django's robust user system.