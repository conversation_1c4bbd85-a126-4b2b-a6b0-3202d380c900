## ASP.NET to Django Conversion Script:

This modernization plan outlines the strategy to transition your existing ASP.NET application, specifically the `PO_PR_ItemGrid.aspx` module, to a modern Django 5.0+ solution. We will leverage AI-assisted automation principles, focusing on `Fat Models`, `Thin Views`, `HTMX`, `Alpine.js`, and `DataTables` to deliver a highly performant, maintainable, and user-friendly system.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists (`core/base.html`).
- Focus ONLY on component-specific code for the current module (`material_management` app).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html`.
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
The ASP.NET code-behind's `LoadData()` method uses multiple SQL queries involving several tables. We infer the following database tables and their likely column names. The `fun.select` method is a custom helper, but its arguments reveal the table and column names.

**Identified Tables and Key Columns:**

*   **`tblMM_PR_Master`**:
    *   `PRNo` (string)
    *   `FinYearId` (int) - FK to `tblFinancial_master`
    *   `SysDate` (date/datetime)
    *   `SessionId` (string/int) - FK to `tblHR_OfficeStaff` EmpId
    *   `WONo` (string)
    *   `CompId` (int)
    *   `Id` (PK)

*   **`tblMM_PR_Details`**:
    *   `Id` (PK, int)
    *   `ItemId` (int) - FK to `tblDG_Item_Master`
    *   `DelDate` (date/datetime)
    *   `AHId` (int) - FK to `AccHead`
    *   `Qty` (double/decimal) - PR Quantity
    *   `PRNo` (string) - FK to `tblMM_PR_Master`
    *   `MId` (int) - FK to `tblMM_PR_Master.Id`
    *   `SupplierId` (string)

*   **`tblDG_Item_Master`**:
    *   `Id` (PK, int)
    *   `ItemCode` (string)
    *   `ManfDesc` (string) - Used as `PurchDesc`
    *   `UOMBasic` (int) - FK to `Unit_Master`
    *   `CompId` (int)

*   **`Unit_Master`**:
    *   `Id` (PK, int)
    *   `Symbol` (string) - Used as `UOMPurch`

*   **`AccHead`**:
    *   `Id` (PK, int)
    *   `Symbol` (string)
    *   `Description` (string) - Combined with Symbol for `AcHead`

*   **`tblHR_OfficeStaff`**:
    *   `EmpId` (PK, string/int)
    *   `Title` (string)
    *   `EmployeeName` (string) - Combined with Title for `GenBy`
    *   `CompId` (int)

*   **`tblFinancial_master`**:
    *   `FinYearId` (PK, string)
    *   `FinYear` (string)
    *   `CompId` (int)

*   **`tblMM_PO_Master`**:
    *   `Id` (PK, int)
    *   `CompId` (int)

*   **`tblMM_PO_Details`**:
    *   `Qty` (double/decimal) - PO Quantity
    *   `PRId` (int) - FK to `tblMM_PR_Details.Id`
    *   `MId` (int) - FK to `tblMM_PO_Master.Id`

*   **`tblMM_PR_PO_Temp`**:
    *   `Id` (PK, int)
    *   `PRNo` (string)
    *   `PRId` (int)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
The ASP.NET page primarily functions as a **Read** operation (displaying a list of items). It includes filtering based on `SupplierId`, `Financial Year`, and `Company ID`.
There is also a "Select" action, which is essentially a **Read/Action** on a specific item, leading to a redirect to another page (`PO_PR_ItemSelect.aspx`) with details of the selected item.

*   **Create:** Not present on this specific page.
*   **Read:**
    *   Loads data into `GridView2` based on query parameters (`SupCode`, `FyId`, `CompId`).
    *   Performs complex joins and calculations (`RemainQty`, `POQty`, formatted `Date`, `AcHead`, `GenBy`, `ItemCode`, `Description`, `UOMPurch`, `FinYear`).
    *   Filters out items that have `RemainQty <= 0` or exist in `tblMM_PR_PO_Temp`.
    *   Supports pagination (`AllowPaging`).
*   **Update:** Not present on this specific page.
*   **Delete:** Not present on this specific page.
*   **Action (Select):** When a "Select" link is clicked, it identifies `PRNo`, `Id`, `WONo` of the row and redirects to another page with these parameters. This will be handled as an HTMX `hx-post` to a dedicated Django view.

**Validation Logic:** Implicit filtering based on `RemQty > 0` and `tblMM_PR_PO_Temp` existence.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The ASP.NET page uses a `GridView` (`GridView2`) as the primary UI component for data display.

*   **`asp:GridView` (`GridView2`):**
    *   Displays a tabular list of "Purchase Request Items".
    *   Columns: SN (row index), Fin Year, Select (LinkButton), WO No, PR No, Date, Item Code, Description, UOM, PR Qty, PO Qty, Remain Qty, A/c Head, Del. Date, Gen. By, Id (hidden).
    *   Paging (`AllowPaging="True"`).
    *   `onpageindexchanging`: Handles pagination.
    *   `onrowcommand`: Handles the "Select" action.
    *   `EmptyDataTemplate`: Displays "No data to display !".
    *   CSS Class: `yui-datatable-theme`.

*   **`asp:LinkButton` (`lnkButton` with `CommandName="sel"`):**
    *   Used in the "Select" column to trigger a row-specific action.

*   **`asp:Label` controls:**
    *   Used extensively within `ItemTemplate` to display data for each column.

**Frontend Mapping:**
*   `GridView` will be replaced with a `<table>` enhanced by `DataTables.js` for client-side functionality (searching, sorting, pagination).
*   The `LinkButton` will be an HTML `<button>` or `<a>` tag with HTMX attributes to trigger actions without full page reloads.
*   CSS: `yui-datatable.css` will be replaced by DataTables default styling, integrated with Tailwind CSS. `StyleSheet.css` suggests general application styling. `loadingNotifier.js` indicates client-side loading feedback, which Alpine.js and HTMX can handle.

### Step 4: Generate Django Code

We will create a Django application named `material_management`.

#### 4.1 Models (`material_management/models.py`)

We will define models for all identified underlying database tables with `managed = False`. Then, we will implement a custom manager method on `PRDetail` that encapsulates the complex data retrieval and processing logic from the original `LoadData` method. This manager method will return a list of custom data objects that represent the grid's rows.

```python
from django.db import models
from django.db.models import Sum, F, OuterRef, Subquery, Value
from django.db.models.functions import Coalesce
from django.utils.dateformat import DateFormat
from datetime import datetime

# Helper function to replicate fun.GetItemCode_PartNo
# In a real scenario, this would ideally be a method on ItemMaster or a dedicated service.
# For simplicity, assuming a direct lookup.
def get_item_code_part_no(comp_id, item_id):
    try:
        item = ItemMaster.objects.using('legacy_db').get(id=item_id, comp_id=comp_id)
        # Assuming ItemCode is the part number or what GetItemCode_PartNo returns.
        # Original fun.GetItemCode_PartNo might have more complex logic.
        return item.item_code
    except ItemMaster.DoesNotExist:
        return "N/A"

class AccHead(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'AccHead'
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'

    def __str__(self):
        return f"[{self.symbol}] {self.description}" if self.symbol else self.description

class FinancialYear(models.Model):
    fin_year_id = models.CharField(db_column='FinYearId', primary_key=True, max_length=10)
    fin_year = models.CharField(db_column='FinYear', max_length=10, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year or self.fin_year_id

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50, blank=True, null=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True)
    uom_basic = models.IntegerField(db_column='UOMBasic', blank=True, null=True) # FK to UnitMaster
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.item_code or self.manf_desc

class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit Master'
        verbose_name_plural = 'Unit Masters'

    def __str__(self):
        return self.symbol or str(self.id)

class OfficeStaff(models.Model):
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50) # Assuming EmpId can be string
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title}. {self.employee_name}" if self.title else self.employee_name

class POMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'
        verbose_name = 'PO Master'
        verbose_name_plural = 'PO Masters'

    def __str__(self):
        return f"PO {self.id}"

class PODetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming an ID for PODetail
    m_id = models.ForeignKey(POMaster, models.DO_NOTHING, db_column='MId', related_name='po_details')
    pr_id = models.IntegerField(db_column='PRId', blank=True, null=True) # FK to PRDetail.Id
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=5, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Details'
        verbose_name = 'PO Detail'
        verbose_name_plural = 'PO Details'

    def __str__(self):
        return f"PO Detail {self.id} (PR: {self.pr_id})"

class PRMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    pr_no = models.CharField(db_column='PRNo', max_length=50, blank=True, null=True)
    fin_year_id = models.CharField(db_column='FinYearId', max_length=10, blank=True, null=True) # FK to FinancialYear
    sys_date = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True) # FK to OfficeStaff EmpId
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Master'
        verbose_name = 'PR Master'
        verbose_name_plural = 'PR Masters'

    def __str__(self):
        return self.pr_no or str(self.id)

class PRPOTemp(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    pr_no = models.CharField(db_column='PRNo', max_length=50, blank=True, null=True)
    pr_id = models.IntegerField(db_column='PRId', blank=True, null=True) # FK to PRDetail.Id

    class Meta:
        managed = False
        db_table = 'tblMM_PR_PO_Temp'
        verbose_name = 'PR PO Temp'
        verbose_name_plural = 'PR PO Temp'

    def __str__(self):
        return f"Temp PR: {self.pr_no}, PRId: {self.pr_id}"

class PRDetailManager(models.Manager):
    def get_pr_items_for_grid(self, sup_code, fin_year_id, comp_id):
        # This method replicates the complex LoadData logic from the ASP.NET code-behind.
        # It queries multiple tables and performs calculations and filtering.

        # Subquery to calculate total POQty for each PRId
        # The original code's Sum(tblMM_PO_Details.Qty)As TotPoQty logic
        po_qty_subquery = PODetail.objects.filter(
            pr_id=OuterRef('id'),
            m_id__comp_id=comp_id # Joining with POMaster's CompId
        ).order_by().values('pr_id').annotate(
            total_po_qty=Coalesce(Sum('qty'), Value(0.0))
        ).values('total_po_qty')

        # Filter PR details based on supplier, financial year, and company ID
        # And ensure it's not in tblMM_PR_PO_Temp AND Remaining Qty > 0
        pr_details_qs = self.get_queryset().filter(
            supplier_id=sup_code,
            pr_master__fin_year_id__lte=fin_year_id, # Assumes fin_year_id is comparable
            pr_master__comp_id=comp_id
        ).select_related(
            'pr_master', 'item_master', 'item_master__uom_basic_obj', 'acc_head'
        ).annotate(
            po_qty=Subquery(po_qty_subquery, output_field=models.DecimalField()),
            # Coalesce handles cases where po_qty_subquery returns NULL (no matching POs)
            calculated_po_qty=Coalesce(F('po_qty'), Value(0.0)),
            remain_qty=F('qty') - Coalesce(F('po_qty'), Value(0.0)),
            # Annotate fields for display
            display_pr_no=F('pr_master__pr_no'),
            display_fin_year=F('pr_master__fin_year__fin_year'),
            display_wo_no=F('pr_master__wo_no'),
            display_date=F('pr_master__sys_date'),
            display_item_code=F('item_master__item_code'), # Simpler approach for GetItemCode_PartNo
            display_purch_desc=F('item_master__manf_desc'),
            display_uom_purch=F('item_master__uom_basic_obj__symbol'),
            display_ac_head=models.Case(
                models.When(acc_head__symbol__isnull=False, then=models.Func(
                    Value('['), F('acc_head__symbol'), Value(']'), F('acc_head__description'),
                    function='CONCAT', output_field=models.CharField()
                )),
                default=F('acc_head__description'),
                output_field=models.CharField()
            ),
            display_gen_by=models.Func(
                F('pr_master__session_id__title'), Value('. '), F('pr_master__session_id__employee_name'),
                function='CONCAT', output_field=models.CharField()
            )
        ).filter(
            remain_qty__gt=0
        )

        # Apply the tblMM_PR_PO_Temp filter (DSCheck.HasRows == false)
        # This means PRDetails entries should NOT exist in PRPOTemp for the given PRNo and PRId
        pr_temp_subquery_exists = PRPOTemp.objects.filter(
            pr_no=OuterRef('pr_master__pr_no'),
            pr_id=OuterRef('id')
        )
        pr_details_qs = pr_details_qs.annotate(
            pr_temp_exists=Subquery(pr_temp_subquery_exists.values('id')[:1])
        ).filter(pr_temp_exists__isnull=True)

        # The ASP.NET code manually formats dates. We'll do it in Python after fetching.
        # Or you could use database functions like `strftime` for specific databases.
        # For this example, we'll format during iteration.

        # Structure the output similar to the DataRow in ASP.NET
        results = []
        for pr_item in pr_details_qs:
            # Replicate the data row construction and formatting
            formatted_date = DateFormat(pr_item.display_date).format('d/m/Y') if pr_item.display_date else ''
            formatted_deli_date = DateFormat(pr_item.del_date).format('d/m/Y') if pr_item.del_date else ''
            
            # The 'GenBy' field in ASP.NET also checked CompId for tblHR_OfficeStaff
            # Our select_related on session_id (which is OfficeStaff) already includes comp_id check implicit in the join if it's there
            # Replicate fun.GetItemCode_PartNo logic (simplified here)
            # item_code_part_no = get_item_code_part_no(comp_id, pr_item.item_master_id)
            # Using the annotated display_item_code directly

            results.append({
                'id': pr_item.id, # PRDetail ID
                'pr_no': pr_item.display_pr_no,
                'fin_year': pr_item.display_fin_year,
                'wo_no': pr_item.display_wo_no,
                'date': formatted_date,
                'item_code': pr_item.display_item_code,
                'purch_desc': pr_item.display_purch_desc,
                'uom_purch': pr_item.display_uom_purch,
                'qty': pr_item.qty, # PR Qty
                'po_qty': round(float(pr_item.calculated_po_qty), 5), # PO Qty
                'remain_qty': round(float(pr_item.remain_qty), 5), # Remaining Qty
                'ac_head': pr_item.display_ac_head,
                'deli_date': formatted_deli_date,
                'gen_by': pr_item.display_gen_by,
            })
        return results

class PRDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    pr_no = models.CharField(db_column='PRNo', max_length=50, blank=True, null=True) # Directly on detail for join consistency
    m_id = models.ForeignKey(PRMaster, models.DO_NOTHING, db_column='MId', related_name='pr_details')
    item_id = models.IntegerField(db_column='ItemId', blank=True, null=True) # Can be FK, but ASP.NET joins by ID directly
    item_master = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId', related_name='pr_details_items', null=True) # Direct FK
    del_date = models.DateTimeField(db_column='DelDate', blank=True, null=True)
    ah_id = models.IntegerField(db_column='AHId', blank=True, null=True) # Can be FK
    acc_head = models.ForeignKey(AccHead, models.DO_NOTHING, db_column='AHId', related_name='pr_details_acc_heads', null=True) # Direct FK
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=5, blank=True, null=True)
    supplier_id = models.CharField(db_column='SupplierId', max_length=50, blank=True, null=True)

    objects = PRDetailManager() # Attach the custom manager

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Details'
        verbose_name = 'PR Detail'
        verbose_name_plural = 'PR Details'

    def __str__(self):
        return f"PR Item {self.id} for PR No {self.pr_no}"

```

#### 4.2 Forms (`material_management/forms.py`)

Since the original ASP.NET page is purely for display and selection, it doesn't have an explicit form for `PurchaseRequestItem` creation or editing. However, to adhere to the prompt's requirement for a complete CRUD set, we'll create a dummy `ModelForm` for `PRDetail`. This form would be used if there were "Add New" or "Edit" functionalities for individual PR details.

```python
from django import forms
from .models import PRDetail, ItemMaster, AccHead # Import relevant models for fields

class PRDetailForm(forms.ModelForm):
    # Example: Adding a 'pr_master_id' field if it were editable directly from PRDetail
    # This form is conceptual based on the prompt's template, as the source ASPX is read-only.
    # In a real app, you'd likely have a form for PRMaster and nested forms for PRDetails.
    class Meta:
        model = PRDetail
        # fields = ['pr_no', 'item_master', 'qty', 'del_date', 'acc_head', 'supplier_id'] # Example fields
        # Using a minimal set of fields that might conceptually be edited if this were an edit form
        fields = ['qty', 'del_date', 'supplier_id'] # Minimal fields for a conceptual form
        widgets = {
            'qty': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'del_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'supplier_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        
    # Add custom validation methods here if needed
    def clean(self):
        cleaned_data = super().clean()
        # Example validation: ensure quantity is positive
        qty = cleaned_data.get('qty')
        if qty is not None and qty <= 0:
            self.add_error('qty', 'Quantity must be a positive number.')
        return cleaned_data

```

#### 4.3 Views (`material_management/views.py`)

We will use Class-Based Views (CBVs) for list, create, update, and delete, even though the original page only provides read and select functionality. The core data loading logic will be in `PRDetailListView` and its partial table view. The `select` command will be handled by a dedicated view.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from .models import PRDetail # We use PRDetail as the primary model for data interaction
from .forms import PRDetailForm # The conceptual form

# Data class to hold the processed PR Item data for the grid
# This helps in strict type hinting and clarity for what the manager method returns.
from dataclasses import dataclass

@dataclass
class PRGridItem:
    id: int
    pr_no: str
    fin_year: str
    wo_no: str
    date: str
    item_code: str
    purch_desc: str
    uom_purch: str
    qty: float
    po_qty: float
    remain_qty: float
    ac_head: str
    deli_date: str
    gen_by: str

class PRDetailListView(ListView):
    # This view orchestrates loading the full page and the initial HTMX trigger
    model = PRDetail # Use PRDetail as the model
    template_name = 'material_management/prdetail/list.html'
    context_object_name = 'pr_items' # Renaming for clarity

    def get_queryset(self):
        # We don't directly return a queryset here, as the actual data is
        # processed by the manager method which returns a list of dictionaries.
        # This method is primarily for the base ListView's model setup.
        return PRDetail.objects.none() # Or an empty queryset, as data is loaded via HTMX

class PRDetailTablePartialView(View):
    # This view provides the HTMX-loaded partial table content
    def get(self, request, *args, **kwargs):
        # Extract parameters from session and query string, replicating ASP.NET behavior
        comp_id = request.session.get('compid', 1) # Default to 1 if not in session
        fin_year_id = request.session.get('finyear', 'FY2023') # Default value
        sup_code = request.GET.get('Code', '') # From QueryString

        # Use the custom manager method to get processed data
        pr_items_data = PRDetail.objects.get_pr_items_for_grid(sup_code, fin_year_id, comp_id)
        
        # Convert dictionary list to PRGridItem objects for better type handling in template
        pr_items = [PRGridItem(**item) for item in pr_items_data]

        context = {
            'pr_items': pr_items,
        }
        return render(request, 'material_management/prdetail/_prdetail_table.html', context)

class PRDetailCreateView(CreateView):
    model = PRDetail
    form_class = PRDetailForm
    template_name = 'material_management/prdetail/form.html'
    success_url = reverse_lazy('prdetail_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'PR Detail added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPRDetailList'
                }
            )
        return response

class PRDetailUpdateView(UpdateView):
    model = PRDetail
    form_class = PRDetailForm
    template_name = 'material_management/prdetail/form.html'
    success_url = reverse_lazy('prdetail_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'PR Detail updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPRDetailList'
                }
            )
        return response

class PRDetailDeleteView(DeleteView):
    model = PRDetail
    template_name = 'material_management/prdetail/confirm_delete.html'
    success_url = reverse_lazy('prdetail_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'PR Detail deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPRDetailList'
                }
            )
        return response

class PRDetailSelectView(View):
    def post(self, request, *args, **kwargs):
        # Replicate the logic of GridView2_RowCommand
        prno = request.POST.get('prno')
        prid = request.POST.get('prid')
        wono = request.POST.get('wono')
        sup_code = request.POST.get('sup_code') # Passed from hidden input or data attribute

        # Construct the URL for redirect, similar to ASP.NET Response.Redirect
        # This can be handled client-side with HTMX (e.g., hx-push-url) or server-side.
        # For server-side redirect on HTMX, we return HX-Redirect header.
        if prno and prid and wono:
            # Construct the target URL for the next page, e.g., 'po_pr_item_select'
            # You would define a URL pattern for this in your main urls.py or another app's urls.py
            target_url = reverse_lazy('po_pr_item_select') # Placeholder for the actual select page
            # Append query parameters as in ASP.NET
            target_url += f"?wono={wono}&prno={prno}&prid={prid}&Code={sup_code}&ModId=6&SubModId=35"
            
            # For HTMX, trigger a client-side redirect or push URL
            return HttpResponse(
                status=204, # No content
                headers={
                    'HX-Redirect': target_url # HTMX will redirect the browser
                    # Or 'HX-Push-Url': target_url if you want to update URL without full redirect
                }
            )
        
        messages.error(self.request, "Invalid selection parameters.")
        return HttpResponse(status=400) # Bad request

```

#### 4.4 Templates (`material_management/templates/material_management/prdetail/`)

**`list.html`** (Main page, extends `core/base.html`):

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Purchase Request Items</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'prdetail_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New PR Detail
        </button>
    </div>
    
    <div id="prdetailTable-container"
         hx-trigger="load, refreshPRDetailList from:body"
         hx-get="{% url 'prdetail_table' %}?Code={{ request.GET.Code|default:'' }}" {# Pass SupCode from query string #}
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading PR Items...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for broader UI states
    });

    // Event listener to re-initialize DataTables when new content is swapped in
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'prdetailTable-container') {
            // Destroy existing DataTable if it exists
            if ($.fn.DataTable.isDataTable('#prdetailTable')) {
                $('#prdetailTable').DataTable().destroy();
            }
            // Initialize new DataTable
            $('#prdetailTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                // Add any other DataTables options here
            });
        }
    });

    // Listen for 'refreshPRDetailList' custom event to trigger HX-GET
    document.body.addEventListener('refreshPRDetailList', function() {
        htmx.trigger('#prdetailTable-container', 'refresh');
    });

    // Close modal on successful form submission (hx-trigger handles refresh)
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.detail.target.closest('form') && evt.detail.xhr.status === 204) {
            // For HTMX 204 No Content response, close the modal
            document.getElementById('modal').classList.remove('is-active');
            document.getElementById('modalContent').innerHTML = ''; // Clear content
        }
    });

</script>
{% endblock %}
```

**`_prdetail_table.html`** (Partial for DataTable content):

```html
<div class="overflow-x-auto shadow-md sm:rounded-lg">
    <table id="prdetailTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PR No</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">PR Qty</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">PO Qty</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Remain Qty</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">A/c Head</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Del. Date</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for item in pr_items %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ item.fin_year }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ item.wo_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ item.pr_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ item.date }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ item.item_code }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ item.purch_desc }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ item.uom_purch }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.qty|floatformat:"3" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.po_qty|floatformat:"3" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.remain_qty|floatformat:"3" }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ item.ac_head }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ item.deli_date }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ item.gen_by }}</td>
                <td class="py-2 px-4 border-b border-gray-200">
                    <button 
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded"
                        hx-post="{% url 'prdetail_select' %}"
                        hx-vals='{"prno": "{{ item.pr_no }}", "prid": "{{ item.id }}", "wono": "{{ item.wo_no }}", "sup_code": "{{ request.GET.Code|default:'' }}"}'
                        hx-confirm="Are you sure you want to select this item?"
                        hx-target="body" hx-swap="none"> {# HTMX will handle redirect via HX-Redirect header #}
                        Select
                    </button>
                    {# Example buttons for Edit/Delete for the complete CRUD template #}
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                        hx-get="{% url 'prdetail_edit' item.id %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                        hx-get="{% url 'prdetail_delete' item.id %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="15" class="py-4 text-center text-gray-500">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

{# DataTables initialization moved to main list.html's htmx:afterSwap listener #}

```

**`form.html`** (Partial for Create/Update):

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} PR Detail</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`confirm_delete.html`** (Partial for Delete confirmation):

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700">Are you sure you want to delete this PR Detail (ID: {{ prdetail.pk }})?</p>
    
    <div class="mt-6 flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button 
            hx-post="{% url 'prdetail_delete' prdetail.pk %}"
            hx-swap="none"
            type="submit" 
            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            Delete
        </button>
    </div>
</div>
```

#### 4.5 URLs (`material_management/urls.py`)

```python
from django.urls import path
from .views import (
    PRDetailListView, 
    PRDetailTablePartialView,
    PRDetailCreateView, 
    PRDetailUpdateView, 
    PRDetailDeleteView,
    PRDetailSelectView
)

urlpatterns = [
    path('pr_item_grid/', PRDetailListView.as_view(), name='prdetail_list'),
    path('pr_item_grid/table/', PRDetailTablePartialView.as_view(), name='prdetail_table'),
    path('pr_item_grid/add/', PRDetailCreateView.as_view(), name='prdetail_add'),
    path('pr_item_grid/edit/<int:pk>/', PRDetailUpdateView.as_view(), name='prdetail_edit'),
    path('pr_item_grid/delete/<int:pk>/', PRDetailDeleteView.as_view(), name='prdetail_delete'),
    path('pr_item_grid/select/', PRDetailSelectView.as_view(), name='prdetail_select'),
    # Define a placeholder for the target page for "select" action in your project's main urls.py
    # path('po_pr_item_select/', YourSelectPageView.as_view(), name='po_pr_item_select'),
]

```
*(Add this to your project's main `urls.py` in `urlpatterns` for it to be accessible, e.g., `path('material_management/', include('material_management.urls'))`)*

#### 4.6 Tests (`material_management/tests.py`)

```python       
from django.test import TestCase, Client
from django.urls import reverse
from django.db import connection # For checking database schema if needed

from .models import (
    PRDetail, PRMaster, ItemMaster, UnitMaster, AccHead,
    OfficeStaff, FinancialYear, POMaster, PODetail, PRPOTemp
)
import datetime
from unittest.mock import patch, MagicMock

# --- Model Tests ---

class LegacyModelTest(TestCase):
    # Use a dummy database alias for testing 'managed=False' models
    databases = {'default', 'legacy_db'} # Ensure 'legacy_db' is configured in settings for tests

    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for all related legacy models
        # This setup assumes a specific order and relationships for foreign keys
        
        # Financial Year
        FinancialYear.objects.using('legacy_db').create(fin_year_id='FY2023', fin_year='2023-2024', comp_id=1)
        FinancialYear.objects.using('legacy_db').create(fin_year_id='FY2022', fin_year='2022-2023', comp_id=1)

        # AccHead
        AccHead.objects.using('legacy_db').create(id=1, symbol='AC', description='Account Receivable')
        AccHead.objects.using('legacy_db').create(id=2, symbol='XP', description='Expenses')

        # UnitMaster
        UnitMaster.objects.using('legacy_db').create(id=101, symbol='KG')
        UnitMaster.objects.using('legacy_db').create(id=102, symbol='EA')

        # ItemMaster
        ItemMaster.objects.using('legacy_db').create(id=1001, item_code='ITEM001', manf_desc='Product Alpha', uom_basic=101, comp_id=1)
        ItemMaster.objects.using('legacy_db').create(id=1002, item_code='ITEM002', manf_desc='Product Beta', uom_basic=102, comp_id=1)

        # OfficeStaff
        OfficeStaff.objects.using('legacy_db').create(emp_id='EMP001', title='Mr', employee_name='John Doe', comp_id=1)
        OfficeStaff.objects.using('legacy_db').create(emp_id='EMP002', title='Ms', employee_name='Jane Smith', comp_id=1)

        # PRMaster
        PRMaster.objects.using('legacy_db').create(id=1, pr_no='PR001', fin_year_id='FY2023', sys_date='2023-01-15', session_id='EMP001', wo_no='WO123', comp_id=1)
        PRMaster.objects.using('legacy_db').create(id=2, pr_no='PR002', fin_year_id='FY2023', sys_date='2023-02-10', session_id='EMP002', wo_no='WO456', comp_id=1)

        # PRDetail
        PRDetail.objects.using('legacy_db').create(id=101, m_id=PRMaster.objects.using('legacy_db').get(id=1), item_id=1001, del_date='2023-03-01', ah_id=1, qty=100.00, supplier_id='SUP001', pr_no='PR001')
        PRDetail.objects.using('legacy_db').create(id=102, m_id=PRMaster.objects.using('legacy_db').get(id=2), item_id=1002, del_date='2023-03-05', ah_id=2, qty=50.00, supplier_id='SUP002', pr_no='PR002')
        # PRDetail with some PO Quantity
        PRDetail.objects.using('legacy_db').create(id=103, m_id=PRMaster.objects.using('legacy_db').get(id=1), item_id=1001, del_date='2023-03-10', ah_id=1, qty=75.00, supplier_id='SUP001', pr_no='PR001')

        # POMaster
        POMaster.objects.using('legacy_db').create(id=1, comp_id=1)
        POMaster.objects.using('legacy_db').create(id=2, comp_id=1)

        # PODetail
        PODetail.objects.using('legacy_db').create(id=201, m_id=POMaster.objects.using('legacy_db').get(id=1), pr_id=101, qty=20.00)
        PODetail.objects.using('legacy_db').create(id=202, m_id=POMaster.objects.using('legacy_db').get(id=2), pr_id=103, qty=70.00) # Leaves 5.00 remain_qty for 103

        # PRPOTemp
        PRPOTemp.objects.using('legacy_db').create(id=301, pr_no='PR002', pr_id=102) # This should exclude PRDetail 102

    def test_model_creation(self):
        pr_master = PRMaster.objects.using('legacy_db').get(id=1)
        self.assertEqual(pr_master.pr_no, 'PR001')

        pr_detail = PRDetail.objects.using('legacy_db').get(id=101)
        self.assertEqual(pr_detail.qty, 100.00)
        self.assertEqual(pr_detail.supplier_id, 'SUP001')
        self.assertEqual(pr_detail.item_master.item_code, 'ITEM001')
        self.assertEqual(pr_detail.acc_head.description, 'Account Receivable')
        self.assertEqual(pr_detail.pr_master.session_id_obj.employee_name, 'John Doe') # Direct FK lookup

    def test_pr_item_grid_manager_method(self):
        # Test PRDetailManager.get_pr_items_for_grid
        sup_code = 'SUP001'
        fin_year_id = 'FY2023'
        comp_id = 1
        
        pr_items = PRDetail.objects.using('legacy_db').get_pr_items_for_grid(sup_code, fin_year_id, comp_id)
        
        self.assertEqual(len(pr_items), 2) # PR001 (id 101) and PR001 (id 103) should be included

        # Check PRDetail 101
        item_101 = next(item for item in pr_items if item['id'] == 101)
        self.assertIsNotNone(item_101)
        self.assertEqual(item_101['pr_no'], 'PR001')
        self.assertEqual(item_101['qty'], 100.00)
        self.assertEqual(item_101['po_qty'], 20.00)
        self.assertEqual(item_101['remain_qty'], 80.00)
        self.assertEqual(item_101['item_code'], 'ITEM001')
        self.assertEqual(item_101['ac_head'], '[AC]Account Receivable')
        self.assertEqual(item_101['gen_by'], 'Mr. John Doe')
        self.assertEqual(item_101['fin_year'], '2023-2024')
        self.assertEqual(item_101['date'], '15/01/2023')
        self.assertEqual(item_101['deli_date'], '01/03/2023')


        # Check PRDetail 103
        item_103 = next(item for item in pr_items if item['id'] == 103)
        self.assertIsNotNone(item_103)
        self.assertEqual(item_103['pr_no'], 'PR001')
        self.assertEqual(item_103['qty'], 75.00)
        self.assertEqual(item_103['po_qty'], 70.00)
        self.assertEqual(item_103['remain_qty'], 5.00)

        # Check that PRDetail 102 is excluded due to PRPOTemp entry
        item_102_excluded = any(item for item in pr_items if item['id'] == 102)
        self.assertFalse(item_102_excluded)

        # Test with a supplier code that has no entries
        no_data_items = PRDetail.objects.using('legacy_db').get_pr_items_for_grid('NONEXISTENT', fin_year_id, comp_id)
        self.assertEqual(len(no_data_items), 0)

# --- Views Tests ---

class PRDetailViewsTest(TestCase):
    databases = {'default', 'legacy_db'} # Ensure 'legacy_db' is configured in settings for tests

    @classmethod
    def setUpTestData(cls):
        # Re-create test data as views operate on the same data
        # Financial Year
        FinancialYear.objects.using('legacy_db').create(fin_year_id='FY2023', fin_year='2023-2024', comp_id=1)
        FinancialYear.objects.using('legacy_db').create(fin_year_id='FY2022', fin_year='2022-2023', comp_id=1)

        # AccHead
        AccHead.objects.using('legacy_db').create(id=1, symbol='AC', description='Account Receivable')
        AccHead.objects.using('legacy_db').create(id=2, symbol='XP', description='Expenses')

        # UnitMaster
        UnitMaster.objects.using('legacy_db').create(id=101, symbol='KG')
        UnitMaster.objects.using('legacy_db').create(id=102, symbol='EA')

        # ItemMaster
        ItemMaster.objects.using('legacy_db').create(id=1001, item_code='ITEM001', manf_desc='Product Alpha', uom_basic=101, comp_id=1)
        ItemMaster.objects.using('legacy_db').create(id=1002, item_code='ITEM002', manf_desc='Product Beta', uom_basic=102, comp_id=1)

        # OfficeStaff
        OfficeStaff.objects.using('legacy_db').create(emp_id='EMP001', title='Mr', employee_name='John Doe', comp_id=1)
        OfficeStaff.objects.using('legacy_db').create(emp_id='EMP002', title='Ms', employee_name='Jane Smith', comp_id=1)

        # PRMaster
        PRMaster.objects.using('legacy_db').create(id=1, pr_no='PR001', fin_year_id='FY2023', sys_date='2023-01-15', session_id='EMP001', wo_no='WO123', comp_id=1)
        PRMaster.objects.using('legacy_db').create(id=2, pr_no='PR002', fin_year_id='FY2023', sys_date='2023-02-10', session_id='EMP002', wo_no='WO456', comp_id=1)

        # PRDetail
        PRDetail.objects.using('legacy_db').create(id=101, m_id=PRMaster.objects.using('legacy_db').get(id=1), item_id=1001, del_date='2023-03-01', ah_id=1, qty=100.00, supplier_id='SUP001', pr_no='PR001')
        PRDetail.objects.using('legacy_db').create(id=102, m_id=PRMaster.objects.using('legacy_db').get(id=2), item_id=1002, del_date='2023-03-05', ah_id=2, qty=50.00, supplier_id='SUP002', pr_no='PR002')
        PRDetail.objects.using('legacy_db').create(id=103, m_id=PRMaster.objects.using('legacy_db').get(id=1), item_id=1001, del_date='2023-03-10', ah_id=1, qty=75.00, supplier_id='SUP001', pr_no='PR001')

        # POMaster
        POMaster.objects.using('legacy_db').create(id=1, comp_id=1)
        POMaster.objects.using('legacy_db').create(id=2, comp_id=1)

        # PODetail
        PODetail.objects.using('legacy_db').create(id=201, m_id=POMaster.objects.using('legacy_db').get(id=1), pr_id=101, qty=20.00)
        PODetail.objects.using('legacy_db').create(id=202, m_id=POMaster.objects.using('legacy_db').get(id=2), pr_id=103, qty=70.00)

        # PRPOTemp
        PRPOTemp.objects.using('legacy_db').create(id=301, pr_no='PR002', pr_id=102)

    def setUp(self):
        self.client = Client()
        # Set session variables for testing
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 'FY2023'
        session.save()
    
    def test_list_view_get(self):
        response = self.client.get(reverse('prdetail_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/prdetail/list.html')
        # The list view doesn't directly return pr_items in context now, it's loaded via HTMX
        self.assertContains(response, 'Loading PR Items...') # Check for initial loading message

    def test_prdetail_table_partial_view(self):
        # Test HTMX partial view load
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('prdetail_table'), {'Code': 'SUP001'}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/prdetail/_prdetail_table.html')
        self.assertContains(response, 'PR001') # Check for data from the table
        self.assertContains(response, 'ITEM001')
        self.assertContains(response, '80.00') # Remaining Qty for item 101
        self.assertNotContains(response, 'PR002') # Should be filtered out by PRPOTemp

    def test_prdetail_table_partial_view_no_data(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('prdetail_table'), {'Code': 'NONEXISTENT'}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/prdetail/_prdetail_table.html')
        self.assertContains(response, 'No data to display !')

    def test_create_view_get(self):
        response = self.client.get(reverse('prdetail_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/prdetail/form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add PR Detail')

    def test_create_view_post_htmx(self):
        # We need to mock the save on the legacy_db as PRDetail is managed=False
        # For a real scenario, you'd insert directly into the legacy DB.
        # Here, we'll simulate the save for testing purposes.
        with patch('material_management.models.PRDetail.objects.using') as mock_using:
            mock_prdetail_manager = MagicMock()
            mock_using.return_value = mock_prdetail_manager
            mock_prdetail_manager.create.return_value = MagicMock(pk=999) # Mock a created instance
            
            data = {
                'qty': 123.45,
                'del_date': '2024-05-30',
                'supplier_id': 'SUP999',
            }
            response = self.client.post(reverse('prdetail_add'), data, HTTP_HX_REQUEST='true')
            self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
            self.assertIn('HX-Trigger', response.headers)
            self.assertEqual(response.headers['HX-Trigger'], 'refreshPRDetailList')
            mock_prdetail_manager.create.assert_called_once()
            # Assertions for the data passed to create (need to align with form fields)
            # This requires careful mocking of the specific fields saved.
            # For simplicity, just checking call count.

    def test_update_view_get(self):
        pr_detail = PRDetail.objects.using('legacy_db').get(id=101)
        response = self.client.get(reverse('prdetail_edit', args=[pr_detail.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/prdetail/form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Edit PR Detail')
        self.assertContains(response, 'value="100.00000"') # Check for current quantity value

    def test_update_view_post_htmx(self):
        pr_detail = PRDetail.objects.using('legacy_db').get(id=101)
        with patch('material_management.models.PRDetail.objects.using') as mock_using:
            mock_prdetail_manager = MagicMock()
            mock_using.return_value = mock_prdetail_manager
            mock_prdetail_manager.get.return_value = pr_detail # Ensure get returns the object to update
            mock_prdetail_manager.filter.return_value = MagicMock(first=lambda: pr_detail) # For update path
            pr_detail.save = MagicMock() # Mock save on the instance
            
            data = {
                'qty': 110.00,
                'del_date': '2023-03-01', # Keep existing date for simplicity
                'supplier_id': 'SUP001',
            }
            response = self.client.post(reverse('prdetail_edit', args=[pr_detail.id]), data, HTTP_HX_REQUEST='true')
            self.assertEqual(response.status_code, 204)
            self.assertIn('HX-Trigger', response.headers)
            self.assertEqual(response.headers['HX-Trigger'], 'refreshPRDetailList')
            pr_detail.save.assert_called_once()
            self.assertEqual(pr_detail.qty, 110.00)

    def test_delete_view_get(self):
        pr_detail = PRDetail.objects.using('legacy_db').get(id=101)
        response = self.client.get(reverse('prdetail_delete', args=[pr_detail.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/prdetail/confirm_delete.html')
        self.assertTrue('prdetail' in response.context)
        self.assertContains(response, 'Are you sure you want to delete this PR Detail (ID: 101)?')

    def test_delete_view_post_htmx(self):
        pr_detail_id_to_delete = 101
        pr_detail_count_before = PRDetail.objects.using('legacy_db').count()
        
        with patch('material_management.models.PRDetail.objects.using') as mock_using:
            mock_prdetail_manager = MagicMock()
            mock_using.return_value = mock_prdetail_manager
            # Mock the filter and delete method for the specific object
            mock_filter_result = MagicMock()
            mock_filter_result.first.return_value = PRDetail.objects.using('legacy_db').get(id=pr_detail_id_to_delete)
            mock_filter_result.delete.return_value = (1, {'material_management.PRDetail': 1}) # (num_deleted, dict_of_deleted_objects)
            mock_prdetail_manager.filter.return_value = mock_filter_result

            response = self.client.post(reverse('prdetail_delete', args=[pr_detail_id_to_delete]), HTTP_HX_REQUEST='true')
            self.assertEqual(response.status_code, 204)
            self.assertIn('HX-Trigger', response.headers)
            self.assertEqual(response.headers['HX-Trigger'], 'refreshPRDetailList')
            # Verify delete was called on the mock object
            mock_filter_result.delete.assert_called_once()
            # In a real scenario, you'd check `PRDetail.objects.using('legacy_db').filter(id=pr_detail_id_to_delete).exists()` is False

    def test_prdetail_select_view_post_htmx(self):
        data = {
            'prno': 'PR001',
            'prid': '101',
            'wono': 'WO123',
            'sup_code': 'SUP001',
        }
        response = self.client.post(reverse('prdetail_select'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content
        self.assertIn('HX-Redirect', response.headers)
        expected_redirect_url = reverse('po_pr_item_select') + '?wono=WO123&prno=PR001&prid=101&Code=SUP001&ModId=6&SubModId=35'
        self.assertEqual(response.headers['HX-Redirect'], expected_redirect_url)

    def test_prdetail_select_view_post_invalid_data(self):
        data = {
            'prno': 'PR001',
            'prid': '', # Missing prid
            'wono': 'WO123',
            'sup_code': 'SUP001',
        }
        response = self.client.post(reverse('prdetail_select'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400) # Bad Request
        self.assertContains(response, 'Invalid selection parameters.', status_code=400)

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
*   **HTMX for DataTables:** The `list.html` uses `hx-get` on `prdetailTable-container` to load the table content from `{% url 'prdetail_table' %}`. It's triggered on `load` and a custom `refreshPRDetailList` event.
*   **DataTables Initialization:** The `_prdetail_table.html` partial contains the `<table>` with `id="prdetailTable"`. The JavaScript in `list.html`'s `extra_js` block listens for `htmx:afterSwap` on the table container to destroy and re-initialize DataTables when the content is loaded/refreshed.
*   **Modals for CRUD:**
    *   Buttons like "Add New PR Detail", "Edit", and "Delete" use `hx-get` to fetch the form/confirmation partials (`form.html`, `confirm_delete.html`) into `#modalContent`.
    *   They use `_="on click add .is-active to #modal"` to show the modal (assuming `is-active` class controls visibility).
    *   Cancel buttons in modals use `_="on click remove .is-active from #modal"` to hide the modal.
*   **Form Submission (HTMX):** Forms within the modal (e.g., `form.html`, `confirm_delete.html`) use `hx-post="{{ request.path }}"` for submission.
    *   Upon successful `form_valid` or `delete` in views, a `HttpResponse(status=204, headers={'HX-Trigger': 'refreshPRDetailList'})` is returned.
    *   The `list.html` listens for `refreshPRDetailList` on `document.body` and triggers a refresh of the `prdetailTable-container`.
    *   The `htmx:afterRequest` listener also closes the modal automatically if a 204 response is received.
*   **"Select" Action (HTMX):** The "Select" button in `_prdetail_table.html` uses `hx-post="{% url 'prdetail_select' %}"` to send the selected row's data (`prno`, `prid`, `wono`, `sup_code`) to the `PRDetailSelectView`.
    *   The `PRDetailSelectView` then returns an `HX-Redirect` header with the target URL, causing the browser to navigate to the new page.
*   **Alpine.js:** While not explicitly shown for complex UI states, Alpine.js could manage modal visibility, form states (e.g., showing/hiding fields conditionally), or simple client-side animations. The `_=` attributes in the templates demonstrate basic Alpine.js (via `hyperscript` which Alpine supports well) for modal toggling.

### Final Notes

*   **Database Configuration:** Ensure your Django `settings.py` is configured with a database alias (e.g., `legacy_db`) pointing to your SQL Server instance hosting these tables.
*   **`fun.GetItemCode_PartNo`:** The `models.py` attempts a simplified mapping. If `GetItemCode_PartNo` had more complex business logic (e.g., combining multiple fields, lookup in other tables), that logic would need to be precisely replicated in the `PRDetailManager` or a dedicated `services.py` module.
*   **Error Handling:** The ASP.NET code had a generic `try...catch` for `LoadData`. In Django, errors should be handled gracefully, potentially with custom exception handling middleware or more specific `try...except` blocks in the manager method for database interactions.
*   **User Authentication/Authorization:** The ASP.NET code uses `Session["compid"]` and `Session["finyear"]`. In Django, this typically involves `request.user` and associated user profiles or permissions. Ensure proper authentication and authorization are implemented for accessing `CompId` and `FinYearId` if they're tied to the logged-in user. For this migration, we assume these session variables are available.
*   **Scalability & Performance:** For very large datasets or extremely complex queries, consider optimizing the `PRDetailManager.get_pr_items_for_grid` method further, possibly using raw SQL queries for the core data retrieval and then hydrating Django model instances, or leveraging database views. The current ORM approach with `annotate` and `Subquery` is powerful but requires careful indexing on the database side.