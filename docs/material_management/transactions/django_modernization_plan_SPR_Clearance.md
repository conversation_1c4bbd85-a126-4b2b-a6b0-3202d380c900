This document outlines a comprehensive plan for modernizing your ASP.NET application, specifically the `SPR_Clearance.aspx` module, by migrating it to a robust and scalable Django 5.0+ solution. Our approach prioritizes automation, clean architecture, and modern web standards using HTMX and Alpine.js.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:** The provided ASP.NET `.aspx` and C# code-behind files do not explicitly contain database schema definitions or SQL queries. The C# code-behind's `Page_Load` method is empty, indicating that any data interaction is either declarative in the ASPX (which is not present) or occurs through a deeper framework layer.

**Inferences & Assumptions:**
Based on the page title "SPR - Check / Approve / Authorize" and the presence of "If Yes" / "If Not Clear" radio buttons with a "remark" text box, this page is designed to record a decision and a comment related to an existing "SPR" (Supplier Purchase Requisition or similar). The `<iframe>` likely displays details of the SPR being processed.

To support this functionality and the required Django structure (models, views), we will infer the following database tables:

1.  **`tblSprRequest`**: This table is assumed to hold the main SPR (Supplier Purchase Requisition) records. While not directly managed by this ASP.NET page, it's the entity being "cleared."
    *   **Columns:**
        *   `REQ_ID` (Primary Key, e.g., `INT` or `BIGINT`)
        *   `REQ_NO` (Request Number, e.g., `NVARCHAR(50)`)
        *   `STATUS` (Current status of the SPR, e.g., `NVARCHAR(20)`, initially 'Pending')
        *   `REQUEST_DT` (Date of Request, e.g., `DATETIME`)
        *   *(Other relevant SPR fields like description, requested items, etc., are omitted for this specific migration but would exist)*

2.  **`tblSprClearance`**: This table will store the actual clearance decisions made on the SPR.
    *   **Columns:**
        *   `ID` (Primary Key, e.g., `INT` or `BIGINT`)
        *   `SPR_REQ_ID` (Foreign Key to `tblSprRequest.REQ_ID`, e.g., `INT`)
        *   `DECISION` (Decision, e.g., `NVARCHAR(10)`, 'Approved' or 'Rejected')
        *   `REMARK` (Remarks, e.g., `NVARCHAR(MAX)` or `TEXT`)
        *   `CLEARED_BY` (User who cleared it, Foreign Key to a User table, e.g., `INT`)
        *   `CLEARED_DATE` (Date and time of clearance, e.g., `DATETIME`)

### Step 2: Identify Backend Functionality

**Analysis:** The ASP.NET page primarily acts as an input form for a clearance decision.

*   **Create/Update Operation:**
    *   The "Proceed" button (`Button1`) triggers the submission of the decision and remark. This indicates either:
        *   **Creation:** A new `SPR_Clearance` record is created for a given `SPR_Request`.
        *   **Update:** An existing `SPR_Request` record's status is updated, potentially alongside the creation of a `SPR_Clearance` log entry.
    *   For this migration, we will model it as **creating a new `SprClearance` record**, which in turn triggers a business logic method on the related `SprRequest` to update its status. This ensures an audit trail of clearance actions.

*   **Read Operation:**
    *   The `<iframe>` (`id="I1"`) is expected to display details of the `SPR_Request` being cleared. This implies a "Read" operation on the `tblSprRequest` table.
    *   For the Django side, we will also create a `ListView` for `SprClearance` records to provide a historical audit trail of all clearances, which is a common requirement in ERP systems.

*   **Validation Logic:**
    *   The `RadioButton2` text "If Not Clear send remark." implies that the `TextBox1` (remark) becomes mandatory if the "Rejected" decision is chosen.

### Step 3: Infer UI Components

**Analysis:** The ASP.NET controls translate directly to standard HTML form elements managed by Django forms.

*   **`<iframe>` (id="I1")**: Replaced with an HTMX-driven partial view that loads the `SPR_Request` details dynamically.
*   **`asp:RadioButton` (`RadioButton1`, `RadioButton2`)**: Will be mapped to a Django `forms.ChoiceField` rendered with radio buttons.
*   **`asp:TextBox` (`TextBox1`)**: Will be mapped to a Django `forms.TextField` (or `CharField` with `widget=forms.Textarea`).
*   **`asp:Button` (`Button1`, `Button2`)**: Will be mapped to standard HTML `<button>` elements. The "Proceed" button will submit the form via HTMX. The "Cancel" button will typically navigate back or close a modal.

### Step 4: Generate Django Code

We will structure the Django application within a module named `material_management`, aligning with the original ASP.NET namespace `Module_MaterialManagement_Transactions_SPR_Clearance`.

#### 4.1 Models (`material_management/models.py`)

This file will define the Django models that map to your existing database tables. We include `SprRequest` as a necessary related model, even though its full definition is beyond the scope of *this specific ASP.NET page's* migration, as it's the entity being cleared.

```python
from django.db import models
from django.contrib.auth.models import User # Assuming Django's built-in User model for 'cleared_by'

# Placeholder for the main SPR Request model.
# In a real-world scenario, this model would be fully defined from its own ASP.NET page/table migration.
class SprRequest(models.Model):
    """
    Represents a Supplier Purchase Request (SPR).
    Maps to an assumed existing table 'tblSprRequest'.
    """
    request_number = models.CharField(max_length=50, unique=True, db_column='REQ_NO', verbose_name='Request Number')
    status = models.CharField(max_length=20, default='Pending', db_column='STATUS', verbose_name='Status')
    request_date = models.DateTimeField(db_column='REQUEST_DT', auto_now_add=True, verbose_name='Request Date')
    description = models.TextField(blank=True, null=True, verbose_name='Description') # Added for detail view example

    class Meta:
        managed = False # Django will not manage this table's schema
        db_table = 'tblSprRequest'
        verbose_name = 'SPR Request'
        verbose_name_plural = 'SPR Requests'

    def __str__(self):
        return self.request_number

    def update_status(self, new_status: str):
        """
        Updates the status of the SPR Request.
        Business logic: Potentially validates new_status, logs changes, etc.
        """
        if new_status not in ['Pending', 'Approved', 'Rejected', 'Cancelled']: # Example status validation
            raise ValueError(f"Invalid status: {new_status}")
        self.status = new_status
        self.save(update_fields=['status']) # Only update the status field
        # Add any post-update actions here (e.g., logging, notifications)
        # print(f"SPR {self.request_number} status updated to {self.status}")

class SprClearance(models.Model):
    """
    Represents a clearance (check/approve/authorize) record for an SPR Request.
    Maps to an assumed existing table 'tblSprClearance'.
    """
    DECISION_CHOICES = [
        ('Approved', 'Approved (If Yes)'),
        ('Rejected', 'Rejected (If Not Clear send remark.)'),
    ]

    spr_request = models.ForeignKey(
        SprRequest,
        on_delete=models.PROTECT, # Prevent deletion of SPR Request if clearances exist
        db_column='SPR_REQ_ID',
        verbose_name='SPR Request',
        related_name='clearances'
    )
    decision = models.CharField(
        max_length=10,
        choices=DECISION_CHOICES,
        db_column='DECISION',
        verbose_name='Clearance Decision'
    )
    remark = models.TextField(
        blank=True,
        null=True,
        db_column='REMARK',
        verbose_name='Remarks'
    )
    cleared_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT, # Prevent user deletion if they have cleared SPRs
        db_column='CLEARED_BY',
        verbose_name='Cleared By'
    )
    cleared_date = models.DateTimeField(
        auto_now_add=True,
        db_column='CLEARED_DATE',
        verbose_name='Cleared Date'
    )

    class Meta:
        managed = False # Django will not manage this table's schema
        db_table = 'tblSprClearance' # Inferred table name
        verbose_name = 'SPR Clearance'
        verbose_name_plural = 'SPR Clearances'
        ordering = ['-cleared_date'] # Order by most recent clearance first

    def __str__(self):
        return f"Clearance for {self.spr_request.request_number} ({self.decision}) by {self.cleared_by.username}"

    def process_clearance(self):
        """
        Business logic to process the clearance. This method encapsulates the
        impact of this clearance record on the associated SPR Request.
        """
        # Update the related SPR_Request status based on the decision.
        # This is the 'fat model' approach: business logic lives here.
        if self.decision == 'Approved':
            self.spr_request.update_status('Approved')
        elif self.decision == 'Rejected':
            self.spr_request.update_status('Rejected')
        # Else: if a decision is 'Pending' or 'In Review' but not 'Approved'/'Rejected'
        # The SPR status might remain 'Pending' or move to 'In Review' based on business rules.
        # For simplicity, we only update on 'Approved' or 'Rejected'.

        # Additional business logic can go here:
        # - Send email notifications
        # - Create audit logs
        # - Trigger workflows for next steps
        # print(f"Clearance for SPR {self.spr_request.request_number} processed. New SPR status: {self.spr_request.status}")

```

#### 4.2 Forms (`material_management/forms.py`)

This file defines the Django forms, including custom validation.

```python
from django import forms
from .models import SprClearance, SprRequest

class SprClearanceForm(forms.ModelForm):
    """
    Form for creating or updating an SPR Clearance record.
    Includes validation for remarks based on decision.
    """
    # This field is used to display the SPR Request number in the form
    # and is populated from the view's context, not directly from the model.
    spr_request_display = forms.CharField(
        label="SPR Request Number",
        required=False,
        widget=forms.TextInput(attrs={'readonly': 'readonly', 'class': 'block w-full px-3 py-2 bg-gray-100 border border-gray-300 rounded-md shadow-sm sm:text-sm'})
    )

    class Meta:
        model = SprClearance
        # spr_request is included here but will be a HiddenInput, set by the view.
        fields = ['spr_request', 'decision', 'remark']
        widgets = {
            'spr_request': forms.HiddenInput(), # This field will be pre-filled by the view
            'decision': forms.RadioSelect(
                attrs={'class': 'form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out'}
            ),
            'remark': forms.Textarea(
                attrs={'rows': 4, 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}
            ),
        }
        labels = {
            'decision': 'Clearance Decision',
            'remark': 'Remarks (Required if Not Clear)',
        }

    def __init__(self, *args, **kwargs):
        # Allow passing an SPR object to pre-fill the display field
        self.spr_obj = kwargs.pop('spr_obj', None)
        super().__init__(*args, **kwargs)

        if self.spr_obj:
            self.fields['spr_request_display'].initial = self.spr_obj.request_number
            # Ensure the hidden FK field is correctly initialized for both create and update
            self.fields['spr_request'].initial = self.spr_obj.pk

        # Manually apply Tailwind classes to radio button labels for better styling control
        # (The widget attrs above apply to the input elements, not their labels/wrappers)
        # This part might be better handled directly in the template for fine-grained control
        # For simplicity in form rendering, the default Django rendering will be used.

    def clean(self):
        """
        Custom validation for the form.
        Ensures 'remark' is provided if 'decision' is 'Rejected'.
        """
        cleaned_data = super().clean()
        decision = cleaned_data.get('decision')
        remark = cleaned_data.get('remark')

        if decision == 'Rejected' and not remark:
            self.add_error('remark', 'Remarks are required when the decision is "Rejected".')
        return cleaned_data

```

#### 4.3 Views (`material_management/views.py`)

This file defines the Django Class-Based Views (CBVs) for handling the SPR clearance process and managing historical clearance records. Views are kept "thin" (under 15 lines where possible) by offloading business logic to models.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404
from django.shortcuts import get_object_or_404
from django.contrib.auth.mixins import LoginRequiredMixin # Ensure user is logged in
from .models import SprClearance, SprRequest
from .forms import SprClearanceForm

class SprClearanceFormView(LoginRequiredMixin, CreateView):
    """
    View for displaying the SPR details and allowing a user to submit a clearance decision.
    Corresponds to the main functionality of the original ASP.NET page.
    It's a CreateView as each clearance action creates a new historical record.
    """
    model = SprClearance
    form_class = SprClearanceForm
    template_name = 'material_management/spr_clearance/clearance_form.html'
    success_url = reverse_lazy('sprclearance_list') # Redirect to the list of clearances after success

    def get_initial(self):
        """Pre-fill the hidden spr_request field from URL kwargs or GET parameters."""
        initial = super().get_initial()
        spr_request_id = self.kwargs.get('pk') or self.request.GET.get('spr_id')
        if spr_request_id:
            initial['spr_request'] = spr_request_id
        return initial

    def get_context_data(self, **kwargs):
        """Add the SPR_Request object to the context for display."""
        context = super().get_context_data(**kwargs)
        spr_request_id = self.kwargs.get('pk') or self.request.GET.get('spr_id')
        if spr_request_id:
            context['spr_request'] = get_object_or_404(SprRequest, pk=spr_request_id)
        return context
    
    def get_form_kwargs(self):
        """Pass the SPR_Request object to the form for display purposes."""
        kwargs = super().get_form_kwargs()
        spr_request_id = self.kwargs.get('pk') or self.request.GET.get('spr_id')
        if spr_request_id:
            kwargs['spr_obj'] = get_object_or_404(SprRequest, pk=spr_request_id)
        return kwargs

    def form_valid(self, form):
        """
        Assigns the current user as 'cleared_by' and calls the model's business logic.
        Handles HTMX responses for dynamic updates.
        """
        form.instance.cleared_by = self.request.user # Assign the logged-in user
        response = super().form_valid(form) # Saves the SprClearance instance

        form.instance.process_clearance() # Call the business logic in the model

        messages.success(self.request, 'SPR Clearance processed successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content response with a trigger header
            # to refresh the list of clearances in the background.
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSprClearanceList'
                }
            )
        return response

class SprClearanceListView(LoginRequiredMixin, ListView):
    """
    Displays a list of all SPR Clearance records.
    This serves as an audit/history view.
    """
    model = SprClearance
    template_name = 'material_management/spr_clearance/list.html'
    context_object_name = 'sprclearances'

class SprClearanceTablePartialView(SprClearanceListView):
    """
    A partial view specifically for HTMX to load and refresh the DataTables content.
    Inherits filtering/ordering from ListView, but renders only the table HTML.
    """
    template_name = 'material_management/spr_clearance/_sprclearance_table.html'

class SprClearanceUpdateView(LoginRequiredMixin, UpdateView):
    """
    Allows editing an existing SPR Clearance record.
    (Though in a real ERP, clearance records might be immutable logs).
    """
    model = SprClearance
    form_class = SprClearanceForm
    template_name = 'material_management/spr_clearance/form.html' # Generic form template for edit
    success_url = reverse_lazy('sprclearance_list')

    def get_form_kwargs(self):
        """Pass the SPR_Request object to the form for display purposes during update."""
        kwargs = super().get_form_kwargs()
        if self.object.spr_request:
            kwargs['spr_obj'] = self.object.spr_request
        return kwargs

    def form_valid(self, form):
        """Handles saving updates and triggering model business logic."""
        form.instance.cleared_by = self.request.user # Update 'cleared_by' on edit
        response = super().form_valid(form)
        form.instance.process_clearance() # Re-process/update SPR status if decision changed

        messages.success(self.request, 'SPR Clearance updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSprClearanceList'
                }
            )
        return response

class SprClearanceDeleteView(LoginRequiredMixin, DeleteView):
    """
    Handles deletion of an SPR Clearance record.
    (Caution: In an ERP, deletion of audit trails is often restricted).
    """
    model = SprClearance
    template_name = 'material_management/spr_clearance/confirm_delete.html'
    success_url = reverse_lazy('sprclearance_list')

    def delete(self, request, *args, **kwargs):
        """Performs deletion and handles HTMX response."""
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'SPR Clearance deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSprClearanceList'
                }
            )
        return response

class SprRequestDetailView(LoginRequiredMixin, DetailView):
    """
    Provides a partial view of an SPR Request's details.
    This replaces the functionality of the ASP.NET iframe, providing SPR details.
    """
    model = SprRequest
    template_name = 'material_management/spr_request/detail.html'
    context_object_name = 'spr_request'

```

#### 4.4 Templates (`material_management/templates/material_management/`)

We will create several templates:
*   `spr_clearance/clearance_form.html`: The main page for processing an SPR clearance, combining SPR details with the clearance form. This is the direct Django equivalent of your original `SPR_Clearance.aspx`.
*   `spr_clearance/list.html`: For displaying a list of all historical SPR clearances.
*   `spr_clearance/_sprclearance_table.html`: A partial template for the DataTables content, loaded via HTMX.
*   `spr_clearance/form.html`: A generic partial template for adding/editing a *clearance record* (used by `UpdateView`).
*   `spr_clearance/confirm_delete.html`: A partial template for confirming deletion of a clearance record.
*   `spr_request/detail.html`: A partial template to display details of an `SprRequest` (simulating the `iframe` content).

**`material_management/templates/material_management/spr_clearance/clearance_form.html`**
This template combines the SPR Request details (loaded via HTMX) with the clearance form.

```html
{% extends 'core/base.html' %} {# DO NOT include core/base.html content here #}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-3xl font-extrabold text-gray-900 mb-8">SPR - Check / Approve / Authorize</h2>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {# Left Panel: SPR Request Details (replaces original iframe) #}
        <div class="lg:col-span-1 border border-gray-200 rounded-lg shadow-md p-6 bg-white">
            <h3 class="text-xl font-semibold text-gray-800 mb-5 border-b pb-3">SPR Request Details</h3>
            {% if spr_request %}
                <div hx-get="{% url 'spr_request_detail_partial' spr_request.pk %}"
                     hx-trigger="load, refreshSprRequestDetail from:body"
                     hx-target="this"
                     hx-swap="innerHTML">
                    <!-- SPR Request details will be loaded here via HTMX -->
                    <div class="flex items-center justify-center p-4">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mr-3"></div>
                        <p class="text-gray-600">Loading SPR details...</p>
                    </div>
                </div>
            {% else %}
                <div class="text-center p-6 text-gray-600">
                    <p class="mb-2">No SPR Request selected for clearance.</p>
                    <p>Please navigate from an SPR list or provide an SPR ID.</p>
                </div>
            {% endif %}
        </div>

        {# Right Panel: Clearance Action Form #}
        <div class="lg:col-span-1 border border-gray-200 rounded-lg shadow-md p-6 bg-white">
            <h3 class="text-xl font-semibold text-gray-800 mb-5 border-b pb-3">Clearance Action</h3>
            <form hx-post="{% url 'sprclearance_process' pk=spr_request.pk %}" hx-swap="none">
                {% csrf_token %}
                
                {{ form.spr_request }} {# Hidden input for the foreign key #}
                {{ form.spr_request_display }} {# Readonly input for display #}

                <div class="mb-6 mt-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">{{ form.decision.label }}:</label>
                    <div class="flex flex-col space-y-3">
                        {% for radio in form.decision %}
                        <div class="flex items-center">
                            {{ radio.tag }}
                            <label for="{{ radio.id_for_label }}" class="ml-2 text-base font-medium text-gray-800 cursor-pointer">{{ radio.choice_label }}</label>
                        </div>
                        {% endfor %}
                    </div>
                    {% if form.decision.errors %}
                        <p class="text-red-500 text-xs mt-2">{{ form.decision.errors }}</p>
                    {% endif %}
                </div>

                <div class="mb-6">
                    <label for="{{ form.remark.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        {{ form.remark.label }}
                    </label>
                    {{ form.remark }}
                    {% if form.remark.errors %}
                        <p class="text-red-500 text-xs mt-2">{{ form.remark.errors }}</p>
                    {% endif %}
                </div>
                
                <div class="mt-8 flex items-center justify-end space-x-4">
                    <a href="{% url 'sprclearance_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-md transition duration-150 ease-in-out">
                        Cancel
                    </a>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-md shadow-sm transition duration-150 ease-in-out">
                        Proceed
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Example Alpine.js component (if needed for more complex UI state)
    document.addEventListener('alpine:init', () => {
        Alpine.data('clearanceForm', () => ({
            // Add any Alpine state or functions here, e.g., to conditionally show/hide remark field
        }));
    });
</script>
{% endblock %}

```

**`material_management/templates/material_management/spr_clearance/list.html`**
This template provides the entry point for viewing all `SprClearance` records.

```html
{% extends 'core/base.html' %} {# DO NOT include core/base.html content here #}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">SPR Clearances History</h2>
        {# Button to add a new clearance (if applicable, typically clearances are from an SPR list) #}
        {# For this page, typically you'd initiate clearance from an SPR_Request list.
           This button is illustrative for adding a *new* clearance if the PK is known. #}
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm"
            hx-get="{% url 'sprclearance_process' pk=1 %}" {# Example: pk=1, should be dynamic #}
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Process New SPR Clearance
        </button>
    </div>
    
    <div id="sprClearanceTable-container"
         hx-trigger="load, refreshSprClearanceList from:body" {# Loads on page load, refreshes on custom trigger #}
         hx-get="{% url 'sprclearance_table' %}" {# HTMX endpoint for the table partial #}
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg overflow-hidden">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading SPR Clearances...</p>
        </div>
    </div>
    
    {# Modal for form/confirmation, managed by Alpine.js #}
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-auto"
             _="on htmx:afterOnLoad add .is-active to #modal">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# Ensure jQuery and DataTables CDN links are in core/base.html #}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI state
    });

    // Listen for custom HTMX event to refresh DataTables
    document.body.addEventListener('refreshSprClearanceList', function() {
        // HTMX handles swapping, but if you need to re-initialize DataTables after a swap:
        // The _sprclearance_table.html partial already includes the DataTable init script.
        // So, this listener primarily serves to signal that a refresh has happened.
    });
</script>
{% endblock %}

```

**`material_management/templates/material_management/spr_clearance/_sprclearance_table.html`**
This partial template renders only the table content, allowing HTMX to swap it independently.

```html
<div class="overflow-x-auto">
    <table id="sprClearanceTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SPR Request</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Decision</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remark</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cleared By</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cleared Date</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in sprclearances %}
            <tr>
                <td class="py-3 px-6 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-blue-600 hover:text-blue-800">
                    <a href="{% url 'spr_request_detail_partial' obj.spr_request.pk %}"
                       hx-get="{% url 'spr_request_detail_partial' obj.spr_request.pk %}"
                       hx-target="#modalContent"
                       hx-trigger="click"
                       _="on click add .is-active to #modal">
                        {{ obj.spr_request.request_number }}
                    </a>
                </td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.get_decision_display }}</td>
                <td class="py-3 px-6 text-sm text-gray-700">{{ obj.remark|default:"-" }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.cleared_by.username }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-700">{{ obj.cleared_date|date:"Y-m-d H:i" }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="text-yellow-600 hover:text-yellow-900 mr-4"
                        hx-get="{% url 'sprclearance_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="text-red-600 hover:text-red-900"
                        hx-get="{% url 'sprclearance_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-4 px-6 text-center text-gray-500">No SPR clearances found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
$(document).ready(function() {
    // Destroy existing DataTable instance before re-initializing if HTMX replaces content
    if ($.fn.DataTable.isDataTable('#sprClearanceTable')) {
        $('#sprClearanceTable').DataTable().destroy();
    }
    $('#sprClearanceTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "responsive": true,
        "autoWidth": false
    });
});
</script>
```

**`material_management/templates/material_management/spr_clearance/form.html`**
This is a generic partial form for creating/updating `SprClearance` records when accessed via modals (e.g., from the list view).

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5 border-b pb-3">{{ form.instance.pk|yesno:'Edit,Add' }} SPR Clearance</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-4">
            {{ form.spr_request_display }} {# Display field for SPR Request #}
            {{ form.spr_request }} {# Hidden input for the foreign key #}

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">{{ form.decision.label }}:</label>
                <div class="flex flex-col space-y-2">
                    {% for radio in form.decision %}
                    <div class="flex items-center">
                        {{ radio.tag }}
                        <label for="{{ radio.id_for_label }}" class="ml-2 text-sm font-medium text-gray-700 cursor-pointer">{{ radio.choice_label }}</label>
                    </div>
                    {% endfor %}
                </div>
                {% if form.decision.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.decision.errors }}</p>
                {% endif %}
            </div>

            <div class="mb-4">
                <label for="{{ form.remark.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.remark.label }}
                </label>
                {{ form.remark }}
                {% if form.remark.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                Save
            </button>
        </div>
    </form>
</div>
```

**`material_management/templates/material_management/spr_clearance/confirm_delete.html`**
Partial template for delete confirmation in a modal.

```html
<div class="p-6 text-center">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the clearance record for SPR "{{ object.spr_request.request_number }}" ({{ object.decision }})?</p>
    <form hx-post="{% url 'sprclearance_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-center space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-md shadow-sm"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-5 rounded-md shadow-sm">
                Delete
            </button>
        </div>
    </form>
</div>
```

**`material_management/templates/material_management/spr_request/detail.html`**
Partial template to display SPR Request details, replacing the `iframe` content.

```html
<dl class="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-3">
    <div class="sm:col-span-1">
        <dt class="text-sm font-medium text-gray-500">Request Number</dt>
        <dd class="mt-1 text-base text-gray-900 font-semibold">{{ spr_request.request_number }}</dd>
    </div>
    <div class="sm:col-span-1">
        <dt class="text-sm font-medium text-gray-500">Status</dt>
        <dd class="mt-1 text-base text-gray-900">{{ spr_request.status }}</dd>
    </div>
    <div class="sm:col-span-1">
        <dt class="text-sm font-medium text-gray-500">Request Date</dt>
        <dd class="mt-1 text-base text-gray-900">{{ spr_request.request_date|date:"Y-m-d H:i" }}</dd>
    </div>
    {# Add more SPR fields as needed based on your tblSprRequest schema #}
    <div class="sm:col-span-2">
        <dt class="text-sm font-medium text-gray-500">Description</dt>
        <dd class="mt-1 text-base text-gray-900">
            {% if spr_request.description %}{{ spr_request.description }}{% else %}N/A{% endif %}
        </dd>
    </div>
</dl>

```

#### 4.5 URLs (`material_management/urls.py`)

This file defines the URL patterns for your Django application, linking URLs to the respective views.

```python
from django.urls import path
from .views import (
    SprClearanceFormView, 
    SprClearanceListView, 
    SprClearanceUpdateView, 
    SprClearanceDeleteView, 
    SprClearanceTablePartialView,
    SprRequestDetailView # For the partial SPR details
)

urlpatterns = [
    # Main SPR Clearance Form (replaces original ASPX)
    # The 'pk' represents the primary key of the SPR_Request being cleared.
    path('sprclearance/process/<int:pk>/', SprClearanceFormView.as_view(), name='sprclearance_process'),
    
    # URL for a new clearance form without an initial SPR_ID (if needed for direct access)
    # path('sprclearance/process/', SprClearanceFormView.as_view(), name='sprclearance_process_new'),

    # List/CRUD for historical SPR Clearance records
    path('sprclearance/list/', SprClearanceListView.as_view(), name='sprclearance_list'),
    path('sprclearance/table/', SprClearanceTablePartialView.as_view(), name='sprclearance_table'), # HTMX endpoint for table
    path('sprclearance/edit/<int:pk>/', SprClearanceUpdateView.as_view(), name='sprclearance_edit'),
    path('sprclearance/delete/<int:pk>/', SprClearanceDeleteView.as_view(), name='sprclearance_delete'),

    # Partial view for SPR Request details (used by HTMX where iframe was in ASP.NET)
    path('spr_request/<int:pk>/detail/partial/', SprRequestDetailView.as_view(), name='spr_request_detail_partial'),
]

```

#### 4.6 Tests (`material_management/tests.py`)

Comprehensive tests for models, forms, and views ensure the quality and correctness of the migration.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from .models import SprRequest, SprClearance
from .forms import SprClearanceForm
import datetime

class SprRequestModelTest(TestCase):
    """Tests for the SprRequest model."""

    @classmethod
    def setUpTestData(cls):
        # Create a test SPR Request instance
        cls.spr_request = SprRequest.objects.create(
            request_number='REQ-TEST-001',
            status='Pending',
            description='Test SPR description'
        )
  
    def test_spr_request_creation(self):
        """Verify SPR Request object is created correctly."""
        spr_req = SprRequest.objects.get(pk=self.spr_request.pk)
        self.assertEqual(spr_req.request_number, 'REQ-TEST-001')
        self.assertEqual(spr_req.status, 'Pending')
        self.assertEqual(spr_req.description, 'Test SPR description')
        self.assertIsNotNone(spr_req.request_date)

    def test_update_status_method(self):
        """Test the update_status method of SprRequest."""
        spr_req = SprRequest.objects.get(pk=self.spr_request.pk)
        spr_req.update_status('Approved')
        spr_req.refresh_from_db() # Reload from DB to get latest status
        self.assertEqual(spr_req.status, 'Approved')

        # Test invalid status update
        with self.assertRaises(ValueError):
            spr_req.update_status('InvalidStatus')

    def test_str_representation(self):
        """Test the __str__ method of SprRequest."""
        spr_req = SprRequest.objects.get(pk=self.spr_request.pk)
        self.assertEqual(str(spr_req), 'REQ-TEST-001')


class SprClearanceModelTest(TestCase):
    """Tests for the SprClearance model."""

    @classmethod
    def setUpTestData(cls):
        # Create a test user and SPR Request for clearances
        cls.user = User.objects.create_user(username='clearing_user', password='password123')
        cls.spr_request = SprRequest.objects.create(request_number='REQ-TEST-002', status='Pending')

        # Create a test SprClearance instance
        cls.clearance = SprClearance.objects.create(
            spr_request=cls.spr_request,
            decision='Approved',
            remark='Initial approval for test SPR.',
            cleared_by=cls.user
        )
  
    def test_spr_clearance_creation(self):
        """Verify SprClearance object is created correctly."""
        clearance_obj = SprClearance.objects.get(pk=self.clearance.pk)
        self.assertEqual(clearance_obj.spr_request.request_number, 'REQ-TEST-002')
        self.assertEqual(clearance_obj.decision, 'Approved')
        self.assertEqual(clearance_obj.remark, 'Initial approval for test SPR.')
        self.assertEqual(clearance_obj.cleared_by.username, 'clearing_user')
        self.assertIsNotNone(clearance_obj.cleared_date)

    def test_process_clearance_approved(self):
        """Test the process_clearance method for an 'Approved' decision."""
        new_spr_req = SprRequest.objects.create(request_number='REQ-PROCESS-001', status='Pending')
        new_clearance = SprClearance.objects.create(
            spr_request=new_spr_req,
            decision='Approved',
            remark='Approved via process_clearance test.',
            cleared_by=self.user
        )
        new_clearance.process_clearance()
        new_spr_req.refresh_from_db() # Reload SPR status from DB
        self.assertEqual(new_spr_req.status, 'Approved')

    def test_process_clearance_rejected(self):
        """Test the process_clearance method for a 'Rejected' decision."""
        new_spr_req = SprRequest.objects.create(request_number='REQ-PROCESS-002', status='Pending')
        new_clearance = SprClearance.objects.create(
            spr_request=new_spr_req,
            decision='Rejected',
            remark='Rejected via process_clearance test.',
            cleared_by=self.user
        )
        new_clearance.process_clearance()
        new_spr_req.refresh_from_db() # Reload SPR status from DB
        self.assertEqual(new_spr_req.status, 'Rejected')

    def test_str_representation(self):
        """Test the __str__ method of SprClearance."""
        clearance_obj = SprClearance.objects.get(pk=self.clearance.pk)
        expected_str = f"Clearance for {clearance_obj.spr_request.request_number} ({clearance_obj.decision}) by {clearance_obj.cleared_by.username}"
        self.assertEqual(str(clearance_obj), expected_str)

class SprClearanceFormTest(TestCase):
    """Tests for the SprClearanceForm."""

    @classmethod
    def setUpTestData(cls):
        cls.user = User.objects.create_user(username='form_user', password='password123')
        cls.spr_request = SprRequest.objects.create(request_number='REQ-FORM-001', status='Pending')

    def test_form_valid_approved(self):
        """Test form is valid when decision is Approved and remark is optional."""
        data = {
            'spr_request': self.spr_request.pk,
            'decision': 'Approved',
            'remark': 'Optional remark.',
        }
        form = SprClearanceForm(data=data, spr_obj=self.spr_request)
        self.assertTrue(form.is_valid(), form.errors)
        self.assertEqual(form.cleaned_data['remark'], 'Optional remark.')

    def test_form_valid_rejected_with_remark(self):
        """Test form is valid when decision is Rejected and remark is provided."""
        data = {
            'spr_request': self.spr_request.pk,
            'decision': 'Rejected',
            'remark': 'Reason for rejection.',
        }
        form = SprClearanceForm(data=data, spr_obj=self.spr_request)
        self.assertTrue(form.is_valid(), form.errors)
        self.assertEqual(form.cleaned_data['remark'], 'Reason for rejection.')

    def test_form_invalid_rejected_no_remark(self):
        """Test form is invalid when decision is Rejected but no remark is provided."""
        data = {
            'spr_request': self.spr_request.pk,
            'decision': 'Rejected',
            'remark': '',
        }
        form = SprClearanceForm(data=data, spr_obj=self.spr_request)
        self.assertFalse(form.is_valid())
        self.assertIn('remark', form.errors)
        self.assertEqual(form.errors['remark'], ['Remarks are required when the decision is "Rejected".'])

    def test_form_initialization_with_spr_obj(self):
        """Test form initializes spr_request_display and hidden spr_request correctly."""
        form = SprClearanceForm(spr_obj=self.spr_request)
        self.assertEqual(form.fields['spr_request_display'].initial, self.spr_request.request_number)
        self.assertEqual(form.fields['spr_request'].initial, self.spr_request.pk)


class SprClearanceViewsTest(TestCase):
    """Integration tests for SprClearance views."""

    @classmethod
    def setUpTestData(cls):
        cls.user = User.objects.create_user(username='test_user', password='password123')
        cls.spr_request_1 = SprRequest.objects.create(request_number='REQ-VIEW-001', status='Pending')
        cls.spr_request_2 = SprRequest.objects.create(request_number='REQ-VIEW-002', status='Pending')
        cls.clearance_approved = SprClearance.objects.create(
            spr_request=cls.spr_request_1,
            decision='Approved',
            remark='Initial approval by setup.',
            cleared_by=cls.user
        )

    def setUp(self):
        self.client = Client()
        # Log in the user for all tests requiring authentication
        self.client.login(username='test_user', password='password123')

    def test_spr_clearance_form_view_get(self):
        """Test GET request to the clearance form view."""
        response = self.client.get(reverse('sprclearance_process', args=[self.spr_request_2.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spr_clearance/clearance_form.html')
        self.assertIn('form', response.context)
        self.assertIn('spr_request', response.context)
        self.assertEqual(response.context['spr_request'], self.spr_request_2)
        self.assertContains(response, self.spr_request_2.request_number)

    def test_spr_clearance_form_view_post_approved(self):
        """Test POST request for an 'Approved' clearance."""
        initial_spr_status = self.spr_request_2.status
        data = {
            'spr_request': self.spr_request_2.pk,
            'decision': 'Approved',
            'remark': 'Approved in view test.',
        }
        response = self.client.post(reverse('sprclearance_process', args=[self.spr_request_2.pk]), data)
        self.assertEqual(response.status_code, 302) # Redirect on success
        self.spr_request_2.refresh_from_db()
        self.assertEqual(self.spr_request_2.status, 'Approved') # Verify SPR status updated
        self.assertTrue(SprClearance.objects.filter(spr_request=self.spr_request_2, decision='Approved', remark='Approved in view test.').exists())
        self.assertRedirects(response, reverse('sprclearance_list'))

    def test_spr_clearance_form_view_post_rejected_no_remark_validation(self):
        """Test POST request for 'Rejected' clearance without remark (should fail validation)."""
        data = {
            'spr_request': self.spr_request_2.pk,
            'decision': 'Rejected',
            'remark': '', # Missing remark
        }
        response = self.client.post(reverse('sprclearance_process', args=[self.spr_request_2.pk]), data)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'material_management/spr_clearance/clearance_form.html')
        self.assertFormError(response, 'form', 'remark', 'Remarks are required when the decision is "Rejected".')
        self.spr_request_2.refresh_from_db()
        self.assertEqual(self.spr_request_2.status, 'Pending') # Status should not have changed

    def test_spr_clearance_form_view_post_rejected_with_remark(self):
        """Test POST request for 'Rejected' clearance with remark."""
        data = {
            'spr_request': self.spr_request_2.pk,
            'decision': 'Rejected',
            'remark': 'Needs significant rework.',
        }
        response = self.client.post(reverse('sprclearance_process', args=[self.spr_request_2.pk]), data)
        self.assertEqual(response.status_code, 302)
        self.spr_request_2.refresh_from_db()
        self.assertEqual(self.spr_request_2.status, 'Rejected')
        self.assertTrue(SprClearance.objects.filter(spr_request=self.spr_request_2, decision='Rejected', remark='Needs significant rework.').exists())

    def test_spr_clearance_list_view(self):
        """Test GET request to the SPR Clearance list view."""
        response = self.client.get(reverse('sprclearance_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spr_clearance/list.html')
        self.assertIn('sprclearances', response.context)
        self.assertContains(response, self.clearance_approved.spr_request.request_number)
        self.assertContains(response, self.clearance_approved.cleared_by.username)

    def test_spr_clearance_table_partial_view_htmx(self):
        """Test HTMX request to the table partial view."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('sprclearance_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spr_clearance/_sprclearance_table.html')
        self.assertIn('sprclearances', response.context)
        self.assertContains(response, self.clearance_approved.spr_request.request_number)

    def test_spr_clearance_update_view_get(self):
        """Test GET request to the update view."""
        response = self.client.get(reverse('sprclearance_edit', args=[self.clearance_approved.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spr_clearance/form.html')
        self.assertEqual(response.context['object'], self.clearance_approved)

    def test_spr_clearance_update_view_post(self):
        """Test POST request to update an existing clearance."""
        data = {
            'spr_request': self.clearance_approved.spr_request.pk,
            'decision': 'Rejected',
            'remark': 'Updated remark for re-evaluation.',
        }
        response = self.client.post(reverse('sprclearance_edit', args=[self.clearance_approved.pk]), data)
        self.assertEqual(response.status_code, 302)
        self.clearance_approved.refresh_from_db()
        self.assertEqual(self.clearance_approved.decision, 'Rejected')
        self.assertEqual(self.clearance_approved.remark, 'Updated remark for re-evaluation.')
        self.clearance_approved.spr_request.refresh_from_db() # Verify parent SPR status update
        self.assertEqual(self.clearance_approved.spr_request.status, 'Rejected')

    def test_spr_clearance_delete_view_get(self):
        """Test GET request to the delete confirmation view."""
        response = self.client.get(reverse('sprclearance_delete', args=[self.clearance_approved.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spr_clearance/confirm_delete.html')
        self.assertEqual(response.context['object'], self.clearance_approved)

    def test_spr_clearance_delete_view_post(self):
        """Test POST request to delete a clearance."""
        clearance_count_before = SprClearance.objects.count()
        response = self.client.post(reverse('sprclearance_delete', args=[self.clearance_approved.pk]))
        self.assertEqual(response.status_code, 302)
        self.assertEqual(SprClearance.objects.count(), clearance_count_before - 1)
        self.assertFalse(SprClearance.objects.filter(pk=self.clearance_approved.pk).exists())
        self.assertRedirects(response, reverse('sprclearance_list'))

    def test_spr_clearance_form_view_htmx_post(self):
        """Test HTMX POST request for clearance submission."""
        data = {
            'spr_request': self.spr_request_2.pk,
            'decision': 'Approved',
            'remark': 'HTMX-driven approval.',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('sprclearance_process', args=[self.spr_request_2.pk]), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX success without content
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshSprClearanceList', response.headers['HX-Trigger'])
        self.assertTrue(SprClearance.objects.filter(spr_request=self.spr_request_2, remark='HTMX-driven approval.').exists())

    def test_spr_request_detail_view(self):
        """Test the partial view for SPR request details (simulating iframe content)."""
        response = self.client.get(reverse('spr_request_detail_partial', args=[self.spr_request_1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spr_request/detail.html')
        self.assertIn('spr_request', response.context)
        self.assertEqual(response.context['spr_request'], self.spr_request_1)
        self.assertContains(response, self.spr_request_1.request_number)
        self.assertContains(response, self.spr_request_1.status)

    def test_spr_clearance_update_view_htmx_post(self):
        """Test HTMX POST request for updating a clearance."""
        data = {
            'spr_request': self.clearance_approved.spr_request.pk,
            'decision': 'Rejected',
            'remark': 'Updated via HTMX.',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('sprclearance_edit', args=[self.clearance_approved.pk]), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX success without content
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshSprClearanceList', response.headers['HX-Trigger'])
        self.clearance_approved.refresh_from_db()
        self.assertEqual(self.clearance_approved.remark, 'Updated via HTMX.')

    def test_spr_clearance_delete_view_htmx_post(self):
        """Test HTMX POST request for deleting a clearance."""
        clearance_count_before = SprClearance.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('sprclearance_delete', args=[self.clearance_approved.pk]), **headers)
        self.assertEqual(response.status_code, 204) # HTMX success without content
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshSprClearanceList', response.headers['HX-Trigger'])
        self.assertEqual(SprClearance.objects.count(), clearance_count_before - 1)

```

### Step 5: HTMX and Alpine.js Integration

The generated Django templates leverage HTMX and Alpine.js to create a dynamic, modern user experience without full page reloads:

*   **HTMX for Data Loading:**
    *   The `spr_clearance/list.html` uses `hx-get="{% url 'sprclearance_table' %}"` with `hx-trigger="load, refreshSprClearanceList from:body"` to load the DataTable content after the page loads and refresh it whenever a custom `refreshSprClearanceList` event is dispatched (e.g., after a successful form submission).
    *   The `spr_clearance/clearance_form.html` uses `hx-get="{% url 'spr_request_detail_partial' spr_request.pk %}"` to dynamically load the SPR Request details, replacing the original `<iframe>` functionality.
    *   Modals for "Add," "Edit," and "Delete" actions are opened by `hx-get` requests targeting `#modalContent` within the main `list.html` structure.

*   **HTMX for Form Submission:**
    *   All form submissions (`hx-post`) are configured with `hx-swap="none"` and the view returns `HttpResponse(status=204, headers={'HX-Trigger': 'refreshSprClearanceList'})`. This design pattern ensures:
        *   No full page reload occurs.
        *   The backend successfully processes the request.
        *   A custom HTMX event (`refreshSprClearanceList`) is triggered on the client, which in turn causes the `sprClearanceTable-container` to re-fetch its content, thereby updating the list view seamlessly.
        *   Django's `messages` framework is used for user feedback (e.g., "SPR Clearance processed successfully.").

*   **Alpine.js for UI State:**
    *   Alpine.js is used to manage the modal's visibility (`_="on click add .is-active to #modal"`). It listens for clicks to open the modal and also handles closing the modal when clicking outside or on a "Cancel" button. This keeps JavaScript concerns minimal and declarative directly in the HTML.

*   **DataTables for List Views:**
    *   The `_sprclearance_table.html` partial includes the JavaScript initialization for DataTables. This provides client-side features like searching, sorting, and pagination for the list of SPR clearances, significantly improving user experience. The DataTables initialization is wrapped in `$(document).ready(function() { ... });` and includes a `destroy()` call to handle cases where HTMX replaces the table content, ensuring correct re-initialization.

### Final Notes

This modernization plan transforms the basic ASP.NET clearance page into a highly dynamic and maintainable Django application component.

*   **Placeholders:** Replace `[APP_NAME]`, `[MODEL_NAME_LOWER]`, etc., with your specific application and model names. We've used `material_management`, `SprClearance`, and `SprRequest` as inferred from your ASP.NET code.
*   **DRY Templates:** The use of partial templates (`_sprclearance_table.html`, `form.html`, `confirm_delete.html`, `detail.html`) promotes code reusability.
*   **Fat Models, Thin Views:** Business logic (like `process_clearance` for updating SPR status) is encapsulated within the `SprClearance` model, keeping the Django views concise and focused on request handling.
*   **Comprehensive Tests:** The included tests cover model functionality, form validation, and view interactions (including HTMX requests), ensuring high code quality and confidence in the migration.
*   **Business Value:** This modernized approach provides:
    *   **Improved User Experience:** Faster, more interactive interface with no full page reloads.
    *   **Reduced Development Cost:** Automated migration strategies and adherence to modern Django/HTMX patterns reduce manual coding effort and maintenance.
    *   **Enhanced Maintainability:** Clean separation of concerns (models for logic, forms for validation, views for request handling, templates for presentation) makes the codebase easier to understand and extend.
    *   **Scalability:** Django's robust framework combined with efficient frontend techniques ensures the application can grow with your business needs.