## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

The ASP.NET code interacts with several tables. Based on the `MP_Tree1` and `FillFIN` functions, and various SQL queries, the primary entities involved in this form are:

*   **`tblDG_Item_Master`**: Contains master data for items.
    *   `Id` (PK)
    *   `ItemCode`
    *   `ManfDesc` (Description)
    *   `UOMBasic` (FK to `Unit_Master`)
    *   `FileName` (for drawing/image)
    *   `FileData` (Binary data for drawing/image)
    *   `ContentType` (MIME type for drawing/image)
    *   `AttName` (for specification)
    *   `AttData` (Binary data for specification)
    *   `AttContentType` (MIME type for specification)
*   **`tblMM_PLN_PR_Temp`**: A temporary table for planning Purchase Requisition details before final generation.
    *   `Id` (PK)
    *   `CompId` (Company ID, FK to `Company` model)
    *   `SessionId` (User session ID, maps to Django `request.user.username` or similar)
    *   `ItemId` (FK to `tblDG_Item_Master`)
    *   `SupplierId` (FK to `tblMM_Supplier_master`)
    *   `Qty`
    *   `Rate`
    *   `DelDate` (Delivery Date)
    *   `Discount`
*   **`tblMM_Supplier_master`**: Master data for suppliers.
    *   `SupplierId` (PK)
    *   `SupplierName`
    *   `CompId` (FK to `Company` model)
*   **`tblMM_Rate_Register`**: Stores rates and discounts for items from suppliers.
    *   `Id` (PK)
    *   `ItemId` (FK to `tblDG_Item_Master`)
    *   `CompId` (FK to `Company` model)
    *   `Rate`
    *   `Discount`
    *   `Flag` (Indicator for preferred rate, e.g., 1 for active)
*   **`tblMM_RateLockUnLock_Master`**: Manages rate locking.
    *   `ItemId` (FK to `tblDG_Item_Master`)
    *   `CompId` (FK to `Company` model)
    *   `LockUnlock` (1 for locked, 0 for unlocked)
    *   `Type` (Specific lock type, e.g., 1 for rate lock)
*   **`SD_Cust_WorkOrder_Master`**: Work Order master data.
    *   `WONo` (PK/Unique Identifier)
    *   `BoughtoutMaterialDate` (Relevant date for material procurement)
    *   `CompId` (FK to `Company` model)
    *   `FinYearId` (Financial Year ID, FK to `FinancialYear` model)
*   **`tblMM_PR_Master`**: Master table for generated Purchase Requisitions.
    *   `Id` (PK)
    *   `SysDate` (System Date)
    *   `SysTime` (System Time)
    *   `CompId` (FK to `Company` model)
    *   `SessionId` (User session ID)
    *   `FinYearId` (FK to `FinancialYear` model)
    *   `WONo` (Work Order Number)
    *   `PRNo` (Purchase Requisition Number, unique within company/fin year)
*   **`tblMM_PR_Details`**: Detail table for generated Purchase Requisitions.
    *   `Id` (PK)
    *   `MId` (FK to `tblMM_PR_Master`)
    *   `PRNo` (Duplicate of master PRNo, for convenience)
    *   `ItemId` (FK to `tblDG_Item_Master`)
    *   `Qty`
    *   `SupplierId` (FK to `tblMM_Supplier_master`)
    *   `Rate`
    *   `AHId` (Account Head ID - assumed FK to a dummy `AccountHead` model)
    *   `DelDate` (Delivery Date)
    *   `Discount`
*   **Aggregated Data Sources:**
    *   `tblDG_BOM_Master`: Bill of Material data, used in `MP_Tree1` for `BOMQty` calculation.
    *   `tblInv_WIS_Details` & `tblInv_WIS_Master`: For `WISQty` (Work In progress Stock) calculation.
    *   `tblQc_MaterialQuality_Details`, `tblinv_MaterialReceived_Details`, `tblMM_PO_Details`: For `GQNQty` (Goods Quality Note) calculation.
    *   `Unit_Master`: For `UOMBasic`.

    The `MP_Tree1` method effectively creates a "view" of item details with calculated quantities (BOM Qty, PR Qty, WIS Qty, GQN Qty). This will be represented as a `PrItemDetail` model, assuming it can be backed by a complex SQL query or a database view.

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

*   **Read (Display PR Items):**
    *   `MP_Tree1` populates `RadGrid1` with item details, including calculated BOM, PR, WIS, and GQN quantities, and filters for items needing PR.
    *   `FillFIN` populates the nested `GridView5` with existing temporary PR entries (`tblMM_PLN_PR_Temp`) for each item, or an empty row for new input, pre-filling quantities and rates.
*   **Create (Add Temporary PR Item):**
    *   The "Add" (`TempAdd`) `LinkButton` within `RadGrid1` triggers insertion into `tblMM_PLN_PR_Temp` for a specific item. This involves validation of quantity, rate, and delivery date, and checks for duplicate entries and rate acceptability.
*   **Delete (Remove Temporary PR Item):**
    *   The `ImageButton3` (cross.gif) within `GridView5` triggers deletion of an entry from `tblMM_PLN_PR_Temp`.
*   **Create (Generate Final PR):**
    *   The "Generate PR" (`RadButton1`) `Button` triggers the final creation of records in `tblMM_PR_Master` and `tblMM_PR_Details` based on all entries in `tblMM_PLN_PR_Temp` for the current session. It includes checks for total quantities.
*   **Validation Logic:**
    *   Required fields, date format, and numerical inputs are validated (e.g., `RequiredFieldValidator`, `RegularExpressionValidator`).
    *   Complex business logic for rate acceptability and quantity checks (e.g., `(bomQty - FINQty - PRQty - PRTempQty - WisQty + GQnQty) >= 0`) are critical for the `TempAdd` and `Generate PR` actions.
    *   Supplier autocomplete and delivery date calendar pickers are used for input.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

*   **Main Display (Equivalent to `RadGrid1`):** A list/table displaying items with columns for `Item Code`, `Description`, `UOM`, `BOM Qty`, `PR Qty`, `WIS Qty`, `GQN Qty`, and actions. This will be a Django template with DataTables.
*   **Item Actions (`LinkButton` for `viewImg`, `viewSpec`, `TempAdd`):**
    *   `viewImg`/`viewSpec`: Links/buttons to trigger file downloads for item drawings/specifications.
    *   `TempAdd`: A button per item row that, when clicked, finalizes the input in the nested form for that item and adds it to the temporary PR details.
*   **Nested Input Grid (Equivalent to `GridView5`):** A table within each main item row that allows adding/viewing/deleting temporary PR entries for that item.
    *   `TextBox` for `Supplier`: Autocomplete functionality.
    *   `TextBox` for `Qty`, `Rate`, `Discount`: Numerical inputs.
    *   `TextBox` for `Deliv.Date`: Date picker functionality.
    *   `ImageButton` (cross.gif): Delete button for individual temporary PR entries.
    *   `CheckBox`: Header checkbox for enabling/disabling input rows, potentially triggering auto-fill.
*   **Global Actions (`RadButton1`, `RadButton2`):**
    *   "Generate PR": Button to finalize all temporary PR entries into official PR records.
    *   "Cancel": Button to navigate back to the previous page.
*   **Status/Information Labels:** `lblWono` displays the Work Order Number.

## Step 4: Generate Django Code

The Django application will be named `pr_module`.

## 4.1 Models

Task: Create Django models based on the database schema.

## Instructions:

We will define models for `Company`, `FinancialYear`, `AccountHead`, `UnitMaster`, `ItemMaster`, `Supplier`, `WorkOrder`, `RateRegister`, `RateLockUnlock`, `PrPlanningDetail`, `PrMaster`, and `PrDetail`. `PrItemDetail` will represent the complex view from `MP_Tree1`.

```python
# pr_module/models.py
from django.db import models
from django.utils import timezone
from django.db.models import Sum, F, DecimalField
from django.db.models.functions import Coalesce
from decimal import Decimal

# Dummy models for foreign keys that are not the main focus
class Company(models.Model):
    id = models.IntegerField(primary_key=True, db_column='CompId')
    name = models.CharField(max_length=255)

    class Meta:
        managed = False
        db_table = 'Company_Master' # Placeholder for actual company table
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name

class FinancialYear(models.Model):
    id = models.IntegerField(primary_key=True, db_column='FinYearId')
    year_start = models.DateField()
    year_end = models.DateField()

    class Meta:
        managed = False
        db_table = 'FinYear_Master' # Placeholder
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return f"{self.year_start.year}-{self.year_end.year}"

class AccountHead(models.Model):
    id = models.IntegerField(primary_key=True, db_column='AHId')
    name = models.CharField(max_length=255)

    class Meta:
        managed = False
        db_table = 'tblAH_Master' # Placeholder for actual account head table
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'

    def __str__(self):
        return self.name

class UnitMaster(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    symbol = models.CharField(max_length=50, db_column='Symbol') # UOMBasic in ASP.NET code seems to be Symbol
    description = models.CharField(max_length=255) # Assuming Unit_Master has a description

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit of Measure'
        verbose_name_plural = 'Units of Measure'

    def __str__(self):
        return self.symbol

class ItemMaster(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    item_code = models.CharField(max_length=50, db_column='ItemCode')
    manf_desc = models.CharField(max_length=255, db_column='ManfDesc')
    uom_basic = models.ForeignKey(UnitMaster, on_delete=models.DO_NOTHING, db_column='UOMBasic')
    file_name = models.CharField(max_length=255, db_column='FileName', blank=True, null=True)
    file_data = models.BinaryField(db_column='FileData', blank=True, null=True)
    content_type = models.CharField(max_length=100, db_column='ContentType', blank=True, null=True)
    att_name = models.CharField(max_length=255, db_column='AttName', blank=True, null=True)
    att_data = models.BinaryField(db_column='AttData', blank=True, null=True)
    att_content_type = models.CharField(max_length=100, db_column='AttContentType', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return f"{self.item_code} - {self.manf_desc}"

class Supplier(models.Model):
    id = models.IntegerField(primary_key=True, db_column='SupplierId')
    name = models.CharField(max_length=255, db_column='SupplierName')
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.name

class WorkOrder(models.Model):
    # Assuming WONo is unique or primary key for the WorkOrder
    wo_no = models.CharField(max_length=50, primary_key=True, db_column='WONo')
    boughtout_material_date = models.DateField(db_column='BoughtoutMaterialDate', blank=True, null=True)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId')
    fin_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.wo_no

class RateRegister(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    item = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId')
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId')
    rate = models.DecimalField(max_digits=18, decimal_places=3, db_column='Rate')
    discount = models.DecimalField(max_digits=18, decimal_places=3, db_column='Discount')
    flag = models.IntegerField(db_column='Flag') # 1 for preferred

    class Meta:
        managed = False
        db_table = 'tblMM_Rate_Register'
        verbose_name = 'Rate Register Entry'
        verbose_name_plural = 'Rate Register Entries'

    def __str__(self):
        return f"Item: {self.item.item_code}, Rate: {self.rate}, Discount: {self.discount}"

    def get_discounted_rate(self):
        return self.rate * (Decimal('1') - self.discount / Decimal('100'))

class RateLockUnlock(models.Model):
    # No explicit PK in ASP.NET, assuming composite or auto-generated
    # Creating a dummy PK for Django, if composite, adjust accordingly
    id = models.BigAutoField(primary_key=True) # Assuming no explicit PK, Django needs one
    item = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId')
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId')
    lock_unlock = models.IntegerField(db_column='LockUnlock') # 1 for locked
    type = models.IntegerField(db_column='Type') # 1 for rate lock

    class Meta:
        managed = False
        db_table = 'tblMM_RateLockUnLock_Master'
        verbose_name = 'Rate Lock Unlock'
        verbose_name_plural = 'Rate Locks Unlocks'
        unique_together = (('item', 'company', 'type'),) # Assuming combination is unique

    def __str__(self):
        return f"Item: {self.item.item_code}, Locked: {self.lock_unlock == 1}"

class PrPlanningDetail(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId')
    session_id = models.CharField(max_length=255, db_column='SessionId') # Stores user session ID
    item = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId')
    supplier = models.ForeignKey(Supplier, on_delete=models.DO_NOTHING, db_column='SupplierId')
    qty = models.DecimalField(max_digits=18, decimal_places=3, db_column='Qty')
    rate = models.DecimalField(max_digits=18, decimal_places=3, db_column='Rate')
    delivery_date = models.DateField(db_column='DelDate')
    discount = models.DecimalField(max_digits=18, decimal_places=3, db_column='Discount')

    class Meta:
        managed = False
        db_table = 'tblMM_PLN_PR_Temp'
        verbose_name = 'PR Planning Detail'
        verbose_name_plural = 'PR Planning Details'
        # Unique constraint based on ASP.NET's `sqlCheck`
        unique_together = (('company', 'supplier', 'delivery_date', 'item', 'session_id'),)

    def __str__(self):
        return f"Temp PR for {self.item.item_code} by {self.supplier.name}"

    def get_discounted_rate(self):
        """Calculates the effective rate after discount."""
        return self.rate * (Decimal('1') - self.discount / Decimal('100'))

    @classmethod
    def get_pr_temp_qty_for_item(cls, item_id, session_id, company_id):
        """Calculates total quantity for an item in the temporary PR table."""
        return cls.objects.filter(
            item_id=item_id, session_id=session_id, company_id=company_id
        ).aggregate(
            total_qty=Coalesce(Sum('qty'), Decimal('0.000'))
        )['total_qty']

    def is_rate_acceptable(self, current_company_id):
        """
        Implements the complex rate validation logic from ASP.NET `TempAdd`.
        This method should be called on the model instance (or before saving).
        """
        new_disc_rate = self.get_discounted_rate()

        # Try to find a preferred rate (Flag=1)
        preferred_rate_entry = RateRegister.objects.filter(
            item=self.item, company_id=current_company_id, flag=1
        ).annotate(
            dis_rate=F('rate') * (Decimal('1') - F('discount') / Decimal('100'))
        ).order_by('dis_rate').first()

        if preferred_rate_entry:
            best_rate = preferred_rate_entry.dis_rate
        else:
            # If no preferred, find the minimum discounted rate
            min_rate_entry = RateRegister.objects.filter(
                item=self.item, company_id=current_company_id
            ).annotate(
                dis_rate=F('rate') * (Decimal('1') - F('discount') / Decimal('100'))
            ).order_by('dis_rate').first()
            best_rate = min_rate_entry.dis_rate if min_rate_entry else Decimal('0.000')

        # Check if the entered discounted rate is acceptable based on business rules
        # (rate - DiscRate) >= 0 OR rate is locked.
        if best_rate > Decimal('0'): # If a historical rate exists
            rate_difference = (best_rate - new_disc_rate).quantize(Decimal('0.00'))
            if rate_difference >= Decimal('0'): # New rate is less than or equal to best historical
                return True
            else: # New rate is higher than best historical, check for lock
                is_rate_locked = RateLockUnlock.objects.filter(
                    item=self.item, company_id=current_company_id, lock_unlock=1, type=1
                ).exists()
                return is_rate_locked # Acceptable only if locked
        else: # No historical rate, any positive discounted rate is acceptable
            return new_disc_rate > Decimal('0')

class PrMaster(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id') # Assumed primary key
    sys_date = models.DateField(db_column='SysDate', default=timezone.localdate)
    sys_time = models.TimeField(db_column='SysTime', default=timezone.localtime)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId')
    session_id = models.CharField(max_length=255, db_column='SessionId')
    fin_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId')
    wo_no = models.CharField(max_length=50, db_column='WONo') # Can be FK to WorkOrder if WONo is PK
    pr_no = models.CharField(max_length=50, db_column='PRNo', unique=True) # Unique per company/fin year

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Master'
        verbose_name = 'PR Master'
        verbose_name_plural = 'PR Masters'

    def __str__(self):
        return self.pr_no

class PrDetail(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id') # Assumed primary key
    master = models.ForeignKey(PrMaster, on_delete=models.DO_NOTHING, db_column='MId')
    pr_no = models.CharField(max_length=50, db_column='PRNo') # Redundant, but kept for db_table mapping
    item = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId')
    qty = models.DecimalField(max_digits=18, decimal_places=3, db_column='Qty')
    supplier = models.ForeignKey(Supplier, on_delete=models.DO_NOTHING, db_column='SupplierId')
    rate = models.DecimalField(max_digits=18, decimal_places=3, db_column='Rate')
    account_head = models.ForeignKey(AccountHead, on_delete=models.DO_NOTHING, db_column='AHId') # Assuming AHId=28 is a valid FK
    delivery_date = models.DateField(db_column='DelDate')
    discount = models.DecimalField(max_digits=18, decimal_places=3, db_column='Discount')

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Details'
        verbose_name = 'PR Detail'
        verbose_name_plural = 'PR Details'

    def __str__(self):
        return f"PR Detail {self.pr_no} - {self.item.item_code}"

class PrItemDetail(models.Model):
    """
    Represents the aggregated data displayed in RadGrid1.
    This model mirrors the output of the complex SQL query in MP_Tree1.
    It's treated as a read-only view or result of a complex query.
    For demonstration, we assume a table or view with these columns exists.
    """
    item_id = models.IntegerField(primary_key=True, db_column='ItemId') # ItemId is the unique identifier for the row
    item_code = models.CharField(max_length=50, db_column='ItemCode')
    description = models.CharField(max_length=255, db_column='ManfDesc')
    uom_basic_symbol = models.CharField(max_length=50, db_column='UOMBasic') # Symbol from Unit_Master
    bom_qty = models.DecimalField(max_digits=18, decimal_places=3, db_column='BOMQty')
    pr_qty = models.DecimalField(max_digits=18, decimal_places=3, db_column='PRQty')
    wis_qty = models.DecimalField(max_digits=18, decimal_places=3, db_column='WISQty')
    gqn_qty = models.DecimalField(max_digits=18, decimal_places=3, db_column='GQNQty')
    file_name = models.CharField(max_length=255, db_column='FileName', blank=True, null=True)
    att_name = models.CharField(max_length=255, db_column='AttName', blank=True, null=True)

    class Meta:
        managed = False
        # This table name is a placeholder. In a real scenario, this would be a database view
        # or a model that uses a custom manager to perform the complex query.
        db_table = 'pr_item_details_view'
        verbose_name = 'PR Item Detail'
        verbose_name_plural = 'PR Item Details'

    def __str__(self):
        return f"{self.item_code} - {self.description}"

    def get_remaining_qty(self, session_id, company_id):
        """
        Calculates the remaining quantity for PR, mirroring ASP.NET logic:
        (BOMQty - PRQty - PRTempQty - WISQty + GQNQty)
        """
        pr_temp_qty = PrPlanningDetail.get_pr_temp_qty_for_item(
            item_id=self.item_id, session_id=session_id, company_id=company_id
        )
        return (self.bom_qty - self.pr_qty - pr_temp_qty - self.wis_qty + self.gqn_qty).quantize(Decimal('0.00'))

    def get_best_rate_info(self, company_id):
        """
        Retrieves the best rate and discount for the item, mimicking FillFIN logic.
        Prioritizes Flag=1 rates, then lowest discounted rate.
        """
        # Prioritize Flag=1 rates
        preferred_rate = RateRegister.objects.filter(
            item_id=self.item_id, company_id=company_id, flag=1
        ).order_by('rate').first() # Order by rate just in case multiple flag=1

        if preferred_rate:
            return {'rate': preferred_rate.rate, 'discount': preferred_rate.discount}
        else:
            # Get the lowest discounted rate if no preferred
            lowest_discounted_rate = RateRegister.objects.filter(
                item_id=self.item_id, company_id=company_id
            ).annotate(
                dis_rate=F('rate') * (Decimal('1') - F('discount') / Decimal('100'))
            ).order_by('dis_rate').first()

            if lowest_discounted_rate:
                return {'rate': lowest_discounted_rate.rate, 'discount': lowest_discounted_rate.discount}
        return {'rate': Decimal('0.00'), 'discount': Decimal('0.00')} # Default if no rates found

```

## 4.2 Forms

Task: Define a Django form for user input.

## Instructions:

We need a form for `PrPlanningDetail`. The supplier field will use custom validation to extract `SupplierId` from a combined string.

```python
# pr_module/forms.py
from django import forms
from .models import PrPlanningDetail, Supplier
from decimal import Decimal
import re

class PrPlanningDetailForm(forms.ModelForm):
    # supplier_name is a CharField for the autocomplete input, not directly mapped to DB column
    supplier_name = forms.CharField(
        max_length=255,
        required=True,
        label="Supplier",
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'hx-get': '/pr_module/suppliers/autocomplete/', 'hx-trigger': 'keyup changed delay:500ms', 'hx-target': '#supplier-suggestions', 'autocomplete': 'off'})
    )

    class Meta:
        model = PrPlanningDetail
        fields = ['qty', 'rate', 'discount', 'delivery_date'] # supplier and item are set dynamically
        widgets = {
            'qty': forms.NumberInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.001'}),
            'rate': forms.NumberInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01'}),
            'discount': forms.NumberInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01'}),
            'delivery_date': forms.DateInput(attrs={'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
        }
        labels = {
            'qty': 'Qty',
            'rate': 'Rate',
            'discount': 'Discount',
            'delivery_date': 'Deliv.Date',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # If instance exists (editing), set initial supplier_name
        if self.instance and self.instance.pk:
            self.fields['supplier_name'].initial = f"{self.instance.supplier.name} [{self.instance.supplier.id}]"

    def clean_supplier_name(self):
        """
        Validates supplier name and extracts supplier ID.
        Mimics ASP.NET fun.getCode where "SupplierName [SupplierId]" is used.
        """
        supplier_input = self.cleaned_data['supplier_name']
        match = re.search(r'\[(\d+)\]$', supplier_input)
        if match:
            supplier_id = match.group(1)
            try:
                supplier = Supplier.objects.get(id=supplier_id)
                self.cleaned_data['supplier'] = supplier # Add supplier object to cleaned_data
                return supplier_input
            except Supplier.DoesNotExist:
                raise forms.ValidationError("Invalid supplier ID in input.")
        raise forms.ValidationError("Please select a supplier from the autocomplete list or enter in 'Name [ID]' format.")

    def clean_qty(self):
        qty = self.cleaned_data['qty']
        if qty <= 0:
            raise forms.ValidationError("Quantity must be greater than zero.")
        return qty

    def clean_rate(self):
        rate = self.cleaned_data['rate']
        if rate <= 0:
            raise forms.ValidationError("Rate must be greater than zero.")
        return rate

    def clean_discount(self):
        discount = self.cleaned_data['discount']
        if discount < 0 or discount > 100:
            raise forms.ValidationError("Discount must be between 0 and 100.")
        return discount

    def clean(self):
        """
        Perform cross-field validation, including the complex rate acceptability.
        """
        cleaned_data = super().clean()
        qty = cleaned_data.get('qty')
        rate = cleaned_data.get('rate')
        discount = cleaned_data.get('discount')
        supplier = cleaned_data.get('supplier')

        # Check for empty fields if required in ASP.NET, though ModelForm handles 'required'
        if not all([qty, rate, supplier]):
            raise forms.ValidationError("Please fill all required fields (Qty, Rate, Supplier).")

        # Validate rate acceptability using the model method
        if rate is not None and discount is not None and supplier:
            # Create a temporary PrPlanningDetail instance for validation
            # Use request.user.company and request.session.session_key or similar for session_id and company_id
            # These values will be passed from the view, not directly in the form
            temp_pr_planning = PrPlanningDetail(
                item=self.instance.item if self.instance.pk else None, # Item is set in view for new objects
                qty=qty,
                rate=rate,
                discount=discount,
                supplier=supplier,
                delivery_date=cleaned_data.get('delivery_date', timezone.now().date()), # dummy date for validation
                session_id='dummy_session_id', # Dummy for form validation, actual passed in view
                company_id=1 # Dummy for form validation, actual passed in view
            )
            # This validation assumes the item is available in the form context or can be retrieved.
            # In a real scenario, `company_id` would come from `request.user.company.id` or a session.
            # We'll pass it from the view to the form's `clean` method if needed.
            # For now, we'll assume the view passes item and company to instance before validation or in clean.
            
            # The rate acceptability check is complex and relies on specific item_id and company_id.
            # It's better to perform this check in the view or a service layer
            # after the form is valid, where session/user context is readily available.
            # For now, we'll keep the basic validation here.
            
            # Add a placeholder for rate acceptability validation, to be done in view
            # if not temp_pr_planning.is_rate_acceptable(company_id_from_session):
            #     self.add_error(None, "Entered rate is not acceptable!")

        return cleaned_data

```

## 4.3 Views

Task: Implement CRUD operations using CBVs.

## Instructions:

Views will be thin, primarily handling HTTP requests, calling model methods for business logic, and returning HTMX responses.

```python
# pr_module/views.py
from django.views.generic import ListView, View
from django.shortcuts import render, get_object_or_404
from django.http import HttpResponse, JsonResponse
from django.contrib import messages
from django.urls import reverse_lazy
from django.db import transaction # For Generate PR
from django.db.models import Max
from django.utils.dateformat import format
import datetime # For date parsing

from .models import (
    PrItemDetail, PrPlanningDetail, Supplier, PrMaster, PrDetail,
    Company, FinancialYear, WorkOrder, ItemMaster, RateRegister,
    AccountHead # Assuming a fixed AHId=28 for PR Details
)
from .forms import PrPlanningDetailForm

class PrItemDetailListView(ListView):
    """
    Displays the list of PR Items (equivalent to RadGrid1).
    The complex data aggregation (BOM, PR, WIS, GQN quantities)
    is assumed to be handled by the PrItemDetail model's underlying DB view or manager.
    """
    model = PrItemDetail
    template_name = 'pr_module/pritemdetail/list.html'
    context_object_name = 'pr_item_details'
    paginate_by = 10 # DataTables handles pagination, but for initial load, good practice

    def get_queryset(self):
        # Access session variables (CompanyId, FinancialYearId, WONo)
        company_id = self.request.session.get('compid', 1) # Default to 1 if not in session
        fin_year_id = self.request.session.get('finyear', 2024) # Default to 2024
        wo_no = self.request.GET.get('WONo', 'WO-TEST-001') # Get from query string, default for testing

        # In a real system, MP_Tree1 logic would populate this.
        # For this example, we assume PrItemDetail.objects.all() directly represents
        # the filtered and calculated data of MP_Tree1.
        # If PrItemDetail is a DB View, it would already contain pre-calculated values.
        # If it's a regular table populated by a batch job, same.
        # If it's a dynamic query, it would be complex here:
        # return PrItemDetail.objects.raw("SELECT ... FROM tblDG_BOM_Master ... WHERE WONo=%s AND CompId=%s ...", [wo_no, company_id])
        
        # Dummy queryset for demonstration; in reality, this would be a complex join/aggregation
        # If PrItemDetail represents a DB VIEW, this would be simpler.
        queryset = PrItemDetail.objects.all()
        # Add filtering by WONo, CompanyId, FinYearId if these are part of the view's filter
        # For simplicity, we'll assume the view implicitly handles WONo from context or is already filtered.
        
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['wono'] = self.request.GET.get('WONo', 'WO-TEST-001')
        # Add company_id and fin_year_id to context for use in templates/forms
        context['company_id'] = self.request.session.get('compid', 1)
        context['fin_year_id'] = self.request.session.get('finyear', 2024)
        return context

class PrPlanningDetailTablePartialView(View):
    """
    Returns the partial HTML for the nested PR Planning Detail table for a specific item.
    This corresponds to the inner GridView5.
    """
    def get(self, request, item_id, *args, **kwargs):
        session_id = request.session.session_key # Or request.user.username
        company_id = request.session.get('compid', 1)
        wono = request.GET.get('WONo', 'WO-TEST-001') # Passed from parent request

        pr_item_detail = get_object_or_404(PrItemDetail, item_id=item_id)
        pr_planning_details = PrPlanningDetail.objects.filter(
            item_id=item_id, session_id=session_id, company_id=company_id
        )

        remaining_qty = pr_item_detail.get_remaining_qty(session_id, company_id)
        
        # Mimic FillFIN logic: add an empty row if remaining_qty > 0 or no existing entries
        show_add_row = True
        if pr_planning_details.exists() and remaining_qty <= 0:
            show_add_row = False # No need to add more if BOM qty is met

        initial_data = {}
        if show_add_row:
            best_rate_info = pr_item_detail.get_best_rate_info(company_id)
            wo_mfg_date_str = WorkOrder.objects.filter(wo_no=wono, company_id=company_id, fin_year_id=company_id).values_list('boughtout_material_date', flat=True).first()
            wo_mfg_date = wo_mfg_date_str if wo_mfg_date_str else timezone.localdate()

            initial_data = {
                'qty': remaining_qty,
                'rate': best_rate_info['rate'],
                'discount': best_rate_info['discount'],
                'delivery_date': wo_mfg_date.strftime('%Y-%m-%d'), # YYYY-MM-DD for HTML date input
            }
        
        form = PrPlanningDetailForm(initial=initial_data)

        context = {
            'pr_item_detail': pr_item_detail,
            'pr_planning_details': pr_planning_details,
            'form': form,
            'show_add_row': show_add_row,
            'session_id': session_id,
            'company_id': company_id,
        }
        return render(request, 'pr_module/prplanningdetail/_pr_planning_table.html', context)

class PrPlanningDetailAddUpdateView(View):
    """
    Handles adding/updating a single PrPlanningDetail entry (mimics TempAdd).
    """
    def post(self, request, item_id, *args, **kwargs):
        session_id = request.session.session_key # Or request.user.username
        company_id = request.session.get('compid', 1)
        
        pr_item_detail = get_object_or_404(PrItemDetail, item_id=item_id)
        form = PrPlanningDetailForm(request.POST)

        if form.is_valid():
            supplier = form.cleaned_data['supplier']
            qty = form.cleaned_data['qty']
            rate = form.cleaned_data['rate']
            discount = form.cleaned_data['discount']
            delivery_date = form.cleaned_data['delivery_date']

            # Check for existing (duplicate) entry as per ASP.NET `sqlCheck`
            existing_entry = PrPlanningDetail.objects.filter(
                company_id=company_id,
                supplier=supplier,
                delivery_date=delivery_date,
                item_id=item_id,
                session_id=session_id
            ).first()

            if existing_entry:
                messages.error(request, "A similar entry already exists for this item, supplier, and delivery date.")
                return self.render_error_response(request, item_id, form)

            # Perform complex quantity and rate validation from ASP.NET logic
            current_pr_temp_qty = PrPlanningDetail.get_pr_temp_qty_for_item(item_id, session_id, company_id)
            # Re-calculate remaining_qty with the *new* quantity being added
            remaining_qty_after_add = (pr_item_detail.bom_qty - pr_item_detail.pr_qty - current_pr_temp_qty - qty - pr_item_detail.wis_qty + pr_item_detail.gqn_qty).quantize(Decimal('0.00'))

            if remaining_qty_after_add < Decimal('0'):
                messages.error(request, f"Entered quantity ({qty}) exceeds remaining BOM quantity for this item.")
                return self.render_error_response(request, item_id, form)

            # Create a dummy instance for rate validation
            temp_pr_planning = PrPlanningDetail(
                item=pr_item_detail.item_id, # Use item_id as integer
                qty=qty, rate=rate, discount=discount,
                supplier=supplier, delivery_date=delivery_date,
                session_id=session_id, company_id=company_id
            )
            # Need to create ItemMaster instance for rate_acceptable method
            temp_pr_planning.item = ItemMaster.objects.get(id=item_id)

            if not temp_pr_planning.is_rate_acceptable(company_id):
                 messages.error(request, "Entered rate is not acceptable!")
                 return self.render_error_response(request, item_id, form)

            # All validations pass, save the new entry
            PrPlanningDetail.objects.create(
                company_id=company_id,
                session_id=session_id,
                item_id=item_id,
                supplier=supplier,
                qty=qty,
                rate=rate,
                delivery_date=delivery_date,
                discount=discount
            )
            messages.success(request, 'PR Planning Detail added successfully.')
            return HttpResponse(status=204, headers={'HX-Trigger': f'refreshPrPlanningDetailTable_{item_id}'})
        
        # If form is not valid
        messages.error(request, "Invalid data entry. Please check the form.")
        return self.render_error_response(request, item_id, form)

    def render_error_response(self, request, item_id, form):
        # Re-render the form with errors, potentially including existing details
        session_id = request.session.session_key
        company_id = request.session.get('compid', 1)
        pr_item_detail = get_object_or_404(PrItemDetail, item_id=item_id)
        pr_planning_details = PrPlanningDetail.objects.filter(
            item_id=item_id, session_id=session_id, company_id=company_id
        )
        context = {
            'pr_item_detail': pr_item_detail,
            'pr_planning_details': pr_planning_details,
            'form': form,
            'show_add_row': True, # Always show add row if there's an error on submission
            'session_id': session_id,
            'company_id': company_id,
        }
        return render(request, 'pr_module/prplanningdetail/_pr_planning_table.html', context)


class PrPlanningDetailDeleteView(View):
    """
    Handles deletion of a single PrPlanningDetail entry.
    """
    def post(self, request, pk, *args, **kwargs):
        pr_planning_detail = get_object_or_404(PrPlanningDetail, pk=pk)
        item_id = pr_planning_detail.item.id # Get item_id before deleting
        pr_planning_detail.delete()
        messages.success(request, 'PR Planning Detail deleted successfully.')
        return HttpResponse(status=204, headers={'HX-Trigger': f'refreshPrPlanningDetailTable_{item_id}'})


class SupplierAutocompleteView(View):
    """
    Provides supplier names for autocomplete functionality.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        company_id = request.session.get('compid', 1)
        
        suppliers = Supplier.objects.filter(
            company_id=company_id,
            name__icontains=query
        ).values_list('name', 'id')[:10] # Limit results

        results = [f"{name} [{id}]" for name, id in suppliers]
        return JsonResponse(results, safe=False)

class FileDownloadView(View):
    """
    Handles file downloads for item drawings/specifications.
    Mimics ASP.NET's DownloadFile.aspx.
    """
    def get(self, request, model_name, pk, file_field, filename_field, content_type_field, *args, **kwargs):
        if model_name == 'tblDG_Item_Master':
            model_class = ItemMaster
        else:
            return HttpResponse("Invalid model", status=400)
        
        obj = get_object_or_404(model_class, pk=pk)
        
        file_data = getattr(obj, file_field, None)
        filename = getattr(obj, filename_field, 'download')
        content_type = getattr(obj, content_type_field, 'application/octet-stream')

        if file_data:
            response = HttpResponse(file_data, content_type=content_type)
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            return response
        else:
            return HttpResponse("File not found or empty.", status=404)

class GeneratePrView(View):
    """
    Handles the 'Generate PR' functionality, creating final PR Master and Details records.
    """
    def post(self, request, *args, **kwargs):
        session_id = request.session.session_key
        company_id = request.session.get('compid', 1)
        fin_year_id = request.session.get('finyear', 2024)
        wono = request.POST.get('wono', 'WO-TEST-001') # Get WONo from hidden input or similar

        pr_planning_details = PrPlanningDetail.objects.filter(
            session_id=session_id, company_id=company_id
        )

        if not pr_planning_details.exists():
            messages.error(request, "No PR planning entries found to generate PR.")
            return HttpResponse(status=200, headers={'HX-Refresh': 'true'}) # Refresh page to show messages

        try:
            with transaction.atomic():
                # Validate total quantities per item before generation
                for item_id in pr_planning_details.values_list('item_id', flat=True).distinct():
                    item_detail = get_object_or_404(PrItemDetail, item_id=item_id)
                    total_planned_qty = PrPlanningDetail.get_pr_temp_qty_for_item(item_id, session_id, company_id)
                    remaining_bom_qty = (item_detail.bom_qty - item_detail.pr_qty - item_detail.wis_qty + item_detail.gqn_qty).quantize(Decimal('0.00'))

                    if total_planned_qty > remaining_bom_qty + Decimal('0.001'): # Allow slight floating point variations
                        messages.error(request, f"Total planned quantity ({total_planned_qty}) for item {item_detail.item_code} exceeds remaining BOM quantity ({remaining_bom_qty}).")
                        raise ValueError("Quantity mismatch")

                # Generate new PRNo
                last_pr_master = PrMaster.objects.filter(
                    company_id=company_id, fin_year_id=fin_year_id
                ).order_by('-pr_no').first() # Assuming PRNo is string but sortable numerically if padded
                
                if last_pr_master and last_pr_master.pr_no.isdigit():
                    new_pr_no_int = int(last_pr_master.pr_no) + 1
                else:
                    new_pr_no_int = 1 # Start from 1 if no existing PRs or non-numeric PRNo

                new_pr_no = f"{new_pr_no_int:04d}" # Format as "0001"

                # Create PR Master record
                pr_master = PrMaster.objects.create(
                    company_id=company_id,
                    session_id=session_id,
                    fin_year_id=fin_year_id,
                    wo_no=wono,
                    pr_no=new_pr_no
                )

                # Create PR Details records from temporary entries
                account_head = get_object_or_404(AccountHead, id=28) # Assuming AHId=28 is fixed
                for detail in pr_planning_details:
                    PrDetail.objects.create(
                        master=pr_master,
                        pr_no=new_pr_no, # Redundant but matches ASP.NET
                        item=detail.item,
                        qty=detail.qty,
                        supplier=detail.supplier,
                        rate=detail.rate,
                        account_head=account_head,
                        delivery_date=detail.delivery_date,
                        discount=detail.discount
                    )
                
                # Clear temporary PR entries after successful generation
                pr_planning_details.delete()

                messages.success(request, f"PR '{new_pr_no}' generated successfully.")
                return HttpResponse(status=204, headers={'HX-Redirect': reverse_lazy('pr_new_list_after_generation')}) # Redirect to PR list page

        except ValueError as e:
            # Error message already set by previous checks
            return HttpResponse(status=200, headers={'HX-Refresh': 'true'}) # Refresh page to show error messages
        except Exception as e:
            messages.error(request, f"An unexpected error occurred during PR generation: {e}")
            return HttpResponse(status=200, headers={'HX-Refresh': 'true'})

```

## 4.4 Templates

Task: Create templates for each view.

## Instructions:

List Template (`pr_module/pritemdetail/list.html`):
- Extends `core/base.html`.
- Uses DataTables for the main item list.
- Each item row will have a dynamic section loaded via HTMX for its planning details.
- Includes a general modal for forms.

Table Partial Template (`pr_module/pritemdetail/_pr_item_row.html` - this is the structure for the *dynamic content within each row of the main table*).
Actually, the main table will load its content via HTMX too. So it's `_pr_item_table.html` for the main table content and `_pr_planning_table.html` for the nested content.

```html
<!-- pr_module/pritemdetail/list.html -->
{% extends 'core/base.html' %}

{% block title %}PR - New{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">PR - New (WO No: {{ wono }})</h2>
        <div>
            <form hx-post="{% url 'generate_pr' %}" hx-swap="none" class="inline-block">
                {% csrf_token %}
                <input type="hidden" name="wono" value="{{ wono }}">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                    Generate PR
                </button>
            </form>
            <a href="{% url 'pr_new_list_after_generation' %}" class="bg-gray-400 hover:bg-gray-500 text-white font-bold py-2 px-4 rounded-md shadow-sm ml-2">
                Cancel
            </a>
        </div>
    </div>

    <div id="prItemDetailTable-container"
         hx-trigger="load, refreshPrItemDetailList from:body"
         hx-get="{% url 'pr_item_detail_table_partial' %}?WONo={{ wono }}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading PR Item Details...</p>
        </div>
    </div>
    
    <!-- General Modal Structure for HTMX loaded forms -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center z-50 hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4"
             _="on htmx:afterSwap remove .is-active from #modal end">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/datatables.net@2.0.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/datatables.net-dt@2.0.7/js/dataTables.dataTables.min.js"></script>
<link href="https://cdn.jsdelivr.net/npm/datatables.net-dt@2.0.7/css/dataTables.dataTables.min.css" rel="stylesheet">
<script>
    // Alpine.js setup if needed, but HTMX often handles UI toggles better here
    // Example for modal toggle:
    // document.addEventListener('alpine:init', () => {
    //     Alpine.data('modal', () => ({
    //         isOpen: false,
    //         toggle() {
    //             this.isOpen = !this.isOpen;
    //         }
    //     }));
    // });
    
    // Listen for HTMX afterSwap events to initialize DataTables
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'prItemDetailTable-container') {
            $('#prItemDetailTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "searching": true, // Enable search box
                "ordering": false, // Disable sorting as ASP.NET had AllowSorting="false"
                "paging": true,
                "info": true,
            });
        }
    });

    // Event listener for opening the modal (assuming you pass button click event)
    document.body.addEventListener('click', function(evt) {
        if (evt.target.matches('[hx-target="#modalContent"]')) {
            document.getElementById('modal').classList.add('is-active');
        }
    });
</script>
{% endblock %}
```

Table Partial Template (`pr_module/pritemdetail/_pr_item_table.html`):
This is the content loaded into `prItemDetailTable-container`.

```html
<!-- pr_module/pritemdetail/_pr_item_table.html -->
<div class="overflow-x-auto relative shadow-md sm:rounded-lg">
    <table id="prItemDetailTable" class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
                <th scope="col" class="py-3 px-6">SN</th>
                <th scope="col" class="py-3 px-6">Item Code</th>
                <th scope="col" class="py-3 px-6">Description</th>
                <th scope="col" class="py-3 px-6">UOM</th>
                <th scope="col" class="py-3 px-6">BOM Qty</th>
                <th scope="col" class="py-3 px-6">PR Qty</th>
                <th scope="col" class="py-3 px-6">WIS Qty</th>
                <th scope="col" class="py-3 px-6">GQN Qty</th>
                <th scope="col" class="py-3 px-6">Draw/Img</th>
                <th scope="col" class="py-3 px-6">Spec.</th>
                <th scope="col" class="py-3 px-6">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for item in pr_item_details %}
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                <td class="py-4 px-6 font-medium text-gray-900 whitespace-nowrap dark:text-white">{{ forloop.counter }}</td>
                <td class="py-4 px-6">{{ item.item_code }}</td>
                <td class="py-4 px-6">{{ item.description }}</td>
                <td class="py-4 px-6">{{ item.uom_basic_symbol }}</td>
                <td class="py-4 px-6 text-right">{{ item.bom_qty|floatformat:"3" }}</td>
                <td class="py-4 px-6 text-right">{{ item.pr_qty|floatformat:"3" }}</td>
                <td class="py-4 px-6 text-right">{{ item.wis_qty|floatformat:"3" }}</td>
                <td class="py-4 px-6 text-right">{{ item.gqn_qty|floatformat:"3" }}</td>
                <td class="py-4 px-6 text-center">
                    {% if item.file_name %}
                    <a href="{% url 'file_download' model_name='tblDG_Item_Master' pk=item.item_id file_field='file_data' filename_field='file_name' content_type_field='content_type' %}" class="text-blue-600 hover:underline">View</a>
                    {% else %}
                    -
                    {% endif %}
                </td>
                <td class="py-4 px-6 text-center">
                    {% if item.att_name %}
                    <a href="{% url 'file_download' model_name='tblDG_Item_Master' pk=item.item_id file_field='att_data' filename_field='att_name' content_type_field='att_content_type' %}" class="text-blue-600 hover:underline">View</a>
                    {% else %}
                    -
                    {% endif %}
                </td>
                <td class="py-4 px-6">
                    <button class="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-1 px-3 rounded text-xs"
                            hx-get="{% url 'pr_planning_table_partial' item.item_id %}?WONo={{ wono }}"
                            hx-target="#prPlanningDetailSection_{{ item.item_id }}"
                            hx-trigger="click"
                            hx-swap="innerHTML"
                            onclick="this.closest('tr').nextElementSibling.classList.toggle('hidden');">
                        Add/View Supplier
                    </button>
                </td>
            </tr>
            <!-- Collapsible row for nested PR Planning Details -->
            <tr class="hidden bg-gray-100 border-b">
                <td colspan="11" class="p-4">
                    <div id="prPlanningDetailSection_{{ item.item_id }}" class="bg-white p-4 rounded shadow-inner">
                        <div class="text-center py-4">
                            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-gray-400"></div>
                            <p class="mt-2 text-gray-500">Loading supplier details...</p>
                        </div>
                    </div>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="11" class="py-4 px-6 text-center">No items found for PR.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

```

Nested PR Planning Table Partial (`pr_module/prplanningdetail/_pr_planning_table.html`):
This is the content loaded into `prPlanningDetailSection_{{ item.item_id }}`.

```html
<!-- pr_module/prplanningdetail/_pr_planning_table.html -->
<h4 class="text-md font-semibold text-gray-700 mb-3">Supplier Planning for {{ pr_item_detail.item_code }}</h4>
<div class="overflow-x-auto relative shadow-md sm:rounded-lg mb-4">
    <table class="w-full text-sm text-left text-gray-500">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50">
            <tr>
                <th scope="col" class="py-2 px-4"></th> {# For delete button/checkbox #}
                <th scope="col" class="py-2 px-4">Supplier</th>
                <th scope="col" class="py-2 px-4">Qty</th>
                <th scope="col" class="py-2 px-4">Rate</th>
                <th scope="col" class="py-2 px-4">Discount</th>
                <th scope="col" class="py-2 px-4">Deliv.Date</th>
            </tr>
        </thead>
        <tbody>
            {% for detail in pr_planning_details %}
            <tr class="bg-white border-b hover:bg-gray-50">
                <td class="py-2 px-4">
                    <button hx-post="{% url 'pr_planning_detail_delete' detail.pk %}"
                            hx-confirm="Are you sure you want to delete this planning detail?"
                            hx-swap="none"
                            class="text-red-500 hover:text-red-700 text-sm">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
                    </button>
                </td>
                <td class="py-2 px-4">{{ detail.supplier.name }}</td>
                <td class="py-2 px-4 text-right">{{ detail.qty|floatformat:"3" }}</td>
                <td class="py-2 px-4 text-right">{{ detail.rate|floatformat:"2" }}</td>
                <td class="py-2 px-4 text-right">{{ detail.discount|floatformat:"2" }}</td>
                <td class="py-2 px-4">{{ detail.delivery_date|date:"d-m-Y" }}</td>
            </tr>
            {% endfor %}

            {% if show_add_row %}
            <tr class="bg-white border-b">
                <td class="py-2 px-4"></td> {# Empty cell for spacing #}
                <td colspan="5" class="p-0">
                    <form hx-post="{% url 'pr_planning_detail_add_update' pr_item_detail.item_id %}" hx-swap="none">
                        {% csrf_token %}
                        <input type="hidden" name="session_id" value="{{ session_id }}">
                        <input type="hidden" name="company_id" value="{{ company_id }}">
                        <div class="flex items-center space-x-2 py-2 px-4">
                            <div class="relative flex-grow">
                                {{ form.supplier_name }}
                                <div id="supplier-suggestions" class="absolute z-10 bg-white border border-gray-300 rounded shadow-lg w-full max-h-48 overflow-y-auto mt-1">
                                    {# HTMX autocomplete results will go here #}
                                </div>
                                {% if form.supplier_name.errors %}
                                <p class="text-red-500 text-xs mt-1">{{ form.supplier_name.errors }}</p>
                                {% endif %}
                            </div>
                            <div class="flex-none w-20">
                                {{ form.qty }}
                                {% if form.qty.errors %}<p class="text-red-500 text-xs mt-1">{{ form.qty.errors }}</p>{% endif %}
                            </div>
                            <div class="flex-none w-20">
                                {{ form.rate }}
                                {% if form.rate.errors %}<p class="text-red-500 text-xs mt-1">{{ form.rate.errors }}</p>{% endif %}
                            </div>
                            <div class="flex-none w-20">
                                {{ form.discount }}
                                {% if form.discount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.discount.errors }}</p>{% endif %}
                            </div>
                            <div class="flex-none w-32">
                                {{ form.delivery_date }}
                                {% if form.delivery_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.delivery_date.errors }}</p>{% endif %}
                            </div>
                            <div class="flex-none">
                                <button type="submit" class="bg-green-500 hover:bg-green-600 text-white font-bold py-1 px-3 rounded text-xs">Add</button>
                            </div>
                        </div>
                        {% if form.non_field_errors %}
                        <div class="text-red-500 text-xs mt-1 px-4">{{ form.non_field_errors }}</div>
                        {% endif %}
                    </form>
                </td>
            </tr>
            {% else %}
            <tr class="bg-white border-b">
                <td colspan="6" class="py-2 px-4 text-center text-gray-500">
                    All required quantities for this item have been planned.
                </td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
    // Script for handling HTMX autocomplete suggestions
    document.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'supplier-suggestions') {
            document.querySelectorAll('#supplier-suggestions div').forEach(item => {
                item.addEventListener('click', function() {
                    const inputField = this.closest('.relative').querySelector('input[type="text"]');
                    inputField.value = this.textContent;
                    document.getElementById('supplier-suggestions').innerHTML = ''; // Clear suggestions
                });
            });
        }
    });
</script>
```

Autocomplete Suggestions Partial (`pr_module/prplanningdetail/_supplier_suggestions.html`):
This will be loaded by HTMX into `#supplier-suggestions`.

```html
<!-- pr_module/prplanningdetail/_supplier_suggestions.html -->
{% for suggestion in suggestions %}
    <div class="p-2 cursor-pointer hover:bg-blue-100">{{ suggestion }}</div>
{% empty %}
    <div class="p-2 text-gray-500">No suggestions</div>
{% endfor %}
```

## 4.5 URLs

Task: Define URL patterns for the views.

```python
# pr_module/urls.py
from django.urls import path
from .views import (
    PrItemDetailListView, PrPlanningDetailTablePartialView,
    PrPlanningDetailAddUpdateView, PrPlanningDetailDeleteView,
    SupplierAutocompleteView, FileDownloadView, GeneratePrView
)
from django.views.generic import RedirectView

urlpatterns = [
    # Main PR Item List View
    path('pr_new_details/', PrItemDetailListView.as_view(), name='pr_new_details_list'),
    
    # HTMX endpoints for nested PR planning details
    path('pr_new_details/item/<int:item_id>/planning_table/', PrPlanningDetailTablePartialView.as_view(), name='pr_planning_table_partial'),
    path('pr_new_details/item/<int:item_id>/add_update_planning/', PrPlanningDetailAddUpdateView.as_view(), name='pr_planning_detail_add_update'),
    path('pr_new_details/planning/<int:pk>/delete/', PrPlanningDetailDeleteView.as_view(), name='pr_planning_detail_delete'),

    # Autocomplete for suppliers
    path('suppliers/autocomplete/', SupplierAutocompleteView.as_view(), name='supplier_autocomplete'),

    # File Download (Drawings/Specs)
    path('download/<str:model_name>/<int:pk>/<str:file_field>/<str:filename_field>/<str:content_type_field>/', FileDownloadView.as_view(), name='file_download'),

    # Generate PR
    path('generate_pr/', GeneratePrView.as_view(), name='generate_pr'),

    # Dummy URL for redirection after PR generation or cancel (mimicking PR_New.aspx)
    path('pr_new_list_after_generation/', RedirectView.as_view(url='/your-main-pr-list-page/', permanent=False), name='pr_new_list_after_generation'),
    # You would replace '/your-main-pr-list-page/' with the actual URL of your PR list page
]

```

## 4.6 Tests

Task: Write tests for the model and views.

```python
# pr_module/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from decimal import Decimal
import datetime

from .models import (
    Company, FinancialYear, UnitMaster, ItemMaster, Supplier,
    WorkOrder, RateRegister, RateLockUnlock, PrPlanningDetail,
    PrMaster, PrDetail, AccountHead, PrItemDetail
)

class PrModuleTestSetup(TestCase):
    """Base setup for common test data."""
    @classmethod
    def setUpTestData(cls):
        # Create dummy related objects first
        cls.company = Company.objects.create(id=1, name='Test Company')
        cls.fin_year = FinancialYear.objects.create(id=2024, year_start='2024-01-01', year_end='2024-12-31')
        cls.uom = UnitMaster.objects.create(id=1, symbol='PCS', description='Pieces')
        cls.item = ItemMaster.objects.create(id=101, item_code='ITEM001', manf_desc='Test Item 1', uom_basic=cls.uom,
                                            file_name='drawing.pdf', file_data=b'pdfdata', content_type='application/pdf',
                                            att_name='spec.doc', att_data=b'docdata', att_content_type='application/msword')
        cls.supplier = Supplier.objects.create(id=1, name='Test Supplier 1', company=cls.company)
        cls.supplier2 = Supplier.objects.create(id=2, name='Test Supplier 2', company=cls.company)
        cls.work_order = WorkOrder.objects.create(wo_no='WO-TEST-001', boughtout_material_date='2024-06-01', company=cls.company, fin_year=cls.fin_year)
        cls.account_head = AccountHead.objects.create(id=28, name='Material Purchase') # Fixed AHId from ASP.NET

        # Sample Rate Register entries
        RateRegister.objects.create(id=1, item=cls.item, company=cls.company, rate=Decimal('100.00'), discount=Decimal('5.00'), flag=0)
        RateRegister.objects.create(id=2, item=cls.item, company=cls.company, rate=Decimal('95.00'), discount=Decimal('0.00'), flag=1) # Preferred rate
        RateRegister.objects.create(id=3, item=cls.item, company=cls.company, rate=Decimal('110.00'), discount=Decimal('10.00'), flag=0)

        # Sample Rate Lock Unlock entry
        RateLockUnlock.objects.create(item=cls.item, company=cls.company, lock_unlock=1, type=1) # Rate is locked

        # PrItemDetail dummy data (simulates a database view or complex query result)
        PrItemDetail.objects.create(
            item_id=cls.item.id,
            item_code=cls.item.item_code,
            description=cls.item.manf_desc,
            uom_basic_symbol=cls.uom.symbol,
            bom_qty=Decimal('100.000'),
            pr_qty=Decimal('10.000'),
            wis_qty=Decimal('5.000'),
            gqn_qty=Decimal('2.000'),
            file_name=cls.item.file_name,
            att_name=cls.item.att_name
        )

        cls.client = Client()
        cls.session_key = 'test_session_key_123'
        cls.client.session['compid'] = cls.company.id
        cls.client.session['finyear'] = cls.fin_year.id
        cls.client.session.session_key = cls.session_key # Manually set for testing

class PrPlanningDetailModelTest(PrModuleTestSetup):
    def test_pr_planning_detail_creation(self):
        pr_plan = PrPlanningDetail.objects.create(
            id=1, company=self.company, session_id=self.session_key,
            item=self.item, supplier=self.supplier, qty=Decimal('10.000'),
            rate=Decimal('90.00'), delivery_date='2024-07-01', discount=Decimal('5.00')
        )
        self.assertEqual(pr_plan.item.item_code, 'ITEM001')
        self.assertEqual(pr_plan.supplier.name, 'Test Supplier 1')
        self.assertEqual(pr_plan.qty, Decimal('10.000'))
        self.assertEqual(pr_plan.get_discounted_rate(), Decimal('85.50'))

    def test_get_pr_temp_qty_for_item(self):
        PrPlanningDetail.objects.create(id=2, company=self.company, session_id=self.session_key, item=self.item, supplier=self.supplier, qty=Decimal('10.000'), rate=Decimal('90'), delivery_date='2024-07-01', discount=Decimal('0'))
        PrPlanningDetail.objects.create(id=3, company=self.company, session_id=self.session_key, item=self.item, supplier=self.supplier2, qty=Decimal('5.000'), rate=Decimal('90'), delivery_date='2024-07-02', discount=Decimal('0'))
        total_qty = PrPlanningDetail.get_pr_temp_qty_for_item(self.item.id, self.session_key, self.company.id)
        self.assertEqual(total_qty, Decimal('15.000'))

    def test_is_rate_acceptable_preferred_rate(self):
        # Preferred rate is 95, entered 90.00, discount 0.00 => 90.00. rate_difference = (95-90) = 5 >= 0. Should be True.
        pr_plan = PrPlanningDetail(item=self.item, rate=Decimal('90.00'), discount=Decimal('0.00'))
        self.assertTrue(pr_plan.is_rate_acceptable(self.company.id))

    def test_is_rate_acceptable_higher_than_best_but_locked(self):
        # Preferred rate is 95, entered 100.00, discount 0.00 => 100.00. rate_difference = (95-100) = -5 < 0.
        # But rate is locked, so should be True.
        pr_plan = PrPlanningDetail(item=self.item, rate=Decimal('100.00'), discount=Decimal('0.00'))
        self.assertTrue(pr_plan.is_rate_acceptable(self.company.id))

    def test_is_rate_acceptable_higher_than_best_and_not_locked(self):
        # Remove the rate lock for this test
        RateLockUnlock.objects.filter(item=self.item, company=self.company).delete()
        # Preferred rate is 95, entered 100.00, discount 0.00 => 100.00. rate_difference = (95-100) = -5 < 0.
        # Rate not locked, so should be False.
        pr_plan = PrPlanningDetail(item=self.item, rate=Decimal('100.00'), discount=Decimal('0.00'))
        self.assertFalse(pr_plan.is_rate_acceptable(self.company.id))
        
    def test_is_rate_acceptable_no_historical_rate(self):
        # Test item without any rates in RateRegister
        item_no_rate = ItemMaster.objects.create(id=102, item_code='ITEM002', manf_desc='Item No Rate', uom_basic=self.uom)
        pr_plan = PrPlanningDetail(item=item_no_rate, rate=Decimal('50.00'), discount=Decimal('0.00'))
        self.assertTrue(pr_plan.is_rate_acceptable(self.company.id)) # Any positive rate is acceptable

class PrItemDetailModelTest(PrModuleTestSetup):
    def test_get_remaining_qty(self):
        # Current: BOM=100, PR=10, WIS=5, GQN=2. Remaining = 100 - 10 - 0 - 5 + 2 = 87
        item_detail = PrItemDetail.objects.get(item_id=self.item.id)
        self.assertEqual(item_detail.get_remaining_qty(self.session_key, self.company.id), Decimal('87.000'))

        # Add some temp PR items
        PrPlanningDetail.objects.create(id=4, company=self.company, session_id=self.session_key, item=self.item, supplier=self.supplier, qty=Decimal('20.000'), rate=Decimal('90'), delivery_date='2024-07-01', discount=Decimal('0'))
        # Remaining now: 87 - 20 = 67
        self.assertEqual(item_detail.get_remaining_qty(self.session_key, self.company.id), Decimal('67.000'))

    def test_get_best_rate_info(self):
        item_detail = PrItemDetail.objects.get(item_id=self.item.id)
        rate_info = item_detail.get_best_rate_info(self.company.id)
        # Preferred rate is 95, discount 0
        self.assertEqual(rate_info['rate'], Decimal('95.00'))
        self.assertEqual(rate_info['discount'], Decimal('0.00'))

        # No rates for item_no_rate
        item_no_rate = ItemMaster.objects.create(id=103, item_code='ITEM003', manf_desc='Item No Rate', uom_basic=self.uom)
        PrItemDetail.objects.create(
            item_id=item_no_rate.id, item_code=item_no_rate.item_code, description=item_no_rate.manf_desc, uom_basic_symbol=self.uom.symbol,
            bom_qty=Decimal('50'), pr_qty=Decimal('0'), wis_qty=Decimal('0'), gqn_qty=Decimal('0')
        )
        rate_info_no_rate = PrItemDetail.objects.get(item_id=item_no_rate.id).get_best_rate_info(self.company.id)
        self.assertEqual(rate_info_no_rate['rate'], Decimal('0.00'))
        self.assertEqual(rate_info_no_rate['discount'], Decimal('0.00'))


class PrModuleViewsTest(PrModuleTestSetup):
    def test_pr_item_detail_list_view(self):
        response = self.client.get(reverse('pr_new_details_list') + '?WONo=WO-TEST-001')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'pr_module/pritemdetail/list.html')
        self.assertContains(response, 'WO-TEST-001')
        self.assertContains(response, 'ITEM001')
        self.assertContains(response, 'BOM Qty') # Check for table headers

    def test_pr_planning_table_partial_view_get(self):
        response = self.client.get(reverse('pr_planning_table_partial', args=[self.item.id]) + '?WONo=WO-TEST-001')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'pr_module/prplanningdetail/_pr_planning_table.html')
        self.assertContains(response, 'Supplier Planning for ITEM001')
        self.assertContains(response, 'name="supplier_name"') # Check for form input

    def test_pr_planning_detail_add_update_view_post_success(self):
        data = {
            'supplier_name': f'Test Supplier 1 [{self.supplier.id}]',
            'qty': '5.000',
            'rate': '95.00',
            'discount': '0.00',
            'delivery_date': '2024-07-15'
        }
        # Initial remaining_qty for ITEM001 is 87.000. Add 5.000, new remaining 82.000.
        # This rate is acceptable based on best rate logic (95 at 0% disc)
        response = self.client.post(reverse('pr_planning_detail_add_update', args=[self.item.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertTrue(PrPlanningDetail.objects.filter(item=self.item, supplier=self.supplier, qty=Decimal('5.000')).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn(f'refreshPrPlanningDetailTable_{self.item.id}', response.headers['HX-Trigger'])

    def test_pr_planning_detail_add_update_view_post_invalid_qty(self):
        data = {
            'supplier_name': f'Test Supplier 1 [{self.supplier.id}]',
            'qty': '1000.000', # Exceeds remaining BOM qty (87)
            'rate': '95.00',
            'discount': '0.00',
            'delivery_date': '2024-07-15'
        }
        response = self.client.post(reverse('pr_planning_detail_add_update', args=[self.item.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Returns HTML with errors
        self.assertContains(response, "Entered quantity (1000.000) exceeds remaining BOM quantity for this item.")
        self.assertFalse(PrPlanningDetail.objects.filter(qty=Decimal('1000.000')).exists())

    def test_pr_planning_detail_add_update_view_post_invalid_rate(self):
        # No rate lock. Preferred rate 95, entered 200 (higher than 95).
        RateLockUnlock.objects.filter(item=self.item, company=self.company).delete()
        data = {
            'supplier_name': f'Test Supplier 1 [{self.supplier.id}]',
            'qty': '10.000',
            'rate': '200.00',
            'discount': '0.00',
            'delivery_date': '2024-07-15'
        }
        response = self.client.post(reverse('pr_planning_detail_add_update', args=[self.item.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Returns HTML with errors
        self.assertContains(response, "Entered rate is not acceptable!")
        self.assertFalse(PrPlanningDetail.objects.filter(qty=Decimal('10.000'), rate=Decimal('200.00')).exists())

    def test_pr_planning_detail_delete_view_post_success(self):
        pr_plan = PrPlanningDetail.objects.create(id=5, company=self.company, session_id=self.session_key, item=self.item, supplier=self.supplier, qty=Decimal('1.000'), rate=Decimal('1'), delivery_date='2024-07-01', discount=Decimal('0'))
        initial_count = PrPlanningDetail.objects.count()
        response = self.client.post(reverse('pr_planning_detail_delete', args=[pr_plan.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(PrPlanningDetail.objects.count(), initial_count - 1)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn(f'refreshPrPlanningDetailTable_{self.item.id}', response.headers['HX-Trigger'])

    def test_supplier_autocomplete_view(self):
        response = self.client.get(reverse('supplier_autocomplete') + '?q=Test', HTTP_ACCEPT='application/json')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), [f'Test Supplier 1 [{self.supplier.id}]', f'Test Supplier 2 [{self.supplier2.id}]'])

    def test_file_download_view(self):
        # Test image download
        response = self.client.get(reverse('file_download', args=['tblDG_Item_Master', self.item.id, 'file_data', 'file_name', 'content_type']))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/pdf')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="drawing.pdf"')
        self.assertEqual(response.content, b'pdfdata')

        # Test spec download
        response = self.client.get(reverse('file_download', args=['tblDG_Item_Master', self.item.id, 'att_data', 'att_name', 'att_content_type']))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/msword')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="spec.doc"')
        self.assertEqual(response.content, b'docdata')

    def test_generate_pr_view_success(self):
        # Add some planning details to be generated
        PrPlanningDetail.objects.create(id=6, company=self.company, session_id=self.session_key, item=self.item, supplier=self.supplier, qty=Decimal('10.000'), rate=Decimal('90'), delivery_date='2024-07-01', discount=Decimal('0'))
        PrPlanningDetail.objects.create(id=7, company=self.company, session_id=self.session_key, item=self.item, supplier=self.supplier2, qty=Decimal('5.000'), rate=Decimal('92'), delivery_date='2024-07-02', discount=Decimal('0'))
        
        initial_pr_master_count = PrMaster.objects.count()
        initial_pr_detail_count = PrDetail.objects.count()
        initial_pr_planning_count = PrPlanningDetail.objects.count()

        response = self.client.post(reverse('generate_pr'), {'wono': self.work_order.wo_no}, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HX-Redirect
        self.assertIn('HX-Redirect', response.headers)
        
        self.assertEqual(PrMaster.objects.count(), initial_pr_master_count + 1)
        self.assertEqual(PrDetail.objects.count(), initial_pr_detail_count + 2) # 2 details added
        self.assertEqual(PrPlanningDetail.objects.count(), initial_pr_planning_count - 2) # 2 details deleted

        new_pr_master = PrMaster.objects.latest('id')
        self.assertEqual(new_pr_master.pr_no, '0001') # First PR
        self.assertEqual(new_pr_master.wo_no, self.work_order.wo_no)

    def test_generate_pr_view_empty_planning(self):
        # Ensure no planning details exist
        PrPlanningDetail.objects.all().delete()
        response = self.client.post(reverse('generate_pr'), {'wono': self.work_order.wo_no}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # No 204/redirect if error
        self.assertContains(response, "No PR planning entries found to generate PR.")
        self.assertIn('HX-Refresh', response.headers)

    def test_generate_pr_view_quantity_exceeds_bom(self):
        # Add planning details that exceed BOM for item
        PrPlanningDetail.objects.create(id=8, company=self.company, session_id=self.session_key, item=self.item, supplier=self.supplier, qty=Decimal('200.000'), rate=Decimal('90'), delivery_date='2024-07-01', discount=Decimal('0'))
        # Remaining BOM for ITEM001 is 87.000. 200.000 > 87.000.
        
        response = self.client.post(reverse('generate_pr'), {'wono': self.work_order.wo_no}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Total planned quantity (200.000) for item ITEM001 exceeds remaining BOM quantity (87.000).")
        self.assertIn('HX-Refresh', response.headers)
        # Should not create any PR master or details
        self.assertEqual(PrMaster.objects.count(), 0)
        self.assertEqual(PrDetail.objects.count(), 0)

```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

*   **HTMX for dynamic updates:**
    *   The main list view (`pr_module/pritemdetail/list.html`) uses `hx-trigger="load, refreshPrItemDetailList from:body"` and `hx-get="{% url 'pr_item_detail_table_partial' %}"` to load the main DataTables list dynamically after page load or on a custom event `refreshPrItemDetailList`.
    *   Each row in the main table has an "Add/View Supplier" button that uses `hx-get="{% url 'pr_planning_table_partial' item.item_id %}"` to load the nested `_pr_planning_table.html` into a collapsible section (`#prPlanningDetailSection_{{ item.item_id }}`).
    *   The `Add` button in the nested planning table uses `hx-post` to `pr_planning_detail_add_update` and `hx-swap="none"` with `HX-Trigger` headers from the view to refresh the specific nested table (`refreshPrPlanningDetailTable_{{ item.item_id }}`).
    *   The delete button in the nested table uses `hx-post` to `pr_planning_detail_delete` with `hx-swap="none"` and `HX-Trigger` for refresh.
    *   The "Generate PR" button uses `hx-post` to `generate_pr` with `hx-swap="none"` and `HX-Redirect` on success or `HX-Refresh` on error.
    *   Supplier autocomplete uses `hx-get`, `hx-trigger="keyup changed delay:500ms"`, and `hx-target` to load suggestions into a dropdown.
*   **Alpine.js for UI state management:**
    *   The general modal is set up with a simple `on click` listener in `list.html` to toggle its `is-active` class. No complex Alpine.js data objects are strictly needed for this basic modal toggle. However, if more complex UI state (like form reset, custom validation display) were needed, Alpine.js would be integrated.
*   **DataTables for list views:**
    *   The main `prItemDetailTable` is initialized using jQuery DataTables in `list.html`'s `extra_js` block, triggered by `htmx:afterSwap` after the table content is loaded.
    *   `"pageLength"`, `"lengthMenu"`, `"searching"`, `"ordering"`, `"paging"`, `"info"` are configured for the DataTables instance.
*   **No full page reloads:** All CRUD operations on temporary PR entries and the "Generate PR" action are handled via HTMX, preventing full page reloads. Messages are integrated with Django's `messages` framework, which HTMX `HX-Refresh` can cause to be displayed on a full page refresh (if `HX-Refresh` is used) or custom HTMX events can be used to dynamically display messages.
*   **DRY template inheritance:** All templates extend `core/base.html`, ensuring all CDN links (including DataTables, HTMX, Alpine.js) are managed centrally.

## Final Notes

*   **Placeholders:** Replace `[COMPLEX_SQL_VIEW_OR_QUERY_RESULT]` in `PrItemDetail`'s `db_table` with an actual database view name or implement a custom manager with the complex `MP_Tree1` SQL logic.
*   **Session Management:** `request.session.get('compid')`, `request.session.get('finyear')`, and `request.session.session_key` are used to map ASP.NET `Session` variables. In a real application, `request.user` attributes (e.g., `request.user.company.id`) would be preferred for company ID.
*   **`fun.getCode` replacement:** The form's `clean_supplier_name` method handles extracting the ID from the `SupplierName [ID]` format.
*   **Database IDs:** The ASP.NET code implicitly uses integer IDs for `ItemMaster`, `Supplier`, `PRMaster`, `PRDetails`. Django's `IntegerField(primary_key=True, db_column='Id')` or `models.BigAutoField(primary_key=True)` (if auto-incrementing identity) is used, along with `managed=False` for existing databases.
*   **Fixed `AHId`:** `AHId=28` in `tblMM_PR_Details` is hardcoded in the ASP.NET. This is mirrored in the Django `GeneratePrView` by fetching `AccountHead.objects.get(id=28)`.
*   **Error Handling:** `messages.error` is used for user feedback, which can be picked up by `HX-Refresh` or custom HTMX event listeners on the client side.
*   **Scalability:** For very large datasets, the `MP_Tree1` query (represented by `PrItemDetail`) might need careful optimization, potentially using materialized views or a custom database cursor.
*   **User Authentication:** This plan assumes a basic Django user authentication is in place to provide `request.user` and `request.session`.