## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

This ASP.NET application primarily interacts with several database tables to manage Purchase Orders (PO) and their associated items from Supplier Purchase Requisitions (SPR). The core tables involved for this specific page's functionality are:

*   **`tblMM_SPR_PO_Temp`**: This is a temporary staging table used to hold the SPR items selected for a PO before finalization. It's the source for the "Selected Items" grid.
    *   **Columns:** `Id` (Primary Key), `SPRNo`, `SessionId`, `CompId`, `SPRId` (Foreign Key to `tblMM_SPR_Details`), `Qty`, `Rate`, `Discount`, `AddDesc`, `PF` (Foreign Key to `tblPacking_Master`), `ExST` (Foreign Key to `tblExciseser_Master`), `VAT` (Foreign Key to `tblVAT_Master`), `DelDate`, `BudgetCode` (Foreign Key to `tblMIS_BudgetCode`).
*   **`tblMM_PO_Master`**: This table stores the main Purchase Order header details.
    *   **Columns:** `Id` (PK), `SysDate`, `SysTime`, `SessionId`, `CompId`, `FinYearId`, `PRSPRFlag`, `PONo`, `SupplierId` (FK to `tblMM_Supplier_master`), `Reference` (FK to `tblMM_PO_Reference`), `ReferenceDate`, `ReferenceDesc`, `PaymentTerms` (FK to `tblPayment_Master`), `Warrenty` (FK to `tblWarrenty_Master`), `Freight` (FK to `tblFreight_Master`), `Octroi` (FK to `tblOctroi_Master`), `ModeOfDispatch`, `Inspection`, `FileName`, `FileSize`, `ContentType`, `FileData` (for file uploads), `Remarks`, `ShipTo`, `Insurance`, `TC`.
*   **`tblMM_PO_Details`**: Stores the individual item details for a finalized Purchase Order, populated from `tblMM_SPR_PO_Temp`.
    *   **Columns:** `Id` (PK), `MId` (FK to `tblMM_PO_Master`), `PONo`, `SPRNo`, `SPRId` (FK to `tblMM_SPR_Details`), `Qty`, `Rate`, `Discount`, `AddDesc`, `PF`, `ExST`, `VAT`, `DelDate`, `BudgetCode`.

Supporting master tables for dropdowns, lookups, and calculations:

*   `tblMM_PO_Reference` (for DDLReference)
*   `tblMM_Supplier_master` (for supplier details and autocomplete)
*   `tblcountry`, `tblState`, `tblCity` (for supplier address lookup)
*   `tbl_PO_terms` (for `TextBox1` default content)
*   `tblMM_SPR_Master`, `tblMM_SPR_Details` (to get item details like `ItemId`, `DeptId`, `WONo`, `AHId` based on `SPRId` in `tblMM_SPR_PO_Temp`)
*   `tblDG_Item_Master`, `Unit_Master` (for item codes, descriptions, UOMs)
*   `AccHead` (for account heads)
*   `tblPacking_Master` (for PF - Packing/Forwarding charges)
*   `tblVAT_Master` (for VAT)
*   `tblExciseser_Master` (for Excise/Service Tax)
*   `BusinessGroup` (for department names)
*   `tblPayment_Master`, `tblFreight_Master`, `tblOctroi_Master`, `tblWarrenty_Master` (for terms & conditions dropdowns)
*   `tblMM_RateLockUnLock_Master` (for rate locking/unlocking)
*   `tblMIS_BudgetCode` (for budget validation)

### Step 2: Identify Backend Functionality

The ASP.NET page provides the following key functionalities:

*   **Display Purchase Order Header (Read):** Shows supplier name, address, and pre-fills terms and conditions from master data.
*   **List Selected SPR Items (Read):** Populates `GridView3` with items from `tblMM_SPR_PO_Temp` based on the current session and company ID. This involves complex joins and calculations (`BasicAmt`, `DiscAmt`, `TaxAmt`, `TotalAmt`).
*   **Delete SPR Item (Delete):** Allows removal of an item from the "Selected Items" grid (and `tblMM_SPR_PO_Temp`).
*   **Generate Purchase Order (Create):**
    *   Generates a new Purchase Order Number (`PONo`).
    *   Validates budget against selected items.
    *   Inserts the main PO details into `tblMM_PO_Master`.
    *   Inserts each selected SPR item from `tblMM_SPR_PO_Temp` into `tblMM_PO_Details`.
    *   Handles file upload (`FileUpload1`).
    *   Updates `tblMM_RateLockUnLock_Master` to lock rates.
    *   Clears the temporary `tblMM_SPR_PO_Temp` table for the session.
    *   Redirects upon completion.
*   **Autocomplete for Supplier Name:** Provides suggestions as the user types.
*   **Client-side Date Picker:** For the reference date field.
*   **Navigation:** Cancelling redirects to a different page.

### Step 3: Infer UI Components

The page uses a master page structure with several content placeholders. The primary content is in `Content7` and includes:

*   **Header Section:**
    *   `asp:Label` (`lblSupplierName`, `LblAddress`): Display supplier information.
    *   `asp:TextBox` (`txtNewCustomerName`): For supplier name input, with `cc1:AutoCompleteExtender` for search.
    *   `asp:DropDownList` (`DDLReference`): For PO reference type.
    *   `asp:TextBox` (`txtRefDate`): For reference date, with `cc1:CalendarExtender` for date picking and validators.
    *   `asp:TextBox` (`txtReferenceDesc`): For reference description.
*   **Tabbed Interface (`cc1:TabContainer`):**
    *   **"SPR Items" Tab (`TabPanel1`):** Contains an `iframe` (`Iframe1`) pointing to `PO_SPR_ItemGrid.aspx`, likely for adding items to the temporary table. (This `iframe` content is considered out of scope for *this* direct conversion but noted as a separate module).
    *   **"Selected Items" Tab (`TabPanel2`):** Contains an `asp:Panel` with `asp:GridView` (`GridView3`) displaying the temporary SPR items. This grid supports paging and a "Delete" command.
    *   **"Terms & Conditions" Tab (`TabPanel3`):** Contains various input fields:
        *   `asp:DropDownList` (`DDLPaymentTerms`, `DDLFreight`, `DDLOctroi`, `drpwarrenty`): For selecting terms.
        *   `asp:TextBox` (`txtInsurance`, `txtRemarks`, `TextBox1`, `txtShipTo`, `txtModeOfDispatch`, `txtInspection`): For various text inputs.
        *   `asp:FileUpload` (`FileUpload1`): For attaching documents.
*   **Action Buttons:**
    *   `asp:Button` (`btnProceed`): "Generate PO" - triggers the main PO creation logic. Includes client-side confirmation.
    *   `asp:Button` (`btnCancel`): "Cancel" - redirects the user.

### Step 4: Generate Django Code

We will create a Django app named `purchase_orders` for this migration.

#### 4.1 Models (`purchase_orders/models.py`)

We'll define the main models for `PurchaseOrder` and `SprPoTempItem`. For other lookup tables, we'll create simpler models or assume they exist. The `SprPoTempItem` will have methods to encapsulate the complex calculation logic from the C# `LoadData` method, adhering to the "Fat Model" principle.

```python
from django.db import models
from django.utils import timezone
from django.urls import reverse
from django.core.files.base import ContentFile
import os
import uuid
import logging

logger = logging.getLogger(__name__)

# --- Supporting Master Models (Simplified for demonstration) ---
# In a real system, these would have their own detailed models, potentially in a 'core' or 'master_data' app.

class Company(models.Model):
    id = models.IntegerField(primary_key=True, db_column='CompId')
    name = models.CharField(max_length=255, db_column='CompName')

    class Meta:
        managed = False
        db_table = 'tblCompany_Master'
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name

    @staticmethod
    def get_company_address(company_id):
        # Placeholder for fun.CompAdd(CompId) logic
        # In a real app, this would query a CompanyAddress table or similar
        return "Company Address Line 1, City, State, Country. PinCode."


class FinancialYear(models.Model):
    id = models.IntegerField(primary_key=True, db_column='FinYearId')
    year_name = models.CharField(max_length=50, db_column='FinYearName')

    class Meta:
        managed = False
        db_table = 'tblFinYear_Master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.year_name


class SupplierMaster(models.Model):
    supplier_id = models.CharField(primary_key=True, max_length=50, db_column='SupplierId')
    supplier_name = models.CharField(max_length=255, db_column='SupplierName')
    regd_address = models.TextField(db_column='RegdAddress', null=True, blank=True)
    regd_country_id = models.IntegerField(db_column='RegdCountry', null=True, blank=True)
    regd_state_id = models.IntegerField(db_column='RegdState', null=True, blank=True)
    regd_city_id = models.IntegerField(db_column='RegdCity', null=True, blank=True)
    regd_pin_no = models.CharField(max_length=20, db_column='RegdPinNo', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier Master'
        verbose_name_plural = 'Supplier Masters'

    def __str__(self):
        return f"{self.supplier_name} [{self.supplier_id}]"

    @property
    def full_address(self):
        # Placeholder for complex address lookup (fun.select for country, state, city)
        city_name = self._get_related_name('tblCity', 'CityId', self.regd_city_id, 'CityName')
        state_name = self._get_related_name('tblState', 'SId', self.regd_state_id, 'StateName')
        country_name = self._get_related_name('tblcountry', 'CId', self.regd_country_id, 'CountryName')

        parts = [self.regd_address]
        if city_name: parts.append(city_name)
        if state_name: parts.append(state_name)
        if country_name: parts.append(country_name)
        if self.regd_pin_no: parts.append(self.regd_pin_no)

        return ", ".join(filter(None, parts))

    def _get_related_name(self, table_name, id_column, pk_value, name_column):
        # Generic helper to fetch name from a related master table
        if pk_value is None:
            return None
        # This would ideally use actual Django models for tblCity, tblState, tblcountry
        # For 'managed=False' context, it mimics direct SQL lookup
        from django.db import connection
        try:
            with connection.cursor() as cursor:
                cursor.execute(f"SELECT {name_column} FROM {table_name} WHERE {id_column} = %s", [pk_value])
                result = cursor.fetchone()
                return result[0] if result else None
        except Exception as e:
            logger.error(f"Error fetching related name from {table_name}: {e}")
            return None


class PoReference(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    ref_desc = models.CharField(max_length=255, db_column='RefDesc')

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Reference'
        verbose_name = 'PO Reference'
        verbose_name_plural = 'PO References'

    def __str__(self):
        return self.ref_desc


class PaymentTerms(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    terms = models.TextField(db_column='Terms')

    class Meta:
        managed = False
        db_table = 'tblPayment_Master'
        verbose_name = 'Payment Term'
        verbose_name_plural = 'Payment Terms'

    def __str__(self):
        return self.terms


class FreightMaster(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    terms = models.TextField(db_column='Terms')

    class Meta:
        managed = False
        db_table = 'tblFreight_Master'
        verbose_name = 'Freight Master'
        verbose_plural = 'Freight Masters'

    def __str__(self):
        return self.terms


class OctroiMaster(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    terms = models.TextField(db_column='Terms')

    class Meta:
        managed = False
        db_table = 'tblOctroi_Master'
        verbose_name = 'Octroi Master'
        verbose_plural = 'Octroi Masters'

    def __str__(self):
        return self.terms


class WarrantyMaster(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    terms = models.TextField(db_column='Terms')

    class Meta:
        managed = False
        db_table = 'tblWarrenty_Master'
        verbose_name = 'Warranty Master'
        verbose_plural = 'Warranty Masters'

    def __str__(self):
        return self.terms


class PackingMaster(models.Model): # For PF
    id = models.IntegerField(primary_key=True, db_column='Id')
    terms = models.TextField(db_column='Terms')
    value = models.DecimalField(max_digits=10, decimal_places=2, db_column='Value')

    class Meta:
        managed = False
        db_table = 'tblPacking_Master'
        verbose_name = 'Packing Master'
        verbose_plural = 'Packing Masters'

    def __str__(self):
        return self.terms


class VatMaster(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    terms = models.TextField(db_column='Terms')
    value = models.DecimalField(max_digits=10, decimal_places=2, db_column='Value')

    class Meta:
        managed = False
        db_table = 'tblVAT_Master'
        verbose_name = 'VAT Master'
        verbose_plural = 'VAT Masters'

    def __str__(self):
        return self.terms


class ExciseServiceTaxMaster(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    terms = models.TextField(db_column='Terms')
    value = models.DecimalField(max_digits=10, decimal_places=2, db_column='Value')

    class Meta:
        managed = False
        db_table = 'tblExciseser_Master'
        verbose_name = 'Excise/Service Tax Master'
        verbose_plural = 'Excise/Service Tax Masters'

    def __str__(self):
        return self.terms


class BudgetCode(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    symbol = models.CharField(max_length=50, db_column='Symbol')
    description = models.TextField(db_column='Description')

    class Meta:
        managed = False
        db_table = 'tblMIS_BudgetCode'
        verbose_name = 'Budget Code'
        verbose_plural = 'Budget Codes'

    def __str__(self):
        return f"{self.symbol} - {self.description}"


class BusinessGroup(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    symbol = models.CharField(max_length=50, db_column='Symbol')

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_plural = 'Business Groups'

    def __str__(self):
        return self.symbol


class AccountHead(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    symbol = models.CharField(max_length=50, db_column='Symbol')

    class Meta:
        managed = False
        db_table = 'AccHead'
        verbose_name = 'Account Head'
        verbose_plural = 'Account Heads'

    def __str__(self):
        return self.symbol


class UnitMaster(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    symbol = models.CharField(max_length=50, db_column='Symbol')

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit Master'
        verbose_plural = 'Unit Masters'

    def __str__(self):
        return self.symbol


class ItemMaster(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    item_code = models.CharField(max_length=50, db_column='ItemCode')
    manufacturer_description = models.TextField(db_column='ManfDesc')
    uom_basic = models.ForeignKey(UnitMaster, on_delete=models.DO_NOTHING, db_column='UOMBasic', related_name='items')
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='items')

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_plural = 'Item Masters'

    def __str__(self):
        return f"{self.item_code} - {self.manufacturer_description}"


class SprDetail(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    spr_no = models.CharField(max_length=50, db_column='SPRNo')
    # MId (FK to tblMM_SPR_Master) is needed but omitted for brevity in this context
    item = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId', related_name='spr_details')
    department = models.ForeignKey(BusinessGroup, on_delete=models.DO_NOTHING, db_column='DeptId', null=True, blank=True, related_name='spr_details')
    work_order_no = models.CharField(max_length=50, db_column='WONo', null=True, blank=True)
    account_head = models.ForeignKey(AccountHead, on_delete=models.DO_NOTHING, db_column='AHId', null=True, blank=True, related_name='spr_details')
    budget_code = models.ForeignKey(BudgetCode, on_delete=models.DO_NOTHING, db_column='BudgetCode', null=True, blank=True, related_name='spr_details')

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Details'
        verbose_name = 'SPR Detail'
        verbose_plural = 'SPR Details'

    def __str__(self):
        return f"SPR: {self.spr_no}, Item: {self.item.item_code}"


# --- Core Models for this page ---

class SprPoTempItem(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    spr_no = models.CharField(max_length=50, db_column='SPRNo')
    session_id = models.CharField(max_length=255, db_column='SessionId')
    company_id = models.IntegerField(db_column='CompId') # Use integer directly, will be linked by session/context
    spr_detail = models.ForeignKey(SprDetail, on_delete=models.DO_NOTHING, db_column='SPRId')
    quantity = models.DecimalField(max_digits=18, decimal_places=3, db_column='Qty')
    rate = models.DecimalField(max_digits=18, decimal_places=2, db_column='Rate')
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, db_column='Discount')
    additional_description = models.TextField(db_column='AddDesc', null=True, blank=True)
    pf = models.ForeignKey(PackingMaster, on_delete=models.DO_NOTHING, db_column='PF')
    excise_service_tax = models.ForeignKey(ExciseServiceTaxMaster, on_delete=models.DO_NOTHING, db_column='ExST')
    vat = models.ForeignKey(VatMaster, on_delete=models.DO_NOTHING, db_column='VAT')
    delivery_date = models.DateField(db_column='DelDate')
    budget_code = models.ForeignKey(BudgetCode, on_delete=models.DO_NOTHING, db_column='BudgetCode', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_PO_Temp'
        verbose_name = 'SPR PO Temporary Item'
        verbose_name_plural = 'SPR PO Temporary Items'

    def __str__(self):
        return f"{self.spr_no} - {self.spr_detail.item.item_code} (Temp ID: {self.id})"

    # --- Fat Model: Business logic methods (formerly in C# LoadData) ---

    @property
    def item_code_display(self):
        """Returns the item code from the associated SPR Detail's Item."""
        return self.spr_detail.item.item_code

    @property
    def purchase_description(self):
        """Returns the manufacturer description from the associated SPR Detail's Item."""
        return self.spr_detail.item.manufacturer_description

    @property
    def uom_purchase(self):
        """Returns the UOM symbol from the associated SPR Detail's Item's Unit."""
        return self.spr_detail.item.uom_basic.symbol

    @property
    def account_head_display(self):
        """Returns the account head symbol from the associated SPR Detail."""
        return self.spr_detail.account_head.symbol if self.spr_detail.account_head else "NA"

    @property
    def work_order_no(self):
        """Returns the work order number from the associated SPR Detail."""
        return self.spr_detail.work_order_no if self.spr_detail.work_order_no else "NA"

    @property
    def department_display(self):
        """Returns the department symbol from the associated SPR Detail's Business Group."""
        return self.spr_detail.department.symbol if self.spr_detail.department else "NA"

    @property
    def basic_amount(self):
        """Calculates basic amount (Qty * Rate)."""
        return self.quantity * self.rate

    @property
    def discount_amount(self):
        """Calculates discount amount (Qty * Rate * Discount / 100)."""
        return self.basic_amount * (self.discount_percentage / 100)

    @property
    def taxable_amount(self):
        """Calculates taxable amount (Basic Amt - Discount Amt)."""
        return self.basic_amount - self.discount_amount

    @property
    def total_tax_percentage(self):
        """Calculates combined tax percentage from PF, ExST, VAT values."""
        pf_val = self.pf.value if self.pf else 0
        exst_val = self.excise_service_tax.value if self.excise_service_tax else 0
        vat_val = self.vat.value if self.vat else 0
        return pf_val + exst_val + vat_val

    @property
    def tax_amount(self):
        """Calculates total tax amount based on combined tax percentage."""
        return self.taxable_amount * (self.total_tax_percentage / 100)

    @property
    def total_amount(self):
        """Calculates total amount (Basic Amt - Disc Amt + Tax Amt)."""
        return self.taxable_amount + self.tax_amount


class PurchaseOrder(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    sys_date = models.DateField(db_column='SysDate', default=timezone.now)
    sys_time = models.TimeField(db_column='SysTime', default=timezone.now)
    session_id = models.CharField(max_length=255, db_column='SessionId') # From ASP.NET session ID
    company_id = models.IntegerField(db_column='CompId') # From ASP.NET session compid
    financial_year_id = models.IntegerField(db_column='FinYearId') # From ASP.NET session finyear
    pr_spr_flag = models.BooleanField(default=True, db_column='PRSPRFlag') # '1' in ASP.NET
    po_no = models.CharField(max_length=50, db_column='PONo', unique=True)
    supplier = models.ForeignKey(SupplierMaster, on_delete=models.DO_NOTHING, db_column='SupplierId', to_field='supplier_id')
    reference_type = models.ForeignKey(PoReference, on_delete=models.DO_NOTHING, db_column='Reference')
    reference_date = models.DateField(db_column='ReferenceDate')
    reference_description = models.TextField(db_column='ReferenceDesc', null=True, blank=True)
    payment_terms = models.ForeignKey(PaymentTerms, on_delete=models.DO_NOTHING, db_column='PaymentTerms')
    warranty = models.ForeignKey(WarrantyMaster, on_delete=models.DO_NOTHING, db_column='Warrenty')
    freight = models.ForeignKey(FreightMaster, on_delete=models.DO_NOTHING, db_column='Freight')
    octroi = models.ForeignKey(OctroiMaster, on_delete=models.DO_NOTHING, db_column='Octroi')
    mode_of_dispatch = models.TextField(db_column='ModeOfDispatch', null=True, blank=True)
    inspection = models.TextField(db_column='Inspection', null=True, blank=True)
    
    # File handling: Django's FileField stores path, not binary data in DB by default.
    # To mimic varbinary(MAX) storage, custom storage backend is needed or manually save.
    # For modernization, we'll store on disk and keep path/metadata.
    attached_file_name = models.CharField(max_length=255, db_column='FileName', null=True, blank=True)
    attached_file_content_type = models.CharField(max_length=100, db_column='ContentType', null=True, blank=True)
    attached_file_path = models.FileField(upload_to='po_annexures/', db_column='FileData', null=True, blank=True) # Renamed from FileData to reflect path storage
    
    remarks = models.TextField(db_column='Remarks', null=True, blank=True)
    ship_to = models.TextField(db_column='ShipTo', null=True, blank=True)
    insurance = models.TextField(db_column='Insurance', null=True, blank=True)
    terms_conditions_text = models.TextField(db_column='TC', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'

    def __str__(self):
        return self.po_no

    @classmethod
    def generate_po_number(cls, company_id, financial_year_id):
        """
        Generates the next sequential Purchase Order number based on the last one.
        Mimics C# fun.select("PONo", "tblMM_PO_Master", "CompId='...' AND FinYearId='...' order by PONo desc")
        """
        try:
            last_po = cls.objects.filter(
                company_id=company_id,
                financial_year_id=financial_year_id
            ).order_by('-po_no').first() # Assumes PONo is sortable string like '0001', '0002'

            if last_po and last_po.po_no.isdigit():
                next_num = int(last_po.po_no) + 1
            else:
                next_num = 1
            return f"{next_num:04d}" # Format as 4-digit string
        except Exception as e:
            logger.error(f"Error generating PO number: {e}")
            raise ValueError("Could not generate a new PO number.")

    def perform_budget_validation(self, company_id, financial_year_id, temp_items):
        """
        Performs the complex budget validation logic from the ASP.NET code.
        This is a critical business rule, so it resides in the model.
        Returns (True, None) on success or (False, list_of_insufficient_budgets) on failure.
        """
        # Mimic the C# SQL query for budget amounts and validation.
        # This will be a complex aggregation logic.
        # For simplicity, we'll iterate through temp_items and simulate the check.
        # In a real scenario, this would involve sophisticated queries or a dedicated budgeting service.
        
        insufficient_budgets = []
        
        # Aggregate amounts by (DeptId, WONo, BudgetCode) as in ASP.NET's Sqlbud query
        aggregated_amounts = {}
        for item in temp_items:
            key = (
                item.spr_detail.department.id if item.spr_detail.department else 0,
                item.spr_detail.work_order_no if item.spr_detail.work_order_no else "NA",
                item.budget_code.id if item.budget_code else 0
            )
            total_item_amount = item.total_amount # Use the fat model property
            
            if key not in aggregated_amounts:
                aggregated_amounts[key] = 0
            aggregated_amounts[key] += total_item_amount

        # Simulate CalBalBudgetAmt logic
        for (dept_id, wono, budget_code_id), aggregated_amount in aggregated_amounts.items():
            total_budget = 0
            budget_symbol = "NA"
            budget_description = "NA"
            wono_display = wono

            try:
                # Fetch BudgetCode details for display if insufficient
                if budget_code_id:
                    budget_obj = BudgetCode.objects.get(id=budget_code_id)
                    budget_symbol = budget_obj.symbol
                    budget_description = budget_obj.description

                if dept_id == 0: # WO No based budget
                    # Simulate fun.CheckValidWONo and calbalbud.TotBalBudget_WONO
                    # Placeholder for actual budget check logic
                    is_valid_wono = True # Assume valid for demo
                    if is_valid_wono:
                        total_budget = self._get_budget_for_wono(budget_code_id, company_id, financial_year_id, wono)
                else: # Business Group (Department) based budget
                    # Simulate calbalbud.TotBalBudget_BG
                    if dept_id:
                        dept_obj = BusinessGroup.objects.get(id=dept_id)
                        wono_display = dept_obj.symbol
                    total_budget = self._get_budget_for_bg(dept_id, company_id, financial_year_id)
            except Exception as e:
                logger.error(f"Error during budget lookup for ({dept_id}, {wono}, {budget_code_id}): {e}")
                # Treat as failure or insufficient if lookup itself fails
                total_budget = 0 # Default to 0 to trigger insufficient

            if (total_budget - aggregated_amount) < 0:
                insufficient_budgets.append({
                    'wono_bg': wono_display,
                    'budget_code': budget_symbol,
                    'description': budget_description,
                    'balance_amount': round(total_budget, 3) # Mimic C# Math.Round
                })
        
        return len(insufficient_budgets) == 0, insufficient_budgets

    def _get_budget_for_wono(self, budget_code_id, company_id, financial_year_id, wono):
        # Placeholder for actual budget system integration (TotBalBudget_WONO)
        # This would involve querying a dedicated budget ledger/system
        return *********.00 # Placeholder large budget for success demo

    def _get_budget_for_bg(self, dept_id, company_id, financial_year_id):
        # Placeholder for actual budget system integration (TotBalBudget_BG)
        # This would involve querying a dedicated budget ledger/system
        return *********.00 # Placeholder large budget for success demo


class PurchaseOrderDetail(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    master = models.ForeignKey(PurchaseOrder, on_delete=models.CASCADE, db_column='MId', related_name='details')
    po_no = models.CharField(max_length=50, db_column='PONo') # Denormalized from master
    spr_no = models.CharField(max_length=50, db_column='SPRNo')
    spr_detail = models.ForeignKey(SprDetail, on_delete=models.DO_NOTHING, db_column='SPRId')
    quantity = models.DecimalField(max_digits=18, decimal_places=3, db_column='Qty')
    rate = models.DecimalField(max_digits=18, decimal_places=2, db_column='Rate')
    discount_percentage = models.DecimalField(max_digits=5, decimal_places=2, db_column='Discount')
    additional_description = models.TextField(db_column='AddDesc', null=True, blank=True)
    pf = models.ForeignKey(PackingMaster, on_delete=models.DO_NOTHING, db_column='PF')
    excise_service_tax = models.ForeignKey(ExciseServiceTaxMaster, on_delete=models.DO_NOTHING, db_column='ExST')
    vat = models.ForeignKey(VatMaster, on_delete=models.DO_NOTHING, db_column='VAT')
    delivery_date = models.DateField(db_column='DelDate')
    budget_code = models.ForeignKey(BudgetCode, on_delete=models.DO_NOTHING, db_column='BudgetCode', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Details'
        verbose_name = 'Purchase Order Detail'
        verbose_name_plural = 'Purchase Order Details'

    def __str__(self):
        return f"PO {self.po_no} - Item {self.spr_detail.item.item_code}"


class RateLockUnlockMaster(models.Model):
    # This table is updated in btnProceed_Click
    item = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId')
    type = models.IntegerField(db_column='Type') # '1' or '2' in ASP.NET
    lock_unlock = models.BooleanField(db_column='LockUnlock') # '0' for locked
    locked_by_transaction = models.CharField(max_length=50, db_column='LockedbyTranaction', null=True, blank=True)
    lock_date = models.DateField(db_column='LockDate', null=True, blank=True)
    lock_time = models.TimeField(db_column='LockTime', null=True, blank=True)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblMM_RateLockUnLock_Master'
        verbose_name = 'Rate Lock/Unlock'
        verbose_plural = 'Rate Lock/Unlock'

    def __str__(self):
        return f"Item {self.item.item_code} - Locked: {self.lock_unlock}"

    @classmethod
    def lock_item_rates(cls, item_id, transaction_no, company_id):
        """
        Locks item rates for a given transaction, mimicking C# `sqlt` and `sqlt1`.
        """
        now = timezone.now()
        cls.objects.filter(item_id=item_id, company_id=company_id).update(
            lock_unlock=False, # 0 in ASP.NET
            locked_by_transaction=transaction_no,
            lock_date=now.date(),
            lock_time=now.time()
        )
        logger.info(f"Rates locked for item {item_id} by PO {transaction_no}")

```

#### 4.2 Forms (`purchase_orders/forms.py`)

We'll define a form for the `PurchaseOrder` header details. No form is needed for `SprPoTempItem` as it's populated from another page (iframe) and only displayed/deleted here.

```python
from django import forms
from .models import (
    PurchaseOrder, SupplierMaster, PoReference,
    PaymentTerms, FreightMaster, OctroiMaster, WarrantyMaster
)
from django.utils import timezone
import re

class PurchaseOrderForm(forms.ModelForm):
    # Supplier Name with ID for autocomplete
    supplier_display = forms.CharField(
        max_length=255,
        required=True,
        label="Supplier Name",
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Start typing supplier name...',
            'hx-get': '/purchase-orders/api/suppliers/autocomplete/', # HTMX for autocomplete
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#supplier-suggestions',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off' # Disable browser autocomplete
        })
    )

    class Meta:
        model = PurchaseOrder
        fields = [
            'supplier', 'reference_type', 'reference_date', 'reference_description',
            'payment_terms', 'freight', 'octroi', 'warranty',
            'insurance', 'remarks', 'terms_conditions_text', 'ship_to',
            'mode_of_dispatch', 'inspection', 'attached_file_path'
        ]
        widgets = {
            'supplier': forms.HiddenInput(), # Actual supplier ID is handled by clean_supplier_display
            'reference_type': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'reference_date': forms.DateInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm datepicker',
                'placeholder': 'dd-MM-yyyy',
                'readonly': 'readonly' # Mimic ASP.NET readonly attribute for calendar extender
            }, format='%d-%m-%Y'),
            'reference_description': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'width': '105px'}),
            'payment_terms': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'freight': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'octroi': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'warranty': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'insurance': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'width': '250px'}),
            'remarks': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3, 'width': '381px'}),
            'terms_conditions_text': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 6, 'width': '100%'}),
            'ship_to': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3, 'width': '374px'}),
            'mode_of_dispatch': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'width': '295px'}),
            'inspection': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'width': '294px'}),
            'attached_file_path': forms.FileInput(attrs={'class': 'block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'}),
        }

    def __init__(self, *args, **kwargs):
        user_session_data = kwargs.pop('user_session_data', None) # Passed from view context
        super().__init__(*args, **kwargs)

        # Populate dropdowns from database
        self.fields['reference_type'].queryset = PoReference.objects.all()
        self.fields['payment_terms'].queryset = PaymentTerms.objects.all()
        self.fields['freight'].queryset = FreightMaster.objects.all()
        self.fields['octroi'].queryset = OctroiMaster.objects.all()
        self.fields['warranty'].queryset = WarrantyMaster.objects.all()

        if self.instance.pk: # If updating an existing PO
            self.fields['supplier_display'].initial = str(self.instance.supplier) # Set initial display
        elif user_session_data and 'supplier_code' in user_session_data:
            # For new PO from query string, pre-fill supplier info
            supplier_code = user_session_data['supplier_code']
            try:
                supplier = SupplierMaster.objects.get(supplier_id=supplier_code)
                self.initial['supplier_display'] = str(supplier)
                self.initial['supplier'] = supplier.supplier_id
                self.initial['ship_to'] = Company.get_company_address(user_session_data['company_id']) # Mimic CompAdd
                # For LblAddress, it will be rendered separately in template
            except SupplierMaster.DoesNotExist:
                pass

        # Set default T&C text mimicking the ASP.NET behavior
        if not self.instance.pk and 'terms_conditions_text' not in self.initial:
            try:
                # Mimic fun.select1("Terms", "tbl_PO_terms") and StringBuilder
                terms_list = [term.terms for term in PoTerms.objects.all()]
                self.initial['terms_conditions_text'] = "\n".join(terms_list)
            except Exception:
                self.initial['terms_conditions_text'] = ""

    def clean_supplier_display(self):
        supplier_display = self.cleaned_data['supplier_display']
        match = re.match(r'^(.*?) \[(\w+)\]$', supplier_display.strip())
        if not match:
            raise forms.ValidationError("Invalid supplier format. Please select from autocomplete suggestions.")
        supplier_name = match.group(1).strip()
        supplier_id_str = match.group(2).strip()

        try:
            supplier = SupplierMaster.objects.get(supplier_id=supplier_id_str, supplier_name=supplier_name)
        except SupplierMaster.DoesNotExist:
            raise forms.ValidationError("Selected supplier does not exist or format is incorrect.")

        self.cleaned_data['supplier'] = supplier # Set the actual supplier object
        return supplier_display

    def clean_reference_date(self):
        ref_date_str = self.cleaned_data['reference_date']
        # Django's DateField handles format. We only need the validation logic.
        if not isinstance(ref_date_str, (type(None), type(timezone.now().date()))):
            # This validation should be handled by form's DateField itself
            # but if it was a TextInput, manual regex was needed like in ASP.NET
            pass
        return ref_date_str

    def save(self, commit=True):
        po = super().save(commit=False)
        po.supplier = self.cleaned_data['supplier'] # Ensure FK is set from cleaned_data
        if commit:
            po.save()
        return po

# Model for tbl_PO_terms to support populating default terms_conditions_text
class PoTerms(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    terms = models.TextField(db_column='Terms')

    class Meta:
        managed = False
        db_table = 'tbl_PO_terms'
        verbose_name = 'PO Term'
        verbose_name_plural = 'PO Terms'

    def __str__(self):
        return self.terms

```

#### 4.3 Views (`purchase_orders/views.py`)

This section includes the main PO creation view, a partial view for the SPR items table, a delete view for SPR items, and an API view for supplier autocomplete.

```python
from django.views.generic import FormView, ListView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, redirect
from django.db import transaction
from django.template.loader import render_to_string
import logging

from .models import (
    PurchaseOrder, SprPoTempItem, PurchaseOrderDetail, RateLockUnlockMaster,
    SupplierMaster, Company, FinancialYear, ItemMaster, SprDetail
)
from .forms import PurchaseOrderForm

logger = logging.getLogger(__name__)

class PurchaseOrderCreateView(FormView):
    template_name = 'purchase_orders/po_spr_items/main_form.html'
    form_class = PurchaseOrderForm
    # success_url handled by HTMX triggers, or redirected
    success_url = reverse_lazy('po_new_redirect') # Redirect to the PO_new.aspx equivalent

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Mimic ASP.NET Session variables and QueryString
        context['current_user_session_id'] = self.request.session.get('username', 'test_user_session')
        context['current_company_id'] = self.request.session.get('compid', 1) # Default to 1
        context['current_financial_year_id'] = self.request.session.get('finyear', 1) # Default to 1
        context['supplier_code_from_qs'] = self.request.GET.get('Code', None)
        
        # Pass session data to form for initial population
        context['form'] = self.get_form(
            self.get_form_class(),
            user_session_data={
                'supplier_code': context['supplier_code_from_qs'],
                'company_id': context['current_company_id']
            }
        )
        
        # Pre-fill supplier address if supplier_code is available
        if context['supplier_code_from_qs']:
            try:
                supplier = SupplierMaster.objects.get(supplier_id=context['supplier_code_from_qs'])
                context['supplier_address'] = supplier.full_address
                context['supplier_name_display'] = str(supplier)
            except SupplierMaster.DoesNotExist:
                context['supplier_address'] = "Supplier not found."
                context['supplier_name_display'] = "Supplier not found."
        else:
            context['supplier_address'] = ""
            context['supplier_name_display'] = ""

        # Placeholder for iframe content - needs separate view/migration
        context['spr_item_grid_iframe_url'] = reverse_lazy('po_spr_item_grid') # Example url
        
        return context

    def form_valid(self, form):
        current_session_id = self.request.session.get('username', 'test_user_session')
        current_company_id = self.request.session.get('compid', 1)
        current_financial_year_id = self.request.session.get('finyear', 1)
        
        temp_items = SprPoTempItem.objects.filter(
            session_id=current_session_id,
            company_id=current_company_id
        ).select_related('spr_detail__item__uom_basic', 'spr_detail__department', 'spr_detail__account_head', 'pf', 'excise_service_tax', 'vat', 'budget_code')

        if not temp_items.exists():
            messages.error(self.request, "No SPR items selected to generate PO. Please add items first.")
            return self.form_invalid(form) # Re-render the form with error

        # Step 1: Budget Validation
        is_budget_sufficient, insufficient_budgets = form.instance.perform_budget_validation(
            company_id=current_company_id,
            financial_year_id=current_financial_year_id,
            temp_items=temp_items
        )

        if not is_budget_sufficient:
            error_msg = "Insufficient budget for the following items:"
            for item in insufficient_budgets:
                error_msg += f"<br>- WO/BG: {item['wono_bg']}, Budget Code: {item['budget_code']}, Bal Amt: {item['balance_amount']}"
            messages.error(self.request, error_msg)
            # Mimic ASP.NET's redirect to PO_Error.aspx
            return HttpResponse(status=200, headers={'HX-Redirect': reverse_lazy('po_error_redirect')})


        try:
            with transaction.atomic():
                # Step 2: Generate PO Number
                po_number = PurchaseOrder.generate_po_number(current_company_id, current_financial_year_id)

                # Step 3: Save Purchase Order Master
                po_master = form.save(commit=False)
                po_master.session_id = current_session_id
                po_master.company_id = current_company_id
                po_master.financial_year_id = current_financial_year_id
                po_master.po_no = po_number
                po_master.pr_spr_flag = True # '1' in ASP.NET

                # Handle file upload
                uploaded_file = self.request.FILES.get('attached_file_path')
                if uploaded_file:
                    # Save file to MEDIA_ROOT
                    file_ext = os.path.splitext(uploaded_file.name)[1]
                    file_name = f"{uuid.uuid4()}{file_ext}" # Generate unique file name
                    po_master.attached_file_path.save(file_name, ContentFile(uploaded_file.read()), save=False)
                    po_master.attached_file_name = uploaded_file.name
                    po_master.attached_file_content_type = uploaded_file.content_type
                
                po_master.save()

                # Step 4: Save Purchase Order Details and lock rates
                for temp_item in temp_items:
                    PurchaseOrderDetail.objects.create(
                        master=po_master,
                        po_no=po_master.po_no,
                        spr_no=temp_item.spr_no,
                        spr_detail=temp_item.spr_detail,
                        quantity=temp_item.quantity,
                        rate=temp_item.rate,
                        discount_percentage=temp_item.discount_percentage,
                        additional_description=temp_item.additional_description,
                        pf=temp_item.pf,
                        excise_service_tax=temp_item.excise_service_tax,
                        vat=temp_item.vat,
                        delivery_date=temp_item.delivery_date,
                        budget_code=temp_item.budget_code
                    )
                    # Lock item rates
                    # ASP.NET original code gets ItemId from tblMM_SPR_Details.ItemId via complex query.
                    # This is now simplified as FK lookup on SprDetail.item.id
                    RateLockUnlockMaster.lock_item_rates(
                        item_id=temp_item.spr_detail.item.id,
                        transaction_no=po_master.po_no,
                        company_id=current_company_id
                    )

                # Step 5: Clear temporary items
                temp_items.delete()

            messages.success(self.request, f"Purchase Order {po_number} generated successfully.")
            # Mimic ASP.NET's redirect to PO_new.aspx
            return HttpResponse(status=200, headers={'HX-Redirect': self.get_success_url()})

        except Exception as e:
            logger.exception("Error generating Purchase Order.")
            messages.error(self.request, f"An error occurred while generating the Purchase Order: {e}")
            return self.form_invalid(form) # Re-render form with error

    def form_invalid(self, form):
        # This will be called when form validation fails.
        # We need to ensure error messages are displayed and the form is re-rendered via HTMX.
        response = super().form_invalid(form)
        # If it's an HX-Request, we want to return the form partial with errors.
        if self.request.headers.get('HX-Request'):
            # The current form is rendered into the main_form.html; if part of a modal, it'd be rendered there.
            # For this overall page, we re-render the whole page effectively showing errors.
            messages.error(self.request, "Please correct the errors in the form.")
            return self.render_to_response(self.get_context_data(form=form))
        return response

    def post(self, request, *args, **kwargs):
        # Override post to ensure correct handling of HX-Request vs full page POST
        form = self.get_form()
        if form.is_valid():
            return self.form_valid(form)
        else:
            return self.form_invalid(form)

# Separate view for the selected items table, loaded via HTMX
class SprPoTempItemTablePartialView(ListView):
    model = SprPoTempItem
    template_name = 'purchase_orders/po_spr_items/_spr_temp_items_table.html'
    context_object_name = 'spr_po_temp_items'

    def get_queryset(self):
        current_session_id = self.request.session.get('username', 'test_user_session')
        current_company_id = self.request.session.get('compid', 1)
        # Fetch items for the current session and company, prefetching related data for calculations
        return SprPoTempItem.objects.filter(
            session_id=current_session_id,
            company_id=current_company_id
        ).order_by('id').select_related(
            'spr_detail__item__uom_basic',
            'spr_detail__department',
            'spr_detail__account_head',
            'pf',
            'excise_service_tax',
            'vat',
            'budget_code'
        )

class SprPoTempItemDeleteView(DeleteView):
    model = SprPoTempItem
    template_name = 'purchase_orders/po_spr_items/_confirm_delete_spr_item.html'
    # success_url is dynamic, trigger HTMX to refresh list
    
    def get_object(self, queryset=None):
        # Ensure object belongs to current session and company before deletion
        current_session_id = self.request.session.get('username', 'test_user_session')
        current_company_id = self.request.session.get('compid', 1)
        obj = get_object_or_404(self.get_queryset(), pk=self.kwargs['pk'], 
                                session_id=current_session_id, company_id=current_company_id)
        return obj

    def delete(self, request, *args, **kwargs):
        # This handles the actual deletion
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'SPR item deleted successfully.')
        # HTMX will listen for 'refreshSprPoTempItemList' trigger
        return HttpResponse(
            status=204, # No content, tells HTMX to do nothing with this response, just trigger
            headers={
                'HX-Trigger': 'refreshSprPoTempItemList'
            }
        )

# API endpoint for Supplier Autocomplete
class SupplierAutocompleteAPIView(View):
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('supplier_display', '').strip()
        current_company_id = self.request.session.get('compid', 1)

        if not prefix_text:
            return JsonResponse([], safe=False)

        # Mimic ASP.NET's `sql` web method logic
        # Filter suppliers by name starting with prefix_text and by company ID
        # Limiting results to a reasonable number (e.g., 10)
        suppliers = SupplierMaster.objects.filter(
            company_id=current_company_id, # Assuming company_id field on SupplierMaster
            supplier_name__istartswith=prefix_text
        ).order_by('supplier_name')[:10]

        results = [
            f"{supplier.supplier_name} [{supplier.supplier_id}]"
            for supplier in suppliers
        ]
        return JsonResponse(results, safe=False)


# Placeholder views for redirects from ASP.NET
class PoNewRedirectView(View):
    def get(self, request):
        messages.success(request, "Redirected to PO_new equivalent page.")
        return redirect(reverse_lazy('dashboard')) # Redirect to a generic dashboard or list page

class PoErrorRedirectView(View):
    def get(self, request):
        messages.error(request, "Budget validation failed. Please check the budget details.")
        return redirect(reverse_lazy('po_spr_items_create')) # Redirect back to the form
    
# Placeholder for the iframe content (PO_SPR_ItemGrid.aspx)
class PoSprItemGridIframeView(View):
    def get(self, request):
        # This view would typically render content for adding SPR items to tblMM_SPR_PO_Temp
        # For this migration, it's just a placeholder, as the user requested to skip iframe content
        return HttpResponse("<h1>SPR Item Grid (To be migrated separately)</h1><p>This content would allow adding items to the temporary PO table.</p>")

```

#### 4.4 Templates

The templates are designed for HTMX partial loading, with the main page holding the modal structure.

**`purchase_orders/po_spr_items/main_form.html`**

```html
{% extends 'core/base.html' %}
{% load static %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto bg-white p-8 rounded-lg shadow-lg">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">PO - For SPR Items</h2>

        {% if messages %}
            <div class="mb-4">
                {% for message in messages %}
                    <div class="p-3 rounded-md {% if message.tags == 'error' %}bg-red-100 text-red-700{% elif message.tags == 'success' %}bg-green-100 text-green-700{% else %}bg-blue-100 text-blue-700{% endif %}">
                        {{ message|safe }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <form method="post" enctype="multipart/form-data" hx-post="{% url 'po_spr_items_create' %}" hx-swap="outerHTML" hx-target="#main-content-container" hx-trigger="submit">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div>
                    <label for="{{ form.supplier_display.id_for_label }}" class="block text-sm font-medium text-gray-700">Supplier Name</label>
                    {{ form.supplier_display }}
                    <div id="supplier-suggestions" class="bg-white border border-gray-200 rounded-md shadow-lg z-10 relative"></div>
                    {% if supplier_name_display %}<p class="mt-2 text-sm text-gray-600">Current: {{ supplier_name_display }}</p>{% endif %}
                    {% if form.supplier_display.errors %}<p class="text-red-500 text-xs mt-1">{{ form.supplier_display.errors }}</p>{% endif %}
                    {{ form.supplier }} {# Hidden field for actual supplier ID #}
                </div>
                <div>
                    <label for="{{ form.reference_type.id_for_label }}" class="block text-sm font-medium text-gray-700">Reference</label>
                    {{ form.reference_type }}
                    {% if form.reference_type.errors %}<p class="text-red-500 text-xs mt-1">{{ form.reference_type.errors }}</p>{% endif %}
                </div>
                <div class="col-span-2">
                    <label class="block text-sm font-medium text-gray-700">Address</label>
                    <p class="mt-1 text-gray-600">{{ supplier_address|default:"-"|linebreaksbr }}</p>
                </div>
                <div>
                    <label for="{{ form.reference_date.id_for_label }}" class="block text-sm font-medium text-gray-700">Ref. Date</label>
                    {{ form.reference_date }}
                    {% if form.reference_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.reference_date.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.reference_description.id_for_label }}" class="block text-sm font-medium text-gray-700">Ref. Description</label>
                    {{ form.reference_description }}
                    {% if form.reference_description.errors %}<p class="text-red-500 text-xs mt-1">{{ form.reference_description.errors }}</p>{% endif %}
                </div>
            </div>

            <div x-data="{ activeTab: 'sprItems' }" class="mb-8">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                        <button type="button" @click="activeTab = 'sprItems'" :class="{'border-blue-500 text-blue-600': activeTab === 'sprItems', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'sprItems'}" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                            SPR Items
                        </button>
                        <button type="button" @click="activeTab = 'selectedItems'" :class="{'border-blue-500 text-blue-600': activeTab === 'selectedItems', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'selectedItems'}" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                            Selected Items
                        </button>
                        <button type="button" @click="activeTab = 'termsConditions'" :class="{'border-blue-500 text-blue-600': activeTab === 'termsConditions', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'termsConditions'}" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                            Terms & Conditions
                        </button>
                    </nav>
                </div>

                <div x-show="activeTab === 'sprItems'" class="p-4 bg-gray-50 rounded-b-lg">
                    <iframe src="{{ spr_item_grid_iframe_url }}?Code={{ supplier_code_from_qs }}" class="w-full h-80 border border-gray-300 rounded" frameborder="0"></iframe>
                    <p class="text-sm text-gray-500 mt-2">
                        This section displays items from the SPR (Supplier Purchase Requisition).
                        (The content of this iframe is part of a separate module, its migration is out of scope for this specific request).
                    </p>
                </div>

                <div x-show="activeTab === 'selectedItems'" class="p-4 bg-gray-50 rounded-b-lg overflow-x-auto">
                    <div id="sprPoTempItemTable-container"
                         hx-trigger="load, refreshSprPoTempItemList from:body"
                         hx-get="{% url 'po_spr_items_table' %}"
                         hx-swap="innerHTML">
                        <!-- DataTable will be loaded here via HTMX -->
                        <div class="text-center py-8">
                            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                            <p class="mt-2 text-gray-600">Loading Selected Items...</p>
                        </div>
                    </div>
                </div>

                <div x-show="activeTab === 'termsConditions'" class="p-4 bg-gray-50 rounded-b-lg">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="mb-4">
                                <label for="{{ form.payment_terms.id_for_label }}" class="block text-sm font-medium text-gray-700">Payment Terms</label>
                                {{ form.payment_terms }}
                                {% if form.payment_terms.errors %}<p class="text-red-500 text-xs mt-1">{{ form.payment_terms.errors }}</p>{% endif %}
                            </div>
                            <div class="mb-4">
                                <label for="{{ form.freight.id_for_label }}" class="block text-sm font-medium text-gray-700">Freight</label>
                                {{ form.freight }}
                                {% if form.freight.errors %}<p class="text-red-500 text-xs mt-1">{{ form.freight.errors }}</p>{% endif %}
                            </div>
                            <div class="mb-4">
                                <label for="{{ form.octroi.id_for_label }}" class="block text-sm font-medium text-gray-700">Octroi</label>
                                {{ form.octroi }}
                                {% if form.octroi.errors %}<p class="text-red-500 text-xs mt-1">{{ form.octroi.errors }}</p>{% endif %}
                            </div>
                            <div class="mb-4">
                                <label for="{{ form.warranty.id_for_label }}" class="block text-sm font-medium text-gray-700">Warranty</label>
                                {{ form.warranty }}
                                {% if form.warranty.errors %}<p class="text-red-500 text-xs mt-1">{{ form.warranty.errors }}</p>{% endif %}
                            </div>
                            <div class="mb-4">
                                <label for="{{ form.insurance.id_for_label }}" class="block text-sm font-medium text-gray-700">Insurance</label>
                                {{ form.insurance }}
                                {% if form.insurance.errors %}<p class="text-red-500 text-xs mt-1">{{ form.insurance.errors }}</p>{% endif %}
                            </div>
                            <div class="mb-4">
                                <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700">Remarks</label>
                                {{ form.remarks }}
                                {% if form.remarks.errors %}<p class="text-red-500 text-xs mt-1">{{ form.remarks.errors }}</p>{% endif %}
                            </div>
                            <div class="mb-4 col-span-2">
                                <label for="{{ form.terms_conditions_text.id_for_label }}" class="block text-sm font-medium text-gray-700">Terms & Conditions Text</label>
                                {{ form.terms_conditions_text }}
                                {% if form.terms_conditions_text.errors %}<p class="text-red-500 text-xs mt-1">{{ form.terms_conditions_text.errors }}</p>{% endif %}
                            </div>
                        </div>
                        <div>
                            <div class="mb-4">
                                <label for="{{ form.ship_to.id_for_label }}" class="block text-sm font-medium text-gray-700">Ship To</label>
                                {{ form.ship_to }}
                                {% if form.ship_to.errors %}<p class="text-red-500 text-xs mt-1">{{ form.ship_to.errors }}</p>{% endif %}
                            </div>
                            <div class="mb-4">
                                <label for="{{ form.mode_of_dispatch.id_for_label }}" class="block text-sm font-medium text-gray-700">Mode of Dispatch</label>
                                {{ form.mode_of_dispatch }}
                                {% if form.mode_of_dispatch.errors %}<p class="text-red-500 text-xs mt-1">{{ form.mode_of_dispatch.errors }}</p>{% endif %}
                            </div>
                            <div class="mb-4">
                                <label for="{{ form.inspection.id_for_label }}" class="block text-sm font-medium text-gray-700">Inspection</label>
                                {{ form.inspection }}
                                {% if form.inspection.errors %}<p class="text-red-500 text-xs mt-1">{{ form.inspection.errors }}</p>{% endif %}
                            </div>
                            <div class="mb-4">
                                <label for="{{ form.attached_file_path.id_for_label }}" class="block text-sm font-medium text-gray-700">Annexure (File Upload)</label>
                                {{ form.attached_file_path }}
                                {% if form.attached_file_path.errors %}<p class="text-red-500 text-xs mt-1">{{ form.attached_file_path.errors }}</p>{% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-8 flex justify-end space-x-4">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm"
                        _="on click
                            if !confirm('Are you sure you want to generate the Purchase Order?')
                                halt the event">
                    Generate PO
                </button>
                <a href="{% url 'po_new_redirect' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm">
                    Cancel
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Modal for form/delete confirmation -->
<div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
     _="on click if event.target.id == 'modal' remove .is-active from me add .hidden to me">
    <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.tailwindcss.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.8/css/dataTables.tailwindcss.css">

<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css">

<script>
    // Initialize datepicker
    $(function() {
        $('.datepicker').datepicker({
            format: 'dd-mm-yyyy',
            autoclose: true,
            todayHighlight: true
        });
    });

    // Handle messages and modal for HTMX responses
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'modalContent' && event.detail.xhr.status === 200) {
            // Re-initialize datepicker if a form containing it is loaded into modal
            $('#modalContent .datepicker').datepicker({
                format: 'dd-mm-yyyy',
                autoclose: true,
                todayHighlight: true
            });
        }
    });

    document.body.addEventListener('htmx:afterRequest', function(event) {
        // If an HTMX request returned success (e.g., 200 for content, 204 for no content),
        // and it was meant to close the modal, then close it.
        // This is generic; specific triggers on successful form submissions are better.
        if (event.detail.xhr.status === 204 && event.detail.requestConfig.headers['HX-Trigger'] === 'refreshSprPoTempItemList') {
             const modal = document.getElementById('modal');
             modal.classList.add('hidden');
             modal.classList.remove('is-active');
        }
        // Handle redirect headers from HTMX (e.g., after successful PO generation or budget error)
        if (event.detail.xhr.getResponseHeader('HX-Redirect')) {
            window.location.href = event.detail.xhr.getResponseHeader('HX-Redirect');
        }
    });

    // Set initial tab state for Alpine.js if needed, or rely on default
    // let initialTabIndex = "{{ request.session.tab_index|default:'selectedItems' }}";
    // document.querySelector('[x-data]').__x.$data.activeTab = initialTabIndex;

</script>
{% endblock %}
```

**`purchase_orders/po_spr_items/_spr_temp_items_table.html`**

```html
{% load humanize %} {# For formatting numbers if needed #}
<div class="overflow-x-auto">
    <table id="sprPoTempItemTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SPR No</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BG Group</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Basic Amt</th>
                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Dis %</th>
                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Dis Amt</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PF</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ex/Ser Tax</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">VAT</th>
                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Taxable Amt</th>
                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amt</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Add Desc.</th>
                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">A/c Head</th>
                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Deli Date</th>
                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if spr_po_temp_items %}
                {% for item in spr_po_temp_items %}
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.spr_no }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.work_order_no }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.department_display }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.item_code_display }}</td>
                    <td class="px-6 py-4 whitespace-pre-wrap text-sm text-gray-900 max-w-xs">{{ item.purchase_description }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.uom_purchase }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900">{{ item.quantity|floatformat:3 }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900">{{ item.rate|floatformat:2 }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900">{{ item.basic_amount|floatformat:2 }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900">{{ item.discount_percentage|floatformat:2 }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900">{{ item.discount_amount|floatformat:2 }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.pf.terms }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.excise_service_tax.terms }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.vat.terms }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900">{{ item.taxable_amount|floatformat:2 }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900">{{ item.total_amount|floatformat:2 }}</td>
                    <td class="px-6 py-4 whitespace-pre-wrap text-sm text-gray-900 max-w-xs">{{ item.additional_description }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-900">{{ item.account_head_display }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-center text-gray-900">{{ item.delivery_date|date:"d-m-Y" }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                            class="text-red-600 hover:text-red-900 mr-2"
                            hx-get="{% url 'po_spr_items_delete' item.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal remove .hidden from #modal">
                            Delete
                        </button>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="21" class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium text-gray-500">
                        No data to display!
                    </td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists to prevent re-initialization errors
        if ($.fn.DataTable.isDataTable('#sprPoTempItemTable')) {
            $('#sprPoTempItemTable').DataTable().destroy();
        }
        $('#sprPoTempItemTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "scrollX": true
        });
    });
</script>
```

**`purchase_orders/po_spr_items/_confirm_delete_spr_item.html`**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete this SPR item?</p>
    
    <div class="flex justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click remove .is-active from #modal add .hidden to #modal">
            Cancel
        </button>
        <button 
            type="button" 
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
            hx-delete="{% url 'po_spr_items_delete' object.pk %}"
            hx-swap="none"
            _="on click remove .is-active from #modal add .hidden to #modal">
            Delete
        </button>
    </div>
</div>
```

#### 4.5 URLs (`purchase_orders/urls.py`)

```python
from django.urls import path
from .views import (
    PurchaseOrderCreateView,
    SprPoTempItemTablePartialView,
    SprPoTempItemDeleteView,
    SupplierAutocompleteAPIView,
    PoNewRedirectView,
    PoErrorRedirectView,
    PoSprItemGridIframeView # Placeholder for iframe content
)

urlpatterns = [
    path('po-spr-items/', PurchaseOrderCreateView.as_view(), name='po_spr_items_create'),
    path('po-spr-items/table/', SprPoTempItemTablePartialView.as_view(), name='po_spr_items_table'),
    path('po-spr-items/delete/<int:pk>/', SprPoTempItemDeleteView.as_view(), name='po_spr_items_delete'),
    
    # API for autocomplete
    path('api/suppliers/autocomplete/', SupplierAutocompleteAPIView.as_view(), name='supplier_autocomplete'),

    # Redirect paths mimicking ASP.NET
    path('po-new-redirect/', PoNewRedirectView.as_view(), name='po_new_redirect'),
    path('po-error-redirect/', PoErrorRedirectView.as_view(), name='po_error_redirect'),

    # Iframe content placeholder (for PO_SPR_ItemGrid.aspx)
    path('po-spr-item-grid/', PoSprItemGridIframeView.as_view(), name='po_spr_item_grid'),
]
```

#### 4.6 Tests (`purchase_orders/tests.py`)

This section provides comprehensive tests for models and views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import date, time
from unittest.mock import patch, MagicMock
from io import BytesIO

from .models import (
    PurchaseOrder, SprPoTempItem, PurchaseOrderDetail, RateLockUnlockMaster,
    SupplierMaster, PoReference, PaymentTerms, FreightMaster, OctroiMaster,
    WarrantyMaster, PackingMaster, VatMaster, ExciseServiceTaxMaster,
    BudgetCode, BusinessGroup, AccountHead, UnitMaster, ItemMaster, SprDetail,
    Company, FinancialYear, PoTerms
)

class MasterDataSetupMixin(TestCase):
    """Mixin to set up common master data for tests."""
    @classmethod
    def setUpTestData(cls):
        # Create minimal master data required by models and forms
        cls.company = Company.objects.create(id=1, name='Test Company')
        cls.financial_year = FinancialYear.objects.create(id=1, year_name='2024-2025')
        cls.supplier = SupplierMaster.objects.create(
            supplier_id='SUP001',
            supplier_name='Test Supplier',
            regd_address='123 Test St',
            regd_city_id=1, regd_state_id=1, regd_country_id=1, regd_pin_no='123456'
        )
        cls.po_ref = PoReference.objects.create(id=1, ref_desc='Standard PO')
        cls.payment_terms = PaymentTerms.objects.create(id=1, terms='Net 30 Days')
        cls.freight = FreightMaster.objects.create(id=1, terms='FOB Destination')
        cls.octroi = OctroiMaster.objects.create(id=1, terms='As Actual')
        cls.warranty = WarrantyMaster.objects.create(id=1, terms='1 Year')
        cls.packing = PackingMaster.objects.create(id=1, terms='Standard Packing', value=5.0)
        cls.vat = VatMaster.objects.create(id=1, terms='VAT @ 10%', value=10.0)
        cls.excise_service_tax = ExciseServiceTaxMaster.objects.create(id=1, terms='ExST @ 8%', value=8.0)
        cls.budget_code = BudgetCode.objects.create(id=1, symbol='PROJ001', description='Project ABC Budget')
        cls.business_group = BusinessGroup.objects.create(id=1, symbol='ENGG')
        cls.account_head = AccountHead.objects.create(id=1, symbol='RAW_MAT')
        cls.unit = UnitMaster.objects.create(id=1, symbol='PCS')
        cls.item = ItemMaster.objects.create(
            id=1, item_code='ITEM001', manufacturer_description='Test Item Description',
            uom_basic=cls.unit, company=cls.company
        )
        cls.spr_detail = SprDetail.objects.create(
            id=1, spr_no='SPR001', item=cls.item, department=cls.business_group,
            work_order_no='WO001', account_head=cls.account_head, budget_code=cls.budget_code
        )
        # Add default PO terms
        PoTerms.objects.create(id=1, terms="Term 1: Payment within 30 days.")
        PoTerms.objects.create(id=2, terms="Term 2: Delivery to site.")


class SprPoTempItemModelTest(MasterDataSetupMixin, TestCase):
    def setUp(self):
        super().setUp()
        self.temp_item = SprPoTempItem.objects.create(
            id=1,
            spr_no='SPR001',
            session_id='test_session_id',
            company_id=self.company.id,
            spr_detail=self.spr_detail,
            quantity=10.000,
            rate=100.00,
            discount_percentage=5.00,
            additional_description='Extra details for item',
            pf=self.packing,
            excise_service_tax=self.excise_service_tax,
            vat=self.vat,
            delivery_date=date(2024, 12, 31),
            budget_code=self.budget_code
        )

    def test_spr_po_temp_item_creation(self):
        self.assertEqual(self.temp_item.spr_no, 'SPR001')
        self.assertEqual(self.temp_item.quantity, 10.000)
        self.assertEqual(self.temp_item.rate, 100.00)
        self.assertEqual(self.temp_item.discount_percentage, 5.00)
        self.assertEqual(self.temp_item.delivery_date, date(2024, 12, 31))
        self.assertEqual(self.temp_item.spr_detail.item.item_code, 'ITEM001')

    def test_item_code_display(self):
        self.assertEqual(self.temp_item.item_code_display, 'ITEM001')

    def test_purchase_description(self):
        self.assertEqual(self.temp_item.purchase_description, 'Test Item Description')

    def test_uom_purchase(self):
        self.assertEqual(self.temp_item.uom_purchase, 'PCS')

    def test_account_head_display(self):
        self.assertEqual(self.temp_item.account_head_display, 'RAW_MAT')
        
        # Test 'NA' case if no account_head
        self.spr_detail.account_head = None
        self.spr_detail.save()
        self.assertEqual(self.temp_item.account_head_display, 'NA')


    def test_work_order_no(self):
        self.assertEqual(self.temp_item.work_order_no, 'WO001')
        self.spr_detail.work_order_no = None
        self.spr_detail.save()
        self.assertEqual(self.temp_item.work_order_no, 'NA')

    def test_department_display(self):
        self.assertEqual(self.temp_item.department_display, 'ENGG')
        self.spr_detail.department = None
        self.spr_detail.save()
        self.assertEqual(self.temp_item.department_display, 'NA')

    def test_basic_amount_calculation(self):
        self.assertAlmostEqual(self.temp_item.basic_amount, 10.000 * 100.00)

    def test_discount_amount_calculation(self):
        expected_discount = (10.000 * 100.00) * (5.00 / 100)
        self.assertAlmostEqual(self.temp_item.discount_amount, expected_discount)

    def test_taxable_amount_calculation(self):
        expected_taxable = (10.000 * 100.00) - ((10.000 * 100.00) * (5.00 / 100))
        self.assertAlmostEqual(self.temp_item.taxable_amount, expected_taxable)

    def test_total_tax_percentage_calculation(self):
        expected_total_tax = self.packing.value + self.excise_service_tax.value + self.vat.value
        self.assertAlmostEqual(self.temp_item.total_tax_percentage, expected_total_tax)

    def test_tax_amount_calculation(self):
        taxable_amount = (10.000 * 100.00) - ((10.000 * 100.00) * (5.00 / 100))
        total_tax_percent = self.packing.value + self.excise_service_tax.value + self.vat.value
        expected_tax_amount = taxable_amount * (total_tax_percent / 100)
        self.assertAlmostEqual(self.temp_item.tax_amount, expected_tax_amount)

    def test_total_amount_calculation(self):
        basic_amount = 1000.00
        discount_amount = 50.00
        taxable_amount = basic_amount - discount_amount # 950
        total_tax_percent = 5.0 + 8.0 + 10.0 # 23.0
        tax_amount = taxable_amount * (total_tax_percent / 100) # 950 * 0.23 = 218.5
        expected_total_amount = taxable_amount + tax_amount # 950 + 218.5 = 1168.5

        # Update temp_item for specific values for easier testing
        self.temp_item.quantity = 1.0
        self.temp_item.rate = 1000.00
        self.temp_item.discount_percentage = 5.0
        self.temp_item.pf.value = 5.0
        self.temp_item.excise_service_tax.value = 8.0
        self.temp_item.vat.value = 10.0
        self.temp_item.save()

        self.assertAlmostEqual(self.temp_item.total_amount, expected_total_amount)


class PurchaseOrderModelTest(MasterDataSetupMixin, TestCase):
    def setUp(self):
        super().setUp()
        self.po = PurchaseOrder.objects.create(
            id=1,
            session_id='test_session_id',
            company_id=self.company.id,
            financial_year_id=self.financial_year.id,
            po_no='0001',
            supplier=self.supplier,
            reference_type=self.po_ref,
            reference_date=date(2024, 7, 1),
            payment_terms=self.payment_terms,
            warranty=self.warranty,
            freight=self.freight,
            octroi=self.octroi,
            ship_to='Company Address',
        )

    def test_purchase_order_creation(self):
        self.assertEqual(self.po.po_no, '0001')
        self.assertEqual(self.po.supplier.supplier_name, 'Test Supplier')
        self.assertTrue(PurchaseOrder.objects.filter(po_no='0001').exists())

    def test_generate_po_number_first_po(self):
        PurchaseOrder.objects.all().delete() # Ensure no existing POs
        new_po_no = PurchaseOrder.generate_po_number(self.company.id, self.financial_year.id)
        self.assertEqual(new_po_no, '0001')

    def test_generate_po_number_next_po(self):
        PurchaseOrder.objects.create(
            session_id='test_session_id',
            company_id=self.company.id,
            financial_year_id=self.financial_year.id,
            po_no='0001',
            supplier=self.supplier,
            reference_type=self.po_ref,
            reference_date=date(2024, 7, 1),
            payment_terms=self.payment_terms,
            warranty=self.warranty,
            freight=self.freight,
            octroi=self.octroi,
            ship_to='Company Address',
        )
        new_po_no = PurchaseOrder.generate_po_number(self.company.id, self.financial_year.id)
        self.assertEqual(new_po_no, '0002')

    @patch.object(PurchaseOrder, '_get_budget_for_wono', return_value=1000.0)
    @patch.object(PurchaseOrder, '_get_budget_for_bg', return_value=1000.0)
    def test_perform_budget_validation_sufficient(self, mock_wono_budget, mock_bg_budget):
        temp_item_sufficient = SprPoTempItem.objects.create(
            spr_no='SPR002', session_id='test_session_id', company_id=self.company.id,
            spr_detail=self.spr_detail, quantity=1.0, rate=500.0, discount_percentage=0.0,
            pf=self.packing, excise_service_tax=self.excise_service_tax, vat=self.vat,
            delivery_date=date(2024, 12, 31), budget_code=self.budget_code
        )
        is_sufficient, insufficient_budgets = self.po.perform_budget_validation(
            self.company.id, self.financial_year.id, [temp_item_sufficient]
        )
        self.assertTrue(is_sufficient)
        self.assertEqual(len(insufficient_budgets), 0)

    @patch.object(PurchaseOrder, '_get_budget_for_wono', return_value=100.0) # Insufficient budget
    @patch.object(PurchaseOrder, '_get_budget_for_bg', return_value=100.0) # Insufficient budget
    def test_perform_budget_validation_insufficient(self, mock_wono_budget, mock_bg_budget):
        temp_item_insufficient = SprPoTempItem.objects.create(
            spr_no='SPR003', session_id='test_session_id', company_id=self.company.id,
            spr_detail=self.spr_detail, quantity=1.0, rate=5000.0, discount_percentage=0.0,
            pf=self.packing, excise_service_tax=self.excise_service_tax, vat=self.vat,
            delivery_date=date(2024, 12, 31), budget_code=self.budget_code
        )
        is_sufficient, insufficient_budgets = self.po.perform_budget_validation(
            self.company.id, self.financial_year.id, [temp_item_insufficient]
        )
        self.assertFalse(is_sufficient)
        self.assertEqual(len(insufficient_budgets), 1)
        self.assertEqual(insufficient_budgets[0]['budget_code'], self.budget_code.symbol)
        self.assertAlmostEqual(insufficient_budgets[0]['balance_amount'], 100.0)


class RateLockUnlockMasterTest(MasterDataSetupMixin, TestCase):
    def setUp(self):
        super().setUp()
        RateLockUnlockMaster.objects.create(
            item=self.item, type=1, lock_unlock=True, company=self.company
        )
        RateLockUnlockMaster.objects.create(
            item=self.item, type=2, lock_unlock=True, company=self.company
        )

    def test_lock_item_rates(self):
        transaction_no = 'PO001'
        RateLockUnlockMaster.lock_item_rates(self.item.id, transaction_no, self.company.id)

        locked_rates = RateLockUnlockMaster.objects.filter(item=self.item, company=self.company)
        for rate_entry in locked_rates:
            self.assertFalse(rate_entry.lock_unlock) # Should be False (0)
            self.assertEqual(rate_entry.locked_by_transaction, transaction_no)
            self.assertEqual(rate_entry.lock_date, timezone.now().date())
            self.assertEqual(rate_entry.lock_time.hour, timezone.now().time().hour)


class PurchaseOrderViewsTest(MasterDataSetupMixin, TestCase):
    def setUp(self):
        super().setUp()
        self.client = Client()
        # Mock session variables for the client
        self.client.session['username'] = 'test_user_session'
        self.client.session['compid'] = self.company.id
        self.client.session['finyear'] = self.financial_year.id
        self.main_url = reverse('po_spr_items_create')
        self.table_partial_url = reverse('po_spr_items_table')
        self.delete_url_template = 'po_spr_items_delete'
        self.autocomplete_url = reverse('supplier_autocomplete')

        self.temp_item = SprPoTempItem.objects.create(
            id=1,
            spr_no='SPR001',
            session_id='test_user_session',
            company_id=self.company.id,
            spr_detail=self.spr_detail,
            quantity=1.0,
            rate=100.00,
            discount_percentage=0.0,
            additional_description='Temp Item',
            pf=self.packing,
            excise_service_tax=self.excise_service_tax,
            vat=self.vat,
            delivery_date=date(2024, 12, 31),
            budget_code=self.budget_code
        )

    def test_po_create_view_get(self):
        response = self.client.get(self.main_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'purchase_orders/po_spr_items/main_form.html')
        self.assertIn('form', response.context)
        self.assertIn('supplier_address', response.context)
        self.assertIn('spr_po_temp_items', response.context) # Should be available via HX-GET

    def test_po_create_view_get_with_supplier_code_qs(self):
        url = f"{self.main_url}?Code={self.supplier.supplier_id}"
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.supplier.supplier_name)
        self.assertContains(response, self.supplier.full_address().replace('\n', '<br>')) # Address displayed in template

    @patch.object(PurchaseOrder, 'perform_budget_validation', return_value=(True, []))
    @patch.object(PurchaseOrder, 'generate_po_number', return_value='0001')
    def test_po_create_view_post_success(self, mock_generate_po, mock_budget_validation):
        form_data = {
            'supplier_display': f'{self.supplier.supplier_name} [{self.supplier.supplier_id}]',
            'supplier': self.supplier.supplier_id,
            'reference_type': self.po_ref.id,
            'reference_date': '01-07-2024', # Formatted date
            'reference_description': 'Test PO from Django',
            'payment_terms': self.payment_terms.id,
            'freight': self.freight.id,
            'octroi': self.octroi.id,
            'warranty': self.warranty.id,
            'insurance': '100.00',
            'remarks': 'Some remarks',
            'terms_conditions_text': 'All standard terms apply.',
            'ship_to': 'Test Ship To Address',
            'mode_of_dispatch': 'Air Cargo',
            'inspection': 'Pre-dispatch Inspection',
        }
        # Simulate file upload
        uploaded_file = BytesIO(b"test file content")
        uploaded_file.name = "test_document.pdf"
        uploaded_file.content_type = "application/pdf"
        files = {'attached_file_path': uploaded_file}

        response = self.client.post(self.main_url, data=form_data, files=files, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.headers.get('HX-Redirect'), reverse_lazy('po_new_redirect'))
        self.assertTrue(PurchaseOrder.objects.filter(po_no='0001').exists())
        self.assertEqual(PurchaseOrderDetail.objects.count(), 1) # One item from temp_item
        self.assertEqual(SprPoTempItem.objects.count(), 0) # Temp item should be deleted
        mock_budget_validation.assert_called_once()
        mock_generate_po.assert_called_once()

    @patch.object(PurchaseOrder, 'perform_budget_validation', return_value=(False, [{'wono_bg': 'WO001', 'budget_code': 'PROJ001', 'description': 'Project ABC Budget', 'balance_amount': 50.0}]))
    def test_po_create_view_post_budget_failure(self, mock_budget_validation):
        form_data = {
            'supplier_display': f'{self.supplier.supplier_name} [{self.supplier.supplier_id}]',
            'supplier': self.supplier.supplier_id,
            'reference_type': self.po_ref.id,
            'reference_date': '01-07-2024',
            'reference_description': 'Test PO',
            'payment_terms': self.payment_terms.id,
            'freight': self.freight.id,
            'octroi': self.octroi.id,
            'warranty': self.warranty.id,
        }
        response = self.client.post(self.main_url, data=form_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.headers.get('HX-Redirect'), reverse_lazy('po_error_redirect'))
        messages_list = list(messages.get_messages(response.wsgi_request))
        self.assertEqual(len(messages_list), 1)
        self.assertIn("Insufficient budget", str(messages_list[0]))
        self.assertTrue(SprPoTempItem.objects.exists()) # Should not delete temp items on budget failure

    def test_po_create_view_post_no_temp_items(self):
        SprPoTempItem.objects.all().delete() # Remove temp item
        form_data = {
            'supplier_display': f'{self.supplier.supplier_name} [{self.supplier.supplier_id}]',
            'supplier': self.supplier.supplier_id,
            'reference_type': self.po_ref.id,
            'reference_date': '01-07-2024',
            'reference_description': 'Test PO',
            'payment_terms': self.payment_terms.id,
            'freight': self.freight.id,
            'octroi': self.octroi.id,
            'warranty': self.warranty.id,
        }
        response = self.client.post(self.main_url, data=form_data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 200)
        messages_list = list(messages.get_messages(response.wsgi_request))
        self.assertEqual(len(messages_list), 1)
        self.assertIn("No SPR items selected", str(messages_list[0]))
        self.assertTemplateUsed(response, 'purchase_orders/po_spr_items/main_form.html') # Should re-render form

    def test_spr_po_temp_item_table_partial_view(self):
        response = self.client.get(self.table_partial_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'purchase_orders/po_spr_items/_spr_temp_items_table.html')
        self.assertIn('spr_po_temp_items', response.context)
        self.assertEqual(len(response.context['spr_po_temp_items']), 1)
        self.assertContains(response, 'ITEM001') # Check for item in table

    def test_spr_po_temp_item_delete_view_get(self):
        response = self.client.get(reverse(self.delete_url_template, args=[self.temp_item.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'purchase_orders/po_spr_items/_confirm_delete_spr_item.html')
        self.assertContains(response, 'Are you sure you want to delete this SPR item?')

    def test_spr_po_temp_item_delete_view_delete(self):
        response = self.client.delete(reverse(self.delete_url_template, args=[self.temp_item.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success code for no content
        self.assertFalse(SprPoTempItem.objects.filter(pk=self.temp_item.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSprPoTempItemList')
        messages_list = list(messages.get_messages(response.wsgi_request))
        self.assertEqual(len(messages_list), 1)
        self.assertIn('deleted successfully', str(messages_list[0]))

    def test_supplier_autocomplete_api_view(self):
        response = self.client.get(self.autocomplete_url, {'supplier_display': 'Test'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = response.json()
        self.assertIsInstance(data, list)
        self.assertIn(f'{self.supplier.supplier_name} [{self.supplier.supplier_id}]', data)

        response = self.client.get(self.autocomplete_url, {'supplier_display': 'NonExistent'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), [])

        response = self.client.get(self.autocomplete_url, {'supplier_display': ''}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), [])

    def test_po_new_redirect_view(self):
        response = self.client.get(reverse('po_new_redirect'))
        self.assertEqual(response.status_code, 302) # Redirect
        messages_list = list(messages.get_messages(response.wsgi_request))
        self.assertEqual(len(messages_list), 1)
        self.assertIn("Redirected to PO_new equivalent page.", str(messages_list[0]))

    def test_po_error_redirect_view(self):
        response = self.client.get(reverse('po_error_redirect'))
        self.assertEqual(response.status_code, 302) # Redirect
        messages_list = list(messages.get_messages(response.wsgi_request))
        self.assertEqual(len(messages_list), 1)
        self.assertIn("Budget validation failed.", str(messages_list[0]))

```