## ASP.NET to Django Conversion Script: Comprehensive Modernization Plan for SPR Item Entry

This document outlines a comprehensive modernization plan to transition your legacy ASP.NET `SPR_NoCode.aspx` application to a modern Django 5.0+ solution. Our approach prioritizes AI-assisted automation, clean architecture, and enhanced user experience through HTMX and Alpine.js.

### Business Value Proposition

Migrating this ASP.NET component to Django will deliver significant business benefits:

1.  **Reduced Technical Debt:** Eliminating outdated ASP.NET Web Forms technology and moving to a modern framework like Django reduces ongoing maintenance costs and security vulnerabilities.
2.  **Improved Developer Productivity:** Django's "batteries-included" philosophy, clear structure, and powerful ORM accelerate development cycles, allowing new features to be delivered faster.
3.  **Enhanced User Experience:** The use of HTMX and Alpine.js will enable highly interactive and responsive user interfaces without complex JavaScript, leading to a smoother and more efficient data entry process.
4.  **Scalability & Performance:** Django is designed for scalability, allowing your application to handle increasing data volumes and user traffic more efficiently.
5.  **Simplified Deployment:** Django applications are easier to deploy and manage, often leveraging modern containerization (e.g., Docker) and cloud platforms.
6.  **Maintainability & Testability:** The "Fat Model, Thin View" architecture, coupled with comprehensive unit and integration tests, ensures a highly maintainable and robust codebase, reducing future bug occurrences.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:

-   NEVER include base.html template code in your output - assume it already exists
-   Focus ONLY on component-specific code for the current module
-   Always include complete unit tests for models and integration tests for views
-   Use modern Django 5.0+ patterns and follow best practices
-   Keep your code clean, efficient, and avoid redundancy
-   Always generate complete, runnable Django code

## AutoERP Guidelines:

-   Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
-   Map models to existing database using `managed = False` and `db_table`
-   Implement DataTables for client-side searching, sorting, and pagination
-   Use HTMX for dynamic interactions and Alpine.js for UI state management
-   All templates should extend `core/base.html` (but DO NOT include `base.html` code)
-   Achieve at least 80% test coverage with unit and integration tests
-   Follow DRY (Don't Repeat Yourself) principle throughout the codebase
-   Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

This component interacts with several database tables, primarily inserting data into `tblMM_SPR_Temp` and retrieving lookup information from others.

**Inferred Tables and Columns:**

*   **`tblMM_SPR_Temp`** (Main transaction table for SPR line items)
    *   `Id` (Primary Key, inferred auto-increment)
    *   `SysDate` (Date)
    *   `SysTime` (Time)
    *   `CompId` (Integer)
    *   `FinYearId` (Integer)
    *   `SessionId` (String, maps to username)
    *   `ItemId` (Integer, Foreign Key to `tblDG_Item_Master`)
    *   `SupplierId` (String, Foreign Key to `tblMM_Supplier_master`'s `SupplierId` code)
    *   `Qty` (Decimal)
    *   `Rate` (Decimal)
    *   `AHId` (Integer, Foreign Key to `AccHead`)
    *   `WONo` (String, nullable)
    *   `DeptId` (String, Foreign Key to `BusinessGroup`'s `Id`)
    *   `Remarks` (String, nullable)
    *   `DelDate` (Date)
    *   `Discount` (Decimal)

*   **`tblDG_Item_Master`** (Item lookup)
    *   `Id` (Primary Key)
    *   `ItemCode` (String)
    *   `ManfDesc` (String, Manufacturer Description)
    *   `UOMBasic` (Integer, Foreign Key to `Unit_Master`)
    *   `AHId` (Integer, Foreign Key to `AccHead`, often an account head ID)
    *   `CId` (Integer, inferred, seems like another category/account ID, nullable)
    *   `CompId` (Integer)

*   **`Unit_Master`** (Unit of Measurement lookup)
    *   `Id` (Primary Key)
    *   `Symbol` (String)

*   **`tblMM_Rate_Register`** (Rate lookup)
    *   `Id` (Primary Key)
    *   `Rate` (Decimal)
    *   `Discount` (Decimal)
    *   `Flag` (Integer, inferred boolean)
    *   `ItemId` (Integer, Foreign Key to `tblDG_Item_Master`)
    *   `CompId` (Integer)

*   **`tblMM_RateLockUnLock_Master`** (Rate lock/unlock configuration)
    *   `ItemId` (Integer, Foreign Key to `tblDG_Item_Master`)
    *   `CompId` (Integer)
    *   `LockUnlock` (Integer, inferred boolean, `1` for locked)
    *   `Type` (Integer, `1` for specific type)

*   **`tblMM_Supplier_master`** (Supplier lookup)
    *   `SupplierId` (String, used as unique code, not necessarily PK)
    *   `SupplierName` (String)
    *   `CompId` (Integer)

*   **`AccHead`** (Account Head lookup)
    *   `Id` (Primary Key)
    *   `Category` (String, e.g., "Labour", "With Material")
    *   `Symbol` (String)
    *   `Description` (String)

*   **`BusinessGroup`** (Department/Business Group lookup)
    *   `Id` (Primary Key)
    *   `Symbol` (String, used as Dept)

### Step 2: Identify Backend Functionality

The ASP.NET page primarily handles the "Create" operation for a new SPR line item (`tblMM_SPR_Temp`), with extensive "Read" functionality for populating fields and performing complex validations.

*   **Read (Data Pre-population and Lookups):**
    *   Retrieving item details (`ItemCode`, `Description`, `UOM`) based on `ItemId` from query string.
    *   Fetching initial `Rate` and `Discount` from `tblMM_Rate_Register` based on `ItemId` and `CompId`. This includes complex logic to find a flagged rate or the minimum discounted rate.
    *   Dynamically populating "A/c Head" dropdown (`DropDownList1`) based on radio button selection (`RbtnLabour`/`RbtnWithMaterial`), querying `AccHead`.
    *   Providing auto-completion suggestions for "Supplier" (`txtNewCustomerName`) from `tblMM_Supplier_master`.
    *   Populating "BG Group" dropdown (`drpdept`) from `BusinessGroup`.
    *   Validating WO No (`fun.CheckValidWONo`).
    *   Validating Supplier Code (`fun.chkSupplierCode`).
    *   Validating Delivery Date format and number quantities (`fun.DateValidation`, `fun.NumberValidationQty`).
    *   Checking rate lock status (`tblMM_RateLockUnLock_Master`).

*   **Create (Form Submission):**
    *   Inserting a new record into `tblMM_SPR_Temp` on `btnAdd_Click`.
    *   This involves gathering all form data, performing server-side validations (including the complex rate acceptance logic), and determining `AHId`, `WONo`, or `DeptId` based on user selections.
    *   Redirection upon successful addition or displaying alerts on failure.

*   **Navigation:**
    *   `btnCancel2_Click` redirects to `SPR_New.aspx`.
    *   The `rt` link navigates to a `RateRegisterSingleItemPrint.aspx` report for the current item.

### Step 3: Infer UI Components

The ASP.NET controls will be replaced by standard HTML form elements with Tailwind CSS for styling, powered by Django forms, HTMX, and Alpine.js for dynamic behavior.

*   **Labels:** `lbltIemCode`, `lblUOMBasic`, `lblManfDescription`, `lblAHId` will become Django template variables.
*   **Text Boxes:** `txtQty`, `txtRate`, `txtDiscount`, `txtNewCustomerName`, `txtwono`, `textDelDate`, `txtRemark` will map to Django form fields (e.g., `DecimalField`, `CharField`, `DateField`). `textDelDate` will use HTML5 `type="date"` for native date picking.
*   **Validators:** ASP.NET validators (`RequiredFieldValidator`, `RegularExpressionValidator`) will be replaced by Django form field validation rules.
*   **Auto-completion (`AutoCompleteExtender`):** For `txtNewCustomerName`, this will be handled by an HTMX `hx-post` or `hx-get` to a search endpoint, returning a list of suggestions that Alpine.js can use to render a dynamic dropdown/list.
*   **Radio Buttons:** `RbtnLabour`, `RbtnWithMaterial`, `rdwono`, `rddept` will be HTML radio inputs. Their `AutoPostBack` functionality will be mimicked by HTMX `hx-post` or `hx-get` to dynamically update related form parts (like the A/c Head dropdown).
*   **Dropdowns (`DropDownList1`, `drpdept`):** HTML `<select>` elements populated by Django form choices or HTMX-loaded partials.
*   **Buttons:** `btnAdd`, `btnCancel2` will be HTML `<button>` elements. `btnAdd` will use `hx-post` for form submission, and `btnCancel2` will be a simple link/button back. The `confirmationAdd()` JavaScript will be integrated into the HTMX flow or Alpine.js event handling.
*   **Report Link (`rt`):** A standard HTML `<a>` tag in the template.

### Step 4: Generate Django Code

We will create a Django application named `spr` to encapsulate this functionality.

#### 4.1 Models

We will define models for all identified database tables, ensuring `managed = False` as per the migration guidelines.

```python
# spr/models.py
from django.db import models
from django.utils import timezone
from decimal import Decimal, InvalidOperation
from django.core.exceptions import ValidationError

# Helper function to get CompId and FinYearId from current context/session
# In a real app, these might come from a Company/FinancialYear model linked to the User
def get_current_company_id():
    # Placeholder: In a real app, this would come from the current user's profile
    # or a session variable if multi-company is managed.
    return 1 # Example static ID

def get_current_financial_year_id():
    # Placeholder: In a real app, this would come from the current financial period setup.
    return 2024 # Example static ID

class Unit(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit of Measure'
        verbose_name_plural = 'Units of Measure'

    def __str__(self):
        return self.symbol

class AccountHead(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    category = models.CharField(db_column='Category', max_length=100)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    description = models.CharField(db_column='Description', max_length=255)

    class Meta:
        managed = False
        db_table = 'AccHead'
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'

    def __str__(self):
        return f"[{self.symbol}] {self.description}"

    @classmethod
    def get_heads_by_category(cls, category):
        """Fetches account heads filtered by category."""
        return cls.objects.filter(category=category).order_by('symbol')

class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, unique=True) # Assuming Symbol is unique for Dept

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255)
    uom_basic = models.ForeignKey(Unit, models.DO_NOTHING, db_column='UOMBasic')
    ah_id = models.ForeignKey(AccountHead, models.DO_NOTHING, db_column='AHId', related_name='items_as_ah', null=True, blank=True)
    c_id = models.IntegerField(db_column='CId', null=True, blank=True)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return f"{self.item_code} - {self.manf_desc}"

    @classmethod
    def get_item_details(cls, item_id, comp_id):
        """Fetches detailed item information."""
        try:
            return cls.objects.select_related('uom_basic', 'ah_id').get(id=item_id, comp_id=comp_id)
        except cls.DoesNotExist:
            return None

class RateRegister(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=3)
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=3)
    flag = models.IntegerField(db_column='Flag') # 1 for flagged, 0 for others
    item = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId')
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_Rate_Register'
        verbose_name = 'Rate Register'
        verbose_name_plural = 'Rate Registers'

    def __str__(self):
        return f"Item: {self.item.item_code}, Rate: {self.rate}, Discount: {self.discount}"

    @property
    def discounted_rate(self):
        """Calculates (Rate - (Rate * Discount / 100))"""
        return (self.rate - (self.rate * self.discount / Decimal('100'))).quantize(Decimal('0.00'))

    @classmethod
    def get_initial_rates(cls, item_id, comp_id):
        """
        Retrieves initial rate and discount based on ASP.NET logic:
        1. Rate with Flag=1
        2. Else, minimum discounted rate (DiscRate Asc)
        """
        try:
            # Try to find a flagged rate first
            flagged_rate = cls.objects.filter(item_id=item_id, comp_id=comp_id, flag=1).first()
            if flagged_rate:
                return flagged_rate.rate, flagged_rate.discount
            
            # If no flagged rate, find the minimum discounted rate
            min_disc_rate_obj = cls.objects.filter(item_id=item_id, comp_id=comp_id).order_by('discounted_rate').first()
            if min_disc_rate_obj:
                return min_disc_rate_obj.rate, min_disc_rate_obj.discount
        except Exception as e:
            # Log this error in a real application
            print(f"Error fetching initial rates: {e}")
        return None, None # Return None if no rates found

class RateLockUnlock(models.Model):
    item = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId', primary_key=True) # Assuming ItemId is part of PK
    comp_id = models.IntegerField(db_column='CompId')
    lock_unlock = models.IntegerField(db_column='LockUnlock') # 1 for locked, 0 for unlocked
    type = models.IntegerField(db_column='Type') # 1 for specific type

    class Meta:
        managed = False
        db_table = 'tblMM_RateLockUnLock_Master'
        verbose_name = 'Rate Lock/Unlock'
        verbose_name_plural = 'Rate Lock/Unlocks'
        unique_together = (('item', 'comp_id', 'type'),) # Adjust unique_together if needed based on actual DB schema

    @classmethod
    def is_rate_locked_for_type(cls, item_id, comp_id, lock_type=1):
        """Checks if rate is locked for a specific item, company, and type."""
        return cls.objects.filter(item_id=item_id, comp_id=comp_id, lock_unlock=1, type=lock_type).exists()

class Supplier(models.Model):
    # SupplierId is a CHAR/VARCHAR in ASP.NET code, not necessarily PK, but unique code.
    # Assuming SupplierId is what the code uses as the lookup key.
    supplier_id = models.CharField(db_column='SupplierId', max_length=50, unique=True, primary_key=True) 
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return f"{self.supplier_name} [{self.supplier_id}]"

    @classmethod
    def get_supplier_by_code(cls, supplier_code, comp_id):
        """Retrieves a supplier by their code."""
        try:
            return cls.objects.get(supplier_id=supplier_code, comp_id=comp_id)
        except cls.DoesNotExist:
            return None

    @classmethod
    def search_suppliers(cls, prefix_text, comp_id, count=10):
        """Searches suppliers by name for autocomplete."""
        # This matches the ASP.NET logic of startsWith, case-insensitive.
        return cls.objects.filter(comp_id=comp_id, supplier_name__istartswith=prefix_text).order_by('supplier_name')[:count]


class SPRTemp(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True) # Assuming auto-incremented PK
    sys_date = models.DateField(db_column='SysDate', default=timezone.now)
    sys_time = models.TimeField(db_column='SysTime', default=timezone.now)
    comp_id = models.IntegerField(db_column='CompId') # Will be set from session/user
    fin_year_id = models.IntegerField(db_column='FinYearId') # Will be set from session/user
    session_id = models.CharField(db_column='SessionId', max_length=255) # Will be set from request.user.username
    item = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId')
    supplier = models.ForeignKey(Supplier, models.DO_NOTHING, db_column='SupplierId') # Uses SupplierId (string) as FK
    quantity = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3)
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=3)
    account_head = models.ForeignKey(AccountHead, models.DO_NOTHING, db_column='AHId')
    wo_no = models.CharField(db_column='WONo', max_length=100, blank=True, null=True)
    department = models.ForeignKey(BusinessGroup, models.DO_NOTHING, db_column='DeptId', null=True, blank=True)
    remarks = models.TextField(db_column='Remarks', blank=True, null=True)
    delivery_date = models.DateField(db_column='DelDate')
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=3)

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Temp'
        verbose_name = 'SPR Temporary Entry'
        verbose_name_plural = 'SPR Temporary Entries'

    def __str__(self):
        return f"SPR Entry for {self.item.item_code} - Qty: {self.quantity}"

    @property
    def calculated_discounted_rate(self):
        """Calculates (Rate - (Rate * Discount / 100)) for the current instance."""
        return (self.rate - (self.rate * self.discount / Decimal('100'))).quantize(Decimal('0.00'))

    @classmethod
    def check_valid_wo_no(cls, wo_no, comp_id, fin_year_id):
        """
        Placeholder for ASP.NET's fun.CheckValidWONo.
        In a real system, this would query a WorkOrder or similar table.
        For now, we'll assume it's valid if not empty.
        """
        return wo_no and wo_no.strip() != "" # Simplified logic

    @classmethod
    def process_spr_entry(cls, item_id, form_data, user_session_info):
        """
        Encapsulates the complex business logic for adding an SPR entry.
        This method replaces much of the btnAdd_Click logic.
        user_session_info should contain: user, comp_id, fin_year_id
        """
        comp_id = user_session_info['comp_id']
        fin_year_id = user_session_info['fin_year_id']
        session_id = user_session_info['username'] # Or user.id

        # 1. Retrieve item details and determine AHId
        item_obj = ItemMaster.get_item_details(item_id, comp_id)
        if not item_obj:
            raise ValidationError("Invalid Item selected.")

        ah_id = None
        if item_obj.c_id is not None:
            # If item has CId, use its pre-defined AHId
            ah_id = item_obj.ah_id_id # Get the ID of the related AccountHead
        else:
            # Otherwise, use the one selected from DropDownList1
            ah_id = form_data.get('account_head')
            if not ah_id:
                raise ValidationError("Account Head is required for this item.")
            try:
                ah_id = int(ah_id)
            except (ValueError, TypeError):
                raise ValidationError("Invalid Account Head selected.")

        # 2. Determine WONo or DeptId
        wo_no = None
        dept_id = None
        is_wo_checked = form_data.get('rdwono_checked') == 'on' # Checkbox value might be 'on' for checked
        is_dept_checked = form_data.get('rddept_checked') == 'on'
        
        # ASP.NET original logic allows both radio buttons to be unchecked,
        # but the if (u == 1) block implies one must be selected and valid.
        # Enforcing this logic here:
        if not (is_wo_checked or is_dept_checked):
             raise ValidationError("WO No or BG Group must be selected.")

        if is_wo_checked:
            entered_wo_no = form_data.get('wo_no')
            if not SPRTemp.check_valid_wo_no(entered_wo_no, comp_id, fin_year_id):
                raise ValidationError("WO No is invalid or not found.")
            wo_no = entered_wo_no
        elif is_dept_checked:
            dept_id_val = form_data.get('department')
            if not dept_id_val:
                raise ValidationError("BG Group is required.")
            try:
                dept_id = int(dept_id_val)
            except (ValueError, TypeError):
                raise ValidationError("Invalid BG Group selected.")
        else:
            raise ValidationError("WO No or BG Group selection is required.")
        
        # 3. Rate and Discount Calculation/Validation
        try:
            entered_rate = Decimal(form_data['rate'])
            entered_discount = Decimal(form_data['discount'])
        except (InvalidOperation, ValueError):
            raise ValidationError("Rate and Discount must be valid numbers.")

        entered_disc_rate = (entered_rate - (entered_rate * entered_discount / Decimal('100'))).quantize(Decimal('0.00'))

        # Get the 'standard' or 'minimum' rate from RateRegister
        standard_rate_value, _ = RateRegister.get_initial_rates(item_id, comp_id)
        standard_rate = Decimal(standard_rate_value).quantize(Decimal('0.00')) if standard_rate_value else Decimal('0.00')

        if entered_disc_rate <= 0:
            raise ValidationError("Entered rate (after discount) must be positive.")

        # Rate validation logic from ASP.NET code
        if standard_rate > 0:
            # x = standard_rate - entered_disc_rate
            # if x >= 0 means entered_disc_rate <= standard_rate (entered rate is acceptable or lower)
            if entered_disc_rate > standard_rate: # if entered_disc_rate is higher than standard_rate
                # Check if rate lock is active
                if not RateLockUnlock.is_rate_locked_for_type(item_id, comp_id, lock_type=1):
                    raise ValidationError("Entered rate is not acceptable! It's higher than the standard rate and not locked.")
        # If standard_rate is 0 or less, or if entered_disc_rate <= standard_rate, it's considered acceptable by the ASP.NET logic,
        # UNLESS entered_disc_rate itself is not positive. This is handled by entered_disc_rate <= 0 check above.

        # 4. Supplier Validation
        supplier_full_text = form_data['supplier_name'] # e.g., "Supplier A [123]"
        supplier_code = None
        # Extract supplier code from text field, mimicking fun.getCode
        if '[' in supplier_full_text and supplier_full_text.endswith(']'):
            try:
                supplier_code = supplier_full_text.split('[')[-1][:-1].strip()
            except IndexError:
                pass # Will be caught by supplier_obj check

        supplier_obj = Supplier.get_supplier_by_code(supplier_code, comp_id)
        if not supplier_obj:
            raise ValidationError("Supplier not found or invalid format. Please use 'SupplierName [SupplierId]'.")

        # 5. Other Validations (Date, Quantity, Remarks - handled by form)
        # The form itself should handle basic type/format validation for quantity, rate, discount, delivery_date.
        # This method is for more complex business rules.

        # 6. Create and Save the SPRTemp object
        spr_entry = cls(
            sys_date=timezone.now().date(),
            sys_time=timezone.now().time(),
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            session_id=session_id,
            item=item_obj,
            supplier=supplier_obj,
            quantity=Decimal(form_data['quantity']),
            rate=entered_rate,
            account_head_id=ah_id, # Use ah_id directly
            wo_no=wo_no,
            department_id=dept_id, # Use dept_id directly
            remarks=form_data.get('remarks', ''),
            delivery_date=form_data['delivery_date'],
            discount=entered_discount,
        )
        spr_entry.save()
        return spr_entry
```

#### 4.2 Forms

We'll define a `ModelForm` for `SPRTemp` and a custom field for the supplier auto-complete.

```python
# spr/forms.py
from django import forms
from .models import SPRTemp, ItemMaster, AccountHead, BusinessGroup, Supplier, RateRegister
from decimal import Decimal
from django.core.exceptions import ValidationError

class SPRTempForm(forms.ModelForm):
    # Hidden fields for radio button selection, as they are not standard model fields
    rdwono_checked = forms.CharField(widget=forms.HiddenInput(), required=False)
    rddept_checked = forms.CharField(widget=forms.HiddenInput(), required=False)
    
    # Supplier will be handled as a CharField for input, then resolved in process_spr_entry
    supplier_name = forms.CharField(
        label="Supplier",
        max_length=255,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Type supplier name (e.g., Supplier A [Code])',
            'hx-post': '/spr/suppliers/search/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#supplier-suggestions',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off',
            '@input': 'showSuggestions = true', # Alpine.js
        })
    )

    class Meta:
        model = SPRTemp
        fields = [
            'quantity', 'rate', 'discount', 'supplier_name', 
            'account_head', 'wo_no', 'department', 'delivery_date', 'remarks',
            'rdwono_checked', 'rddept_checked' # Include hidden fields
        ]
        
        widgets = {
            'quantity': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.001', 'min': '0'}),
            'rate': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01', 'min': '0'}),
            'discount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01', 'min': '0', 'max': '100'}),
            'account_head': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'wo_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'department': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'delivery_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'remarks': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
        }
        labels = {
            'quantity': 'Qty',
            'rate': 'Rate',
            'account_head': 'A/c Head',
            'wo_no': 'WO No',
            'department': 'BG Group',
            'delivery_date': 'Delivery Date',
            'remarks': 'Remarks',
        }
        
    def __init__(self, *args, **kwargs):
        self.item_id = kwargs.pop('item_id', None)
        self.comp_id = kwargs.pop('comp_id', None) # Passed from view/session
        super().__init__(*args, **kwargs)

        if self.item_id and self.comp_id:
            # Pre-populate initial rates and item details
            initial_rate, initial_discount = RateRegister.get_initial_rates(self.item_id, self.comp_id)
            if initial_rate is not None:
                self.fields['rate'].initial = initial_rate
            if initial_discount is not None:
                self.fields['discount'].initial = initial_discount
                
            item_obj = ItemMaster.get_item_details(self.item_id, self.comp_id)
            if item_obj:
                # If CId exists for the item, then AccountHead is fixed, so disable radio buttons and dropdown
                if item_obj.c_id is not None:
                    # Pre-set selected account head for display
                    if item_obj.ah_id:
                        self.fields['account_head'].widget = forms.HiddenInput()
                        self.fields['account_head'].initial = item_obj.ah_id.id
                        self.fields['account_head'].label = False # Hide label if field is hidden

                    # Disable the radio buttons as they are not used
                    self.fields['rdwono_checked'].widget = forms.HiddenInput()
                    self.fields['rddept_checked'].widget = forms.HiddenInput()
                    self.fields['rdwono_checked'].required = False
                    self.fields['rddept_checked'].required = False

                else:
                    # Populate Account Head dropdown based on default "Labour" category initially
                    self.fields['account_head'].queryset = AccountHead.get_heads_by_category("Labour")
            else:
                raise ValidationError("Item not found or invalid.")
        else:
            # If item_id/comp_id not provided, this form is not correctly initialized
            raise ValidationError("Form must be initialized with item_id and comp_id.")

        # Populate BusinessGroup dropdown
        self.fields['department'].queryset = BusinessGroup.objects.all().order_by('symbol')
        self.fields['department'].required = False # Only required if rddept is checked, handled in process_spr_entry

    def clean_quantity(self):
        quantity = self.cleaned_data['quantity']
        if quantity is None:
            raise ValidationError("Quantity is required.")
        if quantity <= 0:
            raise ValidationError("Quantity must be positive.")
        # Equivalent to fun.NumberValidationQty
        if quantity.as_tuple().exponent < -3: # Check for more than 3 decimal places
            raise ValidationError("Quantity cannot have more than 3 decimal places.")
        return quantity

    def clean_rate(self):
        rate = self.cleaned_data['rate']
        if rate is None:
            raise ValidationError("Rate is required.")
        if rate <= 0: # ASP.NET code allows 0. But their DiscRate must be > 0. Let's make rate > 0
            raise ValidationError("Rate must be positive.")
        # Equivalent to fun.NumberValidationQty
        if rate.as_tuple().exponent < -3: # Check for more than 3 decimal places (assuming for rates too)
            raise ValidationError("Rate cannot have more than 3 decimal places.")
        return rate
    
    def clean_discount(self):
        discount = self.cleaned_data['discount']
        if discount is None:
            raise ValidationError("Discount is required.")
        if discount < 0 or discount > 100:
            raise ValidationError("Discount must be between 0 and 100.")
        # Equivalent to fun.NumberValidationQty
        if discount.as_tuple().exponent < -3: # Check for more than 3 decimal places
            raise ValidationError("Discount cannot have more than 3 decimal places.")
        return discount

    def clean_delivery_date(self):
        delivery_date = self.cleaned_data['delivery_date']
        if delivery_date is None:
            raise ValidationError("Delivery Date is required.")
        # fun.DateValidation typically checks format and validity. Django's DateField does this.
        return delivery_date

    def clean(self):
        cleaned_data = super().clean()
        
        # Ensure at least one radio button is checked (handled in process_spr_entry as well, but can reinforce here)
        rdwono_checked = cleaned_data.get('rdwono_checked') == 'on'
        rddept_checked = cleaned_data.get('rddept_checked') == 'on'
        
        # Check if the item's AHId is set directly or if radio buttons are needed
        item_obj = ItemMaster.get_item_details(self.item_id, self.comp_id)
        if item_obj and item_obj.c_id is None: # Only if CId is None, then radio buttons are relevant
            if not (rdwono_checked or rddept_checked):
                self.add_error(None, "WO No or BG Group must be selected.")
            elif rdwono_checked and not cleaned_data.get('wo_no'):
                self.add_error('wo_no', "WO No is required when selected.")
            elif rddept_checked and not cleaned_data.get('department'):
                self.add_error('department', "BG Group is required when selected.")
        
        # Supplier name format check (partial check, full validation in model method)
        supplier_name = cleaned_data.get('supplier_name')
        if supplier_name and '[' not in supplier_name or not supplier_name.endswith(']'):
            self.add_error('supplier_name', "Please select a supplier from the suggestions (e.g., Supplier A [Code]).")

        return cleaned_data

```

#### 4.3 Views

We need a main `CreateView` for the SPR entry, a partial view for the table (though this specific page is a form, not a list, so a list view might not be needed for `SPRTemp` itself unless it's meant to show *all* temporary entries), and an HTMX endpoint for dynamic Account Head dropdown.

```python
# spr/views.py
from django.views.generic import FormView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404
from django.template.loader import render_to_string
from django.core.exceptions import ValidationError

from .models import (
    SPRTemp, ItemMaster, AccountHead, BusinessGroup,
    RateRegister, RateLockUnlock, Supplier, Unit,
    get_current_company_id, get_current_financial_year_id # Helper for session context
)
from .forms import SPRTempForm

# Helper function to get CompId and FinYearId from request context (simulating session)
def get_user_session_info(request):
    # In a real application, you'd get these from request.user profile or actual session
    return {
        'username': request.user.username if request.user.is_authenticated else 'anonymous',
        'comp_id': get_current_company_id(), # Example global/user-specific company ID
        'fin_year_id': get_current_financial_year_id(), # Example global/user-specific financial year ID
    }


class SPRTempCreateView(FormView):
    template_name = 'spr/sprtemp/form.html'
    form_class = SPRTempForm
    success_url = reverse_lazy('spr_new_redirect') # Redirect back to SPR_New.aspx context

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        item_id = self.kwargs.get('item_id') # Get item_id from URL
        comp_id = get_user_session_info(self.request)['comp_id']
        
        item_details = ItemMaster.get_item_details(item_id, comp_id)
        if item_details:
            context['item_details'] = item_details
            # For the fixed AHId case, display it as a label
            if item_details.c_id is not None and item_details.ah_id:
                context['fixed_account_head_label'] = f"{item_details.ah_id.category} - [{item_details.ah_id.symbol}] {item_details.ah_id.description}"
            
            # The RateRegister link
            context['rate_register_link'] = f"/Module/MaterialManagement/Reports/RateRegisterSingleItemPrint.aspx?ItemId={item_id}&CompId={comp_id}"
        else:
            messages.error(self.request, "Invalid Item ID provided.")
            # Handle redirection or error display appropriately
            # In a real app, you might want to redirect to an error page or item selection page.
            # For now, just pass a flag
            context['invalid_item'] = True
        return context

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['item_id'] = self.kwargs.get('item_id')
        kwargs['comp_id'] = get_user_session_info(self.request)['comp_id']
        return kwargs

    def form_valid(self, form):
        item_id = self.kwargs.get('item_id')
        user_session_info = get_user_session_info(self.request)
        
        try:
            # Call the model's business logic method
            SPRTemp.process_spr_entry(item_id, form.cleaned_data, user_session_info)
            messages.success(self.request, 'SPR Entry added successfully.')
            
            # HTMX response for success, typically just trigger a refresh or close modal
            if self.request.headers.get('HX-Request'):
                # Send 204 No Content and a trigger to close modal and perhaps refresh another part of the UI
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'sprEntryAdded' # Custom event to trigger success actions
                    }
                )
            return super().form_valid(form) # Fallback for non-HTMX
            
        except ValidationError as e:
            form.add_error(None, e.message)
            return self.form_invalid(form)
        except Exception as e:
            # Generic error handling for unexpected issues
            messages.error(self.request, f"An unexpected error occurred: {e}")
            return self.form_invalid(form)

    def form_invalid(self, form):
        # HTMX re-renders the form with errors
        if self.request.headers.get('HX-Request'):
            return HttpResponse(render_to_string(self.template_name, self.get_context_data(form=form), self.request))
        return super().form_invalid(form)


class AccountHeadDropdownView(TemplateView):
    """
    HTMX endpoint to dynamically update the Account Head dropdown based on radio button selection.
    """
    def post(self, request, *args, **kwargs):
        category = request.POST.get('category') # 'Labour' or 'With Material'
        if not category:
            return HttpResponse(status=400) # Bad Request

        account_heads = AccountHead.get_heads_by_category(category)
        context = {'form': SPRTempForm(initial={'account_head': None}), 'account_heads_queryset': account_heads} # Pass queryset for dropdown
        
        # Render just the select field
        return HttpResponse(render_to_string('spr/sprtemp/_account_head_dropdown_options.html', context, request))

class SupplierSearchView(TemplateView):
    """
    HTMX endpoint for supplier autocomplete suggestions.
    """
    def post(self, request, *args, **kwargs):
        prefix_text = request.POST.get('search', '')
        comp_id = get_user_session_info(request)['comp_id']
        
        if len(prefix_text) < 1: # Minimum prefix length 1 as per ASP.NET
            return HttpResponse("")

        suppliers = Supplier.search_suppliers(prefix_text, comp_id, count=10)
        
        context = {
            'suppliers': suppliers,
        }
        return HttpResponse(render_to_string('spr/sprtemp/_supplier_suggestions.html', context, request))

# Placeholder view for redirecting to SPR_New.aspx context
class SPRNewRedirectView(TemplateView):
    def get(self, request, *args, **kwargs):
        # In a real app, this would redirect to the actual SPR_New Django view
        messages.success(request, "SPR Entry process completed successfully.")
        return HttpResponse("Redirecting to SPR New page (simulated).", status=200) # For demo, just show message
        # Actual redirect: return redirect('spr_new_list') # Assuming a Django URL for SPR_New
```

#### 4.4 Templates

We'll define the main form template, and partials for the dynamic dropdown and supplier suggestions. This specific page is a form, not a list, so we'll focus on the form and its dynamic elements.

**`spr/sprtemp/form.html`** (Main entry form)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">SPR - Item Master Entry</h2>
    </div>

    {% if messages %}
    <div class="mb-4">
        {% for message in messages %}
        <div class="p-3 {% if message.tags == 'success' %}bg-green-100 text-green-800{% elif message.tags == 'error' %}bg-red-100 text-red-800{% else %}bg-blue-100 text-blue-800{% endif %} rounded-md">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    {% if item_details %}
    <div class="bg-white p-6 rounded-lg shadow-lg max-w-4xl mx-auto">
        <h3 class="text-lg font-medium text-gray-900 mb-5">Item Details</h3>
        <div class="grid grid-cols-2 gap-4 mb-6">
            <div>
                <label class="block text-sm font-medium text-gray-700">Item Code</label>
                <p class="mt-1 text-sm text-gray-900">{{ item_details.item_code }}</p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Description</label>
                <p class="mt-1 text-sm text-gray-900">{{ item_details.manf_desc }}</p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">UOM</label>
                <p class="mt-1 text-sm text-gray-900">{{ item_details.uom_basic.symbol }}</p>
            </div>
        </div>

        <form hx-post="{% url 'spr_item_entry' item_details.id %}" hx-swap="outerHTML" hx-target="#spr-entry-form-container" hx-indicator="#form-loading-indicator">
            {% csrf_token %}
            <div class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="{{ form.quantity.id_for_label }}" class="block text-sm font-medium text-gray-700">Qty</label>
                        {{ form.quantity }}
                        {% if form.quantity.errors %}<p class="text-red-500 text-xs mt-1">{{ form.quantity.errors }}</p>{% endif %}
                    </div>
                    <div>
                        <label for="{{ form.rate.id_for_label }}" class="block text-sm font-medium text-gray-700">Rate</label>
                        <div class="flex items-center">
                            {{ form.rate }}
                            <a href="{{ rate_register_link }}" target="_blank" class="ml-2 text-blue-600 hover:text-blue-800">
                                <img src="/static/images/Rupee.JPG" alt="Rupee" class="h-5 w-5 inline-block">
                            </a>
                        </div>
                        {% if form.rate.errors %}<p class="text-red-500 text-xs mt-1">{{ form.rate.errors }}</p>{% endif %}
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="{{ form.discount.id_for_label }}" class="block text-sm font-medium text-gray-700">Discount</label>
                        {{ form.discount }}
                        {% if form.discount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.discount.errors }}</p>{% endif %}
                    </div>
                    <div x-data="{ showSuggestions: false }" @click.away="showSuggestions = false">
                        <label for="{{ form.supplier_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Supplier</label>
                        {{ form.supplier_name }}
                        <div id="supplier-suggestions" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg mt-1 w-full max-h-60 overflow-y-auto"
                             x-show="showSuggestions && $el.children.length > 0">
                            <!-- HTMX will inject suggestions here -->
                        </div>
                        {% if form.supplier_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.supplier_name.errors }}</p>{% endif %}
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700">A/c Head</label>
                    {% if item_details.c_id is not None %}
                        <!-- Fixed A/c Head for this item -->
                        <p class="mt-1 text-sm text-gray-900 font-semibold">{{ fixed_account_head_label }}</p>
                        {{ form.account_head }} {# hidden input #}
                        {{ form.rdwono_checked }} {# hidden input #}
                        {{ form.rddept_checked }} {# hidden input #}
                    {% else %}
                        <div class="flex items-center space-x-4 mb-2">
                            <input type="radio" id="RbtnLabour" name="category_selector" value="Labour"
                                   {% if not request.POST.category_selector or request.POST.category_selector == 'Labour' %}checked{% endif %}
                                   hx-post="{% url 'spr_account_head_dropdown' %}"
                                   hx-target="#account-head-dropdown-container"
                                   hx-vals='{"category": "Labour"}'
                                   hx-trigger="change"
                                   class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"
                                   onclick="document.getElementById('id_rdwono_checked').value = ''; document.getElementById('id_rddept_checked').value = '';">
                            <label for="RbtnLabour" class="ml-2 block text-sm text-gray-900">Labour</label>

                            <input type="radio" id="RbtnWithMaterial" name="category_selector" value="With Material"
                                   {% if request.POST.category_selector == 'With Material' %}checked{% endif %}
                                   hx-post="{% url 'spr_account_head_dropdown' %}"
                                   hx-target="#account-head-dropdown-container"
                                   hx-vals='{"category": "With Material"}'
                                   hx-trigger="change"
                                   class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"
                                   onclick="document.getElementById('id_rdwono_checked').value = ''; document.getElementById('id_rddept_checked').value = '';">
                            <label for="RbtnWithMaterial" class="ml-2 block text-sm text-gray-900">With Material</label>
                        </div>
                        <div id="account-head-dropdown-container" class="mt-2">
                            {% include 'spr/sprtemp/_account_head_dropdown_options.html' with form=form %}
                        </div>
                        {% if form.account_head.errors %}<p class="text-red-500 text-xs mt-1">{{ form.account_head.errors }}</p>{% endif %}
                    {% endif %}
                </div>

                <div class="flex items-center space-x-4">
                    <input type="radio" id="rdwono" name="wo_dept_selector" value="wo_no"
                           {% if not form.rdwono_checked.value and not form.rddept_checked.value or form.rdwono_checked.value %}checked{% endif %}
                           hx-on:change="document.getElementById('id_rdwono_checked').value = 'on'; document.getElementById('id_rddept_checked').value = ''; document.getElementById('wo_no_group').classList.remove('hidden'); document.getElementById('dept_group').classList.add('hidden');"
                           class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300">
                    <label for="rdwono" class="ml-2 block text-sm text-gray-900">WO No</label>
                    <div id="wo_no_group" class="{% if not form.rdwono_checked.value %}hidden{% endif %}">
                        {{ form.wo_no }}
                        {% if form.wo_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.wo_no.errors }}</p>{% endif %}
                    </div>

                    <input type="radio" id="rddept" name="wo_dept_selector" value="dept"
                           {% if form.rddept_checked.value %}checked{% endif %}
                           hx-on:change="document.getElementById('id_rddept_checked').value = 'on'; document.getElementById('id_rdwono_checked').value = ''; document.getElementById('dept_group').classList.remove('hidden'); document.getElementById('wo_no_group').classList.add('hidden');"
                           class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300">
                    <label for="rddept" class="ml-2 block text-sm text-gray-900">BG Group</label>
                    <div id="dept_group" class="{% if not form.rddept_checked.value %}hidden{% endif %}">
                        {{ form.department }}
                        {% if form.department.errors %}<p class="text-red-500 text-xs mt-1">{{ form.department.errors }}</p>{% endif %}
                    </div>
                    {# Hidden inputs to track radio button state on submission #}
                    {{ form.rdwono_checked }}
                    {{ form.rddept_checked }}
                </div>

                <div>
                    <label for="{{ form.delivery_date.id_for_label }}" class="block text-sm font-medium text-gray-700">Delivery Date</label>
                    {{ form.delivery_date }}
                    {% if form.delivery_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.delivery_date.errors }}</p>{% endif %}
                </div>

                <div>
                    <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700">Remarks</label>
                    {{ form.remarks }}
                    {% if form.remarks.errors %}<p class="text-red-500 text-xs mt-1">{{ form.remarks.errors }}</p>{% endif %}
                </div>
            </div>

            <div class="mt-6 flex items-center justify-end space-x-4">
                <button type="button" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                        hx-get="{% url 'spr_new_redirect' %}" hx-push-url="true">
                    Cancel
                </button>
                <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Add
                </button>
                <span id="form-loading-indicator" class="htmx-indicator ml-3">
                    <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                </span>
            </div>
            {% if form.non_field_errors %}
                <div class="text-red-500 text-sm mt-3">
                    {% for error in form.non_field_errors %}{{ error }}{% endfor %}
                </div>
            {% endif %}
        </form>
    </div>
    {% else %}
    <div class="text-center text-red-600 font-bold text-xl">
        Item details could not be loaded. Please check the provided Item ID.
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('sprForm', () => ({
            // Add Alpine.js state for form elements if needed, e.g., for WO/Dept visibility
            // Initial states for WO/Dept groups based on Django's initial value
            init() {
                // Ensure the correct WO/Dept group is visible on load
                const rdwono_checked = document.getElementById('rdwono').checked;
                const rddept_checked = document.getElementById('rddept').checked;

                if (rdwono_checked) {
                    document.getElementById('wo_no_group').classList.remove('hidden');
                    document.getElementById('dept_group').classList.add('hidden');
                    document.getElementById('id_rdwono_checked').value = 'on';
                    document.getElementById('id_rddept_checked').value = '';
                } else if (rddept_checked) {
                    document.getElementById('dept_group').classList.remove('hidden');
                    document.getElementById('wo_no_group').classList.add('hidden');
                    document.getElementById('id_rddept_checked').value = 'on';
                    document.getElementById('id_rdwono_checked').value = '';
                } else { // Default to WO No if neither is checked
                    document.getElementById('wo_no_group').classList.remove('hidden');
                    document.getElementById('dept_group').classList.add('hidden');
                    document.getElementById('id_rdwono_checked').value = 'on';
                    document.getElementById('id_rddept_checked').value = '';
                    document.getElementById('rdwono').checked = true;
                }
            }
        }));
    });

    // Listen for the custom event after successful SPR entry
    document.body.addEventListener('sprEntryAdded', function(evt) {
        // You can add a success message here, e.g., by targeting a div
        // This is where you might trigger a notification or redirect as needed.
        // For now, messages are handled by Django's messages framework on redirect.
        window.location.href = "{% url 'spr_new_redirect' %}"; // Redirect to a success/list page
    });
</script>
{% endblock %}
```

**`spr/sprtemp/_account_head_dropdown_options.html`** (Partial for dynamic Account Head dropdown)

```html
<select name="{{ form.account_head.name }}" id="{{ form.account_head.id_for_label }}" 
        class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
    <option value="">---------</option>
    {% for acc_head in account_heads_queryset %}
        <option value="{{ acc_head.id }}" {% if form.account_head.value == acc_head.id %}selected{% endif %}>
            {{ acc_head.head }}
        </option>
    {% endfor %}
</select>
```

**`spr/sprtemp/_supplier_suggestions.html`** (Partial for supplier autocomplete suggestions)

```html
{% for supplier in suppliers %}
    <div class="px-3 py-2 hover:bg-gray-100 cursor-pointer"
         hx-on:click="document.getElementById('{{ form.supplier_name.id_for_label }}').value = '{{ supplier.supplier_name }} [{{ supplier.supplier_id }}]'; document.getElementById('supplier-suggestions').innerHTML = ''; showSuggestions = false;">
        {{ supplier.supplier_name }} [{{ supplier.supplier_id }}]
    </div>
{% endfor %}
{% if not suppliers and request.POST.search %}
    <div class="px-3 py-2 text-gray-500">No suggestions found.</div>
{% endif %}
```

#### 4.5 URLs

We'll define URL patterns for the main form, the dynamic dropdown, and supplier search.

```python
# spr/urls.py
from django.urls import path
from .views import SPRTempCreateView, AccountHeadDropdownView, SupplierSearchView, SPRNewRedirectView

urlpatterns = [
    # Main SPR Item Entry Form for a specific ItemId
    path('spr/item_entry/<int:item_id>/', SPRTempCreateView.as_view(), name='spr_item_entry'),
    
    # HTMX endpoint for dynamically updating Account Head dropdown
    path('spr/account_head_dropdown/', AccountHeadDropdownView.as_view(), name='spr_account_head_dropdown'),

    # HTMX endpoint for supplier autocomplete suggestions
    path('spr/suppliers/search/', SupplierSearchView.as_view(), name='spr_supplier_search'),

    # Placeholder for redirecting to the "SPR New" page (simulates the ASP.NET redirect)
    path('spr/new/redirect/', SPRNewRedirectView.as_view(), name='spr_new_redirect'),

    # If there was a list view for SPRTemp (e.g., to review all temporary entries), it would go here.
    # For now, this specific page only handles creation.
    # path('spr/temp/list/', SPRTempListView.as_view(), name='sprtemp_list'),
]
```

#### 4.6 Tests

Comprehensive unit tests for model methods and integration tests for views.

```python
# spr/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from decimal import Decimal
from unittest.mock import patch

from .models import (
    SPRTemp, ItemMaster, Unit, AccountHead, BusinessGroup,
    RateRegister, RateLockUnlock, Supplier,
    get_current_company_id, get_current_financial_year_id
)
from .forms import SPRTempForm

# Mock the company and financial year IDs for consistent testing
@patch('spr.models.get_current_company_id', return_value=100)
@patch('spr.models.get_current_financial_year_id', return_value=2024)
class SPRModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls, mock_comp_id, mock_fin_year_id):
        cls.comp_id = mock_comp_id.return_value
        cls.fin_year_id = mock_fin_year_id.return_value

        # Create basic lookup data
        cls.unit_pcs = Unit.objects.create(id=1, symbol='PCS')
        cls.acc_head_labour = AccountHead.objects.create(id=101, category='Labour', symbol='LAB', description='Labour Charges')
        cls.acc_head_material = AccountHead.objects.create(id=102, category='With Material', symbol='MAT', description='Material Cost')
        cls.acc_head_fixed = AccountHead.objects.create(id=103, category='Fixed', symbol='FIX', description='Fixed Overhead')
        cls.biz_group_it = BusinessGroup.objects.create(id=201, symbol='IT')
        cls.biz_group_hr = BusinessGroup.objects.create(id=202, symbol='HR')
        cls.supplier_a = Supplier.objects.create(supplier_id='SUP001', supplier_name='Supplier A', comp_id=cls.comp_id)
        cls.supplier_b = Supplier.objects.create(supplier_id='SUP002', supplier_name='Supplier B', comp_id=cls.comp_id)
        cls.supplier_c = Supplier.objects.create(supplier_id='C-Supp', supplier_name='Another Supplier', comp_id=cls.comp_id)

        # Create test ItemMaster records
        cls.item_with_cid = ItemMaster.objects.create(
            id=1, item_code='ITEM001', manf_desc='Processor', uom_basic=cls.unit_pcs,
            ah_id=cls.acc_head_fixed, c_id=1, comp_id=cls.comp_id
        )
        cls.item_no_cid = ItemMaster.objects.create(
            id=2, item_code='ITEM002', manf_desc='RAM Module', uom_basic=cls.unit_pcs,
            ah_id=None, c_id=None, comp_id=cls.comp_id
        )

        # Create RateRegister records
        RateRegister.objects.create(id=1, item=cls.item_with_cid, comp_id=cls.comp_id, rate=Decimal('100.00'), discount=Decimal('10.00'), flag=0)
        RateRegister.objects.create(id=2, item=cls.item_with_cid, comp_id=cls.comp_id, rate=Decimal('95.00'), discount=Decimal('5.00'), flag=1) # Flagged rate
        RateRegister.objects.create(id=3, item=cls.item_no_cid, comp_id=cls.comp_id, rate=Decimal('50.00'), discount=Decimal('0.00'), flag=0)
        RateRegister.objects.create(id=4, item=cls.item_no_cid, comp_id=cls.comp_id, rate=Decimal('48.00'), discount=Decimal('5.00'), flag=0) # Lowest discounted rate for item_no_cid

        # Create RateLockUnlock records
        RateLockUnlock.objects.create(item=cls.item_with_cid, comp_id=cls.comp_id, lock_unlock=1, type=1) # Item1 rate locked
        RateLockUnlock.objects.create(item=cls.item_no_cid, comp_id=cls.comp_id, lock_unlock=0, type=1) # Item2 rate not locked

    def test_item_master_details(self):
        item = ItemMaster.get_item_details(self.item_with_cid.id, self.comp_id)
        self.assertIsNotNone(item)
        self.assertEqual(item.item_code, 'ITEM001')
        self.assertEqual(item.uom_basic.symbol, 'PCS')
        self.assertEqual(item.ah_id.symbol, 'FIX')

    def test_rate_register_discounted_rate(self):
        rate_entry = RateRegister.objects.get(id=1)
        self.assertEqual(rate_entry.discounted_rate, Decimal('90.00'))

    def test_rate_register_get_initial_rates(self):
        # Test for item with flagged rate
        rate, discount = RateRegister.get_initial_rates(self.item_with_cid.id, self.comp_id)
        self.assertEqual(rate, Decimal('95.00')) # Flagged rate
        self.assertEqual(discount, Decimal('5.00')) # Flagged discount

        # Test for item without flagged rate, min discounted rate
        rate, discount = RateRegister.get_initial_rates(self.item_no_cid.id, self.comp_id)
        # For ITEM002: 50.00 rate (0 disc) -> 50.00, 48.00 rate (5 disc) -> 45.60. Min is 45.60 from Rate=48, Disc=5
        self.assertEqual(rate, Decimal('48.00'))
        self.assertEqual(discount, Decimal('5.00'))

    def test_rate_lock_unlock(self):
        self.assertTrue(RateLockUnlock.is_rate_locked_for_type(self.item_with_cid.id, self.comp_id))
        self.assertFalse(RateLockUnlock.is_rate_locked_for_type(self.item_no_cid.id, self.comp_id))

    def test_supplier_search(self):
        suppliers = Supplier.search_suppliers('Supp', self.comp_id)
        self.assertEqual(len(suppliers), 2) # Supplier A, Another Supplier
        self.assertEqual(suppliers[0].supplier_name, 'Another Supplier') # Alphabetical
        self.assertEqual(suppliers[1].supplier_name, 'Supplier A')

        suppliers = Supplier.search_suppliers('Supplier B', self.comp_id)
        self.assertEqual(len(suppliers), 1)
        self.assertEqual(suppliers[0].supplier_name, 'Supplier B')

    @patch('spr.models.SPRTemp.check_valid_wo_no', return_value=True) # Mock WO validation
    def test_process_spr_entry_success_item_with_cid(self, mock_wo_check):
        form_data = {
            'quantity': '10.500',
            'rate': '110.00',
            'discount': '0.00',
            'supplier_name': f'Supplier A [{self.supplier_a.supplier_id}]',
            'account_head': str(self.acc_head_fixed.id), # This will be ignored if item has CId
            'rdwono_checked': 'on',
            'wo_no': 'WO001',
            'rddept_checked': '',
            'department': '',
            'delivery_date': timezone.now().date(),
            'remarks': 'Test remarks',
        }
        user_info = {'username': 'testuser', 'comp_id': self.comp_id, 'fin_year_id': self.fin_year_id}
        
        spr_entry = SPRTemp.process_spr_entry(self.item_with_cid.id, form_data, user_info)
        self.assertIsNotNone(spr_entry)
        self.assertEqual(spr_entry.item, self.item_with_cid)
        self.assertEqual(spr_entry.account_head, self.acc_head_fixed) # Should use item's AHId
        self.assertEqual(spr_entry.wo_no, 'WO001')
        self.assertEqual(spr_entry.calculated_discounted_rate, Decimal('110.00'))
        
        # Test higher rate when locked
        form_data['rate'] = '120.00' # Higher than standard 90/95, but locked
        spr_entry = SPRTemp.process_spr_entry(self.item_with_cid.id, form_data, user_info)
        self.assertIsNotNone(spr_entry) # Should still pass because rate is locked

    @patch('spr.models.SPRTemp.check_valid_wo_no', return_value=True) # Mock WO validation
    def test_process_spr_entry_success_item_no_cid(self, mock_wo_check):
        form_data = {
            'quantity': '5.000',
            'rate': '45.00',
            'discount': '0.00',
            'supplier_name': f'Supplier B [{self.supplier_b.supplier_id}]',
            'account_head': str(self.acc_head_labour.id),
            'rdwono_checked': 'on',
            'wo_no': 'WO002',
            'rddept_checked': '',
            'department': '',
            'delivery_date': timezone.now().date(),
            'remarks': '',
        }
        user_info = {'username': 'testuser2', 'comp_id': self.comp_id, 'fin_year_id': self.fin_year_id}

        spr_entry = SPRTemp.process_spr_entry(self.item_no_cid.id, form_data, user_info)
        self.assertIsNotNone(spr_entry)
        self.assertEqual(spr_entry.item, self.item_no_cid)
        self.assertEqual(spr_entry.account_head, self.acc_head_labour)
        self.assertEqual(spr_entry.wo_no, 'WO002')
        self.assertEqual(spr_entry.calculated_discounted_rate, Decimal('45.00'))

    @patch('spr.models.SPRTemp.check_valid_wo_no', return_value=True)
    def test_process_spr_entry_rate_not_acceptable(self, mock_wo_check):
        form_data = {
            'quantity': '1.000',
            'rate': '60.00', # Much higher than standard 45.60
            'discount': '0.00',
            'supplier_name': f'Supplier B [{self.supplier_b.supplier_id}]',
            'account_head': str(self.acc_head_labour.id),
            'rdwono_checked': 'on',
            'wo_no': 'WO003',
            'rddept_checked': '',
            'department': '',
            'delivery_date': timezone.now().date(),
            'remarks': '',
        }
        user_info = {'username': 'testuser2', 'comp_id': self.comp_id, 'fin_year_id': self.fin_year_id}
        
        # Rate for item_no_cid is NOT locked. So higher rate should fail.
        with self.assertRaisesMessage(ValidationError, "Entered rate is not acceptable! It's higher than the standard rate and not locked."):
            SPRTemp.process_spr_entry(self.item_no_cid.id, form_data, user_info)

    def test_process_spr_entry_supplier_not_found(self):
        form_data = {
            'quantity': '1.000', 'rate': '100.00', 'discount': '0.00',
            'supplier_name': 'NonExistent Supplier [NON001]',
            'account_head': str(self.acc_head_labour.id),
            'rdwono_checked': 'on', 'wo_no': 'WO004', 'rddept_checked': '', 'department': '',
            'delivery_date': timezone.now().date(), 'remarks': '',
        }
        user_info = {'username': 'testuser', 'comp_id': self.comp_id, 'fin_year_id': self.fin_year_id}
        with self.assertRaisesMessage(ValidationError, "Supplier not found or invalid format. Please use 'SupplierName [SupplierId]'."):
            SPRTemp.process_spr_entry(self.item_with_cid.id, form_data, user_info)

    def test_process_spr_entry_wo_dept_not_selected(self):
        form_data = {
            'quantity': '1.000', 'rate': '100.00', 'discount': '0.00',
            'supplier_name': f'Supplier A [{self.supplier_a.supplier_id}]',
            'account_head': str(self.acc_head_labour.id),
            'rdwono_checked': '', 'wo_no': '', 'rddept_checked': '', 'department': '',
            'delivery_date': timezone.now().date(), 'remarks': '',
        }
        user_info = {'username': 'testuser', 'comp_id': self.comp_id, 'fin_year_id': self.fin_year_id}
        with self.assertRaisesMessage(ValidationError, "WO No or BG Group must be selected."):
            SPRTemp.process_spr_entry(self.item_no_cid.id, form_data, user_info)

@patch('spr.models.get_current_company_id', return_value=100)
@patch('spr.models.get_current_financial_year_id', return_value=2024)
class SPRViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls, mock_comp_id, mock_fin_year_id):
        cls.comp_id = mock_comp_id.return_value
        cls.fin_year_id = mock_fin_year_id.return_value

        # Create basic lookup data for views
        cls.unit_pcs = Unit.objects.create(id=1, symbol='PCS')
        cls.acc_head_labour = AccountHead.objects.create(id=101, category='Labour', symbol='LAB', description='Labour Charges')
        cls.acc_head_material = AccountHead.objects.create(id=102, category='With Material', symbol='MAT', description='Material Cost')
        cls.acc_head_fixed = AccountHead.objects.create(id=103, category='Fixed', symbol='FIX', description='Fixed Overhead')
        cls.biz_group_it = BusinessGroup.objects.create(id=201, symbol='IT')
        cls.supplier_a = Supplier.objects.create(supplier_id='SUP001', supplier_name='Supplier A', comp_id=cls.comp_id)

        cls.item_no_cid = ItemMaster.objects.create(
            id=2, item_code='ITEM002', manf_desc='RAM Module', uom_basic=cls.unit_pcs,
            ah_id=None, c_id=None, comp_id=cls.comp_id
        )
        RateRegister.objects.create(id=3, item=cls.item_no_cid, comp_id=cls.comp_id, rate=Decimal('50.00'), discount=Decimal('0.00'), flag=0)
        RateLockUnlock.objects.create(item=cls.item_no_cid, comp_id=cls.comp_id, lock_unlock=0, type=1)

    def setUp(self):
        self.client = Client()
        # Mock authentication for testing
        self.user = self.client.login(username='testuser', password='password') # Assuming a user exists/can be logged in

    def test_spr_item_entry_get(self):
        response = self.client.get(reverse('spr_item_entry', args=[self.item_no_cid.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'spr/sprtemp/form.html')
        self.assertContains(response, 'RAM Module')
        self.assertContains(response, 'Qty')
        self.assertContains(response, 'Labour') # Default selected A/c Head radio

    @patch('spr.models.SPRTemp.check_valid_wo_no', return_value=True) # Mock WO validation
    def test_spr_item_entry_post_success(self, mock_wo_check):
        data = {
            'quantity': '5.000',
            'rate': '49.00',
            'discount': '0.00',
            'supplier_name': f'Supplier A [{self.supplier_a.supplier_id}]',
            'account_head': str(self.acc_head_labour.id),
            'rdwono_checked': 'on', # Simulate WO No selected
            'wo_no': 'TESTWO123',
            'rddept_checked': '',
            'department': '', # Not selected
            'delivery_date': timezone.now().date().isoformat(), # YYYY-MM-DD
            'remarks': 'Testing a new SPR entry.',
        }
        
        # Test HTMX POST
        headers = {'HTTP_HX_Request': 'true'}
        response = self.client.post(reverse('spr_item_entry', args=[self.item_no_cid.id]), data, **headers)
        
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'sprEntryAdded')
        self.assertTrue(SPRTemp.objects.filter(item=self.item_no_cid, quantity=Decimal('5.000')).exists())

    @patch('spr.models.SPRTemp.check_valid_wo_no', return_value=False) # Mock WO validation to fail
    def test_spr_item_entry_post_invalid_form(self, mock_wo_check):
        data = {
            'quantity': '5.000',
            'rate': '49.00',
            'discount': '0.00',
            'supplier_name': f'Supplier A [{self.supplier_a.supplier_id}]',
            'account_head': str(self.acc_head_labour.id),
            'rdwono_checked': 'on',
            'wo_no': 'INVALIDWO', # This will make validation fail
            'rddept_checked': '',
            'department': '',
            'delivery_date': timezone.now().date().isoformat(),
            'remarks': 'Testing a new SPR entry.',
        }

        headers = {'HTTP_HX_Request': 'true'}
        response = self.client.post(reverse('spr_item_entry', args=[self.item_no_cid.id]), data, **headers)
        
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors for HTMX
        self.assertContains(response, 'WO No is invalid or not found.')
        self.assertFalse(SPRTemp.objects.filter(item=self.item_no_cid, quantity=Decimal('5.000')).exists())

    def test_account_head_dropdown_view(self):
        response = self.client.post(reverse('spr_account_head_dropdown'), {'category': 'With Material'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.acc_head_material.description)
        self.assertNotContains(response, self.acc_head_labour.description)

    def test_supplier_search_view(self):
        response = self.client.post(reverse('spr_supplier_search'), {'search': 'Supp'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Supplier A [SUP001]')
        self.assertContains(response, 'Another Supplier [C-Supp]')
        self.assertNotContains(response, 'Supplier B [SUP002]') # Not starting with 'Supp'
        
        response = self.client.post(reverse('spr_supplier_search'), {'search': 'NonExistent'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No suggestions found.')
        
        response = self.client.post(reverse('spr_supplier_search'), {'search': ''}) # MinimumPrefixLength=1
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content.decode(), "") # Empty response for prefix < 1

    def test_spr_new_redirect_view(self):
        response = self.client.get(reverse('spr_new_redirect'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Redirecting to SPR New page (simulated).")
        # In a real app, you'd test for actual redirect header if it was a redirect.
```

### Step 5: HTMX and Alpine.js Integration

The provided Django code snippets (templates, views, URLs) already incorporate HTMX and Alpine.js:

*   **HTMX for Form Submission:** The `spr/sprtemp/form.html` uses `hx-post`, `hx-swap="outerHTML"`, `hx-target` on the main form to submit data without a full page refresh. Upon successful submission, a `204 No Content` response with an `HX-Trigger` header (`sprEntryAdded`) is sent, which is then caught by Alpine.js for a programmatic redirect or UI update.
*   **HTMX for Dynamic Account Head:**
    *   Radio buttons in `spr/sprtemp/form.html` have `hx-post` to `{% url 'spr_account_head_dropdown' %}` and `hx-target="#account-head-dropdown-container"`.
    *   The `AccountHeadDropdownView` renders `spr/sprtemp/_account_head_dropdown_options.html` (a partial containing only the `<select>` element), which HTMX then swaps into the target container.
*   **HTMX for Supplier Autocomplete:**
    *   The `supplier_name` input field uses `hx-post` to `{% url 'spr_supplier_search' %}` with `hx-trigger="keyup changed delay:500ms"` and `hx-target="#supplier-suggestions"`.
    *   The `SupplierSearchView` renders `spr/sprtemp/_supplier_suggestions.html`, which is then swapped into the `supplier-suggestions` div.
*   **Alpine.js for UI State:**
    *   `x-data` is used to control the `showSuggestions` state for the supplier autocomplete, ensuring the suggestion list only appears when typing and hides when clicking away.
    *   Simple `onclick` handlers on radio buttons manage hidden input values and show/hide WO No/BG Group fields, providing immediate client-side feedback before HTMX potentially updates the DOM for the Account Head dropdown.
    *   `document.body.addEventListener('sprEntryAdded', ...)` is used to catch the custom HTMX event and trigger a JavaScript redirect, mimicking the `Response.Redirect` from ASP.NET after a successful form submission.
*   **DataTables:** While this specific form page doesn't have a data table, the general guideline for list views is to use DataTables. The template example demonstrates how `_sprtemp_table.html` would be used for a DataTables integration if there were a list view of SPR entries.

### Final Notes

*   **Placeholders:** Replace `get_current_company_id()` and `get_current_financial_year_id()` with your actual logic for determining the current company and financial year (e.g., from user profiles, multi-tenancy context, or settings).
*   **Static Files:** Ensure your Django project is configured to serve static files (like `images/Rupee.JPG` if it exists).
*   **Database Schema:** The `managed = False` setting means Django won't create/manage these tables. You must ensure your database has these tables with the exact column names and types inferred from the ASP.NET code.
*   **Error Handling:** The `process_spr_entry` method uses `ValidationError` for business logic failures, which Django forms can display. Generic exceptions are also caught. Robust logging should be implemented in a production environment.
*   **URLs:** Remember to include your `spr.urls` in your main Django project's `urls.py`.
*   **Security:** Always sanitize user input, use Django's built-in security features, and ensure proper authentication and authorization. The provided `session_id` logic relies on `request.user.username`; ensure users are authenticated before accessing this view.
*   **Deployment:** Consider deploying this Django application using Gunicorn/Nginx, Docker containers, or cloud-specific deployment services.