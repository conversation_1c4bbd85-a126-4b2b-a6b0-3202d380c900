This modernization plan details the transition of your ASP.NET SPR (Stock Purchase Requisition) Edit page to a modern Django application. We'll leverage Django's powerful ORM for database interactions, HTMX and Alpine.js for dynamic frontend functionality, and DataTables for efficient data presentation, all while adhering to a "fat model, thin view" architecture. This approach significantly improves maintainability, scalability, and user experience with minimal traditional JavaScript.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:** The ASP.NET code interacts with three primary database tables: `tblMM_SPR_Master`, `tblHR_OfficeStaff`, and `tblFinancial_master`. The `CompId` and `FinYearId` are used as filtering contexts, often retrieved from the user's session.

**Extracted Schema:**

*   **`tblMM_SPR_Master`**: This is the main table for SPR records.
    *   `Id`: Primary Key (integer).
    *   `SessionId`: Maps to `EmpId` in `tblHR_OfficeStaff` (integer).
    *   `SPRNo`: Stock Purchase Requisition Number (string).
    *   `FinYearId`: Maps to `FinYearId` in `tblFinancial_master` (integer).
    *   `SysDate`: System Date (string, likely stored as 'MM-DD-YYYY' or 'DD-MM-YYYY').
    *   `SysTime`: System Time (string).
    *   `CompId`: Company Identifier (integer).

*   **`tblHR_OfficeStaff`**: Stores employee information.
    *   `EmpId`: Primary Key (integer).
    *   `Title`: Employee title (string, e.g., "Mr.", "Ms.").
    *   `EmployeeName`: Full name of the employee (string).
    *   `CompId`: Company Identifier (integer).

*   **`tblFinancial_master`**: Stores financial year details.
    *   `FinYearId`: Primary Key (integer).
    *   `FinYear`: Financial year period (string, e.g., "2023-2024").
    *   `CompId`: Company Identifier (integer).

### Step 2: Identify Backend Functionality

**Analysis:** The ASP.NET page `SPR_Edit.aspx` is primarily a "read" operation, displaying a list of SPRs with robust search, filtering, and pagination capabilities. It doesn't perform direct "create", "update", or "delete" operations on the SPR records themselves, but rather navigates to a separate "details" page for editing.

**Functionality Breakdown:**

*   **Read (Display List):**
    *   Retrieves a list of SPR records from `tblMM_SPR_Master`.
    *   Filters these records based on `CompId` and `FinYearId` (less than or equal to current).
    *   Performs additional filtering based on user input:
        *   By "Employee Name" (linking `SessionId` from `tblMM_SPR_Master` to `EmpId` in `tblHR_OfficeStaff`). The employee search also includes an autocomplete feature.
        *   By "SPR No" (direct match on `SPRNo`).
    *   Presents data in a paginated grid.
    *   Calculates and displays "SN" (serial number), "Fin Year", "Date", "Time", and "Gen By" (Generated By Employee Name).
    *   Provides a "Select" link to navigate to a separate SPR details page (`SPR_Edit_Details.aspx`).

*   **Search/Filter Logic:**
    *   A dropdown (`drpfield`) allows selecting the search criterion (Employee Name or SPR No).
    *   Based on the selection, the corresponding input field (`txtEmpName` or `txtSprNo`) is made visible, and the other is hidden.
    *   A "Search" button (`btnSearch`) triggers the data refresh based on the selected criteria.
    *   Employee name input (`txtEmpName`) has an autocomplete feature (`AutoCompleteExtender`) that fetches suggestions from `tblHR_OfficeStaff`.

### Step 3: Infer UI Components

**Analysis:** The ASP.NET UI uses standard Web Forms controls. We'll map these to modern Django templates with HTMX, Alpine.js, and DataTables for enhanced user experience.

**UI Component Mapping:**

*   **Page Structure:**
    *   Master page (`MasterPage.master`) will be replaced by Django's `core/base.html` template inheritance.
    *   Content placed within `ContentPlaceHolder3` (`Content7`) will form the primary content block.

*   **Search/Filter Section:**
    *   `drpfield` (ASP.NET DropDownList): Django `forms.ChoiceField` with HTMX `hx-get` to dynamically toggle input field visibility.
    *   `txtEmpName` (ASP.NET TextBox with `AutoCompleteExtender`): Django `forms.CharField` with an `attrs` for HTMX. The autocomplete functionality will be implemented using Alpine.js and HTMX for fetching suggestions from a Django endpoint.
    *   `txtSprNo` (ASP.NET TextBox): Django `forms.CharField` with `attrs` for HTMX to control visibility.
    *   `btnSearch` (ASP.NET Button): A standard HTML button with HTMX `hx-get` to trigger table data reload.

*   **Data Display Grid:**
    *   `GridView2` (ASP.NET GridView): This will be transformed into an HTML `<table>` element initialized as a DataTables instance. This handles client-side sorting, filtering, and pagination, replicating and enhancing the original functionality.
    *   `HyperLinkField` for "Select": A standard HTML `<a>` tag linking to the Django SPR detail page.
    *   BoundFields (`SPRNo`, `SysDate`, `Time`, `EmpName`): Will be mapped to Django template variables.
    *   TemplateFields (`SN`, `Fin Year`): Custom rendering in Django templates.
    *   `EmptyDataTemplate`: Handled by DataTables `emptyTable` option and Django's `{% empty %}` tag in the template.

*   **Dynamic Behavior:**
    *   `drpfield_SelectedIndexChanged` (AutoPostBack): Replaced by HTMX `hx-get` on the dropdown's `change` event to a partial view that renders the correct input fields.
    *   `btnSearch_Click`: Replaced by HTMX `hx-get` on the search button's `click` event to a partial view that renders the table content.
    *   `GridView2_PageIndexChanging`: DataTables handles pagination automatically on the client-side.
    *   `GetCompletionList` (WebMethod for Autocomplete): A dedicated Django view endpoint returning JSON data for the autocomplete field, integrated with Alpine.js.

---

### Step 4: Generate Django Code

We will create a Django application named `spr_management` to house the new components.

#### 4.1 Models (`spr_management/models.py`)

This section defines the Django models that map to your existing database tables. We use `managed = False` and `db_table` to connect to your legacy database without Django managing schema changes. Properties are added to the `SPRMaster` model to encapsulate business logic and data formatting, adhering to the "fat model" principle.

```python
from django.db import models
from datetime import datetime

# Note: We are assuming your Django project's settings.py
# is configured to connect to your legacy SQL Server database
# via a DATABASES entry named 'legacy_db'.
# Example:
# DATABASES = {
#     'default': { ... },
#     'legacy_db': {
#         'ENGINE': 'sql_server.pyodbc', # Or 'django.db.backends.mysql', etc.
#         'NAME': 'YourLegacyDBName',
#         'USER': 'YourUser',
#         'PASSWORD': 'YourPassword',
#         'HOST': 'YourHost',
#         'PORT': 'YourPort',
#         'OPTIONS': {
#             'driver': 'ODBC Driver 17 for SQL Server',
#         },
#     }
# }
# You may also need to set up DATABASE_ROUTERS to direct these models to 'legacy_db'.

class FinancialYear(models.Model):
    # This model maps to tblFinancial_master
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False  # Django will not manage this table's schema
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class Employee(models.Model):
    # This model maps to tblHR_OfficeStaff
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False  # Django will not manage this table's schema
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        # Replicate the original 'Title. EmployeeName' format
        return f"{self.title + '. ' if self.title else ''}{self.employee_name}".strip()
        
    @property
    def str_display(self):
        # Property to return the string used for autocomplete display
        return f"{self.title + '. ' if self.title else ''}{self.employee_name}".strip()


class SPRMaster(models.Model):
    # This model maps to tblMM_SPR_Master
    id = models.IntegerField(db_column='Id', primary_key=True)
    session_id = models.IntegerField(db_column='SessionId')  # Corresponds to Employee.emp_id
    spr_no = models.CharField(db_column='SPRNo', max_length=50)
    fin_year_id = models.IntegerField(db_column='FinYearId')  # Corresponds to FinancialYear.fin_year_id
    sys_date = models.CharField(db_column='SysDate', max_length=20)  # Stored as string in DB
    sys_time = models.CharField(db_column='SysTime', max_length=20)  # Stored as string in DB
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False  # Django will not manage this table's schema
        db_table = 'tblMM_SPR_Master'
        verbose_name = 'SPR Master'
        verbose_name_plural = 'SPR Masters'

    def __str__(self):
        return self.spr_no

    @property
    def formatted_sys_date(self):
        """Converts the stored 'SysDate' string to 'DD/MM/YYYY' for display."""
        try:
            # The original ASP.NET code had complex date string manipulation,
            # indicating a need to parse various formats.
            # We attempt common date formats ('MM-DD-YYYY', 'DD-MM-YYYY', 'YYYY-MM-DD').
            date_formats = ["%m-%d-%Y", "%d-%m-%Y", "%Y-%m-%d"]
            for fmt in date_formats:
                try:
                    dt_obj = datetime.strptime(self.sys_date, fmt)
                    return dt_obj.strftime("%d/%m/%Y")
                except ValueError:
                    continue
            return self.sys_date  # Fallback to original string if parsing fails
        except Exception:
            return self.sys_date  # Fallback for any unexpected errors

    @property
    def display_emp_name(self):
        """Fetches the Employee's full name based on SessionId and CompId."""
        # This property will execute a query each time it's accessed, leading to N+1 queries
        # if not handled efficiently in the view (e.g., by pre-fetching or manual joins).
        # For the model's pure representation, it remains here.
        try:
            employee = Employee.objects.using('legacy_db').get(emp_id=self.session_id, comp_id=self.comp_id)
            return str(employee)
        except Employee.DoesNotExist:
            return "N/A"

    @property
    def display_fin_year(self):
        """Fetches the Financial Year string based on FinYearId and CompId."""
        # Similar to display_emp_name, this can be an N+1 query.
        try:
            fin_year_obj = FinancialYear.objects.using('legacy_db').get(fin_year_id=self.fin_year_id, comp_id=self.comp_id)
            return fin_year_obj.fin_year
        except FinancialYear.DoesNotExist:
            return "N/A"

    @classmethod
    def get_filtered_sprs(cls, spr_no_filter, emp_id_filter, current_comp_id, current_fin_year_id):
        """
        Retrieves and filters SPR records, optimizing related data fetching.
        This method encapsulates the 'LoadData' logic from the ASP.NET code-behind.
        """
        # Start with the base query filtered by company and financial year (less than or equal, as per original logic)
        qs = cls.objects.using('legacy_db').filter(
            comp_id=current_comp_id,
            fin_year_id__lte=current_fin_year_id
        ).order_by('-id')  # Order by Id Desc as per original

        if spr_no_filter:
            qs = qs.filter(spr_no__iexact=spr_no_filter)  # Case-insensitive exact match

        if emp_id_filter:
            qs = qs.filter(session_id=emp_id_filter)

        sprs = list(qs)  # Execute the main query once

        # Optimize by fetching related Employee and FinancialYear data in bulk
        session_ids = list(set(s.session_id for s in sprs))
        fin_year_ids = list(set(s.fin_year_id for s in sprs))

        # Fetch employees and financial years for the current company
        employees_map = {
            emp.emp_id: emp.str_display
            for emp in Employee.objects.using('legacy_db').filter(emp_id__in=session_ids, comp_id=current_comp_id)
        }
        financial_years_map = {
            fy.fin_year_id: fy.fin_year
            for fy in FinancialYear.objects.using('legacy_db').filter(fin_year_id__in=fin_year_ids, comp_id=current_comp_id)
        }

        # Attach pre-fetched display data to each SPR object
        for spr in sprs:
            spr.display_emp_name = employees_map.get(spr.session_id, "N/A")
            spr.display_fin_year = financial_years_map.get(spr.fin_year_id, "N/A")

        return sprs

    @property
    def get_absolute_url(self):
        """Generates the URL for the SPR details page, replicating original navigation."""
        # The original URL: ~/Module/MaterialManagement/Transactions/SPR_Edit_Details.aspx?SPRNo={0}&amp;ModId=6&amp;SubModId=31&amp;Id={1}
        # We simulate this with a Django URL pattern for a hypothetical detail page.
        from django.urls import reverse
        return reverse('spr_management:spr_detail', kwargs={'pk': self.id}) + f'?spr_no={self.spr_no}'

```

#### 4.2 Forms (`spr_management/forms.py`)

This form handles the search criteria input. It's a regular `forms.Form` as it doesn't map directly to a database model for creation/update, but rather defines query parameters.

```python
from django import forms
from django.db.models import Q

class SPRSearchForm(forms.Form):
    SEARCH_CHOICES = [
        ('0', 'Employee Name'),
        ('1', 'SPR No'),
    ]
    
    # Dropdown for search type
    search_by = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
            'hx-get': "{% url 'spr_management:toggle_search_fields' %}",
            'hx-target': '#search-fields-container',
            'hx-swap': 'outerHTML',
            'hx-trigger': 'change',
        })
    )
    
    # Input for Employee Name search, initially visible
    emp_name = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm w-80',
            'placeholder': 'Enter Employee Name',
            'id': 'txtEmpName' # Important for HTMX/Alpine.js
        })
    )
    
    # Input for SPR No search, initially hidden
    spr_no = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
            'placeholder': 'Enter SPR Number',
            'id': 'txtSprNo' # Important for HTMX/Alpine.js
        })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Control initial visibility based on the form's bound state or default
        if self.is_bound:
            if self.data.get('search_by') == '1': # If SPR No is selected
                self.fields['emp_name'].widget.attrs['style'] = 'display: none;'
            else: # If Employee Name is selected (or default)
                self.fields['spr_no'].widget.attrs['style'] = 'display: none;'
        else: # Initial form load
            self.fields['spr_no'].widget.attrs['style'] = 'display: none;'

    def clean_emp_name(self):
        """
        Cleans the employee name input.
        The original ASP.NET `fun.getCode()` likely extracted an EmpId from a string like "Employee Name [EmpId]".
        This method replicates that extraction.
        """
        emp_name_val = self.cleaned_data.get('emp_name')
        if emp_name_val:
            try:
                # Expects "Employee Name [EmpId]" format
                parts = emp_name_val.split(' [')
                if len(parts) == 2 and parts[1].endswith(']'):
                    emp_id = int(parts[1][:-1])  # Remove ']' and convert to int
                    return emp_id  # Return the extracted EmpId for filtering
            except ValueError:
                pass  # Not in expected format, proceed as if no EmpId was found
        return None  # Return None if no value or not in expected format
```

#### 4.3 Views (`spr_management/views.py`)

These are thin, class-based views (CBVs) that orchestrate the data flow and rendering. Business logic is delegated to the model (`SPRMaster.get_filtered_sprs`) and form (`SPRSearchForm.clean_emp_name`).

```python
from django.views.generic import ListView, View
from django.shortcuts import render
from django.http import JsonResponse, HttpResponse
from django.db.models import Q # Used for complex lookups in autocomplete
from .models import SPRMaster, Employee
from .forms import SPRSearchForm

class SPRListView(ListView):
    """
    Main view for displaying the SPR search and list page.
    This view renders the container for the search form and the HTMX-loaded table.
    """
    model = SPRMaster
    template_name = 'spr_management/spr/list.html'
    context_object_name = 'spr_masters'

    def get_queryset(self):
        # This view serves the initial page structure, the table data is loaded
        # via an HTMX call to SPRTablePartialView after the page loads.
        # So, we return an empty queryset initially.
        return SPRMaster.objects.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the search form
        context['form'] = SPRSearchForm(self.request.GET or None)
        
        # Pass session-like data to context (e.g., CompId, FinYearId).
        # In a real application, these would typically come from request.session
        # or the logged-in user's profile. Using placeholders for demonstration.
        context['current_comp_id'] = self.request.session.get('compid', 1) # Default to 1
        context['current_fin_year_id'] = self.request.session.get('finyear_id', 2023) # Default to 2023
        return context

class SPRTablePartialView(View):
    """
    View to dynamically load and render the SPR data table via HTMX.
    This view receives search parameters and returns the HTML for the table.
    """
    def get(self, request, *args, **kwargs):
        form = SPRSearchForm(request.GET) # Bind the form with current GET parameters
        spr_no_filter = None
        emp_id_filter = None
        
        search_by_type = request.GET.get('search_by', '0') # Default to '0' (Employee Name)

        if form.is_valid():
            if search_by_type == '1': # Search by SPR No
                spr_no_filter = form.cleaned_data.get('spr_no')
            else: # Search by Employee Name (default '0')
                # The form's clean_emp_name method already extracts the EmpId
                emp_id_filter = form.cleaned_data.get('emp_name')

        # Retrieve session-like data for filtering
        current_comp_id = request.session.get('compid', 1)
        current_fin_year_id = request.session.get('finyear_id', 2023)

        # Call the model method to get filtered and optimized SPR data
        spr_list = SPRMaster.get_filtered_sprs(spr_no_filter, emp_id_filter, 
                                               current_comp_id, current_fin_year_id)

        context = {
            'spr_masters': spr_list,
            'current_comp_id': current_comp_id, # useful for debugging or future links
        }
        # Render the partial table template
        return render(request, 'spr_management/spr/_spr_table.html', context)

class ToggleSearchFieldsView(View):
    """
    HTMX endpoint to dynamically render the correct search input field
    (Employee Name or SPR No) based on the dropdown selection.
    """
    def get(self, request, *args, **kwargs):
        search_by_type = request.GET.get('search_by', '0') # '0' for Employee Name, '1' for SPR No
        
        # Create a form instance to get field renderings
        form = SPRSearchForm(initial={'search_by': search_by_type})
        
        context = {
            'form': form,
            'search_by': search_by_type,
        }
        # Render the partial containing only the input fields
        return render(request, 'spr_management/spr/_search_fields.html', context)

class EmployeeAutocompleteView(View):
    """
    HTMX endpoint to provide JSON suggestions for employee autocomplete.
    This replicates the `GetCompletionList` web method.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('q', '').strip()
        current_comp_id = request.session.get('compid', 1) # Placeholder for CompId

        results = []
        if prefix_text:
            # Query employees whose name or ID starts with the prefix text, for the current company
            employees = Employee.objects.using('legacy_db').filter(
                Q(employee_name__istartswith=prefix_text) | Q(emp_id__icontains=prefix_text),
                comp_id=current_comp_id
            ).order_by('employee_name')[:10] # Limit results as per typical autocomplete

            # Format results as "Title. EmployeeName [EmpId]"
            results = [f"{emp.str_display} [{emp.emp_id}]" for emp in employees]
            # The original code sorts `main` array, so we sort here too.
            results.sort()
        
        return JsonResponse(results, safe=False) # safe=False for lists
```

#### 4.4 Templates (`spr_management/templates/spr_management/spr/`)

These templates provide the HTML structure and integrate HTMX, Alpine.js, and DataTables. They extend a base template (`core/base.html`) for consistent layout.

**`list.html` (Main page for SPR search and list)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">SPR - Edit</h2>
    </div>
    
    <div class="bg-white p-6 rounded-lg shadow-md mb-6">
        <form id="sprSearchForm" 
              hx-get="{% url 'spr_management:spr_table_partial' %}" 
              hx-target="#sprTable-container" 
              hx-swap="innerHTML" 
              hx-indicator="#loadingIndicator"
              class="space-y-4"> {# Add some vertical spacing #}
            {% csrf_token %}
            <div class="flex flex-col sm:flex-row items-end space-y-4 sm:space-y-0 sm:space-x-4">
                <div class="flex-grow w-full sm:w-auto">
                    <label for="{{ form.search_by.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By</label>
                    {{ form.search_by }}
                </div>
                
                {# HTMX-driven dynamic search fields container #}
                {# The 'load' trigger and hx-get ensures initial rendering and subsequent updates #}
                <div id="search-fields-container" 
                     hx-trigger="load" 
                     hx-get="{% url 'spr_management:toggle_search_fields' %}?search_by={% if form.is_bound %}{{ form.search_by.value }}{% else %}0{% endif %}" 
                     hx-swap="outerHTML" 
                     class="flex-grow w-full sm:w-auto relative"> {# relative for autocomplete positioning #}
                    {# Initial content will be replaced by HTMX after load #}
                    {# This partial is just a placeholder, the view's output will populate it #}
                    {% include 'spr_management/spr/_search_fields.html' %}
                </div>
                
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow-sm flex-shrink-0 w-full sm:w-auto">
                    Search
                </button>
                <div id="loadingIndicator" class="htmx-indicator ml-4 flex-shrink-0">
                    <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                </div>
            </div>
        </form>
    </div>

    {# Container for the DataTables table, loaded via HTMX #}
    <div id="sprTable-container"
         hx-trigger="load, refreshSPRList from:body" {# Load on page load and on custom event #}
         hx-get="{% url 'spr_management:spr_table_partial' %}"
         hx-swap="innerHTML"
         hx-indicator="#loadingIndicator"
         class="mt-6 bg-white shadow-md rounded-lg p-4">
        <!-- Initial loading state for user feedback -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading SPR Data...</p>
        </div>
    </div>
    
</div>
{% endblock %}

{% block extra_js %}
<!-- Include jQuery for DataTables (if not already in base.html) -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>

<!-- DataTables CSS and JS (assuming Tailwind CSS integration via dataTables.tailwindcss.min.css/js) -->
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.8/css/dataTables.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.8/css/dataTables.tailwindcss.min.css">
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.tailwindcss.min.js"></script>

<!-- Alpine.js (if not already in base.html) -->
<script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.14.0/dist/cdn.min.js"></script>


<script>
    // DataTables re-initialization after HTMX swap
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.target.id === 'sprTable-container') {
            // Destroy existing DataTable instance before initializing a new one
            if ($.fn.DataTable.isDataTable('#sprTable')) {
                $('#sprTable').DataTable().destroy();
            }
            $('#sprTable').DataTable({
                "pageLength": 20, // Match original PageSize
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "responsive": true,
                "language": {
                    "emptyTable": "No data to display !", // Match original EmptyDataTemplate message
                    "search": "Filter records:", // Customize search label
                },
                "columnDefs": [
                    { "orderable": false, "targets": [1] } // Disable sorting on "Select" column
                ]
            });
        }
    });

    // Alpine.js setup for the employee name autocomplete
    document.addEventListener('htmx:afterSwap', function(event) {
        // This targets the specific div that contains the dynamically swapped search fields
        if (event.detail.target.id === 'search-fields-container') {
            const empNameInput = document.getElementById('txtEmpName');
            if (empNameInput) {
                // Initialize Alpine.js for the autocomplete functionality on the input field
                Alpine.data('autocompleteEmployee', () => ({
                    suggestions: [],
                    searchTerm: '',
                    selectedSuggestionIndex: -1,
                    showSuggestions: false,
                    init() {
                        this.$watch('searchTerm', (term) => {
                            if (term.length >= 1) { // MinimumPrefixLength="1"
                                clearTimeout(this.searchTimeout);
                                this.searchTimeout = setTimeout(() => {
                                    fetch(`{% url "spr_management:employee_autocomplete" %}?q=${encodeURIComponent(term)}`)
                                        .then(response => response.json())
                                        .then(data => {
                                            this.suggestions = data;
                                            this.showSuggestions = data.length > 0;
                                            this.selectedSuggestionIndex = data.length > 0 ? 0 : -1; // FirstRowSelected="True"
                                        });
                                }, 100); // CompletionInterval="100"
                            } else {
                                this.suggestions = [];
                                this.showSuggestions = false;
                            }
                        });
                        // Initialize searchTerm with the current value if any (e.g. from previous search)
                        this.searchTerm = empNameInput.value;
                    },
                    selectSuggestion(index) {
                        if (index !== -1) {
                            this.searchTerm = this.suggestions[index];
                            this.showSuggestions = false;
                            this.selectedSuggestionIndex = -1; // Reset selection
                            empNameInput.value = this.searchTerm; // Update hidden input value
                        }
                    },
                    handleKeydown(event) {
                        if (event.key === 'ArrowDown') {
                            event.preventDefault();
                            this.selectedSuggestionIndex = (this.selectedSuggestionIndex + 1) % this.suggestions.length;
                            this.$nextTick(() => this.$refs.suggestionsList.children[this.selectedSuggestionIndex]?.scrollIntoView({ block: 'nearest' }));
                        } else if (event.key === 'ArrowUp') {
                            event.preventDefault();
                            this.selectedSuggestionIndex = (this.selectedSuggestionIndex - 1 + this.suggestions.length) % this.suggestions.length;
                            this.$nextTick(() => this.$refs.suggestionsList.children[this.selectedSuggestionIndex]?.scrollIntoView({ block: 'nearest' }));
                        } else if (event.key === 'Enter') {
                            event.preventDefault();
                            this.selectSuggestion(this.selectedSuggestionIndex);
                            document.getElementById('sprSearchForm').requestSubmit(); // Trigger form submission after selection
                        } else if (event.key === 'Escape') {
                            this.showSuggestions = false;
                        }
                    }
                }));

                // Attach Alpine.js data and event handlers to the input
                empNameInput.setAttribute('x-data', 'autocompleteEmployee');
                empNameInput.setAttribute('x-model', 'searchTerm');
                empNameInput.setAttribute('@keydown', 'handleKeydown');
                empNameInput.setAttribute('@focus', 'showSuggestions = suggestions.length > 0');
                empNameInput.setAttribute('@blur.away', 'showSuggestions = false');
                empNameInput.setAttribute('autocomplete', 'off');

                // Create and insert the autocomplete suggestion list container
                let suggestionsContainer = empNameInput.nextElementSibling;
                if (!suggestionsContainer || !suggestionsContainer.hasAttribute('x-show')) {
                    suggestionsContainer = document.createElement('div');
                    suggestionsContainer.setAttribute('x-show', 'showSuggestions');
                    suggestionsContainer.setAttribute('x-cloak', ''); // Hide until Alpine processes
                    suggestionsContainer.setAttribute('class', 'absolute z-10 w-full bg-white border border-gray-300 mt-1 max-h-60 overflow-y-auto rounded-md shadow-lg');
                    suggestionsContainer.setAttribute('x-ref', 'suggestionsList');
                    suggestionsContainer.innerHTML = `
                        <template x-for="(suggestion, index) in suggestions" :key="index">
                            <div class="py-2 px-3 cursor-pointer hover:bg-gray-100 bg"
                                 :class="{ 'bg-blue-100 bgtext': index === selectedSuggestionIndex }" {# bgtext is for highlight #}
                                 @mousedown="selectSuggestion(index)">
                                <span x-text="suggestion"></span>
                            </div>
                        </template>
                        <div x-show="suggestions.length === 0 && searchTerm.length > 0" class="py-2 px-3 text-gray-500">No results found.</div>
                    `;
                    empNameInput.parentNode.insertBefore(suggestionsContainer, empNameInput.nextSibling);
                }
                
                // Re-initialize Alpine.js on the new content if it was swapped
                Alpine.initTree(empNameInput.closest('[x-data]'));
            }
        }
    });

    // Ensure the initial form load triggers the search fields rendering and table load.
    // This allows HTMX to populate the initial UI state.
    document.addEventListener('DOMContentLoaded', () => {
        htmx.trigger(document.getElementById('search-fields-container'), 'load');
        htmx.trigger(document.getElementById('sprTable-container'), 'load');
    });

</script>
{% endblock %}
```

**`_spr_table.html` (Partial for the DataTables content)**

```html
<table id="sprTable" class="min-w-full bg-white yui-datatable-theme display compact">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">SPRNo</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/2">Gen By</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in spr_masters %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                {# Replicates original HyperLinkField to SPR_Edit_Details.aspx #}
                <a href="{{ obj.get_absolute_url }}" class="text-blue-600 hover:text-blue-800 font-medium">Select</a>
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.spr_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.display_fin_year }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.formatted_sys_date }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.sys_time }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.display_emp_name }}</td>
        </tr>
        {% empty %}
        {# DataTables will handle the "No data to display" message via its language options, #}
        {# but this fallback ensures a message if DataTables doesn't initialize or for non-JS scenarios. #}
        {% endfor %}
    </tbody>
</table>

{# DataTables initialization is handled in the parent list.html's htmx:afterSwap event listener #}
{# so no inline script is needed here, making this partial truly content-only. #}
```

**`_search_fields.html` (Partial for dynamic search inputs)**

```html
{# This partial is used by HTMX to swap the content of #search-fields-container #}
{# It renders either the employee name input or the SPR number input based on `search_by` context #}
<div id="search-fields-container" 
     class="flex-grow w-full sm:w-auto relative"> {# relative for autocomplete positioning #}
    {% if search_by == '1' %} {# SPR No is selected #}
        <div class="flex-grow">
            <label for="{{ form.spr_no.id_for_label }}" class="block text-sm font-medium text-gray-700">SPR No</label>
            {{ form.spr_no }}
        </div>
        {# The emp_name field is logically hidden by not being rendered here, or by Alpine's style if always present #}
        {# For simplicity and strict separation, only the active field is rendered. #}
    {% else %} {# Employee Name is selected (default, '0') #}
        <div class="flex-grow">
            <label for="{{ form.emp_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Employee Name</label>
            {# The emp_name input will be wrapped by Alpine.js for autocomplete #}
            {{ form.emp_name }}
        </div>
        {# The spr_no field is logically hidden #}
    {% endif %}
</div>
```

#### 4.5 URLs (`spr_management/urls.py`)

This file defines the URL patterns for your Django application, linking URLs to your views.

```python
from django.urls import path
from django.http import HttpResponse # For placeholder detail view
from .views import SPRListView, SPRTablePartialView, ToggleSearchFieldsView, EmployeeAutocompleteView

app_name = 'spr_management' # Namespace for URLs

urlpatterns = [
    # Main SPR list and search page
    path('spr/edit/', SPRListView.as_view(), name='spr_list'),
    
    # HTMX endpoint to load the SPR data table
    path('spr/edit/table/', SPRTablePartialView.as_view(), name='spr_table_partial'),
    
    # HTMX endpoint to dynamically toggle search input fields
    path('spr/edit/toggle_search_fields/', ToggleSearchFieldsView.as_view(), name='toggle_search_fields'),
    
    # HTMX endpoint for employee name autocomplete suggestions (returns JSON)
    path('spr/edit/autocomplete_employee/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),

    # Placeholder for the SPR detail page, matching the original navigation link format.
    # In a full migration, this would be a proper Django detail view (e.g., UpdateView/DetailView).
    path('spr/<int:pk>/details/', lambda request, pk: HttpResponse(f"<html><head><title>SPR Details</title></head><body><h1 style='text-align: center;'>Details for SPR ID: {pk} (SPR No: {request.GET.get('spr_no', 'N/A')})</h1><p style='text-align: center;'>This is a placeholder for the SPR detail page. In a real application, this would load the full details and allow editing.</p></body></html>"), name='spr_detail'),
]
```

#### 4.6 Tests (`spr_management/tests.py`)

Comprehensive tests for models and views ensure the correctness and robustness of the migrated functionality. For `managed=False` models, mocking database interactions is common in tests to avoid needing a live legacy database connection during the test run.

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from .models import SPRMaster, Employee, FinancialYear
from .forms import SPRSearchForm
from datetime import datetime

# Configure a dummy 'legacy_db' for testing purposes to satisfy the .using() calls
# In a real setup, you'd ensure your test runner correctly sets up DBs
# For this example, we mock the .using('legacy_db') call.
class MockQuerySet(MagicMock):
    """A mock QuerySet that supports chaining methods."""
    def filter(self, *args, **kwargs):
        return self._mock_self.filter(*args, **kwargs)

    def order_by(self, *args, **kwargs):
        return self._mock_self.order_by(*args, **kwargs)

    def values(self, *args, **kwargs):
        return self._mock_self.values(*args, **kwargs)

    def get(self, *args, **kwargs):
        return self._mock_self.get(*args, **kwargs)


class SPRModelTest(TestCase):
    """Unit tests for the SPRMaster, Employee, and FinancialYear models."""

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        # Patch the .objects.using('legacy_db') to return a mock QuerySet
        # This allows us to test the model methods without a real legacy database connection.
        cls.patcher_spr_objects = patch.object(SPRMaster.objects, 'using', MagicMock(return_value=SPRMaster.objects))
        cls.patcher_employee_objects = patch.object(Employee.objects, 'using', MagicMock(return_value=Employee.objects))
        cls.patcher_financial_objects = patch.object(FinancialYear.objects, 'using', MagicMock(return_value=FinancialYear.objects))
        
        cls.patcher_spr_objects.start()
        cls.patcher_employee_objects.start()
        cls.patcher_financial_objects.start()

    @classmethod
    def tearDownClass(cls):
        cls.patcher_spr_objects.stop()
        cls.patcher_employee_objects.stop()
        cls.patcher_financial_objects.stop()
        super().tearDownClass()

    def setUp(self):
        # Create test data directly for Django's test database,
        # which acts as the 'legacy_db' for these mocked tests.
        SPRMaster.objects.all().delete()
        Employee.objects.all().delete()
        FinancialYear.objects.all().delete()

        self.fy1 = FinancialYear.objects.create(fin_year_id=2022, fin_year="2022-2023", comp_id=1)
        self.fy2 = FinancialYear.objects.create(fin_year_id=2023, fin_year="2023-2024", comp_id=1)
        
        self.emp1 = Employee.objects.create(emp_id=101, title="Mr.", employee_name="John Doe", comp_id=1)
        self.emp2 = Employee.objects.create(emp_id=102, title="Ms.", employee_name="Jane Smith", comp_id=1)
        
        self.spr1 = SPRMaster.objects.create(
            id=1, session_id=101, spr_no="SPR001", fin_year_id=2022, sys_date="01-15-2023", sys_time="10:00 AM", comp_id=1
        )
        self.spr2 = SPRMaster.objects.create(
            id=2, session_id=102, spr_no="SPR002", fin_year_id=2023, sys_date="02-20-2024", sys_time="11:30 AM", comp_id=1
        )
        self.spr3 = SPRMaster.objects.create(
            id=3, session_id=101, spr_no="SPR003", fin_year_id=2023, sys_date="03-01-2024", sys_time="01:00 PM", comp_id=1
        )

    def test_spr_creation(self):
        """Test basic creation and retrieval of SPRMaster objects."""
        self.assertEqual(SPRMaster.objects.count(), 3)
        spr = SPRMaster.objects.get(id=1)
        self.assertEqual(spr.spr_no, "SPR001")
        self.assertEqual(spr.session_id, 101)

    def test_formatted_sys_date_property(self):
        """Test the formatted_sys_date property for correct date formatting."""
        self.assertEqual(self.spr1.formatted_sys_date, "15/01/2023")
        self.assertEqual(self.spr2.formatted_sys_date, "20/02/2024")
        
        # Test with different input formats
        spr_test = SPRMaster.objects.create(id=4, session_id=101, spr_no="SPR004", fin_year_id=2023, sys_date="12-25-2023", sys_time="09:00 AM", comp_id=1)
        self.assertEqual(spr_test.formatted_sys_date, "25/12/2023")
        
        spr_test.sys_date = "2023-10-05" # YYYY-MM-DD
        self.assertEqual(spr_test.formatted_sys_date, "05/10/2023")

        # Test with an unparseable date, should return original string
        self.spr1.sys_date = "invalid-date-format"
        self.assertEqual(self.spr1.formatted_sys_date, "invalid-date-format")

    def test_display_emp_name_property(self):
        """Test the display_emp_name property, including cases where employee is not found."""
        with patch.object(Employee.objects, 'get') as mock_get:
            mock_get.return_value = self.emp1
            self.assertEqual(self.spr1.display_emp_name, "Mr. John Doe")
            mock_get.assert_called_with(emp_id=self.spr1.session_id, comp_id=self.spr1.comp_id)

            mock_get.side_effect = Employee.DoesNotExist
            self.assertEqual(self.spr1.display_emp_name, "N/A")

    def test_display_fin_year_property(self):
        """Test the display_fin_year property, including cases where financial year is not found."""
        with patch.object(FinancialYear.objects, 'get') as mock_get:
            mock_get.return_value = self.fy1
            self.assertEqual(self.spr1.display_fin_year, "2022-2023")
            mock_get.assert_called_with(fin_year_id=self.spr1.fin_year_id, comp_id=self.spr1.comp_id)

            mock_get.side_effect = FinancialYear.DoesNotExist
            self.assertEqual(self.spr1.display_fin_year, "N/A")

    def test_get_filtered_sprs_no_filters(self):
        """Test filtering with no search criteria, only global context."""
        with patch.object(Employee.objects, 'filter') as mock_emp_filter, \
             patch.object(FinancialYear.objects, 'filter') as mock_fy_filter:
            
            mock_emp_filter.return_value = [self.emp1, self.emp2] # Mock employees fetched in bulk
            mock_fy_filter.return_value = [self.fy1, self.fy2] # Mock financial years fetched in bulk

            # Filter by current_comp_id=1 and fin_year_id <= 2023
            filtered_sprs = SPRMaster.get_filtered_sprs(None, None, 1, 2023)
            
            self.assertEqual(len(filtered_sprs), 2)
            # Should return SPR3 and SPR2, ordered by ID Desc
            self.assertEqual(filtered_sprs[0].spr_no, "SPR003") 
            self.assertEqual(filtered_sprs[1].spr_no, "SPR002")
            
            self.assertEqual(filtered_sprs[0].display_emp_name, "Mr. John Doe")
            self.assertEqual(filtered_sprs[0].display_fin_year, "2023-2024")
            self.assertEqual(filtered_sprs[1].display_emp_name, "Ms. Jane Smith")
            self.assertEqual(filtered_sprs[1].display_fin_year, "2023-2024")

    def test_get_filtered_sprs_by_spr_no(self):
        """Test filtering by SPR Number."""
        with patch.object(Employee.objects, 'filter') as mock_emp_filter, \
             patch.object(FinancialYear.objects, 'filter') as mock_fy_filter:
            mock_emp_filter.return_value = [self.emp1]
            mock_fy_filter.return_value = [self.fy1]
            filtered_sprs = SPRMaster.get_filtered_sprs("SPR001", None, 1, 2023)
            self.assertEqual(len(filtered_sprs), 1)
            self.assertEqual(filtered_sprs[0].spr_no, "SPR001")

    def test_get_filtered_sprs_by_emp_id(self):
        """Test filtering by Employee ID (SessionId)."""
        with patch.object(Employee.objects, 'filter') as mock_emp_filter, \
             patch.object(FinancialYear.objects, 'filter') as mock_fy_filter:
            mock_emp_filter.return_value = [self.emp1, self.emp2]
            mock_fy_filter.return_value = [self.fy1, self.fy2]
            filtered_sprs = SPRMaster.get_filtered_sprs(None, 101, 1, 2023)
            self.assertEqual(len(filtered_sprs), 2)
            # Ordered by ID desc: SPR3, then SPR1
            self.assertEqual(filtered_sprs[0].spr_no, "SPR003")
            self.assertEqual(filtered_sprs[1].spr_no, "SPR001")

    def test_get_absolute_url(self):
        """Test the URL generation for the SPR detail page."""
        expected_url = f'/spr_management/spr/{self.spr1.id}/details/?spr_no={self.spr1.spr_no}'
        self.assertEqual(self.spr1.get_absolute_url, expected_url)


class SPRViewsTest(TestCase):
    """Integration tests for SPR-related views."""

    client = Client()

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        # Patch the .objects.using('legacy_db') for views as well
        cls.patcher_spr_objects = patch.object(SPRMaster.objects, 'using', MagicMock(return_value=SPRMaster.objects))
        cls.patcher_employee_objects = patch.object(Employee.objects, 'using', MagicMock(return_value=Employee.objects))
        cls.patcher_financial_objects = patch.object(FinancialYear.objects, 'using', MagicMock(return_value=FinancialYear.objects))
        
        cls.patcher_spr_objects.start()
        cls.patcher_employee_objects.start()
        cls.patcher_financial_objects.start()

    @classmethod
    def tearDownClass(cls):
        cls.patcher_spr_objects.stop()
        cls.patcher_employee_objects.stop()
        cls.patcher_financial_objects.stop()
        super().tearDownClass()

    def setUp(self):
        # Create test data for views
        SPRMaster.objects.all().delete()
        Employee.objects.all().delete()
        FinancialYear.objects.all().delete()

        self.fy1 = FinancialYear.objects.create(fin_year_id=2022, fin_year="2022-2023", comp_id=1)
        self.fy2 = FinancialYear.objects.create(fin_year_id=2023, fin_year="2023-2024", comp_id=1)
        self.emp1 = Employee.objects.create(emp_id=101, title="Mr.", employee_name="John Doe", comp_id=1)
        self.emp2 = Employee.objects.create(emp_id=102, title="Ms.", employee_name="Jane Smith", comp_id=1)
        self.spr1 = SPRMaster.objects.create(
            id=1, session_id=101, spr_no="SPR001", fin_year_id=2022, sys_date="01-15-2023", sys_time="10:00 AM", comp_id=1
        )
        self.spr2 = SPRMaster.objects.create(
            id=2, session_id=102, spr_no="SPR002", fin_year_id=2023, sys_date="02-20-2024", sys_time="11:30 AM", comp_id=1
        )
        self.spr3 = SPRMaster.objects.create(
            id=3, session_id=101, spr_no="SPR003", fin_year_id=2023, sys_date="03-01-2024", sys_time="01:00 PM", comp_id=1
        )
        # Mock session variables that the views rely on
        session = self.client.session
        session['compid'] = 1
        session['finyear_id'] = 2023
        session.save()


    def test_spr_list_view_get(self):
        """Test the main SPR list page loads correctly."""
        response = self.client.get(reverse('spr_management:spr_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'spr_management/spr/list.html')
        self.assertIsInstance(response.context['form'], SPRSearchForm)
        self.assertContains(response, '<h2 class="text-2xl font-bold text-gray-800">SPR - Edit</h2>')

    def test_spr_table_partial_view_no_filters(self):
        """Test the HTMX partial for the table with no explicit filters."""
        # Mock the `get_filtered_sprs` method to control returned data for this test
        with patch('spr_management.models.SPRMaster.get_filtered_sprs') as mock_get_filtered:
            # Assume these are the results for default filters (comp_id=1, fin_year_id<=2023, order by ID desc)
            mock_get_filtered.return_value = [self.spr3, self.spr2] 
            
            response = self.client.get(reverse('spr_management:spr_table_partial'))
            self.assertEqual(response.status_code, 200)
            self.assertTemplateUsed(response, 'spr_management/spr/_spr_table.html')
            self.assertContains(response, "SPR003")
            self.assertContains(response, "SPR002")
            self.assertNotContains(response, "SPR001")
            
            # Verify the model method was called with expected default parameters
            mock_get_filtered.assert_called_with(None, None, 1, 2023)

    def test_spr_table_partial_view_with_spr_no_filter(self):
        """Test the HTMX partial for the table with an SPR Number filter."""
        with patch('spr_management.models.SPRMaster.get_filtered_sprs') as mock_get_filtered:
            mock_get_filtered.return_value = [self.spr1] # Mock filtered result
            response = self.client.get(reverse('spr_management:spr_table_partial'), 
                                       {'search_by': '1', 'spr_no': 'SPR001'})
            self.assertEqual(response.status_code, 200)
            self.assertContains(response, "SPR001")
            self.assertNotContains(response, "SPR002")
            mock_get_filtered.assert_called_with('SPR001', None, 1, 2023)

    def test_spr_table_partial_view_with_employee_name_filter(self):
        """Test the HTMX partial for the table with an Employee Name filter."""
        with patch('spr_management.models.SPRMaster.get_filtered_sprs') as mock_get_filtered:
            mock_get_filtered.return_value = [self.spr3, self.spr1] # Mock filtered results
            response = self.client.get(reverse('spr_management:spr_table_partial'), 
                                       {'search_by': '0', 'emp_name': 'John Doe [101]'})
            self.assertEqual(response.status_code, 200)
            self.assertContains(response, "SPR003")
            self.assertContains(response, "SPR001")
            self.assertNotContains(response, "SPR002")
            mock_get_filtered.assert_called_with(None, 101, 1, 2023)
            
    def test_spr_table_partial_view_with_invalid_employee_name_format(self):
        """Test the HTMX partial when employee name format is invalid."""
        with patch('spr_management.models.SPRMaster.get_filtered_sprs') as mock_get_filtered:
            mock_get_filtered.return_value = [] # Should return no results as emp_id_filter will be None
            response = self.client.get(reverse('spr_management:spr_table_partial'), 
                                       {'search_by': '0', 'emp_name': 'Just a Name'})
            self.assertEqual(response.status_code, 200)
            # The model method should be called with emp_id_filter as None
            mock_get_filtered.assert_called_with(None, None, 1, 2023)


    def test_toggle_search_fields_view_emp_name(self):
        """Test that the correct input field (Employee Name) is rendered."""
        response = self.client.get(reverse('spr_management:toggle_search_fields'), 
                                   {'search_by': '0'}) # Employee Name
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'spr_management/spr/_search_fields.html')
        self.assertContains(response, 'id="txtEmpName"')
        self.assertNotContains(response, 'id="txtSprNo"')

    def test_toggle_search_fields_view_spr_no(self):
        """Test that the correct input field (SPR No) is rendered."""
        response = self.client.get(reverse('spr_management:toggle_search_fields'), 
                                   {'search_by': '1'}) # SPR No
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'spr_management/spr/_search_fields.html')
        self.assertContains(response, 'id="txtSprNo"')
        self.assertNotContains(response, 'id="txtEmpName"')

    def test_employee_autocomplete_view(self):
        """Test the employee autocomplete endpoint returns correct JSON suggestions."""
        # Ensure employees are present for autocomplete query results
        Employee.objects.create(emp_id=103, title="Dr.", employee_name="Alice Wonderland", comp_id=1)
        Employee.objects.create(emp_id=104, title="Mr.", employee_name="Bob Johnson", comp_id=2) # Different company

        # Test with name prefix
        response = self.client.get(reverse('spr_management:employee_autocomplete'), {'q': 'john'})
        self.assertEqual(response.status_code, 200)
        json_data = response.json()
        self.assertIn("Mr. John Doe [101]", json_data)
        self.assertNotIn("Ms. Jane Smith [102]", json_data)
        self.assertNotIn("Mr. Bob Johnson [104]", json_data) # Filtered by comp_id

        # Test with ID prefix (as per original GetCompletionList filtering by EmpId)
        response = self.client.get(reverse('spr_management:employee_autocomplete'), {'q': '10'})
        json_data = response.json()
        self.assertIn("Mr. John Doe [101]", json_data)
        self.assertIn("Ms. Jane Smith [102]", json_data)
        self.assertIn("Dr. Alice Wonderland [103]", json_data)
        self.assertNotIn("Mr. Bob Johnson [104]", json_data)

        # Test with empty prefix
        response = self.client.get(reverse('spr_management:employee_autocomplete'), {'q': ''})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), []) # Should return empty list

    def test_spr_detail_url(self):
        """Test the placeholder SPR detail URL."""
        response = self.client.get(reverse('spr_management:spr_detail', args=[self.spr1.id]), {'spr_no': self.spr1.spr_no})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, f"Details for SPR ID: {self.spr1.id}")
        self.assertContains(response, f"SPR No: {self.spr1.spr_no}")

```

---

### Step 5: HTMX and Alpine.js Integration

**Instructions and Implementation Details:**

1.  **Core HTMX for Page Updates:**
    *   The main `list.html` template uses `hx-trigger="load, refreshSPRList from:body"` and `hx-get="{% url 'spr_management:spr_table_partial' %}"` on the `#sprTable-container` div. This means the SPR list table is loaded asynchronously when the page first loads (`load`) and can be refreshed on demand by dispatching a custom `refreshSPRList` event from anywhere on the page (e.g., after an add/edit/delete operation in another module, though not directly applicable here).
    *   The search form is also configured with `hx-get` to the `spr_table_partial` endpoint, so submitting the form (via `Search` button) will refresh only the table, not the entire page.

2.  **Dynamic Search Field Toggling with HTMX:**
    *   The `search_by` dropdown (`form.search_by`) has `hx-get` to `{% url 'spr_management:toggle_search_fields' %}`. When its value changes (`hx-trigger="change"`), an HTMX request is sent.
    *   The response targets `#search-fields-container` and swaps its `outerHTML`, effectively replacing the input field (either `txtEmpName` or `txtSprNo`) and its label. This replicates the `Visible = true/false` logic of ASP.NET.

3.  **Employee Autocomplete with Alpine.js & HTMX:**
    *   The `txtEmpName` input is enhanced with Alpine.js. An `x-data` attribute with an `autocompleteEmployee` component manages the search term, suggestions array, and UI state (showing/hiding suggestions).
    *   As the user types (`x-model="searchTerm"`), an `x-watch` directive detects changes and triggers a `fetch` request to `{% url "spr_management:employee_autocomplete" %}`. This endpoint returns a JSON array of formatted employee names.
    *   Alpine.js then populates and displays the suggestions, handles keyboard navigation (`@keydown`), and allows selection (`@mousedown` or `Enter`). The `FirstRowSelected="True"` logic is also replicated.
    *   The `htmx:afterSwap` listener in `list.html` ensures that when the `_search_fields.html` partial is loaded (e.g., when toggling search types), the Alpine.js component for `txtEmpName` is correctly re-initialized and attached.

4.  **DataTables for List Views:**
    *   The `_spr_table.html` partial contains a standard `<table>` element with `id="sprTable"`.
    *   In `list.html`, a JavaScript listener `htmx:afterSwap` detects when the `sprTable-container` div is updated. Inside this listener, it re-initializes `$('#sprTable').DataTable()` with the desired options (e.g., `pageLength`, `lengthMenu`, `language` for empty table message). This ensures DataTables works correctly even after HTMX replaces the table content.
    *   This provides client-side searching, sorting, and pagination, offloading processing from the server and providing a snappier user experience.

5.  **No Custom JavaScript (Beyond HTMX/Alpine/DataTables):**
    *   All dynamic interactions are handled through HTMX attributes and Alpine.js directives. There are no large, custom JavaScript files or complex DOM manipulation scripts, adhering to the "no additional JavaScript" principle.

6.  **Loading Indicators:**
    *   `hx-indicator="#loadingIndicator"` is used on HTMX elements (`sprSearchForm`, `sprTable-container`) to show a simple spinning loader (`htmx-indicator` class and CSS) while data is being fetched. This provides crucial user feedback during asynchronous operations.

This complete plan, from database schema mapping to frontend interactions and testing, provides a robust and modern Django replacement for your ASP.NET application, ready for AI-assisted automation.