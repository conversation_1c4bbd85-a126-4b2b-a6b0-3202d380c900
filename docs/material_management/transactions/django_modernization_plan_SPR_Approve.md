This document outlines a comprehensive plan for modernizing your ASP.NET application, specifically the `SPR_Approve.aspx` module, by migrating it to a robust Django 5.0+ solution. This plan emphasizes automation, clear instructions, and modern web development best practices, ensuring a smooth transition with significant business benefits.

## ASP.NET to Django Conversion Script:

This section details the automated conversion process for the `SPR_Approve` module.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists in `core/base.html`.
- Focus ONLY on component-specific code for the current module (`material_management` app).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html`.
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code interacts with three primary tables:
1.  `tblMM_SPR_Master`: The main table for SPR (Store Purchase Requisition) approval.
2.  `tblHR_OfficeStaff`: Used to retrieve employee names based on `SessionId`.
3.  `tblFinancial_master`: Used to retrieve financial year names.

**Inferred Tables and Columns:**

*   **`tblMM_SPR_Master`**
    *   `Id` (PK, int)
    *   `SPRNo` (string)
    *   `SysDate` (date)
    *   `SysTime` (time)
    *   `SessionId` (string/int, representing `EmpId` from `tblHR_OfficeStaff`)
    *   `Checked` (int, Boolean: 0 or 1)
    *   `CheckedDate` (date)
    *   `Approve` (int, Boolean: 0 or 1)
    *   `ApprovedBy` (string)
    *   `ApproveDate` (date)
    *   `ApproveTime` (time)
    *   `Authorize` (int, Boolean: 0 or 1)
    *   `AuthorizeDate` (date)
    *   `FinYearId` (string/int, representing `FinYearId` from `tblFinancial_master`)
    *   `CompId` (int)

*   **`tblHR_OfficeStaff`**
    *   `EmpId` (PK, string/int)
    *   `EmployeeName` (string)
    *   `Title` (string)
    *   `CompId` (int)

*   **`tblFinancial_master`**
    *   `FinYearId` (PK, string/int)
    *   `FinYear` (string)
    *   `CompId` (int)

### Step 2: Identify Backend Functionality

**Task:** Determine the operations performed by the ASP.NET code.

**Analysis:**
The ASP.NET page `SPR_Approve.aspx` is primarily a **read** (list/search) and **update** (bulk approve) page. It also has a **view** (redirect) functionality for individual records.

*   **Read (List & Search):**
    *   Populates a `GridView` with SPR records (`tblMM_SPR_Master`) that are `Checked='1'` and `Approve='0'`.
    *   Filters can be applied by `SPRNo` or `Employee Name` (`SessionId` mapping to `EmpId`).
    *   Data is pulled from `tblMM_SPR_Master`, `tblHR_OfficeStaff` (for `GenBy`), and `tblFinancial_master` (for `FinYear`).
    *   Pagination is supported by `GridView2_PageIndexChanging`.
    *   Employee name autocomplete (`GetCompletionList`) for the search field.

*   **Update (Bulk Approve):**
    *   The "Approved" button (`App_Click`) iterates through selected (checked) rows in the `GridView`.
    *   For each selected row, it updates the corresponding `tblMM_SPR_Master` record by setting `Approve='1'`, `ApprovedBy`, `ApproveDate`, and `ApproveTime`.

*   **View (Redirect):**
    *   The "View" `LinkButton` in each row (`GridView2_RowCommand`) redirects the user to `SPR_View_Print.aspx` with various query parameters (`Id`, `SPRNo`, etc.).

*   **UI Logic:**
    *   The `drpfield_SelectedIndexChanged` event toggles the visibility of the `txtEmpName` and `txtSprNo` textboxes based on the selected search criteria.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**
The page uses several ASP.NET controls:

*   **Input Controls:**
    *   `drpfield` (DropDownList): For selecting search type (Employee Name/SPR No). This will be a standard HTML `<select>` with Alpine.js for dynamic input display.
    *   `txtEmpName` (TextBox): For employee name input.
    *   `txtEmpName_AutoCompleteExtender`: Provides autocomplete for `txtEmpName`. This will be handled by HTMX for fetching suggestions and Alpine.js for displaying and managing the autocomplete dropdown.
    *   `txtSprNo` (TextBox): For SPR number input.

*   **Action Buttons:**
    *   `Button1` ("Search"): Triggers the data reload. This will be an HTMX `hx-get` on the form submission.
    *   `App` ("Approved"): Triggers the bulk approval. This will be an HTMX `hx-post` with a form.

*   **Data Display:**
    *   `GridView2`: Displays tabular data. This will be replaced by a standard HTML `<table>` enhanced with DataTables for client-side sorting, filtering, and pagination.
    *   `Panel1`: Acts as a container for `GridView2` with scrollbars. The DataTables table will handle overflow, and a `<div>` will replace this.

*   **Interactive Elements within GridView:**
    *   `LinkButton` (View): To navigate to a detailed view page. This will be a standard `<a>` tag.
    *   `CheckBox` (CK): For selecting records for approval. This will be a standard `<input type="checkbox">`.
    *   `Label`s (lblsprno, lblId, etc.): Display data. These will be Django template variables.

### Step 4: Generate Django Code

We will create a new Django application named `material_management` to house this functionality.

#### 4.1 Models

**Task:** Create Django models based on the identified database schema.

**Explanation:**
We define three models (`SprMaster`, `EmployeeStaff`, `FinancialYear`) to represent the tables. `managed = False` is crucial to tell Django not to manage table creation, as they already exist. Custom managers are added to `SprMaster` and `EmployeeStaff` to encapsulate complex query logic, adhering to the "fat model" principle.

**File: `material_management/models.py`**

```python
from django.db import models
from django.utils import timezone

class SprMasterManager(models.Manager):
    """
    Custom manager for SprMaster model to handle complex queries
    and data enrichment (GenBy, FinYear lookup).
    """
    def get_approvable_sprs(self, fin_year_id, comp_id, spr_no_filter=None, emp_name_filter=None):
        """
        Retrieves SPR records that are checked, not yet approved,
        and match the given filters.
        Enriches results with GenBy employee name and Financial Year name.
        """
        queryset = self.filter(
            fin_year_id__lte=fin_year_id,
            comp_id=comp_id,
            checked_status=True,  # Equivalent to Checked='1'
            approve_status=False  # Equivalent to Approve='0'
        ).order_by('-id')

        if spr_no_filter:
            queryset = queryset.filter(spr_no=spr_no_filter)

        if emp_name_filter:
            # Map EmployeeName to EmpId (SessionId) using EmployeeStaff model
            from .models import EmployeeStaff # Local import to avoid circular dependency
            try:
                emp_id_obj = EmployeeStaff.objects.get(employee_name=emp_name_filter, comp_id=comp_id)
                queryset = queryset.filter(session_id=emp_id_obj.emp_id)
            except EmployeeStaff.DoesNotExist:
                # If employee name doesn't match, no results for this filter
                queryset = queryset.none()
            except Exception as e:
                # Handle other potential errors during employee lookup
                print(f"Error looking up employee '{emp_name_filter}': {e}")
                queryset = queryset.none()

        # Efficiently fetch related employee names and financial years using bulk lookups
        all_session_ids = list(queryset.values_list('session_id', flat=True).distinct())
        employee_map = {
            emp.emp_id: f"{emp.title}. {emp.employee_name}"
            for emp in EmployeeStaff.objects.filter(emp_id__in=all_session_ids, comp_id=comp_id)
        }
        
        all_fin_year_ids = list(queryset.values_list('fin_year_id', flat=True).distinct())
        financial_year_map = {
            fy.fin_year_id: fy.fin_year
            for fy in FinancialYear.objects.filter(fin_year_id__in=all_fin_year_ids, comp_id=comp_id)
        }

        # Attach derived fields (GenBy and FinYear Name) and formatted dates to each SPR object
        for spr_obj in queryset:
            spr_obj.gen_by_name = employee_map.get(spr_obj.session_id, 'N/A')
            spr_obj.financial_year_name = financial_year_map.get(spr_obj.fin_year_id, 'N/A')
            spr_obj.display_sys_date = spr_obj.sys_date.strftime('%d/%m/%Y') if spr_obj.sys_date else ''
            spr_obj.display_checked_date = spr_obj.checked_date.strftime('%d/%m/%Y') if spr_obj.checked_date else ''
            spr_obj.display_approve_date = spr_obj.approve_date.strftime('%d/%m/%Y') if spr_obj.approve_date else ''
            spr_obj.display_authorize_date = spr_obj.authorize_date.strftime('%d/%m/%Y') if spr_obj.authorize_date else ''
            spr_obj.can_approve = not spr_obj.approve_status # Determine if checkbox should be visible

        return queryset


class SprMaster(models.Model):
    """
    Model representing the tblMM_SPR_Master table.
    Includes methods for business logic related to SPR approvals.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    spr_no = models.CharField(db_column='SPRNo', max_length=50)
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    session_id = models.CharField(db_column='SessionId', max_length=50) # EmpId from tblHR_OfficeStaff
    checked_status = models.BooleanField(db_column='Checked') # 1 for True, 0 for False
    checked_date = models.DateField(db_column='CheckedDate', null=True, blank=True)
    approve_status = models.BooleanField(db_column='Approve') # 1 for True, 0 for False
    approved_by = models.CharField(db_column='ApprovedBy', max_length=50, null=True, blank=True)
    approve_date = models.DateField(db_column='ApproveDate', null=True, blank=True)
    approve_time = models.TimeField(db_column='ApproveTime', null=True, blank=True)
    authorize_status = models.BooleanField(db_column='Authorize') # 1 for True, 0 for False
    authorize_date = models.DateField(db_column='AuthorizeDate', null=True, blank=True)
    fin_year_id = models.CharField(db_column='FinYearId', max_length=50) # FinYearId from tblFinancial_master
    comp_id = models.IntegerField(db_column='CompId')

    objects = SprMasterManager() # Assign the custom manager

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Master'
        verbose_name = 'SPR Master Record'
        verbose_name_plural = 'SPR Master Records'

    def __str__(self):
        return f"SPR No: {self.spr_no} (ID: {self.id})"

    def approve_spr(self, user_id):
        """
        Marks this SPR record as approved.
        """
        if not self.approve_status: # Only approve if not already approved
            self.approve_status = True
            self.approved_by = user_id
            self.approve_date = timezone.now().date()
            self.approve_time = timezone.now().time()
            self.save(update_fields=['approve_status', 'approved_by', 'approve_date', 'approve_time'])
            return True
        return False


class EmployeeStaffManager(models.Manager):
    """
    Custom manager for EmployeeStaff model to handle autocomplete queries.
    """
    def get_autocomplete_suggestions(self, prefix_text, comp_id, limit=10):
        """
        Returns employee names and IDs matching the prefix for autocomplete.
        """
        # Using istartswith for case-insensitive 'starts with' behavior
        employees = self.filter(
            employee_name__istartswith=prefix_text,
            comp_id=comp_id
        ).order_by('employee_name')[:limit]
        
        return [f"{emp.employee_name} [{emp.emp_id}]" for emp in employees]


class EmployeeStaff(models.Model):
    """
    Model representing the tblHR_OfficeStaff table.
    """
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    title = models.CharField(db_column='Title', max_length=50)
    comp_id = models.IntegerField(db_column='CompId')

    objects = EmployeeStaffManager() # Assign the custom manager

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee Staff'
        verbose_name_plural = 'Employee Staff'

    def __str__(self):
        return f"{self.title}. {self.employee_name} ({self.emp_id})"


class FinancialYear(models.Model):
    """
    Model representing the tblFinancial_master table.
    """
    fin_year_id = models.CharField(db_column='FinYearId', primary_key=True, max_length=50)
    fin_year = models.CharField(db_column='FinYear', max_length=50)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year
```

#### 4.2 Forms

**Task:** Define Django forms for user input.

**Explanation:**
Since the page mainly involves filtering and bulk approval, we'll use a standard Django `forms.Form` for the search criteria and handle the bulk approval directly in the view. No `ModelForm` is strictly necessary for this page's primary functionality.

**File: `material_management/forms.py`**

```python
from django import forms

class SprApproveSearchForm(forms.Form):
    """
    Form for search criteria on the SPR Approve page.
    The 'field_type' determines which text input ('employee_name' or 'spr_no') is used.
    """
    FIELD_CHOICES = [
        ('0', 'Employee Name'),
        ('1', 'SPR No'),
    ]
    field_type = forms.ChoiceField(
        choices=FIELD_CHOICES,
        initial='0', # Default to Employee Name
        widget=forms.Select(attrs={'class': 'box3 w-40 p-2 border border-gray-300 rounded-md shadow-sm',
                                   'hx-get': 'hx-request-to-swap-inputs', # HTMX for dynamic input swap
                                   'hx-target': '#search-input-container',
                                   'hx-swap': 'innerHTML',
                                   'hx-trigger': 'change'})
    )
    employee_name = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 w-96 p-2 border border-gray-300 rounded-md shadow-sm',
                                      'placeholder': 'Enter Employee Name',
                                      'hx-get': 'autocomplete_employee/', # HTMX for autocomplete
                                      'hx-trigger': 'keyup changed delay:300ms',
                                      'hx-target': '#autocomplete-results',
                                      'hx-swap': 'innerHTML',
                                      'hx-indicator': '.htmx-indicator'})
    )
    spr_no = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 w-96 p-2 border border-gray-300 rounded-md shadow-sm',
                                     'placeholder': 'Enter SPR No'})
    )

```

#### 4.3 Views

**Task:** Implement the page logic using Django Class-Based Views (CBVs), keeping them thin.

**Explanation:**
We use a `TemplateView` for the main page, and a `ListView` (or `View` rendering a partial) for the DataTables content loaded via HTMX. The autocomplete and bulk approval actions are handled by separate `View` classes. This ensures strict separation of concerns and keeps view methods concise (aiming for 5-15 lines).

**File: `material_management/views.py`**

```python
from django.views.generic import TemplateView, View, ListView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from django.db import transaction
from django.utils import timezone

from .models import SprMaster, EmployeeStaff
from .forms import SprApproveSearchForm

# Define the relative path to the view/print page for SPR details
# This should ideally be a Django URL pattern if it's also migrated
SPR_VIEW_PRINT_URL_TEMPLATE = "/module/materialmanagement/transactions/spr_view_print/{spr_id}/{spr_no}/"

class SprApproveListView(TemplateView):
    """
    Main view for the SPR Approval page.
    Renders the search form and a container for the SPR list.
    """
    template_name = 'material_management/spr_approve/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = SprApproveSearchForm(self.request.GET or None)
        return context

class SprApproveTablePartialView(ListView):
    """
    HTMX endpoint to render the SPR list table.
    Handles filtering logic based on GET parameters.
    """
    model = SprMaster
    template_name = 'material_management/spr_approve/_spr_table.html'
    context_object_name = 'spr_records'

    def get_queryset(self):
        # Retrieve session-specific parameters (CompId, FinYearId)
        comp_id = self.request.session.get('compid')
        fin_year_id = self.request.session.get('finyear')

        if not comp_id or not fin_year_id:
            # Handle cases where session data is missing (e.g., redirect to login)
            messages.error(self.request, "Session data for company or financial year is missing.")
            return SprMaster.objects.none()

        # Get search parameters from the request
        field_type = self.request.GET.get('field_type', '0') # Default to Employee Name
        employee_name = self.request.GET.get('employee_name')
        spr_no = self.request.GET.get('spr_no')

        # Pass filters to the custom manager method
        spr_no_filter = spr_no if field_type == '1' else None
        emp_name_filter = employee_name if field_type == '0' else None

        return SprMaster.objects.get_approvable_sprs(
            fin_year_id=fin_year_id,
            comp_id=comp_id,
            spr_no_filter=spr_no_filter,
            emp_name_filter=emp_name_filter
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Add the URL template for the view link, to be used in the template
        context['spr_view_print_url_template'] = SPR_VIEW_PRINT_URL_TEMPLATE
        return context


class SprApproveBulkApproveView(View):
    """
    HTMX endpoint to handle bulk approval of SPR records.
    """
    @transaction.atomic
    def post(self, request, *args, **kwargs):
        spr_ids_to_approve = request.POST.getlist('selected_spr_ids')

        if not spr_ids_to_approve:
            messages.warning(request, "No records selected for approval.")
            # HTMX response to trigger a client-side alert
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': '{"showAlert": "No records selected for approval."}'}
            )

        user_id = request.session.get('username')
        comp_id = request.session.get('compid')

        if not user_id or not comp_id:
            messages.error(request, "User session or company ID not found. Cannot process approval.")
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': '{"showAlert": "User session or company ID not found. Cannot process approval."}'}
            )

        approved_count = 0
        for spr_id in spr_ids_to_approve:
            try:
                spr_record = SprMaster.objects.get(id=spr_id, comp_id=comp_id, approve_status=False)
                if spr_record.approve_spr(user_id):
                    approved_count += 1
            except SprMaster.DoesNotExist:
                # Record not found or already approved, skip
                pass
            except Exception as e:
                # Log the error for specific record approval failure
                print(f"Error approving SPR ID {spr_id}: {e}")

        if approved_count > 0:
            messages.success(request, f"{approved_count} records approved successfully.")
        else:
            messages.info(request, "No new records were approved (they might have been already approved or not found).")

        # HTMX response to refresh the table and show messages
        return HttpResponse(
            status=204, # No content, just triggers a client-side action
            headers={
                'HX-Trigger': 'refreshSprApproveList' # Trigger the main list refresh on the frontend
            }
        )


class EmployeeAutoCompleteView(View):
    """
    HTMX endpoint for employee name autocomplete suggestions.
    Returns JSON response.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('query', '')
        comp_id = request.session.get('compid')

        if not comp_id:
            return JsonResponse([], safe=False) # Or handle error appropriately

        suggestions = EmployeeStaff.objects.get_autocomplete_suggestions(query, comp_id)
        
        # Return as a list of strings, which HTMX can parse directly into options or a list
        return JsonResponse(suggestions, safe=False)

class SearchInputPartialView(TemplateView):
    """
    HTMX endpoint to render the dynamic search input (Emp Name or SPR No).
    """
    template_name = 'material_management/spr_approve/_search_input_partial.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = SprApproveSearchForm(self.request.GET or None)
        context['field_type'] = self.request.GET.get('field_type', '0')
        return context

```

#### 4.4 Templates

**Task:** Create templates for each view, adhering to HTMX, Alpine.js, and DataTables requirements.

**Explanation:**
The main `list.html` orchestrates the page, using HTMX to dynamically load the `_spr_table.html` partial and for the `_search_input_partial.html` for dynamic input toggling. Alpine.js manages UI state like the autocomplete dropdown and the `confirmation` dialog. DataTables initializes the table.

**File: `material_management/templates/material_management/spr_approve/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{
    searchType: '{{ search_form.field_type.value }}',
    employeeName: '{{ search_form.employee_name.value|default:"" }}',
    sprNo: '{{ search_form.spr_no.value|default:"" }}',
    autocompleteResults: [],
    showAutocomplete: false,
    confirmAction: (message) => window.confirm(message)
}"
    x-init="
        $watch('searchType', value => {
            // Reset input values when search type changes
            employeeName = '';
            sprNo = '';
        });
        // Listen for custom event to show alert messages
        document.body.addEventListener('showAlert', (event) => {
            alert(event.detail); // Basic alert, can be replaced with a nicer Alpine.js modal
        });
">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">SPR Approval</h2>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-md mb-6">
        <form hx-get="{% url 'material_management:spr_approve_table' %}"
              hx-target="#sprApproveTable-container"
              hx-swap="innerHTML"
              hx-indicator="#loading-spinner">
            {% csrf_token %}
            <div class="flex items-center space-x-4 mb-4">
                <label for="id_field_type" class="text-gray-700 font-medium">Search By:</label>
                <select name="{{ search_form.field_type.name }}"
                        id="{{ search_form.field_type.id_for_label }}"
                        class="{{ search_form.field_type.widget.attrs.class }}"
                        x-model="searchType"
                        hx-get="{% url 'material_management:search_input_partial' %}"
                        hx-target="#search-input-container"
                        hx-swap="innerHTML"
                        hx-trigger="change">
                    {% for value, label in search_form.field_type.field.choices %}
                        <option value="{{ value }}" {% if value == search_form.field_type.value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
                
                <div id="search-input-container" class="relative flex-grow">
                    {% include 'material_management/spr_approve/_search_input_partial.html' with form=search_form field_type=search_form.field_type.value %}
                </div>

                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                    Search
                </button>
            </div>
            
            <div id="loading-spinner" class="htmx-indicator flex items-center text-gray-500">
                <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-500 mr-2"></div>
                Loading...
            </div>
        </form>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-md"
         hx-trigger="load, refreshSprApproveList from:body"
         hx-get="{% url 'material_management:spr_approve_table' %}"
         hx-target="#sprApproveTable-container"
         hx-swap="innerHTML"
         hx-indicator="#loading-spinner">
        <!-- DataTables table will be loaded here via HTMX -->
        <div class="text-center py-10" id="initial-loading">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading SPR records...</p>
        </div>
        <div id="sprApproveTable-container"></div>
    </div>

    <div class="mt-6 text-right">
        <form hx-post="{% url 'material_management:spr_approve_bulk_approve' %}"
              hx-target="#sprApproveTable-container"
              hx-swap="innerHTML"
              hx-confirm="Are you sure you want to approve the selected records?"
              hx-indicator="#loading-spinner">
            {% csrf_token %}
            <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                Approve Selected
            </button>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.tailwindcss.min.js"></script>
<link href="https://cdn.datatables.net/2.0.7/css/dataTables.tailwindcss.min.css" rel="stylesheet">
{% endblock %}
```

**File: `material_management/templates/material_management/spr_approve/_spr_table.html`**

```html
<div class="overflow-x-auto">
    <table id="sprApproveTable" class="min-w-full bg-white divide-y divide-gray-200 shadow-sm rounded-lg">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SPR No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Checked</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">For Approve</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Authorized</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% if spr_records %}
                {% for record in spr_records %}
                <tr>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ forloop.counter }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ record.spr_no }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ record.financial_year_name }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ record.display_sys_date }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ record.sys_time|time:"H:i" }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-left">{{ record.gen_by_name }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ record.display_checked_date }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-center">
                        {% if record.can_approve %}
                            <input type="checkbox" name="selected_spr_ids" value="{{ record.id }}" class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                        {% else %}
                            <span class="text-gray-500">{{ record.display_approve_date }}</span>
                        {% endif %}
                    </td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ record.display_authorize_date }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-center">
                        <a href="{{ spr_view_print_url_template|urlencode:record.id|urlencode:record.spr_no }}" 
                           class="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-1 px-3 rounded text-xs"
                           target="_blank">
                            View
                        </a>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
            <tr>
                <td colspan="10" class="py-4 text-center text-gray-500 text-lg">No data to display !</td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after the table has been loaded by HTMX
    $(document).ready(function() {
        $('#sprApproveTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [7, 9] } // Disable sorting for 'For Approve' and 'Actions' columns
            ]
        });
    });
</script>
```

**File: `material_management/templates/material_management/spr_approve/_search_input_partial.html`**

```html
{% comment %}
This partial is loaded via HTMX to dynamically swap search input fields.
It relies on the 'field_type' context variable.
{% endcomment %}
{% if field_type == '0' %}
    <div x-show="searchType === '0'" class="w-full relative">
        <input type="text"
               name="employee_name"
               id="{{ form.employee_name.id_for_label }}"
               class="{{ form.employee_name.widget.attrs.class }}"
               placeholder="{{ form.employee_name.widget.attrs.placeholder }}"
               value="{{ form.employee_name.value|default:'' }}"
               x-model="employeeName"
               hx-get="{% url 'material_management:autocomplete_employee' %}"
               hx-trigger="keyup changed delay:300ms, search"
               hx-target="#autocomplete-results"
               hx-swap="innerHTML"
               hx-indicator=".htmx-indicator"
               autocomplete="off"
               @focus="showAutocomplete = true"
               @blur.debounce.100ms="setTimeout(() => showAutocomplete = false, 100)"
               @input="if (employeeName.length < 1) autocompleteResults = []">
        
        <div x-show="showAutocomplete && autocompleteResults.length > 0"
             class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg w-full mt-1 max-h-60 overflow-y-auto">
            <ul id="autocomplete-results">
                {% comment %} HTMX will populate this {% endcomment %}
            </ul>
        </div>
    </div>
{% else %}
    <div x-show="searchType === '1'" class="w-full">
        <input type="text"
               name="spr_no"
               id="{{ form.spr_no.id_for_label }}"
               class="{{ form.spr_no.widget.attrs.class }}"
               placeholder="{{ form.spr_no.widget.attrs.placeholder }}"
               value="{{ form.spr_no.value|default:'' }}"
               x-model="sprNo">
    </div>
{% endif %}

{% comment %}
Example of HTMX populating autocomplete-results:
<li class="px-4 py-2 hover:bg-gray-100 cursor-pointer" 
    hx-on:click="employeeName = 'Employee Name [ID]'; showAutocomplete = false;">
    Employee Name [ID]
</li>
{% endcomment %}
```

**File: `material_management/templates/material_management/spr_approve/_autocomplete_results.html` (for HTMX response)**

```html
{% comment %} This partial is returned by the autocomplete view {% endcomment %}
{% if suggestions %}
    {% for suggestion in suggestions %}
        <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer text-gray-800"
            @click="employeeName = '{{ suggestion|cut:" ["|cut:"]" }}'; showAutocomplete = false;">
            {{ suggestion }}
        </li>
    {% endfor %}
{% else %}
    <li class="px-4 py-2 text-gray-500">No suggestions found.</li>
{% endif %}
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Explanation:**
This defines the paths to access the main approval page, the HTMX endpoints for the table, bulk approval, employee autocomplete, and the dynamic search input.

**File: `material_management/urls.py`**

```python
from django.urls import path
from .views import (
    SprApproveListView, 
    SprApproveTablePartialView, 
    SprApproveBulkApproveView,
    EmployeeAutoCompleteView,
    SearchInputPartialView
)

app_name = 'material_management'

urlpatterns = [
    # Main SPR Approve page
    path('spr_approve/', SprApproveListView.as_view(), name='spr_approve_list'),
    
    # HTMX endpoint for the main SPR table content (search results)
    path('spr_approve/table/', SprApproveTablePartialView.as_view(), name='spr_approve_table'),
    
    # HTMX endpoint for bulk approval action
    path('spr_approve/bulk_approve/', SprApproveBulkApproveView.as_view(), name='spr_approve_bulk_approve'),
    
    # HTMX endpoint for employee autocomplete suggestions
    path('spr_approve/autocomplete_employee/', EmployeeAutoCompleteView.as_view(), name='autocomplete_employee'),

    # HTMX endpoint to dynamically swap search input fields
    path('spr_approve/search_input_partial/', SearchInputPartialView.as_view(), name='search_input_partial'),
]

```

#### 4.6 Tests

**Task:** Write unit tests for the models and integration tests for the views, ensuring adequate coverage.

**Explanation:**
These tests cover model behavior, custom manager methods, and view interactions, including HTMX requests and session handling. We aim for high test coverage to ensure the stability and correctness of the migrated functionality.

**File: `material_management/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import date, time

from .models import SprMaster, EmployeeStaff, FinancialYear

class SprMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Setup common test data for all SprMaster tests
        cls.comp_id = 101
        cls.fin_year_id = '2023-24'
        
        # Create FinancialYear
        FinancialYear.objects.create(fin_year_id=cls.fin_year_id, fin_year='2023-2024', comp_id=cls.comp_id)
        
        # Create EmployeeStaff
        cls.emp1 = EmployeeStaff.objects.create(emp_id='E001', employee_name='John Doe', title='Mr', comp_id=cls.comp_id)
        cls.emp2 = EmployeeStaff.objects.create(emp_id='E002', employee_name='Jane Smith', title='Ms', comp_id=cls.comp_id)

        # Create SprMaster records
        cls.spr1 = SprMaster.objects.create(
            id=1, spr_no='SPR001', sys_date=date(2023, 1, 1), sys_time=time(10, 0, 0),
            session_id=cls.emp1.emp_id, checked_status=True, checked_date=date(2023, 1, 2),
            approve_status=False, authorize_status=False,
            fin_year_id=cls.fin_year_id, comp_id=cls.comp_id
        )
        cls.spr2 = SprMaster.objects.create(
            id=2, spr_no='SPR002', sys_date=date(2023, 1, 5), sys_time=time(11, 30, 0),
            session_id=cls.emp2.emp_id, checked_status=True, checked_date=date(2023, 1, 6),
            approve_status=True, approved_by='admin', approve_date=date(2023, 1, 7), approve_time=time(9,0,0),
            authorize_status=False,
            fin_year_id=cls.fin_year_id, comp_id=cls.comp_id
        )
        cls.spr3 = SprMaster.objects.create(
            id=3, spr_no='SPR003', sys_date=date(2023, 1, 10), sys_time=time(14, 0, 0),
            session_id=cls.emp1.emp_id, checked_status=True, checked_date=date(2023, 1, 11),
            approve_status=False, authorize_status=False,
            fin_year_id=cls.fin_year_id, comp_id=cls.comp_id
        )
        cls.spr4_unapproved = SprMaster.objects.create(
            id=4, spr_no='SPR004', sys_date=date(2023, 1, 15), sys_time=time(15, 0, 0),
            session_id=cls.emp1.emp_id, checked_status=False, # Not checked
            approve_status=False, authorize_status=False,
            fin_year_id=cls.fin_year_id, comp_id=cls.comp_id
        )


    def test_spr_master_creation(self):
        self.assertEqual(self.spr1.spr_no, 'SPR001')
        self.assertEqual(self.spr1.session_id, self.emp1.emp_id)
        self.assertFalse(self.spr1.approve_status)

    def test_approve_spr_method(self):
        user_id = 'testuser'
        initial_approve_status = self.spr1.approve_status
        approved = self.spr1.approve_spr(user_id)
        self.assertTrue(approved)
        self.spr1.refresh_from_db()
        self.assertTrue(self.spr1.approve_status)
        self.assertEqual(self.spr1.approved_by, user_id)
        self.assertEqual(self.spr1.approve_date, timezone.now().date())
        # Test approving an already approved SPR
        approved_again = self.spr2.approve_spr(user_id)
        self.assertFalse(approved_again) # Should return False as it was already approved

    def test_spr_master_manager_get_approvable_sprs_no_filter(self):
        sprs = SprMaster.objects.get_approvable_sprs(self.fin_year_id, self.comp_id)
        self.assertEqual(len(sprs), 2) # spr1, spr3 (spr2 is already approved, spr4 not checked)
        self.assertIn(self.spr1, sprs)
        self.assertIn(self.spr3, sprs)
        self.assertNotIn(self.spr2, sprs)
        self.assertNotIn(self.spr4_unapproved, sprs)
        # Check derived fields
        for spr_obj in sprs:
            self.assertIsNotNone(spr_obj.gen_by_name)
            self.assertIsNotNone(spr_obj.financial_year_name)
            self.assertTrue(hasattr(spr_obj, 'display_sys_date')) # Check for dynamic attributes

    def test_spr_master_manager_get_approvable_sprs_spr_no_filter(self):
        sprs = SprMaster.objects.get_approvable_sprs(self.fin_year_id, self.comp_id, spr_no_filter='SPR001')
        self.assertEqual(len(sprs), 1)
        self.assertEqual(sprs[0].spr_no, 'SPR001')

    def test_spr_master_manager_get_approvable_sprs_emp_name_filter(self):
        sprs = SprMaster.objects.get_approvable_sprs(self.fin_year_id, self.comp_id, emp_name_filter='John Doe')
        self.assertEqual(len(sprs), 2) # spr1, spr3
        self.assertIn(self.spr1, sprs)
        self.assertIn(self.spr3, sprs)

    def test_spr_master_manager_get_approvable_sprs_invalid_emp_name(self):
        sprs = SprMaster.objects.get_approvable_sprs(self.fin_year_id, self.comp_id, emp_name_filter='Non Existent Employee')
        self.assertEqual(len(sprs), 0)

class EmployeeStaffModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.comp_id = 101
        EmployeeStaff.objects.create(emp_id='E001', employee_name='Alice Smith', title='Dr', comp_id=cls.comp_id)
        EmployeeStaff.objects.create(emp_id='E002', employee_name='Bob Johnson', title='Mr', comp_id=cls.comp_id)
        EmployeeStaff.objects.create(emp_id='E003', employee_name='Alicia Keys', title='Ms', comp_id=cls.comp_id)
        EmployeeStaff.objects.create(emp_id='E004', employee_name='David Brown', title='Mr', comp_id=999) # Different comp_id

    def test_employee_staff_creation(self):
        emp = EmployeeStaff.objects.get(emp_id='E001')
        self.assertEqual(emp.employee_name, 'Alice Smith')

    def test_get_autocomplete_suggestions(self):
        suggestions = EmployeeStaff.objects.get_autocomplete_suggestions('ali', self.comp_id)
        self.assertEqual(len(suggestions), 2)
        self.assertIn('Alice Smith [E001]', suggestions)
        self.assertIn('Alicia Keys [E003]', suggestions)
        # Test case insensitivity
        suggestions = EmployeeStaff.objects.get_autocomplete_suggestions('ALi', self.comp_id)
        self.assertEqual(len(suggestions), 2)

        suggestions = EmployeeStaff.objects.get_autocomplete_suggestions('bob', self.comp_id)
        self.assertEqual(len(suggestions), 1)
        self.assertIn('Bob Johnson [E002]', suggestions)

        suggestions = EmployeeStaff.objects.get_autocomplete_suggestions('nonexistent', self.comp_id)
        self.assertEqual(len(suggestions), 0)

        suggestions = EmployeeStaff.objects.get_autocomplete_suggestions('Alice', 999) # Wrong comp_id
        self.assertEqual(len(suggestions), 0)

class SprApproveViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.comp_id = 101
        cls.fin_year_id = '2023-24'
        FinancialYear.objects.create(fin_year_id=cls.fin_year_id, fin_year='2023-2024', comp_id=cls.comp_id)
        cls.emp1 = EmployeeStaff.objects.create(emp_id='E001', employee_name='John Doe', title='Mr', comp_id=cls.comp_id)
        cls.emp2 = EmployeeStaff.objects.create(emp_id='E002', employee_name='Jane Smith', title='Ms', comp_id=cls.comp_id)
        cls.spr_approvable_1 = SprMaster.objects.create(
            id=1, spr_no='SPR100', sys_date=date(2023, 3, 1), sys_time=time(10, 0, 0),
            session_id=cls.emp1.emp_id, checked_status=True, checked_date=date(2023, 3, 2),
            approve_status=False, authorize_status=False,
            fin_year_id=cls.fin_year_id, comp_id=cls.comp_id
        )
        cls.spr_approvable_2 = SprMaster.objects.create(
            id=2, spr_no='SPR101', sys_date=date(2023, 3, 5), sys_time=time(11, 0, 0),
            session_id=cls.emp2.emp_id, checked_status=True, checked_date=date(2023, 3, 6),
            approve_status=False, authorize_status=False,
            fin_year_id=cls.fin_year_id, comp_id=cls.comp_id
        )
        cls.spr_already_approved = SprMaster.objects.create(
            id=3, spr_no='SPR102', sys_date=date(2023, 3, 10), sys_time=time(12, 0, 0),
            session_id=cls.emp1.emp_id, checked_status=True, checked_date=date(2023, 3, 11),
            approve_status=True, approved_by='old_approver', approve_date=date(2023, 3, 12), approve_time=time(9,0,0),
            authorize_status=False,
            fin_year_id=cls.fin_year_id, comp_id=cls.comp_id
        )

    def setUp(self):
        self.client = Client()
        session = self.client.session
        session['compid'] = self.comp_id
        session['finyear'] = self.fin_year_id
        session['username'] = 'testuser' # User for approval
        session.save()

    def test_spr_approve_list_view_get(self):
        response = self.client.get(reverse('material_management:spr_approve_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spr_approve/list.html')
        self.assertIn('search_form', response.context)

    def test_spr_approve_table_partial_view_get(self):
        response = self.client.get(reverse('material_management:spr_approve_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spr_approve/_spr_table.html')
        self.assertIn('spr_records', response.context)
        self.assertEqual(len(response.context['spr_records']), 2) # Should get spr_approvable_1, spr_approvable_2

        # Test filter by SPR No
        response = self.client.get(reverse('material_management:spr_approve_table'), {'field_type': '1', 'spr_no': 'SPR100'})
        self.assertEqual(len(response.context['spr_records']), 1)
        self.assertEqual(response.context['spr_records'][0].spr_no, 'SPR100')

        # Test filter by Employee Name
        response = self.client.get(reverse('material_management:spr_approve_table'), {'field_type': '0', 'employee_name': 'John Doe'})
        self.assertEqual(len(response.context['spr_records']), 1) # Only SPR100 from John Doe should be approvable
        self.assertEqual(response.context['spr_records'][0].spr_no, 'SPR100')
    
    def test_spr_approve_table_partial_view_get_missing_session(self):
        session = self.client.session
        del session['compid']
        session.save()
        response = self.client.get(reverse('material_management:spr_approve_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spr_approve/_spr_table.html')
        self.assertEqual(len(response.context['spr_records']), 0) # No records if session data is missing

    def test_spr_approve_bulk_approve_view_post_success(self):
        initial_unapproved_count = SprMaster.objects.filter(approve_status=False, comp_id=self.comp_id).count()
        self.assertEqual(initial_unapproved_count, 2) # spr_approvable_1, spr_approvable_2

        response = self.client.post(
            reverse('material_management:spr_approve_bulk_approve'),
            {'selected_spr_ids': [self.spr_approvable_1.id, self.spr_approvable_2.id]},
            HTTP_HX_REQUEST='true' # Simulate HTMX request
        )
        self.assertEqual(response.status_code, 204) # HTMX No Content response
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSprApproveList')

        # Verify records are updated in DB
        self.spr_approvable_1.refresh_from_db()
        self.spr_approvable_2.refresh_from_db()
        self.assertTrue(self.spr_approvable_1.approve_status)
        self.assertTrue(self.spr_approvable_2.approve_status)
        self.assertEqual(self.spr_approvable_1.approved_by, 'testuser')

        final_unapproved_count = SprMaster.objects.filter(approve_status=False, comp_id=self.comp_id).count()
        self.assertEqual(final_unapproved_count, 0)

    def test_spr_approve_bulk_approve_view_post_no_selection(self):
        response = self.client.post(
            reverse('material_management:spr_approve_bulk_approve'),
            {}, # No selected_spr_ids
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('showAlert', response.headers['HX-Trigger']) # Should trigger alert

    def test_spr_approve_bulk_approve_view_post_already_approved(self):
        # Try to approve a record that is already approved
        response = self.client.post(
            reverse('material_management:spr_approve_bulk_approve'),
            {'selected_spr_ids': [self.spr_already_approved.id]},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSprApproveList') # Should still refresh

        self.spr_already_approved.refresh_from_db()
        self.assertTrue(self.spr_already_approved.approve_status) # Still approved, no change to approved_by

    def test_employee_autocomplete_view(self):
        response = self.client.get(
            reverse('material_management:autocomplete_employee'),
            {'query': 'john'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        suggestions = response.json()
        self.assertEqual(len(suggestions), 1)
        self.assertEqual(suggestions[0], 'John Doe [E001]')

        response = self.client.get(
            reverse('material_management:autocomplete_employee'),
            {'query': 'NonExistent'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        suggestions = response.json()
        self.assertEqual(len(suggestions), 0)

    def test_search_input_partial_view(self):
        # Test default (employee name)
        response = self.client.get(
            reverse('material_management:search_input_partial'),
            {'field_type': '0'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spr_approve/_search_input_partial.html')
        self.assertContains(response, 'name="employee_name"')
        self.assertNotContains(response, 'name="spr_no"')

        # Test SPR No
        response = self.client.get(
            reverse('material_management:search_input_partial'),
            {'field_type': '1'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spr_approve/_search_input_partial.html')
        self.assertContains(response, 'name="spr_no"')
        self.assertNotContains(response, 'name="employee_name"')

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

The provided templates and views already integrate HTMX and Alpine.js as follows:

*   **HTMX for Dynamic Content Loading:**
    *   The main `list.html` uses `hx-get` to load the `_spr_table.html` partial on initial page load and whenever the `refreshSprApproveList` custom event is triggered (e.g., after a search or an approval action).
    *   The search form uses `hx-get` to submit search queries and `hx-target` to update the `sprApproveTable-container` div with the new table content.
    *   The `field_type` dropdown uses `hx-get` to dynamically swap the search input field (`_search_input_partial.html`) based on selection.
    *   The "Approve Selected" button uses `hx-post` to submit the bulk approval request, targeting the table container for refresh and showing a confirmation dialog via `hx-confirm`.
    *   The employee name input utilizes `hx-get` for sending autocomplete queries and `hx-target` to update the `#autocomplete-results` list.
    *   `HX-Trigger` headers are used in views to trigger `refreshSprApproveList` event on the client after successful operations, ensuring the table automatically updates without a full page refresh. `showAlert` triggers a basic alert.

*   **Alpine.js for UI State Management:**
    *   `x-data` on the main container manages UI state variables like `searchType`, `employeeName`, `sprNo`, `autocompleteResults`, and `showAutocomplete`.
    *   `x-model` binds input fields and the dropdown to Alpine.js data properties.
    *   `x-show` dynamically shows/hides the search input fields and the autocomplete results dropdown.
    *   Event listeners (`@focus`, `@blur`, `@input`, `@click`) manage the visibility and content of the autocomplete dropdown.
    *   A basic `confirmAction` function demonstrates how client-side confirmations could be handled with Alpine.js (though `hx-confirm` is used directly here).
    *   A basic `showAlert` event listener is set up to display messages triggered by HTMX `HX-Trigger` headers.

*   **DataTables for List Views:**
    *   The `_spr_table.html` partial includes a `<script>` block that initializes DataTables on the `sprApproveTable` once the table is loaded into the DOM by HTMX.
    *   It configures pagination (`pageLength`, `lengthMenu`) and disables sorting for action columns.
    *   Necessary DataTables CDN links (jQuery, DataTables JS, Tailwind CSS integration) are included in the `extra_js` block of the `list.html` template.

*   **No Custom JavaScript (beyond HTMX/Alpine/DataTables ecosystem):**
    *   All dynamic interactions are handled by HTMX, Alpine.js, or DataTables, adhering to the principle of minimizing custom JavaScript development.

### Final Notes

*   **Placeholders:** Replace `[MODEL_NAME_LOWER]_list` with the actual Django URL names (e.g., `material_management:spr_approve_list`). The provided code uses these explicitly.
*   **DRY Templates:** `_spr_table.html`, `_search_input_partial.html`, and `_autocomplete_results.html` are partial templates to keep the main `list.html` clean and to enable HTMX to swap smaller parts of the page.
*   **Business Logic in Models:** The `SprMasterManager` and `SprMaster.approve_spr` method encapsulate the complex data retrieval and update logic, keeping views thin and focused on orchestrating requests and responses.
*   **Comprehensive Tests:** The provided `tests.py` ensures the core functionality of models and views is robust and prevents regressions during future development.
*   **Session Management:** Ensure Django's session middleware is correctly configured in your `settings.py` to handle `request.session` variables that mimic the ASP.NET `Session` state (`compid`, `finyear`, `username`).
*   **SPR View/Print Page:** The `SPR_VIEW_PRINT_URL_TEMPLATE` is a placeholder. If `SPR_View_Print.aspx` is also migrated, its Django URL should be used here. The current implementation uses simple string formatting.