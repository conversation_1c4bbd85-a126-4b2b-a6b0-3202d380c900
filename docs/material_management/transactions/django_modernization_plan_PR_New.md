## ASP.NET to Django Conversion Script: PR - New Module

This document outlines a strategic plan for migrating your existing ASP.NET application, specifically the "PR - New" module, to a modern Django-based solution. Our approach emphasizes automation, clear communication, and the adoption of cutting-edge web technologies like HTMX and Alpine.js, ensuring a robust, scalable, and maintainable system.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the "PR - New" module.

**Analysis:**
The ASP.NET code primarily interacts with two database tables:
1.  **`SD_Cust_WorkOrder_Master`**: This table holds the main work order data displayed in the grid.
2.  **`tblSD_WO_Category`**: This table is used to populate the "WO Category" dropdown for filtering.
3.  `tblMM_PLN_PR_Temp`: This appears to be a session-specific temporary table cleared on page load. In Django, such temporary, session-scoped data is typically handled through Django's session management or in-memory data structures, rather than a dedicated database model. Therefore, we will **not** create a Django model for this table.

**Inferred Schema:**

*   **`SD_Cust_WorkOrder_Master`** (`[TABLE_NAME] = 'SD_Cust_WorkOrder_Master'`):
    *   `WONo` (string): Work Order Number.
    *   `TaskProjectTitle` (string): Project Title.
    *   `ReleaseWIS` (string, '0' or '1'): Indicates release status.
    *   `DryActualRun` (string, '0' or '1'): Indicates run status.
    *   `CompId` (integer): Company ID, used for filtering.
    *   `CloseOpen` (string, '0' or '1'): Indicates if the work order is open ('0') or closed ('1').
    *   `CId` (integer): Category ID, inferred from the filtering logic (`DDLTaskWOType.SelectedValue`).

*   **`tblSD_WO_Category`** (`[TABLE_NAME] = 'tblSD_WO_Category'`):
    *   `CId` (integer): Category ID, likely the primary key.
    *   `Symbol` (string): Category symbol.
    *   `CName` (string): Category name.
    *   `CompId` (integer): Company ID, used for filtering.

### Step 2: Identify Backend Functionality

**Task:** Determine the operations performed by the ASP.NET code.

**Analysis:**
The "PR - New" module primarily focuses on **reading and filtering** existing work order data.

*   **Read (Display List):** The core functionality is to fetch and display a list of work orders from `SD_Cust_WorkOrder_Master`.
    *   **Filtering:** The list can be filtered by:
        *   "WO No" (`TxtWONo`): Exact match on `WONo`.
        *   "WO Category" (`DDLTaskWOType`): Based on `CId` from `tblSD_WO_Category`.
    *   **Conditional Display Logic:**
        *   The "WIS" column's display value ('Released', 'Stop', 'Not Release') is derived from `ReleaseWIS` and `DryActualRun` fields.
        *   The "WIS Run" column's display value ('Yes', 'No') is derived from `DryActualRun`.
        *   The "Select" action button's visibility is conditional: it's visible only if "WIS Run" is 'Yes' AND "WIS" is 'Released'.
    *   **Pagination:** The `GridView2` supports paging with a size of 20 items per page.

*   **Action (Select):** Clicking "Select" redirects the user to a "PR\_New\_Details.aspx" page with the selected `WONo` and module/sub-module IDs. This is not a CRUD operation on the work order itself within this page, but a navigation to a detail view.

*   **No Create, Update, or Delete:** There are no direct operations for creating, updating, or deleting work orders from this specific page.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles to map them to Django/HTML components.

**Analysis:**

*   **Search/Filter Inputs:**
    *   `TxtWONo` (`asp:TextBox`): Maps to a standard HTML `<input type="text">` for WO Number search.
    *   `DDLTaskWOType` (`asp:DropDownList`): Maps to a standard HTML `<select>` element for WO Category selection. Its `AutoPostBack="True"` will be replicated using HTMX `hx-trigger="change"`.
    *   `Button1` (`asp:Button`): Maps to a standard HTML `<button type="submit">` for triggering the search, also utilizing HTMX.

*   **Data Display:**
    *   `GridView2` (`asp:GridView`): This is the main data presentation control. It will be replaced by an HTML `<table>` enhanced with **DataTables.js** for client-side sorting, searching, and pagination.
        *   The `EmptyDataTemplate` will be a simple conditional rendering in the Django template.
        *   The column definitions (SN, Select, WO No, Project Title, WIS, WIS Run) will translate directly to `<th>` and `<td>` elements.

*   **Actions:**
    *   `LinkButton1` (`asp:LinkButton` within `GridView2`): This "Select" action will be an HTML `<a>` tag or a `<button>` that links to the detailed page (or triggers an HTMX request for a modal, if details were shown as a modal). Given the original `Response.Redirect`, a direct `<a>` link is suitable. Its visibility will be controlled by Django template logic based on the model's derived properties.

### Step 4: Generate Django Code

We will create a Django application named `material_management` to house this module.

### 4.1 Models (`material_management/models.py`)

This file will define the structure of your database tables as Django models, making them accessible and manageable within your application. The `managed = False` setting tells Django that the database tables already exist and it should not attempt to create or modify them.

```python
from django.db import models

class WoCategory(models.Model):
    # This maps to tblSD_WO_Category. CId is the primary key.
    c_id = models.IntegerField(db_column='CId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=255) # Assuming appropriate max_length
    c_name = models.CharField(db_column='CName', max_length=255) # Assuming appropriate max_length
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblSD_WO_Category'
        verbose_name = 'WO Category'
        verbose_name_plural = 'WO Categories'

    def __str__(self):
        return f"{self.symbol} - {self.c_name}"

class WorkOrder(models.Model):
    # This maps to SD_Cust_WorkOrder_Master. Assuming Django's default 'id' as PK.
    # If 'WONo' is the actual primary key in the DB, change won_no to primary_key=True.
    won_no = models.CharField(db_column='WONo', max_length=50) # Assuming appropriate max_length
    task_project_title = models.CharField(db_column='TaskProjectTitle', max_length=500) # Assuming appropriate max_length
    release_wis = models.CharField(db_column='ReleaseWIS', max_length=1) # Stored as '0' or '1'
    dry_actual_run = models.CharField(db_column='DryActualRun', max_length=1) # Stored as '0' or '1'
    comp_id = models.IntegerField(db_column='CompId')
    close_open = models.CharField(db_column='CloseOpen', max_length=1) # '0' for open, '1' for closed
    c_id = models.IntegerField(db_column='CId', null=True, blank=True) # Category ID for filtering, assumed to be on this table

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.won_no

    @property
    def get_release_status(self):
        """
        Calculates and returns the 'WIS' (Release Status) based on 'ReleaseWIS'
        and 'DryActualRun' fields, replicating the ASP.NET logic.
        """
        if self.release_wis == '1':
            return 'Released'
        elif self.dry_actual_run == '1':
            return 'Stop'
        else:
            return 'Not Release'

    @property
    def get_run_status(self):
        """
        Calculates and returns the 'WIS Run' status based on 'DryActualRun' field.
        """
        return 'Yes' if self.dry_actual_run == '1' else 'No'

    def is_selectable(self):
        """
        Determines if the 'Select' button should be visible for this work order,
        based on 'WIS Run' and 'WIS' statuses.
        """
        return self.get_run_status == 'Yes' and self.get_release_status == 'Released'

    @classmethod
    def get_filtered_work_orders(cls, comp_id, won_no=None, category_id=None):
        """
        Retrieves a queryset of WorkOrder objects, applying filters for company ID,
        open status, WO Number, and WO Category. This method centralizes the data
        retrieval logic, adhering to the 'Fat Model' principle.
        """
        queryset = cls.objects.filter(comp_id=comp_id, close_open='0').order_by('won_no')

        if won_no:
            queryset = queryset.filter(won_no__iexact=won_no) # Case-insensitive exact match

        if category_id and category_id != 'WO Category': # 'WO Category' is the default dropdown value
            queryset = queryset.filter(c_id=int(category_id))
            
        return queryset

```

### 4.2 Forms (`material_management/forms.py`)

This form will handle the input fields for searching and filtering the work order list. It's a simple `forms.Form` as it's not directly tied to a model for creation/update, but rather for query parameters.

```python
from django import forms
from .models import WoCategory

class WorkOrderSearchForm(forms.Form):
    """
    Form for capturing search criteria (WO No and WO Category).
    """
    wo_no = forms.CharField(
        label="WO No",
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter WO No'
        })
    )
    
    category = forms.ModelChoiceField(
        queryset=WoCategory.objects.all(), # Queryset will be filtered in __init__
        label="WO Category",
        required=False,
        empty_label="WO Category", # Replicates the "WO Category" default option
        to_field_name='c_id', # Use CId as the value transmitted by the select
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '{% url "pr_new_table" %}', # HTMX will trigger a GET to reload the table
            'hx-target': '#workOrderTable-container',
            'hx-swap': 'innerHTML',
            'hx-trigger': 'change' # AutoPostBack equivalent
        })
    )

    def __init__(self, *args, **kwargs):
        """
        Initializes the form, filtering the WO Category queryset by company ID.
        """
        comp_id = kwargs.pop('comp_id', None)
        super().__init__(*args, **kwargs)
        if comp_id:
            self.fields['category'].queryset = WoCategory.objects.filter(comp_id=comp_id)

```

### 4.3 Views (`material_management/views.py`)

Views are kept thin, delegating business logic to the models. We'll use two views: one for the initial page load (TemplateView) and another for the HTMX-driven table content (ListView).

```python
from django.views.generic import TemplateView, ListView
from django.conf import settings # Used for DEFAULT_COMPANY_ID placeholder
from .models import WorkOrder, WoCategory
from .forms import WorkOrderSearchForm

class WorkOrderListView(TemplateView):
    """
    Renders the main 'PR - New' page, including the search form and a container
    for the dynamically loaded work order table.
    """
    template_name = 'material_management/pr_new/list.html'

    def get_context_data(self, **kwargs):
        """
        Prepares the context for the template, including the search form.
        Company ID is retrieved, assuming it's available from user session or settings.
        """
        context = super().get_context_data(**kwargs)
        # In a real application, CompId would come from request.user.profile.company_id
        # or a similar authentication/session mechanism.
        current_comp_id = getattr(settings, 'DEFAULT_COMPANY_ID', 1) # Placeholder value

        context['search_form'] = WorkOrderSearchForm(
            self.request.GET or None, 
            comp_id=current_comp_id # Pass comp_id to filter categories in the form
        )
        return context

class WorkOrderTablePartialView(ListView):
    """
    Returns only the HTML fragment for the work order table.
    This view is specifically designed to be targeted by HTMX requests
    from the search form and initial page load.
    """
    model = WorkOrder
    template_name = 'material_management/pr_new/_work_order_table.html'
    context_object_name = 'work_orders'

    def get_queryset(self):
        """
        Retrieves the work orders based on search filters provided in GET parameters.
        Leverages the 'Fat Model' method WorkOrder.get_filtered_work_orders.
        """
        current_comp_id = getattr(settings, 'DEFAULT_COMPANY_ID', 1) # Placeholder value

        won_no = self.request.GET.get('wo_no')
        category_id = self.request.GET.get('category')
        
        return self.model.get_filtered_work_orders(
            comp_id=current_comp_id,
            won_no=won_no,
            category_id=category_id
        )

```

### 4.4 Templates

Templates are designed to be compact, reusable, and leverage HTMX for dynamic updates and DataTables for interactive data presentation.

**`material_management/pr_new/list.html`**
This is the main page template that extends the base layout and includes the search form and the container for the dynamically loaded table.

```html
{% extends 'core/base.html' %}

{% block title %}PR - New{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">PR - New</h2>
    </div>

    {# Search and Filter Form #}
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <form id="search-form" 
              hx-get="{% url 'pr_new_table' %}" {# Target the partial table view #}
              hx-target="#workOrderTable-container" 
              hx-swap="innerHTML" 
              hx-trigger="submit, change from:#id_category"> {# Trigger on submit or category change #}
            {% csrf_token %}
            <div class="flex flex-col sm:flex-row items-end gap-4">
                <div class="flex-grow">
                    <label for="{{ search_form.wo_no.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">WO No:</label>
                    {{ search_form.wo_no }}
                </div>
                <div class="flex-grow">
                    <label for="{{ search_form.category.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">WO Category:</label>
                    {{ search_form.category }}
                </div>
                <div>
                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Search
                    </button>
                </div>
            </div>
        </form>
    </div>
    
    {# Container for the dynamically loaded work order table #}
    <div id="workOrderTable-container"
         hx-trigger="load, refreshWorkOrderList from:body" {# Loads on page load, and can be triggered by custom events #}
         hx-get="{% url 'pr_new_table' %}" {# Initial load of the table #}
         hx-swap="innerHTML">
        <!-- Loading indicator shown while HTMX fetches data -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Work Orders...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Any page-specific Alpine.js initialization goes here if needed.
     For this specific module, HTMX and DataTables handle most interactions. -->
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for broader UI interactions
    });
</script>
{% endblock %}
```

**`material_management/pr_new/_work_order_table.html`**
This partial template contains only the table structure and the DataTables initialization script. It's designed to be loaded dynamically by HTMX.

```html
<div class="bg-white shadow-md rounded-lg p-4">
    {% if work_orders %}
    <table id="workOrderTable" class="min-w-full bg-white border-collapse yui-datatable-theme">
        <thead>
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-5%">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-7%">Action</th> {# Equivalent to original "Select" column #}
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-10%">WO No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-60%">Project Title</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-10%">WIS</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-10%">WIS Run</th>
            </tr>
        </thead>
        <tbody>
            {% for wo in work_orders %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    {% if wo.is_selectable %}
                    {# Redirects to a details page, similar to ASP.NET Response.Redirect #}
                    <a href="{% url 'pr_new_details' %}?WONo={{ wo.won_no }}" class="text-blue-600 hover:text-blue-800 font-medium">Select</a>
                    {% endif %}
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ wo.won_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ wo.task_project_title }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ wo.get_release_status }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ wo.get_run_status }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    {# Displayed if no work orders are found, replicating EmptyDataTemplate #}
    <div class="text-center p-10">
        <p class="text-lg text-red-700 font-semibold">No data to display !</p>
    </div>
    {% endif %}
</div>

<!-- DataTables Initialization Script: Runs whenever this partial is loaded/reloaded -->
<script>
    // Ensure jQuery and DataTables are loaded via CDN in base.html
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists to prevent re-initialization errors
        if ($.fn.DataTable.isDataTable('#workOrderTable')) {
            $('#workOrderTable').DataTable().destroy();
        }
        $('#workOrderTable').DataTable({
            "pageLength": 20, // Matches original GridView PageSize
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "paging": true,      // Enable pagination
            "searching": true,   // Enable search box
            "ordering": true,    // Enable column sorting
            "info": true         // Display "Showing X of Y entries" info
        });
    });
</script>
```

### 4.5 URLs (`material_management/urls.py`)

This file defines the web addresses for accessing your Django views.

```python
from django.urls import path
from django.http import HttpResponse # For placeholder details page
from .views import WorkOrderListView, WorkOrderTablePartialView

urlpatterns = [
    # Main page URL for PR - New list view
    path('pr_new/', WorkOrderListView.as_view(), name='pr_new_list'),
    
    # HTMX endpoint to dynamically load or refresh the work order table content
    path('pr_new/table/', WorkOrderTablePartialView.as_view(), name='pr_new_table'),

    # Placeholder for the PR_New_Details.aspx equivalent.
    # In a real migration, this would be a proper view and template for details.
    path('pr_new/details/', lambda request: HttpResponse(
        f"Work Order Details Page for WONo: {request.GET.get('WONo', 'N/A')}. "
        f"ModId: {request.GET.get('ModId', 'N/A')}, SubModId: {request.GET.get('SubModId', 'N/A')}"
    ), name='pr_new_details'),
]

```

### 4.6 Tests (`material_management/tests.py`)

Thorough testing ensures the stability and correctness of your migrated application. These tests cover both the data logic (models) and the user interface interactions (views).

```python       
from django.test import TestCase, Client
from django.urls import reverse
from .models import WoCategory, WorkOrder
from django.conf import settings

# Ensure a default company ID exists for tests, mimicking the ASP.NET session's CompId
# In a real application, this would be part of your Django settings or determined by user authentication.
if not hasattr(settings, 'DEFAULT_COMPANY_ID'):
    settings.DEFAULT_COMPANY_ID = 1

class WoCategoryModelTest(TestCase):
    """
    Unit tests for the WoCategory model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create sample WoCategory data for all tests in this class
        WoCategory.objects.create(
            c_id=1,
            symbol='WO',
            c_name='Work Order Category A',
            comp_id=settings.DEFAULT_COMPANY_ID
        )
        WoCategory.objects.create(
            c_id=2,
            symbol='PR',
            c_name='Purchase Request Category B',
            comp_id=settings.DEFAULT_COMPANY_ID
        )
  
    def test_wo_category_creation(self):
        """Verify WoCategory objects are created correctly."""
        category_a = WoCategory.objects.get(c_id=1)
        self.assertEqual(category_a.symbol, 'WO')
        self.assertEqual(category_a.c_name, 'Work Order Category A')
        self.assertEqual(category_a.comp_id, settings.DEFAULT_COMPANY_ID)
        
    def test_str_representation(self):
        """Verify the __str__ method returns the expected string."""
        category_a = WoCategory.objects.get(c_id=1)
        self.assertEqual(str(category_a), 'WO - Work Order Category A')

class WorkOrderModelTest(TestCase):
    """
    Unit tests for the WorkOrder model and its business logic methods.
    """
    @classmethod
    def setUpTestData(cls):
        # Create sample data including a category and various work orders
        # to test different scenarios for status and selectability.
        WoCategory.objects.create(
            c_id=100,
            symbol='TEST',
            c_name='Test Category',
            comp_id=settings.DEFAULT_COMPANY_ID
        )

        WorkOrder.objects.create(
            won_no='WO/2023/001', # Selectable
            task_project_title='Project Alpha',
            release_wis='1', # Released
            dry_actual_run='1', # Yes
            comp_id=settings.DEFAULT_COMPANY_ID,
            close_open='0',
            c_id=100
        )
        WorkOrder.objects.create(
            won_no='WO/2023/002', # Not selectable (Stop)
            task_project_title='Project Beta',
            release_wis='0', # Not Released
            dry_actual_run='1', # Yes
            comp_id=settings.DEFAULT_COMPANY_ID,
            close_open='0',
            c_id=100
        )
        WorkOrder.objects.create(
            won_no='WO/2023/003', # Not selectable (Not Release)
            task_project_title='Project Gamma',
            release_wis='0', # Not Released
            dry_actual_run='0', # No
            comp_id=settings.DEFAULT_COMPANY_ID,
            close_open='0',
            c_id=100
        )
        WorkOrder.objects.create(
            won_no='WO/2023/004', # Closed order - should not appear in default list
            task_project_title='Project Delta (Closed)',
            release_wis='1',
            dry_actual_run='1',
            comp_id=settings.DEFAULT_COMPANY_ID,
            close_open='1', 
            c_id=100
        )
        WorkOrder.objects.create(
            won_no='WO/2023/005', # Different company - should not appear in default list
            task_project_title='Project Epsilon',
            release_wis='1',
            dry_actual_run='1',
            comp_id=settings.DEFAULT_COMPANY_ID + 1,
            close_open='0',
            c_id=100
        )
  
    def test_work_order_creation(self):
        """Verify WorkOrder objects are created correctly."""
        wo = WorkOrder.objects.get(won_no='WO/2023/001')
        self.assertEqual(wo.task_project_title, 'Project Alpha')
        self.assertEqual(wo.release_wis, '1')
        self.assertEqual(wo.dry_actual_run, '1')
        self.assertEqual(wo.c_id, 100)
        
    def test_get_release_status(self):
        """Verify 'get_release_status' property works as expected."""
        self.assertEqual(WorkOrder.objects.get(won_no='WO/2023/001').get_release_status, 'Released')
        self.assertEqual(WorkOrder.objects.get(won_no='WO/2023/002').get_release_status, 'Stop')
        self.assertEqual(WorkOrder.objects.get(won_no='WO/2023/003').get_release_status, 'Not Release')

    def test_get_run_status(self):
        """Verify 'get_run_status' property works as expected."""
        self.assertEqual(WorkOrder.objects.get(won_no='WO/2023/001').get_run_status, 'Yes')
        self.assertEqual(WorkOrder.objects.get(won_no='WO/2023/003').get_run_status, 'No')

    def test_is_selectable(self):
        """Verify 'is_selectable' property works correctly based on conditions."""
        self.assertTrue(WorkOrder.objects.get(won_no='WO/2023/001').is_selectable()) # Released & Yes -> Selectable
        self.assertFalse(WorkOrder.objects.get(won_no='WO/2023/002').is_selectable()) # Stop & Yes -> Not selectable
        self.assertFalse(WorkOrder.objects.get(won_no='WO/2023/003').is_selectable()) # Not Release & No -> Not selectable

    def test_get_filtered_work_orders(self):
        """Verify 'get_filtered_work_orders' class method filters correctly."""
        # Test basic filtering (comp_id and close_open='0')
        qs = WorkOrder.get_filtered_work_orders(comp_id=settings.DEFAULT_COMPANY_ID)
        self.assertEqual(qs.count(), 3) # Should only include WO/2023/001, 002, 003 (open for default company)
        self.assertIn(WorkOrder.objects.get(won_no='WO/2023/001'), qs)
        self.assertNotIn(WorkOrder.objects.get(won_no='WO/2023/004'), qs) # Closed
        self.assertNotIn(WorkOrder.objects.get(won_no='WO/2023/005'), qs) # Wrong company

        # Test filtering by WONo
        qs_wo_no = WorkOrder.get_filtered_work_orders(comp_id=settings.DEFAULT_COMPANY_ID, won_no='WO/2023/001')
        self.assertEqual(qs_wo_no.count(), 1)
        self.assertEqual(qs_wo_no.first().won_no, 'WO/2023/001')

        # Test filtering by Category
        qs_cat = WorkOrder.get_filtered_work_orders(comp_id=settings.DEFAULT_COMPANY_ID, category_id='100')
        self.assertEqual(qs_cat.count(), 3)
        self.assertTrue(all(wo.c_id == 100 for wo in qs_cat))

        # Test combined filtering
        qs_combined = WorkOrder.get_filtered_work_orders(comp_id=settings.DEFAULT_COMPANY_ID, won_no='WO/2023/002', category_id='100')
        self.assertEqual(qs_combined.count(), 1)
        self.assertEqual(qs_combined.first().won_no, 'WO/2023/002')

        # Test no match scenario
        qs_no_match = WorkOrder.get_filtered_work_orders(comp_id=settings.DEFAULT_COMPANY_ID, won_no='NON_EXISTENT')
        self.assertEqual(qs_no_match.count(), 0)

class WorkOrderViewsTest(TestCase):
    """
    Integration tests for the WorkOrder views, including HTMX interactions.
    """
    @classmethod
    def setUpTestData(cls):
        # Create sample data for view tests
        WoCategory.objects.create(
            c_id=1,
            symbol='WO',
            c_name='Work Order Category',
            comp_id=settings.DEFAULT_COMPANY_ID
        )
        WorkOrder.objects.create(
            won_no='WO/VIEW/001', # Selectable
            task_project_title='View Test Project One',
            release_wis='1',
            dry_actual_run='1',
            comp_id=settings.DEFAULT_COMPANY_ID,
            close_open='0',
            c_id=1
        )
        WorkOrder.objects.create(
            won_no='WO/VIEW/002', # Not selectable
            task_project_title='View Test Project Two',
            release_wis='0',
            dry_actual_run='0',
            comp_id=settings.DEFAULT_COMPANY_ID,
            close_open='0',
            c_id=1
        )
        WorkOrder.objects.create(
            won_no='WO/VIEW/003', # Different company, should not show
            task_project_title='View Test Project Three',
            release_wis='1',
            dry_actual_run='1',
            comp_id=settings.DEFAULT_COMPANY_ID + 1,
            close_open='0',
            c_id=1
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view_get(self):
        """Test the main list page loads correctly and contains expected elements."""
        response = self.client.get(reverse('pr_new_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/pr_new/list.html')
        self.assertIn('search_form', response.context)
        self.assertContains(response, '<div id="workOrderTable-container"') # HTMX target for table
        self.assertContains(response, 'Loading Work Orders...') # Initial loading state

    def test_table_partial_view_get_initial_load(self):
        """Test the HTMX partial view for the table loads correctly with initial data."""
        response = self.client.get(reverse('pr_new_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/pr_new/_work_order_table.html')
        self.assertIn('work_orders', response.context)
        # Should include only orders for DEFAULT_COMPANY_ID and close_open='0'
        self.assertEqual(response.context['work_orders'].count(), 2) 
        self.assertContains(response, 'WO/VIEW/001')
        self.assertContains(response, 'WO/VIEW/002')
        self.assertNotContains(response, 'WO/VIEW/003')

    def test_table_partial_view_search_wo_no(self):
        """Test filtering the table by WO Number using HTMX."""
        # Simulate HTMX request by adding HTTP_HX_REQUEST header
        response = self.client.get(reverse('pr_new_table'), {'wo_no': 'WO/VIEW/001'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/pr_new/_work_order_table.html')
        self.assertIn('work_orders', response.context)
        self.assertEqual(response.context['work_orders'].count(), 1)
        self.assertContains(response, 'WO/VIEW/001')
        self.assertNotContains(response, 'WO/VIEW/002')

    def test_table_partial_view_search_category(self):
        """Test filtering the table by category using HTMX."""
        response = self.client.get(reverse('pr_new_table'), {'category': '1'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/pr_new/_work_order_table.html')
        self.assertIn('work_orders', response.context)
        self.assertEqual(response.context['work_orders'].count(), 2)
        self.assertContains(response, 'WO/VIEW/001')
        self.assertContains(response, 'WO/VIEW/002')

    def test_table_partial_view_search_no_results(self):
        """Test search returning no results, verifying the empty data message."""
        response = self.client.get(reverse('pr_new_table'), {'wo_no': 'NON_EXISTENT'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/pr_new/_work_order_table.html')
        self.assertIn('work_orders', response.context)
        self.assertEqual(response.context['work_orders'].count(), 0)
        self.assertContains(response, 'No data to display !')

    def test_select_link_visibility(self):
        """Test that the 'Select' link is conditionally rendered based on model logic."""
        # Test WorkOrder with is_selectable=True
        response_selectable = self.client.get(reverse('pr_new_table'), {'wo_no': 'WO/VIEW/001'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response_selectable.status_code, 200)
        self.assertContains(response_selectable, '<a href="/pr_new/details/?WONo=WO/VIEW/001"') # Check if link is present

        # Test WorkOrder with is_selectable=False
        response_not_selectable = self.client.get(reverse('pr_new_table'), {'wo_no': 'WO/VIEW/002'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response_not_selectable.status_code, 200)
        self.assertNotContains(response_not_selectable, '<a href="/pr_new/details/?WONo=WO/VIEW/002"') # Check if link is NOT present
        self.assertContains(response_not_selectable, 'WO/VIEW/002') # Row itself should still be there

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for Dynamic Updates:**
    *   The search form (including the "Search" button and "WO Category" dropdown) uses `hx-get`, `hx-target`, `hx-swap`, and `hx-trigger` to fetch the updated table partial without a full page reload.
    *   The `workOrderTable-container` div uses `hx-trigger="load, refreshWorkOrderList from:body"` to initially load the table and to respond to custom events (e.g., if a future CRUD operation on a *different* page needs to update this list).
*   **DataTables for List Views:**
    *   The `_work_order_table.html` partial initializes DataTables on the `workOrderTable` element. This provides client-side searching, sorting, and pagination as observed in the original `GridView`.
    *   The `pageLength` is set to 20, matching the ASP.NET `PageSize`.
*   **Alpine.js for UI State Management:**
    *   While not strictly critical for the basic functionality of *this* page (as HTMX and DataTables cover most of the dynamism), Alpine.js is included in the `list.html` as a placeholder for future, more complex UI state management (e.g., controlling modals, simple toggles, dynamic form fields) as per the overall modernization strategy.
*   **No Full Page Reloads:** All filtering and table updates are handled via HTMX, ensuring a smooth, single-page application-like experience.
*   **`HX-Trigger` Responses:** Although no direct CRUD operations are performed *on this page* that would require triggering a list refresh, the `refreshWorkOrderList` custom event is configured to re-trigger the table load, providing a robust mechanism for future integrations.

---

## Final Notes

This comprehensive plan provides a clear, actionable roadmap for transitioning the "PR - New" module to Django. By leveraging modern frameworks, adhering to best practices, and focusing on automation, we aim to deliver a performant, maintainable, and user-friendly application. The detailed code examples and test cases ensure high quality and reduce manual effort during implementation.