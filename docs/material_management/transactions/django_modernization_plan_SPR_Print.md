This comprehensive plan outlines the migration of your legacy ASP.NET application, `SPR_Print.aspx`, to a modern Django-based solution. Our approach emphasizes automated processes, clear instructions, and the use of cutting-edge technologies like HTMX and Alpine.js to deliver a highly interactive and efficient user experience without traditional complex JavaScript.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code interacts with three primary database tables, likely representing core ERP entities:
*   `tblMM_SPR_Master`: Contains the main Store Purchase Requisition (SPR) data.
*   `tblHR_OfficeStaff`: Stores employee information.
*   `tblFinancial_master`: Manages financial year data.
*   A `tblCompany` is implied by the `CompId` filter, but not explicitly queried. We'll define a placeholder for it.

**Identified Tables and Columns:**

*   **`tblMM_SPR_Master`**
    *   `Id` (Primary Key, integer)
    *   `SPRNo` (String/Varchar)
    *   `SessionId` (Integer, foreign key to `tblHR_OfficeStaff.EmpId`)
    *   `FinYearId` (Integer, foreign key to `tblFinancial_master.FinYearId`)
    *   `SysDate` (String/Varchar, stored as 'MM-DD-YYYY', converted to 'DD/MM/YYYY' in ASP.NET)
    *   `SysTime` (String/Varchar)
    *   `CompId` (Integer, foreign key to `tblCompany.CompId`)
*   **`tblHR_OfficeStaff`**
    *   `EmpId` (Primary Key, integer)
    *   `Title` (String/Varchar, e.g., 'Mr.', 'Ms.')
    *   `EmployeeName` (String/Varchar)
    *   `CompId` (Integer, foreign key to `tblCompany.CompId`)
*   **`tblFinancial_master`**
    *   `FinYearId` (Primary Key, integer)
    *   `FinYear` (String/Varchar, e.g., '2023-2024')
    *   `CompId` (Integer, foreign key to `tblCompany.CompId`)
*   **`tblCompany`** (Inferred)
    *   `CompId` (Primary Key, integer)
    *   `CompanyName` (String/Varchar) - (Assumed, replace with actual column name if different)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD and search operations in the ASP.NET code.

**Analysis:**
The `SPR_Print.aspx` page primarily focuses on **Read (Retrieve)** operations with advanced filtering and display capabilities.

*   **Read (Retrieve):**
    *   **List Display:** The `GridView2` displays a list of SPR records.
    *   **Initial Data Load:** `LoadData` function populates the `GridView` initially.
    *   **Filtering:** Users can filter records by "Employee Name" or "SPR No" using a dropdown and a text input.
    *   **Search Action:** The "Search" button (`btnSearch_Click`) triggers the `LoadData` function with the applied filters.
    *   **Pagination:** The `GridView2_PageIndexChanging` event handles pagination.
    *   **Autocomplete:** `GetCompletionList` (a WebMethod) provides autocomplete suggestions for employee names.
*   **Other Interactions:**
    *   **Dropdown Change:** `drpfield_SelectedIndexChanged` dynamically shows/hides the employee name or SPR number input field.
    *   **Select Action:** The "Select" `LinkButton` in the `GridView` redirects to `SPR_Print_Details.aspx` with specific SPR parameters (`SPRNo`, `Id`, `ModId`, `SubModId`, `Key`, `f`). This indicates a drill-down to a detail view, not a direct CRUD operation on the current page's data.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**
The page's UI elements map to Django templates and frontend interactivity as follows:

*   **Header:** "SPR - Print" maps to a page title in the Django template.
*   **Search/Filter Section:**
    *   `drpfield` (DropDownList): Will be a standard HTML `<select>` element. Its behavior (showing/hiding textboxes) will be managed by Alpine.js.
    *   `txtEmpName` (TextBox) & `txtSprNo` (TextBox): Standard HTML `<input type="text">` elements.
    *   `txtEmpName_AutoCompleteExtender`: Replaced by HTMX for real-time search suggestions combined with a specific Django view for autocomplete data.
    *   `btnSearch` (Button): A standard HTML `<button>` that triggers an HTMX request to refresh the data table.
*   **Data Display Section:**
    *   `GridView2`: Replaced by a standard HTML `<table>` element.
        *   Its data-binding and pagination features will be managed by [DataTables.js](https://datatables.net/) on the client-side.
        *   Sorting and client-side search (for displayed columns) will also be handled by DataTables.
        *   The "Select" `LinkButton` will become a standard HTML `<button>` or `<a>` tag, linking to the SPR detail page.
*   **Dynamic UI:** The `AutoPostBack` and `Visible` property changes will be managed by Alpine.js for lightweight client-side state management.

### Step 4: Generate Django Code

We will create a new Django application, likely named `material_management`, to house these components.

#### 4.1 Models

**Task:** Create Django models based on the identified database schema. We'll include placeholder models for `Company`, `HROfficeStaff`, and `FinancialYear` as they are dependencies for `SPRMaster`.

**Instructions:**
- Models are defined with `managed = False` to connect to existing legacy tables.
- Fields are mapped using `db_column`.
- Custom properties are added to `SPRMaster` to handle date formatting and employee name retrieval, aligning with the "fat model" principle.

```python
# material_management/models.py
from django.db import models
from django.db.models import F, Value
from django.db.models.functions import Concat, Cast
from django.db.models import CharField
import datetime

# Placeholder for Company model, assuming CompId is the primary key and there's a name column.
# Adjust db_table and field names as per your actual Company table.
class Company(models.Model):
    id = models.IntegerField(db_column='CompId', primary_key=True)
    name = models.CharField(max_length=255, db_column='CompanyName', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCompany' # Replace with actual company table name if different
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name or f"Company {self.id}"

class HROfficeStaff(models.Model):
    id = models.IntegerField(db_column='EmpId', primary_key=True)
    title = models.CharField(max_length=50, db_column='Title')
    employee_name = models.CharField(max_length=255, db_column='EmployeeName')
    # Link to Company model if multi-company support is needed
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='hr_staff_members', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'HR Office Staff'
        verbose_name_plural = 'HR Office Staff'

    def __str__(self):
        return f"{self.title or ''}. {self.employee_name}" # Handle potential null title

    def get_full_name_with_title(self):
        """Returns the employee's full name with title, e.g., 'Mr. John Doe'."""
        return f"{self.title or ''}. {self.employee_name}"

class FinancialYear(models.Model):
    id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(max_length=20, db_column='FinYear')
    # Link to Company model if multi-company support is needed
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='financial_years', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class SPRMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    spr_no = models.CharField(max_length=50, db_column='SPRNo')
    # Use ForeignKey for SessionId and FinYearId, mapping to respective models
    session_user = models.ForeignKey(HROfficeStaff, on_delete=models.DO_NOTHING, db_column='SessionId', related_name='spr_masters', null=True, blank=True)
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='spr_masters', null=True, blank=True)
    
    # Store date and time as CharField as in original, with properties for formatted display
    sys_date_str = models.CharField(max_length=20, db_column='SysDate', help_text="Stored as MM-DD-YYYY")
    sys_time_str = models.CharField(max_length=20, db_column='SysTime')
    
    # Link to Company model
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='spr_masters_by_company', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Master'
        verbose_name = 'SPR Master'
        verbose_name_plural = 'SPR Masters'
        ordering = ['-id'] # Matches 'Order by Id Desc' in original SQL

    def __str__(self):
        return self.spr_no

    @property
    def formatted_sys_date(self):
        """Converts the SysDate string (MM-DD-YYYY) to DD/MM/YYYY for display."""
        try:
            # Assuming SysDate_str is in 'MM-DD-YYYY'
            date_obj = datetime.datetime.strptime(self.sys_date_str, '%m-%d-%Y').date()
            return date_obj.strftime('%d/%m/%Y') # Format to 'DD/MM/YYYY'
        except (ValueError, TypeError):
            return self.sys_date_str # Return original if conversion fails
    
    @property
    def generated_by_name(self):
        """Fetches the full name of the employee who generated the SPR."""
        return self.session_user.get_full_name_with_title() if self.session_user else 'N/A'

    @property
    def fin_year_display(self):
        """Fetches the financial year string."""
        return self.financial_year.fin_year if self.financial_year else 'N/A'
```

#### 4.2 Forms

**Task:** Define a Django form for handling the search/filter inputs. Since this is primarily a display page, a simple `Form` will suffice, rather than a `ModelForm`.

**Instructions:**
- Use `forms.Form` for search filters.
- Add `widgets` for Tailwind CSS classes.
- No direct `ModelForm` for `SPRMaster` as there are no CRUD operations on this page itself.

```python
# material_management/forms.py
from django import forms
from .models import HROfficeStaff # Used for autocomplete suggestions

class SPRSearchForm(forms.Form):
    # Field to select search type: 'employee' or 'spr_no'
    search_by_choices = [
        ('employee', 'Employee Name'),
        ('spr_no', 'SPR No'),
    ]
    search_by = forms.ChoiceField(
        choices=search_by_choices,
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'x-model': 'searchByType' # Alpine.js binding
        }),
        label="Search By"
    )

    # Fields for search values (one will be visible at a time via Alpine.js)
    employee_name = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter Employee Name',
            'x-show': "searchByType === 'employee'", # Alpine.js binding
            'hx-get': '/material_management/spr/autocomplete/employees/', # HTMX for autocomplete
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#employee-suggestions',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off'
        }),
        label="Employee Name"
    )

    spr_no = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter SPR No',
            'x-show': "searchByType === 'spr_no'" # Alpine.js binding
        }),
        label="SPR No"
    )
```

#### 4.3 Views

**Task:** Implement views for listing SPRs, handling search filters, and providing autocomplete suggestions. The `SPR_Print_Details.aspx` redirection will be a separate URL.

**Instructions:**
- `SPRListView`: Displays the initial page with the search form and a placeholder for the table.
- `SPRTablePartialView`: Fetches filtered data and renders only the table HTML (for HTMX). This is where the core `LoadData` logic resides, transformed into Django ORM queries.
- `EmployeeAutocompleteView`: Provides employee name suggestions for the autocomplete feature.
- Keep views thin (5-15 lines) by pushing data retrieval and filtering logic to managers or model methods if complex. For this case, direct ORM in view is fine as the filtering is simple.

```python
# material_management/views.py
from django.views.generic import ListView, TemplateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db.models import Q # For complex queries
from .models import SPRMaster, HROfficeStaff, FinancialYear, Company # Ensure Company is imported
from .forms import SPRSearchForm

# Assuming global context for CompId and FyId (e.g., from user session or settings)
# For a real application, this would be retrieved from request.user or middleware.
# For now, we'll use dummy values or assume they are passed via context/session if applicable.
# Placeholder: replace with actual logic to get these values
DEFAULT_COMP_ID = 1 # Example Company ID
DEFAULT_FIN_YEAR_ID = 1 # Example Financial Year ID (e.g., current financial year)

class SPRListView(TemplateView):
    """
    Renders the main SPR print page with the search form.
    The SPR list table itself will be loaded via HTMX into this page.
    """
    template_name = 'material_management/spr/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = SPRSearchForm(self.request.GET)
        return context

class SPRTablePartialView(ListView):
    """
    Returns the HTML for the SPR data table, intended to be loaded via HTMX.
    Handles search filters and mimics the LoadData logic.
    """
    model = SPRMaster
    template_name = 'material_management/spr/_spr_table.html'
    context_object_name = 'spr_masters'
    paginate_by = 20 # Matches original PageSize

    def get_queryset(self):
        # In a real application, CompId and FinYearId would come from session/user context.
        # For this example, we assume they are globally available or defaults.
        # You would replace DEFAULT_COMP_ID and DEFAULT_FIN_YEAR_ID with actual session/user data.
        current_comp_id = self.request.session.get('compid', DEFAULT_COMP_ID)
        current_fin_year_id = self.request.session.get('finyearid', DEFAULT_FIN_YEAR_ID) # Assuming 'finyearid' in session

        queryset = SPRMaster.objects.filter(
            company__id=current_comp_id,
            financial_year__id__lte=current_fin_year_id # Assuming FinYearId <= current (as per original logic)
        ).select_related('session_user', 'financial_year', 'company') # Efficiently retrieve related data

        # Apply filters based on search form
        search_by = self.request.GET.get('search_by')
        search_value_emp = self.request.GET.get('employee_name')
        search_value_spr = self.request.GET.get('spr_no')

        if search_by == 'spr_no' and search_value_spr:
            queryset = queryset.filter(spr_no__iexact=search_value_spr) # Case-insensitive exact match
        elif search_by == 'employee' and search_value_emp:
            # Original code used fun.getCode(txtEmpName.Text) and then compared with SessionId.
            # This implies EmpId is part of the employee name string or lookup.
            # We'll adapt this by allowing search by part of employee name.
            # If the original 'fun.getCode' maps a name to EmpId, that logic needs to be in model/manager.
            # For simplicity, we search by employee name directly here.
            # If 'fun.getCode' extracts employee ID from 'Name [ID]', we would parse it here.
            # For example, if search_value_emp is "John Doe [123]", we would parse '123'.
            # Assuming GetCompletionList returns 'EmployeeName [EmpId]' format, we'll parse it.
            
            # Simple approach: search by employee_name field
            # Check if search_value_emp contains EmpId in 'Name [ID]' format
            if '[' in search_value_emp and ']' in search_value_emp:
                try:
                    emp_id_str = search_value_emp.split('[')[-1].strip(']')
                    emp_id = int(emp_id_str)
                    queryset = queryset.filter(session_user__id=emp_id)
                except ValueError:
                    # Fallback if parsing fails, search by employee name
                    queryset = queryset.filter(session_user__employee_name__icontains=search_value_emp)
            else:
                # If no ID, search by name contains
                queryset = queryset.filter(session_user__employee_name__icontains=search_value_emp)

        return queryset

    # No pagination logic needed in the view; DataTables handles it on the client.
    # The `paginate_by` is for Django's built-in pagination if DataTables is not used
    # in server-side processing mode. For pure client-side DataTables, we just pass
    # the entire filtered queryset.

class EmployeeAutocompleteView(View):
    """
    Provides employee name suggestions for the autocomplete input, via HTMX.
    Mimics GetCompletionList WebMethod.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        # In a real app, CompId would come from session/user context.
        current_comp_id = request.session.get('compid', DEFAULT_COMP_ID)
        
        # Filter employees by company and name, case-insensitive
        employees = HROfficeStaff.objects.filter(
            company__id=current_comp_id,
            employee_name__icontains=query
        ).values('id', 'employee_name', 'title') # Select specific fields for efficiency

        suggestions = []
        for emp in employees:
            # Format as 'Title. EmployeeName [EmpId]'
            formatted_name = f"{emp['title'] or ''}. {emp['employee_name']} [{emp['id']}]"
            suggestions.append(formatted_name)
        
        # Limit the number of suggestions if needed, e.g., to 10 as per ASP.NET logic
        # if len(suggestions) > 10:
        #    suggestions = suggestions[:10]
        
        # HTMX expects plain HTML for dynamic updates, or JSON if hx-vals is used and parsed client-side.
        # For simplicity, returning a simple list of DIVs that HTMX can swap in.
        # Each suggestion can be clickable using Alpine.js or HTMX to populate the input field.
        html_suggestions = ""
        if suggestions:
            html_suggestions = "<div class='absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto' x-show='autocompleteOpen'>"
            for suggestion in suggestions:
                html_suggestions += f"<div class='p-2 hover:bg-gray-100 cursor-pointer' @click='autocompleteOpen = false; $el.closest(\"form\").querySelector(\"[name=employee_name]\").value = \"{suggestion.replace('\"', '&quot;')}\"' hx-get='{reverse_lazy('spr_table_partial')}?search_by=employee&employee_name={suggestion}' hx-target='#sprTable-container' hx-swap='innerHTML' hx-indicator='#loading-indicator'>{suggestion}</div>"
            html_suggestions += "</div>"
        
        return HttpResponse(html_suggestions)

# View for redirecting to details page, mimicking SPR_Print_Details.aspx
# This would typically be a link directly in the template, not a separate view for redirect.
# The original ASP.NET code redirected on a "Sel" command.
# We'll provide a placeholder function for the URL.
def spr_detail_redirect_view(request, spr_id):
    """
    This function mimics the redirection logic from the original ASP.NET page.
    In Django, you would directly generate the URL in the template using reverse.
    This view is just a placeholder if a server-side redirect is strictly required
    for some reason, but ideally, the link on the table should be a direct URL.
    """
    spr_obj = SPRMaster.objects.filter(id=spr_id).first()
    if spr_obj:
        # Construct the URL for the details page
        # Replace 'spr_details_url' with the actual URL pattern for your SPR details page
        # and adjust parameters as needed.
        detail_url = reverse_lazy('material_management:spr_details', kwargs={'pk': spr_obj.id})
        # You might also want to pass SPRNo or other params if needed for the details page
        # e.g., detail_url = f"{detail_url}?spr_no={spr_obj.spr_no}&modid=6..."
        return HttpResponse(status=204, headers={'HX-Redirect': detail_url}) # HTMX redirect
    return HttpResponse(status=404) # Not found if SPR doesn't exist
```

#### 4.4 Templates

**Task:** Create templates for the list view and a partial template for the data table.

**Instructions:**
- `list.html`: Extends `core/base.html`, includes search form, and a container for the HTMX-loaded table. Uses Alpine.js for dropdown logic and autocomplete visibility.
- `_spr_table.html`: Contains the DataTables-enabled table structure, loaded via HTMX.
- No form or delete templates as this page doesn't directly perform these operations.

```html
<!-- material_management/templates/material_management/spr/list.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">SPR - Print</h2>
    </div>

    <!-- Search Form Section -->
    <div class="bg-white shadow-md rounded-lg p-6 mb-6" x-data="{ searchByType: '{{ form.search_by.value|default:'employee' }}', autocompleteOpen: false, employeeNameInput: '', sprNoInput: '' }" x-init="
        employeeNameInput = document.querySelector('[name=employee_name]').value;
        sprNoInput = document.querySelector('[name=spr_no]').value;
        if(employeeNameInput !== '') searchByType = 'employee';
        else if(sprNoInput !== '') searchByType = 'spr_no';
    ">
        <form hx-get="{% url 'material_management:spr_table_partial' %}"
              hx-target="#sprTable-container"
              hx-swap="innerHTML"
              hx-indicator="#loading-indicator"
              x-on:submit.prevent="$el.requestSubmit()">
            {% csrf_token %}
            <div class="flex items-center space-x-4">
                <div class="flex-grow">
                    <label for="{{ form.search_by.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By</label>
                    {{ form.search_by }}
                </div>
                
                <div class="flex-grow relative">
                    <div x-show="searchByType === 'employee'">
                        <label for="{{ form.employee_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Employee Name</label>
                        <input type="text" name="employee_name" id="{{ form.employee_name.id_for_label }}" 
                               class="{{ form.employee_name.field.widget.attrs.class }}" 
                               placeholder="{{ form.employee_name.field.widget.attrs.placeholder }}"
                               x-model="employeeNameInput"
                               hx-get="{% url 'material_management:employee_autocomplete' %}"
                               hx-trigger="keyup changed delay:500ms"
                               hx-target="#employee-suggestions"
                               hx-swap="innerHTML"
                               autocomplete="off"
                               x-on:focus="autocompleteOpen = true"
                               x-on:blur.away="setTimeout(() => autocompleteOpen = false, 100)"
                               >
                        <div id="employee-suggestions" class="absolute z-10 w-full" x-show="autocompleteOpen">
                            <!-- Autocomplete suggestions will be loaded here by HTMX -->
                        </div>
                    </div>
                    <div x-show="searchByType === 'spr_no'">
                        <label for="{{ form.spr_no.id_for_label }}" class="block text-sm font-medium text-gray-700">SPR No</label>
                        <input type="text" name="spr_no" id="{{ form.spr_no.id_for_label }}" 
                               class="{{ form.spr_no.field.widget.attrs.class }}" 
                               placeholder="{{ form.spr_no.field.widget.attrs.placeholder }}"
                               x-model="sprNoInput">
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-transparent">.</label> {# Placeholder for alignment #}
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow-sm">
                        Search
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Loading Indicator -->
    <div id="loading-indicator" class="htmx-indicator text-center py-4">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">Loading SPR data...</p>
    </div>

    <!-- SPR Table Container -->
    <div id="sprTable-container"
         hx-trigger="load, refreshSPRList from:body"
         hx-get="{% url 'material_management:spr_table_partial' %}"
         hx-target="#sprTable-container"
         hx-swap="innerHTML"
         hx-indicator="#loading-indicator">
        <!-- The data table will be loaded here via HTMX -->
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js is already initialized globally by base.html
    });
</script>
{% endblock %}
```

```html
<!-- material_management/templates/material_management/spr/_spr_table.html -->
<!-- This partial template is loaded by HTMX -->
<div class="bg-white shadow-md rounded-lg p-6">
    {% if spr_masters %}
    <table id="sprTable" class="min-w-full bg-white dataTable">
        <thead>
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SPR No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen By</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for spr in spr_masters %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ spr.spr_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ spr.fin_year_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ spr.formatted_sys_date }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ spr.sys_time_str }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ spr.generated_by_name }}</td>
                <td class="py-2 px-4 border-b border-gray-200">
                    <a href="{% url 'material_management:spr_details' pk=spr.id %}"
                       class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded mr-2">
                        Select
                    </a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <script>
        $(document).ready(function() {
            $('#sprTable').DataTable({
                "paging": true,
                "searching": true,
                "ordering": true,
                "info": true,
                "lengthChange": true,
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
            });
        });
    </script>
    {% else %}
    <div class="text-center py-8">
        <p class="text-lg text-gray-600">No data to display!</p>
    </div>
    {% endif %}
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
- Define paths for the main list view, the HTMX-loaded table partial, and the employee autocomplete endpoint.
- Include a placeholder for the SPR details page.

```python
# material_management/urls.py
from django.urls import path
from .views import SPRListView, SPRTablePartialView, EmployeeAutocompleteView, spr_detail_redirect_view

app_name = 'material_management' # Namespace for URLs

urlpatterns = [
    path('spr/', SPRListView.as_view(), name='spr_list'),
    path('spr/table/', SPRTablePartialView.as_view(), name='spr_table_partial'),
    path('spr/autocomplete/employees/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),
    # Placeholder for the SPR details page. Replace with your actual URL pattern.
    # The 'Select' button in the table will link to this.
    path('spr/details/<int:pk>/', spr_detail_redirect_view, name='spr_details'), # Example, assuming simple PK based lookup
]
```

#### 4.6 Tests

**Task:** Write tests for the models and views to ensure functionality and coverage.

**Instructions:**
- Include unit tests for model properties and data relationships.
- Add integration tests for the list view, partial table view, and autocomplete, covering various search scenarios.
- Simulate HTMX requests in tests.

```python
# material_management/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import Company, HROfficeStaff, FinancialYear, SPRMaster
from .forms import SPRSearchForm
import datetime

# Dummy values for testing
TEST_COMP_ID = 99
TEST_FIN_YEAR_ID = 2023 # Represents the FinYearId (integer)
TEST_FIN_YEAR_STR = '2023-2024'
TEST_EMP_ID_1 = 101
TEST_EMP_ID_2 = 102

class ModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a test company
        cls.company = Company.objects.create(id=TEST_COMP_ID, name='Test Company')
        
        # Create test financial year
        cls.fin_year = FinancialYear.objects.create(id=TEST_FIN_YEAR_ID, fin_year=TEST_FIN_YEAR_STR, company=cls.company)

        # Create test HR staff members
        cls.employee1 = HROfficeStaff.objects.create(id=TEST_EMP_ID_1, title='Mr', employee_name='John Doe', company=cls.company)
        cls.employee2 = HROfficeStaff.objects.create(id=TEST_EMP_ID_2, title='Ms', employee_name='Jane Smith', company=cls.company)

        # Create test SPRMaster instances
        SPRMaster.objects.create(
            id=1, spr_no='SPR001', session_user=cls.employee1, financial_year=cls.fin_year,
            sys_date_str='12-25-2023', sys_time_str='10:00:00', company=cls.company
        )
        SPRMaster.objects.create(
            id=2, spr_no='SPR002', session_user=cls.employee2, financial_year=cls.fin_year,
            sys_date_str='01-15-2024', sys_time_str='11:30:00', company=cls.company
        )
        SPRMaster.objects.create(
            id=3, spr_no='SPR003', session_user=cls.employee1, financial_year=cls.fin_year,
            sys_date_str='02-01-2024', sys_time_str='09:00:00', company=cls.company
        )

    def test_company_creation(self):
        self.assertEqual(Company.objects.count(), 1)
        company = Company.objects.get(id=TEST_COMP_ID)
        self.assertEqual(company.name, 'Test Company')

    def test_financial_year_creation(self):
        self.assertEqual(FinancialYear.objects.count(), 1)
        fin_year = FinancialYear.objects.get(id=TEST_FIN_YEAR_ID)
        self.assertEqual(fin_year.fin_year, TEST_FIN_YEAR_STR)
        self.assertEqual(fin_year.company, self.company)

    def test_hr_office_staff_creation(self):
        self.assertEqual(HROfficeStaff.objects.count(), 2)
        emp = HROfficeStaff.objects.get(id=TEST_EMP_ID_1)
        self.assertEqual(emp.employee_name, 'John Doe')
        self.assertEqual(emp.get_full_name_with_title(), 'Mr. John Doe')
        self.assertEqual(emp.company, self.company)

    def test_spr_master_creation(self):
        self.assertEqual(SPRMaster.objects.count(), 3)
        spr1 = SPRMaster.objects.get(id=1)
        self.assertEqual(spr1.spr_no, 'SPR001')
        self.assertEqual(spr1.session_user, self.employee1)
        self.assertEqual(spr1.financial_year, self.fin_year)
        self.assertEqual(spr1.sys_date_str, '12-25-2023')
        self.assertEqual(spr1.sys_time_str, '10:00:00')
        self.assertEqual(spr1.company, self.company)

    def test_formatted_sys_date_property(self):
        spr1 = SPRMaster.objects.get(id=1)
        self.assertEqual(spr1.formatted_sys_date, '25/12/2023')
        
        spr2 = SPRMaster.objects.get(id=2)
        self.assertEqual(spr2.formatted_sys_date, '15/01/2024')

    def test_generated_by_name_property(self):
        spr1 = SPRMaster.objects.get(id=1)
        self.assertEqual(spr1.generated_by_name, 'Mr. John Doe')
        spr2 = SPRMaster.objects.get(id=2)
        self.assertEqual(spr2.generated_by_name, 'Ms. Jane Smith')

    def test_fin_year_display_property(self):
        spr1 = SPRMaster.objects.get(id=1)
        self.assertEqual(spr1.fin_year_display, TEST_FIN_YEAR_STR)


class SPRViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Set up shared data for all tests
        cls.company = Company.objects.create(id=TEST_COMP_ID, name='Test Company')
        cls.fin_year = FinancialYear.objects.create(id=TEST_FIN_YEAR_ID, fin_year=TEST_FIN_YEAR_STR, company=cls.company)
        cls.employee1 = HROfficeStaff.objects.create(id=TEST_EMP_ID_1, title='Mr', employee_name='John Doe', company=cls.company)
        cls.employee2 = HROfficeStaff.objects.create(id=TEST_EMP_ID_2, title='Ms', employee_name='Jane Smith', company=cls.company)

        SPRMaster.objects.create(id=1, spr_no='SPR001', session_user=cls.employee1, financial_year=cls.fin_year, sys_date_str='12-25-2023', sys_time_str='10:00:00', company=cls.company)
        SPRMaster.objects.create(id=2, spr_no='SPR002', session_user=cls.employee2, financial_year=cls.fin_year, sys_date_str='01-15-2024', sys_time_str='11:30:00', company=cls.company)
        SPRMaster.objects.create(id=3, spr_no='SPR003', session_user=cls.employee1, financial_year=cls.fin_year, sys_date_str='02-01-2024', sys_time_str='09:00:00', company=cls.company)

    def setUp(self):
        self.client = Client()
        # Set session data for CompId and FinYearId for views
        session = self.client.session
        session['compid'] = TEST_COMP_ID
        session['finyearid'] = TEST_FIN_YEAR_ID
        session.save()

    def test_spr_list_view_get(self):
        response = self.client.get(reverse('material_management:spr_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spr/list.html')
        self.assertIsInstance(response.context['form'], SPRSearchForm)

    def test_spr_table_partial_view_no_filter(self):
        response = self.client.get(reverse('material_management:spr_table_partial'), headers={'HX-Request': 'true'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spr/_spr_table.html')
        self.assertTrue('spr_masters' in response.context)
        self.assertEqual(len(response.context['spr_masters']), 3) # All 3 SPRs should be returned

    def test_spr_table_partial_view_filter_by_spr_no(self):
        response = self.client.get(
            reverse('material_management:spr_table_partial'),
            {'search_by': 'spr_no', 'spr_no': 'SPR001'},
            headers={'HX-Request': 'true'}
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['spr_masters']), 1)
        self.assertEqual(response.context['spr_masters'][0].spr_no, 'SPR001')

    def test_spr_table_partial_view_filter_by_employee_name(self):
        response = self.client.get(
            reverse('material_management:spr_table_partial'),
            {'search_by': 'employee', 'employee_name': f'John Doe [{TEST_EMP_ID_1}]'},
            headers={'HX-Request': 'true'}
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['spr_masters']), 2) # SPR001 and SPR003
        self.assertEqual(response.context['spr_masters'][0].session_user.employee_name, 'John Doe')
        self.assertEqual(response.context['spr_masters'][1].session_user.employee_name, 'John Doe')

    def test_spr_table_partial_view_filter_by_employee_name_partial(self):
        # Test partial name search if ID parsing fails or isn't provided
        response = self.client.get(
            reverse('material_management:spr_table_partial'),
            {'search_by': 'employee', 'employee_name': 'Jane'}, # No ID, should search by contains
            headers={'HX-Request': 'true'}
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['spr_masters']), 1)
        self.assertEqual(response.context['spr_masters'][0].session_user.employee_name, 'Jane Smith')

    def test_employee_autocomplete_view(self):
        response = self.client.get(
            reverse('material_management:employee_autocomplete'),
            {'q': 'john'},
            headers={'HX-Request': 'true'}
        )
        self.assertEqual(response.status_code, 200)
        self.assertIn('Mr. John Doe', response.content.decode('utf-8'))
        self.assertNotIn('Jane Smith', response.content.decode('utf-8'))

        response = self.client.get(
            reverse('material_management:employee_autocomplete'),
            {'q': 'jane'},
            headers={'HX-Request': 'true'}
        )
        self.assertEqual(response.status_code, 200)
        self.assertIn('Ms. Jane Smith', response.content.decode('utf-8'))
        self.assertNotIn('John Doe', response.content.decode('utf-8'))

    def test_spr_detail_redirect_view(self):
        response = self.client.get(reverse('material_management:spr_details', kwargs={'pk': 1}), headers={'HX-Request': 'true'})
        self.assertEqual(response.status_code, 204) # HTMX No Content success
        self.assertTrue('HX-Redirect' in response.headers)
        self.assertIn(reverse('material_management:spr_details', kwargs={'pk': 1}), response.headers['HX-Redirect'])

        response = self.client.get(reverse('material_management:spr_details', kwargs={'pk': 999}), headers={'HX-Request': 'true'})
        self.assertEqual(response.status_code, 404) # Not found
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
- **HTMX for Dynamic Content:**
    - The `spr_table_partial` view is loaded into `list.html` via `hx-get` on page load and on `refreshSPRList` event.
    - The search form uses `hx-get` to trigger a refresh of the `sprTable-container` when the form is submitted, effectively mimicking the `btnSearch_Click`.
    - Employee autocomplete uses `hx-get` on `keyup changed delay:500ms` to fetch suggestions, targeting the `#employee-suggestions` div.
    - The "Select" button on each row in `_spr_table.html` directly links to the `spr_details` URL, which could be a full page navigation or another HTMX-loaded component if the details page is also modernized.
- **Alpine.js for UI State:**
    - `x-data` on the search form manages the `searchByType` variable, controlling the `x-show` attributes of the `employee_name` and `spr_no` input fields, replacing the `drpfield_SelectedIndexChanged` logic.
    - `autocompleteOpen` variable in Alpine.js manages the visibility of autocomplete suggestions.
    - `x-on:click` on autocomplete suggestions populates the input field and triggers a new HTMX search.
- **DataTables for List Views:**
    - The `_spr_table.html` includes a `<script>` block to initialize DataTables on the `sprTable` once it's loaded into the DOM. This provides client-side pagination, searching, and sorting, replacing the `GridView` functionality.
- **DRY Templates:** `base.html` is assumed to contain all necessary CDN links for HTMX, Alpine.js, and DataTables (jQuery, DataTables CSS/JS).

---

## Final Notes

This comprehensive plan provides a robust framework for migrating your ASP.NET `SPR_Print` functionality to Django. By following these guidelines:

*   **Automation:** The plan is structured to be executed systematically, focusing on generating the necessary Django components and integrating them. AI-assisted tools can directly translate the identified schema and UI elements into these Django files.
*   **Business Value:** The transition to Django with HTMX and Alpine.js will result in a more responsive and modern web application. Users will experience faster interactions without full page reloads, improved search capabilities with live autocomplete, and a generally smoother user interface, leading to higher productivity and user satisfaction. The "fat model, thin view" architecture ensures the application is scalable, maintainable, and easier to extend for future business requirements.
*   **Maintainability:** Strict separation of concerns (models for business logic, thin views for presentation, HTMX/Alpine.js for frontend interaction) significantly reduces complexity and makes debugging and future development more straightforward.
*   **Testability:** Comprehensive unit and integration tests ensure the migrated application is reliable and functions as expected, reducing post-migration issues.

This structured approach, leveraging modern Django patterns and efficient frontend techniques, will modernize your ERP module effectively.