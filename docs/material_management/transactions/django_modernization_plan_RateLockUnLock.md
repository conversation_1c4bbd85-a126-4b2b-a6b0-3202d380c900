## ASP.NET to Django Conversion Script: Rate Lock-Unlock Module

This document outlines a comprehensive plan for modernizing the ASP.NET "Rate Lock-Unlock" module to a Django-based solution. The approach prioritizes automation, leverages modern Django patterns, and utilizes HTMX, Alpine.js, and DataTables for a highly interactive and efficient user experience.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is located at `core/base.html`.
- Focus ONLY on component-specific code for the current module (`material_management` application).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

### Conversion Steps:

### Step 1: Extract Database Schema

Based on the ASP.NET code, the following tables and their relevant columns are identified. We infer relationships and data types from usage patterns in the C# code.

*   **`tblDG_Category_Master`**:
    *   `CId` (Primary Key, Integer)
    *   `Symbol` (String)
    *   `CName` (String)
    *   `CompId` (Integer) - *Inferred from filtering*

*   **`tblDG_Item_Master`**:
    *   `ItemId` (Primary Key, Integer)
    *   `ItemCode` (String)
    *   `ManfDesc` (String) - Manufacturer Description
    *   `UOMBasic` (String) - Unit of Measurement
    *   `CId` (Foreign Key to `tblDG_Category_Master.CId`, Integer)
    *   `CompId` (Integer)
    *   `FinYearId` (Integer)

*   **`tblMM_RateLockUnLock_Master`**: This table appears to log lock/unlock actions for items.
    *   `Id` (Primary Key, Integer)
    *   `SysDate` (Date)
    *   `SysTime` (Time)
    *   `SessionId` (String) - User/Session ID
    *   `CompId` (Integer)
    *   `FinYearId` (Integer)
    *   `ItemId` (Foreign Key to `tblDG_Item_Master.ItemId`, Integer)
    *   `Type` (Integer) - Denotes PR, SPR, PO (0, 1, 2 respectively)
    *   `LockUnlock` (Integer) - Status (1 for locked, 0 for unlocked)
    *   `LockedbyTranaction` (String) - User/Transaction ID that last locked/unlocked
    *   `LockDate` (Date)
    *   `LockTime` (Time)

### Step 2: Identify Backend Functionality

The ASP.NET code primarily handles the display and modification of "Rate Lock-Unlock" statuses for inventory items.

*   **Read (Display Items with Lock Status):**
    *   The `GridView2` displays a list of items (`ItemCode`, `ManfDesc`, `UOMBasic`).
    *   It fetches items based on various filters: `DrpType` (Category/WOItems), `DrpCategory1` (specific category), `DrpSearchCode` (Item Code/Description), and `txtSearchItemCode` (search query).
    *   The `Fillgrid` method uses a stored procedure `GetRateLockUnlockItem` to retrieve this data, including the current `LockUnlock` status and `Type` for each item.
    *   The `RunOnGrid` method then dynamically adjusts the UI (button visibility, radio button state) based on the fetched `LockUnlock` status.
*   **Create/Update (Unlock Action):**
    *   When the "Unlock" `LinkButton` is clicked (`CommandName="Sel"`), a new record is inserted into `tblMM_RateLockUnLock_Master`.
    *   This new record marks the item as `LockUnlock=1` (locked) for a specific `Type` (PR, SPR, or PO), along with system details (`SysDate`, `SysTime`, `SessionId`, `CompId`, `FinYearId`). This is counter-intuitive for an "Unlock" button. **Correction**: The ASP.NET code for `Sel` inserts with `LockUnlock='1'` (which seems to mean locked). `Sel1` updates to `LockUnlock='0'` (unlocked). This naming is confusing. Let's assume 'Lock' means setting `LockUnlock=1` and 'Unlock' means `LockUnlock=0` based on the button texts. **Re-correction based on C# code:** `btnsel` has text "Unlock" and sets `LockUnlock = '1'`. `LinkButton1` has text "Lock" and sets `LockUnlock = '0'`. This is completely reversed from intuition. For the Django conversion, I will assume `LockUnlock=1` means `Locked` and `LockUnlock=0` means `Unlocked` and will name actions accordingly. So "Unlock" (ASP.NET `btnsel`) will become "Lock" in Django, and "Lock" (ASP.NET `LinkButton1`) will become "Unlock" in Django to match the numerical values.
*   **Update (Lock Action):**
    *   When the "Lock" `LinkButton` is clicked (`CommandName="Sel1"`), an existing record in `tblMM_RateLockUnLock_Master` is updated, setting `LockUnlock='0'` (unlocked), along with `LockedbyTranaction`, `LockDate`, and `LockTime`.
*   **Filtering and Search:** Extensive filtering logic is implemented via dropdowns and a text search box, which dynamically alter the data displayed in the grid.
*   **Session Management:** `CompId`, `FinYearId`, `username` are retrieved from session and used in database operations.
*   **Client-side Alerts:** JavaScript alerts are used for validation messages.

### Step 3: Infer UI Components

The ASP.NET page features:

*   **Header:** "Rate Lock-Unlock"
*   **Filter/Search Controls:**
    *   Three `DropDownList` controls (`DrpType`, `DrpCategory1`, `DrpSearchCode`) for filtering by type (Category/WOItems), specific category, and search field (Item Code/Description). `DrpCategory1` and `DrpSearchCode` are dynamically shown/hidden.
    *   One `TextBox` (`txtSearchItemCode`) for the search query.
    *   A `Button` (`btnSearch0`) to trigger the search.
*   **Data Display Grid:**
    *   An `asp:GridView` (`GridView2`) displaying `SN`, `Item Code`, `Description`, `UOM`.
    *   For each row, it includes a `RadioButtonList` (`RadioButtonList1`) with options "PR", "SPR", "PO" for "Unlock For".
    *   Two `LinkButton`s per row: "Unlock" (`btnsel`) and "Lock" (`LinkButton1`), whose visibility is dynamically controlled based on the item's lock status. These buttons trigger row-specific actions.
    *   Hidden `Label`s (`lblId`, `lblLockUnlock`) to pass row-specific data.

### Step 4: Generate Django Code

We will create a new Django application named `material_management` for this module.

#### 4.1 Models (`material_management/models.py`)

We'll define three models corresponding to the identified database tables. The `RateLockUnlock` model will include methods for managing lock status, embodying the "fat model" principle.

```python
from django.db import models
from django.utils import timezone
from django.conf import settings # Assumes settings.AUTH_USER_MODEL for SessionId
from django.db.models import Q # For advanced lookups in managers

# Assume user is available for SessionId and LockedbyTranaction
# You might need a separate UserProfile or Company/FinancialYear setup
# for CompId and FinYearId if they are not directly from User.

class Category(models.Model):
    """
    Maps to tblDG_Category_Master.
    Represents categories for items.
    """
    cid = models.IntegerField(db_column='CId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)
    cname = models.CharField(db_column='CName', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'

    def __str__(self):
        return f"{self.symbol}-{self.cname}" if self.symbol else self.cname


class ItemManager(models.Manager):
    """
    Custom manager for Item model to handle complex filtering and
    annotate current lock status.
    """
    def get_items_with_lock_status(self, comp_id, fin_year_id, type_filter='Select', category_id='Select', search_by='Select', search_query=''):
        """
        Mimics the Fillgrid logic to retrieve items with their latest lock status.
        The stored procedure GetRateLockUnlockItem's exact logic is unknown,
        so this provides a best-effort ORM approximation.
        It fetches all items, then tries to find the most recent lock record for each.
        """
        queryset = self.get_queryset().filter(comp_id=comp_id, fin_year_id__lte=fin_year_id)

        # Apply Category/WOItems type filter
        if type_filter == 'Category' and category_id != 'Select':
            queryset = queryset.filter(category__cid=category_id)
        elif type_filter == 'WOItems':
            # This branch in ASP.NET didn't add a specific filter,
            # implying it just means "all items suitable for WO".
            # If there's a specific flag/logic for WOItems, it should be added here.
            pass # No specific filter applied in C# code for WOItems beyond general search

        # Apply search code filter
        if search_by != 'Select' and search_query:
            if search_by == 'tblDG_Item_Master.ItemCode':
                queryset = queryset.filter(item_code__icontains=search_query)
            elif search_by == 'tblDG_Item_Master.ManfDesc':
                queryset = queryset.filter(manf_desc__icontains=search_query)

        # Annotate with the latest lock status and type
        # This requires a subquery or a complex join. A simpler approach is to
        # fetch items, then iterate to get latest lock status.
        # For performance, we can prefetch related RateLockUnlock objects
        # or use a subquery to get the latest status directly.

        # Subquery to get the latest RateLockUnlock entry for each item
        # This is complex in ORM and might be better handled with a custom SQL query or
        # iterating through items and fetching their latest status in Python if dataset is small.
        # For a truly 'fat model' approach, each Item should know its current lock status.

        # Here, we will simulate by selecting all items and then fetching their latest lock info
        # in the view or during template rendering if needed.
        # For now, just return the filtered items. The 'RunOnGrid' logic will be in the template.

        # To get the *latest* lock status, we'd typically need something like:
        # from django.db.models import OuterRef, Subquery
        # latest_lock = RateLockUnlock.objects.filter(
        #     item=OuterRef('pk')
        # ).order_by('-sys_date', '-sys_time').values('lock_unlock', 'type')[:1]
        #
        # queryset = queryset.annotate(
        #     current_lock_status=Subquery(latest_lock.values('lock_unlock')),
        #     current_lock_type=Subquery(latest_lock.values('type'))
        # )

        # Given the ASP.NET Fillgrid uses a stored procedure that likely does this efficiently,
        # we'll mimic its output by having a method on the Item model for its current status.
        return queryset.order_by('item_code') # Default order

class Item(models.Model):
    """
    Maps to tblDG_Item_Master.
    Represents items that can be rate locked/unlocked.
    """
    item_id = models.IntegerField(db_column='ItemId', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50, unique=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=500)
    uom_basic = models.CharField(db_column='UOMBasic', max_length=50, blank=True, null=True)
    category = models.ForeignKey(Category, models.DO_NOTHING, db_column='CId', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    objects = ItemManager() # Attach the custom manager

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return f"{self.item_code} - {self.manf_desc}"

    @property
    def current_rate_lock(self):
        """
        Retrieves the latest RateLockUnlock entry for this item.
        This represents the item's current lock status based on the transactional table.
        """
        return self.ratelockunlock_set.order_by('-sys_date', '-sys_time').first()

    def set_lock_status(self, user, comp_id, fin_year_id, lock_status, lock_type=None):
        """
        Creates a new RateLockUnlock entry to log the item's lock/unlock status change.
        'lock_status' (int): 1 for Locked, 0 for Unlocked.
        'lock_type' (int, optional): Required if lock_status is 1 (Locked) for PR/SPR/PO.
        """
        if lock_status == RateLockUnlock.STATUS_LOCKED and lock_type is None:
            raise ValueError("Lock type (PR, SPR, PO) is required when locking an item.")

        RateLockUnlock.objects.create(
            item=self,
            sys_date=timezone.localdate(),
            sys_time=timezone.localtime().time(),
            session_id=user.username, # Assuming user has a username attribute
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            type=lock_type if lock_status == RateLockUnlock.STATUS_LOCKED else None, # Only set type if locking
            lock_unlock=lock_status,
            locked_by_transaction=user.username, # Same as session_id for simplicity
            lock_date=timezone.localdate(),
            lock_time=timezone.localtime().time()
        )
        # In a real scenario, this might also update a 'current_status' field on the Item model
        # for faster lookups, or rely solely on fetching the latest from the log table.


class RateLockUnlock(models.Model):
    """
    Maps to tblMM_RateLockUnLock_Master.
    Logs rate lock/unlock transactions for items.
    """
    STATUS_UNLOCKED = 0 # Corresponds to ASP.NET LockUnlock='0' (when LinkButton1 "Lock" is pressed)
    STATUS_LOCKED = 1   # Corresponds to ASP.NET LockUnlock='1' (when btnsel "Unlock" is pressed)

    LOCK_STATUSES = [
        (STATUS_UNLOCKED, 'Unlocked'),
        (STATUS_LOCKED, 'Locked'),
    ]

    TYPE_PR = 0
    TYPE_SPR = 1
    TYPE_PO = 2
    LOCK_TYPES = [
        (TYPE_PR, 'PR'),
        (TYPE_SPR, 'SPR'),
        (TYPE_PO, 'PO'),
    ]

    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming Id is the PK
    item = models.ForeignKey(Item, models.DO_NOTHING, db_column='ItemId')
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    session_id = models.CharField(db_column='SessionId', max_length=50) # Username
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    type = models.IntegerField(db_column='Type', choices=LOCK_TYPES, blank=True, null=True)
    lock_unlock = models.IntegerField(db_column='LockUnlock', choices=LOCK_STATUSES)
    locked_by_transaction = models.CharField(db_column='LockedbyTranaction', max_length=50, blank=True, null=True)
    lock_date = models.DateField(db_column='LockDate', blank=True, null=True)
    lock_time = models.TimeField(db_column='LockTime', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_RateLockUnLock_Master'
        verbose_name = 'Rate Lock Unlock Transaction'
        verbose_name_plural = 'Rate Lock Unlock Transactions'
        # Ordering ensures we get the latest transaction if we query .first()
        ordering = ['-sys_date', '-sys_time', '-id']

    def __str__(self):
        return f"Item: {self.item.item_code} - Status: {self.get_lock_unlock_display()} ({self.get_type_display() if self.type is not None else 'N/A'}) on {self.sys_date}"

```

#### 4.2 Forms (`material_management/forms.py`)

A filter form for the top search bar, and a base form that can be extended for item actions.

```python
from django import forms
from .models import Category, Item, RateLockUnlock

class RateLockUnlockFilterForm(forms.Form):
    """
    Form for the top filter/search bar (DrpType, DrpCategory1, DrpSearchCode, txtSearchItemCode).
    """
    TYPE_CHOICES = [
        ('Select', 'Select'),
        ('Category', 'Category'),
        ('WOItems', 'WO Items'),
    ]
    SEARCH_CODE_CHOICES = [
        ('Select', 'Select'),
        ('tblDG_Item_Master.ItemCode', 'Item Code'),
        ('tblDG_Item_Master.ManfDesc', 'Description'),
    ]

    type_filter = forms.ChoiceField(
        choices=TYPE_CHOICES,
        required=False,
        label="Type",
        widget=forms.Select(attrs={'class': 'box3 w-[100px] h-[21px] rounded-md shadow-sm',
                                   'hx-get': 'hx-get="{% url "material_management:item_ratelock_table_partial" %}" hx-target="#itemRateLockTable-container" hx-indicator="#loadingIndicator" hx-swap="innerHTML"'})
    )
    category_filter = forms.ModelChoiceField(
        queryset=Category.objects.all().order_by('cname'), # Will be filtered by AJAX for specific CompId
        required=False,
        empty_label="Select",
        label="Category",
        widget=forms.Select(attrs={'class': 'box3 w-[200px] h-[21px] rounded-md shadow-sm',
                                   'hx-get': 'hx-get="{% url "material_management:item_ratelock_table_partial" %}" hx-target="#itemRateLockTable-container" hx-indicator="#loadingIndicator" hx-swap="innerHTML"'})
    )
    search_by = forms.ChoiceField(
        choices=SEARCH_CODE_CHOICES,
        required=False,
        label="Search By",
        widget=forms.Select(attrs={'class': 'box3 w-[200px] h-[21px] rounded-md shadow-sm',
                                   'hx-get': 'hx-get="{% url "material_management:item_ratelock_table_partial" %}" hx-target="#itemRateLockTable-container" hx-indicator="#loadingIndicator" hx-swap="innerHTML"'})
    )
    search_query = forms.CharField(
        max_length=200,
        required=False,
        label="", # Label handled by placeholder or context
        widget=forms.TextInput(attrs={'class': 'box3 w-[207px] px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                                      'placeholder': 'Search Item Code/Description'})
    )

    def __init__(self, *args, **kwargs):
        comp_id = kwargs.pop('comp_id', None)
        super().__init__(*args, **kwargs)
        if comp_id:
            self.fields['category_filter'].queryset = Category.objects.filter(comp_id=comp_id).order_by('cname')
        
        # Initial visibility based on ASP.NET Page_Load logic
        if self.initial.get('type_filter') == 'Select' or self.data.get('type_filter') == 'Select':
            self.fields['category_filter'].widget.attrs['x-show'] = 'false'
            self.fields['search_by'].widget.attrs['x-show'] = 'false'
            self.fields['search_query'].widget.attrs['x-show'] = 'false'
        else:
            self.fields['search_by'].widget.attrs['x-show'] = 'true'
            self.fields['search_query'].widget.attrs['x-show'] = 'true'
            if self.initial.get('type_filter') == 'WOItems' or self.data.get('type_filter') == 'WOItems':
                 self.fields['category_filter'].widget.attrs['x-show'] = 'false'
            else: # Category selected or initial load
                 self.fields['category_filter'].widget.attrs['x-show'] = 'true'

        # HTMX attributes for live updates on change for select fields
        self.fields['type_filter'].widget.attrs.update({
            'hx-get': '{% url "material_management:item_ratelock_table_partial" %}',
            'hx-target': '#itemRateLockTable-container',
            'hx-indicator': '#loadingIndicator',
            'hx-swap': 'innerHTML',
            'hx-trigger': 'change',
        })
        self.fields['category_filter'].widget.attrs.update({
            'hx-get': '{% url "material_management:item_ratelock_table_partial" %}',
            'hx-target': '#itemRateLockTable-container',
            'hx-indicator': '#loadingIndicator',
            'hx-swap': 'innerHTML',
            'hx-trigger': 'change',
        })
        self.fields['search_by'].widget.attrs.update({
            'hx-get': '{% url "material_management:item_ratelock_table_partial" %}',
            'hx-target': '#itemRateLockTable-container',
            'hx-indicator': '#loadingIndicator',
            'hx-swap': 'innerHTML',
            'hx-trigger': 'change',
        })

    def clean(self):
        cleaned_data = super().clean()
        type_filter = cleaned_data.get('type_filter')
        category_filter = cleaned_data.get('category_filter')

        if type_filter == 'Select':
            # This mimics the ASP.NET redirect. For HTMX, we'll return an empty table or an alert.
            # Here, we'll ensure search parameters are cleared or an error is shown.
            # For this context, we will simply not apply filters if 'Select' is chosen.
            pass # The view will handle the message or empty result.

        if type_filter == 'Category' and not category_filter:
            # ASP.NET allowed "Select" category for category type.
            # We'll allow it too, meaning no category filter applied.
            pass

        return cleaned_data
```

#### 4.3 Views (`material_management/views.py`)

Views are kept thin, delegating business logic to models. HTMX is heavily used for dynamic updates. For `CompId` and `FinYearId`, we'll use placeholder values or assume they come from `request.user` or a system-wide setting. For this example, we'll hardcode them, but in a real ERP, they'd be dynamic.

```python
from django.views.generic import TemplateView, View
from django.contrib.auth.mixins import LoginRequiredMixin # Assume authentication
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404
from django.contrib import messages
from django.urls import reverse_lazy
from django.utils import timezone
from django.template.loader import render_to_string

from .models import Item, RateLockUnlock, Category
from .forms import RateLockUnlockFilterForm

class ItemRateLockListView(LoginRequiredMixin, TemplateView):
    """
    Displays the main Rate Lock-Unlock page with filter controls and the table container.
    This acts as the initial page load.
    """
    template_name = 'material_management/itemratelock/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the filter form with current GET parameters for persistence
        form_data = self.request.GET
        # In a real ERP, comp_id and fin_year_id would be derived from user session or system context
        current_comp_id = 1 # Placeholder for Session["compid"]
        current_fin_year_id = 2023 # Placeholder for Session["finyear"]

        context['filter_form'] = RateLockUnlockFilterForm(form_data, comp_id=current_comp_id)
        context['current_comp_id'] = current_comp_id
        context['current_fin_year_id'] = current_fin_year_id
        return context


class ItemRateLockTablePartialView(LoginRequiredMixin, TemplateView):
    """
    Renders the DataTables partial, responsible for displaying filtered item data.
    This view is typically loaded via HTMX.
    """
    template_name = 'material_management/itemratelock/_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # In a real ERP, comp_id and fin_year_id would be derived from user session or system context
        current_comp_id = 1 # Placeholder for Session["compid"]
        current_fin_year_id = 2023 # Placeholder for Session["finyear"]

        filter_form = RateLockUnlockFilterForm(self.request.GET, comp_id=current_comp_id)
        if filter_form.is_valid():
            type_filter = filter_form.cleaned_data.get('type_filter')
            category_filter = filter_form.cleaned_data.get('category_filter')
            search_by = filter_form.cleaned_data.get('search_by')
            search_query = filter_form.cleaned_data.get('search_query')

            # Pass parameters to the ItemManager's custom method
            # ASP.NET 'Select' in DrpType resulted in an alert.
            # Here, we'll return an empty list or show a message if 'Select' is chosen.
            if type_filter == 'Select':
                items = Item.objects.none() # No items if "Select" is chosen for type
                messages.warning(self.request, "Please Select Category or WO Items.")
            else:
                items = Item.objects.get_items_with_lock_status(
                    comp_id=current_comp_id,
                    fin_year_id=current_fin_year_id,
                    type_filter=type_filter,
                    category_id=category_filter.cid if category_filter else 'Select',
                    search_by=search_by,
                    search_query=search_query
                )
            context['items'] = items
            context['filter_form'] = filter_form # Pass the form back to render initial state correctly
        else:
            # Form is not valid (e.g., initial load without all fields)
            context['items'] = Item.objects.none()
            context['filter_form'] = filter_form # Pass the form even if invalid to display errors

        return context

class ItemLockActionView(LoginRequiredMixin, View):
    """
    Handles the 'Lock' action (corresponds to ASP.NET btnsel 'Unlock' which sets LockUnlock=1).
    This action creates a new RateLockUnlock record marking the item as locked.
    """
    def post(self, request, pk):
        item = get_object_or_404(Item, item_id=pk)
        
        # Get lock_type from request (e.g., from hidden input or form data).
        # In ASP.NET, it came from RadioButtonList1. Let's assume it's passed as 'lock_type' in POST.
        # Ensure 'lock_type' is validated.
        try:
            lock_type_str = request.POST.get('lock_type') # Value from RadioButtonList1 (0, 1, 2)
            if lock_type_str == "":
                messages.error(request, "Invalid data input. Please select a lock type (PR/SPR/PO).")
                return HttpResponse(status=200, headers={'HX-Trigger': 'showMessage'}) # Return 200 for message display
            
            lock_type = int(lock_type_str)
            if lock_type not in [RateLockUnlock.TYPE_PR, RateLockUnlock.TYPE_SPR, RateLockUnlock.TYPE_PO]:
                raise ValueError("Invalid lock type value.")

            # In a real ERP, comp_id and fin_year_id would be derived from user session or system context
            current_comp_id = 1 # Placeholder for Session["compid"]
            current_fin_year_id = 2023 # Placeholder for Session["finyear"]

            item.set_lock_status(
                user=request.user,
                comp_id=current_comp_id,
                fin_year_id=current_fin_year_id,
                lock_status=RateLockUnlock.STATUS_LOCKED, # 1 for Locked
                lock_type=lock_type
            )
            messages.success(request, f"Item {item.item_code} locked successfully for {RateLockUnlock.LOCK_TYPES[lock_type][1]}.")
        except (ValueError, TypeError) as e:
            messages.error(request, f"Error processing lock request: {e}")
            return HttpResponse(status=200, headers={'HX-Trigger': 'showMessage'})
        except Exception as e:
            messages.error(request, f"An unexpected error occurred: {e}")
            return HttpResponse(status=200, headers={'HX-Trigger': 'showMessage'})

        # Trigger a refresh of the table and display message
        return HttpResponse(
            status=204, # No Content
            headers={'HX-Trigger': '{"refreshItemRateLockList":true, "showMessage":true}'}
        )

class ItemUnlockActionView(LoginRequiredMixin, View):
    """
    Handles the 'Unlock' action (corresponds to ASP.NET LinkButton1 'Lock' which sets LockUnlock=0).
    This action creates a new RateLockUnlock record marking the item as unlocked.
    """
    def post(self, request, pk):
        item = get_object_or_404(Item, item_id=pk)

        try:
            # In a real ERP, comp_id and fin_year_id would be derived from user session or system context
            current_comp_id = 1 # Placeholder for Session["compid"]
            current_fin_year_id = 2023 # Placeholder for Session["finyear"]

            # ASP.NET LinkButton1 ('Lock') logic was to update the *existing* record's LockUnlock to 0.
            # In our fat model, we create a new log entry for the status change.
            item.set_lock_status(
                user=request.user,
                comp_id=current_comp_id,
                fin_year_id=current_fin_year_id,
                lock_status=RateLockUnlock.STATUS_UNLOCKED, # 0 for Unlocked
                lock_type=None # Type is not relevant when unlocking
            )
            messages.success(request, f"Item {item.item_code} unlocked successfully.")
        except Exception as e:
            messages.error(request, f"An unexpected error occurred: {e}")
            return HttpResponse(status=200, headers={'HX-Trigger': 'showMessage'})

        # Trigger a refresh of the table and display message
        return HttpResponse(
            status=204, # No Content
            headers={'HX-Trigger': '{"refreshItemRateLockList":true, "showMessage":true}'}
        )

# Helper view for dynamic category dropdown (if needed separately, not strictly necessary with HTMX re-render of _table)
class CategoryDropdownPartialView(View):
    """
    This view can be used to dynamically load category options based on type_filter.
    However, current HTMX setup reloads the entire table, so this is optional.
    """
    def get(self, request):
        comp_id = 1 # Placeholder
        categories = Category.objects.filter(comp_id=comp_id).order_by('cname')
        options_html = '<option value="Select">Select</option>'
        for cat in categories:
            options_html += f'<option value="{cat.cid}">{cat.symbol}-{cat.cname}</option>'
        return HttpResponse(options_html)

```

#### 4.4 Templates (`material_management/templates/material_management/itemratelock/`)

##### `list.html` (Main Page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <h2 class="text-2xl font-bold text-gray-800 mb-4 sm:mb-0">Rate Lock-Unlock</h2>
    </div>

    <!-- Filter/Search Bar -->
    <div class="bg-white p-4 shadow rounded-lg mb-6" x-data="{ typeFilter: '{{ filter_form.type_filter.value }}' }">
        <div class="flex flex-wrap items-center space-x-2 space-y-2 sm:space-y-0"
             hx-target="#itemRateLockTable-container"
             hx-indicator="#loadingIndicator"
             hx-swap="innerHTML">
            
            <label for="{{ filter_form.type_filter.id_for_label }}" class="text-sm font-medium text-gray-700 sr-only">Type:</label>
            {{ filter_form.type_filter.tag | safe }}

            <label for="{{ filter_form.category_filter.id_for_label }}" class="text-sm font-medium text-gray-700 sr-only">Category:</label>
            <span x-show="typeFilter === 'Category'">
                {{ filter_form.category_filter.tag | safe }}
            </span>

            <label for="{{ filter_form.search_by.id_for_label }}" class="text-sm font-medium text-gray-700 sr-only">Search By:</label>
            <span x-show="typeFilter !== 'Select'">
                 {{ filter_form.search_by.tag | safe }}
            </span>

            <label for="{{ filter_form.search_query.id_for_label }}" class="text-sm font-medium text-gray-700 sr-only">Search Query:</label>
            <span x-show="typeFilter !== 'Select'">
                {{ filter_form.search_query.tag | safe }}
            </span>
            
            <button 
                id="btnSearch"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-sm redbox"
                hx-get="{% url 'material_management:item_ratelock_table_partial' %}"
                hx-target="#itemRateLockTable-container"
                hx-indicator="#loadingIndicator"
                hx-swap="innerHTML"
                hx-vals="js:{
                    type_filter: document.getElementById('id_type_filter').value,
                    category_filter: document.getElementById('id_category_filter').value,
                    search_by: document.getElementById('id_search_by').value,
                    search_query: document.getElementById('id_search_query').value
                }"
                hx-trigger="click">
                Search
            </button>
        </div>
        <div id="loadingIndicator" class="htmx-indicator mt-4 text-center text-blue-600">
            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <p>Loading items...</p>
        </div>
    </div>
    
    <!-- Item List Table Container -->
    <div id="itemRateLockTable-container"
         hx-trigger="load, refreshItemRateLockList from:body"
         hx-get="{% url 'material_management:item_ratelock_table_partial' %}"
         hx-indicator="#loadingIndicator"
         hx-swap="innerHTML">
        <!-- Initial load message, then replaced by table partial -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading...</p>
        </div>
    </div>
    
    <!-- Global Message Display (for Django messages) -->
    <div id="message-container" x-data="{ show: false, message: '' }" 
         x-init="$watch('show', value => { if (value) { setTimeout(() => show = false, 3000) } });
                 htmx.on('showMessage', (evt) => { 
                     message = evt.detail.value; 
                     show = true; 
                     // Clear messages after display to prevent re-triggering
                     setTimeout(() => { if (typeof Alpine !== 'undefined' && Alpine.raw(evt.target.$data).message) Alpine.raw(evt.target.$data).message = ''; }, 3000); 
                 });"
         class="fixed bottom-4 right-4 z-50">
        {% if messages %}
            {% for message in messages %}
            <div x-show="show || true" x-transition:enter="transition ease-out duration-300" 
                 x-transition:enter-start="opacity-0 transform translate-y-2" 
                 x-transition:enter-end="opacity-100 transform translate-y-0"
                 x-transition:leave="transition ease-in duration-300" 
                 x-transition:leave-start="opacity-100 transform translate-y-0" 
                 x-transition:leave-end="opacity-0 transform translate-y-2"
                 class="p-3 mb-2 rounded-md shadow-md text-white
                        {% if message.tags == 'success' %}bg-green-500{% elif message.tags == 'error' %}bg-red-500{% elif message.tags == 'warning' %}bg-yellow-500{% else %}bg-blue-500{% endif %}"
                 role="alert">
                {{ message }}
            </div>
            {% endfor %}
        {% endif %}
    </div>

</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js component initialization for filter form visibility
    document.addEventListener('alpine:init', () => {
        Alpine.data('filterForm', () => ({
            typeFilter: '{{ filter_form.type_filter.value }}',
            init() {
                // Ensure initial visibility is set correctly based on `typeFilter`
                this.$watch('typeFilter', value => {
                    if (value === 'Select') {
                        document.getElementById('id_category_filter').style.display = 'none';
                        document.getElementById('id_search_by').style.display = 'none';
                        document.getElementById('id_search_query').style.display = 'none';
                    } else {
                        document.getElementById('id_search_by').style.display = '';
                        document.getElementById('id_search_query').style.display = '';
                        if (value === 'Category') {
                            document.getElementById('id_category_filter').style.display = '';
                        } else {
                            document.getElementById('id_category_filter').style.display = 'none';
                        }
                    }
                });
                // Call it once on init
                this.typeFilter = document.getElementById('id_type_filter').value;
            }
        }));
    });

    // Custom HTMX event listener for messages (if not handled by Alpine directly)
    document.body.addEventListener('showMessage', function(evt) {
        if (evt.detail && evt.detail.value) {
            // This assumes a global Alpine message component or simple alert
            alert(evt.detail.value);
        }
    });

    // Helper for DataTables initialization
    function initializeDataTable() {
        if ($.fn.DataTable.isDataTable('#itemRateLockTable')) {
            $('#itemRateLockTable').DataTable().destroy();
        }
        $('#itemRateLockTable').DataTable({
            "pageLength": 20, // Matches ASP.NET PageSize
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [5, 6] } // Disable ordering on Unlock For and Actions
            ]
        });
    }

    // HTMX lifecycle event after a swap happens on #itemRateLockTable-container
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.target.id === 'itemRateLockTable-container') {
            initializeDataTable();
        }
    });

    // Initial DataTables setup on page load if the table is already present (e.g., non-HTMX first load)
    document.addEventListener('DOMContentLoaded', function() {
        if (document.getElementById('itemRateLockTable')) {
            initializeDataTable();
        }
    });

    // Global HTMX trigger for messages - ensures messages pop up even from 204 responses
    htmx.on('htmx:trigger', function(evt) {
        if (evt.detail.trigger === 'showMessage') {
            const msgContainer = document.getElementById('message-container');
            if (msgContainer && msgContainer.__alpine && evt.detail.value) {
                // If Alpine is managing it, set the message directly
                msgContainer.__alpine.$data.message = evt.detail.value;
                msgContainer.__alpine.$data.show = true;
            } else if (evt.detail.value) {
                // Fallback for simple alerts
                alert(evt.detail.value);
            }
        }
    });
</script>
{% endblock %}
```

##### `_table.html` (DataTables Partial)

```html
<div class="overflow-x-auto relative shadow-md sm:rounded-lg">
    {% if items %}
    <table id="itemRateLockTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unlock For</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for item in items %}
            {% with current_lock=item.current_rate_lock %}
            <tr hx-swap-oob="true"> {# OOB swap not used here, but good practice for per-row updates if needed #}
                <td class="py-2 px-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ item.item_code }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ item.manf_desc }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ item.uom_basic }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">
                    <div x-data="{ radioDisabled: {% if current_lock and current_lock.lock_unlock == current_lock.STATUS_LOCKED %}true{% else %}false{% endif %} }">
                        <label class="inline-flex items-center">
                            <input type="radio" name="lock_type_{{ item.item_id }}" value="{{ RateLockUnlock.TYPE_PR }}"
                                   {% if current_lock and current_lock.lock_unlock == current_lock.STATUS_LOCKED and current_lock.type == current_lock.TYPE_PR %}checked{% endif %}
                                   {% if current_lock and current_lock.lock_unlock == current_lock.STATUS_LOCKED %}disabled{% endif %}
                                   class="form-radio text-indigo-600 h-4 w-4">
                            <span class="ml-1 text-xs">PR</span>
                        </label>
                        <label class="inline-flex items-center ml-2">
                            <input type="radio" name="lock_type_{{ item.item_id }}" value="{{ RateLockUnlock.TYPE_SPR }}"
                                   {% if current_lock and current_lock.lock_unlock == current_lock.STATUS_LOCKED and current_lock.type == current_lock.TYPE_SPR %}checked{% endif %}
                                   {% if current_lock and current_lock.lock_unlock == current_lock.STATUS_LOCKED %}disabled{% endif %}
                                   class="form-radio text-indigo-600 h-4 w-4">
                            <span class="ml-1 text-xs">SPR</span>
                        </label>
                        <label class="inline-flex items-center ml-2">
                            <input type="radio" name="lock_type_{{ item.item_id }}" value="{{ RateLockUnlock.TYPE_PO }}"
                                   {% if current_lock and current_lock.lock_unlock == current_lock.STATUS_LOCKED and current_lock.type == current_lock.TYPE_PO %}checked{% endif %}
                                   {% if current_lock and current_lock.lock_unlock == current_lock.STATUS_LOCKED %}disabled{% endif %}
                                   class="form-radio text-indigo-600 h-4 w-4">
                            <span class="ml-1 text-xs">PO</span>
                        </label>
                    </div>
                </td>
                <td class="py-2 px-4 whitespace-nowrap text-sm font-medium">
                    {# ASP.NET 'Unlock' button corresponds to Django 'Lock' (sets LockUnlock=1) #}
                    {# ASP.NET 'Lock' button corresponds to Django 'Unlock' (sets LockUnlock=0) #}
                    
                    {% if current_lock and current_lock.lock_unlock == current_lock.STATUS_LOCKED %}
                        <button 
                            class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs"
                            hx-post="{% url 'material_management:item_unlock' item.item_id %}"
                            hx-confirm="Are you sure you want to UNLOCK this item?"
                            hx-indicator="#loadingIndicator"
                            hx-swap="none">
                            Unlock
                        </button>
                    {% else %}
                        <button 
                            class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded text-xs"
                            hx-post="{% url 'material_management:item_lock' item.item_id %}"
                            hx-confirm="Are you sure you want to LOCK this item? Please ensure a type is selected."
                            hx-vals="js:{ lock_type: document.querySelector('input[name=lock_type_{{ item.item_id }}]:checked') ? document.querySelector('input[name=lock_type_{{ item.item_id }}]:checked').value : '' }"
                            hx-indicator="#loadingIndicator"
                            hx-swap="none">
                            Lock
                        </button>
                    {% endif %}
                </td>
            </tr>
            {% endwith %}
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <div class="text-center py-8">
        <p class="font-bold text-lg text-red-700">No data to display !</p>
        {% if messages %}
            {# Display messages here if they were set in the view #}
            {% for message in messages %}
                <p class="text-red-500 text-sm mt-2">{{ message }}</p>
            {% endfor %}
        {% endif %}
    </div>
    {% endif %}
</div>

{# This script will be executed after HTMX swaps the content #}
<script>
    // DataTables initialization handled by htmx:afterSwap in list.html
    // For standalone testing of this partial, uncomment below:
    // $(document).ready(function() {
    //     if ($.fn.DataTable.isDataTable('#itemRateLockTable')) {
    //         $('#itemRateLockTable').DataTable().destroy();
    //     }
    //     $('#itemRateLockTable').DataTable({
    //         "pageLength": 20,
    //         "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]]
    //     });
    // });
</script>
```

#### 4.5 URLs (`material_management/urls.py`)

```python
from django.urls import path
from .views import (
    ItemRateLockListView,
    ItemRateLockTablePartialView,
    ItemLockActionView,
    ItemUnlockActionView,
    # CategoryDropdownPartialView # Only if needed separately
)

app_name = 'material_management'

urlpatterns = [
    path('ratelock/', ItemRateLockListView.as_view(), name='item_ratelock_list'),
    path('ratelock/table/', ItemRateLockTablePartialView.as_view(), name='item_ratelock_table_partial'),
    path('ratelock/lock/<int:pk>/', ItemLockActionView.as_view(), name='item_lock'), # Corresponds to ASP.NET btnsel 'Unlock'
    path('ratelock/unlock/<int:pk>/', ItemUnlockActionView.as_view(), name='item_unlock'), # Corresponds to ASP.NET LinkButton1 'Lock'
    # path('ratelock/categories_partial/', CategoryDropdownPartialView.as_view(), name='category_dropdown_partial'),
]

```

#### 4.6 Tests (`material_management/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.utils import timezone
from .models import Item, Category, RateLockUnlock

User = get_user_model()

class RateLockUnlockModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a test user for session_id/locked_by_transaction
        cls.user = User.objects.create_user(username='testuser', password='password123')
        
        # Create test Category
        cls.category = Category.objects.create(cid=101, symbol='CAT', cname='Test Category', comp_id=1)
        
        # Create test Items
        cls.item1 = Item.objects.create(
            item_id=1, item_code='ITEM001', manf_desc='Test Item One', uom_basic='PCS',
            category=cls.category, comp_id=1, fin_year_id=2023
        )
        cls.item2 = Item.objects.create(
            item_id=2, item_code='ITEM002', manf_desc='Test Item Two', uom_basic='KG',
            category=cls.category, comp_id=1, fin_year_id=2023
        )
        cls.item3 = Item.objects.create(
            item_id=3, item_code='ITEM003', manf_desc='Another Item', uom_basic='M',
            category=cls.category, comp_id=1, fin_year_id=2023
        )

        # Create initial RateLockUnlock entries for testing
        RateLockUnlock.objects.create(
            id=1, item=cls.item1, sys_date=timezone.localdate(), sys_time=timezone.localtime().time(),
            session_id='admin', comp_id=1, fin_year_id=2023,
            type=RateLockUnlock.TYPE_PR, lock_unlock=RateLockUnlock.STATUS_LOCKED,
            locked_by_transaction='admin', lock_date=timezone.localdate(), lock_time=timezone.localtime().time()
        )
        # Item 2 is initially unlocked by having a latest status of UNLOCKED
        RateLockUnlock.objects.create(
            id=2, item=cls.item2, sys_date=timezone.localdate(), sys_time=timezone.localtime().time(),
            session_id='admin', comp_id=1, fin_year_id=2023,
            type=None, lock_unlock=RateLockUnlock.STATUS_UNLOCKED,
            locked_by_transaction='admin', lock_date=timezone.localdate(), lock_time=timezone.localtime().time()
        )

    def test_category_creation(self):
        self.assertEqual(self.category.cname, 'Test Category')
        self.assertEqual(str(self.category), 'CAT-Test Category')

    def test_item_creation(self):
        self.assertEqual(self.item1.item_code, 'ITEM001')
        self.assertEqual(self.item1.category, self.category)
        self.assertEqual(str(self.item1), 'ITEM001 - Test Item One')

    def test_item_current_rate_lock_property(self):
        lock_status_item1 = self.item1.current_rate_lock
        self.assertIsNotNone(lock_status_item1)
        self.assertEqual(lock_status_item1.lock_unlock, RateLockUnlock.STATUS_LOCKED)
        self.assertEqual(lock_status_item1.item, self.item1)

        lock_status_item2 = self.item2.current_rate_lock
        self.assertIsNotNone(lock_status_item2)
        self.assertEqual(lock_status_item2.lock_unlock, RateLockUnlock.STATUS_UNLOCKED)
        self.assertEqual(lock_status_item2.item, self.item2)

        # Item 3 has no lock history
        self.assertIsNone(self.item3.current_rate_lock)

    def test_item_set_lock_status_lock(self):
        initial_count = RateLockUnlock.objects.filter(item=self.item3).count()
        self.item3.set_lock_status(
            user=self.user,
            comp_id=1,
            fin_year_id=2023,
            lock_status=RateLockUnlock.STATUS_LOCKED,
            lock_type=RateLockUnlock.TYPE_PO
        )
        self.assertEqual(RateLockUnlock.objects.filter(item=self.item3).count(), initial_count + 1)
        new_lock = self.item3.current_rate_lock
        self.assertEqual(new_lock.lock_unlock, RateLockUnlock.STATUS_LOCKED)
        self.assertEqual(new_lock.type, RateLockUnlock.TYPE_PO)
        self.assertEqual(new_lock.session_id, self.user.username)

    def test_item_set_lock_status_unlock(self):
        initial_count = RateLockUnlock.objects.filter(item=self.item1).count()
        self.item1.set_lock_status(
            user=self.user,
            comp_id=1,
            fin_year_id=2023,
            lock_status=RateLockUnlock.STATUS_UNLOCKED
        )
        self.assertEqual(RateLockUnlock.objects.filter(item=self.item1).count(), initial_count + 1)
        new_unlock = self.item1.current_rate_lock
        self.assertEqual(new_unlock.lock_unlock, RateLockUnlock.STATUS_UNLOCKED)
        self.assertIsNone(new_unlock.type) # Type should be None when unlocking

    def test_item_set_lock_status_missing_type_for_lock(self):
        with self.assertRaises(ValueError):
            self.item3.set_lock_status(
                user=self.user,
                comp_id=1,
                fin_year_id=2023,
                lock_status=RateLockUnlock.STATUS_LOCKED,
                lock_type=None # Missing type
            )

    def test_item_manager_get_items_with_lock_status(self):
        # Test basic retrieval
        items = Item.objects.get_items_with_lock_status(comp_id=1, fin_year_id=2023)
        self.assertEqual(items.count(), 3)

        # Test Category filter
        items_cat = Item.objects.get_items_with_lock_status(
            comp_id=1, fin_year_id=2023, type_filter='Category', category_id=self.category.cid
        )
        self.assertEqual(items_cat.count(), 3) # All items are in this category

        # Test search by Item Code
        items_search_code = Item.objects.get_items_with_lock_status(
            comp_id=1, fin_year_id=2023, type_filter='Category', category_id=self.category.cid,
            search_by='tblDG_Item_Master.ItemCode', search_query='ITEM001'
        )
        self.assertEqual(items_search_code.count(), 1)
        self.assertEqual(items_search_code.first(), self.item1)

        # Test search by Description
        items_search_desc = Item.objects.get_items_with_lock_status(
            comp_id=1, fin_year_id=2023, type_filter='WOItems', # type_filter 'WOItems' should not break search
            search_by='tblDG_Item_Master.ManfDesc', search_query='Two'
        )
        self.assertEqual(items_search_desc.count(), 1)
        self.assertEqual(items_search_desc.first(), self.item2)

        # Test 'Select' type_filter
        items_select_type = Item.objects.get_items_with_lock_status(
            comp_id=1, fin_year_id=2023, type_filter='Select'
        )
        self.assertEqual(items_select_type.count(), 0) # Should return no items as per ASP.NET logic

class RateLockUnlockViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.user = User.objects.create_user(username='viewer', password='viewerpassword')
        cls.admin_user = User.objects.create_user(username='admin', password='adminpassword', is_staff=True, is_superuser=True)
        
        cls.category = Category.objects.create(cid=201, symbol='TESTCAT', cname='Test Category', comp_id=1)
        cls.item = Item.objects.create(
            item_id=10, item_code='TESTITEM', manf_desc='Test Item for Views', uom_basic='EA',
            category=cls.category, comp_id=1, fin_year_id=2023
        )
        # Ensure item is initially unlocked for testing lock functionality
        RateLockUnlock.objects.create(
            id=10, item=cls.item, sys_date=timezone.localdate(), sys_time=timezone.localtime().time(),
            session_id='initial', comp_id=1, fin_year_id=2023,
            type=None, lock_unlock=RateLockUnlock.STATUS_UNLOCKED,
            locked_by_transaction='initial', lock_date=timezone.localdate(), lock_time=timezone.localtime().time()
        )

    def setUp(self):
        self.client = Client()
        self.client.login(username='admin', password='adminpassword') # Login as admin for actions

    def test_item_ratelock_list_view_get(self):
        response = self.client.get(reverse('material_management:item_ratelock_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/itemratelock/list.html')
        self.assertIn('filter_form', response.context)

    def test_item_ratelock_table_partial_view_get_initial(self):
        response = self.client.get(reverse('material_management:item_ratelock_table_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/itemratelock/_table.html')
        # Check if items are loaded correctly (none if default 'Select' type)
        self.assertIn('items', response.context)
        self.assertEqual(response.context['items'].count(), 0) # Default 'Select' type_filter

        # Test with a valid filter
        response = self.client.get(reverse('material_management:item_ratelock_table_partial'), {
            'type_filter': 'Category',
            'category_filter': self.category.cid
        })
        self.assertEqual(response.status_code, 200)
        self.assertIn('items', response.context)
        self.assertGreater(response.context['items'].count(), 0)
        self.assertEqual(response.context['items'].first().item_id, self.item.item_id)

    def test_item_lock_action_view_post_success(self):
        initial_lock_count = RateLockUnlock.objects.filter(item=self.item).count()
        response = self.client.post(reverse('material_management:item_lock', args=[self.item.item_id]), 
                                     {'lock_type': RateLockUnlock.TYPE_PO}, 
                                     HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HTMX success: No Content
        self.assertEqual(RateLockUnlock.objects.filter(item=self.item).count(), initial_lock_count + 1)
        self.assertEqual(self.item.current_rate_lock.lock_unlock, RateLockUnlock.STATUS_LOCKED)
        self.assertEqual(self.item.current_rate_lock.type, RateLockUnlock.TYPE_PO)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshItemRateLockList', response.headers['HX-Trigger'])
        self.assertIn('showMessage', response.headers['HX-Trigger'])

    def test_item_lock_action_view_post_missing_type(self):
        response = self.client.post(reverse('material_management:item_lock', args=[self.item.item_id]), 
                                     {'lock_type': ''}, # Missing type value
                                     HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200) # Should return 200 with messages
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('showMessage', response.headers['HX-Trigger']) # Should trigger a message
        self.assertContains(response, 'Invalid data input. Please select a lock type') # Check message content


    def test_item_unlock_action_view_post_success(self):
        # First, ensure the item is locked to test unlock
        self.item.set_lock_status(
            user=self.admin_user, comp_id=1, fin_year_id=2023,
            lock_status=RateLockUnlock.STATUS_LOCKED, lock_type=RateLockUnlock.TYPE_PR
        )
        self.assertEqual(self.item.current_rate_lock.lock_unlock, RateLockUnlock.STATUS_LOCKED)

        initial_unlock_count = RateLockUnlock.objects.filter(item=self.item).count()
        response = self.client.post(reverse('material_management:item_unlock', args=[self.item.item_id]), {}, 
                                      HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertEqual(RateLockUnlock.objects.filter(item=self.item).count(), initial_unlock_count + 1)
        self.assertEqual(self.item.current_rate_lock.lock_unlock, RateLockUnlock.STATUS_UNLOCKED)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshItemRateLockList', response.headers['HX-Trigger'])
        self.assertIn('showMessage', response.headers['HX-Trigger'])
    
    def test_auth_required_views(self):
        self.client.logout()
        response = self.client.get(reverse('material_management:item_ratelock_list'))
        self.assertRedirects(response, f'{reverse(settings.LOGIN_URL)}?next={reverse("material_management:item_ratelock_list")}')

        response = self.client.post(reverse('material_management:item_lock', args=[self.item.item_id]))
        self.assertRedirects(response, f'{reverse(settings.LOGIN_URL)}?next={reverse("material_management:item_lock", args=[self.item.item_id])}')

```

### Step 5: HTMX and Alpine.js Integration

*   **Dynamic Filtering/Search:**
    *   The `RateLockUnlockFilterForm` fields (`type_filter`, `category_filter`, `search_by`) have `hx-get` and `hx-trigger="change"` attributes pointing to `item_ratelock_table_partial`. This means any change in these dropdowns will automatically re-fetch and re-render the item table without a full page refresh.
    *   The "Search" button explicitly triggers `hx-get` to the partial view, passing all current filter values using `hx-vals="js:{...}"`.
*   **Live Table Updates:**
    *   The `itemRateLockTable-container` `div` in `list.html` uses `hx-trigger="load, refreshItemRateLockList from:body"`. This ensures the table is loaded on initial page load and reloaded whenever the `refreshItemRateLockList` custom event is triggered (after a Lock/Unlock action).
    *   The `hx-target` and `hx-swap="innerHTML"` attributes ensure only the table content is updated, not the entire page.
*   **Lock/Unlock Actions:**
    *   The "Lock" and "Unlock" buttons in `_table.html` use `hx-post` to `item_lock` and `item_unlock` URLs respectively.
    *   `hx-confirm` is used for client-side confirmation dialogs.
    *   `hx-swap="none"` and `HX-Trigger` headers in the Django views (e.g., `{"refreshItemRateLockList":true, "showMessage":true}`) are used to trigger a refresh of the list and display a message without modifying the current DOM element.
*   **Loading Indicator:** An `htmx-indicator` (`#loadingIndicator`) is used to provide visual feedback during HTMX requests.
*   **Alpine.js for UI State:**
    *   `x-data` and `x-show` are used in `list.html` to control the visibility of `category_filter`, `search_by`, and `search_query` dropdowns/textbox based on the `type_filter` selection, mimicking the ASP.NET `Visible` property.
    *   A global Alpine.js component on `#message-container` handles displaying Django `messages` and fading them out, providing a modern, non-blocking notification system.
    *   Radio button disabling in `_table.html` is managed by Alpine.js `x-data` binding.
*   **DataTables:**
    *   The `_table.html` includes a `<table id="itemRateLockTable">` which is initialized by DataTables via a JavaScript function `initializeDataTable()`.
    *   This function is called by HTMX's `htmx:afterSwap` event listener in `list.html`, ensuring DataTables is re-initialized correctly after the table content is updated via HTMX.
    *   `pageLength` and `lengthMenu` are set to match the ASP.NET `PageSize` and provide user options.

### Final Notes

*   **Placeholders:** Replace `current_comp_id = 1` and `current_fin_year_id = 2023` with actual values derived from your Django application's session or user profile logic.
*   **Database Schema:** The `managed = False` setting in models is critical for connecting to an existing database without Django attempting to create/modify tables. Ensure the `db_table` and `db_column` mappings are accurate.
*   **Stored Procedures:** The `GetRateLockUnlockItem` stored procedure's logic has been approximated in `ItemManager.get_items_with_lock_status`. For a complete migration, the exact SQL logic of the stored procedure would need to be translated to Django ORM queries or raw SQL queries (`Item.objects.raw()`).
*   **Authentication:** `LoginRequiredMixin` has been added to views, assuming a standard Django authentication setup.
*   **CSS:** The provided CSS classes are basic Tailwind. You would integrate full Tailwind CSS setup and possibly `daisyUI` or custom components for a more polished look matching the original application's theme.
*   **Error Handling:** Basic `try-except` blocks are included in views. Robust error logging and user-friendly error pages should be implemented.
*   **Test Coverage:** Ensure the tests are expanded to cover edge cases, form validation, and all possible interaction paths to achieve at least 80% coverage.
*   **Security:** Always sanitize user input, use CSRF protection (Django forms handle this automatically), and follow secure coding practices.