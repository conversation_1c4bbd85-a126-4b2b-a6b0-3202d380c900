## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Initial Analysis of ASP.NET Code:

The provided ASP.NET code (`.aspx` and `.aspx.cs` files) consists solely of an empty page structure and an empty `Page_Load` method. It does not contain any explicit UI controls, database connections, data sources, or business logic.

Given this limitation, we will infer a common scenario for a "PO Approve Dashboard" (Purchase Order Approval Dashboard) and proceed with a comprehensive Django modernization plan based on these inferences. This approach demonstrates how the automation tool can generate a full feature set even with minimal starting information, by leveraging common patterns associated with the given page title.

For this example, we will assume the dashboard manages `PurchaseOrder` records, allowing users to view, approve, reject, and potentially manage (create/edit/delete) these orders.

## Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
Since the provided ASP.NET code does not explicitly define database interactions, we infer a database table named `tblPurchaseOrder` based on the page title "PO_Approve_Dashboard".

**Inferred Database Table and Columns:**

*   **[TABLE_NAME]:** `tblPurchaseOrder`
*   **Columns:**
    *   `Id` (Primary Key, integer)
    *   `PoNumber` (string, e.g., 'PO-2023-001')
    *   `SupplierName` (string)
    *   `OrderDate` (date)
    *   `Amount` (decimal)
    *   `Status` (string, e.g., 'Pending', 'Approved', 'Rejected')
    *   `ApproverName` (string, nullable)
    *   `ApprovalDate` (date, nullable)
    *   `Remarks` (string, nullable)

## Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
As no specific functionality was found in the ASP.NET code-behind, we infer the following standard functionalities for a Purchase Order Approval Dashboard:

*   **Read:** Display a list of all purchase orders, with specific filters for 'Pending' orders on the dashboard.
*   **Update:**
    *   Ability to update general details of a purchase order.
    *   Specific action to "Approve" a purchase order, changing its status and recording approver details.
    *   Specific action to "Reject" a purchase order, changing its status and allowing remarks.
*   **Create:** Ability to add new purchase orders to the system.
*   **Delete:** Ability to remove purchase orders.

Validation logic will be inferred for required fields during creation and update.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
Based on the dashboard nature and inferred functionality, we will implement the following Django UI components:

*   **Dashboard View:** A main page presenting a `DataTables`-enabled list of `PurchaseOrder` records, allowing for client-side search, sort, and pagination.
*   **Action Buttons:**
    *   "Add New Purchase Order" button to open a modal for creation.
    *   "Edit" button per row to open a modal for updating details.
    *   "Approve" button per row to initiate the approval process (potentially via a confirmation modal).
    *   "Reject" button per row to initiate the rejection process (potentially via a confirmation modal with remarks input).
    *   "Delete" button per row to open a modal for deletion confirmation.
*   **Modal Forms:** HTMX-loaded partial forms for "Add/Edit Purchase Order," "Approve Confirmation," "Reject Confirmation," and "Delete Confirmation."

## Step 4: Generate Django Code

### 4.1 Models (`purchase_orders/models.py`)

This file will define the `PurchaseOrder` model, mapping to the inferred `tblPurchaseOrder` database table. It will include methods for business logic, such as `approve_order` and `reject_order`.

```python
from django.db import models
from django.utils import timezone

class PurchaseOrder(models.Model):
    # Primary key (assuming Id is automatically handled by Django or manually defined if needed)
    # Django will automatically create an 'id' primary key if not specified, 
    # which is usually sufficient for existing tables with a primary key.
    
    po_number = models.CharField(db_column='PoNumber', max_length=50, unique=True, verbose_name='PO Number')
    supplier_name = models.CharField(db_column='SupplierName', max_length=255, verbose_name='Supplier Name')
    order_date = models.DateField(db_column='OrderDate', verbose_name='Order Date')
    amount = models.DecimalField(db_column='Amount', max_digits=10, decimal_places=2, verbose_name='Amount')
    status = models.CharField(db_column='Status', max_length=50, default='Pending', 
                              choices=[('Pending', 'Pending'), ('Approved', 'Approved'), ('Rejected', 'Rejected')], verbose_name='Status')
    approver_name = models.CharField(db_column='ApproverName', max_length=255, blank=True, null=True, verbose_name='Approver Name')
    approval_date = models.DateField(db_column='ApprovalDate', blank=True, null=True, verbose_name='Approval Date')
    remarks = models.TextField(db_column='Remarks', blank=True, null=True, verbose_name='Remarks')

    class Meta:
        managed = False  # Important: Tells Django not to manage table creation/deletion
        db_table = 'tblPurchaseOrder'  # Map to the inferred existing table
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'
        ordering = ['-order_date', 'po_number'] # Default ordering for lists

    def __str__(self):
        return f"{self.po_number} - {self.supplier_name}"

    # Business logic methods (Fat Model)
    def approve_order(self, approver_name):
        """Approves the purchase order, setting status, approver, and approval date."""
        if self.status == 'Pending':
            self.status = 'Approved'
            self.approver_name = approver_name
            self.approval_date = timezone.now().date()
            self.save()
            return True
        return False # Already approved or rejected

    def reject_order(self, approver_name, remarks):
        """Rejects the purchase order, setting status, approver, approval date, and remarks."""
        if self.status == 'Pending':
            self.status = 'Rejected'
            self.approver_name = approver_name
            self.approval_date = timezone.now().date()
            self.remarks = remarks
            self.save()
            return True
        return False # Already approved or rejected

    def is_pending(self):
        """Checks if the order is in pending status."""
        return self.status == 'Pending'
```

### 4.2 Forms (`purchase_orders/forms.py`)

This file will define the `PurchaseOrderForm` for handling input when creating or updating `PurchaseOrder` records, along with a specific form for approval remarks.

```python
from django import forms
from .models import PurchaseOrder

class PurchaseOrderForm(forms.ModelForm):
    class Meta:
        model = PurchaseOrder
        fields = ['po_number', 'supplier_name', 'order_date', 'amount', 'remarks']
        widgets = {
            'po_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'supplier_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'order_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'remarks': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            # Status, ApproverName, ApprovalDate are typically handled by logic, not directly in form
        }

    # Add custom validation if needed, e.g., to ensure PO number format
    def clean_po_number(self):
        po_number = self.cleaned_data['po_number']
        # Example: Ensure PO number starts with 'PO-'
        if not po_number.startswith('PO-'):
            raise forms.ValidationError("PO Number must start with 'PO-'.")
        return po_number

class PurchaseOrderRejectForm(forms.Form):
    remarks = forms.CharField(
        widget=forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 4}),
        label="Rejection Remarks",
        required=True
    )
```

### 4.3 Views (`purchase_orders/views.py`)

This file will contain the Class-Based Views (CBVs) for handling all CRUD operations and specific approval/rejection actions. Views are kept thin, delegating business logic to the model.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin # Recommended for production systems

from .models import PurchaseOrder
from .forms import PurchaseOrderForm, PurchaseOrderRejectForm

# Helper for HTMX responses
def htmx_response(request, success_message, trigger_event='refreshPurchaseOrderList'):
    messages.success(request, success_message)
    if request.headers.get('HX-Request'):
        return HttpResponse(
            status=204,
            headers={
                'HX-Trigger': trigger_event
            }
        )
    return HttpResponseRedirect(reverse_lazy('purchaseorder_list'))

class PurchaseOrderListView(ListView):
    model = PurchaseOrder
    template_name = 'purchase_orders/purchaseorder/list.html'
    context_object_name = 'purchase_orders'

    def get_queryset(self):
        # Example: Show only 'Pending' orders by default on dashboard
        return PurchaseOrder.objects.filter(status='Pending')

class PurchaseOrderTablePartialView(ListView):
    model = PurchaseOrder
    template_name = 'purchase_orders/purchaseorder/_purchaseorder_table.html'
    context_object_name = 'purchase_orders'

    def get_queryset(self):
        # This view provides the full list for the DataTable
        return PurchaseOrder.objects.all()

class PurchaseOrderCreateView(CreateView):
    model = PurchaseOrder
    form_class = PurchaseOrderForm
    template_name = 'purchase_orders/purchaseorder/_purchaseorder_form.html'
    success_url = reverse_lazy('purchaseorder_list') # Not strictly used with HTMX, but good practice

    def form_valid(self, form):
        response = super().form_valid(form)
        return htmx_response(self.request, 'Purchase Order added successfully.')

class PurchaseOrderUpdateView(UpdateView):
    model = PurchaseOrder
    form_class = PurchaseOrderForm
    template_name = 'purchase_orders/purchaseorder/_purchaseorder_form.html'
    success_url = reverse_lazy('purchaseorder_list') # Not strictly used with HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        return htmx_response(self.request, 'Purchase Order updated successfully.')

class PurchaseOrderDeleteView(DeleteView):
    model = PurchaseOrder
    template_name = 'purchase_orders/purchaseorder/_purchaseorder_confirm_delete.html'
    success_url = reverse_lazy('purchaseorder_list') # Not strictly used with HTMX

    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        success_message = f'Purchase Order "{self.object.po_number}" deleted successfully.'
        response = super().delete(request, *args, **kwargs)
        return htmx_response(request, success_message)

class PurchaseOrderApproveView(View):
    def get(self, request, pk):
        purchase_order = get_object_or_404(PurchaseOrder, pk=pk)
        return HttpResponse(f"""
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Approval</h3>
                <p class="mb-4">Are you sure you want to approve Purchase Order: <strong>{purchase_order.po_number} - {purchase_order.supplier_name}</strong>?</p>
                <div class="mt-6 flex items-center justify-end space-x-4">
                    <button type="button" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                            _="on click remove .is-active from #modal">
                        Cancel
                    </button>
                    <button type="button" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
                            hx-post="{reverse_lazy('purchaseorder_approve', args=[pk])}"
                            hx-swap="none">
                        Approve
                    </button>
                </div>
            </div>
        """)

    def post(self, request, pk):
        purchase_order = get_object_or_404(PurchaseOrder, pk=pk)
        # In a real app, 'request.user.get_full_name()' or similar would be used
        approver_name = request.user.username if request.user.is_authenticated else "System Approver" 
        
        if purchase_order.approve_order(approver_name):
            return htmx_response(request, f'Purchase Order "{purchase_order.po_number}" approved successfully.')
        else:
            messages.error(request, f'Purchase Order "{purchase_order.po_number}" could not be approved (status: {purchase_order.status}).')
            return htmx_response(request, f'Purchase Order "{purchase_order.po_number}" could not be approved.', 'none') # No refresh on error, let user see message

class PurchaseOrderRejectView(View):
    def get(self, request, pk):
        purchase_order = get_object_or_404(PurchaseOrder, pk=pk)
        form = PurchaseOrderRejectForm()
        return HttpResponse(f"""
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-5">Reject Purchase Order: {purchase_order.po_number}</h3>
                <form hx-post="{reverse_lazy('purchaseorder_reject', args=[pk])}" hx-swap="none">
                    {'{% csrf_token %}'}
                    <div class="mb-4">
                        <label for="{form.remarks.id_for_label}" class="block text-sm font-medium text-gray-700">
                            {form.remarks.label}
                        </label>
                        {form.remarks}
                        {'{% if form.remarks.errors %}'}
                        <p class="text-red-500 text-xs mt-1">{'{% for error in form.remarks.errors %}'}{'{% endfor %}'}</p>
                        {'{% endif %}'}
                    </div>
                    <div class="mt-6 flex items-center justify-end space-x-4">
                        <button type="button" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                                _="on click remove .is-active from #modal">
                            Cancel
                        </button>
                        <button type="submit" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                            Reject
                        </button>
                    </div>
                </form>
            </div>
        """)

    def post(self, request, pk):
        purchase_order = get_object_or_404(PurchaseOrder, pk=pk)
        form = PurchaseOrderRejectForm(request.POST)
        if form.is_valid():
            approver_name = request.user.username if request.user.is_authenticated else "System Approver"
            remarks = form.cleaned_data['remarks']
            if purchase_order.reject_order(approver_name, remarks):
                return htmx_response(request, f'Purchase Order "{purchase_order.po_number}" rejected successfully.')
            else:
                messages.error(request, f'Purchase Order "{purchase_order.po_number}" could not be rejected (status: {purchase_order.status}).')
                return htmx_response(request, f'Purchase Order "{purchase_order.po_number}" could not be rejected.', 'none') # No refresh on error
        else:
            # Re-render form with errors if validation fails
            # For HTMX, this usually means swapping back the form with errors
            return HttpResponse(f"""
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-5">Reject Purchase Order: {purchase_order.po_number}</h3>
                    <form hx-post="{reverse_lazy('purchaseorder_reject', args=[pk])}" hx-swap="none">
                        {'{% csrf_token %}'}
                        <div class="mb-4">
                            <label for="{form.remarks.id_for_label}" class="block text-sm font-medium text-gray-700">
                                {form.remarks.label}
                            </label>
                            {form.remarks}
                            {'{% if form.remarks.errors %}'}
                            <p class="text-red-500 text-xs mt-1">{'{% for error in form.remarks.errors %}'}{'{% endfor %}'}</p>
                            {'{% endif %}'}
                        </div>
                        <div class="mt-6 flex items-center justify-end space-x-4">
                            <button type="button" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                                    _="on click remove .is-active from #modal">
                                Cancel
                            </button>
                            <button type="submit" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                                Reject
                            </button>
                        </div>
                    </form>
                </div>
            """)

```

### 4.4 Templates

These templates use HTMX for dynamic content updates and Alpine.js for UI state management (like modal visibility). They leverage DataTables for enhanced list presentation.

#### `purchase_orders/templates/purchase_orders/purchaseorder/list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Purchase Orders Dashboard</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'purchaseorder_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Purchase Order
        </button>
    </div>
    
    <div id="purchaseorderTable-container"
         hx-trigger="load, refreshPurchaseOrderList from:body"
         hx-get="{% url 'purchaseorder_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading Purchase Orders...</p>
        </div>
    </div>
    
    <!-- Modal for forms -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on closeModal.htmx remove .is-active from me"
         x-data="{ showModal: false }"
         x-show="showModal"
         x-on:click.self="showModal = false"
         x-init="htmx.on('htmx:afterSwap', function(evt) { if (evt.target.id == 'modalContent') showModal = true; });
                 htmx.on('htmx:beforeSwap', function(evt) { if (evt.detail.xhr.status === 204) showModal = false; });
                 htmx.on('htmx:beforeRequest', function(evt) { if (evt.target.getAttribute('hx-post')) { showModal = false; } });">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"
             @click.away="showModal = false">
             <!-- Content loaded by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js component initialization
    document.addEventListener('alpine:init', () => {
        // No specific components needed beyond the modal control on this page
    });

    // Handle closing modal after HTMX success (status 204)
    document.body.addEventListener('htmx:afterOnLoad', function(evt) {
        if (evt.detail.xhr.status === 204 && evt.detail.elt.closest('#modalContent')) {
            document.getElementById('modal').classList.remove('is-active');
        }
    });

    // Custom event listener for refreshing specific parts of the page, e.g., messages
    document.body.addEventListener('refreshPurchaseOrderList', function() {
        // This event is triggered by HX-Trigger header.
        // The purchaseorderTable-container already reloads automatically via hx-trigger="refreshPurchaseOrderList from:body"
        // This is primarily for demonstration; DataTables re-initialization is handled inside _purchaseorder_table.html
    });
</script>
{% endblock %}
```

#### `purchase_orders/templates/purchase_orders/purchaseorder/_purchaseorder_table.html`

```html
<table id="purchaseorderTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO Number</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Approver</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Approval Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for po in purchase_orders %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ po.po_number }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ po.supplier_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ po.order_date|date:"Y-m-d" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ po.amount|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                    {% if po.status == 'Approved' %}bg-green-100 text-green-800
                    {% elif po.status == 'Rejected' %}bg-red-100 text-red-800
                    {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                    {{ po.status }}
                </span>
            </td>
            <td class="py-2 px-4 border-b border-gray-200">{{ po.approver_name|default:"-" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ po.approval_date|default:"-"|date:"Y-m-d" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">
                {% if po.is_pending %}
                <button 
                    class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded text-xs mr-1"
                    hx-get="{% url 'purchaseorder_approve' po.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Approve
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs mr-1"
                    hx-get="{% url 'purchaseorder_reject' po.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Reject
                </button>
                {% endif %}
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded text-xs mr-1"
                    hx-get="{% url 'purchaseorder_edit' po.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-1 px-2 rounded text-xs"
                    hx-get="{% url 'purchaseorder_delete' po.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="9" class="py-4 text-center text-gray-500">No purchase orders found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    // Destroy existing DataTable instance if it exists to prevent re-initialization errors
    if ($.fn.DataTable.isDataTable('#purchaseorderTable')) {
        $('#purchaseorderTable').DataTable().destroy();
    }
    $('#purchaseorderTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "responsive": true,
        "autoWidth": false,
        "columnDefs": [
            { "orderable": false, "targets": [8] } // Disable sorting for 'Actions' column
        ]
    });
});
</script>
```

#### `purchase_orders/templates/purchase_orders/purchaseorder/_purchaseorder_form.html`

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Purchase Order</h3>
    <form hx-post="{{ request.path }}" hx-swap="none"> {# hx-swap="none" allows for HX-Trigger to close modal #}
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

#### `purchase_orders/templates/purchase_orders/purchaseorder/_purchaseorder_confirm_delete.html`

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="mb-4">Are you sure you want to delete Purchase Order: <strong>{{ object.po_number }} - {{ object.supplier_name }}</strong>?</p>
    <form hx-post="{% url 'purchaseorder_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs (`purchase_orders/urls.py`)

This file defines the URL patterns for accessing the various `PurchaseOrder` views.

```python
from django.urls import path
from .views import (
    PurchaseOrderListView, 
    PurchaseOrderCreateView, 
    PurchaseOrderUpdateView, 
    PurchaseOrderDeleteView,
    PurchaseOrderTablePartialView,
    PurchaseOrderApproveView,
    PurchaseOrderRejectView
)

urlpatterns = [
    path('purchase-orders/', PurchaseOrderListView.as_view(), name='purchaseorder_list'),
    path('purchase-orders/table/', PurchaseOrderTablePartialView.as_view(), name='purchaseorder_table'),
    path('purchase-orders/add/', PurchaseOrderCreateView.as_view(), name='purchaseorder_add'),
    path('purchase-orders/edit/<int:pk>/', PurchaseOrderUpdateView.as_view(), name='purchaseorder_edit'),
    path('purchase-orders/delete/<int:pk>/', PurchaseOrderDeleteView.as_view(), name='purchaseorder_delete'),
    path('purchase-orders/approve/<int:pk>/', PurchaseOrderApproveView.as_view(), name='purchaseorder_approve'),
    path('purchase-orders/reject/<int:pk>/', PurchaseOrderRejectView.as_view(), name='purchaseorder_reject'),
]

```

### 4.6 Tests (`purchase_orders/tests.py`)

This file contains comprehensive unit tests for the `PurchaseOrder` model and integration tests for all associated views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from unittest.mock import patch # For mocking request.user if needed

from .models import PurchaseOrder
from .forms import PurchaseOrderForm, PurchaseOrderRejectForm

class PurchaseOrderModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.po1 = PurchaseOrder.objects.create(
            po_number='PO-001',
            supplier_name='Supplier A',
            order_date=timezone.now().date(),
            amount=100.50,
            status='Pending'
        )
        cls.po2 = PurchaseOrder.objects.create(
            po_number='PO-002',
            supplier_name='Supplier B',
            order_date=timezone.now().date(),
            amount=250.00,
            status='Approved',
            approver_name='Test User',
            approval_date=timezone.now().date()
        )
  
    def test_purchase_order_creation(self):
        self.assertEqual(self.po1.po_number, 'PO-001')
        self.assertEqual(self.po1.supplier_name, 'Supplier A')
        self.assertEqual(self.po1.amount, 100.50)
        self.assertEqual(self.po1.status, 'Pending')
        self.assertIsNone(self.po1.approver_name)
        self.assertIsNone(self.po1.approval_date)
        
    def test_po_number_label(self):
        field_label = self.po1._meta.get_field('po_number').verbose_name
        self.assertEqual(field_label, 'PO Number')
        
    def test_supplier_name_label(self):
        field_label = self.po1._meta.get_field('supplier_name').verbose_name
        self.assertEqual(field_label, 'Supplier Name')

    def test_str_representation(self):
        self.assertEqual(str(self.po1), 'PO-001 - Supplier A')

    def test_approve_order_pending(self):
        self.assertTrue(self.po1.approve_order('Admin User'))
        self.assertEqual(self.po1.status, 'Approved')
        self.assertEqual(self.po1.approver_name, 'Admin User')
        self.assertIsNotNone(self.po1.approval_date)

    def test_approve_order_non_pending(self):
        self.assertFalse(self.po2.approve_order('Another User')) # po2 is already Approved
        self.assertEqual(self.po2.status, 'Approved') # Status should not change

    def test_reject_order_pending(self):
        self.assertTrue(self.po1.reject_order('Admin User', 'Needs more info'))
        self.assertEqual(self.po1.status, 'Rejected')
        self.assertEqual(self.po1.approver_name, 'Admin User')
        self.assertIsNotNone(self.po1.approval_date)
        self.assertEqual(self.po1.remarks, 'Needs more info')

    def test_reject_order_non_pending(self):
        self.assertFalse(self.po2.reject_order('Another User', 'Rejected after approval')) # po2 is already Approved
        self.assertEqual(self.po2.status, 'Approved') # Status should not change

    def test_is_pending_method(self):
        self.assertTrue(self.po1.is_pending())
        self.assertFalse(self.po2.is_pending())
        self.po1.status = 'Approved'
        self.assertFalse(self.po1.is_pending())


class PurchaseOrderViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.po1 = PurchaseOrder.objects.create(
            po_number='PO-LIST-001',
            supplier_name='List Supplier A',
            order_date=timezone.now().date(),
            amount=100.00,
            status='Pending'
        )
        cls.po2 = PurchaseOrder.objects.create(
            po_number='PO-LIST-002',
            supplier_name='List Supplier B',
            order_date=timezone.now().date(),
            amount=200.00,
            status='Approved'
        )
        cls.po3 = PurchaseOrder.objects.create(
            po_number='PO-LIST-003',
            supplier_name='List Supplier C',
            order_date=timezone.now().date(),
            amount=300.00,
            status='Pending'
        )
    
    def setUp(self):
        self.client = Client()
        # Mocking user for views that rely on request.user
        self.user_patcher = patch('django.contrib.auth.mixins.LoginRequiredMixin.dispatch', return_value=None)
        self.mock_user = self.user_patcher.start()
        # Create a dummy user for request.user if needed in views (e.g., for approver_name)
        # Note: A proper User model setup and login would be better for real authentication tests
        self.client.force_login(self._create_user(username='testuser'))


    def tearDown(self):
        self.user_patcher.stop()

    def _create_user(self, username='testuser'):
        from django.contrib.auth.models import User
        return User.objects.create_user(username=username, password='password')

    def test_list_view(self):
        response = self.client.get(reverse('purchaseorder_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'purchase_orders/purchaseorder/list.html')
        self.assertTrue('purchase_orders' in response.context)
        # Ensure only pending orders are shown for the main dashboard list (based on get_queryset)
        self.assertEqual(len(response.context['purchase_orders']), 2) # PO-LIST-001, PO-LIST-003
        self.assertContains(response, 'PO-LIST-001')
        self.assertNotContains(response, 'PO-LIST-002') # This one is Approved

    def test_table_partial_view(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('purchaseorder_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'purchase_orders/purchaseorder/_purchaseorder_table.html')
        self.assertTrue('purchase_orders' in response.context)
        # Ensure all orders are shown for the DataTable partial
        self.assertEqual(len(response.context['purchase_orders']), 3) 
        self.assertContains(response, 'PO-LIST-001')
        self.assertContains(response, 'PO-LIST-002')
        self.assertContains(response, 'PO-LIST-003')

    def test_create_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('purchaseorder_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'purchase_orders/purchaseorder/_purchaseorder_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Purchase Order')
        
    def test_create_view_post_success(self):
        data = {
            'po_number': 'PO-NEW-001',
            'supplier_name': 'New Supplier',
            'order_date': '2023-01-01',
            'amount': '500.75',
            'remarks': 'New PO from test'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('purchaseorder_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX success response
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshPurchaseOrderList', response.headers['HX-Trigger'])
        self.assertTrue(PurchaseOrder.objects.filter(po_number='PO-NEW-001').exists())
        self.assertEqual(PurchaseOrder.objects.get(po_number='PO-NEW-001').status, 'Pending') # Default status

    def test_create_view_post_invalid(self):
        data = { # Missing po_number prefix, amount invalid
            'po_number': '001', 
            'supplier_name': 'New Supplier',
            'order_date': 'invalid-date',
            'amount': 'abc',
            'remarks': ''
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('purchaseorder_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # Form re-renders with errors
        self.assertTemplateUsed(response, 'purchase_orders/purchaseorder/_purchaseorder_form.html')
        self.assertContains(response, "PO Number must start with 'PO-'.")
        self.assertContains(response, "Enter a valid date.")
        self.assertContains(response, "Enter a number.")
        self.assertFalse(PurchaseOrder.objects.filter(po_number='001').exists())

    def test_update_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('purchaseorder_edit', args=[self.po1.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'purchase_orders/purchaseorder/_purchaseorder_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Edit Purchase Order')
        self.assertContains(response, self.po1.po_number)

    def test_update_view_post_success(self):
        data = {
            'po_number': self.po1.po_number, # Keep same PO number
            'supplier_name': 'Updated Supplier',
            'order_date': self.po1.order_date.isoformat(),
            'amount': '150.00',
            'remarks': 'Updated PO'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('purchaseorder_edit', args=[self.po1.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshPurchaseOrderList', response.headers['HX-Trigger'])
        self.po1.refresh_from_db()
        self.assertEqual(self.po1.supplier_name, 'Updated Supplier')
        self.assertEqual(self.po1.amount, 150.00)

    def test_delete_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('purchaseorder_delete', args=[self.po1.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'purchase_orders/purchaseorder/_purchaseorder_confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertContains(response, 'Confirm Delete')
        self.assertContains(response, self.po1.po_number)

    def test_delete_view_post(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('purchaseorder_delete', args=[self.po1.pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshPurchaseOrderList', response.headers['HX-Trigger'])
        self.assertFalse(PurchaseOrder.objects.filter(pk=self.po1.pk).exists())

    def test_approve_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('purchaseorder_approve', args=[self.po1.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Confirm Approval')
        self.assertContains(response, self.po1.po_number)

    def test_approve_view_post_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('purchaseorder_approve', args=[self.po1.pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshPurchaseOrderList', response.headers['HX-Trigger'])
        self.po1.refresh_from_db()
        self.assertEqual(self.po1.status, 'Approved')
        self.assertEqual(self.po1.approver_name, 'testuser') # From self.client.force_login
        self.assertIsNotNone(self.po1.approval_date)
    
    def test_approve_view_post_already_approved(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('purchaseorder_approve', args=[self.po2.pk]), **headers)
        self.assertEqual(response.status_code, 204) # Still 204 for HTMX, but trigger might be different or no trigger
        # For error scenario, the view is written to return 204 but with a specific trigger.
        # Alternatively, a 200 status with a swapped error message would be more descriptive.
        # Let's adjust view for explicit error message. For now, testing the outcome.
        self.po2.refresh_from_db()
        self.assertEqual(self.po2.status, 'Approved') # Status remains unchanged
        # Check messages for the error message
        messages = list(response.wsgi_request._messages)
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), f'Purchase Order "{self.po2.po_number}" could not be approved (status: {self.po2.status}).')


    def test_reject_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('purchaseorder_reject', args=[self.po1.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Reject Purchase Order')
        self.assertContains(response, self.po1.po_number)
        self.assertContains(response, '<textarea') # Check for remarks field

    def test_reject_view_post_success(self):
        data = {'remarks': 'Rejected by test user because of insufficient details.'}
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('purchaseorder_reject', args=[self.po1.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshPurchaseOrderList', response.headers['HX-Trigger'])
        self.po1.refresh_from_db()
        self.assertEqual(self.po1.status, 'Rejected')
        self.assertEqual(self.po1.approver_name, 'testuser')
        self.assertIsNotNone(self.po1.approval_date)
        self.assertEqual(self.po1.remarks, 'Rejected by test user because of insufficient details.')

    def test_reject_view_post_no_remarks(self):
        data = {'remarks': ''} # Required field
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('purchaseorder_reject', args=[self.po1.pk]), data, **headers)
        self.assertEqual(response.status_code, 200) # Form re-renders with errors
        self.assertContains(response, "This field is required.")
        self.po1.refresh_from_db()
        self.assertEqual(self.po1.status, 'Pending') # Status should not change if validation fails

```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for All Dynamic Updates:** All form submissions (Create, Update), delete confirmations, and approval/rejection actions are handled via HTMX POST requests, which return a `204 No Content` status with an `HX-Trigger` header upon success. This header signals the browser to trigger a `refreshPurchaseOrderList` event, which in turn causes the `purchaseorderTable-container` to reload its content (`_purchaseorder_table.html`) dynamically, ensuring the list is always up-to-date without a full page refresh. Modals are loaded using `hx-get` into `#modalContent`.
*   **Alpine.js for UI State Management:** Alpine.js is used to control the visibility of the modal (`#modal`) using `x-data` and `x-show`. The `on click add .is-active to #modal` syntax provides a declarative way to show the modal when a button is clicked. Event listeners like `htmx:afterSwap` and `htmx:beforeSwap` are used to update Alpine.js's `showModal` state, ensuring the modal opens when content is loaded into it and closes when an HTMX action results in a 204 status (success).
*   **DataTables for List Views:** The `_purchaseorder_table.html` partial includes the JavaScript initialization for DataTables. The script is inside the partial so that it runs every time the partial is loaded via HTMX, ensuring DataTables correctly re-initializes on the new content. This provides client-side searching, sorting, and pagination for the list of purchase orders.
*   **No Full Page Reloads:** All interactions (adding, editing, deleting, approving, rejecting, and list refreshes) are designed to occur without full page reloads, providing a smooth and responsive user experience.
*   **DRY Templates:** The use of `_purchaseorder_table.html`, `_purchaseorder_form.html`, and `_purchaseorder_confirm_delete.html` as partials ensures that reusable components are not duplicated across different templates. The main `list.html` extends `core/base.html`, adhering to DRY principles for shared layout elements and CDN links.

## Final Notes

*   **Placeholders:** All `[PLACEHOLDER]` values have been replaced with inferred names like `PurchaseOrder`, `tblPurchaseOrder`, `po_number`, etc., based on typical ERP scenarios and the original ASP.NET page title.
*   **Business Logic in Models:** Business logic for approving and rejecting orders (`approve_order`, `reject_order`) is encapsulated within the `PurchaseOrder` model, strictly adhering to the "fat model, thin view" principle. This improves code organization, testability, and reusability.
*   **Comprehensive Tests:** Unit tests for model methods and integration tests for all view actions are included to ensure correctness, maintainability, and stability of the migrated application.
*   **Automated Conversion:** The detailed breakdown into separate files and the clear instructions for each step demonstrate how an AI-assisted automation tool can systematically convert legacy ASP.NET components into modern Django equivalents, focusing on patterns that minimize manual intervention and maximize efficiency.
*   **Business Value:** This modernization provides a significantly improved user experience with faster, dynamic interactions, a more maintainable and scalable codebase, and a robust foundation for future enhancements. The clear separation of concerns and adherence to modern web standards reduce development costs and technical debt.