## ASP.NET to Django Conversion Script: SPR New Module

This plan outlines the modernization of the "SPR - New" module from its legacy ASP.NET implementation to a robust, modern Django 5.0+ application. Our approach leverages AI-assisted automation to streamline the transition, focusing on business value and maintainability.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code to define Django models.

**Instructions:**
The ASP.NET code interacts with several database tables. We will focus on the primary temporary table (`tblMM_SPR_Temp`) that holds the currently selected or added items, and other master tables it references for lookups and final processing.

**Identified Tables and Key Columns:**

*   **`tblMM_SPR_Temp`** (Temporary items for the current SPR):
    *   `Id` (Primary Key, integer)
    *   `SysDate` (Date)
    *   `SysTime` (Time)
    *   `CompId` (Company ID, integer)
    *   `FinYearId` (Financial Year ID, integer)
    *   `SessionId` (User Session ID, string)
    *   `SupplierId` (Foreign Key to `tblMM_Supplier_master`, string/integer)
    *   `NoCode` (Integer, nullable, for custom/non-catalog items)
    *   `ItemId` (Foreign Key to `tblDG_Item_Master`, integer, nullable, for catalog items)
    *   `ManfDesc` (Description, string)
    *   `UOMBasic` (Foreign Key to `Unit_Master`, integer)
    *   `Qty` (Quantity, decimal)
    *   `Rate` (Rate, decimal)
    *   `AHId` (Foreign Key to `AccHead`, integer)
    *   `WONo` (Work Order Number, string, nullable)
    *   `DeptId` (Foreign Key to `BusinessGroup`, integer, nullable)
    *   `Remarks` (Remarks, string, nullable)
    *   `DelDate` (Delivery Date, date)
    *   `Discount` (Discount, decimal)

*   **`tblDG_Item_Master`** (Main Item Catalog):
    *   `Id` (Primary Key, integer)
    *   `ItemCode` (String)
    *   `ManfDesc` (Description, string)
    *   `UOMBasic` (Foreign Key to `Unit_Master`, integer)
    *   `StockQty` (Decimal)
    *   `Location` (Foreign Key to `tblDG_Location_Master`, integer)
    *   `CId` (Foreign Key to `tblDG_Category_Master`, integer)
    *   `MinOrderQty` (Decimal)
    *   `MinStockQty` (Decimal)
    *   `Absolute` (Boolean/Integer)
    *   `OpeningBalQty` (Decimal)
    *   `UOMConFact` (Decimal)
    *   `OpeningBalDate` (Date)
    *   `AHId` (Foreign Key to `AccHead`, integer)

*   **`tblMM_Supplier_master`**:
    *   `SupplierId` (Primary Key, string/integer)
    *   `SupplierName` (String)

*   **`Unit_Master`**:
    *   `Id` (Primary Key, integer)
    *   `Symbol` (String, e.g., "PC", "KG")

*   **`AccHead`**:
    *   `Id` (Primary Key, integer)
    *   `Symbol` (String, e.g., "LBR")
    *   `Description` (String, e.g., "Labour Charges")

*   **`BusinessGroup`**:
    *   `Id` (Primary Key, integer)
    *   `Symbol` (String, e.g., "DEPT-A")

*   **`tblDG_Category_Master`**:
    *   `CId` (Primary Key, integer)
    *   `Symbol` (String)
    *   `CName` (String)

*   **`tblDG_Location_Master`**:
    *   `Id` (Primary Key, integer)
    *   `LocationLabel` (String)
    *   `LocationNo` (String)

*   **`tblMM_SPR_Master`** (Header for final SPR):
    *   `Id` (Primary Key, integer)
    *   `SPRNo` (String, unique SPR number)
    *   `SysDate`, `SysTime`, `SessionId`, `CompId`, `FinYearId` (similar to temp table)

*   **`tblMM_SPR_Details`** (Line items for final SPR):
    *   `MId` (Foreign Key to `tblMM_SPR_Master`, integer)
    *   `SPRNo` (String)
    *   `ItemId` (Foreign Key to `tblDG_Item_Master`, integer)
    *   `Qty`, `Rate`, `SupplierId`, `AHId`, `WONo`, `DeptId`, `Remarks`, `DelDate`, `Discount` (similar to temp table)

*   **`tblFinancial_master`**:
    *   `Id`, `FinYear`, `FinYearFrom`, `FinYearTo`, etc. (used for financial year details)

*   **`tblMM_RateLockUnLock_Master`**:
    *   `ItemId`, `LockUnlock`, `LockedbyTranaction`, `LockDate`, `LockTime`, `Type`, `CompId` (used for rate locking)

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations (Create, Read, Update, Delete) and business logic within the ASP.NET code.

**Instructions:**
The "SPR - New" module enables users to create new Material Requisitions (SPR). It involves selecting items from a master catalog or adding custom "no-code" items, managing these items in a temporary list, and then finalizing the requisition.

*   **Read (R):**
    *   Displaying a searchable and filterable list of `ItemMaster` records (`GridView2`).
    *   Displaying a temporary list of selected/added `SPRTempItem` records for the current session (`GridView3`).
    *   Populating various dropdowns (Categories, Locations, Units, Account Heads, Business Groups) from their respective master tables.
    *   Providing an autocomplete search for `Supplier` records.

*   **Create (C):**
    *   **Adding "No-Code" Items:** Users can enter details for an item that doesn't exist in the `ItemMaster` catalog. This item is then added to the temporary `tblMM_SPR_Temp` table.
    *   **Adding Existing "Item Master" Items:** Users can select an item from `GridView2` and then provide specific details (quantity, rate, delivery date, etc.) before adding it to `tblMM_SPR_Temp`.
    *   **Finalizing SPR:** The "Proceed" button triggers a batch creation process where all items from `tblMM_SPR_Temp` are moved to `tblMM_SPR_Master` (header) and `tblMM_SPR_Details` (line items). For "no-code" items, new entries are also created in `tblDG_Item_Master`.

*   **Update (U):**
    *   While there isn't a direct "update" of an `SPRTempItem` record shown, any modification to an item in the temporary list would typically involve deleting it and re-adding it. The current ASP.NET implementation follows this pattern implicitly for `SPRTempItem` records (delete and re-add if correction needed).

*   **Delete (D):**
    *   Users can remove items from the temporary `tblMM_SPR_Temp` list (`GridView3`'s "Delete" link).

**Validation Logic and Business Rules:**
*   Input validation: Required fields, numeric formats (Qty, Rate, Discount), date format, valid WO No.
*   Item existence check: Before adding an existing `ItemMaster` item to the temporary list, check if it's already there for the current session.
*   Automatic `SPRNo` generation based on financial year and sequence.
*   Dynamic visibility of search fields based on dropdown selections.
*   Account Head dropdown population based on radio button selection.
*   Rate locking update logic upon final SPR creation.
*   Generation of `ItemCode` for "no-code" items (`FinYear-SPRNo-NoCode`).

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, inferring their Django and HTMX equivalents.

**Instructions:**
The ASP.NET page utilizes a `TabContainer` to organize different functional areas. Each `TabPanel` contains specific controls for search, data entry, or data display.

*   **Main Structure:**
    *   `TabContainer1`: Will be implemented using a single Django template that renders all sections. HTMX and Alpine.js will manage the "tab-like" behavior or simply display all sections as distinct panels that can be interacted with dynamically. For simplicity and modern UI, I will represent the tabs as logical sections within a single page, dynamically updated via HTMX.

*   **"Item Master" Section (`TabPanel1`):**
    *   **Search/Filter Controls:**
        *   `DrpType` (DropDownList): Django `forms.ChoiceField` with `select` widget.
        *   `DrpCategory` (DropDownList): Django `forms.ChoiceField` with `select` widget. Populated dynamically via HTMX.
        *   `DrpSearchCode` (DropDownList): Django `forms.ChoiceField` with `select` widget.
        *   `DropDownList3` (DropDownList): Django `forms.ChoiceField` with `select` widget (for Location). Populated dynamically via HTMX.
        *   `txtSearchItemCode` (TextBox): Django `forms.CharField` with `textinput` widget.
        *   `btnSearch` (Button): HTMX `hx-get` to trigger search and update `_item_master_table.html`.
    *   **Data Display:**
        *   `GridView2`: Replaced by a `<table>` enhanced with DataTables. Columns: SN, Select (LinkButton), Item Code, Description, UOM, Stock Qty, Location. The "Select" action will trigger an HTMX `hx-get` to load a modal form to capture additional details.

*   **"No Code Item" Section (`TabPanel2`):**
    *   **Data Entry Form:** This will be a Django `ModelForm` rendered as a partial template.
        *   `txtManfDesc` (MultiLine TextBox): `forms.Textarea`.
        *   `DDLUnitBasic` (DropDownList): `forms.ChoiceField` with `select` widget.
        *   `rdwono`, `rddept` (RadioButtons): `forms.RadioSelect` for mutual exclusion. Will use Alpine.js for showing/hiding related textboxes/dropdowns.
        *   `txtwono` (TextBox): `forms.CharField`.
        *   `drpdept` (DropDownList): `forms.ChoiceField` with `select` widget.
        *   `textDelDate` (TextBox with CalendarExtender): `forms.DateField` with `dateinput` widget and Alpine.js for a simple date picker if needed, though modern browsers handle `type="date"` well.
        *   `txtRemark` (MultiLine TextBox): `forms.Textarea`.
        *   `txtQty`, `txtRate`, `txtDiscount` (Textboxes): `forms.DecimalField`.
        *   `txtAutoSupplierExt` (TextBox with AutoCompleteExtender): `forms.CharField` with `textinput` and HTMX `hx-get` for autocomplete functionality.
        *   `RbtnLabour`, `RbtnWithMaterial`, `RbtnExpenses`, `RbtnSerProvider` (RadioButtons): `forms.RadioSelect` for Account Head type, controlling `DropDownList1`.
        *   `DropDownList1` (DropDownList): `forms.ChoiceField` with `select` widget.
        *   `btnAdd` (Button): HTMX `hx-post` to submit the form and add to `tblMM_SPR_Temp`, triggering a refresh of `_selected_items_table.html`.

*   **"Selected Items" Section (`TabPanel3`):**
    *   **Data Display:**
        *   `GridView3`: Replaced by a `<table>` enhanced with DataTables. Columns: SN, Delete (LinkButton), Item Code, No Code, Description, UOM, Del.Date, A/c Head, WONo, Dept, Qty, Rate, Dis, Remarks. The "Delete" action will trigger an HTMX `hx-get` to load a confirmation modal, then `hx-delete` to remove the item.

*   **Page-Level Actions:**
    *   `Button1` ("Proceed"): HTMX `hx-post` to finalize the SPR, transferring temporary items to master tables and clearing the session list.
    *   `btnCancel` ("Cancel"): Standard Django URL redirect.

*   **Client-Side Scripting (`loadingNotifier.js`, `PopUpMsg.js`, `OnChanged`, `confirmationAdd`, `confirmationDelete`):**
    *   All dynamic interactions (`AutoPostBack`, `AjaxControlToolkit`) will be replaced by HTMX.
    *   Client-side validation will be handled by Django's form validation (server-side) and HTMX for dynamic re-validation.
    *   Pop-up messages will be replaced by Django messages framework combined with HTMX/Alpine.js for non-intrusive toasts or modal alerts.
    *   The `OnChanged` `SetTabIndex` is no longer relevant with an HTMX-driven approach where specific content areas are updated.

---

### Step 4: Generate Django Code

We will create a Django application named `spr_app`.

#### 4.1 Models (`spr_app/models.py`)

**Task:** Create Django models mapping to the identified database tables, using `managed = False` for existing tables. Implement "fat model" approach by adding business logic methods.

**Instructions:**
Define models for `SPRTempItem`, `ItemMaster`, `Supplier`, `Unit`, `AccountHead`, `BusinessGroup`, `Category`, `Location`, `SPRMaster`, `SPRDetail`. For related tables, define basic fields required for foreign key relationships or display.

```python
from django.db import models
from django.utils import timezone
from decimal import Decimal
import datetime

# --- Helper Models (for Foreign Keys and Lookups) ---
# These models assume existing tables and thus use managed = False.
# They are simplified to only include fields relevant to this module's interaction.

class Unit(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class AccountHead(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    description = models.CharField(db_column='Description', max_length=255)

    class Meta:
        managed = False
        db_table = 'AccHead'
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'

    def __str__(self):
        return f"[{self.symbol}] - {self.description}"

class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50) # Assuming 'Dept' maps to Symbol

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

class Supplier(models.Model):
    supplier_id = models.CharField(db_column='SupplierId', primary_key=True, max_length=50)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.supplier_name

class Category(models.Model):
    cid = models.IntegerField(db_column='CId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    cname = models.CharField(db_column='CName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'

    def __str__(self):
        return f"[{self.symbol}] - {self.cname}"

class Location(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    location_label = models.CharField(db_column='LocationLabel', max_length=255)
    location_no = models.CharField(db_column='LocationNo', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblDG_Location_Master'
        verbose_name = 'Location'
        verbose_name_plural = 'Locations'

    def __str__(self):
        return f"{self.location_label}-{self.location_no}"

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=100)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=500)
    uom_basic = models.ForeignKey(Unit, models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)
    stock_qty = models.DecimalField(db_column='StockQty', max_digits=18, decimal_places=3, blank=True, null=True)
    location = models.ForeignKey(Location, models.DO_NOTHING, db_column='Location', blank=True, null=True)
    cid = models.ForeignKey(Category, models.DO_NOTHING, db_column='CId', blank=True, null=True)
    # Add other fields as necessary if they are used elsewhere
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    session_id = models.CharField(db_column='SessionId', max_length=50)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    min_order_qty = models.DecimalField(db_column='MinOrderQty', max_digits=18, decimal_places=3)
    min_stock_qty = models.DecimalField(db_column='MinStockQty', max_digits=18, decimal_places=3)
    absolute = models.IntegerField(db_column='Absolute') # 0 or 1
    opening_bal_qty = models.DecimalField(db_column='OpeningBalQty', max_digits=18, decimal_places=3)
    uom_con_fact = models.DecimalField(db_column='UOMConFact', max_digits=18, decimal_places=3)
    opening_bal_date = models.DateField(db_column='OpeningBalDate')
    ahid = models.ForeignKey(AccountHead, models.DO_NOTHING, db_column='AHId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.item_code

    # Business logic: Method to get item code/part no (if any complex logic for it)
    def get_item_code_part_no(self, comp_id):
        # Placeholder for original fun.GetItemCode_PartNo(CompId, ItemId)
        # Assuming it just returns item_code for ItemMaster items, otherwise it would involve lookups.
        return self.item_code

class FinancialYear(models.Model):
    # Simplified model assuming we only need FinYear and FinYearFrom
    id = models.IntegerField(db_column='Id', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=20)
    fin_year_from = models.DateField(db_column='FinYearFrom')
    fin_year_to = models.DateField(db_column='FinYearTo')
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class RateLockUnlock(models.Model):
    # Simplified model
    item_id = models.IntegerField(db_column='ItemId', primary_key=True) # Assuming ItemId is unique key or composite
    lock_unlock = models.IntegerField(db_column='LockUnlock')
    locked_by_transaction = models.CharField(db_column='LockedbyTranaction', max_length=50, blank=True, null=True)
    lock_date = models.DateField(db_column='LockDate', blank=True, null=True)
    lock_time = models.TimeField(db_column='LockTime', blank=True, null=True)
    type = models.IntegerField(db_column='Type')
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_RateLockUnLock_Master'
        verbose_name = 'Rate Lock Unlock'
        verbose_name_plural = 'Rate Lock Unlocks'
        # Composite primary key example if needed:
        # unique_together = (('item_id', 'type', 'comp_id'),)


# --- Core Module Model ---
class SPRTempItem(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    session_id = models.CharField(db_column='SessionId', max_length=50)
    supplier_id = models.CharField(db_column='SupplierId', max_length=50) # Stored as string, but refers to Supplier.supplier_id
    nocode = models.IntegerField(db_column='NoCode', blank=True, null=True) # Null for ItemMaster items
    item = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId', blank=True, null=True) # Null for NoCode items
    manf_desc = models.CharField(db_column='ManfDesc', max_length=500)
    uom_basic = models.ForeignKey(Unit, models.DO_NOTHING, db_column='UOMBasic')
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3)
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=2)
    account_head = models.ForeignKey(AccountHead, models.DO_NOTHING, db_column='AHId')
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    dept = models.ForeignKey(BusinessGroup, models.DO_NOTHING, db_column='DeptId', blank=True, null=True)
    remarks = models.CharField(db_column='Remarks', max_length=1000, blank=True, null=True)
    del_date = models.DateField(db_column='DelDate')
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=3)

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Temp'
        verbose_name = 'SPR Temporary Item'
        verbose_name_plural = 'SPR Temporary Items'

    def __str__(self):
        return f"Temp SPR Item: {self.manf_desc}"

    # Business logic from C# LoadData and btnAdd_Click (NoCode and ItemMaster item handling)
    def get_display_data(self):
        """
        Mimics the complex lookups done in C# LoadData to prepare data for GridView3.
        This is a fat model method that pulls related data.
        """
        data = {
            'id': self.id,
            'item_code': 'NA', # Default
            'nocode_str': 'NA', # Default
            'description': self.manf_desc,
            'uom_basic': self.uom_basic.symbol,
            'del_date': self.del_date.strftime('%d-%m-%Y') if self.del_date else '', # fun.FromDateDMY
            'account_head': str(self.account_head), # "[Symbol] - Description"
            'wo_no': self.wo_no if self.wo_no else 'NA',
            'dept': self.dept.symbol if self.dept else 'NA',
            'qty': f"{self.qty:.3f}", # "N3" format
            'rate': f"{self.rate:.2f}", # "N2" format
            'discount': f"{self.discount:.3f}",
            'remarks': self.remarks,
        }

        if self.item: # It's an ItemMaster item
            data['item_code'] = self.item.item_code
            data['nocode_str'] = '' # No NoCode for ItemMaster items
            data['description'] = self.item.manf_desc # Use master description
            data['uom_basic'] = self.item.uom_basic.symbol
        else: # It's a NoCode item
            data['nocode_str'] = str(self.nocode)
            data['item_code'] = '' # No ItemCode for initial NoCode items

        return data
    
    @classmethod
    def get_next_nocode_value(cls, comp_id, session_id):
        """Calculates the next NoCode value for a session, mimicking C# logic."""
        count = cls.objects.filter(comp_id=comp_id, session_id=session_id, nocode__isnull=False).count()
        return count + 1

    @classmethod
    def create_spr_item(cls, user, form_data, is_nocode=True):
        """
        Handles creation of an SPRTempItem, encapsulating logic from btnAdd_Click
        and GridView2_RowCommand (for adding item master items to temp list).
        """
        comp_id = user.company_id # Assuming user has company_id
        fin_year_id = user.financial_year_id # Assuming user has financial_year_id
        session_id = user.username # Assuming username is used as session_id

        # Use timezone.now() for current date and time
        current_date = timezone.localdate()
        current_time = timezone.localtime().time()

        if is_nocode:
            # Generate next NoCode for new item
            nocode_val = cls.get_next_nocode_value(comp_id, session_id)
            item_id = None # No ItemMaster ID for NoCode items
            manf_desc = form_data['manf_desc']
        else:
            # For existing ItemMaster items selected from grid
            item_master_id = form_data['item'].id
            if cls.objects.filter(item_id=item_master_id, comp_id=comp_id, session_id=session_id).exists():
                raise ValueError("Item is already in your temporary list.")
            
            item_id = ItemMaster.objects.get(pk=item_master_id)
            nocode_val = None
            manf_desc = item_id.manf_desc # Use ItemMaster's description

        qty = Decimal(form_data['qty'])
        rate = Decimal(form_data['rate'])
        discount = Decimal(form_data.get('discount', '0'))

        # WO No vs Dept ID logic
        wo_no = form_data.get('wo_no') if form_data.get('wo_type') == 'wono' else None
        dept_id = form_data.get('dept') if form_data.get('wo_type') == 'dept' else None

        # Simplified supplier logic assuming supplier_id is extracted from name_code string
        supplier_full = form_data['supplier']
        supplier_id = supplier_full.split('[')[-1].replace(']', '') # Extract ID from "Name [ID]"
        
        # Check supplier code validity - fun.chkSupplierCode
        # This check might need a dedicated SupplierManager or method
        # For now, we assume the extracted ID is valid.
        # if not Supplier.objects.filter(supplier_id=supplier_id).exists():
        #     raise ValueError("Invalid Supplier Code.")

        # Check WO No validity - fun.CheckValidWONo
        # This check might need a dedicated WONoManager or method
        # For now, we assume it's valid if provided.
        # if wo_no and not some_wo_check_function(wo_no, comp_id, fin_year_id):
        #     raise ValueError("Invalid WO No.")

        # Get UOM and Account Head objects
        uom_obj = Unit.objects.get(pk=form_data['uom_basic'])
        ah_obj = AccountHead.objects.get(pk=form_data['account_head'])
        dept_obj = BusinessGroup.objects.get(pk=dept_id) if dept_id else None


        temp_item = cls.objects.create(
            sys_date=current_date,
            sys_time=current_time,
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            session_id=session_id,
            supplier_id=supplier_id,
            nocode=nocode_val,
            item=item_id,
            manf_desc=manf_desc,
            uom_basic=uom_obj,
            qty=qty,
            rate=rate,
            account_head=ah_obj,
            wo_no=wo_no,
            dept=dept_obj,
            remarks=form_data.get('remarks'),
            del_date=form_data['del_date'],
            discount=discount
        )
        return temp_item

class SPRMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    session_id = models.CharField(db_column='SessionId', max_length=50)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    spr_no = models.CharField(db_column='SPRNo', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Master'
        verbose_name = 'SPR Master'
        verbose_name_plural = 'SPR Masters'

    def __str__(self):
        return self.spr_no

    @classmethod
    def generate_spr_no(cls, comp_id, fin_year_id):
        """Generates the next SPR number."""
        last_spr = cls.objects.filter(comp_id=comp_id, fin_year_id=fin_year_id).order_by('-id').first()
        if last_spr:
            next_spr_int = int(last_spr.spr_no) + 1
        else:
            next_spr_int = 1
        return f"{next_spr_int:04d}"

class SPRDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming a primary key for details
    master = models.ForeignKey(SPRMaster, models.DO_NOTHING, db_column='MId')
    spr_no = models.CharField(db_column='SPRNo', max_length=50)
    item = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId')
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3)
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=2)
    supplier_id = models.CharField(db_column='SupplierId', max_length=50)
    account_head = models.ForeignKey(AccountHead, models.DO_NOTHING, db_column='AHId')
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    dept = models.ForeignKey(BusinessGroup, models.DO_NOTHING, db_column='DeptId', blank=True, null=True)
    remarks = models.CharField(db_column='Remarks', max_length=1000, blank=True, null=True)
    del_date = models.DateField(db_column='DelDate')
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=3)

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Details'
        verbose_name = 'SPR Detail'
        verbose_name_plural = 'SPR Details'

    def __str__(self):
        return f"SPR {self.spr_no} - Item: {self.item.item_code}"


class SPRManager:
    """
    Encapsulates the complex 'Proceed' logic from Button1_Click.
    This class orchestrates the finalization of an SPR from temporary items.
    """
    @staticmethod
    def finalize_spr(user):
        comp_id = user.company_id
        fin_year_id = user.financial_year_id
        session_id = user.username
        
        current_date = timezone.localdate()
        current_time = timezone.localtime().time()

        # Retrieve all temporary items for the current session
        temp_items = SPRTempItem.objects.filter(
            comp_id=comp_id,
            session_id=session_id
        ).order_by('-id') # Order by Id Desc as in C# LoadData

        if not temp_items.exists():
            raise ValueError("No items found in your temporary SPR list. Please add items before proceeding.")

        # 1. Generate SPR Number
        new_spr_no = SPRMaster.generate_spr_no(comp_id, fin_year_id)

        # 2. Create SPR Master record
        spr_master = SPRMaster.objects.create(
            sys_date=current_date,
            sys_time=current_time,
            session_id=session_id,
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            spr_no=new_spr_no
        )

        # 3. Process each temporary item into SPR Details and potentially Item Master
        for temp_item in temp_items:
            item_for_detail = temp_item.item # Already an ItemMaster object if from catalog
            
            if temp_item.nocode: # It's a "No Code Item"
                # Generate new ItemCode for the NoCode item
                # C# logic: FinYear-SPRNo-NoC1 (D3 format for NoC)
                financial_year = FinancialYear.objects.get(comp_id=comp_id, id=fin_year_id)
                fin_year_str = financial_year.fin_year
                formatted_nocode = f"{temp_item.nocode:03d}"
                generated_item_code = f"{fin_year_str}-{new_spr_no}-{formatted_nocode}"

                # Check if item already exists in tblDG_Item_Master to prevent duplicates
                # This seems to be a complex check, sometimes it will not create a new master item
                # if the code already exists. Replicating the C# logic where it only creates if DS7.Tables[0].Rows.Count == 0
                existing_item_master = ItemMaster.objects.filter(
                    comp_id=comp_id,
                    item_code=generated_item_code
                ).first()

                if not existing_item_master:
                    # Get FinYearFrom for OpeningBalDate (from tblFinancial_master)
                    opening_bal_date = financial_year.fin_year_from

                    # Create new ItemMaster entry for the NoCode item
                    item_for_detail = ItemMaster.objects.create(
                        sys_date=current_date,
                        sys_time=current_time,
                        session_id=session_id,
                        comp_id=comp_id,
                        fin_year_id=fin_year_id,
                        cid=Category.objects.get(pk=26), # Hardcoded CId=26 from C# code
                        item_code=generated_item_code,
                        manf_desc=temp_item.manf_desc,
                        uom_basic=temp_item.uom_basic,
                        min_order_qty=Decimal('0'),
                        min_stock_qty=Decimal('0'),
                        stock_qty=Decimal('0'),
                        absolute=0, # Hardcoded 0
                        opening_bal_qty=Decimal('0'),
                        uom_con_fact=Decimal('0'),
                        opening_bal_date=opening_bal_date,
                        ahid=temp_item.account_head
                    )
                else:
                    item_for_detail = existing_item_master # Use existing item if code already present

            # Create SPR Detail record
            SPRDetail.objects.create(
                master=spr_master,
                spr_no=new_spr_no,
                item=item_for_detail,
                qty=temp_item.qty,
                rate=temp_item.rate,
                supplier_id=temp_item.supplier_id,
                account_head=temp_item.account_head,
                wo_no=temp_item.wo_no,
                dept=temp_item.dept,
                remarks=temp_item.remarks,
                del_date=temp_item.del_date,
                discount=temp_item.discount
            )

            # Update Rate Lock Unlock Master
            # This is complex and might involve a manager/service
            # C# logic: LockUnlock='0',LockedbyTranaction='SPRIdStr' , LockDate='CDate' ,LockTime='CTime'
            #            where ItemId='ItemId' And Type='1' AND CompId='CompId'
            # Assuming 'Type=1' and 'CompId' are part of the unique key or filter
            RateLockUnlock.objects.update_or_create(
                item_id=item_for_detail.id,
                type=1,
                comp_id=comp_id,
                defaults={
                    'lock_unlock': 0,
                    'locked_by_transaction': new_spr_no,
                    'lock_date': current_date,
                    'lock_time': current_time
                }
            )

        # 4. Clear temporary items for the session
        temp_items.delete()

        return new_spr_no

```

#### 4.2 Forms (`spr_app/forms.py`)

**Task:** Define Django forms for user input, including validation and appropriate widgets.

**Instructions:**
Create forms for the "No Code Item" entry and a separate form for adding quantity/rate details to selected `ItemMaster` items.

```python
from django import forms
from .models import SPRTempItem, Unit, AccountHead, BusinessGroup, ItemMaster, Supplier, Category, Location
import re
from datetime import date

# Helper function to validate decimal fields with 3 decimal places
def validate_decimal_3_places(value):
    if value is not None:
        if not re.match(r"^\d{1,15}(\.\d{0,3})?$", str(value)):
            raise forms.ValidationError("Enter a valid number with up to 3 decimal places.")

# Helper function to validate decimal fields with 2 decimal places
def validate_decimal_2_places(value):
    if value is not None:
        if not re.match(r"^\d{1,15}(\.\d{0,2})?$", str(value)):
            raise forms.ValidationError("Enter a valid number with up to 2 decimal places.")

class ItemMasterSearchForm(forms.Form):
    """
    Form for the 'Item Master' search section.
    Replicates DrpType, DrpCategory, DrpSearchCode, DropDownList3, txtSearchItemCode.
    """
    TYPE_CHOICES = [
        ('', 'Select'),
        ('Category', 'Category'),
        ('WOItems', 'WO Items'),
    ]
    SEARCH_CODE_CHOICES = [
        ('', 'Select'),
        ('ItemCode', 'Item Code'),
        ('ManfDesc', 'Description'),
        ('Location', 'Location'),
    ]

    type = forms.ChoiceField(
        choices=TYPE_CHOICES,
        required=True,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'hx-get': '/spr/item-master-search-options/', 'hx-target': '#item-master-dynamic-fields', 'hx-swap': 'outerHTML', 'hx-indicator': '.htmx-indicator'})
    )
    category = forms.ModelChoiceField(
        queryset=Category.objects.all().order_by('cname'), # Ordered by Category Name
        to_field_name='cid',
        required=False,
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'hx-get': '/spr/item-master-search-fields/', 'hx-target': '#item-master-search-value-container', 'hx-swap': 'outerHTML', 'hx-indicator': '.htmx-indicator'})
    )
    search_code = forms.ChoiceField(
        choices=SEARCH_CODE_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'hx-get': '/spr/item-master-search-value-field/', 'hx-target': '#item-master-search-value-container', 'hx-swap': 'outerHTML', 'hx-indicator': '.htmx-indicator'})
    )
    search_value_text = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Search Value'})
    )
    search_value_location = forms.ModelChoiceField(
        queryset=Location.objects.all().order_by('location_label'),
        to_field_name='id',
        required=False,
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Initially hide dynamic fields, they will be shown by HTMX
        self.fields['category'].widget.attrs['class'] += ' hidden'
        self.fields['search_code'].widget.attrs['class'] += ' hidden'
        self.fields['search_value_text'].widget.attrs['class'] += ' hidden'
        self.fields['search_value_location'].widget.attrs['class'] += ' hidden'

class SPRTempItemNoCodeForm(forms.Form):
    """
    Form for adding 'No Code Item' to SPRTempItem.
    Replicates TabPanel2 functionality.
    """
    manf_desc = forms.CharField(
        label="Description",
        widget=forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-24'})
    )
    uom_basic = forms.ModelChoiceField(
        queryset=Unit.objects.all(),
        to_field_name='id',
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    WO_TYPE_CHOICES = [
        ('wono', 'WO No'),
        ('dept', 'BG Group'),
    ]
    wo_type = forms.ChoiceField(
        choices=WO_TYPE_CHOICES,
        widget=forms.RadioSelect(attrs={'x-model': 'woType'}),
        initial='wono'
    )
    wo_no = forms.CharField(
        label="WO No",
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3', 'x-show': "woType === 'wono'"}))
    dept = forms.ModelChoiceField(
        label="BG Group",
        queryset=BusinessGroup.objects.all(),
        to_field_name='id',
        empty_label="Select",
        required=False,
        widget=forms.Select(attrs={'class': 'box3', 'x-show': "woType === 'dept'"}))
    del_date = forms.DateField(
        label="Delivery Date",
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    remarks = forms.CharField(
        label="Remarks",
        required=False,
        widget=forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-16'})
    )
    qty = forms.DecimalField(
        label="Qty",
        min_value=0,
        validators=[validate_decimal_3_places],
        widget=forms.NumberInput(attrs={'class': 'box3', 'step': '0.001'})
    )
    rate = forms.DecimalField(
        label="Rate",
        min_value=0,
        validators=[validate_decimal_2_places],
        initial=0,
        widget=forms.NumberInput(attrs={'class': 'box3', 'step': '0.01'})
    )
    discount = forms.DecimalField(
        label="Discount",
        min_value=0,
        validators=[validate_decimal_3_places],
        initial=0,
        required=False,
        widget=forms.NumberInput(attrs={'class': 'box3', 'step': '0.001'})
    )
    supplier = forms.CharField(
        label="Supplier",
        max_length=255,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Start typing to search...', 'hx-get': '/spr/supplier-autocomplete/', 'hx-trigger': 'keyup changed delay:500ms', 'hx-target': '#supplier-suggestions', 'hx-swap': 'innerHTML'})
    )
    account_head_type = forms.ChoiceField(
        choices=[
            ('labour', 'Labour'),
            ('material', 'With Material'),
            ('expenses', 'Expenses'),
            ('ser_provider', 'Ser. Provider'),
        ],
        widget=forms.RadioSelect(attrs={'x-model': 'accountHeadType', 'hx-get': '/spr/account-head-options/', 'hx-target': '#account-head-dropdown', 'hx-trigger': 'change'}),
        initial='labour',
        label="A/c Head Type"
    )
    account_head = forms.ModelChoiceField(
        queryset=AccountHead.objects.all(), # This will be filtered dynamically
        to_field_name='id',
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    def clean(self):
        cleaned_data = super().clean()
        wo_type = cleaned_data.get('wo_type')
        wo_no = cleaned_data.get('wo_no')
        dept = cleaned_data.get('dept')
        del_date = cleaned_data.get('del_date')
        qty = cleaned_data.get('qty')
        rate = cleaned_data.get('rate')
        supplier = cleaned_data.get('supplier')

        # Mimic C# validation for WO No / Dept
        if wo_type == 'wono':
            if not wo_no:
                self.add_error('wo_no', 'WO No is required.')
            cleaned_data['dept'] = None # Ensure dept is null if wono is chosen
        elif wo_type == 'dept':
            if not dept:
                self.add_error('dept', 'BG Group is required.')
            cleaned_data['wo_no'] = None # Ensure wono is null if dept is chosen

        # Date validation (C# fun.DateValidation)
        if del_date and del_date < date.today(): # Example: future date check
             self.add_error('del_date', 'Delivery Date cannot be in the past.')

        # Supplier validation (C# fun.chkSupplierCode) - this is tricky with autocomplete.
        # It needs to extract the actual ID from the selected string "Name [ID]"
        if supplier:
            match = re.search(r'\[(.*?)\]', supplier)
            if not match:
                self.add_error('supplier', 'Invalid supplier format. Please select from autocomplete suggestions.')
            else:
                supplier_id = match.group(1)
                try:
                    Supplier.objects.get(supplier_id=supplier_id)
                except Supplier.DoesNotExist:
                    self.add_error('supplier', 'Invalid supplier selected.')
        else:
            self.add_error('supplier', 'Supplier is required.')

        # Rate validation (C# Rate > 0)
        if rate is not None and rate <= 0:
            self.add_error('rate', 'Rate must be greater than zero.')

        return cleaned_data

class SPRTempItemFromMasterForm(forms.Form):
    """
    Form for adding details (Qty, Rate, etc.) to an existing ItemMaster item
    selected from the grid, before adding it to SPRTempItem.
    """
    item = forms.ModelChoiceField(
        queryset=ItemMaster.objects.all(),
        widget=forms.HiddenInput(), # Hidden, as it's passed from the selected item
        required=True
    )
    manf_desc = forms.CharField(
        label="Description",
        required=False, # Will be pre-filled from item.manf_desc
        widget=forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-24', 'readonly': 'readonly'})
    )
    uom_basic = forms.ModelChoiceField(
        queryset=Unit.objects.all(),
        required=False, # Will be pre-filled
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'disabled': 'disabled'})
    )
    WO_TYPE_CHOICES = [
        ('wono', 'WO No'),
        ('dept', 'BG Group'),
    ]
    wo_type = forms.ChoiceField(
        choices=WO_TYPE_CHOICES,
        widget=forms.RadioSelect(attrs={'x-model': 'woType'}),
        initial='wono'
    )
    wo_no = forms.CharField(
        label="WO No",
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3', 'x-show': "woType === 'wono'"}))
    dept = forms.ModelChoiceField(
        label="BG Group",
        queryset=BusinessGroup.objects.all(),
        to_field_name='id',
        empty_label="Select",
        required=False,
        widget=forms.Select(attrs={'class': 'box3', 'x-show': "woType === 'dept'"}))
    del_date = forms.DateField(
        label="Delivery Date",
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    remarks = forms.CharField(
        label="Remarks",
        required=False,
        widget=forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-16'})
    )
    qty = forms.DecimalField(
        label="Qty",
        min_value=0,
        validators=[validate_decimal_3_places],
        widget=forms.NumberInput(attrs={'class': 'box3', 'step': '0.001'})
    )
    rate = forms.DecimalField(
        label="Rate",
        min_value=0,
        validators=[validate_decimal_2_places],
        initial=0,
        widget=forms.NumberInput(attrs={'class': 'box3', 'step': '0.01'})
    )
    discount = forms.DecimalField(
        label="Discount",
        min_value=0,
        validators=[validate_decimal_3_places],
        initial=0,
        required=False,
        widget=forms.NumberInput(attrs={'class': 'box3', 'step': '0.001'})
    )
    supplier = forms.CharField(
        label="Supplier",
        max_length=255,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Start typing to search...', 'hx-get': '/spr/supplier-autocomplete/', 'hx-trigger': 'keyup changed delay:500ms', 'hx-target': '#supplier-suggestions', 'hx-swap': 'innerHTML'})
    )
    account_head_type = forms.ChoiceField(
        choices=[
            ('labour', 'Labour'),
            ('material', 'With Material'),
            ('expenses', 'Expenses'),
            ('ser_provider', 'Ser. Provider'),
        ],
        widget=forms.RadioSelect(attrs={'x-model': 'accountHeadType', 'hx-get': '/spr/account-head-options/', 'hx-target': '#account-head-dropdown', 'hx-trigger': 'change'}),
        initial='labour',
        label="A/c Head Type"
    )
    account_head = forms.ModelChoiceField(
        queryset=AccountHead.objects.all(), # This will be filtered dynamically
        to_field_name='id',
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    def __init__(self, *args, **kwargs):
        item_master = kwargs.pop('item_master', None)
        super().__init__(*args, **kwargs)
        if item_master:
            self.initial['item'] = item_master
            self.initial['manf_desc'] = item_master.manf_desc
            self.initial['uom_basic'] = item_master.uom_basic.id if item_master.uom_basic else None
            # Disable UOM and description fields, they are pre-filled
            self.fields['uom_basic'].widget.attrs['disabled'] = True
            self.fields['manf_desc'].widget.attrs['readonly'] = True
        
        # Initial account head population for the dynamic dropdown
        self.fields['account_head'].queryset = AccountHead.objects.filter(symbol='LBR') # Default Labour


    def clean(self):
        cleaned_data = super().clean()
        wo_type = cleaned_data.get('wo_type')
        wo_no = cleaned_data.get('wo_no')
        dept = cleaned_data.get('dept')
        del_date = cleaned_data.get('del_date')
        qty = cleaned_data.get('qty')
        rate = cleaned_data.get('rate')
        supplier = cleaned_data.get('supplier')

        # Mimic C# validation for WO No / Dept
        if wo_type == 'wono':
            if not wo_no:
                self.add_error('wo_no', 'WO No is required.')
            cleaned_data['dept'] = None
        elif wo_type == 'dept':
            if not dept:
                self.add_error('dept', 'BG Group is required.')
            cleaned_data['wo_no'] = None

        if del_date and del_date < date.today():
             self.add_error('del_date', 'Delivery Date cannot be in the past.')

        if supplier:
            match = re.search(r'\[(.*?)\]', supplier)
            if not match:
                self.add_error('supplier', 'Invalid supplier format. Please select from autocomplete suggestions.')
            else:
                supplier_id = match.group(1)
                try:
                    Supplier.objects.get(supplier_id=supplier_id)
                except Supplier.DoesNotExist:
                    self.add_error('supplier', 'Invalid supplier selected.')
        else:
            self.add_error('supplier', 'Supplier is required.')

        if rate is not None and rate <= 0:
            self.add_error('rate', 'Rate must be greater than zero.')

        return cleaned_data

```

#### 4.3 Views (`spr_app/views.py`)

**Task:** Implement CRUD operations using Django Class-Based Views (CBVs), ensuring thin views and business logic in models. Create HTMX-specific partial views for dynamic content.

**Instructions:**
A main `SPRNewView` will render the overall page. Additional views will handle HTMX requests for searching items, adding/deleting temp items, and autocomplete.

```python
from django.views.generic import TemplateView, View
from django.shortcuts import render, get_object_or_404
from django.http import HttpResponse, JsonResponse
from django.urls import reverse_lazy
from django.contrib import messages
from django.db.models import Q # For complex queries

from .models import (
    SPRTempItem, ItemMaster, Supplier, Unit, AccountHead, BusinessGroup,
    Category, Location, SPRManager
)
from .forms import ItemMasterSearchForm, SPRTempItemNoCodeForm, SPRTempItemFromMasterForm

class SPRNewView(TemplateView):
    """
    Main view for the SPR - New page, orchestrating all components.
    This replaces the overall SPR_New.aspx functionality.
    """
    template_name = 'spr_app/spr_new.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Assuming user and company/financial year are available through request.user
        user = self.request.user
        comp_id = user.company_id if hasattr(user, 'company_id') else 1 # Placeholder
        session_id = user.username if hasattr(user, 'username') else 'test_session' # Placeholder

        context['item_master_search_form'] = ItemMasterSearchForm()
        context['spr_temp_item_nocode_form'] = SPRTempItemNoCodeForm(initial={'account_head_type': 'labour', 'wo_type': 'wono'})
        
        # Initial load of selected items grid
        context['spr_temp_items'] = SPRTempItem.objects.filter(
            comp_id=comp_id,
            session_id=session_id
        ).order_by('-id')
        
        # Initial Item Master grid load (empty or default search)
        context['item_masters'] = ItemMaster.objects.none()

        return context

# --- HTMX Partial Views ---

class ItemMasterTablePartialView(View):
    """
    Loads and renders the Item Master table (GridView2 replacement).
    Responds to search form submissions or initial page load.
    """
    def get(self, request, *args, **kwargs):
        user = request.user
        comp_id = user.company_id if hasattr(user, 'company_id') else 1
        fin_year_id = user.financial_year_id if hasattr(user, 'financial_year_id') else 1

        form = ItemMasterSearchForm(request.GET)
        item_masters = ItemMaster.objects.none()

        if form.is_valid():
            type_val = form.cleaned_data.get('type')
            category_val = form.cleaned_data.get('category')
            search_code_val = form.cleaned_data.get('search_code')
            search_value_text = form.cleaned_data.get('search_value_text')
            search_value_location = form.cleaned_data.get('search_value_location')
            
            # Mimic C# Fillgrid logic with dynamic query construction
            queryset = ItemMaster.objects.filter(comp_id=comp_id, fin_year_id__lte=fin_year_id)

            if type_val == 'Category' and category_val:
                queryset = queryset.filter(cid=category_val)
                if search_code_val == 'ItemCode' and search_value_text:
                    queryset = queryset.filter(item_code__startswith=search_value_text)
                elif search_code_val == 'ManfDesc' and search_value_text:
                    queryset = queryset.filter(manf_desc__icontains=search_value_text)
                elif search_code_val == 'Location' and search_value_location:
                    queryset = queryset.filter(location=search_value_location)
            elif type_val == 'WOItems':
                if search_code_val == 'ItemCode' and search_value_text:
                    queryset = queryset.filter(item_code__icontains=search_value_text)
                elif search_code_val == 'ManfDesc' and search_value_text:
                    queryset = queryset.filter(manf_desc__icontains=search_value_text)
            elif not type_val and search_value_text: # General search if type is not selected
                queryset = queryset.filter(Q(manf_desc__icontains=search_value_text) | Q(item_code__icontains=search_value_text))
            
            item_masters = queryset.distinct() # Ensure distinct results

        return render(request, 'spr_app/partials/_item_master_table.html', {'item_masters': item_masters})

class ItemMasterSearchOptionsView(View):
    """
    Dynamically updates dropdowns for Item Master search based on 'type' selection.
    """
    def get(self, request, *args, **kwargs):
        type_val = request.GET.get('type', '')
        
        context = {
            'categories': Category.objects.all().order_by('cname') if type_val == 'Category' else Category.objects.none(),
            'locations': Location.objects.all().order_by('location_label') if type_val == 'Category' else Location.objects.none(),
            'type_selected': type_val
        }
        return render(request, 'spr_app/partials/_item_master_search_dynamic_fields.html', context)

class ItemMasterSearchValueFieldView(View):
    """
    Dynamically updates the search value input field (text or dropdown for location)
    based on 'search_code' selection.
    """
    def get(self, request, *args, **kwargs):
        search_code = request.GET.get('search_code', '')
        type_val = request.GET.get('type', '') # Needed to determine if Location is valid option
        
        context = {
            'search_code': search_code,
            'locations': Location.objects.all().order_by('location_label')
        }
        return render(request, 'spr_app/partials/_item_master_search_value_field.html', context)


class SPRTempItemListView(View):
    """
    Loads and renders the Selected Items table (GridView3 replacement).
    Responds to HTMX triggers for refresh.
    """
    def get(self, request, *args, **kwargs):
        user = request.user
        comp_id = user.company_id if hasattr(user, 'company_id') else 1
        session_id = user.username if hasattr(user, 'username') else 'test_session'

        spr_temp_items = SPRTempItem.objects.filter(
            comp_id=comp_id,
            session_id=session_id
        ).order_by('-id') # Order by Id Desc as in C# LoadData

        # Apply get_display_data for formatted output
        display_items = [item.get_display_data() for item in spr_temp_items]

        return render(request, 'spr_app/partials/_selected_items_table.html', {'spr_temp_items': display_items})


class SPRTempItemNoCodeCreateView(View):
    """
    Handles POST requests for adding 'No Code Item' to SPRTempItem.
    """
    def post(self, request, *args, **kwargs):
        form = SPRTempItemNoCodeForm(request.POST)
        if form.is_valid():
            try:
                SPRTempItem.create_spr_item(user=request.user, form_data=form.cleaned_data, is_nocode=True)
                messages.success(request, 'No Code Item added successfully.')
                # Clear form fields after successful submission (Hx-reset or re-render empty form)
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshSPRTempList, resetNoCodeItemForm'
                    }
                )
            except ValueError as e:
                messages.error(request, str(e))
        else:
            messages.error(request, 'Please correct the errors in the form.')
        
        # Render the form again with errors for HTMX swap
        # Need to re-populate dynamic elements like account_head queryset if it changed
        form_context = {
            'spr_temp_item_nocode_form': form,
            'account_head_options': AccountHead.objects.all() # Or filter by type
        }
        return render(request, 'spr_app/partials/_no_code_item_form.html', form_context)

class SPRTempItemFromMasterCreateView(View):
    """
    Handles GET (for modal) and POST (for submission) for adding ItemMaster item details
    to SPRTempItem.
    """
    def get(self, request, item_id):
        item_master = get_object_or_404(ItemMaster, pk=item_id)
        form = SPRTempItemFromMasterForm(item_master=item_master)
        return render(request, 'spr_app/partials/_add_item_details_modal.html', {'form': form, 'item_master': item_master})

    def post(self, request, item_id):
        item_master = get_object_or_404(ItemMaster, pk=item_id)
        form = SPRTempItemFromMasterForm(request.POST, item_master=item_master)
        if form.is_valid():
            try:
                SPRTempItem.create_spr_item(user=request.user, form_data=form.cleaned_data, is_nocode=False)
                messages.success(request, f'Item "{item_master.item_code}" added to temporary list.')
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshSPRTempList, closeModal'
                    }
                )
            except ValueError as e:
                messages.error(request, str(e))
        else:
            messages.error(request, 'Please correct the errors in the form.')
        
        return render(request, 'spr_app/partials/_add_item_details_modal.html', {'form': form, 'item_master': item_master})


class SPRTempItemDeleteView(View):
    """
    Handles GET (for confirmation modal) and DELETE (for deletion) of SPRTempItem.
    """
    def get(self, request, pk):
        item = get_object_or_404(SPRTempItem, pk=pk)
        return render(request, 'spr_app/partials/_confirm_delete_modal.html', {'object': item})

    def post(self, request, pk): # Using POST for HTMX delete, will trigger DELETE method internally
        user = request.user
        comp_id = user.company_id if hasattr(user, 'company_id') else 1
        session_id = user.username if hasattr(user, 'username') else 'test_session'

        item = get_object_or_404(SPRTempItem, pk=pk, comp_id=comp_id, session_id=session_id)
        item.delete()
        messages.success(request, 'Item removed from temporary list.')
        return HttpResponse(
            status=204,
            headers={
                'HX-Trigger': 'refreshSPRTempList, closeModal'
            }
        )

class SupplierAutocompleteView(View):
    """
    Provides supplier name suggestions for the autocomplete functionality via HTMX.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        if not query:
            return HttpResponse("")

        comp_id = request.user.company_id if hasattr(request.user, 'company_id') else 1 # Placeholder

        # Filter suppliers based on prefix text and company ID
        # C# original was doing StartsWith(prefixText.ToLower()) for supplier name.
        # It also implicitly filters by compid (fun.select("SupplierId,SupplierName", "tblMM_Supplier_master","CompId='"+CompId1+"'");)
        suppliers = Supplier.objects.filter(
            supplier_name__istartswith=query, # Case-insensitive starts with
            # comp_id=comp_id # Uncomment if Supplier has comp_id
        ).values('supplier_id', 'supplier_name')[:10] # Limit to 10 results as in C#

        # Format: "SupplierName [SupplierId]"
        suggestions = [f"{s['supplier_name']} [{s['supplier_id']}]" for s in suppliers]
        
        # HTMX typically expects HTML fragments or JSON for complex responses.
        # For a simple text list, a JSON array or HTML list is common.
        # For simplicity, returning a JSON array, which Alpine.js or custom JS can consume
        # or HTMX can swap into a list.
        return JsonResponse(suggestions, safe=False)

class AccountHeadOptionsView(View):
    """
    Dynamically loads Account Head options based on the selected type (radio buttons).
    Mimics fun.AcHead logic.
    """
    def get(self, request, *args, **kwargs):
        acc_type = request.GET.get('type', 'labour') # Default to labour
        
        # This mapping should ideally be configured in models or a constants file
        # C# fun.AcHead logic:
        # RbtnLabour -> filter for Symbol='LBR'
        # RbtnWithMaterial -> filter for Symbol='MAT'
        # RbtnExpenses -> filter for Symbol='EXP'
        # RbtnSerProvider -> filter for Symbol='SER'
        symbol_map = {
            'labour': 'LBR',
            'material': 'MAT',
            'expenses': 'EXP',
            'ser_provider': 'SER',
        }
        
        symbol_filter = symbol_map.get(acc_type, 'LBR') # Default to LBR if type not found
        
        account_heads = AccountHead.objects.filter(symbol=symbol_filter).order_by('description')
        
        context = {
            'account_heads': account_heads
        }
        return render(request, 'spr_app/partials/_account_head_dropdown_options.html', context)


class SPRProceedView(View):
    """
    Handles the 'Proceed' button click to finalize the SPR.
    Encapsulates the complex logic from Button1_Click in SPRManager.
    """
    def post(self, request, *args, **kwargs):
        user = request.user
        try:
            new_spr_no = SPRManager.finalize_spr(user)
            messages.success(request, f'SPR {new_spr_no} created successfully and temporary items cleared.')
            # After successful finalize, reload the entire page or redirect to a dashboard
            # C# reloads the same page, implying clearing the temporary grid.
            return HttpResponse(
                status=204, # No content, tells HTMX to do nothing directly
                headers={
                    'HX-Redirect': reverse_lazy('spr_new') # Redirect to clear everything
                }
            )
        except ValueError as e:
            messages.error(request, str(e))
        except Exception as e:
            messages.error(request, f'An unexpected error occurred: {e}')
        
        # If there's an error, re-render main view to show messages
        # Or trigger HTMX to swap messages container
        return HttpResponse(status=400) # Indicate an error occurred. HTMX can catch this.


class SPRCancelView(View):
    """
    Handles the 'Cancel' button, redirecting to the dashboard.
    """
    def get(self, request, *args, **kwargs):
        # This is a simple redirect, not an HTMX interaction, so standard HttpResponseRedirect
        return HttpResponse(
            status=204,
            headers={
                'HX-Redirect': reverse_lazy('spr_dashboard') # Assuming 'spr_dashboard' URL exists
            }
        )

```

#### 4.4 Templates (`spr_app/templates/spr_app/`)

**Task:** Create main and partial templates for all views, leveraging HTMX, Alpine.js, and DataTables.

**Instructions:**
*   `spr_new.html`: Main page structure.
*   `partials/_item_master_search_form.html`: Contains search controls.
*   `partials/_item_master_search_dynamic_fields.html`: For dynamic category/location visibility.
*   `partials/_item_master_search_value_field.html`: For dynamic text/dropdown for search value.
*   `partials/_item_master_table.html`: The DataTables for `GridView2`.
*   `partials/_no_code_item_form.html`: The form for `TabPanel2`.
*   `partials/_selected_items_table.html`: The DataTables for `GridView3`.
*   `partials/_add_item_details_modal.html`: Modal content for adding ItemMaster items.
*   `partials/_confirm_delete_modal.html`: Modal content for delete confirmation.
*   `partials/_account_head_dropdown_options.html`: For dynamic account head dropdown options.

```html
<!-- spr_app/templates/spr_app/spr_new.html -->
{% extends 'core/base.html' %}

{% block title %}SPR - New{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ activeTab: 'itemMaster', showModal: false, modalContent: '' }"
     @refreshSPRTempList.window="document.getElementById('sprTempItemsContainer')._x_dataStack[0].refreshTable()"
     @closeModal.window="showModal = false; modalContent = ''"
     @resetNoCodeItemForm.window="$nextTick(() => { document.getElementById('noCodeItemForm').reset(); })"
     >

    <h2 class="text-2xl font-bold mb-6">SPR - New</h2>

    <!-- Messages Container -->
    <div id="messages-container" class="mb-4">
        {% if messages %}
            {% for message in messages %}
                <div class="p-3 mb-2 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-700{% elif message.tags == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}
    </div>

    <!-- Tab Navigation -->
    <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-8" aria-label="Tabs">
            <button @click="activeTab = 'itemMaster'" :class="{'border-indigo-500 text-indigo-600': activeTab === 'itemMaster', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'itemMaster'}"
                class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                Item Master
            </button>
            <button @click="activeTab = 'noCodeItem'" :class="{'border-indigo-500 text-indigo-600': activeTab === 'noCodeItem', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'noCodeItem'}"
                class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                No Code Item
            </button>
            <button @click="activeTab = 'selectedItems'" :class="{'border-indigo-500 text-indigo-600': activeTab === 'selectedItems', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'selectedItems'}"
                class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                Selected Items
            </button>
        </nav>
    </div>

    <!-- Tab Content -->
    <div class="mt-8">
        <!-- Item Master Tab Content -->
        <div x-show="activeTab === 'itemMaster'" class="bg-white shadow-md rounded-lg p-6">
            <h3 class="text-xl font-semibold mb-4">Search Item Master</h3>
            <form hx-get="{% url 'item_master_table_partial' %}" hx-target="#itemMasterTableContainer" hx-swap="innerHTML" hx-indicator="#item-master-loading-indicator">
                {% csrf_token %}
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                    <div>
                        <label for="id_type" class="block text-sm font-medium text-gray-700">Type</label>
                        {{ item_master_search_form.type }}
                    </div>
                    <div id="item-master-dynamic-fields" hx-target="this" hx-swap="outerHTML">
                        <!-- Dynamic fields (Category, Search Code) will be loaded here -->
                        {% include 'spr_app/partials/_item_master_search_dynamic_fields.html' with categories=None locations=None type_selected=None %}
                    </div>
                    <div id="item-master-search-value-container" hx-target="this" hx-swap="outerHTML">
                        <!-- Search value input (text or dropdown) will be loaded here -->
                        {% include 'spr_app/partials/_item_master_search_value_field.html' with search_code=None locations=None %}
                    </div>
                </div>
                <div class="flex justify-end mt-4">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Search</button>
                </div>
            </form>

            <div id="itemMasterTableContainer" class="mt-6"
                 hx-trigger="load, searchItemMaster from:body"
                 hx-get="{% url 'item_master_table_partial' %}"
                 hx-swap="innerHTML">
                <!-- Item Master DataTables will be loaded here via HTMX -->
                <div id="item-master-loading-indicator" class="htmx-indicator text-center">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2">Loading Item Master...</p>
                </div>
            </div>
        </div>

        <!-- No Code Item Tab Content -->
        <div x-show="activeTab === 'noCodeItem'" class="bg-white shadow-md rounded-lg p-6">
            <h3 class="text-xl font-semibold mb-4">Add No Code Item</h3>
            <form id="noCodeItemForm" hx-post="{% url 'spr_temp_item_nocode_add' %}" hx-swap="outerHTML" hx-target="#noCodeItemForm">
                {% csrf_token %}
                {% include 'spr_app/partials/_no_code_item_form.html' with spr_temp_item_nocode_form=spr_temp_item_nocode_form %}
                <div class="flex justify-end mt-6">
                    <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">Add Item</button>
                </div>
            </form>
        </div>

        <!-- Selected Items Tab Content -->
        <div x-show="activeTab === 'selectedItems'" x-data="sprTempItemsData()" class="bg-white shadow-md rounded-lg p-6">
            <h3 class="text-xl font-semibold mb-4">Selected Items</h3>
            <div id="sprTempItemsContainer"
                 hx-trigger="load, refreshSPRTempList from:body"
                 hx-get="{% url 'spr_temp_item_list_partial' %}"
                 hx-swap="innerHTML">
                <!-- Selected Items DataTables will be loaded here via HTMX -->
                <div class="htmx-indicator text-center">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2">Loading Selected Items...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Proceed/Cancel Buttons -->
    <div class="mt-8 flex justify-end space-x-4">
        <button
            class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
            hx-post="{% url 'spr_proceed' %}"
            hx-confirm="Are you sure you want to proceed and finalize the SPR?"
            hx-indicator="#loading-spinner"
            hx-swap="none"
            >
            Proceed
        </button>
        <button
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            hx-get="{% url 'spr_cancel' %}"
            hx-swap="none"
            >
            Cancel
        </button>
        <div id="loading-spinner" class="htmx-indicator ml-3">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
    </div>

    <!-- Modal for forms (Add Item Details, Delete Confirmation) -->
    <div x-show="showModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50"
         @click.self="showModal = false; modalContent = ''">
        <div class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full" @click.stop="">
            <div x-html="modalContent"></div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- DataTables JS and initialization for dynamic content -->
<script src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.3/js/dataTables.tailwindcss.min.js"></script>
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('sprTempItemsData', () => ({
            refreshTable() {
                // Manually trigger HTMX request for selected items table
                htmx.trigger(document.getElementById('sprTempItemsContainer'), 'refreshSPRTempList');
            }
        }));

        Alpine.data('noCodeItemFormData', () => ({
            woType: 'wono', // Default for WO No / BG Group
            accountHeadType: 'labour', // Default for Account Head
            init() {
                // Initialize default values for radio buttons if form is rendered initially
                // and Alpine.js picks it up.
            }
        }));
    });

    // Handle HTMX afterSwap to initialize DataTables on dynamically loaded content
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'itemMasterTableContainer') {
            $('#itemMasterTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true // Destroy existing DataTable before re-initializing
            });
        }
        if (event.detail.target.id === 'sprTempItemsContainer') {
            $('#sprTempItemsTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true // Destroy existing DataTable before re-initializing
            });
        }

        // Reinitialize Alpine.js on the form if it was swapped
        if (event.detail.target.id === 'noCodeItemForm') {
            Alpine.initTree(event.detail.target);
        }
    });

    // Event listener for opening modals via HTMX
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'modalContent') {
            document.querySelector('[x-data]').__alpine_data_registry.get('root').showModal = true;
            Alpine.initTree(event.detail.target); // Re-initialize Alpine on modal content
        }
    });

    // Close modal via Alpine.js when triggered by HX-Trigger
    document.body.addEventListener('closeModal', function() {
        document.querySelector('[x-data]').__alpine_data_registry.get('root').showModal = false;
        document.querySelector('[x-data]').__alpine_data_registry.get('root').modalContent = '';
    });

    // Reset No Code Item Form after successful submission
    document.body.addEventListener('resetNoCodeItemForm', function() {
        // Clear all form fields
        $('#noCodeItemForm')[0].reset();
        // Manually trigger Alpine.js data reset if needed for specific values
        // For radio buttons, Alpine.js x-model should re-apply initial state after reset
        // Or re-render the form.
    });
</script>
{% endblock %}
```

```html
<!-- spr_app/templates/spr_app/partials/_item_master_search_form.html -->
<!-- This partial would ideally contain the whole form if it were a standalone HTMX loadable part -->
<!-- For this setup, the form fields are in spr_new.html, and only the dynamic parts are in separate partials. -->
<!-- This partial is not used directly as a top-level form, but its fields are rendered in spr_new.html -->
```

```html
<!-- spr_app/templates/spr_app/partials/_item_master_search_dynamic_fields.html -->
<div id="item-master-dynamic-fields">
    {% if type_selected == 'Category' %}
        <div>
            <label for="id_category" class="block text-sm font-medium text-gray-700">Category</label>
            <select name="category" id="id_category" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" hx-get="{% url 'item_master_search_value_field' %}" hx-target="#item-master-search-value-container" hx-swap="outerHTML" hx-indicator=".htmx-indicator" hx-vals="js:{type: document.getElementById('id_type').value}">
                <option value="">Select</option>
                {% for category in categories %}
                    <option value="{{ category.cid }}">{{ category }}</option>
                {% endfor %}
            </select>
        </div>
        <div>
            <label for="id_search_code" class="block text-sm font-medium text-gray-700">Search By</label>
            <select name="search_code" id="id_search_code" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" hx-get="{% url 'item_master_search_value_field' %}" hx-target="#item-master-search-value-container" hx-swap="outerHTML" hx-indicator=".htmx-indicator" hx-vals="js:{type: document.getElementById('id_type').value, category: document.getElementById('id_category').value}">
                <option value="">Select</option>
                <option value="ItemCode">Item Code</option>
                <option value="ManfDesc">Description</option>
                <option value="Location">Location</option>
            </select>
        </div>
    {% elif type_selected == 'WOItems' %}
        <div>
            <label for="id_category" class="block text-sm font-medium text-gray-700">Category</label>
            <select name="category" id="id_category" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" disabled>
                <option value="">Select</option>
            </select>
        </div>
        <div>
            <label for="id_search_code" class="block text-sm font-medium text-gray-700">Search By</label>
            <select name="search_code" id="id_search_code" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" hx-get="{% url 'item_master_search_value_field' %}" hx-target="#item-master-search-value-container" hx-swap="outerHTML" hx-indicator=".htmx-indicator" hx-vals="js:{type: document.getElementById('id_type').value}">
                <option value="">Select</option>
                <option value="ItemCode">Item Code</option>
                <option value="ManfDesc">Description</option>
            </select>
        </div>
    {% else %}
        <!-- Default/Empty state -->
        <div>
            <label for="id_category" class="block text-sm font-medium text-gray-700">Category</label>
            <select name="category" id="id_category" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" disabled>
                <option value="">Select</option>
            </select>
        </div>
        <div>
            <label for="id_search_code" class="block text-sm font-medium text-gray-700">Search By</label>
            <select name="search_code" id="id_search_code" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" disabled>
                <option value="">Select</option>
            </select>
        </div>
    {% endif %}
</div>
```

```html
<!-- spr_app/templates/spr_app/partials/_item_master_search_value_field.html -->
<div id="item-master-search-value-container">
    {% if search_code == 'Location' %}
        <div>
            <label for="id_search_value_location" class="block text-sm font-medium text-gray-700">Location</label>
            <select name="search_value_location" id="id_search_value_location" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                <option value="">Select</option>
                {% for location in locations %}
                    <option value="{{ location.id }}">{{ location }}</option>
                {% endfor %}
            </select>
        </div>
    {% elif search_code == 'ItemCode' or search_code == 'ManfDesc' %}
        <div>
            <label for="id_search_value_text" class="block text-sm font-medium text-gray-700">Search Value</label>
            <input type="text" name="search_value_text" id="id_search_value_text" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
        </div>
    {% else %}
        <!-- Default/Empty state or text input -->
        <div>
            <label for="id_search_value_text" class="block text-sm font-medium text-gray-700">Search Value</label>
            <input type="text" name="search_value_text" id="id_search_value_text" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
        </div>
    {% endif %}
</div>
```

```html
<!-- spr_app/templates/spr_app/partials/_item_master_table.html -->
<table id="itemMasterTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Stock Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for item in item_masters %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.item_code }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.manf_desc }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.uom_basic.symbol }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.stock_qty|floatformat:"3" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.location }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'spr_temp_item_from_master_add' item.id %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal, set @showModal to true on #modal"
                    >
                    Select
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="7" class="py-4 px-4 text-center text-gray-500">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// DataTables initialization will be handled by htmx:afterSwap in spr_new.html
</script>
```

```html
<!-- spr_app/templates/spr_app/partials/_no_code_item_form.html -->
<div x-data="noCodeItemFormData()" class="space-y-4">
    {% for field in spr_temp_item_nocode_form %}
    <div class="mb-4">
        <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ field.label }}
            {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
        </label>
        {% if field.name == 'wo_type' %}
            <div class="flex space-x-4">
                {% for radio in field %}
                    <label class="inline-flex items-center">
                        {{ radio.tag }}
                        <span class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</span>
                    </label>
                {% endfor %}
            </div>
            {% if spr_temp_item_nocode_form.wo_no.errors %}<p class="text-red-500 text-xs mt-1">{{ spr_temp_item_nocode_form.wo_no.errors }}</p>{% endif %}
            {% if spr_temp_item_nocode_form.dept.errors %}<p class="text-red-500 text-xs mt-1">{{ spr_temp_item_nocode_form.dept.errors }}</p>{% endif %}
        {% elif field.name == 'wo_no' or field.name == 'dept' %}
            {% if field.name == 'wo_no' %}
                <div x-show="woType === 'wono'">
                    {{ field }}
                </div>
            {% else %} {# field.name == 'dept' #}
                <div x-show="woType === 'dept'">
                    {{ field }}
                </div>
            {% endif %}
        {% elif field.name == 'account_head_type' %}
            <div class="flex space-x-4">
                {% for radio in field %}
                    <label class="inline-flex items-center">
                        {{ radio.tag }}
                        <span class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</span>
                    </label>
                {% endfor %}
            </div>
            {% if spr_temp_item_nocode_form.account_head.errors %}<p class="text-red-500 text-xs mt-1">{{ spr_temp_item_nocode_form.account_head.errors }}</p>{% endif %}
        {% elif field.name == 'account_head' %}
            <div id="account-head-dropdown">
                {{ field }}
            </div>
        {% elif field.name == 'supplier' %}
            {{ field }}
            <div id="supplier-suggestions" class="bg-white border border-gray-200 rounded-md shadow-lg z-10 max-h-48 overflow-y-auto"></div>
        {% else %}
            {{ field }}
        {% endif %}
        
        {% if field.errors %}
        <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
        {% endif %}
    </div>
    {% endfor %}
</div>
```

```html
<!-- spr_app/templates/spr_app/partials/_selected_items_table.html -->
<table id="sprTempItemsTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">No Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Del. Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">A/c Head</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Dept</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Disc</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for item in spr_temp_items %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.item_code }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.nocode_str }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.description }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.uom_basic }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.del_date }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.account_head }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.wo_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.dept }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.qty }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.rate }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.discount }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.remarks }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                <button
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'spr_temp_item_delete' item.id %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal, set @showModal to true on #modal"
                    >
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="14" class="py-4 px-4 text-center text-gray-500">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// DataTables initialization will be handled by htmx:afterSwap in spr_new.html
</script>
```

```html
<!-- spr_app/templates/spr_app/partials/_add_item_details_modal.html -->
<div class="p-6" x-data="noCodeItemFormData()"> {# Reusing noCodeItemFormData for WO/Dept and AccHead logic #}
    <h3 class="text-lg font-medium text-gray-900 mb-5">Add Details for Item: {{ item_master.item_code }}</h3>
    <form hx-post="{% url 'spr_temp_item_from_master_add' item_master.id %}" hx-swap="outerHTML" hx-target="#modalContent">
        {% csrf_token %}
        
        <div class="space-y-4">
            {{ form.item }} {# Hidden field for item ID #}

            <div class="mb-4">
                <label for="{{ form.manf_desc.id_for_label }}" class="block text-sm font-medium text-gray-700">Description</label>
                {{ form.manf_desc }}
                {% if form.manf_desc.errors %}<p class="text-red-500 text-xs mt-1">{{ form.manf_desc.errors }}</p>{% endif %}
            </div>

            <div class="mb-4">
                <label for="{{ form.uom_basic.id_for_label }}" class="block text-sm font-medium text-gray-700">UOM</label>
                {{ form.uom_basic }}
                {% if form.uom_basic.errors %}<p class="text-red-500 text-xs mt-1">{{ form.uom_basic.errors }}</p>{% endif %}
            </div>

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700">WO No / BG Group</label>
                <div class="flex space-x-4">
                    {% for radio in form.wo_type %}
                        <label class="inline-flex items-center">
                            {{ radio.tag }}
                            <span class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</span>
                        </label>
                    {% endfor %}
                </div>
                {% if form.wo_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.wo_no.errors }}</p>{% endif %}
                {% if form.dept.errors %}<p class="text-red-500 text-xs mt-1">{{ form.dept.errors }}</p>{% endif %}
            </div>

            <div class="mb-4">
                {% if form.wo_no %}
                <div x-show="woType === 'wono'">
                    <label for="{{ form.wo_no.id_for_label }}" class="block text-sm font-medium text-gray-700">WO No</label>
                    {{ form.wo_no }}
                </div>
                {% endif %}
                {% if form.dept %}
                <div x-show="woType === 'dept'">
                    <label for="{{ form.dept.id_for_label }}" class="block text-sm font-medium text-gray-700">BG Group</label>
                    {{ form.dept }}
                </div>
                {% endif %}
            </div>

            <div class="mb-4">
                <label for="{{ form.del_date.id_for_label }}" class="block text-sm font-medium text-gray-700">Delivery Date</label>
                {{ form.del_date }}
                {% if form.del_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.del_date.errors }}</p>{% endif %}
            </div>

            <div class="mb-4">
                <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700">Remarks</label>
                {{ form.remarks }}
                {% if form.remarks.errors %}<p class="text-red-500 text-xs mt-1">{{ form.remarks.errors }}</p>{% endif %}
            </div>

            <div class="mb-4 grid grid-cols-3 gap-4">
                <div>
                    <label for="{{ form.qty.id_for_label }}" class="block text-sm font-medium text-gray-700">Qty</label>
                    {{ form.qty }}
                    {% if form.qty.errors %}<p class="text-red-500 text-xs mt-1">{{ form.qty.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.rate.id_for_label }}" class="block text-sm font-medium text-gray-700">Rate</label>
                    {{ form.rate }}
                    {% if form.rate.errors %}<p class="text-red-500 text-xs mt-1">{{ form.rate.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.discount.id_for_label }}" class="block text-sm font-medium text-gray-700">Discount</label>
                    {{ form.discount }}
                    {% if form.discount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.discount.errors }}</p>{% endif %}
                </div>
            </div>

            <div class="mb-4">
                <label for="{{ form.supplier.id_for_label }}" class="block text-sm font-medium text-gray-700">Supplier</label>
                {{ form.supplier }}
                <div id="supplier-suggestions" class="bg-white border border-gray-200 rounded-md shadow-lg z-10 max-h-48 overflow-y-auto"></div>
                {% if form.supplier.errors %}<p class="text-red-500 text-xs mt-1">{{ form.supplier.errors }}</p>{% endif %}
            </div>

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700">A/c Head Type</label>
                <div class="flex space-x-4">
                    {% for radio in form.account_head_type %}
                        <label class="inline-flex items-center">
                            {{ radio.tag }}
                            <span class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</span>
                        </label>
                    {% endfor %}
                </div>
                {% if form.account_head.errors %}<p class="text-red-500 text-xs mt-1">{{ form.account_head.errors }}</p>{% endif %}
            </div>
            
            <div class="mb-4">
                <label for="{{ form.account_head.id_for_label }}" class="block text-sm font-medium text-gray-700">Account Head</label>
                <div id="account-head-dropdown">
                    {{ form.account_head }}
                </div>
            </div>

        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click call (document.querySelector('[x-data]')).__alpine_data_registry.get('root').showModal = false; (document.querySelector('[x-data]')).__alpine_data_registry.get('root').modalContent = ''"
                >
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Add to List
            </button>
        </div>
    </form>
</div>
```

```html
<!-- spr_app/templates/spr_app/partials/_confirm_delete_modal.html -->
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete this item from the temporary list?</p>
    
    <div class="flex justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click call (document.querySelector('[x-data]')).__alpine_data_registry.get('root').showModal = false; (document.querySelector('[x-data]')).__alpine_data_registry.get('root').modalContent = ''"
            >
            Cancel
        </button>
        <button 
            type="button" 
            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
            hx-post="{% url 'spr_temp_item_delete' object.id %}"
            hx-swap="none"
            >
            Delete
        </button>
    </div>
</div>
```

```html
<!-- spr_app/templates/spr_app/partials/_account_head_dropdown_options.html -->
<select name="account_head" id="id_account_head" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
    <option value="">Select</option>
    {% for ah in account_heads %}
        <option value="{{ ah.id }}">{{ ah }}</option>
    {% endfor %}
</select>
{% if form.account_head.errors %}<p class="text-red-500 text-xs mt-1">{{ form.account_head.errors }}</p>{% endif %}

```

#### 4.5 URLs (`spr_app/urls.py`)

**Task:** Define URL patterns for the views, including HTMX-specific endpoints.

**Instructions:**
Create paths for the main view, item master table updates, adding temp items (both types), deleting temp items, supplier autocomplete, and account head options.

```python
from django.urls import path
from .views import (
    SPRNewView,
    ItemMasterTablePartialView, ItemMasterSearchOptionsView, ItemMasterSearchValueFieldView,
    SPRTempItemListView, SPRTempItemNoCodeCreateView, SPRTempItemFromMasterCreateView,
    SPRTempItemDeleteView, SupplierAutocompleteView, AccountHeadOptionsView,
    SPRProceedView, SPRCancelView
)

urlpatterns = [
    path('spr/new/', SPRNewView.as_view(), name='spr_new'),
    
    # HTMX endpoints for Item Master search/table
    path('spr/item-master-table/', ItemMasterTablePartialView.as_view(), name='item_master_table_partial'),
    path('spr/item-master-search-options/', ItemMasterSearchOptionsView.as_view(), name='item_master_search_options'),
    path('spr/item-master-search-value-field/', ItemMasterSearchValueFieldView.as_view(), name='item_master_search_value_field'),

    # HTMX endpoints for SPR Temporary Items
    path('spr/temp-items/', SPRTempItemListView.as_view(), name='spr_temp_item_list_partial'),
    path('spr/temp-items/add-nocode/', SPRTempItemNoCodeCreateView.as_view(), name='spr_temp_item_nocode_add'),
    path('spr/temp-items/add-from-master/<int:item_id>/', SPRTempItemFromMasterCreateView.as_view(), name='spr_temp_item_from_master_add'),
    path('spr/temp-items/delete/<int:pk>/', SPRTempItemDeleteView.as_view(), name='spr_temp_item_delete'),

    # HTMX endpoints for dynamic dropdowns and autocomplete
    path('spr/supplier-autocomplete/', SupplierAutocompleteView.as_view(), name='supplier_autocomplete'),
    path('spr/account-head-options/', AccountHeadOptionsView.as_view(), name='account_head_options'),

    # Final actions
    path('spr/proceed/', SPRProceedView.as_view(), name='spr_proceed'),
    path('spr/cancel/', SPRCancelView.as_view(), name='spr_cancel'), # This is a redirect out of the module
]

```

#### 4.6 Tests (`spr_app/tests.py`)

**Task:** Write comprehensive unit tests for models and integration tests for views, ensuring at least 80% test coverage.

**Instructions:**
Mock necessary objects like `request.user` attributes (company_id, username, financial_year_id) since they come from `Session` in ASP.NET. Create dummy data for all related models to support tests.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import date, time
from decimal import Decimal

from .models import (
    Unit, AccountHead, BusinessGroup, Supplier, Category, Location,
    ItemMaster, FinancialYear, RateLockUnlock,
    SPRTempItem, SPRMaster, SPRDetail, SPRManager
)
from django.contrib.messages import get_messages
from unittest.mock import Mock

# --- Mock User for Session-based attributes ---
class MockUser:
    def __init__(self, username='testuser', company_id=1, financial_year_id=1):
        self.username = username
        self.company_id = company_id
        self.financial_year_id = financial_year_id
        self.is_authenticated = True

# --- Setup for shared test data ---
class SPRModuleTestSetup(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create base data for all tests
        cls.user = MockUser()
        cls.comp_id = cls.user.company_id
        cls.fin_year_id = cls.user.financial_year_id
        cls.session_id = cls.user.username

        # Create essential lookup data
        cls.unit_pc = Unit.objects.create(id=1, symbol='PC')
        cls.unit_kg = Unit.objects.create(id=2, symbol='KG')

        cls.acc_head_lbr = AccountHead.objects.create(id=1, symbol='LBR', description='Labour Charges')
        cls.acc_head_mat = AccountHead.objects.create(id=2, symbol='MAT', description='Material Cost')
        cls.acc_head_exp = AccountHead.objects.create(id=3, symbol='EXP', description='Expenses')
        cls.acc_head_ser = AccountHead.objects.create(id=4, symbol='SER', description='Service Provider')

        cls.bg_group_a = BusinessGroup.objects.create(id=1, symbol='DEPT-A')
        cls.bg_group_b = BusinessGroup.objects.create(id=2, symbol='DEPT-B')

        cls.supplier_abc = Supplier.objects.create(supplier_id='SUP001', supplier_name='ABC Corp')
        cls.supplier_xyz = Supplier.objects.create(supplier_id='SUP002', supplier_name='XYZ Ltd')

        cls.category_raw = Category.objects.create(cid=1, symbol='RAW', cname='Raw Materials')
        cls.category_nocode = Category.objects.create(cid=26, symbol='NOC', cname='No Code Items') # Hardcoded 26

        cls.location_main = Location.objects.create(id=1, location_label='Main', location_no='101')
        cls.location_aux = Location.objects.create(id=2, location_label='Aux', location_no='202')

        cls.fin_year_2023 = FinancialYear.objects.create(id=1, comp_id=cls.comp_id, fin_year='2023-24', fin_year_from=date(2023, 4, 1), fin_year_to=date(2024, 3, 31))
        
        # Create an ItemMaster item
        cls.item_master_001 = ItemMaster.objects.create(
            id=1,
            sys_date=date.today(),
            sys_time=time(10,0,0),
            session_id='init_session',
            comp_id=cls.comp_id,
            fin_year_id=cls.fin_year_id,
            item_code='ITEM-001',
            manf_desc='Standard Product A',
            uom_basic=cls.unit_pc,
            stock_qty=Decimal('100.000'),
            location=cls.location_main,
            cid=cls.category_raw,
            min_order_qty=Decimal('10'),
            min_stock_qty=Decimal('5'),
            absolute=0,
            opening_bal_qty=Decimal('0'),
            uom_con_fact=Decimal('1'),
            opening_bal_date=date(2023,4,1),
            ahid=cls.acc_head_mat
        )
        # Another ItemMaster item
        cls.item_master_002 = ItemMaster.objects.create(
            id=2,
            sys_date=date.today(),
            sys_time=time(10,0,0),
            session_id='init_session',
            comp_id=cls.comp_id,
            fin_year_id=cls.fin_year_id,
            item_code='ITEM-002',
            manf_desc='Standard Product B',
            uom_basic=cls.unit_kg,
            stock_qty=Decimal('50.000'),
            location=cls.location_aux,
            cid=cls.category_raw,
            min_order_qty=Decimal('5'),
            min_stock_qty=Decimal('2'),
            absolute=0,
            opening_bal_qty=Decimal('0'),
            uom_con_fact=Decimal('1'),
            opening_bal_date=date(2023,4,1),
            ahid=cls.acc_head_mat
        )

# --- Model Tests ---
class SPRTempItemModelTest(SPRModuleTestSetup):
    def test_spr_temp_item_creation(self):
        temp_item = SPRTempItem.objects.create(
            id=1,
            sys_date=timezone.localdate(),
            sys_time=timezone.localtime().time(),
            comp_id=self.comp_id,
            fin_year_id=self.fin_year_id,
            session_id=self.session_id,
            supplier_id=self.supplier_abc.supplier_id,
            nocode=1,
            item=None,
            manf_desc='Custom Bolt',
            uom_basic=self.unit_pc,
            qty=Decimal('10.000'),
            rate=Decimal('5.50'),
            account_head=self.acc_head_lbr,
            wo_no='WO123',
            dept=None,
            remarks='For repair',
            del_date=date.today(),
            discount=Decimal('0.500')
        )
        self.assertEqual(temp_item.manf_desc, 'Custom Bolt')
        self.assertEqual(temp_item.uom_basic.symbol, 'PC')
        self.assertEqual(temp_item.account_head.symbol, 'LBR')

    def test_get_display_data_nocode_item(self):
        temp_item = SPRTempItem.objects.create(
            id=1, sys_date=timezone.localdate(), sys_time=timezone.localtime().time(),
            comp_id=self.comp_id, fin_year_id=self.fin_year_id, session_id=self.session_id,
            supplier_id=self.supplier_abc.supplier_id, nocode=1, item=None,
            manf_desc='Custom Widget', uom_basic=self.unit_pc, qty=Decimal('1.000'),
            rate=Decimal('100.00'), account_head=self.acc_head_exp, wo_no=None,
            dept=self.bg_group_a, remarks='Test remark', del_date=date.today(), discount=Decimal('0')
        )
        display_data = temp_item.get_display_data()
        self.assertEqual(display_data['item_code'], '')
        self.assertEqual(display_data['nocode_str'], '1')
        self.assertEqual(display_data['description'], 'Custom Widget')
        self.assertEqual(display_data['uom_basic'], 'PC')
        self.assertEqual(display_data['qty'], '1.000')
        self.assertEqual(display_data['rate'], '100.00')
        self.assertEqual(display_data['account_head'], '[EXP] - Expenses')
        self.assertEqual(display_data['wo_no'], 'NA')
        self.assertEqual(display_data['dept'], 'DEPT-A')

    def test_get_display_data_item_master_item(self):
        temp_item = SPRTempItem.objects.create(
            id=1, sys_date=timezone.localdate(), sys_time=timezone.localtime().time(),
            comp_id=self.comp_id, fin_year_id=self.fin_year_id, session_id=self.session_id,
            supplier_id=self.supplier_abc.supplier_id, nocode=None, item=self.item_master_001,
            manf_desc='Ignored', uom_basic=self.unit_pc, qty=Decimal('5.000'),
            rate=Decimal('20.00'), account_head=self.acc_head_mat, wo_no='WO456',
            dept=None, remarks='Existing item', del_date=date.today(), discount=Decimal('1')
        )
        display_data = temp_item.get_display_data()
        self.assertEqual(display_data['item_code'], 'ITEM-001')
        self.assertEqual(display_data['nocode_str'], '')
        self.assertEqual(display_data['description'], 'Standard Product A')
        self.assertEqual(display_data['uom_basic'], 'PC')
        self.assertEqual(display_data['qty'], '5.000')
        self.assertEqual(display_data['rate'], '20.00')
        self.assertEqual(display_data['account_head'], '[MAT] - Material Cost')
        self.assertEqual(display_data['wo_no'], 'WO456')
        self.assertEqual(display_data['dept'], 'NA')

    def test_get_next_nocode_value(self):
        SPRTempItem.objects.create(
            id=1, sys_date=timezone.localdate(), sys_time=timezone.localtime().time(),
            comp_id=self.comp_id, fin_year_id=self.fin_year_id, session_id=self.session_id,
            supplier_id='SUP001', nocode=1, item=None, manf_desc='A', uom_basic=self.unit_pc,
            qty=Decimal('1'), rate=Decimal('1'), account_head=self.acc_head_lbr, del_date=date.today()
        )
        SPRTempItem.objects.create(
            id=2, sys_date=timezone.localdate(), sys_time=timezone.localtime().time(),
            comp_id=self.comp_id, fin_year_id=self.fin_year_id, session_id=self.session_id,
            supplier_id='SUP001', nocode=2, item=None, manf_desc='B', uom_basic=self.unit_pc,
            qty=Decimal('1'), rate=Decimal('1'), account_head=self.acc_head_lbr, del_date=date.today()
        )
        next_nocode = SPRTempItem.get_next_nocode_value(self.comp_id, self.session_id)
        self.assertEqual(next_nocode, 3)

    def test_create_spr_item_nocode(self):
        form_data = {
            'manf_desc': 'New NoCode Item',
            'uom_basic': self.unit_pc.id,
            'wo_type': 'wono',
            'wo_no': 'WO789',
            'dept': None,
            'del_date': date.today(),
            'remarks': 'New item remarks',
            'qty': Decimal('12.5'),
            'rate': Decimal('99.99'),
            'discount': Decimal('1.00'),
            'supplier': f"{self.supplier_xyz.supplier_name} [{self.supplier_xyz.supplier_id}]",
            'account_head_type': 'labour',
            'account_head': self.acc_head_lbr.id,
        }
        initial_count = SPRTempItem.objects.count()
        temp_item = SPRTempItem.create_spr_item(self.user, form_data, is_nocode=True)
        self.assertEqual(SPRTempItem.objects.count(), initial_count + 1)
        self.assertEqual(temp_item.manf_desc, 'New NoCode Item')
        self.assertEqual(temp_item.nocode, 1) # First nocode item for this session

    def test_create_spr_item_from_master(self):
        form_data = {
            'item': self.item_master_001,
            'manf_desc': self.item_master_001.manf_desc, # Will be ignored by create_spr_item for existing items
            'uom_basic': self.item_master_001.uom_basic.id,
            'wo_type': 'dept',
            'wo_no': None,
            'dept': self.bg_group_b.id,
            'del_date': date.today(),
            'remarks': 'Master item remarks',
            'qty': Decimal('3.0'),
            'rate': Decimal('150.00'),
            'discount': Decimal('0'),
            'supplier': f"{self.supplier_abc.supplier_name} [{self.supplier_abc.supplier_id}]",
            'account_head_type': 'material',
            'account_head': self.acc_head_mat.id,
        }
        initial_count = SPRTempItem.objects.count()
        temp_item = SPRTempItem.create_spr_item(self.user, form_data, is_nocode=False)
        self.assertEqual(SPRTempItem.objects.count(), initial_count + 1)
        self.assertEqual(temp_item.item, self.item_master_001)
        self.assertIsNone(temp_item.nocode)
        self.assertEqual(temp_item.qty, Decimal('3.000'))

    def test_create_spr_item_duplicate_from_master_raises_error(self):
        # Add item first
        SPRTempItem.objects.create(
            id=1, sys_date=timezone.localdate(), sys_time=timezone.localtime().time(),
            comp_id=self.comp_id, fin_year_id=self.fin_year_id, session_id=self.session_id,
            supplier_id='SUP001', nocode=None, item=self.item_master_001, manf_desc='A', uom_basic=self.unit_pc,
            qty=Decimal('1'), rate=Decimal('1'), account_head=self.acc_head_lbr, del_date=date.today()
        )
        form_data = {
            'item': self.item_master_001,
            'uom_basic': self.item_master_001.uom_basic.id,
            'wo_type': 'wono', 'wo_no': 'WO_TEST',
            'del_date': date.today(), 'qty': Decimal('1'), 'rate': Decimal('1'),
            'supplier': f"{self.supplier_abc.supplier_name} [{self.supplier_abc.supplier_id}]",
            'account_head': self.acc_head_lbr.id,
        }
        with self.assertRaisesMessage(ValueError, "Item is already in your temporary list."):
            SPRTempItem.create_spr_item(self.user, form_data, is_nocode=False)

class SPRMasterModelTest(SPRModuleTestSetup):
    def test_generate_spr_no(self):
        # No existing SPRs
        next_spr_no = SPRMaster.generate_spr_no(self.comp_id, self.fin_year_id)
        self.assertEqual(next_spr_no, '0001')

        # Create one SPR
        SPRMaster.objects.create(id=1, sys_date=date.today(), sys_time=time(10,0,0),
                                 session_id='prev_session', comp_id=self.comp_id,
                                 fin_year_id=self.fin_year_id, spr_no='0001')
        next_spr_no = SPRMaster.generate_spr_no(self.comp_id, self.fin_year_id)
        self.assertEqual(next_spr_no, '0002')

class SPRManagerTest(SPRModuleTestSetup):
    def test_finalize_spr_no_items(self):
        with self.assertRaisesMessage(ValueError, "No items found in your temporary SPR list. Please add items before proceeding."):
            SPRManager.finalize_spr(self.user)

    def test_finalize_spr_with_items(self):
        # Add a no-code item
        SPRTempItem.objects.create(
            id=1, sys_date=timezone.localdate(), sys_time=timezone.localtime().time(),
            comp_id=self.comp_id, fin_year_id=self.fin_year_id, session_id=self.session_id,
            supplier_id=self.supplier_abc.supplier_id, nocode=1, item=None,
            manf_desc='NoCode Test Item', uom_basic=self.unit_pc, qty=Decimal('10.000'),
            rate=Decimal('5.50'), account_head=self.acc_head_lbr, wo_no='WO111', dept=None,
            remarks='NoCode Test', del_date=date.today(), discount=Decimal('0')
        )
        # Add an item master item
        SPRTempItem.objects.create(
            id=2, sys_date=timezone.localdate(), sys_time=timezone.localtime().time(),
            comp_id=self.comp_id, fin_year_id=self.fin_year_id, session_id=self.session_id,
            supplier_id=self.supplier_xyz.supplier_id, nocode=None, item=self.item_master_001,
            manf_desc='Master Test Item', uom_basic=self.unit_kg, qty=Decimal('2.000'),
            rate=Decimal('25.00'), account_head=self.acc_head_mat, wo_no=None, dept=self.bg_group_a,
            remarks='Master Test', del_date=date.today(), discount=Decimal('1')
        )

        initial_spr_master_count = SPRMaster.objects.count()
        initial_spr_detail_count = SPRDetail.objects.count()
        initial_temp_item_count = SPRTempItem.objects.count()
        initial_item_master_count = ItemMaster.objects.count()

        new_spr_no = SPRManager.finalize_spr(self.user)

        self.assertEqual(SPRMaster.objects.count(), initial_spr_master_count + 1)
        self.assertEqual(SPRDetail.objects.count(), initial_spr_detail_count + initial_temp_item_count)
        self.assertEqual(SPRTempItem.objects.count(), 0) # Temp items should be cleared

        # Check SPR Master details
        spr_master = SPRMaster.objects.get(spr_no=new_spr_no)
        self.assertEqual(spr_master.session_id, self.session_id)

        # Check SPR Details
        spr_details = SPRDetail.objects.filter(master=spr_master)
        self.assertEqual(spr_details.count(), 2)

        # Verify no-code item was created in ItemMaster
        fin_year_str = self.fin_year_2023.fin_year
        expected_nocode_item_code = f"{fin_year_str}-{new_spr_no}-001"
        new_nocode_item_master = ItemMaster.objects.get(item_code=expected_nocode_item_code)
        self.assertEqual(new_nocode_item_master.manf_desc, 'NoCode Test Item')
        self.assertEqual(ItemMaster.objects.count(), initial_item_master_count + 1) # One new item master created

        # Verify RateLockUnlock updated
        rate_lock_nocode = RateLockUnlock.objects.get(item_id=new_nocode_item_master.id, type=1, comp_id=self.comp_id)
        self.assertEqual(rate_lock_nocode.lock_unlock, 0)
        self.assertEqual(rate_lock_nocode.locked_by_transaction, new_spr_no)

        rate_lock_master = RateLockUnlock.objects.get(item_id=self.item_master_001.id, type=1, comp_id=self.comp_id)
        self.assertEqual(rate_lock_master.lock_unlock, 0)
        self.assertEqual(rate_lock_master.locked_by_transaction, new_spr_no)


# --- View Tests ---
class SPRNewViewsTest(SPRModuleTestSetup):
    def setUp(self):
        self.client = Client()
        self.client.force_login(MockUser()) # Log in a mock user for session context

    def test_spr_new_view_get(self):
        response = self.client.get(reverse('spr_new'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'spr_app/spr_new.html')
        self.assertIn('item_master_search_form', response.context)
        self.assertIn('spr_temp_item_nocode_form', response.context)
        self.assertIn('spr_temp_items', response.context)
        self.assertIn('item_masters', response.context)

    def test_item_master_table_partial_get(self):
        response = self.client.get(reverse('item_master_table_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'spr_app/partials/_item_master_table.html')
        self.assertIn('item_masters', response.context)
        self.assertQuerysetEqual(response.context['item_masters'], ItemMaster.objects.none())

    def test_item_master_table_partial_search(self):
        # Search by item code
        response = self.client.get(reverse('item_master_table_partial'), {
            'type': 'Category',
            'category': self.category_raw.cid,
            'search_code': 'ItemCode',
            'search_value_text': 'ITEM-001'
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'spr_app/partials/_item_master_table.html')
        self.assertQuerysetEqual(response.context['item_masters'], [self.item_master_001])

        # Search by description without type
        response = self.client.get(reverse('item_master_table_partial'), {
            'type': '',
            'search_value_text': 'product'
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'spr_app/partials/_item_master_table.html')
        self.assertQuerysetEqual(response.context['item_masters'].order_by('id'), [self.item_master_001, self.item_master_002])

    def test_item_master_search_options_get(self):
        response = self.client.get(reverse('item_master_search_options'), {'type': 'Category'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'spr_app/partials/_item_master_search_dynamic_fields.html')
        self.assertIn('categories', response.context)
        self.assertIn('locations', response.context)
        self.assertTrue(response.context['categories'].exists())
        self.assertTrue(response.context['locations'].exists())

    def test_item_master_search_value_field_get(self):
        response = self.client.get(reverse('item_master_search_value_field'), {'search_code': 'Location', 'type': 'Category'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'spr_app/partials/_item_master_search_value_field.html')
        self.assertIn('locations', response.context)
        self.assertTrue(response.context['locations'].exists())

    def test_spr_temp_item_list_partial_get(self):
        response = self.client.get(reverse('spr_temp_item_list_partial'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'spr_app/partials/_selected_items_table.html')
        self.assertIn('spr_temp_items', response.context)
        self.assertEqual(len(response.context['spr_temp_items']), 0) # Initially empty

    def test_spr_temp_item_nocode_create_post_success(self):
        url = reverse('spr_temp_item_nocode_add')
        data = {
            'manf_desc': 'HTMX NoCode Item',
            'uom_basic': self.unit_pc.id,
            'wo_type': 'wono',
            'wo_no': 'HTMXWO1',
            'del_date': date.today().strftime('%Y-%m-%d'),
            'qty': '10.000',
            'rate': '15.00',
            'discount': '0.000',
            'supplier': f"{self.supplier_abc.supplier_name} [{self.supplier_abc.supplier_id}]",
            'account_head_type': 'labour',
            'account_head': self.acc_head_lbr.id,
        }
        initial_count = SPRTempItem.objects.count()
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No content, indicates success for HTMX
        self.assertEqual(SPRTempItem.objects.count(), initial_count + 1)
        self.assertTrue('HX-Trigger' in response.headers)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'No Code Item added successfully.')

    def test_spr_temp_item_nocode_create_post_invalid(self):
        url = reverse('spr_temp_item_nocode_add')
        data = { # Missing required fields
            'manf_desc': '',
            'uom_basic': '',
            'qty': '-5.0', # Invalid quantity
            'rate': '0', # Invalid rate
            'supplier': 'Invalid Supplier',
            'del_date': 'invalid-date',
            'wo_type': 'wono',
            'wo_no': '', # Missing WO No when wo_type is wono
            'account_head_type': 'labour',
            'account_head': '',
        }
        initial_count = SPRTempItem.objects.count()
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'spr_app/partials/_no_code_item_form.html')
        self.assertEqual(SPRTempItem.objects.count(), initial_count) # No item created
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Please correct the errors in the form.')
        self.assertIn('form', response.context['spr_temp_item_nocode_form'].errors)


    def test_spr_temp_item_from_master_add_get(self):
        url = reverse('spr_temp_item_from_master_add', args=[self.item_master_001.id])
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'spr_app/partials/_add_item_details_modal.html')
        self.assertIn('form', response.context)
        self.assertIn('item_master', response.context)
        self.assertEqual(response.context['form'].initial['item'], self.item_master_001)

    def test_spr_temp_item_from_master_add_post_success(self):
        url = reverse('spr_temp_item_from_master_add', args=[self.item_master_002.id])
        data = {
            'item': self.item_master_002.id,
            'manf_desc': self.item_master_002.manf_desc,
            'uom_basic': self.item_master_002.uom_basic.id,
            'wo_type': 'wono',
            'wo_no': 'WO_ITEM_002',
            'del_date': date.today().strftime('%Y-%m-%d'),
            'qty': '5.000',
            'rate': '25.00',
            'discount': '0.000',
            'supplier': f"{self.supplier_xyz.supplier_name} [{self.supplier_xyz.supplier_id}]",
            'account_head_type': 'material',
            'account_head': self.acc_head_mat.id,
        }
        initial_count = SPRTempItem.objects.count()
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(SPRTempItem.objects.count(), initial_count + 1)
        self.assertTrue(SPRTempItem.objects.filter(item=self.item_master_002, qty=Decimal('5.000')).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertIn('added to temporary list', str(messages[0]))


    def test_spr_temp_item_delete_get(self):
        temp_item = SPRTempItem.objects.create(
            id=1, sys_date=timezone.localdate(), sys_time=timezone.localtime().time(),
            comp_id=self.comp_id, fin_year_id=self.fin_year_id, session_id=self.session_id,
            supplier_id='SUP001', nocode=1, item=None, manf_desc='To Be Deleted',
            uom_basic=self.unit_pc, qty=Decimal('1'), rate=Decimal('1'), account_head=self.acc_head_lbr, del_date=date.today()
        )
        url = reverse('spr_temp_item_delete', args=[temp_item.id])
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'spr_app/partials/_confirm_delete_modal.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], temp_item)

    def test_spr_temp_item_delete_post_success(self):
        temp_item = SPRTempItem.objects.create(
            id=1, sys_date=timezone.localdate(), sys_time=timezone.localtime().time(),
            comp_id=self.comp_id, fin_year_id=self.fin_year_id, session_id=self.session_id,
            supplier_id='SUP001', nocode=1, item=None, manf_desc='To Be Deleted',
            uom_basic=self.unit_pc, qty=Decimal('1'), rate=Decimal('1'), account_head=self.acc_head_lbr, del_date=date.today()
        )
        url = reverse('spr_temp_item_delete', args=[temp_item.id])
        initial_count = SPRTempItem.objects.count()
        response = self.client.post(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(SPRTempItem.objects.count(), initial_count - 1)
        self.assertFalse(SPRTempItem.objects.filter(id=temp_item.id).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Item removed from temporary list.')

    def test_supplier_autocomplete_view(self):
        url = reverse('supplier_autocomplete') + '?q=ABC'
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        self.assertIn(f"{self.supplier_abc.supplier_name} [{self.supplier_abc.supplier_id}]", response.json())
        self.assertNotIn(f"{self.supplier_xyz.supplier_name} [{self.supplier_xyz.supplier_id}]", response.json())

    def test_account_head_options_view(self):
        url = reverse('account_head_options') + '?type=material'
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'spr_app/partials/_account_head_dropdown_options.html')
        self.assertIn('account_heads', response.context)
        self.assertQuerysetEqual(response.context['account_heads'], [self.acc_head_mat])

    def test_spr_proceed_view_success(self):
        # Add some temporary items first
        SPRTempItem.objects.create(
            id=1, sys_date=timezone.localdate(), sys_time=timezone.localtime().time(),
            comp_id=self.comp_id, fin_year_id=self.fin_year_id, session_id=self.session_id,
            supplier_id=self.supplier_abc.supplier_id, nocode=1, item=None,
            manf_desc='Test Proc Item', uom_basic=self.unit_pc, qty=Decimal('1'),
            rate=Decimal('1'), account_head=self.acc_head_lbr, del_date=date.today()
        )
        url = reverse('spr_proceed')
        initial_temp_count = SPRTempItem.objects.count()
        initial_spr_master_count = SPRMaster.objects.count()
        initial_spr_detail_count = SPRDetail.objects.count()
        
        response = self.client.post(url, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 204)
        self.assertTrue('HX-Redirect' in response.headers)
        self.assertEqual(response.headers['HX-Redirect'], reverse('spr_new')) # Redirects back to clear

        self.assertEqual(SPRTempItem.objects.count(), 0)
        self.assertEqual(SPRMaster.objects.count(), initial_spr_master_count + 1)
        self.assertEqual(SPRDetail.objects.count(), initial_spr_detail_count + initial_temp_count)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertIn('SPR created successfully', str(messages[0]))

    def test_spr_proceed_view_no_items(self):
        url = reverse('spr_proceed')
        response = self.client.post(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400) # Bad request due to validation error
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'No items found in your temporary SPR list. Please add items before proceeding.')

    def test_spr_cancel_view(self):
        response = self.client.get(reverse('spr_cancel'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertTrue('HX-Redirect' in response.headers)
        self.assertEqual(response.headers['HX-Redirect'], reverse('spr_dashboard')) # Assumed dashboard URL

```

---

### Step 5: HTMX and Alpine.js Integration

**Task:** Ensure all dynamic interactions are driven by HTMX with Alpine.js managing UI state, using DataTables for lists, and achieving a seamless user experience without full page reloads.

**Instructions:**

*   **HTMX for dynamic content loading and form submissions:**
    *   **Tab Switching (Simulated):** The main `spr_new.html` uses Alpine.js `x-show` to toggle visibility of content sections (Item Master, No Code Item, Selected Items). When a section becomes visible, it has `hx-trigger="load"` or other triggers to fetch its data/form content.
    *   **Item Master Search:** The `form` in `itemMaster` tab has `hx-get` to `item_master_table_partial` and `hx-target` to `#itemMasterTableContainer`. Dropdowns within the search form use `hx-get` to `item_master_search_options` and `item_master_search_value_field` to dynamically update related form fields.
    *   **"Select" Item from Master List:** Buttons in `_item_master_table.html` use `hx-get` to `spr_temp_item_from_master_add/<int:item_id>/`, targeting `#modalContent` to load the item details form into a modal.
    *   **"No Code Item" Form Submission:** The `form` in `noCodeItem` tab uses `hx-post` to `spr_temp_item_nocode_add/`, targeting the form itself to re-render it (with errors or cleared) and `hx-swap="outerHTML"`.
    *   **Selected Items List Refresh:** Both adding (from master or no-code) and deleting items trigger `HX-Trigger: refreshSPRTempList` from the server, which `spr_new.html`'s `#sprTempItemsContainer` listens for to re-fetch its content.
    *   **"Delete" Item from Selected List:** Buttons in `_selected_items_table.html` use `hx-get` to `spr_temp_item_delete/<int:pk>/`, targeting `#modalContent` for confirmation. The confirmation modal then `hx-post`s to the same URL to perform the delete.
    *   **Supplier Autocomplete:** The supplier input field uses `hx-get` to `supplier_autocomplete/` with `hx-trigger="keyup changed delay:500ms"` to fetch suggestions, targeting `#supplier-suggestions`.
    *   **Account Head Dynamic Dropdown:** Radio buttons for account head type use `hx-get` to `account_head_options/` with `hx-trigger="change"` to update the `account_head` dropdown.
    *   **"Proceed" Button:** `hx-post` to `spr_proceed/` with `hx-confirm` for user confirmation and `hx-redirect` for full page reload/redirection after successful finalization.
    *   **Messages:** Django's messages framework is used, and HTMX/Alpine.js can be configured to display these as dynamic toasts/alerts.

*   **Alpine.js for UI state management:**
    *   **Tab System:** `x-data="{ activeTab: 'itemMaster' }"` on the main container manages which tab's content is `x-show`n.
    *   **Modals:** `x-data="{ showModal: false, modalContent: '' }"` on the main container controls modal visibility and content. HTMX responses can update `modalContent`, and Alpine handles showing the modal.
    *   **Dynamic Form Fields:** `x-show="woType === 'wono'"` or `x-show="woType === 'dept'"` for WO No/BG Group fields based on radio button selection within the "No Code Item" form and "Add Item Details" modal. Similarly for account head type.
    *   **Reinitialization:** `htmx:afterSwap` event listeners are used to re-initialize Alpine.js components and DataTables on dynamically loaded content.

*   **DataTables for List Views:**
    *   Both `_item_master_table.html` and `_selected_items_table.html` contain `<table>` elements with unique IDs (`itemMasterTable`, `sprTempItemsTable`).
    *   A JavaScript block in `spr_new.html` (within `{% block extra_js %}`) listens for `htmx:afterSwap` events on their respective containers. When these containers are updated by HTMX, it re-initializes `$(...).DataTable()`, ensuring proper client-side searching, sorting, and pagination. `destroy: true` is used to clean up previous DataTable instances.

This integrated approach ensures that the application behaves like a modern Single Page Application (SPA) for common interactions, providing a smooth user experience while keeping the server-side logic purely Django-based and highly testable.