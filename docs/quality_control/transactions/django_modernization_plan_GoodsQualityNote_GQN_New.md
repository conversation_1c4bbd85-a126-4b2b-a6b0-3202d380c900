The following Django modernization plan addresses the migration of the provided ASP.NET Goods Quality Note (GQN) list functionality. This plan focuses on a systematic, automated conversion process, emphasizing Django's "Fat Model, Thin View" architecture, HTMX for dynamic interactions, Alpine.js for UI state management, and DataTables for efficient data presentation.

## ASP.NET to Django Conversion Script: Goods Quality Note (GQN) List

This document outlines the strategy and code for transitioning the ASP.NET Goods Quality Note (GQN) list page into a modern Django application. Our goal is to leverage AI-assisted automation to transform the legacy system into a robust, scalable, and maintainable solution.

### Core Business Value of Modernization:

*   **Enhanced User Experience:** By using HTMX and Alpine.js, the application will provide a highly responsive, single-page application (SPA)-like feel without complex JavaScript, leading to faster interactions and improved user satisfaction.
*   **Improved Performance:** Optimized Django ORM queries and client-side DataTables ensure efficient data loading and manipulation, reducing server load and response times.
*   **Simplified Maintenance:** Adopting Django's "Fat Model, Thin View" pattern centralizes business logic, making the codebase easier to understand, debug, and extend. Strict separation of concerns eliminates intertwined logic, fostering modularity.
*   **Increased Scalability:** Django's robust framework is designed to handle increasing loads, ensuring the application can grow with your business needs.
*   **Future-Proofing:** Moving to a modern, actively developed framework like Django reduces reliance on legacy technologies, mitigates security risks, and provides access to a vast ecosystem of tools and libraries.
*   **Developer Efficiency:** The clear, conventional structure of Django, combined with comprehensive testing, accelerates future development and reduces the likelihood of introducing bugs.

---

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
The ASP.NET `loadData` function reveals a complex SQL query that joins several tables. For Django, we will create models representing these existing database tables. These models will use `managed = False` to indicate that Django should not manage their schema in the database, as they are pre-existing.

**Identified Tables and Key Columns:**

*   `tblinv_MaterialReceived_Master` (Core GQN record)
    *   `Id` (Primary Key)
    *   `FinYearId` (Foreign Key to `tblFinancial_master`)
    *   `GRRNo`
    *   `GINNo`
    *   `GINId` (Foreign Key to `tblInv_Inward_Master`)
    *   `SysDate`
    *   `CompId` (Foreign Key to `tblCompany_master`)
*   `tblFinancial_master`
    *   `FinYearId` (Primary Key)
    *   `FinYear`
    *   `CompId`
*   `tblInv_Inward_Master` (Goods Inward Note - GIN)
    *   `Id` (Primary Key)
    *   `PONo` (This is actually `POMId` which is FK to `tblMM_PO_Master`)
    *   `ChallanNo`
    *   `ChallanDate`
    *   `POMId` (Foreign Key to `tblMM_PO_Master`)
    *   `CompId`
*   `tblMM_PO_Master` (Purchase Order)
    *   `Id` (Primary Key)
    *   `PONo`
    *   `SupplierId` (Foreign Key to `tblMM_Supplier_master`)
    *   `CompId`
*   `tblMM_Supplier_master`
    *   `SupplierId` (Primary Key)
    *   `SupplierName`
    *   `CompId`
*   `tblinv_MaterialReceived_Details`
    *   `Id` (Primary Key, inferred, or part of composite key)
    *   `MId` (Foreign Key to `tblinv_MaterialReceived_Master`)
    *   `ReceivedQty`
*   `tblQc_MaterialQuality_Master`
    *   `Id` (Primary Key)
    *   `GRRId` (Foreign Key to `tblinv_MaterialReceived_Master`)
*   `tblQc_MaterialQuality_Details`
    *   `Id` (Primary Key, inferred)
    *   `MId` (Foreign Key to `tblQc_MaterialQuality_Master`)
    *   `GRRId` (Foreign Key to `tblinv_MaterialReceived_Master`, as per SQL query)
    *   `AcceptedQty`
    *   `RejectedQty`
*   `tblCompany_master` (Inferred from `CompId` usage)
    *   `CompId` (Primary Key)
    *   `CompanyName`

### Step 2: Identify Backend Functionality

**Task:** Determine the data retrieval and filtering logic.

The ASP.NET page primarily functions as a searchable list view for "Goods Quality Notes". The key operations are:

*   **Data Retrieval (Read):** Displaying a list of material receipt records (`tblinv_MaterialReceived_Master`) along with related details from linked tables (Financial Year, GIN, PO, Supplier, Challan Info).
*   **Complex Filtering/Business Logic:**
    *   Filtering records by `FinYearId` (less than or equal to current session's financial year ID) and `CompId` (current session's company ID).
    *   Applying dynamic search conditions based on user selection (Supplier Name, GRR No, PO No).
    *   Crucially, filtering records to show only those where the `ReceivedQty` for a Goods Receipt (GRR) is greater than the total `AcceptedQty` plus `RejectedQty` from associated Quality Notes. This ensures only items pending quality assessment are displayed.
*   **Search Functionality:** Allowing users to filter the list using a dropdown for search type (Supplier Name, GRR No, PO No) and an associated text input. The Supplier Name search includes an autocomplete feature.
*   **Navigation:** Providing a "Select" action for each displayed row. This action redirects the user to a detailed GQN entry page, passing various record identifiers as query parameters. This is a navigation action, not a CRUD operation on the current entity.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, and map them to modern Django/HTMX/Alpine.js/DataTables equivalents.

*   **Search/Filter Controls:**
    *   `asp:DropDownList ID="DropDownList1"`: Will be a standard HTML `<select>` element within a Django `forms.Form`. HTMX will be used to dynamically update the visibility of associated input fields when the selection changes.
    *   `asp:TextBox ID="txtSupplier"` with `cc1:AutoCompleteExtender`: Will be a standard HTML `<input type="text">` field. HTMX will power its autocomplete functionality by making AJAX calls to a Django API endpoint that returns supplier suggestions. Alpine.js will manage the visibility of the suggestion list.
    *   `asp:TextBox ID="Txtfield"`: Another standard HTML `<input type="text">` for GRR No or PO No. Its visibility will also be controlled by HTMX based on the `search_type` dropdown.
    *   `asp:Button ID="btnSearch"`: Will be a standard HTML `<button type="submit">`. This button will trigger an HTMX POST request to refresh the DataTables content based on the current filter criteria.
*   **Data Display (`asp:GridView ID="GridView2"`):**
    *   This will be replaced by a modern HTML `<table>` element, which will be initialized as a DataTables instance.
    *   HTMX will be used to load and swap this table dynamically (as a partial template) when search filters are applied or refreshed.
    *   Client-side DataTables will handle pagination, sorting, and local search within the displayed dataset, providing a fluid user experience.
    *   The "Select" `asp:LinkButton` in each row will be a standard HTML `<a>` or `<button>` that constructs a URL for redirection, mimicking the original ASP.NET behavior.

### Step 4: Generate Django Code

We'll organize the code within a Django application named `inventory`.

#### 4.1 Models

**Task:** Create Django models based on the database schema identified in Step 1. These models will adhere to the "Fat Model" principle by including methods or custom managers that encapsulate the complex business logic and data retrieval.

**File: `inventory/models.py`**

```python
from django.db import models
from django.db.models import Sum, F, ExpressionWrapper, fields
from django.db.models.functions import Coalesce

# Base Models (assuming these tables exist in your database)
class CompanyMaster(models.Model):
    """
    Represents the tblCompany_master table.
    Used for filtering records by company ID.
    """
    id = models.IntegerField(db_column='CompId', primary_key=True)
    company_name = models.CharField(db_column='CompanyName', max_length=255)

    class Meta:
        managed = False  # Django will not manage this table's schema
        db_table = 'tblCompany_master'
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.company_name

class FinancialMaster(models.Model):
    """
    Represents the tblFinancial_master table.
    Used for filtering records by financial year.
    """
    id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50)
    company = models.ForeignKey(CompanyMaster, on_delete=models.DO_NOTHING, db_column='CompId', related_name='financial_years')

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class SupplierMaster(models.Model):
    """
    Represents the tblMM_Supplier_master table.
    Used for supplier information and autocomplete.
    """
    id = models.IntegerField(db_column='SupplierId', primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    company = models.ForeignKey(CompanyMaster, on_delete=models.DO_NOTHING, db_column='CompId', related_name='suppliers')

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return f"{self.supplier_name} [{self.id}]"

class POMaster(models.Model):
    """
    Represents the tblMM_PO_Master table (Purchase Order).
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    po_no = models.CharField(db_column='PONo', max_length=50)
    supplier = models.ForeignKey(SupplierMaster, on_delete=models.DO_NOTHING, db_column='SupplierId', related_name='purchase_orders')
    company = models.ForeignKey(CompanyMaster, on_delete=models.DO_NOTHING, db_column='CompId', related_name='pos')

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'

    def __str__(self):
        return self.po_no

class InwardMaster(models.Model):
    """
    Represents the tblInv_Inward_Master table (Goods Inward Note - GIN).
    Linked to MaterialReceivedMaster and POMaster.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    # GINNo is retrieved from tblinv_MaterialReceived_Master's GINNo which links to GINId
    # The actual GINNo field is often directly on MaterialReceivedMaster
    # but the SQL query shows tblInv_Inward_Master.PONo, ChallanNo, ChallanDate.
    # For gin_no, we will rely on MaterialReceivedMaster's own field for now.
    po_master = models.ForeignKey(POMaster, on_delete=models.DO_NOTHING, db_column='POMId', related_name='inward_notes')
    challan_no = models.CharField(db_column='ChallanNo', max_length=100, null=True, blank=True)
    challan_date = models.DateField(db_column='ChallanDate', null=True, blank=True)
    company = models.ForeignKey(CompanyMaster, on_delete=models.DO_NOTHING, db_column='CompId', related_name='inward_masters')

    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Master'
        verbose_name = 'Inward Note'
        verbose_name_plural = 'Inward Notes'

    def __str__(self):
        return f"GIN ID: {self.id}" # Or a more descriptive field if available

class MaterialReceivedDetails(models.Model):
    """
    Represents tblinv_MaterialReceived_Details.
    Used for calculating GRRQty (total received quantity for a GRR).
    """
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming 'Id' is the PK for simplicity. Adjust if composite.
    material_received_master = models.ForeignKey('MaterialReceivedMaster', on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    received_qty = models.DecimalField(db_column='ReceivedQty', max_digits=10, decimal_places=3, null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialReceived_Details'
        verbose_name = 'Material Received Detail'
        verbose_name_plural = 'Material Received Details'

class QualityMaster(models.Model):
    """
    Represents tblQc_MaterialQuality_Master.
    Links quality records to specific Material Received (GRR) instances.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    grr_master = models.ForeignKey('MaterialReceivedMaster', on_delete=models.DO_NOTHING, db_column='GRRId', related_name='quality_notes')

    class Meta:
        managed = False
        db_table = 'tblQc_MaterialQuality_Master'
        verbose_name = 'Quality Master'
        verbose_name_plural = 'Quality Masters'

class QualityDetails(models.Model):
    """
    Represents tblQc_MaterialQuality_Details.
    Used for calculating GQNQty (accepted + rejected quantities).
    """
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming 'Id' is the PK.
    quality_master = models.ForeignKey(QualityMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    # The original query also links GRRId directly here, which is unusual for a detail table
    # but we will reflect it for accurate translation of existing logic.
    grr_master = models.ForeignKey('MaterialReceivedMaster', on_delete=models.DO_NOTHING, db_column='GRRId', related_name='quality_details_by_grr')
    accepted_qty = models.DecimalField(db_column='AcceptedQty', max_digits=10, decimal_places=3, null=True, blank=True)
    rejected_qty = models.DecimalField(db_column='RejectedQty', max_digits=10, decimal_places=3, null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblQc_MaterialQuality_Details'
        verbose_name = 'Quality Detail'
        verbose_name_plural = 'Quality Details'

class MaterialReceivedMasterQuerySet(models.QuerySet):
    """
    Custom QuerySet for MaterialReceivedMaster to encapsulate complex filtering
    and annotation logic, adhering to the 'Fat Model' principle.
    """
    def for_gqn_listing(self, company_id, financial_year_id, search_type=None, search_term=None):
        """
        Applies the filtering and annotation logic directly translated from
        the ASP.NET loadData method's SQL query.
        """
        # Annotate with calculated GRRQty and GQNQty (accepted + rejected)
        # Coalesce ensures NULLs are treated as 0 for sum operations
        queryset = self.annotate(
            grr_qty=Coalesce(Sum('details__received_qty'), 0.0, output_field=fields.DecimalField()),
            gqn_qty=Coalesce(
                Sum(F('quality_details_by_grr__accepted_qty') + F('quality_details_by_grr__rejected_qty')),
                0.0,
                output_field=fields.DecimalField()
            )
        ).select_related(
            # Optimize joins for related data needed in the list view
            'financial_year',
            'inward_note', # Using gin_id directly, defined as inward_note
            'inward_note__po_master',
            'inward_note__po_master__supplier'
        ).filter(
            # Apply base filters: financial year and company ID
            financial_year_id__lte=financial_year_id,
            company_id=company_id
        )

        # Apply dynamic search filters based on the selected search type
        if search_term:
            if search_type == '1':  # GRR No
                queryset = queryset.filter(grr_no=search_term)
            elif search_type == '2':  # PO No
                queryset = queryset.filter(inward_note__po_master__po_no=search_term)
            elif search_type == '0':  # Supplier Name
                try:
                    # Attempt to extract supplier ID from "Name [ID]" format (from autocomplete)
                    supplier_id = search_term.split(' ')[-1].strip('[]')
                    if supplier_id.isdigit():
                        queryset = queryset.filter(inward_note__po_master__supplier_id=int(supplier_id))
                    else: # Fallback if ID extraction fails, try partial name match
                         queryset = queryset.filter(inward_note__po_master__supplier__supplier_name__icontains=search_term)
                except (IndexError, ValueError):
                    # Fallback for simple name search if the format is not "Name [ID]"
                    queryset = queryset.filter(inward_note__po_master__supplier__supplier_name__icontains=search_term)

        # Apply the crucial business logic filter: (GRRQty - GQNQty) > 0
        queryset = queryset.filter(
            ExpressionWrapper(
                F('grr_qty') - F('gqn_qty'),
                output_field=fields.DecimalField(max_digits=10, decimal_places=3)
            ) > 0
        ).order_by('-id') # Order by Id Desc as in original SQL

        return queryset

class MaterialReceivedMaster(models.Model):
    """
    Represents the tblinv_MaterialReceived_Master table,
    serving as the primary model for Goods Quality Notes.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    financial_year = models.ForeignKey(FinancialMaster, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='material_receipts')
    grr_no = models.CharField(db_column='GRRNo', max_length=50)
    gin_no = models.CharField(db_column='GINNo', max_length=50, null=True, blank=True) # Inferred from GridView display
    inward_note = models.ForeignKey(InwardMaster, on_delete=models.DO_NOTHING, db_column='GINId', related_name='material_receipts')
    sys_date = models.DateField(db_column='SysDate')
    company = models.ForeignKey(CompanyMaster, on_delete=models.DO_NOTHING, db_column='CompId', related_name='material_receipts')

    # Assign the custom QuerySet as the default manager
    objects = MaterialReceivedMasterQuerySet.as_manager()

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialReceived_Master'
        verbose_name = 'Material Received Note'
        verbose_name_plural = 'Material Received Notes'

    def __str__(self):
        return self.grr_no

    # Properties to easily access related data and formatted values in templates
    @property
    def formatted_sys_date(self):
        return self.sys_date.strftime('%d/%m/%Y') if self.sys_date else ''

    @property
    def supplier_name_formatted(self):
        # Access through chained relationships (material_received -> inward_note -> po_master -> supplier)
        if self.inward_note and self.inward_note.po_master and self.inward_note.po_master.supplier:
            return f"{self.inward_note.po_master.supplier.supplier_name} [{self.inward_note.po_master.supplier.id}]"
        return ''

    @property
    def po_no(self):
        return self.inward_note.po_master.po_no if self.inward_note and self.inward_note.po_master else ''

    @property
    def challan_no(self):
        return self.inward_note.challan_no if self.inward_note else ''

    @property
    def formatted_challan_date(self):
        return self.inward_note.challan_date.strftime('%d/%m/%Y') if self.inward_note and self.inward_note.challan_date else ''

    @property
    def supplier_id(self):
        return self.inward_note.po_master.supplier.id if self.inward_note and self.inward_note.po_master and self.inward_note.po_master.supplier else None

    @property
    def financial_year_str(self):
        return self.financial_year.fin_year if self.financial_year else ''

```

#### 4.2 Forms

**Task:** Define a Django form for the user input (search/filter fields). Since this is not a CRUD form for a model, we will use a standard `forms.Form`.

**File: `inventory/forms.py`**

```python
from django import forms

class GQNFilterForm(forms.Form):
    """
    Form for handling the search and filter inputs on the Goods Quality Note list page.
    """
    SEARCH_CHOICES = [
        ('0', 'Supplier Name'),
        ('1', 'GRR No'),
        ('2', 'PO No'),
    ]

    search_type = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        initial='0',
        label='Search By',
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
            'hx-post': 'hx-filter-change/', # HTMX endpoint to re-render filter fields
            'hx-target': '#filter-fields',
            'hx-swap': 'outerHTML',
            'hx-indicator': '#filter-spinner',
            'name': 'search_type', # Required for form submission via HTMX
        })
    )
    
    search_supplier_name = forms.CharField(
        max_length=255,
        required=False,
        label='Supplier Name',
        widget=forms.TextInput(attrs={
            'id': 'txtSupplier', # Matching original ID for consistency
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
            'placeholder': 'Enter Supplier Name',
            'hx-get': '/inventory/api/supplier-autocomplete/', # HTMX endpoint for autocomplete suggestions
            'hx-trigger': 'keyup changed delay:500ms, focus', # Trigger on keyup and focus
            'hx-target': '#supplier-suggestions',
            'hx-swap': 'outerHTML',
            'autocomplete': 'off', # Disable browser's autocomplete
            'aria-autocomplete': 'list',
            'aria-controls': 'supplier-suggestions',
            'aria-expanded': 'false',
        })
    )

    search_field_text = forms.CharField(
        max_length=255,
        required=False,
        label='Search Value',
        widget=forms.TextInput(attrs={
            'id': 'Txtfield', # Matching original ID
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
            'placeholder': 'Enter GRR No or PO No',
        })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Determine initial visibility of fields based on the pre-selected search type
        selected_type = self.initial.get('search_type', self.data.get('search_type', '0'))
        if selected_type == '0':
            self.fields['search_field_text'].widget.attrs['style'] = 'display:none;'
            self.fields['search_supplier_name'].widget.attrs['aria-expanded'] = 'true' # For accessibility
        else:
            self.fields['search_supplier_name'].widget.attrs['style'] = 'display:none;'
            self.fields['search_field_text'].widget.attrs['aria-expanded'] = 'true'

    def clean(self):
        cleaned_data = super().clean()
        search_type = cleaned_data.get('search_type')
        search_supplier_name = cleaned_data.get('search_supplier_name')
        search_field_text = cleaned_data.get('search_field_text')

        # Add custom validation to ensure a search term is provided for the selected type
        if search_type == '0' and not search_supplier_name:
            self.add_error('search_supplier_name', 'Supplier Name is required for this search type.')
        elif search_type in ['1', '2'] and not search_field_text:
            self.add_error('search_field_text', 'Search value is required for this search type.')
        
        return cleaned_data

```

#### 4.3 Views

**Task:** Implement Django Class-Based Views (CBVs) for the main page, the HTMX-driven data table, the dynamic filter fields, and the supplier autocomplete API. Views will be kept "thin" (5-15 lines) by delegating complex logic to the models.

**File: `inventory/views.py`**

```python
from django.views.generic import TemplateView
from django.shortcuts import render
from django.http import JsonResponse, HttpResponse
from django.contrib.auth.mixins import LoginRequiredMixin
import json # For parsing HTMX-JSON-BODY if needed, though not directly used in this specific form submission

from .models import MaterialReceivedMaster, SupplierMaster, CompanyMaster, FinancialMaster
from .forms import GQNFilterForm

class GQNListView(LoginRequiredMixin, TemplateView):
    """
    Main view for the Goods Quality Note list page.
    Renders the initial page layout, including the filter form.
    The data table content is loaded separately via HTMX.
    """
    template_name = 'inventory/gqn/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the filter form.
        # Initial values can come from request.GET (e.g., after a full page refresh
        # preserving search state) or defaults.
        context['form'] = GQNFilterForm(self.request.GET)
        context['page_title'] = "Goods Quality Note [GQN] - New"
        return context

class GQNTablePartialView(LoginRequiredMixin, TemplateView):
    """
    Renders only the DataTables table for HTMX requests.
    This view handles the actual data retrieval based on filter criteria.
    It's designed to be swapped into the main page's container.
    """
    template_name = 'inventory/gqn/_gqn_table.html'

    def get_queryset(self):
        # In a real application, 'compid' and 'finyear' would come from
        # the authenticated user's session or profile.
        # For demonstration, using safe defaults.
        company_id = self.request.session.get('compid', 1)  # Default company ID
        financial_year_id = self.request.session.get('finyear', 2024) # Default financial year ID

        # Determine if the request is GET or POST to get form data
        form_data = self.request.GET if self.request.method == 'GET' else self.request.POST
        form = GQNFilterForm(form_data)
        
        # Extract search parameters from the form data
        search_type = form_data.get('search_type')
        search_term = ''
        if search_type == '0':
            search_term = form_data.get('search_supplier_name')
        elif search_type in ['1', '2']:
            search_term = form_data.get('search_field_text')

        # Utilize the custom manager method in MaterialReceivedMaster to fetch filtered data.
        # This encapsulates the complex SQL logic and business rules in the model.
        queryset = MaterialReceivedMaster.objects.for_gqn_listing(
            company_id=company_id,
            financial_year_id=financial_year_id,
            search_type=search_type,
            search_term=search_term
        )
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass the filtered queryset to the template
        context['gqn_notes'] = self.get_queryset()
        return context
    
    def post(self, request, *args, **kwargs):
        # HTMX forms submit via POST. We re-use the GET logic to render the table.
        # This allows the search button to work via POST while the initial load is GET.
        return self.get(request, *args, **kwargs)

class FilterFieldsPartialView(TemplateView):
    """
    Renders only the filter input fields when the search_type dropdown changes.
    This is an HTMX target to dynamically swap between supplier name and generic text input.
    """
    template_name = 'inventory/gqn/_filter_fields.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Re-initialize the form with the new search_type to control field visibility
        form_data = self.request.GET if self.request.method == 'GET' else self.request.POST
        context['form'] = GQNFilterForm(form_data)
        return context

    def post(self, request, *args, **kwargs):
        # HTMX will post when the dropdown changes, so we handle POST requests.
        return self.get(request, *args, **kwargs) # Re-use GET logic for rendering

class SupplierAutocompleteAPI(LoginRequiredMixin, TemplateView):
    """
    Provides supplier suggestions for the autocomplete functionality.
    This mimics the ASP.NET WebMethod `sql` and returns JSON.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('prefixText', '').strip()
        if not prefix_text:
            return JsonResponse([], safe=False) # Return empty list if no prefix

        # Ensure company_id is fetched securely
        company_id = request.session.get('compid', 1) # Default company ID

        # Query SupplierMaster for names containing the prefix
        suppliers = SupplierMaster.objects.filter(
            company_id=company_id,
            supplier_name__icontains=prefix_text # Case-insensitive contains
        ).values_list('supplier_name', 'id')[:10] # Limit results as in original code

        # Format suggestions as "SupplierName [SupplierId]"
        suggestions = [f"{name} [{_id}]" for name, _id in suppliers]
        suggestions.sort() # Sort alphabetically, as per ASP.NET

        return JsonResponse(suggestions, safe=False)

```

#### 4.4 Templates

**Task:** Create Django templates for the main GQN list page and partial templates for the DataTables table, filter fields, and supplier suggestions. These templates will integrate HTMX and Alpine.js for a dynamic user experience and use Tailwind CSS for styling.

**File: `inventory/gqn/list.html`** (Main GQN List Page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">{{ page_title }}</h2>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        {# The form uses HTMX to update the table and dynamically swap filter fields #}
        <form id="gqn-filter-form" 
              hx-post="{% url 'inventory:gqn_table_partial' %}" 
              hx-target="#gqn-table-container" 
              hx-swap="innerHTML" 
              hx-indicator="#loading-spinner">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                {# Search Type Dropdown #}
                <div class="col-span-1">
                    <label for="{{ form.search_type.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.search_type.label }}
                    </label>
                    {{ form.search_type }}
                </div>
                
                {# Dynamic Filter Fields (Supplier Name or GRR/PO No) #}
                <div id="filter-fields" class="col-span-2 flex items-center space-x-2">
                    {% include 'inventory/gqn/_filter_fields.html' %}
                </div>
                
                {# Search Button #}
                <div class="col-span-1">
                    <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out">
                        Search
                    </button>
                    {# HTMX loading spinner #}
                    <div id="loading-spinner" class="htmx-indicator ml-2 inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500 hidden" role="status" aria-label="loading">
                        <span class="sr-only">Loading...</span>
                    </div>
                </div>
            </div>
        </form>
    </div>

    {# Container for the DataTables table, loaded via HTMX #}
    <div id="gqn-table-container"
         hx-trigger="load, refreshGQNList from:body" {# Load on page load, and when 'refreshGQNList' event is triggered #}
         hx-get="{% url 'inventory:gqn_table_partial' %}" {# Endpoint to fetch the table content #}
         hx-swap="innerHTML"
         hx-indicator="#loading-spinner">
        {# Initial loading state displayed before HTMX fetches content #}
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500" role="status" aria-label="loading"></div>
            <p class="mt-2 text-gray-600">Loading Goods Quality Notes...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# JavaScript specific to this page, including DataTables initialization and Alpine.js for UI state #}
<script>
    // Listen for HTMX afterSwap events to re-initialize DataTables
    document.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'gqn-table-container') {
            // Check if DataTables is already initialized on this table and destroy if so
            if ($.fn.DataTable.isDataTable('#gqnTable')) {
                $('#gqnTable').DataTable().destroy(); 
            }
            // Initialize DataTables with specific settings
            $('#gqnTable').DataTable({
                "pageLength": 20, // Match ASP.NET PageSize
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "pagingType": "full_numbers", // Show all page numbers
                "columnDefs": [
                    { "orderable": false, "targets": [0, 1] } // SN and Select columns are not orderable
                ]
            });
        }
    });

    // Alpine.js component for managing supplier autocomplete suggestions visibility
    document.addEventListener('alpine:init', () => {
        Alpine.data('supplierAutocomplete', () => ({
            suggestionsVisible: false,
            init() {
                // Watch for HTMX loading state specific to the suggestion target
                this.$watch('$store.htmx.loading', (loading) => {
                    // Only show suggestions if not loading and there's content
                    if (this.$el.id === 'supplier-suggestions' && !loading) {
                        this.suggestionsVisible = this.$el.children.length > 0;
                    }
                });
                // Hide suggestions when input loses focus, with a slight delay
                this.$el.closest('.flex-grow').querySelector('#txtSupplier').addEventListener('blur', () => {
                    setTimeout(() => { this.suggestionsVisible = false; }, 100);
                });
                // Show suggestions when input gains focus
                this.$el.closest('.flex-grow').querySelector('#txtSupplier').addEventListener('focus', () => {
                    if (this.$el.children.length > 0) {
                        this.suggestionsVisible = true;
                    }
                });
            },
            // Function to select a suggestion and populate the input field
            selectSuggestion(value) {
                document.getElementById('txtSupplier').value = value;
                this.suggestionsVisible = false;
            }
        }));
    });
</script>
{% endblock %}

```

**File: `inventory/gqn/_gqn_table.html`** (Partial for DataTables Content)

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-md">
    <table id="gqnTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GRR No</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GIN No</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Supplier</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan No</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan Date</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if gqn_notes %}
                {% for note in gqn_notes %}
                <tr>
                    <td class="py-2 px-4 whitespace-nowrap text-right text-sm text-gray-500">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-center text-sm">
                        {# Link to the GQN Details page, mimicking original query parameters #}
                        <a href="{% url 'inventory:gqn_details' %}?Id={{ note.id }}&SupId={{ note.supplier_id|default:'' }}&GRRNo={{ note.grr_no }}&GINNo={{ note.gin_no|default:'' }}&GINId={{ note.inward_note.id|default:'' }}&PONo={{ note.po_no|default:'' }}&FyId={{ note.financial_year.id|default:'' }}&ModId=10&SubModId=46"
                           class="text-blue-600 hover:text-blue-900 font-bold py-1 px-2 rounded">
                            Select
                        </a>
                    </td>
                    <td class="py-2 px-4 whitespace-nowrap text-center text-sm text-gray-500">{{ note.financial_year_str }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-center text-sm text-gray-500">{{ note.grr_no }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-center text-sm text-gray-500">{{ note.formatted_sys_date }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-center text-sm text-gray-500">{{ note.gin_no|default:'' }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-center text-sm text-gray-500">{{ note.po_no|default:'' }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-left text-sm text-gray-500">{{ note.supplier_name_formatted }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-center text-sm text-gray-500">{{ note.challan_no|default:'' }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-center text-sm text-gray-500">{{ note.formatted_challan_date }}</td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="10" class="py-4 px-4 text-center text-lg text-red-700">No data to display !</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
    // This script block runs when this partial template is loaded/swapped by HTMX.
    // It ensures DataTables is correctly initialized on the new table content.
    $(document).ready(function() {
        // Destroy any pre-existing DataTable instance on this table to prevent conflicts
        if ($.fn.DataTable.isDataTable('#gqnTable')) {
            $('#gqnTable').DataTable().destroy(); 
        }
        // Initialize the DataTable with specified options
        $('#gqnTable').DataTable({
            "pageLength": 20, // Default number of rows per page
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]], // Options for rows per page
            "pagingType": "full_numbers", // Display "First", "Last", and numbered pages
            "columnDefs": [
                { "orderable": false, "targets": [0, 1] } // Disable sorting for 'SN' and 'Select' columns
            ]
        });
    });
</script>
```

**File: `inventory/gqn/_filter_fields.html`** (Partial for Dynamic Filter Inputs)

```html
{# This partial is swapped by HTMX to show/hide the appropriate input field based on search_type #}
<div id="filter-fields" class="flex-grow flex items-center space-x-2">
    {% if form.search_type.value == '0' %} {# If 'Supplier Name' is selected #}
        <div class="flex-grow relative">
            <label for="{{ form.search_supplier_name.id_for_label }}" class="sr-only">Supplier Name</label>
            {{ form.search_supplier_name }}
            {# Container for autocomplete suggestions, managed by Alpine.js for visibility #}
            <div id="supplier-suggestions" 
                 x-data="supplierAutocomplete" 
                 x-show="suggestionsVisible" 
                 @click.away="suggestionsVisible = false" 
                 class="absolute bg-white border border-gray-200 mt-1 w-full rounded-md shadow-lg z-10 max-h-60 overflow-y-auto">
                {# HTMX will swap suggestion list into this div #}
                {# Default content will be empty or a loading spinner #}
            </div>
            {% if form.search_supplier_name.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.search_supplier_name.errors }}</p>
            {% endif %}
        </div>
    {% else %} {# If 'GRR No' or 'PO No' is selected #}
        <div class="flex-grow">
            <label for="{{ form.search_field_text.id_for_label }}" class="sr-only">Search Value</label>
            {{ form.search_field_text }}
            {% if form.search_field_text.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.search_field_text.errors }}</p>
            {% endif %}
        </div>
    {% endif %}
</div>
{# HTMX loading spinner for this specific partial swap #}
<div id="filter-spinner" class="htmx-indicator inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500 hidden" role="status" aria-label="loading"></div>

```

**File: `inventory/gqn/_supplier_suggestions.html`** (Partial for Autocomplete Suggestions)

```html
{# This partial is swapped into #supplier-suggestions by the SupplierAutocompleteAPI HTMX call #}
{% if suggestions %}
    {% for suggestion in suggestions %}
        <div class="px-4 py-2 hover:bg-gray-100 cursor-pointer text-sm" 
             role="option"
             @click="selectSuggestion('{{ suggestion }}')"> {# Alpine.js click handler to populate input #}
            {{ suggestion }}
        </div>
    {% endfor %}
{% else %}
    <div class="px-4 py-2 text-sm text-gray-500">No suggestions found.</div>
{% endif %}
```

#### 4.5 URLs

**Task:** Define URL patterns for all the views, including the main page and the HTMX-specific partial endpoints.

**File: `inventory/urls.py`**

```python
from django.urls import path
from .views import GQNListView, GQNTablePartialView, FilterFieldsPartialView, SupplierAutocompleteAPI
from django.http import HttpResponse # For placeholder redirect

app_name = 'inventory' # Define app namespace for 'inventory:gqn_list' etc.

urlpatterns = [
    # Main page for the Goods Quality Note list
    path('gqn-list/', GQNListView.as_view(), name='gqn_list'),

    # HTMX endpoint to fetch and update the DataTables table content
    path('gqn-list/table/', GQNTablePartialView.as_view(), name='gqn_table_partial'),

    # HTMX endpoint to dynamically swap the search input fields when dropdown changes
    path('gqn-list/hx-filter-change/', FilterFieldsPartialView.as_view(), name='hx_filter_change'),

    # API endpoint for supplier autocomplete suggestions
    path('api/supplier-autocomplete/', SupplierAutocompleteAPI.as_view(), name='supplier_autocomplete_api'),
    
    # Placeholder for the GQN details page redirection.
    # In a real application, this would point to a dedicated view (e.g., a DetailView or UpdateView)
    # in another module or within this one, handling the GQN details.
    path('gqn-details/', lambda request: HttpResponse("<h3>Redirected to GQN Details Page.</h3><p>This is a placeholder for the actual details page implementation.</p>", status=200), name='gqn_details'),
]
```

#### 4.6 Tests

**Task:** Write comprehensive unit tests for the Django models and integration tests for the views. This ensures functionality, data integrity, and adherence to business logic, aiming for at least 80% test coverage.

**File: `inventory/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date
import json

from .models import (
    CompanyMaster, FinancialMaster, SupplierMaster, POMaster, InwardMaster,
    MaterialReceivedMaster, MaterialReceivedDetails, QualityMaster, QualityDetails
)
from .forms import GQNFilterForm

class GQNModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Establish base data required for all model tests
        cls.company = CompanyMaster.objects.create(id=1, company_name='TestCo')
        cls.fin_year_current = FinancialMaster.objects.create(id=2024, fin_year='2023-24', company=cls.company)
        cls.fin_year_old = FinancialMaster.objects.create(id=2023, fin_year='2022-23', company=cls.company)
        cls.supplier1 = SupplierMaster.objects.create(id=101, supplier_name='Supplier A', company=cls.company)
        cls.supplier2 = SupplierMaster.objects.create(id=102, supplier_name='Supplier B', company=cls.company)
        cls.po1 = POMaster.objects.create(id=1, po_no='PO001', supplier=cls.supplier1, company=cls.company)
        cls.po2 = POMaster.objects.create(id=2, po_no='PO002', supplier=cls.supplier2, company=cls.company)
        cls.inward1 = InwardMaster.objects.create(id=1001, po_master=cls.po1, challan_no='CH001', challan_date='2024-01-15', company=cls.company)
        cls.inward2 = InwardMaster.objects.create(id=1002, po_master=cls.po2, challan_no='CH002', challan_date='2024-02-20', company=cls.company)

        # Create MaterialReceivedMaster instances with different GQN statuses
        # Case 1: Needs GQN (GRRQty > GQNQty)
        cls.mrm_needs_gqn = MaterialReceivedMaster.objects.create(
            id=1, financial_year=cls.fin_year_current, grr_no='GRR001',
            gin_no='GIN001', inward_note=cls.inward1, sys_date='2024-03-01', company=cls.company
        )
        MaterialReceivedDetails.objects.create(id=1, material_received_master=cls.mrm_needs_gqn, received_qty=100)
        quality_master_needs_gqn = QualityMaster.objects.create(id=1, grr_master=cls.mrm_needs_gqn)
        QualityDetails.objects.create(id=1, quality_master=quality_master_needs_gqn, grr_master=cls.mrm_needs_gqn, accepted_qty=50, rejected_qty=10) # Total 60

        # Case 2: No GQN needed (GRRQty == GQNQty)
        cls.mrm_gqn_completed = MaterialReceivedMaster.objects.create(
            id=2, financial_year=cls.fin_year_current, grr_no='GRR002',
            gin_no='GIN002', inward_note=cls.inward2, sys_date='2024-03-05', company=cls.company
        )
        MaterialReceivedDetails.objects.create(id=2, material_received_master=cls.mrm_gqn_completed, received_qty=200)
        quality_master_completed = QualityMaster.objects.create(id=2, grr_master=cls.mrm_gqn_completed)
        QualityDetails.objects.create(id=2, quality_master=quality_master_completed, grr_master=cls.mrm_gqn_completed, accepted_qty=150, rejected_qty=50) # Total 200

        # Case 3: No GQN records yet (GRRQty > 0, GQNQty is 0 or NULL)
        cls.mrm_no_gqn_yet = MaterialReceivedMaster.objects.create(
            id=3, financial_year=cls.fin_year_current, grr_no='GRR003',
            gin_no='GIN003', inward_note=cls.inward1, sys_date='2024-03-10', company=cls.company
        )
        MaterialReceivedDetails.objects.create(id=3, material_received_master=cls.mrm_no_gqn_yet, received_qty=150)

        # Case 4: Old financial year, should not show with default filter
        cls.mrm_old_fy = MaterialReceivedMaster.objects.create(
            id=4, financial_year=cls.fin_year_old, grr_no='GRR004',
            gin_no='GIN004', inward_note=cls.inward2, sys_date='2023-01-01', company=cls.company
        )
        MaterialReceivedDetails.objects.create(id=4, material_received_master=cls.mrm_old_fy, received_qty=50)

        # Case 5: Material received with multiple details for GRRQty sum
        cls.mrm_multi_details = MaterialReceivedMaster.objects.create(
            id=5, financial_year=cls.fin_year_current, grr_no='GRR005',
            gin_no='GIN005', inward_note=cls.inward1, sys_date='2024-03-15', company=cls.company
        )
        MaterialReceivedDetails.objects.create(id=5, material_received_master=cls.mrm_multi_details, received_qty=20)
        MaterialReceivedDetails.objects.create(id=6, material_received_master=cls.mrm_multi_details, received_qty=30)
        # Total 50, no GQN yet, should be included

    def test_model_field_values(self):
        # Verify basic field data correctness
        note = MaterialReceivedMaster.objects.get(id=1)
        self.assertEqual(note.grr_no, 'GRR001')
        self.assertEqual(note.financial_year.fin_year, '2023-24')
        self.assertEqual(note.inward_note.id, self.inward1.id)
        self.assertEqual(note.sys_date, date(2024, 3, 1))

    def test_derived_properties(self):
        # Verify correctness of @property methods
        note = MaterialReceivedMaster.objects.get(id=1)
        self.assertEqual(note.formatted_sys_date, '01/03/2024')
        self.assertEqual(note.supplier_name_formatted, 'Supplier A [101]')
        self.assertEqual(note.po_no, 'PO001')
        self.assertEqual(note.challan_no, 'CH001')
        self.assertEqual(note.formatted_challan_date, '15/01/2024')
        self.assertEqual(note.supplier_id, 101)
        self.assertEqual(note.financial_year_str, '2023-24')
        
        # Test property for a case where related objects might be missing (though constrained by FKs)
        self.inward1.po_master = None # Temporarily break link
        self.inward1.save()
        note = MaterialReceivedMaster.objects.get(id=1)
        self.assertEqual(note.supplier_name_formatted, '')
        self.assertEqual(note.po_no, '')
        self.inward1.po_master = self.po1 # Restore link
        self.inward1.save()

    def test_for_gqn_listing_query_logic(self):
        # Test basic filtering (GRRQty - GQNQty > 0)
        queryset = MaterialReceivedMaster.objects.for_gqn_listing(
            company_id=self.company.id,
            financial_year_id=self.fin_year_current.id
        )
        self.assertIn(self.mrm_needs_gqn, queryset)     # GRR:100, GQN:60. Diff:40 > 0
        self.assertIn(self.mrm_no_gqn_yet, queryset)    # GRR:150, GQN:0. Diff:150 > 0
        self.assertIn(self.mrm_multi_details, queryset) # GRR:50, GQN:0. Diff:50 > 0
        self.assertNotIn(self.mrm_gqn_completed, queryset) # GRR:200, GQN:200. Diff:0, not > 0
        self.assertNotIn(self.mrm_old_fy, queryset)     # Old financial year

        self.assertEqual(queryset.count(), 3) # Should return 3 records

        # Test GRR No search filter
        queryset_grr = MaterialReceivedMaster.objects.for_gqn_listing(
            company_id=self.company.id,
            financial_year_id=self.fin_year_current.id,
            search_type='1',
            search_term='GRR001'
        )
        self.assertEqual(queryset_grr.count(), 1)
        self.assertEqual(queryset_grr.first(), self.mrm_needs_gqn)

        # Test PO No search filter
        queryset_po = MaterialReceivedMaster.objects.for_gqn_listing(
            company_id=self.company.id,
            financial_year_id=self.fin_year_current.id,
            search_type='2',
            search_term='PO001'
        )
        # MRM_needs_gqn and MRM_no_gqn_yet, MRM_multi_details use PO001 (via inward1)
        self.assertIn(self.mrm_needs_gqn, queryset_po)
        self.assertIn(self.mrm_no_gqn_yet, queryset_po)
        self.assertIn(self.mrm_multi_details, queryset_po)
        self.assertEqual(queryset_po.count(), 3)

        # Test Supplier Name search by exact formatted string ("Name [ID]")
        queryset_sup_id = MaterialReceivedMaster.objects.for_gqn_listing(
            company_id=self.company.id,
            financial_year_id=self.fin_year_current.id,
            search_type='0',
            search_term=f'Supplier A [{self.supplier1.id}]'
        )
        self.assertIn(self.mrm_needs_gqn, queryset_sup_id)
        self.assertIn(self.mrm_no_gqn_yet, queryset_sup_id)
        self.assertIn(self.mrm_multi_details, queryset_sup_id)
        self.assertEqual(queryset_sup_id.count(), 3)

        # Test Supplier Name search by partial name (fallback)
        queryset_sup_name = MaterialReceivedMaster.objects.for_gqn_listing(
            company_id=self.company.id,
            financial_year_id=self.fin_year_current.id,
            search_type='0',
            search_term='suppliER a' # Test case-insensitivity
        )
        self.assertIn(self.mrm_needs_gqn, queryset_sup_name)
        self.assertIn(self.mrm_no_gqn_yet, queryset_sup_name)
        self.assertIn(self.mrm_multi_details, queryset_sup_name)
        self.assertEqual(queryset_sup_name.count(), 3)

        # Test search with no matching results
        queryset_no_match = MaterialReceivedMaster.objects.for_gqn_listing(
            company_id=self.company.id,
            financial_year_id=self.fin_year_current.id,
            search_type='1',
            search_term='NONEXISTENT'
        )
        self.assertEqual(queryset_no_match.count(), 0)

class GQNFormsTest(TestCase):
    def test_gqn_filter_form_initialization(self):
        form = GQNFilterForm()
        self.assertEqual(form.fields['search_type'].initial, '0')
        self.assertTrue('display:none;' in form.fields['search_field_text'].widget.attrs['style'])
        self.assertFalse('display:none;' in form.fields['search_supplier_name'].widget.attrs['style'])

    def test_gqn_filter_form_supplier_name_visible(self):
        form = GQNFilterForm(data={'search_type': '0'})
        self.assertFalse('display:none;' in form.fields['search_supplier_name'].widget.attrs['style'])
        self.assertTrue('display:none;' in form.fields['search_field_text'].widget.attrs['style'])

    def test_gqn_filter_form_text_field_visible(self):
        form = GQNFilterForm(data={'search_type': '1'})
        self.assertTrue('display:none;' in form.fields['search_supplier_name'].widget.attrs['style'])
        self.assertFalse('display:none;' in form.fields['search_field_text'].widget.attrs['style'])

    def test_gqn_filter_form_valid_data_supplier(self):
        form = GQNFilterForm(data={'search_type': '0', 'search_supplier_name': 'Test Supplier'})
        self.assertTrue(form.is_valid())

    def test_gqn_filter_form_valid_data_grr_po(self):
        form = GQNFilterForm(data={'search_type': '1', 'search_field_text': 'GRR123'})
        self.assertTrue(form.is_valid())
        form = GQNFilterForm(data={'search_type': '2', 'search_field_text': 'PO456'})
        self.assertTrue(form.is_valid())

    def test_gqn_filter_form_invalid_missing_supplier_name(self):
        form = GQNFilterForm(data={'search_type': '0', 'search_supplier_name': ''})
        self.assertFalse(form.is_valid())
        self.assertIn('Supplier Name is required for this search type.', form.errors['search_supplier_name'])

    def test_gqn_filter_form_invalid_missing_text_field(self):
        form = GQNFilterForm(data={'search_type': '1', 'search_field_text': ''})
        self.assertFalse(form.is_valid())
        self.assertIn('Search value is required for this search type.', form.errors['search_field_text'])

class GQNViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for views
        cls.company = CompanyMaster.objects.create(id=1, company_name='TestCo')
        cls.fin_year = FinancialMaster.objects.create(id=2024, fin_year='2023-24', company=cls.company)
        cls.supplier = SupplierMaster.objects.create(id=101, supplier_name='Supplier A', company=cls.company)
        cls.po = POMaster.objects.create(id=1, po_no='PO001', supplier=cls.supplier, company=cls.company)
        cls.inward = InwardMaster.objects.create(id=1001, po_master=cls.po, challan_no='CH001', challan_date='2024-01-15', company=cls.company)
        cls.mrm = MaterialReceivedMaster.objects.create(
            id=1, financial_year=cls.fin_year, grr_no='GRR001',
            gin_no='GIN001', inward_note=cls.inward, sys_date='2024-03-01', company=cls.company
        )
        MaterialReceivedDetails.objects.create(id=1, material_received_master=cls.mrm, received_qty=100) # Ensure it passes the GQN filter

    def setUp(self):
        self.client = Client()
        # Simulate user login and set session data expected by views
        session = self.client.session
        session['compid'] = self.company.id
        session['finyear'] = self.fin_year.id
        session.save()
        # Mock a logged-in user for LoginRequiredMixin
        # In a real app, you might have a custom user model or a test user fixture
        from django.contrib.auth.models import User
        self.user = User.objects.create_user(username='testuser', password='password123')
        self.client.login(username='testuser', password='password123')


    def test_gqn_list_view_get(self):
        response = self.client.get(reverse('inventory:gqn_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/gqn/list.html')
        self.assertContains(response, 'Goods Quality Note [GQN] - New')
        self.assertIsInstance(response.context['form'], GQNFilterForm)
        self.assertContains(response, '<div id="gqn-table-container"') # Check for table container

    def test_gqn_table_partial_view_get_initial_load(self):
        response = self.client.get(
            reverse('inventory:gqn_table_partial'),
            HTTP_HX_REQUEST='true' # Simulate HTMX request
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/gqn/_gqn_table.html')
        self.assertIn('gqn_notes', response.context)
        self.assertContains(response, self.mrm.grr_no) # Check if data is present
        self.assertContains(response, 'table id="gqnTable"') # Check for the table element

    def test_gqn_table_partial_view_post_search(self):
        # Simulate a search by GRR No via POST (as form submission is POST)
        response = self.client.post(
            reverse('inventory:gqn_table_partial'),
            {'search_type': '1', 'search_field_text': 'GRR001'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/gqn/_gqn_table.html')
        self.assertIn('gqn_notes', response.context)
        self.assertEqual(response.context['gqn_notes'].count(), 1)
        self.assertEqual(response.context['gqn_notes'].first().grr_no, 'GRR001')

    def test_gqn_table_partial_view_post_supplier_search(self):
        # Simulate a search by Supplier Name via POST
        response = self.client.post(
            reverse('inventory:gqn_table_partial'),
            {'search_type': '0', 'search_supplier_name': f'Supplier A [{self.supplier.id}]'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/gqn/_gqn_table.html')
        self.assertIn('gqn_notes', response.context)
        self.assertEqual(response.context['gqn_notes'].count(), 1)
        self.assertEqual(response.context['gqn_notes'].first().supplier_name_formatted, f'Supplier A [{self.supplier.id}]')

    def test_filter_fields_partial_view(self):
        # Simulate dropdown change (POST request for hx-post)
        response = self.client.post(
            reverse('inventory:hx_filter_change'),
            {'search_type': '1'}, # Change to GRR No
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/gqn/_filter_fields.html')
        # Check that the Txtfield is rendered and txtSupplier is effectively hidden by style
        self.assertContains(response, 'id="Txtfield"')
        self.assertContains(response, 'id="txtSupplier"') # Element is still there, but style='display:none;' will be applied

    def test_supplier_autocomplete_api(self):
        response = self.client.get(
            reverse('inventory:supplier_autocomplete_api'),
            {'prefixText': 'Suppli'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        suggestions = json.loads(response.content)
        self.assertIsInstance(suggestions, list)
        self.assertIn(f'Supplier A [{self.supplier.id}]', suggestions)
        self.assertEqual(len(suggestions), 1)

    def test_supplier_autocomplete_api_no_prefix(self):
        response = self.client.get(
            reverse('inventory:supplier_autocomplete_api'),
            {'prefixText': ''},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(json.loads(response.content), [])

    def test_supplier_autocomplete_api_no_match(self):
        response = self.client.get(
            reverse('inventory:supplier_autocomplete_api'),
            {'prefixText': 'XYZ'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(json.loads(response.content), [])

    def test_gqn_details_redirect_url(self):
        # Test that the 'Select' link generates the correct URL and target page is accessible
        # This only tests the URL generation and that the target placeholder exists.
        expected_params = {
            'Id': self.mrm.id,
            'SupId': self.mrm.supplier_id,
            'GRRNo': self.mrm.grr_no,
            'GINNo': self.mrm.gin_no,
            'GINId': self.mrm.inward_note.id,
            'PONo': self.mrm.po_no,
            'FyId': self.mrm.financial_year.id,
            'ModId': 10,
            'SubModId': 46
        }
        # Construct the expected URL with query parameters
        expected_url = reverse('inventory:gqn_details') + '?' + '&'.join([f"{k}={v}" for k, v in expected_params.items()])
        
        # Test a direct GET request to the placeholder URL
        response = self.client.get(expected_url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Redirected to GQN Details Page.")

```

## Step 5: HTMX and Alpine.js Integration

The integration of HTMX and Alpine.js is crucial for achieving a modern, responsive user experience without complex client-side frameworks.

*   **HTMX for Dynamic Updates:**
    *   The main list page (`list.html`) leverages `hx-trigger="load"` to initially populate the `#gqn-table-container` with content from `gqn_table_partial/`.
    *   Any submission of the filter form (`gqn-filter-form`) uses `hx-post` to send data to `gqn_table_partial/` and `hx-target` to swap only the table content, avoiding full page reloads.
    *   The `search_type` dropdown's `hx-post` updates the `#filter-fields` div, dynamically showing or hiding the appropriate search input (`txtSupplier` or `Txtfield`).
    *   The `txtSupplier` input uses `hx-get` to query `supplier-autocomplete/` and `hx-target` to load the suggestions into `#supplier-suggestions` in real-time as the user types.
    *   `hx-indicator` attributes provide visual feedback (spinners) during AJAX requests.
*   **Alpine.js for UI State Management:**
    *   An Alpine.js component (`x-data="supplierAutocomplete"`) is used to manage the `suggestionsVisible` state for the autocomplete dropdown. This allows the suggestion list to appear/disappear based on user interaction (typing, focus, click-away) without requiring complex JavaScript.
    *   The `selectSuggestion` Alpine.js method ensures that clicking a suggestion populates the input field and hides the suggestion list, providing a smooth user experience.
*   **DataTables for List Views:**
    *   The `_gqn_table.html` partial explicitly defines an HTML `<table>` with `id="gqnTable"`.
    *   A JavaScript block within this partial (executed via `$(document).ready()`) initializes DataTables on this table. This ensures DataTables is re-initialized correctly every time the table content is swapped by HTMX.
    *   DataTables handles client-side pagination, instant searching, and column sorting, offloading these operations from the server and providing a highly interactive grid.
*   **Seamless Interaction:** All user interactions (filtering, searching, selecting autocomplete suggestions) are designed to function without full page reloads, mirroring a modern single-page application experience.
*   **DRY Template Structure:** Partials like `_gqn_table.html`, `_filter_fields.html`, and `_supplier_suggestions.html` promote reusability and maintainability.

## Final Notes

*   **Database Mapping:** The models use `managed = False` and `db_table='[TABLE_NAME]'` to connect to your existing ASP.NET database tables. Ensure your Django `settings.py` is configured with the correct database connection details.
*   **Session Data:** The views assume `request.session['compid']` and `request.session['finyear']` are available, mimicking the ASP.NET `Session` usage. In a production environment, ensure these are robustly set (e.g., during user login or from a user profile).
*   **Error Handling:** While Django's ORM handles many database errors, specific business logic exceptions or validation needs would be added to model methods or forms as required.
*   **Tailwind CSS:** All HTML elements are styled using Tailwind CSS utility classes, assuming Tailwind is correctly set up in your Django project.
*   **Extensibility:** This plan provides a solid foundation. Future enhancements (e.g., server-side processing for DataTables with very large datasets, more advanced filtering, adding actual CRUD for GQN details) can be built upon this architecture.
*   **AI Automation:** The provided structure and code are designed to be easily generated, parsed, and iterated upon by AI tools, accelerating the migration process significantly.