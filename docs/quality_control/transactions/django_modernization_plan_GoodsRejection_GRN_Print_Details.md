## ASP.NET to Django Conversion Script: Goods Rejection Report Modernization

This document outlines a strategic plan for modernizing your legacy ASP.NET Goods Rejection Report functionality into a robust, scalable, and maintainable Django application. Our approach leverages cutting-edge web technologies like HTMX and Alpine.js, ensuring a highly interactive user experience without the complexity of traditional JavaScript frameworks. By adopting Django's "Fat Model, Thin View" architecture, we enhance code clarity, promote reusability, and significantly reduce development and maintenance efforts.

This modernization focuses on automation-driven approaches, providing clear, step-by-step instructions designed to be actionable through AI-assisted tools and non-technical guidance. The goal is to deliver a solution that is not only technologically advanced but also provides tangible business benefits, such as improved performance, easier feature development, and reduced operational costs.

### Business Value & Outcomes:
*   **Enhanced User Experience:** Modern, responsive interfaces using HTMX and Alpine.js deliver a smoother, faster interaction without full page reloads.
*   **Reduced Development Costs:** Django's conventions, ORM, and "Fat Model, Thin View" principles simplify development, making it quicker to build and extend features.
*   **Improved Maintainability:** Clean code separation, comprehensive testing, and adherence to DRY principles mean less time spent on bug fixing and easier updates.
*   **Scalability & Performance:** Django's robust architecture is designed for high-traffic applications, ensuring your system can grow with your business needs.
*   **Future-Proofing:** Transitioning to a widely adopted, open-source framework like Django ensures your application remains relevant and supported for years to come.

---

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the Goods Rejection Report.

**Analysis:**
The ASP.NET code fetches data from numerous tables to compile the Goods Rejection report. The primary tables central to this module and the report generation are:

*   `tblQc_MaterialQuality_Master`: Main table for Goods Rejection records.
*   `tblQc_MaterialQuality_Details`: Details of rejected items for a specific master record.
*   `tblQc_Rejection_Reason`: Lookup table for rejection reasons.
*   `tblDG_Item_Master`: Master data for items (products).
*   `Unit_Master`: Lookup table for units of measurement.
*   `tblMM_Supplier_master`: Master data for suppliers.

Other tables like `tblinv_MaterialReceived_Master`, `tblinv_MaterialReceived_Details`, `tblInv_Inward_Master`, `tblInv_Inward_Details`, `tblMM_PO_Details`, `tblMM_PO_Master`, `tblMM_PR_Master`, `tblMM_PR_Details`, `tblMM_SPR_Master`, `tblMM_SPR_Details` are also accessed for comprehensive data aggregation.

**Inferred Tables and Columns:**

*   **`tblQc_MaterialQuality_Master`** (`GoodsRejectionMaster` in Django)
    *   `Id` (Primary Key)
    *   `FinYearId` (Integer)
    *   `GQNNo` (String)
    *   `GRRNo` (String)
    *   `SysDate` (DateTime)
    *   `CompId` (Integer)

*   **`tblQc_MaterialQuality_Details`** (`GoodsRejectionDetail` in Django)
    *   `Id` (Primary Key)
    *   `MId` (Foreign Key to `tblQc_MaterialQuality_Master.Id`)
    *   `AcceptedQty` (Decimal)
    *   `RejectedQty` (Decimal)
    *   `RejectionReason` (Foreign Key to `tblQc_Rejection_Reason.Id`)
    *   `Remarks` (String)
    *   `GRRId` (Foreign Key to `tblinv_MaterialReceived_Master.Id`)
    *   `DGRRId` (Foreign Key to `tblinv_MaterialReceived_Details.Id`)

*   **`tblQc_Rejection_Reason`** (`RejectionReason` in Django)
    *   `Id` (Primary Key)
    *   `Symbol` (String - The reason symbol/name)

*   **`tblDG_Item_Master`** (`ItemMaster` in Django)
    *   `Id` (Primary Key)
    *   `ItemCode` (String)
    *   `ManfDesc` (String - Manufacturer description)
    *   `UOMBasic` (Foreign Key to `Unit_Master.Id`)
    *   `CompId` (Integer)

*   **`Unit_Master`** (`UnitMaster` in Django)
    *   `Id` (Primary Key)
    *   `Symbol` (String - Unit symbol/name)

*   **`tblMM_Supplier_master`** (`SupplierMaster` in Django)
    *   `SupplierId` (Primary Key - used as a unique identifier)
    *   `SupplierName` (String)
    *   `CompId` (Integer)

(Other tables like `tblinv_MaterialReceived_Master`, `tblInv_Inward_Master`, `tblMM_PO_Master`, etc., will be represented as minimal models to facilitate relationships or assumed to be handled by other Django apps, as their full schema is not the primary focus of *this* report's core data entry.)

### Step 2: Identify Backend Functionality

**Task:** Determine the operations performed by the ASP.NET code.

**Analysis:**
The provided ASP.NET page `GoodsRejection_GRN_Print_Details.aspx` and its code-behind `GoodsRejection_GRN_Print_Details.aspx.cs` are designed purely for **Read** operations.

*   **Read:** The core functionality involves retrieving a comprehensive set of "Goods Rejection" related data from various interconnected database tables based on query string parameters (`GRRNo`, `GINNo`, `PONo`, `FyId`, `Id`) and session variables (`username`, `finyear`, `compid`). This aggregated data is then used to populate a Crystal Report for printing.
*   **No Create, Update, or Delete:** There are no functionalities for creating, updating, or deleting records directly from this page. It is a report display page.
*   **Navigation:** A "Cancel" button (`btnCancel`) redirects the user back to `GoodsRejection_GRN.aspx`.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**
The ASP.NET page primarily uses:

*   **`CR:CrystalReportViewer`:** The main component for displaying the Crystal Report. In Django, this will be replaced by direct HTML rendering of the aggregated data, often styled to mimic a report format, or by generating a PDF dynamically.
*   **`CR:CrystalReportSource`:** Defines the path to the `.rpt` file. This logic will be encapsulated in the Django model's data aggregation methods.
*   **`asp:Panel`:** A container for the report viewer. This translates to a simple `div` in HTML.
*   **`asp:Button` (`btnCancel`):** A button for navigation. This will become a simple `<a>` tag or an HTMX-powered button triggering a redirect.

Since the original page is a print/report view, there are no typical data input controls like `TextBox` or `DropDownList` for editing data directly on this page.

### Step 4: Generate Django Code

The Django application will be named `quality_control`.

#### 4.1 Models

**Task:** Create Django models based on the identified database schema. The primary focus is `GoodsRejectionMaster` and its details, with methods for aggregating complex report data.

**Filename:** `quality_control/models.py`

```python
from django.db import models
from django.db.models import F, Sum, Q
from django.conf import settings
from datetime import datetime

# --- Dependent/Lookup Models (Minimal definitions for FK relationships) ---

class UnitMaster(models.Model):
    """
    Corresponds to Unit_Master table.
    Used for Units of Measurement (UOM).
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit Master'
        verbose_name_plural = 'Unit Masters'

    def __str__(self):
        return self.symbol or f"Unit {self.id}"

class ItemMaster(models.Model):
    """
    Corresponds to tblDG_Item_Master table.
    Used for item (product) master data.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=100, blank=True, null=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True)
    uom_basic = models.ForeignKey(UnitMaster, on_delete=models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.item_code or f"Item {self.id}"

    # Utility method mimicking fun.GetItemCode_PartNo
    @staticmethod
    def get_item_code_part_no(comp_id, item_id):
        try:
            item = ItemMaster.objects.get(id=item_id, comp_id=comp_id)
            return item.item_code
        except ItemMaster.DoesNotExist:
            return "N/A"

class SupplierMaster(models.Model):
    """
    Corresponds to tblMM_Supplier_master table.
    Used for supplier master data.
    """
    supplier_id = models.IntegerField(db_column='SupplierId', primary_key=True) # Assuming SupplierId is PK
    supplier_name = models.CharField(db_column='SupplierName', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier Master'
        verbose_name_plural = 'Supplier Masters'

    def __str__(self):
        return self.supplier_name or f"Supplier {self.supplier_id}"

class RejectionReason(models.Model):
    """
    Corresponds to tblQc_Rejection_Reason table.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblQc_Rejection_Reason'
        verbose_name = 'Rejection Reason'
        verbose_name_plural = 'Rejection Reasons'

    def __str__(self):
        return self.symbol or f"Reason {self.id}"

# --- Core Module Models ---

# Minimal definition for tblinv_MaterialReceived_Master/Details
class MaterialReceivedMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    gin_no = models.CharField(db_column='GINNo', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialReceived_Master'

class MaterialReceivedDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    m_id = models.ForeignKey(MaterialReceivedMaster, on_delete=models.DO_NOTHING, db_column='MId', blank=True, null=True)
    received_qty = models.DecimalField(db_column='ReceivedQty', max_digits=18, decimal_places=3, blank=True, null=True)
    po_id = models.IntegerField(db_column='POId', blank=True, null=True) # This likely links to tblMM_PO_Details.Id

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialReceived_Details'

# Minimal definition for tblInv_Inward_Master/Details
class InwardMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    pono = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    challan_no = models.CharField(db_column='ChallanNo', max_length=50, blank=True, null=True)
    challan_date = models.DateTimeField(db_column='ChallanDate', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Master'

class InwardDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    gin_id = models.ForeignKey(InwardMaster, on_delete=models.DO_NOTHING, db_column='GINId', blank=True, null=True)
    po_id = models.IntegerField(db_column='POId', blank=True, null=True) # Likely links to tblMM_PO_Details.Id
    received_qty = models.DecimalField(db_column='ReceivedQty', max_digits=18, decimal_places=3, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Details'

# Minimal definition for tblMM_PO_Master/Details (Purchase Order)
class POMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    pono = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    prspr_flag = models.CharField(db_column='PRSPRFlag', max_length=1, blank=True, null=True) # 0 for PR, 1 for SPR
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    supplier = models.ForeignKey(SupplierMaster, on_delete=models.DO_NOTHING, db_column='SupplierId', to_field='supplier_id', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'

class PODetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    m_id = models.ForeignKey(POMaster, on_delete=models.DO_NOTHING, db_column='MId', blank=True, null=True)
    pono = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    prno = models.CharField(db_column='PRNo', max_length=50, blank=True, null=True)
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3, blank=True, null=True)
    pr_id = models.IntegerField(db_column='PRId', blank=True, null=True) # Likely links to tblMM_PR_Details.Id
    sprno = models.CharField(db_column='SPRNo', max_length=50, blank=True, null=True)
    spr_id = models.IntegerField(db_column='SPRId', blank=True, null=True) # Likely links to tblMM_SPR_Details.Id

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Details'

# Minimal definition for tblMM_PR_Master/Details (Purchase Requisition)
class PRMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    prno = models.CharField(db_column='PRNo', max_length=50, blank=True, null=True)
    wono = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Master'

class PRDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    m_id = models.ForeignKey(PRMaster, on_delete=models.DO_NOTHING, db_column='MId', blank=True, null=True)
    prno = models.CharField(db_column='PRNo', max_length=50, blank=True, null=True)
    item = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId', blank=True, null=True)
    ahid = models.IntegerField(db_column='AHId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Details'

# Minimal definition for tblMM_SPR_Master/Details (Store Purchase Requisition)
class SPRMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sprno = models.CharField(db_column='SPRNo', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Master'

class SPRDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    m_id = models.ForeignKey(SPRMaster, on_delete=models.DO_NOTHING, db_column='MId', blank=True, null=True)
    sprno = models.CharField(db_column='SPRNo', max_length=50, blank=True, null=True)
    item = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId', blank=True, null=True)
    wono = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    dept_id = models.IntegerField(db_column='DeptId', blank=True, null=True)
    ahid = models.IntegerField(db_column='AHId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Details'


class GoodsRejectionMaster(models.Model):
    """
    Corresponds to tblQc_MaterialQuality_Master.
    Represents a master record for Goods Rejection.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    gqn_no = models.CharField(db_column='GQNNo', max_length=50, blank=True, null=True)
    grr_no = models.CharField(db_column='GRRNo', max_length=50, blank=True, null=True)
    sys_date = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblQc_MaterialQuality_Master'
        verbose_name = 'Goods Rejection'
        verbose_name_plural = 'Goods Rejections'

    def __str__(self):
        return f"GRR No: {self.grr_no or 'N/A'}"

    # Business logic method for fetching report data
    def get_grn_report_data(self, comp_id, grr_id):
        """
        Aggregates all necessary data for the GRN print report,
        mimicking the complex joins in the ASP.NET code.
        This method is designed to be called from a view to prepare context.
        """
        report_details = []
        grr_master_data = {}

        try:
            # Fetch main Goods Rejection Master data
            master_record = GoodsRejectionMaster.objects.get(id=grr_id, comp_id=comp_id)
            grr_master_data['GRRNo'] = master_record.grr_no
            grr_master_data['GINNO'] = None # Will be populated from Inward/MaterialReceived
            grr_master_data['GQNNO'] = master_record.gqn_no
            grr_master_data['ChallanNo'] = None
            grr_master_data['ChallanDate'] = None
            grr_master_data['SupplierName'] = None # Will be populated from PO

            # Fetch details
            rejected_details = GoodsRejectionDetail.objects.filter(
                m_id=master_record.id,
                rejected_qty__gt=0 # Only include rejected items
            ).select_related('rejection_reason') # Eager load rejection reason

            for detail in rejected_details:
                row_data = {
                    'Id': detail.id,
                    'AcceptedQty': detail.accepted_qty,
                    'RejectedQty': detail.rejected_qty,
                    'RejReason': detail.rejection_reason.symbol if detail.rejection_reason else '',
                    'Remarks': detail.remarks,
                    'CompId': comp_id,
                    'ItemCode': 'N/A',
                    'PurchDesc': 'N/A',
                    'UOMPurch': 'N/A',
                    'POQty': 0.0,
                    'InvQty': 0.0,
                    'RecedQty': 0.0,
                }

                # Lookup MaterialReceivedMaster and Detail
                material_received_detail = MaterialReceivedDetail.objects.filter(
                    id=detail.dgrr_id,
                    m_id=detail.grr_id # This matches the ASP.NET logic where GRRId in detail maps to MId in MR Master
                ).first()

                if material_received_detail:
                    row_data['RecedQty'] = float(material_received_detail.received_qty or 0.0)
                    material_received_master = material_received_detail.m_id
                    if material_received_master:
                        grr_master_data['GINNO'] = material_received_master.gin_no

                        # Lookup InwardMaster and Detail
                        inward_detail = InwardDetail.objects.filter(
                            po_id=material_received_detail.po_id,
                            gin_id=material_received_master.gin_no # Assuming GINId here maps to GINNo
                        ).first()

                        if inward_detail and inward_detail.gin_id:
                            inward_master = inward_detail.gin_id
                            row_data['InvQty'] = float(inward_detail.received_qty or 0.0)
                            grr_master_data['ChallanNo'] = inward_master.challan_no
                            grr_master_data['ChallanDate'] = inward_master.challan_date.strftime('%d/%m/%Y') if inward_master.challan_date else ''

                            # Lookup PO Master and Detail
                            po_detail = PODetail.objects.filter(
                                id=inward_detail.po_id,
                                m_id__pono=inward_master.pono, # Join via pono as in ASP.NET
                                m_id__comp_id=comp_id,
                            ).select_related('m_id', 'm_id__supplier').first() # Eager load PO Master and Supplier

                            if po_detail:
                                row_data['POQty'] = float(po_detail.qty or 0.0)
                                if po_detail.m_id and po_detail.m_id.supplier:
                                    grr_master_data['SupplierName'] = po_detail.m_id.supplier.supplier_name

                                if po_detail.m_id.prspr_flag == '0': # PR Flag
                                    pr_detail = PRDetail.objects.filter(
                                        id=po_detail.pr_id,
                                        prno=po_detail.prno,
                                        m_id__comp_id=comp_id
                                    ).select_related('item', 'item__uom_basic').first()
                                    if pr_detail and pr_detail.item:
                                        row_data['ItemCode'] = ItemMaster.get_item_code_part_no(comp_id, pr_detail.item.id)
                                        row_data['PurchDesc'] = pr_detail.item.manf_desc
                                        row_data['UOMPurch'] = pr_detail.item.uom_basic.symbol if pr_detail.item.uom_basic else ''
                                elif po_detail.m_id.prspr_flag == '1': # SPR Flag
                                    spr_detail = SPRDetail.objects.filter(
                                        id=po_detail.spr_id,
                                        sprno=po_detail.sprno,
                                        m_id__comp_id=comp_id
                                    ).select_related('item', 'item__uom_basic').first()
                                    if spr_detail and spr_detail.item:
                                        row_data['ItemCode'] = ItemMaster.get_item_code_part_no(comp_id, spr_detail.item.id)
                                        row_data['PurchDesc'] = spr_detail.item.manf_desc
                                        row_data['UOMPurch'] = spr_detail.item.uom_basic.symbol if spr_detail.item.uom_basic else ''
                report_details.append(row_data)

            # This is a placeholder for the company address, assuming a utility function
            # or a setting somewhere. In a real system, this would come from a CompanyProfile model.
            grr_master_data['Address'] = "Your Company Address Line 1\nYour Company Address Line 2\nCity, State, Zip"


        except GoodsRejectionMaster.DoesNotExist:
            pass # Handle case where master record is not found

        return {
            'master_data': grr_master_data,
            'details': report_details
        }

class GoodsRejectionDetail(models.Model):
    """
    Corresponds to tblQc_MaterialQuality_Details.
    Details of rejected items associated with a GoodsRejectionMaster.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    m_id = models.ForeignKey(GoodsRejectionMaster, on_delete=models.DO_NOTHING, db_column='MId', blank=True, null=True)
    accepted_qty = models.DecimalField(db_column='AcceptedQty', max_digits=18, decimal_places=3, blank=True, null=True)
    rejected_qty = models.DecimalField(db_column='RejectedQty', max_digits=18, decimal_places=3, blank=True, null=True)
    rejection_reason = models.ForeignKey(RejectionReason, on_delete=models.DO_NOTHING, db_column='RejectionReason', blank=True, null=True)
    remarks = models.CharField(db_column='Remarks', max_length=500, blank=True, null=True)
    grr_id = models.IntegerField(db_column='GRRId', blank=True, null=True) # ID from tblinv_MaterialReceived_Master
    dgrr_id = models.IntegerField(db_column='DGRRId', blank=True, null=True) # ID from tblinv_MaterialReceived_Details

    class Meta:
        managed = False
        db_table = 'tblQc_MaterialQuality_Details'
        verbose_name = 'Goods Rejection Detail'
        verbose_name_plural = 'Goods Rejection Details'

    def __str__(self):
        return f"Detail {self.id} for GRR {self.m_id_id}"

```

#### 4.2 Forms

**Task:** Define Django forms for user input. Since this specific ASP.NET page is a report/print view and does not involve direct data entry or modification, no form is required for its direct translation.

However, as per the request to provide a comprehensive plan including CRUD, we would define a `GoodsRejectionMasterForm` for a typical data entry scenario if this model were editable.

**Filename:** `quality_control/forms.py`

```python
from django import forms
from .models import GoodsRejectionMaster

class GoodsRejectionMasterForm(forms.ModelForm):
    """
    Form for creating/updating GoodsRejectionMaster records.
    (Not directly used by the GRN_Print_Details page, but for typical CRUD operations).
    """
    class Meta:
        model = GoodsRejectionMaster
        fields = ['grr_no', 'gqn_no', 'sys_date', 'fin_year_id', 'comp_id']
        widgets = {
            'grr_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'gqn_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sys_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'fin_year_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'comp_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
    
    # Example custom validation
    def clean_grr_no(self):
        grr_no = self.cleaned_data['grr_no']
        # Add your specific validation logic here, e.g., uniqueness check
        return grr_no

```

#### 4.3 Views

**Task:** Implement the report display using a Django CBV. We will also include standard CRUD views for the `GoodsRejectionMaster` model to fulfill the template requirements, even though the original ASP.NET page is only a report.

**Filename:** `quality_control/views.py`

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import get_object_or_404
from .models import GoodsRejectionMaster, GoodsRejectionDetail # Import all necessary models
from .forms import GoodsRejectionMasterForm

# --- General CRUD Views for GoodsRejectionMaster (as per template requirements) ---

class GoodsRejectionMasterListView(ListView):
    model = GoodsRejectionMaster
    template_name = 'quality_control/goodsrejectionmaster/list.html'
    context_object_name = 'goodsrejectionmasters'

class GoodsRejectionMasterCreateView(CreateView):
    model = GoodsRejectionMaster
    form_class = GoodsRejectionMasterForm
    template_name = 'quality_control/goodsrejectionmaster/form.html'
    success_url = reverse_lazy('goodsrejectionmaster_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Goods Rejection Master added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshGoodsRejectionMasterList'
                }
            )
        return response

class GoodsRejectionMasterUpdateView(UpdateView):
    model = GoodsRejectionMaster
    form_class = GoodsRejectionMasterForm
    template_name = 'quality_control/goodsrejectionmaster/form.html'
    success_url = reverse_lazy('goodsrejectionmaster_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Goods Rejection Master updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshGoodsRejectionMasterList'
                }
            )
        return response

class GoodsRejectionMasterDeleteView(DeleteView):
    model = GoodsRejectionMaster
    template_name = 'quality_control/goodsrejectionmaster/confirm_delete.html'
    success_url = reverse_lazy('goodsrejectionmaster_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Goods Rejection Master deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshGoodsRejectionMasterList'
                }
            )
        return response

class GoodsRejectionMasterTablePartialView(ListView):
    """
    Renders only the table content for HTMX partial updates.
    """
    model = GoodsRejectionMaster
    template_name = 'quality_control/goodsrejectionmaster/_goodsrejectionmaster_table.html'
    context_object_name = 'goodsrejectionmasters'

# --- Specific View for Goods Rejection Report (mimicking ASP.NET page) ---

class GoodsRejectionReportView(DetailView):
    """
    Displays the Goods Rejection report, fetching data via model methods.
    This replaces the Crystal Report Viewer functionality.
    """
    model = GoodsRejectionMaster
    template_name = 'quality_control/goodsrejectionmaster/grn_report.html'
    context_object_name = 'grn_master'
    pk_url_kwarg = 'id' # Map 'id' from URL to primary key

    def get_object(self, queryset=None):
        """
        Retrieves the GoodsRejectionMaster object based on the 'id' URL parameter.
        """
        # The ASP.NET code uses Request.QueryString["Id"]
        master_id = self.request.GET.get('Id') # Use GET for query string, like ASP.NET Request.QueryString
        if not master_id:
            # Fallback to PK if Id is not in GET (for direct URL access or different routing)
            master_id = self.kwargs.get(self.pk_url_kwarg)
        
        return get_object_or_404(GoodsRejectionMaster, id=master_id)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Access session variables similar to ASP.NET Session[]
        comp_id = self.request.session.get('compid')
        fin_year_id = self.request.session.get('finyear')
        username = self.request.session.get('username')

        # Retrieve GRR ID from object or query string (matching ASP.NET logic)
        grr_id = self.object.id 
        
        if not comp_id:
            messages.error(self.request, "Company ID not found in session. Please log in.")
            return context # Or redirect to login

        # Call the fat model method to get all aggregated report data
        report_data = self.object.get_grn_report_data(comp_id, grr_id)
        
        context['report_master_data'] = report_data['master_data']
        context['report_details'] = report_data['details']
        context['fin_year_id'] = fin_year_id
        context['username'] = username
        
        return context

```

#### 4.4 Templates

**Task:** Create templates for each view, including the main report page.

**Filename:** `quality_control/templates/quality_control/goodsrejectionmaster/list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Goods Rejections</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'goodsrejectionmaster_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Goods Rejection
        </button>
    </div>
    
    <div id="goodsrejectionmasterTable-container"
         hx-trigger="load, refreshGoodsRejectionMasterList from:body"
         hx-get="{% url 'goodsrejectionmaster_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading Goods Rejections...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed
    });
</script>
{% endblock %}
```

**Filename:** `quality_control/templates/quality_control/goodsrejectionmaster/_goodsrejectionmaster_table.html`

```html
<table id="goodsrejectionmasterTable" class="min-w-full bg-white">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GRR No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GQN No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in goodsrejectionmasters %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.grr_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.gqn_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.sys_date|date:"d/m/Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'goodsrejectionmaster_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'goodsrejectionmaster_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
                <a href="{% url 'goodsrejection_grn_report' %}?Id={{ obj.pk }}" 
                   class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded">
                   Print Report
                </a>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    $('#goodsrejectionmasterTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
    });
});
</script>
```

**Filename:** `quality_control/templates/quality_control/goodsrejectionmaster/form.html`

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Goods Rejection Master</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**Filename:** `quality_control/templates/quality_control/goodsrejectionmaster/confirm_delete.html`

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete this Goods Rejection Master record ({{ object.grr_no }})?</p>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

**Filename:** `quality_control/templates/quality_control/goodsrejectionmaster/grn_report.html` (The actual report page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6 print:hidden">
        <h2 class="text-2xl font-bold">Goods Rejection Report (GRR: {{ grn_master.grr_no }})</h2>
        <a href="{% url 'goodsrejectionmaster_list' %}" 
           class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Cancel
        </a>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-lg">
        <div class="text-center mb-6">
            <h1 class="text-3xl font-bold text-gray-800">GOODS REJECTION REPORT</h1>
            <p class="text-gray-600 whitespace-pre-line">{{ report_master_data.Address }}</p>
            <p class="text-gray-600">GRR No: <strong>{{ report_master_data.GRRNo }}</strong> | GIN No: <strong>{{ report_master_data.GINNO }}</strong> | GQN No: <strong>{{ report_master_data.GQNNO }}</strong></p>
            <p class="text-gray-600">Challan No: <strong>{{ report_master_data.ChallanNo }}</strong> | Challan Date: <strong>{{ report_master_data.ChallanDate }}</strong></p>
            <p class="text-gray-600">Supplier: <strong>{{ report_master_data.SupplierName }}</strong></p>
        </div>

        <h3 class="text-xl font-semibold mb-4">Rejected Items Details</h3>
        <div class="overflow-x-auto">
            <table class="min-w-full bg-white border border-gray-300">
                <thead>
                    <tr>
                        <th class="py-2 px-4 border-b border-gray-300 bg-gray-100 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">SN</th>
                        <th class="py-2 px-4 border-b border-gray-300 bg-gray-100 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Item Code</th>
                        <th class="py-2 px-4 border-b border-gray-300 bg-gray-100 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Description</th>
                        <th class="py-2 px-4 border-b border-gray-300 bg-gray-100 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">UOM</th>
                        <th class="py-2 px-4 border-b border-gray-300 bg-gray-100 text-right text-xs font-medium text-gray-600 uppercase tracking-wider">PO Qty</th>
                        <th class="py-2 px-4 border-b border-gray-300 bg-gray-100 text-right text-xs font-medium text-gray-600 uppercase tracking-wider">Inward Qty</th>
                        <th class="py-2 px-4 border-b border-gray-300 bg-gray-100 text-right text-xs font-medium text-gray-600 uppercase tracking-wider">Received Qty</th>
                        <th class="py-2 px-4 border-b border-gray-300 bg-gray-100 text-right text-xs font-medium text-gray-600 uppercase tracking-wider">Accepted Qty</th>
                        <th class="py-2 px-4 border-b border-gray-300 bg-gray-100 text-right text-xs font-medium text-gray-600 uppercase tracking-wider">Rejected Qty</th>
                        <th class="py-2 px-4 border-b border-gray-300 bg-gray-100 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Rejection Reason</th>
                        <th class="py-2 px-4 border-b border-gray-300 bg-gray-100 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Remarks</th>
                    </tr>
                </thead>
                <tbody>
                    {% for detail in report_details %}
                    <tr>
                        <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ detail.ItemCode }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ detail.PurchDesc }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ detail.UOMPurch }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ detail.POQty|floatformat:3 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ detail.InvQty|floatformat:3 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ detail.RecedQty|floatformat:3 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ detail.AcceptedQty|floatformat:3 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ detail.RejectedQty|floatformat:3 }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ detail.RejReason }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ detail.Remarks }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="11" class="py-4 px-4 text-center text-gray-500">No rejected items found for this record.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // No specific HTMX or Alpine.js for this static report view,
    // but can be added if interactive elements are desired.
    // For printing, typical browser print dialog (Ctrl+P / Cmd+P) is used.
</script>
{% endblock %}
```

#### 4.5 URLs

**Task:** Define URL patterns for the views, including the new report view.

**Filename:** `quality_control/urls.py`

```python
from django.urls import path
from .views import (
    GoodsRejectionMasterListView, GoodsRejectionMasterCreateView, 
    GoodsRejectionMasterUpdateView, GoodsRejectionMasterDeleteView,
    GoodsRejectionMasterTablePartialView, GoodsRejectionReportView
)

urlpatterns = [
    # URLs for general CRUD operations on GoodsRejectionMaster
    path('goods-rejections/', GoodsRejectionMasterListView.as_view(), name='goodsrejectionmaster_list'),
    path('goods-rejections/add/', GoodsRejectionMasterCreateView.as_view(), name='goodsrejectionmaster_add'),
    path('goods-rejections/edit/<int:pk>/', GoodsRejectionMasterUpdateView.as_view(), name='goodsrejectionmaster_edit'),
    path('goods-rejections/delete/<int:pk>/', GoodsRejectionMasterDeleteView.as_view(), name='goodsrejectionmaster_delete'),
    path('goods-rejections/table/', GoodsRejectionMasterTablePartialView.as_view(), name='goodsrejectionmaster_table'),

    # URL for the specific Goods Rejection Print Details Report
    # Note: 'id' in query string in ASP.NET is now handled by get_object in view, 
    # but for consistent URL routing, we expect a PK in the path if navigating directly
    # or handle the ?Id= parameter from the previous page.
    path('goods-rejections/report/', GoodsRejectionReportView.as_view(), name='goodsrejection_grn_report'),
    # You might also have a URL like this if `id` is always passed as a path parameter:
    # path('goods-rejections/report/<int:id>/', GoodsRejectionReportView.as_view(), name='goodsrejection_grn_report'),
]

```

#### 4.6 Tests

**Task:** Write comprehensive unit tests for models and integration tests for views to ensure functionality and coverage.

**Filename:** `quality_control/tests.py`

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from .models import (
    GoodsRejectionMaster, GoodsRejectionDetail, RejectionReason, 
    UnitMaster, ItemMaster, SupplierMaster, MaterialReceivedMaster,
    MaterialReceivedDetail, InwardMaster, InwardDetail, POMaster, PODetail,
    PRMaster, PRDetail, SPRMaster, SPRDetail
)
from datetime import datetime

class GoodsRejectionModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for all related models
        cls.comp_id = 1
        cls.fin_year_id = 2023

        # Lookup data
        cls.unit_pcs = UnitMaster.objects.create(id=1, symbol='PCS')
        cls.item1 = ItemMaster.objects.create(id=101, item_code='ITEM001', manf_desc='Test Item 1', uom_basic=cls.unit_pcs, comp_id=cls.comp_id)
        cls.reason1 = RejectionReason.objects.create(id=1, symbol='Damaged')
        cls.supplier1 = SupplierMaster.objects.create(supplier_id=1, supplier_name='Supplier A', comp_id=cls.comp_id)

        # PO-related data
        cls.po_master = POMaster.objects.create(id=201, pono='PO001', prspr_flag='0', fin_year_id=cls.fin_year_id, supplier=cls.supplier1, comp_id=cls.comp_id)
        cls.pr_master = PRMaster.objects.create(id=301, prno='PR001', wono='WO001', comp_id=cls.comp_id)
        cls.pr_detail = PRDetail.objects.create(id=302, m_id=cls.pr_master, prno='PR001', item=cls.item1, ahid=1)
        cls.po_detail = PODetail.objects.create(id=202, m_id=cls.po_master, pono='PO001', prno='PR001', qty=100.000, pr_id=cls.pr_detail.id)

        # Inward/Material Received data
        cls.inward_master = InwardMaster.objects.create(id=401, pono='PO001', challan_no='CH001', challan_date=datetime(2023, 1, 10), comp_id=cls.comp_id)
        cls.inward_detail = InwardDetail.objects.create(id=402, gin_id=cls.inward_master, po_id=cls.po_detail.id, received_qty=90.000)
        cls.mr_master = MaterialReceivedMaster.objects.create(id=501, gin_no=cls.inward_master.challan_no, comp_id=cls.comp_id) # GINNo from ChallanNo
        cls.mr_detail = MaterialReceivedDetail.objects.create(id=502, m_id=cls.mr_master, received_qty=90.000, po_id=cls.po_detail.id)

        # Goods Rejection data
        cls.grn_master = GoodsRejectionMaster.objects.create(
            id=1, grr_no='GRR001', gqn_no='GQN001', sys_date=datetime.now(), 
            fin_year_id=cls.fin_year_id, comp_id=cls.comp_id
        )
        cls.grn_detail_rejected = GoodsRejectionDetail.objects.create(
            id=10, m_id=cls.grn_master, accepted_qty=50.000, rejected_qty=40.000, 
            rejection_reason=cls.reason1, remarks='Damaged in transit',
            grr_id=cls.mr_master.id, dgrr_id=cls.mr_detail.id
        )
        cls.grn_detail_accepted = GoodsRejectionDetail.objects.create(
            id=11, m_id=cls.grn_master, accepted_qty=10.000, rejected_qty=0.000, 
            rejection_reason=None, remarks='Accepted item',
            grr_id=cls.mr_master.id, dgrr_id=cls.mr_detail.id
        )

    def test_goods_rejection_master_creation(self):
        obj = GoodsRejectionMaster.objects.get(id=self.grn_master.id)
        self.assertEqual(obj.grr_no, 'GRR001')
        self.assertEqual(obj.gqn_no, 'GQN001')
        self.assertEqual(obj.comp_id, self.comp_id)
        self.assertEqual(str(obj), f"GRR No: {self.grn_master.grr_no}")

    def test_goods_rejection_detail_creation(self):
        obj = GoodsRejectionDetail.objects.get(id=self.grn_detail_rejected.id)
        self.assertEqual(obj.m_id, self.grn_master)
        self.assertEqual(obj.rejected_qty, 40.000)
        self.assertEqual(obj.rejection_reason, self.reason1)
        self.assertEqual(str(obj), f"Detail {obj.id} for GRR {self.grn_master.id}")

    def test_item_master_get_item_code_part_no(self):
        item_code = ItemMaster.get_item_code_part_no(self.comp_id, self.item1.id)
        self.assertEqual(item_code, 'ITEM001')
        item_code_not_found = ItemMaster.get_item_code_part_no(self.comp_id, 9999)
        self.assertEqual(item_code_not_found, 'N/A')

    def test_get_grn_report_data_method(self):
        report_data = self.grn_master.get_grn_report_data(self.comp_id, self.grn_master.id)
        
        # Test master data
        self.assertIn('master_data', report_data)
        self.assertEqual(report_data['master_data']['GRRNo'], 'GRR001')
        self.assertEqual(report_data['master_data']['GQNNO'], 'GQN001')
        self.assertEqual(report_data['master_data']['SupplierName'], 'Supplier A')
        self.assertEqual(report_data['master_data']['ChallanNo'], 'CH001')
        self.assertEqual(report_data['master_data']['GINNO'], 'CH001') # GINNo is derived from ChallanNo in this mock setup
        self.assertIn('Address', report_data['master_data'])
        
        # Test details
        self.assertIn('details', report_data)
        self.assertEqual(len(report_data['details']), 1) # Only rejected items
        detail = report_data['details'][0]
        
        self.assertEqual(detail['Id'], self.grn_detail_rejected.id)
        self.assertEqual(detail['ItemCode'], 'ITEM001')
        self.assertEqual(detail['PurchDesc'], 'Test Item 1')
        self.assertEqual(detail['UOMPurch'], 'PCS')
        self.assertEqual(detail['POQty'], 100.000)
        self.assertEqual(detail['InvQty'], 90.000)
        self.assertEqual(detail['RecedQty'], 90.000)
        self.assertEqual(detail['AcceptedQty'], 50.000)
        self.assertEqual(detail['RejectedQty'], 40.000)
        self.assertEqual(detail['RejReason'], 'Damaged')
        self.assertEqual(detail['Remarks'], 'Damaged in transit')

    def test_get_grn_report_data_no_rejected_items(self):
        # Create a master record with no rejected details
        grn_master_no_rej = GoodsRejectionMaster.objects.create(
            id=2, grr_no='GRR002', gqn_no='GQN002', sys_date=datetime.now(), 
            fin_year_id=self.fin_year_id, comp_id=self.comp_id
        )
        GoodsRejectionDetail.objects.create(
            id=12, m_id=grn_master_no_rej, accepted_qty=100.000, rejected_qty=0.000, 
            rejection_reason=None, remarks='All accepted',
            grr_id=self.mr_master.id, dgrr_id=self.mr_detail.id
        )
        
        report_data = grn_master_no_rej.get_grn_report_data(self.comp_id, grn_master_no_rej.id)
        self.assertEqual(len(report_data['details']), 0)


class GoodsRejectionViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Similar to model test, create data for views to interact with
        cls.comp_id = 1
        cls.fin_year_id = 2023

        # Create minimal test data for all related models
        cls.unit_pcs = UnitMaster.objects.create(id=1, symbol='PCS')
        cls.item1 = ItemMaster.objects.create(id=101, item_code='ITEM001', manf_desc='Test Item 1', uom_basic=cls.unit_pcs, comp_id=cls.comp_id)
        cls.reason1 = RejectionReason.objects.create(id=1, symbol='Damaged')
        cls.supplier1 = SupplierMaster.objects.create(supplier_id=1, supplier_name='Supplier A', comp_id=cls.comp_id)

        cls.po_master = POMaster.objects.create(id=201, pono='PO001', prspr_flag='0', fin_year_id=cls.fin_year_id, supplier=cls.supplier1, comp_id=cls.comp_id)
        cls.pr_master = PRMaster.objects.create(id=301, prno='PR001', wono='WO001', comp_id=cls.comp_id)
        cls.pr_detail = PRDetail.objects.create(id=302, m_id=cls.pr_master, prno='PR001', item=cls.item1, ahid=1)
        cls.po_detail = PODetail.objects.create(id=202, m_id=cls.po_master, pono='PO001', prno='PR001', qty=100.000, pr_id=cls.pr_detail.id)

        cls.inward_master = InwardMaster.objects.create(id=401, pono='PO001', challan_no='CH001', challan_date=datetime(2023, 1, 10), comp_id=cls.comp_id)
        cls.inward_detail = InwardDetail.objects.create(id=402, gin_id=cls.inward_master, po_id=cls.po_detail.id, received_qty=90.000)
        cls.mr_master = MaterialReceivedMaster.objects.create(id=501, gin_no=cls.inward_master.challan_no, comp_id=cls.comp_id)
        cls.mr_detail = MaterialReceivedDetail.objects.create(id=502, m_id=cls.mr_master, received_qty=90.000, po_id=cls.po_detail.id)

        cls.grn_master = GoodsRejectionMaster.objects.create(
            id=1, grr_no='GRR001', gqn_no='GQN001', sys_date=datetime.now(), 
            fin_year_id=cls.fin_year_id, comp_id=cls.comp_id
        )
        cls.grn_detail_rejected = GoodsRejectionDetail.objects.create(
            id=10, m_id=cls.grn_master, accepted_qty=50.000, rejected_qty=40.000, 
            rejection_reason=cls.reason1, remarks='Damaged in transit',
            grr_id=cls.mr_master.id, dgrr_id=cls.mr_detail.id
        )

    def setUp(self):
        self.client = Client()
        # Set session variables for testing
        session = self.client.session
        session['compid'] = self.comp_id
        session['finyear'] = self.fin_year_id
        session['username'] = 'testuser'
        session.save()
    
    # --- CRUD View Tests ---
    def test_list_view(self):
        response = self.client.get(reverse('goodsrejectionmaster_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/goodsrejectionmaster/list.html')
        self.assertTrue('goodsrejectionmasters' in response.context)
        self.assertContains(response, 'GRR001') # Check if our test data is displayed

    def test_create_view_get(self):
        response = self.client.get(reverse('goodsrejectionmaster_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/goodsrejectionmaster/form.html')
        self.assertTrue('form' in response.context)
        
    def test_create_view_post(self):
        initial_count = GoodsRejectionMaster.objects.count()
        data = {
            'grr_no': 'GRR002',
            'gqn_no': 'GQN002',
            'sys_date': '2024-01-01',
            'fin_year_id': 2024,
            'comp_id': self.comp_id,
        }
        response = self.client.post(reverse('goodsrejectionmaster_add'), data)
        self.assertEqual(response.status_code, 302) # Redirect after successful creation
        self.assertEqual(GoodsRejectionMaster.objects.count(), initial_count + 1)
        self.assertTrue(GoodsRejectionMaster.objects.filter(grr_no='GRR002').exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Goods Rejection Master added successfully.')
        
    def test_update_view_get(self):
        obj = self.grn_master
        response = self.client.get(reverse('goodsrejectionmaster_edit', args=[obj.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/goodsrejectionmaster/form.html')
        self.assertEqual(response.context['form'].instance, obj)

    def test_update_view_post(self):
        obj = self.grn_master
        data = {
            'grr_no': 'GRR001_UPDATED',
            'gqn_no': obj.gqn_no,
            'sys_date': obj.sys_date.strftime('%Y-%m-%d'),
            'fin_year_id': obj.fin_year_id,
            'comp_id': obj.comp_id,
        }
        response = self.client.post(reverse('goodsrejectionmaster_edit', args=[obj.id]), data)
        self.assertEqual(response.status_code, 302)
        obj.refresh_from_db()
        self.assertEqual(obj.grr_no, 'GRR001_UPDATED')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Goods Rejection Master updated successfully.')

    def test_delete_view_get(self):
        obj = self.grn_master
        response = self.client.get(reverse('goodsrejectionmaster_delete', args=[obj.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/goodsrejectionmaster/confirm_delete.html')
        self.assertEqual(response.context['object'], obj)

    def test_delete_view_post(self):
        obj_to_delete = GoodsRejectionMaster.objects.create(
            id=99, grr_no='GRR_DEL', gqn_no='GQN_DEL', sys_date=datetime.now(), 
            fin_year_id=self.fin_year_id, comp_id=self.comp_id
        )
        initial_count = GoodsRejectionMaster.objects.count()
        response = self.client.post(reverse('goodsrejectionmaster_delete', args=[obj_to_delete.id]))
        self.assertEqual(response.status_code, 302)
        self.assertEqual(GoodsRejectionMaster.objects.count(), initial_count - 1)
        self.assertFalse(GoodsRejectionMaster.objects.filter(id=obj_to_delete.id).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Goods Rejection Master deleted successfully.')
        
    # --- HTMX Interaction Tests ---
    def test_create_view_post_htmx(self):
        initial_count = GoodsRejectionMaster.objects.count()
        data = {
            'grr_no': 'HTMX_GRR',
            'gqn_no': 'HTMX_GQN',
            'sys_date': '2024-02-01',
            'fin_year_id': 2024,
            'comp_id': self.comp_id,
        }
        response = self.client.post(reverse('goodsrejectionmaster_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX No Content response
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshGoodsRejectionMasterList')
        self.assertEqual(GoodsRejectionMaster.objects.count(), initial_count + 1)

    def test_delete_view_post_htmx(self):
        obj_htmx_delete = GoodsRejectionMaster.objects.create(
            id=98, grr_no='GRR_HTMX_DEL', gqn_no='GQN_HTMX_DEL', sys_date=datetime.now(), 
            fin_year_id=self.fin_year_id, comp_id=self.comp_id
        )
        initial_count = GoodsRejectionMaster.objects.count()
        response = self.client.post(reverse('goodsrejectionmaster_delete', args=[obj_htmx_delete.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshGoodsRejectionMasterList')
        self.assertEqual(GoodsRejectionMaster.objects.count(), initial_count - 1)

    # --- Report View Test ---
    def test_grn_report_view(self):
        url = reverse('goodsrejection_grn_report') + f'?Id={self.grn_master.id}'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/goodsrejectionmaster/grn_report.html')
        self.assertContains(response, 'GRR001')
        self.assertContains(response, 'GQN001')
        self.assertContains(response, 'ITEM001')
        self.assertContains(response, 'Damaged in transit')
        self.assertContains(response, '40.000') # Rejected quantity
        self.assertContains(response, 'Supplier A') # Supplier Name

    def test_grn_report_view_no_id_in_query_string(self):
        # Test direct access using PK in URL instead of query string (if configured)
        # Note: Current URL setup for report does not take PK directly in path.
        # It relies on `?Id=` as per ASP.NET original.
        # If the URL was `path('goods-rejections/report/<int:id>/', ...)` this test would change.
        url = reverse('goodsrejection_grn_report') # No query param
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404) # Because get_object_or_404 expects 'id' parameter from query string or URL kwargs.

    def test_grn_report_view_invalid_id(self):
        url = reverse('goodsrejection_grn_report') + '?Id=99999' # Non-existent ID
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404) # Not Found
```

### Step 5: HTMX and Alpine.js Integration

The provided Django code structure already incorporates HTMX for dynamic content loading, form submissions, and list refreshes. Alpine.js is assumed to be initialized via `base.html` and ready for use if more complex UI state management is needed (e.g., managing modal visibility, dynamic form fields, etc.).

*   **HTMX for CRUD Modals:** All Add, Edit, and Delete operations for `GoodsRejectionMaster` are handled via HTMX, loading forms and confirmation dialogs into a modal without full page reloads. The `HX-Trigger` headers ensure the main list view is refreshed after successful operations.
*   **DataTables Integration:** The `_goodsrejectionmaster_table.html` partial explicitly includes the DataTables initialization script, providing client-side searching, sorting, and pagination for the list of Goods Rejection records.
*   **Report View:** The `grn_report.html` page is a standard Django template rendering the aggregated report data. It can be navigated to via a simple link, allowing for browser-level print functionality. While it doesn't heavily use HTMX for dynamic content *within* the report itself (as it's a static printout), the entire list of reports could be loaded/refreshed via HTMX. The "Cancel" button is a simple link.

---

### Final Notes

*   **Placeholder Replacement:** Remember to replace placeholder company address details in `GoodsRejectionMaster.get_grn_report_data` with actual company information, ideally pulled from a `CompanyProfile` or `Settings` model.
*   **Database Synchronization:** After defining these models, ensure you run `python manage.py inspectdb` if you are connecting to an existing database to verify the `db_table` and column mappings, then `makemigrations` and `migrate` to set up Django's ORM (even with `managed = False`, Django needs to know about your models).
*   **Error Handling:** The `get_grn_report_data` method includes basic `try-except` for `DoesNotExist`. More robust error handling and logging should be integrated into a production system.
*   **Authentication & Authorization:** The ASP.NET code uses `Session["username"]`, `Session["compid"]`, `Session["finyear"]`. Django's built-in authentication system (`request.user`, `request.session`) should be used for securing views and access to company-specific data. Middleware might be necessary to inject `compid` and `finyear` into the session for every request, or this logic can be moved to the `get_context_data` method or a custom mixin.
*   **Scalability for Complex Reports:** For extremely large reports or complex Crystal Report features (e.g., charts, nested groups), consider integrating a dedicated PDF generation library like WeasyPrint or ReportLab in Django, or a reporting service, if direct HTML rendering becomes insufficient for printing requirements. This plan focuses on replicating the data display in a web-friendly, printable HTML format.