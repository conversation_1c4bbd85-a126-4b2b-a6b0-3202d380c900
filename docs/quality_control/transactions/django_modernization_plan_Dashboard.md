## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Analysis of Provided ASP.NET Code:

The provided ASP.NET code snippets (`Dashboard.aspx` and `Dashboard.aspx.cs`) indicate a basic page setup within a "Quality Control" module. However, **the code is largely empty**, containing only content placeholders, a master page reference, and an empty `Page_Load` method in the code-behind. This means there are no explicit database interactions, UI controls, or business logic defined within these specific files that could be directly extracted.

To provide a concrete and runnable Django modernization plan that demonstrates our approach, we will proceed by **assuming a common scenario for a "Quality Control Dashboard"**: managing a list of "Quality Control Items." This allows us to illustrate the full migration process from an empty template to a fully functional Django CRUD application, adhering to all the specified guidelines.

For the purpose of this demonstration, we will hypothesize the existence of a database table named `qc_items` and create a corresponding Django model `QCItem` to manage these items.

---

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Since the provided ASP.NET code is empty, we cannot directly extract schema details. For this modernization plan, we will **infer a hypothetical database table and columns** that would typically be managed within a "Quality Control" module.

**Inferred Table:** `qc_items`
**Inferred Columns:**
*   `id` (Primary Key, integer)
*   `name` (String, e.g., 'Inspection Report Q1')
*   `description` (Text, e.g., 'Details of the Q1 quality audit.')
*   `status` (String, e.g., 'Pending', 'Approved', 'Rejected')
*   `created_at` (Datetime)
*   `updated_at` (Datetime)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

As no specific backend functionality was present in the ASP.NET code, we will implement standard **CRUD (Create, Read, Update, Delete) operations** for our hypothetical `QCItem` entity. This is a foundational set of operations for most business applications and will demonstrate how to build a dynamic, data-driven interface using Django, HTMX, and DataTables.

*   **Create:** Ability to add new `QCItem` records.
*   **Read:** Display a list of all `QCItem` records.
*   **Update:** Ability to modify existing `QCItem` records.
*   **Delete:** Ability to remove `QCItem` records.
*   **Validation:** Basic field validation for input data.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

Given the empty ASP.NET code, we will **design modern UI components** suitable for a Django application using HTMX, Alpine.js, and DataTables, replacing the traditional ASP.NET GridView, TextBoxes, and Buttons.

*   **List View:** A table powered by **DataTables** to display `QCItem` records, enabling client-side searching, sorting, and pagination. This replaces the functionality of an ASP.NET GridView.
*   **Form for Create/Update:** A modal form, loaded via **HTMX**, containing input fields (e.g., `TextInput`, `Textarea`, `Select`) for `QCItem` attributes. This replaces ASP.NET TextBoxes, DropDownLists, etc., and their associated validation.
*   **Action Buttons:** Buttons for "Add New," "Edit," and "Delete" operations, all triggering **HTMX** requests to load modals or submit data without full page reloads. This replaces ASP.NET Buttons or LinkButtons.
*   **Modals:** All forms (create, update, delete confirmation) will appear in a modal overlay, managed by **Alpine.js** for UI state (showing/hiding the modal) and driven by HTMX for content loading.
*   **Client-Side Interactions:** Any "loadingNotifier.js" functionality would be integrated into HTMX's built-in indicators or simple Alpine.js state management.

---

## Step 4: Generate Django Code

We will create a new Django application, perhaps named `quality_control`, to house these components.

### 4.1 Models (`quality_control/models.py`)

Task: Create a Django model based on the database schema.

## Instructions:

We define the `QCItem` model, mapping to our hypothetical `qc_items` table. It includes methods for basic business logic related to status changes, adhering to the "fat model" principle.

```python
from django.db import models
from django.utils import timezone

class QCItem(models.Model):
    # Primary key is automatically added by Django unless pk=True is specified
    name = models.CharField(
        max_length=255, 
        verbose_name="Item Name",
        db_column='name' # Explicitly map to database column
    )
    description = models.TextField(
        blank=True, 
        null=True, 
        verbose_name="Description",
        db_column='description'
    )
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('APPROVED', 'Approved'),
        ('REJECTED', 'Rejected'),
        ('IN_REVIEW', 'In Review'),
    ]
    status = models.CharField(
        max_length=50, 
        choices=STATUS_CHOICES, 
        default='PENDING', 
        verbose_name="Status",
        db_column='status'
    )
    created_at = models.DateTimeField(
        auto_now_add=True, 
        verbose_name="Created At",
        db_column='created_at'
    )
    updated_at = models.DateTimeField(
        auto_now=True, 
        verbose_name="Last Updated",
        db_column='updated_at'
    )

    class Meta:
        managed = False  # Set to False if the table already exists and is not managed by Django migrations
        db_table = 'qc_items' # Map to the existing database table
        verbose_name = 'Quality Control Item'
        verbose_name_plural = 'Quality Control Items'
        ordering = ['-created_at'] # Default ordering for list views

    def __str__(self):
        return f"{self.name} ({self.status})"
        
    # Business logic methods (Fat Model)
    def mark_as_approved(self):
        if self.status in ['PENDING', 'IN_REVIEW']:
            self.status = 'APPROVED'
            self.save()
            return True
        return False

    def mark_as_rejected(self, reason=None):
        if self.status in ['PENDING', 'IN_REVIEW']:
            self.status = 'REJECTED'
            # In a real scenario, you might have a 'rejection_reason' field
            self.save()
            return True
        return False

    def is_editable(self):
        # Example business rule: item is editable if not approved or rejected
        return self.status not in ['APPROVED', 'REJECTED']

```

### 4.2 Forms (`quality_control/forms.py`)

Task: Define a Django form for user input.

## Instructions:

Create a `ModelForm` for `QCItem` to handle user input. We'll add widgets with basic Tailwind CSS classes for styling, making them ready for immediate use.

```python
from django import forms
from .models import QCItem

class QCItemForm(forms.ModelForm):
    class Meta:
        model = QCItem
        fields = ['name', 'description', 'status'] # Exclude auto_now_add/auto_now fields from form
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter item name'
            }),
            'description': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'rows': 3,
                'placeholder': 'Provide a description (optional)'
            }),
            'status': forms.Select(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
        }
        
    def clean_name(self):
        name = self.cleaned_data.get('name')
        if not name:
            raise forms.ValidationError("Item Name cannot be empty.")
        # Example custom validation: ensure name is unique (for new items or if name changed)
        if self.instance.pk: # If it's an update
            if QCItem.objects.filter(name=name).exclude(pk=self.instance.pk).exists():
                raise forms.ValidationError("An item with this name already exists.")
        else: # If it's a creation
            if QCItem.objects.filter(name=name).exists():
                raise forms.ValidationError("An item with this name already exists.")
        return name
```

### 4.3 Views (`quality_control/views.py`)

Task: Implement CRUD operations using CBVs.

## Instructions:

We define `QCItemListView`, `QCItemCreateView`, `QCItemUpdateView`, `QCItemDeleteView`, and importantly, a `QCItemTablePartialView` to serve the DataTables content via HTMX. Views are kept thin, delegating complex logic to the model or Django's `ModelForm`.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import QCItem
from .forms import QCItemForm

class QCItemListView(ListView):
    model = QCItem
    template_name = 'quality_control/qcitem/list.html'
    context_object_name = 'qcitems' # Renamed for clarity in templates

class QCItemCreateView(CreateView):
    model = QCItem
    form_class = QCItemForm
    template_name = 'quality_control/qcitem/form.html'
    success_url = reverse_lazy('qcitem_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Quality Control Item added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, HTMX expects this for full swap
                headers={
                    'HX-Trigger': 'refreshQCItemList' # Custom event to trigger table refresh
                }
            )
        return response

class QCItemUpdateView(UpdateView):
    model = QCItem
    form_class = QCItemForm
    template_name = 'quality_control/qcitem/form.html'
    success_url = reverse_lazy('qcitem_list')

    def form_valid(self, form):
        # Example of calling model business logic before saving
        # if form.instance.status == 'APPROVED' and not form.instance.mark_as_approved():
        #    messages.error(self.request, 'Could not approve item.')
        #    return self.form_invalid(form) # Re-render form with error
        
        response = super().form_valid(form)
        messages.success(self.request, 'Quality Control Item updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshQCItemList'
                }
            )
        return response

class QCItemDeleteView(DeleteView):
    model = QCItem
    template_name = 'quality_control/qcitem/confirm_delete.html'
    success_url = reverse_lazy('qcitem_list')

    def delete(self, request, *args, **kwargs):
        # Example of calling model business logic before deleting
        # obj = self.get_object()
        # if not obj.is_editable(): # Prevent deletion if status is locked
        #     messages.error(self.request, 'Item cannot be deleted due to its status.')
        #     if request.headers.get('HX-Request'):
        #         # Return a 200 OK with some content or error message for HTMX to display
        #         return HttpResponse(f"<div class='text-red-500'>Error: Item cannot be deleted.</div>", status=200)
        #     return HttpResponseRedirect(self.get_success_url())
            
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Quality Control Item deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshQCItemList'
                }
            )
        return response

# This view renders only the table partial for HTMX requests
class QCItemTablePartialView(ListView):
    model = QCItem
    template_name = 'quality_control/qcitem/_qcitem_table.html'
    context_object_name = 'qcitems' # Renamed for clarity in templates
```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

These templates will be placed in `quality_control/templates/quality_control/qcitem/`. They demonstrate DRY principles by using partials and extend `core/base.html` (which is assumed to exist).

#### `quality_control/templates/quality_control/qcitem/list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Quality Control Items</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'qcitem_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New QC Item
        </button>
    </div>
    
    <div id="qcitemTable-container"
         hx-trigger="load, refreshQCItemList from:body"
         hx-get="{% url 'qcitem_table' %}"
         hx-swap="innerHTML"
         class="bg-white p-6 rounded-lg shadow-md overflow-x-auto">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading quality control items...</p>
        </div>
    </div>
    
    <!-- Modal for forms and confirmations -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 sm:mx-auto relative">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js component initialization if needed for UI state management not handled by HTMX
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js needed for this basic CRUD, HTMX handles interaction and modal display logic.
        // The `_` (hyperscript) attribute on the modal div handles its visibility.
    });
</script>
{% endblock %}
```

#### `quality_control/templates/quality_control/qcitem/_qcitem_table.html` (Partial Template)

```html
<table id="qcitemTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Name</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created At</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in qcitems %}
        <tr>
            <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-3 px-6 whitespace-nowrap text-sm font-medium text-gray-900">{{ obj.name }}</td>
            <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-500">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                    {% if obj.status == 'APPROVED' %}bg-green-100 text-green-800
                    {% elif obj.status == 'REJECTED' %}bg-red-100 text-red-800
                    {% elif obj.status == 'PENDING' %}bg-yellow-100 text-yellow-800
                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                    {{ obj.get_status_display }}
                </span>
            </td>
            <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-500">{{ obj.created_at|date:"M d, Y H:i" }}</td>
            <td class="py-3 px-6 whitespace-nowrap text-sm font-medium">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 text-xs transition duration-300 ease-in-out"
                    hx-get="{% url 'qcitem_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-xs transition duration-300 ease-in-out"
                    hx-get="{% url 'qcitem_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="5" class="py-4 px-6 text-center text-gray-500">No quality control items found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Initialize DataTables only after the content is loaded
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#qcitemTable')) { // Prevent re-initialization
            $('#qcitemTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "dom": 'lfrtip', // Layout: Length changing input, Filtering input, Table, Information, Pagination
                "responsive": true, // Make table responsive
                "language": {
                    "search": "Search items:",
                    "lengthMenu": "Show _MENU_ entries"
                }
            });
        }
    });
</script>
```

#### `quality_control/templates/quality_control/qcitem/_qcitem_form.html` (Partial Template)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-4">{{ form.instance.pk|yesno:'Edit,Add' }} Quality Control Item</h3>
    <form hx-post="{{ request.path }}" hx-swap="none"
          hx-on::after-request="if(event.detail.successful) { remove .is-active from #modal; }"
          hx-on::htmx:validation:error="console.log('Validation Error');">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}
                        <span class="text-red-500">*</span>
                    {% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600 list-disc pl-5">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
            {% if form.non_field_errors %}
                <ul class="mt-1 text-sm text-red-600 list-disc pl-5">
                    {% for error in form.non_field_errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4 pt-4 border-t">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out">
                Save Item
            </button>
        </div>
    </form>
</div>
```

#### `quality_control/templates/quality_control/qcitem/_qcitem_confirm_delete.html` (Partial Template)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the Quality Control Item: <span class="font-bold text-red-600">{{ qcitem.name }}</span>?
        This action cannot be undone.
    </p>
    <form hx-post="{% url 'qcitem_delete' qcitem.pk %}" hx-swap="none"
          hx-on::after-request="if(event.detail.successful) { remove .is-active from #modal; }">
        {% csrf_token %}
        <div class="mt-8 flex items-center justify-end space-x-4 pt-4 border-t">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out">
                Delete Item
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs (`quality_control/urls.py`)

Task: Define URL patterns for the views.

## Instructions:

Create paths for all views, including the partial view for HTMX-driven table updates. This file would be included in the project's main `urls.py`.

```python
from django.urls import path
from .views import (
    QCItemListView, 
    QCItemCreateView, 
    QCItemUpdateView, 
    QCItemDeleteView,
    QCItemTablePartialView, # For HTMX partial table rendering
)

urlpatterns = [
    # Main list view (full page load, but table part loads via HTMX)
    path('qc-items/', QCItemListView.as_view(), name='qcitem_list'),
    
    # HTMX-only endpoint for refreshing the table content
    path('qc-items/table/', QCItemTablePartialView.as_view(), name='qcitem_table'),
    
    # CRUD operations, designed to be loaded into a modal via HTMX
    path('qc-items/add/', QCItemCreateView.as_view(), name='qcitem_add'),
    path('qc-items/edit/<int:pk>/', QCItemUpdateView.as_view(), name='qcitem_edit'),
    path('qc-items/delete/<int:pk>/', QCItemDeleteView.as_view(), name='qcitem_delete'),
]
```

### 4.6 Tests (`quality_control/tests.py`)

Task: Write tests for the model and views.

## Instructions:

Include comprehensive unit tests for model methods and integration tests for all views to ensure functionality and robustness. We aim for high test coverage, at least 80%.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from .models import QCItem
import json

class QCItemModelTest(TestCase):
    """
    Unit tests for the QCItem model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.item1 = QCItem.objects.create(
            name='First QC Check',
            description='Initial quality check for new product batch A.',
            status='PENDING'
        )
        cls.item2 = QCItem.objects.create(
            name='Approved Product Inspection',
            description='Final inspection for batch B.',
            status='APPROVED'
        )
  
    def test_qcitem_creation(self):
        """Test that a QCItem instance can be created and its attributes are correct."""
        obj = QCItem.objects.get(id=self.item1.id)
        self.assertEqual(obj.name, 'First QC Check')
        self.assertEqual(obj.description, 'Initial quality check for new product batch A.')
        self.assertEqual(obj.status, 'PENDING')
        self.assertIsNotNone(obj.created_at)
        self.assertIsNotNone(obj.updated_at)
        self.assertEqual(str(obj), 'First QC Check (PENDING)') # Test __str__ method

    def test_field_verbose_name(self):
        """Test verbose names of fields."""
        obj = QCItem.objects.get(id=self.item1.id)
        name_label = obj._meta.get_field('name').verbose_name
        self.assertEqual(name_label, 'Item Name')
        status_label = obj._meta.get_field('status').verbose_name
        self.assertEqual(status_label, 'Status')

    def test_mark_as_approved(self):
        """Test the mark_as_approved model method."""
        # Item that can be approved
        self.assertTrue(self.item1.mark_as_approved())
        self.item1.refresh_from_db()
        self.assertEqual(self.item1.status, 'APPROVED')

        # Item that cannot be approved (already approved)
        self.assertFalse(self.item2.mark_as_approved())
        self.item2.refresh_from_db()
        self.assertEqual(self.item2.status, 'APPROVED') # Should remain APPROVED

    def test_mark_as_rejected(self):
        """Test the mark_as_rejected model method."""
        item3 = QCItem.objects.create(
            name='Test Item For Rejection',
            status='PENDING'
        )
        self.assertTrue(item3.mark_as_rejected())
        item3.refresh_from_db()
        self.assertEqual(item3.status, 'REJECTED')

        # Item that cannot be rejected (already rejected or approved)
        self.assertFalse(self.item2.mark_as_rejected())
        self.item2.refresh_from_db()
        self.assertEqual(self.item2.status, 'APPROVED')
        
    def test_is_editable_method(self):
        """Test the is_editable model method."""
        self.assertTrue(self.item1.is_editable()) # PENDING is editable
        self.assertFalse(self.item2.is_editable()) # APPROVED is not editable
        rejected_item = QCItem.objects.create(name='Rejected Item', status='REJECTED')
        self.assertFalse(rejected_item.is_editable()) # REJECTED is not editable

class QCItemViewsTest(TestCase):
    """
    Integration tests for QCItem views.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.item1 = QCItem.objects.create(
            name='View Test Item 1',
            description='Description for view test item 1.',
            status='PENDING'
        )
        cls.item2 = QCItem.objects.create(
            name='View Test Item 2',
            description='Description for view test item 2.',
            status='APPROVED'
        )
    
    def setUp(self):
        # Set up a new client for each test method
        self.client = Client()
    
    def test_list_view(self):
        """Test the QCItem list view (main page)."""
        response = self.client.get(reverse('qcitem_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/qcitem/list.html')
        self.assertTrue('qcitems' in response.context) # Check if context contains our object list
        self.assertContains(response, self.item1.name)
        self.assertContains(response, self.item2.name)

    def test_table_partial_view(self):
        """Test the HTMX partial for the QCItem table."""
        response = self.client.get(reverse('qcitem_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/qcitem/_qcitem_table.html')
        self.assertTrue('qcitems' in response.context)
        self.assertContains(response, self.item1.name)
        self.assertContains(response, self.item2.name)
        self.assertContains(response, '<table id="qcitemTable"') # Ensure table structure is present

    def test_create_view_get(self):
        """Test GET request to the create form view."""
        response = self.client.get(reverse('qcitem_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/qcitem/form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Quality Control Item')

    def test_create_view_post_success(self):
        """Test POST request for creating a new QCItem."""
        initial_count = QCItem.objects.count()
        data = {
            'name': 'New Test QC Item',
            'description': 'Description for new item.',
            'status': 'PENDING',
        }
        response = self.client.post(reverse('qcitem_add'), data, **{'HTTP_HX_REQUEST': 'true'})
        self.assertEqual(response.status_code, 204) # HTMX expects 204 for successful full swap
        self.assertTrue(QCItem.objects.filter(name='New Test QC Item').exists())
        self.assertEqual(QCItem.objects.count(), initial_count + 1)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshQCItemList')

    def test_create_view_post_validation_error(self):
        """Test POST request with invalid data for creating a QCItem."""
        initial_count = QCItem.objects.count()
        data = {
            'name': '', # Empty name
            'description': 'Invalid item.',
            'status': 'PENDING',
        }
        response = self.client.post(reverse('qcitem_add'), data) # No HX-Request header to get full form
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertContains(response, 'Item Name cannot be empty.')
        self.assertEqual(QCItem.objects.count(), initial_count) # No item created

    def test_update_view_get(self):
        """Test GET request to the update form view."""
        response = self.client.get(reverse('qcitem_edit', args=[self.item1.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/qcitem/form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Edit Quality Control Item')
        self.assertContains(response, self.item1.name)

    def test_update_view_post_success(self):
        """Test POST request for updating an existing QCItem."""
        data = {
            'name': 'Updated QC Check Name',
            'description': self.item1.description,
            'status': 'APPROVED',
        }
        response = self.client.post(reverse('qcitem_edit', args=[self.item1.id]), data, **{'HTTP_HX_REQUEST': 'true'})
        self.assertEqual(response.status_code, 204)
        self.item1.refresh_from_db()
        self.assertEqual(self.item1.name, 'Updated QC Check Name')
        self.assertEqual(self.item1.status, 'APPROVED')
        self.assertEqual(response.headers['HX-Trigger'], 'refreshQCItemList')
        
    def test_update_view_post_duplicate_name_error(self):
        """Test POST request for updating with a duplicate name."""
        data = {
            'name': self.item2.name, # Attempt to change item1's name to item2's name
            'description': self.item1.description,
            'status': self.item1.status,
        }
        response = self.client.post(reverse('qcitem_edit', args=[self.item1.id]), data)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertContains(response, 'An item with this name already exists.')
        self.item1.refresh_from_db()
        self.assertNotEqual(self.item1.name, self.item2.name) # Ensure name was not updated

    def test_delete_view_get(self):
        """Test GET request to the delete confirmation view."""
        response = self.client.get(reverse('qcitem_delete', args=[self.item1.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/qcitem/confirm_delete.html')
        self.assertTrue('qcitem' in response.context)
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, self.item1.name)

    def test_delete_view_post_success(self):
        """Test POST request for deleting a QCItem."""
        # Create a new item to delete, so we don't interfere with setUpTestData items
        item_to_delete = QCItem.objects.create(
            name='Temporary Item to Delete',
            status='PENDING'
        )
        initial_count = QCItem.objects.count()
        response = self.client.post(reverse('qcitem_delete', args=[item_to_delete.id]), **{'HTTP_HX_REQUEST': 'true'})
        self.assertEqual(response.status_code, 204)
        self.assertFalse(QCItem.objects.filter(id=item_to_delete.id).exists())
        self.assertEqual(QCItem.objects.count(), initial_count - 1)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshQCItemList')

```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

The provided Django code already incorporates HTMX and Alpine.js principles:

*   **HTMX for dynamic updates:**
    *   The `list.html` uses `hx-get` to load the `_qcitem_table.html` partial, which displays the DataTables. This means the table is loaded dynamically.
    *   "Add New", "Edit", and "Delete" buttons use `hx-get` to load forms/confirmations into a modal (`#modalContent`) without full page reloads.
    *   Form submissions (`hx-post`) in `_qcitem_form.html` and `_qcitem_confirm_delete.html` trigger a `HX-Trigger: refreshQCItemList` header from the Django views. This event is then listened for by `qcitemTable-container` in `list.html` to reload only the table content, ensuring the list is always up-to-date after any CRUD operation.
    *   The `hx-swap="none"` on form submissions, combined with the `HX-Trigger` and `204 No Content` status, ensures HTMX silently processes the request and then triggers the list refresh, providing a seamless user experience.

*   **Alpine.js for UI state management:**
    *   The `_` (Hyperscript) attribute is used on the `#modal` div to control its `hidden` class. `on click add .is-active to #modal` shows the modal when action buttons are clicked. `on click if event.target.id == 'modal' remove .is-active from me` allows clicking outside the modal to close it. `on click remove .is-active from #modal` on Cancel/Save buttons provides a clean way to close the modal after interaction.
    *   This eliminates the need for large, custom JavaScript files, keeping the frontend extremely lightweight and declarative.

*   **DataTables for list views:**
    *   The `_qcitem_table.html` partial explicitly initializes DataTables using `$(document).ready(function() { $('#qcitemTable').DataTable(...); });`. This ensures client-side searching, sorting, and pagination are available for all list data. The `isDataTable` check prevents re-initialization on subsequent HTMX loads.

*   **DRY Templates:**
    *   The main `list.html` extends `core/base.html` and uses a partial `_qcitem_table.html` to load the actual table content.
    *   Forms (`_qcitem_form.html`) and delete confirmations (`_qcitem_confirm_delete.html`) are also partials, designed to be loaded into the modal. This avoids code duplication and keeps templates concise.

## Final Notes

This comprehensive plan demonstrates the modernization of an ASP.NET application to a Django-based solution, even starting from an empty ASP.NET code base. The key is to:

1.  **Systematically infer or define the core entities and operations** based on the module's likely purpose.
2.  **Apply Django best practices:** Fat Models, Thin Views, Class-Based Views, and proper URL routing.
3.  **Leverage modern frontend techniques:** HTMX for server-side templating and dynamic content, Alpine.js for minimal client-side interactivity and state, and DataTables for robust data presentation.
4.  **Prioritize automation:** The structure provided (models, forms, views, templates, URLs, tests) is designed to be generated by an AI-assisted tool. An actual conversion tool would perform the analysis (Steps 1-3) on a much larger codebase and generate these components automatically, drastically reducing manual development time and potential human errors.
5.  **Ensure comprehensive testing:** Automated tests for both backend logic (models) and frontend interactions (views) are crucial for verifying the correctness of the migration and ensuring future maintainability.

By following this approach, organizations can transition from legacy ASP.NET systems to modern, maintainable Django applications with significantly reduced effort and risk, focusing on business value rather than complex technical implementation details.