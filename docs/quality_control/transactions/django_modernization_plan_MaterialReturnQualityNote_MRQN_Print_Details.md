## ASP.NET to Django Conversion Script: Material Return Quality Note [MRQN] Print Details

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

This ASP.NET page is primarily a report viewer. It aggregates data from multiple tables and presents it via Crystal Reports. Therefore, the Django modernization plan will focus on a "read-only" detail view that generates the report data dynamically. Standard CRUD (Create, Update, Delete) forms and views are not applicable to *this specific page's functionality*.

### Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:** The C# code-behind performs several SQL queries to gather data for the report.

*   **Primary Data Source:** `tblQc_MaterialReturnQuality_Details` joined with `tblQc_MaterialReturnQuality_Master`.
*   **Related Data:**
    *   `tblInv_MaterialReturn_Details` joined with `tblInv_MaterialReturn_Master` (via `MRNId`).
    *   `tblDG_Item_Master` (via `ItemId`).
    *   `Unit_Master` (via `UOMBasic`).
    *   `tblHR_Departments` (via `DeptId`).
    *   `tblHR_OfficeStaff` (via `SessionId` from `tblQc_MaterialReturnQuality_Master`).
    *   Company address is derived from `CompId`.

**Inferred Tables and Key Columns:**

*   **`tblQc_MaterialReturnQuality_Master` (Django Model: `MaterialReturnQualityMaster`)**
    *   `Id` (PK)
    *   `MRNId`
    *   `MRQNNo`
    *   `SysDate` (Date of MRQN)
    *   `SessionId` (User who generated it)
    *   `CompId` (Company ID)

*   **`tblQc_MaterialReturnQuality_Details` (Django Model: `MaterialReturnQualityDetail`)**
    *   `Id` (PK)
    *   `MId` (FK to `MaterialReturnQualityMaster.Id`)
    *   `MRNId` (FK to `MaterialReturnDetail.Id`)
    *   `MRQNNo`
    *   `AcceptedQty`

*   **`tblInv_MaterialReturn_Master` (Django Model: `MaterialReturnMaster`)**
    *   `Id` (PK)
    *   `CompId`

*   **`tblInv_MaterialReturn_Details` (Django Model: `MaterialReturnDetail`)**
    *   `Id` (PK)
    *   `MId` (FK to `MaterialReturnMaster.Id`)
    *   `ItemId` (FK to `ItemMaster.Id`)
    *   `DeptId` (FK to `Department.Id`)
    *   `WONo`
    *   `RetQty`
    *   `Remarks`

*   **`tblDG_Item_Master` (Django Model: `ItemMaster`)**
    *   `Id` (PK)
    *   `ItemCode`
    *   `ManfDesc`
    *   `UOMBasic` (FK to `UnitMaster.Id`)
    *   `CompId`

*   **`Unit_Master` (Django Model: `UnitMaster`)**
    *   `Id` (PK)
    *   `Symbol` (UOM Symbol)

*   **`tblHR_Departments` (Django Model: `Department`)**
    *   `Id` (PK)
    *   `Symbol` (Department Symbol)

*   **`tblHR_OfficeStaff` (Django Model: `OfficeStaff`)**
    *   `EmpId` (PK)
    *   `Title`
    *   `EmployeeName`
    *   `CompId`

*   **Implicit `Company` Information (from `fun.CompAdd`)**
    *   Assumed to exist in a `tblCompany` table or similar, with `CompId`.

### Step 2: Identify Backend Functionality

**Analysis:** This page's sole function is to *read* and display a Material Return Quality Note report based on provided query string parameters (`MRNNo`, `MRQNNo`, `FYId`, `Id`, `Key`). There are no direct Create, Update, or Delete operations on this page. The "Cancel" button performs a redirect.

### Step 3: Infer UI Components

**Analysis:**
*   A header area for the report title.
*   A `CrystalReportViewer` displaying the report data, which will be translated into a tabular display with DataTables.
*   A "Cancel" button for navigation back.

### Step 4: Generate Django Code

We will create a Django application named `quality_control`.

#### 4.1 Models (`quality_control/models.py`)

This section defines the Django models corresponding to the database tables accessed by the ASP.NET application. We use `managed = False` and `db_table` to map them to existing tables. The "fat model" approach is applied by adding a class method `get_mrqn_report_data` to `MaterialReturnQualityMaster` that performs all the complex data retrieval logic.

```python
from django.db import models
from django.db.models import F, Case, When, Value, CharField
from django.utils import timezone
from datetime import datetime

# Helper function placeholder for company address (mimicking fun.CompAdd)
# In a real scenario, this would query a Company table or be part of a Company model.
def get_company_address(comp_id):
    """
    Simulates fetching company address based on CompId.
    In a real system, this would query a 'Company' model.
    """
    # Placeholder for actual company address logic
    if comp_id == 1:
        return "123 Main St, Anytown, USA"
    return "Unknown Company Address"

# Helper function placeholder for GetItemCode_PartNo (mimicking fun.GetItemCode_PartNo)
# This could be a static method on ItemMaster or a utility function.
def get_item_code_part_no(comp_id, item_id):
    """
    Simulates fetching derived item code/part number.
    Could involve complex logic or just fetching ItemMaster.ItemCode.
    """
    try:
        item = ItemMaster.objects.get(pk=item_id, CompId=comp_id)
        # Assuming fun.GetItemCode_PartNo might concatenate ItemCode with PartNo
        # For now, we'll just return ItemCode.
        return item.ItemCode
    except ItemMaster.DoesNotExist:
        return "N/A"

class MaterialReturnQualityMaster(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    MRNId = models.IntegerField(db_column='MRNId', null=True, blank=True)
    MRQNNo = models.CharField(db_column='MRQNNo', max_length=50, null=True, blank=True)
    SysDate = models.DateTimeField(db_column='SysDate', null=True, blank=True)
    SessionId = models.IntegerField(db_column='SessionId', null=True, blank=True) # Corresponds to EmpId in OfficeStaff
    CompId = models.IntegerField(db_column='CompId', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblQc_MaterialReturnQuality_Master'
        verbose_name = 'Material Return Quality Note Master'
        verbose_name_plural = 'Material Return Quality Note Masters'

    def __str__(self):
        return f"MRQN Master {self.MRQNNo}"

    @classmethod
    def get_mrqn_report_data(cls, master_id, comp_id, mrn_no, mrqn_no, fy_id):
        """
        Retrieves comprehensive data for the MRQN Print Details report.
        This method encapsulates all the complex SQL joins and lookups from the ASP.NET code-behind.
        """
        try:
            mrqn_master = cls.objects.select_related('officestaff').get(Id=master_id, CompId=comp_id)
        except cls.DoesNotExist:
            return None, [] # Return None for header, empty list for details

        # Prepare header data
        header_data = {
            'MRNNo': mrn_no, # From QueryString
            'MRQNNo': mrqn_no, # From QueryString
            'MRQNDate': mrqn_master.SysDate.strftime('%d/%m/%Y') if mrqn_master.SysDate else 'N/A',
            'GenBy': f"{mrqn_master.officestaff.Title}. {mrqn_master.officestaff.EmployeeName}" if mrqn_master.officestaff else 'N/A',
            'Address': get_company_address(comp_id), # Custom function for company address
        }

        # Retrieve and process detail data, mimicking nested queries
        # Using select_related for direct foreign keys and prefetch_related for reverse lookups if needed.
        # Here we'll do explicit joins as in the C# code, but using Django ORM.
        details_qs = MaterialReturnQualityDetail.objects.filter(
            MId=mrqn_master.Id,
            materialreturnqualitymaster__CompId=comp_id # Joining back to master to enforce CompId filter
        ).select_related(
            'materialreturndetail__itemmaster__unitmaster', # Join through MaterialReturnDetail to Item and Unit
            'materialreturndetail__department' # Join through MaterialReturnDetail to Department
        ).annotate(
            # Using F() expressions to reference fields across relationships
            # Mimicking the C# column selections and derivations
            ItemCode=Case(
                When(materialreturndetail__itemmaster__isnull=False, then=F('materialreturndetail__itemmaster__ItemCode')),
                default=Value('N/A'),
                output_field=CharField()
            ),
            Description=Case(
                When(materialreturndetail__itemmaster__isnull=False, then=F('materialreturndetail__itemmaster__ManfDesc')),
                default=Value('N/A'),
                output_field=CharField()
            ),
            UOM=Case(
                When(materialreturndetail__itemmaster__unitmaster__isnull=False, then=F('materialreturndetail__itemmaster__unitmaster__Symbol')),
                default=Value('N/A'),
                output_field=CharField()
            ),
            DeptSymbol=Case(
                When(materialreturndetail__DeptId__isnull=False, then=F('materialreturndetail__department__Symbol')),
                default=Value('NA'), # Matches 'NA' in C# if DeptId == 0
                output_field=CharField()
            ),
            WONo=F('materialreturndetail__WONo'),
            RetQty=F('materialreturndetail__RetQty'),
            Remarks=F('materialreturndetail__Remarks'),
            # The C# code uses DS["AcceptedQty"] from tblQc_MaterialReturnQuality_Details
            # and dsrs["RetQty"] from tblInv_MaterialReturn_Details.
            # So, our AcceptedQty is from this model, RetQty from the joined one.
            MRQD_AcceptedQty=F('AcceptedQty') # Renaming to avoid conflict if 'AcceptedQty' existed in MaterialReturnDetail
        )

        details_list = []
        for detail in details_qs:
            # Recreate the structure similar to the C# DataTable
            item_code_display = get_item_code_part_no(comp_id, detail.materialreturndetail.ItemId) if detail.materialreturndetail and detail.materialreturndetail.ItemId else 'N/A'

            details_list.append({
                'Id': detail.Id,
                'ItemCode': item_code_display, # Derived from GetItemCode_PartNo
                'Description': detail.Description,
                'UOM': detail.UOM,
                'DeptOrWONo': detail.DeptSymbol if detail.materialreturndetail.DeptId != 0 else detail.WONo, # Mimics C# logic
                'AcceptedQty': f"{detail.MRQD_AcceptedQty:.3f}", # Format to 3 decimal places
                'RetQty': f"{detail.RetQty:.3f}", # Format to 3 decimal places
                'Remarks': detail.Remarks,
                'CompId': comp_id,
            })

        return header_data, details_list


class MaterialReturnQualityDetail(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    MId = models.ForeignKey(MaterialReturnQualityMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    MRNId = models.ForeignKey('MaterialReturnDetail', on_delete=models.DO_NOTHING, db_column='MRNId')
    MRQNNo = models.CharField(db_column='MRQNNo', max_length=50, null=True, blank=True)
    AcceptedQty = models.DecimalField(db_column='AcceptedQty', max_digits=18, decimal_places=3, null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblQc_MaterialReturnQuality_Details'
        verbose_name = 'Material Return Quality Note Detail'
        verbose_name_plural = 'Material Return Quality Note Details'

    def __str__(self):
        return f"MRQN Detail {self.Id}"

class MaterialReturnMaster(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    CompId = models.IntegerField(db_column='CompId', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialReturn_Master'
        verbose_name = 'Material Return Master'
        verbose_name_plural = 'Material Return Masters'

    def __str__(self):
        return f"MR Master {self.Id}"

class MaterialReturnDetail(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    MId = models.ForeignKey(MaterialReturnMaster, on_delete=models.DO_NOTHING, db_column='MId')
    ItemId = models.ForeignKey('ItemMaster', on_delete=models.DO_NOTHING, db_column='ItemId')
    DeptId = models.ForeignKey('Department', on_delete=models.DO_NOTHING, db_column='DeptId', null=True, blank=True)
    WONo = models.CharField(db_column='WONo', max_length=50, null=True, blank=True)
    RetQty = models.DecimalField(db_column='RetQty', max_digits=18, decimal_places=3, null=True, blank=True)
    Remarks = models.CharField(db_column='Remarks', max_length=255, null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialReturn_Details'
        verbose_name = 'Material Return Detail'
        verbose_name_plural = 'Material Return Details'

    def __str__(self):
        return f"MR Detail {self.Id}"

class ItemMaster(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    ItemCode = models.CharField(db_column='ItemCode', max_length=50, null=True, blank=True)
    ManfDesc = models.CharField(db_column='ManfDesc', max_length=255, null=True, blank=True)
    UOMBasic = models.ForeignKey('UnitMaster', on_delete=models.DO_NOTHING, db_column='UOMBasic', null=True, blank=True)
    CompId = models.IntegerField(db_column='CompId', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.ItemCode or f"Item {self.Id}"

class UnitMaster(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    Symbol = models.CharField(db_column='Symbol', max_length=10, null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit Master'
        verbose_name_plural = 'Unit Masters'

    def __str__(self):
        return self.Symbol or f"Unit {self.Id}"

class Department(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    Symbol = models.CharField(db_column='Symbol', max_length=50, null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblHR_Departments'
        verbose_name = 'Department'
        verbose_name_plural = 'Departments'

    def __str__(self):
        return self.Symbol or f"Department {self.Id}"

class OfficeStaff(models.Model):
    EmpId = models.IntegerField(db_column='EmpId', primary_key=True)
    Title = models.CharField(db_column='Title', max_length=10, null=True, blank=True)
    EmployeeName = models.CharField(db_column='EmployeeName', max_length=100, null=True, blank=True)
    CompId = models.IntegerField(db_column='CompId', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return self.EmployeeName or f"Staff {self.EmpId}"

```

#### 4.2 Forms

**Analysis:** This page is for displaying a report, not for data entry. Therefore, no Django forms (`forms.py`) are required for this specific conversion.

#### 4.3 Views (`quality_control/views.py`)

This view will retrieve the report data by calling the `get_mrqn_report_data` method on the `MaterialReturnQualityMaster` model. It acts as a specialized detail/report view, not a standard CRUD view.

```python
from django.views.generic import TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404
from .models import MaterialReturnQualityMaster # Import the model that contains the report logic
from django.conf import settings # Assuming settings.COMP_ID would hold the company ID

class MRQNPrintDetailView(TemplateView):
    template_name = 'quality_control/mrqn_print_details.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Extract parameters from URL and query string, mimicking ASP.NET Request.QueryString
        master_id = self.kwargs.get('pk')
        mrn_no = self.request.GET.get('MRNNo', 'N/A')
        mrqn_no = self.request.GET.get('MRQNNo', 'N/A')
        fy_id = self.request.GET.get('FYId', 0) # Assuming FYId might not always be present or needed for data retrieval here
        
        # Get CompId from session or a default setting
        comp_id = self.request.session.get('compid', getattr(settings, 'DEFAULT_COMP_ID', 1)) # Default to 1 if not in session

        # Call the fat model method to get all report data
        header_data, detail_data = MaterialReturnQualityMaster.get_mrqn_report_data(
            master_id, comp_id, mrn_no, mrqn_no, fy_id
        )

        if not header_data:
            raise Http404("Material Return Quality Note details not found.")

        context['report_header'] = header_data
        context['report_details'] = detail_data
        context['cancel_url'] = reverse_lazy('quality_control:mrqn_list_page') # Redirect to the MRQN list page, assuming it exists

        return context

# A placeholder view for the main MRQN list page that the cancel button redirects to
class MRQNListPageView(TemplateView):
    template_name = 'quality_control/mrqn_list_page.html' # This template would contain the actual list of MRQNs
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['message'] = "This is the Material Return Quality Note list page."
        return context

# Note: The original ASP.NET page is a report viewer.
# Standard CRUD views (CreateView, UpdateView, DeleteView) and forms are not
# applicable for this specific page's conversion, as its functionality is
# solely to display pre-existing data as a report.
# If other ASP.NET pages handled MRQN creation/editing, then those would be
# converted into respective Django CreateView/UpdateView components.
```

#### 4.4 Templates (`quality_control/templates/quality_control/`)

We will create two templates:
1.  `mrqn_print_details.html`: The main page template for displaying the report.
2.  `_mrqn_details_table.html`: A partial template to render the DataTables content, which can be loaded dynamically or included directly. Given this is a print view, a direct include is simpler.

**`quality_control/templates/quality_control/mrqn_print_details.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col items-center justify-center mb-6">
        <h2 class="text-3xl font-extrabold text-gray-900 mb-2">Material Return Quality Note [MRQN] - Print</h2>
        <p class="text-gray-600 text-lg">Detailed Report</p>
    </div>

    <div class="bg-white shadow-lg rounded-lg p-6 mb-8">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-700">
            <div>
                <p><strong class="font-semibold">MRN No:</strong> {{ report_header.MRNNo }}</p>
                <p><strong class="font-semibold">MRQN No:</strong> {{ report_header.MRQNNo }}</p>
            </div>
            <div class="md:text-right">
                <p><strong class="font-semibold">MRQN Date:</strong> {{ report_header.MRQNDate }}</p>
                <p><strong class="font-semibold">Generated By:</strong> {{ report_header.GenBy }}</p>
                <p><strong class="font-semibold">Company Address:</strong> {{ report_header.Address }}</p>
            </div>
        </div>
    </div>

    <h3 class="text-xl font-bold text-gray-800 mb-4">Material Details</h3>
    <div class="bg-white shadow-lg rounded-lg p-6">
        <!-- This div will contain the DataTables table -->
        <div id="mrqnDetailsTable-container">
            {% include 'quality_control/_mrqn_details_table.html' %}
        </div>
    </div>

    <div class="mt-8 text-center">
        <a href="{{ cancel_url }}"
           class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-6 rounded-lg shadow-md transition duration-300 ease-in-out">
            Cancel
        </a>
        <button onclick="window.print()"
            class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-6 rounded-lg shadow-md transition duration-300 ease-in-out ml-4">
            Print Report
        </button>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // No specific Alpine.js needed for this static report view, but the block is kept for consistency.
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for UI state management.
    });
</script>
{% endblock %}
```

**`quality_control/templates/quality_control/_mrqn_details_table.html`**

```html
<table id="mrqnDetailsTable" class="min-w-full bg-white border border-gray-300 dataTable no-footer">
    <thead>
        <tr>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dept/WO No</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Returned Qty</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Accepted Qty</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
        </tr>
    </thead>
    <tbody>
        {% for detail in report_details %}
        <tr class="{% cycle 'bg-white' 'bg-gray-50' %} hover:bg-gray-100">
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ detail.ItemCode }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ detail.Description }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ detail.UOM }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ detail.DeptOrWONo }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ detail.RetQty }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ detail.AcceptedQty }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ detail.Remarks }}</td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="8" class="py-4 px-4 text-center text-gray-500">No material return quality note details found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    $('#mrqnDetailsTable').DataTable({
        "paging": true,      // Enable pagination
        "searching": true,   // Enable search box
        "ordering": true,    // Enable sorting
        "info": true,        // Show "Showing X to Y of Z entries"
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]], // Customize number of entries per page
        "columnDefs": [
            { "orderable": false, "targets": 0 } // Disable sorting for SN column
        ]
    });
});
</script>
```

#### 4.5 URLs (`quality_control/urls.py`)

Defines the URL patterns for accessing the MRQN print details view.

```python
from django.urls import path
from .views import MRQNPrintDetailView, MRQNListPageView

app_name = 'quality_control' # Define app_name for namespacing

urlpatterns = [
    # URL for the MRQN Print Details report
    # The 'pk' represents the MId (master ID) from the original ASP.NET query string 'Id'.
    # Query parameters (MRNNo, MRQNNo, FYId) will be accessed via request.GET in the view.
    path('mrqn_print/<int:pk>/', MRQNPrintDetailView.as_view(), name='mrqn_print_details'),
    
    # Placeholder URL for the MRQN list page, which the "Cancel" button redirects to
    path('mrqn_list/', MRQNListPageView.as_view(), name='mrqn_list_page'),
]

```

#### 4.6 Tests (`quality_control/tests.py`)

Comprehensive tests for the models and the view, focusing on the data retrieval logic for the report.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import datetime, timedelta
from unittest.mock import patch

from .models import (
    MaterialReturnQualityMaster, MaterialReturnQualityDetail,
    MaterialReturnMaster, MaterialReturnDetail,
    ItemMaster, UnitMaster, Department, OfficeStaff,
    get_company_address, get_item_code_part_no
)

# Mock the utility functions for isolated testing
@patch('quality_control.models.get_company_address', return_value="Test Company Address")
@patch('quality_control.models.get_item_code_part_no', side_effect=lambda comp_id, item_id: f"ITEM-{item_id}")
class MRQNModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all related models
        cls.company_id = 1
        
        # Office Staff
        cls.office_staff = OfficeStaff.objects.create(EmpId=101, Title="Mr", EmployeeName="John Doe", CompId=cls.company_id)
        
        # Unit Master
        cls.unit_ea = UnitMaster.objects.create(Id=1, Symbol="EA")
        cls.unit_kg = UnitMaster.objects.create(Id=2, Symbol="KG")
        
        # Item Master
        cls.item_a = ItemMaster.objects.create(Id=201, ItemCode="ITM001", ManfDesc="Item A Description", UOMBasic=cls.unit_ea, CompId=cls.company_id)
        cls.item_b = ItemMaster.objects.create(Id=202, ItemCode="ITM002", ManfDesc="Item B Description", UOMBasic=cls.unit_kg, CompId=cls.company_id)
        
        # Department
        cls.dept_prod = Department.objects.create(Id=301, Symbol="Production")
        cls.dept_qa = Department.objects.create(Id=302, Symbol="QA")

        # Material Return Master
        cls.mr_master = MaterialReturnMaster.objects.create(Id=401, CompId=cls.company_id)
        
        # Material Return Detail
        cls.mr_detail_1 = MaterialReturnDetail.objects.create(Id=501, MId=cls.mr_master, ItemId=cls.item_a, DeptId=cls.dept_prod, WONo="WO-123", RetQty=10.500, Remarks="Scratched surface")
        cls.mr_detail_2 = MaterialReturnDetail.objects.create(Id=502, MId=cls.mr_master, ItemId=cls.item_b, DeptId=None, WONo="WO-456", RetQty=5.250, Remarks="Wrong item", CompId=cls.company_id) # DeptId can be 0 or null in C#

        # Material Return Quality Master
        cls.mrqn_master_1 = MaterialReturnQualityMaster.objects.create(
            Id=601,
            MRNId=cls.mr_master.Id, # This MRNId in master is actually MRNNo from query string in ASP.NET
            MRQNNo="MRQN-001",
            SysDate=timezone.now(),
            SessionId=cls.office_staff.EmpId,
            CompId=cls.company_id
        )
        cls.mrqn_master_2 = MaterialReturnQualityMaster.objects.create(
            Id=602,
            MRNId=cls.mr_master.Id, # This MRNId in master is actually MRNNo from query string in ASP.NET
            MRQNNo="MRQN-002",
            SysDate=timezone.now() - timedelta(days=1),
            SessionId=cls.office_staff.EmpId,
            CompId=cls.company_id
        )

        # Material Return Quality Details (linking to MRQN Master and Material Return Details)
        MaterialReturnQualityDetail.objects.create(Id=701, MId=cls.mrqn_master_1, MRNId=cls.mr_detail_1, MRQNNo="MRQN-001", AcceptedQty=8.000)
        MaterialReturnQualityDetail.objects.create(Id=702, MId=cls.mrqn_master_1, MRNId=cls.mr_detail_2, MRQNNo="MRQN-001", AcceptedQty=3.500)


    def test_mrqn_master_creation(self, mock_get_item_code_part_no, mock_get_company_address):
        self.assertEqual(MaterialReturnQualityMaster.objects.count(), 2)
        mrqn = MaterialReturnQualityMaster.objects.get(Id=601)
        self.assertEqual(mrqn.MRQNNo, "MRQN-001")
        self.assertEqual(mrqn.CompId, self.company_id)
        
    def test_mrqn_detail_creation(self, mock_get_item_code_part_no, mock_get_company_address):
        self.assertEqual(MaterialReturnQualityDetail.objects.count(), 2)
        mrqn_det = MaterialReturnQualityDetail.objects.get(Id=701)
        self.assertEqual(mrqn_det.MId.MRQNNo, "MRQN-001")
        self.assertEqual(float(mrqn_det.AcceptedQty), 8.000)

    def test_get_mrqn_report_data_header(self, mock_get_item_code_part_no, mock_get_company_address):
        header, details = MaterialReturnQualityMaster.get_mrqn_report_data(
            self.mrqn_master_1.Id, self.company_id, "MRN-XYZ", "MRQN-001", 2023
        )
        self.assertIsNotNone(header)
        self.assertEqual(header['MRNNo'], "MRN-XYZ")
        self.assertEqual(header['MRQNNo'], "MRQN-001")
        self.assertEqual(header['GenBy'], f"{self.office_staff.Title}. {self.office_staff.EmployeeName}")
        self.assertEqual(header['Address'], "Test Company Address")
        self.assertIn(self.mrqn_master_1.SysDate.strftime('%d/%m/%Y'), header['MRQNDate'])

    def test_get_mrqn_report_data_details(self, mock_get_item_code_part_no, mock_get_company_address):
        header, details = MaterialReturnQualityMaster.get_mrqn_report_data(
            self.mrqn_master_1.Id, self.company_id, "MRN-XYZ", "MRQN-001", 2023
        )
        self.assertEqual(len(details), 2)

        detail_1 = next(item for item in details if item['Id'] == self.mr_detail_1.Id) # Match by MRNId from ASP.NET logic
        self.assertIsNotNone(detail_1)
        self.assertEqual(detail_1['ItemCode'], f"ITEM-{self.item_a.Id}")
        self.assertEqual(detail_1['Description'], self.item_a.ManfDesc)
        self.assertEqual(detail_1['UOM'], self.unit_ea.Symbol)
        self.assertEqual(detail_1['DeptOrWONo'], self.dept_prod.Symbol)
        self.assertEqual(detail_1['RetQty'], f"{self.mr_detail_1.RetQty:.3f}")
        # Note: AcceptedQty in our details_list comes from MaterialReturnQualityDetail.AcceptedQty
        self.assertEqual(detail_1['AcceptedQty'], "8.000") 
        self.assertEqual(detail_1['Remarks'], self.mr_detail_1.Remarks)
        
        detail_2 = next(item for item in details if item['Id'] == self.mr_detail_2.Id) # Match by MRNId
        self.assertIsNotNone(detail_2)
        self.assertEqual(detail_2['ItemCode'], f"ITEM-{self.item_b.Id}")
        self.assertEqual(detail_2['Description'], self.item_b.ManfDesc)
        self.assertEqual(detail_2['UOM'], self.unit_kg.Symbol)
        self.assertEqual(detail_2['DeptOrWONo'], self.mr_detail_2.WONo) # DeptId is null/0, so WONo should be used
        self.assertEqual(detail_2['RetQty'], f"{self.mr_detail_2.RetQty:.3f}")
        self.assertEqual(detail_2['AcceptedQty'], "3.500")
        self.assertEqual(detail_2['Remarks'], self.mr_detail_2.Remarks)

    def test_get_mrqn_report_data_not_found(self, mock_get_item_code_part_no, mock_get_company_address):
        header, details = MaterialReturnQualityMaster.get_mrqn_report_data(
            9999, self.company_id, "MRN-XXX", "MRQN-XXX", 2023
        )
        self.assertIsNone(header)
        self.assertEqual(details, [])


class MRQNPrintDetailViewTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.company_id = 1
        
        # Create minimal data for view tests
        self.office_staff = OfficeStaff.objects.create(EmpId=101, Title="Mr", EmployeeName="John Doe", CompId=self.company_id)
        self.unit_ea = UnitMaster.objects.create(Id=1, Symbol="EA")
        self.item_a = ItemMaster.objects.create(Id=201, ItemCode="ITM001", ManfDesc="Item A", UOMBasic=self.unit_ea, CompId=self.company_id)
        self.mr_master = MaterialReturnMaster.objects.create(Id=401, CompId=self.company_id)
        self.mr_detail = MaterialReturnDetail.objects.create(Id=501, MId=self.mr_master, ItemId=self.item_a, DeptId=None, WONo="WO-789", RetQty=1.000, Remarks="Test")
        self.mrqn_master = MaterialReturnQualityMaster.objects.create(
            Id=601,
            MRNId=self.mr_master.Id,
            MRQNNo="MRQN-VIEWTEST",
            SysDate=timezone.now(),
            SessionId=self.office_staff.EmpId,
            CompId=self.company_id
        )
        MaterialReturnQualityDetail.objects.create(Id=701, MId=self.mrqn_master, MRNId=self.mr_detail, MRQNNo="MRQN-VIEWTEST", AcceptedQty=0.900)

    @patch('quality_control.models.get_company_address', return_value="View Test Address")
    @patch('quality_control.models.get_item_code_part_no', return_value="VIEW-ITEM-CODE")
    def test_mrqn_print_detail_view_success(self, mock_get_item_code_part_no, mock_get_company_address):
        url = reverse('quality_control:mrqn_print_details', args=[self.mrqn_master.Id])
        response = self.client.get(url, {
            'MRNNo': 'TESTMRN',
            'MRQNNo': 'TESTMRQN',
            'FYId': '2023'
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/mrqn_print_details.html')
        self.assertIn('report_header', response.context)
        self.assertIn('report_details', response.context)
        self.assertEqual(response.context['report_header']['MRNNo'], 'TESTMRN')
        self.assertEqual(len(response.context['report_details']), 1)
        self.assertEqual(response.context['report_details'][0]['ItemCode'], 'VIEW-ITEM-CODE')
        self.assertContains(response, 'Material Return Quality Note [MRQN] - Print')
        self.assertContains(response, 'TESTMRQN')
        self.assertContains(response, '0.900') # Check for formatted quantity

    @patch('quality_control.models.get_company_address', return_value="View Test Address")
    @patch('quality_control.models.get_item_code_part_no', return_value="VIEW-ITEM-CODE")
    def test_mrqn_print_detail_view_not_found(self, mock_get_item_code_part_no, mock_get_company_address):
        url = reverse('quality_control:mrqn_print_details', args=[99999]) # Non-existent ID
        response = self.client.get(url, {
            'MRNNo': 'TESTMRN',
            'MRQNNo': 'TESTMRQN',
            'FYId': '2023'
        })
        self.assertEqual(response.status_code, 404) # Expecting Http404

    @patch('quality_control.models.get_company_address', return_value="View Test Address")
    @patch('quality_control.models.get_item_code_part_no', return_value="VIEW-ITEM-CODE")
    def test_mrqn_print_detail_view_cancel_url(self, mock_get_item_code_part_no, mock_get_company_address):
        url = reverse('quality_control:mrqn_print_details', args=[self.mrqn_master.Id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertIn('cancel_url', response.context)
        self.assertEqual(response.context['cancel_url'], reverse('quality_control:mrqn_list_page'))
        self.assertContains(response, f'href="{reverse("quality_control:mrqn_list_page")}"')

```

### Step 5: HTMX and Alpine.js Integration

**Analysis:** For this specific page, HTMX and Alpine.js are less critical than for a typical CRUD interface, as it's primarily a static report display. However, we've adhered to the guidelines by:

*   **DataTables:** Implemented for the tabular detail data, enabling client-side search, sort, and pagination.
*   **HTMX:** While not heavily used for dynamic content *on this page*, the general setup (base.html, potential future partial loading) respects HTMX principles. The "Cancel" button is a simple link, but could be made HTMX-driven if navigating within a modal or partial. The "Print" button uses `window.print()`.
*   **Alpine.js:** The `extra_js` block is included, ready for any future minor client-side UI state management, though not strictly required for this page's current functionality.

### Final Notes

*   **Placeholders:** All placeholders (`[MODEL_NAME]`, `[APP_NAME]`, etc.) have been replaced with concrete values relevant to the Material Return Quality Note report.
*   **DRY:** The `_mrqn_details_table.html` serves as a partial template, and the report logic is centralized in the model method, adhering to DRY principles.
*   **Fat Model, Thin View:** The complex data retrieval from multiple tables is entirely contained within the `MaterialReturnQualityMaster.get_mrqn_report_data` method (fat model). The `MRQNPrintDetailView` remains concise, primarily orchestrating data retrieval and template rendering (thin view).
*   **Testing:** Comprehensive unit tests for the model's data retrieval logic and integration tests for the view ensure high test coverage and correctness.
*   **Tailwind CSS:** Assumed to be configured in `core/base.html` and applied via the classes in the generated HTML templates.
*   **Company ID:** The `CompId` is assumed to come from `self.request.session` or `settings.DEFAULT_COMP_ID`, reflecting common enterprise application patterns.
*   **Crystal Reports Replacement:** Direct Crystal Report viewing is replaced by Django's templating system rendering the data retrieved from the database, which is the standard approach in web frameworks like Django. If advanced PDF generation or complex layouts are required, libraries like `ReportLab` or `WeasyPrint` (for HTML to PDF) would be integrated.