This modernization plan details the transition of your legacy ASP.NET application, specifically the "Goods Quality Note [GQN] - Edit" module, to a robust and scalable Django 5.0+ solution. We will leverage AI-assisted automation to systematically convert your existing logic and UI components into modern Django patterns, focusing on business value and maintainability.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the ASP.NET code.

**Analysis:**
The ASP.NET code interacts with an `Sp_GQN_Edit` stored procedure and directly queries `tblMM_Supplier_master`. The `GridView2` displays data that is a composite of information likely spanning several tables, based on the filter parameters (`GQNNo`, `GRRNo`, `PONo`, `SupplierId`). For the purpose of `managed = False` and `db_table`, we will infer the primary tables used to generate this data.

**Identified Tables & Columns:**

1.  **Main GQN Data:** Based on the common prefix and key fields like `GQNNo`, `Id`, `SysDate`, we infer the main table for GQN records is `tblQc_MaterialQuality_Master`.
    *   **Table Name:** `tblQc_MaterialQuality_Master`
    *   **Columns:**
        *   `Id` (Primary Key, integer)
        *   `GQNNo` (String, max length unknown, infer `CharField`)
        *   `SysDate` (DateTime, infer `DateField` or `DateTimeField`)
        *   `FinYearId` (Integer)
        *   `GRRNo` (String, infer `CharField` - likely a foreign key or lookup to another table, but will be treated as CharField for direct mapping as seen in the GridView)
        *   `GINNo` (String, infer `CharField`)
        *   `PONo` (String, infer `CharField`)
        *   `SupId` (Integer, infer `IntegerField` - likely a foreign key to `tblMM_Supplier_master`)
        *   `ChNO` (String, infer `CharField`)
        *   `ChDT` (DateTime, infer `DateField` or `DateTimeField`)
        *   *(Note: The `Sp_GQN_Edit` implies more complex joins. For `managed=False`, we map to the core table and handle joins in Django ORM).*

2.  **Supplier Master Data:** Explicitly queried by `AutoCompleteExtender` for supplier search.
    *   **Table Name:** `tblMM_Supplier_master`
    *   **Columns:**
        *   `SupplierId` (Primary Key, integer)
        *   `SupplierName` (String, infer `CharField`)
        *   `CompId` (Integer, infer `IntegerField`)

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations and logic within the ASP.NET code.

**Analysis:**

*   **Read (List & Filter):** The primary function is displaying a list of "Goods Quality Notes" in a `GridView`, with extensive filtering capabilities based on `Supplier Name`, `GQN No`, `GRR No`, and `PO No`. This involves dynamically constructing SQL queries (via a stored procedure `Sp_GQN_Edit`) and applying parameters (`CompId`, `FinYearId`, and various search terms). Pagination is also implemented.
*   **Search/Autocomplete:** The `AutoCompleteExtender` provides a real-time search for suppliers by name, fetching data from `tblMM_Supplier_master`.
*   **Navigation/View Details:** The `GridView2_RowCommand` suggests a "Select" action on a row, which redirects to `GoodsQualityNote_GQN_Edit_Details.aspx` with numerous query parameters. This indicates a navigation to an edit/detail page for a specific GQN record. For a complete modernization, we will include standard CRUD (Create, Update, Delete) views, assuming the `Details` page implies an Update capability.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles in the user interface.

**Analysis:**

*   **Search Form:**
    *   `DropDownList1`: A `select` element for choosing the search criteria (Supplier Name, GQN No, GRR No, PO No).
    *   `txtSupplier` and `Txtfield`: `input` elements for entering the search term, with dynamic visibility based on the selected criteria.
    *   `Button1`: A "Search" button to trigger the data refresh.
    *   `AutoCompleteExtender1`: Provides autocomplete functionality for the `txtSupplier` field, requiring a backend service.
*   **Data Display:**
    *   `GridView2`: The main data table, displaying a list of GQN records with various details and implicitly supporting pagination.
*   **Actions:**
    *   Implicit "Select" action per row in the `GridView` (though `LinkButton` is commented out, `RowCommand` implies its intent), leading to a detail/edit page.

### Step 4: Generate Django Code

We will create a Django application named `goods_quality_notes` to house the models, forms, views, and templates for this module.

#### 4.1 Models (`goods_quality_notes/models.py`)

We'll define two models, `GoodsQualityNote` and `Supplier`, directly mapping to the identified database tables using `managed = False`. We'll also introduce a custom manager for `GoodsQualityNote` to encapsulate the complex search and filtering logic, adhering to the "fat model, thin view" principle.

```python
from django.db import models
from django.db.models import Q # Used for complex query conditions

class GoodsQualityNoteManager(models.Manager):
    """
    Custom manager for GoodsQualityNote to handle complex search and filtering
    logic previously found in the ASP.NET Page_Load and loadData methods.
    """
    def search_and_filter(self, search_by, search_term, company_id, fin_year_id):
        # Start with the base queryset, filtering by mandatory company_id and fin_year_id
        # Assuming GoodsQualityNote has a company_id field for filtering.
        # If not, this filter would need to be applied via related models or
        # passed to a raw SQL query/stored procedure call if that's the final approach.
        # For this exercise, we assume company_id is a field on GoodsQualityNote.
        queryset = self.get_queryset().filter(
            company_id=company_id,
            fin_year_id=fin_year_id
        )

        # Optimize queries by selecting related data needed for display and filtering
        # Assuming 'supplier' is the related_name or field name for the ForeignKey to Supplier.
        queryset = queryset.select_related('supplier')

        # Apply dynamic search conditions based on the 'search_by' criteria
        if search_term:
            if search_by == '0':  # Supplier Name (from DropDownList1)
                # The original ASP.NET used 'SupplierName [SupplierId]' and then parsed the ID.
                # Here, we'll try to find by ID if the term is numeric, or by name if text.
                if search_term.isdigit():
                    supplier_id = int(search_term)
                    queryset = queryset.filter(supplier__id=supplier_id)
                else:
                    queryset = queryset.filter(supplier__supplier_name__icontains=search_term)
            elif search_by == '1':  # GQN No
                queryset = queryset.filter(gqn_no__icontains=search_term)
            elif search_by == '2':  # GRR No
                queryset = queryset.filter(grr_no__icontains=search_term)
            elif search_by == '3':  # PO No
                queryset = queryset.filter(po_no__icontains=search_term)
        
        # Order the results (example ordering, adjust as needed)
        queryset = queryset.order_by('-sys_date', '-gqn_no')
        
        return queryset

class GoodsQualityNote(models.Model):
    """
    Represents a Goods Quality Note record, mapping to tblQc_MaterialQuality_Master.
    Fields are inferred from GridView columns and filter logic.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    gqn_no = models.CharField(db_column='GQNNo', max_length=50) # Assuming max_length
    sys_date = models.DateField(db_column='SysDate')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    # Assuming these fields are present on tblQc_MaterialQuality_Master or are FKs
    # For managed=False, we treat them as direct fields on the mapped table for simplicity,
    # even if originally they might be from other joined tables in the SP.
    grr_no = models.CharField(db_column='GRRNo', max_length=50, blank=True, null=True)
    gin_no = models.CharField(db_column='GINNo', max_length=50, blank=True, null=True)
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    
    # ForeignKey to Supplier, mapping to SupId
    # If SupId in DB is not a true FK, use IntegerField and handle lookup manually.
    # Assuming it is an FK for better ORM usage.
    supplier = models.ForeignKey('Supplier', on_delete=models.DO_NOTHING, db_column='SupId', related_name='goods_quality_notes')
    
    challan_no = models.CharField(db_column='ChNO', max_length=50, blank=True, null=True)
    challan_date = models.DateField(db_column='ChDT', blank=True, null=True)
    
    # Adding CompId as inferred from the C# code parameters
    company_id = models.IntegerField(db_column='CompId', default=1) # Assuming a default or populated elsewhere

    objects = GoodsQualityNoteManager() # Attach the custom manager

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblQc_MaterialQuality_Master'
        verbose_name = 'Goods Quality Note'
        verbose_name_plural = 'Goods Quality Notes'

    def __str__(self):
        return f"{self.gqn_no} - {self.supplier.supplier_name}"

class Supplier(models.Model):
    """
    Represents a Supplier, mapping to tblMM_Supplier_master.
    Used for the autocomplete functionality.
    """
    id = models.IntegerField(db_column='SupplierId', primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255) # Assuming max_length
    company_id = models.IntegerField(db_column='CompId', default=1) # Assuming a default or populated elsewhere

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.supplier_name

    # Business logic methods can be added here, e.g., for data validation or transformations.
    # Example:
    def format_for_autocomplete(self):
        """
        Formats the supplier name and ID for autocomplete suggestions.
        Matches the ASP.NET `sql` web method output: "SupplierName [SupplierId]".
        """
        return f"{self.supplier_name} [{self.id}]"

```

#### 4.2 Forms (`goods_quality_notes/forms.py`)

A form for the main `GoodsQualityNote` model, and a search form for the list page.

```python
from django import forms
from .models import GoodsQualityNote, Supplier

class GoodsQualityNoteForm(forms.ModelForm):
    """
    Form for creating and updating Goods Quality Note records.
    """
    class Meta:
        model = GoodsQualityNote
        fields = [
            'gqn_no', 'sys_date', 'fin_year_id', 'grr_no', 'gin_no',
            'po_no', 'supplier', 'challan_no', 'challan_date', 'company_id'
        ]
        widgets = {
            'gqn_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sys_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'fin_year_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'grr_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'gin_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'po_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'supplier': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'challan_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'challan_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'company_id': forms.HiddenInput(), # Likely handled by session/context
        }

    def clean_supplier(self):
        """
        Custom cleaning for the supplier field. If the original ASP.NET was using
        'SupplierName [SupplierId]' and then parsing, this form should handle
        receiving an ID or providing a lookup. Assuming the `supplier` field
        is a standard ForeignKey dropdown for now.
        """
        supplier_obj = self.cleaned_data.get('supplier')
        if not supplier_obj:
            raise forms.ValidationError("Supplier is required.")
        return supplier_obj

class GQNFilterForm(forms.Form):
    """
    Form for handling the search filters on the Goods Quality Note list page.
    Mimics the ASP.NET DropDownList and TextBoxes.
    """
    SEARCH_BY_CHOICES = [
        ('0', 'Supplier Name'),
        ('1', 'GQN No'),
        ('2', 'GRR No'),
        ('3', 'PO No'),
    ]
    search_by = forms.ChoiceField(
        choices=SEARCH_BY_CHOICES,
        required=False,
        label="Search By",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': "{% url 'goodsqualitynote_list' %}", # Trigger GET request on change
            'hx-trigger': 'change',
            'hx-target': '#gqn_table-container', # Target the table container
            'hx-swap': 'outerHTML',
            'hx-include': '#search_term_input', # Include the search term
            '_': "on change set #search_term_input.value = '' and if target.value == '0' add .hidden to #text_field_container else remove .hidden from #text_field_container and if target.value == '0' remove .hidden from #supplier_search_container else add .hidden to #supplier_search_container" # Alpine.js logic for visibility
        })
    )
    search_term = forms.CharField(
        max_length=255,
        required=False,
        label="Search Term",
        widget=forms.TextInput(attrs={
            'id': 'search_term_input',
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter search term...'
        })
    )

    # Specific fields for the two input boxes.
    # Only one of these will be visible and actually used via HTMX/Alpine.js
    supplier_search_term = forms.CharField(
        max_length=255,
        required=False,
        label="Supplier Name",
        widget=forms.TextInput(attrs={
            'id': 'txtSupplier', # Matches original ID for clarity
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter supplier name',
            # HTMX for autocomplete: Trigger search on input, target for results, swap type
            'hx-get': "{% url 'supplier_autocomplete' %}",
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#autocomplete-results',
            'hx-swap': 'innerHTML',
            'name': 'supplier_search_term' # Ensure name is set for form processing
        })
    )
    
    other_search_term = forms.CharField(
        max_length=255,
        required=False,
        label="Search Value",
        widget=forms.TextInput(attrs={
            'id': 'Txtfield', # Matches original ID for clarity
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter value'
        })
    )

    def clean(self):
        cleaned_data = super().clean()
        search_by = cleaned_data.get('search_by')
        # Based on search_by, determine which field (supplier_search_term or other_search_term)
        # to use as the actual 'search_term' for the queryset filtering.
        if search_by == '0':
            cleaned_data['search_term'] = cleaned_data.get('supplier_search_term')
        else:
            cleaned_data['search_term'] = cleaned_data.get('other_search_term')
        return cleaned_data

```

#### 4.3 Views (`goods_quality_notes/views.py`)

We'll implement Class-Based Views (CBVs) for the list, create, update, and delete operations, along with a dedicated view for the HTMX-driven DataTables content and supplier autocomplete. Each view adheres to the 5-15 line limit by offloading logic to models or custom managers.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from .models import GoodsQualityNote, Supplier
from .forms import GoodsQualityNoteForm, GQNFilterForm
from django.shortcuts import get_object_or_404
from django.db.models import Q # For autocomplete

class GoodsQualityNoteListView(ListView):
    """
    Displays a list of Goods Quality Notes with search and filter options.
    This view renders the initial page containing the search form and a placeholder
    for the HTMX-loaded DataTables.
    """
    model = GoodsQualityNote
    template_name = 'goods_quality_notes/goodsqualitynote/list.html'
    context_object_name = 'goodsqualitynotes'
    paginate_by = 20 # Matches ASP.NET PageSize

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize filter form with current GET parameters or defaults
        context['filter_form'] = GQNFilterForm(self.request.GET)
        return context
    
    # get_queryset is implemented in GoodsQualityNoteTablePartialView for HTMX reloads


class GoodsQualityNoteTablePartialView(ListView):
    """
    Renders only the table portion of the Goods Quality Notes list for HTMX updates.
    This view receives search parameters via GET and uses the model manager for filtering.
    """
    model = GoodsQualityNote
    template_name = 'goods_quality_notes/goodsqualitynote/_table.html'
    context_object_name = 'goodsqualitynotes'
    paginate_by = 20 # Important for DataTables pagination

    def get_queryset(self):
        # Simulate session variables (replace with actual session/user context in production)
        company_id = self.request.session.get('compid', 1) 
        fin_year_id = self.request.session.get('finyear', 1) 

        # Process the filter form to extract search parameters
        filter_form = GQNFilterForm(self.request.GET)
        if filter_form.is_valid():
            search_by = filter_form.cleaned_data.get('search_by', '0')
            search_term = filter_form.cleaned_data.get('search_term', '')
            
            # Use the custom manager's method for filtering
            return GoodsQualityNote.objects.search_and_filter(
                search_by, search_term, company_id, fin_year_id
            )
        return GoodsQualityNote.objects.none() # Return empty if form is invalid or no search

class GoodsQualityNoteCreateView(CreateView):
    """
    Handles creation of new Goods Quality Note records via a modal form.
    """
    model = GoodsQualityNote
    form_class = GoodsQualityNoteForm
    template_name = 'goods_quality_notes/goodsqualitynote/_form.html' # Partial template for HTMX
    success_url = reverse_lazy('goodsqualitynote_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        # Populate session-dependent fields before saving
        form.instance.company_id = self.request.session.get('compid', 1)
        form.instance.fin_year_id = self.request.session.get('finyear', 1)
        response = super().form_valid(form)
        messages.success(self.request, 'Goods Quality Note added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content and trigger a refresh event
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshGoodsQualityNoteList'
                }
            )
        return response

    def form_invalid(self, form):
        # Render the form again with errors for HTMX
        if self.request.headers.get('HX-Request'):
            return HttpResponse(render_to_string(self.template_name, {'form': form}), status=400)
        return super().form_invalid(form)


class GoodsQualityNoteUpdateView(UpdateView):
    """
    Handles updating existing Goods Quality Note records via a modal form.
    """
    model = GoodsQualityNote
    form_class = GoodsQualityNoteForm
    template_name = 'goods_quality_notes/goodsqualitynote/_form.html' # Partial template for HTMX
    success_url = reverse_lazy('goodsqualitynote_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Goods Quality Note updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshGoodsQualityNoteList'
                }
            )
        return response

    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            return HttpResponse(render_to_string(self.template_name, {'form': form}), status=400)
        return super().form_invalid(form)


class GoodsQualityNoteDeleteView(DeleteView):
    """
    Handles deletion of Goods Quality Note records via a modal confirmation.
    """
    model = GoodsQualityNote
    template_name = 'goods_quality_notes/goodsqualitynote/_confirm_delete.html' # Partial template for HTMX
    success_url = reverse_lazy('goodsqualitynote_list') # Fallback for non-HTMX requests

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Goods Quality Note deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshGoodsQualityNoteList'
                }
            )
        return response


class SupplierAutocompleteView(View):
    """
    Provides autocomplete suggestions for the Supplier field via HTMX.
    Mimics the ASP.NET `sql` WebMethod.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('term', '').strip()
        company_id = request.session.get('compid', 1) # Get company_id from session

        # Filter suppliers based on prefixText and company_id
        # The ASP.NET logic used StartsWith, here we use icontains for more flexibility
        suppliers = Supplier.objects.filter(
            company_id=company_id,
            supplier_name__icontains=query
        ).order_by('supplier_name')[:10] # Limit to 10 results as in ASP.NET

        results = [supplier.format_for_autocomplete() for supplier in suppliers]
        
        # HTMX expects HTML fragments for display in a div, not JSON by default for a text input.
        # So we'll render a list of suggestions.
        html_suggestions = render_to_string('goods_quality_notes/supplier/_autocomplete_results.html', {'results': results})
        return HttpResponse(html_suggestions)

```

#### 4.4 Templates

**4.4.1 `goods_quality_notes/goodsqualitynote/list.html`**

```html
{% extends 'core/base.html' %}

{% block title %}Goods Quality Notes - Edit{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Goods Quality Note [GQN] - Edit</h2>
        <form class="space-y-4" hx-get="{% url 'goodsqualitynote_table' %}" hx-target="#gqn_table-container" hx-swap="outerHTML" hx-trigger="submit, keyup changed delay:500ms from:#search_term_input">
            <div class="flex flex-wrap items-center gap-4">
                <div class="flex-shrink-0">
                    <label for="{{ filter_form.search_by.id_for_label }}" class="sr-only">Search By</label>
                    {{ filter_form.search_by }}
                </div>
                
                <div id="supplier_search_container" class="flex-grow {% if filter_form.search_by.value != '0' %}hidden{% endif %} relative">
                    <label for="{{ filter_form.supplier_search_term.id_for_label }}" class="sr-only">Supplier Name</label>
                    {{ filter_form.supplier_search_term }}
                    <div id="autocomplete-results" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-60 overflow-y-auto">
                        <!-- Autocomplete results will be loaded here -->
                    </div>
                </div>

                <div id="text_field_container" class="flex-grow {% if filter_form.search_by.value == '0' %}hidden{% endif %}">
                    <label for="{{ filter_form.other_search_term.id_for_label }}" class="sr-only">Search Value</label>
                    {{ filter_form.other_search_term }}
                </div>
                
                <div class="flex-shrink-0">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow">
                        Search
                    </button>
                </div>
            </div>
        </form>
    </div>

    <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-gray-700">GQN Records</h3>
        <button 
            class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded shadow"
            hx-get="{% url 'goodsqualitynote_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New GQN
        </button>
    </div>
    
    <div id="gqn_table-container"
         hx-trigger="load, refreshGoodsQualityNoteList from:body"
         hx-get="{% url 'goodsqualitynote_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg overflow-x-auto">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Goods Quality Notes...</p>
        </div>
    </div>
    
    <!-- Modal for CRUD operations -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize Alpine.js for visibility toggling
    document.addEventListener('alpine:init', () => {
        Alpine.data('gqnSearch', () => ({
            searchBy: '{{ filter_form.search_by.value|default:"0" }}', // Default to '0' (Supplier Name)
            init() {
                // Ensure correct visibility on page load based on initial searchBy value
                this.toggleSearchInputs();
            },
            toggleSearchInputs() {
                const supplierContainer = document.getElementById('supplier_search_container');
                const textFieldContainer = document.getElementById('text_field_container');
                const searchTextInput = document.getElementById('search_term_input'); // The hidden field that always gets the value
                const txtSupplier = document.getElementById('txtSupplier'); // The visible supplier field
                const Txtfield = document.getElementById('Txtfield'); // The visible other fields field

                if (this.searchBy === '0') {
                    supplierContainer.classList.remove('hidden');
                    textFieldContainer.classList.add('hidden');
                    searchTextInput.value = txtSupplier.value; // Sync value to the hidden field
                } else {
                    supplierContainer.classList.add('hidden');
                    textFieldContainer.classList.remove('hidden');
                    searchTextInput.value = Txtfield.value; // Sync value to the hidden field
                }
            },
            updateSearchTermInput(event) {
                // When either visible input changes, update the hidden 'search_term' field
                const searchTextInput = document.getElementById('search_term_input');
                searchTextInput.value = event.target.value;
            },
            selectAutocompleteSuggestion(value) {
                // When an autocomplete suggestion is clicked, populate txtSupplier and trigger search
                const txtSupplier = document.getElementById('txtSupplier');
                txtSupplier.value = value;
                const searchTextInput = document.getElementById('search_term_input');
                searchTextInput.value = value; // Sync value to the hidden field
                document.getElementById('autocomplete-results').innerHTML = ''; // Clear suggestions
                // Manually trigger HTMX request to refresh table
                htmx.trigger(document.getElementById('search_term_input'), 'changed');
            }
        }));
    });
    // This part should be within `document.addEventListener('htmx:afterSwap')` or similar
    // for when the table is loaded/reloaded, ensuring DataTables initialization.
    // For simplicity, we add it directly here assuming the _table.html handles its own init.
</script>
{% endblock %}
```

**4.4.2 `goods_quality_notes/goodsqualitynote/_table.html`**

```html
<div id="gqn_table-container" class="bg-white shadow-md rounded-lg overflow-x-auto">
    {% if goodsqualitynotes %}
    <table id="goodsqualitynoteTable" class="min-w-full bg-white divide-y divide-gray-200">
        <thead>
            <tr>
                <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Fin Year</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">GQN No</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Date</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">GRR No</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">GIN No</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">PO No</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Name of Supplier</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Challan No</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Challan Date</th>
                <th class="py-3 px-4 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for gqn in goodsqualitynotes %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter0|add:page_obj.start_index }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ gqn.fin_year_id }}</td> {# FinYear is not a direct field, displaying FinYearId as placeholder #}
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ gqn.gqn_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ gqn.sys_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ gqn.grr_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ gqn.gin_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ gqn.po_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ gqn.supplier.supplier_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ gqn.challan_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ gqn.challan_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="text-indigo-600 hover:text-indigo-900 mr-4"
                        hx-get="{% url 'goodsqualitynote_edit' gqn.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="text-red-600 hover:text-red-900"
                        hx-get="{% url 'goodsqualitynote_delete' gqn.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <div class="text-center p-8">
        <p class="text-gray-600 font-semibold text-lg">No data to display !</p>
        <p class="text-gray-500 text-sm mt-1">Adjust your search filters or add new records.</p>
    </div>
    {% endif %}
</div>

<script>
    // Initialize DataTables only after HTMX has swapped in the new table content
    // This script should be located *after* the table element in the _table.html partial.
    // It will run every time the table partial is loaded via HTMX.
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#goodsqualitynoteTable')) {
            $('#goodsqualitynoteTable').DataTable().destroy(); // Destroy existing instance if any
        }
        $('#goodsqualitynoteTable').DataTable({
            "pageLength": {{ page_obj.paginator.per_page }}, // Use Django's paginate_by
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "autoWidth": false,
            // Disable DataTables' own pagination and searching since HTMX handles it for the primary search.
            // If DataTables client-side search/pagination is desired, need to adjust backend.
            // For this migration, we're assuming the server-side filtering via HTMX is primary.
            "paging": true, // Enable if using client-side pagination
            "searching": false, // Disable DataTables' default search input
            "info": true // Enable info display
        });
    });
</script>
```

**4.4.3 `goods_quality_notes/goodsqualitynote/_form.html`**

```html
<div class="p-6" x-data="{ companyId: '{{ form.instance.company_id.value|default:1 }}' }">
    <h3 class="text-2xl font-semibold text-gray-800 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Goods Quality Note</h3>
    <form hx-post="{{ request.path }}" 
          hx-swap="none" 
          hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label for="{{ form.gqn_no.id_for_label }}" class="block text-sm font-medium text-gray-700">GQN No</label>
                {{ form.gqn_no }}
                {% if form.gqn_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.gqn_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.sys_date.id_for_label }}" class="block text-sm font-medium text-gray-700">System Date</label>
                {{ form.sys_date }}
                {% if form.sys_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.sys_date.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.fin_year_id.id_for_label }}" class="block text-sm font-medium text-gray-700">Financial Year ID</label>
                {{ form.fin_year_id }}
                {% if form.fin_year_id.errors %}<p class="text-red-500 text-xs mt-1">{{ form.fin_year_id.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.supplier.id_for_label }}" class="block text-sm font-medium text-gray-700">Supplier</label>
                {{ form.supplier }}
                {% if form.supplier.errors %}<p class="text-red-500 text-xs mt-1">{{ form.supplier.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.grr_no.id_for_label }}" class="block text-sm font-medium text-gray-700">GRR No</label>
                {{ form.grr_no }}
                {% if form.grr_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.grr_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.gin_no.id_for_label }}" class="block text-sm font-medium text-gray-700">GIN No</label>
                {{ form.gin_no }}
                {% if form.gin_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.gin_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.po_no.id_for_label }}" class="block text-sm font-medium text-gray-700">PO No</label>
                {{ form.po_no }}
                {% if form.po_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.po_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.challan_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Challan No</label>
                {{ form.challan_no }}
                {% if form.challan_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.challan_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.challan_date.id_for_label }}" class="block text-sm font-medium text-gray-700">Challan Date</label>
                {{ form.challan_date }}
                {% if form.challan_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.challan_date.errors }}</p>{% endif %}
            </div>
            
            {# Hidden field for company_id, populated from session in view #}
            {{ form.company_id }}
            {% if form.company_id.errors %}<p class="text-red-500 text-xs mt-1">{{ form.company_id.errors }}</p>{% endif %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded shadow"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow">
                Save
            </button>
        </div>
    </form>
</div>
```

**4.4.4 `goods_quality_notes/goodsqualitynote/_confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-800 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete Goods Quality Note: <span class="font-bold">"{{ object }}"</span>?</p>
    <form hx-post="{% url 'goodsqualitynote_delete' object.pk %}" 
          hx-swap="none" 
          hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded shadow"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded shadow">
                Delete
            </button>
        </div>
    </form>
</div>
```

**4.4.5 `goods_quality_notes/supplier/_autocomplete_results.html`**

```html
{% for result in results %}
    <div class="px-4 py-2 hover:bg-gray-100 cursor-pointer text-gray-800"
         hx-on:click="selectAutocompleteSuggestion('{{ result|split:" ["|first }}')"> {# Alpine.js function to populate input #}
        {{ result }}
    </div>
{% empty %}
    <div class="px-4 py-2 text-gray-500">No suggestions</div>
{% endfor %}
```

#### 4.5 URLs (`goods_quality_notes/urls.py`)

```python
from django.urls import path
from .views import (
    GoodsQualityNoteListView,
    GoodsQualityNoteTablePartialView,
    GoodsQualityNoteCreateView,
    GoodsQualityNoteUpdateView,
    GoodsQualityNoteDeleteView,
    SupplierAutocompleteView
)

urlpatterns = [
    path('gqn/', GoodsQualityNoteListView.as_view(), name='goodsqualitynote_list'),
    path('gqn/table/', GoodsQualityNoteTablePartialView.as_view(), name='goodsqualitynote_table'),
    path('gqn/add/', GoodsQualityNoteCreateView.as_view(), name='goodsqualitynote_add'),
    path('gqn/edit/<int:pk>/', GoodsQualityNoteUpdateView.as_view(), name='goodsqualitynote_edit'),
    path('gqn/delete/<int:pk>/', GoodsQualityNoteDeleteView.as_view(), name='goodsqualitynote_delete'),
    path('supplier/autocomplete/', SupplierAutocompleteView.as_view(), name='supplier_autocomplete'),
]
```

#### 4.6 Tests (`goods_quality_notes/tests.py`)

Comprehensive tests for models and views ensure the reliability and correctness of the migration.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import GoodsQualityNote, Supplier
from datetime import date
from django.http import HttpResponse

class GoodsQualityNoteModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a test supplier first as GQN has a ForeignKey to Supplier
        cls.supplier = Supplier.objects.create(id=101, supplier_name='Test Supplier Inc.', company_id=1)
        
        # Create test data for GoodsQualityNote model
        GoodsQualityNote.objects.create(
            id=1,
            gqn_no='GQN2023-001',
            sys_date=date(2023, 1, 15),
            fin_year_id=2023,
            grr_no='GRR-ABC',
            gin_no='GIN-XYZ',
            po_no='PO-123',
            supplier=cls.supplier,
            challan_no='CH-456',
            challan_date=date(2023, 1, 10),
            company_id=1
        )
        GoodsQualityNote.objects.create(
            id=2,
            gqn_no='GQN2023-002',
            sys_date=date(2023, 2, 20),
            fin_year_id=2023,
            grr_no='GRR-DEF',
            gin_no='GIN-UVW',
            po_no='PO-456',
            supplier=cls.supplier,
            challan_no='CH-789',
            challan_date=date(2023, 2, 18),
            company_id=1
        )
        Supplier.objects.create(id=102, supplier_name='Another Supplier Ltd.', company_id=1)
        Supplier.objects.create(id=103, supplier_name='Foreign Supplier', company_id=2)


    def test_goodsqualitynote_creation(self):
        gqn = GoodsQualityNote.objects.get(id=1)
        self.assertEqual(gqn.gqn_no, 'GQN2023-001')
        self.assertEqual(gqn.sys_date, date(2023, 1, 15))
        self.assertEqual(gqn.supplier.supplier_name, 'Test Supplier Inc.')
        self.assertEqual(gqn.company_id, 1)

    def test_goodsqualitynote_str_method(self):
        gqn = GoodsQualityNote.objects.get(id=1)
        self.assertEqual(str(gqn), 'GQN2023-001 - Test Supplier Inc.')

    def test_supplier_creation(self):
        supplier = Supplier.objects.get(id=101)
        self.assertEqual(supplier.supplier_name, 'Test Supplier Inc.')
        self.assertEqual(supplier.company_id, 1)
        self.assertEqual(str(supplier), 'Test Supplier Inc.')

    def test_supplier_format_for_autocomplete(self):
        supplier = Supplier.objects.get(id=101)
        self.assertEqual(supplier.format_for_autocomplete(), 'Test Supplier Inc. [101]')

    def test_goodsqualitynote_manager_search_supplier_name_id(self):
        # Simulate ASP.NET's 'Name [ID]' parsing
        company_id = 1
        fin_year_id = 2023
        
        # Search by supplier ID (as if parsed from 'Name [ID]')
        qs = GoodsQualityNote.objects.search_and_filter('0', '101', company_id, fin_year_id)
        self.assertEqual(qs.count(), 2)
        self.assertTrue(all(g.supplier.id == 101 for g in qs))

    def test_goodsqualitynote_manager_search_supplier_name_partial(self):
        qs = GoodsQualityNote.objects.search_and_filter('0', 'test', 1, 2023)
        self.assertEqual(qs.count(), 2)
        qs = GoodsQualityNote.objects.search_and_filter('0', 'NonExistent', 1, 2023)
        self.assertEqual(qs.count(), 0)

    def test_goodsqualitynote_manager_search_gqn_no(self):
        qs = GoodsQualityNote.objects.search_and_filter('1', '001', 1, 2023)
        self.assertEqual(qs.count(), 1)
        self.assertEqual(qs.first().gqn_no, 'GQN2023-001')
        qs = GoodsQualityNote.objects.search_and_filter('1', '2023', 1, 2023) # Partial match
        self.assertEqual(qs.count(), 2)

    def test_goodsqualitynote_manager_search_grr_no(self):
        qs = GoodsQualityNote.objects.search_and_filter('2', 'ABC', 1, 2023)
        self.assertEqual(qs.count(), 1)
        self.assertEqual(qs.first().grr_no, 'GRR-ABC')

    def test_goodsqualitynote_manager_search_po_no(self):
        qs = GoodsQualityNote.objects.search_and_filter('3', '123', 1, 2023)
        self.assertEqual(qs.count(), 1)
        self.assertEqual(qs.first().po_no, 'PO-123')

    def test_goodsqualitynote_manager_filter_by_company_and_fin_year(self):
        # Create a GQN for a different company
        GoodsQualityNote.objects.create(
            id=3,
            gqn_no='GQN2023-003',
            sys_date=date(2023, 3, 1),
            fin_year_id=2023,
            supplier=self.supplier,
            company_id=2 # Different company
        )
        qs = GoodsQualityNote.objects.search_and_filter('0', '', 1, 2023)
        self.assertEqual(qs.count(), 2) # Only GQN for company 1
        qs = GoodsQualityNote.objects.search_and_filter('0', '', 2, 2023)
        self.assertEqual(qs.count(), 1) # Only GQN for company 2

class GoodsQualityNoteViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Set up shared test data
        cls.supplier1 = Supplier.objects.create(id=101, supplier_name='Test Supplier A', company_id=1)
        cls.supplier2 = Supplier.objects.create(id=102, supplier_name='Test Supplier B', company_id=1)
        GoodsQualityNote.objects.create(
            id=1, gqn_no='GQN001', sys_date=date(2023, 1, 1), fin_year_id=2023, supplier=cls.supplier1, company_id=1
        )
        GoodsQualityNote.objects.create(
            id=2, gqn_no='GQN002', sys_date=date(2023, 1, 2), fin_year_id=2023, supplier=cls.supplier2, company_id=1
        )
        # GQN for a different company to test filtering
        GoodsQualityNote.objects.create(
            id=3, gqn_no='GQN003', sys_date=date(2023, 1, 3), fin_year_id=2023, supplier=cls.supplier1, company_id=2
        )

    def setUp(self):
        self.client = Client()
        # Mock session data as ASP.NET code used Session["compid"] and Session["finyear"]
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2023
        session.save()

    def test_list_view(self):
        response = self.client.get(reverse('goodsqualitynote_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'goods_quality_notes/goodsqualitynote/list.html')
        self.assertIn('filter_form', response.context)

    def test_table_partial_view_no_filters(self):
        response = self.client.get(reverse('goodsqualitynote_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'goods_quality_notes/goodsqualitynote/_table.html')
        self.assertEqual(response.context['goodsqualitynotes'].count(), 2) # Only for compid=1

    def test_table_partial_view_with_gqn_filter(self):
        response = self.client.get(reverse('goodsqualitynote_table'), {'search_by': '1', 'search_term': 'GQN001'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['goodsqualitynotes'].count(), 1)
        self.assertEqual(response.context['goodsqualitynotes'].first().gqn_no, 'GQN001')

    def test_table_partial_view_with_supplier_filter(self):
        # Simulate search by supplier ID (as fun.getCode would extract it)
        response = self.client.get(reverse('goodsqualitynote_table'), {'search_by': '0', 'search_term': '101'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['goodsqualitynotes'].count(), 1)
        self.assertEqual(response.context['goodsqualitynotes'].first().supplier.supplier_name, 'Test Supplier A')

        # Simulate search by partial supplier name
        response = self.client.get(reverse('goodsqualitynote_table'), {'search_by': '0', 'search_term': 'Supplier B'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['goodsqualitynotes'].count(), 1)
        self.assertEqual(response.context['goodsqualitynotes'].first().supplier.supplier_name, 'Test Supplier B')


    def test_create_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('goodsqualitynote_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'goods_quality_notes/goodsqualitynote/_form.html')
        self.assertIn('form', response.context)

    def test_create_view_post_htmx_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'gqn_no': 'GQN003',
            'sys_date': '2023-03-01',
            'fin_year_id': 2023,
            'grr_no': 'GRR-NEW',
            'gin_no': 'GIN-NEW',
            'po_no': 'PO-NEW',
            'supplier': self.supplier1.pk, # Use the PK of an existing supplier
            'challan_no': 'CH-NEW',
            'challan_date': '2023-02-28',
            'company_id': 1
        }
        response = self.client.post(reverse('goodsqualitynote_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX No Content
        self.assertTrue(GoodsQualityNote.objects.filter(gqn_no='GQN003').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshGoodsQualityNoteList')

    def test_create_view_post_htmx_invalid(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'gqn_no': '', # Invalid data
            'sys_date': '2023-03-01',
            'fin_year_id': 2023,
            'supplier': self.supplier1.pk,
            'company_id': 1
        }
        response = self.client.post(reverse('goodsqualitynote_add'), data, **headers)
        self.assertEqual(response.status_code, 400) # HTMX Bad Request
        self.assertTemplateUsed(response, 'goods_quality_notes/goodsqualitynote/_form.html')
        self.assertIn('gqn_no', response.context['form'].errors)

    def test_update_view_get_htmx(self):
        obj = GoodsQualityNote.objects.get(id=1)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('goodsqualitynote_edit', args=[obj.id]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'goods_quality_notes/goodsqualitynote/_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance.gqn_no, 'GQN001')

    def test_update_view_post_htmx_success(self):
        obj = GoodsQualityNote.objects.get(id=1)
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'gqn_no': 'GQN001-Updated',
            'sys_date': '2023-01-01',
            'fin_year_id': 2023,
            'grr_no': 'GRR-OLD',
            'gin_no': 'GIN-OLD',
            'po_no': 'PO-OLD',
            'supplier': self.supplier1.pk,
            'challan_no': 'CH-OLD',
            'challan_date': '2023-01-01',
            'company_id': 1
        }
        response = self.client.post(reverse('goodsqualitynote_edit', args=[obj.id]), data, **headers)
        self.assertEqual(response.status_code, 204)
        obj.refresh_from_db()
        self.assertEqual(obj.gqn_no, 'GQN001-Updated')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshGoodsQualityNoteList')

    def test_delete_view_get_htmx(self):
        obj = GoodsQualityNote.objects.get(id=1)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('goodsqualitynote_delete', args=[obj.id]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'goods_quality_notes/goodsqualitynote/_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'].gqn_no, 'GQN001')

    def test_delete_view_post_htmx_success(self):
        obj_to_delete = GoodsQualityNote.objects.get(id=1)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('goodsqualitynote_delete', args=[obj_to_delete.id]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertFalse(GoodsQualityNote.objects.filter(id=1).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshGoodsQualityNoteList')

    def test_supplier_autocomplete_view(self):
        response = self.client.get(reverse('supplier_autocomplete'), {'term': 'Test Supplier'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'goods_quality_notes/supplier/_autocomplete_results.html')
        # Check if the response contains expected suggestions
        self.assertContains(response, 'Test Supplier A [101]')
        self.assertContains(response, 'Test Supplier B [102]')
        self.assertNotContains(response, 'Foreign Supplier') # Should be filtered by company_id

        # Test with a specific ID-like search
        response = self.client.get(reverse('supplier_autocomplete'), {'term': '101'})
        self.assertContains(response, 'Test Supplier A [101]')
        self.assertNotContains(response, 'Test Supplier B [102]')

        # Test no results
        response = self.client.get(reverse('supplier_autocomplete'), {'term': 'NoMatch'})
        self.assertContains(response, 'No suggestions')

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for Dynamic Interactions:**
    *   The main list view (`goodsqualitynote/list.html`) uses `hx-get` on `id="gqn_table-container"` with `hx-trigger="load, refreshGoodsQualityNoteList from:body"` to initially load and dynamically refresh the DataTables content.
    *   The search form uses `hx-get` and `hx-target` to submit filters and update only the table.
    *   Buttons for "Add New GQN", "Edit", and "Delete" use `hx-get` to fetch their respective forms into a modal (`#modalContent`).
    *   Form submissions (Create/Update/Delete) use `hx-post` and `hx-swap="none"` with `hx-on::after-request` to close the modal on success (HTTP 204 No Content) and trigger `refreshGoodsQualityNoteList` to update the main list.
    *   The `supplier/autocomplete/` endpoint serves HTML partials with suggestions for the supplier input, which HTMX swaps into a dropdown.
*   **Alpine.js for UI State Management:**
    *   The `list.html` uses `x-data` and `_=` attributes (from Alpine.js and htmx-ext-alpine) to manage the visibility of the two search input fields (`txtSupplier` and `Txtfield`) based on the `DropDownList1` selection, mimicking the original ASP.NET `SelectedIndexChanged` logic.
    *   Alpine.js is also used to control the visibility of the modal (`.is-active` class toggling).
    *   The `selectAutocompleteSuggestion` Alpine.js function handles populating the search input and triggering the table refresh after a suggestion is clicked.
*   **DataTables for List Views:**
    *   The `goodsqualitynote/_table.html` partial initializes DataTables on the loaded table. It dynamically sets `pageLength` based on Django's `paginate_by` for consistency. The primary search and filtering are handled server-side via HTMX, allowing DataTables to focus on client-side presentation (sorting, secondary search if enabled).
*   **No Additional JavaScript:** All dynamic interactions are managed purely through HTMX and Alpine.js, eliminating the need for custom, complex JavaScript files.
*   **DRY Template Inheritance:** All templates (`list.html`) extend `core/base.html`, ensuring a consistent layout and central management of CDN links (e.g., for DataTables, HTMX, Alpine.js, Tailwind CSS).

### Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating the specified ASP.NET module to Django. By following these steps, your organization will benefit from:

*   **Improved Maintainability:** Django's structured approach, fat models, and thin views lead to cleaner, more organized code.
*   **Enhanced Performance:** HTMX and Alpine.js provide a modern, snappy user experience without full page reloads, akin to single-page applications but with the simplicity of traditional server-rendered apps.
*   **Scalability:** Django's robust ORM and architecture support application growth and larger datasets.
*   **Reduced Development Time:** The systematic, automation-focused approach minimizes manual code rewriting, accelerating the migration process.
*   **Simplified Frontend:** Relying solely on HTMX and Alpine.js removes the complexity of traditional JavaScript frameworks, reducing development effort and potential bugs.
*   **Testability:** Comprehensive unit and integration tests ensure code quality and prevent regressions during ongoing development.

This plan is designed to be executed through conversational AI guidance, where each step can be prompted, analyzed, and the generated code reviewed, leading to a systematic and efficient modernization process.