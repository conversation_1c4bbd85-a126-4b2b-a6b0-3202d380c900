## ASP.NET to Django Conversion Script: `ScrapMaterial_Report`

This document outlines the modernization plan for transitioning the ASP.NET `ScrapMaterial_Report.aspx` application to a modern Django solution. Our approach emphasizes AI-assisted automation, leveraging Django 5.0+, HTMX, Alpine.js, and DataTables to deliver a robust, maintainable, and highly responsive web application. The core principle is to use 'Fat Models, Thin Views', ensuring business logic resides solely within models, while views remain concise and focused on orchestrating data presentation.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

The ASP.NET code interacts with several database tables to generate the "Scrap Report". We'll infer their structure and relationships based on the SQL queries and data access patterns.

*   **Primary Report Data Source:** `tblQC_Scrapregister`
    *   Columns: `ScrapNo` (NVARCHAR), `SysDate` (DATETIME), `CompId` (INT), `FinYearId` (INT), `SessionId` (INT, likely `EmpId`), `MRQNId` (INT, FK to `tblQc_MaterialReturnQuality_Master`).
*   **Related Tables:**
    *   `tblQc_MaterialReturnQuality_Master`:
        *   Columns: `Id` (PK, INT), `SysDate` (DATETIME, aliased as `MRQNDATE`), `MRNNo` (NVARCHAR), `MRNId` (INT, FK to `tblInv_MaterialReturn_Master`), `MRQNNo` (NVARCHAR), `FinYearId` (INT), `SessionId` (INT).
    *   `tblInv_MaterialReturn_Master`:
        *   Columns: `Id` (PK, INT), `SysDate` (DATETIME, aliased as `MRNDate`).
    *   `tblFinancial_master`:
        *   Columns: `FinYearId` (PK, INT), `FinYear` (NVARCHAR), `CompId` (INT).
    *   `tblHR_OfficeStaff`:
        *   Columns: `EmpId` (PK, INT), `EmployeeName` (NVARCHAR), `Title` (NVARCHAR).

### Step 2: Identify Backend Functionality

The ASP.NET `ScrapMaterial_Report` page primarily serves as a reporting interface, focused on **read** operations with filtering and pagination.

*   **Read (Reporting & Listing):** Displays a list of scrap material records with details pulled from multiple related tables.
    *   **Filtering:** Allows searching by `MRQN No`, `MRN No`, `Employee Name`, or `Scrap No` using a dropdown and a textbox.
    *   **Pagination:** Supports navigating through large datasets.
    *   **Autocomplete:** Provides suggestions for employee names.
*   **Navigation to Detail:** The "Select" action on each row redirects to a `ScrapMaterial_Report_Details.aspx` page, passing key identifiers (MRNNo, ScrapNo, FYId, MRQNNo). This will be mapped to a Django detail view.
*   **No Direct CRUD on this page:** The provided ASP.NET code does not show explicit Create, Update, or Delete operations for the scrap records *on this report page*. However, to adhere to the instruction of providing CRUD templates, a generic example for a hypothetical `ScrapReport` object will be provided. The primary focus of the generated code will be the report listing and filtering.

### Step 3: Infer UI Components

The ASP.NET controls will be translated into standard HTML forms and table structures, enhanced with HTMX, Alpine.js, and DataTables for a modern user experience.

*   **Search/Filter Section:**
    *   `drpfield` (DropDownList): Replaced by a `<select>` element for filter type selection.
    *   `txtMqnNo` (TextBox): A `<input type="text">` for searching by number-based criteria (MRQN, MRN, Scrap).
    *   `txtEmpName` (TextBox with `AutoCompleteExtender`): A `<input type="text">` for employee name search, with an HTMX-powered autocomplete fetching suggestions from a Django endpoint.
    *   `Button1` (Button): A `<button>` to trigger the search, using HTMX to dynamically refresh the results table.
*   **Data Display (GridView1):** Replaced by a `<table>` element initialized with DataTables for client-side sorting, searching, and pagination.
    *   Columns: SN, FinYear, Scrap No, Date (Scrap SysDate), MRQN No, Date (MRQN Date), MRN No, Date (MRN Date), Gen. By.
    *   "Select" LinkButton: Replaced by an HTMX-enabled button that triggers a redirect to the detail page.

### Step 4: Generate Django Code

We will create a Django application, e.g., `qualitycontrol`, to house the modernized code.

#### 4.1 Models (`qualitycontrol/models.py`)

We'll define Django models for the identified database tables, mapping them to the existing schema using `managed = False`. The complex data aggregation logic from the ASP.NET `loadData` method will be encapsulated in a custom manager for `ScrapRegister`, adhering to the 'Fat Model' principle.

```python
from django.db import models
from django.db.models import F, Value, CharField
from django.db.models.functions import Concat

class FinancialYear(models.Model):
    finyearid = models.IntegerField(db_column='FinYearId', primary_key=True)
    finyear = models.CharField(db_column='FinYear', max_length=50, blank=True, null=True)
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.finyear or 'N/A'

class OfficeStaff(models.Model):
    empid = models.IntegerField(db_column='EmpId', primary_key=True)
    employeename = models.CharField(db_column='EmployeeName', max_length=250, blank=True, null=True)
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title}. {self.employeename}" if self.title else self.employeename or 'N/A'
    
    @property
    def full_name(self):
        return f"{self.title}. {self.employeename}" if self.title else self.employeename

class MaterialReturnMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sysdate = models.DateTimeField(db_column='SysDate', blank=True, null=True) # MRNDate

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialReturn_Master'
        verbose_name = 'Material Return'
        verbose_name_plural = 'Material Returns'

    def __str__(self):
        return f"MRN {self.id}"

class MaterialReturnQualityMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sysdate = models.DateTimeField(db_column='SysDate', blank=True, null=True) # MRQNDATE
    mrnno = models.CharField(db_column='MRNNo', max_length=50, blank=True, null=True)
    mrn_master = models.ForeignKey(MaterialReturnMaster, models.DO_NOTHING, db_column='MRNId', blank=True, null=True)
    mrqnno = models.CharField(db_column='MRQNNo', max_length=50, blank=True, null=True)
    finyearid = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    sessionid = models.IntegerField(db_column='SessionId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblQc_MaterialReturnQuality_Master'
        verbose_name = 'Material Return Quality'
        verbose_name_plural = 'Material Return Qualities'

    def __str__(self):
        return self.mrqnno or 'N/A'


class ScrapReportManager(models.Manager):
    def get_scrap_report_data(self, comp_id, fin_year_id, search_field, search_value):
        """
        Mimics the complex data loading logic from ASP.NET's loadData method.
        This method aggregates data from multiple tables.
        """
        # Start with ScrapRegister, as it's the primary table in the ASP.NET code
        queryset = self.get_queryset().filter(
            compid=comp_id,
            finyearid__lte=fin_year_id # ASP.NET uses finyearid <= FinYearId
        ).select_related(
            'mrqn_master', # Link to MaterialReturnQualityMaster
            'financial_year', # Link to FinancialYear
            'generated_by_staff' # Link to OfficeStaff
        ).annotate(
            finyear_display=F('financial_year__finyear'),
            mrqn_date=F('mrqn_master__sysdate'),
            mrqn_no=F('mrqn_master__mrqnno'),
            mrn_no=F('mrqn_master__mrnno'),
            # Join to MaterialReturnMaster for MRNDate
            mrn_date=F('mrqn_master__mrn_master__sysdate'),
            gen_by=Concat(F('generated_by_staff__title'), Value('. '), F('generated_by_staff__employeename'), output_field=CharField())
        ).order_by('-sysdate') # Order by SysDate (Scrap Date) for consistency

        # Apply dynamic filters based on search_field and search_value
        if search_value:
            if search_field == '0': # MRQN No
                queryset = queryset.filter(mrqn_master__mrqnno__icontains=search_value)
            elif search_field == '1': # MRN No
                queryset = queryset.filter(mrqn_master__mrnno__icontains=search_value)
            elif search_field == '2': # Employee Name (SessionId in ScrapRegister -> EmpId in OfficeStaff)
                # Note: ASP.NET uses fun.getCode(txtEmpName.Text) which likely gets EmpId from name
                # Here we assume search_value is part of employee name. If it's EmpId, adjust filter.
                queryset = queryset.filter(generated_by_staff__employeename__icontains=search_value)
            elif search_field == '3': # Scrap No
                queryset = queryset.filter(scrapno__icontains=search_value)
        
        # Select specific fields that match the ASP.NET GridView output structure
        return queryset.values(
            'scrapno',
            'sysdate', # Scrap SysDate
            'finyearid', # From ScrapRegister
            'finyear_display',
            'mrqn_date',
            'mrqn_no',
            'mrn_no',
            'mrn_date',
            'gen_by'
        )


class ScrapRegister(models.Model):
    # Id field might be missing in original 'fun.select' statement, assuming auto-increment PK
    # If not present, Django will add 'id' by default.
    id = models.AutoField(primary_key=True) # Assuming an auto-increment primary key
    scrapno = models.CharField(db_column='ScrapNo', max_length=50, blank=True, null=True)
    sysdate = models.DateTimeField(db_column='SysDate', blank=True, null=True) # Scrap date
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)
    finyearid = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    sessionid = models.IntegerField(db_column='SessionId', blank=True, null=True) # This is EmpId
    mrqnid = models.IntegerField(db_column='MRQNId', blank=True, null=True) # This is Id from tblQc_MaterialReturnQuality_Master

    # Define explicit FK relationships to make select_related work
    mrqn_master = models.ForeignKey(MaterialReturnQualityMaster, models.DO_NOTHING, db_column='MRQNId', related_name='scrap_registers_mrqn', blank=True, null=True)
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYearId', related_name='scrap_registers_finyear', blank=True, null=True)
    generated_by_staff = models.ForeignKey(OfficeStaff, models.DO_NOTHING, db_column='SessionId', related_name='scrap_registers_generated_by', blank=True, null=True)

    objects = ScrapReportManager() # Custom manager for report logic

    class Meta:
        managed = False
        db_table = 'tblQC_Scrapregister'
        verbose_name = 'Scrap Register'
        verbose_name_plural = 'Scrap Registers'

    def __str__(self):
        return self.scrapno or f"Scrap ID: {self.id}"

    # Business logic methods related to a single scrap record can go here.
    # For example, a method to format dates if not handled in the manager
    def format_date_dmy(self, date_field):
        """ Mimics fun.FromDateDMY, formats a date to DD/MM/YYYY """
        if date_field:
            return date_field.strftime('%d/%m/%Y')
        return 'N/A'

```

#### 4.2 Forms (`qualitycontrol/forms.py`)

A form for search filters and a simple ModelForm for CRUD operations on a `ScrapReport` object (if it were editable).

```python
from django import forms
from .models import ScrapRegister # We will define a generic ScrapReport for CRUD later if needed.

# Form for search/filter options
class ScrapReportSearchForm(forms.Form):
    SEARCH_CHOICES = [
        ('0', 'MRQN No'),
        ('1', 'MRN No'),
        ('2', 'Employee Name'),
        ('3', 'Scrap No'),
    ]
    field_type = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'id': 'drpfield', # Matching original ID for Alpine.js/HTMX
            'hx-post': 'hx-post', # Mark for HTMX handling
            'hx-trigger': 'change',
            'hx-target': '#search-inputs-container',
            'hx-swap': 'innerHTML',
        })
    )
    
    search_value_num = forms.CharField(
        max_length=250,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter number',
            'id': 'txtMqnNo', # Matching original ID
        })
    )

    search_value_emp = forms.CharField(
        max_length=250,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter Employee Name',
            'id': 'txtEmpName', # Matching original ID
            'hx-get': '/qualitycontrol/autocomplete-employee/', # HTMX for autocomplete
            'hx-trigger': 'keyup changed delay:300ms, search',
            'hx-target': '#autocomplete-suggestions',
            'hx-swap': 'innerHTML',
            'hx-indicator': '#autocomplete-spinner',
            'autocomplete': 'off', # Prevent browser autocomplete
        })
    )

# --- Generic CRUD Form (for demonstration of CRUD templates) ---
# This form assumes ScrapRegister is editable for demo purposes.
class ScrapRegisterForm(forms.ModelForm):
    class Meta:
        model = ScrapRegister
        fields = ['scrapno', 'sysdate', 'mrqn_master', 'financial_year', 'generated_by_staff']
        widgets = {
            'scrapno': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sysdate': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            # For ForeignKey fields, adjust widget if you want dropdowns or other selectors
            'mrqn_master': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'financial_year': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'generated_by_staff': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }

```

#### 4.3 Views (`qualitycontrol/views.py`)

Views will be thin, primarily delegating data retrieval to the model manager and handling HTMX responses. The core `ScrapReportListView` will manage the report display and search.

```python
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy, reverse
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db.models import Q
from django.conf import settings # Assuming settings.COMP_ID and settings.FIN_YEAR_ID exist

from .models import ScrapRegister, OfficeStaff
from .forms import ScrapReportSearchForm, ScrapRegisterForm

# --- Views for Scrap Report ---

class ScrapReportListView(ListView):
    model = ScrapRegister
    template_name = 'qualitycontrol/scrapreport/list.html'
    context_object_name = 'scrap_reports'
    paginate_by = 15 # Matching ASP.NET PageSize

    def get_queryset(self):
        # Assume CompId and FinYearId are from session/settings as in ASP.NET
        # For simplicity, hardcode if session not implemented yet in Django layer.
        comp_id = self.request.session.get('compid', settings.DEFAULT_COMP_ID)
        fin_year_id = self.request.session.get('finyear', settings.DEFAULT_FIN_YEAR_ID)

        # Get search parameters from GET request (or POST for initial load/search button)
        search_field = self.request.GET.get('field_type', self.request.POST.get('field_type', '0'))
        search_value = self.request.GET.get('search_value', self.request.POST.get('search_value', ''))

        # Delegate complex query logic to the model manager
        queryset = ScrapRegister.objects.get_scrap_report_data(
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            search_field=search_field,
            search_value=search_value
        )
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Populate search form with current values for persistence
        search_field = self.request.GET.get('field_type', self.request.POST.get('field_type', '0'))
        search_value = self.request.GET.get('search_value', self.request.POST.get('search_value', ''))
        
        context['search_form'] = ScrapReportSearchForm(initial={
            'field_type': search_field,
            'search_value_num': search_value if search_field in ['0', '1', '3'] else '',
            'search_value_emp': search_value if search_field == '2' else '',
        })
        context['current_search_field'] = search_field # For Alpine.js to control visibility
        context['current_search_value'] = search_value # To pre-fill textboxes

        return context

class ScrapReportTablePartialView(ScrapReportListView):
    """
    Returns only the table HTML for HTMX updates.
    Inherits get_queryset and other logic from ScrapReportListView.
    """
    template_name = 'qualitycontrol/scrapreport/_scrapreport_table.html'

class ScrapReportSearchFormPartialView(ListView):
    """
    Returns only the search form HTML for HTMX updates (e.g., when dropdown changes).
    """
    model = ScrapRegister # Dummy model, not actually querying here
    template_name = 'qualitycontrol/scrapreport/_search_form.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        search_field = self.request.POST.get('field_type', '0') # Only POST for this partial
        context['search_form'] = ScrapReportSearchForm(initial={'field_type': search_field})
        context['current_search_field'] = search_field
        return context

class ScrapReportDetailView(DetailView):
    model = ScrapRegister
    template_name = 'qualitycontrol/scrapreport/detail.html'
    context_object_name = 'scrap_report'
    
    # Custom get_object to find by the combination of fields used in redirect (MRNNo, ScrapNo, FYId, MRQNNo)
    # This might require adjusting the primary key or having a unique constraint,
    # or finding a way to map these back to the single 'id' of ScrapRegister.
    # For now, we'll use the 'id' (pk) that would be passed if the list view returned it.
    # If the original ASP.NET redirect parameters are truly the lookup, then a custom manager method
    # on ScrapRegister to `get_by_complex_keys` would be ideal.
    # For demonstration, we assume `pk` is the ScrapRegister.id
    def get_object(self, queryset=None):
        pk = self.kwargs.get(self.pk_url_kwarg)
        # In a real scenario, you'd map the ASP.NET redirect params to a single object:
        # mrn_no = self.request.GET.get('MRNNo')
        # scrap_no = self.request.GET.get('ScrapNo')
        # fy_id = self.request.GET.get('FYId')
        # mrqn_no = self.request.GET.get('MRQNNo')
        # return ScrapRegister.objects.get_by_report_params(mrn_no, scrap_no, fy_id, mrqn_no)
        return ScrapRegister.objects.get(id=pk) # Simplified for demo

# Autocomplete endpoint for employee names
def autocomplete_employee(request):
    query = request.GET.get('q', '')
    comp_id = request.session.get('compid', settings.DEFAULT_COMP_ID) # Adjust if not in session
    
    if query:
        employees = OfficeStaff.objects.filter(
            compid=comp_id,
            employeename__icontains=query
        ).annotate(
            full_display_name=Concat(F('title'), Value('. '), F('employeename'), Value(' ['), F('empid'), Value(']'))
        )[:10] # Limit suggestions as in ASP.NET
        suggestions = [{'value': emp.full_display_name, 'text': emp.full_display_name} for emp in employees]
    else:
        suggestions = []
    return JsonResponse({'suggestions': suggestions})


# --- Generic CRUD Views (for demonstration of CRUD templates as per prompt) ---
# These views would be for a different module if ScrapRegister is truly read-only here.
# For simplicity, they are provided to fulfill the template requirements.

class ScrapRegisterCreateView(CreateView):
    model = ScrapRegister
    form_class = ScrapRegisterForm
    template_name = 'qualitycontrol/scrapreport/form.html'
    success_url = reverse_lazy('scrapregister_list') # Redirect to general list, not report

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Scrap Register added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshScrapRegisterList'
                }
            )
        return response

class ScrapRegisterUpdateView(UpdateView):
    model = ScrapRegister
    form_class = ScrapRegisterForm
    template_name = 'qualitycontrol/scrapreport/form.html'
    success_url = reverse_lazy('scrapregister_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Scrap Register updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshScrapRegisterList'
                }
            )
        return response

class ScrapRegisterDeleteView(DeleteView):
    model = ScrapRegister
    template_name = 'qualitycontrol/scrapreport/confirm_delete.html'
    success_url = reverse_lazy('scrapregister_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Scrap Register deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshScrapRegisterList'
                }
            )
        return response

```

#### 4.4 Templates (`qualitycontrol/templates/qualitycontrol/scrapreport/`)

*   `list.html`: The main report page, containing the search form and a container for the HTMX-loaded table.
*   `_search_form.html`: Partial template for the search controls, dynamically swapped by HTMX.
*   `_scrapreport_table.html`: Partial template containing the DataTables-enabled table of results.
*   `detail.html`: Page for displaying individual scrap report details (redirect target).
*   `form.html`: Generic form for adding/editing a `ScrapRegister` (used in CRUD demo).
*   `confirm_delete.html`: Generic delete confirmation (used in CRUD demo).

**`list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6 text-gray-800">Scrap Report</h2>

    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-700 mb-4">Search Filters</h3>
        <form hx-get="{% url 'scrapreport_table_partial' %}"
              hx-target="#scrapreport-table-container"
              hx-swap="innerHTML"
              hx-indicator="#loading-indicator"
              _="on submit if #txtEmpName is visible set #search_value.value to #txtEmpName.value else set #search_value.value to #txtMqnNo.value"
              class="space-y-4">
            {% csrf_token %}
            <input type="hidden" name="search_value" id="search_value">
            
            <div id="search-inputs-container" hx-post="{% url 'scrapreport_search_form_partial' %}" hx-swap="innerHTML">
                {# This will be dynamically updated by HTMX on field_type change #}
                {# Initial load will render it through the list view's context #}
                {% include 'qualitycontrol/scrapreport/_search_form.html' with search_form=search_form current_search_field=current_search_field current_search_value=current_search_value %}
            </div>

            <div class="flex items-center justify-end">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                    Search
                </button>
            </div>
        </form>
    </div>

    <div id="loading-indicator" class="htmx-indicator flex items-center justify-center py-4">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="ml-3 text-gray-600">Loading Report Data...</p>
    </div>

    <div id="scrapreport-table-container"
         hx-trigger="load, refreshScrapReportList from:body, submit from:#search-form"
         hx-get="{% url 'scrapreport_table_partial' %}{% if request.GET %}{% raw %}?{% endraw %}{{ request.GET.urlencode }}{% endif %}"
         hx-swap="innerHTML">
        {# Initial table content will be loaded here via HTMX. #}
        {# The loading indicator above will show while this loads. #}
    </div>

    {# MODAL FOR CRUD OPERATIONS DEMO #}
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full">
            {# Content loaded via HTMX #}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('reportPage', () => ({
            field_type: '{{ current_search_field }}',
            txtMqnNo_visible: {% if current_search_field in '013' %}true{% else %}false{% endif %},
            txtEmpName_visible: {% if current_search_field == '2' %}true{% else %}false{% endif %},
            
            init() {
                // Ensure initial visibility is correct when page loads
                this.updateVisibility();
            },

            updateVisibility() {
                this.txtMqnNo_visible = (this.field_type === '0' || this.field_type === '1' || this.field_type === '3');
                this.txtEmpName_visible = (this.field_type === '2');
            },
        }));
    });

    document.body.addEventListener('htmx:afterSwap', function(evt) {
        // Re-initialize DataTables after HTMX swaps in new table content
        if (evt.detail.target.id === 'scrapreport-table-container') {
            $('#scrapreportTable').DataTable({
                "pageLength": 15, // Matching ASP.NET
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true, // Destroy existing instance before re-initialization
                "processing": true,
                "serverSide": false, // DataTables client-side
                "pagingType": "full_numbers",
            });
        }

        // Handle autocomplete suggestions display
        if (evt.detail.target.id === 'autocomplete-suggestions') {
            const suggestionsDiv = document.getElementById('autocomplete-suggestions');
            if (suggestionsDiv.children.length > 0) {
                suggestionsDiv.classList.remove('hidden');
            } else {
                suggestionsDiv.classList.add('hidden');
            }
        }
    });

    // Close autocomplete suggestions when clicking outside
    document.addEventListener('click', function(event) {
        const txtEmpName = document.getElementById('txtEmpName');
        const suggestionsDiv = document.getElementById('autocomplete-suggestions');
        if (suggestionsDiv && !suggestionsDiv.contains(event.target) && event.target !== txtEmpName) {
            suggestionsDiv.classList.add('hidden');
        }
    });

    // Handle selection from autocomplete
    document.addEventListener('click', function(event) {
        if (event.target.closest('.autocomplete-suggestion')) {
            const selectedText = event.target.dataset.value;
            const txtEmpName = document.getElementById('txtEmpName');
            if (txtEmpName) {
                txtEmpName.value = selectedText.split(' [')[0]; // Set only name, not ID in textbox
                // Trigger HTMX search or update hidden input if needed
                document.getElementById('autocomplete-suggestions').classList.add('hidden');
            }
        }
    });

</script>
{% endblock %}
```

**`_search_form.html`** (Partial)

```html
<div x-data="reportPage" x-init="updateVisibility()">
    <div class="flex flex-wrap items-center gap-4">
        <label for="drpfield" class="block text-sm font-medium text-gray-700">Search By:</label>
        {{ search_form.field_type }}

        <div class="relative flex-grow min-w-[250px]">
            <div x-show="txtMqnNo_visible" style="display: none;">
                <label for="txtMqnNo" class="sr-only">Search Value</label>
                {{ search_form.search_value_num }}
            </div>
            <div x-show="txtEmpName_visible" style="display: none;">
                <label for="txtEmpName" class="sr-only">Employee Name</label>
                {{ search_form.search_value_emp }}
                <div id="autocomplete-suggestions" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg mt-1 w-full max-h-60 overflow-y-auto hidden"></div>
                <img id="autocomplete-spinner" class="htmx-indicator absolute right-3 top-1/2 -translate-y-1/2 w-5 h-5" src="/static/img/spinner.svg" alt="Loading...">
            </div>
        </div>
    </div>
</div>

<script>
    // Set initial value for search_value hidden input based on visible field
    document.addEventListener('DOMContentLoaded', function() {
        const fieldTypeSelect = document.getElementById('drpfield');
        const txtMqnNo = document.getElementById('txtMqnNo');
        const txtEmpName = document.getElementById('txtEmpName');
        const searchValueInput = document.getElementById('search_value');

        function updateSearchValue() {
            if (fieldTypeSelect.value === '2') {
                searchValueInput.value = txtEmpName.value;
            } else {
                searchValueInput.value = txtMqnNo.value;
            }
        }

        // Set initial value
        updateSearchValue();

        // Update when text fields change
        txtMqnNo.addEventListener('input', updateSearchValue);
        txtEmpName.addEventListener('input', updateSearchValue);

        // This script runs on HTMX swap too, so we need to ensure Alpine re-initializes
        // The x-init on the parent div handles Alpine re-initialization.
    });
</script>
```

**`_scrapreport_table.html`** (Partial)

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-md p-4">
    <table id="scrapreportTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">FinYear</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Scrap No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Scrap Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MRQN No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MRQN Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MRN No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MRN Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Generated By</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for report in scrap_reports %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ report.finyear_display|default:'N/A' }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ report.scrapno|default:'N/A' }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ report.sysdate|date:"d/m/Y"|default:'N/A' }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ report.mrqn_no|default:'N/A' }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ report.mrqn_date|date:"d/m/Y"|default:'N/A' }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ report.mrn_no|default:'N/A' }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ report.mrn_date|date:"d/m/Y"|default:'N/A' }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-left">{{ report.gen_by|default:'N/A' }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <a href="{% url 'scrapreport_detail' pk=report.id %}"
                       class="text-blue-600 hover:text-blue-900">
                        Select
                    </a>
                    {# Optional: Generic CRUD buttons if this table were editable #}
                    <button
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                        hx-get="{% url 'scrapregister_edit' report.id %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                        hx-get="{% url 'scrapregister_delete' report.id %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="10" class="py-3 px-4 text-center text-gray-500">
                    <p class="font-bold text-lg text-maroon-600">No data to display !</p>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // DataTables initialization
    // This script will run every time the partial is swapped in by HTMX.
    // The `destroy: true` option ensures it properly re-initializes.
    // Ensure jQuery and DataTables libraries are loaded in base.html
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#scrapreportTable')) {
            $('#scrapreportTable').DataTable({
                "pageLength": 15,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true, // Crucial for HTMX re-initialization
                "processing": true,
                "serverSide": false, // DataTables client-side processing
                "pagingType": "full_numbers",
                "columnDefs": [
                    { "orderable": false, "targets": [0, 9] } // Disable sorting for SN and Actions
                ]
            });
        }
    });

    // Populate autocomplete suggestions
    htmx.onLoad(function(elt) {
        if (elt.id === 'autocomplete-suggestions') {
            const suggestions = JSON.parse(elt.textContent || '{"suggestions": []}').suggestions;
            let html = '';
            if (suggestions.length > 0) {
                suggestions.forEach(item => {
                    html += `<div class="p-2 cursor-pointer hover:bg-gray-100 autocomplete-suggestion" data-value="${item.value}">${item.text}</div>`;
                });
            } else {
                html = '<div class="p-2 text-gray-500">No suggestions</div>';
            }
            elt.innerHTML = html;
            elt.classList.remove('hidden');
        }
    });

</script>
```

**`detail.html`** (For the "Select" action, a simple detail view)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6">
        <h2 class="text-2xl font-bold mb-6 text-gray-800">Scrap Report Details: {{ scrap_report.scrapno }}</h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-gray-700">
            <div>
                <p><strong class="font-semibold">Scrap No:</strong> {{ scrap_report.scrapno }}</p>
                <p><strong class="font-semibold">Scrap Date:</strong> {{ scrap_report.sysdate|date:"d/m/Y" }}</p>
                <p><strong class="font-semibold">Financial Year:</strong> {{ scrap_report.financial_year.finyear }}</p>
            </div>
            <div>
                <p><strong class="font-semibold">MRQN No:</strong> {{ scrap_report.mrqn_master.mrqnno }}</p>
                <p><strong class="font-semibold">MRQN Date:</strong> {{ scrap_report.mrqn_master.sysdate|date:"d/m/Y" }}</p>
                <p><strong class="font-semibold">MRN No:</strong> {{ scrap_report.mrqn_master.mrnno }}</p>
                <p><strong class="font-semibold">MRN Date:</strong> {{ scrap_report.mrqn_master.mrn_master.sysdate|date:"d/m/Y" }}</p>
                <p><strong class="font-semibold">Generated By:</strong> {{ scrap_report.generated_by_staff.full_name }}</p>
            </div>
        </div>

        <div class="mt-8">
            <a href="{% url 'scrapreport_list' %}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-md">
                Back to Report
            </a>
        </div>
    </div>
</div>
{% endblock %}
```

**`form.html`** (Generic form for CRUD demo)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Scrap Register</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-ext="json-enc" hx-on::after-request="if(event.detail.successful) { htmx.trigger(document.body, 'refreshScrapRegisterList'); }" >
        {% csrf_token %}

        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`confirm_delete.html`** (Generic delete confirmation)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete this Scrap Register: <strong>{{ object.scrapno }}</strong>?</p>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`qualitycontrol/urls.py`)

Define URL patterns for the report, partials, and the generic CRUD actions.

```python
from django.urls import path
from .views import (
    ScrapReportListView,
    ScrapReportTablePartialView,
    ScrapReportSearchFormPartialView,
    ScrapReportDetailView,
    autocomplete_employee,
    ScrapRegisterCreateView,
    ScrapRegisterUpdateView,
    ScrapRegisterDeleteView
)

urlpatterns = [
    # Scrap Report URLs
    path('scrap-report/', ScrapReportListView.as_view(), name='scrapreport_list'),
    path('scrap-report/table/', ScrapReportTablePartialView.as_view(), name='scrapreport_table_partial'),
    path('scrap-report/search-form-partial/', ScrapReportSearchFormPartialView.as_view(), name='scrapreport_search_form_partial'),
    path('scrap-report/detail/<int:pk>/', ScrapReportDetailView.as_view(), name='scrapreport_detail'),
    path('autocomplete-employee/', autocomplete_employee, name='autocomplete_employee'),

    # Generic CRUD URLs for ScrapRegister (for demonstration of templates)
    path('scrapregister/add/', ScrapRegisterCreateView.as_view(), name='scrapregister_add'),
    path('scrapregister/edit/<int:pk>/', ScrapRegisterUpdateView.as_view(), name='scrapregister_edit'),
    path('scrapregister/delete/<int:pk>/', ScrapRegisterDeleteView.as_view(), name='scrapregister_delete'),
]

```

#### 4.6 Tests (`qualitycontrol/tests.py`)

Comprehensive tests for models and views. This includes unit tests for model methods and integration tests for all view interactions, particularly focusing on HTMX.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import timedelta
from unittest.mock import patch, MagicMock

from .models import (
    FinancialYear,
    OfficeStaff,
    MaterialReturnMaster,
    MaterialReturnQualityMaster,
    ScrapRegister
)

# Mock settings for DEFAULT_COMP_ID and DEFAULT_FIN_YEAR_ID
# In a real project, these would be in settings.py or configured via test settings.
@patch('django.conf.settings.DEFAULT_COMP_ID', 1)
@patch('django.conf.settings.DEFAULT_FIN_YEAR_ID', 2023)
class ModelTestSetup(TestCase):
    """Base class to set up common test data for models and views."""
    @classmethod
    def setUpTestData(cls):
        cls.comp_id = 1
        cls.fin_year_id = 2023

        cls.financial_year = FinancialYear.objects.create(
            finyearid=cls.fin_year_id,
            finyear='2023-2024',
            compid=cls.comp_id
        )
        cls.office_staff = OfficeStaff.objects.create(
            empid=101,
            employeename='John Doe',
            title='Mr',
            compid=cls.comp_id
        )
        cls.office_staff_2 = OfficeStaff.objects.create(
            empid=102,
            employeename='Jane Smith',
            title='Ms',
            compid=cls.comp_id
        )
        cls.mrn_master = MaterialReturnMaster.objects.create(
            id=201,
            sysdate=timezone.now() - timedelta(days=5)
        )
        cls.mrqn_master = MaterialReturnQualityMaster.objects.create(
            id=301,
            sysdate=timezone.now() - timedelta(days=4),
            mrnno='MRN-001',
            mrn_master=cls.mrn_master,
            mrqnno='MRQN-001',
            finyearid=cls.fin_year_id,
            sessionid=cls.office_staff.empid
        )
        cls.scrap_register = ScrapRegister.objects.create(
            id=1,
            scrapno='SCRAP-001',
            sysdate=timezone.now(),
            compid=cls.comp_id,
            finyearid=cls.fin_year_id,
            sessionid=cls.office_staff.empid,
            mrqnid=cls.mrqn_master.id
        )
        # Create another scrap register for testing filters
        cls.mrn_master_2 = MaterialReturnMaster.objects.create(id=202, sysdate=timezone.now() - timedelta(days=7))
        cls.mrqn_master_2 = MaterialReturnQualityMaster.objects.create(id=302, sysdate=timezone.now() - timedelta(days=6), mrnno='MRN-002', mrn_master=cls.mrn_master_2, mrqnno='MRQN-002', finyearid=cls.fin_year_id, sessionid=cls.office_staff_2.empid)
        cls.scrap_register_2 = ScrapRegister.objects.create(
            id=2,
            scrapno='SCRAP-002',
            sysdate=timezone.now() - timedelta(days=1),
            compid=cls.comp_id,
            finyearid=cls.fin_year_id,
            sessionid=cls.office_staff_2.empid,
            mrqnid=cls.mrqn_master_2.id
        )

class FinancialYearModelTest(ModelTestSetup):
    def test_financial_year_creation(self):
        self.assertEqual(self.financial_year.finyear, '2023-2024')
        self.assertEqual(self.financial_year.compid, self.comp_id)
        self.assertEqual(str(self.financial_year), '2023-2024')

class OfficeStaffModelTest(ModelTestSetup):
    def test_office_staff_creation(self):
        self.assertEqual(self.office_staff.employeename, 'John Doe')
        self.assertEqual(self.office_staff.full_name, 'Mr. John Doe')
        self.assertEqual(str(self.office_staff), 'Mr. John Doe')

class ScrapRegisterModelTest(ModelTestSetup):
    def test_scrap_register_creation(self):
        self.assertEqual(self.scrap_register.scrapno, 'SCRAP-001')
        self.assertEqual(self.scrap_register.compid, self.comp_id)
        self.assertEqual(self.scrap_register.mrqn_master.mrqnno, 'MRQN-001')
        self.assertEqual(self.scrap_register.generated_by_staff.employeename, 'John Doe')

    def test_get_scrap_report_data_no_filter(self):
        queryset = ScrapRegister.objects.get_scrap_report_data(
            comp_id=self.comp_id,
            fin_year_id=self.fin_year_id,
            search_field='',
            search_value=''
        )
        self.assertEqual(queryset.count(), 2)
        # Check if expected fields are present in values queryset
        first_record = queryset.first()
        self.assertIn('scrapno', first_record)
        self.assertIn('finyear_display', first_record)
        self.assertIn('gen_by', first_record)

    def test_get_scrap_report_data_filter_mrqn_no(self):
        queryset = ScrapRegister.objects.get_scrap_report_data(
            comp_id=self.comp_id,
            fin_year_id=self.fin_year_id,
            search_field='0', # MRQN No
            search_value='MRQN-001'
        )
        self.assertEqual(queryset.count(), 1)
        self.assertEqual(queryset.first()['scrapno'], 'SCRAP-001')

    def test_get_scrap_report_data_filter_mrn_no(self):
        queryset = ScrapRegister.objects.get_scrap_report_data(
            comp_id=self.comp_id,
            fin_year_id=self.fin_year_id,
            search_field='1', # MRN No
            search_value='MRN-002'
        )
        self.assertEqual(queryset.count(), 1)
        self.assertEqual(queryset.first()['scrapno'], 'SCRAP-002')

    def test_get_scrap_report_data_filter_employee_name(self):
        queryset = ScrapRegister.objects.get_scrap_report_data(
            comp_id=self.comp_id,
            fin_year_id=self.fin_year_id,
            search_field='2', # Employee Name
            search_value='Jane Smith'
        )
        self.assertEqual(queryset.count(), 1)
        self.assertEqual(queryset.first()['gen_by'], 'Ms. Jane Smith')

    def test_get_scrap_report_data_filter_scrap_no(self):
        queryset = ScrapRegister.objects.get_scrap_report_data(
            comp_id=self.comp_id,
            fin_year_id=self.fin_year_id,
            search_field='3', # Scrap No
            search_value='SCRAP-001'
        )
        self.assertEqual(queryset.count(), 1)
        self.assertEqual(queryset.first()['scrapno'], 'SCRAP-001')

    def test_format_date_dmy_method(self):
        self.assertIsNotNone(self.scrap_register.sysdate)
        formatted_date = self.scrap_register.format_date_dmy(self.scrap_register.sysdate)
        self.assertEqual(formatted_date, self.scrap_register.sysdate.strftime('%d/%m/%Y'))
        self.assertEqual(self.scrap_register.format_date_dmy(None), 'N/A')

class ScrapReportViewsTest(ModelTestSetup):
    def setUp(self):
        super().setUp()
        self.client = Client()
        # Mock session for the client
        self.client.session['compid'] = self.comp_id
        self.client.session['finyear'] = self.fin_year_id
        self.client.session.save()

    def test_scrapreport_list_view_get(self):
        response = self.client.get(reverse('scrapreport_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qualitycontrol/scrapreport/list.html')
        self.assertIn('scrap_reports', response.context)
        self.assertEqual(len(response.context['scrap_reports']), 2) # Both records should be returned

    def test_scrapreport_list_view_get_with_filter(self):
        response = self.client.get(reverse('scrapreport_list'), {
            'field_type': '0', # MRQN No
            'search_value': 'MRQN-001'
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qualitycontrol/scrapreport/list.html')
        self.assertIn('scrap_reports', response.context)
        self.assertEqual(len(response.context['scrap_reports']), 1)
        self.assertEqual(response.context['scrap_reports'][0]['scrapno'], 'SCRAP-001')

    def test_scrapreport_table_partial_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('scrapreport_table_partial'), headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qualitycontrol/scrapreport/_scrapreport_table.html')
        self.assertIn('scrap_reports', response.context)
        self.assertEqual(len(response.context['scrap_reports']), 2)
        self.assertContains(response, 'SCRAP-001') # Check for content in partial

    def test_scrapreport_search_form_partial_view_post_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('scrapreport_search_form_partial'), {'field_type': '2'}, headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qualitycontrol/scrapreport/_search_form.html')
        self.assertIn('search_form', response.context)
        self.assertEqual(response.context['current_search_field'], '2')

    def test_autocomplete_employee_view(self):
        response = self.client.get(reverse('autocomplete_employee'), {'q': 'john'})
        self.assertEqual(response.status_code, 200)
        self.assertIsInstance(response.json(), dict)
        self.assertIn('suggestions', response.json())
        self.assertEqual(len(response.json()['suggestions']), 1)
        self.assertEqual(response.json()['suggestions'][0]['value'], 'Mr. John Doe [101]')

    def test_autocomplete_employee_view_no_query(self):
        response = self.client.get(reverse('autocomplete_employee'), {'q': ''})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()['suggestions']), 0)

    def test_scrapreport_detail_view(self):
        response = self.client.get(reverse('scrapreport_detail', args=[self.scrap_register.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qualitycontrol/scrapreport/detail.html')
        self.assertIn('scrap_report', response.context)
        self.assertEqual(response.context['scrap_report'].scrapno, 'SCRAP-001')

    # --- Generic CRUD Tests (for ScrapRegister model) ---
    def test_scrapregister_create_view_get(self):
        response = self.client.get(reverse('scrapregister_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qualitycontrol/scrapreport/form.html')
        self.assertIn('form', response.context)

    def test_scrapregister_create_view_post(self):
        new_scrap_count = ScrapRegister.objects.count()
        response = self.client.post(reverse('scrapregister_add'), {
            'scrapno': 'NEW-SCRAP',
            'sysdate': '2024-01-01',
            'compid': self.comp_id,
            'finyearid': self.fin_year_id,
            'sessionid': self.office_staff.empid,
            'mrqnid': self.mrqn_master.id,
            'mrqn_master': self.mrqn_master.id, # Required for ModelForm
            'financial_year': self.financial_year.finyearid, # Required for ModelForm
            'generated_by_staff': self.office_staff.empid # Required for ModelForm
        })
        self.assertEqual(response.status_code, 302) # Redirect on success
        self.assertEqual(ScrapRegister.objects.count(), new_scrap_count + 1)
        self.assertTrue(ScrapRegister.objects.filter(scrapno='NEW-SCRAP').exists())
        # Test HTMX response for create
        headers = {'HTTP_HX_REQUEST': 'true'}
        response_htmx = self.client.post(reverse('scrapregister_add'), {
            'scrapno': 'NEW-SCRAP-HTMX',
            'sysdate': '2024-01-02',
            'compid': self.comp_id,
            'finyearid': self.fin_year_id,
            'sessionid': self.office_staff.empid,
            'mrqnid': self.mrqn_master.id,
            'mrqn_master': self.mrqn_master.id,
            'financial_year': self.financial_year.finyearid,
            'generated_by_staff': self.office_staff.empid
        }, headers=headers)
        self.assertEqual(response_htmx.status_code, 204) # No content
        self.assertIn('HX-Trigger', response_htmx.headers)
        self.assertEqual(response_htmx.headers['HX-Trigger'], 'refreshScrapRegisterList')

    def test_scrapregister_update_view_get(self):
        response = self.client.get(reverse('scrapregister_edit', args=[self.scrap_register.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qualitycontrol/scrapreport/form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance.scrapno, 'SCRAP-001')

    def test_scrapregister_update_view_post(self):
        updated_name = 'UPDATED-SCRAP'
        response = self.client.post(reverse('scrapregister_edit', args=[self.scrap_register.id]), {
            'scrapno': updated_name,
            'sysdate': self.scrap_register.sysdate.strftime('%Y-%m-%d'),
            'compid': self.scrap_register.compid,
            'finyearid': self.scrap_register.finyearid,
            'sessionid': self.scrap_register.sessionid,
            'mrqnid': self.scrap_register.mrqnid,
            'mrqn_master': self.scrap_register.mrqn_master.id,
            'financial_year': self.scrap_register.financial_year.finyearid,
            'generated_by_staff': self.scrap_register.generated_by_staff.empid
        })
        self.assertEqual(response.status_code, 302)
        self.scrap_register.refresh_from_db()
        self.assertEqual(self.scrap_register.scrapno, updated_name)

    def test_scrapregister_delete_view_get(self):
        response = self.client.get(reverse('scrapregister_delete', args=[self.scrap_register.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qualitycontrol/scrapreport/confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'].scrapno, 'SCRAP-001')

    def test_scrapregister_delete_view_post(self):
        scrap_count = ScrapRegister.objects.count()
        response = self.client.post(reverse('scrapregister_delete', args=[self.scrap_register.id]))
        self.assertEqual(response.status_code, 302)
        self.assertEqual(ScrapRegister.objects.count(), scrap_count - 1)
        self.assertFalse(ScrapRegister.objects.filter(id=self.scrap_register.id).exists())
```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for Search & Table Refresh:**
    *   The main `list.html` uses `hx-get` to load the table partial (`_scrapreport_table.html`) on `load` and on a custom `refreshScrapReportList` event.
    *   The search form submits via `hx-get` to the `scrapreport_table_partial` view, refreshing only the table without a full page reload.
    *   The `drpfield` `select` element uses `hx-post` to dynamically swap the search input fields (`_search_form.html`).
    *   The employee name input uses `hx-get` to `autocomplete_employee` for real-time suggestions.
    *   CRUD operations (add/edit/delete buttons) use `hx-get` to fetch forms/confirmation dialogues into a modal, and `hx-post` to submit them. Successful CRUD operations return `204 No Content` with an `HX-Trigger` header to signal a `refreshScrapRegisterList` event, causing the main list to update.
*   **Alpine.js for UI State:**
    *   Alpine.js is used in `list.html` and `_search_form.html` to manage the visibility of the search input fields (`txtMqnNo` vs `txtEmpName`) based on the `field_type` dropdown selection. This provides immediate client-side feedback without a server roundtrip for every dropdown change.
*   **DataTables for List Views:**
    *   The `_scrapreport_table.html` partial includes JavaScript to initialize DataTables on the `scrapreportTable`. The `destroy: true` option is crucial for HTMX, ensuring DataTables properly re-initializes each time the table content is swapped.

### Final Notes

*   **Placeholders:** `settings.DEFAULT_COMP_ID` and `settings.DEFAULT_FIN_YEAR_ID` should be defined in your Django `settings.py` or fetched from user sessions/profiles if they vary by user.
*   **Date Formatting:** Dates are formatted in templates using Django's `date` filter (e.g., `|date:"d/m/Y"`), replacing the `fun.FromDateDMY` logic.
*   **`fun.getCode`:** The original `fun.getCode(txtEmpName.Text)` implies converting employee name to ID. In Django, if `txtEmpName` is used for filtering, we filter by `employeename__icontains`. If `EmpId` is truly needed, the autocomplete would return `EmpId` (or a combined string `Name [ID]`) and the search form would extract the ID before submitting. For this plan, we filter by name for simplicity consistent with the autocomplete output.
*   **Database Connection:** The ASP.NET `clsFunctions fun = new clsFunctions(); string connStr = fun.Connection();` is replaced by Django's ORM, which handles database connections automatically via `settings.DATABASES`.
*   **Error Handling:** The `try-catch` blocks in ASP.NET are replaced by Django's robust error handling and form validation. Messages are used for user feedback.
*   **Modals:** The modal implementation is basic. For production, consider a dedicated Alpine.js component for modals or a more advanced JS library if complex modal interactions are required.
*   **Scalability:** The `ScrapReportManager` with `select_related` and `annotate` makes the data retrieval highly efficient, leveraging the ORM's capabilities. For extremely large datasets, server-side processing for DataTables could be implemented, but for typical report sizes, client-side DataTables is sufficient and faster for the user.