## ASP.NET to Django Conversion Script: Goods Rejection Note (GRN) Report

This document outlines the modernization plan for the ASP.NET Goods Rejection Note (GRN) report page to a modern Django-based solution. Our approach prioritizes automation, leveraging Django's ORM, HTMX, Alpine.js, and DataTables to deliver a robust, performant, and user-friendly application.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists as `core/base.html`.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:** The ASP.NET code extensively queries several tables to compile the GRN report data. The primary table appears to be `tblQc_MaterialQuality_Master`, and it joins with or looks up data from:
- `tblQc_MaterialQuality_Details` (for `RejectedQty` filtering)
- `tblinv_MaterialReceived_Master`
- `tblinv_MaterialReceived_Details`
- `tblFinancial_master`
- `tblInv_Inward_Master`
- `tblInv_Inward_Details`
- `tblMM_PO_Master`
- `tblMM_PO_Details`
- `tblMM_Supplier_master`

The `loadData` method constructs a `DataTable` with the following columns, which represent the effective schema of the report:
- `Id` (from `tblQc_MaterialQuality_Master`)
- `FinYearId` (from `tblQc_MaterialQuality_Master`)
- `FinYear` (from `tblFinancial_master`)
- `GQNNo` (from `tblQc_MaterialQuality_Master`)
- `SysDate` (from `tblQc_MaterialQuality_Master`)
- `GRRNo` (from `tblQc_MaterialQuality_Master`)
- `GINNo` (from `tblinv_MaterialReceived_Master`)
- `PONo` (from `tblInv_Inward_Master`)
- `SupId` (from `tblMM_PO_Master`)
- `Supplier` (from `tblMM_Supplier_master`)
- `ChNO` (from `tblInv_Inward_Master`)
- `ChDT` (from `tblInv_Inward_Master`)

**Inferred Relationships and Data Types:**
*   `tblQc_MaterialQuality_Master`: Primary table for GRN records. `Id` (PK), `FinYearId` (FK to `tblFinancial_master`), `GRRId` (FK to `tblinv_MaterialReceived_Master`), `GQNNo`, `SysDate` (DateTime).
*   `tblinv_MaterialReceived_Master`: Contains `GINNo`. `Id` (PK), `GINId` (FK to `tblInv_Inward_Master`).
*   `tblInv_Inward_Master`: Contains `PONo`, `ChallanNo`, `ChallanDate`. `Id` (PK), `POId` (FK to `tblMM_PO_Master`).
*   `tblMM_PO_Master`: Contains `SupplierId`. `Id` (PK), `SupplierId` (FK to `tblMM_Supplier_master`).
*   `tblMM_Supplier_master`: Contains `SupplierName`. `SupplierId` (PK).
*   `tblFinancial_master`: Contains `FinYear`. `FinYearId` (PK).
*   `tblQc_MaterialQuality_Details`: Contains `MId` (FK to `tblQc_MaterialQuality_Master.Id`) and `RejectedQty`.

We will define Django models for each of these relevant tables, all with `managed = False`, to allow the ORM to perform lookups where logical, and encapsulate complex filtering/joining logic in a custom manager for the main `GoodsRejectionNote` model.

### Step 2: Identify Backend Functionality

**Analysis:**
*   **Read (List/Report):** The primary function is to display a list of "Goods Rejection Notes" based on various search criteria (Supplier Name, GQN No, GRR No, PO No). Data is paginated by the `GridView`.
*   **Filter/Search:** Input fields (`DropDownList1`, `Txtfield`, `txtSupplier`) and a "Search" button (`btnSearch`) trigger data filtering. The dropdown dynamically shows/hides textboxes.
*   **Autocomplete:** `txtSupplier` has an `AutoCompleteExtender` which fetches supplier names from `tblMM_Supplier_master`.
*   **Navigation/Detail:** The "Select" `LinkButton` on each row redirects to `GoodsRejection_GRN_Print_Details.aspx` with various query parameters (`Id`, `SupId`, `GRRNo`, etc.). This implies a detailed view or report generation page that takes these IDs as input.

**CRUD Operations:** This particular ASP.NET page is a "Read" (report) page. There are no explicit "Create", "Update", or "Delete" operations performed *on the Goods Rejection Note itself* from this screen. However, for a complete modernization plan, placeholder `CreateView`, `UpdateView`, and `DeleteView` templates are included as per the instructions, though they might not be directly utilized by this specific report page's functionality. The "Select" action will be translated to a new URL pattern that likely leads to a Django `DetailView` for the `GoodsRejectionNote`.

### Step 3: Infer UI Components

**Analysis:**
*   `DropDownList1`: Django `forms.ChoiceField` rendered as `<select>` with HTMX `hx-post` for dynamic behavior.
*   `Txtfield` and `txtSupplier`: Django `forms.CharField` rendered as `<input type="text">`. `txtSupplier` requires an HTMX-driven autocomplete.
*   `btnSearch`: `<button type="submit">` or HTMX-triggered button.
*   `GridView2`: Replaced by a `<table>` enhanced with `DataTables.js` for client-side functionality (pagination, sorting, filtering). HTMX will be used to swap the entire table content on search/filter actions.
*   `LinkButton` (Select): Replaced by a `<button>` or `<a>` with `hx-get` to a detail view or a new page.

### Step 4: Generate Django Code

We will structure this into a new Django application, e.g., `inventory_reports`.

#### 4.1 Models (inventory_reports/models.py)

We will define models for all involved tables as `managed = False` and create a custom manager for `GoodsRejectionNote` to encapsulate the complex data retrieval logic.

```python
from django.db import models
from django.db.models import F, OuterRef, Subquery, Exists, Q
from django.utils import timezone
from datetime import datetime

# Utility functions for session/context data, mimicking ASP.NET's Session
# In a real application, these would come from request.user.profile or similar.
# For demonstration, we'll assume a placeholder for current company/financial year.
class GlobalContext:
    _instance = None
    _data = {
        'compid': 1, # Placeholder
        'finyearid': 1, # Placeholder
        'username': 'admin' # Placeholder
    }

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(GlobalContext, cls).__new__(cls)
        return cls._instance

    def get(self, key):
        return self._data.get(key)

# Placeholder for clsFunctions.fun.Connection() and FromDateDMY()
# In a real app, this would be handled by Django's database settings and timezone.
def get_db_connection_string():
    # Django handles connection strings in settings.py DATABASES
    return ""

def from_date_dmy(date_str):
    """Converts YYYY-MM-DD HH:MM:SS string to DD/MM/YYYY string."""
    try:
        dt_obj = datetime.strptime(date_str.split(' ')[0], '%Y-%m-%d')
        return dt_obj.strftime('%d/%m/%Y')
    except (ValueError, TypeError):
        return date_str # Return as-is if parsing fails or already in desired format

# --- Unmanaged Models (Directly map to existing database tables) ---

class TblfinancialMaster(models.Model):
    # This model represents the tblFinancial_master table
    finyearid = models.IntegerField(db_column='FinYearId', primary_key=True)
    finyear = models.CharField(db_column='FinYear', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.finyear or f"ID: {self.finyearid}"

class TblmmSupplierMaster(models.Model):
    # This model represents the tblMM_Supplier_master table
    supplierid = models.CharField(db_column='SupplierId', primary_key=True, max_length=50) # Assuming string PK
    suppliername = models.CharField(db_column='SupplierName', max_length=250, blank=True, null=True)
    compid = models.IntegerField(db_column='CompId', blank=True, null=True) # Assuming CompId exists

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.suppliername or f"ID: {self.supplierid}"

class TblinvMaterialreceivedMaster(models.Model):
    # This model represents the tblinv_MaterialReceived_Master table
    id = models.IntegerField(db_column='Id', primary_key=True)
    grrno = models.CharField(db_column='GRRNo', max_length=50, blank=True, null=True)
    ginno = models.CharField(db_column='GINNo', max_length=50, blank=True, null=True)
    ginid = models.IntegerField(db_column='GINId', blank=True, null=True) # FK to TblinvInwardMaster
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialReceived_Master'
        verbose_name = 'Material Received'
        verbose_name_plural = 'Material Received'

    def __str__(self):
        return self.grrno or f"ID: {self.id}"

class TblinvInwardMaster(models.Model):
    # This model represents the tblInv_Inward_Master table
    id = models.IntegerField(db_column='Id', primary_key=True)
    pono = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    poid = models.IntegerField(db_column='POId', blank=True, null=True) # FK to TblmmPoMaster
    challanno = models.CharField(db_column='ChallanNo', max_length=100, blank=True, null=True)
    challandate = models.DateTimeField(db_column='ChallanDate', blank=True, null=True)
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Master'
        verbose_name = 'Inward'
        verbose_name_plural = 'Inwards'

    def __str__(self):
        return self.pono or f"ID: {self.id}"

class TblmmPoMaster(models.Model):
    # This model represents the tblMM_PO_Master table
    id = models.IntegerField(db_column='Id', primary_key=True)
    supplierid = models.CharField(db_column='SupplierId', max_length=50, blank=True, null=True) # FK to TblmmSupplierMaster
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'

    def __str__(self):
        return f"PO ID: {self.id}"

class TblqcMaterialqualityDetails(models.Model):
    # This model represents the tblQc_MaterialQuality_Details table
    id = models.IntegerField(db_column='Id', primary_key=True)
    mid = models.IntegerField(db_column='MId', blank=True, null=True) # FK to TblqcMaterialqualityMaster
    rejectedqty = models.DecimalField(db_column='RejectedQty', max_digits=18, decimal_places=3, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblQc_MaterialQuality_Details'
        verbose_name = 'Material Quality Detail'
        verbose_name_plural = 'Material Quality Details'

    def __str__(self):
        return f"Detail ID: {self.id} for MId: {self.mid}"


class GoodsRejectionNoteManager(models.Manager):
    def get_grn_report_data(self, search_type=None, search_value=None, supplier_id=None):
        ctx = GlobalContext()
        comp_id = ctx.get('compid')
        fin_year_id = ctx.get('finyearid')

        # Start with the base query on tblQc_MaterialQuality_Master
        queryset = self.get_queryset().filter(
            finyearid__lte=fin_year_id, # ASP.NET uses <=
            compid=comp_id
        ).order_by('-id')

        # Filter based on RejectedQty > 0 from details table
        # We need to check if ANY detail has rejected qty > 0
        has_rejected_qty_subquery = TblqcMaterialqualityDetails.objects.filter(
            mid=OuterRef('id'),
            rejectedqty__gt=0
        )
        queryset = queryset.annotate(has_rejected=Exists(has_rejected_qty_subquery)).filter(has_rejected=True)

        # Apply dynamic filters
        if search_type == '1' and search_value: # GQN No
            queryset = queryset.filter(gqnno=search_value)
        elif search_type == '2' and search_value: # GRR No
            # Join with tblinv_MaterialReceived_Master and filter
            grr_ids = TblinvMaterialreceivedMaster.objects.filter(grrno=search_value, compid=comp_id).values_list('id', flat=True)
            queryset = queryset.filter(grrid__in=grr_ids)
        elif search_type == '3' and search_value: # PO No
            # Join with tblinv_MaterialReceived_Master -> tblInv_Inward_Master and filter
            gin_ids = TblinvInwardMaster.objects.filter(pono=search_value, compid=comp_id).values_list('id', flat=True)
            mr_ids = TblinvMaterialreceivedMaster.objects.filter(ginid__in=gin_ids, compid=comp_id).values_list('id', flat=True)
            queryset = queryset.filter(grrid__in=mr_ids)
        elif search_type == '0' and supplier_id: # Supplier Name (uses SupId from search)
            # Join with tblinv_MaterialReceived_Master -> tblInv_Inward_Master -> tblMM_PO_Master and filter
            po_ids = TblmmPoMaster.objects.filter(supplierid=supplier_id, compid=comp_id).values_list('id', flat=True)
            gin_ids = TblinvInwardMaster.objects.filter(poid__in=po_ids, compid=comp_id).values_list('id', flat=True)
            mr_ids = TblinvMaterialreceivedMaster.objects.filter(ginid__in=gin_ids, compid=comp_id).values_list('id', flat=True)
            queryset = queryset.filter(grrid__in=mr_ids)

        # Annotate with related data for display in the report
        # We will need to join across multiple tables.
        # This will be a complex query, mimicking the C# joins.
        # For 'managed=False' models, we can either define explicit ForeignKeys if the DB has them,
        # or use Subqueries/F expressions. Assuming logical FKs are present in the DB schema
        # for ORM to use.

        # Define explicit ForeignKeys on GoodsRejectionNote for easier joining if they exist logically
        # The following 'joins' are conceptual and need to be explicitly defined or handled via subqueries.
        # For simplicity, we define the FKs on the main model assuming they are there.

        # Note: If the DB doesn't have FK constraints, we still define them in Django models
        # to use `select_related`, but if the IDs are not unique or don't logically match,
        # raw SQL or manual joining in the manager might be needed.
        # Here, I'm assuming a logical FK mapping.

        # Add explicit FKs to GoodsRejectionNote for `select_related`
        # This is a key part of making 'fat model' work with legacy DBs.
        # The fields are `grrid` pointing to `tblinv_MaterialReceived_Master.id` etc.

        # Pre-fetch related objects using select_related and prefetch_related
        queryset = queryset.select_related(
            'fin_year', # `fin_year` is a related_name for finyearid FK
            'material_received_master' # `material_received_master` is related_name for grrid FK
        ).prefetch_related(
            # Need to chain prefetch_related for deeper nested lookups
            'material_received_master__inward_master', # from TblinvMaterialreceivedMaster to TblinvInwardMaster
            'material_received_master__inward_master__po_master', # from TblinvInwardMaster to TblmmPoMaster
            'material_received_master__inward_master__po_master__supplier_master' # from TblmmPoMaster to TblmmSupplierMaster
        )

        # Now, construct the data like the ASP.NET DataTable
        results = []
        for obj in queryset:
            # Safely access potentially missing related objects
            fin_year_name = obj.fin_year.finyear if hasattr(obj, 'fin_year') and obj.fin_year else ''
            sys_date_formatted = from_date_dmy(obj.sysdate.isoformat()) if obj.sysdate else ''

            material_received = obj.material_received_master
            gin_no = material_received.ginno if material_received else ''
            inward = material_received.inward_master if material_received and hasattr(material_received, 'inward_master') else None
            po_no = inward.pono if inward else ''
            challan_no = inward.challanno if inward else ''
            challan_date_formatted = from_date_dmy(inward.challandate.isoformat()) if inward and inward.challandate else ''

            po_master = inward.po_master if inward and hasattr(inward, 'po_master') else None
            sup_id = po_master.supplierid if po_master else ''
            supplier_name = po_master.supplier_master.suppliername if po_master and hasattr(po_master, 'supplier_master') else ''

            # Mimic ASP.NET's supplier name format
            full_supplier_name = f"{supplier_name} [{sup_id}]" if supplier_name and sup_id else supplier_name

            results.append({
                'Id': obj.id,
                'FinYearId': obj.finyearid,
                'FinYear': fin_year_name,
                'GQNNo': obj.gqnno,
                'SysDate': sys_date_formatted,
                'GRRNo': obj.grrno,
                'GINNo': gin_no,
                'PONo': po_no,
                'SupId': sup_id,
                'Supplier': full_supplier_name,
                'ChNO': challan_no,
                'ChDT': challan_date_formatted,
            })
        return results


class GoodsRejectionNote(models.Model):
    # This model represents the tblQc_MaterialQuality_Master table
    id = models.IntegerField(db_column='Id', primary_key=True)
    finyearid = models.IntegerField(db_column='FinYearId') # FK to tblFinancial_master
    gqnno = models.CharField(db_column='GQNNo', max_length=50, blank=True, null=True)
    grrno = models.CharField(db_column='GRRNo', max_length=50, blank=True, null=True)
    sysdate = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    grrid = models.IntegerField(db_column='GRRId', blank=True, null=True) # FK to tblinv_MaterialReceived_Master
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)

    # Define explicit ForeignKeys for Django ORM to use select_related/prefetch_related
    # Note: These are defined here for ORM convenience, assuming logical keys exist in DB
    # The `to_field` is important if the primary key isn't 'id' in the target table.
    fin_year = models.ForeignKey(TblfinancialMaster, on_delete=models.DO_NOTHING, db_column='FinYearId',
                                related_name='rejection_notes', blank=True, null=True)
    material_received_master = models.ForeignKey(TblinvMaterialreceivedMaster, on_delete=models.DO_NOTHING, db_column='GRRId',
                                                related_name='rejection_notes', blank=True, null=True)
    
    # Custom manager
    objects = GoodsRejectionNoteManager()

    class Meta:
        managed = False
        db_table = 'tblQc_MaterialQuality_Master'
        verbose_name = 'Goods Rejection Note'
        verbose_name_plural = 'Goods Rejection Notes'

    def __str__(self):
        return self.gqnno or f"GRN ID: {self.id}"
    
    # Example business logic method if needed later (Fat Model)
    def calculate_total_rejected_quantity(self):
        # In a real scenario, this would query TblqcMaterialqualityDetails
        # e.g., self.tblqcmaterialqualitydetails_set.aggregate(total=Sum('rejectedqty'))['total']
        return 0 # Placeholder


```

#### 4.2 Forms (inventory_reports/forms.py)

Since the page is primarily for viewing/filtering, we'll create a `Form` for the search criteria, not a `ModelForm` for `GoodsRejectionNote` itself. However, for future CRUD operations on the `GoodsRejectionNote`, a placeholder `ModelForm` is included.

```python
from django import forms
from .models import GoodsRejectionNote, TblmmSupplierMaster

class GoodsRejectionNoteSearchForm(forms.Form):
    SEARCH_CHOICES = [
        ('0', 'Supplier Name'),
        ('1', 'GQN No'),
        ('2', 'GRR No'),
        ('3', 'PO No'),
    ]
    search_type = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        required=False,
        label="Search By",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
            'hx-post': 'hx-post="{% url "inventory_reports:grn_list" %}" hx-target="#grnTable-container" hx-indicator="#loadingIndicator" hx-swap="innerHTML"',
            'hx-vals': '{"search_type": this.value, "trigger_refresh": "true"}', # Send search_type and flag for partial update
            'hx-sync': 'closest form:abort', # Prevent multiple requests
        })
    )
    search_text = forms.CharField(
        max_length=255,
        required=False,
        label="Search Value",
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
            'placeholder': 'Enter search value',
            'x-show': "searchType != '0'", # Alpine.js conditional visibility
            'hx-get': "{% url 'inventory_reports:supplier_autocomplete' %}", # Example for HTMX autocomplete
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#supplier-suggestions',
            'hx-swap': 'innerHTML',
            'name': 'search_text_input', # Use distinct name for text input
        })
    )
    supplier_text = forms.CharField(
        max_length=255,
        required=False,
        label="Supplier Name",
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
            'placeholder': 'Start typing supplier name...',
            'x-show': "searchType == '0'", # Alpine.js conditional visibility
            'hx-get': "{% url 'inventory_reports:supplier_autocomplete' %}",
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#supplier-suggestions',
            'hx-swap': 'innerHTML',
            'name': 'supplier_text_input', # Use distinct name for supplier input
            'x-model': 'supplierSearch', # Alpine.js model for input
            '@input': 'clearSupplierId', # Alpine.js method to clear hidden ID
        })
    )
    # Hidden field to store the actual SupId for search
    supplier_id = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.HiddenInput(attrs={'x-model': 'selectedSupplierId'}) # Alpine.js model
    )

    def clean(self):
        cleaned_data = super().clean()
        search_type = cleaned_data.get('search_type')
        search_text = cleaned_data.get('search_text')
        supplier_text = cleaned_data.get('supplier_text')
        supplier_id = cleaned_data.get('supplier_id')

        if search_type == '0':
            if not supplier_id:
                # If supplier_text is provided but no ID, try to get ID from text
                if supplier_text:
                    try:
                        # Assuming format "Name [ID]"
                        parts = supplier_text.strip().split(' [')
                        if len(parts) == 2 and parts[1].endswith(']'):
                            potential_id = parts[1][:-1]
                            # Validate if this ID exists in DB
                            if TblmmSupplierMaster.objects.filter(supplierid=potential_id).exists():
                                cleaned_data['supplier_id'] = potential_id
                                return cleaned_data
                    except Exception:
                        pass # Silently fail if parsing/lookup fails
                # If no valid supplier ID found or text is empty, clear it.
                if not cleaned_data.get('supplier_id'):
                    self.add_error('supplier_text', 'Please select a valid supplier from the suggestions or enter a valid Supplier Name [ID].')
        else: # For GQN, GRR, PO No
            if not search_text:
                self.add_error('search_text', 'This field is required for the selected search type.')
        
        return cleaned_data

# Placeholder form for actual GRN record if needed for CRUD
class GoodsRejectionNoteForm(forms.ModelForm):
    class Meta:
        model = GoodsRejectionNote
        fields = ['gqnno', 'grrno', 'sysdate', 'finyearid'] # Example fields
        widgets = {
            'gqnno': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'grrno': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'sysdate': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'finyearid': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
        }
```

#### 4.3 Views (inventory_reports/views.py)

We'll create a `ListView` for the main report, a partial view for the table, an autocomplete view for suppliers, and placeholders for CRUD views.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from django.db.models import Q

from .models import GoodsRejectionNote, TblmmSupplierMaster, GlobalContext
from .forms import GoodsRejectionNoteSearchForm, GoodsRejectionNoteForm

class GoodsRejectionNoteListView(View):
    template_name = 'inventory_reports/goodsrejectionnote/list.html'
    
    def get(self, request, *args, **kwargs):
        form = GoodsRejectionNoteSearchForm()
        # Initial load, so no search applied yet.
        # DataTables will handle pagination/sorting client-side, so send all data.
        grn_data = GoodsRejectionNote.objects.get_grn_report_data()
        
        context = {
            'form': form,
            'goods_rejection_notes': grn_data, # Initial data for the table partial
            'search_type_initial': '0', # Default dropdown selection
        }
        return self.render_to_response(context)

    def post(self, request, *args, **kwargs):
        form = GoodsRejectionNoteSearchForm(request.POST)
        if form.is_valid():
            search_type = form.cleaned_data.get('search_type')
            search_value = form.cleaned_data.get('search_text')
            supplier_id = form.cleaned_data.get('supplier_id')
            
            grn_data = GoodsRejectionNote.objects.get_grn_report_data(
                search_type=search_type, 
                search_value=search_value,
                supplier_id=supplier_id
            )
        else:
            grn_data = [] # No data if form is invalid
            # Add error messages if needed, though form rendering usually shows them
            # messages.error(request, 'Please correct the errors in the search form.')

        # This view primarily serves as a full page render for GET and triggers HTMX partial swap for POST.
        # For simplicity, POST also renders the list.html or just refreshes the table partial.
        context = {
            'form': form,
            'goods_rejection_notes': grn_data,
            'search_type_initial': form.cleaned_data.get('search_type', '0'),
        }

        if request.headers.get('HX-Request'):
            # If HTMX request, render only the table partial
            table_html = render_to_string(
                'inventory_reports/goodsrejectionnote/_goodsrejectionnote_table.html', 
                {'goods_rejection_notes': grn_data}, 
                request
            )
            # We don't need a 204 with HX-Trigger for a POST, as the target is already specific.
            # If the form itself needs to update, we'd swap form.
            return HttpResponse(table_html)
        
        return self.render_to_response(context)

    def render_to_response(self, context):
        return self.render_to_response_template(self.template_name, context)

    def render_to_response_template(self, template, context):
        return super().render_to_response(context)


class GoodsRejectionNoteTablePartialView(ListView):
    # This view is specifically for HTMX requests to refresh the table.
    model = GoodsRejectionNote
    template_name = 'inventory_reports/goodsrejectionnote/_goodsrejectionnote_table.html'
    context_object_name = 'goods_rejection_notes'

    def get_queryset(self):
        # This view should handle the search parameters sent via HTMX POST
        search_type = self.request.POST.get('search_type', '0') # Default to Supplier Name
        search_text = self.request.POST.get('search_text_input', '')
        supplier_id = self.request.POST.get('supplier_id', '') # Hidden supplier ID
        
        # Use the custom manager to get filtered data
        return GoodsRejectionNote.objects.get_grn_report_data(
            search_type=search_type, 
            search_value=search_text,
            supplier_id=supplier_id
        )

    def post(self, request, *args, **kwargs):
        # DataTables usually does not send POST requests for refresh, but HTMX might for form submission
        # This method is primarily for the hx-get to /table/ if the form is submitted to a different endpoint
        # The main list view handles the POST and returns the table partial.
        # If this is called directly by HTMX (e.g., from hx-post on the form, targeting this URL),
        # then the logic in get_queryset needs to access request.POST
        return self.get(request, *args, **kwargs) # Re-use get logic for POST


class SupplierAutocompleteView(View):
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('q', '')
        if not prefix_text:
            return JsonResponse([], safe=False)

        ctx = GlobalContext()
        comp_id = ctx.get('compid')

        # Mimic ASP.NET's case-insensitive StartsWith and "Name [ID]" format
        suppliers = TblmmSupplierMaster.objects.filter(
            compid=comp_id,
            suppliername__istartswith=prefix_text
        ).order_by('suppliername')[:10] # Limit to 10 as in ASP.NET example

        results = [
            f"{supplier.suppliername} [{supplier.supplierid}]"
            for supplier in suppliers
        ]
        return JsonResponse(results, safe=False)

# --- CRUD Placeholders (as requested by template, but not directly used by original ASP.NET page) ---

class GoodsRejectionNoteCreateView(CreateView):
    model = GoodsRejectionNote
    form_class = GoodsRejectionNoteForm
    template_name = 'inventory_reports/goodsrejectionnote/_goodsrejectionnote_form.html'
    success_url = reverse_lazy('inventory_reports:grn_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Goods Rejection Note added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, tells HTMX to do nothing more than trigger
                headers={
                    'HX-Trigger': 'refreshGoodsRejectionNoteList'
                }
            )
        return response

class GoodsRejectionNoteUpdateView(UpdateView):
    model = GoodsRejectionNote
    form_class = GoodsRejectionNoteForm
    template_name = 'inventory_reports/goodsrejectionnote/_goodsrejectionnote_form.html'
    success_url = reverse_lazy('inventory_reports:grn_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Goods Rejection Note updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshGoodsRejectionNoteList'
                }
            )
        return response

class GoodsRejectionNoteDeleteView(DeleteView):
    model = GoodsRejectionNote
    template_name = 'inventory_reports/goodsrejectionnote/_goodsrejectionnote_confirm_delete.html'
    success_url = reverse_lazy('inventory_reports:grn_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Goods Rejection Note deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshGoodsRejectionNoteList'
                }
            )
        return response

class GoodsRejectionNoteSelectView(View):
    # This view handles the "Select" button action from the GridView.
    # It mimics the ASP.NET redirect with query parameters.
    def get(self, request, pk):
        # Retrieve the GoodsRejectionNote object
        try:
            grn = GoodsRejectionNote.objects.get(pk=pk)
            # You might need to fetch related data here to construct the parameters
            # Similar logic to get_grn_report_data for specific item
            report_data = GoodsRejectionNote.objects.get_grn_report_data(
                search_type=None, search_value=None, supplier_id=None # No specific filters needed here
            )
            selected_item = next((item for item in report_data if item['Id'] == pk), None)

            if selected_item:
                sup_id = selected_item.get('SupId', '')
                grr_no = selected_item.get('GRRNo', '')
                gin_no = selected_item.get('GINNo', '')
                po_no = selected_item.get('PONo', '')
                fy_id = selected_item.get('FinYearId', '')

                # Construct the redirect URL (e.g., to a new report page)
                # This should be a Django URL, not an .aspx one.
                # Example: redirect to a new detail page or a PDF generation view
                redirect_url = reverse_lazy('inventory_reports:grn_print_details', kwargs={'pk': pk})
                # Add query parameters if they are truly needed on the target page for distinct logic
                # (Django generally prefers path parameters for primary IDs)
                # If these parameters modify the *behavior* of the target page, keep them.
                query_params = f"SupId={sup_id}&GRRNo={grr_no}&GINNo={gin_no}&PONo={po_no}&FyId={fy_id}"
                
                return HttpResponse(
                    status=204,
                    headers={'HX-Redirect': f"{redirect_url}?{query_params}"} # HTMX redirect
                )
            else:
                messages.error(request, "Selected Goods Rejection Note not found.")
                return HttpResponse(status=404) # Or redirect back with error

        except GoodsRejectionNote.DoesNotExist:
            messages.error(request, "Goods Rejection Note not found.")
            return HttpResponse(status=404)

class GoodsRejectionNotePrintDetailsView(View):
    # Placeholder view for the target of the "Select" action
    # This would render the detailed report or trigger a PDF download.
    def get(self, request, pk):
        # Logic to fetch detailed GRN data for 'pk' and query parameters
        # Render a print-friendly template or return a PDF response
        grn_details = {} # Fetch details for pk
        # You would use pk, and optionally request.GET parameters if needed
        return HttpResponse(f"Details for GRN ID: {pk}. SupId: {request.GET.get('SupId')}, GRRNo: {request.GET.get('GRRNo')}")
```

#### 4.4 Templates (inventory_reports/templates/inventory_reports/goodsrejectionnote/)

**`list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Goods Rejection Notes</h2>
        <button
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'inventory_reports:grn_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New GRN
        </button>
    </div>

    <div x-data="{ searchType: '{{ search_type_initial }}', supplierSearch: '{{ form.supplier_text.value|default:"" }}', selectedSupplierId: '{{ form.supplier_id.value|default:"" }}' }"
         @htmx:after-on-load.self="
            searchType = $event.detail.elt.querySelector('[name=search_type]').value;
            supplierSearch = $event.detail.elt.querySelector('[name=supplier_text_input]').value;
            selectedSupplierId = $event.detail.elt.querySelector('[name=supplier_id]').value;
         "
         >
        <form hx-post="{% url 'inventory_reports:grn_list' %}"
              hx-target="#grnTable-container"
              hx-indicator="#loadingIndicator"
              hx-swap="innerHTML"
              hx-sync="this:abort"
              class="mb-6 bg-gray-50 p-4 rounded-md shadow-sm grid grid-cols-1 md:grid-cols-3 gap-4">
            {% csrf_token %}

            <div>
                <label for="{{ form.search_type.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.search_type.label }}
                </label>
                <select id="{{ form.search_type.id_for_label }}" name="{{ form.search_type.name }}"
                        x-model="searchType"
                        @change="$el.closest('form').submit()"
                        class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    {% for value, label in form.search_type.field.choices %}
                        <option value="{{ value }}" {% if value == search_type_initial %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>

            <div class="md:col-span-2 relative">
                <label for="search_input" class="block text-sm font-medium text-gray-700">Search Value</label>
                <input type="text" id="search_input"
                       :name="searchType == '0' ? 'supplier_text_input' : 'search_text_input'"
                       class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                       :placeholder="searchType == '0' ? 'Start typing supplier name...' : 'Enter search value'"
                       x-bind:value="searchType == '0' ? supplierSearch : search_text"
                       hx-get="{% url 'inventory_reports:supplier_autocomplete' %}"
                       hx-trigger="keyup changed delay:500ms, search"
                       hx-target="#supplier-suggestions"
                       hx-swap="innerHTML"
                       @input="if(searchType == '0') { supplierSearch = $event.target.value; clearSupplierId(); } else { search_text = $event.target.value; }"
                       />
                <input type="hidden" name="supplier_id" x-model="selectedSupplierId" />
                <div id="supplier-suggestions" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1"></div>
            </div>

            <div class="md:col-start-3 md:row-start-2 flex items-end">
                <button
                    type="submit"
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded w-full">
                    Search
                </button>
            </div>
            {% for field_error in form.non_field_errors %}
                <p class="text-red-500 text-xs mt-1 col-span-full">{{ field_error }}</p>
            {% endfor %}
            {% for field in form %}
                {% if field.errors %}
                    <p class="text-red-500 text-xs mt-1 col-span-full">{{ field.label }}: {{ field.errors }}</p>
                {% endif %}
            {% endfor %}
        </form>
    </div>

    <div id="grnTable-container"
         hx-trigger="load, refreshGoodsRejectionNoteList from:body"
         hx-get="{% url 'inventory_reports:grn_table' %}"
         hx-swap="innerHTML"
         class="relative min-h-[200px]"> {# Added min-height for loader #}
        <!-- DataTable will be loaded here via HTMX -->
        <div id="loadingIndicator" class="htmx-indicator absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="ml-2 text-gray-700">Loading data...</p>
        </div>
        {# Initial render if data_tables is already available (from initial GET request) #}
        {% if goods_rejection_notes %}
            {% include 'inventory_reports/goodsrejectionnote/_goodsrejectionnote_table.html' with goods_rejection_notes=goods_rejection_notes %}
        {% endif %}
    </div>

    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('grnSearchForm', () => ({
            searchType: '0',
            supplierSearch: '',
            selectedSupplierId: '',
            init() {
                // Initialize with current values if available from the form's initial render
                const searchTypeSelect = this.$el.querySelector('select[name="search_type"]');
                if (searchTypeSelect) {
                    this.searchType = searchTypeSelect.value;
                }
                const supplierTextInput = this.$el.querySelector('input[name="supplier_text_input"]');
                if (supplierTextInput) {
                    this.supplierSearch = supplierTextInput.value;
                }
                const supplierIdInput = this.$el.querySelector('input[name="supplier_id"]');
                if (supplierIdInput) {
                    this.selectedSupplierId = supplierIdInput.value;
                }
            },
            selectSupplier(name, id) {
                this.supplierSearch = name;
                this.selectedSupplierId = id;
                this.$el.querySelector('#supplier-suggestions').innerHTML = ''; // Clear suggestions
                // Optionally submit the form after selection
                this.$el.closest('form').submit();
            },
            clearSupplierId() {
                // Clear the hidden ID if the user manually types
                // We'll let the form's clean method handle validation if no ID is selected from suggestions.
                this.selectedSupplierId = '';
            }
        }));
    });

    // Handle HTMX response for autocomplete suggestions
    document.body.addEventListener('htmx:afterOnLoad', (event) => {
        if (event.detail.target.id === 'supplier-suggestions' && event.detail.xhr.status === 200) {
            const suggestions = JSON.parse(event.detail.xhr.responseText);
            let html = '';
            suggestions.forEach(item => {
                // Extract Name and ID from "Name [ID]" format
                const parts = item.split(' [');
                const name = parts[0];
                const id = parts.length > 1 ? parts[1].slice(0, -1) : '';
                html += `<div class="p-2 cursor-pointer hover:bg-gray-100" @click="selectSupplier('${name}', '${id}')">${item}</div>`;
            });
            event.detail.target.innerHTML = html;
        }
    });
</script>
{% endblock %}
```

**`_goodsrejectionnote_table.html`**

```html
<table id="goodsrejectionnoteTable" class="min-w-full bg-white border-collapse">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GQN No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GRR No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GIN No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Supplier</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% if goods_rejection_notes %}
            {% for obj in goods_rejection_notes %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.FinYear }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.GQNNo }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.SysDate }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.GRRNo }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.GINNo }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.PONo }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.Supplier }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.ChNO }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.ChDT }}</td>
                <td class="py-2 px-4 border-b border-gray-200">
                    <button
                        class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded mr-2"
                        hx-get="{% url 'inventory_reports:grn_select' obj.Id %}"
                        hx-trigger="click"
                        hx-swap="none"
                        hx-ext="json-enc"
                        _="on htmx:beforeRequest add .htmx-indicator to #loadingIndicator on htmx:afterRequest remove .htmx-indicator from #loadingIndicator"
                        >
                        Select
                    </button>
                    <button
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                        hx-get="{% url 'inventory_reports:grn_edit' obj.Id %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                        hx-get="{% url 'inventory_reports:grn_delete' obj.Id %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        {% else %}
            <tr>
                <td colspan="11" class="py-4 px-4 text-center text-gray-500">No data to display !</td>
            </tr>
        {% endif %}
    </tbody>
</table>

<script>
    // Ensure DataTables is re-initialized after HTMX swap
    $(document).ready(function() {
        $('#goodsrejectionnoteTable').DataTable({
            "pageLength": 24, // Matches ASP.NET PageSize
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "destroy": true, // Destroy existing instance before re-initialization
            "autoWidth": false,
            "columns": [
                { "orderable": false }, // SN column
                null, // Fin Year
                null, // GQN No
                null, // Date
                null, // GRR No
                null, // GIN No
                null, // PO No
                null, // Name of Supplier
                null, // Challan No
                null, // Challan Date
                { "orderable": false }  // Actions
            ]
        });
    });
</script>
```

**`_goodsrejectionnote_form.html`** (For Create/Update Modal)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Goods Rejection Note</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-2 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`_goodsrejectionnote_confirm_delete.html`** (For Delete Modal)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Goods Rejection Note: "{{ object.gqnno }}"?</p>
    <form hx-delete="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (inventory_reports/urls.py)

```python
from django.urls import path
from .views import (
    GoodsRejectionNoteListView, GoodsRejectionNoteTablePartialView, 
    GoodsRejectionNoteCreateView, GoodsRejectionNoteUpdateView, GoodsRejectionNoteDeleteView,
    SupplierAutocompleteView, GoodsRejectionNoteSelectView, GoodsRejectionNotePrintDetailsView
)

app_name = 'inventory_reports'

urlpatterns = [
    path('grn/', GoodsRejectionNoteListView.as_view(), name='grn_list'),
    path('grn/table/', GoodsRejectionNoteTablePartialView.as_view(), name='grn_table'), # HTMX target for table updates
    path('grn/add/', GoodsRejectionNoteCreateView.as_view(), name='grn_add'),
    path('grn/edit/<int:pk>/', GoodsRejectionNoteUpdateView.as_view(), name='grn_edit'),
    path('grn/delete/<int:pk>/', GoodsRejectionNoteDeleteView.as_view(), name='grn_delete'),
    path('grn/select/<int:pk>/', GoodsRejectionNoteSelectView.as_view(), name='grn_select'), # For 'Select' button
    path('grn/print-details/<int:pk>/', GoodsRejectionNotePrintDetailsView.as_view(), name='grn_print_details'), # Target for 'Select'
    path('grn/autocomplete_supplier/', SupplierAutocompleteView.as_view(), name='supplier_autocomplete'), # HTMX autocomplete
]
```

#### 4.6 Tests (inventory_reports/tests.py)

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch
from datetime import datetime

from .models import (
    GoodsRejectionNote, TblfinancialMaster, TblmmSupplierMaster,
    TblinvMaterialreceivedMaster, TblinvInwardMaster, TblmmPoMaster,
    TblqcMaterialqualityDetails, GlobalContext
)
from .forms import GoodsRejectionNoteSearchForm

# Mock GlobalContext for consistent test environment
@patch('inventory_reports.models.GlobalContext')
class GoodsRejectionNoteModelTest(TestCase):
    @classmethod
    def setUpTestData(cls, MockGlobalContext):
        # Setup mock global context
        mock_ctx = MockGlobalContext.return_value
        mock_ctx.get.side_effect = lambda key: {
            'compid': 1,
            'finyearid': 2023,
            'username': 'testuser'
        }.get(key)

        # Create minimal test data for related tables first
        TblfinancialMaster.objects.create(finyearid=2023, finyear='2023-24')
        TblfinancialMaster.objects.create(finyearid=2022, finyear='2022-23')

        TblmmSupplierMaster.objects.create(supplierid='SUP001', suppliername='Test Supplier A', compid=1)
        TblmmSupplierMaster.objects.create(supplierid='SUP002', suppliername='Test Supplier B', compid=1)

        TblmmPoMaster.objects.create(id=101, supplierid='SUP001', compid=1)
        TblmmPoMaster.objects.create(id=102, supplierid='SUP002', compid=1)

        TblinvInwardMaster.objects.create(id=201, pono='PO-001', poid=101, challanno='CH-001', challandate=datetime(2023, 1, 1), compid=1)
        TblinvInwardMaster.objects.create(id=202, pono='PO-002', poid=102, challanno='CH-002', challandate=datetime(2023, 1, 2), compid=1)

        TblinvMaterialreceivedMaster.objects.create(id=301, grrno='GRR-001', ginno='GIN-001', ginid=201, compid=1)
        TblinvMaterialreceivedMaster.objects.create(id=302, grrno='GRR-002', ginno='GIN-002', ginid=202, compid=1)

        # Create GoodsRejectionNote objects
        grn1 = GoodsRejectionNote.objects.create(
            id=1, finyearid=2023, gqnno='GQN-001', grrno='GRN-001', sysdate=datetime(2023, 1, 10), grrid=301, compid=1
        )
        grn2 = GoodsRejectionNote.objects.create(
            id=2, finyearid=2023, gqnno='GQN-002', grrno='GRN-002', sysdate=datetime(2023, 1, 11), grrid=302, compid=1
        )
        grn3 = GoodsRejectionNote.objects.create(
            id=3, finyearid=2022, gqnno='GQN-003', grrno='GRN-003', sysdate=datetime(2022, 1, 12), grrid=301, compid=1
        ) # This one should be filtered out by finyearid <= 2023

        # Create details with rejected qty
        TblqcMaterialqualityDetails.objects.create(id=1001, mid=grn1.id, rejectedqty=5.0)
        TblqcMaterialqualityDetails.objects.create(id=1002, mid=grn2.id, rejectedqty=10.0)
        TblqcMaterialqualityDetails.objects.create(id=1003, mid=grn3.id, rejectedqty=0.0) # This GRN should be filtered out by rejected qty check

    def test_grn_creation(self, MockGlobalContext):
        grn = GoodsRejectionNote.objects.get(id=1)
        self.assertEqual(grn.gqnno, 'GQN-001')
        self.assertEqual(grn.grrno, 'GRN-001')
        self.assertEqual(grn.sysdate.year, 2023)

    def test_get_grn_report_data_no_filter(self, MockGlobalContext):
        # Only grn1 and grn2 should be returned because grn3 has 0 rejected qty and is older fin year
        data = GoodsRejectionNote.objects.get_grn_report_data()
        self.assertEqual(len(data), 2)
        self.assertEqual(data[0]['Id'], 2) # Ordered by Id Desc
        self.assertEqual(data[1]['Id'], 1)
        self.assertEqual(data[0]['Supplier'], 'Test Supplier B [SUP002]')
        self.assertEqual(data[1]['Supplier'], 'Test Supplier A [SUP001]')

    def test_get_grn_report_data_gqn_no_filter(self, MockGlobalContext):
        data = GoodsRejectionNote.objects.get_grn_report_data(search_type='1', search_value='GQN-001')
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]['GQNNo'], 'GQN-001')

    def test_get_grn_report_data_grr_no_filter(self, MockGlobalContext):
        data = GoodsRejectionNote.objects.get_grn_report_data(search_type='2', search_value='GRR-001')
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]['GRRNo'], 'GRN-001')

    def test_get_grn_report_data_po_no_filter(self, MockGlobalContext):
        data = GoodsRejectionNote.objects.get_grn_report_data(search_type='3', search_value='PO-001')
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]['PONo'], 'PO-001')

    def test_get_grn_report_data_supplier_filter(self, MockGlobalContext):
        data = GoodsRejectionNote.objects.get_grn_report_data(search_type='0', supplier_id='SUP001')
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]['Supplier'], 'Test Supplier A [SUP001]')

    def test_from_date_dmy_utility(self, MockGlobalContext):
        self.assertEqual(self.patcher.is_patched, True) # Just to use MockGlobalContext in method
        dt_str = "2023-01-10 12:30:00"
        self.assertEqual(from_date_dmy(dt_str), "10/01/2023")
        self.assertEqual(from_date_dmy(None), None) # Test with None

@patch('inventory_reports.models.GlobalContext')
class GoodsRejectionNoteViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls, MockGlobalContext):
        # Setup mock global context
        mock_ctx = MockGlobalContext.return_value
        mock_ctx.get.side_effect = lambda key: {
            'compid': 1,
            'finyearid': 2023,
            'username': 'testuser'
        }.get(key)
        
        # Create minimal test data for related tables first
        TblfinancialMaster.objects.create(finyearid=2023, finyear='2023-24')
        TblfinancialMaster.objects.create(finyearid=2022, finyear='2022-23')

        TblmmSupplierMaster.objects.create(supplierid='SUP001', suppliername='Test Supplier A', compid=1)
        TblmmSupplierMaster.objects.create(supplierid='SUP002', suppliername='Test Supplier B', compid=1)

        TblmmPoMaster.objects.create(id=101, supplierid='SUP001', compid=1)
        TblmmPoMaster.objects.create(id=102, supplierid='SUP002', compid=1)

        TblinvInwardMaster.objects.create(id=201, pono='PO-001', poid=101, challanno='CH-001', challandate=datetime(2023, 1, 1), compid=1)
        TblinvInwardMaster.objects.create(id=202, pono='PO-002', poid=102, challanno='CH-002', challandate=datetime(2023, 1, 2), compid=1)

        TblinvMaterialreceivedMaster.objects.create(id=301, grrno='GRR-001', ginno='GIN-001', ginid=201, compid=1)
        TblinvMaterialreceivedMaster.objects.create(id=302, grrno='GRR-002', ginno='GIN-002', ginid=202, compid=1)

        # Create GoodsRejectionNote objects
        cls.grn1 = GoodsRejectionNote.objects.create(
            id=1, finyearid=2023, gqnno='GQN-001', grrno='GRN-001', sysdate=datetime(2023, 1, 10), grrid=301, compid=1
        )
        TblqcMaterialqualityDetails.objects.create(id=1001, mid=cls.grn1.id, rejectedqty=5.0)
        
        cls.grn2 = GoodsRejectionNote.objects.create(
            id=2, finyearid=2023, gqnno='GQN-002', grrno='GRN-002', sysdate=datetime(2023, 1, 11), grrid=302, compid=1
        )
        TblqcMaterialqualityDetails.objects.create(id=1002, mid=cls.grn2.id, rejectedqty=10.0)

    def setUp(self):
        self.client = Client()

    def test_list_view_get(self, MockGlobalContext):
        response = self.client.get(reverse('inventory_reports:grn_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/goodsrejectionnote/list.html')
        self.assertIsInstance(response.context['form'], GoodsRejectionNoteSearchForm)
        self.assertIn('goods_rejection_notes', response.context)
        # Check if the initial data contains expected GRNs
        self.assertEqual(len(response.context['goods_rejection_notes']), 2)

    def test_list_view_post_search(self, MockGlobalContext):
        # Simulate a POST request for search
        data = {
            'search_type': '1', # GQN No
            'search_text_input': 'GQN-001',
            'supplier_text_input': '', # Ensure other fields are present if x-show hides them
            'supplier_id': '',
        }
        response = self.client.post(reverse('inventory_reports:grn_list'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/goodsrejectionnote/_goodsrejectionnote_table.html')
        self.assertContains(response, 'GQN-001')
        self.assertNotContains(response, 'GQN-002')

    def test_list_view_post_supplier_search(self, MockGlobalContext):
        data = {
            'search_type': '0', # Supplier Name
            'supplier_text_input': 'Test Supplier A [SUP001]',
            'supplier_id': 'SUP001',
            'search_text_input': '',
        }
        response = self.client.post(reverse('inventory_reports:grn_list'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/goodsrejectionnote/_goodsrejectionnote_table.html')
        self.assertContains(response, 'Test Supplier A')
        self.assertNotContains(response, 'Test Supplier B')

    def test_table_partial_view_get(self, MockGlobalContext):
        # This view is typically called via HTMX GET after the initial page load
        response = self.client.get(reverse('inventory_reports:grn_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/goodsrejectionnote/_goodsrejectionnote_table.html')
        self.assertIn('goods_rejection_notes', response.context)
        self.assertEqual(len(response.context['goods_rejection_notes']), 2)

    def test_table_partial_view_post_search(self, MockGlobalContext):
        # This simulates a direct HTMX POST to the table endpoint, which is less common
        # but could happen if the form targets it.
        data = {
            'search_type': '1', # GQN No
            'search_text_input': 'GQN-001',
            'supplier_text_input': '',
            'supplier_id': '',
        }
        response = self.client.post(reverse('inventory_reports:grn_table'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/goodsrejectionnote/_goodsrejectionnote_table.html')
        self.assertContains(response, 'GQN-001')
        self.assertNotContains(response, 'GQN-002')

    def test_supplier_autocomplete_view(self, MockGlobalContext):
        response = self.client.get(reverse('inventory_reports:supplier_autocomplete'), {'q': 'test sup'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = response.json()
        self.assertIn('Test Supplier A [SUP001]', data)
        self.assertIn('Test Supplier B [SUP002]', data)

    def test_create_view_get(self, MockGlobalContext):
        response = self.client.get(reverse('inventory_reports:grn_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/goodsrejectionnote/_goodsrejectionnote_form.html')
        self.assertTrue('form' in response.context)

    def test_create_view_post_htmx(self, MockGlobalContext):
        data = {
            'gqnno': 'NEW-GQN-005',
            'grrno': 'NEW-GRN-005',
            'sysdate': '2023-05-15',
            'finyearid': 2023
        }
        response = self.client.post(reverse('inventory_reports:grn_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshGoodsRejectionNoteList')
        # Check if the object was created (need to mock the related GRR, GIN, PO for full manager test)
        # For this test, we just check if it's in the GoodsRejectionNote table.
        self.assertTrue(GoodsRejectionNote.objects.filter(gqnno='NEW-GQN-005').exists())

    def test_update_view_get(self, MockGlobalContext):
        response = self.client.get(reverse('inventory_reports:grn_edit', args=[self.grn1.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/goodsrejectionnote/_goodsrejectionnote_form.html')
        self.assertEqual(response.context['form'].instance.id, self.grn1.id)

    def test_update_view_post_htmx(self, MockGlobalContext):
        data = {
            'gqnno': 'GQN-001-UPDATED',
            'grrno': 'GRN-001-UPDATED',
            'sysdate': '2023-01-10',
            'finyearid': 2023
        }
        response = self.client.post(reverse('inventory_reports:grn_edit', args=[self.grn1.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshGoodsRejectionNoteList')
        self.grn1.refresh_from_db()
        self.assertEqual(self.grn1.gqnno, 'GQN-001-UPDATED')

    def test_delete_view_get(self, MockGlobalContext):
        response = self.client.get(reverse('inventory_reports:grn_delete', args=[self.grn1.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/goodsrejectionnote/_goodsrejectionnote_confirm_delete.html')
        self.assertEqual(response.context['object'].id, self.grn1.id)

    def test_delete_view_post_htmx(self, MockGlobalContext):
        response = self.client.delete(reverse('inventory_reports:grn_delete', args=[self.grn1.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshGoodsRejectionNoteList')
        self.assertFalse(GoodsRejectionNote.objects.filter(id=self.grn1.id).exists())

    def test_select_view_get_htmx_redirect(self, MockGlobalContext):
        response = self.client.get(reverse('inventory_reports:grn_select', args=[self.grn1.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX redirect
        self.assertIn('HX-Redirect', response.headers)
        self.assertTrue(response.headers['HX-Redirect'].startswith(reverse('inventory_reports:grn_print_details', kwargs={'pk': self.grn1.id})))
        self.assertIn('SupId=SUP001', response.headers['HX-Redirect'])
        self.assertIn('GRRNo=GRN-001', response.headers['HX-Redirect'])

    def test_print_details_view(self, MockGlobalContext):
        response = self.client.get(reverse('inventory_reports:grn_print_details', kwargs={'pk': self.grn1.id}))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, f"Details for GRN ID: {self.grn1.id}")

```

### Step 5: HTMX and Alpine.js Integration

**Summary of Implementation:**
*   **HTMX for dynamic updates:**
    *   The main list view (`list.html`) uses `hx-get` to load the table partial (`_goodsrejectionnote_table.html`) on initial `load` and `refreshGoodsRejectionNoteList` event.
    *   The search form uses `hx-post` to submit filters, targeting the `grnTable-container` to swap its `innerHTML` with the updated table partial.
    *   The `search_type` dropdown uses `hx-post` on `change` to trigger an immediate form submission and table refresh, allowing dynamic visibility of `search_text` vs `supplier_text`.
    *   Autocomplete for `supplier_text` uses `hx-get` to `supplier_autocomplete` view on `keyup changed delay:500ms`, targeting `supplier-suggestions`.
    *   CRUD operations (Add/Edit/Delete) use `hx-get` to load their respective forms/confirmations into a modal via `hx-target="#modalContent"`, and `hx-post`/`hx-delete` for submission, returning `204 No Content` with `HX-Trigger` to refresh the main list.
    *   The "Select" button uses `hx-get` and `HX-Redirect` header to navigate to the print details page without a full page reload if HTMX is active.

*   **Alpine.js for UI state management:**
    *   An `x-data` block on the search form manages the `searchType` variable, which controls the `x-show` visibility of `search_text` and `supplier_text` input fields.
    *   `supplierSearch` and `selectedSupplierId` Alpine variables handle the supplier autocomplete input and its corresponding hidden ID.
    *   Alpine `@click` is used on autocomplete suggestions to populate the input and hidden ID.
    *   The modal (`#modal`) uses Alpine.js `x-data` and `hidden` attribute with `is-active` class toggling for visibility.

*   **DataTables for list views:**
    *   The `_goodsrejectionnote_table.html` partial contains the `<table>` element.
    *   A JavaScript block within this partial (executed when HTMX injects it) initializes DataTables on the table ID (`#goodsrejectionnoteTable`), ensuring proper client-side pagination, sorting, and searching. `destroy: true` is crucial for re-initialization after HTMX swaps the content.

*   **DRY Template Inheritance:**
    *   All templates extend `core/base.html` to inherit common headers, footers, and CDN links (HTMX, Alpine.js, jQuery, DataTables, Tailwind CSS). `base.html` itself is not included in the output.

**Business Benefits and Outcomes:**

1.  **Enhanced User Experience:** The transition to HTMX and Alpine.js creates a highly responsive, interactive web application. Users will experience instant feedback on search filters, form submissions, and data updates without frustrating full-page reloads, akin to a Single Page Application (SPA) but with a much simpler development model. DataTables provides advanced client-side features for large datasets, improving usability.

2.  **Reduced Server Load:** By offloading much of the UI logic and interactions to the client-side with HTMX and Alpine.js, the server only sends back minimal HTML fragments or JSON data, significantly reducing bandwidth consumption and server processing.

3.  **Simplified Development & Maintenance:** Django's "Fat Model, Thin View" pattern centralizes business logic, making the codebase more organized, easier to understand, and less prone to errors. Using `managed = False` models allows gradual modernization without immediate database schema changes.

4.  **Improved Performance & Scalability:** Django's ORM and efficient handling of database connections, combined with the lightweight frontend, lead to a faster and more scalable application compared to the legacy ASP.NET setup.

5.  **Robustness and Reliability:** Comprehensive unit and integration tests (aiming for 80%+ coverage) ensure the reliability and correctness of the migrated functionality, catching regressions early in the development cycle.

6.  **Future-Proofing:** Moving to a modern, open-source framework like Django ensures the application is built on widely supported technologies with a vibrant community, facilitating future enhancements and integrations.

This comprehensive plan, driven by AI-assisted automation, sets a clear path for the effective modernization of the Goods Rejection Note report, delivering immediate value in terms of user experience and system efficiency.