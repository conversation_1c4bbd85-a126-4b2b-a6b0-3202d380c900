## ASP.NET to Django Conversion Script: Item Process Module

This document outlines a modernization plan to transition your existing ASP.NET "Material Process" module to a modern Django-based solution. Our approach prioritizes automation, leveraging AI-assisted tools to streamline the conversion process, ensuring a robust, maintainable, and scalable application.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET `GridView` and its code-behind reveal operations on `tblPln_Process_Master`. The `SqlDataSource2` and `GridView1_RowEditing` also indicate a dependency on `Unit_Master` for a dropdown list.

**Inferred Schema:**
*   **`tblPln_Process_Master`**:
    *   `Id` (Primary Key, integer)
    *   `ProcessName` (string, required)
    *   `Symbol` (string, required)
    *   `UOMBasic` (integer, Foreign Key to `Unit_Master.Id` - inferred from `GridView1_RowEditing` and `DDLBasic`)
*   **`Unit_Master`**:
    *   `Id` (Primary Key, integer)
    *   `Symbol` (string - inferred from `DataTextField="Symbol"` in `DDLBasic`)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Analysis:**
The `GridView1` control and its associated event handlers (`OnRowCommand`, `onrowupdating`, `onrowdeleting`, `onpageindexchanging`, `onrowediting`) directly manage CRUD operations.

*   **Create (Add):** Handled by `GridView1_RowCommand` for `CommandName="Add"` (footer insert) and `CommandName="Add1"` (empty data template insert). It takes `ProcessName` and `Symbol`.
*   **Read (Load):** Handled by `LoadData()`, which fetches `Id`, `ProcessName`, and `Symbol` from `tblPln_Process_Master` and binds them to the `GridView`. Pagination is handled via `GridView1_PageIndexChanging`.
*   **Update (Edit):** Handled by `GridView1_RowUpdating`. It retrieves `Id`, `ProcessName`, and `Symbol` from the edited row. There's also logic in `GridView1_RowEditing` to pre-select a `UOMBasic` value from `Unit_Master` for the edited row.
*   **Delete (Del):** Handled by `GridView1_RowCommand` for `CommandName="Del"`. It uses the `Id` to delete the record.
*   **Validation:** `RequiredFieldValidator` ensures `ProcessName` and `Symbol` are not empty on insert and update.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**
The primary UI component is the `asp:GridView`.

*   **`asp:GridView`**: This will be replaced by a modern HTML `<table>` combined with the DataTables.js library for client-side search, sort, and pagination.
    *   **Display:** `ProcessName` and `Symbol` are shown. `Id` is hidden.
    *   **Edit/Delete Buttons:** These will be transformed into HTMX-enabled buttons that trigger modals for edit and delete actions.
    *   **Insert Form (Footer/EmptyDataTemplate):** These will be integrated into a single, reusable Django form that appears in a modal for adding new records.
*   **`asp:TextBox` (for input fields like `txtPName`, `txtSymbol`, `txtProcessName1`, `txtSymbol1`, `txtName`):** These will become standard `<input type="text">` fields within Django forms, styled with Tailwind CSS.
*   **`asp:DropDownList` (`DDLBasic`):** This will be a standard HTML `<select>` element, populated by Django's `forms.ModelChoiceField` for the `UOMBasic` foreign key.
*   **`asp:Button`, `asp:LinkButton`:** These action triggers will be transformed into HTML `<button>` elements with HTMX attributes to handle dynamic interactions (e.g., opening modals, submitting forms, triggering table refreshes).
*   **Client-Side JavaScript (`PopUpMsg.js`, `loadingNotifier.js`, `confirmationAdd()`, `confirmationDelete()`):** These will be replaced by HTMX's declarative approach for AJAX requests and Alpine.js for simple UI state management (like showing/hiding modals) and interactive confirmations. Django's messages framework will handle notifications.

### Step 4: Generate Django Code

We will create a new Django application, let's call it `material_planning`, to encapsulate this module.

#### 4.1 Models (`material_planning/models.py`)

**Task:** Create Django models based on the identified database schema.

**Instructions:**
We define `ProcessMaster` and `UnitMaster` models, mapping them to the existing database tables. The `UOMBasic` field in `ProcessMaster` will be a `ForeignKey` to `UnitMaster`. Business logic related to these entities will reside here.

```python
from django.db import models
from django.core.exceptions import ValidationError

class UnitMaster(models.Model):
    """
    Represents the Unit Master from the existing Unit_Master table.
    Used for UOMBasic in ProcessMaster.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'Unit_Master'
        verbose_name = 'Unit Master'
        verbose_name_plural = 'Unit Masters'

    def __str__(self):
        return self.symbol or f"Unit #{self.id}"

class ProcessMaster(models.Model):
    """
    Represents the Process Master from the existing tblPln_Process_Master table.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    process_name = models.CharField(db_column='ProcessName', max_length=255, verbose_name="Name of Process")
    symbol = models.CharField(db_column='Symbol', max_length=50, verbose_name="Symbol")
    uom_basic = models.ForeignKey(
        UnitMaster, 
        models.DO_NOTHING, 
        db_column='UOMBasic', 
        blank=True, 
        null=True,
        verbose_name="UOM Basic"
    )

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblPln_Process_Master'
        verbose_name = 'Process Master'
        verbose_name_plural = 'Process Masters'

    def __str__(self):
        return self.process_name

    def clean(self):
        """
        Custom validation for ProcessMaster.
        Example: Ensure ProcessName and Symbol are not empty.
        """
        if not self.process_name:
            raise ValidationError({'process_name': 'Process Name cannot be empty.'})
        if not self.symbol:
            raise ValidationError({'symbol': 'Symbol cannot be empty.'})
            
    # Business logic methods related to Process Master can be added here
    # Example: Check if a process can be deleted
    def can_delete(self):
        # Implement logic to check for related records before deletion
        # e.g., if self.related_items.exists(): return False
        return True
```

#### 4.2 Forms (`material_planning/forms.py`)

**Task:** Define a Django form for user input for `ProcessMaster`.

**Instructions:**
We'll create a `ModelForm` for `ProcessMaster`, including `ProcessName`, `Symbol`, and `UOMBasic`. Widgets will be configured with Tailwind CSS classes.

```python
from django import forms
from .models import ProcessMaster, UnitMaster

class ProcessMasterForm(forms.ModelForm):
    # DDLBasic equivalent: UOMBasic dropdown
    uom_basic = forms.ModelChoiceField(
        queryset=UnitMaster.objects.all(),
        required=False, # Based on original ASP.NET code, DDLBasic might not be mandatory
        label="UOM Basic",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    class Meta:
        model = ProcessMaster
        fields = ['process_name', 'symbol', 'uom_basic']
        widgets = {
            'process_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'symbol': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        
    def clean(self):
        cleaned_data = super().clean()
        process_name = cleaned_data.get('process_name')
        symbol = cleaned_data.get('symbol')

        # Mimic RequiredFieldValidator behavior
        if not process_name:
            self.add_error('process_name', 'Process Name is required.')
        if not symbol:
            self.add_error('symbol', 'Symbol is required.')
            
        return cleaned_data
```

#### 4.3 Views (`material_planning/views.py`)

**Task:** Implement CRUD operations using Django Class-Based Views (CBVs), ensuring thin views and HTMX responses.

**Instructions:**
We define `ListView`, `CreateView`, `UpdateView`, and `DeleteView` for `ProcessMaster`. A separate `ProcessMasterTablePartialView` will handle the HTMX rendering of the DataTables.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import render, get_object_or_404
from .models import ProcessMaster, UnitMaster
from .forms import ProcessMasterForm
from django.db import IntegrityError

class ProcessMasterListView(ListView):
    """
    Displays the main page for Process Masters, which will load the table via HTMX.
    """
    model = ProcessMaster
    template_name = 'material_planning/processmaster/list.html'
    context_object_name = 'process_masters'

class ProcessMasterTablePartialView(ListView):
    """
    Renders only the DataTables table for Process Masters, intended for HTMX requests.
    """
    model = ProcessMaster
    template_name = 'material_planning/processmaster/_processmaster_table.html'
    context_object_name = 'process_masters'
    
    def get_queryset(self):
        # Original ASP.NET LoadData had "Id!='0'" filter, which selects all.
        # This queryset will return all ProcessMaster objects.
        return super().get_queryset().order_by('id')

class ProcessMasterCreateView(CreateView):
    """
    Handles creation of new Process Master records. Responds to HTMX requests.
    """
    model = ProcessMaster
    form_class = ProcessMasterForm
    template_name = 'material_planning/processmaster/_processmaster_form.html'
    success_url = reverse_lazy('processmaster_list') # Fallback for non-HTMX requests

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass UnitMaster objects to the form if needed for custom rendering, though ModelChoiceField handles it
        context['units'] = UnitMaster.objects.all() # Example for direct template access if form widget wasn't enough
        return context

    def form_valid(self, form):
        try:
            # Business logic (e.g., ID generation or other prep) could be here,
            # but for simple CRUD, model.save() handles it.
            # In ASP.NET, 'Id' was retrieved and used from DataKeyNames, so we assume
            # the database handles ID assignment for new inserts.
            # If 'Id' is manually assigned, override save() in model or form.
            response = super().form_valid(form)
            messages.success(self.request, 'Process Master added successfully.')
            if self.request.headers.get('HX-Request'):
                # For HTMX, send a 204 No Content and trigger a refresh event
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshProcessMasterList'
                    }
                )
            return response
        except IntegrityError:
            form.add_error(None, "A record with these details already exists or ID conflict.")
            return self.form_invalid(form)


    def form_invalid(self, form):
        # If form is invalid, re-render the form with errors
        if self.request.headers.get('HX-Request'):
            # For HTMX, re-render the form partial with errors
            return render(self.request, self.template_name, {'form': form})
        return super().form_invalid(form)


class ProcessMasterUpdateView(UpdateView):
    """
    Handles updating existing Process Master records. Responds to HTMX requests.
    """
    model = ProcessMaster
    form_class = ProcessMasterForm
    template_name = 'material_planning/processmaster/_processmaster_form.html'
    context_object_name = 'process_master'
    success_url = reverse_lazy('processmaster_list') # Fallback for non-HTMX requests

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pre-select UOMBasic if needed, though form_class handles it
        return context

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Process Master updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshProcessMasterList'
                }
            )
        return response

    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form})
        return super().form_invalid(form)


class ProcessMasterDeleteView(DeleteView):
    """
    Handles deletion of Process Master records. Responds to HTMX requests.
    """
    model = ProcessMaster
    template_name = 'material_planning/processmaster/_processmaster_confirm_delete.html'
    context_object_name = 'process_master'
    success_url = reverse_lazy('processmaster_list') # Fallback for non-HTMX requests

    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        # Add business logic for deletion conditions here (fat model approach)
        if not self.object.can_delete(): # Example: Call a method on the model
            messages.error(self.request, 'This Process Master cannot be deleted due to existing dependencies.')
            if request.headers.get('HX-Request'):
                return HttpResponse(status=400, headers={'HX-Trigger': 'showMessage'}) # Trigger a message display
            return HttpResponseRedirect(self.get_success_url())

        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Process Master deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshProcessMasterList'
                }
            )
        return response
```

#### 4.4 Templates (`material_planning/templates/material_planning/processmaster/`)

**Task:** Create HTML templates for each view, leveraging DRY principles, HTMX, and DataTables.

**Instructions:**
`list.html` will be the main page. `_processmaster_table.html`, `_processmaster_form.html`, and `_processmaster_confirm_delete.html` will be partials loaded via HTMX.

**1. `list.html`**

```html
{% extends 'core/base.html' %}

{% block title %}Material Process{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Material Processes</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'processmaster_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Process
        </button>
    </div>
    
    <div id="processmasterTable-container"
         hx-trigger="load, refreshProcessMasterList from:body"
         hx-get="{% url 'processmaster_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg overflow-hidden">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="p-6 text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Process Masters...</p>
        </div>
    </div>
    
    <!-- Modal for form/confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ showModal: false }" x-show="showModal"
         x-on:close-modal.window="showModal = false">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4"
             x-on:htmx:after-request="if ($event.detail.xhr.status === 204 || $event.detail.xhr.status === 400) showModal = false;">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('modalController', () => ({
            showModal: false,
            openModal() { this.showModal = true; },
            closeModal() { this.showModal = false; }
        }));
    });

    // Helper for DataTables re-initialization if needed
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'processmasterTable-container') {
            $('#processmasterTable').DataTable({
                "pageLength": 15, // Matches original PageSize
                "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
                "destroy": true, // Destroy existing instance if any
                "language": {
                    "emptyTable": "No Material Processes found. Click 'Add New Process' to create one."
                }
            });
        }
    });

    // For showing Django messages from HX-Trigger
    document.body.addEventListener('showMessage', function(evt) {
        // This is a placeholder for a more sophisticated message display system
        // You might use Alpine.js, Toastr.js, etc. for actual message display
        console.log("Message triggered:", evt.detail.xhr.getResponseHeader('HX-Trigger'));
        // Example: If a message div exists
        const messagesDiv = document.getElementById('messages');
        if (messagesDiv) {
            messagesDiv.innerHTML = 'Success!'; // Replace with actual message logic
        }
    });
</script>
{% endblock %}
```

**2. `_processmaster_table.html` (Partial for DataTable)**

```html
<table id="processmasterTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Process</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM Basic</th>
            <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in process_masters %}
        <tr class="hover:bg-gray-50">
            <td class="py-3 px-4 whitespace-nowrap text-right text-sm text-gray-700">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-left text-sm text-gray-700">{{ obj.process_name }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-center text-sm text-gray-700">{{ obj.symbol }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-center text-sm text-gray-700">{{ obj.uom_basic.symbol|default:"-" }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm font-medium text-center">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md text-xs mr-2 transition duration-150 ease-in-out"
                    hx-get="{% url 'processmaster_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-xs transition duration-150 ease-in-out"
                    hx-get="{% url 'processmaster_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <!-- This empty state is handled by DataTables language option,
             but can also be a simple message for non-JS scenarios -->
        {% endfor %}
    </tbody>
</table>

<script>
// DataTables initialization handled in parent template's htmx:afterSwap event listener.
// This ensures it re-initializes correctly each time the table partial is swapped in.
</script>
```

**3. `_processmaster_form.html` (Partial for Create/Update Form)**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add New' }} Material Process</h3>
    <form hx-post="{{ request.path }}" 
          hx-swap="outerHTML" 
          hx-target="#modalContent" 
          hx-trigger="submit">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-600 text-xs mt-1">{{ field.errors|join:", " }}</p>
                {% endif %}
            </div>
            {% endfor %}
            {% if form.non_field_errors %}
            <div class="text-red-600 text-sm mt-2">
                {{ form.non_field_errors }}
            </div>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-md shadow-sm transition duration-150 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

**4. `_processmaster_confirm_delete.html` (Partial for Delete Confirmation)**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-red-700 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the process "<span class="font-medium">{{ process_master.process_name }}</span>"?</p>
    <form hx-post="{% url 'processmaster_delete' process_master.pk %}" 
          hx-swap="none" 
          hx-trigger="submit"
          hx-confirm="This action cannot be undone. Confirm deletion?">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-5 rounded-md shadow-sm transition duration-150 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`material_planning/urls.py`)

**Task:** Define URL patterns for the views.

**Instructions:**
We'll map the main list view, the table partial, and the CRUD operation endpoints.

```python
from django.urls import path
from .views import (
    ProcessMasterListView, 
    ProcessMasterTablePartialView,
    ProcessMasterCreateView, 
    ProcessMasterUpdateView, 
    ProcessMasterDeleteView
)

urlpatterns = [
    # Main page for Process Masters
    path('processmaster/', ProcessMasterListView.as_view(), name='processmaster_list'),
    
    # HTMX endpoint for refreshing the DataTables table
    path('processmaster/table/', ProcessMasterTablePartialView.as_view(), name='processmaster_table'),

    # HTMX endpoint for displaying and submitting the Add form
    path('processmaster/add/', ProcessMasterCreateView.as_view(), name='processmaster_add'),
    
    # HTMX endpoint for displaying and submitting the Edit form
    path('processmaster/edit/<int:pk>/', ProcessMasterUpdateView.as_view(), name='processmaster_edit'),
    
    # HTMX endpoint for displaying and submitting the Delete confirmation
    path('processmaster/delete/<int:pk>/', ProcessMasterDeleteView.as_view(), name='processmaster_delete'),
]
```

#### 4.6 Tests (`material_planning/tests.py`)

**Task:** Write comprehensive tests for the models and views to ensure functionality and maintainability.

**Instructions:**
Include unit tests for model methods and properties, and integration tests for all view interactions, particularly focusing on HTMX responses.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from .models import ProcessMaster, UnitMaster
from django.db.utils import IntegrityError

class UnitMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        UnitMaster.objects.create(id=1, symbol='KG')
        UnitMaster.objects.create(id=2, symbol='MTR')
        
    def test_unitmaster_creation(self):
        unit = UnitMaster.objects.get(id=1)
        self.assertEqual(unit.symbol, 'KG')
        self.assertEqual(str(unit), 'KG')
        
    def test_symbol_label(self):
        unit = UnitMaster.objects.get(id=1)
        field_label = unit._meta.get_field('symbol').verbose_name
        self.assertEqual(field_label, 'Symbol')

class ProcessMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dependent data
        cls.unit_kg = UnitMaster.objects.create(id=1, symbol='KG')
        cls.unit_mtr = UnitMaster.objects.create(id=2, symbol='MTR')

        # Create test data for all tests
        ProcessMaster.objects.create(
            id=101,
            process_name='Cutting',
            symbol='CUT',
            uom_basic=cls.unit_kg
        )
        ProcessMaster.objects.create(
            id=102,
            process_name='Stitching',
            symbol='STC',
            uom_basic=cls.unit_mtr
        )
  
    def test_processmaster_creation(self):
        obj = ProcessMaster.objects.get(id=101)
        self.assertEqual(obj.process_name, 'Cutting')
        self.assertEqual(obj.symbol, 'CUT')
        self.assertEqual(obj.uom_basic, self.unit_kg)
        self.assertEqual(str(obj), 'Cutting') # Test __str__ method
        
    def test_process_name_label(self):
        obj = ProcessMaster.objects.get(id=101)
        field_label = obj._meta.get_field('process_name').verbose_name
        self.assertEqual(field_label, 'Name of Process')
        
    def test_symbol_label(self):
        obj = ProcessMaster.objects.get(id=101)
        field_label = obj._meta.get_field('symbol').verbose_name
        self.assertEqual(field_label, 'Symbol')

    def test_uom_basic_label(self):
        obj = ProcessMaster.objects.get(id=101)
        field_label = obj._meta.get_field('uom_basic').verbose_name
        self.assertEqual(field_label, 'UOM Basic')

    def test_clean_method_requires_fields(self):
        obj = ProcessMaster(process_name='', symbol='SYM', uom_basic=self.unit_kg)
        with self.assertRaisesMessage(IntegrityError, 'Process Name is required.'): # For model's clean method
             # The error will be raised by forms when saving, if model's clean() is called directly, use ValidationError
            obj.full_clean() # Calling model validation
        
        obj_no_symbol = ProcessMaster(process_name='Test', symbol='', uom_basic=self.unit_kg)
        with self.assertRaisesMessage(IntegrityError, 'Symbol is required.'): # For model's clean method
            obj_no_symbol.full_clean()
        
    def test_can_delete_method(self):
        obj = ProcessMaster.objects.get(id=101)
        # Assuming no dependencies for now, so it should be true
        self.assertTrue(obj.can_delete())
        # Add a test case for false if can_delete logic is more complex
        # e.g., if you had a mock related object:
        # with mock.patch.object(obj.related_items, 'exists', return_value=True):
        #    self.assertFalse(obj.can_delete())


class ProcessMasterViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dependent data
        cls.unit_kg = UnitMaster.objects.create(id=1, symbol='KG')
        cls.unit_mtr = UnitMaster.objects.create(id=2, symbol='MTR')

        # Create test data for all tests
        ProcessMaster.objects.create(
            id=101,
            process_name='Cutting',
            symbol='CUT',
            uom_basic=cls.unit_kg
        )
        ProcessMaster.objects.create(
            id=102,
            process_name='Stitching',
            symbol='STC',
            uom_basic=cls.unit_mtr
        )
    
    def setUp(self):
        self.client = Client()
        # Add any necessary user authentication if views are protected
        # self.client.login(username='testuser', password='testpassword')
    
    def test_list_view(self):
        response = self.client.get(reverse('processmaster_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/processmaster/list.html')
        # Check that the main list view doesn't directly contain the table content yet
        self.assertContains(response, 'id="processmasterTable-container"')
        
    def test_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('processmaster_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/processmaster/_processmaster_table.html')
        self.assertTrue('process_masters' in response.context)
        self.assertEqual(len(response.context['process_masters']), 2)
        self.assertContains(response, 'Cutting')
        self.assertContains(response, 'STC')
        
    def test_create_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('processmaster_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/processmaster/_processmaster_form.html')
        self.assertTrue('form' in response.context)
        
    def test_create_view_post_htmx_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'process_name': 'Washing',
            'symbol': 'WSH',
            'uom_basic': self.unit_kg.id, # Send ID of existing UnitMaster
            'id': 103 # Assuming 'Id' is auto-generated or managed by DB for new records,
                     # otherwise, if ID is provided manually, include it in data.
                     # Django's ModelForm usually handles PK unless specified.
                     # For managed=False with int PK, you might need to manually set it in createview.
                     # But for simplicity, we'll assume DB auto-increments or a mechanism.
        }
        # To make this test pass for unmanaged ID field, we must create a unique ID manually for test
        ProcessMaster.objects.filter(id=103).delete() # Ensure no conflict
        data['id'] = 103 # For testing purposes if ID is manually provided
        
        response = self.client.post(reverse('processmaster_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX success code
        self.assertEqual(response['HX-Trigger'], 'refreshProcessMasterList')
        self.assertTrue(ProcessMaster.objects.filter(process_name='Washing').exists())
        
    def test_create_view_post_htmx_invalid(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'process_name': '', # Invalid data
            'symbol': 'INV'
        }
        response = self.client.post(reverse('processmaster_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # HTMX re-renders form on error
        self.assertTemplateUsed(response, 'material_planning/processmaster/_processmaster_form.html')
        self.assertContains(response, 'Process Name is required.')
        self.assertFalse(ProcessMaster.objects.filter(symbol='INV').exists()) # Object not created
        
    def test_update_view_get_htmx(self):
        obj = ProcessMaster.objects.get(id=101)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('processmaster_edit', args=[obj.id]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/processmaster/_processmaster_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, obj)
        
    def test_update_view_post_htmx_success(self):
        obj = ProcessMaster.objects.get(id=101)
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'process_name': 'New Cutting Name',
            'symbol': 'NCUT',
            'uom_basic': self.unit_mtr.id,
            'id': obj.id
        }
        response = self.client.post(reverse('processmaster_edit', args=[obj.id]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response['HX-Trigger'], 'refreshProcessMasterList')
        obj.refresh_from_db()
        self.assertEqual(obj.process_name, 'New Cutting Name')
        self.assertEqual(obj.symbol, 'NCUT')
        
    def test_update_view_post_htmx_invalid(self):
        obj = ProcessMaster.objects.get(id=101)
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'process_name': '', # Invalid data
            'symbol': ''
        }
        response = self.client.post(reverse('processmaster_edit', args=[obj.id]), data, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/processmaster/_processmaster_form.html')
        self.assertContains(response, 'Process Name is required.')
        self.assertContains(response, 'Symbol is required.')
        
    def test_delete_view_get_htmx(self):
        obj = ProcessMaster.objects.get(id=101)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('processmaster_delete', args=[obj.id]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/processmaster/_processmaster_confirm_delete.html')
        self.assertTrue('process_master' in response.context)
        self.assertEqual(response.context['process_master'], obj)
        
    def test_delete_view_post_htmx_success(self):
        obj = ProcessMaster.objects.get(id=101)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('processmaster_delete', args=[obj.id]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response['HX-Trigger'], 'refreshProcessMasterList')
        self.assertFalse(ProcessMaster.objects.filter(id=101).exists())
        
    def test_delete_view_post_htmx_failure_due_to_can_delete(self):
        obj = ProcessMaster.objects.get(id=102)
        # Temporarily make can_delete return False for this object
        original_can_delete = ProcessMaster.can_delete
        ProcessMaster.can_delete = lambda self: False
        
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('processmaster_delete', args=[obj.id]), **headers)
        
        self.assertEqual(response.status_code, 400) # Bad Request or other client error for HTMX
        self.assertEqual(response['HX-Trigger'], 'showMessage') # Expect a message trigger
        self.assertTrue(ProcessMaster.objects.filter(id=102).exists()) # Object should not be deleted
        
        # Restore original method
        ProcessMaster.can_delete = original_can_delete
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated templates and views are designed to integrate HTMX and Alpine.js seamlessly.

*   **HTMX for CRUD:**
    *   **List Refresh:** `hx-trigger="load, refreshProcessMasterList from:body"` on the table container ensures the table loads on page load and refreshes after any CRUD operation (`HX-Trigger: refreshProcessMasterList`).
    *   **Modal Loading:** Buttons for "Add", "Edit", and "Delete" use `hx-get` to fetch the form/confirmation partials into the `#modalContent` div.
    *   **Form Submission:** Forms within the modal use `hx-post`, `hx-swap="outerHTML"`, and `hx-target="#modalContent"` to re-render the form with validation errors if invalid, or `hx-swap="none"` and `HX-Trigger` on success (handled by Django views returning 204 No Content).
    *   **Delete Confirmation:** The delete form uses `hx-confirm` for a native browser confirmation before the request is sent, mimicking `OnClientClick` behavior.
*   **Alpine.js for Modals:**
    *   The `#modal` div uses `x-data="{ showModal: false }"` and `x-show="showModal"`.
    *   `on click add .is-active to #modal` (Hyperscript) is used to show the modal when HTMX buttons are clicked.
    *   `on click if event.target.id == 'modal' remove .is-active from me` closes the modal when clicking outside.
    *   `x-on:htmx:after-request="if ($event.detail.xhr.status === 204 || $event.detail.xhr.status === 400) showModal = false;"` hides the modal after successful form submission (204) or on certain errors (400) that mean the form doesn't need to stay open.
*   **DataTables:**
    *   The `_processmaster_table.html` partial contains the `<table>` element with `id="processmasterTable"`.
    *   A `<script>` block in `list.html` listens for `htmx:afterSwap` on the table container. When the table partial is loaded, it initializes `$('#processmasterTable').DataTable()`, ensuring proper client-side searching, sorting, and pagination. `destroy: true` is crucial for re-initialization.

---

## Final Notes

This comprehensive plan provides a clear roadmap for migrating the ASP.NET "Material Process" module to Django. By adhering to the 'Fat Model, Thin View' principle, utilizing HTMX and Alpine.js for dynamic frontend interactions, and implementing DataTables for efficient data presentation, your organization will gain a modern, performable, and maintainable application. The focus on AI-assisted automation and clear, step-by-step instructions ensures that this transition can be executed systematically and efficiently.