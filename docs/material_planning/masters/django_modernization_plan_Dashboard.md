## ASP.NET to Django Conversion Script:

This document outlines a strategic plan for modernizing your legacy ASP.NET application, specifically focusing on the "Material Planning Masters Dashboard" module, into a robust and maintainable Django-based solution. Our approach prioritizes AI-assisted automation, ensuring a systematic and efficient transition. We will leverage modern Django 5.0+ patterns, emphasizing a "Fat Model, Thin View" architecture, coupled with HTMX and Alpine.js for dynamic, reactive user interfaces without extensive custom JavaScript.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists as `core/base.html`.
- Focus ONLY on component-specific code for the current module (`material_planning`).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

The provided ASP.NET code for `Dashboard.aspx` and its C# code-behind is extremely minimal, indicating a basic page structure without explicit data operations or UI components within the snippet itself. The page inherits from a `MasterPage` and is part of `Module_MaterialPlanning_Masters_Dashboard`. This strongly suggests that this dashboard likely displays or summarizes master data related to "Material Planning."

Given the lack of specific details, we will **infer** a common scenario for a "Masters Dashboard" to be a listing and management (CRUD) interface for a core entity. We will proceed with the assumption that this dashboard manages **Material** master data.

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Since the provided ASP.NET code does not contain explicit database interactions (like `SqlDataSource` or direct SQL commands), we are inferring a schema based on the module name `Module_MaterialPlanning_Masters`. A common master data table for "Material Planning" would be `tblMaterial`.

**Inferred Database Table:** `tblMaterial`

**Inferred Columns:**
*   `MaterialID` (Primary Key, Integer)
*   `MaterialCode` (String, e.g., 'RAW_001')
*   `MaterialName` (String, e.g., 'Steel Rod')
*   `UnitOfMeasure` (String, e.g., 'KG', 'PCS')
*   `Description` (String, optional)
*   `IsActive` (Boolean)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

Based on the inference that this is a "Masters" module, the dashboard is expected to support full CRUD (Create, Read, Update, Delete) operations for `Material` records, even though no explicit controls are present in the provided ASP.NET code.

*   **Read:** Displaying a list of `Material` records.
*   **Create:** Adding new `Material` records.
*   **Update:** Modifying existing `Material` records.
*   **Delete:** Removing `Material` records.
*   **Validation:** Basic validation for required fields (e.g., `MaterialCode`, `MaterialName`, `UnitOfMeasure`).

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

In a typical ASP.NET "Masters" module, a `GridView` would be used to display the list of materials. Buttons and TextBoxes would facilitate adding, editing, and deleting records, possibly through a separate form or within the GridView itself.

*   **GridView:** To display the list of `Material` records.
*   **TextBoxes:** For `MaterialCode`, `MaterialName`, `UnitOfMeasure`, `Description`.
*   **Checkbox:** For `IsActive`.
*   **Buttons:** For "Add New Material," "Edit," and "Delete" actions.
*   **Client-Side Interactions:** Minimal `loadingNotifier.js` is present in the ASP.NET header. This suggests a simple loading indicator. We will replace this with HTMX loading states and Alpine.js for UI state management (e.g., modal visibility).

## Step 4: Generate Django Code

We will create a new Django application named `material_planning` to encapsulate this module's functionality.

### 4.1 Models

Task: Create a Django model based on the database schema.

## Instructions:

- Name the model `Material`.
- Define fields corresponding to the inferred `tblMaterial` columns.
- Set `managed = False` and `db_table = 'tblMaterial'` in the Meta class.
- Include a `__str__` method for better representation.
- Add a placeholder for business logic.

**File:** `material_planning/models.py`

```python
from django.db import models

class Material(models.Model):
    """
    Represents a Material master data record.
    Maps to an existing database table 'tblMaterial'.
    """
    material_id = models.IntegerField(db_column='MaterialID', primary_key=True)
    material_code = models.CharField(db_column='MaterialCode', max_length=100, unique=True, verbose_name="Material Code")
    material_name = models.CharField(db_column='MaterialName', max_length=255, verbose_name="Material Name")
    unit_of_measure = models.CharField(db_column='UnitOfMeasure', max_length=50, verbose_name="Unit of Measure")
    description = models.TextField(db_column='Description', blank=True, null=True, verbose_name="Description")
    is_active = models.BooleanField(db_column='IsActive', default=True, verbose_name="Is Active")

    class Meta:
        managed = False  # Important: Django will not create/delete this table
        db_table = 'tblMaterial'
        verbose_name = 'Material'
        verbose_name_plural = 'Materials'
        ordering = ['material_code'] # Default ordering for lists

    def __str__(self):
        """
        Returns the material name for string representation.
        """
        return self.material_name

    # Business logic methods (examples):
    # def can_be_used_in_production(self):
    #     """
    #     Determines if this material can be used in production based on its status.
    #     """
    #     return self.is_active
    #
    # def get_full_details(self):
    #     """
    #     Returns a formatted string with material code and name.
    #     """
    #     return f"{self.material_code} - {self.material_name} ({self.unit_of_measure})"
```

### 4.2 Forms

Task: Define a Django form for user input.

## Instructions:

- Create a `ModelForm` for `Material`.
- Include all editable fields.
- Add widgets with Tailwind CSS classes for styling.
- Implement any specific validation (though none inferred beyond basic `max_length`).

**File:** `material_planning/forms.py`

```python
from django import forms
from .models import Material

class MaterialForm(forms.ModelForm):
    """
    Form for creating and updating Material records.
    """
    class Meta:
        model = Material
        fields = ['material_code', 'material_name', 'unit_of_measure', 'description', 'is_active']
        widgets = {
            'material_code': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter material code'}),
            'material_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter material name'}),
            'unit_of_measure': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'e.g., KG, PCS'}),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3, 'placeholder': 'Optional description'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded'}),
        }
        labels = {
            'material_code': 'Material Code',
            'material_name': 'Material Name',
            'unit_of_measure': 'Unit of Measure',
            'description': 'Description',
            'is_active': 'Is Active',
        }
        
    def clean_material_code(self):
        """
        Custom validation for material code to ensure uniqueness during updates.
        """
        material_code = self.cleaned_data['material_code']
        # Check if a material with this code already exists, excluding the current instance if updating.
        query = Material.objects.filter(material_code__iexact=material_code)
        if self.instance.pk: # If updating an existing instance
            query = query.exclude(pk=self.instance.pk)
        if query.exists():
            raise forms.ValidationError("This Material Code already exists.")
        return material_code
```

### 4.3 Views

Task: Implement CRUD operations using CBVs.

## Instructions:

- Define `MaterialListView`, `MaterialCreateView`, `MaterialUpdateView`, `MaterialDeleteView`.
- Add a `MaterialTablePartialView` to render just the DataTables portion via HTMX.
- Keep views thin (5-15 lines) by delegating logic to models or Django's built-in CBV functionalities.
- Implement HTMX `HX-Trigger` headers for seamless updates.

**File:** `material_planning/views.py`

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import Material
from .forms import MaterialForm

class MaterialListView(ListView):
    """
    Displays a list of all Material records.
    The actual table content is loaded via HTMX in MaterialTablePartialView.
    """
    model = Material
    template_name = 'material_planning/material/list.html'
    context_object_name = 'materials' # Not directly used in list.html, but in _material_table.html via HTMX

class MaterialTablePartialView(ListView):
    """
    Renders only the DataTables table content for Materials,
    intended to be fetched via HTMX.
    """
    model = Material
    template_name = 'material_planning/material/_material_table.html'
    context_object_name = 'materials' # This will be the queryset available in the partial template

class MaterialCreateView(CreateView):
    """
    Handles creation of new Material records.
    Renders form, handles POST, and triggers HTMX event on success.
    """
    model = Material
    form_class = MaterialForm
    template_name = 'material_planning/material/_material_form.html' # This is a partial template
    success_url = reverse_lazy('material_list') # Not strictly needed with HX-Trigger

    def form_valid(self, form):
        """
        Called when form is valid. Saves object and sends HTMX trigger.
        """
        response = super().form_valid(form)
        messages.success(self.request, 'Material added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return 204 No Content and trigger a refresh event
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialList'
                }
            )
        return response

class MaterialUpdateView(UpdateView):
    """
    Handles updating existing Material records.
    Renders form, handles POST, and triggers HTMX event on success.
    """
    model = Material
    form_class = MaterialForm
    template_name = 'material_planning/material/_material_form.html' # This is a partial template
    context_object_name = 'material' # Name for the instance in template
    success_url = reverse_lazy('material_list') # Not strictly needed with HX-Trigger

    def form_valid(self, form):
        """
        Called when form is valid. Saves object and sends HTMX trigger.
        """
        response = super().form_valid(form)
        messages.success(self.request, 'Material updated successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return 204 No Content and trigger a refresh event
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialList'
                }
            )
        return response

class MaterialDeleteView(DeleteView):
    """
    Handles deletion of Material records.
    Renders confirmation, handles POST, and triggers HTMX event on success.
    """
    model = Material
    template_name = 'material_planning/material/_material_confirm_delete.html' # This is a partial template
    context_object_name = 'material' # Name for the instance in template
    success_url = reverse_lazy('material_list') # Not strictly needed with HX-Trigger

    def delete(self, request, *args, **kwargs):
        """
        Called when delete is confirmed. Deletes object and sends HTMX trigger.
        """
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Material deleted successfully.')
        if request.headers.get('HX-Request'):
            # For HTMX requests, return 204 No Content and trigger a refresh event
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialList'
                }
            )
        return response

```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

- Create the main `list.html` which extends `core/base.html`.
- Create partial templates (`_material_table.html`, `_material_form.html`, `_material_confirm_delete.html`) for HTMX-driven content.
- Ensure DataTables initialization for the table partial.
- Use HTMX attributes for dynamic interactions and Alpine.js for modal logic.

**Directory Structure:** `material_planning/templates/material_planning/material/`

**File:** `material_planning/templates/material_planning/material/list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Materials Dashboard</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'material_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Material
        </button>
    </div>
    
    <div id="materialTable-container"
         hx-trigger="load, refreshMaterialList from:body"
         hx-get="{% url 'material_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg overflow-hidden">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="p-6 text-center">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Materials data...</p>
        </div>
    </div>
    
    <!-- Global Modal structure for HTMX forms -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ showModal: false }" x-show="showModal" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 scale-90" x-transition:enter-end="opacity-100 scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-90">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4"
             _="on htmx:afterSwap remove .is-active from #modal end"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states.
        // For simple modal open/close via HTMX, the _="on click" syntax is sufficient.
    });

    // Handle messages from Django (e.g., success messages after form submission)
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.xhr.status === 204) {
            // This means an HTMX request returned no content, often for successful form submissions
            // You might have a global toast/notification system activated by HX-Trigger
            // For now, Django's messages framework renders on the next full page load,
            // or we'd need a specific HTMX target for messages.
        }
    });

    // Listen for the HX-Trigger 'refreshMaterialList' and remove modal
    document.body.addEventListener('refreshMaterialList', function() {
        const modal = document.getElementById('modal');
        if (modal) {
            modal.classList.remove('is-active');
        }
    });
</script>
{% endblock %}
```

**File:** `material_planning/templates/material_planning/material/_material_table.html`

```html
<div class="overflow-x-auto">
    <table id="materialTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Material Code</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Material Name</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Active</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for material in materials %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ material.material_code }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ material.material_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ material.unit_of_measure }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">
                    {% if material.is_active %}
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Yes</span>
                    {% else %}
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">No</span>
                    {% endif %}
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 text-xs"
                        hx-get="{% url 'material_edit' material.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-xs"
                        hx-get="{% url 'material_delete' material.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 px-6 text-center text-gray-500">No materials found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
// DataTables initialization
// Ensure jQuery is loaded via core/base.html (e.g., from CDN)
$(document).ready(function() {
    // Destroy existing DataTable instance if it exists to prevent re-initialization errors
    if ($.fn.DataTable.isDataTable('#materialTable')) {
        $('#materialTable').DataTable().destroy();
    }
    $('#materialTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 5] }, // Disable sorting for SN and Actions
            { "searchable": false, "targets": [0, 5] } // Disable searching for SN and Actions
        ],
        "dom": 'lfrtip', // This specifies layout: Length changing, Filtering, Table, Information, Pagination
    });
});
</script>
```

**File:** `material_planning/templates/material_planning/material/_material_form.html`

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Material</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {% for field in form %}
            <div class="mb-4 {% if field.field.widget.input_type == 'checkbox' %}md:col-span-2 flex items-center{% endif %}">
                {% if field.field.widget.input_type == 'checkbox' %}
                    {{ field }}
                    <label for="{{ field.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-700">
                        {{ field.label }}
                    </label>
                {% else %}
                    <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        {{ field.label }}
                        {% if field.field.required %}
                            <span class="text-red-500">*</span>
                        {% endif %}
                    </label>
                    {{ field }}
                {% endif %}

                {% if field.errors %}
                <p class="text-red-600 text-xs mt-1">{{ field.errors.as_text|cut:"* " }}</p>
                {% endif %}
                {% if field.help_text %}
                <p class="text-gray-500 text-xs mt-1">{{ field.help_text }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-5 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out">
                Save Material
                <span id="form-spinner" class="htmx-indicator ml-2 inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white"></span>
            </button>
        </div>
    </form>
</div>
```

**File:** `material_planning/templates/material_planning/material/_material_confirm_delete.html`

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the material: <strong class="font-medium">{{ material.material_name }} ({{ material.material_code }})</strong>?</p>
    
    <form hx-post="{% url 'material_delete' material.pk %}" hx-swap="none" hx-indicator="#delete-spinner">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-5 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out">
                Confirm Delete
                <span id="delete-spinner" class="htmx-indicator ml-2 inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white"></span>
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

- Create paths for list, create, update, delete, and the HTMX-specific table partial view.
- Use a consistent `material/` prefix.

**File:** `material_planning/urls.py`

```python
from django.urls import path
from .views import (
    MaterialListView,
    MaterialCreateView,
    MaterialUpdateView,
    MaterialDeleteView,
    MaterialTablePartialView,
)

urlpatterns = [
    # Main page for Materials dashboard
    path('materials/', MaterialListView.as_view(), name='material_list'),
    
    # HTMX endpoint for the DataTables content
    path('materials/table/', MaterialTablePartialView.as_view(), name='material_table'),

    # CRUD operations via modals
    path('materials/add/', MaterialCreateView.as_view(), name='material_add'),
    path('materials/<int:pk>/edit/', MaterialUpdateView.as_view(), name='material_edit'),
    path('materials/<int:pk>/delete/', MaterialDeleteView.as_view(), name='material_delete'),
]
```

### 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

- Include comprehensive unit tests for model methods and properties.
- Add integration tests for all views (list, create, update, delete) including HTMX interactions.
- Aim for high test coverage.

**File:** `material_planning/tests.py`

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import Material
from .forms import MaterialForm

class MaterialModelTest(TestCase):
    """
    Unit tests for the Material model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a test material for all tests in this class
        cls.material1 = Material.objects.create(
            material_id=101,
            material_code='TEST_MAT_001',
            material_name='Test Material One',
            unit_of_measure='KG',
            description='A test material for unit tests.',
            is_active=True
        )
        cls.material2 = Material.objects.create(
            material_id=102,
            material_code='TEST_MAT_002',
            material_name='Test Material Two',
            unit_of_measure='PCS',
            is_active=False
        )

    def test_material_creation(self):
        """Test that a material object is created correctly."""
        self.assertEqual(self.material1.material_code, 'TEST_MAT_001')
        self.assertEqual(self.material1.material_name, 'Test Material One')
        self.assertTrue(self.material1.is_active)

    def test_material_str_method(self):
        """Test the __str__ method of the Material model."""
        self.assertEqual(str(self.material1), 'Test Material One')

    def test_material_verbose_name(self):
        """Test verbose names for model and fields."""
        self.assertEqual(Material._meta.verbose_name, 'Material')
        self.assertEqual(Material._meta.verbose_name_plural, 'Materials')
        field_code = self.material1._meta.get_field('material_code')
        self.assertEqual(field_code.verbose_name, 'Material Code')
        
    def test_material_code_uniqueness(self):
        """Test that material codes are unique (model level)."""
        with self.assertRaises(Exception): # Assuming database unique constraint would raise an error
            Material.objects.create(
                material_id=103,
                material_code='TEST_MAT_001', # Duplicate code
                material_name='Another Material',
                unit_of_measure='MT',
                is_active=True
            )

class MaterialFormTest(TestCase):
    """
    Unit tests for the MaterialForm.
    """
    def test_material_form_valid(self):
        """Test that the form is valid with correct data."""
        form_data = {
            'material_code': 'NEW_CODE_001',
            'material_name': 'New Product',
            'unit_of_measure': 'EA',
            'description': 'A new product description.',
            'is_active': True,
        }
        form = MaterialForm(data=form_data)
        self.assertTrue(form.is_valid(), form.errors)

    def test_material_form_invalid_missing_required(self):
        """Test that the form is invalid if required fields are missing."""
        form_data = {
            'material_code': 'NEW_CODE_002',
            'unit_of_measure': 'EA',
        } # material_name is missing
        form = MaterialForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('material_name', form.errors)

    def test_material_form_material_code_uniqueness(self):
        """Test custom validation for unique material code."""
        Material.objects.create(
            material_id=1,
            material_code='EXISTING_CODE',
            material_name='Existing Material',
            unit_of_measure='KG',
            is_active=True
        )
        form_data = {
            'material_code': 'EXISTING_CODE', # Duplicate
            'material_name': 'Another Material',
            'unit_of_measure': 'L',
            'is_active': True,
        }
        form = MaterialForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('material_code', form.errors)
        self.assertIn('This Material Code already exists.', form.errors['material_code'][0])

    def test_material_form_material_code_uniqueness_on_update(self):
        """Test unique material code validation allows update on same instance."""
        existing_material = Material.objects.create(
            material_id=2,
            material_code='CODE_FOR_UPDATE',
            material_name='Original Name',
            unit_of_measure='KG',
            is_active=True
        )
        Material.objects.create(
            material_id=3,
            material_code='ANOTHER_CODE',
            material_name='Another Material',
            unit_of_measure='L',
            is_active=True
        )

        form_data = {
            'material_code': 'CODE_FOR_UPDATE', # Same code as existing_material
            'material_name': 'Updated Name',
            'unit_of_measure': 'KG',
            'is_active': True,
        }
        # Pass instance to form for update scenario
        form = MaterialForm(data=form_data, instance=existing_material)
        self.assertTrue(form.is_valid(), form.errors)

        form_data_duplicate_other = {
            'material_code': 'ANOTHER_CODE', # Code from another instance
            'material_name': 'Updated Name',
            'unit_of_measure': 'KG',
            'is_active': True,
        }
        form = MaterialForm(data=form_data_duplicate_other, instance=existing_material)
        self.assertFalse(form.is_valid())
        self.assertIn('material_code', form.errors)

class MaterialViewsTest(TestCase):
    """
    Integration tests for Material views.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for views
        cls.material1 = Material.objects.create(
            material_id=1001,
            material_code='PROD_A',
            material_name='Product A',
            unit_of_measure='Units',
            is_active=True
        )
        cls.material2 = Material.objects.create(
            material_id=1002,
            material_code='RAW_B',
            material_name='Raw Material B',
            unit_of_measure='Kg',
            is_active=False
        )
    
    def setUp(self):
        # Set up a new client for each test method
        self.client = Client()

    def test_material_list_view(self):
        """Test the Material list page (initial load, HTML structure)."""
        response = self.client.get(reverse('material_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/material/list.html')
        self.assertContains(response, 'Materials Dashboard') # Check for main page title
        self.assertContains(response, 'id="materialTable-container"') # Check for HTMX container

    def test_material_table_partial_view_get(self):
        """Test that the HTMX partial for the table loads correctly."""
        response = self.client.get(reverse('material_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/material/_material_table.html')
        self.assertContains(response, 'id="materialTable"') # Check for table ID
        self.assertContains(response, 'PROD_A') # Check if material data is present
        self.assertContains(response, 'RAW_B')
        self.assertEqual(response.context['materials'].count(), 2)

    def test_material_create_view_get(self):
        """Test GET request to Material creation form."""
        response = self.client.get(reverse('material_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/material/_material_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Material')

    def test_material_create_view_post_success(self):
        """Test successful POST request to create a Material (HTMX enabled)."""
        new_material_data = {
            'material_id': 1003, # Manually setting PK as it's not auto-incremented by Django
            'material_code': 'FIN_C',
            'material_name': 'Finished Good C',
            'unit_of_measure': 'EA',
            'description': 'A new finished good.',
            'is_active': True,
        }
        # Simulate HTMX request by adding HX-Request header
        response = self.client.post(reverse('material_add'), new_material_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # No Content expected for HTMX success
        self.assertTrue(Material.objects.filter(material_code='FIN_C').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialList')

    def test_material_create_view_post_invalid(self):
        """Test POST request with invalid data to create a Material."""
        invalid_material_data = {
            'material_id': 1004,
            'material_code': 'PROD_A', # Duplicate code, should fail form validation
            'material_name': '', # Missing required field
            'unit_of_measure': 'L',
            'is_active': True,
        }
        response = self.client.post(reverse('material_add'), invalid_material_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertContains(response, 'Add Material')
        self.assertContains(response, 'This Material Code already exists.')
        self.assertContains(response, 'This field is required.')
        self.assertFalse(Material.objects.filter(material_code='INVALID_TEST_CODE').exists())

    def test_material_update_view_get(self):
        """Test GET request to Material update form."""
        response = self.client.get(reverse('material_edit', args=[self.material1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/material/_material_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Edit Material')
        self.assertContains(response, self.material1.material_name) # Form should be pre-filled

    def test_material_update_view_post_success(self):
        """Test successful POST request to update a Material (HTMX enabled)."""
        updated_data = {
            'material_id': self.material1.pk, # PK is required for update form
            'material_code': 'PROD_A', # Code remains same or unique
            'material_name': 'Updated Product A Name',
            'unit_of_measure': 'Dozens',
            'is_active': False,
        }
        response = self.client.post(reverse('material_edit', args=[self.material1.pk]), updated_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # No Content expected for HTMX success
        self.material1.refresh_from_db() # Reload instance from DB
        self.assertEqual(self.material1.material_name, 'Updated Product A Name')
        self.assertEqual(self.material1.unit_of_measure, 'Dozens')
        self.assertFalse(self.material1.is_active)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialList')

    def test_material_update_view_post_invalid(self):
        """Test POST request with invalid data to update a Material."""
        # Try to change material1's code to material2's code
        invalid_data = {
            'material_id': self.material1.pk,
            'material_code': self.material2.material_code, # Duplicate code
            'material_name': 'Attempting Invalid Update',
            'unit_of_measure': 'L',
            'is_active': True,
        }
        response = self.client.post(reverse('material_edit', args=[self.material1.pk]), invalid_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertContains(response, 'Edit Material')
        self.assertContains(response, 'This Material Code already exists.')
        # Ensure original data is not changed
        self.material1.refresh_from_db()
        self.assertEqual(self.material1.material_name, 'Product A')

    def test_material_delete_view_get(self):
        """Test GET request to Material delete confirmation."""
        response = self.client.get(reverse('material_delete', args=[self.material1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/material/_material_confirm_delete.html')
        self.assertIn('material', response.context)
        self.assertContains(response, f'delete the material: {self.material1.material_name}')

    def test_material_delete_view_post_success(self):
        """Test successful POST request to delete a Material (HTMX enabled)."""
        # Create a material specifically for deletion test
        material_to_delete = Material.objects.create(
            material_id=1003,
            material_code='DEL_ME',
            material_name='Delete Me',
            unit_of_measure='Unit',
            is_active=True
        )
        pk_to_delete = material_to_delete.pk

        response = self.client.post(reverse('material_delete', args=[pk_to_delete]), HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # No Content expected for HTMX success
        self.assertFalse(Material.objects.filter(pk=pk_to_delete).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialList')
```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

- **HTMX for all dynamic updates:**
    - The main `list.html` uses `hx-get` on `materialTable-container` to load `{% url 'material_table' %}` upon page load (`load`) and when `refreshMaterialList` is triggered.
    - "Add New Material," "Edit," and "Delete" buttons use `hx-get` to load forms/confirmation into the `#modalContent` target.
    - Forms (`_material_form.html`, `_material_confirm_delete.html`) use `hx-post` to submit data back to their respective `hx-post="{{ request.path }}"` URLs.
    - Upon successful form submission, Django views return `status=204` (No Content) with an `HX-Trigger` header set to `refreshMaterialList`. This tells HTMX to refresh the table.
    - `hx-swap="none"` on forms ensures the form itself isn't swapped out, allowing the `HX-Trigger` to handle the modal closure and list refresh.
    - `hx-indicator` is used to show a small loading spinner during form submission.

- **Alpine.js for UI state management:**
    - The modal (`#modal`) uses `x-data` and `x-show` for a more explicit Alpine.js control over its visibility, allowing for `x-transition` for smooth animations.
    - The `_=` (hyperscript) attributes on buttons (`on click add .is-active to #modal`) and on the modal itself (`on click if event.target.id == 'modal' remove .is-active from me`) provide concise client-side scripting for modal opening and closing, including clicking outside.
    - `on htmx:afterSwap remove .is-active from #modal` ensures the modal closes automatically after an HTMX form submission, provided the form target is `#modalContent` which is within the modal.

- **DataTables for all list views:**
    - The `_material_table.html` partial includes a `<table>` with the ID `materialTable`.
    - A `<script>` block within this partial initializes DataTables on `$('#materialTable')` when the partial is loaded and inserted into the DOM. This ensures DataTables is always applied to the dynamically loaded table.

- **No additional JavaScript requirements:**
    - All dynamic interactions are handled through HTMX attributes and a minimal Alpine.js setup for modal state. The `loadingNotifier.js` from the original ASP.NET is replaced by HTMX's `hx-indicator` and the general loading spinner provided in `list.html`.

## Final Notes

- **Placeholders:** All `[PLACEHOLDER]` values have been replaced with concrete names inferred from the "Material Planning Masters Dashboard" context.
- **DRY Templates:** `list.html` extends `core/base.html` (which is assumed to exist and provide common elements like CDNs for jQuery, DataTables, HTMX, Alpine.js, and Tailwind CSS). CRUD forms and the data table are implemented as partial templates to be dynamically loaded by HTMX.
- **Fat Model, Thin View:** Business logic, such as unique material code validation, is placed within the `MaterialForm` (which interacts closely with the `Material` model). The views (`CreateView`, `UpdateView`, `DeleteView`) are kept concise, primarily handling HTTP request/response flow and delegating data operations to Django's ORM and form validation.
- **Comprehensive Tests:** Both model-level unit tests and view-level integration tests are provided, covering basic CRUD operations and HTMX interactions.
- **AI-Assisted Automation:** This detailed plan, with its explicit structure and code generation, is designed to be easily digestible by conversational AI tools. An AI could take this plan, adapt it to more specific ASP.NET code inputs, and generate the corresponding Django application files with high accuracy, significantly reducing manual development effort.