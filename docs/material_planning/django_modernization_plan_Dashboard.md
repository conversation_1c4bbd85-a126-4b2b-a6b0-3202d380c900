## ASP.NET to Django Conversion Script: Modernizing Your Material Planning Dashboard

This document outlines a strategic plan to modernize your legacy ASP.NET Material Planning Dashboard into a robust, scalable, and maintainable Django application. Our approach focuses on automated code generation patterns, leveraging cutting-edge web technologies like Django 5.0+, HTMX, and Alpine.js to deliver a highly performant and user-friendly experience.

### Business Value of Django Modernization:

Transitioning to Django offers significant business benefits:

*   **Cost Reduction:** Modern frameworks are easier to maintain, requiring less developer time for updates and bug fixes.
*   **Enhanced Scalability:** Django's architecture is designed for growth, allowing your application to handle increased user loads and data volumes without significant re-engineering.
*   **Improved User Experience:** With HTMX and Alpine.js, the dashboard will feel snappier and more responsive, eliminating full page reloads and providing a smoother interaction flow.
*   **Future-Proofing:** Moving to a widely adopted, actively maintained open-source framework ensures your application remains relevant and secure.
*   **Faster Feature Development:** Django's "batteries included" philosophy and clean architecture enable quicker development cycles for new features and enhancements.
*   **Data-Driven Decisions:** Implementing DataTables ensures your teams can efficiently search, sort, and analyze material planning data directly within the dashboard.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:

*   **NEVER include `base.html` template code in your output** - assume it already exists and is extended.
*   Focus ONLY on component-specific code for the current module (`MaterialPlanning` in this case).
*   Always include complete unit tests for models and integration tests for views.
*   Use modern Django 5.0+ patterns and follow best practices.
*   Keep your code clean, efficient, and avoid redundancy.
*   Always generate complete, runnable Django code.

## AutoERP Guidelines:

*   Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
*   Map models to existing database using `managed = False` and `db_table`.
*   Implement DataTables for client-side searching, sorting, and pagination.
*   Use HTMX for dynamic interactions and Alpine.js for UI state management.
*   All templates should extend `core/base.html` (but DO NOT include `base.html` code).
*   Achieve at least 80% test coverage with unit and integration tests.
*   Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
*   Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**

The provided ASP.NET code is a barebones `Dashboard.aspx` with no explicit database interactions or UI elements, making direct schema extraction impossible. However, a "Material Planning Dashboard" typically displays aggregated or detailed information related to material planning activities. We will infer a plausible database schema for a core entity that such a dashboard might manage or display.

**Inferred Database Schema:**

We will assume the dashboard displays and manages `MaterialPlanningActivity` records.

*   **Table Name:** `tbl_material_planning_activity`
*   **Columns:**
    *   `id` (Primary Key, INTEGER)
    *   `activity_name` (VARCHAR)
    *   `planned_date` (DATE)
    *   `status` (VARCHAR, e.g., 'Planned', 'In Progress', 'Completed', 'Cancelled')
    *   `assigned_to` (VARCHAR, e.g., an employee ID or name)
    *   `notes` (TEXT)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**

Since the ASP.NET `Dashboard.aspx` and its code-behind are empty, no explicit CRUD operations are present. However, for a typical ERP dashboard, we anticipate the need to:

*   **Read:** Display a list of all `MaterialPlanningActivity` records.
*   **Create:** Add new `MaterialPlanningActivity` records.
*   **Update:** Modify existing `MaterialPlanningActivity` records.
*   **Delete:** Remove `MaterialPlanningActivity` records.
*   **Validation:** Basic field validation for `activity_name`, `planned_date`, and `status`.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

Given the empty ASP.NET code, no UI components are specified. Based on the inferred functionality, we will design the Django UI to include:

*   **List View:** A table (using DataTables for filtering/sorting) to display all `MaterialPlanningActivity` records. Each row will have "Edit" and "Delete" actions.
*   **Add/Edit Form:** A modal form (loaded via HTMX) for creating and updating `MaterialPlanningActivity` records. This form will include fields for `activity_name`, `planned_date`, `status`, `assigned_to`, and `notes`.
*   **Delete Confirmation:** A modal confirmation dialog for deleting records.
*   **Dynamic Interactions:** All form submissions and table refreshes will be handled via HTMX, with Alpine.js managing modal visibility.

---

### Step 4: Generate Django Code

We will create a new Django app named `material_planning` to encapsulate this module's functionality.

#### 4.1 Models (`material_planning/models.py`)

This model maps directly to the existing `tbl_material_planning_activity` database table. It includes methods for business logic related to material planning activities.

```python
# material_planning/models.py
from django.db import models
from django.utils import timezone

class MaterialPlanningActivity(models.Model):
    id = models.AutoField(db_column='id', primary_key=True)
    activity_name = models.CharField(db_column='activity_name', max_length=255, verbose_name='Activity Name')
    planned_date = models.DateField(db_column='planned_date', verbose_name='Planned Date', default=timezone.now)
    status = models.CharField(db_column='status', max_length=50, verbose_name='Status',
                              choices=[('Planned', 'Planned'), ('In Progress', 'In Progress'),
                                       ('Completed', 'Completed'), ('Cancelled', 'Cancelled')],
                              default='Planned')
    assigned_to = models.CharField(db_column='assigned_to', max_length=100, blank=True, null=True, verbose_name='Assigned To')
    notes = models.TextField(db_column='notes', blank=True, null=True, verbose_name='Notes')

    class Meta:
        managed = False  # Set to True if Django should manage the table schema
        db_table = 'tbl_material_planning_activity'
        verbose_name = 'Material Planning Activity'
        verbose_name_plural = 'Material Planning Activities'
        ordering = ['-planned_date', 'activity_name'] # Default ordering

    def __str__(self):
        return f"{self.activity_name} ({self.status} on {self.planned_date})"
        
    def is_completed(self):
        """
        Business logic: Check if the activity status is 'Completed'.
        """
        return self.status == 'Completed'

    def update_status(self, new_status):
        """
        Business logic: Updates the status of the activity, with basic validation.
        """
        valid_statuses = ['Planned', 'In Progress', 'Completed', 'Cancelled']
        if new_status in valid_statuses:
            self.status = new_status
            self.save()
            return True
        return False
```

#### 4.2 Forms (`material_planning/forms.py`)

This Django `ModelForm` handles data capture and validation for `MaterialPlanningActivity` records, styled with Tailwind CSS.

```python
# material_planning/forms.py
from django import forms
from .models import MaterialPlanningActivity

class MaterialPlanningActivityForm(forms.ModelForm):
    class Meta:
        model = MaterialPlanningActivity
        fields = ['activity_name', 'planned_date', 'status', 'assigned_to', 'notes']
        widgets = {
            'activity_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'planned_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'status': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'assigned_to': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
        }
        
    def clean_activity_name(self):
        activity_name = self.cleaned_data.get('activity_name')
        if not activity_name:
            raise forms.ValidationError("Activity name cannot be empty.")
        return activity_name
```

#### 4.3 Views (`material_planning/views.py`)

These thin views handle rendering and form processing, delegating business logic to the model. An additional view for the HTMX-loaded table partial is included.

```python
# material_planning/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import MaterialPlanningActivity
from .forms import MaterialPlanningActivityForm

class MaterialPlanningActivityListView(ListView):
    model = MaterialPlanningActivity
    template_name = 'material_planning/materialplanningactivity/list.html'
    context_object_name = 'materialplanningactivities'
    
    # This view is purely for the main page shell, the table content is loaded via HTMX

class MaterialPlanningActivityTablePartialView(ListView):
    model = MaterialPlanningActivity
    template_name = 'material_planning/materialplanningactivity/_materialplanningactivity_table.html'
    context_object_name = 'materialplanningactivities'
    # This view is specifically for HTMX requests to refresh the table.
    # No complex logic here, just fetches objects and renders the partial.

class MaterialPlanningActivityCreateView(CreateView):
    model = MaterialPlanningActivity
    form_class = MaterialPlanningActivityForm
    template_name = 'material_planning/materialplanningactivity/_materialplanningactivity_form.html'
    success_url = reverse_lazy('materialplanningactivity_list') # Redirect for non-HTMX requests

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Material Planning Activity added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a no-content response and trigger a client-side event
            return HttpResponse(
                status=204, # No Content
                headers={'HX-Trigger': 'refreshMaterialPlanningActivityList'}
            )
        return response

class MaterialPlanningActivityUpdateView(UpdateView):
    model = MaterialPlanningActivity
    form_class = MaterialPlanningActivityForm
    template_name = 'material_planning/materialplanningactivity/_materialplanningactivity_form.html'
    success_url = reverse_lazy('materialplanningactivity_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Material Planning Activity updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshMaterialPlanningActivityList'}
            )
        return response

class MaterialPlanningActivityDeleteView(DeleteView):
    model = MaterialPlanningActivity
    template_name = 'material_planning/materialplanningactivity/_materialplanningactivity_confirm_delete.html'
    success_url = reverse_lazy('materialplanningactivity_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Material Planning Activity deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshMaterialPlanningActivityList'}
            )
        return response
```

#### 4.4 Templates

These templates use DRY principles with partials and integrate HTMX for dynamic content updates.

**`material_planning/materialplanningactivity/list.html`**
This is the main page for displaying the list of material planning activities.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Material Planning Activities</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'materialplanningactivity_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Activity
        </button>
    </div>
    
    <div id="materialplanningactivityTable-container"
         hx-trigger="load, refreshMaterialPlanningActivityList from:body"
         hx-get="{% url 'materialplanningactivity_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg p-6">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading material planning activities...</p>
        </div>
    </div>
    
    <!-- Modal for form (Add/Edit) and delete confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white rounded-lg shadow-2xl max-w-3xl w-full mx-4 my-8 transform transition-all sm:my-8 sm:align-middle sm:max-w-xl sm:w-full">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is typically initialized globally in base.html
    // If specific Alpine.js components are needed for this page, define them here.
</script>
{% endblock %}
```

**`material_planning/materialplanningactivity/_materialplanningactivity_table.html`**
This partial template renders the DataTables table, dynamically loaded via HTMX.

```html
<table id="materialplanningactivityTable" class="min-w-full bg-white table-auto border-collapse">
    <thead>
        <tr>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Activity Name</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Planned Date</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Status</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Assigned To</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in materialplanningactivities %}
        <tr class="hover:bg-gray-50">
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ forloop.counter }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.activity_name }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.planned_date|date:"Y-m-d" }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                    {% if obj.status == 'Completed' %}bg-green-100 text-green-800
                    {% elif obj.status == 'In Progress' %}bg-yellow-100 text-yellow-800
                    {% elif obj.status == 'Cancelled' %}bg-red-100 text-red-800
                    {% else %}bg-blue-100 text-blue-800{% endif %}">
                    {{ obj.status }}
                </span>
            </td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.assigned_to|default_if_none:"N/A" }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 text-xs transition duration-300 ease-in-out"
                    hx-get="{% url 'materialplanningactivity_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-xs transition duration-300 ease-in-out"
                    hx-get="{% url 'materialplanningactivity_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-4 text-center text-gray-500">No material planning activities found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// Initialize DataTables once the table is loaded via HTMX
$(document).ready(function() {
    $('#materialplanningactivityTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[5, 10, 25, 50, -1], [5, 10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 5] }, // Disable sorting for SN and Actions columns
            { "searchable": false, "targets": [0, 5] } // Disable searching for SN and Actions columns
        ],
        "language": { // Customize DataTables text
            "search": "Search Activities:",
            "lengthMenu": "Show _MENU_ activities",
            "info": "Showing _START_ to _END_ of _TOTAL_ activities",
            "infoEmpty": "No activities to show",
            "infoFiltered": "(filtered from _MAX_ total activities)",
            "paginate": {
                "first": "First",
                "last": "Last",
                "next": "Next",
                "previous": "Previous"
            }
        },
        "dom": '<"flex justify-between items-center flex-wrap"lf>rt<"flex justify-between items-center flex-wrap"ip>' // Customize DataTables layout
    });
});
</script>
```

**`material_planning/materialplanningactivity/_materialplanningactivity_form.html`**
This partial template handles the creation and editing forms within a modal.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Material Planning Activity</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-5">
        {% csrf_token %}
        
        {% for field in form %}
        <div class="mb-4">
            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                {{ field.label }}
                {% if field.field.required %} <span class="text-red-500">*</span>{% endif %}
            </label>
            {{ field }}
            {% if field.help_text %}
            <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
            {% endif %}
            {% if field.errors %}
            <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
            {% endif %}
        </div>
        {% endfor %}
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                Save Activity
            </button>
        </div>
    </form>
</div>
```

**`material_planning/materialplanningactivity/_materialplanningactivity_confirm_delete.html`**
This partial template provides a confirmation dialog for deletions.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">Confirm Deletion</h3>
    <p class="text-gray-700 mb-8">Are you sure you want to delete the activity: <span class="font-bold">{{ object.activity_name }}</span>?</p>
    
    <div class="flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-300 ease-in-out"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button 
            hx-post="{% url 'materialplanningactivity_delete' object.pk %}"
            hx-target="body"
            hx-swap="none"
            type="button" 
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            _="on click remove .is-active from #modal">
            Confirm Delete
        </button>
    </div>
</div>
```

#### 4.5 URLs (`material_planning/urls.py`)

This file defines the URL patterns for the `material_planning` application, mapping URLs to the respective views.

```python
# material_planning/urls.py
from django.urls import path
from .views import (
    MaterialPlanningActivityListView,
    MaterialPlanningActivityCreateView,
    MaterialPlanningActivityUpdateView,
    MaterialPlanningActivityDeleteView,
    MaterialPlanningActivityTablePartialView,
)

urlpatterns = [
    path('material-planning/', MaterialPlanningActivityListView.as_view(), name='materialplanningactivity_list'),
    path('material-planning/add/', MaterialPlanningActivityCreateView.as_view(), name='materialplanningactivity_add'),
    path('material-planning/edit/<int:pk>/', MaterialPlanningActivityUpdateView.as_view(), name='materialplanningactivity_edit'),
    path('material-planning/delete/<int:pk>/', MaterialPlanningActivityDeleteView.as_view(), name='materialplanningactivity_delete'),
    # HTMX specific endpoint for the table partial
    path('material-planning/table/', MaterialPlanningActivityTablePartialView.as_view(), name='materialplanningactivity_table'),
]
```

#### 4.6 Tests (`material_planning/tests.py`)

Comprehensive tests for both the model's business logic and the views' functionality, including HTMX interactions.

```python       
# material_planning/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import MaterialPlanningActivity

class MaterialPlanningActivityModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        MaterialPlanningActivity.objects.create(
            activity_name='Test Activity 1',
            planned_date='2023-01-15',
            status='Planned',
            assigned_to='John Doe',
            notes='Initial planning phase.'
        )
        MaterialPlanningActivity.objects.create(
            activity_name='Test Activity 2 (Completed)',
            planned_date='2023-01-10',
            status='Completed',
            assigned_to='Jane Smith',
            notes='All materials received.'
        )
  
    def test_material_planning_activity_creation(self):
        obj = MaterialPlanningActivity.objects.get(id=1)
        self.assertEqual(obj.activity_name, 'Test Activity 1')
        self.assertEqual(obj.planned_date.strftime('%Y-%m-%d'), '2023-01-15')
        self.assertEqual(obj.status, 'Planned')
        self.assertEqual(obj.assigned_to, 'John Doe')
        self.assertEqual(obj.notes, 'Initial planning phase.')
        
    def test_activity_name_label(self):
        obj = MaterialPlanningActivity.objects.get(id=1)
        field_label = obj._meta.get_field('activity_name').verbose_name
        self.assertEqual(field_label, 'Activity Name')
        
    def test_is_completed_method(self):
        activity_completed = MaterialPlanningActivity.objects.get(activity_name='Test Activity 2 (Completed)')
        activity_planned = MaterialPlanningActivity.objects.get(activity_name='Test Activity 1')
        self.assertTrue(activity_completed.is_completed())
        self.assertFalse(activity_planned.is_completed())

    def test_update_status_method(self):
        obj = MaterialPlanningActivity.objects.get(activity_name='Test Activity 1')
        self.assertEqual(obj.status, 'Planned')
        
        # Test valid status update
        self.assertTrue(obj.update_status('In Progress'))
        obj.refresh_from_db() # Reload object to get latest status
        self.assertEqual(obj.status, 'In Progress')

        # Test invalid status update
        self.assertFalse(obj.update_status('Invalid Status'))
        obj.refresh_from_db()
        self.assertEqual(obj.status, 'In Progress') # Should not change

    def test_str_representation(self):
        obj = MaterialPlanningActivity.objects.get(activity_name='Test Activity 1')
        expected_str = 'Test Activity 1 (Planned on 2023-01-15)'
        self.assertEqual(str(obj), expected_str)


class MaterialPlanningActivityViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create initial test data for views
        MaterialPlanningActivity.objects.create(
            activity_name='View Test Activity 1',
            planned_date='2024-03-01',
            status='Planned',
            assigned_to='Test User',
            notes='For view testing.'
        )
    
    def setUp(self):
        # Set up data for each test method
        self.client = Client()
    
    def test_list_view(self):
        response = self.client.get(reverse('materialplanningactivity_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/materialplanningactivity/list.html')
        # The list view only loads the shell, the table content is loaded via HTMX
        # We check for the presence of the modal structure
        self.assertContains(response, '<div id="modal"')

    def test_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('materialplanningactivity_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/materialplanningactivity/_materialplanningactivity_table.html')
        self.assertTrue('materialplanningactivities' in response.context)
        self.assertContains(response, 'View Test Activity 1') # Check if activity is in the table

    def test_create_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('materialplanningactivity_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/materialplanningactivity/_materialplanningactivity_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Material Planning Activity') # Check form title

    def test_create_view_post_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'activity_name': 'New HTMX Activity',
            'planned_date': '2024-04-10',
            'status': 'Planned',
            'assigned_to': 'Another User',
            'notes': 'Created via HTMX post.'
        }
        response = self.client.post(reverse('materialplanningactivity_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # 204 No Content for successful HTMX post
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMaterialPlanningActivityList', response.headers['HX-Trigger'])
        self.assertTrue(MaterialPlanningActivity.objects.filter(activity_name='New HTMX Activity').exists())

    def test_create_view_post_invalid_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'activity_name': '', # Invalid data
            'planned_date': '2024-04-10',
            'status': 'Planned'
        }
        response = self.client.post(reverse('materialplanningactivity_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # Should render form with errors
        self.assertContains(response, 'Activity name cannot be empty.')
        self.assertTemplateUsed(response, 'material_planning/materialplanningactivity/_materialplanningactivity_form.html')


    def test_update_view_get_htmx(self):
        obj = MaterialPlanningActivity.objects.get(activity_name='View Test Activity 1')
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('materialplanningactivity_edit', args=[obj.id]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/materialplanningactivity/_materialplanningactivity_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Edit Material Planning Activity')
        self.assertContains(response, obj.activity_name) # Check if current data is pre-filled

    def test_update_view_post_htmx(self):
        obj = MaterialPlanningActivity.objects.get(activity_name='View Test Activity 1')
        headers = {'HTTP_HX_REQUEST': 'true'}
        updated_name = 'Updated HTMX Activity'
        data = {
            'activity_name': updated_name,
            'planned_date': '2024-03-01', # Keep other fields same or update as needed
            'status': 'In Progress',
            'assigned_to': 'Test User',
            'notes': 'For view testing.'
        }
        response = self.client.post(reverse('materialplanningactivity_edit', args=[obj.id]), data, **headers)
        self.assertEqual(response.status_code, 204) # 204 No Content for successful HTMX post
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMaterialPlanningActivityList', response.headers['HX-Trigger'])
        obj.refresh_from_db()
        self.assertEqual(obj.activity_name, updated_name)
        self.assertEqual(obj.status, 'In Progress')

    def test_delete_view_get_htmx(self):
        obj = MaterialPlanningActivity.objects.get(activity_name='View Test Activity 1')
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('materialplanningactivity_delete', args=[obj.id]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/materialplanningactivity/_materialplanningactivity_confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, obj.activity_name)

    def test_delete_view_post_htmx(self):
        obj_to_delete = MaterialPlanningActivity.objects.create(
            activity_name='To Be Deleted Activity',
            planned_date=timezone.now().date(),
            status='Planned'
        )
        initial_count = MaterialPlanningActivity.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('materialplanningactivity_delete', args=[obj_to_delete.id]), **headers)
        self.assertEqual(response.status_code, 204) # 204 No Content for successful HTMX delete
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMaterialPlanningActivityList', response.headers['HX-Trigger'])
        self.assertEqual(MaterialPlanningActivity.objects.count(), initial_count - 1)
        self.assertFalse(MaterialPlanningActivity.objects.filter(id=obj_to_delete.id).exists())
```

### Step 5: HTMX and Alpine.js Integration

This modernization plan fully embraces the HTMX-driven approach for a dynamic user experience without complex JavaScript frameworks.

*   **HTMX for CRUD Modals:** All Add, Edit, and Delete actions are triggered by HTMX `hx-get` requests to load forms and confirmation dialogs into a modal (`#modalContent`). Form submissions within the modal use `hx-post` and `hx-swap="none"`, relying on `HX-Trigger` headers to signal the main page to refresh the activity list.
*   **Dynamic Table Refresh:** The `materialplanningactivityTable-container` uses `hx-trigger="load, refreshMaterialPlanningActivityList from:body"` and `hx-get="{% url 'materialplanningactivity_table' %}"` to automatically load and refresh the DataTables content.
*   **Alpine.js for Modal State:** The main modal `div` (`#modal`) uses an Alpine.js `_` attribute (`on click if event.target.id == 'modal' remove .is-active from me`) to hide the modal when clicking outside of its content area, creating a seamless user experience.
*   **DataTables for List Views:** The `_materialplanningactivity_table.html` partial uses jQuery DataTables for client-side search, sorting, and pagination, providing a rich data browsing experience. The DataTables initialization script is included directly in the partial to ensure it runs correctly each time the table content is loaded via HTMX.

This setup ensures that all user interactions that modify data or require new forms happen without full page reloads, providing a fast and fluid interface reminiscent of a single-page application (SPA) but with the simplicity of server-rendered HTML.

### Final Notes

This comprehensive plan transforms a skeletal ASP.NET dashboard into a modern, maintainable Django application.

*   **Placeholders have been replaced:** `[MODEL_NAME]` is `MaterialPlanningActivity`, `[APP_NAME]` is `material_planning`, and fields are mapped accordingly.
*   **DRY principles are applied:** Templates leverage partials for reusable components, and `base.html` is extended but not included.
*   **Business logic is in models:** The `is_completed` and `update_status` methods demonstrate how business rules are encapsulated in the `MaterialPlanningActivity` model, keeping views concise and focused on request/response handling.
*   **Comprehensive tests are provided:** Unit tests cover model behavior, and integration tests validate view functionality, including critical HTMX interactions.
*   **HTMX and Alpine.js are fully integrated:** All dynamic elements, including modal forms and table refreshes, utilize HTMX, ensuring a responsive user interface without requiring complex JavaScript.
*   **Tailwind CSS is assumed for styling:** The provided HTML includes Tailwind CSS classes, ready for a modern frontend.

This structured approach, driven by AI-assisted pattern generation, allows for a systematic and efficient migration, significantly reducing manual effort and potential errors while ensuring the resulting application is modern and robust.