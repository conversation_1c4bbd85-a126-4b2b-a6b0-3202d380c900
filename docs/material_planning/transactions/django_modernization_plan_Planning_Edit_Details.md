## ASP.NET to Django Conversion Script: Material Planning - Edit

This comprehensive plan outlines the migration of your ASP.NET Material Planning - Edit module to a modern Django application, focusing on AI-assisted automation, a "fat model, thin view" architecture, and dynamic frontend interactions using HTMX and Alpine.js.

### Business Value Proposition

Migrating this module to Django will deliver significant business benefits:

1.  **Improved Agility & Maintainability:** Django's modular, organized structure and Python's readability will make the application easier to understand, maintain, and adapt to changing business needs. This reduces development time and costs for future enhancements.
2.  **Enhanced User Experience:** By leveraging HTMX, users will experience a highly responsive interface with instant updates, similar to a single-page application, without the overhead of complex JavaScript frameworks. This leads to higher user satisfaction and efficiency.
3.  **Future-Proof Technology Stack:** Moving away from legacy ASP.NET Web Forms to Django positions your application on a modern, widely-supported, and actively developed framework, ensuring long-term viability and access to a larger talent pool.
4.  **Reduced Technical Debt:** Eliminating outdated technologies and practices (like inline C# code in ASPX, direct SQL queries in code-behind) streamlines your codebase, minimizing bugs and security vulnerabilities.
5.  **Scalability & Performance:** Django's robust ORM and framework optimizations, combined with efficient HTMX partial updates, will improve application performance and scalability, allowing your business to grow without infrastructure bottlenecks.
6.  **Automated Testing & Quality:** The plan includes comprehensive unit and integration tests, ensuring higher code quality, fewer regressions, and a more reliable application, which directly translates to fewer operational disruptions.

### Conversion Steps:

### Step 1: Extract Database Schema

Based on the ASP.NET code, we infer the following tables and their relevant columns. The application primarily interacts with `tblMP_Material_RawMaterial` and `tblMP_Material_Process` for editing. Other tables like `tblMM_Supplier_master`, `tblDG_Item_Master`, `Unit_Master`, `tblMM_PR_Master`, and `tblMM_PR_Details` are used for lookups and validation.

**Primary Tables for Editing:**

*   **`tblMP_Material_RawMaterial`** (Raw Material Planning Details)
    *   `Id` (PK, int)
    *   `PLNo` (Planning Number, string)
    *   `MId` (Master ID, string)
    *   `ItemId` (Item ID, string/int)
    *   `SupplierId` (Supplier ID, string/int)
    *   `CompDate` (Completion Date, datetime)
    *   `PId` (Process ID, string/int)
    *   `CId` (Component ID, string/int)

*   **`tblMP_Material_Process`** (Processing Material Planning Details)
    *   `Id` (PK, int)
    *   `PLNo` (Planning Number, string)
    *   `MId` (Master ID, string)
    *   `ItemId` (Item ID, string/int)
    *   `SupplierId` (Supplier ID, string/int)
    *   `CompDate` (Completion Date, datetime)
    *   `PId` (Process ID, string/int)
    *   `CId` (Component ID, string/int)

**Lookup/Related Tables:**

*   **`tblMM_Supplier_master`** (Suppliers)
    *   `SupplierId` (PK/Unique ID, string/int)
    *   `SupplierName` (string)
    *   `CompId` (Company ID, int)

*   **`tblDG_Item_Master`** (Items)
    *   `Id` (PK, int)
    *   `ItemCode` (string)
    *   `ManfDesc` (string)
    *   `UOMBasic` (Unit of Measure ID, string/int)
    *   `CompId` (Company ID, int)

*   **`Unit_Master`** (Units of Measure)
    *   `Id` (PK, int)
    *   `Symbol` (string)

*   **`tblMM_PR_Master`** (Purchase Request Master) - Used for check
    *   `PRNo` (PK, string)
    *   `Id` (Master ID, int)
    *   `CompId` (Company ID, int)

*   **`tblMM_PR_Details`** (Purchase Request Details) - Used for check
    *   `Id` (PK, int)
    *   `PRNo` (FK to tblMM_PR_Master, string)
    *   `MId` (FK to tblMM_PR_Master, int)
    *   `PId` (Process ID, string/int)
    *   `ItemId` (Item ID, string/int)
    *   `CId` (Component ID, string/int)

### Step 2: Identify Backend Functionality

The ASP.NET page provides the following functionalities:

*   **Read (R):**
    *   Displays lists of "Raw Material Planning Details" and "Processing Material Planning Details" filtered by `PLNo` and `MId` from the query string.
    *   Fetches associated `SupplierName`, `ItemCode`, `Description`, and `Unit Symbol` by joining multiple tables.
    *   Includes pagination.
*   **Update (U):**
    *   Allows inline editing of `SupplierName` and `CompDate` for selected (checked) rows within both "Raw Material" and "Processing Material" grids.
    *   A single "Update" button in the footer of each grid triggers a batch update of all checked rows in that grid.
    *   Includes client-side validation for `SupplierName` (required) and `CompDate` (regex for date format).
    *   Provides an autocomplete feature for `SupplierName`.
*   **Business Logic/Validation:**
    *   `disableEdit()`: Prevents editing of a raw or processing material detail if a corresponding Purchase Request (PR) exists for it. This logic needs to be translated to a model method.
    *   Date format conversion (`FromDateDMY`, `FromDate`).
    *   Supplier ID extraction from "Name [ID]" format (`getCode`).
*   **Navigation:**
    *   A "Cancel" button redirects to the main `Planning_Edit.aspx` page.

### Step 3: Infer UI Components

The ASP.NET controls will be mapped to Django templates and frontend technologies:

*   **`asp:GridView` (ID: `GridView2`, `GridView1`):** These will be rendered as standard HTML `<table>` elements, enhanced with DataTables. Each row will have elements for displaying data and conditional input fields/checkboxes.
*   **`asp:Label` (e.g., `lblPLNo`, `lblItemCode`):** Standard `<span>` or `<div>` elements for display.
*   **`asp:TextBox` (ID: `txtSupName`, `TxtCompDate`):** HTML `<input type="text">` elements.
*   **`asp:CheckBox` (ID: `CK`, `ck`):** HTML `<input type="checkbox">` elements. These will trigger HTMX swaps to toggle edit mode for a row and Alpine.js for local UI state.
*   **`AjaxControlToolkit.AutoCompleteExtender`:** Will be replaced by HTMX requests to a Django endpoint providing JSON, combined with Alpine.js for displaying and selecting suggestions.
*   **`AjaxControlToolkit.CalendarExtender`:** Will be replaced by a simple HTML5 `<input type="date">` or a lightweight Alpine.js date picker library (if more advanced UI needed, but HTML5 is preferred).
*   **`asp:RequiredFieldValidator`, `asp:RegularExpressionValidator`:** Replaced by Django Form validation (server-side) and potentially simple client-side checks with Alpine.js/HTMX.
*   **`asp:Button` (ID: `btnEditRaw`, `btnEdit`, `btnCancel1`):** HTML `<button>` elements. "Update" buttons will use `hx-post` to send data, "Cancel" will use `hx-get` or `window.location`.

### Step 4: Generate Django Code

We will create a new Django application named `material_planning`.

#### 4.1 Models (`material_planning/models.py`)

We'll define models for the primary planning details and related lookup data.
Assuming `PLNo`, `MId`, `PId`, `CId`, `ItemId`, `SupplierId` are string or integer fields as inferred from `ToString()` and `Convert.ToInt32()` calls in the C# code. `CompDate` will be `DateField`.

```python
from django.db import models
from django.urls import reverse
from django.utils.html import format_html

# Assuming CompId and FinYearId are handled globally, e.g., via session or a base model if common.
# For simplicity, these are not added to every model but would be in a real ERP.

class Unit(models.Model):
    """
    Corresponds to Unit_Master table.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol or 'N/A'

class Item(models.Model):
    """
    Corresponds to tblDG_Item_Master table.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=100)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=500, blank=True, null=True)
    uom_basic = models.ForeignKey(Unit, models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId') # Assuming CompId is part of item master

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return f"{self.item_code} - {self.manf_desc}"

class Supplier(models.Model):
    """
    Corresponds to tblMM_Supplier_master table.
    """
    supplier_id = models.IntegerField(db_column='SupplierId', primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return f"{self.supplier_name} [{self.supplier_id}]"

class PurchaseRequestDetail(models.Model):
    """
    Corresponds to tblMM_PR_Details. Used for checking if an item is "locked" from editing.
    This simplified model assumes a direct link, actual FKs might be more complex.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    pr_no = models.CharField(db_column='PRNo', max_length=50) # Link to master
    m_id = models.IntegerField(db_column='MId') # Link to master ID (tblMM_PR_Master.Id)
    p_id = models.IntegerField(db_column='PId', blank=True, null=True)
    item = models.ForeignKey(Item, models.DO_NOTHING, db_column='ItemId')
    c_id = models.IntegerField(db_column='CId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Details'
        verbose_name = 'Purchase Request Detail'
        verbose_name_plural = 'Purchase Request Details'

    def __str__(self):
        return f"PR Detail {self.id} for Item {self.item.item_code}"


class BaseMaterialPlanningDetail(models.Model):
    """
    Abstract base class for common fields and methods between Raw and Process materials.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    pl_no = models.CharField(db_column='PLNo', max_length=50)
    m_id = models.CharField(db_column='MId', max_length=50) # MId from query string
    item = models.ForeignKey(Item, models.DO_NOTHING, db_column='ItemId')
    supplier = models.ForeignKey(Supplier, models.DO_NOTHING, db_column='SupplierId', blank=True, null=True)
    comp_date = models.DateField(db_column='CompDate', blank=True, null=True)
    p_id = models.IntegerField(db_column='PId', blank=True, null=True)
    c_id = models.IntegerField(db_column='CId', blank=True, null=True)

    class Meta:
        abstract = True # This is an abstract model, not a database table itself

    def has_pr_detail(self, comp_id):
        """
        Checks if a Purchase Request (PR) detail exists for this material detail.
        Corresponds to the disableEdit() logic.
        """
        return PurchaseRequestDetail.objects.filter(
            item=self.item,
            p_id=self.p_id,
            c_id=self.c_id,
            # The original C# has a master PR check which we simplify here
            # assuming PR_Details link to PR_Master which has CompId
            # For direct migration, we would query tblMM_PR_Master and tblMM_PR_Details
            # and check if it matches the current CompId.
            # Simplified: assuming PR_Details check is sufficient for locking.
            # A more robust check might involve tblMM_PR_Master.
        ).exists()

    @property
    def item_code(self):
        return self.item.item_code if self.item else 'N/A'

    @property
    def description(self):
        return self.item.manf_desc if self.item else 'N/A'

    @property
    def unit_symbol(self):
        return self.item.uom_basic.symbol if self.item and self.item.uom_basic else 'N/A'

    @property
    def supplier_display_name(self):
        return str(self.supplier) if self.supplier else 'N/A'


class RawMaterialPlanningDetail(BaseMaterialPlanningDetail):
    """
    Corresponds to tblMP_Material_RawMaterial table.
    """
    class Meta:
        managed = False
        db_table = 'tblMP_Material_RawMaterial'
        verbose_name = 'Raw Material Planning Detail'
        verbose_name_plural = 'Raw Material Planning Details'

    def __str__(self):
        return f"Raw: {self.pl_no} - {self.item.item_code}"

    # Business logic methods specific to Raw Material can go here

class ProcessMaterialPlanningDetail(BaseMaterialPlanningDetail):
    """
    Corresponds to tblMP_Material_Process table.
    """
    class Meta:
        managed = False
        db_table = 'tblMP_Material_Process'
        verbose_name = 'Process Material Planning Detail'
        verbose_name_plural = 'Process Material Planning Details'

    def __str__(self):
        return f"Process: {self.pl_no} - {self.item.item_code}"

    # Business logic methods specific to Process Material can go here
```

#### 4.2 Forms (`material_planning/forms.py`)

We'll create a form for each planning detail type. These forms will be used for updating.
The ASP.NET `getCode` for extracting supplier ID from `SupplierName [ID]` will be handled in the form's `clean_supplier` method or a custom widget/field.

```python
from django import forms
from .models import RawMaterialPlanningDetail, ProcessMaterialPlanningDetail, Supplier
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
import re # For date regex validation

class MaterialPlanningDetailForm(forms.ModelForm):
    """
    Base form for common fields across Raw and Process material details.
    Handles the `SupplierName [ID]` extraction and date validation.
    """
    # Exposing supplier_name as a CharField for autocomplete input,
    # as the model's supplier field is a ForeignKey to Supplier object.
    supplier_display_name = forms.CharField(
        label="Supplier Name",
        required=False, # Original ASP.NET used RequiredFieldValidator conditionally
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Start typing supplier name...',
            # HTMX attributes for autocomplete
            'hx-get': '/material_planning/api/suppliers-autocomplete/',
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#supplier-suggestions',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off', # Prevent browser autocomplete
            '@input': 'clearTimeout($refs.suggestionTimeout); $refs.suggestionTimeout = setTimeout(() => showSuggestions = $el.value.length > 0, 300)',
            '@click.outside': 'showSuggestions = false',
            '@focus': 'showSuggestions = true',
            'x-ref': 'supplierInput'
        })
    )
    # Date field for user input in 'dd-MM-yyyy' format
    comp_date_display = forms.CharField(
        label="Completion Date",
        required=False, # Original ASP.NET used RequiredFieldValidator conditionally
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'DD-MM-YYYY',
            'pattern': r'^(0[1-9]|[12][0-9]|3[01])-(0[1-9]|1[0-2])-\d{4}$', # Client-side regex, server-side in clean
            'x-ref': 'dateInput'
        })
    )

    class Meta:
        abstract = True
        fields = ['supplier', 'comp_date'] # These are model fields, but we'll use display fields for input
        widgets = {
            'supplier': forms.HiddenInput(), # Actual FK, hidden, set by JS/backend logic
            'comp_date': forms.HiddenInput() # Actual DateField, hidden, set by backend logic
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate display fields from instance
        if self.instance.pk:
            self.fields['supplier_display_name'].initial = self.instance.supplier_display_name
            if self.instance.comp_date:
                self.fields['comp_date_display'].initial = self.instance.comp_date.strftime('%d-%m-%Y')
        # Remove the actual model fields from the form rendering, they are handled by clean methods
        self.fields.pop('supplier')
        self.fields.pop('comp_date')

    def clean_supplier_display_name(self):
        display_name = self.cleaned_data.get('supplier_display_name')
        if not display_name:
            return None # Or raise ValidationError if required without condition
        
        match = re.match(r'^(.*?) \[(\d+)\]$', display_name)
        if match:
            supplier_id = match.group(2)
            try:
                supplier = Supplier.objects.get(pk=supplier_id)
                self.cleaned_data['supplier'] = supplier # Set the actual FK object
                return display_name
            except Supplier.DoesNotExist:
                raise ValidationError(_("Invalid supplier selected."))
        else:
            # If not in "Name [ID]" format, try to find by name, or assume manual entry
            # For simplicity, we'll require the [ID] format if a supplier is entered
            raise ValidationError(_("Please select a supplier from the suggestions (e.g., 'Name [ID]')."))

    def clean_comp_date_display(self):
        date_str = self.cleaned_data.get('comp_date_display')
        if not date_str:
            return None # Or raise ValidationError if required without condition

        try:
            # Matches the ASP.NET regex: ^([1-9]|0[1-9]|[12][0-9]|3[01])[- /.]([1-9]|0[1-9]|1[012])[- /.][0-9]{4}$
            # We'll parse to a date object
            self.cleaned_data['comp_date'] = datetime.strptime(date_str, '%d-%m-%Y').date()
            return date_str
        except ValueError:
            raise ValidationError(_("Enter a valid date in DD-MM-YYYY format."))


class RawMaterialPlanningDetailForm(MaterialPlanningDetailForm):
    class Meta(MaterialPlanningDetailForm.Meta):
        model = RawMaterialPlanningDetail
        fields = ['supplier_display_name', 'comp_date_display'] # Use display fields for form input

class ProcessMaterialPlanningDetailForm(MaterialPlanningDetailForm):
    class Meta(MaterialPlanningDetailForm.Meta):
        model = ProcessMaterialPlanningDetail
        fields = ['supplier_display_name', 'comp_date_display'] # Use display fields for form input

from datetime import datetime

class SupplierSearchForm(forms.Form):
    """
    Form for the supplier autocomplete search.
    """
    query = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={'class': 'hidden'}) # Hidden, only used for validation
    )
```

#### 4.3 Views (`material_planning/views.py`)

We'll have a main view to render the page, and separate views for HTMX partials (tables, forms) and update logic. The "fat model, thin view" principle means validation and core data operations reside in forms/models.

```python
from django.views.generic import TemplateView, ListView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, render
from django.db import transaction
import json # For handling JSON responses for HTMX

from .models import RawMaterialPlanningDetail, ProcessMaterialPlanningDetail, Supplier
from .forms import RawMaterialPlanningDetailForm, ProcessMaterialPlanningDetailForm, SupplierSearchForm

# Max lines for view methods: 15. Business logic must be in models or forms.

class MaterialPlanningEditView(TemplateView):
    """
    Main view to display the Material Planning Edit page.
    This replaces the overall ASP.NET page.
    """
    template_name = 'material_planning/planning_edit_details.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # These would typically come from session or route parameters, mimicking ASP.NET
        # For demo, using dummy values or getting from self.request.GET if passed in URL
        context['pl_no'] = self.request.GET.get('plno', 'DUMMYPL001') # From Request.QueryString["plno"]
        context['m_id'] = self.request.GET.get('MId', 'DUMMYMID001') # From Request.QueryString["MId"]
        # Assuming comp_id and fin_year_id from session as in ASP.NET
        context['comp_id'] = self.request.session.get('compid', 1)
        context['fin_year_id'] = self.request.session.get('finyear', 1)
        return context

class RawMaterialsTablePartialView(ListView):
    """
    HTMX partial view to load the raw materials table.
    """
    model = RawMaterialPlanningDetail
    template_name = 'material_planning/_raw_materials_table.html'
    context_object_name = 'raw_materials'

    def get_queryset(self):
        # Mimic ASP.NET filtering by PLNo and MId
        pl_no = self.request.GET.get('pl_no')
        m_id = self.request.GET.get('m_id')
        comp_id = self.request.session.get('compid', 1)

        queryset = RawMaterialPlanningDetail.objects.filter(
            pl_no=pl_no,
            m_id=m_id
        ).order_by('-id').select_related('item__uom_basic', 'supplier') # Optimize lookups
        
        # Pass comp_id to objects for disableEdit logic in template
        for obj in queryset:
            obj.current_comp_id = comp_id 

        return queryset

class ProcessMaterialsTablePartialView(ListView):
    """
    HTMX partial view to load the process materials table.
    """
    model = ProcessMaterialPlanningDetail
    template_name = 'material_planning/_process_materials_table.html'
    context_object_name = 'process_materials'

    def get_queryset(self):
        # Mimic ASP.NET filtering by PLNo and MId
        pl_no = self.request.GET.get('pl_no')
        m_id = self.request.GET.get('m_id')
        comp_id = self.request.session.get('compid', 1)

        queryset = ProcessMaterialPlanningDetail.objects.filter(
            pl_no=pl_no,
            m_id=m_id
        ).order_by('-id').select_related('item__uom_basic', 'supplier') # Optimize lookups

        # Pass comp_id to objects for disableEdit logic in template
        for obj in queryset:
            obj.current_comp_id = comp_id

        return queryset


class UpdateMaterialsView(View):
    """
    Handles batch updates for both raw and process materials from the main form.
    This replaces the GridView_RowCommand logic.
    """
    def post(self, request, *args, **kwargs):
        comp_id = request.session.get('compid', 1)
        updated_count = 0

        # Process Raw Materials
        # HTMX will send data as raw_materials_data=[{'id': 1, 'checked': 'on', 'supplier_display_name': '...', 'comp_date_display': '...'}]
        raw_materials_data = json.loads(request.POST.get('raw_materials_data', '[]'))
        for item_data in raw_materials_data:
            if item_data.get('checked') == 'on':
                pk = item_data.get('id')
                try:
                    obj = RawMaterialPlanningDetail.objects.get(pk=pk)
                    form = RawMaterialPlanningDetailForm(data=item_data, instance=obj)
                    if form.is_valid():
                        form.save() # Saves the actual supplier FK and comp_date DateField
                        updated_count += 1
                    else:
                        messages.error(request, f"Validation failed for Raw Material ID {pk}: {form.errors.as_text()}")
                except RawMaterialPlanningDetail.DoesNotExist:
                    messages.error(request, f"Raw Material with ID {pk} not found.")
                except Exception as e:
                    messages.error(request, f"Error updating Raw Material ID {pk}: {e}")

        # Process Process Materials
        process_materials_data = json.loads(request.POST.get('process_materials_data', '[]'))
        for item_data in process_materials_data:
            if item_data.get('checked') == 'on':
                pk = item_data.get('id')
                try:
                    obj = ProcessMaterialPlanningDetail.objects.get(pk=pk)
                    form = ProcessMaterialPlanningDetailForm(data=item_data, instance=obj)
                    if form.is_valid():
                        form.save()
                        updated_count += 1
                    else:
                        messages.error(request, f"Validation failed for Process Material ID {pk}: {form.errors.as_text()}")
                except ProcessMaterialPlanningDetail.DoesNotExist:
                    messages.error(request, f"Process Material with ID {pk} not found.")
                except Exception as e:
                    messages.error(request, f"Error updating Process Material ID {pk}: {e}")

        if updated_count > 0:
            messages.success(request, f"Successfully updated {updated_count} material planning records.")
        else:
            messages.info(request, "No records selected or updated.")

        # Trigger a refresh of both tables via HTMX
        return HttpResponse(status=204, headers={'HX-Trigger': 'refreshMaterialPlanningTables'})

class SupplierAutocompleteView(View):
    """
    Provides supplier suggestions for autocomplete.
    Replaces the ASP.NET GetCompletionList WebMethod.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('query', '')
        comp_id = request.session.get('compid', 1)
        
        if not prefix_text:
            return JsonResponse([]) # Return empty if no query

        # Filter suppliers by prefix and CompId
        suppliers = Supplier.objects.filter(
            supplier_name__icontains=prefix_text, # Use icontains for case-insensitive search
            comp_id=comp_id
        ).values_list('supplier_name', 'supplier_id')[:10] # Limit to top 10

        suggestions = [f"{name} [{id}]" for name, id in suppliers]
        return JsonResponse(suggestions, safe=False)

class CancelPlanningEditView(View):
    """
    Handles the Cancel button click.
    Redirects to the main planning edit list page.
    """
    def get(self, request, *args, **kwargs):
        messages.info(request, "Material planning edit cancelled.")
        # Simulating the ASP.NET redirect
        # In a real Django app, this would redirect to a named URL, e.g., reverse_lazy('planning_list')
        return HttpResponse(
            status=204,
            headers={'HX-Redirect': reverse_lazy('material_planning:planning_main_list')} # Or a specific URL
        )

# Dummy view for the redirect target of the Cancel button, adjust as needed
class PlanningMainListView(TemplateView):
    template_name = 'material_planning/planning_main_list.html'
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['message'] = "This is the main Material Planning list page."
        return context
```

#### 4.4 Templates (`material_planning/templates/material_planning/`)

We'll create `planning_edit_details.html` as the main page and two partials `_raw_materials_table.html` and `_process_materials_table.html` for HTMX updates.

**`planning_edit_details.html`** (Main Page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8 fontcss">
    <div class="mb-6">
        <h2 class="text-2xl font-bold text-white bg-blue-700 py-2 px-4 rounded-t-md">Material Planning - Edit</h2>
    </div>

    <form id="materialPlanningEditForm" hx-post="{% url 'material_planning:update_materials' %}" hx-trigger="submit" hx-swap="none">
        {% csrf_token %}
        <input type="hidden" name="pl_no" value="{{ pl_no }}">
        <input type="hidden" name="m_id" value="{{ m_id }}">

        <div class="bg-white shadow-md rounded-lg p-6 mb-8">
            <h3 class="text-xl font-semibold mb-4">Raw Material Details</h3>
            <div id="rawMaterialTableContainer"
                 hx-trigger="load, refreshMaterialPlanningTables from:body"
                 hx-get="{% url 'material_planning:raw_materials_table' %}?pl_no={{ pl_no }}&m_id={{ m_id }}"
                 hx-swap="innerHTML">
                <!-- Raw Material DataTable will be loaded here via HTMX -->
                <div class="text-center py-10">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading Raw Materials...</p>
                </div>
            </div>
            
            <div class="mt-4 text-center">
                <button type="submit" name="update_raw" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded redbox">
                    Update Raw Materials
                </button>
            </div>
        </div>

        <div class="bg-white shadow-md rounded-lg p-6 mb-8">
            <h3 class="text-xl font-semibold mb-4">Processing Material Details</h3>
            <div id="processMaterialTableContainer"
                 hx-trigger="load, refreshMaterialPlanningTables from:body"
                 hx-get="{% url 'material_planning:process_materials_table' %}?pl_no={{ pl_no }}&m_id={{ m_id }}"
                 hx-swap="innerHTML">
                <!-- Processing Material DataTable will be loaded here via HTMX -->
                <div class="text-center py-10">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading Processing Materials...</p>
                </div>
            </div>

            <div class="mt-4 text-center">
                <button type="submit" name="update_process" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded redbox">
                    Update Processing Materials
                </button>
            </div>
        </div>
    </form>

    <div class="text-center mt-6">
        <button 
            hx-get="{% url 'material_planning:cancel_planning_edit' %}"
            hx-swap="none"
            class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded redbox">
            Cancel
        </button>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.8/css/dataTables.dataTables.min.css">
<script>
    // Alpine.js setup for row-level editing state and autocomplete
    document.addEventListener('alpine:init', () => {
        Alpine.data('rowData', (initialEditMode, initialSupplierName, initialCompDate) => ({
            editMode: initialEditMode,
            currentSupplierName: initialSupplierName,
            currentCompDate: initialCompDate,
            showSuggestions: false,
            init() {
                this.$watch('currentSupplierName', (val) => {
                    // Update the hidden input field on change
                    this.$refs.supplierInput.value = val;
                });
                this.$watch('currentCompDate', (val) => {
                    // Update the hidden input field on change
                    this.$refs.dateInput.value = val;
                });
            },
            selectSuggestion(suggestion) {
                this.currentSupplierName = suggestion;
                this.showSuggestions = false;
                this.$refs.supplierInput.dispatchEvent(new Event('input')); // Trigger input event for htmx
            },
            toggleEdit() {
                this.editMode = !this.editMode;
                // Update visibility of validation messages on toggle
                this.updateValidationVisibility();
            },
            updateValidationVisibility() {
                // Manually trigger validation display based on editMode
                const formGroup = this.$el; // The x-data root element for the row
                const supplierInput = formGroup.querySelector('[name^="supplier_display_name"]');
                const compDateInput = formGroup.querySelector('[name^="comp_date_display"]');
                
                if (supplierInput && supplierInput._x_model) {
                    supplierInput._x_model.set(supplierInput.value); // Trigger Alpine.js validation
                }
                if (compDateInput && compDateInput._x_model) {
                    compDateInput._x_model.set(compDateInput.value); // Trigger Alpine.js validation
                }
            }
        }));
    });

    // Function to collect table data for batch update
    function collectTableData(tableId) {
        const table = document.getElementById(tableId);
        const data = [];
        $(table).DataTable().rows().every(function() {
            const row = this.node();
            const checkbox = row.querySelector('input[type="checkbox"][name^="selected_item_"]');
            if (checkbox && checkbox.checked) {
                const rowData = {};
                rowData.id = checkbox.value; // Store the ID of the item
                rowData.checked = checkbox.checked ? 'on' : 'off';
                
                const supplierInput = row.querySelector('input[name="supplier_display_name"]');
                if (supplierInput) rowData.supplier_display_name = supplierInput.value;
                
                const compDateInput = row.querySelector('input[name="comp_date_display"]');
                if (compDateInput) rowData.comp_date_display = compDateInput.value;
                
                data.push(rowData);
            }
        });
        return JSON.stringify(data);
    }

    // Intercept form submission to add dynamic data
    document.getElementById('materialPlanningEditForm').addEventListener('htmx:configRequest', function(event) {
        // Collect data from raw materials table
        const rawMaterialsData = collectTableData('rawMaterialTable');
        event.detail.parameters['raw_materials_data'] = rawMaterialsData;

        // Collect data from process materials table
        const processMaterialsData = collectTableData('processMaterialTable');
        event.detail.parameters['process_materials_data'] = processMaterialsData;
    });

</script>
{% endblock %}
```

**`_raw_materials_table.html`** (HTMX Partial)

```html
<table id="rawMaterialTable" class="min-w-full bg-white yui-datatable-theme fontcss">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Edit</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PL No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Comp Date</th>
            <!-- Hidden columns based on original ASP.NET -->
            <th class="hidden">Id</th>
            <th class="hidden">PId</th>
            <th class="hidden">CId</th>
            <th class="hidden">ItemId</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in raw_materials %}
        <tr x-data="rowData({{ not obj.has_pr_detail(obj.current_comp_id) }}, '{{ obj.supplier_display_name }}', '{{ obj.comp_date|date:'d-m-Y' }}')">
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                {% if not obj.has_pr_detail(obj.current_comp_id) %}
                    <input type="checkbox" name="selected_item_{{ obj.pk }}" value="{{ obj.pk }}" @change="toggleEdit">
                {% else %}
                    <span class="text-gray-500">PR</span>
                {% endif %}
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.pl_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.item_code }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.description }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.unit_symbol }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left relative" x-cloak>
                <span x-show="!editMode" class="lblsupl">{{ obj.supplier_display_name }}</span>
                <div x-show="editMode">
                    <input type="text" name="supplier_display_name" class="box3 w-full" x-model="currentSupplierName" x-ref="supplierInput"
                           @keyup.debounce.500ms="showSuggestions = $el.value.length > 0 ? true : false"
                           @focus="showSuggestions = true">
                    <div x-show="showSuggestions" class="absolute z-10 bg-white border border-gray-300 rounded shadow-lg mt-1 w-full max-h-48 overflow-y-auto"
                         id="supplier-suggestions"
                         hx-get="{% url 'material_planning:suppliers_autocomplete' %}"
                         hx-trigger="input changed from:#rawMaterialTable input[name='supplier_display_name']"
                         hx-target="#supplier-suggestions"
                         hx-swap="innerHTML">
                        <!-- Suggestions will be loaded here by HTMX -->
                    </div>
                    {% comment %}
                        Dynamically render validation errors for this row if available (more complex with batch update)
                        For simplicity here, form errors will be in messages framework or need a custom HTMX target for validation.
                    {% endcomment %}
                </div>
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-center" x-cloak>
                <span x-show="!editMode" class="lblCompDate">{{ obj.comp_date|date:'d-m-Y' }}</span>
                <div x-show="editMode">
                    <input type="text" name="comp_date_display" class="box3 w-full" placeholder="DD-MM-YYYY" x-model="currentCompDate" x-ref="dateInput">
                    {% comment %} Date validation errors {% endcomment %}
                </div>
            </td>
            <td class="hidden">{{ obj.id }}</td>
            <td class="hidden">{{ obj.p_id }}</td>
            <td class="hidden">{{ obj.c_id }}</td>
            <td class="hidden">{{ obj.item.id }}</td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="10" class="py-4 px-4 border-b border-gray-200 text-center text-maroon font-bold text-lg">No Raw Material data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Ensure DataTable is initialized after HTMX content load
    if ($.fn.DataTable.isDataTable('#rawMaterialTable')) {
        $('#rawMaterialTable').DataTable().destroy();
    }
    $('#rawMaterialTable').DataTable({
        "pageLength": 15,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [1] }, // Disable sorting on Edit column
            { "visible": false, "targets": [8,9,10,11] } // Hidden columns Id, PId, CId, ItemId
        ]
    });
</script>
```

**`_process_materials_table.html`** (HTMX Partial)

```html
<table id="processMaterialTable" class="min-w-full bg-white yui-datatable-theme fontcss">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Edit</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PL No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Comp Date</th>
            <!-- Hidden columns based on original ASP.NET -->
            <th class="hidden">Id</th>
            <th class="hidden">PId</th>
            <th class="hidden">CId</th>
            <th class="hidden">ItemId</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in process_materials %}
        <tr x-data="rowData({{ not obj.has_pr_detail(obj.current_comp_id) }}, '{{ obj.supplier_display_name }}', '{{ obj.comp_date|date:'d-m-Y' }}')">
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                {% if not obj.has_pr_detail(obj.current_comp_id) %}
                    <input type="checkbox" name="selected_item_{{ obj.pk }}" value="{{ obj.pk }}" @change="toggleEdit">
                {% else %}
                    <span class="text-gray-500">PR</span>
                {% endif %}
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.pl_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.item_code }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.description }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.unit_symbol }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left relative" x-cloak>
                <span x-show="!editMode" class="lblsupl">{{ obj.supplier_display_name }}</span>
                <div x-show="editMode">
                    <input type="text" name="supplier_display_name" class="box3 w-full" x-model="currentSupplierName" x-ref="supplierInput"
                           @keyup.debounce.500ms="showSuggestions = $el.value.length > 0 ? true : false"
                           @focus="showSuggestions = true">
                    <div x-show="showSuggestions" class="absolute z-10 bg-white border border-gray-300 rounded shadow-lg mt-1 w-full max-h-48 overflow-y-auto"
                         id="supplier-suggestions"
                         hx-get="{% url 'material_planning:suppliers_autocomplete' %}"
                         hx-trigger="input changed from:#processMaterialTable input[name='supplier_display_name']"
                         hx-target="#supplier-suggestions"
                         hx-swap="innerHTML">
                        <!-- Suggestions will be loaded here by HTMX -->
                    </div>
                </div>
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-center" x-cloak>
                <span x-show="!editMode" class="lblCompDate">{{ obj.comp_date|date:'d-m-Y' }}</span>
                <div x-show="editMode">
                    <input type="text" name="comp_date_display" class="box3 w-full" placeholder="DD-MM-YYYY" x-model="currentCompDate" x-ref="dateInput">
                </div>
            </td>
            <td class="hidden">{{ obj.id }}</td>
            <td class="hidden">{{ obj.p_id }}</td>
            <td class="hidden">{{ obj.c_id }}</td>
            <td class="hidden">{{ obj.item.id }}</td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="10" class="py-4 px-4 border-b border-gray-200 text-center text-maroon font-bold text-lg">No Processing Material data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    if ($.fn.DataTable.isDataTable('#processMaterialTable')) {
        $('#processMaterialTable').DataTable().destroy();
    }
    $('#processMaterialTable').DataTable({
        "pageLength": 15,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [1] },
            { "visible": false, "targets": [8,9,10,11] }
        ]
    });
</script>
```

**`_supplier_suggestions.html`** (HTMX Partial for Autocomplete)

```html
{% for suggestion in suggestions %}
    <div class="px-4 py-2 hover:bg-gray-100 cursor-pointer" @click="selectSuggestion('{{ suggestion }}')">
        {{ suggestion }}
    </div>
{% empty %}
    <div class="px-4 py-2 text-gray-500">No suggestions</div>
{% endfor %}
```

#### 4.5 URLs (`material_planning/urls.py`)

```python
from django.urls import path
from .views import (
    MaterialPlanningEditView,
    RawMaterialsTablePartialView,
    ProcessMaterialsTablePartialView,
    UpdateMaterialsView,
    SupplierAutocompleteView,
    CancelPlanningEditView,
    PlanningMainListView # Dummy target for cancel button
)

app_name = 'material_planning'

urlpatterns = [
    path('planning-edit/', MaterialPlanningEditView.as_view(), name='planning_edit'),
    path('planning-edit/raw-materials-table/', RawMaterialsTablePartialView.as_view(), name='raw_materials_table'),
    path('planning-edit/process-materials-table/', ProcessMaterialsTablePartialView.as_view(), name='process_materials_table'),
    path('planning-edit/update-materials/', UpdateMaterialsView.as_view(), name='update_materials'),
    path('api/suppliers-autocomplete/', SupplierAutocompleteView.as_view(), name='suppliers_autocomplete'),
    path('planning-edit/cancel/', CancelPlanningEditView.as_view(), name='cancel_planning_edit'),
    # Dummy URL for cancel target, replace with actual main planning list
    path('planning-main-list/', PlanningMainListView.as_view(), name='planning_main_list'),
]
```

#### 4.6 Tests (`material_planning/tests.py`)

Comprehensive tests cover models, forms, and views to ensure functionality and data integrity.

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date
from unittest.mock import patch

from .models import (
    Unit,
    Item,
    Supplier,
    PurchaseRequestDetail,
    RawMaterialPlanningDetail,
    ProcessMaterialPlanningDetail
)
from .forms import RawMaterialPlanningDetailForm, ProcessMaterialPlanningDetailForm

# Mocking the session for tests since ASP.NET used Session["compid"]
class MockSession:
    def get(self, key, default=None):
        if key == 'compid':
            return 1 # Default company ID for tests
        if key == 'finyear':
            return 2024 # Default financial year for tests
        return default

class MaterialPlanningModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary lookup data
        cls.unit_ea = Unit.objects.create(id=1, symbol='EA')
        cls.item_raw = Item.objects.create(id=101, item_code='RM001', manf_desc='Raw Material A', uom_basic=cls.unit_ea, comp_id=1)
        cls.item_process = Item.objects.create(id=102, item_code='PM001', manf_desc='Process Material B', uom_basic=cls.unit_ea, comp_id=1)
        cls.supplier1 = Supplier.objects.create(supplier_id=1, supplier_name='Supplier Alpha', comp_id=1)
        cls.supplier2 = Supplier.objects.create(supplier_id=2, supplier_name='Supplier Beta', comp_id=1)

        # Create planning details
        cls.raw_detail1 = RawMaterialPlanningDetail.objects.create(
            id=1, pl_no='PL001', m_id='M001', item=cls.item_raw, supplier=cls.supplier1, comp_date=date(2024, 7, 1), p_id=1, c_id=1
        )
        cls.raw_detail2 = RawMaterialPlanningDetail.objects.create(
            id=2, pl_no='PL001', m_id='M001', item=cls.item_raw, supplier=cls.supplier2, comp_date=date(2024, 7, 5), p_id=2, c_id=1
        )
        cls.process_detail1 = ProcessMaterialPlanningDetail.objects.create(
            id=3, pl_no='PL001', m_id='M001', item=cls.item_process, supplier=cls.supplier1, comp_date=date(2024, 7, 10), p_id=1, c_id=2
        )

        # Create a PR detail to test disableEdit logic
        cls.pr_detail = PurchaseRequestDetail.objects.create(
            id=1, pr_no='PR001', m_id=1, item=cls.item_raw, p_id=1, c_id=1
        )

    def test_raw_material_detail_creation(self):
        self.assertEqual(self.raw_detail1.pl_no, 'PL001')
        self.assertEqual(self.raw_detail1.item.item_code, 'RM001')
        self.assertEqual(self.raw_detail1.supplier.supplier_name, 'Supplier Alpha')
        self.assertEqual(self.raw_detail1.comp_date, date(2024, 7, 1))

    def test_process_material_detail_creation(self):
        self.assertEqual(self.process_detail1.pl_no, 'PL001')
        self.assertEqual(self.process_detail1.item.item_code, 'PM001')

    def test_item_properties(self):
        self.assertEqual(self.raw_detail1.item_code, 'RM001')
        self.assertEqual(self.raw_detail1.description, 'Raw Material A')
        self.assertEqual(self.raw_detail1.unit_symbol, 'EA')
        self.assertEqual(self.raw_detail1.supplier_display_name, 'Supplier Alpha [1]')

    def test_has_pr_detail_method(self):
        # raw_detail1 has a corresponding PR detail
        self.assertTrue(self.raw_detail1.has_pr_detail(comp_id=1))
        # raw_detail2 does not have a corresponding PR detail
        self.assertFalse(self.raw_detail2.has_pr_detail(comp_id=1))
        # process_detail1 does not have a corresponding PR detail
        self.assertFalse(self.process_detail1.has_pr_detail(comp_id=1))

class MaterialPlanningFormsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.unit_ea = Unit.objects.create(id=1, symbol='EA')
        cls.item_raw = Item.objects.create(id=101, item_code='RM001', manf_desc='Raw Material A', uom_basic=cls.unit_ea, comp_id=1)
        cls.supplier1 = Supplier.objects.create(supplier_id=1, supplier_name='Supplier Alpha', comp_id=1)
        cls.supplier2 = Supplier.objects.create(supplier_id=2, supplier_name='Supplier Beta', comp_id=1)
        cls.raw_detail1 = RawMaterialPlanningDetail.objects.create(
            id=1, pl_no='PL001', m_id='M001', item=cls.item_raw, supplier=cls.supplier1, comp_date=date(2024, 7, 1), p_id=1, c_id=1
        )

    def test_raw_material_form_valid_data(self):
        data = {
            'supplier_display_name': 'Supplier Beta [2]',
            'comp_date_display': '15-08-2024',
        }
        form = RawMaterialPlanningDetailForm(data=data, instance=self.raw_detail1)
        self.assertTrue(form.is_valid(), form.errors)
        self.assertEqual(form.cleaned_data['supplier'], self.supplier2)
        self.assertEqual(form.cleaned_data['comp_date'], date(2024, 8, 15))

    def test_raw_material_form_invalid_supplier_format(self):
        data = {
            'supplier_display_name': 'Invalid Supplier',
            'comp_date_display': '15-08-2024',
        }
        form = RawMaterialPlanningDetailForm(data=data, instance=self.raw_detail1)
        self.assertFalse(form.is_valid())
        self.assertIn('Please select a supplier from the suggestions', form.errors['supplier_display_name'][0])

    def test_raw_material_form_invalid_date_format(self):
        data = {
            'supplier_display_name': 'Supplier Beta [2]',
            'comp_date_display': '2024-08-15', # Wrong format
        }
        form = RawMaterialPlanningDetailForm(data=data, instance=self.raw_detail1)
        self.assertFalse(form.is_valid())
        self.assertIn('Enter a valid date in DD-MM-YYYY format.', form.errors['comp_date_display'][0])

    def test_raw_material_form_partial_update(self):
        data = {
            'supplier_display_name': 'Supplier Beta [2]',
            'comp_date_display': '01-07-2024', # Date is same as initial
        }
        form = RawMaterialPlanningDetailForm(data=data, instance=self.raw_detail1)
        self.assertTrue(form.is_valid())
        updated_detail = form.save()
        self.assertEqual(updated_detail.supplier, self.supplier2)
        self.assertEqual(updated_detail.comp_date, date(2024, 7, 1))

class MaterialPlanningViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.unit_ea = Unit.objects.create(id=1, symbol='EA')
        cls.item_raw = Item.objects.create(id=101, item_code='RM001', manf_desc='Raw Material A', uom_basic=cls.unit_ea, comp_id=1)
        cls.item_process = Item.objects.create(id=102, item_code='PM001', manf_desc='Process Material B', uom_basic=cls.unit_ea, comp_id=1)
        cls.supplier1 = Supplier.objects.create(supplier_id=1, supplier_name='Supplier Alpha', comp_id=1)
        cls.supplier2 = Supplier.objects.create(supplier_id=2, supplier_name='Supplier Beta', comp_id=1)

        RawMaterialPlanningDetail.objects.create(
            id=1, pl_no='PL001', m_id='M001', item=cls.item_raw, supplier=cls.supplier1, comp_date=date(2024, 7, 1), p_id=1, c_id=1
        )
        RawMaterialPlanningDetail.objects.create(
            id=2, pl_no='PL001', m_id='M001', item=cls.item_raw, supplier=cls.supplier2, comp_date=date(2024, 7, 5), p_id=2, c_id=1
        )
        ProcessMaterialPlanningDetail.objects.create(
            id=3, pl_no='PL001', m_id='M001', item=cls.item_process, supplier=cls.supplier1, comp_date=date(2024, 7, 10), p_id=1, c_id=2
        )
        PurchaseRequestDetail.objects.create(
            id=1, pr_no='PR001', m_id=1, item=cls.item_raw, p_id=1, c_id=1 # This will lock raw_detail1
        )

    def setUp(self):
        self.client = Client()
        self.client.session['compid'] = 1 # Mimic session compid

    def test_material_planning_edit_view(self):
        response = self.client.get(reverse('material_planning:planning_edit') + '?plno=PL001&MId=M001')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/planning_edit_details.html')
        self.assertContains(response, 'Material Planning - Edit')
        self.assertContains(response, 'rawMaterialTableContainer')
        self.assertContains(response, 'processMaterialTableContainer')

    def test_raw_materials_table_partial_view(self):
        response = self.client.get(reverse('material_planning:raw_materials_table') + '?pl_no=PL001&m_id=M001')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/_raw_materials_table.html')
        self.assertContains(response, 'Raw Material A') # Item description
        self.assertContains(response, 'RM001') # Item code
        self.assertContains(response, 'Supplier Alpha [1]') # Supplier display name
        self.assertContains(response, 'PR') # Check if disable edit logic works for raw_detail1
        self.assertContains(response, '<input type="checkbox" name="selected_item_2" value="2"') # Checkbox for raw_detail2

    def test_process_materials_table_partial_view(self):
        response = self.client.get(reverse('material_planning:process_materials_table') + '?pl_no=PL001&m_id=M001')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/_process_materials_table.html')
        self.assertContains(response, 'Process Material B')
        self.assertContains(response, 'PM001')
        self.assertContains(response, 'Supplier Alpha [1]')
        self.assertNotContains(response, 'PR') # Should not contain 'PR' for process materials

    def test_update_materials_view_raw_material(self):
        # Data for updating raw_detail2
        update_data = {
            'raw_materials_data': json.dumps([
                {
                    'id': 2,
                    'checked': 'on',
                    'supplier_display_name': 'Supplier Alpha [1]',
                    'comp_date_display': '20-07-2024'
                }
            ]),
            'process_materials_data': '[]' # No process materials to update
        }
        response = self.client.post(
            reverse('material_planning:update_materials'),
            data=update_data,
            HTTP_HX_REQUEST='true', # Simulate HTMX request
            content_type='application/x-www-form-urlencoded' # HTMX sends form data this way by default
        )
        self.assertEqual(response.status_code, 204) # HTMX success without content
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMaterialPlanningTables', response.headers['HX-Trigger'])

        raw_detail2 = RawMaterialPlanningDetail.objects.get(id=2)
        self.assertEqual(raw_detail2.supplier, self.supplier1)
        self.assertEqual(raw_detail2.comp_date, date(2024, 7, 20))

    def test_update_materials_view_process_material(self):
        # Data for updating process_detail1
        update_data = {
            'raw_materials_data': '[]',
            'process_materials_data': json.dumps([
                {
                    'id': 3,
                    'checked': 'on',
                    'supplier_display_name': 'Supplier Beta [2]',
                    'comp_date_display': '25-07-2024'
                }
            ])
        }
        response = self.client.post(
            reverse('material_planning:update_materials'),
            data=update_data,
            HTTP_HX_REQUEST='true',
            content_type='application/x-www-form-urlencoded'
        )
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)

        process_detail1 = ProcessMaterialPlanningDetail.objects.get(id=3)
        self.assertEqual(process_detail1.supplier, self.supplier2)
        self.assertEqual(process_detail1.comp_date, date(2024, 7, 25))

    def test_update_materials_view_invalid_data(self):
        # Invalid date format
        update_data = {
            'raw_materials_data': json.dumps([
                {
                    'id': 2,
                    'checked': 'on',
                    'supplier_display_name': 'Supplier Alpha [1]',
                    'comp_date_display': 'invalid-date'
                }
            ]),
            'process_materials_data': '[]'
        }
        response = self.client.post(
            reverse('material_planning:update_materials'),
            data=update_data,
            HTTP_HX_REQUEST='true',
            content_type='application/x-www-form-urlencoded'
        )
        self.assertEqual(response.status_code, 204) # Still 204 with messages
        messages = list(response.context['messages']) if response.context else []
        self.assertTrue(any(str(m) == "Validation failed for Raw Material ID 2: comp_date_display\n  Enter a valid date in DD-MM-YYYY format." for m in messages))

    def test_supplier_autocomplete_view(self):
        # Mocking HttpContext.Current.Session as done in ASP.NET
        with patch('django.http.request.HttpRequest.session', new_callable=MockSession):
            response = self.client.get(reverse('material_planning:suppliers_autocomplete') + '?query=Supp')
            self.assertEqual(response.status_code, 200)
            self.assertEqual(response['Content-Type'], 'application/json')
            suggestions = response.json()
            self.assertIn('Supplier Alpha [1]', suggestions)
            self.assertIn('Supplier Beta [2]', suggestions)
            self.assertNotIn('Another Supplier [3]', suggestions) # If it existed and didn't match

    def test_cancel_planning_edit_view(self):
        response = self.client.get(reverse('material_planning:cancel_planning_edit'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Redirect', response.headers)
        self.assertEqual(response.headers['HX-Redirect'], reverse('material_planning:planning_main_list'))

```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for Dynamic Content:**
    *   `planning_edit_details.html` uses `hx-get` on `rawMaterialTableContainer` and `processMaterialTableContainer` with `hx-trigger="load, refreshMaterialPlanningTables from:body"` to initially load and then refresh the table content.
    *   The "Update" buttons on the main form use `hx-post` to `{% url 'material_planning:update_materials' %}`. `hx-swap="none"` prevents content swap on success, relying on `HX-Trigger` from the server to refresh tables.
    *   `htmx:configRequest` listener collects data from checked rows in both tables and sends it as JSON strings in the POST request.
    *   Supplier autocomplete `input` fields use `hx-get` to `{% url 'material_planning:suppliers_autocomplete' %}` with `hx-trigger="keyup changed delay:500ms"` to fetch suggestions dynamically. `hx-target` is set to `#supplier-suggestions` (an Alpine.js controlled `div`) and `hx-swap="innerHTML"`.
    *   The "Cancel" button uses `hx-get` to `{% url 'material_planning:cancel_planning_edit' %}` and `hx-swap="none"`. The view returns an `HX-Redirect` header to navigate.
*   **Alpine.js for UI State and Interactivity:**
    *   Each table row (`<tr>`) uses `x-data="rowData(...)"` to manage its local `editMode` state.
    *   The `editMode` variable controls the visibility of `<span>` (display) vs. `<input>` (edit) elements using `x-show`.
    *   The checkbox `input` uses `@change="toggleEdit"` to switch the `editMode` state for that row.
    *   `x-model` binds input field values (`currentSupplierName`, `currentCompDate`) to the Alpine.js component's state, keeping them in sync.
    *   The supplier suggestion `div` uses `x-show="showSuggestions"` to control its visibility.
    *   `@click="selectSuggestion(...)"` on suggestion items updates the `currentSupplierName` and hides suggestions.
*   **DataTables Integration:**
    *   Both `_raw_materials_table.html` and `_process_materials_table.html` include JavaScript to initialize DataTables on their respective tables (`#rawMaterialTable`, `#processMaterialTable`).
    *   The DataTables initialization (`$(document).ready(function() { ... });`) is wrapped in an `if ($.fn.DataTable.isDataTable(...)) { ... .destroy(); }` block to ensure re-initialization works correctly when HTMX reloads the table partial, preventing errors.
    *   Configuration (`pageLength`, `lengthMenu`, `columnDefs`) is applied to mimic original GridView settings.

### Final Notes

*   **Placeholders:** Replace `DUMMYPL001`, `DUMMYMID001`, `1` (for `CompId`), `2024` (for `FinYearId`) with actual logic to retrieve these from `request.GET`, `request.session`, or other appropriate sources as your application context requires.
*   **CSS:** The `fontcss`, `redbox`, `box3` classes are assumed to be handled by Tailwind CSS compilation as per the system instructions. Ensure your Tailwind configuration includes these custom styles if they are not standard utility classes.
*   **Error Handling:** The `UpdateMaterialsView` includes basic `messages.error` for validation and exceptions. For a production system, a more robust error display, potentially using HTMX `hx-swap="outerHTML"` on specific error message div or global alert component, would be beneficial.
*   **Authentication/Authorization:** This plan assumes the user is authenticated and authorized to perform these actions. In a real application, Django's built-in authentication system and decorators (e.g., `@login_required`, `LoginRequiredMixin`) would be used.
*   **Database Migrations:** Remember to use Django's `inspectdb` to initially generate models from your existing database and then maintain them. For `managed = False` models, Django won't create or modify table schemas, but it will still help with ORM access.
*   **Modularity:** Consider breaking down `Supplier`, `Item`, `Unit`, and `PurchaseRequestDetail` into separate "master data" or "core" Django apps if they are shared across many modules in your ERP. For this specific migration, keeping them within `material_planning` simplifies the scope.