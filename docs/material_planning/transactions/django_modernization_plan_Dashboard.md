## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

The provided ASP.NET code is a very minimal placeholder. It defines an empty `Dashboard.aspx` page that inherits from a master page and has an empty `Page_Load` event in its code-behind. There are no explicit database interactions, UI controls (like GridViews, TextBoxes), or business logic present.

Given this lack of detail, we will proceed with a *generic example* of how a typical "Dashboard" component in a Material Planning module would be modernized. We will *infer* a simple entity, "Material Planning Item," and demonstrate the full CRUD (Create, Read, Update, Delete) cycle for it, assuming it would be a core piece of data displayed or managed on such a dashboard.

---

## Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
Since the provided ASP.NET code does not contain any database-related elements (like `SqlDataSource`, direct SQL commands, or UI bindings to specific data fields), we cannot extract a schema directly.

**Inferred Schema (for demonstration purposes):**
We will assume a database table named `material_planning_item` that stores information about items relevant to material planning.

*   **`[TABLE_NAME]`**: `material_planning_item`
*   **Columns**:
    *   `id` (Primary Key, integer)
    *   `item_name` (Text/NVARCHAR)
    *   `quantity` (Integer)
    *   `status` (Text/NVARCHAR, e.g., 'Pending', 'Approved', 'Rejected')
    *   `required_by_date` (Date/DateTime)
    *   `last_updated` (DateTime)

---

## Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
The provided ASP.NET code does not contain any explicit backend functionality or CRUD operations. The `Page_Load` event is empty.

**Inferred Functionality (for demonstration purposes):**
Based on the typical needs of a "Dashboard" in a "Material Planning" module, we will assume standard CRUD operations for `MaterialPlanningItem`s:

*   **Create:** Ability to add new material planning items.
*   **Read:** Display a list of all material planning items, potentially with filtering and sorting.
*   **Update:** Ability to modify existing material planning items.
*   **Delete:** Ability to remove material planning items.
*   **Validation:** Basic validation for input fields (e.g., `item_name` required, `quantity` positive integer).

---

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The provided ASP.NET code only specifies content placeholders (`<asp:Content>`). No interactive UI controls like `GridView`, `TextBox`, or `Button` are present.

**Inferred UI Components (for demonstration purposes):**
To support the inferred CRUD functionality, we will assume the following UI components are required:

*   **List View:** A table (which will be powered by DataTables in Django) to display all `MaterialPlanningItem`s. This would typically show `item_name`, `quantity`, `status`, `required_by_date`, and `last_updated`, along with "Edit" and "Delete" action buttons for each row.
*   **Form for Create/Update:** A form with input fields for `item_name`, `quantity`, `status`, and `required_by_date`. This form will likely appear in a modal for a smooth user experience, triggered by "Add New" or "Edit" buttons.
*   **Confirmation Dialog for Delete:** A simple dialog to confirm deletion of an item, also appearing in a modal, triggered by a "Delete" button.
*   **JavaScript:** The original ASP.NET code mentions `loadingNotifier.js`. While the exact function is unknown, we will ensure that our Django solution handles loading states gracefully using HTMX and Alpine.js, eliminating the need for custom JS files.

---

## Step 4: Generate Django Code

We will create a new Django app named `material_planning` to house these components.

### 4.1 Models

**Task:** Create a Django model based on the inferred database schema.

**Instructions:**
The model `MaterialPlanningItem` is mapped to the `material_planning_item` table. We include a simple business logic method `is_urgent()` as an example of a "fat model."

```python
# material_planning/models.py
from django.db import models
from django.utils import timezone

class MaterialPlanningItem(models.Model):
    """
    Represents an item in the material planning module.
    Mapped to an existing database table 'material_planning_item'.
    """
    id = models.AutoField(db_column='id', primary_key=True)
    item_name = models.CharField(db_column='item_name', max_length=255, verbose_name="Item Name")
    quantity = models.IntegerField(db_column='quantity', verbose_name="Quantity")
    status = models.CharField(
        db_column='status', 
        max_length=50, 
        choices=[
            ('Pending', 'Pending'),
            ('Approved', 'Approved'),
            ('Rejected', 'Rejected')
        ],
        default='Pending',
        verbose_name="Status"
    )
    required_by_date = models.DateField(db_column='required_by_date', verbose_name="Required By Date")
    last_updated = models.DateTimeField(db_column='last_updated', auto_now=True, verbose_name="Last Updated")

    class Meta:
        managed = False  # Important: Django won't manage this table's schema
        db_table = 'material_planning_item'
        verbose_name = 'Material Planning Item'
        verbose_name_plural = 'Material Planning Items'
        ordering = ['required_by_date', 'item_name'] # Default ordering for lists

    def __str__(self):
        """String representation of the Material Planning Item."""
        return f"{self.item_name} (Qty: {self.quantity}, Status: {self.status})"
        
    def is_urgent(self):
        """
        Business logic: Determines if the item is urgent based on the required_by_date.
        An item is considered urgent if its required by date is within the next 7 days.
        """
        seven_days_from_now = timezone.now().date() + timezone.timedelta(days=7)
        return self.required_by_date <= seven_days_from_now and self.status == 'Pending'

    def update_status(self, new_status):
        """
        Business logic: Updates the status of the item.
        Performs validation to ensure status is valid.
        """
        if new_status not in [choice[0] for choice in self._meta.get_field('status').choices]:
            raise ValueError(f"Invalid status: {new_status}")
        self.status = new_status
        self.save() # Save the change to the database
        return True
```

### 4.2 Forms

**Task:** Define a Django form for user input for `MaterialPlanningItem`.

**Instructions:**
A `ModelForm` is used to directly map to the `MaterialPlanningItem` model. Widgets are styled with Tailwind CSS classes.

```python
# material_planning/forms.py
from django import forms
from .models import MaterialPlanningItem

class MaterialPlanningItemForm(forms.ModelForm):
    """
    Form for creating and updating MaterialPlanningItem instances.
    """
    class Meta:
        model = MaterialPlanningItem
        fields = ['item_name', 'quantity', 'status', 'required_by_date']
        widgets = {
            'item_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'quantity': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'status': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'required_by_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm datepicker', 'type': 'date'}),
        }
        
    def clean_quantity(self):
        """Custom validation for quantity field."""
        quantity = self.cleaned_data.get('quantity')
        if quantity is not None and quantity <= 0:
            raise forms.ValidationError("Quantity must be a positive number.")
        return quantity
```

### 4.3 Views

**Task:** Implement CRUD operations using CBVs.

**Instructions:**
Views are kept thin, adhering to the 5-15 line limit for methods. Business logic is delegated to the model or handled by Django's built-in form validation. HTMX-specific headers are used for dynamic updates and modal closures. A separate `TablePartialView` is added for HTMX to reload just the DataTable.

```python
# material_planning/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import MaterialPlanningItem
from .forms import MaterialPlanningItemForm

# Base URL for redirects after successful form submission
SUCCESS_URL = reverse_lazy('material_planning_item_list')

class MaterialPlanningItemListView(ListView):
    """
    Displays a list of Material Planning Items.
    The main view for the dashboard page.
    """
    model = MaterialPlanningItem
    template_name = 'material_planning/materialplanningitem/list.html'
    context_object_name = 'material_planning_items'

class MaterialPlanningItemTablePartialView(ListView):
    """
    Renders only the table portion of the Material Planning Item list.
    Used by HTMX to refresh the table content without a full page reload.
    """
    model = MaterialPlanningItem
    template_name = 'material_planning/materialplanningitem/_materialplanningitem_table.html'
    context_object_name = 'material_planning_items'

class MaterialPlanningItemCreateView(CreateView):
    """
    Handles creation of a new Material Planning Item.
    Renders a form and processes its submission.
    """
    model = MaterialPlanningItem
    form_class = MaterialPlanningItemForm
    template_name = 'material_planning/materialplanningitem/_materialplanningitem_form.html' # Partial for modal
    success_url = SUCCESS_URL

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Material Planning Item added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, respond with a 204 No Content and a trigger header
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshMaterialPlanningItemList'}
            )
        return response # Fallback for non-HTMX requests (unlikely with this setup)

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return the form with errors
            return response
        return response # Fallback for non-HTMX requests

class MaterialPlanningItemUpdateView(UpdateView):
    """
    Handles updating an existing Material Planning Item.
    Renders a pre-filled form and processes its submission.
    """
    model = MaterialPlanningItem
    form_class = MaterialPlanningItemForm
    template_name = 'material_planning/materialplanningitem/_materialplanningitem_form.html' # Partial for modal
    success_url = SUCCESS_URL

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Material Planning Item updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshMaterialPlanningItemList'}
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response
        return response

class MaterialPlanningItemDeleteView(DeleteView):
    """
    Handles deletion of a Material Planning Item.
    Renders a confirmation dialog and processes deletion.
    """
    model = MaterialPlanningItem
    template_name = 'material_planning/materialplanningitem/_materialplanningitem_confirm_delete.html' # Partial for modal
    success_url = SUCCESS_URL

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Material Planning Item deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshMaterialPlanningItemList'}
            )
        return response
```

### 4.4 Templates

**Task:** Create templates for each view.

**Instructions:**
Templates are component-specific. `list.html` extends `core/base.html`. Partial templates (`_*.html`) are used for forms and delete confirmations that load dynamically via HTMX into a modal.

```html
{# material_planning/templates/material_planning/materialplanningitem/list.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Material Planning Items</h2>
        <button 
            class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'material_planning_item_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Item
        </button>
    </div>
    
    <div id="material-planning-item-table-container"
         hx-trigger="load, refreshMaterialPlanningItemList from:body"
         hx-get="{% url 'material_planning_item_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg p-6">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-indigo-500"></div>
            <p class="mt-4 text-gray-600">Loading Material Planning Items...</p>
        </div>
    </div>
    
    <!-- HTMX/Alpine.js Modal Structure -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 hidden"
         x-data="{ showModal: false }"
         x-show="showModal"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
         x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
         x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-8 rounded-lg shadow-2xl max-w-2xl w-full mx-4"
             _="on htmx:afterOnLoad add .is-active to #modal">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize Alpine.js for modal management
    document.addEventListener('alpine:init', () => {
        Alpine.store('modal', {
            isOpen: false,
            open() { this.isOpen = true },
            close() { this.isOpen = false }
        });
    });

    // Close modal on HTMX success trigger
    document.body.addEventListener('htmx:afterOnLoad', function(evt) {
        if (evt.detail.xhr.status === 204) { // HTMX success response for form submissions
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.remove('is-active');
                modal.classList.add('hidden'); // Ensure it's hidden
            }
        }
    });

    // Listen for the 'refreshMaterialPlanningItemList' custom event to close the modal
    document.body.addEventListener('refreshMaterialPlanningItemList', function() {
        const modal = document.getElementById('modal');
        if (modal) {
            modal.classList.remove('is-active');
            modal.classList.add('hidden');
        }
    });
</script>
{% endblock %}
```

```html
{# material_planning/templates/material_planning/materialplanningitem/_materialplanningitem_table.html #}
<div class="overflow-x-auto">
    <table id="materialPlanningItemTable" class="min-w-full bg-white border border-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Name</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Required By</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% for obj in material_planning_items %}
            <tr class="hover:bg-gray-50 {% if obj.is_urgent %}bg-red-50/50{% endif %}">
                <td class="py-3 px-4 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap">{{ obj.item_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap">{{ obj.quantity }}</td>
                <td class="py-3 px-4 whitespace-nowrap">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                        {% if obj.status == 'Approved' %}bg-green-100 text-green-800
                        {% elif obj.status == 'Pending' %}bg-yellow-100 text-yellow-800
                        {% elif obj.status == 'Rejected' %}bg-red-100 text-red-800
                        {% endif %}">
                        {{ obj.status }}
                    </span>
                    {% if obj.is_urgent %}
                        <span class="ml-2 text-red-500 text-xs font-bold">URGENT</span>
                    {% endif %}
                </td>
                <td class="py-3 px-4 whitespace-nowrap">{{ obj.required_by_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-4 whitespace-nowrap">{{ obj.last_updated|date:"Y-m-d H:i" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-right text-sm font-medium">
                    <button 
                        class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-1 px-3 rounded-md mr-2 text-xs transition duration-300"
                        hx-get="{% url 'material_planning_item_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-xs transition duration-300"
                        hx-get="{% url 'material_planning_item_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-4 text-center text-gray-500">No material planning items found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after HTMX loads the table content
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#materialPlanningItemTable')) {
            $('#materialPlanningItemTable').DataTable().destroy();
        }
        $('#materialPlanningItemTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "order": [[4, 'asc']], // Order by required_by_date by default
        });
    });
</script>
```

```html
{# material_planning/templates/material_planning/materialplanningitem/_materialplanningitem_form.html #}
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">
        {{ form.instance.pk|yesno:'Edit,Add' }} Material Planning Item
    </h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div class="relative z-0 w-full mb-6 group">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="text-red-600 text-sm mt-2 list-none p-0">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300">
                Save Item
            </button>
            <div id="form-spinner" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-indigo-500"></div>
            </div>
        </div>
    </form>
</div>
```

```html
{# material_planning/templates/material_planning/materialplanningitem/_materialplanningitem_confirm_delete.html #}
<div class="p-6 text-center">
    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
        <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
        </svg>
    </div>
    <h3 class="mt-5 text-lg leading-6 font-medium text-gray-900">Delete Item?</h3>
    <div class="mt-2 px-7 py-3">
        <p class="text-sm text-gray-500">
            Are you sure you want to delete the item "{{ object.item_name }}"?
            This action cannot be undone.
        </p>
    </div>
    <div class="mt-6 flex justify-center space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button 
            type="button" 
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300"
            hx-delete="{% url 'material_planning_item_delete' object.pk %}" 
            hx-swap="none"
            hx-indicator="#delete-spinner">
            Delete
        </button>
        <div id="delete-spinner" class="htmx-indicator ml-3">
            <div class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-red-500"></div>
        </div>
    </div>
</div>
```

### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
URLs are defined within `material_planning/urls.py` and would be included in the project's main `urls.py`.

```python
# material_planning/urls.py
from django.urls import path
from .views import (
    MaterialPlanningItemListView, 
    MaterialPlanningItemCreateView, 
    MaterialPlanningItemUpdateView, 
    MaterialPlanningItemDeleteView,
    MaterialPlanningItemTablePartialView
)

urlpatterns = [
    # Main dashboard view displaying the list
    path('material-planning/dashboard/', MaterialPlanningItemListView.as_view(), name='material_planning_item_list'),
    
    # HTMX endpoint to load/refresh the table content
    path('material-planning/table/', MaterialPlanningItemTablePartialView.as_view(), name='material_planning_item_table'),

    # CRUD operations, designed to be loaded into modals via HTMX
    path('material-planning/add/', MaterialPlanningItemCreateView.as_view(), name='material_planning_item_add'),
    path('material-planning/edit/<int:pk>/', MaterialPlanningItemUpdateView.as_view(), name='material_planning_item_edit'),
    path('material-planning/delete/<int:pk>/', MaterialPlanningItemDeleteView.as_view(), name='material_planning_item_delete'),
]
```

### 4.6 Tests

**Task:** Write tests for the model and views.

**Instructions:**
Comprehensive unit tests for model methods and integration tests for all views ensuring proper responses, template usage, and HTMX interactions.

```python
# material_planning/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import MaterialPlanningItem
import datetime

class MaterialPlanningItemModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.item1 = MaterialPlanningItem.objects.create(
            item_name='Steel Beams',
            quantity=100,
            status='Pending',
            required_by_date=timezone.now().date() + datetime.timedelta(days=5) # Urgent
        )
        cls.item2 = MaterialPlanningItem.objects.create(
            item_name='Copper Wire',
            quantity=500,
            status='Approved',
            required_by_date=timezone.now().date() + datetime.timedelta(days=30) # Not urgent
        )
        cls.item3 = MaterialPlanningItem.objects.create(
            item_name='Plastic Tubes',
            quantity=200,
            status='Pending',
            required_by_date=timezone.now().date() + datetime.timedelta(days=10) # Not urgent yet
        )
  
    def test_item_creation(self):
        """Test that a MaterialPlanningItem can be created successfully."""
        self.assertEqual(self.item1.item_name, 'Steel Beams')
        self.assertEqual(self.item1.quantity, 100)
        self.assertEqual(self.item1.status, 'Pending')
        self.assertIsNotNone(self.item1.last_updated)

    def test_item_name_label(self):
        """Test the verbose name for item_name field."""
        field_label = self.item1._meta.get_field('item_name').verbose_name
        self.assertEqual(field_label, 'Item Name')
        
    def test_quantity_label(self):
        """Test the verbose name for quantity field."""
        field_label = self.item1._meta.get_field('quantity').verbose_name
        self.assertEqual(field_label, 'Quantity')

    def test_str_representation(self):
        """Test the __str__ method of the model."""
        expected_str = f"{self.item1.item_name} (Qty: {self.item1.quantity}, Status: {self.item1.status})"
        self.assertEqual(str(self.item1), expected_str)

    def test_is_urgent_method(self):
        """Test the is_urgent business logic method."""
        # item1 should be urgent
        self.assertTrue(self.item1.is_urgent())
        
        # item2 should not be urgent (status Approved)
        self.assertFalse(self.item2.is_urgent())

        # item3 should not be urgent (required_by_date > 7 days)
        self.assertFalse(self.item3.is_urgent())

        # Change item3 to be urgent
        self.item3.required_by_date = timezone.now().date() + datetime.timedelta(days=3)
        self.item3.save()
        self.assertTrue(self.item3.is_urgent())

    def test_update_status_method(self):
        """Test the update_status business logic method."""
        self.item1.update_status('Approved')
        self.item1.refresh_from_db() # Reload from DB to ensure changes are saved
        self.assertEqual(self.item1.status, 'Approved')

        # Test invalid status
        with self.assertRaises(ValueError):
            self.item1.update_status('Invalid Status')

class MaterialPlanningItemViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.item = MaterialPlanningItem.objects.create(
            item_name='Widget A',
            quantity=10,
            status='Pending',
            required_by_date=timezone.now().date()
        )
    
    def setUp(self):
        # Set up data for each test method that needs a fresh client
        self.client = Client()
    
    def test_list_view_get(self):
        """Test GET request to the MaterialPlanningItem list view."""
        response = self.client.get(reverse('material_planning_item_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/materialplanningitem/list.html')
        self.assertIn('material_planning_items', response.context)
        self.assertContains(response, 'Widget A') # Check if item name is in content

    def test_table_partial_view_get(self):
        """Test GET request to the HTMX-specific table partial view."""
        response = self.client.get(reverse('material_planning_item_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/materialplanningitem/_materialplanningitem_table.html')
        self.assertIn('material_planning_items', response.context)
        self.assertContains(response, 'table') # Check if table structure is present

    def test_create_view_get(self):
        """Test GET request to the MaterialPlanningItem create view (for modal)."""
        response = self.client.get(reverse('material_planning_item_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/materialplanningitem/_materialplanningitem_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Material Planning Item')

    def test_create_view_post_success(self):
        """Test POST request to create a MaterialPlanningItem successfully."""
        data = {
            'item_name': 'New Item B',
            'quantity': 25,
            'status': 'Pending',
            'required_by_date': '2023-12-31'
        }
        response = self.client.post(reverse('material_planning_item_add'), data, HTTP_HX_REQUEST='true')
        
        # Expect 204 No Content for HTMX successful form submission
        self.assertEqual(response.status_code, 204)
        # Check if HX-Trigger header is present
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialPlanningItemList')
        
        # Verify object was created in the database
        self.assertTrue(MaterialPlanningItem.objects.filter(item_name='New Item B').exists())
        self.assertEqual(MaterialPlanningItem.objects.count(), 2) # Original + New

    def test_create_view_post_invalid(self):
        """Test POST request with invalid data for MaterialPlanningItem creation."""
        data = {
            'item_name': '', # Invalid, required
            'quantity': -5,  # Invalid, must be positive
            'status': 'InvalidStatus', # Invalid choice
            'required_by_date': '2023-12-31'
        }
        response = self.client.post(reverse('material_planning_item_add'), data, HTTP_HX_REQUEST='true')
        
        # Expect 200 OK because HTMX receives the form back with errors
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/materialplanningitem/_materialplanningitem_form.html')
        self.assertIn('form', response.context)
        self.assertFalse(response.context['form'].is_valid())
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Quantity must be a positive number.')
        # Check that no new object was created
        self.assertEqual(MaterialPlanningItem.objects.count(), 1)

    def test_update_view_get(self):
        """Test GET request to the MaterialPlanningItem update view (for modal)."""
        response = self.client.get(reverse('material_planning_item_edit', args=[self.item.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/materialplanningitem/_materialplanningitem_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance.pk, self.item.pk)
        self.assertContains(response, 'Edit Material Planning Item')

    def test_update_view_post_success(self):
        """Test POST request to update a MaterialPlanningItem successfully."""
        new_name = 'Updated Widget A'
        data = {
            'item_name': new_name,
            'quantity': self.item.quantity,
            'status': 'Approved',
            'required_by_date': self.item.required_by_date
        }
        response = self.client.post(reverse('material_planning_item_edit', args=[self.item.pk]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialPlanningItemList')
        
        self.item.refresh_from_db()
        self.assertEqual(self.item.item_name, new_name)
        self.assertEqual(self.item.status, 'Approved')

    def test_delete_view_get(self):
        """Test GET request to the MaterialPlanningItem delete confirmation view (for modal)."""
        response = self.client.get(reverse('material_planning_item_delete', args=[self.item.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/materialplanningitem/_materialplanningitem_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'].pk, self.item.pk)
        self.assertContains(response, f'Are you sure you want to delete the item "{self.item.item_name}"?')

    def test_delete_view_post_success(self):
        """Test DELETE request to delete a MaterialPlanningItem successfully."""
        initial_count = MaterialPlanningItem.objects.count()
        response = self.client.delete(reverse('material_planning_item_delete', args=[self.item.pk]), HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialPlanningItemList')
        
        self.assertEqual(MaterialPlanningItem.objects.count(), initial_count - 1)
        self.assertFalse(MaterialPlanningItem.objects.filter(pk=self.item.pk).exists())

```

---

## Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for Dynamic Updates:**
    *   The "Add New Item" button uses `hx-get` to fetch the form and `hx-target="#modalContent"` to load it directly into the modal's content area. `hx-trigger="click"` initiates this on button click.
    *   Similarly, "Edit" and "Delete" buttons on each row use `hx-get` to fetch their respective forms/confirmations into the modal.
    *   The `material-planning-item-table-container` div uses `hx-trigger="load, refreshMaterialPlanningItemList from:body"` to automatically load the table on page load and refresh it whenever the custom `refreshMaterialPlanningItemList` event is triggered. This event is sent by HTMX from the server after a successful form submission (create, update, delete) via the `HX-Trigger` header (`return HttpResponse(status=204, headers={'HX-Trigger': 'refreshMaterialPlanningItemList'})`).
    *   Forms (in `_materialplanningitem_form.html` and `_materialplanningitem_confirm_delete.html`) use `hx-post` or `hx-delete` to submit data asynchronously without a full page reload. `hx-swap="none"` is used on submission to prevent HTMX from changing the content of the modal, as the server responds with 204 No Content and the `HX-Trigger` header instead.
    *   `hx-indicator` is used to show a loading spinner during HTMX requests.

*   **Alpine.js for UI State Management (Modals):**
    *   The main modal div (`#modal`) uses Alpine.js (`x-data`, `x-show`, `x-transition`) to manage its visibility and provide smooth transitions.
    *   A simple `on click add .is-active to #modal` (Hyperscript) is used on the "Add New Item" button and "Edit/Delete" buttons to make the modal visible. This also handles the `hidden` class toggle.
    *   An `on click if event.target.id == 'modal' remove .is-active from me` (Hyperscript) is added to the modal overlay to allow closing the modal by clicking outside the content.
    *   JavaScript listeners are added (`document.body.addEventListener('htmx:afterOnLoad', ...)`, `document.body.addEventListener('refreshMaterialPlanningItemList', ...)`) to close the modal programmatically after successful form submissions, ensuring the modal is hidden when the list refreshes.

*   **DataTables for List Views:**
    *   The `_materialplanningitem_table.html` partial includes the `<table id="materialPlanningItemTable">` element.
    *   A JavaScript block within this partial ensures that `$(document).ready(function() { $('#materialPlanningItemTable').DataTable({...}); });` is called *every time* this partial is loaded by HTMX. This correctly initializes DataTables for the dynamically loaded table, including options for pagination, search, and sorting. The `destroy()` method is called first to prevent re-initialization issues if the table was previously loaded.

By implementing these, the user experience becomes highly interactive, resembling a Single Page Application (SPA) without the complexity of a full JavaScript framework. All interactions work without full page reloads, providing a fast and modern feel.

---

## Final Notes

*   This modernization plan effectively replaces the minimal ASP.NET dashboard with a robust Django-based application using modern web development practices.
*   Placeholders like `[APP_NAME]` have been replaced with `material_planning` derived from the original ASP.NET namespace. Similarly, `[MODEL_NAME]` is `MaterialPlanningItem`.
*   The business logic (`is_urgent`, `update_status`) resides in the `MaterialPlanningItem` model, adhering to the "Fat Model, Thin View" principle.
*   Templates are kept DRY by using partials (`_*.html`) for reusable components (forms, delete confirmations).
*   Comprehensive tests ensure the reliability and correctness of both model logic and view interactions, achieving high test coverage.
*   The entire setup emphasizes AI-assisted automation, as the structure is highly predictable and repetitive, making it suitable for automated code generation tools to convert similar ASP.NET patterns to these Django equivalents. This significantly reduces manual effort and potential errors in large-scale migrations.