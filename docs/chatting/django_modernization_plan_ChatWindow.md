## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Based on the ASP.NET code, we infer the following database tables and their columns, which are crucial for storing chat messages and user information.

*   **`Messages` Table:** This table stores the actual chat messages exchanged between users.
    *   **Columns:**
        *   `UserID`: Integer, representing the sender's ID.
        *   `ToUserID`: Integer, representing the recipient's ID.
        *   `TimeStamp`: DateTime, recording when the message was sent.
        *   `Text`: String, the content of the message.
*   **`PrivateMessages` Table:** This table seems to track the initiation of a private chat session between two users, preventing redundant invitations.
    *   **Columns:**
        *   `UserID`: Integer, representing the initiating user's ID.
        *   `ToUserID`: Integer, representing the invited user's ID.
*   **`Users` Table (inferred):** The code `message.User.EmployeeName` implies the existence of a `Users` table (or similar, like `Employees`) that stores user details.
    *   **Columns (inferred):**
        *   `UserID` (or `Id`): Primary Key, integer.
        *   `EmployeeName`: String, the display name for the user.

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

The ASP.NET code primarily focuses on the "Create" and "Read" aspects of chat messages, with a simple mechanism for initiating private chat sessions.

*   **Create (New Messages):**
    *   When the "Send" button (`btnSend`) is clicked, the `BtnSend_Click` method is triggered.
    *   This method calls `InsertPrivateMessage()` (if a private chat hasn't been initiated between these users already) and `InsertMessage()`.
    *   `InsertMessage()` takes the text from `txtMessage`, the sender's `UserID`, and the recipient's `ToUserID`, and stores it in the `Messages` table.
*   **Read (Displaying Messages):**
    *   The `GetPrivateMessages()` method is responsible for retrieving chat history.
    *   It queries the `Messages` table for all messages exchanged between the two specified users (sender and receiver, in either direction).
    *   It fetches the last 20 messages, orders them by timestamp, and then formats them into HTML using a `StringBuilder` for display in `litMessages`.
    *   This method is called on `Page_Load` (initial display), after sending a message (`BtnSend_Click`), and periodically by `Timer1_OnTick` (for automatic refresh).
*   **Update:** No explicit update operations for existing messages are present.
*   **Delete:** No delete operations are present.
*   **Validation Logic:**
    *   Messages are only inserted if `txtMessage.Text.Length > 0`.
    *   The message text is sanitized by removing `<` characters (`txtMessage.Text.Replace("<", "")`) before insertion. This basic sanitization will be handled by Django's templating system or more robust sanitization libraries.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

The ASP.NET UI components are mapped to their Django/HTMX/Alpine.js equivalents for a modern, dynamic user experience without full page reloads.

*   **Chat Message Display Area (`divMessages`, `litMessages`):**
    *   In Django, this will be a simple `div` element.
    *   HTMX will be used to dynamically load and refresh the chat messages within this `div` using `hx-get` (for periodic updates via `hx-trigger="every 7s"`) and `hx-swap="innerHTML"`.
    *   The styling for alternating message backgrounds will be handled directly in the Django template.
    *   The `SetScrollPosition` JavaScript function will be re-implemented using an HTMX `hx-on::after-swap` attribute or an Alpine.js `x-effect` to ensure the chat window scrolls to the bottom after new messages are loaded.
*   **Message Input Box (`txtMessage`):**
    *   This will be a standard HTML `<textarea>` element within a Django form.
    *   `SetCursorToTextEnd` and `FocusMe` (related to input focus) will be managed using Alpine.js directives (`x-ref`, `@click`).
    *   `ReplaceChars` (client-side sanitization) is largely redundant as Django's forms and templates provide automatic escaping, and server-side validation/sanitization is paramount. Any specific sanitization will be handled in the model.
*   **Send Button (`btnSend`):**
    *   This will be a standard HTML `<button>` element.
    *   HTMX will be used to submit the message form asynchronously using `hx-post` without a full page refresh.
    *   After submission, HTMX will trigger a refresh of the message display area.
*   **AJAX Refresh (`ScriptManager1`, `UpdatePanel1`, `Timer1`):**
    *   This entire mechanism will be replaced by HTMX's `hx-trigger="every 7s"` on the `div` containing the chat messages. This allows for periodic updates without the overhead of ASP.NET's `UpdatePanel`.
*   **Hidden Labels (`lblFromUserId`, `lblToUserId`, `lblFromUsername`, `lblMessageSent`):**
    *   These will be replaced by passing user IDs and usernames directly in the URL patterns to the Django views, or by retrieving them from the session/request context within the view. The `lblMessageSent` flag (to prevent duplicate `PrivateMessage` entries) will be handled by logic within the model's `ensure_initiated` method.

## Step 4: Generate Django Code

### 4.1 Models (`chat/models.py`)

This section defines the Django models that map to your existing database tables. We'll use `managed = False` to ensure Django works with your pre-existing schema.

```python
from django.db import models
from django.utils import timezone
from django.db.models import Q # For complex queries

class ChatUser(models.Model):
    """
    Maps to the 'Users' or 'Employees' table containing user details.
    Assumes a simple mapping for EmployeeName.
    """
    # Assuming 'Id' is the primary key column in your existing Users table
    # Adjust db_column if your actual PK column name is different
    id = models.IntegerField(db_column='Id', primary_key=True)
    employee_name = models.CharField(
        db_column='EmployeeName', 
        max_length=255, 
        verbose_name="Employee Name"
    )

    class Meta:
        managed = False # Do not manage this table's schema in Django
        db_table = 'Users' # Replace with your actual Users table name
        verbose_name = 'Chat User'
        verbose_name_plural = 'Chat Users'

    def __str__(self):
        return self.employee_name

class ChatMessageManager(models.Manager):
    """Custom manager for ChatMessage model to encapsulate business logic."""

    def get_conversation(self, user1_id: int, user2_id: int, limit: int = 20):
        """
        Retrieves the latest messages between two users, ordered by timestamp.
        Equivalent to the ASP.NET GetPrivateMessages logic.
        """
        messages = self.filter(
            Q(sender_user_id=user1_id, receiver_user_id=user2_id) |
            Q(sender_user_id=user2_id, receiver_user_id=user1_id)
        ).order_by('-timestamp')[:limit] # Take the latest 'limit' messages
        return messages.order_by('timestamp') # Re-order for chronological display

    def send_message(self, sender_user: ChatUser, receiver_user: ChatUser, message_text: str):
        """
        Creates and saves a new chat message.
        Equivalent to the ASP.NET InsertMessage logic.
        """
        # Basic sanitization, Django's template engine will escape HTML by default
        # For more robust sanitization, consider using a library like bleach
        cleaned_text = message_text # .replace("<", "") is basic, Django handles this
        
        message = self.create(
            sender_user=sender_user,
            receiver_user=receiver_user,
            timestamp=timezone.now(),
            text=cleaned_text
        )
        # Ensure private chat initiation is recorded if it hasn't been already
        PrivateChatInitiation.objects.ensure_initiated(sender_user, receiver_user)
        return message

class ChatMessage(models.Model):
    """
    Maps to the 'Messages' table storing chat messages.
    """
    sender_user = models.ForeignKey(
        ChatUser, 
        on_delete=models.DO_NOTHING, # Or models.CASCADE if appropriate for your DB
        db_column='UserID', 
        related_name='sent_messages', 
        verbose_name="From User"
    )
    receiver_user = models.ForeignKey(
        ChatUser, 
        on_delete=models.DO_NOTHING, # Or models.CASCADE
        db_column='ToUserID', 
        related_name='received_messages', 
        verbose_name="To User"
    )
    timestamp = models.DateTimeField(db_column='TimeStamp', verbose_name="Time Stamp")
    text = models.TextField(db_column='Text', verbose_name="Message Text")

    objects = ChatMessageManager() # Attach the custom manager

    class Meta:
        managed = False # Do not manage this table's schema in Django
        db_table = 'Messages' # Replace with your actual Messages table name
        verbose_name = 'Chat Message'
        verbose_name_plural = 'Chat Messages'
        # Define a unique constraint if needed, for example unique message IDs

    def __str__(self):
        return f"Message from {self.sender_user.employee_name} to {self.receiver_user.employee_name} at {self.timestamp.strftime('%Y-%m-%d %H:%M')}"

class PrivateChatInitiationManager(models.Manager):
    """Custom manager for PrivateChatInitiation model."""

    def ensure_initiated(self, user1: ChatUser, user2: ChatUser):
        """
        Ensures a private chat initiation record exists between two users.
        Equivalent to the ASP.NET InsertPrivateMessage logic.
        It inserts only if a record doesn't exist for the pair in either direction.
        """
        # Check if an initiation record already exists in either direction
        exists = self.filter(
            (Q(sender_user=user1, receiver_user=user2) |
             Q(sender_user=user2, receiver_user=user1))
        ).exists()

        if not exists:
            self.create(sender_user=user1, receiver_user=user2, initiated_at=timezone.now())

class PrivateChatInitiation(models.Model):
    """
    Maps to the 'PrivateMessages' table for tracking private chat initiations.
    """
    sender_user = models.ForeignKey(
        ChatUser, 
        on_delete=models.DO_NOTHING, 
        db_column='UserID', 
        related_name='initiated_private_chats',
        verbose_name="Initiating User"
    )
    receiver_user = models.ForeignKey(
        ChatUser, 
        on_delete=models.DO_NOTHING, 
        db_column='ToUserID', 
        related_name='received_private_chats',
        verbose_name="Recipient User"
    )
    initiated_at = models.DateTimeField(
        db_column='InitiatedAt', # Assuming a column exists or can be added
        default=timezone.now, 
        verbose_name="Initiated At"
    ) # Added for more robust tracking, inferred from logic

    objects = PrivateChatInitiationManager() # Attach custom manager

    class Meta:
        managed = False # Do not manage this table's schema in Django
        db_table = 'PrivateMessages' # Replace with your actual PrivateMessages table name
        verbose_name = 'Private Chat Initiation'
        verbose_name_plural = 'Private Chat Initiations'
        # Add a unique_together constraint if a pair (user1, user2) should only exist once
        unique_together = (('sender_user', 'receiver_user'),) # For cases where direct pairing matters

    def __str__(self):
        return f"Private chat initiated between {self.sender_user.employee_name} and {self.receiver_user.employee_name}"

```

### 4.2 Forms (`chat/forms.py`)

This form will be used for submitting new chat messages.

```python
from django import forms
from .models import ChatMessage

class ChatMessageForm(forms.ModelForm):
    """
    Form for sending a new chat message.
    The sender and receiver are set in the view, not by the user directly.
    """
    class Meta:
        model = ChatMessage
        fields = ['text'] # Only text field is directly user-editable
        widgets = {
            'text': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'rows': 3,
                'placeholder': 'Type your message here...',
                'x-ref': 'messageInput', # For Alpine.js to focus/scroll
            }),
        }
        labels = {
            'text': '' # No label needed for the textarea in this context
        }
        
    def clean_text(self):
        """
        Ensures message text is not empty and performs basic cleaning.
        Matches ASP.NET's txtMessage.Text.Length > 0 check.
        """
        text = self.cleaned_data.get('text')
        if not text or not text.strip():
            raise forms.ValidationError("Message cannot be empty.")
        return text.strip()

```

### 4.3 Views (`chat/views.py`)

We'll use a single class-based view for the main chat window and another for the HTMX-loaded message list.

```python
from django.views.generic import TemplateView, View
from django.http import JsonResponse, HttpResponse
from django.shortcuts import get_object_or_404
from django.template.loader import render_to_string
from django.contrib import messages
from .models import ChatMessage, ChatUser
from .forms import ChatMessageForm

class ChatWindowView(TemplateView):
    """
    Displays the main private chat window.
    Equivalent to the ChatWindow.aspx page load.
    Handles both initial display and form submission for new messages.
    """
    template_name = 'chat/chat_window.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        from_user_id = self.kwargs['from_user_id']
        to_user_id = self.kwargs['to_user_id']

        context['from_user'] = get_object_or_404(ChatUser, pk=from_user_id)
        context['to_user'] = get_object_or_404(ChatUser, pk=to_user_id)
        context['form'] = ChatMessageForm()
        
        # Initial load of messages, also used for HTMX partial
        context['messages'] = ChatMessage.objects.get_conversation(from_user_id, to_user_id)
        return context

    def post(self, request, *args, **kwargs):
        """
        Handles sending a new message via HTMX POST request.
        Equivalent to BtnSend_Click.
        """
        from_user_id = self.kwargs['from_user_id']
        to_user_id = self.kwargs['to_user_id']
        
        from_user = get_object_or_404(ChatUser, pk=from_user_id)
        to_user = get_object_or_404(ChatUser, pk=to_user_id)

        form = ChatMessageForm(request.POST)
        if form.is_valid():
            message_text = form.cleaned_data['text']
            ChatMessage.objects.send_message(from_user, to_user, message_text)
            
            # Clear the input field after successful submission
            # This is done via HTMX's hx-on::after-request attribute on the form
            
            return HttpResponse(status=204, headers={'HX-Trigger': 'messageSent'})
        else:
            # If form is invalid, return the form with errors
            # HTMX will swap this into the appropriate target (e.g., a modal or the form itself)
            context = {
                'form': form,
                'from_user': from_user,
                'to_user': to_user
            }
            html = render_to_string('chat/_message_form.html', context, request=request)
            return HttpResponse(html, status=400) # Bad Request for invalid form


class MessageListView(View):
    """
    View to render only the chat message list for HTMX updates.
    Equivalent to the periodic refresh part of GetPrivateMessages in Timer1_OnTick.
    """
    def get(self, request, *args, **kwargs):
        from_user_id = self.kwargs['from_user_id']
        to_user_id = self.kwargs['to_user_id']
        
        messages_list = ChatMessage.objects.get_conversation(from_user_id, to_user_id)
        
        context = {
            'messages': messages_list,
            'from_user_id': from_user_id,
            'to_user_id': to_user_id,
        }
        
        # Render just the partial template for the message list
        html = render_to_string('chat/_message_list.html', context, request=request)
        return HttpResponse(html)

```

### 4.4 Templates (`chat/templates/chat/`)

We'll create the main chat window template and a partial for the message list. Note: DataTables is generally for tabular, searchable data. For a real-time chat feed with auto-scrolling, it's not ideal. Thus, I am **not** applying DataTables to the chat message stream itself, as it would disrupt the user experience of a continuous chat. The request for DataTables applies to general "list views," and a chat feed is a specialized type of data presentation that benefits from direct HTML scrolling and HTMX append/refresh.

**`chat_window.html` (Main Chat Window)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8 max-w-lg bg-gray-100 rounded-lg shadow-xl"
     x-data="{ 
        fromUserId: {{ from_user.id }}, 
        toUserId: {{ to_user.id }},
        init() {
            // Function to scroll to the bottom of the chat div
            this.scrollToBottom = () => {
                const div = this.$refs.messageContainer;
                if (div) {
                    div.scrollTop = div.scrollHeight;
                }
            };
            // Initial scroll on load
            this.$nextTick(this.scrollToBottom);

            // Listen for HTMX afterSwap on message list to scroll
            this.$refs.messageContainer.addEventListener('htmx:afterSwap', this.scrollToBottom);

            // Focus on message input when window is active (simulating FocusMe)
            window.addEventListener('focus', () => {
                this.$refs.messageInput.focus();
                this.setCursorToEnd(this.$refs.messageInput);
            });
        },
        setCursorToEnd(el) {
            // Simulates SetCursorToTextEnd for textarea
            if (el && el.value.length > 0) {
                el.setSelectionRange(el.value.length, el.value.length);
            }
        },
        clearMessageInput() {
            this.$refs.messageInput.value = '';
            this.$refs.messageInput.focus();
        }
     }">
    <h2 class="text-xl font-bold text-gray-800 mb-4 text-center">Private Chat with {{ to_user.employee_name }}</h2>
    
    <div id="message-container" x-ref="messageContainer"
         class="bg-white border border-gray-300 rounded-md h-64 overflow-y-scroll p-4 mb-4 text-sm scroll-smooth"
         hx-get="{% url 'chat:message_list' from_user.id to_user.id %}"
         hx-trigger="load, every 7s, messageSent from:body"
         hx-swap="innerHTML">
        <!-- Messages will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading messages...</p>
        </div>
    </div>
    
    <form hx-post="{% url 'chat:send_message' from_user.id to_user.id %}" 
          hx-target="#message-container" 
          hx-swap="none" {# We use hx-trigger on message-container for refresh #}
          hx-on::after-request="clearMessageInput()" {# Clear input after send #}
          class="flex items-center space-x-2">
        {% csrf_token %}
        {{ form.text }} {# Renders the textarea with x-ref="messageInput" #}
        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md flex-shrink-0">
            Send
        </button>
    </form>
    
    {% if form.errors %}
    <div class="mt-4 text-red-600 text-sm">
        {% for field in form %}
            {% if field.errors %}<p>{{ field.errors }}</p>{% endif %}
        {% endfor %}
        {% if form.non_field_errors %}<p>{{ form.non_field_errors }}</p>{% endif %}
    </div>
    {% endif %}

</div>
{% endblock %}

{% block extra_js %}
<script>
    // No custom JavaScript needed, Alpine.js handles it
</script>
{% endblock %}

```

**`_message_list.html` (Partial for HTMX Loading)**

```html
{% for message in messages %}
<div class="p-2 {% if forloop.counter0 is even %}bg-gray-50{% else %}bg-gray-100{% endif %} rounded-md mb-1 break-words">
    <span class="font-semibold text-gray-900">{{ message.sender_user.employee_name }}:</span> 
    <span class="text-gray-700">{{ message.text }}</span>
    <span class="text-xs text-gray-500 float-right">{{ message.timestamp|date:"H:i" }}</span>
</div>
{% empty %}
<p class="text-center text-gray-500 italic">No messages yet. Start the conversation!</p>
{% endfor %}

```

**`_message_form.html` (Partial for form in case of errors - used by HTMX in `ChatWindowView` post)**

```html
<div class="flex items-center space-x-2">
    {{ form.text }}
    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md flex-shrink-0">
        Send
    </button>
</div>
{% if form.errors %}
<div class="mt-2 text-red-600 text-sm">
    {% for field in form %}
        {% if field.errors %}<p>{{ field.errors }}</p>{% endif %}
    {% endfor %}
    {% if form.non_field_errors %}<p>{{ form.non_field_errors }}</p>{% endif %}
</div>
{% endif %}
```

### 4.5 URLs (`chat/urls.py`)

This defines the URL paths for your chat application.

```python
from django.urls import path
from .views import ChatWindowView, MessageListView

app_name = 'chat' # Define app_name for namespacing

urlpatterns = [
    # Main chat window for specific users (from_user_id and to_user_id)
    path('<int:from_user_id>/<int:to_user_id>/', ChatWindowView.as_view(), name='chat_window'),
    
    # HTMX endpoint to fetch and refresh the message list
    path('<int:from_user_id>/<int:to_user_id>/messages/', MessageListView.as_view(), name='message_list'),
    
    # HTMX endpoint for sending a new message
    path('<int:from_user_id>/<int:to_user_id>/send/', ChatWindowView.as_view(), name='send_message'),
]

```

### 4.6 Tests (`chat/tests.py`)

Comprehensive tests for models and views to ensure functionality and coverage.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from unittest.mock import patch # For mocking timezone.now()

from .models import ChatUser, ChatMessage, PrivateChatInitiation

class ChatUserModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a mock ChatUser as the actual table is 'managed=False'
        # For actual database tests, you'd need a real database populated.
        # Here we mock the behavior of `ChatUser`
        # We need to simulate the existing data being present for FKs
        
        # We will create instances manually for testing in-memory logic
        # For 'managed=False' models, Django's ORM operations might not
        # directly reflect database state without a real connection.
        # For unit tests, we can create dummy instances for FK relationships.
        cls.user1 = ChatUser(id=1, employee_name='Alice')
        cls.user2 = ChatUser(id=2, employee_name='Bob')
        cls.user3 = ChatUser(id=3, employee_name='Charlie')

    def test_chat_user_creation(self):
        self.assertEqual(self.user1.employee_name, 'Alice')
        self.assertEqual(self.user1.id, 1)

    def test_str_representation(self):
        self.assertEqual(str(self.user2), 'Bob')


class ChatMessageModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.user1 = ChatUser(id=1, employee_name='Alice')
        cls.user2 = ChatUser(id=2, employee_name='Bob')
        cls.user3 = ChatUser(id=3, employee_name='Charlie')
        
        # Mocking objects.create for managed=False models
        with patch('chat.models.ChatMessage.objects.create') as mock_create_chat, \
             patch('chat.models.PrivateChatInitiation.objects.ensure_initiated') as mock_ensure_private:
            mock_create_chat.side_effect = lambda **kwargs: ChatMessage(**kwargs)
            
            # Create some messages
            cls.msg1 = ChatMessage.objects.send_message(cls.user1, cls.user2, "Hello Bob!")
            cls.msg2 = ChatMessage.objects.send_message(cls.user2, cls.user1, "Hi Alice!")
            cls.msg3 = ChatMessage.objects.send_message(cls.user1, cls.user2, "How are you?")
            cls.msg4 = ChatMessage.objects.send_message(cls.user3, cls.user1, "Hey Alice from Charlie.")

        # Simulate get_conversation by manually filtering and ordering
        # For managed=False models, the ORM might not work directly without a DB.
        # We need a mock for the manager's methods or use in-memory objects and test filters manually.
        # For proper testing with `managed=False`, consider `django-model-mommy` or `factory-boy`
        # or setting up a test database connection that mimics the actual DB.
        
        # For this example, we'll manually mock the manager's behavior
        cls._all_messages = [cls.msg1, cls.msg2, cls.msg3, cls.msg4]
        # Set timestamps for ordering
        cls.msg1.timestamp = timezone.now() - timezone.timedelta(minutes=3)
        cls.msg2.timestamp = timezone.now() - timezone.timedelta(minutes=2)
        cls.msg3.timestamp = timezone.now() - timezone.timedelta(minutes=1)
        cls.msg4.timestamp = timezone.now() - timezone.timedelta(minutes=0.5)


    def test_send_message(self):
        with patch('chat.models.ChatMessage.objects.create') as mock_create_chat, \
             patch('chat.models.PrivateChatInitiation.objects.ensure_initiated') as mock_ensure_private:
            mock_create_chat.side_effect = lambda **kwargs: ChatMessage(**kwargs) # Mimic creation
            
            new_msg = ChatMessage.objects.send_message(self.user1, self.user3, "Test message.")
            self.assertEqual(new_msg.text, "Test message.")
            self.assertEqual(new_msg.sender_user, self.user1)
            self.assertEqual(new_msg.receiver_user, self.user3)
            mock_create_chat.assert_called_once()
            mock_ensure_private.assert_called_once_with(self.user1, self.user3)

    def test_get_conversation(self):
        with patch('chat.models.ChatMessageManager.filter') as mock_filter:
            # Mock the filter result to return our pre-defined messages
            mock_filter.return_value.order_by.return_value.__getitem__.return_value = \
                sorted([self.msg1, self.msg2, self.msg3], key=lambda m: m.timestamp)

            conversation = ChatMessage.objects.get_conversation(self.user1.id, self.user2.id)
            self.assertEqual(len(conversation), 3)
            self.assertEqual(conversation[0].text, "Hello Bob!")
            self.assertEqual(conversation[2].text, "How are you?")
            
            mock_filter.assert_called_once()


class PrivateChatInitiationModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.user1 = ChatUser(id=1, employee_name='Alice')
        cls.user2 = ChatUser(id=2, employee_name='Bob')

    def test_ensure_initiated(self):
        with patch('chat.models.PrivateChatInitiationManager.filter') as mock_filter, \
             patch('chat.models.PrivateChatInitiation.objects.create') as mock_create:
            # First call: no existing record, should create one
            mock_filter.return_value.exists.return_value = False
            PrivateChatInitiation.objects.ensure_initiated(self.user1, self.user2)
            mock_create.assert_called_once_with(sender_user=self.user1, receiver_user=self.user2, initiated_at=timezone.now())

            mock_create.reset_mock() # Reset mock for next test
            # Second call: record exists, should not create
            mock_filter.return_value.exists.return_value = True
            PrivateChatInitiation.objects.ensure_initiated(self.user1, self.user2)
            mock_create.assert_not_called()


class ChatViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Mock ChatUser and ChatMessage objects because they are managed=False
        # For actual integration tests, ensure your test database contains these users
        self.user1 = ChatUser(id=1, employee_name='Alice')
        self.user2 = ChatUser(id=2, employee_name='Bob')

        # Patch get_object_or_404 to return our mock users
        patcher_user1 = patch('chat.views.get_object_or_404', side_effect=lambda model, pk: self.user1 if pk == 1 else self.user2)
        patcher_user1.start()
        self.addCleanup(patcher_user1.stop)

        # Patch ChatMessage.objects.get_conversation
        patcher_get_conversation = patch('chat.models.ChatMessage.objects.get_conversation')
        selfer_get_conversation = patcher_get_conversation.start()
        selfer_get_conversation.return_value = [] # Default empty list
        self.addCleanup(patcher_get_conversation.stop)

        # Patch ChatMessage.objects.send_message
        patcher_send_message = patch('chat.models.ChatMessage.objects.send_message')
        selfer_send_message = patcher_send_message.start()
        selfer_send_message.return_value = ChatMessage(sender_user=self.user1, receiver_user=self.user2, text="Test", timestamp=timezone.now())
        self.addCleanup(patcher_send_message.stop)
        
    def test_chat_window_get(self):
        url = reverse('chat:chat_window', args=[self.user1.id, self.user2.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'chat/chat_window.html')
        self.assertIn('from_user', response.context)
        self.assertIn('to_user', response.context)
        self.assertIn('form', response.context)
        self.assertIn('messages', response.context)
        ChatMessage.objects.get_conversation.assert_called_once_with(self.user1.id, self.user2.id)

    def test_chat_window_post_valid_message(self):
        url = reverse('chat:send_message', args=[self.user1.id, self.user2.id])
        data = {'text': 'Hello, Bob!'}
        
        # Simulate HTMX request
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'messageSent')
        ChatMessage.objects.send_message.assert_called_once_with(self.user1, self.user2, 'Hello, Bob!')

    def test_chat_window_post_invalid_message(self):
        url = reverse('chat:send_message', args=[self.user1.id, self.user2.id])
        data = {'text': ''} # Empty message
        
        # Simulate HTMX request
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 400) # Bad Request for invalid form
        self.assertTemplateUsed(response, 'chat/_message_form.html')
        # Check if form errors are present in the rendered HTML
        self.assertIn(b'Message cannot be empty.', response.content)
        ChatMessage.objects.send_message.assert_not_called() # Should not call send_message

    def test_message_list_view_get(self):
        # Prepare mock messages for the list view
        mock_messages = [
            ChatMessage(sender_user=self.user1, receiver_user=self.user2, text="Msg1", timestamp=timezone.now()),
            ChatMessage(sender_user=self.user2, receiver_user=self.user1, text="Msg2", timestamp=timezone.now()),
        ]
        ChatMessage.objects.get_conversation.return_value = mock_messages

        url = reverse('chat:message_list', args=[self.user1.id, self.user2.id])
        response = self.client.get(url, HTTP_HX_REQUEST='true') # Simulate HTMX request
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'chat/_message_list.html')
        self.assertIn(b'Msg1', response.content)
        self.assertIn(b'Msg2', response.content)
        ChatMessage.objects.get_conversation.assert_called_once_with(self.user1.id, self.user2.id)

    def test_htmx_triggers_and_swaps(self):
        # This is more of an integration test for HTMX setup.
        # It verifies that HTMX attributes are correctly placed in the HTML.
        url = reverse('chat:chat_window', args=[self.user1.id, self.user2.id])
        response = self.client.get(url)
        
        # Check if message-container has HTMX attributes
        self.assertContains(response, 'id="message-container"')
        self.assertContains(response, f'hx-get="{reverse("chat:message_list", args=[self.user1.id, self.user2.id])}"')
        self.assertContains(response, 'hx-trigger="load, every 7s, messageSent from:body"')
        self.assertContains(response, 'hx-swap="innerHTML"')

        # Check if form has HTMX attributes
        self.assertContains(response, f'hx-post="{reverse("chat:send_message", args=[self.user1.id, self.user2.id])}"')
        self.assertContains(response, 'hx-target="#message-container"')
        self.assertContains(response, 'hx-swap="none"')
        self.assertContains(response, 'hx-on::after-request="clearMessageInput()"')

```

## Step 5: HTMX and Alpine.js Integration

The integration of HTMX and Alpine.js is baked directly into the templates and views generated above:

*   **HTMX for Dynamic Updates:**
    *   The `div` with `id="message-container"` in `chat_window.html` uses `hx-get` to periodically fetch updated messages from the `message_list` URL (`every 7s`) and on initial page `load`. It also listens for a `messageSent` event from the `body` (triggered after a message is successfully sent).
    *   The message `form` uses `hx-post` to submit messages asynchronously to the `send_message` URL. `hx-swap="none"` is used because the `message-container` itself will be refreshed by the `messageSent` trigger.
    *   `hx-on::after-request="clearMessageInput()"` on the form ensures that the message input box is cleared and re-focused after a successful send, providing a smooth user experience.
*   **Alpine.js for UI State Management and Interactivity:**
    *   The main container `div` in `chat_window.html` has `x-data` to manage `scrollToBottom` functionality.
    *   `x-init` hooks into the `htmx:afterSwap` event on the `message-container` to automatically scroll the chat window to the bottom whenever new messages are loaded.
    *   `x-ref="messageContainer"` and `x-ref="messageInput"` are used to directly access DOM elements for scrolling and focusing, similar to how the original ASP.NET `document.getElementById` worked.
    *   A `window.addEventListener('focus', ...)` in Alpine.js handles focusing the message input field when the browser window regains focus, mimicking the original `FocusThisWindow` logic.
    *   The `setCursorToEnd` Alpine.js function re-implements the original ASP.NET `SetCursorToTextEnd` for better user input experience.
*   **DataTables:** As noted previously, DataTables is *not* implemented for the chat message stream itself. This is because a real-time chat application benefits from continuous scrolling and appending, which is fundamentally at odds with DataTables' pagination and sorting paradigms. DataTables is best suited for structured, non-streaming tabular data. The current implementation relies on simple HTML `div` with `overflow-y:scroll` and HTMX for dynamic content loading, which aligns with the original ASP.NET chat window's behavior.

## Final Notes

*   **Placeholders:** All `[PLACEHOLDER]` values (like `Users` for `db_table`, `Id` for `db_column`) should be verified against your actual database schema before deployment.
*   **DRY Templates:** The use of `_message_list.html` and `_message_form.html` demonstrates the DRY principle by allowing HTMX to load only the necessary parts of the page, avoiding full page reloads and redundant rendering.
*   **Fat Model, Thin View:** All complex data retrieval (`get_conversation`) and creation (`send_message`, `ensure_initiated`) logic resides within the `ChatMessageManager` and `PrivateChatInitiationManager` custom model managers, keeping the `ChatWindowView` and `MessageListView` concise and focused on orchestrating requests and responses.
*   **Tests:** The provided tests cover model methods and view interactions, including HTMX-specific aspects, aiming for high code coverage. You would need to ensure your Django `settings.py` is configured to connect to your existing ASP.NET database for `managed=False` models to work correctly in a production environment or for integration tests. For unit tests, patching is used to simulate database interactions.
*   **Security:** The original ASP.NET code had `txtMessage.Text.Replace("<", "")`. In Django, input text rendered in templates is automatically escaped by default, mitigating XSS risks. For more advanced sanitization (e.g., allowing specific HTML tags but stripping others), consider a library like `bleach` in your model's `send_message` method.
*   **Real-time vs. Polling:** The current solution uses HTMX polling (`every 7s`), which directly replaces the ASP.NET `Timer` and `UpdatePanel`. For a truly real-time chat experience, a WebSocket solution (e.g., Django Channels) would be recommended, but it's a more significant architectural change than a direct HTMX migration.