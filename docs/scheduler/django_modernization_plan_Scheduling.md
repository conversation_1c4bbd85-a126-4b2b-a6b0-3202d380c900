The following comprehensive modernization plan outlines the transition of your ASP.NET Scheduler module to a modern Django application. This plan prioritizes AI-assisted automation, leveraging Django's robust framework alongside HTMX and Alpine.js for a performant, maintainable, and user-friendly solution. We will convert your legacy Telerik RadScheduler and `SqlDataSource` components into efficient Django models, forms, and views, while emphasizing the "fat model, thin view" paradigm and seamless dynamic interactions.

---

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

**Business Value Proposition:**

This modernization approach delivers significant business benefits by replacing a proprietary, legacy ASP.NET solution with a cutting-edge, open-source Django ecosystem.
-   **Reduced Costs:** Eliminate expensive Telerik licensing fees and dependency on specialized ASP.NET developers.
-   **Enhanced Performance:** HTMX and Alpine.js provide a highly responsive user experience with instant updates, minimizing full page reloads.
-   **Improved Maintainability & Scalability:** Django's structured, "fat model, thin view" architecture makes the application easier to understand, extend, and scale as your business grows.
-   **Future-Proofing:** Transition to a widely adopted, actively maintained open-source framework, reducing vendor lock-in and simplifying future development efforts.
-   **Developer Productivity:** Django's conventions and comprehensive tooling will accelerate development and make onboarding new team members more efficient.

---

## Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code uses `SqlDataSource` components to interact with two primary tables: `[Grouping_Events]` for the core scheduling data and `[Grouping_Rooms]` for managing associated resources (referred to as "Departments" in the UI).

**Extracted Schema:**

**Table 1: `Grouping_Events`**
-   **Table Name:** `Grouping_Events`
-   **Columns:**
    -   `ID` (Primary Key, Integer)
    -   `Subject` (String)
    -   `Description` (String)
    -   `Start` (DateTime)
    -   `End` (DateTime)
    -   `ModuleId` (Integer, Foreign Key to `Grouping_Rooms.ModuleId`)
    -   `RecurrenceRule` (String)
    -   `RecurrenceParentID` (Integer)
    -   `Reminder` (String)
    -   `CompId` (Integer) - Filtered by `SessionParameter`
    -   `FinYearId` (Integer) - Filtered by `SessionParameter`
    -   `SessionId` (String) - Filtered by `SessionParameter`

**Table 2: `Grouping_Rooms`**
-   **Table Name:** `Grouping_Rooms`
-   **Columns:**
    -   `ModuleId` (Primary Key, Integer)
    -   `Name` (String)

---

## Step 2: Identify Backend Functionality

**Task:** Determine the core operations and business logic within the ASP.NET code.

**Analysis:**
The `SqlDataSource` components explicitly define all standard CRUD (Create, Read, Update, Delete) operations for `Grouping_Events`. The C# code-behind handles dynamic UI updates and data filtering based on user selections in checkboxes and a dropdown, which influence how events are grouped or ordered. User-specific filtering (`CompId`, `FinYearId`, `SessionId`) is also a crucial aspect.

**Functionality Breakdown:**

-   **Event CRUD (for `Grouping_Events`):**
    -   **Create:** New events can be added to the database.
    -   **Read:** Events are retrieved, filtered based on `CompId`, `FinYearId`, and `SessionId`.
    -   **Update:** Existing events can be modified.
    -   **Delete:** Events can be removed.
-   **Resource (Department) Data (for `Grouping_Rooms`):**
    -   Read-only access to `Grouping_Rooms` is used to populate a list of departments for event association.
-   **Dynamic Filtering/Grouping Logic:**
    -   The `GroupByRoomCheckBox` controls whether events are organized by their assigned "Departments".
    -   The `GroupByDateCheckBox` further refines this by adding date-based grouping when department grouping is active.
    -   The `GroupingDirectionComboBox` implies a display preference (horizontal/vertical) for grouped data. In Django, this will translate to dynamic sorting or visual presentation changes.
    -   These UI changes trigger an update to the displayed events, simulating a partial page refresh.
-   **Session-based Context:** All data operations are implicitly scoped to the current user's session, using `CompId`, `FinYearId`, and `SessionId` for multi-tenancy or user-specific data isolation.

---

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, and map them to modern web components.

**Analysis:**
The ASP.NET page heavily relies on Telerik RadControls for its rich UI and AJAX capabilities. These proprietary controls will be replaced by standard HTML elements enhanced with HTMX for dynamic interactions, Alpine.js for simple client-side state management, and DataTables for advanced data presentation features.

**UI Component Mapping:**

-   **`telerik:RadAjaxManager`, `telerik:RadAjaxPanel`, `telerik:RadAjaxLoadingPanel`**: These are fully replaced by **HTMX**. HTMX will handle all partial page updates and provide loading indicators (`hx-indicator`) with minimal, declarative attributes directly in the HTML.
-   **`telerik:RadScheduler`**: This sophisticated calendar component will be replaced by:
    -   A **DataTables** powered HTML table to display the list of events, providing robust client-side searching, sorting, and pagination.
    -   Standard HTML forms (rendered as HTMX partials) for Add, Edit, and Delete operations.
    -   The complex visual calendar representation (drag-and-drop, rich recurrence UI) is typically handled by dedicated JavaScript calendar libraries (e.g., FullCalendar). However, given the strict rule against additional JavaScript beyond HTMX/Alpine.js, the focus will be on managing and filtering the *list* of events effectively via DataTables, not recreating the full interactive calendar UI.
-   **`asp:CheckBox` (`GroupByRoomCheckBox`, `GroupByDateCheckBox`)**: These will be standard HTML `<input type="checkbox">` elements. They will use HTMX attributes to trigger dynamic requests that filter the event list displayed in DataTables.
-   **`telerik:RadComboBox` (`GroupingDirectionComboBox`)**: This will be a standard HTML `<select>` element, also utilizing HTMX to dynamically adjust the event list's sorting or filtering.
-   **`asp:SqlDataSource`**: The underlying data access layer will be fully replaced by Django's ORM (Object-Relational Mapper) via defined models.
-   **`Css/styles.css`**: All styling will be migrated to **Tailwind CSS** classes, applied directly within the HTML templates for a utility-first approach.
-   **`Javascript/loadingNotifier.js`**: This client-side loading notification can be achieved using HTMX's `hx-indicator` or simple Alpine.js state management.

---

## Step 4: Generate Django Code

**Assumptions:**
-   Your Django project is named `AutoERP`.
-   A new Django application for this module will be named `scheduler`.
-   The session variables `compid`, `finyear`, and `username` (or a similar user identifier) are available in `request.session` or `request.user` after user authentication.
-   Your project's `core/base.html` template includes necessary CDN links for HTMX, Alpine.js, jQuery, and DataTables.

### 4.1 Models (`scheduler/models.py`)

This section defines the Django models that map directly to your existing database tables. By using `managed = False` and `db_table`, Django will integrate with your current schema without requiring migrations or modifications. Business logic for filtering and ordering is encapsulated within a custom model manager, adhering to the "fat model" principle.

```python
from django.db import models
from django.utils import timezone

class GroupingRoom(models.Model):
    """
    Represents departments or rooms that can be associated with events.
    Corresponds to the 'Grouping_Rooms' table.
    """
    # Assuming ModuleId is the primary key in the existing database
    module_id = models.AutoField(db_column='ModuleId', primary_key=True)
    name = models.CharField(db_column='Name', max_length=255)

    class Meta:
        managed = False  # Important: Django will not manage this table's schema
        db_table = 'Grouping_Rooms'
        verbose_name = 'Department'
        verbose_name_plural = 'Departments'

    def __str__(self):
        return self.name


class GroupingEventManager(models.Manager):
    """
    Custom manager for GroupingEvent to encapsulate business logic for filtering and ordering.
    """
    def for_current_context(self, comp_id, fin_year_id, session_id):
        """
        Filters events based on CompId, FinYearId, and SessionId,
        as derived from the ASP.NET SqlDataSource's session parameters.
        """
        # Ensure fin_year_id is treated as <= as in the original ASP.NET SELECT command
        return self.get_queryset().filter(
            comp_id=comp_id,
            fin_year_id__lte=fin_year_id,
            session_id=session_id
        )

    def apply_grouping_and_ordering(self, queryset, group_by_room_checked, group_by_date_checked, grouping_direction):
        """
        Applies grouping logic (which translates to specific ordering for display)
        similar to the ASP.NET GroupByExpression and GroupingDirection.
        """
        order_fields = []

        if group_by_room_checked:
            # When grouping by room (Departments), order by department name
            order_fields.append('module_id') # Order by FK value for database efficiency
            if grouping_direction == 'Horizontal': # Assuming this implies primary sort
                pass # Already ordered by module_id
            elif grouping_direction == 'Vertical': # Assuming this implies secondary sort
                order_fields.insert(0, 'module_id') # Make it secondary if date is primary, otherwise primary
        
        if group_by_date_checked and group_by_room_checked: # Only applies if Group by Room is checked
            # If grouping by date, add start date to the ordering
            order_fields.append('start__date')
        
        # Ensure a consistent final order, typically by start time
        if not order_fields:
            return queryset.order_by('start')
        
        final_order = []
        # Add distinct order fields to avoid duplicates
        for field in order_fields:
            if field not in final_order:
                final_order.append(field)
        
        # Always add 'start' as the last ordering to ensure consistent sub-sorting
        if 'start' not in final_order:
            final_order.append('start')
        
        return queryset.order_by(*final_order)


class GroupingEvent(models.Model):
    """
    Represents a scheduling event.
    Corresponds to the 'Grouping_Events' table.
    """
    # Using Django's default 'id' field is good practice, but if the existing DB
    # uses 'ID' as primary key and you want to explicitly map it, use this:
    id = models.AutoField(db_column='ID', primary_key=True)
    subject = models.CharField(db_column='Subject', max_length=255)
    description = models.TextField(db_column='Description', blank=True, null=True)
    start = models.DateTimeField(db_column='Start')
    end = models.DateTimeField(db_column='End')

    # Directly mapping the ModuleId from Grouping_Events as an IntegerField.
    # We will fetch the related GroupingRoom.name in the view or template helper.
    module_id = models.IntegerField(db_column='ModuleId', blank=True, null=True)

    recurrence_rule = models.CharField(db_column='RecurrenceRule', max_length=255, blank=True, null=True)
    recurrence_parent_id = models.IntegerField(db_column='RecurrenceParentID', blank=True, null=True)
    reminder = models.CharField(db_column='Reminder', max_length=255, blank=True, null=True)

    # Fields for session/tenant context
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    session_id = models.CharField(db_column='SessionId', max_length=255)

    objects = GroupingEventManager() # Attach our custom manager

    class Meta:
        managed = False  # Important: Django will not manage this table's schema
        db_table = 'Grouping_Events'
        verbose_name = 'Scheduling Event'
        verbose_name_plural = 'Scheduling Events'
        ordering = ['start'] # Default ordering if no specific grouping is applied

    def __str__(self):
        return self.subject

    def get_display_start_time(self):
        """Helper method to format start time for display."""
        return timezone.localtime(self.start).strftime('%Y-%m-%d %H:%M')

    def get_display_end_time(self):
        """Helper method to format end time for display."""
        return timezone.localtime(self.end).strftime('%Y-%m-%d %H:%M')

    def get_department_name(self):
        """Retrieves the associated department name using module_id."""
        if self.module_id is None:
            return "N/A"
        try:
            room = GroupingRoom.objects.get(module_id=self.module_id)
            return room.name
        except GroupingRoom.DoesNotExist:
            return "Unknown Department"

```

### 4.2 Forms (`scheduler/forms.py`)

This form enables the creation and modification of `GroupingEvent` records. It uses Django's `ModelForm` for automatic mapping and validation, augmented with custom widgets to apply Tailwind CSS styling and a `ChoiceField` for selecting departments from `GroupingRoom`.

```python
from django import forms
from .models import GroupingEvent, GroupingRoom

class GroupingEventForm(forms.ModelForm):
    """
    Form for creating and updating GroupingEvent instances.
    """
    # Override module_id to use a ChoiceField for selecting departments
    module_id = forms.ChoiceField(
        label='Department',
        choices=[], # Populated dynamically in __init__
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    class Meta:
        model = GroupingEvent
        fields = [
            'subject', 'description', 'start', 'end',
            'module_id', 'recurrence_rule', 'recurrence_parent_id', 'reminder'
            # Note: comp_id, fin_year_id, session_id will be set automatically in the view
        ]
        widgets = {
            'subject': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            # Use 'datetime-local' for HTML5 input type
            'start': forms.DateTimeInput(attrs={'type': 'datetime-local', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'end': forms.DateTimeInput(attrs={'type': 'datetime-local', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'recurrence_rule': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'recurrence_parent_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'reminder': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate module_id choices from GroupingRoom
        self.fields['module_id'].choices = [(room.module_id, room.name) for room in GroupingRoom.objects.all().order_by('name')]
        # If editing an existing event, ensure the current module_id is selected
        if self.instance.pk and self.instance.module_id:
            self.initial['module_id'] = self.instance.module_id

    def clean_end(self):
        start = self.cleaned_data.get('start')
        end = self.cleaned_data.get('end')
        if start and end and end < start:
            raise forms.ValidationError("End time cannot be before start time.")
        return end

```

### 4.3 Views (`scheduler/views.py`)

These thin Django Class-Based Views (CBVs) handle the core CRUD operations for `GroupingEvent`. The `get_queryset` method of the `ListView` incorporates the session-based filtering and the dynamic grouping logic from the ASP.NET code-behind, leveraging the custom model manager. All views are designed to work seamlessly with HTMX for partial updates.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import render

from .models import GroupingEvent, GroupingRoom
from .forms import GroupingEventForm

class GroupingEventListView(ListView):
    """
    Displays a list of scheduling events, incorporating session-based filtering.
    This view serves the initial full page load for the event list.
    """
    model = GroupingEvent
    template_name = 'scheduler/groupingevent/list.html'
    context_object_name = 'groupingevents'

    def get_queryset(self):
        """
        Filters events based on session parameters: CompId, FinYearId, SessionId.
        This initial queryset is for the full page render.
        """
        # These session parameters need to be available in request.session
        comp_id = self.request.session.get('compid', 1) # Default or error handling
        fin_year_id = self.request.session.get('finyear', 2024) # Default or error handling
        session_id = self.request.session.get('username', 'default_user') # Default or error handling

        queryset = GroupingEvent.objects.for_current_context(comp_id, fin_year_id, session_id)
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass rooms data for the department grouping/filtering options if needed for initial rendering
        context['rooms'] = GroupingRoom.objects.all().order_by('name')
        return context


class GroupingEventTablePartialView(TemplateView):
    """
    Renders only the table portion of the event list,
    designed to be called via HTMX for dynamic updates based on filters.
    """
    template_name = 'scheduler/groupingevent/_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get session parameters, similar to GroupingEventListView
        comp_id = self.request.session.get('compid', 1)
        fin_year_id = self.request.session.get('finyear', 2024)
        session_id = self.request.session.get('username', 'default_user')

        # Get filter parameters from GET request (HTMX will send these)
        group_by_room_checked = self.request.GET.get('group_by_room', 'false') == 'true'
        group_by_date_checked = self.request.GET.get('group_by_date', 'false') == 'true'
        grouping_direction = self.request.GET.get('grouping_direction', 'Horizontal')

        # Apply initial filtering
        queryset = GroupingEvent.objects.for_current_context(comp_id, fin_year_id, session_id)

        # Apply dynamic grouping/ordering logic
        grouping_events = GroupingEvent.objects.apply_grouping_and_ordering(
            queryset,
            group_by_room_checked,
            group_by_date_checked,
            grouping_direction
        )
        
        context['groupingevents'] = grouping_events
        return context


class GroupingEventCreateView(CreateView):
    """
    Handles creation of new scheduling events via an HTMX-powered modal form.
    """
    model = GroupingEvent
    form_class = GroupingEventForm
    template_name = 'scheduler/groupingevent/_form.html' # This will be loaded as a partial
    success_url = reverse_lazy('scheduler:event_list') # Not directly used for HTMX, but good practice

    def form_valid(self, form):
        # Set session-dependent fields before saving
        form.instance.comp_id = self.request.session.get('compid', 1)
        form.instance.fin_year_id = self.request.session.get('finyear', 2024)
        form.instance.session_id = self.request.session.get('username', 'default_user')

        response = super().form_valid(form)
        messages.success(self.request, 'Scheduling Event added successfully.')
        
        # HTMX-specific response: Trigger a refresh of the table and close the modal
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, indicates success to HTMX
                headers={
                    'HX-Trigger': '{"refreshGroupingEventList":true, "closeModal":true}'
                }
            )
        return response

    def form_invalid(self, form):
        # If form is invalid, re-render the form partial with errors for HTMX
        response = render(self.request, self.template_name, {'form': form})
        return response


class GroupingEventUpdateView(UpdateView):
    """
    Handles updates to existing scheduling events via an HTMX-powered modal form.
    """
    model = GroupingEvent
    form_class = GroupingEventForm
    template_name = 'scheduler/groupingevent/_form.html' # This will be loaded as a partial
    success_url = reverse_lazy('scheduler:event_list') # Not directly used for HTMX

    def form_valid(self, form):
        # Ensure session-dependent fields are maintained (or updated if logic demands)
        form.instance.comp_id = self.request.session.get('compid', form.instance.comp_id)
        form.instance.fin_year_id = self.request.session.get('finyear', form.instance.fin_year_id)
        form.instance.session_id = self.request.session.get('username', form.instance.session_id)

        response = super().form_valid(form)
        messages.success(self.request, 'Scheduling Event updated successfully.')

        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshGroupingEventList":true, "closeModal":true}'
                }
            )
        return response

    def form_invalid(self, form):
        response = render(self.request, self.template_name, {'form': form})
        return response


class GroupingEventDeleteView(DeleteView):
    """
    Handles deletion of scheduling events via an HTMX-powered confirmation modal.
    """
    model = GroupingEvent
    template_name = 'scheduler/groupingevent/_confirm_delete.html' # Loaded as a partial
    success_url = reverse_lazy('scheduler:event_list') # Not directly used for HTMX

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Scheduling Event deleted successfully.')
        
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshGroupingEventList":true, "closeModal":true}'
                }
            )
        return response

```

### 4.4 Templates

These templates use HTMX for dynamic interactions and Alpine.js for modal behavior, ensuring a highly interactive single-page-application feel without complex JavaScript. DataTables is integrated for efficient data presentation. Remember, these are *component-specific* and assume `core/base.html` exists and handles all CDN imports.

**`scheduler/groupingevent/list.html`**
This is the main page for listing events, including the grouping controls and the container for the dynamically loaded DataTables.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-6 space-y-4 md:space-y-0">
        <h2 class="text-2xl font-bold text-gray-800">Scheduling Events</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'scheduler:event_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Event
        </button>
    </div>

    <!-- Grouping and Filtering Controls -->
    <div class="bg-white p-6 rounded-lg shadow-md mb-6">
        <div class="flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-6">
            <label class="flex items-center text-gray-700">
                <input
                    type="checkbox"
                    id="GroupByRoomCheckBox"
                    name="group_by_room"
                    class="form-checkbox h-5 w-5 text-blue-600 rounded"
                    hx-get="{% url 'scheduler:event_table' %}"
                    hx-target="#groupingEventTable-container"
                    hx-trigger="change"
                    hx-swap="innerHTML"
                    hx-include="#GroupByRoomCheckBox, #GroupByDateCheckBox, #GroupingDirectionComboBox"
                    {% if request.GET.group_by_room == 'true' %}checked{% endif %}
                    _="on change toggle disabled on #GroupByDateCheckBox, #GroupingDirectionComboBox"
                >
                <span class="ml-2">Group by Departments</span>
            </label>

            <label class="flex items-center text-gray-700">
                <input
                    type="checkbox"
                    id="GroupByDateCheckBox"
                    name="group_by_date"
                    class="form-checkbox h-5 w-5 text-blue-600 rounded"
                    hx-get="{% url 'scheduler:event_table' %}"
                    hx-target="#groupingEventTable-container"
                    hx-trigger="change"
                    hx-swap="innerHTML"
                    hx-include="#GroupByRoomCheckBox, #GroupByDateCheckBox, #GroupingDirectionComboBox"
                    {% if request.GET.group_by_date == 'true' %}checked{% endif %}
                    {% if request.GET.group_by_room != 'true' %}disabled{% endif %}
                >
                <span class="ml-2">Group by date</span>
            </label>

            <div class="w-full md:w-auto">
                <label for="GroupingDirectionComboBox" class="sr-only">Grouping Direction</label>
                <select
                    id="GroupingDirectionComboBox"
                    name="grouping_direction"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    hx-get="{% url 'scheduler:event_table' %}"
                    hx-target="#groupingEventTable-container"
                    hx-trigger="change"
                    hx-swap="innerHTML"
                    hx-include="#GroupByRoomCheckBox, #GroupByDateCheckBox, #GroupingDirectionComboBox"
                    {% if request.GET.group_by_room != 'true' %}disabled{% endif %}
                >
                    <option value="Horizontal" {% if request.GET.grouping_direction == 'Horizontal' %}selected{% endif %}>Horizontal</option>
                    <option value="Vertical" {% if request.GET.grouping_direction == 'Vertical' %}selected{% endif %}>Vertical</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Container for the DataTables driven event list -->
    <div id="groupingEventTable-container"
         hx-trigger="load, refreshGroupingEventList from:body"
         hx-get="{% url 'scheduler:event_table' %}"
         hx-target="#groupingEventTable-container"
         hx-swap="innerHTML">
        <!-- Initial loading state -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading events...</p>
        </div>
    </div>

    <!-- Modal for forms -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me
            on HxTrigger from body(closeModal) remove .is-active from me"
         x-data="{ showModal: false }"
         x-show="showModal"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
         x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
         x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-auto"
             hx-target="this">
            <!-- Form content will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('schedulerPage', () => ({
            // If needed, specific page-level Alpine state
            // For example, managing modal state via Alpine too, though HTMX handles it here.
        }));

        // Event listener for closing modal via HTMX trigger
        document.body.addEventListener('closeModal', () => {
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.remove('is-active');
            }
        });

        // Initialize checkbox/select enabled state on load
        // This is primarily for visual consistency if HTMX doesn't handle initial state
        const groupByRoomCheckbox = document.getElementById('GroupByRoomCheckBox');
        const groupByDateCheckbox = document.getElementById('GroupByDateCheckBox');
        const groupingDirectionComboBox = document.getElementById('GroupingDirectionComboBox');

        if (groupByRoomCheckbox) {
            function updateDependentControls() {
                const isChecked = groupByRoomCheckbox.checked;
                if (groupByDateCheckbox) groupByDateCheckbox.disabled = !isChecked;
                if (groupingDirectionComboBox) groupingDirectionComboBox.disabled = !isChecked;
            }
            groupByRoomCheckbox.addEventListener('change', updateDependentControls);
            updateDependentControls(); // Initial call
        }
    });
</script>
{% endblock %}
```

**`scheduler/groupingevent/_table.html`**
This is a partial template loaded dynamically by HTMX. It contains the DataTables structure for displaying events.

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-md p-4">
    <table id="groupingEventTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Start</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">End</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for event in groupingevents %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ event.subject }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ event.get_department_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ event.get_display_start_time }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ event.get_display_end_time }}</td>
                <td class="py-3 px-4 text-sm text-gray-900 max-w-xs truncate">{{ event.description|default:"-" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 shadow-sm transition duration-300 ease-in-out"
                        hx-get="{% url 'scheduler:event_edit' event.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md shadow-sm transition duration-300 ease-in-out"
                        hx-get="{% url 'scheduler:event_delete' event.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-4 text-center text-gray-500">No scheduling events found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Ensure DataTables is initialized only after the table is loaded by HTMX
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#groupingEventTable')) {
            $('#groupingEventTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "pagingType": "full_numbers",
                "responsive": true
            });
        }
    });
</script>
```

**`scheduler/groupingevent/_form.html`**
This partial template renders the form for adding or editing an event within the modal.

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Scheduling Event</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-target="#modalContent">
        {% csrf_token %}

        <div class="grid grid-cols-1 gap-y-6 sm:grid-cols-2 sm:gap-x-8">
            {% for field in form %}
            <div class="{% if field.name == 'description' %}sm:col-span-2{% endif %}">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-600 text-sm mt-1">{{ field.errors|join:", " }}</p>
                {% endif %}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <div class="mt-8 flex justify-end space-x-4">
            <button
                type="button"
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="px-5 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-300 ease-in-out">
                Save Event
            </button>
        </div>
    </form>
</div>
```

**`scheduler/groupingevent/_confirm_delete.html`**
This partial template is used for the delete confirmation modal.

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the event: <span class="font-bold">"{{ object.subject }}"</span>?</p>

    <div class="flex justify-end space-x-4">
        <button
            type="button"
            class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-300 ease-in-out"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button
            hx-post="{% url 'scheduler:event_delete' object.pk %}"
            hx-swap="none"
            hx-target="#modalContent"
            type="button"
            class="px-5 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-300 ease-in-out">
            Delete
        </button>
    </div>
</div>
```

### 4.5 URLs (`scheduler/urls.py`)

This file defines the URL patterns for your Django `scheduler` application, linking URLs to the respective views. The use of a `name` attribute for each path allows for easy referencing in templates and other parts of your Django application.

```python
from django.urls import path
from .views import (
    GroupingEventListView,
    GroupingEventTablePartialView,
    GroupingEventCreateView,
    GroupingEventUpdateView,
    GroupingEventDeleteView,
)

app_name = 'scheduler' # Namespace for this app's URLs

urlpatterns = [
    path('events/', GroupingEventListView.as_view(), name='event_list'),
    path('events/table/', GroupingEventTablePartialView.as_view(), name='event_table'),
    path('events/add/', GroupingEventCreateView.as_view(), name='event_add'),
    path('events/edit/<int:pk>/', GroupingEventUpdateView.as_view(), name='event_edit'),
    path('events/delete/<int:pk>/', GroupingEventDeleteView.as_view(), name='event_delete'),
]

```

### 4.6 Tests (`scheduler/tests.py`)

Comprehensive unit tests for the `GroupingEvent` and `GroupingRoom` models and integration tests for all views ensure the functionality, data integrity, and HTMX interactions behave as expected. Aim for high test coverage (80%+).

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
import datetime

from .models import GroupingRoom, GroupingEvent

# --- Model Tests ---

class GroupingRoomModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a test room
        GroupingRoom.objects.create(module_id=1, name='Test Department A')
        GroupingRoom.objects.create(module_id=2, name='Test Department B')

    def test_room_creation(self):
        room = GroupingRoom.objects.get(module_id=1)
        self.assertEqual(room.name, 'Test Department A')

    def test_str_representation(self):
        room = GroupingRoom.objects.get(module_id=1)
        self.assertEqual(str(room), 'Test Department A')

    def test_verbose_name_plural(self):
        self.assertEqual(str(GroupingRoom._meta.verbose_name_plural), 'Departments')


class GroupingEventModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data including a department for foreign key resolution
        GroupingRoom.objects.create(module_id=100, name='Test Department Alpha')
        
        # Create a test event
        cls.test_event_data = {
            'id': 1,
            'subject': 'Team Meeting',
            'description': 'Discuss Q3 strategy',
            'start': timezone.make_aware(datetime.datetime(2024, 7, 10, 9, 0, 0)),
            'end': timezone.make_aware(datetime.datetime(2024, 7, 10, 10, 0, 0)),
            'module_id': 100, # Corresponds to Test Department Alpha
            'recurrence_rule': '',
            'recurrence_parent_id': None,
            'reminder': '',
            'comp_id': 1,
            'fin_year_id': 2024,
            'session_id': 'test_user_1',
        }
        GroupingEvent.objects.create(**cls.test_event_data)

        # Create another event for different session context
        cls.another_event_data = {
            'id': 2,
            'subject': 'Project Review',
            'description': 'Review milestones',
            'start': timezone.make_aware(datetime.datetime(2024, 8, 1, 14, 0, 0)),
            'end': timezone.make_aware(datetime.datetime(2024, 8, 1, 15, 0, 0)),
            'module_id': 100,
            'recurrence_rule': '',
            'recurrence_parent_id': None,
            'reminder': '',
            'comp_id': 2,
            'fin_year_id': 2024,
            'session_id': 'test_user_2',
        }
        GroupingEvent.objects.create(**cls.another_event_data)


    def test_event_creation(self):
        event = GroupingEvent.objects.get(id=1)
        self.assertEqual(event.subject, 'Team Meeting')
        self.assertEqual(event.module_id, 100)
        self.assertEqual(event.comp_id, 1)

    def test_str_representation(self):
        event = GroupingEvent.objects.get(id=1)
        self.assertEqual(str(event), 'Team Meeting')

    def test_get_department_name_exists(self):
        event = GroupingEvent.objects.get(id=1)
        self.assertEqual(event.get_department_name(), 'Test Department Alpha')

    def test_get_department_name_does_not_exist(self):
        # Create an event with a non-existent module_id
        event_no_dept = GroupingEvent.objects.create(
            id=3,
            subject='No Dept Event',
            start=timezone.now(),
            end=timezone.now() + datetime.timedelta(hours=1),
            module_id=999, # Non-existent
            comp_id=1, fin_year_id=2024, session_id='test_user_1'
        )
        self.assertEqual(event_no_dept.get_department_name(), 'Unknown Department')
        event_no_dept_null = GroupingEvent.objects.create(
            id=4,
            subject='Null Dept Event',
            start=timezone.now(),
            end=timezone.now() + datetime.timedelta(hours=1),
            module_id=None, # Null
            comp_id=1, fin_year_id=2024, session_id='test_user_1'
        )
        self.assertEqual(event_no_dept_null.get_department_name(), 'N/A')

    def test_for_current_context_manager(self):
        # Test filtering for test_user_1's context
        events = GroupingEvent.objects.for_current_context(1, 2024, 'test_user_1')
        self.assertEqual(events.count(), 1)
        self.assertEqual(events.first().subject, 'Team Meeting')

        # Test filtering for test_user_2's context
        events = GroupingEvent.objects.for_current_context(2, 2024, 'test_user_2')
        self.assertEqual(events.count(), 1)
        self.assertEqual(events.first().subject, 'Project Review')

        # Test filtering for non-existent context
        events = GroupingEvent.objects.for_current_context(99, 2024, 'non_existent')
        self.assertEqual(events.count(), 0)

    def test_apply_grouping_and_ordering_no_grouping(self):
        qs = GroupingEvent.objects.all()
        ordered_qs = GroupingEvent.objects.apply_grouping_and_ordering(qs, False, False, 'Horizontal')
        # Default ordering is by 'start'
        self.assertQuerysetEqual(
            ordered_qs,
            ['<GroupingEvent: Team Meeting>', '<GroupingEvent: Project Review>'],
            transform=lambda x: x,
            ordered=True
        ) # Assumes the datetime values of Team Meeting < Project Review

    def test_apply_grouping_and_ordering_group_by_room(self):
        GroupingRoom.objects.create(module_id=101, name='Test Department Beta')
        GroupingEvent.objects.create(
            id=3,
            subject='Another Meeting',
            start=timezone.make_aware(datetime.datetime(2024, 7, 11, 10, 0, 0)),
            end=timezone.make_aware(datetime.datetime(2024, 7, 11, 11, 0, 0)),
            module_id=101,
            comp_id=1, fin_year_id=2024, session_id='test_user_1'
        )
        qs = GroupingEvent.objects.all()
        ordered_qs = GroupingEvent.objects.apply_grouping_and_ordering(qs, True, False, 'Horizontal')
        
        # Should be ordered by module_id, then start
        # 100 (Alpha - Team Meeting, Project Review) then 101 (Beta - Another Meeting)
        self.assertEqual(ordered_qs[0].subject, 'Team Meeting')
        self.assertEqual(ordered_qs[1].subject, 'Project Review')
        self.assertEqual(ordered_qs[2].subject, 'Another Meeting')


# --- View Tests ---

class GroupingEventViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create common test data for all view tests
        GroupingRoom.objects.create(module_id=1, name='General Department')
        GroupingRoom.objects.create(module_id=2, name='IT Department')

        cls.event1 = GroupingEvent.objects.create(
            id=1, subject='Daily Scrum', description='Standup meeting',
            start=timezone.make_aware(datetime.datetime(2024, 7, 1, 9, 0, 0)),
            end=timezone.make_aware(datetime.datetime(2024, 7, 1, 9, 15, 0)),
            module_id=1, comp_id=1, fin_year_id=2024, session_id='test_user'
        )
        cls.event2 = GroupingEvent.objects.create(
            id=2, subject='Bug Fixing', description='Fix critical bugs',
            start=timezone.make_aware(datetime.datetime(2024, 7, 2, 10, 0, 0)),
            end=timezone.make_aware(datetime.datetime(2024, 7, 2, 12, 0, 0)),
            module_id=2, comp_id=1, fin_year_id=2024, session_id='test_user'
        )
        # Event for a different session
        GroupingEvent.objects.create(
            id=3, subject='Private Event', description='Personal',
            start=timezone.make_aware(datetime.datetime(2024, 7, 3, 11, 0, 0)),
            end=timezone.make_aware(datetime.datetime(2024, 7, 3, 12, 0, 0)),
            module_id=1, comp_id=1, fin_year_id=2024, session_id='other_user'
        )

    def setUp(self):
        self.client = Client()
        # Set session variables for the test user
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2024
        session['username'] = 'test_user'
        session.save()

    def test_event_list_view(self):
        response = self.client.get(reverse('scheduler:event_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'scheduler/groupingevent/list.html')
        self.assertIn('groupingevents', response.context)
        # Only events for 'test_user' should be in the initial context
        self.assertEqual(response.context['groupingevents'].count(), 2)
        self.assertContains(response, 'Daily Scrum')
        self.assertContains(response, 'Bug Fixing')
        self.assertNotContains(response, 'Private Event')

    def test_event_table_partial_view_no_grouping(self):
        response = self.client.get(reverse('scheduler:event_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'scheduler/groupingevent/_table.html')
        self.assertIn('groupingevents', response.context)
        self.assertEqual(response.context['groupingevents'].count(), 2)
        self.assertContains(response, 'Daily Scrum')
        self.assertContains(response, 'Bug Fixing')

    def test_event_table_partial_view_group_by_room(self):
        response = self.client.get(reverse('scheduler:event_table'), {'group_by_room': 'true'})
        self.assertEqual(response.status_code, 200)
        # Check if ordering is by department name (module_id)
        events = response.context['groupingevents']
        self.assertEqual(events[0].subject, 'Daily Scrum') # General Department (id=1)
        self.assertEqual(events[1].subject, 'Bug Fixing') # IT Department (id=2)

    def test_event_table_partial_view_group_by_room_and_date(self):
        # Create another event for General Department on a later date
        GroupingEvent.objects.create(
            id=4, subject='Follow up', description='Follow up on scrum',
            start=timezone.make_aware(datetime.datetime(2024, 7, 1, 14, 0, 0)), # Same day as event1
            end=timezone.make_aware(datetime.datetime(2024, 7, 1, 14, 30, 0)),
            module_id=1, comp_id=1, fin_year_id=2024, session_id='test_user'
        )
        response = self.client.get(reverse('scheduler:event_table'), {
            'group_by_room': 'true',
            'group_by_date': 'true'
        })
        self.assertEqual(response.status_code, 200)
        events = response.context['groupingevents']
        # Should be ordered by module_id, then by date, then by start time
        self.assertEqual(events[0].subject, 'Daily Scrum') # General, 7/1, 9:00
        self.assertEqual(events[1].subject, 'Follow up') # General, 7/1, 14:00
        self.assertEqual(events[2].subject, 'Bug Fixing') # IT, 7/2, 10:00


    def test_event_create_view_get(self):
        response = self.client.get(reverse('scheduler:event_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'scheduler/groupingevent/_form.html')
        self.assertIn('form', response.context)

    def test_event_create_view_post_valid(self):
        data = {
            'subject': 'New Event',
            'description': 'Test description',
            'start': timezone.now().strftime('%Y-%m-%dT%H:%M'),
            'end': (timezone.now() + datetime.timedelta(hours=1)).strftime('%Y-%m-%dT%H:%M'),
            'module_id': self.event1.module_id, # Use an existing module ID
            'recurrence_rule': '',
            'recurrence_parent_id': '',
            'reminder': '',
        }
        response = self.client.post(reverse('scheduler:event_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX expects 204 No Content
        self.assertIn('HX-Trigger', response.headers)
        self.assertTrue(GroupingEvent.objects.filter(subject='New Event').exists())

    def test_event_create_view_post_invalid(self):
        data = {
            'subject': '', # Invalid, required
            'start': timezone.now().strftime('%Y-%m-%dT%H:%M'),
            'end': (timezone.now() - datetime.timedelta(hours=1)).strftime('%Y-%m-%dT%H:%M'), # Invalid end < start
            'module_id': self.event1.module_id,
        }
        response = self.client.post(reverse('scheduler:event_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX re-renders form with errors
        self.assertTemplateUsed(response, 'scheduler/groupingevent/_form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)

    def test_event_update_view_get(self):
        response = self.client.get(reverse('scheduler:event_edit', args=[self.event1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'scheduler/groupingevent/_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.event1)

    def test_event_update_view_post_valid(self):
        updated_subject = 'Updated Scrum'
        data = {
            'subject': updated_subject,
            'description': self.event1.description,
            'start': self.event1.start.strftime('%Y-%m-%dT%H:%M'),
            'end': self.event1.end.strftime('%Y-%m-%dT%H:%M'),
            'module_id': self.event1.module_id,
            'recurrence_rule': self.event1.recurrence_rule,
            'recurrence_parent_id': self.event1.recurrence_parent_id or '',
            'reminder': self.event1.reminder,
        }
        response = self.client.post(reverse('scheduler:event_edit', args=[self.event1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.event1.refresh_from_db()
        self.assertEqual(self.event1.subject, updated_subject)

    def test_event_delete_view_get(self):
        response = self.client.get(reverse('scheduler:event_delete', args=[self.event1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'scheduler/groupingevent/_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.event1)

    def test_event_delete_view_post(self):
        event_count_before = GroupingEvent.objects.count()
        response = self.client.post(reverse('scheduler:event_delete', args=[self.event1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(GroupingEvent.objects.count(), event_count_before - 1)
        self.assertFalse(GroupingEvent.objects.filter(pk=self.event1.pk).exists())
        self.assertIn('HX-Trigger', response.headers)

```

---

## Step 5: HTMX and Alpine.js Integration

**Instructions:**

HTMX and Alpine.js are integral to providing a modern, dynamic user experience without heavy JavaScript frameworks.

-   **HTMX for Dynamic Interactions:**
    -   All form submissions (Add, Edit) use `hx-post` with `hx-swap="none"` and `hx-target="#modalContent"` to submit the form data. A `204 No Content` response with `HX-Trigger` headers is used to close the modal and refresh the event list.
    -   Delete operations use `hx-post` similarly, triggering a list refresh and modal close.
    -   The main event table is loaded and refreshed using `hx-get` on `groupingEventTable-container`, triggered on page `load` and by a custom `refreshGroupingEventList` event.
    -   The grouping checkboxes and dropdown directly trigger HTMX requests to the `event_table` endpoint using `hx-get` and `hx-trigger="change"`, including their current states via `hx-include`.
    -   Loading indicators (e.g., animated spinners) can be added using `hx-indicator` attributes on elements that trigger HTMX requests.

-   **Alpine.js for UI State Management:**
    -   Alpine.js is used to manage the visibility of the modal (`x-data`, `x-show`, `x-transition`).
    -   Custom event listeners (`on HxTrigger from body(closeModal) remove .is-active from me`) are used in Alpine.js to react to HTMX-triggered events, providing a unified way to manage UI elements like modals.
    -   Alpine.js handles the enablement/disablement of the "Group by date" checkbox and "Grouping Direction" dropdown based on the "Group by Departments" checkbox state, replicating the ASP.NET C# logic directly in the frontend.

-   **DataTables for List Views:**
    -   The `_table.html` partial contains a `<table>` element with the ID `groupingEventTable`.
    -   A `<script>` block within this partial ensures that `$('#groupingEventTable').DataTable()` is called *after* the table content is loaded by HTMX, ensuring DataTables is correctly initialized on dynamic content.
    -   DataTables provides built-in client-side searching, sorting, and pagination for the event list, enhancing usability.

-   **DRY Template Inheritance:**
    -   All templates extend `core/base.html` (e.g., `{% extends 'core/base.html' %}`). This base template is responsible for including all global CDN links (HTMX, Alpine.js, jQuery, DataTables, Tailwind CSS), reducing redundancy in individual page templates.
    -   Partial templates (`_table.html`, `_form.html`, `_confirm_delete.html`) are used with HTMX to avoid re-rendering entire pages for small updates, leading to a snappier experience.

---

## Final Notes

-   **Placeholder Replacement:** Ensure all placeholders like `[APP_NAME]`, `[MODEL_NAME]`, `[FIELD]` are correctly replaced with the actual values derived from your ASP.NET application during automated conversion.
-   **Session Variables:** The Django views assume `compid`, `finyear`, and `username` are available in `request.session`. If your authentication and session management differ, adapt the `request.session.get()` calls accordingly (e.g., from `request.user` attributes).
-   **CSS Integration:** Tailwind CSS classes are directly embedded in the HTML. Ensure Tailwind CSS is properly set up in your Django project.
-   **Recurrence Logic:** The `RecurrenceRule` and `RecurrenceParentID` fields are mapped, but the complex logic for generating recurring events from `RecurrenceRule` (as a `RadScheduler` would do) is a significant piece of business logic that would need to be re-implemented in the `GroupingEvent` model or a dedicated service layer if full recurrence functionality is required beyond just data storage.
-   **Time Zones:** Django's `USE_TZ = True` setting is recommended for handling `DateTimeField`s properly across different time zones. `timezone.localtime()` is used in model methods for display.