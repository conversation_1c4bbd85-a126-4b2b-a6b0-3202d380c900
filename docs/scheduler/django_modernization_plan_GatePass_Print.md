## ASP.NET to Django Conversion Script: GatePass Print Module

This document outlines a modernization plan to transition the existing ASP.NET GatePass Print functionality to a modern Django-based solution. The focus is on leveraging AI-assisted automation to streamline the migration, emphasizing a robust, maintainable, and high-performance architecture.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination (where applicable for list views - for this single-item report, it's not directly applicable, but the principle is maintained for other modules).
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database tables and their columns from the ASP.NET code.

The ASP.NET code directly queries several tables. These tables represent core entities within the ERP system. We will define Django models that directly map to these existing database tables.

**Identified Tables and Key Columns:**

*   `tblGate_Pass`
    *   `Id` (Primary Key)
    *   `SessionId` (FK to `tblHR_OfficeStaff.EmpId`)
    *   `CompId`
    *   `SysDate`
    *   `Authorize`
    *   `EmpId` (FK to `tblHR_OfficeStaff.EmpId` - "Self" employee)
    *   `FinYearId` (FK to `tblFinancial_master.FinYearId`)
    *   `GPNo`
    *   `AuthorizedBy` (FK to `tblHR_OfficeStaff.EmpId`)
    *   `AuthorizeDate`
    *   `AuthorizeTime`
*   `tblGatePass_Details`
    *   `Id` (Primary Key, referred to as `DId` in the C# code for distinction)
    *   `MId` (Foreign Key to `tblGate_Pass.Id`)
    *   `FromDate`
    *   `FromTime`
    *   `ToTime`
    *   `Type` (Foreign Key to `tblGatePass_Reason.Id`)
    *   `TypeFor`
    *   `Reason` (Free text reason)
    *   `Feedback`
    *   `EmpId` (FK to `tblHR_OfficeStaff.EmpId` - "Other" employee)
    *   `Place`
    *   `ContactPerson`
    *   `ContactNo`
*   `tblFinancial_master`
    *   `FinYearId` (Primary Key)
    *   `CompId`
    *   `FinYear`
*   `tblGatePass_Reason`
    *   `Id` (Primary Key)
    *   `Reason`
*   `tblHR_OfficeStaff`
    *   `EmpId` (Primary Key)
    *   `CompId`
    *   `Title`
    *   `EmployeeName`
    *   `OfferId` (Foreign Key to `tblHR_Offer_Master.OfferId`)
*   `tblHR_Offer_Master`
    *   `OfferId` (Primary Key)
    *   `CompId`
    *   `TypeOf`

### Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

The `GatePass_Print.aspx` page serves as a **Read (View Report)** operation for a specific Gate Pass record. It does not involve creation, updating, or deleting of Gate Pass data directly from this page. The primary function is to gather related data from multiple tables and present it as a formatted report. The "Cancel" button merely redirects, which in Django is a simple URL redirect.

*   **Read:** The core functionality involves fetching detailed information for a specific `GatePass.Id` (obtained from `Request.QueryString["Id"]`) and its associated `GatePassDetail` records, along with lookups from `FinancialYear`, `GatePassReason`, `OfficeStaff`, and `OfferMaster` tables. This data is then formatted and displayed.

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

The ASP.NET page predominantly uses Crystal Reports controls (`CR:CrystalReportViewer`, `CR:CrystalReportSource`) for rendering. It also has a simple `asp:Button` for "Cancel".

In the Django context, this will be replaced by:

*   **HTML Structure:** A single HTML template that visually presents the Gate Pass report data, mimicking the layout of a printout.
*   **Data Presentation:** The report data will be fetched and prepared in the Django model and passed to the template. Standard HTML elements (tables, divs, spans) will be used for layout and display, styled with Tailwind CSS.
*   **User Action:** A simple HTML `<a>` tag or `button` to navigate back, replacing the "Cancel" button.
*   **Printing:** A standard browser print functionality (triggered via a button or CSS for print media) will be used to generate printable output. No specific custom JavaScript is needed for this, relying on the browser's native capabilities.

### Step 4: Generate Django Code

We will create a new Django application, `scheduler`, to house this functionality.

#### 4.1 Models

Task: Create Django models based on the identified database schema. These models will include methods to encapsulate the complex data retrieval and formatting logic present in the original C# code, adhering to the "Fat Model" principle.

```python
# scheduler/models.py
from django.db import models
from django.utils import timezone
from datetime import datetime

class FinancialYear(models.Model):
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year = models.CharField(db_column='FinYear', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class GatePassReason(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    reason = models.CharField(db_column='Reason', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblGatePass_Reason'
        verbose_name = 'Gate Pass Reason'
        verbose_name_plural = 'Gate Pass Reasons'

    def __str__(self):
        return self.reason

class OfferMaster(models.Model):
    offer_id = models.IntegerField(db_column='OfferId', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId')
    type_of = models.CharField(db_column='TypeOf', max_length=10) # Stored as string "2"

    class Meta:
        managed = False
        db_table = 'tblHR_Offer_Master'
        verbose_name = 'Offer Master'
        verbose_name_plural = 'Offer Masters'

    def __str__(self):
        return f"Offer {self.offer_id} ({self.type_of})"

class OfficeStaff(models.Model):
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId')
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    offer = models.ForeignKey(OfferMaster, on_delete=models.DO_NOTHING, db_column='OfferId', related_name='staff', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title}. {self.employee_name}"

    def get_full_name(self):
        return f"{self.title or ''}. {self.employee_name}".strip()

    def get_employee_type_display(self):
        if self.offer and self.offer.type_of == "2":
            return "[Neha Enterprises]"
        return ""

class GatePass(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    session_id = models.ForeignKey(OfficeStaff, on_delete=models.DO_NOTHING, db_column='SessionId', related_name='gate_passes_session')
    comp_id = models.IntegerField(db_column='CompId')
    sys_date = models.DateField(db_column='SysDate')
    authorize = models.IntegerField(db_column='Authorize') # 0 for No, 1 for Yes, or other states
    emp_id = models.ForeignKey(OfficeStaff, on_delete=models.DO_NOTHING, db_column='EmpId', related_name='gate_passes_self_employee', blank=True, null=True) # SelfEId
    fin_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='gate_passes')
    gp_no = models.CharField(db_column='GPNo', max_length=50)
    authorized_by = models.ForeignKey(OfficeStaff, on_delete=models.DO_NOTHING, db_column='AuthorizedBy', related_name='gate_passes_authorized_by', blank=True, null=True)
    authorize_date = models.DateField(db_column='AuthorizeDate', blank=True, null=True)
    authorize_time = models.CharField(db_column='AuthorizeTime', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblGate_Pass'
        verbose_name = 'Gate Pass'
        verbose_name_plural = 'Gate Passes'

    def __str__(self):
        return f"GP No: {self.gp_no} - {self.sys_date.strftime('%Y-%m-%d')}"

    def get_authorized_by_display(self):
        if self.authorize == 1 and self.authorized_by:
            return self.authorized_by.get_full_name()
        return ""

    def get_authorize_date_display(self):
        if self.authorize == 1 and self.authorize_date:
            return self.authorize_date.strftime('%d/%m/%Y')
        return ""

    def get_authorize_time_display(self):
        if self.authorize == 1 and self.authorize_time:
            return self.authorize_time
        return ""

    def get_self_employee_display(self):
        if self.emp_id:
            return self.emp_id.get_full_name() + ","
        return ""

    def get_session_employee_display(self):
        if self.session_id:
            return self.session_id.get_full_name()
        return ""

class GatePassDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # DId
    gate_pass = models.ForeignKey(GatePass, on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    from_date = models.DateField(db_column='FromDate')
    from_time = models.CharField(db_column='FromTime', max_length=50)
    to_time = models.CharField(db_column='ToTime', max_length=50)
    type_reason = models.ForeignKey(GatePassReason, on_delete=models.DO_NOTHING, db_column='Type', related_name='gate_pass_details')
    type_for = models.CharField(db_column='TypeFor', max_length=255)
    reason = models.CharField(db_column='Reason', max_length=255, blank=True, null=True) # Free text reason
    feedback = models.CharField(db_column='Feedback', max_length=255, blank=True, null=True)
    emp_id = models.ForeignKey(OfficeStaff, on_delete=models.DO_NOTHING, db_column='EmpId', related_name='gate_passes_other_employee', blank=True, null=True) # OtherEId
    place = models.CharField(db_column='Place', max_length=255, blank=True, null=True)
    contact_person = models.CharField(db_column='ContactPerson', max_length=255, blank=True, null=True)
    contact_no = models.CharField(db_column='ContactNo', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblGatePass_Details'
        verbose_name = 'Gate Pass Detail'
        verbose_name_plural = 'Gate Pass Details'

    def __str__(self):
        return f"Detail for GP {self.gate_pass.gp_no} - {self.from_date}"

    def get_reason_type_display(self):
        return self.type_reason.reason

    def get_other_employee_display(self):
        if self.emp_id:
            return self.emp_id.get_full_name() + ","
        return ""

    def get_combined_employee_display(self):
        # Replicates the C# logic for 'Empother' combining SelfEId and OtherEId
        # This logic is complex and spans GatePass and GatePassDetail, better in a method on GatePass
        return self.gate_pass.get_self_employee_display() + self.get_other_employee_display()

    def get_employee_type_context(self):
        # Replicates the C# logic for 'EmpType' for SelfEId or OtherEId
        if self.gate_pass.emp_id:
            return self.gate_pass.emp_id.get_employee_type_display()
        elif self.emp_id:
            return self.emp_id.get_employee_type_display()
        return ""

class GatePassReportData:
    """
    A class to encapsulate the combined report data for a single Gate Pass.
    This replaces the dynamic DataTable construction in C#.
    """
    def __init__(self, gate_pass_instance):
        self.gp = gate_pass_instance
        self.detail = gate_pass_instance.details.first() # Assuming one detail record per gate pass for print view

        # Populate attributes replicating C# DataTable columns
        self.id = self.gp.id
        self.fin_year = self.gp.fin_year.fin_year if self.gp.fin_year else ''
        self.gp_no = self.gp.gp_no
        self.from_date = self.detail.from_date.strftime('%d/%m/%Y') if self.detail and self.detail.from_date else ''
        self.from_time = self.detail.from_time if self.detail else ''
        self.to_time = self.detail.to_time if self.detail else ''
        self.type_of_pass = self.detail.get_reason_type_display() if self.detail else ''
        self.type_for = self.detail.type_for if self.detail else ''
        self.reason_text = self.detail.reason if self.detail and self.detail.reason else '' # Free text reason
        self.authorized_by_display = self.gp.get_authorized_by_display()
        self.authorize_date_display = self.gp.get_authorize_date_display()
        self.authorize_time_display = self.gp.get_authorize_time_display()
        self.feedback = self.detail.feedback if self.detail and self.detail.feedback else ''
        self.detail_id = self.detail.id if self.detail else None
        self.self_other_employee_display = self.detail.get_combined_employee_display() if self.detail else ''
        self.authorize_status = self.gp.authorize
        self.place = self.detail.place if self.detail else ''
        self.contact_person = self.detail.contact_person if self.detail else ''
        self.contact_no = self.detail.contact_no if self.detail else ''
        self.session_employee_display = self.gp.get_session_employee_display()
        self.company_id = self.gp.comp_id
        self.to_date_flag = "A" # Replicating the hardcoded "A" from C#
        self.employee_type_info = self.detail.get_employee_type_context() if self.detail else ''

```

#### 4.2 Forms

Task: Define a Django form for user input.

For a "print" view, a form is not typically needed as there's no user input for CRUD. If a form was needed for filtering or dynamic parameters, it would be defined here. Since the original page is a direct report view, no form is generated.

#### 4.3 Views

Task: Implement the "Read" operation using a Django Class-Based View. The business logic for data aggregation (which was in `Page_Init` in C#) is now handled by the `GatePassReportData` class in the model layer, keeping the view thin.

```python
# scheduler/views.py
from django.views.generic import TemplateView
from django.shortcuts import get_object_or_404
from django.urls import reverse
from django.http import HttpResponseRedirect
from django.contrib.auth.mixins import LoginRequiredMixin # Assuming authentication for ERP

from .models import GatePass, GatePassReportData

class GatePassPrintView(LoginRequiredMixin, TemplateView):
    template_name = 'scheduler/gatepass_print/detail.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        gate_pass_id = self.kwargs.get('pk')
        
        # Fetch the main GatePass object
        gate_pass = get_object_or_404(GatePass.objects.prefetch_related(
            'details',
            'fin_year',
            'session_id',
            'emp_id',
            'authorized_by',
            'details__type_reason',
            'details__emp_id',
            'session_id__offer', # Pre-fetch related offers for employee type logic
            'emp_id__offer',
            'authorized_by__offer',
            'details__emp_id__offer',
        ), id=gate_pass_id)

        # Create the report data object using the GatePass instance
        # This encapsulates all the complex data aggregation logic from C#
        report_data = GatePassReportData(gate_pass)
        context['report'] = report_data
        
        return context

# This view is for the Cancel button redirect.
class GatePassCancelView(LoginRequiredMixin, TemplateView):
    def get(self, request, *args, **kwargs):
        # Simulate the redirect from ASP.NET's Cancel_Click
        # Assuming GatePass_New.aspx maps to a Django view named 'gatepass_new_list'
        return HttpResponseRedirect(reverse('scheduler:gatepass_list'))

```

#### 4.4 Templates

Task: Create templates for the detail view. The main template will display the report, and no separate form/delete templates are needed for this specific page.

```html
{# scheduler/templates/scheduler/gatepass_print/detail.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8 max-w-4xl bg-white shadow-lg rounded-lg print:shadow-none print:p-0">
    <div class="flex justify-between items-center mb-6 print:hidden">
        <h2 class="text-2xl font-bold text-gray-800">GatePass - Print Preview</h2>
        <div class="flex space-x-2">
            <button onclick="window.print()" class="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out">
                Print
            </button>
            <a href="{% url 'scheduler:gatepass_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out">
                Cancel
            </a>
        </div>
    </div>

    {# --- GatePass Report Content --- #}
    <div class="p-6 border border-gray-200 rounded-md print:border-0">
        <h3 class="text-xl font-semibold text-center mb-6 text-gray-800">GATE PASS</h3>

        <div class="grid grid-cols-2 gap-4 mb-4">
            <div><span class="font-medium">Gate Pass No:</span> {{ report.gp_no }}</div>
            <div><span class="font-medium">Date:</span> {{ report.gp.sys_date|date:"d/m/Y" }}</div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4 border-t border-b border-gray-200 py-4">
            <div><span class="font-medium">Financial Year:</span> {{ report.fin_year }}</div>
            <div><span class="font-medium">Company ID:</span> {{ report.company_id }}</div>
            <div><span class="font-medium">Issued By:</span> {{ report.session_employee_display }}</div>
        </div>

        <div class="grid grid-cols-1 gap-4 mb-4">
            <div><span class="font-medium">Employee Name:</span> {{ report.self_other_employee_display }} {{ report.employee_type_info }}</div>
            <div><span class="font-medium">Gate Pass Type:</span> {{ report.type_of_pass }}</div>
            <div><span class="font-medium">Type For:</span> {{ report.type_for }}</div>
            <div><span class="font-medium">Reason:</span> {{ report.reason_text }}</div>
        </div>

        <div class="grid grid-cols-2 gap-4 mb-4 border-t border-b border-gray-200 py-4">
            <div><span class="font-medium">From Date:</span> {{ report.from_date }}</div>
            <div><span class="font-medium">From Time:</span> {{ report.from_time }}</div>
            <div><span class="font-medium">To Time:</span> {{ report.to_time }}</div>
            <div><span class="font-medium">Place:</span> {{ report.place }}</div>
            <div><span class="font-medium">Contact Person:</span> {{ report.contact_person }}</div>
            <div><span class="font-medium">Contact No:</span> {{ report.contact_no }}</div>
        </div>

        <div class="grid grid-cols-1 gap-4 mb-4">
            <div><span class="font-medium">Feedback:</span> {{ report.feedback|default:"N/A" }}</div>
        </div>

        {% if report.authorize_status == 1 %}
        <div class="mt-6 p-4 bg-green-50 border border-green-200 rounded-md">
            <h4 class="text-lg font-semibold text-green-700 mb-2">Authorization Details:</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div><span class="font-medium">Authorized By:</span> {{ report.authorized_by_display }}</div>
                <div><span class="font-medium">Authorization Date:</span> {{ report.authorize_date_display }}</div>
                <div><span class="font-medium">Authorization Time:</span> {{ report.authorize_time_display }}</div>
            </div>
        </div>
        {% else %}
        <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md text-yellow-700">
            <p class="font-medium">Gate Pass Not Authorized.</p>
        </div>
        {% endif %}
    </div>
    {# --- End GatePass Report Content --- #}
</div>
{% endblock %}

{% block extra_js %}
{# No Alpine.js or HTMX needed for this static report view, but kept for consistency #}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed
    });
</script>
{% endblock %}
```

#### 4.5 URLs

Task: Define URL patterns for the views.

```python
# scheduler/urls.py
from django.urls import path
from .views import GatePassPrintView, GatePassCancelView

app_name = 'scheduler' # Define app_name for namespacing

urlpatterns = [
    # URL for displaying a specific Gate Pass print view
    path('gatepass-print/<int:pk>/', GatePassPrintView.as_view(), name='gatepass_print'),
    
    # URL for the Cancel button redirect (example for GatePass_New.aspx)
    path('gatepass/cancel/', GatePassCancelView.as_view(), name='gatepass_cancel'),
    
    # Placeholder for the actual GatePass List view (assuming this exists or will be migrated)
    path('gatepass/', GatePassCancelView.as_view(), name='gatepass_list'), # This should point to your actual GatePass list view.
]
```

#### 4.6 Tests

Task: Write tests for the models and views to ensure data integrity and functionality.

```python
# scheduler/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date, time
from .models import FinancialYear, GatePassReason, OfferMaster, OfficeStaff, GatePass, GatePassDetail, GatePassReportData

class SchedulerModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create common test data
        cls.comp_id = 1
        cls.fin_year = FinancialYear.objects.create(fin_year_id=2023, comp_id=cls.comp_id, fin_year='2023-2024')
        cls.reason_type_personal = GatePassReason.objects.create(id=1, reason='Personal Work')
        cls.offer_type_normal = OfferMaster.objects.create(offer_id=101, comp_id=cls.comp_id, type_of='1')
        cls.offer_type_neha = OfferMaster.objects.create(offer_id=102, comp_id=cls.comp_id, type_of='2')

        cls.staff_authorized_by = OfficeStaff.objects.create(
            emp_id=1001, comp_id=cls.comp_id, title='Mr', employee_name='John Doe', offer=cls.offer_type_normal
        )
        cls.staff_self_employee = OfficeStaff.objects.create(
            emp_id=1002, comp_id=cls.comp_id, title='Ms', employee_name='Jane Smith', offer=cls.offer_type_neha
        )
        cls.staff_other_employee = OfficeStaff.objects.create(
            emp_id=1003, comp_id=cls.comp_id, title='Dr', employee_name='Alan Turing', offer=cls.offer_type_normal
        )
        cls.staff_session = OfficeStaff.objects.create(
            emp_id=1004, comp_id=cls.comp_id, title='Admin', employee_name='System User', offer=cls.offer_type_normal
        )

        cls.gate_pass_authorized = GatePass.objects.create(
            id=1, session_id=cls.staff_session, comp_id=cls.comp_id, sys_date=date(2023, 10, 26),
            authorize=1, emp_id=cls.staff_self_employee, fin_year=cls.fin_year, gp_no='GP001/23',
            authorized_by=cls.staff_authorized_by, authorize_date=date(2023, 10, 27), authorize_time='10:30 AM'
        )
        cls.gate_pass_unauthorized = GatePass.objects.create(
            id=2, session_id=cls.staff_session, comp_id=cls.comp_id, sys_date=date(2023, 10, 28),
            authorize=0, emp_id=cls.staff_self_employee, fin_year=cls.fin_year, gp_no='GP002/23'
        )

        cls.gate_pass_detail_authorized = GatePassDetail.objects.create(
            id=101, gate_pass=cls.gate_pass_authorized, from_date=date(2023, 10, 26), from_time='10:00 AM', to_time='01:00 PM',
            type_reason=cls.reason_type_personal, type_for='Self', reason='Doctor visit', feedback='Completed',
            emp_id=cls.staff_other_employee, place='City Hospital', contact_person='Dr. Brown', contact_no='1234567890'
        )
        cls.gate_pass_detail_unauthorized = GatePassDetail.objects.create(
            id=102, gate_pass=cls.gate_pass_unauthorized, from_date=date(2023, 10, 28), from_time='02:00 PM', to_time='05:00 PM',
            type_reason=cls.reason_type_personal, type_for='Other', reason='Meeting client', feedback=None,
            emp_id=cls.staff_other_employee, place='Client Office', contact_person='Mr. White', contact_no='0987654321'
        )

    def test_office_staff_full_name(self):
        self.assertEqual(self.staff_authorized_by.get_full_name(), 'Mr. John Doe')
        self.assertEqual(self.staff_self_employee.get_full_name(), 'Ms. Jane Smith')

    def test_office_staff_employee_type_display(self):
        self.assertEqual(self.staff_self_employee.get_employee_type_display(), '[Neha Enterprises]')
        self.assertEqual(self.staff_authorized_by.get_employee_type_display(), '')

    def test_gate_pass_authorization_display(self):
        self.assertEqual(self.gate_pass_authorized.get_authorized_by_display(), 'Mr. John Doe')
        self.assertEqual(self.gate_pass_authorized.get_authorize_date_display(), '27/10/2023')
        self.assertEqual(self.gate_pass_authorized.get_authorize_time_display(), '10:30 AM')
        self.assertEqual(self.gate_pass_unauthorized.get_authorized_by_display(), '')
        self.assertEqual(self.gate_pass_unauthorized.get_authorize_date_display(), '')
        self.assertEqual(self.gate_pass_unauthorized.get_authorize_time_display(), '')

    def test_gate_pass_employee_display(self):
        self.assertEqual(self.gate_pass_authorized.get_self_employee_display(), 'Ms. Jane Smith,')
        self.assertEqual(self.gate_pass_authorized.get_session_employee_display(), 'Admin. System User')

    def test_gate_pass_detail_reason_type_display(self):
        self.assertEqual(self.gate_pass_detail_authorized.get_reason_type_display(), 'Personal Work')

    def test_gate_pass_detail_other_employee_display(self):
        self.assertEqual(self.gate_pass_detail_authorized.get_other_employee_display(), 'Dr. Alan Turing,')

    def test_gate_pass_detail_combined_employee_display(self):
        # This tests the logic as if it were on GatePassDetail, even though we moved to GatePassReportData
        self.assertEqual(self.gate_pass_detail_authorized.get_combined_employee_display(), 'Ms. Jane Smith,Dr. Alan Turing,')

    def test_gate_pass_detail_employee_type_context(self):
        self.assertEqual(self.gate_pass_detail_authorized.get_employee_type_context(), '[Neha Enterprises]')
        self.assertEqual(self.gate_pass_detail_unauthorized.get_employee_type_context(), '[Neha Enterprises]') # Still linked to self employee

    def test_gate_pass_report_data(self):
        report = GatePassReportData(self.gate_pass_authorized)
        self.assertEqual(report.id, 1)
        self.assertEqual(report.fin_year, '2023-2024')
        self.assertEqual(report.gp_no, 'GP001/23')
        self.assertEqual(report.from_date, '26/10/2023')
        self.assertEqual(report.authorized_by_display, 'Mr. John Doe')
        self.assertEqual(report.authorize_date_display, '27/10/2023')
        self.assertEqual(report.self_other_employee_display, 'Ms. Jane Smith,Dr. Alan Turing,')
        self.assertEqual(report.employee_type_info, '[Neha Enterprises]')
        self.assertEqual(report.to_date_flag, 'A') # Ensure the hardcoded value is present

        report_unauth = GatePassReportData(self.gate_pass_unauthorized)
        self.assertEqual(report_unauth.authorize_status, 0)
        self.assertEqual(report_unauth.authorized_by_display, '')
        self.assertEqual(report_unauth.authorize_date_display, '')
        self.assertIsNone(report_unauth.detail.feedback) # Check for DBNull.Value equivalent

class GatePassPrintViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create common test data
        cls.comp_id = 1
        cls.fin_year = FinancialYear.objects.create(fin_year_id=2023, comp_id=cls.comp_id, fin_year='2023-2024')
        cls.reason_type = GatePassReason.objects.create(id=1, reason='Personal Work')
        cls.offer_type = OfferMaster.objects.create(offer_id=101, comp_id=cls.comp_id, type_of='1')
        cls.staff_session = OfficeStaff.objects.create(emp_id=1004, comp_id=cls.comp_id, title='Admin', employee_name='System User', offer=cls.offer_type)
        cls.staff_employee = OfficeStaff.objects.create(emp_id=1005, comp_id=cls.comp_id, title='Mr', employee_name='Test Emp', offer=cls.offer_type)

        cls.gate_pass = GatePass.objects.create(
            id=1, session_id=cls.staff_session, comp_id=cls.comp_id, sys_date=date(2023, 11, 1),
            authorize=1, emp_id=cls.staff_employee, fin_year=cls.fin_year, gp_no='GPTEST/23',
            authorized_by=cls.staff_employee, authorize_date=date(2023, 11, 1), authorize_time='09:00 AM'
        )
        GatePassDetail.objects.create(
            id=1001, gate_pass=cls.gate_pass, from_date=date(2023, 11, 1), from_time='09:00 AM', to_time='10:00 AM',
            type_reason=cls.reason_type, type_for='Self', reason='Meeting', feedback='None',
            emp_id=cls.staff_employee, place='Office', contact_person='Manager', contact_no='111111'
        )
        
        # Simulate a logged-in user (required by LoginRequiredMixin)
        cls.client = Client()
        cls.client.force_login(cls.staff_session) # Assuming OfficeStaff can be a user

    def test_gatepass_print_view_success(self):
        response = self.client.get(reverse('scheduler:gatepass_print', args=[self.gate_pass.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'scheduler/gatepass_print/detail.html')
        self.assertIn('report', response.context)
        self.assertEqual(response.context['report'].gp_no, 'GPTEST/23')
        self.assertContains(response, 'GatePass - Print Preview')
        self.assertContains(response, 'GPTEST/23')
        self.assertContains(response, 'Mr. Test Emp') # Authorized by
        self.assertContains(response, 'Authorization Details:')

    def test_gatepass_print_view_not_found(self):
        response = self.client.get(reverse('scheduler:gatepass_print', args=[9999])) # Non-existent ID
        self.assertEqual(response.status_code, 404)

    def test_gatepass_cancel_view_redirect(self):
        response = self.client.get(reverse('scheduler:gatepass_cancel'))
        self.assertEqual(response.status_code, 302) # Should redirect
        self.assertRedirects(response, reverse('scheduler:gatepass_list'))

    def test_gatepass_print_view_unauthenticated_access(self):
        self.client.logout() # Log out the test user
        response = self.client.get(reverse('scheduler:gatepass_print', args=[self.gate_pass.id]))
        # Should redirect to login page (default for LoginRequiredMixin)
        self.assertEqual(response.status_code, 302)
        self.assertIn('/accounts/login/', response.url) # Adjust based on your login URL configuration

```

### Step 5: HTMX and Alpine.js Integration

While this specific "print" page doesn't require complex HTMX/Alpine.js interactions (it's primarily a static report view), the overall strategy for the ERP involves these technologies.

*   **HTMX:** For this module, HTMX can be used for dynamic content loading in an encompassing "Gate Pass Management" module. For example, if there was a list of Gate Passes and clicking one opened its print view in a modal, HTMX would facilitate that. For the direct `gatepass_print` view, it simply renders the full page.
*   **Alpine.js:** Similarly, Alpine.js is not strictly necessary for a static report display but would be crucial for interactive elements in other parts of the ERP, such as filtering the list of gate passes, showing/hiding sections, or client-side form validation.
*   **DataTables:** Not directly applicable to a single-item report view. However, DataTables would be utilized in the `gatepass_list` view (the target of the "Cancel" button) for efficient display and interaction with multiple gate pass records.
*   **No full page reloads:** This principle is maintained as the views are thin and only retrieve necessary data. Subsequent interactions (like navigating back to the list) would ideally use HTMX for smooth transitions if the `gatepass_list` view is also modernized with HTMX.
*   **`HX-Trigger`:** For a direct report view, there's no state change on this page that needs to trigger updates elsewhere. However, if this page was part of a modal flow, success messages or triggers could be sent.

## Final Notes

*   **Placeholders:** `[MODEL_NAME]`, `[APP_NAME]`, etc., have been replaced with `GatePass`, `scheduler`, etc.
*   **DRY Templates:** The `detail.html` template extends `core/base.html` for consistency.
*   **Business Logic in Models:** The complex data aggregation and formatting from the C# `Page_Init` method have been moved to the `GatePass` model and the `GatePassReportData` class, keeping the `GatePassPrintView` lean (around 10 lines of actual logic).
*   **Comprehensive Tests:** Unit tests for models and integration tests for the view are provided, ensuring high test coverage.
*   **Authentication:** `LoginRequiredMixin` has been added to the Django view, assuming the ERP requires user authentication, which is common.
*   **`clsFunctions` replacement:** All custom utility functions (`fun.Connection()`, `fun.select()`, `fun.FromDateDMY()`) are replaced by Django's ORM, `timezone` utilities, and Python's `datetime` formatting.
*   **Hardcoded "A" for ToDate:** The original code had `dr[21] = "A";` for `ToDate`. This is replicated as `self.to_date_flag = "A"` in `GatePassReportData` to ensure behavioral parity, even though its original purpose is unclear. This is an example of an artifact that would be reviewed and potentially refactored/removed during a full modernization project.