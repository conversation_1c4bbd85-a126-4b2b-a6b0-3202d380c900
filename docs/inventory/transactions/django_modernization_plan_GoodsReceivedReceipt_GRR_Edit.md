This comprehensive plan outlines the step-by-step process for modernizing the provided ASP.NET Goods Received Receipt (GRR) application to a robust Django 5.0+ solution. Our approach prioritizes automation-driven migration, leveraging Django's "fat model, thin view" paradigm, HTMX, Alpine.js, and DataTables for a highly performant and user-friendly experience. All technical concepts are presented in plain English, suitable for business stakeholders to understand and oversee the transition.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**

From the ASP.NET code, we identify the following:

*   **Primary Data Source:** The `GridView2` and the `Sp_GRR_Edit` stored procedure indicate a main table for Goods Received Receipts (GRR), likely named `tblMM_GRR_Master` or similar, which is being joined with other tables like `tblMM_PO_Master` for filtering.
    *   **Table Name:** We will infer this as `tblMM_GRR_Master` for the main GRR records.
    *   **Columns (from `GridView2` `Eval` expressions):**
        *   `Id` (Primary Key, integer)
        *   `FinYearId` (Financial Year ID, integer)
        *   `GRRNo` (GRR Number, string)
        *   `SysDate` (System Date, date)
        *   `GINId` (GIN ID, integer)
        *   `GINNo` (GIN Number, string)
        *   `PONo` (Purchase Order Number, string)
        *   `Supplier` (Supplier Name, string - likely denormalized, or from join)
        *   `SupId` (Supplier ID, integer - Foreign Key to Supplier table)
        *   `ChNO` (Challan Number, string)
        *   `ChDT` (Challan Date, date)

*   **Supplier Data Source:** The `AutoCompleteExtender` and the `sql` WebMethod clearly point to a Supplier master table.
    *   **Table Name:** `tblMM_Supplier_master`
    *   **Columns (from `sql` method's `fun.select`):**
        *   `SupplierId` (Primary Key, integer)
        *   `SupplierName` (Supplier Name, string)
        *   `CompId` (Company ID, integer - used for filtering)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic in the ASP.NET code.

**Instructions:**

*   **Create:** There's no explicit "Create New GRR" functionality on *this* `.aspx` page; it's an "Edit" list page. However, for a complete Django solution, we will include a `CreateView` for the `GoodsReceivedReceipt` model, which would typically be triggered by an "Add New" button on the list page.
*   **Read:**
    *   **Listing/Filtering:** The `loadData` method, calling `Sp_GRR_Edit` (a stored procedure), is the primary read operation. It filters data by `CompId` (Company ID), `FinYearId` (Financial Year ID), and optionally by `SupplierId`. This will translate to Django ORM queries.
    *   **Supplier Autocomplete:** The `sql` WebMethod reads `SupplierId` and `SupplierName` from `tblMM_Supplier_master` based on a prefix. This will be an HTMX-driven autocomplete endpoint.
*   **Update:** The "Select" `LinkButton` in `GridView2` leads to a `Response.Redirect` to `GoodsReceivedReceipt_GRR_Edit_Details.aspx`. This indicates that the actual editing of a GRR record occurs on a separate page. For a modern Django approach, we will integrate this "edit" functionality into a modal triggered from the list page, using a `GoodsReceivedReceiptUpdateView`.
*   **Delete:** No explicit delete functionality is present on this page. However, for comprehensive CRUD, a `DeleteView` and associated modal will be included.
*   **Search/Pagination:** The `btnSearch_Click` event and `GridView2_PageIndexChanging` handle client-side search and pagination (though DataTables will take over this in Django).

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

*   **Search/Filter:**
    *   `txtSupplier` (TextBox with `AutoCompleteExtender`): Used for searching/filtering by supplier.
    *   `btnSearch` (Button): Triggers the search based on supplier input.
*   **Data Display:**
    *   `GridView2`: Displays a list of GRR records with pagination and columns for GRR Number, Date, GIN Number, PO Number, Supplier, Challan Number, and Challan Date. It also includes hidden `Id`, `FinYearId`, `GINId`, `SupId` fields.
*   **Actions:**
    *   `LinkButton` with `CommandName="Sel"`: This "Select" button on each row redirects to a detailed edit page for the selected GRR. In Django, this will be represented by an "Edit" button triggering a modal form.

### Step 4: Generate Django Code

We will create a Django application named `inventory_transactions`.

#### 4.1 Models

**Task:** Create Django models based on the identified database schemas.

**Instructions:**

*   **`Supplier` Model:** Maps to `tblMM_Supplier_master`.
*   **`GoodsReceivedReceipt` Model:** Maps to `tblMM_GRR_Master` (inferred) and contains fields from `GridView2` and the `Sp_GRR_Edit` output.
*   Both models will use `managed = False` and `db_table` to connect to existing tables.
*   Business logic for data retrieval (like the `Sp_GRR_Edit` stored procedure) will be implemented as a custom manager or method on the `GoodsReceivedReceipt` model.

**File: `inventory_transactions/models.py`**

```python
from django.db import models

class Supplier(models.Model):
    """
    Maps to the tblMM_Supplier_master table.
    Used for supplier information and autocomplete functionality.
    """
    id = models.IntegerField(db_column='SupplierId', primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    # CompId is filtered on in original logic, assuming it's part of the table
    company_id = models.SmallIntegerField(db_column='CompId', null=True, blank=True)

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return f"{self.supplier_name} [{self.id}]"

class GoodsReceivedReceiptManager(models.Manager):
    """
    Custom manager for GoodsReceivedReceipt to encapsulate complex query logic
    similar to Sp_GRR_Edit stored procedure.
    """
    def get_filtered_grr_data(self, company_id, financial_year_id, supplier_id=None):
        """
        Retrieves GRR data, mimicking the Sp_GRR_Edit stored procedure.
        Assumes joins are implicitly handled by the original table structure or
        we are querying a pre-joined view/materialized view if Sp_GRR_Edit is complex.
        For direct table mapping, this queries GoodsReceivedReceipt.
        """
        queryset = self.filter(
            company_id=company_id, # Assuming company_id exists on GRR table or inferred from context
            financial_year_id=financial_year_id
        )
        if supplier_id:
            queryset = queryset.filter(supplier__id=supplier_id) # Filter by FK to Supplier
        
        # Order by a meaningful field, e.g., GRRNo or SysDate
        return queryset.order_by('-system_date', '-grr_no')


class GoodsReceivedReceipt(models.Model):
    """
    Maps to the inferred tblMM_GRR_Master table or a view joining relevant data.
    Fields derived from GridView2 columns.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    financial_year_id = models.SmallIntegerField(db_column='FinYearId')
    grr_no = models.CharField(db_column='GRRNo', max_length=50)
    system_date = models.DateField(db_column='SysDate')
    gin_id = models.SmallIntegerField(db_column='GINId')
    gin_no = models.CharField(db_column='GINNo', max_length=50)
    po_no = models.CharField(db_column='PONo', max_length=50)
    # The 'Supplier' column from GridView is denormalized name, 'SupId' is the actual FK.
    supplier_name_denormalized = models.CharField(db_column='Supplier', max_length=255)
    supplier = models.ForeignKey(Supplier, models.DO_NOTHING, db_column='SupId') # Direct FK to Supplier model
    challan_no = models.CharField(db_column='ChNO', max_length=50)
    challan_date = models.DateField(db_column='ChDT')
    # Assuming company_id exists on the GRR table for filtering as per Sp_GRR_Edit
    company_id = models.SmallIntegerField(db_column='CompId', null=True, blank=True)

    objects = GoodsReceivedReceiptManager() # Attach custom manager

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblMM_GRR_Master' # Inferred table name
        verbose_name = 'Goods Received Receipt'
        verbose_name_plural = 'Goods Received Receipts'

    def __str__(self):
        return f"{self.grr_no} - {self.supplier_name_denormalized}"

    # Business logic methods can be added here, e.g., for status updates or calculations
    def get_display_date(self):
        """Formats the system date for display."""
        return self.system_date.strftime("%Y-%m-%d") if self.system_date else ''

    def get_supplier_display(self):
        """Returns the supplier name from the FK relationship, fallback to denormalized."""
        return self.supplier.supplier_name if self.supplier else self.supplier_name_denormalized

    def get_absolute_edit_url(self):
        """Returns the URL for editing this specific GRR."""
        from django.urls import reverse
        return reverse('goodsreceivedreceipt_edit', args=[self.pk])

    def get_absolute_delete_url(self):
        """Returns the URL for deleting this specific GRR."""
        from django.urls import reverse
        return reverse('goodsreceivedreceipt_delete', args=[self.pk])
```

#### 4.2 Forms

**Task:** Define Django forms for user input and the search functionality.

**Instructions:**

*   **`GoodsReceivedReceiptForm`:** A ModelForm for creating/editing GRR records.
*   **`SupplierSearchForm`:** A simple form for the supplier search input.

**File: `inventory_transactions/forms.py`**

```python
from django import forms
from .models import GoodsReceivedReceipt, Supplier
from django.db.models import Q # For custom validation if needed

class GoodsReceivedReceiptForm(forms.ModelForm):
    """
    Form for creating and updating Goods Received Receipts.
    """
    class Meta:
        model = GoodsReceivedReceipt
        fields = [
            'grr_no', 'system_date', 'gin_no', 'po_no',
            'supplier', 'challan_no', 'challan_date',
            # financial_year_id and company_id might be set automatically or via context
        ]
        widgets = {
            'grr_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'system_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'gin_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'po_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'supplier': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'challan_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'challan_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = { # Define more user-friendly labels if needed
            'grr_no': 'GRR Number',
            'system_date': 'GRR Date',
            'gin_no': 'GIN Number',
            'po_no': 'PO Number',
            'supplier': 'Supplier',
            'challan_no': 'Challan Number',
            'challan_date': 'Challan Date',
        }

    # Custom validation example (if needed for unique GRR_No per financial year/company)
    def clean(self):
        cleaned_data = super().clean()
        grr_no = cleaned_data.get('grr_no')
        instance = self.instance

        # Assuming company_id and financial_year_id are passed to the form
        # Or derived from the request/user in the view
        company_id = self.initial.get('company_id') or getattr(self, '_company_id', None)
        financial_year_id = self.initial.get('financial_year_id') or getattr(self, '_financial_year_id', None)

        if grr_no and company_id and financial_year_id:
            # Check for uniqueness if required
            queryset = GoodsReceivedReceipt.objects.filter(
                grr_no=grr_no,
                company_id=company_id,
                financial_year_id=financial_year_id
            )
            if instance and instance.pk: # Exclude current instance for updates
                queryset = queryset.exclude(pk=instance.pk)
            
            if queryset.exists():
                self.add_error('grr_no', 'This GRR Number already exists for the current financial year and company.')
        return cleaned_data


class SupplierSearchForm(forms.Form):
    """
    Form for searching suppliers with an autocomplete field.
    """
    supplier_id = forms.IntegerField(
        required=False,
        widget=forms.HiddenInput() # Hidden field to store selected supplier ID
    )
    supplier_name = forms.CharField(
        label="Supplier",
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Start typing supplier name...',
            'hx-get': '/inventory_transactions/suppliers/autocomplete/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms, search', # Trigger on keyup, with delay
            'hx-target': '#supplier-autocomplete-results', # Target to display results
            'hx-swap': 'innerHTML',
            'hx-indicator': '#supplier-autocomplete-loader' # Show loader
        })
    )
```

#### 4.3 Views

**Task:** Implement CRUD operations and data listing using Django Class-Based Views (CBVs), HTMX, and a dedicated autocomplete view.

**Instructions:**

*   **`GoodsReceivedReceiptListView`:** Displays the main search and list page.
*   **`GoodsReceivedReceiptTablePartialView`:** An HTMX endpoint to fetch and render only the table content, allowing dynamic updates after searches or CRUD operations.
*   **`SupplierAutocompleteView`:** An HTMX endpoint that provides supplier suggestions for the search box.
*   **`GoodsReceivedReceiptCreateView`:** Handles adding new GRR records via a modal.
*   **`GoodsReceivedReceiptUpdateView`:** Handles editing existing GRR records via a modal.
*   **`GoodsReceivedReceiptDeleteView`:** Handles deleting GRR records via a confirmation modal.

**File: `inventory_transactions/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from django.shortcuts import get_object_or_404
from .models import GoodsReceivedReceipt, Supplier
from .forms import GoodsReceivedReceiptForm, SupplierSearchForm

# --- Helper function to get Company ID and Financial Year ID from session/user ---
# In a real application, this would come from the authenticated user's profile
# or a multi-tenancy context. For demonstration, we'll use placeholder values.
def get_user_context_ids(request):
    # Placeholder: Replace with actual logic to get company_id and financial_year_id
    # e.g., from request.user.profile or session
    company_id = 1 # Assuming a default company ID
    financial_year_id = 2024 # Assuming a default financial year ID
    return company_id, financial_year_id

# --- Main GRR List and Search View ---
class GoodsReceivedReceiptListView(ListView):
    model = GoodsReceivedReceipt
    template_name = 'inventory_transactions/goodsreceivedreceipt/list.html'
    context_object_name = 'goodsreceivedreceipts' # For initial load, though HTMX reloads table
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initial load might not need all data, as the table partial will load it
        # However, we need the search form for the main page
        context['search_form'] = SupplierSearchForm(self.request.GET)
        return context

# --- HTMX Partial View for the GRR Table ---
class GoodsReceivedReceiptTablePartialView(ListView):
    model = GoodsReceivedReceipt
    template_name = 'inventory_transactions/goodsreceivedreceipt/_goodsreceivedreceipt_table.html'
    context_object_name = 'goodsreceivedreceipts'

    def get_queryset(self):
        company_id, financial_year_id = get_user_context_ids(self.request)
        supplier_id = self.request.GET.get('supplier_id') # Get supplier_id from search form

        # Use the custom manager method to filter data
        queryset = GoodsReceivedReceipt.objects.get_filtered_grr_data(
            company_id=company_id,
            financial_year_id=financial_year_id,
            supplier_id=supplier_id if supplier_id else None
        )
        return queryset

# --- HTMX Partial View for Supplier Autocomplete ---
class SupplierAutocompleteView(View):
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        company_id, _ = get_user_context_ids(request) # Use company_id for filtering suppliers

        if query:
            # Filter suppliers by name and company_id
            suppliers = Supplier.objects.filter(
                supplier_name__icontains=query,
                company_id=company_id
            ).values('id', 'supplier_name')[:10] # Limit results for performance

            # Return HTML list for HTMX to swap in
            html = render_to_string('inventory_transactions/goodsreceivedreceipt/_supplier_autocomplete_results.html', 
                                    {'suppliers': suppliers}, request=request)
            return HttpResponse(html)
        return HttpResponse("") # Return empty if no query

# --- CRUD Views for Goods Received Receipts (using modals) ---
class GoodsReceivedReceiptCreateView(CreateView):
    model = GoodsReceivedReceipt
    form_class = GoodsReceivedReceiptForm
    template_name = 'inventory_transactions/goodsreceivedreceipt/_goodsreceivedreceipt_form.html'
    success_url = reverse_lazy('goodsreceivedreceipt_list') # Not directly used for HTMX, but good practice

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        company_id, financial_year_id = get_user_context_ids(self.request)
        # Pass company_id and financial_year_id to the form for validation/default values
        kwargs['initial'] = {
            'company_id': company_id,
            'financial_year_id': financial_year_id,
        }
        # Attach context to the form instance for validation
        self.form_class._company_id = company_id
        self.form_class._financial_year_id = financial_year_id
        return kwargs

    def form_valid(self, form):
        # Set default values for company_id and financial_year_id
        company_id, financial_year_id = get_user_context_ids(self.request)
        form.instance.company_id = company_id
        form.instance.financial_year_id = financial_year_id
        
        response = super().form_valid(form)
        messages.success(self.request, 'Goods Received Receipt added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, indicates success to HTMX
                headers={
                    'HX-Trigger': 'refreshGoodsReceivedReceiptList' # Trigger custom event to refresh table
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # If HTMX request and form is invalid, return the form with errors
            return HttpResponse(render_to_string(self.template_name, {'form': form}, request=self.request))
        return response


class GoodsReceivedReceiptUpdateView(UpdateView):
    model = GoodsReceivedReceipt
    form_class = GoodsReceivedReceiptForm
    template_name = 'inventory_transactions/goodsreceivedreceipt/_goodsreceivedreceipt_form.html'
    success_url = reverse_lazy('goodsreceivedreceipt_list')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        company_id, financial_year_id = get_user_context_ids(self.request)
        self.form_class._company_id = company_id
        self.form_class._financial_year_id = financial_year_id
        return kwargs

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Goods Received Receipt updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshGoodsReceivedReceiptList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return HttpResponse(render_to_string(self.template_name, {'form': form}, request=self.request))
        return response


class GoodsReceivedReceiptDeleteView(DeleteView):
    model = GoodsReceivedReceipt
    template_name = 'inventory_transactions/goodsreceivedreceipt/_goodsreceivedreceipt_confirm_delete.html'
    success_url = reverse_lazy('goodsreceivedreceipt_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Goods Received Receipt deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshGoodsReceivedReceiptList'
                }
            )
        return response
```

#### 4.4 Templates

**Task:** Create templates for each view, leveraging HTMX for dynamic content loading and Alpine.js for UI state.

**Instructions:**

*   **`list.html`:** The main page, loads the search form and the table via HTMX.
*   **`_goodsreceivedreceipt_table.html`:** A partial template containing the DataTables structure.
*   **`_supplier_autocomplete_results.html`:** A partial template for displaying autocomplete results.
*   **`_goodsreceivedreceipt_form.html`:** A partial template for the add/edit form, designed for modal use.
*   **`_goodsreceivedreceipt_confirm_delete.html`:** A partial template for the delete confirmation modal.

**File: `inventory_transactions/templates/inventory_transactions/goodsreceivedreceipt/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Goods Received Receipts (GRR) - Edit</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
            hx-get="{% url 'goodsreceivedreceipt_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i> Add New GRR
        </button>
    </div>

    <!-- Supplier Search Form -->
    <div class="bg-white p-6 rounded-lg shadow mb-6">
        <div class="flex items-end space-x-4">
            <div class="flex-grow">
                {{ search_form.supplier_name.label_tag }}
                <div class="relative">
                    {{ search_form.supplier_name }}
                    <div id="supplier-autocomplete-results" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-60 overflow-y-auto">
                        <!-- Autocomplete results will be loaded here via HTMX -->
                    </div>
                    <div id="supplier-autocomplete-loader" class="htmx-indicator absolute right-3 top-1/2 -translate-y-1/2">
                        <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                    </div>
                </div>
                {{ search_form.supplier_id }} {# Hidden field for selected supplier ID #}
            </div>
            <div>
                <button 
                    class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                    hx-get="{% url 'goodsreceivedreceipt_table' %}"
                    hx-target="#goodsreceivedreceiptTable-container"
                    hx-swap="innerHTML"
                    hx-indicator="#grr-table-loader"
                    hx-vals="js:{supplier_id: document.getElementById('id_supplier_id').value}">
                    <i class="fas fa-search mr-2"></i> Search
                </button>
            </div>
        </div>
    </div>
    
    <div id="goodsreceivedreceiptTable-container"
         hx-trigger="load, refreshGoodsReceivedReceiptList from:body"
         hx-get="{% url 'goodsreceivedreceipt_table' %}"
         hx-swap="innerHTML"
         hx-indicator="#grr-table-loader">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center p-8">
            <div id="grr-table-loader" class="htmx-indicator inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading GRR data...</p>
        </div>
    </div>
    
    <!-- Modal for CRUD operations -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4 sm:mx-0 overflow-y-auto max-h-[90vh]">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components can be defined here if complex UI state is needed
        // For basic modal toggle, htmx + _hyperscript is often sufficient.
        
        // Listener for autocomplete selection
        document.body.addEventListener('click', function(event) {
            if (event.target.matches('.autocomplete-item')) {
                const supplierName = event.target.dataset.name;
                const supplierId = event.target.dataset.id;
                
                document.getElementById('id_supplier_name').value = supplierName;
                document.getElementById('id_supplier_id').value = supplierId;
                document.getElementById('supplier-autocomplete-results').innerHTML = ''; // Clear results
            }
        });
    });
    // Initialize DataTables after content is loaded by HTMX
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.target.id === 'goodsreceivedreceiptTable-container') {
            $('#goodsreceivedreceiptTable').DataTable({
                "pageLength": 20, // Match ASP.NET PageSize
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true, // Destroy existing table if re-initializing
            });
        }
    });
</script>
{% endblock %}
```

**File: `inventory_transactions/templates/inventory_transactions/goodsreceivedreceipt/_goodsreceivedreceipt_table.html`**

```html
<div class="bg-white p-6 rounded-lg shadow overflow-x-auto">
    <table id="goodsreceivedreceiptTable" class="min-w-full divide-y divide-gray-200">
        <thead>
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GRR No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GIN No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for grr in goodsreceivedreceipts %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ grr.grr_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ grr.system_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ grr.gin_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ grr.po_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ grr.get_supplier_display }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ grr.challan_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ grr.challan_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md shadow-sm transition duration-150 ease-in-out mr-2"
                        hx-get="{% url 'goodsreceivedreceipt_edit' grr.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md shadow-sm transition duration-150 ease-in-out"
                        hx-get="{% url 'goodsreceivedreceipt_delete' grr.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        <i class="fas fa-trash-alt"></i> Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="9" class="py-4 px-4 text-center text-sm text-gray-500">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
// DataTables initialization is handled in list.html's htmx:afterSwap event listener
// This script block will still execute, but the primary initialization logic is centralized.
// It's crucial for DataTables to be initialized *after* the table HTML is inserted into the DOM by HTMX.
</script>
```

**File: `inventory_transactions/templates/inventory_transactions/goodsreceivedreceipt/_supplier_autocomplete_results.html`**

```html
{% for supplier in suppliers %}
    <div class="autocomplete-item px-4 py-2 hover:bg-gray-100 cursor-pointer" 
         data-id="{{ supplier.id }}" 
         data-name="{{ supplier.supplier_name }}">
        {{ supplier.supplier_name }} [{{ supplier.id }}]
    </div>
{% empty %}
    <div class="px-4 py-2 text-gray-500">No matching suppliers found.</div>
{% endfor %}
```

**File: `inventory_transactions/templates/inventory_transactions/goodsreceivedreceipt/_goodsreceivedreceipt_form.html`**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Goods Received Receipt</h3>
    <form hx-post="{{ request.path }}" 
          hx-swap="none" 
          hx-indicator="#form-loader" 
          hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-600 text-sm mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                <span id="form-loader" class="htmx-indicator inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                Save
            </button>
        </div>
    </form>
</div>
```

**File: `inventory_transactions/templates/inventory_transactions/goodsreceivedreceipt/_goodsreceivedreceipt_confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-red-700 mb-6">Confirm Deletion</h3>
    <p class="text-gray-700 mb-8">
        Are you sure you want to delete the Goods Received Receipt: <span class="font-bold">{{ goodsreceivedreceipt.grr_no }}</span> 
        (Supplier: <span class="font-bold">{{ goodsreceivedreceipt.get_supplier_display }}</span>)?
        This action cannot be undone.
    </p>
    
    <form hx-delete="{% url 'goodsreceivedreceipt_delete' goodsreceivedreceipt.pk %}" 
          hx-swap="none" 
          hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                <i class="fas fa-trash-alt mr-2"></i> Confirm Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views within the `inventory_transactions` app.

**Instructions:**

*   Create paths for the main list view, the table partial, supplier autocomplete, and CRUD operations (add, edit, delete).

**File: `inventory_transactions/urls.py`**

```python
from django.urls import path
from .views import (
    GoodsReceivedReceiptListView,
    GoodsReceivedReceiptTablePartialView,
    SupplierAutocompleteView,
    GoodsReceivedReceiptCreateView,
    GoodsReceivedReceiptUpdateView,
    GoodsReceivedReceiptDeleteView,
)

urlpatterns = [
    # Main list page for GRR (corresponds to GRR_Edit.aspx)
    path('goodsreceivedreceipt/', GoodsReceivedReceiptListView.as_view(), name='goodsreceivedreceipt_list'),
    
    # HTMX endpoint for the GRR table content (for dynamic updates/search)
    path('goodsreceivedreceipt/table/', GoodsReceivedReceiptTablePartialView.as_view(), name='goodsreceivedreceipt_table'),

    # HTMX endpoint for supplier autocomplete functionality
    path('suppliers/autocomplete/', SupplierAutocompleteView.as_view(), name='supplier_autocomplete'),

    # CRUD operations for Goods Received Receipts (via modals)
    path('goodsreceivedreceipt/add/', GoodsReceivedReceiptCreateView.as_view(), name='goodsreceivedreceipt_add'),
    path('goodsreceivedreceipt/edit/<int:pk>/', GoodsReceivedReceiptUpdateView.as_view(), name='goodsreceivedreceipt_edit'),
    path('goodsreceivedreceipt/delete/<int:pk>/', GoodsReceivedReceiptDeleteView.as_view(), name='goodsreceivedreceipt_delete'),
]
```

#### 4.6 Tests

**Task:** Write comprehensive unit tests for the models and integration tests for the views to ensure functionality and coverage.

**Instructions:**

*   Include test cases for model creation, field validation, and any custom model methods.
*   Implement integration tests for GET and POST requests to all views, including HTMX-specific headers and responses.
*   Ensure mock data is set up for database interactions.

**File: `inventory_transactions/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import GoodsReceivedReceipt, Supplier
from .forms import GoodsReceivedReceiptForm, SupplierSearchForm
from datetime import date

class SupplierModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a test supplier for all tests in this class
        cls.supplier = Supplier.objects.create(
            id=1,
            supplier_name='Test Supplier',
            company_id=1
        )
        cls.supplier_comp2 = Supplier.objects.create(
            id=2,
            supplier_name='Another Supplier',
            company_id=2
        )

    def test_supplier_creation(self):
        supplier = Supplier.objects.get(id=1)
        self.assertEqual(supplier.supplier_name, 'Test Supplier')
        self.assertEqual(supplier.company_id, 1)

    def test_supplier_str_representation(self):
        supplier = Supplier.objects.get(id=1)
        self.assertEqual(str(supplier), 'Test Supplier [1]')

    def test_supplier_meta_options(self):
        self.assertEqual(Supplier._meta.db_table, 'tblMM_Supplier_master')
        self.assertFalse(Supplier._meta.managed)
        self.assertEqual(Supplier._meta.verbose_name, 'Supplier')
        self.assertEqual(Supplier._meta.verbose_name_plural, 'Suppliers')

class GoodsReceivedReceiptModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.supplier1 = Supplier.objects.create(id=101, supplier_name='Supplier A', company_id=1)
        cls.supplier2 = Supplier.objects.create(id=102, supplier_name='Supplier B', company_id=1)
        
        # Create test GRR data
        cls.grr1 = GoodsReceivedReceipt.objects.create(
            id=1,
            financial_year_id=2024,
            grr_no='GRR-001',
            system_date=date(2024, 1, 15),
            gin_id=1001,
            gin_no='GIN-001',
            po_no='PO-001',
            supplier_name_denormalized='Supplier A',
            supplier=cls.supplier1,
            challan_no='CH-001',
            challan_date=date(2024, 1, 10),
            company_id=1
        )
        cls.grr2 = GoodsReceivedReceipt.objects.create(
            id=2,
            financial_year_id=2024,
            grr_no='GRR-002',
            system_date=date(2024, 2, 20),
            gin_id=1002,
            gin_no='GIN-002',
            po_no='PO-002',
            supplier_name_denormalized='Supplier B',
            supplier=cls.supplier2,
            challan_no='CH-002',
            challan_date=date(2024, 2, 18),
            company_id=1
        )
        # GRR for a different company
        cls.grr_other_comp = GoodsReceivedReceipt.objects.create(
            id=3,
            financial_year_id=2024,
            grr_no='GRR-003',
            system_date=date(2024, 3, 1),
            gin_id=1003,
            gin_no='GIN-003',
            po_no='PO-003',
            supplier_name_denormalized='Supplier C',
            supplier=Supplier.objects.create(id=103, supplier_name='Supplier C', company_id=2),
            challan_no='CH-003',
            challan_date=date(2024, 2, 28),
            company_id=2
        )

    def test_grr_creation(self):
        grr = GoodsReceivedReceipt.objects.get(id=1)
        self.assertEqual(grr.grr_no, 'GRR-001')
        self.assertEqual(grr.supplier.supplier_name, 'Supplier A')
        self.assertEqual(grr.company_id, 1)

    def test_grr_str_representation(self):
        grr = GoodsReceivedReceipt.objects.get(id=1)
        self.assertEqual(str(grr), 'GRR-001 - Supplier A')

    def test_grr_manager_filtered_data(self):
        # Test with company_id and financial_year_id
        grrs = GoodsReceivedReceipt.objects.get_filtered_grr_data(company_id=1, financial_year_id=2024)
        self.assertEqual(len(grrs), 2)
        self.assertIn(self.grr1, grrs)
        self.assertIn(self.grr2, grrs)
        self.assertNotIn(self.grr_other_comp, grrs)

        # Test with supplier_id filter
        grrs_filtered_by_supplier = GoodsReceivedReceipt.objects.get_filtered_grr_data(
            company_id=1, financial_year_id=2024, supplier_id=self.supplier1.id
        )
        self.assertEqual(len(grrs_filtered_by_supplier), 1)
        self.assertEqual(grrs_filtered_by_supplier[0], self.grr1)

        # Test no results
        grrs_no_results = GoodsReceivedReceipt.objects.get_filtered_grr_data(company_id=1, financial_year_id=9999)
        self.assertEqual(len(grrs_no_results), 0)

    def test_get_display_date(self):
        self.assertEqual(self.grr1.get_display_date(), '2024-01-15')

    def test_get_supplier_display(self):
        self.assertEqual(self.grr1.get_supplier_display(), 'Supplier A')

    def test_get_absolute_edit_url(self):
        self.assertEqual(self.grr1.get_absolute_edit_url(), reverse('goodsreceivedreceipt_edit', args=[self.grr1.pk]))

    def test_get_absolute_delete_url(self):
        self.assertEqual(self.grr1.get_absolute_delete_url(), reverse('goodsreceivedreceipt_delete', args=[self.grr1.pk]))


class GoodsReceivedReceiptViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.supplier1 = Supplier.objects.create(id=1, supplier_name='Acme Corp', company_id=1)
        cls.supplier2 = Supplier.objects.create(id=2, supplier_name='Beta Inc', company_id=1)
        cls.grr1 = GoodsReceivedReceipt.objects.create(
            id=101, grr_no='GRR/2024/001', system_date=date(2024, 1, 1),
            gin_id=1, gin_no='GIN/2024/001', po_no='PO/2024/001',
            supplier_name_denormalized='Acme Corp', supplier=cls.supplier1,
            challan_no='CH/2024/001', challan_date=date(2024, 1, 1),
            financial_year_id=2024, company_id=1
        )
        cls.grr2 = GoodsReceivedReceipt.objects.create(
            id=102, grr_no='GRR/2024/002', system_date=date(2024, 1, 2),
            gin_id=2, gin_no='GIN/2024/002', po_no='PO/2024/002',
            supplier_name_denormalized='Beta Inc', supplier=cls.supplier2,
            challan_no='CH/2024/002', challan_date=date(2024, 1, 2),
            financial_year_id=2024, company_id=1
        )
    
    def setUp(self):
        self.client = Client()

    # --- GoodsReceivedReceiptListView (Main Page) ---
    def test_grr_list_view_get(self):
        response = self.client.get(reverse('goodsreceivedreceipt_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/goodsreceivedreceipt/list.html')
        self.assertIn('search_form', response.context)
        self.assertIsInstance(response.context['search_form'], SupplierSearchForm)

    # --- GoodsReceivedReceiptTablePartialView (HTMX Table Content) ---
    def test_grr_table_partial_view_get_no_filter(self):
        response = self.client.get(reverse('goodsreceivedreceipt_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/goodsreceivedreceipt/_goodsreceivedreceipt_table.html')
        self.assertIn('goodsreceivedreceipts', response.context)
        self.assertEqual(len(response.context['goodsreceivedreceipts']), 2) # Both GRRs created belong to comp 1, fy 2024

    def test_grr_table_partial_view_get_with_supplier_filter(self):
        response = self.client.get(
            reverse('goodsreceivedreceipt_table'),
            {'supplier_id': self.supplier1.id},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertIn('goodsreceivedreceipts', response.context)
        self.assertEqual(len(response.context['goodsreceivedreceipts']), 1)
        self.assertEqual(response.context['goodsreceivedreceipts'][0], self.grr1)

    # --- SupplierAutocompleteView (HTMX Autocomplete) ---
    def test_supplier_autocomplete_view(self):
        response = self.client.get(
            reverse('supplier_autocomplete'),
            {'q': 'Acme'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/goodsreceivedreceipt/_supplier_autocomplete_results.html')
        self.assertContains(response, 'Acme Corp')
        self.assertNotContains(response, 'Beta Inc')

    def test_supplier_autocomplete_view_no_query(self):
        response = self.client.get(
            reverse('supplier_autocomplete'),
            {'q': ''},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content.decode(), '') # Expect empty string

    # --- GoodsReceivedReceiptCreateView ---
    def test_grr_create_view_get(self):
        response = self.client.get(reverse('goodsreceivedreceipt_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/goodsreceivedreceipt/_goodsreceivedreceipt_form.html')
        self.assertIn('form', response.context)

    def test_grr_create_view_post_success(self):
        data = {
            'grr_no': 'GRR/NEW/001',
            'system_date': '2024-03-01',
            'gin_id': 3,
            'gin_no': 'GIN/NEW/001',
            'po_no': 'PO/NEW/001',
            'supplier': self.supplier1.id,
            'challan_no': 'CH/NEW/001',
            'challan_date': '2024-02-28',
            # financial_year_id and company_id are set in view's form_valid
        }
        response = self.client.post(reverse('goodsreceivedreceipt_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshGoodsReceivedReceiptList', response.headers['HX-Trigger'])
        self.assertTrue(GoodsReceivedReceipt.objects.filter(grr_no='GRR/NEW/001').exists())

    def test_grr_create_view_post_invalid(self):
        data = { # Missing required fields, invalid date
            'grr_no': '', 
            'system_date': 'invalid-date',
            'supplier': self.supplier1.id,
        }
        response = self.client.post(reverse('goodsreceivedreceipt_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX returns form with errors
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Enter a valid date.')
        self.assertFalse(GoodsReceivedReceipt.objects.filter(grr_no='').exists())

    # --- GoodsReceivedReceiptUpdateView ---
    def test_grr_update_view_get(self):
        response = self.client.get(reverse('goodsreceivedreceipt_edit', args=[self.grr1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/goodsreceivedreceipt/_goodsreceivedreceipt_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.grr1)

    def test_grr_update_view_post_success(self):
        updated_data = {
            'grr_no': 'GRR/UPDATED/001',
            'system_date': '2024-01-01',
            'gin_id': 1,
            'gin_no': 'GIN/2024/001',
            'po_no': 'PO/2024/001',
            'supplier': self.supplier1.id,
            'challan_no': 'CH/2024/001',
            'challan_date': '2024-01-01',
        }
        response = self.client.post(reverse('goodsreceivedreceipt_edit', args=[self.grr1.pk]), updated_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.grr1.refresh_from_db()
        self.assertEqual(self.grr1.grr_no, 'GRR/UPDATED/001')

    # --- GoodsReceivedReceiptDeleteView ---
    def test_grr_delete_view_get(self):
        response = self.client.get(reverse('goodsreceivedreceipt_delete', args=[self.grr1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/goodsreceivedreceipt/_goodsreceivedreceipt_confirm_delete.html')
        self.assertIn('goodsreceivedreceipt', response.context)
        self.assertEqual(response.context['goodsreceivedreceipt'], self.grr1)

    def test_grr_delete_view_post_success(self):
        pk_to_delete = self.grr2.pk
        response = self.client.delete(reverse('goodsreceivedreceipt_delete', args=[pk_to_delete]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertFalse(GoodsReceivedReceipt.objects.filter(pk=pk_to_delete).exists())

    def test_grr_delete_view_post_non_existent(self):
        response = self.client.delete(reverse('goodsreceivedreceipt_delete', args=[999]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 404) # Not Found
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX:**
    *   The main list page (`list.html`) uses `hx-get` to load the table content (`_goodsreceivedreceipt_table.html`) on initial page load and after every search/CRUD operation (triggered by `HX-Trigger`).
    *   The search button uses `hx-get` to reload the table with filter parameters.
    *   The supplier search input uses `hx-get` to fetch autocomplete suggestions, targeting `_supplier_autocomplete_results.html`.
    *   "Add New", "Edit", and "Delete" buttons use `hx-get` to fetch their respective forms/confirmations into the modal (`#modalContent`).
    *   Form submissions (`_goodsreceivedreceipt_form.html`) use `hx-post` or `hx-delete` with `hx-swap="none"` and `hx-on::after-request` to close the modal and `HX-Trigger` to refresh the table.
    *   `htmx-indicator` classes are used for visual feedback during AJAX requests.
*   **Alpine.js:**
    *   Used for simple UI state management, primarily for showing/hiding the modal. The `_="on click add .is-active to #modal"` and `_="on click remove .is-active from me"` (`_hyperscript`) provides this.
*   **DataTables:**
    *   Initialized on the `_goodsreceivedreceipt_table.html` partial once it's loaded into the DOM. The `htmx:afterSwap` event listener in `list.html` ensures DataTables is initialized correctly every time the table content is refreshed.
    *   Provides client-side searching, sorting, and pagination for the GRR list.

### Final Notes

This comprehensive plan covers the migration from the ASP.NET Goods Received Receipt search/list page to a modern Django application. It incorporates the "fat model, thin view" principle, extensive use of HTMX for dynamic interactions, DataTables for enhanced data presentation, and a clear, automated conversion strategy. The focus is on providing a maintainable, scalable, and user-friendly solution, clearly outlining the business benefits of moving to a modern framework.

By following these steps, organizations can systematically transition their legacy ASP.NET applications to a modern Django-based architecture, reducing technical debt, improving performance, and enabling faster feature development.