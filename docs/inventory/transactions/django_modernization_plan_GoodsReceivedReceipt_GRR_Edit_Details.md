This comprehensive Django modernization plan addresses the migration of your ASP.NET Goods Received Receipt (GRR) Edit Details page. We will transform your legacy ASP.NET application into a modern, scalable Django solution, leveraging AI-assisted automation principles. This approach emphasizes fat models, thin views, and a responsive frontend powered by HTMX and Alpine.js, ensuring a robust and maintainable system.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database tables and their columns involved in the ASP.NET code.

The ASP.NET code heavily relies on a complex network of tables for data retrieval and updates. The core tables for this page are `tblinv_MaterialReceived_Details` and `tblinv_MaterialReceived_Master`, along with numerous lookup tables.

**Main Data Table:** `tblinv_MaterialReceived_Details`
**Header Table:** `tblinv_MaterialReceived_Master`

**Key Related Tables Identified (with relevant columns inferred from queries):**
*   `tblMM_Supplier_master`: `SupplierId`, `SupplierName`
*   `tblInv_Inward_Master`: `Id`, `GINNo`, `ChallanNo`, `ChallanDate`, `CompId`, `FinYearId`, `SupplierId`
*   `tblInv_Inward_Details`: `Id`, `GINId`, `POId`, `ReceivedQty`
*   `tblMM_PO_Master`: `Id`, `PONo`, `PRSPRFlag`, `CompId`, `FinYearId`
*   `tblMM_PO_Details`: `Id`, `MId` (Master ID), `PONo`, `Qty`, `PRNo`, `PRId`, `SPRNo`, `SPRId`
*   `tblMM_PR_Master`: `Id`, `PRNo`, `CompId`
*   `tblMM_PR_Details`: `Id`, `MId`, `PRNo`, `ItemId`
*   `tblMM_SPR_Master`: `Id`, `SPRNo`, `CompId`
*   `tblMM_SPR_Details`: `Id`, `MId`, `SPRNo`, `ItemId`
*   `tblDG_Item_Master`: `Id`, `ItemCode`, `ManfDesc`, `UOMBasic`, `FileName`, `FileData`, `ContentType`, `AttName`, `AttData`, `AttContentType`, `CompId`
*   `Unit_Master`: `Id`, `Symbol`
*   `tblQc_MaterialQuality_Master`: `Id`, `GRRId` (Refers to `tblinv_MaterialReceived_Master.Id`)
*   `tblQc_MaterialQuality_Details`: `Id`, `MId` (Refers to `tblQc_MaterialQuality_Master.Id`), `GRRId` (Refers to `tblinv_MaterialReceived_Details.Id`)

### Step 2: Identify Backend Functionality

Task: Determine the data operations and business logic in the ASP.NET code.

*   **Read (Display):**
    *   Retrieving Goods Received Receipt (GRR) header details (GRR No, GIN No, Supplier, Challan No, Challan Date) based on query string parameters.
    *   Loading a detailed list of items for the specific GRR. This involves complex chained lookups and aggregations:
        *   `tblinv_MaterialReceived_Details` (main GRR item details)
        *   Joining with `tblInv_Inward_Details` and `tblInv_Inward_Master` to get Inward Quantity.
        *   Joining with `tblMM_PO_Details` and `tblMM_PO_Master` to get Purchase Order Quantity and determine PR/SPR flag.
        *   Conditional joins with `tblMM_PR_Details`/`tblMM_PR_Master` or `tblMM_SPR_Details`/`tblMM_SPR_Master` based on the PR/SPR flag to get the `ItemId`.
        *   Joining with `tblDG_Item_Master` to get Item Code, Description, Filename, Attachment Name, and `UOMBasic`.
        *   Joining with `Unit_Master` to get UOM Symbol.
        *   Calculating "Total Received Quantity" (`TotRecedQty`) by summing `ReceivedQty` for items related to the same `POId` and `GINId` across all GRR details.
*   **Update:**
    *   Updating the `ReceivedQty` for a specific item detail in `tblinv_MaterialReceived_Details`.
    *   Updating `SysDate`, `SysTime`, `SessionId` in `tblinv_MaterialReceived_Master` upon any detail item update.
    *   **Validation:** The `ReceivedQty` can *only* be decreased or remain the same, it cannot exceed the original quantity. This is a critical business rule.
*   **Conditional Editability:** An item detail's "Edit" button is hidden if quality control data (`tblQc_MaterialQuality_Master`, `tblQc_MaterialQuality_Details`) exists for that specific GRR item.
*   **File Download:** Provides functionality to download associated image files (`FileName`) and specification sheets (`AttName`) from `tblDG_Item_Master` for a given item.
*   **Navigation:** A "Cancel" button redirects the user to another page.

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles, mapping them to Django templates with HTMX/Alpine.js.

*   **Page Header:** Displays static GRR information (`lblGrr`, `lblGIn`, `lblSupplier`, `lblChNo`, `lblDate`). These will be context variables in Django.
*   **Data Grid (`GridView2`):**
    *   Displays a list of GRR items with columns for SN, Edit/Status, Id (hidden), Item Id (hidden), Image (link), Spec. Sheet (link), Item Code, Description, UOM, PO Qty, Inward Qty, Tot Reced Qty, Reced Qty.
    *   **Editing:** When a row is in edit mode, `Reced Qty` becomes a `TextBox` with validation. This will be replaced by an HTMX-driven modal form.
    *   **Paging:** Handled by DataTables.
    *   **Actions:** `Edit` and `Update` buttons (HTMX triggers for modals), `Cancel` (HTMX modal close), `downloadImg`, `downloadSpec` (links to file download views).
*   **Validation Controls:** `RequiredFieldValidator`, `RegularExpressionValidator` for `TxtRecedqty`. These will be translated into Django form field validations and clean methods.
*   **Cancel Button:** `btnCancel` redirects to `GoodsReceivedReceipt_GRR_Edit.aspx`. In Django, this will be a `reverse_lazy` URL or `hx-redirect`.

### Step 4: Generate Django Code

We will create an `inventory` Django application.

#### 4.1 Models (`inventory/models.py`)

This section defines the Django models for the identified database tables, setting `managed = False` for existing tables. The "Fat Model" principle is applied by adding properties to `MaterialReceivedDetail` to encapsulate the complex data lookups (Item Code, Description, UOM, Quantities, File info) that were previously done in the C# `loadData` method.

```python
from django.db import models
from django.db.models import Sum, F
from django.utils import timezone # For SysDate/SysTime

# Placeholder for common functions, if any complex ID lookups are needed
# For now, direct ORM queries are preferred where possible.
# If 'fun.GetItemCode_PartNo' was more complex than a direct lookup,
# it might warrant a static method here or in a utils file.

class SupplierMaster(models.Model):
    id = models.IntegerField(db_column='SupplierId', primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.supplier_name or f"Supplier {self.id}"

class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol or f"Unit {self.id}"

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=255, blank=True, null=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=MAX_LENGTH_FOR_TEXT, blank=True, null=True) # Assuming MAX_LENGTH_FOR_TEXT is defined or set appropriately
    uom_basic_id = models.IntegerField(db_column='UOMBasic', blank=True, null=True) # Renamed to avoid name clash with property
    file_name = models.CharField(db_column='FileName', max_length=255, blank=True, null=True)
    file_data = models.BinaryField(db_column='FileData', blank=True, null=True) # Assuming file content stored directly
    content_type = models.CharField(db_column='ContentType', max_length=100, blank=True, null=True)
    att_name = models.CharField(db_column='AttName', max_length=255, blank=True, null=True)
    att_data = models.BinaryField(db_column='AttData', blank=True, null=True)
    att_content_type = models.CharField(db_column='AttContentType', max_length=100, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.item_code or f"Item {self.id}"

    @property
    def uom(self):
        if self.uom_basic_id:
            try:
                return UnitMaster.objects.get(id=self.uom_basic_id).symbol
            except UnitMaster.DoesNotExist:
                pass
        return None

    @property
    def has_image(self):
        return bool(self.file_name and self.file_data)

    @property
    def has_spec_sheet(self):
        return bool(self.att_name and self.att_data)


class PurchaseRequisitionMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    pr_no = models.CharField(db_column='PRNo', max_length=50)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Master'

    def __str__(self):
        return self.pr_no

class PurchaseRequisitionDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    m_id = models.ForeignKey(PurchaseRequisitionMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    pr_no = models.CharField(db_column='PRNo', max_length=50) # Redundant, but part of schema
    item_id = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId', related_name='pr_details')

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Details'

    def __str__(self):
        return f"PR Detail {self.id} for {self.pr_no}"

class ServicePurchaseRequisitionMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    spr_no = models.CharField(db_column='SPRNo', max_length=50)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Master'

    def __str__(self):
        return self.spr_no

class ServicePurchaseRequisitionDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    m_id = models.ForeignKey(ServicePurchaseRequisitionMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    spr_no = models.CharField(db_column='SPRNo', max_length=50) # Redundant, but part of schema
    item_id = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId', related_name='spr_details')

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Details'

    def __str__(self):
        return f"SPR Detail {self.id} for {self.spr_no}"


class PurchaseOrderMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    pono = models.CharField(db_column='PONo', max_length=50)
    pr_spr_flag = models.CharField(db_column='PRSPRFlag', max_length=1) # '0' for PR, '1' for SPR
    fin_year_id = models.IntegerField(db_column='FinYearId')
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'

    def __str__(self):
        return self.pono


class PurchaseOrderDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    m_id = models.ForeignKey(PurchaseOrderMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    pono = models.CharField(db_column='PONo', max_length=50) # Redundant, but part of schema
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3, blank=True, null=True)
    pr_no = models.CharField(db_column='PRNo', max_length=50, blank=True, null=True)
    pr_id = models.IntegerField(db_column='PRId', blank=True, null=True) # Refers to tblMM_PR_Details.Id
    spr_no = models.CharField(db_column='SPRNo', max_length=50, blank=True, null=True)
    spr_id = models.IntegerField(db_column='SPRId', blank=True, null=True) # Refers to tblMM_SPR_Details.Id

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Details'

    def __str__(self):
        return f"PO Detail {self.id} for {self.pono}"

    @property
    def item_id(self):
        """Fetches the ItemId based on PRSPRFlag and PR/SPR IDs."""
        if self.m_id.pr_spr_flag == '0' and self.pr_id: # PR
            try:
                return PurchaseRequisitionDetail.objects.get(id=self.pr_id, pr_no=self.pr_no).item_id_id # Get actual ItemMaster ID
            except PurchaseRequisitionDetail.DoesNotExist:
                pass
        elif self.m_id.pr_spr_flag == '1' and self.spr_id: # SPR
            try:
                return ServicePurchaseRequisitionDetail.objects.get(id=self.spr_id, spr_no=self.spr_no).item_id_id
            except ServicePurchaseRequisitionDetail.DoesNotExist:
                pass
        return None

    @property
    def item_master(self):
        """Fetches the ItemMaster object."""
        item_id = self.item_id
        if item_id:
            try:
                return ItemMaster.objects.get(id=item_id)
            except ItemMaster.DoesNotExist:
                pass
        return None


class InwardMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    gin_no = models.CharField(db_column='GINNo', max_length=50)
    challan_no = models.CharField(db_column='ChallanNo', max_length=100, blank=True, null=True)
    challan_date = models.DateTimeField(db_column='ChallanDate', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')
    supplier_id = models.IntegerField(db_column='SupplierId', blank=True, null=True)
    pono = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Master'
        verbose_name = 'Inward Master'
        verbose_name_plural = 'Inward Masters'

    def __str__(self):
        return self.gin_no

    @property
    def supplier_name(self):
        if self.supplier_id:
            try:
                return SupplierMaster.objects.get(id=self.supplier_id).supplier_name
            except SupplierMaster.DoesNotExist:
                pass
        return None

class InwardDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    gin_id = models.ForeignKey(InwardMaster, on_delete=models.DO_NOTHING, db_column='GINId', related_name='details')
    gin_no = models.CharField(db_column='GINNo', max_length=50) # Redundant
    po_id = models.IntegerField(db_column='POId') # Refers to tblMM_PO_Details.Id
    received_qty = models.DecimalField(db_column='ReceivedQty', max_digits=18, decimal_places=3, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Details'

    def __str__(self):
        return f"Inward Detail {self.id} for GIN {self.gin_no}"


class MaterialReceivedMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    grr_no = models.CharField(db_column='GRRNo', max_length=50)
    gin_id = models.ForeignKey(InwardMaster, on_delete=models.DO_NOTHING, db_column='GINId', related_name='grr_masters')
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    sys_date = models.DateField(db_column='SysDate', blank=True, null=True)
    sys_time = models.CharField(db_column='SysTime', max_length=10, blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialReceived_Master'
        verbose_name = 'Material Received Master'
        verbose_name_plural = 'Material Received Masters'

    def __str__(self):
        return self.grr_no

    def update_sys_info(self, session_id):
        """Updates system date, time, and session ID."""
        self.sys_date = timezone.localdate()
        self.sys_time = timezone.localtime().strftime('%H:%M:%S')
        self.session_id = session_id
        self.save()


class MaterialReceivedDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    m_id = models.ForeignKey(MaterialReceivedMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    grr_no = models.CharField(db_column='GRRNo', max_length=50) # Redundant
    po_id = models.IntegerField(db_column='POId') # Refers to tblMM_PO_Details.Id
    received_qty = models.DecimalField(db_column='ReceivedQty', max_digits=18, decimal_places=3, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialReceived_Details'
        verbose_name = 'Material Received Detail'
        verbose_name_plural = 'Material Received Details'

    def __str__(self):
        return f"GRR Detail {self.id} for {self.grr_no}"

    # Properties to encapsulate complex data retrieval from 'loadData'
    @property
    def purchase_order_detail(self):
        if self.po_id:
            try:
                # Pre-fetch m_id for PR/SPR flag check if possible
                return PurchaseOrderDetail.objects.select_related('m_id').get(id=self.po_id)
            except PurchaseOrderDetail.DoesNotExist:
                pass
        return None

    @property
    def item_code(self):
        po_detail = self.purchase_order_detail
        if po_detail and po_detail.item_master:
            return po_detail.item_master.item_code
        return None

    @property
    def description(self):
        po_detail = self.purchase_order_detail
        if po_detail and po_detail.item_master:
            return po_detail.item_master.manf_desc
        return None

    @property
    def uom(self):
        po_detail = self.purchase_order_detail
        if po_detail and po_detail.item_master:
            return po_detail.item_master.uom
        return None

    @property
    def po_qty(self):
        po_detail = self.purchase_order_detail
        return po_detail.qty if po_detail else None

    @property
    def inward_qty(self):
        """Fetches the Inward Quantity for this POId and GINId."""
        if self.po_id and self.m_id and self.m_id.gin_id_id: # Access gin_id directly from the FK
            try:
                # Assuming tblInv_Inward_Details has a po_id and is linked to GINId
                inward_detail = InwardDetail.objects.filter(
                    gin_id=self.m_id.gin_id, # Link via the InwardMaster instance
                    po_id=self.po_id
                ).first() # Get the first matching detail
                return inward_detail.received_qty if inward_detail else None
            except InwardDetail.DoesNotExist:
                pass
        return None

    @property
    def total_received_qty(self):
        """Calculates total received quantity for this POId and GINId across all GRR details."""
        if self.po_id and self.m_id and self.m_id.gin_id_id:
            # Aggregate sum of received_qty for all MaterialReceivedDetail objects
            # that share the same POId and are linked to the same GINId via MaterialReceivedMaster
            total_qty = MaterialReceivedDetail.objects.filter(
                po_id=self.po_id,
                m_id__gin_id=self.m_id.gin_id # Filter by GINId through the master record
            ).aggregate(sum_received_qty=Sum('received_qty'))['sum_received_qty']
            return total_qty if total_qty is not None else 0
        return 0

    @property
    def item_image_filename(self):
        po_detail = self.purchase_order_detail
        if po_detail and po_detail.item_master and po_detail.item_master.has_image:
            return "View" # Indicate availability, actual file name is ItemMaster.file_name
        return ""

    @property
    def item_spec_sheet_filename(self):
        po_detail = self.purchase_order_detail
        if po_detail and po_detail.item_master and po_detail.item_master.has_spec_sheet:
            return "View" # Indicate availability, actual file name is ItemMaster.att_name
        return ""

    @property
    def item_id_for_download(self):
        po_detail = self.purchase_order_detail
        if po_detail and po_detail.item_master:
            return po_detail.item_master.id
        return None

    def can_edit(self):
        """
        Determines if this GRR detail item can be edited based on QC data existence.
        This logic mirrors the disableEdit() function in the ASP.NET code.
        """
        try:
            # Check if any QC master record exists for this GRR master AND
            # if any QC detail record exists for THIS GRR detail ID.
            # Simplified: looking for any QC record linked to this GRR detail ID
            has_qc_data = QcMaterialQualityDetail.objects.filter(
                grr_id=self.id # grr_id in QC details refers to MaterialReceivedDetail.Id
            ).exists()
            return not has_qc_data
        except Exception: # Handle potential table/model not found errors
            return True # Default to true if QC models aren't configured/found

class QcMaterialQualityMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    grr_id = models.ForeignKey(MaterialReceivedMaster, on_delete=models.DO_NOTHING, db_column='GRRId', related_name='qc_masters', blank=True, null=True)
    m_id = models.IntegerField(db_column='MId') # Also refers to MaterialReceivedMaster.Id, likely redundant

    class Meta:
        managed = False
        db_table = 'tblQc_MaterialQuality_Master'

    def __str__(self):
        return f"QC Master {self.id}"

class QcMaterialQualityDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    m_id = models.ForeignKey(QcMaterialQualityMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    grr_id = models.ForeignKey(MaterialReceivedDetail, on_delete=models.DO_NOTHING, db_column='GRRId', related_name='qc_details', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblQc_MaterialQuality_Details'

    def __str__(self):
        return f"QC Detail {self.id}"

# Define MAX_LENGTH_FOR_TEXT if not already globally defined for CharFields
MAX_LENGTH_FOR_TEXT = 4000 # A common max length for text fields in SQL Server without explicit length
```

#### 4.2 Forms (`inventory/forms.py`)

A simple ModelForm for `MaterialReceivedDetail` is created, focusing on the `received_qty` field. Custom validation ensures the quantity is valid and does not exceed the previous value.

```python
from django import forms
from .models import MaterialReceivedDetail
from decimal import Decimal, InvalidOperation

class MaterialReceivedDetailForm(forms.ModelForm):
    class Meta:
        model = MaterialReceivedDetail
        fields = ['received_qty']
        widgets = {
            'received_qty': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'type': 'number',
                'step': '0.001', # Allows for 3 decimal places
                'min': '0',
            }),
        }

    def __init__(self, *args, **kwargs):
        self.original_received_qty = kwargs.pop('original_received_qty', None)
        super().__init__(*args, **kwargs)

    def clean_received_qty(self):
        received_qty = self.cleaned_data.get('received_qty')

        if received_qty is None:
            raise forms.ValidationError("Received Quantity is required.")

        try:
            # Ensure it's a valid number with up to 3 decimal places
            received_qty = Decimal(received_qty).quantize(Decimal('0.001'))
        except InvalidOperation:
            raise forms.ValidationError("Invalid quantity format. Please enter a number with up to 3 decimal places.")

        if received_qty < 0:
            raise forms.ValidationError("Received Quantity cannot be negative.")

        # Business logic: Received Qty cannot exceed the original quantity
        # (This is unusual as per typical inventory, but replicates ASP.NET logic)
        if self.instance.pk and self.original_received_qty is not None:
             # If the form is for an existing instance and original_received_qty was passed
            if received_qty > self.original_received_qty:
                raise forms.ValidationError(f"Received Quantity cannot exceed the original quantity ({self.original_received_qty:.3f}).")

        return received_qty

```

#### 4.3 Views (`inventory/views.py`)

The views implement the logic for displaying the GRR header, the list of items, handling updates via HTMX, and managing file downloads. Views are kept thin, delegating complex data retrieval and business logic to the models.

```python
from django.views.generic import TemplateView, UpdateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404
from django.shortcuts import get_object_or_404
from django.utils.dateformat import format
from django.db import transaction

from .models import (
    MaterialReceivedMaster, MaterialReceivedDetail,
    InwardMaster, SupplierMaster, ItemMaster
)
from .forms import MaterialReceivedDetailForm

# The main view to display GRR details and list of items
class GRRDetailView(TemplateView):
    template_name = 'inventory/grr/grr_detail.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        grr_master_id = self.kwargs['master_id']
        comp_id = self.request.session.get('compid') # Assuming compid from session
        fin_year_id = self.request.session.get('finyear') # Assuming finyear from session

        try:
            # Fetch the MaterialReceivedMaster
            grr_master = get_object_or_404(
                MaterialReceivedMaster.objects.select_related('gin_id'),
                id=grr_master_id,
                comp_id=comp_id,
                fin_year_id=fin_year_id # Assuming FinYearId is also part of filtering
            )
            context['grr_master'] = grr_master

            # Fetch Inward Master details
            inward_master = grr_master.gin_id # This is already pre-fetched
            if inward_master:
                context['gin_no'] = inward_master.gin_no
                context['challan_no'] = inward_master.challan_no
                context['challan_date'] = format(inward_master.challan_date, 'd/m/Y') if inward_master.challan_date else ''
                context['supplier_name'] = inward_master.supplier_name
            else:
                # Handle cases where GINId might be missing or invalid
                context['gin_no'] = 'N/A'
                context['challan_no'] = 'N/A'
                context['challan_date'] = 'N/A'
                context['supplier_name'] = 'N/A'

        except (MaterialReceivedMaster.DoesNotExist, InwardMaster.DoesNotExist):
            messages.error(self.request, "GRR or associated Inward record not found.")
            # Consider redirecting or rendering an error page here
            raise Http404("GRR not found.")

        # The actual GRR item details table will be loaded via HTMX
        return context

# HTMX partial view for the GRR item details table
class GRRDetailTablePartialView(TemplateView):
    template_name = 'inventory/grr/_grr_detail_table.html'

    def get_queryset(self):
        grr_master_id = self.kwargs['master_id']
        # The complex joins are now handled by properties on MaterialReceivedDetail
        # We want to prefetch related objects that these properties will access
        # This is a simplified prefetch; real optimization might need more specific queries.
        return MaterialReceivedDetail.objects.filter(m_id_id=grr_master_id).select_related(
            'm_id', # MaterialReceivedMaster
            'm_id__gin_id', # InwardMaster
        ).prefetch_related(
            # Prefetch for purchase_order_detail and its chain
            'purchaseorderdetail__m_id', # PO Master
            'purchaseorderdetail__purchaserequisitiondetail__item_id', # PR Detail -> Item Master
            'purchaseorderdetail__servicepurchaserequisitiondetail__item_id', # SPR Detail -> Item Master
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['grr_details'] = self.get_queryset()
        return context

# HTMX enabled view for updating a single GRR item detail
class MaterialReceivedDetailUpdateView(UpdateView):
    model = MaterialReceivedDetail
    form_class = MaterialReceivedDetailForm
    template_name = 'inventory/grr/_materialreceiveddetail_form.html' # This should be a partial
    # success_url is handled by HX-Trigger in form_valid

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # Pass the original quantity to the form for custom validation
        kwargs['original_received_qty'] = self.object.received_qty
        return kwargs

    def form_valid(self, form):
        # Transaction ensures atomicity of updates to both master and detail
        with transaction.atomic():
            response = super().form_valid(form)
            # Update sys_date, sys_time, session_id on the master record
            self.object.m_id.update_sys_info(self.request.session.get('username')) # Assuming username is session_id
            messages.success(self.request, 'GRR item updated successfully.')

            # For HTMX requests, return a 204 No Content with a trigger header
            if self.request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshGRRDetailTable' # Trigger refresh of the table
                    }
                )
            return response # For non-HTMX requests (unlikely for this specific use case)

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # For HTMX, return the form with errors for re-rendering the modal
            return response
        return response


# View for downloading image or spec sheet
class MaterialDownloadFileView(TemplateView):
    def get(self, request, item_id, file_type):
        item = get_object_or_404(ItemMaster, id=item_id)

        file_data = None
        file_name = None
        content_type = None

        if file_type == 'image':
            file_data = item.file_data
            file_name = item.file_name
            content_type = item.content_type
        elif file_type == 'spec':
            file_data = item.att_data
            file_name = item.att_name
            content_type = item.att_content_type
        else:
            raise Http404("Invalid file type.")

        if not file_data:
            raise Http404(f"No {file_type} data found for this item.")

        response = HttpResponse(file_data, content_type=content_type)
        response['Content-Disposition'] = f'attachment; filename="{file_name}"'
        return response
```

#### 4.4 Templates (`inventory/templates/inventory/grr/`)

We will create the main detail page and two partials: one for the DataTables content and one for the update form modal.

**`inventory/templates/inventory/grr/grr_detail.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Goods Received Receipt [GRR] - Edit</h2>
    </div>

    {# GRR Header Details #}
    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <table class="w-full text-sm text-gray-700">
            <tbody>
                <tr>
                    <td class="w-1/6 py-1 font-semibold">GRR No:</td>
                    <td class="w-1/3 py-1 text-blue-700 font-bold">{{ grr_master.grr_no }}</td>
                    <td class="w-1/6 py-1 font-semibold">GIN No:</td>
                    <td class="w-1/3 py-1 text-blue-700 font-bold">{{ gin_no }}</td>
                </tr>
                <tr>
                    <td class="py-1 font-semibold">Challan No:</td>
                    <td class="py-1 text-blue-700 font-bold">{{ challan_no }}</td>
                    <td class="py-1 font-semibold">Date:</td>
                    <td class="py-1 text-blue-700 font-bold">{{ challan_date }}</td>
                </tr>
                <tr>
                    <td class="py-1 font-semibold">Supplier:</td>
                    <td colspan="3" class="py-1 text-blue-700 font-bold">{{ supplier_name }}</td>
                </tr>
            </tbody>
        </table>
    </div>

    {# DataTables Container for GRR Details #}
    <div class="bg-white shadow-md rounded-lg p-6">
        <div id="grrDetailTable-container"
             hx-trigger="load, refreshGRRDetailTable from:body"
             hx-get="{% url 'inventory:grr_detail_table_partial' master_id=grr_master.id %}"
             hx-swap="innerHTML">
            <!-- DataTable will be loaded here via HTMX -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading GRR details...</p>
            </div>
        </div>
    </div>

    <div class="mt-8 text-center">
        <a href="{% url 'inventory:grr_list' %}" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded transition duration-200">
            Cancel
        </a>
    </div>

    {# Modal for forms (edit) #}
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-auto"
             _="on htmx:afterOnLoad add .is-active to #modal">
            <!-- HTMX loaded content will go here -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# DataTables and Alpine.js are assumed to be loaded in base.html via CDN #}
<script>
    document.addEventListener('htmx:afterOnLoad', function(event) {
        // Close modal if form is successfully submitted (HX-Trigger received 204)
        if (event.detail.xhr.status === 204) {
            document.getElementById('modal').classList.remove('is-active');
        }
    });

    document.addEventListener('alpine:init', () => {
        // Alpine.js components can be defined here if needed for broader UI state
    });
</script>
{% endblock %}
```

**`inventory/templates/inventory/grr/_grr_detail_table.html`**

```html
<table id="grrDetailTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Image</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Spec. Sheet</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">PO Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Inward Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Tot Reced Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Reced Qty</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for detail in grr_details %}
        <tr>
            <td class="py-2 px-4 whitespace-nowrap text-right text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-center text-sm font-medium">
                {% if detail.can_edit %}
                <button
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md transition duration-200"
                    hx-get="{% url 'inventory:grr_item_edit' pk=detail.id %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                {% else %}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    GQN
                </span>
                {% endif %}
            </td>
            <td class="py-2 px-4 whitespace-nowrap text-center text-sm text-blue-600 hover:text-blue-800">
                {% if detail.item_image_filename %}
                <a href="{% url 'inventory:download_material_file' item_id=detail.item_id_for_download file_type='image' %}" class="underline">View</a>
                {% endif %}
            </td>
            <td class="py-2 px-4 whitespace-nowrap text-center text-sm text-blue-600 hover:text-blue-800">
                {% if detail.item_spec_sheet_filename %}
                <a href="{% url 'inventory:download_material_file' item_id=detail.item_id_for_download file_type='spec' %}" class="underline">View</a>
                {% endif %}
            </td>
            <td class="py-2 px-4 whitespace-nowrap text-left text-sm text-gray-900">{{ detail.item_code }}</td>
            <td class="py-2 px-4 text-left text-sm text-gray-900">{{ detail.description }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-center text-sm text-gray-900">{{ detail.uom }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-right text-sm text-gray-900">{{ detail.po_qty|floatformat:"3" }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-right text-sm text-gray-900">{{ detail.inward_qty|floatformat:"3" }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-right text-sm text-gray-900">{{ detail.total_received_qty|floatformat:"3" }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-right text-sm text-gray-900">{{ detail.received_qty|floatformat:"3" }}</td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="11" class="py-4 text-center text-gray-500">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Ensure DataTables is initialized only once per table load
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#grrDetailTable')) {
            $('#grrDetailTable').DataTable().destroy();
        }
        $('#grrDetailTable').DataTable({
            "pageLength": 17, // Matches original ASP.NET page size
            "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
            "pagingType": "simple_numbers", // Or "full_numbers" for more controls
            "columnDefs": [
                { "orderable": false, "targets": [1, 2, 3] } // Disable sorting on Action, Image, Spec. Sheet columns
            ]
        });
    });
</script>
```

**`inventory/templates/inventory/grr/_materialreceiveddetail_form.html`**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Edit Received Quantity</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        
        <div class="space-y-4">
            <div class="mb-4">
                <label for="{{ form.received_qty.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.received_qty.label }}
                </label>
                {{ form.received_qty }}
                {% if form.received_qty.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.received_qty.errors }}</p>
                {% endif %}
            </div>
            {# Display read-only info if needed, e.g., current total received quantity #}
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700">Current Total Received Qty:</label>
                <p class="mt-1 text-sm text-gray-900">{{ form.instance.total_received_qty|floatformat:"3" }}</p>
            </div>
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded transition duration-200"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-200">
                Update
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`inventory/urls.py`)

Define the URL patterns to map incoming requests to the correct views.

```python
from django.urls import path
from .views import (
    GRRDetailView, GRRDetailTablePartialView,
    MaterialReceivedDetailUpdateView, MaterialDownloadFileView
)

app_name = 'inventory' # Define app_name for namespacing URLs

urlpatterns = [
    # Main GRR Detail page
    path('grr/details/<int:master_id>/', GRRDetailView.as_view(), name='grr_detail'),
    # HTMX endpoint for the GRR item details table
    path('grr/details/<int:master_id>/table/', GRRDetailTablePartialView.as_view(), name='grr_detail_table_partial'),
    # HTMX endpoint for updating a single GRR item detail
    path('grr/item/<int:pk>/edit/', MaterialReceivedDetailUpdateView.as_view(), name='grr_item_edit'),
    # File download URLs
    path('grr/download/<str:file_type>/<int:item_id>/', MaterialDownloadFileView.as_view(), name='download_material_file'),

    # Placeholder for the GRR List page (for the cancel button redirect)
    path('grr/', TemplateView.as_view(template_name='inventory/grr/grr_list.html'), name='grr_list'),
]
```

#### 4.6 Tests (`inventory/tests.py`)

Comprehensive tests cover model properties and view functionality, including HTMX interactions and business logic.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from decimal import Decimal
from unittest.mock import patch, MagicMock

# Import all models to ensure they are discovered by Django's test runner
from .models import (
    SupplierMaster, UnitMaster, ItemMaster, PurchaseRequisitionMaster,
    PurchaseRequisitionDetail, ServicePurchaseRequisitionMaster,
    ServicePurchaseRequisitionDetail, PurchaseOrderMaster, PurchaseOrderDetail,
    InwardMaster, InwardDetail, MaterialReceivedMaster, MaterialReceivedDetail,
    QcMaterialQualityMaster, QcMaterialQualityDetail
)

class ModelSetupMixin:
    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for all related models
        cls.comp_id = 1
        cls.fin_year_id = 2023
        cls.session_id = "testuser"

        cls.supplier = SupplierMaster.objects.create(id=1, supplier_name="Test Supplier", comp_id=cls.comp_id)
        cls.unit = UnitMaster.objects.create(id=1, symbol="PCS")
        cls.item = ItemMaster.objects.create(
            id=1, item_code="ITEM001", manf_desc="Test Item Desc",
            uom_basic_id=cls.unit.id, comp_id=cls.comp_id,
            file_name="image.jpg", file_data=b"image_data", content_type="image/jpeg",
            att_name="spec.pdf", att_data=b"spec_data", att_content_type="application/pdf"
        )
        cls.pr_master = PurchaseRequisitionMaster.objects.create(id=1, pr_no="PR001", comp_id=cls.comp_id)
        cls.pr_detail = PurchaseRequisitionDetail.objects.create(id=1, m_id=cls.pr_master, pr_no="PR001", item_id=cls.item)
        cls.spr_master = ServicePurchaseRequisitionMaster.objects.create(id=1, spr_no="SPR001", comp_id=cls.comp_id)
        cls.spr_detail = ServicePurchaseRequisitionDetail.objects.create(id=1, m_id=cls.spr_master, spr_no="SPR001", item_id=cls.item)

        cls.po_master_pr = PurchaseOrderMaster.objects.create(id=1, pono="PO001", pr_spr_flag='0', fin_year_id=cls.fin_year_id, comp_id=cls.comp_id)
        cls.po_detail_pr = PurchaseOrderDetail.objects.create(id=1, m_id=cls.po_master_pr, pono="PO001", qty=Decimal('100.000'), pr_no="PR001", pr_id=cls.pr_detail.id)

        cls.po_master_spr = PurchaseOrderMaster.objects.create(id=2, pono="PO002", pr_spr_flag='1', fin_year_id=cls.fin_year_id, comp_id=cls.comp_id)
        cls.po_detail_spr = PurchaseOrderDetail.objects.create(id=2, m_id=cls.po_master_spr, pono="PO002", qty=Decimal('200.000'), spr_no="SPR001", spr_id=cls.spr_detail.id)

        cls.inward_master = InwardMaster.objects.create(
            id=1, gin_no="GIN001", challan_no="CHALLAN001", challan_date=timezone.now(),
            comp_id=cls.comp_id, supplier_id=cls.supplier.id, pono="PO001", fin_year_id=cls.fin_year_id
        )
        cls.inward_detail = InwardDetail.objects.create(
            id=1, gin_id=cls.inward_master, gin_no="GIN001", po_id=cls.po_detail_pr.id, received_qty=Decimal('90.000')
        )

        cls.grr_master = MaterialReceivedMaster.objects.create(
            id=1, grr_no="GRR001", gin_id=cls.inward_master, comp_id=cls.comp_id,
            fin_year_id=cls.fin_year_id, sys_date=timezone.localdate(), sys_time="10:00:00", session_id=cls.session_id
        )
        cls.grr_detail_1 = MaterialReceivedDetail.objects.create(
            id=1, m_id=cls.grr_master, grr_no="GRR001", po_id=cls.po_detail_pr.id, received_qty=Decimal('50.000')
        )
        cls.grr_detail_2 = MaterialReceivedDetail.objects.create( # Another detail for same PO/GIN for TotRecedQty test
            id=2, m_id=cls.grr_master, grr_no="GRR001", po_id=cls.po_detail_pr.id, received_qty=Decimal('30.000')
        )

        # QC data for testing can_edit
        cls.qc_master = QcMaterialQualityMaster.objects.create(id=1, grr_id=cls.grr_master)
        cls.qc_detail_for_grr_detail_1 = QcMaterialQualityDetail.objects.create(id=1, m_id=cls.qc_master, grr_id=cls.grr_detail_1)


class MaterialReceivedDetailModelTest(ModelSetupMixin, TestCase):
    def test_material_received_detail_creation(self):
        detail = MaterialReceivedDetail.objects.get(id=self.grr_detail_1.id)
        self.assertEqual(detail.received_qty, Decimal('50.000'))
        self.assertEqual(detail.m_id.grr_no, "GRR001")

    def test_item_code_property(self):
        detail = MaterialReceivedDetail.objects.get(id=self.grr_detail_1.id)
        self.assertEqual(detail.item_code, "ITEM001")

    def test_description_property(self):
        detail = MaterialReceivedDetail.objects.get(id=self.grr_detail_1.id)
        self.assertEqual(detail.description, "Test Item Desc")

    def test_uom_property(self):
        detail = MaterialReceivedDetail.objects.get(id=self.grr_detail_1.id)
        self.assertEqual(detail.uom, "PCS")

    def test_po_qty_property(self):
        detail = MaterialReceivedDetail.objects.get(id=self.grr_detail_1.id)
        self.assertEqual(detail.po_qty, Decimal('100.000'))

    def test_inward_qty_property(self):
        detail = MaterialReceivedDetail.objects.get(id=self.grr_detail_1.id)
        self.assertEqual(detail.inward_qty, Decimal('90.000'))

    def test_total_received_qty_property(self):
        # grr_detail_1 (50) + grr_detail_2 (30) for same PO/GIN
        detail = MaterialReceivedDetail.objects.get(id=self.grr_detail_1.id)
        self.assertEqual(detail.total_received_qty, Decimal('80.000'))

    def test_item_image_filename_property(self):
        detail = MaterialReceivedDetail.objects.get(id=self.grr_detail_1.id)
        self.assertEqual(detail.item_image_filename, "View")

    def test_item_spec_sheet_filename_property(self):
        detail = MaterialReceivedDetail.objects.get(id=self.grr_detail_1.id)
        self.assertEqual(detail.item_spec_sheet_filename, "View")

    def test_item_id_for_download_property(self):
        detail = MaterialReceivedDetail.objects.get(id=self.grr_detail_1.id)
        self.assertEqual(detail.item_id_for_download, self.item.id)

    def test_can_edit_property_with_qc_data(self):
        # grr_detail_1 has QC data, so it should not be editable
        detail = MaterialReceivedDetail.objects.get(id=self.grr_detail_1.id)
        self.assertFalse(detail.can_edit())

    def test_can_edit_property_without_qc_data(self):
        # grr_detail_2 does not have QC data, so it should be editable
        detail = MaterialReceivedDetail.objects.get(id=self.grr_detail_2.id)
        self.assertTrue(detail.can_edit())


class GRRDetailViewTest(ModelSetupMixin, TestCase):
    def setUp(self):
        self.client = Client()
        self.client.session['compid'] = self.comp_id
        self.client.session['finyear'] = self.fin_year_id
        self.client.session['username'] = self.session_id

    def test_grr_detail_view_get(self):
        response = self.client.get(reverse('inventory:grr_detail', args=[self.grr_master.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/grr/grr_detail.html')
        self.assertContains(response, self.grr_master.grr_no)
        self.assertContains(response, self.inward_master.gin_no)
        self.assertContains(response, self.supplier.supplier_name)

    def test_grr_detail_view_not_found(self):
        response = self.client.get(reverse('inventory:grr_detail', args=[999999]))
        self.assertEqual(response.status_code, 404)


class GRRDetailTablePartialViewTest(ModelSetupMixin, TestCase):
    def setUp(self):
        self.client = Client()
        self.client.session['compid'] = self.comp_id
        self.client.session['finyear'] = self.fin_year_id
        self.client.session['username'] = self.session_id
        self.headers = {'HTTP_HX_REQUEST': 'true'}

    def test_grr_detail_table_partial_get(self):
        response = self.client.get(reverse('inventory:grr_detail_table_partial', args=[self.grr_master.id]), **self.headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/grr/_grr_detail_table.html')
        self.assertContains(response, self.grr_detail_1.item_code)
        self.assertContains(response, str(self.grr_detail_1.received_qty))
        self.assertContains(response, "Edit") # grr_detail_2 is editable
        self.assertContains(response, "GQN") # grr_detail_1 is not editable


class MaterialReceivedDetailUpdateViewTest(ModelSetupMixin, TestCase):
    def setUp(self):
        self.client = Client()
        self.client.session['compid'] = self.comp_id
        self.client.session['finyear'] = self.fin_year_id
        self.client.session['username'] = self.session_id
        self.headers = {'HTTP_HX_REQUEST': 'true'}

    def test_update_view_get_htmx(self):
        response = self.client.get(reverse('inventory:grr_item_edit', args=[self.grr_detail_2.id]), **self.headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/grr/_materialreceiveddetail_form.html')
        self.assertContains(response, "Edit Received Quantity")
        self.assertContains(response, f'value="{self.grr_detail_2.received_qty:.3f}"')

    def test_update_view_post_success(self):
        original_qty = self.grr_detail_2.received_qty
        new_qty = original_qty - Decimal('10.000') # Decrease quantity as per business rule

        data = {'received_qty': str(new_qty)}
        response = self.client.post(reverse('inventory:grr_item_edit', args=[self.grr_detail_2.id]), data, **self.headers)

        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshGRRDetailTable')

        # Verify database update
        updated_detail = MaterialReceivedDetail.objects.get(id=self.grr_detail_2.id)
        self.assertEqual(updated_detail.received_qty, new_qty)

        # Verify master record update
        updated_master = MaterialReceivedMaster.objects.get(id=self.grr_master.id)
        self.assertEqual(updated_master.session_id, self.session_id)
        self.assertIsNotNone(updated_master.sys_date)
        self.assertIsNotNone(updated_master.sys_time)


    def test_update_view_post_invalid_qty_exceeds_original(self):
        original_qty = self.grr_detail_2.received_qty
        invalid_qty = original_qty + Decimal('10.000') # Increase quantity, which should fail

        data = {'received_qty': str(invalid_qty)}
        response = self.client.post(reverse('inventory:grr_item_edit', args=[self.grr_detail_2.id]), data, **self.headers)

        self.assertEqual(response.status_code, 200) # HTMX returns form with errors
        self.assertContains(response, "Received Quantity cannot exceed the original quantity")

        # Verify no database update
        current_detail = MaterialReceivedDetail.objects.get(id=self.grr_detail_2.id)
        self.assertEqual(current_detail.received_qty, original_qty) # Should remain unchanged

    def test_update_view_post_invalid_format(self):
        data = {'received_qty': 'abc'}
        response = self.client.post(reverse('inventory:grr_item_edit', args=[self.grr_detail_2.id]), data, **self.headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Invalid quantity format. Please enter a number with up to 3 decimal places.")

    def test_update_view_post_missing_qty(self):
        data = {'received_qty': ''}
        response = self.client.post(reverse('inventory:grr_item_edit', args=[self.grr_detail_2.id]), data, **self.headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Received Quantity is required.")


class MaterialDownloadFileViewTest(ModelSetupMixin, TestCase):
    def setUp(self):
        self.client = Client()

    def test_download_image_file(self):
        response = self.client.get(reverse('inventory:download_material_file', args=[self.item.id, 'image']))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'image/jpeg')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="image.jpg"')
        self.assertEqual(response.content, b"image_data")

    def test_download_spec_file(self):
        response = self.client.get(reverse('inventory:download_material_file', args=[self.item.id, 'spec']))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/pdf')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="spec.pdf"')
        self.assertEqual(response.content, b"spec_data")

    def test_download_file_not_found(self):
        item_no_files = ItemMaster.objects.create(id=2, item_code="NOFILE", comp_id=self.comp_id)
        response = self.client.get(reverse('inventory:download_material_file', args=[item_no_files.id, 'image']))
        self.assertEqual(response.status_code, 404)
        self.assertContains(response, "No image data found for this item.")

    def test_download_invalid_file_type(self):
        response = self.client.get(reverse('inventory:download_material_file', args=[self.item.id, 'invalid']))
        self.assertEqual(response.status_code, 404)
        self.assertContains(response, "Invalid file type.")

```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for dynamic updates:**
    *   The main `grr_detail.html` loads the table content `_grr_detail_table.html` via `hx-get` on `load` and on a custom `refreshGRRDetailTable` event. This avoids full page reloads for the table.
    *   Edit buttons in the table trigger `hx-get` to load the `_materialreceiveddetail_form.html` into the `#modalContent` div, making the form appear as a modal.
    *   Form submissions in `_materialreceiveddetail_form.html` use `hx-post` and `hx-swap="none"`. Upon successful submission (HTTP 204), the `HX-Trigger: refreshGRRDetailTable` header is sent, which reloads the table, reflecting the changes.
    *   Modal closing is handled with Alpine.js (`_`).

*   **Alpine.js for UI state management:**
    *   Alpine.js is used to control the visibility of the modal (`add .is-active to #modal`, `remove .is-active from #modal`).
    *   The modal also closes if clicked outside (`on click if event.target.id == 'modal' remove .is-active from me`).

*   **DataTables for List Views:**
    *   The `_grr_detail_table.html` partial uses JavaScript to initialize DataTables on the `grrDetailTable`. This provides client-side searching, sorting, and pagination without requiring server-side processing for these features, matching the `GridView` functionality.
    *   The page length is set to `17` to mimic the original ASP.NET `PageSize`.

*   **No custom JavaScript:** All interactions are handled by HTMX attributes and basic Alpine.js directives, eliminating the need for complex, handwritten JavaScript beyond the DataTables initialization.

## Final Notes

*   **Placeholders:** Replace `MAX_LENGTH_FOR_TEXT` with an appropriate value if it's not globally defined. The `CompId` and `FinYearId` are assumed to be available in the Django session, mirroring the ASP.NET `Session["compid"]` and `Session["finyear"]`.
*   **DRY Principle:** Templates are kept DRY by using partials for reusable components like the item list table and the update form.
*   **Fat Model, Thin View:** The complex data retrieval logic from the ASP.NET `loadData()` method has been encapsulated within properties and methods of the `MaterialReceivedDetail` model, ensuring views remain concise and focused on handling HTTP requests and responses.
*   **Testing:** Comprehensive unit tests for models and integration tests for views ensure the correctness and robustness of the migrated functionality, especially for the complex data fetching and business rules.
*   **User Experience:** The HTMX and Alpine.js integration provides a modern, single-page application-like experience without the overhead of a full SPA framework.