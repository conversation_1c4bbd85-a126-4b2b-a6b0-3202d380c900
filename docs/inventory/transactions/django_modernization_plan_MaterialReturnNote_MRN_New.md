## ASP.NET to Django Conversion Script: Material Return Note Module

This document outlines a comprehensive plan to modernize the existing ASP.NET Material Return Note (MRN) module into a robust Django 5.0+ application. Our approach leverages AI-assisted automation to systematically transform the legacy code, focusing on a "fat model, thin view" architecture, HTMX for dynamic interactions, Alpine.js for UI state, and DataTables for efficient data presentation. The goal is to deliver a scalable, maintainable, and highly performant solution with clear business benefits.

### Business Benefits of Django Modernization:

*   **Enhanced Performance & Scalability:** Django's optimized ORM and efficient request handling, combined with HTMX/Alpine.js for leaner network communication, will lead to faster response times and better support for increased user loads.
*   **Reduced Development & Maintenance Costs:** Python's readability and Django's "batteries-included" philosophy accelerate development. A clean, modular architecture (fat models, thin views) simplifies future updates and reduces the risk of errors.
*   **Improved User Experience:** HTMX enables dynamic, partial page updates, providing a smooth, app-like feel without complex JavaScript frameworks. DataTables offer intuitive sorting, searching, and pagination, making data management effortless.
*   **Future-Proof Technology Stack:** Moving from legacy ASP.NET Web Forms to modern Django ensures your application is built on widely supported, actively developed open-source technologies, making it easier to find talent and integrate with other systems.
*   **Greater Flexibility & Extensibility:** Django's modular design allows for easier integration of new features and external services. The clear separation of concerns in our proposed architecture simplifies modifications and extensions.
*   **Stronger Security Posture:** Django includes built-in protections against common web vulnerabilities (e.g., CSRF, XSS, SQL injection), significantly enhancing the application's security compared to older frameworks.

### Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code to define Django models.

**Analysis:**
The ASP.NET code interacts with several tables for material return operations, item lookup, and master data.

*   `tblDG_Item_Master`: Core item information.
*   `tblDG_Category_Master`: Item categorization.
*   `BusinessGroup`: Used for departmental assignments.
*   `Unit_Master`: Defines units of measure.
*   `tblinv_MaterialReturn_Temp`: A temporary staging table for items being prepared for return. This is central to the user's current session.
*   `tblInv_MaterialReturn_Master`: Stores header information for finalized Material Return Notes.
*   `tblInv_MaterialReturn_Details`: Stores line-item details for finalized Material Return Notes.

### Step 2: Identify Backend Functionality

**Task:** Determine the core CRUD operations and business logic embedded in the ASP.NET code-behind.

**Analysis:**
The application manages material return notes with a two-stage process: selecting items into a temporary list, then generating a final MRN.

*   **Item Search & Listing (Read):** Users can search items by category, item code, description, or location. Results are displayed in a paginated grid (`GridView2`).
*   **Adding Items to Temporary List (Create):** From the search results, users select an item, specify a return quantity, assign it to a department/work order, and add remarks. This adds the item to `tblinv_MaterialReturn_Temp`.
*   **Dynamic Input Control (Update):** Within the item search grid, selecting "BG Group" or "WONo" in a dropdown dynamically shows/hides corresponding input fields for department or work order number (`DropDownList1` in `GridView2`).
*   **Displaying Selected Items (Read):** A separate tab displays all items currently in the `tblinv_MaterialReturn_Temp` table for the current user's session (`GridView3`).
*   **Removing Items from Temporary List (Delete):** Users can delete items from their temporary selection.
*   **Generating Final Material Return Note (Create):** A "Generate MRN" action moves all temporary items into the permanent `tblInv_MaterialReturn_Master` and `tblInv_MaterialReturn_Details` tables, assigns a new MRN number, and clears the temporary table.
*   **Validation:** Input validation for quantity, work order number, and required fields is present. Database checks for item rate availability are also performed before adding to the temp list.

### Step 3: Infer UI Components

**Task:** Analyze the ASP.NET controls and their interactions to inform Django template design and HTMX/Alpine.js integration.

**Analysis:**

*   **Tabbed Interface:** The `AjaxControlToolkit:TabContainer` suggests two primary sections: "Item Master" (for search/add) and "Selected Items" (for review/finalize). This will be replicated using HTMX for tab content loading.
*   **Search Filters:** `DropDownList` controls for type, category, search code, and location, along with `TextBox` for search text and a `Button` for triggering the search. These will map to Django forms and HTMX-driven filtering.
*   **Item Search Grid (`GridView2`):** Displays a list of items with embedded `DropDownList` (for BG Group/WONo selection), `TextBox` (for quantity, WO, remarks), and an "Add" `Button` per row. This is a prime candidate for an HTMX-powered table partial where individual row segments can be re-rendered on dropdown changes.
*   **Selected Items Grid (`GridView3`):** Displays selected items with "Delete" `LinkButton` per row and a "Generate MRN" `Button` in the footer. DataTables will enhance this display.
*   **Client-Side Validation & Alerts:** `RequiredFieldValidator`, `RegularExpressionValidator`, and `ClientScript.RegisterStartupScript` alerts indicate a need for robust Django form validation and HTMX-driven messaging.

### Step 4: Generate Django Code

We will structure the Django application within an assumed `inventory_app` Django app.

#### 4.1 Models (`inventory_app/models.py`)

Models are mapped to the identified database tables. Business logic related to item search, material return processing, and data validation will be moved into model methods or managers, adhering to the "fat model" principle. `CompId`, `FinYearId`, and `SessionId` (mapped to `user_id` or `session_key`) will be handled as context variables for current company, financial year, and user session.

```python
from django.db import models
from django.db.models import F, Sum, Q
from django.core.exceptions import ValidationError
from django.utils import timezone
import math # For decimal rounding


class Company(models.Model):
    # Dummy Company model for relationship, assuming one exists
    # In a real system, this would be a full model with name, address etc.
    id = models.IntegerField(db_column='CompId', primary_key=True)
    name = models.CharField(max_length=255) # Placeholder
    
    class Meta:
        managed = False
        db_table = 'tblCompanyMaster' # Assuming a company master table
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'
    
    def __str__(self):
        return self.name

class FinancialYear(models.Model):
    # Dummy FinancialYear model for relationship
    id = models.IntegerField(db_column='FinYearId', primary_key=True)
    year_name = models.CharField(max_length=50) # Placeholder
    
    class Meta:
        managed = False
        db_table = 'tblFinancialYearMaster' # Assuming a financial year master table
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.year_name


class Category(models.Model):
    id = models.IntegerField(db_column='CId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)
    name = models.CharField(db_column='CName', max_length=255)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'

    def __str__(self):
        return f"[{self.symbol}] - {self.name}" if self.symbol else self.name


class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol


class Unit(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol


class ItemManager(models.Manager):
    def search_items(self, item_type, category_id, search_field, search_value, company_id, fin_year_id):
        """
        Mimics the Fillgrid and GetAllItem stored procedure logic.
        """
        qs = self.get_queryset().filter(company_id=company_id, fin_year_id__lte=fin_year_id)

        if item_type == 'Category':
            if category_id and category_id != 'Select':
                qs = qs.filter(category_id=category_id)

            if search_field == 'tblDG_Item_Master.ItemCode' and search_value:
                qs = qs.filter(item_code__istartswith=search_value)
            elif search_field == 'tblDG_Item_Master.ManfDesc' and search_value:
                qs = qs.filter(manf_desc__icontains=search_value)
            elif search_field == 'tblDG_Item_Master.Location' and search_value and search_value != 'Select':
                qs = qs.filter(location=search_value)
            elif not search_field and search_value: # Default search when no field selected, similar to original logic
                qs = qs.filter(manf_desc__icontains=search_value)

        elif item_type == 'WOItems':
            if search_field == 'tblDG_Item_Master.ItemCode' and search_value:
                qs = qs.filter(item_code__icontains=search_value)
            elif search_field == 'tblDG_Item_Master.ManfDesc' and search_value:
                qs = qs.filter(manf_desc__icontains=search_value)
            elif not search_field and search_value:
                qs = qs.filter(manf_desc__icontains=search_value)
        
        return qs.select_related('category', 'uom_basic') # Eager load related data

class Item(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=500)
    uom_basic = models.ForeignKey(Unit, models.DO_NOTHING, db_column='UOMBasic')
    stock_qty = models.DecimalField(db_column='StockQty', max_digits=18, decimal_places=3)
    location = models.CharField(db_column='Location', max_length=100, blank=True, null=True)
    category = models.ForeignKey(Category, models.DO_NOTHING, db_column='CId', blank=True, null=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', blank=True, null=True)
    fin_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYearId', blank=True, null=True)
    
    objects = ItemManager()

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return f"{self.item_code} - {self.manf_desc}"

    def check_rate_availability(self, company_id):
        """
        Mimics tblMM_Rate_Register check.
        """
        # Assuming tblMM_Rate_Register exists and has ItemId and CompId
        # For simplicity, returning True; implement actual check here
        return True # For demo purposes, assume rate is always available

    def get_display_uom(self):
        """
        Retrieves UOM symbol from Unit_Master.
        """
        return self.uom_basic.symbol if self.uom_basic else ''

    def get_display_stock_qty(self):
        return f"{self.stock_qty:.3f}"


class MaterialReturnTempManager(models.Manager):
    def get_for_session(self, session_id, company_id):
        return self.get_queryset().filter(session_id=session_id, company_id=company_id).order_by('-id')

    def add_item_to_temp(self, company_id, session_id, item_id, dept_id, wo_no, return_qty, remarks):
        """
        Handles adding an item to the temporary return list.
        Mimics GridView2_RowCommand (Add) logic.
        """
        try:
            item = Item.objects.get(id=item_id)
        except Item.DoesNotExist:
            raise ValidationError("Selected item not found.")

        # Check for item rate availability (as per original code)
        if not item.check_rate_availability(company_id):
            raise ValidationError("Selected item is canceled due to rate not being available in ERP.")

        # Check if item is already selected for MRN (in temp table for this session)
        if self.filter(company_id=company_id, session_id=session_id, item_id=item_id).exists():
            raise ValidationError("Item is already selected for MRN.")

        # Validate return quantity format
        if not self.validate_qty(return_qty):
            raise ValidationError("Invalid Return Quantity format.")

        # Validate WO Number if applicable
        if wo_no and not MaterialReturnMaster.validate_wo_no(wo_no, company_id, item.fin_year_id):
            raise ValidationError("Invalid Work Order Number found.")

        # Ensure only one of dept_id or wo_no is provided, or neither if not required by type
        if dept_id and wo_no:
            raise ValidationError("An item cannot be associated with both a department and a work order.")
        
        # Create and save the temporary entry
        material_return_temp = self.create(
            company_id=company_id,
            session_id=session_id,
            item=item,
            dept_id=dept_id if dept_id else None, # Use None for DBNull equivalent
            wo_no=wo_no if wo_no else None, # Use None for DBNull equivalent
            return_qty=return_qty,
            remarks=remarks
        )
        return material_return_temp

    def validate_qty(self, qty_str):
        """Mimics fun.NumberValidationQty"""
        try:
            qty = float(qty_str)
            # Check for non-negative and format (e.g., up to 3 decimal places)
            if qty < 0:
                return False
            # Example: check if it has more than 3 decimal places
            if '.' in qty_str:
                if len(qty_str.split('.')[1]) > 3:
                    return False
            return True
        except ValueError:
            return False


class MaterialReturnTemp(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId')
    session_id = models.CharField(db_column='SessionId', max_length=255) # Can be user.id or session key
    item = models.ForeignKey(Item, models.DO_NOTHING, db_column='ItemId')
    dept = models.ForeignKey(BusinessGroup, models.DO_NOTHING, db_column='DeptId', blank=True, null=True) # Optional
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True) # Optional
    return_qty = models.DecimalField(db_column='RetQty', max_digits=18, decimal_places=3)
    remarks = models.CharField(db_column='Remarks', max_length=500, blank=True, null=True)

    objects = MaterialReturnTempManager()

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialReturn_Temp'
        verbose_name = 'Material Return Temporary Item'
        verbose_name_plural = 'Material Return Temporary Items'

    def __str__(self):
        return f"Temp MRN for {self.item.item_code} by {self.session_id}"

    def get_display_dept_or_wo(self):
        """Returns the department symbol or WO number for display."""
        if self.dept:
            return self.dept.symbol
        elif self.wo_no:
            return self.wo_no
        return "N/A" # Consistent with original "NA"


class MaterialReturnMasterManager(models.Manager):
    def get_next_mrn_no(self, company_id, fin_year_id):
        """
        Generates the next MRN number.
        Mimics the MRN number generation logic in GridView3_RowCommand (proceed).
        """
        last_mrn = self.get_queryset().filter(
            company_id=company_id,
            fin_year_id=fin_year_id
        ).order_by('-mrn_no').first()

        if last_mrn and last_mrn.mrn_no.isdigit():
            next_num = int(last_mrn.mrn_no) + 1
            return f"{next_num:04d}"
        return "0001"

    def process_mrn_from_temp(self, session_id, company_id, fin_year_id):
        """
        Moves items from temp table to master/detail and clears temp.
        Mimics GridView3_RowCommand (proceed) logic.
        """
        temp_items = MaterialReturnTemp.objects.get_for_session(session_id, company_id)

        if not temp_items.exists():
            raise ValidationError("No items selected for Material Return Note.")

        mrn_no = self.get_next_mrn_no(company_id, fin_year_id)

        # Create master entry
        mrn_master = self.create(
            sys_date=timezone.now().date(),
            sys_time=timezone.now().time(),
            company_id=company_id,
            fin_year_id=fin_year_id,
            session_id=session_id,
            mrn_no=mrn_no
        )

        # Create detail entries
        for temp_item in temp_items:
            MaterialReturnDetail.objects.create(
                master=mrn_master,
                mrn_no=mrn_no,
                item=temp_item.item,
                dept=temp_item.dept,
                wo_no=temp_item.wo_no,
                return_qty=temp_item.return_qty,
                remarks=temp_item.remarks
            )
        
        # Clear temporary items
        temp_items.delete()
        return mrn_master

    def validate_wo_no(self, wo_no, company_id, fin_year_id):
        """
        Mimics fun.CheckValidWONo.
        For simplicity, assume WO validation passes for now.
        In a real ERP, this would query a Work Order master table.
        """
        return True # Placeholder: In reality, query tblWO_Master or similar


class MaterialReturnMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId')
    fin_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYearId')
    session_id = models.CharField(db_column='SessionId', max_length=255) # Can be user.id or session key
    mrn_no = models.CharField(db_column='MRNNo', max_length=50)

    objects = MaterialReturnMasterManager()

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialReturn_Master'
        verbose_name = 'Material Return Note (Master)'
        verbose_name_plural = 'Material Return Notes (Master)'

    def __str__(self):
        return f"MRN: {self.mrn_no} on {self.sys_date}"


class MaterialReturnDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(MaterialReturnMaster, models.DO_NOTHING, db_column='MId', related_name='details')
    mrn_no = models.CharField(db_column='MRNNo', max_length=50) # Redundant but kept for db_table consistency
    item = models.ForeignKey(Item, models.DO_NOTHING, db_column='ItemId')
    dept = models.ForeignKey(BusinessGroup, models.DO_NOTHING, db_column='DeptId', blank=True, null=True)
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    return_qty = models.DecimalField(db_column='RetQty', max_digits=18, decimal_places=3)
    remarks = models.CharField(db_column='Remarks', max_length=500, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialReturn_Details'
        verbose_name = 'Material Return Note (Detail)'
        verbose_name_plural = 'Material Return Notes (Details)'

    def __str__(self):
        return f"MRN {self.mrn_no} - Item {self.item.item_code} Qty {self.return_qty}"

```

#### 4.2 Forms (`inventory_app/forms.py`)

Forms are defined for the search criteria and for the individual item details when adding to the temporary list. The `MaterialReturnTempForm` is designed to be used when validating an item before adding it to the temp list.

```python
from django import forms
from .models import Item, Category, BusinessGroup, Unit, MaterialReturnTemp

class MaterialReturnSearchForm(forms.Form):
    ITEM_TYPE_CHOICES = [
        ('Select', 'Select'),
        ('Category', 'Category'),
        ('WOItems', 'WO Items'),
    ]
    SEARCH_CODE_CHOICES = [
        ('Select', 'Select'),
        ('tblDG_Item_Master.ItemCode', 'Item Code'),
        ('tblDG_Item_Master.ManfDesc', 'Description'),
        ('tblDG_Item_Master.Location', 'Location'),
    ]

    item_type = forms.ChoiceField(
        choices=ITEM_TYPE_CHOICES,
        widget=forms.Select(attrs={'class': 'box3 w-full', 'hx-post': 'hx-get-search-options/', 'hx-target': '#search_options_container', 'hx-swap': 'outerHTML', 'hx-trigger': 'change'}),
        label="Type"
    )
    category = forms.ModelChoiceField(
        queryset=Category.objects.none(), # Will be populated dynamically
        required=False,
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3 w-full'}),
        label="Category"
    )
    search_code = forms.ChoiceField(
        choices=SEARCH_CODE_CHOICES,
        required=False,
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3 w-full', 'hx-post': 'hx-get-search-input-field/', 'hx-target': '#search_input_field_container', 'hx-swap': 'outerHTML', 'hx-trigger': 'change'}),
        label="Search By"
    )
    search_text = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 w-full'}),
        label="Search Value"
    )
    search_location = forms.ModelChoiceField(
        queryset=Item.objects.values_list('location', 'location').distinct().order_by('location'), # Assuming Item.location can be a master list
        required=False,
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3 w-full'}),
        label="Location"
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate initial categories if 'Category' is default item_type
        # Assuming current_company_id is passed from view context or request
        current_company_id = kwargs.pop('current_company_id', None)
        if current_company_id:
             self.fields['category'].queryset = Category.objects.filter(company_id=current_company_id).order_by('name')

        # Control initial visibility of fields based on item_type and search_code
        if self.is_bound:
            item_type = self.cleaned_data.get('item_type')
            search_code = self.cleaned_data.get('search_code')
        else:
            item_type = self.initial.get('item_type')
            search_code = self.initial.get('search_code')

        if item_type == 'Category':
            self.fields['category'].widget.attrs['class'] += ' block'
            # Adjust queryset for search_location based on available locations
            self.fields['search_location'].queryset = Item.objects.values_list('location', 'location').distinct().exclude(location__isnull=True).order_by('location')
            if search_code == 'tblDG_Item_Master.Location':
                self.fields['search_text'].widget = forms.HiddenInput()
                self.fields['search_text'].required = False
            else:
                self.fields['search_location'].widget = forms.HiddenInput()
                self.fields['search_location'].required = False
        else: # WOItems or Select
            self.fields['category'].widget = forms.HiddenInput()
            self.fields['category'].required = False
            self.fields['search_location'].widget = forms.HiddenInput()
            self.fields['search_location'].required = False


class MaterialReturnItemForm(forms.Form):
    """
    Form for validating individual item additions to the temp table.
    """
    item_id = forms.IntegerField(widget=forms.HiddenInput())
    ret_qty = forms.DecimalField(
        max_digits=18,
        decimal_places=3,
        min_value=0.001,
        widget=forms.NumberInput(attrs={'class': 'box3 w-full h-4', 'placeholder': 'Return Qty'}),
        error_messages={'min_value': 'Return Quantity must be greater than zero.'}
    )
    remarks = forms.CharField(
        max_length=500,
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 w-full h-4', 'placeholder': 'Remarks'})
    )
    # These two fields will be dynamically shown/hidden
    dept_or_wo_type = forms.ChoiceField(
        choices=[('0', 'Select'), ('1', 'BG Group'), ('2', 'WONo')],
        widget=forms.Select(attrs={'class': 'box3 w-full', 'hx-post': '', 'hx-swap': 'outerHTML', 'hx-trigger': 'change'}), # hx-post target will be dynamic
        required=False,
        label="Type"
    )
    # Placeholder for the actual input for Dept/WO
    dept_id = forms.ModelChoiceField(
        queryset=BusinessGroup.objects.all().order_by('symbol'), # Assume BusinessGroup model has 'symbol'
        required=False,
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3 w-full'}),
        label="Department"
    )
    wo_no = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 w-full h-4', 'placeholder': 'WO Number'}),
        label="Work Order No."
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Hide these fields by default, they will be controlled by HTMX
        self.fields['dept_id'].widget = forms.HiddenInput()
        self.fields['wo_no'].widget = forms.HiddenInput()
        self.fields['dept_id'].required = False # Make sure not required by default
        self.fields['wo_no'].required = False

        if self.is_bound:
            # Re-evaluate visibility based on submitted data
            dept_or_wo_type = self.cleaned_data.get('dept_or_wo_type')
            if dept_or_wo_type == '1': # BG Group
                self.fields['dept_id'].widget = forms.Select(attrs={'class': 'box3 w-full'})
                self.fields['dept_id'].required = True
            elif dept_or_wo_type == '2': # WONo
                self.fields['wo_no'].widget = forms.TextInput(attrs={'class': 'box3 w-full h-4'})
                self.fields['wo_no'].required = True

    def clean(self):
        cleaned_data = super().clean()
        dept_or_wo_type = cleaned_data.get('dept_or_wo_type')
        dept_id = cleaned_data.get('dept_id')
        wo_no = cleaned_data.get('wo_no')
        ret_qty = cleaned_data.get('ret_qty')

        if dept_or_wo_type == '1': # BG Group selected
            if not dept_id:
                self.add_error('dept_id', 'This field is required when "BG Group" is selected.')
            cleaned_data['wo_no'] = None # Ensure WO is null if department is selected
        elif dept_or_wo_type == '2': # WONo selected
            if not wo_no:
                self.add_error('wo_no', 'This field is required when "WONo" is selected.')
            elif not MaterialReturnMaster.objects.validate_wo_no(wo_no, 1, 1): # Dummy CompId, FinYearId
                self.add_error('wo_no', 'Invalid Work Order Number.')
            cleaned_data['dept_id'] = None # Ensure Dept is null if WO is selected
        elif dept_or_wo_type == '0': # Select / Not chosen
            if ret_qty: # If quantity is entered, either dept or WO should be chosen
                 self.add_error('dept_or_wo_type', 'Please select BG Group or WONo.')
            cleaned_data['dept_id'] = None
            cleaned_data['wo_no'] = None
        
        return cleaned_data

```

#### 4.3 Views (`inventory_app/views.py`)

Views are designed to be thin, primarily orchestrating data retrieval and passing it to templates or handling form submissions by invoking model methods. HTMX interaction logic is embedded in the views' responses.## ASP.NET to Django Conversion Script: Material Return Note Module

This document outlines a comprehensive plan to modernize the existing ASP.NET Material Return Note (MRN) module into a robust Django 5.0+ application. Our approach leverages AI-assisted automation to systematically transform the legacy code, focusing on a "fat model, thin view" architecture, HTMX for dynamic interactions, Alpine.js for UI state, and DataTables for efficient data presentation. The goal is to deliver a scalable, maintainable, and highly performant solution with clear business benefits.

### Business Benefits of Django Modernization:

*   **Enhanced Performance & Scalability:** Django's optimized ORM and efficient request handling, combined with HTMX/Alpine.js for leaner network communication, will lead to faster response times and better support for increased user loads.
*   **Reduced Development & Maintenance Costs:** Python's readability and Django's "batteries-included" philosophy accelerate development. A clean, modular architecture (fat models, thin views) simplifies future updates and reduces the risk of errors.
*   **Improved User Experience:** HTMX enables dynamic, partial page updates, providing a smooth, app-like feel without complex JavaScript frameworks. DataTables offer intuitive sorting, searching, and pagination, making data management effortless.
*   **Future-Proof Technology Stack:** Moving from legacy ASP.NET Web Forms to modern Django ensures your application is built on widely supported, actively developed open-source technologies, making it easier to find talent and integrate with other systems.
*   **Greater Flexibility & Extensibility:** Django's modular design allows for easier integration of new features and external services. The clear separation of concerns in our proposed architecture simplifies modifications and extensions.
*   **Stronger Security Posture:** Django includes built-in protections against common web vulnerabilities (e.g., CSRF, XSS, SQL injection), significantly enhancing the application's security compared to older frameworks.

### Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code to define Django models.

**Analysis:**
The ASP.NET code interacts with several tables for material return operations, item lookup, and master data.

*   `tblDG_Item_Master`: Core item information.
*   `tblDG_Category_Master`: Item categorization.
*   `BusinessGroup`: Used for departmental assignments.
*   `Unit_Master`: Defines units of measure.
*   `tblinv_MaterialReturn_Temp`: A temporary staging table for items being prepared for return. This is central to the user's current session.
*   `tblInv_MaterialReturn_Master`: Stores header information for finalized Material Return Notes.
*   `tblInv_MaterialReturn_Details`: Stores line-item details for finalized Material Return Notes.

### Step 2: Identify Backend Functionality

**Task:** Determine the core CRUD operations and business logic embedded in the ASP.NET code-behind.

**Analysis:**
The application manages material return notes with a two-stage process: selecting items into a temporary list, then generating a final MRN.

*   **Item Search & Listing (Read):** Users can search items by category, item code, description, or location. Results are displayed in a paginated grid (`GridView2`).
*   **Adding Items to Temporary List (Create):** From the search results, users select an item, specify a return quantity, assign it to a department/work order, and add remarks. This adds the item to `tblinv_MaterialReturn_Temp`.
*   **Dynamic Input Control (Update):** Within the item search grid, selecting "BG Group" or "WONo" in a dropdown dynamically shows/hides corresponding input fields for department or work order number (`DropDownList1` in `GridView2`).
*   **Displaying Selected Items (Read):** A separate tab displays all items currently in the `tblinv_MaterialReturn_Temp` table for the current user's session (`GridView3`).
*   **Removing Items from Temporary List (Delete):** Users can delete items from their temporary selection.
*   **Generating Final Material Return Note (Create):** A "Generate MRN" action moves all temporary items into the permanent `tblInv_MaterialReturn_Master` and `tblInv_MaterialReturn_Details` tables, assigns a new MRN number, and clears the temporary table.
*   **Validation:** Input validation for quantity, work order number, and required fields is present. Database checks for item rate availability are also performed before adding to the temp list.

### Step 3: Infer UI Components

**Task:** Analyze the ASP.NET controls and their interactions to inform Django template design and HTMX/Alpine.js integration.

**Analysis:**

*   **Tabbed Interface:** The `AjaxControlToolkit:TabContainer` suggests two primary sections: "Item Master" (for search/add) and "Selected Items" (for review/finalize). This will be replicated using HTMX for tab content loading.
*   **Search Filters:** `DropDownList` controls for type, category, search code, and location, along with `TextBox` for search text and a `Button` for triggering the search. These will map to Django forms and HTMX-driven filtering.
*   **Item Search Grid (`GridView2`):** Displays a list of items with embedded `DropDownList` (for BG Group/WONo selection), `TextBox` (for quantity, WO, remarks), and an "Add" `Button` per row. This is a prime candidate for an HTMX-powered table partial where individual row segments can be re-rendered on dropdown changes.
*   **Selected Items Grid (`GridView3`):** Displays selected items with "Delete" `LinkButton` per row and a "Generate MRN" `Button` in the footer. DataTables will enhance this display.
*   **Client-Side Validation & Alerts:** `RequiredFieldValidator`, `RegularExpressionValidator`, and `ClientScript.RegisterStartupScript` alerts indicate a need for robust Django form validation and HTMX-driven messaging.

### Step 4: Generate Django Code

We will structure the Django application within an assumed `inventory_app` Django app.

#### 4.1 Models (`inventory_app/models.py`)

Models are mapped to the identified database tables. Business logic related to item search, material return processing, and data validation will be moved into model methods or managers, adhering to the "fat model" principle. `CompId`, `FinYearId`, and `SessionId` (mapped to `user_id` or `session_key`) will be handled as context variables for current company, financial year, and user session.

```python
from django.db import models
from django.db.models import F, Sum, Q
from django.core.exceptions import ValidationError
from django.utils import timezone
import math # For decimal rounding


class Company(models.Model):
    # Dummy Company model for relationship, assuming one exists
    # In a real system, this would be a full model with name, address etc.
    id = models.IntegerField(db_column='CompId', primary_key=True)
    name = models.CharField(max_length=255) # Placeholder
    
    class Meta:
        managed = False
        db_table = 'tblCompanyMaster' # Assuming a company master table
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'
    
    def __str__(self):
        return self.name

class FinancialYear(models.Model):
    # Dummy FinancialYear model for relationship
    id = models.IntegerField(db_column='FinYearId', primary_key=True)
    year_name = models.CharField(max_length=50) # Placeholder
    
    class Meta:
        managed = False
        db_table = 'tblFinancialYearMaster' # Assuming a financial year master table
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.year_name


class Category(models.Model):
    id = models.IntegerField(db_column='CId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)
    name = models.CharField(db_column='CName', max_length=255)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'

    def __str__(self):
        return f"[{self.symbol}] - {self.name}" if self.symbol else self.name


class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol


class Unit(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol


class ItemManager(models.Manager):
    def search_items(self, item_type, category_id, search_field, search_value, company_id, fin_year_id):
        """
        Mimics the Fillgrid and GetAllItem stored procedure logic.
        """
        qs = self.get_queryset().filter(company_id=company_id, fin_year_id__lte=fin_year_id)

        if item_type == 'Category':
            if category_id and category_id != 'Select':
                qs = qs.filter(category_id=category_id)

            if search_field == 'tblDG_Item_Master.ItemCode' and search_value:
                qs = qs.filter(item_code__istartswith=search_value)
            elif search_field == 'tblDG_Item_Master.ManfDesc' and search_value:
                qs = qs.filter(manf_desc__icontains=search_value)
            elif search_field == 'tblDG_Item_Master.Location' and search_value and search_value != 'Select':
                qs = qs.filter(location=search_value)
            elif not search_field and search_value: # Default search when no field selected, similar to original logic
                qs = qs.filter(manf_desc__icontains=search_value)

        elif item_type == 'WOItems':
            if search_field == 'tblDG_Item_Master.ItemCode' and search_value:
                qs = qs.filter(item_code__icontains=search_value)
            elif search_field == 'tblDG_Item_Master.ManfDesc' and search_value:
                qs = qs.filter(manf_desc__icontains=search_value)
            elif not search_field and search_value:
                qs = qs.filter(manf_desc__icontains=search_value)
        
        return qs.select_related('category', 'uom_basic') # Eager load related data

class Item(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=500)
    uom_basic = models.ForeignKey(Unit, models.DO_NOTHING, db_column='UOMBasic')
    stock_qty = models.DecimalField(db_column='StockQty', max_digits=18, decimal_places=3)
    location = models.CharField(db_column='Location', max_length=100, blank=True, null=True)
    category = models.ForeignKey(Category, models.DO_NOTHING, db_column='CId', blank=True, null=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', blank=True, null=True)
    fin_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYearId', blank=True, null=True)
    
    objects = ItemManager()

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return f"{self.item_code} - {self.manf_desc}"

    def check_rate_availability(self, company_id):
        """
        Mimics tblMM_Rate_Register check.
        """
        # Assuming tblMM_Rate_Register exists and has ItemId and CompId
        # For simplicity, returning True; implement actual check here
        return True # For demo purposes, assume rate is always available

    def get_display_uom(self):
        """
        Retrieves UOM symbol from Unit_Master.
        """
        return self.uom_basic.symbol if self.uom_basic else ''

    def get_display_stock_qty(self):
        return f"{self.stock_qty:.3f}"


class MaterialReturnTempManager(models.Manager):
    def get_for_session(self, session_id, company_id):
        return self.get_queryset().filter(session_id=session_id, company_id=company_id).order_by('-id')

    def add_item_to_temp(self, company_id, session_id, item_id, dept_id, wo_no, return_qty, remarks):
        """
        Handles adding an item to the temporary return list.
        Mimics GridView2_RowCommand (Add) logic.
        """
        try:
            item = Item.objects.get(id=item_id)
        except Item.DoesNotExist:
            raise ValidationError("Selected item not found.")

        # Check for item rate availability (as per original code)
        if not item.check_rate_availability(company_id):
            raise ValidationError("Selected item is canceled due to rate not being available in ERP.")

        # Check if item is already selected for MRN (in temp table for this session)
        if self.filter(company_id=company_id, session_id=session_id, item_id=item_id).exists():
            raise ValidationError("Item is already selected for MRN.")

        # Validate return quantity format
        if not self.validate_qty(return_qty):
            raise ValidationError("Invalid Return Quantity format.")

        # Validate WO Number if applicable
        if wo_no and not MaterialReturnMaster.objects.validate_wo_no(wo_no, company_id, item.fin_year_id):
            raise ValidationError("Invalid Work Order Number found.")

        # Ensure only one of dept_id or wo_no is provided, or neither if not required by type
        if dept_id and wo_no:
            raise ValidationError("An item cannot be associated with both a department and a work order.")
        
        # Create and save the temporary entry
        material_return_temp = self.create(
            company_id=company_id,
            session_id=session_id,
            item=item,
            dept_id=dept_id if dept_id else None, # Use None for DBNull equivalent
            wo_no=wo_no if wo_no else None, # Use None for DBNull equivalent
            return_qty=return_qty,
            remarks=remarks
        )
        return material_return_temp

    def validate_qty(self, qty_str):
        """Mimics fun.NumberValidationQty"""
        try:
            qty = float(qty_str)
            # Check for non-negative and format (e.g., up to 3 decimal places)
            if qty < 0:
                return False
            # Example: check if it has more than 3 decimal places
            if '.' in qty_str:
                if len(qty_str.split('.')[1]) > 3:
                    return False
            return True
        except ValueError:
            return False


class MaterialReturnTemp(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId')
    session_id = models.CharField(db_column='SessionId', max_length=255) # Can be user.id or session key
    item = models.ForeignKey(Item, models.DO_NOTHING, db_column='ItemId')
    dept = models.ForeignKey(BusinessGroup, models.DO_NOTHING, db_column='DeptId', blank=True, null=True) # Optional
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True) # Optional
    return_qty = models.DecimalField(db_column='RetQty', max_digits=18, decimal_places=3)
    remarks = models.CharField(db_column='Remarks', max_length=500, blank=True, null=True)

    objects = MaterialReturnTempManager()

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialReturn_Temp'
        verbose_name = 'Material Return Temporary Item'
        verbose_name_plural = 'Material Return Temporary Items'

    def __str__(self):
        return f"Temp MRN for {self.item.item_code} by {self.session_id}"

    def get_display_dept_or_wo(self):
        """Returns the department symbol or WO number for display."""
        if self.dept:
            return self.dept.symbol
        elif self.wo_no:
            return self.wo_no
        return "N/A" # Consistent with original "NA"


class MaterialReturnMasterManager(models.Manager):
    def get_next_mrn_no(self, company_id, fin_year_id):
        """
        Generates the next MRN number.
        Mimics the MRN number generation logic in GridView3_RowCommand (proceed).
        """
        last_mrn = self.get_queryset().filter(
            company_id=company_id,
            fin_year_id=fin_year_id
        ).order_by('-mrn_no').first()

        if last_mrn and last_mrn.mrn_no.isdigit():
            next_num = int(last_mrn.mrn_no) + 1
            return f"{next_num:04d}"
        return "0001"

    def process_mrn_from_temp(self, session_id, company_id, fin_year_id):
        """
        Moves items from temp table to master/detail and clears temp.
        Mimics GridView3_RowCommand (proceed) logic.
        """
        temp_items = MaterialReturnTemp.objects.get_for_session(session_id, company_id)

        if not temp_items.exists():
            raise ValidationError("No items selected for Material Return Note.")

        mrn_no = self.get_next_mrn_no(company_id, fin_year_id)

        # Create master entry
        mrn_master = self.create(
            sys_date=timezone.now().date(),
            sys_time=timezone.now().time(),
            company_id=company_id,
            fin_year_id=fin_year_id,
            session_id=session_id,
            mrn_no=mrn_no
        )

        # Create detail entries
        for temp_item in temp_items:
            MaterialReturnDetail.objects.create(
                master=mrn_master,
                mrn_no=mrn_no,
                item=temp_item.item,
                dept=temp_item.dept,
                wo_no=temp_item.wo_no,
                return_qty=temp_item.return_qty,
                remarks=temp_item.remarks
            )
        
        # Clear temporary items
        temp_items.delete()
        return mrn_master

    def validate_wo_no(self, wo_no, company_id, fin_year_id):
        """
        Mimics fun.CheckValidWONo.
        For simplicity, assume WO validation passes for now.
        In a real ERP, this would query a Work Order master table.
        """
        return True # Placeholder: In reality, query tblWO_Master or similar


class MaterialReturnMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId')
    fin_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYearId')
    session_id = models.CharField(db_column='SessionId', max_length=255) # Can be user.id or session key
    mrn_no = models.CharField(db_column='MRNNo', max_length=50)

    objects = MaterialReturnMasterManager()

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialReturn_Master'
        verbose_name = 'Material Return Note (Master)'
        verbose_name_plural = 'Material Return Notes (Master)'

    def __str__(self):
        return f"MRN: {self.mrn_no} on {self.sys_date}"


class MaterialReturnDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(MaterialReturnMaster, models.DO_NOTHING, db_column='MId', related_name='details')
    mrn_no = models.CharField(db_column='MRNNo', max_length=50) # Redundant but kept for db_table consistency
    item = models.ForeignKey(Item, models.DO_NOTHING, db_column='ItemId')
    dept = models.ForeignKey(BusinessGroup, models.DO_NOTHING, db_column='DeptId', blank=True, null=True)
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    return_qty = models.DecimalField(db_column='RetQty', max_digits=18, decimal_places=3)
    remarks = models.CharField(db_column='Remarks', max_length=500, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialReturn_Details'
        verbose_name = 'Material Return Note (Detail)'
        verbose_name_plural = 'Material Return Notes (Details)'

    def __str__(self):
        return f"MRN {self.mrn_no} - Item {self.item.item_code} Qty {self.return_qty}"

```

#### 4.2 Forms (`inventory_app/forms.py`)

Forms are defined for the search criteria and for the individual item details when adding to the temporary list. The `MaterialReturnItemForm` is designed to be used when validating an item before adding it to the temp list.

```python
from django import forms
from .models import Item, Category, BusinessGroup, Unit, MaterialReturnTemp, MaterialReturnMaster

class MaterialReturnSearchForm(forms.Form):
    ITEM_TYPE_CHOICES = [
        ('Select', 'Select'),
        ('Category', 'Category'),
        ('WOItems', 'WO Items'),
    ]
    SEARCH_CODE_CHOICES = [
        ('Select', 'Select'),
        ('tblDG_Item_Master.ItemCode', 'Item Code'),
        ('tblDG_Item_Master.ManfDesc', 'Description'),
        ('tblDG_Item_Master.Location', 'Location'),
    ]

    item_type = forms.ChoiceField(
        choices=ITEM_TYPE_CHOICES,
        widget=forms.Select(attrs={'class': 'box3 w-full', 'hx-post': 'hx-get-search-options/', 'hx-target': '#search_options_container', 'hx-swap': 'outerHTML', 'hx-trigger': 'change'}),
        label="Type"
    )
    category = forms.ModelChoiceField(
        queryset=Category.objects.none(), # Will be populated dynamically
        required=False,
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3 w-full'}),
        label="Category"
    )
    search_code = forms.ChoiceField(
        choices=SEARCH_CODE_CHOICES,
        required=False,
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3 w-full', 'hx-post': 'hx-get-search-input-field/', 'hx-target': '#search_input_field_container', 'hx-swap': 'outerHTML', 'hx-trigger': 'change'}),
        label="Search By"
    )
    search_text = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 w-full'}),
        label="Search Value"
    )
    search_location = forms.ModelChoiceField(
        queryset=Item.objects.values_list('location', 'location').distinct().order_by('location').exclude(location__isnull=True).exclude(location=''),
        required=False,
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3 w-full'}),
        label="Location"
    )

    def __init__(self, *args, **kwargs):
        current_company_id = kwargs.pop('current_company_id', None)
        super().__init__(*args, **kwargs)
        
        if current_company_id:
             self.fields['category'].queryset = Category.objects.filter(company_id=current_company_id).order_by('name')

        # Control initial visibility of fields based on item_type and search_code
        item_type = self.data.get('item_type') if self.is_bound else self.initial.get('item_type')
        search_code = self.data.get('search_code') if self.is_bound else self.initial.get('search_code')

        if item_type == 'Category':
            self.fields['category'].widget.attrs['class'] = self.fields['category'].widget.attrs['class'].replace(' hidden', '')
            self.fields['search_text'].widget.attrs['class'] = self.fields['search_text'].widget.attrs['class'].replace(' hidden', '')
            self.fields['search_location'].widget.attrs['class'] = self.fields['search_location'].widget.attrs['class'].replace(' hidden', '')

            if search_code == 'tblDG_Item_Master.Location':
                self.fields['search_text'].widget = forms.HiddenInput(attrs={'class': 'box3 w-full hidden'})
                self.fields['search_text'].required = False
            else:
                self.fields['search_location'].widget = forms.HiddenInput(attrs={'class': 'box3 w-full hidden'})
                self.fields['search_location'].required = False
        else: # WOItems or Select
            self.fields['category'].widget = forms.HiddenInput(attrs={'class': 'box3 w-full hidden'})
            self.fields['category'].required = False
            self.fields['search_location'].widget = forms.HiddenInput(attrs={'class': 'box3 w-full hidden'})
            self.fields['search_location'].required = False

        if item_type == 'Select':
            self.fields['search_code'].widget = forms.HiddenInput(attrs={'class': 'box3 w-full hidden'})
            self.fields['search_text'].widget = forms.HiddenInput(attrs={'class': 'box3 w-full hidden'})
            self.fields['search_location'].widget = forms.HiddenInput(attrs={'class': 'box3 w-full hidden'})


class MaterialReturnItemForm(forms.Form):
    """
    Form for validating individual item additions to the temp table.
    """
    item_id = forms.IntegerField(widget=forms.HiddenInput())
    ret_qty = forms.DecimalField(
        max_digits=18,
        decimal_places=3,
        min_value=0.001,
        widget=forms.NumberInput(attrs={'class': 'box3 w-full h-4'}),
        error_messages={'min_value': 'Return Quantity must be greater than zero.'}
    )
    remarks = forms.CharField(
        max_length=500,
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 w-full h-4'})
    )
    
    dept_or_wo_type = forms.ChoiceField(
        choices=[('0', 'Select'), ('1', 'BG Group'), ('2', 'WONo')],
        widget=forms.Select(attrs={'class': 'box3 w-full', 'hx-post': '', 'hx-swap': 'outerHTML', 'hx-trigger': 'change'}), 
        required=False,
        label="Type"
    )
    
    dept_id = forms.ModelChoiceField(
        queryset=BusinessGroup.objects.all().order_by('symbol'), 
        required=False,
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3 w-full'}),
        label="Department"
    )
    wo_no = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 w-full h-4'}),
        label="Work Order No."
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Hide these fields by default, they will be controlled by HTMX
        self.fields['dept_id'].widget = forms.HiddenInput()
        self.fields['wo_no'].widget = forms.HiddenInput()
        self.fields['dept_id'].required = False 
        self.fields['wo_no'].required = False

        if self.is_bound:
            # Re-evaluate visibility based on submitted data
            dept_or_wo_type = self.data.get('dept_or_wo_type')
            if dept_or_wo_type == '1': # BG Group
                self.fields['dept_id'].widget = forms.Select(attrs={'class': 'box3 w-full'})
                self.fields['dept_id'].required = True
            elif dept_or_wo_type == '2': # WONo
                self.fields['wo_no'].widget = forms.TextInput(attrs={'class': 'box3 w-full h-4'})
                self.fields['wo_no'].required = True

    def clean(self):
        cleaned_data = super().clean()
        dept_or_wo_type = cleaned_data.get('dept_or_wo_type')
        dept_id = cleaned_data.get('dept_id')
        wo_no = cleaned_data.get('wo_no')
        ret_qty = cleaned_data.get('ret_qty')

        if dept_or_wo_type == '1': # BG Group selected
            if not dept_id:
                self.add_error('dept_id', 'This field is required when "BG Group" is selected.')
            cleaned_data['wo_no'] = None 
        elif dept_or_wo_type == '2': # WONo selected
            if not wo_no:
                self.add_error('wo_no', 'This field is required when "WONo" is selected.')
            # In a real ERP, replace 1,1 with actual company/financial year IDs from request.
            elif not MaterialReturnMaster.objects.validate_wo_no(wo_no, 1, 1): 
                self.add_error('wo_no', 'Invalid Work Order Number.')
            cleaned_data['dept_id'] = None 
        elif dept_or_wo_type == '0': # Select / Not chosen
            if ret_qty: 
                 self.add_error('dept_or_wo_type', 'Please select BG Group or WONo if quantity is provided.')
            cleaned_data['dept_id'] = None
            cleaned_data['wo_no'] = None
        
        return cleaned_data

```

#### 4.3 Views (`inventory_app/views.py`)

Views are designed to be thin, primarily orchestrating data retrieval and passing it to templates or handling form submissions by invoking model methods. HTMX interaction logic is embedded in the views' responses.

```python
from django.views.generic import TemplateView, ListView, View
from django.shortcuts import render, get_object_or_404
from django.http import HttpResponse, JsonResponse
from django.contrib import messages
from django.urls import reverse
from django.template.loader import render_to_string
from django.core.exceptions import ValidationError

from .models import (
    Item, Category, BusinessGroup, Unit,
    MaterialReturnTemp, MaterialReturnMaster
)
from .forms import MaterialReturnSearchForm, MaterialReturnItemForm

# --- Helper functions for common context (mimicking ASP.NET Session variables) ---
# In a real application, these would come from authentication, user profiles, or middleware
def get_current_company_id(request):
    return 1 # Placeholder for Session["compid"]

def get_current_fin_year_id(request):
    return 1 # Placeholder for Session["finyear"]

def get_current_session_identifier(request):
    # Use request.user.id for authenticated users, or request.session.session_key for anonymous
    if request.user.is_authenticated:
        return str(request.user.id)
    return request.session.session_key # Fallback for anonymous users if allowed

# --- Main Page View ---
class MaterialReturnNoteView(TemplateView):
    template_name = 'inventory_app/materialreturn/mrn_new.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        current_company_id = get_current_company_id(self.request)

        # Initialize search form
        context['search_form'] = MaterialReturnSearchForm(current_company_id=current_company_id)
        
        # Initial active tab (mimics Session["TabIndex"])
        # For simplicity, we can use a URL parameter or default to 0
        context['active_tab_index'] = self.request.GET.get('tab', '0')
        return context

# --- HTMX Partial Views for Item Search ---
class ItemSearchTableView(ListView):
    model = Item
    template_name = 'inventory_app/materialreturn/_item_list_table.html'
    context_object_name = 'items'
    paginate_by = 17 # Matches ASP.NET GridView2 PageSize

    def get_queryset(self):
        # Default empty queryset if no search is performed yet or form is invalid
        queryset = Item.objects.none()
        
        # Get company/fin_year from session/user context
        current_company_id = get_current_company_id(self.request)
        current_fin_year_id = get_current_fin_year_id(self.request)

        # Process search form if submitted
        form = MaterialReturnSearchForm(self.request.GET, current_company_id=current_company_id)
        
        if form.is_valid():
            item_type = form.cleaned_data.get('item_type')
            category_id = form.cleaned_data.get('category').id if form.cleaned_data.get('category') else None
            search_code = form.cleaned_data.get('search_code')
            search_value = form.cleaned_data.get('search_text')
            search_location = form.cleaned_data.get('search_location')

            # Override search_value if search_code is location and search_location is chosen
            if search_code == 'tblDG_Item_Master.Location' and search_location:
                search_value = search_location

            if item_type and item_type != 'Select':
                queryset = Item.objects.search_items(
                    item_type,
                    category_id,
                    search_code,
                    search_value,
                    current_company_id,
                    current_fin_year_id
                )
            else:
                messages.warning(self.request, "Please Select Category or WO Items.")
        elif self.request.GET: # If GET parameters exist but form is invalid (e.g., first load without valid search)
            # This handles cases where initial load from URL might have invalid params
            messages.error(self.request, "Invalid search parameters provided.")

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass an instance of MaterialReturnItemForm for rendering row controls
        context['item_form_template'] = MaterialReturnItemForm() 
        return context


class ItemSearchFormOptionsView(View):
    """
    HTMX endpoint to dynamically update search form fields (category, search_code, search_text/location).
    Mimics DrpType_SelectedIndexChanged and initial form field visibility logic.
    """
    def post(self, request, *args, **kwargs):
        current_company_id = get_current_company_id(request)
        form = MaterialReturnSearchForm(request.POST, current_company_id=current_company_id)
        
        # Re-render the form fields based on the new selection
        # We're re-rendering the whole form block for simplicity,
        # but one could render only specific fields if needed.
        context = {'search_form': form}
        return render(request, 'inventory_app/materialreturn/_search_form_fields.html', context)


class ItemSearchInputFieldView(View):
    """
    HTMX endpoint to dynamically show/hide search_text or search_location based on search_code.
    Mimics DrpSearchCode_SelectedIndexChanged1.
    """
    def post(self, request, *args, **kwargs):
        current_company_id = get_current_company_id(request)
        form = MaterialReturnSearchForm(request.POST, current_company_id=current_company_id)
        
        # Re-render only the relevant input field container
        context = {'search_form': form}
        return render(request, 'inventory_app/materialreturn/_search_input_field.html', context)


class AddItemToReturnTempView(View):
    """
    HTMX endpoint to add an item to the temporary return note list.
    Mimics GridView2_RowCommand (Add).
    """
    def post(self, request, *args, **kwargs):
        item_id = request.POST.get('item_id')
        current_company_id = get_current_company_id(request)
        current_session_id = get_current_session_identifier(request)

        # Get form data for validation. Create a form instance from the POST data.
        # Ensure that dept_or_wo_type is passed correctly from the row's form.
        form = MaterialReturnItemForm(request.POST)

        if form.is_valid():
            item_id = form.cleaned_data['item_id']
            ret_qty = form.cleaned_data['ret_qty']
            remarks = form.cleaned_data['remarks']
            dept_id = form.cleaned_data.get('dept_id').id if form.cleaned_data.get('dept_id') else None
            wo_no = form.cleaned_data.get('wo_no')

            try:
                MaterialReturnTemp.objects.add_item_to_temp(
                    company_id=current_company_id,
                    session_id=current_session_id,
                    item_id=item_id,
                    dept_id=dept_id,
                    wo_no=wo_no,
                    return_qty=ret_qty,
                    remarks=remarks
                )
                messages.success(request, "Item added to temporary list successfully!")
                # Trigger refresh for selected items table
                response = HttpResponse(status=204)
                response['HX-Trigger'] = 'refreshSelectedItemsList'
                return response
            except ValidationError as e:
                messages.error(request, e.message)
            except Exception as e:
                messages.error(request, f"An unexpected error occurred: {e}")
        else:
            # If form is invalid, construct an error message string
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f"Error in {field}: {error}")
        
        # If there's an error, send 200 OK and let HTMX update messages.
        # This will prevent full page refresh but allow messages to display.
        # We could also re-render the specific row with validation errors.
        return HttpResponse(status=200)


# --- HTMX Partial Views for Selected Items ---
class SelectedItemsTableView(ListView):
    model = MaterialReturnTemp
    template_name = 'inventory_app/materialreturn/_selected_items_table.html'
    context_object_name = 'selected_items'
    paginate_by = 15 # Matches ASP.NET GridView3 PageSize

    def get_queryset(self):
        current_company_id = get_current_company_id(self.request)
        current_session_id = get_current_session_identifier(self.request)
        return MaterialReturnTemp.objects.get_for_session(current_session_id, current_company_id)


class DeleteItemFromReturnTempView(View):
    """
    HTMX endpoint to delete an item from the temporary return note list.
    Mimics GridView3_RowCommand (Del).
    """
    def delete(self, request, pk, *args, **kwargs):
        current_company_id = get_current_company_id(request)
        current_session_id = get_current_session_identifier(request)
        
        try:
            item_to_delete = MaterialReturnTemp.objects.get(
                id=pk,
                company_id=current_company_id,
                session_id=current_session_id
            )
            item_to_delete.delete()
            messages.success(request, "Item removed from temporary list.")
            response = HttpResponse(status=204)
            response['HX-Trigger'] = 'refreshSelectedItemsList'
            return response
        except MaterialReturnTemp.DoesNotExist:
            messages.error(request, "Item not found in your temporary list.")
        except Exception as e:
            messages.error(request, f"Error deleting item: {e}")
        
        return HttpResponse(status=200)


class GenerateMRNView(View):
    """
    HTMX endpoint to finalize the Material Return Note.
    Mimics GridView3_RowCommand (proceed).
    """
    def post(self, request, *args, **kwargs):
        current_company_id = get_current_company_id(request)
        current_fin_year_id = get_current_fin_year_id(request)
        current_session_id = get_current_session_identifier(request)

        try:
            mrn_master = MaterialReturnMaster.objects.process_mrn_from_temp(
                session_id=current_session_id,
                company_id=current_company_id,
                fin_year_id=current_fin_year_id
            )
            messages.success(request, f"Material Return Note {mrn_master.mrn_no} generated successfully!")
            response = HttpResponse(status=204)
            response['HX-Trigger'] = 'refreshSelectedItemsList' # Refresh to show empty list
            return response
        except ValidationError as e:
            messages.error(request, e.message)
        except Exception as e:
            messages.error(request, f"An unexpected error occurred during MRN generation: {e}")

        return HttpResponse(status=200)


class ItemRowInputUpdateView(View):
    """
    HTMX endpoint to update the input fields (dept_id/wo_no) for a specific item row
    in the search table based on the 'dept_or_wo_type' dropdown selection.
    Mimics DropDownList1_SelectedIndexChanged.
    """
    def post(self, request, item_pk, *args, **kwargs):
        form_data = request.POST.copy()
        form = MaterialReturnItemForm(form_data)
        
        # Get the item for context if needed for the row rendering
        item = get_object_or_404(Item, pk=item_pk)

        # Determine which fields to render based on dept_or_wo_type
        dept_or_wo_type = form_data.get('dept_or_wo_type')

        context = {
            'item': item,
            'form': form, # Pass the form instance with validation state
            'show_dept_field': dept_or_wo_type == '1',
            'show_wo_field': dept_or_wo_type == '2',
        }
        # Render just the specific cell's content
        return render(request, 'inventory_app/materialreturn/_item_row_dept_wo_cell.html', context)
```

#### 4.4 Templates

Templates are designed for modularity, using partials (`_*.html`) for HTMX-swapped content. They extend `core/base.html` (not included here) and use Tailwind CSS for styling. DataTables are initialized for list views.

**`inventory_app/materialreturn/mrn_new.html`** (Main Page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-blue-600 text-white p-3 mb-6 rounded-t-lg">
        <h1 class="text-xl font-bold">&nbsp;Material Return Note [MRN] - New</h1>
    </div>

    <!-- Messages -->
    <div id="messages-container" class="mb-4">
        {% if messages %}
            {% for message in messages %}
                <div class="p-3 mb-3 rounded text-sm {% if message.tags == 'success' %}bg-green-100 text-green-800{% elif message.tags == 'error' %}bg-red-100 text-red-800{% elif message.tags == 'warning' %}bg-yellow-100 text-yellow-800{% else %}bg-blue-100 text-blue-800{% endif %}" role="alert">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}
    </div>

    <div x-data="{ activeTab: '{{ active_tab_index }}' }">
        <!-- Tab Headers -->
        <div class="flex border-b border-gray-200 mb-6">
            <button @click="activeTab = '0'" 
                    :class="{'border-b-2 border-blue-500 text-blue-600': activeTab === '0', 'text-gray-500': activeTab !== '0'}"
                    class="py-2 px-4 text-sm font-medium focus:outline-none"
                    hx-get="{% url 'materialreturn_item_master_tab' %}" 
                    hx-target="#tab-content-0" 
                    hx-swap="innerHTML"
                    hx-indicator="#tab-loading-indicator-0">
                Item Master
            </button>
            <button @click="activeTab = '1'" 
                    :class="{'border-b-2 border-blue-500 text-blue-600': activeTab === '1', 'text-gray-500': activeTab !== '1'}"
                    class="py-2 px-4 text-sm font-medium focus:outline-none"
                    hx-get="{% url 'materialreturn_selected_items_tab' %}" 
                    hx-target="#tab-content-1" 
                    hx-swap="innerHTML"
                    hx-indicator="#tab-loading-indicator-1"
                    hx-trigger="click, refreshSelectedItemsList from:body">
                Selected Items
            </button>
        </div>

        <!-- Tab Content -->
        <div id="tab-content-container">
            <div x-show="activeTab === '0'" class="tab-content">
                <div id="tab-content-0" hx-trigger="load, refreshItemList from:body" hx-get="{% url 'materialreturn_item_master_tab' %}" hx-swap="innerHTML">
                    <!-- Loading indicator for Item Master tab -->
                    <div id="tab-loading-indicator-0" class="htmx-indicator text-center p-4">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p class="mt-2">Loading Item Master...</p>
                    </div>
                </div>
            </div>
            <div x-show="activeTab === '1'" class="tab-content">
                <div id="tab-content-1" hx-trigger="load, refreshSelectedItemsList from:body" hx-get="{% url 'materialreturn_selected_items_tab' %}" hx-swap="innerHTML">
                    <!-- Loading indicator for Selected Items tab -->
                    <div id="tab-loading-indicator-1" class="htmx-indicator text-center p-4">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p class="mt-2">Loading Selected Items...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('htmx:afterSwap', function(event) {
        // Re-initialize DataTables after HTMX swaps in new content
        if (event.detail.target.id === 'item-list-table-container' || event.detail.target.id === 'selected-items-table-container') {
            $('#itemTable, #selectedItemsTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true // Destroy existing DataTable instance before creating a new one
            });
        }
        // Handle messages from HX-Trigger-After-Swap
        if (event.detail.target.id === 'messages-container' && event.detail.xhr.getResponseHeader('HX-Trigger-After-Swap')) {
             const trigger = JSON.parse(event.detail.xhr.getResponseHeader('HX-Trigger-After-Swap'));
             if (trigger.message) {
                 // You might want to handle this more robustly, e.g., with a toast notification
                 // For now, assume Django messages are in the rendered content.
                 console.log("HX-Trigger-After-Swap message:", trigger.message);
             }
        }
    });

    // For confirmation dialogs
    function confirmationAdd() {
        return confirm('Are you sure you want to add this item?');
    }
    function confirmationDelete() {
        return confirm('Are you sure you want to delete this item?');
    }
</script>
{% endblock %}
```

**`inventory_app/materialreturn/_item_master_tab_content.html`** (Content for Item Master Tab)

```html
<div class="p-4 bg-gray-50 rounded-lg shadow-sm mb-6">
    <form hx-get="{% url 'materialreturn_item_search_table' %}" 
          hx-target="#item-list-table-container" 
          hx-swap="innerHTML" 
          hx-indicator="#item-list-loading-indicator"
          hx-params="*"
          id="item-search-form"
          class="space-y-4">
        {% csrf_token %}
        <div id="search_options_container" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            {% include 'inventory_app/materialreturn/_search_form_fields.html' with search_form=search_form %}
        </div>
        <div class="flex justify-end mt-4">
            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Search
            </button>
        </div>
    </form>
</div>

<div id="item-list-table-container" 
     hx-trigger="load, refreshItemList from:body" 
     hx-get="{% url 'materialreturn_item_search_table' %}" 
     hx-swap="innerHTML">
    <!-- Loading indicator for Item List table -->
    <div id="item-list-loading-indicator" class="htmx-indicator text-center p-4">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2">Loading Items...</p>
    </div>
</div>
```

**`inventory_app/materialreturn/_selected_items_tab_content.html`** (Content for Selected Items Tab)

```html
<div id="selected-items-table-container"
     hx-trigger="load, refreshSelectedItemsList from:body"
     hx-get="{% url 'materialreturn_selected_items_table' %}"
     hx-swap="innerHTML">
    <!-- Loading indicator for Selected Items table -->
    <div id="selected-items-loading-indicator" class="htmx-indicator text-center p-4">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2">Loading Selected Items...</p>
    </div>
</div>
```

**`inventory_app/materialreturn/_search_form_fields.html`** (Partial for dynamic search form fields)

```html
<div class="col-span-1">
    <label for="{{ search_form.item_type.id_for_label }}" class="block text-sm font-medium text-gray-700">
        {{ search_form.item_type.label }}
    </label>
    {{ search_form.item_type }}
</div>
<div class="col-span-1">
    <label for="{{ search_form.category.id_for_label }}" class="block text-sm font-medium text-gray-700">
        {{ search_form.category.label }}
    </label>
    {{ search_form.category }}
</div>
<div class="col-span-1">
    <label for="{{ search_form.search_code.id_for_label }}" class="block text-sm font-medium text-gray-700">
        {{ search_form.search_code.label }}
    </label>
    {{ search_form.search_code }}
</div>
<div class="col-span-1" id="search_input_field_container">
    {% include 'inventory_app/materialreturn/_search_input_field.html' with search_form=search_form %}
</div>
```

**`inventory_app/materialreturn/_search_input_field.html`** (Partial for dynamic search input)

```html
<label for="{{ search_form.search_text.id_for_label }}" class="block text-sm font-medium text-gray-700">
    Search Value
</label>
{% if search_form.search_code.value == 'tblDG_Item_Master.Location' %}
    {{ search_form.search_location }}
{% else %}
    {{ search_form.search_text }}
{% endif %}
```

**`inventory_app/materialreturn/_item_list_table.html`** (Partial for Item Search Results Grid)

```html
<div class="overflow-x-auto bg-white rounded-lg shadow overflow-y-auto relative">
    {% if items %}
    <table id="itemTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-[3%]">SN</th>
                <th scope="col" class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-[10%]">Item Code</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[35%]">Description</th>
                <th scope="col" class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-[5%]">UOM</th>
                <th scope="col" class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-[8%]">Stock Qty</th>
                <th scope="col" class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-[12%]">BG Group/WoNo Type</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[10%]">BG Group/WoNo</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[12%]">Ret Qty</th>
                <th scope="col" class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-[5%]">Location</th>
                <th scope="col" class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-[10%]">Remarks</th>
                <th scope="col" class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-[4%]">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for item in items %}
            <tr id="item-row-{{ item.pk }}">
                <td class="py-2 px-4 text-right text-sm text-gray-900 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-2 px-4 text-center text-sm text-gray-900 whitespace-nowrap">{{ item.item_code }}</td>
                <td class="py-2 px-4 text-left text-sm text-gray-900">{{ item.manf_desc }}</td>
                <td class="py-2 px-4 text-center text-sm text-gray-900 whitespace-nowrap">{{ item.get_display_uom }}</td>
                <td class="py-2 px-4 text-right text-sm text-gray-900 whitespace-nowrap">{{ item.get_display_stock_qty }}</td>
                
                {# Dynamic Dept/WO Type dropdown #}
                <td class="py-2 px-4 text-center text-sm text-gray-900 whitespace-nowrap">
                    <form hx-post="{% url 'materialreturn_item_row_input_update' item.pk %}" 
                          hx-target="#dept-wo-input-{{ item.pk }}" 
                          hx-swap="outerHTML"
                          hx-trigger="change from:select[name='dept_or_wo_type']"
                          class="inline-block" id="row-form-{{ item.pk }}">
                        {% csrf_token %}
                        <input type="hidden" name="item_id" value="{{ item.pk }}">
                        <select name="dept_or_wo_type" class="box3 w-full h-5">
                            <option value="0">Select</option>
                            <option value="1">BG Group</option>
                            <option value="2">WONo</option>
                        </select>
                    </form>
                </td>

                {# Dynamic Dept/WO Input field #}
                <td class="py-2 px-4 text-left text-sm text-gray-900 whitespace-nowrap" id="dept-wo-input-{{ item.pk }}">
                    <!-- Content will be swapped by HTMX via ItemRowInputUpdateView -->
                    <input type="text" name="wo_no" class="box3 w-full h-4 hidden" placeholder="WO Number">
                    <select name="dept_id" class="box3 w-full h-5 hidden">
                        <option value="">Select</option>
                        {% for dept in item_form_template.fields.dept_id.queryset %}
                        <option value="{{ dept.pk }}">{{ dept.symbol }}</option>
                        {% endfor %}
                    </select>
                </td>

                {# Quantity, Remarks, Add Button #}
                <td class="py-2 px-4 text-sm text-gray-900 whitespace-nowrap">
                    <input type="number" name="ret_qty" class="box3 w-full h-4" placeholder="Ret Qty">
                </td>
                <td class="py-2 px-4 text-sm text-gray-900 whitespace-nowrap">
                    <input type="text" name="remarks" class="box3 w-full h-4" placeholder="Remarks">
                </td>
                <td class="py-2 px-4 text-center text-sm text-gray-900 whitespace-nowrap">
                    <button type="button" 
                            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded text-xs"
                            hx-post="{% url 'materialreturn_add_item_temp' %}"
                            hx-include="#row-form-{{ item.pk }}, #dept-wo-input-{{ item.pk }} input[name='wo_no'], #dept-wo-input-{{ item.pk }} select[name='dept_id'], #item-row-{{ item.pk }} input[name='ret_qty'], #item-row-{{ item.pk }} input[name='remarks']"
                            hx-confirm="return confirmationAdd()"
                            hx-indicator="#htmx-indicator">
                        Add
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <div class="text-center py-8">
        <p class="text-lg font-medium text-gray-600">No data to display !</p>
    </div>
    {% endif %}
</div>

{% comment %} DataTables initialization will happen after HTMX swap {% endcomment %}
```

**`inventory_app/materialreturn/_item_row_dept_wo_cell.html`** (Partial for dynamic input in item row)

```html
{# This partial is swapped into the TD with id="dept-wo-input-{{ item.pk }}" #}
{% if show_wo_field %}
    <input type="text" name="wo_no" class="box3 w-full h-4" placeholder="WO Number" value="{{ form.wo_no.value|default:'' }}">
{% elif show_dept_field %}
    <select name="dept_id" class="box3 w-full h-5">
        <option value="">Select</option>
        {% for dept in form.fields.dept_id.queryset %}
            <option value="{{ dept.pk }}" {% if form.dept_id.value == dept.pk %}selected{% endif %}>{{ dept.symbol }}</option>
        {% endfor %}
    </select>
{% else %}
    <input type="text" name="wo_no" class="box3 w-full h-4 hidden" placeholder="WO Number">
    <select name="dept_id" class="box3 w-full h-5 hidden">
        <option value="">Select</option>
    </select>
{% endif %}
```

**`inventory_app/materialreturn/_selected_items_table.html`** (Partial for Selected Items Grid)

```html
<div class="overflow-x-auto bg-white rounded-lg shadow overflow-y-auto relative">
    {% if selected_items %}
    <table id="selectedItemsTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-[4%]">Actions</th>
                <th scope="col" class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-[13%]">Item Code</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[20%]">Description</th>
                <th scope="col" class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-[5%]">UOM</th>
                <th scope="col" class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-[9%]">Stk Qty</th>
                <th scope="col" class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-[10%]">BG Group</th>
                <th scope="col" class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-[8%]">WONo</th>
                <th scope="col" class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-[7%]">Ret Qty</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for item in selected_items %}
            <tr>
                <td class="py-2 px-4 text-right text-sm text-gray-900 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-2 px-4 text-center text-sm text-gray-900 whitespace-nowrap">
                    <button type="button" 
                            class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs"
                            hx-delete="{% url 'materialreturn_delete_item_temp' item.pk %}"
                            hx-confirm="return confirmationDelete()"
                            hx-indicator="#htmx-indicator">
                        Delete
                    </button>
                </td>
                <td class="py-2 px-4 text-center text-sm text-gray-900 whitespace-nowrap">{{ item.item.item_code }}</td>
                <td class="py-2 px-4 text-left text-sm text-gray-900">{{ item.item.manf_desc }}</td>
                <td class="py-2 px-4 text-center text-sm text-gray-900 whitespace-nowrap">{{ item.item.get_display_uom }}</td>
                <td class="py-2 px-4 text-right text-sm text-gray-900 whitespace-nowrap">{{ item.item.get_display_stock_qty }}</td>
                <td class="py-2 px-4 text-center text-sm text-gray-900 whitespace-nowrap">
                    {% if item.dept %}{{ item.dept.symbol }}{% else %}NA{% endif %}
                </td>
                <td class="py-2 px-4 text-center text-sm text-gray-900 whitespace-nowrap">
                    {% if item.wo_no %}{{ item.wo_no }}{% else %}NA{% endif %}
                </td>
                <td class="py-2 px-4 text-right text-sm text-gray-900 whitespace-nowrap">{{ item.return_qty|floatformat:"3" }}</td>
                <td class="py-2 px-4 text-left text-sm text-gray-900">{{ item.remarks }}</td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot class="bg-gray-50">
            <tr>
                <td colspan="10" class="py-2 px-4 text-right">
                    <button type="button" 
                            class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-sm"
                            hx-post="{% url 'materialreturn_generate_mrn' %}"
                            hx-confirm="return confirmationAdd()"
                            hx-indicator="#htmx-indicator">
                        Generate MRN
                    </button>
                </td>
            </tr>
        </tfoot>
    </table>
    {% else %}
    <div class="text-center py-8">
        <p class="text-lg font-medium text-gray-600">No data to display !</p>
    </div>
    {% endif %}
</div>

{% comment %} DataTables initialization will happen after HTMX swap {% endcomment %}
```

#### 4.5 URLs (`inventory_app/urls.py`)

URL patterns for the main page and all HTMX-specific endpoints.

```python
from django.urls import path
from .views import (
    MaterialReturnNoteView, 
    ItemSearchTableView, 
    ItemSearchFormOptionsView,
    ItemSearchInputFieldView,
    AddItemToReturnTempView, 
    SelectedItemsTableView, 
    DeleteItemFromReturnTempView, 
    GenerateMRNView,
    ItemRowInputUpdateView
)

urlpatterns = [
    path('materialreturn/', MaterialReturnNoteView.as_view(), name='materialreturn_new'),
    
    # HTMX endpoints for Item Master Tab (Search/Add)
    path('materialreturn/item-master-tab/', ItemSearchTableView.as_view(), name='materialreturn_item_master_tab'),
    path('materialreturn/item-search-table/', ItemSearchTableView.as_view(), name='materialreturn_item_search_table'),
    path('materialreturn/hx-get-search-options/', ItemSearchFormOptionsView.as_view(), name='materialreturn_hx_get_search_options'),
    path('materialreturn/hx-get-search-input-field/', ItemSearchInputFieldView.as_view(), name='materialreturn_hx_get_search_input_field'),
    path('materialreturn/add-item-temp/', AddItemToReturnTempView.as_view(), name='materialreturn_add_item_temp'),
    path('materialreturn/item-row-input-update/<int:item_pk>/', ItemRowInputUpdateView.as_view(), name='materialreturn_item_row_input_update'),

    # HTMX endpoints for Selected Items Tab
    path('materialreturn/selected-items-tab/', SelectedItemsTableView.as_view(), name='materialreturn_selected_items_tab'),
    path('materialreturn/selected-items-table/', SelectedItemsTableView.as_view(), name='materialreturn_selected_items_table'),
    path('materialreturn/delete-item-temp/<int:pk>/', DeleteItemFromReturnTempView.as_view(), name='materialreturn_delete_item_temp'),
    path('materialreturn/generate-mrn/', GenerateMRNView.as_view(), name='materialreturn_generate_mrn'),
]

```

#### 4.6 Tests (`inventory_app/tests.py`)

Comprehensive unit tests for model logic and integration tests for views, covering all CRUD operations and HTMX interactions. These tests will ensure the migrated functionality is correct and robust.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.sessions.models import Session
from django.utils import timezone
from datetime import date, time

from .models import (
    Company, FinancialYear, Category, BusinessGroup, Unit, Item,
    MaterialReturnTemp, MaterialReturnMaster, MaterialReturnDetail
)

class MaterialReturnModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create common test data for all model tests
        cls.company = Company.objects.create(id=1, name="Test Company")
        cls.fin_year = FinancialYear.objects.create(id=1, year_name="2023-2024")
        cls.category = Category.objects.create(id=1, symbol="CAT", name="Test Category", company=cls.company)
        cls.unit = Unit.objects.create(id=1, symbol="PCS")
        cls.business_group_a = BusinessGroup.objects.create(id=101, symbol="BG_A")
        cls.business_group_b = BusinessGroup.objects.create(id=102, symbol="BG_B")

        cls.item1 = Item.objects.create(
            id=1, item_code="ITEM001", manf_desc="Test Item 1",
            uom_basic=cls.unit, stock_qty=100.000, location="WAREHOUSE_A",
            category=cls.category, company=cls.company, fin_year=cls.fin_year
        )
        cls.item2 = Item.objects.create(
            id=2, item_code="ITEM002", manf_desc="Test Item 2",
            uom_basic=cls.unit, stock_qty=50.000, location="WAREHOUSE_B",
            category=cls.category, company=cls.company, fin_year=cls.fin_year
        )

        cls.session_id_user1 = 'test_session_user1'
        cls.session_id_user2 = 'test_session_user2' # To test session isolation

    def test_item_search_by_category_item_code(self):
        qs = Item.objects.search_items(
            item_type='Category', category_id=self.category.id,
            search_field='tblDG_Item_Master.ItemCode', search_value='ITEM001',
            company_id=self.company.id, fin_year_id=self.fin_year.id
        )
        self.assertEqual(qs.count(), 1)
        self.assertEqual(qs.first(), self.item1)

    def test_item_search_by_description(self):
        qs = Item.objects.search_items(
            item_type='Category', category_id='Select', # No category filter
            search_field='tblDG_Item_Master.ManfDesc', search_value='Test Item',
            company_id=self.company.id, fin_year_id=self.fin_year.id
        )
        self.assertEqual(qs.count(), 2)
        self.assertIn(self.item1, qs)
        self.assertIn(self.item2, qs)

    def test_item_search_by_location(self):
        qs = Item.objects.search_items(
            item_type='Category', category_id='Select',
            search_field='tblDG_Item_Master.Location', search_value='WAREHOUSE_A',
            company_id=self.company.id, fin_year_id=self.fin_year.id
        )
        self.assertEqual(qs.count(), 1)
        self.assertEqual(qs.first(), self.item1)

    def test_item_search_no_type_selected(self):
        qs = Item.objects.search_items(
            item_type='Select', category_id=None,
            search_field=None, search_value=None,
            company_id=self.company.id, fin_year_id=self.fin_year.id
        )
        self.assertEqual(qs.count(), 0) # Should return empty if 'Select' type is chosen

    def test_add_item_to_temp_success_dept(self):
        temp_item = MaterialReturnTemp.objects.add_item_to_temp(
            company_id=self.company.id, session_id=self.session_id_user1,
            item_id=self.item1.id, dept_id=self.business_group_a.id, wo_no=None,
            return_qty=10.500, remarks="Return for project X"
        )
        self.assertEqual(MaterialReturnTemp.objects.count(), 1)
        self.assertEqual(temp_item.item, self.item1)
        self.assertEqual(temp_item.dept, self.business_group_a)
        self.assertIsNone(temp_item.wo_no)

    def test_add_item_to_temp_success_wo(self):
        temp_item = MaterialReturnTemp.objects.add_item_to_temp(
            company_id=self.company.id, session_id=self.session_id_user1,
            item_id=self.item2.id, dept_id=None, wo_no="WO12345",
            return_qty=5.000, remarks="Return from WO 12345"
        )
        self.assertEqual(MaterialReturnTemp.objects.count(), 1)
        self.assertEqual(temp_item.item, self.item2)
        self.assertEqual(temp_item.wo_no, "WO12345")
        self.assertIsNone(temp_item.dept)

    def test_add_item_to_temp_duplicate_item(self):
        MaterialReturnTemp.objects.add_item_to_temp(
            company_id=self.company.id, session_id=self.session_id_user1,
            item_id=self.item1.id, dept_id=self.business_group_a.id, wo_no=None,
            return_qty=10.000, remarks="Initial add"
        )
        with self.assertRaisesMessage(ValidationError, "Item is already selected for MRN."):
            MaterialReturnTemp.objects.add_item_to_temp(
                company_id=self.company.id, session_id=self.session_id_user1,
                item_id=self.item1.id, dept_id=self.business_group_a.id, wo_no=None,
                return_qty=5.000, remarks="Duplicate add"
            )
        self.assertEqual(MaterialReturnTemp.objects.count(), 1) # Still only one item

    def test_add_item_to_temp_invalid_qty(self):
        with self.assertRaisesMessage(ValidationError, "Invalid Return Quantity format."):
            MaterialReturnTemp.objects.add_item_to_temp(
                company_id=self.company.id, session_id=self.session_id_user1,
                item_id=self.item1.id, dept_id=self.business_group_a.id, wo_no=None,
                return_qty=-10.000, remarks="Negative qty"
            )
        with self.assertRaisesMessage(ValidationError, "Invalid Return Quantity format."):
            MaterialReturnTemp.objects.add_item_to_temp(
                company_id=self.company.id, session_id=self.session_id_user1,
                item_id=self.item1.id, dept_id=self.business_group_a.id, wo_no=None,
                return_qty="abc", remarks="Non-numeric qty"
            )
        with self.assertRaisesMessage(ValidationError, "Invalid Return Quantity format."):
            MaterialReturnTemp.objects.add_item_to_temp(
                company_id=self.company.id, session_id=self.session_id_user1,
                item_id=self.item1.id, dept_id=self.business_group_a.id, wo_no=None,
                return_qty=10.1234, remarks="More than 3 decimal places"
            )

    def test_add_item_to_temp_both_dept_and_wo(self):
        with self.assertRaisesMessage(ValidationError, "An item cannot be associated with both a department and a work order."):
            MaterialReturnTemp.objects.add_item_to_temp(
                company_id=self.company.id, session_id=self.session_id_user1,
                item_id=self.item1.id, dept_id=self.business_group_a.id, wo_no="WOXYZ",
                return_qty=10.000, remarks="Both dept and WO"
            )

    def test_get_next_mrn_no(self):
        # Test initial MRN number
        next_mrn = MaterialReturnMaster.objects.get_next_mrn_no(self.company.id, self.fin_year.id)
        self.assertEqual(next_mrn, "0001")

        # Create a previous MRN
        MaterialReturnMaster.objects.create(
            id=1, sys_date=date.today(), sys_time=time(10, 0, 0),
            company=self.company, fin_year=self.fin_year,
            session_id="dummy_session", mrn_no="0001"
        )
        next_mrn = MaterialReturnMaster.objects.get_next_mrn_no(self.company.id, self.fin_year.id)
        self.assertEqual(next_mrn, "0002")

    def test_process_mrn_from_temp_success(self):
        # Add items to temp for user1
        MaterialReturnTemp.objects.add_item_to_temp(
            company_id=self.company.id, session_id=self.session_id_user1,
            item_id=self.item1.id, dept_id=self.business_group_a.id, wo_no=None,
            return_qty=10.000, remarks="Temp Item 1"
        )
        MaterialReturnTemp.objects.add_item_to_temp(
            company_id=self.company.id, session_id=self.session_id_user1,
            item_id=self.item2.id, dept_id=None, wo_no="WO987",
            return_qty=5.000, remarks="Temp Item 2"
        )
        self.assertEqual(MaterialReturnTemp.objects.count(), 2)

        # Process MRN
        mrn_master = MaterialReturnMaster.objects.process_mrn_from_temp(
            session_id=self.session_id_user1,
            company_id=self.company.id,
            fin_year_id=self.fin_year.id
        )

        self.assertIsNotNone(mrn_master)
        self.assertEqual(mrn_master.mrn_no, "0001")
        self.assertEqual(MaterialReturnMaster.objects.count(), 1)
        self.assertEqual(MaterialReturnDetail.objects.count(), 2)
        self.assertEqual(MaterialReturnTemp.objects.count(), 0) # Temp table should be empty

        detail1 = MaterialReturnDetail.objects.get(item=self.item1)
        self.assertEqual(detail1.master, mrn_master)
        self.assertEqual(detail1.return_qty, 10.000)

        detail2 = MaterialReturnDetail.objects.get(item=self.item2)
        self.assertEqual(detail2.master, mrn_master)
        self.assertEqual(detail2.return_qty, 5.000)


class MaterialReturnViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create common test data for all view tests
        cls.company = Company.objects.create(id=1, name="Test Company")
        cls.fin_year = FinancialYear.objects.create(id=1, year_name="2023-2024")
        cls.category1 = Category.objects.create(id=1, symbol="CAT1", name="Category A", company=cls.company)
        cls.category2 = Category.objects.create(id=2, symbol="CAT2", name="Category B", company=cls.company)
        cls.unit = Unit.objects.create(id=1, symbol="PCS")
        cls.business_group1 = BusinessGroup.objects.create(id=1, symbol="DEPTA")
        cls.business_group2 = BusinessGroup.objects.create(id=2, symbol="DEPTB")

        cls.item1 = Item.objects.create(
            id=1, item_code="ITEM001", manf_desc="Product X",
            uom_basic=cls.unit, stock_qty=100.000, location="LOC_A",
            category=cls.category1, company=cls.company, fin_year=cls.fin_year
        )
        cls.item2 = Item.objects.create(
            id=2, item_code="ITEM002", manf_desc="Product Y",
            uom_basic=cls.unit, stock_qty=50.000, location="LOC_B",
            category=cls.category2, company=cls.company, fin_year=cls.fin_year
        )
        cls.item3 = Item.objects.create(
            id=3, item_code="WOITEM001", manf_desc="WO Specific Item",
            uom_basic=cls.unit, stock_qty=20.000, location="LOC_A",
            category=cls.category1, company=cls.company, fin_year=cls.fin_year
        )

    def setUp(self):
        self.client = Client()
        self.session = self.client.session
        self.session.save() # Ensure session exists and has a key
        # Mock session variables normally set by middleware/authentication
        self.client.get(reverse('materialreturn_new')) # Initialize session data for tests
        # This will set session_key for anonymous user, or if user is authenticated, use their id.
        # For simplicity, we'll use a specific session key.
        self.session_id = self.client.session.session_key 
        # For authenticated user, it might be: self.client.login(username='testuser', password='password')
        # and then self.session_id = self.client.user.id

    # --- MaterialReturnNoteView Tests ---
    def test_material_return_note_view_get(self):
        response = self.client.get(reverse('materialreturn_new'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_app/materialreturn/mrn_new.html')
        self.assertContains(response, 'Material Return Note [MRN] - New')
        self.assertContains(response, 'id="item-search-form"')
        self.assertContains(response, 'id="itemTable"')
        self.assertContains(response, 'id="selectedItemsTable"')
    
    def test_material_return_note_view_initial_tab(self):
        response = self.client.get(reverse('materialreturn_new') + '?tab=1')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'activeTab: \'1\'') # Alpine.js check

    # --- ItemSearchTableView Tests ---
    def test_item_search_table_initial_load(self):
        response = self.client.get(reverse('materialreturn_item_search_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_app/materialreturn/_item_list_table.html')
        self.assertContains(response, 'No data to display !') # Initial load should be empty as no search params

    def test_item_search_table_post_category_item_code(self):
        data = {
            'item_type': 'Category',
            'category': self.category1.id,
            'search_code': 'tblDG_Item_Master.ItemCode',
            'search_text': 'ITEM001',
            # search_location will be hidden
        }
        response = self.client.get(reverse('materialreturn_item_search_table'), data=data)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_app/materialreturn/_item_list_table.html')
        self.assertContains(response, 'ITEM001')
        self.assertNotContains(response, 'ITEM002')

    def test_item_search_table_post_wo_items_description(self):
        data = {
            'item_type': 'WOItems',
            'search_code': 'tblDG_Item_Master.ManfDesc',
            'search_text': 'Specific',
            # category and search_location will be hidden
        }
        response = self.client.get(reverse('materialreturn_item_search_table'), data=data)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WOITEM001')
        self.assertNotContains(response, 'ITEM001')

    # --- ItemSearchFormOptionsView Tests ---
    def test_hx_get_search_options_post_category(self):
        response = self.client.post(reverse('materialreturn_hx_get_search_options'), {'item_type': 'Category'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_app/materialreturn/_search_form_fields.html')
        self.assertContains(response, 'name="category"') # Category dropdown should be visible
        self.assertContains(response, 'name="search_text"') # search_text should be visible by default for ItemCode/Desc
        self.assertContains(response, 'name="search_location" type="hidden"') # search_location should be hidden

    def test_hx_get_search_options_post_woitems(self):
        response = self.client.post(reverse('materialreturn_hx_get_search_options'), {'item_type': 'WOItems'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_app/materialreturn/_search_form_fields.html')
        self.assertContains(response, 'name="category" type="hidden"') # Category dropdown should be hidden
        self.assertContains(response, 'name="search_text"') 
        self.assertContains(response, 'name="search_location" type="hidden"')

    # --- ItemSearchInputFieldView Tests ---
    def test_hx_get_search_input_field_post_location(self):
        data = {'item_type': 'Category', 'search_code': 'tblDG_Item_Master.Location'}
        response = self.client.post(reverse('materialreturn_hx_get_search_input_field'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_app/materialreturn/_search_input_field.html')
        self.assertContains(response, 'name="search_location"')
        self.assertNotContains(response, 'name="search_text"')

    def test_hx_get_search_input_field_post_itemcode(self):
        data = {'item_type': 'Category', 'search_code': 'tblDG_Item_Master.ItemCode'}
        response = self.client.post(reverse('materialreturn_hx_get_search_input_field'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_app/materialreturn/_search_input_field.html')
        self.assertContains(response, 'name="search_text"')
        self.assertNotContains(response, 'name="search_location"')

    # --- AddItemToReturnTempView Tests ---
    def test_add_item_to_return_temp_success_dept(self):
        data = {
            'item_id': self.item1.id,
            'ret_qty': '10.500',
            'remarks': 'Test add via HTMX',
            'dept_or_wo_type': '1', # BG Group
            'dept_id': self.business_group1.id,
            'wo_no': '',
        }
        response = self.client.post(reverse('materialreturn_add_item_temp'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HX-Request should return 204 No Content on success
        self.assertEqual(MaterialReturnTemp.objects.count(), 1)
        self.assertEqual(response['HX-Trigger'], 'refreshSelectedItemsList')

    def test_add_item_to_return_temp_success_wo(self):
        data = {
            'item_id': self.item2.id,
            'ret_qty': '5.000',
            'remarks': 'Test add WO',
            'dept_or_wo_type': '2', # WO No
            'dept_id': '',
            'wo_no': 'TESTWO123',
        }
        response = self.client.post(reverse('materialreturn_add_item_temp'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(MaterialReturnTemp.objects.count(), 1)
        self.assertEqual(response['HX-Trigger'], 'refreshSelectedItemsList')

    def test_add_item_to_return_temp_invalid_data(self):
        data = {
            'item_id': self.item1.id,
            'ret_qty': '-1.000', # Invalid quantity
            'remarks': 'Invalid qty test',
            'dept_or_wo_type': '1',
            'dept_id': self.business_group1.id,
            'wo_no': '',
        }
        response = self.client.post(reverse('materialreturn_add_item_temp'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Should not be 204 on error, let messages display
        self.assertEqual(MaterialReturnTemp.objects.count(), 0)
        # Check for error message in the response (Django messages are rendered on next request)
        # This is harder to test with HX-Request; usually, you'd check for a specific header or re-rendered content.
        # For now, relying on the status code and model count.

    def test_add_item_to_return_temp_missing_dept_or_wo_type(self):
        data = {
            'item_id': self.item1.id,
            'ret_qty': '10.000',
            'remarks': 'Missing type test',
            'dept_or_wo_type': '0', # Select
            'dept_id': '',
            'wo_no': '',
        }
        response = self.client.post(reverse('materialreturn_add_item_temp'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(MaterialReturnTemp.objects.count(), 0)

    # --- SelectedItemsTableView Tests ---
    def test_selected_items_table_view(self):
        MaterialReturnTemp.objects.create(
            company=self.company, session_id=self.session_id, item=self.item1,
            dept=self.business_group1, return_qty=10.000, remarks="Test 1"
        )
        response = self.client.get(reverse('materialreturn_selected_items_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_app/materialreturn/_selected_items_table.html')
        self.assertContains(response, 'ITEM001')
        self.assertEqual(response.context['selected_items'].count(), 1)

    # --- DeleteItemFromReturnTempView Tests ---
    def test_delete_item_from_return_temp(self):
        temp_item = MaterialReturnTemp.objects.create(
            company=self.company, session_id=self.session_id, item=self.item1,
            dept=self.business_group1, return_qty=10.000, remarks="To be deleted"
        )
        self.assertEqual(MaterialReturnTemp.objects.count(), 1)
        response = self.client.delete(reverse('materialreturn_delete_item_temp', args=[temp_item.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(MaterialReturnTemp.objects.count(), 0)
        self.assertEqual(response['HX-Trigger'], 'refreshSelectedItemsList')

    def test_delete_item_from_return_temp_not_found(self):
        response = self.client.delete(reverse('materialreturn_delete_item_temp', args=[999]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Returns 200 with error message
        self.assertContains(response, 'Item not found in your temporary list.')

    # --- GenerateMRNView Tests ---
    def test_generate_mrn_success(self):
        MaterialReturnTemp.objects.create(
            company=self.company, session_id=self.session_id, item=self.item1,
            dept=self.business_group1, return_qty=10.000, remarks="Final Item 1"
        )
        MaterialReturnTemp.objects.create(
            company=self.company, session_id=self.session_id, item=self.item2,
            wo_no="WOXYZ", return_qty=5.000, remarks="Final Item 2"
        )
        self.assertEqual(MaterialReturnTemp.objects.count(), 2)

        response = self.client.post(reverse('materialreturn_generate_mrn'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(MaterialReturnMaster.objects.count(), 1)
        self.assertEqual(MaterialReturnDetail.objects.count(), 2)
        self.assertEqual(MaterialReturnTemp.objects.count(), 0)
        self.assertEqual(response['HX-Trigger'], 'refreshSelectedItemsList')

    def test_generate_mrn_no_items_selected(self):
        self.assertEqual(MaterialReturnTemp.objects.count(), 0)
        response = self.client.post(reverse('materialreturn_generate_mrn'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No items selected for Material Return Note.')
        self.assertEqual(MaterialReturnMaster.objects.count(), 0)
        self.assertEqual(MaterialReturnDetail.objects.count(), 0)

    # --- ItemRowInputUpdateView Tests ---
    def test_item_row_input_update_dept_field(self):
        data = {'dept_or_wo_type': '1'} # Select BG Group
        response = self.client.post(
            reverse('materialreturn_item_row_input_update', args=[self.item1.pk]),
            data,
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_app/materialreturn/_item_row_dept_wo_cell.html')
        self.assertContains(response, '<select name="dept_id"')
        self.assertNotContains(response, '<input type="text" name="wo_no"')

    def test_item_row_input_update_wo_field(self):
        data = {'dept_or_wo_type': '2'} # Select WO No
        response = self.client.post(
            reverse('materialreturn_item_row_input_update', args=[self.item1.pk]),
            data,
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_app/materialreturn/_item_row_dept_wo_cell.html')
        self.assertContains(response, '<input type="text" name="wo_no"')
        self.assertNotContains(response, '<select name="dept_id"')

    def test_item_row_input_update_select_none(self):
        data = {'dept_or_wo_type': '0'} # Select 'Select'
        response = self.client.post(
            reverse('materialreturn_item_row_input_update', args=[self.item1.pk]),
            data,
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_app/materialreturn/_item_row_dept_wo_cell.html')
        self.assertContains(response, 'name="wo_no" class="box3 w-full h-4 hidden"')
        self.assertContains(response, 'name="dept_id" class="box3 w-full h-5 hidden"')

```

### Step 5: HTMX and Alpine.js Integration

**Summary of Integration:**

*   **HTMX for Page Sections:**
    *   The main `mrn_new.html` uses `x-data` for Alpine.js tabs.
    *   Each tab content area (`tab-content-0`, `tab-content-1`) uses `hx-get` to load its respective partial view (`_item_master_tab_content.html` or `_selected_items_tab_content.html`) on initial page load and on tab clicks.
    *   `hx-indicator` provides visual feedback during loading.
*   **HTMX for Search & Tables:**
    *   The `item-search-form` in `_item_master_tab_content.html` uses `hx-get` to submit search parameters to `materialreturn_item_search_table` and swap the entire `item-list-table-container` with the `_item_list_table.html` partial.
    *   `hx-trigger="load, refreshItemList from:body"` ensures search results and selected items tables refresh when needed (e.g., after an item is added/deleted).
    *   DataTables will be initialized on the `itemTable` and `selectedItemsTable` after HTMX swaps the content, ensuring dynamic sorting, filtering, and pagination.
*   **HTMX for Dynamic Forms/Inputs (Crucial for `GridView2` emulation):**
    *   The `select` element `name="dept_or_wo_type"` in each item row in `_item_list_table.html` uses `hx-post` to `materialreturn_item_row_input_update/<item_pk>/`.
    *   `hx-target` is set to the specific `<td>` containing the input field (`#dept-wo-input-{{ item.pk }}`) and `hx-swap="outerHTML"` re-renders only that cell with the correct hidden/visible input field.
*   **HTMX for CRUD Operations:**
    *   "Add" button in `_item_list_table.html` uses `hx-post` to `materialreturn_add_item_temp/`, including necessary form fields via `hx-include`. On success (204 No Content), it triggers `refreshSelectedItemsList`.
    *   "Delete" button in `_selected_items_table.html` uses `hx-delete` to `materialreturn_delete_item_temp/<pk>/`. On success, it triggers `refreshSelectedItemsList`.
    *   "Generate MRN" button in `_selected_items_table.html` footer uses `hx-post` to `materialreturn_generate_mrn/`. On success, it triggers `refreshSelectedItemsList`.
*   **Alpine.js:**
    *   Manages the `activeTab` state for the tabbed interface, handling the visual active state and `x-show` directives for tab content visibility.
*   **Django Messaging Integration:** Views use `messages.success` and `messages.error` which are then rendered on the main page via `{% if messages %}` block. For HTMX requests, `HX-Trigger` or `HX-Trigger-After-Swap` can be used to specifically instruct the client to show messages, or they can be part of the swapped HTML. The current approach assumes messages are part of the swapped content if a 200 response is sent (e.g., on form error), or they are globally picked up by Alpine.js if HTMX triggers a "message" event.

### Final Notes

*   **Placeholders:** Replace `1` for `CompId` and `FinYearId` with actual dynamic values (e.g., from user profile, configuration, or request context). The `session_id` is mapped to `request.session.session_key` for anonymous users or `request.user.id` for authenticated users.
*   **DRY Templates:** The use of `{% include %}` directives for form fields and table contents ensures reusability and adherence to DRY principles.
*   **Fat Models, Thin Views:** Business logic (item search, adding to temp, validating WO, generating MRN) is encapsulated within Django model managers or methods, keeping the view logic concise (5-15 lines per method).
*   **Comprehensive Testing:** The provided unit and integration tests cover various scenarios, including valid/invalid inputs, duplicate entries, and HTMX interactions, aiming for high test coverage.
*   **Client-Side Validation:** While Django forms handle server-side validation, for a more responsive UX, additional Alpine.js or simple browser-native validation (`required` attribute) could be added to forms, but the core logic is server-validated.
*   **Error Handling:** The views use `try-except` blocks to catch `ValidationError` (from models/forms) and generic exceptions, providing user-friendly error messages via Django's messaging framework.
*   **CSS Styling:** Tailwind CSS classes (`box3`, `redbox`, `fontcsswhite`, `yui-datatable-theme` are replaced with Tailwind equivalents like `w-full`, `py-2 px-4`, `bg-blue-500`, `text-white`, `border`, etc.) are used for modern, responsive UI.