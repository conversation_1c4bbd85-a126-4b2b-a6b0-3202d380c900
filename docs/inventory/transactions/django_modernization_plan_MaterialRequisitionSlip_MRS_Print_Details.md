## ASP.NET to Django Conversion Script: Material Requisition Slip Print Details

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

## Step 1: Extract Database Schema

**Analysis:** The ASP.NET code queries multiple tables to assemble the Material Requisition Slip (MRS) report. We identify the following primary tables and their relevant columns based on the SQL commands and data processing logic:

*   **`tblInv_MaterialRequisition_Master`**: This is the main MRS header table.
    *   `Id` (PK)
    *   `MRSNo` (Material Requisition Number)
    *   `SysDate` (System Date, i.e., MRS Date)
    *   `SessionId` (User ID who generated it, refers to `EmpId` in `tblHR_OfficeStaff`)
    *   `CompId` (Company ID, inferred from `Session["compid"]`)
*   **`tblInv_MaterialRequisition_Details`**: This table holds individual line items for each MRS.
    *   `Id` (PK)
    *   `MRSNo` (Matches `MRSNo` in Master, but also has `MId` linking to Master `Id`)
    *   `ItemId` (References `tblDG_Item_Master`)
    *   `DeptId` (References `BusinessGroup`, can be `1` for 'NA' or actual department)
    *   `WONo` (Work Order Number)
    *   `ReqQty` (Requested Quantity)
    *   `Remarks`
    *   `MId` (Foreign Key to `tblInv_MaterialRequisition_Master.Id`)
*   **`tblDG_Item_Master`**: Contains item details.
    *   `Id` (PK)
    *   `ItemCode`
    *   `ManfDesc` (Manufacturer Description, used as Item Description)
    *   `UOMBasic` (References `Unit_Master` for Unit of Measure)
    *   `CompId`
*   **`Unit_Master`**: Defines units of measure.
    *   `Id` (PK)
    *   `Symbol` (Unit Symbol, e.g., KG, MTRS)
*   **`BusinessGroup`**: Used for department names.
    *   `Id` (PK)
    *   `Symbol` (Department Symbol/Name)
*   **`tblHR_OfficeStaff`**: Contains employee details, used to get the "Generated By" name.
    *   `EmpId` (PK, corresponds to `SessionId` in MRS Master)
    *   `Title` (e.g., Mr., Ms.)
    *   `EmployeeName`
    *   `CompId`
*   **Implicit `Company_Master`**: The `fun.CompAdd(CompId)` implies a table holding company address details. We'll assume a `tblCompany_Master` table with `Id` and `Address` fields.

## Step 2: Identify Backend Functionality

**Analysis:** This ASP.NET page is purely a **Read** operation, focused on displaying a Material Requisition Slip.

*   **Read:** The core functionality involves:
    *   Fetching a specific `MaterialRequisitionMaster` record using `Id` (`MId` in ASP.NET) and `CompId`.
    *   Retrieving all associated `MaterialRequisitionDetail` records.
    *   For each detail record, performing lookups for:
        *   `ItemCode`, `ManfDesc` (Description), `UOMBasic` (Unit of Measure) from `tblDG_Item_Master`.
        *   `Symbol` from `Unit_Master` for the `UOMBasic`.
        *   `Symbol` from `BusinessGroup` for the `DeptId`. Special logic handles `DeptId = 1` and `DBNull` values for department and `WONo`.
    *   Retrieving `SysDate` from `tblInv_MaterialRequisition_Master` and formatting it.
    *   Retrieving the employee name (`Title + EmployeeName`) from `tblHR_OfficeStaff` based on `SessionId`.
    *   Retrieving the company address.
    *   Formatting `ReqQty` to three decimal places.
*   **No Create, Update, Delete:** There are no explicit CRUD operations on this page itself.
*   **Report Generation:** The collected and transformed data is then fed into a Crystal Report Viewer. In Django, this will be replaced by an interactive HTML table (using DataTables) for on-screen viewing, potentially with an option to generate a PDF.

## Step 3: Infer UI Components

**Analysis:**
*   **`CrystalReportViewer`**: This is the primary display area. It will be replaced by a modern HTML table, powered by DataTables, to display the MRS line items.
*   **`asp:Button ID="Button1" Text="Cancel"`**: A simple navigation button. This will be a standard Django link or button that redirects.
*   **Basic Header/Footer**: The page includes a header (`Material Requisition Slip [MRS] - Print`) and a footer for the cancel button.

## Step 4: Generate Django Code

We will create a new Django application, e.g., `inventory`, to house this functionality.

### 4.1 Models (in `inventory/models.py`)

These models are designed to map directly to your existing SQL Server database tables using `managed = False`. We've added relationships and methods to encapsulate the data retrieval and transformation logic (the "fat model" approach) previously handled by the C# code.

```python
from django.db import models
from django.utils import timezone

# Assuming a tblCompany_Master table exists for company details
class Company(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    address = models.TextField(db_column='Address', blank=True, null=True)
    # Add other relevant fields like Name, Phone, etc. as needed

    class Meta:
        managed = False
        db_table = 'tblCompany_Master' # Example table name based on context
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return f"Company {self.id}"

class Employee(models.Model):
    # Maps to tblHR_OfficeStaff
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.title or ''} {self.employee_name or ''}".strip()

class Unit(models.Model):
    # Maps to Unit_Master
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class Item(models.Model):
    # Maps to tblDG_Item_Master
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=100)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=500, blank=True, null=True)
    uom_basic_id = models.IntegerField(db_column='UOMBasic', blank=True, null=True) # Foreign key to Unit_Master.Id
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.item_code

    @property
    def uom(self):
        """Retrieves the UOM symbol from Unit_Master."""
        if self.uom_basic_id:
            try:
                return Unit.objects.get(id=self.uom_basic_id).symbol
            except Unit.DoesNotExist:
                pass
        return "N/A"

class BusinessGroup(models.Model):
    # Maps to BusinessGroup (for DeptId)
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=100)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

class MaterialRequisitionMaster(models.Model):
    # Maps to tblInv_MaterialRequisition_Master
    id = models.IntegerField(db_column='Id', primary_key=True)
    mrs_no = models.CharField(db_column='MRSNo', max_length=50)
    sys_date = models.DateTimeField(db_column='SysDate')
    session_id = models.IntegerField(db_column='SessionId') # Maps to Employee.emp_id
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialRequisition_Master'
        verbose_name = 'Material Requisition Master'
        verbose_name_plural = 'Material Requisition Masters'

    def __str__(self):
        return self.mrs_no

    @property
    def mrs_date_dmy(self):
        """Formats the system date to DD/MM/YYYY."""
        return self.sys_date.strftime('%d/%m/%Y')

    @property
    def generated_by(self):
        """Retrieves the full name of the employee who generated the MRS."""
        try:
            employee = Employee.objects.get(emp_id=self.session_id, comp_id=self.comp_id)
            return f"{employee.title or ''}. {employee.employee_name or ''}".strip()
        except Employee.DoesNotExist:
            return "N/A"
    
    @property
    def company_address(self):
        """Retrieves the company address."""
        try:
            company = Company.objects.get(id=self.comp_id)
            return company.address
        except Company.DoesNotExist:
            return "N/A"

    def get_report_details(self):
        """
        Gathers and formats all details for the MRS report,
        mimicking the data structure prepared for Crystal Reports.
        This method embodies the 'fat model' principle by centralizing
        complex data aggregation and transformation logic.
        """
        # Using select_related and prefetch_related for efficient queries
        details = self.mrs_details.select_related('mrs_master').all()
        report_data = []

        for detail in details:
            item_obj = None
            try:
                item_obj = Item.objects.get(id=detail.item_id, comp_id=self.comp_id)
            except Item.DoesNotExist:
                pass 

            dept_symbol = "NA"
            wo_display = detail.wo_no if detail.wo_no else "NA" # Default for WONo

            # Replicate the DeptId/WONo logic from C#
            if str(detail.dept_id) == "1": # ASP.NET uses string "1" for DeptId 'NA'
                dept_symbol = "NA"
                wo_display = detail.wo_no if detail.wo_no else "NA"
            elif detail.dept_id: # If DeptId is not 1 and not null
                try:
                    dept_obj = BusinessGroup.objects.get(id=detail.dept_id)
                    dept_symbol = dept_obj.symbol
                    wo_display = "NA" # If department is present, WO is NA
                except BusinessGroup.DoesNotExist:
                    # If DeptId exists but BusinessGroup not found, treat as 'NA' for symbol
                    dept_symbol = "NA"
                    wo_display = detail.wo_no if detail.wo_no else "NA"
            else: # If DeptId is None/DBNull
                dept_symbol = "NA"
                wo_display = detail.wo_no if detail.wo_no else "NA"
            
            report_data.append({
                'id': detail.id,
                'mrs_no': detail.mrs_no,
                'item_code': item_obj.item_code if item_obj else "N/A",
                'description': item_obj.manf_desc if item_obj else "N/A",
                'uom': item_obj.uom if item_obj else "N/A",
                'department_symbol': dept_symbol,
                'wo_no': wo_display,
                'req_qty': f"{detail.req_qty:.3f}", # Format to 3 decimal places
                'remarks': detail.remarks if detail.remarks else "",
            })
        return report_data


class MaterialRequisitionDetail(models.Model):
    # Maps to tblInv_MaterialRequisition_Details
    id = models.IntegerField(db_column='Id', primary_key=True)
    mrs_no = models.CharField(db_column='MRSNo', max_length=50)
    item_id = models.IntegerField(db_column='ItemId') # FK to Item.Id
    dept_id = models.IntegerField(db_column='DeptId', blank=True, null=True) # FK to BusinessGroup.Id
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    req_qty = models.DecimalField(db_column='ReqQty', max_digits=18, decimal_places=3)
    remarks = models.TextField(db_column='Remarks', blank=True, null=True)
    m_id = models.IntegerField(db_column='MId') # This is the foreign key to MaterialRequisitionMaster.Id
    
    # Explicit ForeignKey to MaterialRequisitionMaster for ORM relationship
    # related_name='mrs_details' allows accessing details from master.details
    mrs_master = models.ForeignKey(MaterialRequisitionMaster, on_delete=models.DO_NOTHING, 
                                   db_column='MId', related_name='mrs_details')

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialRequisition_Details'
        verbose_name = 'Material Requisition Detail'
        verbose_name_plural = 'Material Requisition Details'

    def __str__(self):
        return f"MRS Detail {self.id} for MRS {self.mrs_no}"

```

### 4.2 Forms (in `inventory/forms.py`)

For a read-only "print" view, a Django `ModelForm` is not explicitly needed for user input. If filtering or selection of MRS number were required via a form, it would be defined here. For this direct migration, no form is necessary.

### 4.3 Views (in `inventory/views.py`)

We use a `DetailView` for the main MRS details and a `TemplateView` for the partial table loaded via HTMX. The C# code uses `MId` (from `Request.QueryString["Id"]`) and `CompId` (from `Session["compid"]`) to identify the MRS. We'll map `MId` to `pk` in the URL and retrieve `CompId` from the session.

```python
from django.views.generic import DetailView, TemplateView
from django.urls import reverse_lazy
from django.shortcuts import get_object_or_404
from django.http import HttpResponse, Http404
from django.contrib import messages
from .models import MaterialRequisitionMaster

class MaterialRequisitionPrintDetailsView(DetailView):
    """
    Displays the detailed Material Requisition Slip (MRS) report.
    This view replaces the ASP.NET Crystal Report Viewer page.
    It fetches master MRS data and its related formatted details.
    """
    model = MaterialRequisitionMaster
    template_name = 'inventory/materialrequisition/print_details.html'
    context_object_name = 'mrs_master'
    
    def get_object(self, queryset=None):
        """
        Retrieves the MaterialRequisitionMaster object based on URL parameters
        (Id as pk) and session data (compid), mirroring the ASP.NET logic.
        """
        # The ASP.NET code uses Request.QueryString["Id"] as MId
        m_id = self.kwargs.get('pk') 
        comp_id = self.request.session.get('compid') # From Session["compid"]

        if not m_id or comp_id is None:
            messages.error(self.request, "Missing required parameters for MRS details.")
            raise Http404("MRS ID or Company ID missing.")

        try:
            # Replicate the C# filtering logic
            obj = get_object_or_404(
                MaterialRequisitionMaster, 
                id=m_id, 
                comp_id=comp_id
            )
            return obj
        except Http404:
            messages.error(self.request, "Material Requisition Slip not found or you do not have permission.")
            raise

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        mrs_master = context['mrs_master']
        if mrs_master:
            # Use the fat model method to get the prepared report data
            context['report_details'] = mrs_master.get_report_details()
        else:
            context['report_details'] = [] 
            messages.warning(self.request, "No MRS details available.")
        return context

class MaterialRequisitionDetailsTablePartialView(DetailView):
    """
    Renders only the DataTables-powered table of MRS details,
    designed to be loaded dynamically via HTMX.
    """
    model = MaterialRequisitionMaster
    template_name = 'inventory/materialrequisition/_mrs_details_table.html'
    context_object_name = 'mrs_master'

    def get_object(self, queryset=None):
        # Similar object retrieval as the main view
        m_id = self.kwargs.get('pk')
        comp_id = self.request.session.get('compid')
        if not m_id or comp_id is None:
            raise Http404("MRS ID or Company ID missing for table view.")
        return get_object_or_404(MaterialRequisitionMaster, id=m_id, comp_id=comp_id)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        mrs_master = context['mrs_master']
        if mrs_master:
            context['report_details'] = mrs_master.get_report_details()
        else:
            context['report_details'] = []
        return context
```

### 4.4 Templates

The templates are structured for a traditional Django view with HTMX for dynamic content.

**`inventory/materialrequisition/print_details.html`**
This is the main page template that extends `core/base.html`. It sets up the overall layout and includes a placeholder for the HTMX-loaded DataTables content.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4 border-b pb-2">Material Requisition Slip [MRS] - Print</h2>
        
        {% if mrs_master %}
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-700 mb-6">
            <div>
                <p><strong>MRS No:</strong> {{ mrs_master.mrs_no }}</p>
                <p><strong>MRS Date:</strong> {{ mrs_master.mrs_date_dmy }}</p>
            </div>
            <div class="md:text-right">
                <p><strong>Generated By:</strong> {{ mrs_master.generated_by }}</p>
                <p><strong>Company Address:</strong> {{ mrs_master.company_address }}</p>
            </div>
        </div>

        <div id="mrs-details-table-container"
             hx-trigger="load"
             hx-get="{% url 'inventory:mrs_details_table_partial' pk=mrs_master.pk %}"
             hx-swap="innerHTML">
            <!-- Loading indicator for initial HTMX load -->
            <div class="flex justify-center items-center h-48">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                <p class="ml-4 text-gray-600">Loading MRS details...</p>
            </div>
        </div>
        {% else %}
        <div class="text-center text-red-600 text-lg">
            <p>Error: Material Requisition Slip details could not be loaded.</p>
        </div>
        {% endif %}

        <div class="mt-8 text-center">
            <a href="{% url 'some_other_mrs_list_page' %}" 
               class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-6 rounded-lg shadow-md">
                Cancel
            </a>
            <!-- Potentially add a Print PDF button here if needed -->
            <!-- <button class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-6 rounded-lg shadow-md ml-4"
                     onclick="window.print();">Print (Browser)</button> -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
    });
</script>
{% endblock %}
```

**`inventory/materialrequisition/_mrs_details_table.html`**
This partial template contains the DataTables-powered table for the MRS line items. It's designed to be loaded by HTMX.

```html
<div class="overflow-x-auto">
    <table id="mrsDetailsTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MRS No</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th class="py-3 px-6 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Req Qty</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for detail in report_details %}
            <tr class="hover:bg-gray-50">
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ detail.mrs_no }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ detail.item_code }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ detail.description }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ detail.uom }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ detail.department_symbol }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ detail.wo_no }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900 text-right">{{ detail.req_qty }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ detail.remarks }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="9" class="py-4 px-6 text-center text-gray-500">No material requisition details found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- DataTables initialization script -->
<script>
    $(document).ready(function() {
        $('#mrsDetailsTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0] } // Disable sorting for SN column
            ]
        });
    });
</script>
```

### 4.5 URLs (in `inventory/urls.py`)

Define the URL patterns for accessing the Material Requisition Slip print details.

```python
from django.urls import path
from .views import MaterialRequisitionPrintDetailsView, MaterialRequisitionDetailsTablePartialView

app_name = 'inventory' # Define app_name for namespacing

urlpatterns = [
    # Main URL for MRS Print Details, takes MId as pk
    path('mrs/print/<int:pk>/', MaterialRequisitionPrintDetailsView.as_view(), name='mrs_print_details'),
    
    # HTMX endpoint for loading the details table dynamically
    path('mrs/print/<int:pk>/table/', MaterialRequisitionDetailsTablePartialView.as_view(), name='mrs_details_table_partial'),

    # The C# code uses 'Response.Redirect("MaterialRequisitionSlip_MRS_Print.aspx?FyId=4&ModId=9&SubModId=40");'
    # This implies there's another list/summary page. For this example, let's assume a placeholder
    # 'some_other_mrs_list_page' which should be replaced with the actual URL.
    # E.g., path('mrs/list/', MRSListView.as_view(), name='some_other_mrs_list_page'),
]

```

### 4.6 Tests (in `inventory/tests.py`)

Comprehensive tests for models and views ensure data integrity and functionality.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import datetime, timedelta
from .models import (
    Company, Employee, Unit, Item, BusinessGroup,
    MaterialRequisitionMaster, MaterialRequisitionDetail
)

class MaterialRequisitionModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dummy data for all related models
        cls.company = Company.objects.create(id=1, address="123 Main St, Anytown")
        cls.employee = Employee.objects.create(emp_id=101, title="Mr", employee_name="John Doe", comp_id=1)
        cls.unit_kg = Unit.objects.create(id=1, symbol="KG")
        cls.unit_meter = Unit.objects.create(id=2, symbol="MTR")
        cls.item1 = Item.objects.create(id=1, item_code="ITEM001", manf_desc="Product A", uom_basic_id=cls.unit_kg.id, comp_id=1)
        cls.item2 = Item.objects.create(id=2, item_code="ITEM002", manf_desc="Product B", uom_basic_id=cls.unit_meter.id, comp_id=1)
        cls.dept_production = BusinessGroup.objects.create(id=2, symbol="Production") # Not '1'
        cls.dept_admin = BusinessGroup.objects.create(id=1, symbol="Administration") # '1' for 'NA' logic

        # Create a Material Requisition Master
        cls.mrs_master = MaterialRequisitionMaster.objects.create(
            id=1001,
            mrs_no="MRS/2023/001",
            sys_date=timezone.now(),
            session_id=cls.employee.emp_id,
            comp_id=cls.company.id
        )

        # Create Material Requisition Details
        cls.mrs_detail1 = MaterialRequisitionDetail.objects.create(
            id=1,
            mrs_no="MRS/2023/001",
            item_id=cls.item1.id,
            dept_id=cls.dept_production.id,
            wo_no="WO-PROD-001",
            req_qty=10.555,
            remarks="For manufacturing line",
            m_id=cls.mrs_master.id,
            mrs_master=cls.mrs_master # Link to master object for FK
        )
        cls.mrs_detail2 = MaterialRequisitionDetail.objects.create(
            id=2,
            mrs_no="MRS/2023/001",
            item_id=cls.item2.id,
            dept_id=cls.dept_admin.id, # This should trigger 'NA' for department
            wo_no="WO-ADMIN-001",
            req_qty=5.000,
            remarks="For office use",
            m_id=cls.mrs_master.id,
            mrs_master=cls.mrs_master
        )
        cls.mrs_detail3 = MaterialRequisitionDetail.objects.create(
            id=3,
            mrs_no="MRS/2023/001",
            item_id=cls.item1.id,
            dept_id=None, # Test DBNull.Value case
            wo_no="WO-MISC-001",
            req_qty=2.75,
            remarks="General purpose",
            m_id=cls.mrs_master.id,
            mrs_master=cls.mrs_master
        )
        cls.mrs_detail4 = MaterialRequisitionDetail.objects.create(
            id=4,
            mrs_no="MRS/2023/001",
            item_id=cls.item1.id,
            dept_id=999, # Test non-existent department
            wo_no="WO-BAD-DEPT",
            req_qty=1.0,
            remarks="Bad dept ID",
            m_id=cls.mrs_master.id,
            mrs_master=cls.mrs_master
        )
        cls.mrs_detail5 = MaterialRequisitionDetail.objects.create(
            id=5,
            mrs_no="MRS/2023/001",
            item_id=999, # Test non-existent item
            dept_id=cls.dept_production.id,
            wo_no="WO-BAD-ITEM",
            req_qty=1.0,
            remarks="Bad item ID",
            m_id=cls.mrs_master.id,
            mrs_master=cls.mrs_master
        )


    def test_mrs_master_creation(self):
        self.assertEqual(self.mrs_master.mrs_no, "MRS/2023/001")
        self.assertEqual(self.mrs_master.comp_id, 1)

    def test_mrs_master_mrs_date_dmy(self):
        expected_date = self.mrs_master.sys_date.strftime('%d/%m/%Y')
        self.assertEqual(self.mrs_master.mrs_date_dmy, expected_date)

    def test_mrs_master_generated_by(self):
        self.assertEqual(self.mrs_master.generated_by, "Mr. John Doe")
        # Test case where employee does not exist
        self.mrs_master.session_id = 9999
        self.assertEqual(self.mrs_master.generated_by, "N/A")

    def test_mrs_master_company_address(self):
        self.assertEqual(self.mrs_master.company_address, "123 Main St, Anytown")
        # Test case where company does not exist
        self.mrs_master.comp_id = 9999
        self.assertEqual(self.mrs_master.company_address, "N/A")

    def test_item_uom_property(self):
        self.assertEqual(self.item1.uom, "KG")
        self.assertEqual(self.item2.uom, "MTR")
        # Test item with non-existent UOM
        self.item1.uom_basic_id = 999
        self.assertEqual(self.item1.uom, "N/A")

    def test_mrs_master_get_report_details(self):
        report_details = self.mrs_master.get_report_details()
        self.assertEqual(len(report_details), 5) # All 5 details added

        # Test detail1: Normal case
        detail1_data = next(d for d in report_details if d['id'] == self.mrs_detail1.id)
        self.assertEqual(detail1_data['item_code'], "ITEM001")
        self.assertEqual(detail1_data['description'], "Product A")
        self.assertEqual(detail1_data['uom'], "KG")
        self.assertEqual(detail1_data['department_symbol'], "Production")
        self.assertEqual(detail1_data['wo_no'], "WO-PROD-001")
        self.assertEqual(detail1_data['req_qty'], "10.555")

        # Test detail2: DeptId = 1 (Administration) -> Dept Symbol 'NA', WO No remains
        detail2_data = next(d for d in report_details if d['id'] == self.mrs_detail2.id)
        self.assertEqual(detail2_data['department_symbol'], "NA")
        self.assertEqual(detail2_data['wo_no'], "WO-ADMIN-001")

        # Test detail3: DeptId is None -> Dept Symbol 'NA', WO No remains
        detail3_data = next(d for d in report_details if d['id'] == self.mrs_detail3.id)
        self.assertEqual(detail3_data['department_symbol'], "NA")
        self.assertEqual(detail3_data['wo_no'], "WO-MISC-001")

        # Test detail4: Non-existent DeptId -> Dept Symbol 'NA', WO No remains
        detail4_data = next(d for d in report_details if d['id'] == self.mrs_detail4.id)
        self.assertEqual(detail4_data['department_symbol'], "NA")
        self.assertEqual(detail4_data['wo_no'], "WO-BAD-DEPT")

        # Test detail5: Non-existent Item -> ItemCode/Description/UOM 'N/A'
        detail5_data = next(d for d in report_details if d['id'] == self.mrs_detail5.id)
        self.assertEqual(detail5_data['item_code'], "N/A")
        self.assertEqual(detail5_data['description'], "N/A")
        self.assertEqual(detail5_data['uom'], "N/A")


class MaterialRequisitionViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimum data for views to operate
        cls.company = Company.objects.create(id=1, address="Test Company Address")
        cls.employee = Employee.objects.create(emp_id=101, title="Mr", employee_name="Test User", comp_id=1)
        cls.mrs_master = MaterialRequisitionMaster.objects.create(
            id=1001, mrs_no="MRS/V-001", sys_date=timezone.now(), session_id=cls.employee.emp_id, comp_id=cls.company.id
        )
        cls.unit_kg = Unit.objects.create(id=1, symbol="KG")
        cls.item = Item.objects.create(id=1, item_code="ITEM001", manf_desc="Product A", uom_basic_id=cls.unit_kg.id, comp_id=1)
        cls.mrs_detail = MaterialRequisitionDetail.objects.create(
            id=1, mrs_no="MRS/V-001", item_id=cls.item.id, dept_id=2, wo_no="WO-1", req_qty=5.0, remarks="Test", m_id=cls.mrs_master.id, mrs_master=cls.mrs_master
        )
        
        # Another MRS for isolation tests
        cls.other_mrs_master = MaterialRequisitionMaster.objects.create(
            id=1002, mrs_no="MRS/V-002", sys_date=timezone.now(), session_id=cls.employee.emp_id, comp_id=2 # Different company
        )

    def setUp(self):
        self.client = Client()
        # Simulate ASP.NET session for compid
        session = self.client.session
        session['compid'] = self.company.id
        session.save()
        # Mocking a URL for the cancel button as per instructions
        self.some_other_mrs_list_page_url = "/mrs/list/" 

    def test_mrs_print_details_view_success(self):
        response = self.client.get(reverse('inventory:mrs_print_details', args=[self.mrs_master.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialrequisition/print_details.html')
        self.assertIn('mrs_master', response.context)
        self.assertEqual(response.context['mrs_master'], self.mrs_master)
        self.assertIn('report_details', response.context)
        self.assertGreater(len(response.context['report_details']), 0)
        self.assertContains(response, "Material Requisition Slip [MRS] - Print")
        self.assertContains(response, "MRS/V-001")
        self.assertContains(response, "Test Company Address")

    def test_mrs_print_details_view_no_mrs_found(self):
        # Test with a non-existent MRS ID
        response = self.client.get(reverse('inventory:mrs_print_details', args=[99999]))
        self.assertEqual(response.status_code, 404) # Should raise Http404

    def test_mrs_print_details_view_wrong_company(self):
        # Test trying to access MRS from a different company
        session = self.client.session
        session['compid'] = 9999 # Non-existent company ID
        session.save()
        response = self.client.get(reverse('inventory:mrs_print_details', args=[self.mrs_master.id]))
        self.assertEqual(response.status_code, 404) # Should raise Http404

    def test_mrs_print_details_view_missing_session_compid(self):
        # Test case where compid is missing from session
        session = self.client.session
        del session['compid']
        session.save()
        response = self.client.get(reverse('inventory:mrs_print_details', args=[self.mrs_master.id]))
        self.assertEqual(response.status_code, 404) # Should raise Http404

    def test_mrs_details_table_partial_view_success(self):
        response = self.client.get(reverse('inventory:mrs_details_table_partial', args=[self.mrs_master.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialrequisition/_mrs_details_table.html')
        self.assertIn('mrs_master', response.context)
        self.assertEqual(response.context['mrs_master'], self.mrs_master)
        self.assertIn('report_details', response.context)
        self.assertGreater(len(response.context['report_details']), 0)
        self.assertContains(response, "Item Code") # Check for table header
        self.assertContains(response, "ITEM001") # Check for detail content

    def test_mrs_details_table_partial_view_no_mrs_found(self):
        response = self.client.get(reverse('inventory:mrs_details_table_partial', args=[99999]))
        self.assertEqual(response.status_code, 404) # Should raise Http404

    def test_mrs_details_table_partial_view_htmx_trigger(self):
        # Test that the main view triggers HTMX load
        response = self.client.get(reverse('inventory:mrs_print_details', args=[self.mrs_master.id]))
        self.assertEqual(response.status_code, 200)
        # Check if the HTMX attributes are present to load the table
        self.assertContains(response, 'hx-get="{% url \'inventory:mrs_details_table_partial\' pk=mrs_master.pk %}"')
        self.assertContains(response, 'hx-trigger="load"')
        self.assertContains(response, 'hx-swap="innerHTML"')

```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for Dynamic Content:**
    *   The `print_details.html` template uses `hx-get` on `div#mrs-details-table-container` to load the `_mrs_details_table.html` partial. This ensures that the main page loads quickly, and the potentially large table data is fetched and rendered dynamically without a full page reload.
    *   The `hx-trigger="load"` ensures the table loads automatically when the page is displayed.
*   **Alpine.js for UI State:**
    *   Currently, basic Alpine.js setup is included in `base.html` and `print_details.html`. For this read-only view, complex UI state management is not explicitly needed. If interactive elements (e.g., dynamic filtering options, a printable PDF generation button that shows a loading spinner) were added, Alpine.js would manage their visibility or state.
*   **DataTables for List Views:**
    *   The `_mrs_details_table.html` partial includes a `<table>` element with `id="mrsDetailsTable"`.
    *   A JavaScript block within this partial initializes DataTables on this table using `$(document).ready(function() { $('#mrsDetailsTable').DataTable({...}); });`. This provides client-side searching, sorting, and pagination for the MRS detail items, enhancing user experience.
*   **No Custom JavaScript (beyond DataTables initialization):** All interactions are handled via HTMX attributes or simple browser actions (like the cancel button being a direct link).
*   **DRY Template Inheritance:** `print_details.html` extends `core/base.html`, ensuring all common CDN links (HTMX, Alpine.js, jQuery, DataTables, Tailwind CSS) are included once in `base.html` and reused.

## Final Notes

*   **Placeholders:** Replace `{% url 'some_other_mrs_list_page' %}` with the actual URL name of your MRS list/summary page once it's migrated.
*   **Database Tables:** Ensure the `db_table` names in `models.py` exactly match your existing SQL Server table names. The `id` fields are set as `primary_key=True` to match the existing schema where `Id` is the primary key.
*   **Company ID from Session:** The current implementation assumes `request.session['compid']` exists, replicating the ASP.NET `Session["compid"]` behavior. Ensure your Django authentication/middleware sets this session variable.
*   **Error Handling:** The views include basic error handling for missing parameters or non-existent objects, raising `Http404` and adding `messages.error` for user feedback.
*   **Modularity:** This plan focuses on migrating this specific "print details" functionality. Other modules (e.g., MRS creation, list view, general inventory management) would follow similar structured migration processes.
*   **Report Export:** If the Crystal Report was primarily for PDF generation, you would integrate a library like ReportLab or WeasyPrint within `MaterialRequisitionPrintDetailsView` (or a separate view) to generate a PDF based on `mrs_master.get_report_details()` and serve it as a file download. This can be triggered by an HTMX-powered button.