## ASP.NET to Django Conversion Script:

This document outlines a comprehensive plan for modernizing your existing ASP.NET application, specifically the "Goods Inward Note [GIN] - New" functionality, to a modern Django-based solution. We will leverage Django's powerful ORM, class-based views, and a cutting-edge frontend stack (HTMX + Alpine.js + DataTables) to deliver a highly interactive, efficient, and maintainable web application.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:

-   NEVER include base.html template code in your output - assume it already exists and is extended.
-   Focus ONLY on component-specific code for the current module.
-   Always include complete unit tests for models and integration tests for views.
-   Use modern Django 5.0+ patterns and follow best practices.
-   Keep your code clean, efficient, and avoid redundancy.
-   Always generate complete, runnable Django code.

## AutoERP Guidelines:

-   Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
-   Map models to existing database using `managed = False` and `db_table`.
-   Implement DataTables for client-side searching, sorting, and pagination.
-   Use HTMX for dynamic interactions and Alpine.js for UI state management.
-   All templates should extend `core/base.html` (but DO NOT include `base.html` code).
-   Achieve at least 80% test coverage with unit and integration tests.
-   Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
-   Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code that are central to the "Goods Inward Note" creation process.

**Instructions:**
The ASP.NET code primarily creates records in `tblInv_Inward_Master` (for the GIN header) and `tblInv_Inward_Details` (for each item received). It also interacts with `tblACC_Asset_Category` and `tblACC_Asset_SubCategory` for dropdown selections. The `GridView`'s data is derived from complex joins involving `tblMM_PO_Details`, `tblDG_Item_Master`, `Unit_Master`, and `AccHead`.

**Identified Tables and Inferred Columns:**

**1. `tblInv_Inward_Master` (Django Model: `GoodsInwardNote`)**
    -   `Id` (PK, int)
    -   `SysDate` (date/datetime)
    -   `SysTime` (time/datetime)
    -   `CompId` (int)
    -   `FinYearId` (int)
    -   `SessionId` (varchar)
    -   `GINNo` (varchar, unique)
    -   `PONo` (varchar)
    -   `ChallanNo` (varchar)
    -   `ChallanDate` (date/datetime)
    -   `GateEntryNo` (varchar)
    -   `GDate` (date/datetime)
    -   `GTime` (varchar, stores formatted time)
    -   `ModeofTransport` (varchar)
    -   `VehicleNo` (varchar)
    -   `POMId` (int)

**2. `tblInv_Inward_Details` (Django Model: `GoodsInwardNoteDetail`)**
    -   `Id` (PK, int)
    -   `GINNo` (varchar, typically redundant if FK to `GINId` is used)
    -   `GINId` (int, FK to `tblInv_Inward_Master.Id`)
    -   `POId` (int, refers to `tblMM_PO_Details.Id`)
    -   `Qty` (float/decimal, for Challan Qty)
    -   `ReceivedQty` (float/decimal)
    -   `ACategoyId` (int, FK to `tblACC_Asset_Category.Id`)
    -   `ASubCategoyId` (int, FK to `tblACC_Asset_SubCategory.Id`)

**3. `tblACC_Asset_Category` (Django Model: `AssetCategory`)**
    -   `Id` (PK, int)
    -   `Abbrivation` (varchar)

**4. `tblACC_Asset_SubCategory` (Django Model: `AssetSubCategory`)**
    -   `Id` (PK, int)
    -   `MId` (int, FK to `tblACC_Asset_Category.Id`)
    -   `SubCat` (varchar)

**Auxiliary Data Source Tables (for `GridView` population logic - data fetched, not directly modeled for CRUD here):**
-   `tblMM_PO_Details`, `tblMM_PO_Master`, `tblMM_PR_Details`, `tblMM_PR_Master`, `tblMM_SPR_Details`, `tblMM_SPR_Master`, `tblDG_Item_Master`, `Unit_Master`, `AccHead`. The complex data derivation from these will be encapsulated in a helper class or service for populating `POItemData` objects.

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations and business logic within the ASP.NET code.

**Instructions:**
The ASP.NET page `GoodsInwardNote_GIN_New_PO_Details.aspx` focuses on creating a new Goods Inward Note (GIN) entry based on a Purchase Order (PO).

-   **Read/Display:**
    -   Retrieves `PONo`, `ChallanNo`, `ChallanDate`, `MId` from query string parameters to pre-populate GIN header details.
    -   Performs complex database queries (`loaddata` function) across multiple tables (`tblMM_PO_Details`, `tblDG_Item_Master`, `Unit_Master`, `AccHead`, etc.) to fetch details for PO items, including their item code, description, UOM, and various quantity statuses (`TotRecdQty`, `TotGINQty`, `TotRejQty`, `TotGSNQty`).
    -   Dynamically populates `Category` and `Sub-Category` dropdowns.
    -   Handles file download requests for item images and specification sheets.

-   **Create (Primary Functionality):**
    -   Generates a new sequential `GINNo`.
    -   Collects header information (Mode of Transport, Vehicle No, Gate Entry No, GIN Date/Time).
    -   Allows selection of PO items (via checkboxes) and input of `Challan Qty` and `Received Qty` for each selected item.
    -   Inserts a new record into `tblInv_Inward_Master` for the GIN header.
    -   Inserts multiple associated records into `tblInv_Inward_Details` for each selected PO item, storing the received quantities and, if applicable, the selected asset category/subcategory.

-   **Validation:**
    -   **Required Fields:** `Mode of Transport`, `Vehicle No`, `Gate Entry No`, `GIN Date`.
    -   **Data Format:** `GIN Date` (date format), `Challan Qty`, `Received Qty` (numeric/decimal format).
    -   **Business Logic:** Critical validation that `Received Qty` does not exceed the remaining quantity on the PO (`PO Qty - Total Received Qty`).
    -   **Conditional Required:** `Category` and `Sub-Category` are mandatory if the item's Account Head (`AHId`) is "33" (indicating an asset).

-   **Dynamic UI (HTMX/Alpine.js Candidates):**
    -   Toggling a checkbox on a `GridView` row reveals/hides associated quantity input fields and enables/disables category/subcategory dropdowns.
    -   Selecting a `Category` dynamically populates the `Sub-Category` dropdown.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles to guide Django template and frontend (HTMX/Alpine.js) design.

**Instructions:**
The ASP.NET page is a form-heavy view with a central `GridView` for displaying and interacting with a list of PO items.

-   **Header/Form Section:**
    -   `asp:Label` controls (`LblPONo`, `lblChallanNo`, `LblChallanDate`): Display static PO/Challan header data, read-only.
    -   `asp:TextBox` controls (`TxtModeoftransport`, `TxtVehicleNo`, `TxtGateentryNo`, `TxtGDate`): User input fields for GIN header details.
    -   `cc1:CalendarExtender`: Date picker for `TxtGDate`.
    -   `MKB:TimeSelector`: Time picker control.
    -   `asp:RequiredFieldValidator`, `asp:RegularExpressionValidator`: ASP.NET server-side validation, to be migrated to Django forms and HTMX validation.

-   **Main Data Grid (`GridView1`):**
    -   Acts as an editable table for PO line items. This will be replaced by a Django formset rendered in a table, leveraging DataTables for display.
    -   `asp:CheckBox` (`CheckBox1`): For selecting individual PO items for inwarding. This will trigger HTMX swaps to show/hide relevant input fields within the row.
    -   `asp:Label` controls (`lblId`, `lblAHId`, `lblItemId`, `lblItemCode`, `lblPurChaseDesc`, `lblUOMPurchase`, `lblPOQty`, `lblTotRecdQty`, `lblTotGSNQty`, `lblTotGINQty`, `lblTotRejQty`, `lblChallanQty`, `lblReceivedQty`): Display read-only PO item details.
    -   `asp:LinkButton` (`btnlnkImg`, `btnlnkSpec`): For file downloads, will be converted to standard HTML links or buttons triggering Django views.
    -   `asp:TextBox` controls (`TxtChallanQty`, `TxtReceivedQty`): Input fields for quantities, conditionally visible.
    -   `asp:DropDownList` controls (`ddCategory`, `ddSubCategory`): For selecting asset categories, conditionally visible and enabled.
    -   `asp:SqlDataSource`: Populates `ddCategory`. This data will be fetched via Django ORM.

-   **Action Buttons:**
    -   `asp:Button` (`BtnInsert`): The submit button for creating the GIN. Will trigger an HTMX form submission.
    -   `asp:Button` (`BtnCancel`): Navigates back, will be a standard Django link.

The overall UI flow indicates a single form submission for the header, combined with a dynamic list of detail forms (formset) where specific rows can be enabled for input. HTMX will be used for the checkbox toggles, dynamic dropdown loading, and the main form submission, preventing full page reloads. Alpine.js can manage the modal for displaying the form and basic UI state like loading indicators.

---

## Step 4: Generate Django Code

We will create an `inventory` Django app to house this functionality.

### 4.1 Models (`inventory/models.py`)

This file will define the Django models that map to your existing database tables. We'll use `managed = False` and `db_table` to connect to your current schema.

```python
from django.db import models
from django.utils import timezone
import math # For math.isclose or rounding for float comparisons

class AssetCategory(models.Model):
    # Maps to tblACC_Asset_Category
    id = models.IntegerField(db_column='Id', primary_key=True)
    abbreviation = models.CharField(db_column='Abbrivation', max_length=255)

    class Meta:
        managed = False  # Set to True if Django should manage table creation
        db_table = 'tblACC_Asset_Category'
        verbose_name = 'Asset Category'
        verbose_name_plural = 'Asset Categories'

    def __str__(self):
        return self.abbreviation

class AssetSubCategory(models.Model):
    # Maps to tblACC_Asset_SubCategory
    id = models.IntegerField(db_column='Id', primary_key=True)
    master_category = models.ForeignKey(AssetCategory, models.DO_NOTHING, db_column='MId', related_name='subcategories')
    sub_category_name = models.CharField(db_column='SubCat', max_length=255)

    class Meta:
        managed = False  # Set to True if Django should manage table creation
        db_table = 'tblACC_Asset_SubCategory'
        verbose_name = 'Asset Subcategory'
        verbose_name_plural = 'Asset Subcategories'

    def __str__(self):
        return self.sub_category_name

class GoodsInwardNote(models.Model):
    # Maps to tblInv_Inward_Master
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming Id is an identity column
    sys_date = models.DateField(db_column='SysDate', auto_now_add=True)
    sys_time = models.TimeField(db_column='SysTime', auto_now_add=True)
    company_id = models.IntegerField(db_column='CompId')
    financial_year_id = models.IntegerField(db_column='FinYearId')
    session_id = models.CharField(db_column='SessionId', max_length=255) # Maps to Session["username"]
    gin_number = models.CharField(db_column='GINNo', max_length=50, unique=True)
    po_number = models.CharField(db_column='PONo', max_length=50)
    challan_number = models.CharField(db_column='ChallanNo', max_length=50)
    challan_date = models.DateField(db_column='ChallanDate')
    gate_entry_number = models.CharField(db_column='GateEntryNo', max_length=50)
    gin_date = models.DateField(db_column='GDate')
    gin_time = models.CharField(db_column='GTime', max_length=50) # Storing as CharField to match ASP.NET original format
    mode_of_transport = models.CharField(db_column='ModeofTransport', max_length=255)
    vehicle_number = models.CharField(db_column='VehicleNo', max_length=255)
    po_master_id = models.IntegerField(db_column='POMId') # Refers to tblMM_PO_Master.Id

    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Master'
        verbose_name = 'Goods Inward Note'
        verbose_name_plural = 'Goods Inward Notes'

    def __str__(self):
        return f"GIN {self.gin_number} (PO: {self.po_number})"

    @classmethod
    def generate_next_gin_number(cls, company_id, financial_year_id):
        # This replicates the ASP.NET logic for auto-incrementing GINNo (D4 format)
        # In a robust system, this should use database sequences or a dedicated number generation service.
        last_gin = cls.objects.filter(
            company_id=company_id,
            financial_year_id=financial_year_id
        ).order_by('-gin_number').first()

        if last_gin and last_gin.gin_number.isdigit():
            next_num = int(last_gin.gin_number) + 1
        else:
            next_num = 1
        return f"{next_num:04d}" # Formats as 0001, 0002, etc.

    # Business logic moved from ASP.NET code-behind to model methods (Fat Model)
    def save_gin_and_details(self, details_data, user_session_id, company_id, financial_year_id):
        """
        Saves the Goods Inward Note header and its details in a single transaction.
        This method encapsulates the 'BtnInsert_Click' logic.
        """
        # Set auto-generated fields
        self.gin_number = GoodsInwardNote.generate_next_gin_number(company_id, financial_year_id)
        self.session_id = user_session_id
        self.company_id = company_id
        self.financial_year_id = financial_year_id
        self.sys_date = timezone.localdate()
        self.sys_time = timezone.localtime().time()

        # Save master record
        self.save()

        # Save detail records
        for detail_item in details_data:
            # Create a detail instance
            detail = GoodsInwardNoteDetail(
                gin_master=self,
                gin_number=self.gin_number, # Redundant, but matching original schema
                po_detail_id=detail_item['po_detail_id'],
                challan_quantity=detail_item['challan_quantity'],
                received_quantity=detail_item['received_quantity'],
                asset_category_id=detail_item.get('asset_category_id'),
                asset_subcategory_id=detail_item.get('asset_subcategory_id')
            )
            detail.save()

class GoodsInwardNoteDetail(models.Model):
    # Maps to tblInv_Inward_Details
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming Id is an identity column
    gin_number = models.CharField(db_column='GINNo', max_length=50) # Matching original schema, but GINId is preferred
    gin_master = models.ForeignKey(GoodsInwardNote, models.DO_NOTHING, db_column='GINId', related_name='details')
    po_detail_id = models.IntegerField(db_column='POId') # Refers to tblMM_PO_Details.Id
    challan_quantity = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=5) # Assuming decimal for precision
    received_quantity = models.DecimalField(db_column='ReceivedQty', max_digits=18, decimal_places=5)
    asset_category = models.ForeignKey(AssetCategory, models.DO_NOTHING, db_column='ACategoyId', null=True, blank=True)
    asset_subcategory = models.ForeignKey(AssetSubCategory, models.DO_NOTHING, db_column='ASubCategoyId', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Details'
        verbose_name = 'Goods Inward Note Detail'
        verbose_name_plural = 'Goods Inward Note Details'

    def __str__(self):
        return f"GIN Detail for {self.gin_master.gin_number} (PO Item: {self.po_detail_id})"

# --- Helper for PO Item Data (derived from complex ASP.NET queries) ---
# This class acts as a Data Transfer Object (DTO) or "pseudo-model" for the data displayed in the GridView.
# It encapsulates the complex business logic for calculating quantities and determining item eligibility.
class POItemData:
    """
    A representation of a single Purchase Order item, enriched with calculated
    quantities and eligibility flags, mirroring the data structure created
    by the ASP.NET `loaddata` method for the GridView.
    This data would be populated by a service layer or a custom manager that
    executes the necessary SQL joins and calculations.
    """
    def __init__(self, po_detail_id, item_code, description, uom, po_qty,
                 tot_recd_qty, tot_remain_qty, file_name, att_name, item_id,
                 tot_gin_qty, tot_rej_qty, tot_gsn_qty, ah_id):
        self.po_detail_id = po_detail_id
        self.item_code = item_code
        self.description = description
        self.uom = uom
        self.po_qty = po_qty # Total PO quantity
        self.tot_recd_qty = tot_recd_qty # Total quantity received (GQN Qty)
        self.tot_remain_qty = tot_remain_qty # Total remaining quantity (PO Qty - Tot Recd Qty)
        self.file_name = file_name
        self.att_name = att_name
        self.item_id = item_id
        self.tot_gin_qty = tot_gin_qty # Total GIN Qty
        self.tot_rej_qty = tot_rej_qty # Total Rejected Qty
        self.tot_gsn_qty = tot_gsn_qty # Total GSN Qty (for Labour Category)
        self.ah_id = ah_id # Account Head ID (integer)

    @property
    def can_be_inwarded(self):
        """
        Determines if the item can be selected for inwarding based on ASP.NET's `loaddata` logic.
        This represents the visibility of `CheckBox1`.
        """
        # Using math.isclose for float comparisons to avoid precision issues
        # The original code used round(value, 5) for comparison.
        if math.isclose(self.tot_rej_qty, 0, rel_tol=1e-5): # if TotRejQty == 0
            if math.isclose(self.po_qty - self.tot_gsn_qty, 0, rel_tol=1e-5) or \
               math.isclose(self.po_qty - self.tot_recd_qty, 0, rel_tol=1e-5) or \
               math.isclose(self.po_qty - self.tot_gin_qty, 0, rel_tol=1e-5):
                return False
            else:
                return True
        elif self.tot_rej_qty > 0:
            if math.isclose(self.po_qty - self.tot_gin_qty, 0, rel_tol=1e-5) or \
               (self.po_qty - self.tot_gin_qty < 0 and not math.isclose(self.po_qty - self.tot_gin_qty, 0, rel_tol=1e-5)): # equivalent to <= 0
                if math.isclose(self.po_qty - self.tot_recd_qty, 0, rel_tol=1e-5):
                    return False
                elif math.isclose(self.po_qty - self.tot_gin_qty + self.tot_rej_qty, 0, rel_tol=1e-5):
                    return False
                else:
                    return True
            else:
                return True
        return False # Fallback, if logic doesn't cover all cases

    @property
    def is_asset_account_head(self):
        """
        Returns True if the item's Account Head ID corresponds to an 'Asset' category.
        This determines if Category/SubCategory dropdowns are shown.
        """
        # Assuming '33' is the ID for the 'Labour' / Asset category as per ASP.NET code
        return self.ah_id == 33

    @classmethod
    def get_po_items_for_gin_creation(cls, po_number, po_master_id, company_id, financial_year_id):
        """
        This method simulates the complex data retrieval logic from ASP.NET's `loaddata`.
        In a real application, this would involve direct SQL queries or complex ORM joins
        to the original database tables (tblMM_PO_Details, tblDG_Item_Master, etc.)
        and then transforming that raw data into `POItemData` objects.

        For this example, we return dummy data to demonstrate the structure.
        Replace this with actual database queries.
        """
        # Placeholder for actual data retrieval from DB
        # This is where fun.select, fun.GetItemCode_PartNo, fun.GQNQTY_PO, etc.
        # would be translated into Django ORM queries or raw SQL.
        
        # Example dummy data structure mirroring ASP.NET's DataTable
        data = [
            cls(po_detail_id=1, item_code="ITEM001", description="Widget A", uom="PCS", po_qty=100.0,
                tot_recd_qty=50.0, tot_remain_qty=50.0, file_name="img_a.jpg", att_name="spec_a.pdf",
                item_id=101, tot_gin_qty=50.0, tot_rej_qty=0.0, tot_gsn_qty=0.0, ah_id=1),
            cls(po_detail_id=2, item_code="ITEM002", description="Labor Service", uom="HRS", po_qty=200.0,
                tot_recd_qty=100.0, tot_remain_qty=100.0, file_name="", att_name="",
                item_id=102, tot_gin_qty=100.0, tot_rej_qty=0.0, tot_gsn_qty=50.0, ah_id=33), # AHId 33 for Labour/Asset
            cls(po_detail_id=3, item_code="ITEM003", description="Gadget C", uom="UNIT", po_qty=50.0,
                tot_recd_qty=50.0, tot_remain_qty=0.0, file_name="img_c.png", att_name="",
                item_id=103, tot_gin_qty=50.0, tot_rej_qty=0.0, tot_gsn_qty=0.0, ah_id=2), # Fully received
            cls(po_detail_id=4, item_code="ITEM004", description="Defective Item", uom="PCS", po_qty=30.0,
                tot_recd_qty=10.0, tot_remain_qty=20.0, file_name="", att_name="",
                item_id=104, tot_gin_qty=5.0, tot_rej_qty=5.0, tot_gsn_qty=0.0, ah_id=1)
        ]
        
        # Filter items based on the 'can_be_inwarded' logic derived from ASP.NET
        # The ASP.NET code re-binds the GridView and then iterates rows to hide checkboxes.
        # Here, we filter the list of items to show only those that are "inwardable"
        # or handle their checkbox visibility in the template.
        
        # For simplicity in this DTO, let's assume this method returns all relevant PO items
        # and the template logic (via can_be_inwarded property) handles checkbox visibility.
        return data

```

### 4.2 Forms (`inventory/forms.py`)

Django forms will handle input validation and rendering. We'll use a main form for the GIN header and a formset for the detail items.

```python
from django import forms
from .models import GoodsInwardNote, GoodsInwardNoteDetail, AssetCategory, AssetSubCategory
from datetime import datetime, date

class GoodsInwardNoteForm(forms.ModelForm):
    # Hidden fields for initial context, will be set in view or passed from URL
    po_number = forms.CharField(widget=forms.HiddenInput(), required=False)
    challan_number = forms.CharField(widget=forms.HiddenInput(), required=False)
    challan_date = forms.DateField(widget=forms.HiddenInput(), required=False)
    po_master_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    class Meta:
        model = GoodsInwardNote
        # Fields that will be displayed in the form
        fields = [
            'gate_entry_number', 'gin_date', 'mode_of_transport', 'vehicle_number',
            # 'po_number', 'challan_number', 'challan_date', 'po_master_id' are handled separately as display/context
        ]
        # Widgets for styling and custom attributes
        widgets = {
            'gate_entry_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'gin_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm datepicker', 'placeholder': 'DD-MM-YYYY', 'readonly': 'readonly'}),
            'mode_of_transport': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'value': '-'}),
            'vehicle_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'value': '-'}),
        }

    # Custom field for time, as ASP.NET used a separate TimeSelector control
    gin_time = forms.CharField(
        max_length=50,
        required=True,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm timepicker', 'placeholder': 'HH:MM AM/PM'}),
        initial=datetime.now().strftime('%I:%M %p') # Default to current time
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate initial values from context or URL parameters if they exist
        if 'initial' in kwargs:
            self.fields['po_number'].initial = kwargs['initial'].get('po_number')
            self.fields['challan_number'].initial = kwargs['initial'].get('challan_number')
            self.fields['challan_date'].initial = kwargs['initial'].get('challan_date')
            self.fields['po_master_id'].initial = kwargs['initial'].get('po_master_id')
        
        # Set default values as per ASP.NET (e.g., '-' for text fields)
        if not self.instance.pk: # Only for new instances
            self.fields['mode_of_transport'].initial = '-'
            self.fields['vehicle_number'].initial = '-'
            self.fields['gin_date'].initial = date.today() # Default to current date

    def clean_gin_date(self):
        gin_date = self.cleaned_data['gin_date']
        # ASP.NET used fun.DateValidation, basic Django validation for date type is usually enough.
        # Add custom logic here if specific date formats or ranges are required.
        return gin_date

class GoodsInwardNoteDetailForm(forms.Form):
    # This form is for a single row in the GridView, handling quantities and categories.
    # It does NOT map directly to GoodsInwardNoteDetail model fields for initial display,
    # but collects data to *create* GoodsInwardNoteDetail instances.
    
    # Hidden fields to carry PO item context
    po_detail_id = forms.IntegerField(widget=forms.HiddenInput())
    item_id = forms.IntegerField(widget=forms.HiddenInput()) # ItemId
    ah_id = forms.IntegerField(widget=forms.HiddenInput()) # Account Head ID
    po_qty = forms.DecimalField(max_digits=18, decimal_places=5, widget=forms.HiddenInput())
    tot_recd_qty = forms.DecimalField(max_digits=18, decimal_places=5, widget=forms.HiddenInput())
    tot_gin_qty = forms.DecimalField(max_digits=18, decimal_places=5, widget=forms.HiddenInput())
    tot_rej_qty = forms.DecimalField(max_digits=18, decimal_places=5, widget=forms.HiddenInput())
    tot_gsn_qty = forms.DecimalField(max_digits=18, decimal_places=5, widget=forms.HiddenInput())

    # Checkbox for selection (mimics ASP.NET CheckBox1)
    is_selected = forms.BooleanField(required=False, widget=forms.CheckboxInput(attrs={
        'class': 'form-checkbox h-4 w-4 text-blue-600',
        'hx-post': 'hx-post-url-for-row-toggle', # Placeholder, actual URL will be in template
        'hx-trigger': 'change',
        'hx-swap': 'outerHTML',
        'hx-target': 'closest tr' # Target the whole row for re-rendering
    }))

    # Fields for user input (conditionally visible in template)
    challan_quantity = forms.DecimalField(
        max_digits=18, decimal_places=5, required=False,
        widget=forms.NumberInput(attrs={
            'class': 'block w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm sm:text-sm numeric-input',
            'placeholder': 'Challan Qty',
            'step': '0.001'
        })
    )
    received_quantity = forms.DecimalField(
        max_digits=18, decimal_places=5, required=False,
        widget=forms.NumberInput(attrs={
            'class': 'block w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm sm:text-sm numeric-input',
            'placeholder': 'Received Qty',
            'step': '0.001'
        })
    )

    # Conditional dropdowns for Asset Category
    asset_category = forms.ModelChoiceField(
        queryset=AssetCategory.objects.all(),
        required=False,
        empty_label="Select",
        widget=forms.Select(attrs={
            'class': 'block w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm sm:text-sm',
            'hx-post': 'hx-post-url-for-subcategory', # Placeholder, actual URL in template
            'hx-trigger': 'change',
            'hx-swap': 'outerHTML',
            'hx-target': 'closest .subcategory-field-container' # Target sibling for re-rendering subcategory
        })
    )
    asset_subcategory = forms.ModelChoiceField(
        queryset=AssetSubCategory.objects.none(), # Populated dynamically
        required=False,
        empty_label="Select",
        widget=forms.Select(attrs={
            'class': 'block w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm sm:text-sm'
        })
    )

    def __init__(self, *args, **kwargs):
        is_asset_ah = kwargs.pop('is_asset_ah', False) # Flag passed from view based on ah_id
        super().__init__(*args, **kwargs)

        # Set initial value for Received Quantity based on TotRemainQty
        # from the POItemData for non-asset items by default, if not explicitly set.
        if 'initial' in kwargs and 'tot_remain_qty' in kwargs['initial'] and not self.is_bound:
             self.fields['received_quantity'].initial = kwargs['initial']['tot_remain_qty']
        
        # Populate Asset Category dropdown
        self.fields['asset_category'].queryset = AssetCategory.objects.all()

        # If a category is already selected (e.g., on re-render for subcategory update)
        if 'asset_category' in self.initial and self.initial['asset_category']:
            try:
                category_id = int(self.initial['asset_category'])
                self.fields['asset_subcategory'].queryset = AssetSubCategory.objects.filter(master_category__id=category_id)
            except (ValueError, TypeError):
                pass # Handle cases where category_id is invalid

        # Disable/hide category fields if not an asset AHId, replicating ASP.NET logic
        if not is_asset_ah:
            self.fields['asset_category'].widget.attrs['disabled'] = 'disabled'
            self.fields['asset_subcategory'].widget.attrs['disabled'] = 'disabled'
            # Consider setting required=False permanently for these fields if not an asset
            # Or ensure validation bypasses them
            self.fields['asset_category'].required = False
            self.fields['asset_subcategory'].required = False
        else:
            # Enable if asset AHId, and required if selected
            self.fields['asset_category'].required = True
            self.fields['asset_subcategory'].required = True

    def clean(self):
        cleaned_data = super().clean()
        is_selected = cleaned_data.get('is_selected')
        challan_quantity = cleaned_data.get('challan_quantity')
        received_quantity = cleaned_data.get('received_quantity')
        ah_id = cleaned_data.get('ah_id')
        po_qty = cleaned_data.get('po_qty')
        tot_recd_qty = cleaned_data.get('tot_recd_qty')
        tot_gin_qty = cleaned_data.get('tot_gin_qty')
        tot_rej_qty = cleaned_data.get('tot_rej_qty')

        asset_category = cleaned_data.get('asset_category')
        asset_subcategory = cleaned_data.get('asset_subcategory')

        if is_selected:
            # Replicate ASP.NET's RequiredFieldValidator logic
            if challan_quantity is None:
                self.add_error('challan_quantity', "Challan Qty is required for selected item.")
            if received_quantity is None:
                self.add_error('received_quantity', "Received Qty is required for selected item.")

            # Replicate the core business validation: received_quantity <= remaining_qty
            # (po_qty - tot_recd_qty)
            if challan_quantity is not None and received_quantity is not None:
                remaining_qty = po_qty - (tot_gin_qty - tot_rej_qty) # GIN-Rej from ASP.NET logic in loaddata
                # Correct calculation for remaining quantity based on BtnInsert_Click logic:
                # calpoqty = poqty - totrecdqty; // This was the crucial part in C#
                # if (Math.Round(calpoqty, 5) >= Math.Round(RecvQty, 5))
                cal_remaining_qty = po_qty - tot_recd_qty # Assuming tot_recd_qty is the aggregate received
                
                if received_quantity > cal_remaining_qty and not math.isclose(received_quantity, cal_remaining_qty, rel_tol=1e-5):
                    self.add_error('received_quantity', f"Received Quantity cannot exceed Remaining PO Quantity ({cal_remaining_qty:.3f}).")
            
            # Replicate Category/Subcategory validation for AHId 33 (Asset)
            if ah_id == 33:
                if not asset_category:
                    self.add_error('asset_category', "Category is required for Asset items.")
                if not asset_subcategory:
                    self.add_error('asset_subcategory', "Sub-Category is required for Asset items.")

        # If not selected, clear values and make sure they are not required
        else:
            self.cleaned_data['challan_quantity'] = None
            self.cleaned_data['received_quantity'] = None
            self.cleaned_data['asset_category'] = None
            self.cleaned_data['asset_subcategory'] = None
            # Ensure errors from above don't block submission if unchecked later
            for field_name in ['challan_quantity', 'received_quantity', 'asset_category', 'asset_subcategory']:
                if field_name in self._errors:
                    del self._errors[field_name]

        return cleaned_data

# Formset for handling multiple detail forms (each row in the GridView)
GoodsInwardNoteDetailFormset = forms.formset_factory(GoodsInwardNoteDetailForm, extra=0)
```

### 4.3 Views (`inventory/views.py`)

This file will contain the Class-Based Views (CBVs) for handling the GIN creation process, adhering to the fat model, thin view principle.

```python
from django.views.generic import FormView, View
from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.forms import formset_factory
from .models import GoodsInwardNote, GoodsInwardNoteDetail, AssetCategory, AssetSubCategory, POItemData
from .forms import GoodsInwardNoteForm, GoodsInwardNoteDetailForm, GoodsInwardNoteDetailFormset
import json

class GoodsInwardNoteCreateView(FormView):
    template_name = 'inventory/goods_inward_note/create.html'
    form_class = GoodsInwardNoteForm
    success_url = reverse_lazy('goods_inward_note_list') # Redirect to GIN list after successful creation

    def get_initial(self):
        """
        Populates initial data for the main form from URL query parameters,
        mimicking ASP.NET's Request.QueryString.
        """
        initial = super().get_initial()
        initial['po_number'] = self.request.GET.get('PoNo', '')
        initial['challan_number'] = self.request.GET.get('ChNo', '')
        initial['challan_date'] = self.request.GET.get('ChDt', '') # Format 'dd-MM-yyyy' needs parsing if not YYYY-MM-DD
        initial['po_master_id'] = self.request.GET.get('mid', '')
        return initial

    def get_context_data(self, **kwargs):
        """
        Adds the main form, PO header details, and the PO items formset to the context.
        """
        context = super().get_context_data(**kwargs)
        
        # Initial data for PO header details (for display)
        po_number = self.request.GET.get('PoNo', '')
        challan_number = self.request.GET.get('ChNo', '')
        challan_date_str = self.request.GET.get('ChDt', '')
        
        # Safely parse date if it's in DD-MM-YYYY format, otherwise assume YYYY-MM-DD
        try:
            challan_date = datetime.strptime(challan_date_str, '%d-%m-%Y').date()
        except ValueError:
            challan_date = None # Or handle as error/default if parsing fails
            
        context['po_details'] = {
            'po_number': po_number,
            'challan_number': challan_number,
            'challan_date': challan_date,
        }

        # Populate PO items for the formset (simulates `loaddata` in ASP.NET)
        # This will be passed to the formset, usually from a GET request.
        # When POSTing, formset.is_valid() handles data.
        po_items_data = POItemData.get_po_items_for_gin_creation(
            po_number=po_number,
            po_master_id=self.request.GET.get('mid', ''),
            company_id=self.request.session.get('compid', 1), # Assuming session data
            financial_year_id=self.request.session.get('finyear', 2023) # Assuming session data
        )
        
        # Prepare initial data for each form in the formset
        # ASP.NET's GridView automatically re-populates from DataTable,
        # here we prepare initial for each form in the formset.
        formset_initial_data = []
        for item in po_items_data:
            formset_initial_data.append({
                'po_detail_id': item.po_detail_id,
                'item_id': item.item_id,
                'ah_id': item.ah_id,
                'po_qty': item.po_qty,
                'tot_recd_qty': item.tot_recd_qty,
                'tot_gin_qty': item.tot_gin_qty,
                'tot_rej_qty': item.tot_rej_qty,
                'tot_gsn_qty': item.tot_gsn_qty,
                'is_selected': False, # Default to unchecked on initial load
                'challan_quantity': None, # Initial value
                'received_quantity': item.tot_remain_qty, # Pre-fill with remaining qty as per ASP.NET
            })
        
        if self.request.method == 'POST':
            # When form is submitted, formset is created with POST data
            context['po_items_formset'] = GoodsInwardNoteDetailFormset(
                self.request.POST, 
                prefix='po_details',
                form_kwargs={'is_asset_ah': False} # This needs to be set per form in formset
            )
            # Customizing form_kwargs per form in a formset on POST:
            # This requires iterating through forms in formset if each needs different `is_asset_ah`
            # For simplicity here, the `is_asset_ah` will be dynamically calculated in the template via Alpine.js 
            # and used for conditional display, and validation in `clean` method.
            # Or, we can modify the formset creation if we re-fetch data for is_asset_ah
            
        else:
            # On GET request, create formset with initial data
            formset_kwargs = []
            for item in po_items_data:
                formset_kwargs.append({'is_asset_ah': item.is_asset_account_head})
            
            context['po_items_formset'] = GoodsInwardNoteDetailFormset(
                prefix='po_details', 
                initial=formset_initial_data,
                form_kwargs=formset_kwargs # Pass dynamic kwargs per form
            )
            
        context['po_items_display_data'] = po_items_data # For read-only display fields
        return context

    def form_valid(self, form):
        """
        Handles valid form submission. Creates GIN master and detail records.
        """
        # Create formset with POST data
        po_items_formset = GoodsInwardNoteDetailFormset(self.request.POST, prefix='po_details')
        
        # Check if formset is valid
        if not po_items_formset.is_valid():
            messages.error(self.request, "Please correct the errors in the PO Items.")
            # If formset is invalid, re-render the page with errors
            return self.form_invalid(form)

        # Extract data for selected items from the formset
        selected_details_data = []
        has_selected_items = False
        for detail_form in po_items_formset:
            if detail_form.cleaned_data.get('is_selected'):
                has_selected_items = True
                selected_details_data.append({
                    'po_detail_id': detail_form.cleaned_data['po_detail_id'],
                    'challan_quantity': detail_form.cleaned_data['challan_quantity'],
                    'received_quantity': detail_form.cleaned_data['received_quantity'],
                    'asset_category_id': detail_form.cleaned_data.get('asset_category').id if detail_form.cleaned_data.get('asset_category') else None,
                    'asset_subcategory_id': detail_form.cleaned_data.get('asset_subcategory').id if detail_form.cleaned_data.get('asset_subcategory') else None,
                })
        
        if not has_selected_items:
            messages.error(self.request, "Please select at least one item to inward.")
            return self.form_invalid(form)

        try:
            # Instantiate GoodsInwardNote model with cleaned data
            gin = form.save(commit=False)
            
            # Additional fields derived from query string and session
            gin.po_number = form.cleaned_data['po_number']
            gin.challan_number = form.cleaned_data['challan_number']
            gin.challan_date = form.cleaned_data['challan_date']
            gin.po_master_id = form.cleaned_data['po_master_id']
            gin.gin_time = form.cleaned_data['gin_time'] # Custom field
            
            # Call model method to save master and details in a transaction
            gin.save_gin_and_details(
                selected_details_data,
                self.request.session.get('username', 'system'), # Get user from session
                self.request.session.get('compid', 1), # Get company ID from session
                self.request.session.get('finyear', 2023) # Get financial year from session
            )

            messages.success(self.request, f"Goods Inward Note {gin.gin_number} created successfully.")
            
            # HTMX response for success (e.g., refresh list, close modal)
            if self.request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204, # No Content
                    headers={'HX-Trigger': 'ginCreatedEvent'} # Custom event for frontend
                )
            return super().form_valid(form) # Redirect on regular request

        except Exception as e:
            messages.error(self.request, f"An error occurred during GIN creation: {e}")
            return self.form_invalid(form)

    def form_invalid(self, form):
        """
        Handles invalid form submission. Re-renders the form with errors.
        """
        # If HTMX request, render only the form partial with errors
        if self.request.headers.get('HX-Request'):
            context = self.get_context_data(form=form)
            # Re-create formset with submitted data and errors
            context['po_items_formset'] = GoodsInwardNoteDetailFormset(self.request.POST, prefix='po_details')
            
            return render(self.request, 'inventory/goods_inward_note/_create_form_partial.html', context)
        return super().form_invalid(form)


class SubCategoryView(View):
    """
    Handles dynamic loading of subcategories via HTMX when a category is selected.
    """
    def post(self, request, *args, **kwargs):
        category_id = request.POST.get('asset_category')
        form_prefix = request.POST.get('form_prefix') # To identify which form in the formset
        field_name = request.POST.get('field_name') # The actual field name, e.g., 'asset_subcategory'
        
        subcategories = AssetSubCategory.objects.none()
        if category_id and category_id != 'Select': # 'Select' is usually empty_label value
            try:
                subcategories = AssetSubCategory.objects.filter(master_category__id=category_id)
            except ValueError:
                pass # Invalid category_id, keep subcategories empty

        # Re-render just the subcategory select element for HTMX swap
        # We need to know which form in the formset this belongs to.
        # HTMX targets 'closest .subcategory-field-container' and swaps outerHTML.
        # So we need to render the new select tag with the correct name and ID for that specific form.

        # A common pattern is to re-render the whole form-row or a container holding the field.
        # For simplicity, we'll return a partial HTML for the select field itself.
        # This requires the template to be structured to accept this kind of swap.
        
        # The formset's form structure and prefix are important here.
        # Let's assume the field_name will be like 'po_details-0-asset_subcategory'
        
        # We need to render the specific form element.
        # For simplicity, let's just return JSON and let Alpine.js populate.
        # Or, pass enough context to render the select field.
        
        # To make this truly HTMX-friendly, we need to know the form index.
        # This typically comes from an attribute on the HTMX trigger element (e.g., hx-vals='{"index": "{{ forloop.counter0 }}"}')
        
        # For now, let's return options JSON for Alpine.js to handle.
        # If strict HTMX partial rendering, would render:
        # <select name="po_details-X-asset_subcategory" ...> options </select>
        
        options = [{'id': 0, 'name': 'Select'}] # Add empty option
        for subcat in subcategories:
            options.append({'id': subcat.id, 'name': subcat.sub_category_name})
        
        # This response is for a 'hx-swap="outerHTML"' on the subcategory select element.
        # We need to return the full <select> tag with its correct name attribute.
        # The `name` attribute needs to be `formset_prefix-INDEX-field_name`.
        
        # Let's assume the template passes `data-form-idx` attribute to the category dropdown
        # and we use it to construct the name.
        form_idx = request.POST.get('form_idx')
        select_name = f"{form_prefix}-{form_idx}-{field_name}" if form_idx else field_name

        # Construct the HTML for the select tag
        html_options = '<option value="">Select</option>'
        for subcat in subcategories:
            html_options += f'<option value="{subcat.id}">{subcat.sub_category_name}</option>'
        
        # We need to ensure the class matches the one used by the form widget for styling
        select_html = f'''
            <select id="id_{select_name}" name="{select_name}" 
                    class="block w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm sm:text-sm">
                {html_options}
            </select>
        '''
        return HttpResponse(select_html)

# View for file downloads, replicating ASP.NET's DownloadFile.aspx
class DownloadFileView(View):
    def get(self, request, *args, **kwargs):
        item_id = request.GET.get('Id')
        table_name = request.GET.get('tbl')
        file_data_field = request.GET.get('qfd')
        file_name_field = request.GET.get('qfn')
        content_type_field = request.GET.get('qct')

        # In a real scenario, you'd query the specific table/model and fields.
        # This is a simplified placeholder.
        # Example: if table_name == 'tblDG_Item_Master'
        # item = get_object_or_404(ItemMaster, id=item_id)
        # file_data = getattr(item, file_data_field)
        # file_name = getattr(item, file_name_field)
        # content_type = getattr(item, content_type_field)

        # Placeholder: Return a dummy file for demonstration
        dummy_file_content = b"This is a dummy file content."
        dummy_file_name = "dummy_file.txt"
        dummy_content_type = "text/plain"

        response = HttpResponse(dummy_file_content, content_type=dummy_content_type)
        response['Content-Disposition'] = f'attachment; filename="{dummy_file_name}"'
        return response

```

### 4.4 Templates (`inventory/templates/inventory/goods_inward_note/`)

This section provides the templates for the GIN creation page.

#### `create.html` (Main GIN creation page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6 text-gray-800">Goods Inward Note [GIN] - New</h2>
    
    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <form id="ginForm" method="post" hx-post="{% url 'goods_inward_note_create' %}" hx-swap="outerHTML" hx-target="#mainContentDiv">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <!-- Header Info (read-only, from URL params) -->
                <div>
                    <label class="block text-sm font-medium text-gray-700">PO No:</label>
                    <p class="mt-1 text-lg font-bold text-gray-900">{{ po_details.po_number }}</p>
                    {{ form.po_number }} <!-- Hidden input for form submission -->
                    {{ form.po_master_id }}
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Challan No:</label>
                    <p class="mt-1 text-lg font-bold text-gray-900">{{ po_details.challan_number }}</p>
                    {{ form.challan_number }} <!-- Hidden input for form submission -->
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Challan Date:</label>
                    <p class="mt-1 text-lg font-bold text-gray-900">{{ po_details.challan_date|date:"d-m-Y" }}</p>
                    {{ form.challan_date }} <!-- Hidden input for form submission -->
                </div>

                <!-- Input Fields (from GoodsInwardNoteForm) -->
                <div>
                    <label for="{{ form.mode_of_transport.id_for_label }}" class="block text-sm font-medium text-gray-700">Mode of Transport:</label>
                    {{ form.mode_of_transport }}
                    {% if form.mode_of_transport.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.mode_of_transport.errors }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.vehicle_number.id_for_label }}" class="block text-sm font-medium text-gray-700">Vehicle No:</label>
                    {{ form.vehicle_number }}
                    {% if form.vehicle_number.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.vehicle_number.errors }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.gate_entry_number.id_for_label }}" class="block text-sm font-medium text-gray-700">Gate Entry No:</label>
                    {{ form.gate_entry_number }}
                    {% if form.gate_entry_number.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.gate_entry_number.errors }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.gin_date.id_for_label }}" class="block text-sm font-medium text-gray-700">Date:</label>
                    {{ form.gin_date }}
                    {% if form.gin_date.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.gin_date.errors }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.gin_time.id_for_label }}" class="block text-sm font-medium text-gray-700">Time:</label>
                    {{ form.gin_time }}
                    {% if form.gin_time.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.gin_time.errors }}</p>
                    {% endif %}
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">PO Items</h3>
            <div class="overflow-x-auto border border-gray-200 rounded-lg shadow-sm">
                <!-- HTMX will handle reloading this table -->
                <div id="po-items-table-container">
                    {% include 'inventory/goods_inward_note/_po_items_table.html' with po_items_formset=po_items_formset po_items_display_data=po_items_display_data %}
                </div>
            </div>

            <div class="mt-8 flex justify-center space-x-4">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md shadow-lg transition duration-300">
                    Insert GIN
                </button>
                <a href="{% url 'goods_inward_note_list' %}" class="bg-gray-400 hover:bg-gray-500 text-gray-800 font-bold py-2 px-6 rounded-md shadow-lg transition duration-300">
                    Cancel
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/plugins/timePlugin.js"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

<script>
    document.addEventListener('DOMContentLoaded', function() {
        flatpickr(".datepicker", {
            dateFormat: "d-m-Y", // Match ASP.NET format
            allowInput: false, // Make readonly as per ASP.NET
            disableMobile: true,
        });

        flatpickr(".timepicker", {
            enableTime: true,
            noCalendar: true,
            dateFormat: "H:i K", // H:i for 24hr, K for AM/PM (12-hour with AM/PM)
            time_24hr: false,
            initialHour: new Date().getHours(),
            initialMinute: new Date().getMinutes(),
            minuteIncrement: 1,
        });
    });

    // Handle general messages (e.g., success/error from Django messages framework)
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        const messages = evt.detail.xhr.getResponseHeader('X-Messages');
        if (messages) {
            JSON.parse(messages).forEach(msg => {
                alert(msg.message); // Simple alert, replace with toast notifications
            });
        }
        // If the entire form is swapped due to an invalid submission, reinitialize flatpickr
        if (evt.target.id === 'mainContentDiv') {
            flatpickr(".datepicker", { dateFormat: "d-m-Y", allowInput: false, disableMobile: true });
            flatpickr(".timepicker", { enableTime: true, noCalendar: true, dateFormat: "H:i K", time_24hr: false, minuteIncrement: 1 });
        }
    });

    // Custom event listener for successful GIN creation
    document.body.addEventListener('ginCreatedEvent', function(evt) {
        // Example: Redirect or clear form, show success message
        window.location.href = "{% url 'goods_inward_note_list' %}"; // Redirect to GIN list
    });
</script>

<!-- Alpine.js for dynamic UI state management -->
<script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('poItemRow', (initialSelected, ahId, currentCategory, currentSubcategory) => ({
            isSelected: initialSelected,
            isAssetAH: ahId === 33, // Match ASP.NET's AHId "33" logic
            selectedCategory: currentCategory,
            selectedSubcategory: currentSubcategory,
            subcategories: [], // Populate dynamically

            toggleSelected() {
                this.isSelected = !this.isSelected;
                if (!this.isSelected) {
                    // Reset fields if unchecked, matching ASP.NET
                    this.selectedCategory = '';
                    this.selectedSubcategory = '';
                    this.subcategories = [];
                }
            },

            async loadSubcategories(event) {
                const categoryId = event.target.value;
                this.selectedCategory = categoryId;
                if (!categoryId || categoryId === 'Select' || !this.isAssetAH) {
                    this.subcategories = [];
                    this.selectedSubcategory = '';
                    return;
                }

                // Simulate HTMX request for subcategories
                // In a true HTMX approach, the select itself would trigger the hx-post
                // and hx-swap='outerHTML' would replace the subcategory dropdown directly.
                // For Alpine.js to manage, we fetch JSON.
                const response = await fetch('{% url "load_subcategories" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': document.querySelector('input[name="csrfmiddlewaretoken"]').value
                    },
                    body: `asset_category=${categoryId}&form_prefix=po_details&field_name=asset_subcategory&form_idx=${event.target.dataset.formIdx}`
                });
                const htmlResponse = await response.text();
                // Replace the subcategory select element directly with the HTML from server
                const subcategoryContainer = event.target.closest('td').querySelector('.subcategory-field-container');
                if (subcategoryContainer) {
                    subcategoryContainer.innerHTML = htmlResponse;
                }
                // Since HTMX swap replaced the element, Alpine.js re-initialization is needed.
                // Or ensure the select is managed by Alpine.js if we return JSON.
                // For simplicity, sticking to server-rendered select on HTMX swap.
            }
        }));
    });
</script>
{% endblock %}
```

#### `_po_items_table.html` (Partial for HTMX update of the DataTables section)

```html
<table id="poItemsTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
            <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Image</th>
            <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Spec. Sheet</th>
            <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
            <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sub-Cate</th>
            <th scope="col" class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">PO Qty</th>
            <th scope="col" class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Tot GQN Qty</th>
            <th scope="col" class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Tot GSN Qty</th>
            <th scope="col" class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Tot GIN Qty</th>
            <th scope="col" class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Tot Rej Qty</th>
            <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan Qty</th>
            <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Recd Qty</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for form in po_items_formset %}
        <tr x-data="poItemRow(false, {{ po_items_display_data|get_item:forloop.counter0 }}.ah_id, '{{ form.asset_category.value|default:"" }}', '{{ form.asset_subcategory.value|default:"" }}')"
            {% if not po_items_display_data|get_item:forloop.counter0 %}.can_be_inwarded %} class="opacity-50" {% endif %}>
            
            <td class="py-2 px-4 whitespace-nowrap">{{ forloop.counter }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-center">
                {% if po_items_display_data|get_item:forloop.counter0 %}.can_be_inwarded %}
                    {{ form.is_selected.as_widget }}
                    <input type="hidden" name="{{ form.prefix }}-{{ forloop.counter0 }}-po_detail_id" value="{{ po_items_display_data|get_item:forloop.counter0 }}.po_detail_id">
                    <input type="hidden" name="{{ form.prefix }}-{{ forloop.counter0 }}-item_id" value="{{ po_items_display_data|get_item:forloop.counter0 }}.item_id">
                    <input type="hidden" name="{{ form.prefix }}-{{ forloop.counter0 }}-ah_id" value="{{ po_items_display_data|get_item:forloop.counter0 }}.ah_id">
                    <input type="hidden" name="{{ form.prefix }}-{{ forloop.counter0 }}-po_qty" value="{{ po_items_display_data|get_item:forloop.counter0 }}.po_qty }}">
                    <input type="hidden" name="{{ form.prefix }}-{{ forloop.counter0 }}-tot_recd_qty" value="{{ po_items_display_data|get_item:forloop.counter0 }}.tot_recd_qty }}">
                    <input type="hidden" name="{{ form.prefix }}-{{ forloop.counter0 }}-tot_gin_qty" value="{{ po_items_display_data|get_item:forloop.counter0 }}.tot_gin_qty }}">
                    <input type="hidden" name="{{ form.prefix }}-{{ forloop.counter0 }}-tot_rej_qty" value="{{ po_items_display_data|get_item:forloop.counter0 }}.tot_rej_qty }}">
                    <input type="hidden" name="{{ form.prefix }}-{{ forloop.counter0 }}-tot_gsn_qty" value="{{ po_items_display_data|get_item:forloop.counter0 }}.tot_gsn_qty }}">

                {% else %}
                    <!-- Item cannot be inwarded, checkbox is hidden -->
                    <span class="text-gray-400">N/A</span>
                {% endif %}
            </td>
            <td class="py-2 px-4 whitespace-nowrap text-center">
                {% if po_items_display_data|get_item:forloop.counter0 %}.file_name %}
                    <a href="{% url 'download_file' %}?Id={{ po_items_display_data|get_item:forloop.counter0 }}.item_id}&tbl=tblDG_Item_Master&qfd=FileData&qfn=FileName&qct=ContentType" class="text-blue-600 hover:underline">View</a>
                {% endif %}
            </td>
            <td class="py-2 px-4 whitespace-nowrap text-center">
                {% if po_items_display_data|get_item:forloop.counter0 %}.att_name %}
                    <a href="{% url 'download_file' %}?Id={{ po_items_display_data|get_item:forloop.counter0 }}.item_id}&tbl=tblDG_Item_Master&qfd=AttData&qfn=AttName&qct=AttContentType" class="text-blue-600 hover:underline">View</a>
                {% endif %}
            </td>
            <td class="py-2 px-4 whitespace-nowrap">{{ po_items_display_data|get_item:forloop.counter0 }}.item_code }}</td>
            <td class="py-2 px-4 whitespace-nowrap">{{ po_items_display_data|get_item:forloop.counter0 }}.description }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-center">{{ po_items_display_data|get_item:forloop.counter0 }}.uom }}</td>
            
            <!-- Category and Subcategory fields -->
            <td class="py-2 px-4 whitespace-nowrap" x-show="isAssetAH" :class="{'hidden': !isAssetAH}">
                <div x-show="isSelected" :class="{'hidden': !isSelected}">
                    {{ form.asset_category }}
                    {% if form.asset_category.errors %}<p class="text-red-500 text-xs mt-1">{{ form.asset_category.errors }}</p>{% endif %}
                </div>
                <div x-show="!isSelected" :class="{'hidden': isSelected}">
                    <p class="text-gray-700">N/A</p>
                </div>
            </td>
            <td class="py-2 px-4 whitespace-nowrap" x-show="isAssetAH" :class="{'hidden': !isAssetAH}">
                <div x-show="isSelected" class="subcategory-field-container" :class="{'hidden': !isSelected}">
                    {{ form.asset_subcategory }}
                    {% if form.asset_subcategory.errors %}<p class="text-red-500 text-xs mt-1">{{ form.asset_subcategory.errors }}</p>{% endif %}
                </div>
                <div x-show="!isSelected" :class="{'hidden': isSelected}">
                    <p class="text-gray-700">N/A</p>
                </div>
            </td>
            
            <td class="py-2 px-4 whitespace-nowrap text-right">{{ po_items_display_data|get_item:forloop.counter0 }}.po_qty }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-right">{{ po_items_display_data|get_item:forloop.counter0 }}.tot_recd_qty }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-right">{{ po_items_display_data|get_item:forloop.counter0 }}.tot_gsn_qty }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-right">{{ po_items_display_data|get_item:forloop.counter0 }}.tot_gin_qty }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-right">{{ po_items_display_data|get_item:forloop.counter0 }}.tot_rej_qty }}</td>

            <!-- Challan Qty and Received Qty fields -->
            <td class="py-2 px-4 whitespace-nowrap">
                <div x-show="isSelected" :class="{'hidden': !isSelected}">
                    {{ form.challan_quantity }}
                    {% if form.challan_quantity.errors %}<p class="text-red-500 text-xs mt-1">{{ form.challan_quantity.errors }}</p>{% endif %}
                </div>
                <div x-show="!isSelected" :class="{'hidden': isSelected}">
                    <p class="text-gray-700">{{ po_items_display_data|get_item:forloop.counter0 }}.challan_quantity|default:"-" }}</p>
                </div>
            </td>
            <td class="py-2 px-4 whitespace-nowrap">
                <div x-show="isSelected" :class="{'hidden': !isSelected}">
                    {{ form.received_quantity }}
                    {% if form.received_quantity.errors %}<p class="text-red-500 text-xs mt-1">{{ form.received_quantity.errors }}</p>{% endif %}
                </div>
                <div x-show="!isSelected" :class="{'hidden': isSelected}">
                    <p class="text-gray-700">{{ po_items_display_data|get_item:forloop.counter0 }}.received_quantity|default:"-" }}</p>
                </div>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="16" class="py-4 px-4 text-center text-red-500 font-bold">No data found to display</td>
        </tr>
        {% endfor %}
        {{ po_items_formset.management_form }}
    </tbody>
</table>

<script>
// DataTables initialization
$(document).ready(function() {
    // Only initialize DataTables if it hasn't been initialized on this element already
    if (!$.fn.DataTable.isDataTable('#poItemsTable')) {
        $('#poItemsTable').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "pageLength": 15,
            "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
            "columnDefs": [
                { "orderable": false, "targets": [1, 2, 3] } // Disable sorting for Select, Image, Spec. Sheet
            ]
        });
    } else {
        // If table already exists, destroy and re-create to refresh with new HTMX content
        $('#poItemsTable').DataTable().destroy();
        $('#poItemsTable').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "pageLength": 15,
            "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
            "columnDefs": [
                { "orderable": false, "targets": [1, 2, 3] } 
            ]
        });
    }
});
</script>

{% comment %}
    Custom filter to get item from list by index, as direct list indexing might not work with dot notation
    Use: {{ my_list|get_item:index }}
{% endcomment %}
{% load custom_filters %} 

```

#### `_create_form_partial.html` (Partial for HTMX error re-rendering)

This partial will be rendered by `form_invalid` in `GoodsInwardNoteCreateView` when an HTMX request fails validation. It includes the main form and the PO items table.

```html
{% load custom_filters %}
{% comment %} This partial is swapped into the main content area when form validation fails via HTMX {% endcomment %}

<div id="mainContentDiv">
    {% if messages %}
        <div class="mb-4">
            {% for message in messages %}
                <div class="p-3 {% if message.tags == 'error' %}bg-red-100 border-l-4 border-red-500 text-red-700{% else %}bg-green-100 border-l-4 border-green-500 text-green-700{% endif %} rounded shadow-sm" role="alert">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <h2 class="text-2xl font-bold mb-6 text-gray-800">Goods Inward Note [GIN] - New</h2>
    
    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <form id="ginForm" method="post" hx-post="{% url 'goods_inward_note_create' %}" hx-swap="outerHTML" hx-target="#mainContentDiv">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <!-- Header Info (read-only, from URL params) -->
                <div>
                    <label class="block text-sm font-medium text-gray-700">PO No:</label>
                    <p class="mt-1 text-lg font-bold text-gray-900">{{ po_details.po_number }}</p>
                    {{ form.po_number }} <!-- Hidden input for form submission -->
                    {{ form.po_master_id }}
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Challan No:</label>
                    <p class="mt-1 text-lg font-bold text-gray-900">{{ po_details.challan_number }}</p>
                    {{ form.challan_number }} <!-- Hidden input for form submission -->
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Challan Date:</label>
                    <p class="mt-1 text-lg font-bold text-gray-900">{{ po_details.challan_date|date:"d-m-Y" }}</p>
                    {{ form.challan_date }} <!-- Hidden input for form submission -->
                </div>

                <!-- Input Fields (from GoodsInwardNoteForm) -->
                <div>
                    <label for="{{ form.mode_of_transport.id_for_label }}" class="block text-sm font-medium text-gray-700">Mode of Transport:</label>
                    {{ form.mode_of_transport }}
                    {% if form.mode_of_transport.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.mode_of_transport.errors }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.vehicle_number.id_for_label }}" class="block text-sm font-medium text-gray-700">Vehicle No:</label>
                    {{ form.vehicle_number }}
                    {% if form.vehicle_number.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.vehicle_number.errors }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.gate_entry_number.id_for_label }}" class="block text-sm font-medium text-gray-700">Gate Entry No:</label>
                    {{ form.gate_entry_number }}
                    {% if form.gate_entry_number.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.gate_entry_number.errors }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.gin_date.id_for_label }}" class="block text-sm font-medium text-gray-700">Date:</label>
                    {{ form.gin_date }}
                    {% if form.gin_date.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.gin_date.errors }}</p>
                    {% endif %}
                </div>
                <div>
                    <label for="{{ form.gin_time.id_for_label }}" class="block text-sm font-medium text-gray-700">Time:</label>
                    {{ form.gin_time }}
                    {% if form.gin_time.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.gin_time.errors }}</p>
                    {% endif %}
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">PO Items</h3>
            <div class="overflow-x-auto border border-gray-200 rounded-lg shadow-sm">
                <div id="po-items-table-container">
                    {% include 'inventory/goods_inward_note/_po_items_table.html' with po_items_formset=po_items_formset po_items_display_data=po_items_display_data %}
                </div>
            </div>

            <div class="mt-8 flex justify-center space-x-4">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md shadow-lg transition duration-300">
                    Insert GIN
                </button>
                <a href="{% url 'goods_inward_note_list' %}" class="bg-gray-400 hover:bg-gray-500 text-gray-800 font-bold py-2 px-6 rounded-md shadow-lg transition duration-300">
                    Cancel
                </a>
            </div>
        </form>
    </div>
</div>
```

#### `custom_filters.py` (Inside `templatetags/` directory of your app, e.g., `inventory/templatetags/custom_filters.py`)

```python
from django import template

register = template.Library()

@register.filter
def get_item(list_or_queryset, index):
    """
    Returns the item at the given index from a list or queryset.
    Useful for accessing items in formsets where the order matches display data.
    """
    try:
        return list_or_queryset[index]
    except (IndexError, TypeError):
        return None

```
(Remember to add `{% load custom_filters %}` at the top of your templates that use this filter).

### 4.5 URLs (`inventory/urls.py`)

This file defines the URL patterns for your Django views.

```python
from django.urls import path
from .views import GoodsInwardNoteCreateView, SubCategoryView, DownloadFileView

urlpatterns = [
    # Main GIN creation page (equivalent to GoodsInwardNote_GIN_New_PO_Details.aspx)
    path('gin/new-po-details/', GoodsInwardNoteCreateView.as_view(), name='goods_inward_note_create'),
    
    # Endpoint for dynamic subcategory loading via HTMX
    path('gin/load-subcategories/', SubCategoryView.as_view(), name='load_subcategories'),
    
    # Endpoint for file downloads (mimics DownloadFile.aspx)
    path('download-file/', DownloadFileView.as_view(), name='download_file'),

    # Placeholder for a GIN list view (BtnCancel redirect destination)
    # This would be a separate ListView implementation for GoodsInwardNote model
    path('gin/list/', GoodsInwardNoteCreateView.as_view(), name='goods_inward_note_list'), # Temp placeholder for redirect
]
```

### 4.6 Tests (`inventory/tests.py`)

Comprehensive tests for models and views ensure code quality and maintainability.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import date, time
import math # For comparisons of floating point numbers

from .models import AssetCategory, AssetSubCategory, GoodsInwardNote, GoodsInwardNoteDetail, POItemData

class AssetCategoryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        AssetCategory.objects.create(id=1, abbreviation='RAW')
        AssetCategory.objects.create(id=33, abbreviation='LABOUR') # AHId 33 is significant

    def test_asset_category_creation(self):
        category = AssetCategory.objects.get(id=1)
        self.assertEqual(category.abbreviation, 'RAW')
        self.assertEqual(str(category), 'RAW')

    def test_asset_category_verbose_name(self):
        self.assertEqual(AssetCategory._meta.verbose_name, 'Asset Category')
        self.assertEqual(AssetCategory._meta.verbose_name_plural, 'Asset Categories')

class AssetSubCategoryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        category = AssetCategory.objects.create(id=33, abbreviation='LABOUR')
        AssetSubCategory.objects.create(id=101, master_category=category, sub_category_name='Skilled')
        AssetSubCategory.objects.create(id=102, master_category=category, sub_category_name='Unskilled')

    def test_asset_subcategory_creation(self):
        sub_category = AssetSubCategory.objects.get(id=101)
        self.assertEqual(sub_category.sub_category_name, 'Skilled')
        self.assertEqual(sub_category.master_category.abbreviation, 'LABOUR')
        self.assertEqual(str(sub_category), 'Skilled')

    def test_asset_subcategory_verbose_name(self):
        self.assertEqual(AssetSubCategory._meta.verbose_name, 'Asset Subcategory')
        self.assertEqual(AssetSubCategory._meta.verbose_name_plural, 'Asset Subcategories')

class GoodsInwardNoteModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal related data for testing GIN creation
        cls.company_id = 1
        cls.fin_year_id = 2024
        GoodsInwardNote.objects.create(
            id=1,
            gin_number='0001',
            po_number='PO/2024/001',
            challan_number='CH/2024/001',
            challan_date=date(2024, 1, 1),
            gate_entry_number='GE/001',
            gin_date=date(2024, 1, 2),
            gin_time='10:00 AM',
            mode_of_transport='Road',
            vehicle_number='VEH001',
            po_master_id=10,
            company_id=cls.company_id,
            financial_year_id=cls.fin_year_id,
            session_id='testuser'
        )
        AssetCategory.objects.create(id=1, abbreviation='RAW')
        AssetCategory.objects.create(id=33, abbreviation='LABOUR')
        AssetSubCategory.objects.create(id=101, master_category_id=33, sub_category_name='Skilled')

    def test_gin_creation(self):
        gin = GoodsInwardNote.objects.get(id=1)
        self.assertEqual(gin.gin_number, '0001')
        self.assertEqual(gin.po_number, 'PO/2024/001')
        self.assertEqual(str(gin), 'GIN 0001 (PO: PO/2024/001)')

    def test_generate_next_gin_number(self):
        next_gin_number = GoodsInwardNote.generate_next_gin_number(self.company_id, self.fin_year_id)
        self.assertEqual(next_gin_number, '0002') # Based on 0001 existing

        # Test for empty GINs
        GoodsInwardNote.objects.all().delete()
        next_gin_number_new = GoodsInwardNote.generate_next_gin_number(self.company_id, self.fin_year_id)
        self.assertEqual(next_gin_number_new, '0001')

    def test_save_gin_and_details(self):
        initial_gin_count = GoodsInwardNote.objects.count()
        initial_detail_count = GoodsInwardNoteDetail.objects.count()

        gin_data = GoodsInwardNote(
            po_number='PO/2024/002',
            challan_number='CH/2024/002',
            challan_date=date(2024, 1, 3),
            gate_entry_number='GE/002',
            gin_date=date(2024, 1, 4),
            gin_time='11:00 AM',
            mode_of_transport='Air',
            vehicle_number='PLANE01',
            po_master_id=11
        )
        
        details_data = [
            {
                'po_detail_id': 20,
                'challan_quantity': 10.000,
                'received_quantity': 9.500,
                'asset_category_id': None,
                'asset_subcategory_id': None
            },
            {
                'po_detail_id': 21,
                'challan_quantity': 50.000,
                'received_quantity': 45.000,
                'asset_category_id': 33,
                'asset_subcategory_id': 101
            }
        ]

        gin_data.save_gin_and_details(details_data, 'newuser', self.company_id, self.fin_year_id)

        self.assertEqual(GoodsInwardNote.objects.count(), initial_gin_count + 1)
        self.assertEqual(GoodsInwardNoteDetail.objects.count(), initial_detail_count + 2)

        new_gin = GoodsInwardNote.objects.get(gin_number='0002') # Assuming 0001 existed already
        self.assertEqual(new_gin.po_number, 'PO/2024/002')
        self.assertEqual(new_gin.details.count(), 2)
        
        detail1 = new_gin.details.get(po_detail_id=20)
        self.assertEqual(detail1.challan_quantity, 10.000)
        self.assertEqual(detail1.received_quantity, 9.500)

class POItemDataTest(TestCase):
    def test_can_be_inwarded_logic(self):
        # Case 1: All quantities zero, should be inwardable (assuming it means "not fully received")
        item1 = POItemData(1, 'A', 'Desc', 'UOM', 100, 0, 100, '', '', 1, 0, 0, 0, 1)
        self.assertTrue(item1.can_be_inwarded)

        # Case 2: Fully GIN-ed, should not be inwardable
        item2 = POItemData(2, 'B', 'Desc', 'UOM', 100, 100, 0, '', '', 2, 100, 0, 0, 1)
        self.assertFalse(item2.can_be_inwarded)

        # Case 3: Fully GSN-ed (for Labour category), should not be inwardable
        item3 = POItemData(3, 'C', 'Desc', 'UOM', 100, 50, 50, '', '', 3, 50, 0, 100, 33) # tot_gsn_qty makes it not inwardable
        self.assertFalse(item3.can_be_inwarded)
        
        # Case 4: Item with rejection, but still remaining quantity
        item4 = POItemData(4, 'D', 'Desc', 'UOM', 100, 90, 10, '', '', 4, 80, 10, 0, 1)
        self.assertTrue(item4.can_be_inwarded)
        
        # Case 5: Item with rejection, no remaining quantity after GIN
        item5 = POItemData(5, 'E', 'Desc', 'UOM', 100, 100, 0, '', '', 5, 100, 5, 0, 1)
        self.assertFalse(item5.can_be_inwarded)

    def test_is_asset_account_head(self):
        item_asset = POItemData(1, 'A', 'D', 'U', 1, 0, 1, '', '', 1, 0, 0, 0, 33)
        self.assertTrue(item_asset.is_asset_account_head)

        item_non_asset = POItemData(2, 'B', 'D', 'U', 1, 0, 1, '', '', 2, 0, 0, 0, 1)
        self.assertFalse(item_non_asset.is_asset_account_head)

class GoodsInwardNoteCreateViewTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Mock session data and required related objects
        self.session = self.client.session
        self.session['username'] = 'testuser'
        self.session['compid'] = 1
        self.session['finyear'] = 2024
        self.session.save()

        # Create dummy Asset Categories/Subcategories for dropdowns
        self.category_raw = AssetCategory.objects.create(id=1, abbreviation='RAW')
        self.category_labour = AssetCategory.objects.create(id=33, abbreviation='LABOUR')
        self.sub_category_skilled = AssetSubCategory.objects.create(id=101, master_category=self.category_labour, sub_category_name='Skilled')
        self.sub_category_unskilled = AssetSubCategory.objects.create(id=102, master_category=self.category_labour, sub_category_name='Unskilled')

        self.url = reverse('goods_inward_note_create')
        self.query_params = '?PoNo=PO123&ChNo=CH456&ChDt=01-01-2024&mid=789'
        self.full_url = f"{self.url}{self.query_params}"

    def test_get_request(self):
        response = self.client.get(self.full_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goods_inward_note/create.html')
        
        # Test initial form values
        self.assertContains(response, 'PO123')
        self.assertContains(response, 'CH456')
        self.assertContains(response, '01-01-2024')
        self.assertContains(response, '<input type="hidden" name="po_number" value="PO123"')
        self.assertContains(response, '<input type="hidden" name="po_master_id" value="789"')

        # Check formset management form is present
        self.assertContains(response, 'id="id_po_details-TOTAL_FORMS"')
        self.assertContains(response, 'id="id_po_details-INITIAL_FORMS"')
        
        # Check initial display of PO items (from POItemData mock)
        self.assertContains(response, 'Widget A')
        self.assertContains(response, 'Labor Service')
        
        # Verify hidden/visible states based on is_asset_ah and is_selected
        # This is more complex to test directly in HTML, but can check form field attributes.
        # e.g., for 'Labor Service' (AHId 33), category dropdown should be shown when selected in template.

    def test_post_request_valid_data(self):
        # Mock POItemData for the formset processing
        # Ensure our mock POItemData.get_po_items_for_gin_creation matches the expected data.
        # For simplicity, using a specific mock here that matches the data in POItemData class
        # (Widget A, Labor Service, Gadget C, Defective Item)
        
        # Data for GoodsInwardNoteForm
        post_data = {
            'po_number': 'PO123',
            'challan_number': 'CH456',
            'challan_date': '01-01-2024',
            'po_master_id': '789',
            'gate_entry_number': 'GE123',
            'gin_date': '02-01-2024',
            'gin_time': '10:30 AM',
            'mode_of_transport': 'Road',
            'vehicle_number': 'ABC-123',
            
            # Formset data for two selected items
            'po_details-TOTAL_FORMS': '4',
            'po_details-INITIAL_FORMS': '4',
            'po_details-MIN_NUM_FORMS': '0',
            'po_details-MAX_NUM_FORMS': '1000',

            # Item 1: Widget A (index 0 in POItemData.get_po_items_for_gin_creation)
            'po_details-0-po_detail_id': '1',
            'po_details-0-item_id': '101',
            'po_details-0-ah_id': '1', # Not an asset AHId
            'po_details-0-po_qty': '100.0',
            'po_details-0-tot_recd_qty': '50.0',
            'po_details-0-tot_gin_qty': '50.0',
            'po_details-0-tot_rej_qty': '0.0',
            'po_details-0-tot_gsn_qty': '0.0',
            'po_details-0-is_selected': 'on', # Selected
            'po_details-0-challan_quantity': '20.000',
            'po_details-0-received_quantity': '15.000',
            'po_details-0-asset_category': '', # Not applicable, but present
            'po_details-0-asset_subcategory': '', # Not applicable, but present

            # Item 2: Labor Service (index 1 in POItemData.get_po_items_for_gin_creation)
            'po_details-1-po_detail_id': '2',
            'po_details-1-item_id': '102',
            'po_details-1-ah_id': '33', # Asset AHId
            'po_details-1-po_qty': '200.0',
            'po_details-1-tot_recd_qty': '100.0',
            'po_details-1-tot_gin_qty': '100.0',
            'po_details-1-tot_rej_qty': '0.0',
            'po_details-1-tot_gsn_qty': '50.0',
            'po_details-1-is_selected': 'on', # Selected
            'po_details-1-challan_quantity': '30.000',
            'po_details-1-received_quantity': '25.000', # Received Qty (25) <= Remaining (100)
            'po_details-1-asset_category': self.category_labour.id, # Required for AHId 33
            'po_details-1-asset_subcategory': self.sub_category_skilled.id, # Required for AHId 33

            # Item 3: Gadget C (index 2 in POItemData - not selected, or fully received)
            'po_details-2-po_detail_id': '3',
            'po_details-2-item_id': '103',
            'po_details-2-ah_id': '2',
            'po_details-2-po_qty': '50.0',
            'po_details-2-tot_recd_qty': '50.0',
            'po_details-2-tot_gin_qty': '50.0',
            'po_details-2-tot_rej_qty': '0.0',
            'po_details-2-tot_gsn_qty': '0.0',
            'po_details-2-is_selected': '', # Not selected
            'po_details-2-challan_quantity': '',
            'po_details-2-received_quantity': '',
            'po_details-2-asset_category': '',
            'po_details-2-asset_subcategory': '',
            
            # Item 4: Defective Item (index 3 in POItemData - not selected)
            'po_details-3-po_detail_id': '4',
            'po_details-3-item_id': '104',
            'po_details-3-ah_id': '1',
            'po_details-3-po_qty': '30.0',
            'po_details-3-tot_recd_qty': '10.0',
            'po_details-3-tot_gin_qty': '5.0',
            'po_details-3-tot_rej_qty': '5.0',
            'po_details-3-tot_gsn_qty': '0.0',
            'po_details-3-is_selected': '', # Not selected
            'po_details-3-challan_quantity': '',
            'po_details-3-received_quantity': '',
            'po_details-3-asset_category': '',
            'po_details-3-asset_subcategory': '',
        }
        
        response = self.client.post(self.full_url, post_data, follow=True)
        
        self.assertEqual(response.status_code, 200) # Should redirect to list view upon success
        self.assertRedirects(response, reverse('goods_inward_note_list'))
        
        # Verify GIN Master record created
        gin_master = GoodsInwardNote.objects.order_by('-id').first()
        self.assertIsNotNone(gin_master)
        self.assertEqual(gin_master.po_number, 'PO123')
        self.assertEqual(gin_master.gate_entry_number, 'GE123')
        self.assertEqual(gin_master.details.count(), 2) # Two detail items created

        # Verify GIN Detail records
        detail1 = gin_master.details.get(po_detail_id=1)
        self.assertEqual(detail1.challan_quantity, 20.000)
        self.assertEqual(detail1.received_quantity, 15.000)

        detail2 = gin_master.details.get(po_detail_id=2)
        self.assertEqual(detail2.challan_quantity, 30.000)
        self.assertEqual(detail2.received_quantity, 25.000)
        self.assertEqual(detail2.asset_category_id, self.category_labour.id)
        self.assertEqual(detail2.asset_subcategory_id, self.sub_category_skilled.id)

    def test_post_request_invalid_data_quantity_exceeds(self):
        post_data = {
            'po_number': 'PO123', 'challan_number': 'CH456', 'challan_date': '01-01-2024', 'po_master_id': '789',
            'gate_entry_number': 'GE123', 'gin_date': '02-01-2024', 'gin_time': '10:30 AM',
            'mode_of_transport': 'Road', 'vehicle_number': 'ABC-123',
            
            'po_details-TOTAL_FORMS': '1', 'po_details-INITIAL_FORMS': '1',
            'po_details-MIN_NUM_FORMS': '0', 'po_details-MAX_NUM_FORMS': '1000',

            # Item 1: Widget A, but received_quantity > remaining (50.0)
            'po_details-0-po_detail_id': '1', 'po_details-0-item_id': '101', 'po_details-0-ah_id': '1',
            'po_details-0-po_qty': '100.0', 'po_details-0-tot_recd_qty': '50.0', 'po_details-0-tot_gin_qty': '50.0',
            'po_details-0-tot_rej_qty': '0.0', 'po_details-0-tot_gsn_qty': '0.0',
            'po_details-0-is_selected': 'on',
            'po_details-0-challan_quantity': '60.000',
            'po_details-0-received_quantity': '51.000', # This should trigger an error
            'po_details-0-asset_category': '',
            'po_details-0-asset_subcategory': '',
        }
        
        response = self.client.post(self.full_url, post_data)
        self.assertEqual(response.status_code, 200) # Should return 200 with errors
        self.assertContains(response, "Received Quantity cannot exceed Remaining PO Quantity (50.000).")
        self.assertEqual(GoodsInwardNote.objects.count(), 0) # No GIN should be created

    def test_post_request_invalid_data_missing_asset_category(self):
        post_data = {
            'po_number': 'PO123', 'challan_number': 'CH456', 'challan_date': '01-01-2024', 'po_master_id': '789',
            'gate_entry_number': 'GE123', 'gin_date': '02-01-2024', 'gin_time': '10:30 AM',
            'mode_of_transport': 'Road', 'vehicle_number': 'ABC-123',
            
            'po_details-TOTAL_FORMS': '1', 'po_details-INITIAL_FORMS': '1',
            'po_details-MIN_NUM_FORMS': '0', 'po_details-MAX_NUM_FORMS': '1000',

            # Item 2: Labor Service (AHId 33), but missing asset_category
            'po_details-0-po_detail_id': '2', 'po_details-0-item_id': '102', 'po_details-0-ah_id': '33',
            'po_details-0-po_qty': '200.0', 'po_details-0-tot_recd_qty': '100.0', 'po_details-0-tot_gin_qty': '100.0',
            'po_details-0-tot_rej_qty': '0.0', 'po_details-0-tot_gsn_qty': '50.0',
            'po_details-0-is_selected': 'on',
            'po_details-0-challan_quantity': '30.000',
            'po_details-0-received_quantity': '25.000',
            'po_details-0-asset_category': '', # Missing category
            'po_details-0-asset_subcategory': '',
        }
        
        response = self.client.post(self.full_url, post_data)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Category is required for Asset items.")
        self.assertEqual(GoodsInwardNote.objects.count(), 0)

    def test_htmx_post_request_valid_data(self):
        post_data = {
            'po_number': 'PO123', 'challan_number': 'CH456', 'challan_date': '01-01-2024', 'po_master_id': '789',
            'gate_entry_number': 'GE123', 'gin_date': '02-01-2024', 'gin_time': '10:30 AM',
            'mode_of_transport': 'Road', 'vehicle_number': 'ABC-123',
            
            'po_details-TOTAL_FORMS': '1', 'po_details-INITIAL_FORMS': '1',
            'po_details-MIN_NUM_FORMS': '0', 'po_details-MAX_NUM_FORMS': '1000',

            'po_details-0-po_detail_id': '1', 'po_details-0-item_id': '101', 'po_details-0-ah_id': '1',
            'po_details-0-po_qty': '100.0', 'po_details-0-tot_recd_qty': '50.0', 'po_details-0-tot_gin_qty': '50.0',
            'po_details-0-tot_rej_qty': '0.0', 'po_details-0-tot_gsn_qty': '0.0',
            'po_details-0-is_selected': 'on',
            'po_details-0-challan_quantity': '20.000',
            'po_details-0-received_quantity': '15.000',
            'po_details-0-asset_category': '',
            'po_details-0-asset_subcategory': '',
        }

        response = self.client.post(self.full_url, post_data, HTTP_HX_Request='true')
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('ginCreatedEvent', response.headers['HX-Trigger'])
        self.assertEqual(GoodsInwardNote.objects.count(), 1) # GIN created

    def test_htmx_post_request_invalid_data(self):
        post_data = {
            'po_number': 'PO123', 'challan_number': 'CH456', 'challan_date': '01-01-2024', 'po_master_id': '789',
            'gate_entry_number': '', # Missing required field
            'gin_date': '02-01-2024', 'gin_time': '10:30 AM',
            'mode_of_transport': 'Road', 'vehicle_number': 'ABC-123',
            
            'po_details-TOTAL_FORMS': '0', 'po_details-INITIAL_FORMS': '0',
            'po_details-MIN_NUM_FORMS': '0', 'po_details-MAX_NUM_FORMS': '1000',
        }
        
        response = self.client.post(self.full_url, post_data, HTTP_HX_Request='true')
        self.assertEqual(response.status_code, 200) # Should return 200 with HTML partial
        self.assertTemplateUsed(response, 'inventory/goods_inward_note/_create_form_partial.html')
        self.assertContains(response, "This field is required.") # Error message for gate_entry_number
        self.assertEqual(GoodsInwardNote.objects.count(), 0)

class SubCategoryViewTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.category = AssetCategory.objects.create(id=33, abbreviation='LABOUR')
        self.sub_category1 = AssetSubCategory.objects.create(id=101, master_category=self.category, sub_category_name='Skilled')
        self.sub_category2 = AssetSubCategory.objects.create(id=102, master_category=self.category, sub_category_name='Unskilled')
        self.url = reverse('load_subcategories')

    def test_post_request_load_subcategories(self):
        post_data = {
            'asset_category': self.category.id,
            'form_prefix': 'po_details',
            'field_name': 'asset_subcategory',
            'form_idx': '0'
        }
        response = self.client.post(self.url, post_data)
        self.assertEqual(response.status_code, 200)
        self.assertIn('<option value="">Select</option>', response.content.decode())
        self.assertIn(f'<option value="{self.sub_category1.id}">Skilled</option>', response.content.decode())
        self.assertIn(f'<option value="{self.sub_category2.id}">Unskilled</option>', response.content.decode())
        self.assertIn(f'name="po_details-0-asset_subcategory"', response.content.decode())

    def test_post_request_invalid_category(self):
        post_data = {
            'asset_category': '999', # Non-existent category
            'form_prefix': 'po_details',
            'field_name': 'asset_subcategory',
            'form_idx': '0'
        }
        response = self.client.post(self.url, post_data)
        self.assertEqual(response.status_code, 200)
        self.assertIn('<option value="">Select</option>', response.content.decode())
        self.assertNotIn('Skilled', response.content.decode())

class DownloadFileViewTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.url = reverse('download_file')

    def test_download_file_view(self):
        query_params = '?Id=1&tbl=tblDG_Item_Master&qfd=FileData&qfn=FileName&qct=ContentType'
        response = self.client.get(f'{self.url}{query_params}')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/plain')
        self.assertIn('attachment; filename="dummy_file.txt"', response['Content-Disposition'])
        self.assertEqual(response.content, b"This is a dummy file content.")
```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**

-   **HTMX for dynamic updates:**
    -   The main form submission (`ginForm`) uses `hx-post`, `hx-swap="outerHTML"`, `hx-target="#mainContentDiv"` to re-render the entire form (or redirect on success) without a full page reload, including displaying validation errors inline.
    -   The `poItemsTable` is included as a partial. If the entire `mainContentDiv` is swapped, the DataTables will re-initialize on `DOMContentLoaded`.
    -   The `is_selected` checkbox in `_po_items_table.html` uses `x-data` from Alpine.js to manage its state and conditionally show/hide fields. While `hx-post` + `hx-swap` on `change` could re-render the row, Alpine.js handles it purely client-side for immediate UI feedback.
    -   The `asset_category` dropdown uses `hx-post` to `{% url 'load_subcategories' %}` which returns an HTML `<select>` element. `hx-swap="outerHTML"` and `hx-target="closest .subcategory-field-container"` are used to replace only the subcategory dropdown dynamically.

-   **Alpine.js for UI state management:**
    -   `x-data="poItemRow(...)"` is used on each table row (`<tr>`) to manage the `isSelected`, `isAssetAH`, and visibility of conditional fields (`challan_quantity`, `received_quantity`, `asset_category`, `asset_subcategory`). This replaces the ASP.NET `CheckBox1_CheckedChanged` server-side postback with client-side reactivity.
    -   `x-show="isSelected"` and `:class="{'hidden': !isSelected}"` are used for conditional visibility.
    -   Alpine.js could also manage modal pop-ups for confirmation/status messages, though Django's `messages` framework and simple `alert` are used for this example.

-   **DataTables for list views:**
    -   The `_po_items_table.html` partial includes DataTables initialization script. It correctly handles re-initialization if the partial is swapped via HTMX. This provides client-side searching, sorting, and pagination without server-side processing for this specific data grid.

-   **No additional JavaScript:**
    -   All dynamic interactions are handled by HTMX and Alpine.js. Standard JavaScript libraries like `Flatpickr` are used for date/time pickers as they provide specialized UI widgets not natively covered by HTML/Alpine.js.

-   **DRY template inheritance:**
    -   `create.html` extends `core/base.html`. All CDN links for HTMX, Alpine.js, Flatpickr, and jQuery/DataTables are expected to be in `core/base.html` or loaded in `extra_js` block.

-   **Business logic in models only:**
    -   The `GoodsInwardNote.save_gin_and_details` method encapsulates the entire transaction logic for GIN creation, including generating the GIN number and saving master and detail records.
    -   The `POItemData` class (and its `can_be_inwarded` and `is_asset_account_head` properties) encapsulates the complex item eligibility and display logic, keeping the view logic thin.
    -   Django form `clean` methods (`GoodsInwardNoteDetailForm.clean`) handle all validation that was previously in the ASP.NET code-behind's button click and `GetValidate` methods.

## Final Notes

This comprehensive plan transforms your ASP.NET Goods Inward Note functionality into a robust, modern Django application. It prioritizes automated conversion concepts by mapping ASP.NET patterns to Django equivalents, leveraging modern frontend technologies, and adhering to strict architectural guidelines for maintainability and scalability.

Remember to configure your Django `settings.py` to include the `inventory` app, connect to your existing database, and potentially configure the `MESSAGE_STORAGE` for displaying Django messages properly. You will also need to add your `MEDIA_ROOT` and `MEDIA_URL` settings for file serving once you convert the ASP.NET file storage mechanism (likely from DB `VARBINARY` to file system or cloud storage).