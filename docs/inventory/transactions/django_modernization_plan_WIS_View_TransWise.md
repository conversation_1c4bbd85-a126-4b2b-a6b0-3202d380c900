## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

This plan outlines the modernization of the `WIS_View_TransWise.aspx` and its code-behind to a modern Django application. The original ASP.NET page displays a list of 'WIS' (Work-in-Site or Work-in-Progress) transactions filtered by a Work Order Number (WONo) and allows navigation to a detail/print view.

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

The ASP.NET code utilizes a stored procedure `Sp_WIS_Trans_Grid` to fetch data and binds it to `GridView2`. The columns displayed in the GridView give us direct insight into the underlying data structure.

-   **Stored Procedure:** `Sp_WIS_Trans_Grid` (accepts `@CompId`, `@FinId`, `@x` (WONo) parameters)
-   **Inferred Table Name:** While not explicitly stated, common ERP patterns suggest a table like `tbl_wis_transactions`. We will use this as a placeholder.
-   **Identified Columns (from `GridView2` `Eval` expressions):**
    *   `Id` (Hidden, likely Primary Key, integer)
    *   `WISNo` (string, `LblWISNo`)
    *   `SysDate` (date, `LblDate`)
    *   `SysTime` (time, `LblTime`)
    *   `TaskTargetTryOut_FDate` (date, `LbltryoutFDate`)
    *   `TaskTargetTryOut_TDate` (date, `LbltryoutTDate`)
    *   `TaskTargetDespach_FDate` (date, `LblDFDate`)
    *   `TaskTargetDespach_TDate` (date, `LblDTDate`)
    *   `GenBy` (string, `LblGenBy`)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

-   **Create:** No direct 'create' functionality is observed on this page. It's purely a display/list view.
-   **Read:** This page's primary function is to `Read` and display a list of WIS transactions. The `loadgrid()` method and `Sp_WIS_Trans_Grid` are responsible for this. Filtering is based on `WONo`, `CompId`, and `FinYearId`.
-   **Update:** No direct 'update' functionality is observed.
-   **Delete:** No direct 'delete' functionality is observed.
-   **Navigation/Action:**
    *   **"Select" LinkButton:** Triggers `GridView2_RowCommand` to navigate to `WIS_View_TransWise_print.aspx` for a specific WIS record (using `WISNo`, `WISId`). This implies a detail/print view.
    *   **"Cancel" Button:** Triggers `Cancel_Click` to navigate back to `WIS_Dry_Actual_Run.aspx` or `WIS_ActualRun_Print.aspx` based on a 'status' query parameter.
    *   **Pagination:** `GridView2_PageIndexChanging` handles pagination.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

-   **`asp:Label ID="LblWONo"`:** Displays the Work Order Number, passed via query string. This will be a context variable in Django.
-   **`asp:Panel ID="Panel1"`:** A container for `GridView2`, likely for styling/scrolling. In Django, this will be a `div`.
-   **`asp:GridView ID="GridView2"`:** The main data display control. This will be replaced by an HTML `<table>` element integrated with DataTables for client-side features (pagination, search, sort). Each `asp:TemplateField` corresponds to a table column.
    *   `SN`: Row counter.
    *   `Select` (LinkButton): An action column with a button/link to view details.
    *   Other fields are direct data displays.
-   **`asp:Button ID="Cancel"`:** A navigation button. This will be an `<a>` tag or `<button>` with a `hx-get` or simple `href` in Django.

## Step 4: Generate Django Code

The Django application will be named `inventory`, reflecting the module `Module_Inventory_Transactions`. The specific component relates to `WIS_Transaction` viewing.

### 4.1 Models

Task: Create a Django model based on the database schema.

## Instructions:

The `WisTransaction` model represents the data retrieved from the `Sp_WIS_Trans_Grid` stored procedure. We'll set `managed = False` and `db_table` to instruct Django that this model maps to an existing table in the database and doesn't manage its schema. We'll add a custom manager to encapsulate the logic for filtering based on `CompId`, `FinYearId`, and `WONo`, mimicking the stored procedure's behavior.

## `inventory/models.py`

```python
from django.db import models

class WisTransactionManager(models.Manager):
    """
    Custom manager for WisTransaction to encapsulate data retrieval logic
    that would typically reside in a stored procedure.
    """
    def get_transactions_by_wono(self, comp_id, fin_year_id, wono):
        """
        Retrieves WIS transactions filtered by Company ID, Financial Year ID,
        and Work Order Number. This method simulates the Sp_WIS_Trans_Grid
        stored procedure's behavior by applying filters.
        
        In a real-world scenario, if the stored procedure logic is complex
        and cannot be easily replicated by Django ORM, this method would
        directly execute the stored procedure using Django's database cursor.
        
        Example using raw SQL if SP is complex:
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("EXEC Sp_WIS_Trans_Grid @CompId=%s, @FinId=%s, @x=%s", [comp_id, fin_year_id, wono])
            # Assuming the stored procedure returns all necessary columns in order
            # You would need to map these results to model instances manually
            # or use a more sophisticated library for SP execution.
            # For simplicity, we assume direct ORM mapping for this example.
        """
        
        # Simulate SP filtering based on available model fields and parameters.
        # This assumes that 'wono', 'comp_id', 'fin_year_id' are filterable
        # attributes of the underlying data, even if not direct model fields.
        # For this example, we'll assume a 'work_order_no' field exists or
        # the SP's 'x' parameter maps to some logical filtering.
        # As 'WONo' is a query parameter, we'll filter on a hypothetical 'work_order_no' field.
        # In a real setup, `comp_id` and `fin_year_id` might be context-aware,
        # or also part of the underlying data structure for filtering.
        
        # NOTE: For this example, we assume `wono` can be directly filtered on a field.
        # If `comp_id` and `fin_year_id` were also columns in `tbl_wis_transactions`,
        # they would be added to the filter below.
        
        # Placeholder for actual data retrieval logic, assuming a direct ORM filter
        # that mimics what the SP would do if its logic was simple filtering.
        queryset = self.filter(work_order_no=wono).order_by('-sys_date', '-sys_time') 
        return queryset

class WisTransaction(models.Model):
    # Primary Key, mapping to 'Id' from GridView
    id = models.IntegerField(db_column='Id', primary_key=True)
    wis_no = models.CharField(db_column='WISNo', max_length=100)
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    task_target_try_out_f_date = models.DateField(db_column='TaskTargetTryOut_FDate', null=True, blank=True)
    task_target_try_out_t_date = models.DateField(db_column='TaskTargetTryOut_TDate', null=True, blank=True)
    task_target_despatch_f_date = models.DateField(db_column='TaskTargetDespach_FDate', null=True, blank=True)
    task_target_despatch_t_date = models.DateField(db_column='TaskTargetDespach_TDate', null=True, blank=True)
    gen_by = models.CharField(db_column='GenBy', max_length=100)
    
    # Placeholder for the WONo filtering. This field is inferred to exist
    # on the table to allow direct ORM filtering based on the 'x' parameter.
    work_order_no = models.CharField(db_column='WONo', max_length=100, default='UNKNOWN')

    objects = WisTransactionManager() # Attach the custom manager

    class Meta:
        managed = False  # Django will not create/manage this table
        db_table = 'tbl_wis_transactions'  # Assumed table name
        verbose_name = 'WIS Transaction'
        verbose_name_plural = 'WIS Transactions'

    def __str__(self):
        return f"WIS No: {self.wis_no} (WO: {self.work_order_no})"

    @property
    def formatted_sys_date(self):
        """Returns system date in a user-friendly format."""
        return self.sys_date.strftime('%Y-%m-%d') if self.sys_date else ''

    @property
    def formatted_sys_time(self):
        """Returns system time in a user-friendly format."""
        return self.sys_time.strftime('%H:%M:%S') if self.sys_time else ''

    @property
    def formatted_try_out_from_date(self):
        return self.task_target_try_out_f_date.strftime('%Y-%m-%d') if self.task_target_try_out_f_date else ''

    @property
    def formatted_try_out_to_date(self):
        return self.task_target_try_out_t_date.strftime('%Y-%m-%d') if self.task_target_try_out_t_date else ''

    @property
    def formatted_despatch_from_date(self):
        return self.task_target_despatch_f_date.strftime('%Y-%m-%d') if self.task_target_despatch_f_date else ''

    @property
    def formatted_despatch_to_date(self):
        return self.task_target_despatch_t_date.strftime('%Y-%m-%d') if self.task_target_despatch_t_date else ''
```

### 4.2 Forms

Task: Define a Django form for user input.

## Instructions:

Since this ASP.NET page is a read-only list view and doesn't involve user input forms for creating or updating `WisTransaction` objects, a `ModelForm` is not required for this specific page's functionality. If 'Add' or 'Edit' actions were present, a `WisTransactionForm` would be defined here.

## `inventory/forms.py`

```python
# No forms are required for this specific 'list' functionality.
# This file is included to complete the expected structure.
# If CRUD operations were present, forms would be defined here.
```

### 4.3 Views

Task: Implement CRUD operations using CBVs.

## Instructions:

We will implement two views:
1.  `WisTransactionListView`: The main page view, which renders the initial container and receives the `WONo` and `Status` query parameters.
2.  `WisTransactionTablePartialView`: An HTMX-specific view that returns only the table content, allowing dynamic refreshes of the list without full page reloads. This also handles the redirect logic if no data is found, mirroring the ASP.NET behavior.

## `inventory/views.py`

```python
from django.views.generic import TemplateView, ListView
from django.urls import reverse_lazy
from django.shortcuts import redirect
from django.http import HttpResponse
from django.conf import settings # For accessing global settings like COMP_ID, FIN_YEAR_ID

from .models import WisTransaction

# Dummy URLs for redirection, replace with actual project URLs
# For example, these might be defined in the main project urls.py or another app's urls.py
DRY_ACTUAL_RUN_URL = reverse_lazy('inventory:wis_dry_actual_run') # Example: /inventory/dry-run/
ACTUAL_RUN_PRINT_URL = reverse_lazy('inventory:wis_actual_run_print') # Example: /inventory/actual-run-print/
WIS_TRANSACTION_PRINT_URL_NAME = 'inventory:wis_transaction_print' # Example: /inventory/wis-transactions/print/<pk>/

class WisTransactionListView(TemplateView):
    """
    Main view for displaying WIS Transactions.
    It renders the base template and an HTMX container for the table.
    """
    template_name = 'inventory/wis_transaction/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Extract WONo from query parameters and pass to context for display
        context['wono'] = self.request.GET.get('WONo', 'N/A')
        context['status'] = self.request.GET.get('status', '0')
        return context

class WisTransactionTablePartialView(ListView):
    """
    HTMX-specific view to load and refresh the WIS Transaction table.
    It applies filtering based on query parameters and handles redirection
    if no data is found, mimicking the ASP.NET loadgrid() behavior.
    """
    model = WisTransaction
    template_name = 'inventory/wis_transaction/_wis_transaction_table.html'
    context_object_name = 'wis_transactions'
    paginate_by = 20 # Matches ASP.NET PageSize

    def get_queryset(self):
        wono = self.request.GET.get('WONo')
        status = self.request.GET.get('status', '0') # Default status to 0
        
        # Retrieve Company ID and Financial Year ID.
        # In ASP.NET, these came from Session. In Django, they might be:
        # 1. From request.session (if stored there by authentication/context middleware)
        # 2. From global settings (if they are constant for the application instance)
        # 3. Passed as URL parameters if context-specific.
        # For this example, let's assume they are stored in Django settings for simplicity
        # or retrieved from session if a custom middleware provides them.
        # Placeholder values for demonstration:
        comp_id = getattr(settings, 'COMPANY_ID', 1)
        fin_year_id = getattr(settings, 'FINANCIAL_YEAR_ID', 1)

        if not wono:
            # If WONo is missing, it's an invalid request for this specific view
            # In ASP.NET, it would just show an empty grid or redirect if the SP failed.
            # Here, we can return an empty queryset.
            return self.model.objects.none()

        queryset = self.model.objects.get_transactions_by_wono(comp_id, fin_year_id, wono)
        
        # Emulate ASP.NET's `loadgrid` redirection if no data is found
        if not queryset.exists():
            if status == '0':
                return redirect(DRY_ACTUAL_RUN_URL)
            else: # Assuming status == '1'
                return redirect(ACTUAL_RUN_PRINT_URL)
        
        return queryset

    def render_to_response(self, context, **response_kwargs):
        # If get_queryset returned a redirect, handle it
        if isinstance(context, redirect):
            return context # It's a redirect object, return it directly
        return super().render_to_response(context, **response_kwargs)

class WisTransactionPrintRedirectView(TemplateView):
    """
    Handles the 'Select' action, redirecting to a conceptual print/detail page.
    This simulates the `GridView2_RowCommand` logic.
    """
    def get(self, request, *args, **kwargs):
        wis_id = kwargs.get('pk')
        wono = request.GET.get('won', '') # 'wn' from ASP.NET query string
        wis_no = request.GET.get('wis_no', '') # 'WISNo' from ASP.NET query string
        status = request.GET.get('status', '0')
        
        # Generate a random key if needed, similar to ASP.NET's GetRandomAlphaNumeric()
        # For Django, this might just be a timestamp or a UUID, or not needed at all.
        # Placeholder: a simple timestamp.
        import time
        random_key = int(time.time()) 
        
        # Construct the URL for the print/detail page
        # This assumes a named URL 'wis_transaction_print' exists with parameters.
        redirect_url = reverse_lazy(WIS_TRANSACTION_PRINT_URL_NAME, kwargs={'pk': wis_id})
        redirect_url += f"?WISNo={wis_no}&Key={random_key}&WISId={wis_id}&wn={wono}&ModId=9&SubModId=53&status={status}"
        
        return redirect(redirect_url)

class WisTransactionCancelRedirectView(TemplateView):
    """
    Handles the 'Cancel' action, redirecting based on the 'status' parameter.
    This simulates the `Cancel_Click` logic.
    """
    def get(self, request, *args, **kwargs):
        status = request.GET.get('status', '0')
        if status == '0':
            return redirect(DRY_ACTUAL_RUN_URL)
        else: # Assuming status == '1'
            return redirect(ACTUAL_RUN_PRINT_URL)
```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

-   **`list.html`**: The main page, extending `core/base.html`, containing the `LblWONo` and an HTMX-powered container for the DataTables content.
-   **`_wis_transaction_table.html`**: A partial template that contains the `<table>` structure, including DataTables initialization, and loops through the `wis_transactions` to populate rows. This partial is loaded via HTMX.
-   No `form.html` or `confirm_delete.html` is generated as these operations are not present on the original ASP.NET page.

## `inventory/templates/inventory/wis_transaction/list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">
            Transaction-wise WIS Issue Of WONo: <span class="text-blue-600">{{ wono }}</span>
        </h2>
        <!-- No 'Add New' button present in original ASP.NET, so not adding here -->
    </div>

    <div id="wisTransactionTable-container"
         hx-trigger="load, refreshWisTransactionList from:body"
         hx-get="{% url 'inventory:wis_transaction_table_partial' %}?WONo={{ wono }}&status={{ status }}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading WIS Transactions...</p>
        </div>
    </div>
    
    <div class="mt-6 text-center">
        <a href="{% url 'inventory:wis_transaction_cancel' %}?status={{ status }}"
           class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded shadow-md transition duration-300 ease-in-out">
            Cancel
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states.
        // For simple list view, Alpine.js might not be strictly necessary,
        // but included for completeness as per guidelines.
    });

    // Handle HTMX and DataTables initialization for dynamically loaded content
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'wisTransactionTable-container') {
            // Check if the table is already a DataTable instance to avoid re-initialization
            if ($.fn.DataTable.isDataTable('#wisTransactionTable')) {
                $('#wisTransactionTable').DataTable().destroy();
            }
            $('#wisTransactionTable').DataTable({
                "pageLength": 20, // Matches ASP.NET PageSize
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "responsive": true
            });
        }
    });

    // This event listener specifically handles successful HTMX POSTs (like form submissions)
    // that might trigger a refresh of the list, although not directly used for this read-only view.
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.successful && event.detail.xhr.status === 204) {
            // Trigger a custom event on the body to refresh the list via HTMX
            const refreshEvent = new CustomEvent('refreshWisTransactionList');
            document.body.dispatchEvent(refreshEvent);
        }
    });
</script>
{% endblock %}
```

## `inventory/templates/inventory/wis_transaction/_wis_transaction_table.html`

```html
<div class="overflow-x-auto relative shadow-md sm:rounded-lg">
    <table id="wisTransactionTable" class="min-w-full text-sm text-left text-gray-500">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-4 text-center">SN</th>
                <th scope="col" class="py-3 px-4 text-center">Actions</th>
                <th scope="col" class="py-3 px-4 text-center">WIS No</th>
                <th scope="col" class="py-3 px-4 text-center">Date</th>
                <th scope="col" class="py-3 px-4 text-center">Time</th>
                <th scope="col" class="py-3 px-4 text-center">Try Out From Date</th>
                <th scope="col" class="py-3 px-4 text-center">Try Out To Date</th>
                <th scope="col" class="py-3 px-4 text-center">Dispatch From Date</th>
                <th scope="col" class="py-3 px-4 text-center">Dispatch To Date</th>
                <th scope="col" class="py-3 px-4 text-left">Gen. By</th>
            </tr>
        </thead>
        <tbody>
            {% if wis_transactions %}
                {% for obj in wis_transactions %}
                <tr class="bg-white border-b hover:bg-gray-50">
                    <td class="py-2 px-4 text-right">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 text-center">
                        <a href="{% url 'inventory:wis_transaction_print_redirect' pk=obj.pk %}?wis_no={{ obj.wis_no }}&won={{ wono }}&status={{ status }}"
                           class="font-medium text-blue-600 hover:underline">Select</a>
                    </td>
                    <td class="py-2 px-4 text-center">{{ obj.wis_no }}</td>
                    <td class="py-2 px-4 text-center">{{ obj.formatted_sys_date }}</td>
                    <td class="py-2 px-4 text-center">{{ obj.formatted_sys_time }}</td>
                    <td class="py-2 px-4 text-center">{{ obj.formatted_try_out_from_date }}</td>
                    <td class="py-2 px-4 text-center">{{ obj.formatted_try_out_to_date }}</td>
                    <td class="py-2 px-4 text-center">{{ obj.formatted_despatch_from_date }}</td>
                    <td class="py-2 px-4 text-center">{{ obj.formatted_despatch_to_date }}</td>
                    <td class="py-2 px-4 text-left">{{ obj.gen_by }}</td>
                </tr>
                {% endfor %}
            {% else %}
                <tr class="bg-white border-b">
                    <td colspan="10" class="py-4 px-6 text-center text-red-500 font-bold">
                        No data found to display.
                    </td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<!-- DataTables initialization script (executed when HTMX swaps this content) -->
<script>
    // This script block will run after HTMX inserts the content.
    // The main list.html will ensure DataTable is initialized only once or re-initialized correctly.
    // This inner script block is less critical if list.html handles it, but good practice.
    // It's commented out here to prevent double initialization if list.html handles it.
    /*
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#wisTransactionTable')) {
            $('#wisTransactionTable').DataTable().destroy();
        }
        $('#wisTransactionTable').DataTable({
            "pageLength": 20, // Matches ASP.NET PageSize
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "responsive": true
        });
    });
    */
</script>
```

### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

Define paths for the main list view, the HTMX table partial, and the redirect actions.

## `inventory/urls.py`

```python
from django.urls import path
from .views import (
    WisTransactionListView, 
    WisTransactionTablePartialView, 
    WisTransactionPrintRedirectView,
    WisTransactionCancelRedirectView
)

app_name = 'inventory' # Define app namespace for reverse_lazy

urlpatterns = [
    # Main WIS Transaction List View (loads the shell and HTMX container)
    path('wis-transactions/', WisTransactionListView.as_view(), name='wis_transaction_list'),
    
    # HTMX endpoint to load the table content
    path('wis-transactions/table/', WisTransactionTablePartialView.as_view(), name='wis_transaction_table_partial'),
    
    # Endpoint for the 'Select' action (redirects to print/detail page)
    # The pk here is the WISId
    path('wis-transactions/print-redirect/<int:pk>/', WisTransactionPrintRedirectView.as_view(), name='wis_transaction_print_redirect'),
    
    # Endpoint for the 'Cancel' action (redirects based on status)
    path('wis-transactions/cancel/', WisTransactionCancelRedirectView.as_view(), name='wis_transaction_cancel'),
    
    # Placeholder URLs for the redirection targets (replace with actual paths in your project)
    path('wis-dry-run/', TemplateView.as_view(template_name='inventory/wis_dry_actual_run.html'), name='wis_dry_actual_run'),
    path('wis-actual-run-print/', TemplateView.as_view(template_name='inventory/wis_actual_run_print.html'), name='wis_actual_run_print'),
    path('wis-transactions/print/<int:pk>/', TemplateView.as_view(template_name='inventory/wis_transaction_print.html'), name='wis_transaction_print'),
]
```

### 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

Include comprehensive unit tests for model methods and integration tests for all views.

## `inventory/tests.py`

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.conf import settings
from unittest.mock import patch # For mocking settings or DB calls if needed

from .models import WisTransaction

# Configure mock settings for COMP_ID and FINANCIAL_YEAR_ID for testing
# In a real project, these would be in settings.py or retrieved from session/user profile
settings.configure(
    SECRET_KEY='a-very-secret-key-for-testing',
    DEBUG=True,
    TEMPLATES=[{
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
    }],
    INSTALLED_APPS=[
        'django.contrib.admin',
        'django.contrib.auth',
        'django.contrib.contenttypes',
        'django.contrib.sessions',
        'django.contrib.messages',
        'django.contrib.staticfiles',
        'inventory', # Your app
    ],
    MIDDLEWARE=[
        'django.contrib.sessions.middleware.SessionMiddleware',
        'django.contrib.auth.middleware.AuthenticationMiddleware',
        'django.contrib.messages.middleware.MessageMiddleware',
    ],
    COMP_ID=1,
    FINANCIAL_YEAR_ID=1,
    ROOT_URLCONF='inventory.urls', # Point to your app's urls for reverse lookups
)


class WisTransactionModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.wistransaction = WisTransaction.objects.create(
            id=1,
            wis_no='WIS001',
            sys_date='2023-01-15',
            sys_time='10:30:00',
            task_target_try_out_f_date='2023-01-10',
            task_target_try_out_t_date='2023-01-12',
            task_target_despatch_f_date='2023-01-20',
            task_target_despatch_t_date='2023-01-22',
            gen_by='John Doe',
            work_order_no='WO_XYZ_001'
        )
        WisTransaction.objects.create(
            id=2,
            wis_no='WIS002',
            sys_date='2023-01-16',
            sys_time='11:00:00',
            gen_by='Jane Smith',
            work_order_no='WO_XYZ_001'
        )
        WisTransaction.objects.create(
            id=3,
            wis_no='WIS003',
            sys_date='2023-01-17',
            sys_time='12:00:00',
            gen_by='Bob Johnson',
            work_order_no='WO_ABC_002'
        )

    def test_wis_transaction_creation(self):
        obj = WisTransaction.objects.get(id=1)
        self.assertEqual(obj.wis_no, 'WIS001')
        self.assertEqual(str(obj.sys_date), '2023-01-15')
        self.assertEqual(str(obj.sys_time), '10:30:00')
        self.assertEqual(obj.gen_by, 'John Doe')
        self.assertEqual(obj.work_order_no, 'WO_XYZ_001')

    def test_str_representation(self):
        obj = WisTransaction.objects.get(id=1)
        self.assertEqual(str(obj), "WIS No: WIS001 (WO: WO_XYZ_001)")

    def test_formatted_properties(self):
        obj = WisTransaction.objects.get(id=1)
        self.assertEqual(obj.formatted_sys_date, '2023-01-15')
        self.assertEqual(obj.formatted_sys_time, '10:30:00')
        self.assertEqual(obj.formatted_try_out_from_date, '2023-01-10')
        self.assertEqual(obj.formatted_try_out_to_date, '2023-01-12')
        self.assertEqual(obj.formatted_despatch_from_date, '2023-01-20')
        self.assertEqual(obj.formatted_despatch_to_date, '2023-01-22')

    def test_get_transactions_by_wono_manager_method(self):
        # Test for existing WONo
        transactions = WisTransaction.objects.get_transactions_by_wono(
            comp_id=settings.COMP_ID, fin_year_id=settings.FINANCIAL_YEAR_ID, wono='WO_XYZ_001'
        )
        self.assertEqual(transactions.count(), 2)
        self.assertIn(self.wistransaction, transactions)
        self.assertIn(WisTransaction.objects.get(id=2), transactions)

        # Test for non-existing WONo
        transactions_none = WisTransaction.objects.get_transactions_by_wono(
            comp_id=settings.COMP_ID, fin_year_id=settings.FINANCIAL_YEAR_ID, wono='NON_EXISTENT_WO'
        )
        self.assertEqual(transactions_none.count(), 0)


class WisTransactionViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.wistransaction = WisTransaction.objects.create(
            id=1,
            wis_no='WIS001',
            sys_date='2023-01-15',
            sys_time='10:30:00',
            gen_by='John Doe',
            work_order_no='WO_XYZ_001'
        )
        WisTransaction.objects.create(
            id=2,
            wis_no='WIS002',
            sys_date='2023-01-16',
            sys_time='11:00:00',
            gen_by='Jane Smith',
            work_order_no='WO_XYZ_001'
        )
        # Add data for redirection test
        WisTransaction.objects.create(
            id=3,
            wis_no='WIS003',
            sys_date='2023-01-17',
            sys_time='12:00:00',
            gen_by='Redirect Test',
            work_order_no='WO_REDIRECT_001'
        )
    
    def setUp(self):
        self.client = Client()

    def test_list_view_get(self):
        response = self.client.get(reverse('inventory:wis_transaction_list'), {'WONo': 'WO_XYZ_001', 'status': '0'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/wis_transaction/list.html')
        self.assertContains(response, 'WO_XYZ_001') # Check if WONo is displayed

    def test_table_partial_view_get_with_data(self):
        response = self.client.get(reverse('inventory:wis_transaction_table_partial'), 
                                   {'WONo': 'WO_XYZ_001', 'status': '0'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/wis_transaction/_wis_transaction_table.html')
        self.assertContains(response, 'WIS001')
        self.assertContains(response, 'WIS002')
        self.assertNotContains(response, 'No data found to display')
        self.assertContains(response, 'data-dt-idx="1"') # Check for DataTable pagination element if JS works

    def test_table_partial_view_get_no_data_redirect_status_0(self):
        # Mock the manager method to return empty queryset
        with patch.object(WisTransaction.objects, 'get_transactions_by_wono', return_value=WisTransaction.objects.none()):
            response = self.client.get(reverse('inventory:wis_transaction_table_partial'), 
                                       {'WONo': 'NON_EXISTENT', 'status': '0'})
            self.assertEqual(response.status_code, 302) # Expect redirect
            self.assertRedirects(response, reverse('inventory:wis_dry_actual_run'))

    def test_table_partial_view_get_no_data_redirect_status_1(self):
        with patch.object(WisTransaction.objects, 'get_transactions_by_wono', return_value=WisTransaction.objects.none()):
            response = self.client.get(reverse('inventory:wis_transaction_table_partial'), 
                                       {'WONo': 'NON_EXISTENT', 'status': '1'})
            self.assertEqual(response.status_code, 302) # Expect redirect
            self.assertRedirects(response, reverse('inventory:wis_actual_run_print'))

    def test_print_redirect_view(self):
        response = self.client.get(reverse('inventory:wis_transaction_print_redirect', args=[self.wistransaction.pk]), 
                                   {'wis_no': self.wistransaction.wis_no, 'won': self.wistransaction.work_order_no, 'status': '0'})
        self.assertEqual(response.status_code, 302) # Expect redirect
        # Check if redirect URL contains expected parameters.
        # It's hard to predict the 'Key' accurately for simple comparison, so check the base URL and other parameters.
        self.assertTrue(response.url.startswith(reverse('inventory:wis_transaction_print', args=[self.wistransaction.pk])))
        self.assertIn(f"WISNo={self.wistransaction.wis_no}", response.url)
        self.assertIn(f"wn={self.wistransaction.work_order_no}", response.url)
        self.assertIn("ModId=9", response.url)
        self.assertIn("SubModId=53", response.url)
        self.assertIn("status=0", response.url)

    def test_cancel_redirect_view_status_0(self):
        response = self.client.get(reverse('inventory:wis_transaction_cancel'), {'status': '0'})
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('inventory:wis_dry_actual_run'))

    def test_cancel_redirect_view_status_1(self):
        response = self.client.get(reverse('inventory:wis_transaction_cancel'), {'status': '1'})
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('inventory:wis_actual_run_print'))

    def test_htmx_trigger_on_success(self):
        # This test checks for the HTMX header, but the actual trigger is client-side.
        # We can't directly test the client-side event dispatch from Django's test client.
        # However, we can ensure the server response for a successful HTMX POST (if any)
        # would return the correct status code and header for HTMX.
        # For this specific view, there's no HTMX POST, but if there were, it would look like:
        # headers = {'HTTP_HX_REQUEST': 'true'}
        # response = self.client.post(reverse('some_htmx_post_view'), data, **headers)
        # self.assertEqual(response.status_code, 204)
        # self.assertIn('HX-Trigger', response.headers)
        pass # No HTMX POST in this specific conversion.
```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

-   **HTMX for dynamic table loading:** The `wis_transaction_table_partial` view is fetched via `hx-get` into a container on `wis_transaction_list.html`. This ensures the table content loads dynamically.
-   **DataTables for client-side features:** DataTables is initialized on the `_wis_transaction_table.html` partial once it's loaded and swapped into the DOM by HTMX. This provides search, sort, and internal pagination.
-   **HTMX for navigation:** The "Select" and "Cancel" buttons use standard `<a>` tags with `href` pointing to Django URLs, which then perform server-side redirects, matching the ASP.NET behavior. While HTMX could make these soft redirects, the original code used full page navigations, so keeping this consistent.
-   **Alpine.js:** While not strictly *needed* for this simple list view, `alpine:init` and basic Alpine.js setup are included in `list.html` for future extensibility (e.g., modals for filters, or more complex UI interactions).
-   **DRY Templates:** The use of `_wis_transaction_table.html` as a partial ensures the table rendering logic is reusable and dynamically loaded.
-   **No HTML in Views:** All HTML is strictly in template files.

## Final Notes

-   **Placeholder URLs:** Remember to replace the placeholder URLs (e.g., `DRY_ACTUAL_RUN_URL`, `ACTUAL_RUN_PRINT_URL`, `WIS_TRANSACTION_PRINT_URL_NAME`) in `inventory/views.py` and `inventory/urls.py` with the actual URLs in your Django project once the corresponding pages are migrated.
-   **Database Connection:** The `WisTransactionManager` currently uses Django ORM filtering as a simulation. For direct stored procedure execution, you would need to use `django.db.connection` and manage cursor operations, which is a more advanced task requiring knowledge of the stored procedure's exact logic and return structure. The current solution assumes the core filtering of the SP can be mapped to ORM.
-   **Session Variables:** `CompId` and `FinYearId` were accessed from ASP.NET `Session`. In Django, these could come from:
    *   Django `request.session` (if values are set there).
    *   A custom context processor if they are user-specific.
    *   Django settings if they are global application constants.
    *   User profile/model if tied to the logged-in user.
    The current implementation uses `settings.COMP_ID` and `settings.FINANCIAL_YEAR_ID` as a demonstration.
-   **Error Handling:** The original ASP.NET had a `try-catch` block. Django's robust error handling (middleware, `DEBUG` settings) provides a structured way to manage exceptions. Specific business logic errors (like "no data found") are handled by redirects in the `get_queryset` of the `WisTransactionTablePartialView`, mimicking the original behavior.
-   **Tailwind CSS:** The HTML templates include classes like `container mx-auto`, `bg-blue-500`, `shadow-md`, etc., which are indicative of Tailwind CSS usage, as per the guidelines.
-   **AI-Assisted Automation:** This detailed breakdown into distinct files and the adherence to "fat model, thin view," HTMX, and DataTables makes the process modular and suitable for AI-driven code generation, where each component can be generated and tested independently. The conversational AI can guide non-technical stakeholders through these steps, explaining the business value of each transformed component.