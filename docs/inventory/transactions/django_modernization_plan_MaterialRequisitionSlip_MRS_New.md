## ASP.NET to Django Conversion Script: Material Requisition Slip

This document outlines a plan to modernize your ASP.NET Material Requisition Slip (MRS) module to a robust Django-based solution. We'll leverage AI-assisted automation to transform your existing logic into a modern, maintainable, and scalable application.

### Business Value of this Modernization:

*   **Improved User Experience:** Dynamic, fast interfaces with HTMX and Alpine.js eliminate full page reloads, making the application feel snappier and more intuitive for your users.
*   **Reduced Development Costs:** By adopting Django's "batteries-included" philosophy, we gain a powerful ORM, built-in security features, and a clear architectural pattern, reducing the need for extensive manual coding and complex SQL queries.
*   **Enhanced Maintainability:** The "Fat Model, Thin View" approach centralizes business logic, making it easier to understand, test, and update the application. This reduces the risk of bugs and simplifies future enhancements.
*   **Future-Proof Technology:** Django, HTMX, and Alpine.js are modern, actively maintained technologies, ensuring your application remains relevant and secure for years to come, avoiding the pitfalls of legacy systems.
*   **Scalability:** Django's design allows for easy scaling as your business grows, handling increased data and user loads efficiently.
*   **Standardized Development:** By adopting consistent patterns (e.g., DataTables for all lists, DRY templates), we establish a predictable and efficient development workflow.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database tables and their columns from the ASP.NET code.

**Instructions:**

Based on the ASP.NET code, we've identified the following primary tables relevant to the Material Requisition Slip process. These will be mapped directly to Django models.

*   `tblDG_Item_Master`: Stores details about all inventory items.
*   `tblDG_Category_Master`: Manages item categories.
*   `tblDG_Location_Master`: Stores inventory locations.
*   `BusinessGroup`: Represents departments or business groups.
*   `Unit_Master`: Defines units of measure.
*   `tblinv_MaterialRequisition_Temp`: A temporary table (like a shopping cart) for items selected by a user before finalizing an MRS.
*   `tblInv_MaterialRequisition_Master`: The header for a finalized Material Requisition Slip.
*   `tblInv_MaterialRequisition_Details`: The line items associated with a finalized MRS.

## Step 2: Identify Backend Functionality

Task: Determine the data operations in the ASP.NET code.

**Instructions:**

The existing ASP.NET module performs the following key operations:

*   **Searching Items:** Users can search for items by category, item code, description, or location on the "Item Master" tab. This involves filtering and retrieving data from `tblDG_Item_Master` and related lookup tables.
*   **Adding Temporary Items:** Selected items from the search results are added to a temporary holding area (`tblinv_MaterialRequisition_Temp`) with a requested quantity, department, or WO number, and remarks. Critical validation checks are performed here, such as ensuring the requested quantity does not exceed the available stock and checking WO number validity.
*   **Listing Temporary Items:** The "Selected Items" tab displays all items currently in the temporary holding area for the active user.
*   **Deleting Temporary Items:** Items can be removed from the temporary list.
*   **Generating MRS:** When the user is satisfied with the temporary list, a final Material Requisition Slip is generated. This moves all temporary items into the permanent `tblInv_MaterialRequisition_Master` and `tblInv_MaterialRequisition_Details` tables and clears the temporary list.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

**Instructions:**

The ASP.NET user interface is built with standard web form controls, which will be replaced with modern HTML5 elements enhanced by Django's template language, HTMX, and Alpine.js.

*   **Dropdowns:** Used for selecting item types (Category/WO Items), search criteria (Item Code, Description, Location), item categories, and departments/WO types. These will become standard HTML `<select>` elements.
*   **Text Inputs:** Used for search terms, requested quantities, WO numbers, and remarks. These will become HTML `<input type="text">` or `<textarea>` elements.
*   **Buttons:** `Search`, `Add` (to temporary list), `Delete` (from temporary list), and `Generate MRS`. These will be HTML `<button>` elements with `hx-` attributes for dynamic interactions.
*   **Grids:** `GridView2` (for item search results) and `GridView3` (for selected temporary items). Both will be transformed into dynamic HTML `<table>` elements utilizing the DataTables JavaScript library for client-side functionality.
*   **Tabs:** The `AjaxControlToolkit:TabContainer` will be replaced by a simple HTML structure managed by Alpine.js, enabling smooth transitions between "Item Master" and "Selected Items" without full page reloads.
*   **Validation Messages:** ASP.NET's `RequiredFieldValidator` and `RegularExpressionValidator` will be translated into Django form validation and client-side feedback using Alpine.js.

## Step 4: Generate Django Code

We will structure the Django application, named `inventory`, to handle the Material Requisition Slip functionality.

### 4.1 Models (`inventory/models.py`)

Task: Create Django models based on the identified database schema. These models will also contain the business logic, adhering to the "Fat Model" principle.

**Instructions:**

We define models for all related tables, linking them with Django's `ForeignKey` relationships where appropriate. `managed = False` ensures Django doesn't create or modify these tables, as they are existing in the database. Utility methods for data lookup and business logic (like stock validation, WO number validation, and MRS generation) are added directly to the models.

```python
from django.db import models, transaction
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.core.validators import RegexValidator

# Assume CompId and FinYearId are handled globally, e.g., via a settings or user profile.
# For simplicity, we'll use placeholder values or fetch from an assumed User profile.
# In a real ERP, these would likely come from request.user or a context manager.
DEFAULT_COMP_ID = 1 # Example Company ID
DEFAULT_FIN_YEAR_ID = 1 # Example Financial Year ID

class Category(models.Model):
    # CId, Symbol, CName, CompId
    cid = models.IntegerField(db_column='CId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)
    cname = models.CharField(db_column='CName', max_length=255, blank=True, null=True)
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'

    def __str__(self):
        return f"[{self.symbol}] - {self.cname}" if self.symbol else self.cname or f"Category {self.cid}"

class Location(models.Model):
    # Id, LocationLabel, LocationNo
    id = models.IntegerField(db_column='Id', primary_key=True)
    locationlabel = models.CharField(db_column='LocationLabel', max_length=100, blank=True, null=True)
    locationno = models.CharField(db_column='LocationNo', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Location_Master'
        verbose_name = 'Location'
        verbose_name_plural = 'Locations'

    def __str__(self):
        return f"{self.locationlabel}-{self.locationno}"

class BusinessGroup(models.Model):
    # Id, Symbol (Dept name)
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol or f"Business Group {self.id}"

class Unit(models.Model):
    # Id, Symbol (UOM symbol)
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit of Measure'
        verbose_name_plural = 'Units of Measure'

    def __str__(self):
        return self.symbol or f"Unit {self.id}"

class Item(models.Model):
    # Id, ItemCode, ManfDesc, UOMBasic, StockQty, Location, CId, CompId, FinYearId
    id = models.IntegerField(db_column='Id', primary_key=True)
    itemcode = models.CharField(db_column='ItemCode', max_length=100, blank=True, null=True)
    manfdesc = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True)
    uombasic = models.ForeignKey(Unit, models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)
    stockqty = models.FloatField(db_column='StockQty', blank=True, null=True)
    location = models.ForeignKey(Location, models.DO_NOTHING, db_column='Location', blank=True, null=True)
    cid = models.ForeignKey(Category, models.DO_NOTHING, db_column='CId', blank=True, null=True)
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)
    finyearid = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return f"{self.itemcode} - {self.manfdesc}"

    @classmethod
    def get_filtered_items(cls, search_type, category_id, search_code_field, search_term, location_id, comp_id, fin_year_id):
        """
        Mimics the Fillgrid logic from ASP.NET to search items.
        Returns a QuerySet of Item objects.
        """
        queryset = cls.objects.filter(compid=comp_id, finyearid__lte=fin_year_id)

        if search_type == 'Category' and category_id and category_id != 'Select':
            queryset = queryset.filter(cid=category_id)
            if search_code_field and search_code_field != 'Select':
                if search_code_field == 'tblDG_Item_Master.ItemCode' and search_term:
                    queryset = queryset.filter(itemcode__istartswith=search_term)
                elif search_code_field == 'tblDG_Item_Master.ManfDesc' and search_term:
                    queryset = queryset.filter(manfdesc__icontains=search_term)
                elif search_code_field == 'tblDG_Item_Master.Location' and location_id and location_id != 'Select':
                    queryset = queryset.filter(location_id=location_id)
            elif search_term: # If no specific search code, default to description
                queryset = queryset.filter(manfdesc__icontains=search_term)
        elif search_type == 'WOItems':
            if search_code_field and search_code_field != 'Select':
                if search_code_field == 'tblDG_Item_Master.ItemCode' and search_term:
                    queryset = queryset.filter(itemcode__icontains=search_term)
                elif search_code_field == 'tblDG_Item_Master.ManfDesc' and search_term:
                    queryset = queryset.filter(manfdesc__icontains=search_term)
            elif search_term: # If no specific search code, default to description
                queryset = queryset.filter(manfdesc__icontains=search_term)
        
        # Note: The original ASP.NET GetAllItem stored procedure likely has more complex joins/logic.
        # This ORM translation aims to capture the filtering as seen in the C# `Fillgrid`.
        return queryset

class MaterialRequisitionTempItem(models.Model):
    # Id, CompId, SessionId, ItemId, DeptId, WONo, ReqQty, Remarks
    id = models.IntegerField(db_column='Id', primary_key=True)
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)
    sessionid = models.CharField(db_column='SessionId', max_length=255, blank=True, null=True) # Maps to request.user.id
    item = models.ForeignKey(Item, models.DO_NOTHING, db_column='ItemId', blank=True, null=True)
    dept = models.ForeignKey(BusinessGroup, models.DO_NOTHING, db_column='DeptId', blank=True, null=True)
    wono = models.CharField(db_column='WONo', max_length=100, blank=True, null=True)
    reqqty = models.FloatField(db_column='ReqQty', blank=True, null=True)
    remarks = models.CharField(db_column='Remarks', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialRequisition_Temp'
        verbose_name = 'Temporary Material Requisition Item'
        verbose_name_plural = 'Temporary Material Requisition Items'

    def __str__(self):
        return f"Temp MRS for {self.item.manfdesc} ({self.reqqty})"

    @classmethod
    def add_item_to_temp(cls, item_id, dept_or_wo_type, dept_id, wo_no, req_qty, remarks, user_session_id, comp_id, fin_year_id):
        """
        Business logic for adding an item to the temporary MRS list.
        Performs stock validation and ensures item is not already in temp list.
        """
        # 1. Check if item is already selected
        if cls.objects.filter(item_id=item_id, compid=comp_id, sessionid=user_session_id).exists():
            raise ValidationError("Item is already selected for MRS.")

        item = Item.objects.get(id=item_id, compid=comp_id)

        # 2. Stock validation
        if item.stockqty is None or item.stockqty <= 0 or req_qty > item.stockqty:
            raise ValidationError("Requested Quantity should be less than or equal to Stock Quantity.")

        # 3. WO No / Dept validation
        selected_dept = None
        selected_wono = None
        if dept_or_wo_type == '1':  # BGGroup
            if not dept_id or dept_id == '0':
                raise ValidationError("Please select a Business Group.")
            selected_dept = BusinessGroup.objects.get(id=dept_id)
        elif dept_or_wo_type == '2':  # WONo
            if not wo_no:
                raise ValidationError("WO Number is required.")
            if not cls.check_valid_wo_no(wo_no, comp_id, fin_year_id):
                raise ValidationError("WO Number or Dept is not found!")
            selected_wono = wo_no
        else: # Case 'Select' for DropDownList1
            raise ValidationError("Please select 'BGGroup' or 'WONo'.")


        cls.objects.create(
            compid=comp_id,
            sessionid=user_session_id,
            item=item,
            dept=selected_dept,
            wono=selected_wono,
            reqqty=req_qty,
            remarks=remarks
        )
        return True

    @classmethod
    def check_valid_wo_no(cls, wo_no, comp_id, fin_year_id):
        """
        Mimics fun.CheckValidWONo. This is a placeholder; actual logic
        would involve checking against a 'WorkOrder' table.
        """
        # Placeholder for actual WO No validation logic
        # For demonstration, assume any non-empty WO is valid
        return wo_no is not None and wo_no != ''


class MaterialRequisition(models.Model):
    # Id, SysDate, SysTime, CompId, FinYearId, SessionId, MRSNo
    id = models.IntegerField(db_column='Id', primary_key=True)
    sysdate = models.DateField(db_column='SysDate', blank=True, null=True)
    systime = models.CharField(db_column='SysTime', max_length=20, blank=True, null=True)
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)
    finyearid = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    sessionid = models.CharField(db_column='SessionId', max_length=255, blank=True, null=True)
    mrsno = models.CharField(db_column='MRSNo', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialRequisition_Master'
        verbose_name = 'Material Requisition Slip'
        verbose_name_plural = 'Material Requisition Slips'

    def __str__(self):
        return self.mrsno or f"MRS {self.id}"

    @classmethod
    def generate_next_mrs_no(cls, comp_id, fin_year_id):
        """Generates the next MRS number (e.g., 0001, 0002)."""
        last_mrs = cls.objects.filter(compid=comp_id, finyearid=fin_year_id).order_by('-mrsno').first()
        if last_mrs and last_mrs.mrsno and last_mrs.mrsno.isdigit():
            next_num = int(last_mrs.mrsno) + 1
        else:
            next_num = 1
        return str(next_num).zfill(4)

    @classmethod
    @transaction.atomic
    def finalize_mrs_from_temp(cls, user_session_id, comp_id, fin_year_id):
        """
        Business logic to finalize a Material Requisition Slip from temporary items.
        Creates a new MRS Master and Details entry, then clears temporary items.
        """
        temp_items = MaterialRequisitionTempItem.objects.filter(
            sessionid=user_session_id,
            compid=comp_id
        )

        if not temp_items.exists():
            raise ValidationError("No items selected to generate MRS.")

        mrs_no = cls.generate_next_mrs_no(comp_id, fin_year_id)
        current_date = timezone.localdate()
        current_time = timezone.localtime().strftime("%H:%M:%S")

        # Create MRS Master record
        mrs_master = cls.objects.create(
            sysdate=current_date,
            systime=current_time,
            compid=comp_id,
            finyearid=fin_year_id,
            sessionid=user_session_id,
            mrsno=mrs_no
        )

        # Create MRS Details records from temporary items
        for temp_item in temp_items:
            MaterialRequisitionDetail.objects.create(
                mid=mrs_master,
                mrsno=mrs_no,
                item=temp_item.item,
                dept=temp_item.dept,
                wono=temp_item.wono,
                reqqty=temp_item.reqqty,
                remarks=temp_item.remarks
            )

        # Clear temporary items for this session
        temp_items.delete()

        return mrs_master

class MaterialRequisitionDetail(models.Model):
    # MId, MRSNo, ItemId, DeptId, WONo, ReqQty, Remarks
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming a primary key for this table as well
    mid = models.ForeignKey(MaterialRequisition, models.DO_NOTHING, db_column='MId', blank=True, null=True)
    mrsno = models.CharField(db_column='MRSNo', max_length=50, blank=True, null=True)
    item = models.ForeignKey(Item, models.DO_NOTHING, db_column='ItemId', blank=True, null=True)
    dept = models.ForeignKey(BusinessGroup, models.DO_NOTHING, db_column='DeptId', blank=True, null=True)
    wono = models.CharField(db_column='WONo', max_length=100, blank=True, null=True)
    reqqty = models.FloatField(db_column='ReqQty', blank=True, null=True)
    remarks = models.CharField(db_column='Remarks', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialRequisition_Details'
        verbose_name = 'Material Requisition Detail'
        verbose_name_plural = 'Material Requisition Details'

    def __str__(self):
        return f"Detail for MRS {self.mrsno} - {self.item.manfdesc}"

```

### 4.2 Forms (`inventory/forms.py`)

Task: Define Django forms for user input, specifically for adding items to the temporary MRS list.

**Instructions:**

We create a form for `MaterialRequisitionTempItem` to handle the 'Add' action, including custom validation for quantity and the dynamic WO/Dept selection. We'll use Django's built-in `RegexValidator` for quantity.

```python
from django import forms
from django.core.validators import RegexValidator
from django.core.exceptions import ValidationError
from .models import Item, BusinessGroup, Location, Category, MaterialRequisitionTempItem

class ItemSearchForm(forms.Form):
    """
    Form for filtering items in the Item Master tab.
    Corresponds to DrpType, DrpCategory1, DrpSearchCode, DropDownList3, txtSearchItemCode.
    """
    TYPE_CHOICES = [
        ('', 'Select'),
        ('Category', 'Category'),
        ('WOItems', 'WO Items'),
    ]
    SEARCH_CODE_CHOICES = [
        ('', 'Select'),
        ('tblDG_Item_Master.ItemCode', 'Item Code'),
        ('tblDG_Item_Master.ManfDesc', 'Description'),
        ('tblDG_Item_Master.Location', 'Location'),
    ]

    item_type = forms.ChoiceField(
        choices=TYPE_CHOICES,
        required=False,
        label="Type",
        widget=forms.Select(attrs={
            'class': 'box3 w-full',
            'hx-get': '/inventory/items/filter-options/', # Endpoint for dynamic dropdowns
            'hx-target': '#filter-options-container',
            'hx-swap': 'innerHTML',
            'hx-trigger': 'change',
            'name': 'item_type' # Explicitly set name
        })
    )
    category = forms.ModelChoiceField(
        queryset=Category.objects.all().order_by('cname'), # Assume a manager method for comp_id
        required=False,
        empty_label="Select",
        label="Category",
        widget=forms.Select(attrs={
            'class': 'box3 w-full',
            'hx-get': '/inventory/items/search-form/', # Endpoint for dynamic dropdowns
            'hx-target': '#item-search-form-controls',
            'hx-swap': 'outerHTML', # Swap the form controls
            'hx-trigger': 'change',
            'name': 'category' # Explicitly set name
        })
    )
    search_code = forms.ChoiceField(
        choices=SEARCH_CODE_CHOICES,
        required=False,
        label="Search By",
        widget=forms.Select(attrs={
            'class': 'box3 w-full',
            'hx-get': '/inventory/items/search-form/', # Endpoint for dynamic dropdowns
            'hx-target': '#item-search-form-controls',
            'hx-swap': 'outerHTML', # Swap the form controls
            'hx-trigger': 'change',
            'name': 'search_code' # Explicitly set name
        })
    )
    location = forms.ModelChoiceField(
        queryset=Location.objects.all().order_by('locationlabel'),
        required=False,
        empty_label="Select",
        label="Location",
        widget=forms.Select(attrs={
            'class': 'box3 w-full',
            'name': 'location' # Explicitly set name
        })
    )
    search_term = forms.CharField(
        max_length=207,
        required=False,
        label="Search Term",
        widget=forms.TextInput(attrs={'class': 'box3 w-full'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Dynamic visibility and queryset based on initial data or POST data
        # For HTMX, this form is re-rendered, so its initial state will reflect current selections.
        item_type = self.initial.get('item_type') or self.data.get('item_type')
        search_code = self.initial.get('search_code') or self.data.get('search_code')

        self.fields['category'].widget.attrs['hx-include'] = '#id_item_type'
        self.fields['search_code'].widget.attrs['hx-include'] = '#id_item_type, #id_category'
        self.fields['location'].widget.attrs['hx-include'] = '#id_item_type, #id_category, #id_search_code'
        self.fields['search_term'].widget.attrs['hx-include'] = '#id_item_type, #id_category, #id_search_code, #id_location'

        if not item_type or item_type == '':
            self.fields['category'].widget.attrs['class'] += ' hidden'
            self.fields['search_code'].widget.attrs['class'] += ' hidden'
            self.fields['location'].widget.attrs['class'] += ' hidden'
            self.fields['search_term'].widget.attrs['class'] += ' hidden'
        elif item_type == 'Category':
            self.fields['category'].widget.attrs.pop('class', None) # Make visible
            self.fields['search_code'].widget.attrs.pop('class', None)
            if search_code == 'tblDG_Item_Master.Location':
                self.fields['location'].widget.attrs.pop('class', None)
                self.fields['search_term'].widget.attrs['class'] += ' hidden'
            else:
                self.fields['location'].widget.attrs['class'] += ' hidden'
                self.fields['search_term'].widget.attrs.pop('class', None)
        elif item_type == 'WOItems':
            self.fields['category'].widget.attrs['class'] += ' hidden'
            self.fields['location'].widget.attrs['class'] += ' hidden'
            self.fields['search_code'].widget.attrs.pop('class', None)
            self.fields['search_term'].widget.attrs.pop('class', None)


class AddTempItemForm(forms.Form):
    """
    Form for adding an item to the temporary MRS list.
    Corresponds to DropDownList1, drpdept, txtwono, txtqty, txtremarks for a specific item row.
    """
    DEPT_WO_CHOICES = [
        ('0', 'Select'),
        ('1', 'BGGroup'),
        ('2', 'WONo'),
    ]

    item_id = forms.IntegerField(widget=forms.HiddenInput())
    dept_wo_type = forms.ChoiceField(
        choices=DEPT_WO_CHOICES,
        required=True,
        label="BGGroup/WoNo",
        widget=forms.Select(attrs={'class': 'box3 w-full', 'hx-post': 'this', 'hx-swap': 'outerHTML', 'hx-trigger': 'change'})
    )
    dept_id = forms.ModelChoiceField(
        queryset=BusinessGroup.objects.all().order_by('symbol'),
        required=False,
        empty_label="Select",
        label="Department",
        widget=forms.Select(attrs={'class': 'box3 w-full'})
    )
    wo_no = forms.CharField(
        max_length=100,
        required=False,
        label="WO Number",
        widget=forms.TextInput(attrs={'class': 'box3 w-full'})
    )
    req_qty = forms.FloatField(
        required=True,
        label="Req. Qty",
        widget=forms.TextInput(attrs={'class': 'box3 w-full'}),
        validators=[
            RegexValidator(
                regex=r'^\d{1,15}(\.\d{0,3})?$',
                message="Quantity must be a number with up to 15 digits before and 3 after decimal."
            )
        ]
    )
    remarks = forms.CharField(
        max_length=255,
        required=False,
        label="Remarks",
        widget=forms.TextInput(attrs={'class': 'box3 w-full'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make fields for dept and wo_no dynamically visible/required
        dept_wo_type = self.initial.get('dept_wo_type') or self.data.get('dept_wo_type')

        self.fields['dept_id'].required = (dept_wo_type == '1')
        self.fields['wo_no'].required = (dept_wo_type == '2')

        self.fields['dept_id'].widget.attrs['class'] = 'box3 w-full ' + ('hidden' if dept_wo_type != '1' else '')
        self.fields['wo_no'].widget.attrs['class'] = 'box3 w-full ' + ('hidden' if dept_wo_type != '2' else '')

        # req_qty is required if dept_wo_type is selected
        self.fields['req_qty'].required = (dept_wo_type != '0')

    def clean(self):
        cleaned_data = super().clean()
        dept_wo_type = cleaned_data.get('dept_wo_type')
        dept_id = cleaned_data.get('dept_id')
        wo_no = cleaned_data.get('wo_no')

        if dept_wo_type == '1' and not dept_id:
            self.add_error('dept_id', "Please select a Business Group.")
        elif dept_wo_type == '2' and not wo_no:
            self.add_error('wo_no', "WO Number is required.")

        return cleaned_data

```

### 4.3 Views (`inventory/views.py`)

Task: Implement the logic for the MRS creation process using Class-Based Views (CBVs), keeping views thin and moving logic to models.

**Instructions:**

We will use a main `TemplateView` for the MRS page, and separate `View` subclasses or `ListView`s to handle the HTMX-loaded partials for item search results and selected temporary items. CRUD operations on temporary items will be handled by dedicated HTMX endpoints.

```python
from django.views.generic import TemplateView, ListView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.template.loader import render_to_string
from django.shortcuts import get_object_or_404, redirect
from django.core.exceptions import ValidationError
from django.db import IntegrityError # For potential primary key conflicts if `managed=False` PKs clash

from .models import Item, MaterialRequisitionTempItem, MaterialRequisition, DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID, Category, Location, BusinessGroup
from .forms import ItemSearchForm, AddTempItemForm

# Helper for current user's session ID (using request.user.id as a proxy for SessionId)
# In a real system, you might have an explicit session ID or link it to Auth's session.
def get_user_session_id(request):
    return str(request.user.id) if request.user.is_authenticated else request.session.session_key or 'anonymous'

class MaterialRequisitionSlipView(TemplateView):
    """
    Main view for the Material Requisition Slip.
    Renders the tab container and initial search form.
    """
    template_name = 'inventory/mrs/main.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize search form with categories and locations
        context['item_search_form'] = ItemSearchForm()
        # This will be overridden by HTMX requests
        context['selected_tab'] = self.request.GET.get('tab', 'item_master')
        return context

# --- HTMX Endpoints for Item Master Tab ---

class ItemSearchFormControlsView(View):
    """
    Handles dynamic rendering of search form controls (category, location, search term)
    based on 'Type' and 'Search By' selections.
    """
    def get(self, request, *args, **kwargs):
        item_type = request.GET.get('item_type', '')
        category_id = request.GET.get('category', '')
        search_code = request.GET.get('search_code', '')
        search_term = request.GET.get('search_term', '')
        location_id = request.GET.get('location', '')

        # Re-initialize form with current selections to control visibility
        form = ItemSearchForm(
            initial={
                'item_type': item_type,
                'category': category_id,
                'search_code': search_code,
                'search_term': search_term,
                'location': location_id
            }
        )
        return HttpResponse(render_to_string('inventory/mrs/_item_search_form_controls.html', {'item_search_form': form}, request=request))

class ItemListTableView(ListView):
    """
    Renders the DataTables for available items based on search filters.
    Loaded via HTMX.
    """
    model = Item
    template_name = 'inventory/mrs/_item_list_table.html'
    context_object_name = 'items'
    paginate_by = 17 # Matches ASP.NET GridView2 PageSize

    def get_queryset(self):
        form = ItemSearchForm(self.request.GET)
        # Default values for company and financial year
        comp_id = DEFAULT_COMP_ID # Replace with actual logic to get user's company ID
        fin_year_id = DEFAULT_FIN_YEAR_ID # Replace with actual logic to get user's financial year ID

        if form.is_valid():
            item_type = form.cleaned_data.get('item_type')
            category_id = form.cleaned_data.get('category')
            search_code = form.cleaned_data.get('search_code')
            search_term = form.cleaned_data.get('search_term')
            location_id = form.cleaned_data.get('location')

            queryset = Item.get_filtered_items(
                item_type,
                category_id.cid if category_id else None, # Pass CID if Category object
                search_code,
                search_term,
                location_id.id if location_id else None, # Pass ID if Location object
                comp_id,
                fin_year_id
            )
        else:
            # If form is not valid (e.g., initial load or missing required fields)
            queryset = Item.objects.none() # Return empty queryset

        # Ensure related data is pre-fetched for display
        queryset = queryset.select_related('uombasic', 'location', 'cid')
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Add the AddTempItemForm instance for each row
        context['add_temp_item_form'] = AddTempItemForm()
        return context

class AddTempItemView(View):
    """
    Handles adding an item to the temporary MRS list via HTMX POST.
    """
    def post(self, request, *args, **kwargs):
        form = AddTempItemForm(request.POST)
        user_session_id = get_user_session_id(request)
        comp_id = DEFAULT_COMP_ID # From session/user context

        if form.is_valid():
            item_id = form.cleaned_data['item_id']
            dept_wo_type = form.cleaned_data['dept_wo_type']
            dept_id = form.cleaned_data['dept_id'].id if form.cleaned_data['dept_id'] else None
            wo_no = form.cleaned_data['wo_no']
            req_qty = form.cleaned_data['req_qty']
            remarks = form.cleaned_data['remarks']

            try:
                MaterialRequisitionTempItem.add_item_to_temp(
                    item_id, dept_wo_type, dept_id, wo_no, req_qty, remarks, user_session_id, comp_id, DEFAULT_FIN_YEAR_ID
                )
                messages.success(request, 'Item added to temporary list successfully.')
                # Trigger refresh for selected items tab
                return HttpResponse(status=204, headers={'HX-Trigger': 'refreshSelectedItemsList'})
            except ValidationError as e:
                messages.error(request, e.message)
            except Exception as e:
                messages.error(request, f"An unexpected error occurred: {e}")
        else:
            # If form is not valid, re-render the form for the specific row with errors
            # This requires passing the current item's context for re-rendering
            # A more robust solution for per-row form validation might involve:
            # 1. A separate endpoint for just the form row.
            # 2. HTMX targetting the specific row to replace its form.
            messages.error(request, "Please correct the errors in the form.")

        # If form is not valid or an error occurred, return the updated row with errors
        # This part requires specific context to re-render the row correctly.
        # For simplicity in this example, we'll just send back a message.
        # A full implementation would render the specific item row again.
        return HttpResponse(status=200, headers={'HX-Trigger': 'showMessage'})

class DynamicAddTempItemFormView(View):
    """
    Renders the AddTempItemForm for a specific item row, dynamically showing/hiding fields.
    This is used by HTMX's hx-post='this' on the dept_wo_type dropdown.
    """
    def post(self, request, item_id, *args, **kwargs):
        # We need to capture all fields from the row, not just the dropdown that changed
        # HTMX will send all inputs from the form containing 'this'
        form = AddTempItemForm(request.POST)
        form.fields['item_id'].initial = item_id # Ensure item_id is retained
        
        # Manually set the instance for context, if needed for fields
        item = get_object_or_404(Item, id=item_id)
        
        # Render just the form fields that need to change visibility
        return HttpResponse(render_to_string('inventory/mrs/_add_temp_item_form_fields.html', {'form': form, 'item': item}, request=request))

# --- HTMX Endpoints for Selected Items Tab ---

class MaterialRequisitionTempItemListView(ListView):
    """
    Renders the DataTables for selected temporary MRS items.
    Loaded via HTMX.
    """
    model = MaterialRequisitionTempItem
    template_name = 'inventory/mrs/_selected_items_table.html'
    context_object_name = 'temp_items'
    paginate_by = 15 # Matches ASP.NET GridView3 PageSize

    def get_queryset(self):
        user_session_id = get_user_session_id(self.request)
        comp_id = DEFAULT_COMP_ID # From session/user context
        queryset = MaterialRequisitionTempItem.objects.filter(
            sessionid=user_session_id,
            compid=comp_id
        ).select_related('item', 'item__uombasic', 'dept').order_by('-id')
        return queryset

class DeleteTempItemView(View):
    """
    Handles deleting a temporary MRS item via HTMX POST.
    """
    def post(self, request, pk, *args, **kwargs):
        user_session_id = get_user_session_id(request)
        comp_id = DEFAULT_COMP_ID # From session/user context

        try:
            temp_item = get_object_or_404(MaterialRequisitionTempItem, id=pk, sessionid=user_session_id, compid=comp_id)
            temp_item.delete()
            messages.success(request, 'Item removed from temporary list.')
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshSelectedItemsList'})
        except Exception as e:
            messages.error(request, f"Error deleting item: {e}")
            return HttpResponse(status=200, headers={'HX-Trigger': 'showMessage'})

class GenerateMRSView(View):
    """
    Handles generating the final MRS from temporary items via HTMX POST.
    """
    def post(self, request, *args, **kwargs):
        user_session_id = get_user_session_id(request)
        comp_id = DEFAULT_COMP_ID # From session/user context
        fin_year_id = DEFAULT_FIN_YEAR_ID # From session/user context

        try:
            mrs_master = MaterialRequisition.finalize_mrs_from_temp(user_session_id, comp_id, fin_year_id)
            messages.success(request, f"Material Requisition Slip {mrs_master.mrsno} generated successfully!")
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshSelectedItemsList'})
        except ValidationError as e:
            messages.error(request, e.message)
        except Exception as e:
            messages.error(request, f"An unexpected error occurred during MRS generation: {e}")

        return HttpResponse(status=200, headers={'HX-Trigger': 'showMessage'})

```

### 4.4 Templates (`inventory/templates/inventory/mrs/`)

Task: Create templates for the main MRS page and its HTMX-loaded partials.

**Instructions:**

All templates will extend `core/base.html` (or a partial for HTMX requests). We'll use DataTables for item lists, HTMX for dynamic content loading and form submissions, and Alpine.js for tab management and simple UI logic (like confirming deletes).

**`main.html` (Main MRS Page)**

```html
{% extends 'core/base.html' %}

{% block title %}Material Requisition Slip (MRS) - New{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ activeTab: '{{ selected_tab }}' }">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Material Requisition Slip [MRS] - New</h2>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6">
        <!-- Tabs for Item Master and Selected Items -->
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <a @click.prevent="activeTab = 'item_master'"
                   :class="activeTab === 'item_master' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'"
                   class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Item Master
                </a>
                <a @click.prevent="activeTab = 'selected_items'"
                   :class="activeTab === 'selected_items' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'"
                   class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Selected Items
                </a>
            </nav>
        </div>

        <!-- Tab Content -->
        <div class="mt-6">
            <!-- Item Master Tab Content -->
            <div x-show="activeTab === 'item_master'">
                <div class="mb-4" id="item-search-form-controls"
                     hx-get="{% url 'inventory:item_search_form_controls' %}"
                     hx-trigger="load, change from:#id_item_type, change from:#id_search_code"
                     hx-target="#item-search-form-controls"
                     hx-swap="outerHTML">
                    <!-- Initial search form -->
                    {% include 'inventory/mrs/_item_search_form_controls.html' with item_search_form=item_search_form %}
                </div>

                <div class="flex justify-start mb-4">
                    <button id="btnSearch"
                            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded redbox"
                            hx-get="{% url 'inventory:item_list_table' %}"
                            hx-target="#item-list-table-container"
                            hx-trigger="click"
                            hx-include="#item-search-form-controls select, #item-search-form-controls input"
                            hx-indicator="#item-list-loading-indicator">
                        Search
                    </button>
                </div>

                <div id="item-list-table-container"
                     hx-trigger="load once, searchItems from:#btnSearch"
                     hx-get="{% url 'inventory:item_list_table' %}"
                     hx-swap="innerHTML">
                    <!-- Item list table will be loaded here via HTMX -->
                    <div id="item-list-loading-indicator" class="htmx-indicator text-center">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p class="mt-2">Loading items...</p>
                    </div>
                </div>
            </div>

            <!-- Selected Items Tab Content -->
            <div x-show="activeTab === 'selected_items'">
                <div id="selected-items-table-container"
                     hx-trigger="load once, refreshSelectedItemsList from:body"
                     hx-get="{% url 'inventory:temp_item_list_table' %}"
                     hx-swap="innerHTML">
                    <!-- Selected items table will be loaded here via HTMX -->
                    <div class="text-center">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p class="mt-2">Loading selected items...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Global message container -->
<div id="messages" class="fixed bottom-4 right-4 z-50">
    {% include 'core/messages.html' %} {# Assumes core/messages.html for django.contrib.messages #}
</div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('mrsApp', () => ({
            activeTab: '{{ selected_tab }}', // Initialize tab state from Django context
            init() {
                // Ensure initial tab is loaded via HTMX if not already present
                if (this.activeTab === 'item_master' && !document.getElementById('item-list-table-container').children.length) {
                    htmx.trigger(document.getElementById('item-list-table-container'), 'load');
                } else if (this.activeTab === 'selected_items' && !document.getElementById('selected-items-table-container').children.length) {
                    htmx.trigger(document.getElementById('selected-items-table-container'), 'load');
                }

                // Listen for custom HX-Trigger 'showMessage' to reload messages
                document.body.addEventListener('showMessage', function() {
                    htmx.trigger(document.getElementById('messages'), 'load');
                });
            }
        }));
    });

    // Handle DataTables initialization for HTMX loaded content
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'item-list-table-container' || evt.detail.target.id === 'selected-items-table-container') {
            const tableId = evt.detail.target.querySelector('table').id;
            if (tableId && !$.fn.DataTable.isDataTable(`#${tableId}`)) {
                $(`#${tableId}`).DataTable({
                    "pageLength": 10, // Default page length
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "destroy": true, // Allow reinitialization
                    "paging": true,
                    "ordering": true,
                    "info": true,
                    "searching": true // Enable client-side search by default
                });
            }
        }
    });
</script>
{% endblock %}
```

**`_item_search_form_controls.html` (Partial for Item Search Form)**

```html
<div id="item-search-form-controls">
    <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-4">
        <div>
            <label for="{{ item_search_form.item_type.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ item_search_form.item_type.label }}</label>
            {{ item_search_form.item_type }}
        </div>
        <div>
            <label for="{{ item_search_form.category.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ item_search_form.category.label }}</label>
            {{ item_search_form.category }}
        </div>
        <div>
            <label for="{{ item_search_form.search_code.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ item_search_form.search_code.label }}</label>
            {{ item_search_form.search_code }}
        </div>
        <div>
            <label for="{{ item_search_form.location.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ item_search_form.location.label }}</label>
            {{ item_search_form.location }}
        </div>
        <div>
            <label for="{{ item_search_form.search_term.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ item_search_form.search_term.label }}</label>
            {{ item_search_form.search_term }}
        </div>
    </div>
</div>
```

**`_item_list_table.html` (Partial for Item Search Results)**

```html
<table id="itemListTable" class="min-w-full bg-white table-auto">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Stock Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BGGroup/WoNo</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type Details</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Req. Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% if items %}
            {% for item in items %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter0|add:items.start_index }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.itemcode }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ item.manfdesc }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.uombasic.symbol }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.stockqty|floatformat:3 }}</td>
                <td class="py-2 px-4 border-b border-gray-200" colspan="2">
                    <form hx-post="{% url 'inventory:dynamic_add_temp_item_form_fields' item.id %}" hx-swap="outerHTML" hx-target="#add-form-{{ item.id }}-fields">
                        <div id="add-form-{{ item.id }}-fields">
                            {% with form=add_temp_item_form %}
                                {{ form.item_id.as_hidden }} {# Keep item_id hidden for all rows #}
                                <input type="hidden" name="item_id" value="{{ item.id }}">
                                <div class="mb-2">
                                    <label for="id_dept_wo_type_{{ item.id }}" class="sr-only">Type</label>
                                    <select id="id_dept_wo_type_{{ item.id }}" name="dept_wo_type" class="box3 w-full" hx-post="this" hx-swap="outerHTML" hx-target="closest('tr').querySelector('[data-hx-target-fields]')" hx-trigger="change">
                                        {% for value, label in add_temp_item_form.fields.dept_wo_type.choices %}
                                            <option value="{{ value }}" {% if add_temp_item_form.data.dept_wo_type == value %}selected{% endif %}>{{ label }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="mb-2 {% if add_temp_item_form.data.dept_wo_type != '1' %}hidden{% endif %}">
                                    <label for="id_dept_id_{{ item.id }}" class="sr-only">Department</label>
                                    <select id="id_dept_id_{{ item.id }}" name="dept_id" class="box3 w-full">
                                        <option value="">Select</option>
                                        {% for bg in add_temp_item_form.fields.dept_id.queryset %}
                                            <option value="{{ bg.id }}" {% if add_temp_item_form.data.dept_id|stringformat:"s" == bg.id|stringformat:"s" %}selected{% endif %}>{{ bg.symbol }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="mb-2 {% if add_temp_item_form.data.dept_wo_type != '2' %}hidden{% endif %}">
                                    <label for="id_wo_no_{{ item.id }}" class="sr-only">WO Number</label>
                                    <input type="text" id="id_wo_no_{{ item.id }}" name="wo_no" value="{{ add_temp_item_form.data.wo_no|default:'' }}" class="box3 w-full" placeholder="WO No." />
                                </div>
                            {% endwith %}
                        </div>
                    </form>
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">
                    <input type="text" name="req_qty" form="add-form-{{ item.id }}" value="{{ add_temp_item_form.data.req_qty|default:'' }}" class="box3 w-full" placeholder="Req. Qty" />
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.location.locationlabel }}-{{ item.location.locationno }}</td>
                <td class="py-2 px-4 border-b border-gray-200">
                    <input type="text" name="remarks" form="add-form-{{ item.id }}" value="{{ add_temp_item_form.data.remarks|default:'' }}" class="box3 w-full" placeholder="Remarks" />
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <button id="add-btn-{{ item.id }}"
                            class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded redbox"
                            hx-post="{% url 'inventory:add_temp_item' %}"
                            hx-include="closest('tr') [name='item_id'], closest('tr') [name='dept_wo_type'], closest('tr') [name='dept_id'], closest('tr') [name='wo_no'], closest('tr') [name='req_qty'], closest('tr') [name='remarks']"
                            hx-swap="none"
                            hx-trigger="click">
                        Add
                    </button>
                </td>
            </tr>
            {% endfor %}
        {% else %}
        <tr>
            <td colspan="11" class="py-4 px-4 text-center text-lg text-maroon font-bold">No data to display !</td>
        </tr>
        {% endif %}
    </tbody>
</table>
```

**`_add_temp_item_form_fields.html` (Partial for dynamically updating form fields within a row)**

```html
{# This template is rendered by DynamicAddTempItemFormView to update only the specific fields in a row #}
{# It assumes `form` and `item` are available in context #}
<div id="add-form-{{ item.id }}-fields">
    {{ form.item_id.as_hidden }}
    <input type="hidden" name="item_id" value="{{ item.id }}">
    <div class="mb-2">
        <label for="id_dept_wo_type_{{ item.id }}" class="sr-only">Type</label>
        <select id="id_dept_wo_type_{{ item.id }}" name="dept_wo_type" class="box3 w-full" hx-post="this" hx-swap="outerHTML" hx-target="closest('tr').querySelector('[data-hx-target-fields]')" hx-trigger="change">
            {% for value, label in form.fields.dept_wo_type.choices %}
                <option value="{{ value }}" {% if form.data.dept_wo_type == value %}selected{% endif %}>{{ label }}</option>
            {% endfor %}
        </select>
    </div>
    <div class="mb-2 {% if form.data.dept_wo_type != '1' %}hidden{% endif %}">
        <label for="id_dept_id_{{ item.id }}" class="sr-only">Department</label>
        <select id="id_dept_id_{{ item.id }}" name="dept_id" class="box3 w-full">
            <option value="">Select</option>
            {% for bg in form.fields.dept_id.queryset %}
                <option value="{{ bg.id }}" {% if form.data.dept_id|stringformat:"s" == bg.id|stringformat:"s" %}selected{% endif %}>{{ bg.symbol }}</option>
            {% endfor %}
        </select>
    </div>
    <div class="mb-2 {% if form.data.dept_wo_type != '2' %}hidden{% endif %}">
        <label for="id_wo_no_{{ item.id }}" class="sr-only">WO Number</label>
        <input type="text" id="id_wo_no_{{ item.id }}" name="wo_no" value="{{ form.data.wo_no|default:'' }}" class="box3 w-full" placeholder="WO No." />
    </div>
</div>
```

**`_selected_items_table.html` (Partial for Selected Items List)**

```html
<table id="selectedItemsTable" class="min-w-full bg-white table-auto">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Stk Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">BGGroup</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">WONo</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Req. Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
        </tr>
    </thead>
    <tbody>
        {% if temp_items %}
            {% for item in temp_items %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter0|add:temp_items.start_index }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <button class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                            hx-post="{% url 'inventory:delete_temp_item' item.id %}"
                            hx-confirm="Are you sure you want to delete this item from the temporary list?"
                            hx-swap="none"
                            hx-trigger="click">
                        Delete
                    </button>
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.item.itemcode }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ item.item.manfdesc }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.item.uombasic.symbol }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.item.stockqty|floatformat:3 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.dept.symbol|default:"NA" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.wono|default:"NA" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.reqqty|floatformat:3 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ item.remarks }}</td>
            </tr>
            {% endfor %}
        {% else %}
        <tr>
            <td colspan="10" class="py-4 px-4 text-center text-lg text-maroon font-bold">No data to display !</td>
        </tr>
        {% endif %}
    </tbody>
    <tfoot>
        <tr>
            <td colspan="10" class="py-2 px-4 border-t border-gray-200 bg-gray-50 text-right">
                <button class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded redbox"
                        hx-post="{% url 'inventory:generate_mrs' %}"
                        hx-confirm="Are you sure you want to generate the Material Requisition Slip with these items?"
                        hx-swap="none"
                        hx-trigger="click">
                    Generate MRS
                </button>
            </td>
        </tr>
    </tfoot>
</table>
```

### 4.5 URLs (`inventory/urls.py`)

Task: Define URL patterns for all views and HTMX endpoints.

**Instructions:**

URL patterns are clearly defined, using Django's `path` and `reverse_lazy` for consistent naming and easy referencing.

```python
from django.urls import path
from .views import (
    MaterialRequisitionSlipView,
    ItemSearchFormControlsView,
    ItemListTableView,
    AddTempItemView,
    DynamicAddTempItemFormView,
    MaterialRequisitionTempItemListView,
    DeleteTempItemView,
    GenerateMRSView,
)

app_name = 'inventory'

urlpatterns = [
    # Main MRS page
    path('mrs/new/', MaterialRequisitionSlipView.as_view(), name='mrs_new'),

    # HTMX endpoints for Item Master tab (TabPanel1)
    path('items/search-form/', ItemSearchFormControlsView.as_view(), name='item_search_form_controls'),
    path('items/table/', ItemListTableView.as_view(), name='item_list_table'),
    path('items/add-temp/', AddTempItemView.as_view(), name='add_temp_item'),
    path('items/<int:item_id>/dynamic-form-fields/', DynamicAddTempItemFormView.as_view(), name='dynamic_add_temp_item_form_fields'),

    # HTMX endpoints for Selected Items tab (TabPanel2)
    path('temp-items/table/', MaterialRequisitionTempItemListView.as_view(), name='temp_item_list_table'),
    path('temp-items/delete/<int:pk>/', DeleteTempItemView.as_view(), name='delete_temp_item'),
    path('temp-items/generate-mrs/', GenerateMRSView.as_view(), name='generate_mrs'),
]

```

### 4.6 Tests (`inventory/tests.py`)

Task: Write comprehensive tests for the models and views, ensuring good test coverage.

**Instructions:**

Unit tests will cover model methods and business logic (e.g., `add_item_to_temp`, `finalize_mrs_from_temp`). Integration tests will verify view responses, form handling, and HTMX interactions. We aim for at least 80% test coverage.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db.utils import IntegrityError
from django.core.exceptions import ValidationError
from django.contrib.auth import get_user_model
from unittest.mock import patch, MagicMock

from .models import (
    Item, Category, Location, BusinessGroup, Unit,
    MaterialRequisitionTempItem, MaterialRequisition, MaterialRequisitionDetail,
    DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID
)

User = get_user_model()

class ModelSetupMixin:
    """Helper to create necessary related objects for tests."""
    @classmethod
    def setUpTestData(cls):
        cls.company_id = DEFAULT_COMP_ID
        cls.fin_year_id = DEFAULT_FIN_YEAR_ID

        cls.category = Category.objects.create(cid=1, symbol='CAT', cname='Category A', compid=cls.company_id)
        cls.location = Location.objects.create(id=1, locationlabel='WARE', locationno='001')
        cls.unit = Unit.objects.create(id=1, symbol='PC')
        cls.business_group = BusinessGroup.objects.create(id=101, symbol='PROD')

        cls.item1 = Item.objects.create(
            id=1, itemcode='ITEM001', manfdesc='Product A', uombasic=cls.unit,
            stockqty=100.0, location=cls.location, cid=cls.category,
            compid=cls.company_id, finyearid=cls.fin_year_id
        )
        cls.item2 = Item.objects.create(
            id=2, itemcode='ITEM002', manfdesc='Product B', uombasic=cls.unit,
            stockqty=50.0, location=cls.location, cid=cls.category,
            compid=cls.company_id, finyearid=cls.fin_year_id
        )
        cls.item3_low_stock = Item.objects.create(
            id=3, itemcode='ITEM003', manfdesc='Product C (Low Stock)', uombasic=cls.unit,
            stockqty=5.0, location=cls.location, cid=cls.category,
            compid=cls.company_id, finyearid=cls.fin_year_id
        )

        cls.user = User.objects.create_user(username='testuser', password='password123')


class MaterialRequisitionTempItemModelTest(ModelSetupMixin, TestCase):
    def setUp(self):
        self.client = Client()
        self.client.login(username='testuser', password='password123')
        # Ensure that MaterialRequisitionTempItem.objects.all().delete() is called
        # if using unique primary keys for each test case instead of relying on default setUpTestData.
        # For simplicity, relying on the class-level setUpTestData means PKs won't conflict across model methods tests.
        MaterialRequisitionTempItem.objects.all().delete() # Clean temp items before each test

    def test_add_item_to_temp_success(self):
        self.assertTrue(MaterialRequisitionTempItem.add_item_to_temp(
            item_id=self.item1.id, dept_or_wo_type='1', dept_id=self.business_group.id,
            wo_no=None, req_qty=10.0, remarks='Test add',
            user_session_id=str(self.user.id), comp_id=self.company_id, fin_year_id=self.fin_year_id
        ))
        self.assertEqual(MaterialRequisitionTempItem.objects.count(), 1)
        temp_item = MaterialRequisitionTempItem.objects.first()
        self.assertEqual(temp_item.item, self.item1)
        self.assertEqual(temp_item.reqqty, 10.0)
        self.assertEqual(temp_item.dept, self.business_group)

    def test_add_item_to_temp_duplicate(self):
        MaterialRequisitionTempItem.add_item_to_temp(
            item_id=self.item1.id, dept_or_wo_type='1', dept_id=self.business_group.id,
            wo_no=None, req_qty=10.0, remarks='Test add',
            user_session_id=str(self.user.id), comp_id=self.company_id, fin_year_id=self.fin_year_id
        )
        with self.assertRaises(ValidationError) as cm:
            MaterialRequisitionTempItem.add_item_to_temp(
                item_id=self.item1.id, dept_or_wo_type='1', dept_id=self.business_group.id,
                wo_no=None, req_qty=5.0, remarks='Duplicate',
                user_session_id=str(self.user.id), comp_id=self.company_id, fin_year_id=self.fin_year_id
            )
        self.assertIn("Item is already selected for MRS.", cm.exception.message)

    def test_add_item_to_temp_stock_exceeded(self):
        with self.assertRaises(ValidationError) as cm:
            MaterialRequisitionTempItem.add_item_to_temp(
                item_id=self.item3_low_stock.id, dept_or_wo_type='1', dept_id=self.business_group.id,
                wo_no=None, req_qty=10.0, remarks='Too much',
                user_session_id=str(self.user.id), comp_id=self.company_id, fin_year_id=self.fin_year_id
            )
        self.assertIn("Requested Quantity should be less than or equal to Stock Quantity.", cm.exception.message)

    def test_add_item_to_temp_missing_dept_for_bg(self):
        with self.assertRaises(ValidationError) as cm:
            MaterialRequisitionTempItem.add_item_to_temp(
                item_id=self.item1.id, dept_or_wo_type='1', dept_id='0', # '0' simulates 'Select'
                wo_no=None, req_qty=10.0, remarks='Missing dept',
                user_session_id=str(self.user.id), comp_id=self.company_id, fin_year_id=self.fin_year_id
            )
        self.assertIn("Please select a Business Group.", cm.exception.message)

    @patch('inventory.models.MaterialRequisitionTempItem.check_valid_wo_no', return_value=False)
    def test_add_item_to_temp_invalid_wo_no(self, mock_check_wo_no):
        with self.assertRaises(ValidationError) as cm:
            MaterialRequisitionTempItem.add_item_to_temp(
                item_id=self.item1.id, dept_or_wo_type='2', dept_id=None,
                wo_no='INVALIDWO', req_qty=10.0, remarks='Invalid WO',
                user_session_id=str(self.user.id), comp_id=self.company_id, fin_year_id=self.fin_year_id
            )
        self.assertIn("WO Number or Dept is not found!", cm.exception.message)
        mock_check_wo_no.assert_called_once_with('INVALIDWO', self.company_id, self.fin_year_id)

    @patch('inventory.models.MaterialRequisitionTempItem.check_valid_wo_no', return_value=True)
    def test_add_item_to_temp_with_wo_no_success(self, mock_check_wo_no):
        self.assertTrue(MaterialRequisitionTempItem.add_item_to_temp(
            item_id=self.item1.id, dept_or_wo_type='2', dept_id=None,
            wo_no='WO123', req_qty=10.0, remarks='Test WO add',
            user_session_id=str(self.user.id), comp_id=self.company_id, fin_year_id=self.fin_year_id
        ))
        self.assertEqual(MaterialRequisitionTempItem.objects.count(), 1)
        temp_item = MaterialRequisitionTempItem.objects.first()
        self.assertEqual(temp_item.wono, 'WO123')
        self.assertIsNone(temp_item.dept)
        mock_check_wo_no.assert_called_once_with('WO123', self.company_id, self.fin_year_id)

class MaterialRequisitionModelTest(ModelSetupMixin, TestCase):
    def setUp(self):
        self.client = Client()
        self.client.login(username='testuser', password='password123')
        MaterialRequisitionTempItem.objects.all().delete()
        MaterialRequisition.objects.all().delete()
        MaterialRequisitionDetail.objects.all().delete()

    def test_generate_next_mrs_no(self):
        # Initial MRS number
        self.assertEqual(MaterialRequisition.generate_next_mrs_no(self.company_id, self.fin_year_id), '0001')
        MaterialRequisition.objects.create(id=1, mrsno='0001', compid=self.company_id, finyearid=self.fin_year_id)
        self.assertEqual(MaterialRequisition.generate_next_mrs_no(self.company_id, self.fin_year_id), '0002')
        MaterialRequisition.objects.create(id=2, mrsno='0010', compid=self.company_id, finyearid=self.fin_year_id)
        self.assertEqual(MaterialRequisition.generate_next_mrs_no(self.company_id, self.fin_year_id), '0011')

    def test_finalize_mrs_from_temp_success(self):
        MaterialRequisitionTempItem.objects.create(
            id=1, compid=self.company_id, sessionid=str(self.user.id), item=self.item1,
            dept=self.business_group, wono=None, reqqty=10.0, remarks='Item 1 for MRS'
        )
        MaterialRequisitionTempItem.objects.create(
            id=2, compid=self.company_id, sessionid=str(self.user.id), item=self.item2,
            dept=None, wono='WO456', reqqty=5.0, remarks='Item 2 for MRS'
        )

        mrs_master = MaterialRequisition.finalize_mrs_from_temp(
            user_session_id=str(self.user.id), comp_id=self.company_id, fin_year_id=self.fin_year_id
        )

        self.assertIsNotNone(mrs_master)
        self.assertEqual(MaterialRequisition.objects.count(), 1)
        self.assertEqual(MaterialRequisitionDetail.objects.count(), 2)
        self.assertEqual(MaterialRequisitionTempItem.objects.count(), 0) # Temp items should be cleared

        detail1 = MaterialRequisitionDetail.objects.get(item=self.item1)
        self.assertEqual(detail1.mid, mrs_master)
        self.assertEqual(detail1.reqqty, 10.0)

        detail2 = MaterialRequisitionDetail.objects.get(item=self.item2)
        self.assertEqual(detail2.mid, mrs_master)
        self.assertEqual(detail2.reqqty, 5.0)
        self.assertEqual(detail2.wono, 'WO456')

    def test_finalize_mrs_from_temp_no_items(self):
        with self.assertRaises(ValidationError) as cm:
            MaterialRequisition.finalize_mrs_from_temp(
                user_session_id=str(self.user.id), comp_id=self.company_id, fin_year_id=self.fin_year_id
            )
        self.assertIn("No items selected to generate MRS.", cm.exception.message)
        self.assertEqual(MaterialRequisition.objects.count(), 0)
        self.assertEqual(MaterialRequisitionDetail.objects.count(), 0)


class MaterialRequisitionViewsTest(ModelSetupMixin, TestCase):
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(username='testuser', password='password123')
        self.client.force_login(self.user)
        MaterialRequisitionTempItem.objects.all().delete()

    def test_mrs_new_view(self):
        response = self.client.get(reverse('inventory:mrs_new'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/mrs/main.html')
        self.assertContains(response, 'Item Master')
        self.assertContains(response, 'Selected Items')

    def test_item_search_form_controls_view_get(self):
        response = self.client.get(reverse('inventory:item_search_form_controls'), {'item_type': 'Category'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/mrs/_item_search_form_controls.html')
        self.assertContains(response, 'id_category')
        self.assertContains(response, 'id_search_code')
        self.assertNotContains(response, 'id_location hidden') # Category type allows location
        self.assertNotContains(response, 'id_search_term hidden') # Category type allows search term

    def test_item_list_table_view_get_initial(self):
        response = self.client.get(reverse('inventory:item_list_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/mrs/_item_list_table.html')
        self.assertContains(response, 'No data to display') # Should be empty without filters

    def test_item_list_table_view_get_with_filters(self):
        response = self.client.get(reverse('inventory:item_list_table'), {
            'item_type': 'Category',
            'category': self.category.cid,
            'search_code': 'tblDG_Item_Master.ManfDesc',
            'search_term': 'Product A'
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/mrs/_item_list_table.html')
        self.assertContains(response, self.item1.itemcode)
        self.assertNotContains(response, 'No data to display')

    def test_add_temp_item_view_post_success(self):
        data = {
            'item_id': self.item1.id,
            'dept_wo_type': '1', # BGGroup
            'dept_id': self.business_group.id,
            'req_qty': 10.0,
            'remarks': 'Test add via HTMX',
        }
        response = self.client.post(reverse('inventory:add_temp_item'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertTrue(MaterialRequisitionTempItem.objects.filter(item=self.item1).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshSelectedItemsList', response.headers['HX-Trigger'])

    def test_add_temp_item_view_post_validation_error(self):
        data = {
            'item_id': self.item3_low_stock.id, # Stock 5.0
            'dept_wo_type': '1',
            'dept_id': self.business_group.id,
            'req_qty': 10.0, # Too much
            'remarks': 'Exceed stock',
        }
        response = self.client.post(reverse('inventory:add_temp_item'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Returns 200 with messages on error
        self.assertFalse(MaterialRequisitionTempItem.objects.filter(item=self.item3_low_stock).exists())
        self.assertContains(response, "HX-Trigger") # Should have a trigger for messages
        self.assertIn('showMessage', response.headers['HX-Trigger'])

    def test_dynamic_add_temp_item_form_view_post(self):
        # Simulate changing the dropdown to BGGroup
        data = {
            'dept_wo_type': '1',
            'item_id': self.item1.id # This would be passed from hidden input
        }
        response = self.client.post(reverse('inventory:dynamic_add_temp_item_form_fields', args=[self.item1.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/mrs/_add_temp_item_form_fields.html')
        self.assertContains(response, 'id_dept_id') # Dept dropdown should be visible
        self.assertContains(response, 'id_wo_no hidden') # WO no textbox should be hidden

        # Simulate changing to WONo
        data = {
            'dept_wo_type': '2',
            'item_id': self.item1.id
        }
        response = self.client.post(reverse('inventory:dynamic_add_temp_item_form_fields', args=[self.item1.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/mrs/_add_temp_item_form_fields.html')
        self.assertContains(response, 'id_dept_id hidden') # Dept dropdown should be hidden
        self.assertContains(response, 'id_wo_no') # WO no textbox should be visible

    def test_material_requisition_temp_item_list_view_get(self):
        # Add some temp items first
        MaterialRequisitionTempItem.objects.create(
            id=1, compid=self.company_id, sessionid=str(self.user.id), item=self.item1,
            dept=self.business_group, wono=None, reqqty=10.0, remarks='Test temp item'
        )
        response = self.client.get(reverse('inventory:temp_item_list_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/mrs/_selected_items_table.html')
        self.assertContains(response, self.item1.itemcode)
        self.assertContains(response, 'Generate MRS')

    def test_delete_temp_item_view_post_success(self):
        temp_item = MaterialRequisitionTempItem.objects.create(
            id=1, compid=self.company_id, sessionid=str(self.user.id), item=self.item1,
            dept=self.business_group, wono=None, reqqty=10.0, remarks='To be deleted'
        )
        response = self.client.post(reverse('inventory:delete_temp_item', args=[temp_item.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(MaterialRequisitionTempItem.objects.filter(id=temp_item.id).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshSelectedItemsList', response.headers['HX-Trigger'])

    def test_generate_mrs_view_post_success(self):
        MaterialRequisitionTempItem.objects.create(
            id=1, compid=self.company_id, sessionid=str(self.user.id), item=self.item1,
            dept=self.business_group, wono=None, reqqty=10.0, remarks='Item 1 for MRS'
        )
        response = self.client.post(reverse('inventory:generate_mrs'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(MaterialRequisitionTempItem.objects.count(), 0) # Temp items cleared
        self.assertEqual(MaterialRequisition.objects.count(), 1) # MRS created
        self.assertEqual(MaterialRequisitionDetail.objects.count(), 1) # Detail created
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshSelectedItemsList', response.headers['HX-Trigger'])

    def test_generate_mrs_view_post_no_items(self):
        response = self.client.post(reverse('inventory:generate_mrs'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Returns 200 with messages on error
        self.assertEqual(MaterialRequisitionTempItem.objects.count(), 0)
        self.assertEqual(MaterialRequisition.objects.count(), 0)
        self.assertContains(response, "HX-Trigger")
        self.assertIn('showMessage', response.headers['HX-Trigger'])
```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**

The provided templates and views already integrate HTMX and Alpine.js:

*   **HTMX:**
    *   The `main.html` uses `hx-get` on `div` elements to load the item search form controls, item list table, and selected items table.
    *   `hx-trigger="load, change from:..."` ensures dynamic updates of form controls and table data.
    *   `hx-post` is used for adding/deleting temporary items and generating the MRS.
    *   `hx-swap="none"` and `hx-trigger="refreshSelectedItemsList"` (or `showMessage`) are used for actions that don't directly return content but trigger refreshes of other parts of the page or global messages.
    *   The "Add" button's `hx-include` attribute gathers all necessary data from the parent table row.
    *   The `dept_wo_type` dropdown uses `hx-post="this"` and `hx-target="closest('tr').querySelector('[data-hx-target-fields]')"` to update only the relevant form fields within its row, making the UI highly dynamic without full page reloads.
*   **Alpine.js:**
    *   The `main.html` uses `x-data="{ activeTab: '{{ selected_tab }}' }"` to manage the state of the tabs.
    *   `@click.prevent` on tab headers changes the `activeTab` variable, which then conditionally shows/hides tab content using `x-show`.
    *   `document.addEventListener('alpine:init', ...)` ensures Alpine.js component initialization.
*   **DataTables:**
    *   The `_item_list_table.html` and `_selected_items_table.html` use `id="itemListTable"` and `id="selectedItemsTable"` respectively.
    *   A JavaScript snippet in `base.html` (or `main.html` in this case for demonstration) listens for `htmx:afterSwap` events. When a new table is loaded via HTMX, it checks if the table is already a DataTable and, if not, initializes it, providing client-side search, sorting, and pagination.

## Final Notes

*   **Placeholders:** Replace `DEFAULT_COMP_ID` and `DEFAULT_FIN_YEAR_ID` with actual logic to retrieve the company ID and financial year ID, likely from the authenticated user's profile or a global context manager.
*   **DRY Templates:** The use of partial templates (`_item_search_form_controls.html`, `_item_list_table.html`, `_selected_items_table.html`, `_add_temp_item_form_fields.html`) promotes reusability and clean separation of concerns.
*   **Fat Models:** All complex business logic from the ASP.NET code-behind (e.g., stock validation, WO number validation, MRS generation, complex data retrieval/joins) has been moved into the Django `models.py` file, either as class methods or instance methods. This keeps views concise and focused on handling HTTP requests and responses.
*   **Thin Views:** All Django `View` methods are kept extremely compact, primarily delegating to model methods or rendering simple forms/templates.
*   **Comprehensive Tests:** The provided tests demonstrate how to achieve good coverage for both model business logic and view interactions, including HTMX-specific tests.
*   **Error Handling:** The views use `django.contrib.messages` and `HX-Trigger` to communicate feedback to the user, ensuring a clear and immediate response even without full page reloads.