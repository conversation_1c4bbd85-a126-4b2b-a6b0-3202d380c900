## ASP.NET to Django Conversion Script: Customer Challan List & Search

This document outlines a strategic plan to modernize your existing ASP.NET Customer Challan list and search functionality into a robust, scalable, and maintainable Django application. Our approach leverages AI-assisted automation for systematic conversion, minimizing manual effort and ensuring a seamless transition.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

This conversion focuses on the `CustomerChallan_Edit.aspx` page, which primarily serves as a list and search interface for customer challans, including an autocomplete feature for customer names. The actual "editing" of challan *details* appears to occur on a separate page (`CustomerChallan_Edit_Details.aspx`), which falls outside the scope of this initial conversion for the list page.

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the ASP.NET code.

**Instructions:**
From the ASP.NET code, we identify two primary data sources:
1.  The `GetCustChallan` stored procedure, which populates the `SearchGridView1`. This procedure returns data that includes `Id`, `FinYear`, `CCNo`, `CustomerName`, and `CustomerId`. We'll assume a conceptual `CustomerChallan_Data` table that this procedure primarily queries or constructs its output from.
2.  The `SD_Cust_master` table, used by the `sql` web method for customer autocomplete. It exposes `CustomerId` and `CustomerName`.

**Extracted Schema:**

*   **For Customer Challan List (conceptual `CustomerChallan_Data`):**
    *   `Id` (Primary Key, Integer)
    *   `FinYear` (Financial Year, Text/Char)
    *   `CCNo` (Customer Challan Number, Text/Char)
    *   `CustomerName` (Customer's Full Name, Text/Char)
    *   `CustomerId` (Customer's Code, Text/Char)

*   **For Customer Master (actual `SD_Cust_master` table):**
    *   `CustomerId` (Primary Key for this table, Text/Char)
    *   `CustomerName` (Customer's Full Name, Text/Char)
    *   `CompId` (Company ID, Integer - likely part of a composite key or a filter column)

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations performed by the ASP.NET code.

**Instructions:**
The ASP.NET code primarily performs **Read** operations with **Filtering/Search** and **Autocomplete**.

*   **Read (List View):**
    *   The `Page_Load` and `SearchGridView1_PageIndexChanging` events call `BindData` to retrieve a list of customer challans.
    *   The `BindData` method executes the `GetCustChallan` stored procedure, filtering by `CustomerId`, `CompId` (Company ID from session), and `FinId` (Financial Year ID from session).
*   **Filter/Search:**
    *   The `Search_Click` event uses `TxtSearchValue` and `fun.getCode` to extract a customer ID, then calls `BindData` to re-filter the list.
*   **Autocomplete:**
    *   The `sql` web method provides suggestions for `TxtSearchValue` by querying `SD_Cust_master` based on `prefixText` and `CompId` from the session. It formats results as "CustomerName [CustomerId]".
*   **No Create/Update/Delete:** This page only displays a list and links to a separate "details" page. Therefore, no direct CRUD operations for challans are implemented on *this* specific page.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, mapping them to modern web components.

**Instructions:**
The following ASP.NET UI components and their functionalities will be transformed:

*   **`TxtSearchValue` (TextBox with `AutoCompleteExtender`):** This will become a standard HTML `<input type="text">` field. Its autocomplete functionality will be powered by HTMX (`hx-get`, `hx-trigger`, `hx-target`) interacting with a Django view, and Alpine.js for managing the dropdown list's visibility and selection behavior.
*   **`Search` (Button):** This will be an HTML `<button>` that triggers an HTMX request (`hx-post` or `hx-get`) to re-load the challan list based on the search criteria, without a full page refresh.
*   **`SearchGridView1` (GridView):** This will be replaced by a standard HTML `<table>` element. DataTables.js will be applied to this table on the client-side to provide advanced features like pagination, instant search, and sorting, mirroring the GridView's capabilities but with enhanced performance and user experience.
*   **HyperLinkField `CCNo`:** This will be a standard `<a>` tag within the DataTables row, linking to the detail/edit page for the specific customer challan.

---

### Step 4: Generate Django Code

We will create a new Django application, let's call it `inventory`, to house this functionality.

#### 4.1 Models (`inventory/models.py`)

**Task:** Create Django models based on the identified database schema. We'll include powerful class methods to encapsulate business logic (fat model approach) such as data retrieval for the challan list and customer autocomplete.

**Instructions:**
-   Define `CustomerChallan` mapping to the conceptual output of `GetCustChallan`.
-   Define `Customer` mapping to `SD_Cust_master`.
-   Implement class methods for `CustomerChallan` to retrieve filtered lists (replacing `BindData`) and for `Customer` to provide autocomplete suggestions (replacing the `sql` web method).
-   Remember to use `managed = False` as these models will map to existing database tables/views.

```python
# inventory/models.py
from django.db import models, connection
import re # For parsing customer string

class CustomerChallan(models.Model):
    """
    Represents customer challan data, typically retrieved from a stored procedure
    or view like 'GetCustChallan'.
    This model is read-only for listing purposes on this page.
    """
    id = models.IntegerField(db_column='Id', primary_key=True) # Assumed PK
    fin_year = models.CharField(db_column='FinYear', max_length=50)
    cc_no = models.CharField(db_column='CCNo', max_length=50)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    customer_id = models.CharField(db_column='CustomerId', max_length=50)

    class Meta:
        managed = False # Django won't manage table creation/deletion
        db_table = 'CustomerChallan_Data_View' # Or whatever the underlying view/table for GetCustChallan is
        verbose_name = 'Customer Challan'
        verbose_name_plural = 'Customer Challans'

    def __str__(self):
        return f"Challan {self.cc_no} for {self.customer_name}"

    @classmethod
    def get_challan_list(cls, customer_id: str = '', company_id: int = 0, financial_year_id: str = ''):
        """
        Retrieves a list of customer challans, mimicking the 'BindData' functionality.
        This method directly calls the 'GetCustChallan' stored procedure.
        """
        try:
            with connection.cursor() as cursor:
                # The ASP.NET code passes an 'x' parameter which is ' And SD_Cust_master.CustomerId=' + spid
                # and then maps it to @CustId. This implies direct SQL injection or string concatenation.
                # In Django, we'll pass the actual customer_id and let the SP handle it, or construct
                # a safe query. Given the original SP call:
                # da.SelectCommand.Parameters["@CustId"].Value = x;
                # da.SelectCommand.Parameters["@CompId"].Value = CId;
                # da.SelectCommand.Parameters["@FinId"].Value = FyId;
                # We assume the SP expects the actual customer ID.
                # If 'x' was passed directly, we'd need to adjust.
                
                # IMPORTANT: Replace 'GetCustChallan' with the actual stored procedure name
                # and verify parameter names and types.
                cursor.execute(
                    "EXEC GetCustChallan @CustId=%s, @CompId=%s, @FinId=%s",
                    [customer_id, company_id, financial_year_id]
                )
                # Fetch all results from the stored procedure
                columns = [col[0] for col in cursor.description]
                raw_data = cursor.fetchall()

                # Convert raw data to model instances or dictionaries
                challans = []
                for row in raw_data:
                    # Map the row data to the model's fields directly
                    # Assumes column names from SP match model field names (or db_column names)
                    challan_data = dict(zip(columns, row))
                    # Create an instance, ensuring 'id' is mapped correctly if it's not 'Id' in SP output
                    challans.append(cls(
                        id=challan_data.get('Id'),
                        fin_year=challan_data.get('FinYear'),
                        cc_no=challan_data.get('CCNo'),
                        customer_name=challan_data.get('CustomerName'),
                        customer_id=challan_data.get('CustomerId')
                    ))
                return challans
        except Exception as e:
            # Log the exception for debugging
            print(f"Error fetching challan list: {e}")
            return [] # Return empty list on error

class Customer(models.Model):
    """
    Represents a customer from the SD_Cust_master table, used for autocomplete.
    """
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customer_name

    @classmethod
    def search_customers(cls, prefix_text: str, company_id: int):
        """
        Searches for customers matching a prefix and company ID, mimicking the 'sql' web method.
        Returns a list of formatted strings: "CustomerName [CustomerId]".
        """
        # Case-insensitive search
        customers = cls.objects.filter(
            customer_name__icontains=prefix_text,
            comp_id=company_id
        ).order_by('customer_name')[:10] # Limit to 10 as per typical autocomplete behavior

        return [f"{c.customer_name} [{c.customer_id}]" for c in customers]

    @staticmethod
    def extract_customer_id_from_formatted_string(formatted_string: str) -> str:
        """
        Parses a string like "CustomerName [CustomerId]" and returns CustomerId.
        Mimics the 'fun.getCode' logic.
        """
        match = re.search(r'\[(.*?)\]$', formatted_string)
        if match:
            return match.group(1)
        return formatted_string # Return original if not found, or empty string/None depending on desired error handling
```

#### 4.2 Forms (`inventory/forms.py`)

**Task:** Define a Django form for user input. For this list page, we don't need a full `ModelForm` for the `CustomerChallan` itself. We'll instead define a simple form for the search input to manage basic validation if necessary, or handle the search input directly in the view if simple. For the autocomplete search, no explicit form is strictly needed.

**Instructions:**
Since the page's primary interaction is search, we'll create a lightweight form for the search input. This allows for Django's form validation if more complex search logic were added.

```python
# inventory/forms.py
from django import forms

class CustomerChallanSearchForm(forms.Form):
    """
    Form for the customer challan search input.
    """
    search_value = forms.CharField(
        required=False,
        label="Customer Name",
        widget=forms.TextInput(attrs={
            'id': 'TxtSearchValue', # Match original ID for clarity
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Start typing customer name...',
            'hx-get': '/inventory/customerchallan/autocomplete/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms, search', # Trigger on keyup after delay
            'hx-target': '#autocomplete-results', # Target div for results
            'hx-swap': 'innerHTML',
            'autocomplete': 'off', # Disable browser autocomplete
            '@input': 'showResults = true' # Alpine.js to show results on input
        })
    )

```

#### 4.3 Views (`inventory/views.py`)

**Task:** Implement the list view, the partial view for the DataTables table, and the autocomplete view using Class-Based Views (CBVs). All views will be kept thin, delegating business logic to the models.

**Instructions:**
-   `CustomerChallanListView`: Renders the main page with the search form.
-   `CustomerChallanTablePartialView`: Handles the HTMX request to render just the table content, incorporating search filters.
-   `CustomerAutocompleteView`: Provides JSON or HTML snippets for autocomplete suggestions.

```python
# inventory/views.py
from django.views.generic import ListView, TemplateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from .models import CustomerChallan, Customer
from .forms import CustomerChallanSearchForm

class CustomerChallanListView(TemplateView):
    """
    Main view for displaying the customer challan list page.
    Renders the search form and container for the HTMX-loaded table.
    """
    template_name = 'inventory/customerchallan/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the search form
        context['form'] = CustomerChallanSearchForm()
        return context

class CustomerChallanTablePartialView(TemplateView):
    """
    HTMX endpoint to load/reload the customer challan table.
    Filters data based on request parameters.
    """
    template_name = 'inventory/customerchallan/_customerchallan_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get session variables (mimicking ASP.NET Session)
        company_id = self.request.session.get('compid', 1)  # Default to 1 if not in session
        financial_year_id = self.request.session.get('finyear', '2023-2024') # Default value

        # Get search value from GET parameters (HTMX sends form data as GET for table updates)
        # Parse customer ID from the formatted string if present
        search_value_raw = self.request.GET.get('search_value', '')
        customer_id_filter = Customer.extract_customer_id_from_formatted_string(search_value_raw)

        # Call the model method to get filtered data
        customer_challans = CustomerChallan.get_challan_list(
            customer_id=customer_id_filter,
            company_id=company_id,
            financial_year_id=financial_year_id
        )
        context['customer_challans'] = customer_challans
        return context

class CustomerAutocompleteView(View):
    """
    HTMX endpoint for customer name autocomplete suggestions.
    Returns a list of customer names as HTML or JSON.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('search_value', '')
        company_id = request.session.get('compid', 1) # Default to 1 if not in session

        if not prefix_text:
            return HttpResponse("") # Return empty response if no input

        suggestions = Customer.search_customers(prefix_text, company_id)
        
        # Render as HTML list for HTMX to swap into the target
        html_suggestions = render_to_string(
            'inventory/customerchallan/_autocomplete_results.html',
            {'suggestions': suggestions}
        )
        return HttpResponse(html_suggestions)

```

#### 4.4 Templates (`inventory/templates/inventory/customerchallan/`)

**Task:** Create templates for the main list page and partial templates for the DataTables table and autocomplete results. These templates will heavily utilize HTMX, Alpine.js, and Tailwind CSS.

**Instructions:**
-   `list.html`: The main page, extends `core/base.html`, includes search input and a placeholder for the HTMX-loaded table.
-   `_customerchallan_table.html`: Partial template containing the `<table>` structure, loaded dynamically by HTMX. This is where DataTables will be initialized.
-   `_autocomplete_results.html`: Partial template for autocomplete suggestions.

```html
{# inventory/templates/inventory/customerchallan/list.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Customer Challan - Edit</h2>
    </div>

    {# Search Section #}
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <div class="flex items-center space-x-4" x-data="{ showResults: false, selectedCustomer: '' }">
            <label for="TxtSearchValue" class="text-gray-700 font-medium whitespace-nowrap">Customer Name:</label>
            <div class="relative flex-grow">
                {{ form.search_value }}
                {# Autocomplete results container #}
                <div id="autocomplete-results" 
                     class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg mt-1 w-full max-h-60 overflow-y-auto"
                     x-show="showResults && $el.children.length > 0"
                     @click.away="showResults = false">
                    {# HTMX will swap autocomplete suggestions here #}
                </div>
            </div>
            <button id="Search" 
                    type="button" 
                    class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    hx-get="{% url 'inventory:customerchallan_table' %}"
                    hx-target="#customerchallan-table-container"
                    hx-trigger="click, keyup[event.key === 'Enter'] from:#TxtSearchValue"
                    hx-include="#TxtSearchValue"
                    _="on click set #TxtSearchValue.value to selectedCustomer; showResults = false">
                Search
            </button>
        </div>
    </div>

    {# Challan Table Section #}
    <div id="customerchallan-table-container"
         hx-trigger="load" {# Load table on page load #}
         hx-get="{% url 'inventory:customerchallan_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg p-6">
        {# Initial loading state #}
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading customer challans...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js setup if needed, but HTMX is handling much of the interaction
    // Ensure DataTables is loaded and initialized via HTMX fragments.
    // The DataTables initialization is specifically in the _customerchallan_table.html partial.
</script>
{% endblock %}
```

```html
{# inventory/templates/inventory/customerchallan/_customerchallan_table.html #}
{# This partial template contains the DataTables table and is loaded via HTMX #}

<table id="customerchallanTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">CCNo</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
            {# No 'Actions' column as this is a list view linking to a separate detail page #}
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% if customer_challans %}
            {% for challan in customer_challans %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ challan.fin_year }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    {# Mimic HyperLinkField to a details page #}
                    <a href="{% url 'inventory:customerchallan_detail' pk=challan.id %}" class="text-blue-600 hover:underline">
                        {{ challan.cc_no }}
                    </a>
                </td>
                <td class="py-2 px-4 border-b border-gray-200">{{ challan.customer_name }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ challan.customer_id }}</td>
            </tr>
            {% endfor %}
        {% else %}
            <tr>
                <td colspan="5" class="py-4 text-center text-lg text-red-700 font-semibold">No data to display !</td>
            </tr>
        {% endif %}
    </tbody>
</table>

<script>
    // Ensure DataTables is initialized only AFTER the table is loaded into the DOM
    // This script runs when the HTMX fragment is swapped in.
    $(document).ready(function() {
        $('#customerchallanTable').DataTable({
            "pageLength": 17, // Mimic original PageSize
            "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
            "responsive": true,
            "destroy": true // Destroy existing DataTable instance if any
        });
    });
</script>
```

```html
{# inventory/templates/inventory/customerchallan/_autocomplete_results.html #}
{# This partial template is for autocomplete suggestions #}
{% if suggestions %}
    <ul>
        {% for suggestion in suggestions %}
        <li class="px-4 py-2 hover:bg-blue-100 cursor-pointer"
            @click="selectedCustomer = '{{ suggestion }}'; $el.closest('#autocomplete-results').previousElementSibling.value = '{{ suggestion }}'; showResults = false;">
            {{ suggestion }}
        </li>
        {% endfor %}
    </ul>
{% else %}
    <div class="px-4 py-2 text-gray-500">No matching customers.</div>
{% endif %}
```

#### 4.5 URLs (`inventory/urls.py`)

**Task:** Define URL patterns for the views.

**Instructions:**
Create paths for the main list page, the HTMX-loaded table, the autocomplete endpoint, and a placeholder for the future detail/edit page.

```python
# inventory/urls.py
from django.urls import path
from .views import CustomerChallanListView, CustomerChallanTablePartialView, CustomerAutocompleteView

app_name = 'inventory' # Namespace for URLs

urlpatterns = [
    path('customerchallan/', CustomerChallanListView.as_view(), name='customerchallan_list'),
    path('customerchallan/table/', CustomerChallanTablePartialView.as_view(), name='customerchallan_table'),
    path('customerchallan/autocomplete/', CustomerAutocompleteView.as_view(), name='customerchallan_autocomplete'),
    # Placeholder for the detail/edit page, assuming 'Id' is the PK for CustomerChallan
    path('customerchallan/detail/<int:pk>/', CustomerChallanListView.as_view(), name='customerchallan_detail'), # This would be a DetailView/UpdateView later
]
```
*(Note: The `customerchallan_detail` path currently points to `CustomerChallanListView` as a placeholder. This would be replaced with an actual `DetailView` or `UpdateView` when implementing the 'details' page functionality.)*

#### 4.6 Tests (`inventory/tests.py`)

**Task:** Write comprehensive tests for the models and views, ensuring good test coverage.

**Instructions:**
-   **Model Tests:** Verify data retrieval, parsing, and filtering logic within the `CustomerChallan` and `Customer` models.
-   **View Tests:** Check HTTP responses, template usage, context data, and HTMX-specific headers for all views.

```python
# inventory/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.db import connection
from unittest.mock import MagicMock, patch

# Mock the database cursor for models to avoid actual DB calls during unit tests
class MockCursor:
    def __init__(self, fetchall_data=None, description_data=None):
        self._fetchall_data = fetchall_data if fetchall_data is not None else []
        self._description_data = description_data if description_data is not None else []
    
    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        pass

    def execute(self, sql, params=None):
        pass # Mock execute, actual data provided by fetchall_data

    def fetchall(self):
        return self._fetchall_data

    @property
    def description(self):
        return self._description_data

class CustomerChallanModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Setup mock data for CustomerChallan for consistency
        cls.challan_data = [
            (1, '2023-2024', 'CC001', 'Customer A', 'CUST001'),
            (2, '2023-2024', 'CC002', 'Customer B', 'CUST002'),
            (3, '2022-2023', 'CC003', 'Customer A', 'CUST001'),
        ]
        cls.challan_description = [
            ('Id',), ('FinYear',), ('CCNo',), ('CustomerName',), ('CustomerId',)
        ]
        
        # Setup mock data for Customer for consistency
        cls.customer_data_full = [
            {'CustomerId': 'CUST001', 'CustomerName': 'Customer A', 'CompId': 1},
            {'CustomerId': 'CUST002', 'CustomerName': 'Customer B', 'CompId': 1},
            {'CustomerId': 'CUST003', 'CustomerName': 'Customer C', 'CompId': 2},
        ]

    def setUp(self):
        # Patch connection.cursor for each test method to ensure isolation
        self.mock_cursor = MockCursor(
            fetchall_data=self.challan_data,
            description_data=self.challan_description
        )
        self.patcher_cursor = patch('django.db.connection.cursor', return_value=self.mock_cursor)
        self.mock_cursor_context = self.patcher_cursor.start()
        
        # Patch Customer.objects.filter for model-level tests
        self.patcher_customer_filter = patch('inventory.models.Customer.objects.filter')
        self.mock_customer_filter = self.patcher_customer_filter.start()
        
        # Create mock objects that behave like QuerySet results for Customer.search_customers
        mock_customer_queryset = []
        for data in self.customer_data_full:
            mock_customer = MagicMock()
            for key, value in data.items():
                setattr(mock_customer, key.lower(), value) # Convert to lowercase for attribute access
            mock_customer_queryset.append(mock_customer)
        
        self.mock_customer_filter.return_value = MagicMock(
            order_by=MagicMock(return_value=MagicMock(__getitem__=MagicMock(return_value=mock_customer_queryset))) # For [:10] slicing
        )

    def tearDown(self):
        self.patcher_cursor.stop()
        self.patcher_customer_filter.stop()

    def test_get_challan_list_all(self):
        from .models import CustomerChallan
        challans = CustomerChallan.get_challan_list(company_id=1, financial_year_id='2023-2024')
        self.assertEqual(len(challans), 3) # Based on mock data
        self.assertEqual(challans[0].cc_no, 'CC001')
        self.assertEqual(challans[0].customer_name, 'Customer A')
        
    def test_get_challan_list_filtered_customer(self):
        from .models import CustomerChallan
        # Mock cursor for a specific filtered result
        self.mock_cursor._fetchall_data = [self.challan_data[0], self.challan_data[2]]
        
        challans = CustomerChallan.get_challan_list(customer_id='CUST001', company_id=1, financial_year_id='2023-2024')
        self.assertEqual(len(challans), 2)
        self.assertEqual(challans[0].customer_id, 'CUST001')
        self.assertEqual(challans[1].customer_id, 'CUST001')

    def test_customer_search_customers(self):
        from .models import Customer
        suggestions = Customer.search_customers('customer a', company_id=1)
        self.assertEqual(suggestions, ['Customer A [CUST001]']) # Based on mock data filtering

        suggestions_b = Customer.search_customers('customer b', company_id=1)
        self.assertEqual(suggestions_b, ['Customer B [CUST002]'])
        
        suggestions_empty = Customer.search_customers('nonexistent', company_id=1)
        self.assertEqual(suggestions_empty, [])

    def test_extract_customer_id_from_formatted_string(self):
        from .models import Customer
        self.assertEqual(Customer.extract_customer_id_from_formatted_string("Customer A [CUST001]"), "CUST001")
        self.assertEqual(Customer.extract_customer_id_from_formatted_string("Another Customer [ID123]"), "ID123")
        self.assertEqual(Customer.extract_customer_id_from_formatted_string("Just a Name"), "Just a Name") # Should return original if format not matched

class CustomerChallanViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Set session data for tests
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = '2023-2024'
        session.save()
        
        # Mock the model methods to avoid actual DB calls during view tests
        self.patcher_get_challan_list = patch('inventory.models.CustomerChallan.get_challan_list')
        self.mock_get_challan_list = self.patcher_get_challan_list.start()
        self.mock_get_challan_list.return_value = [
            MagicMock(id=1, fin_year='2023-2024', cc_no='CC001', customer_name='Customer A', customer_id='CUST001'),
            MagicMock(id=2, fin_year='2023-2024', cc_no='CC002', customer_name='Customer B', customer_id='CUST002'),
        ]

        self.patcher_search_customers = patch('inventory.models.Customer.search_customers')
        self.mock_search_customers = self.patcher_search_customers.start()
        self.mock_search_customers.return_value = ['Customer X [CUSTX]']
        
        self.patcher_extract_customer_id = patch('inventory.models.Customer.extract_customer_id_from_formatted_string')
        self.mock_extract_customer_id = self.patcher_extract_customer_id.start()
        self.mock_extract_customer_id.return_value = '' # Default mock

    def tearDown(self):
        self.patcher_get_challan_list.stop()
        self.patcher_search_customers.stop()
        self.patcher_extract_customer_id.stop()

    def test_list_view_get(self):
        response = self.client.get(reverse('inventory:customerchallan_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/customerchallan/list.html')
        self.assertContains(response, 'Customer Challan - Edit')
        self.assertContains(response, 'id="customerchallan-table-container"') # Check for HTMX container

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('inventory:customerchallan_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/customerchallan/_customerchallan_table.html')
        self.assertTrue('customer_challans' in response.context)
        self.assertEqual(len(response.context['customer_challans']), 2)
        self.mock_get_challan_list.assert_called_with(customer_id='', company_id=1, financial_year_id='2023-2024')
        self.assertContains(response, 'CC001') # Check for rendered data

    def test_table_partial_view_get_with_search(self):
        self.mock_extract_customer_id.return_value = 'CUST001' # Simulate parsing
        response = self.client.get(reverse('inventory:customerchallan_table'), {'search_value': 'Customer A [CUST001]'})
        self.assertEqual(response.status_code, 200)
        self.mock_get_challan_list.assert_called_with(customer_id='CUST001', company_id=1, financial_year_id='2023-2024')

    def test_autocomplete_view_get_with_prefix(self):
        response = self.client.get(reverse('inventory:customerchallan_autocomplete'), {'search_value': 'Cust'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/customerchallan/_autocomplete_results.html')
        self.mock_search_customers.assert_called_with('Cust', 1)
        self.assertContains(response, 'Customer X [CUSTX]')

    def test_autocomplete_view_get_empty_prefix(self):
        response = self.client.get(reverse('inventory:customerchallan_autocomplete'), {'search_value': ''})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content.decode(), "") # Should return empty string
        self.mock_search_customers.assert_not_called()
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated Django templates already include the necessary HTMX attributes and Alpine.js directives to achieve dynamic interactions:

*   **HTMX for dynamic table loading:**
    *   The `customerchallan-table-container` in `list.html` uses `hx-get="{% url 'inventory:customerchallan_table' %}"` and `hx-trigger="load"` to load the table content on page load.
    *   The "Search" button uses `hx-get="{% url 'inventory:customerchallan_table' %}"` with `hx-target="#customerchallan-table-container"` and `hx-include="#TxtSearchValue"` to reload the table with filtered data when clicked.
    *   The `hx-trigger="keyup changed delay:500ms, search"` on `TxtSearchValue` combined with `hx-get="/inventory/customerchallan/autocomplete/"` provides the autocomplete functionality.
*   **Alpine.js for UI state management (Autocomplete):**
    *   An `x-data` block is used on the search section to manage `showResults` (for displaying the autocomplete dropdown) and `selectedCustomer` (for storing the selected value).
    *   `@input="showResults = true"` ensures the autocomplete results appear when typing.
    *   `@click.away="showResults = false"` hides the results when clicking outside.
    *   The `<li>` elements in `_autocomplete_results.html` use Alpine's `@click` to populate the search input and hide results.
*   **DataTables for list views:**
    *   The `_customerchallan_table.html` partial includes a JavaScript block that initializes DataTables on the `customerchallanTable` after it's loaded by HTMX. `destroy: true` is crucial to prevent re-initialization issues if the table is swapped multiple times.
*   **No full page reloads:** All search, filter, and autocomplete interactions are handled via HTMX requests, ensuring a smooth, single-page application feel without writing custom JavaScript.

---

### Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating your ASP.NET Customer Challan list and search functionality to Django. By adhering to the principles of fat models, thin views, and embracing modern frontend technologies like HTMX and Alpine.js with DataTables, you will achieve:

*   **Improved Maintainability:** Business logic is centralized in models, separating concerns.
*   **Enhanced Performance:** HTMX and DataTables provide dynamic updates without full page reloads, offering a snappier user experience.
*   **Modern Architecture:** Transition to a widely adopted, secure, and scalable framework.
*   **Reduced Manual Effort:** Focus on systematic conversion leveraging AI-assisted strategies.
*   **Testability:** Comprehensive tests ensure code quality and prevent regressions.

This setup is fully geared towards being managed and extended through conversational AI guidance, streamlining the modernization process for your organization.