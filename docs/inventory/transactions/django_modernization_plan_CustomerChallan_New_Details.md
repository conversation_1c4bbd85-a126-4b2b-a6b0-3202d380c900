## ASP.NET to Django Conversion Script: Customer <PERSON><PERSON>ails

This document outlines a comprehensive modernization plan to transition the `CustomerChallan_New_Details.aspx` ASP.NET application to a modern Django-based solution. Our approach emphasizes automation, clear separation of concerns, and the use of modern web technologies like Django 5.0+, HTMX, Alpine.js, and DataTables, adhering to the "Fat Model, Thin View" paradigm.

The goal is to deliver a robust, maintainable, and scalable application while ensuring that the migration process is transparent and manageable for business stakeholders through clear, actionable steps.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
Based on the ASP.NET code-behind, the following tables and their inferred schemas are identified. These tables will be mapped directly to Django models using `managed = False` and `db_table` to connect to the existing database.

**Inferred Database Schema:**

1.  **`tblDG_Item_Master` (Item Information)**
    *   `Id` (Primary Key, Integer)
    *   `CId` (Integer, Foreign Key to `tblDG_Category_Master`)
    *   `ItemCode` (String)
    *   `ManfDesc` (String, "Description")
    *   `UOMBasic` (String, "UOM")
    *   `StockQty` (Float, "Stock Qty")
    *   `Location` (String)
    *   `CompId` (Integer, Company ID)
    *   `FinYearId` (Integer, Financial Year ID)

2.  **`tblDG_Category_Master` (Item Categories)**
    *   `CId` (Primary Key, Integer)
    *   `Symbol` (String)
    *   `CName` (String, Category Name)
    *   `CompId` (Integer, Company ID)

3.  **`SD_Cust_WorkOrder_Master` (Customer Work Order)**
    *   `WONo` (Primary Key, String, Work Order Number) - *Inferred PK from query string usage.*
    *   `CustomerId` (Integer)
    *   `CompId` (Integer, Company ID)

4.  **`tblInv_Customer_Challan_Master` (Customer Challan Header)**
    *   `Id` (Primary Key, Integer)
    *   `SysDate` (String, Date)
    *   `SysTime` (String, Time)
    *   `SessionId` (String, User Session ID/Username)
    *   `CompId` (Integer, Company ID)
    *   `FinYearId` (Integer, Financial Year ID)
    *   `CCNo` (String, Challan Number, generated sequence)
    *   `CustomerId` (Integer, Foreign Key to Customer)
    *   `WONo` (String, Foreign Key to Work Order)

5.  **`tblInv_Customer_Challan_Details` (Customer Challan Line Items)**
    *   `Id` (Primary Key, Integer) - *Inferred auto-incrementing PK.*
    *   `MId` (Integer, Foreign Key to `tblInv_Customer_Challan_Master.Id`)
    *   `ChallanQty` (Float)
    *   `ItemId` (Integer, Foreign Key to `tblDG_Item_Master.Id`)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic in the ASP.NET code.

**Instructions:**

*   **Read (R):**
    *   **Item Listing:** The `Fillgrid` method is the primary read operation for displaying `tblDG_Item_Master` records. It supports filtering by category, item code, description, and location, as well as by "WO Items" (items related to a specific work order). This logic is complex, relying on a stored procedure (`GetAllItem`), and will be encapsulated in a Django model manager.
    *   **Dropdown Data:** Dynamic population of `DrpCategory` from `tblDG_Category_Master` and locations (inferred from `fun.drpLocat`) from `tblDG_Item_Master`.
    *   **Customer ID Retrieval:** Fetches `CustomerId` from `SD_Cust_WorkOrder_Master` based on the provided `WONo` from the query string.
*   **Create (C):**
    *   **Challan Creation:** The `BtnAdd_Click` event orchestrates the creation of a new customer challan. It first generates a unique `CCNo` (Customer Challan Number) by incrementing the last existing number for the company and financial year.
    *   **Header and Details Insertion:** It inserts a record into `tblInv_Customer_Challan_Master` and then iterates through the selected items in the grid (`GridView2`), inserting corresponding records into `tblInv_Customer_Challan_Details` for each item with a valid `ChallanQty`.
*   **Update (U) & Delete (D):**
    *   **No Direct Update/Delete:** The provided ASP.NET code primarily focuses on *creating* a new customer challan. There are no explicit update or delete operations on challan records within this specific `.aspx` page. The "Clear Challan" tab (`CustomerChallan_Clear.aspx`) suggests this functionality is handled in a separate module. For this migration, we will focus on the creation and listing of items for selection.
*   **Validation:**
    *   **Quantity Validation:** `TxtQty` in the `GridView` has a `RegularExpressionValidator` (`^\d{1,15}(\.\d{0,3})?$`) and `RequiredFieldValidator`, ensuring it's a valid numeric quantity and not empty when selected.
    *   **Dropdown Selection:** Checks if "Category" or "WO Items" is selected in `DrpType`.
    *   **Customer/Supplier Code Validation:** `fun.chkEmpCustSupplierCode` is called before challan insertion, implying a check for customer validity.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The user interface is structured with a tab container, search filters, an item selection grid, and action buttons.

*   **Tab Container (`cc1:TabContainer`):** This will be replaced by a simple HTMX-driven tabbed interface, where each tab's content is loaded dynamically via `hx-get`.
*   **Search/Filter Controls:**
    *   `DrpType` (DropDownList): Maps to a Django `ChoiceField` or `Select` widget for filtering item types (Category/WO Items). Its `AutoPostBack` suggests HTMX `hx-trigger="change"`.
    *   `DrpCategory` (DropDownList): Maps to a Django `ChoiceField` for categories, dynamically populated. Hidden/shown via Alpine.js or HTMX swap.
    *   `DrpSearchCode` (DropDownList): Maps to a `ChoiceField` for search criteria (Item Code, Description, Location). Its `AutoPostBack` suggests HTMX `hx-trigger="change"`.
    *   `DropDownList3` (DropDownList): Maps to a `ChoiceField` for locations, dynamically populated. Hidden/shown via Alpine.js or HTMX swap.
    *   `txtSearchItemCode` (TextBox): Maps to a `TextInput` for search input. Hidden/shown via Alpine.js or HTMX swap.
    *   `btnSearch` (Button): Maps to an HTMX `hx-post` or `hx-get` to trigger the item search and refresh the grid.
*   **Item Selection Grid (`asp:GridView`):** This will be migrated to a standard HTML table rendered by Django, with DataTables.js for client-side functionality.
    *   **Checkbox (`CheckBox1`):** For selecting items. Its `AutoPostBack` and `oncheckedchanged` indicate that enabling/disabling of the quantity textbox (`TxtQty`) is dynamic. This will be handled by HTMX sending a request to update the row's state (e.g., enable/disable the textbox).
    *   **Challan Quantity (`TxtQty`):** A `TextInput` for entering quantity. Validation handled by Django forms on submission. Enabled/disabled dynamically using HTMX/Alpine.js.
    *   **Item Details (`BoundField`, `TemplateField`):** Displaying `ItemCode`, `ManfDesc`, `UOMBasic`, `StockQty`, `Location`. These will be simple template variables.
*   **Action Buttons:**
    *   `BtnAdd` (Submit Button): This will be an HTMX `hx-post` for submitting the entire challan creation form, including all selected items and their quantities.
    *   `Btncancel`, `Btncancel1` (Cancel Buttons): Will redirect to the main challan listing page or close a modal if opened in one.

### Step 4: Generate Django Code

We will structure the Django application as `inventory_challan`.

#### 4.1 Models

**Task:** Create Django models based on the identified database schema.

**Instructions:**
Models are defined with `managed = False` and `db_table` to connect to the existing database tables. Business logic, especially the complex `GetAllItem` stored procedure equivalent and `CCNo` generation, is moved to custom managers or model methods.

**`inventory_challan/models.py`**

```python
from django.db import models, connection
from django.db.models import F, Value, CharField
from django.db.models.functions import Concat
from django.utils import timezone
import math

class ItemMasterManager(models.Manager):
    """
    Custom manager for ItemMaster to encapsulate complex item fetching logic
    equivalent to the ASP.NET GetAllItem stored procedure.
    """
    def get_filtered_items(self, company_id, financial_year_id,
                           challan_type="Select", category_id="Select",
                           search_code="Select", search_term="", work_order_no=None):
        """
        Fetches items based on various criteria, mimicking the GetAllItem stored procedure logic.
        """
        qs = self.get_queryset().filter(comp_id=company_id, fin_year_id__lte=financial_year_id)

        if challan_type == "Category":
            if category_id != "Select":
                qs = qs.filter(c_id=category_id)
                if search_code != "Select":
                    if search_code == "tblDG_Item_Master.ItemCode":
                        qs = qs.filter(item_code__istartswith=search_term)
                    elif search_code == "tblDG_Item_Master.ManfDesc":
                        qs = qs.filter(manf_desc__icontains=search_term)
                    elif search_code == "tblDG_Item_Master.Location" and search_term != "Select":
                        qs = qs.filter(location=search_term)
            elif search_term: # If category is 'Select' but search term exists
                qs = qs.filter(manf_desc__icontains=search_term)

        elif challan_type == "WOItems" and work_order_no:
            # Assuming there's a linking table or a way to get items from a WO.
            # This is a placeholder as the exact WO-Item relationship isn't clear from ASP.NET code.
            # For demonstration, let's assume ItemMaster has a work_order_no field or related_name.
            # The ASP.NET code implies a generic item search even for WOItems,
            # so we'll apply search filters similar to category if WOItems is selected.
            if search_code != "Select":
                if search_code == "tblDG_Item_Master.ItemCode":
                    qs = qs.filter(item_code__icontains=search_term)
                elif search_code == "tblDG_Item_Master.ManfDesc":
                    qs = qs.filter(manf_desc__icontains=search_term)
            elif search_term:
                qs = qs.filter(manf_desc__icontains=search_term)
            
            # Additional filter to ensure items are relevant to the specific work order
            # This part needs explicit database schema from SD_Cust_WorkOrder_Details or similar.
            # Placeholder: Assuming a direct filter or relation exists.
            # Example: qs = qs.filter(customer_work_order__won_no=work_order_no)
            pass # Currently, WOItems logic mirrors general search in ASP.NET code for this section.


        # Order by Item Code for consistent display
        return qs.order_by('item_code')

class ItemMaster(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    c_id = models.IntegerField(db_column='CId', null=True, blank=True) # Category ID
    item_code = models.CharField(db_column='ItemCode', max_length=100, blank=True, null=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=500, blank=True, null=True) # Manufacturer Description
    uom_basic = models.CharField(db_column='UOMBasic', max_length=50, blank=True, null=True)
    stock_qty = models.FloatField(db_column='StockQty', default=0.0)
    location = models.CharField(db_column='Location', max_length=100, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    objects = ItemMasterManager()

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return f"{self.item_code} - {self.manf_desc}"

    @property
    def formatted_stock_qty(self):
        return f"{self.stock_qty:.3f}" # Format to 3 decimal places

class CategoryMaster(models.Model):
    c_id = models.AutoField(db_column='CId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)
    c_name = models.CharField(db_column='CName', max_length=200, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'

    def __str__(self):
        return f"[{self.symbol}] - {self.c_name}"

class CustomerWorkOrder(models.Model):
    # Assuming WONo is the primary key as inferred from usage
    won_no = models.CharField(db_column='WONo', max_length=50, primary_key=True)
    customer_id = models.IntegerField(db_column='CustomerId')
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Customer Work Order'
        verbose_name_plural = 'Customer Work Orders'

    def __str__(self):
        return self.won_no

class CustomerChallanMaster(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    sys_date = models.CharField(db_column='SysDate', max_length=10) # Stored as string in ASP.NET
    sys_time = models.CharField(db_column='SysTime', max_length=10) # Stored as string in ASP.NET
    session_id = models.CharField(db_column='SessionId', max_length=100, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    cc_no = models.CharField(db_column='CCNo', max_length=10, unique=True, blank=True, null=True) # Challan Number
    customer_id = models.IntegerField(db_column='CustomerId')
    won_no = models.CharField(db_column='WONo', max_length=50) # Work Order Number

    class Meta:
        managed = False
        db_table = 'tblInv_Customer_Challan_Master'
        verbose_name = 'Customer Challan Master'
        verbose_name_plural = 'Customer Challan Masters'

    def __str__(self):
        return self.cc_no or f"Challan {self.id}"

    def save(self, *args, **kwargs):
        if not self.cc_no:
            self.cc_no = self.generate_challan_number()
        # Ensure SysDate and SysTime are set on save if not already
        if not self.sys_date:
            self.sys_date = timezone.now().strftime("%d/%m/%Y")
        if not self.sys_time:
            self.sys_time = timezone.now().strftime("%H:%M:%S")
        super().save(*args, **kwargs)

    def generate_challan_number(self):
        """
        Generates a new CCNo based on the last existing number for the company and financial year.
        Mimics ASP.NET's `fun.select("CCNo", ..., "order by Id desc")` logic.
        """
        last_challan = CustomerChallanMaster.objects.filter(
            comp_id=self.comp_id,
            fin_year_id=self.fin_year_id
        ).order_by('-id').first()

        if last_challan and last_challan.cc_no and last_challan.cc_no.isdigit():
            next_num = int(last_challan.cc_no) + 1
        else:
            next_num = 1
        return f"{next_num:04d}" # Format as 0001, 0002, etc.

    @classmethod
    def check_customer_supplier_code(cls, customer_id, type_id, company_id):
        """
        Mimics fun.chkEmpCustSupplierCode.
        Type_id=2 is for Customer.
        This function's exact logic is unknown, but typically it checks if a customer/supplier exists.
        For demonstration, we'll assume a basic check against CustomerWorkOrder.
        """
        # This is a placeholder. Real implementation needs access to the Customer entity.
        # Assuming CustomerWorkOrder implies customer existence for now.
        return CustomerWorkOrder.objects.filter(customer_id=customer_id, comp_id=company_id).exists()

class CustomerChallanDetail(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    m_id = models.IntegerField(db_column='MId') # Master ID
    challan_qty = models.FloatField(db_column='ChallanQty')
    item_id = models.IntegerField(db_column='ItemId')

    # Add related objects for easier access
    master = models.ForeignKey(CustomerChallanMaster, on_delete=models.CASCADE, db_column='MId', related_name='details', editable=False)
    item = models.ForeignKey(ItemMaster, on_delete=models.CASCADE, db_column='ItemId', related_name='challan_details', editable=False)

    class Meta:
        managed = False
        db_table = 'tblInv_Customer_Challan_Details'
        verbose_name = 'Customer Challan Detail'
        verbose_name_plural = 'Customer Challan Details'

    def __str__(self):
        return f"Detail {self.id} for Challan {self.m_id}"

    @property
    def formatted_challan_qty(self):
        return f"{self.challan_qty:.3f}" # Format to 3 decimal places

    @staticmethod
    def number_validation_qty(value):
        """
        Mimics fun.NumberValidationQty and ASP.NET RegularExpressionValidator.
        Validates if a string is a valid number with up to 3 decimal places.
        """
        if not isinstance(value, str):
            value = str(value)
        import re
        return bool(re.fullmatch(r"^\d{1,15}(\.\d{0,3})?$", value))

```

#### 4.2 Forms

**Task:** Define Django forms for user input and data validation.

**Instructions:**
A main `ItemSearchForm` handles the search criteria. A `ChallanItemForm` is created for each row in the DataTables grid to capture `ChallanQty` and item selection. A `formset` is used to manage multiple `ChallanItemForm` instances.

**`inventory_challan/forms.py`**

```python
from django import forms
from django.core.exceptions import ValidationError
from .models import ItemMaster, CategoryMaster, CustomerChallanMaster, CustomerChallanDetail

# Custom widget to disable/enable text input based on checkbox state for HTMX
class CheckboxEnableTextInput(forms.TextInput):
    template_name = 'inventory_challan/widgets/checkbox_enable_text_input.html'

    def get_context(self, name, value, attrs):
        context = super().get_context(name, value, attrs)
        context['widget']['attrs']['x-bind:disabled'] = '!isChecked' # Alpine.js
        return context

class ItemSearchForm(forms.Form):
    ITEM_TYPE_CHOICES = [
        ('Select', 'Select'),
        ('Category', 'Category'),
        ('WOItems', 'WO Items'),
    ]
    SEARCH_CODE_CHOICES = [
        ('Select', 'Select'),
        ('tblDG_Item_Master.ItemCode', 'Item Code'),
        ('tblDG_Item_Master.ManfDesc', 'Description'),
        ('tblDG_Item_Master.Location', 'Location'),
    ]

    type = forms.ChoiceField(
        choices=ITEM_TYPE_CHOICES,
        label="Type",
        required=False,
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-post': '/inventory-challan/get-search-options/', # Endpoint to dynamically update search options
            'hx-target': '#search-options-container',
            'hx-swap': 'innerHTML',
            'hx-indicator': '.htmx-indicator',
        })
    )
    category = forms.ChoiceField(
        choices=[('Select', 'Select')], # Populated dynamically
        label="Category",
        required=False,
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-post': '/inventory-challan/get-search-code-options/', # Endpoint to dynamically update search code options
            'hx-target': '#search-code-container',
            'hx-swap': 'innerHTML',
            'hx-indicator': '.htmx-indicator',
        })
    )
    search_code = forms.ChoiceField(
        choices=SEARCH_CODE_CHOICES,
        label="Search By",
        required=False,
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-post': '/inventory-challan/get-search-input-field/', # Endpoint to dynamically update search input type
            'hx-target': '#search-input-container',
            'hx-swap': 'innerHTML',
            'hx-indicator': '.htmx-indicator',
        })
    )
    search_term_text = forms.CharField(
        label="Search Term",
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter search term',
        })
    )
    search_term_location = forms.ChoiceField(
        choices=[('Select', 'Select')], # Populated dynamically
        label="Location",
        required=False,
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
        })
    )

    def __init__(self, *args, **kwargs):
        company_id = kwargs.pop('company_id', None)
        super().__init__(*args, **kwargs)

        if company_id:
            # Populate category dropdown
            categories = CategoryMaster.objects.filter(comp_id=company_id).order_by('c_name')
            self.fields['category'].choices = [('Select', 'Select')] + [(str(c.c_id), str(c)) for c in categories]

            # Populate location dropdown from distinct item locations
            locations = ItemMaster.objects.filter(comp_id=company_id).values_list('location', flat=True).distinct().order_by('location')
            self.fields['search_term_location'].choices = [('Select', 'Select')] + [(loc, loc) for loc in locations if loc]

        # Initial visibility based on POST data or defaults
        if self.is_bound:
            type_val = self.data.get('type')
            category_val = self.data.get('category')
            search_code_val = self.data.get('search_code')
        else:
            type_val = self.initial.get('type', 'Select')
            category_val = self.initial.get('category', 'Select')
            search_code_val = self.initial.get('search_code', 'Select')

        # Control initial visibility
        self.fields['category'].widget.attrs['x-show'] = 'itemType === "Category"'
        self.fields['search_code'].widget.attrs['x-show'] = 'itemType !== "Select"'
        self.fields['search_term_text'].widget.attrs['x-show'] = 'searchCode !== "Select" && searchCode !== "tblDG_Item_Master.Location"'
        self.fields['search_term_location'].widget.attrs['x-show'] = 'searchCode === "tblDG_Item_Master.Location"'

class ChallanItemForm(forms.Form):
    # This form represents a single row in the DataTables grid
    item_id = forms.IntegerField(widget=forms.HiddenInput())
    item_code = forms.CharField(required=False, widget=forms.HiddenInput())
    manf_desc = forms.CharField(required=False, widget=forms.HiddenInput())
    uom_basic = forms.CharField(required=False, widget=forms.HiddenInput())
    stock_qty = forms.FloatField(required=False, widget=forms.HiddenInput())
    location = forms.CharField(required=False, widget=forms.HiddenInput())

    # User-editable fields
    is_selected = forms.BooleanField(
        required=False,
        label="Select",
        widget=forms.CheckboxInput(attrs={
            'x-model': 'isChecked', # Alpine.js binding
            'hx-post': '', # This would be a local HTMX event, not a server post
            'hx-trigger': 'change',
            'class': 'w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500'
        })
    )
    challan_qty = forms.CharField( # Use CharField for initial input, convert to float in clean method
        required=False,
        label="Challan Qty",
        widget=CheckboxEnableTextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter quantity',
        })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set initial value for is_selected if provided in initial data
        if self.initial.get('is_selected') is None:
            self.initial['is_selected'] = False

    def clean_challan_qty(self):
        qty_str = self.cleaned_data.get('challan_qty')
        is_selected = self.cleaned_data.get('is_selected')

        if is_selected:
            if not qty_str:
                raise ValidationError("Challan Quantity is required when item is selected.")
            if not CustomerChallanDetail.number_validation_qty(qty_str):
                raise ValidationError("Invalid quantity format. Up to 3 decimal places allowed.")
            try:
                qty = float(qty_str)
                if qty == 0:
                    raise ValidationError("Challan Quantity cannot be zero.")
                return qty # Return as float if valid
            except ValueError:
                raise ValidationError("Invalid number format for Challan Quantity.")
        return None # Return None if not selected

    def clean(self):
        cleaned_data = super().clean()
        is_selected = cleaned_data.get('is_selected')
        challan_qty = cleaned_data.get('challan_qty') # This is the cleaned float if valid, or original string if not yet cleaned_challan_qty ran

        if is_selected and (challan_qty is None or challan_qty == ''):
            # This handles the case where clean_challan_qty might have returned None or left as '' due to initial validation failure
            # Or if it was required but not filled by user.
            # Add a specific error if it's selected but quantity is missing after initial cleaning.
            # (Note: clean_challan_qty handles the primary required check, this is a fallback for overall form validation)
            if 'challan_qty' not in self.errors: # Only add if not already an error
                self.add_error('challan_qty', "Challan Quantity is required when item is selected.")

        return cleaned_data

# Formset for handling multiple ChallanItemForm instances
ChallanItemFormSet = forms.formset_factory(ChallanItemForm, extra=0) # extra=0 means no empty forms initially

```

#### 4.3 Views

**Task:** Implement CRUD operations using CBVs, ensuring thin views and business logic in models.

**Instructions:**
The main view (`CustomerChallanCreateView`) will manage the entire page. HTMX will be heavily used to dynamically load parts of the page, such as the item table and dropdown options. Views should be thin (5-15 lines).

**`inventory_challan/views.py`**

```python
from django.views.generic import TemplateView, View, ListView
from django.urls import reverse_lazy
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.db import transaction
from django.forms import formset_factory

from .models import (
    ItemMaster, CategoryMaster, CustomerWorkOrder,
    CustomerChallanMaster, CustomerChallanDetail
)
from .forms import ItemSearchForm, ChallanItemForm, ChallanItemFormSet

class CustomerChallanCreateView(TemplateView):
    """
    Main view for the Customer Challan creation page.
    Handles initial load and acts as a container for HTMX partials.
    """
    template_name = 'inventory_challan/customerchallan_new_details.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Mock Session/QueryString data as per ASP.NET
        # In a real app, this would come from request.user or actual session/query parameters
        context['comp_id'] = self.request.session.get('compid', 1)  # Default for demo
        context['fin_year_id'] = self.request.session.get('finyear', 2023) # Default for demo
        context['username'] = self.request.session.get('username', 'demo_user') # Default for demo
        context['work_order_no'] = self.request.GET.get('WONo', 'WO-001') # Default for demo

        # Fetch Customer ID from work_order_no
        customer_work_order = CustomerWorkOrder.objects.filter(
            won_no=context['work_order_no'],
            comp_id=context['comp_id']
        ).first()
        context['customer_id'] = customer_work_order.customer_id if customer_work_order else None

        if not context['customer_id']:
            messages.error(self.request, "Work Order or Customer not found.")
            # Decide on redirect or error page for production
            # return redirect(reverse_lazy('some_error_page'))

        context['search_form'] = ItemSearchForm(company_id=context['comp_id'])
        return context

    def post(self, request, *args, **kwargs):
        """
        Handles the submission of the entire challan, including selected items.
        This endpoint receives data from the main "Submit" button.
        """
        comp_id = request.session.get('compid', 1)
        fin_year_id = request.session.get('finyear', 2023)
        username = request.session.get('username', 'demo_user')
        work_order_no = request.GET.get('WONo', 'WO-001')
        customer_id = CustomerWorkOrder.objects.filter(
            won_no=work_order_no, comp_id=comp_id
        ).first().customer_id if CustomerWorkOrder.objects.filter(won_no=work_order_no, comp_id=comp_id).exists() else None

        if not customer_id:
            messages.error(request, "Customer Work Order not found. Cannot create Challan.")
            return HttpResponse(status=400) # Bad Request

        # Validate customer
        if not CustomerChallanMaster.check_customer_supplier_code(customer_id, 2, comp_id):
            messages.error(request, "Invalid Customer for Challan creation.")
            return HttpResponse(status=400)

        # Process the formset for selected items
        challan_item_formset = ChallanItemFormSet(request.POST)

        if not challan_item_formset.is_valid():
            messages.error(request, "Invalid input data. Please check selected quantities.")
            # If not valid, we need to re-render the table with errors.
            # This implies the formset should be rendered inside the hx-target for the table.
            # For simplicity in this thin view, we'll return a 400.
            # A more robust solution might return the partial with errors.
            return render(request, 'inventory_challan/partials/_item_table.html', {
                'item_formset': challan_item_formset, # Re-render with errors
                'items': ItemMaster.objects.none(), # No items to display if formset is invalid
                'show_errors': True, # Flag to show errors in template
            }, status=400)

        selected_items_data = [
            form.cleaned_data for form in challan_item_formset
            if form.cleaned_data.get('is_selected') and form.cleaned_data.get('challan_qty') is not None
        ]

        if not selected_items_data:
            messages.warning(request, "No items selected or valid quantities entered.")
            return HttpResponse(status=204) # No content, but trigger refresh if needed

        try:
            with transaction.atomic():
                # Create Challan Master record
                challan_master = CustomerChallanMaster.objects.create(
                    session_id=username,
                    comp_id=comp_id,
                    fin_year_id=fin_year_id,
                    customer_id=customer_id,
                    won_no=work_order_no,
                    # CCNo, SysDate, SysTime are set in ChallanMaster's save method
                )

                # Create Challan Detail records
                for item_data in selected_items_data:
                    CustomerChallanDetail.objects.create(
                        master=challan_master,
                        challan_qty=item_data['challan_qty'],
                        item_id=item_data['item_id'],
                    )
            messages.success(request, f"Customer Challan {challan_master.cc_no} created successfully.")
            return HttpResponse(
                status=204, # No content, indicates success without navigating
                headers={'HX-Trigger': 'refreshItemList'} # Trigger a custom HTMX event to refresh the item list
            )
        except Exception as e:
            messages.error(request, f"Error creating challan: {str(e)}")
            return HttpResponse(status=500) # Internal Server Error

class ItemTablePartialView(ListView):
    """
    HTMX endpoint to load/refresh the DataTables for items.
    Handles item search/filtering.
    """
    model = ItemMaster
    template_name = 'inventory_challan/partials/_item_table.html'
    context_object_name = 'items'

    def get_queryset(self):
        comp_id = self.request.session.get('compid', 1)
        fin_year_id = self.request.session.get('finyear', 2023)
        work_order_no = self.request.GET.get('WONo', 'WO-001') # From main page URL

        # Initialize form with data from GET or POST for filtering
        form = ItemSearchForm(self.request.GET or self.request.POST, company_id=comp_id)

        if form.is_valid():
            challan_type = form.cleaned_data.get('type', 'Select')
            category_id = form.cleaned_data.get('category', 'Select')
            search_code = form.cleaned_data.get('search_code', 'Select')
            search_term_text = form.cleaned_data.get('search_term_text', '')
            search_term_location = form.cleaned_data.get('search_term_location', 'Select')

            # Determine the actual search term based on search_code
            search_term = search_term_location if search_code == "tblDG_Item_Master.Location" else search_term_text

            items = ItemMaster.objects.get_filtered_items(
                company_id=comp_id,
                financial_year_id=fin_year_id,
                challan_type=challan_type,
                category_id=category_id,
                search_code=search_code,
                search_term=search_term,
                work_order_no=work_order_no # Pass WO number for 'WOItems' type filtering
            )
            return items
        else:
            # If form is not valid (e.g., initial load, or error), return empty queryset
            messages.error(self.request, "Invalid search parameters provided.")
            return ItemMaster.objects.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass an empty formset for initial rendering (no data selected)
        # The actual forms will be populated via JavaScript/DataTables.
        # When a POST is made for a full formset (e.g., on "Submit"), that formset
        # instance is passed down to be rendered with errors if invalid.
        if self.request.method == 'POST' and self.request.headers.get('HX-Request'):
            # If this is an HTMX POST requesting a refresh (e.g., after submit)
            # The formset is likely invalid if we're re-rendering with errors
            context['item_formset'] = ChallanItemFormSet(self.request.POST)
            context['show_errors'] = True
        else:
            # For GET requests (initial load or search), prepare empty forms
            # We need to render the forms dynamically for each item to handle the
            # checkbox and quantity input per row.
            items = self.get_queryset()
            form_data = [{'item_id': item.id,
                          'item_code': item.item_code,
                          'manf_desc': item.manf_desc,
                          'uom_basic': item.uom_basic,
                          'stock_qty': item.stock_qty,
                          'location': item.location,
                          'is_selected': False, # Default to not selected
                          'challan_qty': '' # Default empty quantity
                          } for item in items]
            context['item_formset'] = ChallanItemFormSet(initial=form_data)
        return context

class DynamicSearchOptionsView(View):
    """
    HTMX endpoint to dynamically update search options based on selected item type.
    Mimics DrpType_SelectedIndexChanged behavior.
    """
    def post(self, request, *args, **kwargs):
        comp_id = request.session.get('compid', 1)
        item_type = request.POST.get('type', 'Select')

        # Create a dummy form to access form field properties for rendering
        form = ItemSearchForm(company_id=comp_id)
        
        # Render parts of the form based on type_val
        context = {
            'item_type': item_type,
            'category_field': form['category'],
            'search_code_field': form['search_code'],
            'search_term_text_field': form['search_term_text'],
            'search_term_location_field': form['search_term_location'],
            'search_form': form # Pass the full form for x-show logic
        }

        # The visibility logic is handled by Alpine.js in the template based on x-show
        # We just need to ensure the correct choices are available in the dropdowns.
        return render(request, 'inventory_challan/partials/_search_options.html', context)

class DynamicSearchCodeOptionsView(View):
    """
    HTMX endpoint to dynamically update search code options/input fields based on category.
    Mimics DrpCategory_SelectedIndexChanged behavior.
    """
    def post(self, request, *args, **kwargs):
        comp_id = request.session.get('compid', 1)
        category_id = request.POST.get('category', 'Select')
        # We need the type field as well to correctly render search options
        item_type = request.POST.get('type', 'Select')


        form = ItemSearchForm(request.POST, company_id=comp_id)
        form.is_valid() # Populate cleaned_data if valid

        context = {
            'item_type': item_type,
            'category_id': category_id,
            'search_code_field': form['search_code'],
            'search_term_text_field': form['search_term_text'],
            'search_term_location_field': form['search_term_location'],
            'search_form': form, # Pass the full form for x-show logic
        }
        return render(request, 'inventory_challan/partials/_search_code_options.html', context)

class DynamicSearchInputFieldView(View):
    """
    HTMX endpoint to dynamically update the search input field (text or dropdown)
    based on the selected search code (Item Code, Description, Location).
    Mimics DrpSearchCode_SelectedIndexChanged behavior.
    """
    def post(self, request, *args, **kwargs):
        comp_id = request.session.get('compid', 1)
        search_code = request.POST.get('search_code', 'Select')
        # We need the type field as well to correctly render search options
        item_type = request.POST.get('type', 'Select')
        category_id = request.POST.get('category', 'Select')

        form = ItemSearchForm(request.POST, company_id=comp_id)
        form.is_valid() # Populate cleaned_data if valid

        context = {
            'item_type': item_type,
            'category_id': category_id,
            'search_code': search_code,
            'search_term_text_field': form['search_term_text'],
            'search_term_location_field': form['search_term_location'],
            'search_form': form, # Pass the full form for x-show logic
        }
        return render(request, 'inventory_challan/partials/_search_input_field.html', context)

# Dummy view for the "Clear Challan" tab, mimicking the iframe
class CustomerChallanClearView(TemplateView):
    template_name = 'inventory_challan/customerchallan_clear.html'
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # This page would typically contain logic to clear/manage existing challans
        context['work_order_no'] = self.request.GET.get('WONO', 'WO-001')
        return context

```

#### 4.4 Templates

**Task:** Create templates for each view, leveraging HTMX, Alpine.js, and DataTables.

**Instructions:**
Templates will extend `core/base.html` and use partials for dynamic content. HTMX attributes (`hx-get`, `hx-post`, `hx-target`, `hx-trigger`, `hx-swap`) drive interactions. Alpine.js manages UI state like `x-show` and `x-model`. DataTables is initialized for list views.

**`inventory_challan/customerchallan_new_details.html`** (Main Page - Add/Clear Challan Tabs)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-3xl font-bold text-gray-800 mb-6">Customer Challan Management</h2>

    {% comment %} Alpine.js data for global state management like selected tab {% endcomment %}
    <div x-data="{ activeTab: 'addChallan' }">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <button @click="activeTab = 'addChallan'"
                        :class="activeTab === 'addChallan' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none">
                    Customer Challan (Add)
                </button>
                <button @click="activeTab = 'clearChallan'"
                        :class="activeTab === 'clearChallan' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none"
                        hx-get="{% url 'inventory_challan:challan_clear' %}?WONO={{ work_order_no }}"
                        hx-target="#tab-content-clear-challan"
                        hx-swap="innerHTML">
                    Clear Challan
                </button>
            </nav>
        </div>

        <div id="tab-content-add-challan" x-show="activeTab === 'addChallan'" class="mt-6">
            {% include 'inventory_challan/partials/_add_challan_tab.html' %}
        </div>

        <div id="tab-content-clear-challan" x-show="activeTab === 'clearChallan'" class="mt-6">
            <!-- Content for Clear Challan tab will be loaded via HTMX here -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2">Loading Clear Challan data...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('challanForm', () => ({
            itemType: '{{ search_form.type.value|default:"Select" }}',
            searchCode: '{{ search_form.search_code.value|default:"Select" }}',
            init() {
                this.$watch('itemType', (value) => {
                    // Reset category and search code when item type changes
                    if (value === 'Select' || value === 'WOItems') {
                        this.searchCode = 'Select';
                    }
                });
                this.$watch('searchCode', (value) => {
                    // Reset search term when search code changes
                    // (This is implicitly handled by hx-post/hx-swap in the partials)
                });
            }
        }));
    });

    // Handle global HTMX triggers for messages and list refresh
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.xhr.status >= 400 && evt.detail.xhr.status < 600) {
            // Error handling for HTMX requests (e.g., form validation errors)
            const errorMessages = evt.detail.xhr.getResponseHeader('X-Messages');
            if (errorMessages) {
                JSON.parse(errorMessages).forEach(msg => {
                    console.error('HTMX Error:', msg.message);
                    // You might want to display this in a toast or specific error area
                });
            }
        }
    });

    document.body.addEventListener('refreshItemList', function(evt) {
        // Re-trigger the item table load after a successful challan creation
        document.getElementById('item-table-container')._x_dataStack[0].$nextTick(() => {
            document.getElementById('item-table-container').setAttribute('hx-get', '{% url "inventory_challan:item_table_partial" %}?WONO={{ work_order_no }}');
            htmx.trigger(document.getElementById('item-table-container'), 'load');
        });
    });
</script>
{% endblock %}
```

**`inventory_challan/partials/_add_challan_tab.html`** (Content for the "Customer Challan (Add)" Tab)

```html
<div x-data="challanForm" class="bg-white p-6 rounded-lg shadow-lg">
    <h3 class="text-xl font-semibold mb-4">Add New Customer Challan</h3>

    <form hx-post="{% url 'inventory_challan:challan_create' %}?WONo={{ work_order_no }}"
          hx-target="#item-table-container"
          hx-swap="innerHTML"
          hx-indicator="#loading-indicator">
        {% csrf_token %}

        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6 items-end">
            <div>
                <label for="{{ search_form.type.id_for_label }}" class="block text-sm font-medium text-gray-700">Type</label>
                {{ search_form.type }}
                <input type="hidden" name="type" x-model="itemType"> {# Hidden input to ensure type is sent with other form fields #}
            </div>

            <div id="search-options-container" class="contents">
                {# Initial load of dynamic search options and input fields #}
                {% include 'inventory_challan/partials/_search_options.html' with item_type=search_form.type.value|default:"Select" category_field=search_form.category search_code_field=search_form.search_code search_term_text_field=search_form.search_term_text search_term_location_field=search_form.search_term_location search_form=search_form %}
            </div>

            <div>
                <button type="button" class="mt-6 bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
                        hx-post="{% url 'inventory_challan:item_table_partial' %}?WONo={{ work_order_no }}"
                        hx-target="#item-table-container"
                        hx-swap="innerHTML"
                        hx-include="closest form"
                        hx-indicator="#loading-indicator">
                    Search Items
                </button>
            </div>
        </div>

        <div class="relative h-96 overflow-auto border border-gray-200 rounded-md p-2 mb-6">
            <div id="loading-indicator" class="htmx-indicator absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-700">Loading items...</p>
            </div>
            <div id="item-table-container"
                 hx-trigger="load, refreshItemList from:body"
                 hx-get="{% url 'inventory_challan:item_table_partial' %}?WONo={{ work_order_no }}"
                 hx-swap="innerHTML">
                <!-- DataTables will be loaded here via HTMX -->
                <div class="text-center py-10">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2">Loading item list...</p>
                </div>
            </div>
        </div>

        <div class="flex justify-center space-x-4">
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded">
                Submit Challan
            </button>
            <a href="{% url 'inventory_challan:customer_challan_new' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-6 rounded">
                Cancel
            </a>
        </div>
    </form>
</div>

```

**`inventory_challan/partials/_search_options.html`** (Partial for dynamic dropdowns/inputs based on Type)

```html
{# This partial is loaded into #search-options-container #}
{# It dynamically sets the visibility based on itemType value handled by Alpine.js in parent template #}
<div>
    <label for="{{ category_field.id_for_label }}" class="block text-sm font-medium text-gray-700">Category</label>
    <div x-bind:data-category-field-value="itemType">
        {{ category_field }}
        <input type="hidden" name="{{ category_field.name }}" x-model="search_form.category.value" x-show="false"> {# Sync hidden input #}
    </div>
</div>

<div id="search-code-container">
    {# This is swapped when category changes #}
    {% include 'inventory_challan/partials/_search_code_options.html' with item_type=item_type category_id=category_field.value search_code_field=search_code_field search_term_text_field=search_term_text_field search_term_location_field=search_term_location_field search_form=search_form %}
</div>

```

**`inventory_challan/partials/_search_code_options.html`** (Partial for dynamic search code options)

```html
{# This partial is loaded into #search-code-container #}
{# It dynamically sets the visibility based on itemType and searchCode value handled by Alpine.js in parent template #}
<div>
    <label for="{{ search_code_field.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By</label>
    <div x-bind:data-search-code-field-value="itemType" x-bind:data-category-field-value="search_form.category.value">
        {{ search_code_field }}
        <input type="hidden" name="{{ search_code_field.name }}" x-model="searchCode" x-show="false"> {# Sync hidden input #}
    </div>
</div>

<div id="search-input-container">
    {# This is swapped when search_code changes #}
    {% include 'inventory_challan/partials/_search_input_field.html' with item_type=item_type category_id=category_id search_code=search_code_field.value search_term_text_field=search_term_text_field search_term_location_field=search_term_location_field search_form=search_form %}
</div>
```

**`inventory_challan/partials/_search_input_field.html`** (Partial for dynamic search input field)

```html
{# This partial is loaded into #search-input-container #}
{# It dynamically sets the visibility based on searchCode value handled by Alpine.js in parent template #}
<div x-bind:data-search-code-value="searchCode">
    <label for="{{ search_term_text_field.id_for_label }}" class="block text-sm font-medium text-gray-700">Search Term</label>
    {{ search_term_text_field }}
    {{ search_term_location_field }}
</div>
```

**`inventory_challan/partials/_item_table.html`** (DataTables Partial)

```html
{% load django_tables2 %}
{% comment %}
This template is swapped into `item-table-container`.
It includes the DataTables initialization script.
The formset `item_formset` contains ChallanItemForm instances for each item.
{% endcomment %}

<table id="itemTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
            <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th scope="col" class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Stock Qty</th>
            <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
            <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan Qty</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% if items %}
            {% for form in item_formset %}
                <tr x-data="{ isChecked: {{ form.is_selected.value|yesno:'true,false' }} }">
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-center text-sm text-gray-500">
                        {{ form.is_selected }}
                        {{ form.item_id }} {# Hidden fields for item data #}
                        {{ form.item_code }}
                        {{ form.manf_desc }}
                        {{ form.uom_basic }}
                        {{ form.stock_qty }}
                        {{ form.location }}
                        {# Management form prefix for formset #}
                        <input type="hidden" name="{{ form.prefix }}-id" value="{{ form.instance.pk|default:'' }}">
                    </td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ form.initial.item_code }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ form.initial.manf_desc }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-center text-sm text-gray-500">{{ form.initial.uom_basic }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-right text-sm text-gray-500">{{ form.initial.stock_qty|floatformat:"3" }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ form.initial.location }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">
                        {{ form.challan_qty }}
                        {% if show_errors and form.challan_qty.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ form.challan_qty.errors|first }}</p>
                        {% endif %}
                    </td>
                </tr>
            {% endfor %}
            {# Management form for the formset #}
            {{ item_formset.management_form }}
        {% else %}
            <tr>
                <td colspan="8" class="py-4 px-4 text-center text-sm text-gray-500">No data to display!</td>
            </tr>
        {% endif %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    // Only initialize DataTables if it hasn't been initialized already
    if (!$.fn.DataTable.isDataTable('#itemTable')) {
        $('#itemTable').DataTable({
            "pageLength": 15, // As per ASP.NET GridView PageSize
            "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
            "responsive": true,
            "order": [], // Disable initial sorting
            "paging": true,
            "info": true,
            "searching": true // Enable searching (global filter)
        });
    }
});
</script>
```

**`inventory_challan/partials/_clear_challan_tab.html`** (Content for the "Clear Challan" Tab)

```html
<div class="bg-white p-6 rounded-lg shadow-lg min-h-[450px]">
    <h3 class="text-xl font-semibold mb-4">Clear Customer Challan</h3>
    <p class="text-gray-700 mb-4">
        This section is for managing and clearing existing customer challans.
        In the original ASP.NET application, this content was loaded via an iframe from `CustomerChallan_Clear.aspx`.
    </p>
    <p class="text-gray-700">
        You are currently viewing data related to Work Order: <span class="font-bold text-indigo-600">{{ work_order_no }}</span>.
    </p>
    <div class="mt-6 flex justify-center">
        <a href="{% url 'inventory_challan:customer_challan_new' %}" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-6 rounded">
            Back to Challan List
        </a>
    </div>
</div>
```

**`inventory_challan/widgets/checkbox_enable_text_input.html`** (Custom Widget Template)

```html
<input type="text" name="{{ widget.name }}" value="{{ widget.value|default_if_none:'' }}"{% include "django/forms/widgets/attrs.html" %} x-bind:disabled="!isChecked">
```

#### 4.5 URLs

**Task:** Define URL patterns for the views, including HTMX partials.

**Instructions:**
A `urls.py` file within the `inventory_challan` app handles all routes.

**`inventory_challan/urls.py`**

```python
from django.urls import path
from .views import (
    CustomerChallanCreateView, ItemTablePartialView,
    DynamicSearchOptionsView, DynamicSearchCodeOptionsView, DynamicSearchInputFieldView,
    CustomerChallanClearView
)

app_name = 'inventory_challan'

urlpatterns = [
    # Main page for adding/viewing challans
    path('customer-challan-details/', CustomerChallanCreateView.as_view(), name='challan_create'),
    # HTMX endpoint for the item table (search and display)
    path('customer-challan-details/item-table/', ItemTablePartialView.as_view(), name='item_table_partial'),

    # HTMX endpoints for dynamic dropdowns and input fields
    path('customer-challan-details/get-search-options/', DynamicSearchOptionsView.as_view(), name='get_search_options'),
    path('customer-challan-details/get-search-code-options/', DynamicSearchCodeOptionsView.as_view(), name='get_search_code_options'),
    path('customer-challan-details/get-search-input-field/', DynamicSearchInputFieldView.as_view(), name='get_search_input_field'),

    # URL for the "Clear Challan" tab (dummy/placeholder for iframe content)
    path('customer-challan-clear/', CustomerChallanClearView.as_view(), name='challan_clear'),

    # Placeholder for redirect to main challan listing page (CustomerChallan_New.aspx)
    path('customer-challan-new/', TemplateView.as_view(template_name="inventory_challan/customer_challan_new.html"), name='customer_challan_new'),
]

```

**`core/base.html`** (Assumed to exist, includes CDNs for jQuery, DataTables, HTMX, Alpine.js, Tailwind CSS)

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}ERP System{% endblock %}</title>
    <!-- Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <!-- jQuery (for DataTables) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.css">
    <!-- DataTables JS -->
    <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.js"></script>
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10" integrity="sha384-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