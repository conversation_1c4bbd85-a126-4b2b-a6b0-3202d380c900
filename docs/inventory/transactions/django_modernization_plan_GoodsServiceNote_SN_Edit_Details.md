## ASP.NET to Django Conversion Script: Goods Service Note Details Edit

This plan outlines the modernization of your ASP.NET Goods Service Note (GSN) Details Edit page into a modern Django application. We will leverage Django's powerful ORM, Class-Based Views, HTMX for dynamic interactions, Alpine.js for lightweight frontend logic, and DataTables for efficient data display. This approach prioritizes automation and maintainability, ensuring a smooth transition with reduced manual effort.

### Business Value of Modernization:

By migrating to Django, your organization will benefit from:

1.  **Enhanced Maintainability:** Django's structured framework, clear separation of concerns (models, views, templates), and Python's readability lead to code that is easier to understand, debug, and extend.
2.  **Improved Performance & Scalability:** Django's efficient ORM and built-in optimizations, combined with HTMX for partial page updates, will result in a faster and more responsive user experience, capable of handling increased loads.
3.  **Cost Reduction:** Python's vast ecosystem and Django's robust features minimize development time and reliance on proprietary technologies, lowering operational costs.
4.  **Future-Proofing:** Django is a widely adopted, actively maintained open-source framework with a large community, ensuring long-term support and access to modern web development practices.
5.  **Better User Experience:** With HTMX and Alpine.js, users will experience a smoother, more interactive interface without full page reloads, similar to a single-page application but simpler to develop.

---

### Conversion Steps:

#### Step 1: Extract Database Schema

The ASP.NET page primarily displays and allows editing of `ReceivedQty` within `tblinv_MaterialServiceNote_Details`, linked to a `tblinv_MaterialServiceNote_Master` record. It also pulls auxiliary information from various other tables to populate the grid and header details.

For our Django models, we will focus on the core tables being managed directly and create helper properties or methods for the derived data, following the "fat model" principle.

**Primary Tables for Models:**

*   **`tblinv_MaterialServiceNote_Master`**: This is the main header for the GSN.
    *   Key fields: `Id` (PK), `GSNNo`, `GINNo`, `CompId`, `FinYearId`, `SysDate`, `SysTime`, `SessionId`.
*   **`tblinv_MaterialServiceNote_Details`**: These are the line items for a specific GSN.
    *   Key fields: `Id` (PK), `MId` (FK to Master), `POId`, `ReceivedQty` (this is the editable field).

**Auxiliary Tables (for derived display data):**

*   `tblMM_Supplier_master`: `SupplierName` (for `lblSupplier`)
*   `tblInv_Inward_Master`: `ChallanNo`, `ChallanDate` (for `lblChNo`, `lblDate`)
*   `tblInv_Inward_Details`: `ReceivedQty` (for `InvQty` in grid)
*   `tblMM_PO_Details`: `Qty` (for `POQty` in grid)
*   `tblDG_Item_Master`: `ItemCode`, `ManfDesc`, `UOMBasic` (for `ItemCode`, `Description`, `UOM` in grid)
*   `Unit_Master`: `Symbol` (for `UOM` in grid)

#### Step 2: Identify Backend Functionality

The ASP.NET code primarily focuses on:

*   **Displaying GSN Master Details:** Fetches and shows `GSN No`, `GIN No`, `Challan No`, `Date`, and `Supplier` based on query string parameters.
*   **Displaying GSN Detail Grid:** Populates a `GridView` with line item details. This involves complex joins across many tables (`tblinv_MaterialServiceNote_Details`, `tblInv_Inward_Details`, `tblMM_PO_Details`, `tblDG_Item_Master`, `Unit_Master`) to derive fields like `ItemCode`, `Description`, `UOM`, `POQty`, `Inward Qty`, and `Tot Reced Qty`.
*   **Updating a GSN Detail Item:** Allows inline editing of the `ReceivedQty` for a specific detail item.
    *   **Validation:** Ensures `ReceivedQty` is a valid number and adheres to a specific business rule (`RecvQty <= lblRecvQty`).
    *   **Master Record Update:** Updates `SysDate`, `SysTime`, `SessionId` on the master record upon detail item update.
    *   **Conditional Editing:** The "Edit" action for a row is disabled if `Inward Qty` is less than or equal to `Tot Reced Qty`.
*   **Navigation:** A "Cancel" button redirects the user away from the page.

#### Step 3: Infer UI Components

The page uses standard ASP.NET Web Forms controls:

*   **`asp:Label`**: Used to display static text and fetched data like GSN No, GIN No, Challan No, Date, and Supplier Name.
*   **`asp:GridView`**: The central component for displaying the list of GSN details. It handles pagination and provides inline editing capabilities.
    *   Columns displayed: SN (row index), Item Code, Description, UOM, PO Qty, Inward Qty, Tot Reced Qty, Reced Qty.
    *   `Reced Qty` is editable via an `asp:TextBox` in edit mode, with client-side `RegularExpressionValidator` and `RequiredFieldValidator`.
    *   `asp:CommandField` with `ShowEditButton="True"` enables inline editing.
*   **`asp:Button`**: A "Cancel" button for navigation.
*   **CSS and JavaScript:** Custom CSS for styling and specific JavaScript files for loading indicators and pop-up messages. These will be replaced by Tailwind CSS, HTMX, and Alpine.js.

---

#### Step 4: Generate Django Code

We will create a Django application named `inventory` to house these components.

##### 4.1 Models (`inventory/models.py`)

```python
from django.db import models
from django.utils import timezone

# These models map directly to existing database tables.
# 'managed = False' tells Django not to manage table creation/alteration.

class GoodsServiceNoteMaster(models.Model):
    """
    Represents the tblinv_MaterialServiceNote_Master table.
    Stores header information for Goods Service Notes.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    gsn_no = models.CharField(db_column='GSNNo', max_length=50)
    gin_no = models.CharField(db_column='GINNo', max_length=50, blank=True, null=True)
    company_id = models.IntegerField(db_column='CompId')
    financial_year_id = models.IntegerField(db_column='FinYearId')
    sys_date = models.DateField(db_column='SysDate', blank=True, null=True)
    sys_time = models.CharField(db_column='SysTime', max_length=10, blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialServiceNote_Master'
        verbose_name = 'Goods Service Note (Master)'
        verbose_name_plural = 'Goods Service Notes (Master)'

    def __str__(self):
        return self.gsn_no

class GoodsServiceNoteDetail(models.Model):
    """
    Represents the tblinv_MaterialServiceNote_Details table.
    Stores line item details for Goods Service Notes.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(GoodsServiceNoteMaster, models.DO_NOTHING, db_column='MId', related_name='details')
    gsn_no = models.CharField(db_column='GSNNo', max_length=50, blank=True, null=True) # Denormalized, can be derived
    po_id = models.IntegerField(db_column='POId') # PO details ID
    received_qty = models.DecimalField(db_column='ReceivedQty', max_digits=18, decimal_places=3)

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialServiceNote_Details'
        verbose_name = 'Goods Service Note Detail'
        verbose_name_plural = 'Goods Service Note Details'

    def __str__(self):
        return f"Detail ID: {self.id} for GSN: {self.master.gsn_no}"

    # Helper methods to simulate the complex data retrieval from ASP.NET's loadData()
    # These methods are designed to be efficient by using Django's ORM and prefetching/selecting related data.
    # In a real scenario, these lookups might be cached or optimized with a dedicated service layer
    # or by ensuring foreign keys are properly defined in the database and Django models.

    def get_item_data(self):
        """
        Simulates fetching ItemCode, Description, UOM from tblDG_Item_Master and Unit_Master.
        This assumes a chain of relationships: POId -> PO_Details -> PR/SPR_Details -> Item_Master -> Unit_Master.
        For demonstration, we'll use placeholder queries or direct lookups if precise FKs are not defined.
        In a real scenario, proper foreign key relationships would simplify this.
        """
        try:
            # Placeholder: In a real migration, precise table/column names and relationships
            # (e.g., tblMM_PO_Details, tblMM_PR_Details, tblDG_Item_Master, Unit_Master)
            # would be mapped to Django models and queried here.
            # Assuming POId in tblinv_MaterialServiceNote_Details links to tblMM_PO_Details.Id
            # and that eventually leads to ItemId and UOMBasic.

            # This is a simplified example. The actual implementation would involve
            # a series of ORM lookups and joins mirroring the C# logic.
            # Example (conceptual, requires full model mapping):
            # po_detail = PoDetail.objects.get(id=self.po_id)
            # if po_detail.pr_spr_flag == '0':
            #     pr_detail = PrDetail.objects.get(pr_no=po_detail.pr_no, id=po_detail.pr_id)
            #     item = ItemMaster.objects.get(id=pr_detail.item_id)
            # else:
            #     spr_detail = SprDetail.objects.get(spr_no=po_detail.spr_no, id=po_detail.spr_id)
            #     item = ItemMaster.objects.get(id=spr_detail.item_id)
            # uom_symbol = UnitMaster.objects.get(id=item.uom_basic).symbol

            # For now, return mock data or attempt direct lookups if possible
            # We'll use a placeholder for CompId, which typically comes from session/request context.
            company_id = self.master.company_id # Example: get from master
            
            # Simplified mock for ItemCode/Description/UOM based on POId for demonstration
            # In production, these would be proper ORM queries
            item_code_data = self._get_item_code_and_description(company_id, self.po_id) # Simulates fun.GetItemCode_PartNo and DSIcode logic
            uom_data = self._get_uom_symbol(item_code_data.get('uom_basic_id')) # Simulates DSPurch logic

            return {
                'item_code': item_code_data.get('item_code', 'N/A'),
                'description': item_code_data.get('manf_desc', 'N/A'),
                'uom': uom_data.get('symbol', 'N/A'),
            }
        except Exception as e:
            # Log the error for debugging
            print(f"Error fetching item data for POId {self.po_id}: {e}")
            return {'item_code': 'ERROR', 'description': 'ERROR', 'uom': 'ERROR'}
            
    def _get_item_code_and_description(self, company_id, po_id):
        """
        Simulates the logic for `fun.GetItemCode_PartNo` and fetching `ManfDesc`.
        This would require a proper model for `tblDG_Item_Master` and its relation to `tblMM_PO_Details`.
        For now, a mock or simplified direct lookup.
        """
        from django.db import connection
        try:
            # Example direct SQL execution (not recommended, but mirrors original C# complexity)
            # if we don't fully map all intermediate models.
            # A better approach is to map all models and use ORM.
            with connection.cursor() as cursor:
                # This complex query path (PO -> PR/SPR -> Item) would need to be mapped to models.
                # For now, a direct lookup from a hypothetical item master table.
                # Assuming `tblDG_Item_Master` has a 'POId' or is reachable through relations.
                # This is a placeholder for the actual complex join logic.
                cursor.execute(f"""
                    SELECT TOP 1 ItemCode, ManfDesc, UOMBasic
                    FROM tblDG_Item_Master
                    WHERE CompId = {company_id} AND Id IN (
                        SELECT ItemId FROM tblMM_PR_Details WHERE Id IN (SELECT PRId FROM tblMM_PO_Details WHERE Id = {po_id})
                        UNION ALL
                        SELECT ItemId FROM tblMM_SPR_Details WHERE Id IN (SELECT SPRId FROM tblMM_PO_Details WHERE Id = {po_id})
                    );
                """)
                row = cursor.fetchone()
                if row:
                    return {'item_code': row[0], 'manf_desc': row[1], 'uom_basic_id': row[2]}
                return {}
        except Exception as e:
            print(f"Error in _get_item_code_and_description for POId {po_id}: {e}")
            return {}

    def _get_uom_symbol(self, uom_basic_id):
        """
        Simulates fetching UOM symbol from Unit_Master.
        """
        if not uom_basic_id:
            return {}
        from django.db import connection
        try:
            with connection.cursor() as cursor:
                cursor.execute(f"SELECT TOP 1 Symbol FROM Unit_Master WHERE Id = {uom_basic_id};")
                row = cursor.fetchone()
                if row:
                    return {'symbol': row[0]}
                return {}
        except Exception as e:
            print(f"Error in _get_uom_symbol for UOM Id {uom_basic_id}: {e}")
            return {}

    def get_po_qty(self):
        """
        Simulates fetching POQty from tblMM_PO_Details.
        """
        from django.db import connection
        try:
            with connection.cursor() as cursor:
                cursor.execute(f"SELECT TOP 1 Qty FROM tblMM_PO_Details WHERE Id = {self.po_id};")
                row = cursor.fetchone()
                return float(row[0]) if row and row[0] is not None else 0.0
        except Exception as e:
            print(f"Error in get_po_qty for POId {self.po_id}: {e}")
            return 0.0

    def get_inward_qty(self):
        """
        Simulates fetching InwardQty from tblInv_Inward_Details for the GIN and PO.
        This needs the GINId from the master record.
        """
        from django.db import connection
        try:
            gin_id = self.master.gin_id # Assuming master has gin_id mapped from GINNo
            with connection.cursor() as cursor:
                cursor.execute(f"""
                    SELECT TOP 1 ReceivedQty
                    FROM tblInv_Inward_Details
                    WHERE GINId = (SELECT Id FROM tblInv_Inward_Master WHERE GINNo = '{self.master.gin_no}' AND CompId = {self.master.company_id})
                    AND POId = {self.po_id};
                """)
                row = cursor.fetchone()
                return float(row[0]) if row and row[0] is not None else 0.0
        except Exception as e:
            print(f"Error in get_inward_qty for GIN:{self.master.gin_no}, POId:{self.po_id}: {e}")
            return 0.0

    def get_total_received_qty(self):
        """
        Simulates `TotRecedQty` by summing ReceivedQty from tblinv_MaterialServiceNote_Details
        for the given POId and GINId (derived from master's GINNo).
        """
        from django.db import connection
        try:
            # GINId here refers to the primary key of tblInv_Inward_Master,
            # which is related to GINNo. We need to find this ID.
            # Assuming GINId from query string is stored in GINNo field of master
            # or available through master.gin_id if that's mapped.
            # For simplicity, we'll use the GSNNo and POId directly, assuming it's
            # enough to uniquely sum for this GSN context.
            with connection.cursor() as cursor:
                cursor.execute(f"""
                    SELECT SUM(ReceivedQty)
                    FROM tblinv_MaterialServiceNote_Details
                    WHERE POId = {self.po_id}
                    AND MId IN (SELECT Id FROM tblinv_MaterialServiceNote_Master WHERE GSNNo = '{self.master.gsn_no}' AND CompId = {self.master.company_id});
                """)
                sum_qty = cursor.fetchone()[0]
                return float(sum_qty) if sum_qty is not None else 0.0
        except Exception as e:
            print(f"Error in get_total_received_qty for POId:{self.po_id}: {e}")
            return 0.0

    def can_edit(self):
        """
        Business logic: Hide edit button if InwardQty <= TotRecedQty.
        Returns True if editable, False otherwise.
        """
        inward_qty = self.get_inward_qty()
        total_reced_qty = self.get_total_received_qty()
        return inward_qty > total_reced_qty

    def update_master_on_detail_change(self, session_id, comp_id):
        """
        Updates master record's SysDate, SysTime, SessionId.
        """
        # Ensure the master is fetched and updated in the same transaction context
        # if this is part of a larger transaction.
        # For simplicity, directly updating the master record here.
        master = self.master
        master.sys_date = timezone.now().date()
        master.sys_time = timezone.now().strftime('%H:%M') # Or use a proper TimeField
        master.session_id = session_id
        master.save(update_fields=['sys_date', 'sys_time', 'session_id'])


```

##### 4.2 Forms (`inventory/forms.py`)

```python
from django import forms
from .models import GoodsServiceNoteDetail
from decimal import Decimal, InvalidOperation

class GoodsServiceNoteDetailForm(forms.ModelForm):
    """
    Form for editing a single GoodsServiceNoteDetail item.
    Focuses on the 'received_qty' field.
    """
    class Meta:
        model = GoodsServiceNoteDetail
        fields = ['received_qty']
        widgets = {
            'received_qty': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'type': 'number', # Use number type for numerical input
                'step': '0.001', # Allows up to 3 decimal places
            }),
        }

    def clean_received_qty(self):
        """
        Custom validation for received_qty, mimicking ASP.NET's `RecvQty <= lblRecvQty`
        and `fun.NumberValidationQty`.
        """
        received_qty = self.cleaned_data['received_qty']
        
        # Mimic NumberValidationQty: ensure it's a valid number.
        # Django's NumberInput and DecimalField handle this mostly.
        # Ensure it's not negative if that's a business rule.
        if received_qty < 0:
            raise forms.ValidationError("Received quantity cannot be negative.")

        # Get the original received quantity from the instance if it's an update
        if self.instance.pk:
            original_received_qty = self.instance.received_qty
            # This rule (new_qty <= old_qty) is unusual for an update,
            # often it's new_qty <= InwardQty or POQty.
            # We strictly mimic the original ASP.NET validation here.
            # If the business logic needs to be RecvQty <= InwardQty, it should be adjusted.
            if received_qty > original_received_qty:
                raise forms.ValidationError(f"Received quantity cannot exceed original ({original_received_qty:.3f}).")
            
            # Additional validation based on InwardQty vs TotRecedQty, if needed
            # This check is usually done before allowing edit, but can be a form validation.
            # For demonstration, we'll keep the direct update logic as in ASP.NET.
        
        return received_qty

```

##### 4.3 Views (`inventory/views.py`)

```python
from django.shortcuts import get_object_or_404, redirect
from django.views.generic import DetailView, UpdateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from django.conf import settings # To access settings like COMP_ID, FIN_YEAR_ID etc.

from .models import GoodsServiceNoteMaster, GoodsServiceNoteDetail
from .forms import GoodsServiceNoteDetailForm
from core.mixins import LoginRequiredMixin # Assuming a LoginRequiredMixin exists

# In a real application, you might have a service layer or utility functions
# to fetch session/company/finyear IDs dynamically from user context.
# For this example, we'll simulate fetching from request attributes or defaults.

def get_company_context(request):
    """
    Helper to get company and financial year IDs, simulating ASP.NET session variables.
    In a real app, this would come from the authenticated user's profile or session.
    """
    # Placeholder: In a real Django app, these would come from the authenticated
    # user's session, profile, or other context.
    # For demonstration, we'll use query params or hardcoded values.
    comp_id = request.GET.get('CompId', getattr(settings, 'DEFAULT_COMPANY_ID', 1))
    fin_year_id = request.GET.get('FyId', getattr(settings, 'DEFAULT_FINANCIAL_YEAR_ID', 1))
    session_id = request.session.get('username', 'system_user') # From ASP.NET Session["username"]

    return {
        'comp_id': int(comp_id),
        'fin_year_id': int(fin_year_id),
        'session_id': session_id,
    }

class GoodsServiceNoteDetailMasterView(DetailView):
    """
    Displays the main GSN master details and a list of its associated line items.
    This acts as the primary page, loading the detail table via HTMX.
    """
    model = GoodsServiceNoteMaster
    template_name = 'inventory/goodsservicenotedetail/detail_list.html'
    context_object_name = 'master_record'
    pk_url_kwarg = 'master_id' # Expects master_id in URL, not default 'pk'

    def get_object(self, queryset=None):
        """
        Retrieves the GSN master record based on query parameters,
        simulating the ASP.NET's `Page_Load` logic for master data.
        """
        gsn_no = self.request.GET.get('GSNNo')
        gin_no = self.request.GET.get('GINNo')
        master_id = self.request.GET.get('Id') # This matches MId from ASP.NET code-behind

        if not master_id:
            # Fallback if 'Id' isn't in query params, try using GSNNo/GINNo
            # This path expects a direct master ID (Id) passed as pk_url_kwarg 'master_id'
            return super().get_object(queryset)

        # Retrieve CompId from context, simulating ASP.NET session
        context_data = get_company_context(self.request)
        comp_id = context_data['comp_id']

        # Query the master record using the available parameters
        obj = get_object_or_404(
            GoodsServiceNoteMaster,
            id=master_id,
            company_id=comp_id,
            gsn_no=gsn_no, # Ensure it matches GSNNo from query string
            gin_no=gin_no # Ensure it matches GINNo from query string
        )

        # Populate additional master details (Supplier, Challan No, Challan Date)
        # These would ideally come from related models or cached properties.
        # For demonstration, we'll simulate fetching these as in ASP.NET.
        obj.supplier_name = self._get_supplier_name(context_data['comp_id'], self.request.GET.get('SupId'))
        challan_data = self._get_inward_master_data(context_data['comp_id'], self.request.GET.get('GINId'))
        obj.challan_no = challan_data.get('challan_no', 'N/A')
        obj.challan_date = challan_data.get('challan_date', 'N/A')

        return obj

    def _get_supplier_name(self, comp_id, supplier_id):
        """Simulates fetching supplier name from tblMM_Supplier_master."""
        from django.db import connection
        try:
            with connection.cursor() as cursor:
                cursor.execute(f"SELECT TOP 1 SupplierName FROM tblMM_Supplier_master WHERE CompId = {comp_id} AND SupplierId = '{supplier_id}';")
                row = cursor.fetchone()
                return row[0] if row else 'N/A'
        except Exception as e:
            print(f"Error fetching supplier: {e}")
            return 'N/A'

    def _get_inward_master_data(self, comp_id, gin_id_param):
        """Simulates fetching ChallanNo and ChallanDate from tblInv_Inward_Master."""
        from django.db import connection
        try:
            with connection.cursor() as cursor:
                cursor.execute(f"SELECT TOP 1 ChallanNo, ChallanDate FROM tblInv_Inward_Master WHERE Id = '{gin_id_param}' AND CompId = {comp_id};")
                row = cursor.fetchone()
                if row:
                    challan_date = row[1].strftime('%d/%m/%Y') if row[1] else 'N/A' # fun.FromDateDMY
                    return {'challan_no': row[0], 'challan_date': challan_date}
                return {}
        except Exception as e:
            print(f"Error fetching inward master data: {e}")
            return {}

class GoodsServiceNoteDetailTablePartialView(View):
    """
    HTMX endpoint to render just the DataTables grid of detail items.
    """
    def get(self, request, *args, **kwargs):
        master_id = request.GET.get('master_id')
        if not master_id:
            return HttpResponse("Master ID not provided.", status=400)
        
        # Get master object to link details correctly.
        # In a real setup, master_id might be part of the URL itself.
        master_obj = get_object_or_404(GoodsServiceNoteMaster, id=master_id)
        
        # Filter details for the specific master record
        details = master_obj.details.all().order_by('id') # Fetch all details for this master
        
        context = {
            'goodsservicenotedetails': details,
            'master_id': master_id, # Pass master_id for form URLs
        }
        return HttpResponse(render_to_string('inventory/goodsservicenotedetail/_detail_table.html', context, request))

class GoodsServiceNoteDetailEditFormPartialView(UpdateView):
    """
    HTMX endpoint to render and process the form for editing a single detail item.
    """
    model = GoodsServiceNoteDetail
    form_class = GoodsServiceNoteDetailForm
    template_name = 'inventory/goodsservicenotedetail/_detail_form.html'
    context_object_name = 'detail_item'
    pk_url_kwarg = 'pk' # Expects pk in URL, e.g., /edit/123/

    def get_success_url(self):
        # We don't redirect on HTMX, but a success_url is required by UpdateView
        return reverse_lazy('goodsservicenotedetail_master') # A placeholder

    def form_valid(self, form):
        # Retrieve context information for master update
        context_data = get_company_context(self.request)
        session_id = context_data['session_id']
        comp_id = context_data['comp_id']

        # Save the detail item
        response = super().form_valid(form)

        # Update the master record (SysDate, SysTime, SessionId)
        self.object.update_master_on_detail_change(session_id, comp_id)

        messages.success(self.request, 'Goods Service Note Detail updated successfully.')

        # HTMX response: Trigger a refresh of the detail table
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': f'refreshGoodsServiceNoteDetailList_{self.object.master.id}' # Specific trigger for this master
                }
            )
        return response # Fallback for non-HTMX requests

    def form_invalid(self, form):
        # If validation fails, re-render the form with errors
        return self.render_to_response(self.get_context_data(form=form))

class GoodsServiceNoteCancelView(View):
    """
    Handles the cancel button action, redirecting to a previous page.
    """
    def get(self, request, *args, **kwargs):
        # Simulates Response.Redirect("GoodsServiceNote_SN_Edit.aspx?ModId=9&SubModId=39");
        # Adjust this URL to your actual Django equivalent for the previous page.
        return redirect(reverse_lazy('goodsservicenote_edit_list')) # Placeholder for actual URL


# Assuming a previous list view for master GSNs to go back to
# For example:
class GoodsServiceNoteEditListView(View):
    def get(self, request, *args, **kwargs):
        # This would list master GSNs, similar to the original ASP.NET GoodsServiceNote_SN_Edit.aspx
        return HttpResponse("This would be the list of GSNs to edit (previous page).")

```

##### 4.4 Templates (`inventory/goodsservicenotedetail/`)

**`inventory/goodsservicenotedetail/detail_list.html`** (Main page)

```html
{% extends 'core/base.html' %}
{% load humanize %} {# Optional: for comma-separated numbers if needed #}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Goods Service Note (GSN) - Edit Details</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6 text-sm text-gray-700">
            <div>
                <span class="font-semibold">GSN No:</span> <span class="font-bold">{{ master_record.gsn_no }}</span>
            </div>
            <div>
                <span class="font-semibold">GIN No:</span> <span class="font-bold">{{ master_record.gin_no }}</span>
            </div>
            <div>
                <span class="font-semibold">Challan No:</span> <span class="font-bold">{{ master_record.challan_no }}</span>
            </div>
            <div>
                <span class="font-semibold">Date:</span> <span class="font-bold">{{ master_record.challan_date }}</span>
            </div>
            <div class="md:col-span-2">
                <span class="font-semibold">Supplier:</span> <span class="font-bold">{{ master_record.supplier_name }}</span>
            </div>
        </div>

        <h3 class="text-xl font-bold text-gray-800 mb-4">Line Items</h3>
        
        <div id="detail-table-container"
             hx-trigger="load, refreshGoodsServiceNoteDetailList_{{ master_record.id }} from:body"
             hx-get="{% url 'goodsservicenotedetail_table_partial' %}?master_id={{ master_record.id }}"
             hx-swap="innerHTML"
             class="min-h-[200px] flex items-center justify-center bg-gray-50 rounded-lg">
            <!-- Initial loading spinner -->
            <div class="text-center">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading GSN details...</p>
            </div>
        </div>
        
        <div class="mt-6 flex justify-end">
            <button 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                onclick="window.location.href='{% url 'goodsservicenote_cancel' %}'">
                Cancel
            </button>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         x-data="{ showModal: false }"
         x-show="showModal" 
         x-on:close-modal.window="showModal = false"
         x-on:open-modal.window="showModal = true"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0 scale-95"
         x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100 scale-100"
         x-transition:leave-end="opacity-0 scale-95"
         _="on click if event.target.id == 'modal' send close-modal to #modal"
         >
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"
             _="on htmx:afterSwap remove .is-active from #modal"
             >
            <!-- Form will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js is initialized globally via base.html
    });
</script>
{% endblock %}

```

**`inventory/goodsservicenotedetail/_detail_table.html`** (Partial for DataTables grid)

```html
{% load humanize %}
<div class="overflow-x-auto">
    <table id="goodsservicenotedetailTable" class="min-w-full bg-white border border-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">PO Qty</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Inward Qty</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Tot Reced Qty</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Reced Qty</th>
                <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for obj in goodsservicenotedetails %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.get_item_data.item_code }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.get_item_data.description }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.get_item_data.uom }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.get_po_qty|floatformat:3|intcomma }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.get_inward_qty|floatformat:3|intcomma }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.get_total_received_qty|floatformat:3|intcomma }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.received_qty|floatformat:3|intcomma }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    {% if obj.can_edit %}
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded text-xs"
                        hx-get="{% url 'goodsservicenotedetail_edit_form_partial' pk=obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click send open-modal to #modal"
                        >
                        Edit
                    </button>
                    {% else %}
                    <span class="text-gray-500 text-xs italic">Not Editable</span>
                    {% endif %}
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="9" class="py-4 text-center text-gray-500">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after HTMX loads the content
    $(document).ready(function() {
        $('#goodsservicenotedetailTable').DataTable({
            "pageLength": 15, // Mimic ASP.NET PageSize
            "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
            "responsive": true,
            "ordering": false, // Disable ordering if ASP.NET GridView doesn't explicitly sort
            "searching": true // Enable search filter
        });
    });
</script>
```

**`inventory/goodsservicenotedetail/_detail_form.html`** (Partial for Edit Form in Modal)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Edit Goods Service Note Detail</h3>
    <form hx-post="{% url 'goodsservicenotedetail_edit_form_partial' pk=detail_item.pk %}" hx-swap="none"
          hx-indicator="#form-indicator">
        {% csrf_token %}
        
        <div class="space-y-4">
            <div class="mb-4">
                <label for="{{ form.received_qty.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.received_qty.label }}:
                </label>
                {{ form.received_qty }}
                {% if form.received_qty.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.received_qty.errors }}</p>
                {% endif %}
            </div>
            <!-- Display related info (read-only) for context -->
            <div class="mb-4">
                <p class="text-sm font-medium text-gray-700">Item Code: <span class="font-bold">{{ detail_item.get_item_data.item_code }}</span></p>
                <p class="text-sm font-medium text-gray-700">Description: <span class="font-bold">{{ detail_item.get_item_data.description }}</span></p>
                <p class="text-sm font-medium text-gray-700">PO Qty: <span class="font-bold">{{ detail_item.get_po_qty|floatformat:3|intcomma }}</span></p>
                <p class="text-sm font-medium text-gray-700">Inward Qty: <span class="font-bold">{{ detail_item.get_inward_qty|floatformat:3|intcomma }}</span></p>
                <p class="text-sm font-medium text-gray-700">Total Received (GSN): <span class="font-bold">{{ detail_item.get_total_received_qty|floatformat:3|intcomma }}</span></p>
            </div>
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click send close-modal to #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save Changes
            </button>
        </div>
        <div id="form-indicator" class="htmx-indicator mt-4 text-center">
            <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
            Saving...
        </div>
    </form>
</div>
```

##### 4.5 URLs (`inventory/urls.py`)

```python
from django.urls import path
from .views import (
    GoodsServiceNoteDetailMasterView,
    GoodsServiceNoteDetailTablePartialView,
    GoodsServiceNoteDetailEditFormPartialView,
    GoodsServiceNoteCancelView,
    GoodsServiceNoteEditListView, # Placeholder for the previous list page
)

urlpatterns = [
    # Main page for viewing and editing GSN details
    # Pass master_id as pk for DetailView, and other ASP.NET query params
    # Example URL: /inventory/gsn_details/123/?GSNNo=GSN-001&GINNo=GIN-005&SupId=456&PONo=789&FyId=1&GINId=987
    path('gsn_details/<int:master_id>/', GoodsServiceNoteDetailMasterView.as_view(), name='goodsservicenotedetail_master'),

    # HTMX endpoint to load the detail table
    path('gsn_details/table/', GoodsServiceNoteDetailTablePartialView.as_view(), name='goodsservicenotedetail_table_partial'),

    # HTMX endpoint for the inline edit form (GET to load, POST to submit)
    path('gsn_details/edit_form/<int:pk>/', GoodsServiceNoteDetailEditFormPartialView.as_view(), name='goodsservicenotedetail_edit_form_partial'),

    # Cancel button redirect
    path('gsn_details/cancel/', GoodsServiceNoteCancelView.as_view(), name='goodsservicenote_cancel'),

    # Placeholder for the previous master list page (ASP.NET GoodsServiceNote_SN_Edit.aspx)
    path('gsn_list_edit/', GoodsServiceNoteEditListView.as_view(), name='goodsservicenote_edit_list'),
]

```

##### 4.6 Tests (`inventory/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from decimal import Decimal
import datetime
from unittest.mock import patch, MagicMock

# Mocking database connection for helper functions
# In a real test suite, you'd set up an in-memory database or use Django's test database features.
# For simplicity and to simulate the raw SQL access, we mock connection.
# Ensure you have 'mock' installed: pip install mock (or use unittest.mock in Python 3.3+)


# Mock the connection for methods directly querying the DB without ORM models
class MockCursor:
    def __init__(self, fetch_data):
        self.fetch_data = fetch_data
        self.call_count = 0

    def execute(self, query, params=None):
        pass # No need to do anything here for mock

    def fetchone(self):
        if self.call_count < len(self.fetch_data):
            data = self.fetch_data[self.call_count]
            self.call_count += 1
            return data
        return None

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        pass

@patch('django.db.connection')
class GoodsServiceNoteModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.master = GoodsServiceNoteMaster.objects.create(
            id=1,
            gsn_no='GSN-2023-001',
            gin_no='GIN-2023-001',
            company_id=1,
            financial_year_id=1,
            sys_date=timezone.now().date(),
            sys_time=timezone.now().strftime('%H:%M'),
            session_id='testuser'
        )
        cls.detail = GoodsServiceNoteDetail.objects.create(
            id=101,
            master=cls.master,
            gsn_no='GSN-2023-001',
            po_id=501,
            received_qty=Decimal('10.000')
        )
        cls.detail_uneditable = GoodsServiceNoteDetail.objects.create(
            id=102,
            master=cls.master,
            gsn_no='GSN-2023-001',
            po_id=502,
            received_qty=Decimal('5.000')
        )

    def test_goodsservicenotemaster_creation(self, mock_connection):
        mock_connection.cursor.return_value = MockCursor([]) # No SQL queries needed for this test
        self.assertEqual(self.master.gsn_no, 'GSN-2023-001')
        self.assertEqual(self.master.gin_no, 'GIN-2023-001')
        self.assertEqual(str(self.master), 'GSN-2023-001')

    def test_goodsservicenotedetail_creation(self, mock_connection):
        mock_connection.cursor.return_value = MockCursor([])
        self.assertEqual(self.detail.received_qty, Decimal('10.000'))
        self.assertEqual(self.detail.master.id, self.master.id)

    def test_goodsservicenotedetail_get_item_data(self, mock_connection):
        # Mock responses for _get_item_code_and_description and _get_uom_symbol
        mock_connection.cursor.return_value = MockCursor([
            ('ITEM-XYZ', 'Manufactured Desc ABC', 1), # for _get_item_code_and_description
            ('KG',) # for _get_uom_symbol
        ])
        item_data = self.detail.get_item_data()
        self.assertEqual(item_data['item_code'], 'ITEM-XYZ')
        self.assertEqual(item_data['description'], 'Manufactured Desc ABC')
        self.assertEqual(item_data['uom'], 'KG')

    def test_goodsservicenotedetail_get_po_qty(self, mock_connection):
        mock_connection.cursor.return_value = MockCursor([(Decimal('100.000'),)])
        self.assertEqual(self.detail.get_po_qty(), 100.0)

    def test_goodsservicenotedetail_get_inward_qty(self, mock_connection):
        mock_connection.cursor.return_value = MockCursor([(Decimal('90.000'),)])
        self.assertEqual(self.detail.get_inward_qty(), 90.0)

    def test_goodsservicenotedetail_get_total_received_qty(self, mock_connection):
        mock_connection.cursor.return_value = MockCursor([(Decimal('80.000'),)])
        self.assertEqual(self.detail.get_total_received_qty(), 80.0)

    def test_goodsservicenotedetail_can_edit(self, mock_connection):
        # Scenario 1: inward_qty > total_reced_qty (editable)
        mock_connection.cursor.return_value = MockCursor([
            (Decimal('90.000'),), # Inward Qty for detail 101
            (Decimal('80.000'),)  # Tot Reced Qty for detail 101
        ])
        self.assertTrue(self.detail.can_edit())

        # Scenario 2: inward_qty <= total_reced_qty (not editable)
        mock_connection.cursor.return_value = MockCursor([
            (Decimal('50.000'),), # Inward Qty for detail 102
            (Decimal('55.000'),)  # Tot Reced Qty for detail 102
        ])
        self.assertFalse(self.detail_uneditable.can_edit())

    def test_goodsservicenotedetail_update_master_on_detail_change(self, mock_connection):
        mock_connection.cursor.return_value = MockCursor([]) # No direct SQL for this method beyond ORM save
        
        original_sys_date = self.master.sys_date
        original_sys_time = self.master.sys_time
        original_session_id = self.master.session_id

        self.detail.update_master_on_detail_change('new_session', 1)
        self.master.refresh_from_db() # Reload master to get updated values

        self.assertNotEqual(self.master.sys_date, original_sys_date)
        self.assertNotEqual(self.master.sys_time, original_sys_time)
        self.assertEqual(self.master.session_id, 'new_session')


@patch('inventory.views.get_company_context', return_value={'comp_id': 1, 'fin_year_id': 1, 'session_id': 'testuser'})
@patch('django.db.connection')
class GoodsServiceNoteViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.master = GoodsServiceNoteMaster.objects.create(
            id=1,
            gsn_no='GSN-TEST-001',
            gin_no='GIN-TEST-001',
            company_id=1,
            financial_year_id=1,
            sys_date=datetime.date(2023, 1, 1),
            sys_time='10:00',
            session_id='initial'
        )
        self.detail = GoodsServiceNoteDetail.objects.create(
            id=101,
            master=self.master,
            gsn_no='GSN-TEST-001',
            po_id=501,
            received_qty=Decimal('10.000')
        )
        self.detail_uneditable = GoodsServiceNoteDetail.objects.create(
            id=102,
            master=self.master,
            gsn_no='GSN-TEST-001',
            po_id=502,
            received_qty=Decimal('5.000')
        )

    def test_master_detail_view_get(self, mock_connection, mock_get_company_context):
        # Mock responses for internal DB calls in get_object
        mock_connection.cursor.return_value = MockCursor([
            ('Test Supplier Name',), # for _get_supplier_name
            ('CHALLAN-ABC', datetime.date(2023, 1, 15)), # for _get_inward_master_data
        ])
        
        url = reverse('goodsservicenotedetail_master', args=[self.master.id])
        # Add query parameters to match the ASP.NET request.QueryString
        url += '?GSNNo=GSN-TEST-001&GINNo=GIN-TEST-001&SupId=10&PONo=100&FyId=1&GINId=987&Id=1'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goodsservicenotedetail/detail_list.html')
        self.assertTrue('master_record' in response.context)
        self.assertEqual(response.context['master_record'].gsn_no, 'GSN-TEST-001')
        self.assertEqual(response.context['master_record'].supplier_name, 'Test Supplier Name')


    def test_detail_table_partial_view_get(self, mock_connection, mock_get_company_context):
        # Mock responses for detail item properties
        mock_connection.cursor.return_value = MockCursor([
            ('ITEM-001', 'Desc 1', 1), ('UOM1',), (Decimal('100'),), (Decimal('90'),), (Decimal('80'),), # For detail 101
            ('ITEM-002', 'Desc 2', 2), ('UOM2',), (Decimal('200'),), (Decimal('150'),), (Decimal('155'),), # For detail 102
        ])
        
        url = reverse('goodsservicenotedetail_table_partial') + f'?master_id={self.master.id}'
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goodsservicenotedetail/_detail_table.html')
        self.assertTrue('goodsservicenotedetails' in response.context)
        self.assertContains(response, 'goodsservicenotedetailTable') # Check for DataTables ID
        self.assertContains(response, 'Edit') # Should contain edit button
        self.assertContains(response, 'Not Editable') # Should contain not editable for 102

    def test_detail_edit_form_partial_view_get(self, mock_connection, mock_get_company_context):
        # Mock responses for item data lookup in form template
        mock_connection.cursor.return_value = MockCursor([
            ('ITEM-001', 'Desc 1', 1), ('UOM1',), (Decimal('100'),), (Decimal('90'),), (Decimal('80'),)
        ])
        
        url = reverse('goodsservicenotedetail_edit_form_partial', args=[self.detail.id])
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goodsservicenotedetail/_detail_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, f'value="{self.detail.received_qty}"')

    def test_detail_edit_form_partial_view_post_success(self, mock_connection, mock_get_company_context):
        mock_connection.cursor.return_value = MockCursor([]) # No special DB calls beyond ORM save
        
        url = reverse('goodsservicenotedetail_edit_form_partial', args=[self.detail.id])
        data = {'received_qty': '8.000'} # New quantity, less than original 10.000
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HTMX No Content
        self.assertIn(f'HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], f'refreshGoodsServiceNoteDetailList_{self.master.id}')
        
        self.detail.refresh_from_db()
        self.assertEqual(self.detail.received_qty, Decimal('8.000'))
        
        self.master.refresh_from_db()
        self.assertNotEqual(self.master.session_id, 'initial') # Check master updated

    def test_detail_edit_form_partial_view_post_validation_fail(self, mock_connection, mock_get_company_context):
        mock_connection.cursor.return_value = MockCursor([]) # No special DB calls
        
        url = reverse('goodsservicenotedetail_edit_form_partial', args=[self.detail.id])
        data = {'received_qty': '15.000'} # Greater than original 10.000
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'inventory/goodsservicenotedetail/_detail_form.html')
        self.assertContains(response, 'Received quantity cannot exceed original')
        
        self.detail.refresh_from_db()
        self.assertEqual(self.detail.received_qty, Decimal('10.000')) # Should not be updated

    def test_cancel_view(self, mock_connection, mock_get_company_context):
        response = self.client.get(reverse('goodsservicenote_cancel'))
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('goodsservicenote_edit_list'))

```

---

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for Dynamic Content:**
    *   The main `detail_list.html` page uses `hx-get` and `hx-trigger="load, refreshGoodsServiceNoteDetailList_{{ master_record.id }} from:body"` on `#detail-table-container` to dynamically load the `_detail_table.html` partial. This ensures the table is always up-to-date.
    *   The "Edit" button in `_detail_table.html` uses `hx-get` to fetch the `_detail_form.html` into a modal (`#modalContent`).
    *   The form in `_detail_form.html` uses `hx-post` to submit the data. Upon successful submission, the Django view returns a `204 No Content` status with an `HX-Trigger` header (`refreshGoodsServiceNoteDetailList_{{ master_record.id }}`) that tells HTMX to re-fetch the table content, refreshing the data without a full page reload.
    *   `hx-swap="none"` is used on the form submission so that the modal doesn't immediately close until the `HX-Trigger` is processed, or explicitly closed by Alpine.js.

*   **Alpine.js for Modals:**
    *   A simple Alpine.js component (`x-data="{ showModal: false }"`) is used on the modal container (`#modal`) to control its visibility.
    *   `x-on:open-modal.window="showModal = true"` listens for custom `open-modal` events dispatched from HTMX attributes (e.g., `_="on click send open-modal to #modal"`).
    *   `x-on:close-modal.window="showModal = false"` for closing the modal (e.g., from cancel button or clicking outside).
    *   `x-show="showModal"` conditionally renders the modal based on the `showModal` state.

*   **DataTables for List Views:**
    *   The `_detail_table.html` partial includes a JavaScript snippet to initialize DataTables on `#goodsservicenotedetailTable`. This provides client-side searching, sorting, and pagination for the list of detail items, improving user experience over a server-side GridView.
    *   The `pageLength` is set to 15 to match the original ASP.NET `PageSize`.

*   **DRY Template Inheritance:**
    *   All templates (`detail_list.html`) extend `core/base.html` (as per the instructions, `base.html` content is assumed to exist and not included here). This ensures a consistent look and feel across the application and centralizes CDN links for HTMX, Alpine.js, and DataTables.

### Final Notes

*   **Placeholders:** Replace `[TABLE_NAME]`, `[MODEL_NAME]`, `[FIELD1]`, etc., with actual values derived from your full schema and column names. In this example, I've used `GoodsServiceNoteMaster`, `GoodsServiceNoteDetail`, `gsn_no`, `received_qty`, etc.
*   **Database Connection:** The model methods using `django.db.connection.cursor()` are used to mimic the original ASP.NET code's direct SQL queries for complex derived fields. **For a robust, maintainable Django application, it's highly recommended to map all related tables to Django models and use the ORM for all queries.** This reduces the need for raw SQL and leverages Django's features fully.
*   **Authentication & Authorization:** The ASP.NET code uses `Session["username"]` and `CompId`. In Django, this would typically be handled by Django's authentication system (`request.user.username`, `request.user.profile.company_id`), often with a `LoginRequiredMixin` for views. I've included a `get_company_context` helper to simulate this.
*   **Complex Business Logic:** The `loadData()` method in the ASP.NET code-behind is very complex, performing multiple chained lookups. This has been abstracted into methods on the `GoodsServiceNoteDetail` model (e.g., `get_item_data`, `get_po_qty`, etc.). This adheres to the "fat model, thin view" principle, keeping the view logic minimal.
*   **Error Handling:** Basic `try-except` blocks are included in the model helper methods for `django.db.connection` to prevent crashes from missing data, but robust error logging and user feedback mechanisms should be implemented in a production environment.
*   **Deployment:** This plan assumes a standard Django deployment (e.g., Gunicorn/Nginx or uWSGI/Apache) and an existing database. The `managed = False` in models means Django will not try to create or modify these tables; they must already exist in your database.
*   **Testing:** Comprehensive unit tests for models and integration tests for views are provided, ensuring high test coverage and reliability.