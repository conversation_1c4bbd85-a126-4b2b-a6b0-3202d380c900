## ASP.NET to Django Conversion Script: Supplier Challan Module Modernization

This document outlines the strategic plan to modernize the existing ASP.NET Supplier Challan module into a robust, scalable, and maintainable Django application. Our approach prioritizes automation, leverages modern web technologies like HTMX and Alpine.js, and adheres to the "Fat Model, Thin View" architectural pattern for optimal performance and development efficiency.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

### Conversion Steps:

This plan addresses the `SupplierChallan_New_Details.aspx` page, which focuses on generating new supplier challans by selecting and specifying quantities for purchase request (PR) items.

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code interacts with multiple database tables, primarily `tblInv_Supplier_Challan_Master` for main challan details and `tblInv_Supplier_Challan_Details` for individual item quantities within a challan. The `GridView2` data is populated from a stored procedure (`GetSup_Challan`) that joins information from `tblInv_Supplier_PR_Details`, `tblInv_WorkOrder`, `tblInv_Item_Master`, and `tblInv_UnitOfMeasures`. The `disableCheck` method further queries `GetSupplier_PR_ChQty` to determine remaining quantities.

**Inferred Tables and Columns:**

*   **`tblInv_Supplier_Challan_Master` (for `SupplierChallanMaster` Model):**
    *   `Id` (PK, int)
    *   `SysDate` (date)
    *   `SysTime` (string/time)
    *   `SessionId` (string)
    *   `CompId` (int)
    *   `FinYearId` (int)
    *   `SCNo` (string)
    *   `SupplierId` (int, FK)
    *   `Remarks` (text)
    *   `VehicleNo` (string)
    *   `Transpoter` (string)

*   **`tblInv_Supplier_Challan_Details` (for `SupplierChallanDetail` Model):**
    *   `Id` (PK, int)
    *   `MId` (int, FK to `tblInv_Supplier_Challan_Master`)
    *   `PRDId` (int, FK to `tblInv_Supplier_PR_Details`)
    *   `ChallanQty` (decimal)

*   **`tblInv_Supplier_PR_Details` (for `PurchaseRequestDetail` Model - Represents source PR items for selection):**
    *   `Id` (PK, int, corresponds to `lblprDId`)
    *   `PRNo` (string, `lblprNo`)
    *   `PRDate` (date, `lblprDt`)
    *   `WONo` (string, `lblWono`)
    *   `ItemCode` (string, `lblit`) - *This would likely be a foreign key to ItemMaster*
    *   `Descr` (string, `lbldesc`)
    *   `Symbol` (string, `lbluom`) - *This would likely be a foreign key to UnitOfMeasure*
    *   `Qty` (decimal, `lblprqty`) - Purchase Request Quantity
    *   `SupplierId` (int, FK) - Supplier associated with this PR item
    *   `CompId` (int) - Company ID

*   **Auxiliary Tables (Placeholder models required for FK relationships or direct mapping):**
    *   `tblInv_Supplier_Master` (for `Supplier` Model)
    *   `tblInv_Item_Master` (for `Item` Model)
    *   `tblInv_UnitOfMeasures` (for `UnitOfMeasure` Model)

### Step 2: Identify Backend Functionality

**Read Operations:**
- The `fillGrid` method fetches data using the `GetSup_Challan` stored procedure, displaying a list of pending purchase request items (`GridView2`) for a specific supplier.
- The `disableCheck` method queries `GetSupplier_PR_ChQty` to calculate and display the remaining quantity for each PR item.

**Create Operations:**
- The `BtnAdd_Click` event handler is responsible for creating a new supplier challan.
- It generates a unique `SCNo` (Supplier Challan Number) by incrementing the last existing number for the company and financial year.
- It inserts records into `tblInv_Supplier_Challan_Master` for the main challan details.
- For each selected item in the `GridView2` with a valid quantity, it inserts records into `tblInv_Supplier_Challan_Details`.

**Validation Logic:**
- Quantity validation (`ReqChNo`, `Reg2` on `txtqty`): ensures quantity is numeric, not empty, and positive.
- Business logic validation (`Convert.ToDouble(decimal.Parse((PrQty - (Qty + TotChalnQty)).ToString()).ToString("N3")) >= 0`): ensures the entered challan quantity does not exceed the remaining quantity for a PR item.
- Check for at least one selected item: `if (x == y && y > 0)`.

**Update/Delete Operations:**
- No explicit update or delete operations for existing challans are present on this "New Details" page. The checkbox `oncheckedchanged` only affects the UI state of the `txtqty` field.

### Step 3: Infer UI Components

**Key Controls and Their Roles:**

*   **`TabContainer1` (AjaxControlToolkit.TabContainer):** Used for tabbed navigation ("Generate" and "View"). In Django, this will be handled by HTMX for dynamic content loading, potentially with Alpine.js for UI state.
*   **`GridView2` (asp:GridView):** Displays a list of PR items. This will be replaced by a Django template rendering an HTML `<table>` integrated with DataTables.
    *   Columns like `PRNo`, `Date`, `WONo`, `Item Code`, `Description`, `UOM`, `PR Qty`, `Remain Qty` will map to table columns.
    *   `CheckBox1` (asp:CheckBox): To select PR items. This will be an HTML checkbox, dynamically enabling/disabling the quantity input using Alpine.js or HTMX.
    *   `txtqty` (asp:TextBox): To enter challan quantity. This will be an HTML `<input type="number">`.
    *   `RequiredFieldValidator`, `RegularExpressionValidator`: Client-side validation will be handled by HTML5 attributes and Django form validation on the server.
*   **`txtVehicleNo`, `txtTranspoter`, `txtRemarks` (asp:TextBox):** Standard text inputs for master challan details. These will map to Django form fields.
*   **`BtnAdd` (asp:Button):** "Submit" button. Will trigger an HTMX POST request to create the challan.
*   **`btnCancel` (asp:Button):** "Cancel" button. Will trigger a redirect.
*   **`myframe` (html:Iframe):** Used to load another page (`SupplierChallan_Clear_Details.aspx`). This will be replaced by an HTMX-loaded partial template or a separate Django view.

---

### Step 4: Generate Django Code

**Application Name:** `inventory`

For clarity and adherence to the "Fat Model, Thin View" principle, the complex logic around quantity validation and challan number generation will reside within the models or custom managers.

```python
# inventory/models.py
from django.db import models, transaction
from django.db.models import Sum, F
from django.core.exceptions import ValidationError
from datetime import date, datetime

# Mock/Placeholder Models for related tables (assuming managed=False)
# These represent existing tables in the database that are not managed by Django migrations
# but are used for foreign key relationships.

class Company(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='CompanyName', max_length=255)
    class Meta:
        managed = False
        db_table = 'tblCompanyMaster' # Assumed table name
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

class FinancialYear(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    year = models.CharField(db_column='FinYearName', max_length=10)
    class Meta:
        managed = False
        db_table = 'tblFinancialYearMaster' # Assumed table name
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

class UserSession(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    username = models.CharField(db_column='UserName', max_length=50)
    class Meta:
        managed = False
        db_table = 'tblUserSession' # Assumed table name
        verbose_name = 'User Session'
        verbose_name_plural = 'User Sessions'

class Supplier(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='SupplierName', max_length=255)
    code = models.CharField(db_column='SupplierCode', max_length=50, unique=True, null=True) # Assuming code exists for chkEmpCustSupplierCode
    class Meta:
        managed = False
        db_table = 'tblInv_Supplier_Master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def chk_emp_cust_supplier_code(self, type_id, company_id):
        # Simulates fun.chkEmpCustSupplierCode(supid, 3, CompId)
        # Assuming type_id=3 is for supplier.
        # This function in ASP.NET returns 1 if valid, otherwise implies invalid.
        # Here we'll return True if supplier code exists for company, False otherwise.
        # This might be an actual database lookup or a simple existence check.
        # For now, let's assume existence is sufficient.
        return Supplier.objects.filter(id=self.id, company_id=company_id).exists() # Adjust based on actual impl.

class Item(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    code = models.CharField(db_column='ItemCode', max_length=50)
    description = models.CharField(db_column='Descr', max_length=255)
    class Meta:
        managed = False
        db_table = 'tblInv_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

class UnitOfMeasure(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=10)
    class Meta:
        managed = False
        db_table = 'tblInv_UnitOfMeasures'
        verbose_name = 'Unit of Measure'
        verbose_name_plural = 'Units of Measure'

class PurchaseRequestDetail(models.Model):
    # This model directly maps to tblInv_Supplier_PR_Details
    # The GridView data is a result of joins on this table.
    id = models.IntegerField(db_column='Id', primary_key=True) # Corresponds to lblprDId
    pr_no = models.CharField(db_column='PRNo', max_length=50)
    pr_date = models.DateField(db_column='PRDate')
    wo_no = models.CharField(db_column='WONo', max_length=50)
    item_code_fk = models.ForeignKey(Item, on_delete=models.DO_NOTHING, db_column='ItemCode', related_name='pr_details_by_item_code') # Assuming it's the actual ItemCode as FK
    description = models.CharField(db_column='Descr', max_length=255) # Redundant if item_code_fk is used, but kept for direct mapping to ASPX
    uom_symbol_fk = models.ForeignKey(UnitOfMeasure, on_delete=models.DO_NOTHING, db_column='UOMId', related_name='pr_details_by_uom') # Assuming UOMId as FK
    pr_quantity = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3)
    supplier_fk = models.ForeignKey(Supplier, on_delete=models.DO_NOTHING, db_column='SupplierId', related_name='pr_details')
    company_fk = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='pr_details')

    class Meta:
        managed = False
        db_table = 'tblInv_Supplier_PR_Details'
        verbose_name = 'Purchase Request Detail'
        verbose_name_plural = 'Purchase Request Details'

    @property
    def item_code(self):
        return self.item_code_fk.code if self.item_code_fk else ''

    @property
    def uom_symbol(self):
        return self.uom_symbol_fk.symbol if self.uom_symbol_fk else ''

    def get_total_challaned_quantity(self):
        """Calculates the total quantity already challaned for this PR detail."""
        return self.challan_details.aggregate(Sum('challan_quantity'))['challan_quantity__sum'] or 0.0

    def get_remaining_quantity(self):
        """Calculates the remaining quantity that can be challaned for this PR detail."""
        return self.pr_quantity - self.get_total_challaned_quantity()

    @classmethod
    def get_eligible_pr_items(cls, supplier_id, company_id):
        """
        Simulates the GetSup_Challan and disableCheck logic.
        Returns PR details with remaining quantity > 0 for a given supplier and company.
        """
        # This will fetch PR details and calculate remaining quantity for each.
        # In a high-performance scenario, this might be optimized with a database view or custom SQL.
        eligible_items = []
        for item in cls.objects.filter(supplier_fk_id=supplier_id, company_fk_id=company_id):
            if item.get_remaining_quantity() > 0:
                eligible_items.append(item)
        return eligible_items


class SupplierChallanMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate', default=date.today)
    sys_time = models.CharField(db_column='SysTime', max_length=10, default=datetime.now().strftime('%H:%M:%S'))
    session_id = models.CharField(db_column='SessionId', max_length=50) # Maps to Session["username"]
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='master_challans')
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='master_challans')
    challan_no = models.CharField(db_column='SCNo', max_length=10)
    supplier = models.ForeignKey(Supplier, on_delete=models.DO_NOTHING, db_column='SupplierId', related_name='master_challans')
    remarks = models.TextField(db_column='Remarks', blank=True, null=True)
    vehicle_no = models.CharField(db_column='VehicleNo', max_length=50, blank=True, null=True)
    transporter = models.CharField(db_column='Transpoter', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_Supplier_Challan_Master'
        verbose_name = 'Supplier Challan'
        verbose_name_plural = 'Supplier Challans'

    def __str__(self):
        return f"Challan {self.challan_no} ({self.supplier.name if self.supplier else 'N/A'})"

    def get_next_challan_no(self):
        """Generates the next sequential challan number based on company and financial year."""
        last_challan = SupplierChallanMaster.objects.filter(
            company=self.company,
            financial_year=self.financial_year
        ).order_by(F('id').desc()).first() # Order by ID descending for the latest
        if last_challan and last_challan.challan_no.isdigit():
            next_no = int(last_challan.challan_no) + 1
        else:
            next_no = 1
        return f"{next_no:04d}" # Format as "0001"


class SupplierChallanDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master_challan = models.ForeignKey(SupplierChallanMaster, on_delete=models.CASCADE, db_column='MId', related_name='challan_details')
    pr_detail = models.ForeignKey(PurchaseRequestDetail, on_delete=models.DO_NOTHING, db_column='PRDId', related_name='challan_details')
    challan_quantity = models.DecimalField(db_column='ChallanQty', max_digits=18, decimal_places=3)

    class Meta:
        managed = False
        db_table = 'tblInv_Supplier_Challan_Details'
        verbose_name = 'Supplier Challan Detail'
        verbose_name_plural = 'Supplier Challan Details'

    def __str__(self):
        return f"Detail for Challan {self.master_challan.challan_no}: PR {self.pr_detail.pr_no} Qty {self.challan_quantity}"

    def clean(self):
        """Custom validation for challan_quantity against remaining PR quantity."""
        if self.challan_quantity <= 0:
            raise ValidationError('Challan quantity must be greater than zero.')

        if self.challan_quantity > self.pr_detail.get_remaining_quantity():
            raise ValidationError(
                f"Challan quantity ({self.challan_quantity}) exceeds remaining quantity "
                f"({self.pr_detail.get_remaining_quantity()}) for PR {self.pr_detail.pr_no}."
            )

    @classmethod
    @transaction.atomic
    def create_challan_with_details(cls, master_data, pr_items_data, user_session_id, company_id, financial_year_id):
        """
        Creates a new SupplierChallanMaster and associated SupplierChallanDetails.
        Handles SCNo generation and quantity validations.
        """
        supplier = Supplier.objects.get(id=master_data['supplier_id'])
        company = Company.objects.get(id=company_id)
        fin_year = FinancialYear.objects.get(id=financial_year_id)
        
        # 1. Validate supplier code (simulates chkEmpCustSupplierCode)
        if not supplier.chk_emp_cust_supplier_code(type_id=3, company_id=company_id):
            raise ValidationError("Invalid supplier code for the given company.")

        # Prepare master record
        master_instance = SupplierChallanMaster(
            supplier=supplier,
            remarks=master_data.get('remarks'),
            vehicle_no=master_data.get('vehicle_no'),
            transporter=master_data.get('transporter'),
            session_id=user_session_id,
            company=company,
            financial_year=fin_year,
            sys_date=date.today(),
            sys_time=datetime.now().strftime('%H:%M:%S')
        )
        master_instance.challan_no = master_instance.get_next_challan_no()
        master_instance.save() # Save to get the Id for details

        details_created_count = 0
        for item_data in pr_items_data:
            pr_detail_id = item_data['pr_detail_id']
            challan_qty = item_data['challan_quantity']

            if challan_qty is None or challan_qty <= 0:
                continue # Skip if quantity is invalid or not provided

            pr_detail = PurchaseRequestDetail.objects.get(id=pr_detail_id)

            detail_instance = SupplierChallanDetail(
                master_challan=master_instance,
                pr_detail=pr_detail,
                challan_quantity=challan_qty
            )
            detail_instance.clean() # Run custom validation
            detail_instance.save()
            details_created_count += 1
        
        if details_created_count == 0:
            raise ValidationError("No valid PR items selected or quantities provided for challan creation.")

        return master_instance


# Helper to simulate Session and QueryString values for testing/dev
# In a real app, these would come from request.session and request.GET
class AppConfig:
    def __init__(self, comp_id, fin_year_id, username):
        self.comp_id = comp_id
        self.fin_year_id = fin_year_id
        self.username = username

# Example usage for testing/dev, not part of models file itself
# config = AppConfig(comp_id=1, fin_year_id=1, username='testuser')
# supplier_id = 1 # from request.GET['SupplierId']
# master_data = {'supplier_id': supplier_id, 'remarks': 'Test Challan', ...}
# pr_items_data = [{'pr_detail_id': 1, 'challan_quantity': 10}, ...]
# try:
#     new_challan = SupplierChallanMaster.create_challan_with_details(
#         master_data, pr_items_data, config.username, config.comp_id, config.fin_year_id
#     )
#     print(f"Challan {new_challan.challan_no} created.")
# except ValidationError as e:
#     print(f"Error creating challan: {e.message}")

```

```python
# inventory/forms.py
from django import forms
from django.forms import formset_factory
from .models import SupplierChallanMaster, SupplierChallanDetail, PurchaseRequestDetail

class SupplierChallanMasterForm(forms.ModelForm):
    # Using DecimalField for quantity-like inputs where precision is important
    # Assuming 'SupplierId' is passed via URL, so it's not directly in the form fields.
    
    class Meta:
        model = SupplierChallanMaster
        fields = ['remarks', 'vehicle_no', 'transporter']
        widgets = {
            'remarks': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-24', 'rows': 3}),
            'vehicle_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'transporter': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }

class SupplierChallanDetailForm(forms.Form):
    # This form is for the dynamically selected PR items
    pr_detail_id = forms.IntegerField(widget=forms.HiddenInput())
    # challan_quantity corresponds to 'txtqty'
    challan_quantity = forms.DecimalField(
        required=False, # Not required if checkbox is unchecked
        min_value=0.001, # Simulates "not zero" and positive check
        max_digits=18,
        decimal_places=3,
        widget=forms.NumberInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter quantity',
            'step': '0.001',
            'x-bind:disabled': '!isChecked', # Alpine.js to enable/disable
            'x-bind:class': "{'bg-gray-100': !isChecked}"
        })
    )
    # The checkbox itself is part of the UI, not a form field for submission directly in this setup
    # Its state is used to determine if challan_quantity should be validated/included.

    def clean_challan_quantity(self):
        quantity = self.cleaned_data.get('challan_quantity')
        pr_detail_id = self.cleaned_data.get('pr_detail_id')

        if quantity is None:
            # If not checked, quantity will be None, skip validation
            return None

        if pr_detail_id:
            try:
                pr_detail = PurchaseRequestDetail.objects.get(id=pr_detail_id)
                remaining_qty = pr_detail.get_remaining_quantity()

                if quantity > remaining_qty:
                    raise forms.ValidationError(
                        f"Quantity ({quantity}) exceeds remaining ({remaining_qty}) for PR {pr_detail.pr_no}."
                    )
            except PurchaseRequestDetail.DoesNotExist:
                raise forms.ValidationError("Invalid Purchase Request Detail.")
        
        return quantity

# We won't use a Django FormSet for the main submission in the traditional sense
# because the user dynamically selects rows from a potentially large list.
# Instead, the view will process selected items from HTMX POST data.
# The SupplierChallanDetailForm is primarily for individual quantity validation.

```

```python
# inventory/views.py
from django.views.generic import TemplateView, View
from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.forms import DecimalField
from django.core.exceptions import ValidationError as DjangoValidationError
from decimal import Decimal, InvalidOperation

from .models import SupplierChallanMaster, PurchaseRequestDetail, Supplier, Company, FinancialYear, UserSession
from .forms import SupplierChallanMasterForm, SupplierChallanDetailForm


class SupplierChallanCreateView(TemplateView):
    """
    Handles the main "Generate" tab, displaying the master form and container for PR items.
    """
    template_name = 'inventory/supplierchallan/create.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        supplier_id = self.kwargs.get('supplier_id')
        context['supplier'] = get_object_or_404(Supplier, id=supplier_id)
        context['master_form'] = SupplierChallanMasterForm()
        # Initial load of the PR items table is handled by HTMX on the template
        return context

class SupplierChallanPrItemsTablePartialView(View):
    """
    HTMX endpoint to render the DataTables for eligible PR items for a supplier.
    This replaces the GridView2 data binding and disableCheck logic.
    """
    def get(self, request, *args, **kwargs):
        supplier_id = self.kwargs.get('supplier_id')
        # Simulate session values (CompId, FinYearId)
        # In a real application, these would come from request.session
        company_id = request.session.get('comp_id', 1) # Default to 1 for example
        financial_year_id = request.session.get('fin_year_id', 1) # Default to 1 for example

        eligible_pr_items = PurchaseRequestDetail.get_eligible_pr_items(
            supplier_id=supplier_id,
            company_id=company_id
        )
        context = {
            'pr_items': eligible_pr_items,
        }
        return render(request, 'inventory/supplierchallan/_pr_item_list_table.html', context)


class SupplierChallanSubmitView(View):
    """
    Handles the submission of the entire challan creation form,
    including master details and selected PR items.
    This replaces the BtnAdd_Click logic.
    """
    def post(self, request, *args, **kwargs):
        supplier_id = self.kwargs.get('supplier_id')
        # Simulate session values (CompId, FinYearId, username)
        company_id = request.session.get('comp_id', 1)
        financial_year_id = request.session.get('fin_year_id', 1)
        user_session_id = request.session.get('username', 'default_user')

        master_form = SupplierChallanMasterForm(request.POST)

        # Collect selected PR item quantities from POST data
        # HTMX will send data like pr_item_id_1: 'quantity_1', pr_item_id_2: 'quantity_2'
        pr_items_data = []
        errors = {}

        # First, validate master form
        if not master_form.is_valid():
            messages.error(request, 'Please correct errors in the challan details.')
            # If not an HTMX request, redirect or re-render page with errors
            if not request.headers.get('HX-Request'):
                return redirect(reverse_lazy('supplierchallan_create', kwargs={'supplier_id': supplier_id}))
            # For HTMX, re-render form with errors
            context = {'master_form': master_form, 'supplier': get_object_or_404(Supplier, id=supplier_id)}
            return render(request, 'inventory/supplierchallan/_master_form.html', context)


        # Validate individual PR item quantities (simulates GridView iteration and validation)
        # We expect data in the format 'pr_item_id_X': 'quantity_value'
        pr_detail_field = DecimalField(max_digits=18, decimal_places=3, min_value=Decimal('0.001'))

        for key, value in request.POST.items():
            if key.startswith('pr_item_id_'):
                try:
                    pr_detail_id = int(key.replace('pr_item_id_', ''))
                    # Only process if quantity is provided and looks like a number
                    if value and value.strip() != '':
                        try:
                            # Convert to Decimal, then validate
                            challan_quantity = pr_detail_field.clean(value)
                            # Now, validate against remaining quantity via model's clean method
                            # Temporarily create an instance for validation
                            pr_detail = PurchaseRequestDetail.objects.get(id=pr_detail_id)
                            temp_detail = SupplierChallanDetail(
                                pr_detail=pr_detail,
                                challan_quantity=challan_quantity
                            )
                            temp_detail.clean() # This will raise ValidationError if quantity is too high
                            pr_items_data.append({'pr_detail_id': pr_detail_id, 'challan_quantity': challan_quantity})
                        except (InvalidOperation, ValueError):
                            errors[f'pr_item_id_{pr_detail_id}'] = 'Invalid quantity format.'
                        except DjangoValidationError as e:
                            errors[f'pr_item_id_{pr_detail_id}'] = e.message
                except ValueError:
                    continue # Not a valid pr_item_id_X key

        if errors:
            messages.error(request, 'Please correct quantity errors for selected items.')
            # For HTMX, re-render the table with inline errors if possible or provide a general error.
            # For simplicity, we'll send a general error and let HTMX re-render the list.
            if request.headers.get('HX-Request'):
                # Re-fetch the table to show updated state, but the errors won't be inline this way
                # A more sophisticated approach would involve returning JSON errors and Alpine.js handling.
                # For now, we'll signal a refresh and hope context gets passed.
                # Or, even better, return a re-rendered table with error messages.
                # As per fat model/thin view, errors are raised in models/forms.
                # Here, we'll try to re-render the table with context of errors, or return status 400.
                response = HttpResponse(status=400) # Indicate bad request
                response['HX-Trigger'] = 'showGlobalMessage' # Custom event for Alpine.js to pick up and display messages
                return response
            return redirect(reverse_lazy('supplierchallan_create', kwargs={'supplier_id': supplier_id}))


        if not pr_items_data:
            messages.error(request, 'No PR items selected or valid quantities provided for challan creation.')
            if request.headers.get('HX-Request'):
                response = HttpResponse(status=400)
                response['HX-Trigger'] = 'showGlobalMessage'
                return response
            return redirect(reverse_lazy('supplierchallan_create', kwargs={'supplier_id': supplier_id}))


        try:
            with transaction.atomic():
                new_challan = SupplierChallanMaster.create_challan_with_details(
                    master_data={
                        'supplier_id': supplier_id,
                        'remarks': master_form.cleaned_data.get('remarks'),
                        'vehicle_no': master_form.cleaned_data.get('vehicle_no'),
                        'transporter': master_form.cleaned_data.get('transporter')
                    },
                    pr_items_data=pr_items_data,
                    user_session_id=user_session_id,
                    company_id=company_id,
                    financial_year_id=financial_year_id
                )
            messages.success(request, f"Challan {new_challan.challan_no} inserted successfully!")
            # For HTMX request, send a success header to trigger UI refresh
            if request.headers.get('HX-Request'):
                # Reset master form fields for next entry
                response = HttpResponse(status=204) # No Content, success
                response['HX-Trigger'] = 'refreshPrItemList' # Custom event to refresh the PR items table
                return response
            return redirect(reverse_lazy('supplierchallan_create', kwargs={'supplier_id': supplier_id}))

        except DjangoValidationError as e:
            messages.error(request, f"Error creating challan: {e.message}")
            if request.headers.get('HX-Request'):
                response = HttpResponse(status=400)
                response['HX-Trigger'] = 'showGlobalMessage'
                return response
            return redirect(reverse_lazy('supplierchallan_create', kwargs={'supplier_id': supplier_id}))
        except Exception as e:
            messages.error(request, f"An unexpected error occurred: {str(e)}")
            if request.headers.get('HX-Request'):
                response = HttpResponse(status=500)
                response['HX-Trigger'] = 'showGlobalMessage'
                return response
            return redirect(reverse_lazy('supplierchallan_create', kwargs={'supplier_id': supplier_id}))


class SupplierChallanClearView(TemplateView):
    """
    Placeholder for the "Clear" tab content, replacing the iframe.
    This would typically display a list of completed challans or allow clearing.
    """
    template_name = 'inventory/supplierchallan/clear_challan.html' # This template would need to be created.
    # In a real scenario, this would likely be another HTMX-driven list view.
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        supplier_id = self.kwargs.get('supplier_id')
        context['supplier'] = get_object_or_404(Supplier, id=supplier_id)
        # Add logic to fetch and display "clearable" challans here
        context['clearable_challans'] = [] # Replace with actual data
        return context

```

```html
<!-- inventory/templates/inventory/supplierchallan/create.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <table width="100%" align="center" cellpadding="0" cellspacing="0">
        <tr height="21">
            <td style="background:url(/static/images/hdbg.JPG)" class="fontcsswhite px-4 py-2 rounded-t-lg">
                &nbsp;<strong>Supplier Challan - New</strong>
            </td>
        </tr>
    </table>

    <div x-data="{ activeTab: 'generate' }" class="mt-4">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <a href="#" @click.prevent="activeTab = 'generate'"
                   :class="{'border-indigo-500 text-indigo-600': activeTab === 'generate', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'generate'}"
                   class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                   hx-get="{% url 'supplierchallan_generate_tab' supplier.id %}"
                   hx-target="#tabContent"
                   hx-swap="innerHTML"
                   hx-trigger="click, load once delay:100ms[activeTab === 'generate']">
                    Generate
                </a>
                <a href="#" @click.prevent="activeTab = 'clear'"
                   :class="{'border-indigo-500 text-indigo-600': activeTab === 'clear', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'clear'}"
                   class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                   hx-get="{% url 'supplierchallan_clear_tab' supplier.id %}"
                   hx-target="#tabContent"
                   hx-swap="innerHTML"
                   hx-trigger="click, load once delay:100ms[activeTab === 'clear']">
                    Clear
                </a>
            </nav>
        </div>

        <div id="tabContent" class="p-4 bg-white shadow-sm rounded-b-lg">
            <!-- Content will be loaded here via HTMX -->
            <div class="text-center py-8">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading tab content...</p>
            </div>
        </div>
    </div>
</div>

<!-- Global messages container for HTMX triggers -->
<div id="globalMessages" x-data="{ show: false, message: '', type: '' }"
     @showglobalmessage.window="message = $event.detail.message; type = $event.detail.type; show = true; setTimeout(() => show = false, 5000);"
     x-show="show" x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0 transform -translate-y-4"
     x-transition:enter-end="opacity-100 transform translate-y-0"
     x-transition:leave="transition ease-in duration-300"
     x-transition:leave-start="opacity-100 transform translate-y-0"
     x-transition:leave-end="opacity-0 transform -translate-y-4"
     class="fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg text-white"
     :class="{'bg-green-500': type === 'success', 'bg-red-500': type === 'error'}">
    <span x-text="message"></span>
</div>

{% endblock %}

{% block extra_js %}
<script>
    // Dispatch custom event for global messages
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.detail.xhr.status >= 200 && evt.detail.xhr.status < 300) {
            const successMessages = Array.from(evt.detail.elt.querySelectorAll('.success')).map(el => el.textContent);
            if (successMessages.length > 0) {
                window.dispatchEvent(new CustomEvent('showGlobalMessage', { detail: { message: successMessages[0], type: 'success' } }));
            }
        } else if (evt.detail.xhr.status >= 400) {
            const errorMessages = Array.from(evt.detail.elt.querySelectorAll('.error')).map(el => el.textContent);
            if (errorMessages.length > 0) {
                window.dispatchEvent(new CustomEvent('showGlobalMessage', { detail: { message: errorMessages[0], type: 'error' } }));
            } else {
                window.dispatchEvent(new CustomEvent('showGlobalMessage', { detail: { message: 'An error occurred.', type: 'error' } }));
            }
        }
    });

    // Helper for DataTables initialization within HTMX
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'prItemsTable-container' || event.detail.target.closest('#prItemsTable-container')) {
            // Re-initialize DataTable if it's the PR items table or its parent
            $('#prItemsTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true // Destroy existing DataTable instance before re-initialization
            });
        }
    });
</script>
{% endblock %}
```

```html
<!-- inventory/templates/inventory/supplierchallan/_generate_challan_tab.html -->
<div x-data="generateChallanData()" class="p-4">
    <form id="challanForm" hx-post="{% url 'supplierchallan_submit' supplier.id %}" hx-swap="none"
          hx-on::after-request="handleFormResponse(event)">
        {% csrf_token %}

        <div class="mb-4">
            <h3 class="text-lg font-semibold text-gray-800 mb-2">Select Purchase Request Items</h3>
            <div id="prItemsTable-container"
                 hx-trigger="load, refreshPrItemList from:body"
                 hx-get="{% url 'supplierchallan_pr_items_table' supplier.id %}"
                 hx-swap="innerHTML">
                <!-- DataTable will be loaded here via HTMX -->
                <div class="text-center py-8">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading PR items...</p>
                </div>
            </div>
        </div>

        <div class="border-t border-gray-300 pt-4 mt-4">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Challan Details</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="{{ master_form.vehicle_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Vehicle No.</label>
                    {{ master_form.vehicle_no }}
                    {% if master_form.vehicle_no.errors %}<p class="text-red-500 text-xs mt-1">{{ master_form.vehicle_no.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ master_form.transporter.id_for_label }}" class="block text-sm font-medium text-gray-700">Transporter</label>
                    {{ master_form.transporter }}
                    {% if master_form.transporter.errors %}<p class="text-red-500 text-xs mt-1">{{ master_form.transporter.errors }}</p>{% endif %}
                </div>
                <div class="col-span-1 md:col-span-2">
                    <label for="{{ master_form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700">Remarks</label>
                    {{ master_form.remarks }}
                    {% if master_form.remarks.errors %}<p class="text-red-500 text-xs mt-1">{{ master_form.remarks.errors }}</p>{% endif %}
                </div>
            </div>
        </div>

        <div class="mt-6 flex justify-end space-x-4">
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow-sm">
                Submit
            </button>
            <a href="{% url 'supplierchallan_cancel' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded shadow-sm">
                Cancel
            </a>
        </div>
    </form>
</div>

<script>
    function generateChallanData() {
        return {
            handleFormResponse(event) {
                if (event.detail.xhr.status === 204) {
                    // Success, no content
                    console.log('Challan submitted successfully, resetting form.');
                    document.getElementById('challanForm').reset(); // Reset master form fields
                    // HTMX trigger 'refreshPrItemList' handled by prItemsTable-container
                } else if (event.detail.xhr.status === 400 || event.detail.xhr.status === 500) {
                    console.error('Challan submission failed.');
                    // Global message already triggered by base.html's htmx:afterRequest
                }
            }
        }
    }
</script>
```

```html
<!-- inventory/templates/inventory/supplierchallan/_pr_item_list_table.html -->
{% if pr_items %}
<div class="overflow-x-auto shadow-md sm:rounded-lg">
    <table id="prItemsTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"></th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PR NO</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th scope="col" class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">PR Qty</th>
                <th scope="col" class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Remain Qty</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for pr_item in pr_items %}
            <tr x-data="{ isChecked: false }">
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-center">
                    {% if pr_item.get_remaining_quantity > 0 %}
                        <input type="checkbox" id="checkbox_{{ pr_item.id }}"
                               x-model="isChecked"
                               class="form-checkbox h-4 w-4 text-blue-600 border-gray-300 rounded">
                        <input type="hidden" name="pr_item_id_{{ pr_item.id }}" value="{{ pr_item.id }}">
                    {% endif %}
                </td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ pr_item.pr_no }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ pr_item.pr_date|date:"d-M-Y" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ pr_item.wo_no }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ pr_item.item_code }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-left">{{ pr_item.description }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ pr_item.uom_symbol }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ pr_item.pr_quantity|floatformat:3 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ pr_item.get_remaining_quantity|floatformat:3 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm">
                    <input type="number" id="qty_{{ pr_item.id }}"
                           name="pr_item_id_{{ pr_item.id }}_quantity"
                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                           placeholder="Enter Qty" step="0.001" min="0.001"
                           x-bind:disabled="!isChecked || {{ pr_item.get_remaining_quantity }} <= 0"
                           x-bind:value="isChecked && {{ pr_item.get_remaining_quantity }} > 0 ? '' : ''"
                           x-bind:class="{ 'bg-gray-100': !isChecked || {{ pr_item.get_remaining_quantity }} <= 0 }">
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% else %}
<div class="text-center py-8">
    <p class="text-lg text-gray-600">No data to display !</p>
</div>
{% endif %}

<script>
    // DataTables initialization is handled by htmx:afterSwap event in create.html
    // This script block should only contain Alpine.js data/methods specific to this partial, if any.
</script>
```

```html
<!-- inventory/templates/inventory/supplierchallan/_clear_challan_tab.html -->
<div class="p-4">
    <h3 class="text-lg font-semibold text-gray-800 mb-4">Clear Supplier Challans</h3>
    <p class="text-gray-600">This section allows viewing and clearing existing supplier challans for {{ supplier.name }}.</p>
    <div class="mt-4 bg-gray-100 p-6 rounded-lg shadow-inner min-h-[400px] flex items-center justify-center">
        <p class="text-gray-500 italic">Content for clearing challans would be loaded here. (e.g., another DataTables listing completed challans)</p>
    </div>
</div>
```

```python
# inventory/urls.py
from django.urls import path
from .views import SupplierChallanCreateView, SupplierChallanPrItemsTablePartialView, SupplierChallanSubmitView, SupplierChallanClearView
from django.views.generic import RedirectView

urlpatterns = [
    # Main page for creating new challans, passing supplier_id via URL
    path('new/<int:supplier_id>/', SupplierChallanCreateView.as_view(), name='supplierchallan_create'),

    # HTMX endpoint to load the PR items table
    path('new/<int:supplier_id>/pr-items-table/', SupplierChallanPrItemsTablePartialView.as_view(), name='supplierchallan_pr_items_table'),

    # HTMX endpoint for submitting the challan creation form
    path('new/<int:supplier_id>/submit/', SupplierChallanSubmitView.as_view(), name='supplierchallan_submit'),

    # HTMX endpoint for the "Generate" tab content
    path('new/<int:supplier_id>/generate-tab/', SupplierChallanPrItemsTablePartialView.as_view(), name='supplierchallan_generate_tab'),

    # HTMX endpoint for the "Clear" tab content
    path('new/<int:supplier_id>/clear-tab/', SupplierChallanClearView.as_view(), name='supplierchallan_clear_tab'),

    # Redirect for the cancel button, or to a default list view
    path('cancel/', RedirectView.as_view(url=reverse_lazy('dashboard_home')), name='supplierchallan_cancel'), # Replace with your actual dashboard/list URL
]

```

```python
# inventory/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.db import connection, transaction
from datetime import date, datetime
from decimal import Decimal

from .models import (
    SupplierChallanMaster, SupplierChallanDetail, PurchaseRequestDetail,
    Supplier, Item, UnitOfMeasure, Company, FinancialYear, UserSession
)

class SupplierChallanModelTest(TestCase):
    # Set up dummy sequences for managed=False PKs if using PostgreSQL or similar
    # For SQLite or direct PK assignment, this might not be strictly necessary
    # but good practice for robustness in real-world scenarios.
    @classmethod
    def setUpTestData(cls):
        # Create dummy data for ForeignKey relationships
        cls.company = Company.objects.create(id=1, name='Test Company')
        cls.financial_year = FinancialYear.objects.create(id=1, year='2024-25')
        cls.user_session = UserSession.objects.create(id=1, username='testuser')
        cls.supplier = Supplier.objects.create(id=101, name='Test Supplier A', code='SUP001', company_id=cls.company.id) # Link supplier to company
        cls.supplier_invalid_company = Supplier.objects.create(id=102, name='Test Supplier B', code='SUP002', company_id=999) # Invalid company for tests

        cls.item = Item.objects.create(id=1, code='ITEM001', description='Test Item')
        cls.uom = UnitOfMeasure.objects.create(id=1, symbol='PCS')

        # Create test PurchaseRequestDetail instances
        cls.pr_detail_1 = PurchaseRequestDetail.objects.create(
            id=1, pr_no='PR001', pr_date=date(2024, 1, 1), wo_no='WO001',
            item_code_fk=cls.item, description='Item 1 Description', uom_symbol_fk=cls.uom,
            pr_quantity=Decimal('100.000'), supplier_fk=cls.supplier, company_fk=cls.company
        )
        cls.pr_detail_2 = PurchaseRequestDetail.objects.create(
            id=2, pr_no='PR002', pr_date=date(2024, 1, 5), wo_no='WO002',
            item_code_fk=cls.item, description='Item 2 Description', uom_symbol_fk=cls.uom,
            pr_quantity=Decimal('50.000'), supplier_fk=cls.supplier, company_fk=cls.company
        )
        # PR detail already partially challaned
        cls.pr_detail_3 = PurchaseRequestDetail.objects.create(
            id=3, pr_no='PR003', pr_date=date(2024, 1, 10), wo_no='WO003',
            item_code_fk=cls.item, description='Item 3 Description', uom_symbol_fk=cls.uom,
            pr_quantity=Decimal('75.000'), supplier_fk=cls.supplier, company_fk=cls.company
        )

        cls.existing_challan_master = SupplierChallanMaster.objects.create(
            id=1, sys_date=date(2024, 2, 1), sys_time='10:00:00', session_id='olduser',
            company=cls.company, financial_year=cls.financial_year, challan_no='0001',
            supplier=cls.supplier, remarks='Existing Challan', vehicle_no='VEH123', transporter='Trans ABC'
        )
        SupplierChallanDetail.objects.create(
            id=1, master_challan=cls.existing_challan_master, pr_detail=cls.pr_detail_3, challan_quantity=Decimal('25.000')
        )
        # PR detail with zero remaining quantity
        cls.pr_detail_4 = PurchaseRequestDetail.objects.create(
            id=4, pr_no='PR004', pr_date=date(2024, 1, 15), wo_no='WO004',
            item_code_fk=cls.item, description='Item 4 Description', uom_symbol_fk=cls.uom,
            pr_quantity=Decimal('20.000'), supplier_fk=cls.supplier, company_fk=cls.company
        )
        cls.existing_challan_master_2 = SupplierChallanMaster.objects.create(
            id=2, sys_date=date(2024, 2, 2), sys_time='11:00:00', session_id='olduser',
            company=cls.company, financial_year=cls.financial_year, challan_no='0002',
            supplier=cls.supplier, remarks='Existing Challan 2', vehicle_no='VEH124', transporter='Trans DEF'
        )
        SupplierChallanDetail.objects.create(
            id=2, master_challan=cls.existing_challan_master_2, pr_detail=cls.pr_detail_4, challan_quantity=Decimal('20.000')
        )


    def test_supplier_challan_master_creation(self):
        challan = SupplierChallanMaster.objects.create(
            id=3, sys_date=date(2024, 3, 1), sys_time='12:00:00', session_id='newuser',
            company=self.company, financial_year=self.financial_year, challan_no='0003',
            supplier=self.supplier, remarks='New Challan', vehicle_no='XYZ', transporter='Trans XYZ'
        )
        self.assertEqual(challan.challan_no, '0003')
        self.assertEqual(challan.supplier.name, 'Test Supplier A')

    def test_supplier_challan_detail_creation(self):
        challan = SupplierChallanMaster.objects.get(id=1)
        detail = SupplierChallanDetail.objects.create(
            id=3, master_challan=challan, pr_detail=self.pr_detail_1, challan_quantity=Decimal('10.000')
        )
        self.assertEqual(detail.challan_quantity, Decimal('10.000'))
        self.assertEqual(detail.pr_detail.pr_no, 'PR001')

    def test_pr_detail_remaining_quantity(self):
        # PR detail 1: 100 Qty, 0 challaned -> remaining 100
        self.assertEqual(self.pr_detail_1.get_remaining_quantity(), Decimal('100.000'))
        # PR detail 3: 75 Qty, 25 challaned -> remaining 50
        self.assertEqual(self.pr_detail_3.get_remaining_quantity(), Decimal('50.000'))
        # PR detail 4: 20 Qty, 20 challaned -> remaining 0
        self.assertEqual(self.pr_detail_4.get_remaining_quantity(), Decimal('0.000'))

    def test_get_next_challan_no(self):
        # Last challan is 0002
        new_master_instance = SupplierChallanMaster(company=self.company, financial_year=self.financial_year)
        self.assertEqual(new_master_instance.get_next_challan_no(), '0003')

        # Test with no existing challans for a different company
        other_company = Company.objects.create(id=2, name='Other Co')
        other_fin_year = FinancialYear.objects.create(id=2, year='2025-26')
        new_master_instance_2 = SupplierChallanMaster(company=other_company, financial_year=other_fin_year)
        self.assertEqual(new_master_instance_2.get_next_challan_no(), '0001')

    def test_pr_detail_clean_method_valid_quantity(self):
        detail_form_instance = SupplierChallanDetail(
            pr_detail=self.pr_detail_1, challan_quantity=Decimal('50.000')
        )
        try:
            detail_form_instance.clean()
        except DjangoValidationError:
            self.fail("ValidationError raised unexpectedly for valid quantity.")

    def test_pr_detail_clean_method_excess_quantity(self):
        detail_form_instance = SupplierChallanDetail(
            pr_detail=self.pr_detail_3, challan_quantity=Decimal('50.001') # exceeds remaining 50
        )
        with self.assertRaises(DjangoValidationError) as cm:
            detail_form_instance.clean()
        self.assertIn("exceeds remaining quantity", str(cm.exception))

    def test_pr_detail_clean_method_zero_quantity(self):
        detail_form_instance = SupplierChallanDetail(
            pr_detail=self.pr_detail_1, challan_quantity=Decimal('0.000')
        )
        with self.assertRaises(DjangoValidationError) as cm:
            detail_form_instance.clean()
        self.assertIn("must be greater than zero", str(cm.exception))

    def test_create_challan_with_details_success(self):
        master_data = {
            'supplier_id': self.supplier.id,
            'remarks': 'Test automated challan',
            'vehicle_no': 'TEST1234',
            'transporter': 'AutoTrans'
        }
        pr_items_data = [
            {'pr_detail_id': self.pr_detail_1.id, 'challan_quantity': Decimal('10.000')},
            {'pr_detail_id': self.pr_detail_3.id, 'challan_quantity': Decimal('20.000')} # Remaining was 50, so 20 is fine
        ]
        
        initial_challan_count = SupplierChallanMaster.objects.count()
        initial_detail_count = SupplierChallanDetail.objects.count()

        challan = SupplierChallanMaster.create_challan_with_details(
            master_data, pr_items_data, self.user_session.username, self.company.id, self.financial_year.id
        )

        self.assertIsNotNone(challan)
        self.assertEqual(SupplierChallanMaster.objects.count(), initial_challan_count + 1)
        self.assertEqual(SupplierChallanDetail.objects.count(), initial_detail_count + len(pr_items_data))
        self.assertEqual(challan.challan_no, '0003') # Because 0001, 0002 already exist
        self.assertEqual(self.pr_detail_1.get_remaining_quantity(), Decimal('90.000')) # 100 - 10
        self.assertEqual(self.pr_detail_3.get_remaining_quantity(), Decimal('30.000')) # (75-25) - 20 = 50 - 20

    def test_create_challan_with_details_validation_fail(self):
        master_data = {
            'supplier_id': self.supplier.id,
            'remarks': 'Test invalid challan',
            'vehicle_no': 'FAIL123',
            'transporter': 'FailTrans'
        }
        pr_items_data = [
            {'pr_detail_id': self.pr_detail_1.id, 'challan_quantity': Decimal('100.001')} # Exceeds
        ]

        initial_challan_count = SupplierChallanMaster.objects.count()
        initial_detail_count = SupplierChallanDetail.objects.count()

        with self.assertRaises(DjangoValidationError):
            SupplierChallanMaster.create_challan_with_details(
                master_data, pr_items_data, self.user_session.username, self.company.id, self.financial_year.id
            )

        self.assertEqual(SupplierChallanMaster.objects.count(), initial_challan_count) # No master created
        self.assertEqual(SupplierChallanDetail.objects.count(), initial_detail_count) # No details created

    def test_create_challan_with_no_valid_items(self):
        master_data = {
            'supplier_id': self.supplier.id,
            'remarks': 'Test no items',
            'vehicle_no': 'NOITEMS',
            'transporter': 'NoTrans'
        }
        pr_items_data = [] # No items provided

        initial_challan_count = SupplierChallanMaster.objects.count()
        initial_detail_count = SupplierChallanDetail.objects.count()

        with self.assertRaises(DjangoValidationError) as cm:
            SupplierChallanMaster.create_challan_with_details(
                master_data, pr_items_data, self.user_session.username, self.company.id, self.financial_year.id
            )
        self.assertIn("No valid PR items selected", str(cm.exception))
        self.assertEqual(SupplierChallanMaster.objects.count(), initial_challan_count)
        self.assertEqual(SupplierChallanDetail.objects.count(), initial_detail_count)

    def test_chk_emp_cust_supplier_code_valid(self):
        self.assertTrue(self.supplier.chk_emp_cust_supplier_code(3, self.company.id))

    def test_chk_emp_cust_supplier_code_invalid(self):
        # Supplier 'SUP002' (id=102) is linked to company_id=999, so it's invalid for company.id=1
        self.assertFalse(self.supplier_invalid_company.chk_emp_cust_supplier_code(3, self.company.id))


class SupplierChallanViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Set up session defaults for tests, simulating ASP.NET Session
        self.client.session['comp_id'] = 1
        self.client.session['fin_year_id'] = 1
        self.client.session['username'] = 'testuser'

        # Create necessary related objects for testing
        self.company = Company.objects.create(id=1, name='Test Company')
        self.financial_year = FinancialYear.objects.create(id=1, year='2024-25')
        self.user_session = UserSession.objects.create(id=1, username='testuser')
        self.supplier = Supplier.objects.create(id=101, name='Test Supplier A', code='SUP001', company_id=self.company.id)
        self.item = Item.objects.create(id=1, code='ITEM001', description='Test Item')
        self.uom = UnitOfMeasure.objects.create(id=1, symbol='PCS')

        # Create test PurchaseRequestDetail instances
        self.pr_detail_eligible = PurchaseRequestDetail.objects.create(
            id=10, pr_no='PR010', pr_date=date(2024, 4, 1), wo_no='WO010',
            item_code_fk=self.item, description='Eligible Item', uom_symbol_fk=self.uom,
            pr_quantity=Decimal('100.000'), supplier_fk=self.supplier, company_fk=self.company
        )
        self.pr_detail_zero_remaining = PurchaseRequestDetail.objects.create(
            id=11, pr_no='PR011', pr_date=date(2024, 4, 2), wo_no='WO011',
            item_code_fk=self.item, description='Zero Remaining Item', uom_symbol_fk=self.uom,
            pr_quantity=Decimal('50.000'), supplier_fk=self.supplier, company_fk=self.company
        )
        # Create an existing challan to make pr_detail_zero_remaining truly zero
        existing_challan_master = SupplierChallanMaster.objects.create(
            id=100, sys_date=date(2024, 3, 1), sys_time='09:00:00', session_id='testuser',
            company=self.company, financial_year=self.financial_year, challan_no='0001',
            supplier=self.supplier, remarks='Initial Challan', vehicle_no='V1', transporter='T1'
        )
        SupplierChallanDetail.objects.create(
            id=100, master_challan=existing_challan_master, pr_detail=self.pr_detail_zero_remaining, challan_quantity=Decimal('50.000')
        )

    def test_create_view_get(self):
        response = self.client.get(reverse('supplierchallan_create', kwargs={'supplier_id': self.supplier.id}))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/supplierchallan/create.html')
        self.assertContains(response, 'Supplier Challan - New')
        self.assertContains(response, '<div id="prItemsTable-container"') # Check if HTMX container is present

    def test_pr_items_table_partial_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('supplierchallan_pr_items_table', kwargs={'supplier_id': self.supplier.id}), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/supplierchallan/_pr_item_list_table.html')
        self.assertContains(response, 'Eligible Item')
        self.assertNotContains(response, 'Zero Remaining Item') # Should not be displayed as remaining qty is 0

    def test_challan_submit_view_post_success(self):
        master_data = {
            'remarks': 'New challan remarks',
            'vehicle_no': 'VEH987',
            'transporter': 'Swift Logistics'
        }
        # Simulate selected PR items with quantities
        pr_item_quantity_data = {
            f'pr_item_id_{self.pr_detail_eligible.id}_quantity': '10.000',
            f'pr_item_id_{self.pr_detail_eligible.id}': str(self.pr_detail_eligible.id), # Hidden input value
        }
        post_data = {**master_data, **pr_item_quantity_data}

        headers = {'HTTP_HX_REQUEST': 'true'}
        initial_master_count = SupplierChallanMaster.objects.count()
        initial_detail_count = SupplierChallanDetail.objects.count()

        response = self.client.post(reverse('supplierchallan_submit', kwargs={'supplier_id': self.supplier.id}), post_data, **headers)

        self.assertEqual(response.status_code, 204) # 204 No Content for successful HTMX POST
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshPrItemList', response.headers['HX-Trigger'])

        self.assertEqual(SupplierChallanMaster.objects.count(), initial_master_count + 1)
        self.assertEqual(SupplierChallanDetail.objects.count(), initial_detail_count + 1)

        new_master = SupplierChallanMaster.objects.order_by('-id').first()
        self.assertEqual(new_master.remarks, 'New challan remarks')
        self.assertEqual(new_master.challan_no, '0002') # Next in sequence after 0001
        self.assertEqual(self.pr_detail_eligible.get_remaining_quantity(), Decimal('90.000')) # 100 - 10

    def test_challan_submit_view_post_validation_error_quantity(self):
        master_data = {
            'remarks': 'Invalid quantity challan',
            'vehicle_no': 'BADQTY',
            'transporter': 'Bad Trans'
        }
        # Attempt to challan more than remaining
        pr_item_quantity_data = {
            f'pr_item_id_{self.pr_detail_eligible.id}_quantity': '1000.000', # Too much
            f'pr_item_id_{self.pr_detail_eligible.id}': str(self.pr_detail_eligible.id),
        }
        post_data = {**master_data, **pr_item_quantity_data}

        headers = {'HTTP_HX_REQUEST': 'true'}
        initial_master_count = SupplierChallanMaster.objects.count()

        response = self.client.post(reverse('supplierchallan_submit', kwargs={'supplier_id': self.supplier.id}), post_data, **headers)

        self.assertEqual(response.status_code, 400) # Bad Request
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('showGlobalMessage', response.headers['HX-Trigger'])
        self.assertEqual(SupplierChallanMaster.objects.count(), initial_master_count) # No challan created

    def test_challan_submit_view_post_no_items_selected(self):
        master_data = {
            'remarks': 'No items selected',
            'vehicle_no': 'NOITEMS',
            'transporter': 'None'
        }
        post_data = {**master_data} # No pr_item_id_X_quantity fields

        headers = {'HTTP_HX_REQUEST': 'true'}
        initial_master_count = SupplierChallanMaster.objects.count()

        response = self.client.post(reverse('supplierchallan_submit', kwargs={'supplier_id': self.supplier.id}), post_data, **headers)

        self.assertEqual(response.status_code, 400) # Bad Request
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('showGlobalMessage', response.headers['HX-Trigger'])
        self.assertEqual(SupplierChallanMaster.objects.count(), initial_master_count)

    def test_clear_challan_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('supplierchallan_clear_tab', kwargs={'supplier_id': self.supplier.id}), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/supplierchallan/_clear_challan_tab.html')
        self.assertContains(response, 'Clear Supplier Challans')
```

### Step 5: HTMX and Alpine.js Integration

**Key Principles Applied:**

1.  **HTMX for Tabbed Navigation:** The main `create.html` uses `hx-get` on tab clicks to load `_generate_challan_tab.html` and `_clear_challan_tab.html` into the `#tabContent` div. `hx-trigger="load once"` ensures the initial tab content loads automatically.
2.  **HTMX for DataTables Content:** The `_generate_challan_tab.html` includes a `div` with `id="prItemsTable-container"` that uses `hx-trigger="load, refreshPrItemList from:body"` and `hx-get="{% url 'supplierchallan_pr_items_table' supplier.id %}"` to fetch and load the PR item table. This ensures the table is always up-to-date after any CRUD operation.
3.  **Alpine.js for Checkbox-Quantity Interaction:** Each row in `_pr_item_list_table.html` uses `x-data="{ isChecked: false }"` to manage the state of its checkbox. `x-model="isChecked"` binds the checkbox, and `x-bind:disabled="!isChecked || pr_item.get_remaining_quantity <= 0"` and `x-bind:class` control the quantity input's enabled/disabled state and styling dynamically, without server-side interaction until form submission.
4.  **HTMX for Form Submission:** The main form in `_generate_challan_tab.html` uses `hx-post="{% url 'supplierchallan_submit' supplier.id %}"` and `hx-swap="none"`.
    *   `hx-swap="none"` prevents HTMX from replacing content on success, allowing the `HX-Trigger` header from the server to control UI updates.
    *   `hx-on::after-request` on the form (`handleFormResponse` in Alpine.js) can capture status codes and handle form resets or other UI logic.
5.  **`HX-Trigger` for Global UI Updates:**
    *   Upon successful challan creation (HTTP 204 from `SupplierChallanSubmitView`), the server sends `HX-Trigger: 'refreshPrItemList'`. This event is picked up by the `#prItemsTable-container` `div` which then re-fetches its content, refreshing the DataTables.
    *   For messages (success/error), `showGlobalMessage` is a custom event dispatched on `htmx:afterRequest` in `create.html`, caught by a global Alpine.js component for displaying alerts.
6.  **DataTables Integration:** The `_pr_item_list_table.html` contains the `<table>` element, and a `<script>` block (triggered by `htmx:afterSwap` on its parent in `create.html`) initializes DataTables, ensuring proper client-side searching, sorting, and pagination. `destroy: true` is crucial for re-initializing DataTables on subsequent HTMX loads.

### Final Notes

*   **Placeholders:** `{% url 'dashboard_home' %}` should be replaced with the actual URL name for your application's dashboard or a list of challans.
*   **Static Files:** Ensure your Django project is configured to serve static files correctly, including images like `hdbg.JPG` and any custom CSS referenced.
*   **User/Company/Financial Year Context:** The `request.session.get('comp_id')`, `request.session.get('fin_year_id')`, and `request.session.get('username')` mocks are placeholders. In a real application, proper authentication and context middleware would populate these `session` variables, ensuring security and correct data filtering.
*   **Error Handling:** The current error handling uses `messages.error` and `HX-Trigger` for global messages. For more granular per-field validation errors visible to the user, the `SupplierChallanSubmitView` would need to render the `_generate_challan_tab.html` partial (or just the `_master_form.html`) with the `master_form` containing `form.errors` and the `_pr_item_list_table.html` would need a mechanism to display errors next to each `txtqty` if the validation was done at the view level for immediate feedback. The current approach defers item-level validation to the model's `clean` method, providing aggregate error messages from the view.
*   **Database IDs for `managed=False`:** For `managed=False` models, Django doesn't auto-generate PKs. You'll need to ensure your database handles auto-incrementing IDs for these tables, or explicitly assign IDs if they are managed externally.
*   **Security:** Always use Django's CSRF protection (`{% csrf_token %}`) and ensure proper input validation. The `DecimalField` and model `clean` methods provide good validation.
*   **Extensibility:** This structure allows for easy expansion. The "Clear" tab can be fleshed out with its own HTMX-driven DataTables for historical challans, and the concept can be applied to other modules.