This modernization plan details the transition of your ASP.NET Crystal Report viewer for "Transaction wise WIS" into a modern Django 5.0+ application. We will transform the complex C# data retrieval logic into efficient Django models and views, leveraging HTMX and Alpine.js for a highly interactive user experience, and DataTables for superior data presentation.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

## Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the ASP.NET code.

**Instructions:**
From the C# code, we identified several database interactions. The `fun.select` calls and `SqlDataAdapter.Fill` operations reveal the following tables and their key columns, which will be mapped to Django models using `managed = False` (meaning Django will not create or modify these tables, but rather interact with existing ones).

**Identified Tables and Columns:**

-   **`tblInv_WIS_Master`**:
    -   `Id` (primary key, likely string/GUID)
    -   `SessionId` (foreign key to employee ID)
    -   `SysDate` (date)
-   **`tblInv_WIS_Details`**:
    -   `MId` (foreign key to `tblInv_WIS_Master.Id`)
    -   `ItemId` (foreign key to `tblDG_Item_Master.Id`)
    -   `PId`, `CId` (additional identifiers)
    -   `IssuedQty` (numeric)
-   **`tblDG_Item_Master`**:
    -   `Id` (primary key, likely string/GUID)
    -   `ItemCode` (string)
    -   `ManfDesc` (string)
    -   `UOMBasic` (foreign key to `Unit_Master.Id`)
    -   `StockQty` (numeric)
-   **`Unit_Master`**:
    -   `Id` (primary key, likely string/GUID)
    -   `Symbol` (string)
-   **`tblHR_OfficeStaff`**:
    -   `EmpId` (primary key, likely string/GUID)
    -   `CompId` (integer, Company ID)
    -   `Title` (string)
    -   `EmployeeName` (string)
-   **`SD_Cust_WorkOrder_Master`**:
    -   `WONo` (primary key, likely string/GUID)
    -   `CompId` (integer, Company ID)
    -   `TaskTargetTryOut_FDate`, `TaskTargetTryOut_TDate`, `TaskTargetDespach_FDate`, `TaskTargetDespach_TDate` (dates)
    -   `TaskProjectTitle`, `TaskProjectLeader` (strings)
-   **Implicit/Helper Table (`tbl_Company_Master`)**:
    -   `CompanyName`, `Address` (for `fun.getCompany`, `fun.CompAdd`).

## Step 2: Identify Backend Functionality

**Task:** Determine the purpose of the ASP.NET code.

**Instructions:**
The ASP.NET page `WIS_View_TransWise_print.aspx` serves a singular purpose: displaying a "Transaction wise WIS" report. It is **not** a standard CRUD (Create, Read, Update, Delete) interface. Instead, it:

-   **Reads:** Gathers data from multiple related tables (`tblInv_WIS_Master`, `tblInv_WIS_Details`, `tblDG_Item_Master`, `Unit_Master`, `tblHR_OfficeStaff`, `SD_Cust_WorkOrder_Master`) based on query string parameters (`WISId`, `WISNo`, `wn`, `status`, `Key`) and session variables (`compid`, `finyear`).
-   **Aggregates:** Performs complex calculations and joins (e.g., summing `IssuedQty`, fetching BOM quantities via `fun.AllComponentBOMQty`).
-   **Presents:** Renders the aggregated data using a Crystal Report.
-   **Navigates:** Includes a "Cancel" button for redirection.

This page's core functionality is a **read-only data report generation**. There are no explicit create, update, or delete operations on the main entities from this particular page.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The ASP.NET page is lean on interactive UI controls, focusing primarily on report display.

-   **`CR:CrystalReportViewer`**: This is the central component, responsible for rendering the Crystal Report directly within the web page. In Django, this will be replaced by an HTML table (enhanced with DataTables) and potentially a PDF export option.
-   **`<asp:Button ID="Cancel"`**: A simple button that, when clicked, navigates the user back to a previous page (`WIS_View_TransWise.aspx`) with relevant parameters. This will be a standard link or button in Django.
-   **`loadingNotifier.js`**: A client-side JavaScript file, indicating a need for feedback during report loading. This can be handled with HTMX's built-in indicators or Alpine.js for UI state.

## Step 4: Generate Django Code

We will create a new Django application, perhaps named `inventory_transactions`, to house this functionality.

### 4.1 Models

**Task:** Create Django models based on the identified database schema. These models will reflect the existing database structure (`managed = False`). The business logic for aggregating report data will be implemented as a method within the `WisMaster` model, adhering to the "fat model" principle.

**Instructions:**
Define Django models for each identified database table. Crucially, we introduce a `dataclass` called `TransactionWiseWISReportData` to represent the *structure of a single row in the final report output*, separate from the database models themselves. This promotes clear separation between raw database entities and processed report data. We also include helper functions for date formatting and company information.

```python
# inventory_transactions/models.py
from django.db import models
from dataclasses import dataclass
from datetime import date, datetime

# --- Helper functions for date formatting (mimicking ASP.NET fun.FromDateDMY, fun.FromDate) ---
def format_date_dmy(dt):
    """Formats a date object to 'dd/MM/yyyy' string."""
    if isinstance(dt, datetime):
        return dt.strftime('%d/%m/%Y')
    elif isinstance(dt, date):
        return dt.strftime('%d/%m/%Y')
    return ''

def format_date(dt):
    """Formats a date object to 'MM/dd/yyyy' string."""
    if isinstance(dt, datetime):
        return dt.strftime('%m/%d/%Y')
    elif isinstance(dt, date):
        return dt.strftime('%m/%d/%Y')
    return ''

# --- Placeholder for Company and Address retrieval (mimicking fun.getCompany, fun.CompAdd) ---
class Company(models.Model):
    """
    Maps to tbl_Company_Master. Used for company-specific data.
    """
    id = models.IntegerField(db_column='CompId', primary_key=True) # Assuming CompId is primary key
    company_name = models.CharField(db_column='CompanyName', max_length=255, blank=True, null=True)
    address = models.TextField(db_column='Address', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tbl_Company_Master' # Placeholder table name for company info
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.company_name or f"Company {self.id}"

    @staticmethod
    def get_company_name(comp_id):
        try:
            return Company.objects.get(pk=comp_id).company_name
        except Company.DoesNotExist:
            return "Company Not Found"

    @staticmethod
    def get_company_address(comp_id):
        try:
            return Company.objects.get(pk=comp_id).address
        except Company.DoesNotExist:
            return "Address Not Found"

# --- Core Database Models (managed=False) ---
class WisMaster(models.Model):
    """
    Maps to tblInv_WIS_Master. Represents the main Work In progress Slip master record.
    """
    id = models.CharField(db_column='Id', primary_key=True, max_length=50) # Assuming Id is string/GUID
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True) # Likely EmpId from tblHR_OfficeStaff
    sys_date = models.DateField(db_column='SysDate', blank=True, null=True) # System Date of WIS creation

    class Meta:
        managed = False
        db_table = 'tblInv_WIS_Master'
        verbose_name = 'WIS Master'
        verbose_name_plural = 'WIS Masters'

    def __str__(self):
        return f"WIS Master {self.id}"

    # Business logic to get related report data (Fat Model approach)
    @staticmethod
    def get_transaction_wise_wis_report_data(wis_id: str, wis_no: str, wo_no: str, comp_id: int, fin_year_id: int) -> list['TransactionWiseWISReportData']:
        """
        Mimics the complex data aggregation logic from the ASP.NET Page_Init.
        Retrieves and processes data from multiple tables to form the WIS report.
        """
        report_items = []

        try:
            wis_master_obj = WisMaster.objects.get(id=wis_id)
        except WisMaster.DoesNotExist:
            return [] # No master record, no report data

        # Fetch related WIS Details and aggregate issued quantities
        # Using .select_related() and .prefetch_related() for efficiency if possible
        # For simplicity in this example, we'll simulate the join and aggregation as per ASP.NET logic.
        wis_details_qs = WisDetail.objects.filter(master=wis_master_obj).select_related('item__uom_basic')

        issued_qty_map = {}
        distinct_item_ids = set()
        for detail in wis_details_qs:
            if detail.item:
                issued_qty_map[detail.item.id] = issued_qty_map.get(detail.item.id, 0.0) + (detail.issued_qty or 0.0)
                distinct_item_ids.add(detail.item.id)

        # Get work order details
        wo_master = WorkOrderMaster.objects.filter(wo_no=wo_no, company_id=comp_id).first()

        # Get generated by employee info
        generated_by_staff = OfficeStaff.objects.filter(emp_id=wis_master_obj.session_id, company_id=comp_id).first()
        generated_by_name = f"{generated_by_staff.title}. {generated_by_staff.employee_name}" if generated_by_staff else "N/A"

        # Fetch all distinct item masters in one query for efficiency
        item_masters = ItemMaster.objects.filter(id__in=list(distinct_item_ids)).select_related('uom_basic')
        item_master_map = {item.id: item for item in item_masters}

        # Iterate through distinct items to build report rows
        for item_id in distinct_item_ids:
            item_master = item_master_map.get(item_id)
            if not item_master:
                continue

            # Mimic fun.GetItemCode_PartNo (can be a method on ItemMaster if complex)
            item_code_display = item_master.get_item_code_part_no(comp_id)

            # Mimic fun.AllComponentBOMQty (complex logic, likely a separate utility/manager method)
            bom_qty = ItemMaster.get_bom_quantity(comp_id, wo_no, item_id, fin_year_id)

            issued_qty_for_item = issued_qty_map.get(item_id, 0.0)

            # Get UOM Symbol
            uom_symbol = item_master.uom_basic.symbol if item_master.uom_basic else "N/A"

            report_items.append(TransactionWiseWISReportData(
                item_code=item_code_display,
                manf_desc=item_master.manf_desc or '',
                uom_basic=uom_symbol,
                wis_no=wis_no,
                bom_qty=bom_qty,
                issued_qty=round(issued_qty_for_item, 3), # Mimic N3 formatting
                generated_by=generated_by_name,
                company_id=comp_id,
                task_target_try_out_fdate=format_date_dmy(wo_master.task_target_try_out_fdate) if wo_master else '',
                task_target_try_out_tdate=format_date_dmy(wo_master.task_target_try_out_tdate) if wo_master else '',
                task_target_despatch_fdate=format_date_dmy(wo_master.task_target_despatch_fdate) if wo_master else '',
                task_target_despatch_tdate=format_date_dmy(wo_master.task_target_despatch_tdate) if wo_master else '',
                task_project_title=wo_master.task_project_title if wo_master else '',
                task_project_leader=wo_master.task_project_leader if wo_master else '',
                sys_date=format_date(wis_master_obj.sys_date) if wis_master_obj.sys_date else '',
                wo_no=wo_no,
                stock_qty=item_master.stock_qty or 0.0
            ))
        
        # Sort by ItemCode, mimicking `dv.Sort = "ItemCode"`
        report_items.sort(key=lambda x: x.item_code)

        return report_items


class WisDetail(models.Model):
    """
    Maps to tblInv_WIS_Details.
    """
    # Assuming MId and ItemId form a composite primary key, or an auto-incrementing ID exists.
    # For simplicity, Django will add an 'id' field if not explicitly defined.
    # If a composite PK is truly intended and no other PK, one would use:
    # class Meta: primary_key = ('MId', 'ItemId')
    master = models.ForeignKey(WisMaster, on_delete=models.CASCADE, db_column='MId', related_name='details')
    item = models.ForeignKey('ItemMaster', on_delete=models.CASCADE, db_column='ItemId', related_name='wis_details')
    pid = models.CharField(db_column='PId', max_length=50, blank=True, null=True)
    cid = models.CharField(db_column='CId', max_length=50, blank=True, null=True)
    issued_qty = models.FloatField(db_column='IssuedQty', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_WIS_Details'
        verbose_name = 'WIS Detail'
        verbose_name_plural = 'WIS Details'
        # Example if composite PK: unique_together = (('master', 'item'),)


class ItemMaster(models.Model):
    """
    Maps to tblDG_Item_Master.
    """
    id = models.CharField(db_column='Id', primary_key=True, max_length=50) # Assuming Id is string/GUID
    item_code = models.CharField(db_column='ItemCode', max_length=255, blank=True, null=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True)
    uom_basic = models.ForeignKey('UnitMaster', on_delete=models.SET_NULL, db_column='UOMBasic', blank=True, null=True)
    stock_qty = models.FloatField(db_column='StockQty', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.item_code or self.manf_desc or f"Item {self.id}"

    @staticmethod
    def get_bom_quantity(comp_id: int, wo_no: str, item_id: str, fin_year_id: int) -> float:
        """
        Placeholder for fun.AllComponentBOMQty logic.
        This would involve complex queries related to BOM tables (Bill of Materials).
        In a real system, this could query other tables or a stored procedure.
        """
        # Example: return a dummy value as the actual logic is external
        return 10.0 # Dummy value for demonstration

    def get_item_code_part_no(self, comp_id: int) -> str:
        """
        Placeholder for fun.GetItemCode_PartNo logic.
        If this is just a direct lookup, it's self.item_code.
        If it implies specific formatting or complex logic, it would be here.
        """
        return self.item_code or f"Item {self.id}" # For now, assumes direct lookup


class UnitMaster(models.Model):
    """
    Maps to Unit_Master.
    """
    id = models.CharField(db_column='Id', primary_key=True, max_length=50) # Assuming Id is string/GUID
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit Master'
        verbose_name_plural = 'Unit Masters'

    def __str__(self):
        return self.symbol or f"Unit {self.id}"


class OfficeStaff(models.Model):
    """
    Maps to tblHR_OfficeStaff.
    """
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50) # Assuming EmpId is string/GUID
    company_id = models.IntegerField(db_column='CompId')
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title or ''}. {self.employee_name or ''}"


class WorkOrderMaster(models.Model):
    """
    Maps to SD_Cust_WorkOrder_Master.
    """
    wo_no = models.CharField(db_column='WONo', primary_key=True, max_length=50) # Assuming WONo is PK
    company_id = models.IntegerField(db_column='CompId')
    task_target_try_out_fdate = models.DateField(db_column='TaskTargetTryOut_FDate', blank=True, null=True)
    task_target_try_out_tdate = models.DateField(db_column='TaskTargetTryOut_TDate', blank=True, null=True)
    task_target_despatch_fdate = models.DateField(db_column='TaskTargetDespach_FDate', blank=True, null=True)
    task_target_despatch_tdate = models.DateField(db_column='TaskTargetDespach_TDate', blank=True, null=True)
    task_project_title = models.CharField(db_column='TaskProjectTitle', max_length=255, blank=True, null=True)
    task_project_leader = models.CharField(db_column='TaskProjectLeader', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order Master'
        verbose_name_plural = 'Work Order Masters'

    def __str__(self):
        return self.wo_no or f"Work Order {self.pk}"


# --- Report Data Structure (Conceptual Model for the report output) ---
@dataclass
class TransactionWiseWISReportData:
    """
    Represents a single row of the Transaction wise WIS report.
    This is a conceptual data structure (dataclass), not a Django ORM model linked to a DB table.
    It defines the shape of the data that will be displayed in the report table.
    """
    item_code: str
    manf_desc: str
    uom_basic: str
    wis_no: str
    bom_qty: float
    issued_qty: float
    generated_by: str
    company_id: int
    task_target_try_out_fdate: str # Formatted date string
    task_target_try_out_tdate: str # Formatted date string
    task_target_despatch_fdate: str # Formatted date string
    task_target_despatch_tdate: str # Formatted date string
    task_project_title: str
    task_project_leader: str
    sys_date: str # Formatted date string
    wo_no: str
    stock_qty: float

```

### 4.2 Forms

**Task:** Since the ASP.NET page is a report viewer and not a data entry or CRUD form, a traditional Django `ModelForm` is not directly applicable to *this specific page*. However, adhering to the broader requirement for `[MODEL_NAME]Form`, we will provide a basic form for the primary entity `WisMaster`, should a CRUD interface for it be developed later in the application.

**Instructions:**
Define a `ModelForm` for `WisMaster`. This demonstrates how a form would be structured for this model, even though this particular report view doesn't use it.

```python
# inventory_transactions/forms.py
from django import forms
from .models import WisMaster

class WisMasterForm(forms.ModelForm):
    """
    A sample form for WisMaster.
    Note: This specific report page does not use a form.
    This is provided to fulfill the general requirement of generating a form for [MODEL_NAME].
    """
    class Meta:
        model = WisMaster
        fields = ['id', 'session_id', 'sys_date']
        widgets = {
            'id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'WIS ID (e.g., GUID)'}),
            'session_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Employee Session ID'}),
            'sys_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
        }
        labels = {
            'id': 'WIS ID',
            'session_id': 'Generated By (Session ID)',
            'sys_date': 'System Date',
        }
        
    def clean_id(self):
        wis_id = self.cleaned_data['id']
        # Example validation: ensure ID is unique for new objects if this is a CreateForm
        if self.instance.pk is None and WisMaster.objects.filter(id=wis_id).exists():
            raise forms.ValidationError("This WIS ID already exists.")
        return wis_id
```

### 4.3 Views

**Task:** Implement a Django Class-Based View (CBV) to handle the display of the Transaction wise WIS report. This view will retrieve parameters, fetch data using the `WisMaster` model's specialized method, and render the report. We also include a separate view for the HTMX-loaded table partial and provide basic CRUD views for `WisMaster` for completeness as per template instructions.

**Instructions:**
Define `TransactionWiseWISReportView` (TemplateView) for the main report page. Define `TransactionWiseWISReportTableView` (ListView-like, to render the partial table). Additionally, provide standard CRUD views for `WisMaster` as requested by the template structure, even if they aren't directly linked to the original ASP.NET page's functionality.

```python
# inventory_transactions/views.py
from django.views.generic import TemplateView, ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import get_object_or_404
from .models import WisMaster, Company, TransactionWiseWISReportData # Import all relevant models and dataclass
from .forms import WisMasterForm # Import the form for WisMaster

# --- Report-Specific Views ---
class TransactionWiseWISReportView(TemplateView):
    """
    Main view to display the Transaction wise WIS report.
    Fetches parameters from the URL and calls the model to get report data.
    """
    template_name = 'inventory_transactions/transactionwisreport/detail.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Extract parameters from query string, mimicking ASP.NET Request.QueryString
        wis_id = self.request.GET.get('WISId', '')
        wis_no = self.request.GET.get('WISNo', '')
        wo_no = self.request.GET.get('wn', '')
        # Assuming compid and finyear are available via session or user profile
        comp_id = int(self.request.session.get('compid', 1)) # Default to 1 if not in session
        fin_year_id = int(self.request.session.get('finyear', 2023)) # Default to 2023

        # Add context for header (Company Name, Address)
        context['company_name'] = Company.get_company_name(comp_id)
        context['company_address'] = Company.get_company_address(comp_id)
        context['wis_no'] = wis_no # Pass WISNo to template for display

        # The actual report data will be loaded via HTMX into a partial
        # We only pass necessary identifiers for the HTMX call
        context['wis_id'] = wis_id
        context['wo_no'] = wo_no
        context['comp_id'] = comp_id
        context['fin_year_id'] = fin_year_id

        return context

class TransactionWiseWISReportTableView(ListView):
    """
    HTMX-specific view to render only the DataTables table for the report.
    This keeps the main view thin and allows for dynamic reloading of the table.
    """
    template_name = 'inventory_transactions/transactionwisreport/_report_table.html'
    context_object_name = 'report_items' # Renamed to better reflect report data

    def get_queryset(self):
        # Retrieve parameters from GET request (passed by HTMX)
        wis_id = self.request.GET.get('WISId', '')
        wis_no = self.request.GET.get('WISNo', '')
        wo_no = self.request.GET.get('wn', '')
        comp_id = int(self.request.GET.get('CompId', self.request.session.get('compid', 1)))
        fin_year_id = int(self.request.GET.get('FinYearId', self.request.session.get('finyear', 2023)))

        # Call the fat model method to get the aggregated report data
        # This is where the complex data processing from ASP.NET C# is encapsulated.
        report_data = WisMaster.get_transaction_wise_wis_report_data(
            wis_id=wis_id,
            wis_no=wis_no,
            wo_no=wo_no,
            comp_id=comp_id,
            fin_year_id=fin_year_id
        )
        return report_data
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Add headers for the DataTables table based on TransactionWiseWISReportData fields
        context['headers'] = [
            'Item Code', 'Manufacture Description', 'UOM', 'WIS No.', 'BOM Qty',
            'Issued Qty', 'Generated By', 'Company ID', 'Try Out F Date', 'Try Out T Date',
            'Despatch F Date', 'Despatch T Date', 'Project Title', 'Project Leader',
            'System Date', 'Work Order No.', 'Stock Qty'
        ]
        return context

def cancel_wis_report_view(request):
    """
    Handles the 'Cancel' button click, redirecting to the previous WIS view page.
    Mimics ASP.NET Response.Redirect("~/Module/Inventory/Transactions/WIS_View_TransWise.aspx...")
    """
    wo_no = request.GET.get('wn', '')
    status = request.GET.get('status', '') # Assuming status is an integer but passed as string
    
    # Construct the URL for the previous page.
    # In a real Django app, this would be a reverse() call to a known URL.
    # For now, mimicking the direct redirect from ASP.NET.
    redirect_url = reverse_lazy('wis_transaction_list') # Placeholder for actual list view
    return HttpResponseRedirect(f"{redirect_url}?WONo={wo_no}&ModId=9&SubModId=53&status={status}")

# --- General CRUD Views for WisMaster (as per template instructions) ---
# Note: These views are for general WisMaster management, not directly from the
# ASP.NET Crystal Report page. They are provided for completeness based on the template.

class WisMasterListView(ListView):
    model = WisMaster
    template_name = 'inventory_transactions/wismaster/list.html'
    context_object_name = 'wismasters' # Changed from wis_master_plural_lower

class WisMasterCreateView(CreateView):
    model = WisMaster
    form_class = WisMasterForm
    template_name = 'inventory_transactions/wismaster/form.html'
    success_url = reverse_lazy('wismaster_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'WIS Master added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWisMasterList'
                }
            )
        return response

class WisMasterUpdateView(UpdateView):
    model = WisMaster
    form_class = WisMasterForm
    template_name = 'inventory_transactions/wismaster/form.html'
    success_url = reverse_lazy('wismaster_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'WIS Master updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWisMasterList'
                }
            )
        return response

class WisMasterDeleteView(DeleteView):
    model = WisMaster
    template_name = 'inventory_transactions/wismaster/confirm_delete.html'
    success_url = reverse_lazy('wismaster_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'WIS Master deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWisMasterList'
                }
            )
        return response

```

### 4.4 Templates

**Task:** Create templates for the report view and the HTMX-loaded table partial. Since this is a report and not a CRUD interface, the forms and delete confirmation templates will be provided only for the generic `WisMaster` CRUD functionality outlined in the general template instructions.

**Instructions:**
-   `inventory_transactions/transactionwisreport/detail.html`: The main report page, setting up the overall layout and triggering the HTMX load for the report table.
-   `inventory_transactions/transactionwisreport/_report_table.html`: The partial template for the DataTables report table.
-   `inventory_transactions/wismaster/list.html`: Standard list view for `WisMaster` (general CRUD).
-   `inventory_transactions/wismaster/_wismaster_table.html`: Partial for `WisMaster` list.
-   `inventory_transactions/wismaster/form.html`: Form for `WisMaster` create/update.
-   `inventory_transactions/wismaster/confirm_delete.html`: Confirmation for `WisMaster` delete.

**File: `inventory_transactions/templates/inventory_transactions/transactionwisreport/detail.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div class="mb-4 md:mb-0">
            <h2 class="text-3xl font-extrabold text-gray-900 mb-2">Transaction Wise WIS Report</h2>
            <p class="text-lg text-gray-600">For WIS No.: <strong>{{ wis_no }}</strong></p>
        </div>
        <div class="text-right">
            <p class="text-gray-700 font-semibold">{{ company_name }}</p>
            <p class="text-sm text-gray-600">{{ company_address }}</p>
        </div>
    </div>

    <div class="bg-white shadow-lg rounded-lg p-6 mb-8">
        <h3 class="text-xl font-semibold text-gray-800 mb-4">Report Details</h3>
        <div id="wisReportTable-container"
             hx-trigger="load, refreshWisReport from:body"
             hx-get="{% url 'transactionwisreport_table' %}?WISId={{ wis_id }}&WISNo={{ wis_no }}&wn={{ wo_no }}&CompId={{ comp_id }}&FinYearId={{ fin_year_id }}"
             hx-swap="innerHTML"
             class="min-h-[200px] flex items-center justify-center">
            <!-- Loading indicator for HTMX -->
            <div class="text-center">
                <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                <p class="mt-4 text-gray-600">Loading report data...</p>
            </div>
        </div>
    </div>

    <div class="flex justify-end mt-8">
        <a href="{% url 'cancel_wis_report' %}?wn={{ wo_no }}&status={{ request.GET.status|default:'' }}"
           class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
            Cancel
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js component initialization if needed for more complex UI states.
    // For this simple report view, HTMX handles most interactions.
    document.addEventListener('alpine:init', () => {
        Alpine.data('reportViewer', () => ({
            // No specific state needed for this view with current requirements.
            // Add if filtering, sorting, or other UI states are introduced client-side.
        }));
    });
</script>
{% endblock %}
```

**File: `inventory_transactions/templates/inventory_transactions/transactionwisreport/_report_table.html`**
```html
<table id="wisReportTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr class="bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
            <th class="py-3 px-4 border-b border-gray-200">SN</th>
            {% for header in headers %}
            <th class="py-3 px-4 border-b border-gray-200">{{ header }}</th>
            {% endfor %}
        </tr>
    </thead>
    <tbody>
        {% for item in report_items %}
        <tr class="border-b border-gray-200 hover:bg-gray-50">
            <td class="py-3 px-4">{{ forloop.counter }}</td>
            <td class="py-3 px-4 text-sm text-gray-800">{{ item.item_code }}</td>
            <td class="py-3 px-4 text-sm text-gray-800">{{ item.manf_desc }}</td>
            <td class="py-3 px-4 text-sm text-gray-800">{{ item.uom_basic }}</td>
            <td class="py-3 px-4 text-sm text-gray-800">{{ item.bom_qty|floatformat:"2" }}</td>
            <td class="py-3 px-4 text-sm text-gray-800">{{ item.issued_qty|floatformat:"3" }}</td>
            <td class="py-3 px-4 text-sm text-gray-800">{{ item.generated_by }}</td>
            <td class="py-3 px-4 text-sm text-gray-800">{{ item.company_id }}</td>
            <td class="py-3 px-4 text-sm text-gray-800">{{ item.task_target_try_out_fdate }}</td>
            <td class="py-3 px-4 text-sm text-gray-800">{{ item.task_target_try_out_tdate }}</td>
            <td class="py-3 px-4 text-sm text-gray-800">{{ item.task_target_despatch_fdate }}</td>
            <td class="py-3 px-4 text-sm text-gray-800">{{ item.task_target_despatch_tdate }}</td>
            <td class="py-3 px-4 text-sm text-gray-800">{{ item.task_project_title }}</td>
            <td class="py-3 px-4 text-sm text-gray-800">{{ item.task_project_leader }}</td>
            <td class="py-3 px-4 text-sm text-gray-800">{{ item.sys_date }}</td>
            <td class="py-3 px-4 text-sm text-gray-800">{{ item.wo_no }}</td>
            <td class="py-3 px-4 text-sm text-gray-800">{{ item.stock_qty|floatformat:"2" }}</td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="18" class="py-4 px-4 text-center text-gray-600">No report data available for the given criteria.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// DataTables initialization
$(document).ready(function() {
    $('#wisReportTable').DataTable({
        "pageLength": 10, // Default number of rows per page
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]], // Options for rows per page
        "ordering": true, // Enable sorting
        "searching": true, // Enable search box
        "paging": true, // Enable pagination
        "info": true // Show info about number of entries
    });
});
</script>
```

**File: `inventory_transactions/templates/inventory_transactions/wismaster/list.html`** (General `WisMaster` CRUD List)
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">WIS Masters</h2>
        <button
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'wismaster_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New WIS Master
        </button>
    </div>

    <div id="wismasterTable-container"
         hx-trigger="load, refreshWisMasterList from:body"
         hx-get="{% url 'wismaster_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading...</p>
        </div>
    </div>

    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed
    });
</script>
{% endblock %}
```

**File: `inventory_transactions/templates/inventory_transactions/wismaster/_wismaster_table.html`** (Partial for `WisMaster` list)
```html
<table id="wismasterTable" class="min-w-full bg-white">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WIS ID</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Session ID</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">System Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in wismasters %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.id }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.session_id }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.sys_date|date:"Y-m-d" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'wismaster_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'wismaster_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    $('#wismasterTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
    });
});
</script>
```

**File: `inventory_transactions/templates/inventory_transactions/wismaster/form.html`** (Form for `WisMaster` CRUD)
```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} WIS Master</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}

        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**File: `inventory_transactions/templates/inventory_transactions/wismaster/confirm_delete.html`** (Confirmation for `WisMaster` CRUD)
```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete WIS Master with ID: <strong>{{ object.id }}</strong>?</p>
    <form hx-post="{% url 'wismaster_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs

**Task:** Define URL patterns for both the Transaction wise WIS report views and the general `WisMaster` CRUD operations.

**Instructions:**
Create paths that are clear, concise, and follow Django's URL routing conventions.

```python
# inventory_transactions/urls.py
from django.urls import path
from .views import (
    TransactionWiseWISReportView, TransactionWiseWISReportTableView, cancel_wis_report_view,
    WisMasterListView, WisMasterCreateView, WisMasterUpdateView, WisMasterDeleteView,
)
from django.views.generic import TemplateView # For the _wismaster_table partial


urlpatterns = [
    # URLs for the Transaction Wise WIS Report
    path('wis-report/', TransactionWiseWISReportView.as_view(), name='transactionwisreport_detail'),
    path('wis-report/table/', TransactionWiseWISReportTableView.as_view(), name='transactionwisreport_table'),
    path('wis-report/cancel/', cancel_wis_report_view, name='cancel_wis_report'), # for the cancel button

    # URLs for general WIS Master CRUD operations (as per template requirement)
    path('wismasters/', WisMasterListView.as_view(), name='wismaster_list'),
    path('wismasters/add/', WisMasterCreateView.as_view(), name='wismaster_add'),
    path('wismasters/edit/<str:pk>/', WisMasterUpdateView.as_view(), name='wismaster_edit'), # PK is string
    path('wismasters/delete/<str:pk>/', WisMasterDeleteView.as_view(), name='wismaster_delete'), # PK is string
    # HTMX partial for the WIS Master list table
    path('wismasters/table/', WisMasterListView.as_view(template_name='inventory_transactions/wismaster/_wismaster_table.html'), name='wismaster_table'),
]

```

### 4.6 Tests

**Task:** Write comprehensive tests for the models and views to ensure functionality and data integrity.

**Instructions:**
Include unit tests for model methods (especially the report data aggregation) and integration tests for all views (report display, HTMX interactions, and general CRUD).

```python
# inventory_transactions/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from datetime import date
from .models import (
    WisMaster, WisDetail, ItemMaster, UnitMaster, OfficeStaff, WorkOrderMaster, Company,
    TransactionWiseWISReportData, format_date_dmy, format_date
)

# Helper for creating test data
def create_test_data():
    Company.objects.create(id=1, company_name="Test Company", address="123 Test St")
    unit_pcs = UnitMaster.objects.create(id='PCS', symbol='PCS')
    item_a = ItemMaster.objects.create(id='ITEM001', item_code='IA001', manf_desc='Item A Desc', uom_basic=unit_pcs, stock_qty=100.0)
    item_b = ItemMaster.objects.create(id='ITEM002', item_code='IB002', manf_desc='Item B Desc', uom_basic=unit_pcs, stock_qty=50.0)
    staff = OfficeStaff.objects.create(emp_id='EMP001', company_id=1, title='Mr', employee_name='John Doe')
    wo = WorkOrderMaster.objects.create(
        wo_no='WO001', company_id=1,
        task_target_try_out_fdate=date(2023, 1, 1), task_target_try_out_tdate=date(2023, 1, 5),
        task_target_despatch_fdate=date(2023, 1, 6), task_target_despatch_tdate=date(2023, 1, 10),
        task_project_title='Project Alpha', task_project_leader='Jane Smith'
    )
    wis_master = WisMaster.objects.create(id='WIS001', session_id='EMP001', sys_date=date(2023, 1, 15))
    WisDetail.objects.create(master=wis_master, item=item_a, issued_qty=10.0)
    WisDetail.objects.create(master=wis_master, item=item_a, issued_qty=5.0) # For aggregation test
    WisDetail.objects.create(master=wis_master, item=item_b, issued_qty=20.0)
    return {
        'company': Company.objects.get(id=1),
        'unit': unit_pcs,
        'item_a': item_a,
        'item_b': item_b,
        'staff': staff,
        'wo': wo,
        'wis_master': wis_master,
    }


# Mock the external BOM quantity function for testing purposes
@patch('inventory_transactions.models.ItemMaster.get_bom_quantity', return_value=12.5)
@patch('inventory_transactions.models.ItemMaster.get_item_code_part_no', side_effect=lambda self, comp_id: self.item_code)
class WisMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.test_data = create_test_data()

    def test_wis_master_creation(self, mock_get_item_code_part_no, mock_get_bom_quantity):
        wis_master = WisMaster.objects.get(id='WIS001')
        self.assertEqual(wis_master.session_id, 'EMP001')
        self.assertEqual(wis_master.sys_date, date(2023, 1, 15))
        self.assertEqual(str(wis_master), 'WIS Master WIS001')

    def test_report_data_generation(self, mock_get_item_code_part_no, mock_get_bom_quantity):
        wis_id = 'WIS001'
        wis_no = 'WIS/2023/001'
        wo_no = 'WO001'
        comp_id = 1
        fin_year_id = 2023

        report_data = WisMaster.get_transaction_wise_wis_report_data(
            wis_id, wis_no, wo_no, comp_id, fin_year_id
        )

        self.assertEqual(len(report_data), 2) # Should have two distinct items

        # Check data for Item A (total issued_qty should be 10.0 + 5.0 = 15.0)
        item_a_data = next((item for item in report_data if item.item_code == 'IA001'), None)
        self.assertIsNotNone(item_a_data)
        self.assertEqual(item_a_data.issued_qty, 15.0)
        self.assertEqual(item_a_data.bom_qty, 12.5) # From mock
        self.assertEqual(item_a_data.wis_no, wis_no)
        self.assertEqual(item_a_data.generated_by, 'Mr. John Doe')
        self.assertEqual(item_a_data.task_project_title, 'Project Alpha')
        self.assertEqual(item_a_data.sys_date, format_date(date(2023, 1, 15)))
        self.assertEqual(item_a_data.uom_basic, 'PCS')
        self.assertEqual(item_a_data.stock_qty, 100.0)

        # Check data for Item B
        item_b_data = next((item for item in report_data if item.item_code == 'IB002'), None)
        self.assertIsNotNone(item_b_data)
        self.assertEqual(item_b_data.issued_qty, 20.0)
        self.assertEqual(item_b_data.stock_qty, 50.0)

        # Verify sorting
        self.assertEqual(report_data[0].item_code, 'IA001')
        self.assertEqual(report_data[1].item_code, 'IB002')


class ReportViewTests(TestCase):
    client = Client()

    @classmethod
    def setUpTestData(cls):
        cls.test_data = create_test_data()

    @patch('inventory_transactions.models.WisMaster.get_transaction_wise_wis_report_data')
    @patch('inventory_transactions.models.Company.get_company_name', return_value="Mock Company")
    @patch('inventory_transactions.models.Company.get_company_address', return_value="Mock Address")
    def test_wis_report_detail_view(self, mock_get_company_address, mock_get_company_name, mock_get_report_data):
        # Mock the report data for consistent testing
        mock_get_report_data.return_value = [
            TransactionWiseWISReportData(
                item_code='IA001', manf_desc='Item A', uom_basic='PCS', wis_no='WIS/001',
                bom_qty=10.0, issued_qty=5.0, generated_by='Mr. Tester', company_id=1,
                task_target_try_out_fdate='01/01/2023', task_target_try_out_tdate='05/01/2023',
                task_target_despatch_fdate='06/01/2023', task_target_despatch_tdate='10/01/2023',
                task_project_title='Test Project', task_project_leader='Test Leader',
                sys_date='01/15/2023', wo_no='WO/001', stock_qty=100.0
            )
        ]

        # Simulate session variables
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2023
        session.save()

        response = self.client.get(reverse('transactionwisreport_detail'), {
            'WISId': 'WIS001',
            'WISNo': 'WIS/2023/001',
            'wn': 'WO001',
            'status': '1',
            'Key': 'test_key'
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/transactionwisreport/detail.html')
        self.assertContains(response, 'Transaction Wise WIS Report')
        self.assertContains(response, 'WIS/2023/001')
        self.assertContains(response, 'Mock Company')
        self.assertContains(response, 'Mock Address')
        self.assertContains(response, 'id="wisReportTable-container"') # Check if HTMX container is present
        mock_get_report_data.assert_called_once() # Ensure the data method was called

    @patch('inventory_transactions.models.WisMaster.get_transaction_wise_wis_report_data')
    def test_wis_report_table_partial_view_htmx(self, mock_get_report_data):
        mock_get_report_data.return_value = [
            TransactionWiseWISReportData(
                item_code='IA001', manf_desc='Item A', uom_basic='PCS', wis_no='WIS/001',
                bom_qty=10.0, issued_qty=5.0, generated_by='Mr. Tester', company_id=1,
                task_target_try_out_fdate='01/01/2023', task_target_try_out_tdate='05/01/2023',
                task_target_despatch_fdate='06/01/2023', task_target_despatch_tdate='10/01/2023',
                task_project_title='Test Project', task_project_leader='Test Leader',
                sys_date='01/15/2023', wo_no='WO/001', stock_qty=100.0
            )
        ]

        # Simulate session variables and HTMX request
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2023
        session.save()

        response = self.client.get(reverse('transactionwisreport_table'), {
            'WISId': 'WIS001',
            'WISNo': 'WIS/2023/001',
            'wn': 'WO001',
            'CompId': '1',
            'FinYearId': '2023'
        }, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/transactionwisreport/_report_table.html')
        self.assertContains(response, 'id="wisReportTable"') # Check if table is present
        self.assertContains(response, 'IA001') # Check if data is rendered
        mock_get_report_data.assert_called_once_with(
            wis_id='WIS001', wis_no='WIS/2023/001', wo_no='WO001', comp_id=1, fin_year_id=2023
        )


    def test_cancel_wis_report_view(self):
        # Test redirect
        response = self.client.get(reverse('cancel_wis_report'), {
            'wn': 'WO001_Test',
            'status': '2'
        })
        self.assertEqual(response.status_code, 302) # Should redirect
        self.assertEqual(response.url, '/wismasters/?WONo=WO001_Test&ModId=9&SubModId=53&status=2') # Checks the redirect URL


class WisMasterCRUDViewsTest(TestCase):
    client = Client()

    @classmethod
    def setUpTestData(cls):
        # Create initial data for CRUD tests
        cls.test_wis = WisMaster.objects.create(id='WIS002', session_id='EMP002', sys_date=date(2023, 2, 1))

    def test_list_view(self):
        response = self.client.get(reverse('wismaster_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/wismaster/list.html')
        self.assertContains(response, 'WIS Masters')
        self.assertContains(response, self.test_wis.id)

    def test_create_view_get(self):
        response = self.client.get(reverse('wismaster_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/wismaster/form.html')
        self.assertContains(response, 'Add WIS Master')

    def test_create_view_post(self):
        data = {
            'id': 'WIS003',
            'session_id': 'EMP003',
            'sys_date': '2023-03-01'
        }
        response = self.client.post(reverse('wismaster_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success -> No Content
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWisMasterList')
        self.assertTrue(WisMaster.objects.filter(id='WIS003').exists())

    def test_update_view_get(self):
        response = self.client.get(reverse('wismaster_edit', args=[self.test_wis.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/wismaster/form.html')
        self.assertContains(response, 'Edit WIS Master')
        self.assertContains(response, self.test_wis.id)

    def test_update_view_post(self):
        data = {
            'id': self.test_wis.id,
            'session_id': 'EMP002_Updated',
            'sys_date': '2023-02-02'
        }
        response = self.client.post(reverse('wismaster_edit', args=[self.test_wis.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWisMasterList')
        self.test_wis.refresh_from_db()
        self.assertEqual(self.test_wis.session_id, 'EMP002_Updated')

    def test_delete_view_get(self):
        response = self.client.get(reverse('wismaster_delete', args=[self.test_wis.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/wismaster/confirm_delete.html')
        self.assertContains(response, 'Confirm Delete')
        self.assertContains(response, self.test_wis.id)

    def test_delete_view_post(self):
        wis_to_delete_id = WisMaster.objects.create(id='WIS004', session_id='EMP004', sys_date=date(2023, 4, 1)).id
        response = self.client.post(reverse('wismaster_delete', args=[wis_to_delete_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWisMasterList')
        self.assertFalse(WisMaster.objects.filter(id=wis_to_delete_id).exists())

```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**
-   **HTMX for dynamic updates:**
    -   The main report view (`detail.html`) uses `hx-get` on a container to load the `_report_table.html` partial via `hx-trigger="load"`. This ensures the table loads dynamically after the page itself.
    -   `hx-trigger="refreshWisReport from:body"` on the container ensures the table can be reloaded by custom events (e.g., if a filter form were added).
    -   For CRUD operations on `WisMaster` (in `list.html`), `hx-get` is used to load `form.html` and `confirm_delete.html` into a modal.
    -   Form submissions use `hx-post` with `hx-swap="none"` and `HX-Trigger` headers to send success/error signals back to the client without full page reloads, triggering a `refreshWisMasterList` event on the body to update the main list.
-   **Alpine.js for UI state management:**
    -   Alpine.js is used for simple UI state, such as showing/hiding the modal (`x-data`, `x-show`, `x-transition`). The `_="on click add .is-active to #modal"` syntax for HTMX is a convenient way to integrate with Alpine/CSS classes.
    -   The modal setup (backdrop, content container) is Alpine-managed.
-   **DataTables for list views and data presentation:**
    -   The `_report_table.html` and `_wismaster_table.html` partials include a `<script>` tag that initializes DataTables on the rendered `<table>` element. This handles client-side searching, sorting, and pagination without requiring additional backend logic beyond providing the raw data.
-   **No custom JavaScript requirements:**
    -   All dynamic interactions are achieved using HTMX and Alpine.js. No separate custom `.js` files are required beyond the CDN links for HTMX, Alpine.js, jQuery, and DataTables (which are assumed to be in `base.html`).

## Final Notes

This comprehensive plan provides a robust framework for migrating the "Transaction wise WIS Print" functionality from ASP.NET to Django.

-   **Business Logic in Models:** The complex data aggregation previously in the C# code-behind is now encapsulated within the `WisMaster` model's `get_transaction_wise_wis_report_data` static method. This maintains the "fat model, thin view" principle.
-   **Clean Views:** Views are kept concise (typically 5-15 lines) by delegating data retrieval and processing to models.
-   **DRY Templates:** HTML templates utilize Django's template inheritance and partials for reusability, preventing code duplication.
-   **Modern Frontend:** Exclusive use of HTMX and Alpine.js delivers a highly responsive, single-page application (SPA)-like experience without the overhead of a full JavaScript framework.
-   **Data Presentation:** DataTables provides powerful client-side features for the tabular report, enhancing usability.
-   **Automation Focus:** The structured output and clear division of responsibilities make this plan highly amenable to AI-assisted code generation and automated deployment pipelines, minimizing manual intervention and accelerating the migration process.
-   **Test Coverage:** Included unit and integration tests ensure the reliability and correctness of the migrated functionality.

This approach not only replicates the original functionality but significantly modernizes it, providing a more maintainable, scalable, and user-friendly solution.