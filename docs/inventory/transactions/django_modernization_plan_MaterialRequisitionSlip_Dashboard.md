## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code primarily interacts with `tblinv_MaterialRequisition_Temp` for cleanup purposes. This suggests a temporary staging table. While a "dashboard" would typically read from a main `MaterialRequisition` table, given the provided code's scope, we will focus on the `tblinv_MaterialRequisition_Temp` table. The existing code uses `CompId` (Company ID) and `SessionId` (User Session ID/Username) to filter temporary records. For a comprehensive example, we'll infer additional common fields for a temporary requisition item.

**Inferred Database Schema for `tblinv_MaterialRequisition_Temp`:**
*   `id`: Primary Key (auto-incrementing, integer)
*   `CompId`: Integer, corresponding to the company.
*   `SessionId`: String, corresponding to the user's session or username.
*   `RequisitionNo`: String, optional, for when a temporary item might be linked to a requisition number.
*   `ItemCode`: String, the code for the item being requisitioned.
*   `Quantity`: Decimal, the quantity of the item.
*   `UOM`: String, Unit of Measure (e.g., 'Nos', 'Kg').
*   `Remarks`: Text, optional, for any comments.
*   `IsApproved`: Boolean, default False, to track temporary approval status.
*   `CreatedAt`: DateTime, for tracking when the entry was created, useful for age-based cleanup.

### Step 2: Identify Backend Functionality

**Analysis:**
The core functionality in the provided ASP.NET code-behind is:
*   **Delete**: `DELETE FROM tblinv_MaterialRequisition_Temp WHERE CompId = 'X' AND SessionId = 'Y'`. This is a cleanup operation executed on `Page_Load`.
*   **Session Management**: Retrieval of `username` and `compid` from ASP.NET Session.

**Django Translation:**
*   **Cleanup**: We will implement a dedicated "cleanup" endpoint in Django, rather than doing it automatically on page load (which is generally discouraged for destructive operations). This allows for explicit user action or integration with scheduled tasks.
*   **Session/User Management**: Django's built-in authentication system (`request.user`) will handle the `username`. `CompId` would typically come from a user profile or organization model linked to the user. For this example, we'll assume `comp_id` can be retrieved from `request.session` for demonstration purposes, or from a related user object.
*   **Dashboard Implication**: Although the ASP.NET code only showed deletion, a "dashboard" implies displaying data. Therefore, we will also implement **Create, Read (List/Detail), Update** operations for `MaterialRequisitionTemp` entries to make the dashboard functional.

### Step 3: Infer UI Components

**Analysis:**
The ASP.NET `.aspx` file showed content placeholders and `loadingNotifier.js`. No actual UI controls were present in the snippet.

**Django Approach (Standard Dashboard Components):**
*   **List View**: A main page displaying a list of `MaterialRequisitionTemp` entries using DataTables for client-side functionality.
*   **CRUD Forms**: Modals triggered by HTMX for adding, editing, and confirming deletion of entries.
*   **Dynamic Loading**: HTMX for lazy loading tables and forms, and for submitting data without full page reloads.
*   **UI State**: Alpine.js for modal visibility and other minor client-side UI manipulations.
*   **Cleanup Button**: A button to trigger the temporary data cleanup.

### Step 4: Generate Django Code

We will create a Django app named `inventory` to house this functionality.

#### 4.1 Models (`inventory/models.py`)

```python
from django.db import models

class MaterialRequisitionTemp(models.Model):
    """
    Represents a temporary material requisition entry, typically used for staging
    before final submission or during multi-step processes.
    Corresponds to tblinv_MaterialRequisition_Temp in ASP.NET.
    """
    id = models.AutoField(db_column='Id', primary_key=True) # Explicitly define PK if it's named 'Id'
    comp_id = models.IntegerField(db_column='CompId', verbose_name='Company ID')
    session_id = models.CharField(db_column='SessionId', max_length=255, verbose_name='Session ID')
    requisition_no = models.CharField(
        db_column='RequisitionNo', max_length=50, blank=True, null=True,
        verbose_name='Requisition Number'
    )
    item_code = models.CharField(db_column='ItemCode', max_length=100, verbose_name='Item Code')
    quantity = models.DecimalField(db_column='Quantity', max_digits=10, decimal_places=2, verbose_name='Quantity')
    uom = models.CharField(db_column='UOM', max_length=20, verbose_name='Unit of Measure')
    remarks = models.TextField(db_column='Remarks', blank=True, null=True, verbose_name='Remarks')
    is_approved = models.BooleanField(db_column='IsApproved', default=False, verbose_name='Approved')
    created_at = models.DateTimeField(db_column='CreatedAt', auto_now_add=True, verbose_name='Created At')

    class Meta:
        managed = False  # Set to True if Django should manage this table
        db_table = 'tblinv_MaterialRequisition_Temp'
        verbose_name = 'Material Requisition Temp'
        verbose_name_plural = 'Material Requisition Temps'
        ordering = ['-created_at'] # Order by creation date, newest first

    def __str__(self):
        return f"Temp MRS {self.requisition_no or self.id} ({self.item_code})"

    # Business logic methods (Fat Model)
    def clean_session_data(self, user_id=None):
        """
        Deletes temporary material requisition entries for this specific instance's session and company.
        This would be used if you wanted to clean *just this item's* session.
        For general cleanup, use the class method `cleanup_for_user`.
        """
        # Example of instance-specific cleanup logic
        # In this specific case, it's more common to clean *all* for a user.
        # This method is here to demonstrate a fat model concept.
        pass

    @classmethod
    def cleanup_for_user(cls, company_id, session_id):
        """
        Deletes all temporary material requisition entries for a given company and session ID.
        This directly translates the ASP.NET Page_Load cleanup logic.
        """
        deleted_count, _ = cls.objects.filter(
            comp_id=company_id,
            session_id=session_id
        ).delete()
        return deleted_count

    def get_display_fields(self):
        """Returns a dictionary of fields suitable for display."""
        return {
            'Company ID': self.comp_id,
            'Session ID': self.session_id,
            'Requisition No.': self.requisition_no,
            'Item Code': self.item_code,
            'Quantity': self.quantity,
            'UOM': self.uom,
            'Approved': 'Yes' if self.is_approved else 'No',
            'Created At': self.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'Remarks': self.remarks,
        }

```

#### 4.2 Forms (`inventory/forms.py`)

```python
from django import forms
from .models import MaterialRequisitionTemp

class MaterialRequisitionTempForm(forms.ModelForm):
    """
    Form for creating and updating MaterialRequisitionTemp instances.
    """
    class Meta:
        model = MaterialRequisitionTemp
        fields = [
            'comp_id', 'session_id', 'requisition_no', 'item_code',
            'quantity', 'uom', 'remarks', 'is_approved'
        ]
        widgets = {
            'comp_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'session_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'requisition_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'item_code': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'quantity': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01'}),
            'uom': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'remarks': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'is_approved': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded'}),
        }
        labels = {
            'comp_id': 'Company ID',
            'session_id': 'Session ID',
            'requisition_no': 'Requisition No.',
            'item_code': 'Item Code',
            'quantity': 'Quantity',
            'uom': 'UOM',
            'remarks': 'Remarks',
            'is_approved': 'Approved',
        }

    def clean(self):
        cleaned_data = super().clean()
        # Example custom validation: ensure quantity is positive
        quantity = cleaned_data.get('quantity')
        if quantity is not None and quantity <= 0:
            self.add_error('quantity', "Quantity must be a positive number.")
        return cleaned_data

```

#### 4.3 Views (`inventory/views.py`)

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.template.loader import render_to_string
from django.shortcuts import get_object_or_404
from django.db import transaction

from .models import MaterialRequisitionTemp
from .forms import MaterialRequisitionTempForm

# Base mixin for HTMX responses
class HTMXResponseMixin:
    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, f'{self.model._meta.verbose_name} saved successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshMaterialRequisitionTempList'}
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # For HTMX, re-render the form with errors
            return render_to_string(self.template_name, {'form': form}, request=self.request)
        return response

    def delete(self, request, *args, **kwargs):
        with transaction.atomic():
            response = super().delete(request, *args, **kwargs)
            messages.success(self.request, f'{self.model._meta.verbose_name} deleted successfully.')
            if request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204,
                    headers={'HX-Trigger': 'refreshMaterialRequisitionTempList'}
                )
            return response


class MaterialRequisitionTempListView(ListView):
    """
    Displays the main dashboard for temporary material requisitions.
    """
    model = MaterialRequisitionTemp
    template_name = 'inventory/materialrequisitiontemp/list.html'
    context_object_name = 'material_requisition_temps'

    # The list itself is loaded via HTMX into a partial, so this view
    # primarily serves the base page structure.
    # No need to fetch objects here initially if the table is loaded via HTMX.


class MaterialRequisitionTempTablePartialView(ListView):
    """
    Renders the DataTables portion of the material requisition temp list.
    Designed to be loaded via HTMX.
    """
    model = MaterialRequisitionTemp
    template_name = 'inventory/materialrequisitiontemp/_materialrequisitiontemp_table.html'
    context_object_name = 'material_requisition_temps' # Consistent context name

    def get_queryset(self):
        # Filter by current user's session_id and company_id if available
        # In a real app, ensure security for comp_id and session_id
        queryset = super().get_queryset()
        if self.request.user.is_authenticated:
            session_id = self.request.user.username
            # For demonstration, retrieve comp_id from session or a default
            comp_id = self.request.session.get('compid', 1) # Default to 1 if not set
            queryset = queryset.filter(comp_id=comp_id, session_id=session_id)
        return queryset


class MaterialRequisitionTempCreateView(HTMXResponseMixin, CreateView):
    """
    Handles creation of new temporary material requisition entries.
    """
    model = MaterialRequisitionTemp
    form_class = MaterialRequisitionTempForm
    template_name = 'inventory/materialrequisitiontemp/_materialrequisitiontemp_form.html'
    success_url = reverse_lazy('materialrequisitiontemp_list') # Fallback if not HTMX

    def get_initial(self):
        # Pre-fill comp_id and session_id for the current user
        initial = super().get_initial()
        if self.request.user.is_authenticated:
            initial['session_id'] = self.request.user.username
            initial['comp_id'] = self.request.session.get('compid', 1) # Default to 1 if not set
        return initial

    def form_valid(self, form):
        # Automatically set user/company info before saving if not already set by initial
        if not form.instance.session_id and self.request.user.is_authenticated:
            form.instance.session_id = self.request.user.username
        if not form.instance.comp_id:
            form.instance.comp_id = self.request.session.get('compid', 1) # Default to 1
        return super().form_valid(form)


class MaterialRequisitionTempUpdateView(HTMXResponseMixin, UpdateView):
    """
    Handles updating existing temporary material requisition entries.
    """
    model = MaterialRequisitionTemp
    form_class = MaterialRequisitionTempForm
    template_name = 'inventory/materialrequisitiontemp/_materialrequisitiontemp_form.html'
    context_object_name = 'material_requisition_temp'
    success_url = reverse_lazy('materialrequisitiontemp_list') # Fallback if not HTMX


class MaterialRequisitionTempDeleteView(HTMXResponseMixin, DeleteView):
    """
    Handles deletion of temporary material requisition entries.
    """
    model = MaterialRequisitionTemp
    template_name = 'inventory/materialrequisitiontemp/confirm_delete.html'
    context_object_name = 'material_requisition_temp'
    success_url = reverse_lazy('materialrequisitiontemp_list') # Fallback if not HTMX


class MaterialRequisitionTempCleanupView(View):
    """
    View to trigger the cleanup of temporary material requisition data
    for the current session and company, similar to the ASP.NET Page_Load logic.
    """
    def post(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            messages.error(request, "Authentication required for this operation.")
            return HttpResponse(status=401) # Unauthorized

        # Securely retrieve comp_id and session_id
        # In a production system, 'compid' should ideally be part of user profile/model
        # or managed through a more robust session mechanism.
        comp_id = request.session.get('compid', None)
        session_id = request.user.username

        if comp_id is None:
            messages.error(request, "Company ID not found in session for cleanup. Cannot proceed.")
            return HttpResponse(status=400) # Bad Request

        deleted_count = MaterialRequisitionTemp.cleanup_for_user(comp_id, session_id)
        messages.success(request, f"Cleaned up {deleted_count} temporary requisition entries.")

        # Trigger refresh of the table after cleanup
        return HttpResponse(
            status=204,  # No Content
            headers={'HX-Trigger': 'refreshMaterialRequisitionTempList'}
        )

```

#### 4.4 Templates (`inventory/templates/inventory/materialrequisitiontemp/`)

**`list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Material Requisition Temps</h2>
        <div class="flex space-x-4">
            <button
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                hx-get="{% url 'materialrequisitiontemp_add' %}"
                hx-target="#modalContent"
                hx-trigger="click"
                _="on click add .is-active to #modal">
                Add New Entry
            </button>
            <button
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                hx-post="{% url 'materialrequisitiontemp_cleanup' %}"
                hx-confirm="Are you sure you want to clean up ALL temporary material requisitions for your session and company?"
                hx-target="body" hx-swap="none"> {# Swap none as it's a background action triggering refresh #}
                Clean Temp Data
            </button>
        </div>
    </div>

    <div id="materialrequisitiontempTable-container"
         hx-trigger="load, refreshMaterialRequisitionTempList from:body"
         hx-get="{% url 'materialrequisitiontemp_table' %}"
         hx-swap="innerHTML">
        <!-- Initial loading indicator -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading temporary requisitions...</p>
        </div>
    </div>

    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ showModal: false }"
         x-show="showModal"
         x-on:close-modal.window="showModal = false"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
         x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
         x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"
             @click.away="showModal = false"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component for modal visibility,
        // although htmx+_hyperscript also handles it via `on click add .is-active to #modal`
        // x-data="{ showModal: false }" in the modal div is more standard for Alpine.js if we wanted to control it purely with Alpine.
        // The _hyperscript logic is concise for this case.
    });

    // Event listener for HTMX afterSwap to initialize DataTables when content loads/refreshes
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.target.id === 'materialrequisitiontempTable-container') {
            $('#materialrequisitiontempTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true, // Destroy existing instance if any
                "responsive": true
            });
        }
        // Close modal after successful form submission/deletion (status 204)
        if (event.detail.xhr.status === 204 && event.detail.requestHeaders['HX-Request']) {
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.remove('is-active');
            }
        }
    });

</script>
{% endblock %}
```

**`_materialrequisitiontemp_table.html`** (Partial)

```html
<table id="materialrequisitiontempTable" class="min-w-full bg-white border border-gray-200 divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company ID</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Session ID</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Requisition No.</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Approved</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created At</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="divide-y divide-gray-200">
        {% for obj in material_requisition_temps %}
        <tr class="hover:bg-gray-50">
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.comp_id }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.session_id }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.requisition_no|default:"-" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.item_code }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.quantity }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.uom }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {% if obj.is_approved %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                    {{ obj.is_approved|yesno:"Yes,No" }}
                </span>
            </td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.created_at|date:"Y-m-d H:i" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">
                <button
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2 text-sm"
                    hx-get="{% url 'materialrequisitiontemp_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-sm"
                    hx-get="{% url 'materialrequisitiontemp_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="10" class="py-4 px-4 text-center text-gray-500">No temporary material requisition entries found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<!-- DataTable initialization handled in list.html after htmx:afterSwap -->
```

**`_materialrequisitiontemp_form.html`** (Partial)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Material Requisition Temp</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}

        <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md text-sm"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md text-sm">
                <span id="form-spinner" class="htmx-indicator inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                Save Changes
            </button>
        </div>
    </form>
</div>
```

**`confirm_delete.html`** (Partial)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete this temporary material requisition entry?</p>
    <p class="text-sm text-gray-600 mb-4">
        <strong>Item Code:</strong> {{ material_requisition_temp.item_code }}<br>
        <strong>Quantity:</strong> {{ material_requisition_temp.quantity }} {{ material_requisition_temp.uom }}<br>
        <strong>Requisition No:</strong> {{ material_requisition_temp.requisition_no|default:"N/A" }}
    </p>

    <form hx-post="{% url 'materialrequisitiontemp_delete' material_requisition_temp.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md text-sm"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md text-sm">
                Confirm Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`inventory/urls.py`)

```python
from django.urls import path
from .views import (
    MaterialRequisitionTempListView,
    MaterialRequisitionTempTablePartialView,
    MaterialRequisitionTempCreateView,
    MaterialRequisitionTempUpdateView,
    MaterialRequisitionTempDeleteView,
    MaterialRequisitionTempCleanupView
)

urlpatterns = [
    # Main dashboard view
    path('materialrequisitiontemp/', MaterialRequisitionTempListView.as_view(), name='materialrequisitiontemp_list'),

    # HTMX partial for the table content
    path('materialrequisitiontemp/table/', MaterialRequisitionTempTablePartialView.as_view(), name='materialrequisitiontemp_table'),

    # CRUD operations
    path('materialrequisitiontemp/add/', MaterialRequisitionTempCreateView.as_view(), name='materialrequisitiontemp_add'),
    path('materialrequisitiontemp/edit/<int:pk>/', MaterialRequisitionTempUpdateView.as_view(), name='materialrequisitiontemp_edit'),
    path('materialrequisitiontemp/delete/<int:pk>/', MaterialRequisitionTempDeleteView.as_view(), name='materialrequisitiontemp_delete'),

    # Specific cleanup operation from original ASP.NET Page_Load
    path('materialrequisitiontemp/cleanup/', MaterialRequisitionTempCleanupView.as_view(), name='materialrequisitiontemp_cleanup'),
]
```

#### 4.6 Tests (`inventory/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from django.db import IntegrityError

from .models import MaterialRequisitionTemp
from .forms import MaterialRequisitionTempForm

class MaterialRequisitionTempModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a test user
        cls.user = User.objects.create_user(username='testuser', password='password123')
        # Create test data for all tests
        cls.temp_mrs = MaterialRequisitionTemp.objects.create(
            comp_id=1,
            session_id='testuser',
            requisition_no='MRS/001/TEMP',
            item_code='ITEM001',
            quantity=10.5,
            uom='Nos',
            remarks='Test item',
            is_approved=False
        )
        MaterialRequisitionTemp.objects.create( # Another entry for cleanup testing
            comp_id=1,
            session_id='anotheruser',
            item_code='ITEM002',
            quantity=5.0,
            uom='Kg'
        )
        MaterialRequisitionTemp.objects.create( # Another entry for current user
            comp_id=1,
            session_id='testuser',
            item_code='ITEM003',
            quantity=2.0,
            uom='Pcs'
        )

    def test_materialrequisitiontemp_creation(self):
        obj = MaterialRequisitionTemp.objects.get(pk=self.temp_mrs.pk)
        self.assertEqual(obj.comp_id, 1)
        self.assertEqual(obj.session_id, 'testuser')
        self.assertEqual(obj.item_code, 'ITEM001')
        self.assertEqual(float(obj.quantity), 10.5) # Convert Decimal to float for comparison
        self.assertEqual(obj.uom, 'Nos')
        self.assertFalse(obj.is_approved)

    def test_string_representation(self):
        obj = MaterialRequisitionTemp.objects.get(pk=self.temp_mrs.pk)
        self.assertEqual(str(obj), f"Temp MRS {obj.requisition_no} ({obj.item_code})")

    def test_verbose_name_plural(self):
        self.assertEqual(str(MaterialRequisitionTemp._meta.verbose_name_plural), 'Material Requisition Temps')

    def test_cleanup_for_user(self):
        # Verify initial count for 'testuser'
        self.assertEqual(MaterialRequisitionTemp.objects.filter(session_id='testuser').count(), 2)

        # Perform cleanup for 'testuser' and comp_id 1
        deleted_count = MaterialRequisitionTemp.cleanup_for_user(1, 'testuser')
        self.assertEqual(deleted_count, 2)
        self.assertEqual(MaterialRequisitionTemp.objects.filter(session_id='testuser').count(), 0)
        self.assertEqual(MaterialRequisitionTemp.objects.filter(session_id='anotheruser').count(), 1) # Other users unaffected


class MaterialRequisitionTempFormTest(TestCase):
    def test_form_valid_data(self):
        form_data = {
            'comp_id': 1,
            'session_id': 'formuser',
            'item_code': 'FORMITEM',
            'quantity': 100.0,
            'uom': 'Pcs',
            'is_approved': True
        }
        form = MaterialRequisitionTempForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_form_invalid_quantity(self):
        form_data = {
            'comp_id': 1,
            'session_id': 'formuser',
            'item_code': 'FORMITEM',
            'quantity': 0, # Invalid quantity
            'uom': 'Pcs',
            'is_approved': False
        }
        form = MaterialRequisitionTempForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('quantity', form.errors)
        self.assertIn('Quantity must be a positive number.', form.errors['quantity'])


class MaterialRequisitionTempViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.user = User.objects.create_user(username='viewtestuser', password='password123')
        cls.temp_mrs = MaterialRequisitionTemp.objects.create(
            comp_id=1,
            session_id='viewtestuser',
            item_code='VIEWITEM',
            quantity=10.0,
            uom='Units',
            remarks='View test item'
        )
        cls.other_temp_mrs = MaterialRequisitionTemp.objects.create(
            comp_id=2, # Different company ID
            session_id='anotheruser',
            item_code='OTHERITEM',
            quantity=5.0,
            uom='Boxes'
        )

    def setUp(self):
        self.client = Client()
        self.client.login(username='viewtestuser', password='password123')
        self.client.session['compid'] = 1 # Set compid in session for views that use it

    def test_list_view_get(self):
        response = self.client.get(reverse('materialrequisitiontemp_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialrequisitiontemp/list.html')

    def test_table_partial_view_get(self):
        # This view is typically called via HTMX
        response = self.client.get(reverse('materialrequisitiontemp_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialrequisitiontemp/_materialrequisitiontemp_table.html')
        self.assertIn('material_requisition_temps', response.context)
        # Should only show items for 'viewtestuser' and comp_id 1
        self.assertEqual(len(response.context['material_requisition_temps']), 1)
        self.assertEqual(response.context['material_requisition_temps'][0], self.temp_mrs)
        self.assertContains(response, 'VIEWITEM')
        self.assertNotContains(response, 'OTHERITEM')


    def test_create_view_get(self):
        response = self.client.get(reverse('materialrequisitiontemp_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialrequisitiontemp/_materialrequisitiontemp_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].initial['session_id'], 'viewtestuser')
        self.assertEqual(response.context['form'].initial['comp_id'], 1)


    def test_create_view_post_success(self):
        self.assertEqual(MaterialRequisitionTemp.objects.count(), 2) # Initial: temp_mrs, other_temp_mrs
        data = {
            'comp_id': 1,
            'session_id': 'viewtestuser',
            'item_code': 'NEWITEM',
            'quantity': 20.0,
            'uom': 'Boxes',
            'is_approved': False
        }
        response = self.client.post(reverse('materialrequisitiontemp_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success (No Content)
        self.assertTrue(MaterialRequisitionTemp.objects.filter(item_code='NEWITEM').exists())
        self.assertEqual(MaterialRequisitionTemp.objects.count(), 3)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialRequisitionTempList')


    def test_create_view_post_invalid(self):
        initial_count = MaterialRequisitionTemp.objects.count()
        data = {
            'comp_id': 1,
            'session_id': 'viewtestuser',
            'item_code': 'INVALIDITEM',
            'quantity': 0, # Invalid quantity
            'uom': 'Units',
            'is_approved': False
        }
        response = self.client.post(reverse('materialrequisitiontemp_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX renders form with errors
        self.assertTemplateUsed(response, 'inventory/materialrequisitiontemp/_materialrequisitiontemp_form.html')
        self.assertIn('form', response.context)
        self.assertFalse(response.context['form'].is_valid())
        self.assertFalse(MaterialRequisitionTemp.objects.filter(item_code='INVALIDITEM').exists())
        self.assertEqual(MaterialRequisitionTemp.objects.count(), initial_count)


    def test_update_view_get(self):
        response = self.client.get(reverse('materialrequisitiontemp_edit', args=[self.temp_mrs.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialrequisitiontemp/_materialrequisitiontemp_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.temp_mrs)


    def test_update_view_post_success(self):
        data = {
            'comp_id': self.temp_mrs.comp_id,
            'session_id': self.temp_mrs.session_id,
            'item_code': 'UPDATEDITEM',
            'quantity': 15.0,
            'uom': 'Pcs',
            'is_approved': True
        }
        response = self.client.post(reverse('materialrequisitiontemp_edit', args=[self.temp_mrs.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success
        self.temp_mrs.refresh_from_db()
        self.assertEqual(self.temp_mrs.item_code, 'UPDATEDITEM')
        self.assertTrue(self.temp_mrs.is_approved)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialRequisitionTempList')


    def test_delete_view_get(self):
        response = self.client.get(reverse('materialrequisitiontemp_delete', args=[self.temp_mrs.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialrequisitiontemp/confirm_delete.html')
        self.assertIn('material_requisition_temp', response.context)
        self.assertEqual(response.context['material_requisition_temp'], self.temp_mrs)


    def test_delete_view_post_success(self):
        initial_count = MaterialRequisitionTemp.objects.count()
        response = self.client.post(reverse('materialrequisitiontemp_delete', args=[self.temp_mrs.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertEqual(MaterialRequisitionTemp.objects.count(), initial_count - 1)
        self.assertFalse(MaterialRequisitionTemp.objects.filter(pk=self.temp_mrs.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialRequisitionTempList')


    def test_cleanup_view_post_success(self):
        # Create an extra item for this user to ensure cleanup has something to delete
        MaterialRequisitionTemp.objects.create(
            comp_id=1,
            session_id='viewtestuser',
            item_code='CLEANUPITEM',
            quantity=1.0,
            uom='EA'
        )
        self.assertEqual(MaterialRequisitionTemp.objects.filter(session_id='viewtestuser').count(), 2)

        response = self.client.post(reverse('materialrequisitiontemp_cleanup'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertEqual(MaterialRequisitionTemp.objects.filter(session_id='viewtestuser').count(), 0)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialRequisitionTempList')

    def test_cleanup_view_post_unauthenticated(self):
        self.client.logout()
        response = self.client.post(reverse('materialrequisitiontemp_cleanup'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 401) # Unauthorized
        self.assertFalse('HX-Trigger' in response.headers) # No trigger on failure
```

### Step 5: HTMX and Alpine.js Integration

As demonstrated in the templates and views:
*   **HTMX:** Used for all dynamic content loading (table partials, form modals, delete confirmation modals) and form submissions (`hx-get`, `hx-post`, `hx-target`, `hx-swap`). `HX-Trigger` headers are sent from views on successful CRUD operations to refresh the list table automatically.
*   **Alpine.js:** Integrated with `_hyperscript` for simple UI state management, specifically for showing/hiding the modal (`_="on click add .is-active to #modal"`).
*   **DataTables:** The `list.html` and `_materialrequisitiontemp_table.html` are set up to use DataTables for client-side searching, sorting, and pagination. Initialization is deferred until HTMX has swapped the table content, ensuring it works correctly with dynamic updates.
*   All interactions are designed to work without full page reloads, providing a modern, responsive user experience.

---

## Final Notes

*   This plan provides a robust framework for migrating the specified ASP.NET dashboard functionality to a modern Django application.
*   The `MaterialRequisitionTemp` model fields are inferred for a more complete example. In a real migration, these would be directly mapped from your existing database schema.
*   User authentication and company ID handling (`request.user.username` and `request.session.get('compid', 1)`) are simplified for demonstration. In a production environment, `compid` should be securely retrieved from the authenticated user's profile or an organization model.
*   The `managed = False` in the model's `Meta` class indicates that Django will not create or modify the `tblinv_MaterialRequisition_Temp` table. This is crucial when working with an existing database schema. If you intend for Django to manage the table, set `managed = True` and run `makemigrations` and `migrate`.
*   Error handling for `form_invalid` in `HTMXResponseMixin` ensures that form validation errors are re-rendered within the modal, providing immediate feedback to the user without a full page refresh.
*   This structure promotes a clean architecture where business logic resides in models, and views are kept thin, primarily handling HTTP requests and responses.