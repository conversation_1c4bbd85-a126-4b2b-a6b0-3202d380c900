## ASP.NET to Django Conversion Script: Goods Service Note [GSN] - Edit

This document outlines a comprehensive modernization plan to transition the existing ASP.NET Goods Service Note (GSN) editing and listing functionality to a modern Django application. The approach prioritizes automation-driven migration, adhering to a "Fat Model, Thin View" architecture, and leveraging contemporary frontend technologies like HTMX and Alpine.js for a highly interactive user experience without complex JavaScript.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and extends `core/base.html`.
- Focus ONLY on component-specific code for the current module (`GoodsServiceNote_SN_Edit`).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

### Conversion Steps:

### Step 1: Extract Database Schema

**Business Implication:** Understanding the existing data structure is the foundation of modernization. This step ensures that the new Django application correctly interprets and interacts with your legacy database, protecting your historical data.

**Analysis:**
The ASP.NET code interacts with multiple tables to compile the Goods Service Note display. The primary table is `tblinv_MaterialServiceNote_Master`. Other tables are joined to fetch related details like financial year, GIN (Goods Inward Note) information, Purchase Order (PO) data, and Supplier details.

**Identified Tables and Key Columns:**

*   **`tblinv_MaterialServiceNote_Master`** (Primary data source for GSNs)
    *   `Id` (Primary Key, Integer)
    *   `FinYearId` (Integer, links to `tblFinancial_master`)
    *   `GSNNo` (String)
    *   `GINNo` (String)
    *   `GINId` (Integer, links to `tblInv_Inward_Master`)
    *   `SysDate` (DateTime)
    *   `CompId` (Integer, Company ID)

*   **`tblFinancial_master`** (For Financial Year details)
    *   `FinYearId` (Primary Key / Unique Identifier, Integer)
    *   `FinYear` (String)
    *   `CompId` (Integer)

*   **`tblInv_Inward_Master`** (For GIN, PO, Challan details related to GSN)
    *   `Id` (Primary Key, Integer)
    *   `PONo` (String)
    *   `ChallanNo` (String)
    *   `ChallanDate` (DateTime)
    *   `CompId` (Integer)

*   **`tblMM_PO_Master`** (For linking POs to Suppliers)
    *   `Id` (Primary Key, Integer)
    *   `PONo` (String)
    *   `SupplierId` (String, links to `tblMM_Supplier_master`)
    *   `CompId` (Integer)

*   **`tblMM_Supplier_master`** (For Supplier details)
    *   `SupplierId` (Primary Key / Unique Identifier, String)
    *   `SupplierName` (String)
    *   `CompId` (Integer)

*   **`tblInv_Inward_Details`** and **`tblMM_PO_Details`**: These tables are used in complex joins in the C# code to bridge relationships. In Django, with `managed=False`, these might be mapped as separate models or handled implicitly through the fat model's lookup logic if no direct manipulation is required. For the purpose of listing and search, the derived columns will be fetched via properties on the main `GoodsServiceNote` model.

### Step 2: Identify Backend Functionality

**Business Implication:** This step clarifies the core operations of the existing system, allowing us to replicate and enhance them in Django. The focus is on ensuring all business processes are accurately transitioned.

**Identified Functionality:**

*   **Read (List View):** The primary function of the page is to display a list of Goods Service Notes.
    *   Data is retrieved from `tblinv_MaterialServiceNote_Master` and enriched with data from `tblFinancial_master`, `tblInv_Inward_Master`, `tblMM_PO_Master`, and `tblMM_Supplier_master` via a series of joins and lookups.
    *   Pagination is supported (`PageSize="20"`).
*   **Search/Filter:** Users can search for GSNs by `Supplier` name (using `txtSupplier` and `btnSearch`). This filters the displayed list.
*   **Autocomplete:** The `txtSupplier` field has an autocomplete extender that suggests supplier names based on prefix input, fetching data from `tblMM_Supplier_master`.
*   **Select Action:** Each row in the `GridView` has a "Select" link button (`btnlnk`) which, when clicked, redirects the user to a detailed editing page (`GoodsServiceNote_SN_Edit_Details.aspx`) with various IDs as query parameters. This indicates a navigation to an "Edit" or "Detail" view for a specific GSN.
*   **Session Management:** `CompId` (Company ID) and `FinYearId` (Financial Year ID) are retrieved from session variables and used in data filtering.

### Step 3: Infer UI Components

**Business Implication:** Understanding the current user interface elements helps us design a modern, user-friendly Django interface that maintains or improves usability. The goal is to map ASP.NET controls to modern HTML5, HTMX, and Alpine.js patterns.

**Identified UI Components and Their Roles:**

*   **Page Structure:** The page is structured within an ASP.NET Master Page, providing a consistent layout (Django: `core/base.html` for layout, content blocks).
*   **Header:** `<b>&nbsp;Goods Service Note [GSN] - Edit</b>` (Django: H2 tag).
*   **Supplier Search:**
    *   `txtSupplier` (ASP.NET TextBox): A text input field for entering supplier names.
    *   `txtSupplier_AutoCompleteExtender1` (AjaxControlToolkit AutoCompleteExtender): Provides dynamic suggestions for supplier names as the user types.
    *   `btnSearch` (ASP.NET Button): Triggers the search functionality.
*   **Data Display:**
    *   `GridView2` (ASP.NET GridView): Displays the tabular data. This will be replaced by an HTML `<table>` element integrated with **DataTables.js** for client-side sorting, filtering, and pagination.
    *   **Grid Columns (Django equivalent in HTML table headers/cells):**
        *   SN (Serial Number)
        *   Select (Action button/link for editing)
        *   Fin Year Id (Hidden)
        *   Fin Year
        *   GSN No
        *   GIN Id (Hidden)
        *   GIN No
        *   Date (SysDate)
        *   PONo
        *   Name of Supplier
        *   Id (Hidden, Primary Key)
        *   Challan No
        *   Date (ChallanDate)
        *   Sup Id (Hidden, Supplier ID)
*   **Styling:** Existing `Css/StyleSheet.css` and `Css/yui-datatable.css` will be replaced by **Tailwind CSS** and **DataTables default styling**.

### Step 4: Generate Django Code

**Business Implication:** This is the core implementation phase, translating the identified functionalities into robust, maintainable Django code. The emphasis is on clean architecture, performance, and testability.

For this application, we'll assume a Django app named `inventory`.

#### 4.1 Models (`inventory/models.py`)

**Business Implication:** Django models define the structure and behavior of your data. By mapping them to the existing legacy database tables, we ensure data integrity and enable powerful object-relational mapping (ORM) capabilities. The "Fat Model" approach means that complex data retrieval logic, which was previously scattered in the code-behind, is now centralized within the model itself, making it reusable and easier to manage.

```python
from django.db import models
from django.utils import timezone

# IMPORTANT: For 'managed = False', field types and max_lengths must match the exact
# schema of your existing SQL Server database. Adjust accordingly.

# Model for tblFinancial_master
class FinancialYear(models.Model):
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=100)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False  # Django will not create/delete/modify this table
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

# Model for tblMM_Supplier_master
class Supplier(models.Model):
    supplier_id = models.CharField(db_column='SupplierId', max_length=50, primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return f"{self.supplier_name} [{self.supplier_id}]"

# Model for tblInv_Inward_Master
class GoodsInwardNote(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    po_no = models.CharField(db_column='PONo', max_length=100)
    challan_no = models.CharField(db_column='ChallanNo', max_length=100)
    challan_date = models.DateTimeField(db_column='ChallanDate')
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Master'
        verbose_name = 'Goods Inward Note'
        verbose_name_plural = 'Goods Inward Notes'

    def __str__(self):
        return self.po_no

# Model for tblMM_PO_Master
class PurchaseOrder(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    po_no = models.CharField(db_column='PONo', max_length=100)
    supplier_id = models.CharField(db_column='SupplierId', max_length=50) # This is not a FK field here, but a value
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'

    def __str__(self):
        return self.po_no

# Main Model: GoodsServiceNote (from tblinv_MaterialServiceNote_Master)
class GoodsServiceNote(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    fin_year_id = models.IntegerField(db_column='FinYearId')
    gsn_no = models.CharField(db_column='GSNNo', max_length=100)
    gin_no = models.CharField(db_column='GINNo', max_length=100)
    gin_id = models.IntegerField(db_column='GINId')
    sys_date = models.DateTimeField(db_column='SysDate')
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialServiceNote_Master'
        verbose_name = 'Goods Service Note'
        verbose_name_plural = 'Goods Service Notes'

    def __str__(self):
        return self.gsn_no

    # --- Fat Model: Derived properties from related tables ---
    # These properties encapsulate the complex join logic from the C# loadData method.
    # Note: For large datasets, calling these properties for each row in a list view
    # can lead to N+1 query problems. In a production scenario, for optimal performance
    # in list views, consider a custom manager with .extra() or RawSQL, or pre-populating
    # these values during a more optimized query.
    # However, this approach adheres to the "fat model" principle for business logic encapsulation.

    @property
    def fin_year_display(self):
        """Retrieves the financial year string from FinancialYear model."""
        try:
            # Assumes 'default' database alias, adjust if using a specific 'legacy_db' alias
            return FinancialYear.objects.get(fin_year_id=self.fin_year_id, comp_id=self.comp_id).fin_year
        except FinancialYear.DoesNotExist:
            return 'N/A'

    @property
    def gin_details_obj(self):
        """Retrieves the GoodsInwardNote object related to this GSN."""
        try:
            return GoodsInwardNote.objects.get(id=self.gin_id, comp_id=self.comp_id)
        except GoodsInwardNote.DoesNotExist:
            return None

    @property
    def po_no_display(self):
        """Retrieves the Purchase Order number from the related GIN."""
        gin = self.gin_details_obj
        return gin.po_no if gin else 'N/A'

    @property
    def challan_no_display(self):
        """Retrieves the Challan Number from the related GIN."""
        gin = self.gin_details_obj
        return gin.challan_no if gin else 'N/A'

    @property
    def challan_date_display(self):
        """Retrieves and formats the Challan Date from the related GIN."""
        gin = self.gin_details_obj
        return gin.challan_date.strftime('%d/%m/%Y') if gin and gin.challan_date else 'N/A'

    @property
    def supplier_details(self):
        """
        Retrieves supplier ID and name based on the complex C# logic:
        GSN -> GIN -> PO -> Supplier.
        This attempts to mimic the exact multi-table lookup logic from the C# code-behind.
        """
        try:
            gin_instance = self.gin_details_obj
            if not gin_instance:
                return {'supplier_id': None, 'supplier_name': 'N/A'}

            # The C# logic is complex here, involving tblInv_Inward_Details and tblMM_PO_Details.
            # Simplified interpretation for managed=False, assuming PONo is sufficient to find PO Master.
            # In C#, it does: GINId -> InwardMaster (PONo, POId) -> PO Master (matching PONo/POId) -> SupplierId.
            # For this 'managed=False' scenario, we'll try to follow the data flow directly.
            # A more robust solution might need explicit models for InwardDetail and PODetail if direct DB
            # lookups based on single fields are unreliable.
            
            # This segment attempts to simulate the C# path as closely as possible:
            # From GIN instance, get its PO No.
            po_no_from_gin = gin_instance.po_no

            # Now, find the PurchaseOrder (tblMM_PO_Master) using this PO No and company ID.
            # C# uses tblMM_PO_Master.PONo + tblMM_PO_Details.Id/MId. Here, a direct match on PONo is assumed as a simplification.
            # If PONo is not unique in tblMM_PO_Master, this needs to be refined.
            po_master = PurchaseOrder.objects.filter(po_no=po_no_from_gin, comp_id=self.comp_id).first()
            if not po_master:
                return {'supplier_id': None, 'supplier_name': 'N/A'}

            # Finally, use the SupplierId from the found PurchaseOrder to get the Supplier Name.
            supplier_obj = Supplier.objects.filter(supplier_id=po_master.supplier_id, comp_id=self.comp_id).first()

            if supplier_obj:
                return {'supplier_id': supplier_obj.supplier_id, 'supplier_name': supplier_obj.supplier_name}
            else:
                # Fallback if supplier not found, but ID from PO is known
                return {'supplier_id': po_master.supplier_id, 'supplier_name': 'N/A'}

        except Exception: # Catch any lookup errors gracefully
            return {'supplier_id': None, 'supplier_name': 'N/A'}

    @property
    def supplier_name_display(self):
        """Returns the formatted supplier name."""
        return self.supplier_details['supplier_name']

    @property
    def supplier_id_display(self):
        """Returns the supplier ID."""
        return self.supplier_details['supplier_id']

```

#### 4.2 Forms (`inventory/forms.py`)

**Business Implication:** Forms in Django handle user input and validation, ensuring data quality before it's processed. This centralizes validation logic, making it easier to maintain and reuse across different parts of the application.

```python
from django import forms

class GoodsServiceNoteSearchForm(forms.Form):
    """
    Form for the supplier search functionality.
    This form is simple, as the autocomplete functionality is handled by HTMX.
    """
    supplier_name = forms.CharField(
        max_length=255,
        required=False,
        label="Supplier:",
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'id': 'txtSupplier',
            'placeholder': 'Start typing supplier name...',
            'hx-get': '/inventory/goodsservicenote/autocomplete/supplier/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#supplier-autocomplete-results', # Target to display results
            'hx-swap': 'innerHTML',
            'autocomplete': 'off', # Prevent browser autocomplete
            '_="on keydown if event.key == \'Enter\' preventDefault()"' # Prevent form submission on Enter
        })
    )

    # Hidden field to store selected supplier ID for search
    supplier_id_selected = forms.CharField(
        widget=forms.HiddenInput(attrs={'id': 'supplierIdSelected'}),
        required=False
    )
```

#### 4.3 Views (`inventory/views.py`)

**Business Implication:** Views define how your application responds to web requests. By using Django's Class-Based Views (CBVs) and keeping them "thin" (minimal logic), we ensure a clear separation of concerns. Business logic is delegated to the "Fat Models", making views concise, readable, and focused on presentation and routing. HTMX is integrated to provide dynamic updates without full page reloads, enhancing user experience.

```python
from django.views.generic import ListView, View
from django.shortcuts import render
from django.http import HttpResponse, JsonResponse, HttpResponseRedirect
from django.urls import reverse_lazy
from django.db.models import Q
from django.contrib import messages
import json

from .models import GoodsServiceNote, Supplier
from .forms import GoodsServiceNoteSearchForm

# Assuming Company ID and Financial Year ID would be available from the session or user profile.
# For demonstration, we'll use placeholder values. In a real ERP, these would come from the current user's context.
# Placeholder for session data
def get_session_context(request):
    # In a real ERP, this would come from request.user.company.id, request.session['finyear'], etc.
    # For this migration, we'll assume these are available.
    # You might implement a middleware or a context processor to make these globally available.
    comp_id = request.session.get('compid', 1)  # Defaulting to 1 for demonstration
    fin_year_id = request.session.get('finyear', 1) # Defaulting to 1 for demonstration
    return {'comp_id': comp_id, 'fin_year_id': fin_year_id}


class GoodsServiceNoteListView(ListView):
    model = GoodsServiceNote
    template_name = 'inventory/goodsservicenote/list.html'
    context_object_name = 'goodsservicenotes'
    # Page size for initial load, DataTables handles subsequent pagination on client-side
    # but for server-side pagination (if needed later), this would be important.
    paginate_by = 20 

    def get_queryset(self):
        """
        Initial queryset for the list page.
        Actual filtering is handled by GoodsServiceNoteTablePartialView for HTMX requests.
        """
        context = get_session_context(self.request)
        return GoodsServiceNote.objects.filter(
            comp_id=context['comp_id'],
            fin_year_id__lte=context['fin_year_id'] # Mimics FinYearId <= condition
        ).order_by('-id')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = GoodsServiceNoteSearchForm(self.request.GET)
        return context

# HTMX endpoint to return only the table content
class GoodsServiceNoteTablePartialView(ListView):
    model = GoodsServiceNote
    template_name = 'inventory/goodsservicenote/_goodsservicenote_table.html'
    context_object_name = 'goodsservicenotes'

    def get_queryset(self):
        """
        Filters the GSN list based on supplier_id_selected from the search form.
        This is where the actual filtering logic based on user input occurs.
        """
        context = get_session_context(self.request)
        queryset = GoodsServiceNote.objects.filter(
            comp_id=context['comp_id'],
            fin_year_id__lte=context['fin_year_id']
        ).order_by('-id')

        # Get supplier_id for filtering
        supplier_id = self.request.GET.get('supplier_id_selected', '')

        if supplier_id:
            # We need to filter based on the derived supplier_id.
            # This is complex with managed=False and properties.
            # A direct ORM filter is not possible on the @property.
            # Option 1: Fetch all, then filter in Python (bad for large data).
            # Option 2: Use RawSQL or custom manager to perform complex join at DB level.
            # Option 3: If supplier_id is directly stored in GSN table, filter directly.
            
            # For demonstration, and given the complexity of the joins in C#
            # and the 'fat model' constraint, we'll demonstrate a pragmatic approach:
            # Fetch relevant GSNs, then filter them by iterating through their supplier_id_display.
            # THIS IS NOT SCALABLE FOR LARGE DATABASES. A raw SQL query or
            # more sophisticated ORM join (if relationships were defined) would be needed for production.

            # Pragmatic approach for POC/initial migration (will be slow if queryset is huge):
            filtered_goodsservicenotes = []
            for gsn in queryset:
                # Use the property from the fat model to get the supplier ID
                if gsn.supplier_id_display and gsn.supplier_id_display.lower() == supplier_id.lower():
                    filtered_goodsservicenotes.append(gsn)
            return filtered_goodsservicenotes
        
        return queryset

    def render_to_response(self, context, **response_kwargs):
        # HTMX requests are often sent with HX-Request header
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, context)
        # Fallback for non-HTMX requests (though list.html handles initial load via HTMX)
        return super().render_to_response(context, **response_kwargs)


class SupplierAutoCompleteView(View):
    """
    HTMX endpoint for supplier name autocomplete.
    Mimics the ASP.NET WebMethod 'sql'.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('supplier_name', '').strip()
        context = get_session_context(request)
        
        if not prefix_text:
            return JsonResponse([], safe=False)

        # Filter suppliers starting with prefix_text
        suppliers = Supplier.objects.filter(
            Q(supplier_name__istartswith=prefix_text) |
            Q(supplier_id__istartswith=prefix_text),
            comp_id=context['comp_id']
        ).order_by('supplier_name')[:10] # Limit results as in original example

        results = []
        for s in suppliers:
            results.append(f"{s.supplier_name} [{s.supplier_id}]")
        
        return JsonResponse(results, safe=False)

# This would be the target for the "Select" action from the GridView.
# The original redirects to 'GoodsServiceNote_SN_Edit_Details.aspx?Id=...'
# This implies an update or detail view.
class GoodsServiceNoteDetailView(View): # Or DetailView / UpdateView
    def get(self, request, pk):
        # Retrieve additional query parameters from original redirect for context
        sup_id = request.GET.get('SupId')
        gsn_no = request.GET.get('GSNNo')
        gin_no = request.GET.get('GINNo')
        gin_id = request.GET.get('GINId')
        po_no = request.GET.get('PONo')
        fy_id = request.GET.get('FyId')
        mod_id = request.GET.get('ModId')
        sub_mod_id = request.GET.get('SubModId')

        try:
            gsn_instance = GoodsServiceNote.objects.get(pk=pk)
            # You would pass gsn_instance and other details to your detail/edit template
            # For a true fat model, thin view approach, the template might pull most details
            # from gsn_instance properties.
            context = {
                'goodsservicenote': gsn_instance,
                'sup_id': sup_id,
                'gsn_no_param': gsn_no,
                'gin_no_param': gin_no,
                'gin_id_param': gin_id,
                'po_no_param': po_no,
                'fy_id_param': fy_id,
                'mod_id_param': mod_id,
                'sub_mod_id_param': sub_mod_id,
            }
            # This would render a full page for the detail/edit view initially.
            # For HTMX, this would typically be a form in a modal or partial.
            return render(request, 'inventory/goodsservicenote/detail.html', context)
        except GoodsServiceNote.DoesNotExist:
            messages.error(request, "Goods Service Note not found.")
            return HttpResponseRedirect(reverse_lazy('goodsservicenote_list'))

# Placeholder for potential Create/Update/Delete if implemented in future modules
# class GoodsServiceNoteCreateView(CreateView): ...
# class GoodsServiceNoteUpdateView(UpdateView): ...
# class GoodsServiceNoteDeleteView(DeleteView): ...
```

#### 4.4 Templates (`inventory/templates/inventory/goodsservicenote/`)

**Business Implication:** Templates define the user interface. By using Django's template inheritance and HTMX for dynamic content, we create a responsive and efficient UI that feels modern and fast, while maintaining consistency with a `base.html` structure.

**`list.html`** (Main page for listing and searching)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Goods Service Note [GSN] - Edit</h2>
        <!-- No direct "Add New" button in original ASP.NET for this page, but common for lists -->
        <!-- Example Add New button if needed, linking to a future CreateView -->
        {# <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'goodsservicenote_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New GSN
        </button> #}
    </div>
    
    <div class="bg-white p-6 rounded-lg shadow-md mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Search GSN</h3>
        <form id="searchForm" hx-get="{% url 'goodsservicenote_table' %}" hx-target="#goodsservicenoteTable-container" hx-swap="innerHTML" hx-indicator="#loadingIndicator">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    {{ search_form.supplier_name.label_tag }}
                    <div class="relative">
                        {{ search_form.supplier_name }}
                        <div id="supplier-autocomplete-results" class="absolute z-10 bg-white border border-gray-300 rounded-md mt-1 w-full shadow-lg max-h-48 overflow-y-auto"></div>
                    </div>
                    {{ search_form.supplier_id_selected }}
                </div>
            </div>
            <div class="mt-6 flex justify-end">
                <button type="submit" class="redbox bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded shadow-sm">
                    Search
                </button>
            </div>
        </form>
    </div>

    <!-- Loading Indicator for HTMX -->
    <div id="loadingIndicator" class="htmx-indicator text-center py-4">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">Loading data...</p>
    </div>

    <div id="goodsservicenoteTable-container"
         hx-trigger="load, refreshGoodsServiceNoteList from:body"
         hx-get="{% url 'goodsservicenote_table' %}"
         hx-swap="innerHTML"
         hx-indicator="#loadingIndicator">
        <!-- DataTables content will be loaded here via HTMX -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading initial table data...</p>
        </div>
    </div>
    
    <!-- Modal for future form/details (if used for edit/add) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.tailwindcss.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.8/css/dataTables.dataTables.min.css"> {# Or DataTables Tailwind CSS #}

<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
    });

    // Handle HTMX after swap event for DataTables initialization
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'goodsservicenoteTable-container') {
            // Check if DataTable is already initialized to prevent re-initialization
            if ($.fn.DataTable.isDataTable('#goodsservicenoteTable')) {
                $('#goodsservicenoteTable').DataTable().destroy(); // Destroy previous instance
            }
            $('#goodsservicenoteTable').DataTable({
                "pageLength": 20, // Matches original PageSize
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "paging": true,
                "searching": true, // Enable client-side search by default
                "ordering": true // Enable sorting by default
            });
        }
    });

    // Handle selection from autocomplete results for Supplier
    document.body.addEventListener('click', function(event) {
        if (event.target.closest('.autocomplete-item')) {
            const selectedText = event.target.textContent;
            const supplierIdMatch = selectedText.match(/\[(.*?)\]$/);
            if (supplierIdMatch) {
                const supplierId = supplierIdMatch[1];
                document.getElementById('txtSupplier').value = selectedText;
                document.getElementById('supplierIdSelected').value = supplierId;
                document.getElementById('supplier-autocomplete-results').innerHTML = ''; // Clear results
            }
        }
    });

    // Clear autocomplete results if input is cleared
    document.getElementById('txtSupplier').addEventListener('input', function() {
        if (this.value.trim() === '') {
            document.getElementById('supplierIdSelected').value = '';
            document.getElementById('supplier-autocomplete-results').innerHTML = '';
        }
    });
</script>
{% endblock %}

```

**`_goodsservicenote_table.html`** (Partial template for the DataTables content)

```html
<div class="overflow-x-auto bg-white rounded-lg shadow overflow-y-auto relative">
    <table id="goodsservicenoteTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GSN No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GIN No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PONo</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Supplier</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in goodsservicenotes %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.fin_year_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.gsn_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.gin_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.sys_date|date:"d/m/Y" }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.po_no_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.supplier_name_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.challan_no_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.challan_date_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200">
                    <a href="{% url 'goodsservicenote_detail' obj.pk %}?SupId={{ obj.supplier_id_display }}&GSNNo={{ obj.gsn_no }}&GINNo={{ obj.gin_no }}&GINId={{ obj.gin_id }}&PONo={{ obj.po_no_display }}&FyId={{ obj.fin_year_id }}&ModId=9&SubModId=39"
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2">
                        Select
                    </a>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="10" class="py-4 px-4 text-center text-gray-500">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<div id="supplier-autocomplete-results" class="bg-white border border-gray-300 rounded-md mt-1 w-full shadow-lg max-h-48 overflow-y-auto">
    {% for result in autocomplete_results %}
        <div class="autocomplete-item px-4 py-2 hover:bg-gray-100 cursor-pointer">{{ result }}</div>
    {% endfor %}
</div>

```
**`_supplier_autocomplete_results.html`** (HTMX partial for autocomplete suggestions)
This is not explicitly requested in the template example, but for a true HTMX autocomplete, it's typically a separate partial.

```html
{% for result in autocomplete_results %}
    <div class="autocomplete-item px-4 py-2 hover:bg-gray-100 cursor-pointer">{{ result }}</div>
{% empty %}
    <div class="px-4 py-2 text-gray-500">No suggestions</div>
{% endfor %}
```

**`detail.html`** (A placeholder for the "Select" action's target page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">Goods Service Note Details: {{ goodsservicenote.gsn_no }}</h2>
    <div class="bg-white p-6 rounded-lg shadow-md">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-lg">
            <div><strong>GSN No:</strong> {{ goodsservicenote.gsn_no }}</div>
            <div><strong>Fin Year:</strong> {{ goodsservicenote.fin_year_display }}</div>
            <div><strong>System Date:</strong> {{ goodsservicenote.sys_date|date:"d/m/Y" }}</div>
            <div><strong>GIN No:</strong> {{ goodsservicenote.gin_no }}</div>
            <div><strong>PO No:</strong> {{ goodsservicenote.po_no_display }}</div>
            <div><strong>Supplier:</strong> {{ goodsservicenote.supplier_name_display }}</div>
            <div><strong>Challan No:</strong> {{ goodsservicenote.challan_no_display }}</div>
            <div><strong>Challan Date:</strong> {{ goodsservicenote.challan_date_display }}</div>
            <!-- Displaying query parameters received for context -->
            <div class="col-span-full mt-4 text-gray-600 text-sm">
                <p><strong>Passed Parameters:</strong></p>
                <p>SupId: {{ sup_id }}</p>
                <p>GSNNo: {{ gsn_no_param }}</p>
                <p>GINNo: {{ gin_no_param }}</p>
                <p>GINId: {{ gin_id_param }}</p>
                <p>PONo: {{ po_no_param }}</p>
                <p>FyId: {{ fy_id_param }}</p>
                <p>ModId: {{ mod_id_param }}</p>
                <p>SubModId: {{ sub_mod_id_param }}</p>
            </div>
        </div>
        <div class="mt-6 flex justify-end">
            <a href="{% url 'goodsservicenote_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                Back to List
            </a>
            <!-- Potentially an edit button here that loads a form into a modal -->
            {# <button 
                class="ml-4 bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                hx-get="{% url 'goodsservicenote_edit' goodsservicenote.pk %}"
                hx-target="#modalContent"
                hx-trigger="click"
                _="on click add .is-active to #modal">
                Edit GSN
            </button> #}
        </div>
    </div>

    <!-- Modal (if using HTMX for edit/add forms on this page) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

```

#### 4.5 URLs (`inventory/urls.py`)

**Business Implication:** URL patterns define the address for each page or endpoint in your application. A clear and consistent URL structure makes the application intuitive and easier to navigate for both users and developers.

```python
from django.urls import path
from .views import (
    GoodsServiceNoteListView,
    GoodsServiceNoteTablePartialView,
    SupplierAutoCompleteView,
    GoodsServiceNoteDetailView,
    # GoodsServiceNoteCreateView, # Uncomment if needed
    # GoodsServiceNoteUpdateView, # Uncomment if needed
    # GoodsServiceNoteDeleteView, # Uncomment if needed
)

urlpatterns = [
    # Main list page
    path('goodsservicenote/', GoodsServiceNoteListView.as_view(), name='goodsservicenote_list'),
    
    # HTMX endpoint for the table content (for search/refresh)
    path('goodsservicenote/table/', GoodsServiceNoteTablePartialView.as_view(), name='goodsservicenote_table'),

    # HTMX endpoint for supplier autocomplete
    path('goodsservicenote/autocomplete/supplier/', SupplierAutoCompleteView.as_view(), name='supplier_autocomplete'),

    # Detail view for selected GSN (corresponds to original redirect)
    # The original redirect used query params, but Django convention is path params.
    # We still accept query params on the detail view to mimic original behavior fully.
    path('goodsservicenote/detail/<int:pk>/', GoodsServiceNoteDetailView.as_view(), name='goodsservicenote_detail'),

    # Add/Edit/Delete views (uncomment and implement when ready)
    # path('goodsservicenote/add/', GoodsServiceNoteCreateView.as_view(), name='goodsservicenote_add'),
    # path('goodsservicenote/edit/<int:pk>/', GoodsServiceNoteUpdateView.as_view(), name='goodsservicenote_edit'),
    # path('goodsservicenote/delete/<int:pk>/', GoodsServiceNoteDeleteView.as_view(), name='goodsservicenote_delete'),
]

```

#### 4.6 Tests (`inventory/tests.py`)

**Business Implication:** Comprehensive testing is crucial for ensuring the quality, reliability, and maintainability of your modernized application. Unit tests verify individual components (models), while integration tests confirm that different parts of the system work together as expected (views). This minimizes bugs and reduces risks during deployment and future updates.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.http import QueryDict
from unittest.mock import patch, MagicMock

# Import all models to test them
from .models import (
    FinancialYear,
    Supplier,
    GoodsInwardNote,
    PurchaseOrder,
    GoodsServiceNote,
)

# Mocking session for testing purposes
class MockSession:
    def __init__(self, compid=1, finyear=1):
        self._data = {'compid': compid, 'finyear': finyear}

    def get(self, key, default=None):
        return self._data.get(key, default)

    def __getitem__(self, key):
        return self._data[key]

    def __setitem__(self, key, value):
        self._data[key] = value

class ModelTestBase(TestCase):
    """Base class for setting up common test data for models."""
    @classmethod
    def setUpTestData(cls):
        # Create common test data for managed=False models
        # Ensure these match the assumed legacy DB schema
        FinancialYear.objects.create(fin_year_id=2023, fin_year='2023-2024', comp_id=1)
        FinancialYear.objects.create(fin_year_id=2022, fin_year='2022-2023', comp_id=1)

        Supplier.objects.create(supplier_id='SUP001', supplier_name='ABC Corp', comp_id=1)
        Supplier.objects.create(supplier_id='SUP002', supplier_name='XYZ Ltd', comp_id=1)

        GoodsInwardNote.objects.create(id=101, po_no='PO-2023-001', challan_no='CH-001', challan_date='2023-01-15', comp_id=1)
        GoodsInwardNote.objects.create(id=102, po_no='PO-2023-002', challan_no='CH-002', challan_date='2023-02-20', comp_id=1)

        PurchaseOrder.objects.create(id=201, po_no='PO-2023-001', supplier_id='SUP001', comp_id=1)
        PurchaseOrder.objects.create(id=202, po_no='PO-2023-002', supplier_id='SUP002', comp_id=1)

        GoodsServiceNote.objects.create(
            id=1, fin_year_id=2023, gsn_no='GSN-2023-001', gin_no='GIN-001', gin_id=101,
            sys_date='2023-03-01', comp_id=1
        )
        GoodsServiceNote.objects.create(
            id=2, fin_year_id=2022, gsn_no='GSN-2022-001', gin_no='GIN-002', gin_id=102,
            sys_date='2022-11-10', comp_id=1
        )

# Unit tests for Models
class GoodsServiceNoteModelTest(ModelTestBase):
    def test_goodsservicenote_creation(self):
        gsn = GoodsServiceNote.objects.get(id=1)
        self.assertEqual(gsn.gsn_no, 'GSN-2023-001')
        self.assertEqual(gsn.fin_year_id, 2023)
        self.assertEqual(gsn.gin_id, 101)

    def test_fin_year_display_property(self):
        gsn = GoodsServiceNote.objects.get(id=1)
        self.assertEqual(gsn.fin_year_display, '2023-2024')

        # Test with non-existent financial year
        gsn.fin_year_id = 9999 # Non-existent
        self.assertEqual(gsn.fin_year_display, 'N/A')

    def test_gin_details_obj_property(self):
        gsn = GoodsServiceNote.objects.get(id=1)
        gin_obj = gsn.gin_details_obj
        self.assertIsNotNone(gin_obj)
        self.assertEqual(gin_obj.po_no, 'PO-2023-001')

        # Test with non-existent GIN
        gsn.gin_id = 999 # Non-existent
        self.assertIsNone(gsn.gin_details_obj)

    def test_po_no_display_property(self):
        gsn = GoodsServiceNote.objects.get(id=1)
        self.assertEqual(gsn.po_no_display, 'PO-2023-001')

        # Test with missing GIN details
        gsn.gin_id = 999
        self.assertEqual(gsn.po_no_display, 'N/A')

    def test_challan_details_display_properties(self):
        gsn = GoodsServiceNote.objects.get(id=1)
        self.assertEqual(gsn.challan_no_display, 'CH-001')
        self.assertEqual(gsn.challan_date_display, '15/01/2023')

        # Test with missing GIN details
        gsn.gin_id = 999
        self.assertEqual(gsn.challan_no_display, 'N/A')
        self.assertEqual(gsn.challan_date_display, 'N/A')

    @patch('inventory.models.PurchaseOrder.objects.filter')
    @patch('inventory.models.Supplier.objects.filter')
    def test_supplier_details_property_found(self, mock_supplier_filter, mock_purchaseorder_filter):
        gsn = GoodsServiceNote.objects.get(id=1)

        # Mocking the chain of lookups for supplier_details
        mock_gin_obj = MagicMock(spec=GoodsInwardNote)
        mock_gin_obj.po_no = 'PO-2023-001'
        gsn.gin_details_obj = mock_gin_obj # Temporarily set mock for property access

        mock_po = MagicMock(spec=PurchaseOrder)
        mock_po.supplier_id = 'SUP001'
        mock_purchaseorder_filter.return_value.first.return_value = mock_po

        mock_supplier = MagicMock(spec=Supplier)
        mock_supplier.supplier_id = 'SUP001'
        mock_supplier.supplier_name = 'ABC Corp'
        mock_supplier_filter.return_value.first.return_value = mock_supplier

        details = gsn.supplier_details
        self.assertEqual(details, {'supplier_id': 'SUP001', 'supplier_name': 'ABC Corp'})
        mock_purchaseorder_filter.assert_called_with(po_no='PO-2023-001', comp_id=1)
        mock_supplier_filter.assert_called_with(supplier_id='SUP001', comp_id=1)

    @patch('inventory.models.PurchaseOrder.objects.filter')
    @patch('inventory.models.Supplier.objects.filter')
    def test_supplier_details_property_no_po(self, mock_supplier_filter, mock_purchaseorder_filter):
        gsn = GoodsServiceNote.objects.get(id=1)
        mock_gin_obj = MagicMock(spec=GoodsInwardNote)
        mock_gin_obj.po_no = 'NON-EXISTENT-PO'
        gsn.gin_details_obj = mock_gin_obj

        mock_purchaseorder_filter.return_value.first.return_value = None # No PO found

        details = gsn.supplier_details
        self.assertEqual(details, {'supplier_id': None, 'supplier_name': 'N/A'})
        mock_supplier_filter.assert_not_called()

    @patch('inventory.models.PurchaseOrder.objects.filter')
    @patch('inventory.models.Supplier.objects.filter')
    def test_supplier_details_property_no_supplier(self, mock_supplier_filter, mock_purchaseorder_filter):
        gsn = GoodsServiceNote.objects.get(id=1)
        mock_gin_obj = MagicMock(spec=GoodsInwardNote)
        mock_gin_obj.po_no = 'PO-2023-001'
        gsn.gin_details_obj = mock_gin_obj

        mock_po = MagicMock(spec=PurchaseOrder)
        mock_po.supplier_id = 'NONEXISTENT_SUP'
        mock_purchaseorder_filter.return_value.first.return_value = mock_po
        mock_supplier_filter.return_value.first.return_value = None # No Supplier found

        details = gsn.supplier_details
        self.assertEqual(details, {'supplier_id': 'NONEXISTENT_SUP', 'supplier_name': 'N/A'})


# Integration tests for Views
class GoodsServiceNoteViewsTest(ModelTestBase):
    def setUp(self):
        super().setUp()
        self.client = Client()
        # Mock session for the client
        self.client.session['compid'] = 1
        self.client.session['finyear'] = 2023

    def test_list_view_get(self):
        response = self.client.get(reverse('goodsservicenote_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goodsservicenote/list.html')
        self.assertIn('goodsservicenotes', response.context)
        self.assertEqual(len(response.context['goodsservicenotes']), 2) # Both GSNs created

    def test_table_partial_view_get_no_filter(self):
        response = self.client.get(reverse('goodsservicenote_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goodsservicenote/_goodsservicenote_table.html')
        self.assertIn('goodsservicenotes', response.context)
        self.assertEqual(len(response.context['goodsservicenotes']), 2) # Both GSNs should be returned

    def test_table_partial_view_get_with_supplier_filter(self):
        # The complex filtering logic in the view's get_queryset for supplier_id_selected
        # is tested here. This simulates the form submission.
        
        # Test with an existing supplier ID
        response = self.client.get(
            reverse('goodsservicenote_table'),
            {'supplier_id_selected': 'SUP001'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goodsservicenote/_goodsservicenote_table.html')
        self.assertEqual(len(response.context['goodsservicenotes']), 1) # Only GSN-2023-001 for SUP001
        self.assertEqual(response.context['goodsservicenotes'][0].gsn_no, 'GSN-2023-001')

        # Test with a non-existent supplier ID
        response = self.client.get(
            reverse('goodsservicenote_table'),
            {'supplier_id_selected': 'NONEXISTENT'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['goodsservicenotes']), 0)


    def test_supplier_autocomplete_view_get(self):
        response = self.client.get(reverse('supplier_autocomplete'), {'supplier_name': 'ABC'})
        self.assertEqual(response.status_code, 200)
        json_response = response.json()
        self.assertIsInstance(json_response, list)
        self.assertIn('ABC Corp [SUP001]', json_response)
        self.assertNotIn('XYZ Ltd [SUP002]', json_response) # Should not match 'ABC'

        response = self.client.get(reverse('supplier_autocomplete'), {'supplier_name': 'sup00'}) # Test ID match
        self.assertEqual(response.status_code, 200)
        json_response = response.json()
        self.assertIsInstance(json_response, list)
        self.assertIn('ABC Corp [SUP001]', json_response)
        self.assertIn('XYZ Ltd [SUP002]', json_response)
        
        response = self.client.get(reverse('supplier_autocomplete'), {'supplier_name': ''})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), []) # Empty query should return empty list

    def test_goodsservicenote_detail_view_get_exists(self):
        gsn = GoodsServiceNote.objects.get(id=1)
        response = self.client.get(reverse('goodsservicenote_detail', args=[gsn.id]), {
            'SupId': gsn.supplier_id_display,
            'GSNNo': gsn.gsn_no,
            'GINNo': gsn.gin_no,
            'GINId': gsn.gin_id,
            'PONo': gsn.po_no_display,
            'FyId': gsn.fin_year_id,
            'ModId': 9,
            'SubModId': 39,
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goodsservicenote/detail.html')
        self.assertIn('goodsservicenote', response.context)
        self.assertEqual(response.context['goodsservicenote'].id, gsn.id)
        self.assertEqual(response.context['sup_id'], gsn.supplier_id_display)
        self.assertEqual(response.context['gsn_no_param'], gsn.gsn_no)

    def test_goodsservicenote_detail_view_get_not_exists(self):
        response = self.client.get(reverse('goodsservicenote_detail', args=[9999]))
        self.assertEqual(response.status_code, 302) # Redirects back to list on error
        self.assertRedirects(response, reverse('goodsservicenote_list'))
        # Check for error message (requires message middleware in settings.py)
        # messages = list(response.wsgi_request._messages)
        # self.assertEqual(len(messages), 1)
        # self.assertEqual(str(messages[0]), "Goods Service Note not found.")
```

### Step 5: HTMX and Alpine.js Integration

**Business Implication:** By strategically integrating HTMX and Alpine.js, we deliver a highly responsive and dynamic user experience without the complexity of traditional SPA frameworks. This reduces development time, simplifies debugging, and minimizes the amount of JavaScript code, resulting in faster page loads and a more fluid interface.

*   **HTMX for Search and List Updates:**
    *   The `searchForm` (containing `txtSupplier` and `btnSearch`) uses `hx-get` to `{% url 'goodsservicenote_table' %}`. When the form is submitted (or `txtSupplier` changes after a delay), an HTMX request fetches only the updated table content.
    *   `hx-target="#goodsservicenoteTable-container"` ensures only the table `div` is updated.
    *   `hx-swap="innerHTML"` replaces the table's content.
    *   `hx-indicator="#loadingIndicator"` provides visual feedback during AJAX requests.
    *   `hx-trigger="load, refreshGoodsServiceNoteList from:body"` on the table container ensures it loads immediately and can be refreshed by custom HTMX triggers.
    *   The `Supplier` autocomplete functionality leverages HTMX `hx-get` on `txtSupplier` to `{% url 'supplier_autocomplete' %}`, displaying suggestions in a `hx-target` div. When an autocomplete item is clicked, Alpine.js or simple JS copies the selected value to the hidden `supplier_id_selected` field and the visible `txtSupplier`.
*   **DataTables for Client-Side Interactivity:**
    *   The `_goodsservicenote_table.html` partial renders a standard HTML `<table>`.
    *   After HTMX swaps the new table content into the DOM, a JavaScript snippet in `list.html` detects the `htmx:afterSwap` event, then initializes or re-initializes `DataTables.js` on the newly loaded `<table>` (`#goodsservicenoteTable`). This ensures features like client-side search, sorting, and pagination work seamlessly without server-side processing for these operations.
*   **Alpine.js for UI State (e.g., Modals):**
    *   The modal (`#modal`) uses Alpine.js (`x-data`, `x-show`) and HTMX (`_="on click add .is-active to #modal"`) to manage its visibility. Clicking outside the modal can dismiss it, providing a smooth user experience.
    *   The "Select" links on the table rows could optionally use HTMX to fetch and display the `detail.html` content within this modal, rather than a full page redirect, for a more modern single-page application feel. For this migration, it's kept as a full page redirect to match the original behavior, but the modal setup is ready for future enhancements.
*   **`HX-Trigger` for Refresh:** After a successful CRUD operation (if implemented for `CreateView`, `UpdateView`, `DeleteView` in the future), the view would return an `HttpResponse` with an `HX-Trigger` header (`{'HX-Trigger': 'refreshGoodsServiceNoteList'}`). This signal would then prompt the `goodsservicenoteTable-container` to reload its content automatically, ensuring the list is always up-to-date.

### Final Notes

*   **Database Configuration:** Remember to configure your `settings.py` for connecting to the legacy SQL Server database using `django-mssql-backend`. You might need to define a separate database alias (e.g., `'legacy_db'`) and specify `using('legacy_db')` in your model queries if your Django app uses a different default database.
*   **Performance for `managed=False` and complex joins:** The "Fat Model" with properties for derived fields is a good encapsulation of business logic. However, for large datasets displayed in `ListView` where many lookups occur per row, this can lead to N+1 query issues. For production-scale list views, consider using Django's `RawSQL` or a custom manager to construct a single, efficient database query that performs all necessary joins at the SQL level, or using `select_related`/`prefetch_related` if Django's ORM can infer direct relationships.
*   **Session Data (`CompId`, `FinYearId`):** The current solution assumes `compid` and `finyear` are in `request.session`. In a production Django application, these would typically be tied to the authenticated user (e.g., `request.user.company` and `request.user.financial_year`), possibly configured in `settings.py` or a `middleware` for global context.
*   **Error Handling:** The C# code had `try-catch` blocks that often just swallowed exceptions. The Django code provides basic error handling (e.g., `DoesNotExist` for models, `N/A` for missing data), but robust error logging and user feedback mechanisms should be implemented in a production system.
*   **Security:** Ensure proper authentication and authorization (e.g., Django's `LoginRequiredMixin`, permission checks) are applied to views. The original ASP.NET code used `Session["username"]`, `Session["finyear"]`, `Session["compid"]` which would be replaced by Django's authentication system.
*   **Scalability:** While HTMX and Alpine.js enhance frontend performance, review database queries (especially `get_queryset` in `GoodsServiceNoteTablePartialView`) for large datasets to ensure they scale efficiently.

This modernization plan provides a solid foundation for transitioning your legacy ASP.NET application to a modern, maintainable Django solution using automated, conversational AI-assisted approaches. By adhering to these principles, you will achieve significant improvements in performance, user experience, and long-term maintainability.