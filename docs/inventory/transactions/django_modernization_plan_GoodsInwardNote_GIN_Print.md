## ASP.NET to Django Conversion Script: Goods Inward Note (GIN) Print Module

This document outlines a comprehensive plan for migrating the ASP.NET "Goods Inward Note [GIN] - Print" module to a modern Django application. The focus is on leveraging Django 5.0+, HTMX, Alpine.js, and DataTables to create a performant, maintainable, and user-friendly solution, emphasizing automation-driven conversion.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code interacts with at least three tables, inferred from the `SqlDataAdapter` (using `Sp_GIN_Edit` stored procedure) and `AutoCompleteExtender` for supplier details:
1.  `tblInv_Inward_Master`: This appears to be the primary table for Goods Inward Notes, containing `Id`, `FinYear`, `PONo`, `GINNo`, `SysDate`, `ChNO`, `ChDT`, and likely a `SupplierId` to link to supplier details.
2.  `tblMM_Supplier_master`: Used for supplier search and autocomplete, containing `SupplierId`, `SupplierName`, and `CompId`.
3.  `tblMM_PO_Master`: Referenced in the C# `loadData` function for filtering by `SupplierId`, suggesting a link between GIN, PO, and Supplier.

For the purpose of this migration focusing on the "Print" (list/search) module, we will primarily define models for `tblInv_Inward_Master` and `tblMM_Supplier_master` based on the data displayed in the `GridView` and the search functionality.

**Inferred Tables and Columns:**

*   **[TABLE_NAME]** (for GINs): `tblInv_Inward_Master`
    *   `Id` (PK, int) -> `id`
    *   `FinYear` (string/int) -> `financial_year`
    *   `PONo` (string) -> `po_number`
    *   `GINNo` (string) -> `gin_number`
    *   `SysDate` (datetime) -> `system_date`
    *   `SupplierId` (int, FK to `tblMM_Supplier_master`) -> `supplier` (ForeignKey)
    *   `ChNO` (string) -> `challan_number`
    *   `ChDT` (datetime) -> `challan_date`

*   **[TABLE_NAME]** (for Suppliers): `tblMM_Supplier_master`
    *   `SupplierId` (PK, int) -> `id`
    *   `SupplierName` (string) -> `name`
    *   `CompId` (int) -> `company_id`

### Step 2: Identify Backend Functionality

**Analysis:**
The page's primary function is to list Goods Inward Notes with filtering capabilities and to allow navigation to a detailed view of a selected GIN.
*   **Read (List):** Displays GIN records in a grid (`GridView2`).
*   **Search/Filter:** Allows searching GINs by:
    *   Supplier Name (`DropDownList1` value "0")
    *   Purchase Order Number (`PONo`) (`DropDownList1` value "1")
    *   Goods Inward Note Number (`GINNo`) (`DropDownList1` value "2")
*   **Navigation:** A "Select" link button for each row redirects the user to `GoodsInwardNote_GIN_Print_Details.aspx` with various query parameters (`Id`, `GINo`, `ChNo`, `ChDt`, `fyid`).
*   **Autocomplete:** Provides suggestions for Supplier Names using an `AutoCompleteExtender`.

There are no direct "Create", "Update", or "Delete" operations for GINs on this specific ASP.NET page. However, per the instructions, we will scaffold the necessary Django CRUD views and forms for `GoodsInwardNote` to provide a complete modernization blueprint.

### Step 3: Infer UI Components

**Analysis:**
The ASP.NET page uses standard Web Forms controls:
*   `DropDownList` (ID `DropDownList1`): For selecting the search criteria type (Supplier Name, PO No, GIN No).
*   `TextBox` (ID `txtEnqId`): For entering PO No or GIN No.
*   `TextBox` (ID `txtSupplier`): For entering Supplier Name, paired with `AutoCompleteExtender`.
*   `AutoCompleteExtender` (ID `txtSupplier_AutoCompleteExtender`): Provides real-time suggestions for `txtSupplier`.
*   `Button` (ID `Button1`): Triggers the search operation.
*   `GridView` (ID `GridView2`): Displays the tabular data of GINs with columns like SN, Select, Fin Year, PONo, GIN No, Date, Name of Supplier, Challan No, Challan Date. It supports pagination and has a "Select" `LinkButton` per row.

**Mapping to Django/HTMX/Alpine.js:**
*   **Search Form:** Will be a Django `forms.Form` rendered in the template.
    *   `DropDownList1` will be an HTML `<select>` element.
    *   `txtEnqId` and `txtSupplier` will be HTML `<input type="text">` elements.
    *   Visibility toggling of `txtEnqId` and `txtSupplier` based on `select` choice will be handled by Alpine.js.
    *   `AutoCompleteExtender` for supplier will be replaced by an HTMX-powered `input` field querying a Django view.
    *   `Button1` will trigger an HTMX request to update the table.
*   **Data Grid:** `GridView2` will be replaced by an HTML `<table>` element rendered by a Django template, enhanced with DataTables.js for client-side functionality. The table content will be loaded and updated via HTMX.
*   **"Select" Link:** Will be a standard Django URL (`{% url '...' obj.pk %}`) linking to the GIN detail page, if available.

---

### Step 4: Generate Django Code

We will create a Django application named `inventory`.

#### 4.1 Models (`inventory/models.py`)

```python
from django.db import models
from django.db.models import Q # Used in manager for search logic

class Supplier(models.Model):
    """
    Maps to tblMM_Supplier_master for supplier details.
    Used for autocomplete functionality.
    """
    id = models.IntegerField(db_column='SupplierId', primary_key=True)
    name = models.CharField(db_column='SupplierName', max_length=255)
    company_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False  # Set to False as per instructions for existing DB
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return f"{self.name} [{self.id}]"

class GoodsInwardNoteManager(models.Manager):
    """
    Custom manager for GoodsInwardNote to encapsulate search logic.
    """
    def search_gin_notes(self, company_id, financial_year_id, search_type, search_query):
        qs = self.get_queryset().select_related('supplier').filter(
            supplier__company_id=company_id, # Assuming company_id is a filter for GINs as well
            # financial_year=financial_year_id # Uncomment if FinYear is stored as actual text in GIN table
        )

        if search_query:
            if search_type == 'supplier_name':
                # Supplier name search, need to extract ID if format is "Name [ID]"
                from .utils import get_supplier_id_from_text
                supplier_id = get_supplier_id_from_text(search_query)
                if supplier_id:
                    qs = qs.filter(supplier__id=supplier_id)
                else:
                    # Fallback to name contains search if ID not found, or raise error
                    qs = qs.filter(supplier__name__icontains=search_query)
            elif search_type == 'po_number':
                qs = qs.filter(po_number__icontains=search_query)
            elif search_type == 'gin_number':
                qs = qs.filter(gin_number__icontains=search_query)
        
        # Original C# code has explicit FinId in SP. Assuming a field 'financial_year' in the model.
        # If 'financial_year' is a string or char, it might be compared directly.
        # If it's a foreign key to a FinancialYear model, adjust accordingly.
        # For this example, assuming 'financial_year' is a CharField matching FyId string
        # qs = qs.filter(financial_year=financial_year_id) # Adjust if the field is int or another type

        return qs

class GoodsInwardNote(models.Model):
    """
    Maps to tblInv_Inward_Master for Goods Inward Note details.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    financial_year = models.CharField(db_column='FinYear', max_length=100) # Assuming string based on C#
    po_number = models.CharField(db_column='PONo', max_length=100)
    gin_number = models.CharField(db_column='GINNo', max_length=100)
    system_date = models.DateTimeField(db_column='SysDate')
    # Assuming SupplierId in tblInv_Inward_Master links to tblMM_Supplier_master.SupplierId
    supplier = models.ForeignKey(Supplier, models.DO_NOTHING, db_column='SupplierId', related_name='gin_notes')
    challan_number = models.CharField(db_column='ChNO', max_length=100, blank=True, null=True)
    challan_date = models.DateTimeField(db_column='ChDT', blank=True, null=True)

    objects = GoodsInwardNoteManager() # Attach the custom manager

    class Meta:
        managed = False  # Set to False as per instructions for existing DB
        db_table = 'tblInv_Inward_Master'
        verbose_name = 'Goods Inward Note'
        verbose_name_plural = 'Goods Inward Notes'
        # Define default ordering if needed, e.g., by gin_number or system_date
        ordering = ['-system_date']

    def __str__(self):
        return f"GIN {self.gin_number} (PO: {self.po_number})"

    # Example of a business logic method (Fat Model)
    def is_recent(self):
        """ Checks if the GIN is recent (e.g., within the last 30 days). """
        from datetime import timedelta
        return self.system_date > (timezone.now() - timedelta(days=30))

```

#### 4.2 Forms (`inventory/forms.py`)

```python
from django import forms
from .models import GoodsInwardNote, Supplier

# Helper function to parse supplier ID from the autocomplete format "Name [ID]"
import re
def get_supplier_id_from_text(text):
    match = re.search(r'\[(\d+)\]$', text)
    if match:
        return int(match.group(1))
    return None

class GoodsInwardNoteSearchForm(forms.Form):
    """
    Form for search criteria on the GIN list page.
    """
    SEARCH_CHOICES = [
        ('supplier_name', 'Supplier Name'),
        ('po_number', 'PO No'),
        ('gin_number', 'GIN No'),
    ]

    search_type = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        required=True,
        widget=forms.Select(attrs={'class': 'box3 w-full sm:w-auto', 'x-model': 'searchType'}) # x-model for Alpine.js
    )
    
    search_query_text = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 w-full sm:w-auto', 'placeholder': 'Enter search query', 'x-model': 'searchQueryText', 'x-show': "searchType === 'po_number' || searchType === 'gin_number'", 'x-transition'})
    )

    supplier_autocomplete = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full sm:w-auto',
            'placeholder': 'Start typing supplier name...',
            'hx-get': '/inventory/api/suppliers/autocomplete/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#supplier-suggestions',
            'hx-swap': 'innerHTML',
            '_': "on input if event.target.value.length < 1 remove .is-active from #supplier-suggestions",
            'x-model': 'supplierSearchQuery',
            'x-show': "searchType === 'supplier_name'",
            'x-transition'
        })
    )


class GoodsInwardNoteForm(forms.ModelForm):
    """
    Generic form for creating/updating GoodsInwardNote instances (for CRUD modals).
    """
    class Meta:
        model = GoodsInwardNote
        fields = ['financial_year', 'po_number', 'gin_number', 'system_date', 'supplier', 'challan_number', 'challan_date']
        widgets = {
            'financial_year': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'po_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'gin_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'system_date': forms.DateTimeInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'datetime-local'}),
            'supplier': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'challan_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'challan_date': forms.DateTimeInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'datetime-local'}),
        }
    
    def clean_po_number(self):
        po_number = self.cleaned_data['po_number']
        # Example validation: Ensure PO number is unique (if applicable)
        if GoodsInwardNote.objects.filter(po_number=po_number).exclude(pk=self.instance.pk).exists():
            raise forms.ValidationError("This Purchase Order Number already exists.")
        return po_number

```

#### 4.3 Views (`inventory/views.py`)

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from django.db.models import Q

from .models import GoodsInwardNote, Supplier
from .forms import GoodsInwardNoteSearchForm, GoodsInwardNoteForm, get_supplier_id_from_text

# Define placeholder company_id and financial_year_id (replace with actual session/user context)
# In a real application, these would come from authentication or user profile.
DUMMY_COMPANY_ID = 1
DUMMY_FINANCIAL_YEAR_ID = "2023-2024" # Example based on C# string usage

class GoodsInwardNoteListView(ListView):
    """
    Displays the main page for Goods Inward Note list and search.
    Handles the initial load of the search form and table container.
    """
    model = GoodsInwardNote
    template_name = 'inventory/goods_inward_note/list.html'
    context_object_name = 'goods_inward_notes' # This will be the initial data, if any

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = GoodsInwardNoteSearchForm()
        # Initial data for the table, consider making this an empty queryset or
        # loading a small set, as the full list will be loaded via HTMX
        context[self.context_object_name] = [] 
        return context

class GoodsInwardNoteTablePartialView(ListView):
    """
    HTMX endpoint to render only the Goods Inward Note table for dynamic updates.
    Handles search filtering based on GET parameters.
    """
    model = GoodsInwardNote
    template_name = 'inventory/goods_inward_note/_goods_inward_note_table.html'
    context_object_name = 'goods_inward_notes'

    def get_queryset(self):
        # Extract search parameters from GET request
        search_type = self.request.GET.get('search_type', '')
        search_query = self.request.GET.get('search_query', '')
        supplier_autocomplete_query = self.request.GET.get('supplier_autocomplete', '')

        # Use the custom manager for filtering
        queryset = GoodsInwardNote.objects.search_gin_notes(
            company_id=DUMMY_COMPANY_ID,
            financial_year_id=DUMMY_FINANCIAL_YEAR_ID,
            search_type=search_type,
            search_query=search_query or supplier_autocomplete_query # Use appropriate query field
        )
        return queryset

    # No need for post() method here, as HTMX handles updates with GET requests for table data

class SupplierAutocompleteView(View):
    """
    HTMX endpoint to provide supplier suggestions for the autocomplete field.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        suggestions = []
        if query:
            # Filter by name containing the query
            suppliers = Supplier.objects.filter(
                Q(name__icontains=query) | Q(id__icontains=query), # Allow searching by ID as well
                company_id=DUMMY_COMPANY_ID # Filter by company_id if applicable
            )[:10] # Limit suggestions
            suggestions = [str(s) for s in suppliers] # Format: "SupplierName [ID]"
        
        # Render a simple list of suggestions for HTMX to swap into the target div
        html_suggestions = ""
        if suggestions:
            html_suggestions = "<ul class='absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-60 overflow-auto'>"
            for suggestion in suggestions:
                # When a suggestion is clicked, set the input value and hide the suggestions
                html_suggestions += f"<li class='px-3 py-2 cursor-pointer hover:bg-gray-100' " \
                                    f"onclick=\"document.getElementById('id_supplier_autocomplete').value='{suggestion.replace('\'', '\\\'')}';" \
                                    f"this.closest('ul').remove();\">" \
                                    f"{suggestion}</li>"
            html_suggestions += "</ul>"

        return HttpResponse(html_suggestions)


# --- Generic CRUD Views (for modals) ---

class GoodsInwardNoteCreateView(CreateView):
    model = GoodsInwardNote
    form_class = GoodsInwardNoteForm
    template_name = 'inventory/goods_inward_note/_goods_inward_note_form.html'
    success_url = reverse_lazy('goods_inward_note_list') # Not directly used for HTMX success

    def form_valid(self, form):
        # Additional logic (e.g., setting company_id, financial_year) can go here
        # form.instance.company_id = DUMMY_COMPANY_ID 
        # form.instance.financial_year = DUMMY_FINANCIAL_YEAR_ID
        response = super().form_valid(form)
        messages.success(self.request, 'Goods Inward Note added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content response
                headers={
                    'HX-Trigger': '{"refreshGoodsInwardNoteList": {}, "closeModal": {}}' # Trigger events
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # If HTMX request, re-render the form with errors
            return HttpResponse(render_to_string(self.template_name, {'form': form}, request=self.request))
        return response


class GoodsInwardNoteUpdateView(UpdateView):
    model = GoodsInwardNote
    form_class = GoodsInwardNoteForm
    template_name = 'inventory/goods_inward_note/_goods_inward_note_form.html'
    success_url = reverse_lazy('goods_inward_note_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Goods Inward Note updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshGoodsInwardNoteList": {}, "closeModal": {}}'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return HttpResponse(render_to_string(self.template_name, {'form': form}, request=self.request))
        return response


class GoodsInwardNoteDeleteView(DeleteView):
    model = GoodsInwardNote
    template_name = 'inventory/goods_inward_note/_goods_inward_note_confirm_delete.html'
    success_url = reverse_lazy('goods_inward_note_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Goods Inward Note deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshGoodsInwardNoteList": {}, "closeModal": {}}'
                }
            )
        return response

```

#### 4.4 Templates (`inventory/templates/inventory/goods_inward_note/`)

**`list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <h2 class="text-2xl font-bold text-gray-800">Goods Inward Notes (GIN)</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'goods_inward_note_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New GIN
        </button>
    </div>

    <div x-data="{ searchType: 'supplier_name', searchQueryText: '', supplierSearchQuery: '' }" class="bg-white p-6 rounded-lg shadow-lg mb-8">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Search GIN</h3>
        <form hx-get="{% url 'goods_inward_note_table' %}"
              hx-target="#goods-inward-note-table-container"
              hx-swap="innerHTML"
              hx-trigger="submit, change from:#id_search_type"
              class="space-y-4">
            {% csrf_token %}
            <div class="flex flex-col sm:flex-row items-start sm:items-end gap-4">
                <div class="w-full sm:w-auto">
                    <label for="id_search_type" class="block text-sm font-medium text-gray-700 mb-1">Search By:</label>
                    {{ search_form.search_type }}
                </div>

                <div class="w-full sm:w-auto flex-grow relative">
                    <label for="id_search_query_text" class="block text-sm font-medium text-gray-700 mb-1" x-show="searchType !== 'supplier_name'">Search Query:</label>
                    <label for="id_supplier_autocomplete" class="block text-sm font-medium text-gray-700 mb-1" x-show="searchType === 'supplier_name'">Supplier Name:</label>
                    
                    {{ search_form.search_query_text }}
                    {{ search_form.supplier_autocomplete }}
                    <div id="supplier-suggestions"></div> {# HTMX target for autocomplete results #}
                </div>
                
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out w-full sm:w-auto">
                    Search
                </button>
            </div>
        </form>
    </div>
    
    <div id="goods-inward-note-table-container"
         hx-trigger="load, refreshGoodsInwardNoteList from:body" {# Listener for custom event from CRUD operations #}
         hx-get="{% url 'goods_inward_note_table' %}"
         hx-swap="innerHTML">
        <!-- Initial loading state -->
        <div class="text-center p-8 bg-white rounded-lg shadow-lg">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Goods Inward Notes...</p>
        </div>
    </div>
    
    <!-- Modal for form/delete confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden"
         _="on hx-trigger close_modal remove .is-active from me
            on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ showModal: false }"> {# Alpine.js for modal state #}
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.8/css/dataTables.dataTables.min.css">
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components would be initialized here if needed
        // The x-data on the form element handles the state for searchType
    });
</script>
{% endblock %}
```

**`_goods_inward_note_table.html`**

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-lg p-4">
    {% if goods_inward_notes %}
    <table id="goodsInwardNoteTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GIN No</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Supplier</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan No</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan Date</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in goods_inward_notes %}
            <tr>
                <td class="py-4 px-6 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ obj.financial_year }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ obj.po_number }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ obj.gin_number }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ obj.system_date|date:"Y-m-d H:i" }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ obj.supplier.name }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ obj.challan_number }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ obj.challan_date|date:"Y-m-d" }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm font-medium">
                    <a href="{% url 'goods_inward_note_detail' obj.pk %}" class="text-blue-600 hover:text-blue-900 mr-3">Select</a> {# Example detail URL, replace with actual #}
                    <button
                        class="text-yellow-600 hover:text-yellow-900 mr-3"
                        hx-get="{% url 'goods_inward_note_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="text-red-600 hover:text-red-900"
                        hx-get="{% url 'goods_inward_note_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    <script>
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#goodsInwardNoteTable')) {
            $('#goodsInwardNoteTable').DataTable({
                "pageLength": 20, // Page size from ASP.NET GridView
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "responsive": true
            });
        }
    });
    </script>
    {% else %}
    <p class="text-center text-gray-600 p-4">No data to display !</p>
    {% endif %}
</div>
```

**`_goods_inward_note_form.html`**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Goods Inward Note</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-indicator">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors|striptags }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
                _="on click send closeModal to body"> {# Custom event for Alpine to close modal #}
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
                Save GIN
            </button>
        </div>
        <div id="form-indicator" class="htmx-indicator mt-4 text-center text-blue-500">Saving...</div>
    </form>
</div>
```

**`_goods_inward_note_confirm_delete.html`**

```html
<div class="p-6 text-center">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Goods Inward Note "<strong>{{ object.gin_number }}</strong>"?</p>
    <form hx-delete="{% url 'goods_inward_note_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex items-center justify-center space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
                _="on click send closeModal to body">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`inventory/urls.py`)

```python
from django.urls import path
from .views import (
    GoodsInwardNoteListView,
    GoodsInwardNoteTablePartialView,
    SupplierAutocompleteView,
    GoodsInwardNoteCreateView,
    GoodsInwardNoteUpdateView,
    GoodsInwardNoteDeleteView,
)

urlpatterns = [
    path('goods-inward-note/', GoodsInwardNoteListView.as_view(), name='goods_inward_note_list'),
    path('goods-inward-note/table/', GoodsInwardNoteTablePartialView.as_view(), name='goods_inward_note_table'),
    
    # API endpoint for supplier autocomplete
    path('api/suppliers/autocomplete/', SupplierAutocompleteView.as_view(), name='supplier_autocomplete'),

    # CRUD endpoints for GIN (used by modals)
    path('goods-inward-note/add/', GoodsInwardNoteCreateView.as_view(), name='goods_inward_note_add'),
    path('goods-inward-note/edit/<int:pk>/', GoodsInwardNoteUpdateView.as_view(), name='goods_inward_note_edit'),
    path('goods-inward-note/delete/<int:pk>/', GoodsInwardNoteDeleteView.as_view(), name='goods_inward_note_delete'),
    
    # Placeholder for the detail page navigated by "Select"
    # This would typically be in its own app or a more specific URL for GIN details
    path('goods-inward-note/details/<int:pk>/', View.as_view(template_name='inventory/goods_inward_note/detail.html'), name='goods_inward_note_detail'),
]

```
**Note:** You would also need to include these URLs in your project's main `urls.py`:
`path('inventory/', include('inventory.urls')),`

#### 4.6 Tests (`inventory/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import datetime
from django.utils import timezone

from .models import GoodsInwardNote, Supplier, get_supplier_id_from_text
from .views import DUMMY_COMPANY_ID, DUMMY_FINANCIAL_YEAR_ID

class SupplierModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for Supplier
        cls.supplier1 = Supplier.objects.create(
            id=101,
            name='Test Supplier A',
            company_id=DUMMY_COMPANY_ID
        )
        cls.supplier2 = Supplier.objects.create(
            id=102,
            name='Another Supplier B',
            company_id=DUMMY_COMPANY_ID
        )

    def test_supplier_creation(self):
        self.assertEqual(self.supplier1.name, 'Test Supplier A')
        self.assertEqual(self.supplier1.company_id, DUMMY_COMPANY_ID)

    def test_supplier_str_method(self):
        self.assertEqual(str(self.supplier1), 'Test Supplier A [101]')

    def test_supplier_db_table_and_managed(self):
        self.assertEqual(Supplier._meta.db_table, 'tblMM_Supplier_master')
        self.assertFalse(Supplier._meta.managed)

class GoodsInwardNoteModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test supplier for GINs
        cls.supplier = Supplier.objects.create(
            id=201,
            name='Mega Corp',
            company_id=DUMMY_COMPANY_ID
        )
        # Create test data for GoodsInwardNote
        cls.gin1 = GoodsInwardNote.objects.create(
            id=1,
            financial_year=DUMMY_FINANCIAL_YEAR_ID,
            po_number='PO-001',
            gin_number='GIN-001',
            system_date=timezone.make_aware(datetime(2023, 1, 15, 10, 0, 0)),
            supplier=cls.supplier,
            challan_number='CH-001',
            challan_date=timezone.make_aware(datetime(2023, 1, 10, 0, 0, 0))
        )
        cls.gin2 = GoodsInwardNote.objects.create(
            id=2,
            financial_year=DUMMY_FINANCIAL_YEAR_ID,
            po_number='PO-002',
            gin_number='GIN-002',
            system_date=timezone.make_aware(datetime(2023, 2, 20, 11, 0, 0)),
            supplier=cls.supplier,
            challan_number='CH-002',
            challan_date=timezone.make_aware(datetime(2023, 2, 18, 0, 0, 0))
        )

    def test_gin_creation(self):
        gin = GoodsInwardNote.objects.get(id=1)
        self.assertEqual(gin.po_number, 'PO-001')
        self.assertEqual(gin.supplier.name, 'Mega Corp')

    def test_gin_str_method(self):
        gin = GoodsInwardNote.objects.get(id=1)
        self.assertEqual(str(gin), 'GIN GIN-001 (PO: PO-001)')

    def test_gin_db_table_and_managed(self):
        self.assertEqual(GoodsInwardNote._meta.db_table, 'tblInv_Inward_Master')
        self.assertFalse(GoodsInwardNote._meta.managed)

    def test_gin_is_recent_method(self):
        # Mock timezone.now for consistent testing of `is_recent`
        with self.settings(USE_TZ=True):
            with self.mock.patch('django.utils.timezone.now') as mock_now:
                mock_now.return_value = timezone.make_aware(datetime(2023, 3, 15, 12, 0, 0)) # Set a specific "now"

                # GIN from Jan 15, 2023 is not recent (more than 30 days ago)
                gin_old = GoodsInwardNote.objects.get(id=1)
                self.assertFalse(gin_old.is_recent())

                # GIN from Feb 20, 2023 is recent (less than 30 days ago from March 15)
                gin_recent = GoodsInwardNote.objects.get(id=2)
                self.assertTrue(gin_recent.is_recent())
                
                # Test with a GIN from current mocked 'now'
                recent_gin = GoodsInwardNote.objects.create(
                    id=3, financial_year=DUMMY_FINANCIAL_YEAR_ID, po_number='PO-003',
                    gin_number='GIN-003', system_date=mock_now.return_value - timezone.timedelta(days=1),
                    supplier=self.supplier
                )
                self.assertTrue(recent_gin.is_recent())


class GoodsInwardNoteViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.supplier = Supplier.objects.create(
            id=301,
            name='Test Supplier Co.',
            company_id=DUMMY_COMPANY_ID
        )
        cls.gin = GoodsInwardNote.objects.create(
            id=10,
            financial_year=DUMMY_FINANCIAL_YEAR_ID,
            po_number='P123',
            gin_number='G456',
            system_date=timezone.make_aware(datetime(2024, 5, 1, 10, 0, 0)),
            supplier=cls.supplier,
            challan_number='C789',
            challan_date=timezone.make_aware(datetime(2024, 4, 28, 0, 0, 0))
        )

    def setUp(self):
        self.client = Client()

    def test_list_view_get(self):
        response = self.client.get(reverse('goods_inward_note_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goods_inward_note/list.html')
        self.assertContains(response, 'Goods Inward Notes (GIN)')
        self.assertIsInstance(response.context['search_form'], GoodsInwardNoteSearchForm)

    def test_table_partial_view_no_search(self):
        response = self.client.get(reverse('goods_inward_note_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goods_inward_note/_goods_inward_note_table.html')
        self.assertContains(response, 'G456') # Checks if the existing GIN is in the table

    def test_table_partial_view_search_po_number(self):
        response = self.client.get(reverse('goods_inward_note_table'), {
            'search_type': 'po_number',
            'search_query': 'P123'
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'P123')
        self.assertNotContains(response, 'PO-NON-EXISTENT')

    def test_table_partial_view_search_supplier_name(self):
        response = self.client.get(reverse('goods_inward_note_table'), {
            'search_type': 'supplier_name',
            'supplier_autocomplete': 'Test Supplier Co. [301]' # Simulating the autocomplete format
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Supplier Co.')

        # Test with name only if ID parsing fails or not provided
        response = self.client.get(reverse('goods_inward_note_table'), {
            'search_type': 'supplier_name',
            'supplier_autocomplete': 'Test Supplier Co.' # No ID, should still find by icontains
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Supplier Co.')


    def test_supplier_autocomplete_view(self):
        response = self.client.get(reverse('supplier_autocomplete'), {'q': 'test'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertIn('Test Supplier Co. [301]', response.content.decode())

        response = self.client.get(reverse('supplier_autocomplete'), {'q': 'nonexistent'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertNotIn('nonexistent', response.content.decode()) # No suggestions

    def test_create_view_get(self):
        response = self.client.get(reverse('goods_inward_note_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goods_inward_note/_goods_inward_note_form.html')
        self.assertContains(response, 'Add Goods Inward Note')

    def test_create_view_post_success(self):
        data = {
            'financial_year': DUMMY_FINANCIAL_YEAR_ID,
            'po_number': 'NEW-PO-001',
            'gin_number': 'NEW-GIN-001',
            'system_date': timezone.now().isoformat(),
            'supplier': self.supplier.pk,
            'challan_number': 'NEW-CH-001',
            'challan_date': timezone.now().isoformat(),
        }
        response = self.client.post(reverse('goods_inward_note_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HX-Request success returns 204
        self.assertTrue(GoodsInwardNote.objects.filter(gin_number='NEW-GIN-001').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshGoodsInwardNoteList', response.headers['HX-Trigger'])

    def test_create_view_post_invalid(self):
        data = {
            'financial_year': '', # Invalid data
            'po_number': '',
            'gin_number': '',
            'system_date': '',
            'supplier': '',
        }
        response = self.client.post(reverse('goods_inward_note_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertContains(response, 'This field is required.')

    def test_update_view_get(self):
        response = self.client.get(reverse('goods_inward_note_edit', args=[self.gin.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goods_inward_note/_goods_inward_note_form.html')
        self.assertContains(response, 'Edit Goods Inward Note')
        self.assertContains(response, self.gin.gin_number)

    def test_update_view_post_success(self):
        updated_gin_number = 'UPDATED-G456'
        data = {
            'financial_year': self.gin.financial_year,
            'po_number': self.gin.po_number,
            'gin_number': updated_gin_number,
            'system_date': self.gin.system_date.isoformat(),
            'supplier': self.gin.supplier.pk,
            'challan_number': self.gin.challan_number,
            'challan_date': self.gin.challan_date.isoformat(),
        }
        response = self.client.post(reverse('goods_inward_note_edit', args=[self.gin.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.gin.refresh_from_db()
        self.assertEqual(self.gin.gin_number, updated_gin_number)
        self.assertIn('HX-Trigger', response.headers)

    def test_delete_view_get(self):
        response = self.client.get(reverse('goods_inward_note_delete', args=[self.gin.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goods_inward_note/_goods_inward_note_confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, self.gin.gin_number)

    def test_delete_view_post_success(self):
        gin_to_delete = GoodsInwardNote.objects.create(
            id=99,
            financial_year=DUMMY_FINANCIAL_YEAR_ID,
            po_number='TO-DELETE',
            gin_number='TO-DELETE-GIN',
            system_date=timezone.now(),
            supplier=self.supplier
        )
        response = self.client.delete(reverse('goods_inward_note_delete', args=[gin_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(GoodsInwardNote.objects.filter(pk=gin_to_delete.pk).exists())
        self.assertIn('HX-Trigger', response.headers)

    def test_get_supplier_id_from_text(self):
        self.assertEqual(get_supplier_id_from_text('Supplier Name [123]'), 123)
        self.assertIsNone(get_supplier_id_from_text('Supplier Name'))
        self.assertIsNone(get_supplier_id_from_text(''))
        self.assertEqual(get_supplier_id_from_text('Another [9876]'), 9876)

```

#### 4.7 Utilities (`inventory/utils.py`)

```python
import re

def get_supplier_id_from_text(text):
    """
    Parses a string like "Supplier Name [ID]" to extract the integer ID.
    Used for converting autocomplete input to a usable supplier ID.
    """
    if not text:
        return None
    match = re.search(r'\[(\d+)\]$', text)
    if match:
        return int(match.group(1))
    return None

```

### Step 5: HTMX and Alpine.js Integration

The provided code templates heavily leverage HTMX and Alpine.js to achieve a modern, dynamic user experience without extensive custom JavaScript.

*   **HTMX for dynamic content:**
    *   The main list view (`list.html`) contains a `div` (`goods-inward-note-table-container`) that uses `hx-get` to load the table content from `{% url 'goods_inward_note_table' %}`.
    *   This table content (`_goods_inward_note_table.html`) is swapped into the container.
    *   The search form uses `hx-get` to submit its parameters to `goods_inward_note_table` and update the table.
    *   The "Add", "Edit", and "Delete" buttons in `_goods_inward_note_table.html` use `hx-get` to fetch their respective forms (`_goods_inward_note_form.html` or `_goods_inward_note_confirm_delete.html`) into the modal (`#modalContent`).
    *   Form submissions (`hx-post`, `hx-delete`) in the modal partials return `204 No Content` and `HX-Trigger` headers to inform the client to refresh the main list (`refreshGoodsInwardNoteList`) and close the modal (`closeModal`).
    *   The supplier autocomplete input uses `hx-get` on `keyup changed delay:500ms` to query `{% url 'supplier_autocomplete' %}` and `hx-target` to update a `div` with suggestions.
*   **Alpine.js for UI state management:**
    *   The main `list.html` uses `x-data="{ searchType: 'supplier_name', searchQueryText: '', supplierSearchQuery: '' }"` to manage the state of the search form.
    *   `x-model` binds the select and text inputs to these Alpine.js variables.
    *   `x-show` and `x-transition` are used on the `search_query_text` and `supplier_autocomplete` input fields to dynamically show/hide them based on the `searchType` selected in the dropdown, replicating the ASP.NET `Visible` property changes.
    *   A simple `_` attribute `on click add .is-active to #modal` is used to show the modal, and `on click if event.target.id == 'modal' remove .is-active from me` to close it by clicking outside. Custom `closeModal` event is triggered for more controlled closing.
*   **DataTables for list views:**
    *   The `_goods_inward_note_table.html` includes a `<script>` block that initializes DataTables on the `goodsInwardNoteTable`. This provides client-side searching, sorting, and pagination without requiring full page reloads for these operations.

This combination ensures a highly interactive user experience with minimal traditional JavaScript, making the application easier to maintain and faster to develop.

---

## Final Notes

*   **Placeholders:** Ensure `DUMMY_COMPANY_ID` and `DUMMY_FINANCIAL_YEAR_ID` in `inventory/views.py` are replaced with actual values from user sessions or configuration once an authentication system is in place.
*   **Database Connection:** The Django `settings.py` must be configured to connect to your existing SQL Server database (e.g., using `django-pyodbc` or `django-mssql-backend`). Remember to install `pyodbc` for `django-pyodbc`.
*   **`core/base.html`:** This file is assumed to exist in your `core` app and contain the basic HTML structure, CDN links for Tailwind CSS, Alpine.js, and HTMX.
*   **Error Handling:** While `form_invalid` is handled for HTMX, more robust error reporting (e.g., toast messages for `HX-Trigger`) might be desired in a production application.
*   **Security:** Always ensure proper input validation, CSRF protection (Django handles this automatically with `{% csrf_token %}`), and parameterized queries (Django ORM handles this) to prevent SQL injection. The original C# code directly concatenated strings in SQL queries, which is a significant security risk. The Django ORM inherently protects against this.